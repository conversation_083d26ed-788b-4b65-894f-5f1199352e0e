import { Meteor } from 'meteor/meteor';
import { Mongo } from 'meteor/mongo';
import { check } from 'meteor/check';
import { Match } from 'meteor/check';

export const Tasks = new Mongo.Collection('tasks');

export const taskCategories = [
  'Development',
  'Design',
  'Marketing',
  'Sales',
  'Support',
  'Planning',
  'Research',
  'Other'
];

export const taskLabels = [
  { name: 'Bug', color: '#ef4444' },
  { name: 'Feature', color: '#3b82f6' },
  { name: 'Enhancement', color: '#10b981' },
  { name: 'Documentation', color: '#8b5cf6' },
  { name: 'Urgent', color: '#f59e0b' },
  { name: 'Blocked', color: '#6b7280' }
];

if (Meteor.isServer) {
  // Publications
  Meteor.publish('tasks', async function() {
    if (!this.userId) {
      return this.ready();
    }

    // Get user's role
    const user = await Meteor.users.findOneAsync(this.userId);
    const isAdmin = user?.roles?.includes('admin');

    // If admin, show all tasks
    if (isAdmin) {
      return Tasks.find({}, { 
        sort: { createdAt: -1 },
        fields: {
          title: 1,
          description: 1,
          startDate: 1,
          dueDate: 1,
          priority: 1,
          status: 1,
          assignedTo: 1,
          checklist: 1,
          category: 1,
          labels: 1,
          progress: 1,
          attachments: 1,
          links: 1,
          createdAt: 1,
          createdBy: 1,
          updatedAt: 1,
          updatedBy: 1
        }
      });
    }

    // For team members, show tasks they're assigned to or created
    return Tasks.find({
      $or: [
        { assignedTo: this.userId },
        { createdBy: this.userId }
      ]
    }, { 
      sort: { createdAt: -1 },
      fields: {
        title: 1,
        description: 1,
        startDate: 1,
        dueDate: 1,
        priority: 1,
        status: 1,
        assignedTo: 1,
        checklist: 1,
        category: 1,
        labels: 1,
        progress: 1,
        attachments: 1,
        links: 1,
        createdAt: 1,
        createdBy: 1,
        updatedAt: 1,
        updatedBy: 1
      }
    });
  });

  // Publish user data for tasks
  Meteor.publish('taskUsers', function() {
    console.log('Starting taskUsers publication');
    if (!this.userId) {
      console.log('No userId, returning ready');
      return this.ready();
    }

    // Get all tasks
    const tasks = Tasks.find({}).fetch();
    console.log('Found tasks:', tasks.length);
    
    // Collect all user IDs from tasks
    const userIds = new Set();
    tasks.forEach(task => {
      // Add users who uploaded attachments
      if (task.attachments) {
        task.attachments.forEach(attachment => {
          if (attachment.uploadedBy) {
            userIds.add(String(attachment.uploadedBy));
          }
        });
      }
      // Add users who added links
      if (task.links) {
        task.links.forEach(link => {
          if (link.addedBy) {
            userIds.add(String(link.addedBy));
          }
        });
      }
      // Add assigned users
      if (task.assignedTo) {
        task.assignedTo.forEach(userId => {
          userIds.add(String(userId));
        });
      }
    });

    const userIdArray = Array.from(userIds);
    console.log('Publishing user data for IDs:', userIdArray);

    // Find users and log what we found
    const users = Meteor.users.find(
      { _id: { $in: userIdArray } },
      { 
        fields: {
          _id: 1,
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        } 
      }
    ).fetch();

    console.log('Found users:', users.map(u => ({
      _id: u._id,
      name: `${u.profile?.firstName || ''} ${u.profile?.lastName || ''}`.trim(),
      hasProfile: !!u.profile
    })));

    // Return the cursor
    return Meteor.users.find(
      { _id: { $in: userIdArray } },
      { 
      fields: {
        _id: 1,
          emails: 1,
          roles: 1,
        'profile.firstName': 1,
        'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        } 
      }
    );
  });

  // Add a specific publication for a single task
  Meteor.publish('task', function(taskId) {
    check(taskId, String);
    
    if (!this.userId) {
      return this.ready();
    }

    const user = Meteor.users.findOne(this.userId);
    const isAdmin = user?.roles?.includes('admin');

    // Return a cursor that will update reactively
    const cursor = Tasks.find(
      { _id: taskId },
      {
        fields: {
          title: 1,
          description: 1,
          startDate: 1,
          dueDate: 1,
          priority: 1,
          status: 1,
          assignedTo: 1,
          checklist: 1,
          category: 1,
          labels: 1,
          progress: 1,
          attachments: 1,
          links: 1,
          createdAt: 1,
          createdBy: 1,
          updatedAt: 1,
          updatedBy: 1
        }
      }
    );

    // Log for debugging
    console.log('Publishing task:', taskId);
    cursor.observe({
      added: (doc) => console.log('Task added to publication:', doc._id),
      changed: (doc) => console.log('Task changed in publication:', doc._id),
      removed: (doc) => console.log('Task removed from publication:', doc._id)
    });

    return cursor;
  });
}

Meteor.methods({
  async 'tasks.insert'(task) {
    check(task, {
      title: String,
      description: String,
      startDate: Date,
      dueDate: Date,
      priority: String,
      status: String,
      assignedTo: Array,
      checklist: Array,
      category: String,
      labels: Array,
      progress: Number
    });

    if (!this.userId) {
      throw new Meteor.Error('Not authorized.');
    }

    console.log('Creating new task:', task); // Debug log

    // Process checklist items
    const processedChecklist = task.checklist.map(item => ({
      text: item.text,
      completed: item.completed || false
    }));

    const taskToInsert = {
      ...task,
      createdAt: new Date(),
      createdBy: this.userId,
      updatedAt: new Date(),
      updatedBy: this.userId,
      progress: task.progress || 0,
      status: 'pending', // Default status
      checklist: processedChecklist,
      labels: task.labels || [],
      category: task.category || '',
      assignedTo: task.assignedTo || []
    };

    console.log('Inserting task with values:', taskToInsert); // Debug log

    try {
      const result = await Tasks.insertAsync(taskToInsert);
      console.log('Task created successfully:', result); // Debug log
      return result;
    } catch (error) {
      console.error('Error creating task:', error);
      throw new Meteor.Error('task-creation-failed', error.message);
    }
  },

  async 'tasks.update'(taskId, task) {
    try {
      console.log('Starting task update:', { taskId, task });

      check(taskId, String);
      check(task, {
        title: String,
        description: String,
        startDate: Date,
        dueDate: Date,
        priority: String,
        assignedTo: Array,
        checklist: Array,
        category: Match.Optional(String),
        labels: Match.Optional(Array),
        progress: Match.Optional(Number),
        status: Match.Optional(String),
        attachments: Match.Optional(Array)
      });

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
      }

      const existingTask = await Tasks.findOneAsync(taskId);
      if (!existingTask) {
        throw new Meteor.Error('not-found', 'Task not found');
      }

      // Check if user is assigned to the task or is admin
      const user = await Meteor.users.findOneAsync(this.userId);
      const isAdmin = user?.roles?.includes('admin');
      
      if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
        throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
      }

      // Calculate progress based on checklist
      const completedItems = task.checklist.filter(item => item.completed).length;
      const totalItems = task.checklist.length;
      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

      // Update task status based on progress
      let status = task.status;
      if (progress === 100) {
        status = 'completed';
      } else if (progress > 0) {
        status = 'in-progress';
      } else {
        status = 'pending';
      }

      const taskToUpdate = {
        ...task,
        updatedAt: new Date(),
        updatedBy: this.userId,
        progress,
        status,
        category: task.category || existingTask.category || '',
        labels: task.labels || existingTask.labels || [],
        attachments: task.attachments || existingTask.attachments || []
      };

      const result = await Tasks.updateAsync(
        { _id: taskId },
        { $set: taskToUpdate }
      );

      if (result === 0) {
        throw new Meteor.Error('update-failed', 'Failed to update task');
      }

      return result;
    } catch (error) {
      console.error('Error updating task:', error);
      if (error instanceof Meteor.Error) {
        throw error;
      }
      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
    }
  },

  async 'tasks.delete'(taskId) {
    check(taskId, String);

    if (!this.userId) {
      throw new Meteor.Error('Not authorized.');
    }

    try {
      const result = await Tasks.removeAsync(taskId);
      
      if (result === 0) {
        throw new Meteor.Error('not-found', 'Task not found');
      }

      return result;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw new Meteor.Error('task-delete-failed', error.message);
    }
  },

  'tasks.updateProgress'(taskId, progress) {
    check(taskId, String);
    check(progress, Number);

    if (!this.userId) {
      throw new Meteor.Error('Not authorized.');
    }

    const task = Tasks.findOne(taskId);
    if (!task) {
      throw new Meteor.Error('Task not found.');
    }

    // Check if user is assigned to the task
    if (!task.assignedTo.includes(this.userId)) {
      throw new Meteor.Error('Not authorized to modify this task.');
    }

    // Update task status based on progress
    let status = task.status;
    if (progress === 100) {
      status = 'completed';
    } else if (progress > 0) {
      status = 'in-progress';
    }

    return Tasks.update(taskId, {
      $set: {
        progress,
        status,
        updatedAt: new Date(),
        updatedBy: this.userId
      }
    });
  },

  async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
    try {
      console.log('Starting toggleChecklistItem with:', { taskId, itemIndex, userId: this.userId });

      if (!this.userId) {
        console.log('No user ID found');
        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
      }

      // Validate inputs
      if (!taskId || typeof taskId !== 'string') {
        console.log('Invalid taskId:', taskId);
        throw new Meteor.Error('invalid-input', 'Invalid task ID');
      }

      if (typeof itemIndex !== 'number' || itemIndex < 0) {
        console.log('Invalid itemIndex:', itemIndex);
        throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
      }

      const task = await Tasks.findOneAsync(taskId);
      console.log('Found task:', task);

      if (!task) {
        console.log('Task not found');
        throw new Meteor.Error('not-found', 'Task not found');
      }

      // Check if user is assigned to the task or is admin
      const user = await Meteor.users.findOneAsync(this.userId);
      console.log('Found user:', user);
      
      const isAdmin = user?.roles?.includes('admin');
      console.log('Is admin:', isAdmin);
      
      if (!isAdmin && !task.assignedTo.includes(this.userId)) {
        console.log('User not authorized:', { userId: this.userId, assignedTo: task.assignedTo });
        throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
      }

      const checklist = task.checklist || [];
      console.log('Current checklist:', checklist);

      if (itemIndex >= checklist.length) {
        console.log('Invalid item index:', { itemIndex, checklistLength: checklist.length });
        throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
      }

      // Create a new array to ensure reactivity
      const updatedChecklist = [...checklist];
      updatedChecklist[itemIndex] = {
        ...updatedChecklist[itemIndex],
        completed: !updatedChecklist[itemIndex].completed
      };

      console.log('Updated checklist:', updatedChecklist);

      // Calculate progress
      const completedItems = updatedChecklist.filter(item => item.completed).length;
      const totalItems = updatedChecklist.length;
      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

      // Update task status based on progress
      let status;
      if (progress === 100) {
        status = 'completed';
      } else if (progress > 0) {
        status = 'in-progress';
      } else {
        status = 'pending';
      }

      console.log('Updating task with:', {
        taskId,
        updatedChecklist,
        progress,
        status
      });

      // First verify the task still exists
      const existingTask = await Tasks.findOneAsync(taskId);
      if (!existingTask) {
        throw new Meteor.Error('task-not-found', 'Task no longer exists');
      }

      // Perform the update
      const updateResult = await Tasks.updateAsync(
        { _id: taskId },
        {
          $set: {
            checklist: updatedChecklist,
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        }
      );

      console.log('Update result:', updateResult);

      if (updateResult === 0) {
        throw new Meteor.Error('update-failed', 'Failed to update task');
      }

      // Verify the update
      const updatedTask = await Tasks.findOneAsync(taskId);
      console.log('Task after update:', updatedTask);

      return updateResult;
    } catch (error) {
      console.error('Error in toggleChecklistItem:', error);
      if (error instanceof Meteor.Error) {
        throw error;
      }
      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
    }
  },

  async 'tasks.addAttachment'(taskId, fileData) {
    try {
      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
      }

      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('not-found', 'Task not found');
      }

      // Check if user is assigned to the task
      if (!task.assignedTo.includes(this.userId)) {
        throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
      }

      if (!fileData || !fileData.name || !fileData.data) {
        throw new Meteor.Error('invalid-input', 'Invalid file data');
      }

      // Ensure we're storing the user ID as a string
      const uploaderId = String(this.userId);

      // Add the file data to attachments with uploader info
      const result = await Tasks.updateAsync(
        { _id: taskId },
        {
          $push: {
            attachments: {
              name: fileData.name,
              type: fileData.type,
              data: fileData.data,
              uploadedAt: new Date(),
              uploadedBy: uploaderId
            }
          },
          $set: {
            updatedAt: new Date(),
            updatedBy: uploaderId
          }
        }
      );

      if (result === 0) {
        throw new Meteor.Error('update-failed', 'Failed to add attachment');
      }

      // Get and return the updated task
      const updatedTask = await Tasks.findOneAsync(taskId);
      console.log('Task after adding attachment:', updatedTask);
      return updatedTask;
    } catch (error) {
      console.error('Error adding attachment:', error);
      if (error instanceof Meteor.Error) {
        throw error;
      }
      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
    }
  },

  async 'tasks.addLink'(taskId, link) {
    try {
      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
      }

      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('not-found', 'Task not found');
      }

      // Check if user is assigned to the task
      if (!task.assignedTo.includes(this.userId)) {
        throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
      }

      if (!link) {
        throw new Meteor.Error('invalid-input', 'Link URL is required');
      }

      // Validate URL format
      try {
        new URL(link);
      } catch (e) {
        throw new Meteor.Error('invalid-input', 'Invalid URL format');
      }

      // Add the link to links array with adder info
      const result = await Tasks.updateAsync(
        { _id: taskId },
        {
          $push: {
            links: {
              url: link,
              addedAt: new Date(),
              addedBy: String(this.userId)
            }
          },
          $set: {
            updatedAt: new Date(),
            updatedBy: String(this.userId)
          }
        }
      );

      if (result === 0) {
        throw new Meteor.Error('update-failed', 'Failed to add link');
      }

      // Get and return the updated task
      const updatedTask = await Tasks.findOneAsync(taskId);
      console.log('Task after adding link:', updatedTask);
      return updatedTask;
    } catch (error) {
      console.error('Error adding link:', error);
      if (error instanceof Meteor.Error) {
        throw error;
      }
      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
    }
  },

  async 'tasks.removeAttachment'(taskId, attachmentIndex) {
    try {
      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
      }

      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('not-found', 'Task not found');
      }

      // Check if the attachment exists
      if (!task.attachments || !task.attachments[attachmentIndex]) {
        throw new Meteor.Error('not-found', 'Attachment not found');
      }

      const attachment = task.attachments[attachmentIndex];
      
      // Convert both IDs to strings for comparison
      const currentUserId = String(this.userId);
      const uploadedById = String(attachment.uploadedBy);
      
      // Only allow the uploader to remove the attachment
      if (currentUserId !== uploadedById) {
        throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
      }

      // Create a new array without the specified attachment
      const updatedAttachments = [...task.attachments];
      updatedAttachments.splice(attachmentIndex, 1);

      // Update the task with the new attachments array
      const result = await Tasks.updateAsync(
        { _id: taskId },
        {
          $set: {
            attachments: updatedAttachments,
            updatedAt: new Date(),
            updatedBy: currentUserId
          }
        }
      );

      if (result === 0) {
        throw new Meteor.Error('update-failed', 'Failed to remove attachment');
      }

      // Get and return the updated task
      const updatedTask = await Tasks.findOneAsync(taskId);
      console.log('Task after removing attachment:', updatedTask);
      return updatedTask;
    } catch (error) {
      console.error('Error removing attachment:', error);
      if (error instanceof Meteor.Error) {
        throw error;
      }
      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
    }
  },

  async 'tasks.removeLink'(taskId, linkIndex) {
    try {
      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
      }

      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('not-found', 'Task not found');
      }

      // Check if the link exists
      if (!task.links || !task.links[linkIndex]) {
        throw new Meteor.Error('not-found', 'Link not found');
      }

      const link = task.links[linkIndex];
      
      // Convert both IDs to strings for comparison
      const currentUserId = String(this.userId);
      const addedById = String(link.addedBy);
      
      // Only allow the user who added the link to remove it
      if (currentUserId !== addedById) {
        throw new Meteor.Error('not-authorized', 'You can only remove your own links');
      }

      // Create a new array without the specified link
      const updatedLinks = [...task.links];
      updatedLinks.splice(linkIndex, 1);

      // Update the task with the new links array
      const result = await Tasks.updateAsync(
        { _id: taskId },
        {
          $set: {
            links: updatedLinks,
            updatedAt: new Date(),
            updatedBy: currentUserId
          }
        }
      );

      if (result === 0) {
        throw new Meteor.Error('update-failed', 'Failed to remove link');
      }

      // Get and return the updated task
      const updatedTask = await Tasks.findOneAsync(taskId);
      console.log('Task after removing link:', updatedTask);
      return updatedTask;
    } catch (error) {
      console.error('Error removing link:', error);
      if (error instanceof Meteor.Error) {
        throw error;
      }
      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
    }
  },

  async 'tasks.findOne'(taskId) {
    check(taskId, String);
    
    if (!this.userId) {
      throw new Meteor.Error('Not authorized.');
    }

    const task = await Tasks.findOneAsync(taskId);
    if (!task) {
      throw new Meteor.Error('task-not-found', 'Task not found');
    }

    return task;
  },
}); 