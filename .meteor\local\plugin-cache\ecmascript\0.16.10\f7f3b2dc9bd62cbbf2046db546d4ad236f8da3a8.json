{"metadata": {}, "options": {"assumptions": {}, "compact": false, "sourceMaps": true, "ast": true, "babelrc": false, "configFile": false, "parserOpts": {"sourceType": "module", "sourceFileName": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "plugins": ["*", "flow", "jsx", "asyncGenerators", "bigInt", "classPrivateMethods", "classPrivateProperties", "classProperties", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportExtensions", "exportNamespaceFrom", "functionBind", "functionSent", "importMeta", "nullishCoalescingOperator", "numericSeparator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", ["pipelineOperator", {"proposal": "minimal"}], "throwExpressions", "topLevelAwait", "objectRestSpread", "objectRestSpread", "asyncGenerators", "classProperties", "classPrivateProperties", "jsx", "nullishCoalescingOperator", "nullishCoalescingOperator", "optionalChaining", "optionalChaining", "optionalCatchBinding", "optionalCatchBinding", "classProperties", "classPrivateProperties", "classPrivateMethods", "classProperties", "classPrivateProperties", "asyncGenerators", "asyncGenerators", "objectRestSpread", "objectRestSpread", "logicalAssignment"], "allowImportExportEverywhere": true, "allowReturnOutsideFunction": true, "allowUndeclaredExports": true, "strictMode": false}, "caller": {"name": "meteor", "arch": "os.windows.x86_64"}, "sourceFileName": "server/main.js", "filename": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "targets": {}, "cloneInputAst": true, "browserslistConfigFile": false, "passPerPreset": false, "envName": "development", "cwd": "C:\\Users\\<USER>\\smart-task-management-system", "root": "C:\\Users\\<USER>\\smart-task-management-system", "rootMode": "root", "plugins": [{"key": "base$0", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$0", "visitor": {"Program": {"enter": [null], "exit": [null]}, "_exploded": true, "_verified": true}, "options": {"avoidModernSyntax": false, "enforceStrictMode": false, "dynamicImport": true, "generateLetDeclarations": true, "topLevelAwait": true}, "externalDependencies": []}, {"key": "transform-runtime", "visitor": {"MemberExpression": {"enter": [null]}, "ObjectPattern": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "Identifier": {"enter": [null]}, "JSXIdentifier": {"enter": [null]}}, "options": {"version": "7.17.2", "helpers": true, "useESModules": false, "corejs": false}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-react-jsx", "visitor": {"JSXNamespacedName": {"enter": [null]}, "JSXSpreadChild": {"enter": [null]}, "Program": {"enter": [null]}, "JSXFragment": {"exit": [null]}, "JSXElement": {"exit": [null]}, "JSXAttribute": {"enter": [null]}}, "options": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "runtime": "classic", "throwIfNamespace": true, "useBuiltIns": false}, "externalDependencies": []}, {"key": "transform-react-display-name", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-pure-annotations", "visitor": {"CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "syntax-nullish-coalescing-operator", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-nullish-coalescing-operator", "visitor": {"LogicalExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-chaining", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-chaining", "visitor": {"OptionalCallExpression": {"enter": [null]}, "OptionalMemberExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-catch-binding", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-catch-binding", "visitor": {"CatchClause": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-class-properties", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-async-generators", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-logical-assignment-operators", "visitor": {"AssignmentExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-literals", "visitor": {"NumericLiteral": {"enter": [null]}, "StringLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-template-literals", "visitor": {"TaggedTemplateExpression": {"enter": [null]}, "TemplateLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-parameters", "visitor": {"_exploded": true, "_verified": true, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-exponentiation-operator", "visitor": {"AssignmentExpression": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}], "presets": [], "generatorOpts": {"filename": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "comments": true, "compact": false, "sourceMaps": true, "sourceFileName": "server/main.js"}}, "code": "!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {\n  \"use strict\";\n  try {\n    let _objectSpread;\n    module.link(\"@babel/runtime/helpers/objectSpread2\", {\n      default(v) {\n        _objectSpread = v;\n      }\n    }, 0);\n    let Meteor;\n    module.link(\"meteor/meteor\", {\n      Meteor(v) {\n        Meteor = v;\n      }\n    }, 0);\n    let LinksCollection;\n    module.link(\"/imports/api/links\", {\n      LinksCollection(v) {\n        LinksCollection = v;\n      }\n    }, 1);\n    let Accounts;\n    module.link(\"meteor/accounts-base\", {\n      Accounts(v) {\n        Accounts = v;\n      }\n    }, 2);\n    let Email;\n    module.link(\"meteor/email\", {\n      Email(v) {\n        Email = v;\n      }\n    }, 3);\n    let Tasks;\n    module.link(\"/imports/api/tasks\", {\n      Tasks(v) {\n        Tasks = v;\n      }\n    }, 4);\n    let Roles;\n    module.link(\"meteor/alanning:roles\", {\n      Roles(v) {\n        Roles = v;\n      }\n    }, 5);\n    let check;\n    module.link(\"meteor/check\", {\n      check(v) {\n        check = v;\n      }\n    }, 6);\n    let bcrypt;\n    module.link(\"bcrypt\", {\n      default(v) {\n        bcrypt = v;\n      }\n    }, 7);\n    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();\n    async function insertLink(_ref) {\n      let {\n        title,\n        url\n      } = _ref;\n      await LinksCollection.insertAsync({\n        title,\n        url,\n        createdAt: new Date()\n      });\n    }\n    const ADMIN_TOKEN = '123456';\n    Meteor.startup(async () => {\n      var _Meteor$settings$priv;\n      // Ensure indexes for Tasks collection\n      try {\n        await Tasks.createIndex({\n          createdAt: 1\n        });\n        await Tasks.createIndex({\n          assignedTo: 1\n        });\n        await Tasks.createIndex({\n          createdBy: 1\n        });\n\n        // Ensure indexes for Users collection\n        // Note: emails.address index is already created by Meteor accounts system\n        await Meteor.users.createIndex({\n          roles: 1\n        }, {\n          background: true\n        });\n      } catch (error) {\n        console.warn('[Startup] Index creation warning:', error.message);\n      }\n\n      // Check if we have any team members\n      try {\n        const allUsers = await Meteor.users.find().fetchAsync();\n        console.log('[Startup] All users:', allUsers.map(user => {\n          var _user$emails, _user$emails$;\n          return {\n            id: user._id,\n            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,\n            roles: user.roles,\n            profile: user.profile\n          };\n        }));\n\n        // First, ensure all users have roles and createdAt\n        for (const user of allUsers) {\n          const updates = {};\n          if (!user.roles || !Array.isArray(user.roles)) {\n            updates.roles = ['team-member'];\n          }\n          if (!user.createdAt) {\n            updates.createdAt = new Date();\n          }\n          if (Object.keys(updates).length > 0) {\n            var _user$emails2, _user$emails2$;\n            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);\n            await Meteor.users.updateAsync(user._id, {\n              $set: updates\n            });\n          }\n        }\n        const teamMembersCount = await Meteor.users.find({\n          'roles': 'team-member'\n        }).countAsync();\n        console.log('[Startup] Found team members:', teamMembersCount);\n\n        // Create test team members if none exist\n        if (teamMembersCount === 0) {\n          console.log('[Startup] Creating test team members');\n          try {\n            // Create multiple test team members\n            const testMembers = [{\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'John',\n              lastName: 'Doe'\n            }, {\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'Jane',\n              lastName: 'Smith'\n            }];\n            for (const member of testMembers) {\n              const userId = await Accounts.createUserAsync({\n                email: member.email,\n                password: member.password,\n                role: 'team-member',\n                createdAt: new Date(),\n                profile: {\n                  firstName: member.firstName,\n                  lastName: member.lastName,\n                  role: 'team-member',\n                  fullName: \"\".concat(member.firstName, \" \").concat(member.lastName)\n                }\n              });\n\n              // Set the role explicitly\n              await Meteor.users.updateAsync(userId, {\n                $set: {\n                  roles: ['team-member']\n                }\n              });\n              console.log('[Startup] Created test team member:', {\n                id: userId,\n                email: member.email,\n                name: \"\".concat(member.firstName, \" \").concat(member.lastName)\n              });\n            }\n          } catch (error) {\n            console.error('[Startup] Error creating test team members:', error);\n          }\n        }\n      } catch (error) {\n        console.error('[Startup] Error checking team members:', error);\n      }\n\n      // Email configuration from settings\n      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;\n\n      // Configure email SMTP\n      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {\n        process.env.MAIL_URL = \"smtp://\".concat(encodeURIComponent(emailSettings.username), \":\").concat(encodeURIComponent(emailSettings.password), \"@\").concat(emailSettings.server, \":\").concat(emailSettings.port);\n\n        // Test email configuration\n        try {\n          console.log('Testing email configuration...');\n          Email.send({\n            to: emailSettings.username,\n            from: emailSettings.username,\n            subject: 'Test Email',\n            text: 'If you receive this email, your email configuration is working correctly.'\n          });\n          console.log('Test email sent successfully!');\n        } catch (error) {\n          console.error('Error sending test email:', error);\n        }\n      } else {\n        console.warn('Email configuration is missing in settings.json');\n      }\n\n      // Configure account creation - allow login without email verification\n      Accounts.config({\n        sendVerificationEmail: false,\n        // Disable email verification requirement\n        forbidClientAccountCreation: false\n      });\n\n      // Add login validation hook to explicitly allow all login attempts\n      Accounts.validateLoginAttempt(attempt => {\n        var _attempt$error, _attempt$user;\n        console.log('[validateLoginAttempt] Login attempt:', {\n          type: attempt.type,\n          allowed: attempt.allowed,\n          error: (_attempt$error = attempt.error) === null || _attempt$error === void 0 ? void 0 : _attempt$error.message,\n          userId: (_attempt$user = attempt.user) === null || _attempt$user === void 0 ? void 0 : _attempt$user._id,\n          methodName: attempt.methodName\n        });\n\n        // Always allow login attempts - bypass any built-in restrictions\n        if (attempt.user && attempt.type === 'password') {\n          console.log('[validateLoginAttempt] Allowing password login for user:', attempt.user._id);\n          return true;\n        }\n\n        // Allow other types of login attempts as well\n        return true;\n      });\n\n      // Customize verification email\n      Accounts.emailTemplates.siteName = \"Task Management System\";\n      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? \"Task Management System <\".concat(emailSettings.username, \">\") : \"Task Management System <<EMAIL>>\";\n      Accounts.emailTemplates.verifyEmail = {\n        subject() {\n          return \"Verify Your Email Address\";\n        },\n        text(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"Hello,\\n\\n\" + \"To verify your email address (\".concat(emailAddress, \"), please click the link below:\\n\\n\") + \"\".concat(url, \"\\n\\n\") + \"If you did not request this verification, please ignore this email.\\n\\n\" + \"Thanks,\\n\" + \"Your Task Management System Team\";\n        },\n        html(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"\\n        <html>\\n          <body style=\\\"font-family: Arial, sans-serif; padding: 20px; color: #333;\\\">\\n            <h2 style=\\\"color: #00875a;\\\">Verify Your Email Address</h2>\\n            <p>Hello,</p>\\n            <p>To verify your email address (\".concat(emailAddress, \"), please click the button below:</p>\\n            <p style=\\\"margin: 20px 0;\\\">\\n              <a href=\\\"\").concat(url, \"\\\" \\n                 style=\\\"background-color: #00875a; \\n                        color: white; \\n                        padding: 12px 25px; \\n                        text-decoration: none; \\n                        border-radius: 4px;\\n                        display: inline-block;\\\">\\n                Verify Email Address\\n              </a>\\n            </p>\\n            <p>If you did not request this verification, please ignore this email.</p>\\n            <p>Thanks,<br>Your Task Management System Team</p>\\n          </body>\\n        </html>\\n      \");\n        }\n      };\n\n      // If the Links collection is empty, add some data.\n      if ((await LinksCollection.find().countAsync()) === 0) {\n        await insertLink({\n          title: 'Do the Tutorial',\n          url: 'https://www.meteor.com/tutorials/react/creating-an-app'\n        });\n        await insertLink({\n          title: 'Follow the Guide',\n          url: 'https://guide.meteor.com'\n        });\n        await insertLink({\n          title: 'Read the Docs',\n          url: 'https://docs.meteor.com'\n        });\n        await insertLink({\n          title: 'Discussions',\n          url: 'https://forums.meteor.com'\n        });\n      }\n\n      // We publish the entire Links collection to all clients.\n      // In order to be fetched in real-time to the clients\n      Meteor.publish(\"links\", function () {\n        return LinksCollection.find();\n      });\n\n      // Add custom fields to users\n      Accounts.onCreateUser((options, user) => {\n        var _customizedUser$email, _customizedUser$email2;\n        console.log('[onCreateUser] Creating user with options:', {\n          email: options.email,\n          role: options.role,\n          profile: options.profile,\n          createdAt: options.createdAt\n        });\n        const customizedUser = _objectSpread({}, user);\n\n        // Ensure we have a profile\n        customizedUser.profile = options.profile || {};\n\n        // Add role from options\n        const role = options.role || 'team-member';\n        customizedUser.roles = [role];\n\n        // Set createdAt if provided, otherwise use current date\n        customizedUser.createdAt = options.createdAt || new Date();\n        console.log('[onCreateUser] Created user:', {\n          id: customizedUser._id,\n          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,\n          roles: customizedUser.roles,\n          profile: customizedUser.profile,\n          createdAt: customizedUser.createdAt\n        });\n        return customizedUser;\n      });\n\n      // Publish team members\n      Meteor.publish('teamMembers', function () {\n        console.log('[teamMembers] Publication called, userId:', this.userId);\n        if (!this.userId) {\n          console.log('[teamMembers] No userId, returning ready');\n          return this.ready();\n        }\n        try {\n          // Simple query to find all team members\n          const teamMembers = Meteor.users.find({\n            $or: [{\n              'roles': 'team-member'\n            }, {\n              'profile.role': 'team-member'\n            }]\n          }, {\n            fields: {\n              emails: 1,\n              roles: 1,\n              'profile.firstName': 1,\n              'profile.lastName': 1,\n              'profile.fullName': 1,\n              createdAt: 1\n            }\n          });\n          console.log('[teamMembers] Publishing team members');\n          return teamMembers;\n        } catch (error) {\n          console.error('[teamMembers] Error in publication:', error);\n          return this.ready();\n        }\n      });\n\n      // Publish user data with roles\n      Meteor.publish('userData', function () {\n        if (!this.userId) {\n          return this.ready();\n        }\n        console.log('[userData] Publishing data for user:', this.userId);\n        return Meteor.users.find({\n          _id: this.userId\n        }, {\n          fields: {\n            roles: 1,\n            emails: 1,\n            profile: 1\n          }\n        });\n      });\n    });\n\n    // Method to create a new user with role\n    Meteor.methods({\n      'users.create'(_ref2) {\n        let {\n          email,\n          password,\n          role,\n          adminToken,\n          firstName,\n          lastName\n        } = _ref2;\n        // Validate admin token if trying to create admin account\n        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n        }\n\n        // Validate password requirements\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(password)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(password)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(password)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(password)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Create the user\n        try {\n          const userId = Accounts.createUser({\n            email,\n            password,\n            role,\n            // This will be used in onCreateUser callback\n            profile: {\n              role,\n              // Store in profile as well for easy access\n              firstName,\n              lastName,\n              fullName: \"\".concat(firstName, \" \").concat(lastName)\n            }\n          });\n\n          // Send verification email\n          if (userId) {\n            Accounts.sendVerificationEmail(userId);\n          }\n          return userId;\n        } catch (error) {\n          throw new Meteor.Error('create-user-failed', error.message);\n        }\n      },\n      async 'users.getRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'User must be logged in');\n        }\n        try {\n          var _user$roles, _user$profile;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          if (!user) {\n            throw new Meteor.Error('user-not-found', 'User not found');\n          }\n\n          // Check both roles array and profile for role\n          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';\n          return role;\n        } catch (error) {\n          throw new Meteor.Error('get-role-failed', error.message);\n        }\n      },\n      'users.resendVerificationEmail'(email) {\n        // Find user by email\n        const user = Accounts.findUserByEmail(email);\n        if (!user) {\n          throw new Meteor.Error('user-not-found', 'No user found with this email address');\n        }\n\n        // Check if email is already verified\n        const userEmail = user.emails[0];\n        if (userEmail.verified) {\n          throw new Meteor.Error('already-verified', 'This email is already verified');\n        }\n\n        // Send verification email\n        try {\n          Accounts.sendVerificationEmail(user._id, email);\n          return true;\n        } catch (error) {\n          throw new Meteor.Error('verification-email-failed', error.message);\n        }\n      },\n      async 'users.forgotPassword'(data) {\n        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));\n        check(data, {\n          email: String,\n          newPassword: String\n        });\n        const {\n          email,\n          newPassword\n        } = data;\n        console.log('[forgotPassword] Processing request for email:', email);\n\n        // Find user by email using async method\n        console.log('[forgotPassword] Searching for user...');\n        let targetUser = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!targetUser) {\n          console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');\n          targetUser = await Meteor.users.findOneAsync({\n            'emails.address': {\n              $regex: new RegExp(\"^\".concat(email, \"$\"), 'i')\n            }\n          });\n          if (!targetUser) {\n            throw new Meteor.Error('user-not-found', 'No user found with this email address');\n          }\n          console.log('[forgotPassword] User found with case-insensitive search');\n        } else {\n          console.log('[forgotPassword] User found with direct search');\n        }\n\n        // Ensure we have a valid user with ID\n        if (!targetUser || !targetUser._id) {\n          throw new Meteor.Error('user-invalid', 'Found user but missing ID');\n        }\n        console.log('[forgotPassword] Final user ID:', targetUser._id);\n        console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);\n\n        // Validate password requirements\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(newPassword)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Comprehensive password update with debugging\n        try {\n          console.log('[forgotPassword] Starting password update...');\n\n          // First, check current user document\n          console.log('[forgotPassword] Checking current user document...');\n          const currentUser = await Meteor.users.findOneAsync(targetUser._id);\n          console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));\n          if (!currentUser) {\n            throw new Meteor.Error('user-not-found', 'User document not found during update');\n          }\n\n          // Create password hash using bcrypt directly\n          const bcrypt = require('bcrypt');\n          const saltRounds = 10;\n          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);\n          console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);\n          console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');\n\n          // Try multiple update approaches\n          let updateResult = 0;\n          let successMethod = null;\n\n          // Method 1: Update services.password.bcrypt directly\n          console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');\n          try {\n            updateResult = await Meteor.users.updateAsync(targetUser._id, {\n              $set: {\n                'services.password.bcrypt': hashedPassword\n              }\n            });\n            console.log('[forgotPassword] Method 1 result:', updateResult);\n            if (updateResult === 1) {\n              successMethod = 'Method 1: services.password.bcrypt';\n            }\n          } catch (method1Error) {\n            console.error('[forgotPassword] Method 1 error:', method1Error);\n          }\n\n          // Method 2: Update entire services.password object\n          if (updateResult !== 1) {\n            console.log('[forgotPassword] Method 2: Updating entire services.password object...');\n            try {\n              updateResult = await Meteor.users.updateAsync(targetUser._id, {\n                $set: {\n                  'services.password': {\n                    bcrypt: hashedPassword\n                  }\n                }\n              });\n              console.log('[forgotPassword] Method 2 result:', updateResult);\n              if (updateResult === 1) {\n                successMethod = 'Method 2: entire services.password object';\n              }\n            } catch (method2Error) {\n              console.error('[forgotPassword] Method 2 error:', method2Error);\n            }\n          }\n\n          // Method 3: Update entire services object\n          if (updateResult !== 1) {\n            console.log('[forgotPassword] Method 3: Updating entire services object...');\n            try {\n              var _currentUser$services, _currentUser$services2;\n              updateResult = await Meteor.users.updateAsync(targetUser._id, {\n                $set: {\n                  services: {\n                    password: {\n                      bcrypt: hashedPassword\n                    },\n                    resume: ((_currentUser$services = currentUser.services) === null || _currentUser$services === void 0 ? void 0 : _currentUser$services.resume) || {\n                      loginTokens: []\n                    },\n                    email: ((_currentUser$services2 = currentUser.services) === null || _currentUser$services2 === void 0 ? void 0 : _currentUser$services2.email) || {}\n                  }\n                }\n              });\n              console.log('[forgotPassword] Method 3 result:', updateResult);\n              if (updateResult === 1) {\n                successMethod = 'Method 3: entire services object';\n              }\n            } catch (method3Error) {\n              console.error('[forgotPassword] Method 3 error:', method3Error);\n            }\n          }\n\n          // Method 4: Test basic update capability\n          if (updateResult !== 1) {\n            console.log('[forgotPassword] Method 4: Testing basic update capability...');\n            try {\n              const testResult = await Meteor.users.updateAsync(targetUser._id, {\n                $set: {\n                  'profile.passwordResetTest': new Date()\n                }\n              });\n              console.log('[forgotPassword] Basic update test result:', testResult);\n              if (testResult === 1) {\n                console.log('[forgotPassword] Basic updates work, trying password with $unset first...');\n                // Try unsetting and then setting\n                await Meteor.users.updateAsync(targetUser._id, {\n                  $unset: {\n                    'services.password': ''\n                  }\n                });\n                updateResult = await Meteor.users.updateAsync(targetUser._id, {\n                  $set: {\n                    'services.password': {\n                      bcrypt: hashedPassword\n                    }\n                  }\n                });\n                console.log('[forgotPassword] Unset/Set method result:', updateResult);\n                if (updateResult === 1) {\n                  successMethod = 'Method 4: unset then set';\n                }\n              }\n            } catch (method4Error) {\n              console.error('[forgotPassword] Method 4 error:', method4Error);\n            }\n          }\n          if (updateResult === 1) {\n            var _updatedUser$services, _updatedUser$services2;\n            console.log(\"[forgotPassword] Password update successful using: \".concat(successMethod));\n\n            // Verify the update worked\n            const updatedUser = await Meteor.users.findOneAsync(targetUser._id);\n            console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));\n\n            // Test password verification\n            if ((_updatedUser$services = updatedUser.services) !== null && _updatedUser$services !== void 0 && (_updatedUser$services2 = _updatedUser$services.password) !== null && _updatedUser$services2 !== void 0 && _updatedUser$services2.bcrypt) {\n              const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);\n              console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');\n            }\n            return {\n              success: true,\n              message: 'Password updated successfully'\n            };\n          } else {\n            console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);\n\n            // Log user permissions and collection info\n            console.log('[forgotPassword] User ID:', targetUser._id);\n            console.log('[forgotPassword] User ID type:', typeof targetUser._id);\n            console.log('[forgotPassword] Current user exists:', !!currentUser);\n            console.log('[forgotPassword] User roles:', currentUser.roles);\n            throw new Meteor.Error('password-update-failed', 'Failed to update password in database');\n          }\n        } catch (error) {\n          console.error('[forgotPassword] Error during password update:', error);\n          throw new Meteor.Error('password-update-failed', \"Failed to update password: \".concat(error.message));\n        }\n      },\n      async 'users.debugUser'(_ref3) {\n        let {\n          email\n        } = _ref3;\n        try {\n          var _fullUser$services, _fullUser$services2, _fullUser$services2$p;\n          check(email, String);\n          console.log('[debugUser] Debugging user:', email);\n\n          // Find user using async method\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            console.log('[debugUser] User not found');\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[debugUser] User found:', user._id);\n\n          // Get full user document using async method\n          const fullUser = await Meteor.users.findOneAsync(user._id);\n          console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));\n          if (!fullUser) {\n            console.log('[debugUser] Full user document not found');\n            return {\n              success: false,\n              error: 'Full user document not found'\n            };\n          }\n\n          // Test basic update using async method\n          let testUpdateResult = null;\n          try {\n            testUpdateResult = await Meteor.users.updateAsync(user._id, {\n              $set: {\n                'profile.debugTest': new Date()\n              }\n            });\n            console.log('[debugUser] Test update result:', testUpdateResult);\n          } catch (updateError) {\n            console.error('[debugUser] Test update error:', updateError);\n          }\n\n          // Try using Accounts.setPassword if available\n          let hasSetPassword = false;\n          try {\n            hasSetPassword = typeof Accounts.setPassword === 'function';\n            console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);\n          } catch (setPasswordError) {\n            console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);\n          }\n          const result = {\n            success: true,\n            userId: user._id,\n            userIdType: typeof user._id,\n            hasServices: !!fullUser.services,\n            hasPassword: !!((_fullUser$services = fullUser.services) !== null && _fullUser$services !== void 0 && _fullUser$services.password),\n            hasBcrypt: !!((_fullUser$services2 = fullUser.services) !== null && _fullUser$services2 !== void 0 && (_fullUser$services2$p = _fullUser$services2.password) !== null && _fullUser$services2$p !== void 0 && _fullUser$services2$p.bcrypt),\n            roles: fullUser.roles || [],\n            profile: fullUser.profile || {},\n            testUpdateResult: testUpdateResult,\n            hasSetPassword: hasSetPassword,\n            servicesStructure: fullUser.services || {}\n          };\n          console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));\n          return result;\n        } catch (error) {\n          console.error('[debugUser] Error in debug method:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.testLogin'(_ref4) {\n        let {\n          email,\n          password\n        } = _ref4;\n        check(email, String);\n        check(password, String);\n        console.log('[testLogin] Testing login for email:', email);\n\n        // Find user using async method\n        const user = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!user) {\n          console.log('[testLogin] User not found');\n          return {\n            success: false,\n            error: 'User not found'\n          };\n        }\n        console.log('[testLogin] User found:', user._id);\n        console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));\n\n        // Test password verification\n        try {\n          // Check if password service exists\n          if (!user.services || !user.services.password || !user.services.password.bcrypt) {\n            console.log('[testLogin] No password hash found in user services');\n            return {\n              success: false,\n              error: 'No password hash found',\n              userId: user._id,\n              services: user.services\n            };\n          }\n          const bcrypt = require('bcrypt');\n          const storedHash = user.services.password.bcrypt;\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n          console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n          console.log('[testLogin] Password length:', password.length);\n          return {\n            success: passwordMatch,\n            userId: user._id,\n            hashPreview: storedHash.substring(0, 20) + '...',\n            passwordLength: password.length\n          };\n        } catch (error) {\n          console.error('[testLogin] Error during password test:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.comparePasswordFormats'(_ref5) {\n        var _user$services, _user$services$passwo;\n        let {\n          email\n        } = _ref5;\n        check(email, String);\n        console.log('[comparePasswordFormats] Checking password format for:', email);\n\n        // Find user\n        const user = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!user) {\n          return {\n            success: false,\n            error: 'User not found'\n          };\n        }\n        console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));\n\n        // Check if user has password\n        if (!((_user$services = user.services) !== null && _user$services !== void 0 && (_user$services$passwo = _user$services.password) !== null && _user$services$passwo !== void 0 && _user$services$passwo.bcrypt)) {\n          return {\n            success: false,\n            error: 'No password found'\n          };\n        }\n        const storedHash = user.services.password.bcrypt;\n        console.log('[comparePasswordFormats] Stored hash:', storedHash);\n        console.log('[comparePasswordFormats] Hash length:', storedHash.length);\n        console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));\n\n        // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)\n        const isBcrypt = /^\\$2[aby]\\$/.test(storedHash);\n        console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);\n        return {\n          success: true,\n          userId: user._id,\n          hashLength: storedHash.length,\n          hashPreview: storedHash.substring(0, 20) + '...',\n          isBcryptFormat: isBcrypt,\n          fullServices: user.services\n        };\n      },\n      async 'users.testActualLogin'(_ref6) {\n        let {\n          email,\n          password\n        } = _ref6;\n        check(email, String);\n        check(password, String);\n        console.log('[testActualLogin] Testing actual login for:', email);\n        try {\n          var _user$services2, _user$services2$passw, _user$emails3, _user$emails3$;\n          // Try to simulate what Meteor.loginWithPassword does\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            console.log('[testActualLogin] User not found');\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[testActualLogin] User found:', user._id);\n          console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));\n\n          // Check if password service exists\n          if (!((_user$services2 = user.services) !== null && _user$services2 !== void 0 && (_user$services2$passw = _user$services2.password) !== null && _user$services2$passw !== void 0 && _user$services2$passw.bcrypt)) {\n            console.log('[testActualLogin] No password hash found');\n            return {\n              success: false,\n              error: 'No password hash found'\n            };\n          }\n\n          // Test bcrypt verification\n          const bcrypt = require('bcrypt');\n          const storedHash = user.services.password.bcrypt;\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n          console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n\n          // Check if user has any login restrictions\n          const isVerified = ((_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.verified) || false;\n          console.log('[testActualLogin] Email verified:', isVerified);\n\n          // Check user roles\n          console.log('[testActualLogin] User roles:', user.roles);\n\n          // Try to create a login token manually to test if that works\n          let loginTokenTest = null;\n          try {\n            // This is what Meteor does internally for login\n            const stampedToken = Accounts._generateStampedLoginToken();\n            console.log('[testActualLogin] Generated login token:', !!stampedToken);\n            loginTokenTest = 'Token generation successful';\n          } catch (tokenError) {\n            console.error('[testActualLogin] Token generation error:', tokenError);\n            loginTokenTest = tokenError.message;\n          }\n          return {\n            success: passwordMatch,\n            userId: user._id,\n            passwordVerification: passwordMatch,\n            emailVerified: isVerified,\n            userRoles: user.roles,\n            hashPreview: storedHash.substring(0, 20) + '...',\n            loginTokenTest: loginTokenTest,\n            fullUserStructure: {\n              _id: user._id,\n              emails: user.emails,\n              services: user.services,\n              roles: user.roles,\n              profile: user.profile\n            }\n          };\n        } catch (error) {\n          console.error('[testActualLogin] Error:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.simulateLogin'(_ref7) {\n        let {\n          email,\n          password\n        } = _ref7;\n        check(email, String);\n        check(password, String);\n        console.log('[simulateLogin] Simulating login for:', email);\n        try {\n          var _user$services3, _user$services3$passw, _user$emails4, _user$emails4$;\n          // Find user\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[simulateLogin] User found:', user._id);\n\n          // Check password\n          const bcrypt = require('bcrypt');\n          const storedHash = (_user$services3 = user.services) === null || _user$services3 === void 0 ? void 0 : (_user$services3$passw = _user$services3.password) === null || _user$services3$passw === void 0 ? void 0 : _user$services3$passw.bcrypt;\n          if (!storedHash) {\n            return {\n              success: false,\n              error: 'No password hash found'\n            };\n          }\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[simulateLogin] Password match:', passwordMatch);\n          if (!passwordMatch) {\n            return {\n              success: false,\n              error: 'Invalid password'\n            };\n          }\n\n          // Check if there are any login restrictions\n          const restrictions = [];\n\n          // Check email verification requirement\n          const emailVerified = ((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.verified) || false;\n          if (!emailVerified) {\n            restrictions.push('Email not verified');\n          }\n\n          // Check if user is active (no disabled flag)\n          if (user.disabled) {\n            restrictions.push('User account disabled');\n          }\n\n          // Check roles\n          if (!user.roles || user.roles.length === 0) {\n            restrictions.push('No roles assigned');\n          }\n          console.log('[simulateLogin] Login restrictions:', restrictions);\n\n          // Try to manually create what loginWithPassword would do\n          let loginSimulation = 'Not attempted';\n          try {\n            // Check if we can generate a login token\n            const stampedToken = Accounts._generateStampedLoginToken();\n            if (stampedToken) {\n              loginSimulation = 'Login token generation successful';\n            }\n          } catch (tokenError) {\n            loginSimulation = \"Token error: \".concat(tokenError.message);\n          }\n          return {\n            success: passwordMatch && restrictions.length === 0,\n            userId: user._id,\n            passwordMatch: passwordMatch,\n            emailVerified: emailVerified,\n            restrictions: restrictions,\n            loginSimulation: loginSimulation,\n            userStructure: {\n              _id: user._id,\n              emails: user.emails,\n              roles: user.roles,\n              profile: user.profile,\n              disabled: user.disabled || false\n            }\n          };\n        } catch (error) {\n          console.error('[simulateLogin] Error:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.checkAndFixAdminRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _user$emails5, _user$emails5$;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          console.log('[checkAndFixAdminRole] Checking user:', {\n            id: user === null || user === void 0 ? void 0 : user._id,\n            email: user === null || user === void 0 ? void 0 : (_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address,\n            roles: user === null || user === void 0 ? void 0 : user.roles\n          });\n\n          // If user has no roles array, initialize it\n          if (!user.roles) {\n            await Meteor.users.updateAsync(this.userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return 'Roles initialized';\n          }\n\n          // If user has no roles or doesn't have admin role\n          if (!user.roles.includes('admin')) {\n            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n\n            // Check if this is the first user (they should be admin)\n            const totalUsers = await Meteor.users.find().countAsync();\n            if (totalUsers === 1) {\n              console.log('[checkAndFixAdminRole] First user, setting as admin');\n              await Meteor.users.updateAsync(this.userId, {\n                $set: {\n                  roles: ['admin']\n                }\n              });\n              return 'Admin role added';\n            }\n            return 'User is not admin';\n          }\n          return 'User is already admin';\n        } catch (error) {\n          console.error('[checkAndFixAdminRole] Error:', error);\n          throw new Meteor.Error('check-role-failed', error.message);\n        }\n      },\n      async 'users.diagnoseRoles'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _currentUser$roles;\n          const currentUser = await Meteor.users.findOneAsync(this.userId);\n          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {\n            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n          }\n          const allUsers = await Meteor.users.find().fetchAsync();\n          const usersWithIssues = [];\n          const fixes = [];\n          for (const user of allUsers) {\n            var _user$profile3, _user$roles2;\n            const issues = [];\n\n            // Check if roles array exists\n            if (!user.roles || !Array.isArray(user.roles)) {\n              var _user$profile2, _user$emails6, _user$emails6$;\n              issues.push('No roles array');\n              // Fix: Initialize roles based on profile\n              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [role]\n                }\n              });\n              fixes.push(\"Initialized roles for \".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));\n            }\n\n            // Check if role matches profile\n            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {\n              var _user$emails7, _user$emails7$;\n              issues.push('Role mismatch with profile');\n              // Fix: Update roles to match profile\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [user.profile.role]\n                }\n              });\n              fixes.push(\"Fixed role mismatch for \".concat((_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address));\n            }\n            if (issues.length > 0) {\n              var _user$emails8, _user$emails8$;\n              usersWithIssues.push({\n                email: (_user$emails8 = user.emails) === null || _user$emails8 === void 0 ? void 0 : (_user$emails8$ = _user$emails8[0]) === null || _user$emails8$ === void 0 ? void 0 : _user$emails8$.address,\n                issues\n              });\n            }\n          }\n          return {\n            usersWithIssues,\n            fixes,\n            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n          };\n        } catch (error) {\n          throw new Meteor.Error('diagnose-failed', error.message);\n        }\n      },\n      'users.createTestTeamMember'() {\n        // Only allow in development\n        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n          try {\n            const testMember = {\n              email: '<EMAIL>',\n              password: 'TestPass123!',\n              firstName: 'Test',\n              lastName: 'Member'\n            };\n            const userId = Accounts.createUser({\n              email: testMember.email,\n              password: testMember.password,\n              profile: {\n                firstName: testMember.firstName,\n                lastName: testMember.lastName,\n                role: 'team-member',\n                fullName: \"\".concat(testMember.firstName, \" \").concat(testMember.lastName)\n              }\n            });\n\n            // Set the role explicitly\n            Meteor.users.update(userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return {\n              success: true,\n              userId,\n              message: 'Test team member created successfully'\n            };\n          } catch (error) {\n            console.error('[createTestTeamMember] Error:', error);\n            throw new Meteor.Error('create-test-member-failed', error.message);\n          }\n        } else {\n          throw new Meteor.Error('not-development', 'This method is only available in development');\n        }\n      }\n    });\n    __reify_async_result__();\n  } catch (_reifyError) {\n    return __reify_async_result__(_reifyError);\n  }\n  __reify_async_result__()\n}, {\n  self: this,\n  async: false\n});", "map": {"version": 3, "names": ["_objectSpread", "module", "link", "default", "v", "Meteor", "LinksCollection", "Accounts", "Email", "Tasks", "Roles", "check", "bcrypt", "__reifyWaitForDeps__", "insertLink", "_ref", "title", "url", "insertAsync", "createdAt", "Date", "ADMIN_TOKEN", "startup", "_Meteor$settings$priv", "createIndex", "assignedTo", "created<PERSON>y", "users", "roles", "background", "error", "console", "warn", "message", "allUsers", "find", "fetchAsync", "log", "map", "user", "_user$emails", "_user$emails$", "id", "_id", "email", "emails", "address", "profile", "updates", "Array", "isArray", "Object", "keys", "length", "_user$emails2", "_user$emails2$", "updateAsync", "$set", "teamMembersCount", "countAsync", "testMembers", "password", "firstName", "lastName", "member", "userId", "createUserAsync", "role", "fullName", "concat", "name", "emailSettings", "settings", "private", "username", "process", "env", "MAIL_URL", "encodeURIComponent", "server", "port", "send", "to", "from", "subject", "text", "config", "sendVerificationEmail", "forbidClientAccountCreation", "validateLoginAttempt", "attempt", "_attempt$error", "_attempt$user", "type", "allowed", "methodName", "emailTemplates", "siteName", "verifyEmail", "emailAddress", "html", "publish", "onCreateUser", "options", "_customizedUser$email", "_customizedUser$email2", "customizedUser", "ready", "teamMembers", "$or", "fields", "methods", "users.create", "_ref2", "adminToken", "Error", "passwordRegex", "uppercase", "number", "special", "passwordErrors", "test", "push", "join", "createUser", "users.getRole", "_user$roles", "_user$profile", "findOneAsync", "users.resendVerificationEmail", "findUserByEmail", "userEmail", "verified", "users.forgotPassword", "data", "JSON", "stringify", "String", "newPassword", "targetUser", "$regex", "RegExp", "currentUser", "require", "saltRounds", "hashedPassword", "hashSync", "substring", "updateResult", "successMethod", "method1Error", "method2Error", "_currentUser$services", "_currentUser$services2", "services", "resume", "loginTokens", "method3Error", "testResult", "$unset", "method4Error", "_updatedUser$services", "_updatedUser$services2", "updatedUser", "testVerification", "compareSync", "success", "users.debugUser", "_ref3", "_fullUser$services", "_fullUser$services2", "_fullUser$services2$p", "fullUser", "testUpdateResult", "updateError", "hasSetPassword", "setPassword", "setPasswordError", "result", "userIdType", "hasServices", "hasPassword", "hasBcrypt", "servicesStructure", "users.testLogin", "_ref4", "storedHash", "passwordMatch", "hashPreview", "<PERSON><PERSON><PERSON><PERSON>", "users.comparePasswordFormats", "_ref5", "_user$services", "_user$services$passwo", "isBcrypt", "hash<PERSON><PERSON><PERSON>", "isBcryptFormat", "fullServices", "users.testActualLogin", "_ref6", "_user$services2", "_user$services2$passw", "_user$emails3", "_user$emails3$", "isVerified", "loginTokenTest", "stampedToken", "_generateStampedLoginToken", "tokenError", "passwordVerification", "emailVerified", "userRoles", "fullUserStructure", "users.simulateLogin", "_ref7", "_user$services3", "_user$services3$passw", "_user$emails4", "_user$emails4$", "restrictions", "disabled", "loginSimulation", "userStructure", "users.checkAndFixAdminRole", "_user$emails5", "_user$emails5$", "includes", "totalUsers", "users.diagnoseRoles", "_currentUser$roles", "usersWithIssues", "fixes", "_user$profile3", "_user$roles2", "issues", "_user$profile2", "_user$emails6", "_user$emails6$", "_user$emails7", "_user$emails7$", "_user$emails8", "_user$emails8$", "users.createTestTeamMember", "NODE_ENV", "testMember", "update", "__reify_async_result__", "_reifyError", "self", "async"], "sources": ["server/main.js"], "sourcesContent": ["import { Meteor } from 'meteor/meteor';\nimport { LinksCollection } from '/imports/api/links';\nimport { Accounts } from 'meteor/accounts-base';\nimport { Email } from 'meteor/email';\nimport { Tasks } from '/imports/api/tasks';\nimport { Roles } from 'meteor/alanning:roles';\nimport { check } from 'meteor/check';\nimport bcrypt from 'bcrypt';\n\nasync function insertLink({ title, url }) {\n  await LinksCollection.insertAsync({ title, url, createdAt: new Date() });\n}\n\nconst ADMIN_TOKEN = '123456';\n\nMeteor.startup(async () => {\n  // Ensure indexes for Tasks collection\n  try {\n    await Tasks.createIndex({ createdAt: 1 });\n    await Tasks.createIndex({ assignedTo: 1 });\n    await Tasks.createIndex({ createdBy: 1 });\n\n    // Ensure indexes for Users collection\n    // Note: emails.address index is already created by Meteor accounts system\n    await Meteor.users.createIndex({ roles: 1 }, { background: true });\n  } catch (error) {\n    console.warn('[Startup] Index creation warning:', error.message);\n  }\n\n  // Check if we have any team members\n  try {\n    const allUsers = await Meteor.users.find().fetchAsync();\n    console.log('[Startup] All users:', allUsers.map(user => ({\n      id: user._id,\n      email: user.emails?.[0]?.address,\n      roles: user.roles,\n      profile: user.profile\n    })));\n\n    // First, ensure all users have roles and createdAt\n    for (const user of allUsers) {\n      const updates = {};\n      \n      if (!user.roles || !Array.isArray(user.roles)) {\n        updates.roles = ['team-member'];\n      }\n      \n      if (!user.createdAt) {\n        updates.createdAt = new Date();\n      }\n      \n      if (Object.keys(updates).length > 0) {\n        console.log('[Startup] Fixing missing fields for user:', user.emails?.[0]?.address);\n        await Meteor.users.updateAsync(user._id, {\n          $set: updates\n        });\n      }\n    }\n\n    const teamMembersCount = await Meteor.users.find({ 'roles': 'team-member' }).countAsync();\n    console.log('[Startup] Found team members:', teamMembersCount);\n\n    // Create test team members if none exist\n    if (teamMembersCount === 0) {\n      console.log('[Startup] Creating test team members');\n      try {\n        // Create multiple test team members\n        const testMembers = [\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'John',\n            lastName: 'Doe'\n          },\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'Jane',\n            lastName: 'Smith'\n          }\n        ];\n\n        for (const member of testMembers) {\n          const userId = await Accounts.createUserAsync({\n            email: member.email,\n            password: member.password,\n            role: 'team-member',\n            createdAt: new Date(),\n            profile: {\n              firstName: member.firstName,\n              lastName: member.lastName,\n              role: 'team-member',\n              fullName: `${member.firstName} ${member.lastName}`\n            }\n          });\n\n          // Set the role explicitly\n          await Meteor.users.updateAsync(userId, {\n            $set: { roles: ['team-member'] }\n          });\n\n          console.log('[Startup] Created test team member:', {\n            id: userId,\n            email: member.email,\n            name: `${member.firstName} ${member.lastName}`\n          });\n        }\n      } catch (error) {\n        console.error('[Startup] Error creating test team members:', error);\n      }\n    }\n  } catch (error) {\n    console.error('[Startup] Error checking team members:', error);\n  }\n\n  // Email configuration from settings\n  const emailSettings = Meteor.settings.private?.email;\n  \n  // Configure email SMTP\n  if (emailSettings?.username && emailSettings?.password) {\n    process.env.MAIL_URL = `smtp://${encodeURIComponent(emailSettings.username)}:${encodeURIComponent(emailSettings.password)}@${emailSettings.server}:${emailSettings.port}`;\n    \n    // Test email configuration\n    try {\n      console.log('Testing email configuration...');\n      Email.send({\n        to: emailSettings.username,\n        from: emailSettings.username,\n        subject: 'Test Email',\n        text: 'If you receive this email, your email configuration is working correctly.'\n      });\n      console.log('Test email sent successfully!');\n    } catch (error) {\n      console.error('Error sending test email:', error);\n    }\n  } else {\n    console.warn('Email configuration is missing in settings.json');\n  }\n\n  // Configure account creation - allow login without email verification\n  Accounts.config({\n    sendVerificationEmail: false,  // Disable email verification requirement\n    forbidClientAccountCreation: false\n  });\n\n  // Add login validation hook to explicitly allow all login attempts\n  Accounts.validateLoginAttempt((attempt) => {\n    console.log('[validateLoginAttempt] Login attempt:', {\n      type: attempt.type,\n      allowed: attempt.allowed,\n      error: attempt.error?.message,\n      userId: attempt.user?._id,\n      methodName: attempt.methodName\n    });\n\n    // Always allow login attempts - bypass any built-in restrictions\n    if (attempt.user && attempt.type === 'password') {\n      console.log('[validateLoginAttempt] Allowing password login for user:', attempt.user._id);\n      return true;\n    }\n\n    // Allow other types of login attempts as well\n    return true;\n  });\n\n  // Customize verification email\n  Accounts.emailTemplates.siteName = \"Task Management System\";\n  Accounts.emailTemplates.from = emailSettings?.username ? \n    `Task Management System <${emailSettings.username}>` : \n    \"Task Management System <<EMAIL>>\";\n\n  Accounts.emailTemplates.verifyEmail = {\n    subject() {\n      return \"Verify Your Email Address\";\n    },\n    text(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `Hello,\\n\\n`\n        + `To verify your email address (${emailAddress}), please click the link below:\\n\\n`\n        + `${url}\\n\\n`\n        + `If you did not request this verification, please ignore this email.\\n\\n`\n        + `Thanks,\\n`\n        + `Your Task Management System Team`;\n    },\n    html(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (${emailAddress}), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"${url}\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      `;\n    }\n  };\n\n  // If the Links collection is empty, add some data.\n  if (await LinksCollection.find().countAsync() === 0) {\n    await insertLink({\n      title: 'Do the Tutorial',\n      url: 'https://www.meteor.com/tutorials/react/creating-an-app',\n    });\n\n    await insertLink({\n      title: 'Follow the Guide',\n      url: 'https://guide.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Read the Docs',\n      url: 'https://docs.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Discussions',\n      url: 'https://forums.meteor.com',\n    });\n  }\n\n  // We publish the entire Links collection to all clients.\n  // In order to be fetched in real-time to the clients\n  Meteor.publish(\"links\", function () {\n    return LinksCollection.find();\n  });\n\n  // Add custom fields to users\n  Accounts.onCreateUser((options, user) => {\n    console.log('[onCreateUser] Creating user with options:', {\n      email: options.email,\n      role: options.role,\n      profile: options.profile,\n      createdAt: options.createdAt\n    });\n\n    const customizedUser = { ...user };\n    \n    // Ensure we have a profile\n    customizedUser.profile = options.profile || {};\n    \n    // Add role from options\n    const role = options.role || 'team-member';\n    customizedUser.roles = [role];\n    \n    // Set createdAt if provided, otherwise use current date\n    customizedUser.createdAt = options.createdAt || new Date();\n    \n    console.log('[onCreateUser] Created user:', {\n      id: customizedUser._id,\n      email: customizedUser.emails?.[0]?.address,\n      roles: customizedUser.roles,\n      profile: customizedUser.profile,\n      createdAt: customizedUser.createdAt\n    });\n    \n    return customizedUser;\n  });\n\n  // Publish team members\n  Meteor.publish('teamMembers', function() {\n    console.log('[teamMembers] Publication called, userId:', this.userId);\n    \n    if (!this.userId) {\n      console.log('[teamMembers] No userId, returning ready');\n      return this.ready();\n    }\n\n    try {\n      // Simple query to find all team members\n      const teamMembers = Meteor.users.find(\n        { \n          $or: [\n            { 'roles': 'team-member' },\n            { 'profile.role': 'team-member' }\n          ]\n        },\n        { \n          fields: { \n            emails: 1, \n            roles: 1,\n            'profile.firstName': 1,\n            'profile.lastName': 1,\n            'profile.fullName': 1,\n            createdAt: 1\n          }\n        }\n      );\n\n      console.log('[teamMembers] Publishing team members');\n      return teamMembers;\n    } catch (error) {\n      console.error('[teamMembers] Error in publication:', error);\n      return this.ready();\n    }\n  });\n\n  // Publish user data with roles\n  Meteor.publish('userData', function() {\n    if (!this.userId) {\n      return this.ready();\n    }\n\n    console.log('[userData] Publishing data for user:', this.userId);\n    \n    return Meteor.users.find(\n      { _id: this.userId },\n      { \n        fields: { \n          roles: 1, \n          emails: 1,\n          profile: 1\n        } \n      }\n    );\n  });\n});\n\n// Method to create a new user with role\nMeteor.methods({\n  'users.create'({ email, password, role, adminToken, firstName, lastName }) {\n    // Validate admin token if trying to create admin account\n    if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n      throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n    }\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(password)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(password)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(password)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(password)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Create the user\n    try {\n      const userId = Accounts.createUser({\n        email,\n        password,\n        role, // This will be used in onCreateUser callback\n        profile: {\n          role, // Store in profile as well for easy access\n          firstName,\n          lastName,\n          fullName: `${firstName} ${lastName}`\n        }\n      });\n\n      // Send verification email\n      if (userId) {\n        Accounts.sendVerificationEmail(userId);\n      }\n\n      return userId;\n    } catch (error) {\n      throw new Meteor.Error('create-user-failed', error.message);\n    }\n  },\n\n  async 'users.getRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'User must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      if (!user) {\n        throw new Meteor.Error('user-not-found', 'User not found');\n      }\n      \n      // Check both roles array and profile for role\n      const role = user.roles?.[0] || user.profile?.role || 'team-member';\n      return role;\n    } catch (error) {\n      throw new Meteor.Error('get-role-failed', error.message);\n    }\n  },\n\n  'users.resendVerificationEmail'(email) {\n    // Find user by email\n    const user = Accounts.findUserByEmail(email);\n    if (!user) {\n      throw new Meteor.Error('user-not-found', 'No user found with this email address');\n    }\n\n    // Check if email is already verified\n    const userEmail = user.emails[0];\n    if (userEmail.verified) {\n      throw new Meteor.Error('already-verified', 'This email is already verified');\n    }\n\n    // Send verification email\n    try {\n      Accounts.sendVerificationEmail(user._id, email);\n      return true;\n    } catch (error) {\n      throw new Meteor.Error('verification-email-failed', error.message);\n    }\n  },\n\n  async 'users.forgotPassword'(data) {\n    console.log('[forgotPassword] Method called with data:', JSON.stringify(data));\n\n    check(data, {\n      email: String,\n      newPassword: String\n    });\n\n    const { email, newPassword } = data;\n    console.log('[forgotPassword] Processing request for email:', email);\n\n    // Find user by email using async method\n    console.log('[forgotPassword] Searching for user...');\n    let targetUser = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!targetUser) {\n      console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');\n      targetUser = await Meteor.users.findOneAsync({\n        'emails.address': { $regex: new RegExp(`^${email}$`, 'i') }\n      });\n\n      if (!targetUser) {\n        throw new Meteor.Error('user-not-found', 'No user found with this email address');\n      }\n\n      console.log('[forgotPassword] User found with case-insensitive search');\n    } else {\n      console.log('[forgotPassword] User found with direct search');\n    }\n\n    // Ensure we have a valid user with ID\n    if (!targetUser || !targetUser._id) {\n      throw new Meteor.Error('user-invalid', 'Found user but missing ID');\n    }\n\n    console.log('[forgotPassword] Final user ID:', targetUser._id);\n    console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(newPassword)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Comprehensive password update with debugging\n    try {\n      console.log('[forgotPassword] Starting password update...');\n\n      // First, check current user document\n      console.log('[forgotPassword] Checking current user document...');\n      const currentUser = await Meteor.users.findOneAsync(targetUser._id);\n      console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));\n\n      if (!currentUser) {\n        throw new Meteor.Error('user-not-found', 'User document not found during update');\n      }\n\n      // Create password hash using bcrypt directly\n      const bcrypt = require('bcrypt');\n      const saltRounds = 10;\n      const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);\n      console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);\n      console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');\n\n      // Try multiple update approaches\n      let updateResult = 0;\n      let successMethod = null;\n\n      // Method 1: Update services.password.bcrypt directly\n      console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');\n      try {\n        updateResult = await Meteor.users.updateAsync(targetUser._id, {\n          $set: {\n            'services.password.bcrypt': hashedPassword\n          }\n        });\n        console.log('[forgotPassword] Method 1 result:', updateResult);\n        if (updateResult === 1) {\n          successMethod = 'Method 1: services.password.bcrypt';\n        }\n      } catch (method1Error) {\n        console.error('[forgotPassword] Method 1 error:', method1Error);\n      }\n\n      // Method 2: Update entire services.password object\n      if (updateResult !== 1) {\n        console.log('[forgotPassword] Method 2: Updating entire services.password object...');\n        try {\n          updateResult = await Meteor.users.updateAsync(targetUser._id, {\n            $set: {\n              'services.password': {\n                bcrypt: hashedPassword\n              }\n            }\n          });\n          console.log('[forgotPassword] Method 2 result:', updateResult);\n          if (updateResult === 1) {\n            successMethod = 'Method 2: entire services.password object';\n          }\n        } catch (method2Error) {\n          console.error('[forgotPassword] Method 2 error:', method2Error);\n        }\n      }\n\n      // Method 3: Update entire services object\n      if (updateResult !== 1) {\n        console.log('[forgotPassword] Method 3: Updating entire services object...');\n        try {\n          updateResult = await Meteor.users.updateAsync(targetUser._id, {\n            $set: {\n              services: {\n                password: {\n                  bcrypt: hashedPassword\n                },\n                resume: currentUser.services?.resume || { loginTokens: [] },\n                email: currentUser.services?.email || {}\n              }\n            }\n          });\n          console.log('[forgotPassword] Method 3 result:', updateResult);\n          if (updateResult === 1) {\n            successMethod = 'Method 3: entire services object';\n          }\n        } catch (method3Error) {\n          console.error('[forgotPassword] Method 3 error:', method3Error);\n        }\n      }\n\n      // Method 4: Test basic update capability\n      if (updateResult !== 1) {\n        console.log('[forgotPassword] Method 4: Testing basic update capability...');\n        try {\n          const testResult = await Meteor.users.updateAsync(targetUser._id, {\n            $set: {\n              'profile.passwordResetTest': new Date()\n            }\n          });\n          console.log('[forgotPassword] Basic update test result:', testResult);\n\n          if (testResult === 1) {\n            console.log('[forgotPassword] Basic updates work, trying password with $unset first...');\n            // Try unsetting and then setting\n            await Meteor.users.updateAsync(targetUser._id, {\n              $unset: { 'services.password': '' }\n            });\n\n            updateResult = await Meteor.users.updateAsync(targetUser._id, {\n              $set: {\n                'services.password': {\n                  bcrypt: hashedPassword\n                }\n              }\n            });\n            console.log('[forgotPassword] Unset/Set method result:', updateResult);\n            if (updateResult === 1) {\n              successMethod = 'Method 4: unset then set';\n            }\n          }\n        } catch (method4Error) {\n          console.error('[forgotPassword] Method 4 error:', method4Error);\n        }\n      }\n\n      if (updateResult === 1) {\n        console.log(`[forgotPassword] Password update successful using: ${successMethod}`);\n\n        // Verify the update worked\n        const updatedUser = await Meteor.users.findOneAsync(targetUser._id);\n        console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));\n\n        // Test password verification\n        if (updatedUser.services?.password?.bcrypt) {\n          const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);\n          console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');\n        }\n\n        return { success: true, message: 'Password updated successfully' };\n      } else {\n        console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);\n\n        // Log user permissions and collection info\n        console.log('[forgotPassword] User ID:', targetUser._id);\n        console.log('[forgotPassword] User ID type:', typeof targetUser._id);\n        console.log('[forgotPassword] Current user exists:', !!currentUser);\n        console.log('[forgotPassword] User roles:', currentUser.roles);\n\n        throw new Meteor.Error('password-update-failed', 'Failed to update password in database');\n      }\n\n    } catch (error) {\n      console.error('[forgotPassword] Error during password update:', error);\n      throw new Meteor.Error('password-update-failed', `Failed to update password: ${error.message}`);\n    }\n  },\n\n  async 'users.debugUser'({ email }) {\n    try {\n      check(email, String);\n\n      console.log('[debugUser] Debugging user:', email);\n\n      // Find user using async method\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        console.log('[debugUser] User not found');\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[debugUser] User found:', user._id);\n\n      // Get full user document using async method\n      const fullUser = await Meteor.users.findOneAsync(user._id);\n      console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));\n\n      if (!fullUser) {\n        console.log('[debugUser] Full user document not found');\n        return { success: false, error: 'Full user document not found' };\n      }\n\n      // Test basic update using async method\n      let testUpdateResult = null;\n      try {\n        testUpdateResult = await Meteor.users.updateAsync(user._id, {\n          $set: { 'profile.debugTest': new Date() }\n        });\n        console.log('[debugUser] Test update result:', testUpdateResult);\n      } catch (updateError) {\n        console.error('[debugUser] Test update error:', updateError);\n      }\n\n      // Try using Accounts.setPassword if available\n      let hasSetPassword = false;\n      try {\n        hasSetPassword = typeof Accounts.setPassword === 'function';\n        console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);\n      } catch (setPasswordError) {\n        console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);\n      }\n\n      const result = {\n        success: true,\n        userId: user._id,\n        userIdType: typeof user._id,\n        hasServices: !!fullUser.services,\n        hasPassword: !!(fullUser.services?.password),\n        hasBcrypt: !!(fullUser.services?.password?.bcrypt),\n        roles: fullUser.roles || [],\n        profile: fullUser.profile || {},\n        testUpdateResult: testUpdateResult,\n        hasSetPassword: hasSetPassword,\n        servicesStructure: fullUser.services || {}\n      };\n\n      console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));\n      return result;\n\n    } catch (error) {\n      console.error('[debugUser] Error in debug method:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.testLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[testLogin] Testing login for email:', email);\n\n    // Find user using async method\n    const user = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!user) {\n      console.log('[testLogin] User not found');\n      return { success: false, error: 'User not found' };\n    }\n\n    console.log('[testLogin] User found:', user._id);\n    console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));\n\n    // Test password verification\n    try {\n      // Check if password service exists\n      if (!user.services || !user.services.password || !user.services.password.bcrypt) {\n        console.log('[testLogin] No password hash found in user services');\n        return {\n          success: false,\n          error: 'No password hash found',\n          userId: user._id,\n          services: user.services\n        };\n      }\n\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services.password.bcrypt;\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n\n      console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n      console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n      console.log('[testLogin] Password length:', password.length);\n\n      return {\n        success: passwordMatch,\n        userId: user._id,\n        hashPreview: storedHash.substring(0, 20) + '...',\n        passwordLength: password.length\n      };\n    } catch (error) {\n      console.error('[testLogin] Error during password test:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.comparePasswordFormats'({ email }) {\n    check(email, String);\n\n    console.log('[comparePasswordFormats] Checking password format for:', email);\n\n    // Find user\n    const user = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!user) {\n      return { success: false, error: 'User not found' };\n    }\n\n    console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));\n\n    // Check if user has password\n    if (!user.services?.password?.bcrypt) {\n      return { success: false, error: 'No password found' };\n    }\n\n    const storedHash = user.services.password.bcrypt;\n    console.log('[comparePasswordFormats] Stored hash:', storedHash);\n    console.log('[comparePasswordFormats] Hash length:', storedHash.length);\n    console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));\n\n    // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)\n    const isBcrypt = /^\\$2[aby]\\$/.test(storedHash);\n    console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);\n\n    return {\n      success: true,\n      userId: user._id,\n      hashLength: storedHash.length,\n      hashPreview: storedHash.substring(0, 20) + '...',\n      isBcryptFormat: isBcrypt,\n      fullServices: user.services\n    };\n  },\n\n  async 'users.testActualLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[testActualLogin] Testing actual login for:', email);\n\n    try {\n      // Try to simulate what Meteor.loginWithPassword does\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        console.log('[testActualLogin] User not found');\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[testActualLogin] User found:', user._id);\n      console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));\n\n      // Check if password service exists\n      if (!user.services?.password?.bcrypt) {\n        console.log('[testActualLogin] No password hash found');\n        return { success: false, error: 'No password hash found' };\n      }\n\n      // Test bcrypt verification\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services.password.bcrypt;\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n\n      console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n      console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n\n      // Check if user has any login restrictions\n      const isVerified = user.emails?.[0]?.verified || false;\n      console.log('[testActualLogin] Email verified:', isVerified);\n\n      // Check user roles\n      console.log('[testActualLogin] User roles:', user.roles);\n\n      // Try to create a login token manually to test if that works\n      let loginTokenTest = null;\n      try {\n        // This is what Meteor does internally for login\n        const stampedToken = Accounts._generateStampedLoginToken();\n        console.log('[testActualLogin] Generated login token:', !!stampedToken);\n        loginTokenTest = 'Token generation successful';\n      } catch (tokenError) {\n        console.error('[testActualLogin] Token generation error:', tokenError);\n        loginTokenTest = tokenError.message;\n      }\n\n      return {\n        success: passwordMatch,\n        userId: user._id,\n        passwordVerification: passwordMatch,\n        emailVerified: isVerified,\n        userRoles: user.roles,\n        hashPreview: storedHash.substring(0, 20) + '...',\n        loginTokenTest: loginTokenTest,\n        fullUserStructure: {\n          _id: user._id,\n          emails: user.emails,\n          services: user.services,\n          roles: user.roles,\n          profile: user.profile\n        }\n      };\n\n    } catch (error) {\n      console.error('[testActualLogin] Error:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.simulateLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[simulateLogin] Simulating login for:', email);\n\n    try {\n      // Find user\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[simulateLogin] User found:', user._id);\n\n      // Check password\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services?.password?.bcrypt;\n\n      if (!storedHash) {\n        return { success: false, error: 'No password hash found' };\n      }\n\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n      console.log('[simulateLogin] Password match:', passwordMatch);\n\n      if (!passwordMatch) {\n        return { success: false, error: 'Invalid password' };\n      }\n\n      // Check if there are any login restrictions\n      const restrictions = [];\n\n      // Check email verification requirement\n      const emailVerified = user.emails?.[0]?.verified || false;\n      if (!emailVerified) {\n        restrictions.push('Email not verified');\n      }\n\n      // Check if user is active (no disabled flag)\n      if (user.disabled) {\n        restrictions.push('User account disabled');\n      }\n\n      // Check roles\n      if (!user.roles || user.roles.length === 0) {\n        restrictions.push('No roles assigned');\n      }\n\n      console.log('[simulateLogin] Login restrictions:', restrictions);\n\n      // Try to manually create what loginWithPassword would do\n      let loginSimulation = 'Not attempted';\n      try {\n        // Check if we can generate a login token\n        const stampedToken = Accounts._generateStampedLoginToken();\n        if (stampedToken) {\n          loginSimulation = 'Login token generation successful';\n        }\n      } catch (tokenError) {\n        loginSimulation = `Token error: ${tokenError.message}`;\n      }\n\n      return {\n        success: passwordMatch && restrictions.length === 0,\n        userId: user._id,\n        passwordMatch: passwordMatch,\n        emailVerified: emailVerified,\n        restrictions: restrictions,\n        loginSimulation: loginSimulation,\n        userStructure: {\n          _id: user._id,\n          emails: user.emails,\n          roles: user.roles,\n          profile: user.profile,\n          disabled: user.disabled || false\n        }\n      };\n\n    } catch (error) {\n      console.error('[simulateLogin] Error:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.checkAndFixAdminRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      console.log('[checkAndFixAdminRole] Checking user:', {\n        id: user?._id,\n        email: user?.emails?.[0]?.address,\n        roles: user?.roles\n      });\n      \n      // If user has no roles array, initialize it\n      if (!user.roles) {\n        await Meteor.users.updateAsync(this.userId, {\n          $set: { roles: ['team-member'] }\n        });\n        return 'Roles initialized';\n      }\n      \n      // If user has no roles or doesn't have admin role\n      if (!user.roles.includes('admin')) {\n        console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n        \n        // Check if this is the first user (they should be admin)\n        const totalUsers = await Meteor.users.find().countAsync();\n        if (totalUsers === 1) {\n          console.log('[checkAndFixAdminRole] First user, setting as admin');\n          await Meteor.users.updateAsync(this.userId, {\n            $set: { roles: ['admin'] }\n          });\n          return 'Admin role added';\n        }\n        return 'User is not admin';\n      }\n      \n      return 'User is already admin';\n    } catch (error) {\n      console.error('[checkAndFixAdminRole] Error:', error);\n      throw new Meteor.Error('check-role-failed', error.message);\n    }\n  },\n\n  async 'users.diagnoseRoles'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n\n    try {\n      const currentUser = await Meteor.users.findOneAsync(this.userId);\n      if (!currentUser.roles?.includes('admin')) {\n        throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n      }\n\n      const allUsers = await Meteor.users.find().fetchAsync();\n      const usersWithIssues = [];\n      const fixes = [];\n\n      for (const user of allUsers) {\n        const issues = [];\n        \n        // Check if roles array exists\n        if (!user.roles || !Array.isArray(user.roles)) {\n          issues.push('No roles array');\n          // Fix: Initialize roles based on profile\n          const role = user.profile?.role || 'team-member';\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [role] }\n          });\n          fixes.push(`Initialized roles for ${user.emails?.[0]?.address}`);\n        }\n        \n        // Check if role matches profile\n        if (user.profile?.role && user.roles?.[0] !== user.profile.role) {\n          issues.push('Role mismatch with profile');\n          // Fix: Update roles to match profile\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [user.profile.role] }\n          });\n          fixes.push(`Fixed role mismatch for ${user.emails?.[0]?.address}`);\n        }\n\n        if (issues.length > 0) {\n          usersWithIssues.push({\n            email: user.emails?.[0]?.address,\n            issues\n          });\n        }\n      }\n\n      return {\n        usersWithIssues,\n        fixes,\n        message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n      };\n    } catch (error) {\n      throw new Meteor.Error('diagnose-failed', error.message);\n    }\n  },\n\n  'users.createTestTeamMember'() {\n    // Only allow in development\n    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n      try {\n        const testMember = {\n          email: '<EMAIL>',\n          password: 'TestPass123!',\n          firstName: 'Test',\n          lastName: 'Member'\n        };\n\n        const userId = Accounts.createUser({\n          email: testMember.email,\n          password: testMember.password,\n          profile: {\n            firstName: testMember.firstName,\n            lastName: testMember.lastName,\n            role: 'team-member',\n            fullName: `${testMember.firstName} ${testMember.lastName}`\n          }\n        });\n\n        // Set the role explicitly\n        Meteor.users.update(userId, {\n          $set: { roles: ['team-member'] }\n        });\n\n        return {\n          success: true,\n          userId,\n          message: 'Test team member created successfully'\n        };\n      } catch (error) {\n        console.error('[createTestTeamMember] Error:', error);\n        throw new Meteor.Error('create-test-member-failed', error.message);\n      }\n    } else {\n      throw new Meteor.Error('not-development', 'This method is only available in development');\n    }\n  }\n});"], "mappings": ";;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAIC,MAAM;IAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACG,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,eAAe;IAACL,MAAM,CAACC,IAAI,CAAC,oBAAoB,EAAC;MAACI,eAAeA,CAACF,CAAC,EAAC;QAACE,eAAe,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,QAAQ;IAACN,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACK,QAAQA,CAACH,CAAC,EAAC;QAACG,QAAQ,GAACH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,KAAK;IAACP,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACM,KAAKA,CAACJ,CAAC,EAAC;QAACI,KAAK,GAACJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIK,KAAK;IAACR,MAAM,CAACC,IAAI,CAAC,oBAAoB,EAAC;MAACO,KAAKA,CAACL,CAAC,EAAC;QAACK,KAAK,GAACL,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIM,KAAK;IAACT,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACQ,KAAKA,CAACN,CAAC,EAAC;QAACM,KAAK,GAACN,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIO,KAAK;IAACV,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACS,KAAKA,CAACP,CAAC,EAAC;QAACO,KAAK,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,MAAM;IAACX,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACQ,MAAM,GAACR,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIS,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IASlmB,eAAeC,UAAUA,CAAAC,IAAA,EAAiB;MAAA,IAAhB;QAAEC,KAAK;QAAEC;MAAI,CAAC,GAAAF,IAAA;MACtC,MAAMT,eAAe,CAACY,WAAW,CAAC;QAAEF,KAAK;QAAEC,GAAG;QAAEE,SAAS,EAAE,IAAIC,IAAI,CAAC;MAAE,CAAC,CAAC;IAC1E;IAEA,MAAMC,WAAW,GAAG,QAAQ;IAE5BhB,MAAM,CAACiB,OAAO,CAAC,YAAY;MAAA,IAAAC,qBAAA;MACzB;MACA,IAAI;QACF,MAAMd,KAAK,CAACe,WAAW,CAAC;UAAEL,SAAS,EAAE;QAAE,CAAC,CAAC;QACzC,MAAMV,KAAK,CAACe,WAAW,CAAC;UAAEC,UAAU,EAAE;QAAE,CAAC,CAAC;QAC1C,MAAMhB,KAAK,CAACe,WAAW,CAAC;UAAEE,SAAS,EAAE;QAAE,CAAC,CAAC;;QAEzC;QACA;QACA,MAAMrB,MAAM,CAACsB,KAAK,CAACH,WAAW,CAAC;UAAEI,KAAK,EAAE;QAAE,CAAC,EAAE;UAAEC,UAAU,EAAE;QAAK,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mCAAmC,EAAEF,KAAK,CAACG,OAAO,CAAC;MAClE;;MAEA;MACA,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM7B,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;QACvDL,OAAO,CAACM,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,GAAG,CAACC,IAAI;UAAA,IAAAC,YAAA,EAAAC,aAAA;UAAA,OAAK;YACxDC,EAAE,EAAEH,IAAI,CAACI,GAAG;YACZC,KAAK,GAAAJ,YAAA,GAAED,IAAI,CAACM,MAAM,cAAAL,YAAA,wBAAAC,aAAA,GAAXD,YAAA,CAAc,CAAC,CAAC,cAAAC,aAAA,uBAAhBA,aAAA,CAAkBK,OAAO;YAChClB,KAAK,EAAEW,IAAI,CAACX,KAAK;YACjBmB,OAAO,EAAER,IAAI,CAACQ;UAChB,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA,KAAK,MAAMR,IAAI,IAAIL,QAAQ,EAAE;UAC3B,MAAMc,OAAO,GAAG,CAAC,CAAC;UAElB,IAAI,CAACT,IAAI,CAACX,KAAK,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACX,IAAI,CAACX,KAAK,CAAC,EAAE;YAC7CoB,OAAO,CAACpB,KAAK,GAAG,CAAC,aAAa,CAAC;UACjC;UAEA,IAAI,CAACW,IAAI,CAACpB,SAAS,EAAE;YACnB6B,OAAO,CAAC7B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;UAChC;UAEA,IAAI+B,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;YAAA,IAAAC,aAAA,EAAAC,cAAA;YACnCxB,OAAO,CAACM,GAAG,CAAC,2CAA2C,GAAAiB,aAAA,GAAEf,IAAI,CAACM,MAAM,cAAAS,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBT,OAAO,CAAC;YACnF,MAAMzC,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;cACvCc,IAAI,EAAET;YACR,CAAC,CAAC;UACJ;QACF;QAEA,MAAMU,gBAAgB,GAAG,MAAMrD,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC;UAAE,OAAO,EAAE;QAAc,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC;QACzF5B,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEqB,gBAAgB,CAAC;;QAE9D;QACA,IAAIA,gBAAgB,KAAK,CAAC,EAAE;UAC1B3B,OAAO,CAACM,GAAG,CAAC,sCAAsC,CAAC;UACnD,IAAI;YACF;YACA,MAAMuB,WAAW,GAAG,CAClB;cACEhB,KAAK,EAAE,mBAAmB;cAC1BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,EACD;cACEnB,KAAK,EAAE,mBAAmB;cAC1BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,CACF;YAED,KAAK,MAAMC,MAAM,IAAIJ,WAAW,EAAE;cAChC,MAAMK,MAAM,GAAG,MAAM1D,QAAQ,CAAC2D,eAAe,CAAC;gBAC5CtB,KAAK,EAAEoB,MAAM,CAACpB,KAAK;gBACnBiB,QAAQ,EAAEG,MAAM,CAACH,QAAQ;gBACzBM,IAAI,EAAE,aAAa;gBACnBhD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;gBACrB2B,OAAO,EAAE;kBACPe,SAAS,EAAEE,MAAM,CAACF,SAAS;kBAC3BC,QAAQ,EAAEC,MAAM,CAACD,QAAQ;kBACzBI,IAAI,EAAE,aAAa;kBACnBC,QAAQ,KAAAC,MAAA,CAAKL,MAAM,CAACF,SAAS,OAAAO,MAAA,CAAIL,MAAM,CAACD,QAAQ;gBAClD;cACF,CAAC,CAAC;;cAEF;cACA,MAAM1D,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACS,MAAM,EAAE;gBACrCR,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAAC,aAAa;gBAAE;cACjC,CAAC,CAAC;cAEFG,OAAO,CAACM,GAAG,CAAC,qCAAqC,EAAE;gBACjDK,EAAE,EAAEuB,MAAM;gBACVrB,KAAK,EAAEoB,MAAM,CAACpB,KAAK;gBACnB0B,IAAI,KAAAD,MAAA,CAAKL,MAAM,CAACF,SAAS,OAAAO,MAAA,CAAIL,MAAM,CAACD,QAAQ;cAC9C,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACrE;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;;MAEA;MACA,MAAMyC,aAAa,IAAAhD,qBAAA,GAAGlB,MAAM,CAACmE,QAAQ,CAACC,OAAO,cAAAlD,qBAAA,uBAAvBA,qBAAA,CAAyBqB,KAAK;;MAEpD;MACA,IAAI2B,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,IAAIH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEV,QAAQ,EAAE;QACtDc,OAAO,CAACC,GAAG,CAACC,QAAQ,aAAAR,MAAA,CAAaS,kBAAkB,CAACP,aAAa,CAACG,QAAQ,CAAC,OAAAL,MAAA,CAAIS,kBAAkB,CAACP,aAAa,CAACV,QAAQ,CAAC,OAAAQ,MAAA,CAAIE,aAAa,CAACQ,MAAM,OAAAV,MAAA,CAAIE,aAAa,CAACS,IAAI,CAAE;;QAEzK;QACA,IAAI;UACFjD,OAAO,CAACM,GAAG,CAAC,gCAAgC,CAAC;UAC7C7B,KAAK,CAACyE,IAAI,CAAC;YACTC,EAAE,EAAEX,aAAa,CAACG,QAAQ;YAC1BS,IAAI,EAAEZ,aAAa,CAACG,QAAQ;YAC5BU,OAAO,EAAE,YAAY;YACrBC,IAAI,EAAE;UACR,CAAC,CAAC;UACFtD,OAAO,CAACM,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,CAAC,OAAOP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAC;MACjE;;MAEA;MACAzB,QAAQ,CAAC+E,MAAM,CAAC;QACdC,qBAAqB,EAAE,KAAK;QAAG;QAC/BC,2BAA2B,EAAE;MAC/B,CAAC,CAAC;;MAEF;MACAjF,QAAQ,CAACkF,oBAAoB,CAAEC,OAAO,IAAK;QAAA,IAAAC,cAAA,EAAAC,aAAA;QACzC7D,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAE;UACnDwD,IAAI,EAAEH,OAAO,CAACG,IAAI;UAClBC,OAAO,EAAEJ,OAAO,CAACI,OAAO;UACxBhE,KAAK,GAAA6D,cAAA,GAAED,OAAO,CAAC5D,KAAK,cAAA6D,cAAA,uBAAbA,cAAA,CAAe1D,OAAO;UAC7BgC,MAAM,GAAA2B,aAAA,GAAEF,OAAO,CAACnD,IAAI,cAAAqD,aAAA,uBAAZA,aAAA,CAAcjD,GAAG;UACzBoD,UAAU,EAAEL,OAAO,CAACK;QACtB,CAAC,CAAC;;QAEF;QACA,IAAIL,OAAO,CAACnD,IAAI,IAAImD,OAAO,CAACG,IAAI,KAAK,UAAU,EAAE;UAC/C9D,OAAO,CAACM,GAAG,CAAC,0DAA0D,EAAEqD,OAAO,CAACnD,IAAI,CAACI,GAAG,CAAC;UACzF,OAAO,IAAI;QACb;;QAEA;QACA,OAAO,IAAI;MACb,CAAC,CAAC;;MAEF;MACApC,QAAQ,CAACyF,cAAc,CAACC,QAAQ,GAAG,wBAAwB;MAC3D1F,QAAQ,CAACyF,cAAc,CAACb,IAAI,GAAGZ,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,8BAAAL,MAAA,CACzBE,aAAa,CAACG,QAAQ,SACjD,8CAA8C;MAEhDnE,QAAQ,CAACyF,cAAc,CAACE,WAAW,GAAG;QACpCd,OAAOA,CAAA,EAAG;UACR,OAAO,2BAA2B;QACpC,CAAC;QACDC,IAAIA,CAAC9C,IAAI,EAAEtB,GAAG,EAAE;UACd,MAAMkF,YAAY,GAAG5D,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO;UAC3C,OAAO,gDAAAuB,MAAA,CAC8B8B,YAAY,wCAAqC,MAAA9B,MAAA,CAC/EpD,GAAG,SAAM,4EAC6D,cAC9D,qCACuB;QACxC,CAAC;QACDmF,IAAIA,CAAC7D,IAAI,EAAEtB,GAAG,EAAE;UACd,MAAMkF,YAAY,GAAG5D,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO;UAC3C,sQAAAuB,MAAA,CAKyC8B,YAAY,gHAAA9B,MAAA,CAElCpD,GAAG;QAexB;MACF,CAAC;;MAED;MACA,IAAI,OAAMX,eAAe,CAAC6B,IAAI,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC,MAAK,CAAC,EAAE;QACnD,MAAM7C,UAAU,CAAC;UACfE,KAAK,EAAE,iBAAiB;UACxBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,kBAAkB;UACzBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,eAAe;UACtBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,aAAa;UACpBC,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;;MAEA;MACA;MACAZ,MAAM,CAACgG,OAAO,CAAC,OAAO,EAAE,YAAY;QAClC,OAAO/F,eAAe,CAAC6B,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACA5B,QAAQ,CAAC+F,YAAY,CAAC,CAACC,OAAO,EAAEhE,IAAI,KAAK;QAAA,IAAAiE,qBAAA,EAAAC,sBAAA;QACvC1E,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAE;UACxDO,KAAK,EAAE2D,OAAO,CAAC3D,KAAK;UACpBuB,IAAI,EAAEoC,OAAO,CAACpC,IAAI;UAClBpB,OAAO,EAAEwD,OAAO,CAACxD,OAAO;UACxB5B,SAAS,EAAEoF,OAAO,CAACpF;QACrB,CAAC,CAAC;QAEF,MAAMuF,cAAc,GAAA1G,aAAA,KAAQuC,IAAI,CAAE;;QAElC;QACAmE,cAAc,CAAC3D,OAAO,GAAGwD,OAAO,CAACxD,OAAO,IAAI,CAAC,CAAC;;QAE9C;QACA,MAAMoB,IAAI,GAAGoC,OAAO,CAACpC,IAAI,IAAI,aAAa;QAC1CuC,cAAc,CAAC9E,KAAK,GAAG,CAACuC,IAAI,CAAC;;QAE7B;QACAuC,cAAc,CAACvF,SAAS,GAAGoF,OAAO,CAACpF,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC;QAE1DW,OAAO,CAACM,GAAG,CAAC,8BAA8B,EAAE;UAC1CK,EAAE,EAAEgE,cAAc,CAAC/D,GAAG;UACtBC,KAAK,GAAA4D,qBAAA,GAAEE,cAAc,CAAC7D,MAAM,cAAA2D,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4B3D,OAAO;UAC1ClB,KAAK,EAAE8E,cAAc,CAAC9E,KAAK;UAC3BmB,OAAO,EAAE2D,cAAc,CAAC3D,OAAO;UAC/B5B,SAAS,EAAEuF,cAAc,CAACvF;QAC5B,CAAC,CAAC;QAEF,OAAOuF,cAAc;MACvB,CAAC,CAAC;;MAEF;MACArG,MAAM,CAACgG,OAAO,CAAC,aAAa,EAAE,YAAW;QACvCtE,OAAO,CAACM,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC4B,MAAM,CAAC;QAErE,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;UAChBlC,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC;UACvD,OAAO,IAAI,CAACsE,KAAK,CAAC,CAAC;QACrB;QAEA,IAAI;UACF;UACA,MAAMC,WAAW,GAAGvG,MAAM,CAACsB,KAAK,CAACQ,IAAI,CACnC;YACE0E,GAAG,EAAE,CACH;cAAE,OAAO,EAAE;YAAc,CAAC,EAC1B;cAAE,cAAc,EAAE;YAAc,CAAC;UAErC,CAAC,EACD;YACEC,MAAM,EAAE;cACNjE,MAAM,EAAE,CAAC;cACTjB,KAAK,EAAE,CAAC;cACR,mBAAmB,EAAE,CAAC;cACtB,kBAAkB,EAAE,CAAC;cACrB,kBAAkB,EAAE,CAAC;cACrBT,SAAS,EAAE;YACb;UACF,CACF,CAAC;UAEDY,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC;UACpD,OAAOuE,WAAW;QACpB,CAAC,CAAC,OAAO9E,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,IAAI,CAAC6E,KAAK,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;;MAEF;MACAtG,MAAM,CAACgG,OAAO,CAAC,UAAU,EAAE,YAAW;QACpC,IAAI,CAAC,IAAI,CAACpC,MAAM,EAAE;UAChB,OAAO,IAAI,CAAC0C,KAAK,CAAC,CAAC;QACrB;QAEA5E,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC4B,MAAM,CAAC;QAEhE,OAAO5D,MAAM,CAACsB,KAAK,CAACQ,IAAI,CACtB;UAAEQ,GAAG,EAAE,IAAI,CAACsB;QAAO,CAAC,EACpB;UACE6C,MAAM,EAAE;YACNlF,KAAK,EAAE,CAAC;YACRiB,MAAM,EAAE,CAAC;YACTE,OAAO,EAAE;UACX;QACF,CACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA1C,MAAM,CAAC0G,OAAO,CAAC;MACb,cAAcC,CAAAC,KAAA,EAA6D;QAAA,IAA5D;UAAErE,KAAK;UAAEiB,QAAQ;UAAEM,IAAI;UAAE+C,UAAU;UAAEpD,SAAS;UAAEC;QAAS,CAAC,GAAAkD,KAAA;QACvE;QACA,IAAI9C,IAAI,KAAK,OAAO,IAAI+C,UAAU,KAAK7F,WAAW,EAAE;UAClD,MAAM,IAAIhB,MAAM,CAAC8G,KAAK,CAAC,qBAAqB,EAAE,8BAA8B,CAAC;QAC/E;;QAEA;QACA,MAAMC,aAAa,GAAG;UACpB/D,MAAM,EAAE,OAAO;UACfgE,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAAC/D,MAAM,CAACoE,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UACxC2D,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UAC3C2D,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UACxC2D,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UACzC2D,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAACnE,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAIhD,MAAM,CAAC8G,KAAK,CAAC,kBAAkB,EAAEK,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF,MAAM1D,MAAM,GAAG1D,QAAQ,CAACqH,UAAU,CAAC;YACjChF,KAAK;YACLiB,QAAQ;YACRM,IAAI;YAAE;YACNpB,OAAO,EAAE;cACPoB,IAAI;cAAE;cACNL,SAAS;cACTC,QAAQ;cACRK,QAAQ,KAAAC,MAAA,CAAKP,SAAS,OAAAO,MAAA,CAAIN,QAAQ;YACpC;UACF,CAAC,CAAC;;UAEF;UACA,IAAIE,MAAM,EAAE;YACV1D,QAAQ,CAACgF,qBAAqB,CAACtB,MAAM,CAAC;UACxC;UAEA,OAAOA,MAAM;QACf,CAAC,CAAC,OAAOnC,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,oBAAoB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC7D;MACF,CAAC;MAED,MAAM,eAAe4F,CAAA,EAAG;QACtB,IAAI,CAAC,IAAI,CAAC5D,MAAM,EAAE;UAChB,MAAM,IAAI5D,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,wBAAwB,CAAC;QACpE;QAEA,IAAI;UAAA,IAAAW,WAAA,EAAAC,aAAA;UACF,MAAMxF,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC,IAAI,CAAC/D,MAAM,CAAC;UACzD,IAAI,CAAC1B,IAAI,EAAE;YACT,MAAM,IAAIlC,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;UAC5D;;UAEA;UACA,MAAMhD,IAAI,GAAG,EAAA2D,WAAA,GAAAvF,IAAI,CAACX,KAAK,cAAAkG,WAAA,uBAAVA,WAAA,CAAa,CAAC,CAAC,OAAAC,aAAA,GAAIxF,IAAI,CAACQ,OAAO,cAAAgF,aAAA,uBAAZA,aAAA,CAAc5D,IAAI,KAAI,aAAa;UACnE,OAAOA,IAAI;QACb,CAAC,CAAC,OAAOrC,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,iBAAiB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,+BAA+BgG,CAACrF,KAAK,EAAE;QACrC;QACA,MAAML,IAAI,GAAGhC,QAAQ,CAAC2H,eAAe,CAACtF,KAAK,CAAC;QAC5C,IAAI,CAACL,IAAI,EAAE;UACT,MAAM,IAAIlC,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;QACnF;;QAEA;QACA,MAAMgB,SAAS,GAAG5F,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;QAChC,IAAIsF,SAAS,CAACC,QAAQ,EAAE;UACtB,MAAM,IAAI/H,MAAM,CAAC8G,KAAK,CAAC,kBAAkB,EAAE,gCAAgC,CAAC;QAC9E;;QAEA;QACA,IAAI;UACF5G,QAAQ,CAACgF,qBAAqB,CAAChD,IAAI,CAACI,GAAG,EAAEC,KAAK,CAAC;UAC/C,OAAO,IAAI;QACb,CAAC,CAAC,OAAOd,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,2BAA2B,EAAErF,KAAK,CAACG,OAAO,CAAC;QACpE;MACF,CAAC;MAED,MAAM,sBAAsBoG,CAACC,IAAI,EAAE;QACjCvG,OAAO,CAACM,GAAG,CAAC,2CAA2C,EAAEkG,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC;QAE9E3H,KAAK,CAAC2H,IAAI,EAAE;UACV1F,KAAK,EAAE6F,MAAM;UACbC,WAAW,EAAED;QACf,CAAC,CAAC;QAEF,MAAM;UAAE7F,KAAK;UAAE8F;QAAY,CAAC,GAAGJ,IAAI;QACnCvG,OAAO,CAACM,GAAG,CAAC,gDAAgD,EAAEO,KAAK,CAAC;;QAEpE;QACAb,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;QACrD,IAAIsG,UAAU,GAAG,MAAMtI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;UAC/C,gBAAgB,EAAEpF;QACpB,CAAC,CAAC;QAEF,IAAI,CAAC+F,UAAU,EAAE;UACf5G,OAAO,CAACM,GAAG,CAAC,gFAAgF,CAAC;UAC7FsG,UAAU,GAAG,MAAMtI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAE;cAAEY,MAAM,EAAE,IAAIC,MAAM,KAAAxE,MAAA,CAAKzB,KAAK,QAAK,GAAG;YAAE;UAC5D,CAAC,CAAC;UAEF,IAAI,CAAC+F,UAAU,EAAE;YACf,MAAM,IAAItI,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;UACnF;UAEApF,OAAO,CAACM,GAAG,CAAC,0DAA0D,CAAC;QACzE,CAAC,MAAM;UACLN,OAAO,CAACM,GAAG,CAAC,gDAAgD,CAAC;QAC/D;;QAEA;QACA,IAAI,CAACsG,UAAU,IAAI,CAACA,UAAU,CAAChG,GAAG,EAAE;UAClC,MAAM,IAAItC,MAAM,CAAC8G,KAAK,CAAC,cAAc,EAAE,2BAA2B,CAAC;QACrE;QAEApF,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEsG,UAAU,CAAChG,GAAG,CAAC;QAC9DZ,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAE,OAAOsG,UAAU,CAAChG,GAAG,CAAC;;QAE1E;QACA,MAAMyE,aAAa,GAAG;UACpB/D,MAAM,EAAE,OAAO;UACfgE,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAAC/D,MAAM,CAACoE,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC3ClB,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC9ClB,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC3ClB,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC5ClB,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAACnE,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAIhD,MAAM,CAAC8G,KAAK,CAAC,kBAAkB,EAAEK,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF5F,OAAO,CAACM,GAAG,CAAC,8CAA8C,CAAC;;UAE3D;UACAN,OAAO,CAACM,GAAG,CAAC,oDAAoD,CAAC;UACjE,MAAMyG,WAAW,GAAG,MAAMzI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAACW,UAAU,CAAChG,GAAG,CAAC;UACnEZ,OAAO,CAACM,GAAG,CAAC,yCAAyC,EAAEkG,IAAI,CAACC,SAAS,CAACM,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAE5F,IAAI,CAACA,WAAW,EAAE;YAChB,MAAM,IAAIzI,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;UACnF;;UAEA;UACA,MAAMvG,MAAM,GAAGmI,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMC,UAAU,GAAG,EAAE;UACrB,MAAMC,cAAc,GAAGrI,MAAM,CAACsI,QAAQ,CAACR,WAAW,EAAEM,UAAU,CAAC;UAC/DjH,OAAO,CAACM,GAAG,CAAC,iDAAiD,EAAE4G,cAAc,CAAC5F,MAAM,CAAC;UACrFtB,OAAO,CAACM,GAAG,CAAC,gCAAgC,EAAE4G,cAAc,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;;UAEtF;UACA,IAAIC,YAAY,GAAG,CAAC;UACpB,IAAIC,aAAa,GAAG,IAAI;;UAExB;UACAtH,OAAO,CAACM,GAAG,CAAC,iEAAiE,CAAC;UAC9E,IAAI;YACF+G,YAAY,GAAG,MAAM/I,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACmF,UAAU,CAAChG,GAAG,EAAE;cAC5Dc,IAAI,EAAE;gBACJ,0BAA0B,EAAEwF;cAC9B;YACF,CAAC,CAAC;YACFlH,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAE+G,YAAY,CAAC;YAC9D,IAAIA,YAAY,KAAK,CAAC,EAAE;cACtBC,aAAa,GAAG,oCAAoC;YACtD;UACF,CAAC,CAAC,OAAOC,YAAY,EAAE;YACrBvH,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEwH,YAAY,CAAC;UACjE;;UAEA;UACA,IAAIF,YAAY,KAAK,CAAC,EAAE;YACtBrH,OAAO,CAACM,GAAG,CAAC,wEAAwE,CAAC;YACrF,IAAI;cACF+G,YAAY,GAAG,MAAM/I,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACmF,UAAU,CAAChG,GAAG,EAAE;gBAC5Dc,IAAI,EAAE;kBACJ,mBAAmB,EAAE;oBACnB7C,MAAM,EAAEqI;kBACV;gBACF;cACF,CAAC,CAAC;cACFlH,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAE+G,YAAY,CAAC;cAC9D,IAAIA,YAAY,KAAK,CAAC,EAAE;gBACtBC,aAAa,GAAG,2CAA2C;cAC7D;YACF,CAAC,CAAC,OAAOE,YAAY,EAAE;cACrBxH,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEyH,YAAY,CAAC;YACjE;UACF;;UAEA;UACA,IAAIH,YAAY,KAAK,CAAC,EAAE;YACtBrH,OAAO,CAACM,GAAG,CAAC,+DAA+D,CAAC;YAC5E,IAAI;cAAA,IAAAmH,qBAAA,EAAAC,sBAAA;cACFL,YAAY,GAAG,MAAM/I,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACmF,UAAU,CAAChG,GAAG,EAAE;gBAC5Dc,IAAI,EAAE;kBACJiG,QAAQ,EAAE;oBACR7F,QAAQ,EAAE;sBACRjD,MAAM,EAAEqI;oBACV,CAAC;oBACDU,MAAM,EAAE,EAAAH,qBAAA,GAAAV,WAAW,CAACY,QAAQ,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsBG,MAAM,KAAI;sBAAEC,WAAW,EAAE;oBAAG,CAAC;oBAC3DhH,KAAK,EAAE,EAAA6G,sBAAA,GAAAX,WAAW,CAACY,QAAQ,cAAAD,sBAAA,uBAApBA,sBAAA,CAAsB7G,KAAK,KAAI,CAAC;kBACzC;gBACF;cACF,CAAC,CAAC;cACFb,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAE+G,YAAY,CAAC;cAC9D,IAAIA,YAAY,KAAK,CAAC,EAAE;gBACtBC,aAAa,GAAG,kCAAkC;cACpD;YACF,CAAC,CAAC,OAAOQ,YAAY,EAAE;cACrB9H,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAE+H,YAAY,CAAC;YACjE;UACF;;UAEA;UACA,IAAIT,YAAY,KAAK,CAAC,EAAE;YACtBrH,OAAO,CAACM,GAAG,CAAC,+DAA+D,CAAC;YAC5E,IAAI;cACF,MAAMyH,UAAU,GAAG,MAAMzJ,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACmF,UAAU,CAAChG,GAAG,EAAE;gBAChEc,IAAI,EAAE;kBACJ,2BAA2B,EAAE,IAAIrC,IAAI,CAAC;gBACxC;cACF,CAAC,CAAC;cACFW,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAEyH,UAAU,CAAC;cAErE,IAAIA,UAAU,KAAK,CAAC,EAAE;gBACpB/H,OAAO,CAACM,GAAG,CAAC,2EAA2E,CAAC;gBACxF;gBACA,MAAMhC,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACmF,UAAU,CAAChG,GAAG,EAAE;kBAC7CoH,MAAM,EAAE;oBAAE,mBAAmB,EAAE;kBAAG;gBACpC,CAAC,CAAC;gBAEFX,YAAY,GAAG,MAAM/I,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACmF,UAAU,CAAChG,GAAG,EAAE;kBAC5Dc,IAAI,EAAE;oBACJ,mBAAmB,EAAE;sBACnB7C,MAAM,EAAEqI;oBACV;kBACF;gBACF,CAAC,CAAC;gBACFlH,OAAO,CAACM,GAAG,CAAC,2CAA2C,EAAE+G,YAAY,CAAC;gBACtE,IAAIA,YAAY,KAAK,CAAC,EAAE;kBACtBC,aAAa,GAAG,0BAA0B;gBAC5C;cACF;YACF,CAAC,CAAC,OAAOW,YAAY,EAAE;cACrBjI,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEkI,YAAY,CAAC;YACjE;UACF;UAEA,IAAIZ,YAAY,KAAK,CAAC,EAAE;YAAA,IAAAa,qBAAA,EAAAC,sBAAA;YACtBnI,OAAO,CAACM,GAAG,uDAAAgC,MAAA,CAAuDgF,aAAa,CAAE,CAAC;;YAElF;YACA,MAAMc,WAAW,GAAG,MAAM9J,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAACW,UAAU,CAAChG,GAAG,CAAC;YACnEZ,OAAO,CAACM,GAAG,CAAC,yCAAyC,EAAEkG,IAAI,CAACC,SAAS,CAAC2B,WAAW,CAACT,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;YAErG;YACA,KAAAO,qBAAA,GAAIE,WAAW,CAACT,QAAQ,cAAAO,qBAAA,gBAAAC,sBAAA,GAApBD,qBAAA,CAAsBpG,QAAQ,cAAAqG,sBAAA,eAA9BA,sBAAA,CAAgCtJ,MAAM,EAAE;cAC1C,MAAMwJ,gBAAgB,GAAGxJ,MAAM,CAACyJ,WAAW,CAAC3B,WAAW,EAAEyB,WAAW,CAACT,QAAQ,CAAC7F,QAAQ,CAACjD,MAAM,CAAC;cAC9FmB,OAAO,CAACM,GAAG,CAAC,8CAA8C,EAAE+H,gBAAgB,GAAG,MAAM,GAAG,MAAM,CAAC;YACjG;YAEA,OAAO;cAAEE,OAAO,EAAE,IAAI;cAAErI,OAAO,EAAE;YAAgC,CAAC;UACpE,CAAC,MAAM;YACLF,OAAO,CAACD,KAAK,CAAC,oEAAoE,EAAEsH,YAAY,CAAC;;YAEjG;YACArH,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAEsG,UAAU,CAAChG,GAAG,CAAC;YACxDZ,OAAO,CAACM,GAAG,CAAC,gCAAgC,EAAE,OAAOsG,UAAU,CAAChG,GAAG,CAAC;YACpEZ,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAE,CAAC,CAACyG,WAAW,CAAC;YACnE/G,OAAO,CAACM,GAAG,CAAC,8BAA8B,EAAEyG,WAAW,CAAClH,KAAK,CAAC;YAE9D,MAAM,IAAIvB,MAAM,CAAC8G,KAAK,CAAC,wBAAwB,EAAE,uCAAuC,CAAC;UAC3F;QAEF,CAAC,CAAC,OAAOrF,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;UACtE,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,wBAAwB,gCAAA9C,MAAA,CAAgCvC,KAAK,CAACG,OAAO,CAAE,CAAC;QACjG;MACF,CAAC;MAED,MAAM,iBAAiBsI,CAAAC,KAAA,EAAY;QAAA,IAAX;UAAE5H;QAAM,CAAC,GAAA4H,KAAA;QAC/B,IAAI;UAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA;UACFhK,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;UAEpB1G,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEO,KAAK,CAAC;;UAEjD;UACA,MAAML,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAEpF;UACpB,CAAC,CAAC;UAEF,IAAI,CAACL,IAAI,EAAE;YACTR,OAAO,CAACM,GAAG,CAAC,4BAA4B,CAAC;YACzC,OAAO;cAAEiI,OAAO,EAAE,KAAK;cAAExI,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAC,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEE,IAAI,CAACI,GAAG,CAAC;;UAEhD;UACA,MAAMiI,QAAQ,GAAG,MAAMvK,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAACzF,IAAI,CAACI,GAAG,CAAC;UAC1DZ,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEkG,IAAI,CAACC,SAAS,CAACoC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEjF,IAAI,CAACA,QAAQ,EAAE;YACb7I,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC;YACvD,OAAO;cAAEiI,OAAO,EAAE,KAAK;cAAExI,KAAK,EAAE;YAA+B,CAAC;UAClE;;UAEA;UACA,IAAI+I,gBAAgB,GAAG,IAAI;UAC3B,IAAI;YACFA,gBAAgB,GAAG,MAAMxK,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;cAC1Dc,IAAI,EAAE;gBAAE,mBAAmB,EAAE,IAAIrC,IAAI,CAAC;cAAE;YAC1C,CAAC,CAAC;YACFW,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEwI,gBAAgB,CAAC;UAClE,CAAC,CAAC,OAAOC,WAAW,EAAE;YACpB/I,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEgJ,WAAW,CAAC;UAC9D;;UAEA;UACA,IAAIC,cAAc,GAAG,KAAK;UAC1B,IAAI;YACFA,cAAc,GAAG,OAAOxK,QAAQ,CAACyK,WAAW,KAAK,UAAU;YAC3DjJ,OAAO,CAACM,GAAG,CAAC,6CAA6C,EAAE0I,cAAc,CAAC;UAC5E,CAAC,CAAC,OAAOE,gBAAgB,EAAE;YACzBlJ,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEmJ,gBAAgB,CAAC;UAClF;UAEA,MAAMC,MAAM,GAAG;YACbZ,OAAO,EAAE,IAAI;YACbrG,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChBwI,UAAU,EAAE,OAAO5I,IAAI,CAACI,GAAG;YAC3ByI,WAAW,EAAE,CAAC,CAACR,QAAQ,CAAClB,QAAQ;YAChC2B,WAAW,EAAE,CAAC,GAAAZ,kBAAA,GAAEG,QAAQ,CAAClB,QAAQ,cAAAe,kBAAA,eAAjBA,kBAAA,CAAmB5G,QAAQ,CAAC;YAC5CyH,SAAS,EAAE,CAAC,GAAAZ,mBAAA,GAAEE,QAAQ,CAAClB,QAAQ,cAAAgB,mBAAA,gBAAAC,qBAAA,GAAjBD,mBAAA,CAAmB7G,QAAQ,cAAA8G,qBAAA,eAA3BA,qBAAA,CAA6B/J,MAAM,CAAC;YAClDgB,KAAK,EAAEgJ,QAAQ,CAAChJ,KAAK,IAAI,EAAE;YAC3BmB,OAAO,EAAE6H,QAAQ,CAAC7H,OAAO,IAAI,CAAC,CAAC;YAC/B8H,gBAAgB,EAAEA,gBAAgB;YAClCE,cAAc,EAAEA,cAAc;YAC9BQ,iBAAiB,EAAEX,QAAQ,CAAClB,QAAQ,IAAI,CAAC;UAC3C,CAAC;UAED3H,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAEkG,IAAI,CAACC,SAAS,CAAC0C,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UACzE,OAAOA,MAAM;QAEf,CAAC,CAAC,OAAOpJ,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,OAAO;YAAEwI,OAAO,EAAE,KAAK;YAAExI,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,iBAAiBuJ,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAE7I,KAAK;UAAEiB;QAAS,CAAC,GAAA4H,KAAA;QACzC9K,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QACpB9H,KAAK,CAACkD,QAAQ,EAAE4E,MAAM,CAAC;QAEvB1G,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAEO,KAAK,CAAC;;QAE1D;QACA,MAAML,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;UAC3C,gBAAgB,EAAEpF;QACpB,CAAC,CAAC;QAEF,IAAI,CAACL,IAAI,EAAE;UACTR,OAAO,CAACM,GAAG,CAAC,4BAA4B,CAAC;UACzC,OAAO;YAAEiI,OAAO,EAAE,KAAK;YAAExI,KAAK,EAAE;UAAiB,CAAC;QACpD;QAEAC,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEE,IAAI,CAACI,GAAG,CAAC;QAChDZ,OAAO,CAACM,GAAG,CAAC,4BAA4B,EAAEkG,IAAI,CAACC,SAAS,CAACjG,IAAI,CAACmH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;QAEjF;QACA,IAAI;UACF;UACA,IAAI,CAACnH,IAAI,CAACmH,QAAQ,IAAI,CAACnH,IAAI,CAACmH,QAAQ,CAAC7F,QAAQ,IAAI,CAACtB,IAAI,CAACmH,QAAQ,CAAC7F,QAAQ,CAACjD,MAAM,EAAE;YAC/EmB,OAAO,CAACM,GAAG,CAAC,qDAAqD,CAAC;YAClE,OAAO;cACLiI,OAAO,EAAE,KAAK;cACdxI,KAAK,EAAE,wBAAwB;cAC/BmC,MAAM,EAAE1B,IAAI,CAACI,GAAG;cAChB+G,QAAQ,EAAEnH,IAAI,CAACmH;YACjB,CAAC;UACH;UAEA,MAAM9I,MAAM,GAAGmI,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAM2C,UAAU,GAAGnJ,IAAI,CAACmH,QAAQ,CAAC7F,QAAQ,CAACjD,MAAM;UAChD,MAAM+K,aAAa,GAAG/K,MAAM,CAACyJ,WAAW,CAACxG,QAAQ,EAAE6H,UAAU,CAAC;UAE9D3J,OAAO,CAACM,GAAG,CAAC,oCAAoC,EAAEsJ,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;UAClF5J,OAAO,CAACM,GAAG,CAAC,0BAA0B,EAAEqJ,UAAU,CAACvC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;UAC5EpH,OAAO,CAACM,GAAG,CAAC,8BAA8B,EAAEwB,QAAQ,CAACR,MAAM,CAAC;UAE5D,OAAO;YACLiH,OAAO,EAAEqB,aAAa;YACtB1H,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChBiJ,WAAW,EAAEF,UAAU,CAACvC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAChD0C,cAAc,EAAEhI,QAAQ,CAACR;UAC3B,CAAC;QACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D,OAAO;YAAEwI,OAAO,EAAE,KAAK;YAAExI,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,8BAA8B6J,CAAAC,KAAA,EAAY;QAAA,IAAAC,cAAA,EAAAC,qBAAA;QAAA,IAAX;UAAErJ;QAAM,CAAC,GAAAmJ,KAAA;QAC5CpL,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QAEpB1G,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAEO,KAAK,CAAC;;QAE5E;QACA,MAAML,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;UAC3C,gBAAgB,EAAEpF;QACpB,CAAC,CAAC;QAEF,IAAI,CAACL,IAAI,EAAE;UACT,OAAO;YAAE+H,OAAO,EAAE,KAAK;YAAExI,KAAK,EAAE;UAAiB,CAAC;QACpD;QAEAC,OAAO,CAACM,GAAG,CAAC,mDAAmD,EAAEkG,IAAI,CAACC,SAAS,CAACjG,IAAI,CAACmH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;QAExG;QACA,IAAI,GAAAsC,cAAA,GAACzJ,IAAI,CAACmH,QAAQ,cAAAsC,cAAA,gBAAAC,qBAAA,GAAbD,cAAA,CAAenI,QAAQ,cAAAoI,qBAAA,eAAvBA,qBAAA,CAAyBrL,MAAM,GAAE;UACpC,OAAO;YAAE0J,OAAO,EAAE,KAAK;YAAExI,KAAK,EAAE;UAAoB,CAAC;QACvD;QAEA,MAAM4J,UAAU,GAAGnJ,IAAI,CAACmH,QAAQ,CAAC7F,QAAQ,CAACjD,MAAM;QAChDmB,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEqJ,UAAU,CAAC;QAChE3J,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEqJ,UAAU,CAACrI,MAAM,CAAC;QACvEtB,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAEqJ,UAAU,CAACvC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEtF;QACA,MAAM+C,QAAQ,GAAG,aAAa,CAACzE,IAAI,CAACiE,UAAU,CAAC;QAC/C3J,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAE6J,QAAQ,CAAC;QAEnE,OAAO;UACL5B,OAAO,EAAE,IAAI;UACbrG,MAAM,EAAE1B,IAAI,CAACI,GAAG;UAChBwJ,UAAU,EAAET,UAAU,CAACrI,MAAM;UAC7BuI,WAAW,EAAEF,UAAU,CAACvC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;UAChDiD,cAAc,EAAEF,QAAQ;UACxBG,YAAY,EAAE9J,IAAI,CAACmH;QACrB,CAAC;MACH,CAAC;MAED,MAAM,uBAAuB4C,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAE3J,KAAK;UAAEiB;QAAS,CAAC,GAAA0I,KAAA;QAC/C5L,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QACpB9H,KAAK,CAACkD,QAAQ,EAAE4E,MAAM,CAAC;QAEvB1G,OAAO,CAACM,GAAG,CAAC,6CAA6C,EAAEO,KAAK,CAAC;QAEjE,IAAI;UAAA,IAAA4J,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA;UACF;UACA,MAAMpK,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAEpF;UACpB,CAAC,CAAC;UAEF,IAAI,CAACL,IAAI,EAAE;YACTR,OAAO,CAACM,GAAG,CAAC,kCAAkC,CAAC;YAC/C,OAAO;cAAEiI,OAAO,EAAE,KAAK;cAAExI,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAC,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEE,IAAI,CAACI,GAAG,CAAC;UACtDZ,OAAO,CAACM,GAAG,CAAC,kCAAkC,EAAEkG,IAAI,CAACC,SAAS,CAACjG,IAAI,CAACmH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEvF;UACA,IAAI,GAAA8C,eAAA,GAACjK,IAAI,CAACmH,QAAQ,cAAA8C,eAAA,gBAAAC,qBAAA,GAAbD,eAAA,CAAe3I,QAAQ,cAAA4I,qBAAA,eAAvBA,qBAAA,CAAyB7L,MAAM,GAAE;YACpCmB,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC;YACvD,OAAO;cAAEiI,OAAO,EAAE,KAAK;cAAExI,KAAK,EAAE;YAAyB,CAAC;UAC5D;;UAEA;UACA,MAAMlB,MAAM,GAAGmI,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAM2C,UAAU,GAAGnJ,IAAI,CAACmH,QAAQ,CAAC7F,QAAQ,CAACjD,MAAM;UAChD,MAAM+K,aAAa,GAAG/K,MAAM,CAACyJ,WAAW,CAACxG,QAAQ,EAAE6H,UAAU,CAAC;UAE9D3J,OAAO,CAACM,GAAG,CAAC,0CAA0C,EAAEsJ,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;UACxF5J,OAAO,CAACM,GAAG,CAAC,gCAAgC,EAAEqJ,UAAU,CAACvC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;;UAElF;UACA,MAAMyD,UAAU,GAAG,EAAAF,aAAA,GAAAnK,IAAI,CAACM,MAAM,cAAA6J,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBvE,QAAQ,KAAI,KAAK;UACtDrG,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAEuK,UAAU,CAAC;;UAE5D;UACA7K,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEE,IAAI,CAACX,KAAK,CAAC;;UAExD;UACA,IAAIiL,cAAc,GAAG,IAAI;UACzB,IAAI;YACF;YACA,MAAMC,YAAY,GAAGvM,QAAQ,CAACwM,0BAA0B,CAAC,CAAC;YAC1DhL,OAAO,CAACM,GAAG,CAAC,0CAA0C,EAAE,CAAC,CAACyK,YAAY,CAAC;YACvED,cAAc,GAAG,6BAA6B;UAChD,CAAC,CAAC,OAAOG,UAAU,EAAE;YACnBjL,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEkL,UAAU,CAAC;YACtEH,cAAc,GAAGG,UAAU,CAAC/K,OAAO;UACrC;UAEA,OAAO;YACLqI,OAAO,EAAEqB,aAAa;YACtB1H,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChBsK,oBAAoB,EAAEtB,aAAa;YACnCuB,aAAa,EAAEN,UAAU;YACzBO,SAAS,EAAE5K,IAAI,CAACX,KAAK;YACrBgK,WAAW,EAAEF,UAAU,CAACvC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAChD0D,cAAc,EAAEA,cAAc;YAC9BO,iBAAiB,EAAE;cACjBzK,GAAG,EAAEJ,IAAI,CAACI,GAAG;cACbE,MAAM,EAAEN,IAAI,CAACM,MAAM;cACnB6G,QAAQ,EAAEnH,IAAI,CAACmH,QAAQ;cACvB9H,KAAK,EAAEW,IAAI,CAACX,KAAK;cACjBmB,OAAO,EAAER,IAAI,CAACQ;YAChB;UACF,CAAC;QAEH,CAAC,CAAC,OAAOjB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,OAAO;YAAEwI,OAAO,EAAE,KAAK;YAAExI,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,qBAAqBoL,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAE1K,KAAK;UAAEiB;QAAS,CAAC,GAAAyJ,KAAA;QAC7C3M,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QACpB9H,KAAK,CAACkD,QAAQ,EAAE4E,MAAM,CAAC;QAEvB1G,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEO,KAAK,CAAC;QAE3D,IAAI;UAAA,IAAA2K,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA;UACF;UACA,MAAMnL,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAEpF;UACpB,CAAC,CAAC;UAEF,IAAI,CAACL,IAAI,EAAE;YACT,OAAO;cAAE+H,OAAO,EAAE,KAAK;cAAExI,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAC,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEE,IAAI,CAACI,GAAG,CAAC;;UAEpD;UACA,MAAM/B,MAAM,GAAGmI,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAM2C,UAAU,IAAA6B,eAAA,GAAGhL,IAAI,CAACmH,QAAQ,cAAA6D,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAe1J,QAAQ,cAAA2J,qBAAA,uBAAvBA,qBAAA,CAAyB5M,MAAM;UAElD,IAAI,CAAC8K,UAAU,EAAE;YACf,OAAO;cAAEpB,OAAO,EAAE,KAAK;cAAExI,KAAK,EAAE;YAAyB,CAAC;UAC5D;UAEA,MAAM6J,aAAa,GAAG/K,MAAM,CAACyJ,WAAW,CAACxG,QAAQ,EAAE6H,UAAU,CAAC;UAC9D3J,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEsJ,aAAa,CAAC;UAE7D,IAAI,CAACA,aAAa,EAAE;YAClB,OAAO;cAAErB,OAAO,EAAE,KAAK;cAAExI,KAAK,EAAE;YAAmB,CAAC;UACtD;;UAEA;UACA,MAAM6L,YAAY,GAAG,EAAE;;UAEvB;UACA,MAAMT,aAAa,GAAG,EAAAO,aAAA,GAAAlL,IAAI,CAACM,MAAM,cAAA4K,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBtF,QAAQ,KAAI,KAAK;UACzD,IAAI,CAAC8E,aAAa,EAAE;YAClBS,YAAY,CAACjG,IAAI,CAAC,oBAAoB,CAAC;UACzC;;UAEA;UACA,IAAInF,IAAI,CAACqL,QAAQ,EAAE;YACjBD,YAAY,CAACjG,IAAI,CAAC,uBAAuB,CAAC;UAC5C;;UAEA;UACA,IAAI,CAACnF,IAAI,CAACX,KAAK,IAAIW,IAAI,CAACX,KAAK,CAACyB,MAAM,KAAK,CAAC,EAAE;YAC1CsK,YAAY,CAACjG,IAAI,CAAC,mBAAmB,CAAC;UACxC;UAEA3F,OAAO,CAACM,GAAG,CAAC,qCAAqC,EAAEsL,YAAY,CAAC;;UAEhE;UACA,IAAIE,eAAe,GAAG,eAAe;UACrC,IAAI;YACF;YACA,MAAMf,YAAY,GAAGvM,QAAQ,CAACwM,0BAA0B,CAAC,CAAC;YAC1D,IAAID,YAAY,EAAE;cAChBe,eAAe,GAAG,mCAAmC;YACvD;UACF,CAAC,CAAC,OAAOb,UAAU,EAAE;YACnBa,eAAe,mBAAAxJ,MAAA,CAAmB2I,UAAU,CAAC/K,OAAO,CAAE;UACxD;UAEA,OAAO;YACLqI,OAAO,EAAEqB,aAAa,IAAIgC,YAAY,CAACtK,MAAM,KAAK,CAAC;YACnDY,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChBgJ,aAAa,EAAEA,aAAa;YAC5BuB,aAAa,EAAEA,aAAa;YAC5BS,YAAY,EAAEA,YAAY;YAC1BE,eAAe,EAAEA,eAAe;YAChCC,aAAa,EAAE;cACbnL,GAAG,EAAEJ,IAAI,CAACI,GAAG;cACbE,MAAM,EAAEN,IAAI,CAACM,MAAM;cACnBjB,KAAK,EAAEW,IAAI,CAACX,KAAK;cACjBmB,OAAO,EAAER,IAAI,CAACQ,OAAO;cACrB6K,QAAQ,EAAErL,IAAI,CAACqL,QAAQ,IAAI;YAC7B;UACF,CAAC;QAEH,CAAC,CAAC,OAAO9L,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,OAAO;YAAEwI,OAAO,EAAE,KAAK;YAAExI,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,4BAA4B8L,CAAA,EAAG;QACnC,IAAI,CAAC,IAAI,CAAC9J,MAAM,EAAE;UAChB,MAAM,IAAI5D,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAA6G,aAAA,EAAAC,cAAA;UACF,MAAM1L,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC,IAAI,CAAC/D,MAAM,CAAC;UACzDlC,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAE;YACnDK,EAAE,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,GAAG;YACbC,KAAK,EAAEL,IAAI,aAAJA,IAAI,wBAAAyL,aAAA,GAAJzL,IAAI,CAAEM,MAAM,cAAAmL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmBnL,OAAO;YACjClB,KAAK,EAAEW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX;UACf,CAAC,CAAC;;UAEF;UACA,IAAI,CAACW,IAAI,CAACX,KAAK,EAAE;YACf,MAAMvB,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAAC,IAAI,CAACS,MAAM,EAAE;cAC1CR,IAAI,EAAE;gBAAE7B,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YACF,OAAO,mBAAmB;UAC5B;;UAEA;UACA,IAAI,CAACW,IAAI,CAACX,KAAK,CAACsM,QAAQ,CAAC,OAAO,CAAC,EAAE;YACjCnM,OAAO,CAACM,GAAG,CAAC,kEAAkE,CAAC;;YAE/E;YACA,MAAM8L,UAAU,GAAG,MAAM9N,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC;YACzD,IAAIwK,UAAU,KAAK,CAAC,EAAE;cACpBpM,OAAO,CAACM,GAAG,CAAC,qDAAqD,CAAC;cAClE,MAAMhC,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAAC,IAAI,CAACS,MAAM,EAAE;gBAC1CR,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAAC,OAAO;gBAAE;cAC3B,CAAC,CAAC;cACF,OAAO,kBAAkB;YAC3B;YACA,OAAO,mBAAmB;UAC5B;UAEA,OAAO,uBAAuB;QAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,mBAAmB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC5D;MACF,CAAC;MAED,MAAM,qBAAqBmM,CAAA,EAAG;QAC5B,IAAI,CAAC,IAAI,CAACnK,MAAM,EAAE;UAChB,MAAM,IAAI5D,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAAkH,kBAAA;UACF,MAAMvF,WAAW,GAAG,MAAMzI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC,IAAI,CAAC/D,MAAM,CAAC;UAChE,IAAI,GAAAoK,kBAAA,GAACvF,WAAW,CAAClH,KAAK,cAAAyM,kBAAA,eAAjBA,kBAAA,CAAmBH,QAAQ,CAAC,OAAO,CAAC,GAAE;YACzC,MAAM,IAAI7N,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;UAC5E;UAEA,MAAMjF,QAAQ,GAAG,MAAM7B,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;UACvD,MAAMkM,eAAe,GAAG,EAAE;UAC1B,MAAMC,KAAK,GAAG,EAAE;UAEhB,KAAK,MAAMhM,IAAI,IAAIL,QAAQ,EAAE;YAAA,IAAAsM,cAAA,EAAAC,YAAA;YAC3B,MAAMC,MAAM,GAAG,EAAE;;YAEjB;YACA,IAAI,CAACnM,IAAI,CAACX,KAAK,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACX,IAAI,CAACX,KAAK,CAAC,EAAE;cAAA,IAAA+M,cAAA,EAAAC,aAAA,EAAAC,cAAA;cAC7CH,MAAM,CAAChH,IAAI,CAAC,gBAAgB,CAAC;cAC7B;cACA,MAAMvD,IAAI,GAAG,EAAAwK,cAAA,GAAApM,IAAI,CAACQ,OAAO,cAAA4L,cAAA,uBAAZA,cAAA,CAAcxK,IAAI,KAAI,aAAa;cAChD,MAAM9D,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;gBACvCc,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAACuC,IAAI;gBAAE;cACxB,CAAC,CAAC;cACFoK,KAAK,CAAC7G,IAAI,0BAAArD,MAAA,EAAAuK,aAAA,GAA0BrM,IAAI,CAACM,MAAM,cAAA+L,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkB/L,OAAO,CAAE,CAAC;YAClE;;YAEA;YACA,IAAI,CAAA0L,cAAA,GAAAjM,IAAI,CAACQ,OAAO,cAAAyL,cAAA,eAAZA,cAAA,CAAcrK,IAAI,IAAI,EAAAsK,YAAA,GAAAlM,IAAI,CAACX,KAAK,cAAA6M,YAAA,uBAAVA,YAAA,CAAa,CAAC,CAAC,MAAKlM,IAAI,CAACQ,OAAO,CAACoB,IAAI,EAAE;cAAA,IAAA2K,aAAA,EAAAC,cAAA;cAC/DL,MAAM,CAAChH,IAAI,CAAC,4BAA4B,CAAC;cACzC;cACA,MAAMrH,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;gBACvCc,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAACW,IAAI,CAACQ,OAAO,CAACoB,IAAI;gBAAE;cACrC,CAAC,CAAC;cACFoK,KAAK,CAAC7G,IAAI,4BAAArD,MAAA,EAAAyK,aAAA,GAA4BvM,IAAI,CAACM,MAAM,cAAAiM,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBjM,OAAO,CAAE,CAAC;YACpE;YAEA,IAAI4L,MAAM,CAACrL,MAAM,GAAG,CAAC,EAAE;cAAA,IAAA2L,aAAA,EAAAC,cAAA;cACrBX,eAAe,CAAC5G,IAAI,CAAC;gBACnB9E,KAAK,GAAAoM,aAAA,GAAEzM,IAAI,CAACM,MAAM,cAAAmM,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBnM,OAAO;gBAChC4L;cACF,CAAC,CAAC;YACJ;UACF;UAEA,OAAO;YACLJ,eAAe;YACfC,KAAK;YACLtM,OAAO,EAAEsM,KAAK,CAAClL,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;UACpD,CAAC;QACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,iBAAiB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,4BAA4BiN,CAAA,EAAG;QAC7B;QACA,IAAI,CAACvK,OAAO,CAACC,GAAG,CAACuK,QAAQ,IAAIxK,OAAO,CAACC,GAAG,CAACuK,QAAQ,KAAK,aAAa,EAAE;UACnE,IAAI;YACF,MAAMC,UAAU,GAAG;cACjBxM,KAAK,EAAE,wBAAwB;cAC/BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC;YAED,MAAME,MAAM,GAAG1D,QAAQ,CAACqH,UAAU,CAAC;cACjChF,KAAK,EAAEwM,UAAU,CAACxM,KAAK;cACvBiB,QAAQ,EAAEuL,UAAU,CAACvL,QAAQ;cAC7Bd,OAAO,EAAE;gBACPe,SAAS,EAAEsL,UAAU,CAACtL,SAAS;gBAC/BC,QAAQ,EAAEqL,UAAU,CAACrL,QAAQ;gBAC7BI,IAAI,EAAE,aAAa;gBACnBC,QAAQ,KAAAC,MAAA,CAAK+K,UAAU,CAACtL,SAAS,OAAAO,MAAA,CAAI+K,UAAU,CAACrL,QAAQ;cAC1D;YACF,CAAC,CAAC;;YAEF;YACA1D,MAAM,CAACsB,KAAK,CAAC0N,MAAM,CAACpL,MAAM,EAAE;cAC1BR,IAAI,EAAE;gBAAE7B,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YAEF,OAAO;cACL0I,OAAO,EAAE,IAAI;cACbrG,MAAM;cACNhC,OAAO,EAAE;YACX,CAAC;UACH,CAAC,CAAC,OAAOH,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,2BAA2B,EAAErF,KAAK,CAACG,OAAO,CAAC;UACpE;QACF,CAAC,MAAM;UACL,MAAM,IAAI5B,MAAM,CAAC8G,KAAK,CAAC,iBAAiB,EAAE,8CAA8C,CAAC;QAC3F;MACF;IACF,CAAC,CAAC;IAACmI,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA", "ignoreList": []}, "sourceType": "module", "externalDependencies": {}, "hash": "f7f3b2dc9bd62cbbf2046db546d4ad236f8da3a8"}