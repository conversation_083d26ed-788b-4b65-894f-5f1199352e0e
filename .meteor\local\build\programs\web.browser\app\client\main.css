@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  body {
    @apply bg-white;
  }
}

@layer components {
  .form-input {
    @apply mt-1 block w-full rounded-lg border border-gray-200 px-4 py-3 focus:border-green-600 focus:ring-2 focus:ring-green-600/20;
  }

  .form-label {
    @apply block text-sm font-semibold text-gray-800;
  }

  .btn {
    @apply px-5 py-2.5 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-600 shadow-sm shadow-green-600/20;
  }

  .btn-secondary {
    @apply border-2 border-gray-200 text-gray-800 hover:border-green-600 hover:text-green-600 focus:ring-green-600 bg-white;
  }

  .error-message {
    @apply mt-1 text-sm text-red-600 bg-red-50 px-3 py-2 rounded-md;
  }
}

:root {
  /* Primary Colors */
  --primary-dark: #15803d;
  --primary-main: #16a34a;
  --primary-light: #22c55e;
  --primary-contrast: #ffffff;

  /* Neutral Colors */
  --neutral-50: #ffffff;
  --neutral-100: #f8fafc;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;

  /* System Colors */
  --error: #dc2626;
  --warning: #f59e0b;
  --info: #0ea5e9;
  --success: #16a34a;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  
  /* Spacing */
  --spacing-xs: 0.375rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1.25rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(22, 163, 74, 0.1), 0 2px 4px -1px rgba(22, 163, 74, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(22, 163, 74, 0.1), 0 4px 6px -2px rgba(22, 163, 74, 0.05);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--neutral-50);
  color: var(--neutral-900);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Authentication Pages */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  background: #fff;
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
  opacity: 0.2;
  pointer-events: none;
}

.auth-card {
  background: #fff;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 480px;
  padding: var(--spacing-xl);
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #111;
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.auth-logo {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-main);
}

.auth-logo svg {
  filter: drop-shadow(0 4px 6px rgba(22, 163, 74, 0.2));
}

.auth-title {
  color: #111;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  letter-spacing: -0.025em;
}

.auth-subtitle {
  color: #111;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.auth-form {
  margin-bottom: var(--spacing-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  color: #111;
  font-weight: 600;
  font-size: 0.875rem;
}

.form-icon {
  color: var(--neutral-500);
  flex-shrink: 0;
}

.form-control {
  width: 100%;
  padding: 0.875rem var(--spacing-md);
  font-size: 1rem;
  line-height: 1.5;
  color: #111;
  background-color: #fff;
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
}

.form-control:hover {
  border-color: var(--neutral-300);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-main);
  box-shadow: 0 0 0 4px rgba(22, 163, 74, 0.1);
}

.form-control::placeholder {
  color: var(--neutral-400);
}

.form-control-error {
  border-color: var(--error);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
}

.form-control-error:focus {
  border-color: var(--error);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
}

/* Password Requirements */
.password-requirements {
  background: var(--neutral-50);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  margin-top: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.password-requirements-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--neutral-600);
  margin: 0 0 var(--spacing-xs) 0;
}

.password-requirements-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xs);
}

.password-requirements-list li {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--neutral-500);
  transition: var(--transition-fast);
}

.password-requirements-list li.requirement-met {
  color: var(--success);
}

.requirement-icon {
  flex-shrink: 0;
  color: var(--neutral-400);
}

.requirement-met .requirement-icon {
  color: var(--success);
}

/* Error Messages */
.error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background-color: rgba(220, 38, 38, 0.05);
  border: 1px solid rgba(220, 38, 38, 0.2);
  border-radius: var(--radius-md);
  color: var(--error);
  font-size: 0.875rem;
  font-weight: 500;
}

.error-message-global {
  margin-bottom: var(--spacing-md);
  text-align: center;
  justify-content: center;
}

.error-icon {
  flex-shrink: 0;
  color: var(--error);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: 0.875rem var(--spacing-lg);
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-main) 0%, var(--primary-dark) 100%);
  color: #fff;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #111 0%, var(--primary-main) 100%);
  color: #fff;
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-auth {
  width: 100%;
  margin-top: var(--spacing-md);
}

.btn-icon {
  flex-shrink: 0;
}

/* Auth Footer */
.auth-footer {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--neutral-200);
}

.auth-footer-text {
  color: #111;
  font-size: 0.875rem;
  margin: 0;
}

.auth-link {
  color: var(--primary-main);
  text-decoration: none;
  font-weight: 600;
  position: relative;
  transition: var(--transition-fast);
  margin-left: var(--spacing-xs);
}

.auth-link:hover {
  color: var(--primary-dark);
}

.auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-main);
  transition: var(--transition-fast);
}

.auth-link:hover::after {
  width: 100%;
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-contrast);
  animation: spin 1s ease-in-out infinite;
  margin-right: var(--spacing-xs);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Dashboard Layout */
.dashboard-container {
  min-height: 100vh;
  background-color: var(--neutral-100);
  display: grid;
  grid-template-rows: auto 1fr;
}

.dashboard-header {
  background-color: var(--neutral-50);
  border-bottom: 1px solid var(--neutral-200);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
}

.dashboard-nav {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.dashboard-content {
  padding: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Cards */
.card {
  background-color: var(--neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
  transform: translateY(-2px);
}

/* Tables */
.table-container {
  overflow-x: auto;
  margin: var(--spacing-md) 0;
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  background-color: var(--neutral-50);
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

th {
  background-color: var(--neutral-100);
  color: var(--neutral-700);
  font-weight: 600;
  text-align: left;
  padding: var(--spacing-md);
  border-bottom: 2px solid var(--neutral-200);
}

td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--neutral-200);
  color: var(--neutral-800);
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: var(--neutral-100);
}

/* Status Indicators */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.success {
  background-color: rgba(22, 163, 74, 0.1);
  color: var(--primary-dark);
}

.status-badge.pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-badge.error {
  background-color: rgba(220, 38, 38, 0.1);
  color: var(--error);
}

/* Password field wrapper for icon positioning */
.form-control-password-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-control-password-wrapper .form-control {
  padding-right: 2.5rem;
  background: #fff;
  color: #111;
}

.password-toggle-btn {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  color: var(--primary-main, #16a34a); /* green by default */
  display: flex;
  align-items: center;
  height: 2rem;
  width: 2rem;
  transition: color 0.15s;
}

.password-toggle-btn:focus,
.password-toggle-btn:hover {
  color: #111; /* black on hover/focus */
  outline: 2px solid var(--primary-main, #16a34a);
  outline-offset: 2px;
}

.password-toggle-btn svg {
  display: block;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-card {
    padding: var(--spacing-lg);
    margin: var(--spacing-sm);
  }

  .auth-title {
    font-size: 1.75rem;
  }

  .btn {
    padding: 0.75rem var(--spacing-md);
    font-size: 0.875rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .password-requirements-list {
    grid-template-columns: 1fr;
  }

  .dashboard-content {
    padding: var(--spacing-sm);
  }

  .card {
    padding: var(--spacing-md);
  }

  th, td {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.875rem;
  }
}

@media (max-width: 600px) {
  .team-member-grid {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }
  .dashboard-content {
    padding: 8px !important;
    width: 100% !important;
  }
  .card {
    padding: 12px !important;
    width: 100% !important;
    min-width: 0 !important;
  }
}
