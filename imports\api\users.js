import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';
import { Accounts } from 'meteor/accounts-base';
import { Roles } from 'meteor/alanning:roles';

if (Meteor.isServer) {
  // Publications
  Meteor.publish('teamMembers', function() {
    if (!this.userId) {
      return this.ready();
    }

    return Meteor.users.find(
      { 
        $or: [
          { 'roles': 'team-member' },
          { 'profile.role': 'team-member' }
        ]
      },
      { 
        fields: { 
          emails: 1, 
          roles: 1, 
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      }
    );
  });
}

Meteor.methods({
  'users.addTeamMember'(memberData) {
    check(memberData, {
      email: String,
      firstName: String,
      lastName: String,
      role: String,
      department: String,
      skills: Array
    });

    if (!this.userId) {
      throw new Meteor.Error('Not authorized.');
    }

    // Check if user already exists
    const existingUser = Accounts.findUserByEmail(memberData.email);
    if (existingUser) {
      throw new Meteor.Error('User already exists with this email.');
    }

    // Create new user
    const userId = Accounts.createUser({
      email: memberData.email,
      password: Math.random().toString(36).slice(-8), // Generate random password
      profile: {
        firstName: memberData.firstName,
        lastName: memberData.lastName,
        role: memberData.role,
        department: memberData.department,
        skills: memberData.skills,
        joinDate: new Date()
      },
      createdAt: new Date()
    });

    // Add team member role
    Roles.addUsersToRoles(userId, ['team-member']);

    // Send welcome email with password
    // Note: In a production environment, you should use a proper email service
    // and implement a password reset flow instead of sending the password
    const password = Math.random().toString(36).slice(-8);
    Accounts.setPassword(userId, password);

    // TODO: Send welcome email with password
    console.log(`New team member created: ${memberData.email} with password: ${password}`);

    return userId;
  },

  'users.updateTeamMember'(memberId, memberData) {
    check(memberId, String);
    check(memberData, {
      email: String,
      firstName: String,
      lastName: String,
      role: String,
      department: String,
      skills: Array
    });

    if (!this.userId) {
      throw new Meteor.Error('Not authorized.');
    }

    const member = Meteor.users.findOne(memberId);
    if (!member) {
      throw new Meteor.Error('Team member not found.');
    }

    // Update user profile
    Meteor.users.update(memberId, {
      $set: {
        'profile.firstName': memberData.firstName,
        'profile.lastName': memberData.lastName,
        'profile.role': memberData.role,
        'profile.department': memberData.department,
        'profile.skills': memberData.skills
      }
    });

    // Update email if changed
    if (member.emails?.[0]?.address !== memberData.email) {
      Accounts.addEmail(memberId, memberData.email);
      if (member.emails?.length > 1) {
        Accounts.removeEmail(memberId, member.emails[0].address);
      }
    }

    return memberId;
  },

  'users.deleteTeamMember'(memberId) {
    check(memberId, String);

    if (!this.userId) {
      throw new Meteor.Error('Not authorized.');
    }

    console.log('[deleteTeamMember] Attempting to delete team member:', memberId);

    const member = Meteor.users.findOne(memberId);
    if (!member) {
      console.log('[deleteTeamMember] Team member not found:', memberId);
      throw new Meteor.Error('Team member not found.');
    }

    console.log('[deleteTeamMember] Found team member:', {
      id: member._id,
      email: member.emails?.[0]?.address,
      roles: member.roles
    });

    try {
      // Remove team member role
      Roles.removeUsersFromRoles(memberId, ['team-member']);
      console.log('[deleteTeamMember] Removed team member role');

      // Delete user account
      const result = Meteor.users.remove({ _id: memberId });
      console.log('[deleteTeamMember] Delete result:', result);

      if (result === 0) {
        console.log('[deleteTeamMember] Failed to delete user');
        throw new Meteor.Error('Failed to delete user');
      }

      console.log('[deleteTeamMember] Successfully deleted team member');
      return memberId;
    } catch (error) {
      console.error('[deleteTeamMember] Error:', error);
      throw new Meteor.Error('delete-team-member-failed', error.message);
    }
  }
}); 