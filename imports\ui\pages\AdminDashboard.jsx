import React, { useState, useEffect, useMemo } from 'react';
import { Meteor } from 'meteor/meteor';
import { useNavigate, Link, Routes, Route, useParams, useLocation } from 'react-router-dom';
import { useTracker } from 'meteor/react-meteor-data';
import { Tasks, taskCategories, taskLabels } from '/imports/api/tasks';
import { CreateTask } from '../components/CreateTask';
import { EditTask } from '../components/EditTask';
import { TeamMemberForm } from '../components/TeamMemberForm';
import { TeamMemberMetrics } from '../components/TeamMemberMetrics';
import { TaskAssignmentHistory } from '../components/TaskAssignmentHistory';
import { ReportsPage } from './ReportsPage';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { Header } from '../components/Header';
import { AIInsightsPanel } from '../components/AIInsightsPanel';

const AdminDashboardHome = () => {
  const { user } = useTracker(() => {
    const userHandle = Meteor.subscribe('userData');
    const user = Meteor.user();
    return { user };
  });

  return (
    <div style={{ 
      padding: '24px',
      background: '#ffffff',
      minHeight: 'calc(100vh - 64px)'
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }}>
        <h2 style={{ 
          color: '#0f172a',
          margin: '0 0 24px 0',
          fontSize: '1.5rem',
          fontWeight: '600'
        }}>
          Welcome, {user?.profile?.firstName || 'Admin'}!
        </h2>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px',
          marginBottom: '32px'
        }}>
          {/* Task Management Card */}
          <div className="dashboard-card" style={{ 
            backgroundColor: '#ffffff',
            border: '1px solid #e2e8f0',
            borderRadius: '12px',
            padding: '24px',
            transition: 'all 0.2s ease',
            cursor: 'pointer',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}>
            <div style={{ 
              width: '48px',
              height: '48px',
              borderRadius: '12px',
              backgroundColor: 'rgba(22, 163, 74, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '16px'
            }}>
              <span style={{ fontSize: '24px' }}>📋</span>
            </div>
            <h3 style={{ 
              color: '#0f172a',
              fontSize: '1.25rem',
              marginBottom: '8px',
              fontWeight: '600'
            }}>Task Management</h3>
            <p style={{ color: '#475569', marginBottom: '16px' }}>Create and manage tasks</p>
            <Link to="/admin-dashboard/tasks" className="btn btn-primary" style={{ width: '100%' }}>
              Manage Tasks
            </Link>
          </div>

          {/* Team Members Card */}
          <div className="dashboard-card" style={{ 
            backgroundColor: '#ffffff',
            border: '1px solid #e2e8f0',
            borderRadius: '12px',
            padding: '24px',
            transition: 'all 0.2s ease',
            cursor: 'pointer',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}>
            <div style={{ 
              width: '48px',
              height: '48px',
              borderRadius: '12px',
              backgroundColor: 'rgba(22, 163, 74, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '16px'
            }}>
              <span style={{ fontSize: '24px', color: '#16a34a' }}>👥</span>
            </div>
            <h3 style={{ 
              color: '#0f172a',
              fontSize: '1.25rem',
              marginBottom: '8px',
              fontWeight: '600'
            }}>Team Members</h3>
            <p style={{ color: '#475569', marginBottom: '16px' }}>View and manage team members</p>
            <Link to="/admin-dashboard/team" className="btn btn-primary" style={{ width: '100%' }}>
              Manage Team
            </Link>
          </div>

          {/* Reports Card */}
          <div className="dashboard-card" style={{ 
            backgroundColor: '#ffffff',
            border: '1px solid #e2e8f0',
            borderRadius: '12px',
            padding: '24px',
            transition: 'all 0.2s ease',
            cursor: 'pointer',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}>
            <div style={{ 
              width: '48px',
              height: '48px',
              borderRadius: '12px',
              backgroundColor: 'rgba(22, 163, 74, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '16px'
            }}>
              <span style={{ fontSize: '24px', color: '#16a34a' }}>📊</span>
            </div>
            <h3 style={{ 
              color: '#0f172a',
              fontSize: '1.25rem',
              marginBottom: '8px',
              fontWeight: '600'
            }}>Reports</h3>
            <p style={{ color: '#475569', marginBottom: '16px' }}>View task analytics and reports</p>
            <Link to="/admin-dashboard/reports" className="btn btn-primary" style={{ width: '100%' }}>
              View Reports
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

const TasksPage = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [viewingTask, setViewingTask] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [userEmails, setUserEmails] = useState({});
  const [userNames, setUserNames] = useState({});
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const { taskId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const { tasks, isLoading, user, tasksReady, userReady, teamMembers } = useTracker(() => {
    const tasksHandle = Meteor.subscribe('tasks');
    const userHandle = Meteor.subscribe('userData');
    const teamMembersHandle = Meteor.subscribe('teamMembers');
    
    const tasksReady = tasksHandle.ready();
    const userReady = userHandle.ready();
    const teamMembersReady = teamMembersHandle.ready();
    
    const user = Meteor.user();
    const tasks = Tasks.find({}, { sort: { createdAt: -1 } }).fetch();
    const teamMembers = Meteor.users.find(
      { 
        $or: [
          { 'roles': 'team-member' },
          { 'profile.role': 'team-member' }
        ]
      },
      { 
        fields: { 
          emails: 1, 
          roles: 1, 
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      }
    ).fetch();
    
    const isLoading = !tasksReady || !userReady || !teamMembersReady;
    
    return {
      tasks,
      user,
      tasksReady,
      userReady,
      teamMembersReady,
      isLoading,
      teamMembers
    };
  }, []);

  useEffect(() => {
    if (taskId) {
      const task = tasks.find(t => t._id === taskId);
      if (task) {
        setViewingTask(task);
      } else {
        if (location.state?.from) {
          if (location.state.memberId) {
            const member = teamMembers.find(m => m._id === location.state.memberId);
            if (member) {
              setSelectedMember(member);
            } else {
              navigate(location.state.from);
            }
          } else {
            navigate(location.state.from);
          }
        } else {
          navigate('/admin-dashboard/tasks');
        }
      }
    } else {
      setViewingTask(null);
    }
  }, [taskId, tasks, location.state, teamMembers, navigate]);

  useEffect(() => {
    const loadUserData = async () => {
      if (!tasks) return;
      
      setIsLoadingUsers(true);
      try {
        const userIds = [...new Set(tasks.flatMap(task => task.assignedTo))];
        const userDataPromises = userIds.map(async userId => {
          const user = await Meteor.users.findOneAsync(userId);
          if (!user) return [userId, { name: 'Unknown User', email: 'Unknown' }];
          
          const firstName = user.profile?.firstName || '';
          const lastName = user.profile?.lastName || '';
          const fullName = `${firstName} ${lastName}`.trim();
          
          return [
            userId, 
            {
              email: user.emails?.[0]?.address || 'No email',
              name: fullName || 'Unknown User'
            }
          ];
        });
        
        const userEntries = await Promise.all(userDataPromises);
        const userData = Object.fromEntries(userEntries);
        
        setUserEmails(Object.fromEntries(userEntries.map(([id, data]) => [id, data.email])));
        setUserNames(Object.fromEntries(userEntries.map(([id, data]) => [id, data.name])));
      } catch (error) {
        console.error('Error loading user data:', error);
      } finally {
        setIsLoadingUsers(false);
      }
    };
    
    loadUserData();
  }, [tasks]);

  const handleCreateTask = () => {
    setShowCreateForm(true);
    setEditingTask(null);
    setSuccessMessage('');
  };

  const handleEditTask = (task) => {
    setEditingTask(task);
    setViewingTask(null);
    setShowCreateForm(false);
  };

  const handleCancel = () => {
    setShowCreateForm(false);
    setEditingTask(null);
    setViewingTask(null);
    setSuccessMessage('Task saved successfully!');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const handleTaskUpdate = async (taskId, updatedTask) => {
    try {
      console.log('Received task update:', { taskId, updatedTask });

      // Ensure all required fields are present
      const taskToUpdate = {
        ...updatedTask,
        title: updatedTask.title.trim(),
        description: updatedTask.description.trim(),
        assignedTo: updatedTask.assignedTo || [],
        checklist: updatedTask.checklist || [],
        labels: updatedTask.labels || [],
        category: updatedTask.category || '',
        progress: updatedTask.progress || 0,
        startDate: new Date(updatedTask.startDate),
        dueDate: new Date(updatedTask.dueDate)
      };

      console.log('Sending task update to server:', taskToUpdate);
      
      // Call the server method
      const result = await new Promise((resolve, reject) => {
        Meteor.call('tasks.update', taskId, taskToUpdate, (error, result) => {
          if (error) {
            console.error('Server error updating task:', error);
            setSuccessMessage('Error updating task: ' + error.message);
            reject(error);
          } else {
            console.log('Task update successful:', result);
            setSuccessMessage('Task updated successfully!');
            resolve(result);
          }
        });
      });

      // Force a refresh of the task data
      const updatedTask = Tasks.findOne(taskId);
      if (updatedTask) {
        if (viewingTask?._id === taskId) {
          setViewingTask(updatedTask);
        }
        if (editingTask?._id === taskId) {
          setEditingTask(updatedTask);
        }
      }

      setEditingTask(null);
      setViewingTask(null);
    } catch (error) {
      console.error('Error updating task:', error);
      setSuccessMessage('Error updating task: ' + error.message);
    } finally {
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  };

  const handleDeleteTask = async (taskId) => {
    const task = tasks.find(t => t._id === taskId);
    if (!task) return;

    const confirmMessage = `Are you sure you want to delete the task "${task.title}"?\n\nThis action cannot be undone.`;
    
    if (window.confirm(confirmMessage)) {
      try {
        setSuccessMessage('Deleting task...');
        await new Promise((resolve, reject) => {
          Meteor.call('tasks.delete', taskId, (error, result) => {
            if (error) {
              console.error('Error deleting task:', error);
              reject(error);
            } else {
              console.log('Task deleted successfully:', result);
              resolve(result);
            }
          });
        });
        
        setSuccessMessage('Task deleted successfully!');
        // Clear any active views
        setViewingTask(null);
        setEditingTask(null);
      } catch (error) {
        console.error('Error deleting task:', error);
        setSuccessMessage(`Error deleting task: ${error.message}`);
      } finally {
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    }
  };

  const filteredAndSortedTasks = useMemo(() => {
    if (!tasks) return [];

    let filtered = [...tasks];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(task => 
        task.title.toLowerCase().includes(query) ||
        task.description.toLowerCase().includes(query)
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(task => task.status === filterStatus);
    }

    // Apply priority filter
    if (filterPriority !== 'all') {
      filtered = filtered.filter(task => task.priority === filterPriority);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'dueDate':
          comparison = new Date(a.dueDate) - new Date(b.dueDate);
          break;
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        default:
          comparison = new Date(b.createdAt) - new Date(a.createdAt);
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [tasks, searchQuery, filterStatus, filterPriority, sortBy, sortOrder]);

  // Subscribe to specific task updates when viewing or editing
  useEffect(() => {
    let handle;
    if (viewingTask) {
      handle = Meteor.subscribe('task', viewingTask._id);
    } else if (editingTask) {
      handle = Meteor.subscribe('task', editingTask._id);
    }
    return () => handle?.stop();
  }, [viewingTask?._id, editingTask?._id]);

  // Update viewingTask when the task data changes
  useEffect(() => {
    if (viewingTask) {
      const updatedTask = Tasks.findOne(viewingTask._id);
      if (updatedTask && JSON.stringify(updatedTask) !== JSON.stringify(viewingTask)) {
        console.log('Updating viewing task:', { old: viewingTask, new: updatedTask });
        setViewingTask(updatedTask);
      }
    }
  }, [viewingTask?._id]);

  // Update editingTask when the task data changes
  useEffect(() => {
    if (editingTask) {
      const updatedTask = Tasks.findOne(editingTask._id);
      if (updatedTask && JSON.stringify(updatedTask) !== JSON.stringify(editingTask)) {
        console.log('Updating editing task:', { old: editingTask, new: updatedTask });
        setEditingTask(updatedTask);
      }
    }
  }, [editingTask?._id]);

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '24px' }}>
        <div className="loading-spinner"></div>
      </div>
    );
  }

  if (viewingTask) {
    return (
      <div style={{ 
        display: 'flex',
        flexDirection: 'row',
        gap: '32px',
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }}>
        <div style={{ flex: 2 }}>
          <div style={{
            backgroundColor: '#ffffff',
            borderRadius: '16px',
            padding: '24px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}>
            {/* Header with Back Arrow and Title */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '24px',
              flexWrap: 'wrap',
              gap: '16px'
            }}>
                <button
                  onClick={() => {
                    if (location.state?.from) {
                      if (location.state.memberId) {
                        navigate(`/admin-dashboard/team/${location.state.memberId}`);
                      } else {
                        navigate(location.state.from);
                      }
                    } else {
                      navigate('/admin-dashboard/tasks');
                    }
                  }}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '1.8rem',
                  color: '#64748b',
                  transition: 'color 0.2s ease',
                  marginRight: '12px',
                  padding: 0,
                  display: 'flex',
                  alignItems: 'center'
                }}
                onMouseOver={e => e.currentTarget.style.color = '#334155'}
                onMouseOut={e => e.currentTarget.style.color = '#64748b'}
                aria-label="Back"
              >
                ←
                </button>
                <h2 style={{ 
                  fontSize: '1.5rem',
                fontWeight: '600',
                color: '#0f172a',
                margin: '0',
                wordBreak: 'break-word',
                display: 'inline-block'
                }}>
                  {viewingTask.title}
                </h2>
            </div>

            {/* Info Row: Status, Priority, Due Date, Progress, Team Members */}
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px', marginBottom: '24px' }}>
              {/* Status Badge */}
                    <span style={{
                padding: '4px 12px',
                      borderRadius: '6px',
                fontSize: '0.875rem',
                      fontWeight: '500',
                backgroundColor: viewingTask.status === 'completed' ? '#dcfce7' : viewingTask.status === 'in-progress' ? '#fef3c7' : '#fee2e2',
                color: viewingTask.status === 'completed' ? '#16a34a' : viewingTask.status === 'in-progress' ? '#d97706' : '#dc2626'
                    }}>
                {viewingTask.status === 'in-progress' ? 'In Progress' : viewingTask.status.charAt(0).toUpperCase() + viewingTask.status.slice(1)}
                    </span>
              {/* Priority Badge */}
                    <span style={{
                padding: '4px 12px',
                      borderRadius: '6px',
                fontSize: '0.875rem',
                      fontWeight: '500',
                backgroundColor: viewingTask.priority === 'high' ? '#fee2e2' : viewingTask.priority === 'medium' ? '#fef3c7' : '#dcfce7',
                color: viewingTask.priority === 'high' ? '#dc2626' : viewingTask.priority === 'medium' ? '#d97706' : '#16a34a'
                    }}>
                {viewingTask.priority.charAt(0).toUpperCase() + viewingTask.priority.slice(1)}
                    </span>
              {/* Due Date Badge */}
              {viewingTask.dueDate && (
                <span style={{
                  padding: '4px 12px',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  backgroundColor: '#f1f5f9',
                  color: '#475569',
                      display: 'flex',
                      alignItems: 'center',
                  gap: '6px'
                    }}>
                  <span>📅</span>
                  Due: {new Date(viewingTask.dueDate).toLocaleDateString()}
                      </span>
              )}
              {/* Progress Badge */}
                      <span style={{
                padding: '4px 12px',
                borderRadius: '6px',
                fontSize: '0.875rem',
                fontWeight: '500',
                        backgroundColor: '#f1f5f9',
                color: '#475569',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
                      }}>
                <span>📊</span>
                Progress: {viewingTask.progress || 0}%
                      </span>
              {/* Team Members */}
              {viewingTask.assignedTo && viewingTask.assignedTo.length > 0 && (
                <span style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                  <span style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '500' }}>Team:</span>
                  {viewingTask.assignedTo.map(userId => (
                          <span
                            key={userId}
                            style={{
                        padding: '4px 12px',
                        borderRadius: '6px',
                        fontSize: '0.875rem',
                              backgroundColor: '#f1f5f9',
                        color: '#475569',
                        fontWeight: '500'
                            }}
                          >
                            {userNames[userId] || 'Unknown User'}
                          </span>
                        ))}
                          </span>
                    )}
                  </div>

            {/* Description Section */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ color: '#334155', marginBottom: '8px', fontSize: '1.2rem', fontWeight: '600' }}>Description</h3>
              <p style={{ color: '#475569', whiteSpace: 'pre-line', lineHeight: '1.6' }}>{viewingTask.description || 'No description provided.'}</p>
                </div>

            {/* Checklist Section */}
                {viewingTask.checklist && viewingTask.checklist.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <h3 style={{ color: '#334155', marginBottom: '16px', fontSize: '1.2rem', fontWeight: '600' }}>Checklist</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                      {viewingTask.checklist.map((item, index) => (
                    <div key={index} style={{
                            display: 'flex',
                            alignItems: 'center',
                      gap: '12px',
                      backgroundColor: '#f8fafc',
                      padding: '12px',
                      borderRadius: '8px',
                      border: '1px solid #e2e8f0',
                            color: item.completed ? '#64748b' : '#0f172a',
                      textDecoration: item.completed ? 'line-through' : 'none',
                      fontSize: '1rem',
                    }}>
                          <input
                            type="checkbox"
                            checked={item.completed}
                            disabled
                        style={{ width: '20px', height: '20px', accentColor: '#3b82f6', cursor: 'pointer' }}
                          />
                          <span>{item.text}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Attachments Section */}
                {viewingTask.attachments && viewingTask.attachments.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <h3 style={{ color: '#334155', marginBottom: '16px', fontSize: '1.2rem', fontWeight: '600' }}>Attachments</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                      {viewingTask.attachments.map((attachment, index) => (
                    <div key={index} style={{
                            display: 'flex',
                            alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '12px',
                            backgroundColor: '#f8fafc',
                      borderRadius: '8px',
                      border: '1px solid #e2e8f0',
                      wordBreak: 'break-word',
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1, minWidth: 0 }}>
                        <span style={{ fontSize: '1.5rem', color: '#64748b' }}>📎</span>
                        <div>
                          <div style={{ fontSize: '0.9rem', color: '#0f172a', fontWeight: '500' }}>
                            {attachment.name}
                          </div>
                          <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                            Uploaded by {userNames[attachment.uploadedBy] || 'Unknown User'} on {new Date(attachment.uploadedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                          <button
                            onClick={() => {
                              try {
                                let base64Data = attachment.data;
                                if (base64Data.startsWith('data:')) {
                                  base64Data = base64Data.split(',')[1];
                                }
                                const byteCharacters = atob(base64Data);
                                const byteNumbers = new Array(byteCharacters.length);
                                for (let i = 0; i < byteCharacters.length; i++) {
                                  byteNumbers[i] = byteCharacters.charCodeAt(i);
                                }
                                const byteArray = new Uint8Array(byteNumbers);
                                const mimeType = attachment.type || 'application/octet-stream';
                                const blob = new Blob([byteArray], { type: mimeType });
                                const url = window.URL.createObjectURL(blob);
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = attachment.name;
                                document.body.appendChild(link);
                                link.click();
                                setTimeout(() => {
                                  document.body.removeChild(link);
                                  window.URL.revokeObjectURL(url);
                                }, 100);
                              } catch (error) {
                                console.error('Error downloading file:', error);
                                alert('Failed to download file. Please try again.');
                              }
                            }}
                            style={{
                          padding: '8px 16px',
                          backgroundColor: '#e2e8f0',
                              border: 'none',
                          borderRadius: '6px',
                          color: '#0f172a',
                          fontSize: '0.875rem',
                              cursor: 'pointer',
                          transition: 'background-color 0.2s ease',
                            }}
                        onMouseOver={e => e.currentTarget.style.backgroundColor = '#cbd5e1'}
                        onMouseOut={e => e.currentTarget.style.backgroundColor = '#e2e8f0'}
                          >
                            Download
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Links Section */}
                {viewingTask.links && viewingTask.links.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <h3 style={{ color: '#334155', marginBottom: '16px', fontSize: '1.2rem', fontWeight: '600' }}>Links</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                      {viewingTask.links.map((link, index) => (
                    <div key={index} style={{
                            display: 'flex',
                            alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '12px',
                            backgroundColor: '#f8fafc',
                      borderRadius: '8px',
                      border: '1px solid #e2e8f0',
                      wordBreak: 'break-word',
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1, minWidth: 0 }}>
                        <span style={{ fontSize: '1.5rem', color: '#64748b' }}>🔗</span>
                        <div>
                          <a 
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ fontSize: '0.9rem', color: '#2563eb', textDecoration: 'none', fontWeight: '500' }}
                            onMouseOver={e => e.currentTarget.style.textDecoration = 'underline'}
                            onMouseOut={e => e.currentTarget.style.textDecoration = 'none'}
                          >
                            {link.url}
                          </a>
                          <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                            Added by {userNames[link.addedBy] || 'Unknown User'} on {new Date(link.addedAt).toLocaleDateString()}
                        </div>
                    </div>
                  </div>
              </div>
                  ))}
            </div>
              </div>
            )}
          </div>
        </div>
        <div style={{ flex: 1, minWidth: 350, maxWidth: 500 }}>
          <AIInsightsPanel taskId={viewingTask._id} task={viewingTask} />
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '24px',
      background: '#ffffff',
      minHeight: 'calc(100vh - 64px)'
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        marginBottom: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <h2 style={{ 
            color: '#0f172a',
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600'
          }}>Tasks Management</h2>
          <button 
            onClick={handleCreateTask}
            className="btn btn-primary"
          >
            Create New Task
          </button>
        </div>

        {successMessage && (
          <div style={{
            backgroundColor: 'rgba(22, 163, 74, 0.1)',
            color: '#15803d',
            padding: '12px 16px',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            {successMessage}
          </div>
        )}

        {showCreateForm ? (
          <CreateTask onCancel={handleCancel} />
        ) : editingTask ? (
          <EditTask 
            task={editingTask} 
            onCancel={handleCancel}
            onUpdate={handleTaskUpdate}
          />
        ) : (
          <>
            <div style={{
              display: 'flex',
              gap: '16px',
              marginBottom: '24px',
              flexWrap: 'wrap'
            }}>
              <div style={{ flex: 1, minWidth: '200px' }}>
                <input
                  type="text"
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    border: '1px solid #e2e8f0',
                    fontSize: '0.875rem'
                  }}
                />
              </div>
              
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                style={{
                  padding: '8px 12px',
                  borderRadius: '6px',
                  border: '1px solid #e2e8f0',
                  fontSize: '0.875rem',
                  minWidth: '120px'
                }}
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
              </select>

              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                style={{
                  padding: '8px 12px',
                  borderRadius: '6px',
                  border: '1px solid #e2e8f0',
                  fontSize: '0.875rem',
                  minWidth: '120px'
                }}
              >
                <option value="all">All Priority</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>

              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-');
                  setSortBy(newSortBy);
                  setSortOrder(newSortOrder);
                }}
                style={{
                  padding: '8px 12px',
                  borderRadius: '6px',
                  border: '1px solid #e2e8f0',
                  fontSize: '0.875rem',
                  minWidth: '150px'
                }}
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="dueDate-asc">Due Date (Earliest)</option>
                <option value="dueDate-desc">Due Date (Latest)</option>
                <option value="priority-desc">Priority (High to Low)</option>
                <option value="priority-asc">Priority (Low to High)</option>
                <option value="title-asc">Title (A-Z)</option>
                <option value="title-desc">Title (Z-A)</option>
              </select>
            </div>

            <div className="tasks-grid">
              {isLoading ? (
                <div style={{ textAlign: 'center', padding: '24px' }}>
                  <div className="loading-spinner"></div>
                </div>
              ) : filteredAndSortedTasks.length > 0 ? (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                  gap: '24px',
                  padding: '16px 0'
                }}>
                  {filteredAndSortedTasks.map(task => (
                    <div
                      key={task._id}
                      onClick={() => navigate(`/admin-dashboard/tasks/${task._id}`)}
                      style={{
                        backgroundColor: '#ffffff',
                        borderRadius: '12px',
                        padding: '20px',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e2e8f0',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '12px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        ':hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                        }
                      }}
                    >
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start'
                      }}>
                        <h3 style={{
                          margin: 0,
                          fontSize: '1.1rem',
                          fontWeight: '600',
                          color: '#0f172a'
                        }}>
                          {task.title}
                        </h3>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <span style={{
                            padding: '4px 8px',
                            borderRadius: '6px',
                            fontSize: '0.75rem',
                            fontWeight: '500',
                            backgroundColor: task.priority === 'high' ? '#fee2e2' :
                                          task.priority === 'medium' ? '#fef3c7' : '#dcfce7',
                            color: task.priority === 'high' ? '#dc2626' :
                                   task.priority === 'medium' ? '#d97706' : '#16a34a'
                          }}>
                            {task.priority}
                          </span>
                          {task.category && (
                            <span style={{
                              padding: '4px 8px',
                              borderRadius: '6px',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              backgroundColor: '#f1f5f9',
                              color: '#475569'
                            }}>
                              {task.category}
                            </span>
                          )}
                        </div>
                      </div>

                      <p style={{
                        margin: 0,
                        fontSize: '0.875rem',
                        color: '#475569',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxHeight: '2.6em'
                      }}>
                        {task.description}
                      </p>

                      <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '8px'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}>
                          <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Status:</span>
                          <span style={{
                            padding: '2px 8px',
                            borderRadius: '4px',
                            fontSize: '0.75rem',
                            fontWeight: '500',
                            backgroundColor: task.status === 'completed' ? '#dcfce7' :
                                          task.status === 'in-progress' ? '#fef3c7' : '#fee2e2',
                            color: task.status === 'completed' ? '#16a34a' :
                                   task.status === 'in-progress' ? '#d97706' : '#dc2626'
                          }}>
                            {task.status}
                          </span>
                        </div>

                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}>
                          <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Progress:</span>
                          <div style={{
                            flex: 1,
                            height: '6px',
                            backgroundColor: '#e2e8f0',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              width: `${task.progress}%`,
                              height: '100%',
                              backgroundColor: task.progress === 100 ? '#16a34a' :
                                             task.progress > 50 ? '#3b82f6' :
                                             task.progress > 0 ? '#f59e0b' : '#dc2626',
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                          <span style={{ fontSize: '0.75rem', color: '#475569', minWidth: '40px' }}>
                            {task.progress}%
                          </span>
                        </div>

                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}>
                          <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Due:</span>
                          <span style={{ fontSize: '0.875rem', color: '#0f172a' }}>
                            {new Date(task.dueDate).toLocaleDateString()}
                          </span>
                        </div>

                        <div style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          gap: '8px'
                        }}>
                          <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Assigned to:</span>
                          <div style={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: '4px'
                          }}>
                            {isLoadingUsers ? (
                              <span style={{
                                fontSize: '0.75rem',
                                padding: '2px 6px',
                                borderRadius: '4px',
                                backgroundColor: '#f1f5f9',
                                color: '#475569'
                              }}>
                                Loading...
                              </span>
                            ) : task.assignedTo.length === 0 ? (
                              <span style={{
                                fontSize: '0.75rem',
                                padding: '2px 6px',
                                borderRadius: '4px',
                                backgroundColor: '#f1f5f9',
                                color: '#475569'
                              }}>
                                Unassigned
                              </span>
                            ) : (
                              <>
                                {task.assignedTo.slice(0, 2).map(userId => (
                                  <span
                                    key={userId}
                                    style={{
                                      fontSize: '0.75rem',
                                      padding: '2px 6px',
                                      borderRadius: '4px',
                                      backgroundColor: '#f1f5f9',
                                      color: '#475569'
                                    }}
                                  >
                                    {userNames[userId] || 'Unknown User'}
                                  </span>
                                ))}
                                {task.assignedTo.length > 2 && (
                                  <span
                                    style={{
                                      fontSize: '0.75rem',
                                      padding: '2px 6px',
                                      borderRadius: '4px',
                                      backgroundColor: '#e2e8f0',
                                      color: '#475569',
                                      cursor: 'pointer'
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const remainingMembers = task.assignedTo.slice(2).map(userId => userNames[userId] || 'Unknown User');
                                      alert(`Additional team members:\n${remainingMembers.join('\n')}`);
                                    }}
                                  >
                                    +{task.assignedTo.length - 2} more
                                  </span>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Checklist preview - only show first 3 items */}
                      {task.checklist && task.checklist.length > 0 && (
                        <div style={{
                          marginTop: '8px',
                          padding: '8px',
                          backgroundColor: '#f8fafc',
                          borderRadius: '6px'
                        }}>
                          <div style={{
                            fontSize: '0.875rem',
                            color: '#64748b',
                            marginBottom: '4px'
                          }}>
                            Checklist ({task.checklist.filter(item => item.completed).length}/{task.checklist.length})
                          </div>
                          <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '4px'
                          }}>
                            {task.checklist.slice(0, 3).map((item, index) => (
                              <div
                                key={index}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '8px',
                                  fontSize: '0.875rem',
                                  color: item.completed ? '#64748b' : '#0f172a',
                                  textDecoration: item.completed ? 'line-through' : 'none'
                                }}
                              >
                                <input
                                  type="checkbox"
                                  checked={item.completed}
                                  disabled
                                  style={{
                                    width: '16px',
                                    height: '16px',
                                    borderRadius: '4px',
                                    border: '1px solid #e2e8f0'
                                  }}
                                />
                                <span style={{
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: '200px'
                                }}>
                                  {item.text}
                                </span>
                              </div>
                            ))}
                            {task.checklist.length > 3 && (
                              <div style={{
                                fontSize: '0.75rem',
                                color: '#64748b',
                                textAlign: 'center',
                                marginTop: '4px'
                              }}>
                                +{task.checklist.length - 3} more items...
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      <div style={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        gap: '8px',
                        marginTop: 'auto',
                        paddingTop: '12px',
                        borderTop: '1px solid #e2e8f0'
                      }}>
                        <button 
                          className="btn btn-secondary" 
                          style={{ 
                            padding: '6px 12px',
                            fontSize: '0.875rem'
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditTask(task);
                          }}
                        >
                          Edit
                        </button>
                        <button 
                          className="btn btn-secondary"
                          style={{ 
                            padding: '6px 12px',
                            fontSize: '0.875rem'
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTask(task._id);
                          }}
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p style={{ textAlign: 'center', color: '#475569' }}>
                  {searchQuery || filterStatus !== 'all' || filterPriority !== 'all'
                    ? 'No tasks match your filters'
                    : 'No tasks found. Create your first task!'}
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

const TeamPage = () => {
  const [selectedMember, setSelectedMember] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshKey, setRefreshKey] = useState(0);
  const { memberId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const { teamMembers, tasks, isLoading } = useTracker(() => {
    const handle = Meteor.subscribe('teamMembers');
    const tasksHandle = Meteor.subscribe('tasks');
    const isLoading = !handle.ready() || !tasksHandle.ready();
    
    const members = Meteor.users.find(
      { 
        $or: [
          { 'roles': 'team-member' },
          { 'profile.role': 'team-member' }
        ]
      },
      { 
        fields: { 
          emails: 1, 
          roles: 1, 
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        },
        sort: { 'profile.firstName': 1, 'profile.lastName': 1 }
      }
    ).fetch();

    const tasks = Tasks.find().fetch();

    return {
      teamMembers: members,
      tasks,
      isLoading
    };
  }, [refreshKey]);

  // Set selected member when memberId changes
  useEffect(() => {
    if (memberId && teamMembers) {
      const member = teamMembers.find(m => m._id === memberId);
      if (member) {
        setSelectedMember(member);
      }
    } else {
      setSelectedMember(null);
    }
  }, [memberId, teamMembers]);

  const handleMemberClick = (member) => {
    setSelectedMember(member);
  };

  const handleBackToList = () => {
    setSelectedMember(null);
  };

  if (selectedMember) {
    return (
      <div style={{ 
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }}>
        <div style={{
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '24px',
            flexWrap: 'wrap',
            gap: '16px'
          }}>
              <button
                onClick={() => {
                  if (location.state?.from) {
                    navigate(location.state.from);
                  } else {
                    setSelectedMember(null);
                    navigate('/admin-dashboard/team');
                  }
                }}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '1.8rem',
                color: '#64748b',
                transition: 'color 0.2s ease',
                marginRight: '12px',
                padding: 0,
                display: 'flex',
                alignItems: 'center'
              }}
              onMouseOver={e => e.currentTarget.style.color = '#334155'}
              onMouseOut={e => e.currentTarget.style.color = '#64748b'}
              aria-label="Back"
            >
              ←
              </button>
              <h2 style={{ 
                color: '#0f172a',
                margin: 0,
                fontSize: '1.5rem',
              fontWeight: '600',
              display: 'inline-block',
              wordBreak: 'break-word'
              }}>
                {selectedMember.profile?.firstName} {selectedMember.profile?.lastName}
              </h2>
          </div>

          <div style={{ marginBottom: '24px' }}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '24px',
              padding: '24px',
              backgroundColor: '#f8fafc',
              borderRadius: '12px',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
            }}>
              {/* Basic Information */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '24px'
              }}>
                <div style={{
                  padding: '16px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <h3 style={{ 
                    fontSize: '0.875rem', 
                    color: '#64748b', 
                    marginBottom: '8px',
                    fontWeight: '500'
                  }}>Email</h3>
                  <p style={{ 
                    fontSize: '1rem', 
                    color: '#0f172a',
                    fontWeight: '500'
                  }}>{selectedMember.emails?.[0]?.address}</p>
                </div>

                <div style={{
                  padding: '16px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <h3 style={{ 
                    fontSize: '0.875rem', 
                    color: '#64748b', 
                    marginBottom: '8px',
                    fontWeight: '500'
                  }}>Role</h3>
                  <p style={{ 
                    fontSize: '1rem', 
                    color: '#0f172a',
                    fontWeight: '500'
                  }}>{selectedMember.profile?.role || 'Team Member'}</p>
                </div>

                <div style={{
                  padding: '16px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <h3 style={{ 
                    fontSize: '0.875rem', 
                    color: '#64748b', 
                    marginBottom: '8px',
                    fontWeight: '500'
                  }}>Joined</h3>
                  <p style={{ 
                    fontSize: '1rem', 
                    color: '#0f172a',
                    fontWeight: '500'
                  }}>
                    {selectedMember.createdAt 
                      ? new Date(selectedMember.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                      : 'Not specified'}
                  </p>
                </div>
              </div>

              {/* Task Statistics */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '24px'
              }}>
                <div style={{
                  padding: '20px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <h3 style={{ 
                    fontSize: '1rem', 
                    color: '#0f172a', 
                    marginBottom: '16px',
                    fontWeight: '600'
                  }}>Task Status</h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Completed</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#dcfce7',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>{tasks.filter(task => task.status === 'completed' && task.assignedTo.includes(selectedMember._id)).length}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>In Progress</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#fef3c7',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>{tasks.filter(task => task.status === 'in-progress' && task.assignedTo.includes(selectedMember._id)).length}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Pending</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#fee2e2',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>{tasks.filter(task => task.status === 'pending' && task.assignedTo.includes(selectedMember._id)).length}</span>
                    </div>
                  </div>
                </div>

                <div style={{
                  padding: '20px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <h3 style={{ 
                    fontSize: '1rem', 
                    color: '#0f172a', 
                    marginBottom: '16px',
                    fontWeight: '600'
                  }}>Task Priority</h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>High Priority</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#fee2e2',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>{tasks.filter(task => task.priority === 'high' && task.assignedTo.includes(selectedMember._id)).length}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Medium Priority</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#fef3c7',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>{tasks.filter(task => task.priority === 'medium' && task.assignedTo.includes(selectedMember._id)).length}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Low Priority</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#dcfce7',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>{tasks.filter(task => task.priority === 'low' && task.assignedTo.includes(selectedMember._id)).length}</span>
                    </div>
                  </div>
                </div>

                <div style={{
                  padding: '20px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <h3 style={{ 
                    fontSize: '1rem', 
                    color: '#0f172a', 
                    marginBottom: '16px',
                    fontWeight: '600'
                  }}>Performance</h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Completion Rate</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#f1f5f9',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>
                        {(() => {
                          const assignedTasks = tasks.filter(task => task.assignedTo.includes(selectedMember._id));
                          if (assignedTasks.length === 0) return '0.0%';
                          const completedTasks = assignedTasks.filter(task => task.status === 'completed').length;
                          return `${((completedTasks / assignedTasks.length) * 100).toFixed(1)}%`;
                        })()}
                      </span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>On-Time Rate</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#f1f5f9',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>
                        {(() => {
                          const assignedTasks = tasks.filter(task => task.assignedTo.includes(selectedMember._id));
                          if (assignedTasks.length === 0) return '0.0%';
                          const onTimeTasks = assignedTasks.filter(task => {
                            if (task.status !== 'completed') return false;
                            return new Date(task.completedAt) <= new Date(task.dueDate);
                          }).length;
                          return `${((onTimeTasks / assignedTasks.length) * 100).toFixed(1)}%`;
                        })()}
                      </span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Average Progress</span>
                      <span style={{ 
                        fontSize: '1rem', 
                        color: '#0f172a',
                        fontWeight: '600',
                        backgroundColor: '#f1f5f9',
                        padding: '4px 12px',
                        borderRadius: '6px'
                      }}>
                        {(() => {
                          const assignedTasks = tasks.filter(task => task.assignedTo.includes(selectedMember._id));
                          if (assignedTasks.length === 0) return '0.0%';
                          const totalProgress = assignedTasks.reduce((sum, task) => sum + (task.progress || 0), 0);
                          return `${((totalProgress / assignedTasks.length)).toFixed(1)}%`;
                        })()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <TaskAssignmentHistory memberId={selectedMember._id} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '24px',
      background: '#ffffff',
      minHeight: 'calc(100vh - 64px)'
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <h2 style={{ 
            color: '#0f172a',
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600'
          }}>Team Management</h2>
        </div>

        {successMessage && (
          <div style={{
            backgroundColor: 'rgba(22, 163, 74, 0.1)',
            color: '#15803d',
            padding: '12px 16px',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            {successMessage}
          </div>
        )}

        <div style={{
          display: 'flex',
          gap: '16px',
          marginBottom: '24px',
          flexWrap: 'wrap'
        }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <input
              type="text"
              placeholder="Search team members..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e2e8f0',
                fontSize: '0.875rem'
              }}
            />
          </div>
        </div>

        {isLoading ? (
          <div style={{ textAlign: 'center', padding: '24px' }}>
            <div className="loading-spinner"></div>
          </div>
        ) : teamMembers.length > 0 ? (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '24px'
          }}>
            {teamMembers.map(member => (
              <div
                key={member._id}
                onClick={() => handleMemberClick(member)}
                style={{
                  backgroundColor: '#ffffff',
                  borderRadius: '12px',
                  padding: '20px',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #e2e8f0',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  ':hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                  }
                }}
              >
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start'
                }}>
                  <div>
                    <h3 style={{
                      margin: 0,
                      fontSize: '1.1rem',
                      fontWeight: '600',
                      color: '#0f172a'
                    }}>
                      {member.profile?.firstName} {member.profile?.lastName}
                    </h3>
                    <p style={{
                      margin: '4px 0 0',
                      fontSize: '0.875rem',
                      color: '#64748b'
                    }}>
                      {member.emails?.[0]?.address}
                    </p>
                  </div>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  <span style={{
                    padding: '4px 8px',
                    borderRadius: '6px',
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    backgroundColor: '#f1f5f9',
                    color: '#475569'
                  }}>
                    {member.profile?.role || 'Team Member'}
                  </span>
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '8px'
                }}>
                  {member.profile?.department && (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Department:</span>
                      <span style={{ fontSize: '0.875rem', color: '#0f172a' }}>{member.profile.department}</span>
                    </div>
                  )}
                  
                  {member.profile?.skills && member.profile.skills.length > 0 && (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      flexWrap: 'wrap'
                    }}>
                      <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Skills:</span>
                      {member.profile.skills.map((skill, index) => (
                        <span key={index} style={{
                          fontSize: '0.75rem',
                          padding: '2px 6px',
                          borderRadius: '4px',
                          backgroundColor: '#e2e8f0',
                          color: '#475569'
                        }}>
                          {skill}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  {/* Joined date has been removed */}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p style={{ textAlign: 'center', color: '#475569' }}>
            {searchQuery
              ? 'No team members match your search'
              : 'No team members found.'}
          </p>
        )}
      </div>
    </div>
  );
};

export const AdminDashboard = () => {
  const navigate = useNavigate();

  const handleLogout = () => {
    Meteor.logout(err => {
      if (err) {
        console.error('Logout error:', err);
      } else {
        navigate('/login');
      }
    });
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <Header userRole="admin" />

      <Routes>
        <Route path="/" element={<AdminDashboardHome />} />
        <Route path="/tasks" element={<TasksPage />} />
        <Route path="/tasks/:taskId" element={<TasksPage />} />
        <Route path="/team" element={<TeamPage />} />
        <Route path="/team/:memberId" element={<TeamPage />} />
        <Route path="/reports" element={<ReportsPage />} />
      </Routes>
    </div>
  );
}; 

