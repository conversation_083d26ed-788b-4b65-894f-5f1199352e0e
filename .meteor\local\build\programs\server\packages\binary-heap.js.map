{"version": 3, "sources": ["meteor://💻app/packages/binary-heap/binary-heap.js", "meteor://💻app/packages/binary-heap/max-heap.js", "meteor://💻app/packages/binary-heap/min-heap.js", "meteor://💻app/packages/binary-heap/min-max-heap.js"], "names": ["module", "link", "MaxHeap", "MinHeap", "MinMaxHeap", "__reifyWaitForDeps__", "__reify_async_result__", "_reifyError", "self", "async", "export", "constructor", "comparator", "options", "arguments", "length", "undefined", "Error", "_comparator", "IdMap", "_heapIdx", "_heap", "Array", "isArray", "initData", "_initFromData", "data", "map", "_ref", "id", "value", "for<PERSON>ach", "_ref2", "i", "set", "parentIdx", "_downHeap", "idx", "leftChildIdx", "size", "left", "right", "rightChildIdx", "largest", "_maxIndex", "_swap", "_upHeap", "parent", "idxA", "idxB", "valueA", "_get", "valueB", "recA", "recB", "get", "has", "push", "remove", "last", "pop", "empty", "clear", "iterator", "obj", "<PERSON><PERSON><PERSON><PERSON>", "def", "clone", "maxElementId", "_selfCheck", "concat", "v", "a", "b", "minElementId", "_minHeap"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACC,OAAO,EAAC;IAAS,CAAC,EAAC,CAAC,CAAC;IAACF,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACE,OAAO,EAAC;IAAS,CAAC,EAAC,CAAC,CAAC;IAACH,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;MAACG,UAAU,EAAC;IAAY,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;ACAhOT,MAAM,CAACU,MAAM,CAAC;EAACR,OAAO,EAACA,CAAA,KAAIA;AAAO,CAAC,CAAC;AAU7B,MAAMA,OAAO,CAAC;EACnBS,WAAWA,CAACC,UAAU,EAAgB;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAClC,IAAI,OAAOF,UAAU,KAAK,UAAU,EAAE;MACpC,MAAM,IAAIK,KAAK,CAAC,+DAA+D,CAAC;IAClF;;IAEA;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAGN,UAAU;IAE7B,IAAI,CAAEC,OAAO,CAACM,KAAK,EAAE;MACnBN,OAAO,CAACM,KAAK,GAAGA,KAAK;IACvB;;IAEA;IACA;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIP,OAAO,CAACM,KAAK,CAAD,CAAC;;IAEjC;IACA;IACA;IACA;IACA,IAAI,CAACE,KAAK,GAAG,EAAE;;IAEf;IACA;IACA;IACA,IAAIC,KAAK,CAACC,OAAO,CAACV,OAAO,CAACW,QAAQ,CAAC,EAAE;MACnC,IAAI,CAACC,aAAa,CAACZ,OAAO,CAACW,QAAQ,CAAC;IACtC;EACF;;EAEA;EACAC,aAAaA,CAACC,IAAI,EAAE;IAClB,IAAI,CAACL,KAAK,GAAGK,IAAI,CAACC,GAAG,CAACC,IAAA;MAAA,IAAC;QAAEC,EAAE;QAAEC;MAAM,CAAC,GAAAF,IAAA;MAAA,OAAM;QAAEC,EAAE;QAAEC;MAAM,CAAC;IAAA,CAAC,CAAC;IAEzDJ,IAAI,CAACK,OAAO,CAAC,CAAAC,KAAA,EAASC,CAAC;MAAA,IAAT;QAAEJ;MAAG,CAAC,GAAAG,KAAA;MAAA,OAAQ,IAAI,CAACZ,QAAQ,CAACc,GAAG,CAACL,EAAE,EAAEI,CAAC,CAAC;IAAA,EAAC;IAErD,IAAI,CAAEP,IAAI,CAACX,MAAM,EAAE;MACjB;IACF;;IAEA;IACA,KAAK,IAAIkB,CAAC,GAAGE,SAAS,CAACT,IAAI,CAACX,MAAM,GAAG,CAAC,CAAC,EAAEkB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpD,IAAI,CAACG,SAAS,CAACH,CAAC,CAAC;IACnB;EACF;EAEAG,SAASA,CAACC,GAAG,EAAE;IACb,OAAOC,YAAY,CAACD,GAAG,CAAC,GAAG,IAAI,CAACE,IAAI,CAAC,CAAC,EAAE;MACtC,MAAMC,IAAI,GAAGF,YAAY,CAACD,GAAG,CAAC;MAC9B,MAAMI,KAAK,GAAGC,aAAa,CAACL,GAAG,CAAC;MAChC,IAAIM,OAAO,GAAGN,GAAG;MAEjB,IAAIG,IAAI,GAAG,IAAI,CAACD,IAAI,CAAC,CAAC,EAAE;QACtBI,OAAO,GAAG,IAAI,CAACC,SAAS,CAACD,OAAO,EAAEH,IAAI,CAAC;MACzC;MAEA,IAAIC,KAAK,GAAG,IAAI,CAACF,IAAI,CAAC,CAAC,EAAE;QACvBI,OAAO,GAAG,IAAI,CAACC,SAAS,CAACD,OAAO,EAAEF,KAAK,CAAC;MAC1C;MAEA,IAAIE,OAAO,KAAKN,GAAG,EAAE;QACnB;MACF;MAEA,IAAI,CAACQ,KAAK,CAACF,OAAO,EAAEN,GAAG,CAAC;MACxBA,GAAG,GAAGM,OAAO;IACf;EACF;EAEAG,OAAOA,CAACT,GAAG,EAAE;IACX,OAAOA,GAAG,GAAG,CAAC,EAAE;MACd,MAAMU,MAAM,GAAGZ,SAAS,CAACE,GAAG,CAAC;MAC7B,IAAI,IAAI,CAACO,SAAS,CAACG,MAAM,EAAEV,GAAG,CAAC,KAAKA,GAAG,EAAE;QACvC,IAAI,CAACQ,KAAK,CAACE,MAAM,EAAEV,GAAG,CAAC;QACvBA,GAAG,GAAGU,MAAM;MACd,CAAC,MAAM;QACL;MACF;IACF;EACF;EAEAH,SAASA,CAACI,IAAI,EAAEC,IAAI,EAAE;IACpB,MAAMC,MAAM,GAAG,IAAI,CAACC,IAAI,CAACH,IAAI,CAAC;IAC9B,MAAMI,MAAM,GAAG,IAAI,CAACD,IAAI,CAACF,IAAI,CAAC;IAC9B,OAAO,IAAI,CAAC/B,WAAW,CAACgC,MAAM,EAAEE,MAAM,CAAC,IAAI,CAAC,GAAGJ,IAAI,GAAGC,IAAI;EAC5D;;EAEA;EACAE,IAAIA,CAACd,GAAG,EAAE;IACR,OAAO,IAAI,CAAChB,KAAK,CAACgB,GAAG,CAAC,CAACP,KAAK;EAC9B;EAEAe,KAAKA,CAACG,IAAI,EAAEC,IAAI,EAAE;IAChB,MAAMI,IAAI,GAAG,IAAI,CAAChC,KAAK,CAAC2B,IAAI,CAAC;IAC7B,MAAMM,IAAI,GAAG,IAAI,CAACjC,KAAK,CAAC4B,IAAI,CAAC;IAE7B,IAAI,CAAC7B,QAAQ,CAACc,GAAG,CAACmB,IAAI,CAACxB,EAAE,EAAEoB,IAAI,CAAC;IAChC,IAAI,CAAC7B,QAAQ,CAACc,GAAG,CAACoB,IAAI,CAACzB,EAAE,EAAEmB,IAAI,CAAC;IAEhC,IAAI,CAAC3B,KAAK,CAAC2B,IAAI,CAAC,GAAGM,IAAI;IACvB,IAAI,CAACjC,KAAK,CAAC4B,IAAI,CAAC,GAAGI,IAAI;EACzB;EAEAE,GAAGA,CAAC1B,EAAE,EAAE;IACN,OAAO,IAAI,CAAC2B,GAAG,CAAC3B,EAAE,CAAC,GACjB,IAAI,CAACsB,IAAI,CAAC,IAAI,CAAC/B,QAAQ,CAACmC,GAAG,CAAC1B,EAAE,CAAC,CAAC,GAChC,IAAI;EACR;EAEAK,GAAGA,CAACL,EAAE,EAAEC,KAAK,EAAE;IACb,IAAI,IAAI,CAAC0B,GAAG,CAAC3B,EAAE,CAAC,EAAE;MAChB,IAAI,IAAI,CAAC0B,GAAG,CAAC1B,EAAE,CAAC,KAAKC,KAAK,EAAE;QAC1B;MACF;MAEA,MAAMO,GAAG,GAAG,IAAI,CAACjB,QAAQ,CAACmC,GAAG,CAAC1B,EAAE,CAAC;MACjC,IAAI,CAACR,KAAK,CAACgB,GAAG,CAAC,CAACP,KAAK,GAAGA,KAAK;;MAE7B;MACA;MACA,IAAI,CAACgB,OAAO,CAACT,GAAG,CAAC;MACjB;MACA,IAAI,CAACD,SAAS,CAACC,GAAG,CAAC;IACrB,CAAC,MAAM;MACL,IAAI,CAACjB,QAAQ,CAACc,GAAG,CAACL,EAAE,EAAE,IAAI,CAACR,KAAK,CAACN,MAAM,CAAC;MACxC,IAAI,CAACM,KAAK,CAACoC,IAAI,CAAC;QAAE5B,EAAE;QAAEC;MAAM,CAAC,CAAC;MAC9B,IAAI,CAACgB,OAAO,CAAC,IAAI,CAACzB,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;IACrC;EACF;EAEA2C,MAAMA,CAAC7B,EAAE,EAAE;IACT,IAAI,IAAI,CAAC2B,GAAG,CAAC3B,EAAE,CAAC,EAAE;MAChB,MAAM8B,IAAI,GAAG,IAAI,CAACtC,KAAK,CAACN,MAAM,GAAG,CAAC;MAClC,MAAMsB,GAAG,GAAG,IAAI,CAACjB,QAAQ,CAACmC,GAAG,CAAC1B,EAAE,CAAC;MAEjC,IAAIQ,GAAG,KAAKsB,IAAI,EAAE;QAChB,IAAI,CAACd,KAAK,CAACR,GAAG,EAAEsB,IAAI,CAAC;QACrB,IAAI,CAACtC,KAAK,CAACuC,GAAG,CAAC,CAAC;QAChB,IAAI,CAACxC,QAAQ,CAACsC,MAAM,CAAC7B,EAAE,CAAC;;QAExB;QACA,IAAI,CAACiB,OAAO,CAACT,GAAG,CAAC;QACjB,IAAI,CAACD,SAAS,CAACC,GAAG,CAAC;MACrB,CAAC,MAAM;QACL,IAAI,CAAChB,KAAK,CAACuC,GAAG,CAAC,CAAC;QAChB,IAAI,CAACxC,QAAQ,CAACsC,MAAM,CAAC7B,EAAE,CAAC;MAC1B;IACF;EACF;EAEA2B,GAAGA,CAAC3B,EAAE,EAAE;IACN,OAAO,IAAI,CAACT,QAAQ,CAACoC,GAAG,CAAC3B,EAAE,CAAC;EAC9B;EAEAgC,KAAKA,CAAA,EAAG;IACN,OAAO,CAAC,IAAI,CAACtB,IAAI,CAAC,CAAC;EACrB;EAEAuB,KAAKA,CAAA,EAAG;IACN,IAAI,CAACzC,KAAK,GAAG,EAAE;IACf,IAAI,CAACD,QAAQ,CAAC0C,KAAK,CAAC,CAAC;EACvB;;EAEA;EACA/B,OAAOA,CAACgC,QAAQ,EAAE;IAChB,IAAI,CAAC1C,KAAK,CAACU,OAAO,CAACiC,GAAG,IAAID,QAAQ,CAACC,GAAG,CAAClC,KAAK,EAAEkC,GAAG,CAACnC,EAAE,CAAC,CAAC;EACxD;EAEAU,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAAClB,KAAK,CAACN,MAAM;EAC1B;EAEAkD,UAAUA,CAACpC,EAAE,EAAEqC,GAAG,EAAE;IAClB,IAAI,IAAI,CAACV,GAAG,CAAC3B,EAAE,CAAC,EAAE;MAChB,OAAO,IAAI,CAAC0B,GAAG,CAAC1B,EAAE,CAAC;IACrB;IAEA,IAAI,CAACK,GAAG,CAACL,EAAE,EAAEqC,GAAG,CAAC;IACjB,OAAOA,GAAG;EACZ;EAEAC,KAAKA,CAAA,EAAG;IACN,MAAMA,KAAK,GAAG,IAAIjE,OAAO,CAAC,IAAI,CAACgB,WAAW,EAAE,IAAI,CAACG,KAAK,CAAC;IACvD,OAAO8C,KAAK;EACd;EAEAC,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC7B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAClB,KAAK,CAAC,CAAC,CAAC,CAACQ,EAAE,GAAG,IAAI;EAC9C;EAEAwC,UAAUA,CAAA,EAAG;IACX,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACN,MAAM,EAAEkB,CAAC,EAAE,EAAE;MAC1C,IAAI,IAAI,CAACW,SAAS,CAACT,SAAS,CAACF,CAAC,CAAC,EAAEA,CAAC,CAAC,KAAKE,SAAS,CAACF,CAAC,CAAC,EAAE;QAClD,MAAM,IAAIhB,KAAK,CAAC,mBAAAqD,MAAA,CAAmB,IAAI,CAACjD,KAAK,CAACY,CAAC,CAAC,CAACJ,EAAE,IACnC,iCAAiC,GACjC,IAAI,CAACR,KAAK,CAACc,SAAS,CAACF,CAAC,CAAC,CAAC,CAACJ,EAAE,CAAC;MAChD;IACF;EACF;AACF;AAEA,MAAMS,YAAY,GAAGL,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC;AACnC,MAAMS,aAAa,GAAGT,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC;AACpC,MAAME,SAAS,GAAGF,CAAC,IAAKA,CAAC,GAAG,CAAC,IAAK,CAAC,C;;;;;;;;;;;;;;ICxNnCjC,MAAM,CAACU,MAAM,CAAC;MAACP,OAAO,EAACA,CAAA,KAAIA;IAAO,CAAC,CAAC;IAAC,IAAID,OAAO;IAACF,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACC,OAAOA,CAACqE,CAAC,EAAC;QAACrE,OAAO,GAACqE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIlE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAE7J,MAAMF,OAAO,SAASD,OAAO,CAAC;MACnCS,WAAWA,CAACC,UAAU,EAAEC,OAAO,EAAE;QAC/B,KAAK,CAAC,CAAC2D,CAAC,EAAEC,CAAC,KAAK,CAAC7D,UAAU,CAAC4D,CAAC,EAAEC,CAAC,CAAC,EAAE5D,OAAO,CAAC;MAC7C;MAEAuD,YAAYA,CAAA,EAAG;QACb,MAAM,IAAInD,KAAK,CAAC,qCAAqC,CAAC;MACxD;MAEAyD,YAAYA,CAAA,EAAG;QACb,OAAO,KAAK,CAACN,YAAY,CAAC,CAAC;MAC7B;IACF;IAAC;IAAC9D,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICdFT,MAAM,CAACU,MAAM,CAAC;MAACN,UAAU,EAACA,CAAA,KAAIA;IAAU,CAAC,CAAC;IAAC,IAAIF,OAAO;IAACF,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACC,OAAOA,CAACqE,CAAC,EAAC;QAACrE,OAAO,GAACqE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIpE,OAAO;IAACH,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACE,OAAOA,CAACoE,CAAC,EAAC;QAACpE,OAAO,GAACoE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIlE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAetO,MAAMD,UAAU,SAASF,OAAO,CAAC;MACtCS,WAAWA,CAACC,UAAU,EAAEC,OAAO,EAAE;QAC/B,KAAK,CAACD,UAAU,EAAEC,OAAO,CAAC;QAC1B,IAAI,CAAC8D,QAAQ,GAAG,IAAIxE,OAAO,CAACS,UAAU,EAAEC,OAAO,CAAC;MAClD;MAEAqB,GAAGA,CAAA,EAAU;QACX,KAAK,CAACA,GAAG,CAAC,GAAApB,SAAO,CAAC;QAClB,IAAI,CAAC6D,QAAQ,CAACzC,GAAG,CAAC,GAAApB,SAAO,CAAC;MAC5B;MAEA4C,MAAMA,CAAA,EAAU;QACd,KAAK,CAACA,MAAM,CAAC,GAAA5C,SAAO,CAAC;QACrB,IAAI,CAAC6D,QAAQ,CAACjB,MAAM,CAAC,GAAA5C,SAAO,CAAC;MAC/B;MAEAgD,KAAKA,CAAA,EAAU;QACb,KAAK,CAACA,KAAK,CAAC,GAAAhD,SAAO,CAAC;QACpB,IAAI,CAAC6D,QAAQ,CAACb,KAAK,CAAC,GAAAhD,SAAO,CAAC;MAC9B;MAEAmD,UAAUA,CAAA,EAAU;QAClB,KAAK,CAACA,UAAU,CAAC,GAAAnD,SAAO,CAAC;QACzB,OAAO,IAAI,CAAC6D,QAAQ,CAACV,UAAU,CAAC,GAAAnD,SAAO,CAAC;MAC1C;MAEAqD,KAAKA,CAAA,EAAG;QACN,MAAMA,KAAK,GAAG,IAAI/D,UAAU,CAAC,IAAI,CAACc,WAAW,EAAE,IAAI,CAACG,KAAK,CAAC;QAC1D,OAAO8C,KAAK;MACd;MAEAO,YAAYA,CAAA,EAAG;QACb,OAAO,IAAI,CAACC,QAAQ,CAACD,YAAY,CAAC,CAAC;MACrC;IAEF;IAAC;IAACpE,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/binary-heap.js", "sourcesContent": ["export { MaxHeap } from './max-heap.js';\nexport { MinHeap } from './min-heap.js';\nexport { MinMaxHeap } from './min-max-heap.js';\n", "// Constructor of Heap\n// - comparator - Function - given two items returns a number\n// - options:\n//   - initData - Array - Optional - the initial data in a format:\n//        Object:\n//          - id - String - unique id of the item\n//          - value - Any - the data value\n//      each value is retained\n//   - IdMap - Constructor - Optional - custom IdMap class to store id->index\n//       mappings internally. Standard IdMap is used by default.\nexport class MaxHeap { \n  constructor(comparator, options = {}) {\n    if (typeof comparator !== 'function') {\n      throw new Error('Passed comparator is invalid, should be a comparison function');\n    }\n\n    // a C-style comparator that is given two values and returns a number,\n    // negative if the first value is less than the second, positive if the second\n    // value is greater than the first and zero if they are equal.\n    this._comparator = comparator;\n\n    if (! options.IdMap) {\n      options.IdMap = IdMap;\n    }\n\n    // _heapIdx maps an id to an index in the Heap array the corresponding value\n    // is located on.\n    this._heapIdx = new options.IdMap;\n\n    // The Heap data-structure implemented as a 0-based contiguous array where\n    // every item on index idx is a node in a complete binary tree. Every node can\n    // have children on indexes idx*2+1 and idx*2+2, except for the leaves. Every\n    // node has a parent on index (idx-1)/2;\n    this._heap = [];\n\n    // If the initial array is passed, we can build the heap in linear time\n    // complexity (O(N)) compared to linearithmic time complexity (O(nlogn)) if\n    // we push elements one by one.\n    if (Array.isArray(options.initData)) {\n      this._initFromData(options.initData);\n    }\n  }\n\n  // Builds a new heap in-place in linear time based on passed data\n  _initFromData(data) {\n    this._heap = data.map(({ id, value }) => ({ id, value }));\n\n    data.forEach(({ id }, i) => this._heapIdx.set(id, i));\n\n    if (! data.length) {\n      return;\n    }\n\n    // start from the first non-leaf - the parent of the last leaf\n    for (let i = parentIdx(data.length - 1); i >= 0; i--) {\n      this._downHeap(i);\n    }\n  }\n\n  _downHeap(idx) {\n    while (leftChildIdx(idx) < this.size()) {\n      const left = leftChildIdx(idx);\n      const right = rightChildIdx(idx);\n      let largest = idx;\n\n      if (left < this.size()) {\n        largest = this._maxIndex(largest, left);\n      }\n\n      if (right < this.size()) {\n        largest = this._maxIndex(largest, right);\n      }\n\n      if (largest === idx) {\n        break;\n      }\n\n      this._swap(largest, idx);\n      idx = largest;\n    }\n  }\n\n  _upHeap(idx) {\n    while (idx > 0) {\n      const parent = parentIdx(idx);\n      if (this._maxIndex(parent, idx) === idx) {\n        this._swap(parent, idx)\n        idx = parent;\n      } else {\n        break;\n      }\n    }\n  }\n\n  _maxIndex(idxA, idxB) {\n    const valueA = this._get(idxA);\n    const valueB = this._get(idxB);\n    return this._comparator(valueA, valueB) >= 0 ? idxA : idxB;\n  }\n\n  // Internal: gets raw data object placed on idxth place in heap\n  _get(idx) {\n    return this._heap[idx].value;\n  }\n\n  _swap(idxA, idxB) {\n    const recA = this._heap[idxA];\n    const recB = this._heap[idxB];\n\n    this._heapIdx.set(recA.id, idxB);\n    this._heapIdx.set(recB.id, idxA);\n\n    this._heap[idxA] = recB;\n    this._heap[idxB] = recA;\n  }\n\n  get(id) {\n    return this.has(id) ?\n      this._get(this._heapIdx.get(id)) :\n      null;\n  }\n\n  set(id, value) {\n    if (this.has(id)) {\n      if (this.get(id) === value) {\n        return;\n      }\n\n      const idx = this._heapIdx.get(id);\n      this._heap[idx].value = value;\n\n      // Fix the new value's position\n      // Either bubble new value up if it is greater than its parent\n      this._upHeap(idx);\n      // or bubble it down if it is smaller than one of its children\n      this._downHeap(idx);\n    } else {\n      this._heapIdx.set(id, this._heap.length);\n      this._heap.push({ id, value });\n      this._upHeap(this._heap.length - 1);\n    }\n  }\n\n  remove(id) {\n    if (this.has(id)) {\n      const last = this._heap.length - 1;\n      const idx = this._heapIdx.get(id);\n\n      if (idx !== last) {\n        this._swap(idx, last);\n        this._heap.pop();\n        this._heapIdx.remove(id);\n\n        // Fix the swapped value's position\n        this._upHeap(idx);\n        this._downHeap(idx);\n      } else {\n        this._heap.pop();\n        this._heapIdx.remove(id);\n      }\n    }\n  }\n\n  has(id) {\n    return this._heapIdx.has(id);\n  }\n\n  empty() {\n    return !this.size();\n  }\n\n  clear() {\n    this._heap = [];\n    this._heapIdx.clear();\n  }\n\n  // iterate over values in no particular order\n  forEach(iterator) {\n    this._heap.forEach(obj => iterator(obj.value, obj.id));\n  }\n\n  size() {\n    return this._heap.length;\n  }\n\n  setDefault(id, def) {\n    if (this.has(id)) {\n      return this.get(id);\n    }\n\n    this.set(id, def);\n    return def;\n  }\n\n  clone() {\n    const clone = new MaxHeap(this._comparator, this._heap);\n    return clone;\n  }\n\n  maxElementId() {\n    return this.size() ? this._heap[0].id : null;\n  }\n\n  _selfCheck() {\n    for (let i = 1; i < this._heap.length; i++) {\n      if (this._maxIndex(parentIdx(i), i) !== parentIdx(i)) {\n          throw new Error(`An item with id ${this._heap[i].id}` +\n                          \" has a parent younger than it: \" +\n                          this._heap[parentIdx(i)].id);\n      }\n    }\n  }\n}\n\nconst leftChildIdx = i => i * 2 + 1;\nconst rightChildIdx = i => i * 2 + 2;\nconst parentIdx = i => (i - 1) >> 1;\n", "import { MaxHeap } from './max-heap.js';\n\nexport class MinHeap extends MaxHeap {\n  constructor(comparator, options) {\n    super((a, b) => -comparator(a, b), options);\n  }\n\n  maxElementId() {\n    throw new Error(\"Cannot call maxElementId on MinHeap\");\n  }\n\n  minElementId() {\n    return super.maxElementId();\n  }\n};\n", "import { <PERSON><PERSON><PERSON><PERSON> } from './max-heap.js';\nimport { <PERSON>Heap } from './min-heap.js';\n\n// This implementation of Min/Max-Heap is just a subclass of Max-Heap\n// with a Min-Heap as an encapsulated property.\n//\n// Most of the operations are just proxy methods to call the same method on both\n// heaps.\n//\n// This implementation takes 2*N memory but is fairly simple to write and\n// understand. And the constant factor of a simple Heap is usually smaller\n// compared to other two-way priority queues like Min/Max Heaps\n// (http://www.cs.otago.ac.nz/staffpriv/mike/Papers/MinMaxHeaps/MinMaxHeaps.pdf)\n// and Interval Heaps\n// (http://www.cise.ufl.edu/~sahni/dsaac/enrich/c13/double.htm)\nexport class MinMaxHeap extends MaxHeap {\n  constructor(comparator, options) {\n    super(comparator, options);\n    this._minHeap = new MinHeap(comparator, options);\n  }\n\n  set(...args) {\n    super.set(...args);\n    this._minHeap.set(...args);\n  }\n\n  remove(...args) {\n    super.remove(...args);\n    this._minHeap.remove(...args);\n  }\n\n  clear(...args) {\n    super.clear(...args);\n    this._minHeap.clear(...args);\n  }\n\n  setDefault(...args) {\n    super.setDefault(...args);\n    return this._minHeap.setDefault(...args);\n  }\n\n  clone() {\n    const clone = new MinMaxHeap(this._comparator, this._heap);\n    return clone;\n  }\n\n  minElementId() {\n    return this._minHeap.minElementId();\n  }\n\n};\n"]}