{"version": 3, "names": ["fs", "require", "path", "createHash", "<PERSON><PERSON><PERSON>", "module", "constructor", "exports", "enable", "cachePath", "createLoader", "arguments", "length", "undefined", "cacheEnabled", "cacheEntries", "Object", "create", "readdirSync", "for<PERSON>ach", "name", "e", "code", "mkdirSync", "Mp", "prototype", "resolve", "id", "_resolveFilename", "moduleLoad", "load", "filename", "result", "apply", "runSetters", "resolved", "Promise", "dynamicImport", "then", "reifyVersion", "version", "reifyBabelParse", "parse", "reifyCompile", "compile", "compileContent", "content", "identical", "generateLetDeclarations", "ast", "_compile", "options", "compiledWithReify", "call", "jsExt", "_extensions", "js", "stat", "statSync", "baseKey", "update", "concat", "mtimeMs", "ino", "size", "digest", "<PERSON><PERSON><PERSON>", "key", "readFileSync", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writeFileLater", "immediateTimer", "pendingWrites", "setImmediate", "keys", "targetPath", "temp<PERSON>ath", "writeFileSync", "renameSync", "err"], "sources": ["/tools/static-assets/server/runtime.js"], "sourcesContent": ["const fs = require('fs');\nconst path = require('path');\nconst { createHash } = require(\"crypto\");\nconst Module = module.constructor;\n\nmodule.exports = function enable ({ cachePath, createLoader = true } = {}) {\n  let cacheEnabled = !!cachePath;\n  let cacheEntries = Object.create(null);\n\n  if (cachePath) {\n    try {\n      fs.readdirSync(cachePath).forEach(name => {\n        cacheEntries[name] = true;\n      });\n    } catch (e) {\n      if (e.code === 'ENOENT') {\n        fs.mkdirSync(cachePath);\n      } else {\n        cacheEnabled = false;\n      }\n    }\n  }\n\n  const Mp = Module.prototype;\n\n  Mp.resolve = function (id) {\n    return Module._resolveFilename(id, this);\n  };\n\n  // Enable the module.{watch,export,...} runtime API needed by Reify.\n  require(\"@meteorjs/reify/lib/runtime\").enable(Mp);\n\n  const moduleLoad = Mp.load;\n  Mp.load = function (filename) {\n    const result = moduleLoad.apply(this, arguments);\n    if (typeof this.runSetters === \"function\") {\n      // Make sure we call module.runSetters (or module.runModuleSetters, a\n      // legacy synonym) whenever a module finishes loading.\n      this.runSetters();\n    }\n    return result;\n  };\n\n  const resolved = Promise.resolve();\n  Mp.dynamicImport = function (id) {\n    return resolved.then(() => require(id));\n  };\n\n  const reifyVersion = require(\"@meteorjs/reify/package.json\").version;\n  const reifyBabelParse = require(\"@meteorjs/reify/lib/parsers/babel\").parse;\n  const reifyCompile = require(\"@meteorjs/reify/lib/compiler\").compile;\n\n  function compileContent (content) {\n    let identical = true;\n\n    try {\n      const result = reifyCompile(content, {\n        parse: reifyBabelParse,\n        generateLetDeclarations: false,\n        ast: false,\n      });\n      if (!result.identical) {\n        identical = false;\n        content = result.code;\n      }\n    } finally {\n      return { content, identical };\n    }\n  }\n\n  const _compile = Mp._compile;\n  Mp._compile = function (content, filename, options) {\n    // When cache is enabled, the file has already been compiled\n    if (!options || !options.compiledWithReify) {\n      content = compileContent(content).content;\n    }\n\n    return _compile.call(this, content, filename);\n  };\n\n  if (cacheEnabled) {\n    const jsExt = Module._extensions.js;\n    Module._extensions['.js'] = function (module, filename) {\n      let stat = fs.statSync(filename);\n      let baseKey = createHash(\"sha1\")\n        .update(`${reifyVersion}\\0${filename}\\0${stat.mtimeMs}\\0${stat.ino}\\0${stat.size}\\0`)\n        .digest('hex');\n\n      // When files don't use import/export, there is no reason to store\n      // an identical copy of the file in the cache. Instead, it stores an empty\n      // file with a different suffix to indicate the original file should be used\n      let identicalKey = baseKey + '-identical.json';\n      let key = baseKey + '.json';\n\n      let content;\n      if (cacheEntries[key]) {\n        content = fs.readFileSync(path.join(cachePath, key), 'utf8');\n      } else if (cacheEntries[identicalKey]) {\n        content = fs.readFileSync(filename, 'utf8');\n      } else {\n        let origContent = fs.readFileSync(filename, 'utf8');\n        let result = compileContent(origContent);\n        content = result.content;\n\n        if (result.identical) {\n          writeFileLater(identicalKey, '');\n        } else {\n          writeFileLater(key, content);\n        }\n      }\n\n      return module._compile(content, filename, { compiledWithReify: true });\n    }\n  }\n\n  let immediateTimer = null;\n  let pendingWrites = Object.create(null);\n  function writeFileLater(key, content) {\n    pendingWrites[key] = content;\n    if (immediateTimer !== null) {\n      return;\n    }\n\n    immediateTimer = setImmediate(() => {\n      immediateTimer = null;\n      Object.keys(pendingWrites).forEach(key => {\n        try {\n          let targetPath = path.resolve(cachePath, key);\n          let tempPath = targetPath + '.tmp';\n          fs.writeFileSync(tempPath, pendingWrites[key]);\n          fs.renameSync(tempPath, targetPath);\n        } catch (err) {\n        }\n      });\n\n      pendingWrites = Object.create(null);\n    });\n  }\n}\n"], "mappings": ";EAAA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;EACxB,MAAMC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;EAC5B,MAAM;IAAEE;EAAW,CAAC,GAAGF,OAAO,CAAC,QAAQ,CAAC;EACxC,MAAMG,MAAM,GAAGC,MAAM,CAACC,WAAW;EAEjCD,MAAM,CAACE,OAAO,GAAG,SAASC,MAAMA,CAAA,EAA2C;IAAA,IAAzC;MAAEC,SAAS;MAAEC,YAAY,GAAG;IAAK,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACvE,IAAIG,YAAY,GAAG,CAAC,CAACL,SAAS;IAC9B,IAAIM,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAEtC,IAAIR,SAAS,EAAE;MACb,IAAI;QACFT,EAAE,CAACkB,WAAW,CAACT,SAAS,CAAC,CAACU,OAAO,CAACC,IAAI,IAAI;UACxCL,YAAY,CAACK,IAAI,CAAC,GAAG,IAAI;QAC3B,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,IAAIA,CAAC,CAACC,IAAI,KAAK,QAAQ,EAAE;UACvBtB,EAAE,CAACuB,SAAS,CAACd,SAAS,CAAC;QACzB,CAAC,MAAM;UACLK,YAAY,GAAG,KAAK;QACtB;MACF;IACF;IAEA,MAAMU,EAAE,GAAGpB,MAAM,CAACqB,SAAS;IAE3BD,EAAE,CAACE,OAAO,GAAG,UAAUC,EAAE,EAAE;MACzB,OAAOvB,MAAM,CAACwB,gBAAgB,CAACD,EAAE,EAAE,IAAI,CAAC;IAC1C,CAAC;;IAED;IACA1B,OAAO,CAAC,6BAA6B,CAAC,CAACO,MAAM,CAACgB,EAAE,CAAC;IAEjD,MAAMK,UAAU,GAAGL,EAAE,CAACM,IAAI;IAC1BN,EAAE,CAACM,IAAI,GAAG,UAAUC,QAAQ,EAAE;MAC5B,MAAMC,MAAM,GAAGH,UAAU,CAACI,KAAK,CAAC,IAAI,EAAEtB,SAAS,CAAC;MAChD,IAAI,OAAO,IAAI,CAACuB,UAAU,KAAK,UAAU,EAAE;QACzC;QACA;QACA,IAAI,CAACA,UAAU,CAAC,CAAC;MACnB;MACA,OAAOF,MAAM;IACf,CAAC;IAED,MAAMG,QAAQ,GAAGC,OAAO,CAACV,OAAO,CAAC,CAAC;IAClCF,EAAE,CAACa,aAAa,GAAG,UAAUV,EAAE,EAAE;MAC/B,OAAOQ,QAAQ,CAACG,IAAI,CAAC,MAAMrC,OAAO,CAAC0B,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,MAAMY,YAAY,GAAGtC,OAAO,CAAC,8BAA8B,CAAC,CAACuC,OAAO;IACpE,MAAMC,eAAe,GAAGxC,OAAO,CAAC,mCAAmC,CAAC,CAACyC,KAAK;IAC1E,MAAMC,YAAY,GAAG1C,OAAO,CAAC,8BAA8B,CAAC,CAAC2C,OAAO;IAEpE,SAASC,cAAcA,CAAEC,OAAO,EAAE;MAChC,IAAIC,SAAS,GAAG,IAAI;MAEpB,IAAI;QACF,MAAMf,MAAM,GAAGW,YAAY,CAACG,OAAO,EAAE;UACnCJ,KAAK,EAAED,eAAe;UACtBO,uBAAuB,EAAE,KAAK;UAC9BC,GAAG,EAAE;QACP,CAAC,CAAC;QACF,IAAI,CAACjB,MAAM,CAACe,SAAS,EAAE;UACrBA,SAAS,GAAG,KAAK;UACjBD,OAAO,GAAGd,MAAM,CAACV,IAAI;QACvB;MACF,CAAC,SAAS;QACR,OAAO;UAAEwB,OAAO;UAAEC;QAAU,CAAC;MAC/B;IACF;IAEA,MAAMG,QAAQ,GAAG1B,EAAE,CAAC0B,QAAQ;IAC5B1B,EAAE,CAAC0B,QAAQ,GAAG,UAAUJ,OAAO,EAAEf,QAAQ,EAAEoB,OAAO,EAAE;MAClD;MACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACC,iBAAiB,EAAE;QAC1CN,OAAO,GAAGD,cAAc,CAACC,OAAO,CAAC,CAACA,OAAO;MAC3C;MAEA,OAAOI,QAAQ,CAACG,IAAI,CAAC,IAAI,EAAEP,OAAO,EAAEf,QAAQ,CAAC;IAC/C,CAAC;IAED,IAAIjB,YAAY,EAAE;MAChB,MAAMwC,KAAK,GAAGlD,MAAM,CAACmD,WAAW,CAACC,EAAE;MACnCpD,MAAM,CAACmD,WAAW,CAAC,KAAK,CAAC,GAAG,UAAUlD,MAAM,EAAE0B,QAAQ,EAAE;QACtD,IAAI0B,IAAI,GAAGzD,EAAE,CAAC0D,QAAQ,CAAC3B,QAAQ,CAAC;QAChC,IAAI4B,OAAO,GAAGxD,UAAU,CAAC,MAAM,CAAC,CAC7ByD,MAAM,IAAAC,MAAA,CAAItB,YAAY,QAAAsB,MAAA,CAAK9B,QAAQ,QAAA8B,MAAA,CAAKJ,IAAI,CAACK,OAAO,QAAAD,MAAA,CAAKJ,IAAI,CAACM,GAAG,QAAAF,MAAA,CAAKJ,IAAI,CAACO,IAAI,OAAI,CAAC,CACpFC,MAAM,CAAC,KAAK,CAAC;;QAEhB;QACA;QACA;QACA,IAAIC,YAAY,GAAGP,OAAO,GAAG,iBAAiB;QAC9C,IAAIQ,GAAG,GAAGR,OAAO,GAAG,OAAO;QAE3B,IAAIb,OAAO;QACX,IAAI/B,YAAY,CAACoD,GAAG,CAAC,EAAE;UACrBrB,OAAO,GAAG9C,EAAE,CAACoE,YAAY,CAAClE,IAAI,CAACmE,IAAI,CAAC5D,SAAS,EAAE0D,GAAG,CAAC,EAAE,MAAM,CAAC;QAC9D,CAAC,MAAM,IAAIpD,YAAY,CAACmD,YAAY,CAAC,EAAE;UACrCpB,OAAO,GAAG9C,EAAE,CAACoE,YAAY,CAACrC,QAAQ,EAAE,MAAM,CAAC;QAC7C,CAAC,MAAM;UACL,IAAIuC,WAAW,GAAGtE,EAAE,CAACoE,YAAY,CAACrC,QAAQ,EAAE,MAAM,CAAC;UACnD,IAAIC,MAAM,GAAGa,cAAc,CAACyB,WAAW,CAAC;UACxCxB,OAAO,GAAGd,MAAM,CAACc,OAAO;UAExB,IAAId,MAAM,CAACe,SAAS,EAAE;YACpBwB,cAAc,CAACL,YAAY,EAAE,EAAE,CAAC;UAClC,CAAC,MAAM;YACLK,cAAc,CAACJ,GAAG,EAAErB,OAAO,CAAC;UAC9B;QACF;QAEA,OAAOzC,MAAM,CAAC6C,QAAQ,CAACJ,OAAO,EAAEf,QAAQ,EAAE;UAAEqB,iBAAiB,EAAE;QAAK,CAAC,CAAC;MACxE,CAAC;IACH;IAEA,IAAIoB,cAAc,GAAG,IAAI;IACzB,IAAIC,aAAa,GAAGzD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACvC,SAASsD,cAAcA,CAACJ,GAAG,EAAErB,OAAO,EAAE;MACpC2B,aAAa,CAACN,GAAG,CAAC,GAAGrB,OAAO;MAC5B,IAAI0B,cAAc,KAAK,IAAI,EAAE;QAC3B;MACF;MAEAA,cAAc,GAAGE,YAAY,CAAC,MAAM;QAClCF,cAAc,GAAG,IAAI;QACrBxD,MAAM,CAAC2D,IAAI,CAACF,aAAa,CAAC,CAACtD,OAAO,CAACgD,GAAG,IAAI;UACxC,IAAI;YACF,IAAIS,UAAU,GAAG1E,IAAI,CAACwB,OAAO,CAACjB,SAAS,EAAE0D,GAAG,CAAC;YAC7C,IAAIU,QAAQ,GAAGD,UAAU,GAAG,MAAM;YAClC5E,EAAE,CAAC8E,aAAa,CAACD,QAAQ,EAAEJ,aAAa,CAACN,GAAG,CAAC,CAAC;YAC9CnE,EAAE,CAAC+E,UAAU,CAACF,QAAQ,EAAED,UAAU,CAAC;UACrC,CAAC,CAAC,OAAOI,GAAG,EAAE,CACd;QACF,CAAC,CAAC;QAEFP,aAAa,GAAGzD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AAAA,EAAAoC,IAAA,OAAAhD,MAAA", "ignoreList": [], "file": "tools/static-assets/server/runtime.js.map"}