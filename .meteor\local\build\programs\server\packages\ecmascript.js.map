{"version": 3, "sources": ["meteor://💻app/packages/ecmascript/ecmascript.js"], "names": ["ECMAScript", "compileForShell", "Error"], "mappings": ";;;;;;;;;;;;;;;;;;AAAAA,UAAU,GAAG;EACXC,eAAeA,CAAA,EAAG;IAChB,MAAM,IAAIC,KAAK,CAAC,gGAAgG,CAAC;EACnH;AACF,CAAC,C", "file": "/packages/ecmascript.js", "sourcesContent": ["ECMAScript = {\n  compileForShell() {\n    throw new Error('compileForShell was removed in Meteor 3. Use Babel.compileForShell instead from babel-compiler');\n  }\n};\n"]}