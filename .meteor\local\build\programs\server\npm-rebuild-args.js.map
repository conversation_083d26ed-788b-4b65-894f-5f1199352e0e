{"version": 3, "names": ["args", "flags", "process", "env", "METEOR_NPM_REBUILD_FLAGS", "split", "for<PERSON>ach", "flag", "push", "exports", "get", "slice"], "sources": ["/tools/static-assets/server/npm-rebuild-args.js"], "sourcesContent": ["// Command-line arguments passed to npm when rebuilding binary packages.\nvar args = [\n  \"rebuild\",\n\n  // The --update-binary flag tells node-pre-gyp to replace previously\n  // installed local binaries with remote binaries:\n  // https://github.com/mapbox/node-pre-gyp#options\n  \"--update-binary\"\n];\n\n// Allow additional flags to be passed via the $METEOR_NPM_REBUILD_FLAGS\n// environment variable.\nvar flags = process.env.METEOR_NPM_REBUILD_FLAGS;\nif (flags) {\n  args = [\"rebuild\"];\n  flags.split(/\\s+/g).forEach(function (flag) {\n    if (flag) {\n      args.push(flag);\n    }\n  });\n}\n\nexports.get = function () {\n  // Make a defensive copy.\n  return args.slice(0);\n};\n"], "mappings": "AAAA;AACA,IAAIA,IAAI,GAAG,CACT,SAAS;AAET;AACA;AACA;AACA,iBAAiB,CAClB;;AAED;AACA;AACA,IAAIC,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,wBAAwB;AAChD,IAAIH,KAAK,EAAE;EACTD,IAAI,GAAG,CAAC,SAAS,CAAC;EAClBC,KAAK,CAACI,KAAK,CAAC,MAAM,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC1C,IAAIA,IAAI,EAAE;MACRP,IAAI,CAACQ,IAAI,CAACD,IAAI,CAAC;IACjB;EACF,CAAC,CAAC;AACJ;AAEAE,OAAO,CAACC,GAAG,GAAG,YAAY;EACxB;EACA,OAAOV,IAAI,CAACW,KAAK,CAAC,CAAC,CAAC;AACtB,CAAC", "ignoreList": [], "file": "tools/static-assets/server/npm-rebuild-args.js.map"}