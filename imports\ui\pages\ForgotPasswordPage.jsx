import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';
import { Link, useNavigate } from 'react-router-dom';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';

const ForgotPasswordSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  newPassword: Yup.string()
    .required('New password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .matches(/[0-9]/, 'Password must contain at least one number')
    .matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
    .required('Confirm password is required'),
});

export const ForgotPasswordPage = () => {
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const navigate = useNavigate();

  const handleSubmit = (values, { setSubmitting }) => {
    const { email, newPassword } = values;
    setError('');
    setSuccess(false);

    Meteor.call('users.forgotPassword', { email, newPassword }, (err, result) => {
      setSubmitting(false);
      if (err) {
        console.error('Forgot password error:', err);
        setError(err.reason || 'Failed to reset password. Please try again.');
      } else {
        setSuccess(true);
        console.log('Password reset successful:', result);
        // Redirect to login page after 2 seconds
        setTimeout(() => {
          navigate('/login');
        }, 2000);
      }
    });
  };

  const testLogin = (email, password) => {
    Meteor.call('users.testLogin', { email, password }, (err, result) => {
      if (err) {
        console.error('Test login error:', err);
        setTestResult({ success: false, error: err.reason });
      } else {
        console.log('Test login result:', result);
        setTestResult(result);
      }
    });
  };

  const debugUser = (email) => {
    Meteor.call('users.debugUser', { email }, (err, result) => {
      if (err) {
        console.error('Debug user error:', err);
        setTestResult({ success: false, error: err.reason });
      } else {
        console.log('Debug user result:', result);
        setTestResult(result);
      }
    });
  };

  const comparePasswordFormats = (email) => {
    Meteor.call('users.comparePasswordFormats', { email }, (err, result) => {
      if (err) {
        console.error('Compare password formats error:', err);
        setTestResult({ success: false, error: err.reason });
      } else {
        console.log('Compare password formats result:', result);
        setTestResult(result);
      }
    });
  };

  const testActualLogin = (email, password) => {
    Meteor.call('users.testActualLogin', { email, password }, (err, result) => {
      if (err) {
        console.error('Test actual login error:', err);
        setTestResult({ success: false, error: err.reason });
      } else {
        console.log('Test actual login result:', result);
        setTestResult(result);
      }
    });
  };

  const simulateLogin = (email, password) => {
    Meteor.call('users.simulateLogin', { email, password }, (err, result) => {
      if (err) {
        console.error('Simulate login error:', err);
        setTestResult({ success: false, error: err.reason });
      } else {
        console.log('Simulate login result:', result);
        setTestResult(result);
      }
    });
  };

  const simulateLogin = (email, password) => {
    Meteor.call('users.simulateLogin', { email, password }, (err, result) => {
      if (err) {
        console.error('Simulate login error:', err);
        setTestResult({ success: false, error: err.reason });
      } else {
        console.log('Simulate login result:', result);
        setTestResult(result);
      }
    });
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <div className="auth-logo">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <h1 className="auth-title">Reset Password</h1>
          <p className="auth-subtitle">Enter your email and new password to reset your account password.</p>
        </div>
        <Formik
          initialValues={{ email: '', newPassword: '', confirmPassword: '' }}
          validationSchema={ForgotPasswordSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="auth-form">
              <div className="form-group">
                <label htmlFor="email" className="form-label">Email Address</label>
                <Field
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}
                  disabled={isSubmitting}
                />
                {errors.email && touched.email && (
                  <div className="error-message">{errors.email}</div>
                )}
              </div>
              <div className="form-group">
                <label htmlFor="newPassword" className="form-label">New Password</label>
                <div className="form-control-password-wrapper">
                  <Field
                    id="newPassword"
                    name="newPassword"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter new password"
                    className={`form-control ${errors.newPassword && touched.newPassword ? 'form-control-error' : ''}`}
                    disabled={isSubmitting}
                  />
                  <button
                    type="button"
                    className="password-toggle-btn"
                    tabIndex={-1}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                    onClick={() => setShowPassword((v) => !v)}
                  >
                    {showPassword ? (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    ) : (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                  </button>
                </div>
                {errors.newPassword && touched.newPassword && (
                  <div className="error-message">{errors.newPassword}</div>
                )}
              </div>
              <div className="form-group">
                <label htmlFor="confirmPassword" className="form-label">Confirm New Password</label>
                <div className="form-control-password-wrapper">
                  <Field
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Confirm new password"
                    className={`form-control ${errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''}`}
                    disabled={isSubmitting}
                  />
                  <button
                    type="button"
                    className="password-toggle-btn"
                    tabIndex={-1}
                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                    onClick={() => setShowConfirmPassword((v) => !v)}
                  >
                    {showConfirmPassword ? (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    ) : (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                  </button>
                </div>
                {errors.confirmPassword && touched.confirmPassword && (
                  <div className="error-message">{errors.confirmPassword}</div>
                )}
              </div>
              {error && (
                <div className="error-message error-message-global">{error}</div>
              )}
              {success && (
                <div className="success-message">Password reset successful! Redirecting to login...</div>
              )}
              <button
                type="submit"
                className="btn btn-primary btn-auth"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading-spinner"></span>
                    Resetting...
                  </>
                ) : (
                  'Reset Password'
                )}
              </button>
              <div className="auth-links-row">
                <Link to="/login" className="auth-link">Back to login</Link>
              </div>
            </Form>
          )}
        </Formik>

        {/* Debug section */}
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
          <h4>Debug: Test Login</h4>
          <button
            onClick={() => {
              const email = document.querySelector('input[name="email"]').value;
              const password = document.querySelector('input[name="newPassword"]').value;
              if (email && password) {
                testLogin(email, password);
              }
            }}
            style={{ padding: '5px 10px', margin: '5px' }}
          >
            Test Password
          </button>
          <button
            onClick={() => {
              const email = document.querySelector('input[name="email"]').value;
              if (email) {
                debugUser(email);
              }
            }}
            style={{ padding: '5px 10px', margin: '5px' }}
          >
            Debug User
          </button>
          <button
            onClick={() => {
              const email = document.querySelector('input[name="email"]').value;
              if (email) {
                comparePasswordFormats(email);
              }
            }}
            style={{ padding: '5px 10px', margin: '5px' }}
          >
            Check Password Format
          </button>
          <button
            onClick={() => {
              const email = document.querySelector('input[name="email"]').value;
              const password = document.querySelector('input[name="newPassword"]').value;
              if (email && password) {
                testActualLogin(email, password);
              }
            }}
            style={{ padding: '5px 10px', margin: '5px' }}
          >
            Test Actual Login
          </button>
          <button
            onClick={() => {
              const email = document.querySelector('input[name="email"]').value;
              const password = document.querySelector('input[name="newPassword"]').value;
              if (email && password) {
                simulateLogin(email, password);
              }
            }}
            style={{ padding: '5px 10px', margin: '5px' }}
          >
            Simulate Login
          </button>
          {testResult && (
            <div style={{ marginTop: '10px', fontSize: '12px' }}>
              <strong>Test Result:</strong> <pre>{JSON.stringify(testResult, null, 2)}</pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 