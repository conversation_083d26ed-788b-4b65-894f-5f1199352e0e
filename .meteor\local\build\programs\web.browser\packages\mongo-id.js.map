{"version": 3, "sources": ["meteor://💻app/packages/mongo-id/id.js"], "names": ["module", "export", "MongoID", "EJSON", "link", "v", "Random", "_looksLikeObjectID", "str", "length", "test", "ObjectID", "constructor", "hexString", "toLowerCase", "Error", "_str", "equals", "other", "valueOf", "toString", "concat", "clone", "typeName", "getTimestamp", "Number", "parseInt", "substr", "toJSONValue", "toHexString", "addType", "idStringify", "id", "firstChar", "char<PERSON>t", "undefined", "JSON", "stringify", "idParse", "parse"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,OAAO,EAACA,CAAA,KAAIA;AAAO,CAAC,CAAC;AAAC,IAAIC,KAAK;AAACH,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;EAACD,KAAKA,CAACE,CAAC,EAAC;IAACF,KAAK,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACN,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;EAACE,MAAMA,CAACD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAGhK,MAAMH,OAAO,GAAG,CAAC,CAAC;AAElBA,OAAO,CAACK,kBAAkB,GAAGC,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,EAAE,IAAI,aAAa,CAACC,IAAI,CAACF,GAAG,CAAC;AAEhFN,OAAO,CAACS,QAAQ,GAAG,MAAMA,QAAQ,CAAC;EAChCC,WAAWA,CAAEC,SAAS,EAAE;IACtB;IACA,IAAIA,SAAS,EAAE;MACbA,SAAS,GAAGA,SAAS,CAACC,WAAW,CAAC,CAAC;MACnC,IAAI,CAACZ,OAAO,CAACK,kBAAkB,CAACM,SAAS,CAAC,EAAE;QAC1C,MAAM,IAAIE,KAAK,CAAC,qDAAqD,CAAC;MACxE;MACA;MACA,IAAI,CAACC,IAAI,GAAGH,SAAS;IACvB,CAAC,MAAM;MACL,IAAI,CAACG,IAAI,GAAGV,MAAM,CAACO,SAAS,CAAC,EAAE,CAAC;IAClC;EACF;EAEAI,MAAMA,CAACC,KAAK,EAAE;IACZ,OAAOA,KAAK,YAAYhB,OAAO,CAACS,QAAQ,IACxC,IAAI,CAACQ,OAAO,CAAC,CAAC,KAAKD,KAAK,CAACC,OAAO,CAAC,CAAC;EACpC;EAEAC,QAAQA,CAAA,EAAG;IACT,qBAAAC,MAAA,CAAoB,IAAI,CAACL,IAAI;EAC/B;EAEAM,KAAKA,CAAA,EAAG;IACN,OAAO,IAAIpB,OAAO,CAACS,QAAQ,CAAC,IAAI,CAACK,IAAI,CAAC;EACxC;EAEAO,QAAQA,CAAA,EAAG;IACT,OAAO,KAAK;EACd;EAEAC,YAAYA,CAAA,EAAG;IACb,OAAOC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAACV,IAAI,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACpD;EAEAR,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACH,IAAI;EAClB;EAEAY,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACT,OAAO,CAAC,CAAC;EACvB;EAEAU,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACV,OAAO,CAAC,CAAC;EACvB;AAEF,CAAC;AAEDhB,KAAK,CAAC2B,OAAO,CAAC,KAAK,EAAEtB,GAAG,IAAI,IAAIN,OAAO,CAACS,QAAQ,CAACH,GAAG,CAAC,CAAC;AAEtDN,OAAO,CAAC6B,WAAW,GAAIC,EAAE,IAAK;EAC5B,IAAIA,EAAE,YAAY9B,OAAO,CAACS,QAAQ,EAAE;IAClC,OAAOqB,EAAE,CAACb,OAAO,CAAC,CAAC;EACrB,CAAC,MAAM,IAAI,OAAOa,EAAE,KAAK,QAAQ,EAAE;IACjC,IAAIC,SAAS,GAAGD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;IAC5B,IAAIF,EAAE,KAAK,EAAE,EAAE;MACb,OAAOA,EAAE;IACX,CAAC,MAAM,IAAIC,SAAS,KAAK,GAAG;IAAI;IACrBA,SAAS,KAAK,GAAG;IAAI;IACrB/B,OAAO,CAACK,kBAAkB,CAACyB,EAAE,CAAC;IAAI;IAClCC,SAAS,KAAK,GAAG,EAAE;MAAE;MAC9B,WAAAZ,MAAA,CAAWW,EAAE;IACf,CAAC,MAAM;MACL,OAAOA,EAAE,CAAC,CAAC;IACb;EACF,CAAC,MAAM,IAAIA,EAAE,KAAKG,SAAS,EAAE;IAC3B,OAAO,GAAG;EACZ,CAAC,MAAM,IAAI,OAAOH,EAAE,KAAK,QAAQ,IAAIA,EAAE,KAAK,IAAI,EAAE;IAChD,MAAM,IAAIjB,KAAK,CAAC,sEAAsE,CAAC;EACzF,CAAC,MAAM;IAAE;IACP,WAAAM,MAAA,CAAWe,IAAI,CAACC,SAAS,CAACL,EAAE,CAAC;EAC/B;AACF,CAAC;AAED9B,OAAO,CAACoC,OAAO,GAAIN,EAAE,IAAK;EACxB,IAAIC,SAAS,GAAGD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;EAC5B,IAAIF,EAAE,KAAK,EAAE,EAAE;IACb,OAAOA,EAAE;EACX,CAAC,MAAM,IAAIA,EAAE,KAAK,GAAG,EAAE;IACrB,OAAOG,SAAS;EAClB,CAAC,MAAM,IAAIF,SAAS,KAAK,GAAG,EAAE;IAC5B,OAAOD,EAAE,CAACL,MAAM,CAAC,CAAC,CAAC;EACrB,CAAC,MAAM,IAAIM,SAAS,KAAK,GAAG,EAAE;IAC5B,OAAOG,IAAI,CAACG,KAAK,CAACP,EAAE,CAACL,MAAM,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,MAAM,IAAIzB,OAAO,CAACK,kBAAkB,CAACyB,EAAE,CAAC,EAAE;IACzC,OAAO,IAAI9B,OAAO,CAACS,QAAQ,CAACqB,EAAE,CAAC;EACjC,CAAC,MAAM;IACL,OAAOA,EAAE;EACX;AACF,CAAC,C", "file": "/packages/mongo-id.js", "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON> } from 'meteor/ejson';\nimport { Random } from 'meteor/random';\n\nconst MongoID = {};\n\nMongoID._looksLikeObjectID = str => str.length === 24 && /^[0-9a-f]*$/.test(str);\n\nMongoID.ObjectID = class ObjectID {\n  constructor (hexString) {\n    //random-based impl of Mongo ObjectID\n    if (hexString) {\n      hexString = hexString.toLowerCase();\n      if (!MongoID._looksLikeObjectID(hexString)) {\n        throw new Error('Invalid hexadecimal string for creating an ObjectID');\n      }\n      // meant to work with _.isEqual(), which relies on structural equality\n      this._str = hexString;\n    } else {\n      this._str = Random.hexString(24);\n    }\n  }\n\n  equals(other) {\n    return other instanceof MongoID.ObjectID &&\n    this.valueOf() === other.valueOf();\n  }\n\n  toString() {\n    return `ObjectID(\"${this._str}\")`;\n  }\n\n  clone() {\n    return new MongoID.ObjectID(this._str);\n  }\n\n  typeName() {\n    return 'oid';\n  }\n\n  getTimestamp() {\n    return Number.parseInt(this._str.substr(0, 8), 16);\n  }\n\n  valueOf() {\n    return this._str;\n  }\n\n  toJSONValue() {\n    return this.valueOf();\n  }\n\n  toHexString() {\n    return this.valueOf();\n  }\n\n}\n\nEJSON.addType('oid', str => new MongoID.ObjectID(str));\n\nMongoID.idStringify = (id) => {\n  if (id instanceof MongoID.ObjectID) {\n    return id.valueOf();\n  } else if (typeof id === 'string') {\n    var firstChar = id.charAt(0);\n    if (id === '') {\n      return id;\n    } else if (firstChar === '-' || // escape previously dashed strings\n               firstChar === '~' || // escape escaped numbers, true, false\n               MongoID._looksLikeObjectID(id) || // escape object-id-form strings\n               firstChar === '{') { // escape object-form strings, for maybe implementing later\n      return `-${id}`;\n    } else {\n      return id; // other strings go through unchanged.\n    }\n  } else if (id === undefined) {\n    return '-';\n  } else if (typeof id === 'object' && id !== null) {\n    throw new Error('Meteor does not currently support objects other than ObjectID as ids');\n  } else { // Numbers, true, false, null\n    return `~${JSON.stringify(id)}`;\n  }\n};\n\nMongoID.idParse = (id) => {\n  var firstChar = id.charAt(0);\n  if (id === '') {\n    return id;\n  } else if (id === '-') {\n    return undefined;\n  } else if (firstChar === '-') {\n    return id.substr(1);\n  } else if (firstChar === '~') {\n    return JSON.parse(id.substr(1));\n  } else if (MongoID._looksLikeObjectID(id)) {\n    return new MongoID.ObjectID(id);\n  } else {\n    return id;\n  }\n};\n\nexport { MongoID };\n"]}