{"format": "javascript-image-pre1", "arch": "os", "load": [{"path": "packages/core-runtime.js"}, {"node_modules": "npm/node_modules/meteor/meteor/node_modules", "path": "packages/meteor.js", "assets": {"meteor.d.ts": "assets/packages/meteor/meteor.d.ts"}}, {"path": "packages/meteor-base.js"}, {"path": "packages/mobile-experience.js"}, {"node_modules": "npm/node_modules/meteor/npm-mongo/node_modules", "path": "packages/npm-mongo.js", "assets": {"index.d.ts": "assets/packages/npm-mongo/index.d.ts"}}, {"node_modules": "npm/node_modules/meteor/modules-runtime/node_modules", "path": "packages/modules-runtime.js"}, {"path": "packages/modules-runtime-hot.js"}, {"node_modules": "npm/node_modules/meteor/modules/node_modules", "path": "packages/modules.js"}, {"node_modules": "npm/node_modules/meteor/es5-shim/node_modules", "path": "packages/es5-shim.js"}, {"path": "packages/modern-browsers.js", "assets": {"modern.d.ts": "assets/packages/modern-browsers/modern.d.ts"}}, {"node_modules": "npm/node_modules/meteor/promise/node_modules", "path": "packages/promise.js", "assets": {"promise.d.ts": "assets/packages/promise/promise.d.ts"}}, {"node_modules": "npm/node_modules/meteor/ecmascript-runtime-client/node_modules", "path": "packages/ecmascript-runtime-client.js"}, {"path": "packages/hot-module-replacement.js", "assets": {"hot-module-replacement.d.ts": "assets/packages/hot-module-replacement/hot-module-replacement.d.ts"}}, {"node_modules": "npm/node_modules/meteor/react-fast-refresh/node_modules", "path": "packages/react-fast-refresh.js"}, {"node_modules": "npm/node_modules/meteor/ecmascript/node_modules", "sourceMap": "packages/ecmascript.js.map", "path": "packages/ecmascript.js"}, {"node_modules": "npm/node_modules/meteor/ecmascript-runtime/node_modules", "path": "packages/ecmascript-runtime.js"}, {"path": "packages/babel-runtime.js"}, {"node_modules": "npm/node_modules/meteor/fetch/node_modules", "path": "packages/fetch.js", "assets": {"fetch.d.ts": "assets/packages/fetch/fetch.d.ts"}}, {"node_modules": "npm/node_modules/meteor/inter-process-messaging/node_modules", "path": "packages/inter-process-messaging.js"}, {"path": "packages/dynamic-import.js"}, {"node_modules": "npm/node_modules/meteor/ecmascript-runtime-server/node_modules", "path": "packages/ecmascript-runtime-server.js"}, {"sourceMap": "packages/base64.js.map", "path": "packages/base64.js"}, {"sourceMap": "packages/ejson.js.map", "path": "packages/ejson.js", "assets": {"ejson.d.ts": "assets/packages/ejson/ejson.d.ts"}}, {"sourceMap": "packages/diff-sequence.js.map", "path": "packages/diff-sequence.js"}, {"path": "packages/geojson-utils.js"}, {"sourceMap": "packages/id-map.js.map", "path": "packages/id-map.js"}, {"sourceMap": "packages/random.js.map", "path": "packages/random.js", "assets": {"random.d.ts": "assets/packages/random/random.d.ts"}}, {"sourceMap": "packages/mongo-id.js.map", "path": "packages/mongo-id.js"}, {"sourceMap": "packages/ordered-dict.js.map", "path": "packages/ordered-dict.js"}, {"sourceMap": "packages/tracker.js.map", "path": "packages/tracker.js", "assets": {"tracker.d.ts": "assets/packages/tracker/tracker.d.ts"}}, {"node_modules": "npm/node_modules/meteor/mongo-decimal/node_modules", "sourceMap": "packages/mongo-decimal.js.map", "path": "packages/mongo-decimal.js"}, {"sourceMap": "packages/minimongo.js.map", "path": "packages/minimongo.js"}, {"sourceMap": "packages/check.js.map", "path": "packages/check.js", "assets": {"check.d.ts": "assets/packages/check/check.d.ts"}}, {"sourceMap": "packages/retry.js.map", "path": "packages/retry.js"}, {"sourceMap": "packages/callback-hook.js.map", "path": "packages/callback-hook.js"}, {"sourceMap": "packages/ddp-common.js.map", "path": "packages/ddp-common.js"}, {"path": "packages/reload.js"}, {"node_modules": "npm/node_modules/meteor/socket-stream-client/node_modules", "sourceMap": "packages/socket-stream-client.js.map", "path": "packages/socket-stream-client.js"}, {"node_modules": "npm/node_modules/meteor/ddp-client/node_modules", "sourceMap": "packages/ddp-client.js.map", "path": "packages/ddp-client.js"}, {"node_modules": "npm/node_modules/meteor/babel-compiler/node_modules", "path": "packages/babel-compiler.js"}, {"path": "packages/typescript.js"}, {"sourceMap": "packages/rate-limit.js.map", "path": "packages/rate-limit.js"}, {"sourceMap": "packages/ddp-rate-limiter.js.map", "path": "packages/ddp-rate-limiter.js", "assets": {"ddp-rate-limiter.d.ts": "assets/packages/ddp-rate-limiter/ddp-rate-limiter.d.ts"}}, {"node_modules": "npm/node_modules/meteor/logging/node_modules", "sourceMap": "packages/logging.js.map", "path": "packages/logging.js", "assets": {"logging.d.ts": "assets/packages/logging/logging.d.ts"}}, {"sourceMap": "packages/routepolicy.js.map", "path": "packages/routepolicy.js"}, {"node_modules": "npm/node_modules/meteor/boilerplate-generator/node_modules", "sourceMap": "packages/boilerplate-generator.js.map", "path": "packages/boilerplate-generator.js"}, {"sourceMap": "packages/webapp-hashing.js.map", "path": "packages/webapp-hashing.js"}, {"node_modules": "npm/node_modules/meteor/webapp/node_modules", "sourceMap": "packages/webapp.js.map", "path": "packages/webapp.js", "assets": {"webapp.d.ts": "assets/packages/webapp/webapp.d.ts"}}, {"node_modules": "npm/node_modules/meteor/ddp-server/node_modules", "sourceMap": "packages/ddp-server.js.map", "path": "packages/ddp-server.js"}, {"path": "packages/ddp.js", "assets": {"ddp.d.ts": "assets/packages/ddp/ddp.d.ts"}}, {"sourceMap": "packages/allow-deny.js.map", "path": "packages/allow-deny.js"}, {"path": "packages/mongo-dev-server.js"}, {"sourceMap": "packages/binary-heap.js.map", "path": "packages/binary-heap.js"}, {"sourceMap": "packages/facts-base.js.map", "path": "packages/facts-base.js"}, {"node_modules": "npm/node_modules/meteor/mongo/node_modules", "sourceMap": "packages/mongo.js.map", "path": "packages/mongo.js", "assets": {"mongo.d.ts": "assets/packages/mongo/mongo.d.ts", "package-types.json": "assets/packages/mongo/package-types.json"}}, {"path": "packages/reactive-var.js", "assets": {"reactive-var.d.ts": "assets/packages/reactive-var/reactive-var.d.ts"}}, {"path": "packages/standard-minifier-js.js"}, {"sourceMap": "packages/shell-server.js.map", "path": "packages/shell-server.js"}, {"path": "packages/static-html.js"}, {"node_modules": "npm/node_modules/meteor/react-meteor-data/node_modules", "path": "packages/react-meteor-data.js", "assets": {"react-meteor-data.d.ts": "assets/packages/react-meteor-data/react-meteor-data.d.ts", "suspense/react-meteor-data.d.ts": "assets/packages/react-meteor-data/suspense/react-meteor-data.d.ts"}}, {"node_modules": "npm/node_modules/meteor/url/node_modules", "path": "packages/url.js"}, {"sourceMap": "packages/accounts-base.js.map", "path": "packages/accounts-base.js", "assets": {"accounts-base.d.ts": "assets/packages/accounts-base/accounts-base.d.ts"}}, {"path": "packages/sha.js"}, {"node_modules": "npm/node_modules/meteor/email/node_modules", "sourceMap": "packages/email.js.map", "path": "packages/email.js", "assets": {"email.d.ts": "assets/packages/email/email.d.ts"}}, {"node_modules": "npm/node_modules/meteor/accounts-password/node_modules", "sourceMap": "packages/accounts-password.js.map", "path": "packages/accounts-password.js"}, {"node_modules": "npm/node_modules/meteor/akryum_postcss/node_modules", "sourceMap": "packages/akryum_postcss.js.map", "path": "packages/akryum_postcss.js"}, {"path": "packages/zodern_types.js"}, {"sourceMap": "packages/alanning_roles.js.map", "path": "packages/alanning_roles.js"}, {"path": "packages/hot-code-push.js"}, {"path": "packages/launch-screen.js"}, {"sourceMap": "packages/autoupdate.js.map", "path": "packages/autoupdate.js"}, {"node_modules": "npm/node_modules", "sourceMap": "app/app.js.map", "path": "app/app.js"}]}