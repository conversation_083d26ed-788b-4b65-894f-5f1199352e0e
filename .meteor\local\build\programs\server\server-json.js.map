{"version": 3, "names": ["fs", "require", "path", "serverJsonPath", "resolve", "process", "argv", "module", "exports", "JSON", "parse", "readFileSync", "normalize", "call"], "sources": ["/tools/static-assets/server/server-json.js"], "sourcesContent": ["var fs = require(\"fs\");\nvar path = require(\"path\");\nvar serverJsonPath = path.resolve(process.argv[2]);\nmodule.exports = JSON.parse(\n  fs.readFileSync(serverJsonPath, 'utf8').normalize('NFC')\n);\n"], "mappings": ";EAAA,IAAIA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;EACtB,IAAIC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;EAC1B,IAAIE,cAAc,GAAGD,IAAI,CAACE,OAAO,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EAClDC,MAAM,CAACC,OAAO,GAAGC,IAAI,CAACC,KAAK,CACzBV,EAAE,CAACW,YAAY,CAACR,cAAc,EAAE,MAAM,CAAC,CAACS,SAAS,CAAC,KAAK,CACzD,CAAC;AAAC,EAAAC,IAAA,OAAAN,MAAA", "ignoreList": [], "file": "tools/static-assets/server/server-json.js.map"}