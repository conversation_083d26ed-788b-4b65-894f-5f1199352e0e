{"version": 3, "sources": ["meteor://💻app/packages/diff-sequence/diff.js"], "names": ["module", "export", "DiffSequence", "hasOwn", "Object", "prototype", "hasOwnProperty", "isObjEmpty", "obj", "key", "call", "diff<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ordered", "oldResults", "newResults", "observer", "options", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectionFn", "EJSON", "clone", "movedBefore", "Error", "for<PERSON>ach", "newDoc", "id", "oldDoc", "get", "changed", "equals", "projectedNew", "projectedOld", "changed<PERSON>ields", "make<PERSON><PERSON>edFields", "added", "fields", "_id", "removed", "has", "old_results", "new_results", "new_presence_of_id", "doc", "Meteor", "_debug", "old_index_of_id", "i", "unmoved", "max_seq_len", "N", "length", "seq_ends", "Array", "ptrs", "old_idx_seq", "i_new", "undefined", "j", "idx", "push", "reverse", "startOfGroup", "endOfGroup", "groupId", "addedBefore", "diffObjects", "left", "right", "callbacks", "keys", "leftValue", "both", "leftOnly", "rightOnly", "rightValue", "diffMaps", "value", "applyChanges", "changeFields"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,YAAY,EAACA,CAAA,KAAIA;AAAY,CAAC,CAAC;AAAvC,MAAMA,YAAY,GAAG,CAAC,CAAC;AAE9B,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,SAASC,UAAUA,CAACC,GAAG,EAAE;EACvB,KAAK,IAAIC,GAAG,IAAIL,MAAM,CAACI,GAAG,CAAC,EAAE;IAC3B,IAAIL,MAAM,CAACO,IAAI,CAACF,GAAG,EAAEC,GAAG,CAAC,EAAE;MACzB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACAP,YAAY,CAACS,gBAAgB,GAAG,UAAUC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAC3BC,QAAQ,EAAEC,OAAO,EAAE;EAC/D,IAAIJ,OAAO,EACTV,YAAY,CAACe,uBAAuB,CAClCJ,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAAC,KAE7Cd,YAAY,CAACgB,yBAAyB,CACpCL,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,CAAC;AAChD,CAAC;AAEDd,YAAY,CAACgB,yBAAyB,GAAG,UAAUL,UAAU,EAAEC,UAAU,EAClBC,QAAQ,EAAEC,OAAO,EAAE;EACxEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIG,YAAY,GAAGH,OAAO,CAACG,YAAY,IAAIC,KAAK,CAACC,KAAK;EAEtD,IAAIN,QAAQ,CAACO,WAAW,EAAE;IACxB,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EAEAT,UAAU,CAACU,OAAO,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;IACvC,IAAIC,MAAM,GAAGd,UAAU,CAACe,GAAG,CAACF,EAAE,CAAC;IAC/B,IAAIC,MAAM,EAAE;MACV,IAAIZ,QAAQ,CAACc,OAAO,IAAI,CAACT,KAAK,CAACU,MAAM,CAACH,MAAM,EAAEF,MAAM,CAAC,EAAE;QACrD,IAAIM,YAAY,GAAGZ,YAAY,CAACM,MAAM,CAAC;QACvC,IAAIO,YAAY,GAAGb,YAAY,CAACQ,MAAM,CAAC;QACvC,IAAIM,aAAa,GACX/B,YAAY,CAACgC,iBAAiB,CAACH,YAAY,EAAEC,YAAY,CAAC;QAChE,IAAI,CAAEzB,UAAU,CAAC0B,aAAa,CAAC,EAAE;UAC/BlB,QAAQ,CAACc,OAAO,CAACH,EAAE,EAAEO,aAAa,CAAC;QACrC;MACF;IACF,CAAC,MAAM,IAAIlB,QAAQ,CAACoB,KAAK,EAAE;MACzB,IAAIC,MAAM,GAAGjB,YAAY,CAACM,MAAM,CAAC;MACjC,OAAOW,MAAM,CAACC,GAAG;MACjBtB,QAAQ,CAACoB,KAAK,CAACV,MAAM,CAACY,GAAG,EAAED,MAAM,CAAC;IACpC;EACF,CAAC,CAAC;EAEF,IAAIrB,QAAQ,CAACuB,OAAO,EAAE;IACpBzB,UAAU,CAACW,OAAO,CAAC,UAAUG,MAAM,EAAED,EAAE,EAAE;MACvC,IAAI,CAACZ,UAAU,CAACyB,GAAG,CAACb,EAAE,CAAC,EACrBX,QAAQ,CAACuB,OAAO,CAACZ,EAAE,CAAC;IACxB,CAAC,CAAC;EACJ;AACF,CAAC;AAEDxB,YAAY,CAACe,uBAAuB,GAAG,UAAUuB,WAAW,EAAEC,WAAW,EACpB1B,QAAQ,EAAEC,OAAO,EAAE;EACtEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIG,YAAY,GAAGH,OAAO,CAACG,YAAY,IAAIC,KAAK,CAACC,KAAK;EAEtD,IAAIqB,kBAAkB,GAAG,CAAC,CAAC;EAC3BD,WAAW,CAACjB,OAAO,CAAC,UAAUmB,GAAG,EAAE;IACjC,IAAID,kBAAkB,CAACC,GAAG,CAACN,GAAG,CAAC,EAC7BO,MAAM,CAACC,MAAM,CAAC,8BAA8B,CAAC;IAC/CH,kBAAkB,CAACC,GAAG,CAACN,GAAG,CAAC,GAAG,IAAI;EACpC,CAAC,CAAC;EAEF,IAAIS,eAAe,GAAG,CAAC,CAAC;EACxBN,WAAW,CAAChB,OAAO,CAAC,UAAUmB,GAAG,EAAEI,CAAC,EAAE;IACpC,IAAIJ,GAAG,CAACN,GAAG,IAAIS,eAAe,EAC5BF,MAAM,CAACC,MAAM,CAAC,8BAA8B,CAAC;IAC/CC,eAAe,CAACH,GAAG,CAACN,GAAG,CAAC,GAAGU,CAAC;EAC9B,CAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,OAAO,GAAG,EAAE;EAChB;EACA,IAAIC,WAAW,GAAG,CAAC;EACnB;EACA;EACA,IAAIC,CAAC,GAAGT,WAAW,CAACU,MAAM;EAC1B,IAAIC,QAAQ,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC;EAC3B;EACA;EACA;EACA,IAAII,IAAI,GAAG,IAAID,KAAK,CAACH,CAAC,CAAC;EACvB;EACA,IAAIK,WAAW,GAAG,SAAAA,CAASC,KAAK,EAAE;IAChC,OAAOV,eAAe,CAACL,WAAW,CAACe,KAAK,CAAC,CAACnB,GAAG,CAAC;EAChD,CAAC;EACD;EACA;EACA,KAAI,IAAIU,CAAC,GAAC,CAAC,EAAEA,CAAC,GAACG,CAAC,EAAEH,CAAC,EAAE,EAAE;IACrB,IAAID,eAAe,CAACL,WAAW,CAACM,CAAC,CAAC,CAACV,GAAG,CAAC,KAAKoB,SAAS,EAAE;MACrD,IAAIC,CAAC,GAAGT,WAAW;MACnB;MACA;MACA;MACA;MACA;MACA,OAAOS,CAAC,GAAG,CAAC,EAAE;QACZ,IAAIH,WAAW,CAACH,QAAQ,CAACM,CAAC,GAAC,CAAC,CAAC,CAAC,GAAGH,WAAW,CAACR,CAAC,CAAC,EAC7C;QACFW,CAAC,EAAE;MACL;MAEAJ,IAAI,CAACP,CAAC,CAAC,GAAIW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGN,QAAQ,CAACM,CAAC,GAAC,CAAC,CAAE;MACxCN,QAAQ,CAACM,CAAC,CAAC,GAAGX,CAAC;MACf,IAAIW,CAAC,GAAC,CAAC,GAAGT,WAAW,EACnBA,WAAW,GAAGS,CAAC,GAAC,CAAC;IACrB;EACF;;EAEA;EACA,IAAIC,GAAG,GAAIV,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGG,QAAQ,CAACH,WAAW,GAAC,CAAC,CAAE;EAC5D,OAAOU,GAAG,IAAI,CAAC,EAAE;IACfX,OAAO,CAACY,IAAI,CAACD,GAAG,CAAC;IACjBA,GAAG,GAAGL,IAAI,CAACK,GAAG,CAAC;EACjB;EACA;EACAX,OAAO,CAACa,OAAO,CAAC,CAAC;;EAEjB;EACA;EACAb,OAAO,CAACY,IAAI,CAACnB,WAAW,CAACU,MAAM,CAAC;EAEhCX,WAAW,CAAChB,OAAO,CAAC,UAAUmB,GAAG,EAAE;IACjC,IAAI,CAACD,kBAAkB,CAACC,GAAG,CAACN,GAAG,CAAC,EAC9BtB,QAAQ,CAACuB,OAAO,IAAIvB,QAAQ,CAACuB,OAAO,CAACK,GAAG,CAACN,GAAG,CAAC;EACjD,CAAC,CAAC;;EAEF;EACA;EACA,IAAIyB,YAAY,GAAG,CAAC;EACpBd,OAAO,CAACxB,OAAO,CAAC,UAAUuC,UAAU,EAAE;IACpC,IAAIC,OAAO,GAAGvB,WAAW,CAACsB,UAAU,CAAC,GAAGtB,WAAW,CAACsB,UAAU,CAAC,CAAC1B,GAAG,GAAG,IAAI;IAC1E,IAAIV,MAAM,EAAEF,MAAM,EAAEW,MAAM,EAAEL,YAAY,EAAEC,YAAY;IACtD,KAAK,IAAIe,CAAC,GAAGe,YAAY,EAAEf,CAAC,GAAGgB,UAAU,EAAEhB,CAAC,EAAE,EAAE;MAC9CtB,MAAM,GAAGgB,WAAW,CAACM,CAAC,CAAC;MACvB,IAAI,CAAC5C,MAAM,CAACO,IAAI,CAACoC,eAAe,EAAErB,MAAM,CAACY,GAAG,CAAC,EAAE;QAC7CD,MAAM,GAAGjB,YAAY,CAACM,MAAM,CAAC;QAC7B,OAAOW,MAAM,CAACC,GAAG;QACjBtB,QAAQ,CAACkD,WAAW,IAAIlD,QAAQ,CAACkD,WAAW,CAACxC,MAAM,CAACY,GAAG,EAAED,MAAM,EAAE4B,OAAO,CAAC;QACzEjD,QAAQ,CAACoB,KAAK,IAAIpB,QAAQ,CAACoB,KAAK,CAACV,MAAM,CAACY,GAAG,EAAED,MAAM,CAAC;MACtD,CAAC,MAAM;QACL;QACAT,MAAM,GAAGa,WAAW,CAACM,eAAe,CAACrB,MAAM,CAACY,GAAG,CAAC,CAAC;QACjDN,YAAY,GAAGZ,YAAY,CAACM,MAAM,CAAC;QACnCO,YAAY,GAAGb,YAAY,CAACQ,MAAM,CAAC;QACnCS,MAAM,GAAGlC,YAAY,CAACgC,iBAAiB,CAACH,YAAY,EAAEC,YAAY,CAAC;QACnE,IAAI,CAACzB,UAAU,CAAC6B,MAAM,CAAC,EAAE;UACvBrB,QAAQ,CAACc,OAAO,IAAId,QAAQ,CAACc,OAAO,CAACJ,MAAM,CAACY,GAAG,EAAED,MAAM,CAAC;QAC1D;QACArB,QAAQ,CAACO,WAAW,IAAIP,QAAQ,CAACO,WAAW,CAACG,MAAM,CAACY,GAAG,EAAE2B,OAAO,CAAC;MACnE;IACF;IACA,IAAIA,OAAO,EAAE;MACXvC,MAAM,GAAGgB,WAAW,CAACsB,UAAU,CAAC;MAChCpC,MAAM,GAAGa,WAAW,CAACM,eAAe,CAACrB,MAAM,CAACY,GAAG,CAAC,CAAC;MACjDN,YAAY,GAAGZ,YAAY,CAACM,MAAM,CAAC;MACnCO,YAAY,GAAGb,YAAY,CAACQ,MAAM,CAAC;MACnCS,MAAM,GAAGlC,YAAY,CAACgC,iBAAiB,CAACH,YAAY,EAAEC,YAAY,CAAC;MACnE,IAAI,CAACzB,UAAU,CAAC6B,MAAM,CAAC,EAAE;QACvBrB,QAAQ,CAACc,OAAO,IAAId,QAAQ,CAACc,OAAO,CAACJ,MAAM,CAACY,GAAG,EAAED,MAAM,CAAC;MAC1D;IACF;IACA0B,YAAY,GAAGC,UAAU,GAAC,CAAC;EAC7B,CAAC,CAAC;AAGJ,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA7D,YAAY,CAACgE,WAAW,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC3DjE,MAAM,CAACkE,IAAI,CAACH,IAAI,CAAC,CAAC3C,OAAO,CAACf,GAAG,IAAI;IAC/B,MAAM8D,SAAS,GAAGJ,IAAI,CAAC1D,GAAG,CAAC;IAC3B,IAAIN,MAAM,CAACO,IAAI,CAAC0D,KAAK,EAAE3D,GAAG,CAAC,EAAE;MAC3B4D,SAAS,CAACG,IAAI,IAAIH,SAAS,CAACG,IAAI,CAAC/D,GAAG,EAAE8D,SAAS,EAAEH,KAAK,CAAC3D,GAAG,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL4D,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAChE,GAAG,EAAE8D,SAAS,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF,IAAIF,SAAS,CAACK,SAAS,EAAE;IACvBtE,MAAM,CAACkE,IAAI,CAACF,KAAK,CAAC,CAAC5C,OAAO,CAACf,GAAG,IAAI;MAChC,MAAMkE,UAAU,GAAGP,KAAK,CAAC3D,GAAG,CAAC;MAC7B,IAAI,CAAEN,MAAM,CAACO,IAAI,CAACyD,IAAI,EAAE1D,GAAG,CAAC,EAAE;QAC5B4D,SAAS,CAACK,SAAS,CAACjE,GAAG,EAAEkE,UAAU,CAAC;MACtC;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAEDzE,YAAY,CAAC0E,QAAQ,GAAG,UAAUT,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACxDF,IAAI,CAAC3C,OAAO,CAAC,UAAU+C,SAAS,EAAE9D,GAAG,EAAE;IACrC,IAAI2D,KAAK,CAAC7B,GAAG,CAAC9B,GAAG,CAAC,EAAC;MACjB4D,SAAS,CAACG,IAAI,IAAIH,SAAS,CAACG,IAAI,CAAC/D,GAAG,EAAE8D,SAAS,EAAEH,KAAK,CAACxC,GAAG,CAACnB,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL4D,SAAS,CAACI,QAAQ,IAAIJ,SAAS,CAACI,QAAQ,CAAChE,GAAG,EAAE8D,SAAS,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF,IAAIF,SAAS,CAACK,SAAS,EAAE;IACvBN,KAAK,CAAC5C,OAAO,CAAC,UAAUmD,UAAU,EAAElE,GAAG,EAAE;MACvC,IAAI,CAAC0D,IAAI,CAAC5B,GAAG,CAAC9B,GAAG,CAAC,EAAC;QACjB4D,SAAS,CAACK,SAAS,CAACjE,GAAG,EAAEkE,UAAU,CAAC;MACtC;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAGDzE,YAAY,CAACgC,iBAAiB,GAAG,UAAUT,MAAM,EAAEE,MAAM,EAAE;EACzD,IAAIS,MAAM,GAAG,CAAC,CAAC;EACflC,YAAY,CAACgE,WAAW,CAACvC,MAAM,EAAEF,MAAM,EAAE;IACvCgD,QAAQ,EAAE,SAAAA,CAAUhE,GAAG,EAAEoE,KAAK,EAAE;MAC9BzC,MAAM,CAAC3B,GAAG,CAAC,GAAGgD,SAAS;IACzB,CAAC;IACDiB,SAAS,EAAE,SAAAA,CAAUjE,GAAG,EAAEoE,KAAK,EAAE;MAC/BzC,MAAM,CAAC3B,GAAG,CAAC,GAAGoE,KAAK;IACrB,CAAC;IACDL,IAAI,EAAE,SAAAA,CAAU/D,GAAG,EAAE8D,SAAS,EAAEI,UAAU,EAAE;MAC1C,IAAI,CAACvD,KAAK,CAACU,MAAM,CAACyC,SAAS,EAAEI,UAAU,CAAC,EACtCvC,MAAM,CAAC3B,GAAG,CAAC,GAAGkE,UAAU;IAC5B;EACF,CAAC,CAAC;EACF,OAAOvC,MAAM;AACf,CAAC;AAEDlC,YAAY,CAAC4E,YAAY,GAAG,UAAUnC,GAAG,EAAEoC,YAAY,EAAE;EACvD3E,MAAM,CAACkE,IAAI,CAACS,YAAY,CAAC,CAACvD,OAAO,CAACf,GAAG,IAAI;IACvC,MAAMoE,KAAK,GAAGE,YAAY,CAACtE,GAAG,CAAC;IAC/B,IAAI,OAAOoE,KAAK,KAAK,WAAW,EAAE;MAChC,OAAOlC,GAAG,CAAClC,GAAG,CAAC;IACjB,CAAC,MAAM;MACLkC,GAAG,CAAClC,GAAG,CAAC,GAAGoE,KAAK;IAClB;EACF,CAAC,CAAC;AACJ,CAAC,C", "file": "/packages/diff-sequence.js", "sourcesContent": ["export const DiffSequence = {};\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nfunction isObjEmpty(obj) {\n  for (let key in Object(obj)) {\n    if (hasOwn.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// ordered: bool.\n// old_results and new_results: collections of documents.\n//    if ordered, they are arrays.\n//    if unordered, they are IdMaps\nDiffSequence.diffQueryChanges = function (ordered, oldResults, newResults,\n                                              observer, options) {\n  if (ordered)\n    DiffSequence.diffQueryOrderedChanges(\n      oldResults, newResults, observer, options);\n  else\n    DiffSequence.diffQueryUnorderedChanges(\n      oldResults, newResults, observer, options);\n};\n\nDiffSequence.diffQueryUnorderedChanges = function (oldResults, newResults,\n                                                       observer, options) {\n  options = options || {};\n  var projectionFn = options.projectionFn || EJSON.clone;\n\n  if (observer.movedBefore) {\n    throw new Error(\"_diffQueryUnordered called with a movedBefore observer!\");\n  }\n\n  newResults.forEach(function (newDoc, id) {\n    var oldDoc = oldResults.get(id);\n    if (oldDoc) {\n      if (observer.changed && !EJSON.equals(oldDoc, newDoc)) {\n        var projectedNew = projectionFn(newDoc);\n        var projectedOld = projectionFn(oldDoc);\n        var changedFields =\n              DiffSequence.makeChangedFields(projectedNew, projectedOld);\n        if (! isObjEmpty(changedFields)) {\n          observer.changed(id, changedFields);\n        }\n      }\n    } else if (observer.added) {\n      var fields = projectionFn(newDoc);\n      delete fields._id;\n      observer.added(newDoc._id, fields);\n    }\n  });\n\n  if (observer.removed) {\n    oldResults.forEach(function (oldDoc, id) {\n      if (!newResults.has(id))\n        observer.removed(id);\n    });\n  }\n};\n\nDiffSequence.diffQueryOrderedChanges = function (old_results, new_results,\n                                                     observer, options) {\n  options = options || {};\n  var projectionFn = options.projectionFn || EJSON.clone;\n\n  var new_presence_of_id = {};\n  new_results.forEach(function (doc) {\n    if (new_presence_of_id[doc._id])\n      Meteor._debug(\"Duplicate _id in new_results\");\n    new_presence_of_id[doc._id] = true;\n  });\n\n  var old_index_of_id = {};\n  old_results.forEach(function (doc, i) {\n    if (doc._id in old_index_of_id)\n      Meteor._debug(\"Duplicate _id in old_results\");\n    old_index_of_id[doc._id] = i;\n  });\n\n  // ALGORITHM:\n  //\n  // To determine which docs should be considered \"moved\" (and which\n  // merely change position because of other docs moving) we run\n  // a \"longest common subsequence\" (LCS) algorithm.  The LCS of the\n  // old doc IDs and the new doc IDs gives the docs that should NOT be\n  // considered moved.\n\n  // To actually call the appropriate callbacks to get from the old state to the\n  // new state:\n\n  // First, we call removed() on all the items that only appear in the old\n  // state.\n\n  // Then, once we have the items that should not move, we walk through the new\n  // results array group-by-group, where a \"group\" is a set of items that have\n  // moved, anchored on the end by an item that should not move.  One by one, we\n  // move each of those elements into place \"before\" the anchoring end-of-group\n  // item, and fire changed events on them if necessary.  Then we fire a changed\n  // event on the anchor, and move on to the next group.  There is always at\n  // least one group; the last group is anchored by a virtual \"null\" id at the\n  // end.\n\n  // Asymptotically: O(N k) where k is number of ops, or potentially\n  // O(N log N) if inner loop of LCS were made to be binary search.\n\n\n  //////// LCS (longest common sequence, with respect to _id)\n  // (see Wikipedia article on Longest Increasing Subsequence,\n  // where the LIS is taken of the sequence of old indices of the\n  // docs in new_results)\n  //\n  // unmoved: the output of the algorithm; members of the LCS,\n  // in the form of indices into new_results\n  var unmoved = [];\n  // max_seq_len: length of LCS found so far\n  var max_seq_len = 0;\n  // seq_ends[i]: the index into new_results of the last doc in a\n  // common subsequence of length of i+1 <= max_seq_len\n  var N = new_results.length;\n  var seq_ends = new Array(N);\n  // ptrs:  the common subsequence ending with new_results[n] extends\n  // a common subsequence ending with new_results[ptr[n]], unless\n  // ptr[n] is -1.\n  var ptrs = new Array(N);\n  // virtual sequence of old indices of new results\n  var old_idx_seq = function(i_new) {\n    return old_index_of_id[new_results[i_new]._id];\n  };\n  // for each item in new_results, use it to extend a common subsequence\n  // of length j <= max_seq_len\n  for(var i=0; i<N; i++) {\n    if (old_index_of_id[new_results[i]._id] !== undefined) {\n      var j = max_seq_len;\n      // this inner loop would traditionally be a binary search,\n      // but scanning backwards we will likely find a subseq to extend\n      // pretty soon, bounded for example by the total number of ops.\n      // If this were to be changed to a binary search, we'd still want\n      // to scan backwards a bit as an optimization.\n      while (j > 0) {\n        if (old_idx_seq(seq_ends[j-1]) < old_idx_seq(i))\n          break;\n        j--;\n      }\n\n      ptrs[i] = (j === 0 ? -1 : seq_ends[j-1]);\n      seq_ends[j] = i;\n      if (j+1 > max_seq_len)\n        max_seq_len = j+1;\n    }\n  }\n\n  // pull out the LCS/LIS into unmoved\n  var idx = (max_seq_len === 0 ? -1 : seq_ends[max_seq_len-1]);\n  while (idx >= 0) {\n    unmoved.push(idx);\n    idx = ptrs[idx];\n  }\n  // the unmoved item list is built backwards, so fix that\n  unmoved.reverse();\n\n  // the last group is always anchored by the end of the result list, which is\n  // an id of \"null\"\n  unmoved.push(new_results.length);\n\n  old_results.forEach(function (doc) {\n    if (!new_presence_of_id[doc._id])\n      observer.removed && observer.removed(doc._id);\n  });\n\n  // for each group of things in the new_results that is anchored by an unmoved\n  // element, iterate through the things before it.\n  var startOfGroup = 0;\n  unmoved.forEach(function (endOfGroup) {\n    var groupId = new_results[endOfGroup] ? new_results[endOfGroup]._id : null;\n    var oldDoc, newDoc, fields, projectedNew, projectedOld;\n    for (var i = startOfGroup; i < endOfGroup; i++) {\n      newDoc = new_results[i];\n      if (!hasOwn.call(old_index_of_id, newDoc._id)) {\n        fields = projectionFn(newDoc);\n        delete fields._id;\n        observer.addedBefore && observer.addedBefore(newDoc._id, fields, groupId);\n        observer.added && observer.added(newDoc._id, fields);\n      } else {\n        // moved\n        oldDoc = old_results[old_index_of_id[newDoc._id]];\n        projectedNew = projectionFn(newDoc);\n        projectedOld = projectionFn(oldDoc);\n        fields = DiffSequence.makeChangedFields(projectedNew, projectedOld);\n        if (!isObjEmpty(fields)) {\n          observer.changed && observer.changed(newDoc._id, fields);\n        }\n        observer.movedBefore && observer.movedBefore(newDoc._id, groupId);\n      }\n    }\n    if (groupId) {\n      newDoc = new_results[endOfGroup];\n      oldDoc = old_results[old_index_of_id[newDoc._id]];\n      projectedNew = projectionFn(newDoc);\n      projectedOld = projectionFn(oldDoc);\n      fields = DiffSequence.makeChangedFields(projectedNew, projectedOld);\n      if (!isObjEmpty(fields)) {\n        observer.changed && observer.changed(newDoc._id, fields);\n      }\n    }\n    startOfGroup = endOfGroup+1;\n  });\n\n\n};\n\n\n// General helper for diff-ing two objects.\n// callbacks is an object like so:\n// { leftOnly: function (key, leftValue) {...},\n//   rightOnly: function (key, rightValue) {...},\n//   both: function (key, leftValue, rightValue) {...},\n// }\nDiffSequence.diffObjects = function (left, right, callbacks) {\n  Object.keys(left).forEach(key => {\n    const leftValue = left[key];\n    if (hasOwn.call(right, key)) {\n      callbacks.both && callbacks.both(key, leftValue, right[key]);\n    } else {\n      callbacks.leftOnly && callbacks.leftOnly(key, leftValue);\n    }\n  });\n\n  if (callbacks.rightOnly) {\n    Object.keys(right).forEach(key => {\n      const rightValue = right[key];\n      if (! hasOwn.call(left, key)) {\n        callbacks.rightOnly(key, rightValue);\n      }\n    });\n  }\n};\n\nDiffSequence.diffMaps = function (left, right, callbacks) {\n  left.forEach(function (leftValue, key) {\n    if (right.has(key)){\n      callbacks.both && callbacks.both(key, leftValue, right.get(key));\n    } else {\n      callbacks.leftOnly && callbacks.leftOnly(key, leftValue);\n    }\n  });\n\n  if (callbacks.rightOnly) {\n    right.forEach(function (rightValue, key) {\n      if (!left.has(key)){\n        callbacks.rightOnly(key, rightValue);\n      }\n    });\n  }\n};\n\n\nDiffSequence.makeChangedFields = function (newDoc, oldDoc) {\n  var fields = {};\n  DiffSequence.diffObjects(oldDoc, newDoc, {\n    leftOnly: function (key, value) {\n      fields[key] = undefined;\n    },\n    rightOnly: function (key, value) {\n      fields[key] = value;\n    },\n    both: function (key, leftValue, rightValue) {\n      if (!EJSON.equals(leftValue, rightValue))\n        fields[key] = rightValue;\n    }\n  });\n  return fields;\n};\n\nDiffSequence.applyChanges = function (doc, changeFields) {\n  Object.keys(changeFields).forEach(key => {\n    const value = changeFields[key];\n    if (typeof value === \"undefined\") {\n      delete doc[key];\n    } else {\n      doc[key] = value;\n    }\n  });\n};\n\n"]}