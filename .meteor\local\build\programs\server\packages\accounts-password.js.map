{"version": 3, "sources": ["meteor://💻app/packages/accounts-password/email_templates.js", "meteor://💻app/packages/accounts-password/password_server.js"], "names": ["_objectSpread", "module", "link", "default", "v", "__reifyWaitForDeps__", "greet", "welcomeMsg", "user", "url", "greeting", "profile", "name", "concat", "Accounts", "emailTemplates", "from", "siteName", "Meteor", "absoluteUrl", "replace", "resetPassword", "subject", "text", "verifyEmail", "enrollAccount", "__reify_async_result__", "_reifyError", "self", "async", "argon2", "bcryptHash", "bcryptCompare", "hash", "compare", "getUserById", "id", "options", "users", "findOneAsync", "_addDefaultFieldSelector", "_bcryptRounds", "_options", "bcryptRounds", "_argon2Enabled", "argon2Enabled", "ARGON2_TYPES", "argon2i", "argon2d", "argon2id", "_argon2Type", "argon2Type", "_argon2TimeCost", "argon2TimeCost", "_argon2MemoryCost", "argon2MemoryCost", "_argon2Parallelism", "argon2Parallelism", "getPasswordString", "password", "SHA256", "algorithm", "Error", "digest", "hashPassword", "type", "timeCost", "memoryCost", "parallelism", "getRoundsFromBcryptHash", "rounds", "hashSegments", "split", "length", "parseInt", "_getRoundsFromBcryptHash", "getArgon2Params", "regex", "match", "_getArgon2Params", "getUserPasswordHash", "_user$services", "_user$services$passwo", "_user$services2", "_user$services2$passw", "services", "bcrypt", "_checkPass<PERSON><PERSON><PERSON>Fields", "_id", "isBcrypt", "startsWith", "isArgon", "updateUserPasswordDefered", "formattedPassword", "defer", "updateUserPassword", "getUpdatorForUserPassword", "encryptedPassword", "$set", "$unset", "updator", "updateAsync", "checkPasswordAsync", "result", "userId", "console", "warn", "verify", "error", "_handleError", "hashRounds", "paramsChanged", "argon2Params", "_checkPasswordAsync", "findUserByUsername", "username", "_findUserByQuery", "findUserByEmail", "email", "NonEmptyString", "Match", "Where", "x", "check", "String", "passwordValidator", "OneOf", "str", "_Meteor$settings", "_Meteor$settings$pack", "_Meteor$settings$pack2", "test", "settings", "packages", "accounts", "passwordMaxLength", "registerLoginHandler", "_Accounts$_check2faEn", "_Accounts", "undefined", "_userQueryValidator", "code", "Optional", "fields", "_check2faEnabled", "call", "_isTokenValid", "twoFactorAuthentication", "secret", "setUsername", "newUsername", "oldUsername", "_checkForCaseInsensitiveDuplicates", "ex", "methods", "changePassword", "oldPassword", "newPassword", "currentToken", "_getLoginToken", "connection", "$pull", "hashedToken", "$ne", "passwordChanged", "setPasswordAsync", "newPlaintextPassword", "_Meteor$settings2", "_Meteor$settings2$pac", "_Meteor$settings2$pac2", "Maybe", "logout", "Boolean", "pluckAddresses", "emails", "arguments", "map", "address", "forgotPassword", "caseSensitiveEmail", "find", "toLowerCase", "sendResetPasswordEmail", "generateResetToken", "reason", "extraTokenData", "includes", "token", "Random", "tokenRecord", "when", "Date", "Object", "assign", "_ensure", "enroll", "reset", "generateVerificationToken", "emailRecord", "e", "verified", "$push", "verificationTokens", "push", "extraParams", "realEmail", "urls", "generateOptionsForEmail", "Email", "sendAsync", "isDevelopment", "isPackageTest", "log", "sendEnrollmentEmail", "_len", "args", "Array", "_key", "_loginMethod", "_Accounts$_check2faEn2", "_Accounts2", "isEnroll", "tokenLifetimeMs", "_getPasswordResetTokenLifetimeMs", "_getPasswordEnrollTokenLifetimeMs", "currentTimeMs", "now", "oldToken", "_setLoginToken", "resetToOldToken", "affectedRecords", "err", "_clearAllLoginTokens", "sendVerificationEmail", "_len2", "_key2", "_Accounts$_check2faEn3", "_Accounts3", "t", "emailsRecord", "addEmailAsync", "newEmail", "caseInsensitiveRegExp", "RegExp", "_escapeRegExp", "updatedEmail", "updated", "didUpdateOwnEmail", "$addToSet", "removeEmail", "createUser", "ObjectIncluding", "hashed", "_createUserCheckingDuplicates", "_len3", "_key3", "forbidClientAccountCreation", "createUserVerifyingEmail", "createUserAsync", "createIndexAsync", "unique", "sparse"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAAlK,MAAMC,KAAK,GAAGC,UAAU,IAAI,CAACC,IAAI,EAAEC,GAAG,KAAK;MACzC,MAAMC,QAAQ,GACZF,IAAI,CAACG,OAAO,IAAIH,IAAI,CAACG,OAAO,CAACC,IAAI,YAAAC,MAAA,CACpBL,IAAI,CAACG,OAAO,CAACC,IAAI,SAC1B,QAAQ;MACd,UAAAC,MAAA,CAAUH,QAAQ,UAAAG,MAAA,CAElBN,UAAU,wCAAAM,MAAA,CAEVJ,GAAG;IAIL,CAAC;;IAED;AACA;AACA;AACA;AACA;IACAK,QAAQ,CAACC,cAAc,GAAAf,aAAA,CAAAA,aAAA,KACjBc,QAAQ,CAACC,cAAc,IAAI,CAAC,CAAC;MACjCC,IAAI,EAAE,yCAAyC;MAC/CC,QAAQ,EAAEC,MAAM,CAACC,WAAW,CAAC,CAAC,CAC3BC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAC3BA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAErBC,aAAa,EAAE;QACbC,OAAO,EAAEA,CAAA,sCAAAT,MAAA,CAC0BC,QAAQ,CAACC,cAAc,CAACE,QAAQ,CAAE;QACrEM,IAAI,EAAEjB,KAAK,CAAC,wBAAwB;MACtC,CAAC;MACDkB,WAAW,EAAE;QACXF,OAAO,EAAEA,CAAA,uCAAAT,MAAA,CAC2BC,QAAQ,CAACC,cAAc,CAACE,QAAQ,CAAE;QACtEM,IAAI,EAAEjB,KAAK,CAAC,8BAA8B;MAC5C,CAAC;MACDmB,aAAa,EAAE;QACbH,OAAO,EAAEA,CAAA,+CAAAT,MAAA,CACmCC,QAAQ,CAACC,cAAc,CAACE,QAAQ,CAAE;QAC9EM,IAAI,EAAEjB,KAAK,CAAC,4BAA4B;MAC1C;IAAC,EACF;IAACoB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC1CF,IAAI7B,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAI0B,MAAM;IAAC7B,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAAC0B,MAAM,GAAC1B,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI2B,UAAU,EAACC,aAAa;IAAC/B,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAC;MAAC+B,IAAIA,CAAC7B,CAAC,EAAC;QAAC2B,UAAU,GAAC3B,CAAC;MAAA,CAAC;MAAC8B,OAAOA,CAAC9B,CAAC,EAAC;QAAC4B,aAAa,GAAC5B,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIU,QAAQ;IAACb,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACY,QAAQA,CAACV,CAAC,EAAC;QAACU,QAAQ,GAACV,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAI5S;IACA,MAAM8B,WAAW,GACf,MAAAA,CAAOC,EAAE,EAAEC,OAAO,KAChB,MAAMnB,MAAM,CAACoB,KAAK,CAACC,YAAY,CAACH,EAAE,EAAEtB,QAAQ,CAAC0B,wBAAwB,CAACH,OAAO,CAAC,CAAC;;IAEnF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEAvB,QAAQ,CAAC2B,aAAa,GAAG,MAAM3B,QAAQ,CAAC4B,QAAQ,CAACC,YAAY,IAAI,EAAE;IAEnE7B,QAAQ,CAAC8B,cAAc,GAAG,MAAM9B,QAAQ,CAAC4B,QAAQ,CAACG,aAAa,IAAI,KAAK;IAExE,MAAMC,YAAY,GAAG;MACnBC,OAAO,EAAEjB,MAAM,CAACiB,OAAO;MACvBC,OAAO,EAAElB,MAAM,CAACkB,OAAO;MACvBC,QAAQ,EAAEnB,MAAM,CAACmB;IACnB,CAAC;IAEDnC,QAAQ,CAACoC,WAAW,GAAG,MAAMJ,YAAY,CAAChC,QAAQ,CAAC4B,QAAQ,CAACS,UAAU,CAAC,IAAIrB,MAAM,CAACmB,QAAQ;IAC1FnC,QAAQ,CAACsC,eAAe,GAAG,MAAMtC,QAAQ,CAAC4B,QAAQ,CAACW,cAAc,IAAI,CAAC;IACtEvC,QAAQ,CAACwC,iBAAiB,GAAG,MAAMxC,QAAQ,CAAC4B,QAAQ,CAACa,gBAAgB,IAAI,KAAK;IAC9EzC,QAAQ,CAAC0C,kBAAkB,GAAG,MAAM1C,QAAQ,CAAC4B,QAAQ,CAACe,iBAAiB,IAAI,CAAC;;IAE5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,MAAMC,iBAAiB,GAAGC,QAAQ,IAAI;MACpC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAGC,MAAM,CAACD,QAAQ,CAAC;MAC7B,CAAC,MACI;QAAE;QACL,IAAIA,QAAQ,CAACE,SAAS,KAAK,SAAS,EAAE;UACpC,MAAM,IAAIC,KAAK,CAAC,mCAAmC,GACjD,4BAA4B,CAAC;QACjC;QACAH,QAAQ,GAAGA,QAAQ,CAACI,MAAM;MAC5B;MACA,OAAOJ,QAAQ;IACjB,CAAC;;IAED;AACA;AACA;AACA;AACA;IACA,MAAMK,YAAY,GAAG,MAAOL,QAAQ,IAAK;MACvCA,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ,CAAC;MACtC,IAAI7C,QAAQ,CAAC8B,cAAc,CAAC,CAAC,KAAK,IAAI,EAAE;QACtC,OAAO,MAAMd,MAAM,CAACG,IAAI,CAAC0B,QAAQ,EAAE;UACjCM,IAAI,EAAEnD,QAAQ,CAACoC,WAAW,CAAC,CAAC;UAC5BgB,QAAQ,EAAEpD,QAAQ,CAACsC,eAAe,CAAC,CAAC;UACpCe,UAAU,EAAErD,QAAQ,CAACwC,iBAAiB,CAAC,CAAC;UACxCc,WAAW,EAAEtD,QAAQ,CAAC0C,kBAAkB,CAAC;QAC3C,CAAC,CAAC;MACJ,CAAC,MACI;QACH,OAAO,MAAMzB,UAAU,CAAC4B,QAAQ,EAAE7C,QAAQ,CAAC2B,aAAa,CAAC,CAAC,CAAC;MAC7D;IACF,CAAC;;IAED;IACA,MAAM4B,uBAAuB,GAAIpC,IAAI,IAAK;MACxC,IAAIqC,MAAM;MACV,IAAIrC,IAAI,EAAE;QACR,MAAMsC,YAAY,GAAGtC,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC;QACpC,IAAID,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE;UAC3BH,MAAM,GAAGI,QAAQ,CAACH,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACxC;MACF;MACA,OAAOD,MAAM;IACf,CAAC;IACDxD,QAAQ,CAAC6D,wBAAwB,GAAGN,uBAAuB;;IAG3D;AACA;AACA;AACA;AACA;AACA;IACA,SAASO,eAAeA,CAAC3C,IAAI,EAAE;MAC7B,MAAM4C,KAAK,GAAG,uDAAuD;MAErE,MAAMC,KAAK,GAAG7C,IAAI,CAAC6C,KAAK,CAACD,KAAK,CAAC;MAE/B,IAAI,CAACC,KAAK,EAAE;QACV,MAAM,IAAIhB,KAAK,CAAC,6BAA6B,CAAC;MAChD;MAEA,MAAM,GAAGG,IAAI,EAAEE,UAAU,EAAED,QAAQ,EAAEE,WAAW,CAAC,GAAGU,KAAK;MAEzD,OAAO;QACLb,IAAI,EAAEnB,YAAY,CAACmB,IAAI,CAAC;QACxBC,QAAQ,EAAEQ,QAAQ,CAACR,QAAQ,EAAE,EAAE,CAAC;QAChCC,UAAU,EAAEO,QAAQ,CAACP,UAAU,EAAE,EAAE,CAAC;QACpCC,WAAW,EAAEM,QAAQ,CAACN,WAAW,EAAE,EAAE;MACvC,CAAC;IACH;IAEAtD,QAAQ,CAACiE,gBAAgB,GAAGH,eAAe;IAE3C,MAAMI,mBAAmB,GAAGxE,IAAI,IAAI;MAAA,IAAAyE,cAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA;MAClC,OAAO,EAAAH,cAAA,GAAAzE,IAAI,CAAC6E,QAAQ,cAAAJ,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAetB,QAAQ,cAAAuB,qBAAA,uBAAvBA,qBAAA,CAAyBpD,MAAM,OAAAqD,eAAA,GAAI3E,IAAI,CAAC6E,QAAQ,cAAAF,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAexB,QAAQ,cAAAyB,qBAAA,uBAAvBA,qBAAA,CAAyBE,MAAM;IAC3E,CAAC;IAEDxE,QAAQ,CAACyE,wBAAwB,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEH,QAAQ,EAAE;IAAE,CAAC;IAE3D,MAAMI,QAAQ,GAAIxD,IAAI,IAAK;MACzB;MACA,OAAOA,IAAI,CAACyD,UAAU,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,MAAMC,OAAO,GAAI1D,IAAI,IAAK;MACtB;MACA,OAAOA,IAAI,CAACyD,UAAU,CAAC,SAAS,CAAC;IACrC,CAAC;IAED,MAAME,yBAAyB,GAAGA,CAACpF,IAAI,EAAEqF,iBAAiB,KAAK;MAC7D3E,MAAM,CAAC4E,KAAK,CAAC,YAAY;QACvB,MAAMC,kBAAkB,CAACvF,IAAI,EAAEqF,iBAAiB,CAAC;MACnD,CAAC,CAAC;IACJ,CAAC;;IAED;AACA;AACA;AACA;AACA;IACA,MAAMG,yBAAyB,GAAG,MAAOH,iBAAiB,IAAK;MAC7D,MAAMI,iBAAiB,GAAG,MAAMjC,YAAY,CAAC6B,iBAAiB,CAAC;MAC/D,IAAI/E,QAAQ,CAAC8B,cAAc,CAAC,CAAC,KAAK,KAAK,EAAE;QACvC,OAAO;UACLsD,IAAI,EAAE;YACJ,0BAA0B,EAAED;UAC9B,CAAC;UACDE,MAAM,EAAE;YACN,0BAA0B,EAAE;UAC9B;QACF,CAAC;MACH,CAAC,MACI,IAAIrF,QAAQ,CAAC8B,cAAc,CAAC,CAAC,KAAK,IAAI,EAAE;QAC3C,OAAO;UACLsD,IAAI,EAAE;YACJ,0BAA0B,EAAED;UAC9B,CAAC;UACDE,MAAM,EAAE;YACN,0BAA0B,EAAE;UAC9B;QACF,CAAC;MACH;IACF,CAAC;IAED,MAAMJ,kBAAkB,GAAG,MAAAA,CAAOvF,IAAI,EAAEqF,iBAAiB,KAAK;MAC5D,MAAMO,OAAO,GAAG,MAAMJ,yBAAyB,CAACH,iBAAiB,CAAC;MAClE,MAAM3E,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAAC;QAAEb,GAAG,EAAEhF,IAAI,CAACgF;MAAI,CAAC,EAAEY,OAAO,CAAC;IAC5D,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,MAAME,kBAAkB,GAAG,MAAAA,CAAO9F,IAAI,EAAEmD,QAAQ,KAAK;MACnD,MAAM4C,MAAM,GAAG;QACbC,MAAM,EAAEhG,IAAI,CAACgF;MACf,CAAC;MAED,MAAMK,iBAAiB,GAAGnC,iBAAiB,CAACC,QAAQ,CAAC;MACrD,MAAM1B,IAAI,GAAG+C,mBAAmB,CAACxE,IAAI,CAAC;MAGtC,MAAMqC,aAAa,GAAG/B,QAAQ,CAAC8B,cAAc,CAAC,CAAC;MAC/C,IAAIC,aAAa,KAAK,KAAK,EAAE;QAC3B,IAAI8C,OAAO,CAAC1D,IAAI,CAAC,EAAE;UACjB;UACA;UACAwE,OAAO,CAACC,IAAI,CAAC,0FAA0F,CAAC;UACxG,MAAM5B,KAAK,GAAG,MAAMhD,MAAM,CAAC6E,MAAM,CAAC1E,IAAI,EAAE4D,iBAAiB,CAAC;UAC1D,IAAI,CAACf,KAAK,EAAE;YACVyB,MAAM,CAACK,KAAK,GAAG9F,QAAQ,CAAC+F,YAAY,CAAC,oBAAoB,EAAE,KAAK,CAAC;UACnE,CAAC,MACG;YACF;YACAjB,yBAAyB,CAACpF,IAAI,EAAE;cAAEuD,MAAM,EAAE8B,iBAAiB;cAAEhC,SAAS,EAAE;YAAU,CAAC,CAAC;UACtF;QACF,CAAC,MACI;UACH,MAAMiD,UAAU,GAAGzC,uBAAuB,CAACpC,IAAI,CAAC;UAChD,MAAM6C,KAAK,GAAG,MAAM9C,aAAa,CAAC6D,iBAAiB,EAAE5D,IAAI,CAAC;UAC1D,IAAI,CAAC6C,KAAK,EAAE;YACVyB,MAAM,CAACK,KAAK,GAAG9F,QAAQ,CAAC+F,YAAY,CAAC,oBAAoB,EAAE,KAAK,CAAC;UACnE,CAAC,MACI,IAAI5E,IAAI,EAAE;YACb,MAAM8E,aAAa,GAAGD,UAAU,KAAKhG,QAAQ,CAAC2B,aAAa,CAAC,CAAC;YAC7D;YACA;YACA,IAAIsE,aAAa,KAAK,IAAI,EAAE;cAC1BnB,yBAAyB,CAACpF,IAAI,EAAE;gBAAEuD,MAAM,EAAE8B,iBAAiB;gBAAEhC,SAAS,EAAE;cAAU,CAAC,CAAC;YACtF;UACF;QACF;MACF,CAAC,MACI,IAAIhB,aAAa,KAAK,IAAI,EAAE;QAC/B,IAAI4C,QAAQ,CAACxD,IAAI,CAAC,EAAE;UAClB;UACA,MAAM6C,KAAK,GAAG,MAAM9C,aAAa,CAAC6D,iBAAiB,EAAE5D,IAAI,CAAC;UAC1D,IAAI,CAAC6C,KAAK,EAAE;YACVyB,MAAM,CAACK,KAAK,GAAG9F,QAAQ,CAAC+F,YAAY,CAAC,oBAAoB,EAAE,KAAK,CAAC;UACnE,CAAC,MACI;YACH;YACAjB,yBAAyB,CAACpF,IAAI,EAAE;cAAEuD,MAAM,EAAE8B,iBAAiB;cAAEhC,SAAS,EAAE;YAAU,CAAC,CAAC;UACtF;QACF,CAAC,MACI;UACH;UACA,MAAMmD,YAAY,GAAGpC,eAAe,CAAC3C,IAAI,CAAC;UAC1C,MAAM6C,KAAK,GAAG,MAAMhD,MAAM,CAAC6E,MAAM,CAAC1E,IAAI,EAAE4D,iBAAiB,CAAC;UAC1D,IAAI,CAACf,KAAK,EAAE;YACVyB,MAAM,CAACK,KAAK,GAAG9F,QAAQ,CAAC+F,YAAY,CAAC,oBAAoB,EAAE,KAAK,CAAC;UACnE,CAAC,MACI,IAAI5E,IAAI,EAAE;YACb,MAAM8E,aAAa,GAAGC,YAAY,CAAC7C,UAAU,KAAKrD,QAAQ,CAACwC,iBAAiB,CAAC,CAAC,IAC5E0D,YAAY,CAAC9C,QAAQ,KAAKpD,QAAQ,CAACsC,eAAe,CAAC,CAAC,IACpD4D,YAAY,CAAC5C,WAAW,KAAKtD,QAAQ,CAAC0C,kBAAkB,CAAC,CAAC,IAC1DwD,YAAY,CAAC/C,IAAI,KAAKnD,QAAQ,CAACoC,WAAW,CAAC,CAAC;YAC9C,IAAI6D,aAAa,KAAK,IAAI,EAAE;cAC1B;cACAnB,yBAAyB,CAACpF,IAAI,EAAE;gBAAEuD,MAAM,EAAE8B,iBAAiB;gBAAEhC,SAAS,EAAE;cAAU,CAAC,CAAC;YACtF;UACF;QACF;MACF;MAGA,OAAO0C,MAAM;IACf,CAAC;IAEDzF,QAAQ,CAACmG,mBAAmB,GAAGX,kBAAkB;;IAEjD;IACA;IACA;;IAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAxF,QAAQ,CAACoG,kBAAkB,GACzB,OAAOC,QAAQ,EAAE9E,OAAO,KACtB,MAAMvB,QAAQ,CAACsG,gBAAgB,CAAC;MAAED;IAAS,CAAC,EAAE9E,OAAO,CAAC;;IAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAvB,QAAQ,CAACuG,eAAe,GACtB,OAAOC,KAAK,EAAEjF,OAAO,KACnB,MAAMvB,QAAQ,CAACsG,gBAAgB,CAAC;MAAEE;IAAM,CAAC,EAAEjF,OAAO,CAAC;;IAEvD;IACA,MAAMkF,cAAc,GAAGC,KAAK,CAACC,KAAK,CAACC,CAAC,IAAI;MACtCC,KAAK,CAACD,CAAC,EAAEE,MAAM,CAAC;MAChB,OAAOF,CAAC,CAACjD,MAAM,GAAG,CAAC;IACrB,CAAC,CAAC;IAEF,MAAMoD,iBAAiB,GAAGL,KAAK,CAACM,KAAK,CACnCN,KAAK,CAACC,KAAK,CAACM,GAAG;MAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAAA,OAAIV,KAAK,CAACW,IAAI,CAACJ,GAAG,EAAEH,MAAM,CAAC,IAAIG,GAAG,CAACtD,MAAM,MAAAuD,gBAAA,GAAI9G,MAAM,CAACkH,QAAQ,cAAAJ,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBK,QAAQ,cAAAJ,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BK,QAAQ,cAAAJ,sBAAA,uBAAnCA,sBAAA,CAAqCK,iBAAiB,KAAI,GAAG;IAAA,EAAC,EAAE;MAC1HxE,MAAM,EAAEyD,KAAK,CAACC,KAAK,CAACM,GAAG,IAAIP,KAAK,CAACW,IAAI,CAACJ,GAAG,EAAEH,MAAM,CAAC,IAAIG,GAAG,CAACtD,MAAM,KAAK,EAAE,CAAC;MACxEZ,SAAS,EAAE2D,KAAK,CAACM,KAAK,CAAC,SAAS;IAClC,CACF,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAhH,QAAQ,CAAC0H,oBAAoB,CAAC,UAAU,EAAE,MAAMnG,OAAO,IAAI;MAAA,IAAAoG,qBAAA,EAAAC,SAAA;MACzD,IAAI,CAACrG,OAAO,CAACsB,QAAQ,EACnB,OAAOgF,SAAS,CAAC,CAAC;;MAEpBhB,KAAK,CAACtF,OAAO,EAAE;QACb7B,IAAI,EAAEM,QAAQ,CAAC8H,mBAAmB;QAClCjF,QAAQ,EAAEkE,iBAAiB;QAC3BgB,IAAI,EAAErB,KAAK,CAACsB,QAAQ,CAACvB,cAAc;MACrC,CAAC,CAAC;MAGF,MAAM/G,IAAI,GAAG,MAAMM,QAAQ,CAACsG,gBAAgB,CAAC/E,OAAO,CAAC7B,IAAI,EAAE;QAACuI,MAAM,EAAA/I,aAAA;UAChEqF,QAAQ,EAAE;QAAC,GACRvE,QAAQ,CAACyE,wBAAwB;MACrC,CAAC,CAAC;MACH,IAAI,CAAC/E,IAAI,EAAE;QACTM,QAAQ,CAAC+F,YAAY,CAAC,gBAAgB,CAAC;MACzC;MAEA,IAAI,CAAC7B,mBAAmB,CAACxE,IAAI,CAAC,EAAE;QAC9BM,QAAQ,CAAC+F,YAAY,CAAC,0BAA0B,CAAC;MACnD;MAEA,MAAMN,MAAM,GAAG,MAAMD,kBAAkB,CAAC9F,IAAI,EAAE6B,OAAO,CAACsB,QAAQ,CAAC;MAC/D;MACA;MACA,IACE,CAAC4C,MAAM,CAACK,KAAK,KAAA6B,qBAAA,GACb,CAAAC,SAAA,GAAA5H,QAAQ,EAACkI,gBAAgB,cAAAP,qBAAA,eAAzBA,qBAAA,CAAAQ,IAAA,CAAAP,SAAA,EAA4BlI,IAAI,CAAC,EACjC;QACA,IAAI,CAAC6B,OAAO,CAACwG,IAAI,EAAE;UACjB/H,QAAQ,CAAC+F,YAAY,CAAC,2BAA2B,EAAE,IAAI,EAAE,aAAa,CAAC;QACzE;QACA,IACE,CAAC/F,QAAQ,CAACoI,aAAa,CACrB1I,IAAI,CAAC6E,QAAQ,CAAC8D,uBAAuB,CAACC,MAAM,EAC5C/G,OAAO,CAACwG,IACV,CAAC,EACD;UACA/H,QAAQ,CAAC+F,YAAY,CAAC,kBAAkB,EAAE,IAAI,EAAE,kBAAkB,CAAC;QACrE;MACF;MAEA,OAAON,MAAM;IACf,CAAC,CAAC;;IAEF;IACA;IACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAzF,QAAQ,CAACuI,WAAW,GAClB,OAAO7C,MAAM,EAAE8C,WAAW,KAAK;MAC7B3B,KAAK,CAACnB,MAAM,EAAEe,cAAc,CAAC;MAC7BI,KAAK,CAAC2B,WAAW,EAAE/B,cAAc,CAAC;MAElC,MAAM/G,IAAI,GAAG,MAAM2B,WAAW,CAACqE,MAAM,EAAE;QACrCuC,MAAM,EAAE;UACN5B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MAEF,IAAI,CAAC3G,IAAI,EAAE;QACTM,QAAQ,CAAC+F,YAAY,CAAC,gBAAgB,CAAC;MACzC;MAEA,MAAM0C,WAAW,GAAG/I,IAAI,CAAC2G,QAAQ;;MAEjC;MACA,MAAMrG,QAAQ,CAAC0I,kCAAkC,CAAC,UAAU,EAC1D,UAAU,EAAEF,WAAW,EAAE9I,IAAI,CAACgF,GAAG,CAAC;MAEpC,MAAMtE,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAAC;QAAEb,GAAG,EAAEhF,IAAI,CAACgF;MAAI,CAAC,EAAE;QAAEU,IAAI,EAAE;UAAEiB,QAAQ,EAAEmC;QAAY;MAAE,CAAC,CAAC;;MAEtF;MACA;MACA,IAAI;QACF,MAAMxI,QAAQ,CAAC0I,kCAAkC,CAAC,UAAU,EAC1D,UAAU,EAAEF,WAAW,EAAE9I,IAAI,CAACgF,GAAG,CAAC;MACtC,CAAC,CAAC,OAAOiE,EAAE,EAAE;QACX;QACA,MAAMvI,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAAC;UAAEb,GAAG,EAAEhF,IAAI,CAACgF;QAAI,CAAC,EAAE;UAAEU,IAAI,EAAE;YAAEiB,QAAQ,EAAEoC;UAAY;QAAE,CAAC,CAAC;QACtF,MAAME,EAAE;MACV;IACF,CAAC;;IAEH;IACA;IACA;IACAvI,MAAM,CAACwI,OAAO,CACZ;MACEC,cAAc,EAAE,eAAAA,CAAeC,WAAW,EAAEC,WAAW,EAAE;QACvDlC,KAAK,CAACiC,WAAW,EAAE/B,iBAAiB,CAAC;QACrCF,KAAK,CAACkC,WAAW,EAAEhC,iBAAiB,CAAC;QAErC,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE;UAChB,MAAM,IAAItF,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;QAClD;QAEA,MAAMtD,IAAI,GAAG,MAAM2B,WAAW,CAAC,IAAI,CAACqE,MAAM,EAAE;UAC1CuC,MAAM,EAAA/I,aAAA;YACJqF,QAAQ,EAAE;UAAC,GACRvE,QAAQ,CAACyE,wBAAwB;QAExC,CAAC,CAAC;QACF,IAAI,CAAC/E,IAAI,EAAE;UACTM,QAAQ,CAAC+F,YAAY,CAAC,gBAAgB,CAAC;QACzC;QAEA,IAAI,CAAC7B,mBAAmB,CAACxE,IAAI,CAAC,EAAE;UAC9BM,QAAQ,CAAC+F,YAAY,CAAC,0BAA0B,CAAC;QACnD;QAEA,MAAMN,MAAM,GAAG,MAAMD,kBAAkB,CAAC9F,IAAI,EAAEoJ,WAAW,CAAC;QAC1D,IAAIrD,MAAM,CAACK,KAAK,EAAE;UAChB,MAAML,MAAM,CAACK,KAAK;QACpB;;QAEA;QACA;QACA;QACA;QACA,MAAMkD,YAAY,GAAGhJ,QAAQ,CAACiJ,cAAc,CAAC,IAAI,CAACC,UAAU,CAAC5H,EAAE,CAAC;QAChE,MAAMgE,OAAO,GAAG,MAAMJ,yBAAyB,CAAC6D,WAAW,CAAC;QAE5D,MAAM3I,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC5B;UAAEb,GAAG,EAAE,IAAI,CAACgB;QAAO,CAAC,EACpB;UACEN,IAAI,EAAEE,OAAO,CAACF,IAAI;UAClB+D,KAAK,EAAE;YACL,6BAA6B,EAAE;cAAEC,WAAW,EAAE;gBAAEC,GAAG,EAAEL;cAAa;YAAE;UACtE,CAAC;UACD3D,MAAM,EAAAnG,aAAA;YAAI,yBAAyB,EAAE;UAAC,GAAKoG,OAAO,CAACD,MAAM;QAC3D,CACF,CAAC;QAED,OAAO;UAAEiE,eAAe,EAAE;QAAK,CAAC;MAClC;IACF,CAAC,CAAC;;IAGJ;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAtJ,QAAQ,CAACuJ,gBAAgB,GACvB,OAAO7D,MAAM,EAAE8D,oBAAoB,EAAEjI,OAAO,KAAK;MAC/CsF,KAAK,CAACnB,MAAM,EAAEoB,MAAM,CAAC;MACrBD,KAAK,CAAC2C,oBAAoB,EAAE9C,KAAK,CAACC,KAAK,CAACM,GAAG;QAAA,IAAAwC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QAAA,OAAIjD,KAAK,CAACW,IAAI,CAACJ,GAAG,EAAEH,MAAM,CAAC,IAAIG,GAAG,CAACtD,MAAM,MAAA8F,iBAAA,GAAIrJ,MAAM,CAACkH,QAAQ,cAAAmC,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiBlC,QAAQ,cAAAmC,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BlC,QAAQ,cAAAmC,sBAAA,uBAAnCA,sBAAA,CAAqClC,iBAAiB,KAAI,GAAG;MAAA,EAAC,CAAC;MACvJZ,KAAK,CAACtF,OAAO,EAAEmF,KAAK,CAACkD,KAAK,CAAC;QAAEC,MAAM,EAAEC;MAAQ,CAAC,CAAC,CAAC;MAChDvI,OAAO,GAAArC,aAAA;QAAK2K,MAAM,EAAE;MAAI,GAAKtI,OAAO,CAAE;MAEtC,MAAM7B,IAAI,GAAG,MAAM2B,WAAW,CAACqE,MAAM,EAAE;QAAEuC,MAAM,EAAE;UAAEvD,GAAG,EAAE;QAAE;MAAE,CAAC,CAAC;MAC9D,IAAI,CAAChF,IAAI,EAAE;QACT,MAAM,IAAIU,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC;MAC/C;MAEA,IAAIsC,OAAO,GAAG,MAAMJ,yBAAyB,CAACsE,oBAAoB,CAAC;MACnElE,OAAO,CAACD,MAAM,GAAGC,OAAO,CAACD,MAAM,IAAI,CAAC,CAAC;MACrCC,OAAO,CAACD,MAAM,CAAC,yBAAyB,CAAC,GAAG,CAAC;MAE7C,IAAI9D,OAAO,CAACsI,MAAM,EAAE;QAClBvE,OAAO,CAACD,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC;MACnD;MAEA,MAAMjF,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAAC;QAAEb,GAAG,EAAEhF,IAAI,CAACgF;MAAI,CAAC,EAAEY,OAAO,CAAC;IAC5D,CAAC;;IAEH;IACA;IACA;;IAEA;IACA,MAAMyE,cAAc,GAAG,SAAAA,CAAA;MAAA,IAACC,MAAM,GAAAC,SAAA,CAAAtG,MAAA,QAAAsG,SAAA,QAAApC,SAAA,GAAAoC,SAAA,MAAG,EAAE;MAAA,OAAKD,MAAM,CAACE,GAAG,CAAC1D,KAAK,IAAIA,KAAK,CAAC2D,OAAO,CAAC;IAAA;;IAE1E;IACA;IACA/J,MAAM,CAACwI,OAAO,CAAC;MAACwB,cAAc,EAAE,MAAM7I,OAAO,IAAI;QAC/CsF,KAAK,CAACtF,OAAO,EAAE;UAACiF,KAAK,EAAEM;QAAM,CAAC,CAAC;QAE/B,MAAMpH,IAAI,GAAG,MAAMM,QAAQ,CAACuG,eAAe,CAAChF,OAAO,CAACiF,KAAK,EAAE;UAAEyB,MAAM,EAAE;YAAE+B,MAAM,EAAE;UAAE;QAAE,CAAC,CAAC;QAErF,IAAI,CAACtK,IAAI,EAAE;UACTM,QAAQ,CAAC+F,YAAY,CAAC,gBAAgB,CAAC;QACzC;QAEA,MAAMiE,MAAM,GAAGD,cAAc,CAACrK,IAAI,CAACsK,MAAM,CAAC;QAC1C,MAAMK,kBAAkB,GAAGL,MAAM,CAACM,IAAI,CACpC9D,KAAK,IAAIA,KAAK,CAAC+D,WAAW,CAAC,CAAC,KAAKhJ,OAAO,CAACiF,KAAK,CAAC+D,WAAW,CAAC,CAC7D,CAAC;QAED,MAAMvK,QAAQ,CAACwK,sBAAsB,CAAC9K,IAAI,CAACgF,GAAG,EAAE2F,kBAAkB,CAAC;MACrE;IAAC,CAAC,CAAC;;IAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACArK,QAAQ,CAACyK,kBAAkB,GACzB,OAAO/E,MAAM,EAAEc,KAAK,EAAEkE,MAAM,EAAEC,cAAc,KAAK;MACjD;MACA;MACA;MACA,MAAMjL,IAAI,GAAG,MAAM2B,WAAW,CAACqE,MAAM,CAAC;MACtC,IAAI,CAAChG,IAAI,EAAE;QACTM,QAAQ,CAAC+F,YAAY,CAAC,iBAAiB,CAAC;MAC1C;;MAEA;MACA,IAAI,CAACS,KAAK,IAAI9G,IAAI,CAACsK,MAAM,IAAItK,IAAI,CAACsK,MAAM,CAAC,CAAC,CAAC,EAAE;QAC3CxD,KAAK,GAAG9G,IAAI,CAACsK,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO;MAChC;;MAEA;MACA,IAAI,CAAC3D,KAAK,IACR,CAAEuD,cAAc,CAACrK,IAAI,CAACsK,MAAM,CAAC,CAACY,QAAQ,CAACpE,KAAK,CAAE,EAAE;QAChDxG,QAAQ,CAAC+F,YAAY,CAAC,yBAAyB,CAAC;MAClD;MAEA,MAAM8E,KAAK,GAAGC,MAAM,CAACxC,MAAM,CAAC,CAAC;MAC7B,MAAMyC,WAAW,GAAG;QAClBF,KAAK;QACLrE,KAAK;QACLwE,IAAI,EAAE,IAAIC,IAAI,CAAC;MACjB,CAAC;MAED,IAAIP,MAAM,KAAK,eAAe,EAAE;QAC9BK,WAAW,CAACL,MAAM,GAAG,OAAO;MAC9B,CAAC,MAAM,IAAIA,MAAM,KAAK,eAAe,EAAE;QACrCK,WAAW,CAACL,MAAM,GAAG,QAAQ;MAC/B,CAAC,MAAM,IAAIA,MAAM,EAAE;QACjB;QACAK,WAAW,CAACL,MAAM,GAAGA,MAAM;MAC7B;MAEA,IAAIC,cAAc,EAAE;QAClBO,MAAM,CAACC,MAAM,CAACJ,WAAW,EAAEJ,cAAc,CAAC;MAC5C;MACA;MACA;MACA;MACA,IAAID,MAAM,KAAK,eAAe,EAAE;QAC9B,MAAMtK,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC5B;UAAEb,GAAG,EAAEhF,IAAI,CAACgF;QAAI,CAAC,EACjB;UACEU,IAAI,EAAE;YACJ,0BAA0B,EAAE2F;UAC9B;QACF,CACF,CAAC;QACD;QACA3K,MAAM,CAACgL,OAAO,CAAC1L,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC2L,MAAM,GAAGN,WAAW;MACnE,CAAC,MACI;QACH,MAAM3K,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC5B;UAAEb,GAAG,EAAEhF,IAAI,CAACgF;QAAI,CAAC,EACjB;UACEU,IAAI,EAAE;YACJ,yBAAyB,EAAE2F;UAC7B;QACF,CACF,CAAC;QACD;QACA3K,MAAM,CAACgL,OAAO,CAAC1L,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC4L,KAAK,GAAGP,WAAW;MAClE;MAEA,OAAO;QAAEvE,KAAK;QAAE9G,IAAI;QAAEmL;MAAM,CAAC;IAC/B,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA7K,QAAQ,CAACuL,yBAAyB,GAChC,OAAO7F,MAAM,EAAEc,KAAK,EAAEmE,cAAc,KAAK;MACzC;MACA;MACA;MACA,MAAMjL,IAAI,GAAG,MAAM2B,WAAW,CAACqE,MAAM,CAAC;MACtC,IAAI,CAAChG,IAAI,EAAE;QACTM,QAAQ,CAAC+F,YAAY,CAAC,iBAAiB,CAAC;MAC1C;;MAEA;MACA,IAAI,CAACS,KAAK,EAAE;QACV,MAAMgF,WAAW,GAAG,CAAC9L,IAAI,CAACsK,MAAM,IAAI,EAAE,EAAEM,IAAI,CAACmB,CAAC,IAAI,CAACA,CAAC,CAACC,QAAQ,CAAC;QAC9DlF,KAAK,GAAG,CAACgF,WAAW,IAAI,CAAC,CAAC,EAAErB,OAAO;QAEnC,IAAI,CAAC3D,KAAK,EAAE;UACVxG,QAAQ,CAAC+F,YAAY,CAAC,8CAA8C,CAAC;QACvE;MACF;;MAEA;MACA,IAAI,CAACS,KAAK,IACR,CAAEuD,cAAc,CAACrK,IAAI,CAACsK,MAAM,CAAC,CAACY,QAAQ,CAACpE,KAAK,CAAE,EAAE;QAChDxG,QAAQ,CAAC+F,YAAY,CAAC,yBAAyB,CAAC;MAClD;MAEA,MAAM8E,KAAK,GAAGC,MAAM,CAACxC,MAAM,CAAC,CAAC;MAC7B,MAAMyC,WAAW,GAAG;QAClBF,KAAK;QACL;QACAV,OAAO,EAAE3D,KAAK;QACdwE,IAAI,EAAE,IAAIC,IAAI,CAAC;MACjB,CAAC;MAED,IAAIN,cAAc,EAAE;QAClBO,MAAM,CAACC,MAAM,CAACJ,WAAW,EAAEJ,cAAc,CAAC;MAC5C;MAEA,MAAMvK,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAAC;QAACb,GAAG,EAAEhF,IAAI,CAACgF;MAAG,CAAC,EAAE;QAACiH,KAAK,EAAE;UACtD,mCAAmC,EAAEZ;QACvC;MAAC,CAAC,CAAC;;MAEH;MACA3K,MAAM,CAACgL,OAAO,CAAC1L,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC;MACzC,IAAI,CAACA,IAAI,CAAC6E,QAAQ,CAACiC,KAAK,CAACoF,kBAAkB,EAAE;QAC3ClM,IAAI,CAAC6E,QAAQ,CAACiC,KAAK,CAACoF,kBAAkB,GAAG,EAAE;MAC7C;MACAlM,IAAI,CAAC6E,QAAQ,CAACiC,KAAK,CAACoF,kBAAkB,CAACC,IAAI,CAACd,WAAW,CAAC;MAExD,OAAO;QAACvE,KAAK;QAAE9G,IAAI;QAAEmL;MAAK,CAAC;IAC7B,CAAC;;IAGD;IACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA7K,QAAQ,CAACwK,sBAAsB,GAC7B,OAAO9E,MAAM,EAAEc,KAAK,EAAEmE,cAAc,EAAEmB,WAAW,KAAK;MACpD,MAAM;QAAEtF,KAAK,EAAEuF,SAAS;QAAErM,IAAI;QAAEmL;MAAM,CAAC,GACrC,MAAM7K,QAAQ,CAACyK,kBAAkB,CAAC/E,MAAM,EAAEc,KAAK,EAAE,eAAe,EAAEmE,cAAc,CAAC;MACnF,MAAMhL,GAAG,GAAGK,QAAQ,CAACgM,IAAI,CAACzL,aAAa,CAACsK,KAAK,EAAEiB,WAAW,CAAC;MAC3D,MAAMvK,OAAO,GAAG,MAAMvB,QAAQ,CAACiM,uBAAuB,CAACF,SAAS,EAAErM,IAAI,EAAEC,GAAG,EAAE,eAAe,CAAC;MAC7F,MAAMuM,KAAK,CAACC,SAAS,CAAC5K,OAAO,CAAC;MAE9B,IAAInB,MAAM,CAACgM,aAAa,IAAI,CAAChM,MAAM,CAACiM,aAAa,EAAE;QACjD1G,OAAO,CAAC2G,GAAG,0BAAAvM,MAAA,CAA2BJ,GAAG,CAAG,CAAC;MAC/C;MACA,OAAO;QAAE6G,KAAK,EAAEuF,SAAS;QAAErM,IAAI;QAAEmL,KAAK;QAAElL,GAAG;QAAE4B;MAAQ,CAAC;IACxD,CAAC;;IAEH;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAvB,QAAQ,CAACuM,mBAAmB,GAC1B,OAAO7G,MAAM,EAAEc,KAAK,EAAEmE,cAAc,EAAEmB,WAAW,KAAK;MAEpD,MAAM;QAAEtF,KAAK,EAAEuF,SAAS;QAAErM,IAAI;QAAEmL;MAAM,CAAC,GACrC,MAAM7K,QAAQ,CAACyK,kBAAkB,CAAC/E,MAAM,EAAEc,KAAK,EAAE,eAAe,EAAEmE,cAAc,CAAC;MAEnF,MAAMhL,GAAG,GAAGK,QAAQ,CAACgM,IAAI,CAACrL,aAAa,CAACkK,KAAK,EAAEiB,WAAW,CAAC;MAE3D,MAAMvK,OAAO,GACX,MAAMvB,QAAQ,CAACiM,uBAAuB,CAACF,SAAS,EAAErM,IAAI,EAAEC,GAAG,EAAE,eAAe,CAAC;MAE/E,MAAMuM,KAAK,CAACC,SAAS,CAAC5K,OAAO,CAAC;MAC9B,IAAInB,MAAM,CAACgM,aAAa,IAAI,CAAChM,MAAM,CAACiM,aAAa,EAAE;QACjD1G,OAAO,CAAC2G,GAAG,4BAAAvM,MAAA,CAA6BJ,GAAG,CAAG,CAAC;MACjD;MACA,OAAO;QAAE6G,KAAK,EAAEuF,SAAS;QAAErM,IAAI;QAAEmL,KAAK;QAAElL,GAAG;QAAE4B;MAAQ,CAAC;IACxD,CAAC;;IAGH;IACA;IACAnB,MAAM,CAACwI,OAAO,CACZ;MACErI,aAAa,EACX,eAAAA,CAAA,EAAyB;QAAA,SAAAiM,IAAA,GAAAvC,SAAA,CAAAtG,MAAA,EAAN8I,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAA1C,SAAA,CAAA0C,IAAA;QAAA;QACrB,MAAM9B,KAAK,GAAG4B,IAAI,CAAC,CAAC,CAAC;QACrB,MAAM1D,WAAW,GAAG0D,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,MAAMzM,QAAQ,CAAC4M,YAAY,CAChC,IAAI,EACJ,eAAe,EACfH,IAAI,EACJ,UAAU,EACV,YAAY;UAAA,IAAAI,sBAAA,EAAAC,UAAA;UACVjG,KAAK,CAACgE,KAAK,EAAE/D,MAAM,CAAC;UACpBD,KAAK,CAACkC,WAAW,EAAEhC,iBAAiB,CAAC;UACrC,IAAIrH,IAAI,GAAG,MAAMU,MAAM,CAACoB,KAAK,CAACC,YAAY,CACxC;YAAE,+BAA+B,EAAEoJ;UAAM,CAAC,EAC1C;YACE5C,MAAM,EAAE;cACN1D,QAAQ,EAAE,CAAC;cACXyF,MAAM,EAAE;YACV;UACF,CACF,CAAC;UAED,IAAI+C,QAAQ,GAAG,KAAK;UACpB;UACA;UACA;UACA,IAAI,CAACrN,IAAI,EAAE;YACTA,IAAI,GAAG,MAAMU,MAAM,CAACoB,KAAK,CAACC,YAAY,CACpC;cAAE,gCAAgC,EAAEoJ;YAAM,CAAC,EAC3C;cACE5C,MAAM,EAAE;gBACN1D,QAAQ,EAAE,CAAC;gBACXyF,MAAM,EAAE;cACV;YACF,CACF,CAAC;YACD+C,QAAQ,GAAG,IAAI;UACjB;UACA,IAAI,CAACrN,IAAI,EAAE;YACT,MAAM,IAAIU,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAC9C;UACA,IAAI+H,WAAW,GAAG,CAAC,CAAC;UACpB,IAAIgC,QAAQ,EAAE;YACZhC,WAAW,GAAGrL,IAAI,CAAC6E,QAAQ,CAAC1B,QAAQ,CAACwI,MAAM;UAC7C,CAAC,MAAM;YACLN,WAAW,GAAGrL,IAAI,CAAC6E,QAAQ,CAAC1B,QAAQ,CAACyI,KAAK;UAC5C;UACA,MAAM;YAAEN,IAAI;YAAExE;UAAM,CAAC,GAAGuE,WAAW;UACnC,IAAIiC,eAAe,GAAGhN,QAAQ,CAACiN,gCAAgC,CAAC,CAAC;UACjE,IAAIF,QAAQ,EAAE;YACZC,eAAe,GAAGhN,QAAQ,CAACkN,iCAAiC,CAAC,CAAC;UAChE;UACA,MAAMC,aAAa,GAAGlC,IAAI,CAACmC,GAAG,CAAC,CAAC;UAChC,IAAKD,aAAa,GAAGnC,IAAI,GAAIgC,eAAe,EAC1C,MAAM,IAAI5M,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAC9C,IAAI,CAAE+G,cAAc,CAACrK,IAAI,CAACsK,MAAM,CAAC,CAACY,QAAQ,CAACpE,KAAK,CAAE,EAChD,OAAO;YACLd,MAAM,EAAEhG,IAAI,CAACgF,GAAG;YAChBoB,KAAK,EAAE,IAAI1F,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,iCAAiC;UAChE,CAAC;;UAEH;UACA;UACA;UACA;UACA,MAAMqK,QAAQ,GAAGrN,QAAQ,CAACiJ,cAAc,CAAC,IAAI,CAACC,UAAU,CAAC5H,EAAE,CAAC;UAC5DtB,QAAQ,CAACsN,cAAc,CAAC5N,IAAI,CAACgF,GAAG,EAAE,IAAI,CAACwE,UAAU,EAAE,IAAI,CAAC;UACxD,MAAMqE,eAAe,GAAGA,CAAA,KACtBvN,QAAQ,CAACsN,cAAc,CAAC5N,IAAI,CAACgF,GAAG,EAAE,IAAI,CAACwE,UAAU,EAAEmE,QAAQ,CAAC;UAE9D,MAAM/H,OAAO,GAAG,MAAMJ,yBAAyB,CAAC6D,WAAW,CAAC;UAE5D,IAAI;YACF;YACA;YACA;YACA;YACA,IAAIyE,eAAe,GAAG,CAAC,CAAC;YACxB;YACA,IAAIT,QAAQ,EAAE;cACZS,eAAe,GAAG,MAAMpN,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC9C;gBACEb,GAAG,EAAEhF,IAAI,CAACgF,GAAG;gBACb,gBAAgB,EAAE8B,KAAK;gBACvB,gCAAgC,EAAEqE;cACpC,CAAC,EACD;gBACEzF,IAAI,EAAAlG,aAAA;kBACF,mBAAmB,EAAE;gBAAI,GACtBoG,OAAO,CAACF,IAAI,CAChB;gBACDC,MAAM,EAAAnG,aAAA;kBACJ,0BAA0B,EAAE;gBAAC,GAC1BoG,OAAO,CAACD,MAAM;cAErB,CAAC,CAAC;YACN,CAAC,MACI;cACHmI,eAAe,GAAG,MAAMpN,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC9C;gBACEb,GAAG,EAAEhF,IAAI,CAACgF,GAAG;gBACb,gBAAgB,EAAE8B,KAAK;gBACvB,+BAA+B,EAAEqE;cACnC,CAAC,EACD;gBACEzF,IAAI,EAAAlG,aAAA;kBACF,mBAAmB,EAAE;gBAAI,GACtBoG,OAAO,CAACF,IAAI,CAChB;gBACDC,MAAM,EAAAnG,aAAA;kBACJ,yBAAyB,EAAE;gBAAC,GACzBoG,OAAO,CAACD,MAAM;cAErB,CAAC,CAAC;YACN;YACA,IAAImI,eAAe,KAAK,CAAC,EACvB,OAAO;cACL9H,MAAM,EAAEhG,IAAI,CAACgF,GAAG;cAChBoB,KAAK,EAAE,IAAI1F,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,eAAe;YAC9C,CAAC;UACL,CAAC,CAAC,OAAOyK,GAAG,EAAE;YACZF,eAAe,CAAC,CAAC;YACjB,MAAME,GAAG;UACX;;UAEA;UACA;UACA,MAAMzN,QAAQ,CAAC0N,oBAAoB,CAAChO,IAAI,CAACgF,GAAG,CAAC;UAE7C,KAAAmI,sBAAA,GAAI,CAAAC,UAAA,GAAA9M,QAAQ,EAACkI,gBAAgB,cAAA2E,sBAAA,eAAzBA,sBAAA,CAAA1E,IAAA,CAAA2E,UAAA,EAA4BpN,IAAI,CAAC,EAAE;YACrC,OAAO;cACLgG,MAAM,EAAEhG,IAAI,CAACgF,GAAG;cAChBoB,KAAK,EAAE9F,QAAQ,CAAC+F,YAAY,CAC1B,iEAAiE,EACjE,KAAK,EACL,aACF;YACF,CAAC;UACH;UACA,OAAO;YAAEL,MAAM,EAAEhG,IAAI,CAACgF;UAAI,CAAC;QAC7B,CACF,CAAC;MACH;IACJ,CACF,CAAC;;IAED;IACA;IACA;;IAGA;IACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA1E,QAAQ,CAAC2N,qBAAqB,GAC5B,OAAOjI,MAAM,EAAEc,KAAK,EAAEmE,cAAc,EAAEmB,WAAW,KAAK;MACpD;MACA;MACA;;MAEA,MAAM;QAAEtF,KAAK,EAAEuF,SAAS;QAAErM,IAAI;QAAEmL;MAAM,CAAC,GACrC,MAAM7K,QAAQ,CAACuL,yBAAyB,CAAC7F,MAAM,EAAEc,KAAK,EAAEmE,cAAc,CAAC;MACzE,MAAMhL,GAAG,GAAGK,QAAQ,CAACgM,IAAI,CAACtL,WAAW,CAACmK,KAAK,EAAEiB,WAAW,CAAC;MACzD,MAAMvK,OAAO,GAAG,MAAMvB,QAAQ,CAACiM,uBAAuB,CAACF,SAAS,EAAErM,IAAI,EAAEC,GAAG,EAAE,aAAa,CAAC;MAC3F,MAAMuM,KAAK,CAACC,SAAS,CAAC5K,OAAO,CAAC;MAC9B,IAAInB,MAAM,CAACgM,aAAa,IAAI,CAAChM,MAAM,CAACiM,aAAa,EAAE;QACjD1G,OAAO,CAAC2G,GAAG,8BAAAvM,MAAA,CAA+BJ,GAAG,CAAG,CAAC;MACnD;MACA,OAAO;QAAE6G,KAAK,EAAEuF,SAAS;QAAErM,IAAI;QAAEmL,KAAK;QAAElL,GAAG;QAAE4B;MAAQ,CAAC;IACxD,CAAC;;IAEH;IACA;IACAnB,MAAM,CAACwI,OAAO,CACZ;MACElI,WAAW,EAAE,eAAAA,CAAA,EAAyB;QAAA,SAAAkN,KAAA,GAAA3D,SAAA,CAAAtG,MAAA,EAAN8I,IAAI,OAAAC,KAAA,CAAAkB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJpB,IAAI,CAAAoB,KAAA,IAAA5D,SAAA,CAAA4D,KAAA;QAAA;QAClC,MAAMhD,KAAK,GAAG4B,IAAI,CAAC,CAAC,CAAC;QACrB,OAAO,MAAMzM,QAAQ,CAAC4M,YAAY,CAChC,IAAI,EACJ,aAAa,EACbH,IAAI,EACJ,UAAU,EACV,YAAY;UAAA,IAAAqB,sBAAA,EAAAC,UAAA;UACVlH,KAAK,CAACgE,KAAK,EAAE/D,MAAM,CAAC;UAEpB,MAAMpH,IAAI,GAAG,MAAMU,MAAM,CAACoB,KAAK,CAACC,YAAY,CAC1C;YAAE,yCAAyC,EAAEoJ;UAAM,CAAC,EACpD;YACE5C,MAAM,EAAE;cACN1D,QAAQ,EAAE,CAAC;cACXyF,MAAM,EAAE;YACV;UACF,CACF,CAAC;UACD,IAAI,CAACtK,IAAI,EACP,MAAM,IAAIU,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,2BAA2B,CAAC;UAE1D,MAAM+H,WAAW,GACf,MAAMrL,IAAI,CACP6E,QAAQ,CAACiC,KAAK,CAACoF,kBAAkB,CAACtB,IAAI,CAAC0D,CAAC,IAAIA,CAAC,CAACnD,KAAK,IAAIA,KAAK,CAAC;UAElE,IAAI,CAACE,WAAW,EACd,OAAO;YACLrF,MAAM,EAAEhG,IAAI,CAACgF,GAAG;YAChBoB,KAAK,EAAE,IAAI1F,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,2BAA2B;UAC1D,CAAC;UAEH,MAAMiL,YAAY,GAChBvO,IAAI,CAACsK,MAAM,CAACM,IAAI,CAACmB,CAAC,IAAIA,CAAC,CAACtB,OAAO,IAAIY,WAAW,CAACZ,OAAO,CAAC;UAEzD,IAAI,CAAC8D,YAAY,EACf,OAAO;YACLvI,MAAM,EAAEhG,IAAI,CAACgF,GAAG;YAChBoB,KAAK,EAAE,IAAI1F,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,0CAA0C;UACzE,CAAC;;UAEH;UACA;UACA;UACA;UACA;UACA,MAAM5C,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC5B;YACEb,GAAG,EAAEhF,IAAI,CAACgF,GAAG;YACb,gBAAgB,EAAEqG,WAAW,CAACZ;UAChC,CAAC,EACD;YACE/E,IAAI,EAAE;cAAE,mBAAmB,EAAE;YAAK,CAAC;YACnC+D,KAAK,EAAE;cAAE,mCAAmC,EAAE;gBAAEgB,OAAO,EAAEY,WAAW,CAACZ;cAAQ;YAAE;UACjF,CAAC,CAAC;UAEJ,KAAA2D,sBAAA,GAAI,CAAAC,UAAA,GAAA/N,QAAQ,EAACkI,gBAAgB,cAAA4F,sBAAA,eAAzBA,sBAAA,CAAA3F,IAAA,CAAA4F,UAAA,EAA4BrO,IAAI,CAAC,EAAE;YACzC,OAAO;cACLgG,MAAM,EAAEhG,IAAI,CAACgF,GAAG;cAChBoB,KAAK,EAAE9F,QAAQ,CAAC+F,YAAY,CAC1B,+DAA+D,EAC/D,KAAK,EACL,aACF;YACF,CAAC;UACH;UAAC,OAAO;YAAEL,MAAM,EAAEhG,IAAI,CAACgF;UAAI,CAAC;QAC1B,CACF,CAAC;MACH;IACF,CAAC,CAAC;;IAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA1E,QAAQ,CAACkO,aAAa,GAAG,OAAOxI,MAAM,EAAEyI,QAAQ,EAAEzC,QAAQ,KAAK;MAC7D7E,KAAK,CAACnB,MAAM,EAAEe,cAAc,CAAC;MAC7BI,KAAK,CAACsH,QAAQ,EAAE1H,cAAc,CAAC;MAC/BI,KAAK,CAAC6E,QAAQ,EAAEhF,KAAK,CAACsB,QAAQ,CAAC8B,OAAO,CAAC,CAAC;MAExC,IAAI4B,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBA,QAAQ,GAAG,KAAK;MAClB;MAEA,MAAMhM,IAAI,GAAG,MAAM2B,WAAW,CAACqE,MAAM,EAAE;QAAEuC,MAAM,EAAE;UAAE+B,MAAM,EAAE;QAAE;MAAE,CAAC,CAAC;MACjE,IAAI,CAACtK,IAAI,EAAE,MAAM,IAAIU,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC;;MAExD;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMoL,qBAAqB,GAAG,IAAIC,MAAM,KAAAtO,MAAA,CAClCK,MAAM,CAACkO,aAAa,CAACH,QAAQ,CAAC,QAClC,GACF,CAAC;;MAED;MACA;MACA,MAAMI,YAAY,GAAG,eAAAA,CAAA,EAA4B;QAAA,IAArBvE,MAAM,GAAAC,SAAA,CAAAtG,MAAA,QAAAsG,SAAA,QAAApC,SAAA,GAAAoC,SAAA,MAAG,EAAE;QAAA,IAAEvF,GAAG,GAAAuF,SAAA,CAAAtG,MAAA,OAAAsG,SAAA,MAAApC,SAAA;QAC1C,IAAI2G,OAAO,GAAG,KAAK;QACnB,KAAK,MAAMhI,KAAK,IAAIwD,MAAM,EAAE;UAC1B,IAAIoE,qBAAqB,CAAC/G,IAAI,CAACb,KAAK,CAAC2D,OAAO,CAAC,EAAE;YAC7C,MAAM/J,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC5B;cACEb,GAAG,EAAEA,GAAG;cACR,gBAAgB,EAAE8B,KAAK,CAAC2D;YAC1B,CAAC,EACD;cACE/E,IAAI,EAAE;gBACJ,kBAAkB,EAAE+I,QAAQ;gBAC5B,mBAAmB,EAAEzC;cACvB;YACF,CACF,CAAC;YACD8C,OAAO,GAAG,IAAI;UAChB;QACF;QACA,OAAOA,OAAO;MAChB,CAAC;MACD,MAAMC,iBAAiB,GAAG,MAAMF,YAAY,CAAC7O,IAAI,CAACsK,MAAM,EAAEtK,IAAI,CAACgF,GAAG,CAAC;;MAEnE;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAI+J,iBAAiB,EAAE;QACrB;MACF;;MAEA;MACA,MAAMzO,QAAQ,CAAC0I,kCAAkC,CAC/C,gBAAgB,EAChB,OAAO,EACPyF,QAAQ,EACRzO,IAAI,CAACgF,GACP,CAAC;MAED,MAAMtE,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC5B;QACEb,GAAG,EAAEhF,IAAI,CAACgF;MACZ,CAAC,EACD;QACEgK,SAAS,EAAE;UACT1E,MAAM,EAAE;YACNG,OAAO,EAAEgE,QAAQ;YACjBzC,QAAQ,EAAEA;UACZ;QACF;MACF,CACF,CAAC;;MAED;MACA;MACA,IAAI;QACF,MAAM1L,QAAQ,CAAC0I,kCAAkC,CAC/C,gBAAgB,EAChB,OAAO,EACPyF,QAAQ,EACRzO,IAAI,CAACgF,GACP,CAAC;MACH,CAAC,CAAC,OAAOiE,EAAE,EAAE;QACX;QACA,MAAMvI,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAC5B;UAAEb,GAAG,EAAEhF,IAAI,CAACgF;QAAI,CAAC,EACjB;UAAEyE,KAAK,EAAE;YAAEa,MAAM,EAAE;cAAEG,OAAO,EAAEgE;YAAS;UAAE;QAAE,CAC7C,CAAC;QACD,MAAMxF,EAAE;MACV;IACF,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA3I,QAAQ,CAAC2O,WAAW,GAClB,OAAOjJ,MAAM,EAAEc,KAAK,KAAK;MACvBK,KAAK,CAACnB,MAAM,EAAEe,cAAc,CAAC;MAC7BI,KAAK,CAACL,KAAK,EAAEC,cAAc,CAAC;MAE5B,MAAM/G,IAAI,GAAG,MAAM2B,WAAW,CAACqE,MAAM,EAAE;QAAEuC,MAAM,EAAE;UAAEvD,GAAG,EAAE;QAAE;MAAE,CAAC,CAAC;MAC9D,IAAI,CAAChF,IAAI,EACP,MAAM,IAAIU,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC;MAE/C,MAAM5C,MAAM,CAACoB,KAAK,CAAC+D,WAAW,CAAC;QAAEb,GAAG,EAAEhF,IAAI,CAACgF;MAAI,CAAC,EAC9C;QAAEyE,KAAK,EAAE;UAAEa,MAAM,EAAE;YAAEG,OAAO,EAAE3D;UAAM;QAAE;MAAE,CAAC,CAAC;IAC9C,CAAC;;IAEH;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA,MAAMoI,UAAU,GACd,MAAMrN,OAAO,IAAI;MACf;MACA;MACAsF,KAAK,CAACtF,OAAO,EAAEmF,KAAK,CAACmI,eAAe,CAAC;QACnCxI,QAAQ,EAAEK,KAAK,CAACsB,QAAQ,CAAClB,MAAM,CAAC;QAChCN,KAAK,EAAEE,KAAK,CAACsB,QAAQ,CAAClB,MAAM,CAAC;QAC7BjE,QAAQ,EAAE6D,KAAK,CAACsB,QAAQ,CAACjB,iBAAiB;MAC5C,CAAC,CAAC,CAAC;MAEH,MAAM;QAAEV,QAAQ;QAAEG,KAAK;QAAE3D;MAAS,CAAC,GAAGtB,OAAO;MAC7C,IAAI,CAAC8E,QAAQ,IAAI,CAACG,KAAK,EACrB,MAAM,IAAIpG,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,iCAAiC,CAAC;MAEhE,MAAMtD,IAAI,GAAG;QAAE6E,QAAQ,EAAE,CAAC;MAAE,CAAC;MAC7B,IAAI1B,QAAQ,EAAE;QACZ,MAAMiM,MAAM,GAAG,MAAM5L,YAAY,CAACL,QAAQ,CAAC;QAC3C,MAAMd,aAAa,GAAG/B,QAAQ,CAAC8B,cAAc,CAAC,CAAC;QAC/C,IAAIC,aAAa,KAAK,KAAK,EAAE;UAC3BrC,IAAI,CAAC6E,QAAQ,CAAC1B,QAAQ,GAAG;YAAE2B,MAAM,EAAEsK;UAAO,CAAC;QAC7C,CAAC,MACI;UACHpP,IAAI,CAAC6E,QAAQ,CAAC1B,QAAQ,GAAG;YAAE7B,MAAM,EAAE8N;UAAO,CAAC;QAC7C;MACF;MAEA,OAAO,MAAM9O,QAAQ,CAAC+O,6BAA6B,CAAC;QAAErP,IAAI;QAAE8G,KAAK;QAAEH,QAAQ;QAAE9E;MAAQ,CAAC,CAAC;IACzF,CAAC;;IAEH;IACAnB,MAAM,CAACwI,OAAO,CACZ;MACEgG,UAAU,EAAE,eAAAA,CAAA,EAAyB;QAAA,SAAAI,KAAA,GAAA/E,SAAA,CAAAtG,MAAA,EAAN8I,IAAI,OAAAC,KAAA,CAAAsC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJxC,IAAI,CAAAwC,KAAA,IAAAhF,SAAA,CAAAgF,KAAA;QAAA;QACjC,MAAM1N,OAAO,GAAGkL,IAAI,CAAC,CAAC,CAAC;QACvB,OAAO,MAAMzM,QAAQ,CAAC4M,YAAY,CAChC,IAAI,EACJ,YAAY,EACZH,IAAI,EACJ,UAAU,EACV,YAAY;UACV;UACA5F,KAAK,CAACtF,OAAO,EAAE2J,MAAM,CAAC;UACtB,IAAIlL,QAAQ,CAAC4B,QAAQ,CAACsN,2BAA2B,EAC/C,OAAO;YACLpJ,KAAK,EAAE,IAAI1F,MAAM,CAAC4C,KAAK,CAAC,GAAG,EAAE,mBAAmB;UAClD,CAAC;UAEH,MAAM0C,MAAM,GAAG,MAAM1F,QAAQ,CAACmP,wBAAwB,CAAC5N,OAAO,CAAC;;UAE/D;UACA,OAAO;YAAEmE,MAAM,EAAEA;UAAO,CAAC;QAC3B,CACF,CAAC;MACH;IACF,CAAC,CAAC;;IAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA1F,QAAQ,CAACmP,wBAAwB,GAC/B,MAAO5N,OAAO,IAAK;MACjBA,OAAO,GAAArC,aAAA,KAAQqC,OAAO,CAAE;MACxB;MACA,MAAMmE,MAAM,GAAG,MAAMkJ,UAAU,CAACrN,OAAO,CAAC;MACxC;MACA;MACA,IAAI,CAACmE,MAAM,EACT,MAAM,IAAI1C,KAAK,CAAC,sCAAsC,CAAC;;MAEzD;MACA;MACA;MACA,IAAIzB,OAAO,CAACiF,KAAK,IAAIxG,QAAQ,CAAC4B,QAAQ,CAAC+L,qBAAqB,EAAE;QAC5D,IAAIpM,OAAO,CAACsB,QAAQ,EAAE;UACpB,MAAM7C,QAAQ,CAAC2N,qBAAqB,CAACjI,MAAM,EAAEnE,OAAO,CAACiF,KAAK,CAAC;QAC7D,CAAC,MAAM;UACL,MAAMxG,QAAQ,CAACuM,mBAAmB,CAAC7G,MAAM,EAAEnE,OAAO,CAACiF,KAAK,CAAC;QAC3D;MACF;MAEA,OAAOd,MAAM;IACf,CAAC;;IAEH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA1F,QAAQ,CAACoP,eAAe,GAAGR,UAAU;;IAErC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA5O,QAAQ,CAAC4O,UAAU,GAAG5O,QAAQ,CAACoP,eAAe;;IAE9C;IACA;IACA;IACA,MAAMhP,MAAM,CAACoB,KAAK,CAAC6N,gBAAgB,CAAC,yCAAyC,EAC3E;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;IACjC,MAAMnP,MAAM,CAACoB,KAAK,CAAC6N,gBAAgB,CAAC,+BAA+B,EACjE;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;IACjC,MAAMnP,MAAM,CAACoB,KAAK,CAAC6N,gBAAgB,CAAC,gCAAgC,EAClE;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;IAAC3O,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/accounts-password.js", "sourcesContent": ["const greet = welcomeMsg => (user, url) => {\n  const greeting =\n    user.profile && user.profile.name\n      ? `Hello ${user.profile.name},`\n      : 'Hello,';\n  return `${greeting}\n\n${welcomeMsg}, simply click the link below.\n\n${url}\n\nThank you.\n`;\n};\n\n/**\n * @summary Options to customize emails sent from the Accounts system.\n * @locus Server\n * @importFromPackage accounts-base\n */\nAccounts.emailTemplates = {\n  ...(Accounts.emailTemplates || {}),\n  from: 'Accounts Example <<EMAIL>>',\n  siteName: Meteor.absoluteUrl()\n    .replace(/^https?:\\/\\//, '')\n    .replace(/\\/$/, ''),\n\n  resetPassword: {\n    subject: () =>\n      `How to reset your password on ${Accounts.emailTemplates.siteName}`,\n    text: greet('To reset your password'),\n  },\n  verifyEmail: {\n    subject: () =>\n      `How to verify email address on ${Accounts.emailTemplates.siteName}`,\n    text: greet('To verify your account email'),\n  },\n  enrollAccount: {\n    subject: () =>\n      `An account has been created for you on ${Accounts.emailTemplates.siteName}`,\n    text: greet('To start using the service'),\n  },\n};\n", "import argon2 from \"argon2\";\nimport { hash as bcryptHash, compare as bcryptCompare } from \"bcrypt\";\nimport { Accounts } from \"meteor/accounts-base\";\n\n// Utility for grabbing user\nconst getUserById =\n  async (id, options) =>\n    await Meteor.users.findOneAsync(id, Accounts._addDefaultFieldSelector(options));\n\n// User records have two fields that are used for password-based login:\n// - 'services.password.bcrypt', which stores the bcrypt password, which will be deprecated\n// - 'services.password.argon2', which stores the argon2 password\n//\n// When the client sends a password to the server, it can either be a\n// string (the plaintext password) or an object with keys 'digest' and\n// 'algorithm' (must be \"sha-256\" for now). The Meteor client always sends\n// password objects { digest: *, algorithm: \"sha-256\" }, but DDP clients\n// that don't have access to SHA can just send plaintext passwords as\n// strings.\n//\n// When the server receives a plaintext password as a string, it always\n// hashes it with SHA256 before passing it into bcrypt / argon2. When the server\n// receives a password as an object, it asserts that the algorithm is\n// \"sha-256\" and then passes the digest to bcrypt / argon2.\n\nAccounts._bcryptRounds = () => Accounts._options.bcryptRounds || 10;\n\nAccounts._argon2Enabled = () => Accounts._options.argon2Enabled || false;\n\nconst ARGON2_TYPES = {\n  argon2i: argon2.argon2i,\n  argon2d: argon2.argon2d,\n  argon2id: argon2.argon2id\n};\n\nAccounts._argon2Type = () => ARGON2_TYPES[Accounts._options.argon2Type] || argon2.argon2id;\nAccounts._argon2TimeCost = () => Accounts._options.argon2TimeCost || 2;\nAccounts._argon2MemoryCost = () => Accounts._options.argon2MemoryCost || 19456;\nAccounts._argon2Parallelism = () => Accounts._options.argon2Parallelism || 1;\n\n/**\n * Extracts the string to be encrypted using bcrypt or Argon2 from the given `password`.\n *\n * @param {string|Object} password - The password provided by the client. It can be:\n *  - A plaintext string password.\n *  - An object with the following properties:\n *      @property {string} digest - The hashed password.\n *      @property {string} algorithm - The hashing algorithm used. Must be \"sha-256\".\n *\n * @returns {string} - The resulting password string to encrypt.\n *\n * @throws {Error} - If the `algorithm` in the password object is not \"sha-256\".\n */\nconst getPasswordString = password => {\n  if (typeof password === \"string\") {\n    password = SHA256(password);\n  }\n  else { // 'password' is an object\n    if (password.algorithm !== \"sha-256\") {\n      throw new Error(\"Invalid password hash algorithm. \" +\n        \"Only 'sha-256' is allowed.\");\n    }\n    password = password.digest;\n  }\n  return password;\n};\n\n/**\n * Encrypt the given `password` using either bcrypt or Argon2.\n * @param password can be a string (in which case it will be run through SHA256 before encryption) or an object with properties `digest` and `algorithm` (in which case we bcrypt or Argon2 `password.digest`).\n * @returns {Promise<string>} The encrypted password.\n */\nconst hashPassword = async (password) => {\n  password = getPasswordString(password);\n  if (Accounts._argon2Enabled() === true) {\n    return await argon2.hash(password, {\n      type: Accounts._argon2Type(),\n      timeCost: Accounts._argon2TimeCost(),\n      memoryCost: Accounts._argon2MemoryCost(),\n      parallelism: Accounts._argon2Parallelism()\n    });\n  }\n  else {\n    return await bcryptHash(password, Accounts._bcryptRounds());\n  }\n};\n\n// Extract the number of rounds used in the specified bcrypt hash.\nconst getRoundsFromBcryptHash = (hash) => {\n  let rounds;\n  if (hash) {\n    const hashSegments = hash.split(\"$\");\n    if (hashSegments.length > 2) {\n      rounds = parseInt(hashSegments[2], 10);\n    }\n  }\n  return rounds;\n};\nAccounts._getRoundsFromBcryptHash = getRoundsFromBcryptHash;\n\n\n/**\n * Extract readable parameters from an Argon2 hash string.\n * @param {string} hash - The Argon2 hash string.\n * @returns {object} An object containing the parsed parameters.\n * @throws {Error} If the hash format is invalid.\n */\nfunction getArgon2Params(hash) {\n  const regex = /^\\$(argon2(?:i|d|id))\\$v=\\d+\\$m=(\\d+),t=(\\d+),p=(\\d+)/;\n\n  const match = hash.match(regex);\n\n  if (!match) {\n    throw new Error(\"Invalid Argon2 hash format.\");\n  }\n\n  const [, type, memoryCost, timeCost, parallelism] = match;\n\n  return {\n    type: ARGON2_TYPES[type],\n    timeCost: parseInt(timeCost, 10),\n    memoryCost: parseInt(memoryCost, 10),\n    parallelism: parseInt(parallelism, 10)\n  };\n}\n\nAccounts._getArgon2Params = getArgon2Params;\n\nconst getUserPasswordHash = user => {\n  return user.services?.password?.argon2 || user.services?.password?.bcrypt;\n};\n\nAccounts._checkPasswordUserFields = { _id: 1, services: 1 };\n\nconst isBcrypt = (hash) => {\n  // bcrypt hashes start with $2a$ or $2b$\n  return hash.startsWith(\"$2\");\n};\n\nconst isArgon = (hash) => {\n    // argon2 hashes start with $argon2i$, $argon2d$ or $argon2id$\n    return hash.startsWith(\"$argon2\");\n}\n\nconst updateUserPasswordDefered = (user, formattedPassword) => {\n  Meteor.defer(async () => {\n    await updateUserPassword(user, formattedPassword);\n  });\n};\n\n/**\n * Hashes the provided password and returns an object that can be used to update the user's password.\n * @param formattedPassword\n * @returns {Promise<{$set: {\"services.password.bcrypt\": string}}|{$unset: {\"services.password.bcrypt\": number}, $set: {\"services.password.argon2\": string}}>}\n */\nconst getUpdatorForUserPassword = async (formattedPassword) => {\n  const encryptedPassword = await hashPassword(formattedPassword);\n  if (Accounts._argon2Enabled() === false) {\n    return {\n      $set: {\n        \"services.password.bcrypt\": encryptedPassword\n      },\n      $unset: {\n        \"services.password.argon2\": 1\n      }\n    };\n  }\n  else if (Accounts._argon2Enabled() === true) {\n    return {\n      $set: {\n        \"services.password.argon2\": encryptedPassword\n      },\n      $unset: {\n        \"services.password.bcrypt\": 1\n      }\n    };\n  }\n};\n\nconst updateUserPassword = async (user, formattedPassword) => {\n  const updator = await getUpdatorForUserPassword(formattedPassword);\n  await Meteor.users.updateAsync({ _id: user._id }, updator);\n};\n\n/**\n * Checks whether the provided password matches the hashed password stored in the user's database record.\n *\n * @param {Object} user - The user object containing at least:\n *   @property {string} _id - The user's unique identifier.\n *   @property {Object} services - The user's services data.\n *   @property {Object} services.password - The user's password object.\n *   @property {string} [services.password.argon2] - The Argon2 hashed password.\n *   @property {string} [services.password.bcrypt] - The bcrypt hashed password, deprecated\n *\n * @param {string|Object} password - The password provided by the client. It can be:\n *   - A plaintext string password.\n *   - An object with the following properties:\n *       @property {string} digest - The hashed password.\n *       @property {string} algorithm - The hashing algorithm used. Must be \"sha-256\".\n *\n * @returns {Promise<Object>} - A result object with the following properties:\n *   @property {string} userId - The user's unique identifier.\n *   @property {Object} [error] - An error object if the password does not match or an error occurs.\n *\n * @throws {Error} - If an unexpected error occurs during the process.\n */\nconst checkPasswordAsync = async (user, password) => {\n  const result = {\n    userId: user._id\n  };\n\n  const formattedPassword = getPasswordString(password);\n  const hash = getUserPasswordHash(user);\n\n\n  const argon2Enabled = Accounts._argon2Enabled();\n  if (argon2Enabled === false) {\n    if (isArgon(hash)) {\n      // this is a rollback feature, enabling to switch back from argon2 to bcrypt if needed\n      // TODO : deprecate this\n      console.warn(\"User has an argon2 password and argon2 is not enabled, rolling back to bcrypt encryption\");\n      const match = await argon2.verify(hash, formattedPassword);\n      if (!match) {\n        result.error = Accounts._handleError(\"Incorrect password\", false);\n      }\n      else{\n        // The password checks out, but the user's stored password needs to be updated to argon2\n        updateUserPasswordDefered(user, { digest: formattedPassword, algorithm: \"sha-256\" });\n      }\n    }\n    else {\n      const hashRounds = getRoundsFromBcryptHash(hash);\n      const match = await bcryptCompare(formattedPassword, hash);\n      if (!match) {\n        result.error = Accounts._handleError(\"Incorrect password\", false);\n      }\n      else if (hash) {\n        const paramsChanged = hashRounds !== Accounts._bcryptRounds();\n        // The password checks out, but the user's bcrypt hash needs to be updated\n        // to match current bcrypt settings\n        if (paramsChanged === true) {\n          updateUserPasswordDefered(user, { digest: formattedPassword, algorithm: \"sha-256\" });\n        }\n      }\n    }\n  }\n  else if (argon2Enabled === true) {\n    if (isBcrypt(hash)) {\n      // migration code from bcrypt to argon2\n      const match = await bcryptCompare(formattedPassword, hash);\n      if (!match) {\n        result.error = Accounts._handleError(\"Incorrect password\", false);\n      }\n      else {\n        // The password checks out, but the user's stored password needs to be updated to argon2\n        updateUserPasswordDefered(user, { digest: formattedPassword, algorithm: \"sha-256\" });\n      }\n    }\n    else {\n      // argon2 password\n      const argon2Params = getArgon2Params(hash);\n      const match = await argon2.verify(hash, formattedPassword);\n      if (!match) {\n        result.error = Accounts._handleError(\"Incorrect password\", false);\n      }\n      else if (hash) {\n        const paramsChanged = argon2Params.memoryCost !== Accounts._argon2MemoryCost() ||\n          argon2Params.timeCost !== Accounts._argon2TimeCost() ||\n          argon2Params.parallelism !== Accounts._argon2Parallelism() ||\n          argon2Params.type !== Accounts._argon2Type();\n        if (paramsChanged === true) {\n          // The password checks out, but the user's argon2 hash needs to be updated with the right params\n          updateUserPasswordDefered(user, { digest: formattedPassword, algorithm: \"sha-256\" });\n        }\n      }\n    }\n  }\n\n\n  return result;\n};\n\nAccounts._checkPasswordAsync = checkPasswordAsync;\n\n///\n/// LOGIN\n///\n\n\n/**\n * @summary Finds the user asynchronously with the specified username.\n * First tries to match username case sensitively; if that fails, it\n * tries case insensitively; but if more than one user matches the case\n * insensitive search, it returns null.\n * @locus Server\n * @param {String} username The username to look for\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n * @returns {Promise<Object>} A user if found, else null\n * @importFromPackage accounts-base\n */\nAccounts.findUserByUsername =\n  async (username, options) =>\n    await Accounts._findUserByQuery({ username }, options);\n\n/**\n * @summary Finds the user asynchronously with the specified email.\n * First tries to match email case sensitively; if that fails, it\n * tries case insensitively; but if more than one user matches the case\n * insensitive search, it returns null.\n * @locus Server\n * @param {String} email The email address to look for\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n * @returns {Promise<Object>} A user if found, else null\n * @importFromPackage accounts-base\n */\nAccounts.findUserByEmail =\n  async (email, options) =>\n    await Accounts._findUserByQuery({ email }, options);\n\n// XXX maybe this belongs in the check package\nconst NonEmptyString = Match.Where(x => {\n  check(x, String);\n  return x.length > 0;\n});\n\nconst passwordValidator = Match.OneOf(\n  Match.Where(str => Match.test(str, String) && str.length <= Meteor.settings?.packages?.accounts?.passwordMaxLength || 256), {\n    digest: Match.Where(str => Match.test(str, String) && str.length === 64),\n    algorithm: Match.OneOf('sha-256')\n  }\n);\n\n// Handler to login with a password.\n//\n// The Meteor client sets options.password to an object with keys\n// 'digest' (set to SHA256(password)) and 'algorithm' (\"sha-256\").\n//\n// For other DDP clients which don't have access to SHA, the handler\n// also accepts the plaintext password in options.password as a string.\n//\n// (It might be nice if servers could turn the plaintext password\n// option off. Or maybe it should be opt-in, not opt-out?\n// Accounts.config option?)\n//\n// Note that neither password option is secure without SSL.\n//\nAccounts.registerLoginHandler(\"password\", async options => {\n  if (!options.password)\n    return undefined; // don't handle\n\n  check(options, {\n    user: Accounts._userQueryValidator,\n    password: passwordValidator,\n    code: Match.Optional(NonEmptyString),\n  });\n\n\n  const user = await Accounts._findUserByQuery(options.user, {fields: {\n    services: 1,\n    ...Accounts._checkPasswordUserFields,\n  }});\n  if (!user) {\n    Accounts._handleError(\"User not found\");\n  }\n\n  if (!getUserPasswordHash(user)) {\n    Accounts._handleError(\"User has no password set\");\n  }\n\n  const result = await checkPasswordAsync(user, options.password);\n  // This method is added by the package accounts-2fa\n  // First the login is validated, then the code situation is checked\n  if (\n    !result.error &&\n    Accounts._check2faEnabled?.(user)\n  ) {\n    if (!options.code) {\n      Accounts._handleError('2FA code must be informed', true, 'no-2fa-code');\n    }\n    if (\n      !Accounts._isTokenValid(\n        user.services.twoFactorAuthentication.secret,\n        options.code\n      )\n    ) {\n      Accounts._handleError('Invalid 2FA code', true, 'invalid-2fa-code');\n    }\n  }\n\n  return result;\n});\n\n///\n/// CHANGING\n///\n\n/**\n * @summary Change a user's username asynchronously. Use this instead of updating the\n * database directly. The operation will fail if there is an existing user\n * with a username only differing in case.\n * @locus Server\n * @param {String} userId The ID of the user to update.\n * @param {String} newUsername A new username for the user.\n * @importFromPackage accounts-base\n */\nAccounts.setUsername =\n  async (userId, newUsername) => {\n    check(userId, NonEmptyString);\n    check(newUsername, NonEmptyString);\n\n    const user = await getUserById(userId, {\n      fields: {\n        username: 1,\n      }\n    });\n\n    if (!user) {\n      Accounts._handleError(\"User not found\");\n    }\n\n    const oldUsername = user.username;\n\n    // Perform a case insensitive check for duplicates before update\n    await Accounts._checkForCaseInsensitiveDuplicates('username',\n      'Username', newUsername, user._id);\n\n    await Meteor.users.updateAsync({ _id: user._id }, { $set: { username: newUsername } });\n\n    // Perform another check after update, in case a matching user has been\n    // inserted in the meantime\n    try {\n      await Accounts._checkForCaseInsensitiveDuplicates('username',\n        'Username', newUsername, user._id);\n    } catch (ex) {\n      // Undo update if the check fails\n      await Meteor.users.updateAsync({ _id: user._id }, { $set: { username: oldUsername } });\n      throw ex;\n    }\n  };\n\n// Let the user change their own password if they know the old\n// password. `oldPassword` and `newPassword` should be objects with keys\n// `digest` and `algorithm` (representing the SHA256 of the password).\nMeteor.methods(\n  {\n    changePassword: async function(oldPassword, newPassword) {\n      check(oldPassword, passwordValidator);\n      check(newPassword, passwordValidator);\n\n      if (!this.userId) {\n        throw new Meteor.Error(401, \"Must be logged in\");\n      }\n\n      const user = await getUserById(this.userId, {\n        fields: {\n          services: 1,\n          ...Accounts._checkPasswordUserFields\n        }\n      });\n      if (!user) {\n        Accounts._handleError(\"User not found\");\n      }\n\n      if (!getUserPasswordHash(user)) {\n        Accounts._handleError(\"User has no password set\");\n      }\n\n      const result = await checkPasswordAsync(user, oldPassword);\n      if (result.error) {\n        throw result.error;\n      }\n\n      // It would be better if this removed ALL existing tokens and replaced\n      // the token for the current connection with a new one, but that would\n      // be tricky, so we'll settle for just replacing all tokens other than\n      // the one for the current connection.\n      const currentToken = Accounts._getLoginToken(this.connection.id);\n      const updator = await getUpdatorForUserPassword(newPassword);\n\n      await Meteor.users.updateAsync(\n        { _id: this.userId },\n        {\n          $set: updator.$set,\n          $pull: {\n            \"services.resume.loginTokens\": { hashedToken: { $ne: currentToken } }\n          },\n          $unset: { \"services.password.reset\": 1, ...updator.$unset }\n        }\n      );\n\n      return { passwordChanged: true };\n    }\n  });\n\n\n// Force change the users password.\n\n/**\n * @summary Forcibly change the password for a user.\n * @locus Server\n * @param {String} userId The id of the user to update.\n * @param {String} newPlaintextPassword A new password for the user.\n * @param {Object} [options]\n * @param {Object} options.logout Logout all current connections with this userId (default: true)\n * @importFromPackage accounts-base\n */\nAccounts.setPasswordAsync =\n  async (userId, newPlaintextPassword, options) => {\n    check(userId, String);\n    check(newPlaintextPassword, Match.Where(str => Match.test(str, String) && str.length <= Meteor.settings?.packages?.accounts?.passwordMaxLength || 256));\n    check(options, Match.Maybe({ logout: Boolean }));\n    options = { logout: true, ...options };\n\n    const user = await getUserById(userId, { fields: { _id: 1 } });\n    if (!user) {\n      throw new Meteor.Error(403, \"User not found\");\n    }\n\n    let updator = await getUpdatorForUserPassword(newPlaintextPassword);\n    updator.$unset = updator.$unset || {};\n    updator.$unset[\"services.password.reset\"] = 1;\n\n    if (options.logout) {\n      updator.$unset[\"services.resume.loginTokens\"] = 1;\n    }\n\n    await Meteor.users.updateAsync({ _id: user._id }, updator);\n  };\n\n///\n/// RESETTING VIA EMAIL\n///\n\n// Utility for plucking addresses from emails\nconst pluckAddresses = (emails = []) => emails.map(email => email.address);\n\n// Method called by a user to request a password reset email. This is\n// the start of the reset process.\nMeteor.methods({forgotPassword: async options => {\n  check(options, {email: String})\n\n  const user = await Accounts.findUserByEmail(options.email, { fields: { emails: 1 } });\n\n  if (!user) {\n    Accounts._handleError(\"User not found\");\n  }\n\n  const emails = pluckAddresses(user.emails);\n  const caseSensitiveEmail = emails.find(\n    email => email.toLowerCase() === options.email.toLowerCase()\n  );\n\n  await Accounts.sendResetPasswordEmail(user._id, caseSensitiveEmail);\n}});\n\n/**\n * @summary Asynchronously generates a reset token and saves it into the database.\n * @locus Server\n * @param {String} userId The id of the user to generate the reset token for.\n * @param {String} email Which address of the user to generate the reset token for. This address must be in the user's `emails` list. If `null`, defaults to the first email in the list.\n * @param {String} reason `resetPassword` or `enrollAccount`.\n * @param {Object} [extraTokenData] Optional additional data to be added into the token record.\n * @returns {Promise<Object>} Promise of an object with {email, user, token} values.\n * @importFromPackage accounts-base\n */\nAccounts.generateResetToken =\n  async (userId, email, reason, extraTokenData) => {\n  // Make sure the user exists, and email is one of their addresses.\n  // Don't limit the fields in the user object since the user is returned\n  // by the function and some other fields might be used elsewhere.\n  const user = await getUserById(userId);\n  if (!user) {\n    Accounts._handleError(\"Can't find user\");\n  }\n\n  // pick the first email if we weren't passed an email.\n  if (!email && user.emails && user.emails[0]) {\n    email = user.emails[0].address;\n  }\n\n  // make sure we have a valid email\n  if (!email ||\n    !(pluckAddresses(user.emails).includes(email))) {\n    Accounts._handleError(\"No such email for user.\");\n  }\n\n  const token = Random.secret();\n  const tokenRecord = {\n    token,\n    email,\n    when: new Date()\n  };\n\n  if (reason === 'resetPassword') {\n    tokenRecord.reason = 'reset';\n  } else if (reason === 'enrollAccount') {\n    tokenRecord.reason = 'enroll';\n  } else if (reason) {\n    // fallback so that this function can be used for unknown reasons as well\n    tokenRecord.reason = reason;\n  }\n\n  if (extraTokenData) {\n    Object.assign(tokenRecord, extraTokenData);\n  }\n  // if this method is called from the enroll account work-flow then\n  // store the token record in 'services.password.enroll' db field\n  // else store the token record in in 'services.password.reset' db field\n  if (reason === \"enrollAccount\") {\n    await Meteor.users.updateAsync(\n      { _id: user._id },\n      {\n        $set: {\n          \"services.password.enroll\": tokenRecord\n        }\n      }\n    );\n    // before passing to template, update user object with new token\n    Meteor._ensure(user, \"services\", \"password\").enroll = tokenRecord;\n  }\n  else {\n    await Meteor.users.updateAsync(\n      { _id: user._id },\n      {\n        $set: {\n          \"services.password.reset\": tokenRecord\n        }\n      }\n    );\n    // before passing to template, update user object with new token\n    Meteor._ensure(user, \"services\", \"password\").reset = tokenRecord;\n  }\n\n  return { email, user, token };\n};\n\n/**\n * @summary Generates asynchronously an e-mail verification token and saves it into the database.\n * @locus Server\n * @param {String} userId The id of the user to generate the  e-mail verification token for.\n * @param {String} email Which address of the user to generate the e-mail verification token for. This address must be in the user's `emails` list. If `null`, defaults to the first unverified email in the list.\n * @param {Object} [extraTokenData] Optional additional data to be added into the token record.\n * @returns {Promise<Object>} Promise of an object with {email, user, token} values.\n * @importFromPackage accounts-base\n */\nAccounts.generateVerificationToken =\n  async (userId, email, extraTokenData) => {\n  // Make sure the user exists, and email is one of their addresses.\n  // Don't limit the fields in the user object since the user is returned\n  // by the function and some other fields might be used elsewhere.\n  const user = await getUserById(userId);\n  if (!user) {\n    Accounts._handleError(\"Can't find user\");\n  }\n\n  // pick the first unverified email if we weren't passed an email.\n  if (!email) {\n    const emailRecord = (user.emails || []).find(e => !e.verified);\n    email = (emailRecord || {}).address;\n\n    if (!email) {\n      Accounts._handleError(\"That user has no unverified email addresses.\");\n    }\n  }\n\n  // make sure we have a valid email\n  if (!email ||\n    !(pluckAddresses(user.emails).includes(email))) {\n    Accounts._handleError(\"No such email for user.\");\n  }\n\n  const token = Random.secret();\n  const tokenRecord = {\n    token,\n    // TODO: This should probably be renamed to \"email\" to match reset token record.\n    address: email,\n    when: new Date()\n  };\n\n  if (extraTokenData) {\n    Object.assign(tokenRecord, extraTokenData);\n  }\n\n  await Meteor.users.updateAsync({_id: user._id}, {$push: {\n    'services.email.verificationTokens': tokenRecord\n  }});\n\n  // before passing to template, update user object with new token\n  Meteor._ensure(user, 'services', 'email');\n  if (!user.services.email.verificationTokens) {\n    user.services.email.verificationTokens = [];\n  }\n  user.services.email.verificationTokens.push(tokenRecord);\n\n  return {email, user, token};\n};\n\n\n// send the user an email with a link that when opened allows the user\n// to set a new password, without the old password.\n\n/**\n * @summary Send an email asynchronously with a link the user can use to reset their password.\n * @locus Server\n * @param {String} userId The id of the user to send email to.\n * @param {String} [email] Optional. Which address of the user's to send the email to. This address must be in the user's `emails` list. Defaults to the first email in the list.\n * @param {Object} [extraTokenData] Optional additional data to be added into the token record.\n * @param {Object} [extraParams] Optional additional params to be added to the reset url.\n * @returns {Promise<Object>} Promise of an object with {email, user, token, url, options} values.\n * @importFromPackage accounts-base\n */\nAccounts.sendResetPasswordEmail =\n  async (userId, email, extraTokenData, extraParams) => {\n    const { email: realEmail, user, token } =\n      await Accounts.generateResetToken(userId, email, 'resetPassword', extraTokenData);\n    const url = Accounts.urls.resetPassword(token, extraParams);\n    const options = await Accounts.generateOptionsForEmail(realEmail, user, url, 'resetPassword');\n    await Email.sendAsync(options);\n\n    if (Meteor.isDevelopment && !Meteor.isPackageTest) {\n      console.log(`\\nReset password URL: ${ url }`);\n    }\n    return { email: realEmail, user, token, url, options };\n  };\n\n// send the user an email informing them that their account was created, with\n// a link that when opened both marks their email as verified and forces them\n// to choose their password. The email must be one of the addresses in the\n// user's emails field, or undefined to pick the first email automatically.\n//\n// This is not called automatically. It must be called manually if you\n// want to use enrollment emails.\n\n/**\n * @summary Send an email asynchronously with a link the user can use to set their initial password.\n * @locus Server\n * @param {String} userId The id of the user to send email to.\n * @param {String} [email] Optional. Which address of the user's to send the email to. This address must be in the user's `emails` list. Defaults to the first email in the list.\n * @param {Object} [extraTokenData] Optional additional data to be added into the token record.\n * @param {Object} [extraParams] Optional additional params to be added to the enrollment url.\n * @returns {Promise<Object>} Promise of an object {email, user, token, url, options} values.\n * @importFromPackage accounts-base\n */\nAccounts.sendEnrollmentEmail =\n  async (userId, email, extraTokenData, extraParams) => {\n\n    const { email: realEmail, user, token } =\n      await Accounts.generateResetToken(userId, email, 'enrollAccount', extraTokenData);\n\n    const url = Accounts.urls.enrollAccount(token, extraParams);\n\n    const options =\n      await Accounts.generateOptionsForEmail(realEmail, user, url, 'enrollAccount');\n\n    await Email.sendAsync(options);\n    if (Meteor.isDevelopment && !Meteor.isPackageTest) {\n      console.log(`\\nEnrollment email URL: ${ url }`);\n    }\n    return { email: realEmail, user, token, url, options };\n  };\n\n\n// Take token from sendResetPasswordEmail or sendEnrollmentEmail, change\n// the users password, and log them in.\nMeteor.methods(\n  {\n    resetPassword:\n      async function (...args) {\n        const token = args[0];\n        const newPassword = args[1];\n        return await Accounts._loginMethod(\n          this,\n          \"resetPassword\",\n          args,\n          \"password\",\n          async () => {\n            check(token, String);\n            check(newPassword, passwordValidator);\n            let user = await Meteor.users.findOneAsync(\n              { \"services.password.reset.token\": token },\n              {\n                fields: {\n                  services: 1,\n                  emails: 1,\n                }\n              }\n            );\n\n            let isEnroll = false;\n            // if token is in services.password.reset db field implies\n            // this method is was not called from enroll account workflow\n            // else this method is called from enroll account workflow\n            if (!user) {\n              user = await Meteor.users.findOneAsync(\n                { \"services.password.enroll.token\": token },\n                {\n                  fields: {\n                    services: 1,\n                    emails: 1,\n                  }\n                }\n              );\n              isEnroll = true;\n            }\n            if (!user) {\n              throw new Meteor.Error(403, \"Token expired\");\n            }\n            let tokenRecord = {};\n            if (isEnroll) {\n              tokenRecord = user.services.password.enroll;\n            } else {\n              tokenRecord = user.services.password.reset;\n            }\n            const { when, email } = tokenRecord;\n            let tokenLifetimeMs = Accounts._getPasswordResetTokenLifetimeMs();\n            if (isEnroll) {\n              tokenLifetimeMs = Accounts._getPasswordEnrollTokenLifetimeMs();\n            }\n            const currentTimeMs = Date.now();\n            if ((currentTimeMs - when) > tokenLifetimeMs)\n              throw new Meteor.Error(403, \"Token expired\");\n            if (!(pluckAddresses(user.emails).includes(email)))\n              return {\n                userId: user._id,\n                error: new Meteor.Error(403, \"Token has invalid email address\")\n              };\n\n            // NOTE: We're about to invalidate tokens on the user, who we might be\n            // logged in as. Make sure to avoid logging ourselves out if this\n            // happens. But also make sure not to leave the connection in a state\n            // of having a bad token set if things fail.\n            const oldToken = Accounts._getLoginToken(this.connection.id);\n            Accounts._setLoginToken(user._id, this.connection, null);\n            const resetToOldToken = () =>\n              Accounts._setLoginToken(user._id, this.connection, oldToken);\n\n            const updator = await getUpdatorForUserPassword(newPassword);\n\n            try {\n              // Update the user record by:\n              // - Changing the password to the new one\n              // - Forgetting about the reset token or enroll token that was just used\n              // - Verifying their email, since they got the password reset via email.\n              let affectedRecords = {};\n              // if reason is enroll then check services.password.enroll.token field for affected records\n              if (isEnroll) {\n                affectedRecords = await Meteor.users.updateAsync(\n                  {\n                    _id: user._id,\n                    \"emails.address\": email,\n                    \"services.password.enroll.token\": token\n                  },\n                  {\n                    $set: {\n                      \"emails.$.verified\": true,\n                      ...updator.$set\n                    },\n                    $unset: {\n                      \"services.password.enroll\": 1,\n                      ...updator.$unset\n                    }\n                  });\n              }\n              else {\n                affectedRecords = await Meteor.users.updateAsync(\n                  {\n                    _id: user._id,\n                    \"emails.address\": email,\n                    \"services.password.reset.token\": token\n                  },\n                  {\n                    $set: {\n                      \"emails.$.verified\": true,\n                      ...updator.$set\n                    },\n                    $unset: {\n                      \"services.password.reset\": 1,\n                      ...updator.$unset\n                    }\n                  });\n              }\n              if (affectedRecords !== 1)\n                return {\n                  userId: user._id,\n                  error: new Meteor.Error(403, \"Invalid email\")\n                };\n            } catch (err) {\n              resetToOldToken();\n              throw err;\n            }\n\n            // Replace all valid login tokens with new ones (changing\n            // password should invalidate existing sessions).\n            await Accounts._clearAllLoginTokens(user._id);\n\n            if (Accounts._check2faEnabled?.(user)) {\n              return {\n                userId: user._id,\n                error: Accounts._handleError(\n                  'Changed password, but user not logged in because 2FA is enabled',\n                  false,\n                  '2fa-enabled'\n                ),\n              };\n            }\n            return { userId: user._id };\n          }\n        );\n      }\n  }\n);\n\n///\n/// EMAIL VERIFICATION\n///\n\n\n// send the user an email with a link that when opened marks that\n// address as verified\n\n/**\n * @summary Send an email asynchronously with a link the user can use verify their email address.\n * @locus Server\n * @param {String} userId The id of the user to send email to.\n * @param {String} [email] Optional. Which address of the user's to send the email to. This address must be in the user's `emails` list. Defaults to the first unverified email in the list.\n * @param {Object} [extraTokenData] Optional additional data to be added into the token record.\n * @param {Object} [extraParams] Optional additional params to be added to the verification url.\n * @returns {Promise<Object>} Promise of an object with {email, user, token, url, options} values.\n * @importFromPackage accounts-base\n */\nAccounts.sendVerificationEmail =\n  async (userId, email, extraTokenData, extraParams) => {\n    // XXX Also generate a link using which someone can delete this\n    // account if they own said address but weren't those who created\n    // this account.\n\n    const { email: realEmail, user, token } =\n      await Accounts.generateVerificationToken(userId, email, extraTokenData);\n    const url = Accounts.urls.verifyEmail(token, extraParams);\n    const options = await Accounts.generateOptionsForEmail(realEmail, user, url, 'verifyEmail');\n    await Email.sendAsync(options);\n    if (Meteor.isDevelopment && !Meteor.isPackageTest) {\n      console.log(`\\nVerification email URL: ${ url }`);\n    }\n    return { email: realEmail, user, token, url, options };\n  };\n\n// Take token from sendVerificationEmail, mark the email as verified,\n// and log them in.\nMeteor.methods(\n  {\n    verifyEmail: async function (...args) {\n      const token = args[0];\n      return await Accounts._loginMethod(\n        this,\n        \"verifyEmail\",\n        args,\n        \"password\",\n        async () => {\n          check(token, String);\n\n          const user = await Meteor.users.findOneAsync(\n            { 'services.email.verificationTokens.token': token },\n            {\n              fields: {\n                services: 1,\n                emails: 1,\n              }\n            }\n          );\n          if (!user)\n            throw new Meteor.Error(403, \"Verify email link expired\");\n\n          const tokenRecord =\n            await user\n              .services.email.verificationTokens.find(t => t.token == token);\n\n          if (!tokenRecord)\n            return {\n              userId: user._id,\n              error: new Meteor.Error(403, \"Verify email link expired\")\n            };\n\n          const emailsRecord =\n            user.emails.find(e => e.address == tokenRecord.address);\n\n          if (!emailsRecord)\n            return {\n              userId: user._id,\n              error: new Meteor.Error(403, \"Verify email link is for unknown address\")\n            };\n\n          // By including the address in the query, we can use 'emails.$' in the\n          // modifier to get a reference to the specific object in the emails\n          // array. See\n          // http://www.mongodb.org/display/DOCS/Updating/#Updating-The%24positionaloperator)\n          // http://www.mongodb.org/display/DOCS/Updating#Updating-%24pull\n          await Meteor.users.updateAsync(\n            {\n              _id: user._id,\n              'emails.address': tokenRecord.address\n            },\n            {\n              $set: { 'emails.$.verified': true },\n              $pull: { 'services.email.verificationTokens': { address: tokenRecord.address } }\n            });\n\n          if (Accounts._check2faEnabled?.(user)) {\n        return {\n          userId: user._id,\n          error: Accounts._handleError(\n            'Email verified, but user not logged in because 2FA is enabled',\n            false,\n            '2fa-enabled'\n          ),\n        };\n      }return { userId: user._id };\n        }\n      );\n    }\n  });\n\n/**\n * @summary Asynchronously add an email address for a user. Use this instead of directly\n * updating the database. The operation will fail if there is a different user\n * with an email only differing in case. If the specified user has an existing\n * email only differing in case however, we replace it.\n * @locus Server\n * @param {String} userId The ID of the user to update.\n * @param {String} newEmail A new email address for the user.\n * @param {Boolean} [verified] Optional - whether the new email address should\n * be marked as verified. Defaults to false.\n * @importFromPackage accounts-base\n */\nAccounts.addEmailAsync = async (userId, newEmail, verified) => {\n  check(userId, NonEmptyString);\n  check(newEmail, NonEmptyString);\n  check(verified, Match.Optional(Boolean));\n\n  if (verified === void 0) {\n    verified = false;\n  }\n\n  const user = await getUserById(userId, { fields: { emails: 1 } });\n  if (!user) throw new Meteor.Error(403, \"User not found\");\n\n  // Allow users to change their own email to a version with a different case\n\n  // We don't have to call checkForCaseInsensitiveDuplicates to do a case\n  // insensitive check across all emails in the database here because: (1) if\n  // there is no case-insensitive duplicate between this user and other users,\n  // then we are OK and (2) if this would create a conflict with other users\n  // then there would already be a case-insensitive duplicate and we can't fix\n  // that in this code anyway.\n  const caseInsensitiveRegExp = new RegExp(\n    `^${Meteor._escapeRegExp(newEmail)}$`,\n    \"i\"\n  );\n\n  // TODO: This is a linear search. If we have a lot of emails.\n  //  we should consider using a different data structure.\n  const updatedEmail = async (emails = [], _id) => {\n    let updated = false;\n    for (const email of emails) {\n      if (caseInsensitiveRegExp.test(email.address)) {\n        await Meteor.users.updateAsync(\n          {\n            _id: _id,\n            \"emails.address\": email.address,\n          },\n          {\n            $set: {\n              \"emails.$.address\": newEmail,\n              \"emails.$.verified\": verified,\n            },\n          }\n        );\n        updated = true;\n      }\n    }\n    return updated;\n  };\n  const didUpdateOwnEmail = await updatedEmail(user.emails, user._id);\n\n  // In the other updates below, we have to do another call to\n  // checkForCaseInsensitiveDuplicates to make sure that no conflicting values\n  // were added to the database in the meantime. We don't have to do this for\n  // the case where the user is updating their email address to one that is the\n  // same as before, but only different because of capitalization. Read the\n  // big comment above to understand why.\n\n  if (didUpdateOwnEmail) {\n    return;\n  }\n\n  // Perform a case insensitive check for duplicates before update\n  await Accounts._checkForCaseInsensitiveDuplicates(\n    \"emails.address\",\n    \"Email\",\n    newEmail,\n    user._id\n  );\n\n  await Meteor.users.updateAsync(\n    {\n      _id: user._id,\n    },\n    {\n      $addToSet: {\n        emails: {\n          address: newEmail,\n          verified: verified,\n        },\n      },\n    }\n  );\n\n  // Perform another check after update, in case a matching user has been\n  // inserted in the meantime\n  try {\n    await Accounts._checkForCaseInsensitiveDuplicates(\n      \"emails.address\",\n      \"Email\",\n      newEmail,\n      user._id\n    );\n  } catch (ex) {\n    // Undo update if the check fails\n    await Meteor.users.updateAsync(\n      { _id: user._id },\n      { $pull: { emails: { address: newEmail } } }\n    );\n    throw ex;\n  }\n};\n\n/**\n * @summary Remove an email address asynchronously for a user. Use this instead of updating\n * the database directly.\n * @locus Server\n * @param {String} userId The ID of the user to update.\n * @param {String} email The email address to remove.\n * @importFromPackage accounts-base\n */\nAccounts.removeEmail =\n  async (userId, email) => {\n    check(userId, NonEmptyString);\n    check(email, NonEmptyString);\n\n    const user = await getUserById(userId, { fields: { _id: 1 } });\n    if (!user)\n      throw new Meteor.Error(403, \"User not found\");\n\n    await Meteor.users.updateAsync({ _id: user._id },\n      { $pull: { emails: { address: email } } });\n  }\n\n///\n/// CREATING USERS\n///\n\n// Shared createUser function called from the createUser method, both\n// if originates in client or server code. Calls user provided hooks,\n// does the actual user insertion.\n//\n// returns the user id\nconst createUser =\n  async options => {\n    // Unknown keys allowed, because a onCreateUserHook can take arbitrary\n    // options.\n    check(options, Match.ObjectIncluding({\n      username: Match.Optional(String),\n      email: Match.Optional(String),\n      password: Match.Optional(passwordValidator)\n    }));\n\n    const { username, email, password } = options;\n    if (!username && !email)\n      throw new Meteor.Error(400, \"Need to set a username or email\");\n\n    const user = { services: {} };\n    if (password) {\n      const hashed = await hashPassword(password);\n      const argon2Enabled = Accounts._argon2Enabled();\n      if (argon2Enabled === false) {\n        user.services.password = { bcrypt: hashed };\n      }\n      else {\n        user.services.password = { argon2: hashed };\n      }\n    }\n\n    return await Accounts._createUserCheckingDuplicates({ user, email, username, options });\n  };\n\n// method for create user. Requests come from the client.\nMeteor.methods(\n  {\n    createUser: async function (...args) {\n      const options = args[0];\n      return await Accounts._loginMethod(\n        this,\n        \"createUser\",\n        args,\n        \"password\",\n        async () => {\n          // createUser() above does more checking.\n          check(options, Object);\n          if (Accounts._options.forbidClientAccountCreation)\n            return {\n              error: new Meteor.Error(403, \"Signups forbidden\")\n            };\n\n          const userId = await Accounts.createUserVerifyingEmail(options);\n\n          // client gets logged in as the new user afterwards.\n          return { userId: userId };\n        }\n      );\n    }\n  });\n\n/**\n * @summary Creates an user asynchronously and sends an email if `options.email` is informed.\n * Then if the `sendVerificationEmail` option from the `Accounts` package is\n * enabled, you'll send a verification email if `options.password` is informed,\n * otherwise you'll send an enrollment email.\n * @locus Server\n * @param {Object} options The options object to be passed down when creating\n * the user\n * @param {String} options.username A unique name for this user.\n * @param {String} options.email The user's email address.\n * @param {String} options.password The user's password. This is __not__ sent in plain text over the wire.\n * @param {Object} options.profile The user's profile, typically including the `name` field.\n * @importFromPackage accounts-base\n * */\nAccounts.createUserVerifyingEmail =\n  async (options) => {\n    options = { ...options };\n    // Create user. result contains id and token.\n    const userId = await createUser(options);\n    // safety belt. createUser is supposed to throw on error. send 500 error\n    // instead of sending a verification email with empty userid.\n    if (!userId)\n      throw new Error(\"createUser failed to insert new user\");\n\n    // If `Accounts._options.sendVerificationEmail` is set, register\n    // a token to verify the user's primary email, and send it to\n    // that address.\n    if (options.email && Accounts._options.sendVerificationEmail) {\n      if (options.password) {\n        await Accounts.sendVerificationEmail(userId, options.email);\n      } else {\n        await Accounts.sendEnrollmentEmail(userId, options.email);\n      }\n    }\n\n    return userId;\n  };\n\n// Create user directly on the server.\n//\n// Unlike the client version, this does not log you in as this user\n// after creation.\n//\n// returns Promise<userId> or throws an error if it can't create\n//\n// XXX add another argument (\"server options\") that gets sent to onCreateUser,\n// which is always empty when called from the createUser method? eg, \"admin:\n// true\", which we want to prevent the client from setting, but which a custom\n// method calling Accounts.createUser could set?\n//\n\nAccounts.createUserAsync = createUser\n\n// Create user directly on the server.\n//\n// Unlike the client version, this does not log you in as this user\n// after creation.\n//\n// returns userId or throws an error if it can't create\n//\n// XXX add another argument (\"server options\") that gets sent to onCreateUser,\n// which is always empty when called from the createUser method? eg, \"admin:\n// true\", which we want to prevent the client from setting, but which a custom\n// method calling Accounts.createUser could set?\n//\n\nAccounts.createUser = Accounts.createUserAsync;\n\n///\n/// PASSWORD-SPECIFIC INDEXES ON USERS\n///\nawait Meteor.users.createIndexAsync('services.email.verificationTokens.token',\n  { unique: true, sparse: true });\nawait Meteor.users.createIndexAsync('services.password.reset.token',\n  { unique: true, sparse: true });\nawait Meteor.users.createIndexAsync('services.password.enroll.token',\n  { unique: true, sparse: true });\n\n"]}