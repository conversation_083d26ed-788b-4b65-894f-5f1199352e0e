{"version": 3, "sources": ["meteor://💻app/packages/react-meteor-data/index.js", "meteor://💻app/packages/react-meteor-data/useFind.ts", "meteor://💻app/packages/react-meteor-data/useSubscribe.ts", "meteor://💻app/packages/react-meteor-data/useTracker.ts", "meteor://💻app/packages/react-meteor-data/withTracker.tsx"], "names": ["React", "module", "link", "default", "v", "useTracker", "withTracker", "useFind", "useSubscribe", "Meteor", "isDevelopment", "version", "split", "console", "warn", "export", "Mongo", "useReducer", "useMemo", "useEffect", "useRef", "Tracker", "useFindReducer", "data", "action", "type", "slice", "atIndex", "document", "doc", "fromIndex", "copy", "splice", "toIndex", "checkCursor", "cursor", "undefined", "<PERSON><PERSON><PERSON>", "_mongo", "_cursorDescription", "fetchData", "observer", "observe", "addedAt", "before", "stop", "useFindClient", "factory", "deps", "arguments", "length", "nonreactive", "dispatch", "didMount", "current", "changedAt", "newDocument", "oldDocument", "removedAt", "movedTo", "_suppress_initial", "useFindServer", "_cursor$fetch", "_cursor$fetch2", "fetch", "call", "isServer", "useFindDev", "expects", "pos", "arg", "concat", "Array", "isArray", "exportDefault", "useSubscribeClient", "name", "_len", "args", "_key", "updateOnReady", "subscription", "isReady", "subscribe", "ready", "useSubscribeServer", "<PERSON><PERSON><PERSON><PERSON>", "Package", "mongo", "Object", "getPrototypeOf", "prototype", "keys", "for<PERSON>ach", "key", "fur", "x", "useForceUpdate", "useTrackerNoDeps", "reactiveFn", "skipUpdate", "refs", "isMounted", "trackerData", "forceUpdate", "computation", "autorun", "c", "firstRun", "defer", "_refs$computation", "useTrackerWithDeps", "comp", "useTrackerClient", "useTrackerServer", "_useTracker", "useTrackerDev", "_extends", "forwardRef", "memo", "options", "Component", "getMeteorData", "WithTracker", "props", "ref", "createElement", "pure"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,KAAK;AAACC,MAAM,CAACC,IAAI,CAAC,OAAO,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,KAAK,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAACH,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACG,UAAU,EAAC;AAAY,CAAC,EAAC,CAAC,CAAC;AAACJ,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACI,WAAW,EAAC;AAAa,CAAC,EAAC,CAAC,CAAC;AAACL,MAAM,CAACC,IAAI,CAAC,WAAW,EAAC;EAACK,OAAO,EAAC;AAAS,CAAC,EAAC,CAAC,CAAC;AAACN,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACM,YAAY,EAAC;AAAc,CAAC,EAAC,CAAC,CAAC;AAG1R,IAAIC,MAAM,CAACC,aAAa,EAAE;EACxB,MAAMN,CAAC,GAAGJ,KAAK,CAACW,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC;EAClC,IAAIR,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAKA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,EAAE;IACzCS,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;EACvE;AACF,C;;;;;;;;;;;ACRAb,MAAA,CAAOc,MAAE;EAAMR,OAAE,EAAMA,CAAA,KAAAA;AAAA;AAAA,IAAeE,MAAA;AAAAR,MAAA,CAAAC,IAAA;EAAAO,OAAAL,CAAA;IAAAK,MAAA,GAAAL,CAAA;EAAA;AAAA;AAAA,IAAAY,KAAA;AAAAf,MAAA,CAAAC,IAAA;EAAAc,MAAAZ,CAAA;IAAAY,KAAA,GAAAZ,CAAA;EAAA;AAAA;AAAA,IAAAa,UAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,MAAA;AAAAnB,MAAA,CAAAC,IAAA;EAAAe,WAAAb,CAAA;IAAAa,UAAA,GAAAb,CAAA;EAAA;EAAAc,QAAAd,CAAA;IAAAc,OAAA,GAAAd,CAAA;EAAA;EAAAe,UAAAf,CAAA;IAAAe,SAAA,GAAAf,CAAA;EAAA;EAAAgB,OAAAhB,CAAA;IAAAgB,MAAA,GAAAhB,CAAA;EAAA;AAAA;AAAA,IAAAiB,OAAA;AAAApB,MAAA,CAAAC,IAAA;EAAAmB,QAAAjB,CAAA;IAAAiB,OAAA,GAAAjB,CAAA;EAAA;AAAA;AAYtC,MAAMkB,cAAc,GAAGA,CAAIC,IAAS,EAAEC,MAAyB,KAAS;EACtE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,SAAS;MACZ,OAAOD,MAAM,CAACD,IAAI;IACpB,KAAK,SAAS;MACZ,OAAO,CACL,GAAGA,IAAI,CAACG,KAAK,CAAC,CAAC,EAAEF,MAAM,CAACG,OAAO,CAAC,EAChCH,MAAM,CAACI,QAAQ,EACf,GAAGL,IAAI,CAACG,KAAK,CAACF,MAAM,CAACG,OAAO,CAAC,CAC9B;IACH,KAAK,WAAW;MACd,OAAO,CACL,GAAGJ,IAAI,CAACG,KAAK,CAAC,CAAC,EAAEF,MAAM,CAACG,OAAO,CAAC,EAChCH,MAAM,CAACI,QAAQ,EACf,GAAGL,IAAI,CAACG,KAAK,CAACF,MAAM,CAACG,OAAO,GAAG,CAAC,CAAC,CAClC;IACH,KAAK,WAAW;MACd,OAAO,CACL,GAAGJ,IAAI,CAACG,KAAK,CAAC,CAAC,EAAEF,MAAM,CAACG,OAAO,CAAC,EAChC,GAAGJ,IAAI,CAACG,KAAK,CAACF,MAAM,CAACG,OAAO,GAAG,CAAC,CAAC,CAClC;IACH,KAAK,SAAS;MACZ,MAAME,GAAG,GAAGN,IAAI,CAACC,MAAM,CAACM,SAAS,CAAC;MAClC,MAAMC,IAAI,GAAG,CACX,GAAGR,IAAI,CAACG,KAAK,CAAC,CAAC,EAAEF,MAAM,CAACM,SAAS,CAAC,EAClC,GAAGP,IAAI,CAACG,KAAK,CAACF,MAAM,CAACM,SAAS,GAAG,CAAC,CAAC,CACpC;MACDC,IAAI,CAACC,MAAM,CAACR,MAAM,CAACS,OAAO,EAAE,CAAC,EAAEJ,GAAG,CAAC;MACnC,OAAOE,IAAI;EACf;AACF,CAAC;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,WAAW,GAAOC,MAA8F,IAAI;EACxH,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKC,SAAS,IAAI,EAAED,MAAM,YAAYnB,KAAK,CAACqB,MAAM,CAAC,IAC5E,EAAEF,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACI,kBAAkB,CAAC,EAAE;IACjD1B,OAAO,CAACC,IAAI,CACV,yDAAyD,GACvD,oDAAoD,CACvD;EACH;AACF,CAAC;AAED;AACA,MAAM0B,SAAS,GAAOL,MAAuB,IAAI;EAC/C,MAAMZ,IAAI,GAAQ,EAAE;EACpB,MAAMkB,QAAQ,GAAGN,MAAM,CAACO,OAAO,CAAC;IAC9BC,OAAOA,CAAEf,QAAQ,EAAED,OAAO,EAAEiB,MAAM;MAChCrB,IAAI,CAACS,MAAM,CAACL,OAAO,EAAE,CAAC,EAAEC,QAAQ,CAAC;IACnC;GACD,CAAC;EACFa,QAAQ,CAACI,IAAI,EAAE;EACf,OAAOtB,IAAI;AACb,CAAC;AAED,MAAMuB,aAAa,GAAG,SAAAA,CAAUC,OAAmD,EAA+B;EAAA,IAA7BC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAuB,EAAE;EAC5G,MAAMd,MAAM,GAAGjB,OAAO,CAAC,MAAK;IAC1B;IACA;IACA,MAAMiB,MAAM,GAAGd,OAAO,CAAC8B,WAAW,CAACJ,OAAO,CAAC;IAC3C,IAAItC,MAAM,CAACC,aAAa,EAAE;MACxBwB,WAAW,CAACC,MAAM,CAAC;IACrB;IACA,OAAOA,MAAM;EACf,CAAC,EAAEa,IAAI,CAAC;EAER,MAAM,CAACzB,IAAI,EAAE6B,QAAQ,CAAC,GAAGnC,UAAU,CACjCK,cAAc,EACd,IAAI,EACJ,MAAK;IACH,IAAI,EAAEa,MAAM,YAAYnB,KAAK,CAACqB,MAAM,CAAC,EAAE;MACrC,OAAO,EAAE;IACX;IAEA,OAAOG,SAAS,CAACL,MAAM,CAAC;EAC1B,CAAC,CACF;EAED;EACA;EACA,MAAMkB,QAAQ,GAAGjC,MAAM,CAAC,KAAK,CAAC;EAE9BD,SAAS,CAAC,MAAK;IACb;IACA,IAAIkC,QAAQ,CAACC,OAAO,EAAE;MACpB,IAAI,EAAEnB,MAAM,YAAYnB,KAAK,CAACqB,MAAM,CAAC,EAAE;QACrC;MACF;MAEA,MAAMd,IAAI,GAAGiB,SAAS,CAACL,MAAM,CAAC;MAC9BiB,QAAQ,CAAC;QAAE3B,IAAI,EAAE,SAAS;QAAEF;MAAI,CAAE,CAAC;IACrC,CAAC,MAAM;MACL8B,QAAQ,CAACC,OAAO,GAAG,IAAI;IACzB;IAEA,IAAI,EAAEnB,MAAM,YAAYnB,KAAK,CAACqB,MAAM,CAAC,EAAE;MACrC;IACF;IAEA,MAAMI,QAAQ,GAAGN,MAAM,CAACO,OAAO,CAAC;MAC9BC,OAAOA,CAAEf,QAAQ,EAAED,OAAO,EAAEiB,MAAM;QAChCQ,QAAQ,CAAC;UAAE3B,IAAI,EAAE,SAAS;UAAEG,QAAQ;UAAED;QAAO,CAAE,CAAC;MAClD,CAAC;MACD4B,SAASA,CAAEC,WAAW,EAAEC,WAAW,EAAE9B,OAAO;QAC1CyB,QAAQ,CAAC;UAAE3B,IAAI,EAAE,WAAW;UAAEG,QAAQ,EAAE4B,WAAW;UAAE7B;QAAO,CAAE,CAAC;MACjE,CAAC;MACD+B,SAASA,CAAED,WAAW,EAAE9B,OAAO;QAC7ByB,QAAQ,CAAC;UAAE3B,IAAI,EAAE,WAAW;UAAEE;QAAO,CAAE,CAAC;MAC1C,CAAC;MACDgC,OAAOA,CAAE/B,QAAQ,EAAEE,SAAS,EAAEG,OAAO,EAAEW,MAAM;QAC3CQ,QAAQ,CAAC;UAAE3B,IAAI,EAAE,SAAS;UAAEK,SAAS;UAAEG;QAAO,CAAE,CAAC;MACnD,CAAC;MACD;MACA2B,iBAAiB,EAAE;KACpB,CAAC;IAEF,OAAO,MAAK;MACVnB,QAAQ,CAACI,IAAI,EAAE;IACjB,CAAC;EACH,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EAEZ,OAAOA,MAAM,GAAGZ,IAAI,GAAGY,MAAM;AAC/B,CAAC;AAED,MAAM0B,aAAa,GAAGA,CAAUd,OAAiD,EAAEC,IAAoB,KACrG3B,OAAO,CAAC8B,WAAW,CAAC,MAAK;EAAA,IAAAW,aAAA,EAAAC,cAAA;EACvB,MAAM5B,MAAM,GAAGY,OAAO,EAAE;EACxB,IAAItC,MAAM,CAACC,aAAa,EAAEwB,WAAW,CAACC,MAAM,CAAC;EAC7C,QAAA2B,aAAA,GAAO3B,MAAM,aAANA,MAAM,wBAAA4B,cAAA,GAAN5B,MAAM,CAAE6B,KAAK,cAAAD,cAAA,uBAAbA,cAAA,CAAAE,IAAA,CAAA9B,MAAe,CAAE,cAAA2B,aAAA,cAAAA,aAAA,GAAI,IAAI;AAClC,CAAC,CACF;AAEM,MAAMvD,OAAO,GAAGE,MAAM,CAACyD,QAAQ,GAClCL,aAAa,GACbf,aAAa;AAEjB,SAASqB,UAAUA,CAAWpB,OAAmD,EAA2B;EAAA,IAAzBC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAuB,EAAE;EAC1G,SAASnC,IAAIA,CAAEsD,OAAe,EAAEC,GAAW,EAAEC,GAAW,EAAE7C,IAAY;IACpEZ,OAAO,CAACC,IAAI,CACV,+BAAAyD,MAAA,CAA+BH,OAAO,eAAAG,MAAA,CAAaF,GAAG,sBAAAE,MAAA,CAC9CD,GAAG,0BAAAC,MAAA,CAAwB9C,IAAI,OAAK,CAC7C;EACH;EAEA,IAAI,OAAOsB,OAAO,KAAK,UAAU,EAAE;IACjCjC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,EAAEiC,OAAO,CAAC;EAChD;EAEA,IAAI,CAACC,IAAI,IAAI,CAACwB,KAAK,CAACC,OAAO,CAACzB,IAAI,CAAC,EAAE;IACjClC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAOkC,IAAI,CAAC;EAC3C;EAEA,OAAOzC,OAAO,CAACwC,OAAO,EAAEC,IAAI,CAAC;AAC/B;AA3KA/C,MAAA,CAAOyE,aAAU,CA6KFjE,MAAM,CAACC,aAAa,GAC/ByD,UAAU,GACV5D,OA/KmB,E;;;;;;;;;;;ACAvBN,MAAA,CAAOc,MAAE;EAAMP,YAAQ,EAAAA,CAAA,KAAAA;AAAe;AAAA,IAAAC,MAAA;AAAAR,MAAA,CAAAC,IAAA;EAAAO,OAAAL,CAAA;IAAAK,MAAA,GAAAL,CAAA;EAAA;AAAA;AAAA,IAAAC,UAAA;AAAAJ,MAAA,CAAAC,IAAA;EAAAG,WAAAD,CAAA;IAAAC,UAAA,GAAAD,CAAA;EAAA;AAAA;AAGtC,MAAMuE,kBAAkB,GAAG,SAAAA,CAACC,IAAa,EAAmC;EAAA,SAAAC,IAAA,GAAA5B,SAAA,CAAAC,MAAA,EAA9B4B,IAAW,OAAAN,KAAA,CAAAK,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAAXD,IAAW,CAAAC,IAAA,QAAA9B,SAAA,CAAA8B,IAAA;EAAA;EACvD,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,YAAuC;EAE3C,MAAMC,OAAO,GAAG7E,UAAU,CAAC,MAAK;IAC9B,IAAI,CAACuE,IAAI,EAAE,OAAO,IAAI;IAEtBK,YAAY,GAAGxE,MAAM,CAAC0E,SAAS,CAACP,IAAI,EAAE,GAAGE,IAAI,CAAC;IAE9C,OAAOG,YAAY,CAACG,KAAK,EAAE;EAC7B,CAAC,EAAE,MAAO,CAACJ,aAAc,CAAC;EAE1B,OAAO,MAAK;IACVA,aAAa,GAAG,IAAI;IACpB,OAAO,CAACE,OAAO;EACjB,CAAC;AACH,CAAC;AAED,MAAMG,kBAAkB,GAAG,SAAAA,CAACT,IAAa;EAAA,OACvC,MAAM,KAAK;AAAA,CACZ;AAEM,MAAMpE,YAAY,GAAGC,MAAM,CAACyD,QAAQ,GACvCmB,kBAAkB,GAClBV,kBAAkB,C;;;;;;;;;;;AC1BtB1E,MAAA,CAAOc,MAAE;EAAMV,UAAQ,EAAAA,CAAA,KAAAA;AAAgB;AAAA,IAAAI,MAAA;AAAAR,MAAA,CAAAC,IAAA;EAAAO,OAAAL,CAAA;IAAAK,MAAA,GAAAL,CAAA;EAAA;AAAA;AAAA,IAAAiB,OAAA;AAAApB,MAAA,CAAAC,IAAA;EAAAmB,QAAAjB,CAAA;IAAAiB,OAAA,GAAAjB,CAAA;EAAA;AAAA;AAAA,IAAAa,UAAA,EAAAE,SAAA,EAAAC,MAAA,EAAAF,OAAA;AAAAjB,MAAA,CAAAC,IAAA;EAAAe,WAAAb,CAAA;IAAAa,UAAA,GAAAb,CAAA;EAAA;EAAAe,UAAAf,CAAA;IAAAe,SAAA,GAAAf,CAAA;EAAA;EAAAgB,OAAAhB,CAAA;IAAAgB,MAAA,GAAAhB,CAAA;EAAA;EAAAc,QAAAd,CAAA;IAAAc,OAAA,GAAAd,CAAA;EAAA;AAAA;AAIvC;AACA,SAAS8B,WAAWA,CAAEX,IAAS;EAC7B,IAAI+D,UAAU,GAAG,KAAK;EACtB,IAAIC,OAAO,CAACC,KAAK,IAAID,OAAO,CAACC,KAAK,CAACxE,KAAK,IAAIO,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5E,IAAIA,IAAI,YAAYgE,OAAO,CAACC,KAAK,CAACxE,KAAK,CAACqB,MAAM,EAAE;MAC9CiD,UAAU,GAAG,IAAI;IACnB,CAAC,MAAM,IAAIG,MAAM,CAACC,cAAc,CAACnE,IAAI,CAAC,KAAKkE,MAAM,CAACE,SAAS,EAAE;MAC3DF,MAAM,CAACG,IAAI,CAACrE,IAAI,CAAC,CAACsE,OAAO,CAAEC,GAAG,IAAI;QAChC,IAAIvE,IAAI,CAACuE,GAAG,CAAC,YAAYP,OAAO,CAACC,KAAK,CAACxE,KAAK,CAACqB,MAAM,EAAE;UACnDiD,UAAU,GAAG,IAAI;QACnB;MACF,CAAC,CAAC;IACJ;EACF;EACA,IAAIA,UAAU,EAAE;IACdzE,OAAO,CAACC,IAAI,CACV,+DAA+D,GAC7D,6DAA6D,GAC7D,+CAA+C,CAClD;EACH;AACF;AAEA;AACA;AACA,MAAMiF,GAAG,GAAIC,CAAS,IAAaA,CAAC,GAAG,CAAC;AACxC,MAAMC,cAAc,GAAGA,CAAA,KAAMhF,UAAU,CAAC8E,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAgBlD,MAAMG,gBAAgB,GAAG,SAAAA,CAAUC,UAA0B,EAAuC;EAAA,IAArCC,UAAA,GAAAnD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAA6B,IAAI;EAC9F,MAAM;IAAEK,OAAO,EAAE+C;EAAI,CAAE,GAAGjF,MAAM,CAAc;IAC5CkF,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;GACd,CAAC;EACF,MAAMC,WAAW,GAAGP,cAAc,EAAE;EAEpC;EACA,IAAII,IAAI,CAACI,WAAW,EAAE;IACpBJ,IAAI,CAACI,WAAW,CAAC5D,IAAI,EAAE;IACvB;IACA,OAAOwD,IAAI,CAACI,WAAW;EACzB;EAEA;EACA;EACA;EACA;EACA;EACApF,OAAO,CAAC8B,WAAW,CAAC,MAAM9B,OAAO,CAACqF,OAAO,CAAEC,CAAsB,IAAI;IACnEN,IAAI,CAACI,WAAW,GAAGE,CAAC;IACpB,MAAMpF,IAAI,GAAG4E,UAAU,CAACQ,CAAC,CAAC;IAC1B,IAAIA,CAAC,CAACC,QAAQ,EAAE;MACd;MACAP,IAAI,CAACE,WAAW,GAAGhF,IAAI;IACzB,CAAC,MAAM,IAAI,CAAC6E,UAAU,IAAI,CAACA,UAAU,CAACC,IAAI,CAACE,WAAW,EAAEhF,IAAI,CAAC,EAAE;MAC7D;MACAiF,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CAAC;EAEH;EACA,IAAI,CAACH,IAAI,CAACC,SAAS,EAAE;IACnB7F,MAAM,CAACoG,KAAK,CAAC,MAAK;MAChB,IAAI,CAACR,IAAI,CAACC,SAAS,IAAID,IAAI,CAACI,WAAW,EAAE;QACvCJ,IAAI,CAACI,WAAW,CAAC5D,IAAI,EAAE;QACvB,OAAOwD,IAAI,CAACI,WAAW;MACzB;IACF,CAAC,CAAC;EACJ;EAEAtF,SAAS,CAAC,MAAK;IACb;IACAkF,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA;IACA;IACA,IAAI,CAACD,IAAI,CAACI,WAAW,EAAE;MACrB;MACA;MACA,IAAI,CAACL,UAAU,EAAE;QACfI,WAAW,EAAE;MACf,CAAC,MAAM;QACLnF,OAAO,CAAC8B,WAAW,CAAC,MAAM9B,OAAO,CAACqF,OAAO,CAAEC,CAAsB,IAAI;UACnE,MAAMpF,IAAI,GAAG4E,UAAU,CAACQ,CAAC,CAAC;UAC1BN,IAAI,CAACI,WAAW,GAAGE,CAAC;UACpB,IAAI,CAACP,UAAU,CAACC,IAAI,CAACE,WAAW,EAAEhF,IAAI,CAAC,EAAE;YACvC;YACAiF,WAAW,EAAE;UACf;QACF,CAAC,CAAC,CAAC;MACL;IACF;IAEA;IACA,OAAO,MAAK;MAAA,IAAAM,iBAAA;MACV,CAAAA,iBAAA,GAAAT,IAAI,CAACI,WAAW,cAAAK,iBAAA,uBAAhBA,iBAAA,CAAkBjE,IAAI,EAAE;MACxB,OAAOwD,IAAI,CAACI,WAAW;MACvBJ,IAAI,CAACC,SAAS,GAAG,KAAK;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOD,IAAI,CAACE,WAAW;AACzB,CAAC;AAED,MAAMQ,kBAAkB,GAAG,SAAAA,CAAUZ,UAA0B,EAAEnD,IAAoB,EAA0C;EAAA,IAAxCoD,UAAA,GAAAnD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAA6B,IAAI;EACtH,MAAMuD,WAAW,GAAGP,cAAc,EAAE;EAEpC,MAAM;IAAE3C,OAAO,EAAE+C;EAAI,CAAE,GAAGjF,MAAM,CAK7B;IAAE+E;EAAU,CAAE,CAAC;EAElB;EACAE,IAAI,CAACF,UAAU,GAAGA,UAAU;EAE5BjF,OAAO,CAAC,MAAK;IACX;IACA;IACA,MAAM8F,IAAI,GAAG3F,OAAO,CAAC8B,WAAW,CAC9B,MAAM9B,OAAO,CAACqF,OAAO,CAAEC,CAAsB,IAAI;MAC/C,MAAMpF,IAAI,GAAG8E,IAAI,CAACF,UAAU,EAAE;MAC9B,IAAIQ,CAAC,CAACC,QAAQ,EAAE;QACdP,IAAI,CAAC9E,IAAI,GAAGA,IAAI;MAClB,CAAC,MAAM,IAAI,CAAC6E,UAAU,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC9E,IAAI,EAAEA,IAAI,CAAC,EAAE;QACtD8E,IAAI,CAAC9E,IAAI,GAAGA,IAAI;QAChBiF,WAAW,EAAE;MACf;IACF,CAAC,CAAC,CACH;IAED;IACA;IACA;IACA;IACA,IAAIH,IAAI,CAACW,IAAI,EAAEX,IAAI,CAACW,IAAI,CAACnE,IAAI,EAAE;IAE/B;IACA;IACA;IACAwD,IAAI,CAACW,IAAI,GAAGA,IAAI;IAChB;IACAvG,MAAM,CAACoG,KAAK,CAAC,MAAK;MAChB,IAAI,CAACR,IAAI,CAACC,SAAS,IAAID,IAAI,CAACW,IAAI,EAAE;QAChCX,IAAI,CAACW,IAAI,CAACnE,IAAI,EAAE;QAChB,OAAOwD,IAAI,CAACW,IAAI;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAEhE,IAAI,CAAC;EAER7B,SAAS,CAAC,MAAK;IACb;IACAkF,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,IAAI,CAACD,IAAI,CAACW,IAAI,EAAE;MACdX,IAAI,CAACW,IAAI,GAAG3F,OAAO,CAAC8B,WAAW,CAC7B,MAAM9B,OAAO,CAACqF,OAAO,CAAEC,CAAC,IAAI;QAC1B,MAAMpF,IAAI,GAAM8E,IAAI,CAACF,UAAU,CAACQ,CAAC,CAAC;QAClC,IAAI,CAACP,UAAU,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC9E,IAAI,EAAEA,IAAI,CAAC,EAAE;UAC/C8E,IAAI,CAAC9E,IAAI,GAAGA,IAAI;UAChBiF,WAAW,EAAE;QACf;MACF,CAAC,CAAC,CACH;IACH;IAEA,OAAO,MAAK;MACVH,IAAI,CAACW,IAAI,CAACnE,IAAI,EAAE;MAChB,OAAOwD,IAAI,CAACW,IAAI;MAChBX,IAAI,CAACC,SAAS,GAAG,KAAK;IACxB,CAAC;EACH,CAAC,EAAEtD,IAAI,CAAC;EAER,OAAOqD,IAAI,CAAC9E,IAAS;AACvB,CAAC;AAID,SAAS0F,gBAAgBA,CAAWd,UAA0B,EAAiF;EAAA,IAA/EnD,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAwC,IAAI;EAAA,IAAEmD,UAAA,GAAAnD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAA6B,IAAI;EAC7I,IAAID,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKZ,SAAS,IAAI,CAACoC,KAAK,CAACC,OAAO,CAACzB,IAAI,CAAC,EAAE;IAC/D,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MAC9BoD,UAAU,GAAGpD,IAAI;IACnB;IACA,OAAOkD,gBAAgB,CAACC,UAAU,EAAEC,UAAU,CAAC;EACjD,CAAC,MAAM;IACL,OAAOW,kBAAkB,CAACZ,UAAU,EAAEnD,IAAI,EAAEoD,UAAU,CAAC;EACzD;AACF;AAEA,MAAMc,gBAAgB,GAA6Bf,UAAU,IAAI;EAC/D,OAAO9E,OAAO,CAAC8B,WAAW,CAACgD,UAAU,CAAC;AACxC,CAAC;AAED;AACA;AACA,MAAMgB,WAAW,GAAG1G,MAAM,CAACyD,QAAQ,GAC/BgD,gBAAgB,GAChBD,gBAAgB;AAEpB,SAASG,aAAaA,CAAEjB,UAAU,EAAgC;EAAA,IAA9BnD,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAG,IAAI;EAAA,IAAEmD,UAAU,GAAAnD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAG,IAAI;EAChE,SAASnC,IAAIA,CAAEsD,OAAe,EAAEC,GAAW,EAAEC,GAAW,EAAE7C,IAAY;IACpEZ,OAAO,CAACC,IAAI,CACV,kCAAAyD,MAAA,CAAkCH,OAAO,eAAAG,MAAA,CAAaF,GAAG,sBAAAE,MAAA,CACjDD,GAAG,0BAAAC,MAAA,CAAwB9C,IAAI,OAAK,CAC7C;EACH;EAEA,IAAI,OAAO0E,UAAU,KAAK,UAAU,EAAE;IACpCrF,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,EAAEqF,UAAU,CAAC;EACnD;EAEA,IAAInD,IAAI,IAAIoD,UAAU,IAAI,CAAC5B,KAAK,CAACC,OAAO,CAACzB,IAAI,CAAC,IAAI,OAAOoD,UAAU,KAAK,UAAU,EAAE;IAClFtF,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,kBAAkB,KAAAyD,MAAA,CACrD,OAAOvB,IAAI,SAAAuB,MAAA,CAAM,OAAO6B,UAAU,CAAE,CAAC;EAC5C,CAAC,MAAM;IACL,IAAIpD,IAAI,IAAI,CAACwB,KAAK,CAACC,OAAO,CAACzB,IAAI,CAAC,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MAC9DlC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,oBAAoB,EAAE,OAAOkC,IAAI,CAAC;IACrE;IACA,IAAIoD,UAAU,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;MAClDtF,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,OAAOsF,UAAU,CAAC;IAC1D;EACF;EAEA,MAAM7E,IAAI,GAAG4F,WAAW,CAAChB,UAAU,EAAEnD,IAAI,EAAEoD,UAAU,CAAC;EACtDlE,WAAW,CAACX,IAAI,CAAC;EACjB,OAAOA,IAAI;AACb;AAEO,MAAMlB,UAAU,GAAGI,MAAM,CAACC,aAAa,GAC1C0G,aAAwC,GACxCD,WAAW,C;;;;;;;;;;;AC1Pf,IAAAE,QAAY;AAAApH,MAAI,CAAAC,IAAA,iCAAgC;EAAAC,QAAAC,CAAA;IAAAiH,QAAA,GAAAjH,CAAA;EAAA;AAAA;AAAhDH,MAAA,CAAOc,MAAK,CAAE;EAAAT,WAAY,EAAAA,CAAA,KAAEA;AAAY;AAAA,IAAQN,KAAA,EAAAsH,UAAA,EAAAC,IAAA;AAAAtH,MAAA,CAAAC,IAAA;EAAAC,QAAAC,CAAA;IAAAJ,KAAA,GAAAI,CAAA;EAAA;EAAAkH,WAAAlH,CAAA;IAAAkH,UAAA,GAAAlH,CAAA;EAAA;EAAAmH,KAAAnH,CAAA;IAAAmH,IAAA,GAAAnH,CAAA;EAAA;AAAA;AAAA,IAAAC,UAAA;AAAAJ,MAAA,CAAAC,IAAA;EAAAG,WAAAD,CAAA;IAAAC,UAAA,GAAAD,CAAA;EAAA;AAAA;AAUzC,MAAME,WAAW,GAAIkH,OAAqC,IAAI;EACnE,OAAQC,SAA8B,IAAI;IACxC,MAAMC,aAAa,GAAG,OAAOF,OAAO,KAAK,UAAU,GAC/CA,OAAO,GACPA,OAAO,CAACE,aAAa;IAEzB,MAAMC,WAAW,gBAAGL,UAAU,CAAC,CAACM,KAAK,EAAEC,GAAG,KAAI;MAC5C,MAAMtG,IAAI,GAAGlB,UAAU,CACrB,MAAMqH,aAAa,CAACE,KAAK,CAAC,IAAI,EAAE,EAC/BJ,OAA2B,CAACpB,UAAU,CACxC;MACD,oBACEpG,KAAA,CAAA8H,aAAA,CAACL,SAAS,EAAAJ,QAAA;QAACQ,GAAG,EAAEA;MAAI,GAAKD,KAAK,EAAMrG,IAAI,CAAC,CAAG;IAEhD,CAAC,CAAC;IAEF,MAAM;MAAEwG,IAAI,GAAG;IAAI,CAAE,GAAGP,OAA0B;IAClD,OAAOO,IAAI,gBAAGR,IAAI,CAACI,WAAW,CAAC,GAAGA,WAAW;EAC/C,CAAC;AACH,CAAC,C", "file": "/packages/react-meteor-data.js", "sourcesContent": ["/* global Meteor*/\nimport React from 'react';\n\nif (Meteor.isDevelopment) {\n  const v = React.version.split('.');\n  if (v[0] < 16 || (v[0] == 16 && v[1] < 8)) {\n    console.warn('react-meteor-data 2.x requires React version >= 16.8.');\n  }\n}\n\nexport { useTracker } from './useTracker';\nexport { withTracker } from './withTracker.tsx';\nexport { useFind } from './useFind';\nexport { useSubscribe } from './useSubscribe';", "import { Meteor } from 'meteor/meteor'\nimport { Mongo } from 'meteor/mongo'\nimport { useReducer, useMemo, useEffect, Reducer, DependencyList, useRef } from 'react'\nimport { Tracker } from 'meteor/tracker'\n\ntype useFindActions<T> =\n  | { type: 'refresh', data: T[] }\n  | { type: 'addedAt', document: T, atIndex: number }\n  | { type: 'changedAt', document: T, atIndex: number }\n  | { type: 'removedAt', atIndex: number }\n  | { type: 'movedTo', fromIndex: number, toIndex: number }\n\nconst useFindReducer = <T>(data: T[], action: useFindActions<T>): T[] => {\n  switch (action.type) {\n    case 'refresh':\n      return action.data\n    case 'addedAt':\n      return [\n        ...data.slice(0, action.atIndex),\n        action.document,\n        ...data.slice(action.atIndex)\n      ]\n    case 'changedAt':\n      return [\n        ...data.slice(0, action.atIndex),\n        action.document,\n        ...data.slice(action.atIndex + 1)\n      ]\n    case 'removedAt':\n      return [\n        ...data.slice(0, action.atIndex),\n        ...data.slice(action.atIndex + 1)\n      ]\n    case 'movedTo':\n      const doc = data[action.fromIndex]\n      const copy = [\n        ...data.slice(0, action.fromIndex),\n        ...data.slice(action.fromIndex + 1)\n      ]\n      copy.splice(action.toIndex, 0, doc)\n      return copy\n  }\n}\n\n// Check for valid Cursor or null.\n// On client, we should have a Mongo.Cursor (defined in\n// https://github.com/meteor/meteor/blob/devel/packages/minimongo/cursor.js and\n// https://github.com/meteor/meteor/blob/devel/packages/mongo/collection.js).\n// On server, however, we instead get a private Cursor type from\n// https://github.com/meteor/meteor/blob/devel/packages/mongo/mongo_driver.js\n// which has fields _mongo and _cursorDescription.\nconst checkCursor = <T>(cursor: Mongo.Cursor<T> | Partial<{ _mongo: any, _cursorDescription: any }> | undefined | null) => {\n  if (cursor !== null && cursor !== undefined && !(cursor instanceof Mongo.Cursor) &&\n      !(cursor._mongo && cursor._cursorDescription)) {\n    console.warn(\n      'Warning: useFind requires an instance of Mongo.Cursor. '\n      + 'Make sure you do NOT call .fetch() on your cursor.'\n    );\n  }\n}\n\n// Synchronous data fetch. It uses cursor observing instead of cursor.fetch() because synchronous fetch will be deprecated.\nconst fetchData = <T>(cursor: Mongo.Cursor<T>) => {\n  const data: T[] = []\n  const observer = cursor.observe({\n    addedAt (document, atIndex, before) {\n      data.splice(atIndex, 0, document)\n    },\n  })\n  observer.stop()\n  return data\n}\n\nconst useFindClient = <T = any>(factory: () => (Mongo.Cursor<T> | undefined | null), deps: DependencyList = []) => {\n  const cursor = useMemo(() => {\n    // To avoid creating side effects in render, opt out\n    // of Tracker integration altogether.\n    const cursor = Tracker.nonreactive(factory);\n    if (Meteor.isDevelopment) {\n      checkCursor(cursor)\n    }\n    return cursor\n  }, deps)\n\n  const [data, dispatch] = useReducer<Reducer<T[], useFindActions<T>>, null>(\n    useFindReducer,\n    null,\n    () => {\n      if (!(cursor instanceof Mongo.Cursor)) {\n        return []\n      }\n\n      return fetchData(cursor)\n    }\n  )\n\n  // Store information about mounting the component.\n  // It will be used to run code only if the component is updated.\n  const didMount = useRef(false)\n\n  useEffect(() => {\n    // Fetch intitial data if cursor was changed.\n    if (didMount.current) {\n      if (!(cursor instanceof Mongo.Cursor)) {\n        return\n      }\n\n      const data = fetchData(cursor)\n      dispatch({ type: 'refresh', data })\n    } else {\n      didMount.current = true\n    }\n\n    if (!(cursor instanceof Mongo.Cursor)) {\n      return\n    }\n\n    const observer = cursor.observe({\n      addedAt (document, atIndex, before) {\n        dispatch({ type: 'addedAt', document, atIndex })\n      },\n      changedAt (newDocument, oldDocument, atIndex) {\n        dispatch({ type: 'changedAt', document: newDocument, atIndex })\n      },\n      removedAt (oldDocument, atIndex) {\n        dispatch({ type: 'removedAt', atIndex })\n      },\n      movedTo (document, fromIndex, toIndex, before) {\n        dispatch({ type: 'movedTo', fromIndex, toIndex })\n      },\n      // @ts-ignore\n      _suppress_initial: true\n    })\n\n    return () => {\n      observer.stop()\n    }\n  }, [cursor])\n\n  return cursor ? data : cursor\n}\n\nconst useFindServer = <T = any>(factory: () => Mongo.Cursor<T> | undefined | null, deps: DependencyList) => (\n  Tracker.nonreactive(() => {\n    const cursor = factory()\n    if (Meteor.isDevelopment) checkCursor(cursor)\n    return cursor?.fetch?.() ?? null\n  })\n)\n\nexport const useFind = Meteor.isServer\n  ? useFindServer\n  : useFindClient\n\nfunction useFindDev <T = any>(factory: () => (Mongo.Cursor<T> | undefined | null), deps: DependencyList = []) {\n  function warn (expects: string, pos: string, arg: string, type: string) {\n    console.warn(\n      `Warning: useFind expected a ${expects} in it\\'s ${pos} argument `\n        + `(${arg}), but got type of \\`${type}\\`.`\n    );\n  }\n\n  if (typeof factory !== 'function') {\n    warn(\"function\", \"1st\", \"reactiveFn\", factory);\n  }\n\n  if (!deps || !Array.isArray(deps)) {\n    warn(\"array\", \"2nd\", \"deps\", typeof deps);\n  }\n\n  return useFind(factory, deps);\n}\n\nexport default Meteor.isDevelopment\n  ? useFindDev\n  : useFind;\n", "import { Meteor } from 'meteor/meteor'\nimport { useTracker } from './useTracker'\n\nconst useSubscribeClient = (name?: string, ...args: any[]): () => boolean => {\n  let updateOnReady = false\n  let subscription: Meteor.SubscriptionHandle\n\n  const isReady = useTracker(() => {\n    if (!name) return true\n\n    subscription = Meteor.subscribe(name, ...args)\n\n    return subscription.ready()\n  }, () => (!updateOnReady))\n\n  return () => {\n    updateOnReady = true\n    return !isReady\n  }\n}\n\nconst useSubscribeServer = (name?: string, ...args: any[]): () => boolean => (\n  () => false\n)\n\nexport const useSubscribe = Meteor.isServer\n  ? useSubscribeServer\n  : useSubscribeClient\n", "declare var Package: any\nimport { Meteor } from 'meteor/meteor';\nimport { Tracker } from 'meteor/tracker';\nimport { useReducer, useEffect, useRef, useMemo, DependencyList } from 'react';\n\n// Warns if data is a Mongo.Cursor or a POJO containing a Mongo.Cursor.\nfunction checkCursor (data: any): void {\n  let shouldWarn = false;\n  if (Package.mongo && Package.mongo.Mongo && data && typeof data === 'object') {\n    if (data instanceof Package.mongo.Mongo.Cursor) {\n      shouldWarn = true;\n    } else if (Object.getPrototypeOf(data) === Object.prototype) {\n      Object.keys(data).forEach((key) => {\n        if (data[key] instanceof Package.mongo.Mongo.Cursor) {\n          shouldWarn = true;\n        }\n      });\n    }\n  }\n  if (shouldWarn) {\n    console.warn(\n      'Warning: your reactive function is returning a Mongo cursor. '\n      + 'This value will not be reactive. You probably want to call '\n      + '`.fetch()` on the cursor before returning it.'\n    );\n  }\n}\n\n// Used to create a forceUpdate from useReducer. Forces update by\n// incrementing a number whenever the dispatch method is invoked.\nconst fur = (x: number): number => x + 1;\nconst useForceUpdate = () => useReducer(fur, 0)[1];\n\nexport interface IReactiveFn<T> {\n  (c?: Tracker.Computation): T\n}\n\nexport interface ISkipUpdate<T> {\n  <T>(prev: T, next: T): boolean\n}\n\ntype TrackerRefs = {\n  computation?: Tracker.Computation;\n  isMounted: boolean;\n  trackerData: any;\n}\n\nconst useTrackerNoDeps = <T = any>(reactiveFn: IReactiveFn<T>, skipUpdate: ISkipUpdate<T> = null) => {\n  const { current: refs } = useRef<TrackerRefs>({\n    isMounted: false,\n    trackerData: null\n  });\n  const forceUpdate = useForceUpdate();\n\n  // Without deps, always dispose and recreate the computation with every render.\n  if (refs.computation) {\n    refs.computation.stop();\n    // @ts-ignore This makes TS think ref.computation is \"never\" set\n    delete refs.computation;\n  }\n\n  // Use Tracker.nonreactive in case we are inside a Tracker Computation.\n  // This can happen if someone calls `ReactDOM.render` inside a Computation.\n  // In that case, we want to opt out of the normal behavior of nested\n  // Computations, where if the outer one is invalidated or stopped,\n  // it stops the inner one.\n  Tracker.nonreactive(() => Tracker.autorun((c: Tracker.Computation) => {\n    refs.computation = c;\n    const data = reactiveFn(c);\n    if (c.firstRun) {\n      // Always run the reactiveFn on firstRun\n      refs.trackerData = data;\n    } else if (!skipUpdate || !skipUpdate(refs.trackerData, data)) {\n      // For any reactive change, forceUpdate and let the next render rebuild the computation.\n      forceUpdate();\n    }\n  }));\n\n  // To clean up side effects in render, stop the computation immediately\n  if (!refs.isMounted) {\n    Meteor.defer(() => {\n      if (!refs.isMounted && refs.computation) {\n        refs.computation.stop();\n        delete refs.computation;\n      }\n    });\n  }\n\n  useEffect(() => {\n    // Let subsequent renders know we are mounted (render is committed).\n    refs.isMounted = true;\n\n    // In some cases, the useEffect hook will run before Meteor.defer, such as\n    // when React.lazy is used. In those cases, we might as well leave the\n    // computation alone!\n    if (!refs.computation) {\n      // Render is committed, but we no longer have a computation. Invoke\n      // forceUpdate and let the next render recreate the computation.\n      if (!skipUpdate) {\n        forceUpdate();\n      } else {\n        Tracker.nonreactive(() => Tracker.autorun((c: Tracker.Computation) => {\n          const data = reactiveFn(c);\n          refs.computation = c;\n          if (!skipUpdate(refs.trackerData, data)) {\n            // For any reactive change, forceUpdate and let the next render rebuild the computation.\n            forceUpdate();\n          }\n        }));\n      }\n    }\n\n    // stop the computation on unmount\n    return () =>{\n      refs.computation?.stop();\n      delete refs.computation;\n      refs.isMounted = false;\n    }\n  }, []);\n\n  return refs.trackerData;\n}\n\nconst useTrackerWithDeps = <T = any>(reactiveFn: IReactiveFn<T>, deps: DependencyList, skipUpdate: ISkipUpdate<T> = null): T => {\n  const forceUpdate = useForceUpdate();\n\n  const { current: refs } = useRef<{\n    reactiveFn: IReactiveFn<T>;\n    data?: T;\n    comp?: Tracker.Computation;\n    isMounted?: boolean;\n  }>({ reactiveFn });\n\n  // keep reactiveFn ref fresh\n  refs.reactiveFn = reactiveFn;\n\n  useMemo(() => {\n    // To jive with the lifecycle interplay between Tracker/Subscribe, run the\n    // reactive function in a computation, then stop it, to force flush cycle.\n    const comp = Tracker.nonreactive(\n      () => Tracker.autorun((c: Tracker.Computation) => {\n        const data = refs.reactiveFn();\n        if (c.firstRun) {\n          refs.data = data;\n        } else if (!skipUpdate || !skipUpdate(refs.data, data)) {\n          refs.data = data;\n          forceUpdate();\n        }\n      })\n    );\n\n    // Stop the computation immediately to avoid creating side effects in render.\n    // refers to this issues:\n    // https://github.com/meteor/react-packages/issues/382\n    // https://github.com/meteor/react-packages/issues/381\n    if (refs.comp) refs.comp.stop();\n\n    // In some cases, the useEffect hook will run before Meteor.defer, such as\n    // when React.lazy is used. This will allow it to be stopped earlier in\n    // useEffect if needed.\n    refs.comp = comp;\n    // To avoid creating side effects in render, stop the computation immediately\n    Meteor.defer(() => {\n      if (!refs.isMounted && refs.comp) {\n        refs.comp.stop();\n        delete refs.comp;\n      }\n    });\n  }, deps);\n\n  useEffect(() => {\n    // Let subsequent renders know we are mounted (render is committed).\n    refs.isMounted = true;\n\n    if (!refs.comp) {\n      refs.comp = Tracker.nonreactive(\n        () => Tracker.autorun((c) => {\n          const data: T = refs.reactiveFn(c);\n          if (!skipUpdate || !skipUpdate(refs.data, data)) {\n            refs.data = data;\n            forceUpdate();\n          }\n        })\n      );\n    }\n\n    return () => {\n      refs.comp.stop();\n      delete refs.comp;\n      refs.isMounted = false;\n    };\n  }, deps);\n\n  return refs.data as T;\n};\n\nfunction useTrackerClient <T = any>(reactiveFn: IReactiveFn<T>, skipUpdate?: ISkipUpdate<T>): T;\nfunction useTrackerClient <T = any>(reactiveFn: IReactiveFn<T>, deps?: DependencyList, skipUpdate?: ISkipUpdate<T>): T;\nfunction useTrackerClient <T = any>(reactiveFn: IReactiveFn<T>, deps: DependencyList | ISkipUpdate<T> = null, skipUpdate: ISkipUpdate<T> = null): T {\n  if (deps === null || deps === undefined || !Array.isArray(deps)) {\n    if (typeof deps === \"function\") {\n      skipUpdate = deps;\n    }\n    return useTrackerNoDeps(reactiveFn, skipUpdate);\n  } else {\n    return useTrackerWithDeps(reactiveFn, deps, skipUpdate);\n  }\n}\n\nconst useTrackerServer: typeof useTrackerClient = (reactiveFn) => {\n  return Tracker.nonreactive(reactiveFn);\n}\n\n// When rendering on the server, we don't want to use the Tracker.\n// We only do the first rendering on the server so we can get the data right away\nconst _useTracker = Meteor.isServer\n  ? useTrackerServer\n  : useTrackerClient;\n\nfunction useTrackerDev (reactiveFn, deps = null, skipUpdate = null) {\n  function warn (expects: string, pos: string, arg: string, type: string) {\n    console.warn(\n      `Warning: useTracker expected a ${expects} in it\\'s ${pos} argument `\n        + `(${arg}), but got type of \\`${type}\\`.`\n    );\n  }\n\n  if (typeof reactiveFn !== 'function') {\n    warn(\"function\", \"1st\", \"reactiveFn\", reactiveFn);\n  }\n\n  if (deps && skipUpdate && !Array.isArray(deps) && typeof skipUpdate === \"function\") {\n    warn(\"array & function\", \"2nd and 3rd\", \"deps, skipUpdate\",\n      `${typeof deps} & ${typeof skipUpdate}`);\n  } else {\n    if (deps && !Array.isArray(deps) && typeof deps !== \"function\") {\n      warn(\"array or function\", \"2nd\", \"deps or skipUpdate\", typeof deps);\n    }\n    if (skipUpdate && typeof skipUpdate !== \"function\") {\n      warn(\"function\", \"3rd\", \"skipUpdate\", typeof skipUpdate);\n    }\n  }\n\n  const data = _useTracker(reactiveFn, deps, skipUpdate);\n  checkCursor(data);\n  return data;\n}\n\nexport const useTracker = Meteor.isDevelopment\n  ? useTrackerDev as typeof useTrackerClient\n  : _useTracker;\n", "import React, { forwardRef, memo } from 'react';\nimport { useTracker } from './useTracker';\n\ntype ReactiveFn = (props: object) => any;\ntype ReactiveOptions = {\n  getMeteorData: ReactiveFn;\n  pure?: boolean;\n  skipUpdate?: (prev: any, next: any) => boolean;\n}\n\nexport const withTracker = (options: ReactiveFn | ReactiveOptions) => {\n  return (Component: React.ComponentType) => {\n    const getMeteorData = typeof options === 'function'\n      ? options\n      : options.getMeteorData;\n\n    const WithTracker = forwardRef((props, ref) => {\n      const data = useTracker(\n        () => getMeteorData(props) || {},\n        (options as ReactiveOptions).skipUpdate\n      );\n      return (\n        <Component ref={ref} {...props} {...data} />\n      );\n    });\n\n    const { pure = true } = options as ReactiveOptions;\n    return pure ? memo(WithTracker) : WithTracker;\n  };\n}\n"]}