{"version": 3, "sources": ["meteor://💻app/packages/mongo-id/id.js"], "names": ["_typeof", "module", "link", "default", "v", "export", "MongoID", "EJSON", "Random", "_looksLikeObjectID", "str", "length", "test", "ObjectID", "hexString", "toLowerCase", "Error", "_str", "_proto", "prototype", "equals", "other", "valueOf", "toString", "clone", "typeName", "getTimestamp", "Number", "parseInt", "substr", "toJSONValue", "toHexString", "addType", "idStringify", "id", "firstChar", "char<PERSON>t", "undefined", "JSON", "stringify", "idParse", "parse"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,OAAO;AAACC,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,OAAO,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA3FH,MAAM,CAACI,MAAM,CAAC;EAACC,OAAO,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,OAAO;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,KAAK;AAACN,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACK,KAAK,EAAC,SAAAA,CAASH,CAAC,EAAC;IAACG,KAAK,GAACH,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAII,MAAM;AAACP,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACM,MAAM,EAAC,SAAAA,CAASJ,CAAC,EAAC;IAACI,MAAM,GAACJ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAGjM,IAAME,OAAO,GAAG,CAAC,CAAC;AAElBA,OAAO,CAACG,kBAAkB,GAAG,UAAAC,GAAG;EAAA,OAAIA,GAAG,CAACC,MAAM,KAAK,EAAE,IAAI,aAAa,CAACC,IAAI,CAACF,GAAG,CAAC;AAAA;AAEhFJ,OAAO,CAACO,QAAQ;EACd,SAAAA,SAAaC,SAAS,EAAE;IACtB;IACA,IAAIA,SAAS,EAAE;MACbA,SAAS,GAAGA,SAAS,CAACC,WAAW,CAAC,CAAC;MACnC,IAAI,CAACT,OAAO,CAACG,kBAAkB,CAACK,SAAS,CAAC,EAAE;QAC1C,MAAM,IAAIE,KAAK,CAAC,qDAAqD,CAAC;MACxE;MACA;MACA,IAAI,CAACC,IAAI,GAAGH,SAAS;IACvB,CAAC,MAAM;MACL,IAAI,CAACG,IAAI,GAAGT,MAAM,CAACM,SAAS,CAAC,EAAE,CAAC;IAClC;EACF;EAAC,IAAAI,MAAA,GAAAL,QAAA,CAAAM,SAAA;EAAAD,MAAA,CAEDE,MAAM;IAAN,SAAAA,MAAMA,CAACC,KAAK,EAAE;MACZ,OAAOA,KAAK,YAAYf,OAAO,CAACO,QAAQ,IACxC,IAAI,CAACS,OAAO,CAAC,CAAC,KAAKD,KAAK,CAACC,OAAO,CAAC,CAAC;IACpC;IAAC,OAHDF,MAAM;EAAA;EAAAF,MAAA,CAKNK,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAG;MACT,uBAAoB,IAAI,CAACN,IAAI;IAC/B;IAAC,OAFDM,QAAQ;EAAA;EAAAL,MAAA,CAIRM,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,IAAIlB,OAAO,CAACO,QAAQ,CAAC,IAAI,CAACI,IAAI,CAAC;IACxC;IAAC,OAFDO,KAAK;EAAA;EAAAN,MAAA,CAILO,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAG;MACT,OAAO,KAAK;IACd;IAAC,OAFDA,QAAQ;EAAA;EAAAP,MAAA,CAIRQ,YAAY;IAAZ,SAAAA,YAAYA,CAAA,EAAG;MACb,OAAOC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAACX,IAAI,CAACY,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD;IAAC,OAFDH,YAAY;EAAA;EAAAR,MAAA,CAIZI,OAAO;IAAP,SAAAA,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACL,IAAI;IAClB;IAAC,OAFDK,OAAO;EAAA;EAAAJ,MAAA,CAIPY,WAAW;IAAX,SAAAA,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACR,OAAO,CAAC,CAAC;IACvB;IAAC,OAFDQ,WAAW;EAAA;EAAAZ,MAAA,CAIXa,WAAW;IAAX,SAAAA,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACT,OAAO,CAAC,CAAC;IACvB;IAAC,OAFDS,WAAW;EAAA;EAAA,OAAAlB,QAAA;AAAA,GAIZ;AAEDN,KAAK,CAACyB,OAAO,CAAC,KAAK,EAAE,UAAAtB,GAAG;EAAA,OAAI,IAAIJ,OAAO,CAACO,QAAQ,CAACH,GAAG,CAAC;AAAA,EAAC;AAEtDJ,OAAO,CAAC2B,WAAW,GAAG,UAACC,EAAE,EAAK;EAC5B,IAAIA,EAAE,YAAY5B,OAAO,CAACO,QAAQ,EAAE;IAClC,OAAOqB,EAAE,CAACZ,OAAO,CAAC,CAAC;EACrB,CAAC,MAAM,IAAI,OAAOY,EAAE,KAAK,QAAQ,EAAE;IACjC,IAAIC,SAAS,GAAGD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;IAC5B,IAAIF,EAAE,KAAK,EAAE,EAAE;MACb,OAAOA,EAAE;IACX,CAAC,MAAM,IAAIC,SAAS,KAAK,GAAG;IAAI;IACrBA,SAAS,KAAK,GAAG;IAAI;IACrB7B,OAAO,CAACG,kBAAkB,CAACyB,EAAE,CAAC;IAAI;IAClCC,SAAS,KAAK,GAAG,EAAE;MAAE;MAC9B,aAAWD,EAAE;IACf,CAAC,MAAM;MACL,OAAOA,EAAE,CAAC,CAAC;IACb;EACF,CAAC,MAAM,IAAIA,EAAE,KAAKG,SAAS,EAAE;IAC3B,OAAO,GAAG;EACZ,CAAC,MAAM,IAAIrC,OAAA,CAAOkC,EAAE,MAAK,QAAQ,IAAIA,EAAE,KAAK,IAAI,EAAE;IAChD,MAAM,IAAIlB,KAAK,CAAC,sEAAsE,CAAC;EACzF,CAAC,MAAM;IAAE;IACP,aAAWsB,IAAI,CAACC,SAAS,CAACL,EAAE,CAAC;EAC/B;AACF,CAAC;AAED5B,OAAO,CAACkC,OAAO,GAAG,UAACN,EAAE,EAAK;EACxB,IAAIC,SAAS,GAAGD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;EAC5B,IAAIF,EAAE,KAAK,EAAE,EAAE;IACb,OAAOA,EAAE;EACX,CAAC,MAAM,IAAIA,EAAE,KAAK,GAAG,EAAE;IACrB,OAAOG,SAAS;EAClB,CAAC,MAAM,IAAIF,SAAS,KAAK,GAAG,EAAE;IAC5B,OAAOD,EAAE,CAACL,MAAM,CAAC,CAAC,CAAC;EACrB,CAAC,MAAM,IAAIM,SAAS,KAAK,GAAG,EAAE;IAC5B,OAAOG,IAAI,CAACG,KAAK,CAACP,EAAE,CAACL,MAAM,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,MAAM,IAAIvB,OAAO,CAACG,kBAAkB,CAACyB,EAAE,CAAC,EAAE;IACzC,OAAO,IAAI5B,OAAO,CAACO,QAAQ,CAACqB,EAAE,CAAC;EACjC,CAAC,MAAM;IACL,OAAOA,EAAE;EACX;AACF,CAAC,C", "file": "/packages/mongo-id.js", "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON> } from 'meteor/ejson';\nimport { Random } from 'meteor/random';\n\nconst MongoID = {};\n\nMongoID._looksLikeObjectID = str => str.length === 24 && /^[0-9a-f]*$/.test(str);\n\nMongoID.ObjectID = class ObjectID {\n  constructor (hexString) {\n    //random-based impl of Mongo ObjectID\n    if (hexString) {\n      hexString = hexString.toLowerCase();\n      if (!MongoID._looksLikeObjectID(hexString)) {\n        throw new Error('Invalid hexadecimal string for creating an ObjectID');\n      }\n      // meant to work with _.isEqual(), which relies on structural equality\n      this._str = hexString;\n    } else {\n      this._str = Random.hexString(24);\n    }\n  }\n\n  equals(other) {\n    return other instanceof MongoID.ObjectID &&\n    this.valueOf() === other.valueOf();\n  }\n\n  toString() {\n    return `ObjectID(\"${this._str}\")`;\n  }\n\n  clone() {\n    return new MongoID.ObjectID(this._str);\n  }\n\n  typeName() {\n    return 'oid';\n  }\n\n  getTimestamp() {\n    return Number.parseInt(this._str.substr(0, 8), 16);\n  }\n\n  valueOf() {\n    return this._str;\n  }\n\n  toJSONValue() {\n    return this.valueOf();\n  }\n\n  toHexString() {\n    return this.valueOf();\n  }\n\n}\n\nEJSON.addType('oid', str => new MongoID.ObjectID(str));\n\nMongoID.idStringify = (id) => {\n  if (id instanceof MongoID.ObjectID) {\n    return id.valueOf();\n  } else if (typeof id === 'string') {\n    var firstChar = id.charAt(0);\n    if (id === '') {\n      return id;\n    } else if (firstChar === '-' || // escape previously dashed strings\n               firstChar === '~' || // escape escaped numbers, true, false\n               MongoID._looksLikeObjectID(id) || // escape object-id-form strings\n               firstChar === '{') { // escape object-form strings, for maybe implementing later\n      return `-${id}`;\n    } else {\n      return id; // other strings go through unchanged.\n    }\n  } else if (id === undefined) {\n    return '-';\n  } else if (typeof id === 'object' && id !== null) {\n    throw new Error('Meteor does not currently support objects other than ObjectID as ids');\n  } else { // Numbers, true, false, null\n    return `~${JSON.stringify(id)}`;\n  }\n};\n\nMongoID.idParse = (id) => {\n  var firstChar = id.charAt(0);\n  if (id === '') {\n    return id;\n  } else if (id === '-') {\n    return undefined;\n  } else if (firstChar === '-') {\n    return id.substr(1);\n  } else if (firstChar === '~') {\n    return JSON.parse(id.substr(1));\n  } else if (MongoID._looksLikeObjectID(id)) {\n    return new MongoID.ObjectID(id);\n  } else {\n    return id;\n  }\n};\n\nexport { MongoID };\n"]}