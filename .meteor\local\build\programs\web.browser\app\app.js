var require = meteorInstall({"imports":{"api":{"ai":{"ollamaService.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/api/ai/ollamaService.js                                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _Meteor$settings$priv, _Meteor$settings$priv2, _Meteor$settings$priv3, _Meteor$settings$priv4;
  module1.export({
    OllamaService: () => OllamaService
  });
  ___INIT_METEOR_FAST_REFRESH(module);
  // Initialize Ollama client
  let ollamaEndpoint = ((_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : (_Meteor$settings$priv2 = _Meteor$settings$priv.ollama) === null || _Meteor$settings$priv2 === void 0 ? void 0 : _Meteor$settings$priv2.endpoint) || 'http://localhost:11434';
  let defaultModel = ((_Meteor$settings$priv3 = Meteor.settings.private) === null || _Meteor$settings$priv3 === void 0 ? void 0 : (_Meteor$settings$priv4 = _Meteor$settings$priv3.ollama) === null || _Meteor$settings$priv4 === void 0 ? void 0 : _Meteor$settings$priv4.model) || 'tinyllama:1.1b'; // Changed to tinyllama:1.1b (extremely small model)

  // Helper function to handle API errors
  const handleApiError = (error, context) => {
    console.error("Error in ".concat(context, ":"), error);
    if (error.code === 'ECONNREFUSED') {
      throw new Meteor.Error('connection-failed', 'Could not connect to Ollama. Please make sure Ollama is running on your machine.');
    }
    throw new Meteor.Error('ai-service-error', "AI service error: ".concat(error.message));
  };

  // Helper function to call Ollama API
  async function callOllama(prompt) {
    try {
      const response = await fetch("".concat(ollamaEndpoint, "/api/generate"), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: defaultModel,
          prompt: prompt,
          stream: false
        })
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Meteor.Error('ai-service-error', "AI service error: ".concat(errorData.error || response.statusText));
      }
      const data = await response.json();
      return data.response;
    } catch (error) {
      handleApiError(error, 'Ollama API call');
    }
  }
  const OllamaService = {
    callOllama,
    async analyzeTask(task) {
      // Create a simplified prompt with minimal data
      const prompt = "Analyze this task: ".concat(task.title || 'Untitled task', ". ").concat(task.description || '');
      console.log('Ollama prompt (analyzeTask):', prompt);
      return await callOllama(prompt);
    },
    async suggestTaskOptimization(taskId) {
      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('task-not-found', 'Task not found');
      }
      const prompt = "Suggest improvements for this task: ".concat(task.title || 'Untitled task', ". ").concat(task.description || '');
      console.log('Ollama prompt (suggestTaskOptimization):', prompt);
      return await callOllama(prompt);
    },
    async predictTaskCompletion(taskId) {
      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('task-not-found', 'Task not found');
      }
      const prompt = "Predict completion time for: ".concat(task.title || 'Untitled task', ". ").concat(task.description || '');
      console.log('Ollama prompt (predictTaskCompletion):', prompt);
      return await callOllama(prompt);
    },
    async generateTaskSummary(taskId) {
      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('task-not-found', 'Task not found');
      }
      const prompt = "Summarize this task: ".concat(task.title || 'Untitled task', ". ").concat(task.description || '');
      console.log('Ollama prompt (generateTaskSummary):', prompt);
      return await callOllama(prompt);
    },
    async suggestTaskDivision(task) {
      const prompt = "Suggest how to divide this task among team members: ".concat(task.title || 'Untitled task', ". ").concat(task.description || '');
      console.log('Ollama prompt (suggestTaskDivision):', prompt);
      return await callOllama(prompt);
    },
    async recommendTools(taskId) {
      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('task-not-found', 'Task not found');
      }
      const prompt = "Recommend tools and techniques for this task: ".concat(task.title || 'Untitled task', ". ").concat(task.description || '');
      console.log('Ollama prompt (recommendTools):', prompt);
      return await callOllama(prompt);
    },
    async predictDailyProgress(taskId) {
      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('task-not-found', 'Task not found');
      }
      const prompt = "Predict daily progress needed to complete by deadline for: ".concat(task.title || 'Untitled task', ". ").concat(task.description || '', ". Due date: ").concat(task.dueDate || 'Not specified');
      console.log('Ollama prompt (predictDailyProgress):', prompt);
      return await callOllama(prompt);
    }
  };
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"index.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/api/index.js                                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    Tasks: () => Tasks
  });
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 0);
  let Tasks;
  module1.link("./tasks", {
    Tasks(v) {
      Tasks = v;
    }
  }, 1);
  ___INIT_METEOR_FAST_REFRESH(module);
  // Ensure collections are available on both client and server
  if (Meteor.isClient) {
    window.Tasks = Tasks;
  }
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/api/tasks.js                                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  let _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default(v) {
      _objectSpread = v;
    }
  }, 0);
  module1.export({
    Tasks: () => Tasks,
    taskCategories: () => taskCategories,
    taskLabels: () => taskLabels
  });
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 0);
  let Mongo;
  module1.link("meteor/mongo", {
    Mongo(v) {
      Mongo = v;
    }
  }, 1);
  let check;
  module1.link("meteor/check", {
    check(v) {
      check = v;
    }
  }, 2);
  let Match;
  module1.link("meteor/check", {
    Match(v) {
      Match = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  const Tasks = new Mongo.Collection('tasks');
  const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
  const taskLabels = [{
    name: 'Bug',
    color: '#ef4444'
  }, {
    name: 'Feature',
    color: '#3b82f6'
  }, {
    name: 'Enhancement',
    color: '#10b981'
  }, {
    name: 'Documentation',
    color: '#8b5cf6'
  }, {
    name: 'Urgent',
    color: '#f59e0b'
  }, {
    name: 'Blocked',
    color: '#6b7280'
  }];
  if (Meteor.isServer) {
    // Publications
    Meteor.publish('tasks', async function () {
      var _user$roles;
      if (!this.userId) {
        return this.ready();
      }

      // Get user's role
      const user = await Meteor.users.findOneAsync(this.userId);
      const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

      // If admin, show all tasks
      if (isAdmin) {
        return Tasks.find({}, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      }

      // For team members, show tasks they're assigned to or created
      return Tasks.find({
        $or: [{
          assignedTo: this.userId
        }, {
          createdBy: this.userId
        }]
      }, {
        sort: {
          createdAt: -1
        },
        fields: {
          title: 1,
          description: 1,
          startDate: 1,
          dueDate: 1,
          priority: 1,
          status: 1,
          assignedTo: 1,
          checklist: 1,
          category: 1,
          labels: 1,
          progress: 1,
          attachments: 1,
          links: 1,
          createdAt: 1,
          createdBy: 1,
          updatedAt: 1,
          updatedBy: 1
        }
      });
    });

    // Publish user data for tasks
    Meteor.publish('taskUsers', function () {
      console.log('Starting taskUsers publication');
      if (!this.userId) {
        console.log('No userId, returning ready');
        return this.ready();
      }

      // Get all tasks
      const tasks = Tasks.find({}).fetch();
      console.log('Found tasks:', tasks.length);

      // Collect all user IDs from tasks
      const userIds = new Set();
      tasks.forEach(task => {
        // Add users who uploaded attachments
        if (task.attachments) {
          task.attachments.forEach(attachment => {
            if (attachment.uploadedBy) {
              userIds.add(String(attachment.uploadedBy));
            }
          });
        }
        // Add users who added links
        if (task.links) {
          task.links.forEach(link => {
            if (link.addedBy) {
              userIds.add(String(link.addedBy));
            }
          });
        }
        // Add assigned users
        if (task.assignedTo) {
          task.assignedTo.forEach(userId => {
            userIds.add(String(userId));
          });
        }
      });
      const userIdArray = Array.from(userIds);
      console.log('Publishing user data for IDs:', userIdArray);

      // Find users and log what we found
      const users = Meteor.users.find({
        _id: {
          $in: userIdArray
        }
      }, {
        fields: {
          _id: 1,
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      }).fetch();
      console.log('Found users:', users.map(u => {
        var _u$profile, _u$profile2;
        return {
          _id: u._id,
          name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
          hasProfile: !!u.profile
        };
      }));

      // Return the cursor
      return Meteor.users.find({
        _id: {
          $in: userIdArray
        }
      }, {
        fields: {
          _id: 1,
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      });
    });

    // Add a specific publication for a single task
    Meteor.publish('task', function (taskId) {
      var _user$roles2;
      check(taskId, String);
      if (!this.userId) {
        return this.ready();
      }
      const user = Meteor.users.findOne(this.userId);
      const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

      // Return a cursor that will update reactively
      const cursor = Tasks.find({
        _id: taskId
      }, {
        fields: {
          title: 1,
          description: 1,
          startDate: 1,
          dueDate: 1,
          priority: 1,
          status: 1,
          assignedTo: 1,
          checklist: 1,
          category: 1,
          labels: 1,
          progress: 1,
          attachments: 1,
          links: 1,
          createdAt: 1,
          createdBy: 1,
          updatedAt: 1,
          updatedBy: 1
        }
      });

      // Log for debugging
      console.log('Publishing task:', taskId);
      cursor.observe({
        added: doc => console.log('Task added to publication:', doc._id),
        changed: doc => console.log('Task changed in publication:', doc._id),
        removed: doc => console.log('Task removed from publication:', doc._id)
      });
      return cursor;
    });
  }
  Meteor.methods({
    async 'tasks.insert'(task) {
      check(task, {
        title: String,
        description: String,
        startDate: Date,
        dueDate: Date,
        priority: String,
        status: String,
        assignedTo: Array,
        checklist: Array,
        category: String,
        labels: Array,
        progress: Number
      });
      if (!this.userId) {
        throw new Meteor.Error('Not authorized.');
      }
      console.log('Creating new task:', task); // Debug log

      // Process checklist items
      const processedChecklist = task.checklist.map(item => ({
        text: item.text,
        completed: item.completed || false
      }));
      const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
        createdAt: new Date(),
        createdBy: this.userId,
        updatedAt: new Date(),
        updatedBy: this.userId,
        progress: task.progress || 0,
        status: 'pending',
        // Default status
        checklist: processedChecklist,
        labels: task.labels || [],
        category: task.category || '',
        assignedTo: task.assignedTo || []
      });
      console.log('Inserting task with values:', taskToInsert); // Debug log

      try {
        const result = await Tasks.insertAsync(taskToInsert);
        console.log('Task created successfully:', result); // Debug log
        return result;
      } catch (error) {
        console.error('Error creating task:', error);
        throw new Meteor.Error('task-creation-failed', error.message);
      }
    },
    async 'tasks.update'(taskId, task) {
      try {
        var _user$roles3;
        console.log('Starting task update:', {
          taskId,
          task
        });
        check(taskId, String);
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          assignedTo: Array,
          checklist: Array,
          category: Match.Optional(String),
          labels: Match.Optional(Array),
          progress: Match.Optional(Number),
          status: Match.Optional(String),
          attachments: Match.Optional(Array)
        });
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
        }
        const existingTask = await Tasks.findOneAsync(taskId);
        if (!existingTask) {
          throw new Meteor.Error('not-found', 'Task not found');
        }

        // Check if user is assigned to the task or is admin
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
        if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
        }

        // Calculate progress based on checklist
        const completedItems = task.checklist.filter(item => item.completed).length;
        const totalItems = task.checklist.length;
        const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        } else {
          status = 'pending';
        }
        const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress,
          status,
          category: task.category || existingTask.category || '',
          labels: task.labels || existingTask.labels || [],
          attachments: task.attachments || existingTask.attachments || []
        });
        const result = await Tasks.updateAsync({
          _id: taskId
        }, {
          $set: taskToUpdate
        });
        if (result === 0) {
          throw new Meteor.Error('update-failed', 'Failed to update task');
        }
        return result;
      } catch (error) {
        console.error('Error updating task:', error);
        if (error instanceof Meteor.Error) {
          throw error;
        }
        throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
      }
    },
    async 'tasks.delete'(taskId) {
      check(taskId, String);
      if (!this.userId) {
        throw new Meteor.Error('Not authorized.');
      }
      try {
        const result = await Tasks.removeAsync(taskId);
        if (result === 0) {
          throw new Meteor.Error('not-found', 'Task not found');
        }
        return result;
      } catch (error) {
        console.error('Error deleting task:', error);
        throw new Meteor.Error('task-delete-failed', error.message);
      }
    },
    'tasks.updateProgress'(taskId, progress) {
      check(taskId, String);
      check(progress, Number);
      if (!this.userId) {
        throw new Meteor.Error('Not authorized.');
      }
      const task = Tasks.findOne(taskId);
      if (!task) {
        throw new Meteor.Error('Task not found.');
      }

      // Check if user is assigned to the task
      if (!task.assignedTo.includes(this.userId)) {
        throw new Meteor.Error('Not authorized to modify this task.');
      }

      // Update task status based on progress
      let status = task.status;
      if (progress === 100) {
        status = 'completed';
      } else if (progress > 0) {
        status = 'in-progress';
      }
      return Tasks.update(taskId, {
        $set: {
          progress,
          status,
          updatedAt: new Date(),
          updatedBy: this.userId
        }
      });
    },
    async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
      try {
        var _user$roles4;
        console.log('Starting toggleChecklistItem with:', {
          taskId,
          itemIndex,
          userId: this.userId
        });
        if (!this.userId) {
          console.log('No user ID found');
          throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
        }

        // Validate inputs
        if (!taskId || typeof taskId !== 'string') {
          console.log('Invalid taskId:', taskId);
          throw new Meteor.Error('invalid-input', 'Invalid task ID');
        }
        if (typeof itemIndex !== 'number' || itemIndex < 0) {
          console.log('Invalid itemIndex:', itemIndex);
          throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
        }
        const task = await Tasks.findOneAsync(taskId);
        console.log('Found task:', task);
        if (!task) {
          console.log('Task not found');
          throw new Meteor.Error('not-found', 'Task not found');
        }

        // Check if user is assigned to the task or is admin
        const user = await Meteor.users.findOneAsync(this.userId);
        console.log('Found user:', user);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
        console.log('Is admin:', isAdmin);
        if (!isAdmin && !task.assignedTo.includes(this.userId)) {
          console.log('User not authorized:', {
            userId: this.userId,
            assignedTo: task.assignedTo
          });
          throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
        }
        const checklist = task.checklist || [];
        console.log('Current checklist:', checklist);
        if (itemIndex >= checklist.length) {
          console.log('Invalid item index:', {
            itemIndex,
            checklistLength: checklist.length
          });
          throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
        }

        // Create a new array to ensure reactivity
        const updatedChecklist = [...checklist];
        updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
          completed: !updatedChecklist[itemIndex].completed
        });
        console.log('Updated checklist:', updatedChecklist);

        // Calculate progress
        const completedItems = updatedChecklist.filter(item => item.completed).length;
        const totalItems = updatedChecklist.length;
        const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

        // Update task status based on progress
        let status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        } else {
          status = 'pending';
        }
        console.log('Updating task with:', {
          taskId,
          updatedChecklist,
          progress,
          status
        });

        // First verify the task still exists
        const existingTask = await Tasks.findOneAsync(taskId);
        if (!existingTask) {
          throw new Meteor.Error('task-not-found', 'Task no longer exists');
        }

        // Perform the update
        const updateResult = await Tasks.updateAsync({
          _id: taskId
        }, {
          $set: {
            checklist: updatedChecklist,
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
        console.log('Update result:', updateResult);
        if (updateResult === 0) {
          throw new Meteor.Error('update-failed', 'Failed to update task');
        }

        // Verify the update
        const updatedTask = await Tasks.findOneAsync(taskId);
        console.log('Task after update:', updatedTask);
        return updateResult;
      } catch (error) {
        console.error('Error in toggleChecklistItem:', error);
        if (error instanceof Meteor.Error) {
          throw error;
        }
        throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
      }
    },
    async 'tasks.addAttachment'(taskId, fileData) {
      try {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('not-found', 'Task not found');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
        }
        if (!fileData || !fileData.name || !fileData.data) {
          throw new Meteor.Error('invalid-input', 'Invalid file data');
        }

        // Ensure we're storing the user ID as a string
        const uploaderId = String(this.userId);

        // Add the file data to attachments with uploader info
        const result = await Tasks.updateAsync({
          _id: taskId
        }, {
          $push: {
            attachments: {
              name: fileData.name,
              type: fileData.type,
              data: fileData.data,
              uploadedAt: new Date(),
              uploadedBy: uploaderId
            }
          },
          $set: {
            updatedAt: new Date(),
            updatedBy: uploaderId
          }
        });
        if (result === 0) {
          throw new Meteor.Error('update-failed', 'Failed to add attachment');
        }

        // Get and return the updated task
        const updatedTask = await Tasks.findOneAsync(taskId);
        console.log('Task after adding attachment:', updatedTask);
        return updatedTask;
      } catch (error) {
        console.error('Error adding attachment:', error);
        if (error instanceof Meteor.Error) {
          throw error;
        }
        throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
      }
    },
    async 'tasks.addLink'(taskId, link) {
      try {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('not-found', 'Task not found');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
        }
        if (!link) {
          throw new Meteor.Error('invalid-input', 'Link URL is required');
        }

        // Validate URL format
        try {
          new URL(link);
        } catch (e) {
          throw new Meteor.Error('invalid-input', 'Invalid URL format');
        }

        // Add the link to links array with adder info
        const result = await Tasks.updateAsync({
          _id: taskId
        }, {
          $push: {
            links: {
              url: link,
              addedAt: new Date(),
              addedBy: String(this.userId)
            }
          },
          $set: {
            updatedAt: new Date(),
            updatedBy: String(this.userId)
          }
        });
        if (result === 0) {
          throw new Meteor.Error('update-failed', 'Failed to add link');
        }

        // Get and return the updated task
        const updatedTask = await Tasks.findOneAsync(taskId);
        console.log('Task after adding link:', updatedTask);
        return updatedTask;
      } catch (error) {
        console.error('Error adding link:', error);
        if (error instanceof Meteor.Error) {
          throw error;
        }
        throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
      }
    },
    async 'tasks.removeAttachment'(taskId, attachmentIndex) {
      try {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('not-found', 'Task not found');
        }

        // Check if the attachment exists
        if (!task.attachments || !task.attachments[attachmentIndex]) {
          throw new Meteor.Error('not-found', 'Attachment not found');
        }
        const attachment = task.attachments[attachmentIndex];

        // Convert both IDs to strings for comparison
        const currentUserId = String(this.userId);
        const uploadedById = String(attachment.uploadedBy);

        // Only allow the uploader to remove the attachment
        if (currentUserId !== uploadedById) {
          throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
        }

        // Create a new array without the specified attachment
        const updatedAttachments = [...task.attachments];
        updatedAttachments.splice(attachmentIndex, 1);

        // Update the task with the new attachments array
        const result = await Tasks.updateAsync({
          _id: taskId
        }, {
          $set: {
            attachments: updatedAttachments,
            updatedAt: new Date(),
            updatedBy: currentUserId
          }
        });
        if (result === 0) {
          throw new Meteor.Error('update-failed', 'Failed to remove attachment');
        }

        // Get and return the updated task
        const updatedTask = await Tasks.findOneAsync(taskId);
        console.log('Task after removing attachment:', updatedTask);
        return updatedTask;
      } catch (error) {
        console.error('Error removing attachment:', error);
        if (error instanceof Meteor.Error) {
          throw error;
        }
        throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
      }
    },
    async 'tasks.removeLink'(taskId, linkIndex) {
      try {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('not-found', 'Task not found');
        }

        // Check if the link exists
        if (!task.links || !task.links[linkIndex]) {
          throw new Meteor.Error('not-found', 'Link not found');
        }
        const link = task.links[linkIndex];

        // Convert both IDs to strings for comparison
        const currentUserId = String(this.userId);
        const addedById = String(link.addedBy);

        // Only allow the user who added the link to remove it
        if (currentUserId !== addedById) {
          throw new Meteor.Error('not-authorized', 'You can only remove your own links');
        }

        // Create a new array without the specified link
        const updatedLinks = [...task.links];
        updatedLinks.splice(linkIndex, 1);

        // Update the task with the new links array
        const result = await Tasks.updateAsync({
          _id: taskId
        }, {
          $set: {
            links: updatedLinks,
            updatedAt: new Date(),
            updatedBy: currentUserId
          }
        });
        if (result === 0) {
          throw new Meteor.Error('update-failed', 'Failed to remove link');
        }

        // Get and return the updated task
        const updatedTask = await Tasks.findOneAsync(taskId);
        console.log('Task after removing link:', updatedTask);
        return updatedTask;
      } catch (error) {
        console.error('Error removing link:', error);
        if (error instanceof Meteor.Error) {
          throw error;
        }
        throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
      }
    },
    async 'tasks.findOne'(taskId) {
      check(taskId, String);
      if (!this.userId) {
        throw new Meteor.Error('Not authorized.');
      }
      const task = await Tasks.findOneAsync(taskId);
      if (!task) {
        throw new Meteor.Error('task-not-found', 'Task not found');
      }
      return task;
    }
  });
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"ui":{"components":{"AIInsightsPanel.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/AIInsightsPanel.jsx                                                                           //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    AIInsightsPanel: () => AIInsightsPanel
  });
  let React, useState, useEffect;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    }
  }, 0);
  let Box, Card, CardContent, Typography, Tabs, Tab, CircularProgress, Alert, Divider, List, ListItem, ListItemText, Chip, IconButton, Tooltip, Button;
  module1.link("@mui/material", {
    Box(v) {
      Box = v;
    },
    Card(v) {
      Card = v;
    },
    CardContent(v) {
      CardContent = v;
    },
    Typography(v) {
      Typography = v;
    },
    Tabs(v) {
      Tabs = v;
    },
    Tab(v) {
      Tab = v;
    },
    CircularProgress(v) {
      CircularProgress = v;
    },
    Alert(v) {
      Alert = v;
    },
    Divider(v) {
      Divider = v;
    },
    List(v) {
      List = v;
    },
    ListItem(v) {
      ListItem = v;
    },
    ListItemText(v) {
      ListItemText = v;
    },
    Chip(v) {
      Chip = v;
    },
    IconButton(v) {
      IconButton = v;
    },
    Tooltip(v) {
      Tooltip = v;
    },
    Button(v) {
      Button = v;
    }
  }, 1);
  let AnalyticsIcon, AssignmentIcon, GroupIcon, TimelineIcon, RefreshIcon, SpeedIcon, LightbulbIcon, ErrorIcon;
  module1.link("@mui/icons-material", {
    Analytics(v) {
      AnalyticsIcon = v;
    },
    Assignment(v) {
      AssignmentIcon = v;
    },
    Group(v) {
      GroupIcon = v;
    },
    Timeline(v) {
      TimelineIcon = v;
    },
    Refresh(v) {
      RefreshIcon = v;
    },
    Speed(v) {
      SpeedIcon = v;
    },
    Lightbulb(v) {
      LightbulbIcon = v;
    },
    Error(v) {
      ErrorIcon = v;
    }
  }, 2);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 3);
  let OllamaService;
  module1.link("../../api/ai/ollamaService", {
    OllamaService(v) {
      OllamaService = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TabPanel = _ref => {
    let {
      children,
      value,
      index
    } = _ref;
    return /*#__PURE__*/React.createElement("div", {
      hidden: value !== index,
      style: {
        padding: '16px'
      }
    }, value === index && children);
  };
  _c = TabPanel;
  const AIInsightsPanel = _ref2 => {
    let {
      taskId,
      task: propTask
    } = _ref2;
    _s();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [insights, setInsights] = useState(null);
    const [selectedTab, setSelectedTab] = useState(0);
    const [retryCount, setRetryCount] = useState(0);
    const fetchInsights = async () => {
      setLoading(true);
      setError(null);
      try {
        let result;
        let taskObj = propTask;
        if (!taskObj || !taskObj.title) {
          setError('Task data is missing or incomplete.');
          setLoading(false);
          return;
        }
        switch (selectedTab) {
          case 0:
            result = await OllamaService.analyzeTask(taskObj);
            break;
          case 1:
            result = await OllamaService.suggestTaskOptimization(taskObj._id);
            break;
          case 2:
            result = await OllamaService.predictTaskCompletion(taskObj._id);
            break;
          case 3:
            result = await OllamaService.generateTaskSummary(taskObj._id);
            break;
          case 4:
            result = await OllamaService.recommendTaskAssignments(taskObj._id);
            break;
          default:
            result = await OllamaService.analyzeTask(taskObj);
        }
        setInsights(result);
        setRetryCount(0); // Reset retry count on success
      } catch (error) {
        if (error.error === 'connection-failed') {
          setError('Could not connect to Ollama. Please make sure Ollama is running on your machine.');
        } else {
          setError(error.message || 'Failed to fetch insights');
        }
      } finally {
        setLoading(false);
      }
    };
    useEffect(() => {
      fetchInsights();
    }, [taskId, selectedTab]);
    const handleTabChange = (event, newValue) => {
      setSelectedTab(newValue);
    };
    const handleRefresh = () => {
      setRetryCount(prev => prev + 1);
      fetchInsights();
    };
    const renderInsightContent = content => {
      if (!content) return null;

      // Split content into sections based on numbered points
      const sections = content.split(/\d\./).filter(Boolean);
      return /*#__PURE__*/React.createElement(List, null, sections.map((section, index) => /*#__PURE__*/React.createElement(ListItem, {
        key: index,
        alignItems: "flex-start"
      }, /*#__PURE__*/React.createElement(ListItemText, {
        primary: /*#__PURE__*/React.createElement(Typography, {
          variant: "subtitle1",
          color: "primary"
        }, section.split('\n')[0].trim()),
        secondary: /*#__PURE__*/React.createElement(Typography, {
          component: "span",
          variant: "body2",
          color: "text.primary",
          style: {
            whiteSpace: 'pre-line'
          }
        }, section.split('\n').slice(1).join('\n').trim())
      }))));
    };
    const renderErrorContent = () => {
      if (!error) return null;
      return /*#__PURE__*/React.createElement(Box, {
        sx: {
          p: 2,
          textAlign: 'center'
        }
      }, /*#__PURE__*/React.createElement(ErrorIcon, {
        color: "error",
        sx: {
          fontSize: 48,
          mb: 2
        }
      }), /*#__PURE__*/React.createElement(Typography, {
        variant: "h6",
        color: "error",
        gutterBottom: true
      }, error), retryCount < 3 && /*#__PURE__*/React.createElement(Button, {
        variant: "contained",
        color: "primary",
        onClick: handleRefresh,
        startIcon: /*#__PURE__*/React.createElement(RefreshIcon, null),
        sx: {
          mt: 2
        }
      }, "Try Again"), retryCount >= 3 && /*#__PURE__*/React.createElement(Typography, {
        variant: "body2",
        color: "text.secondary",
        sx: {
          mt: 2
        }
      }, "Multiple attempts failed. Please try again later."));
    };
    return /*#__PURE__*/React.createElement(Card, {
      sx: {
        maxWidth: 800,
        margin: '20px auto',
        p: 2,
        height: '100%'
      }
    }, /*#__PURE__*/React.createElement(CardContent, null, /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      component: "h2"
    }, "AI Insights"), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Refresh Insights"
    }, /*#__PURE__*/React.createElement(IconButton, {
      onClick: handleRefresh,
      disabled: loading
    }, /*#__PURE__*/React.createElement(RefreshIcon, null)))), /*#__PURE__*/React.createElement(Tabs, {
      value: selectedTab,
      onChange: handleTabChange,
      variant: "fullWidth",
      sx: {
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Tooltip, {
      title: "Analysis"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(AnalyticsIcon, null),
      "aria-label": "Analysis"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Optimization"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(SpeedIcon, null),
      "aria-label": "Optimization"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Prediction"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(TimelineIcon, null),
      "aria-label": "Prediction"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Recommendations"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(GroupIcon, null),
      "aria-label": "Recommendations"
    }))), loading && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(CircularProgress, null)), error && renderErrorContent(), !loading && !error && insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        p: 1
      }
    }, renderInsightContent(insights)), !loading && !error && !insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(LightbulbIcon, {
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Typography, null, "No insights available"))));
  };
  _s(AIInsightsPanel, "Act2AOnU28qM7wyZXfGrXz8QXTY=");
  _c2 = AIInsightsPanel;
  var _c, _c2;
  $RefreshReg$(_c, "TabPanel");
  $RefreshReg$(_c2, "AIInsightsPanel");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"CreateTask.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/CreateTask.jsx                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  let _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default(v) {
      _objectSpread = v;
    }
  }, 0);
  module1.export({
    CreateTask: () => CreateTask
  });
  let React, useState, useEffect;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 2);
  let Formik, Form, Field;
  module1.link("formik", {
    Formik(v) {
      Formik = v;
    },
    Form(v) {
      Form = v;
    },
    Field(v) {
      Field = v;
    }
  }, 3);
  let Yup;
  module1.link("yup", {
    "*"(v) {
      Yup = v;
    }
  }, 4);
  let taskCategories, taskLabels;
  module1.link("/imports/api/tasks", {
    taskCategories(v) {
      taskCategories = v;
    },
    taskLabels(v) {
      taskLabels = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TaskSchema = Yup.object().shape({
    title: Yup.string().min(3, 'Title must be at least 3 characters').required('Title is required'),
    description: Yup.string().min(10, 'Description must be at least 10 characters').required('Description is required'),
    startDate: Yup.date().required('Start date is required'),
    dueDate: Yup.date().min(Yup.ref('startDate'), 'Due date must be after start date').required('Due date is required'),
    priority: Yup.string().oneOf(['high', 'medium', 'low'], 'Invalid priority').required('Priority is required'),
    assignedTo: Yup.array().min(1, 'Assign at least one team member').required('Assigned members are required'),
    checklist: Yup.array().of(Yup.object().shape({
      text: Yup.string().required('Checklist item cannot be empty'),
      completed: Yup.boolean()
    }))
  });
  const CreateTask = _ref => {
    let {
      onCancel
    } = _ref;
    _s();
    const [submitError, setSubmitError] = useState('');

    // Subscribe to team members data
    const {
      teamMembers,
      isLoading,
      currentUser,
      error
    } = useTracker(() => {
      const subscriptions = {
        userData: Meteor.subscribe('userData'),
        teamMembers: Meteor.subscribe('teamMembers')
      };
      const user = Meteor.user();
      if (!user) {
        return {
          isLoading: false,
          error: 'Not logged in',
          currentUser: null,
          teamMembers: []
        };
      }
      if (!subscriptions.userData.ready() || !subscriptions.teamMembers.ready()) {
        return {
          isLoading: true,
          currentUser: user,
          teamMembers: [],
          error: null
        };
      }
      try {
        const members = Meteor.users.find({
          $or: [{
            'roles': 'team-member'
          }, {
            'profile.role': 'team-member'
          }]
        }, {
          fields: {
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.fullName': 1
          },
          sort: {
            'profile.firstName': 1,
            'profile.lastName': 1
          }
        }).fetch();
        return {
          teamMembers: members,
          currentUser: user,
          isLoading: false,
          error: null
        };
      } catch (err) {
        console.error('Error fetching team members:', err);
        return {
          isLoading: false,
          error: 'Error loading team members',
          currentUser: user,
          teamMembers: []
        };
      }
    }, []);
    const handleSubmit = async (values, _ref2) => {
      let {
        setSubmitting
      } = _ref2;
      try {
        console.log('Creating task with values:', values);
        const taskToCreate = _objectSpread(_objectSpread({}, values), {}, {
          startDate: new Date(values.startDate),
          dueDate: new Date(values.dueDate),
          checklist: values.checklist.filter(item => item.text.trim() !== ''),
          progress: 0,
          status: 'pending',
          category: values.category || '',
          labels: values.labels || [],
          assignedTo: values.assignedTo || []
        });
        console.log('Sending task creation to server:', taskToCreate);
        const result = await new Promise((resolve, reject) => {
          Meteor.call('tasks.insert', taskToCreate, (error, result) => {
            if (error) {
              console.error('Error creating task:', error);
              reject(error);
            } else {
              console.log('Task created successfully:', result);
              resolve(result);
            }
          });
        });
        console.log('Task creation result:', result);
        onCancel();
      } catch (error) {
        console.error('Error creating task:', error);
        setSubmitError(error.message || 'Failed to create task');
      } finally {
        setSubmitting(false);
      }
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Create New Task"), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: onCancel,
      className: "btn btn-secondary",
      style: {
        padding: '8px'
      }
    }, /*#__PURE__*/React.createElement("svg", {
      className: "w-5 h-5",
      fill: "none",
      stroke: "currentColor",
      viewBox: "0 0 24 24"
    }, /*#__PURE__*/React.createElement("path", {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2,
      d: "M6 18L18 6M6 6l12 12"
    })))), submitError && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#fee2e2',
        color: '#dc2626',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, submitError), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        title: '',
        description: '',
        startDate: new Date().toISOString().split('T')[0],
        dueDate: new Date().toISOString().split('T')[0],
        priority: 'medium',
        status: 'pending',
        assignedTo: [],
        checklist: [],
        category: '',
        labels: [],
        progress: 0
      },
      validationSchema: TaskSchema,
      onSubmit: handleSubmit
    }, _ref3 => {
      let {
        values,
        errors,
        touched,
        setFieldValue,
        isSubmitting
      } = _ref3;
      return /*#__PURE__*/React.createElement(Form, {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "title",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Task Title"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "title",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task title"
      }), errors.title && touched.title && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.title)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "description",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Description"), /*#__PURE__*/React.createElement(Field, {
        as: "textarea",
        name: "description",
        rows: 4,
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task description"
      }), errors.description && touched.description && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.description)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "startDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Start Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "startDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.startDate && touched.startDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.startDate)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "dueDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Due Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "dueDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.dueDate && touched.dueDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.dueDate))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "priority",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Priority"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "priority",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }, /*#__PURE__*/React.createElement("option", {
        value: "high"
      }, "High"), /*#__PURE__*/React.createElement("option", {
        value: "medium"
      }, "Medium"), /*#__PURE__*/React.createElement("option", {
        value: "low"
      }, "Low")), errors.priority && touched.priority && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.priority)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Checklist Items"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, values.checklist.map((item, index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          gap: '8px',
          alignItems: 'flex-start'
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "checkbox",
        checked: item.completed,
        onChange: e => {
          const newChecklist = [...values.checklist];
          newChecklist[index].completed = e.target.checked;
          setFieldValue('checklist', newChecklist);
        },
        style: {
          width: '16px',
          height: '16px',
          marginTop: '4px'
        }
      }), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "checklist.".concat(index, ".text"),
        placeholder: "Enter checklist item",
        style: {
          flex: 1,
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: () => {
          const newChecklist = values.checklist.filter((_, i) => i !== index);
          setFieldValue('checklist', newChecklist);
        },
        style: {
          padding: '8px',
          color: '#dc2626',
          background: '#fee2e2',
          border: '1px solid #fecaca',
          borderRadius: '4px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minWidth: '32px',
          height: '32px'
        },
        title: "Delete item"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "w-5 h-5",
        fill: "none",
        stroke: "currentColor",
        viewBox: "0 0 24 24"
      }, /*#__PURE__*/React.createElement("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
      }))))), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: () => {
          setFieldValue('checklist', [...values.checklist, {
            text: '',
            completed: false
          }]);
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          borderRadius: '6px',
          border: '1px solid #e2e8f0',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          width: 'fit-content'
        }
      }, /*#__PURE__*/React.createElement("svg", {
        className: "w-5 h-5",
        fill: "none",
        stroke: "currentColor",
        viewBox: "0 0 24 24"
      }, /*#__PURE__*/React.createElement("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M12 4v16m8-8H4"
      })), "Add Checklist Item"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Assign Team Members"), error ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem'
        }
      }, error) : isLoading ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "Loading team members...") : (teamMembers === null || teamMembers === void 0 ? void 0 : teamMembers.length) === 0 ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "No team members available") : /*#__PURE__*/React.createElement("div", {
        style: {
          maxHeight: '200px',
          overflowY: 'auto',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          padding: '8px'
        }
      }, teamMembers.map(member => {
        var _values$assignedTo, _values$assignedTo2;
        return /*#__PURE__*/React.createElement("div", {
          key: member._id,
          onClick: () => {
            const currentAssigned = values.assignedTo || [];
            const newAssigned = currentAssigned.includes(member._id) ? currentAssigned.filter(id => id !== member._id) : [...currentAssigned, member._id];
            setFieldValue('assignedTo', newAssigned);
          },
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '4px',
            backgroundColor: (_values$assignedTo = values.assignedTo) !== null && _values$assignedTo !== void 0 && _values$assignedTo.includes(member._id) ? '#f1f5f9' : 'transparent'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: (_values$assignedTo2 = values.assignedTo) === null || _values$assignedTo2 === void 0 ? void 0 : _values$assignedTo2.includes(member._id),
          onChange: () => {},
          style: {
            width: '16px',
            height: '16px',
            borderRadius: '4px',
            border: '1px solid #e2e8f0'
          }
        }), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.875rem',
            fontWeight: '500'
          }
        }, member.profile.firstName, " ", member.profile.lastName), /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.75rem',
            color: '#64748b'
          }
        }, member.emails[0].address)));
      })), errors.assignedTo && touched.assignedTo && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.assignedTo)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: onCancel,
        style: {
          padding: '8px 16px',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          cursor: 'pointer'
        }
      }, "Cancel"), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        disabled: isSubmitting,
        style: {
          padding: '8px 16px',
          backgroundColor: '#16a34a',
          color: '#ffffff',
          borderRadius: '6px',
          cursor: isSubmitting ? 'not-allowed' : 'pointer',
          opacity: isSubmitting ? 0.5 : 1
        }
      }, isSubmitting ? 'Creating...' : 'Create Task')));
    })));
  };
  _s(CreateTask, "UUPI7BARsPMCqciM3wKOjHElPdA=", false, function () {
    return [useTracker];
  });
  _c = CreateTask;
  var _c;
  $RefreshReg$(_c, "CreateTask");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"EditTask.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/EditTask.jsx                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  let _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default(v) {
      _objectSpread = v;
    }
  }, 0);
  module1.export({
    EditTask: () => EditTask
  });
  let React, useState;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 2);
  let Formik, Form, Field;
  module1.link("formik", {
    Formik(v) {
      Formik = v;
    },
    Form(v) {
      Form = v;
    },
    Field(v) {
      Field = v;
    }
  }, 3);
  let Yup;
  module1.link("yup", {
    "*"(v) {
      Yup = v;
    }
  }, 4);
  let taskCategories, taskLabels;
  module1.link("/imports/api/tasks", {
    taskCategories(v) {
      taskCategories = v;
    },
    taskLabels(v) {
      taskLabels = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TaskSchema = Yup.object().shape({
    title: Yup.string().min(3, 'Title must be at least 3 characters').required('Title is required'),
    description: Yup.string().min(10, 'Description must be at least 10 characters').required('Description is required'),
    startDate: Yup.date().required('Start date is required'),
    dueDate: Yup.date().min(Yup.ref('startDate'), 'Due date must be after start date').required('Due date is required'),
    priority: Yup.string().oneOf(['high', 'medium', 'low'], 'Invalid priority').required('Priority is required'),
    assignedTo: Yup.array().min(1, 'Assign at least one team member').required('Assigned members are required'),
    checklist: Yup.array().of(Yup.object().shape({
      text: Yup.string().required('Checklist item cannot be empty'),
      completed: Yup.boolean()
    }))
  });
  const EditTask = _ref => {
    let {
      task,
      onCancel,
      onUpdate
    } = _ref;
    _s();
    const [submitError, setSubmitError] = useState('');

    // Subscribe to team members data
    const {
      teamMembers,
      isLoading,
      currentUser,
      error
    } = useTracker(() => {
      const subscriptions = {
        userData: Meteor.subscribe('userData'),
        teamMembers: Meteor.subscribe('teamMembers')
      };
      const user = Meteor.user();
      if (!user) {
        return {
          isLoading: false,
          error: 'Not logged in',
          currentUser: null,
          teamMembers: []
        };
      }
      if (!subscriptions.userData.ready() || !subscriptions.teamMembers.ready()) {
        return {
          isLoading: true,
          currentUser: user,
          teamMembers: [],
          error: null
        };
      }
      try {
        const members = Meteor.users.find({
          $or: [{
            'roles': 'team-member'
          }, {
            'profile.role': 'team-member'
          }]
        }, {
          fields: {
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.fullName': 1
          },
          sort: {
            'profile.firstName': 1,
            'profile.lastName': 1
          }
        }).fetch();
        return {
          teamMembers: members,
          currentUser: user,
          isLoading: false,
          error: null
        };
      } catch (err) {
        console.error('Error fetching team members:', err);
        return {
          isLoading: false,
          error: 'Error loading team members',
          currentUser: user,
          teamMembers: []
        };
      }
    }, []);
    const handleSubmit = async (values, _ref2) => {
      let {
        setSubmitting
      } = _ref2;
      try {
        console.log('Form submitted with values:', values); // Debug log

        // Format dates to ISO string
        const formattedValues = _objectSpread(_objectSpread({}, values), {}, {
          startDate: new Date(values.startDate),
          dueDate: new Date(values.dueDate),
          // Ensure all required fields are present
          title: values.title.trim(),
          description: values.description.trim(),
          assignedTo: values.assignedTo || [],
          checklist: values.checklist || []
        });
        console.log('Updating task with values:', formattedValues); // Debug log

        const result = await new Promise((resolve, reject) => {
          Meteor.call('tasks.update', task._id, formattedValues, (error, result) => {
            if (error) {
              console.error('Error updating task:', error);
              reject(error);
            } else {
              console.log('Task updated successfully:', result);
              resolve(result);
            }
          });
        });
        console.log('Task update result:', result);
        onCancel(); // Call onCancel after successful update
      } catch (error) {
        console.error('Error updating task:', error);
        setSubmitError(error.message || 'Failed to update task');
      } finally {
        setSubmitting(false);
      }
    };
    if (!task) {
      return /*#__PURE__*/React.createElement("div", null, "Loading task data...");
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Edit Task"), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: onCancel,
      className: "btn btn-secondary",
      style: {
        padding: '8px'
      }
    }, /*#__PURE__*/React.createElement("svg", {
      className: "w-5 h-5",
      fill: "none",
      stroke: "currentColor",
      viewBox: "0 0 24 24"
    }, /*#__PURE__*/React.createElement("path", {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2,
      d: "M6 18L18 6M6 6l12 12"
    })))), submitError && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#fee2e2',
        color: '#dc2626',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, submitError), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        title: task.title || '',
        description: task.description || '',
        startDate: task.startDate ? new Date(task.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        priority: task.priority || 'medium',
        assignedTo: task.assignedTo || [],
        checklist: task.checklist || []
      },
      validationSchema: TaskSchema,
      onSubmit: handleSubmit,
      enableReinitialize: true
    }, _ref3 => {
      let {
        values,
        errors,
        touched,
        setFieldValue,
        isSubmitting
      } = _ref3;
      return /*#__PURE__*/React.createElement(Form, {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "title",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Task Title"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "title",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task title"
      }), errors.title && touched.title && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.title)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "description",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Description"), /*#__PURE__*/React.createElement(Field, {
        as: "textarea",
        name: "description",
        rows: 4,
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task description"
      }), errors.description && touched.description && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.description)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "startDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Start Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "startDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.startDate && touched.startDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.startDate)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "dueDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Due Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "dueDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.dueDate && touched.dueDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.dueDate))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "priority",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Priority"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "priority",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }, /*#__PURE__*/React.createElement("option", {
        value: "high"
      }, "High"), /*#__PURE__*/React.createElement("option", {
        value: "medium"
      }, "Medium"), /*#__PURE__*/React.createElement("option", {
        value: "low"
      }, "Low")), errors.priority && touched.priority && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.priority)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Checklist Items"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, values.checklist.map((item, index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          gap: '8px',
          alignItems: 'flex-start'
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "checkbox",
        checked: item.completed,
        onChange: e => {
          const newChecklist = [...values.checklist];
          newChecklist[index].completed = e.target.checked;
          setFieldValue('checklist', newChecklist);
        },
        style: {
          width: '16px',
          height: '16px',
          marginTop: '4px'
        }
      }), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "checklist.".concat(index, ".text"),
        placeholder: "Enter checklist item",
        style: {
          flex: 1,
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: () => {
          const newChecklist = values.checklist.filter((_, i) => i !== index);
          setFieldValue('checklist', newChecklist);
        },
        style: {
          padding: '8px',
          color: '#dc2626',
          background: '#fee2e2',
          border: '1px solid #fecaca',
          borderRadius: '4px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minWidth: '32px',
          height: '32px'
        },
        title: "Delete item"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "w-5 h-5",
        fill: "none",
        stroke: "currentColor",
        viewBox: "0 0 24 24"
      }, /*#__PURE__*/React.createElement("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
      }))))), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: () => {
          setFieldValue('checklist', [...values.checklist, {
            text: '',
            completed: false
          }]);
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          borderRadius: '6px',
          border: '1px solid #e2e8f0',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          width: 'fit-content'
        }
      }, /*#__PURE__*/React.createElement("svg", {
        className: "w-5 h-5",
        fill: "none",
        stroke: "currentColor",
        viewBox: "0 0 24 24"
      }, /*#__PURE__*/React.createElement("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M12 4v16m8-8H4"
      })), "Add Checklist Item"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Assign Team Members"), error ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem'
        }
      }, error) : isLoading ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "Loading team members...") : (teamMembers === null || teamMembers === void 0 ? void 0 : teamMembers.length) === 0 ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "No team members available") : /*#__PURE__*/React.createElement("div", {
        style: {
          maxHeight: '200px',
          overflowY: 'auto',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          padding: '8px'
        }
      }, teamMembers.map(member => {
        var _values$assignedTo, _values$assignedTo2;
        return /*#__PURE__*/React.createElement("div", {
          key: member._id,
          onClick: () => {
            const currentAssigned = values.assignedTo || [];
            const newAssigned = currentAssigned.includes(member._id) ? currentAssigned.filter(id => id !== member._id) : [...currentAssigned, member._id];
            setFieldValue('assignedTo', newAssigned);
          },
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '4px',
            backgroundColor: (_values$assignedTo = values.assignedTo) !== null && _values$assignedTo !== void 0 && _values$assignedTo.includes(member._id) ? '#f1f5f9' : 'transparent'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: (_values$assignedTo2 = values.assignedTo) === null || _values$assignedTo2 === void 0 ? void 0 : _values$assignedTo2.includes(member._id),
          onChange: () => {},
          style: {
            width: '16px',
            height: '16px',
            borderRadius: '4px',
            border: '1px solid #e2e8f0'
          }
        }), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.875rem',
            fontWeight: '500'
          }
        }, member.profile.firstName, " ", member.profile.lastName), /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.75rem',
            color: '#64748b'
          }
        }, member.emails[0].address)));
      })), errors.assignedTo && touched.assignedTo && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.assignedTo)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: onCancel,
        style: {
          padding: '8px 16px',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          cursor: 'pointer'
        }
      }, "Cancel"), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        disabled: isSubmitting,
        style: {
          padding: '8px 16px',
          backgroundColor: '#16a34a',
          color: '#ffffff',
          borderRadius: '6px',
          cursor: isSubmitting ? 'not-allowed' : 'pointer',
          opacity: isSubmitting ? 0.5 : 1
        }
      }, isSubmitting ? 'Saving...' : 'Save Changes')));
    })));
  };
  _s(EditTask, "UUPI7BARsPMCqciM3wKOjHElPdA=", false, function () {
    return [useTracker];
  });
  _c = EditTask;
  var _c;
  $RefreshReg$(_c, "EditTask");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"Header.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/Header.jsx                                                                                    //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    Header: () => Header
  });
  let React;
  module1.link("react", {
    default(v) {
      React = v;
    }
  }, 0);
  let Link, useNavigate;
  module1.link("react-router-dom", {
    Link(v) {
      Link = v;
    },
    useNavigate(v) {
      useNavigate = v;
    }
  }, 1);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 2);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const Header = () => {
    var _user$profile, _user$roles;
    _s();
    const navigate = useNavigate();
    const {
      user
    } = useTracker(() => {
      const subscription = Meteor.subscribe('userData');
      return {
        user: Meteor.user(),
        isLoading: !subscription.ready()
      };
    }, []);
    const userRole = (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);
    const handleLogout = () => {
      Meteor.logout(err => {
        if (err) {
          console.error('Logout error:', err);
        } else {
          navigate('/login', {
            replace: true
          });
        }
      });
    };
    return /*#__PURE__*/React.createElement("nav", {
      style: {
        backgroundColor: '#ffffff',
        borderBottom: '1px solid #e2e8f0',
        padding: '16px 24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '24px'
      }
    }, /*#__PURE__*/React.createElement(Link, {
      to: userRole === 'admin' ? "/admin-dashboard" : "/team-dashboard",
      style: {
        color: '#16a34a',
        textDecoration: 'none',
        fontSize: '1.25rem',
        fontWeight: '600'
      }
    }, "Task Management"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px'
      }
    }, /*#__PURE__*/React.createElement(Link, {
      to: userRole === 'admin' ? "/admin-dashboard/tasks" : "/team-dashboard/tasks",
      style: {
        color: '#475569',
        textDecoration: 'none',
        fontWeight: '500',
        ':hover': {
          color: '#16a34a'
        }
      }
    }, "Tasks"), /*#__PURE__*/React.createElement(Link, {
      to: userRole === 'admin' ? "/admin-dashboard/team" : "/team-dashboard/team",
      style: {
        color: '#475569',
        textDecoration: 'none',
        fontWeight: '500',
        ':hover': {
          color: '#16a34a'
        }
      }
    }, "Team"))), /*#__PURE__*/React.createElement("button", {
      onClick: handleLogout,
      className: "btn btn-secondary",
      style: {
        padding: '8px 16px'
      }
    }, "Logout"));
  };
  _s(Header, "R4j7jVEJP72bhi/6bIckCScI5HY=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = Header;
  var _c;
  $RefreshReg$(_c, "Header");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TaskAssignmentHistory.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TaskAssignmentHistory.jsx                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TaskAssignmentHistory: () => TaskAssignmentHistory
  });
  let React;
  module1.link("react", {
    default(v) {
      React = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 2);
  let Tasks;
  module1.link("/imports/api/tasks", {
    Tasks(v) {
      Tasks = v;
    }
  }, 3);
  let useNavigate;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TaskAssignmentHistory = _ref => {
    let {
      memberId
    } = _ref;
    _s();
    const navigate = useNavigate();
    const {
      tasks,
      isLoading
    } = useTracker(() => {
      const handle = Meteor.subscribe('tasks');
      const isLoading = !handle.ready();
      if (isLoading) {
        return {
          tasks: [],
          isLoading
        };
      }
      const tasks = Tasks.find({
        assignedTo: memberId
      }, {
        sort: {
          createdAt: -1
        }
      }).fetch();
      return {
        tasks,
        isLoading
      };
    }, [memberId]);
    const handleTaskClick = taskId => {
      navigate("/admin-dashboard/tasks/".concat(taskId), {
        state: {
          from: "/admin-dashboard/team/".concat(memberId),
          memberId: memberId
        }
      });
    };
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        className: "animate-pulse"
      }, /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-3/4 mb-4"
      }), /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-1/2"
      }));
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
        overflow: 'hidden'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '20px',
        borderBottom: '1px solid #e2e8f0'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1rem',
        color: '#0f172a',
        fontWeight: '600'
      }
    }, "Task Assignment History")), /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, tasks.length > 0 ? tasks.map(task => /*#__PURE__*/React.createElement("div", {
      key: task._id,
      style: {
        padding: '12px',
        backgroundColor: '#f8fafc',
        borderRadius: '6px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        transition: 'all 0.2s ease'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.875rem',
        color: '#0f172a',
        fontWeight: '500'
      }
    }, task.title), /*#__PURE__*/React.createElement("button", {
      onClick: e => {
        e.stopPropagation();
        handleTaskClick(task._id);
      },
      className: "ml-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors border border-gray-200 bg-white shadow-sm",
      title: "View task details",
      style: {
        minWidth: '32px',
        minHeight: '32px'
      }
    }, /*#__PURE__*/React.createElement("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      className: "h-4 w-4",
      viewBox: "0 0 20 20",
      fill: "currentColor"
    }, /*#__PURE__*/React.createElement("path", {
      fillRule: "evenodd",
      d: "M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",
      clipRule: "evenodd"
    }))))) : /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '12px',
        textAlign: 'center',
        color: '#64748b',
        fontSize: '0.875rem'
      }
    }, "No tasks assigned yet")));
  };
  _s(TaskAssignmentHistory, "MWwWFuPhxvHCHylbFRlLYyeQiNw=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = TaskAssignmentHistory;
  var _c;
  $RefreshReg$(_c, "TaskAssignmentHistory");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamAIInsightsPanel.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamAIInsightsPanel.jsx                                                                       //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamAIInsightsPanel: () => TeamAIInsightsPanel
  });
  let React, useState, useEffect;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    }
  }, 0);
  let Box, Card, CardContent, Typography, Tabs, Tab, CircularProgress, Alert, Divider, List, ListItem, ListItemText, Chip, IconButton, Tooltip, Button;
  module1.link("@mui/material", {
    Box(v) {
      Box = v;
    },
    Card(v) {
      Card = v;
    },
    CardContent(v) {
      CardContent = v;
    },
    Typography(v) {
      Typography = v;
    },
    Tabs(v) {
      Tabs = v;
    },
    Tab(v) {
      Tab = v;
    },
    CircularProgress(v) {
      CircularProgress = v;
    },
    Alert(v) {
      Alert = v;
    },
    Divider(v) {
      Divider = v;
    },
    List(v) {
      List = v;
    },
    ListItem(v) {
      ListItem = v;
    },
    ListItemText(v) {
      ListItemText = v;
    },
    Chip(v) {
      Chip = v;
    },
    IconButton(v) {
      IconButton = v;
    },
    Tooltip(v) {
      Tooltip = v;
    },
    Button(v) {
      Button = v;
    }
  }, 1);
  let GroupIcon, BuildIcon, TimelineIcon, RefreshIcon, ErrorIcon, LightbulbIcon;
  module1.link("@mui/icons-material", {
    Group(v) {
      GroupIcon = v;
    },
    Build(v) {
      BuildIcon = v;
    },
    Timeline(v) {
      TimelineIcon = v;
    },
    Refresh(v) {
      RefreshIcon = v;
    },
    Error(v) {
      ErrorIcon = v;
    },
    Lightbulb(v) {
      LightbulbIcon = v;
    }
  }, 2);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 3);
  let OllamaService;
  module1.link("../../api/ai/ollamaService", {
    OllamaService(v) {
      OllamaService = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TabPanel = _ref => {
    let {
      children,
      value,
      index
    } = _ref;
    return /*#__PURE__*/React.createElement("div", {
      hidden: value !== index,
      style: {
        padding: '16px'
      }
    }, value === index && children);
  };
  _c = TabPanel;
  const TeamAIInsightsPanel = _ref2 => {
    let {
      taskId,
      task: propTask
    } = _ref2;
    _s();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [insights, setInsights] = useState(null);
    const [selectedTab, setSelectedTab] = useState(0);
    const [retryCount, setRetryCount] = useState(0);
    const fetchInsights = async () => {
      setLoading(true);
      setError(null);
      try {
        let result;
        let taskObj = propTask;
        if (!taskObj || !taskObj.title) {
          setError('Task data is missing or incomplete.');
          setLoading(false);
          return;
        }
        switch (selectedTab) {
          case 0:
            result = await OllamaService.suggestTaskDivision(taskObj);
            break;
          case 1:
            result = await OllamaService.recommendTools(taskObj._id);
            break;
          case 2:
            result = await OllamaService.predictDailyProgress(taskObj._id);
            break;
          default:
            result = await OllamaService.suggestTaskDivision(taskObj);
        }
        setInsights(result);
        setRetryCount(0); // Reset retry count on success
      } catch (error) {
        if (error.error === 'connection-failed') {
          setError('Could not connect to Ollama. Please make sure Ollama is running on your machine.');
        } else {
          setError(error.message || 'Failed to fetch insights');
        }
      } finally {
        setLoading(false);
      }
    };
    useEffect(() => {
      fetchInsights();
    }, [taskId, selectedTab]);
    const handleTabChange = (event, newValue) => {
      setSelectedTab(newValue);
    };
    const handleRefresh = () => {
      setRetryCount(prev => prev + 1);
      fetchInsights();
    };
    const renderInsightContent = content => {
      if (!content) return null;

      // Split content into sections based on numbered points
      const sections = content.split(/\d\./).filter(Boolean);
      return /*#__PURE__*/React.createElement(List, null, sections.map((section, index) => /*#__PURE__*/React.createElement(ListItem, {
        key: index,
        alignItems: "flex-start"
      }, /*#__PURE__*/React.createElement(ListItemText, {
        primary: /*#__PURE__*/React.createElement(Typography, {
          variant: "subtitle1",
          color: "primary"
        }, section.split('\n')[0].trim()),
        secondary: /*#__PURE__*/React.createElement(Typography, {
          component: "span",
          variant: "body2",
          color: "text.primary",
          style: {
            whiteSpace: 'pre-line'
          }
        }, section.split('\n').slice(1).join('\n').trim())
      }))));
    };
    const renderErrorContent = () => {
      if (!error) return null;
      return /*#__PURE__*/React.createElement(Box, {
        sx: {
          p: 2,
          textAlign: 'center'
        }
      }, /*#__PURE__*/React.createElement(ErrorIcon, {
        color: "error",
        sx: {
          fontSize: 48,
          mb: 2
        }
      }), /*#__PURE__*/React.createElement(Typography, {
        variant: "h6",
        color: "error",
        gutterBottom: true
      }, error), retryCount < 3 && /*#__PURE__*/React.createElement(Button, {
        variant: "contained",
        color: "primary",
        onClick: handleRefresh,
        startIcon: /*#__PURE__*/React.createElement(RefreshIcon, null),
        sx: {
          mt: 2
        }
      }, "Try Again"), retryCount >= 3 && /*#__PURE__*/React.createElement(Typography, {
        variant: "body2",
        color: "text.secondary",
        sx: {
          mt: 2
        }
      }, "Multiple attempts failed. Please try again later."));
    };
    return /*#__PURE__*/React.createElement(Card, {
      sx: {
        maxWidth: 800,
        margin: '20px auto',
        p: 2,
        height: '100%'
      }
    }, /*#__PURE__*/React.createElement(CardContent, null, /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      component: "h2"
    }, "AI Task Assistant"), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Refresh Insights"
    }, /*#__PURE__*/React.createElement(IconButton, {
      onClick: handleRefresh,
      disabled: loading
    }, /*#__PURE__*/React.createElement(RefreshIcon, null)))), /*#__PURE__*/React.createElement(Tabs, {
      value: selectedTab,
      onChange: handleTabChange,
      variant: "fullWidth",
      sx: {
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Tooltip, {
      title: "Task Division"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(GroupIcon, null),
      "aria-label": "Task Division"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Tools & Techniques"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(BuildIcon, null),
      "aria-label": "Tools & Techniques"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Daily Progress"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(TimelineIcon, null),
      "aria-label": "Daily Progress"
    }))), loading && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(CircularProgress, null)), error && renderErrorContent(), !loading && !error && insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        p: 1
      }
    }, renderInsightContent(insights)), !loading && !error && !insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(LightbulbIcon, {
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Typography, null, "No insights available"))));
  };
  _s(TeamAIInsightsPanel, "Act2AOnU28qM7wyZXfGrXz8QXTY=");
  _c2 = TeamAIInsightsPanel;
  var _c, _c2;
  $RefreshReg$(_c, "TabPanel");
  $RefreshReg$(_c2, "TeamAIInsightsPanel");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamMemberForm.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamMemberForm.jsx                                                                            //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamMemberForm: () => TeamMemberForm
  });
  let React, useState;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let Formik, Form, Field;
  module1.link("formik", {
    Formik(v) {
      Formik = v;
    },
    Form(v) {
      Form = v;
    },
    Field(v) {
      Field = v;
    }
  }, 2);
  let Yup;
  module1.link("yup", {
    "*"(v) {
      Yup = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TeamMemberSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email address').required('Email is required'),
    firstName: Yup.string().required('First name is required'),
    lastName: Yup.string().required('Last name is required'),
    role: Yup.string().required('Role is required'),
    department: Yup.string().required('Department is required'),
    skills: Yup.array().of(Yup.string()).min(1, 'At least one skill is required')
  });
  const TeamMemberForm = _ref => {
    var _member$emails, _member$emails$, _member$profile, _member$profile2, _member$profile3, _member$profile4, _member$profile5;
    let {
      member,
      onCancel
    } = _ref;
    _s();
    const [newSkill, setNewSkill] = useState('');
    const [submitError, setSubmitError] = useState('');
    const initialValues = member ? {
      email: ((_member$emails = member.emails) === null || _member$emails === void 0 ? void 0 : (_member$emails$ = _member$emails[0]) === null || _member$emails$ === void 0 ? void 0 : _member$emails$.address) || '',
      firstName: ((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '',
      lastName: ((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '',
      role: ((_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : _member$profile3.role) || '',
      department: ((_member$profile4 = member.profile) === null || _member$profile4 === void 0 ? void 0 : _member$profile4.department) || '',
      skills: ((_member$profile5 = member.profile) === null || _member$profile5 === void 0 ? void 0 : _member$profile5.skills) || []
    } : {
      email: '',
      firstName: '',
      lastName: '',
      role: '',
      department: '',
      skills: []
    };
    const handleSubmit = async values => {
      setSubmitError('');
      try {
        if (member) {
          await Meteor.call('users.updateTeamMember', member._id, values);
        } else {
          await Meteor.call('users.addTeamMember', values);
        }
        onCancel();
      } catch (err) {
        setSubmitError(err.message);
      }
    };
    return /*#__PURE__*/React.createElement("div", {
      className: "bg-white rounded-xl shadow-lg p-6 max-w-2xl mx-auto"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between items-center mb-6"
    }, /*#__PURE__*/React.createElement("h2", {
      className: "text-2xl font-bold text-gray-900"
    }, member ? 'Edit Team Member' : 'Add Team Member'), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: onCancel,
      className: "text-gray-400 hover:text-gray-500"
    }, /*#__PURE__*/React.createElement("svg", {
      className: "h-6 w-6",
      fill: "none",
      viewBox: "0 0 24 24",
      stroke: "currentColor"
    }, /*#__PURE__*/React.createElement("path", {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2,
      d: "M6 18L18 6M6 6l12 12"
    })))), submitError && /*#__PURE__*/React.createElement("div", {
      className: "mb-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-md"
    }, submitError), /*#__PURE__*/React.createElement(Formik, {
      initialValues: initialValues,
      validationSchema: TeamMemberSchema,
      onSubmit: handleSubmit
    }, _ref2 => {
      let {
        values,
        errors,
        touched,
        setFieldValue
      } = _ref2;
      return /*#__PURE__*/React.createElement(Form, {
        className: "space-y-6"
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "block text-sm font-medium text-gray-700"
      }, "Email"), /*#__PURE__*/React.createElement(Field, {
        type: "email",
        name: "email",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Enter email address"
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "grid grid-cols-1 md:grid-cols-2 gap-4"
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "firstName",
        className: "block text-sm font-medium text-gray-700"
      }, "First Name"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "firstName",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Enter first name"
      }), errors.firstName && touched.firstName && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.firstName)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "lastName",
        className: "block text-sm font-medium text-gray-700"
      }, "Last Name"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "lastName",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Enter last name"
      }), errors.lastName && touched.lastName && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.lastName))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "role",
        className: "block text-sm font-medium text-gray-700"
      }, "Role"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "role",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500"
      }, /*#__PURE__*/React.createElement("option", {
        value: ""
      }, "Select a role"), /*#__PURE__*/React.createElement("option", {
        value: "developer"
      }, "Developer"), /*#__PURE__*/React.createElement("option", {
        value: "designer"
      }, "Designer"), /*#__PURE__*/React.createElement("option", {
        value: "manager"
      }, "Manager"), /*#__PURE__*/React.createElement("option", {
        value: "analyst"
      }, "Analyst")), errors.role && touched.role && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.role)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "department",
        className: "block text-sm font-medium text-gray-700"
      }, "Department"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "department",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500"
      }, /*#__PURE__*/React.createElement("option", {
        value: ""
      }, "Select a department"), /*#__PURE__*/React.createElement("option", {
        value: "Engineering"
      }, "Engineering"), /*#__PURE__*/React.createElement("option", {
        value: "Design"
      }, "Design"), /*#__PURE__*/React.createElement("option", {
        value: "Product"
      }, "Product"), /*#__PURE__*/React.createElement("option", {
        value: "Marketing"
      }, "Marketing"), /*#__PURE__*/React.createElement("option", {
        value: "Sales"
      }, "Sales"), /*#__PURE__*/React.createElement("option", {
        value: "Support"
      }, "Support")), errors.department && touched.department && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.department)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        className: "block text-sm font-medium text-gray-700"
      }, "Skills"), /*#__PURE__*/React.createElement("div", {
        className: "flex gap-2 mt-1"
      }, /*#__PURE__*/React.createElement("input", {
        type: "text",
        value: newSkill,
        onChange: e => setNewSkill(e.target.value),
        className: "flex-1 rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Add a skill",
        onKeyPress: e => {
          if (e.key === 'Enter') {
            e.preventDefault();
            if (newSkill.trim() && !values.skills.includes(newSkill.trim())) {
              setFieldValue('skills', [...values.skills, newSkill.trim()]);
              setNewSkill('');
            }
          }
        }
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: () => {
          if (newSkill.trim() && !values.skills.includes(newSkill.trim())) {
            setFieldValue('skills', [...values.skills, newSkill.trim()]);
            setNewSkill('');
          }
        },
        className: "px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
      }, "Add")), /*#__PURE__*/React.createElement("div", {
        className: "mt-2 flex flex-wrap gap-2"
      }, values.skills.map((skill, index) => /*#__PURE__*/React.createElement("span", {
        key: index,
        className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
      }, skill, /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: () => {
          setFieldValue('skills', values.skills.filter((_, i) => i !== index));
        },
        className: "ml-1 inline-flex items-center justify-center h-4 w-4 rounded-full hover:bg-green-200"
      }, "\xD7")))), errors.skills && touched.skills && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.skills)), /*#__PURE__*/React.createElement("div", {
        className: "flex justify-end gap-3 pt-4"
      }, /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: onCancel,
        className: "px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
      }, "Cancel"), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
      }, member ? 'Save Changes' : 'Add Member')));
    }));
  };
  _s(TeamMemberForm, "hnW8fAclsRCCGBAQ6RzUgEzp6nE=");
  _c = TeamMemberForm;
  var _c;
  $RefreshReg$(_c, "TeamMemberForm");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamMemberMetrics.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamMemberMetrics.jsx                                                                         //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamMemberMetrics: () => TeamMemberMetrics
  });
  let React;
  module1.link("react", {
    default(v) {
      React = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 2);
  let Tasks;
  module1.link("/imports/api/tasks", {
    Tasks(v) {
      Tasks = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TeamMemberMetrics = _ref => {
    let {
      memberId
    } = _ref;
    _s();
    const {
      metrics,
      isLoading
    } = useTracker(() => {
      const handle = Meteor.subscribe('tasks');
      const isLoading = !handle.ready();
      if (isLoading) {
        return {
          metrics: null,
          isLoading
        };
      }
      const tasks = Tasks.find({
        assignedTo: memberId
      }).fetch();
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(task => task.status === 'completed').length;
      const inProgressTasks = tasks.filter(task => task.status === 'in-progress').length;
      const pendingTasks = tasks.filter(task => task.status === 'pending').length;
      const highPriorityTasks = tasks.filter(task => task.priority === 'high').length;
      const mediumPriorityTasks = tasks.filter(task => task.priority === 'medium').length;
      const lowPriorityTasks = tasks.filter(task => task.priority === 'low').length;
      const averageProgress = tasks.length > 0 ? tasks.reduce((sum, task) => sum + task.progress, 0) / tasks.length : 0;
      const onTimeTasks = tasks.filter(task => {
        if (task.status !== 'completed') return false;
        const completedDate = task.updatedAt || task.createdAt;
        return new Date(completedDate) <= new Date(task.dueDate);
      }).length;
      const completionRate = totalTasks > 0 ? completedTasks / totalTasks * 100 : 0;
      const onTimeRate = completedTasks > 0 ? onTimeTasks / completedTasks * 100 : 0;
      return {
        metrics: {
          totalTasks,
          completedTasks,
          inProgressTasks,
          pendingTasks,
          highPriorityTasks,
          mediumPriorityTasks,
          lowPriorityTasks,
          averageProgress,
          completionRate,
          onTimeRate
        },
        isLoading
      };
    }, [memberId]);
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        className: "animate-pulse"
      }, /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-3/4 mb-4"
      }), /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-1/2"
      }));
    }
    return /*#__PURE__*/React.createElement("div", {
      className: "space-y-6"
    }, /*#__PURE__*/React.createElement("div", {
      className: "grid grid-cols-1 md:grid-cols-3 gap-4"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-white p-4 rounded-lg shadow"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-semibold text-gray-900 mb-4"
    }, "Task Status"), /*#__PURE__*/React.createElement("div", {
      className: "space-y-3"
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Completed"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.completedTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-green-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.completedTasks / metrics.totalTasks * 100, "%")
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "In Progress"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.inProgressTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-blue-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.inProgressTasks / metrics.totalTasks * 100, "%")
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Pending"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.pendingTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-yellow-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.pendingTasks / metrics.totalTasks * 100, "%")
      }
    }))))), /*#__PURE__*/React.createElement("div", {
      className: "bg-white p-4 rounded-lg shadow"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-semibold text-gray-900 mb-4"
    }, "Task Priority"), /*#__PURE__*/React.createElement("div", {
      className: "space-y-3"
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "High Priority"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.highPriorityTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-red-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.highPriorityTasks / metrics.totalTasks * 100, "%")
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Medium Priority"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.mediumPriorityTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-yellow-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.mediumPriorityTasks / metrics.totalTasks * 100, "%")
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Low Priority"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.lowPriorityTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-green-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.lowPriorityTasks / metrics.totalTasks * 100, "%")
      }
    }))))), /*#__PURE__*/React.createElement("div", {
      className: "bg-white p-4 rounded-lg shadow"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-semibold text-gray-900 mb-4"
    }, "Performance"), /*#__PURE__*/React.createElement("div", {
      className: "space-y-4"
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Completion Rate"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.completionRate.toFixed(1), "%")), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-green-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.completionRate, "%")
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "On-Time Rate"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.onTimeRate.toFixed(1), "%")), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-blue-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.onTimeRate, "%")
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Average Progress"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.averageProgress.toFixed(1), "%")), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-purple-500 h-2 rounded-full",
      style: {
        width: "".concat(metrics.averageProgress, "%")
      }
    })))))));
  };
  _s(TeamMemberMetrics, "KKxl2vplfctxeDyZFWbPRqkRWww=", false, function () {
    return [useTracker];
  });
  _c = TeamMemberMetrics;
  var _c;
  $RefreshReg$(_c, "TeamMemberMetrics");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamTaskView.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamTaskView.jsx                                                                              //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamTaskView: () => TeamTaskView
  });
  let React, useState, useEffect;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    }
  }, 0);
  let useNavigate;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    }
  }, 1);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 2);
  let TeamAIInsightsPanel;
  module1.link("./TeamAIInsightsPanel", {
    TeamAIInsightsPanel(v) {
      TeamAIInsightsPanel = v;
    }
  }, 3);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TeamTaskView = _ref => {
    let {
      task,
      onCancel
    } = _ref;
    _s();
    // State for managing UI interactions and data
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [newLink, setNewLink] = useState('');
    const [file, setFile] = useState(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isUploading, setIsUploading] = useState(false);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [userNames, setUserNames] = useState({});
    const [isLoadingUsers, setIsLoadingUsers] = useState(true);

    // Hook for navigation
    const navigate = useNavigate();

    // Subscribe to tasks and user data
    const {
      tasks
    } = useTracker(() => {
      const tasksHandle = Meteor.subscribe('tasks');
      const userDataHandle = Meteor.subscribe('userData');
      const taskUsersHandle = Meteor.subscribe('taskUsers');
      return {
        tasks: tasksHandle.ready() && userDataHandle.ready() && taskUsersHandle.ready()
      };
    });

    // Load user data when task changes
    useEffect(() => {
      const loadUserData = async () => {
        if (!task) return;
        setIsLoadingUsers(true);
        try {
          // Collect all user IDs from the task
          const userIds = new Set();

          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }

          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }

          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
          console.log('Loading user data for IDs:', Array.from(userIds));

          // Load user data for all collected IDs
          const userDataPromises = Array.from(userIds).map(async userId => {
            var _user$profile, _user$profile2;
            const user = await Meteor.users.findOneAsync(userId);
            if (!user) return [userId, 'Unknown User'];
            const firstName = ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.firstName) || '';
            const lastName = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.lastName) || '';
            const fullName = "".concat(firstName, " ").concat(lastName).trim();
            return [userId, fullName || 'Unknown User'];
          });
          const userEntries = await Promise.all(userDataPromises);
          const names = Object.fromEntries(userEntries);
          console.log('Loaded user names:', names);
          setUserNames(names);
        } catch (error) {
          console.error('Error loading user data:', error);
        } finally {
          setIsLoadingUsers(false);
        }
      };
      loadUserData();
    }, [task]);

    // Handles updating the task's overall progress based on checklist completion
    const handleSubmit = async () => {
      setIsSubmitting(true);
      setError(''); // Clear previous errors
      setSuccessMessage(''); // Clear previous success messages
      try {
        var _task$checklist, _task$checklist2;
        // Calculate progress based on completed checklist items
        const completedItems = ((_task$checklist = task.checklist) === null || _task$checklist === void 0 ? void 0 : _task$checklist.filter(item => item.completed).length) || 0;
        const totalItems = ((_task$checklist2 = task.checklist) === null || _task$checklist2 === void 0 ? void 0 : _task$checklist2.length) || 0;
        const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

        // Call Meteor method to update task progress
        await Meteor.call('tasks.updateProgress', task._id, progress);
        setIsSubmitting(false);
        setSuccessMessage('Task progress updated successfully!');
        setTimeout(() => navigate('/team-dashboard/tasks'), 1500); // Navigate after a brief success message
      } catch (error) {
        console.error('Error submitting task:', error);
        setError(error.reason || 'Failed to update task progress.');
        setIsSubmitting(false);
      }
    };

    // Handles file selection for attachment upload
    const handleFileChange = e => {
      const selectedFile = e.target.files[0];
      if (selectedFile) {
        // Check file size (limit to 5MB)
        if (selectedFile.size > 5 * 1024 * 1024) {
          setError('File size exceeds 5MB limit.');
          setFile(null); // Clear selected file
          return;
        }
        setFile(selectedFile);
        setError(''); // Clear any previous errors
        setSuccessMessage(''); // Clear any previous success messages
      }
    };

    // Handles uploading a selected file as an attachment
    const handleFileUpload = () => {
      if (!file) {
        setError('Please select a file to upload.');
        return;
      }
      setIsUploading(true);
      setUploadProgress(0);
      setError(''); // Clear previous errors
      setSuccessMessage(''); // Clear previous success messages

      const reader = new FileReader();

      // Update upload progress
      reader.onprogress = event => {
        if (event.lengthComputable) {
          const progress = Math.round(event.loaded / event.total * 100);
          setUploadProgress(progress);
        }
      };

      // When file reading is complete, call Meteor method to add attachment
      reader.onload = event => {
        const fileData = {
          name: file.name,
          type: file.type,
          data: event.target.result // Base64 encoded file data
        };
        Meteor.call('tasks.addAttachment', task._id, fileData, (error, result) => {
          setIsUploading(false);
          if (error) {
            console.error('Error uploading file:', error);
            setError(error.reason || 'Failed to upload file.');
          } else {
            setFile(null); // Clear selected file
            setSuccessMessage('File uploaded successfully!');
            setTimeout(() => setSuccessMessage(''), 3000); // Clear message after 3 seconds
            // Reset the file input visually
            const fileInput = document.getElementById('file-upload');
            if (fileInput) fileInput.value = '';
          }
        });
      };

      // Handle file reading errors
      reader.onerror = () => {
        setIsUploading(false);
        setError('Error reading file.');
      };
      reader.readAsDataURL(file); // Read file as Data URL (base64)
    };

    // Handles adding a new link to the task
    const handleAddLink = () => {
      if (!newLink) {
        setError('Please enter a link.');
        return;
      }

      // Basic URL validation
      try {
        new URL(newLink); // Throws an error for invalid URLs
      } catch (e) {
        setError('Please enter a valid URL (e.g., include http:// or https://).');
        return;
      }
      Meteor.call('tasks.addLink', task._id, newLink, (error, result) => {
        if (error) {
          console.error('Error adding link:', error);
          setError(error.reason || 'Failed to add link.');
        } else {
          setNewLink(''); // Clear input
          setSuccessMessage('Link added successfully!');
          setTimeout(() => setSuccessMessage(''), 3000); // Clear message after 3 seconds
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'row',
        gap: '32px',
        padding: '24px',
        background: '#f8fafc',
        // Light grey background
        minHeight: 'calc(100vh - 64px)',
        // Adjust for header if present
        fontFamily: 'Inter, sans-serif' // Modern sans-serif font
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 2
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
        border: '1px solid #e2e8f0' // Light border
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '24px',
        flexWrap: 'wrap',
        gap: '16px'
      }
    }, /*#__PURE__*/React.createElement("button", {
      onClick: onCancel,
      style: {
        background: 'none',
        border: 'none',
        cursor: 'pointer',
        fontSize: '1.8rem',
        color: '#64748b',
        transition: 'color 0.2s ease',
        marginRight: '12px',
        padding: 0,
        display: 'flex',
        alignItems: 'center'
      },
      onMouseOver: e => e.currentTarget.style.color = '#334155',
      onMouseOut: e => e.currentTarget.style.color = '#64748b',
      "aria-label": "Back"
    }, "\u2190"), /*#__PURE__*/React.createElement("h2", {
      style: {
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#0f172a',
        margin: '0',
        wordBreak: 'break-word',
        display: 'inline-block'
      }
    }, task.title)), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#334155',
        marginBottom: '8px',
        fontSize: '1.2rem',
        fontWeight: '600'
      }
    }, "Description"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        whiteSpace: 'pre-line',
        lineHeight: '1.6'
      }
    }, task.description || 'No description provided.')), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '32px',
        marginTop: '32px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Status"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in_progress' ? '#fef9c3' : '#f1f5f9',
        color: task.status === 'completed' ? '#166534' : task.status === 'in_progress' ? '#92400e' : '#475569'
      }
    }, task.status === 'not_started' ? 'Not Started' : task.status === 'in_progress' ? 'In Progress' : task.status === 'blocked' ? 'Blocked' : task.status === 'completed' ? 'Completed' : task.status)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Priority"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef9c3' : '#dcfce7',
        color: task.priority === 'high' ? '#991b1b' : task.priority === 'medium' ? '#92400e' : '#166534'
      }
    }, task.priority === 'high' ? 'High' : task.priority === 'medium' ? 'Medium' : 'Low')), task.dueDate && /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Due Date"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: '#e0e7ef',
        color: '#2563eb'
      }
    }, new Date(task.dueDate).toLocaleDateString())), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Progress"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: '#e0e7ef',
        color: '#2563eb'
      }
    }, task.progress || 0, "%")), task.assignedTo && task.assignedTo.length > 0 && /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Team Members"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '8px',
        flexWrap: 'wrap'
      }
    }, task.assignedTo.map(userId => /*#__PURE__*/React.createElement("span", {
      key: userId,
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        backgroundColor: '#f1f5f9',
        color: '#475569',
        fontWeight: '500'
      }
    }, userNames[userId] || 'Unknown User'))))), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#334155',
        marginBottom: '16px',
        fontSize: '1.2rem',
        fontWeight: '600'
      }
    }, "Checklist"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, task.checklist.map((item, index) => /*#__PURE__*/React.createElement("div", {
      key: index,
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        backgroundColor: '#f8fafc',
        padding: '12px',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "checkbox",
      checked: item.completed,
      onChange: () => {
        Meteor.call('tasks.toggleChecklistItem', task._id, index, error => {
          if (error) {
            console.error('Error toggling checklist item:', error);
            setError(error.reason || 'Failed to update checklist item.');
          } else {
            setSuccessMessage('Checklist item updated!');
            setTimeout(() => setSuccessMessage(''), 3000);
          }
        });
      },
      style: {
        width: '20px',
        height: '20px',
        accentColor: '#3b82f6',
        cursor: 'pointer'
      }
    }), /*#__PURE__*/React.createElement("span", {
      style: {
        color: item.completed ? '#64748b' : '#0f172a',
        textDecoration: item.completed ? 'line-through' : 'none',
        fontSize: '1rem',
        flex: 1
      }
    }, item.text))))), /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: '24px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.2rem',
        fontWeight: '600',
        color: '#0f172a',
        marginBottom: '16px'
      }
    }, "Attachments"), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '16px',
        padding: '16px',
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0',
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      id: "file-upload",
      type: "file",
      onChange: handleFileChange,
      style: {
        fontSize: '0.9rem',
        color: '#475569'
      }
    }), /*#__PURE__*/React.createElement("button", {
      onClick: handleFileUpload,
      disabled: !file || isUploading,
      style: {
        padding: '10px 20px',
        backgroundColor: '#3b82f6',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: file && !isUploading ? 'pointer' : 'not-allowed',
        opacity: file && !isUploading ? 1 : 0.7,
        fontSize: '1rem',
        fontWeight: 600,
        transition: 'background-color 0.2s ease'
      },
      onMouseOver: e => {
        if (file && !isUploading) e.currentTarget.style.backgroundColor = '#2563eb';
      },
      onMouseOut: e => {
        if (file && !isUploading) e.currentTarget.style.backgroundColor = '#3b82f6';
      }
    }, isUploading ? "Uploading... ".concat(uploadProgress, "%") : 'Upload File'), isUploading && /*#__PURE__*/React.createElement("div", {
      style: {
        height: '8px',
        backgroundColor: '#e2e8f0',
        borderRadius: '4px',
        overflow: 'hidden',
        marginTop: '8px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: "".concat(uploadProgress, "%"),
        height: '100%',
        backgroundColor: '#3b82f6',
        transition: 'width 0.1s linear'
      }
    }))), task.attachments && task.attachments.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, task.attachments.map((attachment, index) => {
      // Ensure we're working with string IDs
      const currentUserId = String(Meteor.userId());
      const uploadedById = String(attachment.uploadedBy);
      const isOwner = currentUserId === uploadedById;
      console.log('Rendering attachment:', {
        attachment,
        uploadedById,
        userName: userNames[uploadedById],
        allUserNames: userNames
      });
      return /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          wordBreak: 'break-word' // Ensure long names wrap
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          minWidth: 0
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1.5rem',
          color: '#64748b'
        }
      }, "\uD83D\uDCCE"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.9rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, attachment.name), /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.75rem',
          color: '#64748b'
        }
      }, "Uploaded by ", userNames[uploadedById] || 'Unknown User', " on ", new Date(attachment.uploadedAt).toLocaleDateString()))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px',
          flexShrink: 0
        }
      }, /*#__PURE__*/React.createElement("button", {
        onClick: () => {
          try {
            let base64Data = attachment.data;
            if (base64Data.startsWith('data:')) {
              base64Data = base64Data.split(',')[1];
            }
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const mimeType = attachment.type || 'application/octet-stream';
            const blob = new Blob([byteArray], {
              type: mimeType
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = attachment.name;
            document.body.appendChild(link);
            link.click();
            setTimeout(() => {
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
            }, 100);
          } catch (downloadError) {
            console.error('Error downloading file:', downloadError);
            setError('Failed to download file. Please try again.');
          }
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#e2e8f0',
          border: 'none',
          borderRadius: '6px',
          color: '#0f172a',
          fontSize: '0.875rem',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease'
        },
        onMouseOver: e => e.currentTarget.style.backgroundColor = '#cbd5e1',
        onMouseOut: e => e.currentTarget.style.backgroundColor = '#e2e8f0'
      }, "Download"), isOwner && /*#__PURE__*/React.createElement("button", {
        onClick: () => {
          if (window.confirm('Are you sure you want to remove this attachment?')) {
            Meteor.call('tasks.removeAttachment', task._id, index, error => {
              if (error) {
                console.error('Error removing attachment:', error);
                setError(error.reason || 'Failed to remove attachment.');
              } else {
                setSuccessMessage('Attachment removed successfully!');
                setTimeout(() => setSuccessMessage(''), 3000);
              }
            });
          }
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#ef4444',
          border: 'none',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease'
        },
        onMouseOver: e => e.currentTarget.style.backgroundColor = '#dc2626',
        onMouseOut: e => e.currentTarget.style.backgroundColor = '#ef4444'
      }, "Remove")));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#64748b',
        fontSize: '0.9rem'
      }
    }, "No attachments yet. Upload files to share them!")), /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: '32px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.2rem',
        fontWeight: '600',
        color: '#0f172a',
        marginBottom: '16px'
      }
    }, "Links"), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '16px',
        padding: '16px',
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0',
        display: 'flex',
        gap: '12px',
        alignItems: 'center'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: newLink,
      onChange: e => setNewLink(e.target.value),
      placeholder: "Enter URL (https://example.com)",
      style: {
        flex: 1,
        padding: '10px 12px',
        border: '1px solid #cbd5e1',
        borderRadius: '6px',
        fontSize: '0.9rem',
        color: '#334155',
        boxSizing: 'border-box'
      }
    }), /*#__PURE__*/React.createElement("button", {
      onClick: handleAddLink,
      style: {
        padding: '10px 20px',
        backgroundColor: '#3b82f6',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        fontSize: '1rem',
        fontWeight: 600,
        transition: 'background-color 0.2s ease'
      },
      onMouseOver: e => e.currentTarget.style.backgroundColor = '#2563eb',
      onMouseOut: e => e.currentTarget.style.backgroundColor = '#3b82f6'
    }, "Add Link")), task.links && task.links.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, task.links.map((link, index) => {
      // Ensure we're working with string IDs
      const currentUserId = String(Meteor.userId());
      const addedById = String(link.addedBy);
      const isOwner = currentUserId === addedById;
      return /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          wordBreak: 'break-word' // Ensure long URLs wrap
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          minWidth: 0
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1.5rem',
          color: '#64748b'
        }
      }, "\uD83D\uDD17"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("a", {
        href: link.url,
        target: "_blank",
        rel: "noopener noreferrer",
        style: {
          fontSize: '0.9rem',
          color: '#2563eb',
          textDecoration: 'none',
          fontWeight: '500'
        },
        onMouseOver: e => e.currentTarget.style.textDecoration = 'underline',
        onMouseOut: e => e.currentTarget.style.textDecoration = 'none'
      }, link.url), /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.75rem',
          color: '#64748b'
        }
      }, "Added by ", userNames[addedById] || 'Unknown User', " on ", new Date(link.addedAt).toLocaleDateString()))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px',
          flexShrink: 0
        }
      }, isOwner && /*#__PURE__*/React.createElement("button", {
        onClick: () => {
          if (window.confirm('Are you sure you want to remove this link?')) {
            Meteor.call('tasks.removeLink', task._id, index, error => {
              if (error) {
                console.error('Error removing link:', error);
                setError(error.reason || 'Failed to remove link.');
              } else {
                setSuccessMessage('Link removed successfully!');
                setTimeout(() => setSuccessMessage(''), 3000);
              }
            });
          }
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#ef4444',
          border: 'none',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease'
        },
        onMouseOver: e => e.currentTarget.style.backgroundColor = '#dc2626',
        onMouseOut: e => e.currentTarget.style.backgroundColor = '#ef4444'
      }, "Remove")));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#64748b',
        fontSize: '0.9rem'
      }
    }, "No links yet. Add relevant URLs to this task!")), error && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#fee2e2',
        color: '#b91c1c',
        padding: '12px',
        borderRadius: '8px',
        marginTop: '24px',
        fontSize: '0.9rem',
        fontWeight: 500,
        border: '1px solid #fca5a5'
      }
    }, error), successMessage && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#dcfce7',
        color: '#166534',
        padding: '12px',
        borderRadius: '8px',
        marginTop: '24px',
        fontSize: '0.9rem',
        fontWeight: 500,
        border: '1px solid #86efac'
      }
    }, successMessage), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginTop: '32px',
        paddingTop: '24px',
        borderTop: '1px solid #e2e8f0'
      }
    }, /*#__PURE__*/React.createElement("button", {
      onClick: handleSubmit,
      disabled: isSubmitting,
      style: {
        backgroundColor: '#3b82f6',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        padding: '12px 24px',
        fontWeight: 600,
        fontSize: '1rem',
        cursor: 'pointer',
        opacity: isSubmitting ? 0.7 : 1,
        transition: 'background-color 0.2s ease'
      },
      onMouseOver: e => {
        if (!isSubmitting) e.currentTarget.style.backgroundColor = '#2563eb';
      },
      onMouseOut: e => {
        if (!isSubmitting) e.currentTarget.style.backgroundColor = '#3b82f6';
      }
    }, isSubmitting ? 'Updating Progress...' : 'Update Task Progress')))), /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: 350,
        maxWidth: 500
      }
    }, /*#__PURE__*/React.createElement(TeamAIInsightsPanel, {
      taskId: task._id,
      task: task
    })));
  };
  _s(TeamTaskView, "bVLBOqMpsgKQiYtiUBqcnu40q/Y=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = TeamTaskView;
  var _c;
  $RefreshReg$(_c, "TeamTaskView");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"pages":{"AdminDashboard.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/AdminDashboard.jsx                                                                                 //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  let _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default(v) {
      _objectSpread = v;
    }
  }, 0);
  module1.export({
    AdminDashboard: () => AdminDashboard
  });
  let React, useState, useEffect, useMemo;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    },
    useMemo(v) {
      useMemo = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useNavigate, Link, Routes, Route, useParams, useLocation;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    },
    Link(v) {
      Link = v;
    },
    Routes(v) {
      Routes = v;
    },
    Route(v) {
      Route = v;
    },
    useParams(v) {
      useParams = v;
    },
    useLocation(v) {
      useLocation = v;
    }
  }, 2);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 3);
  let Tasks, taskCategories, taskLabels;
  module1.link("/imports/api/tasks", {
    Tasks(v) {
      Tasks = v;
    },
    taskCategories(v) {
      taskCategories = v;
    },
    taskLabels(v) {
      taskLabels = v;
    }
  }, 4);
  let CreateTask;
  module1.link("../components/CreateTask", {
    CreateTask(v) {
      CreateTask = v;
    }
  }, 5);
  let EditTask;
  module1.link("../components/EditTask", {
    EditTask(v) {
      EditTask = v;
    }
  }, 6);
  let TeamMemberForm;
  module1.link("../components/TeamMemberForm", {
    TeamMemberForm(v) {
      TeamMemberForm = v;
    }
  }, 7);
  let TeamMemberMetrics;
  module1.link("../components/TeamMemberMetrics", {
    TeamMemberMetrics(v) {
      TeamMemberMetrics = v;
    }
  }, 8);
  let TaskAssignmentHistory;
  module1.link("../components/TaskAssignmentHistory", {
    TaskAssignmentHistory(v) {
      TaskAssignmentHistory = v;
    }
  }, 9);
  let ReportsPage;
  module1.link("./ReportsPage", {
    ReportsPage(v) {
      ReportsPage = v;
    }
  }, 10);
  let Formik, Form, Field;
  module1.link("formik", {
    Formik(v) {
      Formik = v;
    },
    Form(v) {
      Form = v;
    },
    Field(v) {
      Field = v;
    }
  }, 11);
  let Yup;
  module1.link("yup", {
    "*"(v) {
      Yup = v;
    }
  }, 12);
  let Header;
  module1.link("../components/Header", {
    Header(v) {
      Header = v;
    }
  }, 13);
  let AIInsightsPanel;
  module1.link("../components/AIInsightsPanel", {
    AIInsightsPanel(v) {
      AIInsightsPanel = v;
    }
  }, 14);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$(),
    _s2 = $RefreshSig$(),
    _s3 = $RefreshSig$(),
    _s4 = $RefreshSig$();
  const AdminDashboardHome = () => {
    var _user$profile;
    _s();
    const {
      user
    } = useTracker(() => {
      const userHandle = Meteor.subscribe('userData');
      const user = Meteor.user();
      return {
        user
      };
    });
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: '0 0 24px 0',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Welcome, ", (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.firstName) || 'Admin', "!"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px'
      }
    }, "\uD83D\uDCCB")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "Task Management"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "Create and manage tasks"), /*#__PURE__*/React.createElement(Link, {
      to: "/admin-dashboard/tasks",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "Manage Tasks")), /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px',
        color: '#16a34a'
      }
    }, "\uD83D\uDC65")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "Team Members"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View and manage team members"), /*#__PURE__*/React.createElement(Link, {
      to: "/admin-dashboard/team",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "Manage Team")), /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px',
        color: '#16a34a'
      }
    }, "\uD83D\uDCCA")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "Reports"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View task analytics and reports"), /*#__PURE__*/React.createElement(Link, {
      to: "/admin-dashboard/reports",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "View Reports")))));
  };
  _s(AdminDashboardHome, "8VRLU9EIGLI5z3TCuSbfcW/qD8k=", false, function () {
    return [useTracker];
  });
  _c = AdminDashboardHome;
  const TasksPage = () => {
    _s2();
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [editingTask, setEditingTask] = useState(null);
    const [viewingTask, setViewingTask] = useState(null);
    const [successMessage, setSuccessMessage] = useState('');
    const [userEmails, setUserEmails] = useState({});
    const [userNames, setUserNames] = useState({});
    const [isLoadingUsers, setIsLoadingUsers] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [filterStatus, setFilterStatus] = useState('all');
    const [filterPriority, setFilterPriority] = useState('all');
    const [sortBy, setSortBy] = useState('createdAt');
    const [sortOrder, setSortOrder] = useState('desc');
    const {
      taskId
    } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const {
      tasks,
      isLoading,
      user,
      tasksReady,
      userReady,
      teamMembers
    } = useTracker(() => {
      const tasksHandle = Meteor.subscribe('tasks');
      const userHandle = Meteor.subscribe('userData');
      const teamMembersHandle = Meteor.subscribe('teamMembers');
      const tasksReady = tasksHandle.ready();
      const userReady = userHandle.ready();
      const teamMembersReady = teamMembersHandle.ready();
      const user = Meteor.user();
      const tasks = Tasks.find({}, {
        sort: {
          createdAt: -1
        }
      }).fetch();
      const teamMembers = Meteor.users.find({
        $or: [{
          'roles': 'team-member'
        }, {
          'profile.role': 'team-member'
        }]
      }, {
        fields: {
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      }).fetch();
      const isLoading = !tasksReady || !userReady || !teamMembersReady;
      return {
        tasks,
        user,
        tasksReady,
        userReady,
        teamMembersReady,
        isLoading,
        teamMembers
      };
    }, []);
    useEffect(() => {
      if (taskId) {
        const task = tasks.find(t => t._id === taskId);
        if (task) {
          setViewingTask(task);
        } else {
          var _location$state;
          if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.from) {
            if (location.state.memberId) {
              const member = teamMembers.find(m => m._id === location.state.memberId);
              if (member) {
                setSelectedMember(member);
              } else {
                navigate(location.state.from);
              }
            } else {
              navigate(location.state.from);
            }
          } else {
            navigate('/admin-dashboard/tasks');
          }
        }
      } else {
        setViewingTask(null);
      }
    }, [taskId, tasks, location.state, teamMembers, navigate]);
    useEffect(() => {
      const loadUserData = async () => {
        if (!tasks) return;
        setIsLoadingUsers(true);
        try {
          const userIds = [...new Set(tasks.flatMap(task => task.assignedTo))];
          const userDataPromises = userIds.map(async userId => {
            var _user$profile2, _user$profile3, _user$emails, _user$emails$;
            const user = await Meteor.users.findOneAsync(userId);
            if (!user) return [userId, {
              name: 'Unknown User',
              email: 'Unknown'
            }];
            const firstName = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.firstName) || '';
            const lastName = ((_user$profile3 = user.profile) === null || _user$profile3 === void 0 ? void 0 : _user$profile3.lastName) || '';
            const fullName = "".concat(firstName, " ").concat(lastName).trim();
            return [userId, {
              email: ((_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address) || 'No email',
              name: fullName || 'Unknown User'
            }];
          });
          const userEntries = await Promise.all(userDataPromises);
          const userData = Object.fromEntries(userEntries);
          setUserEmails(Object.fromEntries(userEntries.map(_ref => {
            let [id, data] = _ref;
            return [id, data.email];
          })));
          setUserNames(Object.fromEntries(userEntries.map(_ref2 => {
            let [id, data] = _ref2;
            return [id, data.name];
          })));
        } catch (error) {
          console.error('Error loading user data:', error);
        } finally {
          setIsLoadingUsers(false);
        }
      };
      loadUserData();
    }, [tasks]);
    const handleCreateTask = () => {
      setShowCreateForm(true);
      setEditingTask(null);
      setSuccessMessage('');
    };
    const handleEditTask = task => {
      setEditingTask(task);
      setViewingTask(null);
      setShowCreateForm(false);
    };
    const handleCancel = () => {
      setShowCreateForm(false);
      setEditingTask(null);
      setViewingTask(null);
      setSuccessMessage('Task saved successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    };
    const handleTaskUpdate = async (taskId, updatedTask) => {
      try {
        console.log('Received task update:', {
          taskId,
          updatedTask
        });

        // Ensure all required fields are present
        const taskToUpdate = _objectSpread(_objectSpread({}, updatedTask), {}, {
          title: updatedTask.title.trim(),
          description: updatedTask.description.trim(),
          assignedTo: updatedTask.assignedTo || [],
          checklist: updatedTask.checklist || [],
          labels: updatedTask.labels || [],
          category: updatedTask.category || '',
          progress: updatedTask.progress || 0,
          startDate: new Date(updatedTask.startDate),
          dueDate: new Date(updatedTask.dueDate)
        });
        console.log('Sending task update to server:', taskToUpdate);

        // Call the server method
        const result = await new Promise((resolve, reject) => {
          Meteor.call('tasks.update', taskId, taskToUpdate, (error, result) => {
            if (error) {
              console.error('Server error updating task:', error);
              setSuccessMessage('Error updating task: ' + error.message);
              reject(error);
            } else {
              console.log('Task update successful:', result);
              setSuccessMessage('Task updated successfully!');
              resolve(result);
            }
          });
        });

        // Force a refresh of the task data
        const updatedTask = Tasks.findOne(taskId);
        if (updatedTask) {
          if ((viewingTask === null || viewingTask === void 0 ? void 0 : viewingTask._id) === taskId) {
            setViewingTask(updatedTask);
          }
          if ((editingTask === null || editingTask === void 0 ? void 0 : editingTask._id) === taskId) {
            setEditingTask(updatedTask);
          }
        }
        setEditingTask(null);
        setViewingTask(null);
      } catch (error) {
        console.error('Error updating task:', error);
        setSuccessMessage('Error updating task: ' + error.message);
      } finally {
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    };
    const handleDeleteTask = async taskId => {
      const task = tasks.find(t => t._id === taskId);
      if (!task) return;
      const confirmMessage = "Are you sure you want to delete the task \"".concat(task.title, "\"?\n\nThis action cannot be undone.");
      if (window.confirm(confirmMessage)) {
        try {
          setSuccessMessage('Deleting task...');
          await new Promise((resolve, reject) => {
            Meteor.call('tasks.delete', taskId, (error, result) => {
              if (error) {
                console.error('Error deleting task:', error);
                reject(error);
              } else {
                console.log('Task deleted successfully:', result);
                resolve(result);
              }
            });
          });
          setSuccessMessage('Task deleted successfully!');
          // Clear any active views
          setViewingTask(null);
          setEditingTask(null);
        } catch (error) {
          console.error('Error deleting task:', error);
          setSuccessMessage("Error deleting task: ".concat(error.message));
        } finally {
          setTimeout(() => setSuccessMessage(''), 3000);
        }
      }
    };
    const filteredAndSortedTasks = useMemo(() => {
      if (!tasks) return [];
      let filtered = [...tasks];

      // Apply search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(task => task.title.toLowerCase().includes(query) || task.description.toLowerCase().includes(query));
      }

      // Apply status filter
      if (filterStatus !== 'all') {
        filtered = filtered.filter(task => task.status === filterStatus);
      }

      // Apply priority filter
      if (filterPriority !== 'all') {
        filtered = filtered.filter(task => task.priority === filterPriority);
      }

      // Apply sorting
      filtered.sort((a, b) => {
        let comparison = 0;
        switch (sortBy) {
          case 'title':
            comparison = a.title.localeCompare(b.title);
            break;
          case 'dueDate':
            comparison = new Date(a.dueDate) - new Date(b.dueDate);
            break;
          case 'priority':
            const priorityOrder = {
              high: 3,
              medium: 2,
              low: 1
            };
            comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
            break;
          case 'status':
            comparison = a.status.localeCompare(b.status);
            break;
          default:
            comparison = new Date(b.createdAt) - new Date(a.createdAt);
        }
        return sortOrder === 'asc' ? comparison : -comparison;
      });
      return filtered;
    }, [tasks, searchQuery, filterStatus, filterPriority, sortBy, sortOrder]);

    // Subscribe to specific task updates when viewing or editing
    useEffect(() => {
      let handle;
      if (viewingTask) {
        handle = Meteor.subscribe('task', viewingTask._id);
      } else if (editingTask) {
        handle = Meteor.subscribe('task', editingTask._id);
      }
      return () => {
        var _handle;
        return (_handle = handle) === null || _handle === void 0 ? void 0 : _handle.stop();
      };
    }, [viewingTask === null || viewingTask === void 0 ? void 0 : viewingTask._id, editingTask === null || editingTask === void 0 ? void 0 : editingTask._id]);

    // Update viewingTask when the task data changes
    useEffect(() => {
      if (viewingTask) {
        const updatedTask = Tasks.findOne(viewingTask._id);
        if (updatedTask && JSON.stringify(updatedTask) !== JSON.stringify(viewingTask)) {
          console.log('Updating viewing task:', {
            old: viewingTask,
            new: updatedTask
          });
          setViewingTask(updatedTask);
        }
      }
    }, [viewingTask === null || viewingTask === void 0 ? void 0 : viewingTask._id]);

    // Update editingTask when the task data changes
    useEffect(() => {
      if (editingTask) {
        const updatedTask = Tasks.findOne(editingTask._id);
        if (updatedTask && JSON.stringify(updatedTask) !== JSON.stringify(editingTask)) {
          console.log('Updating editing task:', {
            old: editingTask,
            new: updatedTask
          });
          setEditingTask(updatedTask);
        }
      }
    }, [editingTask === null || editingTask === void 0 ? void 0 : editingTask._id]);
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }
    if (viewingTask) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'row',
          gap: '32px',
          padding: '24px',
          background: '#ffffff',
          minHeight: 'calc(100vh - 64px)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          flex: 2
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        onClick: () => {
          var _location$state2;
          if ((_location$state2 = location.state) !== null && _location$state2 !== void 0 && _location$state2.from) {
            if (location.state.memberId) {
              navigate("/admin-dashboard/team/".concat(location.state.memberId));
            } else {
              navigate(location.state.from);
            }
          } else {
            navigate('/admin-dashboard/tasks');
          }
        },
        style: {
          background: 'none',
          border: 'none',
          cursor: 'pointer',
          fontSize: '1.8rem',
          color: '#64748b',
          transition: 'color 0.2s ease',
          marginRight: '12px',
          padding: 0,
          display: 'flex',
          alignItems: 'center'
        },
        onMouseOver: e => e.currentTarget.style.color = '#334155',
        onMouseOut: e => e.currentTarget.style.color = '#64748b',
        "aria-label": "Back"
      }, "\u2190"), /*#__PURE__*/React.createElement("h2", {
        style: {
          fontSize: '1.5rem',
          fontWeight: '600',
          color: '#0f172a',
          margin: '0',
          wordBreak: 'break-word',
          display: 'inline-block'
        }
      }, viewingTask.title)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexWrap: 'wrap',
          gap: '12px',
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: viewingTask.status === 'completed' ? '#dcfce7' : viewingTask.status === 'in-progress' ? '#fef3c7' : '#fee2e2',
          color: viewingTask.status === 'completed' ? '#16a34a' : viewingTask.status === 'in-progress' ? '#d97706' : '#dc2626'
        }
      }, viewingTask.status === 'in-progress' ? 'In Progress' : viewingTask.status.charAt(0).toUpperCase() + viewingTask.status.slice(1)), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: viewingTask.priority === 'high' ? '#fee2e2' : viewingTask.priority === 'medium' ? '#fef3c7' : '#dcfce7',
          color: viewingTask.priority === 'high' ? '#dc2626' : viewingTask.priority === 'medium' ? '#d97706' : '#16a34a'
        }
      }, viewingTask.priority.charAt(0).toUpperCase() + viewingTask.priority.slice(1)), viewingTask.dueDate && /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCC5"), "Due: ", new Date(viewingTask.dueDate).toLocaleDateString()), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCCA"), "Progress: ", viewingTask.progress || 0, "%"), viewingTask.assignedTo && viewingTask.assignedTo.length > 0 && /*#__PURE__*/React.createElement("span", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          fontWeight: '500'
        }
      }, "Team:"), viewingTask.assignedTo.map(userId => /*#__PURE__*/React.createElement("span", {
        key: userId,
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          fontWeight: '500'
        }
      }, userNames[userId] || 'Unknown User')))), /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '8px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Description"), /*#__PURE__*/React.createElement("p", {
        style: {
          color: '#475569',
          whiteSpace: 'pre-line',
          lineHeight: '1.6'
        }
      }, viewingTask.description || 'No description provided.')), viewingTask.checklist && viewingTask.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '16px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Checklist"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, viewingTask.checklist.map((item, index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          backgroundColor: '#f8fafc',
          padding: '12px',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          color: item.completed ? '#64748b' : '#0f172a',
          textDecoration: item.completed ? 'line-through' : 'none',
          fontSize: '1rem'
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "checkbox",
        checked: item.completed,
        disabled: true,
        style: {
          width: '20px',
          height: '20px',
          accentColor: '#3b82f6',
          cursor: 'pointer'
        }
      }), /*#__PURE__*/React.createElement("span", null, item.text))))), viewingTask.attachments && viewingTask.attachments.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '16px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Attachments"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, viewingTask.attachments.map((attachment, index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          wordBreak: 'break-word'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          minWidth: 0
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1.5rem',
          color: '#64748b'
        }
      }, "\uD83D\uDCCE"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.9rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, attachment.name), /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.75rem',
          color: '#64748b'
        }
      }, "Uploaded by ", userNames[attachment.uploadedBy] || 'Unknown User', " on ", new Date(attachment.uploadedAt).toLocaleDateString()))), /*#__PURE__*/React.createElement("button", {
        onClick: () => {
          try {
            let base64Data = attachment.data;
            if (base64Data.startsWith('data:')) {
              base64Data = base64Data.split(',')[1];
            }
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const mimeType = attachment.type || 'application/octet-stream';
            const blob = new Blob([byteArray], {
              type: mimeType
            });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = attachment.name;
            document.body.appendChild(link);
            link.click();
            setTimeout(() => {
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
            }, 100);
          } catch (error) {
            console.error('Error downloading file:', error);
            alert('Failed to download file. Please try again.');
          }
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#e2e8f0',
          border: 'none',
          borderRadius: '6px',
          color: '#0f172a',
          fontSize: '0.875rem',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease'
        },
        onMouseOver: e => e.currentTarget.style.backgroundColor = '#cbd5e1',
        onMouseOut: e => e.currentTarget.style.backgroundColor = '#e2e8f0'
      }, "Download"))))), viewingTask.links && viewingTask.links.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '16px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Links"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, viewingTask.links.map((link, index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          wordBreak: 'break-word'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          minWidth: 0
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1.5rem',
          color: '#64748b'
        }
      }, "\uD83D\uDD17"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("a", {
        href: link.url,
        target: "_blank",
        rel: "noopener noreferrer",
        style: {
          fontSize: '0.9rem',
          color: '#2563eb',
          textDecoration: 'none',
          fontWeight: '500'
        },
        onMouseOver: e => e.currentTarget.style.textDecoration = 'underline',
        onMouseOut: e => e.currentTarget.style.textDecoration = 'none'
      }, link.url), /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.75rem',
          color: '#64748b'
        }
      }, "Added by ", userNames[link.addedBy] || 'Unknown User', " on ", new Date(link.addedAt).toLocaleDateString()))))))))), /*#__PURE__*/React.createElement("div", {
        style: {
          flex: 1,
          minWidth: 350,
          maxWidth: 500
        }
      }, /*#__PURE__*/React.createElement(AIInsightsPanel, {
        taskId: viewingTask._id,
        task: viewingTask
      })));
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        marginBottom: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Tasks Management"), /*#__PURE__*/React.createElement("button", {
      onClick: handleCreateTask,
      className: "btn btn-primary"
    }, "Create New Task")), successMessage && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        color: '#15803d',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, successMessage), showCreateForm ? /*#__PURE__*/React.createElement(CreateTask, {
      onCancel: handleCancel
    }) : editingTask ? /*#__PURE__*/React.createElement(EditTask, {
      task: editingTask,
      onCancel: handleCancel,
      onUpdate: handleTaskUpdate
    }) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search tasks...",
      value: searchQuery,
      onChange: e => setSearchQuery(e.target.value),
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    })), /*#__PURE__*/React.createElement("select", {
      value: filterStatus,
      onChange: e => setFilterStatus(e.target.value),
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '120px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Status"), /*#__PURE__*/React.createElement("option", {
      value: "pending"
    }, "Pending"), /*#__PURE__*/React.createElement("option", {
      value: "in-progress"
    }, "In Progress"), /*#__PURE__*/React.createElement("option", {
      value: "completed"
    }, "Completed")), /*#__PURE__*/React.createElement("select", {
      value: filterPriority,
      onChange: e => setFilterPriority(e.target.value),
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '120px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Priority"), /*#__PURE__*/React.createElement("option", {
      value: "high"
    }, "High"), /*#__PURE__*/React.createElement("option", {
      value: "medium"
    }, "Medium"), /*#__PURE__*/React.createElement("option", {
      value: "low"
    }, "Low")), /*#__PURE__*/React.createElement("select", {
      value: "".concat(sortBy, "-").concat(sortOrder),
      onChange: e => {
        const [newSortBy, newSortOrder] = e.target.value.split('-');
        setSortBy(newSortBy);
        setSortOrder(newSortOrder);
      },
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '150px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "createdAt-desc"
    }, "Newest First"), /*#__PURE__*/React.createElement("option", {
      value: "createdAt-asc"
    }, "Oldest First"), /*#__PURE__*/React.createElement("option", {
      value: "dueDate-asc"
    }, "Due Date (Earliest)"), /*#__PURE__*/React.createElement("option", {
      value: "dueDate-desc"
    }, "Due Date (Latest)"), /*#__PURE__*/React.createElement("option", {
      value: "priority-desc"
    }, "Priority (High to Low)"), /*#__PURE__*/React.createElement("option", {
      value: "priority-asc"
    }, "Priority (Low to High)"), /*#__PURE__*/React.createElement("option", {
      value: "title-asc"
    }, "Title (A-Z)"), /*#__PURE__*/React.createElement("option", {
      value: "title-desc"
    }, "Title (Z-A)"))), /*#__PURE__*/React.createElement("div", {
      className: "tasks-grid"
    }, isLoading ? /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "loading-spinner"
    })) : filteredAndSortedTasks.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px',
        padding: '16px 0'
      }
    }, filteredAndSortedTasks.map(task => /*#__PURE__*/React.createElement("div", {
      key: task._id,
      onClick: () => navigate("/admin-dashboard/tasks/".concat(task._id)),
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '12px',
        padding: '20px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e2e8f0',
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        ':hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        margin: 0,
        fontSize: '1.1rem',
        fontWeight: '600',
        color: '#0f172a'
      }
    }, task.title), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '4px 8px',
        borderRadius: '6px',
        fontSize: '0.75rem',
        fontWeight: '500',
        backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef3c7' : '#dcfce7',
        color: task.priority === 'high' ? '#dc2626' : task.priority === 'medium' ? '#d97706' : '#16a34a'
      }
    }, task.priority), task.category && /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '4px 8px',
        borderRadius: '6px',
        fontSize: '0.75rem',
        fontWeight: '500',
        backgroundColor: '#f1f5f9',
        color: '#475569'
      }
    }, task.category))), /*#__PURE__*/React.createElement("p", {
      style: {
        margin: 0,
        fontSize: '0.875rem',
        color: '#475569',
        display: '-webkit-box',
        WebkitLineClamp: 2,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxHeight: '2.6em'
      }
    }, task.description), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.875rem',
        color: '#64748b'
      }
    }, "Status:"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '2px 8px',
        borderRadius: '4px',
        fontSize: '0.75rem',
        fontWeight: '500',
        backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in-progress' ? '#fef3c7' : '#fee2e2',
        color: task.status === 'completed' ? '#16a34a' : task.status === 'in-progress' ? '#d97706' : '#dc2626'
      }
    }, task.status)), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.875rem',
        color: '#64748b'
      }
    }, "Progress:"), /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        height: '6px',
        backgroundColor: '#e2e8f0',
        borderRadius: '3px',
        overflow: 'hidden'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: "".concat(task.progress, "%"),
        height: '100%',
        backgroundColor: task.progress === 100 ? '#16a34a' : task.progress > 50 ? '#3b82f6' : task.progress > 0 ? '#f59e0b' : '#dc2626',
        transition: 'width 0.3s ease'
      }
    })), /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.75rem',
        color: '#475569',
        minWidth: '40px'
      }
    }, task.progress, "%")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.875rem',
        color: '#64748b'
      }
    }, "Due:"), /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.875rem',
        color: '#0f172a'
      }
    }, new Date(task.dueDate).toLocaleDateString())), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'flex-start',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.875rem',
        color: '#64748b'
      }
    }, "Assigned to:"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '4px'
      }
    }, isLoadingUsers ? /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.75rem',
        padding: '2px 6px',
        borderRadius: '4px',
        backgroundColor: '#f1f5f9',
        color: '#475569'
      }
    }, "Loading...") : task.assignedTo.length === 0 ? /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.75rem',
        padding: '2px 6px',
        borderRadius: '4px',
        backgroundColor: '#f1f5f9',
        color: '#475569'
      }
    }, "Unassigned") : /*#__PURE__*/React.createElement(React.Fragment, null, task.assignedTo.slice(0, 2).map(userId => /*#__PURE__*/React.createElement("span", {
      key: userId,
      style: {
        fontSize: '0.75rem',
        padding: '2px 6px',
        borderRadius: '4px',
        backgroundColor: '#f1f5f9',
        color: '#475569'
      }
    }, userNames[userId] || 'Unknown User')), task.assignedTo.length > 2 && /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '0.75rem',
        padding: '2px 6px',
        borderRadius: '4px',
        backgroundColor: '#e2e8f0',
        color: '#475569',
        cursor: 'pointer'
      },
      onClick: e => {
        e.stopPropagation();
        const remainingMembers = task.assignedTo.slice(2).map(userId => userNames[userId] || 'Unknown User');
        alert("Additional team members:\n".concat(remainingMembers.join('\n')));
      }
    }, "+", task.assignedTo.length - 2, " more"))))), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: '8px',
        padding: '8px',
        backgroundColor: '#f8fafc',
        borderRadius: '6px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        fontSize: '0.875rem',
        color: '#64748b',
        marginBottom: '4px'
      }
    }, "Checklist (", task.checklist.filter(item => item.completed).length, "/", task.checklist.length, ")"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '4px'
      }
    }, task.checklist.slice(0, 3).map((item, index) => /*#__PURE__*/React.createElement("div", {
      key: index,
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        fontSize: '0.875rem',
        color: item.completed ? '#64748b' : '#0f172a',
        textDecoration: item.completed ? 'line-through' : 'none'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "checkbox",
      checked: item.completed,
      disabled: true,
      style: {
        width: '16px',
        height: '16px',
        borderRadius: '4px',
        border: '1px solid #e2e8f0'
      }
    }), /*#__PURE__*/React.createElement("span", {
      style: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '200px'
      }
    }, item.text))), task.checklist.length > 3 && /*#__PURE__*/React.createElement("div", {
      style: {
        fontSize: '0.75rem',
        color: '#64748b',
        textAlign: 'center',
        marginTop: '4px'
      }
    }, "+", task.checklist.length - 3, " more items..."))), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '8px',
        marginTop: 'auto',
        paddingTop: '12px',
        borderTop: '1px solid #e2e8f0'
      }
    }, /*#__PURE__*/React.createElement("button", {
      className: "btn btn-secondary",
      style: {
        padding: '6px 12px',
        fontSize: '0.875rem'
      },
      onClick: e => {
        e.stopPropagation();
        handleEditTask(task);
      }
    }, "Edit"), /*#__PURE__*/React.createElement("button", {
      className: "btn btn-secondary",
      style: {
        padding: '6px 12px',
        fontSize: '0.875rem'
      },
      onClick: e => {
        e.stopPropagation();
        handleDeleteTask(task._id);
      }
    }, "Delete"))))) : /*#__PURE__*/React.createElement("p", {
      style: {
        textAlign: 'center',
        color: '#475569'
      }
    }, searchQuery || filterStatus !== 'all' || filterPriority !== 'all' ? 'No tasks match your filters' : 'No tasks found. Create your first task!')))));
  };
  _s2(TasksPage, "haIMCGJnOXw241pK4MoXfftCTNs=", false, function () {
    return [useParams, useNavigate, useLocation, useTracker];
  });
  _c2 = TasksPage;
  const TeamPage = () => {
    _s3();
    const [selectedMember, setSelectedMember] = useState(null);
    const [successMessage, setSuccessMessage] = useState('');
    const [searchQuery, setSearchQuery] = useState('');
    const [refreshKey, setRefreshKey] = useState(0);
    const {
      memberId
    } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const {
      teamMembers,
      tasks,
      isLoading
    } = useTracker(() => {
      const handle = Meteor.subscribe('teamMembers');
      const tasksHandle = Meteor.subscribe('tasks');
      const isLoading = !handle.ready() || !tasksHandle.ready();
      const members = Meteor.users.find({
        $or: [{
          'roles': 'team-member'
        }, {
          'profile.role': 'team-member'
        }]
      }, {
        fields: {
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        },
        sort: {
          'profile.firstName': 1,
          'profile.lastName': 1
        }
      }).fetch();
      const tasks = Tasks.find().fetch();
      return {
        teamMembers: members,
        tasks,
        isLoading
      };
    }, [refreshKey]);

    // Set selected member when memberId changes
    useEffect(() => {
      if (memberId && teamMembers) {
        const member = teamMembers.find(m => m._id === memberId);
        if (member) {
          setSelectedMember(member);
        }
      } else {
        setSelectedMember(null);
      }
    }, [memberId, teamMembers]);
    const handleMemberClick = member => {
      setSelectedMember(member);
    };
    const handleBackToList = () => {
      setSelectedMember(null);
    };
    if (selectedMember) {
      var _selectedMember$profi, _selectedMember$profi2, _selectedMember$email, _selectedMember$email2, _selectedMember$profi3;
      return /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '24px',
          background: '#ffffff',
          minHeight: 'calc(100vh - 64px)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        onClick: () => {
          var _location$state3;
          if ((_location$state3 = location.state) !== null && _location$state3 !== void 0 && _location$state3.from) {
            navigate(location.state.from);
          } else {
            setSelectedMember(null);
            navigate('/admin-dashboard/team');
          }
        },
        style: {
          background: 'none',
          border: 'none',
          cursor: 'pointer',
          fontSize: '1.8rem',
          color: '#64748b',
          transition: 'color 0.2s ease',
          marginRight: '12px',
          padding: 0,
          display: 'flex',
          alignItems: 'center'
        },
        onMouseOver: e => e.currentTarget.style.color = '#334155',
        onMouseOut: e => e.currentTarget.style.color = '#64748b',
        "aria-label": "Back"
      }, "\u2190"), /*#__PURE__*/React.createElement("h2", {
        style: {
          color: '#0f172a',
          margin: 0,
          fontSize: '1.5rem',
          fontWeight: '600',
          display: 'inline-block',
          wordBreak: 'break-word'
        }
      }, (_selectedMember$profi = selectedMember.profile) === null || _selectedMember$profi === void 0 ? void 0 : _selectedMember$profi.firstName, " ", (_selectedMember$profi2 = selectedMember.profile) === null || _selectedMember$profi2 === void 0 ? void 0 : _selectedMember$profi2.lastName)), /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '24px',
          padding: '24px',
          backgroundColor: '#f8fafc',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '16px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Email"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, (_selectedMember$email = selectedMember.emails) === null || _selectedMember$email === void 0 ? void 0 : (_selectedMember$email2 = _selectedMember$email[0]) === null || _selectedMember$email2 === void 0 ? void 0 : _selectedMember$email2.address)), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '16px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Role"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, ((_selectedMember$profi3 = selectedMember.profile) === null || _selectedMember$profi3 === void 0 ? void 0 : _selectedMember$profi3.role) || 'Team Member')), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '16px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Joined"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, selectedMember.createdAt ? new Date(selectedMember.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }) : 'Not specified'))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '20px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          marginBottom: '16px',
          fontWeight: '600'
        }
      }, "Task Status"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Completed"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#dcfce7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(task => task.status === 'completed' && task.assignedTo.includes(selectedMember._id)).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "In Progress"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fef3c7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(task => task.status === 'in-progress' && task.assignedTo.includes(selectedMember._id)).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Pending"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fee2e2',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(task => task.status === 'pending' && task.assignedTo.includes(selectedMember._id)).length)))), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '20px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          marginBottom: '16px',
          fontWeight: '600'
        }
      }, "Task Priority"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "High Priority"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fee2e2',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(task => task.priority === 'high' && task.assignedTo.includes(selectedMember._id)).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Medium Priority"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fef3c7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(task => task.priority === 'medium' && task.assignedTo.includes(selectedMember._id)).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Low Priority"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#dcfce7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(task => task.priority === 'low' && task.assignedTo.includes(selectedMember._id)).length)))), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '20px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          marginBottom: '16px',
          fontWeight: '600'
        }
      }, "Performance"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Completion Rate"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#f1f5f9',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, (() => {
        const assignedTasks = tasks.filter(task => task.assignedTo.includes(selectedMember._id));
        if (assignedTasks.length === 0) return '0.0%';
        const completedTasks = assignedTasks.filter(task => task.status === 'completed').length;
        return "".concat((completedTasks / assignedTasks.length * 100).toFixed(1), "%");
      })())), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "On-Time Rate"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#f1f5f9',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, (() => {
        const assignedTasks = tasks.filter(task => task.assignedTo.includes(selectedMember._id));
        if (assignedTasks.length === 0) return '0.0%';
        const onTimeTasks = assignedTasks.filter(task => {
          if (task.status !== 'completed') return false;
          return new Date(task.completedAt) <= new Date(task.dueDate);
        }).length;
        return "".concat((onTimeTasks / assignedTasks.length * 100).toFixed(1), "%");
      })())), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Average Progress"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#f1f5f9',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, (() => {
        const assignedTasks = tasks.filter(task => task.assignedTo.includes(selectedMember._id));
        if (assignedTasks.length === 0) return '0.0%';
        const totalProgress = assignedTasks.reduce((sum, task) => sum + (task.progress || 0), 0);
        return "".concat((totalProgress / assignedTasks.length).toFixed(1), "%");
      })()))))))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement(TaskAssignmentHistory, {
        memberId: selectedMember._id
      }))));
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Team Management")), successMessage && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        color: '#15803d',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, successMessage), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search team members...",
      value: searchQuery,
      onChange: e => setSearchQuery(e.target.value),
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    }))), isLoading ? /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "loading-spinner"
    })) : teamMembers.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px'
      }
    }, teamMembers.map(member => {
      var _member$profile, _member$profile2, _member$emails, _member$emails$, _member$profile3, _member$profile4, _member$profile5;
      return /*#__PURE__*/React.createElement("div", {
        key: member._id,
        onClick: () => handleMemberClick(member),
        style: {
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          padding: '20px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e2e8f0',
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          ':hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
        style: {
          margin: 0,
          fontSize: '1.1rem',
          fontWeight: '600',
          color: '#0f172a'
        }
      }, (_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName, " ", (_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName), /*#__PURE__*/React.createElement("p", {
        style: {
          margin: '4px 0 0',
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, (_member$emails = member.emails) === null || _member$emails === void 0 ? void 0 : (_member$emails$ = _member$emails[0]) === null || _member$emails$ === void 0 ? void 0 : _member$emails$.address)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569'
        }
      }, ((_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : _member$profile3.role) || 'Team Member'))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }
      }, ((_member$profile4 = member.profile) === null || _member$profile4 === void 0 ? void 0 : _member$profile4.department) && /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Department:"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#0f172a'
        }
      }, member.profile.department)), ((_member$profile5 = member.profile) === null || _member$profile5 === void 0 ? void 0 : _member$profile5.skills) && member.profile.skills.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          flexWrap: 'wrap'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Skills:"), member.profile.skills.map((skill, index) => /*#__PURE__*/React.createElement("span", {
        key: index,
        style: {
          fontSize: '0.75rem',
          padding: '2px 6px',
          borderRadius: '4px',
          backgroundColor: '#e2e8f0',
          color: '#475569'
        }
      }, skill)))));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        textAlign: 'center',
        color: '#475569'
      }
    }, searchQuery ? 'No team members match your search' : 'No team members found.')));
  };
  _s3(TeamPage, "lKdXwm6kORrbMJTCCSZUPsDTM6s=", false, function () {
    return [useParams, useNavigate, useLocation, useTracker];
  });
  _c3 = TeamPage;
  const AdminDashboard = () => {
    _s4();
    const navigate = useNavigate();
    const handleLogout = () => {
      Meteor.logout(err => {
        if (err) {
          console.error('Logout error:', err);
        } else {
          navigate('/login');
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        minHeight: '100vh',
        backgroundColor: '#ffffff'
      }
    }, /*#__PURE__*/React.createElement(Header, {
      userRole: "admin"
    }), /*#__PURE__*/React.createElement(Routes, null, /*#__PURE__*/React.createElement(Route, {
      path: "/",
      element: /*#__PURE__*/React.createElement(AdminDashboardHome, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks",
      element: /*#__PURE__*/React.createElement(TasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks/:taskId",
      element: /*#__PURE__*/React.createElement(TasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team",
      element: /*#__PURE__*/React.createElement(TeamPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team/:memberId",
      element: /*#__PURE__*/React.createElement(TeamPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/reports",
      element: /*#__PURE__*/React.createElement(ReportsPage, null)
    })));
  };
  _s4(AdminDashboard, "CzcTeTziyjMsSrAVmHuCCb6+Bfg=", false, function () {
    return [useNavigate];
  });
  _c4 = AdminDashboard;
  var _c, _c2, _c3, _c4;
  $RefreshReg$(_c, "AdminDashboardHome");
  $RefreshReg$(_c2, "TasksPage");
  $RefreshReg$(_c3, "TeamPage");
  $RefreshReg$(_c4, "AdminDashboard");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"ForgotPasswordPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/ForgotPasswordPage.jsx                                                                             //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    ForgotPasswordPage: () => ForgotPasswordPage
  });
  let React, useState;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let Link, useNavigate;
  module1.link("react-router-dom", {
    Link(v) {
      Link = v;
    },
    useNavigate(v) {
      useNavigate = v;
    }
  }, 2);
  let Formik, Form, Field;
  module1.link("formik", {
    Formik(v) {
      Formik = v;
    },
    Form(v) {
      Form = v;
    },
    Field(v) {
      Field = v;
    }
  }, 3);
  let Yup;
  module1.link("yup", {
    "*"(v) {
      Yup = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const ForgotPasswordSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email address').required('Email is required'),
    newPassword: Yup.string().required('New password is required').min(8, 'Password must be at least 8 characters').matches(/[A-Z]/, 'Password must contain at least one uppercase letter').matches(/[0-9]/, 'Password must contain at least one number').matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),
    confirmPassword: Yup.string().oneOf([Yup.ref('newPassword'), null], 'Passwords must match').required('Confirm password is required')
  });
  const ForgotPasswordPage = () => {
    _s();
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const navigate = useNavigate();
    const handleSubmit = async (values, _ref) => {
      let {
        setSubmitting
      } = _ref;
      setError('');
      setSuccess(false);
      try {
        await Meteor.callAsync('users.directResetPassword', values.email, values.newPassword, values.confirmPassword);
        setSuccess(true);
        setTimeout(() => navigate('/login'), 2000);
      } catch (err) {
        setError(err.reason || 'Failed to reset password');
      } finally {
        setSubmitting(false);
      }
    };
    return /*#__PURE__*/React.createElement("div", {
      className: "auth-container"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-card"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-header"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-logo"
    }, /*#__PURE__*/React.createElement("svg", {
      width: "48",
      height: "48",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/React.createElement("path", {
      d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }))), /*#__PURE__*/React.createElement("h1", {
      className: "auth-title"
    }, "Reset Password"), /*#__PURE__*/React.createElement("p", {
      className: "auth-subtitle"
    }, "Enter your email and new password to reset your account password.")), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        email: '',
        newPassword: '',
        confirmPassword: ''
      },
      validationSchema: ForgotPasswordSchema,
      onSubmit: handleSubmit
    }, _ref2 => {
      let {
        errors,
        touched,
        isSubmitting
      } = _ref2;
      return /*#__PURE__*/React.createElement(Form, {
        className: "auth-form"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "form-label"
      }, "Email Address"), /*#__PURE__*/React.createElement(Field, {
        id: "email",
        name: "email",
        type: "email",
        placeholder: "Enter your email address",
        className: "form-control ".concat(errors.email && touched.email ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "newPassword",
        className: "form-label"
      }, "New Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "newPassword",
        name: "newPassword",
        type: showPassword ? 'text' : 'password',
        placeholder: "Enter new password",
        className: "form-control ".concat(errors.newPassword && touched.newPassword ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showPassword ? 'Hide password' : 'Show password',
        onClick: () => setShowPassword(v => !v)
      }, showPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.newPassword && touched.newPassword && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, errors.newPassword)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "confirmPassword",
        className: "form-label"
      }, "Confirm New Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "confirmPassword",
        name: "confirmPassword",
        type: showConfirmPassword ? 'text' : 'password',
        placeholder: "Confirm new password",
        className: "form-control ".concat(errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showConfirmPassword ? 'Hide password' : 'Show password',
        onClick: () => setShowConfirmPassword(v => !v)
      }, showConfirmPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.confirmPassword && touched.confirmPassword && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, errors.confirmPassword)), error && /*#__PURE__*/React.createElement("div", {
        className: "error-message error-message-global"
      }, error), success && /*#__PURE__*/React.createElement("div", {
        className: "success-message"
      }, "Password reset successful! Redirecting to login..."), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "btn btn-primary btn-auth",
        disabled: isSubmitting
      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
        className: "loading-spinner"
      }), "Resetting...") : 'Reset Password'), /*#__PURE__*/React.createElement("div", {
        className: "auth-links-row"
      }, /*#__PURE__*/React.createElement(Link, {
        to: "/login",
        className: "auth-link"
      }, "Back to login")));
    })));
  };
  _s(ForgotPasswordPage, "SKcJ1cLrH/PgGBigo3yeBxHWOfg=", false, function () {
    return [useNavigate];
  });
  _c = ForgotPasswordPage;
  var _c;
  $RefreshReg$(_c, "ForgotPasswordPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"LoginPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/LoginPage.jsx                                                                                      //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    LoginPage: () => LoginPage
  });
  let React, useState, useEffect;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useNavigate, Link;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    },
    Link(v) {
      Link = v;
    }
  }, 2);
  let Formik, Form, Field;
  module1.link("formik", {
    Formik(v) {
      Formik = v;
    },
    Form(v) {
      Form = v;
    },
    Field(v) {
      Field = v;
    }
  }, 3);
  let Yup;
  module1.link("yup", {
    "*"(v) {
      Yup = v;
    }
  }, 4);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const LoginSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email address').required('Email is required'),
    password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required')
  });
  const LoginPage = () => {
    _s();
    const navigate = useNavigate();
    const [error, setError] = useState('');
    const [resendEmailSent, setResendEmailSent] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const {
      user
    } = useTracker(() => {
      const subscription = Meteor.subscribe('userData');
      return {
        user: Meteor.user(),
        isLoading: !subscription.ready()
      };
    }, []);
    useEffect(() => {
      if (user) {
        var _user$profile, _user$roles;
        const role = ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);
        navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');
      }
    }, [user, navigate]);
    const handleResendVerification = email => {
      Meteor.call('users.resendVerificationEmail', email, err => {
        if (err) {
          setError(err.reason || 'Failed to resend verification email');
        } else {
          setResendEmailSent(true);
        }
      });
    };
    const handleSubmit = (_ref, _ref2) => {
      let {
        email,
        password
      } = _ref;
      let {
        setSubmitting
      } = _ref2;
      setError(''); // Clear any previous errors
      setResendEmailSent(false);
      Meteor.loginWithPassword(email, password, err => {
        if (err) {
          console.error('Login error:', err);
          if (err.error === 'email-not-verified') {
            setError(/*#__PURE__*/React.createElement("div", null, "Email not verified.", /*#__PURE__*/React.createElement("button", {
              onClick: () => handleResendVerification(email),
              className: "auth-link",
              style: {
                background: 'none',
                border: 'none',
                padding: '0 4px',
                cursor: 'pointer'
              }
            }, "Resend verification email"), resendEmailSent && /*#__PURE__*/React.createElement("div", {
              style: {
                marginTop: '8px',
                color: 'var(--success)',
                fontSize: '0.875rem'
              }
            }, "Verification email sent!")));
          } else {
            setError(err.reason || 'Login failed. Please check your credentials.');
          }
          setSubmitting(false);
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      className: "auth-container"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-card"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-header"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-logo"
    }, /*#__PURE__*/React.createElement("svg", {
      width: "48",
      height: "48",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/React.createElement("path", {
      d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }))), /*#__PURE__*/React.createElement("h1", {
      className: "auth-title"
    }, "Welcome Back"), /*#__PURE__*/React.createElement("p", {
      className: "auth-subtitle"
    }, "Sign in to access your workspace and manage your tasks efficiently")), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        email: '',
        password: ''
      },
      validationSchema: LoginSchema,
      onSubmit: handleSubmit
    }, _ref3 => {
      let {
        errors,
        touched,
        isSubmitting
      } = _ref3;
      return /*#__PURE__*/React.createElement(Form, {
        className: "auth-form"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Email Address"), /*#__PURE__*/React.createElement(Field, {
        id: "email",
        name: "email",
        type: "email",
        placeholder: "Enter your email address",
        className: "form-control ".concat(errors.email && touched.email ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "password",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "password",
        name: "password",
        type: showPassword ? 'text' : 'password',
        placeholder: "Enter your password",
        className: "form-control ".concat(errors.password && touched.password ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showPassword ? 'Hide password' : 'Show password',
        onClick: () => setShowPassword(v => !v)
      }, showPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.password && touched.password && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.password)), error && /*#__PURE__*/React.createElement("div", {
        className: "error-message error-message-global"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), error), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "btn btn-primary btn-auth",
        disabled: isSubmitting
      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
        className: "loading-spinner"
      }), "Signing in...") : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("svg", {
        className: "btn-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Sign In")), /*#__PURE__*/React.createElement("div", {
        className: "auth-links-row"
      }, /*#__PURE__*/React.createElement(Link, {
        to: "/forgot-password",
        className: "auth-link"
      }, "Forgot password?")));
    }), /*#__PURE__*/React.createElement("div", {
      className: "auth-footer"
    }, /*#__PURE__*/React.createElement("p", {
      className: "auth-footer-text"
    }, "Don't have an account?", /*#__PURE__*/React.createElement(Link, {
      to: "/signup",
      className: "auth-link"
    }, "Create an account")))));
  };
  _s(LoginPage, "NEaA31AkfK7s/oVXFdSbcR+dzU4=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = LoginPage;
  var _c;
  $RefreshReg$(_c, "LoginPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"ReportsPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/ReportsPage.jsx                                                                                    //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  let _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default(v) {
      _objectSpread = v;
    }
  }, 0);
  module1.export({
    ReportsPage: () => ReportsPage
  });
  let React;
  module1.link("react", {
    default(v) {
      React = v;
    }
  }, 0);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 1);
  let Tasks;
  module1.link("/imports/api/tasks", {
    Tasks(v) {
      Tasks = v;
    }
  }, 2);
  let ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement;
  module1.link("chart.js", {
    Chart(v) {
      ChartJS = v;
    },
    ArcElement(v) {
      ArcElement = v;
    },
    Tooltip(v) {
      Tooltip = v;
    },
    Legend(v) {
      Legend = v;
    },
    CategoryScale(v) {
      CategoryScale = v;
    },
    LinearScale(v) {
      LinearScale = v;
    },
    BarElement(v) {
      BarElement = v;
    }
  }, 3);
  let Pie, Bar;
  module1.link("react-chartjs-2", {
    Pie(v) {
      Pie = v;
    },
    Bar(v) {
      Bar = v;
    }
  }, 4);
  let XLSX;
  module1.link("xlsx", {
    "*"(v) {
      XLSX = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  // Register Chart.js components
  ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);
  const ReportsPage = () => {
    _s();
    const {
      tasks,
      isLoading,
      teamMembers
    } = useTracker(() => {
      const tasksHandle = Meteor.subscribe('tasks');
      const teamMembersHandle = Meteor.subscribe('teamMembers');
      const tasks = Tasks.find({}, {
        sort: {
          createdAt: -1
        }
      }).fetch();
      const teamMembers = Meteor.users.find({
        $or: [{
          'roles': 'team-member'
        }, {
          'profile.role': 'team-member'
        }]
      }, {
        fields: {
          _id: 1,
          'profile.firstName': 1,
          'profile.lastName': 1
        }
      }).fetch();
      return {
        tasks,
        teamMembers,
        isLoading: !tasksHandle.ready() || !teamMembersHandle.ready()
      };
    });
    const handleDownloadReport = () => {
      // Create a map of user IDs to full names
      const userNames = {};
      teamMembers.forEach(member => {
        var _member$profile, _member$profile2;
        const firstName = ((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '';
        const lastName = ((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '';
        userNames[member._id] = "".concat(firstName, " ").concat(lastName).trim() || 'Unknown User';
      });

      // Prepare data for Excel
      const excelData = tasks.map(task => {
        const assignedToNames = task.assignedTo.map(userId => userNames[userId] || 'Unknown User');
        return {
          'Task ID': task._id,
          'Title': task.title,
          'Status': task.status,
          'Priority': task.priority,
          'Progress': "".concat(task.progress, "%"),
          'Start Date': new Date(task.startDate).toLocaleDateString(),
          'Due Date': new Date(task.dueDate).toLocaleDateString(),
          'Created At': new Date(task.createdAt).toLocaleDateString(),
          'Assigned To': assignedToNames.join('; ')
        };
      });

      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(excelData);

      // Set column widths
      const columnWidths = [{
        wch: 36
      },
      // Task ID
      {
        wch: 30
      },
      // Title
      {
        wch: 12
      },
      // Status
      {
        wch: 10
      },
      // Priority
      {
        wch: 10
      },
      // Progress
      {
        wch: 12
      },
      // Start Date
      {
        wch: 12
      },
      // Due Date
      {
        wch: 12
      },
      // Created At
      {
        wch: 30
      } // Assigned To
      ];
      worksheet['!cols'] = columnWidths;

      // Create workbook
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Task Report');

      // Generate Excel file
      XLSX.writeFile(workbook, "task-report-".concat(new Date().toISOString().split('T')[0], ".xlsx"));
    };
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }

    // Calculate task status distribution
    const statusData = {
      completed: tasks.filter(task => task.status === 'completed').length,
      'in-progress': tasks.filter(task => task.status === 'in-progress').length,
      pending: tasks.filter(task => task.status === 'pending').length
    };

    // Calculate task priority distribution
    const priorityData = {
      high: tasks.filter(task => task.priority === 'high').length,
      medium: tasks.filter(task => task.priority === 'medium').length,
      low: tasks.filter(task => task.priority === 'low').length
    };

    // Task Status Chart Configuration
    const statusChartData = {
      labels: ['Completed', 'In Progress', 'Pending'],
      datasets: [{
        data: [statusData.completed, statusData['in-progress'], statusData.pending],
        backgroundColor: ['rgba(22, 163, 74, 0.8)',
        // Green for completed
        'rgba(59, 130, 246, 0.8)',
        // Blue for in-progress
        'rgba(245, 158, 11, 0.8)' // Yellow for pending
        ],
        borderColor: ['rgb(22, 163, 74)', 'rgb(59, 130, 246)', 'rgb(245, 158, 11)'],
        borderWidth: 1
      }]
    };

    // Task Priority Chart Configuration
    const priorityChartData = {
      labels: ['High', 'Medium', 'Low'],
      datasets: [{
        label: 'Number of Tasks',
        data: [priorityData.high, priorityData.medium, priorityData.low],
        backgroundColor: ['rgba(239, 68, 68, 0.8)',
        // Red for high
        'rgba(245, 158, 11, 0.8)',
        // Yellow for medium
        'rgba(22, 163, 74, 0.8)' // Green for low
        ],
        borderColor: ['rgb(239, 68, 68)', 'rgb(245, 158, 11)', 'rgb(22, 163, 74)'],
        borderWidth: 1
      }]
    };
    const chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Task Analytics"), /*#__PURE__*/React.createElement("button", {
      onClick: handleDownloadReport,
      className: "btn btn-primary",
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCE5"), "Download Detailed Report")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.1rem',
        marginBottom: '16px',
        fontWeight: '600'
      }
    }, "Task Status Distribution"), /*#__PURE__*/React.createElement("div", {
      style: {
        height: '300px'
      }
    }, /*#__PURE__*/React.createElement(Pie, {
      data: statusChartData,
      options: chartOptions
    }))), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.1rem',
        marginBottom: '16px',
        fontWeight: '600'
      }
    }, "Task Priority Distribution"), /*#__PURE__*/React.createElement("div", {
      style: {
        height: '300px'
      }
    }, /*#__PURE__*/React.createElement(Bar, {
      data: priorityChartData,
      options: _objectSpread(_objectSpread({}, chartOptions), {}, {
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        }
      })
    })))), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px',
        marginTop: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "Total Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, tasks.length)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "Completed Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#16a34a',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, statusData.completed)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "In Progress"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#3b82f6',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, statusData['in-progress'])), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "Pending Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#f59e0b',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, statusData.pending)))));
  };
  _s(ReportsPage, "4o4tbTyPIeBeo4AlHFSH4ARbaLQ=", false, function () {
    return [useTracker];
  });
  _c = ReportsPage;
  var _c;
  $RefreshReg$(_c, "ReportsPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"SignupPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/SignupPage.jsx                                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    SignupPage: () => SignupPage
  });
  let React, useState;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useNavigate, Link;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    },
    Link(v) {
      Link = v;
    }
  }, 2);
  let Formik, Form, Field;
  module1.link("formik", {
    Formik(v) {
      Formik = v;
    },
    Form(v) {
      Form = v;
    },
    Field(v) {
      Field = v;
    }
  }, 3);
  let Yup;
  module1.link("yup", {
    "*"(v) {
      Yup = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const SignupSchema = Yup.object().shape({
    firstName: Yup.string().min(2, 'First name must be at least 2 characters').required('First name is required'),
    lastName: Yup.string().min(2, 'Last name must be at least 2 characters').required('Last name is required'),
    email: Yup.string().email('Invalid email address').required('Email is required'),
    password: Yup.string().required('Password is required').min(8, 'Password must be at least 8 characters').matches(/[A-Z]/, 'Password must contain at least one uppercase letter').matches(/[0-9]/, 'Password must contain at least one number').matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),
    confirmPassword: Yup.string().oneOf([Yup.ref('password'), null], 'Passwords must match').required('Confirm password is required'),
    role: Yup.string().oneOf(['admin', 'team-member'], 'Invalid role').required('Role is required'),
    adminToken: Yup.string().when('role', {
      is: 'admin',
      then: () => Yup.string().required('Admin token is required').length(6, 'Admin token must be 6 digits'),
      otherwise: () => Yup.string()
    })
  });
  const SignupPage = () => {
    _s();
    const navigate = useNavigate();
    const [error, setError] = useState('');
    const [verificationSent, setVerificationSent] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const handleSubmit = (values, _ref) => {
      let {
        setSubmitting
      } = _ref;
      const {
        email,
        password,
        role,
        adminToken,
        firstName,
        lastName
      } = values;
      setError('');
      Meteor.call('users.create', {
        email,
        password,
        role,
        adminToken,
        firstName,
        lastName
      }, err => {
        if (err) {
          console.error('Signup error:', err);
          setError(err.reason || 'Signup failed');
          setSubmitting(false);
        } else {
          // Log in the user after successful signup
          Meteor.loginWithPassword(email, password, err => {
            setSubmitting(false);
            if (err) {
              if (err.error === 'email-not-verified') {
                setVerificationSent(true);
              } else {
                console.error('Login error:', err);
                setError(err.reason || 'Login failed after signup');
              }
            } else {
              navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');
            }
          });
        }
      });
    };
    if (verificationSent) {
      return /*#__PURE__*/React.createElement("div", {
        className: "auth-container"
      }, /*#__PURE__*/React.createElement("div", {
        className: "auth-card"
      }, /*#__PURE__*/React.createElement("div", {
        className: "auth-header"
      }, /*#__PURE__*/React.createElement("div", {
        className: "auth-logo"
      }, /*#__PURE__*/React.createElement("svg", {
        width: "48",
        height: "48",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }))), /*#__PURE__*/React.createElement("h1", {
        className: "auth-title"
      }, "Verify Your Email"), /*#__PURE__*/React.createElement("p", {
        className: "auth-subtitle"
      }, "We've sent a verification email to your address. Please check your inbox and click the verification link to activate your account.")), /*#__PURE__*/React.createElement(Link, {
        to: "/login",
        className: "btn btn-primary btn-auth"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "btn-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M19 12H5M12 19L5 12L12 5",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Return to Login")));
    }
    return /*#__PURE__*/React.createElement("div", {
      className: "auth-container"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-card"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-header"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-logo"
    }, /*#__PURE__*/React.createElement("svg", {
      width: "48",
      height: "48",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/React.createElement("path", {
      d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }), /*#__PURE__*/React.createElement("path", {
      d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }))), /*#__PURE__*/React.createElement("h1", {
      className: "auth-title"
    }, "Create Account"), /*#__PURE__*/React.createElement("p", {
      className: "auth-subtitle"
    }, "Join your team's workspace and start managing tasks efficiently")), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        role: 'team-member',
        adminToken: ''
      },
      validationSchema: SignupSchema,
      onSubmit: handleSubmit
    }, _ref2 => {
      let {
        errors,
        touched,
        values,
        isSubmitting
      } = _ref2;
      return /*#__PURE__*/React.createElement(Form, {
        className: "auth-form"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-row"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "firstName",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "First Name"), /*#__PURE__*/React.createElement(Field, {
        id: "firstName",
        name: "firstName",
        type: "text",
        placeholder: "Enter your first name",
        className: "form-control ".concat(errors.firstName && touched.firstName ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.firstName && touched.firstName && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.firstName)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "lastName",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Last Name"), /*#__PURE__*/React.createElement(Field, {
        id: "lastName",
        name: "lastName",
        type: "text",
        placeholder: "Enter your last name",
        className: "form-control ".concat(errors.lastName && touched.lastName ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.lastName && touched.lastName && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.lastName))), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Email Address"), /*#__PURE__*/React.createElement(Field, {
        id: "email",
        name: "email",
        type: "email",
        placeholder: "Enter your email address",
        className: "form-control ".concat(errors.email && touched.email ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "password",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "password",
        name: "password",
        type: showPassword ? 'text' : 'password',
        placeholder: "Create a secure password",
        className: "form-control ".concat(errors.password && touched.password ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showPassword ? 'Hide password' : 'Show password',
        onClick: () => setShowPassword(v => !v)
      }, showPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), /*#__PURE__*/React.createElement("div", {
        className: "password-requirements"
      }, /*#__PURE__*/React.createElement("p", {
        className: "password-requirements-title"
      }, "Password requirements:"), /*#__PURE__*/React.createElement("ul", {
        className: "password-requirements-list"
      }, /*#__PURE__*/React.createElement("li", {
        className: values.password.length >= 8 ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "At least 8 characters"), /*#__PURE__*/React.createElement("li", {
        className: /[A-Z]/.test(values.password) ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "One uppercase letter"), /*#__PURE__*/React.createElement("li", {
        className: /[0-9]/.test(values.password) ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "One number"), /*#__PURE__*/React.createElement("li", {
        className: /[!@#$%^&*]/.test(values.password) ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "One special character (!@#$%^&*)"))), errors.password && touched.password && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.password)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "confirmPassword",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Confirm Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "confirmPassword",
        name: "confirmPassword",
        type: showConfirmPassword ? 'text' : 'password',
        placeholder: "Confirm your password",
        className: "form-control ".concat(errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showConfirmPassword ? 'Hide password' : 'Show password',
        onClick: () => setShowConfirmPassword(v => !v)
      }, showConfirmPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.confirmPassword && touched.confirmPassword && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.confirmPassword)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "role",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 2L2 7L12 12L22 7L12 2Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M2 17L12 22L22 17",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M2 12L12 17L22 12",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Account Type"), /*#__PURE__*/React.createElement(Field, {
        id: "role",
        name: "role",
        as: "select",
        className: "form-control ".concat(errors.role && touched.role ? 'form-control-error' : ''),
        disabled: isSubmitting
      }, /*#__PURE__*/React.createElement("option", {
        value: "team-member"
      }, "Team Member"), /*#__PURE__*/React.createElement("option", {
        value: "admin"
      }, "Administrator")), errors.role && touched.role && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.role)), values.role === 'admin' && /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "adminToken",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Admin Token"), /*#__PURE__*/React.createElement(Field, {
        id: "adminToken",
        name: "adminToken",
        type: "text",
        placeholder: "Enter 6-digit admin token",
        className: "form-control ".concat(errors.adminToken && touched.adminToken ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.adminToken && touched.adminToken && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.adminToken)), error && /*#__PURE__*/React.createElement("div", {
        className: "error-message error-message-global"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), error), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "btn btn-primary btn-auth",
        disabled: isSubmitting
      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
        className: "loading-spinner"
      }), "Creating Account...") : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("svg", {
        className: "btn-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Create Account")));
    }), /*#__PURE__*/React.createElement("div", {
      className: "auth-footer"
    }, /*#__PURE__*/React.createElement("p", {
      className: "auth-footer-text"
    }, "Already have an account?", /*#__PURE__*/React.createElement(Link, {
      to: "/login",
      className: "auth-link"
    }, "Log In")))));
  };
  _s(SignupPage, "Kr2Rw59cPwlTmhH2LL47KLyTCzw=", false, function () {
    return [useNavigate];
  });
  _c = SignupPage;
  var _c;
  $RefreshReg$(_c, "SignupPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TaskDetailPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TaskDetailPage.jsx                                                                                 //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TaskDetailPage: () => TaskDetailPage
  });
  let React, useState, useEffect;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    }
  }, 0);
  let useParams;
  module1.link("react-router-dom", {
    useParams(v) {
      useParams = v;
    }
  }, 1);
  let Container, Paper, Typography, Box, Grid, Chip, Divider, CircularProgress, Alert;
  module1.link("@mui/material", {
    Container(v) {
      Container = v;
    },
    Paper(v) {
      Paper = v;
    },
    Typography(v) {
      Typography = v;
    },
    Box(v) {
      Box = v;
    },
    Grid(v) {
      Grid = v;
    },
    Chip(v) {
      Chip = v;
    },
    Divider(v) {
      Divider = v;
    },
    CircularProgress(v) {
      CircularProgress = v;
    },
    Alert(v) {
      Alert = v;
    }
  }, 2);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 3);
  let AIInsightsPanel;
  module1.link("../components/AIInsightsPanel", {
    AIInsightsPanel(v) {
      AIInsightsPanel = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TaskDetailPage = () => {
    _s();
    const {
      taskId
    } = useParams();
    const [task, setTask] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    useEffect(() => {
      const fetchTask = async () => {
        try {
          const result = await Meteor.call('tasks.findOne', taskId);
          setTask(result);
        } catch (err) {
          setError(err.message);
        } finally {
          setLoading(false);
        }
      };
      fetchTask();
    }, [taskId]);
    if (loading) {
      return /*#__PURE__*/React.createElement(Box, {
        sx: {
          display: 'flex',
          justifyContent: 'center',
          p: 3
        }
      }, /*#__PURE__*/React.createElement(CircularProgress, null));
    }
    if (error) {
      return /*#__PURE__*/React.createElement(Container, {
        maxWidth: "md",
        sx: {
          mt: 4
        }
      }, /*#__PURE__*/React.createElement(Alert, {
        severity: "error"
      }, error));
    }
    if (!task) {
      return /*#__PURE__*/React.createElement(Container, {
        maxWidth: "md",
        sx: {
          mt: 4
        }
      }, /*#__PURE__*/React.createElement(Alert, {
        severity: "warning"
      }, "Task not found"));
    }
    return /*#__PURE__*/React.createElement(Container, {
      maxWidth: "lg",
      sx: {
        mt: 4,
        mb: 4
      }
    }, /*#__PURE__*/React.createElement(Grid, {
      container: true,
      spacing: 3
    }, /*#__PURE__*/React.createElement(Grid, {
      item: true,
      xs: 12,
      md: 8
    }, /*#__PURE__*/React.createElement(Paper, {
      sx: {
        p: 3,
        mb: 3
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "h4",
      gutterBottom: true
    }, task.title), /*#__PURE__*/React.createElement(Box, {
      sx: {
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Chip, {
      label: task.status,
      color: task.status === 'completed' ? 'success' : 'primary',
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Chip, {
      label: task.priority,
      color: task.priority === 'high' ? 'error' : task.priority === 'medium' ? 'warning' : 'info',
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Chip, {
      label: task.category,
      variant: "outlined"
    })), /*#__PURE__*/React.createElement(Divider, {
      sx: {
        my: 2
      }
    }), /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      gutterBottom: true
    }, "Description"), /*#__PURE__*/React.createElement(Typography, {
      variant: "body1",
      paragraph: true
    }, task.description), /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      gutterBottom: true
    }, "Progress"), /*#__PURE__*/React.createElement(Box, {
      sx: {
        width: '100%',
        bgcolor: 'background.paper'
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "body2",
      color: "text.secondary"
    }, task.progress, "% Complete")), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      gutterBottom: true,
      sx: {
        mt: 2
      }
    }, "Checklist"), /*#__PURE__*/React.createElement(Box, {
      component: "ul",
      sx: {
        pl: 2
      }
    }, task.checklist.map((item, index) => /*#__PURE__*/React.createElement(Typography, {
      component: "li",
      key: index,
      sx: {
        textDecoration: item.completed ? 'line-through' : 'none',
        color: item.completed ? 'text.secondary' : 'text.primary'
      }
    }, item.text)))))), /*#__PURE__*/React.createElement(Grid, {
      item: true,
      xs: 12,
      md: 4
    }, /*#__PURE__*/React.createElement(AIInsightsPanel, {
      taskId: taskId
    }))));
  };
  _s(TaskDetailPage, "5YVkBmKnY966FhgZjti6CLprjNQ=", false, function () {
    return [useParams];
  });
  _c = TaskDetailPage;
  var _c;
  $RefreshReg$(_c, "TaskDetailPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamDashboard.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TeamDashboard.jsx                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamDashboard: () => TeamDashboard
  });
  let React, useState;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useNavigate, Routes, Route, Link;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    },
    Routes(v) {
      Routes = v;
    },
    Route(v) {
      Route = v;
    },
    Link(v) {
      Link = v;
    }
  }, 2);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 3);
  let Tasks;
  module1.link("/imports/api/tasks", {
    Tasks(v) {
      Tasks = v;
    }
  }, 4);
  let TeamTasksPage;
  module1.link("./TeamTasksPage", {
    TeamTasksPage(v) {
      TeamTasksPage = v;
    }
  }, 5);
  let TeamMembersPage;
  module1.link("./TeamMembersPage", {
    TeamMembersPage(v) {
      TeamMembersPage = v;
    }
  }, 6);
  let Header;
  module1.link("../components/Header", {
    Header(v) {
      Header = v;
    }
  }, 7);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$(),
    _s2 = $RefreshSig$();
  const TeamDashboardHome = () => {
    var _user$profile;
    _s();
    const {
      tasks,
      isLoading,
      user
    } = useTracker(() => {
      const tasksHandle = Meteor.subscribe('tasks');
      const userHandle = Meteor.subscribe('userData');
      const user = Meteor.user();
      const tasks = Tasks.find({
        assignedTo: user === null || user === void 0 ? void 0 : user._id
      }, {
        sort: {
          createdAt: -1
        }
      }).fetch();
      return {
        tasks,
        user,
        isLoading: !tasksHandle.ready() || !userHandle.ready()
      };
    });
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }
    const pendingTasks = tasks.filter(task => task.status === 'pending').length;
    const inProgressTasks = tasks.filter(task => task.status === 'in-progress').length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: '0 0 24px 0',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Welcome, ", (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.firstName) || 'Team Member', "!"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px'
      }
    }, "\uD83D\uDCCB")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "My Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View and manage your assigned tasks"), /*#__PURE__*/React.createElement(Link, {
      to: "/team-dashboard/tasks",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "View Tasks")), /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px'
      }
    }, "\uD83D\uDC65")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "My Team"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View your team members and collaborators"), /*#__PURE__*/React.createElement(Link, {
      to: "/team-dashboard/team",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "View Team"))), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '12px',
        padding: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '24px',
        fontWeight: '600'
      }
    }, "Task Overview"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '4px'
      }
    }, "Pending Tasks"), /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, pendingTasks)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '4px'
      }
    }, "In Progress"), /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, inProgressTasks)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '4px'
      }
    }, "Completed Tasks"), /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, completedTasks))))));
  };
  _s(TeamDashboardHome, "F+yCUgZLHn51yhMWFM7HnexYYgg=", false, function () {
    return [useTracker];
  });
  _c = TeamDashboardHome;
  const TeamDashboard = () => {
    _s2();
    const navigate = useNavigate();
    const handleLogout = () => {
      Meteor.logout(err => {
        if (err) {
          console.error('Logout error:', err);
        } else {
          navigate('/login');
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        minHeight: '100vh',
        backgroundColor: '#ffffff'
      }
    }, /*#__PURE__*/React.createElement(Header, {
      userRole: "team-member"
    }), /*#__PURE__*/React.createElement(Routes, null, /*#__PURE__*/React.createElement(Route, {
      path: "/",
      element: /*#__PURE__*/React.createElement(TeamDashboardHome, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks",
      element: /*#__PURE__*/React.createElement(TeamTasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks/:taskId",
      element: /*#__PURE__*/React.createElement(TeamTasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team",
      element: /*#__PURE__*/React.createElement(TeamMembersPage, null)
    })));
  };
  _s2(TeamDashboard, "CzcTeTziyjMsSrAVmHuCCb6+Bfg=", false, function () {
    return [useNavigate];
  });
  _c2 = TeamDashboard;
  var _c, _c2;
  $RefreshReg$(_c, "TeamDashboardHome");
  $RefreshReg$(_c2, "TeamDashboard");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamMembersPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TeamMembersPage.jsx                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamMembersPage: () => TeamMembersPage
  });
  let React, useState;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useNavigate;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    }
  }, 2);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 3);
  let Tasks;
  module1.link("/imports/api/tasks", {
    Tasks(v) {
      Tasks = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TeamMembersPage = () => {
    _s();
    const navigate = useNavigate();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedMemberId, setSelectedMemberId] = useState(null);
    const {
      teamMembers,
      tasks,
      isLoading,
      currentUser
    } = useTracker(() => {
      const teamMembersHandle = Meteor.subscribe('teamMembers');
      const tasksHandle = Meteor.subscribe('tasks');
      const userHandle = Meteor.subscribe('userData');
      const currentUser = Meteor.user();
      const tasks = Tasks.find({}, {
        sort: {
          createdAt: -1
        }
      }).fetch();

      // Get all tasks that the current user is assigned to
      const userTasks = Tasks.find({
        assignedTo: currentUser === null || currentUser === void 0 ? void 0 : currentUser._id
      }).fetch();

      // Get unique team member IDs from those tasks
      const teamMemberIds = new Set();
      userTasks.forEach(task => {
        task.assignedTo.forEach(memberId => {
          if (memberId !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser._id)) {
            teamMemberIds.add(memberId);
          }
        });
      });

      // Fetch team members who share tasks with the current user
      const members = Meteor.users.find({
        _id: {
          $in: Array.from(teamMemberIds)
        }
      }, {
        fields: {
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      }).fetch();
      return {
        teamMembers: members,
        tasks,
        currentUser,
        isLoading: !teamMembersHandle.ready() || !tasksHandle.ready() || !userHandle.ready()
      };
    });
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }
    const filteredMembers = teamMembers.filter(member => {
      var _member$profile, _member$profile2, _member$emails, _member$emails$, _member$emails$$addre;
      const fullName = "".concat(((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '', " ").concat(((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '').toLowerCase();
      const email = ((_member$emails = member.emails) === null || _member$emails === void 0 ? void 0 : (_member$emails$ = _member$emails[0]) === null || _member$emails$ === void 0 ? void 0 : (_member$emails$$addre = _member$emails$.address) === null || _member$emails$$addre === void 0 ? void 0 : _member$emails$$addre.toLowerCase()) || '';
      return searchQuery === '' || fullName.includes(searchQuery.toLowerCase()) || email.includes(searchQuery.toLowerCase());
    });

    // Get shared tasks, attachments, and links for a member
    const getMemberContributions = memberId => {
      // Get tasks that both the current user and the member are assigned to
      const sharedTasks = tasks.filter(task => task.assignedTo.includes(memberId) && task.assignedTo.includes(currentUser === null || currentUser === void 0 ? void 0 : currentUser._id));
      const attachments = sharedTasks.flatMap(task => (task.attachments || []).filter(attachment => attachment.uploadedBy === memberId));
      const links = sharedTasks.flatMap(task => (task.links || []).filter(link => link.addedBy === memberId));
      return {
        sharedTasks,
        attachments,
        links
      };
    };
    const handleMemberClick = memberId => {
      setSelectedMemberId(selectedMemberId === memberId ? null : memberId);
    };
    const handleAttachmentClick = (e, attachment) => {
      e.stopPropagation(); // Prevent card click event
      try {
        // Remove data URL prefix if present
        let base64Data = attachment.data;
        if (base64Data.startsWith('data:')) {
          base64Data = base64Data.split(',')[1];
        }

        // Create a blob from the base64 data
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        // Get the correct MIME type
        const mimeType = attachment.type || 'application/octet-stream';
        const blob = new Blob([byteArray], {
          type: mimeType
        });

        // Create a download link and trigger it
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = attachment.name;
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);
      } catch (error) {
        console.error('Error downloading file:', error);
        alert('Failed to download file. Please try again.');
      }
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Team Members")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search team members...",
      value: searchQuery,
      onChange: e => setSearchQuery(e.target.value),
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    }))), filteredMembers.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px',
        width: '100%'
      },
      className: "team-member-grid"
    }, filteredMembers.map(member => {
      var _member$profile3, _member$profile4, _member$emails2, _member$emails2$, _member$profile5;
      const {
        sharedTasks,
        attachments,
        links
      } = getMemberContributions(member._id);
      const isSelected = selectedMemberId === member._id;
      return /*#__PURE__*/React.createElement("div", {
        key: member._id,
        onClick: () => handleMemberClick(member._id),
        style: {
          backgroundColor: '#ffffff',
          border: '1px solid #e2e8f0',
          borderRadius: '12px',
          padding: '24px',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
          cursor: 'pointer',
          ':hover': {
            borderColor: '#16a34a',
            transform: 'translateY(-2px)'
          }
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
        style: {
          margin: 0,
          fontSize: '1.1rem',
          fontWeight: '600',
          color: '#0f172a'
        }
      }, (_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : _member$profile3.firstName, " ", (_member$profile4 = member.profile) === null || _member$profile4 === void 0 ? void 0 : _member$profile4.lastName), /*#__PURE__*/React.createElement("p", {
        style: {
          margin: '4px 0 0',
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, (_member$emails2 = member.emails) === null || _member$emails2 === void 0 ? void 0 : (_member$emails2$ = _member$emails2[0]) === null || _member$emails2$ === void 0 ? void 0 : _member$emails2$.address)), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569'
        }
      }, ((_member$profile5 = member.profile) === null || _member$profile5 === void 0 ? void 0 : _member$profile5.role) || 'Team Member')), sharedTasks.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: '16px'
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Shared Tasks"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '4px'
        }
      }, sharedTasks.map(task => /*#__PURE__*/React.createElement("div", {
        key: task._id,
        style: {
          padding: '6px 8px',
          backgroundColor: '#f8fafc',
          borderRadius: '6px',
          fontSize: '0.875rem',
          color: '#0f172a',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: '100%'
        }
      }, task.title)))), isSelected && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: '16px'
        }
      }, attachments.length > 1 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '16px'
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "All Files"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }
      }, attachments.slice(1).map((attachment, index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        onClick: e => handleAttachmentClick(e, attachment),
        style: {
          padding: '8px',
          backgroundColor: '#f8fafc',
          borderRadius: '6px',
          fontSize: '0.875rem',
          color: '#0f172a',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          cursor: 'pointer'
        }
      }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCCE"), attachment.name)))), links.length > 1 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '16px'
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "All Links"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }
      }, links.slice(1).map((link, index) => /*#__PURE__*/React.createElement("a", {
        key: index,
        href: link.url,
        target: "_blank",
        rel: "noopener noreferrer",
        onClick: e => e.stopPropagation(),
        style: {
          padding: '8px',
          backgroundColor: '#f8fafc',
          borderRadius: '6px',
          fontSize: '0.875rem',
          color: '#0f172a',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          textDecoration: 'none',
          cursor: 'pointer'
        }
      }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDD17"), link.url))))));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        textAlign: 'center',
        color: '#475569'
      }
    }, searchQuery ? 'No team members match your search' : 'No team members share tasks with you yet')));
  };
  _s(TeamMembersPage, "NPArPGcOg4DCRnm96SeCCnjTQy4=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = TeamMembersPage;
  var _c;
  $RefreshReg$(_c, "TeamMembersPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamTasksPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TeamTasksPage.jsx                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamTasksPage: () => TeamTasksPage
  });
  let React, useState, useEffect;
  module1.link("react", {
    default(v) {
      React = v;
    },
    useState(v) {
      useState = v;
    },
    useEffect(v) {
      useEffect = v;
    }
  }, 0);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 1);
  let useNavigate, useParams, useLocation;
  module1.link("react-router-dom", {
    useNavigate(v) {
      useNavigate = v;
    },
    useParams(v) {
      useParams = v;
    },
    useLocation(v) {
      useLocation = v;
    }
  }, 2);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 3);
  let Tasks;
  module1.link("/imports/api/tasks", {
    Tasks(v) {
      Tasks = v;
    }
  }, 4);
  let TeamTaskView;
  module1.link("../components/TeamTaskView", {
    TeamTaskView(v) {
      TeamTaskView = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  const TeamTasksPage = () => {
    _s();
    const navigate = useNavigate();
    const location = useLocation();
    const {
      taskId
    } = useParams();
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [priorityFilter, setPriorityFilter] = useState('all');
    const {
      tasks,
      isLoading,
      user,
      teamMembers
    } = useTracker(() => {
      const tasksHandle = Meteor.subscribe('tasks');
      const userHandle = Meteor.subscribe('userData');
      const teamMembersHandle = Meteor.subscribe('teamMembers');
      const user = Meteor.user();
      const tasks = Tasks.find({
        assignedTo: user === null || user === void 0 ? void 0 : user._id
      }, {
        sort: {
          createdAt: -1
        }
      }).fetch();
      const teamMembers = Meteor.users.find({}, {
        fields: {
          _id: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.email': 1
        }
      }).fetch();
      return {
        tasks,
        user,
        teamMembers,
        isLoading: !tasksHandle.ready() || !userHandle.ready() || !teamMembersHandle.ready()
      };
    });
    const userNames = {};
    teamMembers === null || teamMembers === void 0 ? void 0 : teamMembers.forEach(member => {
      var _member$profile, _member$profile2, _member$profile3, _member$profile3$emai;
      const firstName = ((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '';
      const lastName = ((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '';
      userNames[member._id] = "".concat(firstName, " ").concat(lastName).trim() || ((_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : (_member$profile3$emai = _member$profile3.email) === null || _member$profile3$emai === void 0 ? void 0 : _member$profile3$emai.split('@')[0]) || 'Unknown';
    });
    const currentTask = taskId ? tasks.find(t => t._id === taskId) : null;
    if (currentTask) {
      return /*#__PURE__*/React.createElement(TeamTaskView, {
        task: currentTask,
        onCancel: () => navigate('/team-dashboard/tasks')
      });
    }
    const filteredTasks = tasks.filter(task => {
      const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) || task.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
      const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
      return matchesSearch && matchesStatus && matchesPriority;
    });
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "My Tasks")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search tasks...",
      value: searchQuery,
      onChange: e => setSearchQuery(e.target.value),
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    })), /*#__PURE__*/React.createElement("select", {
      value: statusFilter,
      onChange: e => setStatusFilter(e.target.value),
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '150px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Status"), /*#__PURE__*/React.createElement("option", {
      value: "pending"
    }, "Pending"), /*#__PURE__*/React.createElement("option", {
      value: "in-progress"
    }, "In Progress"), /*#__PURE__*/React.createElement("option", {
      value: "completed"
    }, "Completed")), /*#__PURE__*/React.createElement("select", {
      value: priorityFilter,
      onChange: e => setPriorityFilter(e.target.value),
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '150px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Priority"), /*#__PURE__*/React.createElement("option", {
      value: "high"
    }, "High"), /*#__PURE__*/React.createElement("option", {
      value: "medium"
    }, "Medium"), /*#__PURE__*/React.createElement("option", {
      value: "low"
    }, "Low"))), isLoading ? /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "loading-spinner"
    })) : filteredTasks.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px',
        padding: '16px 0'
      }
    }, filteredTasks.map(task => {
      var _task$checklist, _task$checklist2;
      const completed = ((_task$checklist = task.checklist) === null || _task$checklist === void 0 ? void 0 : _task$checklist.filter(item => item.completed).length) || 0;
      const total = ((_task$checklist2 = task.checklist) === null || _task$checklist2 === void 0 ? void 0 : _task$checklist2.length) || 0;
      const progress = total > 0 ? Math.round(completed / total * 100) : 0;
      return /*#__PURE__*/React.createElement("div", {
        key: task._id,
        onClick: () => navigate("/team-dashboard/tasks/".concat(task._id)),
        style: {
          backgroundColor: '#ffffff',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '16px',
          cursor: 'pointer'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
        style: {
          margin: 0,
          fontSize: '1.1rem',
          fontWeight: '600',
          color: '#0f172a'
        }
      }, task.title), /*#__PURE__*/React.createElement("p", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem',
          marginBottom: '12px',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxHeight: '2.6em'
        }
      }, task.description))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          marginBottom: '8px',
          flexWrap: 'wrap'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef3c7' : '#dcfce7',
          color: task.priority === 'high' ? '#dc2626' : task.priority === 'medium' ? '#d97706' : '#16a34a'
        }
      }, task.priority), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in-progress' ? '#fef3c7' : '#f1f5f9',
          color: task.status === 'completed' ? '#16a34a' : task.status === 'in-progress' ? '#d97706' : '#475569'
        }
      }, task.status), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "Due:"), /*#__PURE__*/React.createElement("span", {
        style: {
          color: '#0f172a'
        }
      }, new Date(task.dueDate).toLocaleDateString())), total > 0 && /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "Progress:"), /*#__PURE__*/React.createElement("span", {
        style: {
          color: '#0f172a'
        }
      }, progress, "%"))), task.assignedTo && task.assignedTo.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginBottom: '8px',
          flexWrap: 'wrap'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b'
        }
      }, "Team:"), task.assignedTo.slice(0, 3).map(userId => /*#__PURE__*/React.createElement("span", {
        key: userId,
        style: {
          fontSize: '0.85rem',
          padding: '2px 8px',
          borderRadius: '4px',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          fontWeight: 500
        }
      }, userNames[userId] || 'Unknown')), task.assignedTo.length > 3 && /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b'
        }
      }, "+", task.assignedTo.length - 3, " more")), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: '8px',
          padding: '8px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "Checklist Progress:"), /*#__PURE__*/React.createElement("span", {
        style: {
          color: '#0f172a',
          fontWeight: '500'
        }
      }, completed, "/", total)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '4px'
        }
      }, task.checklist.slice(0, 3).map((item, index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '0.75rem',
          color: '#64748b'
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "checkbox",
        checked: item.completed,
        disabled: true
      }), /*#__PURE__*/React.createElement("span", {
        style: {
          textDecoration: item.completed ? 'line-through' : 'none'
        }
      }, item.text))))));
    })) : /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        color: '#64748b',
        padding: '24px'
      }
    }, "No tasks found.")));
  };
  _s(TeamTasksPage, "WgKy44ipTVWXTbwc1iNrDYm2hdA=", false, function () {
    return [useNavigate, useLocation, useParams, useTracker];
  });
  _c = TeamTasksPage;
  var _c;
  $RefreshReg$(_c, "TeamTasksPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"App.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/App.jsx                                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    App: () => App
  });
  let React;
  module1.link("react", {
    default(v) {
      React = v;
    }
  }, 0);
  let Routes, Route, Navigate, useLocation;
  module1.link("react-router-dom", {
    Routes(v) {
      Routes = v;
    },
    Route(v) {
      Route = v;
    },
    Navigate(v) {
      Navigate = v;
    },
    useLocation(v) {
      useLocation = v;
    }
  }, 1);
  let useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker(v) {
      useTracker = v;
    }
  }, 2);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 3);
  let LoginPage;
  module1.link("./pages/LoginPage", {
    LoginPage(v) {
      LoginPage = v;
    }
  }, 4);
  let SignupPage;
  module1.link("./pages/SignupPage", {
    SignupPage(v) {
      SignupPage = v;
    }
  }, 5);
  let AdminDashboard;
  module1.link("./pages/AdminDashboard", {
    AdminDashboard(v) {
      AdminDashboard = v;
    }
  }, 6);
  let TeamDashboard;
  module1.link("./pages/TeamDashboard", {
    TeamDashboard(v) {
      TeamDashboard = v;
    }
  }, 7);
  let TeamTasksPage;
  module1.link("./pages/TeamTasksPage", {
    TeamTasksPage(v) {
      TeamTasksPage = v;
    }
  }, 8);
  let TeamMembersPage;
  module1.link("./pages/TeamMembersPage", {
    TeamMembersPage(v) {
      TeamMembersPage = v;
    }
  }, 9);
  let TaskDetailPage;
  module1.link("./pages/TaskDetailPage", {
    TaskDetailPage(v) {
      TaskDetailPage = v;
    }
  }, 10);
  let ForgotPasswordPage;
  module1.link("./pages/ForgotPasswordPage", {
    ForgotPasswordPage(v) {
      ForgotPasswordPage = v;
    }
  }, 11);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$(),
    _s2 = $RefreshSig$();
  // Protected Route component
  const ProtectedRoute = _ref => {
    let {
      children,
      allowedRoles
    } = _ref;
    _s();
    const location = useLocation();
    const {
      user,
      userRole,
      isLoading
    } = useTracker(() => {
      var _user$profile, _user$roles;
      const subscription = Meteor.subscribe('userData');
      const user = Meteor.user();
      const userRole = (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);
      return {
        user,
        userRole,
        isLoading: !subscription.ready()
      };
    }, []);
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", null, "Loading...");
    }
    if (!user) {
      return /*#__PURE__*/React.createElement(Navigate, {
        to: "/login",
        state: {
          from: location
        },
        replace: true
      });
    }
    if (allowedRoles && !allowedRoles.includes(userRole)) {
      return /*#__PURE__*/React.createElement(Navigate, {
        to: userRole === 'admin' ? '/admin-dashboard' : '/team-dashboard',
        replace: true
      });
    }
    return children;
  };
  _s(ProtectedRoute, "mLGqI+cBY0PttkscNXiFj6AZFEU=", false, function () {
    return [useLocation, useTracker];
  });
  _c = ProtectedRoute;
  const App = () => {
    var _user$profile2, _user$profile3, _user$profile4;
    _s2();
    const {
      user,
      isLoading
    } = useTracker(() => {
      const subscription = Meteor.subscribe('userData');
      return {
        user: Meteor.user(),
        isLoading: !subscription.ready()
      };
    }, []);
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", null, "Loading...");
    }
    return /*#__PURE__*/React.createElement("div", {
      className: "container"
    }, /*#__PURE__*/React.createElement(Routes, null, /*#__PURE__*/React.createElement(Route, {
      path: "/login",
      element: user ? /*#__PURE__*/React.createElement(Navigate, {
        to: ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard',
        replace: true
      }) : /*#__PURE__*/React.createElement(LoginPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/signup",
      element: user ? /*#__PURE__*/React.createElement(Navigate, {
        to: ((_user$profile3 = user.profile) === null || _user$profile3 === void 0 ? void 0 : _user$profile3.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard',
        replace: true
      }) : /*#__PURE__*/React.createElement(SignupPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/admin-dashboard/*",
      element: /*#__PURE__*/React.createElement(ProtectedRoute, {
        allowedRoles: ['admin']
      }, /*#__PURE__*/React.createElement(AdminDashboard, null))
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team-dashboard/*",
      element: /*#__PURE__*/React.createElement(ProtectedRoute, {
        allowedRoles: ['team-member']
      }, /*#__PURE__*/React.createElement(TeamDashboard, null))
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/forgot-password",
      element: /*#__PURE__*/React.createElement(ForgotPasswordPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/",
      element: /*#__PURE__*/React.createElement(Navigate, {
        to: user ? ((_user$profile4 = user.profile) === null || _user$profile4 === void 0 ? void 0 : _user$profile4.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard' : '/login',
        replace: true
      })
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks/:taskId",
      element: /*#__PURE__*/React.createElement(TaskDetailPage, null)
    })));
  };
  _s2(App, "FQsDSDpz35b7IMQ9/L3SUoeMc8w=", false, function () {
    return [useTracker];
  });
  _c2 = App;
  var _c, _c2;
  $RefreshReg$(_c, "ProtectedRoute");
  $RefreshReg$(_c2, "App");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"client":{"main.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// client/main.jsx                                                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  let React;
  module1.link("react", {
    default(v) {
      React = v;
    }
  }, 0);
  let createRoot;
  module1.link("react-dom/client", {
    createRoot(v) {
      createRoot = v;
    }
  }, 1);
  let BrowserRouter;
  module1.link("react-router-dom", {
    BrowserRouter(v) {
      BrowserRouter = v;
    }
  }, 2);
  let Meteor;
  module1.link("meteor/meteor", {
    Meteor(v) {
      Meteor = v;
    }
  }, 3);
  let App;
  module1.link("/imports/ui/App", {
    App(v) {
      App = v;
    }
  }, 4);
  module1.link("/imports/api");
  ___INIT_METEOR_FAST_REFRESH(module);
  // Import collections

  // Add some CSS for the loading spinner
  const style = document.createElement('style');
  style.textContent = "\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n";
  document.head.appendChild(style);

  // Enable hot reloading in development
  if (module.hot) {
    module.hot.accept();
  }
  Meteor.startup(() => {
    const container = document.getElementById('react-target');
    const root = createRoot(container);
    root.render(/*#__PURE__*/React.createElement(BrowserRouter, null, /*#__PURE__*/React.createElement(App, null)));
  });
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".html",
    ".ts",
    ".mjs",
    ".css",
    ".tsx",
    ".jsx"
  ]
});

require("/client/main.jsx");