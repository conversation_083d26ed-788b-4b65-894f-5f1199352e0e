{"version": 3, "sources": ["meteor://💻app/packages/shell-server/main.js", "meteor://💻app/packages/shell-server/shell-server.js"], "names": ["module", "link", "listen", "v", "__reifyWaitForDeps__", "shellDir", "process", "env", "METEOR_SHELL_DIR", "__reify_async_result__", "_reifyError", "self", "async", "module1", "export", "disable", "assert", "default", "pathJoin", "join", "PassThrough", "closeSync", "openSync", "readFileSync", "unlink", "writeFileSync", "writeSync", "createServer", "replStart", "start", "INFO_FILE_MODE", "parseInt", "EXITING_MESSAGE", "callback", "Server", "Meteor", "startup", "__meteor_bootstrap__", "hooks", "startupHooks", "push", "setImmediate", "getInfoFile", "JSON", "stringify", "status", "reason", "mode", "ignored", "evalCommandPromise", "Promise", "resolve", "constructor", "ok", "key", "Math", "random", "toString", "slice", "server", "socket", "onConnection", "on", "err", "console", "error", "stack", "infoFile", "port", "address", "timeout", "setTimeout", "removeAllListeners", "end", "readJSONFromStream", "options", "replInputSocket", "clearTimeout", "columns", "Object", "assign", "create", "prompt", "terminal", "useColors", "ignoreUndefined", "input", "useGlobal", "output", "evaluateAndExit", "startREPL", "_wrappedDefaultEval", "call", "command", "global", "filename", "result", "sendResultToSocket", "message", "code", "enableInteractiveMode", "repl", "context", "setRequireAndModule", "defaultEval", "eval", "wrappedDefaultEval", "file", "Package", "<PERSON>l", "compileForShell", "cacheDirectory", "getCacheDirectory", "then", "resolvedResult", "catch", "rejectedError", "initializeHistory", "defineProperty", "get", "last", "set", "val", "configurable", "addHelp", "cmd", "helpText", "info", "commands", "help", "write", "defineCommand", "action", "sendMessage", "exit", "historyFile", "getHistoryFile", "historyFd", "historyLines", "split", "seenLines", "history", "historyIndex", "length", "line", "pop", "test", "addListener", "inputStream", "outputStream", "dataSoFar", "onData", "buffer", "lines", "shift", "json", "parse", "SyntaxError", "finish", "pipe", "onClose", "Error", "finished", "removeListener", "modules", "toBeInstalled", "shellModuleName", "require", "exports", "extensions", "meteorInstall"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;MAAC,GAAG,EAAC;IAAG,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACF,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;MAACC,MAAMA,CAACC,CAAC,EAAC;QAACD,MAAM,GAACC,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAG7K,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB;IAC7C,IAAIH,QAAQ,EAAE;MACZH,MAAM,CAACG,QAAQ,CAAC;IAClB;IAACI,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICNDC,OAAO,CAACC,MAAM,CAAC;MAACZ,MAAM,EAACA,CAAA,KAAIA,MAAM;MAACa,OAAO,EAACA,CAAA,KAAIA;IAAO,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACH,OAAO,CAACZ,IAAI,CAAC,QAAQ,EAAC;MAACgB,OAAOA,CAACd,CAAC,EAAC;QAACa,MAAM,GAACb,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIe,QAAQ;IAACL,OAAO,CAACZ,IAAI,CAAC,MAAM,EAAC;MAACkB,IAAIA,CAAChB,CAAC,EAAC;QAACe,QAAQ,GAACf,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIiB,WAAW;IAACP,OAAO,CAACZ,IAAI,CAAC,QAAQ,EAAC;MAACmB,WAAWA,CAACjB,CAAC,EAAC;QAACiB,WAAW,GAACjB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIkB,SAAS,EAACC,QAAQ,EAACC,YAAY,EAACC,MAAM,EAACC,aAAa,EAACC,SAAS;IAACb,OAAO,CAACZ,IAAI,CAAC,IAAI,EAAC;MAACoB,SAASA,CAAClB,CAAC,EAAC;QAACkB,SAAS,GAAClB,CAAC;MAAA,CAAC;MAACmB,QAAQA,CAACnB,CAAC,EAAC;QAACmB,QAAQ,GAACnB,CAAC;MAAA,CAAC;MAACoB,YAAYA,CAACpB,CAAC,EAAC;QAACoB,YAAY,GAACpB,CAAC;MAAA,CAAC;MAACqB,MAAMA,CAACrB,CAAC,EAAC;QAACqB,MAAM,GAACrB,CAAC;MAAA,CAAC;MAACsB,aAAaA,CAACtB,CAAC,EAAC;QAACsB,aAAa,GAACtB,CAAC;MAAA,CAAC;MAACuB,SAASA,CAACvB,CAAC,EAAC;QAACuB,SAAS,GAACvB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIwB,YAAY;IAACd,OAAO,CAACZ,IAAI,CAAC,KAAK,EAAC;MAAC0B,YAAYA,CAACxB,CAAC,EAAC;QAACwB,YAAY,GAACxB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIyB,SAAS;IAACf,OAAO,CAACZ,IAAI,CAAC,MAAM,EAAC;MAAC4B,KAAKA,CAAC1B,CAAC,EAAC;QAACyB,SAAS,GAACzB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAACU,OAAO,CAACZ,IAAI,CAAC,gCAAgC,CAAC;IAAC,IAAIG,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAiBnuB,MAAM0B,cAAc,GAAGC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMC,eAAe,GAAG,kBAAkB;;IAE1C;IACA;IACO,SAAS9B,MAAMA,CAACG,QAAQ,EAAE;MAC/B,SAAS4B,QAAQA,CAAA,EAAG;QAClB,IAAIC,MAAM,CAAC7B,QAAQ,CAAC,CAACH,MAAM,CAAC,CAAC;MAC/B;;MAEA;MACA;MACA,IAAI,OAAOiC,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,CAACC,OAAO,CAACH,QAAQ,CAAC;MAC1B,CAAC,MAAM,IAAI,OAAOI,oBAAoB,KAAK,QAAQ,EAAE;QACnD,MAAMC,KAAK,GAAGD,oBAAoB,CAACE,YAAY;QAC/C,IAAID,KAAK,EAAE;UACTA,KAAK,CAACE,IAAI,CAACP,QAAQ,CAAC;QACtB,CAAC,MAAM;UACL;UACAQ,YAAY,CAACR,QAAQ,CAAC;QACxB;MACF;IACF;IAGO,SAASlB,OAAOA,CAACV,QAAQ,EAAE;MAChC,IAAI;QACF;QACA;QACA;QACAoB,aAAa,CACXiB,WAAW,CAACrC,QAAQ,CAAC,EACrBsC,IAAI,CAACC,SAAS,CAAC;UACbC,MAAM,EAAE,UAAU;UAClBC,MAAM,EAAE;QACV,CAAC,CAAC,GAAG,IAAI,EACT;UAAEC,IAAI,EAAEjB;QAAe,CACzB,CAAC;MACH,CAAC,CAAC,OAAOkB,OAAO,EAAE,CAAC;IACrB;IAEA;IACA;IACA;IACA,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;IAE5C,MAAMjB,MAAM,CAAC;MACXkB,WAAWA,CAAC/C,QAAQ,EAAE;QACpBW,MAAM,CAACqC,EAAE,CAAC,IAAI,YAAYnB,MAAM,CAAC;QAEjC,IAAI,CAAC7B,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACiD,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;QAE9C,IAAI,CAACC,MAAM,GACThC,YAAY,CAAEiC,MAAM,IAAK;UACvB,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;QAC3B,CAAC,CAAC,CACDE,EAAE,CAAC,OAAO,EAAGC,GAAG,IAAK;UACpBC,OAAO,CAACC,KAAK,CAACF,GAAG,CAACG,KAAK,CAAC;QAC1B,CAAC,CAAC;MACN;MAEAhE,MAAMA,CAAA,EAAG;QACP,MAAMiE,QAAQ,GAAGzB,WAAW,CAAC,IAAI,CAACrC,QAAQ,CAAC;QAE3CmB,MAAM,CAAC2C,QAAQ,EAAE,MAAM;UACrB,IAAI,CAACR,MAAM,CAACzD,MAAM,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM;YACvCuB,aAAa,CAAC0C,QAAQ,EAAExB,IAAI,CAACC,SAAS,CAAC;cACrCC,MAAM,EAAE,SAAS;cACjBuB,IAAI,EAAE,IAAI,CAACT,MAAM,CAACU,OAAO,CAAC,CAAC,CAACD,IAAI;cAChCd,GAAG,EAAE,IAAI,CAACA;YACZ,CAAC,CAAC,GAAG,IAAI,EAAE;cACTP,IAAI,EAAEjB;YACR,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA+B,YAAYA,CAACD,MAAM,EAAE;QACnB;QACA;QACAA,MAAM,CAACE,EAAE,CAAC,OAAO,EAAE,YAAW;UAC5BF,MAAM,GAAG,IAAI;QACf,CAAC,CAAC;;QAEF;QACA;QACA,MAAMU,OAAO,GAAGC,UAAU,CAAC,YAAW;UACpC,IAAIX,MAAM,EAAE;YACVA,MAAM,CAACY,kBAAkB,CAAC,MAAM,CAAC;YACjCZ,MAAM,CAACa,GAAG,CAACzC,eAAe,GAAG,IAAI,CAAC;UACpC;QACF,CAAC,EAAE,IAAI,CAAC;;QAER;QACA;QACA;QACA;QACA0C,kBAAkB,CAACd,MAAM,EAAE,CAACK,KAAK,EAAEU,OAAO,EAAEC,eAAe,KAAK;UAC9DC,YAAY,CAACP,OAAO,CAAC;UAErB,IAAIL,KAAK,EAAE;YACTL,MAAM,GAAG,IAAI;YACbI,OAAO,CAACC,KAAK,CAACA,KAAK,CAACC,KAAK,CAAC;YAC1B;UACF;UAEA,IAAIS,OAAO,CAACrB,GAAG,KAAK,IAAI,CAACA,GAAG,EAAE;YAC5B,IAAIM,MAAM,EAAE;cACVA,MAAM,CAACa,GAAG,CAACzC,eAAe,GAAG,IAAI,CAAC;YACpC;YACA;UACF;UACA,OAAO2C,OAAO,CAACrB,GAAG;;UAElB;UACA,IAAIqB,OAAO,CAACG,OAAO,IAAIlB,MAAM,EAAE;YAC7BA,MAAM,CAACkB,OAAO,GAAGH,OAAO,CAACG,OAAO;UAClC;UACA,OAAOH,OAAO,CAACG,OAAO;UAEtBH,OAAO,GAAGI,MAAM,CAACC,MAAM,CACrBD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC;UAEnB;UACA;YACEC,MAAM,EAAE,IAAI;YACZC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE,IAAI;YACfC,eAAe,EAAE;UACnB,CAAC;UAED;UACAV,OAAO;UAEP;UACA;YACEW,KAAK,EAAEV,eAAe;YACtBW,SAAS,EAAE,KAAK;YAChBC,MAAM,EAAE5B;UACV,CACF,CAAC;;UAED;UACA;UACA;UACA,IAAIe,OAAO,CAACc,eAAe,EAAE;YAC3Bd,OAAO,CAACO,MAAM,GAAG,EAAE;UACrB;;UAEA;UACA,IAAI,CAACQ,SAAS,CAACf,OAAO,CAAC;UAEvB,IAAIA,OAAO,CAACc,eAAe,EAAE;YAC3B,IAAI,CAACE,mBAAmB,CAACC,IAAI,CAC3Bb,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC,EACnBN,OAAO,CAACc,eAAe,CAACI,OAAO,EAC/BC,MAAM,EACNnB,OAAO,CAACc,eAAe,CAACM,QAAQ,IAAI,gBAAgB,EACpD,UAAU9B,KAAK,EAAE+B,MAAM,EAAE;cACvB,IAAIpC,MAAM,EAAE;gBACV,SAASqC,kBAAkBA,CAACC,OAAO,EAAE;kBACnC;kBACA;kBACAtC,MAAM,CAACa,GAAG,CAAC9B,IAAI,CAACC,SAAS,CAACsD,OAAO,CAAC,GAAG,IAAI,CAAC;gBAC5C;gBAEA,IAAIjC,KAAK,EAAE;kBACTgC,kBAAkB,CAAC;oBACjBhC,KAAK,EAAEA,KAAK,CAACR,QAAQ,CAAC,CAAC;oBACvB0C,IAAI,EAAE;kBACR,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACLF,kBAAkB,CAAC;oBACjBD;kBACF,CAAC,CAAC;gBACJ;cACF;YACF,CACF,CAAC;YACD;UACF;UACA,OAAOrB,OAAO,CAACc,eAAe;UAE9B,IAAI,CAACW,qBAAqB,CAACzB,OAAO,CAAC;QACrC,CAAC,CAAC;MACJ;MAEAe,SAASA,CAACf,OAAO,EAAE;QACjB;QACA;QACAA,OAAO,CAACa,MAAM,CAAC1B,EAAE,CAAC,OAAO,EAAE,YAAW;UACpCa,OAAO,CAACa,MAAM,GAAG,IAAI;QACvB,CAAC,CAAC;QAEF,MAAMa,IAAI,GAAG,IAAI,CAACA,IAAI,GAAGzE,SAAS,CAAC+C,OAAO,CAAC;QAC3C,MAAM;UAAEtE;QAAS,CAAC,GAAG,IAAI;;QAEzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAgG,IAAI,CAACC,OAAO,GAAGR,MAAM;QACrBO,IAAI,CAACd,SAAS,GAAG,IAAI;QAErBgB,mBAAmB,CAACF,IAAI,CAACC,OAAO,CAAC;;QAEjC;QACA;QACA;QACA;QACA,MAAME,WAAW,GAAGH,IAAI,CAACI,IAAI;QAE7B,SAASC,kBAAkBA,CAACP,IAAI,EAAEG,OAAO,EAAEK,IAAI,EAAE1E,QAAQ,EAAE;UACzD,IAAI2E,OAAO,CAAC,gBAAgB,CAAC,EAAE;YAC7B,IAAI;cACFT,IAAI,GAAGS,OAAO,CAAC,gBAAgB,CAAC,CAACC,KAAK,CAACC,eAAe,CAACX,IAAI,EAAE;gBAC3DY,cAAc,EAAEC,iBAAiB,CAAC3G,QAAQ;cAC5C,CAAC,CAAC;YACJ,CAAC,CAAC,OAAO0D,GAAG,EAAE;cACZ;cACA;cACA;cACA;YAAA;UAEJ;UAEAd,kBAAkB,CACfgE,IAAI,CAAC,MAAMT,WAAW,CAACL,IAAI,EAAEG,OAAO,EAAEK,IAAI,EAAE,CAAC1C,KAAK,EAAE+B,MAAM,KAAK;YAC9D,IAAI/B,KAAK,EAAE;cACThC,QAAQ,CAACgC,KAAK,CAAC;YACjB,CAAC,MAAM;cACL;cACA,IAAI+B,MAAM,IAAI,OAAOA,MAAM,CAACiB,IAAI,KAAK,UAAU,EAAE;gBAC/C;gBACAjB,MAAM,CACHiB,IAAI,CAACC,cAAc,IAAI;kBACtBjF,QAAQ,CAAC,IAAI,EAAEiF,cAAc,CAAC;gBAChC,CAAC,CAAC,CACDC,KAAK,CAACC,aAAa,IAAI;kBACtBnF,QAAQ,CAACmF,aAAa,CAAC;gBACzB,CAAC,CAAC;cACN,CAAC,MAAM;gBACLnF,QAAQ,CAAC,IAAI,EAAE+D,MAAM,CAAC;cACxB;YACF;UACF,CAAC,CAAC,CAAC,CACFmB,KAAK,CAAClF,QAAQ,CAAC;QACpB;;QAEA;QACA;QACAoE,IAAI,CAACI,IAAI,GAAG,IAAI,CAACd,mBAAmB,GAAGe,kBAAkB;MAC3D;MAEAN,qBAAqBA,CAACzB,OAAO,EAAE;QAC7B;QACA,IAAI,CAAC0C,iBAAiB,CAAC,CAAC;QAExB,MAAMhB,IAAI,GAAG,IAAI,CAACA,IAAI;;QAEtB;QACA;QACA;QACAtB,MAAM,CAACuC,cAAc,CAACjB,IAAI,CAACC,OAAO,EAAE,IAAI,EAAE;UACxCiB,GAAG,EAAEA,CAAA,KAAMlB,IAAI,CAACmB,IAAI;UACpBC,GAAG,EAAGC,GAAG,IAAK;YACZrB,IAAI,CAACmB,IAAI,GAAGE,GAAG;UACjB,CAAC;UAED;UACA;UACAC,YAAY,EAAE;QAChB,CAAC,CAAC;;QAEF;QACA,SAASC,OAAOA,CAACC,GAAG,EAAEC,QAAQ,EAAE;UAC9B,MAAMC,IAAI,GAAG1B,IAAI,CAAC2B,QAAQ,CAACH,GAAG,CAAC,IAAIxB,IAAI,CAAC2B,QAAQ,CAAC,GAAG,GAAGH,GAAG,CAAC;UAC3D,IAAIE,IAAI,EAAE;YACRA,IAAI,CAACE,IAAI,GAAGH,QAAQ;UACtB;QACF;QACAF,OAAO,CAAC,OAAO,EAAE,wDAAwD,CAAC;QAC1EA,OAAO,CAAC,MAAM,EAAE,wCAAwC,CAAC;QACzDA,OAAO,CAAC,MAAM,EAAE,4BAA4B,CAAC;;QAE7C;QACA;QACAvB,IAAI,CAACvC,EAAE,CAAC,MAAM,EAAE,YAAW;UACzB,IAAIa,OAAO,CAACa,MAAM,EAAE;YAClBb,OAAO,CAACa,MAAM,CAAC0C,KAAK,CAAClG,eAAe,GAAG,IAAI,CAAC;YAC5C2C,OAAO,CAACa,MAAM,CAACf,GAAG,CAAC,CAAC;UACtB;QACF,CAAC,CAAC;;QAEF;QACA;QACAnE,OAAO,CAACwD,EAAE,CAAC,MAAM,EAAE,YAAW;UAC5B,IAAIa,OAAO,CAACa,MAAM,EAAE;YAClBb,OAAO,CAACa,MAAM,CAACf,GAAG,CAAC,CAAC;UACtB;QACF,CAAC,CAAC;;QAEF;QACA;QACA4B,IAAI,CAAC8B,aAAa,CAAC,QAAQ,EAAE;UAC3BF,IAAI,EAAE,kCAAkC;UACxCG,MAAM,EAAE,SAAAA,CAAA,EAAW;YACjB,IAAI9H,OAAO,CAAC+H,WAAW,EAAE;cACvB/H,OAAO,CAAC+H,WAAW,CAAC,cAAc,EAAE;gBAAExC,OAAO,EAAE;cAAS,CAAC,CAAC;YAC5D,CAAC,MAAM;cACLvF,OAAO,CAACgI,IAAI,CAAC,CAAC,CAAC;YACjB;UACF;QACF,CAAC,CAAC;MACJ;;MAEA;MACA;MACAjB,iBAAiBA,CAAA,EAAG;QAClB,MAAMhB,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMkC,WAAW,GAAGC,cAAc,CAAC,IAAI,CAACnI,QAAQ,CAAC;QACjD,IAAIoI,SAAS,GAAGnH,QAAQ,CAACiH,WAAW,EAAE,IAAI,CAAC;QAC3C,MAAMG,YAAY,GAAGnH,YAAY,CAACgH,WAAW,EAAE,MAAM,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC;QAClE,MAAMC,SAAS,GAAG7D,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC;QAErC,IAAI,CAAEoB,IAAI,CAACwC,OAAO,EAAE;UAClBxC,IAAI,CAACwC,OAAO,GAAG,EAAE;UACjBxC,IAAI,CAACyC,YAAY,GAAG,CAAC,CAAC;QACxB;QAEA,OAAOzC,IAAI,CAACwC,OAAO,IAAIH,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;UAC9C,MAAMC,IAAI,GAAGN,YAAY,CAACO,GAAG,CAAC,CAAC;UAC/B,IAAID,IAAI,IAAI,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC,IAAI,CAAEJ,SAAS,CAACI,IAAI,CAAC,EAAE;YAChD3C,IAAI,CAACwC,OAAO,CAACrG,IAAI,CAACwG,IAAI,CAAC;YACvBJ,SAAS,CAACI,IAAI,CAAC,GAAG,IAAI;UACxB;QACF;QAEA3C,IAAI,CAAC8C,WAAW,CAAC,MAAM,EAAE,UAASH,IAAI,EAAE;UACtC,IAAIP,SAAS,IAAI,CAAC,IAAI,IAAI,CAACS,IAAI,CAACF,IAAI,CAAC,EAAE;YACrCtH,SAAS,CAAC+G,SAAS,EAAEO,IAAI,GAAG,IAAI,CAAC;UACnC;QACF,CAAC,CAAC;QAEF,IAAI,CAAC3C,IAAI,CAACvC,EAAE,CAAC,MAAM,EAAE,YAAW;UAC9BzC,SAAS,CAACoH,SAAS,CAAC;UACpBA,SAAS,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC;MACJ;IACF;IAEA,SAAS/D,kBAAkBA,CAAC0E,WAAW,EAAEnH,QAAQ,EAAE;MACjD,MAAMoH,YAAY,GAAG,IAAIjI,WAAW,CAAC,CAAC;MACtC,IAAIkI,SAAS,GAAG,EAAE;MAElB,SAASC,MAAMA,CAACC,MAAM,EAAE;QACtB,MAAMC,KAAK,GAAGD,MAAM,CAAC/F,QAAQ,CAAC,MAAM,CAAC,CAACkF,KAAK,CAAC,IAAI,CAAC;QAEjD,OAAOc,KAAK,CAACV,MAAM,GAAG,CAAC,EAAE;UACvBO,SAAS,IAAIG,KAAK,CAACC,KAAK,CAAC,CAAC;UAE1B,IAAIC,IAAI;UACR,IAAI;YACFA,IAAI,GAAGhH,IAAI,CAACiH,KAAK,CAACN,SAAS,CAAC;UAC9B,CAAC,CAAC,OAAOrF,KAAK,EAAE;YACd,IAAIA,KAAK,YAAY4F,WAAW,EAAE;cAChC;YACF;YAEA,OAAOC,MAAM,CAAC7F,KAAK,CAAC;UACtB;UAEA,IAAIwF,KAAK,CAACV,MAAM,GAAG,CAAC,EAAE;YACpBM,YAAY,CAACnB,KAAK,CAACuB,KAAK,CAACtI,IAAI,CAAC,IAAI,CAAC,CAAC;UACtC;UAEAiI,WAAW,CAACW,IAAI,CAACV,YAAY,CAAC;UAE9B,OAAOS,MAAM,CAAC,IAAI,EAAEH,IAAI,CAAC;QAC3B;MACF;MAEA,SAASK,OAAOA,CAAA,EAAG;QACjBF,MAAM,CAAC,IAAIG,KAAK,CAAC,4BAA4B,CAAC,CAAC;MACjD;MAEA,IAAIC,QAAQ,GAAG,KAAK;MACpB,SAASJ,MAAMA,CAAC7F,KAAK,EAAE0F,IAAI,EAAE;QAC3B,IAAI,CAAEO,QAAQ,EAAE;UACdA,QAAQ,GAAG,IAAI;UACfd,WAAW,CAACe,cAAc,CAAC,MAAM,EAAEZ,MAAM,CAAC;UAC1CH,WAAW,CAACe,cAAc,CAAC,OAAO,EAAEL,MAAM,CAAC;UAC3CV,WAAW,CAACe,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;UAC5C/H,QAAQ,CAACgC,KAAK,EAAE0F,IAAI,EAAEN,YAAY,CAAC;QACrC;MACF;MAEAD,WAAW,CAACtF,EAAE,CAAC,MAAM,EAAEyF,MAAM,CAAC;MAC9BH,WAAW,CAACtF,EAAE,CAAC,OAAO,EAAEgG,MAAM,CAAC;MAC/BV,WAAW,CAACtF,EAAE,CAAC,OAAO,EAAEkG,OAAO,CAAC;IAClC;IAEA,SAAStH,WAAWA,CAACrC,QAAQ,EAAE;MAC7B,OAAOa,QAAQ,CAACb,QAAQ,EAAE,WAAW,CAAC;IACxC;IAEA,SAASmI,cAAcA,CAACnI,QAAQ,EAAE;MAChC,OAAOa,QAAQ,CAACb,QAAQ,EAAE,SAAS,CAAC;IACtC;IAEA,SAAS2G,iBAAiBA,CAAC3G,QAAQ,EAAE;MACnC,OAAOa,QAAQ,CAACb,QAAQ,EAAE,OAAO,CAAC;IACpC;IAEA,SAASkG,mBAAmBA,CAACD,OAAO,EAAE;MACpC,IAAIM,OAAO,CAACwD,OAAO,EAAE;QACnB;QACA;QACA,MAAMC,aAAa,GAAG,CAAC,CAAC;QACxB,MAAMC,eAAe,GAAG,eAAe,GACrC/G,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK;QAE7C2G,aAAa,CAACC,eAAe,CAAC,GAAG,UAAUC,OAAO,EAAEC,OAAO,EAAExK,MAAM,EAAE;UACnEsG,OAAO,CAACtG,MAAM,GAAGA,MAAM;UACvBsG,OAAO,CAACiE,OAAO,GAAGA,OAAO;;UAEzB;UACA;UACAA,OAAO,CAACE,UAAU,GAAG;YACnB,KAAK,EAAE,IAAI;YACX,OAAO,EAAE,IAAI;YACb,OAAO,EAAE;UACX,CAAC;QACH,CAAC;;QAED;QACA;QACA7D,OAAO,CAACwD,OAAO,CAACM,aAAa,CAACL,aAAa,CAAC,CAAC,IAAI,GAAGC,eAAe,CAAC;MACtE;IACF;IAAC7J,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/shell-server.js", "sourcesContent": ["export * from \"./shell-server.js\";\nimport { listen } from \"./shell-server.js\";\n\nconst shellDir = process.env.METEOR_SHELL_DIR;\nif (shellDir) {\n  listen(shellDir);\n}\n", "import assert from \"assert\";\nimport { join as pathJoin } from \"path\";\nimport { PassThrough } from \"stream\";\nimport {\n  closeSync,\n  openSync,\n  readFileSync,\n  unlink,\n  writeFileSync,\n  writeSync,\n} from \"fs\";\nimport { createServer } from \"net\";\nimport { start as replStart } from \"repl\";\n\n// Enable process.sendMessage for communication with build process.\nimport \"meteor/inter-process-messaging\";\n\nconst INFO_FILE_MODE = parseInt(\"600\", 8); // Only the owner can read or write.\nconst EXITING_MESSAGE = \"Shell exiting...\";\n\n// Invoked by the server process to listen for incoming connections from\n// shell clients. Each connection gets its own REPL instance.\nexport function listen(shellDir) {\n  function callback() {\n    new Server(shellDir).listen();\n  }\n\n  // If the server is still in the very early stages of starting up,\n  // Meteor.startup may not available yet.\n  if (typeof Meteor === \"object\") {\n    Meteor.startup(callback);\n  } else if (typeof __meteor_bootstrap__ === \"object\") {\n    const hooks = __meteor_bootstrap__.startupHooks;\n    if (hooks) {\n      hooks.push(callback);\n    } else {\n      // As a fallback, just call the callback asynchronously.\n      setImmediate(callback);\n    }\n  }\n}\n\n// Disabling the shell causes all attached clients to disconnect and exit.\nexport function disable(shellDir) {\n  try {\n    // Replace info.json with a file that says the shell server is\n    // disabled, so that any connected shell clients will fail to\n    // reconnect after the server process closes their sockets.\n    writeFileSync(\n      getInfoFile(shellDir),\n      JSON.stringify({\n        status: \"disabled\",\n        reason: \"Shell server has shut down.\"\n      }) + \"\\n\",\n      { mode: INFO_FILE_MODE }\n    );\n  } catch (ignored) {}\n}\n\n// Shell commands need to be executed in a Fiber in case they call into\n// code that yields. Using a Promise is an even better idea, since it runs\n// its callbacks in Fibers drawn from a pool, so the Fibers are recycled.\nconst evalCommandPromise = Promise.resolve();\n\nclass Server {\n  constructor(shellDir) {\n    assert.ok(this instanceof Server);\n\n    this.shellDir = shellDir;\n    this.key = Math.random().toString(36).slice(2);\n\n    this.server =\n      createServer((socket) => {\n        this.onConnection(socket);\n      })\n      .on(\"error\", (err) => {\n        console.error(err.stack);\n      });\n  }\n\n  listen() {\n    const infoFile = getInfoFile(this.shellDir);\n\n    unlink(infoFile, () => {\n      this.server.listen(0, \"127.0.0.1\", () => {\n        writeFileSync(infoFile, JSON.stringify({\n          status: \"enabled\",\n          port: this.server.address().port,\n          key: this.key\n        }) + \"\\n\", {\n          mode: INFO_FILE_MODE\n        });\n      });\n    });\n  }\n\n  onConnection(socket) {\n    // Make sure this function doesn't try to write anything to the socket\n    // after it has been closed.\n    socket.on(\"close\", function() {\n      socket = null;\n    });\n\n    // If communication is not established within 1000ms of the first\n    // connection, forcibly close the socket.\n    const timeout = setTimeout(function() {\n      if (socket) {\n        socket.removeAllListeners(\"data\");\n        socket.end(EXITING_MESSAGE + \"\\n\");\n      }\n    }, 1000);\n\n    // Let connecting clients configure certain REPL options by sending a\n    // JSON object over the socket. For example, only the client knows\n    // whether it's running a TTY or an Emacs subshell or some other kind of\n    // terminal, so the client must decide the value of options.terminal.\n    readJSONFromStream(socket, (error, options, replInputSocket) => {\n      clearTimeout(timeout);\n\n      if (error) {\n        socket = null;\n        console.error(error.stack);\n        return;\n      }\n\n      if (options.key !== this.key) {\n        if (socket) {\n          socket.end(EXITING_MESSAGE + \"\\n\");\n        }\n        return;\n      }\n      delete options.key;\n\n      // Set the columns to what is being requested by the client.\n      if (options.columns && socket) {\n        socket.columns = options.columns;\n      }\n      delete options.columns;\n\n      options = Object.assign(\n        Object.create(null),\n\n        // Defaults for configurable options.\n        {\n          prompt: \"> \",\n          terminal: true,\n          useColors: true,\n          ignoreUndefined: true,\n        },\n\n        // Configurable options\n        options,\n\n        // Immutable options.\n        {\n          input: replInputSocket,\n          useGlobal: false,\n          output: socket\n        }\n      );\n\n      // The prompt during an evaluateAndExit must be blank to ensure\n      // that the prompt doesn't inadvertently get parsed as part of\n      // the JSON communication channel.\n      if (options.evaluateAndExit) {\n        options.prompt = \"\";\n      }\n\n      // Start the REPL.\n      this.startREPL(options);\n\n      if (options.evaluateAndExit) {\n        this._wrappedDefaultEval.call(\n          Object.create(null),\n          options.evaluateAndExit.command,\n          global,\n          options.evaluateAndExit.filename || \"<meteor shell>\",\n          function (error, result) {\n            if (socket) {\n              function sendResultToSocket(message) {\n                // Sending back a JSON payload allows the client to\n                // distinguish between errors and successful results.\n                socket.end(JSON.stringify(message) + \"\\n\");\n              }\n\n              if (error) {\n                sendResultToSocket({\n                  error: error.toString(),\n                  code: 1\n                });\n              } else {\n                sendResultToSocket({\n                  result,\n                });\n              }\n            }\n          }\n        );\n        return;\n      }\n      delete options.evaluateAndExit;\n\n      this.enableInteractiveMode(options);\n    });\n  }\n\n  startREPL(options) {\n    // Make sure this function doesn't try to write anything to the output\n    // stream after it has been closed.\n    options.output.on(\"close\", function() {\n      options.output = null;\n    });\n\n    const repl = this.repl = replStart(options);\n    const { shellDir } = this;\n\n    // This is technique of setting `repl.context` is similar to how the\n    // `useGlobal` option would work during a normal `repl.start()` and\n    // allows shell access (and tab completion!) to Meteor globals (i.e.\n    // Underscore _, Meteor, etc.). By using this technique, which changes\n    // the context after startup, we avoid stomping on the special `_`\n    // variable (in `repl` this equals the value of the last command) from\n    // being overridden in the client/server socket-handshaking.  Furthermore,\n    // by setting `useGlobal` back to true, we allow the default eval function\n    // to use the desired `runInThisContext` method (https://git.io/vbvAB).\n    repl.context = global;\n    repl.useGlobal = true;\n\n    setRequireAndModule(repl.context);\n\n    // In order to avoid duplicating code here, specifically the complexities\n    // of catching so-called \"Recoverable Errors\" (https://git.io/vbvbl),\n    // we will wrap the default eval, run it in a Fiber (via a Promise), and\n    // give it the opportunity to decide if the user is mid-code-block.\n    const defaultEval = repl.eval;\n\n    function wrappedDefaultEval(code, context, file, callback) {\n      if (Package['babel-compiler']) {\n        try {\n          code = Package['babel-compiler'].Babel.compileForShell(code, {\n            cacheDirectory: getCacheDirectory(shellDir)\n          });\n        } catch (err) {\n          // Any Babel error here might be just fine since it's\n          // possible the code was incomplete (multi-line code on the REPL).\n          // The defaultEval below will use its own functionality to determine\n          // if this error is \"recoverable\".\n        }\n      }\n\n      evalCommandPromise\n        .then(() => defaultEval(code, context, file, (error, result) => {\n          if (error) {\n            callback(error);\n          } else {\n            // Check if the result is a Promise\n            if (result && typeof result.then === 'function') {\n              // Handle the Promise resolution and rejection\n              result\n                .then(resolvedResult => {\n                  callback(null, resolvedResult);\n                })\n                .catch(rejectedError => {\n                  callback(rejectedError);\n                });\n            } else {\n              callback(null, result);\n            }\n          }\n        }))\n        .catch(callback);\n    }\n\n    // Have the REPL use the newly wrapped function instead and store the\n    // _wrappedDefaultEval so that evalulateAndExit calls can use it directly.\n    repl.eval = this._wrappedDefaultEval = wrappedDefaultEval;\n  }\n\n  enableInteractiveMode(options) {\n    // History persists across shell sessions!\n    this.initializeHistory();\n\n    const repl = this.repl;\n\n    // Implement an alternate means of fetching the return value,\n    // via `__` (double underscore) as originally implemented in:\n    // https://github.com/meteor/meteor/commit/2443d832265c7d1c\n    Object.defineProperty(repl.context, \"__\", {\n      get: () => repl.last,\n      set: (val) => {\n        repl.last = val;\n      },\n\n      // Allow this property to be (re)defined more than once (e.g. each\n      // time the server restarts).\n      configurable: true\n    });\n\n    // Some improvements to the existing help messages.\n    function addHelp(cmd, helpText) {\n      const info = repl.commands[cmd] || repl.commands[\".\" + cmd];\n      if (info) {\n        info.help = helpText;\n      }\n    }\n    addHelp(\"break\", \"Terminate current command input and display new prompt\");\n    addHelp(\"exit\", \"Disconnect from server and leave shell\");\n    addHelp(\"help\", \"Show this help information\");\n\n    // When the REPL exits, signal the attached client to exit by sending it\n    // the special EXITING_MESSAGE.\n    repl.on(\"exit\", function() {\n      if (options.output) {\n        options.output.write(EXITING_MESSAGE + \"\\n\");\n        options.output.end();\n      }\n    });\n\n    // When the server process exits, end the output stream but do not\n    // signal the attached client to exit.\n    process.on(\"exit\", function() {\n      if (options.output) {\n        options.output.end();\n      }\n    });\n\n    // This Meteor-specific shell command rebuilds the application as if a\n    // change was made to server code.\n    repl.defineCommand(\"reload\", {\n      help: \"Restart the server and the shell\",\n      action: function() {\n        if (process.sendMessage) {\n          process.sendMessage(\"shell-server\", { command: \"reload\" });\n        } else {\n          process.exit(0);\n        }\n      }\n    });\n  }\n\n  // This function allows a persistent history of shell commands to be saved\n  // to and loaded from .meteor/local/shell/history.\n  initializeHistory() {\n    const repl = this.repl;\n    const historyFile = getHistoryFile(this.shellDir);\n    let historyFd = openSync(historyFile, \"a+\");\n    const historyLines = readFileSync(historyFile, \"utf8\").split(\"\\n\");\n    const seenLines = Object.create(null);\n\n    if (! repl.history) {\n      repl.history = [];\n      repl.historyIndex = -1;\n    }\n\n    while (repl.history && historyLines.length > 0) {\n      const line = historyLines.pop();\n      if (line && /\\S/.test(line) && ! seenLines[line]) {\n        repl.history.push(line);\n        seenLines[line] = true;\n      }\n    }\n\n    repl.addListener(\"line\", function(line) {\n      if (historyFd >= 0 && /\\S/.test(line)) {\n        writeSync(historyFd, line + \"\\n\");\n      }\n    });\n\n    this.repl.on(\"exit\", function() {\n      closeSync(historyFd);\n      historyFd = -1;\n    });\n  }\n}\n\nfunction readJSONFromStream(inputStream, callback) {\n  const outputStream = new PassThrough();\n  let dataSoFar = \"\";\n\n  function onData(buffer) {\n    const lines = buffer.toString(\"utf8\").split(\"\\n\");\n\n    while (lines.length > 0) {\n      dataSoFar += lines.shift();\n\n      let json;\n      try {\n        json = JSON.parse(dataSoFar);\n      } catch (error) {\n        if (error instanceof SyntaxError) {\n          continue;\n        }\n\n        return finish(error);\n      }\n\n      if (lines.length > 0) {\n        outputStream.write(lines.join(\"\\n\"));\n      }\n\n      inputStream.pipe(outputStream);\n\n      return finish(null, json);\n    }\n  }\n\n  function onClose() {\n    finish(new Error(\"stream unexpectedly closed\"));\n  }\n\n  let finished = false;\n  function finish(error, json) {\n    if (! finished) {\n      finished = true;\n      inputStream.removeListener(\"data\", onData);\n      inputStream.removeListener(\"error\", finish);\n      inputStream.removeListener(\"close\", onClose);\n      callback(error, json, outputStream);\n    }\n  }\n\n  inputStream.on(\"data\", onData);\n  inputStream.on(\"error\", finish);\n  inputStream.on(\"close\", onClose);\n}\n\nfunction getInfoFile(shellDir) {\n  return pathJoin(shellDir, \"info.json\");\n}\n\nfunction getHistoryFile(shellDir) {\n  return pathJoin(shellDir, \"history\");\n}\n\nfunction getCacheDirectory(shellDir) {\n  return pathJoin(shellDir, \"cache\");\n}\n\nfunction setRequireAndModule(context) {\n  if (Package.modules) {\n    // Use the same `require` function and `module` object visible to the\n    // application.\n    const toBeInstalled = {};\n    const shellModuleName = \"meteor-shell-\" +\n      Math.random().toString(36).slice(2) + \".js\";\n\n    toBeInstalled[shellModuleName] = function (require, exports, module) {\n      context.module = module;\n      context.require = require;\n\n      // Tab completion sometimes uses require.extensions, but only for\n      // the keys.\n      require.extensions = {\n        \".js\": true,\n        \".json\": true,\n        \".node\": true,\n      };\n    };\n\n    // This populates repl.context.{module,require} by evaluating the\n    // module defined above.\n    Package.modules.meteorInstall(toBeInstalled)(\"./\" + shellModuleName);\n  }\n}\n"]}