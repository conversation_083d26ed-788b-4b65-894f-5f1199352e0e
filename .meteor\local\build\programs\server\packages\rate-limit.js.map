{"version": 3, "sources": ["meteor://💻app/packages/rate-limit/rate-limit.js"], "names": ["module", "export", "RateLimiter", "Meteor", "link", "v", "Random", "__reifyWaitForDeps__", "DEFAULT_INTERVAL_TIME_IN_MILLISECONDS", "DEFAULT_REQUESTS_PER_INTERVAL", "hasOwn", "Object", "prototype", "hasOwnProperty", "Rule", "constructor", "options", "matchers", "id", "_matchers", "_lastResetTime", "Date", "getTime", "counters", "match", "input", "entries", "every", "_ref", "key", "matcher", "call", "_generateKeyString", "filter", "_ref2", "reduce", "returnString", "_ref3", "apply", "timeSinceLastReset", "timeToNextReset", "intervalTime", "resetCounter", "_executeCallback", "reply", "ruleInput", "callback", "e", "console", "error", "rules", "check", "allowed", "timeToReset", "numInvocationsLeft", "Infinity", "matchedRules", "_findAllMatchingRules", "for<PERSON>ach", "rule", "ruleResult", "numInvocations", "numRequestsAllowed", "ruleId", "addRule", "bindEnvironment", "newRule", "increment", "values", "removeRule", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,WAAW,EAACA,CAAA,KAAIA;IAAW,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACH,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACD,MAAMA,CAACE,CAAC,EAAC;QAACF,MAAM,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACN,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACE,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAGzO;IACA,MAAMC,qCAAqC,GAAG,IAAI;IAClD;IACA,MAAMC,6BAA6B,GAAG,EAAE;IAExC,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;;IAE9C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,IAAI,CAAC;MACTC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;QAC7B,IAAI,CAACC,EAAE,GAAGZ,MAAM,CAACY,EAAE,CAAC,CAAC;QAErB,IAAI,CAACF,OAAO,GAAGA,OAAO;QAEtB,IAAI,CAACG,SAAS,GAAGF,QAAQ;QAEzB,IAAI,CAACG,cAAc,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;QAE1C;QACA,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;MACpB;MACA;MACA;MACA;MACAC,KAAKA,CAACC,KAAK,EAAE;QACX,OAAOd,MAAM,CACVe,OAAO,CAAC,IAAI,CAACP,SAAS,CAAC,CACvBQ,KAAK,CAACC,IAAA,IAAoB;UAAA,IAAnB,CAACC,GAAG,EAAEC,OAAO,CAAC,GAAAF,IAAA;UACpB,IAAIE,OAAO,KAAK,IAAI,EAAE;YACpB,IAAI,CAACpB,MAAM,CAACqB,IAAI,CAACN,KAAK,EAAEI,GAAG,CAAC,EAAE;cAC5B,OAAO,KAAK;YACd,CAAC,MAAM,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;cACxC,IAAI,CAAEA,OAAO,CAACL,KAAK,CAACI,GAAG,CAAC,CAAE,EAAE;gBAC1B,OAAO,KAAK;cACd;YACF,CAAC,MAAM,IAAIC,OAAO,KAAKL,KAAK,CAACI,GAAG,CAAC,EAAE;cACjC,OAAO,KAAK;YACd;UACF;UACA,OAAO,IAAI;QACb,CAAC,CAAC;MACN;;MAEA;MACA;MACA;MACAG,kBAAkBA,CAACP,KAAK,EAAE;QACxB,OAAOd,MAAM,CAACe,OAAO,CAAC,IAAI,CAACP,SAAS,CAAC,CAClCc,MAAM,CAACC,KAAA;UAAA,IAAC,CAACL,GAAG,CAAC,GAAAK,KAAA;UAAA,OAAK,IAAI,CAACf,SAAS,CAACU,GAAG,CAAC,KAAK,IAAI;QAAA,EAAC,CAC/CM,MAAM,CAAC,CAACC,YAAY,EAAAC,KAAA,KAAqB;UAAA,IAAnB,CAACR,GAAG,EAAEC,OAAO,CAAC,GAAAO,KAAA;UACnC,IAAI,OAAOP,OAAO,KAAK,UAAU,EAAE;YACjC,IAAIA,OAAO,CAACL,KAAK,CAACI,GAAG,CAAC,CAAC,EAAE;cACvBO,YAAY,IAAIP,GAAG,GAAGJ,KAAK,CAACI,GAAG,CAAC;YAClC;UACF,CAAC,MAAM;YACLO,YAAY,IAAIP,GAAG,GAAGJ,KAAK,CAACI,GAAG,CAAC;UAClC;UACA,OAAOO,YAAY;QACrB,CAAC,EAAE,EAAE,CAAC;MACV;;MAEA;MACA;MACAE,KAAKA,CAACb,KAAK,EAAE;QACX,MAAMI,GAAG,GAAG,IAAI,CAACG,kBAAkB,CAACP,KAAK,CAAC;QAC1C,MAAMc,kBAAkB,GAAG,IAAIlB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAACF,cAAc;QACrE,MAAMoB,eAAe,GAAG,IAAI,CAACxB,OAAO,CAACyB,YAAY,GAAGF,kBAAkB;QACtE,OAAO;UACLV,GAAG;UACHU,kBAAkB;UAClBC;QACF,CAAC;MACH;;MAEA;MACA;MACA;MACAE,YAAYA,CAAA,EAAG;QACb;QACA,IAAI,CAACnB,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAACH,cAAc,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAC5C;MAEAqB,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;QACjC,IAAI;UACF,IAAI,IAAI,CAAC7B,OAAO,CAAC8B,QAAQ,EAAE;YACzB,IAAI,CAAC9B,OAAO,CAAC8B,QAAQ,CAACF,KAAK,EAAEC,SAAS,CAAC;UACzC;QACF,CAAC,CAAC,OAAOE,CAAC,EAAE;UACV;UACAC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;QAClB;MACF;IACF;IAEA,MAAM7C,WAAW,CAAC;MAChB;MACAa,WAAWA,CAAA,EAAG;QACZ;QACA;QACA;;QAEA,IAAI,CAACmC,KAAK,GAAG,CAAC,CAAC;MACjB;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEC,KAAKA,CAAC1B,KAAK,EAAE;QACX,MAAMmB,KAAK,GAAG;UACZQ,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,CAAC;UACdC,kBAAkB,EAAEC;QACtB,CAAC;QAED,MAAMC,YAAY,GAAG,IAAI,CAACC,qBAAqB,CAAChC,KAAK,CAAC;QACtD+B,YAAY,CAACE,OAAO,CAAEC,IAAI,IAAK;UAC7B,MAAMC,UAAU,GAAGD,IAAI,CAACrB,KAAK,CAACb,KAAK,CAAC;UACpC,IAAIoC,cAAc,GAAGF,IAAI,CAACpC,QAAQ,CAACqC,UAAU,CAAC/B,GAAG,CAAC;UAElD,IAAI+B,UAAU,CAACpB,eAAe,GAAG,CAAC,EAAE;YAClC;YACAmB,IAAI,CAACjB,YAAY,CAAC,CAAC;YACnBkB,UAAU,CAACrB,kBAAkB,GAAG,IAAIlB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAClDqC,IAAI,CAACvC,cAAc;YACrBwC,UAAU,CAACpB,eAAe,GAAGmB,IAAI,CAAC3C,OAAO,CAACyB,YAAY;YACtDoB,cAAc,GAAG,CAAC;UACpB;UAEA,IAAIA,cAAc,GAAGF,IAAI,CAAC3C,OAAO,CAAC8C,kBAAkB,EAAE;YACpD;YACA;YACA;YACA;YACA,IAAIlB,KAAK,CAACS,WAAW,GAAGO,UAAU,CAACpB,eAAe,EAAE;cAClDI,KAAK,CAACS,WAAW,GAAGO,UAAU,CAACpB,eAAe;YAChD;YACAI,KAAK,CAACQ,OAAO,GAAG,KAAK;YACrBR,KAAK,CAACU,kBAAkB,GAAG,CAAC;YAC5BV,KAAK,CAACmB,MAAM,GAAGJ,IAAI,CAACzC,EAAE;YACtByC,IAAI,CAAChB,gBAAgB,CAACC,KAAK,EAAEnB,KAAK,CAAC;UACrC,CAAC,MAAM;YACL;YACA;YACA,IAAIkC,IAAI,CAAC3C,OAAO,CAAC8C,kBAAkB,GAAGD,cAAc,GAClDjB,KAAK,CAACU,kBAAkB,IAAIV,KAAK,CAACQ,OAAO,EAAE;cAC3CR,KAAK,CAACS,WAAW,GAAGO,UAAU,CAACpB,eAAe;cAC9CI,KAAK,CAACU,kBAAkB,GAAGK,IAAI,CAAC3C,OAAO,CAAC8C,kBAAkB,GACxDD,cAAc;YAClB;YACAjB,KAAK,CAACmB,MAAM,GAAGJ,IAAI,CAACzC,EAAE;YACtByC,IAAI,CAAChB,gBAAgB,CAACC,KAAK,EAAEnB,KAAK,CAAC;UACrC;QACF,CAAC,CAAC;QACF,OAAOmB,KAAK;MACd;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEoB,OAAOA,CAACL,IAAI,EAAEG,kBAAkB,EAAErB,YAAY,EAAEK,QAAQ,EAAE;QACxD,MAAM9B,OAAO,GAAG;UACd8C,kBAAkB,EAAEA,kBAAkB,IAAIrD,6BAA6B;UACvEgC,YAAY,EAAEA,YAAY,IAAIjC,qCAAqC;UACnEsC,QAAQ,EAAEA,QAAQ,IAAI3C,MAAM,CAAC8D,eAAe,CAACnB,QAAQ;QACvD,CAAC;QAED,MAAMoB,OAAO,GAAG,IAAIpD,IAAI,CAACE,OAAO,EAAE2C,IAAI,CAAC;QACvC,IAAI,CAACT,KAAK,CAACgB,OAAO,CAAChD,EAAE,CAAC,GAAGgD,OAAO;QAChC,OAAOA,OAAO,CAAChD,EAAE;MACnB;;MAEA;AACF;AACA;AACA;AACA;MACEiD,SAASA,CAAC1C,KAAK,EAAE;QACf;QACA,MAAM+B,YAAY,GAAG,IAAI,CAACC,qBAAqB,CAAChC,KAAK,CAAC;QACtD+B,YAAY,CAACE,OAAO,CAAEC,IAAI,IAAK;UAC7B,MAAMC,UAAU,GAAGD,IAAI,CAACrB,KAAK,CAACb,KAAK,CAAC;UAEpC,IAAImC,UAAU,CAACrB,kBAAkB,GAAGoB,IAAI,CAAC3C,OAAO,CAACyB,YAAY,EAAE;YAC7D;YACAkB,IAAI,CAACjB,YAAY,CAAC,CAAC;UACrB;;UAEA;UACA;UACA,IAAIhC,MAAM,CAACqB,IAAI,CAAC4B,IAAI,CAACpC,QAAQ,EAAEqC,UAAU,CAAC/B,GAAG,CAAC,EAAE;YAC9C8B,IAAI,CAACpC,QAAQ,CAACqC,UAAU,CAAC/B,GAAG,CAAC,EAAE;UACjC,CAAC,MAAM;YACL8B,IAAI,CAACpC,QAAQ,CAACqC,UAAU,CAAC/B,GAAG,CAAC,GAAG,CAAC;UACnC;QACF,CAAC,CAAC;MACJ;;MAEA;MACA4B,qBAAqBA,CAAChC,KAAK,EAAE;QAC3B,OAAOd,MAAM,CAACyD,MAAM,CAAC,IAAI,CAAClB,KAAK,CAAC,CAACjB,MAAM,CAAC0B,IAAI,IAAIA,IAAI,CAACnC,KAAK,CAACC,KAAK,CAAC,CAAC;MACpE;;MAEA;AACF;AACA;AACA;AACA;AACA;MACE4C,UAAUA,CAACnD,EAAE,EAAE;QACb,IAAI,IAAI,CAACgC,KAAK,CAAChC,EAAE,CAAC,EAAE;UAClB,OAAO,IAAI,CAACgC,KAAK,CAAChC,EAAE,CAAC;UACrB,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd;IACF;IAACoD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/rate-limit.js", "sourcesContent": ["import { Meteor } from 'meteor/meteor';\nimport { Random } from 'meteor/random';\n\n// Default time interval (in milliseconds) to reset rate limit counters\nconst DEFAULT_INTERVAL_TIME_IN_MILLISECONDS = 1000;\n// Default number of events allowed per time interval\nconst DEFAULT_REQUESTS_PER_INTERVAL = 10;\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\n// A rule is defined by an options object that contains two fields,\n// `numRequestsAllowed` which is the number of events allowed per interval, and\n// an `intervalTime` which is the amount of time in milliseconds before the\n// rate limit restarts its internal counters, and by a matchers object. A\n// matchers object is a POJO that contains a set of keys with values that\n// define the entire set of inputs that match for each key. The values can\n// either be null (optional), a primitive or a function that returns a boolean\n// of whether the provided input's value matches for this key.\n//\n// Rules are uniquely assigned an `id` and they store a dictionary of counters,\n// which are records used to keep track of inputs that match the rule. If a\n// counter reaches the `numRequestsAllowed` within a given `intervalTime`, a\n// rate limit is reached and future inputs that map to that counter will\n// result in errors being returned to the client.\nclass Rule {\n  constructor(options, matchers) {\n    this.id = Random.id();\n\n    this.options = options;\n\n    this._matchers = matchers;\n\n    this._lastResetTime = new Date().getTime();\n\n    // Dictionary of input keys to counters\n    this.counters = {};\n  }\n  // Determine if this rule applies to the given input by comparing all\n  // rule.matchers. If the match fails, search short circuits instead of\n  // iterating through all matchers.\n  match(input) {\n    return Object\n      .entries(this._matchers)\n      .every(([key, matcher]) => {\n        if (matcher !== null) {\n          if (!hasOwn.call(input, key)) {\n            return false;\n          } else if (typeof matcher === 'function') {\n            if (!(matcher(input[key]))) {\n              return false;\n            }\n          } else if (matcher !== input[key]) {\n            return false;\n          }\n        }\n        return true;\n      });\n  }\n\n  // Generates unique key string for provided input by concatenating all the\n  // keys in the matcher with the corresponding values in the input.\n  // Only called if rule matches input.\n  _generateKeyString(input) {\n    return Object.entries(this._matchers)\n      .filter(([key]) => this._matchers[key] !== null)\n      .reduce((returnString, [key, matcher]) => {\n        if (typeof matcher === 'function') {\n          if (matcher(input[key])) {\n            returnString += key + input[key];\n          }\n        } else {\n          returnString += key + input[key];\n        }\n        return returnString;\n      }, '');\n  }\n\n  // Applies the provided input and returns the key string, time since counters\n  // were last reset and time to next reset.\n  apply(input) {\n    const key = this._generateKeyString(input);\n    const timeSinceLastReset = new Date().getTime() - this._lastResetTime;\n    const timeToNextReset = this.options.intervalTime - timeSinceLastReset;\n    return {\n      key,\n      timeSinceLastReset,\n      timeToNextReset,\n    };\n  }\n\n  // Reset counter dictionary for this specific rule. Called once the\n  // timeSinceLastReset has exceeded the intervalTime. _lastResetTime is\n  // set to be the current time in milliseconds.\n  resetCounter() {\n    // Delete the old counters dictionary to allow for garbage collection\n    this.counters = {};\n    this._lastResetTime = new Date().getTime();\n  }\n\n  _executeCallback(reply, ruleInput) {\n    try {\n      if (this.options.callback) {\n        this.options.callback(reply, ruleInput);\n      }\n    } catch (e) {\n      // Do not throw error here\n      console.error(e);\n    }\n  }\n}\n\nclass RateLimiter {\n  // Initialize rules to be an empty dictionary.\n  constructor() {\n    // Dictionary of all rules associated with this RateLimiter, keyed by their\n    // id. Each rule object stores the rule pattern, number of events allowed,\n    // last reset time and the rule reset interval in milliseconds.\n\n    this.rules = {};\n  }\n\n  /**\n  * Checks if this input has exceeded any rate limits.\n  * @param  {object} input dictionary containing key-value pairs of attributes\n  * that match to rules\n  * @return {object} Returns object of following structure\n  * { 'allowed': boolean - is this input allowed\n  *   'timeToReset': integer | Infinity - returns time until counters are reset\n  *                   in milliseconds\n  *   'numInvocationsLeft': integer | Infinity - returns number of calls left\n  *   before limit is reached\n  * }\n  * If multiple rules match, the least number of invocations left is returned.\n  * If the rate limit has been reached, the longest timeToReset is returned.\n  */\n  check(input) {\n    const reply = {\n      allowed: true,\n      timeToReset: 0,\n      numInvocationsLeft: Infinity,\n    };\n\n    const matchedRules = this._findAllMatchingRules(input);\n    matchedRules.forEach((rule) => {\n      const ruleResult = rule.apply(input);\n      let numInvocations = rule.counters[ruleResult.key];\n\n      if (ruleResult.timeToNextReset < 0) {\n        // Reset all the counters since the rule has reset\n        rule.resetCounter();\n        ruleResult.timeSinceLastReset = new Date().getTime() -\n          rule._lastResetTime;\n        ruleResult.timeToNextReset = rule.options.intervalTime;\n        numInvocations = 0;\n      }\n\n      if (numInvocations > rule.options.numRequestsAllowed) {\n        // Only update timeToReset if the new time would be longer than the\n        // previously set time. This is to ensure that if this input triggers\n        // multiple rules, we return the longest period of time until they can\n        // successfully make another call\n        if (reply.timeToReset < ruleResult.timeToNextReset) {\n          reply.timeToReset = ruleResult.timeToNextReset;\n        }\n        reply.allowed = false;\n        reply.numInvocationsLeft = 0;\n        reply.ruleId = rule.id;\n        rule._executeCallback(reply, input);\n      } else {\n        // If this is an allowed attempt and we haven't failed on any of the\n        // other rules that match, update the reply field.\n        if (rule.options.numRequestsAllowed - numInvocations <\n          reply.numInvocationsLeft && reply.allowed) {\n          reply.timeToReset = ruleResult.timeToNextReset;\n          reply.numInvocationsLeft = rule.options.numRequestsAllowed -\n            numInvocations;\n        }\n        reply.ruleId = rule.id;\n        rule._executeCallback(reply, input);\n      }\n    });\n    return reply;\n  }\n\n  /**\n  * Adds a rule to dictionary of rules that are checked against on every call.\n  * Only inputs that pass all of the rules will be allowed. Returns unique rule\n  * id that can be passed to `removeRule`.\n  * @param {object} rule    Input dictionary defining certain attributes and\n  * rules associated with them.\n  * Each attribute's value can either be a value, a function or null. All\n  * functions must return a boolean of whether the input is matched by that\n  * attribute's rule or not\n  * @param {integer} numRequestsAllowed Optional. Number of events allowed per\n  * interval. Default = 10.\n  * @param {integer} intervalTime Optional. Number of milliseconds before\n  * rule's counters are reset. Default = 1000.\n  * @param {function} callback Optional. Function to be called after a\n  * rule is executed. Two objects will be passed to this function.\n  * The first one is the result of RateLimiter.prototype.check\n  * The second is the input object of the rule, it has the following structure:\n  * {\n  *   'type': string - either 'method' or 'subscription'\n  *   'name': string - the name of the method or subscription being called\n  *   'userId': string - the user ID attempting the method or subscription\n  *   'connectionId': string - a string representing the user's DDP connection\n  *   'clientAddress': string - the IP address of the user\n  * }\n  * @return {string} Returns unique rule id\n  */\n  addRule(rule, numRequestsAllowed, intervalTime, callback) {\n    const options = {\n      numRequestsAllowed: numRequestsAllowed || DEFAULT_REQUESTS_PER_INTERVAL,\n      intervalTime: intervalTime || DEFAULT_INTERVAL_TIME_IN_MILLISECONDS,\n      callback: callback && Meteor.bindEnvironment(callback),\n    };\n\n    const newRule = new Rule(options, rule);\n    this.rules[newRule.id] = newRule;\n    return newRule.id;\n  }\n\n  /**\n  * Increment counters in every rule that match to this input\n  * @param  {object} input Dictionary object containing attributes that may\n  * match to rules\n  */\n  increment(input) {\n    // Only increment rule counters that match this input\n    const matchedRules = this._findAllMatchingRules(input);\n    matchedRules.forEach((rule) => {\n      const ruleResult = rule.apply(input);\n\n      if (ruleResult.timeSinceLastReset > rule.options.intervalTime) {\n        // Reset all the counters since the rule has reset\n        rule.resetCounter();\n      }\n\n      // Check whether the key exists, incrementing it if so or otherwise\n      // adding the key and setting its value to 1\n      if (hasOwn.call(rule.counters, ruleResult.key)) {\n        rule.counters[ruleResult.key]++;\n      } else {\n        rule.counters[ruleResult.key] = 1;\n      }\n    });\n  }\n\n  // Returns an array of all rules that apply to provided input\n  _findAllMatchingRules(input) {\n    return Object.values(this.rules).filter(rule => rule.match(input));\n  }\n\n  /**\n   * Provides a mechanism to remove rules from the rate limiter. Returns boolean\n   * about success.\n   * @param  {string} id Rule id returned from #addRule\n   * @return {boolean} Returns true if rule was found and deleted, else false.\n   */\n  removeRule(id) {\n    if (this.rules[id]) {\n      delete this.rules[id];\n      return true;\n    }\n    return false;\n  }\n}\n\nexport { RateLimiter };\n"]}