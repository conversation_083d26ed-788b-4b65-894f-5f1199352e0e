{"version": 3, "sources": ["meteor://💻app/imports/api/links.js", "meteor://💻app/imports/api/tasks.js", "meteor://💻app/server/main.js"], "names": ["module", "export", "LinksCollection", "Mongo", "link", "v", "__reifyWaitForDeps__", "Collection", "__reify_async_result__", "_reifyError", "self", "async", "_objectSpread", "default", "Tasks", "taskCategories", "taskLabels", "Meteor", "check", "Match", "name", "color", "isServer", "publish", "_user$roles", "userId", "ready", "user", "users", "findOneAsync", "isAdmin", "roles", "includes", "find", "sort", "createdAt", "fields", "title", "description", "startDate", "dueDate", "priority", "status", "assignedTo", "checklist", "category", "labels", "progress", "attachments", "links", "created<PERSON>y", "updatedAt", "updatedBy", "$or", "console", "log", "tasks", "fetch", "length", "userIds", "Set", "for<PERSON>ach", "task", "attachment", "uploadedBy", "add", "String", "added<PERSON>y", "userIdArray", "Array", "from", "_id", "$in", "emails", "map", "u", "_u$profile", "_u$profile2", "concat", "profile", "firstName", "lastName", "trim", "hasProfile", "taskId", "_user$roles2", "findOne", "cursor", "observe", "added", "doc", "changed", "removed", "methods", "tasks.insert", "Date", "Number", "Error", "processedChecklist", "item", "text", "completed", "taskToInsert", "result", "insertAsync", "error", "message", "tasks.update", "_user$roles3", "Optional", "existingTask", "completedItems", "filter", "totalItems", "Math", "round", "taskToUpdate", "updateAsync", "$set", "tasks.delete", "removeAsync", "tasks.updateProgress", "update", "tasks.toggleChecklistItem", "itemIndex", "_user$roles4", "checklistLength", "updatedChecklist", "updateResult", "updatedTask", "tasks.addAttachment", "fileData", "data", "uploaderId", "$push", "type", "uploadedAt", "tasks.addLink", "URL", "e", "url", "addedAt", "tasks.removeAttachment", "attachmentIndex", "currentUserId", "uploadedById", "updatedAttachments", "splice", "tasks.removeLink", "linkIndex", "addedById", "updatedLinks", "tasks.findOne", "Accounts", "Email", "Roles", "bcrypt", "insertLink", "_ref", "ADMIN_TOKEN", "startup", "_Meteor$settings$priv", "createIndex", "background", "warn", "allUsers", "fetchAsync", "_user$emails", "_user$emails$", "id", "email", "address", "updates", "isArray", "Object", "keys", "_user$emails2", "_user$emails2$", "teamMembersCount", "countAsync", "testMembers", "password", "member", "createUserAsync", "role", "fullName", "emailSettings", "settings", "private", "username", "process", "env", "MAIL_URL", "encodeURIComponent", "server", "port", "send", "to", "subject", "config", "sendVerificationEmail", "forbidClientAccountCreation", "emailTemplates", "siteName", "verifyEmail", "emailAddress", "html", "onCreateUser", "options", "_customizedUser$email", "_customizedUser$email2", "customizedUser", "teamMembers", "users.create", "_ref2", "adminToken", "passwordRegex", "uppercase", "number", "special", "passwordErrors", "test", "push", "join", "createUser", "users.getRole", "_user$profile", "users.resendVerificationEmail", "findUserByEmail", "userEmail", "verified", "users.forgotPassword", "JSON", "stringify", "newPassword", "findError", "tempEmail", "now", "random", "toString", "substr", "tempUserId", "createError", "tempUser", "passwordHash", "services", "hashError", "remove", "saltRounds", "hashedPassword", "hashSync", "bcryptError", "updateError", "updateMethods", "upsert", "i", "methodError", "cleanupError", "success", "outerError", "stack", "users.checkAndFixAdminRole", "_user$emails3", "_user$emails3$", "totalUsers", "users.diagnoseRoles", "_currentUser$roles", "currentUser", "usersWithIssues", "fixes", "_user$profile3", "issues", "_user$profile2", "_user$emails4", "_user$emails4$", "_user$emails5", "_user$emails5$", "_user$emails6", "_user$emails6$", "users.createTestTeamMember", "NODE_ENV", "testMember"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,eAAe,EAACA,CAAA,KAAIA;IAAe,CAAC,CAAC;IAAC,IAAIC,KAAK;IAACH,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACD,KAAKA,CAACE,CAAC,EAAC;QAACF,KAAK,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEtK,MAAMJ,eAAe,GAAG,IAAIC,KAAK,CAACI,UAAU,CAAC,OAAO,CAAC;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICF7D,IAAIC,aAAa;IAACZ,MAAM,CAACI,IAAI,CAAC,sCAAsC,EAAC;MAACS,OAAOA,CAACR,CAAC,EAAC;QAACO,aAAa,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGL,MAAM,CAACC,MAAM,CAAC;MAACa,KAAK,EAACA,CAAA,KAAIA,KAAK;MAACC,cAAc,EAACA,CAAA,KAAIA,cAAc;MAACC,UAAU,EAACA,CAAA,KAAIA;IAAU,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACjB,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACa,MAAMA,CAACZ,CAAC,EAAC;QAACY,MAAM,GAACZ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIF,KAAK;IAACH,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACD,KAAKA,CAACE,CAAC,EAAC;QAACF,KAAK,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIa,KAAK;IAAClB,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACc,KAAKA,CAACb,CAAC,EAAC;QAACa,KAAK,GAACb,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIc,KAAK;IAACnB,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACe,KAAKA,CAACd,CAAC,EAAC;QAACc,KAAK,GAACd,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAKtY,MAAMQ,KAAK,GAAG,IAAIX,KAAK,CAACI,UAAU,CAAC,OAAO,CAAC;IAE3C,MAAMQ,cAAc,GAAG,CAC5B,aAAa,EACb,QAAQ,EACR,WAAW,EACX,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,OAAO,CACR;IAEM,MAAMC,UAAU,GAAG,CACxB;MAAEI,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC,EACjC;MAAED,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACrC;MAAED,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAU,CAAC,EACzC;MAAED,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAU,CAAC,EAC3C;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAU,CAAC,EACpC;MAAED,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,CACtC;IAED,IAAIJ,MAAM,CAACK,QAAQ,EAAE;MACnB;MACAL,MAAM,CAACM,OAAO,CAAC,OAAO,EAAE,kBAAiB;QAAA,IAAAC,WAAA;QACvC,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;UAChB,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC;QACrB;;QAEA;QACA,MAAMC,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;QACzD,MAAMK,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAAH,WAAA,GAAJG,IAAI,CAAEI,KAAK,cAAAP,WAAA,uBAAXA,WAAA,CAAaQ,QAAQ,CAAC,OAAO,CAAC;;QAE9C;QACA,IAAIF,OAAO,EAAE;UACX,OAAOhB,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC,EAAE;YACpBC,IAAI,EAAE;cAAEC,SAAS,EAAE,CAAC;YAAE,CAAC;YACvBC,MAAM,EAAE;cACNC,KAAK,EAAE,CAAC;cACRC,WAAW,EAAE,CAAC;cACdC,SAAS,EAAE,CAAC;cACZC,OAAO,EAAE,CAAC;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,CAAC;cACbC,SAAS,EAAE,CAAC;cACZC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cACTC,QAAQ,EAAE,CAAC;cACXC,WAAW,EAAE,CAAC;cACdC,KAAK,EAAE,CAAC;cACRd,SAAS,EAAE,CAAC;cACZe,SAAS,EAAE,CAAC;cACZC,SAAS,EAAE,CAAC;cACZC,SAAS,EAAE;YACb;UACF,CAAC,CAAC;QACJ;;QAEA;QACA,OAAOtC,KAAK,CAACmB,IAAI,CAAC;UAChBoB,GAAG,EAAE,CACH;YAAEV,UAAU,EAAE,IAAI,CAAClB;UAAO,CAAC,EAC3B;YAAEyB,SAAS,EAAE,IAAI,CAACzB;UAAO,CAAC;QAE9B,CAAC,EAAE;UACDS,IAAI,EAAE;YAAEC,SAAS,EAAE,CAAC;UAAE,CAAC;UACvBC,MAAM,EAAE;YACNC,KAAK,EAAE,CAAC;YACRC,WAAW,EAAE,CAAC;YACdC,SAAS,EAAE,CAAC;YACZC,OAAO,EAAE,CAAC;YACVC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,CAAC;YACbC,SAAS,EAAE,CAAC;YACZC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAE,CAAC;YACdC,KAAK,EAAE,CAAC;YACRd,SAAS,EAAE,CAAC;YACZe,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAnC,MAAM,CAACM,OAAO,CAAC,WAAW,EAAE,YAAW;QACrC+B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC9B,MAAM,EAAE;UAChB6B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzC,OAAO,IAAI,CAAC7B,KAAK,CAAC,CAAC;QACrB;;QAEA;QACA,MAAM8B,KAAK,GAAG1C,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC;QACpCH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,KAAK,CAACE,MAAM,CAAC;;QAEzC;QACA,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;QACzBJ,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;UACpB;UACA,IAAIA,IAAI,CAACd,WAAW,EAAE;YACpBc,IAAI,CAACd,WAAW,CAACa,OAAO,CAACE,UAAU,IAAI;cACrC,IAAIA,UAAU,CAACC,UAAU,EAAE;gBACzBL,OAAO,CAACM,GAAG,CAACC,MAAM,CAACH,UAAU,CAACC,UAAU,CAAC,CAAC;cAC5C;YACF,CAAC,CAAC;UACJ;UACA;UACA,IAAIF,IAAI,CAACb,KAAK,EAAE;YACda,IAAI,CAACb,KAAK,CAACY,OAAO,CAACzD,IAAI,IAAI;cACzB,IAAIA,IAAI,CAAC+D,OAAO,EAAE;gBAChBR,OAAO,CAACM,GAAG,CAACC,MAAM,CAAC9D,IAAI,CAAC+D,OAAO,CAAC,CAAC;cACnC;YACF,CAAC,CAAC;UACJ;UACA;UACA,IAAIL,IAAI,CAACnB,UAAU,EAAE;YACnBmB,IAAI,CAACnB,UAAU,CAACkB,OAAO,CAACpC,MAAM,IAAI;cAChCkC,OAAO,CAACM,GAAG,CAACC,MAAM,CAACzC,MAAM,CAAC,CAAC;YAC7B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QAEF,MAAM2C,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACX,OAAO,CAAC;QACvCL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,WAAW,CAAC;;QAEzD;QACA,MAAMxC,KAAK,GAAGX,MAAM,CAACW,KAAK,CAACK,IAAI,CAC7B;UAAEsC,GAAG,EAAE;YAAEC,GAAG,EAAEJ;UAAY;QAAE,CAAC,EAC7B;UACEhC,MAAM,EAAE;YACNmC,GAAG,EAAE,CAAC;YACNE,MAAM,EAAE,CAAC;YACT1C,KAAK,EAAE,CAAC;YACR,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;YACrBI,SAAS,EAAE;UACb;QACF,CACF,CAAC,CAACsB,KAAK,CAAC,CAAC;QAETH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE3B,KAAK,CAAC8C,GAAG,CAACC,CAAC;UAAA,IAAAC,UAAA,EAAAC,WAAA;UAAA,OAAK;YAC1CN,GAAG,EAAEI,CAAC,CAACJ,GAAG;YACVnD,IAAI,EAAE,GAAA0D,MAAA,CAAG,EAAAF,UAAA,GAAAD,CAAC,CAACI,OAAO,cAAAH,UAAA,uBAATA,UAAA,CAAWI,SAAS,KAAI,EAAE,OAAAF,MAAA,CAAI,EAAAD,WAAA,GAAAF,CAAC,CAACI,OAAO,cAAAF,WAAA,uBAATA,WAAA,CAAWI,QAAQ,KAAI,EAAE,EAAGC,IAAI,CAAC,CAAC;YACzEC,UAAU,EAAE,CAAC,CAACR,CAAC,CAACI;UAClB,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA,OAAO9D,MAAM,CAACW,KAAK,CAACK,IAAI,CACtB;UAAEsC,GAAG,EAAE;YAAEC,GAAG,EAAEJ;UAAY;QAAE,CAAC,EAC7B;UACAhC,MAAM,EAAE;YACNmC,GAAG,EAAE,CAAC;YACJE,MAAM,EAAE,CAAC;YACT1C,KAAK,EAAE,CAAC;YACV,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,CAAC;YACnB,cAAc,EAAE,CAAC;YACjB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;YACrBI,SAAS,EAAE;UACb;QACF,CACF,CAAC;MACH,CAAC,CAAC;;MAEF;MACAlB,MAAM,CAACM,OAAO,CAAC,MAAM,EAAE,UAAS6D,MAAM,EAAE;QAAA,IAAAC,YAAA;QACtCnE,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAACzC,MAAM,EAAE;UAChB,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC;QACrB;QAEA,MAAMC,IAAI,GAAGV,MAAM,CAACW,KAAK,CAAC0D,OAAO,CAAC,IAAI,CAAC7D,MAAM,CAAC;QAC9C,MAAMK,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAA0D,YAAA,GAAJ1D,IAAI,CAAEI,KAAK,cAAAsD,YAAA,uBAAXA,YAAA,CAAarD,QAAQ,CAAC,OAAO,CAAC;;QAE9C;QACA,MAAMuD,MAAM,GAAGzE,KAAK,CAACmB,IAAI,CACvB;UAAEsC,GAAG,EAAEa;QAAO,CAAC,EACf;UACEhD,MAAM,EAAE;YACNC,KAAK,EAAE,CAAC;YACRC,WAAW,EAAE,CAAC;YACdC,SAAS,EAAE,CAAC;YACZC,OAAO,EAAE,CAAC;YACVC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,CAAC;YACbC,SAAS,EAAE,CAAC;YACZC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAE,CAAC;YACdC,KAAK,EAAE,CAAC;YACRd,SAAS,EAAE,CAAC;YACZe,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE;UACb;QACF,CACF,CAAC;;QAED;QACAE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE6B,MAAM,CAAC;QACvCG,MAAM,CAACC,OAAO,CAAC;UACbC,KAAK,EAAGC,GAAG,IAAKpC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEmC,GAAG,CAACnB,GAAG,CAAC;UAClEoB,OAAO,EAAGD,GAAG,IAAKpC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmC,GAAG,CAACnB,GAAG,CAAC;UACtEqB,OAAO,EAAGF,GAAG,IAAKpC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmC,GAAG,CAACnB,GAAG;QACzE,CAAC,CAAC;QAEF,OAAOgB,MAAM;MACf,CAAC,CAAC;IACJ;IAEAtE,MAAM,CAAC4E,OAAO,CAAC;MACb,MAAM,cAAcC,CAAChC,IAAI,EAAE;QACzB5C,KAAK,CAAC4C,IAAI,EAAE;UACVzB,KAAK,EAAE6B,MAAM;UACb5B,WAAW,EAAE4B,MAAM;UACnB3B,SAAS,EAAEwD,IAAI;UACfvD,OAAO,EAAEuD,IAAI;UACbtD,QAAQ,EAAEyB,MAAM;UAChBxB,MAAM,EAAEwB,MAAM;UACdvB,UAAU,EAAE0B,KAAK;UACjBzB,SAAS,EAAEyB,KAAK;UAChBxB,QAAQ,EAAEqB,MAAM;UAChBpB,MAAM,EAAEuB,KAAK;UACbtB,QAAQ,EAAEiD;QACZ,CAAC,CAAC;QAEF,IAAI,CAAC,IAAI,CAACvE,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA3C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,IAAI,CAAC,CAAC,CAAC;;QAEzC;QACA,MAAMoC,kBAAkB,GAAGpC,IAAI,CAAClB,SAAS,CAAC8B,GAAG,CAACyB,IAAI,KAAK;UACrDC,IAAI,EAAED,IAAI,CAACC,IAAI;UACfC,SAAS,EAAEF,IAAI,CAACE,SAAS,IAAI;QAC/B,CAAC,CAAC,CAAC;QAEH,MAAMC,YAAY,GAAA1F,aAAA,CAAAA,aAAA,KACbkD,IAAI;UACP3B,SAAS,EAAE,IAAI4D,IAAI,CAAC,CAAC;UACrB7C,SAAS,EAAE,IAAI,CAACzB,MAAM;UACtB0B,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;UACrB3C,SAAS,EAAE,IAAI,CAAC3B,MAAM;UACtBsB,QAAQ,EAAEe,IAAI,CAACf,QAAQ,IAAI,CAAC;UAC5BL,MAAM,EAAE,SAAS;UAAE;UACnBE,SAAS,EAAEsD,kBAAkB;UAC7BpD,MAAM,EAAEgB,IAAI,CAAChB,MAAM,IAAI,EAAE;UACzBD,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ,IAAI,EAAE;UAC7BF,UAAU,EAAEmB,IAAI,CAACnB,UAAU,IAAI;QAAE,EAClC;QAEDW,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+C,YAAY,CAAC,CAAC,CAAC;;QAE1D,IAAI;UACF,MAAMC,MAAM,GAAG,MAAMzF,KAAK,CAAC0F,WAAW,CAACF,YAAY,CAAC;UACpDhD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgD,MAAM,CAAC,CAAC,CAAC;UACnD,OAAOA,MAAM;QACf,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,sBAAsB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC/D;MACF,CAAC;MAED,MAAM,cAAcC,CAACvB,MAAM,EAAEtB,IAAI,EAAE;QACjC,IAAI;UAAA,IAAA8C,YAAA;UACFtD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YAAE6B,MAAM;YAAEtB;UAAK,CAAC,CAAC;UAEtD5C,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;UACrBhD,KAAK,CAAC4C,IAAI,EAAE;YACVzB,KAAK,EAAE6B,MAAM;YACb5B,WAAW,EAAE4B,MAAM;YACnB3B,SAAS,EAAEwD,IAAI;YACfvD,OAAO,EAAEuD,IAAI;YACbtD,QAAQ,EAAEyB,MAAM;YAChBvB,UAAU,EAAE0B,KAAK;YACjBzB,SAAS,EAAEyB,KAAK;YAChBxB,QAAQ,EAAE1B,KAAK,CAAC0F,QAAQ,CAAC3C,MAAM,CAAC;YAChCpB,MAAM,EAAE3B,KAAK,CAAC0F,QAAQ,CAACxC,KAAK,CAAC;YAC7BtB,QAAQ,EAAE5B,KAAK,CAAC0F,QAAQ,CAACb,MAAM,CAAC;YAChCtD,MAAM,EAAEvB,KAAK,CAAC0F,QAAQ,CAAC3C,MAAM,CAAC;YAC9BlB,WAAW,EAAE7B,KAAK,CAAC0F,QAAQ,CAACxC,KAAK;UACnC,CAAC,CAAC;UAEF,IAAI,CAAC,IAAI,CAAC5C,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMa,YAAY,GAAG,MAAMhG,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACrD,IAAI,CAAC0B,YAAY,EAAE;YACjB,MAAM,IAAI7F,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,MAAMtE,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD,MAAMK,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAAiF,YAAA,GAAJjF,IAAI,CAAEI,KAAK,cAAA6E,YAAA,uBAAXA,YAAA,CAAa5E,QAAQ,CAAC,OAAO,CAAC;UAE9C,IAAI,CAACF,OAAO,IAAI,CAACgF,YAAY,CAACnE,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YAC9D,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,4CAA4C,CAAC;UACxF;;UAEA;UACA,MAAMc,cAAc,GAAGjD,IAAI,CAAClB,SAAS,CAACoE,MAAM,CAACb,IAAI,IAAIA,IAAI,CAACE,SAAS,CAAC,CAAC3C,MAAM;UAC3E,MAAMuD,UAAU,GAAGnD,IAAI,CAAClB,SAAS,CAACc,MAAM;UACxC,MAAMX,QAAQ,GAAGkE,UAAU,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAEJ,cAAc,GAAGE,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAErF;UACA,IAAIvE,MAAM,GAAGoB,IAAI,CAACpB,MAAM;UACxB,IAAIK,QAAQ,KAAK,GAAG,EAAE;YACpBL,MAAM,GAAG,WAAW;UACtB,CAAC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;YACvBL,MAAM,GAAG,aAAa;UACxB,CAAC,MAAM;YACLA,MAAM,GAAG,SAAS;UACpB;UAEA,MAAM0E,YAAY,GAAAxG,aAAA,CAAAA,aAAA,KACbkD,IAAI;YACPX,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;YACrB3C,SAAS,EAAE,IAAI,CAAC3B,MAAM;YACtBsB,QAAQ;YACRL,MAAM;YACNG,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ,IAAIiE,YAAY,CAACjE,QAAQ,IAAI,EAAE;YACtDC,MAAM,EAAEgB,IAAI,CAAChB,MAAM,IAAIgE,YAAY,CAAChE,MAAM,IAAI,EAAE;YAChDE,WAAW,EAAEc,IAAI,CAACd,WAAW,IAAI8D,YAAY,CAAC9D,WAAW,IAAI;UAAE,EAChE;UAED,MAAMuD,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YAAEkC,IAAI,EAAEF;UAAa,CACvB,CAAC;UAED,IAAIb,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,uBAAuB,CAAC;UAClE;UAEA,OAAOM,MAAM;QACf,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,cAAca,CAACnC,MAAM,EAAE;QAC3BlE,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAACzC,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA,IAAI;UACF,MAAMM,MAAM,GAAG,MAAMzF,KAAK,CAAC0G,WAAW,CAACpC,MAAM,CAAC;UAE9C,IAAImB,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;UAEA,OAAOM,MAAM;QACf,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,oBAAoB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC7D;MACF,CAAC;MAED,sBAAsBe,CAACrC,MAAM,EAAErC,QAAQ,EAAE;QACvC7B,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QACrBhD,KAAK,CAAC6B,QAAQ,EAAEiD,MAAM,CAAC;QAEvB,IAAI,CAAC,IAAI,CAACvE,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA,MAAMnC,IAAI,GAAGhD,KAAK,CAACwE,OAAO,CAACF,MAAM,CAAC;QAClC,IAAI,CAACtB,IAAI,EAAE;UACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;;QAEA;QACA,IAAI,CAACnC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;UAC1C,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,qCAAqC,CAAC;QAC/D;;QAEA;QACA,IAAIvD,MAAM,GAAGoB,IAAI,CAACpB,MAAM;QACxB,IAAIK,QAAQ,KAAK,GAAG,EAAE;UACpBL,MAAM,GAAG,WAAW;QACtB,CAAC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;UACvBL,MAAM,GAAG,aAAa;QACxB;QAEA,OAAO5B,KAAK,CAAC4G,MAAM,CAACtC,MAAM,EAAE;UAC1BkC,IAAI,EAAE;YACJvE,QAAQ;YACRL,MAAM;YACNS,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;YACrB3C,SAAS,EAAE,IAAI,CAAC3B;UAClB;QACF,CAAC,CAAC;MACJ,CAAC;MAED,MAAM,2BAA2BkG,CAACvC,MAAM,EAAEwC,SAAS,EAAE;QACnD,IAAI;UAAA,IAAAC,YAAA;UACFvE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;YAAE6B,MAAM;YAAEwC,SAAS;YAAEnG,MAAM,EAAE,IAAI,CAACA;UAAO,CAAC,CAAC;UAE7F,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;YAChB6B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;YAC/B,MAAM,IAAItC,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;;UAEA;UACA,IAAI,CAACb,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;YACzC9B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6B,MAAM,CAAC;YACtC,MAAM,IAAInE,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,iBAAiB,CAAC;UAC5D;UAEA,IAAI,OAAO2B,SAAS,KAAK,QAAQ,IAAIA,SAAS,GAAG,CAAC,EAAE;YAClDtE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqE,SAAS,CAAC;YAC5C,MAAM,IAAI3G,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,8BAA8B,CAAC;UACzE;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C9B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEO,IAAI,CAAC;UAEhC,IAAI,CAACA,IAAI,EAAE;YACTR,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;YAC7B,MAAM,IAAItC,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,MAAMtE,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD6B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE5B,IAAI,CAAC;UAEhC,MAAMG,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAAkG,YAAA,GAAJlG,IAAI,CAAEI,KAAK,cAAA8F,YAAA,uBAAXA,YAAA,CAAa7F,QAAQ,CAAC,OAAO,CAAC;UAC9CsB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEzB,OAAO,CAAC;UAEjC,IAAI,CAACA,OAAO,IAAI,CAACgC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YACtD6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;cAAE9B,MAAM,EAAE,IAAI,CAACA,MAAM;cAAEkB,UAAU,EAAEmB,IAAI,CAACnB;YAAW,CAAC,CAAC;YACzF,MAAM,IAAI1B,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,4CAA4C,CAAC;UACxF;UAEA,MAAMrD,SAAS,GAAGkB,IAAI,CAAClB,SAAS,IAAI,EAAE;UACtCU,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEX,SAAS,CAAC;UAE5C,IAAIgF,SAAS,IAAIhF,SAAS,CAACc,MAAM,EAAE;YACjCJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;cAAEqE,SAAS;cAAEE,eAAe,EAAElF,SAAS,CAACc;YAAO,CAAC,CAAC;YACpF,MAAM,IAAIzC,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,8BAA8B,CAAC;UACzE;;UAEA;UACA,MAAM8B,gBAAgB,GAAG,CAAC,GAAGnF,SAAS,CAAC;UACvCmF,gBAAgB,CAACH,SAAS,CAAC,GAAAhH,aAAA,CAAAA,aAAA,KACtBmH,gBAAgB,CAACH,SAAS,CAAC;YAC9BvB,SAAS,EAAE,CAAC0B,gBAAgB,CAACH,SAAS,CAAC,CAACvB;UAAS,EAClD;UAED/C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEwE,gBAAgB,CAAC;;UAEnD;UACA,MAAMhB,cAAc,GAAGgB,gBAAgB,CAACf,MAAM,CAACb,IAAI,IAAIA,IAAI,CAACE,SAAS,CAAC,CAAC3C,MAAM;UAC7E,MAAMuD,UAAU,GAAGc,gBAAgB,CAACrE,MAAM;UAC1C,MAAMX,QAAQ,GAAGkE,UAAU,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAEJ,cAAc,GAAGE,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAErF;UACA,IAAIvE,MAAM;UACV,IAAIK,QAAQ,KAAK,GAAG,EAAE;YACpBL,MAAM,GAAG,WAAW;UACtB,CAAC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;YACvBL,MAAM,GAAG,aAAa;UACxB,CAAC,MAAM;YACLA,MAAM,GAAG,SAAS;UACpB;UAEAY,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;YACjC6B,MAAM;YACN2C,gBAAgB;YAChBhF,QAAQ;YACRL;UACF,CAAC,CAAC;;UAEF;UACA,MAAMoE,YAAY,GAAG,MAAMhG,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACrD,IAAI,CAAC0B,YAAY,EAAE;YACjB,MAAM,IAAI7F,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;UACnE;;UAEA;UACA,MAAM+B,YAAY,GAAG,MAAMlH,KAAK,CAACuG,WAAW,CAC1C;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkC,IAAI,EAAE;cACJ1E,SAAS,EAAEmF,gBAAgB;cAC3BhF,QAAQ;cACRL,MAAM;cACNS,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAE,IAAI,CAAC3B;YAClB;UACF,CACF,CAAC;UAED6B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyE,YAAY,CAAC;UAE3C,IAAIA,YAAY,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI/G,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,uBAAuB,CAAC;UAClE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0E,WAAW,CAAC;UAE9C,OAAOD,YAAY;QACrB,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,qBAAqBwB,CAAC9C,MAAM,EAAE+C,QAAQ,EAAE;QAC5C,IAAI;UACF,IAAI,CAAC,IAAI,CAAC1G,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YAC1C,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,wDAAwD,CAAC;UACpG;UAEA,IAAI,CAACkC,QAAQ,IAAI,CAACA,QAAQ,CAAC/G,IAAI,IAAI,CAAC+G,QAAQ,CAACC,IAAI,EAAE;YACjD,MAAM,IAAInH,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,mBAAmB,CAAC;UAC9D;;UAEA;UACA,MAAMoC,UAAU,GAAGnE,MAAM,CAAC,IAAI,CAACzC,MAAM,CAAC;;UAEtC;UACA,MAAM8E,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkD,KAAK,EAAE;cACLtF,WAAW,EAAE;gBACX5B,IAAI,EAAE+G,QAAQ,CAAC/G,IAAI;gBACnBmH,IAAI,EAAEJ,QAAQ,CAACI,IAAI;gBACnBH,IAAI,EAAED,QAAQ,CAACC,IAAI;gBACnBI,UAAU,EAAE,IAAIzC,IAAI,CAAC,CAAC;gBACtB/B,UAAU,EAAEqE;cACd;YACF,CAAC;YACDf,IAAI,EAAE;cACJnE,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAEiF;YACb;UACF,CACF,CAAC;UAED,IAAI9B,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,0BAA0B,CAAC;UACrE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0E,WAAW,CAAC;UACzD,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,eAAe+B,CAACrD,MAAM,EAAEhF,IAAI,EAAE;QAClC,IAAI;UACF,IAAI,CAAC,IAAI,CAACqB,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YAC1C,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,kDAAkD,CAAC;UAC9F;UAEA,IAAI,CAAC7F,IAAI,EAAE;YACT,MAAM,IAAIa,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,sBAAsB,CAAC;UACjE;;UAEA;UACA,IAAI;YACF,IAAIyC,GAAG,CAACtI,IAAI,CAAC;UACf,CAAC,CAAC,OAAOuI,CAAC,EAAE;YACV,MAAM,IAAI1H,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,oBAAoB,CAAC;UAC/D;;UAEA;UACA,MAAMM,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkD,KAAK,EAAE;cACLrF,KAAK,EAAE;gBACL2F,GAAG,EAAExI,IAAI;gBACTyI,OAAO,EAAE,IAAI9C,IAAI,CAAC,CAAC;gBACnB5B,OAAO,EAAED,MAAM,CAAC,IAAI,CAACzC,MAAM;cAC7B;YACF,CAAC;YACD6F,IAAI,EAAE;cACJnE,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAEc,MAAM,CAAC,IAAI,CAACzC,MAAM;YAC/B;UACF,CACF,CAAC;UAED,IAAI8E,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,oBAAoB,CAAC;UAC/D;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE0E,WAAW,CAAC;UACnD,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1C,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,wBAAwBoC,CAAC1D,MAAM,EAAE2D,eAAe,EAAE;QACtD,IAAI;UACF,IAAI,CAAC,IAAI,CAACtH,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACd,WAAW,IAAI,CAACc,IAAI,CAACd,WAAW,CAAC+F,eAAe,CAAC,EAAE;YAC3D,MAAM,IAAI9H,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,sBAAsB,CAAC;UAC7D;UAEA,MAAMlC,UAAU,GAAGD,IAAI,CAACd,WAAW,CAAC+F,eAAe,CAAC;;UAEpD;UACA,MAAMC,aAAa,GAAG9E,MAAM,CAAC,IAAI,CAACzC,MAAM,CAAC;UACzC,MAAMwH,YAAY,GAAG/E,MAAM,CAACH,UAAU,CAACC,UAAU,CAAC;;UAElD;UACA,IAAIgF,aAAa,KAAKC,YAAY,EAAE;YAClC,MAAM,IAAIhI,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,0CAA0C,CAAC;UACtF;;UAEA;UACA,MAAMiD,kBAAkB,GAAG,CAAC,GAAGpF,IAAI,CAACd,WAAW,CAAC;UAChDkG,kBAAkB,CAACC,MAAM,CAACJ,eAAe,EAAE,CAAC,CAAC;;UAE7C;UACA,MAAMxC,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkC,IAAI,EAAE;cACJtE,WAAW,EAAEkG,kBAAkB;cAC/B/F,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAE4F;YACb;UACF,CACF,CAAC;UAED,IAAIzC,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,6BAA6B,CAAC;UACxE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE0E,WAAW,CAAC;UAC3D,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,kBAAkB0C,CAAChE,MAAM,EAAEiE,SAAS,EAAE;QAC1C,IAAI;UACF,IAAI,CAAC,IAAI,CAAC5H,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACb,KAAK,IAAI,CAACa,IAAI,CAACb,KAAK,CAACoG,SAAS,CAAC,EAAE;YACzC,MAAM,IAAIpI,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;UAEA,MAAM7F,IAAI,GAAG0D,IAAI,CAACb,KAAK,CAACoG,SAAS,CAAC;;UAElC;UACA,MAAML,aAAa,GAAG9E,MAAM,CAAC,IAAI,CAACzC,MAAM,CAAC;UACzC,MAAM6H,SAAS,GAAGpF,MAAM,CAAC9D,IAAI,CAAC+D,OAAO,CAAC;;UAEtC;UACA,IAAI6E,aAAa,KAAKM,SAAS,EAAE;YAC/B,MAAM,IAAIrI,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,oCAAoC,CAAC;UAChF;;UAEA;UACA,MAAMsD,YAAY,GAAG,CAAC,GAAGzF,IAAI,CAACb,KAAK,CAAC;UACpCsG,YAAY,CAACJ,MAAM,CAACE,SAAS,EAAE,CAAC,CAAC;;UAEjC;UACA,MAAM9C,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkC,IAAI,EAAE;cACJrE,KAAK,EAAEsG,YAAY;cACnBpG,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAE4F;YACb;UACF,CACF,CAAC;UAED,IAAIzC,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,uBAAuB,CAAC;UAClE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0E,WAAW,CAAC;UACrD,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,eAAe8C,CAACpE,MAAM,EAAE;QAC5BlE,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAACzC,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;QAC7C,IAAI,CAACtB,IAAI,EAAE;UACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;QAC5D;QAEA,OAAOnC,IAAI;MACb;IACF,CAAC,CAAC;IAACtD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC9xBH,IAAIC,aAAa;IAACZ,MAAM,CAACI,IAAI,CAAC,sCAAsC,EAAC;MAACS,OAAOA,CAACR,CAAC,EAAC;QAACO,aAAa,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAIY,MAAM;IAACjB,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACa,MAAMA,CAACZ,CAAC,EAAC;QAACY,MAAM,GAACZ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIH,eAAe;IAACF,MAAM,CAACI,IAAI,CAAC,oBAAoB,EAAC;MAACF,eAAeA,CAACG,CAAC,EAAC;QAACH,eAAe,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIoJ,QAAQ;IAACzJ,MAAM,CAACI,IAAI,CAAC,sBAAsB,EAAC;MAACqJ,QAAQA,CAACpJ,CAAC,EAAC;QAACoJ,QAAQ,GAACpJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIqJ,KAAK;IAAC1J,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACsJ,KAAKA,CAACrJ,CAAC,EAAC;QAACqJ,KAAK,GAACrJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIS,KAAK;IAACd,MAAM,CAACI,IAAI,CAAC,oBAAoB,EAAC;MAACU,KAAKA,CAACT,CAAC,EAAC;QAACS,KAAK,GAACT,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIsJ,KAAK;IAAC3J,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;MAACuJ,KAAKA,CAACtJ,CAAC,EAAC;QAACsJ,KAAK,GAACtJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIa,KAAK;IAAClB,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACc,KAAKA,CAACb,CAAC,EAAC;QAACa,KAAK,GAACb,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIuJ,MAAM;IAAC5J,MAAM,CAACI,IAAI,CAAC,QAAQ,EAAC;MAACS,OAAOA,CAACR,CAAC,EAAC;QAACuJ,MAAM,GAACvJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IASlmB,eAAeuJ,UAAUA,CAAAC,IAAA,EAAiB;MAAA,IAAhB;QAAEzH,KAAK;QAAEuG;MAAI,CAAC,GAAAkB,IAAA;MACtC,MAAM5J,eAAe,CAACsG,WAAW,CAAC;QAAEnE,KAAK;QAAEuG,GAAG;QAAEzG,SAAS,EAAE,IAAI4D,IAAI,CAAC;MAAE,CAAC,CAAC;IAC1E;IAEA,MAAMgE,WAAW,GAAG,QAAQ;IAE5B9I,MAAM,CAAC+I,OAAO,CAAC,YAAY;MAAA,IAAAC,qBAAA;MACzB;MACA,IAAI;QACF,MAAMnJ,KAAK,CAACoJ,WAAW,CAAC;UAAE/H,SAAS,EAAE;QAAE,CAAC,CAAC;QACzC,MAAMrB,KAAK,CAACoJ,WAAW,CAAC;UAAEvH,UAAU,EAAE;QAAE,CAAC,CAAC;QAC1C,MAAM7B,KAAK,CAACoJ,WAAW,CAAC;UAAEhH,SAAS,EAAE;QAAE,CAAC,CAAC;;QAEzC;QACA;QACA,MAAMjC,MAAM,CAACW,KAAK,CAACsI,WAAW,CAAC;UAAEnI,KAAK,EAAE;QAAE,CAAC,EAAE;UAAEoI,UAAU,EAAE;QAAK,CAAC,CAAC;MACpE,CAAC,CAAC,OAAO1D,KAAK,EAAE;QACdnD,OAAO,CAAC8G,IAAI,CAAC,mCAAmC,EAAE3D,KAAK,CAACC,OAAO,CAAC;MAClE;;MAEA;MACA,IAAI;QACF,MAAM2D,QAAQ,GAAG,MAAMpJ,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC,CAAC,CAACqI,UAAU,CAAC,CAAC;QACvDhH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8G,QAAQ,CAAC3F,GAAG,CAAC/C,IAAI;UAAA,IAAA4I,YAAA,EAAAC,aAAA;UAAA,OAAK;YACxDC,EAAE,EAAE9I,IAAI,CAAC4C,GAAG;YACZmG,KAAK,GAAAH,YAAA,GAAE5I,IAAI,CAAC8C,MAAM,cAAA8F,YAAA,wBAAAC,aAAA,GAAXD,YAAA,CAAc,CAAC,CAAC,cAAAC,aAAA,uBAAhBA,aAAA,CAAkBG,OAAO;YAChC5I,KAAK,EAAEJ,IAAI,CAACI,KAAK;YACjBgD,OAAO,EAAEpD,IAAI,CAACoD;UAChB,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA,KAAK,MAAMpD,IAAI,IAAI0I,QAAQ,EAAE;UAC3B,MAAMO,OAAO,GAAG,CAAC,CAAC;UAElB,IAAI,CAACjJ,IAAI,CAACI,KAAK,IAAI,CAACsC,KAAK,CAACwG,OAAO,CAAClJ,IAAI,CAACI,KAAK,CAAC,EAAE;YAC7C6I,OAAO,CAAC7I,KAAK,GAAG,CAAC,aAAa,CAAC;UACjC;UAEA,IAAI,CAACJ,IAAI,CAACQ,SAAS,EAAE;YACnByI,OAAO,CAACzI,SAAS,GAAG,IAAI4D,IAAI,CAAC,CAAC;UAChC;UAEA,IAAI+E,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAAClH,MAAM,GAAG,CAAC,EAAE;YAAA,IAAAsH,aAAA,EAAAC,cAAA;YACnC3H,OAAO,CAACC,GAAG,CAAC,2CAA2C,GAAAyH,aAAA,GAAErJ,IAAI,CAAC8C,MAAM,cAAAuG,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBN,OAAO,CAAC;YACnF,MAAM1J,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC1F,IAAI,CAAC4C,GAAG,EAAE;cACvC+C,IAAI,EAAEsD;YACR,CAAC,CAAC;UACJ;QACF;QAEA,MAAMM,gBAAgB,GAAG,MAAMjK,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC;UAAE,OAAO,EAAE;QAAc,CAAC,CAAC,CAACkJ,UAAU,CAAC,CAAC;QACzF7H,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2H,gBAAgB,CAAC;;QAE9D;QACA,IAAIA,gBAAgB,KAAK,CAAC,EAAE;UAC1B5H,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnD,IAAI;YACF;YACA,MAAM6H,WAAW,GAAG,CAClB;cACEV,KAAK,EAAE,mBAAmB;cAC1BW,QAAQ,EAAE,cAAc;cACxBrG,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,EACD;cACEyF,KAAK,EAAE,mBAAmB;cAC1BW,QAAQ,EAAE,cAAc;cACxBrG,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,CACF;YAED,KAAK,MAAMqG,MAAM,IAAIF,WAAW,EAAE;cAChC,MAAM3J,MAAM,GAAG,MAAMgI,QAAQ,CAAC8B,eAAe,CAAC;gBAC5Cb,KAAK,EAAEY,MAAM,CAACZ,KAAK;gBACnBW,QAAQ,EAAEC,MAAM,CAACD,QAAQ;gBACzBG,IAAI,EAAE,aAAa;gBACnBrJ,SAAS,EAAE,IAAI4D,IAAI,CAAC,CAAC;gBACrBhB,OAAO,EAAE;kBACPC,SAAS,EAAEsG,MAAM,CAACtG,SAAS;kBAC3BC,QAAQ,EAAEqG,MAAM,CAACrG,QAAQ;kBACzBuG,IAAI,EAAE,aAAa;kBACnBC,QAAQ,KAAA3G,MAAA,CAAKwG,MAAM,CAACtG,SAAS,OAAAF,MAAA,CAAIwG,MAAM,CAACrG,QAAQ;gBAClD;cACF,CAAC,CAAC;;cAEF;cACA,MAAMhE,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC5F,MAAM,EAAE;gBACrC6F,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAAC,aAAa;gBAAE;cACjC,CAAC,CAAC;cAEFuB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;gBACjDkH,EAAE,EAAEhJ,MAAM;gBACViJ,KAAK,EAAEY,MAAM,CAACZ,KAAK;gBACnBtJ,IAAI,KAAA0D,MAAA,CAAKwG,MAAM,CAACtG,SAAS,OAAAF,MAAA,CAAIwG,MAAM,CAACrG,QAAQ;cAC9C,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;YACdnD,OAAO,CAACmD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACrE;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdnD,OAAO,CAACmD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;;MAEA;MACA,MAAMiF,aAAa,IAAAzB,qBAAA,GAAGhJ,MAAM,CAAC0K,QAAQ,CAACC,OAAO,cAAA3B,qBAAA,uBAAvBA,qBAAA,CAAyBS,KAAK;;MAEpD;MACA,IAAIgB,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,IAAIH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEL,QAAQ,EAAE;QACtDS,OAAO,CAACC,GAAG,CAACC,QAAQ,aAAAlH,MAAA,CAAamH,kBAAkB,CAACP,aAAa,CAACG,QAAQ,CAAC,OAAA/G,MAAA,CAAImH,kBAAkB,CAACP,aAAa,CAACL,QAAQ,CAAC,OAAAvG,MAAA,CAAI4G,aAAa,CAACQ,MAAM,OAAApH,MAAA,CAAI4G,aAAa,CAACS,IAAI,CAAE;;QAEzK;QACA,IAAI;UACF7I,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CmG,KAAK,CAAC0C,IAAI,CAAC;YACTC,EAAE,EAAEX,aAAa,CAACG,QAAQ;YAC1BvH,IAAI,EAAEoH,aAAa,CAACG,QAAQ;YAC5BS,OAAO,EAAE,YAAY;YACrBlG,IAAI,EAAE;UACR,CAAC,CAAC;UACF9C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,CAAC,OAAOkD,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC,MAAM;QACLnD,OAAO,CAAC8G,IAAI,CAAC,iDAAiD,CAAC;MACjE;;MAEA;MACAX,QAAQ,CAAC8C,MAAM,CAAC;QACdC,qBAAqB,EAAE,IAAI;QAC3BC,2BAA2B,EAAE;MAC/B,CAAC,CAAC;;MAEF;MACAhD,QAAQ,CAACiD,cAAc,CAACC,QAAQ,GAAG,wBAAwB;MAC3DlD,QAAQ,CAACiD,cAAc,CAACpI,IAAI,GAAGoH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,8BAAA/G,MAAA,CACzB4G,aAAa,CAACG,QAAQ,SACjD,8CAA8C;MAEhDpC,QAAQ,CAACiD,cAAc,CAACE,WAAW,GAAG;QACpCN,OAAOA,CAAA,EAAG;UACR,OAAO,2BAA2B;QACpC,CAAC;QACDlG,IAAIA,CAACzE,IAAI,EAAEiH,GAAG,EAAE;UACd,MAAMiE,YAAY,GAAGlL,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAAC,CAACkG,OAAO;UAC3C,OAAO,gDAAA7F,MAAA,CAC8B+H,YAAY,wCAAqC,MAAA/H,MAAA,CAC/E8D,GAAG,SAAM,4EAC6D,cAC9D,qCACuB;QACxC,CAAC;QACDkE,IAAIA,CAACnL,IAAI,EAAEiH,GAAG,EAAE;UACd,MAAMiE,YAAY,GAAGlL,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAAC,CAACkG,OAAO;UAC3C,sQAAA7F,MAAA,CAKyC+H,YAAY,gHAAA/H,MAAA,CAElC8D,GAAG;QAexB;MACF,CAAC;;MAED;MACA,IAAI,OAAM1I,eAAe,CAAC+B,IAAI,CAAC,CAAC,CAACkJ,UAAU,CAAC,CAAC,MAAK,CAAC,EAAE;QACnD,MAAMtB,UAAU,CAAC;UACfxH,KAAK,EAAE,iBAAiB;UACxBuG,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMiB,UAAU,CAAC;UACfxH,KAAK,EAAE,kBAAkB;UACzBuG,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMiB,UAAU,CAAC;UACfxH,KAAK,EAAE,eAAe;UACtBuG,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMiB,UAAU,CAAC;UACfxH,KAAK,EAAE,aAAa;UACpBuG,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;;MAEA;MACA;MACA3H,MAAM,CAACM,OAAO,CAAC,OAAO,EAAE,YAAY;QAClC,OAAOrB,eAAe,CAAC+B,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACAwH,QAAQ,CAACsD,YAAY,CAAC,CAACC,OAAO,EAAErL,IAAI,KAAK;QAAA,IAAAsL,qBAAA,EAAAC,sBAAA;QACvC5J,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;UACxDmH,KAAK,EAAEsC,OAAO,CAACtC,KAAK;UACpBc,IAAI,EAAEwB,OAAO,CAACxB,IAAI;UAClBzG,OAAO,EAAEiI,OAAO,CAACjI,OAAO;UACxB5C,SAAS,EAAE6K,OAAO,CAAC7K;QACrB,CAAC,CAAC;QAEF,MAAMgL,cAAc,GAAAvM,aAAA,KAAQe,IAAI,CAAE;;QAElC;QACAwL,cAAc,CAACpI,OAAO,GAAGiI,OAAO,CAACjI,OAAO,IAAI,CAAC,CAAC;;QAE9C;QACA,MAAMyG,IAAI,GAAGwB,OAAO,CAACxB,IAAI,IAAI,aAAa;QAC1C2B,cAAc,CAACpL,KAAK,GAAG,CAACyJ,IAAI,CAAC;;QAE7B;QACA2B,cAAc,CAAChL,SAAS,GAAG6K,OAAO,CAAC7K,SAAS,IAAI,IAAI4D,IAAI,CAAC,CAAC;QAE1DzC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAC1CkH,EAAE,EAAE0C,cAAc,CAAC5I,GAAG;UACtBmG,KAAK,GAAAuC,qBAAA,GAAEE,cAAc,CAAC1I,MAAM,cAAAwI,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4BvC,OAAO;UAC1C5I,KAAK,EAAEoL,cAAc,CAACpL,KAAK;UAC3BgD,OAAO,EAAEoI,cAAc,CAACpI,OAAO;UAC/B5C,SAAS,EAAEgL,cAAc,CAAChL;QAC5B,CAAC,CAAC;QAEF,OAAOgL,cAAc;MACvB,CAAC,CAAC;;MAEF;MACAlM,MAAM,CAACM,OAAO,CAAC,aAAa,EAAE,YAAW;QACvC+B,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC9B,MAAM,CAAC;QAErE,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;UAChB6B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD,OAAO,IAAI,CAAC7B,KAAK,CAAC,CAAC;QACrB;QAEA,IAAI;UACF;UACA,MAAM0L,WAAW,GAAGnM,MAAM,CAACW,KAAK,CAACK,IAAI,CACnC;YACEoB,GAAG,EAAE,CACH;cAAE,OAAO,EAAE;YAAc,CAAC,EAC1B;cAAE,cAAc,EAAE;YAAc,CAAC;UAErC,CAAC,EACD;YACEjB,MAAM,EAAE;cACNqC,MAAM,EAAE,CAAC;cACT1C,KAAK,EAAE,CAAC;cACR,mBAAmB,EAAE,CAAC;cACtB,kBAAkB,EAAE,CAAC;cACrB,kBAAkB,EAAE,CAAC;cACrBI,SAAS,EAAE;YACb;UACF,CACF,CAAC;UAEDmB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpD,OAAO6J,WAAW;QACpB,CAAC,CAAC,OAAO3G,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,IAAI,CAAC/E,KAAK,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;;MAEF;MACAT,MAAM,CAACM,OAAO,CAAC,UAAU,EAAE,YAAW;QACpC,IAAI,CAAC,IAAI,CAACE,MAAM,EAAE;UAChB,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC;QACrB;QAEA4B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC9B,MAAM,CAAC;QAEhE,OAAOR,MAAM,CAACW,KAAK,CAACK,IAAI,CACtB;UAAEsC,GAAG,EAAE,IAAI,CAAC9C;QAAO,CAAC,EACpB;UACEW,MAAM,EAAE;YACNL,KAAK,EAAE,CAAC;YACR0C,MAAM,EAAE,CAAC;YACTM,OAAO,EAAE;UACX;QACF,CACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA9D,MAAM,CAAC4E,OAAO,CAAC;MACb,cAAcwH,CAAAC,KAAA,EAA6D;QAAA,IAA5D;UAAE5C,KAAK;UAAEW,QAAQ;UAAEG,IAAI;UAAE+B,UAAU;UAAEvI,SAAS;UAAEC;QAAS,CAAC,GAAAqI,KAAA;QACvE;QACA,IAAI9B,IAAI,KAAK,OAAO,IAAI+B,UAAU,KAAKxD,WAAW,EAAE;UAClD,MAAM,IAAI9I,MAAM,CAACgF,KAAK,CAAC,qBAAqB,EAAE,8BAA8B,CAAC;QAC/E;;QAEA;QACA,MAAMuH,aAAa,GAAG;UACpB9J,MAAM,EAAE,OAAO;UACf+J,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAAC9J,MAAM,CAACmK,IAAI,CAACxC,QAAQ,CAAC,EAAE;UACxCuC,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAACxC,QAAQ,CAAC,EAAE;UAC3CuC,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAACxC,QAAQ,CAAC,EAAE;UACxCuC,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAACxC,QAAQ,CAAC,EAAE;UACzCuC,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAAClK,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAIzC,MAAM,CAACgF,KAAK,CAAC,kBAAkB,EAAE2H,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF,MAAMtM,MAAM,GAAGgI,QAAQ,CAACuE,UAAU,CAAC;YACjCtD,KAAK;YACLW,QAAQ;YACRG,IAAI;YAAE;YACNzG,OAAO,EAAE;cACPyG,IAAI;cAAE;cACNxG,SAAS;cACTC,QAAQ;cACRwG,QAAQ,KAAA3G,MAAA,CAAKE,SAAS,OAAAF,MAAA,CAAIG,QAAQ;YACpC;UACF,CAAC,CAAC;;UAEF;UACA,IAAIxD,MAAM,EAAE;YACVgI,QAAQ,CAAC+C,qBAAqB,CAAC/K,MAAM,CAAC;UACxC;UAEA,OAAOA,MAAM;QACf,CAAC,CAAC,OAAOgF,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,oBAAoB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC7D;MACF,CAAC;MAED,MAAM,eAAeuH,CAAA,EAAG;QACtB,IAAI,CAAC,IAAI,CAACxM,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,wBAAwB,CAAC;QACpE;QAEA,IAAI;UAAA,IAAAzE,WAAA,EAAA0M,aAAA;UACF,MAAMvM,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD,IAAI,CAACE,IAAI,EAAE;YACT,MAAM,IAAIV,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;UAC5D;;UAEA;UACA,MAAMuF,IAAI,GAAG,EAAAhK,WAAA,GAAAG,IAAI,CAACI,KAAK,cAAAP,WAAA,uBAAVA,WAAA,CAAa,CAAC,CAAC,OAAA0M,aAAA,GAAIvM,IAAI,CAACoD,OAAO,cAAAmJ,aAAA,uBAAZA,aAAA,CAAc1C,IAAI,KAAI,aAAa;UACnE,OAAOA,IAAI;QACb,CAAC,CAAC,OAAO/E,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,iBAAiB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,+BAA+ByH,CAACzD,KAAK,EAAE;QACrC;QACA,MAAM/I,IAAI,GAAG8H,QAAQ,CAAC2E,eAAe,CAAC1D,KAAK,CAAC;QAC5C,IAAI,CAAC/I,IAAI,EAAE;UACT,MAAM,IAAIV,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;QACnF;;QAEA;QACA,MAAMoI,SAAS,GAAG1M,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAAC;QAChC,IAAI4J,SAAS,CAACC,QAAQ,EAAE;UACtB,MAAM,IAAIrN,MAAM,CAACgF,KAAK,CAAC,kBAAkB,EAAE,gCAAgC,CAAC;QAC9E;;QAEA;QACA,IAAI;UACFwD,QAAQ,CAAC+C,qBAAqB,CAAC7K,IAAI,CAAC4C,GAAG,EAAEmG,KAAK,CAAC;UAC/C,OAAO,IAAI;QACb,CAAC,CAAC,OAAOjE,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,2BAA2B,EAAEQ,KAAK,CAACC,OAAO,CAAC;QACpE;MACF,CAAC;MAED,sBAAsB6H,CAACnG,IAAI,EAAE;QAC3B,IAAI;UACF9E,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEiL,IAAI,CAACC,SAAS,CAACrG,IAAI,CAAC,CAAC;UAE9ElH,KAAK,CAACkH,IAAI,EAAE;YACVsC,KAAK,EAAExG,MAAM;YACbwK,WAAW,EAAExK;UACf,CAAC,CAAC;UAEF,MAAM;YAAEwG,KAAK;YAAEgE;UAAY,CAAC,GAAGtG,IAAI;UACnC9E,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEmH,KAAK,CAAC;;UAEpE;UACA,IAAI/I,IAAI;UACR,IAAI;YACFA,IAAI,GAAG8H,QAAQ,CAAC2E,eAAe,CAAC1D,KAAK,CAAC;YACtCpH,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE5B,IAAI,GAAG,OAAO,GAAG,WAAW,CAAC;UACnF,CAAC,CAAC,OAAOgN,SAAS,EAAE;YAClBrL,OAAO,CAACmD,KAAK,CAAC,sCAAsC,EAAEkI,SAAS,CAAC;YAChE,MAAM,IAAI1N,MAAM,CAACgF,KAAK,CAAC,oBAAoB,EAAE,uBAAuB,CAAC;UACvE;UAEA,IAAI,CAACtE,IAAI,EAAE;YACT,MAAM,IAAIV,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;UACnF;UAEA3C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE5B,IAAI,CAAC4C,GAAG,CAAC;;UAEvD;UACA,MAAMiJ,aAAa,GAAG;YACpB9J,MAAM,EAAE,OAAO;YACf+J,SAAS,EAAE,OAAO;YAClBC,MAAM,EAAE,OAAO;YACfC,OAAO,EAAE;UACX,CAAC;UAED,MAAMC,cAAc,GAAG,EAAE;UACzB,IAAI,CAACJ,aAAa,CAAC9J,MAAM,CAACmK,IAAI,CAACa,WAAW,CAAC,EAAE;YAC3Cd,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;UACpE;UACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAACa,WAAW,CAAC,EAAE;YAC9Cd,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;UAC5E;UACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAACa,WAAW,CAAC,EAAE;YAC3Cd,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;UAClE;UACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAACa,WAAW,CAAC,EAAE;YAC5Cd,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;UACxF;UAEA,IAAIF,cAAc,CAAClK,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,IAAIzC,MAAM,CAACgF,KAAK,CAAC,kBAAkB,EAAE2H,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;UACvE;;UAEE;UACAzK,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;;UAEnE;UACA,MAAMqL,SAAS,WAAA9J,MAAA,CAAWiB,IAAI,CAAC8I,GAAG,CAAC,CAAC,OAAA/J,MAAA,CAAIoC,IAAI,CAAC4H,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,gBAAa;UAC5F1L,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEqL,SAAS,CAAC;UAE9E,IAAIK,UAAU;UACd,IAAI;YACFA,UAAU,GAAGxF,QAAQ,CAACuE,UAAU,CAAC;cAC/BtD,KAAK,EAAEkE,SAAS;cAChBvD,QAAQ,EAAEqD;YACZ,CAAC,CAAC;YACFpL,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE0L,UAAU,CAAC;UAC7E,CAAC,CAAC,OAAOC,WAAW,EAAE;YACpB5L,OAAO,CAACmD,KAAK,CAAC,iDAAiD,EAAEyI,WAAW,CAAC;YAC7E,MAAM,IAAIjO,MAAM,CAACgF,KAAK,CAAC,2BAA2B,EAAE,iCAAiC,CAAC;UACxF;;UAEA;UACA,IAAIkJ,QAAQ,EAAEC,YAAY;UAC1B,IAAI;YACFD,QAAQ,GAAGlO,MAAM,CAACW,KAAK,CAAC0D,OAAO,CAAC2J,UAAU,CAAC;YAC3C3L,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEiL,IAAI,CAACC,SAAS,CAACU,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAE5F,IAAI,CAACA,QAAQ,EAAE;cACb,MAAM,IAAIlJ,KAAK,CAAC,yCAAyC,CAAC;YAC5D;YAEA,IAAI,CAACkJ,QAAQ,CAACE,QAAQ,EAAE;cACtB,MAAM,IAAIpJ,KAAK,CAAC,uCAAuC,CAAC;YAC1D;YAEA,IAAI,CAACkJ,QAAQ,CAACE,QAAQ,CAAChE,QAAQ,EAAE;cAC/B,MAAM,IAAIpF,KAAK,CAAC,wCAAwC,CAAC;YAC3D;YAEAmJ,YAAY,GAAGD,QAAQ,CAACE,QAAQ,CAAChE,QAAQ;YACzC/H,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEiL,IAAI,CAACC,SAAS,CAACW,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAC/F9L,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC7E,CAAC,CAAC,OAAO+L,SAAS,EAAE;YAClBhM,OAAO,CAACmD,KAAK,CAAC,kDAAkD,EAAE6I,SAAS,CAAC;YAC5EhM,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;;YAEvE;YACA,IAAI0L,UAAU,EAAE;cACd,IAAI;gBAAEhO,MAAM,CAACW,KAAK,CAAC2N,MAAM,CAACN,UAAU,CAAC;cAAE,CAAC,CAAC,OAAOtG,CAAC,EAAE,CAAC;YACtD;;YAEA;YACA,IAAI;cACF,MAAM6G,UAAU,GAAG,EAAE;cACrB,MAAMC,cAAc,GAAG7F,MAAM,CAAC8F,QAAQ,CAAChB,WAAW,EAAEc,UAAU,CAAC;cAC/DJ,YAAY,GAAG;gBACbxF,MAAM,EAAE6F;cACV,CAAC;cACDnM,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;YAC9D,CAAC,CAAC,OAAOoM,WAAW,EAAE;cACpBrM,OAAO,CAACmD,KAAK,CAAC,+CAA+C,EAAEkJ,WAAW,CAAC;cAC3E,MAAM,IAAI1O,MAAM,CAACgF,KAAK,CAAC,+BAA+B,EAAE,gCAAgC,CAAC;YAC3F;UACF;;UAEA;UACA3C,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;UACrED,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE5B,IAAI,CAAC4C,GAAG,CAAC;UACzDjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,OAAO5B,IAAI,CAAC4C,GAAG,CAAC;UACrEjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEiL,IAAI,CAACC,SAAS,CAACW,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAE5F,IAAIpH,YAAY,GAAG,CAAC;UACpB,IAAI4H,WAAW,GAAG,IAAI;;UAEtB;UACA,MAAMC,aAAa,GAAG;UACpB;UACA,MAAM;YACJvM,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;YAChE,OAAOtC,MAAM,CAACW,KAAK,CAAC8F,MAAM,CAAC/F,IAAI,CAAC4C,GAAG,EAAE;cACnC+C,IAAI,EAAE;gBACJ,mBAAmB,EAAE8H;cACvB;YACF,CAAC,CAAC;UACJ,CAAC;UACD;UACA,MAAM;YACJ9L,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzE,OAAOtC,MAAM,CAACW,KAAK,CAAC8F,MAAM,CACxB;cAAEnD,GAAG,EAAE5C,IAAI,CAAC4C;YAAI,CAAC,EACjB;cACE+C,IAAI,EAAE;gBACJ,mBAAmB,EAAE8H;cACvB;YACF,CACF,CAAC;UACH,CAAC;UACD;UACA,MAAM;YACJ9L,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;YACtE,OAAOtC,MAAM,CAACW,KAAK,CAAC8F,MAAM,CAAC/F,IAAI,CAAC4C,GAAG,CAACwK,QAAQ,CAAC,CAAC,EAAE;cAC9CzH,IAAI,EAAE;gBACJ,mBAAmB,EAAE8H;cACvB;YACF,CAAC,CAAC;UACJ,CAAC;UACD;UACA,MAAM;YACJ9L,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;YAChE,OAAOtC,MAAM,CAACW,KAAK,CAAC8F,MAAM,CACxB;cAAEnD,GAAG,EAAE5C,IAAI,CAAC4C;YAAI,CAAC,EACjB;cACE+C,IAAI,EAAE;gBACJ,mBAAmB,EAAE8H;cACvB;YACF,CAAC,EACD;cAAEU,MAAM,EAAE;YAAM,CAClB,CAAC;UACH,CAAC,CACF;UAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAACnM,MAAM,EAAEqM,CAAC,EAAE,EAAE;YAC7C,IAAI;cACF/H,YAAY,GAAG6H,aAAa,CAACE,CAAC,CAAC,CAAC,CAAC;cACjCzM,OAAO,CAACC,GAAG,4BAAAuB,MAAA,CAA4BiL,CAAC,GAAG,CAAC,eAAY/H,YAAY,CAAC;cAErE,IAAIA,YAAY,KAAK,CAAC,EAAE;gBACtB1E,OAAO,CAACC,GAAG,mDAAAuB,MAAA,CAAmDiL,CAAC,GAAG,CAAC,CAAE,CAAC;gBACtE;cACF;YACF,CAAC,CAAC,OAAOC,WAAW,EAAE;cACpB1M,OAAO,CAACmD,KAAK,4BAAA3B,MAAA,CAA4BiL,CAAC,GAAG,CAAC,cAAWC,WAAW,CAAC;cACrEJ,WAAW,GAAGI,WAAW;YAC3B;UACF;UAEA,IAAIhI,YAAY,KAAK,CAAC,EAAE;YACtB1E,OAAO,CAACmD,KAAK,CAAC,4CAA4C,CAAC;YAC3DnD,OAAO,CAACmD,KAAK,CAAC,uCAAuC,EAAEuB,YAAY,CAAC;YACpE1E,OAAO,CAACmD,KAAK,CAAC,8BAA8B,EAAEmJ,WAAW,CAAC;;YAE1D;YACA,IAAIX,UAAU,EAAE;cACd,IAAI;gBAAEhO,MAAM,CAACW,KAAK,CAAC2N,MAAM,CAACN,UAAU,CAAC;cAAE,CAAC,CAAC,OAAOtG,CAAC,EAAE,CAAC;YACtD;YAEA,MAAM,IAAI1H,MAAM,CAACgF,KAAK,CAAC,wBAAwB,oDAAAnB,MAAA,CAAoDkD,YAAY,CAAE,CAAC;UACpH;;UAEA;UACA,IAAI;YACF/G,MAAM,CAACW,KAAK,CAAC2N,MAAM,CAACN,UAAU,CAAC;YAC/B3L,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;UAC3D,CAAC,CAAC,OAAO0M,YAAY,EAAE;YACrB3M,OAAO,CAAC8G,IAAI,CAAC,8DAA8D,EAAE6F,YAAY,CAAC;UAC5F;UAEA,IAAIjI,YAAY,KAAK,CAAC,EAAE;YACtB1E,OAAO,CAACC,GAAG,yDAAAuB,MAAA,CAAyD4F,KAAK,CAAE,CAAC;YAC5E,OAAO;cAAEwF,OAAO,EAAE,IAAI;cAAExJ,OAAO,EAAE;YAAgC,CAAC;UACpE,CAAC,MAAM;YACL,MAAM,IAAIzF,MAAM,CAACgF,KAAK,CAAC,wBAAwB,4CAAAnB,MAAA,CAA4CkD,YAAY,CAAE,CAAC;UAC5G;QAEF,CAAC,CAAC,OAAOmI,UAAU,EAAE;UACnB7M,OAAO,CAACmD,KAAK,CAAC,+CAA+C,EAAE0J,UAAU,CAAC;UAC1E7M,OAAO,CAACmD,KAAK,CAAC,6CAA6C,EAAE0J,UAAU,CAACC,KAAK,CAAC;;UAE9E;UACA,IAAID,UAAU,CAAC1J,KAAK,EAAE;YACpB,MAAM0J,UAAU;UAClB;;UAEA;UACA,MAAM,IAAIlP,MAAM,CAACgF,KAAK,CAAC,wBAAwB,6BAAAnB,MAAA,CAA6BqL,UAAU,CAACzJ,OAAO,CAAE,CAAC;QACnG;MACF,CAAC;MAED,MAAM,4BAA4B2J,CAAA,EAAG;QACnC,IAAI,CAAC,IAAI,CAAC5O,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAAqK,aAAA,EAAAC,cAAA;UACF,MAAM5O,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD6B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;YACnDkH,EAAE,EAAE9I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,GAAG;YACbmG,KAAK,EAAE/I,IAAI,aAAJA,IAAI,wBAAA2O,aAAA,GAAJ3O,IAAI,CAAE8C,MAAM,cAAA6L,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmB5F,OAAO;YACjC5I,KAAK,EAAEJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI;UACf,CAAC,CAAC;;UAEF;UACA,IAAI,CAACJ,IAAI,CAACI,KAAK,EAAE;YACf,MAAMd,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC,IAAI,CAAC5F,MAAM,EAAE;cAC1C6F,IAAI,EAAE;gBAAEvF,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YACF,OAAO,mBAAmB;UAC5B;;UAEA;UACA,IAAI,CAACJ,IAAI,CAACI,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACjCsB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;;YAE/E;YACA,MAAMiN,UAAU,GAAG,MAAMvP,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC,CAAC,CAACkJ,UAAU,CAAC,CAAC;YACzD,IAAIqF,UAAU,KAAK,CAAC,EAAE;cACpBlN,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;cAClE,MAAMtC,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC,IAAI,CAAC5F,MAAM,EAAE;gBAC1C6F,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAAC,OAAO;gBAAE;cAC3B,CAAC,CAAC;cACF,OAAO,kBAAkB;YAC3B;YACA,OAAO,mBAAmB;UAC5B;UAEA,OAAO,uBAAuB;QAChC,CAAC,CAAC,OAAO0E,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,mBAAmB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC5D;MACF,CAAC;MAED,MAAM,qBAAqB+J,CAAA,EAAG;QAC5B,IAAI,CAAC,IAAI,CAAChP,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAAyK,kBAAA;UACF,MAAMC,WAAW,GAAG,MAAM1P,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UAChE,IAAI,GAAAiP,kBAAA,GAACC,WAAW,CAAC5O,KAAK,cAAA2O,kBAAA,eAAjBA,kBAAA,CAAmB1O,QAAQ,CAAC,OAAO,CAAC,GAAE;YACzC,MAAM,IAAIf,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;UAC5E;UAEA,MAAMoE,QAAQ,GAAG,MAAMpJ,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC,CAAC,CAACqI,UAAU,CAAC,CAAC;UACvD,MAAMsG,eAAe,GAAG,EAAE;UAC1B,MAAMC,KAAK,GAAG,EAAE;UAEhB,KAAK,MAAMlP,IAAI,IAAI0I,QAAQ,EAAE;YAAA,IAAAyG,cAAA,EAAAzL,YAAA;YAC3B,MAAM0L,MAAM,GAAG,EAAE;;YAEjB;YACA,IAAI,CAACpP,IAAI,CAACI,KAAK,IAAI,CAACsC,KAAK,CAACwG,OAAO,CAAClJ,IAAI,CAACI,KAAK,CAAC,EAAE;cAAA,IAAAiP,cAAA,EAAAC,aAAA,EAAAC,cAAA;cAC7CH,MAAM,CAACjD,IAAI,CAAC,gBAAgB,CAAC;cAC7B;cACA,MAAMtC,IAAI,GAAG,EAAAwF,cAAA,GAAArP,IAAI,CAACoD,OAAO,cAAAiM,cAAA,uBAAZA,cAAA,CAAcxF,IAAI,KAAI,aAAa;cAChD,MAAMvK,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC1F,IAAI,CAAC4C,GAAG,EAAE;gBACvC+C,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAACyJ,IAAI;gBAAE;cACxB,CAAC,CAAC;cACFqF,KAAK,CAAC/C,IAAI,0BAAAhJ,MAAA,EAAAmM,aAAA,GAA0BtP,IAAI,CAAC8C,MAAM,cAAAwM,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBvG,OAAO,CAAE,CAAC;YAClE;;YAEA;YACA,IAAI,CAAAmG,cAAA,GAAAnP,IAAI,CAACoD,OAAO,cAAA+L,cAAA,eAAZA,cAAA,CAActF,IAAI,IAAI,EAAAnG,YAAA,GAAA1D,IAAI,CAACI,KAAK,cAAAsD,YAAA,uBAAVA,YAAA,CAAa,CAAC,CAAC,MAAK1D,IAAI,CAACoD,OAAO,CAACyG,IAAI,EAAE;cAAA,IAAA2F,aAAA,EAAAC,cAAA;cAC/DL,MAAM,CAACjD,IAAI,CAAC,4BAA4B,CAAC;cACzC;cACA,MAAM7M,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC1F,IAAI,CAAC4C,GAAG,EAAE;gBACvC+C,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAACJ,IAAI,CAACoD,OAAO,CAACyG,IAAI;gBAAE;cACrC,CAAC,CAAC;cACFqF,KAAK,CAAC/C,IAAI,4BAAAhJ,MAAA,EAAAqM,aAAA,GAA4BxP,IAAI,CAAC8C,MAAM,cAAA0M,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBzG,OAAO,CAAE,CAAC;YACpE;YAEA,IAAIoG,MAAM,CAACrN,MAAM,GAAG,CAAC,EAAE;cAAA,IAAA2N,aAAA,EAAAC,cAAA;cACrBV,eAAe,CAAC9C,IAAI,CAAC;gBACnBpD,KAAK,GAAA2G,aAAA,GAAE1P,IAAI,CAAC8C,MAAM,cAAA4M,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkB3G,OAAO;gBAChCoG;cACF,CAAC,CAAC;YACJ;UACF;UAEA,OAAO;YACLH,eAAe;YACfC,KAAK;YACLnK,OAAO,EAAEmK,KAAK,CAACnN,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;UACpD,CAAC;QACH,CAAC,CAAC,OAAO+C,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,iBAAiB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,4BAA4B6K,CAAA,EAAG;QAC7B;QACA,IAAI,CAACzF,OAAO,CAACC,GAAG,CAACyF,QAAQ,IAAI1F,OAAO,CAACC,GAAG,CAACyF,QAAQ,KAAK,aAAa,EAAE;UACnE,IAAI;YACF,MAAMC,UAAU,GAAG;cACjB/G,KAAK,EAAE,wBAAwB;cAC/BW,QAAQ,EAAE,cAAc;cACxBrG,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC;YAED,MAAMxD,MAAM,GAAGgI,QAAQ,CAACuE,UAAU,CAAC;cACjCtD,KAAK,EAAE+G,UAAU,CAAC/G,KAAK;cACvBW,QAAQ,EAAEoG,UAAU,CAACpG,QAAQ;cAC7BtG,OAAO,EAAE;gBACPC,SAAS,EAAEyM,UAAU,CAACzM,SAAS;gBAC/BC,QAAQ,EAAEwM,UAAU,CAACxM,QAAQ;gBAC7BuG,IAAI,EAAE,aAAa;gBACnBC,QAAQ,KAAA3G,MAAA,CAAK2M,UAAU,CAACzM,SAAS,OAAAF,MAAA,CAAI2M,UAAU,CAACxM,QAAQ;cAC1D;YACF,CAAC,CAAC;;YAEF;YACAhE,MAAM,CAACW,KAAK,CAAC8F,MAAM,CAACjG,MAAM,EAAE;cAC1B6F,IAAI,EAAE;gBAAEvF,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YAEF,OAAO;cACLmO,OAAO,EAAE,IAAI;cACbzO,MAAM;cACNiF,OAAO,EAAE;YACX,CAAC;UACH,CAAC,CAAC,OAAOD,KAAK,EAAE;YACdnD,OAAO,CAACmD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,2BAA2B,EAAEQ,KAAK,CAACC,OAAO,CAAC;UACpE;QACF,CAAC,MAAM;UACL,MAAM,IAAIzF,MAAM,CAACgF,KAAK,CAAC,iBAAiB,EAAE,8CAA8C,CAAC;QAC3F;MACF;IACF,CAAC,CAAC;IAACzF,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/app.js", "sourcesContent": ["import { Mongo } from 'meteor/mongo';\r\n\r\nexport const LinksCollection = new Mongo.Collection('links');\r\n", "import { Meteor } from 'meteor/meteor';\nimport { Mongo } from 'meteor/mongo';\nimport { check } from 'meteor/check';\nimport { Match } from 'meteor/check';\n\nexport const Tasks = new Mongo.Collection('tasks');\n\nexport const taskCategories = [\n  'Development',\n  'Design',\n  'Marketing',\n  'Sales',\n  'Support',\n  'Planning',\n  'Research',\n  'Other'\n];\n\nexport const taskLabels = [\n  { name: 'Bug', color: '#ef4444' },\n  { name: 'Feature', color: '#3b82f6' },\n  { name: 'Enhancement', color: '#10b981' },\n  { name: 'Documentation', color: '#8b5cf6' },\n  { name: 'Urgent', color: '#f59e0b' },\n  { name: 'Blocked', color: '#6b7280' }\n];\n\nif (Meteor.isServer) {\n  // Publications\n  Meteor.publish('tasks', async function() {\n    if (!this.userId) {\n      return this.ready();\n    }\n\n    // Get user's role\n    const user = await Meteor.users.findOneAsync(this.userId);\n    const isAdmin = user?.roles?.includes('admin');\n\n    // If admin, show all tasks\n    if (isAdmin) {\n      return Tasks.find({}, { \n        sort: { createdAt: -1 },\n        fields: {\n          title: 1,\n          description: 1,\n          startDate: 1,\n          dueDate: 1,\n          priority: 1,\n          status: 1,\n          assignedTo: 1,\n          checklist: 1,\n          category: 1,\n          labels: 1,\n          progress: 1,\n          attachments: 1,\n          links: 1,\n          createdAt: 1,\n          createdBy: 1,\n          updatedAt: 1,\n          updatedBy: 1\n        }\n      });\n    }\n\n    // For team members, show tasks they're assigned to or created\n    return Tasks.find({\n      $or: [\n        { assignedTo: this.userId },\n        { createdBy: this.userId }\n      ]\n    }, { \n      sort: { createdAt: -1 },\n      fields: {\n        title: 1,\n        description: 1,\n        startDate: 1,\n        dueDate: 1,\n        priority: 1,\n        status: 1,\n        assignedTo: 1,\n        checklist: 1,\n        category: 1,\n        labels: 1,\n        progress: 1,\n        attachments: 1,\n        links: 1,\n        createdAt: 1,\n        createdBy: 1,\n        updatedAt: 1,\n        updatedBy: 1\n      }\n    });\n  });\n\n  // Publish user data for tasks\n  Meteor.publish('taskUsers', function() {\n    console.log('Starting taskUsers publication');\n    if (!this.userId) {\n      console.log('No userId, returning ready');\n      return this.ready();\n    }\n\n    // Get all tasks\n    const tasks = Tasks.find({}).fetch();\n    console.log('Found tasks:', tasks.length);\n    \n    // Collect all user IDs from tasks\n    const userIds = new Set();\n    tasks.forEach(task => {\n      // Add users who uploaded attachments\n      if (task.attachments) {\n        task.attachments.forEach(attachment => {\n          if (attachment.uploadedBy) {\n            userIds.add(String(attachment.uploadedBy));\n          }\n        });\n      }\n      // Add users who added links\n      if (task.links) {\n        task.links.forEach(link => {\n          if (link.addedBy) {\n            userIds.add(String(link.addedBy));\n          }\n        });\n      }\n      // Add assigned users\n      if (task.assignedTo) {\n        task.assignedTo.forEach(userId => {\n          userIds.add(String(userId));\n        });\n      }\n    });\n\n    const userIdArray = Array.from(userIds);\n    console.log('Publishing user data for IDs:', userIdArray);\n\n    // Find users and log what we found\n    const users = Meteor.users.find(\n      { _id: { $in: userIdArray } },\n      { \n        fields: {\n          _id: 1,\n          emails: 1,\n          roles: 1,\n          'profile.firstName': 1,\n          'profile.lastName': 1,\n          'profile.role': 1,\n          'profile.department': 1,\n          'profile.skills': 1,\n          'profile.joinDate': 1,\n          createdAt: 1\n        } \n      }\n    ).fetch();\n\n    console.log('Found users:', users.map(u => ({\n      _id: u._id,\n      name: `${u.profile?.firstName || ''} ${u.profile?.lastName || ''}`.trim(),\n      hasProfile: !!u.profile\n    })));\n\n    // Return the cursor\n    return Meteor.users.find(\n      { _id: { $in: userIdArray } },\n      { \n      fields: {\n        _id: 1,\n          emails: 1,\n          roles: 1,\n        'profile.firstName': 1,\n        'profile.lastName': 1,\n          'profile.role': 1,\n          'profile.department': 1,\n          'profile.skills': 1,\n          'profile.joinDate': 1,\n          createdAt: 1\n        } \n      }\n    );\n  });\n\n  // Add a specific publication for a single task\n  Meteor.publish('task', function(taskId) {\n    check(taskId, String);\n    \n    if (!this.userId) {\n      return this.ready();\n    }\n\n    const user = Meteor.users.findOne(this.userId);\n    const isAdmin = user?.roles?.includes('admin');\n\n    // Return a cursor that will update reactively\n    const cursor = Tasks.find(\n      { _id: taskId },\n      {\n        fields: {\n          title: 1,\n          description: 1,\n          startDate: 1,\n          dueDate: 1,\n          priority: 1,\n          status: 1,\n          assignedTo: 1,\n          checklist: 1,\n          category: 1,\n          labels: 1,\n          progress: 1,\n          attachments: 1,\n          links: 1,\n          createdAt: 1,\n          createdBy: 1,\n          updatedAt: 1,\n          updatedBy: 1\n        }\n      }\n    );\n\n    // Log for debugging\n    console.log('Publishing task:', taskId);\n    cursor.observe({\n      added: (doc) => console.log('Task added to publication:', doc._id),\n      changed: (doc) => console.log('Task changed in publication:', doc._id),\n      removed: (doc) => console.log('Task removed from publication:', doc._id)\n    });\n\n    return cursor;\n  });\n}\n\nMeteor.methods({\n  async 'tasks.insert'(task) {\n    check(task, {\n      title: String,\n      description: String,\n      startDate: Date,\n      dueDate: Date,\n      priority: String,\n      status: String,\n      assignedTo: Array,\n      checklist: Array,\n      category: String,\n      labels: Array,\n      progress: Number\n    });\n\n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    console.log('Creating new task:', task); // Debug log\n\n    // Process checklist items\n    const processedChecklist = task.checklist.map(item => ({\n      text: item.text,\n      completed: item.completed || false\n    }));\n\n    const taskToInsert = {\n      ...task,\n      createdAt: new Date(),\n      createdBy: this.userId,\n      updatedAt: new Date(),\n      updatedBy: this.userId,\n      progress: task.progress || 0,\n      status: 'pending', // Default status\n      checklist: processedChecklist,\n      labels: task.labels || [],\n      category: task.category || '',\n      assignedTo: task.assignedTo || []\n    };\n\n    console.log('Inserting task with values:', taskToInsert); // Debug log\n\n    try {\n      const result = await Tasks.insertAsync(taskToInsert);\n      console.log('Task created successfully:', result); // Debug log\n      return result;\n    } catch (error) {\n      console.error('Error creating task:', error);\n      throw new Meteor.Error('task-creation-failed', error.message);\n    }\n  },\n\n  async 'tasks.update'(taskId, task) {\n    try {\n      console.log('Starting task update:', { taskId, task });\n\n      check(taskId, String);\n      check(task, {\n        title: String,\n        description: String,\n        startDate: Date,\n        dueDate: Date,\n        priority: String,\n        assignedTo: Array,\n        checklist: Array,\n        category: Match.Optional(String),\n        labels: Match.Optional(Array),\n        progress: Match.Optional(Number),\n        status: Match.Optional(String),\n        attachments: Match.Optional(Array)\n      });\n\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const existingTask = await Tasks.findOneAsync(taskId);\n      if (!existingTask) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task or is admin\n      const user = await Meteor.users.findOneAsync(this.userId);\n      const isAdmin = user?.roles?.includes('admin');\n      \n      if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {\n        throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');\n      }\n\n      // Calculate progress based on checklist\n      const completedItems = task.checklist.filter(item => item.completed).length;\n      const totalItems = task.checklist.length;\n      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;\n\n      // Update task status based on progress\n      let status = task.status;\n      if (progress === 100) {\n        status = 'completed';\n      } else if (progress > 0) {\n        status = 'in-progress';\n      } else {\n        status = 'pending';\n      }\n\n      const taskToUpdate = {\n        ...task,\n        updatedAt: new Date(),\n        updatedBy: this.userId,\n        progress,\n        status,\n        category: task.category || existingTask.category || '',\n        labels: task.labels || existingTask.labels || [],\n        attachments: task.attachments || existingTask.attachments || []\n      };\n\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        { $set: taskToUpdate }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to update task');\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error updating task:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.delete'(taskId) {\n    check(taskId, String);\n\n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    try {\n      const result = await Tasks.removeAsync(taskId);\n      \n      if (result === 0) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error deleting task:', error);\n      throw new Meteor.Error('task-delete-failed', error.message);\n    }\n  },\n\n  'tasks.updateProgress'(taskId, progress) {\n    check(taskId, String);\n    check(progress, Number);\n\n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    const task = Tasks.findOne(taskId);\n    if (!task) {\n      throw new Meteor.Error('Task not found.');\n    }\n\n    // Check if user is assigned to the task\n    if (!task.assignedTo.includes(this.userId)) {\n      throw new Meteor.Error('Not authorized to modify this task.');\n    }\n\n    // Update task status based on progress\n    let status = task.status;\n    if (progress === 100) {\n      status = 'completed';\n    } else if (progress > 0) {\n      status = 'in-progress';\n    }\n\n    return Tasks.update(taskId, {\n      $set: {\n        progress,\n        status,\n        updatedAt: new Date(),\n        updatedBy: this.userId\n      }\n    });\n  },\n\n  async 'tasks.toggleChecklistItem'(taskId, itemIndex) {\n    try {\n      console.log('Starting toggleChecklistItem with:', { taskId, itemIndex, userId: this.userId });\n\n      if (!this.userId) {\n        console.log('No user ID found');\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      // Validate inputs\n      if (!taskId || typeof taskId !== 'string') {\n        console.log('Invalid taskId:', taskId);\n        throw new Meteor.Error('invalid-input', 'Invalid task ID');\n      }\n\n      if (typeof itemIndex !== 'number' || itemIndex < 0) {\n        console.log('Invalid itemIndex:', itemIndex);\n        throw new Meteor.Error('invalid-input', 'Invalid checklist item index');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      console.log('Found task:', task);\n\n      if (!task) {\n        console.log('Task not found');\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task or is admin\n      const user = await Meteor.users.findOneAsync(this.userId);\n      console.log('Found user:', user);\n      \n      const isAdmin = user?.roles?.includes('admin');\n      console.log('Is admin:', isAdmin);\n      \n      if (!isAdmin && !task.assignedTo.includes(this.userId)) {\n        console.log('User not authorized:', { userId: this.userId, assignedTo: task.assignedTo });\n        throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');\n      }\n\n      const checklist = task.checklist || [];\n      console.log('Current checklist:', checklist);\n\n      if (itemIndex >= checklist.length) {\n        console.log('Invalid item index:', { itemIndex, checklistLength: checklist.length });\n        throw new Meteor.Error('invalid-index', 'Invalid checklist item index');\n      }\n\n      // Create a new array to ensure reactivity\n      const updatedChecklist = [...checklist];\n      updatedChecklist[itemIndex] = {\n        ...updatedChecklist[itemIndex],\n        completed: !updatedChecklist[itemIndex].completed\n      };\n\n      console.log('Updated checklist:', updatedChecklist);\n\n      // Calculate progress\n      const completedItems = updatedChecklist.filter(item => item.completed).length;\n      const totalItems = updatedChecklist.length;\n      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;\n\n      // Update task status based on progress\n      let status;\n      if (progress === 100) {\n        status = 'completed';\n      } else if (progress > 0) {\n        status = 'in-progress';\n      } else {\n        status = 'pending';\n      }\n\n      console.log('Updating task with:', {\n        taskId,\n        updatedChecklist,\n        progress,\n        status\n      });\n\n      // First verify the task still exists\n      const existingTask = await Tasks.findOneAsync(taskId);\n      if (!existingTask) {\n        throw new Meteor.Error('task-not-found', 'Task no longer exists');\n      }\n\n      // Perform the update\n      const updateResult = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $set: {\n            checklist: updatedChecklist,\n            progress,\n            status,\n            updatedAt: new Date(),\n            updatedBy: this.userId\n          }\n        }\n      );\n\n      console.log('Update result:', updateResult);\n\n      if (updateResult === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to update task');\n      }\n\n      // Verify the update\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after update:', updatedTask);\n\n      return updateResult;\n    } catch (error) {\n      console.error('Error in toggleChecklistItem:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.addAttachment'(taskId, fileData) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task\n      if (!task.assignedTo.includes(this.userId)) {\n        throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');\n      }\n\n      if (!fileData || !fileData.name || !fileData.data) {\n        throw new Meteor.Error('invalid-input', 'Invalid file data');\n      }\n\n      // Ensure we're storing the user ID as a string\n      const uploaderId = String(this.userId);\n\n      // Add the file data to attachments with uploader info\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $push: {\n            attachments: {\n              name: fileData.name,\n              type: fileData.type,\n              data: fileData.data,\n              uploadedAt: new Date(),\n              uploadedBy: uploaderId\n            }\n          },\n          $set: {\n            updatedAt: new Date(),\n            updatedBy: uploaderId\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to add attachment');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after adding attachment:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error adding attachment:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.addLink'(taskId, link) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task\n      if (!task.assignedTo.includes(this.userId)) {\n        throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');\n      }\n\n      if (!link) {\n        throw new Meteor.Error('invalid-input', 'Link URL is required');\n      }\n\n      // Validate URL format\n      try {\n        new URL(link);\n      } catch (e) {\n        throw new Meteor.Error('invalid-input', 'Invalid URL format');\n      }\n\n      // Add the link to links array with adder info\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $push: {\n            links: {\n              url: link,\n              addedAt: new Date(),\n              addedBy: String(this.userId)\n            }\n          },\n          $set: {\n            updatedAt: new Date(),\n            updatedBy: String(this.userId)\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to add link');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after adding link:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error adding link:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.removeAttachment'(taskId, attachmentIndex) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if the attachment exists\n      if (!task.attachments || !task.attachments[attachmentIndex]) {\n        throw new Meteor.Error('not-found', 'Attachment not found');\n      }\n\n      const attachment = task.attachments[attachmentIndex];\n      \n      // Convert both IDs to strings for comparison\n      const currentUserId = String(this.userId);\n      const uploadedById = String(attachment.uploadedBy);\n      \n      // Only allow the uploader to remove the attachment\n      if (currentUserId !== uploadedById) {\n        throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');\n      }\n\n      // Create a new array without the specified attachment\n      const updatedAttachments = [...task.attachments];\n      updatedAttachments.splice(attachmentIndex, 1);\n\n      // Update the task with the new attachments array\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $set: {\n            attachments: updatedAttachments,\n            updatedAt: new Date(),\n            updatedBy: currentUserId\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to remove attachment');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after removing attachment:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error removing attachment:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.removeLink'(taskId, linkIndex) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if the link exists\n      if (!task.links || !task.links[linkIndex]) {\n        throw new Meteor.Error('not-found', 'Link not found');\n      }\n\n      const link = task.links[linkIndex];\n      \n      // Convert both IDs to strings for comparison\n      const currentUserId = String(this.userId);\n      const addedById = String(link.addedBy);\n      \n      // Only allow the user who added the link to remove it\n      if (currentUserId !== addedById) {\n        throw new Meteor.Error('not-authorized', 'You can only remove your own links');\n      }\n\n      // Create a new array without the specified link\n      const updatedLinks = [...task.links];\n      updatedLinks.splice(linkIndex, 1);\n\n      // Update the task with the new links array\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $set: {\n            links: updatedLinks,\n            updatedAt: new Date(),\n            updatedBy: currentUserId\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to remove link');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after removing link:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error removing link:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.findOne'(taskId) {\n    check(taskId, String);\n    \n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    const task = await Tasks.findOneAsync(taskId);\n    if (!task) {\n      throw new Meteor.Error('task-not-found', 'Task not found');\n    }\n\n    return task;\n  },\n}); ", "import { Meteor } from 'meteor/meteor';\nimport { LinksCollection } from '/imports/api/links';\nimport { Accounts } from 'meteor/accounts-base';\nimport { Email } from 'meteor/email';\nimport { Tasks } from '/imports/api/tasks';\nimport { Roles } from 'meteor/alanning:roles';\nimport { check } from 'meteor/check';\nimport bcrypt from 'bcrypt';\n\nasync function insertLink({ title, url }) {\n  await LinksCollection.insertAsync({ title, url, createdAt: new Date() });\n}\n\nconst ADMIN_TOKEN = '123456';\n\nMeteor.startup(async () => {\n  // Ensure indexes for Tasks collection\n  try {\n    await Tasks.createIndex({ createdAt: 1 });\n    await Tasks.createIndex({ assignedTo: 1 });\n    await Tasks.createIndex({ createdBy: 1 });\n\n    // Ensure indexes for Users collection\n    // Note: emails.address index is already created by Meteor accounts system\n    await Meteor.users.createIndex({ roles: 1 }, { background: true });\n  } catch (error) {\n    console.warn('[Startup] Index creation warning:', error.message);\n  }\n\n  // Check if we have any team members\n  try {\n    const allUsers = await Meteor.users.find().fetchAsync();\n    console.log('[Startup] All users:', allUsers.map(user => ({\n      id: user._id,\n      email: user.emails?.[0]?.address,\n      roles: user.roles,\n      profile: user.profile\n    })));\n\n    // First, ensure all users have roles and createdAt\n    for (const user of allUsers) {\n      const updates = {};\n      \n      if (!user.roles || !Array.isArray(user.roles)) {\n        updates.roles = ['team-member'];\n      }\n      \n      if (!user.createdAt) {\n        updates.createdAt = new Date();\n      }\n      \n      if (Object.keys(updates).length > 0) {\n        console.log('[Startup] Fixing missing fields for user:', user.emails?.[0]?.address);\n        await Meteor.users.updateAsync(user._id, {\n          $set: updates\n        });\n      }\n    }\n\n    const teamMembersCount = await Meteor.users.find({ 'roles': 'team-member' }).countAsync();\n    console.log('[Startup] Found team members:', teamMembersCount);\n\n    // Create test team members if none exist\n    if (teamMembersCount === 0) {\n      console.log('[Startup] Creating test team members');\n      try {\n        // Create multiple test team members\n        const testMembers = [\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'John',\n            lastName: 'Doe'\n          },\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'Jane',\n            lastName: 'Smith'\n          }\n        ];\n\n        for (const member of testMembers) {\n          const userId = await Accounts.createUserAsync({\n            email: member.email,\n            password: member.password,\n            role: 'team-member',\n            createdAt: new Date(),\n            profile: {\n              firstName: member.firstName,\n              lastName: member.lastName,\n              role: 'team-member',\n              fullName: `${member.firstName} ${member.lastName}`\n            }\n          });\n\n          // Set the role explicitly\n          await Meteor.users.updateAsync(userId, {\n            $set: { roles: ['team-member'] }\n          });\n\n          console.log('[Startup] Created test team member:', {\n            id: userId,\n            email: member.email,\n            name: `${member.firstName} ${member.lastName}`\n          });\n        }\n      } catch (error) {\n        console.error('[Startup] Error creating test team members:', error);\n      }\n    }\n  } catch (error) {\n    console.error('[Startup] Error checking team members:', error);\n  }\n\n  // Email configuration from settings\n  const emailSettings = Meteor.settings.private?.email;\n  \n  // Configure email SMTP\n  if (emailSettings?.username && emailSettings?.password) {\n    process.env.MAIL_URL = `smtp://${encodeURIComponent(emailSettings.username)}:${encodeURIComponent(emailSettings.password)}@${emailSettings.server}:${emailSettings.port}`;\n    \n    // Test email configuration\n    try {\n      console.log('Testing email configuration...');\n      Email.send({\n        to: emailSettings.username,\n        from: emailSettings.username,\n        subject: 'Test Email',\n        text: 'If you receive this email, your email configuration is working correctly.'\n      });\n      console.log('Test email sent successfully!');\n    } catch (error) {\n      console.error('Error sending test email:', error);\n    }\n  } else {\n    console.warn('Email configuration is missing in settings.json');\n  }\n\n  // Configure account creation to require email verification\n  Accounts.config({\n    sendVerificationEmail: true,\n    forbidClientAccountCreation: false\n  });\n\n  // Customize verification email\n  Accounts.emailTemplates.siteName = \"Task Management System\";\n  Accounts.emailTemplates.from = emailSettings?.username ? \n    `Task Management System <${emailSettings.username}>` : \n    \"Task Management System <<EMAIL>>\";\n\n  Accounts.emailTemplates.verifyEmail = {\n    subject() {\n      return \"Verify Your Email Address\";\n    },\n    text(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `Hello,\\n\\n`\n        + `To verify your email address (${emailAddress}), please click the link below:\\n\\n`\n        + `${url}\\n\\n`\n        + `If you did not request this verification, please ignore this email.\\n\\n`\n        + `Thanks,\\n`\n        + `Your Task Management System Team`;\n    },\n    html(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (${emailAddress}), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"${url}\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      `;\n    }\n  };\n\n  // If the Links collection is empty, add some data.\n  if (await LinksCollection.find().countAsync() === 0) {\n    await insertLink({\n      title: 'Do the Tutorial',\n      url: 'https://www.meteor.com/tutorials/react/creating-an-app',\n    });\n\n    await insertLink({\n      title: 'Follow the Guide',\n      url: 'https://guide.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Read the Docs',\n      url: 'https://docs.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Discussions',\n      url: 'https://forums.meteor.com',\n    });\n  }\n\n  // We publish the entire Links collection to all clients.\n  // In order to be fetched in real-time to the clients\n  Meteor.publish(\"links\", function () {\n    return LinksCollection.find();\n  });\n\n  // Add custom fields to users\n  Accounts.onCreateUser((options, user) => {\n    console.log('[onCreateUser] Creating user with options:', {\n      email: options.email,\n      role: options.role,\n      profile: options.profile,\n      createdAt: options.createdAt\n    });\n\n    const customizedUser = { ...user };\n    \n    // Ensure we have a profile\n    customizedUser.profile = options.profile || {};\n    \n    // Add role from options\n    const role = options.role || 'team-member';\n    customizedUser.roles = [role];\n    \n    // Set createdAt if provided, otherwise use current date\n    customizedUser.createdAt = options.createdAt || new Date();\n    \n    console.log('[onCreateUser] Created user:', {\n      id: customizedUser._id,\n      email: customizedUser.emails?.[0]?.address,\n      roles: customizedUser.roles,\n      profile: customizedUser.profile,\n      createdAt: customizedUser.createdAt\n    });\n    \n    return customizedUser;\n  });\n\n  // Publish team members\n  Meteor.publish('teamMembers', function() {\n    console.log('[teamMembers] Publication called, userId:', this.userId);\n    \n    if (!this.userId) {\n      console.log('[teamMembers] No userId, returning ready');\n      return this.ready();\n    }\n\n    try {\n      // Simple query to find all team members\n      const teamMembers = Meteor.users.find(\n        { \n          $or: [\n            { 'roles': 'team-member' },\n            { 'profile.role': 'team-member' }\n          ]\n        },\n        { \n          fields: { \n            emails: 1, \n            roles: 1,\n            'profile.firstName': 1,\n            'profile.lastName': 1,\n            'profile.fullName': 1,\n            createdAt: 1\n          }\n        }\n      );\n\n      console.log('[teamMembers] Publishing team members');\n      return teamMembers;\n    } catch (error) {\n      console.error('[teamMembers] Error in publication:', error);\n      return this.ready();\n    }\n  });\n\n  // Publish user data with roles\n  Meteor.publish('userData', function() {\n    if (!this.userId) {\n      return this.ready();\n    }\n\n    console.log('[userData] Publishing data for user:', this.userId);\n    \n    return Meteor.users.find(\n      { _id: this.userId },\n      { \n        fields: { \n          roles: 1, \n          emails: 1,\n          profile: 1\n        } \n      }\n    );\n  });\n});\n\n// Method to create a new user with role\nMeteor.methods({\n  'users.create'({ email, password, role, adminToken, firstName, lastName }) {\n    // Validate admin token if trying to create admin account\n    if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n      throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n    }\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(password)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(password)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(password)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(password)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Create the user\n    try {\n      const userId = Accounts.createUser({\n        email,\n        password,\n        role, // This will be used in onCreateUser callback\n        profile: {\n          role, // Store in profile as well for easy access\n          firstName,\n          lastName,\n          fullName: `${firstName} ${lastName}`\n        }\n      });\n\n      // Send verification email\n      if (userId) {\n        Accounts.sendVerificationEmail(userId);\n      }\n\n      return userId;\n    } catch (error) {\n      throw new Meteor.Error('create-user-failed', error.message);\n    }\n  },\n\n  async 'users.getRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'User must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      if (!user) {\n        throw new Meteor.Error('user-not-found', 'User not found');\n      }\n      \n      // Check both roles array and profile for role\n      const role = user.roles?.[0] || user.profile?.role || 'team-member';\n      return role;\n    } catch (error) {\n      throw new Meteor.Error('get-role-failed', error.message);\n    }\n  },\n\n  'users.resendVerificationEmail'(email) {\n    // Find user by email\n    const user = Accounts.findUserByEmail(email);\n    if (!user) {\n      throw new Meteor.Error('user-not-found', 'No user found with this email address');\n    }\n\n    // Check if email is already verified\n    const userEmail = user.emails[0];\n    if (userEmail.verified) {\n      throw new Meteor.Error('already-verified', 'This email is already verified');\n    }\n\n    // Send verification email\n    try {\n      Accounts.sendVerificationEmail(user._id, email);\n      return true;\n    } catch (error) {\n      throw new Meteor.Error('verification-email-failed', error.message);\n    }\n  },\n\n  'users.forgotPassword'(data) {\n    try {\n      console.log('[forgotPassword] Method called with data:', JSON.stringify(data));\n\n      check(data, {\n        email: String,\n        newPassword: String\n      });\n\n      const { email, newPassword } = data;\n      console.log('[forgotPassword] Processing request for email:', email);\n\n      // Find user by email using Accounts method (most reliable)\n      let user;\n      try {\n        user = Accounts.findUserByEmail(email);\n        console.log('[forgotPassword] User lookup result:', user ? 'FOUND' : 'NOT FOUND');\n      } catch (findError) {\n        console.error('[forgotPassword] Error finding user:', findError);\n        throw new Meteor.Error('user-lookup-failed', 'Error looking up user');\n      }\n\n      if (!user) {\n        throw new Meteor.Error('user-not-found', 'No user found with this email address');\n      }\n\n      console.log('[forgotPassword] User found:', user._id);\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(newPassword)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n      // Update password using the most reliable method\n      console.log('[forgotPassword] Starting password update process...');\n\n      // Create a temporary user to get the correct password hash structure\n      const tempEmail = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}@temp.local`;\n      console.log('[forgotPassword] Creating temporary user with email:', tempEmail);\n\n      let tempUserId;\n      try {\n        tempUserId = Accounts.createUser({\n          email: tempEmail,\n          password: newPassword\n        });\n        console.log('[forgotPassword] Temporary user created with ID:', tempUserId);\n      } catch (createError) {\n        console.error('[forgotPassword] Error creating temporary user:', createError);\n        throw new Meteor.Error('temp-user-creation-failed', 'Failed to create temporary user');\n      }\n\n      // Get the password hash from the temporary user\n      let tempUser, passwordHash;\n      try {\n        tempUser = Meteor.users.findOne(tempUserId);\n        console.log('[forgotPassword] Temporary user structure:', JSON.stringify(tempUser, null, 2));\n\n        if (!tempUser) {\n          throw new Error('Temporary user not found after creation');\n        }\n\n        if (!tempUser.services) {\n          throw new Error('Temporary user has no services object');\n        }\n\n        if (!tempUser.services.password) {\n          throw new Error('Temporary user has no password service');\n        }\n\n        passwordHash = tempUser.services.password;\n        console.log('[forgotPassword] Password hash structure:', JSON.stringify(passwordHash, null, 2));\n        console.log('[forgotPassword] Password hash extracted from temporary user');\n      } catch (hashError) {\n        console.error('[forgotPassword] Error extracting password hash:', hashError);\n        console.log('[forgotPassword] Trying fallback approach with bcrypt...');\n\n        // Clean up temp user if it exists\n        if (tempUserId) {\n          try { Meteor.users.remove(tempUserId); } catch (e) {}\n        }\n\n        // Fallback: Use bcrypt directly\n        try {\n          const saltRounds = 10;\n          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);\n          passwordHash = {\n            bcrypt: hashedPassword\n          };\n          console.log('[forgotPassword] Fallback bcrypt hash created');\n        } catch (bcryptError) {\n          console.error('[forgotPassword] Bcrypt fallback also failed:', bcryptError);\n          throw new Meteor.Error('password-hash-creation-failed', 'Failed to create password hash');\n        }\n      }\n\n      // Update the target user's password\n      console.log('[forgotPassword] Attempting to update user password...');\n      console.log('[forgotPassword] Target user ID:', user._id);\n      console.log('[forgotPassword] Target user ID type:', typeof user._id);\n      console.log('[forgotPassword] Password hash to set:', JSON.stringify(passwordHash, null, 2));\n\n      let updateResult = 0;\n      let updateError = null;\n\n      // Try multiple update approaches\n      const updateMethods = [\n        // Method 1: Standard update\n        () => {\n          console.log('[forgotPassword] Trying Method 1: Standard update');\n          return Meteor.users.update(user._id, {\n            $set: {\n              'services.password': passwordHash\n            }\n          });\n        },\n        // Method 2: Update with query object\n        () => {\n          console.log('[forgotPassword] Trying Method 2: Update with query object');\n          return Meteor.users.update(\n            { _id: user._id },\n            {\n              $set: {\n                'services.password': passwordHash\n              }\n            }\n          );\n        },\n        // Method 3: Update with string ID\n        () => {\n          console.log('[forgotPassword] Trying Method 3: Update with string ID');\n          return Meteor.users.update(user._id.toString(), {\n            $set: {\n              'services.password': passwordHash\n            }\n          });\n        },\n        // Method 4: Upsert approach\n        () => {\n          console.log('[forgotPassword] Trying Method 4: Upsert approach');\n          return Meteor.users.update(\n            { _id: user._id },\n            {\n              $set: {\n                'services.password': passwordHash\n              }\n            },\n            { upsert: false }\n          );\n        }\n      ];\n\n      for (let i = 0; i < updateMethods.length; i++) {\n        try {\n          updateResult = updateMethods[i]();\n          console.log(`[forgotPassword] Method ${i + 1} result:`, updateResult);\n\n          if (updateResult === 1) {\n            console.log(`[forgotPassword] Update successful with method ${i + 1}`);\n            break;\n          }\n        } catch (methodError) {\n          console.error(`[forgotPassword] Method ${i + 1} error:`, methodError);\n          updateError = methodError;\n        }\n      }\n\n      if (updateResult !== 1) {\n        console.error('[forgotPassword] All update methods failed');\n        console.error('[forgotPassword] Final update result:', updateResult);\n        console.error('[forgotPassword] Last error:', updateError);\n\n        // Clean up temp user\n        if (tempUserId) {\n          try { Meteor.users.remove(tempUserId); } catch (e) {}\n        }\n\n        throw new Meteor.Error('password-update-failed', `Failed to update user password. Update result: ${updateResult}`);\n      }\n\n      // Clean up temporary user\n      try {\n        Meteor.users.remove(tempUserId);\n        console.log('[forgotPassword] Temporary user cleaned up');\n      } catch (cleanupError) {\n        console.warn('[forgotPassword] Warning: Failed to clean up temporary user:', cleanupError);\n      }\n\n      if (updateResult === 1) {\n        console.log(`[forgotPassword] Password reset successful for user: ${email}`);\n        return { success: true, message: 'Password updated successfully' };\n      } else {\n        throw new Meteor.Error('password-update-failed', `Password update failed. Update result: ${updateResult}`);\n      }\n\n    } catch (outerError) {\n      console.error('[forgotPassword] Outer catch - Error details:', outerError);\n      console.error('[forgotPassword] Outer catch - Error stack:', outerError.stack);\n\n      // If it's already a Meteor.Error, re-throw it\n      if (outerError.error) {\n        throw outerError;\n      }\n\n      // Otherwise, wrap it in a Meteor.Error\n      throw new Meteor.Error('forgot-password-failed', `Forgot password failed: ${outerError.message}`);\n    }\n  },\n\n  async 'users.checkAndFixAdminRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      console.log('[checkAndFixAdminRole] Checking user:', {\n        id: user?._id,\n        email: user?.emails?.[0]?.address,\n        roles: user?.roles\n      });\n      \n      // If user has no roles array, initialize it\n      if (!user.roles) {\n        await Meteor.users.updateAsync(this.userId, {\n          $set: { roles: ['team-member'] }\n        });\n        return 'Roles initialized';\n      }\n      \n      // If user has no roles or doesn't have admin role\n      if (!user.roles.includes('admin')) {\n        console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n        \n        // Check if this is the first user (they should be admin)\n        const totalUsers = await Meteor.users.find().countAsync();\n        if (totalUsers === 1) {\n          console.log('[checkAndFixAdminRole] First user, setting as admin');\n          await Meteor.users.updateAsync(this.userId, {\n            $set: { roles: ['admin'] }\n          });\n          return 'Admin role added';\n        }\n        return 'User is not admin';\n      }\n      \n      return 'User is already admin';\n    } catch (error) {\n      console.error('[checkAndFixAdminRole] Error:', error);\n      throw new Meteor.Error('check-role-failed', error.message);\n    }\n  },\n\n  async 'users.diagnoseRoles'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n\n    try {\n      const currentUser = await Meteor.users.findOneAsync(this.userId);\n      if (!currentUser.roles?.includes('admin')) {\n        throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n      }\n\n      const allUsers = await Meteor.users.find().fetchAsync();\n      const usersWithIssues = [];\n      const fixes = [];\n\n      for (const user of allUsers) {\n        const issues = [];\n        \n        // Check if roles array exists\n        if (!user.roles || !Array.isArray(user.roles)) {\n          issues.push('No roles array');\n          // Fix: Initialize roles based on profile\n          const role = user.profile?.role || 'team-member';\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [role] }\n          });\n          fixes.push(`Initialized roles for ${user.emails?.[0]?.address}`);\n        }\n        \n        // Check if role matches profile\n        if (user.profile?.role && user.roles?.[0] !== user.profile.role) {\n          issues.push('Role mismatch with profile');\n          // Fix: Update roles to match profile\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [user.profile.role] }\n          });\n          fixes.push(`Fixed role mismatch for ${user.emails?.[0]?.address}`);\n        }\n\n        if (issues.length > 0) {\n          usersWithIssues.push({\n            email: user.emails?.[0]?.address,\n            issues\n          });\n        }\n      }\n\n      return {\n        usersWithIssues,\n        fixes,\n        message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n      };\n    } catch (error) {\n      throw new Meteor.Error('diagnose-failed', error.message);\n    }\n  },\n\n  'users.createTestTeamMember'() {\n    // Only allow in development\n    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n      try {\n        const testMember = {\n          email: '<EMAIL>',\n          password: 'TestPass123!',\n          firstName: 'Test',\n          lastName: 'Member'\n        };\n\n        const userId = Accounts.createUser({\n          email: testMember.email,\n          password: testMember.password,\n          profile: {\n            firstName: testMember.firstName,\n            lastName: testMember.lastName,\n            role: 'team-member',\n            fullName: `${testMember.firstName} ${testMember.lastName}`\n          }\n        });\n\n        // Set the role explicitly\n        Meteor.users.update(userId, {\n          $set: { roles: ['team-member'] }\n        });\n\n        return {\n          success: true,\n          userId,\n          message: 'Test team member created successfully'\n        };\n      } catch (error) {\n        console.error('[createTestTeamMember] Error:', error);\n        throw new Meteor.Error('create-test-member-failed', error.message);\n      }\n    } else {\n      throw new Meteor.Error('not-development', 'This method is only available in development');\n    }\n  }\n});"]}