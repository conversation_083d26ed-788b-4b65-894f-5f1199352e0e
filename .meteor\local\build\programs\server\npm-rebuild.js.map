{"version": 3, "names": ["process", "env", "METEOR_SKIP_NPM_REBUILD", "exit", "fs", "require", "path", "spawn", "rebuildArgs", "get", "rebuilds", "e", "code", "binDir", "dirname", "execPath", "PATH", "delimiter", "npmCmd", "shell", "platform", "rebuild", "i", "dir", "stdio", "cwd", "join", "__dirname", "on"], "sources": ["/tools/static-assets/server/npm-rebuild.js"], "sourcesContent": ["// If a developer wants to go to the trouble of building on exactly the\n// same architecture as the production machine, then it should be possible\n// to skip running `npm rebuild`.\nif (process.env.METEOR_SKIP_NPM_REBUILD) {\n  process.exit(0);\n}\n\nvar fs = require(\"fs\");\nvar path = require(\"path\");\nvar spawn = require(\"child_process\").spawn;\nvar rebuildArgs = require(\"./npm-rebuild-args.js\").get();\n\ntry {\n  // This JSON file gets written in meteor/tools/isobuild/bundler.js.\n  var rebuilds = require(\"./npm-rebuilds.json\");\n} catch (e) {\n  if (e.code !== \"MODULE_NOT_FOUND\") {\n    throw e;\n  }\n\n  // If npm-rebuilds.json was not written, assume there is nothing that\n  // needs to be rebuilt.\n  process.exit(0);\n}\n\n// Make sure the npm finds this exact version of node in its $PATH.\nvar binDir = path.dirname(process.execPath);\nprocess.env.PATH = binDir + path.delimiter + process.env.PATH;\n\nvar npmCmd = \"npm\";\nvar shell = false;\nif (process.platform === \"win32\") {\n  npmCmd = \"npm.cmd\";\n  shell = true;\n}\n\nfunction rebuild(i) {\n  var dir = rebuilds && rebuilds[i];\n\n  if (! dir) {\n    // Print Node/V8/etc. versions for diagnostic purposes.\n    spawn(npmCmd, [\"version\", \"--json\"], {\n      stdio: \"inherit\",\n      shell,\n    });\n\n    return;\n  }\n\n  spawn(npmCmd, rebuildArgs, {\n    cwd: path.join(__dirname, dir),\n    stdio: \"inherit\",\n    shell,\n  }).on(\"exit\", function (code) {\n    if (code !== 0) {\n      process.exit(code);\n    } else {\n      rebuild(i + 1);\n    }\n  });\n}\n\nrebuild(0);\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,OAAO,CAACC,GAAG,CAACC,uBAAuB,EAAE;EACvCF,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC;AACjB;AAEA,IAAIC,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AACtB,IAAIC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;AAC1B,IAAIE,KAAK,GAAGF,OAAO,CAAC,eAAe,CAAC,CAACE,KAAK;AAC1C,IAAIC,WAAW,GAAGH,OAAO,CAAC,uBAAuB,CAAC,CAACI,GAAG,CAAC,CAAC;AAExD,IAAI;EACF;EACA,IAAIC,QAAQ,GAAGL,OAAO,CAAC,qBAAqB,CAAC;AAC/C,CAAC,CAAC,OAAOM,CAAC,EAAE;EACV,IAAIA,CAAC,CAACC,IAAI,KAAK,kBAAkB,EAAE;IACjC,MAAMD,CAAC;EACT;;EAEA;EACA;EACAX,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC;AACjB;;AAEA;AACA,IAAIU,MAAM,GAAGP,IAAI,CAACQ,OAAO,CAACd,OAAO,CAACe,QAAQ,CAAC;AAC3Cf,OAAO,CAACC,GAAG,CAACe,IAAI,GAAGH,MAAM,GAAGP,IAAI,CAACW,SAAS,GAAGjB,OAAO,CAACC,GAAG,CAACe,IAAI;AAE7D,IAAIE,MAAM,GAAG,KAAK;AAClB,IAAIC,KAAK,GAAG,KAAK;AACjB,IAAInB,OAAO,CAACoB,QAAQ,KAAK,OAAO,EAAE;EAChCF,MAAM,GAAG,SAAS;EAClBC,KAAK,GAAG,IAAI;AACd;AAEA,SAASE,OAAOA,CAACC,CAAC,EAAE;EAClB,IAAIC,GAAG,GAAGb,QAAQ,IAAIA,QAAQ,CAACY,CAAC,CAAC;EAEjC,IAAI,CAAEC,GAAG,EAAE;IACT;IACAhB,KAAK,CAACW,MAAM,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE;MACnCM,KAAK,EAAE,SAAS;MAChBL;IACF,CAAC,CAAC;IAEF;EACF;EAEAZ,KAAK,CAACW,MAAM,EAAEV,WAAW,EAAE;IACzBiB,GAAG,EAAEnB,IAAI,CAACoB,IAAI,CAACC,SAAS,EAAEJ,GAAG,CAAC;IAC9BC,KAAK,EAAE,SAAS;IAChBL;EACF,CAAC,CAAC,CAACS,EAAE,CAAC,MAAM,EAAE,UAAUhB,IAAI,EAAE;IAC5B,IAAIA,IAAI,KAAK,CAAC,EAAE;MACdZ,OAAO,CAACG,IAAI,CAACS,IAAI,CAAC;IACpB,CAAC,MAAM;MACLS,OAAO,CAACC,CAAC,GAAG,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;AAEAD,OAAO,CAAC,CAAC,CAAC", "ignoreList": [], "file": "tools/static-assets/server/npm-rebuild.js.map"}