import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Meteor } from 'meteor/meteor';
import { TeamAIInsightsPanel } from './TeamAIInsightsPanel'; // Assuming this component exists
import { useTracker } from 'meteor/react-meteor-data';

export const TeamTaskView = ({ task, onCancel }) => {
  // State for managing UI interactions and data
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newLink, setNewLink] = useState('');
  const [file, setFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [userNames, setUserNames] = useState({});
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);

  // Hook for navigation
  const navigate = useNavigate();

  // Subscribe to tasks and user data
  const { tasks } = useTracker(() => {
    const tasksHandle = Meteor.subscribe('tasks');
    const userDataHandle = Meteor.subscribe('userData');
    const taskUsersHandle = Meteor.subscribe('taskUsers');
    
    return {
      tasks: tasksHandle.ready() && userDataHandle.ready() && taskUsersHandle.ready()
    };
  });

  // Load user data when task changes
  useEffect(() => {
    const loadUserData = async () => {
      if (!task) return;
      
      setIsLoadingUsers(true);
      try {
        // Collect all user IDs from the task
    const userIds = new Set();

        // Add users who uploaded attachments
    if (task.attachments) {
      task.attachments.forEach(attachment => {
        if (attachment.uploadedBy) {
          userIds.add(String(attachment.uploadedBy));
        }
      });
    }

        // Add users who added links
    if (task.links) {
      task.links.forEach(link => {
        if (link.addedBy) {
          userIds.add(String(link.addedBy));
        }
      });
    }

        // Add assigned users
        if (task.assignedTo) {
          task.assignedTo.forEach(userId => {
            userIds.add(String(userId));
          });
        }

        console.log('Loading user data for IDs:', Array.from(userIds));

        // Load user data for all collected IDs
        const userDataPromises = Array.from(userIds).map(async userId => {
          const user = await Meteor.users.findOneAsync(userId);
          if (!user) return [userId, 'Unknown User'];
          
          const firstName = user.profile?.firstName || '';
          const lastName = user.profile?.lastName || '';
          const fullName = `${firstName} ${lastName}`.trim();
          
          return [userId, fullName || 'Unknown User'];
        });
        
        const userEntries = await Promise.all(userDataPromises);
        const names = Object.fromEntries(userEntries);
        
        console.log('Loaded user names:', names);
        setUserNames(names);
      } catch (error) {
        console.error('Error loading user data:', error);
      } finally {
        setIsLoadingUsers(false);
      }
    };
    
    loadUserData();
  }, [task]);

  // Handles updating the task's overall progress based on checklist completion
  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError(''); // Clear previous errors
    setSuccessMessage(''); // Clear previous success messages
    try {
      // Calculate progress based on completed checklist items
      const completedItems = task.checklist?.filter(item => item.completed).length || 0;
      const totalItems = task.checklist?.length || 0;
      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

      // Call Meteor method to update task progress
      await Meteor.call('tasks.updateProgress', task._id, progress);
      setIsSubmitting(false);
      setSuccessMessage('Task progress updated successfully!');
      setTimeout(() => navigate('/team-dashboard/tasks'), 1500); // Navigate after a brief success message
    } catch (error) {
      console.error('Error submitting task:', error);
      setError(error.reason || 'Failed to update task progress.');
      setIsSubmitting(false);
    }
  };

  // Handles file selection for attachment upload
  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      // Check file size (limit to 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        setError('File size exceeds 5MB limit.');
        setFile(null); // Clear selected file
        return;
      }
      setFile(selectedFile);
      setError(''); // Clear any previous errors
      setSuccessMessage(''); // Clear any previous success messages
    }
  };

  // Handles uploading a selected file as an attachment
  const handleFileUpload = () => {
    if (!file) {
      setError('Please select a file to upload.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError(''); // Clear previous errors
    setSuccessMessage(''); // Clear previous success messages

    const reader = new FileReader();

    // Update upload progress
    reader.onprogress = (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded / event.total) * 100);
        setUploadProgress(progress);
      }
    };

    // When file reading is complete, call Meteor method to add attachment
    reader.onload = (event) => {
      const fileData = {
        name: file.name,
        type: file.type,
        data: event.target.result // Base64 encoded file data
      };

      Meteor.call('tasks.addAttachment', task._id, fileData, (error, result) => {
        setIsUploading(false);
        if (error) {
          console.error('Error uploading file:', error);
          setError(error.reason || 'Failed to upload file.');
        } else {
          setFile(null); // Clear selected file
          setSuccessMessage('File uploaded successfully!');
          setTimeout(() => setSuccessMessage(''), 3000); // Clear message after 3 seconds
          // Reset the file input visually
          const fileInput = document.getElementById('file-upload');
          if (fileInput) fileInput.value = '';
        }
      });
    };

    // Handle file reading errors
    reader.onerror = () => {
      setIsUploading(false);
      setError('Error reading file.');
    };

    reader.readAsDataURL(file); // Read file as Data URL (base64)
  };

  // Handles adding a new link to the task
  const handleAddLink = () => {
    if (!newLink) {
      setError('Please enter a link.');
      return;
    }

    // Basic URL validation
    try {
      new URL(newLink); // Throws an error for invalid URLs
    } catch (e) {
      setError('Please enter a valid URL (e.g., include http:// or https://).');
      return;
    }

    Meteor.call('tasks.addLink', task._id, newLink, (error, result) => {
      if (error) {
        console.error('Error adding link:', error);
        setError(error.reason || 'Failed to add link.');
      } else {
        setNewLink(''); // Clear input
        setSuccessMessage('Link added successfully!');
        setTimeout(() => setSuccessMessage(''), 3000); // Clear message after 3 seconds
      }
    });
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'row',
      gap: '32px',
      padding: '24px',
      background: '#f8fafc', // Light grey background
      minHeight: 'calc(100vh - 64px)', // Adjust for header if present
      fontFamily: 'Inter, sans-serif' // Modern sans-serif font
    }}>
      {/* Main Task Details Section */}
      <div style={{ flex: 2 }}>
        <div style={{
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
          border: '1px solid #e2e8f0' // Light border
        }}>
          {/* Header with Back Button and Title */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '24px',
            flexWrap: 'wrap',
            gap: '16px'
          }}>
            {/* Back Arrow Button */}
              <button
                onClick={onCancel}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                fontSize: '1.8rem',
                  color: '#64748b',
                  transition: 'color 0.2s ease',
                marginRight: '12px',
                padding: 0,
                display: 'flex',
                alignItems: 'center'
                }}
              onMouseOver={e => e.currentTarget.style.color = '#334155'}
              onMouseOut={e => e.currentTarget.style.color = '#64748b'}
              aria-label="Back"
              >
                ←
              </button>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: '#0f172a',
              margin: '0',
              wordBreak: 'break-word',
              display: 'inline-block'
            }}>
              {task.title}
            </h2>
          </div>

          {/* Task Description */}
          <div style={{ marginBottom: '24px' }}>
            <h3 style={{ color: '#334155', marginBottom: '8px', fontSize: '1.2rem', fontWeight: '600' }}>Description</h3>
            <p style={{ color: '#475569', whiteSpace: 'pre-line', lineHeight: '1.6' }}>{task.description || 'No description provided.'}</p>
          </div>

          {/* Task Status and Priority */}
          <div style={{ display: 'flex', gap: '32px', marginTop: '32px', flexWrap: 'wrap' }}>
            <div>
              <h3 style={{ fontSize: '1.1rem', color: '#0f172a', marginBottom: '8px' }}>Status</h3>
              <span style={{
                padding: '8px 20px',
                borderRadius: '20px',
                fontSize: '1rem',
                fontWeight: '500',
                backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in_progress' ? '#fef9c3' : '#f1f5f9',
                color: task.status === 'completed' ? '#166534' : task.status === 'in_progress' ? '#92400e' : '#475569'
              }}>
                {task.status === 'not_started' ? 'Not Started' :
                 task.status === 'in_progress' ? 'In Progress' :
                 task.status === 'blocked' ? 'Blocked' :
                 task.status === 'completed' ? 'Completed' :
                 task.status}
              </span>
            </div>
            <div>
              <h3 style={{ fontSize: '1.1rem', color: '#0f172a', marginBottom: '8px' }}>Priority</h3>
              <span style={{
                padding: '8px 20px',
                borderRadius: '20px',
                fontSize: '1rem',
                fontWeight: '500',
                backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef9c3' : '#dcfce7',
                color: task.priority === 'high' ? '#991b1b' : task.priority === 'medium' ? '#92400e' : '#166534'
              }}>
                {task.priority === 'high' ? 'High' : task.priority === 'medium' ? 'Medium' : 'Low'}
              </span>
            </div>
            {/* Add Due Date */}
            {task.dueDate && (
              <div>
                <h3 style={{ fontSize: '1.1rem', color: '#0f172a', marginBottom: '8px' }}>Due Date</h3>
                <span style={{
                  padding: '8px 20px',
                  borderRadius: '20px',
                  fontSize: '1rem',
                  fontWeight: '500',
                  backgroundColor: '#e0e7ef',
                  color: '#2563eb'
                }}>
                  {new Date(task.dueDate).toLocaleDateString()}
                </span>
              </div>
            )}
            {/* Add Progress */}
            <div>
              <h3 style={{ fontSize: '1.1rem', color: '#0f172a', marginBottom: '8px' }}>Progress</h3>
              <span style={{
                padding: '8px 20px',
                borderRadius: '20px',
                fontSize: '1rem',
                fontWeight: '500',
                backgroundColor: '#e0e7ef',
                color: '#2563eb'
              }}>
                {task.progress || 0}%
              </span>
            </div>
            {/* Add Team Members */}
            {task.assignedTo && task.assignedTo.length > 0 && (
              <div>
                <h3 style={{ fontSize: '1.1rem', color: '#0f172a', marginBottom: '8px' }}>Team Members</h3>
                <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                  {task.assignedTo.map(userId => (
                    <span
                      key={userId}
                      style={{
                        padding: '8px 20px',
                        borderRadius: '20px',
                        fontSize: '1rem',
                        backgroundColor: '#f1f5f9',
                        color: '#475569',
                        fontWeight: '500'
                      }}
                    >
                      {userNames[userId] || 'Unknown User'}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Checklist Section */}
          {task.checklist && task.checklist.length > 0 && (
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ color: '#334155', marginBottom: '16px', fontSize: '1.2rem', fontWeight: '600' }}>Checklist</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {task.checklist.map((item, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    backgroundColor: '#f8fafc',
                    padding: '12px',
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0'
                  }}>
                    <input
                      type="checkbox"
                      checked={item.completed}
                      onChange={() => {
                        Meteor.call('tasks.toggleChecklistItem', task._id, index, (error) => {
                          if (error) {
                            console.error('Error toggling checklist item:', error);
                            setError(error.reason || 'Failed to update checklist item.');
                          } else {
                            setSuccessMessage('Checklist item updated!');
                            setTimeout(() => setSuccessMessage(''), 3000);
                          }
                        });
                      }}
                      style={{ width: '20px', height: '20px', accentColor: '#3b82f6', cursor: 'pointer' }}
                    />
                    <span style={{
                      color: item.completed ? '#64748b' : '#0f172a',
                      textDecoration: item.completed ? 'line-through' : 'none',
                      fontSize: '1rem',
                      flex: 1
                    }}>
                      {item.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Attachments Section */}
          <div style={{ marginTop: '24px' }}>
            <h3 style={{
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#0f172a',
              marginBottom: '16px'
            }}>Attachments</h3>

            {/* File upload form */}
            <div style={{
              marginBottom: '16px',
              padding: '16px',
              backgroundColor: '#f8fafc',
              borderRadius: '8px',
              border: '1px solid #e2e8f0',
              display: 'flex',
              flexDirection: 'column',
              gap: '12px'
            }}>
              <input
                id="file-upload"
                type="file"
                onChange={handleFileChange}
                style={{ fontSize: '0.9rem', color: '#475569' }}
              />
              <button
                onClick={handleFileUpload}
                disabled={!file || isUploading}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: file && !isUploading ? 'pointer' : 'not-allowed',
                  opacity: file && !isUploading ? 1 : 0.7,
                  fontSize: '1rem',
                  fontWeight: 600,
                  transition: 'background-color 0.2s ease',
                }}
                onMouseOver={(e) => { if (file && !isUploading) e.currentTarget.style.backgroundColor = '#2563eb'; }}
                onMouseOut={(e) => { if (file && !isUploading) e.currentTarget.style.backgroundColor = '#3b82f6'; }}
              >
                {isUploading ? `Uploading... ${uploadProgress}%` : 'Upload File'}
              </button>
              {isUploading && (
                <div style={{
                  height: '8px',
                  backgroundColor: '#e2e8f0',
                  borderRadius: '4px',
                  overflow: 'hidden',
                  marginTop: '8px'
                }}>
                  <div style={{
                    width: `${uploadProgress}%`,
                    height: '100%',
                    backgroundColor: '#3b82f6',
                    transition: 'width 0.1s linear'
                  }}></div>
                </div>
              )}
            </div>

            {/* Display existing attachments */}
            {task.attachments && task.attachments.length > 0 ? (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {task.attachments.map((attachment, index) => {
                  // Ensure we're working with string IDs
                  const currentUserId = String(Meteor.userId());
                  const uploadedById = String(attachment.uploadedBy);
                  const isOwner = currentUserId === uploadedById;

                  console.log('Rendering attachment:', {
                    attachment,
                    uploadedById,
                    userName: userNames[uploadedById],
                    allUserNames: userNames
                  });

                  return (
                    <div
                      key={index}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '12px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0',
                        wordBreak: 'break-word', // Ensure long names wrap
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1, minWidth: 0 }}>
                        <span style={{ fontSize: '1.5rem', color: '#64748b' }}>📎</span>
                        <div>
                          <div style={{ fontSize: '0.9rem', color: '#0f172a', fontWeight: '500' }}>
                            {attachment.name}
                          </div>
                          <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                            Uploaded by {userNames[uploadedById] || 'Unknown User'} on {new Date(attachment.uploadedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div style={{ display: 'flex', gap: '8px', flexShrink: 0 }}>
                        <button
                          onClick={() => {
                            try {
                              let base64Data = attachment.data;
                              if (base64Data.startsWith('data:')) {
                                base64Data = base64Data.split(',')[1];
                              }

                              const byteCharacters = atob(base64Data);
                              const byteNumbers = new Array(byteCharacters.length);
                              for (let i = 0; i < byteCharacters.length; i++) {
                                byteNumbers[i] = byteCharacters.charCodeAt(i);
                              }
                              const byteArray = new Uint8Array(byteNumbers);

                              const mimeType = attachment.type || 'application/octet-stream';
                              const blob = new Blob([byteArray], { type: mimeType });

                              const url = window.URL.createObjectURL(blob);
                              const link = document.createElement('a');
                              link.href = url;
                              link.download = attachment.name;
                              document.body.appendChild(link);
                              link.click();

                              setTimeout(() => {
                                document.body.removeChild(link);
                                window.URL.revokeObjectURL(url);
                              }, 100);
                            } catch (downloadError) {
                              console.error('Error downloading file:', downloadError);
                              setError('Failed to download file. Please try again.');
                            }
                          }}
                          style={{
                            padding: '8px 16px',
                            backgroundColor: '#e2e8f0',
                            border: 'none',
                            borderRadius: '6px',
                            color: '#0f172a',
                            fontSize: '0.875rem',
                            cursor: 'pointer',
                            transition: 'background-color 0.2s ease',
                          }}
                          onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#cbd5e1'}
                          onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#e2e8f0'}
                        >
                          Download
                        </button>
                        {isOwner && (
                          <button
                            onClick={() => {
                              if (window.confirm('Are you sure you want to remove this attachment?')) {
                                Meteor.call('tasks.removeAttachment', task._id, index, (error) => {
                                  if (error) {
                                    console.error('Error removing attachment:', error);
                                    setError(error.reason || 'Failed to remove attachment.');
                                  } else {
                                    setSuccessMessage('Attachment removed successfully!');
                                    setTimeout(() => setSuccessMessage(''), 3000);
                                  }
                                });
                              }
                            }}
                            style={{
                              padding: '8px 16px',
                              backgroundColor: '#ef4444',
                              border: 'none',
                              borderRadius: '6px',
                              color: 'white',
                              fontSize: '0.875rem',
                              cursor: 'pointer',
                              transition: 'background-color 0.2s ease',
                            }}
                            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#dc2626'}
                            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#ef4444'}
                          >
                            Remove
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p style={{ color: '#64748b', fontSize: '0.9rem' }}>No attachments yet. Upload files to share them!</p>
            )}
          </div>

          {/* Links Section */}
          <div style={{ marginTop: '32px' }}>
            <h3 style={{
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#0f172a',
              marginBottom: '16px'
            }}>Links</h3>

            {/* Link add form */}
            <div style={{
              marginBottom: '16px',
              padding: '16px',
              backgroundColor: '#f8fafc',
              borderRadius: '8px',
              border: '1px solid #e2e8f0',
              display: 'flex',
              gap: '12px',
              alignItems: 'center'
            }}>
              <input
                type="text"
                value={newLink}
                onChange={(e) => setNewLink(e.target.value)}
                placeholder="Enter URL (https://example.com)"
                style={{
                  flex: 1,
                  padding: '10px 12px',
                  border: '1px solid #cbd5e1',
                  borderRadius: '6px',
                  fontSize: '0.9rem',
                  color: '#334155',
                  boxSizing: 'border-box'
                }}
              />
              <button
                onClick={handleAddLink}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 600,
                  transition: 'background-color 0.2s ease',
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#3b82f6'}
              >
                Add Link
              </button>
            </div>

            {/* Display existing links */}
            {task.links && task.links.length > 0 ? (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {task.links.map((link, index) => {
                  // Ensure we're working with string IDs
                  const currentUserId = String(Meteor.userId());
                  const addedById = String(link.addedBy);
                  const isOwner = currentUserId === addedById;

                  return (
                    <div
                      key={index}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '12px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0',
                        wordBreak: 'break-word', // Ensure long URLs wrap
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1, minWidth: 0 }}>
                        <span style={{ fontSize: '1.5rem', color: '#64748b' }}>🔗</span>
                        <div>
                          <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ fontSize: '0.9rem', color: '#2563eb', textDecoration: 'none', fontWeight: '500' }}
                            onMouseOver={(e) => e.currentTarget.style.textDecoration = 'underline'}
                            onMouseOut={(e) => e.currentTarget.style.textDecoration = 'none'}
                          >
                            {link.url}
                          </a>
                          <div style={{ fontSize: '0.75rem', color: '#64748b' }}>
                            Added by {userNames[addedById] || 'Unknown User'} on {new Date(link.addedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div style={{ display: 'flex', gap: '8px', flexShrink: 0 }}>
                        {isOwner && (
                          <button
                            onClick={() => {
                              if (window.confirm('Are you sure you want to remove this link?')) {
                                Meteor.call('tasks.removeLink', task._id, index, (error) => {
                                  if (error) {
                                    console.error('Error removing link:', error);
                                    setError(error.reason || 'Failed to remove link.');
                                  } else {
                                    setSuccessMessage('Link removed successfully!');
                                    setTimeout(() => setSuccessMessage(''), 3000);
                                  }
                                });
                              }
                            }}
                            style={{
                              padding: '8px 16px',
                              backgroundColor: '#ef4444',
                              border: 'none',
                              borderRadius: '6px',
                              color: 'white',
                              fontSize: '0.875rem',
                              cursor: 'pointer',
                              transition: 'background-color 0.2s ease',
                            }}
                            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#dc2626'}
                            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#ef4444'}
                          >
                            Remove
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p style={{ color: '#64748b', fontSize: '0.9rem' }}>No links yet. Add relevant URLs to this task!</p>
            )}
          </div>

          {/* Error and Success Messages */}
          {error && (
            <div style={{
              backgroundColor: '#fee2e2',
              color: '#b91c1c',
              padding: '12px',
              borderRadius: '8px',
              marginTop: '24px',
              fontSize: '0.9rem',
              fontWeight: 500,
              border: '1px solid #fca5a5'
            }}>
              {error}
            </div>
          )}
          {successMessage && (
            <div style={{
              backgroundColor: '#dcfce7',
              color: '#166534',
              padding: '12px',
              borderRadius: '8px',
              marginTop: '24px',
              fontSize: '0.9rem',
              fontWeight: 500,
              border: '1px solid #86efac'
            }}>
              {successMessage}
            </div>
          )}

          {/* Submit Button */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '32px', paddingTop: '24px', borderTop: '1px solid #e2e8f0' }}>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '12px 24px',
                fontWeight: 600,
                fontSize: '1rem',
                cursor: 'pointer',
                opacity: isSubmitting ? 0.7 : 1,
                transition: 'background-color 0.2s ease',
              }}
              onMouseOver={(e) => { if (!isSubmitting) e.currentTarget.style.backgroundColor = '#2563eb'; }}
              onMouseOut={(e) => { if (!isSubmitting) e.currentTarget.style.backgroundColor = '#3b82f6'; }}
            >
              {isSubmitting ? 'Updating Progress...' : 'Update Task Progress'}
            </button>
          </div>
        </div>
      </div>

      {/* AI Insights Panel Section */}
      <div style={{ flex: 1, minWidth: 350, maxWidth: 500 }}>
        <TeamAIInsightsPanel taskId={task._id} task={task} />
      </div>
    </div>
  );
};