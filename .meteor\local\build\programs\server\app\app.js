Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/links.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/tasks.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// server/main.js                                                                                                  //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      'users.forgotPassword'(data) {
        try {
          console.log('[forgotPassword] Method called with data:', JSON.stringify(data));
          check(data, {
            email: String,
            newPassword: String
          });
          const {
            email,
            newPassword
          } = data;
          console.log('[forgotPassword] Processing request for email:', email);

          // Find user by email using Accounts method (most reliable)
          let user;
          try {
            user = Accounts.findUserByEmail(email);
            console.log('[forgotPassword] User lookup result:', user ? 'FOUND' : 'NOT FOUND');
          } catch (findError) {
            console.error('[forgotPassword] Error finding user:', findError);
            throw new Meteor.Error('user-lookup-failed', 'Error looking up user');
          }
          if (!user) {
            throw new Meteor.Error('user-not-found', 'No user found with this email address');
          }
          console.log('[forgotPassword] User found:', user._id);

          // Validate password requirements
          const passwordRegex = {
            length: /.{8,}/,
            uppercase: /[A-Z]/,
            number: /[0-9]/,
            special: /[!@#$%^&*]/
          };
          const passwordErrors = [];
          if (!passwordRegex.length.test(newPassword)) {
            passwordErrors.push('Password must be at least 8 characters long');
          }
          if (!passwordRegex.uppercase.test(newPassword)) {
            passwordErrors.push('Password must contain at least one uppercase letter');
          }
          if (!passwordRegex.number.test(newPassword)) {
            passwordErrors.push('Password must contain at least one number');
          }
          if (!passwordRegex.special.test(newPassword)) {
            passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
          }
          if (passwordErrors.length > 0) {
            throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
          }

          // Update password using the most reliable method
          console.log('[forgotPassword] Starting password update process...');

          // Create a temporary user to get the correct password hash structure
          const tempEmail = "temp_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9), "@temp.local");
          console.log('[forgotPassword] Creating temporary user with email:', tempEmail);
          let tempUserId;
          try {
            tempUserId = Accounts.createUser({
              email: tempEmail,
              password: newPassword
            });
            console.log('[forgotPassword] Temporary user created with ID:', tempUserId);
          } catch (createError) {
            console.error('[forgotPassword] Error creating temporary user:', createError);
            throw new Meteor.Error('temp-user-creation-failed', 'Failed to create temporary user');
          }

          // Get the password hash from the temporary user
          let tempUser, passwordHash;
          try {
            tempUser = Meteor.users.findOne(tempUserId);
            console.log('[forgotPassword] Temporary user structure:', JSON.stringify(tempUser, null, 2));
            if (!tempUser) {
              throw new Error('Temporary user not found after creation');
            }
            if (!tempUser.services) {
              throw new Error('Temporary user has no services object');
            }
            if (!tempUser.services.password) {
              throw new Error('Temporary user has no password service');
            }
            passwordHash = tempUser.services.password;
            console.log('[forgotPassword] Password hash structure:', JSON.stringify(passwordHash, null, 2));
            console.log('[forgotPassword] Password hash extracted from temporary user');
          } catch (hashError) {
            console.error('[forgotPassword] Error extracting password hash:', hashError);
            console.log('[forgotPassword] Trying fallback approach with bcrypt...');

            // Clean up temp user if it exists
            if (tempUserId) {
              try {
                Meteor.users.remove(tempUserId);
              } catch (e) {}
            }

            // Fallback: Use bcrypt directly
            try {
              const saltRounds = 10;
              const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
              passwordHash = {
                bcrypt: hashedPassword
              };
              console.log('[forgotPassword] Fallback bcrypt hash created');
            } catch (bcryptError) {
              console.error('[forgotPassword] Bcrypt fallback also failed:', bcryptError);
              throw new Meteor.Error('password-hash-creation-failed', 'Failed to create password hash');
            }
          }

          // Update the target user's password
          let updateResult;
          try {
            updateResult = Meteor.users.update(user._id, {
              $set: {
                'services.password': passwordHash
              }
            });
            console.log('[forgotPassword] Update result:', updateResult);
          } catch (updateError) {
            console.error('[forgotPassword] Error updating user password:', updateError);
            // Clean up temp user
            try {
              Meteor.users.remove(tempUserId);
            } catch (e) {}
            throw new Meteor.Error('password-update-failed', 'Failed to update user password');
          }

          // Clean up temporary user
          try {
            Meteor.users.remove(tempUserId);
            console.log('[forgotPassword] Temporary user cleaned up');
          } catch (cleanupError) {
            console.warn('[forgotPassword] Warning: Failed to clean up temporary user:', cleanupError);
          }
          if (updateResult === 1) {
            console.log("[forgotPassword] Password reset successful for user: ".concat(email));
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            throw new Meteor.Error('password-update-failed', "Password update failed. Update result: ".concat(updateResult));
          }
        } catch (outerError) {
          console.error('[forgotPassword] Outer catch - Error details:', outerError);
          console.error('[forgotPassword] Outer catch - Error stack:', outerError.stack);

          // If it's already a Meteor.Error, re-throw it
          if (outerError.error) {
            throw outerError;
          }

          // Otherwise, wrap it in a Meteor.Error
          throw new Meteor.Error('forgot-password-failed', "Forgot password failed: ".concat(outerError.message));
        }
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails3, _user$emails3$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails4, _user$emails4$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails5, _user$emails5$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));
            }
            if (issues.length > 0) {
              var _user$emails6, _user$emails6$;
              usersWithIssues.push({
                email: (_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
