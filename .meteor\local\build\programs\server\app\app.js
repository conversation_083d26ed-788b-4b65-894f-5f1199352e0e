Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                  //
// imports/api/links.js                                                                                             //
//                                                                                                                  //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                    //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                  //
// imports/api/tasks.js                                                                                             //
//                                                                                                                  //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                    //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                  //
// server/main.js                                                                                                   //
//                                                                                                                  //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                    //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation - allow login without email verification
      Accounts.config({
        sendVerificationEmail: false,
        // Disable email verification requirement
        forbidClientAccountCreation: false
      });

      // Add login validation hook to explicitly allow all login attempts
      Accounts.validateLoginAttempt(attempt => {
        var _attempt$error, _attempt$user;
        console.log('[validateLoginAttempt] Login attempt:', {
          type: attempt.type,
          allowed: attempt.allowed,
          error: (_attempt$error = attempt.error) === null || _attempt$error === void 0 ? void 0 : _attempt$error.message,
          userId: (_attempt$user = attempt.user) === null || _attempt$user === void 0 ? void 0 : _attempt$user._id,
          methodName: attempt.methodName
        });

        // Always allow login attempts - bypass any built-in restrictions
        if (attempt.user && attempt.type === 'password') {
          console.log('[validateLoginAttempt] Allowing password login for user:', attempt.user._id);
          return true;
        }

        // Allow other types of login attempts as well
        return true;
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email using async method
        console.log('[forgotPassword] Searching for user...');
        let targetUser = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!targetUser) {
          console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');
          targetUser = await Meteor.users.findOneAsync({
            'emails.address': {
              $regex: new RegExp("^".concat(email, "$"), 'i')
            }
          });
          if (!targetUser) {
            throw new Meteor.Error('user-not-found', 'No user found with this email address');
          }
          console.log('[forgotPassword] User found with case-insensitive search');
        } else {
          console.log('[forgotPassword] User found with direct search');
        }

        // Ensure we have a valid user with ID
        if (!targetUser || !targetUser._id) {
          throw new Meteor.Error('user-invalid', 'Found user but missing ID');
        }
        console.log('[forgotPassword] Final user ID:', targetUser._id);
        console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Comprehensive password update with debugging
        try {
          console.log('[forgotPassword] Starting password update...');

          // First, check current user document
          console.log('[forgotPassword] Checking current user document...');
          const currentUser = await Meteor.users.findOneAsync(targetUser._id);
          console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));
          if (!currentUser) {
            throw new Meteor.Error('user-not-found', 'User document not found during update');
          }

          // Create password hash using bcrypt directly
          const bcrypt = require('bcrypt');
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);
          console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');

          // Try multiple update approaches
          let updateResult = 0;
          let successMethod = null;

          // Method 1: Update services.password.bcrypt directly
          console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');
          try {
            updateResult = await Meteor.users.updateAsync(targetUser._id, {
              $set: {
                'services.password.bcrypt': hashedPassword
              }
            });
            console.log('[forgotPassword] Method 1 result:', updateResult);
            if (updateResult === 1) {
              successMethod = 'Method 1: services.password.bcrypt';
            }
          } catch (method1Error) {
            console.error('[forgotPassword] Method 1 error:', method1Error);
          }

          // Method 2: Update entire services.password object
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 2: Updating entire services.password object...');
            try {
              updateResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  'services.password': {
                    bcrypt: hashedPassword
                  }
                }
              });
              console.log('[forgotPassword] Method 2 result:', updateResult);
              if (updateResult === 1) {
                successMethod = 'Method 2: entire services.password object';
              }
            } catch (method2Error) {
              console.error('[forgotPassword] Method 2 error:', method2Error);
            }
          }

          // Method 3: Update entire services object
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 3: Updating entire services object...');
            try {
              var _currentUser$services, _currentUser$services2;
              updateResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  services: {
                    password: {
                      bcrypt: hashedPassword
                    },
                    resume: ((_currentUser$services = currentUser.services) === null || _currentUser$services === void 0 ? void 0 : _currentUser$services.resume) || {
                      loginTokens: []
                    },
                    email: ((_currentUser$services2 = currentUser.services) === null || _currentUser$services2 === void 0 ? void 0 : _currentUser$services2.email) || {}
                  }
                }
              });
              console.log('[forgotPassword] Method 3 result:', updateResult);
              if (updateResult === 1) {
                successMethod = 'Method 3: entire services object';
              }
            } catch (method3Error) {
              console.error('[forgotPassword] Method 3 error:', method3Error);
            }
          }

          // Method 4: Test basic update capability
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 4: Testing basic update capability...');
            try {
              const testResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  'profile.passwordResetTest': new Date()
                }
              });
              console.log('[forgotPassword] Basic update test result:', testResult);
              if (testResult === 1) {
                console.log('[forgotPassword] Basic updates work, trying password with $unset first...');
                // Try unsetting and then setting
                await Meteor.users.updateAsync(targetUser._id, {
                  $unset: {
                    'services.password': ''
                  }
                });
                updateResult = await Meteor.users.updateAsync(targetUser._id, {
                  $set: {
                    'services.password': {
                      bcrypt: hashedPassword
                    }
                  }
                });
                console.log('[forgotPassword] Unset/Set method result:', updateResult);
                if (updateResult === 1) {
                  successMethod = 'Method 4: unset then set';
                }
              }
            } catch (method4Error) {
              console.error('[forgotPassword] Method 4 error:', method4Error);
            }
          }
          if (updateResult === 1) {
            var _updatedUser$services, _updatedUser$services2;
            console.log("[forgotPassword] Password update successful using: ".concat(successMethod));

            // Verify the update worked
            const updatedUser = await Meteor.users.findOneAsync(targetUser._id);
            console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));

            // Test password verification
            if ((_updatedUser$services = updatedUser.services) !== null && _updatedUser$services !== void 0 && (_updatedUser$services2 = _updatedUser$services.password) !== null && _updatedUser$services2 !== void 0 && _updatedUser$services2.bcrypt) {
              const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);
              console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');
            }
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);

            // Log user permissions and collection info
            console.log('[forgotPassword] User ID:', targetUser._id);
            console.log('[forgotPassword] User ID type:', typeof targetUser._id);
            console.log('[forgotPassword] Current user exists:', !!currentUser);
            console.log('[forgotPassword] User roles:', currentUser.roles);
            throw new Meteor.Error('password-update-failed', 'Failed to update password in database');
          }
        } catch (error) {
          console.error('[forgotPassword] Error during password update:', error);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      async 'users.debugUser'(_ref3) {
        let {
          email
        } = _ref3;
        try {
          var _fullUser$services, _fullUser$services2, _fullUser$services2$p;
          check(email, String);
          console.log('[debugUser] Debugging user:', email);

          // Find user using async method
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            console.log('[debugUser] User not found');
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[debugUser] User found:', user._id);

          // Get full user document using async method
          const fullUser = await Meteor.users.findOneAsync(user._id);
          console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));
          if (!fullUser) {
            console.log('[debugUser] Full user document not found');
            return {
              success: false,
              error: 'Full user document not found'
            };
          }

          // Test basic update using async method
          let testUpdateResult = null;
          try {
            testUpdateResult = await Meteor.users.updateAsync(user._id, {
              $set: {
                'profile.debugTest': new Date()
              }
            });
            console.log('[debugUser] Test update result:', testUpdateResult);
          } catch (updateError) {
            console.error('[debugUser] Test update error:', updateError);
          }

          // Try using Accounts.setPassword if available
          let hasSetPassword = false;
          try {
            hasSetPassword = typeof Accounts.setPassword === 'function';
            console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);
          } catch (setPasswordError) {
            console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);
          }
          const result = {
            success: true,
            userId: user._id,
            userIdType: typeof user._id,
            hasServices: !!fullUser.services,
            hasPassword: !!((_fullUser$services = fullUser.services) !== null && _fullUser$services !== void 0 && _fullUser$services.password),
            hasBcrypt: !!((_fullUser$services2 = fullUser.services) !== null && _fullUser$services2 !== void 0 && (_fullUser$services2$p = _fullUser$services2.password) !== null && _fullUser$services2$p !== void 0 && _fullUser$services2$p.bcrypt),
            roles: fullUser.roles || [],
            profile: fullUser.profile || {},
            testUpdateResult: testUpdateResult,
            hasSetPassword: hasSetPassword,
            servicesStructure: fullUser.services || {}
          };
          console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));
          return result;
        } catch (error) {
          console.error('[debugUser] Error in debug method:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.testLogin'(_ref4) {
        let {
          email,
          password
        } = _ref4;
        check(email, String);
        check(password, String);
        console.log('[testLogin] Testing login for email:', email);

        // Find user using async method
        const user = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!user) {
          console.log('[testLogin] User not found');
          return {
            success: false,
            error: 'User not found'
          };
        }
        console.log('[testLogin] User found:', user._id);
        console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));

        // Test password verification
        try {
          // Check if password service exists
          if (!user.services || !user.services.password || !user.services.password.bcrypt) {
            console.log('[testLogin] No password hash found in user services');
            return {
              success: false,
              error: 'No password hash found',
              userId: user._id,
              services: user.services
            };
          }
          const bcrypt = require('bcrypt');
          const storedHash = user.services.password.bcrypt;
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
          console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');
          console.log('[testLogin] Password length:', password.length);
          return {
            success: passwordMatch,
            userId: user._id,
            hashPreview: storedHash.substring(0, 20) + '...',
            passwordLength: password.length
          };
        } catch (error) {
          console.error('[testLogin] Error during password test:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.comparePasswordFormats'(_ref5) {
        var _user$services, _user$services$passwo;
        let {
          email
        } = _ref5;
        check(email, String);
        console.log('[comparePasswordFormats] Checking password format for:', email);

        // Find user
        const user = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!user) {
          return {
            success: false,
            error: 'User not found'
          };
        }
        console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));

        // Check if user has password
        if (!((_user$services = user.services) !== null && _user$services !== void 0 && (_user$services$passwo = _user$services.password) !== null && _user$services$passwo !== void 0 && _user$services$passwo.bcrypt)) {
          return {
            success: false,
            error: 'No password found'
          };
        }
        const storedHash = user.services.password.bcrypt;
        console.log('[comparePasswordFormats] Stored hash:', storedHash);
        console.log('[comparePasswordFormats] Hash length:', storedHash.length);
        console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));

        // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)
        const isBcrypt = /^\$2[aby]\$/.test(storedHash);
        console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);
        return {
          success: true,
          userId: user._id,
          hashLength: storedHash.length,
          hashPreview: storedHash.substring(0, 20) + '...',
          isBcryptFormat: isBcrypt,
          fullServices: user.services
        };
      },
      async 'users.testActualLogin'(_ref6) {
        let {
          email,
          password
        } = _ref6;
        check(email, String);
        check(password, String);
        console.log('[testActualLogin] Testing actual login for:', email);
        try {
          var _user$services2, _user$services2$passw, _user$emails3, _user$emails3$;
          // Try to simulate what Meteor.loginWithPassword does
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            console.log('[testActualLogin] User not found');
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[testActualLogin] User found:', user._id);
          console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));

          // Check if password service exists
          if (!((_user$services2 = user.services) !== null && _user$services2 !== void 0 && (_user$services2$passw = _user$services2.password) !== null && _user$services2$passw !== void 0 && _user$services2$passw.bcrypt)) {
            console.log('[testActualLogin] No password hash found');
            return {
              success: false,
              error: 'No password hash found'
            };
          }

          // Test bcrypt verification
          const bcrypt = require('bcrypt');
          const storedHash = user.services.password.bcrypt;
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
          console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');

          // Check if user has any login restrictions
          const isVerified = ((_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.verified) || false;
          console.log('[testActualLogin] Email verified:', isVerified);

          // Check user roles
          console.log('[testActualLogin] User roles:', user.roles);

          // Try to create a login token manually to test if that works
          let loginTokenTest = null;
          try {
            // This is what Meteor does internally for login
            const stampedToken = Accounts._generateStampedLoginToken();
            console.log('[testActualLogin] Generated login token:', !!stampedToken);
            loginTokenTest = 'Token generation successful';
          } catch (tokenError) {
            console.error('[testActualLogin] Token generation error:', tokenError);
            loginTokenTest = tokenError.message;
          }
          return {
            success: passwordMatch,
            userId: user._id,
            passwordVerification: passwordMatch,
            emailVerified: isVerified,
            userRoles: user.roles,
            hashPreview: storedHash.substring(0, 20) + '...',
            loginTokenTest: loginTokenTest,
            fullUserStructure: {
              _id: user._id,
              emails: user.emails,
              services: user.services,
              roles: user.roles,
              profile: user.profile
            }
          };
        } catch (error) {
          console.error('[testActualLogin] Error:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.simulateLogin'(_ref7) {
        let {
          email,
          password
        } = _ref7;
        check(email, String);
        check(password, String);
        console.log('[simulateLogin] Simulating login for:', email);
        try {
          var _user$services3, _user$services3$passw, _user$emails4, _user$emails4$;
          // Find user
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[simulateLogin] User found:', user._id);

          // Check password
          const bcrypt = require('bcrypt');
          const storedHash = (_user$services3 = user.services) === null || _user$services3 === void 0 ? void 0 : (_user$services3$passw = _user$services3.password) === null || _user$services3$passw === void 0 ? void 0 : _user$services3$passw.bcrypt;
          if (!storedHash) {
            return {
              success: false,
              error: 'No password hash found'
            };
          }
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[simulateLogin] Password match:', passwordMatch);
          if (!passwordMatch) {
            return {
              success: false,
              error: 'Invalid password'
            };
          }

          // Check if there are any login restrictions
          const restrictions = [];

          // Check email verification requirement
          const emailVerified = ((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.verified) || false;
          if (!emailVerified) {
            restrictions.push('Email not verified');
          }

          // Check if user is active (no disabled flag)
          if (user.disabled) {
            restrictions.push('User account disabled');
          }

          // Check roles
          if (!user.roles || user.roles.length === 0) {
            restrictions.push('No roles assigned');
          }
          console.log('[simulateLogin] Login restrictions:', restrictions);

          // Try to manually create what loginWithPassword would do
          let loginSimulation = 'Not attempted';
          try {
            // Check if we can generate a login token
            const stampedToken = Accounts._generateStampedLoginToken();
            if (stampedToken) {
              loginSimulation = 'Login token generation successful';
            }
          } catch (tokenError) {
            loginSimulation = "Token error: ".concat(tokenError.message);
          }
          return {
            success: passwordMatch && restrictions.length === 0,
            userId: user._id,
            passwordMatch: passwordMatch,
            emailVerified: emailVerified,
            restrictions: restrictions,
            loginSimulation: loginSimulation,
            userStructure: {
              _id: user._id,
              emails: user.emails,
              roles: user.roles,
              profile: user.profile,
              disabled: user.disabled || false
            }
          };
        } catch (error) {
          console.error('[simulateLogin] Error:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails5, _user$emails5$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails6, _user$emails6$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails7, _user$emails7$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address));
            }
            if (issues.length > 0) {
              var _user$emails8, _user$emails8$;
              usersWithIssues.push({
                email: (_user$emails8 = user.emails) === null || _user$emails8 === void 0 ? void 0 : (_user$emails8$ = _user$emails8[0]) === null || _user$emails8$ === void 0 ? void 0 : _user$emails8$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
