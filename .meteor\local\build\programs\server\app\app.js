Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                               //
// imports/api/links.js                                                                                          //
//                                                                                                               //
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                 //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                               //
// imports/api/tasks.js                                                                                          //
//                                                                                                               //
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                 //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                               //
// server/main.js                                                                                                //
//                                                                                                               //
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                 //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      'users.forgotPassword'(data) {
        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }
        console.log('[forgotPassword] User found with ID:', user._id);
        console.log('[forgotPassword] User ID type:', typeof user._id);

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Simple, direct password update approach
        try {
          console.log('[forgotPassword] Starting password update...');

          // Create password hash
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);

          // Try the most basic update first
          console.log('[forgotPassword] Attempting basic update...');
          let updateResult = Meteor.users.update(user._id, {
            $set: {
              'services.password.bcrypt': hashedPassword
            }
          });
          console.log('[forgotPassword] Basic update result:', updateResult);
          if (updateResult !== 1) {
            // Try with full password object
            console.log('[forgotPassword] Trying with full password object...');
            updateResult = Meteor.users.update(user._id, {
              $set: {
                'services.password': {
                  bcrypt: hashedPassword
                }
              }
            });
            console.log('[forgotPassword] Full object update result:', updateResult);
          }
          if (updateResult === 1) {
            console.log('[forgotPassword] Password update successful!');
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            console.error('[forgotPassword] All update attempts failed. Result:', updateResult);
            throw new Meteor.Error('password-update-failed', 'Failed to update password in database');
          }
        } catch (error) {
          console.error('[forgotPassword] Error during password update:', error);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails3, _user$emails3$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails4, _user$emails4$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails5, _user$emails5$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));
            }
            if (issues.length > 0) {
              var _user$emails6, _user$emails6$;
              usersWithIssues.push({
                email: (_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
