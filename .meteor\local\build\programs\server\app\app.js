Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                  //
// imports/api/links.js                                                                                             //
//                                                                                                                  //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                    //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                  //
// imports/api/tasks.js                                                                                             //
//                                                                                                                  //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                    //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                  //
// server/main.js                                                                                                   //
//                                                                                                                  //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                    //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        try {
          console.log('[forgotPassword] Method called with data:', JSON.stringify(data));
          check(data, {
            email: String,
            newPassword: String
          });
          const {
            email,
            newPassword
          } = data;
          console.log('[forgotPassword] Processing request for email:', email);

          // Find user by email using Accounts method (most reliable)
          let user;
          try {
            user = Accounts.findUserByEmail(email);
            console.log('[forgotPassword] User lookup result:', user ? 'FOUND' : 'NOT FOUND');
          } catch (findError) {
            console.error('[forgotPassword] Error finding user:', findError);
            throw new Meteor.Error('user-lookup-failed', 'Error looking up user');
          }
          if (!user) {
            throw new Meteor.Error('user-not-found', 'No user found with this email address');
          }
          console.log('[forgotPassword] User found:', user._id);

          // Validate password requirements
          const passwordRegex = {
            length: /.{8,}/,
            uppercase: /[A-Z]/,
            number: /[0-9]/,
            special: /[!@#$%^&*]/
          };
          const passwordErrors = [];
          if (!passwordRegex.length.test(newPassword)) {
            passwordErrors.push('Password must be at least 8 characters long');
          }
          if (!passwordRegex.uppercase.test(newPassword)) {
            passwordErrors.push('Password must contain at least one uppercase letter');
          }
          if (!passwordRegex.number.test(newPassword)) {
            passwordErrors.push('Password must contain at least one number');
          }
          if (!passwordRegex.special.test(newPassword)) {
            passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
          }
          if (passwordErrors.length > 0) {
            throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
          }

          // Update password using the most reliable method
          console.log('[forgotPassword] Starting password update process...');

          // Create a temporary user to get the correct password hash structure
          const tempEmail = "temp_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9), "@temp.local");
          console.log('[forgotPassword] Creating temporary user with email:', tempEmail);
          let tempUserId;
          try {
            tempUserId = Accounts.createUser({
              email: tempEmail,
              password: newPassword
            });
            console.log('[forgotPassword] Temporary user created with ID:', tempUserId);
          } catch (createError) {
            console.error('[forgotPassword] Error creating temporary user:', createError);
            throw new Meteor.Error('temp-user-creation-failed', 'Failed to create temporary user');
          }

          // Get the password hash from the temporary user
          let tempUser, passwordHash;
          try {
            tempUser = await Meteor.users.findOneAsync(tempUserId);
            console.log('[forgotPassword] Temporary user structure:', JSON.stringify(tempUser, null, 2));
            if (!tempUser) {
              throw new Error('Temporary user not found after creation');
            }
            if (!tempUser.services) {
              throw new Error('Temporary user has no services object');
            }
            if (!tempUser.services.password) {
              throw new Error('Temporary user has no password service');
            }
            passwordHash = tempUser.services.password;
            console.log('[forgotPassword] Password hash structure:', JSON.stringify(passwordHash, null, 2));
            console.log('[forgotPassword] Password hash extracted from temporary user');
          } catch (hashError) {
            console.error('[forgotPassword] Error extracting password hash:', hashError);
            console.log('[forgotPassword] Trying fallback approach with bcrypt...');

            // Clean up temp user if it exists
            if (tempUserId) {
              try {
                await Meteor.users.removeAsync(tempUserId);
              } catch (e) {}
            }

            // Fallback: Use bcrypt directly
            try {
              const saltRounds = 10;
              const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
              passwordHash = {
                bcrypt: hashedPassword
              };
              console.log('[forgotPassword] Fallback bcrypt hash created');
            } catch (bcryptError) {
              console.error('[forgotPassword] Bcrypt fallback also failed:', bcryptError);
              throw new Meteor.Error('password-hash-creation-failed', 'Failed to create password hash');
            }
          }

          // Update the target user's password
          console.log('[forgotPassword] Attempting to update user password...');
          console.log('[forgotPassword] Target user ID:', user._id);
          console.log('[forgotPassword] Target user ID type:', typeof user._id);
          console.log('[forgotPassword] Password hash to set:', JSON.stringify(passwordHash, null, 2));
          let updateResult = 0;
          let updateError = null;

          // Try multiple update approaches
          const updateMethods = [
          // Method 1: Standard update
          () => {
            console.log('[forgotPassword] Trying Method 1: Standard update');
            return Meteor.users.update(user._id, {
              $set: {
                'services.password': passwordHash
              }
            });
          },
          // Method 2: Update with query object
          () => {
            console.log('[forgotPassword] Trying Method 2: Update with query object');
            return Meteor.users.update({
              _id: user._id
            }, {
              $set: {
                'services.password': passwordHash
              }
            });
          },
          // Method 3: Update with string ID
          () => {
            console.log('[forgotPassword] Trying Method 3: Update with string ID');
            return Meteor.users.update(user._id.toString(), {
              $set: {
                'services.password': passwordHash
              }
            });
          },
          // Method 4: Upsert approach
          () => {
            console.log('[forgotPassword] Trying Method 4: Upsert approach');
            return Meteor.users.update({
              _id: user._id
            }, {
              $set: {
                'services.password': passwordHash
              }
            }, {
              upsert: false
            });
          }];
          for (let i = 0; i < updateMethods.length; i++) {
            try {
              updateResult = updateMethods[i]();
              console.log("[forgotPassword] Method ".concat(i + 1, " result:"), updateResult);
              if (updateResult === 1) {
                console.log("[forgotPassword] Update successful with method ".concat(i + 1));
                break;
              }
            } catch (methodError) {
              console.error("[forgotPassword] Method ".concat(i + 1, " error:"), methodError);
              updateError = methodError;
            }
          }
          if (updateResult !== 1) {
            console.error('[forgotPassword] All update methods failed');
            console.error('[forgotPassword] Final update result:', updateResult);
            console.error('[forgotPassword] Last error:', updateError);

            // Let's verify the user still exists and check its current structure
            console.log('[forgotPassword] Verifying user exists...');
            const userCheck = await Meteor.users.findOneAsync(user._id);
            console.log('[forgotPassword] User verification result:', userCheck ? 'EXISTS' : 'NOT FOUND');
            if (userCheck) {
              console.log('[forgotPassword] Current user structure:', JSON.stringify(userCheck, null, 2));

              // Try a simple test update to see if updates work at all
              console.log('[forgotPassword] Trying simple test update...');
              try {
                const testResult = Meteor.users.update(user._id, {
                  $set: {
                    'profile.lastPasswordResetAttempt': new Date()
                  }
                });
                console.log('[forgotPassword] Test update result:', testResult);
                if (testResult === 1) {
                  console.log('[forgotPassword] Test update worked! Issue might be with password field structure');

                  // Try updating just the bcrypt field
                  console.log('[forgotPassword] Trying to update just bcrypt field...');
                  const bcryptResult = Meteor.users.update(user._id, {
                    $set: {
                      'services.password.bcrypt': passwordHash.bcrypt || passwordHash
                    }
                  });
                  console.log('[forgotPassword] Bcrypt field update result:', bcryptResult);
                  if (bcryptResult === 1) {
                    updateResult = bcryptResult;
                    console.log('[forgotPassword] Bcrypt field update successful!');
                  }
                }
              } catch (testError) {
                console.error('[forgotPassword] Test update failed:', testError);
              }
            }

            // Clean up temp user
            if (tempUserId) {
              try {
                await Meteor.users.removeAsync(tempUserId);
              } catch (e) {}
            }
            if (updateResult !== 1) {
              throw new Meteor.Error('password-update-failed', "Failed to update user password. Update result: ".concat(updateResult));
            }
          }

          // Clean up temporary user
          try {
            await Meteor.users.removeAsync(tempUserId);
            console.log('[forgotPassword] Temporary user cleaned up');
          } catch (cleanupError) {
            console.warn('[forgotPassword] Warning: Failed to clean up temporary user:', cleanupError);
          }
          if (updateResult === 1) {
            console.log("[forgotPassword] Password reset successful for user: ".concat(email));
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            throw new Meteor.Error('password-update-failed', "Password update failed. Update result: ".concat(updateResult));
          }
        } catch (outerError) {
          console.error('[forgotPassword] Outer catch - Error details:', outerError);
          console.error('[forgotPassword] Outer catch - Error stack:', outerError.stack);

          // If it's already a Meteor.Error, re-throw it
          if (outerError.error) {
            throw outerError;
          }

          // Otherwise, wrap it in a Meteor.Error
          throw new Meteor.Error('forgot-password-failed', "Forgot password failed: ".concat(outerError.message));
        }
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails3, _user$emails3$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails4, _user$emails4$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails5, _user$emails5$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));
            }
            if (issues.length > 0) {
              var _user$emails6, _user$emails6$;
              usersWithIssues.push({
                email: (_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1ldGVvcjovL/CfkrthcHAvaW1wb3J0cy9hcGkvbGlua3MuanMiLCJtZXRlb3I6Ly/wn5K7YXBwL2ltcG9ydHMvYXBpL3Rhc2tzLmpzIiwibWV0ZW9yOi8v8J+Su2FwcC9zZXJ2ZXIvbWFpbi5qcyJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnQiLCJMaW5rc0NvbGxlY3Rpb24iLCJNb25nbyIsImxpbmsiLCJ2IiwiX19yZWlmeVdhaXRGb3JEZXBzX18iLCJDb2xsZWN0aW9uIiwiX19yZWlmeV9hc3luY19yZXN1bHRfXyIsIl9yZWlmeUVycm9yIiwic2VsZiIsImFzeW5jIiwiX29iamVjdFNwcmVhZCIsImRlZmF1bHQiLCJUYXNrcyIsInRhc2tDYXRlZ29yaWVzIiwidGFza0xhYmVscyIsIk1ldGVvciIsImNoZWNrIiwiTWF0Y2giLCJuYW1lIiwiY29sb3IiLCJpc1NlcnZlciIsInB1Ymxpc2giLCJfdXNlciRyb2xlcyIsInVzZXJJZCIsInJlYWR5IiwidXNlciIsInVzZXJzIiwiZmluZE9uZUFzeW5jIiwiaXNBZG1pbiIsInJvbGVzIiwiaW5jbHVkZXMiLCJmaW5kIiwic29ydCIsImNyZWF0ZWRBdCIsImZpZWxkcyIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJzdGFydERhdGUiLCJkdWVEYXRlIiwicHJpb3JpdHkiLCJzdGF0dXMiLCJhc3NpZ25lZFRvIiwiY2hlY2tsaXN0IiwiY2F0ZWdvcnkiLCJsYWJlbHMiLCJwcm9ncmVzcyIsImF0dGFjaG1lbnRzIiwibGlua3MiLCJjcmVhdGVkQnkiLCJ1cGRhdGVkQXQiLCJ1cGRhdGVkQnkiLCIkb3IiLCJjb25zb2xlIiwibG9nIiwidGFza3MiLCJmZXRjaCIsImxlbmd0aCIsInVzZXJJZHMiLCJTZXQiLCJmb3JFYWNoIiwidGFzayIsImF0dGFjaG1lbnQiLCJ1cGxvYWRlZEJ5IiwiYWRkIiwiU3RyaW5nIiwiYWRkZWRCeSIsInVzZXJJZEFycmF5IiwiQXJyYXkiLCJmcm9tIiwiX2lkIiwiJGluIiwiZW1haWxzIiwibWFwIiwidSIsIl91JHByb2ZpbGUiLCJfdSRwcm9maWxlMiIsImNvbmNhdCIsInByb2ZpbGUiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsInRyaW0iLCJoYXNQcm9maWxlIiwidGFza0lkIiwiX3VzZXIkcm9sZXMyIiwiZmluZE9uZSIsImN1cnNvciIsIm9ic2VydmUiLCJhZGRlZCIsImRvYyIsImNoYW5nZWQiLCJyZW1vdmVkIiwibWV0aG9kcyIsInRhc2tzLmluc2VydCIsIkRhdGUiLCJOdW1iZXIiLCJFcnJvciIsInByb2Nlc3NlZENoZWNrbGlzdCIsIml0ZW0iLCJ0ZXh0IiwiY29tcGxldGVkIiwidGFza1RvSW5zZXJ0IiwicmVzdWx0IiwiaW5zZXJ0QXN5bmMiLCJlcnJvciIsIm1lc3NhZ2UiLCJ0YXNrcy51cGRhdGUiLCJfdXNlciRyb2xlczMiLCJPcHRpb25hbCIsImV4aXN0aW5nVGFzayIsImNvbXBsZXRlZEl0ZW1zIiwiZmlsdGVyIiwidG90YWxJdGVtcyIsIk1hdGgiLCJyb3VuZCIsInRhc2tUb1VwZGF0ZSIsInVwZGF0ZUFzeW5jIiwiJHNldCIsInRhc2tzLmRlbGV0ZSIsInJlbW92ZUFzeW5jIiwidGFza3MudXBkYXRlUHJvZ3Jlc3MiLCJ1cGRhdGUiLCJ0YXNrcy50b2dnbGVDaGVja2xpc3RJdGVtIiwiaXRlbUluZGV4IiwiX3VzZXIkcm9sZXM0IiwiY2hlY2tsaXN0TGVuZ3RoIiwidXBkYXRlZENoZWNrbGlzdCIsInVwZGF0ZVJlc3VsdCIsInVwZGF0ZWRUYXNrIiwidGFza3MuYWRkQXR0YWNobWVudCIsImZpbGVEYXRhIiwiZGF0YSIsInVwbG9hZGVySWQiLCIkcHVzaCIsInR5cGUiLCJ1cGxvYWRlZEF0IiwidGFza3MuYWRkTGluayIsIlVSTCIsImUiLCJ1cmwiLCJhZGRlZEF0IiwidGFza3MucmVtb3ZlQXR0YWNobWVudCIsImF0dGFjaG1lbnRJbmRleCIsImN1cnJlbnRVc2VySWQiLCJ1cGxvYWRlZEJ5SWQiLCJ1cGRhdGVkQXR0YWNobWVudHMiLCJzcGxpY2UiLCJ0YXNrcy5yZW1vdmVMaW5rIiwibGlua0luZGV4IiwiYWRkZWRCeUlkIiwidXBkYXRlZExpbmtzIiwidGFza3MuZmluZE9uZSIsIkFjY291bnRzIiwiRW1haWwiLCJSb2xlcyIsImJjcnlwdCIsImluc2VydExpbmsiLCJfcmVmIiwiQURNSU5fVE9LRU4iLCJzdGFydHVwIiwiX01ldGVvciRzZXR0aW5ncyRwcml2IiwiY3JlYXRlSW5kZXgiLCJiYWNrZ3JvdW5kIiwid2FybiIsImFsbFVzZXJzIiwiZmV0Y2hBc3luYyIsIl91c2VyJGVtYWlscyIsIl91c2VyJGVtYWlscyQiLCJpZCIsImVtYWlsIiwiYWRkcmVzcyIsInVwZGF0ZXMiLCJpc0FycmF5IiwiT2JqZWN0Iiwia2V5cyIsIl91c2VyJGVtYWlsczIiLCJfdXNlciRlbWFpbHMyJCIsInRlYW1NZW1iZXJzQ291bnQiLCJjb3VudEFzeW5jIiwidGVzdE1lbWJlcnMiLCJwYXNzd29yZCIsIm1lbWJlciIsImNyZWF0ZVVzZXJBc3luYyIsInJvbGUiLCJmdWxsTmFtZSIsImVtYWlsU2V0dGluZ3MiLCJzZXR0aW5ncyIsInByaXZhdGUiLCJ1c2VybmFtZSIsInByb2Nlc3MiLCJlbnYiLCJNQUlMX1VSTCIsImVuY29kZVVSSUNvbXBvbmVudCIsInNlcnZlciIsInBvcnQiLCJzZW5kIiwidG8iLCJzdWJqZWN0IiwiY29uZmlnIiwic2VuZFZlcmlmaWNhdGlvbkVtYWlsIiwiZm9yYmlkQ2xpZW50QWNjb3VudENyZWF0aW9uIiwiZW1haWxUZW1wbGF0ZXMiLCJzaXRlTmFtZSIsInZlcmlmeUVtYWlsIiwiZW1haWxBZGRyZXNzIiwiaHRtbCIsIm9uQ3JlYXRlVXNlciIsIm9wdGlvbnMiLCJfY3VzdG9taXplZFVzZXIkZW1haWwiLCJfY3VzdG9taXplZFVzZXIkZW1haWwyIiwiY3VzdG9taXplZFVzZXIiLCJ0ZWFtTWVtYmVycyIsInVzZXJzLmNyZWF0ZSIsIl9yZWYyIiwiYWRtaW5Ub2tlbiIsInBhc3N3b3JkUmVnZXgiLCJ1cHBlcmNhc2UiLCJudW1iZXIiLCJzcGVjaWFsIiwicGFzc3dvcmRFcnJvcnMiLCJ0ZXN0IiwicHVzaCIsImpvaW4iLCJjcmVhdGVVc2VyIiwidXNlcnMuZ2V0Um9sZSIsIl91c2VyJHByb2ZpbGUiLCJ1c2Vycy5yZXNlbmRWZXJpZmljYXRpb25FbWFpbCIsImZpbmRVc2VyQnlFbWFpbCIsInVzZXJFbWFpbCIsInZlcmlmaWVkIiwidXNlcnMuZm9yZ290UGFzc3dvcmQiLCJKU09OIiwic3RyaW5naWZ5IiwibmV3UGFzc3dvcmQiLCJmaW5kRXJyb3IiLCJ0ZW1wRW1haWwiLCJub3ciLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInRlbXBVc2VySWQiLCJjcmVhdGVFcnJvciIsInRlbXBVc2VyIiwicGFzc3dvcmRIYXNoIiwic2VydmljZXMiLCJoYXNoRXJyb3IiLCJzYWx0Um91bmRzIiwiaGFzaGVkUGFzc3dvcmQiLCJoYXNoU3luYyIsImJjcnlwdEVycm9yIiwidXBkYXRlRXJyb3IiLCJ1cGRhdGVNZXRob2RzIiwidXBzZXJ0IiwiaSIsIm1ldGhvZEVycm9yIiwidXNlckNoZWNrIiwidGVzdFJlc3VsdCIsImJjcnlwdFJlc3VsdCIsInRlc3RFcnJvciIsImNsZWFudXBFcnJvciIsInN1Y2Nlc3MiLCJvdXRlckVycm9yIiwic3RhY2siLCJ1c2Vycy5jaGVja0FuZEZpeEFkbWluUm9sZSIsIl91c2VyJGVtYWlsczMiLCJfdXNlciRlbWFpbHMzJCIsInRvdGFsVXNlcnMiLCJ1c2Vycy5kaWFnbm9zZVJvbGVzIiwiX2N1cnJlbnRVc2VyJHJvbGVzIiwiY3VycmVudFVzZXIiLCJ1c2Vyc1dpdGhJc3N1ZXMiLCJmaXhlcyIsIl91c2VyJHByb2ZpbGUzIiwiaXNzdWVzIiwiX3VzZXIkcHJvZmlsZTIiLCJfdXNlciRlbWFpbHM0IiwiX3VzZXIkZW1haWxzNCQiLCJfdXNlciRlbWFpbHM1IiwiX3VzZXIkZW1haWxzNSQiLCJfdXNlciRlbWFpbHM2IiwiX3VzZXIkZW1haWxzNiQiLCJ1c2Vycy5jcmVhdGVUZXN0VGVhbU1lbWJlciIsIk5PREVfRU5WIiwidGVzdE1lbWJlciJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUFBQSxNQUFNLENBQUNDLE1BQU0sQ0FBQztNQUFDQyxlQUFlLEVBQUNBLENBQUEsS0FBSUE7SUFBZSxDQUFDLENBQUM7SUFBQyxJQUFJQyxLQUFLO0lBQUNILE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLGNBQWMsRUFBQztNQUFDRCxLQUFLQSxDQUFDRSxDQUFDLEVBQUM7UUFBQ0YsS0FBSyxHQUFDRSxDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSUMsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUMsTUFBTUEsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUM7SUFFdEssTUFBTUosZUFBZSxHQUFHLElBQUlDLEtBQUssQ0FBQ0ksVUFBVSxDQUFDLE9BQU8sQ0FBQztJQUFDQyxzQkFBQTtFQUFBLFNBQUFDLFdBQUE7SUFBQSxPQUFBRCxzQkFBQSxDQUFBQyxXQUFBO0VBQUE7RUFBQUQsc0JBQUE7QUFBQTtFQUFBRSxJQUFBO0VBQUFDLEtBQUE7QUFBQSxHOzs7Ozs7Ozs7Ozs7OztJQ0Y3RCxJQUFJQyxhQUFhO0lBQUNaLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLHNDQUFzQyxFQUFDO01BQUNTLE9BQU9BLENBQUNSLENBQUMsRUFBQztRQUFDTyxhQUFhLEdBQUNQLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBckdMLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDO01BQUNhLEtBQUssRUFBQ0EsQ0FBQSxLQUFJQSxLQUFLO01BQUNDLGNBQWMsRUFBQ0EsQ0FBQSxLQUFJQSxjQUFjO01BQUNDLFVBQVUsRUFBQ0EsQ0FBQSxLQUFJQTtJQUFVLENBQUMsQ0FBQztJQUFDLElBQUlDLE1BQU07SUFBQ2pCLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLGVBQWUsRUFBQztNQUFDYSxNQUFNQSxDQUFDWixDQUFDLEVBQUM7UUFBQ1ksTUFBTSxHQUFDWixDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSUYsS0FBSztJQUFDSCxNQUFNLENBQUNJLElBQUksQ0FBQyxjQUFjLEVBQUM7TUFBQ0QsS0FBS0EsQ0FBQ0UsQ0FBQyxFQUFDO1FBQUNGLEtBQUssR0FBQ0UsQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlhLEtBQUs7SUFBQ2xCLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLGNBQWMsRUFBQztNQUFDYyxLQUFLQSxDQUFDYixDQUFDLEVBQUM7UUFBQ2EsS0FBSyxHQUFDYixDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSWMsS0FBSztJQUFDbkIsTUFBTSxDQUFDSSxJQUFJLENBQUMsY0FBYyxFQUFDO01BQUNlLEtBQUtBLENBQUNkLENBQUMsRUFBQztRQUFDYyxLQUFLLEdBQUNkLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJQyxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxNQUFNQSxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQztJQUt0WSxNQUFNUSxLQUFLLEdBQUcsSUFBSVgsS0FBSyxDQUFDSSxVQUFVLENBQUMsT0FBTyxDQUFDO0lBRTNDLE1BQU1RLGNBQWMsR0FBRyxDQUM1QixhQUFhLEVBQ2IsUUFBUSxFQUNSLFdBQVcsRUFDWCxPQUFPLEVBQ1AsU0FBUyxFQUNULFVBQVUsRUFDVixVQUFVLEVBQ1YsT0FBTyxDQUNSO0lBRU0sTUFBTUMsVUFBVSxHQUFHLENBQ3hCO01BQUVJLElBQUksRUFBRSxLQUFLO01BQUVDLEtBQUssRUFBRTtJQUFVLENBQUMsRUFDakM7TUFBRUQsSUFBSSxFQUFFLFNBQVM7TUFBRUMsS0FBSyxFQUFFO0lBQVUsQ0FBQyxFQUNyQztNQUFFRCxJQUFJLEVBQUUsYUFBYTtNQUFFQyxLQUFLLEVBQUU7SUFBVSxDQUFDLEVBQ3pDO01BQUVELElBQUksRUFBRSxlQUFlO01BQUVDLEtBQUssRUFBRTtJQUFVLENBQUMsRUFDM0M7TUFBRUQsSUFBSSxFQUFFLFFBQVE7TUFBRUMsS0FBSyxFQUFFO0lBQVUsQ0FBQyxFQUNwQztNQUFFRCxJQUFJLEVBQUUsU0FBUztNQUFFQyxLQUFLLEVBQUU7SUFBVSxDQUFDLENBQ3RDO0lBRUQsSUFBSUosTUFBTSxDQUFDSyxRQUFRLEVBQUU7TUFDbkI7TUFDQUwsTUFBTSxDQUFDTSxPQUFPLENBQUMsT0FBTyxFQUFFLGtCQUFpQjtRQUFBLElBQUFDLFdBQUE7UUFDdkMsSUFBSSxDQUFDLElBQUksQ0FBQ0MsTUFBTSxFQUFFO1VBQ2hCLE9BQU8sSUFBSSxDQUFDQyxLQUFLLENBQUMsQ0FBQztRQUNyQjs7UUFFQTtRQUNBLE1BQU1DLElBQUksR0FBRyxNQUFNVixNQUFNLENBQUNXLEtBQUssQ0FBQ0MsWUFBWSxDQUFDLElBQUksQ0FBQ0osTUFBTSxDQUFDO1FBQ3pELE1BQU1LLE9BQU8sR0FBR0gsSUFBSSxhQUFKQSxJQUFJLHdCQUFBSCxXQUFBLEdBQUpHLElBQUksQ0FBRUksS0FBSyxjQUFBUCxXQUFBLHVCQUFYQSxXQUFBLENBQWFRLFFBQVEsQ0FBQyxPQUFPLENBQUM7O1FBRTlDO1FBQ0EsSUFBSUYsT0FBTyxFQUFFO1VBQ1gsT0FBT2hCLEtBQUssQ0FBQ21CLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRTtZQUNwQkMsSUFBSSxFQUFFO2NBQUVDLFNBQVMsRUFBRSxDQUFDO1lBQUUsQ0FBQztZQUN2QkMsTUFBTSxFQUFFO2NBQ05DLEtBQUssRUFBRSxDQUFDO2NBQ1JDLFdBQVcsRUFBRSxDQUFDO2NBQ2RDLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLE9BQU8sRUFBRSxDQUFDO2NBQ1ZDLFFBQVEsRUFBRSxDQUFDO2NBQ1hDLE1BQU0sRUFBRSxDQUFDO2NBQ1RDLFVBQVUsRUFBRSxDQUFDO2NBQ2JDLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLFFBQVEsRUFBRSxDQUFDO2NBQ1hDLE1BQU0sRUFBRSxDQUFDO2NBQ1RDLFFBQVEsRUFBRSxDQUFDO2NBQ1hDLFdBQVcsRUFBRSxDQUFDO2NBQ2RDLEtBQUssRUFBRSxDQUFDO2NBQ1JkLFNBQVMsRUFBRSxDQUFDO2NBQ1plLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLFNBQVMsRUFBRTtZQUNiO1VBQ0YsQ0FBQyxDQUFDO1FBQ0o7O1FBRUE7UUFDQSxPQUFPdEMsS0FBSyxDQUFDbUIsSUFBSSxDQUFDO1VBQ2hCb0IsR0FBRyxFQUFFLENBQ0g7WUFBRVYsVUFBVSxFQUFFLElBQUksQ0FBQ2xCO1VBQU8sQ0FBQyxFQUMzQjtZQUFFeUIsU0FBUyxFQUFFLElBQUksQ0FBQ3pCO1VBQU8sQ0FBQztRQUU5QixDQUFDLEVBQUU7VUFDRFMsSUFBSSxFQUFFO1lBQUVDLFNBQVMsRUFBRSxDQUFDO1VBQUUsQ0FBQztVQUN2QkMsTUFBTSxFQUFFO1lBQ05DLEtBQUssRUFBRSxDQUFDO1lBQ1JDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLE9BQU8sRUFBRSxDQUFDO1lBQ1ZDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFVBQVUsRUFBRSxDQUFDO1lBQ2JDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLEtBQUssRUFBRSxDQUFDO1lBQ1JkLFNBQVMsRUFBRSxDQUFDO1lBQ1plLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRTtVQUNiO1FBQ0YsQ0FBQyxDQUFDO01BQ0osQ0FBQyxDQUFDOztNQUVGO01BQ0FuQyxNQUFNLENBQUNNLE9BQU8sQ0FBQyxXQUFXLEVBQUUsWUFBVztRQUNyQytCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdDQUFnQyxDQUFDO1FBQzdDLElBQUksQ0FBQyxJQUFJLENBQUM5QixNQUFNLEVBQUU7VUFDaEI2QixPQUFPLENBQUNDLEdBQUcsQ0FBQyw0QkFBNEIsQ0FBQztVQUN6QyxPQUFPLElBQUksQ0FBQzdCLEtBQUssQ0FBQyxDQUFDO1FBQ3JCOztRQUVBO1FBQ0EsTUFBTThCLEtBQUssR0FBRzFDLEtBQUssQ0FBQ21CLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDd0IsS0FBSyxDQUFDLENBQUM7UUFDcENILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGNBQWMsRUFBRUMsS0FBSyxDQUFDRSxNQUFNLENBQUM7O1FBRXpDO1FBQ0EsTUFBTUMsT0FBTyxHQUFHLElBQUlDLEdBQUcsQ0FBQyxDQUFDO1FBQ3pCSixLQUFLLENBQUNLLE9BQU8sQ0FBQ0MsSUFBSSxJQUFJO1VBQ3BCO1VBQ0EsSUFBSUEsSUFBSSxDQUFDZCxXQUFXLEVBQUU7WUFDcEJjLElBQUksQ0FBQ2QsV0FBVyxDQUFDYSxPQUFPLENBQUNFLFVBQVUsSUFBSTtjQUNyQyxJQUFJQSxVQUFVLENBQUNDLFVBQVUsRUFBRTtnQkFDekJMLE9BQU8sQ0FBQ00sR0FBRyxDQUFDQyxNQUFNLENBQUNILFVBQVUsQ0FBQ0MsVUFBVSxDQUFDLENBQUM7Y0FDNUM7WUFDRixDQUFDLENBQUM7VUFDSjtVQUNBO1VBQ0EsSUFBSUYsSUFBSSxDQUFDYixLQUFLLEVBQUU7WUFDZGEsSUFBSSxDQUFDYixLQUFLLENBQUNZLE9BQU8sQ0FBQ3pELElBQUksSUFBSTtjQUN6QixJQUFJQSxJQUFJLENBQUMrRCxPQUFPLEVBQUU7Z0JBQ2hCUixPQUFPLENBQUNNLEdBQUcsQ0FBQ0MsTUFBTSxDQUFDOUQsSUFBSSxDQUFDK0QsT0FBTyxDQUFDLENBQUM7Y0FDbkM7WUFDRixDQUFDLENBQUM7VUFDSjtVQUNBO1VBQ0EsSUFBSUwsSUFBSSxDQUFDbkIsVUFBVSxFQUFFO1lBQ25CbUIsSUFBSSxDQUFDbkIsVUFBVSxDQUFDa0IsT0FBTyxDQUFDcEMsTUFBTSxJQUFJO2NBQ2hDa0MsT0FBTyxDQUFDTSxHQUFHLENBQUNDLE1BQU0sQ0FBQ3pDLE1BQU0sQ0FBQyxDQUFDO1lBQzdCLENBQUMsQ0FBQztVQUNKO1FBQ0YsQ0FBQyxDQUFDO1FBRUYsTUFBTTJDLFdBQVcsR0FBR0MsS0FBSyxDQUFDQyxJQUFJLENBQUNYLE9BQU8sQ0FBQztRQUN2Q0wsT0FBTyxDQUFDQyxHQUFHLENBQUMsK0JBQStCLEVBQUVhLFdBQVcsQ0FBQzs7UUFFekQ7UUFDQSxNQUFNeEMsS0FBSyxHQUFHWCxNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUM3QjtVQUFFc0MsR0FBRyxFQUFFO1lBQUVDLEdBQUcsRUFBRUo7VUFBWTtRQUFFLENBQUMsRUFDN0I7VUFDRWhDLE1BQU0sRUFBRTtZQUNObUMsR0FBRyxFQUFFLENBQUM7WUFDTkUsTUFBTSxFQUFFLENBQUM7WUFDVDFDLEtBQUssRUFBRSxDQUFDO1lBQ1IsbUJBQW1CLEVBQUUsQ0FBQztZQUN0QixrQkFBa0IsRUFBRSxDQUFDO1lBQ3JCLGNBQWMsRUFBRSxDQUFDO1lBQ2pCLG9CQUFvQixFQUFFLENBQUM7WUFDdkIsZ0JBQWdCLEVBQUUsQ0FBQztZQUNuQixrQkFBa0IsRUFBRSxDQUFDO1lBQ3JCSSxTQUFTLEVBQUU7VUFDYjtRQUNGLENBQ0YsQ0FBQyxDQUFDc0IsS0FBSyxDQUFDLENBQUM7UUFFVEgsT0FBTyxDQUFDQyxHQUFHLENBQUMsY0FBYyxFQUFFM0IsS0FBSyxDQUFDOEMsR0FBRyxDQUFDQyxDQUFDO1VBQUEsSUFBQUMsVUFBQSxFQUFBQyxXQUFBO1VBQUEsT0FBSztZQUMxQ04sR0FBRyxFQUFFSSxDQUFDLENBQUNKLEdBQUc7WUFDVm5ELElBQUksRUFBRSxHQUFBMEQsTUFBQSxDQUFHLEVBQUFGLFVBQUEsR0FBQUQsQ0FBQyxDQUFDSSxPQUFPLGNBQUFILFVBQUEsdUJBQVRBLFVBQUEsQ0FBV0ksU0FBUyxLQUFJLEVBQUUsT0FBQUYsTUFBQSxDQUFJLEVBQUFELFdBQUEsR0FBQUYsQ0FBQyxDQUFDSSxPQUFPLGNBQUFGLFdBQUEsdUJBQVRBLFdBQUEsQ0FBV0ksUUFBUSxLQUFJLEVBQUUsRUFBR0MsSUFBSSxDQUFDLENBQUM7WUFDekVDLFVBQVUsRUFBRSxDQUFDLENBQUNSLENBQUMsQ0FBQ0k7VUFDbEIsQ0FBQztRQUFBLENBQUMsQ0FBQyxDQUFDOztRQUVKO1FBQ0EsT0FBTzlELE1BQU0sQ0FBQ1csS0FBSyxDQUFDSyxJQUFJLENBQ3RCO1VBQUVzQyxHQUFHLEVBQUU7WUFBRUMsR0FBRyxFQUFFSjtVQUFZO1FBQUUsQ0FBQyxFQUM3QjtVQUNBaEMsTUFBTSxFQUFFO1lBQ05tQyxHQUFHLEVBQUUsQ0FBQztZQUNKRSxNQUFNLEVBQUUsQ0FBQztZQUNUMUMsS0FBSyxFQUFFLENBQUM7WUFDVixtQkFBbUIsRUFBRSxDQUFDO1lBQ3RCLGtCQUFrQixFQUFFLENBQUM7WUFDbkIsY0FBYyxFQUFFLENBQUM7WUFDakIsb0JBQW9CLEVBQUUsQ0FBQztZQUN2QixnQkFBZ0IsRUFBRSxDQUFDO1lBQ25CLGtCQUFrQixFQUFFLENBQUM7WUFDckJJLFNBQVMsRUFBRTtVQUNiO1FBQ0YsQ0FDRixDQUFDO01BQ0gsQ0FBQyxDQUFDOztNQUVGO01BQ0FsQixNQUFNLENBQUNNLE9BQU8sQ0FBQyxNQUFNLEVBQUUsVUFBUzZELE1BQU0sRUFBRTtRQUFBLElBQUFDLFlBQUE7UUFDdENuRSxLQUFLLENBQUNrRSxNQUFNLEVBQUVsQixNQUFNLENBQUM7UUFFckIsSUFBSSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sRUFBRTtVQUNoQixPQUFPLElBQUksQ0FBQ0MsS0FBSyxDQUFDLENBQUM7UUFDckI7UUFFQSxNQUFNQyxJQUFJLEdBQUdWLE1BQU0sQ0FBQ1csS0FBSyxDQUFDMEQsT0FBTyxDQUFDLElBQUksQ0FBQzdELE1BQU0sQ0FBQztRQUM5QyxNQUFNSyxPQUFPLEdBQUdILElBQUksYUFBSkEsSUFBSSx3QkFBQTBELFlBQUEsR0FBSjFELElBQUksQ0FBRUksS0FBSyxjQUFBc0QsWUFBQSx1QkFBWEEsWUFBQSxDQUFhckQsUUFBUSxDQUFDLE9BQU8sQ0FBQzs7UUFFOUM7UUFDQSxNQUFNdUQsTUFBTSxHQUFHekUsS0FBSyxDQUFDbUIsSUFBSSxDQUN2QjtVQUFFc0MsR0FBRyxFQUFFYTtRQUFPLENBQUMsRUFDZjtVQUNFaEQsTUFBTSxFQUFFO1lBQ05DLEtBQUssRUFBRSxDQUFDO1lBQ1JDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLE9BQU8sRUFBRSxDQUFDO1lBQ1ZDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFVBQVUsRUFBRSxDQUFDO1lBQ2JDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLEtBQUssRUFBRSxDQUFDO1lBQ1JkLFNBQVMsRUFBRSxDQUFDO1lBQ1plLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRTtVQUNiO1FBQ0YsQ0FDRixDQUFDOztRQUVEO1FBQ0FFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtCQUFrQixFQUFFNkIsTUFBTSxDQUFDO1FBQ3ZDRyxNQUFNLENBQUNDLE9BQU8sQ0FBQztVQUNiQyxLQUFLLEVBQUdDLEdBQUcsSUFBS3BDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDRCQUE0QixFQUFFbUMsR0FBRyxDQUFDbkIsR0FBRyxDQUFDO1VBQ2xFb0IsT0FBTyxFQUFHRCxHQUFHLElBQUtwQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyw4QkFBOEIsRUFBRW1DLEdBQUcsQ0FBQ25CLEdBQUcsQ0FBQztVQUN0RXFCLE9BQU8sRUFBR0YsR0FBRyxJQUFLcEMsT0FBTyxDQUFDQyxHQUFHLENBQUMsZ0NBQWdDLEVBQUVtQyxHQUFHLENBQUNuQixHQUFHO1FBQ3pFLENBQUMsQ0FBQztRQUVGLE9BQU9nQixNQUFNO01BQ2YsQ0FBQyxDQUFDO0lBQ0o7SUFFQXRFLE1BQU0sQ0FBQzRFLE9BQU8sQ0FBQztNQUNiLE1BQU0sY0FBY0MsQ0FBQ2hDLElBQUksRUFBRTtRQUN6QjVDLEtBQUssQ0FBQzRDLElBQUksRUFBRTtVQUNWekIsS0FBSyxFQUFFNkIsTUFBTTtVQUNiNUIsV0FBVyxFQUFFNEIsTUFBTTtVQUNuQjNCLFNBQVMsRUFBRXdELElBQUk7VUFDZnZELE9BQU8sRUFBRXVELElBQUk7VUFDYnRELFFBQVEsRUFBRXlCLE1BQU07VUFDaEJ4QixNQUFNLEVBQUV3QixNQUFNO1VBQ2R2QixVQUFVLEVBQUUwQixLQUFLO1VBQ2pCekIsU0FBUyxFQUFFeUIsS0FBSztVQUNoQnhCLFFBQVEsRUFBRXFCLE1BQU07VUFDaEJwQixNQUFNLEVBQUV1QixLQUFLO1VBQ2J0QixRQUFRLEVBQUVpRDtRQUNaLENBQUMsQ0FBQztRQUVGLElBQUksQ0FBQyxJQUFJLENBQUN2RSxNQUFNLEVBQUU7VUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsaUJBQWlCLENBQUM7UUFDM0M7UUFFQTNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQixFQUFFTyxJQUFJLENBQUMsQ0FBQyxDQUFDOztRQUV6QztRQUNBLE1BQU1vQyxrQkFBa0IsR0FBR3BDLElBQUksQ0FBQ2xCLFNBQVMsQ0FBQzhCLEdBQUcsQ0FBQ3lCLElBQUksS0FBSztVQUNyREMsSUFBSSxFQUFFRCxJQUFJLENBQUNDLElBQUk7VUFDZkMsU0FBUyxFQUFFRixJQUFJLENBQUNFLFNBQVMsSUFBSTtRQUMvQixDQUFDLENBQUMsQ0FBQztRQUVILE1BQU1DLFlBQVksR0FBQTFGLGFBQUEsQ0FBQUEsYUFBQSxLQUNia0QsSUFBSTtVQUNQM0IsU0FBUyxFQUFFLElBQUk0RCxJQUFJLENBQUMsQ0FBQztVQUNyQjdDLFNBQVMsRUFBRSxJQUFJLENBQUN6QixNQUFNO1VBQ3RCMEIsU0FBUyxFQUFFLElBQUk0QyxJQUFJLENBQUMsQ0FBQztVQUNyQjNDLFNBQVMsRUFBRSxJQUFJLENBQUMzQixNQUFNO1VBQ3RCc0IsUUFBUSxFQUFFZSxJQUFJLENBQUNmLFFBQVEsSUFBSSxDQUFDO1VBQzVCTCxNQUFNLEVBQUUsU0FBUztVQUFFO1VBQ25CRSxTQUFTLEVBQUVzRCxrQkFBa0I7VUFDN0JwRCxNQUFNLEVBQUVnQixJQUFJLENBQUNoQixNQUFNLElBQUksRUFBRTtVQUN6QkQsUUFBUSxFQUFFaUIsSUFBSSxDQUFDakIsUUFBUSxJQUFJLEVBQUU7VUFDN0JGLFVBQVUsRUFBRW1CLElBQUksQ0FBQ25CLFVBQVUsSUFBSTtRQUFFLEVBQ2xDO1FBRURXLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDZCQUE2QixFQUFFK0MsWUFBWSxDQUFDLENBQUMsQ0FBQzs7UUFFMUQsSUFBSTtVQUNGLE1BQU1DLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDMEYsV0FBVyxDQUFDRixZQUFZLENBQUM7VUFDcERoRCxPQUFPLENBQUNDLEdBQUcsQ0FBQyw0QkFBNEIsRUFBRWdELE1BQU0sQ0FBQyxDQUFDLENBQUM7VUFDbkQsT0FBT0EsTUFBTTtRQUNmLENBQUMsQ0FBQyxPQUFPRSxLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQyxzQkFBc0IsRUFBRUEsS0FBSyxDQUFDO1VBQzVDLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxzQkFBc0IsRUFBRVEsS0FBSyxDQUFDQyxPQUFPLENBQUM7UUFDL0Q7TUFDRixDQUFDO01BRUQsTUFBTSxjQUFjQyxDQUFDdkIsTUFBTSxFQUFFdEIsSUFBSSxFQUFFO1FBQ2pDLElBQUk7VUFBQSxJQUFBOEMsWUFBQTtVQUNGdEQsT0FBTyxDQUFDQyxHQUFHLENBQUMsdUJBQXVCLEVBQUU7WUFBRTZCLE1BQU07WUFBRXRCO1VBQUssQ0FBQyxDQUFDO1VBRXRENUMsS0FBSyxDQUFDa0UsTUFBTSxFQUFFbEIsTUFBTSxDQUFDO1VBQ3JCaEQsS0FBSyxDQUFDNEMsSUFBSSxFQUFFO1lBQ1Z6QixLQUFLLEVBQUU2QixNQUFNO1lBQ2I1QixXQUFXLEVBQUU0QixNQUFNO1lBQ25CM0IsU0FBUyxFQUFFd0QsSUFBSTtZQUNmdkQsT0FBTyxFQUFFdUQsSUFBSTtZQUNidEQsUUFBUSxFQUFFeUIsTUFBTTtZQUNoQnZCLFVBQVUsRUFBRTBCLEtBQUs7WUFDakJ6QixTQUFTLEVBQUV5QixLQUFLO1lBQ2hCeEIsUUFBUSxFQUFFMUIsS0FBSyxDQUFDMEYsUUFBUSxDQUFDM0MsTUFBTSxDQUFDO1lBQ2hDcEIsTUFBTSxFQUFFM0IsS0FBSyxDQUFDMEYsUUFBUSxDQUFDeEMsS0FBSyxDQUFDO1lBQzdCdEIsUUFBUSxFQUFFNUIsS0FBSyxDQUFDMEYsUUFBUSxDQUFDYixNQUFNLENBQUM7WUFDaEN0RCxNQUFNLEVBQUV2QixLQUFLLENBQUMwRixRQUFRLENBQUMzQyxNQUFNLENBQUM7WUFDOUJsQixXQUFXLEVBQUU3QixLQUFLLENBQUMwRixRQUFRLENBQUN4QyxLQUFLO1VBQ25DLENBQUMsQ0FBQztVQUVGLElBQUksQ0FBQyxJQUFJLENBQUM1QyxNQUFNLEVBQUU7WUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsOENBQThDLENBQUM7VUFDMUY7VUFFQSxNQUFNYSxZQUFZLEdBQUcsTUFBTWhHLEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQ3JELElBQUksQ0FBQzBCLFlBQVksRUFBRTtZQUNqQixNQUFNLElBQUk3RixNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLGdCQUFnQixDQUFDO1VBQ3ZEOztVQUVBO1VBQ0EsTUFBTXRFLElBQUksR0FBRyxNQUFNVixNQUFNLENBQUNXLEtBQUssQ0FBQ0MsWUFBWSxDQUFDLElBQUksQ0FBQ0osTUFBTSxDQUFDO1VBQ3pELE1BQU1LLE9BQU8sR0FBR0gsSUFBSSxhQUFKQSxJQUFJLHdCQUFBaUYsWUFBQSxHQUFKakYsSUFBSSxDQUFFSSxLQUFLLGNBQUE2RSxZQUFBLHVCQUFYQSxZQUFBLENBQWE1RSxRQUFRLENBQUMsT0FBTyxDQUFDO1VBRTlDLElBQUksQ0FBQ0YsT0FBTyxJQUFJLENBQUNnRixZQUFZLENBQUNuRSxVQUFVLENBQUNYLFFBQVEsQ0FBQyxJQUFJLENBQUNQLE1BQU0sQ0FBQyxFQUFFO1lBQzlELE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDRDQUE0QyxDQUFDO1VBQ3hGOztVQUVBO1VBQ0EsTUFBTWMsY0FBYyxHQUFHakQsSUFBSSxDQUFDbEIsU0FBUyxDQUFDb0UsTUFBTSxDQUFDYixJQUFJLElBQUlBLElBQUksQ0FBQ0UsU0FBUyxDQUFDLENBQUMzQyxNQUFNO1VBQzNFLE1BQU11RCxVQUFVLEdBQUduRCxJQUFJLENBQUNsQixTQUFTLENBQUNjLE1BQU07VUFDeEMsTUFBTVgsUUFBUSxHQUFHa0UsVUFBVSxHQUFHLENBQUMsR0FBR0MsSUFBSSxDQUFDQyxLQUFLLENBQUVKLGNBQWMsR0FBR0UsVUFBVSxHQUFJLEdBQUcsQ0FBQyxHQUFHLENBQUM7O1VBRXJGO1VBQ0EsSUFBSXZFLE1BQU0sR0FBR29CLElBQUksQ0FBQ3BCLE1BQU07VUFDeEIsSUFBSUssUUFBUSxLQUFLLEdBQUcsRUFBRTtZQUNwQkwsTUFBTSxHQUFHLFdBQVc7VUFDdEIsQ0FBQyxNQUFNLElBQUlLLFFBQVEsR0FBRyxDQUFDLEVBQUU7WUFDdkJMLE1BQU0sR0FBRyxhQUFhO1VBQ3hCLENBQUMsTUFBTTtZQUNMQSxNQUFNLEdBQUcsU0FBUztVQUNwQjtVQUVBLE1BQU0wRSxZQUFZLEdBQUF4RyxhQUFBLENBQUFBLGFBQUEsS0FDYmtELElBQUk7WUFDUFgsU0FBUyxFQUFFLElBQUk0QyxJQUFJLENBQUMsQ0FBQztZQUNyQjNDLFNBQVMsRUFBRSxJQUFJLENBQUMzQixNQUFNO1lBQ3RCc0IsUUFBUTtZQUNSTCxNQUFNO1lBQ05HLFFBQVEsRUFBRWlCLElBQUksQ0FBQ2pCLFFBQVEsSUFBSWlFLFlBQVksQ0FBQ2pFLFFBQVEsSUFBSSxFQUFFO1lBQ3REQyxNQUFNLEVBQUVnQixJQUFJLENBQUNoQixNQUFNLElBQUlnRSxZQUFZLENBQUNoRSxNQUFNLElBQUksRUFBRTtZQUNoREUsV0FBVyxFQUFFYyxJQUFJLENBQUNkLFdBQVcsSUFBSThELFlBQVksQ0FBQzlELFdBQVcsSUFBSTtVQUFFLEVBQ2hFO1VBRUQsTUFBTXVELE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDdUcsV0FBVyxDQUNwQztZQUFFOUMsR0FBRyxFQUFFYTtVQUFPLENBQUMsRUFDZjtZQUFFa0MsSUFBSSxFQUFFRjtVQUFhLENBQ3ZCLENBQUM7VUFFRCxJQUFJYixNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsdUJBQXVCLENBQUM7VUFDbEU7VUFFQSxPQUFPTSxNQUFNO1FBQ2YsQ0FBQyxDQUFDLE9BQU9FLEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHNCQUFzQixFQUFFQSxLQUFLLENBQUM7VUFDNUMsSUFBSUEsS0FBSyxZQUFZeEYsTUFBTSxDQUFDZ0YsS0FBSyxFQUFFO1lBQ2pDLE1BQU1RLEtBQUs7VUFDYjtVQUNBLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxjQUFjLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxJQUFJLDhCQUE4QixDQUFDO1FBQ3pGO01BQ0YsQ0FBQztNQUVELE1BQU0sY0FBY2EsQ0FBQ25DLE1BQU0sRUFBRTtRQUMzQmxFLEtBQUssQ0FBQ2tFLE1BQU0sRUFBRWxCLE1BQU0sQ0FBQztRQUVyQixJQUFJLENBQUMsSUFBSSxDQUFDekMsTUFBTSxFQUFFO1VBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixDQUFDO1FBQzNDO1FBRUEsSUFBSTtVQUNGLE1BQU1NLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDMEcsV0FBVyxDQUFDcEMsTUFBTSxDQUFDO1VBRTlDLElBQUltQixNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxXQUFXLEVBQUUsZ0JBQWdCLENBQUM7VUFDdkQ7VUFFQSxPQUFPTSxNQUFNO1FBQ2YsQ0FBQyxDQUFDLE9BQU9FLEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHNCQUFzQixFQUFFQSxLQUFLLENBQUM7VUFDNUMsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLG9CQUFvQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUM3RDtNQUNGLENBQUM7TUFFRCxzQkFBc0JlLENBQUNyQyxNQUFNLEVBQUVyQyxRQUFRLEVBQUU7UUFDdkM3QixLQUFLLENBQUNrRSxNQUFNLEVBQUVsQixNQUFNLENBQUM7UUFDckJoRCxLQUFLLENBQUM2QixRQUFRLEVBQUVpRCxNQUFNLENBQUM7UUFFdkIsSUFBSSxDQUFDLElBQUksQ0FBQ3ZFLE1BQU0sRUFBRTtVQUNoQixNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztRQUMzQztRQUVBLE1BQU1uQyxJQUFJLEdBQUdoRCxLQUFLLENBQUN3RSxPQUFPLENBQUNGLE1BQU0sQ0FBQztRQUNsQyxJQUFJLENBQUN0QixJQUFJLEVBQUU7VUFDVCxNQUFNLElBQUk3QyxNQUFNLENBQUNnRixLQUFLLENBQUMsaUJBQWlCLENBQUM7UUFDM0M7O1FBRUE7UUFDQSxJQUFJLENBQUNuQyxJQUFJLENBQUNuQixVQUFVLENBQUNYLFFBQVEsQ0FBQyxJQUFJLENBQUNQLE1BQU0sQ0FBQyxFQUFFO1VBQzFDLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLHFDQUFxQyxDQUFDO1FBQy9EOztRQUVBO1FBQ0EsSUFBSXZELE1BQU0sR0FBR29CLElBQUksQ0FBQ3BCLE1BQU07UUFDeEIsSUFBSUssUUFBUSxLQUFLLEdBQUcsRUFBRTtVQUNwQkwsTUFBTSxHQUFHLFdBQVc7UUFDdEIsQ0FBQyxNQUFNLElBQUlLLFFBQVEsR0FBRyxDQUFDLEVBQUU7VUFDdkJMLE1BQU0sR0FBRyxhQUFhO1FBQ3hCO1FBRUEsT0FBTzVCLEtBQUssQ0FBQzRHLE1BQU0sQ0FBQ3RDLE1BQU0sRUFBRTtVQUMxQmtDLElBQUksRUFBRTtZQUNKdkUsUUFBUTtZQUNSTCxNQUFNO1lBQ05TLFNBQVMsRUFBRSxJQUFJNEMsSUFBSSxDQUFDLENBQUM7WUFDckIzQyxTQUFTLEVBQUUsSUFBSSxDQUFDM0I7VUFDbEI7UUFDRixDQUFDLENBQUM7TUFDSixDQUFDO01BRUQsTUFBTSwyQkFBMkJrRyxDQUFDdkMsTUFBTSxFQUFFd0MsU0FBUyxFQUFFO1FBQ25ELElBQUk7VUFBQSxJQUFBQyxZQUFBO1VBQ0Z2RSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxvQ0FBb0MsRUFBRTtZQUFFNkIsTUFBTTtZQUFFd0MsU0FBUztZQUFFbkcsTUFBTSxFQUFFLElBQUksQ0FBQ0E7VUFBTyxDQUFDLENBQUM7VUFFN0YsSUFBSSxDQUFDLElBQUksQ0FBQ0EsTUFBTSxFQUFFO1lBQ2hCNkIsT0FBTyxDQUFDQyxHQUFHLENBQUMsa0JBQWtCLENBQUM7WUFDL0IsTUFBTSxJQUFJdEMsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDhDQUE4QyxDQUFDO1VBQzFGOztVQUVBO1VBQ0EsSUFBSSxDQUFDYixNQUFNLElBQUksT0FBT0EsTUFBTSxLQUFLLFFBQVEsRUFBRTtZQUN6QzlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGlCQUFpQixFQUFFNkIsTUFBTSxDQUFDO1lBQ3RDLE1BQU0sSUFBSW5FLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsaUJBQWlCLENBQUM7VUFDNUQ7VUFFQSxJQUFJLE9BQU8yQixTQUFTLEtBQUssUUFBUSxJQUFJQSxTQUFTLEdBQUcsQ0FBQyxFQUFFO1lBQ2xEdEUsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0JBQW9CLEVBQUVxRSxTQUFTLENBQUM7WUFDNUMsTUFBTSxJQUFJM0csTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGVBQWUsRUFBRSw4QkFBOEIsQ0FBQztVQUN6RTtVQUVBLE1BQU1uQyxJQUFJLEdBQUcsTUFBTWhELEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQzdDOUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsYUFBYSxFQUFFTyxJQUFJLENBQUM7VUFFaEMsSUFBSSxDQUFDQSxJQUFJLEVBQUU7WUFDVFIsT0FBTyxDQUFDQyxHQUFHLENBQUMsZ0JBQWdCLENBQUM7WUFDN0IsTUFBTSxJQUFJdEMsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLFdBQVcsRUFBRSxnQkFBZ0IsQ0FBQztVQUN2RDs7VUFFQTtVQUNBLE1BQU10RSxJQUFJLEdBQUcsTUFBTVYsTUFBTSxDQUFDVyxLQUFLLENBQUNDLFlBQVksQ0FBQyxJQUFJLENBQUNKLE1BQU0sQ0FBQztVQUN6RDZCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGFBQWEsRUFBRTVCLElBQUksQ0FBQztVQUVoQyxNQUFNRyxPQUFPLEdBQUdILElBQUksYUFBSkEsSUFBSSx3QkFBQWtHLFlBQUEsR0FBSmxHLElBQUksQ0FBRUksS0FBSyxjQUFBOEYsWUFBQSx1QkFBWEEsWUFBQSxDQUFhN0YsUUFBUSxDQUFDLE9BQU8sQ0FBQztVQUM5Q3NCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLFdBQVcsRUFBRXpCLE9BQU8sQ0FBQztVQUVqQyxJQUFJLENBQUNBLE9BQU8sSUFBSSxDQUFDZ0MsSUFBSSxDQUFDbkIsVUFBVSxDQUFDWCxRQUFRLENBQUMsSUFBSSxDQUFDUCxNQUFNLENBQUMsRUFBRTtZQUN0RDZCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQixFQUFFO2NBQUU5QixNQUFNLEVBQUUsSUFBSSxDQUFDQSxNQUFNO2NBQUVrQixVQUFVLEVBQUVtQixJQUFJLENBQUNuQjtZQUFXLENBQUMsQ0FBQztZQUN6RixNQUFNLElBQUkxQixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsNENBQTRDLENBQUM7VUFDeEY7VUFFQSxNQUFNckQsU0FBUyxHQUFHa0IsSUFBSSxDQUFDbEIsU0FBUyxJQUFJLEVBQUU7VUFDdENVLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQixFQUFFWCxTQUFTLENBQUM7VUFFNUMsSUFBSWdGLFNBQVMsSUFBSWhGLFNBQVMsQ0FBQ2MsTUFBTSxFQUFFO1lBQ2pDSixPQUFPLENBQUNDLEdBQUcsQ0FBQyxxQkFBcUIsRUFBRTtjQUFFcUUsU0FBUztjQUFFRSxlQUFlLEVBQUVsRixTQUFTLENBQUNjO1lBQU8sQ0FBQyxDQUFDO1lBQ3BGLE1BQU0sSUFBSXpDLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsOEJBQThCLENBQUM7VUFDekU7O1VBRUE7VUFDQSxNQUFNOEIsZ0JBQWdCLEdBQUcsQ0FBQyxHQUFHbkYsU0FBUyxDQUFDO1VBQ3ZDbUYsZ0JBQWdCLENBQUNILFNBQVMsQ0FBQyxHQUFBaEgsYUFBQSxDQUFBQSxhQUFBLEtBQ3RCbUgsZ0JBQWdCLENBQUNILFNBQVMsQ0FBQztZQUM5QnZCLFNBQVMsRUFBRSxDQUFDMEIsZ0JBQWdCLENBQUNILFNBQVMsQ0FBQyxDQUFDdkI7VUFBUyxFQUNsRDtVQUVEL0MsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0JBQW9CLEVBQUV3RSxnQkFBZ0IsQ0FBQzs7VUFFbkQ7VUFDQSxNQUFNaEIsY0FBYyxHQUFHZ0IsZ0JBQWdCLENBQUNmLE1BQU0sQ0FBQ2IsSUFBSSxJQUFJQSxJQUFJLENBQUNFLFNBQVMsQ0FBQyxDQUFDM0MsTUFBTTtVQUM3RSxNQUFNdUQsVUFBVSxHQUFHYyxnQkFBZ0IsQ0FBQ3JFLE1BQU07VUFDMUMsTUFBTVgsUUFBUSxHQUFHa0UsVUFBVSxHQUFHLENBQUMsR0FBR0MsSUFBSSxDQUFDQyxLQUFLLENBQUVKLGNBQWMsR0FBR0UsVUFBVSxHQUFJLEdBQUcsQ0FBQyxHQUFHLENBQUM7O1VBRXJGO1VBQ0EsSUFBSXZFLE1BQU07VUFDVixJQUFJSyxRQUFRLEtBQUssR0FBRyxFQUFFO1lBQ3BCTCxNQUFNLEdBQUcsV0FBVztVQUN0QixDQUFDLE1BQU0sSUFBSUssUUFBUSxHQUFHLENBQUMsRUFBRTtZQUN2QkwsTUFBTSxHQUFHLGFBQWE7VUFDeEIsQ0FBQyxNQUFNO1lBQ0xBLE1BQU0sR0FBRyxTQUFTO1VBQ3BCO1VBRUFZLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHFCQUFxQixFQUFFO1lBQ2pDNkIsTUFBTTtZQUNOMkMsZ0JBQWdCO1lBQ2hCaEYsUUFBUTtZQUNSTDtVQUNGLENBQUMsQ0FBQzs7VUFFRjtVQUNBLE1BQU1vRSxZQUFZLEdBQUcsTUFBTWhHLEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQ3JELElBQUksQ0FBQzBCLFlBQVksRUFBRTtZQUNqQixNQUFNLElBQUk3RixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsdUJBQXVCLENBQUM7VUFDbkU7O1VBRUE7VUFDQSxNQUFNK0IsWUFBWSxHQUFHLE1BQU1sSCxLQUFLLENBQUN1RyxXQUFXLENBQzFDO1lBQUU5QyxHQUFHLEVBQUVhO1VBQU8sQ0FBQyxFQUNmO1lBQ0VrQyxJQUFJLEVBQUU7Y0FDSjFFLFNBQVMsRUFBRW1GLGdCQUFnQjtjQUMzQmhGLFFBQVE7Y0FDUkwsTUFBTTtjQUNOUyxTQUFTLEVBQUUsSUFBSTRDLElBQUksQ0FBQyxDQUFDO2NBQ3JCM0MsU0FBUyxFQUFFLElBQUksQ0FBQzNCO1lBQ2xCO1VBQ0YsQ0FDRixDQUFDO1VBRUQ2QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxnQkFBZ0IsRUFBRXlFLFlBQVksQ0FBQztVQUUzQyxJQUFJQSxZQUFZLEtBQUssQ0FBQyxFQUFFO1lBQ3RCLE1BQU0sSUFBSS9HLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsdUJBQXVCLENBQUM7VUFDbEU7O1VBRUE7VUFDQSxNQUFNZ0MsV0FBVyxHQUFHLE1BQU1uSCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUNwRDlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQixFQUFFMEUsV0FBVyxDQUFDO1VBRTlDLE9BQU9ELFlBQVk7UUFDckIsQ0FBQyxDQUFDLE9BQU92QixLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQywrQkFBK0IsRUFBRUEsS0FBSyxDQUFDO1VBQ3JELElBQUlBLEtBQUssWUFBWXhGLE1BQU0sQ0FBQ2dGLEtBQUssRUFBRTtZQUNqQyxNQUFNUSxLQUFLO1VBQ2I7VUFDQSxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsY0FBYyxFQUFFUSxLQUFLLENBQUNDLE9BQU8sSUFBSSw4QkFBOEIsQ0FBQztRQUN6RjtNQUNGLENBQUM7TUFFRCxNQUFNLHFCQUFxQndCLENBQUM5QyxNQUFNLEVBQUUrQyxRQUFRLEVBQUU7UUFDNUMsSUFBSTtVQUNGLElBQUksQ0FBQyxJQUFJLENBQUMxRyxNQUFNLEVBQUU7WUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsOENBQThDLENBQUM7VUFDMUY7VUFFQSxNQUFNbkMsSUFBSSxHQUFHLE1BQU1oRCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUM3QyxJQUFJLENBQUN0QixJQUFJLEVBQUU7WUFDVCxNQUFNLElBQUk3QyxNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLGdCQUFnQixDQUFDO1VBQ3ZEOztVQUVBO1VBQ0EsSUFBSSxDQUFDbkMsSUFBSSxDQUFDbkIsVUFBVSxDQUFDWCxRQUFRLENBQUMsSUFBSSxDQUFDUCxNQUFNLENBQUMsRUFBRTtZQUMxQyxNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx3REFBd0QsQ0FBQztVQUNwRztVQUVBLElBQUksQ0FBQ2tDLFFBQVEsSUFBSSxDQUFDQSxRQUFRLENBQUMvRyxJQUFJLElBQUksQ0FBQytHLFFBQVEsQ0FBQ0MsSUFBSSxFQUFFO1lBQ2pELE1BQU0sSUFBSW5ILE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsbUJBQW1CLENBQUM7VUFDOUQ7O1VBRUE7VUFDQSxNQUFNb0MsVUFBVSxHQUFHbkUsTUFBTSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sQ0FBQzs7VUFFdEM7VUFDQSxNQUFNOEUsTUFBTSxHQUFHLE1BQU16RixLQUFLLENBQUN1RyxXQUFXLENBQ3BDO1lBQUU5QyxHQUFHLEVBQUVhO1VBQU8sQ0FBQyxFQUNmO1lBQ0VrRCxLQUFLLEVBQUU7Y0FDTHRGLFdBQVcsRUFBRTtnQkFDWDVCLElBQUksRUFBRStHLFFBQVEsQ0FBQy9HLElBQUk7Z0JBQ25CbUgsSUFBSSxFQUFFSixRQUFRLENBQUNJLElBQUk7Z0JBQ25CSCxJQUFJLEVBQUVELFFBQVEsQ0FBQ0MsSUFBSTtnQkFDbkJJLFVBQVUsRUFBRSxJQUFJekMsSUFBSSxDQUFDLENBQUM7Z0JBQ3RCL0IsVUFBVSxFQUFFcUU7Y0FDZDtZQUNGLENBQUM7WUFDRGYsSUFBSSxFQUFFO2NBQ0puRSxTQUFTLEVBQUUsSUFBSTRDLElBQUksQ0FBQyxDQUFDO2NBQ3JCM0MsU0FBUyxFQUFFaUY7WUFDYjtVQUNGLENBQ0YsQ0FBQztVQUVELElBQUk5QixNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsMEJBQTBCLENBQUM7VUFDckU7O1VBRUE7VUFDQSxNQUFNZ0MsV0FBVyxHQUFHLE1BQU1uSCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUNwRDlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLCtCQUErQixFQUFFMEUsV0FBVyxDQUFDO1VBQ3pELE9BQU9BLFdBQVc7UUFDcEIsQ0FBQyxDQUFDLE9BQU94QixLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQywwQkFBMEIsRUFBRUEsS0FBSyxDQUFDO1VBQ2hELElBQUlBLEtBQUssWUFBWXhGLE1BQU0sQ0FBQ2dGLEtBQUssRUFBRTtZQUNqQyxNQUFNUSxLQUFLO1VBQ2I7VUFDQSxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsY0FBYyxFQUFFUSxLQUFLLENBQUNDLE9BQU8sSUFBSSw4QkFBOEIsQ0FBQztRQUN6RjtNQUNGLENBQUM7TUFFRCxNQUFNLGVBQWUrQixDQUFDckQsTUFBTSxFQUFFaEYsSUFBSSxFQUFFO1FBQ2xDLElBQUk7VUFDRixJQUFJLENBQUMsSUFBSSxDQUFDcUIsTUFBTSxFQUFFO1lBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDhDQUE4QyxDQUFDO1VBQzFGO1VBRUEsTUFBTW5DLElBQUksR0FBRyxNQUFNaEQsS0FBSyxDQUFDZSxZQUFZLENBQUN1RCxNQUFNLENBQUM7VUFDN0MsSUFBSSxDQUFDdEIsSUFBSSxFQUFFO1lBQ1QsTUFBTSxJQUFJN0MsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLFdBQVcsRUFBRSxnQkFBZ0IsQ0FBQztVQUN2RDs7VUFFQTtVQUNBLElBQUksQ0FBQ25DLElBQUksQ0FBQ25CLFVBQVUsQ0FBQ1gsUUFBUSxDQUFDLElBQUksQ0FBQ1AsTUFBTSxDQUFDLEVBQUU7WUFDMUMsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsa0RBQWtELENBQUM7VUFDOUY7VUFFQSxJQUFJLENBQUM3RixJQUFJLEVBQUU7WUFDVCxNQUFNLElBQUlhLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsc0JBQXNCLENBQUM7VUFDakU7O1VBRUE7VUFDQSxJQUFJO1lBQ0YsSUFBSXlDLEdBQUcsQ0FBQ3RJLElBQUksQ0FBQztVQUNmLENBQUMsQ0FBQyxPQUFPdUksQ0FBQyxFQUFFO1lBQ1YsTUFBTSxJQUFJMUgsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGVBQWUsRUFBRSxvQkFBb0IsQ0FBQztVQUMvRDs7VUFFQTtVQUNBLE1BQU1NLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDdUcsV0FBVyxDQUNwQztZQUFFOUMsR0FBRyxFQUFFYTtVQUFPLENBQUMsRUFDZjtZQUNFa0QsS0FBSyxFQUFFO2NBQ0xyRixLQUFLLEVBQUU7Z0JBQ0wyRixHQUFHLEVBQUV4SSxJQUFJO2dCQUNUeUksT0FBTyxFQUFFLElBQUk5QyxJQUFJLENBQUMsQ0FBQztnQkFDbkI1QixPQUFPLEVBQUVELE1BQU0sQ0FBQyxJQUFJLENBQUN6QyxNQUFNO2NBQzdCO1lBQ0YsQ0FBQztZQUNENkYsSUFBSSxFQUFFO2NBQ0puRSxTQUFTLEVBQUUsSUFBSTRDLElBQUksQ0FBQyxDQUFDO2NBQ3JCM0MsU0FBUyxFQUFFYyxNQUFNLENBQUMsSUFBSSxDQUFDekMsTUFBTTtZQUMvQjtVQUNGLENBQ0YsQ0FBQztVQUVELElBQUk4RSxNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsb0JBQW9CLENBQUM7VUFDL0Q7O1VBRUE7VUFDQSxNQUFNZ0MsV0FBVyxHQUFHLE1BQU1uSCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUNwRDlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlCQUF5QixFQUFFMEUsV0FBVyxDQUFDO1VBQ25ELE9BQU9BLFdBQVc7UUFDcEIsQ0FBQyxDQUFDLE9BQU94QixLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQyxvQkFBb0IsRUFBRUEsS0FBSyxDQUFDO1VBQzFDLElBQUlBLEtBQUssWUFBWXhGLE1BQU0sQ0FBQ2dGLEtBQUssRUFBRTtZQUNqQyxNQUFNUSxLQUFLO1VBQ2I7VUFDQSxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsY0FBYyxFQUFFUSxLQUFLLENBQUNDLE9BQU8sSUFBSSw4QkFBOEIsQ0FBQztRQUN6RjtNQUNGLENBQUM7TUFFRCxNQUFNLHdCQUF3Qm9DLENBQUMxRCxNQUFNLEVBQUUyRCxlQUFlLEVBQUU7UUFDdEQsSUFBSTtVQUNGLElBQUksQ0FBQyxJQUFJLENBQUN0SCxNQUFNLEVBQUU7WUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsOENBQThDLENBQUM7VUFDMUY7VUFFQSxNQUFNbkMsSUFBSSxHQUFHLE1BQU1oRCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUM3QyxJQUFJLENBQUN0QixJQUFJLEVBQUU7WUFDVCxNQUFNLElBQUk3QyxNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLGdCQUFnQixDQUFDO1VBQ3ZEOztVQUVBO1VBQ0EsSUFBSSxDQUFDbkMsSUFBSSxDQUFDZCxXQUFXLElBQUksQ0FBQ2MsSUFBSSxDQUFDZCxXQUFXLENBQUMrRixlQUFlLENBQUMsRUFBRTtZQUMzRCxNQUFNLElBQUk5SCxNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLHNCQUFzQixDQUFDO1VBQzdEO1VBRUEsTUFBTWxDLFVBQVUsR0FBR0QsSUFBSSxDQUFDZCxXQUFXLENBQUMrRixlQUFlLENBQUM7O1VBRXBEO1VBQ0EsTUFBTUMsYUFBYSxHQUFHOUUsTUFBTSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sQ0FBQztVQUN6QyxNQUFNd0gsWUFBWSxHQUFHL0UsTUFBTSxDQUFDSCxVQUFVLENBQUNDLFVBQVUsQ0FBQzs7VUFFbEQ7VUFDQSxJQUFJZ0YsYUFBYSxLQUFLQyxZQUFZLEVBQUU7WUFDbEMsTUFBTSxJQUFJaEksTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDBDQUEwQyxDQUFDO1VBQ3RGOztVQUVBO1VBQ0EsTUFBTWlELGtCQUFrQixHQUFHLENBQUMsR0FBR3BGLElBQUksQ0FBQ2QsV0FBVyxDQUFDO1VBQ2hEa0csa0JBQWtCLENBQUNDLE1BQU0sQ0FBQ0osZUFBZSxFQUFFLENBQUMsQ0FBQzs7VUFFN0M7VUFDQSxNQUFNeEMsTUFBTSxHQUFHLE1BQU16RixLQUFLLENBQUN1RyxXQUFXLENBQ3BDO1lBQUU5QyxHQUFHLEVBQUVhO1VBQU8sQ0FBQyxFQUNmO1lBQ0VrQyxJQUFJLEVBQUU7Y0FDSnRFLFdBQVcsRUFBRWtHLGtCQUFrQjtjQUMvQi9GLFNBQVMsRUFBRSxJQUFJNEMsSUFBSSxDQUFDLENBQUM7Y0FDckIzQyxTQUFTLEVBQUU0RjtZQUNiO1VBQ0YsQ0FDRixDQUFDO1VBRUQsSUFBSXpDLE1BQU0sS0FBSyxDQUFDLEVBQUU7WUFDaEIsTUFBTSxJQUFJdEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGVBQWUsRUFBRSw2QkFBNkIsQ0FBQztVQUN4RTs7VUFFQTtVQUNBLE1BQU1nQyxXQUFXLEdBQUcsTUFBTW5ILEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQ3BEOUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUNBQWlDLEVBQUUwRSxXQUFXLENBQUM7VUFDM0QsT0FBT0EsV0FBVztRQUNwQixDQUFDLENBQUMsT0FBT3hCLEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLDRCQUE0QixFQUFFQSxLQUFLLENBQUM7VUFDbEQsSUFBSUEsS0FBSyxZQUFZeEYsTUFBTSxDQUFDZ0YsS0FBSyxFQUFFO1lBQ2pDLE1BQU1RLEtBQUs7VUFDYjtVQUNBLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxjQUFjLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxJQUFJLDhCQUE4QixDQUFDO1FBQ3pGO01BQ0YsQ0FBQztNQUVELE1BQU0sa0JBQWtCMEMsQ0FBQ2hFLE1BQU0sRUFBRWlFLFNBQVMsRUFBRTtRQUMxQyxJQUFJO1VBQ0YsSUFBSSxDQUFDLElBQUksQ0FBQzVILE1BQU0sRUFBRTtZQUNoQixNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSw4Q0FBOEMsQ0FBQztVQUMxRjtVQUVBLE1BQU1uQyxJQUFJLEdBQUcsTUFBTWhELEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQzdDLElBQUksQ0FBQ3RCLElBQUksRUFBRTtZQUNULE1BQU0sSUFBSTdDLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxXQUFXLEVBQUUsZ0JBQWdCLENBQUM7VUFDdkQ7O1VBRUE7VUFDQSxJQUFJLENBQUNuQyxJQUFJLENBQUNiLEtBQUssSUFBSSxDQUFDYSxJQUFJLENBQUNiLEtBQUssQ0FBQ29HLFNBQVMsQ0FBQyxFQUFFO1lBQ3pDLE1BQU0sSUFBSXBJLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxXQUFXLEVBQUUsZ0JBQWdCLENBQUM7VUFDdkQ7VUFFQSxNQUFNN0YsSUFBSSxHQUFHMEQsSUFBSSxDQUFDYixLQUFLLENBQUNvRyxTQUFTLENBQUM7O1VBRWxDO1VBQ0EsTUFBTUwsYUFBYSxHQUFHOUUsTUFBTSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sQ0FBQztVQUN6QyxNQUFNNkgsU0FBUyxHQUFHcEYsTUFBTSxDQUFDOUQsSUFBSSxDQUFDK0QsT0FBTyxDQUFDOztVQUV0QztVQUNBLElBQUk2RSxhQUFhLEtBQUtNLFNBQVMsRUFBRTtZQUMvQixNQUFNLElBQUlySSxNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsb0NBQW9DLENBQUM7VUFDaEY7O1VBRUE7VUFDQSxNQUFNc0QsWUFBWSxHQUFHLENBQUMsR0FBR3pGLElBQUksQ0FBQ2IsS0FBSyxDQUFDO1VBQ3BDc0csWUFBWSxDQUFDSixNQUFNLENBQUNFLFNBQVMsRUFBRSxDQUFDLENBQUM7O1VBRWpDO1VBQ0EsTUFBTTlDLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDdUcsV0FBVyxDQUNwQztZQUFFOUMsR0FBRyxFQUFFYTtVQUFPLENBQUMsRUFDZjtZQUNFa0MsSUFBSSxFQUFFO2NBQ0pyRSxLQUFLLEVBQUVzRyxZQUFZO2NBQ25CcEcsU0FBUyxFQUFFLElBQUk0QyxJQUFJLENBQUMsQ0FBQztjQUNyQjNDLFNBQVMsRUFBRTRGO1lBQ2I7VUFDRixDQUNGLENBQUM7VUFFRCxJQUFJekMsTUFBTSxLQUFLLENBQUMsRUFBRTtZQUNoQixNQUFNLElBQUl0RixNQUFNLENBQUNnRixLQUFLLENBQUMsZUFBZSxFQUFFLHVCQUF1QixDQUFDO1VBQ2xFOztVQUVBO1VBQ0EsTUFBTWdDLFdBQVcsR0FBRyxNQUFNbkgsS0FBSyxDQUFDZSxZQUFZLENBQUN1RCxNQUFNLENBQUM7VUFDcEQ5QixPQUFPLENBQUNDLEdBQUcsQ0FBQywyQkFBMkIsRUFBRTBFLFdBQVcsQ0FBQztVQUNyRCxPQUFPQSxXQUFXO1FBQ3BCLENBQUMsQ0FBQyxPQUFPeEIsS0FBSyxFQUFFO1VBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsc0JBQXNCLEVBQUVBLEtBQUssQ0FBQztVQUM1QyxJQUFJQSxLQUFLLFlBQVl4RixNQUFNLENBQUNnRixLQUFLLEVBQUU7WUFDakMsTUFBTVEsS0FBSztVQUNiO1VBQ0EsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGNBQWMsRUFBRVEsS0FBSyxDQUFDQyxPQUFPLElBQUksOEJBQThCLENBQUM7UUFDekY7TUFDRixDQUFDO01BRUQsTUFBTSxlQUFlOEMsQ0FBQ3BFLE1BQU0sRUFBRTtRQUM1QmxFLEtBQUssQ0FBQ2tFLE1BQU0sRUFBRWxCLE1BQU0sQ0FBQztRQUVyQixJQUFJLENBQUMsSUFBSSxDQUFDekMsTUFBTSxFQUFFO1VBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixDQUFDO1FBQzNDO1FBRUEsTUFBTW5DLElBQUksR0FBRyxNQUFNaEQsS0FBSyxDQUFDZSxZQUFZLENBQUN1RCxNQUFNLENBQUM7UUFDN0MsSUFBSSxDQUFDdEIsSUFBSSxFQUFFO1VBQ1QsTUFBTSxJQUFJN0MsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLGdCQUFnQixDQUFDO1FBQzVEO1FBRUEsT0FBT25DLElBQUk7TUFDYjtJQUNGLENBQUMsQ0FBQztJQUFDdEQsc0JBQUE7RUFBQSxTQUFBQyxXQUFBO0lBQUEsT0FBQUQsc0JBQUEsQ0FBQUMsV0FBQTtFQUFBO0VBQUFELHNCQUFBO0FBQUE7RUFBQUUsSUFBQTtFQUFBQyxLQUFBO0FBQUEsRzs7Ozs7Ozs7Ozs7Ozs7SUM5eEJILElBQUlDLGFBQWE7SUFBQ1osTUFBTSxDQUFDSSxJQUFJLENBQUMsc0NBQXNDLEVBQUM7TUFBQ1MsT0FBT0EsQ0FBQ1IsQ0FBQyxFQUFDO1FBQUNPLGFBQWEsR0FBQ1AsQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFyRyxJQUFJWSxNQUFNO0lBQUNqQixNQUFNLENBQUNJLElBQUksQ0FBQyxlQUFlLEVBQUM7TUFBQ2EsTUFBTUEsQ0FBQ1osQ0FBQyxFQUFDO1FBQUNZLE1BQU0sR0FBQ1osQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlILGVBQWU7SUFBQ0YsTUFBTSxDQUFDSSxJQUFJLENBQUMsb0JBQW9CLEVBQUM7TUFBQ0YsZUFBZUEsQ0FBQ0csQ0FBQyxFQUFDO1FBQUNILGVBQWUsR0FBQ0csQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlvSixRQUFRO0lBQUN6SixNQUFNLENBQUNJLElBQUksQ0FBQyxzQkFBc0IsRUFBQztNQUFDcUosUUFBUUEsQ0FBQ3BKLENBQUMsRUFBQztRQUFDb0osUUFBUSxHQUFDcEosQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlxSixLQUFLO0lBQUMxSixNQUFNLENBQUNJLElBQUksQ0FBQyxjQUFjLEVBQUM7TUFBQ3NKLEtBQUtBLENBQUNySixDQUFDLEVBQUM7UUFBQ3FKLEtBQUssR0FBQ3JKLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJUyxLQUFLO0lBQUNkLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLG9CQUFvQixFQUFDO01BQUNVLEtBQUtBLENBQUNULENBQUMsRUFBQztRQUFDUyxLQUFLLEdBQUNULENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJc0osS0FBSztJQUFDM0osTUFBTSxDQUFDSSxJQUFJLENBQUMsdUJBQXVCLEVBQUM7TUFBQ3VKLEtBQUtBLENBQUN0SixDQUFDLEVBQUM7UUFBQ3NKLEtBQUssR0FBQ3RKLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJYSxLQUFLO0lBQUNsQixNQUFNLENBQUNJLElBQUksQ0FBQyxjQUFjLEVBQUM7TUFBQ2MsS0FBS0EsQ0FBQ2IsQ0FBQyxFQUFDO1FBQUNhLEtBQUssR0FBQ2IsQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUl1SixNQUFNO0lBQUM1SixNQUFNLENBQUNJLElBQUksQ0FBQyxRQUFRLEVBQUM7TUFBQ1MsT0FBT0EsQ0FBQ1IsQ0FBQyxFQUFDO1FBQUN1SixNQUFNLEdBQUN2SixDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSUMsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUMsTUFBTUEsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUM7SUFTbG1CLGVBQWV1SixVQUFVQSxDQUFBQyxJQUFBLEVBQWlCO01BQUEsSUFBaEI7UUFBRXpILEtBQUs7UUFBRXVHO01BQUksQ0FBQyxHQUFBa0IsSUFBQTtNQUN0QyxNQUFNNUosZUFBZSxDQUFDc0csV0FBVyxDQUFDO1FBQUVuRSxLQUFLO1FBQUV1RyxHQUFHO1FBQUV6RyxTQUFTLEVBQUUsSUFBSTRELElBQUksQ0FBQztNQUFFLENBQUMsQ0FBQztJQUMxRTtJQUVBLE1BQU1nRSxXQUFXLEdBQUcsUUFBUTtJQUU1QjlJLE1BQU0sQ0FBQytJLE9BQU8sQ0FBQyxZQUFZO01BQUEsSUFBQUMscUJBQUE7TUFDekI7TUFDQSxJQUFJO1FBQ0YsTUFBTW5KLEtBQUssQ0FBQ29KLFdBQVcsQ0FBQztVQUFFL0gsU0FBUyxFQUFFO1FBQUUsQ0FBQyxDQUFDO1FBQ3pDLE1BQU1yQixLQUFLLENBQUNvSixXQUFXLENBQUM7VUFBRXZILFVBQVUsRUFBRTtRQUFFLENBQUMsQ0FBQztRQUMxQyxNQUFNN0IsS0FBSyxDQUFDb0osV0FBVyxDQUFDO1VBQUVoSCxTQUFTLEVBQUU7UUFBRSxDQUFDLENBQUM7O1FBRXpDO1FBQ0E7UUFDQSxNQUFNakMsTUFBTSxDQUFDVyxLQUFLLENBQUNzSSxXQUFXLENBQUM7VUFBRW5JLEtBQUssRUFBRTtRQUFFLENBQUMsRUFBRTtVQUFFb0ksVUFBVSxFQUFFO1FBQUssQ0FBQyxDQUFDO01BQ3BFLENBQUMsQ0FBQyxPQUFPMUQsS0FBSyxFQUFFO1FBQ2RuRCxPQUFPLENBQUM4RyxJQUFJLENBQUMsbUNBQW1DLEVBQUUzRCxLQUFLLENBQUNDLE9BQU8sQ0FBQztNQUNsRTs7TUFFQTtNQUNBLElBQUk7UUFDRixNQUFNMkQsUUFBUSxHQUFHLE1BQU1wSixNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUFDLENBQUMsQ0FBQ3FJLFVBQVUsQ0FBQyxDQUFDO1FBQ3ZEaEgsT0FBTyxDQUFDQyxHQUFHLENBQUMsc0JBQXNCLEVBQUU4RyxRQUFRLENBQUMzRixHQUFHLENBQUMvQyxJQUFJO1VBQUEsSUFBQTRJLFlBQUEsRUFBQUMsYUFBQTtVQUFBLE9BQUs7WUFDeERDLEVBQUUsRUFBRTlJLElBQUksQ0FBQzRDLEdBQUc7WUFDWm1HLEtBQUssR0FBQUgsWUFBQSxHQUFFNUksSUFBSSxDQUFDOEMsTUFBTSxjQUFBOEYsWUFBQSx3QkFBQUMsYUFBQSxHQUFYRCxZQUFBLENBQWMsQ0FBQyxDQUFDLGNBQUFDLGFBQUEsdUJBQWhCQSxhQUFBLENBQWtCRyxPQUFPO1lBQ2hDNUksS0FBSyxFQUFFSixJQUFJLENBQUNJLEtBQUs7WUFDakJnRCxPQUFPLEVBQUVwRCxJQUFJLENBQUNvRDtVQUNoQixDQUFDO1FBQUEsQ0FBQyxDQUFDLENBQUM7O1FBRUo7UUFDQSxLQUFLLE1BQU1wRCxJQUFJLElBQUkwSSxRQUFRLEVBQUU7VUFDM0IsTUFBTU8sT0FBTyxHQUFHLENBQUMsQ0FBQztVQUVsQixJQUFJLENBQUNqSixJQUFJLENBQUNJLEtBQUssSUFBSSxDQUFDc0MsS0FBSyxDQUFDd0csT0FBTyxDQUFDbEosSUFBSSxDQUFDSSxLQUFLLENBQUMsRUFBRTtZQUM3QzZJLE9BQU8sQ0FBQzdJLEtBQUssR0FBRyxDQUFDLGFBQWEsQ0FBQztVQUNqQztVQUVBLElBQUksQ0FBQ0osSUFBSSxDQUFDUSxTQUFTLEVBQUU7WUFDbkJ5SSxPQUFPLENBQUN6SSxTQUFTLEdBQUcsSUFBSTRELElBQUksQ0FBQyxDQUFDO1VBQ2hDO1VBRUEsSUFBSStFLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDSCxPQUFPLENBQUMsQ0FBQ2xILE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFBQSxJQUFBc0gsYUFBQSxFQUFBQyxjQUFBO1lBQ25DM0gsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkNBQTJDLEdBQUF5SCxhQUFBLEdBQUVySixJQUFJLENBQUM4QyxNQUFNLGNBQUF1RyxhQUFBLHdCQUFBQyxjQUFBLEdBQVhELGFBQUEsQ0FBYyxDQUFDLENBQUMsY0FBQUMsY0FBQSx1QkFBaEJBLGNBQUEsQ0FBa0JOLE9BQU8sQ0FBQztZQUNuRixNQUFNMUosTUFBTSxDQUFDVyxLQUFLLENBQUN5RixXQUFXLENBQUMxRixJQUFJLENBQUM0QyxHQUFHLEVBQUU7Y0FDdkMrQyxJQUFJLEVBQUVzRDtZQUNSLENBQUMsQ0FBQztVQUNKO1FBQ0Y7UUFFQSxNQUFNTSxnQkFBZ0IsR0FBRyxNQUFNakssTUFBTSxDQUFDVyxLQUFLLENBQUNLLElBQUksQ0FBQztVQUFFLE9BQU8sRUFBRTtRQUFjLENBQUMsQ0FBQyxDQUFDa0osVUFBVSxDQUFDLENBQUM7UUFDekY3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQywrQkFBK0IsRUFBRTJILGdCQUFnQixDQUFDOztRQUU5RDtRQUNBLElBQUlBLGdCQUFnQixLQUFLLENBQUMsRUFBRTtVQUMxQjVILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNDQUFzQyxDQUFDO1VBQ25ELElBQUk7WUFDRjtZQUNBLE1BQU02SCxXQUFXLEdBQUcsQ0FDbEI7Y0FDRVYsS0FBSyxFQUFFLG1CQUFtQjtjQUMxQlcsUUFBUSxFQUFFLGNBQWM7Y0FDeEJyRyxTQUFTLEVBQUUsTUFBTTtjQUNqQkMsUUFBUSxFQUFFO1lBQ1osQ0FBQyxFQUNEO2NBQ0V5RixLQUFLLEVBQUUsbUJBQW1CO2NBQzFCVyxRQUFRLEVBQUUsY0FBYztjQUN4QnJHLFNBQVMsRUFBRSxNQUFNO2NBQ2pCQyxRQUFRLEVBQUU7WUFDWixDQUFDLENBQ0Y7WUFFRCxLQUFLLE1BQU1xRyxNQUFNLElBQUlGLFdBQVcsRUFBRTtjQUNoQyxNQUFNM0osTUFBTSxHQUFHLE1BQU1nSSxRQUFRLENBQUM4QixlQUFlLENBQUM7Z0JBQzVDYixLQUFLLEVBQUVZLE1BQU0sQ0FBQ1osS0FBSztnQkFDbkJXLFFBQVEsRUFBRUMsTUFBTSxDQUFDRCxRQUFRO2dCQUN6QkcsSUFBSSxFQUFFLGFBQWE7Z0JBQ25CckosU0FBUyxFQUFFLElBQUk0RCxJQUFJLENBQUMsQ0FBQztnQkFDckJoQixPQUFPLEVBQUU7a0JBQ1BDLFNBQVMsRUFBRXNHLE1BQU0sQ0FBQ3RHLFNBQVM7a0JBQzNCQyxRQUFRLEVBQUVxRyxNQUFNLENBQUNyRyxRQUFRO2tCQUN6QnVHLElBQUksRUFBRSxhQUFhO2tCQUNuQkMsUUFBUSxLQUFBM0csTUFBQSxDQUFLd0csTUFBTSxDQUFDdEcsU0FBUyxPQUFBRixNQUFBLENBQUl3RyxNQUFNLENBQUNyRyxRQUFRO2dCQUNsRDtjQUNGLENBQUMsQ0FBQzs7Y0FFRjtjQUNBLE1BQU1oRSxNQUFNLENBQUNXLEtBQUssQ0FBQ3lGLFdBQVcsQ0FBQzVGLE1BQU0sRUFBRTtnQkFDckM2RixJQUFJLEVBQUU7a0JBQUV2RixLQUFLLEVBQUUsQ0FBQyxhQUFhO2dCQUFFO2NBQ2pDLENBQUMsQ0FBQztjQUVGdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMscUNBQXFDLEVBQUU7Z0JBQ2pEa0gsRUFBRSxFQUFFaEosTUFBTTtnQkFDVmlKLEtBQUssRUFBRVksTUFBTSxDQUFDWixLQUFLO2dCQUNuQnRKLElBQUksS0FBQTBELE1BQUEsQ0FBS3dHLE1BQU0sQ0FBQ3RHLFNBQVMsT0FBQUYsTUFBQSxDQUFJd0csTUFBTSxDQUFDckcsUUFBUTtjQUM5QyxDQUFDLENBQUM7WUFDSjtVQUNGLENBQUMsQ0FBQyxPQUFPd0IsS0FBSyxFQUFFO1lBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsNkNBQTZDLEVBQUVBLEtBQUssQ0FBQztVQUNyRTtRQUNGO01BQ0YsQ0FBQyxDQUFDLE9BQU9BLEtBQUssRUFBRTtRQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHdDQUF3QyxFQUFFQSxLQUFLLENBQUM7TUFDaEU7O01BRUE7TUFDQSxNQUFNaUYsYUFBYSxJQUFBekIscUJBQUEsR0FBR2hKLE1BQU0sQ0FBQzBLLFFBQVEsQ0FBQ0MsT0FBTyxjQUFBM0IscUJBQUEsdUJBQXZCQSxxQkFBQSxDQUF5QlMsS0FBSzs7TUFFcEQ7TUFDQSxJQUFJZ0IsYUFBYSxhQUFiQSxhQUFhLGVBQWJBLGFBQWEsQ0FBRUcsUUFBUSxJQUFJSCxhQUFhLGFBQWJBLGFBQWEsZUFBYkEsYUFBYSxDQUFFTCxRQUFRLEVBQUU7UUFDdERTLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxRQUFRLGFBQUFsSCxNQUFBLENBQWFtSCxrQkFBa0IsQ0FBQ1AsYUFBYSxDQUFDRyxRQUFRLENBQUMsT0FBQS9HLE1BQUEsQ0FBSW1ILGtCQUFrQixDQUFDUCxhQUFhLENBQUNMLFFBQVEsQ0FBQyxPQUFBdkcsTUFBQSxDQUFJNEcsYUFBYSxDQUFDUSxNQUFNLE9BQUFwSCxNQUFBLENBQUk0RyxhQUFhLENBQUNTLElBQUksQ0FBRTs7UUFFeks7UUFDQSxJQUFJO1VBQ0Y3SSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxnQ0FBZ0MsQ0FBQztVQUM3Q21HLEtBQUssQ0FBQzBDLElBQUksQ0FBQztZQUNUQyxFQUFFLEVBQUVYLGFBQWEsQ0FBQ0csUUFBUTtZQUMxQnZILElBQUksRUFBRW9ILGFBQWEsQ0FBQ0csUUFBUTtZQUM1QlMsT0FBTyxFQUFFLFlBQVk7WUFDckJsRyxJQUFJLEVBQUU7VUFDUixDQUFDLENBQUM7VUFDRjlDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLCtCQUErQixDQUFDO1FBQzlDLENBQUMsQ0FBQyxPQUFPa0QsS0FBSyxFQUFFO1VBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsMkJBQTJCLEVBQUVBLEtBQUssQ0FBQztRQUNuRDtNQUNGLENBQUMsTUFBTTtRQUNMbkQsT0FBTyxDQUFDOEcsSUFBSSxDQUFDLGlEQUFpRCxDQUFDO01BQ2pFOztNQUVBO01BQ0FYLFFBQVEsQ0FBQzhDLE1BQU0sQ0FBQztRQUNkQyxxQkFBcUIsRUFBRSxJQUFJO1FBQzNCQywyQkFBMkIsRUFBRTtNQUMvQixDQUFDLENBQUM7O01BRUY7TUFDQWhELFFBQVEsQ0FBQ2lELGNBQWMsQ0FBQ0MsUUFBUSxHQUFHLHdCQUF3QjtNQUMzRGxELFFBQVEsQ0FBQ2lELGNBQWMsQ0FBQ3BJLElBQUksR0FBR29ILGFBQWEsYUFBYkEsYUFBYSxlQUFiQSxhQUFhLENBQUVHLFFBQVEsOEJBQUEvRyxNQUFBLENBQ3pCNEcsYUFBYSxDQUFDRyxRQUFRLFNBQ2pELDhDQUE4QztNQUVoRHBDLFFBQVEsQ0FBQ2lELGNBQWMsQ0FBQ0UsV0FBVyxHQUFHO1FBQ3BDTixPQUFPQSxDQUFBLEVBQUc7VUFDUixPQUFPLDJCQUEyQjtRQUNwQyxDQUFDO1FBQ0RsRyxJQUFJQSxDQUFDekUsSUFBSSxFQUFFaUgsR0FBRyxFQUFFO1VBQ2QsTUFBTWlFLFlBQVksR0FBR2xMLElBQUksQ0FBQzhDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQ2tHLE9BQU87VUFDM0MsT0FBTyxnREFBQTdGLE1BQUEsQ0FDOEIrSCxZQUFZLHdDQUFxQyxNQUFBL0gsTUFBQSxDQUMvRThELEdBQUcsU0FBTSw0RUFDNkQsY0FDOUQscUNBQ3VCO1FBQ3hDLENBQUM7UUFDRGtFLElBQUlBLENBQUNuTCxJQUFJLEVBQUVpSCxHQUFHLEVBQUU7VUFDZCxNQUFNaUUsWUFBWSxHQUFHbEwsSUFBSSxDQUFDOEMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDa0csT0FBTztVQUMzQyxzUUFBQTdGLE1BQUEsQ0FLeUMrSCxZQUFZLGdIQUFBL0gsTUFBQSxDQUVsQzhELEdBQUc7UUFleEI7TUFDRixDQUFDOztNQUVEO01BQ0EsSUFBSSxPQUFNMUksZUFBZSxDQUFDK0IsSUFBSSxDQUFDLENBQUMsQ0FBQ2tKLFVBQVUsQ0FBQyxDQUFDLE1BQUssQ0FBQyxFQUFFO1FBQ25ELE1BQU10QixVQUFVLENBQUM7VUFDZnhILEtBQUssRUFBRSxpQkFBaUI7VUFDeEJ1RyxHQUFHLEVBQUU7UUFDUCxDQUFDLENBQUM7UUFFRixNQUFNaUIsVUFBVSxDQUFDO1VBQ2Z4SCxLQUFLLEVBQUUsa0JBQWtCO1VBQ3pCdUcsR0FBRyxFQUFFO1FBQ1AsQ0FBQyxDQUFDO1FBRUYsTUFBTWlCLFVBQVUsQ0FBQztVQUNmeEgsS0FBSyxFQUFFLGVBQWU7VUFDdEJ1RyxHQUFHLEVBQUU7UUFDUCxDQUFDLENBQUM7UUFFRixNQUFNaUIsVUFBVSxDQUFDO1VBQ2Z4SCxLQUFLLEVBQUUsYUFBYTtVQUNwQnVHLEdBQUcsRUFBRTtRQUNQLENBQUMsQ0FBQztNQUNKOztNQUVBO01BQ0E7TUFDQTNILE1BQU0sQ0FBQ00sT0FBTyxDQUFDLE9BQU8sRUFBRSxZQUFZO1FBQ2xDLE9BQU9yQixlQUFlLENBQUMrQixJQUFJLENBQUMsQ0FBQztNQUMvQixDQUFDLENBQUM7O01BRUY7TUFDQXdILFFBQVEsQ0FBQ3NELFlBQVksQ0FBQyxDQUFDQyxPQUFPLEVBQUVyTCxJQUFJLEtBQUs7UUFBQSxJQUFBc0wscUJBQUEsRUFBQUMsc0JBQUE7UUFDdkM1SixPQUFPLENBQUNDLEdBQUcsQ0FBQyw0Q0FBNEMsRUFBRTtVQUN4RG1ILEtBQUssRUFBRXNDLE9BQU8sQ0FBQ3RDLEtBQUs7VUFDcEJjLElBQUksRUFBRXdCLE9BQU8sQ0FBQ3hCLElBQUk7VUFDbEJ6RyxPQUFPLEVBQUVpSSxPQUFPLENBQUNqSSxPQUFPO1VBQ3hCNUMsU0FBUyxFQUFFNkssT0FBTyxDQUFDN0s7UUFDckIsQ0FBQyxDQUFDO1FBRUYsTUFBTWdMLGNBQWMsR0FBQXZNLGFBQUEsS0FBUWUsSUFBSSxDQUFFOztRQUVsQztRQUNBd0wsY0FBYyxDQUFDcEksT0FBTyxHQUFHaUksT0FBTyxDQUFDakksT0FBTyxJQUFJLENBQUMsQ0FBQzs7UUFFOUM7UUFDQSxNQUFNeUcsSUFBSSxHQUFHd0IsT0FBTyxDQUFDeEIsSUFBSSxJQUFJLGFBQWE7UUFDMUMyQixjQUFjLENBQUNwTCxLQUFLLEdBQUcsQ0FBQ3lKLElBQUksQ0FBQzs7UUFFN0I7UUFDQTJCLGNBQWMsQ0FBQ2hMLFNBQVMsR0FBRzZLLE9BQU8sQ0FBQzdLLFNBQVMsSUFBSSxJQUFJNEQsSUFBSSxDQUFDLENBQUM7UUFFMUR6QyxPQUFPLENBQUNDLEdBQUcsQ0FBQyw4QkFBOEIsRUFBRTtVQUMxQ2tILEVBQUUsRUFBRTBDLGNBQWMsQ0FBQzVJLEdBQUc7VUFDdEJtRyxLQUFLLEdBQUF1QyxxQkFBQSxHQUFFRSxjQUFjLENBQUMxSSxNQUFNLGNBQUF3SSxxQkFBQSx3QkFBQUMsc0JBQUEsR0FBckJELHFCQUFBLENBQXdCLENBQUMsQ0FBQyxjQUFBQyxzQkFBQSx1QkFBMUJBLHNCQUFBLENBQTRCdkMsT0FBTztVQUMxQzVJLEtBQUssRUFBRW9MLGNBQWMsQ0FBQ3BMLEtBQUs7VUFDM0JnRCxPQUFPLEVBQUVvSSxjQUFjLENBQUNwSSxPQUFPO1VBQy9CNUMsU0FBUyxFQUFFZ0wsY0FBYyxDQUFDaEw7UUFDNUIsQ0FBQyxDQUFDO1FBRUYsT0FBT2dMLGNBQWM7TUFDdkIsQ0FBQyxDQUFDOztNQUVGO01BQ0FsTSxNQUFNLENBQUNNLE9BQU8sQ0FBQyxhQUFhLEVBQUUsWUFBVztRQUN2QytCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJDQUEyQyxFQUFFLElBQUksQ0FBQzlCLE1BQU0sQ0FBQztRQUVyRSxJQUFJLENBQUMsSUFBSSxDQUFDQSxNQUFNLEVBQUU7VUFDaEI2QixPQUFPLENBQUNDLEdBQUcsQ0FBQywwQ0FBMEMsQ0FBQztVQUN2RCxPQUFPLElBQUksQ0FBQzdCLEtBQUssQ0FBQyxDQUFDO1FBQ3JCO1FBRUEsSUFBSTtVQUNGO1VBQ0EsTUFBTTBMLFdBQVcsR0FBR25NLE1BQU0sQ0FBQ1csS0FBSyxDQUFDSyxJQUFJLENBQ25DO1lBQ0VvQixHQUFHLEVBQUUsQ0FDSDtjQUFFLE9BQU8sRUFBRTtZQUFjLENBQUMsRUFDMUI7Y0FBRSxjQUFjLEVBQUU7WUFBYyxDQUFDO1VBRXJDLENBQUMsRUFDRDtZQUNFakIsTUFBTSxFQUFFO2NBQ05xQyxNQUFNLEVBQUUsQ0FBQztjQUNUMUMsS0FBSyxFQUFFLENBQUM7Y0FDUixtQkFBbUIsRUFBRSxDQUFDO2NBQ3RCLGtCQUFrQixFQUFFLENBQUM7Y0FDckIsa0JBQWtCLEVBQUUsQ0FBQztjQUNyQkksU0FBUyxFQUFFO1lBQ2I7VUFDRixDQUNGLENBQUM7VUFFRG1CLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHVDQUF1QyxDQUFDO1VBQ3BELE9BQU82SixXQUFXO1FBQ3BCLENBQUMsQ0FBQyxPQUFPM0csS0FBSyxFQUFFO1VBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMscUNBQXFDLEVBQUVBLEtBQUssQ0FBQztVQUMzRCxPQUFPLElBQUksQ0FBQy9FLEtBQUssQ0FBQyxDQUFDO1FBQ3JCO01BQ0YsQ0FBQyxDQUFDOztNQUVGO01BQ0FULE1BQU0sQ0FBQ00sT0FBTyxDQUFDLFVBQVUsRUFBRSxZQUFXO1FBQ3BDLElBQUksQ0FBQyxJQUFJLENBQUNFLE1BQU0sRUFBRTtVQUNoQixPQUFPLElBQUksQ0FBQ0MsS0FBSyxDQUFDLENBQUM7UUFDckI7UUFFQTRCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNDQUFzQyxFQUFFLElBQUksQ0FBQzlCLE1BQU0sQ0FBQztRQUVoRSxPQUFPUixNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUN0QjtVQUFFc0MsR0FBRyxFQUFFLElBQUksQ0FBQzlDO1FBQU8sQ0FBQyxFQUNwQjtVQUNFVyxNQUFNLEVBQUU7WUFDTkwsS0FBSyxFQUFFLENBQUM7WUFDUjBDLE1BQU0sRUFBRSxDQUFDO1lBQ1RNLE9BQU8sRUFBRTtVQUNYO1FBQ0YsQ0FDRixDQUFDO01BQ0gsQ0FBQyxDQUFDO0lBQ0osQ0FBQyxDQUFDOztJQUVGO0lBQ0E5RCxNQUFNLENBQUM0RSxPQUFPLENBQUM7TUFDYixjQUFjd0gsQ0FBQUMsS0FBQSxFQUE2RDtRQUFBLElBQTVEO1VBQUU1QyxLQUFLO1VBQUVXLFFBQVE7VUFBRUcsSUFBSTtVQUFFK0IsVUFBVTtVQUFFdkksU0FBUztVQUFFQztRQUFTLENBQUMsR0FBQXFJLEtBQUE7UUFDdkU7UUFDQSxJQUFJOUIsSUFBSSxLQUFLLE9BQU8sSUFBSStCLFVBQVUsS0FBS3hELFdBQVcsRUFBRTtVQUNsRCxNQUFNLElBQUk5SSxNQUFNLENBQUNnRixLQUFLLENBQUMscUJBQXFCLEVBQUUsOEJBQThCLENBQUM7UUFDL0U7O1FBRUE7UUFDQSxNQUFNdUgsYUFBYSxHQUFHO1VBQ3BCOUosTUFBTSxFQUFFLE9BQU87VUFDZitKLFNBQVMsRUFBRSxPQUFPO1VBQ2xCQyxNQUFNLEVBQUUsT0FBTztVQUNmQyxPQUFPLEVBQUU7UUFDWCxDQUFDO1FBRUQsTUFBTUMsY0FBYyxHQUFHLEVBQUU7UUFDekIsSUFBSSxDQUFDSixhQUFhLENBQUM5SixNQUFNLENBQUNtSyxJQUFJLENBQUN4QyxRQUFRLENBQUMsRUFBRTtVQUN4Q3VDLGNBQWMsQ0FBQ0UsSUFBSSxDQUFDLDZDQUE2QyxDQUFDO1FBQ3BFO1FBQ0EsSUFBSSxDQUFDTixhQUFhLENBQUNDLFNBQVMsQ0FBQ0ksSUFBSSxDQUFDeEMsUUFBUSxDQUFDLEVBQUU7VUFDM0N1QyxjQUFjLENBQUNFLElBQUksQ0FBQyxxREFBcUQsQ0FBQztRQUM1RTtRQUNBLElBQUksQ0FBQ04sYUFBYSxDQUFDRSxNQUFNLENBQUNHLElBQUksQ0FBQ3hDLFFBQVEsQ0FBQyxFQUFFO1VBQ3hDdUMsY0FBYyxDQUFDRSxJQUFJLENBQUMsMkNBQTJDLENBQUM7UUFDbEU7UUFDQSxJQUFJLENBQUNOLGFBQWEsQ0FBQ0csT0FBTyxDQUFDRSxJQUFJLENBQUN4QyxRQUFRLENBQUMsRUFBRTtVQUN6Q3VDLGNBQWMsQ0FBQ0UsSUFBSSxDQUFDLGlFQUFpRSxDQUFDO1FBQ3hGO1FBRUEsSUFBSUYsY0FBYyxDQUFDbEssTUFBTSxHQUFHLENBQUMsRUFBRTtVQUM3QixNQUFNLElBQUl6QyxNQUFNLENBQUNnRixLQUFLLENBQUMsa0JBQWtCLEVBQUUySCxjQUFjLENBQUNHLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN2RTs7UUFFQTtRQUNBLElBQUk7VUFDRixNQUFNdE0sTUFBTSxHQUFHZ0ksUUFBUSxDQUFDdUUsVUFBVSxDQUFDO1lBQ2pDdEQsS0FBSztZQUNMVyxRQUFRO1lBQ1JHLElBQUk7WUFBRTtZQUNOekcsT0FBTyxFQUFFO2NBQ1B5RyxJQUFJO2NBQUU7Y0FDTnhHLFNBQVM7Y0FDVEMsUUFBUTtjQUNSd0csUUFBUSxLQUFBM0csTUFBQSxDQUFLRSxTQUFTLE9BQUFGLE1BQUEsQ0FBSUcsUUFBUTtZQUNwQztVQUNGLENBQUMsQ0FBQzs7VUFFRjtVQUNBLElBQUl4RCxNQUFNLEVBQUU7WUFDVmdJLFFBQVEsQ0FBQytDLHFCQUFxQixDQUFDL0ssTUFBTSxDQUFDO1VBQ3hDO1VBRUEsT0FBT0EsTUFBTTtRQUNmLENBQUMsQ0FBQyxPQUFPZ0YsS0FBSyxFQUFFO1VBQ2QsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLG9CQUFvQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUM3RDtNQUNGLENBQUM7TUFFRCxNQUFNLGVBQWV1SCxDQUFBLEVBQUc7UUFDdEIsSUFBSSxDQUFDLElBQUksQ0FBQ3hNLE1BQU0sRUFBRTtVQUNoQixNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx3QkFBd0IsQ0FBQztRQUNwRTtRQUVBLElBQUk7VUFBQSxJQUFBekUsV0FBQSxFQUFBME0sYUFBQTtVQUNGLE1BQU12TSxJQUFJLEdBQUcsTUFBTVYsTUFBTSxDQUFDVyxLQUFLLENBQUNDLFlBQVksQ0FBQyxJQUFJLENBQUNKLE1BQU0sQ0FBQztVQUN6RCxJQUFJLENBQUNFLElBQUksRUFBRTtZQUNULE1BQU0sSUFBSVYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLGdCQUFnQixDQUFDO1VBQzVEOztVQUVBO1VBQ0EsTUFBTXVGLElBQUksR0FBRyxFQUFBaEssV0FBQSxHQUFBRyxJQUFJLENBQUNJLEtBQUssY0FBQVAsV0FBQSx1QkFBVkEsV0FBQSxDQUFhLENBQUMsQ0FBQyxPQUFBME0sYUFBQSxHQUFJdk0sSUFBSSxDQUFDb0QsT0FBTyxjQUFBbUosYUFBQSx1QkFBWkEsYUFBQSxDQUFjMUMsSUFBSSxLQUFJLGFBQWE7VUFDbkUsT0FBT0EsSUFBSTtRQUNiLENBQUMsQ0FBQyxPQUFPL0UsS0FBSyxFQUFFO1VBQ2QsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUMxRDtNQUNGLENBQUM7TUFFRCwrQkFBK0J5SCxDQUFDekQsS0FBSyxFQUFFO1FBQ3JDO1FBQ0EsTUFBTS9JLElBQUksR0FBRzhILFFBQVEsQ0FBQzJFLGVBQWUsQ0FBQzFELEtBQUssQ0FBQztRQUM1QyxJQUFJLENBQUMvSSxJQUFJLEVBQUU7VUFDVCxNQUFNLElBQUlWLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx1Q0FBdUMsQ0FBQztRQUNuRjs7UUFFQTtRQUNBLE1BQU1vSSxTQUFTLEdBQUcxTSxJQUFJLENBQUM4QyxNQUFNLENBQUMsQ0FBQyxDQUFDO1FBQ2hDLElBQUk0SixTQUFTLENBQUNDLFFBQVEsRUFBRTtVQUN0QixNQUFNLElBQUlyTixNQUFNLENBQUNnRixLQUFLLENBQUMsa0JBQWtCLEVBQUUsZ0NBQWdDLENBQUM7UUFDOUU7O1FBRUE7UUFDQSxJQUFJO1VBQ0Z3RCxRQUFRLENBQUMrQyxxQkFBcUIsQ0FBQzdLLElBQUksQ0FBQzRDLEdBQUcsRUFBRW1HLEtBQUssQ0FBQztVQUMvQyxPQUFPLElBQUk7UUFDYixDQUFDLENBQUMsT0FBT2pFLEtBQUssRUFBRTtVQUNkLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQywyQkFBMkIsRUFBRVEsS0FBSyxDQUFDQyxPQUFPLENBQUM7UUFDcEU7TUFDRixDQUFDO01BRUQsTUFBTSxzQkFBc0I2SCxDQUFDbkcsSUFBSSxFQUFFO1FBQ2pDLElBQUk7VUFDRjlFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJDQUEyQyxFQUFFaUwsSUFBSSxDQUFDQyxTQUFTLENBQUNyRyxJQUFJLENBQUMsQ0FBQztVQUU5RWxILEtBQUssQ0FBQ2tILElBQUksRUFBRTtZQUNWc0MsS0FBSyxFQUFFeEcsTUFBTTtZQUNid0ssV0FBVyxFQUFFeEs7VUFDZixDQUFDLENBQUM7VUFFRixNQUFNO1lBQUV3RyxLQUFLO1lBQUVnRTtVQUFZLENBQUMsR0FBR3RHLElBQUk7VUFDbkM5RSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxnREFBZ0QsRUFBRW1ILEtBQUssQ0FBQzs7VUFFcEU7VUFDQSxJQUFJL0ksSUFBSTtVQUNSLElBQUk7WUFDRkEsSUFBSSxHQUFHOEgsUUFBUSxDQUFDMkUsZUFBZSxDQUFDMUQsS0FBSyxDQUFDO1lBQ3RDcEgsT0FBTyxDQUFDQyxHQUFHLENBQUMsc0NBQXNDLEVBQUU1QixJQUFJLEdBQUcsT0FBTyxHQUFHLFdBQVcsQ0FBQztVQUNuRixDQUFDLENBQUMsT0FBT2dOLFNBQVMsRUFBRTtZQUNsQnJMLE9BQU8sQ0FBQ21ELEtBQUssQ0FBQyxzQ0FBc0MsRUFBRWtJLFNBQVMsQ0FBQztZQUNoRSxNQUFNLElBQUkxTixNQUFNLENBQUNnRixLQUFLLENBQUMsb0JBQW9CLEVBQUUsdUJBQXVCLENBQUM7VUFDdkU7VUFFQSxJQUFJLENBQUN0RSxJQUFJLEVBQUU7WUFDVCxNQUFNLElBQUlWLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx1Q0FBdUMsQ0FBQztVQUNuRjtVQUVBM0MsT0FBTyxDQUFDQyxHQUFHLENBQUMsOEJBQThCLEVBQUU1QixJQUFJLENBQUM0QyxHQUFHLENBQUM7O1VBRXZEO1VBQ0EsTUFBTWlKLGFBQWEsR0FBRztZQUNwQjlKLE1BQU0sRUFBRSxPQUFPO1lBQ2YrSixTQUFTLEVBQUUsT0FBTztZQUNsQkMsTUFBTSxFQUFFLE9BQU87WUFDZkMsT0FBTyxFQUFFO1VBQ1gsQ0FBQztVQUVELE1BQU1DLGNBQWMsR0FBRyxFQUFFO1VBQ3pCLElBQUksQ0FBQ0osYUFBYSxDQUFDOUosTUFBTSxDQUFDbUssSUFBSSxDQUFDYSxXQUFXLENBQUMsRUFBRTtZQUMzQ2QsY0FBYyxDQUFDRSxJQUFJLENBQUMsNkNBQTZDLENBQUM7VUFDcEU7VUFDQSxJQUFJLENBQUNOLGFBQWEsQ0FBQ0MsU0FBUyxDQUFDSSxJQUFJLENBQUNhLFdBQVcsQ0FBQyxFQUFFO1lBQzlDZCxjQUFjLENBQUNFLElBQUksQ0FBQyxxREFBcUQsQ0FBQztVQUM1RTtVQUNBLElBQUksQ0FBQ04sYUFBYSxDQUFDRSxNQUFNLENBQUNHLElBQUksQ0FBQ2EsV0FBVyxDQUFDLEVBQUU7WUFDM0NkLGNBQWMsQ0FBQ0UsSUFBSSxDQUFDLDJDQUEyQyxDQUFDO1VBQ2xFO1VBQ0EsSUFBSSxDQUFDTixhQUFhLENBQUNHLE9BQU8sQ0FBQ0UsSUFBSSxDQUFDYSxXQUFXLENBQUMsRUFBRTtZQUM1Q2QsY0FBYyxDQUFDRSxJQUFJLENBQUMsaUVBQWlFLENBQUM7VUFDeEY7VUFFQSxJQUFJRixjQUFjLENBQUNsSyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQzdCLE1BQU0sSUFBSXpDLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxrQkFBa0IsRUFBRTJILGNBQWMsQ0FBQ0csSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1VBQ3ZFOztVQUVFO1VBQ0F6SyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxzREFBc0QsQ0FBQzs7VUFFbkU7VUFDQSxNQUFNcUwsU0FBUyxXQUFBOUosTUFBQSxDQUFXaUIsSUFBSSxDQUFDOEksR0FBRyxDQUFDLENBQUMsT0FBQS9KLE1BQUEsQ0FBSW9DLElBQUksQ0FBQzRILE1BQU0sQ0FBQyxDQUFDLENBQUNDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsZ0JBQWE7VUFDNUYxTCxPQUFPLENBQUNDLEdBQUcsQ0FBQyxzREFBc0QsRUFBRXFMLFNBQVMsQ0FBQztVQUU5RSxJQUFJSyxVQUFVO1VBQ2QsSUFBSTtZQUNGQSxVQUFVLEdBQUd4RixRQUFRLENBQUN1RSxVQUFVLENBQUM7Y0FDL0J0RCxLQUFLLEVBQUVrRSxTQUFTO2NBQ2hCdkQsUUFBUSxFQUFFcUQ7WUFDWixDQUFDLENBQUM7WUFDRnBMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtEQUFrRCxFQUFFMEwsVUFBVSxDQUFDO1VBQzdFLENBQUMsQ0FBQyxPQUFPQyxXQUFXLEVBQUU7WUFDcEI1TCxPQUFPLENBQUNtRCxLQUFLLENBQUMsaURBQWlELEVBQUV5SSxXQUFXLENBQUM7WUFDN0UsTUFBTSxJQUFJak8sTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLDJCQUEyQixFQUFFLGlDQUFpQyxDQUFDO1VBQ3hGOztVQUVBO1VBQ0EsSUFBSWtKLFFBQVEsRUFBRUMsWUFBWTtVQUMxQixJQUFJO1lBQ0ZELFFBQVEsR0FBRyxNQUFNbE8sTUFBTSxDQUFDVyxLQUFLLENBQUNDLFlBQVksQ0FBQ29OLFVBQVUsQ0FBQztZQUN0RDNMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDRDQUE0QyxFQUFFaUwsSUFBSSxDQUFDQyxTQUFTLENBQUNVLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFFNUYsSUFBSSxDQUFDQSxRQUFRLEVBQUU7Y0FDYixNQUFNLElBQUlsSixLQUFLLENBQUMseUNBQXlDLENBQUM7WUFDNUQ7WUFFQSxJQUFJLENBQUNrSixRQUFRLENBQUNFLFFBQVEsRUFBRTtjQUN0QixNQUFNLElBQUlwSixLQUFLLENBQUMsdUNBQXVDLENBQUM7WUFDMUQ7WUFFQSxJQUFJLENBQUNrSixRQUFRLENBQUNFLFFBQVEsQ0FBQ2hFLFFBQVEsRUFBRTtjQUMvQixNQUFNLElBQUlwRixLQUFLLENBQUMsd0NBQXdDLENBQUM7WUFDM0Q7WUFFQW1KLFlBQVksR0FBR0QsUUFBUSxDQUFDRSxRQUFRLENBQUNoRSxRQUFRO1lBQ3pDL0gsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkNBQTJDLEVBQUVpTCxJQUFJLENBQUNDLFNBQVMsQ0FBQ1csWUFBWSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQztZQUMvRjlMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDhEQUE4RCxDQUFDO1VBQzdFLENBQUMsQ0FBQyxPQUFPK0wsU0FBUyxFQUFFO1lBQ2xCaE0sT0FBTyxDQUFDbUQsS0FBSyxDQUFDLGtEQUFrRCxFQUFFNkksU0FBUyxDQUFDO1lBQzVFaE0sT0FBTyxDQUFDQyxHQUFHLENBQUMsMERBQTBELENBQUM7O1lBRXZFO1lBQ0EsSUFBSTBMLFVBQVUsRUFBRTtjQUNkLElBQUk7Z0JBQUUsTUFBTWhPLE1BQU0sQ0FBQ1csS0FBSyxDQUFDNEYsV0FBVyxDQUFDeUgsVUFBVSxDQUFDO2NBQUUsQ0FBQyxDQUFDLE9BQU90RyxDQUFDLEVBQUUsQ0FBQztZQUNqRTs7WUFFQTtZQUNBLElBQUk7Y0FDRixNQUFNNEcsVUFBVSxHQUFHLEVBQUU7Y0FDckIsTUFBTUMsY0FBYyxHQUFHNUYsTUFBTSxDQUFDNkYsUUFBUSxDQUFDZixXQUFXLEVBQUVhLFVBQVUsQ0FBQztjQUMvREgsWUFBWSxHQUFHO2dCQUNieEYsTUFBTSxFQUFFNEY7Y0FDVixDQUFDO2NBQ0RsTSxPQUFPLENBQUNDLEdBQUcsQ0FBQywrQ0FBK0MsQ0FBQztZQUM5RCxDQUFDLENBQUMsT0FBT21NLFdBQVcsRUFBRTtjQUNwQnBNLE9BQU8sQ0FBQ21ELEtBQUssQ0FBQywrQ0FBK0MsRUFBRWlKLFdBQVcsQ0FBQztjQUMzRSxNQUFNLElBQUl6TyxNQUFNLENBQUNnRixLQUFLLENBQUMsK0JBQStCLEVBQUUsZ0NBQWdDLENBQUM7WUFDM0Y7VUFDRjs7VUFFQTtVQUNBM0MsT0FBTyxDQUFDQyxHQUFHLENBQUMsd0RBQXdELENBQUM7VUFDckVELE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtDQUFrQyxFQUFFNUIsSUFBSSxDQUFDNEMsR0FBRyxDQUFDO1VBQ3pEakIsT0FBTyxDQUFDQyxHQUFHLENBQUMsdUNBQXVDLEVBQUUsT0FBTzVCLElBQUksQ0FBQzRDLEdBQUcsQ0FBQztVQUNyRWpCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHdDQUF3QyxFQUFFaUwsSUFBSSxDQUFDQyxTQUFTLENBQUNXLFlBQVksRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7VUFFNUYsSUFBSXBILFlBQVksR0FBRyxDQUFDO1VBQ3BCLElBQUkySCxXQUFXLEdBQUcsSUFBSTs7VUFFdEI7VUFDQSxNQUFNQyxhQUFhLEdBQUc7VUFDcEI7VUFDQSxNQUFNO1lBQ0p0TSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxtREFBbUQsQ0FBQztZQUNoRSxPQUFPdEMsTUFBTSxDQUFDVyxLQUFLLENBQUM4RixNQUFNLENBQUMvRixJQUFJLENBQUM0QyxHQUFHLEVBQUU7Y0FDbkMrQyxJQUFJLEVBQUU7Z0JBQ0osbUJBQW1CLEVBQUU4SDtjQUN2QjtZQUNGLENBQUMsQ0FBQztVQUNKLENBQUM7VUFDRDtVQUNBLE1BQU07WUFDSjlMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDREQUE0RCxDQUFDO1lBQ3pFLE9BQU90QyxNQUFNLENBQUNXLEtBQUssQ0FBQzhGLE1BQU0sQ0FDeEI7Y0FBRW5ELEdBQUcsRUFBRTVDLElBQUksQ0FBQzRDO1lBQUksQ0FBQyxFQUNqQjtjQUNFK0MsSUFBSSxFQUFFO2dCQUNKLG1CQUFtQixFQUFFOEg7Y0FDdkI7WUFDRixDQUNGLENBQUM7VUFDSCxDQUFDO1VBQ0Q7VUFDQSxNQUFNO1lBQ0o5TCxPQUFPLENBQUNDLEdBQUcsQ0FBQyx5REFBeUQsQ0FBQztZQUN0RSxPQUFPdEMsTUFBTSxDQUFDVyxLQUFLLENBQUM4RixNQUFNLENBQUMvRixJQUFJLENBQUM0QyxHQUFHLENBQUN3SyxRQUFRLENBQUMsQ0FBQyxFQUFFO2NBQzlDekgsSUFBSSxFQUFFO2dCQUNKLG1CQUFtQixFQUFFOEg7Y0FDdkI7WUFDRixDQUFDLENBQUM7VUFDSixDQUFDO1VBQ0Q7VUFDQSxNQUFNO1lBQ0o5TCxPQUFPLENBQUNDLEdBQUcsQ0FBQyxtREFBbUQsQ0FBQztZQUNoRSxPQUFPdEMsTUFBTSxDQUFDVyxLQUFLLENBQUM4RixNQUFNLENBQ3hCO2NBQUVuRCxHQUFHLEVBQUU1QyxJQUFJLENBQUM0QztZQUFJLENBQUMsRUFDakI7Y0FDRStDLElBQUksRUFBRTtnQkFDSixtQkFBbUIsRUFBRThIO2NBQ3ZCO1lBQ0YsQ0FBQyxFQUNEO2NBQUVTLE1BQU0sRUFBRTtZQUFNLENBQ2xCLENBQUM7VUFDSCxDQUFDLENBQ0Y7VUFFRCxLQUFLLElBQUlDLENBQUMsR0FBRyxDQUFDLEVBQUVBLENBQUMsR0FBR0YsYUFBYSxDQUFDbE0sTUFBTSxFQUFFb00sQ0FBQyxFQUFFLEVBQUU7WUFDN0MsSUFBSTtjQUNGOUgsWUFBWSxHQUFHNEgsYUFBYSxDQUFDRSxDQUFDLENBQUMsQ0FBQyxDQUFDO2NBQ2pDeE0sT0FBTyxDQUFDQyxHQUFHLDRCQUFBdUIsTUFBQSxDQUE0QmdMLENBQUMsR0FBRyxDQUFDLGVBQVk5SCxZQUFZLENBQUM7Y0FFckUsSUFBSUEsWUFBWSxLQUFLLENBQUMsRUFBRTtnQkFDdEIxRSxPQUFPLENBQUNDLEdBQUcsbURBQUF1QixNQUFBLENBQW1EZ0wsQ0FBQyxHQUFHLENBQUMsQ0FBRSxDQUFDO2dCQUN0RTtjQUNGO1lBQ0YsQ0FBQyxDQUFDLE9BQU9DLFdBQVcsRUFBRTtjQUNwQnpNLE9BQU8sQ0FBQ21ELEtBQUssNEJBQUEzQixNQUFBLENBQTRCZ0wsQ0FBQyxHQUFHLENBQUMsY0FBV0MsV0FBVyxDQUFDO2NBQ3JFSixXQUFXLEdBQUdJLFdBQVc7WUFDM0I7VUFDRjtVQUVBLElBQUkvSCxZQUFZLEtBQUssQ0FBQyxFQUFFO1lBQ3RCMUUsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLDRDQUE0QyxDQUFDO1lBQzNEbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHVDQUF1QyxFQUFFdUIsWUFBWSxDQUFDO1lBQ3BFMUUsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLDhCQUE4QixFQUFFa0osV0FBVyxDQUFDOztZQUUxRDtZQUNBck0sT0FBTyxDQUFDQyxHQUFHLENBQUMsMkNBQTJDLENBQUM7WUFDeEQsTUFBTXlNLFNBQVMsR0FBRyxNQUFNL08sTUFBTSxDQUFDVyxLQUFLLENBQUNDLFlBQVksQ0FBQ0YsSUFBSSxDQUFDNEMsR0FBRyxDQUFDO1lBQzNEakIsT0FBTyxDQUFDQyxHQUFHLENBQUMsNENBQTRDLEVBQUV5TSxTQUFTLEdBQUcsUUFBUSxHQUFHLFdBQVcsQ0FBQztZQUU3RixJQUFJQSxTQUFTLEVBQUU7Y0FDYjFNLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDBDQUEwQyxFQUFFaUwsSUFBSSxDQUFDQyxTQUFTLENBQUN1QixTQUFTLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDOztjQUUzRjtjQUNBMU0sT0FBTyxDQUFDQyxHQUFHLENBQUMsK0NBQStDLENBQUM7Y0FDNUQsSUFBSTtnQkFDRixNQUFNME0sVUFBVSxHQUFHaFAsTUFBTSxDQUFDVyxLQUFLLENBQUM4RixNQUFNLENBQUMvRixJQUFJLENBQUM0QyxHQUFHLEVBQUU7a0JBQy9DK0MsSUFBSSxFQUFFO29CQUNKLGtDQUFrQyxFQUFFLElBQUl2QixJQUFJLENBQUM7a0JBQy9DO2dCQUNGLENBQUMsQ0FBQztnQkFDRnpDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNDQUFzQyxFQUFFME0sVUFBVSxDQUFDO2dCQUUvRCxJQUFJQSxVQUFVLEtBQUssQ0FBQyxFQUFFO2tCQUNwQjNNLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG1GQUFtRixDQUFDOztrQkFFaEc7a0JBQ0FELE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHdEQUF3RCxDQUFDO2tCQUNyRSxNQUFNMk0sWUFBWSxHQUFHalAsTUFBTSxDQUFDVyxLQUFLLENBQUM4RixNQUFNLENBQUMvRixJQUFJLENBQUM0QyxHQUFHLEVBQUU7b0JBQ2pEK0MsSUFBSSxFQUFFO3NCQUNKLDBCQUEwQixFQUFFOEgsWUFBWSxDQUFDeEYsTUFBTSxJQUFJd0Y7b0JBQ3JEO2tCQUNGLENBQUMsQ0FBQztrQkFDRjlMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDhDQUE4QyxFQUFFMk0sWUFBWSxDQUFDO2tCQUV6RSxJQUFJQSxZQUFZLEtBQUssQ0FBQyxFQUFFO29CQUN0QmxJLFlBQVksR0FBR2tJLFlBQVk7b0JBQzNCNU0sT0FBTyxDQUFDQyxHQUFHLENBQUMsa0RBQWtELENBQUM7a0JBQ2pFO2dCQUNGO2NBQ0YsQ0FBQyxDQUFDLE9BQU80TSxTQUFTLEVBQUU7Z0JBQ2xCN00sT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHNDQUFzQyxFQUFFMEosU0FBUyxDQUFDO2NBQ2xFO1lBQ0Y7O1lBRUE7WUFDQSxJQUFJbEIsVUFBVSxFQUFFO2NBQ2QsSUFBSTtnQkFBRSxNQUFNaE8sTUFBTSxDQUFDVyxLQUFLLENBQUM0RixXQUFXLENBQUN5SCxVQUFVLENBQUM7Y0FBRSxDQUFDLENBQUMsT0FBT3RHLENBQUMsRUFBRSxDQUFDO1lBQ2pFO1lBRUEsSUFBSVgsWUFBWSxLQUFLLENBQUMsRUFBRTtjQUN0QixNQUFNLElBQUkvRyxNQUFNLENBQUNnRixLQUFLLENBQUMsd0JBQXdCLG9EQUFBbkIsTUFBQSxDQUFvRGtELFlBQVksQ0FBRSxDQUFDO1lBQ3BIO1VBQ0Y7O1VBRUE7VUFDQSxJQUFJO1lBQ0YsTUFBTS9HLE1BQU0sQ0FBQ1csS0FBSyxDQUFDNEYsV0FBVyxDQUFDeUgsVUFBVSxDQUFDO1lBQzFDM0wsT0FBTyxDQUFDQyxHQUFHLENBQUMsNENBQTRDLENBQUM7VUFDM0QsQ0FBQyxDQUFDLE9BQU82TSxZQUFZLEVBQUU7WUFDckI5TSxPQUFPLENBQUM4RyxJQUFJLENBQUMsOERBQThELEVBQUVnRyxZQUFZLENBQUM7VUFDNUY7VUFFQSxJQUFJcEksWUFBWSxLQUFLLENBQUMsRUFBRTtZQUN0QjFFLE9BQU8sQ0FBQ0MsR0FBRyx5REFBQXVCLE1BQUEsQ0FBeUQ0RixLQUFLLENBQUUsQ0FBQztZQUM1RSxPQUFPO2NBQUUyRixPQUFPLEVBQUUsSUFBSTtjQUFFM0osT0FBTyxFQUFFO1lBQWdDLENBQUM7VUFDcEUsQ0FBQyxNQUFNO1lBQ0wsTUFBTSxJQUFJekYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLHdCQUF3Qiw0Q0FBQW5CLE1BQUEsQ0FBNENrRCxZQUFZLENBQUUsQ0FBQztVQUM1RztRQUVGLENBQUMsQ0FBQyxPQUFPc0ksVUFBVSxFQUFFO1VBQ25CaE4sT0FBTyxDQUFDbUQsS0FBSyxDQUFDLCtDQUErQyxFQUFFNkosVUFBVSxDQUFDO1VBQzFFaE4sT0FBTyxDQUFDbUQsS0FBSyxDQUFDLDZDQUE2QyxFQUFFNkosVUFBVSxDQUFDQyxLQUFLLENBQUM7O1VBRTlFO1VBQ0EsSUFBSUQsVUFBVSxDQUFDN0osS0FBSyxFQUFFO1lBQ3BCLE1BQU02SixVQUFVO1VBQ2xCOztVQUVBO1VBQ0EsTUFBTSxJQUFJclAsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLHdCQUF3Qiw2QkFBQW5CLE1BQUEsQ0FBNkJ3TCxVQUFVLENBQUM1SixPQUFPLENBQUUsQ0FBQztRQUNuRztNQUNGLENBQUM7TUFFRCxNQUFNLDRCQUE0QjhKLENBQUEsRUFBRztRQUNuQyxJQUFJLENBQUMsSUFBSSxDQUFDL08sTUFBTSxFQUFFO1VBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLHVCQUF1QixDQUFDO1FBQ25FO1FBRUEsSUFBSTtVQUFBLElBQUF3SyxhQUFBLEVBQUFDLGNBQUE7VUFDRixNQUFNL08sSUFBSSxHQUFHLE1BQU1WLE1BQU0sQ0FBQ1csS0FBSyxDQUFDQyxZQUFZLENBQUMsSUFBSSxDQUFDSixNQUFNLENBQUM7VUFDekQ2QixPQUFPLENBQUNDLEdBQUcsQ0FBQyx1Q0FBdUMsRUFBRTtZQUNuRGtILEVBQUUsRUFBRTlJLElBQUksYUFBSkEsSUFBSSx1QkFBSkEsSUFBSSxDQUFFNEMsR0FBRztZQUNibUcsS0FBSyxFQUFFL0ksSUFBSSxhQUFKQSxJQUFJLHdCQUFBOE8sYUFBQSxHQUFKOU8sSUFBSSxDQUFFOEMsTUFBTSxjQUFBZ00sYUFBQSx3QkFBQUMsY0FBQSxHQUFaRCxhQUFBLENBQWUsQ0FBQyxDQUFDLGNBQUFDLGNBQUEsdUJBQWpCQSxjQUFBLENBQW1CL0YsT0FBTztZQUNqQzVJLEtBQUssRUFBRUosSUFBSSxhQUFKQSxJQUFJLHVCQUFKQSxJQUFJLENBQUVJO1VBQ2YsQ0FBQyxDQUFDOztVQUVGO1VBQ0EsSUFBSSxDQUFDSixJQUFJLENBQUNJLEtBQUssRUFBRTtZQUNmLE1BQU1kLE1BQU0sQ0FBQ1csS0FBSyxDQUFDeUYsV0FBVyxDQUFDLElBQUksQ0FBQzVGLE1BQU0sRUFBRTtjQUMxQzZGLElBQUksRUFBRTtnQkFBRXZGLEtBQUssRUFBRSxDQUFDLGFBQWE7Y0FBRTtZQUNqQyxDQUFDLENBQUM7WUFDRixPQUFPLG1CQUFtQjtVQUM1Qjs7VUFFQTtVQUNBLElBQUksQ0FBQ0osSUFBSSxDQUFDSSxLQUFLLENBQUNDLFFBQVEsQ0FBQyxPQUFPLENBQUMsRUFBRTtZQUNqQ3NCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtFQUFrRSxDQUFDOztZQUUvRTtZQUNBLE1BQU1vTixVQUFVLEdBQUcsTUFBTTFQLE1BQU0sQ0FBQ1csS0FBSyxDQUFDSyxJQUFJLENBQUMsQ0FBQyxDQUFDa0osVUFBVSxDQUFDLENBQUM7WUFDekQsSUFBSXdGLFVBQVUsS0FBSyxDQUFDLEVBQUU7Y0FDcEJyTixPQUFPLENBQUNDLEdBQUcsQ0FBQyxxREFBcUQsQ0FBQztjQUNsRSxNQUFNdEMsTUFBTSxDQUFDVyxLQUFLLENBQUN5RixXQUFXLENBQUMsSUFBSSxDQUFDNUYsTUFBTSxFQUFFO2dCQUMxQzZGLElBQUksRUFBRTtrQkFBRXZGLEtBQUssRUFBRSxDQUFDLE9BQU87Z0JBQUU7Y0FDM0IsQ0FBQyxDQUFDO2NBQ0YsT0FBTyxrQkFBa0I7WUFDM0I7WUFDQSxPQUFPLG1CQUFtQjtVQUM1QjtVQUVBLE9BQU8sdUJBQXVCO1FBQ2hDLENBQUMsQ0FBQyxPQUFPMEUsS0FBSyxFQUFFO1VBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsK0JBQStCLEVBQUVBLEtBQUssQ0FBQztVQUNyRCxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsbUJBQW1CLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxDQUFDO1FBQzVEO01BQ0YsQ0FBQztNQUVELE1BQU0scUJBQXFCa0ssQ0FBQSxFQUFHO1FBQzVCLElBQUksQ0FBQyxJQUFJLENBQUNuUCxNQUFNLEVBQUU7VUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsdUJBQXVCLENBQUM7UUFDbkU7UUFFQSxJQUFJO1VBQUEsSUFBQTRLLGtCQUFBO1VBQ0YsTUFBTUMsV0FBVyxHQUFHLE1BQU03UCxNQUFNLENBQUNXLEtBQUssQ0FBQ0MsWUFBWSxDQUFDLElBQUksQ0FBQ0osTUFBTSxDQUFDO1VBQ2hFLElBQUksR0FBQW9QLGtCQUFBLEdBQUNDLFdBQVcsQ0FBQy9PLEtBQUssY0FBQThPLGtCQUFBLGVBQWpCQSxrQkFBQSxDQUFtQjdPLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRTtZQUN6QyxNQUFNLElBQUlmLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxnQ0FBZ0MsQ0FBQztVQUM1RTtVQUVBLE1BQU1vRSxRQUFRLEdBQUcsTUFBTXBKLE1BQU0sQ0FBQ1csS0FBSyxDQUFDSyxJQUFJLENBQUMsQ0FBQyxDQUFDcUksVUFBVSxDQUFDLENBQUM7VUFDdkQsTUFBTXlHLGVBQWUsR0FBRyxFQUFFO1VBQzFCLE1BQU1DLEtBQUssR0FBRyxFQUFFO1VBRWhCLEtBQUssTUFBTXJQLElBQUksSUFBSTBJLFFBQVEsRUFBRTtZQUFBLElBQUE0RyxjQUFBLEVBQUE1TCxZQUFBO1lBQzNCLE1BQU02TCxNQUFNLEdBQUcsRUFBRTs7WUFFakI7WUFDQSxJQUFJLENBQUN2UCxJQUFJLENBQUNJLEtBQUssSUFBSSxDQUFDc0MsS0FBSyxDQUFDd0csT0FBTyxDQUFDbEosSUFBSSxDQUFDSSxLQUFLLENBQUMsRUFBRTtjQUFBLElBQUFvUCxjQUFBLEVBQUFDLGFBQUEsRUFBQUMsY0FBQTtjQUM3Q0gsTUFBTSxDQUFDcEQsSUFBSSxDQUFDLGdCQUFnQixDQUFDO2NBQzdCO2NBQ0EsTUFBTXRDLElBQUksR0FBRyxFQUFBMkYsY0FBQSxHQUFBeFAsSUFBSSxDQUFDb0QsT0FBTyxjQUFBb00sY0FBQSx1QkFBWkEsY0FBQSxDQUFjM0YsSUFBSSxLQUFJLGFBQWE7Y0FDaEQsTUFBTXZLLE1BQU0sQ0FBQ1csS0FBSyxDQUFDeUYsV0FBVyxDQUFDMUYsSUFBSSxDQUFDNEMsR0FBRyxFQUFFO2dCQUN2QytDLElBQUksRUFBRTtrQkFBRXZGLEtBQUssRUFBRSxDQUFDeUosSUFBSTtnQkFBRTtjQUN4QixDQUFDLENBQUM7Y0FDRndGLEtBQUssQ0FBQ2xELElBQUksMEJBQUFoSixNQUFBLEVBQUFzTSxhQUFBLEdBQTBCelAsSUFBSSxDQUFDOEMsTUFBTSxjQUFBMk0sYUFBQSx3QkFBQUMsY0FBQSxHQUFYRCxhQUFBLENBQWMsQ0FBQyxDQUFDLGNBQUFDLGNBQUEsdUJBQWhCQSxjQUFBLENBQWtCMUcsT0FBTyxDQUFFLENBQUM7WUFDbEU7O1lBRUE7WUFDQSxJQUFJLENBQUFzRyxjQUFBLEdBQUF0UCxJQUFJLENBQUNvRCxPQUFPLGNBQUFrTSxjQUFBLGVBQVpBLGNBQUEsQ0FBY3pGLElBQUksSUFBSSxFQUFBbkcsWUFBQSxHQUFBMUQsSUFBSSxDQUFDSSxLQUFLLGNBQUFzRCxZQUFBLHVCQUFWQSxZQUFBLENBQWEsQ0FBQyxDQUFDLE1BQUsxRCxJQUFJLENBQUNvRCxPQUFPLENBQUN5RyxJQUFJLEVBQUU7Y0FBQSxJQUFBOEYsYUFBQSxFQUFBQyxjQUFBO2NBQy9ETCxNQUFNLENBQUNwRCxJQUFJLENBQUMsNEJBQTRCLENBQUM7Y0FDekM7Y0FDQSxNQUFNN00sTUFBTSxDQUFDVyxLQUFLLENBQUN5RixXQUFXLENBQUMxRixJQUFJLENBQUM0QyxHQUFHLEVBQUU7Z0JBQ3ZDK0MsSUFBSSxFQUFFO2tCQUFFdkYsS0FBSyxFQUFFLENBQUNKLElBQUksQ0FBQ29ELE9BQU8sQ0FBQ3lHLElBQUk7Z0JBQUU7Y0FDckMsQ0FBQyxDQUFDO2NBQ0Z3RixLQUFLLENBQUNsRCxJQUFJLDRCQUFBaEosTUFBQSxFQUFBd00sYUFBQSxHQUE0QjNQLElBQUksQ0FBQzhDLE1BQU0sY0FBQTZNLGFBQUEsd0JBQUFDLGNBQUEsR0FBWEQsYUFBQSxDQUFjLENBQUMsQ0FBQyxjQUFBQyxjQUFBLHVCQUFoQkEsY0FBQSxDQUFrQjVHLE9BQU8sQ0FBRSxDQUFDO1lBQ3BFO1lBRUEsSUFBSXVHLE1BQU0sQ0FBQ3hOLE1BQU0sR0FBRyxDQUFDLEVBQUU7Y0FBQSxJQUFBOE4sYUFBQSxFQUFBQyxjQUFBO2NBQ3JCVixlQUFlLENBQUNqRCxJQUFJLENBQUM7Z0JBQ25CcEQsS0FBSyxHQUFBOEcsYUFBQSxHQUFFN1AsSUFBSSxDQUFDOEMsTUFBTSxjQUFBK00sYUFBQSx3QkFBQUMsY0FBQSxHQUFYRCxhQUFBLENBQWMsQ0FBQyxDQUFDLGNBQUFDLGNBQUEsdUJBQWhCQSxjQUFBLENBQWtCOUcsT0FBTztnQkFDaEN1RztjQUNGLENBQUMsQ0FBQztZQUNKO1VBQ0Y7VUFFQSxPQUFPO1lBQ0xILGVBQWU7WUFDZkMsS0FBSztZQUNMdEssT0FBTyxFQUFFc0ssS0FBSyxDQUFDdE4sTUFBTSxHQUFHLENBQUMsR0FBRyxtQkFBbUIsR0FBRztVQUNwRCxDQUFDO1FBQ0gsQ0FBQyxDQUFDLE9BQU8rQyxLQUFLLEVBQUU7VUFDZCxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsaUJBQWlCLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxDQUFDO1FBQzFEO01BQ0YsQ0FBQztNQUVELDRCQUE0QmdMLENBQUEsRUFBRztRQUM3QjtRQUNBLElBQUksQ0FBQzVGLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDNEYsUUFBUSxJQUFJN0YsT0FBTyxDQUFDQyxHQUFHLENBQUM0RixRQUFRLEtBQUssYUFBYSxFQUFFO1VBQ25FLElBQUk7WUFDRixNQUFNQyxVQUFVLEdBQUc7Y0FDakJsSCxLQUFLLEVBQUUsd0JBQXdCO2NBQy9CVyxRQUFRLEVBQUUsY0FBYztjQUN4QnJHLFNBQVMsRUFBRSxNQUFNO2NBQ2pCQyxRQUFRLEVBQUU7WUFDWixDQUFDO1lBRUQsTUFBTXhELE1BQU0sR0FBR2dJLFFBQVEsQ0FBQ3VFLFVBQVUsQ0FBQztjQUNqQ3RELEtBQUssRUFBRWtILFVBQVUsQ0FBQ2xILEtBQUs7Y0FDdkJXLFFBQVEsRUFBRXVHLFVBQVUsQ0FBQ3ZHLFFBQVE7Y0FDN0J0RyxPQUFPLEVBQUU7Z0JBQ1BDLFNBQVMsRUFBRTRNLFVBQVUsQ0FBQzVNLFNBQVM7Z0JBQy9CQyxRQUFRLEVBQUUyTSxVQUFVLENBQUMzTSxRQUFRO2dCQUM3QnVHLElBQUksRUFBRSxhQUFhO2dCQUNuQkMsUUFBUSxLQUFBM0csTUFBQSxDQUFLOE0sVUFBVSxDQUFDNU0sU0FBUyxPQUFBRixNQUFBLENBQUk4TSxVQUFVLENBQUMzTSxRQUFRO2NBQzFEO1lBQ0YsQ0FBQyxDQUFDOztZQUVGO1lBQ0FoRSxNQUFNLENBQUNXLEtBQUssQ0FBQzhGLE1BQU0sQ0FBQ2pHLE1BQU0sRUFBRTtjQUMxQjZGLElBQUksRUFBRTtnQkFBRXZGLEtBQUssRUFBRSxDQUFDLGFBQWE7Y0FBRTtZQUNqQyxDQUFDLENBQUM7WUFFRixPQUFPO2NBQ0xzTyxPQUFPLEVBQUUsSUFBSTtjQUNiNU8sTUFBTTtjQUNOaUYsT0FBTyxFQUFFO1lBQ1gsQ0FBQztVQUNILENBQUMsQ0FBQyxPQUFPRCxLQUFLLEVBQUU7WUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQywrQkFBK0IsRUFBRUEsS0FBSyxDQUFDO1lBQ3JELE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQywyQkFBMkIsRUFBRVEsS0FBSyxDQUFDQyxPQUFPLENBQUM7VUFDcEU7UUFDRixDQUFDLE1BQU07VUFDTCxNQUFNLElBQUl6RixNQUFNLENBQUNnRixLQUFLLENBQUMsaUJBQWlCLEVBQUUsOENBQThDLENBQUM7UUFDM0Y7TUFDRjtJQUNGLENBQUMsQ0FBQztJQUFDekYsc0JBQUE7RUFBQSxTQUFBQyxXQUFBO0lBQUEsT0FBQUQsc0JBQUEsQ0FBQUMsV0FBQTtFQUFBO0VBQUFELHNCQUFBO0FBQUE7RUFBQUUsSUFBQTtFQUFBQyxLQUFBO0FBQUEsRyIsImZpbGUiOiIvYXBwLmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTW9uZ28gfSBmcm9tICdtZXRlb3IvbW9uZ28nO1xyXG5cclxuZXhwb3J0IGNvbnN0IExpbmtzQ29sbGVjdGlvbiA9IG5ldyBNb25nby5Db2xsZWN0aW9uKCdsaW5rcycpO1xyXG4iLCJpbXBvcnQgeyBNZXRlb3IgfSBmcm9tICdtZXRlb3IvbWV0ZW9yJztcbmltcG9ydCB7IE1vbmdvIH0gZnJvbSAnbWV0ZW9yL21vbmdvJztcbmltcG9ydCB7IGNoZWNrIH0gZnJvbSAnbWV0ZW9yL2NoZWNrJztcbmltcG9ydCB7IE1hdGNoIH0gZnJvbSAnbWV0ZW9yL2NoZWNrJztcblxuZXhwb3J0IGNvbnN0IFRhc2tzID0gbmV3IE1vbmdvLkNvbGxlY3Rpb24oJ3Rhc2tzJyk7XG5cbmV4cG9ydCBjb25zdCB0YXNrQ2F0ZWdvcmllcyA9IFtcbiAgJ0RldmVsb3BtZW50JyxcbiAgJ0Rlc2lnbicsXG4gICdNYXJrZXRpbmcnLFxuICAnU2FsZXMnLFxuICAnU3VwcG9ydCcsXG4gICdQbGFubmluZycsXG4gICdSZXNlYXJjaCcsXG4gICdPdGhlcidcbl07XG5cbmV4cG9ydCBjb25zdCB0YXNrTGFiZWxzID0gW1xuICB7IG5hbWU6ICdCdWcnLCBjb2xvcjogJyNlZjQ0NDQnIH0sXG4gIHsgbmFtZTogJ0ZlYXR1cmUnLCBjb2xvcjogJyMzYjgyZjYnIH0sXG4gIHsgbmFtZTogJ0VuaGFuY2VtZW50JywgY29sb3I6ICcjMTBiOTgxJyB9LFxuICB7IG5hbWU6ICdEb2N1bWVudGF0aW9uJywgY29sb3I6ICcjOGI1Y2Y2JyB9LFxuICB7IG5hbWU6ICdVcmdlbnQnLCBjb2xvcjogJyNmNTllMGInIH0sXG4gIHsgbmFtZTogJ0Jsb2NrZWQnLCBjb2xvcjogJyM2YjcyODAnIH1cbl07XG5cbmlmIChNZXRlb3IuaXNTZXJ2ZXIpIHtcbiAgLy8gUHVibGljYXRpb25zXG4gIE1ldGVvci5wdWJsaXNoKCd0YXNrcycsIGFzeW5jIGZ1bmN0aW9uKCkge1xuICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgIHJldHVybiB0aGlzLnJlYWR5KCk7XG4gICAgfVxuXG4gICAgLy8gR2V0IHVzZXIncyByb2xlXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kT25lQXN5bmModGhpcy51c2VySWQpO1xuICAgIGNvbnN0IGlzQWRtaW4gPSB1c2VyPy5yb2xlcz8uaW5jbHVkZXMoJ2FkbWluJyk7XG5cbiAgICAvLyBJZiBhZG1pbiwgc2hvdyBhbGwgdGFza3NcbiAgICBpZiAoaXNBZG1pbikge1xuICAgICAgcmV0dXJuIFRhc2tzLmZpbmQoe30sIHsgXG4gICAgICAgIHNvcnQ6IHsgY3JlYXRlZEF0OiAtMSB9LFxuICAgICAgICBmaWVsZHM6IHtcbiAgICAgICAgICB0aXRsZTogMSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogMSxcbiAgICAgICAgICBzdGFydERhdGU6IDEsXG4gICAgICAgICAgZHVlRGF0ZTogMSxcbiAgICAgICAgICBwcmlvcml0eTogMSxcbiAgICAgICAgICBzdGF0dXM6IDEsXG4gICAgICAgICAgYXNzaWduZWRUbzogMSxcbiAgICAgICAgICBjaGVja2xpc3Q6IDEsXG4gICAgICAgICAgY2F0ZWdvcnk6IDEsXG4gICAgICAgICAgbGFiZWxzOiAxLFxuICAgICAgICAgIHByb2dyZXNzOiAxLFxuICAgICAgICAgIGF0dGFjaG1lbnRzOiAxLFxuICAgICAgICAgIGxpbmtzOiAxLFxuICAgICAgICAgIGNyZWF0ZWRBdDogMSxcbiAgICAgICAgICBjcmVhdGVkQnk6IDEsXG4gICAgICAgICAgdXBkYXRlZEF0OiAxLFxuICAgICAgICAgIHVwZGF0ZWRCeTogMVxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBGb3IgdGVhbSBtZW1iZXJzLCBzaG93IHRhc2tzIHRoZXkncmUgYXNzaWduZWQgdG8gb3IgY3JlYXRlZFxuICAgIHJldHVybiBUYXNrcy5maW5kKHtcbiAgICAgICRvcjogW1xuICAgICAgICB7IGFzc2lnbmVkVG86IHRoaXMudXNlcklkIH0sXG4gICAgICAgIHsgY3JlYXRlZEJ5OiB0aGlzLnVzZXJJZCB9XG4gICAgICBdXG4gICAgfSwgeyBcbiAgICAgIHNvcnQ6IHsgY3JlYXRlZEF0OiAtMSB9LFxuICAgICAgZmllbGRzOiB7XG4gICAgICAgIHRpdGxlOiAxLFxuICAgICAgICBkZXNjcmlwdGlvbjogMSxcbiAgICAgICAgc3RhcnREYXRlOiAxLFxuICAgICAgICBkdWVEYXRlOiAxLFxuICAgICAgICBwcmlvcml0eTogMSxcbiAgICAgICAgc3RhdHVzOiAxLFxuICAgICAgICBhc3NpZ25lZFRvOiAxLFxuICAgICAgICBjaGVja2xpc3Q6IDEsXG4gICAgICAgIGNhdGVnb3J5OiAxLFxuICAgICAgICBsYWJlbHM6IDEsXG4gICAgICAgIHByb2dyZXNzOiAxLFxuICAgICAgICBhdHRhY2htZW50czogMSxcbiAgICAgICAgbGlua3M6IDEsXG4gICAgICAgIGNyZWF0ZWRBdDogMSxcbiAgICAgICAgY3JlYXRlZEJ5OiAxLFxuICAgICAgICB1cGRhdGVkQXQ6IDEsXG4gICAgICAgIHVwZGF0ZWRCeTogMVxuICAgICAgfVxuICAgIH0pO1xuICB9KTtcblxuICAvLyBQdWJsaXNoIHVzZXIgZGF0YSBmb3IgdGFza3NcbiAgTWV0ZW9yLnB1Ymxpc2goJ3Rhc2tVc2VycycsIGZ1bmN0aW9uKCkge1xuICAgIGNvbnNvbGUubG9nKCdTdGFydGluZyB0YXNrVXNlcnMgcHVibGljYXRpb24nKTtcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICBjb25zb2xlLmxvZygnTm8gdXNlcklkLCByZXR1cm5pbmcgcmVhZHknKTtcbiAgICAgIHJldHVybiB0aGlzLnJlYWR5KCk7XG4gICAgfVxuXG4gICAgLy8gR2V0IGFsbCB0YXNrc1xuICAgIGNvbnN0IHRhc2tzID0gVGFza3MuZmluZCh7fSkuZmV0Y2goKTtcbiAgICBjb25zb2xlLmxvZygnRm91bmQgdGFza3M6JywgdGFza3MubGVuZ3RoKTtcbiAgICBcbiAgICAvLyBDb2xsZWN0IGFsbCB1c2VyIElEcyBmcm9tIHRhc2tzXG4gICAgY29uc3QgdXNlcklkcyA9IG5ldyBTZXQoKTtcbiAgICB0YXNrcy5mb3JFYWNoKHRhc2sgPT4ge1xuICAgICAgLy8gQWRkIHVzZXJzIHdobyB1cGxvYWRlZCBhdHRhY2htZW50c1xuICAgICAgaWYgKHRhc2suYXR0YWNobWVudHMpIHtcbiAgICAgICAgdGFzay5hdHRhY2htZW50cy5mb3JFYWNoKGF0dGFjaG1lbnQgPT4ge1xuICAgICAgICAgIGlmIChhdHRhY2htZW50LnVwbG9hZGVkQnkpIHtcbiAgICAgICAgICAgIHVzZXJJZHMuYWRkKFN0cmluZyhhdHRhY2htZW50LnVwbG9hZGVkQnkpKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgLy8gQWRkIHVzZXJzIHdobyBhZGRlZCBsaW5rc1xuICAgICAgaWYgKHRhc2subGlua3MpIHtcbiAgICAgICAgdGFzay5saW5rcy5mb3JFYWNoKGxpbmsgPT4ge1xuICAgICAgICAgIGlmIChsaW5rLmFkZGVkQnkpIHtcbiAgICAgICAgICAgIHVzZXJJZHMuYWRkKFN0cmluZyhsaW5rLmFkZGVkQnkpKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgLy8gQWRkIGFzc2lnbmVkIHVzZXJzXG4gICAgICBpZiAodGFzay5hc3NpZ25lZFRvKSB7XG4gICAgICAgIHRhc2suYXNzaWduZWRUby5mb3JFYWNoKHVzZXJJZCA9PiB7XG4gICAgICAgICAgdXNlcklkcy5hZGQoU3RyaW5nKHVzZXJJZCkpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGNvbnN0IHVzZXJJZEFycmF5ID0gQXJyYXkuZnJvbSh1c2VySWRzKTtcbiAgICBjb25zb2xlLmxvZygnUHVibGlzaGluZyB1c2VyIGRhdGEgZm9yIElEczonLCB1c2VySWRBcnJheSk7XG5cbiAgICAvLyBGaW5kIHVzZXJzIGFuZCBsb2cgd2hhdCB3ZSBmb3VuZFxuICAgIGNvbnN0IHVzZXJzID0gTWV0ZW9yLnVzZXJzLmZpbmQoXG4gICAgICB7IF9pZDogeyAkaW46IHVzZXJJZEFycmF5IH0gfSxcbiAgICAgIHsgXG4gICAgICAgIGZpZWxkczoge1xuICAgICAgICAgIF9pZDogMSxcbiAgICAgICAgICBlbWFpbHM6IDEsXG4gICAgICAgICAgcm9sZXM6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUuZmlyc3ROYW1lJzogMSxcbiAgICAgICAgICAncHJvZmlsZS5sYXN0TmFtZSc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUucm9sZSc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUuZGVwYXJ0bWVudCc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUuc2tpbGxzJzogMSxcbiAgICAgICAgICAncHJvZmlsZS5qb2luRGF0ZSc6IDEsXG4gICAgICAgICAgY3JlYXRlZEF0OiAxXG4gICAgICAgIH0gXG4gICAgICB9XG4gICAgKS5mZXRjaCgpO1xuXG4gICAgY29uc29sZS5sb2coJ0ZvdW5kIHVzZXJzOicsIHVzZXJzLm1hcCh1ID0+ICh7XG4gICAgICBfaWQ6IHUuX2lkLFxuICAgICAgbmFtZTogYCR7dS5wcm9maWxlPy5maXJzdE5hbWUgfHwgJyd9ICR7dS5wcm9maWxlPy5sYXN0TmFtZSB8fCAnJ31gLnRyaW0oKSxcbiAgICAgIGhhc1Byb2ZpbGU6ICEhdS5wcm9maWxlXG4gICAgfSkpKTtcblxuICAgIC8vIFJldHVybiB0aGUgY3Vyc29yXG4gICAgcmV0dXJuIE1ldGVvci51c2Vycy5maW5kKFxuICAgICAgeyBfaWQ6IHsgJGluOiB1c2VySWRBcnJheSB9IH0sXG4gICAgICB7IFxuICAgICAgZmllbGRzOiB7XG4gICAgICAgIF9pZDogMSxcbiAgICAgICAgICBlbWFpbHM6IDEsXG4gICAgICAgICAgcm9sZXM6IDEsXG4gICAgICAgICdwcm9maWxlLmZpcnN0TmFtZSc6IDEsXG4gICAgICAgICdwcm9maWxlLmxhc3ROYW1lJzogMSxcbiAgICAgICAgICAncHJvZmlsZS5yb2xlJzogMSxcbiAgICAgICAgICAncHJvZmlsZS5kZXBhcnRtZW50JzogMSxcbiAgICAgICAgICAncHJvZmlsZS5za2lsbHMnOiAxLFxuICAgICAgICAgICdwcm9maWxlLmpvaW5EYXRlJzogMSxcbiAgICAgICAgICBjcmVhdGVkQXQ6IDFcbiAgICAgICAgfSBcbiAgICAgIH1cbiAgICApO1xuICB9KTtcblxuICAvLyBBZGQgYSBzcGVjaWZpYyBwdWJsaWNhdGlvbiBmb3IgYSBzaW5nbGUgdGFza1xuICBNZXRlb3IucHVibGlzaCgndGFzaycsIGZ1bmN0aW9uKHRhc2tJZCkge1xuICAgIGNoZWNrKHRhc2tJZCwgU3RyaW5nKTtcbiAgICBcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICByZXR1cm4gdGhpcy5yZWFkeSgpO1xuICAgIH1cblxuICAgIGNvbnN0IHVzZXIgPSBNZXRlb3IudXNlcnMuZmluZE9uZSh0aGlzLnVzZXJJZCk7XG4gICAgY29uc3QgaXNBZG1pbiA9IHVzZXI/LnJvbGVzPy5pbmNsdWRlcygnYWRtaW4nKTtcblxuICAgIC8vIFJldHVybiBhIGN1cnNvciB0aGF0IHdpbGwgdXBkYXRlIHJlYWN0aXZlbHlcbiAgICBjb25zdCBjdXJzb3IgPSBUYXNrcy5maW5kKFxuICAgICAgeyBfaWQ6IHRhc2tJZCB9LFxuICAgICAge1xuICAgICAgICBmaWVsZHM6IHtcbiAgICAgICAgICB0aXRsZTogMSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogMSxcbiAgICAgICAgICBzdGFydERhdGU6IDEsXG4gICAgICAgICAgZHVlRGF0ZTogMSxcbiAgICAgICAgICBwcmlvcml0eTogMSxcbiAgICAgICAgICBzdGF0dXM6IDEsXG4gICAgICAgICAgYXNzaWduZWRUbzogMSxcbiAgICAgICAgICBjaGVja2xpc3Q6IDEsXG4gICAgICAgICAgY2F0ZWdvcnk6IDEsXG4gICAgICAgICAgbGFiZWxzOiAxLFxuICAgICAgICAgIHByb2dyZXNzOiAxLFxuICAgICAgICAgIGF0dGFjaG1lbnRzOiAxLFxuICAgICAgICAgIGxpbmtzOiAxLFxuICAgICAgICAgIGNyZWF0ZWRBdDogMSxcbiAgICAgICAgICBjcmVhdGVkQnk6IDEsXG4gICAgICAgICAgdXBkYXRlZEF0OiAxLFxuICAgICAgICAgIHVwZGF0ZWRCeTogMVxuICAgICAgICB9XG4gICAgICB9XG4gICAgKTtcblxuICAgIC8vIExvZyBmb3IgZGVidWdnaW5nXG4gICAgY29uc29sZS5sb2coJ1B1Ymxpc2hpbmcgdGFzazonLCB0YXNrSWQpO1xuICAgIGN1cnNvci5vYnNlcnZlKHtcbiAgICAgIGFkZGVkOiAoZG9jKSA9PiBjb25zb2xlLmxvZygnVGFzayBhZGRlZCB0byBwdWJsaWNhdGlvbjonLCBkb2MuX2lkKSxcbiAgICAgIGNoYW5nZWQ6IChkb2MpID0+IGNvbnNvbGUubG9nKCdUYXNrIGNoYW5nZWQgaW4gcHVibGljYXRpb246JywgZG9jLl9pZCksXG4gICAgICByZW1vdmVkOiAoZG9jKSA9PiBjb25zb2xlLmxvZygnVGFzayByZW1vdmVkIGZyb20gcHVibGljYXRpb246JywgZG9jLl9pZClcbiAgICB9KTtcblxuICAgIHJldHVybiBjdXJzb3I7XG4gIH0pO1xufVxuXG5NZXRlb3IubWV0aG9kcyh7XG4gIGFzeW5jICd0YXNrcy5pbnNlcnQnKHRhc2spIHtcbiAgICBjaGVjayh0YXNrLCB7XG4gICAgICB0aXRsZTogU3RyaW5nLFxuICAgICAgZGVzY3JpcHRpb246IFN0cmluZyxcbiAgICAgIHN0YXJ0RGF0ZTogRGF0ZSxcbiAgICAgIGR1ZURhdGU6IERhdGUsXG4gICAgICBwcmlvcml0eTogU3RyaW5nLFxuICAgICAgc3RhdHVzOiBTdHJpbmcsXG4gICAgICBhc3NpZ25lZFRvOiBBcnJheSxcbiAgICAgIGNoZWNrbGlzdDogQXJyYXksXG4gICAgICBjYXRlZ29yeTogU3RyaW5nLFxuICAgICAgbGFiZWxzOiBBcnJheSxcbiAgICAgIHByb2dyZXNzOiBOdW1iZXJcbiAgICB9KTtcblxuICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ05vdCBhdXRob3JpemVkLicpO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdDcmVhdGluZyBuZXcgdGFzazonLCB0YXNrKTsgLy8gRGVidWcgbG9nXG5cbiAgICAvLyBQcm9jZXNzIGNoZWNrbGlzdCBpdGVtc1xuICAgIGNvbnN0IHByb2Nlc3NlZENoZWNrbGlzdCA9IHRhc2suY2hlY2tsaXN0Lm1hcChpdGVtID0+ICh7XG4gICAgICB0ZXh0OiBpdGVtLnRleHQsXG4gICAgICBjb21wbGV0ZWQ6IGl0ZW0uY29tcGxldGVkIHx8IGZhbHNlXG4gICAgfSkpO1xuXG4gICAgY29uc3QgdGFza1RvSW5zZXJ0ID0ge1xuICAgICAgLi4udGFzayxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcbiAgICAgIGNyZWF0ZWRCeTogdGhpcy51c2VySWQsXG4gICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICB1cGRhdGVkQnk6IHRoaXMudXNlcklkLFxuICAgICAgcHJvZ3Jlc3M6IHRhc2sucHJvZ3Jlc3MgfHwgMCxcbiAgICAgIHN0YXR1czogJ3BlbmRpbmcnLCAvLyBEZWZhdWx0IHN0YXR1c1xuICAgICAgY2hlY2tsaXN0OiBwcm9jZXNzZWRDaGVja2xpc3QsXG4gICAgICBsYWJlbHM6IHRhc2subGFiZWxzIHx8IFtdLFxuICAgICAgY2F0ZWdvcnk6IHRhc2suY2F0ZWdvcnkgfHwgJycsXG4gICAgICBhc3NpZ25lZFRvOiB0YXNrLmFzc2lnbmVkVG8gfHwgW11cbiAgICB9O1xuXG4gICAgY29uc29sZS5sb2coJ0luc2VydGluZyB0YXNrIHdpdGggdmFsdWVzOicsIHRhc2tUb0luc2VydCk7IC8vIERlYnVnIGxvZ1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhc2tzLmluc2VydEFzeW5jKHRhc2tUb0luc2VydCk7XG4gICAgICBjb25zb2xlLmxvZygnVGFzayBjcmVhdGVkIHN1Y2Nlc3NmdWxseTonLCByZXN1bHQpOyAvLyBEZWJ1ZyBsb2dcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHRhc2s6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndGFzay1jcmVhdGlvbi1mYWlsZWQnLCBlcnJvci5tZXNzYWdlKTtcbiAgICB9XG4gIH0sXG5cbiAgYXN5bmMgJ3Rhc2tzLnVwZGF0ZScodGFza0lkLCB0YXNrKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCdTdGFydGluZyB0YXNrIHVwZGF0ZTonLCB7IHRhc2tJZCwgdGFzayB9KTtcblxuICAgICAgY2hlY2sodGFza0lkLCBTdHJpbmcpO1xuICAgICAgY2hlY2sodGFzaywge1xuICAgICAgICB0aXRsZTogU3RyaW5nLFxuICAgICAgICBkZXNjcmlwdGlvbjogU3RyaW5nLFxuICAgICAgICBzdGFydERhdGU6IERhdGUsXG4gICAgICAgIGR1ZURhdGU6IERhdGUsXG4gICAgICAgIHByaW9yaXR5OiBTdHJpbmcsXG4gICAgICAgIGFzc2lnbmVkVG86IEFycmF5LFxuICAgICAgICBjaGVja2xpc3Q6IEFycmF5LFxuICAgICAgICBjYXRlZ29yeTogTWF0Y2guT3B0aW9uYWwoU3RyaW5nKSxcbiAgICAgICAgbGFiZWxzOiBNYXRjaC5PcHRpb25hbChBcnJheSksXG4gICAgICAgIHByb2dyZXNzOiBNYXRjaC5PcHRpb25hbChOdW1iZXIpLFxuICAgICAgICBzdGF0dXM6IE1hdGNoLk9wdGlvbmFsKFN0cmluZyksXG4gICAgICAgIGF0dGFjaG1lbnRzOiBNYXRjaC5PcHRpb25hbChBcnJheSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBtdXN0IGJlIGxvZ2dlZCBpbiB0byBwZXJmb3JtIHRoaXMgYWN0aW9uJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGV4aXN0aW5nVGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgaWYgKCFleGlzdGluZ1Rhc2spIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWZvdW5kJywgJ1Rhc2sgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYXNzaWduZWQgdG8gdGhlIHRhc2sgb3IgaXMgYWRtaW5cbiAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICAgIGNvbnN0IGlzQWRtaW4gPSB1c2VyPy5yb2xlcz8uaW5jbHVkZXMoJ2FkbWluJyk7XG4gICAgICBcbiAgICAgIGlmICghaXNBZG1pbiAmJiAhZXhpc3RpbmdUYXNrLmFzc2lnbmVkVG8uaW5jbHVkZXModGhpcy51c2VySWQpKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBhcmUgbm90IGF1dGhvcml6ZWQgdG8gbW9kaWZ5IHRoaXMgdGFzaycpO1xuICAgICAgfVxuXG4gICAgICAvLyBDYWxjdWxhdGUgcHJvZ3Jlc3MgYmFzZWQgb24gY2hlY2tsaXN0XG4gICAgICBjb25zdCBjb21wbGV0ZWRJdGVtcyA9IHRhc2suY2hlY2tsaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0uY29tcGxldGVkKS5sZW5ndGg7XG4gICAgICBjb25zdCB0b3RhbEl0ZW1zID0gdGFzay5jaGVja2xpc3QubGVuZ3RoO1xuICAgICAgY29uc3QgcHJvZ3Jlc3MgPSB0b3RhbEl0ZW1zID4gMCA/IE1hdGgucm91bmQoKGNvbXBsZXRlZEl0ZW1zIC8gdG90YWxJdGVtcykgKiAxMDApIDogMDtcblxuICAgICAgLy8gVXBkYXRlIHRhc2sgc3RhdHVzIGJhc2VkIG9uIHByb2dyZXNzXG4gICAgICBsZXQgc3RhdHVzID0gdGFzay5zdGF0dXM7XG4gICAgICBpZiAocHJvZ3Jlc3MgPT09IDEwMCkge1xuICAgICAgICBzdGF0dXMgPSAnY29tcGxldGVkJztcbiAgICAgIH0gZWxzZSBpZiAocHJvZ3Jlc3MgPiAwKSB7XG4gICAgICAgIHN0YXR1cyA9ICdpbi1wcm9ncmVzcyc7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzdGF0dXMgPSAncGVuZGluZyc7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRhc2tUb1VwZGF0ZSA9IHtcbiAgICAgICAgLi4udGFzayxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICB1cGRhdGVkQnk6IHRoaXMudXNlcklkLFxuICAgICAgICBwcm9ncmVzcyxcbiAgICAgICAgc3RhdHVzLFxuICAgICAgICBjYXRlZ29yeTogdGFzay5jYXRlZ29yeSB8fCBleGlzdGluZ1Rhc2suY2F0ZWdvcnkgfHwgJycsXG4gICAgICAgIGxhYmVsczogdGFzay5sYWJlbHMgfHwgZXhpc3RpbmdUYXNrLmxhYmVscyB8fCBbXSxcbiAgICAgICAgYXR0YWNobWVudHM6IHRhc2suYXR0YWNobWVudHMgfHwgZXhpc3RpbmdUYXNrLmF0dGFjaG1lbnRzIHx8IFtdXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXNrcy51cGRhdGVBc3luYyhcbiAgICAgICAgeyBfaWQ6IHRhc2tJZCB9LFxuICAgICAgICB7ICRzZXQ6IHRhc2tUb1VwZGF0ZSB9XG4gICAgICApO1xuXG4gICAgICBpZiAocmVzdWx0ID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VwZGF0ZS1mYWlsZWQnLCAnRmFpbGVkIHRvIHVwZGF0ZSB0YXNrJyk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHRhc2s6JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgTWV0ZW9yLkVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignc2VydmVyLWVycm9yJywgZXJyb3IubWVzc2FnZSB8fCAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcpO1xuICAgIH1cbiAgfSxcblxuICBhc3luYyAndGFza3MuZGVsZXRlJyh0YXNrSWQpIHtcbiAgICBjaGVjayh0YXNrSWQsIFN0cmluZyk7XG5cbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdOb3QgYXV0aG9yaXplZC4nKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGFza3MucmVtb3ZlQXN5bmModGFza0lkKTtcbiAgICAgIFxuICAgICAgaWYgKHJlc3VsdCA9PT0gMCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtZm91bmQnLCAnVGFzayBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgdGFzazonLCBlcnJvcik7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd0YXNrLWRlbGV0ZS1mYWlsZWQnLCBlcnJvci5tZXNzYWdlKTtcbiAgICB9XG4gIH0sXG5cbiAgJ3Rhc2tzLnVwZGF0ZVByb2dyZXNzJyh0YXNrSWQsIHByb2dyZXNzKSB7XG4gICAgY2hlY2sodGFza0lkLCBTdHJpbmcpO1xuICAgIGNoZWNrKHByb2dyZXNzLCBOdW1iZXIpO1xuXG4gICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignTm90IGF1dGhvcml6ZWQuJyk7XG4gICAgfVxuXG4gICAgY29uc3QgdGFzayA9IFRhc2tzLmZpbmRPbmUodGFza0lkKTtcbiAgICBpZiAoIXRhc2spIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ1Rhc2sgbm90IGZvdW5kLicpO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYXNzaWduZWQgdG8gdGhlIHRhc2tcbiAgICBpZiAoIXRhc2suYXNzaWduZWRUby5pbmNsdWRlcyh0aGlzLnVzZXJJZCkpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ05vdCBhdXRob3JpemVkIHRvIG1vZGlmeSB0aGlzIHRhc2suJyk7XG4gICAgfVxuXG4gICAgLy8gVXBkYXRlIHRhc2sgc3RhdHVzIGJhc2VkIG9uIHByb2dyZXNzXG4gICAgbGV0IHN0YXR1cyA9IHRhc2suc3RhdHVzO1xuICAgIGlmIChwcm9ncmVzcyA9PT0gMTAwKSB7XG4gICAgICBzdGF0dXMgPSAnY29tcGxldGVkJztcbiAgICB9IGVsc2UgaWYgKHByb2dyZXNzID4gMCkge1xuICAgICAgc3RhdHVzID0gJ2luLXByb2dyZXNzJztcbiAgICB9XG5cbiAgICByZXR1cm4gVGFza3MudXBkYXRlKHRhc2tJZCwge1xuICAgICAgJHNldDoge1xuICAgICAgICBwcm9ncmVzcyxcbiAgICAgICAgc3RhdHVzLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgIHVwZGF0ZWRCeTogdGhpcy51c2VySWRcbiAgICAgIH1cbiAgICB9KTtcbiAgfSxcblxuICBhc3luYyAndGFza3MudG9nZ2xlQ2hlY2tsaXN0SXRlbScodGFza0lkLCBpdGVtSW5kZXgpIHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ1N0YXJ0aW5nIHRvZ2dsZUNoZWNrbGlzdEl0ZW0gd2l0aDonLCB7IHRhc2tJZCwgaXRlbUluZGV4LCB1c2VySWQ6IHRoaXMudXNlcklkIH0pO1xuXG4gICAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdObyB1c2VyIElEIGZvdW5kJyk7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBtdXN0IGJlIGxvZ2dlZCBpbiB0byBwZXJmb3JtIHRoaXMgYWN0aW9uJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFZhbGlkYXRlIGlucHV0c1xuICAgICAgaWYgKCF0YXNrSWQgfHwgdHlwZW9mIHRhc2tJZCAhPT0gJ3N0cmluZycpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0ludmFsaWQgdGFza0lkOicsIHRhc2tJZCk7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2ludmFsaWQtaW5wdXQnLCAnSW52YWxpZCB0YXNrIElEJyk7XG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgaXRlbUluZGV4ICE9PSAnbnVtYmVyJyB8fCBpdGVtSW5kZXggPCAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdJbnZhbGlkIGl0ZW1JbmRleDonLCBpdGVtSW5kZXgpO1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdpbnZhbGlkLWlucHV0JywgJ0ludmFsaWQgY2hlY2tsaXN0IGl0ZW0gaW5kZXgnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgY29uc29sZS5sb2coJ0ZvdW5kIHRhc2s6JywgdGFzayk7XG5cbiAgICAgIGlmICghdGFzaykge1xuICAgICAgICBjb25zb2xlLmxvZygnVGFzayBub3QgZm91bmQnKTtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWZvdW5kJywgJ1Rhc2sgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYXNzaWduZWQgdG8gdGhlIHRhc2sgb3IgaXMgYWRtaW5cbiAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdGb3VuZCB1c2VyOicsIHVzZXIpO1xuICAgICAgXG4gICAgICBjb25zdCBpc0FkbWluID0gdXNlcj8ucm9sZXM/LmluY2x1ZGVzKCdhZG1pbicpO1xuICAgICAgY29uc29sZS5sb2coJ0lzIGFkbWluOicsIGlzQWRtaW4pO1xuICAgICAgXG4gICAgICBpZiAoIWlzQWRtaW4gJiYgIXRhc2suYXNzaWduZWRUby5pbmNsdWRlcyh0aGlzLnVzZXJJZCkpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgbm90IGF1dGhvcml6ZWQ6JywgeyB1c2VySWQ6IHRoaXMudXNlcklkLCBhc3NpZ25lZFRvOiB0YXNrLmFzc2lnbmVkVG8gfSk7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBhcmUgbm90IGF1dGhvcml6ZWQgdG8gbW9kaWZ5IHRoaXMgdGFzaycpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBjaGVja2xpc3QgPSB0YXNrLmNoZWNrbGlzdCB8fCBbXTtcbiAgICAgIGNvbnNvbGUubG9nKCdDdXJyZW50IGNoZWNrbGlzdDonLCBjaGVja2xpc3QpO1xuXG4gICAgICBpZiAoaXRlbUluZGV4ID49IGNoZWNrbGlzdC5sZW5ndGgpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0ludmFsaWQgaXRlbSBpbmRleDonLCB7IGl0ZW1JbmRleCwgY2hlY2tsaXN0TGVuZ3RoOiBjaGVja2xpc3QubGVuZ3RoIH0pO1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdpbnZhbGlkLWluZGV4JywgJ0ludmFsaWQgY2hlY2tsaXN0IGl0ZW0gaW5kZXgnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIGEgbmV3IGFycmF5IHRvIGVuc3VyZSByZWFjdGl2aXR5XG4gICAgICBjb25zdCB1cGRhdGVkQ2hlY2tsaXN0ID0gWy4uLmNoZWNrbGlzdF07XG4gICAgICB1cGRhdGVkQ2hlY2tsaXN0W2l0ZW1JbmRleF0gPSB7XG4gICAgICAgIC4uLnVwZGF0ZWRDaGVja2xpc3RbaXRlbUluZGV4XSxcbiAgICAgICAgY29tcGxldGVkOiAhdXBkYXRlZENoZWNrbGlzdFtpdGVtSW5kZXhdLmNvbXBsZXRlZFxuICAgICAgfTtcblxuICAgICAgY29uc29sZS5sb2coJ1VwZGF0ZWQgY2hlY2tsaXN0OicsIHVwZGF0ZWRDaGVja2xpc3QpO1xuXG4gICAgICAvLyBDYWxjdWxhdGUgcHJvZ3Jlc3NcbiAgICAgIGNvbnN0IGNvbXBsZXRlZEl0ZW1zID0gdXBkYXRlZENoZWNrbGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmNvbXBsZXRlZCkubGVuZ3RoO1xuICAgICAgY29uc3QgdG90YWxJdGVtcyA9IHVwZGF0ZWRDaGVja2xpc3QubGVuZ3RoO1xuICAgICAgY29uc3QgcHJvZ3Jlc3MgPSB0b3RhbEl0ZW1zID4gMCA/IE1hdGgucm91bmQoKGNvbXBsZXRlZEl0ZW1zIC8gdG90YWxJdGVtcykgKiAxMDApIDogMDtcblxuICAgICAgLy8gVXBkYXRlIHRhc2sgc3RhdHVzIGJhc2VkIG9uIHByb2dyZXNzXG4gICAgICBsZXQgc3RhdHVzO1xuICAgICAgaWYgKHByb2dyZXNzID09PSAxMDApIHtcbiAgICAgICAgc3RhdHVzID0gJ2NvbXBsZXRlZCc7XG4gICAgICB9IGVsc2UgaWYgKHByb2dyZXNzID4gMCkge1xuICAgICAgICBzdGF0dXMgPSAnaW4tcHJvZ3Jlc3MnO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc3RhdHVzID0gJ3BlbmRpbmcnO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnVXBkYXRpbmcgdGFzayB3aXRoOicsIHtcbiAgICAgICAgdGFza0lkLFxuICAgICAgICB1cGRhdGVkQ2hlY2tsaXN0LFxuICAgICAgICBwcm9ncmVzcyxcbiAgICAgICAgc3RhdHVzXG4gICAgICB9KTtcblxuICAgICAgLy8gRmlyc3QgdmVyaWZ5IHRoZSB0YXNrIHN0aWxsIGV4aXN0c1xuICAgICAgY29uc3QgZXhpc3RpbmdUYXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgICBpZiAoIWV4aXN0aW5nVGFzaykge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd0YXNrLW5vdC1mb3VuZCcsICdUYXNrIG5vIGxvbmdlciBleGlzdHMnKTtcbiAgICAgIH1cblxuICAgICAgLy8gUGVyZm9ybSB0aGUgdXBkYXRlXG4gICAgICBjb25zdCB1cGRhdGVSZXN1bHQgPSBhd2FpdCBUYXNrcy51cGRhdGVBc3luYyhcbiAgICAgICAgeyBfaWQ6IHRhc2tJZCB9LFxuICAgICAgICB7XG4gICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgY2hlY2tsaXN0OiB1cGRhdGVkQ2hlY2tsaXN0LFxuICAgICAgICAgICAgcHJvZ3Jlc3MsXG4gICAgICAgICAgICBzdGF0dXMsXG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICB1cGRhdGVkQnk6IHRoaXMudXNlcklkXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICBjb25zb2xlLmxvZygnVXBkYXRlIHJlc3VsdDonLCB1cGRhdGVSZXN1bHQpO1xuXG4gICAgICBpZiAodXBkYXRlUmVzdWx0ID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VwZGF0ZS1mYWlsZWQnLCAnRmFpbGVkIHRvIHVwZGF0ZSB0YXNrJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFZlcmlmeSB0aGUgdXBkYXRlXG4gICAgICBjb25zdCB1cGRhdGVkVGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgY29uc29sZS5sb2coJ1Rhc2sgYWZ0ZXIgdXBkYXRlOicsIHVwZGF0ZWRUYXNrKTtcblxuICAgICAgcmV0dXJuIHVwZGF0ZVJlc3VsdDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gdG9nZ2xlQ2hlY2tsaXN0SXRlbTonLCBlcnJvcik7XG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBNZXRlb3IuRXJyb3IpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdzZXJ2ZXItZXJyb3InLCBlcnJvci5tZXNzYWdlIHx8ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyk7XG4gICAgfVxuICB9LFxuXG4gIGFzeW5jICd0YXNrcy5hZGRBdHRhY2htZW50Jyh0YXNrSWQsIGZpbGVEYXRhKSB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWF1dGhvcml6ZWQnLCAnWW91IG11c3QgYmUgbG9nZ2VkIGluIHRvIHBlcmZvcm0gdGhpcyBhY3Rpb24nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgaWYgKCF0YXNrKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1mb3VuZCcsICdUYXNrIG5vdCBmb3VuZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFzc2lnbmVkIHRvIHRoZSB0YXNrXG4gICAgICBpZiAoIXRhc2suYXNzaWduZWRUby5pbmNsdWRlcyh0aGlzLnVzZXJJZCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWF1dGhvcml6ZWQnLCAnWW91IGFyZSBub3QgYXV0aG9yaXplZCB0byBhZGQgYXR0YWNobWVudHMgdG8gdGhpcyB0YXNrJyk7XG4gICAgICB9XG5cbiAgICAgIGlmICghZmlsZURhdGEgfHwgIWZpbGVEYXRhLm5hbWUgfHwgIWZpbGVEYXRhLmRhdGEpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignaW52YWxpZC1pbnB1dCcsICdJbnZhbGlkIGZpbGUgZGF0YScpO1xuICAgICAgfVxuXG4gICAgICAvLyBFbnN1cmUgd2UncmUgc3RvcmluZyB0aGUgdXNlciBJRCBhcyBhIHN0cmluZ1xuICAgICAgY29uc3QgdXBsb2FkZXJJZCA9IFN0cmluZyh0aGlzLnVzZXJJZCk7XG5cbiAgICAgIC8vIEFkZCB0aGUgZmlsZSBkYXRhIHRvIGF0dGFjaG1lbnRzIHdpdGggdXBsb2FkZXIgaW5mb1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGFza3MudXBkYXRlQXN5bmMoXG4gICAgICAgIHsgX2lkOiB0YXNrSWQgfSxcbiAgICAgICAge1xuICAgICAgICAgICRwdXNoOiB7XG4gICAgICAgICAgICBhdHRhY2htZW50czoge1xuICAgICAgICAgICAgICBuYW1lOiBmaWxlRGF0YS5uYW1lLFxuICAgICAgICAgICAgICB0eXBlOiBmaWxlRGF0YS50eXBlLFxuICAgICAgICAgICAgICBkYXRhOiBmaWxlRGF0YS5kYXRhLFxuICAgICAgICAgICAgICB1cGxvYWRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgICB1cGxvYWRlZEJ5OiB1cGxvYWRlcklkXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICAkc2V0OiB7XG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICB1cGRhdGVkQnk6IHVwbG9hZGVySWRcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICk7XG5cbiAgICAgIGlmIChyZXN1bHQgPT09IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndXBkYXRlLWZhaWxlZCcsICdGYWlsZWQgdG8gYWRkIGF0dGFjaG1lbnQnKTtcbiAgICAgIH1cblxuICAgICAgLy8gR2V0IGFuZCByZXR1cm4gdGhlIHVwZGF0ZWQgdGFza1xuICAgICAgY29uc3QgdXBkYXRlZFRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdUYXNrIGFmdGVyIGFkZGluZyBhdHRhY2htZW50OicsIHVwZGF0ZWRUYXNrKTtcbiAgICAgIHJldHVybiB1cGRhdGVkVGFzaztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIGF0dGFjaG1lbnQ6JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgTWV0ZW9yLkVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignc2VydmVyLWVycm9yJywgZXJyb3IubWVzc2FnZSB8fCAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcpO1xuICAgIH1cbiAgfSxcblxuICBhc3luYyAndGFza3MuYWRkTGluaycodGFza0lkLCBsaW5rKSB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWF1dGhvcml6ZWQnLCAnWW91IG11c3QgYmUgbG9nZ2VkIGluIHRvIHBlcmZvcm0gdGhpcyBhY3Rpb24nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgaWYgKCF0YXNrKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1mb3VuZCcsICdUYXNrIG5vdCBmb3VuZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFzc2lnbmVkIHRvIHRoZSB0YXNrXG4gICAgICBpZiAoIXRhc2suYXNzaWduZWRUby5pbmNsdWRlcyh0aGlzLnVzZXJJZCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWF1dGhvcml6ZWQnLCAnWW91IGFyZSBub3QgYXV0aG9yaXplZCB0byBhZGQgbGlua3MgdG8gdGhpcyB0YXNrJyk7XG4gICAgICB9XG5cbiAgICAgIGlmICghbGluaykge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdpbnZhbGlkLWlucHV0JywgJ0xpbmsgVVJMIGlzIHJlcXVpcmVkJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFZhbGlkYXRlIFVSTCBmb3JtYXRcbiAgICAgIHRyeSB7XG4gICAgICAgIG5ldyBVUkwobGluayk7XG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2ludmFsaWQtaW5wdXQnLCAnSW52YWxpZCBVUkwgZm9ybWF0Jyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCB0aGUgbGluayB0byBsaW5rcyBhcnJheSB3aXRoIGFkZGVyIGluZm9cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhc2tzLnVwZGF0ZUFzeW5jKFxuICAgICAgICB7IF9pZDogdGFza0lkIH0sXG4gICAgICAgIHtcbiAgICAgICAgICAkcHVzaDoge1xuICAgICAgICAgICAgbGlua3M6IHtcbiAgICAgICAgICAgICAgdXJsOiBsaW5rLFxuICAgICAgICAgICAgICBhZGRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgICBhZGRlZEJ5OiBTdHJpbmcodGhpcy51c2VySWQpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICAkc2V0OiB7XG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICB1cGRhdGVkQnk6IFN0cmluZyh0aGlzLnVzZXJJZClcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICk7XG5cbiAgICAgIGlmIChyZXN1bHQgPT09IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndXBkYXRlLWZhaWxlZCcsICdGYWlsZWQgdG8gYWRkIGxpbmsnKTtcbiAgICAgIH1cblxuICAgICAgLy8gR2V0IGFuZCByZXR1cm4gdGhlIHVwZGF0ZWQgdGFza1xuICAgICAgY29uc3QgdXBkYXRlZFRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdUYXNrIGFmdGVyIGFkZGluZyBsaW5rOicsIHVwZGF0ZWRUYXNrKTtcbiAgICAgIHJldHVybiB1cGRhdGVkVGFzaztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIGxpbms6JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgTWV0ZW9yLkVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignc2VydmVyLWVycm9yJywgZXJyb3IubWVzc2FnZSB8fCAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcpO1xuICAgIH1cbiAgfSxcblxuICBhc3luYyAndGFza3MucmVtb3ZlQXR0YWNobWVudCcodGFza0lkLCBhdHRhY2htZW50SW5kZXgpIHtcbiAgICB0cnkge1xuICAgICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4gdG8gcGVyZm9ybSB0aGlzIGFjdGlvbicpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB0YXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgICBpZiAoIXRhc2spIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWZvdW5kJywgJ1Rhc2sgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGlmIHRoZSBhdHRhY2htZW50IGV4aXN0c1xuICAgICAgaWYgKCF0YXNrLmF0dGFjaG1lbnRzIHx8ICF0YXNrLmF0dGFjaG1lbnRzW2F0dGFjaG1lbnRJbmRleF0pIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWZvdW5kJywgJ0F0dGFjaG1lbnQgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGF0dGFjaG1lbnQgPSB0YXNrLmF0dGFjaG1lbnRzW2F0dGFjaG1lbnRJbmRleF07XG4gICAgICBcbiAgICAgIC8vIENvbnZlcnQgYm90aCBJRHMgdG8gc3RyaW5ncyBmb3IgY29tcGFyaXNvblxuICAgICAgY29uc3QgY3VycmVudFVzZXJJZCA9IFN0cmluZyh0aGlzLnVzZXJJZCk7XG4gICAgICBjb25zdCB1cGxvYWRlZEJ5SWQgPSBTdHJpbmcoYXR0YWNobWVudC51cGxvYWRlZEJ5KTtcbiAgICAgIFxuICAgICAgLy8gT25seSBhbGxvdyB0aGUgdXBsb2FkZXIgdG8gcmVtb3ZlIHRoZSBhdHRhY2htZW50XG4gICAgICBpZiAoY3VycmVudFVzZXJJZCAhPT0gdXBsb2FkZWRCeUlkKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBjYW4gb25seSByZW1vdmUgeW91ciBvd24gYXR0YWNobWVudHMnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIGEgbmV3IGFycmF5IHdpdGhvdXQgdGhlIHNwZWNpZmllZCBhdHRhY2htZW50XG4gICAgICBjb25zdCB1cGRhdGVkQXR0YWNobWVudHMgPSBbLi4udGFzay5hdHRhY2htZW50c107XG4gICAgICB1cGRhdGVkQXR0YWNobWVudHMuc3BsaWNlKGF0dGFjaG1lbnRJbmRleCwgMSk7XG5cbiAgICAgIC8vIFVwZGF0ZSB0aGUgdGFzayB3aXRoIHRoZSBuZXcgYXR0YWNobWVudHMgYXJyYXlcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhc2tzLnVwZGF0ZUFzeW5jKFxuICAgICAgICB7IF9pZDogdGFza0lkIH0sXG4gICAgICAgIHtcbiAgICAgICAgICAkc2V0OiB7XG4gICAgICAgICAgICBhdHRhY2htZW50czogdXBkYXRlZEF0dGFjaG1lbnRzLFxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgdXBkYXRlZEJ5OiBjdXJyZW50VXNlcklkXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICBpZiAocmVzdWx0ID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VwZGF0ZS1mYWlsZWQnLCAnRmFpbGVkIHRvIHJlbW92ZSBhdHRhY2htZW50Jyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCBhbmQgcmV0dXJuIHRoZSB1cGRhdGVkIHRhc2tcbiAgICAgIGNvbnN0IHVwZGF0ZWRUYXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgICBjb25zb2xlLmxvZygnVGFzayBhZnRlciByZW1vdmluZyBhdHRhY2htZW50OicsIHVwZGF0ZWRUYXNrKTtcbiAgICAgIHJldHVybiB1cGRhdGVkVGFzaztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVtb3ZpbmcgYXR0YWNobWVudDonLCBlcnJvcik7XG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBNZXRlb3IuRXJyb3IpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdzZXJ2ZXItZXJyb3InLCBlcnJvci5tZXNzYWdlIHx8ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyk7XG4gICAgfVxuICB9LFxuXG4gIGFzeW5jICd0YXNrcy5yZW1vdmVMaW5rJyh0YXNrSWQsIGxpbmtJbmRleCkge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBtdXN0IGJlIGxvZ2dlZCBpbiB0byBwZXJmb3JtIHRoaXMgYWN0aW9uJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGlmICghdGFzaykge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtZm91bmQnLCAnVGFzayBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgdGhlIGxpbmsgZXhpc3RzXG4gICAgICBpZiAoIXRhc2subGlua3MgfHwgIXRhc2subGlua3NbbGlua0luZGV4XSkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtZm91bmQnLCAnTGluayBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgbGluayA9IHRhc2subGlua3NbbGlua0luZGV4XTtcbiAgICAgIFxuICAgICAgLy8gQ29udmVydCBib3RoIElEcyB0byBzdHJpbmdzIGZvciBjb21wYXJpc29uXG4gICAgICBjb25zdCBjdXJyZW50VXNlcklkID0gU3RyaW5nKHRoaXMudXNlcklkKTtcbiAgICAgIGNvbnN0IGFkZGVkQnlJZCA9IFN0cmluZyhsaW5rLmFkZGVkQnkpO1xuICAgICAgXG4gICAgICAvLyBPbmx5IGFsbG93IHRoZSB1c2VyIHdobyBhZGRlZCB0aGUgbGluayB0byByZW1vdmUgaXRcbiAgICAgIGlmIChjdXJyZW50VXNlcklkICE9PSBhZGRlZEJ5SWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWF1dGhvcml6ZWQnLCAnWW91IGNhbiBvbmx5IHJlbW92ZSB5b3VyIG93biBsaW5rcycpO1xuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGUgYSBuZXcgYXJyYXkgd2l0aG91dCB0aGUgc3BlY2lmaWVkIGxpbmtcbiAgICAgIGNvbnN0IHVwZGF0ZWRMaW5rcyA9IFsuLi50YXNrLmxpbmtzXTtcbiAgICAgIHVwZGF0ZWRMaW5rcy5zcGxpY2UobGlua0luZGV4LCAxKTtcblxuICAgICAgLy8gVXBkYXRlIHRoZSB0YXNrIHdpdGggdGhlIG5ldyBsaW5rcyBhcnJheVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGFza3MudXBkYXRlQXN5bmMoXG4gICAgICAgIHsgX2lkOiB0YXNrSWQgfSxcbiAgICAgICAge1xuICAgICAgICAgICRzZXQ6IHtcbiAgICAgICAgICAgIGxpbmtzOiB1cGRhdGVkTGlua3MsXG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICB1cGRhdGVkQnk6IGN1cnJlbnRVc2VySWRcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICk7XG5cbiAgICAgIGlmIChyZXN1bHQgPT09IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndXBkYXRlLWZhaWxlZCcsICdGYWlsZWQgdG8gcmVtb3ZlIGxpbmsnKTtcbiAgICAgIH1cblxuICAgICAgLy8gR2V0IGFuZCByZXR1cm4gdGhlIHVwZGF0ZWQgdGFza1xuICAgICAgY29uc3QgdXBkYXRlZFRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdUYXNrIGFmdGVyIHJlbW92aW5nIGxpbms6JywgdXBkYXRlZFRhc2spO1xuICAgICAgcmV0dXJuIHVwZGF0ZWRUYXNrO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZW1vdmluZyBsaW5rOicsIGVycm9yKTtcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIE1ldGVvci5FcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3NlcnZlci1lcnJvcicsIGVycm9yLm1lc3NhZ2UgfHwgJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnKTtcbiAgICB9XG4gIH0sXG5cbiAgYXN5bmMgJ3Rhc2tzLmZpbmRPbmUnKHRhc2tJZCkge1xuICAgIGNoZWNrKHRhc2tJZCwgU3RyaW5nKTtcbiAgICBcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdOb3QgYXV0aG9yaXplZC4nKTtcbiAgICB9XG5cbiAgICBjb25zdCB0YXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgaWYgKCF0YXNrKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd0YXNrLW5vdC1mb3VuZCcsICdUYXNrIG5vdCBmb3VuZCcpO1xuICAgIH1cblxuICAgIHJldHVybiB0YXNrO1xuICB9LFxufSk7ICIsImltcG9ydCB7IE1ldGVvciB9IGZyb20gJ21ldGVvci9tZXRlb3InO1xuaW1wb3J0IHsgTGlua3NDb2xsZWN0aW9uIH0gZnJvbSAnL2ltcG9ydHMvYXBpL2xpbmtzJztcbmltcG9ydCB7IEFjY291bnRzIH0gZnJvbSAnbWV0ZW9yL2FjY291bnRzLWJhc2UnO1xuaW1wb3J0IHsgRW1haWwgfSBmcm9tICdtZXRlb3IvZW1haWwnO1xuaW1wb3J0IHsgVGFza3MgfSBmcm9tICcvaW1wb3J0cy9hcGkvdGFza3MnO1xuaW1wb3J0IHsgUm9sZXMgfSBmcm9tICdtZXRlb3IvYWxhbm5pbmc6cm9sZXMnO1xuaW1wb3J0IHsgY2hlY2sgfSBmcm9tICdtZXRlb3IvY2hlY2snO1xuaW1wb3J0IGJjcnlwdCBmcm9tICdiY3J5cHQnO1xuXG5hc3luYyBmdW5jdGlvbiBpbnNlcnRMaW5rKHsgdGl0bGUsIHVybCB9KSB7XG4gIGF3YWl0IExpbmtzQ29sbGVjdGlvbi5pbnNlcnRBc3luYyh7IHRpdGxlLCB1cmwsIGNyZWF0ZWRBdDogbmV3IERhdGUoKSB9KTtcbn1cblxuY29uc3QgQURNSU5fVE9LRU4gPSAnMTIzNDU2JztcblxuTWV0ZW9yLnN0YXJ0dXAoYXN5bmMgKCkgPT4ge1xuICAvLyBFbnN1cmUgaW5kZXhlcyBmb3IgVGFza3MgY29sbGVjdGlvblxuICB0cnkge1xuICAgIGF3YWl0IFRhc2tzLmNyZWF0ZUluZGV4KHsgY3JlYXRlZEF0OiAxIH0pO1xuICAgIGF3YWl0IFRhc2tzLmNyZWF0ZUluZGV4KHsgYXNzaWduZWRUbzogMSB9KTtcbiAgICBhd2FpdCBUYXNrcy5jcmVhdGVJbmRleCh7IGNyZWF0ZWRCeTogMSB9KTtcblxuICAgIC8vIEVuc3VyZSBpbmRleGVzIGZvciBVc2VycyBjb2xsZWN0aW9uXG4gICAgLy8gTm90ZTogZW1haWxzLmFkZHJlc3MgaW5kZXggaXMgYWxyZWFkeSBjcmVhdGVkIGJ5IE1ldGVvciBhY2NvdW50cyBzeXN0ZW1cbiAgICBhd2FpdCBNZXRlb3IudXNlcnMuY3JlYXRlSW5kZXgoeyByb2xlczogMSB9LCB7IGJhY2tncm91bmQ6IHRydWUgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS53YXJuKCdbU3RhcnR1cF0gSW5kZXggY3JlYXRpb24gd2FybmluZzonLCBlcnJvci5tZXNzYWdlKTtcbiAgfVxuXG4gIC8vIENoZWNrIGlmIHdlIGhhdmUgYW55IHRlYW0gbWVtYmVyc1xuICB0cnkge1xuICAgIGNvbnN0IGFsbFVzZXJzID0gYXdhaXQgTWV0ZW9yLnVzZXJzLmZpbmQoKS5mZXRjaEFzeW5jKCk7XG4gICAgY29uc29sZS5sb2coJ1tTdGFydHVwXSBBbGwgdXNlcnM6JywgYWxsVXNlcnMubWFwKHVzZXIgPT4gKHtcbiAgICAgIGlkOiB1c2VyLl9pZCxcbiAgICAgIGVtYWlsOiB1c2VyLmVtYWlscz8uWzBdPy5hZGRyZXNzLFxuICAgICAgcm9sZXM6IHVzZXIucm9sZXMsXG4gICAgICBwcm9maWxlOiB1c2VyLnByb2ZpbGVcbiAgICB9KSkpO1xuXG4gICAgLy8gRmlyc3QsIGVuc3VyZSBhbGwgdXNlcnMgaGF2ZSByb2xlcyBhbmQgY3JlYXRlZEF0XG4gICAgZm9yIChjb25zdCB1c2VyIG9mIGFsbFVzZXJzKSB7XG4gICAgICBjb25zdCB1cGRhdGVzID0ge307XG4gICAgICBcbiAgICAgIGlmICghdXNlci5yb2xlcyB8fCAhQXJyYXkuaXNBcnJheSh1c2VyLnJvbGVzKSkge1xuICAgICAgICB1cGRhdGVzLnJvbGVzID0gWyd0ZWFtLW1lbWJlciddO1xuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAoIXVzZXIuY3JlYXRlZEF0KSB7XG4gICAgICAgIHVwZGF0ZXMuY3JlYXRlZEF0ID0gbmV3IERhdGUoKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKE9iamVjdC5rZXlzKHVwZGF0ZXMpLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1tTdGFydHVwXSBGaXhpbmcgbWlzc2luZyBmaWVsZHMgZm9yIHVzZXI6JywgdXNlci5lbWFpbHM/LlswXT8uYWRkcmVzcyk7XG4gICAgICAgIGF3YWl0IE1ldGVvci51c2Vycy51cGRhdGVBc3luYyh1c2VyLl9pZCwge1xuICAgICAgICAgICRzZXQ6IHVwZGF0ZXNcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgdGVhbU1lbWJlcnNDb3VudCA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kKHsgJ3JvbGVzJzogJ3RlYW0tbWVtYmVyJyB9KS5jb3VudEFzeW5jKCk7XG4gICAgY29uc29sZS5sb2coJ1tTdGFydHVwXSBGb3VuZCB0ZWFtIG1lbWJlcnM6JywgdGVhbU1lbWJlcnNDb3VudCk7XG5cbiAgICAvLyBDcmVhdGUgdGVzdCB0ZWFtIG1lbWJlcnMgaWYgbm9uZSBleGlzdFxuICAgIGlmICh0ZWFtTWVtYmVyc0NvdW50ID09PSAwKSB7XG4gICAgICBjb25zb2xlLmxvZygnW1N0YXJ0dXBdIENyZWF0aW5nIHRlc3QgdGVhbSBtZW1iZXJzJyk7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBDcmVhdGUgbXVsdGlwbGUgdGVzdCB0ZWFtIG1lbWJlcnNcbiAgICAgICAgY29uc3QgdGVzdE1lbWJlcnMgPSBbXG4gICAgICAgICAge1xuICAgICAgICAgICAgZW1haWw6ICd0ZWFtMUBleGFtcGxlLmNvbScsXG4gICAgICAgICAgICBwYXNzd29yZDogJ1RlYW1QYXNzMTIzIScsXG4gICAgICAgICAgICBmaXJzdE5hbWU6ICdKb2huJyxcbiAgICAgICAgICAgIGxhc3ROYW1lOiAnRG9lJ1xuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgZW1haWw6ICd0ZWFtMkBleGFtcGxlLmNvbScsXG4gICAgICAgICAgICBwYXNzd29yZDogJ1RlYW1QYXNzMTIzIScsXG4gICAgICAgICAgICBmaXJzdE5hbWU6ICdKYW5lJyxcbiAgICAgICAgICAgIGxhc3ROYW1lOiAnU21pdGgnXG4gICAgICAgICAgfVxuICAgICAgICBdO1xuXG4gICAgICAgIGZvciAoY29uc3QgbWVtYmVyIG9mIHRlc3RNZW1iZXJzKSB7XG4gICAgICAgICAgY29uc3QgdXNlcklkID0gYXdhaXQgQWNjb3VudHMuY3JlYXRlVXNlckFzeW5jKHtcbiAgICAgICAgICAgIGVtYWlsOiBtZW1iZXIuZW1haWwsXG4gICAgICAgICAgICBwYXNzd29yZDogbWVtYmVyLnBhc3N3b3JkLFxuICAgICAgICAgICAgcm9sZTogJ3RlYW0tbWVtYmVyJyxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgIHByb2ZpbGU6IHtcbiAgICAgICAgICAgICAgZmlyc3ROYW1lOiBtZW1iZXIuZmlyc3ROYW1lLFxuICAgICAgICAgICAgICBsYXN0TmFtZTogbWVtYmVyLmxhc3ROYW1lLFxuICAgICAgICAgICAgICByb2xlOiAndGVhbS1tZW1iZXInLFxuICAgICAgICAgICAgICBmdWxsTmFtZTogYCR7bWVtYmVyLmZpcnN0TmFtZX0gJHttZW1iZXIubGFzdE5hbWV9YFxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgLy8gU2V0IHRoZSByb2xlIGV4cGxpY2l0bHlcbiAgICAgICAgICBhd2FpdCBNZXRlb3IudXNlcnMudXBkYXRlQXN5bmModXNlcklkLCB7XG4gICAgICAgICAgICAkc2V0OiB7IHJvbGVzOiBbJ3RlYW0tbWVtYmVyJ10gfVxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coJ1tTdGFydHVwXSBDcmVhdGVkIHRlc3QgdGVhbSBtZW1iZXI6Jywge1xuICAgICAgICAgICAgaWQ6IHVzZXJJZCxcbiAgICAgICAgICAgIGVtYWlsOiBtZW1iZXIuZW1haWwsXG4gICAgICAgICAgICBuYW1lOiBgJHttZW1iZXIuZmlyc3ROYW1lfSAke21lbWJlci5sYXN0TmFtZX1gXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tTdGFydHVwXSBFcnJvciBjcmVhdGluZyB0ZXN0IHRlYW0gbWVtYmVyczonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1tTdGFydHVwXSBFcnJvciBjaGVja2luZyB0ZWFtIG1lbWJlcnM6JywgZXJyb3IpO1xuICB9XG5cbiAgLy8gRW1haWwgY29uZmlndXJhdGlvbiBmcm9tIHNldHRpbmdzXG4gIGNvbnN0IGVtYWlsU2V0dGluZ3MgPSBNZXRlb3Iuc2V0dGluZ3MucHJpdmF0ZT8uZW1haWw7XG4gIFxuICAvLyBDb25maWd1cmUgZW1haWwgU01UUFxuICBpZiAoZW1haWxTZXR0aW5ncz8udXNlcm5hbWUgJiYgZW1haWxTZXR0aW5ncz8ucGFzc3dvcmQpIHtcbiAgICBwcm9jZXNzLmVudi5NQUlMX1VSTCA9IGBzbXRwOi8vJHtlbmNvZGVVUklDb21wb25lbnQoZW1haWxTZXR0aW5ncy51c2VybmFtZSl9OiR7ZW5jb2RlVVJJQ29tcG9uZW50KGVtYWlsU2V0dGluZ3MucGFzc3dvcmQpfUAke2VtYWlsU2V0dGluZ3Muc2VydmVyfToke2VtYWlsU2V0dGluZ3MucG9ydH1gO1xuICAgIFxuICAgIC8vIFRlc3QgZW1haWwgY29uZmlndXJhdGlvblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnVGVzdGluZyBlbWFpbCBjb25maWd1cmF0aW9uLi4uJyk7XG4gICAgICBFbWFpbC5zZW5kKHtcbiAgICAgICAgdG86IGVtYWlsU2V0dGluZ3MudXNlcm5hbWUsXG4gICAgICAgIGZyb206IGVtYWlsU2V0dGluZ3MudXNlcm5hbWUsXG4gICAgICAgIHN1YmplY3Q6ICdUZXN0IEVtYWlsJyxcbiAgICAgICAgdGV4dDogJ0lmIHlvdSByZWNlaXZlIHRoaXMgZW1haWwsIHlvdXIgZW1haWwgY29uZmlndXJhdGlvbiBpcyB3b3JraW5nIGNvcnJlY3RseS4nXG4gICAgICB9KTtcbiAgICAgIGNvbnNvbGUubG9nKCdUZXN0IGVtYWlsIHNlbnQgc3VjY2Vzc2Z1bGx5IScpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIHRlc3QgZW1haWw6JywgZXJyb3IpO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBjb25zb2xlLndhcm4oJ0VtYWlsIGNvbmZpZ3VyYXRpb24gaXMgbWlzc2luZyBpbiBzZXR0aW5ncy5qc29uJyk7XG4gIH1cblxuICAvLyBDb25maWd1cmUgYWNjb3VudCBjcmVhdGlvbiB0byByZXF1aXJlIGVtYWlsIHZlcmlmaWNhdGlvblxuICBBY2NvdW50cy5jb25maWcoe1xuICAgIHNlbmRWZXJpZmljYXRpb25FbWFpbDogdHJ1ZSxcbiAgICBmb3JiaWRDbGllbnRBY2NvdW50Q3JlYXRpb246IGZhbHNlXG4gIH0pO1xuXG4gIC8vIEN1c3RvbWl6ZSB2ZXJpZmljYXRpb24gZW1haWxcbiAgQWNjb3VudHMuZW1haWxUZW1wbGF0ZXMuc2l0ZU5hbWUgPSBcIlRhc2sgTWFuYWdlbWVudCBTeXN0ZW1cIjtcbiAgQWNjb3VudHMuZW1haWxUZW1wbGF0ZXMuZnJvbSA9IGVtYWlsU2V0dGluZ3M/LnVzZXJuYW1lID8gXG4gICAgYFRhc2sgTWFuYWdlbWVudCBTeXN0ZW0gPCR7ZW1haWxTZXR0aW5ncy51c2VybmFtZX0+YCA6IFxuICAgIFwiVGFzayBNYW5hZ2VtZW50IFN5c3RlbSA8bm9yZXBseUBleGFtcGxlLmNvbT5cIjtcblxuICBBY2NvdW50cy5lbWFpbFRlbXBsYXRlcy52ZXJpZnlFbWFpbCA9IHtcbiAgICBzdWJqZWN0KCkge1xuICAgICAgcmV0dXJuIFwiVmVyaWZ5IFlvdXIgRW1haWwgQWRkcmVzc1wiO1xuICAgIH0sXG4gICAgdGV4dCh1c2VyLCB1cmwpIHtcbiAgICAgIGNvbnN0IGVtYWlsQWRkcmVzcyA9IHVzZXIuZW1haWxzWzBdLmFkZHJlc3M7XG4gICAgICByZXR1cm4gYEhlbGxvLFxcblxcbmBcbiAgICAgICAgKyBgVG8gdmVyaWZ5IHlvdXIgZW1haWwgYWRkcmVzcyAoJHtlbWFpbEFkZHJlc3N9KSwgcGxlYXNlIGNsaWNrIHRoZSBsaW5rIGJlbG93OlxcblxcbmBcbiAgICAgICAgKyBgJHt1cmx9XFxuXFxuYFxuICAgICAgICArIGBJZiB5b3UgZGlkIG5vdCByZXF1ZXN0IHRoaXMgdmVyaWZpY2F0aW9uLCBwbGVhc2UgaWdub3JlIHRoaXMgZW1haWwuXFxuXFxuYFxuICAgICAgICArIGBUaGFua3MsXFxuYFxuICAgICAgICArIGBZb3VyIFRhc2sgTWFuYWdlbWVudCBTeXN0ZW0gVGVhbWA7XG4gICAgfSxcbiAgICBodG1sKHVzZXIsIHVybCkge1xuICAgICAgY29uc3QgZW1haWxBZGRyZXNzID0gdXNlci5lbWFpbHNbMF0uYWRkcmVzcztcbiAgICAgIHJldHVybiBgXG4gICAgICAgIDxodG1sPlxuICAgICAgICAgIDxib2R5IHN0eWxlPVwiZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOyBwYWRkaW5nOiAyMHB4OyBjb2xvcjogIzMzMztcIj5cbiAgICAgICAgICAgIDxoMiBzdHlsZT1cImNvbG9yOiAjMDA4NzVhO1wiPlZlcmlmeSBZb3VyIEVtYWlsIEFkZHJlc3M8L2gyPlxuICAgICAgICAgICAgPHA+SGVsbG8sPC9wPlxuICAgICAgICAgICAgPHA+VG8gdmVyaWZ5IHlvdXIgZW1haWwgYWRkcmVzcyAoJHtlbWFpbEFkZHJlc3N9KSwgcGxlYXNlIGNsaWNrIHRoZSBidXR0b24gYmVsb3c6PC9wPlxuICAgICAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW46IDIwcHggMDtcIj5cbiAgICAgICAgICAgICAgPGEgaHJlZj1cIiR7dXJsfVwiIFxuICAgICAgICAgICAgICAgICBzdHlsZT1cImJhY2tncm91bmQtY29sb3I6ICMwMDg3NWE7IFxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IHdoaXRlOyBcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDEycHggMjVweDsgXG4gICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7IFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1wiPlxuICAgICAgICAgICAgICAgIFZlcmlmeSBFbWFpbCBBZGRyZXNzXG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxwPklmIHlvdSBkaWQgbm90IHJlcXVlc3QgdGhpcyB2ZXJpZmljYXRpb24sIHBsZWFzZSBpZ25vcmUgdGhpcyBlbWFpbC48L3A+XG4gICAgICAgICAgICA8cD5UaGFua3MsPGJyPllvdXIgVGFzayBNYW5hZ2VtZW50IFN5c3RlbSBUZWFtPC9wPlxuICAgICAgICAgIDwvYm9keT5cbiAgICAgICAgPC9odG1sPlxuICAgICAgYDtcbiAgICB9XG4gIH07XG5cbiAgLy8gSWYgdGhlIExpbmtzIGNvbGxlY3Rpb24gaXMgZW1wdHksIGFkZCBzb21lIGRhdGEuXG4gIGlmIChhd2FpdCBMaW5rc0NvbGxlY3Rpb24uZmluZCgpLmNvdW50QXN5bmMoKSA9PT0gMCkge1xuICAgIGF3YWl0IGluc2VydExpbmsoe1xuICAgICAgdGl0bGU6ICdEbyB0aGUgVHV0b3JpYWwnLFxuICAgICAgdXJsOiAnaHR0cHM6Ly93d3cubWV0ZW9yLmNvbS90dXRvcmlhbHMvcmVhY3QvY3JlYXRpbmctYW4tYXBwJyxcbiAgICB9KTtcblxuICAgIGF3YWl0IGluc2VydExpbmsoe1xuICAgICAgdGl0bGU6ICdGb2xsb3cgdGhlIEd1aWRlJyxcbiAgICAgIHVybDogJ2h0dHBzOi8vZ3VpZGUubWV0ZW9yLmNvbScsXG4gICAgfSk7XG5cbiAgICBhd2FpdCBpbnNlcnRMaW5rKHtcbiAgICAgIHRpdGxlOiAnUmVhZCB0aGUgRG9jcycsXG4gICAgICB1cmw6ICdodHRwczovL2RvY3MubWV0ZW9yLmNvbScsXG4gICAgfSk7XG5cbiAgICBhd2FpdCBpbnNlcnRMaW5rKHtcbiAgICAgIHRpdGxlOiAnRGlzY3Vzc2lvbnMnLFxuICAgICAgdXJsOiAnaHR0cHM6Ly9mb3J1bXMubWV0ZW9yLmNvbScsXG4gICAgfSk7XG4gIH1cblxuICAvLyBXZSBwdWJsaXNoIHRoZSBlbnRpcmUgTGlua3MgY29sbGVjdGlvbiB0byBhbGwgY2xpZW50cy5cbiAgLy8gSW4gb3JkZXIgdG8gYmUgZmV0Y2hlZCBpbiByZWFsLXRpbWUgdG8gdGhlIGNsaWVudHNcbiAgTWV0ZW9yLnB1Ymxpc2goXCJsaW5rc1wiLCBmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIExpbmtzQ29sbGVjdGlvbi5maW5kKCk7XG4gIH0pO1xuXG4gIC8vIEFkZCBjdXN0b20gZmllbGRzIHRvIHVzZXJzXG4gIEFjY291bnRzLm9uQ3JlYXRlVXNlcigob3B0aW9ucywgdXNlcikgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdbb25DcmVhdGVVc2VyXSBDcmVhdGluZyB1c2VyIHdpdGggb3B0aW9uczonLCB7XG4gICAgICBlbWFpbDogb3B0aW9ucy5lbWFpbCxcbiAgICAgIHJvbGU6IG9wdGlvbnMucm9sZSxcbiAgICAgIHByb2ZpbGU6IG9wdGlvbnMucHJvZmlsZSxcbiAgICAgIGNyZWF0ZWRBdDogb3B0aW9ucy5jcmVhdGVkQXRcbiAgICB9KTtcblxuICAgIGNvbnN0IGN1c3RvbWl6ZWRVc2VyID0geyAuLi51c2VyIH07XG4gICAgXG4gICAgLy8gRW5zdXJlIHdlIGhhdmUgYSBwcm9maWxlXG4gICAgY3VzdG9taXplZFVzZXIucHJvZmlsZSA9IG9wdGlvbnMucHJvZmlsZSB8fCB7fTtcbiAgICBcbiAgICAvLyBBZGQgcm9sZSBmcm9tIG9wdGlvbnNcbiAgICBjb25zdCByb2xlID0gb3B0aW9ucy5yb2xlIHx8ICd0ZWFtLW1lbWJlcic7XG4gICAgY3VzdG9taXplZFVzZXIucm9sZXMgPSBbcm9sZV07XG4gICAgXG4gICAgLy8gU2V0IGNyZWF0ZWRBdCBpZiBwcm92aWRlZCwgb3RoZXJ3aXNlIHVzZSBjdXJyZW50IGRhdGVcbiAgICBjdXN0b21pemVkVXNlci5jcmVhdGVkQXQgPSBvcHRpb25zLmNyZWF0ZWRBdCB8fCBuZXcgRGF0ZSgpO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKCdbb25DcmVhdGVVc2VyXSBDcmVhdGVkIHVzZXI6Jywge1xuICAgICAgaWQ6IGN1c3RvbWl6ZWRVc2VyLl9pZCxcbiAgICAgIGVtYWlsOiBjdXN0b21pemVkVXNlci5lbWFpbHM/LlswXT8uYWRkcmVzcyxcbiAgICAgIHJvbGVzOiBjdXN0b21pemVkVXNlci5yb2xlcyxcbiAgICAgIHByb2ZpbGU6IGN1c3RvbWl6ZWRVc2VyLnByb2ZpbGUsXG4gICAgICBjcmVhdGVkQXQ6IGN1c3RvbWl6ZWRVc2VyLmNyZWF0ZWRBdFxuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiBjdXN0b21pemVkVXNlcjtcbiAgfSk7XG5cbiAgLy8gUHVibGlzaCB0ZWFtIG1lbWJlcnNcbiAgTWV0ZW9yLnB1Ymxpc2goJ3RlYW1NZW1iZXJzJywgZnVuY3Rpb24oKSB7XG4gICAgY29uc29sZS5sb2coJ1t0ZWFtTWVtYmVyc10gUHVibGljYXRpb24gY2FsbGVkLCB1c2VySWQ6JywgdGhpcy51c2VySWQpO1xuICAgIFxuICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdbdGVhbU1lbWJlcnNdIE5vIHVzZXJJZCwgcmV0dXJuaW5nIHJlYWR5Jyk7XG4gICAgICByZXR1cm4gdGhpcy5yZWFkeSgpO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBTaW1wbGUgcXVlcnkgdG8gZmluZCBhbGwgdGVhbSBtZW1iZXJzXG4gICAgICBjb25zdCB0ZWFtTWVtYmVycyA9IE1ldGVvci51c2Vycy5maW5kKFxuICAgICAgICB7IFxuICAgICAgICAgICRvcjogW1xuICAgICAgICAgICAgeyAncm9sZXMnOiAndGVhbS1tZW1iZXInIH0sXG4gICAgICAgICAgICB7ICdwcm9maWxlLnJvbGUnOiAndGVhbS1tZW1iZXInIH1cbiAgICAgICAgICBdXG4gICAgICAgIH0sXG4gICAgICAgIHsgXG4gICAgICAgICAgZmllbGRzOiB7IFxuICAgICAgICAgICAgZW1haWxzOiAxLCBcbiAgICAgICAgICAgIHJvbGVzOiAxLFxuICAgICAgICAgICAgJ3Byb2ZpbGUuZmlyc3ROYW1lJzogMSxcbiAgICAgICAgICAgICdwcm9maWxlLmxhc3ROYW1lJzogMSxcbiAgICAgICAgICAgICdwcm9maWxlLmZ1bGxOYW1lJzogMSxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogMVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgICAgY29uc29sZS5sb2coJ1t0ZWFtTWVtYmVyc10gUHVibGlzaGluZyB0ZWFtIG1lbWJlcnMnKTtcbiAgICAgIHJldHVybiB0ZWFtTWVtYmVycztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW3RlYW1NZW1iZXJzXSBFcnJvciBpbiBwdWJsaWNhdGlvbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gdGhpcy5yZWFkeSgpO1xuICAgIH1cbiAgfSk7XG5cbiAgLy8gUHVibGlzaCB1c2VyIGRhdGEgd2l0aCByb2xlc1xuICBNZXRlb3IucHVibGlzaCgndXNlckRhdGEnLCBmdW5jdGlvbigpIHtcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICByZXR1cm4gdGhpcy5yZWFkeSgpO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdbdXNlckRhdGFdIFB1Ymxpc2hpbmcgZGF0YSBmb3IgdXNlcjonLCB0aGlzLnVzZXJJZCk7XG4gICAgXG4gICAgcmV0dXJuIE1ldGVvci51c2Vycy5maW5kKFxuICAgICAgeyBfaWQ6IHRoaXMudXNlcklkIH0sXG4gICAgICB7IFxuICAgICAgICBmaWVsZHM6IHsgXG4gICAgICAgICAgcm9sZXM6IDEsIFxuICAgICAgICAgIGVtYWlsczogMSxcbiAgICAgICAgICBwcm9maWxlOiAxXG4gICAgICAgIH0gXG4gICAgICB9XG4gICAgKTtcbiAgfSk7XG59KTtcblxuLy8gTWV0aG9kIHRvIGNyZWF0ZSBhIG5ldyB1c2VyIHdpdGggcm9sZVxuTWV0ZW9yLm1ldGhvZHMoe1xuICAndXNlcnMuY3JlYXRlJyh7IGVtYWlsLCBwYXNzd29yZCwgcm9sZSwgYWRtaW5Ub2tlbiwgZmlyc3ROYW1lLCBsYXN0TmFtZSB9KSB7XG4gICAgLy8gVmFsaWRhdGUgYWRtaW4gdG9rZW4gaWYgdHJ5aW5nIHRvIGNyZWF0ZSBhZG1pbiBhY2NvdW50XG4gICAgaWYgKHJvbGUgPT09ICdhZG1pbicgJiYgYWRtaW5Ub2tlbiAhPT0gQURNSU5fVE9LRU4pIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2ludmFsaWQtYWRtaW4tdG9rZW4nLCAnSW52YWxpZCBhZG1pbiB0b2tlbiBwcm92aWRlZCcpO1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIHBhc3N3b3JkIHJlcXVpcmVtZW50c1xuICAgIGNvbnN0IHBhc3N3b3JkUmVnZXggPSB7XG4gICAgICBsZW5ndGg6IC8uezgsfS8sXG4gICAgICB1cHBlcmNhc2U6IC9bQS1aXS8sXG4gICAgICBudW1iZXI6IC9bMC05XS8sXG4gICAgICBzcGVjaWFsOiAvWyFAIyQlXiYqXS9cbiAgICB9O1xuXG4gICAgY29uc3QgcGFzc3dvcmRFcnJvcnMgPSBbXTtcbiAgICBpZiAoIXBhc3N3b3JkUmVnZXgubGVuZ3RoLnRlc3QocGFzc3dvcmQpKSB7XG4gICAgICBwYXNzd29yZEVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGJlIGF0IGxlYXN0IDggY2hhcmFjdGVycyBsb25nJyk7XG4gICAgfVxuICAgIGlmICghcGFzc3dvcmRSZWdleC51cHBlcmNhc2UudGVzdChwYXNzd29yZCkpIHtcbiAgICAgIHBhc3N3b3JkRXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgdXBwZXJjYXNlIGxldHRlcicpO1xuICAgIH1cbiAgICBpZiAoIXBhc3N3b3JkUmVnZXgubnVtYmVyLnRlc3QocGFzc3dvcmQpKSB7XG4gICAgICBwYXNzd29yZEVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIG51bWJlcicpO1xuICAgIH1cbiAgICBpZiAoIXBhc3N3b3JkUmVnZXguc3BlY2lhbC50ZXN0KHBhc3N3b3JkKSkge1xuICAgICAgcGFzc3dvcmRFcnJvcnMucHVzaCgnUGFzc3dvcmQgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSBzcGVjaWFsIGNoYXJhY3RlciAoIUAjJCVeJiopJyk7XG4gICAgfVxuXG4gICAgaWYgKHBhc3N3b3JkRXJyb3JzLmxlbmd0aCA+IDApIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2ludmFsaWQtcGFzc3dvcmQnLCBwYXNzd29yZEVycm9ycy5qb2luKCcsICcpKTtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgdGhlIHVzZXJcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcklkID0gQWNjb3VudHMuY3JlYXRlVXNlcih7XG4gICAgICAgIGVtYWlsLFxuICAgICAgICBwYXNzd29yZCxcbiAgICAgICAgcm9sZSwgLy8gVGhpcyB3aWxsIGJlIHVzZWQgaW4gb25DcmVhdGVVc2VyIGNhbGxiYWNrXG4gICAgICAgIHByb2ZpbGU6IHtcbiAgICAgICAgICByb2xlLCAvLyBTdG9yZSBpbiBwcm9maWxlIGFzIHdlbGwgZm9yIGVhc3kgYWNjZXNzXG4gICAgICAgICAgZmlyc3ROYW1lLFxuICAgICAgICAgIGxhc3ROYW1lLFxuICAgICAgICAgIGZ1bGxOYW1lOiBgJHtmaXJzdE5hbWV9ICR7bGFzdE5hbWV9YFxuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgLy8gU2VuZCB2ZXJpZmljYXRpb24gZW1haWxcbiAgICAgIGlmICh1c2VySWQpIHtcbiAgICAgICAgQWNjb3VudHMuc2VuZFZlcmlmaWNhdGlvbkVtYWlsKHVzZXJJZCk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB1c2VySWQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2NyZWF0ZS11c2VyLWZhaWxlZCcsIGVycm9yLm1lc3NhZ2UpO1xuICAgIH1cbiAgfSxcblxuICBhc3luYyAndXNlcnMuZ2V0Um9sZScoKSB7XG4gICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWF1dGhvcml6ZWQnLCAnVXNlciBtdXN0IGJlIGxvZ2dlZCBpbicpO1xuICAgIH1cbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlciA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kT25lQXN5bmModGhpcy51c2VySWQpO1xuICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VzZXItbm90LWZvdW5kJywgJ1VzZXIgbm90IGZvdW5kJyk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIENoZWNrIGJvdGggcm9sZXMgYXJyYXkgYW5kIHByb2ZpbGUgZm9yIHJvbGVcbiAgICAgIGNvbnN0IHJvbGUgPSB1c2VyLnJvbGVzPy5bMF0gfHwgdXNlci5wcm9maWxlPy5yb2xlIHx8ICd0ZWFtLW1lbWJlcic7XG4gICAgICByZXR1cm4gcm9sZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignZ2V0LXJvbGUtZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gICd1c2Vycy5yZXNlbmRWZXJpZmljYXRpb25FbWFpbCcoZW1haWwpIHtcbiAgICAvLyBGaW5kIHVzZXIgYnkgZW1haWxcbiAgICBjb25zdCB1c2VyID0gQWNjb3VudHMuZmluZFVzZXJCeUVtYWlsKGVtYWlsKTtcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VzZXItbm90LWZvdW5kJywgJ05vIHVzZXIgZm91bmQgd2l0aCB0aGlzIGVtYWlsIGFkZHJlc3MnKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiBlbWFpbCBpcyBhbHJlYWR5IHZlcmlmaWVkXG4gICAgY29uc3QgdXNlckVtYWlsID0gdXNlci5lbWFpbHNbMF07XG4gICAgaWYgKHVzZXJFbWFpbC52ZXJpZmllZCkge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignYWxyZWFkeS12ZXJpZmllZCcsICdUaGlzIGVtYWlsIGlzIGFscmVhZHkgdmVyaWZpZWQnKTtcbiAgICB9XG5cbiAgICAvLyBTZW5kIHZlcmlmaWNhdGlvbiBlbWFpbFxuICAgIHRyeSB7XG4gICAgICBBY2NvdW50cy5zZW5kVmVyaWZpY2F0aW9uRW1haWwodXNlci5faWQsIGVtYWlsKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd2ZXJpZmljYXRpb24tZW1haWwtZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gIGFzeW5jICd1c2Vycy5mb3Jnb3RQYXNzd29yZCcoZGF0YSkge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBNZXRob2QgY2FsbGVkIHdpdGggZGF0YTonLCBKU09OLnN0cmluZ2lmeShkYXRhKSk7XG5cbiAgICAgIGNoZWNrKGRhdGEsIHtcbiAgICAgICAgZW1haWw6IFN0cmluZyxcbiAgICAgICAgbmV3UGFzc3dvcmQ6IFN0cmluZ1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHsgZW1haWwsIG5ld1Bhc3N3b3JkIH0gPSBkYXRhO1xuICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gUHJvY2Vzc2luZyByZXF1ZXN0IGZvciBlbWFpbDonLCBlbWFpbCk7XG5cbiAgICAgIC8vIEZpbmQgdXNlciBieSBlbWFpbCB1c2luZyBBY2NvdW50cyBtZXRob2QgKG1vc3QgcmVsaWFibGUpXG4gICAgICBsZXQgdXNlcjtcbiAgICAgIHRyeSB7XG4gICAgICAgIHVzZXIgPSBBY2NvdW50cy5maW5kVXNlckJ5RW1haWwoZW1haWwpO1xuICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBVc2VyIGxvb2t1cCByZXN1bHQ6JywgdXNlciA/ICdGT1VORCcgOiAnTk9UIEZPVU5EJyk7XG4gICAgICB9IGNhdGNoIChmaW5kRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignW2ZvcmdvdFBhc3N3b3JkXSBFcnJvciBmaW5kaW5nIHVzZXI6JywgZmluZEVycm9yKTtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndXNlci1sb29rdXAtZmFpbGVkJywgJ0Vycm9yIGxvb2tpbmcgdXAgdXNlcicpO1xuICAgICAgfVxuXG4gICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndXNlci1ub3QtZm91bmQnLCAnTm8gdXNlciBmb3VuZCB3aXRoIHRoaXMgZW1haWwgYWRkcmVzcycpO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBVc2VyIGZvdW5kOicsIHVzZXIuX2lkKTtcblxuICAgIC8vIFZhbGlkYXRlIHBhc3N3b3JkIHJlcXVpcmVtZW50c1xuICAgIGNvbnN0IHBhc3N3b3JkUmVnZXggPSB7XG4gICAgICBsZW5ndGg6IC8uezgsfS8sXG4gICAgICB1cHBlcmNhc2U6IC9bQS1aXS8sXG4gICAgICBudW1iZXI6IC9bMC05XS8sXG4gICAgICBzcGVjaWFsOiAvWyFAIyQlXiYqXS9cbiAgICB9O1xuXG4gICAgY29uc3QgcGFzc3dvcmRFcnJvcnMgPSBbXTtcbiAgICBpZiAoIXBhc3N3b3JkUmVnZXgubGVuZ3RoLnRlc3QobmV3UGFzc3dvcmQpKSB7XG4gICAgICBwYXNzd29yZEVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGJlIGF0IGxlYXN0IDggY2hhcmFjdGVycyBsb25nJyk7XG4gICAgfVxuICAgIGlmICghcGFzc3dvcmRSZWdleC51cHBlcmNhc2UudGVzdChuZXdQYXNzd29yZCkpIHtcbiAgICAgIHBhc3N3b3JkRXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgdXBwZXJjYXNlIGxldHRlcicpO1xuICAgIH1cbiAgICBpZiAoIXBhc3N3b3JkUmVnZXgubnVtYmVyLnRlc3QobmV3UGFzc3dvcmQpKSB7XG4gICAgICBwYXNzd29yZEVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIG51bWJlcicpO1xuICAgIH1cbiAgICBpZiAoIXBhc3N3b3JkUmVnZXguc3BlY2lhbC50ZXN0KG5ld1Bhc3N3b3JkKSkge1xuICAgICAgcGFzc3dvcmRFcnJvcnMucHVzaCgnUGFzc3dvcmQgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSBzcGVjaWFsIGNoYXJhY3RlciAoIUAjJCVeJiopJyk7XG4gICAgfVxuXG4gICAgaWYgKHBhc3N3b3JkRXJyb3JzLmxlbmd0aCA+IDApIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2ludmFsaWQtcGFzc3dvcmQnLCBwYXNzd29yZEVycm9ycy5qb2luKCcsICcpKTtcbiAgICB9XG5cbiAgICAgIC8vIFVwZGF0ZSBwYXNzd29yZCB1c2luZyB0aGUgbW9zdCByZWxpYWJsZSBtZXRob2RcbiAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFN0YXJ0aW5nIHBhc3N3b3JkIHVwZGF0ZSBwcm9jZXNzLi4uJyk7XG5cbiAgICAgIC8vIENyZWF0ZSBhIHRlbXBvcmFyeSB1c2VyIHRvIGdldCB0aGUgY29ycmVjdCBwYXNzd29yZCBoYXNoIHN0cnVjdHVyZVxuICAgICAgY29uc3QgdGVtcEVtYWlsID0gYHRlbXBfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1AdGVtcC5sb2NhbGA7XG4gICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBDcmVhdGluZyB0ZW1wb3JhcnkgdXNlciB3aXRoIGVtYWlsOicsIHRlbXBFbWFpbCk7XG5cbiAgICAgIGxldCB0ZW1wVXNlcklkO1xuICAgICAgdHJ5IHtcbiAgICAgICAgdGVtcFVzZXJJZCA9IEFjY291bnRzLmNyZWF0ZVVzZXIoe1xuICAgICAgICAgIGVtYWlsOiB0ZW1wRW1haWwsXG4gICAgICAgICAgcGFzc3dvcmQ6IG5ld1Bhc3N3b3JkXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBUZW1wb3JhcnkgdXNlciBjcmVhdGVkIHdpdGggSUQ6JywgdGVtcFVzZXJJZCk7XG4gICAgICB9IGNhdGNoIChjcmVhdGVFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbZm9yZ290UGFzc3dvcmRdIEVycm9yIGNyZWF0aW5nIHRlbXBvcmFyeSB1c2VyOicsIGNyZWF0ZUVycm9yKTtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndGVtcC11c2VyLWNyZWF0aW9uLWZhaWxlZCcsICdGYWlsZWQgdG8gY3JlYXRlIHRlbXBvcmFyeSB1c2VyJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCB0aGUgcGFzc3dvcmQgaGFzaCBmcm9tIHRoZSB0ZW1wb3JhcnkgdXNlclxuICAgICAgbGV0IHRlbXBVc2VyLCBwYXNzd29yZEhhc2g7XG4gICAgICB0cnkge1xuICAgICAgICB0ZW1wVXNlciA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kT25lQXN5bmModGVtcFVzZXJJZCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFRlbXBvcmFyeSB1c2VyIHN0cnVjdHVyZTonLCBKU09OLnN0cmluZ2lmeSh0ZW1wVXNlciwgbnVsbCwgMikpO1xuXG4gICAgICAgIGlmICghdGVtcFVzZXIpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1RlbXBvcmFyeSB1c2VyIG5vdCBmb3VuZCBhZnRlciBjcmVhdGlvbicpO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCF0ZW1wVXNlci5zZXJ2aWNlcykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignVGVtcG9yYXJ5IHVzZXIgaGFzIG5vIHNlcnZpY2VzIG9iamVjdCcpO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCF0ZW1wVXNlci5zZXJ2aWNlcy5wYXNzd29yZCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignVGVtcG9yYXJ5IHVzZXIgaGFzIG5vIHBhc3N3b3JkIHNlcnZpY2UnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHBhc3N3b3JkSGFzaCA9IHRlbXBVc2VyLnNlcnZpY2VzLnBhc3N3b3JkO1xuICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBQYXNzd29yZCBoYXNoIHN0cnVjdHVyZTonLCBKU09OLnN0cmluZ2lmeShwYXNzd29yZEhhc2gsIG51bGwsIDIpKTtcbiAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gUGFzc3dvcmQgaGFzaCBleHRyYWN0ZWQgZnJvbSB0ZW1wb3JhcnkgdXNlcicpO1xuICAgICAgfSBjYXRjaCAoaGFzaEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tmb3Jnb3RQYXNzd29yZF0gRXJyb3IgZXh0cmFjdGluZyBwYXNzd29yZCBoYXNoOicsIGhhc2hFcnJvcik7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFRyeWluZyBmYWxsYmFjayBhcHByb2FjaCB3aXRoIGJjcnlwdC4uLicpO1xuXG4gICAgICAgIC8vIENsZWFuIHVwIHRlbXAgdXNlciBpZiBpdCBleGlzdHNcbiAgICAgICAgaWYgKHRlbXBVc2VySWQpIHtcbiAgICAgICAgICB0cnkgeyBhd2FpdCBNZXRlb3IudXNlcnMucmVtb3ZlQXN5bmModGVtcFVzZXJJZCk7IH0gY2F0Y2ggKGUpIHt9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBGYWxsYmFjazogVXNlIGJjcnlwdCBkaXJlY3RseVxuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHNhbHRSb3VuZHMgPSAxMDtcbiAgICAgICAgICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGJjcnlwdC5oYXNoU3luYyhuZXdQYXNzd29yZCwgc2FsdFJvdW5kcyk7XG4gICAgICAgICAgcGFzc3dvcmRIYXNoID0ge1xuICAgICAgICAgICAgYmNyeXB0OiBoYXNoZWRQYXNzd29yZFxuICAgICAgICAgIH07XG4gICAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gRmFsbGJhY2sgYmNyeXB0IGhhc2ggY3JlYXRlZCcpO1xuICAgICAgICB9IGNhdGNoIChiY3J5cHRFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tmb3Jnb3RQYXNzd29yZF0gQmNyeXB0IGZhbGxiYWNrIGFsc28gZmFpbGVkOicsIGJjcnlwdEVycm9yKTtcbiAgICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdwYXNzd29yZC1oYXNoLWNyZWF0aW9uLWZhaWxlZCcsICdGYWlsZWQgdG8gY3JlYXRlIHBhc3N3b3JkIGhhc2gnKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBVcGRhdGUgdGhlIHRhcmdldCB1c2VyJ3MgcGFzc3dvcmRcbiAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIEF0dGVtcHRpbmcgdG8gdXBkYXRlIHVzZXIgcGFzc3dvcmQuLi4nKTtcbiAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFRhcmdldCB1c2VyIElEOicsIHVzZXIuX2lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFRhcmdldCB1c2VyIElEIHR5cGU6JywgdHlwZW9mIHVzZXIuX2lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFBhc3N3b3JkIGhhc2ggdG8gc2V0OicsIEpTT04uc3RyaW5naWZ5KHBhc3N3b3JkSGFzaCwgbnVsbCwgMikpO1xuXG4gICAgICBsZXQgdXBkYXRlUmVzdWx0ID0gMDtcbiAgICAgIGxldCB1cGRhdGVFcnJvciA9IG51bGw7XG5cbiAgICAgIC8vIFRyeSBtdWx0aXBsZSB1cGRhdGUgYXBwcm9hY2hlc1xuICAgICAgY29uc3QgdXBkYXRlTWV0aG9kcyA9IFtcbiAgICAgICAgLy8gTWV0aG9kIDE6IFN0YW5kYXJkIHVwZGF0ZVxuICAgICAgICAoKSA9PiB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gVHJ5aW5nIE1ldGhvZCAxOiBTdGFuZGFyZCB1cGRhdGUnKTtcbiAgICAgICAgICByZXR1cm4gTWV0ZW9yLnVzZXJzLnVwZGF0ZSh1c2VyLl9pZCwge1xuICAgICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgICAnc2VydmljZXMucGFzc3dvcmQnOiBwYXNzd29yZEhhc2hcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgLy8gTWV0aG9kIDI6IFVwZGF0ZSB3aXRoIHF1ZXJ5IG9iamVjdFxuICAgICAgICAoKSA9PiB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gVHJ5aW5nIE1ldGhvZCAyOiBVcGRhdGUgd2l0aCBxdWVyeSBvYmplY3QnKTtcbiAgICAgICAgICByZXR1cm4gTWV0ZW9yLnVzZXJzLnVwZGF0ZShcbiAgICAgICAgICAgIHsgX2lkOiB1c2VyLl9pZCB9LFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICAkc2V0OiB7XG4gICAgICAgICAgICAgICAgJ3NlcnZpY2VzLnBhc3N3b3JkJzogcGFzc3dvcmRIYXNoXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICApO1xuICAgICAgICB9LFxuICAgICAgICAvLyBNZXRob2QgMzogVXBkYXRlIHdpdGggc3RyaW5nIElEXG4gICAgICAgICgpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBUcnlpbmcgTWV0aG9kIDM6IFVwZGF0ZSB3aXRoIHN0cmluZyBJRCcpO1xuICAgICAgICAgIHJldHVybiBNZXRlb3IudXNlcnMudXBkYXRlKHVzZXIuX2lkLnRvU3RyaW5nKCksIHtcbiAgICAgICAgICAgICRzZXQ6IHtcbiAgICAgICAgICAgICAgJ3NlcnZpY2VzLnBhc3N3b3JkJzogcGFzc3dvcmRIYXNoXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIC8vIE1ldGhvZCA0OiBVcHNlcnQgYXBwcm9hY2hcbiAgICAgICAgKCkgPT4ge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFRyeWluZyBNZXRob2QgNDogVXBzZXJ0IGFwcHJvYWNoJyk7XG4gICAgICAgICAgcmV0dXJuIE1ldGVvci51c2Vycy51cGRhdGUoXG4gICAgICAgICAgICB7IF9pZDogdXNlci5faWQgfSxcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgICAgICdzZXJ2aWNlcy5wYXNzd29yZCc6IHBhc3N3b3JkSGFzaFxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgeyB1cHNlcnQ6IGZhbHNlIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICBdO1xuXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHVwZGF0ZU1ldGhvZHMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICB1cGRhdGVSZXN1bHQgPSB1cGRhdGVNZXRob2RzW2ldKCk7XG4gICAgICAgICAgY29uc29sZS5sb2coYFtmb3Jnb3RQYXNzd29yZF0gTWV0aG9kICR7aSArIDF9IHJlc3VsdDpgLCB1cGRhdGVSZXN1bHQpO1xuXG4gICAgICAgICAgaWYgKHVwZGF0ZVJlc3VsdCA9PT0gMSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYFtmb3Jnb3RQYXNzd29yZF0gVXBkYXRlIHN1Y2Nlc3NmdWwgd2l0aCBtZXRob2QgJHtpICsgMX1gKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAobWV0aG9kRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGBbZm9yZ290UGFzc3dvcmRdIE1ldGhvZCAke2kgKyAxfSBlcnJvcjpgLCBtZXRob2RFcnJvcik7XG4gICAgICAgICAgdXBkYXRlRXJyb3IgPSBtZXRob2RFcnJvcjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAodXBkYXRlUmVzdWx0ICE9PSAxKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tmb3Jnb3RQYXNzd29yZF0gQWxsIHVwZGF0ZSBtZXRob2RzIGZhaWxlZCcpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbZm9yZ290UGFzc3dvcmRdIEZpbmFsIHVwZGF0ZSByZXN1bHQ6JywgdXBkYXRlUmVzdWx0KTtcbiAgICAgICAgY29uc29sZS5lcnJvcignW2ZvcmdvdFBhc3N3b3JkXSBMYXN0IGVycm9yOicsIHVwZGF0ZUVycm9yKTtcblxuICAgICAgICAvLyBMZXQncyB2ZXJpZnkgdGhlIHVzZXIgc3RpbGwgZXhpc3RzIGFuZCBjaGVjayBpdHMgY3VycmVudCBzdHJ1Y3R1cmVcbiAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gVmVyaWZ5aW5nIHVzZXIgZXhpc3RzLi4uJyk7XG4gICAgICAgIGNvbnN0IHVzZXJDaGVjayA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kT25lQXN5bmModXNlci5faWQpO1xuICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBVc2VyIHZlcmlmaWNhdGlvbiByZXN1bHQ6JywgdXNlckNoZWNrID8gJ0VYSVNUUycgOiAnTk9UIEZPVU5EJyk7XG5cbiAgICAgICAgaWYgKHVzZXJDaGVjaykge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIEN1cnJlbnQgdXNlciBzdHJ1Y3R1cmU6JywgSlNPTi5zdHJpbmdpZnkodXNlckNoZWNrLCBudWxsLCAyKSk7XG5cbiAgICAgICAgICAvLyBUcnkgYSBzaW1wbGUgdGVzdCB1cGRhdGUgdG8gc2VlIGlmIHVwZGF0ZXMgd29yayBhdCBhbGxcbiAgICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBUcnlpbmcgc2ltcGxlIHRlc3QgdXBkYXRlLi4uJyk7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHRlc3RSZXN1bHQgPSBNZXRlb3IudXNlcnMudXBkYXRlKHVzZXIuX2lkLCB7XG4gICAgICAgICAgICAgICRzZXQ6IHtcbiAgICAgICAgICAgICAgICAncHJvZmlsZS5sYXN0UGFzc3dvcmRSZXNldEF0dGVtcHQnOiBuZXcgRGF0ZSgpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gVGVzdCB1cGRhdGUgcmVzdWx0OicsIHRlc3RSZXN1bHQpO1xuXG4gICAgICAgICAgICBpZiAodGVzdFJlc3VsdCA9PT0gMSkge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBUZXN0IHVwZGF0ZSB3b3JrZWQhIElzc3VlIG1pZ2h0IGJlIHdpdGggcGFzc3dvcmQgZmllbGQgc3RydWN0dXJlJyk7XG5cbiAgICAgICAgICAgICAgLy8gVHJ5IHVwZGF0aW5nIGp1c3QgdGhlIGJjcnlwdCBmaWVsZFxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBUcnlpbmcgdG8gdXBkYXRlIGp1c3QgYmNyeXB0IGZpZWxkLi4uJyk7XG4gICAgICAgICAgICAgIGNvbnN0IGJjcnlwdFJlc3VsdCA9IE1ldGVvci51c2Vycy51cGRhdGUodXNlci5faWQsIHtcbiAgICAgICAgICAgICAgICAkc2V0OiB7XG4gICAgICAgICAgICAgICAgICAnc2VydmljZXMucGFzc3dvcmQuYmNyeXB0JzogcGFzc3dvcmRIYXNoLmJjcnlwdCB8fCBwYXNzd29yZEhhc2hcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBCY3J5cHQgZmllbGQgdXBkYXRlIHJlc3VsdDonLCBiY3J5cHRSZXN1bHQpO1xuXG4gICAgICAgICAgICAgIGlmIChiY3J5cHRSZXN1bHQgPT09IDEpIHtcbiAgICAgICAgICAgICAgICB1cGRhdGVSZXN1bHQgPSBiY3J5cHRSZXN1bHQ7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gQmNyeXB0IGZpZWxkIHVwZGF0ZSBzdWNjZXNzZnVsIScpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBjYXRjaCAodGVzdEVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdbZm9yZ290UGFzc3dvcmRdIFRlc3QgdXBkYXRlIGZhaWxlZDonLCB0ZXN0RXJyb3IpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIENsZWFuIHVwIHRlbXAgdXNlclxuICAgICAgICBpZiAodGVtcFVzZXJJZCkge1xuICAgICAgICAgIHRyeSB7IGF3YWl0IE1ldGVvci51c2Vycy5yZW1vdmVBc3luYyh0ZW1wVXNlcklkKTsgfSBjYXRjaCAoZSkge31cbiAgICAgICAgfVxuXG4gICAgICAgIGlmICh1cGRhdGVSZXN1bHQgIT09IDEpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdwYXNzd29yZC11cGRhdGUtZmFpbGVkJywgYEZhaWxlZCB0byB1cGRhdGUgdXNlciBwYXNzd29yZC4gVXBkYXRlIHJlc3VsdDogJHt1cGRhdGVSZXN1bHR9YCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQ2xlYW4gdXAgdGVtcG9yYXJ5IHVzZXJcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IE1ldGVvci51c2Vycy5yZW1vdmVBc3luYyh0ZW1wVXNlcklkKTtcbiAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gVGVtcG9yYXJ5IHVzZXIgY2xlYW5lZCB1cCcpO1xuICAgICAgfSBjYXRjaCAoY2xlYW51cEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignW2ZvcmdvdFBhc3N3b3JkXSBXYXJuaW5nOiBGYWlsZWQgdG8gY2xlYW4gdXAgdGVtcG9yYXJ5IHVzZXI6JywgY2xlYW51cEVycm9yKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHVwZGF0ZVJlc3VsdCA9PT0gMSkge1xuICAgICAgICBjb25zb2xlLmxvZyhgW2ZvcmdvdFBhc3N3b3JkXSBQYXNzd29yZCByZXNldCBzdWNjZXNzZnVsIGZvciB1c2VyOiAke2VtYWlsfWApO1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBtZXNzYWdlOiAnUGFzc3dvcmQgdXBkYXRlZCBzdWNjZXNzZnVsbHknIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdwYXNzd29yZC11cGRhdGUtZmFpbGVkJywgYFBhc3N3b3JkIHVwZGF0ZSBmYWlsZWQuIFVwZGF0ZSByZXN1bHQ6ICR7dXBkYXRlUmVzdWx0fWApO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAob3V0ZXJFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW2ZvcmdvdFBhc3N3b3JkXSBPdXRlciBjYXRjaCAtIEVycm9yIGRldGFpbHM6Jywgb3V0ZXJFcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKCdbZm9yZ290UGFzc3dvcmRdIE91dGVyIGNhdGNoIC0gRXJyb3Igc3RhY2s6Jywgb3V0ZXJFcnJvci5zdGFjayk7XG5cbiAgICAgIC8vIElmIGl0J3MgYWxyZWFkeSBhIE1ldGVvci5FcnJvciwgcmUtdGhyb3cgaXRcbiAgICAgIGlmIChvdXRlckVycm9yLmVycm9yKSB7XG4gICAgICAgIHRocm93IG91dGVyRXJyb3I7XG4gICAgICB9XG5cbiAgICAgIC8vIE90aGVyd2lzZSwgd3JhcCBpdCBpbiBhIE1ldGVvci5FcnJvclxuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignZm9yZ290LXBhc3N3b3JkLWZhaWxlZCcsIGBGb3Jnb3QgcGFzc3dvcmQgZmFpbGVkOiAke291dGVyRXJyb3IubWVzc2FnZX1gKTtcbiAgICB9XG4gIH0sXG5cbiAgYXN5bmMgJ3VzZXJzLmNoZWNrQW5kRml4QWRtaW5Sb2xlJygpIHtcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4nKTtcbiAgICB9XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIENoZWNraW5nIHVzZXI6Jywge1xuICAgICAgICBpZDogdXNlcj8uX2lkLFxuICAgICAgICBlbWFpbDogdXNlcj8uZW1haWxzPy5bMF0/LmFkZHJlc3MsXG4gICAgICAgIHJvbGVzOiB1c2VyPy5yb2xlc1xuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIElmIHVzZXIgaGFzIG5vIHJvbGVzIGFycmF5LCBpbml0aWFsaXplIGl0XG4gICAgICBpZiAoIXVzZXIucm9sZXMpIHtcbiAgICAgICAgYXdhaXQgTWV0ZW9yLnVzZXJzLnVwZGF0ZUFzeW5jKHRoaXMudXNlcklkLCB7XG4gICAgICAgICAgJHNldDogeyByb2xlczogWyd0ZWFtLW1lbWJlciddIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiAnUm9sZXMgaW5pdGlhbGl6ZWQnO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBJZiB1c2VyIGhhcyBubyByb2xlcyBvciBkb2Vzbid0IGhhdmUgYWRtaW4gcm9sZVxuICAgICAgaWYgKCF1c2VyLnJvbGVzLmluY2x1ZGVzKCdhZG1pbicpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIFVzZXIgaXMgbm90IGFkbWluLCBjaGVja2luZyBpZiBmaXJzdCB1c2VyJyk7XG4gICAgICAgIFxuICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIHRoZSBmaXJzdCB1c2VyICh0aGV5IHNob3VsZCBiZSBhZG1pbilcbiAgICAgICAgY29uc3QgdG90YWxVc2VycyA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kKCkuY291bnRBc3luYygpO1xuICAgICAgICBpZiAodG90YWxVc2VycyA9PT0gMSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIEZpcnN0IHVzZXIsIHNldHRpbmcgYXMgYWRtaW4nKTtcbiAgICAgICAgICBhd2FpdCBNZXRlb3IudXNlcnMudXBkYXRlQXN5bmModGhpcy51c2VySWQsIHtcbiAgICAgICAgICAgICRzZXQ6IHsgcm9sZXM6IFsnYWRtaW4nXSB9XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuICdBZG1pbiByb2xlIGFkZGVkJztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gJ1VzZXIgaXMgbm90IGFkbWluJztcbiAgICAgIH1cbiAgICAgIFxuICAgICAgcmV0dXJuICdVc2VyIGlzIGFscmVhZHkgYWRtaW4nO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIEVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2NoZWNrLXJvbGUtZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gIGFzeW5jICd1c2Vycy5kaWFnbm9zZVJvbGVzJygpIHtcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4nKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY3VycmVudFVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICAgIGlmICghY3VycmVudFVzZXIucm9sZXM/LmluY2x1ZGVzKCdhZG1pbicpKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ09ubHkgYWRtaW5zIGNhbiBkaWFnbm9zZSByb2xlcycpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBhbGxVc2VycyA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kKCkuZmV0Y2hBc3luYygpO1xuICAgICAgY29uc3QgdXNlcnNXaXRoSXNzdWVzID0gW107XG4gICAgICBjb25zdCBmaXhlcyA9IFtdO1xuXG4gICAgICBmb3IgKGNvbnN0IHVzZXIgb2YgYWxsVXNlcnMpIHtcbiAgICAgICAgY29uc3QgaXNzdWVzID0gW107XG4gICAgICAgIFxuICAgICAgICAvLyBDaGVjayBpZiByb2xlcyBhcnJheSBleGlzdHNcbiAgICAgICAgaWYgKCF1c2VyLnJvbGVzIHx8ICFBcnJheS5pc0FycmF5KHVzZXIucm9sZXMpKSB7XG4gICAgICAgICAgaXNzdWVzLnB1c2goJ05vIHJvbGVzIGFycmF5Jyk7XG4gICAgICAgICAgLy8gRml4OiBJbml0aWFsaXplIHJvbGVzIGJhc2VkIG9uIHByb2ZpbGVcbiAgICAgICAgICBjb25zdCByb2xlID0gdXNlci5wcm9maWxlPy5yb2xlIHx8ICd0ZWFtLW1lbWJlcic7XG4gICAgICAgICAgYXdhaXQgTWV0ZW9yLnVzZXJzLnVwZGF0ZUFzeW5jKHVzZXIuX2lkLCB7XG4gICAgICAgICAgICAkc2V0OiB7IHJvbGVzOiBbcm9sZV0gfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGZpeGVzLnB1c2goYEluaXRpYWxpemVkIHJvbGVzIGZvciAke3VzZXIuZW1haWxzPy5bMF0/LmFkZHJlc3N9YCk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8vIENoZWNrIGlmIHJvbGUgbWF0Y2hlcyBwcm9maWxlXG4gICAgICAgIGlmICh1c2VyLnByb2ZpbGU/LnJvbGUgJiYgdXNlci5yb2xlcz8uWzBdICE9PSB1c2VyLnByb2ZpbGUucm9sZSkge1xuICAgICAgICAgIGlzc3Vlcy5wdXNoKCdSb2xlIG1pc21hdGNoIHdpdGggcHJvZmlsZScpO1xuICAgICAgICAgIC8vIEZpeDogVXBkYXRlIHJvbGVzIHRvIG1hdGNoIHByb2ZpbGVcbiAgICAgICAgICBhd2FpdCBNZXRlb3IudXNlcnMudXBkYXRlQXN5bmModXNlci5faWQsIHtcbiAgICAgICAgICAgICRzZXQ6IHsgcm9sZXM6IFt1c2VyLnByb2ZpbGUucm9sZV0gfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGZpeGVzLnB1c2goYEZpeGVkIHJvbGUgbWlzbWF0Y2ggZm9yICR7dXNlci5lbWFpbHM/LlswXT8uYWRkcmVzc31gKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChpc3N1ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHVzZXJzV2l0aElzc3Vlcy5wdXNoKHtcbiAgICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlscz8uWzBdPy5hZGRyZXNzLFxuICAgICAgICAgICAgaXNzdWVzXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdXNlcnNXaXRoSXNzdWVzLFxuICAgICAgICBmaXhlcyxcbiAgICAgICAgbWVzc2FnZTogZml4ZXMubGVuZ3RoID4gMCA/ICdGaXhlZCByb2xlIGlzc3VlcycgOiAnTm8gaXNzdWVzIGZvdW5kJ1xuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignZGlhZ25vc2UtZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gICd1c2Vycy5jcmVhdGVUZXN0VGVhbU1lbWJlcicoKSB7XG4gICAgLy8gT25seSBhbGxvdyBpbiBkZXZlbG9wbWVudFxuICAgIGlmICghcHJvY2Vzcy5lbnYuTk9ERV9FTlYgfHwgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHRlc3RNZW1iZXIgPSB7XG4gICAgICAgICAgZW1haWw6ICd0ZXN0bWVtYmVyQGV4YW1wbGUuY29tJyxcbiAgICAgICAgICBwYXNzd29yZDogJ1Rlc3RQYXNzMTIzIScsXG4gICAgICAgICAgZmlyc3ROYW1lOiAnVGVzdCcsXG4gICAgICAgICAgbGFzdE5hbWU6ICdNZW1iZXInXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgdXNlcklkID0gQWNjb3VudHMuY3JlYXRlVXNlcih7XG4gICAgICAgICAgZW1haWw6IHRlc3RNZW1iZXIuZW1haWwsXG4gICAgICAgICAgcGFzc3dvcmQ6IHRlc3RNZW1iZXIucGFzc3dvcmQsXG4gICAgICAgICAgcHJvZmlsZToge1xuICAgICAgICAgICAgZmlyc3ROYW1lOiB0ZXN0TWVtYmVyLmZpcnN0TmFtZSxcbiAgICAgICAgICAgIGxhc3ROYW1lOiB0ZXN0TWVtYmVyLmxhc3ROYW1lLFxuICAgICAgICAgICAgcm9sZTogJ3RlYW0tbWVtYmVyJyxcbiAgICAgICAgICAgIGZ1bGxOYW1lOiBgJHt0ZXN0TWVtYmVyLmZpcnN0TmFtZX0gJHt0ZXN0TWVtYmVyLmxhc3ROYW1lfWBcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFNldCB0aGUgcm9sZSBleHBsaWNpdGx5XG4gICAgICAgIE1ldGVvci51c2Vycy51cGRhdGUodXNlcklkLCB7XG4gICAgICAgICAgJHNldDogeyByb2xlczogWyd0ZWFtLW1lbWJlciddIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIHVzZXJJZCxcbiAgICAgICAgICBtZXNzYWdlOiAnVGVzdCB0ZWFtIG1lbWJlciBjcmVhdGVkIHN1Y2Nlc3NmdWxseSdcbiAgICAgICAgfTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tjcmVhdGVUZXN0VGVhbU1lbWJlcl0gRXJyb3I6JywgZXJyb3IpO1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdjcmVhdGUtdGVzdC1tZW1iZXItZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1kZXZlbG9wbWVudCcsICdUaGlzIG1ldGhvZCBpcyBvbmx5IGF2YWlsYWJsZSBpbiBkZXZlbG9wbWVudCcpO1xuICAgIH1cbiAgfVxufSk7Il19
