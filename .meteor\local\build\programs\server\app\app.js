Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// imports/api/links.js                                                                                           //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// imports/api/tasks.js                                                                                           //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// server/main.js                                                                                                 //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        var _user$emails3, _user$emails3$;
        console.log('[forgotPassword] Method called with data:', data);
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email
        const user = Accounts.findUserByEmail(email);
        console.log('[forgotPassword] User found:', user ? {
          id: user._id,
          email: (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address
        } : 'No user found');
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Validate password requirements (same as signup)
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Update the user's password using user ID
        console.log('[forgotPassword] Attempting to update password for user ID:', user._id);
        console.log('[forgotPassword] Current user document structure:', JSON.stringify(user, null, 2));
        try {
          // Hash the password using bcrypt (same as Meteor uses internally)
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Generated password hash:', hashedPassword);

          // Try multiple field paths to ensure compatibility with different Meteor versions
          const updateOperations = [
          // Modern Meteor structure
          {
            'services.password.bcrypt': hashedPassword
          },
          // Alternative structure
          {
            'services.password': {
              bcrypt: hashedPassword
            }
          },
          // Fallback structure
          {
            'services.password.hash': hashedPassword
          }];
          let updateResult = 0;
          let lastError = null;
          for (const updateOp of updateOperations) {
            try {
              console.log('[forgotPassword] Trying update operation:', updateOp);
              updateResult = await Meteor.users.updateAsync(user._id, {
                $set: updateOp
              });
              if (updateResult === 1) {
                console.log('[forgotPassword] Update successful with operation:', updateOp);
                break;
              }
            } catch (opError) {
              console.log('[forgotPassword] Update operation failed:', opError.message);
              lastError = opError;
            }
          }
          if (updateResult === 1) {
            console.log("[forgotPassword] Password reset successful for user ID: ".concat(user._id, ", email: ").concat(email));
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            var _lastError;
            // If all update operations failed, try to check the user document again
            const userAfterUpdate = await Meteor.users.findOneAsync(user._id);
            console.log('[forgotPassword] User document after update attempts:', JSON.stringify(userAfterUpdate, null, 2));
            throw new Error("User update failed - no documents modified. Last error: ".concat(((_lastError = lastError) === null || _lastError === void 0 ? void 0 : _lastError.message) || 'Unknown'));
          }
        } catch (error) {
          console.error('[forgotPassword] Error updating password for user:', user._id, 'Error details:', error);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      // Simple test method to verify method calls are working
      'users.testMethod'() {
        console.log('[testMethod] Method called successfully');
        return {
          success: true,
          message: 'Test method working'
        };
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails4, _user$emails4$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails5, _user$emails5$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails6, _user$emails6$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));
            }
            if (issues.length > 0) {
              var _user$emails7, _user$emails7$;
              usersWithIssues.push({
                email: (_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1ldGVvcjovL/CfkrthcHAvaW1wb3J0cy9hcGkvbGlua3MuanMiLCJtZXRlb3I6Ly/wn5K7YXBwL2ltcG9ydHMvYXBpL3Rhc2tzLmpzIiwibWV0ZW9yOi8v8J+Su2FwcC9zZXJ2ZXIvbWFpbi5qcyJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnQiLCJMaW5rc0NvbGxlY3Rpb24iLCJNb25nbyIsImxpbmsiLCJ2IiwiX19yZWlmeVdhaXRGb3JEZXBzX18iLCJDb2xsZWN0aW9uIiwiX19yZWlmeV9hc3luY19yZXN1bHRfXyIsIl9yZWlmeUVycm9yIiwic2VsZiIsImFzeW5jIiwiX29iamVjdFNwcmVhZCIsImRlZmF1bHQiLCJUYXNrcyIsInRhc2tDYXRlZ29yaWVzIiwidGFza0xhYmVscyIsIk1ldGVvciIsImNoZWNrIiwiTWF0Y2giLCJuYW1lIiwiY29sb3IiLCJpc1NlcnZlciIsInB1Ymxpc2giLCJfdXNlciRyb2xlcyIsInVzZXJJZCIsInJlYWR5IiwidXNlciIsInVzZXJzIiwiZmluZE9uZUFzeW5jIiwiaXNBZG1pbiIsInJvbGVzIiwiaW5jbHVkZXMiLCJmaW5kIiwic29ydCIsImNyZWF0ZWRBdCIsImZpZWxkcyIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJzdGFydERhdGUiLCJkdWVEYXRlIiwicHJpb3JpdHkiLCJzdGF0dXMiLCJhc3NpZ25lZFRvIiwiY2hlY2tsaXN0IiwiY2F0ZWdvcnkiLCJsYWJlbHMiLCJwcm9ncmVzcyIsImF0dGFjaG1lbnRzIiwibGlua3MiLCJjcmVhdGVkQnkiLCJ1cGRhdGVkQXQiLCJ1cGRhdGVkQnkiLCIkb3IiLCJjb25zb2xlIiwibG9nIiwidGFza3MiLCJmZXRjaCIsImxlbmd0aCIsInVzZXJJZHMiLCJTZXQiLCJmb3JFYWNoIiwidGFzayIsImF0dGFjaG1lbnQiLCJ1cGxvYWRlZEJ5IiwiYWRkIiwiU3RyaW5nIiwiYWRkZWRCeSIsInVzZXJJZEFycmF5IiwiQXJyYXkiLCJmcm9tIiwiX2lkIiwiJGluIiwiZW1haWxzIiwibWFwIiwidSIsIl91JHByb2ZpbGUiLCJfdSRwcm9maWxlMiIsImNvbmNhdCIsInByb2ZpbGUiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsInRyaW0iLCJoYXNQcm9maWxlIiwidGFza0lkIiwiX3VzZXIkcm9sZXMyIiwiZmluZE9uZSIsImN1cnNvciIsIm9ic2VydmUiLCJhZGRlZCIsImRvYyIsImNoYW5nZWQiLCJyZW1vdmVkIiwibWV0aG9kcyIsInRhc2tzLmluc2VydCIsIkRhdGUiLCJOdW1iZXIiLCJFcnJvciIsInByb2Nlc3NlZENoZWNrbGlzdCIsIml0ZW0iLCJ0ZXh0IiwiY29tcGxldGVkIiwidGFza1RvSW5zZXJ0IiwicmVzdWx0IiwiaW5zZXJ0QXN5bmMiLCJlcnJvciIsIm1lc3NhZ2UiLCJ0YXNrcy51cGRhdGUiLCJfdXNlciRyb2xlczMiLCJPcHRpb25hbCIsImV4aXN0aW5nVGFzayIsImNvbXBsZXRlZEl0ZW1zIiwiZmlsdGVyIiwidG90YWxJdGVtcyIsIk1hdGgiLCJyb3VuZCIsInRhc2tUb1VwZGF0ZSIsInVwZGF0ZUFzeW5jIiwiJHNldCIsInRhc2tzLmRlbGV0ZSIsInJlbW92ZUFzeW5jIiwidGFza3MudXBkYXRlUHJvZ3Jlc3MiLCJ1cGRhdGUiLCJ0YXNrcy50b2dnbGVDaGVja2xpc3RJdGVtIiwiaXRlbUluZGV4IiwiX3VzZXIkcm9sZXM0IiwiY2hlY2tsaXN0TGVuZ3RoIiwidXBkYXRlZENoZWNrbGlzdCIsInVwZGF0ZVJlc3VsdCIsInVwZGF0ZWRUYXNrIiwidGFza3MuYWRkQXR0YWNobWVudCIsImZpbGVEYXRhIiwiZGF0YSIsInVwbG9hZGVySWQiLCIkcHVzaCIsInR5cGUiLCJ1cGxvYWRlZEF0IiwidGFza3MuYWRkTGluayIsIlVSTCIsImUiLCJ1cmwiLCJhZGRlZEF0IiwidGFza3MucmVtb3ZlQXR0YWNobWVudCIsImF0dGFjaG1lbnRJbmRleCIsImN1cnJlbnRVc2VySWQiLCJ1cGxvYWRlZEJ5SWQiLCJ1cGRhdGVkQXR0YWNobWVudHMiLCJzcGxpY2UiLCJ0YXNrcy5yZW1vdmVMaW5rIiwibGlua0luZGV4IiwiYWRkZWRCeUlkIiwidXBkYXRlZExpbmtzIiwidGFza3MuZmluZE9uZSIsIkFjY291bnRzIiwiRW1haWwiLCJSb2xlcyIsImJjcnlwdCIsImluc2VydExpbmsiLCJfcmVmIiwiQURNSU5fVE9LRU4iLCJzdGFydHVwIiwiX01ldGVvciRzZXR0aW5ncyRwcml2IiwiY3JlYXRlSW5kZXgiLCJiYWNrZ3JvdW5kIiwid2FybiIsImFsbFVzZXJzIiwiZmV0Y2hBc3luYyIsIl91c2VyJGVtYWlscyIsIl91c2VyJGVtYWlscyQiLCJpZCIsImVtYWlsIiwiYWRkcmVzcyIsInVwZGF0ZXMiLCJpc0FycmF5IiwiT2JqZWN0Iiwia2V5cyIsIl91c2VyJGVtYWlsczIiLCJfdXNlciRlbWFpbHMyJCIsInRlYW1NZW1iZXJzQ291bnQiLCJjb3VudEFzeW5jIiwidGVzdE1lbWJlcnMiLCJwYXNzd29yZCIsIm1lbWJlciIsImNyZWF0ZVVzZXJBc3luYyIsInJvbGUiLCJmdWxsTmFtZSIsImVtYWlsU2V0dGluZ3MiLCJzZXR0aW5ncyIsInByaXZhdGUiLCJ1c2VybmFtZSIsInByb2Nlc3MiLCJlbnYiLCJNQUlMX1VSTCIsImVuY29kZVVSSUNvbXBvbmVudCIsInNlcnZlciIsInBvcnQiLCJzZW5kIiwidG8iLCJzdWJqZWN0IiwiY29uZmlnIiwic2VuZFZlcmlmaWNhdGlvbkVtYWlsIiwiZm9yYmlkQ2xpZW50QWNjb3VudENyZWF0aW9uIiwiZW1haWxUZW1wbGF0ZXMiLCJzaXRlTmFtZSIsInZlcmlmeUVtYWlsIiwiZW1haWxBZGRyZXNzIiwiaHRtbCIsIm9uQ3JlYXRlVXNlciIsIm9wdGlvbnMiLCJfY3VzdG9taXplZFVzZXIkZW1haWwiLCJfY3VzdG9taXplZFVzZXIkZW1haWwyIiwiY3VzdG9taXplZFVzZXIiLCJ0ZWFtTWVtYmVycyIsInVzZXJzLmNyZWF0ZSIsIl9yZWYyIiwiYWRtaW5Ub2tlbiIsInBhc3N3b3JkUmVnZXgiLCJ1cHBlcmNhc2UiLCJudW1iZXIiLCJzcGVjaWFsIiwicGFzc3dvcmRFcnJvcnMiLCJ0ZXN0IiwicHVzaCIsImpvaW4iLCJjcmVhdGVVc2VyIiwidXNlcnMuZ2V0Um9sZSIsIl91c2VyJHByb2ZpbGUiLCJ1c2Vycy5yZXNlbmRWZXJpZmljYXRpb25FbWFpbCIsImZpbmRVc2VyQnlFbWFpbCIsInVzZXJFbWFpbCIsInZlcmlmaWVkIiwidXNlcnMuZm9yZ290UGFzc3dvcmQiLCJfdXNlciRlbWFpbHMzIiwiX3VzZXIkZW1haWxzMyQiLCJuZXdQYXNzd29yZCIsIkpTT04iLCJzdHJpbmdpZnkiLCJzYWx0Um91bmRzIiwiaGFzaGVkUGFzc3dvcmQiLCJoYXNoU3luYyIsInVwZGF0ZU9wZXJhdGlvbnMiLCJsYXN0RXJyb3IiLCJ1cGRhdGVPcCIsIm9wRXJyb3IiLCJzdWNjZXNzIiwiX2xhc3RFcnJvciIsInVzZXJBZnRlclVwZGF0ZSIsInVzZXJzLnRlc3RNZXRob2QiLCJ1c2Vycy5jaGVja0FuZEZpeEFkbWluUm9sZSIsIl91c2VyJGVtYWlsczQiLCJfdXNlciRlbWFpbHM0JCIsInRvdGFsVXNlcnMiLCJ1c2Vycy5kaWFnbm9zZVJvbGVzIiwiX2N1cnJlbnRVc2VyJHJvbGVzIiwiY3VycmVudFVzZXIiLCJ1c2Vyc1dpdGhJc3N1ZXMiLCJmaXhlcyIsIl91c2VyJHByb2ZpbGUzIiwiaXNzdWVzIiwiX3VzZXIkcHJvZmlsZTIiLCJfdXNlciRlbWFpbHM1IiwiX3VzZXIkZW1haWxzNSQiLCJfdXNlciRlbWFpbHM2IiwiX3VzZXIkZW1haWxzNiQiLCJfdXNlciRlbWFpbHM3IiwiX3VzZXIkZW1haWxzNyQiLCJ1c2Vycy5jcmVhdGVUZXN0VGVhbU1lbWJlciIsIk5PREVfRU5WIiwidGVzdE1lbWJlciJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUFBQSxNQUFNLENBQUNDLE1BQU0sQ0FBQztNQUFDQyxlQUFlLEVBQUNBLENBQUEsS0FBSUE7SUFBZSxDQUFDLENBQUM7SUFBQyxJQUFJQyxLQUFLO0lBQUNILE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLGNBQWMsRUFBQztNQUFDRCxLQUFLQSxDQUFDRSxDQUFDLEVBQUM7UUFBQ0YsS0FBSyxHQUFDRSxDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSUMsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUMsTUFBTUEsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUM7SUFFdEssTUFBTUosZUFBZSxHQUFHLElBQUlDLEtBQUssQ0FBQ0ksVUFBVSxDQUFDLE9BQU8sQ0FBQztJQUFDQyxzQkFBQTtFQUFBLFNBQUFDLFdBQUE7SUFBQSxPQUFBRCxzQkFBQSxDQUFBQyxXQUFBO0VBQUE7RUFBQUQsc0JBQUE7QUFBQTtFQUFBRSxJQUFBO0VBQUFDLEtBQUE7QUFBQSxHOzs7Ozs7Ozs7Ozs7OztJQ0Y3RCxJQUFJQyxhQUFhO0lBQUNaLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLHNDQUFzQyxFQUFDO01BQUNTLE9BQU9BLENBQUNSLENBQUMsRUFBQztRQUFDTyxhQUFhLEdBQUNQLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBckdMLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDO01BQUNhLEtBQUssRUFBQ0EsQ0FBQSxLQUFJQSxLQUFLO01BQUNDLGNBQWMsRUFBQ0EsQ0FBQSxLQUFJQSxjQUFjO01BQUNDLFVBQVUsRUFBQ0EsQ0FBQSxLQUFJQTtJQUFVLENBQUMsQ0FBQztJQUFDLElBQUlDLE1BQU07SUFBQ2pCLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLGVBQWUsRUFBQztNQUFDYSxNQUFNQSxDQUFDWixDQUFDLEVBQUM7UUFBQ1ksTUFBTSxHQUFDWixDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSUYsS0FBSztJQUFDSCxNQUFNLENBQUNJLElBQUksQ0FBQyxjQUFjLEVBQUM7TUFBQ0QsS0FBS0EsQ0FBQ0UsQ0FBQyxFQUFDO1FBQUNGLEtBQUssR0FBQ0UsQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlhLEtBQUs7SUFBQ2xCLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLGNBQWMsRUFBQztNQUFDYyxLQUFLQSxDQUFDYixDQUFDLEVBQUM7UUFBQ2EsS0FBSyxHQUFDYixDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSWMsS0FBSztJQUFDbkIsTUFBTSxDQUFDSSxJQUFJLENBQUMsY0FBYyxFQUFDO01BQUNlLEtBQUtBLENBQUNkLENBQUMsRUFBQztRQUFDYyxLQUFLLEdBQUNkLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJQyxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxNQUFNQSxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQztJQUt0WSxNQUFNUSxLQUFLLEdBQUcsSUFBSVgsS0FBSyxDQUFDSSxVQUFVLENBQUMsT0FBTyxDQUFDO0lBRTNDLE1BQU1RLGNBQWMsR0FBRyxDQUM1QixhQUFhLEVBQ2IsUUFBUSxFQUNSLFdBQVcsRUFDWCxPQUFPLEVBQ1AsU0FBUyxFQUNULFVBQVUsRUFDVixVQUFVLEVBQ1YsT0FBTyxDQUNSO0lBRU0sTUFBTUMsVUFBVSxHQUFHLENBQ3hCO01BQUVJLElBQUksRUFBRSxLQUFLO01BQUVDLEtBQUssRUFBRTtJQUFVLENBQUMsRUFDakM7TUFBRUQsSUFBSSxFQUFFLFNBQVM7TUFBRUMsS0FBSyxFQUFFO0lBQVUsQ0FBQyxFQUNyQztNQUFFRCxJQUFJLEVBQUUsYUFBYTtNQUFFQyxLQUFLLEVBQUU7SUFBVSxDQUFDLEVBQ3pDO01BQUVELElBQUksRUFBRSxlQUFlO01BQUVDLEtBQUssRUFBRTtJQUFVLENBQUMsRUFDM0M7TUFBRUQsSUFBSSxFQUFFLFFBQVE7TUFBRUMsS0FBSyxFQUFFO0lBQVUsQ0FBQyxFQUNwQztNQUFFRCxJQUFJLEVBQUUsU0FBUztNQUFFQyxLQUFLLEVBQUU7SUFBVSxDQUFDLENBQ3RDO0lBRUQsSUFBSUosTUFBTSxDQUFDSyxRQUFRLEVBQUU7TUFDbkI7TUFDQUwsTUFBTSxDQUFDTSxPQUFPLENBQUMsT0FBTyxFQUFFLGtCQUFpQjtRQUFBLElBQUFDLFdBQUE7UUFDdkMsSUFBSSxDQUFDLElBQUksQ0FBQ0MsTUFBTSxFQUFFO1VBQ2hCLE9BQU8sSUFBSSxDQUFDQyxLQUFLLENBQUMsQ0FBQztRQUNyQjs7UUFFQTtRQUNBLE1BQU1DLElBQUksR0FBRyxNQUFNVixNQUFNLENBQUNXLEtBQUssQ0FBQ0MsWUFBWSxDQUFDLElBQUksQ0FBQ0osTUFBTSxDQUFDO1FBQ3pELE1BQU1LLE9BQU8sR0FBR0gsSUFBSSxhQUFKQSxJQUFJLHdCQUFBSCxXQUFBLEdBQUpHLElBQUksQ0FBRUksS0FBSyxjQUFBUCxXQUFBLHVCQUFYQSxXQUFBLENBQWFRLFFBQVEsQ0FBQyxPQUFPLENBQUM7O1FBRTlDO1FBQ0EsSUFBSUYsT0FBTyxFQUFFO1VBQ1gsT0FBT2hCLEtBQUssQ0FBQ21CLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRTtZQUNwQkMsSUFBSSxFQUFFO2NBQUVDLFNBQVMsRUFBRSxDQUFDO1lBQUUsQ0FBQztZQUN2QkMsTUFBTSxFQUFFO2NBQ05DLEtBQUssRUFBRSxDQUFDO2NBQ1JDLFdBQVcsRUFBRSxDQUFDO2NBQ2RDLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLE9BQU8sRUFBRSxDQUFDO2NBQ1ZDLFFBQVEsRUFBRSxDQUFDO2NBQ1hDLE1BQU0sRUFBRSxDQUFDO2NBQ1RDLFVBQVUsRUFBRSxDQUFDO2NBQ2JDLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLFFBQVEsRUFBRSxDQUFDO2NBQ1hDLE1BQU0sRUFBRSxDQUFDO2NBQ1RDLFFBQVEsRUFBRSxDQUFDO2NBQ1hDLFdBQVcsRUFBRSxDQUFDO2NBQ2RDLEtBQUssRUFBRSxDQUFDO2NBQ1JkLFNBQVMsRUFBRSxDQUFDO2NBQ1plLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLFNBQVMsRUFBRSxDQUFDO2NBQ1pDLFNBQVMsRUFBRTtZQUNiO1VBQ0YsQ0FBQyxDQUFDO1FBQ0o7O1FBRUE7UUFDQSxPQUFPdEMsS0FBSyxDQUFDbUIsSUFBSSxDQUFDO1VBQ2hCb0IsR0FBRyxFQUFFLENBQ0g7WUFBRVYsVUFBVSxFQUFFLElBQUksQ0FBQ2xCO1VBQU8sQ0FBQyxFQUMzQjtZQUFFeUIsU0FBUyxFQUFFLElBQUksQ0FBQ3pCO1VBQU8sQ0FBQztRQUU5QixDQUFDLEVBQUU7VUFDRFMsSUFBSSxFQUFFO1lBQUVDLFNBQVMsRUFBRSxDQUFDO1VBQUUsQ0FBQztVQUN2QkMsTUFBTSxFQUFFO1lBQ05DLEtBQUssRUFBRSxDQUFDO1lBQ1JDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLE9BQU8sRUFBRSxDQUFDO1lBQ1ZDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFVBQVUsRUFBRSxDQUFDO1lBQ2JDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLEtBQUssRUFBRSxDQUFDO1lBQ1JkLFNBQVMsRUFBRSxDQUFDO1lBQ1plLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRTtVQUNiO1FBQ0YsQ0FBQyxDQUFDO01BQ0osQ0FBQyxDQUFDOztNQUVGO01BQ0FuQyxNQUFNLENBQUNNLE9BQU8sQ0FBQyxXQUFXLEVBQUUsWUFBVztRQUNyQytCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdDQUFnQyxDQUFDO1FBQzdDLElBQUksQ0FBQyxJQUFJLENBQUM5QixNQUFNLEVBQUU7VUFDaEI2QixPQUFPLENBQUNDLEdBQUcsQ0FBQyw0QkFBNEIsQ0FBQztVQUN6QyxPQUFPLElBQUksQ0FBQzdCLEtBQUssQ0FBQyxDQUFDO1FBQ3JCOztRQUVBO1FBQ0EsTUFBTThCLEtBQUssR0FBRzFDLEtBQUssQ0FBQ21CLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDd0IsS0FBSyxDQUFDLENBQUM7UUFDcENILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGNBQWMsRUFBRUMsS0FBSyxDQUFDRSxNQUFNLENBQUM7O1FBRXpDO1FBQ0EsTUFBTUMsT0FBTyxHQUFHLElBQUlDLEdBQUcsQ0FBQyxDQUFDO1FBQ3pCSixLQUFLLENBQUNLLE9BQU8sQ0FBQ0MsSUFBSSxJQUFJO1VBQ3BCO1VBQ0EsSUFBSUEsSUFBSSxDQUFDZCxXQUFXLEVBQUU7WUFDcEJjLElBQUksQ0FBQ2QsV0FBVyxDQUFDYSxPQUFPLENBQUNFLFVBQVUsSUFBSTtjQUNyQyxJQUFJQSxVQUFVLENBQUNDLFVBQVUsRUFBRTtnQkFDekJMLE9BQU8sQ0FBQ00sR0FBRyxDQUFDQyxNQUFNLENBQUNILFVBQVUsQ0FBQ0MsVUFBVSxDQUFDLENBQUM7Y0FDNUM7WUFDRixDQUFDLENBQUM7VUFDSjtVQUNBO1VBQ0EsSUFBSUYsSUFBSSxDQUFDYixLQUFLLEVBQUU7WUFDZGEsSUFBSSxDQUFDYixLQUFLLENBQUNZLE9BQU8sQ0FBQ3pELElBQUksSUFBSTtjQUN6QixJQUFJQSxJQUFJLENBQUMrRCxPQUFPLEVBQUU7Z0JBQ2hCUixPQUFPLENBQUNNLEdBQUcsQ0FBQ0MsTUFBTSxDQUFDOUQsSUFBSSxDQUFDK0QsT0FBTyxDQUFDLENBQUM7Y0FDbkM7WUFDRixDQUFDLENBQUM7VUFDSjtVQUNBO1VBQ0EsSUFBSUwsSUFBSSxDQUFDbkIsVUFBVSxFQUFFO1lBQ25CbUIsSUFBSSxDQUFDbkIsVUFBVSxDQUFDa0IsT0FBTyxDQUFDcEMsTUFBTSxJQUFJO2NBQ2hDa0MsT0FBTyxDQUFDTSxHQUFHLENBQUNDLE1BQU0sQ0FBQ3pDLE1BQU0sQ0FBQyxDQUFDO1lBQzdCLENBQUMsQ0FBQztVQUNKO1FBQ0YsQ0FBQyxDQUFDO1FBRUYsTUFBTTJDLFdBQVcsR0FBR0MsS0FBSyxDQUFDQyxJQUFJLENBQUNYLE9BQU8sQ0FBQztRQUN2Q0wsT0FBTyxDQUFDQyxHQUFHLENBQUMsK0JBQStCLEVBQUVhLFdBQVcsQ0FBQzs7UUFFekQ7UUFDQSxNQUFNeEMsS0FBSyxHQUFHWCxNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUM3QjtVQUFFc0MsR0FBRyxFQUFFO1lBQUVDLEdBQUcsRUFBRUo7VUFBWTtRQUFFLENBQUMsRUFDN0I7VUFDRWhDLE1BQU0sRUFBRTtZQUNObUMsR0FBRyxFQUFFLENBQUM7WUFDTkUsTUFBTSxFQUFFLENBQUM7WUFDVDFDLEtBQUssRUFBRSxDQUFDO1lBQ1IsbUJBQW1CLEVBQUUsQ0FBQztZQUN0QixrQkFBa0IsRUFBRSxDQUFDO1lBQ3JCLGNBQWMsRUFBRSxDQUFDO1lBQ2pCLG9CQUFvQixFQUFFLENBQUM7WUFDdkIsZ0JBQWdCLEVBQUUsQ0FBQztZQUNuQixrQkFBa0IsRUFBRSxDQUFDO1lBQ3JCSSxTQUFTLEVBQUU7VUFDYjtRQUNGLENBQ0YsQ0FBQyxDQUFDc0IsS0FBSyxDQUFDLENBQUM7UUFFVEgsT0FBTyxDQUFDQyxHQUFHLENBQUMsY0FBYyxFQUFFM0IsS0FBSyxDQUFDOEMsR0FBRyxDQUFDQyxDQUFDO1VBQUEsSUFBQUMsVUFBQSxFQUFBQyxXQUFBO1VBQUEsT0FBSztZQUMxQ04sR0FBRyxFQUFFSSxDQUFDLENBQUNKLEdBQUc7WUFDVm5ELElBQUksRUFBRSxHQUFBMEQsTUFBQSxDQUFHLEVBQUFGLFVBQUEsR0FBQUQsQ0FBQyxDQUFDSSxPQUFPLGNBQUFILFVBQUEsdUJBQVRBLFVBQUEsQ0FBV0ksU0FBUyxLQUFJLEVBQUUsT0FBQUYsTUFBQSxDQUFJLEVBQUFELFdBQUEsR0FBQUYsQ0FBQyxDQUFDSSxPQUFPLGNBQUFGLFdBQUEsdUJBQVRBLFdBQUEsQ0FBV0ksUUFBUSxLQUFJLEVBQUUsRUFBR0MsSUFBSSxDQUFDLENBQUM7WUFDekVDLFVBQVUsRUFBRSxDQUFDLENBQUNSLENBQUMsQ0FBQ0k7VUFDbEIsQ0FBQztRQUFBLENBQUMsQ0FBQyxDQUFDOztRQUVKO1FBQ0EsT0FBTzlELE1BQU0sQ0FBQ1csS0FBSyxDQUFDSyxJQUFJLENBQ3RCO1VBQUVzQyxHQUFHLEVBQUU7WUFBRUMsR0FBRyxFQUFFSjtVQUFZO1FBQUUsQ0FBQyxFQUM3QjtVQUNBaEMsTUFBTSxFQUFFO1lBQ05tQyxHQUFHLEVBQUUsQ0FBQztZQUNKRSxNQUFNLEVBQUUsQ0FBQztZQUNUMUMsS0FBSyxFQUFFLENBQUM7WUFDVixtQkFBbUIsRUFBRSxDQUFDO1lBQ3RCLGtCQUFrQixFQUFFLENBQUM7WUFDbkIsY0FBYyxFQUFFLENBQUM7WUFDakIsb0JBQW9CLEVBQUUsQ0FBQztZQUN2QixnQkFBZ0IsRUFBRSxDQUFDO1lBQ25CLGtCQUFrQixFQUFFLENBQUM7WUFDckJJLFNBQVMsRUFBRTtVQUNiO1FBQ0YsQ0FDRixDQUFDO01BQ0gsQ0FBQyxDQUFDOztNQUVGO01BQ0FsQixNQUFNLENBQUNNLE9BQU8sQ0FBQyxNQUFNLEVBQUUsVUFBUzZELE1BQU0sRUFBRTtRQUFBLElBQUFDLFlBQUE7UUFDdENuRSxLQUFLLENBQUNrRSxNQUFNLEVBQUVsQixNQUFNLENBQUM7UUFFckIsSUFBSSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sRUFBRTtVQUNoQixPQUFPLElBQUksQ0FBQ0MsS0FBSyxDQUFDLENBQUM7UUFDckI7UUFFQSxNQUFNQyxJQUFJLEdBQUdWLE1BQU0sQ0FBQ1csS0FBSyxDQUFDMEQsT0FBTyxDQUFDLElBQUksQ0FBQzdELE1BQU0sQ0FBQztRQUM5QyxNQUFNSyxPQUFPLEdBQUdILElBQUksYUFBSkEsSUFBSSx3QkFBQTBELFlBQUEsR0FBSjFELElBQUksQ0FBRUksS0FBSyxjQUFBc0QsWUFBQSx1QkFBWEEsWUFBQSxDQUFhckQsUUFBUSxDQUFDLE9BQU8sQ0FBQzs7UUFFOUM7UUFDQSxNQUFNdUQsTUFBTSxHQUFHekUsS0FBSyxDQUFDbUIsSUFBSSxDQUN2QjtVQUFFc0MsR0FBRyxFQUFFYTtRQUFPLENBQUMsRUFDZjtVQUNFaEQsTUFBTSxFQUFFO1lBQ05DLEtBQUssRUFBRSxDQUFDO1lBQ1JDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLE9BQU8sRUFBRSxDQUFDO1lBQ1ZDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFVBQVUsRUFBRSxDQUFDO1lBQ2JDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLE1BQU0sRUFBRSxDQUFDO1lBQ1RDLFFBQVEsRUFBRSxDQUFDO1lBQ1hDLFdBQVcsRUFBRSxDQUFDO1lBQ2RDLEtBQUssRUFBRSxDQUFDO1lBQ1JkLFNBQVMsRUFBRSxDQUFDO1lBQ1plLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRSxDQUFDO1lBQ1pDLFNBQVMsRUFBRTtVQUNiO1FBQ0YsQ0FDRixDQUFDOztRQUVEO1FBQ0FFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGtCQUFrQixFQUFFNkIsTUFBTSxDQUFDO1FBQ3ZDRyxNQUFNLENBQUNDLE9BQU8sQ0FBQztVQUNiQyxLQUFLLEVBQUdDLEdBQUcsSUFBS3BDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDRCQUE0QixFQUFFbUMsR0FBRyxDQUFDbkIsR0FBRyxDQUFDO1VBQ2xFb0IsT0FBTyxFQUFHRCxHQUFHLElBQUtwQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyw4QkFBOEIsRUFBRW1DLEdBQUcsQ0FBQ25CLEdBQUcsQ0FBQztVQUN0RXFCLE9BQU8sRUFBR0YsR0FBRyxJQUFLcEMsT0FBTyxDQUFDQyxHQUFHLENBQUMsZ0NBQWdDLEVBQUVtQyxHQUFHLENBQUNuQixHQUFHO1FBQ3pFLENBQUMsQ0FBQztRQUVGLE9BQU9nQixNQUFNO01BQ2YsQ0FBQyxDQUFDO0lBQ0o7SUFFQXRFLE1BQU0sQ0FBQzRFLE9BQU8sQ0FBQztNQUNiLE1BQU0sY0FBY0MsQ0FBQ2hDLElBQUksRUFBRTtRQUN6QjVDLEtBQUssQ0FBQzRDLElBQUksRUFBRTtVQUNWekIsS0FBSyxFQUFFNkIsTUFBTTtVQUNiNUIsV0FBVyxFQUFFNEIsTUFBTTtVQUNuQjNCLFNBQVMsRUFBRXdELElBQUk7VUFDZnZELE9BQU8sRUFBRXVELElBQUk7VUFDYnRELFFBQVEsRUFBRXlCLE1BQU07VUFDaEJ4QixNQUFNLEVBQUV3QixNQUFNO1VBQ2R2QixVQUFVLEVBQUUwQixLQUFLO1VBQ2pCekIsU0FBUyxFQUFFeUIsS0FBSztVQUNoQnhCLFFBQVEsRUFBRXFCLE1BQU07VUFDaEJwQixNQUFNLEVBQUV1QixLQUFLO1VBQ2J0QixRQUFRLEVBQUVpRDtRQUNaLENBQUMsQ0FBQztRQUVGLElBQUksQ0FBQyxJQUFJLENBQUN2RSxNQUFNLEVBQUU7VUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsaUJBQWlCLENBQUM7UUFDM0M7UUFFQTNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQixFQUFFTyxJQUFJLENBQUMsQ0FBQyxDQUFDOztRQUV6QztRQUNBLE1BQU1vQyxrQkFBa0IsR0FBR3BDLElBQUksQ0FBQ2xCLFNBQVMsQ0FBQzhCLEdBQUcsQ0FBQ3lCLElBQUksS0FBSztVQUNyREMsSUFBSSxFQUFFRCxJQUFJLENBQUNDLElBQUk7VUFDZkMsU0FBUyxFQUFFRixJQUFJLENBQUNFLFNBQVMsSUFBSTtRQUMvQixDQUFDLENBQUMsQ0FBQztRQUVILE1BQU1DLFlBQVksR0FBQTFGLGFBQUEsQ0FBQUEsYUFBQSxLQUNia0QsSUFBSTtVQUNQM0IsU0FBUyxFQUFFLElBQUk0RCxJQUFJLENBQUMsQ0FBQztVQUNyQjdDLFNBQVMsRUFBRSxJQUFJLENBQUN6QixNQUFNO1VBQ3RCMEIsU0FBUyxFQUFFLElBQUk0QyxJQUFJLENBQUMsQ0FBQztVQUNyQjNDLFNBQVMsRUFBRSxJQUFJLENBQUMzQixNQUFNO1VBQ3RCc0IsUUFBUSxFQUFFZSxJQUFJLENBQUNmLFFBQVEsSUFBSSxDQUFDO1VBQzVCTCxNQUFNLEVBQUUsU0FBUztVQUFFO1VBQ25CRSxTQUFTLEVBQUVzRCxrQkFBa0I7VUFDN0JwRCxNQUFNLEVBQUVnQixJQUFJLENBQUNoQixNQUFNLElBQUksRUFBRTtVQUN6QkQsUUFBUSxFQUFFaUIsSUFBSSxDQUFDakIsUUFBUSxJQUFJLEVBQUU7VUFDN0JGLFVBQVUsRUFBRW1CLElBQUksQ0FBQ25CLFVBQVUsSUFBSTtRQUFFLEVBQ2xDO1FBRURXLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDZCQUE2QixFQUFFK0MsWUFBWSxDQUFDLENBQUMsQ0FBQzs7UUFFMUQsSUFBSTtVQUNGLE1BQU1DLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDMEYsV0FBVyxDQUFDRixZQUFZLENBQUM7VUFDcERoRCxPQUFPLENBQUNDLEdBQUcsQ0FBQyw0QkFBNEIsRUFBRWdELE1BQU0sQ0FBQyxDQUFDLENBQUM7VUFDbkQsT0FBT0EsTUFBTTtRQUNmLENBQUMsQ0FBQyxPQUFPRSxLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQyxzQkFBc0IsRUFBRUEsS0FBSyxDQUFDO1VBQzVDLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxzQkFBc0IsRUFBRVEsS0FBSyxDQUFDQyxPQUFPLENBQUM7UUFDL0Q7TUFDRixDQUFDO01BRUQsTUFBTSxjQUFjQyxDQUFDdkIsTUFBTSxFQUFFdEIsSUFBSSxFQUFFO1FBQ2pDLElBQUk7VUFBQSxJQUFBOEMsWUFBQTtVQUNGdEQsT0FBTyxDQUFDQyxHQUFHLENBQUMsdUJBQXVCLEVBQUU7WUFBRTZCLE1BQU07WUFBRXRCO1VBQUssQ0FBQyxDQUFDO1VBRXRENUMsS0FBSyxDQUFDa0UsTUFBTSxFQUFFbEIsTUFBTSxDQUFDO1VBQ3JCaEQsS0FBSyxDQUFDNEMsSUFBSSxFQUFFO1lBQ1Z6QixLQUFLLEVBQUU2QixNQUFNO1lBQ2I1QixXQUFXLEVBQUU0QixNQUFNO1lBQ25CM0IsU0FBUyxFQUFFd0QsSUFBSTtZQUNmdkQsT0FBTyxFQUFFdUQsSUFBSTtZQUNidEQsUUFBUSxFQUFFeUIsTUFBTTtZQUNoQnZCLFVBQVUsRUFBRTBCLEtBQUs7WUFDakJ6QixTQUFTLEVBQUV5QixLQUFLO1lBQ2hCeEIsUUFBUSxFQUFFMUIsS0FBSyxDQUFDMEYsUUFBUSxDQUFDM0MsTUFBTSxDQUFDO1lBQ2hDcEIsTUFBTSxFQUFFM0IsS0FBSyxDQUFDMEYsUUFBUSxDQUFDeEMsS0FBSyxDQUFDO1lBQzdCdEIsUUFBUSxFQUFFNUIsS0FBSyxDQUFDMEYsUUFBUSxDQUFDYixNQUFNLENBQUM7WUFDaEN0RCxNQUFNLEVBQUV2QixLQUFLLENBQUMwRixRQUFRLENBQUMzQyxNQUFNLENBQUM7WUFDOUJsQixXQUFXLEVBQUU3QixLQUFLLENBQUMwRixRQUFRLENBQUN4QyxLQUFLO1VBQ25DLENBQUMsQ0FBQztVQUVGLElBQUksQ0FBQyxJQUFJLENBQUM1QyxNQUFNLEVBQUU7WUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsOENBQThDLENBQUM7VUFDMUY7VUFFQSxNQUFNYSxZQUFZLEdBQUcsTUFBTWhHLEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQ3JELElBQUksQ0FBQzBCLFlBQVksRUFBRTtZQUNqQixNQUFNLElBQUk3RixNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLGdCQUFnQixDQUFDO1VBQ3ZEOztVQUVBO1VBQ0EsTUFBTXRFLElBQUksR0FBRyxNQUFNVixNQUFNLENBQUNXLEtBQUssQ0FBQ0MsWUFBWSxDQUFDLElBQUksQ0FBQ0osTUFBTSxDQUFDO1VBQ3pELE1BQU1LLE9BQU8sR0FBR0gsSUFBSSxhQUFKQSxJQUFJLHdCQUFBaUYsWUFBQSxHQUFKakYsSUFBSSxDQUFFSSxLQUFLLGNBQUE2RSxZQUFBLHVCQUFYQSxZQUFBLENBQWE1RSxRQUFRLENBQUMsT0FBTyxDQUFDO1VBRTlDLElBQUksQ0FBQ0YsT0FBTyxJQUFJLENBQUNnRixZQUFZLENBQUNuRSxVQUFVLENBQUNYLFFBQVEsQ0FBQyxJQUFJLENBQUNQLE1BQU0sQ0FBQyxFQUFFO1lBQzlELE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDRDQUE0QyxDQUFDO1VBQ3hGOztVQUVBO1VBQ0EsTUFBTWMsY0FBYyxHQUFHakQsSUFBSSxDQUFDbEIsU0FBUyxDQUFDb0UsTUFBTSxDQUFDYixJQUFJLElBQUlBLElBQUksQ0FBQ0UsU0FBUyxDQUFDLENBQUMzQyxNQUFNO1VBQzNFLE1BQU11RCxVQUFVLEdBQUduRCxJQUFJLENBQUNsQixTQUFTLENBQUNjLE1BQU07VUFDeEMsTUFBTVgsUUFBUSxHQUFHa0UsVUFBVSxHQUFHLENBQUMsR0FBR0MsSUFBSSxDQUFDQyxLQUFLLENBQUVKLGNBQWMsR0FBR0UsVUFBVSxHQUFJLEdBQUcsQ0FBQyxHQUFHLENBQUM7O1VBRXJGO1VBQ0EsSUFBSXZFLE1BQU0sR0FBR29CLElBQUksQ0FBQ3BCLE1BQU07VUFDeEIsSUFBSUssUUFBUSxLQUFLLEdBQUcsRUFBRTtZQUNwQkwsTUFBTSxHQUFHLFdBQVc7VUFDdEIsQ0FBQyxNQUFNLElBQUlLLFFBQVEsR0FBRyxDQUFDLEVBQUU7WUFDdkJMLE1BQU0sR0FBRyxhQUFhO1VBQ3hCLENBQUMsTUFBTTtZQUNMQSxNQUFNLEdBQUcsU0FBUztVQUNwQjtVQUVBLE1BQU0wRSxZQUFZLEdBQUF4RyxhQUFBLENBQUFBLGFBQUEsS0FDYmtELElBQUk7WUFDUFgsU0FBUyxFQUFFLElBQUk0QyxJQUFJLENBQUMsQ0FBQztZQUNyQjNDLFNBQVMsRUFBRSxJQUFJLENBQUMzQixNQUFNO1lBQ3RCc0IsUUFBUTtZQUNSTCxNQUFNO1lBQ05HLFFBQVEsRUFBRWlCLElBQUksQ0FBQ2pCLFFBQVEsSUFBSWlFLFlBQVksQ0FBQ2pFLFFBQVEsSUFBSSxFQUFFO1lBQ3REQyxNQUFNLEVBQUVnQixJQUFJLENBQUNoQixNQUFNLElBQUlnRSxZQUFZLENBQUNoRSxNQUFNLElBQUksRUFBRTtZQUNoREUsV0FBVyxFQUFFYyxJQUFJLENBQUNkLFdBQVcsSUFBSThELFlBQVksQ0FBQzlELFdBQVcsSUFBSTtVQUFFLEVBQ2hFO1VBRUQsTUFBTXVELE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDdUcsV0FBVyxDQUNwQztZQUFFOUMsR0FBRyxFQUFFYTtVQUFPLENBQUMsRUFDZjtZQUFFa0MsSUFBSSxFQUFFRjtVQUFhLENBQ3ZCLENBQUM7VUFFRCxJQUFJYixNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsdUJBQXVCLENBQUM7VUFDbEU7VUFFQSxPQUFPTSxNQUFNO1FBQ2YsQ0FBQyxDQUFDLE9BQU9FLEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHNCQUFzQixFQUFFQSxLQUFLLENBQUM7VUFDNUMsSUFBSUEsS0FBSyxZQUFZeEYsTUFBTSxDQUFDZ0YsS0FBSyxFQUFFO1lBQ2pDLE1BQU1RLEtBQUs7VUFDYjtVQUNBLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxjQUFjLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxJQUFJLDhCQUE4QixDQUFDO1FBQ3pGO01BQ0YsQ0FBQztNQUVELE1BQU0sY0FBY2EsQ0FBQ25DLE1BQU0sRUFBRTtRQUMzQmxFLEtBQUssQ0FBQ2tFLE1BQU0sRUFBRWxCLE1BQU0sQ0FBQztRQUVyQixJQUFJLENBQUMsSUFBSSxDQUFDekMsTUFBTSxFQUFFO1VBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixDQUFDO1FBQzNDO1FBRUEsSUFBSTtVQUNGLE1BQU1NLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDMEcsV0FBVyxDQUFDcEMsTUFBTSxDQUFDO1VBRTlDLElBQUltQixNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxXQUFXLEVBQUUsZ0JBQWdCLENBQUM7VUFDdkQ7VUFFQSxPQUFPTSxNQUFNO1FBQ2YsQ0FBQyxDQUFDLE9BQU9FLEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHNCQUFzQixFQUFFQSxLQUFLLENBQUM7VUFDNUMsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLG9CQUFvQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUM3RDtNQUNGLENBQUM7TUFFRCxzQkFBc0JlLENBQUNyQyxNQUFNLEVBQUVyQyxRQUFRLEVBQUU7UUFDdkM3QixLQUFLLENBQUNrRSxNQUFNLEVBQUVsQixNQUFNLENBQUM7UUFDckJoRCxLQUFLLENBQUM2QixRQUFRLEVBQUVpRCxNQUFNLENBQUM7UUFFdkIsSUFBSSxDQUFDLElBQUksQ0FBQ3ZFLE1BQU0sRUFBRTtVQUNoQixNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztRQUMzQztRQUVBLE1BQU1uQyxJQUFJLEdBQUdoRCxLQUFLLENBQUN3RSxPQUFPLENBQUNGLE1BQU0sQ0FBQztRQUNsQyxJQUFJLENBQUN0QixJQUFJLEVBQUU7VUFDVCxNQUFNLElBQUk3QyxNQUFNLENBQUNnRixLQUFLLENBQUMsaUJBQWlCLENBQUM7UUFDM0M7O1FBRUE7UUFDQSxJQUFJLENBQUNuQyxJQUFJLENBQUNuQixVQUFVLENBQUNYLFFBQVEsQ0FBQyxJQUFJLENBQUNQLE1BQU0sQ0FBQyxFQUFFO1VBQzFDLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLHFDQUFxQyxDQUFDO1FBQy9EOztRQUVBO1FBQ0EsSUFBSXZELE1BQU0sR0FBR29CLElBQUksQ0FBQ3BCLE1BQU07UUFDeEIsSUFBSUssUUFBUSxLQUFLLEdBQUcsRUFBRTtVQUNwQkwsTUFBTSxHQUFHLFdBQVc7UUFDdEIsQ0FBQyxNQUFNLElBQUlLLFFBQVEsR0FBRyxDQUFDLEVBQUU7VUFDdkJMLE1BQU0sR0FBRyxhQUFhO1FBQ3hCO1FBRUEsT0FBTzVCLEtBQUssQ0FBQzRHLE1BQU0sQ0FBQ3RDLE1BQU0sRUFBRTtVQUMxQmtDLElBQUksRUFBRTtZQUNKdkUsUUFBUTtZQUNSTCxNQUFNO1lBQ05TLFNBQVMsRUFBRSxJQUFJNEMsSUFBSSxDQUFDLENBQUM7WUFDckIzQyxTQUFTLEVBQUUsSUFBSSxDQUFDM0I7VUFDbEI7UUFDRixDQUFDLENBQUM7TUFDSixDQUFDO01BRUQsTUFBTSwyQkFBMkJrRyxDQUFDdkMsTUFBTSxFQUFFd0MsU0FBUyxFQUFFO1FBQ25ELElBQUk7VUFBQSxJQUFBQyxZQUFBO1VBQ0Z2RSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxvQ0FBb0MsRUFBRTtZQUFFNkIsTUFBTTtZQUFFd0MsU0FBUztZQUFFbkcsTUFBTSxFQUFFLElBQUksQ0FBQ0E7VUFBTyxDQUFDLENBQUM7VUFFN0YsSUFBSSxDQUFDLElBQUksQ0FBQ0EsTUFBTSxFQUFFO1lBQ2hCNkIsT0FBTyxDQUFDQyxHQUFHLENBQUMsa0JBQWtCLENBQUM7WUFDL0IsTUFBTSxJQUFJdEMsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDhDQUE4QyxDQUFDO1VBQzFGOztVQUVBO1VBQ0EsSUFBSSxDQUFDYixNQUFNLElBQUksT0FBT0EsTUFBTSxLQUFLLFFBQVEsRUFBRTtZQUN6QzlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGlCQUFpQixFQUFFNkIsTUFBTSxDQUFDO1lBQ3RDLE1BQU0sSUFBSW5FLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsaUJBQWlCLENBQUM7VUFDNUQ7VUFFQSxJQUFJLE9BQU8yQixTQUFTLEtBQUssUUFBUSxJQUFJQSxTQUFTLEdBQUcsQ0FBQyxFQUFFO1lBQ2xEdEUsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0JBQW9CLEVBQUVxRSxTQUFTLENBQUM7WUFDNUMsTUFBTSxJQUFJM0csTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGVBQWUsRUFBRSw4QkFBOEIsQ0FBQztVQUN6RTtVQUVBLE1BQU1uQyxJQUFJLEdBQUcsTUFBTWhELEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQzdDOUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsYUFBYSxFQUFFTyxJQUFJLENBQUM7VUFFaEMsSUFBSSxDQUFDQSxJQUFJLEVBQUU7WUFDVFIsT0FBTyxDQUFDQyxHQUFHLENBQUMsZ0JBQWdCLENBQUM7WUFDN0IsTUFBTSxJQUFJdEMsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLFdBQVcsRUFBRSxnQkFBZ0IsQ0FBQztVQUN2RDs7VUFFQTtVQUNBLE1BQU10RSxJQUFJLEdBQUcsTUFBTVYsTUFBTSxDQUFDVyxLQUFLLENBQUNDLFlBQVksQ0FBQyxJQUFJLENBQUNKLE1BQU0sQ0FBQztVQUN6RDZCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGFBQWEsRUFBRTVCLElBQUksQ0FBQztVQUVoQyxNQUFNRyxPQUFPLEdBQUdILElBQUksYUFBSkEsSUFBSSx3QkFBQWtHLFlBQUEsR0FBSmxHLElBQUksQ0FBRUksS0FBSyxjQUFBOEYsWUFBQSx1QkFBWEEsWUFBQSxDQUFhN0YsUUFBUSxDQUFDLE9BQU8sQ0FBQztVQUM5Q3NCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLFdBQVcsRUFBRXpCLE9BQU8sQ0FBQztVQUVqQyxJQUFJLENBQUNBLE9BQU8sSUFBSSxDQUFDZ0MsSUFBSSxDQUFDbkIsVUFBVSxDQUFDWCxRQUFRLENBQUMsSUFBSSxDQUFDUCxNQUFNLENBQUMsRUFBRTtZQUN0RDZCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQixFQUFFO2NBQUU5QixNQUFNLEVBQUUsSUFBSSxDQUFDQSxNQUFNO2NBQUVrQixVQUFVLEVBQUVtQixJQUFJLENBQUNuQjtZQUFXLENBQUMsQ0FBQztZQUN6RixNQUFNLElBQUkxQixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsNENBQTRDLENBQUM7VUFDeEY7VUFFQSxNQUFNckQsU0FBUyxHQUFHa0IsSUFBSSxDQUFDbEIsU0FBUyxJQUFJLEVBQUU7VUFDdENVLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQixFQUFFWCxTQUFTLENBQUM7VUFFNUMsSUFBSWdGLFNBQVMsSUFBSWhGLFNBQVMsQ0FBQ2MsTUFBTSxFQUFFO1lBQ2pDSixPQUFPLENBQUNDLEdBQUcsQ0FBQyxxQkFBcUIsRUFBRTtjQUFFcUUsU0FBUztjQUFFRSxlQUFlLEVBQUVsRixTQUFTLENBQUNjO1lBQU8sQ0FBQyxDQUFDO1lBQ3BGLE1BQU0sSUFBSXpDLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsOEJBQThCLENBQUM7VUFDekU7O1VBRUE7VUFDQSxNQUFNOEIsZ0JBQWdCLEdBQUcsQ0FBQyxHQUFHbkYsU0FBUyxDQUFDO1VBQ3ZDbUYsZ0JBQWdCLENBQUNILFNBQVMsQ0FBQyxHQUFBaEgsYUFBQSxDQUFBQSxhQUFBLEtBQ3RCbUgsZ0JBQWdCLENBQUNILFNBQVMsQ0FBQztZQUM5QnZCLFNBQVMsRUFBRSxDQUFDMEIsZ0JBQWdCLENBQUNILFNBQVMsQ0FBQyxDQUFDdkI7VUFBUyxFQUNsRDtVQUVEL0MsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0JBQW9CLEVBQUV3RSxnQkFBZ0IsQ0FBQzs7VUFFbkQ7VUFDQSxNQUFNaEIsY0FBYyxHQUFHZ0IsZ0JBQWdCLENBQUNmLE1BQU0sQ0FBQ2IsSUFBSSxJQUFJQSxJQUFJLENBQUNFLFNBQVMsQ0FBQyxDQUFDM0MsTUFBTTtVQUM3RSxNQUFNdUQsVUFBVSxHQUFHYyxnQkFBZ0IsQ0FBQ3JFLE1BQU07VUFDMUMsTUFBTVgsUUFBUSxHQUFHa0UsVUFBVSxHQUFHLENBQUMsR0FBR0MsSUFBSSxDQUFDQyxLQUFLLENBQUVKLGNBQWMsR0FBR0UsVUFBVSxHQUFJLEdBQUcsQ0FBQyxHQUFHLENBQUM7O1VBRXJGO1VBQ0EsSUFBSXZFLE1BQU07VUFDVixJQUFJSyxRQUFRLEtBQUssR0FBRyxFQUFFO1lBQ3BCTCxNQUFNLEdBQUcsV0FBVztVQUN0QixDQUFDLE1BQU0sSUFBSUssUUFBUSxHQUFHLENBQUMsRUFBRTtZQUN2QkwsTUFBTSxHQUFHLGFBQWE7VUFDeEIsQ0FBQyxNQUFNO1lBQ0xBLE1BQU0sR0FBRyxTQUFTO1VBQ3BCO1VBRUFZLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHFCQUFxQixFQUFFO1lBQ2pDNkIsTUFBTTtZQUNOMkMsZ0JBQWdCO1lBQ2hCaEYsUUFBUTtZQUNSTDtVQUNGLENBQUMsQ0FBQzs7VUFFRjtVQUNBLE1BQU1vRSxZQUFZLEdBQUcsTUFBTWhHLEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQ3JELElBQUksQ0FBQzBCLFlBQVksRUFBRTtZQUNqQixNQUFNLElBQUk3RixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsdUJBQXVCLENBQUM7VUFDbkU7O1VBRUE7VUFDQSxNQUFNK0IsWUFBWSxHQUFHLE1BQU1sSCxLQUFLLENBQUN1RyxXQUFXLENBQzFDO1lBQUU5QyxHQUFHLEVBQUVhO1VBQU8sQ0FBQyxFQUNmO1lBQ0VrQyxJQUFJLEVBQUU7Y0FDSjFFLFNBQVMsRUFBRW1GLGdCQUFnQjtjQUMzQmhGLFFBQVE7Y0FDUkwsTUFBTTtjQUNOUyxTQUFTLEVBQUUsSUFBSTRDLElBQUksQ0FBQyxDQUFDO2NBQ3JCM0MsU0FBUyxFQUFFLElBQUksQ0FBQzNCO1lBQ2xCO1VBQ0YsQ0FDRixDQUFDO1VBRUQ2QixPQUFPLENBQUNDLEdBQUcsQ0FBQyxnQkFBZ0IsRUFBRXlFLFlBQVksQ0FBQztVQUUzQyxJQUFJQSxZQUFZLEtBQUssQ0FBQyxFQUFFO1lBQ3RCLE1BQU0sSUFBSS9HLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsdUJBQXVCLENBQUM7VUFDbEU7O1VBRUE7VUFDQSxNQUFNZ0MsV0FBVyxHQUFHLE1BQU1uSCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUNwRDlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9CQUFvQixFQUFFMEUsV0FBVyxDQUFDO1VBRTlDLE9BQU9ELFlBQVk7UUFDckIsQ0FBQyxDQUFDLE9BQU92QixLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQywrQkFBK0IsRUFBRUEsS0FBSyxDQUFDO1VBQ3JELElBQUlBLEtBQUssWUFBWXhGLE1BQU0sQ0FBQ2dGLEtBQUssRUFBRTtZQUNqQyxNQUFNUSxLQUFLO1VBQ2I7VUFDQSxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsY0FBYyxFQUFFUSxLQUFLLENBQUNDLE9BQU8sSUFBSSw4QkFBOEIsQ0FBQztRQUN6RjtNQUNGLENBQUM7TUFFRCxNQUFNLHFCQUFxQndCLENBQUM5QyxNQUFNLEVBQUUrQyxRQUFRLEVBQUU7UUFDNUMsSUFBSTtVQUNGLElBQUksQ0FBQyxJQUFJLENBQUMxRyxNQUFNLEVBQUU7WUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsOENBQThDLENBQUM7VUFDMUY7VUFFQSxNQUFNbkMsSUFBSSxHQUFHLE1BQU1oRCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUM3QyxJQUFJLENBQUN0QixJQUFJLEVBQUU7WUFDVCxNQUFNLElBQUk3QyxNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLGdCQUFnQixDQUFDO1VBQ3ZEOztVQUVBO1VBQ0EsSUFBSSxDQUFDbkMsSUFBSSxDQUFDbkIsVUFBVSxDQUFDWCxRQUFRLENBQUMsSUFBSSxDQUFDUCxNQUFNLENBQUMsRUFBRTtZQUMxQyxNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx3REFBd0QsQ0FBQztVQUNwRztVQUVBLElBQUksQ0FBQ2tDLFFBQVEsSUFBSSxDQUFDQSxRQUFRLENBQUMvRyxJQUFJLElBQUksQ0FBQytHLFFBQVEsQ0FBQ0MsSUFBSSxFQUFFO1lBQ2pELE1BQU0sSUFBSW5ILE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsbUJBQW1CLENBQUM7VUFDOUQ7O1VBRUE7VUFDQSxNQUFNb0MsVUFBVSxHQUFHbkUsTUFBTSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sQ0FBQzs7VUFFdEM7VUFDQSxNQUFNOEUsTUFBTSxHQUFHLE1BQU16RixLQUFLLENBQUN1RyxXQUFXLENBQ3BDO1lBQUU5QyxHQUFHLEVBQUVhO1VBQU8sQ0FBQyxFQUNmO1lBQ0VrRCxLQUFLLEVBQUU7Y0FDTHRGLFdBQVcsRUFBRTtnQkFDWDVCLElBQUksRUFBRStHLFFBQVEsQ0FBQy9HLElBQUk7Z0JBQ25CbUgsSUFBSSxFQUFFSixRQUFRLENBQUNJLElBQUk7Z0JBQ25CSCxJQUFJLEVBQUVELFFBQVEsQ0FBQ0MsSUFBSTtnQkFDbkJJLFVBQVUsRUFBRSxJQUFJekMsSUFBSSxDQUFDLENBQUM7Z0JBQ3RCL0IsVUFBVSxFQUFFcUU7Y0FDZDtZQUNGLENBQUM7WUFDRGYsSUFBSSxFQUFFO2NBQ0puRSxTQUFTLEVBQUUsSUFBSTRDLElBQUksQ0FBQyxDQUFDO2NBQ3JCM0MsU0FBUyxFQUFFaUY7WUFDYjtVQUNGLENBQ0YsQ0FBQztVQUVELElBQUk5QixNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsMEJBQTBCLENBQUM7VUFDckU7O1VBRUE7VUFDQSxNQUFNZ0MsV0FBVyxHQUFHLE1BQU1uSCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUNwRDlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLCtCQUErQixFQUFFMEUsV0FBVyxDQUFDO1VBQ3pELE9BQU9BLFdBQVc7UUFDcEIsQ0FBQyxDQUFDLE9BQU94QixLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQywwQkFBMEIsRUFBRUEsS0FBSyxDQUFDO1VBQ2hELElBQUlBLEtBQUssWUFBWXhGLE1BQU0sQ0FBQ2dGLEtBQUssRUFBRTtZQUNqQyxNQUFNUSxLQUFLO1VBQ2I7VUFDQSxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsY0FBYyxFQUFFUSxLQUFLLENBQUNDLE9BQU8sSUFBSSw4QkFBOEIsQ0FBQztRQUN6RjtNQUNGLENBQUM7TUFFRCxNQUFNLGVBQWUrQixDQUFDckQsTUFBTSxFQUFFaEYsSUFBSSxFQUFFO1FBQ2xDLElBQUk7VUFDRixJQUFJLENBQUMsSUFBSSxDQUFDcUIsTUFBTSxFQUFFO1lBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDhDQUE4QyxDQUFDO1VBQzFGO1VBRUEsTUFBTW5DLElBQUksR0FBRyxNQUFNaEQsS0FBSyxDQUFDZSxZQUFZLENBQUN1RCxNQUFNLENBQUM7VUFDN0MsSUFBSSxDQUFDdEIsSUFBSSxFQUFFO1lBQ1QsTUFBTSxJQUFJN0MsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLFdBQVcsRUFBRSxnQkFBZ0IsQ0FBQztVQUN2RDs7VUFFQTtVQUNBLElBQUksQ0FBQ25DLElBQUksQ0FBQ25CLFVBQVUsQ0FBQ1gsUUFBUSxDQUFDLElBQUksQ0FBQ1AsTUFBTSxDQUFDLEVBQUU7WUFDMUMsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsa0RBQWtELENBQUM7VUFDOUY7VUFFQSxJQUFJLENBQUM3RixJQUFJLEVBQUU7WUFDVCxNQUFNLElBQUlhLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsc0JBQXNCLENBQUM7VUFDakU7O1VBRUE7VUFDQSxJQUFJO1lBQ0YsSUFBSXlDLEdBQUcsQ0FBQ3RJLElBQUksQ0FBQztVQUNmLENBQUMsQ0FBQyxPQUFPdUksQ0FBQyxFQUFFO1lBQ1YsTUFBTSxJQUFJMUgsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGVBQWUsRUFBRSxvQkFBb0IsQ0FBQztVQUMvRDs7VUFFQTtVQUNBLE1BQU1NLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDdUcsV0FBVyxDQUNwQztZQUFFOUMsR0FBRyxFQUFFYTtVQUFPLENBQUMsRUFDZjtZQUNFa0QsS0FBSyxFQUFFO2NBQ0xyRixLQUFLLEVBQUU7Z0JBQ0wyRixHQUFHLEVBQUV4SSxJQUFJO2dCQUNUeUksT0FBTyxFQUFFLElBQUk5QyxJQUFJLENBQUMsQ0FBQztnQkFDbkI1QixPQUFPLEVBQUVELE1BQU0sQ0FBQyxJQUFJLENBQUN6QyxNQUFNO2NBQzdCO1lBQ0YsQ0FBQztZQUNENkYsSUFBSSxFQUFFO2NBQ0puRSxTQUFTLEVBQUUsSUFBSTRDLElBQUksQ0FBQyxDQUFDO2NBQ3JCM0MsU0FBUyxFQUFFYyxNQUFNLENBQUMsSUFBSSxDQUFDekMsTUFBTTtZQUMvQjtVQUNGLENBQ0YsQ0FBQztVQUVELElBQUk4RSxNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLE1BQU0sSUFBSXRGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxlQUFlLEVBQUUsb0JBQW9CLENBQUM7VUFDL0Q7O1VBRUE7VUFDQSxNQUFNZ0MsV0FBVyxHQUFHLE1BQU1uSCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUNwRDlCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlCQUF5QixFQUFFMEUsV0FBVyxDQUFDO1VBQ25ELE9BQU9BLFdBQVc7UUFDcEIsQ0FBQyxDQUFDLE9BQU94QixLQUFLLEVBQUU7VUFDZG5ELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQyxvQkFBb0IsRUFBRUEsS0FBSyxDQUFDO1VBQzFDLElBQUlBLEtBQUssWUFBWXhGLE1BQU0sQ0FBQ2dGLEtBQUssRUFBRTtZQUNqQyxNQUFNUSxLQUFLO1VBQ2I7VUFDQSxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsY0FBYyxFQUFFUSxLQUFLLENBQUNDLE9BQU8sSUFBSSw4QkFBOEIsQ0FBQztRQUN6RjtNQUNGLENBQUM7TUFFRCxNQUFNLHdCQUF3Qm9DLENBQUMxRCxNQUFNLEVBQUUyRCxlQUFlLEVBQUU7UUFDdEQsSUFBSTtVQUNGLElBQUksQ0FBQyxJQUFJLENBQUN0SCxNQUFNLEVBQUU7WUFDaEIsTUFBTSxJQUFJUixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsOENBQThDLENBQUM7VUFDMUY7VUFFQSxNQUFNbkMsSUFBSSxHQUFHLE1BQU1oRCxLQUFLLENBQUNlLFlBQVksQ0FBQ3VELE1BQU0sQ0FBQztVQUM3QyxJQUFJLENBQUN0QixJQUFJLEVBQUU7WUFDVCxNQUFNLElBQUk3QyxNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLGdCQUFnQixDQUFDO1VBQ3ZEOztVQUVBO1VBQ0EsSUFBSSxDQUFDbkMsSUFBSSxDQUFDZCxXQUFXLElBQUksQ0FBQ2MsSUFBSSxDQUFDZCxXQUFXLENBQUMrRixlQUFlLENBQUMsRUFBRTtZQUMzRCxNQUFNLElBQUk5SCxNQUFNLENBQUNnRixLQUFLLENBQUMsV0FBVyxFQUFFLHNCQUFzQixDQUFDO1VBQzdEO1VBRUEsTUFBTWxDLFVBQVUsR0FBR0QsSUFBSSxDQUFDZCxXQUFXLENBQUMrRixlQUFlLENBQUM7O1VBRXBEO1VBQ0EsTUFBTUMsYUFBYSxHQUFHOUUsTUFBTSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sQ0FBQztVQUN6QyxNQUFNd0gsWUFBWSxHQUFHL0UsTUFBTSxDQUFDSCxVQUFVLENBQUNDLFVBQVUsQ0FBQzs7VUFFbEQ7VUFDQSxJQUFJZ0YsYUFBYSxLQUFLQyxZQUFZLEVBQUU7WUFDbEMsTUFBTSxJQUFJaEksTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLDBDQUEwQyxDQUFDO1VBQ3RGOztVQUVBO1VBQ0EsTUFBTWlELGtCQUFrQixHQUFHLENBQUMsR0FBR3BGLElBQUksQ0FBQ2QsV0FBVyxDQUFDO1VBQ2hEa0csa0JBQWtCLENBQUNDLE1BQU0sQ0FBQ0osZUFBZSxFQUFFLENBQUMsQ0FBQzs7VUFFN0M7VUFDQSxNQUFNeEMsTUFBTSxHQUFHLE1BQU16RixLQUFLLENBQUN1RyxXQUFXLENBQ3BDO1lBQUU5QyxHQUFHLEVBQUVhO1VBQU8sQ0FBQyxFQUNmO1lBQ0VrQyxJQUFJLEVBQUU7Y0FDSnRFLFdBQVcsRUFBRWtHLGtCQUFrQjtjQUMvQi9GLFNBQVMsRUFBRSxJQUFJNEMsSUFBSSxDQUFDLENBQUM7Y0FDckIzQyxTQUFTLEVBQUU0RjtZQUNiO1VBQ0YsQ0FDRixDQUFDO1VBRUQsSUFBSXpDLE1BQU0sS0FBSyxDQUFDLEVBQUU7WUFDaEIsTUFBTSxJQUFJdEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGVBQWUsRUFBRSw2QkFBNkIsQ0FBQztVQUN4RTs7VUFFQTtVQUNBLE1BQU1nQyxXQUFXLEdBQUcsTUFBTW5ILEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQ3BEOUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUNBQWlDLEVBQUUwRSxXQUFXLENBQUM7VUFDM0QsT0FBT0EsV0FBVztRQUNwQixDQUFDLENBQUMsT0FBT3hCLEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLDRCQUE0QixFQUFFQSxLQUFLLENBQUM7VUFDbEQsSUFBSUEsS0FBSyxZQUFZeEYsTUFBTSxDQUFDZ0YsS0FBSyxFQUFFO1lBQ2pDLE1BQU1RLEtBQUs7VUFDYjtVQUNBLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxjQUFjLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxJQUFJLDhCQUE4QixDQUFDO1FBQ3pGO01BQ0YsQ0FBQztNQUVELE1BQU0sa0JBQWtCMEMsQ0FBQ2hFLE1BQU0sRUFBRWlFLFNBQVMsRUFBRTtRQUMxQyxJQUFJO1VBQ0YsSUFBSSxDQUFDLElBQUksQ0FBQzVILE1BQU0sRUFBRTtZQUNoQixNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSw4Q0FBOEMsQ0FBQztVQUMxRjtVQUVBLE1BQU1uQyxJQUFJLEdBQUcsTUFBTWhELEtBQUssQ0FBQ2UsWUFBWSxDQUFDdUQsTUFBTSxDQUFDO1VBQzdDLElBQUksQ0FBQ3RCLElBQUksRUFBRTtZQUNULE1BQU0sSUFBSTdDLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxXQUFXLEVBQUUsZ0JBQWdCLENBQUM7VUFDdkQ7O1VBRUE7VUFDQSxJQUFJLENBQUNuQyxJQUFJLENBQUNiLEtBQUssSUFBSSxDQUFDYSxJQUFJLENBQUNiLEtBQUssQ0FBQ29HLFNBQVMsQ0FBQyxFQUFFO1lBQ3pDLE1BQU0sSUFBSXBJLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxXQUFXLEVBQUUsZ0JBQWdCLENBQUM7VUFDdkQ7VUFFQSxNQUFNN0YsSUFBSSxHQUFHMEQsSUFBSSxDQUFDYixLQUFLLENBQUNvRyxTQUFTLENBQUM7O1VBRWxDO1VBQ0EsTUFBTUwsYUFBYSxHQUFHOUUsTUFBTSxDQUFDLElBQUksQ0FBQ3pDLE1BQU0sQ0FBQztVQUN6QyxNQUFNNkgsU0FBUyxHQUFHcEYsTUFBTSxDQUFDOUQsSUFBSSxDQUFDK0QsT0FBTyxDQUFDOztVQUV0QztVQUNBLElBQUk2RSxhQUFhLEtBQUtNLFNBQVMsRUFBRTtZQUMvQixNQUFNLElBQUlySSxNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsb0NBQW9DLENBQUM7VUFDaEY7O1VBRUE7VUFDQSxNQUFNc0QsWUFBWSxHQUFHLENBQUMsR0FBR3pGLElBQUksQ0FBQ2IsS0FBSyxDQUFDO1VBQ3BDc0csWUFBWSxDQUFDSixNQUFNLENBQUNFLFNBQVMsRUFBRSxDQUFDLENBQUM7O1VBRWpDO1VBQ0EsTUFBTTlDLE1BQU0sR0FBRyxNQUFNekYsS0FBSyxDQUFDdUcsV0FBVyxDQUNwQztZQUFFOUMsR0FBRyxFQUFFYTtVQUFPLENBQUMsRUFDZjtZQUNFa0MsSUFBSSxFQUFFO2NBQ0pyRSxLQUFLLEVBQUVzRyxZQUFZO2NBQ25CcEcsU0FBUyxFQUFFLElBQUk0QyxJQUFJLENBQUMsQ0FBQztjQUNyQjNDLFNBQVMsRUFBRTRGO1lBQ2I7VUFDRixDQUNGLENBQUM7VUFFRCxJQUFJekMsTUFBTSxLQUFLLENBQUMsRUFBRTtZQUNoQixNQUFNLElBQUl0RixNQUFNLENBQUNnRixLQUFLLENBQUMsZUFBZSxFQUFFLHVCQUF1QixDQUFDO1VBQ2xFOztVQUVBO1VBQ0EsTUFBTWdDLFdBQVcsR0FBRyxNQUFNbkgsS0FBSyxDQUFDZSxZQUFZLENBQUN1RCxNQUFNLENBQUM7VUFDcEQ5QixPQUFPLENBQUNDLEdBQUcsQ0FBQywyQkFBMkIsRUFBRTBFLFdBQVcsQ0FBQztVQUNyRCxPQUFPQSxXQUFXO1FBQ3BCLENBQUMsQ0FBQyxPQUFPeEIsS0FBSyxFQUFFO1VBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsc0JBQXNCLEVBQUVBLEtBQUssQ0FBQztVQUM1QyxJQUFJQSxLQUFLLFlBQVl4RixNQUFNLENBQUNnRixLQUFLLEVBQUU7WUFDakMsTUFBTVEsS0FBSztVQUNiO1VBQ0EsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGNBQWMsRUFBRVEsS0FBSyxDQUFDQyxPQUFPLElBQUksOEJBQThCLENBQUM7UUFDekY7TUFDRixDQUFDO01BRUQsTUFBTSxlQUFlOEMsQ0FBQ3BFLE1BQU0sRUFBRTtRQUM1QmxFLEtBQUssQ0FBQ2tFLE1BQU0sRUFBRWxCLE1BQU0sQ0FBQztRQUVyQixJQUFJLENBQUMsSUFBSSxDQUFDekMsTUFBTSxFQUFFO1VBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixDQUFDO1FBQzNDO1FBRUEsTUFBTW5DLElBQUksR0FBRyxNQUFNaEQsS0FBSyxDQUFDZSxZQUFZLENBQUN1RCxNQUFNLENBQUM7UUFDN0MsSUFBSSxDQUFDdEIsSUFBSSxFQUFFO1VBQ1QsTUFBTSxJQUFJN0MsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLGdCQUFnQixDQUFDO1FBQzVEO1FBRUEsT0FBT25DLElBQUk7TUFDYjtJQUNGLENBQUMsQ0FBQztJQUFDdEQsc0JBQUE7RUFBQSxTQUFBQyxXQUFBO0lBQUEsT0FBQUQsc0JBQUEsQ0FBQUMsV0FBQTtFQUFBO0VBQUFELHNCQUFBO0FBQUE7RUFBQUUsSUFBQTtFQUFBQyxLQUFBO0FBQUEsRzs7Ozs7Ozs7Ozs7Ozs7SUM5eEJILElBQUlDLGFBQWE7SUFBQ1osTUFBTSxDQUFDSSxJQUFJLENBQUMsc0NBQXNDLEVBQUM7TUFBQ1MsT0FBT0EsQ0FBQ1IsQ0FBQyxFQUFDO1FBQUNPLGFBQWEsR0FBQ1AsQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFyRyxJQUFJWSxNQUFNO0lBQUNqQixNQUFNLENBQUNJLElBQUksQ0FBQyxlQUFlLEVBQUM7TUFBQ2EsTUFBTUEsQ0FBQ1osQ0FBQyxFQUFDO1FBQUNZLE1BQU0sR0FBQ1osQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlILGVBQWU7SUFBQ0YsTUFBTSxDQUFDSSxJQUFJLENBQUMsb0JBQW9CLEVBQUM7TUFBQ0YsZUFBZUEsQ0FBQ0csQ0FBQyxFQUFDO1FBQUNILGVBQWUsR0FBQ0csQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlvSixRQUFRO0lBQUN6SixNQUFNLENBQUNJLElBQUksQ0FBQyxzQkFBc0IsRUFBQztNQUFDcUosUUFBUUEsQ0FBQ3BKLENBQUMsRUFBQztRQUFDb0osUUFBUSxHQUFDcEosQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlxSixLQUFLO0lBQUMxSixNQUFNLENBQUNJLElBQUksQ0FBQyxjQUFjLEVBQUM7TUFBQ3NKLEtBQUtBLENBQUNySixDQUFDLEVBQUM7UUFBQ3FKLEtBQUssR0FBQ3JKLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJUyxLQUFLO0lBQUNkLE1BQU0sQ0FBQ0ksSUFBSSxDQUFDLG9CQUFvQixFQUFDO01BQUNVLEtBQUtBLENBQUNULENBQUMsRUFBQztRQUFDUyxLQUFLLEdBQUNULENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJc0osS0FBSztJQUFDM0osTUFBTSxDQUFDSSxJQUFJLENBQUMsdUJBQXVCLEVBQUM7TUFBQ3VKLEtBQUtBLENBQUN0SixDQUFDLEVBQUM7UUFBQ3NKLEtBQUssR0FBQ3RKLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJYSxLQUFLO0lBQUNsQixNQUFNLENBQUNJLElBQUksQ0FBQyxjQUFjLEVBQUM7TUFBQ2MsS0FBS0EsQ0FBQ2IsQ0FBQyxFQUFDO1FBQUNhLEtBQUssR0FBQ2IsQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUl1SixNQUFNO0lBQUM1SixNQUFNLENBQUNJLElBQUksQ0FBQyxRQUFRLEVBQUM7TUFBQ1MsT0FBT0EsQ0FBQ1IsQ0FBQyxFQUFDO1FBQUN1SixNQUFNLEdBQUN2SixDQUFDO01BQUE7SUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDO0lBQUMsSUFBSUMsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUMsTUFBTUEsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUM7SUFTbG1CLGVBQWV1SixVQUFVQSxDQUFBQyxJQUFBLEVBQWlCO01BQUEsSUFBaEI7UUFBRXpILEtBQUs7UUFBRXVHO01BQUksQ0FBQyxHQUFBa0IsSUFBQTtNQUN0QyxNQUFNNUosZUFBZSxDQUFDc0csV0FBVyxDQUFDO1FBQUVuRSxLQUFLO1FBQUV1RyxHQUFHO1FBQUV6RyxTQUFTLEVBQUUsSUFBSTRELElBQUksQ0FBQztNQUFFLENBQUMsQ0FBQztJQUMxRTtJQUVBLE1BQU1nRSxXQUFXLEdBQUcsUUFBUTtJQUU1QjlJLE1BQU0sQ0FBQytJLE9BQU8sQ0FBQyxZQUFZO01BQUEsSUFBQUMscUJBQUE7TUFDekI7TUFDQSxJQUFJO1FBQ0YsTUFBTW5KLEtBQUssQ0FBQ29KLFdBQVcsQ0FBQztVQUFFL0gsU0FBUyxFQUFFO1FBQUUsQ0FBQyxDQUFDO1FBQ3pDLE1BQU1yQixLQUFLLENBQUNvSixXQUFXLENBQUM7VUFBRXZILFVBQVUsRUFBRTtRQUFFLENBQUMsQ0FBQztRQUMxQyxNQUFNN0IsS0FBSyxDQUFDb0osV0FBVyxDQUFDO1VBQUVoSCxTQUFTLEVBQUU7UUFBRSxDQUFDLENBQUM7O1FBRXpDO1FBQ0E7UUFDQSxNQUFNakMsTUFBTSxDQUFDVyxLQUFLLENBQUNzSSxXQUFXLENBQUM7VUFBRW5JLEtBQUssRUFBRTtRQUFFLENBQUMsRUFBRTtVQUFFb0ksVUFBVSxFQUFFO1FBQUssQ0FBQyxDQUFDO01BQ3BFLENBQUMsQ0FBQyxPQUFPMUQsS0FBSyxFQUFFO1FBQ2RuRCxPQUFPLENBQUM4RyxJQUFJLENBQUMsbUNBQW1DLEVBQUUzRCxLQUFLLENBQUNDLE9BQU8sQ0FBQztNQUNsRTs7TUFFQTtNQUNBLElBQUk7UUFDRixNQUFNMkQsUUFBUSxHQUFHLE1BQU1wSixNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUFDLENBQUMsQ0FBQ3FJLFVBQVUsQ0FBQyxDQUFDO1FBQ3ZEaEgsT0FBTyxDQUFDQyxHQUFHLENBQUMsc0JBQXNCLEVBQUU4RyxRQUFRLENBQUMzRixHQUFHLENBQUMvQyxJQUFJO1VBQUEsSUFBQTRJLFlBQUEsRUFBQUMsYUFBQTtVQUFBLE9BQUs7WUFDeERDLEVBQUUsRUFBRTlJLElBQUksQ0FBQzRDLEdBQUc7WUFDWm1HLEtBQUssR0FBQUgsWUFBQSxHQUFFNUksSUFBSSxDQUFDOEMsTUFBTSxjQUFBOEYsWUFBQSx3QkFBQUMsYUFBQSxHQUFYRCxZQUFBLENBQWMsQ0FBQyxDQUFDLGNBQUFDLGFBQUEsdUJBQWhCQSxhQUFBLENBQWtCRyxPQUFPO1lBQ2hDNUksS0FBSyxFQUFFSixJQUFJLENBQUNJLEtBQUs7WUFDakJnRCxPQUFPLEVBQUVwRCxJQUFJLENBQUNvRDtVQUNoQixDQUFDO1FBQUEsQ0FBQyxDQUFDLENBQUM7O1FBRUo7UUFDQSxLQUFLLE1BQU1wRCxJQUFJLElBQUkwSSxRQUFRLEVBQUU7VUFDM0IsTUFBTU8sT0FBTyxHQUFHLENBQUMsQ0FBQztVQUVsQixJQUFJLENBQUNqSixJQUFJLENBQUNJLEtBQUssSUFBSSxDQUFDc0MsS0FBSyxDQUFDd0csT0FBTyxDQUFDbEosSUFBSSxDQUFDSSxLQUFLLENBQUMsRUFBRTtZQUM3QzZJLE9BQU8sQ0FBQzdJLEtBQUssR0FBRyxDQUFDLGFBQWEsQ0FBQztVQUNqQztVQUVBLElBQUksQ0FBQ0osSUFBSSxDQUFDUSxTQUFTLEVBQUU7WUFDbkJ5SSxPQUFPLENBQUN6SSxTQUFTLEdBQUcsSUFBSTRELElBQUksQ0FBQyxDQUFDO1VBQ2hDO1VBRUEsSUFBSStFLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDSCxPQUFPLENBQUMsQ0FBQ2xILE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFBQSxJQUFBc0gsYUFBQSxFQUFBQyxjQUFBO1lBQ25DM0gsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkNBQTJDLEdBQUF5SCxhQUFBLEdBQUVySixJQUFJLENBQUM4QyxNQUFNLGNBQUF1RyxhQUFBLHdCQUFBQyxjQUFBLEdBQVhELGFBQUEsQ0FBYyxDQUFDLENBQUMsY0FBQUMsY0FBQSx1QkFBaEJBLGNBQUEsQ0FBa0JOLE9BQU8sQ0FBQztZQUNuRixNQUFNMUosTUFBTSxDQUFDVyxLQUFLLENBQUN5RixXQUFXLENBQUMxRixJQUFJLENBQUM0QyxHQUFHLEVBQUU7Y0FDdkMrQyxJQUFJLEVBQUVzRDtZQUNSLENBQUMsQ0FBQztVQUNKO1FBQ0Y7UUFFQSxNQUFNTSxnQkFBZ0IsR0FBRyxNQUFNakssTUFBTSxDQUFDVyxLQUFLLENBQUNLLElBQUksQ0FBQztVQUFFLE9BQU8sRUFBRTtRQUFjLENBQUMsQ0FBQyxDQUFDa0osVUFBVSxDQUFDLENBQUM7UUFDekY3SCxPQUFPLENBQUNDLEdBQUcsQ0FBQywrQkFBK0IsRUFBRTJILGdCQUFnQixDQUFDOztRQUU5RDtRQUNBLElBQUlBLGdCQUFnQixLQUFLLENBQUMsRUFBRTtVQUMxQjVILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNDQUFzQyxDQUFDO1VBQ25ELElBQUk7WUFDRjtZQUNBLE1BQU02SCxXQUFXLEdBQUcsQ0FDbEI7Y0FDRVYsS0FBSyxFQUFFLG1CQUFtQjtjQUMxQlcsUUFBUSxFQUFFLGNBQWM7Y0FDeEJyRyxTQUFTLEVBQUUsTUFBTTtjQUNqQkMsUUFBUSxFQUFFO1lBQ1osQ0FBQyxFQUNEO2NBQ0V5RixLQUFLLEVBQUUsbUJBQW1CO2NBQzFCVyxRQUFRLEVBQUUsY0FBYztjQUN4QnJHLFNBQVMsRUFBRSxNQUFNO2NBQ2pCQyxRQUFRLEVBQUU7WUFDWixDQUFDLENBQ0Y7WUFFRCxLQUFLLE1BQU1xRyxNQUFNLElBQUlGLFdBQVcsRUFBRTtjQUNoQyxNQUFNM0osTUFBTSxHQUFHLE1BQU1nSSxRQUFRLENBQUM4QixlQUFlLENBQUM7Z0JBQzVDYixLQUFLLEVBQUVZLE1BQU0sQ0FBQ1osS0FBSztnQkFDbkJXLFFBQVEsRUFBRUMsTUFBTSxDQUFDRCxRQUFRO2dCQUN6QkcsSUFBSSxFQUFFLGFBQWE7Z0JBQ25CckosU0FBUyxFQUFFLElBQUk0RCxJQUFJLENBQUMsQ0FBQztnQkFDckJoQixPQUFPLEVBQUU7a0JBQ1BDLFNBQVMsRUFBRXNHLE1BQU0sQ0FBQ3RHLFNBQVM7a0JBQzNCQyxRQUFRLEVBQUVxRyxNQUFNLENBQUNyRyxRQUFRO2tCQUN6QnVHLElBQUksRUFBRSxhQUFhO2tCQUNuQkMsUUFBUSxLQUFBM0csTUFBQSxDQUFLd0csTUFBTSxDQUFDdEcsU0FBUyxPQUFBRixNQUFBLENBQUl3RyxNQUFNLENBQUNyRyxRQUFRO2dCQUNsRDtjQUNGLENBQUMsQ0FBQzs7Y0FFRjtjQUNBLE1BQU1oRSxNQUFNLENBQUNXLEtBQUssQ0FBQ3lGLFdBQVcsQ0FBQzVGLE1BQU0sRUFBRTtnQkFDckM2RixJQUFJLEVBQUU7a0JBQUV2RixLQUFLLEVBQUUsQ0FBQyxhQUFhO2dCQUFFO2NBQ2pDLENBQUMsQ0FBQztjQUVGdUIsT0FBTyxDQUFDQyxHQUFHLENBQUMscUNBQXFDLEVBQUU7Z0JBQ2pEa0gsRUFBRSxFQUFFaEosTUFBTTtnQkFDVmlKLEtBQUssRUFBRVksTUFBTSxDQUFDWixLQUFLO2dCQUNuQnRKLElBQUksS0FBQTBELE1BQUEsQ0FBS3dHLE1BQU0sQ0FBQ3RHLFNBQVMsT0FBQUYsTUFBQSxDQUFJd0csTUFBTSxDQUFDckcsUUFBUTtjQUM5QyxDQUFDLENBQUM7WUFDSjtVQUNGLENBQUMsQ0FBQyxPQUFPd0IsS0FBSyxFQUFFO1lBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsNkNBQTZDLEVBQUVBLEtBQUssQ0FBQztVQUNyRTtRQUNGO01BQ0YsQ0FBQyxDQUFDLE9BQU9BLEtBQUssRUFBRTtRQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLHdDQUF3QyxFQUFFQSxLQUFLLENBQUM7TUFDaEU7O01BRUE7TUFDQSxNQUFNaUYsYUFBYSxJQUFBekIscUJBQUEsR0FBR2hKLE1BQU0sQ0FBQzBLLFFBQVEsQ0FBQ0MsT0FBTyxjQUFBM0IscUJBQUEsdUJBQXZCQSxxQkFBQSxDQUF5QlMsS0FBSzs7TUFFcEQ7TUFDQSxJQUFJZ0IsYUFBYSxhQUFiQSxhQUFhLGVBQWJBLGFBQWEsQ0FBRUcsUUFBUSxJQUFJSCxhQUFhLGFBQWJBLGFBQWEsZUFBYkEsYUFBYSxDQUFFTCxRQUFRLEVBQUU7UUFDdERTLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxRQUFRLGFBQUFsSCxNQUFBLENBQWFtSCxrQkFBa0IsQ0FBQ1AsYUFBYSxDQUFDRyxRQUFRLENBQUMsT0FBQS9HLE1BQUEsQ0FBSW1ILGtCQUFrQixDQUFDUCxhQUFhLENBQUNMLFFBQVEsQ0FBQyxPQUFBdkcsTUFBQSxDQUFJNEcsYUFBYSxDQUFDUSxNQUFNLE9BQUFwSCxNQUFBLENBQUk0RyxhQUFhLENBQUNTLElBQUksQ0FBRTs7UUFFeks7UUFDQSxJQUFJO1VBQ0Y3SSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxnQ0FBZ0MsQ0FBQztVQUM3Q21HLEtBQUssQ0FBQzBDLElBQUksQ0FBQztZQUNUQyxFQUFFLEVBQUVYLGFBQWEsQ0FBQ0csUUFBUTtZQUMxQnZILElBQUksRUFBRW9ILGFBQWEsQ0FBQ0csUUFBUTtZQUM1QlMsT0FBTyxFQUFFLFlBQVk7WUFDckJsRyxJQUFJLEVBQUU7VUFDUixDQUFDLENBQUM7VUFDRjlDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLCtCQUErQixDQUFDO1FBQzlDLENBQUMsQ0FBQyxPQUFPa0QsS0FBSyxFQUFFO1VBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsMkJBQTJCLEVBQUVBLEtBQUssQ0FBQztRQUNuRDtNQUNGLENBQUMsTUFBTTtRQUNMbkQsT0FBTyxDQUFDOEcsSUFBSSxDQUFDLGlEQUFpRCxDQUFDO01BQ2pFOztNQUVBO01BQ0FYLFFBQVEsQ0FBQzhDLE1BQU0sQ0FBQztRQUNkQyxxQkFBcUIsRUFBRSxJQUFJO1FBQzNCQywyQkFBMkIsRUFBRTtNQUMvQixDQUFDLENBQUM7O01BRUY7TUFDQWhELFFBQVEsQ0FBQ2lELGNBQWMsQ0FBQ0MsUUFBUSxHQUFHLHdCQUF3QjtNQUMzRGxELFFBQVEsQ0FBQ2lELGNBQWMsQ0FBQ3BJLElBQUksR0FBR29ILGFBQWEsYUFBYkEsYUFBYSxlQUFiQSxhQUFhLENBQUVHLFFBQVEsOEJBQUEvRyxNQUFBLENBQ3pCNEcsYUFBYSxDQUFDRyxRQUFRLFNBQ2pELDhDQUE4QztNQUVoRHBDLFFBQVEsQ0FBQ2lELGNBQWMsQ0FBQ0UsV0FBVyxHQUFHO1FBQ3BDTixPQUFPQSxDQUFBLEVBQUc7VUFDUixPQUFPLDJCQUEyQjtRQUNwQyxDQUFDO1FBQ0RsRyxJQUFJQSxDQUFDekUsSUFBSSxFQUFFaUgsR0FBRyxFQUFFO1VBQ2QsTUFBTWlFLFlBQVksR0FBR2xMLElBQUksQ0FBQzhDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQ2tHLE9BQU87VUFDM0MsT0FBTyxnREFBQTdGLE1BQUEsQ0FDOEIrSCxZQUFZLHdDQUFxQyxNQUFBL0gsTUFBQSxDQUMvRThELEdBQUcsU0FBTSw0RUFDNkQsY0FDOUQscUNBQ3VCO1FBQ3hDLENBQUM7UUFDRGtFLElBQUlBLENBQUNuTCxJQUFJLEVBQUVpSCxHQUFHLEVBQUU7VUFDZCxNQUFNaUUsWUFBWSxHQUFHbEwsSUFBSSxDQUFDOEMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDa0csT0FBTztVQUMzQyxzUUFBQTdGLE1BQUEsQ0FLeUMrSCxZQUFZLGdIQUFBL0gsTUFBQSxDQUVsQzhELEdBQUc7UUFleEI7TUFDRixDQUFDOztNQUVEO01BQ0EsSUFBSSxPQUFNMUksZUFBZSxDQUFDK0IsSUFBSSxDQUFDLENBQUMsQ0FBQ2tKLFVBQVUsQ0FBQyxDQUFDLE1BQUssQ0FBQyxFQUFFO1FBQ25ELE1BQU10QixVQUFVLENBQUM7VUFDZnhILEtBQUssRUFBRSxpQkFBaUI7VUFDeEJ1RyxHQUFHLEVBQUU7UUFDUCxDQUFDLENBQUM7UUFFRixNQUFNaUIsVUFBVSxDQUFDO1VBQ2Z4SCxLQUFLLEVBQUUsa0JBQWtCO1VBQ3pCdUcsR0FBRyxFQUFFO1FBQ1AsQ0FBQyxDQUFDO1FBRUYsTUFBTWlCLFVBQVUsQ0FBQztVQUNmeEgsS0FBSyxFQUFFLGVBQWU7VUFDdEJ1RyxHQUFHLEVBQUU7UUFDUCxDQUFDLENBQUM7UUFFRixNQUFNaUIsVUFBVSxDQUFDO1VBQ2Z4SCxLQUFLLEVBQUUsYUFBYTtVQUNwQnVHLEdBQUcsRUFBRTtRQUNQLENBQUMsQ0FBQztNQUNKOztNQUVBO01BQ0E7TUFDQTNILE1BQU0sQ0FBQ00sT0FBTyxDQUFDLE9BQU8sRUFBRSxZQUFZO1FBQ2xDLE9BQU9yQixlQUFlLENBQUMrQixJQUFJLENBQUMsQ0FBQztNQUMvQixDQUFDLENBQUM7O01BRUY7TUFDQXdILFFBQVEsQ0FBQ3NELFlBQVksQ0FBQyxDQUFDQyxPQUFPLEVBQUVyTCxJQUFJLEtBQUs7UUFBQSxJQUFBc0wscUJBQUEsRUFBQUMsc0JBQUE7UUFDdkM1SixPQUFPLENBQUNDLEdBQUcsQ0FBQyw0Q0FBNEMsRUFBRTtVQUN4RG1ILEtBQUssRUFBRXNDLE9BQU8sQ0FBQ3RDLEtBQUs7VUFDcEJjLElBQUksRUFBRXdCLE9BQU8sQ0FBQ3hCLElBQUk7VUFDbEJ6RyxPQUFPLEVBQUVpSSxPQUFPLENBQUNqSSxPQUFPO1VBQ3hCNUMsU0FBUyxFQUFFNkssT0FBTyxDQUFDN0s7UUFDckIsQ0FBQyxDQUFDO1FBRUYsTUFBTWdMLGNBQWMsR0FBQXZNLGFBQUEsS0FBUWUsSUFBSSxDQUFFOztRQUVsQztRQUNBd0wsY0FBYyxDQUFDcEksT0FBTyxHQUFHaUksT0FBTyxDQUFDakksT0FBTyxJQUFJLENBQUMsQ0FBQzs7UUFFOUM7UUFDQSxNQUFNeUcsSUFBSSxHQUFHd0IsT0FBTyxDQUFDeEIsSUFBSSxJQUFJLGFBQWE7UUFDMUMyQixjQUFjLENBQUNwTCxLQUFLLEdBQUcsQ0FBQ3lKLElBQUksQ0FBQzs7UUFFN0I7UUFDQTJCLGNBQWMsQ0FBQ2hMLFNBQVMsR0FBRzZLLE9BQU8sQ0FBQzdLLFNBQVMsSUFBSSxJQUFJNEQsSUFBSSxDQUFDLENBQUM7UUFFMUR6QyxPQUFPLENBQUNDLEdBQUcsQ0FBQyw4QkFBOEIsRUFBRTtVQUMxQ2tILEVBQUUsRUFBRTBDLGNBQWMsQ0FBQzVJLEdBQUc7VUFDdEJtRyxLQUFLLEdBQUF1QyxxQkFBQSxHQUFFRSxjQUFjLENBQUMxSSxNQUFNLGNBQUF3SSxxQkFBQSx3QkFBQUMsc0JBQUEsR0FBckJELHFCQUFBLENBQXdCLENBQUMsQ0FBQyxjQUFBQyxzQkFBQSx1QkFBMUJBLHNCQUFBLENBQTRCdkMsT0FBTztVQUMxQzVJLEtBQUssRUFBRW9MLGNBQWMsQ0FBQ3BMLEtBQUs7VUFDM0JnRCxPQUFPLEVBQUVvSSxjQUFjLENBQUNwSSxPQUFPO1VBQy9CNUMsU0FBUyxFQUFFZ0wsY0FBYyxDQUFDaEw7UUFDNUIsQ0FBQyxDQUFDO1FBRUYsT0FBT2dMLGNBQWM7TUFDdkIsQ0FBQyxDQUFDOztNQUVGO01BQ0FsTSxNQUFNLENBQUNNLE9BQU8sQ0FBQyxhQUFhLEVBQUUsWUFBVztRQUN2QytCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJDQUEyQyxFQUFFLElBQUksQ0FBQzlCLE1BQU0sQ0FBQztRQUVyRSxJQUFJLENBQUMsSUFBSSxDQUFDQSxNQUFNLEVBQUU7VUFDaEI2QixPQUFPLENBQUNDLEdBQUcsQ0FBQywwQ0FBMEMsQ0FBQztVQUN2RCxPQUFPLElBQUksQ0FBQzdCLEtBQUssQ0FBQyxDQUFDO1FBQ3JCO1FBRUEsSUFBSTtVQUNGO1VBQ0EsTUFBTTBMLFdBQVcsR0FBR25NLE1BQU0sQ0FBQ1csS0FBSyxDQUFDSyxJQUFJLENBQ25DO1lBQ0VvQixHQUFHLEVBQUUsQ0FDSDtjQUFFLE9BQU8sRUFBRTtZQUFjLENBQUMsRUFDMUI7Y0FBRSxjQUFjLEVBQUU7WUFBYyxDQUFDO1VBRXJDLENBQUMsRUFDRDtZQUNFakIsTUFBTSxFQUFFO2NBQ05xQyxNQUFNLEVBQUUsQ0FBQztjQUNUMUMsS0FBSyxFQUFFLENBQUM7Y0FDUixtQkFBbUIsRUFBRSxDQUFDO2NBQ3RCLGtCQUFrQixFQUFFLENBQUM7Y0FDckIsa0JBQWtCLEVBQUUsQ0FBQztjQUNyQkksU0FBUyxFQUFFO1lBQ2I7VUFDRixDQUNGLENBQUM7VUFFRG1CLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHVDQUF1QyxDQUFDO1VBQ3BELE9BQU82SixXQUFXO1FBQ3BCLENBQUMsQ0FBQyxPQUFPM0csS0FBSyxFQUFFO1VBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMscUNBQXFDLEVBQUVBLEtBQUssQ0FBQztVQUMzRCxPQUFPLElBQUksQ0FBQy9FLEtBQUssQ0FBQyxDQUFDO1FBQ3JCO01BQ0YsQ0FBQyxDQUFDOztNQUVGO01BQ0FULE1BQU0sQ0FBQ00sT0FBTyxDQUFDLFVBQVUsRUFBRSxZQUFXO1FBQ3BDLElBQUksQ0FBQyxJQUFJLENBQUNFLE1BQU0sRUFBRTtVQUNoQixPQUFPLElBQUksQ0FBQ0MsS0FBSyxDQUFDLENBQUM7UUFDckI7UUFFQTRCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNDQUFzQyxFQUFFLElBQUksQ0FBQzlCLE1BQU0sQ0FBQztRQUVoRSxPQUFPUixNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUN0QjtVQUFFc0MsR0FBRyxFQUFFLElBQUksQ0FBQzlDO1FBQU8sQ0FBQyxFQUNwQjtVQUNFVyxNQUFNLEVBQUU7WUFDTkwsS0FBSyxFQUFFLENBQUM7WUFDUjBDLE1BQU0sRUFBRSxDQUFDO1lBQ1RNLE9BQU8sRUFBRTtVQUNYO1FBQ0YsQ0FDRixDQUFDO01BQ0gsQ0FBQyxDQUFDO0lBQ0osQ0FBQyxDQUFDOztJQUVGO0lBQ0E5RCxNQUFNLENBQUM0RSxPQUFPLENBQUM7TUFDYixjQUFjd0gsQ0FBQUMsS0FBQSxFQUE2RDtRQUFBLElBQTVEO1VBQUU1QyxLQUFLO1VBQUVXLFFBQVE7VUFBRUcsSUFBSTtVQUFFK0IsVUFBVTtVQUFFdkksU0FBUztVQUFFQztRQUFTLENBQUMsR0FBQXFJLEtBQUE7UUFDdkU7UUFDQSxJQUFJOUIsSUFBSSxLQUFLLE9BQU8sSUFBSStCLFVBQVUsS0FBS3hELFdBQVcsRUFBRTtVQUNsRCxNQUFNLElBQUk5SSxNQUFNLENBQUNnRixLQUFLLENBQUMscUJBQXFCLEVBQUUsOEJBQThCLENBQUM7UUFDL0U7O1FBRUE7UUFDQSxNQUFNdUgsYUFBYSxHQUFHO1VBQ3BCOUosTUFBTSxFQUFFLE9BQU87VUFDZitKLFNBQVMsRUFBRSxPQUFPO1VBQ2xCQyxNQUFNLEVBQUUsT0FBTztVQUNmQyxPQUFPLEVBQUU7UUFDWCxDQUFDO1FBRUQsTUFBTUMsY0FBYyxHQUFHLEVBQUU7UUFDekIsSUFBSSxDQUFDSixhQUFhLENBQUM5SixNQUFNLENBQUNtSyxJQUFJLENBQUN4QyxRQUFRLENBQUMsRUFBRTtVQUN4Q3VDLGNBQWMsQ0FBQ0UsSUFBSSxDQUFDLDZDQUE2QyxDQUFDO1FBQ3BFO1FBQ0EsSUFBSSxDQUFDTixhQUFhLENBQUNDLFNBQVMsQ0FBQ0ksSUFBSSxDQUFDeEMsUUFBUSxDQUFDLEVBQUU7VUFDM0N1QyxjQUFjLENBQUNFLElBQUksQ0FBQyxxREFBcUQsQ0FBQztRQUM1RTtRQUNBLElBQUksQ0FBQ04sYUFBYSxDQUFDRSxNQUFNLENBQUNHLElBQUksQ0FBQ3hDLFFBQVEsQ0FBQyxFQUFFO1VBQ3hDdUMsY0FBYyxDQUFDRSxJQUFJLENBQUMsMkNBQTJDLENBQUM7UUFDbEU7UUFDQSxJQUFJLENBQUNOLGFBQWEsQ0FBQ0csT0FBTyxDQUFDRSxJQUFJLENBQUN4QyxRQUFRLENBQUMsRUFBRTtVQUN6Q3VDLGNBQWMsQ0FBQ0UsSUFBSSxDQUFDLGlFQUFpRSxDQUFDO1FBQ3hGO1FBRUEsSUFBSUYsY0FBYyxDQUFDbEssTUFBTSxHQUFHLENBQUMsRUFBRTtVQUM3QixNQUFNLElBQUl6QyxNQUFNLENBQUNnRixLQUFLLENBQUMsa0JBQWtCLEVBQUUySCxjQUFjLENBQUNHLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN2RTs7UUFFQTtRQUNBLElBQUk7VUFDRixNQUFNdE0sTUFBTSxHQUFHZ0ksUUFBUSxDQUFDdUUsVUFBVSxDQUFDO1lBQ2pDdEQsS0FBSztZQUNMVyxRQUFRO1lBQ1JHLElBQUk7WUFBRTtZQUNOekcsT0FBTyxFQUFFO2NBQ1B5RyxJQUFJO2NBQUU7Y0FDTnhHLFNBQVM7Y0FDVEMsUUFBUTtjQUNSd0csUUFBUSxLQUFBM0csTUFBQSxDQUFLRSxTQUFTLE9BQUFGLE1BQUEsQ0FBSUcsUUFBUTtZQUNwQztVQUNGLENBQUMsQ0FBQzs7VUFFRjtVQUNBLElBQUl4RCxNQUFNLEVBQUU7WUFDVmdJLFFBQVEsQ0FBQytDLHFCQUFxQixDQUFDL0ssTUFBTSxDQUFDO1VBQ3hDO1VBRUEsT0FBT0EsTUFBTTtRQUNmLENBQUMsQ0FBQyxPQUFPZ0YsS0FBSyxFQUFFO1VBQ2QsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLG9CQUFvQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUM3RDtNQUNGLENBQUM7TUFFRCxNQUFNLGVBQWV1SCxDQUFBLEVBQUc7UUFDdEIsSUFBSSxDQUFDLElBQUksQ0FBQ3hNLE1BQU0sRUFBRTtVQUNoQixNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx3QkFBd0IsQ0FBQztRQUNwRTtRQUVBLElBQUk7VUFBQSxJQUFBekUsV0FBQSxFQUFBME0sYUFBQTtVQUNGLE1BQU12TSxJQUFJLEdBQUcsTUFBTVYsTUFBTSxDQUFDVyxLQUFLLENBQUNDLFlBQVksQ0FBQyxJQUFJLENBQUNKLE1BQU0sQ0FBQztVQUN6RCxJQUFJLENBQUNFLElBQUksRUFBRTtZQUNULE1BQU0sSUFBSVYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLGdCQUFnQixDQUFDO1VBQzVEOztVQUVBO1VBQ0EsTUFBTXVGLElBQUksR0FBRyxFQUFBaEssV0FBQSxHQUFBRyxJQUFJLENBQUNJLEtBQUssY0FBQVAsV0FBQSx1QkFBVkEsV0FBQSxDQUFhLENBQUMsQ0FBQyxPQUFBME0sYUFBQSxHQUFJdk0sSUFBSSxDQUFDb0QsT0FBTyxjQUFBbUosYUFBQSx1QkFBWkEsYUFBQSxDQUFjMUMsSUFBSSxLQUFJLGFBQWE7VUFDbkUsT0FBT0EsSUFBSTtRQUNiLENBQUMsQ0FBQyxPQUFPL0UsS0FBSyxFQUFFO1VBQ2QsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUMxRDtNQUNGLENBQUM7TUFFRCwrQkFBK0J5SCxDQUFDekQsS0FBSyxFQUFFO1FBQ3JDO1FBQ0EsTUFBTS9JLElBQUksR0FBRzhILFFBQVEsQ0FBQzJFLGVBQWUsQ0FBQzFELEtBQUssQ0FBQztRQUM1QyxJQUFJLENBQUMvSSxJQUFJLEVBQUU7VUFDVCxNQUFNLElBQUlWLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx1Q0FBdUMsQ0FBQztRQUNuRjs7UUFFQTtRQUNBLE1BQU1vSSxTQUFTLEdBQUcxTSxJQUFJLENBQUM4QyxNQUFNLENBQUMsQ0FBQyxDQUFDO1FBQ2hDLElBQUk0SixTQUFTLENBQUNDLFFBQVEsRUFBRTtVQUN0QixNQUFNLElBQUlyTixNQUFNLENBQUNnRixLQUFLLENBQUMsa0JBQWtCLEVBQUUsZ0NBQWdDLENBQUM7UUFDOUU7O1FBRUE7UUFDQSxJQUFJO1VBQ0Z3RCxRQUFRLENBQUMrQyxxQkFBcUIsQ0FBQzdLLElBQUksQ0FBQzRDLEdBQUcsRUFBRW1HLEtBQUssQ0FBQztVQUMvQyxPQUFPLElBQUk7UUFDYixDQUFDLENBQUMsT0FBT2pFLEtBQUssRUFBRTtVQUNkLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQywyQkFBMkIsRUFBRVEsS0FBSyxDQUFDQyxPQUFPLENBQUM7UUFDcEU7TUFDRixDQUFDO01BRUQsTUFBTSxzQkFBc0I2SCxDQUFDbkcsSUFBSSxFQUFFO1FBQUEsSUFBQW9HLGFBQUEsRUFBQUMsY0FBQTtRQUNqQ25MLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJDQUEyQyxFQUFFNkUsSUFBSSxDQUFDO1FBRTlEbEgsS0FBSyxDQUFDa0gsSUFBSSxFQUFFO1VBQ1ZzQyxLQUFLLEVBQUV4RyxNQUFNO1VBQ2J3SyxXQUFXLEVBQUV4SztRQUNmLENBQUMsQ0FBQztRQUVGLE1BQU07VUFBRXdHLEtBQUs7VUFBRWdFO1FBQVksQ0FBQyxHQUFHdEcsSUFBSTtRQUNuQzlFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdEQUFnRCxFQUFFbUgsS0FBSyxDQUFDOztRQUVwRTtRQUNBLE1BQU0vSSxJQUFJLEdBQUc4SCxRQUFRLENBQUMyRSxlQUFlLENBQUMxRCxLQUFLLENBQUM7UUFDNUNwSCxPQUFPLENBQUNDLEdBQUcsQ0FBQyw4QkFBOEIsRUFBRTVCLElBQUksR0FBRztVQUFFOEksRUFBRSxFQUFFOUksSUFBSSxDQUFDNEMsR0FBRztVQUFFbUcsS0FBSyxHQUFBOEQsYUFBQSxHQUFFN00sSUFBSSxDQUFDOEMsTUFBTSxjQUFBK0osYUFBQSx3QkFBQUMsY0FBQSxHQUFYRCxhQUFBLENBQWMsQ0FBQyxDQUFDLGNBQUFDLGNBQUEsdUJBQWhCQSxjQUFBLENBQWtCOUQ7UUFBUSxDQUFDLEdBQUcsZUFBZSxDQUFDO1FBRXhILElBQUksQ0FBQ2hKLElBQUksRUFBRTtVQUNULE1BQU0sSUFBSVYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLHVDQUF1QyxDQUFDO1FBQ25GOztRQUVBO1FBQ0EsTUFBTXVILGFBQWEsR0FBRztVQUNwQjlKLE1BQU0sRUFBRSxPQUFPO1VBQ2YrSixTQUFTLEVBQUUsT0FBTztVQUNsQkMsTUFBTSxFQUFFLE9BQU87VUFDZkMsT0FBTyxFQUFFO1FBQ1gsQ0FBQztRQUVELE1BQU1DLGNBQWMsR0FBRyxFQUFFO1FBQ3pCLElBQUksQ0FBQ0osYUFBYSxDQUFDOUosTUFBTSxDQUFDbUssSUFBSSxDQUFDYSxXQUFXLENBQUMsRUFBRTtVQUMzQ2QsY0FBYyxDQUFDRSxJQUFJLENBQUMsNkNBQTZDLENBQUM7UUFDcEU7UUFDQSxJQUFJLENBQUNOLGFBQWEsQ0FBQ0MsU0FBUyxDQUFDSSxJQUFJLENBQUNhLFdBQVcsQ0FBQyxFQUFFO1VBQzlDZCxjQUFjLENBQUNFLElBQUksQ0FBQyxxREFBcUQsQ0FBQztRQUM1RTtRQUNBLElBQUksQ0FBQ04sYUFBYSxDQUFDRSxNQUFNLENBQUNHLElBQUksQ0FBQ2EsV0FBVyxDQUFDLEVBQUU7VUFDM0NkLGNBQWMsQ0FBQ0UsSUFBSSxDQUFDLDJDQUEyQyxDQUFDO1FBQ2xFO1FBQ0EsSUFBSSxDQUFDTixhQUFhLENBQUNHLE9BQU8sQ0FBQ0UsSUFBSSxDQUFDYSxXQUFXLENBQUMsRUFBRTtVQUM1Q2QsY0FBYyxDQUFDRSxJQUFJLENBQUMsaUVBQWlFLENBQUM7UUFDeEY7UUFFQSxJQUFJRixjQUFjLENBQUNsSyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1VBQzdCLE1BQU0sSUFBSXpDLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxrQkFBa0IsRUFBRTJILGNBQWMsQ0FBQ0csSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3ZFOztRQUVBO1FBQ0F6SyxPQUFPLENBQUNDLEdBQUcsQ0FBQyw2REFBNkQsRUFBRTVCLElBQUksQ0FBQzRDLEdBQUcsQ0FBQztRQUNwRmpCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG1EQUFtRCxFQUFFb0wsSUFBSSxDQUFDQyxTQUFTLENBQUNqTixJQUFJLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRS9GLElBQUk7VUFDRjtVQUNBLE1BQU1rTixVQUFVLEdBQUcsRUFBRTtVQUNyQixNQUFNQyxjQUFjLEdBQUdsRixNQUFNLENBQUNtRixRQUFRLENBQUNMLFdBQVcsRUFBRUcsVUFBVSxDQUFDO1VBQy9EdkwsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkNBQTJDLEVBQUV1TCxjQUFjLENBQUM7O1VBRXhFO1VBQ0EsTUFBTUUsZ0JBQWdCLEdBQUc7VUFDdkI7VUFDQTtZQUFFLDBCQUEwQixFQUFFRjtVQUFlLENBQUM7VUFDOUM7VUFDQTtZQUFFLG1CQUFtQixFQUFFO2NBQUVsRixNQUFNLEVBQUVrRjtZQUFlO1VBQUUsQ0FBQztVQUNuRDtVQUNBO1lBQUUsd0JBQXdCLEVBQUVBO1VBQWUsQ0FBQyxDQUM3QztVQUVELElBQUk5RyxZQUFZLEdBQUcsQ0FBQztVQUNwQixJQUFJaUgsU0FBUyxHQUFHLElBQUk7VUFFcEIsS0FBSyxNQUFNQyxRQUFRLElBQUlGLGdCQUFnQixFQUFFO1lBQ3ZDLElBQUk7Y0FDRjFMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJDQUEyQyxFQUFFMkwsUUFBUSxDQUFDO2NBQ2xFbEgsWUFBWSxHQUFHLE1BQU0vRyxNQUFNLENBQUNXLEtBQUssQ0FBQ3lGLFdBQVcsQ0FBQzFGLElBQUksQ0FBQzRDLEdBQUcsRUFBRTtnQkFDdEQrQyxJQUFJLEVBQUU0SDtjQUNSLENBQUMsQ0FBQztjQUVGLElBQUlsSCxZQUFZLEtBQUssQ0FBQyxFQUFFO2dCQUN0QjFFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLG9EQUFvRCxFQUFFMkwsUUFBUSxDQUFDO2dCQUMzRTtjQUNGO1lBQ0YsQ0FBQyxDQUFDLE9BQU9DLE9BQU8sRUFBRTtjQUNoQjdMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDJDQUEyQyxFQUFFNEwsT0FBTyxDQUFDekksT0FBTyxDQUFDO2NBQ3pFdUksU0FBUyxHQUFHRSxPQUFPO1lBQ3JCO1VBQ0Y7VUFFQSxJQUFJbkgsWUFBWSxLQUFLLENBQUMsRUFBRTtZQUN0QjFFLE9BQU8sQ0FBQ0MsR0FBRyw0REFBQXVCLE1BQUEsQ0FBNERuRCxJQUFJLENBQUM0QyxHQUFHLGVBQUFPLE1BQUEsQ0FBWTRGLEtBQUssQ0FBRSxDQUFDO1lBQ25HLE9BQU87Y0FBRTBFLE9BQU8sRUFBRSxJQUFJO2NBQUUxSSxPQUFPLEVBQUU7WUFBZ0MsQ0FBQztVQUNwRSxDQUFDLE1BQU07WUFBQSxJQUFBMkksVUFBQTtZQUNMO1lBQ0EsTUFBTUMsZUFBZSxHQUFHLE1BQU1yTyxNQUFNLENBQUNXLEtBQUssQ0FBQ0MsWUFBWSxDQUFDRixJQUFJLENBQUM0QyxHQUFHLENBQUM7WUFDakVqQixPQUFPLENBQUNDLEdBQUcsQ0FBQyx1REFBdUQsRUFBRW9MLElBQUksQ0FBQ0MsU0FBUyxDQUFDVSxlQUFlLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQzlHLE1BQU0sSUFBSXJKLEtBQUssNERBQUFuQixNQUFBLENBQTRELEVBQUF1SyxVQUFBLEdBQUFKLFNBQVMsY0FBQUksVUFBQSx1QkFBVEEsVUFBQSxDQUFXM0ksT0FBTyxLQUFJLFNBQVMsQ0FBRSxDQUFDO1VBQy9HO1FBQ0YsQ0FBQyxDQUFDLE9BQU9ELEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLG9EQUFvRCxFQUFFOUUsSUFBSSxDQUFDNEMsR0FBRyxFQUFFLGdCQUFnQixFQUFFa0MsS0FBSyxDQUFDO1VBQ3RHLE1BQU0sSUFBSXhGLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyx3QkFBd0IsZ0NBQUFuQixNQUFBLENBQWdDMkIsS0FBSyxDQUFDQyxPQUFPLENBQUUsQ0FBQztRQUNqRztNQUNGLENBQUM7TUFFRDtNQUNBLGtCQUFrQjZJLENBQUEsRUFBRztRQUNuQmpNLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlDQUF5QyxDQUFDO1FBQ3RELE9BQU87VUFBRTZMLE9BQU8sRUFBRSxJQUFJO1VBQUUxSSxPQUFPLEVBQUU7UUFBc0IsQ0FBQztNQUMxRCxDQUFDO01BRUQsTUFBTSw0QkFBNEI4SSxDQUFBLEVBQUc7UUFDbkMsSUFBSSxDQUFDLElBQUksQ0FBQy9OLE1BQU0sRUFBRTtVQUNoQixNQUFNLElBQUlSLE1BQU0sQ0FBQ2dGLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSx1QkFBdUIsQ0FBQztRQUNuRTtRQUVBLElBQUk7VUFBQSxJQUFBd0osYUFBQSxFQUFBQyxjQUFBO1VBQ0YsTUFBTS9OLElBQUksR0FBRyxNQUFNVixNQUFNLENBQUNXLEtBQUssQ0FBQ0MsWUFBWSxDQUFDLElBQUksQ0FBQ0osTUFBTSxDQUFDO1VBQ3pENkIsT0FBTyxDQUFDQyxHQUFHLENBQUMsdUNBQXVDLEVBQUU7WUFDbkRrSCxFQUFFLEVBQUU5SSxJQUFJLGFBQUpBLElBQUksdUJBQUpBLElBQUksQ0FBRTRDLEdBQUc7WUFDYm1HLEtBQUssRUFBRS9JLElBQUksYUFBSkEsSUFBSSx3QkFBQThOLGFBQUEsR0FBSjlOLElBQUksQ0FBRThDLE1BQU0sY0FBQWdMLGFBQUEsd0JBQUFDLGNBQUEsR0FBWkQsYUFBQSxDQUFlLENBQUMsQ0FBQyxjQUFBQyxjQUFBLHVCQUFqQkEsY0FBQSxDQUFtQi9FLE9BQU87WUFDakM1SSxLQUFLLEVBQUVKLElBQUksYUFBSkEsSUFBSSx1QkFBSkEsSUFBSSxDQUFFSTtVQUNmLENBQUMsQ0FBQzs7VUFFRjtVQUNBLElBQUksQ0FBQ0osSUFBSSxDQUFDSSxLQUFLLEVBQUU7WUFDZixNQUFNZCxNQUFNLENBQUNXLEtBQUssQ0FBQ3lGLFdBQVcsQ0FBQyxJQUFJLENBQUM1RixNQUFNLEVBQUU7Y0FDMUM2RixJQUFJLEVBQUU7Z0JBQUV2RixLQUFLLEVBQUUsQ0FBQyxhQUFhO2NBQUU7WUFDakMsQ0FBQyxDQUFDO1lBQ0YsT0FBTyxtQkFBbUI7VUFDNUI7O1VBRUE7VUFDQSxJQUFJLENBQUNKLElBQUksQ0FBQ0ksS0FBSyxDQUFDQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUU7WUFDakNzQixPQUFPLENBQUNDLEdBQUcsQ0FBQyxrRUFBa0UsQ0FBQzs7WUFFL0U7WUFDQSxNQUFNb00sVUFBVSxHQUFHLE1BQU0xTyxNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUFDLENBQUMsQ0FBQ2tKLFVBQVUsQ0FBQyxDQUFDO1lBQ3pELElBQUl3RSxVQUFVLEtBQUssQ0FBQyxFQUFFO2NBQ3BCck0sT0FBTyxDQUFDQyxHQUFHLENBQUMscURBQXFELENBQUM7Y0FDbEUsTUFBTXRDLE1BQU0sQ0FBQ1csS0FBSyxDQUFDeUYsV0FBVyxDQUFDLElBQUksQ0FBQzVGLE1BQU0sRUFBRTtnQkFDMUM2RixJQUFJLEVBQUU7a0JBQUV2RixLQUFLLEVBQUUsQ0FBQyxPQUFPO2dCQUFFO2NBQzNCLENBQUMsQ0FBQztjQUNGLE9BQU8sa0JBQWtCO1lBQzNCO1lBQ0EsT0FBTyxtQkFBbUI7VUFDNUI7VUFFQSxPQUFPLHVCQUF1QjtRQUNoQyxDQUFDLENBQUMsT0FBTzBFLEtBQUssRUFBRTtVQUNkbkQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLCtCQUErQixFQUFFQSxLQUFLLENBQUM7VUFDckQsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLG1CQUFtQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUM1RDtNQUNGLENBQUM7TUFFRCxNQUFNLHFCQUFxQmtKLENBQUEsRUFBRztRQUM1QixJQUFJLENBQUMsSUFBSSxDQUFDbk8sTUFBTSxFQUFFO1VBQ2hCLE1BQU0sSUFBSVIsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGdCQUFnQixFQUFFLHVCQUF1QixDQUFDO1FBQ25FO1FBRUEsSUFBSTtVQUFBLElBQUE0SixrQkFBQTtVQUNGLE1BQU1DLFdBQVcsR0FBRyxNQUFNN08sTUFBTSxDQUFDVyxLQUFLLENBQUNDLFlBQVksQ0FBQyxJQUFJLENBQUNKLE1BQU0sQ0FBQztVQUNoRSxJQUFJLEdBQUFvTyxrQkFBQSxHQUFDQyxXQUFXLENBQUMvTixLQUFLLGNBQUE4TixrQkFBQSxlQUFqQkEsa0JBQUEsQ0FBbUI3TixRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUU7WUFDekMsTUFBTSxJQUFJZixNQUFNLENBQUNnRixLQUFLLENBQUMsZ0JBQWdCLEVBQUUsZ0NBQWdDLENBQUM7VUFDNUU7VUFFQSxNQUFNb0UsUUFBUSxHQUFHLE1BQU1wSixNQUFNLENBQUNXLEtBQUssQ0FBQ0ssSUFBSSxDQUFDLENBQUMsQ0FBQ3FJLFVBQVUsQ0FBQyxDQUFDO1VBQ3ZELE1BQU15RixlQUFlLEdBQUcsRUFBRTtVQUMxQixNQUFNQyxLQUFLLEdBQUcsRUFBRTtVQUVoQixLQUFLLE1BQU1yTyxJQUFJLElBQUkwSSxRQUFRLEVBQUU7WUFBQSxJQUFBNEYsY0FBQSxFQUFBNUssWUFBQTtZQUMzQixNQUFNNkssTUFBTSxHQUFHLEVBQUU7O1lBRWpCO1lBQ0EsSUFBSSxDQUFDdk8sSUFBSSxDQUFDSSxLQUFLLElBQUksQ0FBQ3NDLEtBQUssQ0FBQ3dHLE9BQU8sQ0FBQ2xKLElBQUksQ0FBQ0ksS0FBSyxDQUFDLEVBQUU7Y0FBQSxJQUFBb08sY0FBQSxFQUFBQyxhQUFBLEVBQUFDLGNBQUE7Y0FDN0NILE1BQU0sQ0FBQ3BDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQztjQUM3QjtjQUNBLE1BQU10QyxJQUFJLEdBQUcsRUFBQTJFLGNBQUEsR0FBQXhPLElBQUksQ0FBQ29ELE9BQU8sY0FBQW9MLGNBQUEsdUJBQVpBLGNBQUEsQ0FBYzNFLElBQUksS0FBSSxhQUFhO2NBQ2hELE1BQU12SyxNQUFNLENBQUNXLEtBQUssQ0FBQ3lGLFdBQVcsQ0FBQzFGLElBQUksQ0FBQzRDLEdBQUcsRUFBRTtnQkFDdkMrQyxJQUFJLEVBQUU7a0JBQUV2RixLQUFLLEVBQUUsQ0FBQ3lKLElBQUk7Z0JBQUU7Y0FDeEIsQ0FBQyxDQUFDO2NBQ0Z3RSxLQUFLLENBQUNsQyxJQUFJLDBCQUFBaEosTUFBQSxFQUFBc0wsYUFBQSxHQUEwQnpPLElBQUksQ0FBQzhDLE1BQU0sY0FBQTJMLGFBQUEsd0JBQUFDLGNBQUEsR0FBWEQsYUFBQSxDQUFjLENBQUMsQ0FBQyxjQUFBQyxjQUFBLHVCQUFoQkEsY0FBQSxDQUFrQjFGLE9BQU8sQ0FBRSxDQUFDO1lBQ2xFOztZQUVBO1lBQ0EsSUFBSSxDQUFBc0YsY0FBQSxHQUFBdE8sSUFBSSxDQUFDb0QsT0FBTyxjQUFBa0wsY0FBQSxlQUFaQSxjQUFBLENBQWN6RSxJQUFJLElBQUksRUFBQW5HLFlBQUEsR0FBQTFELElBQUksQ0FBQ0ksS0FBSyxjQUFBc0QsWUFBQSx1QkFBVkEsWUFBQSxDQUFhLENBQUMsQ0FBQyxNQUFLMUQsSUFBSSxDQUFDb0QsT0FBTyxDQUFDeUcsSUFBSSxFQUFFO2NBQUEsSUFBQThFLGFBQUEsRUFBQUMsY0FBQTtjQUMvREwsTUFBTSxDQUFDcEMsSUFBSSxDQUFDLDRCQUE0QixDQUFDO2NBQ3pDO2NBQ0EsTUFBTTdNLE1BQU0sQ0FBQ1csS0FBSyxDQUFDeUYsV0FBVyxDQUFDMUYsSUFBSSxDQUFDNEMsR0FBRyxFQUFFO2dCQUN2QytDLElBQUksRUFBRTtrQkFBRXZGLEtBQUssRUFBRSxDQUFDSixJQUFJLENBQUNvRCxPQUFPLENBQUN5RyxJQUFJO2dCQUFFO2NBQ3JDLENBQUMsQ0FBQztjQUNGd0UsS0FBSyxDQUFDbEMsSUFBSSw0QkFBQWhKLE1BQUEsRUFBQXdMLGFBQUEsR0FBNEIzTyxJQUFJLENBQUM4QyxNQUFNLGNBQUE2TCxhQUFBLHdCQUFBQyxjQUFBLEdBQVhELGFBQUEsQ0FBYyxDQUFDLENBQUMsY0FBQUMsY0FBQSx1QkFBaEJBLGNBQUEsQ0FBa0I1RixPQUFPLENBQUUsQ0FBQztZQUNwRTtZQUVBLElBQUl1RixNQUFNLENBQUN4TSxNQUFNLEdBQUcsQ0FBQyxFQUFFO2NBQUEsSUFBQThNLGFBQUEsRUFBQUMsY0FBQTtjQUNyQlYsZUFBZSxDQUFDakMsSUFBSSxDQUFDO2dCQUNuQnBELEtBQUssR0FBQThGLGFBQUEsR0FBRTdPLElBQUksQ0FBQzhDLE1BQU0sY0FBQStMLGFBQUEsd0JBQUFDLGNBQUEsR0FBWEQsYUFBQSxDQUFjLENBQUMsQ0FBQyxjQUFBQyxjQUFBLHVCQUFoQkEsY0FBQSxDQUFrQjlGLE9BQU87Z0JBQ2hDdUY7Y0FDRixDQUFDLENBQUM7WUFDSjtVQUNGO1VBRUEsT0FBTztZQUNMSCxlQUFlO1lBQ2ZDLEtBQUs7WUFDTHRKLE9BQU8sRUFBRXNKLEtBQUssQ0FBQ3RNLE1BQU0sR0FBRyxDQUFDLEdBQUcsbUJBQW1CLEdBQUc7VUFDcEQsQ0FBQztRQUNILENBQUMsQ0FBQyxPQUFPK0MsS0FBSyxFQUFFO1VBQ2QsTUFBTSxJQUFJeEYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixFQUFFUSxLQUFLLENBQUNDLE9BQU8sQ0FBQztRQUMxRDtNQUNGLENBQUM7TUFFRCw0QkFBNEJnSyxDQUFBLEVBQUc7UUFDN0I7UUFDQSxJQUFJLENBQUM1RSxPQUFPLENBQUNDLEdBQUcsQ0FBQzRFLFFBQVEsSUFBSTdFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDNEUsUUFBUSxLQUFLLGFBQWEsRUFBRTtVQUNuRSxJQUFJO1lBQ0YsTUFBTUMsVUFBVSxHQUFHO2NBQ2pCbEcsS0FBSyxFQUFFLHdCQUF3QjtjQUMvQlcsUUFBUSxFQUFFLGNBQWM7Y0FDeEJyRyxTQUFTLEVBQUUsTUFBTTtjQUNqQkMsUUFBUSxFQUFFO1lBQ1osQ0FBQztZQUVELE1BQU14RCxNQUFNLEdBQUdnSSxRQUFRLENBQUN1RSxVQUFVLENBQUM7Y0FDakN0RCxLQUFLLEVBQUVrRyxVQUFVLENBQUNsRyxLQUFLO2NBQ3ZCVyxRQUFRLEVBQUV1RixVQUFVLENBQUN2RixRQUFRO2NBQzdCdEcsT0FBTyxFQUFFO2dCQUNQQyxTQUFTLEVBQUU0TCxVQUFVLENBQUM1TCxTQUFTO2dCQUMvQkMsUUFBUSxFQUFFMkwsVUFBVSxDQUFDM0wsUUFBUTtnQkFDN0J1RyxJQUFJLEVBQUUsYUFBYTtnQkFDbkJDLFFBQVEsS0FBQTNHLE1BQUEsQ0FBSzhMLFVBQVUsQ0FBQzVMLFNBQVMsT0FBQUYsTUFBQSxDQUFJOEwsVUFBVSxDQUFDM0wsUUFBUTtjQUMxRDtZQUNGLENBQUMsQ0FBQzs7WUFFRjtZQUNBaEUsTUFBTSxDQUFDVyxLQUFLLENBQUM4RixNQUFNLENBQUNqRyxNQUFNLEVBQUU7Y0FDMUI2RixJQUFJLEVBQUU7Z0JBQUV2RixLQUFLLEVBQUUsQ0FBQyxhQUFhO2NBQUU7WUFDakMsQ0FBQyxDQUFDO1lBRUYsT0FBTztjQUNMcU4sT0FBTyxFQUFFLElBQUk7Y0FDYjNOLE1BQU07Y0FDTmlGLE9BQU8sRUFBRTtZQUNYLENBQUM7VUFDSCxDQUFDLENBQUMsT0FBT0QsS0FBSyxFQUFFO1lBQ2RuRCxPQUFPLENBQUNtRCxLQUFLLENBQUMsK0JBQStCLEVBQUVBLEtBQUssQ0FBQztZQUNyRCxNQUFNLElBQUl4RixNQUFNLENBQUNnRixLQUFLLENBQUMsMkJBQTJCLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxDQUFDO1VBQ3BFO1FBQ0YsQ0FBQyxNQUFNO1VBQ0wsTUFBTSxJQUFJekYsTUFBTSxDQUFDZ0YsS0FBSyxDQUFDLGlCQUFpQixFQUFFLDhDQUE4QyxDQUFDO1FBQzNGO01BQ0Y7SUFDRixDQUFDLENBQUM7SUFBQ3pGLHNCQUFBO0VBQUEsU0FBQUMsV0FBQTtJQUFBLE9BQUFELHNCQUFBLENBQUFDLFdBQUE7RUFBQTtFQUFBRCxzQkFBQTtBQUFBO0VBQUFFLElBQUE7RUFBQUMsS0FBQTtBQUFBLEciLCJmaWxlIjoiL2FwcC5qcyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1vbmdvIH0gZnJvbSAnbWV0ZW9yL21vbmdvJztcclxuXHJcbmV4cG9ydCBjb25zdCBMaW5rc0NvbGxlY3Rpb24gPSBuZXcgTW9uZ28uQ29sbGVjdGlvbignbGlua3MnKTtcclxuIiwiaW1wb3J0IHsgTWV0ZW9yIH0gZnJvbSAnbWV0ZW9yL21ldGVvcic7XG5pbXBvcnQgeyBNb25nbyB9IGZyb20gJ21ldGVvci9tb25nbyc7XG5pbXBvcnQgeyBjaGVjayB9IGZyb20gJ21ldGVvci9jaGVjayc7XG5pbXBvcnQgeyBNYXRjaCB9IGZyb20gJ21ldGVvci9jaGVjayc7XG5cbmV4cG9ydCBjb25zdCBUYXNrcyA9IG5ldyBNb25nby5Db2xsZWN0aW9uKCd0YXNrcycpO1xuXG5leHBvcnQgY29uc3QgdGFza0NhdGVnb3JpZXMgPSBbXG4gICdEZXZlbG9wbWVudCcsXG4gICdEZXNpZ24nLFxuICAnTWFya2V0aW5nJyxcbiAgJ1NhbGVzJyxcbiAgJ1N1cHBvcnQnLFxuICAnUGxhbm5pbmcnLFxuICAnUmVzZWFyY2gnLFxuICAnT3RoZXInXG5dO1xuXG5leHBvcnQgY29uc3QgdGFza0xhYmVscyA9IFtcbiAgeyBuYW1lOiAnQnVnJywgY29sb3I6ICcjZWY0NDQ0JyB9LFxuICB7IG5hbWU6ICdGZWF0dXJlJywgY29sb3I6ICcjM2I4MmY2JyB9LFxuICB7IG5hbWU6ICdFbmhhbmNlbWVudCcsIGNvbG9yOiAnIzEwYjk4MScgfSxcbiAgeyBuYW1lOiAnRG9jdW1lbnRhdGlvbicsIGNvbG9yOiAnIzhiNWNmNicgfSxcbiAgeyBuYW1lOiAnVXJnZW50JywgY29sb3I6ICcjZjU5ZTBiJyB9LFxuICB7IG5hbWU6ICdCbG9ja2VkJywgY29sb3I6ICcjNmI3MjgwJyB9XG5dO1xuXG5pZiAoTWV0ZW9yLmlzU2VydmVyKSB7XG4gIC8vIFB1YmxpY2F0aW9uc1xuICBNZXRlb3IucHVibGlzaCgndGFza3MnLCBhc3luYyBmdW5jdGlvbigpIHtcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICByZXR1cm4gdGhpcy5yZWFkeSgpO1xuICAgIH1cblxuICAgIC8vIEdldCB1c2VyJ3Mgcm9sZVxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICBjb25zdCBpc0FkbWluID0gdXNlcj8ucm9sZXM/LmluY2x1ZGVzKCdhZG1pbicpO1xuXG4gICAgLy8gSWYgYWRtaW4sIHNob3cgYWxsIHRhc2tzXG4gICAgaWYgKGlzQWRtaW4pIHtcbiAgICAgIHJldHVybiBUYXNrcy5maW5kKHt9LCB7IFxuICAgICAgICBzb3J0OiB7IGNyZWF0ZWRBdDogLTEgfSxcbiAgICAgICAgZmllbGRzOiB7XG4gICAgICAgICAgdGl0bGU6IDEsXG4gICAgICAgICAgZGVzY3JpcHRpb246IDEsXG4gICAgICAgICAgc3RhcnREYXRlOiAxLFxuICAgICAgICAgIGR1ZURhdGU6IDEsXG4gICAgICAgICAgcHJpb3JpdHk6IDEsXG4gICAgICAgICAgc3RhdHVzOiAxLFxuICAgICAgICAgIGFzc2lnbmVkVG86IDEsXG4gICAgICAgICAgY2hlY2tsaXN0OiAxLFxuICAgICAgICAgIGNhdGVnb3J5OiAxLFxuICAgICAgICAgIGxhYmVsczogMSxcbiAgICAgICAgICBwcm9ncmVzczogMSxcbiAgICAgICAgICBhdHRhY2htZW50czogMSxcbiAgICAgICAgICBsaW5rczogMSxcbiAgICAgICAgICBjcmVhdGVkQXQ6IDEsXG4gICAgICAgICAgY3JlYXRlZEJ5OiAxLFxuICAgICAgICAgIHVwZGF0ZWRBdDogMSxcbiAgICAgICAgICB1cGRhdGVkQnk6IDFcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gRm9yIHRlYW0gbWVtYmVycywgc2hvdyB0YXNrcyB0aGV5J3JlIGFzc2lnbmVkIHRvIG9yIGNyZWF0ZWRcbiAgICByZXR1cm4gVGFza3MuZmluZCh7XG4gICAgICAkb3I6IFtcbiAgICAgICAgeyBhc3NpZ25lZFRvOiB0aGlzLnVzZXJJZCB9LFxuICAgICAgICB7IGNyZWF0ZWRCeTogdGhpcy51c2VySWQgfVxuICAgICAgXVxuICAgIH0sIHsgXG4gICAgICBzb3J0OiB7IGNyZWF0ZWRBdDogLTEgfSxcbiAgICAgIGZpZWxkczoge1xuICAgICAgICB0aXRsZTogMSxcbiAgICAgICAgZGVzY3JpcHRpb246IDEsXG4gICAgICAgIHN0YXJ0RGF0ZTogMSxcbiAgICAgICAgZHVlRGF0ZTogMSxcbiAgICAgICAgcHJpb3JpdHk6IDEsXG4gICAgICAgIHN0YXR1czogMSxcbiAgICAgICAgYXNzaWduZWRUbzogMSxcbiAgICAgICAgY2hlY2tsaXN0OiAxLFxuICAgICAgICBjYXRlZ29yeTogMSxcbiAgICAgICAgbGFiZWxzOiAxLFxuICAgICAgICBwcm9ncmVzczogMSxcbiAgICAgICAgYXR0YWNobWVudHM6IDEsXG4gICAgICAgIGxpbmtzOiAxLFxuICAgICAgICBjcmVhdGVkQXQ6IDEsXG4gICAgICAgIGNyZWF0ZWRCeTogMSxcbiAgICAgICAgdXBkYXRlZEF0OiAxLFxuICAgICAgICB1cGRhdGVkQnk6IDFcbiAgICAgIH1cbiAgICB9KTtcbiAgfSk7XG5cbiAgLy8gUHVibGlzaCB1c2VyIGRhdGEgZm9yIHRhc2tzXG4gIE1ldGVvci5wdWJsaXNoKCd0YXNrVXNlcnMnLCBmdW5jdGlvbigpIHtcbiAgICBjb25zb2xlLmxvZygnU3RhcnRpbmcgdGFza1VzZXJzIHB1YmxpY2F0aW9uJyk7XG4gICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgY29uc29sZS5sb2coJ05vIHVzZXJJZCwgcmV0dXJuaW5nIHJlYWR5Jyk7XG4gICAgICByZXR1cm4gdGhpcy5yZWFkeSgpO1xuICAgIH1cblxuICAgIC8vIEdldCBhbGwgdGFza3NcbiAgICBjb25zdCB0YXNrcyA9IFRhc2tzLmZpbmQoe30pLmZldGNoKCk7XG4gICAgY29uc29sZS5sb2coJ0ZvdW5kIHRhc2tzOicsIHRhc2tzLmxlbmd0aCk7XG4gICAgXG4gICAgLy8gQ29sbGVjdCBhbGwgdXNlciBJRHMgZnJvbSB0YXNrc1xuICAgIGNvbnN0IHVzZXJJZHMgPSBuZXcgU2V0KCk7XG4gICAgdGFza3MuZm9yRWFjaCh0YXNrID0+IHtcbiAgICAgIC8vIEFkZCB1c2VycyB3aG8gdXBsb2FkZWQgYXR0YWNobWVudHNcbiAgICAgIGlmICh0YXNrLmF0dGFjaG1lbnRzKSB7XG4gICAgICAgIHRhc2suYXR0YWNobWVudHMuZm9yRWFjaChhdHRhY2htZW50ID0+IHtcbiAgICAgICAgICBpZiAoYXR0YWNobWVudC51cGxvYWRlZEJ5KSB7XG4gICAgICAgICAgICB1c2VySWRzLmFkZChTdHJpbmcoYXR0YWNobWVudC51cGxvYWRlZEJ5KSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIC8vIEFkZCB1c2VycyB3aG8gYWRkZWQgbGlua3NcbiAgICAgIGlmICh0YXNrLmxpbmtzKSB7XG4gICAgICAgIHRhc2subGlua3MuZm9yRWFjaChsaW5rID0+IHtcbiAgICAgICAgICBpZiAobGluay5hZGRlZEJ5KSB7XG4gICAgICAgICAgICB1c2VySWRzLmFkZChTdHJpbmcobGluay5hZGRlZEJ5KSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIC8vIEFkZCBhc3NpZ25lZCB1c2Vyc1xuICAgICAgaWYgKHRhc2suYXNzaWduZWRUbykge1xuICAgICAgICB0YXNrLmFzc2lnbmVkVG8uZm9yRWFjaCh1c2VySWQgPT4ge1xuICAgICAgICAgIHVzZXJJZHMuYWRkKFN0cmluZyh1c2VySWQpKTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBjb25zdCB1c2VySWRBcnJheSA9IEFycmF5LmZyb20odXNlcklkcyk7XG4gICAgY29uc29sZS5sb2coJ1B1Ymxpc2hpbmcgdXNlciBkYXRhIGZvciBJRHM6JywgdXNlcklkQXJyYXkpO1xuXG4gICAgLy8gRmluZCB1c2VycyBhbmQgbG9nIHdoYXQgd2UgZm91bmRcbiAgICBjb25zdCB1c2VycyA9IE1ldGVvci51c2Vycy5maW5kKFxuICAgICAgeyBfaWQ6IHsgJGluOiB1c2VySWRBcnJheSB9IH0sXG4gICAgICB7IFxuICAgICAgICBmaWVsZHM6IHtcbiAgICAgICAgICBfaWQ6IDEsXG4gICAgICAgICAgZW1haWxzOiAxLFxuICAgICAgICAgIHJvbGVzOiAxLFxuICAgICAgICAgICdwcm9maWxlLmZpcnN0TmFtZSc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUubGFzdE5hbWUnOiAxLFxuICAgICAgICAgICdwcm9maWxlLnJvbGUnOiAxLFxuICAgICAgICAgICdwcm9maWxlLmRlcGFydG1lbnQnOiAxLFxuICAgICAgICAgICdwcm9maWxlLnNraWxscyc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUuam9pbkRhdGUnOiAxLFxuICAgICAgICAgIGNyZWF0ZWRBdDogMVxuICAgICAgICB9IFxuICAgICAgfVxuICAgICkuZmV0Y2goKTtcblxuICAgIGNvbnNvbGUubG9nKCdGb3VuZCB1c2VyczonLCB1c2Vycy5tYXAodSA9PiAoe1xuICAgICAgX2lkOiB1Ll9pZCxcbiAgICAgIG5hbWU6IGAke3UucHJvZmlsZT8uZmlyc3ROYW1lIHx8ICcnfSAke3UucHJvZmlsZT8ubGFzdE5hbWUgfHwgJyd9YC50cmltKCksXG4gICAgICBoYXNQcm9maWxlOiAhIXUucHJvZmlsZVxuICAgIH0pKSk7XG5cbiAgICAvLyBSZXR1cm4gdGhlIGN1cnNvclxuICAgIHJldHVybiBNZXRlb3IudXNlcnMuZmluZChcbiAgICAgIHsgX2lkOiB7ICRpbjogdXNlcklkQXJyYXkgfSB9LFxuICAgICAgeyBcbiAgICAgIGZpZWxkczoge1xuICAgICAgICBfaWQ6IDEsXG4gICAgICAgICAgZW1haWxzOiAxLFxuICAgICAgICAgIHJvbGVzOiAxLFxuICAgICAgICAncHJvZmlsZS5maXJzdE5hbWUnOiAxLFxuICAgICAgICAncHJvZmlsZS5sYXN0TmFtZSc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUucm9sZSc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUuZGVwYXJ0bWVudCc6IDEsXG4gICAgICAgICAgJ3Byb2ZpbGUuc2tpbGxzJzogMSxcbiAgICAgICAgICAncHJvZmlsZS5qb2luRGF0ZSc6IDEsXG4gICAgICAgICAgY3JlYXRlZEF0OiAxXG4gICAgICAgIH0gXG4gICAgICB9XG4gICAgKTtcbiAgfSk7XG5cbiAgLy8gQWRkIGEgc3BlY2lmaWMgcHVibGljYXRpb24gZm9yIGEgc2luZ2xlIHRhc2tcbiAgTWV0ZW9yLnB1Ymxpc2goJ3Rhc2snLCBmdW5jdGlvbih0YXNrSWQpIHtcbiAgICBjaGVjayh0YXNrSWQsIFN0cmluZyk7XG4gICAgXG4gICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgcmV0dXJuIHRoaXMucmVhZHkoKTtcbiAgICB9XG5cbiAgICBjb25zdCB1c2VyID0gTWV0ZW9yLnVzZXJzLmZpbmRPbmUodGhpcy51c2VySWQpO1xuICAgIGNvbnN0IGlzQWRtaW4gPSB1c2VyPy5yb2xlcz8uaW5jbHVkZXMoJ2FkbWluJyk7XG5cbiAgICAvLyBSZXR1cm4gYSBjdXJzb3IgdGhhdCB3aWxsIHVwZGF0ZSByZWFjdGl2ZWx5XG4gICAgY29uc3QgY3Vyc29yID0gVGFza3MuZmluZChcbiAgICAgIHsgX2lkOiB0YXNrSWQgfSxcbiAgICAgIHtcbiAgICAgICAgZmllbGRzOiB7XG4gICAgICAgICAgdGl0bGU6IDEsXG4gICAgICAgICAgZGVzY3JpcHRpb246IDEsXG4gICAgICAgICAgc3RhcnREYXRlOiAxLFxuICAgICAgICAgIGR1ZURhdGU6IDEsXG4gICAgICAgICAgcHJpb3JpdHk6IDEsXG4gICAgICAgICAgc3RhdHVzOiAxLFxuICAgICAgICAgIGFzc2lnbmVkVG86IDEsXG4gICAgICAgICAgY2hlY2tsaXN0OiAxLFxuICAgICAgICAgIGNhdGVnb3J5OiAxLFxuICAgICAgICAgIGxhYmVsczogMSxcbiAgICAgICAgICBwcm9ncmVzczogMSxcbiAgICAgICAgICBhdHRhY2htZW50czogMSxcbiAgICAgICAgICBsaW5rczogMSxcbiAgICAgICAgICBjcmVhdGVkQXQ6IDEsXG4gICAgICAgICAgY3JlYXRlZEJ5OiAxLFxuICAgICAgICAgIHVwZGF0ZWRBdDogMSxcbiAgICAgICAgICB1cGRhdGVkQnk6IDFcbiAgICAgICAgfVxuICAgICAgfVxuICAgICk7XG5cbiAgICAvLyBMb2cgZm9yIGRlYnVnZ2luZ1xuICAgIGNvbnNvbGUubG9nKCdQdWJsaXNoaW5nIHRhc2s6JywgdGFza0lkKTtcbiAgICBjdXJzb3Iub2JzZXJ2ZSh7XG4gICAgICBhZGRlZDogKGRvYykgPT4gY29uc29sZS5sb2coJ1Rhc2sgYWRkZWQgdG8gcHVibGljYXRpb246JywgZG9jLl9pZCksXG4gICAgICBjaGFuZ2VkOiAoZG9jKSA9PiBjb25zb2xlLmxvZygnVGFzayBjaGFuZ2VkIGluIHB1YmxpY2F0aW9uOicsIGRvYy5faWQpLFxuICAgICAgcmVtb3ZlZDogKGRvYykgPT4gY29uc29sZS5sb2coJ1Rhc2sgcmVtb3ZlZCBmcm9tIHB1YmxpY2F0aW9uOicsIGRvYy5faWQpXG4gICAgfSk7XG5cbiAgICByZXR1cm4gY3Vyc29yO1xuICB9KTtcbn1cblxuTWV0ZW9yLm1ldGhvZHMoe1xuICBhc3luYyAndGFza3MuaW5zZXJ0Jyh0YXNrKSB7XG4gICAgY2hlY2sodGFzaywge1xuICAgICAgdGl0bGU6IFN0cmluZyxcbiAgICAgIGRlc2NyaXB0aW9uOiBTdHJpbmcsXG4gICAgICBzdGFydERhdGU6IERhdGUsXG4gICAgICBkdWVEYXRlOiBEYXRlLFxuICAgICAgcHJpb3JpdHk6IFN0cmluZyxcbiAgICAgIHN0YXR1czogU3RyaW5nLFxuICAgICAgYXNzaWduZWRUbzogQXJyYXksXG4gICAgICBjaGVja2xpc3Q6IEFycmF5LFxuICAgICAgY2F0ZWdvcnk6IFN0cmluZyxcbiAgICAgIGxhYmVsczogQXJyYXksXG4gICAgICBwcm9ncmVzczogTnVtYmVyXG4gICAgfSk7XG5cbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdOb3QgYXV0aG9yaXplZC4nKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgbmV3IHRhc2s6JywgdGFzayk7IC8vIERlYnVnIGxvZ1xuXG4gICAgLy8gUHJvY2VzcyBjaGVja2xpc3QgaXRlbXNcbiAgICBjb25zdCBwcm9jZXNzZWRDaGVja2xpc3QgPSB0YXNrLmNoZWNrbGlzdC5tYXAoaXRlbSA9PiAoe1xuICAgICAgdGV4dDogaXRlbS50ZXh0LFxuICAgICAgY29tcGxldGVkOiBpdGVtLmNvbXBsZXRlZCB8fCBmYWxzZVxuICAgIH0pKTtcblxuICAgIGNvbnN0IHRhc2tUb0luc2VydCA9IHtcbiAgICAgIC4uLnRhc2ssXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICBjcmVhdGVkQnk6IHRoaXMudXNlcklkLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgdXBkYXRlZEJ5OiB0aGlzLnVzZXJJZCxcbiAgICAgIHByb2dyZXNzOiB0YXNrLnByb2dyZXNzIHx8IDAsXG4gICAgICBzdGF0dXM6ICdwZW5kaW5nJywgLy8gRGVmYXVsdCBzdGF0dXNcbiAgICAgIGNoZWNrbGlzdDogcHJvY2Vzc2VkQ2hlY2tsaXN0LFxuICAgICAgbGFiZWxzOiB0YXNrLmxhYmVscyB8fCBbXSxcbiAgICAgIGNhdGVnb3J5OiB0YXNrLmNhdGVnb3J5IHx8ICcnLFxuICAgICAgYXNzaWduZWRUbzogdGFzay5hc3NpZ25lZFRvIHx8IFtdXG4gICAgfTtcblxuICAgIGNvbnNvbGUubG9nKCdJbnNlcnRpbmcgdGFzayB3aXRoIHZhbHVlczonLCB0YXNrVG9JbnNlcnQpOyAvLyBEZWJ1ZyBsb2dcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXNrcy5pbnNlcnRBc3luYyh0YXNrVG9JbnNlcnQpO1xuICAgICAgY29uc29sZS5sb2coJ1Rhc2sgY3JlYXRlZCBzdWNjZXNzZnVsbHk6JywgcmVzdWx0KTsgLy8gRGVidWcgbG9nXG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB0YXNrOicsIGVycm9yKTtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3Rhc2stY3JlYXRpb24tZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gIGFzeW5jICd0YXNrcy51cGRhdGUnKHRhc2tJZCwgdGFzaykge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnU3RhcnRpbmcgdGFzayB1cGRhdGU6JywgeyB0YXNrSWQsIHRhc2sgfSk7XG5cbiAgICAgIGNoZWNrKHRhc2tJZCwgU3RyaW5nKTtcbiAgICAgIGNoZWNrKHRhc2ssIHtcbiAgICAgICAgdGl0bGU6IFN0cmluZyxcbiAgICAgICAgZGVzY3JpcHRpb246IFN0cmluZyxcbiAgICAgICAgc3RhcnREYXRlOiBEYXRlLFxuICAgICAgICBkdWVEYXRlOiBEYXRlLFxuICAgICAgICBwcmlvcml0eTogU3RyaW5nLFxuICAgICAgICBhc3NpZ25lZFRvOiBBcnJheSxcbiAgICAgICAgY2hlY2tsaXN0OiBBcnJheSxcbiAgICAgICAgY2F0ZWdvcnk6IE1hdGNoLk9wdGlvbmFsKFN0cmluZyksXG4gICAgICAgIGxhYmVsczogTWF0Y2guT3B0aW9uYWwoQXJyYXkpLFxuICAgICAgICBwcm9ncmVzczogTWF0Y2guT3B0aW9uYWwoTnVtYmVyKSxcbiAgICAgICAgc3RhdHVzOiBNYXRjaC5PcHRpb25hbChTdHJpbmcpLFxuICAgICAgICBhdHRhY2htZW50czogTWF0Y2guT3B0aW9uYWwoQXJyYXkpXG4gICAgICB9KTtcblxuICAgICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4gdG8gcGVyZm9ybSB0aGlzIGFjdGlvbicpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBleGlzdGluZ1Rhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGlmICghZXhpc3RpbmdUYXNrKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1mb3VuZCcsICdUYXNrIG5vdCBmb3VuZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFzc2lnbmVkIHRvIHRoZSB0YXNrIG9yIGlzIGFkbWluXG4gICAgICBjb25zdCB1c2VyID0gYXdhaXQgTWV0ZW9yLnVzZXJzLmZpbmRPbmVBc3luYyh0aGlzLnVzZXJJZCk7XG4gICAgICBjb25zdCBpc0FkbWluID0gdXNlcj8ucm9sZXM/LmluY2x1ZGVzKCdhZG1pbicpO1xuICAgICAgXG4gICAgICBpZiAoIWlzQWRtaW4gJiYgIWV4aXN0aW5nVGFzay5hc3NpZ25lZFRvLmluY2x1ZGVzKHRoaXMudXNlcklkKSkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgYXJlIG5vdCBhdXRob3JpemVkIHRvIG1vZGlmeSB0aGlzIHRhc2snKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2FsY3VsYXRlIHByb2dyZXNzIGJhc2VkIG9uIGNoZWNrbGlzdFxuICAgICAgY29uc3QgY29tcGxldGVkSXRlbXMgPSB0YXNrLmNoZWNrbGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmNvbXBsZXRlZCkubGVuZ3RoO1xuICAgICAgY29uc3QgdG90YWxJdGVtcyA9IHRhc2suY2hlY2tsaXN0Lmxlbmd0aDtcbiAgICAgIGNvbnN0IHByb2dyZXNzID0gdG90YWxJdGVtcyA+IDAgPyBNYXRoLnJvdW5kKChjb21wbGV0ZWRJdGVtcyAvIHRvdGFsSXRlbXMpICogMTAwKSA6IDA7XG5cbiAgICAgIC8vIFVwZGF0ZSB0YXNrIHN0YXR1cyBiYXNlZCBvbiBwcm9ncmVzc1xuICAgICAgbGV0IHN0YXR1cyA9IHRhc2suc3RhdHVzO1xuICAgICAgaWYgKHByb2dyZXNzID09PSAxMDApIHtcbiAgICAgICAgc3RhdHVzID0gJ2NvbXBsZXRlZCc7XG4gICAgICB9IGVsc2UgaWYgKHByb2dyZXNzID4gMCkge1xuICAgICAgICBzdGF0dXMgPSAnaW4tcHJvZ3Jlc3MnO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc3RhdHVzID0gJ3BlbmRpbmcnO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB0YXNrVG9VcGRhdGUgPSB7XG4gICAgICAgIC4uLnRhc2ssXG4gICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcbiAgICAgICAgdXBkYXRlZEJ5OiB0aGlzLnVzZXJJZCxcbiAgICAgICAgcHJvZ3Jlc3MsXG4gICAgICAgIHN0YXR1cyxcbiAgICAgICAgY2F0ZWdvcnk6IHRhc2suY2F0ZWdvcnkgfHwgZXhpc3RpbmdUYXNrLmNhdGVnb3J5IHx8ICcnLFxuICAgICAgICBsYWJlbHM6IHRhc2subGFiZWxzIHx8IGV4aXN0aW5nVGFzay5sYWJlbHMgfHwgW10sXG4gICAgICAgIGF0dGFjaG1lbnRzOiB0YXNrLmF0dGFjaG1lbnRzIHx8IGV4aXN0aW5nVGFzay5hdHRhY2htZW50cyB8fCBbXVxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGFza3MudXBkYXRlQXN5bmMoXG4gICAgICAgIHsgX2lkOiB0YXNrSWQgfSxcbiAgICAgICAgeyAkc2V0OiB0YXNrVG9VcGRhdGUgfVxuICAgICAgKTtcblxuICAgICAgaWYgKHJlc3VsdCA9PT0gMCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd1cGRhdGUtZmFpbGVkJywgJ0ZhaWxlZCB0byB1cGRhdGUgdGFzaycpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB0YXNrOicsIGVycm9yKTtcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIE1ldGVvci5FcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3NlcnZlci1lcnJvcicsIGVycm9yLm1lc3NhZ2UgfHwgJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnKTtcbiAgICB9XG4gIH0sXG5cbiAgYXN5bmMgJ3Rhc2tzLmRlbGV0ZScodGFza0lkKSB7XG4gICAgY2hlY2sodGFza0lkLCBTdHJpbmcpO1xuXG4gICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignTm90IGF1dGhvcml6ZWQuJyk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhc2tzLnJlbW92ZUFzeW5jKHRhc2tJZCk7XG4gICAgICBcbiAgICAgIGlmIChyZXN1bHQgPT09IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWZvdW5kJywgJ1Rhc2sgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIHRhc2s6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndGFzay1kZWxldGUtZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gICd0YXNrcy51cGRhdGVQcm9ncmVzcycodGFza0lkLCBwcm9ncmVzcykge1xuICAgIGNoZWNrKHRhc2tJZCwgU3RyaW5nKTtcbiAgICBjaGVjayhwcm9ncmVzcywgTnVtYmVyKTtcblxuICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ05vdCBhdXRob3JpemVkLicpO1xuICAgIH1cblxuICAgIGNvbnN0IHRhc2sgPSBUYXNrcy5maW5kT25lKHRhc2tJZCk7XG4gICAgaWYgKCF0YXNrKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdUYXNrIG5vdCBmb3VuZC4nKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFzc2lnbmVkIHRvIHRoZSB0YXNrXG4gICAgaWYgKCF0YXNrLmFzc2lnbmVkVG8uaW5jbHVkZXModGhpcy51c2VySWQpKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdOb3QgYXV0aG9yaXplZCB0byBtb2RpZnkgdGhpcyB0YXNrLicpO1xuICAgIH1cblxuICAgIC8vIFVwZGF0ZSB0YXNrIHN0YXR1cyBiYXNlZCBvbiBwcm9ncmVzc1xuICAgIGxldCBzdGF0dXMgPSB0YXNrLnN0YXR1cztcbiAgICBpZiAocHJvZ3Jlc3MgPT09IDEwMCkge1xuICAgICAgc3RhdHVzID0gJ2NvbXBsZXRlZCc7XG4gICAgfSBlbHNlIGlmIChwcm9ncmVzcyA+IDApIHtcbiAgICAgIHN0YXR1cyA9ICdpbi1wcm9ncmVzcyc7XG4gICAgfVxuXG4gICAgcmV0dXJuIFRhc2tzLnVwZGF0ZSh0YXNrSWQsIHtcbiAgICAgICRzZXQ6IHtcbiAgICAgICAgcHJvZ3Jlc3MsXG4gICAgICAgIHN0YXR1cyxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICB1cGRhdGVkQnk6IHRoaXMudXNlcklkXG4gICAgICB9XG4gICAgfSk7XG4gIH0sXG5cbiAgYXN5bmMgJ3Rhc2tzLnRvZ2dsZUNoZWNrbGlzdEl0ZW0nKHRhc2tJZCwgaXRlbUluZGV4KSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCdTdGFydGluZyB0b2dnbGVDaGVja2xpc3RJdGVtIHdpdGg6JywgeyB0YXNrSWQsIGl0ZW1JbmRleCwgdXNlcklkOiB0aGlzLnVzZXJJZCB9KTtcblxuICAgICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgICBjb25zb2xlLmxvZygnTm8gdXNlciBJRCBmb3VuZCcpO1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4gdG8gcGVyZm9ybSB0aGlzIGFjdGlvbicpO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSBpbnB1dHNcbiAgICAgIGlmICghdGFza0lkIHx8IHR5cGVvZiB0YXNrSWQgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdJbnZhbGlkIHRhc2tJZDonLCB0YXNrSWQpO1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdpbnZhbGlkLWlucHV0JywgJ0ludmFsaWQgdGFzayBJRCcpO1xuICAgICAgfVxuXG4gICAgICBpZiAodHlwZW9mIGl0ZW1JbmRleCAhPT0gJ251bWJlcicgfHwgaXRlbUluZGV4IDwgMCkge1xuICAgICAgICBjb25zb2xlLmxvZygnSW52YWxpZCBpdGVtSW5kZXg6JywgaXRlbUluZGV4KTtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignaW52YWxpZC1pbnB1dCcsICdJbnZhbGlkIGNoZWNrbGlzdCBpdGVtIGluZGV4Jyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdGb3VuZCB0YXNrOicsIHRhc2spO1xuXG4gICAgICBpZiAoIXRhc2spIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1Rhc2sgbm90IGZvdW5kJyk7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1mb3VuZCcsICdUYXNrIG5vdCBmb3VuZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGFzc2lnbmVkIHRvIHRoZSB0YXNrIG9yIGlzIGFkbWluXG4gICAgICBjb25zdCB1c2VyID0gYXdhaXQgTWV0ZW9yLnVzZXJzLmZpbmRPbmVBc3luYyh0aGlzLnVzZXJJZCk7XG4gICAgICBjb25zb2xlLmxvZygnRm91bmQgdXNlcjonLCB1c2VyKTtcbiAgICAgIFxuICAgICAgY29uc3QgaXNBZG1pbiA9IHVzZXI/LnJvbGVzPy5pbmNsdWRlcygnYWRtaW4nKTtcbiAgICAgIGNvbnNvbGUubG9nKCdJcyBhZG1pbjonLCBpc0FkbWluKTtcbiAgICAgIFxuICAgICAgaWYgKCFpc0FkbWluICYmICF0YXNrLmFzc2lnbmVkVG8uaW5jbHVkZXModGhpcy51c2VySWQpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIG5vdCBhdXRob3JpemVkOicsIHsgdXNlcklkOiB0aGlzLnVzZXJJZCwgYXNzaWduZWRUbzogdGFzay5hc3NpZ25lZFRvIH0pO1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgYXJlIG5vdCBhdXRob3JpemVkIHRvIG1vZGlmeSB0aGlzIHRhc2snKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY2hlY2tsaXN0ID0gdGFzay5jaGVja2xpc3QgfHwgW107XG4gICAgICBjb25zb2xlLmxvZygnQ3VycmVudCBjaGVja2xpc3Q6JywgY2hlY2tsaXN0KTtcblxuICAgICAgaWYgKGl0ZW1JbmRleCA+PSBjaGVja2xpc3QubGVuZ3RoKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdJbnZhbGlkIGl0ZW0gaW5kZXg6JywgeyBpdGVtSW5kZXgsIGNoZWNrbGlzdExlbmd0aDogY2hlY2tsaXN0Lmxlbmd0aCB9KTtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignaW52YWxpZC1pbmRleCcsICdJbnZhbGlkIGNoZWNrbGlzdCBpdGVtIGluZGV4Jyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZSBhIG5ldyBhcnJheSB0byBlbnN1cmUgcmVhY3Rpdml0eVxuICAgICAgY29uc3QgdXBkYXRlZENoZWNrbGlzdCA9IFsuLi5jaGVja2xpc3RdO1xuICAgICAgdXBkYXRlZENoZWNrbGlzdFtpdGVtSW5kZXhdID0ge1xuICAgICAgICAuLi51cGRhdGVkQ2hlY2tsaXN0W2l0ZW1JbmRleF0sXG4gICAgICAgIGNvbXBsZXRlZDogIXVwZGF0ZWRDaGVja2xpc3RbaXRlbUluZGV4XS5jb21wbGV0ZWRcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKCdVcGRhdGVkIGNoZWNrbGlzdDonLCB1cGRhdGVkQ2hlY2tsaXN0KTtcblxuICAgICAgLy8gQ2FsY3VsYXRlIHByb2dyZXNzXG4gICAgICBjb25zdCBjb21wbGV0ZWRJdGVtcyA9IHVwZGF0ZWRDaGVja2xpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jb21wbGV0ZWQpLmxlbmd0aDtcbiAgICAgIGNvbnN0IHRvdGFsSXRlbXMgPSB1cGRhdGVkQ2hlY2tsaXN0Lmxlbmd0aDtcbiAgICAgIGNvbnN0IHByb2dyZXNzID0gdG90YWxJdGVtcyA+IDAgPyBNYXRoLnJvdW5kKChjb21wbGV0ZWRJdGVtcyAvIHRvdGFsSXRlbXMpICogMTAwKSA6IDA7XG5cbiAgICAgIC8vIFVwZGF0ZSB0YXNrIHN0YXR1cyBiYXNlZCBvbiBwcm9ncmVzc1xuICAgICAgbGV0IHN0YXR1cztcbiAgICAgIGlmIChwcm9ncmVzcyA9PT0gMTAwKSB7XG4gICAgICAgIHN0YXR1cyA9ICdjb21wbGV0ZWQnO1xuICAgICAgfSBlbHNlIGlmIChwcm9ncmVzcyA+IDApIHtcbiAgICAgICAgc3RhdHVzID0gJ2luLXByb2dyZXNzJztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN0YXR1cyA9ICdwZW5kaW5nJztcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ1VwZGF0aW5nIHRhc2sgd2l0aDonLCB7XG4gICAgICAgIHRhc2tJZCxcbiAgICAgICAgdXBkYXRlZENoZWNrbGlzdCxcbiAgICAgICAgcHJvZ3Jlc3MsXG4gICAgICAgIHN0YXR1c1xuICAgICAgfSk7XG5cbiAgICAgIC8vIEZpcnN0IHZlcmlmeSB0aGUgdGFzayBzdGlsbCBleGlzdHNcbiAgICAgIGNvbnN0IGV4aXN0aW5nVGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgaWYgKCFleGlzdGluZ1Rhc2spIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndGFzay1ub3QtZm91bmQnLCAnVGFzayBubyBsb25nZXIgZXhpc3RzJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFBlcmZvcm0gdGhlIHVwZGF0ZVxuICAgICAgY29uc3QgdXBkYXRlUmVzdWx0ID0gYXdhaXQgVGFza3MudXBkYXRlQXN5bmMoXG4gICAgICAgIHsgX2lkOiB0YXNrSWQgfSxcbiAgICAgICAge1xuICAgICAgICAgICRzZXQ6IHtcbiAgICAgICAgICAgIGNoZWNrbGlzdDogdXBkYXRlZENoZWNrbGlzdCxcbiAgICAgICAgICAgIHByb2dyZXNzLFxuICAgICAgICAgICAgc3RhdHVzLFxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgdXBkYXRlZEJ5OiB0aGlzLnVzZXJJZFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgICAgY29uc29sZS5sb2coJ1VwZGF0ZSByZXN1bHQ6JywgdXBkYXRlUmVzdWx0KTtcblxuICAgICAgaWYgKHVwZGF0ZVJlc3VsdCA9PT0gMCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd1cGRhdGUtZmFpbGVkJywgJ0ZhaWxlZCB0byB1cGRhdGUgdGFzaycpO1xuICAgICAgfVxuXG4gICAgICAvLyBWZXJpZnkgdGhlIHVwZGF0ZVxuICAgICAgY29uc3QgdXBkYXRlZFRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdUYXNrIGFmdGVyIHVwZGF0ZTonLCB1cGRhdGVkVGFzayk7XG5cbiAgICAgIHJldHVybiB1cGRhdGVSZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIHRvZ2dsZUNoZWNrbGlzdEl0ZW06JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgTWV0ZW9yLkVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignc2VydmVyLWVycm9yJywgZXJyb3IubWVzc2FnZSB8fCAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcpO1xuICAgIH1cbiAgfSxcblxuICBhc3luYyAndGFza3MuYWRkQXR0YWNobWVudCcodGFza0lkLCBmaWxlRGF0YSkge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBtdXN0IGJlIGxvZ2dlZCBpbiB0byBwZXJmb3JtIHRoaXMgYWN0aW9uJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGlmICghdGFzaykge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtZm91bmQnLCAnVGFzayBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhc3NpZ25lZCB0byB0aGUgdGFza1xuICAgICAgaWYgKCF0YXNrLmFzc2lnbmVkVG8uaW5jbHVkZXModGhpcy51c2VySWQpKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBhcmUgbm90IGF1dGhvcml6ZWQgdG8gYWRkIGF0dGFjaG1lbnRzIHRvIHRoaXMgdGFzaycpO1xuICAgICAgfVxuXG4gICAgICBpZiAoIWZpbGVEYXRhIHx8ICFmaWxlRGF0YS5uYW1lIHx8ICFmaWxlRGF0YS5kYXRhKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2ludmFsaWQtaW5wdXQnLCAnSW52YWxpZCBmaWxlIGRhdGEnKTtcbiAgICAgIH1cblxuICAgICAgLy8gRW5zdXJlIHdlJ3JlIHN0b3JpbmcgdGhlIHVzZXIgSUQgYXMgYSBzdHJpbmdcbiAgICAgIGNvbnN0IHVwbG9hZGVySWQgPSBTdHJpbmcodGhpcy51c2VySWQpO1xuXG4gICAgICAvLyBBZGQgdGhlIGZpbGUgZGF0YSB0byBhdHRhY2htZW50cyB3aXRoIHVwbG9hZGVyIGluZm9cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhc2tzLnVwZGF0ZUFzeW5jKFxuICAgICAgICB7IF9pZDogdGFza0lkIH0sXG4gICAgICAgIHtcbiAgICAgICAgICAkcHVzaDoge1xuICAgICAgICAgICAgYXR0YWNobWVudHM6IHtcbiAgICAgICAgICAgICAgbmFtZTogZmlsZURhdGEubmFtZSxcbiAgICAgICAgICAgICAgdHlwZTogZmlsZURhdGEudHlwZSxcbiAgICAgICAgICAgICAgZGF0YTogZmlsZURhdGEuZGF0YSxcbiAgICAgICAgICAgICAgdXBsb2FkZWRBdDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgICAgdXBsb2FkZWRCeTogdXBsb2FkZXJJZFxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgdXBkYXRlZEJ5OiB1cGxvYWRlcklkXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICBpZiAocmVzdWx0ID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VwZGF0ZS1mYWlsZWQnLCAnRmFpbGVkIHRvIGFkZCBhdHRhY2htZW50Jyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCBhbmQgcmV0dXJuIHRoZSB1cGRhdGVkIHRhc2tcbiAgICAgIGNvbnN0IHVwZGF0ZWRUYXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgICBjb25zb2xlLmxvZygnVGFzayBhZnRlciBhZGRpbmcgYXR0YWNobWVudDonLCB1cGRhdGVkVGFzayk7XG4gICAgICByZXR1cm4gdXBkYXRlZFRhc2s7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyBhdHRhY2htZW50OicsIGVycm9yKTtcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIE1ldGVvci5FcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3NlcnZlci1lcnJvcicsIGVycm9yLm1lc3NhZ2UgfHwgJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnKTtcbiAgICB9XG4gIH0sXG5cbiAgYXN5bmMgJ3Rhc2tzLmFkZExpbmsnKHRhc2tJZCwgbGluaykge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBtdXN0IGJlIGxvZ2dlZCBpbiB0byBwZXJmb3JtIHRoaXMgYWN0aW9uJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRhc2sgPSBhd2FpdCBUYXNrcy5maW5kT25lQXN5bmModGFza0lkKTtcbiAgICAgIGlmICghdGFzaykge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtZm91bmQnLCAnVGFzayBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhc3NpZ25lZCB0byB0aGUgdGFza1xuICAgICAgaWYgKCF0YXNrLmFzc2lnbmVkVG8uaW5jbHVkZXModGhpcy51c2VySWQpKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBhcmUgbm90IGF1dGhvcml6ZWQgdG8gYWRkIGxpbmtzIHRvIHRoaXMgdGFzaycpO1xuICAgICAgfVxuXG4gICAgICBpZiAoIWxpbmspIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignaW52YWxpZC1pbnB1dCcsICdMaW5rIFVSTCBpcyByZXF1aXJlZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSBVUkwgZm9ybWF0XG4gICAgICB0cnkge1xuICAgICAgICBuZXcgVVJMKGxpbmspO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdpbnZhbGlkLWlucHV0JywgJ0ludmFsaWQgVVJMIGZvcm1hdCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBBZGQgdGhlIGxpbmsgdG8gbGlua3MgYXJyYXkgd2l0aCBhZGRlciBpbmZvXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXNrcy51cGRhdGVBc3luYyhcbiAgICAgICAgeyBfaWQ6IHRhc2tJZCB9LFxuICAgICAgICB7XG4gICAgICAgICAgJHB1c2g6IHtcbiAgICAgICAgICAgIGxpbmtzOiB7XG4gICAgICAgICAgICAgIHVybDogbGluayxcbiAgICAgICAgICAgICAgYWRkZWRBdDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgICAgYWRkZWRCeTogU3RyaW5nKHRoaXMudXNlcklkKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgdXBkYXRlZEJ5OiBTdHJpbmcodGhpcy51c2VySWQpXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICBpZiAocmVzdWx0ID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VwZGF0ZS1mYWlsZWQnLCAnRmFpbGVkIHRvIGFkZCBsaW5rJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCBhbmQgcmV0dXJuIHRoZSB1cGRhdGVkIHRhc2tcbiAgICAgIGNvbnN0IHVwZGF0ZWRUYXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgICBjb25zb2xlLmxvZygnVGFzayBhZnRlciBhZGRpbmcgbGluazonLCB1cGRhdGVkVGFzayk7XG4gICAgICByZXR1cm4gdXBkYXRlZFRhc2s7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyBsaW5rOicsIGVycm9yKTtcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIE1ldGVvci5FcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3NlcnZlci1lcnJvcicsIGVycm9yLm1lc3NhZ2UgfHwgJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnKTtcbiAgICB9XG4gIH0sXG5cbiAgYXN5bmMgJ3Rhc2tzLnJlbW92ZUF0dGFjaG1lbnQnKHRhc2tJZCwgYXR0YWNobWVudEluZGV4KSB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWF1dGhvcml6ZWQnLCAnWW91IG11c3QgYmUgbG9nZ2VkIGluIHRvIHBlcmZvcm0gdGhpcyBhY3Rpb24nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgaWYgKCF0YXNrKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1mb3VuZCcsICdUYXNrIG5vdCBmb3VuZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB0aGUgYXR0YWNobWVudCBleGlzdHNcbiAgICAgIGlmICghdGFzay5hdHRhY2htZW50cyB8fCAhdGFzay5hdHRhY2htZW50c1thdHRhY2htZW50SW5kZXhdKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1mb3VuZCcsICdBdHRhY2htZW50IG5vdCBmb3VuZCcpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBhdHRhY2htZW50ID0gdGFzay5hdHRhY2htZW50c1thdHRhY2htZW50SW5kZXhdO1xuICAgICAgXG4gICAgICAvLyBDb252ZXJ0IGJvdGggSURzIHRvIHN0cmluZ3MgZm9yIGNvbXBhcmlzb25cbiAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBTdHJpbmcodGhpcy51c2VySWQpO1xuICAgICAgY29uc3QgdXBsb2FkZWRCeUlkID0gU3RyaW5nKGF0dGFjaG1lbnQudXBsb2FkZWRCeSk7XG4gICAgICBcbiAgICAgIC8vIE9ubHkgYWxsb3cgdGhlIHVwbG9hZGVyIHRvIHJlbW92ZSB0aGUgYXR0YWNobWVudFxuICAgICAgaWYgKGN1cnJlbnRVc2VySWQgIT09IHVwbG9hZGVkQnlJZCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgY2FuIG9ubHkgcmVtb3ZlIHlvdXIgb3duIGF0dGFjaG1lbnRzJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZSBhIG5ldyBhcnJheSB3aXRob3V0IHRoZSBzcGVjaWZpZWQgYXR0YWNobWVudFxuICAgICAgY29uc3QgdXBkYXRlZEF0dGFjaG1lbnRzID0gWy4uLnRhc2suYXR0YWNobWVudHNdO1xuICAgICAgdXBkYXRlZEF0dGFjaG1lbnRzLnNwbGljZShhdHRhY2htZW50SW5kZXgsIDEpO1xuXG4gICAgICAvLyBVcGRhdGUgdGhlIHRhc2sgd2l0aCB0aGUgbmV3IGF0dGFjaG1lbnRzIGFycmF5XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXNrcy51cGRhdGVBc3luYyhcbiAgICAgICAgeyBfaWQ6IHRhc2tJZCB9LFxuICAgICAgICB7XG4gICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgYXR0YWNobWVudHM6IHVwZGF0ZWRBdHRhY2htZW50cyxcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgIHVwZGF0ZWRCeTogY3VycmVudFVzZXJJZFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgICAgaWYgKHJlc3VsdCA9PT0gMCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd1cGRhdGUtZmFpbGVkJywgJ0ZhaWxlZCB0byByZW1vdmUgYXR0YWNobWVudCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBHZXQgYW5kIHJldHVybiB0aGUgdXBkYXRlZCB0YXNrXG4gICAgICBjb25zdCB1cGRhdGVkVGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgICAgY29uc29sZS5sb2coJ1Rhc2sgYWZ0ZXIgcmVtb3ZpbmcgYXR0YWNobWVudDonLCB1cGRhdGVkVGFzayk7XG4gICAgICByZXR1cm4gdXBkYXRlZFRhc2s7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlbW92aW5nIGF0dGFjaG1lbnQ6JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgTWV0ZW9yLkVycm9yKSB7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignc2VydmVyLWVycm9yJywgZXJyb3IubWVzc2FnZSB8fCAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcpO1xuICAgIH1cbiAgfSxcblxuICBhc3luYyAndGFza3MucmVtb3ZlTGluaycodGFza0lkLCBsaW5rSW5kZXgpIHtcbiAgICB0cnkge1xuICAgICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4gdG8gcGVyZm9ybSB0aGlzIGFjdGlvbicpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB0YXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgICBpZiAoIXRhc2spIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWZvdW5kJywgJ1Rhc2sgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGlmIHRoZSBsaW5rIGV4aXN0c1xuICAgICAgaWYgKCF0YXNrLmxpbmtzIHx8ICF0YXNrLmxpbmtzW2xpbmtJbmRleF0pIHtcbiAgICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignbm90LWZvdW5kJywgJ0xpbmsgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGxpbmsgPSB0YXNrLmxpbmtzW2xpbmtJbmRleF07XG4gICAgICBcbiAgICAgIC8vIENvbnZlcnQgYm90aCBJRHMgdG8gc3RyaW5ncyBmb3IgY29tcGFyaXNvblxuICAgICAgY29uc3QgY3VycmVudFVzZXJJZCA9IFN0cmluZyh0aGlzLnVzZXJJZCk7XG4gICAgICBjb25zdCBhZGRlZEJ5SWQgPSBTdHJpbmcobGluay5hZGRlZEJ5KTtcbiAgICAgIFxuICAgICAgLy8gT25seSBhbGxvdyB0aGUgdXNlciB3aG8gYWRkZWQgdGhlIGxpbmsgdG8gcmVtb3ZlIGl0XG4gICAgICBpZiAoY3VycmVudFVzZXJJZCAhPT0gYWRkZWRCeUlkKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1lvdSBjYW4gb25seSByZW1vdmUgeW91ciBvd24gbGlua3MnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIGEgbmV3IGFycmF5IHdpdGhvdXQgdGhlIHNwZWNpZmllZCBsaW5rXG4gICAgICBjb25zdCB1cGRhdGVkTGlua3MgPSBbLi4udGFzay5saW5rc107XG4gICAgICB1cGRhdGVkTGlua3Muc3BsaWNlKGxpbmtJbmRleCwgMSk7XG5cbiAgICAgIC8vIFVwZGF0ZSB0aGUgdGFzayB3aXRoIHRoZSBuZXcgbGlua3MgYXJyYXlcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhc2tzLnVwZGF0ZUFzeW5jKFxuICAgICAgICB7IF9pZDogdGFza0lkIH0sXG4gICAgICAgIHtcbiAgICAgICAgICAkc2V0OiB7XG4gICAgICAgICAgICBsaW5rczogdXBkYXRlZExpbmtzLFxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgdXBkYXRlZEJ5OiBjdXJyZW50VXNlcklkXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICBpZiAocmVzdWx0ID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ3VwZGF0ZS1mYWlsZWQnLCAnRmFpbGVkIHRvIHJlbW92ZSBsaW5rJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCBhbmQgcmV0dXJuIHRoZSB1cGRhdGVkIHRhc2tcbiAgICAgIGNvbnN0IHVwZGF0ZWRUYXNrID0gYXdhaXQgVGFza3MuZmluZE9uZUFzeW5jKHRhc2tJZCk7XG4gICAgICBjb25zb2xlLmxvZygnVGFzayBhZnRlciByZW1vdmluZyBsaW5rOicsIHVwZGF0ZWRUYXNrKTtcbiAgICAgIHJldHVybiB1cGRhdGVkVGFzaztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVtb3ZpbmcgbGluazonLCBlcnJvcik7XG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBNZXRlb3IuRXJyb3IpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdzZXJ2ZXItZXJyb3InLCBlcnJvci5tZXNzYWdlIHx8ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJyk7XG4gICAgfVxuICB9LFxuXG4gIGFzeW5jICd0YXNrcy5maW5kT25lJyh0YXNrSWQpIHtcbiAgICBjaGVjayh0YXNrSWQsIFN0cmluZyk7XG4gICAgXG4gICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignTm90IGF1dGhvcml6ZWQuJyk7XG4gICAgfVxuXG4gICAgY29uc3QgdGFzayA9IGF3YWl0IFRhc2tzLmZpbmRPbmVBc3luYyh0YXNrSWQpO1xuICAgIGlmICghdGFzaykge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndGFzay1ub3QtZm91bmQnLCAnVGFzayBub3QgZm91bmQnKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdGFzaztcbiAgfSxcbn0pOyAiLCJpbXBvcnQgeyBNZXRlb3IgfSBmcm9tICdtZXRlb3IvbWV0ZW9yJztcbmltcG9ydCB7IExpbmtzQ29sbGVjdGlvbiB9IGZyb20gJy9pbXBvcnRzL2FwaS9saW5rcyc7XG5pbXBvcnQgeyBBY2NvdW50cyB9IGZyb20gJ21ldGVvci9hY2NvdW50cy1iYXNlJztcbmltcG9ydCB7IEVtYWlsIH0gZnJvbSAnbWV0ZW9yL2VtYWlsJztcbmltcG9ydCB7IFRhc2tzIH0gZnJvbSAnL2ltcG9ydHMvYXBpL3Rhc2tzJztcbmltcG9ydCB7IFJvbGVzIH0gZnJvbSAnbWV0ZW9yL2FsYW5uaW5nOnJvbGVzJztcbmltcG9ydCB7IGNoZWNrIH0gZnJvbSAnbWV0ZW9yL2NoZWNrJztcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0JztcblxuYXN5bmMgZnVuY3Rpb24gaW5zZXJ0TGluayh7IHRpdGxlLCB1cmwgfSkge1xuICBhd2FpdCBMaW5rc0NvbGxlY3Rpb24uaW5zZXJ0QXN5bmMoeyB0aXRsZSwgdXJsLCBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkgfSk7XG59XG5cbmNvbnN0IEFETUlOX1RPS0VOID0gJzEyMzQ1Nic7XG5cbk1ldGVvci5zdGFydHVwKGFzeW5jICgpID0+IHtcbiAgLy8gRW5zdXJlIGluZGV4ZXMgZm9yIFRhc2tzIGNvbGxlY3Rpb25cbiAgdHJ5IHtcbiAgICBhd2FpdCBUYXNrcy5jcmVhdGVJbmRleCh7IGNyZWF0ZWRBdDogMSB9KTtcbiAgICBhd2FpdCBUYXNrcy5jcmVhdGVJbmRleCh7IGFzc2lnbmVkVG86IDEgfSk7XG4gICAgYXdhaXQgVGFza3MuY3JlYXRlSW5kZXgoeyBjcmVhdGVkQnk6IDEgfSk7XG5cbiAgICAvLyBFbnN1cmUgaW5kZXhlcyBmb3IgVXNlcnMgY29sbGVjdGlvblxuICAgIC8vIE5vdGU6IGVtYWlscy5hZGRyZXNzIGluZGV4IGlzIGFscmVhZHkgY3JlYXRlZCBieSBNZXRlb3IgYWNjb3VudHMgc3lzdGVtXG4gICAgYXdhaXQgTWV0ZW9yLnVzZXJzLmNyZWF0ZUluZGV4KHsgcm9sZXM6IDEgfSwgeyBiYWNrZ3JvdW5kOiB0cnVlIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUud2FybignW1N0YXJ0dXBdIEluZGV4IGNyZWF0aW9uIHdhcm5pbmc6JywgZXJyb3IubWVzc2FnZSk7XG4gIH1cblxuICAvLyBDaGVjayBpZiB3ZSBoYXZlIGFueSB0ZWFtIG1lbWJlcnNcbiAgdHJ5IHtcbiAgICBjb25zdCBhbGxVc2VycyA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kKCkuZmV0Y2hBc3luYygpO1xuICAgIGNvbnNvbGUubG9nKCdbU3RhcnR1cF0gQWxsIHVzZXJzOicsIGFsbFVzZXJzLm1hcCh1c2VyID0+ICh7XG4gICAgICBpZDogdXNlci5faWQsXG4gICAgICBlbWFpbDogdXNlci5lbWFpbHM/LlswXT8uYWRkcmVzcyxcbiAgICAgIHJvbGVzOiB1c2VyLnJvbGVzLFxuICAgICAgcHJvZmlsZTogdXNlci5wcm9maWxlXG4gICAgfSkpKTtcblxuICAgIC8vIEZpcnN0LCBlbnN1cmUgYWxsIHVzZXJzIGhhdmUgcm9sZXMgYW5kIGNyZWF0ZWRBdFxuICAgIGZvciAoY29uc3QgdXNlciBvZiBhbGxVc2Vycykge1xuICAgICAgY29uc3QgdXBkYXRlcyA9IHt9O1xuICAgICAgXG4gICAgICBpZiAoIXVzZXIucm9sZXMgfHwgIUFycmF5LmlzQXJyYXkodXNlci5yb2xlcykpIHtcbiAgICAgICAgdXBkYXRlcy5yb2xlcyA9IFsndGVhbS1tZW1iZXInXTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKCF1c2VyLmNyZWF0ZWRBdCkge1xuICAgICAgICB1cGRhdGVzLmNyZWF0ZWRBdCA9IG5ldyBEYXRlKCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGlmIChPYmplY3Qua2V5cyh1cGRhdGVzKS5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbU3RhcnR1cF0gRml4aW5nIG1pc3NpbmcgZmllbGRzIGZvciB1c2VyOicsIHVzZXIuZW1haWxzPy5bMF0/LmFkZHJlc3MpO1xuICAgICAgICBhd2FpdCBNZXRlb3IudXNlcnMudXBkYXRlQXN5bmModXNlci5faWQsIHtcbiAgICAgICAgICAkc2V0OiB1cGRhdGVzXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IHRlYW1NZW1iZXJzQ291bnQgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZCh7ICdyb2xlcyc6ICd0ZWFtLW1lbWJlcicgfSkuY291bnRBc3luYygpO1xuICAgIGNvbnNvbGUubG9nKCdbU3RhcnR1cF0gRm91bmQgdGVhbSBtZW1iZXJzOicsIHRlYW1NZW1iZXJzQ291bnQpO1xuXG4gICAgLy8gQ3JlYXRlIHRlc3QgdGVhbSBtZW1iZXJzIGlmIG5vbmUgZXhpc3RcbiAgICBpZiAodGVhbU1lbWJlcnNDb3VudCA9PT0gMCkge1xuICAgICAgY29uc29sZS5sb2coJ1tTdGFydHVwXSBDcmVhdGluZyB0ZXN0IHRlYW0gbWVtYmVycycpO1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gQ3JlYXRlIG11bHRpcGxlIHRlc3QgdGVhbSBtZW1iZXJzXG4gICAgICAgIGNvbnN0IHRlc3RNZW1iZXJzID0gW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIGVtYWlsOiAndGVhbTFAZXhhbXBsZS5jb20nLFxuICAgICAgICAgICAgcGFzc3dvcmQ6ICdUZWFtUGFzczEyMyEnLFxuICAgICAgICAgICAgZmlyc3ROYW1lOiAnSm9obicsXG4gICAgICAgICAgICBsYXN0TmFtZTogJ0RvZSdcbiAgICAgICAgICB9LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGVtYWlsOiAndGVhbTJAZXhhbXBsZS5jb20nLFxuICAgICAgICAgICAgcGFzc3dvcmQ6ICdUZWFtUGFzczEyMyEnLFxuICAgICAgICAgICAgZmlyc3ROYW1lOiAnSmFuZScsXG4gICAgICAgICAgICBsYXN0TmFtZTogJ1NtaXRoJ1xuICAgICAgICAgIH1cbiAgICAgICAgXTtcblxuICAgICAgICBmb3IgKGNvbnN0IG1lbWJlciBvZiB0ZXN0TWVtYmVycykge1xuICAgICAgICAgIGNvbnN0IHVzZXJJZCA9IGF3YWl0IEFjY291bnRzLmNyZWF0ZVVzZXJBc3luYyh7XG4gICAgICAgICAgICBlbWFpbDogbWVtYmVyLmVtYWlsLFxuICAgICAgICAgICAgcGFzc3dvcmQ6IG1lbWJlci5wYXNzd29yZCxcbiAgICAgICAgICAgIHJvbGU6ICd0ZWFtLW1lbWJlcicsXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICBwcm9maWxlOiB7XG4gICAgICAgICAgICAgIGZpcnN0TmFtZTogbWVtYmVyLmZpcnN0TmFtZSxcbiAgICAgICAgICAgICAgbGFzdE5hbWU6IG1lbWJlci5sYXN0TmFtZSxcbiAgICAgICAgICAgICAgcm9sZTogJ3RlYW0tbWVtYmVyJyxcbiAgICAgICAgICAgICAgZnVsbE5hbWU6IGAke21lbWJlci5maXJzdE5hbWV9ICR7bWVtYmVyLmxhc3ROYW1lfWBcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIC8vIFNldCB0aGUgcm9sZSBleHBsaWNpdGx5XG4gICAgICAgICAgYXdhaXQgTWV0ZW9yLnVzZXJzLnVwZGF0ZUFzeW5jKHVzZXJJZCwge1xuICAgICAgICAgICAgJHNldDogeyByb2xlczogWyd0ZWFtLW1lbWJlciddIH1cbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIGNvbnNvbGUubG9nKCdbU3RhcnR1cF0gQ3JlYXRlZCB0ZXN0IHRlYW0gbWVtYmVyOicsIHtcbiAgICAgICAgICAgIGlkOiB1c2VySWQsXG4gICAgICAgICAgICBlbWFpbDogbWVtYmVyLmVtYWlsLFxuICAgICAgICAgICAgbmFtZTogYCR7bWVtYmVyLmZpcnN0TmFtZX0gJHttZW1iZXIubGFzdE5hbWV9YFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbU3RhcnR1cF0gRXJyb3IgY3JlYXRpbmcgdGVzdCB0ZWFtIG1lbWJlcnM6JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdbU3RhcnR1cF0gRXJyb3IgY2hlY2tpbmcgdGVhbSBtZW1iZXJzOicsIGVycm9yKTtcbiAgfVxuXG4gIC8vIEVtYWlsIGNvbmZpZ3VyYXRpb24gZnJvbSBzZXR0aW5nc1xuICBjb25zdCBlbWFpbFNldHRpbmdzID0gTWV0ZW9yLnNldHRpbmdzLnByaXZhdGU/LmVtYWlsO1xuICBcbiAgLy8gQ29uZmlndXJlIGVtYWlsIFNNVFBcbiAgaWYgKGVtYWlsU2V0dGluZ3M/LnVzZXJuYW1lICYmIGVtYWlsU2V0dGluZ3M/LnBhc3N3b3JkKSB7XG4gICAgcHJvY2Vzcy5lbnYuTUFJTF9VUkwgPSBgc210cDovLyR7ZW5jb2RlVVJJQ29tcG9uZW50KGVtYWlsU2V0dGluZ3MudXNlcm5hbWUpfToke2VuY29kZVVSSUNvbXBvbmVudChlbWFpbFNldHRpbmdzLnBhc3N3b3JkKX1AJHtlbWFpbFNldHRpbmdzLnNlcnZlcn06JHtlbWFpbFNldHRpbmdzLnBvcnR9YDtcbiAgICBcbiAgICAvLyBUZXN0IGVtYWlsIGNvbmZpZ3VyYXRpb25cbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ1Rlc3RpbmcgZW1haWwgY29uZmlndXJhdGlvbi4uLicpO1xuICAgICAgRW1haWwuc2VuZCh7XG4gICAgICAgIHRvOiBlbWFpbFNldHRpbmdzLnVzZXJuYW1lLFxuICAgICAgICBmcm9tOiBlbWFpbFNldHRpbmdzLnVzZXJuYW1lLFxuICAgICAgICBzdWJqZWN0OiAnVGVzdCBFbWFpbCcsXG4gICAgICAgIHRleHQ6ICdJZiB5b3UgcmVjZWl2ZSB0aGlzIGVtYWlsLCB5b3VyIGVtYWlsIGNvbmZpZ3VyYXRpb24gaXMgd29ya2luZyBjb3JyZWN0bHkuJ1xuICAgICAgfSk7XG4gICAgICBjb25zb2xlLmxvZygnVGVzdCBlbWFpbCBzZW50IHN1Y2Nlc3NmdWxseSEnKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyB0ZXN0IGVtYWlsOicsIGVycm9yKTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgY29uc29sZS53YXJuKCdFbWFpbCBjb25maWd1cmF0aW9uIGlzIG1pc3NpbmcgaW4gc2V0dGluZ3MuanNvbicpO1xuICB9XG5cbiAgLy8gQ29uZmlndXJlIGFjY291bnQgY3JlYXRpb24gdG8gcmVxdWlyZSBlbWFpbCB2ZXJpZmljYXRpb25cbiAgQWNjb3VudHMuY29uZmlnKHtcbiAgICBzZW5kVmVyaWZpY2F0aW9uRW1haWw6IHRydWUsXG4gICAgZm9yYmlkQ2xpZW50QWNjb3VudENyZWF0aW9uOiBmYWxzZVxuICB9KTtcblxuICAvLyBDdXN0b21pemUgdmVyaWZpY2F0aW9uIGVtYWlsXG4gIEFjY291bnRzLmVtYWlsVGVtcGxhdGVzLnNpdGVOYW1lID0gXCJUYXNrIE1hbmFnZW1lbnQgU3lzdGVtXCI7XG4gIEFjY291bnRzLmVtYWlsVGVtcGxhdGVzLmZyb20gPSBlbWFpbFNldHRpbmdzPy51c2VybmFtZSA/IFxuICAgIGBUYXNrIE1hbmFnZW1lbnQgU3lzdGVtIDwke2VtYWlsU2V0dGluZ3MudXNlcm5hbWV9PmAgOiBcbiAgICBcIlRhc2sgTWFuYWdlbWVudCBTeXN0ZW0gPG5vcmVwbHlAZXhhbXBsZS5jb20+XCI7XG5cbiAgQWNjb3VudHMuZW1haWxUZW1wbGF0ZXMudmVyaWZ5RW1haWwgPSB7XG4gICAgc3ViamVjdCgpIHtcbiAgICAgIHJldHVybiBcIlZlcmlmeSBZb3VyIEVtYWlsIEFkZHJlc3NcIjtcbiAgICB9LFxuICAgIHRleHQodXNlciwgdXJsKSB7XG4gICAgICBjb25zdCBlbWFpbEFkZHJlc3MgPSB1c2VyLmVtYWlsc1swXS5hZGRyZXNzO1xuICAgICAgcmV0dXJuIGBIZWxsbyxcXG5cXG5gXG4gICAgICAgICsgYFRvIHZlcmlmeSB5b3VyIGVtYWlsIGFkZHJlc3MgKCR7ZW1haWxBZGRyZXNzfSksIHBsZWFzZSBjbGljayB0aGUgbGluayBiZWxvdzpcXG5cXG5gXG4gICAgICAgICsgYCR7dXJsfVxcblxcbmBcbiAgICAgICAgKyBgSWYgeW91IGRpZCBub3QgcmVxdWVzdCB0aGlzIHZlcmlmaWNhdGlvbiwgcGxlYXNlIGlnbm9yZSB0aGlzIGVtYWlsLlxcblxcbmBcbiAgICAgICAgKyBgVGhhbmtzLFxcbmBcbiAgICAgICAgKyBgWW91ciBUYXNrIE1hbmFnZW1lbnQgU3lzdGVtIFRlYW1gO1xuICAgIH0sXG4gICAgaHRtbCh1c2VyLCB1cmwpIHtcbiAgICAgIGNvbnN0IGVtYWlsQWRkcmVzcyA9IHVzZXIuZW1haWxzWzBdLmFkZHJlc3M7XG4gICAgICByZXR1cm4gYFxuICAgICAgICA8aHRtbD5cbiAgICAgICAgICA8Ym9keSBzdHlsZT1cImZvbnQtZmFtaWx5OiBBcmlhbCwgc2Fucy1zZXJpZjsgcGFkZGluZzogMjBweDsgY29sb3I6ICMzMzM7XCI+XG4gICAgICAgICAgICA8aDIgc3R5bGU9XCJjb2xvcjogIzAwODc1YTtcIj5WZXJpZnkgWW91ciBFbWFpbCBBZGRyZXNzPC9oMj5cbiAgICAgICAgICAgIDxwPkhlbGxvLDwvcD5cbiAgICAgICAgICAgIDxwPlRvIHZlcmlmeSB5b3VyIGVtYWlsIGFkZHJlc3MgKCR7ZW1haWxBZGRyZXNzfSksIHBsZWFzZSBjbGljayB0aGUgYnV0dG9uIGJlbG93OjwvcD5cbiAgICAgICAgICAgIDxwIHN0eWxlPVwibWFyZ2luOiAyMHB4IDA7XCI+XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIke3VybH1cIiBcbiAgICAgICAgICAgICAgICAgc3R5bGU9XCJiYWNrZ3JvdW5kLWNvbG9yOiAjMDA4NzVhOyBcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTsgXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAxMnB4IDI1cHg7IFxuICAgICAgICAgICAgICAgICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOyBcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcIj5cbiAgICAgICAgICAgICAgICBWZXJpZnkgRW1haWwgQWRkcmVzc1xuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8cD5JZiB5b3UgZGlkIG5vdCByZXF1ZXN0IHRoaXMgdmVyaWZpY2F0aW9uLCBwbGVhc2UgaWdub3JlIHRoaXMgZW1haWwuPC9wPlxuICAgICAgICAgICAgPHA+VGhhbmtzLDxicj5Zb3VyIFRhc2sgTWFuYWdlbWVudCBTeXN0ZW0gVGVhbTwvcD5cbiAgICAgICAgICA8L2JvZHk+XG4gICAgICAgIDwvaHRtbD5cbiAgICAgIGA7XG4gICAgfVxuICB9O1xuXG4gIC8vIElmIHRoZSBMaW5rcyBjb2xsZWN0aW9uIGlzIGVtcHR5LCBhZGQgc29tZSBkYXRhLlxuICBpZiAoYXdhaXQgTGlua3NDb2xsZWN0aW9uLmZpbmQoKS5jb3VudEFzeW5jKCkgPT09IDApIHtcbiAgICBhd2FpdCBpbnNlcnRMaW5rKHtcbiAgICAgIHRpdGxlOiAnRG8gdGhlIFR1dG9yaWFsJyxcbiAgICAgIHVybDogJ2h0dHBzOi8vd3d3Lm1ldGVvci5jb20vdHV0b3JpYWxzL3JlYWN0L2NyZWF0aW5nLWFuLWFwcCcsXG4gICAgfSk7XG5cbiAgICBhd2FpdCBpbnNlcnRMaW5rKHtcbiAgICAgIHRpdGxlOiAnRm9sbG93IHRoZSBHdWlkZScsXG4gICAgICB1cmw6ICdodHRwczovL2d1aWRlLm1ldGVvci5jb20nLFxuICAgIH0pO1xuXG4gICAgYXdhaXQgaW5zZXJ0TGluayh7XG4gICAgICB0aXRsZTogJ1JlYWQgdGhlIERvY3MnLFxuICAgICAgdXJsOiAnaHR0cHM6Ly9kb2NzLm1ldGVvci5jb20nLFxuICAgIH0pO1xuXG4gICAgYXdhaXQgaW5zZXJ0TGluayh7XG4gICAgICB0aXRsZTogJ0Rpc2N1c3Npb25zJyxcbiAgICAgIHVybDogJ2h0dHBzOi8vZm9ydW1zLm1ldGVvci5jb20nLFxuICAgIH0pO1xuICB9XG5cbiAgLy8gV2UgcHVibGlzaCB0aGUgZW50aXJlIExpbmtzIGNvbGxlY3Rpb24gdG8gYWxsIGNsaWVudHMuXG4gIC8vIEluIG9yZGVyIHRvIGJlIGZldGNoZWQgaW4gcmVhbC10aW1lIHRvIHRoZSBjbGllbnRzXG4gIE1ldGVvci5wdWJsaXNoKFwibGlua3NcIiwgZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBMaW5rc0NvbGxlY3Rpb24uZmluZCgpO1xuICB9KTtcblxuICAvLyBBZGQgY3VzdG9tIGZpZWxkcyB0byB1c2Vyc1xuICBBY2NvdW50cy5vbkNyZWF0ZVVzZXIoKG9wdGlvbnMsIHVzZXIpID0+IHtcbiAgICBjb25zb2xlLmxvZygnW29uQ3JlYXRlVXNlcl0gQ3JlYXRpbmcgdXNlciB3aXRoIG9wdGlvbnM6Jywge1xuICAgICAgZW1haWw6IG9wdGlvbnMuZW1haWwsXG4gICAgICByb2xlOiBvcHRpb25zLnJvbGUsXG4gICAgICBwcm9maWxlOiBvcHRpb25zLnByb2ZpbGUsXG4gICAgICBjcmVhdGVkQXQ6IG9wdGlvbnMuY3JlYXRlZEF0XG4gICAgfSk7XG5cbiAgICBjb25zdCBjdXN0b21pemVkVXNlciA9IHsgLi4udXNlciB9O1xuICAgIFxuICAgIC8vIEVuc3VyZSB3ZSBoYXZlIGEgcHJvZmlsZVxuICAgIGN1c3RvbWl6ZWRVc2VyLnByb2ZpbGUgPSBvcHRpb25zLnByb2ZpbGUgfHwge307XG4gICAgXG4gICAgLy8gQWRkIHJvbGUgZnJvbSBvcHRpb25zXG4gICAgY29uc3Qgcm9sZSA9IG9wdGlvbnMucm9sZSB8fCAndGVhbS1tZW1iZXInO1xuICAgIGN1c3RvbWl6ZWRVc2VyLnJvbGVzID0gW3JvbGVdO1xuICAgIFxuICAgIC8vIFNldCBjcmVhdGVkQXQgaWYgcHJvdmlkZWQsIG90aGVyd2lzZSB1c2UgY3VycmVudCBkYXRlXG4gICAgY3VzdG9taXplZFVzZXIuY3JlYXRlZEF0ID0gb3B0aW9ucy5jcmVhdGVkQXQgfHwgbmV3IERhdGUoKTtcbiAgICBcbiAgICBjb25zb2xlLmxvZygnW29uQ3JlYXRlVXNlcl0gQ3JlYXRlZCB1c2VyOicsIHtcbiAgICAgIGlkOiBjdXN0b21pemVkVXNlci5faWQsXG4gICAgICBlbWFpbDogY3VzdG9taXplZFVzZXIuZW1haWxzPy5bMF0/LmFkZHJlc3MsXG4gICAgICByb2xlczogY3VzdG9taXplZFVzZXIucm9sZXMsXG4gICAgICBwcm9maWxlOiBjdXN0b21pemVkVXNlci5wcm9maWxlLFxuICAgICAgY3JlYXRlZEF0OiBjdXN0b21pemVkVXNlci5jcmVhdGVkQXRcbiAgICB9KTtcbiAgICBcbiAgICByZXR1cm4gY3VzdG9taXplZFVzZXI7XG4gIH0pO1xuXG4gIC8vIFB1Ymxpc2ggdGVhbSBtZW1iZXJzXG4gIE1ldGVvci5wdWJsaXNoKCd0ZWFtTWVtYmVycycsIGZ1bmN0aW9uKCkge1xuICAgIGNvbnNvbGUubG9nKCdbdGVhbU1lbWJlcnNdIFB1YmxpY2F0aW9uIGNhbGxlZCwgdXNlcklkOicsIHRoaXMudXNlcklkKTtcbiAgICBcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICBjb25zb2xlLmxvZygnW3RlYW1NZW1iZXJzXSBObyB1c2VySWQsIHJldHVybmluZyByZWFkeScpO1xuICAgICAgcmV0dXJuIHRoaXMucmVhZHkoKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gU2ltcGxlIHF1ZXJ5IHRvIGZpbmQgYWxsIHRlYW0gbWVtYmVyc1xuICAgICAgY29uc3QgdGVhbU1lbWJlcnMgPSBNZXRlb3IudXNlcnMuZmluZChcbiAgICAgICAgeyBcbiAgICAgICAgICAkb3I6IFtcbiAgICAgICAgICAgIHsgJ3JvbGVzJzogJ3RlYW0tbWVtYmVyJyB9LFxuICAgICAgICAgICAgeyAncHJvZmlsZS5yb2xlJzogJ3RlYW0tbWVtYmVyJyB9XG4gICAgICAgICAgXVxuICAgICAgICB9LFxuICAgICAgICB7IFxuICAgICAgICAgIGZpZWxkczogeyBcbiAgICAgICAgICAgIGVtYWlsczogMSwgXG4gICAgICAgICAgICByb2xlczogMSxcbiAgICAgICAgICAgICdwcm9maWxlLmZpcnN0TmFtZSc6IDEsXG4gICAgICAgICAgICAncHJvZmlsZS5sYXN0TmFtZSc6IDEsXG4gICAgICAgICAgICAncHJvZmlsZS5mdWxsTmFtZSc6IDEsXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IDFcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCdbdGVhbU1lbWJlcnNdIFB1Ymxpc2hpbmcgdGVhbSBtZW1iZXJzJyk7XG4gICAgICByZXR1cm4gdGVhbU1lbWJlcnM7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1t0ZWFtTWVtYmVyc10gRXJyb3IgaW4gcHVibGljYXRpb246JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHRoaXMucmVhZHkoKTtcbiAgICB9XG4gIH0pO1xuXG4gIC8vIFB1Ymxpc2ggdXNlciBkYXRhIHdpdGggcm9sZXNcbiAgTWV0ZW9yLnB1Ymxpc2goJ3VzZXJEYXRhJywgZnVuY3Rpb24oKSB7XG4gICAgaWYgKCF0aGlzLnVzZXJJZCkge1xuICAgICAgcmV0dXJuIHRoaXMucmVhZHkoKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnW3VzZXJEYXRhXSBQdWJsaXNoaW5nIGRhdGEgZm9yIHVzZXI6JywgdGhpcy51c2VySWQpO1xuICAgIFxuICAgIHJldHVybiBNZXRlb3IudXNlcnMuZmluZChcbiAgICAgIHsgX2lkOiB0aGlzLnVzZXJJZCB9LFxuICAgICAgeyBcbiAgICAgICAgZmllbGRzOiB7IFxuICAgICAgICAgIHJvbGVzOiAxLCBcbiAgICAgICAgICBlbWFpbHM6IDEsXG4gICAgICAgICAgcHJvZmlsZTogMVxuICAgICAgICB9IFxuICAgICAgfVxuICAgICk7XG4gIH0pO1xufSk7XG5cbi8vIE1ldGhvZCB0byBjcmVhdGUgYSBuZXcgdXNlciB3aXRoIHJvbGVcbk1ldGVvci5tZXRob2RzKHtcbiAgJ3VzZXJzLmNyZWF0ZScoeyBlbWFpbCwgcGFzc3dvcmQsIHJvbGUsIGFkbWluVG9rZW4sIGZpcnN0TmFtZSwgbGFzdE5hbWUgfSkge1xuICAgIC8vIFZhbGlkYXRlIGFkbWluIHRva2VuIGlmIHRyeWluZyB0byBjcmVhdGUgYWRtaW4gYWNjb3VudFxuICAgIGlmIChyb2xlID09PSAnYWRtaW4nICYmIGFkbWluVG9rZW4gIT09IEFETUlOX1RPS0VOKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdpbnZhbGlkLWFkbWluLXRva2VuJywgJ0ludmFsaWQgYWRtaW4gdG9rZW4gcHJvdmlkZWQnKTtcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBwYXNzd29yZCByZXF1aXJlbWVudHNcbiAgICBjb25zdCBwYXNzd29yZFJlZ2V4ID0ge1xuICAgICAgbGVuZ3RoOiAvLns4LH0vLFxuICAgICAgdXBwZXJjYXNlOiAvW0EtWl0vLFxuICAgICAgbnVtYmVyOiAvWzAtOV0vLFxuICAgICAgc3BlY2lhbDogL1shQCMkJV4mKl0vXG4gICAgfTtcblxuICAgIGNvbnN0IHBhc3N3b3JkRXJyb3JzID0gW107XG4gICAgaWYgKCFwYXNzd29yZFJlZ2V4Lmxlbmd0aC50ZXN0KHBhc3N3b3JkKSkge1xuICAgICAgcGFzc3dvcmRFcnJvcnMucHVzaCgnUGFzc3dvcmQgbXVzdCBiZSBhdCBsZWFzdCA4IGNoYXJhY3RlcnMgbG9uZycpO1xuICAgIH1cbiAgICBpZiAoIXBhc3N3b3JkUmVnZXgudXBwZXJjYXNlLnRlc3QocGFzc3dvcmQpKSB7XG4gICAgICBwYXNzd29yZEVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIHVwcGVyY2FzZSBsZXR0ZXInKTtcbiAgICB9XG4gICAgaWYgKCFwYXNzd29yZFJlZ2V4Lm51bWJlci50ZXN0KHBhc3N3b3JkKSkge1xuICAgICAgcGFzc3dvcmRFcnJvcnMucHVzaCgnUGFzc3dvcmQgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSBudW1iZXInKTtcbiAgICB9XG4gICAgaWYgKCFwYXNzd29yZFJlZ2V4LnNwZWNpYWwudGVzdChwYXNzd29yZCkpIHtcbiAgICAgIHBhc3N3b3JkRXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgc3BlY2lhbCBjaGFyYWN0ZXIgKCFAIyQlXiYqKScpO1xuICAgIH1cblxuICAgIGlmIChwYXNzd29yZEVycm9ycy5sZW5ndGggPiAwKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdpbnZhbGlkLXBhc3N3b3JkJywgcGFzc3dvcmRFcnJvcnMuam9pbignLCAnKSk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHRoZSB1c2VyXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJJZCA9IEFjY291bnRzLmNyZWF0ZVVzZXIoe1xuICAgICAgICBlbWFpbCxcbiAgICAgICAgcGFzc3dvcmQsXG4gICAgICAgIHJvbGUsIC8vIFRoaXMgd2lsbCBiZSB1c2VkIGluIG9uQ3JlYXRlVXNlciBjYWxsYmFja1xuICAgICAgICBwcm9maWxlOiB7XG4gICAgICAgICAgcm9sZSwgLy8gU3RvcmUgaW4gcHJvZmlsZSBhcyB3ZWxsIGZvciBlYXN5IGFjY2Vzc1xuICAgICAgICAgIGZpcnN0TmFtZSxcbiAgICAgICAgICBsYXN0TmFtZSxcbiAgICAgICAgICBmdWxsTmFtZTogYCR7Zmlyc3ROYW1lfSAke2xhc3ROYW1lfWBcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIC8vIFNlbmQgdmVyaWZpY2F0aW9uIGVtYWlsXG4gICAgICBpZiAodXNlcklkKSB7XG4gICAgICAgIEFjY291bnRzLnNlbmRWZXJpZmljYXRpb25FbWFpbCh1c2VySWQpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdXNlcklkO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdjcmVhdGUtdXNlci1mYWlsZWQnLCBlcnJvci5tZXNzYWdlKTtcbiAgICB9XG4gIH0sXG5cbiAgYXN5bmMgJ3VzZXJzLmdldFJvbGUnKCkge1xuICAgIGlmICghdGhpcy51c2VySWQpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ1VzZXIgbXVzdCBiZSBsb2dnZWQgaW4nKTtcbiAgICB9XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd1c2VyLW5vdC1mb3VuZCcsICdVc2VyIG5vdCBmb3VuZCcpO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBDaGVjayBib3RoIHJvbGVzIGFycmF5IGFuZCBwcm9maWxlIGZvciByb2xlXG4gICAgICBjb25zdCByb2xlID0gdXNlci5yb2xlcz8uWzBdIHx8IHVzZXIucHJvZmlsZT8ucm9sZSB8fCAndGVhbS1tZW1iZXInO1xuICAgICAgcmV0dXJuIHJvbGU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2dldC1yb2xlLWZhaWxlZCcsIGVycm9yLm1lc3NhZ2UpO1xuICAgIH1cbiAgfSxcblxuICAndXNlcnMucmVzZW5kVmVyaWZpY2F0aW9uRW1haWwnKGVtYWlsKSB7XG4gICAgLy8gRmluZCB1c2VyIGJ5IGVtYWlsXG4gICAgY29uc3QgdXNlciA9IEFjY291bnRzLmZpbmRVc2VyQnlFbWFpbChlbWFpbCk7XG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCd1c2VyLW5vdC1mb3VuZCcsICdObyB1c2VyIGZvdW5kIHdpdGggdGhpcyBlbWFpbCBhZGRyZXNzJyk7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgZW1haWwgaXMgYWxyZWFkeSB2ZXJpZmllZFxuICAgIGNvbnN0IHVzZXJFbWFpbCA9IHVzZXIuZW1haWxzWzBdO1xuICAgIGlmICh1c2VyRW1haWwudmVyaWZpZWQpIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2FscmVhZHktdmVyaWZpZWQnLCAnVGhpcyBlbWFpbCBpcyBhbHJlYWR5IHZlcmlmaWVkJyk7XG4gICAgfVxuXG4gICAgLy8gU2VuZCB2ZXJpZmljYXRpb24gZW1haWxcbiAgICB0cnkge1xuICAgICAgQWNjb3VudHMuc2VuZFZlcmlmaWNhdGlvbkVtYWlsKHVzZXIuX2lkLCBlbWFpbCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndmVyaWZpY2F0aW9uLWVtYWlsLWZhaWxlZCcsIGVycm9yLm1lc3NhZ2UpO1xuICAgIH1cbiAgfSxcblxuICBhc3luYyAndXNlcnMuZm9yZ290UGFzc3dvcmQnKGRhdGEpIHtcbiAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBNZXRob2QgY2FsbGVkIHdpdGggZGF0YTonLCBkYXRhKTtcblxuICAgIGNoZWNrKGRhdGEsIHtcbiAgICAgIGVtYWlsOiBTdHJpbmcsXG4gICAgICBuZXdQYXNzd29yZDogU3RyaW5nXG4gICAgfSk7XG5cbiAgICBjb25zdCB7IGVtYWlsLCBuZXdQYXNzd29yZCB9ID0gZGF0YTtcbiAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBQcm9jZXNzaW5nIHJlcXVlc3QgZm9yIGVtYWlsOicsIGVtYWlsKTtcblxuICAgIC8vIEZpbmQgdXNlciBieSBlbWFpbFxuICAgIGNvbnN0IHVzZXIgPSBBY2NvdW50cy5maW5kVXNlckJ5RW1haWwoZW1haWwpO1xuICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFVzZXIgZm91bmQ6JywgdXNlciA/IHsgaWQ6IHVzZXIuX2lkLCBlbWFpbDogdXNlci5lbWFpbHM/LlswXT8uYWRkcmVzcyB9IDogJ05vIHVzZXIgZm91bmQnKTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigndXNlci1ub3QtZm91bmQnLCAnTm8gdXNlciBmb3VuZCB3aXRoIHRoaXMgZW1haWwgYWRkcmVzcycpO1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIHBhc3N3b3JkIHJlcXVpcmVtZW50cyAoc2FtZSBhcyBzaWdudXApXG4gICAgY29uc3QgcGFzc3dvcmRSZWdleCA9IHtcbiAgICAgIGxlbmd0aDogLy57OCx9LyxcbiAgICAgIHVwcGVyY2FzZTogL1tBLVpdLyxcbiAgICAgIG51bWJlcjogL1swLTldLyxcbiAgICAgIHNwZWNpYWw6IC9bIUAjJCVeJipdL1xuICAgIH07XG5cbiAgICBjb25zdCBwYXNzd29yZEVycm9ycyA9IFtdO1xuICAgIGlmICghcGFzc3dvcmRSZWdleC5sZW5ndGgudGVzdChuZXdQYXNzd29yZCkpIHtcbiAgICAgIHBhc3N3b3JkRXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgYmUgYXQgbGVhc3QgOCBjaGFyYWN0ZXJzIGxvbmcnKTtcbiAgICB9XG4gICAgaWYgKCFwYXNzd29yZFJlZ2V4LnVwcGVyY2FzZS50ZXN0KG5ld1Bhc3N3b3JkKSkge1xuICAgICAgcGFzc3dvcmRFcnJvcnMucHVzaCgnUGFzc3dvcmQgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSB1cHBlcmNhc2UgbGV0dGVyJyk7XG4gICAgfVxuICAgIGlmICghcGFzc3dvcmRSZWdleC5udW1iZXIudGVzdChuZXdQYXNzd29yZCkpIHtcbiAgICAgIHBhc3N3b3JkRXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgbnVtYmVyJyk7XG4gICAgfVxuICAgIGlmICghcGFzc3dvcmRSZWdleC5zcGVjaWFsLnRlc3QobmV3UGFzc3dvcmQpKSB7XG4gICAgICBwYXNzd29yZEVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIHNwZWNpYWwgY2hhcmFjdGVyICghQCMkJV4mKiknKTtcbiAgICB9XG5cbiAgICBpZiAocGFzc3dvcmRFcnJvcnMubGVuZ3RoID4gMCkge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignaW52YWxpZC1wYXNzd29yZCcsIHBhc3N3b3JkRXJyb3JzLmpvaW4oJywgJykpO1xuICAgIH1cblxuICAgIC8vIFVwZGF0ZSB0aGUgdXNlcidzIHBhc3N3b3JkIHVzaW5nIHVzZXIgSURcbiAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBBdHRlbXB0aW5nIHRvIHVwZGF0ZSBwYXNzd29yZCBmb3IgdXNlciBJRDonLCB1c2VyLl9pZCk7XG4gICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gQ3VycmVudCB1c2VyIGRvY3VtZW50IHN0cnVjdHVyZTonLCBKU09OLnN0cmluZ2lmeSh1c2VyLCBudWxsLCAyKSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gSGFzaCB0aGUgcGFzc3dvcmQgdXNpbmcgYmNyeXB0IChzYW1lIGFzIE1ldGVvciB1c2VzIGludGVybmFsbHkpXG4gICAgICBjb25zdCBzYWx0Um91bmRzID0gMTA7XG4gICAgICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGJjcnlwdC5oYXNoU3luYyhuZXdQYXNzd29yZCwgc2FsdFJvdW5kcyk7XG4gICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBHZW5lcmF0ZWQgcGFzc3dvcmQgaGFzaDonLCBoYXNoZWRQYXNzd29yZCk7XG5cbiAgICAgIC8vIFRyeSBtdWx0aXBsZSBmaWVsZCBwYXRocyB0byBlbnN1cmUgY29tcGF0aWJpbGl0eSB3aXRoIGRpZmZlcmVudCBNZXRlb3IgdmVyc2lvbnNcbiAgICAgIGNvbnN0IHVwZGF0ZU9wZXJhdGlvbnMgPSBbXG4gICAgICAgIC8vIE1vZGVybiBNZXRlb3Igc3RydWN0dXJlXG4gICAgICAgIHsgJ3NlcnZpY2VzLnBhc3N3b3JkLmJjcnlwdCc6IGhhc2hlZFBhc3N3b3JkIH0sXG4gICAgICAgIC8vIEFsdGVybmF0aXZlIHN0cnVjdHVyZVxuICAgICAgICB7ICdzZXJ2aWNlcy5wYXNzd29yZCc6IHsgYmNyeXB0OiBoYXNoZWRQYXNzd29yZCB9IH0sXG4gICAgICAgIC8vIEZhbGxiYWNrIHN0cnVjdHVyZVxuICAgICAgICB7ICdzZXJ2aWNlcy5wYXNzd29yZC5oYXNoJzogaGFzaGVkUGFzc3dvcmQgfVxuICAgICAgXTtcblxuICAgICAgbGV0IHVwZGF0ZVJlc3VsdCA9IDA7XG4gICAgICBsZXQgbGFzdEVycm9yID0gbnVsbDtcblxuICAgICAgZm9yIChjb25zdCB1cGRhdGVPcCBvZiB1cGRhdGVPcGVyYXRpb25zKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1tmb3Jnb3RQYXNzd29yZF0gVHJ5aW5nIHVwZGF0ZSBvcGVyYXRpb246JywgdXBkYXRlT3ApO1xuICAgICAgICAgIHVwZGF0ZVJlc3VsdCA9IGF3YWl0IE1ldGVvci51c2Vycy51cGRhdGVBc3luYyh1c2VyLl9pZCwge1xuICAgICAgICAgICAgJHNldDogdXBkYXRlT3BcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIGlmICh1cGRhdGVSZXN1bHQgPT09IDEpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFVwZGF0ZSBzdWNjZXNzZnVsIHdpdGggb3BlcmF0aW9uOicsIHVwZGF0ZU9wKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAob3BFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdbZm9yZ290UGFzc3dvcmRdIFVwZGF0ZSBvcGVyYXRpb24gZmFpbGVkOicsIG9wRXJyb3IubWVzc2FnZSk7XG4gICAgICAgICAgbGFzdEVycm9yID0gb3BFcnJvcjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAodXBkYXRlUmVzdWx0ID09PSAxKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBbZm9yZ290UGFzc3dvcmRdIFBhc3N3b3JkIHJlc2V0IHN1Y2Nlc3NmdWwgZm9yIHVzZXIgSUQ6ICR7dXNlci5faWR9LCBlbWFpbDogJHtlbWFpbH1gKTtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogJ1Bhc3N3b3JkIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyB9O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gSWYgYWxsIHVwZGF0ZSBvcGVyYXRpb25zIGZhaWxlZCwgdHJ5IHRvIGNoZWNrIHRoZSB1c2VyIGRvY3VtZW50IGFnYWluXG4gICAgICAgIGNvbnN0IHVzZXJBZnRlclVwZGF0ZSA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kT25lQXN5bmModXNlci5faWQpO1xuICAgICAgICBjb25zb2xlLmxvZygnW2ZvcmdvdFBhc3N3b3JkXSBVc2VyIGRvY3VtZW50IGFmdGVyIHVwZGF0ZSBhdHRlbXB0czonLCBKU09OLnN0cmluZ2lmeSh1c2VyQWZ0ZXJVcGRhdGUsIG51bGwsIDIpKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVc2VyIHVwZGF0ZSBmYWlsZWQgLSBubyBkb2N1bWVudHMgbW9kaWZpZWQuIExhc3QgZXJyb3I6ICR7bGFzdEVycm9yPy5tZXNzYWdlIHx8ICdVbmtub3duJ31gKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW2ZvcmdvdFBhc3N3b3JkXSBFcnJvciB1cGRhdGluZyBwYXNzd29yZCBmb3IgdXNlcjonLCB1c2VyLl9pZCwgJ0Vycm9yIGRldGFpbHM6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcigncGFzc3dvcmQtdXBkYXRlLWZhaWxlZCcsIGBGYWlsZWQgdG8gdXBkYXRlIHBhc3N3b3JkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuICB9LFxuXG4gIC8vIFNpbXBsZSB0ZXN0IG1ldGhvZCB0byB2ZXJpZnkgbWV0aG9kIGNhbGxzIGFyZSB3b3JraW5nXG4gICd1c2Vycy50ZXN0TWV0aG9kJygpIHtcbiAgICBjb25zb2xlLmxvZygnW3Rlc3RNZXRob2RdIE1ldGhvZCBjYWxsZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogJ1Rlc3QgbWV0aG9kIHdvcmtpbmcnIH07XG4gIH0sXG5cbiAgYXN5bmMgJ3VzZXJzLmNoZWNrQW5kRml4QWRtaW5Sb2xlJygpIHtcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4nKTtcbiAgICB9XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICAgIGNvbnNvbGUubG9nKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIENoZWNraW5nIHVzZXI6Jywge1xuICAgICAgICBpZDogdXNlcj8uX2lkLFxuICAgICAgICBlbWFpbDogdXNlcj8uZW1haWxzPy5bMF0/LmFkZHJlc3MsXG4gICAgICAgIHJvbGVzOiB1c2VyPy5yb2xlc1xuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIElmIHVzZXIgaGFzIG5vIHJvbGVzIGFycmF5LCBpbml0aWFsaXplIGl0XG4gICAgICBpZiAoIXVzZXIucm9sZXMpIHtcbiAgICAgICAgYXdhaXQgTWV0ZW9yLnVzZXJzLnVwZGF0ZUFzeW5jKHRoaXMudXNlcklkLCB7XG4gICAgICAgICAgJHNldDogeyByb2xlczogWyd0ZWFtLW1lbWJlciddIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiAnUm9sZXMgaW5pdGlhbGl6ZWQnO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBJZiB1c2VyIGhhcyBubyByb2xlcyBvciBkb2Vzbid0IGhhdmUgYWRtaW4gcm9sZVxuICAgICAgaWYgKCF1c2VyLnJvbGVzLmluY2x1ZGVzKCdhZG1pbicpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIFVzZXIgaXMgbm90IGFkbWluLCBjaGVja2luZyBpZiBmaXJzdCB1c2VyJyk7XG4gICAgICAgIFxuICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIHRoZSBmaXJzdCB1c2VyICh0aGV5IHNob3VsZCBiZSBhZG1pbilcbiAgICAgICAgY29uc3QgdG90YWxVc2VycyA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kKCkuY291bnRBc3luYygpO1xuICAgICAgICBpZiAodG90YWxVc2VycyA9PT0gMSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIEZpcnN0IHVzZXIsIHNldHRpbmcgYXMgYWRtaW4nKTtcbiAgICAgICAgICBhd2FpdCBNZXRlb3IudXNlcnMudXBkYXRlQXN5bmModGhpcy51c2VySWQsIHtcbiAgICAgICAgICAgICRzZXQ6IHsgcm9sZXM6IFsnYWRtaW4nXSB9XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuICdBZG1pbiByb2xlIGFkZGVkJztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gJ1VzZXIgaXMgbm90IGFkbWluJztcbiAgICAgIH1cbiAgICAgIFxuICAgICAgcmV0dXJuICdVc2VyIGlzIGFscmVhZHkgYWRtaW4nO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbY2hlY2tBbmRGaXhBZG1pblJvbGVdIEVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ2NoZWNrLXJvbGUtZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gIGFzeW5jICd1c2Vycy5kaWFnbm9zZVJvbGVzJygpIHtcbiAgICBpZiAoIXRoaXMudXNlcklkKSB7XG4gICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdub3QtYXV0aG9yaXplZCcsICdZb3UgbXVzdCBiZSBsb2dnZWQgaW4nKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY3VycmVudFVzZXIgPSBhd2FpdCBNZXRlb3IudXNlcnMuZmluZE9uZUFzeW5jKHRoaXMudXNlcklkKTtcbiAgICAgIGlmICghY3VycmVudFVzZXIucm9sZXM/LmluY2x1ZGVzKCdhZG1pbicpKSB7XG4gICAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1hdXRob3JpemVkJywgJ09ubHkgYWRtaW5zIGNhbiBkaWFnbm9zZSByb2xlcycpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBhbGxVc2VycyA9IGF3YWl0IE1ldGVvci51c2Vycy5maW5kKCkuZmV0Y2hBc3luYygpO1xuICAgICAgY29uc3QgdXNlcnNXaXRoSXNzdWVzID0gW107XG4gICAgICBjb25zdCBmaXhlcyA9IFtdO1xuXG4gICAgICBmb3IgKGNvbnN0IHVzZXIgb2YgYWxsVXNlcnMpIHtcbiAgICAgICAgY29uc3QgaXNzdWVzID0gW107XG4gICAgICAgIFxuICAgICAgICAvLyBDaGVjayBpZiByb2xlcyBhcnJheSBleGlzdHNcbiAgICAgICAgaWYgKCF1c2VyLnJvbGVzIHx8ICFBcnJheS5pc0FycmF5KHVzZXIucm9sZXMpKSB7XG4gICAgICAgICAgaXNzdWVzLnB1c2goJ05vIHJvbGVzIGFycmF5Jyk7XG4gICAgICAgICAgLy8gRml4OiBJbml0aWFsaXplIHJvbGVzIGJhc2VkIG9uIHByb2ZpbGVcbiAgICAgICAgICBjb25zdCByb2xlID0gdXNlci5wcm9maWxlPy5yb2xlIHx8ICd0ZWFtLW1lbWJlcic7XG4gICAgICAgICAgYXdhaXQgTWV0ZW9yLnVzZXJzLnVwZGF0ZUFzeW5jKHVzZXIuX2lkLCB7XG4gICAgICAgICAgICAkc2V0OiB7IHJvbGVzOiBbcm9sZV0gfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGZpeGVzLnB1c2goYEluaXRpYWxpemVkIHJvbGVzIGZvciAke3VzZXIuZW1haWxzPy5bMF0/LmFkZHJlc3N9YCk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8vIENoZWNrIGlmIHJvbGUgbWF0Y2hlcyBwcm9maWxlXG4gICAgICAgIGlmICh1c2VyLnByb2ZpbGU/LnJvbGUgJiYgdXNlci5yb2xlcz8uWzBdICE9PSB1c2VyLnByb2ZpbGUucm9sZSkge1xuICAgICAgICAgIGlzc3Vlcy5wdXNoKCdSb2xlIG1pc21hdGNoIHdpdGggcHJvZmlsZScpO1xuICAgICAgICAgIC8vIEZpeDogVXBkYXRlIHJvbGVzIHRvIG1hdGNoIHByb2ZpbGVcbiAgICAgICAgICBhd2FpdCBNZXRlb3IudXNlcnMudXBkYXRlQXN5bmModXNlci5faWQsIHtcbiAgICAgICAgICAgICRzZXQ6IHsgcm9sZXM6IFt1c2VyLnByb2ZpbGUucm9sZV0gfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGZpeGVzLnB1c2goYEZpeGVkIHJvbGUgbWlzbWF0Y2ggZm9yICR7dXNlci5lbWFpbHM/LlswXT8uYWRkcmVzc31gKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChpc3N1ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHVzZXJzV2l0aElzc3Vlcy5wdXNoKHtcbiAgICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlscz8uWzBdPy5hZGRyZXNzLFxuICAgICAgICAgICAgaXNzdWVzXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdXNlcnNXaXRoSXNzdWVzLFxuICAgICAgICBmaXhlcyxcbiAgICAgICAgbWVzc2FnZTogZml4ZXMubGVuZ3RoID4gMCA/ICdGaXhlZCByb2xlIGlzc3VlcycgOiAnTm8gaXNzdWVzIGZvdW5kJ1xuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IE1ldGVvci5FcnJvcignZGlhZ25vc2UtZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9LFxuXG4gICd1c2Vycy5jcmVhdGVUZXN0VGVhbU1lbWJlcicoKSB7XG4gICAgLy8gT25seSBhbGxvdyBpbiBkZXZlbG9wbWVudFxuICAgIGlmICghcHJvY2Vzcy5lbnYuTk9ERV9FTlYgfHwgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHRlc3RNZW1iZXIgPSB7XG4gICAgICAgICAgZW1haWw6ICd0ZXN0bWVtYmVyQGV4YW1wbGUuY29tJyxcbiAgICAgICAgICBwYXNzd29yZDogJ1Rlc3RQYXNzMTIzIScsXG4gICAgICAgICAgZmlyc3ROYW1lOiAnVGVzdCcsXG4gICAgICAgICAgbGFzdE5hbWU6ICdNZW1iZXInXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgdXNlcklkID0gQWNjb3VudHMuY3JlYXRlVXNlcih7XG4gICAgICAgICAgZW1haWw6IHRlc3RNZW1iZXIuZW1haWwsXG4gICAgICAgICAgcGFzc3dvcmQ6IHRlc3RNZW1iZXIucGFzc3dvcmQsXG4gICAgICAgICAgcHJvZmlsZToge1xuICAgICAgICAgICAgZmlyc3ROYW1lOiB0ZXN0TWVtYmVyLmZpcnN0TmFtZSxcbiAgICAgICAgICAgIGxhc3ROYW1lOiB0ZXN0TWVtYmVyLmxhc3ROYW1lLFxuICAgICAgICAgICAgcm9sZTogJ3RlYW0tbWVtYmVyJyxcbiAgICAgICAgICAgIGZ1bGxOYW1lOiBgJHt0ZXN0TWVtYmVyLmZpcnN0TmFtZX0gJHt0ZXN0TWVtYmVyLmxhc3ROYW1lfWBcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFNldCB0aGUgcm9sZSBleHBsaWNpdGx5XG4gICAgICAgIE1ldGVvci51c2Vycy51cGRhdGUodXNlcklkLCB7XG4gICAgICAgICAgJHNldDogeyByb2xlczogWyd0ZWFtLW1lbWJlciddIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIHVzZXJJZCxcbiAgICAgICAgICBtZXNzYWdlOiAnVGVzdCB0ZWFtIG1lbWJlciBjcmVhdGVkIHN1Y2Nlc3NmdWxseSdcbiAgICAgICAgfTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tjcmVhdGVUZXN0VGVhbU1lbWJlcl0gRXJyb3I6JywgZXJyb3IpO1xuICAgICAgICB0aHJvdyBuZXcgTWV0ZW9yLkVycm9yKCdjcmVhdGUtdGVzdC1tZW1iZXItZmFpbGVkJywgZXJyb3IubWVzc2FnZSk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBNZXRlb3IuRXJyb3IoJ25vdC1kZXZlbG9wbWVudCcsICdUaGlzIG1ldGhvZCBpcyBvbmx5IGF2YWlsYWJsZSBpbiBkZXZlbG9wbWVudCcpO1xuICAgIH1cbiAgfVxufSk7Il19
