Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// imports/api/links.js                                                                                           //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// imports/api/tasks.js                                                                                           //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// server/main.js                                                                                                 //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        var _user$emails3, _user$emails3$;
        console.log('[forgotPassword] Method called with data:', data);
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email
        const user = Accounts.findUserByEmail(email);
        console.log('[forgotPassword] User found:', user ? {
          id: user._id,
          email: (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address
        } : 'No user found');
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Validate password requirements (same as signup)
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Update the user's password using user ID
        console.log('[forgotPassword] Attempting to update password for user ID:', user._id);
        console.log('[forgotPassword] User ID type:', typeof user._id);
        console.log('[forgotPassword] Current user document structure:', JSON.stringify(user, null, 2));
        try {
          // First, let's verify the user exists and can be found
          const userCheck = await Meteor.users.findOneAsync(user._id);
          console.log('[forgotPassword] User verification - found user:', userCheck ? 'YES' : 'NO');
          if (!userCheck) {
            throw new Error('User not found during verification step');
          }

          // Try a simple update first to test if updates work at all
          console.log('[forgotPassword] Testing simple update...');
          const testUpdate = await Meteor.users.updateAsync(user._id, {
            $set: {
              'profile.lastPasswordReset': new Date()
            }
          });
          console.log('[forgotPassword] Test update result:', testUpdate);
          if (testUpdate !== 1) {
            throw new Error("Test update failed. Result: ".concat(testUpdate));
          }

          // Now try the password update using bcrypt directly
          console.log('[forgotPassword] Proceeding with password update...');
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Generated hash length:', hashedPassword.length);

          // Try updating with the most common Meteor password structure
          const passwordUpdate = await Meteor.users.updateAsync(user._id, {
            $set: {
              'services.password.bcrypt': hashedPassword
            }
          });
          console.log('[forgotPassword] Password update result:', passwordUpdate);
          if (passwordUpdate === 1) {
            console.log("[forgotPassword] Password reset successful for user ID: ".concat(user._id, ", email: ").concat(email));
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            // If that didn't work, try creating the services object structure
            console.log('[forgotPassword] Trying to create services structure...');
            const servicesUpdate = await Meteor.users.updateAsync(user._id, {
              $set: {
                'services': {
                  password: {
                    bcrypt: hashedPassword
                  }
                }
              }
            });
            console.log('[forgotPassword] Services structure update result:', servicesUpdate);
            if (servicesUpdate === 1) {
              console.log("[forgotPassword] Password reset successful with services structure for user ID: ".concat(user._id));
              return {
                success: true,
                message: 'Password updated successfully'
              };
            } else {
              throw new Error("All update attempts failed. Password update: ".concat(passwordUpdate, ", Services update: ").concat(servicesUpdate));
            }
          }
        } catch (error) {
          console.error('[forgotPassword] Error updating password for user:', user._id, 'Error details:', error);
          console.error('[forgotPassword] Error stack:', error.stack);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      // Simple test method to verify method calls are working
      'users.testMethod'() {
        console.log('[testMethod] Method called successfully');
        return {
          success: true,
          message: 'Test method working'
        };
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails4, _user$emails4$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails5, _user$emails5$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails6, _user$emails6$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));
            }
            if (issues.length > 0) {
              var _user$emails7, _user$emails7$;
              usersWithIssues.push({
                email: (_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
