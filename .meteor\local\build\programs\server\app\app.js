Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/links.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/tasks.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// server/main.js                                                                                                  //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation - allow login without email verification
      Accounts.config({
        sendVerificationEmail: false,
        // Disable email verification requirement
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email using async method
        console.log('[forgotPassword] Searching for user...');
        let targetUser = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!targetUser) {
          console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');
          targetUser = await Meteor.users.findOneAsync({
            'emails.address': {
              $regex: new RegExp("^".concat(email, "$"), 'i')
            }
          });
          if (!targetUser) {
            throw new Meteor.Error('user-not-found', 'No user found with this email address');
          }
          console.log('[forgotPassword] User found with case-insensitive search');
        } else {
          console.log('[forgotPassword] User found with direct search');
        }

        // Ensure we have a valid user with ID
        if (!targetUser || !targetUser._id) {
          throw new Meteor.Error('user-invalid', 'Found user but missing ID');
        }
        console.log('[forgotPassword] Final user ID:', targetUser._id);
        console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Comprehensive password update with debugging
        try {
          console.log('[forgotPassword] Starting password update...');

          // First, check current user document
          console.log('[forgotPassword] Checking current user document...');
          const currentUser = await Meteor.users.findOneAsync(targetUser._id);
          console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));
          if (!currentUser) {
            throw new Meteor.Error('user-not-found', 'User document not found during update');
          }

          // Create password hash using bcrypt directly
          const bcrypt = require('bcrypt');
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);
          console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');

          // Try multiple update approaches
          let updateResult = 0;
          let successMethod = null;

          // Method 1: Update services.password.bcrypt directly
          console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');
          try {
            updateResult = await Meteor.users.updateAsync(targetUser._id, {
              $set: {
                'services.password.bcrypt': hashedPassword
              }
            });
            console.log('[forgotPassword] Method 1 result:', updateResult);
            if (updateResult === 1) {
              successMethod = 'Method 1: services.password.bcrypt';
            }
          } catch (method1Error) {
            console.error('[forgotPassword] Method 1 error:', method1Error);
          }

          // Method 2: Update entire services.password object
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 2: Updating entire services.password object...');
            try {
              updateResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  'services.password': {
                    bcrypt: hashedPassword
                  }
                }
              });
              console.log('[forgotPassword] Method 2 result:', updateResult);
              if (updateResult === 1) {
                successMethod = 'Method 2: entire services.password object';
              }
            } catch (method2Error) {
              console.error('[forgotPassword] Method 2 error:', method2Error);
            }
          }

          // Method 3: Update entire services object
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 3: Updating entire services object...');
            try {
              var _currentUser$services, _currentUser$services2;
              updateResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  services: {
                    password: {
                      bcrypt: hashedPassword
                    },
                    resume: ((_currentUser$services = currentUser.services) === null || _currentUser$services === void 0 ? void 0 : _currentUser$services.resume) || {
                      loginTokens: []
                    },
                    email: ((_currentUser$services2 = currentUser.services) === null || _currentUser$services2 === void 0 ? void 0 : _currentUser$services2.email) || {}
                  }
                }
              });
              console.log('[forgotPassword] Method 3 result:', updateResult);
              if (updateResult === 1) {
                successMethod = 'Method 3: entire services object';
              }
            } catch (method3Error) {
              console.error('[forgotPassword] Method 3 error:', method3Error);
            }
          }

          // Method 4: Test basic update capability
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 4: Testing basic update capability...');
            try {
              const testResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  'profile.passwordResetTest': new Date()
                }
              });
              console.log('[forgotPassword] Basic update test result:', testResult);
              if (testResult === 1) {
                console.log('[forgotPassword] Basic updates work, trying password with $unset first...');
                // Try unsetting and then setting
                await Meteor.users.updateAsync(targetUser._id, {
                  $unset: {
                    'services.password': ''
                  }
                });
                updateResult = await Meteor.users.updateAsync(targetUser._id, {
                  $set: {
                    'services.password': {
                      bcrypt: hashedPassword
                    }
                  }
                });
                console.log('[forgotPassword] Unset/Set method result:', updateResult);
                if (updateResult === 1) {
                  successMethod = 'Method 4: unset then set';
                }
              }
            } catch (method4Error) {
              console.error('[forgotPassword] Method 4 error:', method4Error);
            }
          }
          if (updateResult === 1) {
            var _updatedUser$services, _updatedUser$services2;
            console.log("[forgotPassword] Password update successful using: ".concat(successMethod));

            // Verify the update worked
            const updatedUser = await Meteor.users.findOneAsync(targetUser._id);
            console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));

            // Test password verification
            if ((_updatedUser$services = updatedUser.services) !== null && _updatedUser$services !== void 0 && (_updatedUser$services2 = _updatedUser$services.password) !== null && _updatedUser$services2 !== void 0 && _updatedUser$services2.bcrypt) {
              const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);
              console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');
            }
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);

            // Log user permissions and collection info
            console.log('[forgotPassword] User ID:', targetUser._id);
            console.log('[forgotPassword] User ID type:', typeof targetUser._id);
            console.log('[forgotPassword] Current user exists:', !!currentUser);
            console.log('[forgotPassword] User roles:', currentUser.roles);
            throw new Meteor.Error('password-update-failed', 'Failed to update password in database');
          }
        } catch (error) {
          console.error('[forgotPassword] Error during password update:', error);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      async 'users.debugUser'(_ref3) {
        let {
          email
        } = _ref3;
        try {
          var _fullUser$services, _fullUser$services2, _fullUser$services2$p;
          check(email, String);
          console.log('[debugUser] Debugging user:', email);

          // Find user using async method
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            console.log('[debugUser] User not found');
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[debugUser] User found:', user._id);

          // Get full user document using async method
          const fullUser = await Meteor.users.findOneAsync(user._id);
          console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));
          if (!fullUser) {
            console.log('[debugUser] Full user document not found');
            return {
              success: false,
              error: 'Full user document not found'
            };
          }

          // Test basic update using async method
          let testUpdateResult = null;
          try {
            testUpdateResult = await Meteor.users.updateAsync(user._id, {
              $set: {
                'profile.debugTest': new Date()
              }
            });
            console.log('[debugUser] Test update result:', testUpdateResult);
          } catch (updateError) {
            console.error('[debugUser] Test update error:', updateError);
          }

          // Try using Accounts.setPassword if available
          let hasSetPassword = false;
          try {
            hasSetPassword = typeof Accounts.setPassword === 'function';
            console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);
          } catch (setPasswordError) {
            console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);
          }
          const result = {
            success: true,
            userId: user._id,
            userIdType: typeof user._id,
            hasServices: !!fullUser.services,
            hasPassword: !!((_fullUser$services = fullUser.services) !== null && _fullUser$services !== void 0 && _fullUser$services.password),
            hasBcrypt: !!((_fullUser$services2 = fullUser.services) !== null && _fullUser$services2 !== void 0 && (_fullUser$services2$p = _fullUser$services2.password) !== null && _fullUser$services2$p !== void 0 && _fullUser$services2$p.bcrypt),
            roles: fullUser.roles || [],
            profile: fullUser.profile || {},
            testUpdateResult: testUpdateResult,
            hasSetPassword: hasSetPassword,
            servicesStructure: fullUser.services || {}
          };
          console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));
          return result;
        } catch (error) {
          console.error('[debugUser] Error in debug method:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.testLogin'(_ref4) {
        let {
          email,
          password
        } = _ref4;
        check(email, String);
        check(password, String);
        console.log('[testLogin] Testing login for email:', email);

        // Find user using async method
        const user = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!user) {
          console.log('[testLogin] User not found');
          return {
            success: false,
            error: 'User not found'
          };
        }
        console.log('[testLogin] User found:', user._id);
        console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));

        // Test password verification
        try {
          // Check if password service exists
          if (!user.services || !user.services.password || !user.services.password.bcrypt) {
            console.log('[testLogin] No password hash found in user services');
            return {
              success: false,
              error: 'No password hash found',
              userId: user._id,
              services: user.services
            };
          }
          const bcrypt = require('bcrypt');
          const storedHash = user.services.password.bcrypt;
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
          console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');
          console.log('[testLogin] Password length:', password.length);
          return {
            success: passwordMatch,
            userId: user._id,
            hashPreview: storedHash.substring(0, 20) + '...',
            passwordLength: password.length
          };
        } catch (error) {
          console.error('[testLogin] Error during password test:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.comparePasswordFormats'(_ref5) {
        var _user$services, _user$services$passwo;
        let {
          email
        } = _ref5;
        check(email, String);
        console.log('[comparePasswordFormats] Checking password format for:', email);

        // Find user
        const user = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!user) {
          return {
            success: false,
            error: 'User not found'
          };
        }
        console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));

        // Check if user has password
        if (!((_user$services = user.services) !== null && _user$services !== void 0 && (_user$services$passwo = _user$services.password) !== null && _user$services$passwo !== void 0 && _user$services$passwo.bcrypt)) {
          return {
            success: false,
            error: 'No password found'
          };
        }
        const storedHash = user.services.password.bcrypt;
        console.log('[comparePasswordFormats] Stored hash:', storedHash);
        console.log('[comparePasswordFormats] Hash length:', storedHash.length);
        console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));

        // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)
        const isBcrypt = /^\$2[aby]\$/.test(storedHash);
        console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);
        return {
          success: true,
          userId: user._id,
          hashLength: storedHash.length,
          hashPreview: storedHash.substring(0, 20) + '...',
          isBcryptFormat: isBcrypt,
          fullServices: user.services
        };
      },
      async 'users.testActualLogin'(_ref6) {
        let {
          email,
          password
        } = _ref6;
        check(email, String);
        check(password, String);
        console.log('[testActualLogin] Testing actual login for:', email);
        try {
          var _user$services2, _user$services2$passw, _user$emails3, _user$emails3$;
          // Try to simulate what Meteor.loginWithPassword does
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            console.log('[testActualLogin] User not found');
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[testActualLogin] User found:', user._id);
          console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));

          // Check if password service exists
          if (!((_user$services2 = user.services) !== null && _user$services2 !== void 0 && (_user$services2$passw = _user$services2.password) !== null && _user$services2$passw !== void 0 && _user$services2$passw.bcrypt)) {
            console.log('[testActualLogin] No password hash found');
            return {
              success: false,
              error: 'No password hash found'
            };
          }

          // Test bcrypt verification
          const bcrypt = require('bcrypt');
          const storedHash = user.services.password.bcrypt;
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
          console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');

          // Check if user has any login restrictions
          const isVerified = ((_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.verified) || false;
          console.log('[testActualLogin] Email verified:', isVerified);

          // Check user roles
          console.log('[testActualLogin] User roles:', user.roles);

          // Try to create a login token manually to test if that works
          let loginTokenTest = null;
          try {
            // This is what Meteor does internally for login
            const stampedToken = Accounts._generateStampedLoginToken();
            console.log('[testActualLogin] Generated login token:', !!stampedToken);
            loginTokenTest = 'Token generation successful';
          } catch (tokenError) {
            console.error('[testActualLogin] Token generation error:', tokenError);
            loginTokenTest = tokenError.message;
          }
          return {
            success: passwordMatch,
            userId: user._id,
            passwordVerification: passwordMatch,
            emailVerified: isVerified,
            userRoles: user.roles,
            hashPreview: storedHash.substring(0, 20) + '...',
            loginTokenTest: loginTokenTest,
            fullUserStructure: {
              _id: user._id,
              emails: user.emails,
              services: user.services,
              roles: user.roles,
              profile: user.profile
            }
          };
        } catch (error) {
          console.error('[testActualLogin] Error:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.simulateLogin'(_ref7) {
        let {
          email,
          password
        } = _ref7;
        check(email, String);
        check(password, String);
        console.log('[simulateLogin] Simulating login for:', email);
        try {
          var _user$services3, _user$services3$passw, _user$emails4, _user$emails4$;
          // Find user
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[simulateLogin] User found:', user._id);

          // Check password
          const bcrypt = require('bcrypt');
          const storedHash = (_user$services3 = user.services) === null || _user$services3 === void 0 ? void 0 : (_user$services3$passw = _user$services3.password) === null || _user$services3$passw === void 0 ? void 0 : _user$services3$passw.bcrypt;
          if (!storedHash) {
            return {
              success: false,
              error: 'No password hash found'
            };
          }
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[simulateLogin] Password match:', passwordMatch);
          if (!passwordMatch) {
            return {
              success: false,
              error: 'Invalid password'
            };
          }

          // Check if there are any login restrictions
          const restrictions = [];

          // Check email verification requirement
          const emailVerified = ((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.verified) || false;
          if (!emailVerified) {
            restrictions.push('Email not verified');
          }

          // Check if user is active (no disabled flag)
          if (user.disabled) {
            restrictions.push('User account disabled');
          }

          // Check roles
          if (!user.roles || user.roles.length === 0) {
            restrictions.push('No roles assigned');
          }
          console.log('[simulateLogin] Login restrictions:', restrictions);

          // Try to manually create what loginWithPassword would do
          let loginSimulation = 'Not attempted';
          try {
            // Check if we can generate a login token
            const stampedToken = Accounts._generateStampedLoginToken();
            if (stampedToken) {
              loginSimulation = 'Login token generation successful';
            }
          } catch (tokenError) {
            loginSimulation = "Token error: ".concat(tokenError.message);
          }
          return {
            success: passwordMatch && restrictions.length === 0,
            userId: user._id,
            passwordMatch: passwordMatch,
            emailVerified: emailVerified,
            restrictions: restrictions,
            loginSimulation: loginSimulation,
            userStructure: {
              _id: user._id,
              emails: user.emails,
              roles: user.roles,
              profile: user.profile,
              disabled: user.disabled || false
            }
          };
        } catch (error) {
          console.error('[simulateLogin] Error:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails5, _user$emails5$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails6, _user$emails6$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails7, _user$emails7$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address));
            }
            if (issues.length > 0) {
              var _user$emails8, _user$emails8$;
              usersWithIssues.push({
                email: (_user$emails8 = user.emails) === null || _user$emails8 === void 0 ? void 0 : (_user$emails8$ = _user$emails8[0]) === null || _user$emails8$ === void 0 ? void 0 : _user$emails8$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
