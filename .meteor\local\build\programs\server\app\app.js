Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// imports/api/links.js                                                                                           //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// imports/api/tasks.js                                                                                           //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                //
// server/main.js                                                                                                 //
//                                                                                                                //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                  //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        console.log('[forgotPassword] Method called with data:', data);
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email using multiple approaches
        console.log('[forgotPassword] Searching for user with email:', email);

        // Try different query approaches to find the user
        let user = null;

        // Approach 1: Standard Meteor email structure
        user = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        console.log('[forgotPassword] Approach 1 (emails.address) result:', user ? 'FOUND' : 'NOT FOUND');

        // Approach 2: Case-insensitive search
        if (!user) {
          user = await Meteor.users.findOneAsync({
            'emails.address': {
              $regex: new RegExp("^".concat(email, "$"), 'i')
            }
          });
          console.log('[forgotPassword] Approach 2 (case-insensitive) result:', user ? 'FOUND' : 'NOT FOUND');
        }

        // Approach 3: Try with username field
        if (!user) {
          user = await Meteor.users.findOneAsync({
            username: email
          });
          console.log('[forgotPassword] Approach 3 (username) result:', user ? 'FOUND' : 'NOT FOUND');
        }

        // Approach 4: Try with direct email field
        if (!user) {
          user = await Meteor.users.findOneAsync({
            email: email
          });
          console.log('[forgotPassword] Approach 4 (direct email) result:', user ? 'FOUND' : 'NOT FOUND');
        }

        // Approach 5: Use the original Accounts method as fallback
        if (!user) {
          try {
            user = Accounts.findUserByEmail(email);
            console.log('[forgotPassword] Approach 5 (Accounts.findUserByEmail) result:', user ? 'FOUND' : 'NOT FOUND');
          } catch (accountsError) {
            console.log('[forgotPassword] Accounts.findUserByEmail error:', accountsError.message);
          }
        }

        // Debug: Let's see what users exist in the database
        if (!user) {
          console.log('[forgotPassword] User not found. Let\'s check existing users...');
          const allUsers = await Meteor.users.find({}, {
            fields: {
              emails: 1,
              username: 1,
              email: 1
            },
            limit: 5
          }).fetchAsync();
          console.log('[forgotPassword] Sample users in database:', JSON.stringify(allUsers, null, 2));
        }
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }
        console.log('[forgotPassword] Final user found:', {
          id: user._id,
          emails: user.emails,
          username: user.username,
          email: user.email
        });

        // Validate password requirements (same as signup)
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Update the user's password using user ID
        console.log('[forgotPassword] Attempting to update password for user ID:', user._id);
        console.log('[forgotPassword] User ID type:', typeof user._id);
        console.log('[forgotPassword] Current user document structure:', JSON.stringify(user, null, 2));
        try {
          // User is already found and verified above, no need for additional verification

          // Try a simple update first to test if updates work at all
          console.log('[forgotPassword] Testing simple update...');
          const testUpdate = await Meteor.users.updateAsync(user._id, {
            $set: {
              'profile.lastPasswordReset': new Date()
            }
          });
          console.log('[forgotPassword] Test update result:', testUpdate);
          if (testUpdate !== 1) {
            throw new Error("Test update failed. Result: ".concat(testUpdate));
          }

          // Now try the password update using bcrypt directly
          console.log('[forgotPassword] Proceeding with password update...');
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Generated hash length:', hashedPassword.length);

          // Try updating with the most common Meteor password structure
          const passwordUpdate = await Meteor.users.updateAsync(user._id, {
            $set: {
              'services.password.bcrypt': hashedPassword
            }
          });
          console.log('[forgotPassword] Password update result:', passwordUpdate);
          if (passwordUpdate === 1) {
            console.log("[forgotPassword] Password reset successful for user ID: ".concat(user._id, ", email: ").concat(email));
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            // If that didn't work, try creating the services object structure
            console.log('[forgotPassword] Trying to create services structure...');
            const servicesUpdate = await Meteor.users.updateAsync(user._id, {
              $set: {
                'services': {
                  password: {
                    bcrypt: hashedPassword
                  }
                }
              }
            });
            console.log('[forgotPassword] Services structure update result:', servicesUpdate);
            if (servicesUpdate === 1) {
              console.log("[forgotPassword] Password reset successful with services structure for user ID: ".concat(user._id));
              return {
                success: true,
                message: 'Password updated successfully'
              };
            } else {
              throw new Error("All update attempts failed. Password update: ".concat(passwordUpdate, ", Services update: ").concat(servicesUpdate));
            }
          }
        } catch (error) {
          console.error('[forgotPassword] Error updating password for user:', user._id, 'Error details:', error);
          console.error('[forgotPassword] Error stack:', error.stack);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      // Simple test method to verify method calls are working
      'users.testMethod'() {
        console.log('[testMethod] Method called successfully');
        return {
          success: true,
          message: 'Test method working'
        };
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails3, _user$emails3$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails4, _user$emails4$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails5, _user$emails5$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));
            }
            if (issues.length > 0) {
              var _user$emails6, _user$emails6$;
              usersWithIssues.push({
                email: (_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
