Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/links.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/tasks.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// server/main.js                                                                                                  //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }
        console.log('[forgotPassword] User found with ID:', user._id);
        console.log('[forgotPassword] User ID type:', typeof user._id);

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Comprehensive password update approach with debugging
        try {
          console.log('[forgotPassword] Starting password update...');

          // First, let's examine the current user document structure
          console.log('[forgotPassword] Fetching current user document...');
          const currentUser = await Meteor.users.findOneAsync(user._id);
          console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));
          if (!currentUser) {
            throw new Meteor.Error('user-not-found', 'User document not found during update');
          }

          // Create password hash
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);

          // Try multiple update approaches
          let updateResult = 0;
          const updateAttempts = [{
            name: 'Update services.password.bcrypt',
            operation: () => Meteor.users.updateAsync(user._id, {
              $set: {
                'services.password.bcrypt': hashedPassword
              }
            })
          }, {
            name: 'Update full services.password object',
            operation: () => Meteor.users.updateAsync(user._id, {
              $set: {
                'services.password': {
                  bcrypt: hashedPassword
                }
              }
            })
          }, {
            name: 'Update with query object',
            operation: () => Meteor.users.updateAsync({
              _id: user._id
            }, {
              $set: {
                'services.password.bcrypt': hashedPassword
              }
            })
          }, {
            name: 'Update with string ID',
            operation: () => Meteor.users.updateAsync(user._id.toString(), {
              $set: {
                'services.password.bcrypt': hashedPassword
              }
            })
          }, {
            name: 'Create services object if not exists',
            operation: () => Meteor.users.updateAsync(user._id, {
              $set: {
                'services': _objectSpread(_objectSpread({}, currentUser.services), {}, {
                  password: {
                    bcrypt: hashedPassword
                  }
                })
              }
            })
          }];
          for (const attempt of updateAttempts) {
            try {
              console.log("[forgotPassword] Trying: ".concat(attempt.name));
              updateResult = await attempt.operation();
              console.log("[forgotPassword] ".concat(attempt.name, " result:"), updateResult);
              if (updateResult === 1) {
                console.log("[forgotPassword] SUCCESS with: ".concat(attempt.name));
                break;
              }
            } catch (attemptError) {
              console.error("[forgotPassword] ".concat(attempt.name, " error:"), attemptError);
            }
          }

          // If all updates failed, try a test update to verify the user can be updated at all
          if (updateResult !== 1) {
            console.log('[forgotPassword] All password updates failed. Testing basic update capability...');
            try {
              const testResult = await Meteor.users.updateAsync(user._id, {
                $set: {
                  'profile.passwordResetTest': new Date()
                }
              });
              console.log('[forgotPassword] Test update result:', testResult);
              if (testResult === 1) {
                var _currentUser$services, _currentUser$services2;
                console.log('[forgotPassword] Basic updates work, but password field updates fail');
                // Try one more approach - completely replace the services object
                console.log('[forgotPassword] Trying complete services replacement...');
                const servicesReplaceResult = await Meteor.users.updateAsync(user._id, {
                  $set: {
                    services: {
                      password: {
                        bcrypt: hashedPassword
                      },
                      resume: ((_currentUser$services = currentUser.services) === null || _currentUser$services === void 0 ? void 0 : _currentUser$services.resume) || {},
                      email: ((_currentUser$services2 = currentUser.services) === null || _currentUser$services2 === void 0 ? void 0 : _currentUser$services2.email) || {}
                    }
                  }
                });
                console.log('[forgotPassword] Services replacement result:', servicesReplaceResult);
                updateResult = servicesReplaceResult;
              } else {
                console.error('[forgotPassword] Even basic updates fail. User document may be corrupted or permissions issue.');
              }
            } catch (testError) {
              console.error('[forgotPassword] Test update failed:', testError);
            }
          }
          if (updateResult === 1) {
            console.log('[forgotPassword] Password update successful!');

            // Verify the update worked by checking the user document
            const updatedUser = await Meteor.users.findOneAsync(user._id);
            console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            console.error('[forgotPassword] All update attempts failed. Final result:', updateResult);
            throw new Meteor.Error('password-update-failed', 'Failed to update password in database after all attempts');
          }
        } catch (error) {
          console.error('[forgotPassword] Error during password update:', error);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails3, _user$emails3$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails4, _user$emails4$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails5, _user$emails5$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));
            }
            if (issues.length > 0) {
              var _user$emails6, _user$emails6$;
              usersWithIssues.push({
                email: (_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
