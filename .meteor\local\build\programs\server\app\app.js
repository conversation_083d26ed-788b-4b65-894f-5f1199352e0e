Package["core-runtime"].queue("null",function () {/* Imports for global scope */

MongoInternals = Package.mongo.MongoInternals;
Mongo = Package.mongo.Mongo;
ReactiveVar = Package['reactive-var'].ReactiveVar;
ECMAScript = Package.ecmascript.ECMAScript;
Accounts = Package['accounts-base'].Accounts;
Email = Package.email.Email;
EmailInternals = Package.email.EmailInternals;
Roles = Package['alanning:roles'].Roles;
RolesCollection = Package['alanning:roles'].RolesCollection;
RoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;
Meteor = Package.meteor.Meteor;
global = Package.meteor.global;
meteorEnv = Package.meteor.meteorEnv;
EmitterPromise = Package.meteor.EmitterPromise;
WebApp = Package.webapp.WebApp;
WebAppInternals = Package.webapp.WebAppInternals;
main = Package.webapp.main;
DDP = Package['ddp-client'].DDP;
DDPServer = Package['ddp-server'].DDPServer;
LaunchScreen = Package['launch-screen'].LaunchScreen;
meteorInstall = Package.modules.meteorInstall;
Promise = Package.promise.Promise;
Autoupdate = Package.autoupdate.Autoupdate;

var require = meteorInstall({"imports":{"api":{"links.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/links.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      LinksCollection: () => LinksCollection
    });
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const LinksCollection = new Mongo.Collection('links');
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// imports/api/tasks.js                                                                                            //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    module.export({
      Tasks: () => Tasks,
      taskCategories: () => taskCategories,
      taskLabels: () => taskLabels
    });
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 2);
    let Match;
    module.link("meteor/check", {
      Match(v) {
        Match = v;
      }
    }, 3);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const Tasks = new Mongo.Collection('tasks');
    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
    const taskLabels = [{
      name: 'Bug',
      color: '#ef4444'
    }, {
      name: 'Feature',
      color: '#3b82f6'
    }, {
      name: 'Enhancement',
      color: '#10b981'
    }, {
      name: 'Documentation',
      color: '#8b5cf6'
    }, {
      name: 'Urgent',
      color: '#f59e0b'
    }, {
      name: 'Blocked',
      color: '#6b7280'
    }];
    if (Meteor.isServer) {
      // Publications
      Meteor.publish('tasks', async function () {
        var _user$roles;
        if (!this.userId) {
          return this.ready();
        }

        // Get user's role
        const user = await Meteor.users.findOneAsync(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');

        // If admin, show all tasks
        if (isAdmin) {
          return Tasks.find({}, {
            sort: {
              createdAt: -1
            },
            fields: {
              title: 1,
              description: 1,
              startDate: 1,
              dueDate: 1,
              priority: 1,
              status: 1,
              assignedTo: 1,
              checklist: 1,
              category: 1,
              labels: 1,
              progress: 1,
              attachments: 1,
              links: 1,
              createdAt: 1,
              createdBy: 1,
              updatedAt: 1,
              updatedBy: 1
            }
          });
        }

        // For team members, show tasks they're assigned to or created
        return Tasks.find({
          $or: [{
            assignedTo: this.userId
          }, {
            createdBy: this.userId
          }]
        }, {
          sort: {
            createdAt: -1
          },
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });
      });

      // Publish user data for tasks
      Meteor.publish('taskUsers', function () {
        console.log('Starting taskUsers publication');
        if (!this.userId) {
          console.log('No userId, returning ready');
          return this.ready();
        }

        // Get all tasks
        const tasks = Tasks.find({}).fetch();
        console.log('Found tasks:', tasks.length);

        // Collect all user IDs from tasks
        const userIds = new Set();
        tasks.forEach(task => {
          // Add users who uploaded attachments
          if (task.attachments) {
            task.attachments.forEach(attachment => {
              if (attachment.uploadedBy) {
                userIds.add(String(attachment.uploadedBy));
              }
            });
          }
          // Add users who added links
          if (task.links) {
            task.links.forEach(link => {
              if (link.addedBy) {
                userIds.add(String(link.addedBy));
              }
            });
          }
          // Add assigned users
          if (task.assignedTo) {
            task.assignedTo.forEach(userId => {
              userIds.add(String(userId));
            });
          }
        });
        const userIdArray = Array.from(userIds);
        console.log('Publishing user data for IDs:', userIdArray);

        // Find users and log what we found
        const users = Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        console.log('Found users:', users.map(u => {
          var _u$profile, _u$profile2;
          return {
            _id: u._id,
            name: "".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', " ").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),
            hasProfile: !!u.profile
          };
        }));

        // Return the cursor
        return Meteor.users.find({
          _id: {
            $in: userIdArray
          }
        }, {
          fields: {
            _id: 1,
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        });
      });

      // Add a specific publication for a single task
      Meteor.publish('task', function (taskId) {
        var _user$roles2;
        check(taskId, String);
        if (!this.userId) {
          return this.ready();
        }
        const user = Meteor.users.findOne(this.userId);
        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

        // Return a cursor that will update reactively
        const cursor = Tasks.find({
          _id: taskId
        }, {
          fields: {
            title: 1,
            description: 1,
            startDate: 1,
            dueDate: 1,
            priority: 1,
            status: 1,
            assignedTo: 1,
            checklist: 1,
            category: 1,
            labels: 1,
            progress: 1,
            attachments: 1,
            links: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1
          }
        });

        // Log for debugging
        console.log('Publishing task:', taskId);
        cursor.observe({
          added: doc => console.log('Task added to publication:', doc._id),
          changed: doc => console.log('Task changed in publication:', doc._id),
          removed: doc => console.log('Task removed from publication:', doc._id)
        });
        return cursor;
      });
    }
    Meteor.methods({
      async 'tasks.insert'(task) {
        check(task, {
          title: String,
          description: String,
          startDate: Date,
          dueDate: Date,
          priority: String,
          status: String,
          assignedTo: Array,
          checklist: Array,
          category: String,
          labels: Array,
          progress: Number
        });
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        console.log('Creating new task:', task); // Debug log

        // Process checklist items
        const processedChecklist = task.checklist.map(item => ({
          text: item.text,
          completed: item.completed || false
        }));
        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
          createdAt: new Date(),
          createdBy: this.userId,
          updatedAt: new Date(),
          updatedBy: this.userId,
          progress: task.progress || 0,
          status: 'pending',
          // Default status
          checklist: processedChecklist,
          labels: task.labels || [],
          category: task.category || '',
          assignedTo: task.assignedTo || []
        });
        console.log('Inserting task with values:', taskToInsert); // Debug log

        try {
          const result = await Tasks.insertAsync(taskToInsert);
          console.log('Task created successfully:', result); // Debug log
          return result;
        } catch (error) {
          console.error('Error creating task:', error);
          throw new Meteor.Error('task-creation-failed', error.message);
        }
      },
      async 'tasks.update'(taskId, task) {
        try {
          var _user$roles3;
          console.log('Starting task update:', {
            taskId,
            task
          });
          check(taskId, String);
          check(task, {
            title: String,
            description: String,
            startDate: Date,
            dueDate: Date,
            priority: String,
            assignedTo: Array,
            checklist: Array,
            category: Match.Optional(String),
            labels: Match.Optional(Array),
            progress: Match.Optional(Number),
            status: Match.Optional(String),
            attachments: Match.Optional(Array)
          });
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }

          // Calculate progress based on checklist
          const completedItems = task.checklist.filter(item => item.completed).length;
          const totalItems = task.checklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status = task.status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
            updatedAt: new Date(),
            updatedBy: this.userId,
            progress,
            status,
            category: task.category || existingTask.category || '',
            labels: task.labels || existingTask.labels || [],
            attachments: task.attachments || existingTask.attachments || []
          });
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: taskToUpdate
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }
          return result;
        } catch (error) {
          console.error('Error updating task:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.delete'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        try {
          const result = await Tasks.removeAsync(taskId);
          if (result === 0) {
            throw new Meteor.Error('not-found', 'Task not found');
          }
          return result;
        } catch (error) {
          console.error('Error deleting task:', error);
          throw new Meteor.Error('task-delete-failed', error.message);
        }
      },
      'tasks.updateProgress'(taskId, progress) {
        check(taskId, String);
        check(progress, Number);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = Tasks.findOne(taskId);
        if (!task) {
          throw new Meteor.Error('Task not found.');
        }

        // Check if user is assigned to the task
        if (!task.assignedTo.includes(this.userId)) {
          throw new Meteor.Error('Not authorized to modify this task.');
        }

        // Update task status based on progress
        let status = task.status;
        if (progress === 100) {
          status = 'completed';
        } else if (progress > 0) {
          status = 'in-progress';
        }
        return Tasks.update(taskId, {
          $set: {
            progress,
            status,
            updatedAt: new Date(),
            updatedBy: this.userId
          }
        });
      },
      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {
        try {
          var _user$roles4;
          console.log('Starting toggleChecklistItem with:', {
            taskId,
            itemIndex,
            userId: this.userId
          });
          if (!this.userId) {
            console.log('No user ID found');
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }

          // Validate inputs
          if (!taskId || typeof taskId !== 'string') {
            console.log('Invalid taskId:', taskId);
            throw new Meteor.Error('invalid-input', 'Invalid task ID');
          }
          if (typeof itemIndex !== 'number' || itemIndex < 0) {
            console.log('Invalid itemIndex:', itemIndex);
            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
          }
          const task = await Tasks.findOneAsync(taskId);
          console.log('Found task:', task);
          if (!task) {
            console.log('Task not found');
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task or is admin
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('Found user:', user);
          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
          console.log('Is admin:', isAdmin);
          if (!isAdmin && !task.assignedTo.includes(this.userId)) {
            console.log('User not authorized:', {
              userId: this.userId,
              assignedTo: task.assignedTo
            });
            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
          }
          const checklist = task.checklist || [];
          console.log('Current checklist:', checklist);
          if (itemIndex >= checklist.length) {
            console.log('Invalid item index:', {
              itemIndex,
              checklistLength: checklist.length
            });
            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
          }

          // Create a new array to ensure reactivity
          const updatedChecklist = [...checklist];
          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
            completed: !updatedChecklist[itemIndex].completed
          });
          console.log('Updated checklist:', updatedChecklist);

          // Calculate progress
          const completedItems = updatedChecklist.filter(item => item.completed).length;
          const totalItems = updatedChecklist.length;
          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;

          // Update task status based on progress
          let status;
          if (progress === 100) {
            status = 'completed';
          } else if (progress > 0) {
            status = 'in-progress';
          } else {
            status = 'pending';
          }
          console.log('Updating task with:', {
            taskId,
            updatedChecklist,
            progress,
            status
          });

          // First verify the task still exists
          const existingTask = await Tasks.findOneAsync(taskId);
          if (!existingTask) {
            throw new Meteor.Error('task-not-found', 'Task no longer exists');
          }

          // Perform the update
          const updateResult = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              checklist: updatedChecklist,
              progress,
              status,
              updatedAt: new Date(),
              updatedBy: this.userId
            }
          });
          console.log('Update result:', updateResult);
          if (updateResult === 0) {
            throw new Meteor.Error('update-failed', 'Failed to update task');
          }

          // Verify the update
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after update:', updatedTask);
          return updateResult;
        } catch (error) {
          console.error('Error in toggleChecklistItem:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addAttachment'(taskId, fileData) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
          }
          if (!fileData || !fileData.name || !fileData.data) {
            throw new Meteor.Error('invalid-input', 'Invalid file data');
          }

          // Ensure we're storing the user ID as a string
          const uploaderId = String(this.userId);

          // Add the file data to attachments with uploader info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              attachments: {
                name: fileData.name,
                type: fileData.type,
                data: fileData.data,
                uploadedAt: new Date(),
                uploadedBy: uploaderId
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: uploaderId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.addLink'(taskId, link) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if user is assigned to the task
          if (!task.assignedTo.includes(this.userId)) {
            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
          }
          if (!link) {
            throw new Meteor.Error('invalid-input', 'Link URL is required');
          }

          // Validate URL format
          try {
            new URL(link);
          } catch (e) {
            throw new Meteor.Error('invalid-input', 'Invalid URL format');
          }

          // Add the link to links array with adder info
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $push: {
              links: {
                url: link,
                addedAt: new Date(),
                addedBy: String(this.userId)
              }
            },
            $set: {
              updatedAt: new Date(),
              updatedBy: String(this.userId)
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to add link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after adding link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error adding link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeAttachment'(taskId, attachmentIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the attachment exists
          if (!task.attachments || !task.attachments[attachmentIndex]) {
            throw new Meteor.Error('not-found', 'Attachment not found');
          }
          const attachment = task.attachments[attachmentIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const uploadedById = String(attachment.uploadedBy);

          // Only allow the uploader to remove the attachment
          if (currentUserId !== uploadedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
          }

          // Create a new array without the specified attachment
          const updatedAttachments = [...task.attachments];
          updatedAttachments.splice(attachmentIndex, 1);

          // Update the task with the new attachments array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              attachments: updatedAttachments,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove attachment');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing attachment:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing attachment:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.removeLink'(taskId, linkIndex) {
        try {
          if (!this.userId) {
            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
          }
          const task = await Tasks.findOneAsync(taskId);
          if (!task) {
            throw new Meteor.Error('not-found', 'Task not found');
          }

          // Check if the link exists
          if (!task.links || !task.links[linkIndex]) {
            throw new Meteor.Error('not-found', 'Link not found');
          }
          const link = task.links[linkIndex];

          // Convert both IDs to strings for comparison
          const currentUserId = String(this.userId);
          const addedById = String(link.addedBy);

          // Only allow the user who added the link to remove it
          if (currentUserId !== addedById) {
            throw new Meteor.Error('not-authorized', 'You can only remove your own links');
          }

          // Create a new array without the specified link
          const updatedLinks = [...task.links];
          updatedLinks.splice(linkIndex, 1);

          // Update the task with the new links array
          const result = await Tasks.updateAsync({
            _id: taskId
          }, {
            $set: {
              links: updatedLinks,
              updatedAt: new Date(),
              updatedBy: currentUserId
            }
          });
          if (result === 0) {
            throw new Meteor.Error('update-failed', 'Failed to remove link');
          }

          // Get and return the updated task
          const updatedTask = await Tasks.findOneAsync(taskId);
          console.log('Task after removing link:', updatedTask);
          return updatedTask;
        } catch (error) {
          console.error('Error removing link:', error);
          if (error instanceof Meteor.Error) {
            throw error;
          }
          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');
        }
      },
      async 'tasks.findOne'(taskId) {
        check(taskId, String);
        if (!this.userId) {
          throw new Meteor.Error('Not authorized.');
        }
        const task = await Tasks.findOneAsync(taskId);
        if (!task) {
          throw new Meteor.Error('task-not-found', 'Task not found');
        }
        return task;
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"server":{"main.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                 //
// server/main.js                                                                                                  //
//                                                                                                                 //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                   //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let _objectSpread;
    module.link("@babel/runtime/helpers/objectSpread2", {
      default(v) {
        _objectSpread = v;
      }
    }, 0);
    let Meteor;
    module.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let LinksCollection;
    module.link("/imports/api/links", {
      LinksCollection(v) {
        LinksCollection = v;
      }
    }, 1);
    let Accounts;
    module.link("meteor/accounts-base", {
      Accounts(v) {
        Accounts = v;
      }
    }, 2);
    let Email;
    module.link("meteor/email", {
      Email(v) {
        Email = v;
      }
    }, 3);
    let Tasks;
    module.link("/imports/api/tasks", {
      Tasks(v) {
        Tasks = v;
      }
    }, 4);
    let Roles;
    module.link("meteor/alanning:roles", {
      Roles(v) {
        Roles = v;
      }
    }, 5);
    let check;
    module.link("meteor/check", {
      check(v) {
        check = v;
      }
    }, 6);
    let bcrypt;
    module.link("bcrypt", {
      default(v) {
        bcrypt = v;
      }
    }, 7);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    async function insertLink(_ref) {
      let {
        title,
        url
      } = _ref;
      await LinksCollection.insertAsync({
        title,
        url,
        createdAt: new Date()
      });
    }
    const ADMIN_TOKEN = '123456';
    Meteor.startup(async () => {
      var _Meteor$settings$priv;
      // Ensure indexes for Tasks collection
      try {
        await Tasks.createIndex({
          createdAt: 1
        });
        await Tasks.createIndex({
          assignedTo: 1
        });
        await Tasks.createIndex({
          createdBy: 1
        });

        // Ensure indexes for Users collection
        // Note: emails.address index is already created by Meteor accounts system
        await Meteor.users.createIndex({
          roles: 1
        }, {
          background: true
        });
      } catch (error) {
        console.warn('[Startup] Index creation warning:', error.message);
      }

      // Check if we have any team members
      try {
        const allUsers = await Meteor.users.find().fetchAsync();
        console.log('[Startup] All users:', allUsers.map(user => {
          var _user$emails, _user$emails$;
          return {
            id: user._id,
            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,
            roles: user.roles,
            profile: user.profile
          };
        }));

        // First, ensure all users have roles and createdAt
        for (const user of allUsers) {
          const updates = {};
          if (!user.roles || !Array.isArray(user.roles)) {
            updates.roles = ['team-member'];
          }
          if (!user.createdAt) {
            updates.createdAt = new Date();
          }
          if (Object.keys(updates).length > 0) {
            var _user$emails2, _user$emails2$;
            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);
            await Meteor.users.updateAsync(user._id, {
              $set: updates
            });
          }
        }
        const teamMembersCount = await Meteor.users.find({
          'roles': 'team-member'
        }).countAsync();
        console.log('[Startup] Found team members:', teamMembersCount);

        // Create test team members if none exist
        if (teamMembersCount === 0) {
          console.log('[Startup] Creating test team members');
          try {
            // Create multiple test team members
            const testMembers = [{
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'John',
              lastName: 'Doe'
            }, {
              email: '<EMAIL>',
              password: 'TeamPass123!',
              firstName: 'Jane',
              lastName: 'Smith'
            }];
            for (const member of testMembers) {
              const userId = await Accounts.createUserAsync({
                email: member.email,
                password: member.password,
                role: 'team-member',
                createdAt: new Date(),
                profile: {
                  firstName: member.firstName,
                  lastName: member.lastName,
                  role: 'team-member',
                  fullName: "".concat(member.firstName, " ").concat(member.lastName)
                }
              });

              // Set the role explicitly
              await Meteor.users.updateAsync(userId, {
                $set: {
                  roles: ['team-member']
                }
              });
              console.log('[Startup] Created test team member:', {
                id: userId,
                email: member.email,
                name: "".concat(member.firstName, " ").concat(member.lastName)
              });
            }
          } catch (error) {
            console.error('[Startup] Error creating test team members:', error);
          }
        }
      } catch (error) {
        console.error('[Startup] Error checking team members:', error);
      }

      // Email configuration from settings
      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;

      // Configure email SMTP
      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {
        process.env.MAIL_URL = "smtp://".concat(encodeURIComponent(emailSettings.username), ":").concat(encodeURIComponent(emailSettings.password), "@").concat(emailSettings.server, ":").concat(emailSettings.port);

        // Test email configuration
        try {
          console.log('Testing email configuration...');
          Email.send({
            to: emailSettings.username,
            from: emailSettings.username,
            subject: 'Test Email',
            text: 'If you receive this email, your email configuration is working correctly.'
          });
          console.log('Test email sent successfully!');
        } catch (error) {
          console.error('Error sending test email:', error);
        }
      } else {
        console.warn('Email configuration is missing in settings.json');
      }

      // Configure account creation to require email verification
      Accounts.config({
        sendVerificationEmail: true,
        forbidClientAccountCreation: false
      });

      // Customize verification email
      Accounts.emailTemplates.siteName = "Task Management System";
      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? "Task Management System <".concat(emailSettings.username, ">") : "Task Management System <<EMAIL>>";
      Accounts.emailTemplates.verifyEmail = {
        subject() {
          return "Verify Your Email Address";
        },
        text(user, url) {
          const emailAddress = user.emails[0].address;
          return "Hello,\n\n" + "To verify your email address (".concat(emailAddress, "), please click the link below:\n\n") + "".concat(url, "\n\n") + "If you did not request this verification, please ignore this email.\n\n" + "Thanks,\n" + "Your Task Management System Team";
        },
        html(user, url) {
          const emailAddress = user.emails[0].address;
          return "\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (".concat(emailAddress, "), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"").concat(url, "\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      ");
        }
      };

      // If the Links collection is empty, add some data.
      if ((await LinksCollection.find().countAsync()) === 0) {
        await insertLink({
          title: 'Do the Tutorial',
          url: 'https://www.meteor.com/tutorials/react/creating-an-app'
        });
        await insertLink({
          title: 'Follow the Guide',
          url: 'https://guide.meteor.com'
        });
        await insertLink({
          title: 'Read the Docs',
          url: 'https://docs.meteor.com'
        });
        await insertLink({
          title: 'Discussions',
          url: 'https://forums.meteor.com'
        });
      }

      // We publish the entire Links collection to all clients.
      // In order to be fetched in real-time to the clients
      Meteor.publish("links", function () {
        return LinksCollection.find();
      });

      // Add custom fields to users
      Accounts.onCreateUser((options, user) => {
        var _customizedUser$email, _customizedUser$email2;
        console.log('[onCreateUser] Creating user with options:', {
          email: options.email,
          role: options.role,
          profile: options.profile,
          createdAt: options.createdAt
        });
        const customizedUser = _objectSpread({}, user);

        // Ensure we have a profile
        customizedUser.profile = options.profile || {};

        // Add role from options
        const role = options.role || 'team-member';
        customizedUser.roles = [role];

        // Set createdAt if provided, otherwise use current date
        customizedUser.createdAt = options.createdAt || new Date();
        console.log('[onCreateUser] Created user:', {
          id: customizedUser._id,
          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,
          roles: customizedUser.roles,
          profile: customizedUser.profile,
          createdAt: customizedUser.createdAt
        });
        return customizedUser;
      });

      // Publish team members
      Meteor.publish('teamMembers', function () {
        console.log('[teamMembers] Publication called, userId:', this.userId);
        if (!this.userId) {
          console.log('[teamMembers] No userId, returning ready');
          return this.ready();
        }
        try {
          // Simple query to find all team members
          const teamMembers = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1,
              createdAt: 1
            }
          });
          console.log('[teamMembers] Publishing team members');
          return teamMembers;
        } catch (error) {
          console.error('[teamMembers] Error in publication:', error);
          return this.ready();
        }
      });

      // Publish user data with roles
      Meteor.publish('userData', function () {
        if (!this.userId) {
          return this.ready();
        }
        console.log('[userData] Publishing data for user:', this.userId);
        return Meteor.users.find({
          _id: this.userId
        }, {
          fields: {
            roles: 1,
            emails: 1,
            profile: 1
          }
        });
      });
    });

    // Method to create a new user with role
    Meteor.methods({
      'users.create'(_ref2) {
        let {
          email,
          password,
          role,
          adminToken,
          firstName,
          lastName
        } = _ref2;
        // Validate admin token if trying to create admin account
        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
        }

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(password)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(password)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(password)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(password)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Create the user
        try {
          const userId = Accounts.createUser({
            email,
            password,
            role,
            // This will be used in onCreateUser callback
            profile: {
              role,
              // Store in profile as well for easy access
              firstName,
              lastName,
              fullName: "".concat(firstName, " ").concat(lastName)
            }
          });

          // Send verification email
          if (userId) {
            Accounts.sendVerificationEmail(userId);
          }
          return userId;
        } catch (error) {
          throw new Meteor.Error('create-user-failed', error.message);
        }
      },
      async 'users.getRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'User must be logged in');
        }
        try {
          var _user$roles, _user$profile;
          const user = await Meteor.users.findOneAsync(this.userId);
          if (!user) {
            throw new Meteor.Error('user-not-found', 'User not found');
          }

          // Check both roles array and profile for role
          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';
          return role;
        } catch (error) {
          throw new Meteor.Error('get-role-failed', error.message);
        }
      },
      'users.resendVerificationEmail'(email) {
        // Find user by email
        const user = Accounts.findUserByEmail(email);
        if (!user) {
          throw new Meteor.Error('user-not-found', 'No user found with this email address');
        }

        // Check if email is already verified
        const userEmail = user.emails[0];
        if (userEmail.verified) {
          throw new Meteor.Error('already-verified', 'This email is already verified');
        }

        // Send verification email
        try {
          Accounts.sendVerificationEmail(user._id, email);
          return true;
        } catch (error) {
          throw new Meteor.Error('verification-email-failed', error.message);
        }
      },
      async 'users.forgotPassword'(data) {
        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));
        check(data, {
          email: String,
          newPassword: String
        });
        const {
          email,
          newPassword
        } = data;
        console.log('[forgotPassword] Processing request for email:', email);

        // Find user by email using async method
        console.log('[forgotPassword] Searching for user...');
        let targetUser = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!targetUser) {
          console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');
          targetUser = await Meteor.users.findOneAsync({
            'emails.address': {
              $regex: new RegExp("^".concat(email, "$"), 'i')
            }
          });
          if (!targetUser) {
            throw new Meteor.Error('user-not-found', 'No user found with this email address');
          }
          console.log('[forgotPassword] User found with case-insensitive search');
        } else {
          console.log('[forgotPassword] User found with direct search');
        }

        // Ensure we have a valid user with ID
        if (!targetUser || !targetUser._id) {
          throw new Meteor.Error('user-invalid', 'Found user but missing ID');
        }
        console.log('[forgotPassword] Final user ID:', targetUser._id);
        console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);

        // Validate password requirements
        const passwordRegex = {
          length: /.{8,}/,
          uppercase: /[A-Z]/,
          number: /[0-9]/,
          special: /[!@#$%^&*]/
        };
        const passwordErrors = [];
        if (!passwordRegex.length.test(newPassword)) {
          passwordErrors.push('Password must be at least 8 characters long');
        }
        if (!passwordRegex.uppercase.test(newPassword)) {
          passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!passwordRegex.number.test(newPassword)) {
          passwordErrors.push('Password must contain at least one number');
        }
        if (!passwordRegex.special.test(newPassword)) {
          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (passwordErrors.length > 0) {
          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
        }

        // Comprehensive password update with debugging
        try {
          console.log('[forgotPassword] Starting password update...');

          // First, check current user document
          console.log('[forgotPassword] Checking current user document...');
          const currentUser = await Meteor.users.findOneAsync(targetUser._id);
          console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));
          if (!currentUser) {
            throw new Meteor.Error('user-not-found', 'User document not found during update');
          }

          // Create password hash using bcrypt directly
          const bcrypt = require('bcrypt');
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
          console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);
          console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');

          // Try multiple update approaches
          let updateResult = 0;
          let successMethod = null;

          // Method 1: Update services.password.bcrypt directly
          console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');
          try {
            updateResult = await Meteor.users.updateAsync(targetUser._id, {
              $set: {
                'services.password.bcrypt': hashedPassword
              }
            });
            console.log('[forgotPassword] Method 1 result:', updateResult);
            if (updateResult === 1) {
              successMethod = 'Method 1: services.password.bcrypt';
            }
          } catch (method1Error) {
            console.error('[forgotPassword] Method 1 error:', method1Error);
          }

          // Method 2: Update entire services.password object
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 2: Updating entire services.password object...');
            try {
              updateResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  'services.password': {
                    bcrypt: hashedPassword
                  }
                }
              });
              console.log('[forgotPassword] Method 2 result:', updateResult);
              if (updateResult === 1) {
                successMethod = 'Method 2: entire services.password object';
              }
            } catch (method2Error) {
              console.error('[forgotPassword] Method 2 error:', method2Error);
            }
          }

          // Method 3: Update entire services object
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 3: Updating entire services object...');
            try {
              var _currentUser$services, _currentUser$services2;
              updateResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  services: {
                    password: {
                      bcrypt: hashedPassword
                    },
                    resume: ((_currentUser$services = currentUser.services) === null || _currentUser$services === void 0 ? void 0 : _currentUser$services.resume) || {
                      loginTokens: []
                    },
                    email: ((_currentUser$services2 = currentUser.services) === null || _currentUser$services2 === void 0 ? void 0 : _currentUser$services2.email) || {}
                  }
                }
              });
              console.log('[forgotPassword] Method 3 result:', updateResult);
              if (updateResult === 1) {
                successMethod = 'Method 3: entire services object';
              }
            } catch (method3Error) {
              console.error('[forgotPassword] Method 3 error:', method3Error);
            }
          }

          // Method 4: Test basic update capability
          if (updateResult !== 1) {
            console.log('[forgotPassword] Method 4: Testing basic update capability...');
            try {
              const testResult = await Meteor.users.updateAsync(targetUser._id, {
                $set: {
                  'profile.passwordResetTest': new Date()
                }
              });
              console.log('[forgotPassword] Basic update test result:', testResult);
              if (testResult === 1) {
                console.log('[forgotPassword] Basic updates work, trying password with $unset first...');
                // Try unsetting and then setting
                await Meteor.users.updateAsync(targetUser._id, {
                  $unset: {
                    'services.password': ''
                  }
                });
                updateResult = await Meteor.users.updateAsync(targetUser._id, {
                  $set: {
                    'services.password': {
                      bcrypt: hashedPassword
                    }
                  }
                });
                console.log('[forgotPassword] Unset/Set method result:', updateResult);
                if (updateResult === 1) {
                  successMethod = 'Method 4: unset then set';
                }
              }
            } catch (method4Error) {
              console.error('[forgotPassword] Method 4 error:', method4Error);
            }
          }
          if (updateResult === 1) {
            var _updatedUser$services, _updatedUser$services2;
            console.log("[forgotPassword] Password update successful using: ".concat(successMethod));

            // Verify the update worked
            const updatedUser = await Meteor.users.findOneAsync(targetUser._id);
            console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));

            // Test password verification
            if ((_updatedUser$services = updatedUser.services) !== null && _updatedUser$services !== void 0 && (_updatedUser$services2 = _updatedUser$services.password) !== null && _updatedUser$services2 !== void 0 && _updatedUser$services2.bcrypt) {
              const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);
              console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');
            }
            return {
              success: true,
              message: 'Password updated successfully'
            };
          } else {
            console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);

            // Log user permissions and collection info
            console.log('[forgotPassword] User ID:', targetUser._id);
            console.log('[forgotPassword] User ID type:', typeof targetUser._id);
            console.log('[forgotPassword] Current user exists:', !!currentUser);
            console.log('[forgotPassword] User roles:', currentUser.roles);
            throw new Meteor.Error('password-update-failed', 'Failed to update password in database');
          }
        } catch (error) {
          console.error('[forgotPassword] Error during password update:', error);
          throw new Meteor.Error('password-update-failed', "Failed to update password: ".concat(error.message));
        }
      },
      async 'users.debugUser'(_ref3) {
        let {
          email
        } = _ref3;
        try {
          var _fullUser$services, _fullUser$services2, _fullUser$services2$p;
          check(email, String);
          console.log('[debugUser] Debugging user:', email);

          // Find user using async method
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            console.log('[debugUser] User not found');
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[debugUser] User found:', user._id);

          // Get full user document using async method
          const fullUser = await Meteor.users.findOneAsync(user._id);
          console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));
          if (!fullUser) {
            console.log('[debugUser] Full user document not found');
            return {
              success: false,
              error: 'Full user document not found'
            };
          }

          // Test basic update using async method
          let testUpdateResult = null;
          try {
            testUpdateResult = await Meteor.users.updateAsync(user._id, {
              $set: {
                'profile.debugTest': new Date()
              }
            });
            console.log('[debugUser] Test update result:', testUpdateResult);
          } catch (updateError) {
            console.error('[debugUser] Test update error:', updateError);
          }

          // Try using Accounts.setPassword if available
          let hasSetPassword = false;
          try {
            hasSetPassword = typeof Accounts.setPassword === 'function';
            console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);
          } catch (setPasswordError) {
            console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);
          }
          const result = {
            success: true,
            userId: user._id,
            userIdType: typeof user._id,
            hasServices: !!fullUser.services,
            hasPassword: !!((_fullUser$services = fullUser.services) !== null && _fullUser$services !== void 0 && _fullUser$services.password),
            hasBcrypt: !!((_fullUser$services2 = fullUser.services) !== null && _fullUser$services2 !== void 0 && (_fullUser$services2$p = _fullUser$services2.password) !== null && _fullUser$services2$p !== void 0 && _fullUser$services2$p.bcrypt),
            roles: fullUser.roles || [],
            profile: fullUser.profile || {},
            testUpdateResult: testUpdateResult,
            hasSetPassword: hasSetPassword,
            servicesStructure: fullUser.services || {}
          };
          console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));
          return result;
        } catch (error) {
          console.error('[debugUser] Error in debug method:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.testLogin'(_ref4) {
        let {
          email,
          password
        } = _ref4;
        check(email, String);
        check(password, String);
        console.log('[testLogin] Testing login for email:', email);

        // Find user using async method
        const user = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!user) {
          console.log('[testLogin] User not found');
          return {
            success: false,
            error: 'User not found'
          };
        }
        console.log('[testLogin] User found:', user._id);
        console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));

        // Test password verification
        try {
          // Check if password service exists
          if (!user.services || !user.services.password || !user.services.password.bcrypt) {
            console.log('[testLogin] No password hash found in user services');
            return {
              success: false,
              error: 'No password hash found',
              userId: user._id,
              services: user.services
            };
          }
          const bcrypt = require('bcrypt');
          const storedHash = user.services.password.bcrypt;
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
          console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');
          console.log('[testLogin] Password length:', password.length);
          return {
            success: passwordMatch,
            userId: user._id,
            hashPreview: storedHash.substring(0, 20) + '...',
            passwordLength: password.length
          };
        } catch (error) {
          console.error('[testLogin] Error during password test:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.comparePasswordFormats'(_ref5) {
        var _user$services, _user$services$passwo;
        let {
          email
        } = _ref5;
        check(email, String);
        console.log('[comparePasswordFormats] Checking password format for:', email);

        // Find user
        const user = await Meteor.users.findOneAsync({
          'emails.address': email
        });
        if (!user) {
          return {
            success: false,
            error: 'User not found'
          };
        }
        console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));

        // Check if user has password
        if (!((_user$services = user.services) !== null && _user$services !== void 0 && (_user$services$passwo = _user$services.password) !== null && _user$services$passwo !== void 0 && _user$services$passwo.bcrypt)) {
          return {
            success: false,
            error: 'No password found'
          };
        }
        const storedHash = user.services.password.bcrypt;
        console.log('[comparePasswordFormats] Stored hash:', storedHash);
        console.log('[comparePasswordFormats] Hash length:', storedHash.length);
        console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));

        // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)
        const isBcrypt = /^\$2[aby]\$/.test(storedHash);
        console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);
        return {
          success: true,
          userId: user._id,
          hashLength: storedHash.length,
          hashPreview: storedHash.substring(0, 20) + '...',
          isBcryptFormat: isBcrypt,
          fullServices: user.services
        };
      },
      async 'users.testActualLogin'(_ref6) {
        let {
          email,
          password
        } = _ref6;
        check(email, String);
        check(password, String);
        console.log('[testActualLogin] Testing actual login for:', email);
        try {
          var _user$services2, _user$services2$passw, _user$emails3, _user$emails3$;
          // Try to simulate what Meteor.loginWithPassword does
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            console.log('[testActualLogin] User not found');
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[testActualLogin] User found:', user._id);
          console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));

          // Check if password service exists
          if (!((_user$services2 = user.services) !== null && _user$services2 !== void 0 && (_user$services2$passw = _user$services2.password) !== null && _user$services2$passw !== void 0 && _user$services2$passw.bcrypt)) {
            console.log('[testActualLogin] No password hash found');
            return {
              success: false,
              error: 'No password hash found'
            };
          }

          // Test bcrypt verification
          const bcrypt = require('bcrypt');
          const storedHash = user.services.password.bcrypt;
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
          console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');

          // Check if user has any login restrictions
          const isVerified = ((_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.verified) || false;
          console.log('[testActualLogin] Email verified:', isVerified);

          // Check user roles
          console.log('[testActualLogin] User roles:', user.roles);

          // Try to create a login token manually to test if that works
          let loginTokenTest = null;
          try {
            // This is what Meteor does internally for login
            const stampedToken = Accounts._generateStampedLoginToken();
            console.log('[testActualLogin] Generated login token:', !!stampedToken);
            loginTokenTest = 'Token generation successful';
          } catch (tokenError) {
            console.error('[testActualLogin] Token generation error:', tokenError);
            loginTokenTest = tokenError.message;
          }
          return {
            success: passwordMatch,
            userId: user._id,
            passwordVerification: passwordMatch,
            emailVerified: isVerified,
            userRoles: user.roles,
            hashPreview: storedHash.substring(0, 20) + '...',
            loginTokenTest: loginTokenTest,
            fullUserStructure: {
              _id: user._id,
              emails: user.emails,
              services: user.services,
              roles: user.roles,
              profile: user.profile
            }
          };
        } catch (error) {
          console.error('[testActualLogin] Error:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.simulateLogin'(_ref7) {
        let {
          email,
          password
        } = _ref7;
        check(email, String);
        check(password, String);
        console.log('[simulateLogin] Simulating login for:', email);
        try {
          var _user$services3, _user$services3$passw, _user$emails4, _user$emails4$;
          // Find user
          const user = await Meteor.users.findOneAsync({
            'emails.address': email
          });
          if (!user) {
            return {
              success: false,
              error: 'User not found'
            };
          }
          console.log('[simulateLogin] User found:', user._id);

          // Check password
          const bcrypt = require('bcrypt');
          const storedHash = (_user$services3 = user.services) === null || _user$services3 === void 0 ? void 0 : (_user$services3$passw = _user$services3.password) === null || _user$services3$passw === void 0 ? void 0 : _user$services3$passw.bcrypt;
          if (!storedHash) {
            return {
              success: false,
              error: 'No password hash found'
            };
          }
          const passwordMatch = bcrypt.compareSync(password, storedHash);
          console.log('[simulateLogin] Password match:', passwordMatch);
          if (!passwordMatch) {
            return {
              success: false,
              error: 'Invalid password'
            };
          }

          // Check if there are any login restrictions
          const restrictions = [];

          // Check email verification requirement
          const emailVerified = ((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.verified) || false;
          if (!emailVerified) {
            restrictions.push('Email not verified');
          }

          // Check if user is active (no disabled flag)
          if (user.disabled) {
            restrictions.push('User account disabled');
          }

          // Check roles
          if (!user.roles || user.roles.length === 0) {
            restrictions.push('No roles assigned');
          }
          console.log('[simulateLogin] Login restrictions:', restrictions);

          // Try to manually create what loginWithPassword would do
          let loginSimulation = 'Not attempted';
          try {
            // Check if we can generate a login token
            const stampedToken = Accounts._generateStampedLoginToken();
            if (stampedToken) {
              loginSimulation = 'Login token generation successful';
            }
          } catch (tokenError) {
            loginSimulation = "Token error: ".concat(tokenError.message);
          }
          return {
            success: passwordMatch && restrictions.length === 0,
            userId: user._id,
            passwordMatch: passwordMatch,
            emailVerified: emailVerified,
            restrictions: restrictions,
            loginSimulation: loginSimulation,
            userStructure: {
              _id: user._id,
              emails: user.emails,
              roles: user.roles,
              profile: user.profile,
              disabled: user.disabled || false
            }
          };
        } catch (error) {
          console.error('[simulateLogin] Error:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      async 'users.checkAndFixAdminRole'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _user$emails5, _user$emails5$;
          const user = await Meteor.users.findOneAsync(this.userId);
          console.log('[checkAndFixAdminRole] Checking user:', {
            id: user === null || user === void 0 ? void 0 : user._id,
            email: user === null || user === void 0 ? void 0 : (_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address,
            roles: user === null || user === void 0 ? void 0 : user.roles
          });

          // If user has no roles array, initialize it
          if (!user.roles) {
            await Meteor.users.updateAsync(this.userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return 'Roles initialized';
          }

          // If user has no roles or doesn't have admin role
          if (!user.roles.includes('admin')) {
            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');

            // Check if this is the first user (they should be admin)
            const totalUsers = await Meteor.users.find().countAsync();
            if (totalUsers === 1) {
              console.log('[checkAndFixAdminRole] First user, setting as admin');
              await Meteor.users.updateAsync(this.userId, {
                $set: {
                  roles: ['admin']
                }
              });
              return 'Admin role added';
            }
            return 'User is not admin';
          }
          return 'User is already admin';
        } catch (error) {
          console.error('[checkAndFixAdminRole] Error:', error);
          throw new Meteor.Error('check-role-failed', error.message);
        }
      },
      async 'users.diagnoseRoles'() {
        if (!this.userId) {
          throw new Meteor.Error('not-authorized', 'You must be logged in');
        }
        try {
          var _currentUser$roles;
          const currentUser = await Meteor.users.findOneAsync(this.userId);
          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {
            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
          }
          const allUsers = await Meteor.users.find().fetchAsync();
          const usersWithIssues = [];
          const fixes = [];
          for (const user of allUsers) {
            var _user$profile3, _user$roles2;
            const issues = [];

            // Check if roles array exists
            if (!user.roles || !Array.isArray(user.roles)) {
              var _user$profile2, _user$emails6, _user$emails6$;
              issues.push('No roles array');
              // Fix: Initialize roles based on profile
              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [role]
                }
              });
              fixes.push("Initialized roles for ".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));
            }

            // Check if role matches profile
            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {
              var _user$emails7, _user$emails7$;
              issues.push('Role mismatch with profile');
              // Fix: Update roles to match profile
              await Meteor.users.updateAsync(user._id, {
                $set: {
                  roles: [user.profile.role]
                }
              });
              fixes.push("Fixed role mismatch for ".concat((_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address));
            }
            if (issues.length > 0) {
              var _user$emails8, _user$emails8$;
              usersWithIssues.push({
                email: (_user$emails8 = user.emails) === null || _user$emails8 === void 0 ? void 0 : (_user$emails8$ = _user$emails8[0]) === null || _user$emails8$ === void 0 ? void 0 : _user$emails8$.address,
                issues
              });
            }
          }
          return {
            usersWithIssues,
            fixes,
            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
          };
        } catch (error) {
          throw new Meteor.Error('diagnose-failed', error.message);
        }
      },
      'users.createTestTeamMember'() {
        // Only allow in development
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
          try {
            const testMember = {
              email: '<EMAIL>',
              password: 'TestPass123!',
              firstName: 'Test',
              lastName: 'Member'
            };
            const userId = Accounts.createUser({
              email: testMember.email,
              password: testMember.password,
              profile: {
                firstName: testMember.firstName,
                lastName: testMember.lastName,
                role: 'team-member',
                fullName: "".concat(testMember.firstName, " ").concat(testMember.lastName)
              }
            });

            // Set the role explicitly
            Meteor.users.update(userId, {
              $set: {
                roles: ['team-member']
              }
            });
            return {
              success: true,
              userId,
              message: 'Test team member created successfully'
            };
          } catch (error) {
            console.error('[createTestTeamMember] Error:', error);
            throw new Meteor.Error('create-test-member-failed', error.message);
          }
        } else {
          throw new Meteor.Error('not-development', 'This method is only available in development');
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".ts",
    ".mjs",
    ".tsx",
    ".jsx"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/server/main.js"
  ]
}});

//# sourceURL=meteor://💻app/app/app.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
