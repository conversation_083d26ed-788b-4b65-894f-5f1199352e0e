{"version": 3, "sources": ["meteor://💻app/packages/ddp-client/client/client.js", "meteor://💻app/packages/ddp-client/client/client_convenience.js", "meteor://💻app/packages/ddp-client/client/queue_stub_helpers.js", "meteor://💻app/packages/ddp-client/common/connection_stream_handlers.js", "meteor://💻app/packages/ddp-client/common/document_processors.js", "meteor://💻app/packages/ddp-client/common/livedata_connection.js", "meteor://💻app/packages/ddp-client/common/message_processors.js", "meteor://💻app/packages/ddp-client/common/method_invoker.js", "meteor://💻app/packages/ddp-client/common/mongo_id_map.js", "meteor://💻app/packages/ddp-client/common/namespace.js"], "names": ["module", "link", "DDP", "v", "Meteor", "loadAsyncStubHelpers", "refresh", "runtimeConfig", "__meteor_runtime_config__", "Object", "create", "ddpUrl", "DDP_DEFAULT_CONNECTION_URL", "retry", "Retry", "onDDPVersionNegotiationFailure", "description", "_debug", "Package", "reload", "migrationData", "Reload", "_migrationData", "failures", "DDPVersionNegotiationFailures", "_onMigrate", "retryLater", "_reload", "immediateMigration", "connection", "connect", "for<PERSON>ach", "name", "bind", "_objectSpread", "default", "export", "Connection", "queueSize", "queue", "Promise", "resolve", "queueFunction", "fn", "promiseProps", "arguments", "length", "undefined", "reject", "promise", "_resolve", "_reject", "finally", "_promise$stubPromise", "stubPromise", "catch", "_maybeMigrate", "serverPromise", "oldReadyToMigrate", "prototype", "_readyToMigrate", "apply", "currentMethodInvocation", "oldApplyAsync", "applyAsync", "args", "_CurrentMethodInvocation", "_set", "enclosing", "get", "alreadyInSimulation", "isSimulation", "isFromCallAsync", "_isFromCallAsync", "_getIsSimulation", "stubPromiseResolver", "serverPromiseResolver", "r", "finished", "_setImmediate", "applyAsyncPromise", "then", "result", "err", "console", "warn", "concat", "oldApply", "options", "callback", "_stream", "_neverQueued", "methodInvoker", "call", "_returnMethodInvoker", "_addOutstandingMethod", "queueSend", "oldSubscribe", "subscribe", "oldSend", "_send", "params", "shouldQueue", "_oldSendOutstandingMethodBlocksMessages", "_sendOutstandingMethodBlocksMessages", "ConnectionStreamHandlers", "DDPCommon", "constructor", "_connection", "onMessage", "raw_msg", "msg", "parseDDP", "e", "_heartbeat", "messageReceived", "testMessageOnConnect", "keys", "server_id", "_version", "_versionSuggestion", "_routeMessage", "_livedata_connected", "onConnected", "_handleFailedMessage", "respondToPings", "id", "_livedata_data", "_livedata_nosub", "_livedata_result", "_livedata_error", "_supportedDDPVersions", "indexOf", "version", "reconnect", "_force", "disconnect", "_permanent", "_error", "onReset", "_buildConnectMessage", "_handleOutstandingMethodsOnReset", "_callOnReconnectAndSendAppropriateOutstandingMethods", "_resendSubscriptions", "_lastSessionId", "session", "support", "blocks", "_outstandingMethodBlocks", "currentMethodBlock", "methods", "filter", "sentMessage", "noRetry", "receiveResult", "Error", "shift", "values", "_methodInvokers", "invoker", "entries", "_subscriptions", "_ref", "sub", "_sendQueued", "DocumentProcessors", "MongoID", "DiffSequence", "hasOwn", "isEmpty", "_process_added", "updates", "self", "idParse", "serverDoc", "_getServerDoc", "collection", "isExisting", "document", "fields", "_id", "_resetStores", "currentDoc", "_stores", "getDoc", "_pushUpdate", "_process_changed", "applyChanges", "_process_removed", "_process_ready", "subs", "subId", "_runWhenAllServerDocsAreFlushed", "subRecord", "ready", "readyCallback", "readyDeps", "changed", "_process_updated", "methodId", "docs", "_documentsWrittenByStub", "written", "JSON", "stringify", "writtenByStubs", "idStringify", "replace", "flushCallbacks", "c", "_serverDocuments", "remove", "callbackInvoker", "dataVisible", "push", "serverDocsForCollection", "_objectWithoutProperties", "Tracker", "EJSON", "Random", "MethodInvoker", "slice", "last", "MongoIDMap", "MessageProcessors", "url", "heartbeatInterval", "heartbeatTimeout", "npmFayeOptions", "reloadWithOutstanding", "supportedDDPVersions", "SUPPORTED_DDP_VERSIONS", "bufferedWritesInterval", "bufferedWritesMaxAge", "onReconnect", "ClientStream", "ConnectionError", "headers", "_sockjsOptions", "_dontPrintErrors", "connectTimeoutMs", "_methodHandlers", "_nextMethodId", "_heartbeatInterval", "_heartbeatTimeout", "_afterUpdateCallbacks", "_messagesBufferedUntilQuiescence", "_methodsBlockingQuiescence", "_subsBeingRevived", "_updatesForUnknownStores", "_retryMigrate", "_bufferedWrites", "_bufferedWritesFlushAt", "_bufferedWritesFlushHandle", "_bufferedWritesInterval", "_bufferedWritesMaxAge", "_userId", "_userIdDeps", "Dependency", "isClient", "_streamHandlers", "onDisconnect", "stop", "isServer", "on", "bindEnvironment", "_messageProcessors", "_documentProcessors", "createStoreMethods", "wrappedStore", "store", "keysOfStore", "method", "registerStoreClient", "queued", "Array", "isArray", "beginUpdate", "update", "endUpdate", "registerStoreServer", "callbacks", "lastPara<PERSON>", "onReady", "pop", "onError", "onStop", "some", "f", "existing", "find", "inactive", "equals", "<PERSON><PERSON><PERSON><PERSON>", "stopCallback", "clone", "handle", "record", "depend", "subscriptionId", "active", "onInvalidate", "afterFlush", "isAsyncCall", "_isCallAsyncMethodRunning", "func", "_ref2", "callAsync", "returnServerResultPromise", "_this$_stubCall", "_stubCall", "stubInvocation", "invocation", "stubOptions", "_excluded", "hasStub", "_saveOriginals", "stubReturnValue", "with<PERSON><PERSON><PERSON>", "_isPromise", "exception", "_apply", "_applyAsyncStubInvocation", "_applyAsync", "o", "_this$_stubCall2", "_excluded2", "currentContext", "_setNewContextAndGetCurrent", "_ref3", "stubCallValue", "randomSeed", "_retrieveAndStoreOriginals", "message", "throwStubExceptions", "_expectedByTest", "returnStubValue", "_len", "allArgs", "_key", "from", "value", "onResultReceived", "wait", "stub", "defaultReturn", "randomSeedGenerator", "makeRpcSeed", "setUserId", "userId", "MethodInvocation", "_noYieldsAllowed", "_waitingFor<PERSON>uiescence", "_flushBufferedWrites", "saveOriginals", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "originals", "retrieveOriginals", "doc", "<PERSON><PERSON><PERSON><PERSON>", "_unsubscribeAll", "obj", "send", "stringifyDDP", "_lostConnection", "error", "status", "close", "_anyMethodsAreOutstanding", "invokers", "_processOneDataMessage", "messageType", "_prepareBuffersToFlush", "clearTimeout", "writes", "_performWritesServer", "_updates$store$_name", "_name", "storeName", "messages", "CHUNK_SIZE", "i", "chunk", "Math", "min", "process", "nextTick", "_runAfterUpdateCallbacks", "_performWritesClient", "_updates$store$_name2", "_ref5", "runFAfterUpdates", "unflushedServerDocCount", "onServerDocFlush", "serverDocuments", "writtenByStubForAMethodWithSentMessage", "sendMessage", "_outstandingMethodFinished", "firstBlock", "_sendOutstandingMethods", "m", "oldOutstandingMethodBlocks", "_reconnectHook", "each", "Heartbeat", "onTimeout", "sendPing", "start", "reconnectedToPreviousSession", "gotResult", "bufferedMessages", "bufferedMessage", "standardWrite", "Date", "valueOf", "setTimeout", "_liveDataWritesPromise", "idx", "found", "splice", "reason", "details", "meteorErrorFromMsg", "msgArg", "offendingMessage", "_callback", "_message", "_onResultReceived", "_wait", "_methodResult", "_dataVisible", "_maybeInvokeCallback", "IdMap", "allConnections", "EnvironmentVariable", "_CurrentPublicationInvocation", "_CurrentInvocation", "_CurrentCallAsyncInvocation", "connectionErrorConstructor", "makeErrorType", "ForcedReconnectError", "randomStream", "scope", "RandomStream", "ret", "Hook", "register", "_allSubscriptionsReady", "every", "conn"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;EAACC,GAAG,EAAC;AAAK,CAAC,EAAC,CAAC,CAAC;AAACF,MAAM,CAACC,IAAI,CAAC,+BAA+B,CAAC;AAACD,MAAM,CAACC,IAAI,CAAC,sBAAsB,CAAC,C;;;;;;;;;;;ACApI,IAAIC,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;EAACC,GAAGA,CAACC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAMA,CAACD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,oBAAoB;AAACL,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;EAACI,oBAAoBA,CAACF,CAAC,EAAC;IAACE,oBAAoB,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAIhP;AACA;AACAC,MAAM,CAACE,OAAO,GAAG,MAAM,CAAC,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,OAAOC,yBAAyB,KAAK,WAAW,GAAGA,yBAAyB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACxH,MAAMC,MAAM,GAAGJ,aAAa,CAACK,0BAA0B,IAAI,GAAG;AAE9D,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;AAEzB,SAASC,8BAA8BA,CAACC,WAAW,EAAE;EACnDZ,MAAM,CAACa,MAAM,CAACD,WAAW,CAAC;EAC1B,IAAIE,OAAO,CAACC,MAAM,EAAE;IAClB,MAAMC,aAAa,GAAGF,OAAO,CAACC,MAAM,CAACE,MAAM,CAACC,cAAc,CAAC,UAAU,CAAC,IAAIb,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7F,IAAIa,QAAQ,GAAGH,aAAa,CAACI,6BAA6B,IAAI,CAAC;IAC/D,EAAED,QAAQ;IACVL,OAAO,CAACC,MAAM,CAACE,MAAM,CAACI,UAAU,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE;MAAED,6BAA6B,EAAED;IAAS,CAAC,CAAC,CAAC;IACvGV,KAAK,CAACa,UAAU,CAACH,QAAQ,EAAE,MAAM;MAC/BL,OAAO,CAACC,MAAM,CAACE,MAAM,CAACM,OAAO,CAAC;QAAEC,kBAAkB,EAAE;MAAK,CAAC,CAAC;IAC7D,CAAC,CAAC;EACJ;AACF;;AAEA;AACAvB,oBAAoB,CAAC,CAAC;AAEtBD,MAAM,CAACyB,UAAU,GAAG3B,GAAG,CAAC4B,OAAO,CAACnB,MAAM,EAAE;EACtCI,8BAA8B,EAAEA;AAClC,CAAC,CAAC;;AAEF;AACA;AACA,CACE,WAAW,EACX,SAAS,EACT,aAAa,EACb,MAAM,EACN,WAAW,EACX,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,YAAY,CACb,CAACgB,OAAO,CAACC,IAAI,IAAI;EAChB5B,MAAM,CAAC4B,IAAI,CAAC,GAAG5B,MAAM,CAACyB,UAAU,CAACG,IAAI,CAAC,CAACC,IAAI,CAAC7B,MAAM,CAACyB,UAAU,CAAC;AAChE,CAAC,CAAC,C;;;;;;;;;;;AC/DF,IAAIK,aAAa;AAAClC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAOA,CAAChC,CAAC,EAAC;IAAC+B,aAAa,GAAC/B,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGH,MAAM,CAACoC,MAAM,CAAC;EAAC/B,oBAAoB,EAACA,CAAA,KAAIA;AAAoB,CAAC,CAAC;AAAC,IAAIH,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;EAACC,GAAGA,CAACC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIkC,UAAU;AAACrC,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACoC,UAAUA,CAAClC,CAAC,EAAC;IAACkC,UAAU,GAAClC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAG1N;;AAEA,IAAImC,SAAS,GAAG,CAAC;AACjB,IAAIC,KAAK,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;AAEtB,MAAMpC,oBAAoB,GAAGA,CAAA,KAAM;EACxC,SAASqC,aAAaA,CAACC,EAAE,EAAqB;IAAA,IAAnBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC1CP,SAAS,IAAI,CAAC;IAEd,IAAIG,OAAO;IACX,IAAIO,MAAM;IACV,MAAMC,OAAO,GAAG,IAAIT,OAAO,CAAC,CAACU,QAAQ,EAAEC,OAAO,KAAK;MACjDV,OAAO,GAAGS,QAAQ;MAClBF,MAAM,GAAGG,OAAO;IAClB,CAAC,CAAC;IAEFZ,KAAK,GAAGA,KAAK,CAACa,OAAO,CAAC,MAAM;MAAA,IAAAC,oBAAA;MAC1BV,EAAE,CAACF,OAAO,EAAEO,MAAM,CAAC;MAEnB,QAAAK,oBAAA,GAAOJ,OAAO,CAACK,WAAW,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEFN,OAAO,CACJM,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAA,CAChBH,OAAO,CAAC,MAAM;MACbd,SAAS,IAAI,CAAC;MACd,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnBlC,MAAM,CAACyB,UAAU,CAAC2B,aAAa,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;IAEJP,OAAO,CAACK,WAAW,GAAGV,YAAY,CAACU,WAAW;IAC9CL,OAAO,CAACQ,aAAa,GAAGb,YAAY,CAACa,aAAa;IAElD,OAAOR,OAAO;EAChB;EAEA,IAAIS,iBAAiB,GAAGrB,UAAU,CAACsB,SAAS,CAACC,eAAe;EAC5DvB,UAAU,CAACsB,SAAS,CAACC,eAAe,GAAG,YAAY;IACjD,IAAItB,SAAS,GAAG,CAAC,EAAE;MACjB,OAAO,KAAK;IACd;IAEA,OAAOoB,iBAAiB,CAACG,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;EACjD,CAAC;EAED,IAAIiB,uBAAuB,GAAG,IAAI;;EAElC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAIC,aAAa,GAAG1B,UAAU,CAACsB,SAAS,CAACK,UAAU;EACnD3B,UAAU,CAACsB,SAAS,CAACK,UAAU,GAAG,YAAY;IAC5C,IAAIC,IAAI,GAAGpB,SAAS;IACpB,IAAIb,IAAI,GAAGiC,IAAI,CAAC,CAAC,CAAC;IAElB,IAAIH,uBAAuB,EAAE;MAC3B5D,GAAG,CAACgE,wBAAwB,CAACC,IAAI,CAACL,uBAAuB,CAAC;MAC1DA,uBAAuB,GAAG,IAAI;IAChC;IAEA,MAAMM,SAAS,GAAGlE,GAAG,CAACgE,wBAAwB,CAACG,GAAG,CAAC,CAAC;IACpD,MAAMC,mBAAmB,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,YAAY;IACnD,MAAMC,eAAe,GAAGJ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,gBAAgB;IAEnD,IACErE,MAAM,CAACyB,UAAU,CAAC6C,gBAAgB,CAAC;MACjCF,eAAe;MACfF;IACF,CAAC,CAAC,EACF;MACA;MACA,OAAOP,aAAa,CAACF,KAAK,CAAC,IAAI,EAAEI,IAAI,CAAC;IACxC;IAEA,IAAIU,mBAAmB;IACvB,IAAIC,qBAAqB;IACzB,MAAMtB,WAAW,GAAG,IAAId,OAAO,CAAEqC,CAAC,IAAMF,mBAAmB,GAAGE,CAAE,CAAC;IACjE,MAAMpB,aAAa,GAAG,IAAIjB,OAAO,CAAEqC,CAAC,IAAMD,qBAAqB,GAAGC,CAAE,CAAC;IAErE,OAAOnC,aAAa,CAClB,CAACD,OAAO,EAAEO,MAAM,KAAK;MACnB,IAAI8B,QAAQ,GAAG,KAAK;MAEpB1E,MAAM,CAAC2E,aAAa,CAAC,MAAM;QACzB,MAAMC,iBAAiB,GAAGjB,aAAa,CAACF,KAAK,CAAC,IAAI,EAAEI,IAAI,CAAC;QACzDU,mBAAmB,CAACK,iBAAiB,CAAC1B,WAAW,CAAC;QAClDsB,qBAAqB,CAACI,iBAAiB,CAACvB,aAAa,CAAC;QAEtDuB,iBAAiB,CAAC1B,WAAW,CAC1BC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAAA,CAChBH,OAAO,CAAC,MAAM;UACb0B,QAAQ,GAAG,IAAI;QACjB,CAAC,CAAC;QAEJE,iBAAiB,CACdC,IAAI,CAAEC,MAAM,IAAK;UAChBzC,OAAO,CAACyC,MAAM,CAAC;QACjB,CAAC,CAAC,CACD3B,KAAK,CAAE4B,GAAG,IAAK;UACdnC,MAAM,CAACmC,GAAG,CAAC;QACb,CAAC,CAAC;QAEJ1B,aAAa,CAACF,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC;MAEFnD,MAAM,CAAC2E,aAAa,CAAC,MAAM;QACzB,IAAI,CAACD,QAAQ,EAAE;UACbM,OAAO,CAACC,IAAI,iBAAAC,MAAA,CACMtD,IAAI,wMACtB,CAAC;QACH;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MACEsB,WAAW;MACXG;IACF,CACF,CAAC;EACH,CAAC;EAED,IAAI8B,QAAQ,GAAGlD,UAAU,CAACsB,SAAS,CAACE,KAAK;EACzCxB,UAAU,CAACsB,SAAS,CAACE,KAAK,GAAG,UAAU7B,IAAI,EAAEiC,IAAI,EAAEuB,OAAO,EAAEC,QAAQ,EAAE;IACpE,IAAI,IAAI,CAACC,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOJ,QAAQ,CAAC1B,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IACxC;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAAC4C,QAAQ,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MAC9CC,QAAQ,GAAGD,OAAO;MAClBA,OAAO,GAAGzC,SAAS;IACrB;IAEA,IAAI;MAAE6C,aAAa;MAAEV;IAAO,CAAC,GAAGK,QAAQ,CAACM,IAAI,CAAC,IAAI,EAAE7D,IAAI,EAAEiC,IAAI,EAAA/B,aAAA,CAAAA,aAAA,KACzDsD,OAAO;MACVM,oBAAoB,EAAE;IAAI,IACzBL,QAAQ,CAAC;IAEZ,IAAIG,aAAa,EAAE;MACjBlD,aAAa,CAAED,OAAO,IAAK;QACzB,IAAI,CAACsD,qBAAqB,CAACH,aAAa,EAAEJ,OAAO,CAAC;QAClD/C,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;IAEA,OAAOyC,MAAM;EACf,CAAC;;EAED;AACF;AACA;EACE,IAAIc,SAAS,GAAG,KAAK;EACrB,IAAIC,YAAY,GAAG5D,UAAU,CAACsB,SAAS,CAACuC,SAAS;EACjD7D,UAAU,CAACsB,SAAS,CAACuC,SAAS,GAAG,YAAY;IAC3C,IAAI,IAAI,CAACR,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOM,YAAY,CAACpC,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IAC5C;IAEAmD,SAAS,GAAG,IAAI;IAChB,IAAI;MACF,OAAOC,YAAY,CAACpC,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IAC5C,CAAC,SAAS;MACRmD,SAAS,GAAG,KAAK;IACnB;EACF,CAAC;EAED,IAAIG,OAAO,GAAG9D,UAAU,CAACsB,SAAS,CAACyC,KAAK;EACxC/D,UAAU,CAACsB,SAAS,CAACyC,KAAK,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAE;IAC1D,IAAI,IAAI,CAACZ,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOQ,OAAO,CAACtC,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IACvC;IAEA,IAAI,CAACmD,SAAS,IAAI,CAACM,WAAW,EAAE;MAC9B,OAAOH,OAAO,CAACN,IAAI,CAAC,IAAI,EAAEQ,MAAM,CAAC;IACnC;IAEAL,SAAS,GAAG,KAAK;IACjBtD,aAAa,CAAED,OAAO,IAAK;MACzB,IAAI;QACF0D,OAAO,CAACN,IAAI,CAAC,IAAI,EAAEQ,MAAM,CAAC;MAC5B,CAAC,SAAS;QACR5D,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAI8D,uCAAuC,GACzClE,UAAU,CAACsB,SAAS,CAAC6C,oCAAoC;EAC3DnE,UAAU,CAACsB,SAAS,CAAC6C,oCAAoC,GAAG,YAAY;IACtE,IAAI,IAAI,CAACd,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOY,uCAAuC,CAAC1C,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IACvE;IACAH,aAAa,CAAED,OAAO,IAAK;MACzB,IAAI;QACF8D,uCAAuC,CAAC1C,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;MAChE,CAAC,SAAS;QACRJ,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,C;;;;;;;;;;;AC1NDzC,MAAM,CAACoC,MAAM,CAAC;EAACqE,wBAAwB,EAACA,CAAA,KAAIA;AAAwB,CAAC,CAAC;AAAC,IAAIC,SAAS;AAAC1G,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACyG,SAASA,CAACvG,CAAC,EAAC;IAACuG,SAAS,GAACvG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAMA,CAACD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAG5M,MAAMsG,wBAAwB,CAAC;EACpCE,WAAWA,CAAC9E,UAAU,EAAE;IACtB,IAAI,CAAC+E,WAAW,GAAG/E,UAAU;EAC/B;;EAEA;AACF;AACA;AACA;EACE,MAAMgF,SAASA,CAACC,OAAO,EAAE;IACvB,IAAIC,GAAG;IACP,IAAI;MACFA,GAAG,GAAGL,SAAS,CAACM,QAAQ,CAACF,OAAO,CAAC;IACnC,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV7G,MAAM,CAACa,MAAM,CAAC,6BAA6B,EAAEgG,CAAC,CAAC;MAC/C;IACF;;IAEA;IACA;IACA,IAAI,IAAI,CAACL,WAAW,CAACM,UAAU,EAAE;MAC/B,IAAI,CAACN,WAAW,CAACM,UAAU,CAACC,eAAe,CAAC,CAAC;IAC/C;IAEA,IAAIJ,GAAG,KAAK,IAAI,IAAI,CAACA,GAAG,CAACA,GAAG,EAAE;MAC5B,IAAG,CAACA,GAAG,IAAI,CAACA,GAAG,CAACK,oBAAoB,EAAE;QACpC,IAAI3G,MAAM,CAAC4G,IAAI,CAACN,GAAG,CAAC,CAACjE,MAAM,KAAK,CAAC,IAAIiE,GAAG,CAACO,SAAS,EAAE;QACpDlH,MAAM,CAACa,MAAM,CAAC,qCAAqC,EAAE8F,GAAG,CAAC;MAC3D;MACA;IACF;;IAEA;IACA;IACA,IAAIA,GAAG,CAACA,GAAG,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACH,WAAW,CAACW,QAAQ,GAAG,IAAI,CAACX,WAAW,CAACY,kBAAkB;IACjE;IAEA,MAAM,IAAI,CAACC,aAAa,CAACV,GAAG,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMU,aAAaA,CAACV,GAAG,EAAE;IACvB,QAAQA,GAAG,CAACA,GAAG;MACb,KAAK,WAAW;QACd,MAAM,IAAI,CAACH,WAAW,CAACc,mBAAmB,CAACX,GAAG,CAAC;QAC/C,IAAI,CAACH,WAAW,CAACpB,OAAO,CAACmC,WAAW,CAAC,CAAC;QACtC;MAEF,KAAK,QAAQ;QACX,MAAM,IAAI,CAACC,oBAAoB,CAACb,GAAG,CAAC;QACpC;MAEF,KAAK,MAAM;QACT,IAAI,IAAI,CAACH,WAAW,CAACpB,OAAO,CAACqC,cAAc,EAAE;UAC3C,IAAI,CAACjB,WAAW,CAACR,KAAK,CAAC;YAAEW,GAAG,EAAE,MAAM;YAAEe,EAAE,EAAEf,GAAG,CAACe;UAAG,CAAC,CAAC;QACrD;QACA;MAEF,KAAK,MAAM;QACT;QACA;MAEF,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,SAAS;MACd,KAAK,OAAO;MACZ,KAAK,SAAS;QACZ,MAAM,IAAI,CAAClB,WAAW,CAACmB,cAAc,CAAChB,GAAG,CAAC;QAC1C;MAEF,KAAK,OAAO;QACV,MAAM,IAAI,CAACH,WAAW,CAACoB,eAAe,CAACjB,GAAG,CAAC;QAC3C;MAEF,KAAK,QAAQ;QACX,MAAM,IAAI,CAACH,WAAW,CAACqB,gBAAgB,CAAClB,GAAG,CAAC;QAC5C;MAEF,KAAK,OAAO;QACV,IAAI,CAACH,WAAW,CAACsB,eAAe,CAACnB,GAAG,CAAC;QACrC;MAEF;QACE3G,MAAM,CAACa,MAAM,CAAC,0CAA0C,EAAE8F,GAAG,CAAC;IAClE;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEa,oBAAoBA,CAACb,GAAG,EAAE;IACxB,IAAI,IAAI,CAACH,WAAW,CAACuB,qBAAqB,CAACC,OAAO,CAACrB,GAAG,CAACsB,OAAO,CAAC,IAAI,CAAC,EAAE;MACpE,IAAI,CAACzB,WAAW,CAACY,kBAAkB,GAAGT,GAAG,CAACsB,OAAO;MACjD,IAAI,CAACzB,WAAW,CAAClB,OAAO,CAAC4C,SAAS,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAC,CAAC;IACtD,CAAC,MAAM;MACL,MAAMvH,WAAW,GACf,2DAA2D,GAC3D+F,GAAG,CAACsB,OAAO;MACb,IAAI,CAACzB,WAAW,CAAClB,OAAO,CAAC8C,UAAU,CAAC;QAAEC,UAAU,EAAE,IAAI;QAAEC,MAAM,EAAE1H;MAAY,CAAC,CAAC;MAC9E,IAAI,CAAC4F,WAAW,CAACpB,OAAO,CAACzE,8BAA8B,CAACC,WAAW,CAAC;IACtE;EACF;;EAEA;AACF;AACA;EACE2H,OAAOA,CAAA,EAAG;IACR;IACA;IACA,MAAM5B,GAAG,GAAG,IAAI,CAAC6B,oBAAoB,CAAC,CAAC;IACvC,IAAI,CAAChC,WAAW,CAACR,KAAK,CAACW,GAAG,CAAC;;IAE3B;IACA,IAAI,CAAC8B,gCAAgC,CAAC,CAAC;;IAEvC;IACA;IACA;IACA,IAAI,CAACjC,WAAW,CAACkC,oDAAoD,CAAC,CAAC;IACvE,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;EACEH,oBAAoBA,CAAA,EAAG;IACrB,MAAM7B,GAAG,GAAG;MAAEA,GAAG,EAAE;IAAU,CAAC;IAC9B,IAAI,IAAI,CAACH,WAAW,CAACoC,cAAc,EAAE;MACnCjC,GAAG,CAACkC,OAAO,GAAG,IAAI,CAACrC,WAAW,CAACoC,cAAc;IAC/C;IACAjC,GAAG,CAACsB,OAAO,GAAG,IAAI,CAACzB,WAAW,CAACY,kBAAkB,IAAI,IAAI,CAACZ,WAAW,CAACuB,qBAAqB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACvB,WAAW,CAACY,kBAAkB,GAAGT,GAAG,CAACsB,OAAO;IACjDtB,GAAG,CAACmC,OAAO,GAAG,IAAI,CAACtC,WAAW,CAACuB,qBAAqB;IACpD,OAAOpB,GAAG;EACZ;;EAEA;AACF;AACA;AACA;EACE8B,gCAAgCA,CAAA,EAAG;IACjC,MAAMM,MAAM,GAAG,IAAI,CAACvC,WAAW,CAACwC,wBAAwB;IACxD,IAAID,MAAM,CAACrG,MAAM,KAAK,CAAC,EAAE;IAEzB,MAAMuG,kBAAkB,GAAGF,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO;IAC5CH,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,GAAGD,kBAAkB,CAACE,MAAM,CAC3C3D,aAAa,IAAI;MACf;MACA;MACA,IAAIA,aAAa,CAAC4D,WAAW,IAAI5D,aAAa,CAAC6D,OAAO,EAAE;QACtD7D,aAAa,CAAC8D,aAAa,CACzB,IAAItJ,MAAM,CAACuJ,KAAK,CACd,mBAAmB,EACnB,iEAAiE,GACjE,8DACF,CACF,CAAC;MACH;;MAEA;MACA,OAAO,EAAE/D,aAAa,CAAC4D,WAAW,IAAI5D,aAAa,CAAC6D,OAAO,CAAC;IAC9D,CACF,CAAC;;IAED;IACA,IAAIN,MAAM,CAACrG,MAAM,GAAG,CAAC,IAAIqG,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,CAACxG,MAAM,KAAK,CAAC,EAAE;MACvDqG,MAAM,CAACS,KAAK,CAAC,CAAC;IAChB;;IAEA;IACAnJ,MAAM,CAACoJ,MAAM,CAAC,IAAI,CAACjD,WAAW,CAACkD,eAAe,CAAC,CAAC/H,OAAO,CAACgI,OAAO,IAAI;MACjEA,OAAO,CAACP,WAAW,GAAG,KAAK;IAC7B,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACET,oBAAoBA,CAAA,EAAG;IACrBtI,MAAM,CAACuJ,OAAO,CAAC,IAAI,CAACpD,WAAW,CAACqD,cAAc,CAAC,CAAClI,OAAO,CAACmI,IAAA,IAAe;MAAA,IAAd,CAACpC,EAAE,EAAEqC,GAAG,CAAC,GAAAD,IAAA;MAChE,IAAI,CAACtD,WAAW,CAACwD,WAAW,CAAC;QAC3BrD,GAAG,EAAE,KAAK;QACVe,EAAE,EAAEA,EAAE;QACN9F,IAAI,EAAEmI,GAAG,CAACnI,IAAI;QACdqE,MAAM,EAAE8D,GAAG,CAAC9D;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,C;;;;;;;;;;;ACzMArG,MAAM,CAACoC,MAAM,CAAC;EAACiI,kBAAkB,EAACA,CAAA,KAAIA;AAAkB,CAAC,CAAC;AAAC,IAAIC,OAAO;AAACtK,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;EAACqK,OAAOA,CAACnK,CAAC,EAAC;IAACmK,OAAO,GAACnK,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIoK,YAAY;AAACvK,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;EAACsK,YAAYA,CAACpK,CAAC,EAAC;IAACoK,YAAY,GAACpK,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIqK,MAAM;AAACxK,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAACuK,MAAMA,CAACrK,CAAC,EAAC;IAACqK,MAAM,GAACrK,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsK,OAAO;AAACzK,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAACwK,OAAOA,CAACtK,CAAC,EAAC;IAACsK,OAAO,GAACtK,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAKxW,MAAMkK,kBAAkB,CAAC;EAC9B1D,WAAWA,CAAC9E,UAAU,EAAE;IACtB,IAAI,CAAC+E,WAAW,GAAG/E,UAAU;EAC/B;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAM6I,cAAcA,CAAC3D,GAAG,EAAE4D,OAAO,EAAE;IACjC,MAAMC,IAAI,GAAG,IAAI,CAAChE,WAAW;IAC7B,MAAMkB,EAAE,GAAGwC,OAAO,CAACO,OAAO,CAAC9D,GAAG,CAACe,EAAE,CAAC;IAClC,MAAMgD,SAAS,GAAGF,IAAI,CAACG,aAAa,CAAChE,GAAG,CAACiE,UAAU,EAAElD,EAAE,CAAC;IAExD,IAAIgD,SAAS,EAAE;MACb;MACA,MAAMG,UAAU,GAAGH,SAAS,CAACI,QAAQ,KAAKnI,SAAS;MAEnD+H,SAAS,CAACI,QAAQ,GAAGnE,GAAG,CAACoE,MAAM,IAAI1K,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACtDoK,SAAS,CAACI,QAAQ,CAACE,GAAG,GAAGtD,EAAE;MAE3B,IAAI8C,IAAI,CAACS,YAAY,EAAE;QACrB;QACA;QACA;QACA;QACA,MAAMC,UAAU,GAAG,MAAMV,IAAI,CAACW,OAAO,CAACxE,GAAG,CAACiE,UAAU,CAAC,CAACQ,MAAM,CAACzE,GAAG,CAACe,EAAE,CAAC;QACpE,IAAIwD,UAAU,KAAKvI,SAAS,EAAEgE,GAAG,CAACoE,MAAM,GAAGG,UAAU;QAErDV,IAAI,CAACa,WAAW,CAACd,OAAO,EAAE5D,GAAG,CAACiE,UAAU,EAAEjE,GAAG,CAAC;MAChD,CAAC,MAAM,IAAIkE,UAAU,EAAE;QACrB,MAAM,IAAItB,KAAK,CAAC,mCAAmC,GAAG5C,GAAG,CAACe,EAAE,CAAC;MAC/D;IACF,CAAC,MAAM;MACL8C,IAAI,CAACa,WAAW,CAACd,OAAO,EAAE5D,GAAG,CAACiE,UAAU,EAAEjE,GAAG,CAAC;IAChD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE2E,gBAAgBA,CAAC3E,GAAG,EAAE4D,OAAO,EAAE;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAAChE,WAAW;IAC7B,MAAMkE,SAAS,GAAGF,IAAI,CAACG,aAAa,CAAChE,GAAG,CAACiE,UAAU,EAAEV,OAAO,CAACO,OAAO,CAAC9D,GAAG,CAACe,EAAE,CAAC,CAAC;IAE7E,IAAIgD,SAAS,EAAE;MACb,IAAIA,SAAS,CAACI,QAAQ,KAAKnI,SAAS,EAAE;QACpC,MAAM,IAAI4G,KAAK,CAAC,0CAA0C,GAAG5C,GAAG,CAACe,EAAE,CAAC;MACtE;MACAyC,YAAY,CAACoB,YAAY,CAACb,SAAS,CAACI,QAAQ,EAAEnE,GAAG,CAACoE,MAAM,CAAC;IAC3D,CAAC,MAAM;MACLP,IAAI,CAACa,WAAW,CAACd,OAAO,EAAE5D,GAAG,CAACiE,UAAU,EAAEjE,GAAG,CAAC;IAChD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE6E,gBAAgBA,CAAC7E,GAAG,EAAE4D,OAAO,EAAE;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAAChE,WAAW;IAC7B,MAAMkE,SAAS,GAAGF,IAAI,CAACG,aAAa,CAAChE,GAAG,CAACiE,UAAU,EAAEV,OAAO,CAACO,OAAO,CAAC9D,GAAG,CAACe,EAAE,CAAC,CAAC;IAE7E,IAAIgD,SAAS,EAAE;MACb;MACA,IAAIA,SAAS,CAACI,QAAQ,KAAKnI,SAAS,EAAE;QACpC,MAAM,IAAI4G,KAAK,CAAC,yCAAyC,GAAG5C,GAAG,CAACe,EAAE,CAAC;MACrE;MACAgD,SAAS,CAACI,QAAQ,GAAGnI,SAAS;IAChC,CAAC,MAAM;MACL6H,IAAI,CAACa,WAAW,CAACd,OAAO,EAAE5D,GAAG,CAACiE,UAAU,EAAE;QACxCjE,GAAG,EAAE,SAAS;QACdiE,UAAU,EAAEjE,GAAG,CAACiE,UAAU;QAC1BlD,EAAE,EAAEf,GAAG,CAACe;MACV,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE+D,cAAcA,CAAC9E,GAAG,EAAE4D,OAAO,EAAE;IAC3B,MAAMC,IAAI,GAAG,IAAI,CAAChE,WAAW;;IAE7B;IACA;IACA;IACAG,GAAG,CAAC+E,IAAI,CAAC/J,OAAO,CAAEgK,KAAK,IAAK;MAC1BnB,IAAI,CAACoB,+BAA+B,CAAC,MAAM;QACzC,MAAMC,SAAS,GAAGrB,IAAI,CAACX,cAAc,CAAC8B,KAAK,CAAC;QAC5C;QACA,IAAI,CAACE,SAAS,EAAE;QAChB;QACA,IAAIA,SAAS,CAACC,KAAK,EAAE;QACrBD,SAAS,CAACC,KAAK,GAAG,IAAI;QACtBD,SAAS,CAACE,aAAa,IAAIF,SAAS,CAACE,aAAa,CAAC,CAAC;QACpDF,SAAS,CAACG,SAAS,CAACC,OAAO,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,gBAAgBA,CAACvF,GAAG,EAAE4D,OAAO,EAAE;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAAChE,WAAW;IAC7B;IACAG,GAAG,CAACuC,OAAO,CAACvH,OAAO,CAAEwK,QAAQ,IAAK;MAChC,MAAMC,IAAI,GAAG5B,IAAI,CAAC6B,uBAAuB,CAACF,QAAQ,CAAC,IAAI,CAAC,CAAC;MACzD9L,MAAM,CAACoJ,MAAM,CAAC2C,IAAI,CAAC,CAACzK,OAAO,CAAE2K,OAAO,IAAK;QACvC,MAAM5B,SAAS,GAAGF,IAAI,CAACG,aAAa,CAAC2B,OAAO,CAAC1B,UAAU,EAAE0B,OAAO,CAAC5E,EAAE,CAAC;QACpE,IAAI,CAACgD,SAAS,EAAE;UACd,MAAM,IAAInB,KAAK,CAAC,qBAAqB,GAAGgD,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,CAAC;QAClE;QACA,IAAI,CAAC5B,SAAS,CAAC+B,cAAc,CAACN,QAAQ,CAAC,EAAE;UACvC,MAAM,IAAI5C,KAAK,CACb,MAAM,GACNgD,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,GACvB,yBAAyB,GACzBH,QACF,CAAC;QACH;QACA,OAAOzB,SAAS,CAAC+B,cAAc,CAACN,QAAQ,CAAC;QACzC,IAAI9B,OAAO,CAACK,SAAS,CAAC+B,cAAc,CAAC,EAAE;UACrC;UACA;UACA;UACA;;UAEA;UACA;UACA;UACAjC,IAAI,CAACa,WAAW,CAACd,OAAO,EAAE+B,OAAO,CAAC1B,UAAU,EAAE;YAC5CjE,GAAG,EAAE,SAAS;YACde,EAAE,EAAEwC,OAAO,CAACwC,WAAW,CAACJ,OAAO,CAAC5E,EAAE,CAAC;YACnCiF,OAAO,EAAEjC,SAAS,CAACI;UACrB,CAAC,CAAC;UACF;UACAJ,SAAS,CAACkC,cAAc,CAACjL,OAAO,CAAEkL,CAAC,IAAK;YACtCA,CAAC,CAAC,CAAC;UACL,CAAC,CAAC;;UAEF;UACA;UACA;UACArC,IAAI,CAACsC,gBAAgB,CAACR,OAAO,CAAC1B,UAAU,CAAC,CAACmC,MAAM,CAACT,OAAO,CAAC5E,EAAE,CAAC;QAC9D;MACF,CAAC,CAAC;MACF,OAAO8C,IAAI,CAAC6B,uBAAuB,CAACF,QAAQ,CAAC;;MAE7C;MACA;MACA,MAAMa,eAAe,GAAGxC,IAAI,CAACd,eAAe,CAACyC,QAAQ,CAAC;MACtD,IAAI,CAACa,eAAe,EAAE;QACpB,MAAM,IAAIzD,KAAK,CAAC,iCAAiC,GAAG4C,QAAQ,CAAC;MAC/D;MAEA3B,IAAI,CAACoB,+BAA+B,CAClC;QAAA,OAAaoB,eAAe,CAACC,WAAW,CAAC,GAAAxK,SAAO,CAAC;MAAA,CACnD,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE4I,WAAWA,CAACd,OAAO,EAAEK,UAAU,EAAEjE,GAAG,EAAE;IACpC,IAAI,CAACyD,MAAM,CAAC3E,IAAI,CAAC8E,OAAO,EAAEK,UAAU,CAAC,EAAE;MACrCL,OAAO,CAACK,UAAU,CAAC,GAAG,EAAE;IAC1B;IACAL,OAAO,CAACK,UAAU,CAAC,CAACsC,IAAI,CAACvG,GAAG,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEgE,aAAaA,CAACC,UAAU,EAAElD,EAAE,EAAE;IAC5B,MAAM8C,IAAI,GAAG,IAAI,CAAChE,WAAW;IAC7B,IAAI,CAAC4D,MAAM,CAAC3E,IAAI,CAAC+E,IAAI,CAACsC,gBAAgB,EAAElC,UAAU,CAAC,EAAE;MACnD,OAAO,IAAI;IACb;IACA,MAAMuC,uBAAuB,GAAG3C,IAAI,CAACsC,gBAAgB,CAAClC,UAAU,CAAC;IACjE,OAAOuC,uBAAuB,CAAClJ,GAAG,CAACyD,EAAE,CAAC,IAAI,IAAI;EAChD;AACF,C;;;;;;;;;;;;;AC7MA,IAAI0F,wBAAwB;AAACxN,MAAM,CAACC,IAAI,CAAC,gDAAgD,EAAC;EAACkC,OAAOA,CAAChC,CAAC,EAAC;IAACqN,wBAAwB,GAACrN,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI+B,aAAa;AAAClC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAOA,CAAChC,CAAC,EAAC;IAAC+B,aAAa,GAAC/B,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA3OH,MAAM,CAACoC,MAAM,CAAC;EAACC,UAAU,EAACA,CAAA,KAAIA;AAAU,CAAC,CAAC;AAAC,IAAIjC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAMA,CAACD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIuG,SAAS;AAAC1G,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACyG,SAASA,CAACvG,CAAC,EAAC;IAACuG,SAAS,GAACvG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsN,OAAO;AAACzN,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACwN,OAAOA,CAACtN,CAAC,EAAC;IAACsN,OAAO,GAACtN,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIuN,KAAK;AAAC1N,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACyN,KAAKA,CAACvN,CAAC,EAAC;IAACuN,KAAK,GAACvN,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIwN,MAAM;AAAC3N,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAAC0N,MAAMA,CAACxN,CAAC,EAAC;IAACwN,MAAM,GAACxN,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAImK,OAAO;AAACtK,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;EAACqK,OAAOA,CAACnK,CAAC,EAAC;IAACmK,OAAO,GAACnK,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAID,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACC,GAAGA,CAACC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIyN,aAAa;AAAC5N,MAAM,CAACC,IAAI,CAAC,kBAAkB,EAAC;EAAC2N,aAAaA,CAACzN,CAAC,EAAC;IAACyN,aAAa,GAACzN,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIqK,MAAM,EAACqD,KAAK,EAACxG,IAAI,EAACoD,OAAO,EAACqD,IAAI;AAAC9N,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAACuK,MAAMA,CAACrK,CAAC,EAAC;IAACqK,MAAM,GAACrK,CAAC;EAAA,CAAC;EAAC0N,KAAKA,CAAC1N,CAAC,EAAC;IAAC0N,KAAK,GAAC1N,CAAC;EAAA,CAAC;EAACkH,IAAIA,CAAClH,CAAC,EAAC;IAACkH,IAAI,GAAClH,CAAC;EAAA,CAAC;EAACsK,OAAOA,CAACtK,CAAC,EAAC;IAACsK,OAAO,GAACtK,CAAC;EAAA,CAAC;EAAC2N,IAAIA,CAAC3N,CAAC,EAAC;IAAC2N,IAAI,GAAC3N,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsG,wBAAwB;AAACzG,MAAM,CAACC,IAAI,CAAC,8BAA8B,EAAC;EAACwG,wBAAwBA,CAACtG,CAAC,EAAC;IAACsG,wBAAwB,GAACtG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI4N,UAAU;AAAC/N,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAAC8N,UAAUA,CAAC5N,CAAC,EAAC;IAAC4N,UAAU,GAAC5N,CAAC;EAAA;AAAC,CAAC,EAAC,EAAE,CAAC;AAAC,IAAI6N,iBAAiB;AAAChO,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;EAAC+N,iBAAiBA,CAAC7N,CAAC,EAAC;IAAC6N,iBAAiB,GAAC7N,CAAC;EAAA;AAAC,CAAC,EAAC,EAAE,CAAC;AAAC,IAAIkK,kBAAkB;AAACrK,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAACoK,kBAAkBA,CAAClK,CAAC,EAAC;IAACkK,kBAAkB,GAAClK,CAAC;EAAA;AAAC,CAAC,EAAC,EAAE,CAAC;AAwCxpC,MAAMkC,UAAU,CAAC;EACtBsE,WAAWA,CAACsH,GAAG,EAAEzI,OAAO,EAAE;IACxB,MAAMoF,IAAI,GAAG,IAAI;IAEjB,IAAI,CAACpF,OAAO,GAAGA,OAAO,GAAAtD,aAAA;MACpByF,WAAWA,CAAA,EAAG,CAAC,CAAC;MAChB5G,8BAA8BA,CAACC,WAAW,EAAE;QAC1CZ,MAAM,CAACa,MAAM,CAACD,WAAW,CAAC;MAC5B,CAAC;MACDkN,iBAAiB,EAAE,KAAK;MACxBC,gBAAgB,EAAE,KAAK;MACvBC,cAAc,EAAE3N,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACnC;MACA2N,qBAAqB,EAAE,KAAK;MAC5BC,oBAAoB,EAAE5H,SAAS,CAAC6H,sBAAsB;MACtD1N,KAAK,EAAE,IAAI;MACXgH,cAAc,EAAE,IAAI;MACpB;MACA2G,sBAAsB,EAAE,CAAC;MACzB;MACAC,oBAAoB,EAAE;IAAG,GAEtBjJ,OAAO,CACX;;IAED;IACA;IACA;IACA;IACA;IACAoF,IAAI,CAAC8D,WAAW,GAAG,IAAI;;IAEvB;IACA,IAAI,OAAOT,GAAG,KAAK,QAAQ,EAAE;MAC3BrD,IAAI,CAAClF,OAAO,GAAGuI,GAAG;IACpB,CAAC,MAAM;MA3EX,IAAIU,YAAY;MAAC3O,MAAM,CAACC,IAAI,CAAC,6BAA6B,EAAC;QAAC0O,YAAYA,CAACxO,CAAC,EAAC;UAACwO,YAAY,GAACxO,CAAC;QAAA;MAAC,CAAC,EAAC,EAAE,CAAC;MA8E1FyK,IAAI,CAAClF,OAAO,GAAG,IAAIiJ,YAAY,CAACV,GAAG,EAAE;QACnCpN,KAAK,EAAE2E,OAAO,CAAC3E,KAAK;QACpB+N,eAAe,EAAE1O,GAAG,CAAC0O,eAAe;QACpCC,OAAO,EAAErJ,OAAO,CAACqJ,OAAO;QACxBC,cAAc,EAAEtJ,OAAO,CAACsJ,cAAc;QACtC;QACA;QACA;QACA;QACA;QACAC,gBAAgB,EAAEvJ,OAAO,CAACuJ,gBAAgB;QAC1CC,gBAAgB,EAAExJ,OAAO,CAACwJ,gBAAgB;QAC1CZ,cAAc,EAAE5I,OAAO,CAAC4I;MAC1B,CAAC,CAAC;IACJ;IAEAxD,IAAI,CAAC5B,cAAc,GAAG,IAAI;IAC1B4B,IAAI,CAACpD,kBAAkB,GAAG,IAAI,CAAC,CAAC;IAChCoD,IAAI,CAACrD,QAAQ,GAAG,IAAI,CAAC,CAAC;IACtBqD,IAAI,CAACW,OAAO,GAAG9K,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACpCkK,IAAI,CAACqE,eAAe,GAAGxO,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5CkK,IAAI,CAACsE,aAAa,GAAG,CAAC;IACtBtE,IAAI,CAACzC,qBAAqB,GAAG3C,OAAO,CAAC8I,oBAAoB;IAEzD1D,IAAI,CAACuE,kBAAkB,GAAG3J,OAAO,CAAC0I,iBAAiB;IACnDtD,IAAI,CAACwE,iBAAiB,GAAG5J,OAAO,CAAC2I,gBAAgB;;IAEjD;IACA;IACA;IACA;IACAvD,IAAI,CAACd,eAAe,GAAGrJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;;IAE1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAkK,IAAI,CAACxB,wBAAwB,GAAG,EAAE;;IAElC;IACA;IACA;IACA;IACAwB,IAAI,CAAC6B,uBAAuB,GAAG,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA7B,IAAI,CAACsC,gBAAgB,GAAG,CAAC,CAAC;;IAE1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAtC,IAAI,CAACyE,qBAAqB,GAAG,EAAE;;IAE/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAzE,IAAI,CAAC0E,gCAAgC,GAAG,EAAE;IAC1C;IACA;IACA;IACA1E,IAAI,CAAC2E,0BAA0B,GAAG,CAAC,CAAC;IACpC;IACA;IACA3E,IAAI,CAAC4E,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA;IACA5E,IAAI,CAACS,YAAY,GAAG,KAAK;;IAEzB;IACAT,IAAI,CAAC6E,wBAAwB,GAAG,CAAC,CAAC;IAClC;IACA7E,IAAI,CAAC8E,aAAa,GAAG,IAAI;IACzB;IACA9E,IAAI,CAAC+E,eAAe,GAAG,CAAC,CAAC;IACzB;IACA/E,IAAI,CAACgF,sBAAsB,GAAG,IAAI;IAClC;IACAhF,IAAI,CAACiF,0BAA0B,GAAG,IAAI;IAEtCjF,IAAI,CAACkF,uBAAuB,GAAGtK,OAAO,CAACgJ,sBAAsB;IAC7D5D,IAAI,CAACmF,qBAAqB,GAAGvK,OAAO,CAACiJ,oBAAoB;;IAEzD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA7D,IAAI,CAACX,cAAc,GAAG,CAAC,CAAC;;IAExB;IACAW,IAAI,CAACoF,OAAO,GAAG,IAAI;IACnBpF,IAAI,CAACqF,WAAW,GAAG,IAAIxC,OAAO,CAACyC,UAAU,CAAC,CAAC;;IAE3C;IACA,IAAI9P,MAAM,CAAC+P,QAAQ,IACjBjP,OAAO,CAACC,MAAM,IACd,CAAEqE,OAAO,CAAC6I,qBAAqB,EAAE;MACjCnN,OAAO,CAACC,MAAM,CAACE,MAAM,CAACI,UAAU,CAACZ,KAAK,IAAI;QACxC,IAAI,CAAE+J,IAAI,CAAChH,eAAe,CAAC,CAAC,EAAE;UAC5BgH,IAAI,CAAC8E,aAAa,GAAG7O,KAAK;UAC1B,OAAO,CAAC,KAAK,CAAC;QAChB,CAAC,MAAM;UACL,OAAO,CAAC,IAAI,CAAC;QACf;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,CAACuP,eAAe,GAAG,IAAI3J,wBAAwB,CAAC,IAAI,CAAC;IAEzD,MAAM4J,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,IAAI,CAACnJ,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACoJ,IAAI,CAAC,CAAC;QACtB,IAAI,CAACpJ,UAAU,GAAG,IAAI;MACxB;IACF,CAAC;IAED,IAAI9G,MAAM,CAACmQ,QAAQ,EAAE;MACnB,IAAI,CAAC7K,OAAO,CAAC8K,EAAE,CACb,SAAS,EACTpQ,MAAM,CAACqQ,eAAe,CACpB1J,GAAG,IAAI,IAAI,CAACqJ,eAAe,CAACvJ,SAAS,CAACE,GAAG,CAAC,EAC1C,sBACF,CACF,CAAC;MACD,IAAI,CAACrB,OAAO,CAAC8K,EAAE,CACb,OAAO,EACPpQ,MAAM,CAACqQ,eAAe,CACpB,MAAM,IAAI,CAACL,eAAe,CAACzH,OAAO,CAAC,CAAC,EACpC,oBACF,CACF,CAAC;MACD,IAAI,CAACjD,OAAO,CAAC8K,EAAE,CACb,YAAY,EACZpQ,MAAM,CAACqQ,eAAe,CAACJ,YAAY,EAAE,yBAAyB,CAChE,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAAC3K,OAAO,CAAC8K,EAAE,CAAC,SAAS,EAAEzJ,GAAG,IAAI,IAAI,CAACqJ,eAAe,CAACvJ,SAAS,CAACE,GAAG,CAAC,CAAC;MACtE,IAAI,CAACrB,OAAO,CAAC8K,EAAE,CAAC,OAAO,EAAE,MAAM,IAAI,CAACJ,eAAe,CAACzH,OAAO,CAAC,CAAC,CAAC;MAC9D,IAAI,CAACjD,OAAO,CAAC8K,EAAE,CAAC,YAAY,EAAEH,YAAY,CAAC;IAC7C;IAEA,IAAI,CAACK,kBAAkB,GAAG,IAAI1C,iBAAiB,CAAC,IAAI,CAAC;;IAErD;IACA,IAAI,CAACtG,mBAAmB,GAAIX,GAAG,IAAK,IAAI,CAAC2J,kBAAkB,CAAChJ,mBAAmB,CAACX,GAAG,CAAC;IACpF,IAAI,CAACgB,cAAc,GAAIhB,GAAG,IAAK,IAAI,CAAC2J,kBAAkB,CAAC3I,cAAc,CAAChB,GAAG,CAAC;IAC1E,IAAI,CAACiB,eAAe,GAAIjB,GAAG,IAAK,IAAI,CAAC2J,kBAAkB,CAAC1I,eAAe,CAACjB,GAAG,CAAC;IAC5E,IAAI,CAACkB,gBAAgB,GAAIlB,GAAG,IAAK,IAAI,CAAC2J,kBAAkB,CAACzI,gBAAgB,CAAClB,GAAG,CAAC;IAC9E,IAAI,CAACmB,eAAe,GAAInB,GAAG,IAAK,IAAI,CAAC2J,kBAAkB,CAACxI,eAAe,CAACnB,GAAG,CAAC;IAE5E,IAAI,CAAC4J,mBAAmB,GAAG,IAAItG,kBAAkB,CAAC,IAAI,CAAC;;IAEvD;IACA,IAAI,CAACK,cAAc,GAAG,CAAC3D,GAAG,EAAE4D,OAAO,KAAK,IAAI,CAACgG,mBAAmB,CAACjG,cAAc,CAAC3D,GAAG,EAAE4D,OAAO,CAAC;IAC7F,IAAI,CAACe,gBAAgB,GAAG,CAAC3E,GAAG,EAAE4D,OAAO,KAAK,IAAI,CAACgG,mBAAmB,CAACjF,gBAAgB,CAAC3E,GAAG,EAAE4D,OAAO,CAAC;IACjG,IAAI,CAACiB,gBAAgB,GAAG,CAAC7E,GAAG,EAAE4D,OAAO,KAAK,IAAI,CAACgG,mBAAmB,CAAC/E,gBAAgB,CAAC7E,GAAG,EAAE4D,OAAO,CAAC;IACjG,IAAI,CAACkB,cAAc,GAAG,CAAC9E,GAAG,EAAE4D,OAAO,KAAK,IAAI,CAACgG,mBAAmB,CAAC9E,cAAc,CAAC9E,GAAG,EAAE4D,OAAO,CAAC;IAC7F,IAAI,CAAC2B,gBAAgB,GAAG,CAACvF,GAAG,EAAE4D,OAAO,KAAK,IAAI,CAACgG,mBAAmB,CAACrE,gBAAgB,CAACvF,GAAG,EAAE4D,OAAO,CAAC;;IAEjG;IACA,IAAI,CAACc,WAAW,GAAG,CAACd,OAAO,EAAEK,UAAU,EAAEjE,GAAG,KAC1C,IAAI,CAAC4J,mBAAmB,CAAClF,WAAW,CAACd,OAAO,EAAEK,UAAU,EAAEjE,GAAG,CAAC;IAChE,IAAI,CAACgE,aAAa,GAAG,CAACC,UAAU,EAAElD,EAAE,KAClC,IAAI,CAAC6I,mBAAmB,CAAC5F,aAAa,CAACC,UAAU,EAAElD,EAAE,CAAC;EAC1D;;EAEA;EACA;EACA;EACA8I,kBAAkBA,CAAC5O,IAAI,EAAE6O,YAAY,EAAE;IACrC,MAAMjG,IAAI,GAAG,IAAI;IAEjB,IAAI5I,IAAI,IAAI4I,IAAI,CAACW,OAAO,EAAE,OAAO,KAAK;;IAEtC;IACA;IACA,MAAMuF,KAAK,GAAGrQ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjC,MAAMqQ,WAAW,GAAG,CAClB,QAAQ,EACR,aAAa,EACb,WAAW,EACX,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,gBAAgB,CACjB;IACDA,WAAW,CAAChP,OAAO,CAAEiP,MAAM,IAAK;MAC9BF,KAAK,CAACE,MAAM,CAAC,GAAG,YAAa;QAC3B,IAAIH,YAAY,CAACG,MAAM,CAAC,EAAE;UACxB,OAAOH,YAAY,CAACG,MAAM,CAAC,CAAC,GAAAnO,SAAO,CAAC;QACtC;MACF,CAAC;IACH,CAAC,CAAC;IACF+H,IAAI,CAACW,OAAO,CAACvJ,IAAI,CAAC,GAAG8O,KAAK;IAC1B,OAAOA,KAAK;EACd;EAEAG,mBAAmBA,CAACjP,IAAI,EAAE6O,YAAY,EAAE;IACtC,MAAMjG,IAAI,GAAG,IAAI;IAEjB,MAAMkG,KAAK,GAAGlG,IAAI,CAACgG,kBAAkB,CAAC5O,IAAI,EAAE6O,YAAY,CAAC;IAEzD,MAAMK,MAAM,GAAGtG,IAAI,CAAC6E,wBAAwB,CAACzN,IAAI,CAAC;IAClD,IAAImP,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzBJ,KAAK,CAACO,WAAW,CAACH,MAAM,CAACpO,MAAM,EAAE,KAAK,CAAC;MACvCoO,MAAM,CAACnP,OAAO,CAACgF,GAAG,IAAI;QACpB+J,KAAK,CAACQ,MAAM,CAACvK,GAAG,CAAC;MACnB,CAAC,CAAC;MACF+J,KAAK,CAACS,SAAS,CAAC,CAAC;MACjB,OAAO3G,IAAI,CAAC6E,wBAAwB,CAACzN,IAAI,CAAC;IAC5C;IAEA,OAAO,IAAI;EACb;EACA,MAAMwP,mBAAmBA,CAACxP,IAAI,EAAE6O,YAAY,EAAE;IAC5C,MAAMjG,IAAI,GAAG,IAAI;IAEjB,MAAMkG,KAAK,GAAGlG,IAAI,CAACgG,kBAAkB,CAAC5O,IAAI,EAAE6O,YAAY,CAAC;IAEzD,MAAMK,MAAM,GAAGtG,IAAI,CAAC6E,wBAAwB,CAACzN,IAAI,CAAC;IAClD,IAAImP,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzB,MAAMJ,KAAK,CAACO,WAAW,CAACH,MAAM,CAACpO,MAAM,EAAE,KAAK,CAAC;MAC7C,KAAK,MAAMiE,GAAG,IAAImK,MAAM,EAAE;QACxB,MAAMJ,KAAK,CAACQ,MAAM,CAACvK,GAAG,CAAC;MACzB;MACA,MAAM+J,KAAK,CAACS,SAAS,CAAC,CAAC;MACvB,OAAO3G,IAAI,CAAC6E,wBAAwB,CAACzN,IAAI,CAAC;IAC5C;IAEA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkE,SAASA,CAAClE,IAAI,CAAC,8CAA8C;IAC3D,MAAM4I,IAAI,GAAG,IAAI;IAEjB,MAAMvE,MAAM,GAAGwH,KAAK,CAAChI,IAAI,CAAChD,SAAS,EAAE,CAAC,CAAC;IACvC,IAAI4O,SAAS,GAAGhR,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACnC,IAAI2F,MAAM,CAACvD,MAAM,EAAE;MACjB,MAAM4O,SAAS,GAAGrL,MAAM,CAACA,MAAM,CAACvD,MAAM,GAAG,CAAC,CAAC;MAC3C,IAAI,OAAO4O,SAAS,KAAK,UAAU,EAAE;QACnCD,SAAS,CAACE,OAAO,GAAGtL,MAAM,CAACuL,GAAG,CAAC,CAAC;MAClC,CAAC,MAAM,IAAIF,SAAS,IAAI,CACtBA,SAAS,CAACC,OAAO;MACjB;MACA;MACAD,SAAS,CAACG,OAAO,EACjBH,SAAS,CAACI,MAAM,CACjB,CAACC,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,CAAC,EAAE;QACpCP,SAAS,GAAGpL,MAAM,CAACuL,GAAG,CAAC,CAAC;MAC1B;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMK,QAAQ,GAAGxR,MAAM,CAACoJ,MAAM,CAACe,IAAI,CAACX,cAAc,CAAC,CAACiI,IAAI,CACtD/H,GAAG,IAAKA,GAAG,CAACgI,QAAQ,IAAIhI,GAAG,CAACnI,IAAI,KAAKA,IAAI,IAAI0L,KAAK,CAAC0E,MAAM,CAACjI,GAAG,CAAC9D,MAAM,EAAEA,MAAM,CAC9E,CAAC;IAED,IAAIyB,EAAE;IACN,IAAImK,QAAQ,EAAE;MACZnK,EAAE,GAAGmK,QAAQ,CAACnK,EAAE;MAChBmK,QAAQ,CAACE,QAAQ,GAAG,KAAK,CAAC,CAAC;;MAE3B,IAAIV,SAAS,CAACE,OAAO,EAAE;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIM,QAAQ,CAAC/F,KAAK,EAAE;UAClBuF,SAAS,CAACE,OAAO,CAAC,CAAC;QACrB,CAAC,MAAM;UACLM,QAAQ,CAAC9F,aAAa,GAAGsF,SAAS,CAACE,OAAO;QAC5C;MACF;;MAEA;MACA;MACA,IAAIF,SAAS,CAACI,OAAO,EAAE;QACrB;QACA;QACAI,QAAQ,CAACI,aAAa,GAAGZ,SAAS,CAACI,OAAO;MAC5C;MAEA,IAAIJ,SAAS,CAACK,MAAM,EAAE;QACpBG,QAAQ,CAACK,YAAY,GAAGb,SAAS,CAACK,MAAM;MAC1C;IACF,CAAC,MAAM;MACL;MACAhK,EAAE,GAAG6F,MAAM,CAAC7F,EAAE,CAAC,CAAC;MAChB8C,IAAI,CAACX,cAAc,CAACnC,EAAE,CAAC,GAAG;QACxBA,EAAE,EAAEA,EAAE;QACN9F,IAAI,EAAEA,IAAI;QACVqE,MAAM,EAAEqH,KAAK,CAAC6E,KAAK,CAAClM,MAAM,CAAC;QAC3B8L,QAAQ,EAAE,KAAK;QACfjG,KAAK,EAAE,KAAK;QACZE,SAAS,EAAE,IAAIqB,OAAO,CAACyC,UAAU,CAAC,CAAC;QACnC/D,aAAa,EAAEsF,SAAS,CAACE,OAAO;QAChC;QACAU,aAAa,EAAEZ,SAAS,CAACI,OAAO;QAChCS,YAAY,EAAEb,SAAS,CAACK,MAAM;QAC9BjQ,UAAU,EAAE+I,IAAI;QAChBuC,MAAMA,CAAA,EAAG;UACP,OAAO,IAAI,CAACtL,UAAU,CAACoI,cAAc,CAAC,IAAI,CAACnC,EAAE,CAAC;UAC9C,IAAI,CAACoE,KAAK,IAAI,IAAI,CAACE,SAAS,CAACC,OAAO,CAAC,CAAC;QACxC,CAAC;QACDiE,IAAIA,CAAA,EAAG;UACL,IAAI,CAACzO,UAAU,CAACuI,WAAW,CAAC;YAAErD,GAAG,EAAE,OAAO;YAAEe,EAAE,EAAEA;UAAG,CAAC,CAAC;UACrD,IAAI,CAACqF,MAAM,CAAC,CAAC;UAEb,IAAIsE,SAAS,CAACK,MAAM,EAAE;YACpBL,SAAS,CAACK,MAAM,CAAC,CAAC;UACpB;QACF;MACF,CAAC;MACDlH,IAAI,CAACxE,KAAK,CAAC;QAAEW,GAAG,EAAE,KAAK;QAAEe,EAAE,EAAEA,EAAE;QAAE9F,IAAI,EAAEA,IAAI;QAAEqE,MAAM,EAAEA;MAAO,CAAC,CAAC;IAChE;;IAEA;IACA,MAAMmM,MAAM,GAAG;MACblC,IAAIA,CAAA,EAAG;QACL,IAAI,CAAE9F,MAAM,CAAC3E,IAAI,CAAC+E,IAAI,CAACX,cAAc,EAAEnC,EAAE,CAAC,EAAE;UAC1C;QACF;QACA8C,IAAI,CAACX,cAAc,CAACnC,EAAE,CAAC,CAACwI,IAAI,CAAC,CAAC;MAChC,CAAC;MACDpE,KAAKA,CAAA,EAAG;QACN;QACA,IAAI,CAAC1B,MAAM,CAAC3E,IAAI,CAAC+E,IAAI,CAACX,cAAc,EAAEnC,EAAE,CAAC,EAAE;UACzC,OAAO,KAAK;QACd;QACA,MAAM2K,MAAM,GAAG7H,IAAI,CAACX,cAAc,CAACnC,EAAE,CAAC;QACtC2K,MAAM,CAACrG,SAAS,CAACsG,MAAM,CAAC,CAAC;QACzB,OAAOD,MAAM,CAACvG,KAAK;MACrB,CAAC;MACDyG,cAAc,EAAE7K;IAClB,CAAC;IAED,IAAI2F,OAAO,CAACmF,MAAM,EAAE;MAClB;MACA;MACA;MACA;MACA;MACA;MACAnF,OAAO,CAACoF,YAAY,CAAE5F,CAAC,IAAK;QAC1B,IAAIzC,MAAM,CAAC3E,IAAI,CAAC+E,IAAI,CAACX,cAAc,EAAEnC,EAAE,CAAC,EAAE;UACxC8C,IAAI,CAACX,cAAc,CAACnC,EAAE,CAAC,CAACqK,QAAQ,GAAG,IAAI;QACzC;QAEA1E,OAAO,CAACqF,UAAU,CAAC,MAAM;UACvB,IAAItI,MAAM,CAAC3E,IAAI,CAAC+E,IAAI,CAACX,cAAc,EAAEnC,EAAE,CAAC,IACpC8C,IAAI,CAACX,cAAc,CAACnC,EAAE,CAAC,CAACqK,QAAQ,EAAE;YACpCK,MAAM,CAAClC,IAAI,CAAC,CAAC;UACf;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAOkC,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEO,WAAWA,CAAA,EAAE;IACX,OAAO7S,GAAG,CAACgE,wBAAwB,CAAC8O,yBAAyB,CAAC,CAAC;EACjE;EACA1J,OAAOA,CAACA,OAAO,EAAE;IACf7I,MAAM,CAACuJ,OAAO,CAACV,OAAO,CAAC,CAACvH,OAAO,CAACmI,IAAA,IAAkB;MAAA,IAAjB,CAAClI,IAAI,EAAEiR,IAAI,CAAC,GAAA/I,IAAA;MAC3C,IAAI,OAAO+I,IAAI,KAAK,UAAU,EAAE;QAC9B,MAAM,IAAItJ,KAAK,CAAC,UAAU,GAAG3H,IAAI,GAAG,sBAAsB,CAAC;MAC7D;MACA,IAAI,IAAI,CAACiN,eAAe,CAACjN,IAAI,CAAC,EAAE;QAC9B,MAAM,IAAI2H,KAAK,CAAC,kBAAkB,GAAG3H,IAAI,GAAG,sBAAsB,CAAC;MACrE;MACA,IAAI,CAACiN,eAAe,CAACjN,IAAI,CAAC,GAAGiR,IAAI;IACnC,CAAC,CAAC;EACJ;EAEAvO,gBAAgBA,CAAAwO,KAAA,EAAyC;IAAA,IAAxC;MAAC1O,eAAe;MAAEF;IAAmB,CAAC,GAAA4O,KAAA;IACrD,IAAI,CAAC1O,eAAe,EAAE;MACpB,OAAOF,mBAAmB;IAC5B;IACA,OAAOA,mBAAmB,IAAIpE,GAAG,CAACgE,wBAAwB,CAAC8O,yBAAyB,CAAC,CAAC;EACxF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEnN,IAAIA,CAAC7D,IAAI,CAAC,kCAAkC;IAC1C;IACA;IACA,MAAMiC,IAAI,GAAG4J,KAAK,CAAChI,IAAI,CAAChD,SAAS,EAAE,CAAC,CAAC;IACrC,IAAI4C,QAAQ;IACZ,IAAIxB,IAAI,CAACnB,MAAM,IAAI,OAAOmB,IAAI,CAACA,IAAI,CAACnB,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;MAC9D2C,QAAQ,GAAGxB,IAAI,CAAC2N,GAAG,CAAC,CAAC;IACvB;IACA,OAAO,IAAI,CAAC/N,KAAK,CAAC7B,IAAI,EAAEiC,IAAI,EAAEwB,QAAQ,CAAC;EACzC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0N,SAASA,CAACnR,IAAI,CAAC,yBAAyB;IACtC,MAAMiC,IAAI,GAAG4J,KAAK,CAAChI,IAAI,CAAChD,SAAS,EAAE,CAAC,CAAC;IACrC,IAAIoB,IAAI,CAACnB,MAAM,IAAI,OAAOmB,IAAI,CAACA,IAAI,CAACnB,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;MAC9D,MAAM,IAAI6G,KAAK,CACb,+FACF,CAAC;IACH;IAEA,OAAO,IAAI,CAAC3F,UAAU,CAAChC,IAAI,EAAEiC,IAAI,EAAE;MAAEmP,yBAAyB,EAAE;IAAK,CAAC,CAAC;EACzE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvP,KAAKA,CAAC7B,IAAI,EAAEiC,IAAI,EAAEuB,OAAO,EAAEC,QAAQ,EAAE;IACnC,MAAA4N,eAAA,GAAuD,IAAI,CAACC,SAAS,CAACtR,IAAI,EAAE0L,KAAK,CAAC6E,KAAK,CAACtO,IAAI,CAAC,CAAC;MAAxF;QAAEsP,cAAc;QAAEC;MAA2B,CAAC,GAAAH,eAAA;MAAbI,WAAW,GAAAjG,wBAAA,CAAA6F,eAAA,EAAAK,SAAA;IAElD,IAAID,WAAW,CAACE,OAAO,EAAE;MACvB,IACE,CAAC,IAAI,CAACjP,gBAAgB,CAAC;QACrBJ,mBAAmB,EAAEmP,WAAW,CAACnP,mBAAmB;QACpDE,eAAe,EAAEiP,WAAW,CAACjP;MAC/B,CAAC,CAAC,EACF;QACA,IAAI,CAACoP,cAAc,CAAC,CAAC;MACvB;MACA,IAAI;QACFH,WAAW,CAACI,eAAe,GAAG3T,GAAG,CAACgE,wBAAwB,CACvD4P,SAAS,CAACN,UAAU,EAAED,cAAc,CAAC;QACxC,IAAInT,MAAM,CAAC2T,UAAU,CAACN,WAAW,CAACI,eAAe,CAAC,EAAE;UAClDzT,MAAM,CAACa,MAAM,WAAAqE,MAAA,CACDtD,IAAI,yIAChB,CAAC;QACH;MACF,CAAC,CAAC,OAAOiF,CAAC,EAAE;QACVwM,WAAW,CAACO,SAAS,GAAG/M,CAAC;MAC3B;IACF;IACA,OAAO,IAAI,CAACgN,MAAM,CAACjS,IAAI,EAAEyR,WAAW,EAAExP,IAAI,EAAEuB,OAAO,EAAEC,QAAQ,CAAC;EAChE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEzB,UAAUA,CAAChC,IAAI,EAAEiC,IAAI,EAAEuB,OAAO,EAAmB;IAAA,IAAjBC,QAAQ,GAAA5C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAC7C,MAAMS,WAAW,GAAG,IAAI,CAAC4Q,yBAAyB,CAAClS,IAAI,EAAEiC,IAAI,EAAEuB,OAAO,CAAC;IAEvE,MAAMvC,OAAO,GAAG,IAAI,CAACkR,WAAW,CAAC;MAC/BnS,IAAI;MACJiC,IAAI;MACJuB,OAAO;MACPC,QAAQ;MACRnC;IACF,CAAC,CAAC;IACF,IAAIlD,MAAM,CAAC+P,QAAQ,EAAE;MACnB;MACAlN,OAAO,CAACK,WAAW,GAAGA,WAAW,CAAC2B,IAAI,CAACmP,CAAC,IAAI;QAC1C,IAAIA,CAAC,CAACJ,SAAS,EAAE;UACf,MAAMI,CAAC,CAACJ,SAAS;QACnB;QACA,OAAOI,CAAC,CAACP,eAAe;MAC1B,CAAC,CAAC;MACF;MACA5Q,OAAO,CAACQ,aAAa,GAAG,IAAIjB,OAAO,CAAC,CAACC,OAAO,EAAEO,MAAM,KAClDC,OAAO,CAACgC,IAAI,CAACxC,OAAO,CAAC,CAACc,KAAK,CAACP,MAAM,CACpC,CAAC;IACH;IACA,OAAOC,OAAO;EAChB;EACA,MAAMiR,yBAAyBA,CAAClS,IAAI,EAAEiC,IAAI,EAAEuB,OAAO,EAAE;IACnD,MAAA6O,gBAAA,GAAuD,IAAI,CAACf,SAAS,CAACtR,IAAI,EAAE0L,KAAK,CAAC6E,KAAK,CAACtO,IAAI,CAAC,EAAEuB,OAAO,CAAC;MAAjG;QAAE+N,cAAc;QAAEC;MAA2B,CAAC,GAAAa,gBAAA;MAAbZ,WAAW,GAAAjG,wBAAA,CAAA6G,gBAAA,EAAAC,UAAA;IAClD,IAAIb,WAAW,CAACE,OAAO,EAAE;MACvB,IACE,CAAC,IAAI,CAACjP,gBAAgB,CAAC;QACrBJ,mBAAmB,EAAEmP,WAAW,CAACnP,mBAAmB;QACpDE,eAAe,EAAEiP,WAAW,CAACjP;MAC/B,CAAC,CAAC,EACF;QACA,IAAI,CAACoP,cAAc,CAAC,CAAC;MACvB;MACA,IAAI;QACF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;QACQ,MAAMW,cAAc,GAAGrU,GAAG,CAACgE,wBAAwB,CAACsQ,2BAA2B,CAC7EhB,UACF,CAAC;QACD,IAAI;UACFC,WAAW,CAACI,eAAe,GAAG,MAAMN,cAAc,CAAC,CAAC;QACtD,CAAC,CAAC,OAAOtM,CAAC,EAAE;UACVwM,WAAW,CAACO,SAAS,GAAG/M,CAAC;QAC3B,CAAC,SAAS;UACR/G,GAAG,CAACgE,wBAAwB,CAACC,IAAI,CAACoQ,cAAc,CAAC;QACnD;MACF,CAAC,CAAC,OAAOtN,CAAC,EAAE;QACVwM,WAAW,CAACO,SAAS,GAAG/M,CAAC;MAC3B;IACF;IACA,OAAOwM,WAAW;EACpB;EACA,MAAMU,WAAWA,CAAAM,KAAA,EAAiD;IAAA,IAAhD;MAAEzS,IAAI;MAAEiC,IAAI;MAAEuB,OAAO;MAAEC,QAAQ;MAAEnC;IAAY,CAAC,GAAAmR,KAAA;IAC9D,MAAMhB,WAAW,GAAG,MAAMnQ,WAAW;IACrC,OAAO,IAAI,CAAC2Q,MAAM,CAACjS,IAAI,EAAEyR,WAAW,EAAExP,IAAI,EAAEuB,OAAO,EAAEC,QAAQ,CAAC;EAChE;EAEAwO,MAAMA,CAACjS,IAAI,EAAE0S,aAAa,EAAEzQ,IAAI,EAAEuB,OAAO,EAAEC,QAAQ,EAAE;IACnD,MAAMmF,IAAI,GAAG,IAAI;;IAEjB;IACA;IACA,IAAI,CAACnF,QAAQ,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MAC9CC,QAAQ,GAAGD,OAAO;MAClBA,OAAO,GAAG/E,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/B;IACA8E,OAAO,GAAGA,OAAO,IAAI/E,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAExC,IAAI+E,QAAQ,EAAE;MACZ;MACA;MACA;MACAA,QAAQ,GAAGrF,MAAM,CAACqQ,eAAe,CAC/BhL,QAAQ,EACR,iCAAiC,GAAGzD,IAAI,GAAG,GAC7C,CAAC;IACH;IACA,MAAM;MACJ2R,OAAO;MACPK,SAAS;MACTH,eAAe;MACfvP,mBAAmB;MACnBqQ;IACF,CAAC,GAAGD,aAAa;;IAEjB;IACA;IACAzQ,IAAI,GAAGyJ,KAAK,CAAC6E,KAAK,CAACtO,IAAI,CAAC;IACxB;IACA;IACA;IACA,IACE,IAAI,CAACS,gBAAgB,CAAC;MACpBJ,mBAAmB;MACnBE,eAAe,EAAEkQ,aAAa,CAAClQ;IACjC,CAAC,CAAC,EACF;MACA,IAAIU,MAAM;MAEV,IAAIO,QAAQ,EAAE;QACZA,QAAQ,CAACuO,SAAS,EAAEH,eAAe,CAAC;MACtC,CAAC,MAAM;QACL,IAAIG,SAAS,EAAE,MAAMA,SAAS;QAC9B9O,MAAM,GAAG2O,eAAe;MAC1B;MAEA,OAAOrO,OAAO,CAACM,oBAAoB,GAAG;QAAEZ;MAAO,CAAC,GAAGA,MAAM;IAC3D;;IAEA;IACA;IACA,MAAMqH,QAAQ,GAAG,EAAE,GAAG3B,IAAI,CAACsE,aAAa,EAAE;IAC1C,IAAIyE,OAAO,EAAE;MACX/I,IAAI,CAACgK,0BAA0B,CAACrI,QAAQ,CAAC;IAC3C;;IAEA;IACA;IACA;IACA;IACA,MAAMsI,OAAO,GAAG;MACd9N,GAAG,EAAE,QAAQ;MACbe,EAAE,EAAEyE,QAAQ;MACZyE,MAAM,EAAEhP,IAAI;MACZqE,MAAM,EAAEpC;IACV,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI+P,SAAS,EAAE;MACb,IAAIxO,OAAO,CAACsP,mBAAmB,EAAE;QAC/B,MAAMd,SAAS;MACjB,CAAC,MAAM,IAAI,CAACA,SAAS,CAACe,eAAe,EAAE;QACrC3U,MAAM,CAACa,MAAM,CACX,qDAAqD,GAAGe,IAAI,GAAG,GAAG,EAClEgS,SACF,CAAC;MACH;IACF;;IAEA;IACA;;IAEA;IACA,IAAI/Q,OAAO;IACX,IAAI,CAACwC,QAAQ,EAAE;MACb,IACErF,MAAM,CAAC+P,QAAQ,IACf,CAAC3K,OAAO,CAAC4N,yBAAyB,KACjC,CAAC5N,OAAO,CAAChB,eAAe,IAAIgB,OAAO,CAACwP,eAAe,CAAC,EACrD;QACAvP,QAAQ,GAAIN,GAAG,IAAK;UAClBA,GAAG,IAAI/E,MAAM,CAACa,MAAM,CAAC,yBAAyB,GAAGe,IAAI,GAAG,GAAG,EAAEmD,GAAG,CAAC;QACnE,CAAC;MACH,CAAC,MAAM;QACLlC,OAAO,GAAG,IAAIT,OAAO,CAAC,CAACC,OAAO,EAAEO,MAAM,KAAK;UACzCyC,QAAQ,GAAG,SAAAA,CAAA,EAAgB;YAAA,SAAAwP,IAAA,GAAApS,SAAA,CAAAC,MAAA,EAAZoS,OAAO,OAAA/D,KAAA,CAAA8D,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;cAAPD,OAAO,CAAAC,IAAA,IAAAtS,SAAA,CAAAsS,IAAA;YAAA;YACpB,IAAIlR,IAAI,GAAGkN,KAAK,CAACiE,IAAI,CAACF,OAAO,CAAC;YAC9B,IAAI/P,GAAG,GAAGlB,IAAI,CAAC2F,KAAK,CAAC,CAAC;YACtB,IAAIzE,GAAG,EAAE;cACPnC,MAAM,CAACmC,GAAG,CAAC;cACX;YACF;YACA1C,OAAO,CAAC,GAAGwB,IAAI,CAAC;UAClB,CAAC;QACH,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAI0Q,UAAU,CAACU,KAAK,KAAK,IAAI,EAAE;MAC7BR,OAAO,CAACF,UAAU,GAAGA,UAAU,CAACU,KAAK;IACvC;IAEA,MAAMzP,aAAa,GAAG,IAAIgI,aAAa,CAAC;MACtCrB,QAAQ;MACR9G,QAAQ,EAAEA,QAAQ;MAClB5D,UAAU,EAAE+I,IAAI;MAChB0K,gBAAgB,EAAE9P,OAAO,CAAC8P,gBAAgB;MAC1CC,IAAI,EAAE,CAAC,CAAC/P,OAAO,CAAC+P,IAAI;MACpBV,OAAO,EAAEA,OAAO;MAChBpL,OAAO,EAAE,CAAC,CAACjE,OAAO,CAACiE;IACrB,CAAC,CAAC;IAEF,IAAIvE,MAAM;IAEV,IAAIjC,OAAO,EAAE;MACXiC,MAAM,GAAGM,OAAO,CAACwP,eAAe,GAAG/R,OAAO,CAACgC,IAAI,CAAC,MAAM4O,eAAe,CAAC,GAAG5Q,OAAO;IAClF,CAAC,MAAM;MACLiC,MAAM,GAAGM,OAAO,CAACwP,eAAe,GAAGnB,eAAe,GAAG9Q,SAAS;IAChE;IAEA,IAAIyC,OAAO,CAACM,oBAAoB,EAAE;MAChC,OAAO;QACLF,aAAa;QACbV;MACF,CAAC;IACH;IAEA0F,IAAI,CAAC7E,qBAAqB,CAACH,aAAa,EAAEJ,OAAO,CAAC;IAClD,OAAON,MAAM;EACf;EAEAoO,SAASA,CAACtR,IAAI,EAAEiC,IAAI,EAAEuB,OAAO,EAAE;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMoF,IAAI,GAAG,IAAI;IACjB,MAAMxG,SAAS,GAAGlE,GAAG,CAACgE,wBAAwB,CAACG,GAAG,CAAC,CAAC;IACpD,MAAMmR,IAAI,GAAG5K,IAAI,CAACqE,eAAe,CAACjN,IAAI,CAAC;IACvC,MAAMsC,mBAAmB,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,YAAY;IACnD,MAAMC,eAAe,GAAGJ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,gBAAgB;IACnD,MAAMkQ,UAAU,GAAG;MAAEU,KAAK,EAAE;IAAI,CAAC;IAEjC,MAAMI,aAAa,GAAG;MACpBnR,mBAAmB;MACnBqQ,UAAU;MACVnQ;IACF,CAAC;IACD,IAAI,CAACgR,IAAI,EAAE;MACT,OAAAtT,aAAA,CAAAA,aAAA,KAAYuT,aAAa;QAAE9B,OAAO,EAAE;MAAK;IAC3C;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,MAAM+B,mBAAmB,GAAGA,CAAA,KAAM;MAChC,IAAIf,UAAU,CAACU,KAAK,KAAK,IAAI,EAAE;QAC7BV,UAAU,CAACU,KAAK,GAAG3O,SAAS,CAACiP,WAAW,CAACvR,SAAS,EAAEpC,IAAI,CAAC;MAC3D;MACA,OAAO2S,UAAU,CAACU,KAAK;IACzB,CAAC;IAED,MAAMO,SAAS,GAAGC,MAAM,IAAI;MAC1BjL,IAAI,CAACgL,SAAS,CAACC,MAAM,CAAC;IACxB,CAAC;IAED,MAAMrC,UAAU,GAAG,IAAI9M,SAAS,CAACoP,gBAAgB,CAAC;MAChD9T,IAAI;MACJuC,YAAY,EAAE,IAAI;MAClBsR,MAAM,EAAEjL,IAAI,CAACiL,MAAM,CAAC,CAAC;MACrBrR,eAAe,EAAEgB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhB,eAAe;MACzCoR,SAAS,EAAEA,SAAS;MACpBjB,UAAUA,CAAA,EAAG;QACX,OAAOe,mBAAmB,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;;IAEF;IACA;IACA,MAAMnC,cAAc,GAAGA,CAAA,KAAM;MACzB,IAAInT,MAAM,CAACmQ,QAAQ,EAAE;QACnB;QACA;QACA,OAAOnQ,MAAM,CAAC2V,gBAAgB,CAAC,MAAM;UACnC;UACA,OAAOP,IAAI,CAAC3R,KAAK,CAAC2P,UAAU,EAAE9F,KAAK,CAAC6E,KAAK,CAACtO,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOuR,IAAI,CAAC3R,KAAK,CAAC2P,UAAU,EAAE9F,KAAK,CAAC6E,KAAK,CAACtO,IAAI,CAAC,CAAC;MAClD;IACJ,CAAC;IACD,OAAA/B,aAAA,CAAAA,aAAA,KAAYuT,aAAa;MAAE9B,OAAO,EAAE,IAAI;MAAEJ,cAAc;MAAEC;IAAU;EACtE;;EAEA;EACA;EACA;EACAI,cAAcA,CAAA,EAAG;IACf,IAAI,CAAE,IAAI,CAACoC,qBAAqB,CAAC,CAAC,EAAE;MAClC,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC7B;IAEAxV,MAAM,CAACoJ,MAAM,CAAC,IAAI,CAAC0B,OAAO,CAAC,CAACxJ,OAAO,CAAE+O,KAAK,IAAK;MAC7CA,KAAK,CAACoF,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ;;EAEA;EACA;EACA;EACAtB,0BAA0BA,CAACrI,QAAQ,EAAE;IACnC,MAAM3B,IAAI,GAAG,IAAI;IACjB,IAAIA,IAAI,CAAC6B,uBAAuB,CAACF,QAAQ,CAAC,EACxC,MAAM,IAAI5C,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAMwM,WAAW,GAAG,EAAE;IAEtB1V,MAAM,CAACuJ,OAAO,CAACY,IAAI,CAACW,OAAO,CAAC,CAACxJ,OAAO,CAACqU,KAAA,IAAyB;MAAA,IAAxB,CAACpL,UAAU,EAAE8F,KAAK,CAAC,GAAAsF,KAAA;MACvD,MAAMC,SAAS,GAAGvF,KAAK,CAACwF,iBAAiB,CAAC,CAAC;MAC3C;MACA,IAAI,CAAED,SAAS,EAAE;MACjBA,SAAS,CAACtU,OAAO,CAAC,CAACwU,GAAG,EAAEzO,EAAE,KAAK;QAC7BqO,WAAW,CAAC7I,IAAI,CAAC;UAAEtC,UAAU;UAAElD;QAAG,CAAC,CAAC;QACpC,IAAI,CAAE0C,MAAM,CAAC3E,IAAI,CAAC+E,IAAI,CAACsC,gBAAgB,EAAElC,UAAU,CAAC,EAAE;UACpDJ,IAAI,CAACsC,gBAAgB,CAAClC,UAAU,CAAC,GAAG,IAAI+C,UAAU,CAAC,CAAC;QACtD;QACA,MAAMjD,SAAS,GAAGF,IAAI,CAACsC,gBAAgB,CAAClC,UAAU,CAAC,CAACwL,UAAU,CAC5D1O,EAAE,EACFrH,MAAM,CAACC,MAAM,CAAC,IAAI,CACpB,CAAC;QACD,IAAIoK,SAAS,CAAC+B,cAAc,EAAE;UAC5B;UACA;UACA/B,SAAS,CAAC+B,cAAc,CAACN,QAAQ,CAAC,GAAG,IAAI;QAC3C,CAAC,MAAM;UACL;UACAzB,SAAS,CAACI,QAAQ,GAAGqL,GAAG;UACxBzL,SAAS,CAACkC,cAAc,GAAG,EAAE;UAC7BlC,SAAS,CAAC+B,cAAc,GAAGpM,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;UAC9CoK,SAAS,CAAC+B,cAAc,CAACN,QAAQ,CAAC,GAAG,IAAI;QAC3C;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAE9B,OAAO,CAAC0L,WAAW,CAAC,EAAE;MAC1BvL,IAAI,CAAC6B,uBAAuB,CAACF,QAAQ,CAAC,GAAG4J,WAAW;IACtD;EACF;;EAEA;EACA;EACAM,eAAeA,CAAA,EAAG;IAChBhW,MAAM,CAACoJ,MAAM,CAAC,IAAI,CAACI,cAAc,CAAC,CAAClI,OAAO,CAAEoI,GAAG,IAAK;MAClD;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,GAAG,CAACnI,IAAI,KAAK,kCAAkC,EAAE;QACnDmI,GAAG,CAACmG,IAAI,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;EACJ;;EAEA;EACAlK,KAAKA,CAACsQ,GAAG,EAAE;IACT,IAAI,CAAChR,OAAO,CAACiR,IAAI,CAACjQ,SAAS,CAACkQ,YAAY,CAACF,GAAG,CAAC,CAAC;EAChD;;EAEA;EACA;EACA;EACA;EACA;EACA;EACAtM,WAAWA,CAACsM,GAAG,EAAE;IACf,IAAI,CAACtQ,KAAK,CAACsQ,GAAG,EAAE,IAAI,CAAC;EACvB;;EAEA;EACA;EACA;EACAG,eAAeA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACpR,OAAO,CAACmR,eAAe,CAACC,KAAK,CAAC;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAAA,EAAU;IACd,OAAO,IAAI,CAACrR,OAAO,CAACqR,MAAM,CAAC,GAAAlU,SAAO,CAAC;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAEEyF,SAASA,CAAA,EAAU;IACjB,OAAO,IAAI,CAAC5C,OAAO,CAAC4C,SAAS,CAAC,GAAAzF,SAAO,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE2F,UAAUA,CAAA,EAAU;IAClB,OAAO,IAAI,CAAC9C,OAAO,CAAC8C,UAAU,CAAC,GAAA3F,SAAO,CAAC;EACzC;EAEAmU,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACtR,OAAO,CAAC8C,UAAU,CAAC;MAAEC,UAAU,EAAE;IAAK,CAAC,CAAC;EACtD;;EAEA;EACA;EACA;EACAoN,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC5F,WAAW,EAAE,IAAI,CAACA,WAAW,CAACyC,MAAM,CAAC,CAAC;IAC/C,OAAO,IAAI,CAAC1C,OAAO;EACrB;EAEA4F,SAASA,CAACC,MAAM,EAAE;IAChB;IACA,IAAI,IAAI,CAAC7F,OAAO,KAAK6F,MAAM,EAAE;IAC7B,IAAI,CAAC7F,OAAO,GAAG6F,MAAM;IACrB,IAAI,IAAI,CAAC5F,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC5D,OAAO,CAAC,CAAC;EAClD;;EAEA;EACA;EACA;EACA2J,qBAAqBA,CAAA,EAAG;IACtB,OACE,CAAEvL,OAAO,CAAC,IAAI,CAAC+E,iBAAiB,CAAC,IACjC,CAAE/E,OAAO,CAAC,IAAI,CAAC8E,0BAA0B,CAAC;EAE9C;;EAEA;EACA;EACA0H,yBAAyBA,CAAA,EAAG;IAC1B,MAAMC,QAAQ,GAAG,IAAI,CAACpN,eAAe;IACrC,OAAOrJ,MAAM,CAACoJ,MAAM,CAACqN,QAAQ,CAAC,CAACnF,IAAI,CAAEhI,OAAO,IAAK,CAAC,CAACA,OAAO,CAACP,WAAW,CAAC;EACzE;EAEA,MAAM2N,sBAAsBA,CAACpQ,GAAG,EAAE4D,OAAO,EAAE;IACzC,MAAMyM,WAAW,GAAGrQ,GAAG,CAACA,GAAG;;IAE3B;IACA,IAAIqQ,WAAW,KAAK,OAAO,EAAE;MAC3B,MAAM,IAAI,CAAC1M,cAAc,CAAC3D,GAAG,EAAE4D,OAAO,CAAC;IACzC,CAAC,MAAM,IAAIyM,WAAW,KAAK,SAAS,EAAE;MACpC,IAAI,CAAC1L,gBAAgB,CAAC3E,GAAG,EAAE4D,OAAO,CAAC;IACrC,CAAC,MAAM,IAAIyM,WAAW,KAAK,SAAS,EAAE;MACpC,IAAI,CAACxL,gBAAgB,CAAC7E,GAAG,EAAE4D,OAAO,CAAC;IACrC,CAAC,MAAM,IAAIyM,WAAW,KAAK,OAAO,EAAE;MAClC,IAAI,CAACvL,cAAc,CAAC9E,GAAG,EAAE4D,OAAO,CAAC;IACnC,CAAC,MAAM,IAAIyM,WAAW,KAAK,SAAS,EAAE;MACpC,IAAI,CAAC9K,gBAAgB,CAACvF,GAAG,EAAE4D,OAAO,CAAC;IACrC,CAAC,MAAM,IAAIyM,WAAW,KAAK,OAAO,EAAE;MAClC;IAAA,CACD,MAAM;MACLhX,MAAM,CAACa,MAAM,CAAC,+CAA+C,EAAE8F,GAAG,CAAC;IACrE;EACF;EAEAsQ,sBAAsBA,CAAA,EAAG;IACvB,MAAMzM,IAAI,GAAG,IAAI;IACjB,IAAIA,IAAI,CAACiF,0BAA0B,EAAE;MACnCyH,YAAY,CAAC1M,IAAI,CAACiF,0BAA0B,CAAC;MAC7CjF,IAAI,CAACiF,0BAA0B,GAAG,IAAI;IACxC;IAEAjF,IAAI,CAACgF,sBAAsB,GAAG,IAAI;IAClC;IACA;IACA;IACA,MAAM2H,MAAM,GAAG3M,IAAI,CAAC+E,eAAe;IACnC/E,IAAI,CAAC+E,eAAe,GAAGlP,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC1C,OAAO6W,MAAM;EACf;;EAEA;AACF;AACA;AACA;EACE,MAAMC,oBAAoBA,CAAC7M,OAAO,EAAE;IAClC,MAAMC,IAAI,GAAG,IAAI;IAEjB,IAAIA,IAAI,CAACS,YAAY,IAAI,CAACZ,OAAO,CAACE,OAAO,CAAC,EAAE;MAC1C;MACA,KAAK,MAAMmG,KAAK,IAAIrQ,MAAM,CAACoJ,MAAM,CAACe,IAAI,CAACW,OAAO,CAAC,EAAE;QAAA,IAAAkM,oBAAA;QAC/C,MAAM3G,KAAK,CAACO,WAAW,CACrB,EAAAoG,oBAAA,GAAA9M,OAAO,CAACmG,KAAK,CAAC4G,KAAK,CAAC,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsB3U,MAAM,KAAI,CAAC,EACjC8H,IAAI,CAACS,YACP,CAAC;MACH;MAEAT,IAAI,CAACS,YAAY,GAAG,KAAK;;MAEzB;MACA,KAAK,MAAM,CAACsM,SAAS,EAAEC,QAAQ,CAAC,IAAInX,MAAM,CAACuJ,OAAO,CAACW,OAAO,CAAC,EAAE;QAC3D,MAAMmG,KAAK,GAAGlG,IAAI,CAACW,OAAO,CAACoM,SAAS,CAAC;QACrC,IAAI7G,KAAK,EAAE;UACT;UACA;UACA,MAAM+G,UAAU,GAAG,GAAG;UACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAC9U,MAAM,EAAEgV,CAAC,IAAID,UAAU,EAAE;YACpD,MAAME,KAAK,GAAGH,QAAQ,CAAC/J,KAAK,CAACiK,CAAC,EAAEE,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGD,UAAU,EAAED,QAAQ,CAAC9U,MAAM,CAAC,CAAC;YAE1E,KAAK,MAAMiE,GAAG,IAAIgR,KAAK,EAAE;cACvB,MAAMjH,KAAK,CAACQ,MAAM,CAACvK,GAAG,CAAC;YACzB;YAEA,MAAM,IAAIvE,OAAO,CAACC,OAAO,IAAIyV,OAAO,CAACC,QAAQ,CAAC1V,OAAO,CAAC,CAAC;UACzD;QACF,CAAC,MAAM;UACL;UACAmI,IAAI,CAAC6E,wBAAwB,CAACkI,SAAS,CAAC,GACtC/M,IAAI,CAAC6E,wBAAwB,CAACkI,SAAS,CAAC,IAAI,EAAE;UAChD/M,IAAI,CAAC6E,wBAAwB,CAACkI,SAAS,CAAC,CAACrK,IAAI,CAAC,GAAGsK,QAAQ,CAAC;QAC5D;MACF;;MAEA;MACA,KAAK,MAAM9G,KAAK,IAAIrQ,MAAM,CAACoJ,MAAM,CAACe,IAAI,CAACW,OAAO,CAAC,EAAE;QAC/C,MAAMuF,KAAK,CAACS,SAAS,CAAC,CAAC;MACzB;IACF;IAEA3G,IAAI,CAACwN,wBAAwB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;EACEC,oBAAoBA,CAAC1N,OAAO,EAAE;IAC5B,MAAMC,IAAI,GAAG,IAAI;IAEjB,IAAIA,IAAI,CAACS,YAAY,IAAI,CAACZ,OAAO,CAACE,OAAO,CAAC,EAAE;MAC1C;MACAlK,MAAM,CAACoJ,MAAM,CAACe,IAAI,CAACW,OAAO,CAAC,CAACxJ,OAAO,CAAC+O,KAAK,IAAI;QAAA,IAAAwH,qBAAA;QAC3CxH,KAAK,CAACO,WAAW,CACf,EAAAiH,qBAAA,GAAA3N,OAAO,CAACmG,KAAK,CAAC4G,KAAK,CAAC,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBxV,MAAM,KAAI,CAAC,EACjC8H,IAAI,CAACS,YACP,CAAC;MACH,CAAC,CAAC;MAEFT,IAAI,CAACS,YAAY,GAAG,KAAK;MAEzB5K,MAAM,CAACuJ,OAAO,CAACW,OAAO,CAAC,CAAC5I,OAAO,CAACwW,KAAA,IAA2B;QAAA,IAA1B,CAACZ,SAAS,EAAEC,QAAQ,CAAC,GAAAW,KAAA;QACpD,MAAMzH,KAAK,GAAGlG,IAAI,CAACW,OAAO,CAACoM,SAAS,CAAC;QACrC,IAAI7G,KAAK,EAAE;UACT8G,QAAQ,CAAC7V,OAAO,CAACgF,GAAG,IAAI+J,KAAK,CAACQ,MAAM,CAACvK,GAAG,CAAC,CAAC;QAC5C,CAAC,MAAM;UACL6D,IAAI,CAAC6E,wBAAwB,CAACkI,SAAS,CAAC,GACtC/M,IAAI,CAAC6E,wBAAwB,CAACkI,SAAS,CAAC,IAAI,EAAE;UAChD/M,IAAI,CAAC6E,wBAAwB,CAACkI,SAAS,CAAC,CAACrK,IAAI,CAAC,GAAGsK,QAAQ,CAAC;QAC5D;MACF,CAAC,CAAC;MAEFnX,MAAM,CAACoJ,MAAM,CAACe,IAAI,CAACW,OAAO,CAAC,CAACxJ,OAAO,CAAC+O,KAAK,IAAIA,KAAK,CAACS,SAAS,CAAC,CAAC,CAAC;IACjE;IAEA3G,IAAI,CAACwN,wBAAwB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;EACE,MAAMnC,oBAAoBA,CAAA,EAAG;IAC3B,MAAMrL,IAAI,GAAG,IAAI;IACjB,MAAM2M,MAAM,GAAG3M,IAAI,CAACyM,sBAAsB,CAAC,CAAC;IAE5C,OAAOjX,MAAM,CAAC+P,QAAQ,GAClBvF,IAAI,CAACyN,oBAAoB,CAACd,MAAM,CAAC,GACjC3M,IAAI,CAAC4M,oBAAoB,CAACD,MAAM,CAAC;EACvC;;EAEA;EACA;EACA;EACAa,wBAAwBA,CAAA,EAAG;IACzB,MAAMxN,IAAI,GAAG,IAAI;IACjB,MAAM6G,SAAS,GAAG7G,IAAI,CAACyE,qBAAqB;IAC5CzE,IAAI,CAACyE,qBAAqB,GAAG,EAAE;IAC/BoC,SAAS,CAAC1P,OAAO,CAAEkL,CAAC,IAAK;MACvBA,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ;;EAEA;EACA;EACA;EACAjB,+BAA+BA,CAACgG,CAAC,EAAE;IACjC,MAAMpH,IAAI,GAAG,IAAI;IACjB,MAAM4N,gBAAgB,GAAGA,CAAA,KAAM;MAC7B5N,IAAI,CAACyE,qBAAqB,CAAC/B,IAAI,CAAC0E,CAAC,CAAC;IACpC,CAAC;IACD,IAAIyG,uBAAuB,GAAG,CAAC;IAC/B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,EAAED,uBAAuB;MACzB,IAAIA,uBAAuB,KAAK,CAAC,EAAE;QACjC;QACA;QACAD,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC;IAED/X,MAAM,CAACoJ,MAAM,CAACe,IAAI,CAACsC,gBAAgB,CAAC,CAACnL,OAAO,CAAE4W,eAAe,IAAK;MAChEA,eAAe,CAAC5W,OAAO,CAAE+I,SAAS,IAAK;QACrC,MAAM8N,sCAAsC,GAC1CvR,IAAI,CAACyD,SAAS,CAAC+B,cAAc,CAAC,CAACkF,IAAI,CAACxF,QAAQ,IAAI;UAC9C,MAAMxC,OAAO,GAAGa,IAAI,CAACd,eAAe,CAACyC,QAAQ,CAAC;UAC9C,OAAOxC,OAAO,IAAIA,OAAO,CAACP,WAAW;QACvC,CAAC,CAAC;QAEJ,IAAIoP,sCAAsC,EAAE;UAC1C,EAAEH,uBAAuB;UACzB3N,SAAS,CAACkC,cAAc,CAACM,IAAI,CAACoL,gBAAgB,CAAC;QACjD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAID,uBAAuB,KAAK,CAAC,EAAE;MACjC;MACA;MACAD,gBAAgB,CAAC,CAAC;IACpB;EACF;EAEAzS,qBAAqBA,CAACH,aAAa,EAAEJ,OAAO,EAAE;IAC5C,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE+P,IAAI,EAAE;MACjB;MACA,IAAI,CAACnM,wBAAwB,CAACkE,IAAI,CAAC;QACjCiI,IAAI,EAAE,IAAI;QACVjM,OAAO,EAAE,CAAC1D,aAAa;MACzB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA;MACA,IAAI6E,OAAO,CAAC,IAAI,CAACrB,wBAAwB,CAAC,IACtC0E,IAAI,CAAC,IAAI,CAAC1E,wBAAwB,CAAC,CAACmM,IAAI,EAAE;QAC5C,IAAI,CAACnM,wBAAwB,CAACkE,IAAI,CAAC;UACjCiI,IAAI,EAAE,KAAK;UACXjM,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MAEAwE,IAAI,CAAC,IAAI,CAAC1E,wBAAwB,CAAC,CAACE,OAAO,CAACgE,IAAI,CAAC1H,aAAa,CAAC;IACjE;;IAEA;IACA,IAAI,IAAI,CAACwD,wBAAwB,CAACtG,MAAM,KAAK,CAAC,EAAE;MAC9C8C,aAAa,CAACiT,WAAW,CAAC,CAAC;IAC7B;EACF;;EAEA;EACA;EACA;EACAC,0BAA0BA,CAAA,EAAG;IAC3B,MAAMlO,IAAI,GAAG,IAAI;IACjB,IAAIA,IAAI,CAACqM,yBAAyB,CAAC,CAAC,EAAE;;IAEtC;IACA;IACA;IACA,IAAI,CAAExM,OAAO,CAACG,IAAI,CAACxB,wBAAwB,CAAC,EAAE;MAC5C,MAAM2P,UAAU,GAAGnO,IAAI,CAACxB,wBAAwB,CAACQ,KAAK,CAAC,CAAC;MACxD,IAAI,CAAEa,OAAO,CAACsO,UAAU,CAACzP,OAAO,CAAC,EAC/B,MAAM,IAAIK,KAAK,CACb,6CAA6C,GAC3CgD,IAAI,CAACC,SAAS,CAACmM,UAAU,CAC7B,CAAC;;MAEH;MACA,IAAI,CAAEtO,OAAO,CAACG,IAAI,CAACxB,wBAAwB,CAAC,EAC1CwB,IAAI,CAACoO,uBAAuB,CAAC,CAAC;IAClC;;IAEA;IACApO,IAAI,CAACpH,aAAa,CAAC,CAAC;EACtB;;EAEA;EACA;EACAwV,uBAAuBA,CAAA,EAAG;IACxB,MAAMpO,IAAI,GAAG,IAAI;IAEjB,IAAIH,OAAO,CAACG,IAAI,CAACxB,wBAAwB,CAAC,EAAE;MAC1C;IACF;IAEAwB,IAAI,CAACxB,wBAAwB,CAAC,CAAC,CAAC,CAACE,OAAO,CAACvH,OAAO,CAACkX,CAAC,IAAI;MACpDA,CAAC,CAACJ,WAAW,CAAC,CAAC;IACjB,CAAC,CAAC;EACJ;EAEArS,oCAAoCA,CAAC0S,0BAA0B,EAAE;IAC/D,MAAMtO,IAAI,GAAG,IAAI;IACjB,IAAIH,OAAO,CAACyO,0BAA0B,CAAC,EAAE;;IAEzC;IACA;IACA;IACA,IAAIzO,OAAO,CAACG,IAAI,CAACxB,wBAAwB,CAAC,EAAE;MAC1CwB,IAAI,CAACxB,wBAAwB,GAAG8P,0BAA0B;MAC1DtO,IAAI,CAACoO,uBAAuB,CAAC,CAAC;MAC9B;IACF;;IAEA;IACA;IACA;IACA,IACE,CAAClL,IAAI,CAAClD,IAAI,CAACxB,wBAAwB,CAAC,CAACmM,IAAI,IACzC,CAAC2D,0BAA0B,CAAC,CAAC,CAAC,CAAC3D,IAAI,EACnC;MACA2D,0BAA0B,CAAC,CAAC,CAAC,CAAC5P,OAAO,CAACvH,OAAO,CAAEkX,CAAC,IAAK;QACnDnL,IAAI,CAAClD,IAAI,CAACxB,wBAAwB,CAAC,CAACE,OAAO,CAACgE,IAAI,CAAC2L,CAAC,CAAC;;QAEnD;QACA,IAAIrO,IAAI,CAACxB,wBAAwB,CAACtG,MAAM,KAAK,CAAC,EAAE;UAC9CmW,CAAC,CAACJ,WAAW,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;MAEFK,0BAA0B,CAACtP,KAAK,CAAC,CAAC;IACpC;;IAEA;IACAgB,IAAI,CAACxB,wBAAwB,CAACkE,IAAI,CAAC,GAAG4L,0BAA0B,CAAC;EACnE;EAEApQ,oDAAoDA,CAAA,EAAG;IACrD,MAAM8B,IAAI,GAAG,IAAI;IACjB,MAAMsO,0BAA0B,GAAGtO,IAAI,CAACxB,wBAAwB;IAChEwB,IAAI,CAACxB,wBAAwB,GAAG,EAAE;IAElCwB,IAAI,CAAC8D,WAAW,IAAI9D,IAAI,CAAC8D,WAAW,CAAC,CAAC;IACtCxO,GAAG,CAACiZ,cAAc,CAACC,IAAI,CAAE3T,QAAQ,IAAK;MACpCA,QAAQ,CAACmF,IAAI,CAAC;MACd,OAAO,IAAI;IACb,CAAC,CAAC;IAEFA,IAAI,CAACpE,oCAAoC,CAAC0S,0BAA0B,CAAC;EACvE;;EAEA;EACAtV,eAAeA,CAAA,EAAG;IAChB,OAAO6G,OAAO,CAAC,IAAI,CAACX,eAAe,CAAC;EACtC;;EAEA;EACA;EACAtG,aAAaA,CAAA,EAAG;IACd,MAAMoH,IAAI,GAAG,IAAI;IACjB,IAAIA,IAAI,CAAC8E,aAAa,IAAI9E,IAAI,CAAChH,eAAe,CAAC,CAAC,EAAE;MAChDgH,IAAI,CAAC8E,aAAa,CAAC,CAAC;MACpB9E,IAAI,CAAC8E,aAAa,GAAG,IAAI;IAC3B;EACF;AACF,C;;;;;;;;;;;ACj6CA1P,MAAM,CAACoC,MAAM,CAAC;EAAC4L,iBAAiB,EAACA,CAAA,KAAIA;AAAiB,CAAC,CAAC;AAAC,IAAItH,SAAS;AAAC1G,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACyG,SAASA,CAACvG,CAAC,EAAC;IAACuG,SAAS,GAACvG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAMA,CAACD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAID,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACC,GAAGA,CAACC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIuN,KAAK;AAAC1N,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACyN,KAAKA,CAACvN,CAAC,EAAC;IAACuN,KAAK,GAACvN,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsK,OAAO,EAACD,MAAM;AAACxK,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAACwK,OAAOA,CAACtK,CAAC,EAAC;IAACsK,OAAO,GAACtK,CAAC;EAAA,CAAC;EAACqK,MAAMA,CAACrK,CAAC,EAAC;IAACqK,MAAM,GAACrK,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAM1Z,MAAM6N,iBAAiB,CAAC;EAC7BrH,WAAWA,CAAC9E,UAAU,EAAE;IACtB,IAAI,CAAC+E,WAAW,GAAG/E,UAAU;EAC/B;;EAEA;AACF;AACA;AACA;EACE,MAAM6F,mBAAmBA,CAACX,GAAG,EAAE;IAC7B,MAAM6D,IAAI,GAAG,IAAI,CAAChE,WAAW;IAE7B,IAAIgE,IAAI,CAACrD,QAAQ,KAAK,MAAM,IAAIqD,IAAI,CAACuE,kBAAkB,KAAK,CAAC,EAAE;MAC7DvE,IAAI,CAAC1D,UAAU,GAAG,IAAIR,SAAS,CAAC2S,SAAS,CAAC;QACxCnL,iBAAiB,EAAEtD,IAAI,CAACuE,kBAAkB;QAC1ChB,gBAAgB,EAAEvD,IAAI,CAACwE,iBAAiB;QACxCkK,SAASA,CAAA,EAAG;UACV1O,IAAI,CAACiM,eAAe,CAClB,IAAI3W,GAAG,CAAC0O,eAAe,CAAC,yBAAyB,CACnD,CAAC;QACH,CAAC;QACD2K,QAAQA,CAAA,EAAG;UACT3O,IAAI,CAACxE,KAAK,CAAC;YAAEW,GAAG,EAAE;UAAO,CAAC,CAAC;QAC7B;MACF,CAAC,CAAC;MACF6D,IAAI,CAAC1D,UAAU,CAACsS,KAAK,CAAC,CAAC;IACzB;;IAEA;IACA,IAAI5O,IAAI,CAAC5B,cAAc,EAAE4B,IAAI,CAACS,YAAY,GAAG,IAAI;IAEjD,IAAIoO,4BAA4B;IAChC,IAAI,OAAO1S,GAAG,CAACkC,OAAO,KAAK,QAAQ,EAAE;MACnCwQ,4BAA4B,GAAG7O,IAAI,CAAC5B,cAAc,KAAKjC,GAAG,CAACkC,OAAO;MAClE2B,IAAI,CAAC5B,cAAc,GAAGjC,GAAG,CAACkC,OAAO;IACnC;IAEA,IAAIwQ,4BAA4B,EAAE;MAChC;MACA;IACF;;IAEA;;IAEA;IACA;IACA7O,IAAI,CAAC6E,wBAAwB,GAAGhP,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAEnD,IAAIkK,IAAI,CAACS,YAAY,EAAE;MACrB;MACA;MACAT,IAAI,CAAC6B,uBAAuB,GAAGhM,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAClDkK,IAAI,CAACsC,gBAAgB,GAAGzM,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7C;;IAEA;IACAkK,IAAI,CAACyE,qBAAqB,GAAG,EAAE;;IAE/B;IACAzE,IAAI,CAAC4E,iBAAiB,GAAG/O,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5CD,MAAM,CAACuJ,OAAO,CAACY,IAAI,CAACX,cAAc,CAAC,CAAClI,OAAO,CAACmI,IAAA,IAAe;MAAA,IAAd,CAACpC,EAAE,EAAEqC,GAAG,CAAC,GAAAD,IAAA;MACpD,IAAIC,GAAG,CAAC+B,KAAK,EAAE;QACbtB,IAAI,CAAC4E,iBAAiB,CAAC1H,EAAE,CAAC,GAAG,IAAI;MACnC;IACF,CAAC,CAAC;;IAEF;IACA;IACA;IACA;IACA;IACA;IACA;IACA8C,IAAI,CAAC2E,0BAA0B,GAAG9O,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACrD,IAAIkK,IAAI,CAACS,YAAY,EAAE;MACrB,MAAM6L,QAAQ,GAAGtM,IAAI,CAACd,eAAe;MACrCrJ,MAAM,CAAC4G,IAAI,CAAC6P,QAAQ,CAAC,CAACnV,OAAO,CAAC+F,EAAE,IAAI;QAClC,MAAMiC,OAAO,GAAGmN,QAAQ,CAACpP,EAAE,CAAC;QAC5B,IAAIiC,OAAO,CAAC2P,SAAS,CAAC,CAAC,EAAE;UACvB;UACA;UACA;UACA;UACA9O,IAAI,CAACyE,qBAAqB,CAAC/B,IAAI,CAC7B;YAAA,OAAavD,OAAO,CAACsD,WAAW,CAAC,GAAAxK,SAAO,CAAC;UAAA,CAC3C,CAAC;QACH,CAAC,MAAM,IAAIkH,OAAO,CAACP,WAAW,EAAE;UAC9B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAoB,IAAI,CAAC2E,0BAA0B,CAACxF,OAAO,CAACwC,QAAQ,CAAC,GAAG,IAAI;QAC1D;MACF,CAAC,CAAC;IACJ;IAEA3B,IAAI,CAAC0E,gCAAgC,GAAG,EAAE;;IAE1C;IACA;IACA,IAAI,CAAC1E,IAAI,CAACoL,qBAAqB,CAAC,CAAC,EAAE;MACjC,IAAIpL,IAAI,CAACS,YAAY,EAAE;QACrB,KAAK,MAAMyF,KAAK,IAAIrQ,MAAM,CAACoJ,MAAM,CAACe,IAAI,CAACW,OAAO,CAAC,EAAE;UAC/C,MAAMuF,KAAK,CAACO,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;UAChC,MAAMP,KAAK,CAACS,SAAS,CAAC,CAAC;QACzB;QACA3G,IAAI,CAACS,YAAY,GAAG,KAAK;MAC3B;MACAT,IAAI,CAACwN,wBAAwB,CAAC,CAAC;IACjC;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMrQ,cAAcA,CAAChB,GAAG,EAAE;IACxB,MAAM6D,IAAI,GAAG,IAAI,CAAChE,WAAW;IAE7B,IAAIgE,IAAI,CAACoL,qBAAqB,CAAC,CAAC,EAAE;MAChCpL,IAAI,CAAC0E,gCAAgC,CAAChC,IAAI,CAACvG,GAAG,CAAC;MAE/C,IAAIA,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;QACvB,OAAO6D,IAAI,CAAC4E,iBAAiB,CAACzI,GAAG,CAACe,EAAE,CAAC;MACvC;MAEA,IAAIf,GAAG,CAAC+E,IAAI,EAAE;QACZ/E,GAAG,CAAC+E,IAAI,CAAC/J,OAAO,CAACgK,KAAK,IAAI;UACxB,OAAOnB,IAAI,CAAC4E,iBAAiB,CAACzD,KAAK,CAAC;QACtC,CAAC,CAAC;MACJ;MAEA,IAAIhF,GAAG,CAACuC,OAAO,EAAE;QACfvC,GAAG,CAACuC,OAAO,CAACvH,OAAO,CAACwK,QAAQ,IAAI;UAC9B,OAAO3B,IAAI,CAAC2E,0BAA0B,CAAChD,QAAQ,CAAC;QAClD,CAAC,CAAC;MACJ;MAEA,IAAI3B,IAAI,CAACoL,qBAAqB,CAAC,CAAC,EAAE;QAChC;MACF;;MAEA;MACA;MACA;MACA,MAAM2D,gBAAgB,GAAG/O,IAAI,CAAC0E,gCAAgC;MAC9D,KAAK,MAAMsK,eAAe,IAAInZ,MAAM,CAACoJ,MAAM,CAAC8P,gBAAgB,CAAC,EAAE;QAC7D,MAAM,IAAI,CAACxC,sBAAsB,CAC/ByC,eAAe,EACfhP,IAAI,CAAC+E,eACP,CAAC;MACH;MACA/E,IAAI,CAAC0E,gCAAgC,GAAG,EAAE;IAC5C,CAAC,MAAM;MACL,MAAM,IAAI,CAAC6H,sBAAsB,CAACpQ,GAAG,EAAE6D,IAAI,CAAC+E,eAAe,CAAC;IAC9D;;IAEA;IACA;IACA;IACA,MAAMkK,aAAa,GACjB9S,GAAG,CAACA,GAAG,KAAK,OAAO,IACnBA,GAAG,CAACA,GAAG,KAAK,SAAS,IACrBA,GAAG,CAACA,GAAG,KAAK,SAAS;IAEvB,IAAI6D,IAAI,CAACkF,uBAAuB,KAAK,CAAC,IAAI,CAAC+J,aAAa,EAAE;MACxD,MAAMjP,IAAI,CAACqL,oBAAoB,CAAC,CAAC;MACjC;IACF;IAEA,IAAIrL,IAAI,CAACgF,sBAAsB,KAAK,IAAI,EAAE;MACxChF,IAAI,CAACgF,sBAAsB,GACzB,IAAIkK,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGnP,IAAI,CAACmF,qBAAqB;IACrD,CAAC,MAAM,IAAInF,IAAI,CAACgF,sBAAsB,GAAG,IAAIkK,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE;MAC7D,MAAMnP,IAAI,CAACqL,oBAAoB,CAAC,CAAC;MACjC;IACF;IAEA,IAAIrL,IAAI,CAACiF,0BAA0B,EAAE;MACnCyH,YAAY,CAAC1M,IAAI,CAACiF,0BAA0B,CAAC;IAC/C;IACAjF,IAAI,CAACiF,0BAA0B,GAAGmK,UAAU,CAAC,MAAM;MACjDpP,IAAI,CAACqP,sBAAsB,GAAGrP,IAAI,CAACqL,oBAAoB,CAAC,CAAC;MACzD,IAAI7V,MAAM,CAAC2T,UAAU,CAACnJ,IAAI,CAACqP,sBAAsB,CAAC,EAAE;QAClDrP,IAAI,CAACqP,sBAAsB,CAAC7W,OAAO,CACjC,MAAOwH,IAAI,CAACqP,sBAAsB,GAAGlX,SACvC,CAAC;MACH;IACF,CAAC,EAAE6H,IAAI,CAACkF,uBAAuB,CAAC;EAClC;;EAEA;AACF;AACA;AACA;EACE,MAAMqH,sBAAsBA,CAACpQ,GAAG,EAAE4D,OAAO,EAAE;IACzC,MAAMyM,WAAW,GAAGrQ,GAAG,CAACA,GAAG;IAE3B,QAAQqQ,WAAW;MACjB,KAAK,OAAO;QACV,MAAM,IAAI,CAACxQ,WAAW,CAAC8D,cAAc,CAAC3D,GAAG,EAAE4D,OAAO,CAAC;QACnD;MACF,KAAK,SAAS;QACZ,IAAI,CAAC/D,WAAW,CAAC8E,gBAAgB,CAAC3E,GAAG,EAAE4D,OAAO,CAAC;QAC/C;MACF,KAAK,SAAS;QACZ,IAAI,CAAC/D,WAAW,CAACgF,gBAAgB,CAAC7E,GAAG,EAAE4D,OAAO,CAAC;QAC/C;MACF,KAAK,OAAO;QACV,IAAI,CAAC/D,WAAW,CAACiF,cAAc,CAAC9E,GAAG,EAAE4D,OAAO,CAAC;QAC7C;MACF,KAAK,SAAS;QACZ,IAAI,CAAC/D,WAAW,CAAC0F,gBAAgB,CAACvF,GAAG,EAAE4D,OAAO,CAAC;QAC/C;MACF,KAAK,OAAO;QACV;QACA;MACF;QACEvK,MAAM,CAACa,MAAM,CAAC,+CAA+C,EAAE8F,GAAG,CAAC;IACvE;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMkB,gBAAgBA,CAAClB,GAAG,EAAE;IAC1B,MAAM6D,IAAI,GAAG,IAAI,CAAChE,WAAW;;IAE7B;IACA,IAAI,CAAC6D,OAAO,CAACG,IAAI,CAAC+E,eAAe,CAAC,EAAE;MAClC,MAAM/E,IAAI,CAACqL,oBAAoB,CAAC,CAAC;IACnC;;IAEA;IACA;IACA,IAAIxL,OAAO,CAACG,IAAI,CAACxB,wBAAwB,CAAC,EAAE;MAC1ChJ,MAAM,CAACa,MAAM,CAAC,mDAAmD,CAAC;MAClE;IACF;IACA,MAAMoI,kBAAkB,GAAGuB,IAAI,CAACxB,wBAAwB,CAAC,CAAC,CAAC,CAACE,OAAO;IACnE,IAAIwO,CAAC;IACL,MAAMmB,CAAC,GAAG5P,kBAAkB,CAAC6I,IAAI,CAAC,CAAClB,MAAM,EAAEkJ,GAAG,KAAK;MACjD,MAAMC,KAAK,GAAGnJ,MAAM,CAACzE,QAAQ,KAAKxF,GAAG,CAACe,EAAE;MACxC,IAAIqS,KAAK,EAAErC,CAAC,GAAGoC,GAAG;MAClB,OAAOC,KAAK;IACd,CAAC,CAAC;IACF,IAAI,CAAClB,CAAC,EAAE;MACN7Y,MAAM,CAACa,MAAM,CAAC,qDAAqD,EAAE8F,GAAG,CAAC;MACzE;IACF;;IAEA;IACA;IACA;IACAsC,kBAAkB,CAAC+Q,MAAM,CAACtC,CAAC,EAAE,CAAC,CAAC;IAE/B,IAAItN,MAAM,CAAC3E,IAAI,CAACkB,GAAG,EAAE,OAAO,CAAC,EAAE;MAC7BkS,CAAC,CAACvP,aAAa,CACb,IAAItJ,MAAM,CAACuJ,KAAK,CAAC5C,GAAG,CAAC+P,KAAK,CAACA,KAAK,EAAE/P,GAAG,CAAC+P,KAAK,CAACuD,MAAM,EAAEtT,GAAG,CAAC+P,KAAK,CAACwD,OAAO,CACvE,CAAC;IACH,CAAC,MAAM;MACL;MACArB,CAAC,CAACvP,aAAa,CAAC3G,SAAS,EAAEgE,GAAG,CAAC7B,MAAM,CAAC;IACxC;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAM8C,eAAeA,CAACjB,GAAG,EAAE;IACzB,MAAM6D,IAAI,GAAG,IAAI,CAAChE,WAAW;;IAE7B;IACA;IACA,MAAM,IAAI,CAACmB,cAAc,CAAChB,GAAG,CAAC;;IAE9B;IACA;;IAEA;IACA,IAAI,CAACyD,MAAM,CAAC3E,IAAI,CAAC+E,IAAI,CAACX,cAAc,EAAElD,GAAG,CAACe,EAAE,CAAC,EAAE;MAC7C;IACF;;IAEA;IACA,MAAMuK,aAAa,GAAGzH,IAAI,CAACX,cAAc,CAAClD,GAAG,CAACe,EAAE,CAAC,CAACuK,aAAa;IAC/D,MAAMC,YAAY,GAAG1H,IAAI,CAACX,cAAc,CAAClD,GAAG,CAACe,EAAE,CAAC,CAACwK,YAAY;IAE7D1H,IAAI,CAACX,cAAc,CAAClD,GAAG,CAACe,EAAE,CAAC,CAACqF,MAAM,CAAC,CAAC;IAEpC,MAAMoN,kBAAkB,GAAGC,MAAM,IAAI;MACnC,OACEA,MAAM,IACNA,MAAM,CAAC1D,KAAK,IACZ,IAAI1W,MAAM,CAACuJ,KAAK,CACd6Q,MAAM,CAAC1D,KAAK,CAACA,KAAK,EAClB0D,MAAM,CAAC1D,KAAK,CAACuD,MAAM,EACnBG,MAAM,CAAC1D,KAAK,CAACwD,OACf,CAAC;IAEL,CAAC;;IAED;IACA,IAAIjI,aAAa,IAAItL,GAAG,CAAC+P,KAAK,EAAE;MAC9BzE,aAAa,CAACkI,kBAAkB,CAACxT,GAAG,CAAC,CAAC;IACxC;IAEA,IAAIuL,YAAY,EAAE;MAChBA,YAAY,CAACiI,kBAAkB,CAACxT,GAAG,CAAC,CAAC;IACvC;EACF;;EAEA;AACF;AACA;AACA;EACEmB,eAAeA,CAACnB,GAAG,EAAE;IACnB3G,MAAM,CAACa,MAAM,CAAC,8BAA8B,EAAE8F,GAAG,CAACsT,MAAM,CAAC;IACzD,IAAItT,GAAG,CAAC0T,gBAAgB,EAAEra,MAAM,CAACa,MAAM,CAAC,OAAO,EAAE8F,GAAG,CAAC0T,gBAAgB,CAAC;EACxE;;EAEA;AACF,C;;;;;;;;;;;AC/UAza,MAAM,CAACoC,MAAM,CAAC;EAACwL,aAAa,EAACA,CAAA,KAAIA;AAAa,CAAC,CAAC;AAKzC,MAAMA,aAAa,CAAC;EACzBjH,WAAWA,CAACnB,OAAO,EAAE;IACnB;IACA,IAAI,CAAC+G,QAAQ,GAAG/G,OAAO,CAAC+G,QAAQ;IAChC,IAAI,CAAC/C,WAAW,GAAG,KAAK;IAExB,IAAI,CAACkR,SAAS,GAAGlV,OAAO,CAACC,QAAQ;IACjC,IAAI,CAACmB,WAAW,GAAGpB,OAAO,CAAC3D,UAAU;IACrC,IAAI,CAAC8Y,QAAQ,GAAGnV,OAAO,CAACqP,OAAO;IAC/B,IAAI,CAAC+F,iBAAiB,GAAGpV,OAAO,CAAC8P,gBAAgB,KAAK,MAAM,CAAC,CAAC,CAAC;IAC/D,IAAI,CAACuF,KAAK,GAAGrV,OAAO,CAAC+P,IAAI;IACzB,IAAI,CAAC9L,OAAO,GAAGjE,OAAO,CAACiE,OAAO;IAC9B,IAAI,CAACqR,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,YAAY,GAAG,KAAK;;IAEzB;IACA,IAAI,CAACnU,WAAW,CAACkD,eAAe,CAAC,IAAI,CAACyC,QAAQ,CAAC,GAAG,IAAI;EACxD;EACA;EACA;EACAsM,WAAWA,CAAA,EAAG;IACZ;IACA;IACA;IACA,IAAI,IAAI,CAACa,SAAS,CAAC,CAAC,EAClB,MAAM,IAAI/P,KAAK,CAAC,+CAA+C,CAAC;;IAElE;IACA;IACA,IAAI,CAACoR,YAAY,GAAG,KAAK;IACzB,IAAI,CAACvR,WAAW,GAAG,IAAI;;IAEvB;IACA;IACA,IAAI,IAAI,CAACqR,KAAK,EACZ,IAAI,CAACjU,WAAW,CAAC2I,0BAA0B,CAAC,IAAI,CAAChD,QAAQ,CAAC,GAAG,IAAI;;IAEnE;IACA,IAAI,CAAC3F,WAAW,CAACR,KAAK,CAAC,IAAI,CAACuU,QAAQ,CAAC;EACvC;EACA;EACA;EACAK,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACF,aAAa,IAAI,IAAI,CAACC,YAAY,EAAE;MAC3C;MACA;MACA,IAAI,CAACL,SAAS,CAAC,IAAI,CAACI,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,CAAC;;MAE5D;MACA,OAAO,IAAI,CAAClU,WAAW,CAACkD,eAAe,CAAC,IAAI,CAACyC,QAAQ,CAAC;;MAEtD;MACA;MACA,IAAI,CAAC3F,WAAW,CAACkS,0BAA0B,CAAC,CAAC;IAC/C;EACF;EACA;EACA;EACA;EACA;EACApP,aAAaA,CAACvE,GAAG,EAAED,MAAM,EAAE;IACzB,IAAI,IAAI,CAACwU,SAAS,CAAC,CAAC,EAClB,MAAM,IAAI/P,KAAK,CAAC,0CAA0C,CAAC;IAC7D,IAAI,CAACmR,aAAa,GAAG,CAAC3V,GAAG,EAAED,MAAM,CAAC;IAClC,IAAI,CAAC0V,iBAAiB,CAACzV,GAAG,EAAED,MAAM,CAAC;IACnC,IAAI,CAAC8V,oBAAoB,CAAC,CAAC;EAC7B;EACA;EACA;EACA;EACA;EACA3N,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC0N,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B;EACA;EACAtB,SAASA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACoB,aAAa;EAC7B;AACF,C;;;;;;;;;;;ACpFA9a,MAAM,CAACoC,MAAM,CAAC;EAAC2L,UAAU,EAACA,CAAA,KAAIA;AAAU,CAAC,CAAC;AAAC,IAAIzD,OAAO;AAACtK,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;EAACqK,OAAOA,CAACnK,CAAC,EAAC;IAACmK,OAAO,GAACnK,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAExG,MAAM4N,UAAU,SAASkN,KAAK,CAAC;EACpCtU,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC2D,OAAO,CAACwC,WAAW,EAAExC,OAAO,CAACO,OAAO,CAAC;EAC7C;AACF,C;;;;;;;;;;;ACNA7K,MAAM,CAACoC,MAAM,CAAC;EAAClC,GAAG,EAACA,CAAA,KAAIA;AAAG,CAAC,CAAC;AAAC,IAAIwG,SAAS;AAAC1G,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACyG,SAASA,CAACvG,CAAC,EAAC;IAACuG,SAAS,GAACvG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAMA,CAACD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIkC,UAAU;AAACrC,MAAM,CAACC,IAAI,CAAC,0BAA0B,EAAC;EAACoC,UAAUA,CAAClC,CAAC,EAAC;IAACkC,UAAU,GAAClC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAKhQ;AACA;AACA;AACA,MAAM+a,cAAc,GAAG,EAAE;;AAEzB;AACA;AACA;AACA;AACO,MAAMhb,GAAG,GAAG,CAAC,CAAC;AAErB;AACA;AACA;AACAA,GAAG,CAACgE,wBAAwB,GAAG,IAAI9D,MAAM,CAAC+a,mBAAmB,CAAC,CAAC;AAC/Djb,GAAG,CAACkb,6BAA6B,GAAG,IAAIhb,MAAM,CAAC+a,mBAAmB,CAAC,CAAC;;AAEpE;AACAjb,GAAG,CAACmb,kBAAkB,GAAGnb,GAAG,CAACgE,wBAAwB;AAErDhE,GAAG,CAACob,2BAA2B,GAAG,IAAIlb,MAAM,CAAC+a,mBAAmB,CAAC,CAAC;;AAElE;AACA;AACA,SAASI,0BAA0BA,CAAC1G,OAAO,EAAE;EAC3C,IAAI,CAACA,OAAO,GAAGA,OAAO;AACxB;AAEA3U,GAAG,CAAC0O,eAAe,GAAGxO,MAAM,CAACob,aAAa,CACxC,qBAAqB,EACrBD,0BACF,CAAC;AAEDrb,GAAG,CAACub,oBAAoB,GAAGrb,MAAM,CAACob,aAAa,CAC7C,0BAA0B,EAC1B,MAAM,CAAC,CACT,CAAC;;AAED;AACA;AACA;AACAtb,GAAG,CAACwb,YAAY,GAAG1Z,IAAI,IAAI;EACzB,MAAM2Z,KAAK,GAAGzb,GAAG,CAACgE,wBAAwB,CAACG,GAAG,CAAC,CAAC;EAChD,OAAOqC,SAAS,CAACkV,YAAY,CAACvX,GAAG,CAACsX,KAAK,EAAE3Z,IAAI,CAAC;AAChD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9B,GAAG,CAAC4B,OAAO,GAAG,CAACmM,GAAG,EAAEzI,OAAO,KAAK;EAC9B,MAAMqW,GAAG,GAAG,IAAIxZ,UAAU,CAAC4L,GAAG,EAAEzI,OAAO,CAAC;EACxC0V,cAAc,CAAC5N,IAAI,CAACuO,GAAG,CAAC,CAAC,CAAC;EAC1B,OAAOA,GAAG;AACZ,CAAC;AAED3b,GAAG,CAACiZ,cAAc,GAAG,IAAI2C,IAAI,CAAC;EAAErL,eAAe,EAAE;AAAM,CAAC,CAAC;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAvQ,GAAG,CAACwO,WAAW,GAAGjJ,QAAQ,IAAIvF,GAAG,CAACiZ,cAAc,CAAC4C,QAAQ,CAACtW,QAAQ,CAAC;;AAEnE;AACA;AACA;AACAvF,GAAG,CAAC8b,sBAAsB,GAAG,MAAMd,cAAc,CAACe,KAAK,CACrDC,IAAI,IAAIzb,MAAM,CAACoJ,MAAM,CAACqS,IAAI,CAACjS,cAAc,CAAC,CAACgS,KAAK,CAAC9R,GAAG,IAAIA,GAAG,CAAC+B,KAAK,CACnE,CAAC,C", "file": "/packages/ddp-client.js", "sourcesContent": ["export { DDP } from '../common/namespace.js';\n\nimport '../common/livedata_connection';\n\n// Initialize the default server connection and put it on Meteor.connection\nimport './client_convenience';\n", "import { DDP } from '../common/namespace.js';\nimport { Meteor } from 'meteor/meteor';\nimport { loadAsyncStubHelpers } from \"./queue_stub_helpers\";\n\n// Meteor.refresh can be called on the client (if you're in common code) but it\n// only has an effect on the server.\nMeteor.refresh = () => {};\n\n// By default, try to connect back to the same endpoint as the page\n// was served from.\n//\n// XXX We should be doing this a different way. Right now we don't\n// include ROOT_URL_PATH_PREFIX when computing ddpUrl. (We don't\n// include it on the server when computing\n// DDP_DEFAULT_CONNECTION_URL, and we don't include it in our\n// default, '/'.) We get by with this because DDP.connect then\n// forces the URL passed to it to be interpreted relative to the\n// app's deploy path, even if it is absolute. Instead, we should\n// make DDP_DEFAULT_CONNECTION_URL, if set, include the path prefix;\n// make the default ddpUrl be '' rather that '/'; and make\n// _translateUrl in stream_client_common.js not force absolute paths\n// to be treated like relative paths. See also\n// stream_client_common.js #RationalizingRelativeDDPURLs\nconst runtimeConfig = typeof __meteor_runtime_config__ !== 'undefined' ? __meteor_runtime_config__ : Object.create(null);\nconst ddpUrl = runtimeConfig.DDP_DEFAULT_CONNECTION_URL || '/';\n\nconst retry = new Retry();\n\nfunction onDDPVersionNegotiationFailure(description) {\n  Meteor._debug(description);\n  if (Package.reload) {\n    const migrationData = Package.reload.Reload._migrationData('livedata') || Object.create(null);\n    let failures = migrationData.DDPVersionNegotiationFailures || 0;\n    ++failures;\n    Package.reload.Reload._onMigrate('livedata', () => [true, { DDPVersionNegotiationFailures: failures }]);\n    retry.retryLater(failures, () => {\n      Package.reload.Reload._reload({ immediateMigration: true });\n    });\n  }\n}\n\n// Makes sure to inject the stub async helpers before creating the connection\nloadAsyncStubHelpers();\n\nMeteor.connection = DDP.connect(ddpUrl, {\n  onDDPVersionNegotiationFailure: onDDPVersionNegotiationFailure\n});\n\n// Proxy the public methods of Meteor.connection so they can\n// be called directly on Meteor.\n[\n  'subscribe',\n  'methods',\n  'isAsyncCall',\n  'call',\n  'callAsync',\n  'apply',\n  'applyAsync',\n  'status',\n  'reconnect',\n  'disconnect'\n].forEach(name => {\n  Meteor[name] = Meteor.connection[name].bind(Meteor.connection);\n});\n", "import { DDP } from \"../common/namespace.js\";\nimport { Connection } from \"../common/livedata_connection\";\n\n// https://forums.meteor.com/t/proposal-to-fix-issues-with-async-method-stubs/60826\n\nlet queueSize = 0;\nlet queue = Promise.resolve();\n\nexport const loadAsyncStubHelpers = () => {\n  function queueFunction(fn, promiseProps = {}) {\n    queueSize += 1;\n\n    let resolve;\n    let reject;\n    const promise = new Promise((_resolve, _reject) => {\n      resolve = _resolve;\n      reject = _reject;\n    });\n\n    queue = queue.finally(() => {\n      fn(resolve, reject);\n\n      return promise.stubPromise?.catch(() => {}); // silent uncaught promise\n    });\n\n    promise\n      .catch(() => {}) // silent uncaught promise\n      .finally(() => {\n        queueSize -= 1;\n        if (queueSize === 0) {\n          Meteor.connection._maybeMigrate();\n        }\n      });\n\n    promise.stubPromise = promiseProps.stubPromise;\n    promise.serverPromise = promiseProps.serverPromise;\n\n    return promise;\n  }\n\n  let oldReadyToMigrate = Connection.prototype._readyToMigrate;\n  Connection.prototype._readyToMigrate = function () {\n    if (queueSize > 0) {\n      return false;\n    }\n\n    return oldReadyToMigrate.apply(this, arguments);\n  };\n\n  let currentMethodInvocation = null;\n\n  /**\n   * Meteor sets CurrentMethodInvocation to undefined for the reasons explained at\n   * https://github.com/meteor/meteor/blob/c9e3551b9673a7ed607f18cb1128563ff49ca96f/packages/ddp-client/common/livedata_connection.js#L578-L605\n   * The app code could call `.then` on a promise while the async stub is running,\n   * causing the `then` callback to think it is inside the stub.\n   *\n   * With the queueing we are doing, this is no longer necessary. The point\n   * of the queueing is to prevent app/package code from running while\n   * the stub is running, so we don't need to worry about this.\n   */\n\n  let oldApplyAsync = Connection.prototype.applyAsync;\n  Connection.prototype.applyAsync = function () {\n    let args = arguments;\n    let name = args[0];\n\n    if (currentMethodInvocation) {\n      DDP._CurrentMethodInvocation._set(currentMethodInvocation);\n      currentMethodInvocation = null;\n    }\n\n    const enclosing = DDP._CurrentMethodInvocation.get();\n    const alreadyInSimulation = enclosing?.isSimulation;\n    const isFromCallAsync = enclosing?._isFromCallAsync;\n\n    if (\n      Meteor.connection._getIsSimulation({\n        isFromCallAsync,\n        alreadyInSimulation,\n      })\n    ) {\n      // In stub - call immediately\n      return oldApplyAsync.apply(this, args);\n    }\n\n    let stubPromiseResolver;\n    let serverPromiseResolver;\n    const stubPromise = new Promise((r) => (stubPromiseResolver = r));\n    const serverPromise = new Promise((r) => (serverPromiseResolver = r));\n\n    return queueFunction(\n      (resolve, reject) => {\n        let finished = false;\n\n        Meteor._setImmediate(() => {\n          const applyAsyncPromise = oldApplyAsync.apply(this, args);\n          stubPromiseResolver(applyAsyncPromise.stubPromise);\n          serverPromiseResolver(applyAsyncPromise.serverPromise);\n\n          applyAsyncPromise.stubPromise\n            .catch(() => {}) // silent uncaught promise\n            .finally(() => {\n              finished = true;\n            });\n\n          applyAsyncPromise\n            .then((result) => {\n              resolve(result);\n            })\n            .catch((err) => {\n              reject(err);\n            });\n\n          serverPromise.catch(() => {}); // silent uncaught promise\n        });\n\n        Meteor._setImmediate(() => {\n          if (!finished) {\n            console.warn(\n              `Method stub (${name}) took too long and could cause unexpected problems. Learn more at https://v3-migration-docs.meteor.com/breaking-changes/call-x-callAsync.html#considerations-for-effective-use-of-meteor-callasync`\n            );\n          }\n        });\n      },\n      {\n        stubPromise,\n        serverPromise,\n      }\n    );\n  };\n\n  let oldApply = Connection.prototype.apply;\n  Connection.prototype.apply = function (name, args, options, callback) {\n    if (this._stream._neverQueued) {\n      return oldApply.apply(this, arguments);\n    }\n\n    // Apply runs the stub before synchronously returning.\n    //\n    // However, we want the server to run the methods in the original call order\n    // so we have to queue sending the message to the server until any previous async\n    // methods run.\n    // This does mean the stubs run in a different order than the methods on the\n    // server.\n\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = undefined;\n    }\n\n    let { methodInvoker, result } = oldApply.call(this, name, args, {\n      ...options,\n      _returnMethodInvoker: true,\n    }, callback);\n\n    if (methodInvoker) {\n      queueFunction((resolve) => {\n        this._addOutstandingMethod(methodInvoker, options);\n        resolve();\n      });\n    }\n\n    return result;\n  };\n\n  /**\n   * Queue subscriptions in case they rely on previous method calls\n   */\n  let queueSend = false;\n  let oldSubscribe = Connection.prototype.subscribe;\n  Connection.prototype.subscribe = function () {\n    if (this._stream._neverQueued) {\n      return oldSubscribe.apply(this, arguments);\n    }\n\n    queueSend = true;\n    try {\n      return oldSubscribe.apply(this, arguments);\n    } finally {\n      queueSend = false;\n    }\n  };\n\n  let oldSend = Connection.prototype._send;\n  Connection.prototype._send = function (params, shouldQueue) {\n    if (this._stream._neverQueued) {\n      return oldSend.apply(this, arguments);\n    }\n\n    if (!queueSend && !shouldQueue) {\n      return oldSend.call(this, params);\n    }\n\n    queueSend = false;\n    queueFunction((resolve) => {\n      try {\n        oldSend.call(this, params);\n      } finally {\n        resolve();\n      }\n    });\n  };\n\n  let _oldSendOutstandingMethodBlocksMessages =\n    Connection.prototype._sendOutstandingMethodBlocksMessages;\n  Connection.prototype._sendOutstandingMethodBlocksMessages = function () {\n    if (this._stream._neverQueued) {\n      return _oldSendOutstandingMethodBlocksMessages.apply(this, arguments);\n    }\n    queueFunction((resolve) => {\n      try {\n        _oldSendOutstandingMethodBlocksMessages.apply(this, arguments);\n      } finally {\n        resolve();\n      }\n    });\n  };\n};\n", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\n\nexport class ConnectionStreamHandlers {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * Handles incoming raw messages from the DDP stream\n   * @param {String} raw_msg The raw message received from the stream\n   */\n  async onMessage(raw_msg) {\n    let msg;\n    try {\n      msg = DDPCommon.parseDDP(raw_msg);\n    } catch (e) {\n      Meteor._debug('Exception while parsing DDP', e);\n      return;\n    }\n\n    // Any message counts as receiving a pong, as it demonstrates that\n    // the server is still alive.\n    if (this._connection._heartbeat) {\n      this._connection._heartbeat.messageReceived();\n    }\n\n    if (msg === null || !msg.msg) {\n      if(!msg || !msg.testMessageOnConnect) {\n        if (Object.keys(msg).length === 1 && msg.server_id) return;\n        Meteor._debug('discarding invalid livedata message', msg);\n      }\n      return;\n    }\n\n    // Important: This was missing from previous version\n    // We need to set the current version before routing the message\n    if (msg.msg === 'connected') {\n      this._connection._version = this._connection._versionSuggestion;\n    }\n\n    await this._routeMessage(msg);\n  }\n\n  /**\n   * Routes messages to their appropriate handlers based on message type\n   * @private\n   * @param {Object} msg The parsed DDP message\n   */\n  async _routeMessage(msg) {\n    switch (msg.msg) {\n      case 'connected':\n        await this._connection._livedata_connected(msg);\n        this._connection.options.onConnected();\n        break;\n\n      case 'failed':\n        await this._handleFailedMessage(msg);\n        break;\n\n      case 'ping':\n        if (this._connection.options.respondToPings) {\n          this._connection._send({ msg: 'pong', id: msg.id });\n        }\n        break;\n\n      case 'pong':\n        // noop, as we assume everything's a pong\n        break;\n\n      case 'added':\n      case 'changed':\n      case 'removed':\n      case 'ready':\n      case 'updated':\n        await this._connection._livedata_data(msg);\n        break;\n\n      case 'nosub':\n        await this._connection._livedata_nosub(msg);\n        break;\n\n      case 'result':\n        await this._connection._livedata_result(msg);\n        break;\n\n      case 'error':\n        this._connection._livedata_error(msg);\n        break;\n\n      default:\n        Meteor._debug('discarding unknown livedata message type', msg);\n    }\n  }\n\n  /**\n   * Handles failed connection messages\n   * @private\n   * @param {Object} msg The failed message object\n   */\n  _handleFailedMessage(msg) {\n    if (this._connection._supportedDDPVersions.indexOf(msg.version) >= 0) {\n      this._connection._versionSuggestion = msg.version;\n      this._connection._stream.reconnect({ _force: true });\n    } else {\n      const description =\n        'DDP version negotiation failed; server requested version ' +\n        msg.version;\n      this._connection._stream.disconnect({ _permanent: true, _error: description });\n      this._connection.options.onDDPVersionNegotiationFailure(description);\n    }\n  }\n\n  /**\n   * Handles connection reset events\n   */\n  onReset() {\n    // Reset is called even on the first connection, so this is\n    // the only place we send this message.\n    const msg = this._buildConnectMessage();\n    this._connection._send(msg);\n\n    // Mark non-retry calls as failed and handle outstanding methods\n    this._handleOutstandingMethodsOnReset();\n\n    // Now, to minimize setup latency, go ahead and blast out all of\n    // our pending methods ands subscriptions before we've even taken\n    // the necessary RTT to know if we successfully reconnected.\n    this._connection._callOnReconnectAndSendAppropriateOutstandingMethods();\n    this._resendSubscriptions();\n  }\n\n  /**\n   * Builds the initial connect message\n   * @private\n   * @returns {Object} The connect message object\n   */\n  _buildConnectMessage() {\n    const msg = { msg: 'connect' };\n    if (this._connection._lastSessionId) {\n      msg.session = this._connection._lastSessionId;\n    }\n    msg.version = this._connection._versionSuggestion || this._connection._supportedDDPVersions[0];\n    this._connection._versionSuggestion = msg.version;\n    msg.support = this._connection._supportedDDPVersions;\n    return msg;\n  }\n\n  /**\n   * Handles outstanding methods during a reset\n   * @private\n   */\n  _handleOutstandingMethodsOnReset() {\n    const blocks = this._connection._outstandingMethodBlocks;\n    if (blocks.length === 0) return;\n\n    const currentMethodBlock = blocks[0].methods;\n    blocks[0].methods = currentMethodBlock.filter(\n      methodInvoker => {\n        // Methods with 'noRetry' option set are not allowed to re-send after\n        // recovering dropped connection.\n        if (methodInvoker.sentMessage && methodInvoker.noRetry) {\n          methodInvoker.receiveResult(\n            new Meteor.Error(\n              'invocation-failed',\n              'Method invocation might have failed due to dropped connection. ' +\n              'Failing because `noRetry` option was passed to Meteor.apply.'\n            )\n          );\n        }\n\n        // Only keep a method if it wasn't sent or it's allowed to retry.\n        return !(methodInvoker.sentMessage && methodInvoker.noRetry);\n      }\n    );\n\n    // Clear empty blocks\n    if (blocks.length > 0 && blocks[0].methods.length === 0) {\n      blocks.shift();\n    }\n\n    // Reset all method invokers as unsent\n    Object.values(this._connection._methodInvokers).forEach(invoker => {\n      invoker.sentMessage = false;\n    });\n  }\n\n  /**\n   * Resends all active subscriptions\n   * @private\n   */\n  _resendSubscriptions() {\n    Object.entries(this._connection._subscriptions).forEach(([id, sub]) => {\n      this._connection._sendQueued({\n        msg: 'sub',\n        id: id,\n        name: sub.name,\n        params: sub.params\n      });\n    });\n  }\n}", "import { MongoID } from 'meteor/mongo-id';\nimport { DiffSequence } from 'meteor/diff-sequence';\nimport { hasOwn } from \"meteor/ddp-common/utils\";\nimport { isEmpty } from \"meteor/ddp-common/utils\";\n\nexport class DocumentProcessors {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * @summary Process an 'added' message from the server\n   * @param {Object} msg The added message\n   * @param {Object} updates The updates accumulator\n   */\n  async _process_added(msg, updates) {\n    const self = this._connection;\n    const id = MongoID.idParse(msg.id);\n    const serverDoc = self._getServerDoc(msg.collection, id);\n\n    if (serverDoc) {\n      // Some outstanding stub wrote here.\n      const isExisting = serverDoc.document !== undefined;\n\n      serverDoc.document = msg.fields || Object.create(null);\n      serverDoc.document._id = id;\n\n      if (self._resetStores) {\n        // During reconnect the server is sending adds for existing ids.\n        // Always push an update so that document stays in the store after\n        // reset. Use current version of the document for this update, so\n        // that stub-written values are preserved.\n        const currentDoc = await self._stores[msg.collection].getDoc(msg.id);\n        if (currentDoc !== undefined) msg.fields = currentDoc;\n\n        self._pushUpdate(updates, msg.collection, msg);\n      } else if (isExisting) {\n        throw new Error('Server sent add for existing id: ' + msg.id);\n      }\n    } else {\n      self._pushUpdate(updates, msg.collection, msg);\n    }\n  }\n\n  /**\n   * @summary Process a 'changed' message from the server\n   * @param {Object} msg The changed message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_changed(msg, updates) {\n    const self = this._connection;\n    const serverDoc = self._getServerDoc(msg.collection, MongoID.idParse(msg.id));\n\n    if (serverDoc) {\n      if (serverDoc.document === undefined) {\n        throw new Error('Server sent changed for nonexisting id: ' + msg.id);\n      }\n      DiffSequence.applyChanges(serverDoc.document, msg.fields);\n    } else {\n      self._pushUpdate(updates, msg.collection, msg);\n    }\n  }\n\n  /**\n   * @summary Process a 'removed' message from the server\n   * @param {Object} msg The removed message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_removed(msg, updates) {\n    const self = this._connection;\n    const serverDoc = self._getServerDoc(msg.collection, MongoID.idParse(msg.id));\n\n    if (serverDoc) {\n      // Some outstanding stub wrote here.\n      if (serverDoc.document === undefined) {\n        throw new Error('Server sent removed for nonexisting id:' + msg.id);\n      }\n      serverDoc.document = undefined;\n    } else {\n      self._pushUpdate(updates, msg.collection, {\n        msg: 'removed',\n        collection: msg.collection,\n        id: msg.id\n      });\n    }\n  }\n\n  /**\n   * @summary Process a 'ready' message from the server\n   * @param {Object} msg The ready message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_ready(msg, updates) {\n    const self = this._connection;\n\n    // Process \"sub ready\" messages. \"sub ready\" messages don't take effect\n    // until all current server documents have been flushed to the local\n    // database. We can use a write fence to implement this.\n    msg.subs.forEach((subId) => {\n      self._runWhenAllServerDocsAreFlushed(() => {\n        const subRecord = self._subscriptions[subId];\n        // Did we already unsubscribe?\n        if (!subRecord) return;\n        // Did we already receive a ready message? (Oops!)\n        if (subRecord.ready) return;\n        subRecord.ready = true;\n        subRecord.readyCallback && subRecord.readyCallback();\n        subRecord.readyDeps.changed();\n      });\n    });\n  }\n\n  /**\n   * @summary Process an 'updated' message from the server\n   * @param {Object} msg The updated message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_updated(msg, updates) {\n    const self = this._connection;\n    // Process \"method done\" messages.\n    msg.methods.forEach((methodId) => {\n      const docs = self._documentsWrittenByStub[methodId] || {};\n      Object.values(docs).forEach((written) => {\n        const serverDoc = self._getServerDoc(written.collection, written.id);\n        if (!serverDoc) {\n          throw new Error('Lost serverDoc for ' + JSON.stringify(written));\n        }\n        if (!serverDoc.writtenByStubs[methodId]) {\n          throw new Error(\n            'Doc ' +\n            JSON.stringify(written) +\n            ' not written by method ' +\n            methodId\n          );\n        }\n        delete serverDoc.writtenByStubs[methodId];\n        if (isEmpty(serverDoc.writtenByStubs)) {\n          // All methods whose stubs wrote this method have completed! We can\n          // now copy the saved document to the database (reverting the stub's\n          // change if the server did not write to this object, or applying the\n          // server's writes if it did).\n\n          // This is a fake ddp 'replace' message.  It's just for talking\n          // between livedata connections and minimongo.  (We have to stringify\n          // the ID because it's supposed to look like a wire message.)\n          self._pushUpdate(updates, written.collection, {\n            msg: 'replace',\n            id: MongoID.idStringify(written.id),\n            replace: serverDoc.document\n          });\n          // Call all flush callbacks.\n          serverDoc.flushCallbacks.forEach((c) => {\n            c();\n          });\n\n          // Delete this completed serverDocument. Don't bother to GC empty\n          // IdMaps inside self._serverDocuments, since there probably aren't\n          // many collections and they'll be written repeatedly.\n          self._serverDocuments[written.collection].remove(written.id);\n        }\n      });\n      delete self._documentsWrittenByStub[methodId];\n\n      // We want to call the data-written callback, but we can't do so until all\n      // currently buffered messages are flushed.\n      const callbackInvoker = self._methodInvokers[methodId];\n      if (!callbackInvoker) {\n        throw new Error('No callback invoker for method ' + methodId);\n      }\n\n      self._runWhenAllServerDocsAreFlushed(\n        (...args) => callbackInvoker.dataVisible(...args)\n      );\n    });\n  }\n\n  /**\n   * @summary Push an update to the buffer\n   * @private\n   * @param {Object} updates The updates accumulator\n   * @param {String} collection The collection name\n   * @param {Object} msg The update message\n   */\n  _pushUpdate(updates, collection, msg) {\n    if (!hasOwn.call(updates, collection)) {\n      updates[collection] = [];\n    }\n    updates[collection].push(msg);\n  }\n\n  /**\n   * @summary Get a server document by collection and id\n   * @private\n   * @param {String} collection The collection name\n   * @param {String} id The document id\n   * @returns {Object|null} The server document or null\n   */\n  _getServerDoc(collection, id) {\n    const self = this._connection;\n    if (!hasOwn.call(self._serverDocuments, collection)) {\n      return null;\n    }\n    const serverDocsForCollection = self._serverDocuments[collection];\n    return serverDocsForCollection.get(id) || null;\n  }\n}", "import { Meteor } from 'meteor/meteor';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'meteor/ddp-common';\nimport { Tracker } from 'meteor/tracker';\nimport { EJSON } from 'meteor/ejson';\nimport { Random } from 'meteor/random';\nimport { MongoID } from 'meteor/mongo-id';\nimport { DDP } from './namespace.js';\nimport { MethodInvoker } from './method_invoker';\nimport {\n  hasOwn,\n  slice,\n  keys,\n  isEmpty,\n  last,\n} from \"meteor/ddp-common/utils\";\nimport { ConnectionStreamHandlers } from './connection_stream_handlers';\nimport { MongoIDMap } from './mongo_id_map';\nimport { MessageProcessors } from './message_processors';\nimport { DocumentProcessors } from './document_processors';\n\n// @param url {String|Object} URL to Meteor app,\n//   or an object as a test hook (see code)\n// Options:\n//   reloadWithOutstanding: is it OK to reload if there are outstanding methods?\n//   headers: extra headers to send on the websockets connection, for\n//     server-to-server DDP only\n//   _sockjsOptions: Specifies options to pass through to the sockjs client\n//   onDDPNegotiationVersionFailure: callback when version negotiation fails.\n//\n// XXX There should be a way to destroy a DDP connection, causing all\n// outstanding method calls to fail.\n//\n// XXX Our current way of handling failure and reconnection is great\n// for an app (where we want to tolerate being disconnected as an\n// expect state, and keep trying forever to reconnect) but cumbersome\n// for something like a command line tool that wants to make a\n// connection, call a method, and print an error if connection\n// fails. We should have better usability in the latter case (while\n// still transparently reconnecting if it's just a transient failure\n// or the server migrating us).\nexport class Connection {\n  constructor(url, options) {\n    const self = this;\n\n    this.options = options = {\n      onConnected() {},\n      onDDPVersionNegotiationFailure(description) {\n        Meteor._debug(description);\n      },\n      heartbeatInterval: 17500,\n      heartbeatTimeout: 15000,\n      npmFayeOptions: Object.create(null),\n      // These options are only for testing.\n      reloadWithOutstanding: false,\n      supportedDDPVersions: DDPCommon.SUPPORTED_DDP_VERSIONS,\n      retry: true,\n      respondToPings: true,\n      // When updates are coming within this ms interval, batch them together.\n      bufferedWritesInterval: 5,\n      // Flush buffers immediately if writes are happening continuously for more than this many ms.\n      bufferedWritesMaxAge: 500,\n\n      ...options\n    };\n\n    // If set, called when we reconnect, queuing method calls _before_ the\n    // existing outstanding ones.\n    // NOTE: This feature has been preserved for backwards compatibility. The\n    // preferred method of setting a callback on reconnect is to use\n    // DDP.onReconnect.\n    self.onReconnect = null;\n\n    // as a test hook, allow passing a stream instead of a url.\n    if (typeof url === 'object') {\n      self._stream = url;\n    } else {\n      import { ClientStream } from \"meteor/socket-stream-client\";\n\n      self._stream = new ClientStream(url, {\n        retry: options.retry,\n        ConnectionError: DDP.ConnectionError,\n        headers: options.headers,\n        _sockjsOptions: options._sockjsOptions,\n        // Used to keep some tests quiet, or for other cases in which\n        // the right thing to do with connection errors is to silently\n        // fail (e.g. sending package usage stats). At some point we\n        // should have a real API for handling client-stream-level\n        // errors.\n        _dontPrintErrors: options._dontPrintErrors,\n        connectTimeoutMs: options.connectTimeoutMs,\n        npmFayeOptions: options.npmFayeOptions\n      });\n    }\n\n    self._lastSessionId = null;\n    self._versionSuggestion = null; // The last proposed DDP version.\n    self._version = null; // The DDP version agreed on by client and server.\n    self._stores = Object.create(null); // name -> object with methods\n    self._methodHandlers = Object.create(null); // name -> func\n    self._nextMethodId = 1;\n    self._supportedDDPVersions = options.supportedDDPVersions;\n\n    self._heartbeatInterval = options.heartbeatInterval;\n    self._heartbeatTimeout = options.heartbeatTimeout;\n\n    // Tracks methods which the user has tried to call but which have not yet\n    // called their user callback (ie, they are waiting on their result or for all\n    // of their writes to be written to the local cache). Map from method ID to\n    // MethodInvoker object.\n    self._methodInvokers = Object.create(null);\n\n    // Tracks methods which the user has called but whose result messages have not\n    // arrived yet.\n    //\n    // _outstandingMethodBlocks is an array of blocks of methods. Each block\n    // represents a set of methods that can run at the same time. The first block\n    // represents the methods which are currently in flight; subsequent blocks\n    // must wait for previous blocks to be fully finished before they can be sent\n    // to the server.\n    //\n    // Each block is an object with the following fields:\n    // - methods: a list of MethodInvoker objects\n    // - wait: a boolean; if true, this block had a single method invoked with\n    //         the \"wait\" option\n    //\n    // There will never be adjacent blocks with wait=false, because the only thing\n    // that makes methods need to be serialized is a wait method.\n    //\n    // Methods are removed from the first block when their \"result\" is\n    // received. The entire first block is only removed when all of the in-flight\n    // methods have received their results (so the \"methods\" list is empty) *AND*\n    // all of the data written by those methods are visible in the local cache. So\n    // it is possible for the first block's methods list to be empty, if we are\n    // still waiting for some objects to quiesce.\n    //\n    // Example:\n    //  _outstandingMethodBlocks = [\n    //    {wait: false, methods: []},\n    //    {wait: true, methods: [<MethodInvoker for 'login'>]},\n    //    {wait: false, methods: [<MethodInvoker for 'foo'>,\n    //                            <MethodInvoker for 'bar'>]}]\n    // This means that there were some methods which were sent to the server and\n    // which have returned their results, but some of the data written by\n    // the methods may not be visible in the local cache. Once all that data is\n    // visible, we will send a 'login' method. Once the login method has returned\n    // and all the data is visible (including re-running subs if userId changes),\n    // we will send the 'foo' and 'bar' methods in parallel.\n    self._outstandingMethodBlocks = [];\n\n    // method ID -> array of objects with keys 'collection' and 'id', listing\n    // documents written by a given method's stub. keys are associated with\n    // methods whose stub wrote at least one document, and whose data-done message\n    // has not yet been received.\n    self._documentsWrittenByStub = {};\n    // collection -> IdMap of \"server document\" object. A \"server document\" has:\n    // - \"document\": the version of the document according the\n    //   server (ie, the snapshot before a stub wrote it, amended by any changes\n    //   received from the server)\n    //   It is undefined if we think the document does not exist\n    // - \"writtenByStubs\": a set of method IDs whose stubs wrote to the document\n    //   whose \"data done\" messages have not yet been processed\n    self._serverDocuments = {};\n\n    // Array of callbacks to be called after the next update of the local\n    // cache. Used for:\n    //  - Calling methodInvoker.dataVisible and sub ready callbacks after\n    //    the relevant data is flushed.\n    //  - Invoking the callbacks of \"half-finished\" methods after reconnect\n    //    quiescence. Specifically, methods whose result was received over the old\n    //    connection (so we don't re-send it) but whose data had not been made\n    //    visible.\n    self._afterUpdateCallbacks = [];\n\n    // In two contexts, we buffer all incoming data messages and then process them\n    // all at once in a single update:\n    //   - During reconnect, we buffer all data messages until all subs that had\n    //     been ready before reconnect are ready again, and all methods that are\n    //     active have returned their \"data done message\"; then\n    //   - During the execution of a \"wait\" method, we buffer all data messages\n    //     until the wait method gets its \"data done\" message. (If the wait method\n    //     occurs during reconnect, it doesn't get any special handling.)\n    // all data messages are processed in one update.\n    //\n    // The following fields are used for this \"quiescence\" process.\n\n    // This buffers the messages that aren't being processed yet.\n    self._messagesBufferedUntilQuiescence = [];\n    // Map from method ID -> true. Methods are removed from this when their\n    // \"data done\" message is received, and we will not quiesce until it is\n    // empty.\n    self._methodsBlockingQuiescence = {};\n    // map from sub ID -> true for subs that were ready (ie, called the sub\n    // ready callback) before reconnect but haven't become ready again yet\n    self._subsBeingRevived = {}; // map from sub._id -> true\n    // if true, the next data update should reset all stores. (set during\n    // reconnect.)\n    self._resetStores = false;\n\n    // name -> array of updates for (yet to be created) collections\n    self._updatesForUnknownStores = {};\n    // if we're blocking a migration, the retry func\n    self._retryMigrate = null;\n    // Collection name -> array of messages.\n    self._bufferedWrites = {};\n    // When current buffer of updates must be flushed at, in ms timestamp.\n    self._bufferedWritesFlushAt = null;\n    // Timeout handle for the next processing of all pending writes\n    self._bufferedWritesFlushHandle = null;\n\n    self._bufferedWritesInterval = options.bufferedWritesInterval;\n    self._bufferedWritesMaxAge = options.bufferedWritesMaxAge;\n\n    // metadata for subscriptions.  Map from sub ID to object with keys:\n    //   - id\n    //   - name\n    //   - params\n    //   - inactive (if true, will be cleaned up if not reused in re-run)\n    //   - ready (has the 'ready' message been received?)\n    //   - readyCallback (an optional callback to call when ready)\n    //   - errorCallback (an optional callback to call if the sub terminates with\n    //                    an error, XXX COMPAT WITH *******)\n    //   - stopCallback (an optional callback to call when the sub terminates\n    //     for any reason, with an error argument if an error triggered the stop)\n    self._subscriptions = {};\n\n    // Reactive userId.\n    self._userId = null;\n    self._userIdDeps = new Tracker.Dependency();\n\n    // Block auto-reload while we're waiting for method responses.\n    if (Meteor.isClient &&\n      Package.reload &&\n      ! options.reloadWithOutstanding) {\n      Package.reload.Reload._onMigrate(retry => {\n        if (! self._readyToMigrate()) {\n          self._retryMigrate = retry;\n          return [false];\n        } else {\n          return [true];\n        }\n      });\n    }\n\n    this._streamHandlers = new ConnectionStreamHandlers(this);\n\n    const onDisconnect = () => {\n      if (this._heartbeat) {\n        this._heartbeat.stop();\n        this._heartbeat = null;\n      }\n    };\n\n    if (Meteor.isServer) {\n      this._stream.on(\n        'message',\n        Meteor.bindEnvironment(\n          msg => this._streamHandlers.onMessage(msg),\n          'handling DDP message'\n        )\n      );\n      this._stream.on(\n        'reset',\n        Meteor.bindEnvironment(\n          () => this._streamHandlers.onReset(),\n          'handling DDP reset'\n        )\n      );\n      this._stream.on(\n        'disconnect',\n        Meteor.bindEnvironment(onDisconnect, 'handling DDP disconnect')\n      );\n    } else {\n      this._stream.on('message', msg => this._streamHandlers.onMessage(msg));\n      this._stream.on('reset', () => this._streamHandlers.onReset());\n      this._stream.on('disconnect', onDisconnect);\n    }\n\n    this._messageProcessors = new MessageProcessors(this);\n\n    // Expose message processor methods to maintain backward compatibility\n    this._livedata_connected = (msg) => this._messageProcessors._livedata_connected(msg);\n    this._livedata_data = (msg) => this._messageProcessors._livedata_data(msg);\n    this._livedata_nosub = (msg) => this._messageProcessors._livedata_nosub(msg);\n    this._livedata_result = (msg) => this._messageProcessors._livedata_result(msg);\n    this._livedata_error = (msg) => this._messageProcessors._livedata_error(msg);\n\n    this._documentProcessors = new DocumentProcessors(this);\n\n    // Expose document processor methods to maintain backward compatibility\n    this._process_added = (msg, updates) => this._documentProcessors._process_added(msg, updates);\n    this._process_changed = (msg, updates) => this._documentProcessors._process_changed(msg, updates);\n    this._process_removed = (msg, updates) => this._documentProcessors._process_removed(msg, updates);\n    this._process_ready = (msg, updates) => this._documentProcessors._process_ready(msg, updates);\n    this._process_updated = (msg, updates) => this._documentProcessors._process_updated(msg, updates);\n\n    // Also expose utility methods used by other parts of the system\n    this._pushUpdate = (updates, collection, msg) =>\n      this._documentProcessors._pushUpdate(updates, collection, msg);\n    this._getServerDoc = (collection, id) =>\n      this._documentProcessors._getServerDoc(collection, id);\n  }\n\n  // 'name' is the name of the data on the wire that should go in the\n  // store. 'wrappedStore' should be an object with methods beginUpdate, update,\n  // endUpdate, saveOriginals, retrieveOriginals. see Collection for an example.\n  createStoreMethods(name, wrappedStore) {\n    const self = this;\n\n    if (name in self._stores) return false;\n\n    // Wrap the input object in an object which makes any store method not\n    // implemented by 'store' into a no-op.\n    const store = Object.create(null);\n    const keysOfStore = [\n      'update',\n      'beginUpdate',\n      'endUpdate',\n      'saveOriginals',\n      'retrieveOriginals',\n      'getDoc',\n      '_getCollection'\n    ];\n    keysOfStore.forEach((method) => {\n      store[method] = (...args) => {\n        if (wrappedStore[method]) {\n          return wrappedStore[method](...args);\n        }\n      };\n    });\n    self._stores[name] = store;\n    return store;\n  }\n\n  registerStoreClient(name, wrappedStore) {\n    const self = this;\n\n    const store = self.createStoreMethods(name, wrappedStore);\n\n    const queued = self._updatesForUnknownStores[name];\n    if (Array.isArray(queued)) {\n      store.beginUpdate(queued.length, false);\n      queued.forEach(msg => {\n        store.update(msg);\n      });\n      store.endUpdate();\n      delete self._updatesForUnknownStores[name];\n    }\n\n    return true;\n  }\n  async registerStoreServer(name, wrappedStore) {\n    const self = this;\n\n    const store = self.createStoreMethods(name, wrappedStore);\n\n    const queued = self._updatesForUnknownStores[name];\n    if (Array.isArray(queued)) {\n      await store.beginUpdate(queued.length, false);\n      for (const msg of queued) {\n        await store.update(msg);\n      }\n      await store.endUpdate();\n      delete self._updatesForUnknownStores[name];\n    }\n\n    return true;\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.subscribe\n   * @summary Subscribe to a record set.  Returns a handle that provides\n   * `stop()` and `ready()` methods.\n   * @locus Client\n   * @param {String} name Name of the subscription.  Matches the name of the\n   * server's `publish()` call.\n   * @param {EJSONable} [arg1,arg2...] Optional arguments passed to publisher\n   * function on server.\n   * @param {Function|Object} [callbacks] Optional. May include `onStop`\n   * and `onReady` callbacks. If there is an error, it is passed as an\n   * argument to `onStop`. If a function is passed instead of an object, it\n   * is interpreted as an `onReady` callback.\n   */\n  subscribe(name /* .. [arguments] .. (callback|callbacks) */) {\n    const self = this;\n\n    const params = slice.call(arguments, 1);\n    let callbacks = Object.create(null);\n    if (params.length) {\n      const lastParam = params[params.length - 1];\n      if (typeof lastParam === 'function') {\n        callbacks.onReady = params.pop();\n      } else if (lastParam && [\n        lastParam.onReady,\n        // XXX COMPAT WITH ******* onError used to exist, but now we use\n        // onStop with an error callback instead.\n        lastParam.onError,\n        lastParam.onStop\n      ].some(f => typeof f === \"function\")) {\n        callbacks = params.pop();\n      }\n    }\n\n    // Is there an existing sub with the same name and param, run in an\n    // invalidated Computation? This will happen if we are rerunning an\n    // existing computation.\n    //\n    // For example, consider a rerun of:\n    //\n    //     Tracker.autorun(function () {\n    //       Meteor.subscribe(\"foo\", Session.get(\"foo\"));\n    //       Meteor.subscribe(\"bar\", Session.get(\"bar\"));\n    //     });\n    //\n    // If \"foo\" has changed but \"bar\" has not, we will match the \"bar\"\n    // subcribe to an existing inactive subscription in order to not\n    // unsub and resub the subscription unnecessarily.\n    //\n    // We only look for one such sub; if there are N apparently-identical subs\n    // being invalidated, we will require N matching subscribe calls to keep\n    // them all active.\n    const existing = Object.values(self._subscriptions).find(\n      sub => (sub.inactive && sub.name === name && EJSON.equals(sub.params, params))\n    );\n\n    let id;\n    if (existing) {\n      id = existing.id;\n      existing.inactive = false; // reactivate\n\n      if (callbacks.onReady) {\n        // If the sub is not already ready, replace any ready callback with the\n        // one provided now. (It's not really clear what users would expect for\n        // an onReady callback inside an autorun; the semantics we provide is\n        // that at the time the sub first becomes ready, we call the last\n        // onReady callback provided, if any.)\n        // If the sub is already ready, run the ready callback right away.\n        // It seems that users would expect an onReady callback inside an\n        // autorun to trigger once the sub first becomes ready and also\n        // when re-subs happens.\n        if (existing.ready) {\n          callbacks.onReady();\n        } else {\n          existing.readyCallback = callbacks.onReady;\n        }\n      }\n\n      // XXX COMPAT WITH ******* we used to have onError but now we call\n      // onStop with an optional error argument\n      if (callbacks.onError) {\n        // Replace existing callback if any, so that errors aren't\n        // double-reported.\n        existing.errorCallback = callbacks.onError;\n      }\n\n      if (callbacks.onStop) {\n        existing.stopCallback = callbacks.onStop;\n      }\n    } else {\n      // New sub! Generate an id, save it locally, and send message.\n      id = Random.id();\n      self._subscriptions[id] = {\n        id: id,\n        name: name,\n        params: EJSON.clone(params),\n        inactive: false,\n        ready: false,\n        readyDeps: new Tracker.Dependency(),\n        readyCallback: callbacks.onReady,\n        // XXX COMPAT WITH ******* #errorCallback\n        errorCallback: callbacks.onError,\n        stopCallback: callbacks.onStop,\n        connection: self,\n        remove() {\n          delete this.connection._subscriptions[this.id];\n          this.ready && this.readyDeps.changed();\n        },\n        stop() {\n          this.connection._sendQueued({ msg: 'unsub', id: id });\n          this.remove();\n\n          if (callbacks.onStop) {\n            callbacks.onStop();\n          }\n        }\n      };\n      self._send({ msg: 'sub', id: id, name: name, params: params });\n    }\n\n    // return a handle to the application.\n    const handle = {\n      stop() {\n        if (! hasOwn.call(self._subscriptions, id)) {\n          return;\n        }\n        self._subscriptions[id].stop();\n      },\n      ready() {\n        // return false if we've unsubscribed.\n        if (!hasOwn.call(self._subscriptions, id)) {\n          return false;\n        }\n        const record = self._subscriptions[id];\n        record.readyDeps.depend();\n        return record.ready;\n      },\n      subscriptionId: id\n    };\n\n    if (Tracker.active) {\n      // We're in a reactive computation, so we'd like to unsubscribe when the\n      // computation is invalidated... but not if the rerun just re-subscribes\n      // to the same subscription!  When a rerun happens, we use onInvalidate\n      // as a change to mark the subscription \"inactive\" so that it can\n      // be reused from the rerun.  If it isn't reused, it's killed from\n      // an afterFlush.\n      Tracker.onInvalidate((c) => {\n        if (hasOwn.call(self._subscriptions, id)) {\n          self._subscriptions[id].inactive = true;\n        }\n\n        Tracker.afterFlush(() => {\n          if (hasOwn.call(self._subscriptions, id) &&\n              self._subscriptions[id].inactive) {\n            handle.stop();\n          }\n        });\n      });\n    }\n\n    return handle;\n  }\n\n  /**\n   * @summary Tells if the method call came from a call or a callAsync.\n   * @alias Meteor.isAsyncCall\n   * @locus Anywhere\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @returns boolean\n   */\n  isAsyncCall(){\n    return DDP._CurrentMethodInvocation._isCallAsyncMethodRunning()\n  }\n  methods(methods) {\n    Object.entries(methods).forEach(([name, func]) => {\n      if (typeof func !== 'function') {\n        throw new Error(\"Method '\" + name + \"' must be a function\");\n      }\n      if (this._methodHandlers[name]) {\n        throw new Error(\"A method named '\" + name + \"' is already defined\");\n      }\n      this._methodHandlers[name] = func;\n    });\n  }\n\n  _getIsSimulation({isFromCallAsync, alreadyInSimulation}) {\n    if (!isFromCallAsync) {\n      return alreadyInSimulation;\n    }\n    return alreadyInSimulation && DDP._CurrentMethodInvocation._isCallAsyncMethodRunning();\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.call\n   * @summary Invokes a method with a sync stub, passing any number of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable} [arg1,arg2...] Optional method arguments\n   * @param {Function} [asyncCallback] Optional callback, which is called asynchronously with the error or result after the method is complete. If not provided, the method runs synchronously if possible (see below).\n   */\n  call(name /* .. [arguments] .. callback */) {\n    // if it's a function, the last argument is the result callback,\n    // not a parameter to the remote method.\n    const args = slice.call(arguments, 1);\n    let callback;\n    if (args.length && typeof args[args.length - 1] === 'function') {\n      callback = args.pop();\n    }\n    return this.apply(name, args, callback);\n  }\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.callAsync\n   * @summary Invokes a method with an async stub, passing any number of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable} [arg1,arg2...] Optional method arguments\n   * @returns {Promise}\n   */\n  callAsync(name /* .. [arguments] .. */) {\n    const args = slice.call(arguments, 1);\n    if (args.length && typeof args[args.length - 1] === 'function') {\n      throw new Error(\n        \"Meteor.callAsync() does not accept a callback. You should 'await' the result, or use .then().\"\n      );\n    }\n\n    return this.applyAsync(name, args, { returnServerResultPromise: true });\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.apply\n   * @summary Invoke a method passing an array of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable[]} args Method arguments\n   * @param {Object} [options]\n   * @param {Boolean} options.wait (Client only) If true, don't send this method until all previous method calls have completed, and don't send any subsequent method calls until this one is completed.\n   * @param {Function} options.onResultReceived (Client only) This callback is invoked with the error or result of the method (just like `asyncCallback`) as soon as the error or result is available. The local cache may not yet reflect the writes performed by the method.\n   * @param {Boolean} options.noRetry (Client only) if true, don't send this method again on reload, simply call the callback an error with the error code 'invocation-failed'.\n   * @param {Boolean} options.throwStubExceptions (Client only) If true, exceptions thrown by method stubs will be thrown instead of logged, and the method will not be invoked on the server.\n   * @param {Boolean} options.returnStubValue (Client only) If true then in cases where we would have otherwise discarded the stub's return value and returned undefined, instead we go ahead and return it. Specifically, this is any time other than when (a) we are already inside a stub or (b) we are in Node and no callback was provided. Currently we require this flag to be explicitly passed to reduce the likelihood that stub return values will be confused with server return values; we may improve this in future.\n   * @param {Function} [asyncCallback] Optional callback; same semantics as in [`Meteor.call`](#meteor_call).\n   */\n  apply(name, args, options, callback) {\n    const { stubInvocation, invocation, ...stubOptions } = this._stubCall(name, EJSON.clone(args));\n\n    if (stubOptions.hasStub) {\n      if (\n        !this._getIsSimulation({\n          alreadyInSimulation: stubOptions.alreadyInSimulation,\n          isFromCallAsync: stubOptions.isFromCallAsync,\n        })\n      ) {\n        this._saveOriginals();\n      }\n      try {\n        stubOptions.stubReturnValue = DDP._CurrentMethodInvocation\n          .withValue(invocation, stubInvocation);\n        if (Meteor._isPromise(stubOptions.stubReturnValue)) {\n          Meteor._debug(\n            `Method ${name}: Calling a method that has an async method stub with call/apply can lead to unexpected behaviors. Use callAsync/applyAsync instead.`\n          );\n        }\n      } catch (e) {\n        stubOptions.exception = e;\n      }\n    }\n    return this._apply(name, stubOptions, args, options, callback);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.applyAsync\n   * @summary Invoke a method passing an array of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable[]} args Method arguments\n   * @param {Object} [options]\n   * @param {Boolean} options.wait (Client only) If true, don't send this method until all previous method calls have completed, and don't send any subsequent method calls until this one is completed.\n   * @param {Function} options.onResultReceived (Client only) This callback is invoked with the error or result of the method (just like `asyncCallback`) as soon as the error or result is available. The local cache may not yet reflect the writes performed by the method.\n   * @param {Boolean} options.noRetry (Client only) if true, don't send this method again on reload, simply call the callback an error with the error code 'invocation-failed'.\n   * @param {Boolean} options.throwStubExceptions (Client only) If true, exceptions thrown by method stubs will be thrown instead of logged, and the method will not be invoked on the server.\n   * @param {Boolean} options.returnStubValue (Client only) If true then in cases where we would have otherwise discarded the stub's return value and returned undefined, instead we go ahead and return it. Specifically, this is any time other than when (a) we are already inside a stub or (b) we are in Node and no callback was provided. Currently we require this flag to be explicitly passed to reduce the likelihood that stub return values will be confused with server return values; we may improve this in future.\n   * @param {Boolean} options.returnServerResultPromise (Client only) If true, the promise returned by applyAsync will resolve to the server's return value, rather than the stub's return value. This is useful when you want to ensure that the server's return value is used, even if the stub returns a promise. The same behavior as `callAsync`.\n   */\n  applyAsync(name, args, options, callback = null) {\n    const stubPromise = this._applyAsyncStubInvocation(name, args, options);\n\n    const promise = this._applyAsync({\n      name,\n      args,\n      options,\n      callback,\n      stubPromise,\n    });\n    if (Meteor.isClient) {\n      // only return the stubReturnValue\n      promise.stubPromise = stubPromise.then(o => {\n        if (o.exception) {\n          throw o.exception;\n        }\n        return o.stubReturnValue;\n      });\n      // this avoids attribute recursion\n      promise.serverPromise = new Promise((resolve, reject) =>\n        promise.then(resolve).catch(reject),\n      );\n    }\n    return promise;\n  }\n  async _applyAsyncStubInvocation(name, args, options) {\n    const { stubInvocation, invocation, ...stubOptions } = this._stubCall(name, EJSON.clone(args), options);\n    if (stubOptions.hasStub) {\n      if (\n        !this._getIsSimulation({\n          alreadyInSimulation: stubOptions.alreadyInSimulation,\n          isFromCallAsync: stubOptions.isFromCallAsync,\n        })\n      ) {\n        this._saveOriginals();\n      }\n      try {\n        /*\n         * The code below follows the same logic as the function withValues().\n         *\n         * But as the Meteor package is not compiled by ecmascript, it is unable to use newer syntax in the browser,\n         * such as, the async/await.\n         *\n         * So, to keep supporting old browsers, like IE 11, we're creating the logic one level above.\n         */\n        const currentContext = DDP._CurrentMethodInvocation._setNewContextAndGetCurrent(\n          invocation\n        );\n        try {\n          stubOptions.stubReturnValue = await stubInvocation();\n        } catch (e) {\n          stubOptions.exception = e;\n        } finally {\n          DDP._CurrentMethodInvocation._set(currentContext);\n        }\n      } catch (e) {\n        stubOptions.exception = e;\n      }\n    }\n    return stubOptions;\n  }\n  async _applyAsync({ name, args, options, callback, stubPromise }) {\n    const stubOptions = await stubPromise;\n    return this._apply(name, stubOptions, args, options, callback);\n  }\n\n  _apply(name, stubCallValue, args, options, callback) {\n    const self = this;\n\n    // We were passed 3 arguments. They may be either (name, args, options)\n    // or (name, args, callback)\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = Object.create(null);\n    }\n    options = options || Object.create(null);\n\n    if (callback) {\n      // XXX would it be better form to do the binding in stream.on,\n      // or caller, instead of here?\n      // XXX improve error message (and how we report it)\n      callback = Meteor.bindEnvironment(\n        callback,\n        \"delivering result of invoking '\" + name + \"'\"\n      );\n    }\n    const {\n      hasStub,\n      exception,\n      stubReturnValue,\n      alreadyInSimulation,\n      randomSeed,\n    } = stubCallValue;\n\n    // Keep our args safe from mutation (eg if we don't send the message for a\n    // while because of a wait method).\n    args = EJSON.clone(args);\n    // If we're in a simulation, stop and return the result we have,\n    // rather than going on to do an RPC. If there was no stub,\n    // we'll end up returning undefined.\n    if (\n      this._getIsSimulation({\n        alreadyInSimulation,\n        isFromCallAsync: stubCallValue.isFromCallAsync,\n      })\n    ) {\n      let result;\n\n      if (callback) {\n        callback(exception, stubReturnValue);\n      } else {\n        if (exception) throw exception;\n        result = stubReturnValue;\n      }\n\n      return options._returnMethodInvoker ? { result } : result;\n    }\n\n    // We only create the methodId here because we don't actually need one if\n    // we're already in a simulation\n    const methodId = '' + self._nextMethodId++;\n    if (hasStub) {\n      self._retrieveAndStoreOriginals(methodId);\n    }\n\n    // Generate the DDP message for the method call. Note that on the client,\n    // it is important that the stub have finished before we send the RPC, so\n    // that we know we have a complete list of which local documents the stub\n    // wrote.\n    const message = {\n      msg: 'method',\n      id: methodId,\n      method: name,\n      params: args\n    };\n\n    // If an exception occurred in a stub, and we're ignoring it\n    // because we're doing an RPC and want to use what the server\n    // returns instead, log it so the developer knows\n    // (unless they explicitly ask to see the error).\n    //\n    // Tests can set the '_expectedByTest' flag on an exception so it won't\n    // go to log.\n    if (exception) {\n      if (options.throwStubExceptions) {\n        throw exception;\n      } else if (!exception._expectedByTest) {\n        Meteor._debug(\n          \"Exception while simulating the effect of invoking '\" + name + \"'\",\n          exception\n        );\n      }\n    }\n\n    // At this point we're definitely doing an RPC, and we're going to\n    // return the value of the RPC to the caller.\n\n    // If the caller didn't give a callback, decide what to do.\n    let promise;\n    if (!callback) {\n      if (\n        Meteor.isClient &&\n        !options.returnServerResultPromise &&\n        (!options.isFromCallAsync || options.returnStubValue)\n      ) {\n        callback = (err) => {\n          err && Meteor._debug(\"Error invoking Method '\" + name + \"'\", err);\n        };\n      } else {\n        promise = new Promise((resolve, reject) => {\n          callback = (...allArgs) => {\n            let args = Array.from(allArgs);\n            let err = args.shift();\n            if (err) {\n              reject(err);\n              return;\n            }\n            resolve(...args);\n          };\n        });\n      }\n    }\n\n    // Send the randomSeed only if we used it\n    if (randomSeed.value !== null) {\n      message.randomSeed = randomSeed.value;\n    }\n\n    const methodInvoker = new MethodInvoker({\n      methodId,\n      callback: callback,\n      connection: self,\n      onResultReceived: options.onResultReceived,\n      wait: !!options.wait,\n      message: message,\n      noRetry: !!options.noRetry\n    });\n\n    let result;\n\n    if (promise) {\n      result = options.returnStubValue ? promise.then(() => stubReturnValue) : promise;\n    } else {\n      result = options.returnStubValue ? stubReturnValue : undefined;\n    }\n\n    if (options._returnMethodInvoker) {\n      return {\n        methodInvoker,\n        result,\n      };\n    }\n\n    self._addOutstandingMethod(methodInvoker, options);\n    return result;\n  }\n\n  _stubCall(name, args, options) {\n    // Run the stub, if we have one. The stub is supposed to make some\n    // temporary writes to the database to give the user a smooth experience\n    // until the actual result of executing the method comes back from the\n    // server (whereupon the temporary writes to the database will be reversed\n    // during the beginUpdate/endUpdate process.)\n    //\n    // Normally, we ignore the return value of the stub (even if it is an\n    // exception), in favor of the real return value from the server. The\n    // exception is if the *caller* is a stub. In that case, we're not going\n    // to do a RPC, so we use the return value of the stub as our return\n    // value.\n    const self = this;\n    const enclosing = DDP._CurrentMethodInvocation.get();\n    const stub = self._methodHandlers[name];\n    const alreadyInSimulation = enclosing?.isSimulation;\n    const isFromCallAsync = enclosing?._isFromCallAsync;\n    const randomSeed = { value: null};\n\n    const defaultReturn = {\n      alreadyInSimulation,\n      randomSeed,\n      isFromCallAsync,\n    };\n    if (!stub) {\n      return { ...defaultReturn, hasStub: false };\n    }\n\n    // Lazily generate a randomSeed, only if it is requested by the stub.\n    // The random streams only have utility if they're used on both the client\n    // and the server; if the client doesn't generate any 'random' values\n    // then we don't expect the server to generate any either.\n    // Less commonly, the server may perform different actions from the client,\n    // and may in fact generate values where the client did not, but we don't\n    // have any client-side values to match, so even here we may as well just\n    // use a random seed on the server.  In that case, we don't pass the\n    // randomSeed to save bandwidth, and we don't even generate it to save a\n    // bit of CPU and to avoid consuming entropy.\n\n    const randomSeedGenerator = () => {\n      if (randomSeed.value === null) {\n        randomSeed.value = DDPCommon.makeRpcSeed(enclosing, name);\n      }\n      return randomSeed.value;\n    };\n\n    const setUserId = userId => {\n      self.setUserId(userId);\n    };\n\n    const invocation = new DDPCommon.MethodInvocation({\n      name,\n      isSimulation: true,\n      userId: self.userId(),\n      isFromCallAsync: options?.isFromCallAsync,\n      setUserId: setUserId,\n      randomSeed() {\n        return randomSeedGenerator();\n      }\n    });\n\n    // Note that unlike in the corresponding server code, we never audit\n    // that stubs check() their arguments.\n    const stubInvocation = () => {\n        if (Meteor.isServer) {\n          // Because saveOriginals and retrieveOriginals aren't reentrant,\n          // don't allow stubs to yield.\n          return Meteor._noYieldsAllowed(() => {\n            // re-clone, so that the stub can't affect our caller's values\n            return stub.apply(invocation, EJSON.clone(args));\n          });\n        } else {\n          return stub.apply(invocation, EJSON.clone(args));\n        }\n    };\n    return { ...defaultReturn, hasStub: true, stubInvocation, invocation };\n  }\n\n  // Before calling a method stub, prepare all stores to track changes and allow\n  // _retrieveAndStoreOriginals to get the original versions of changed\n  // documents.\n  _saveOriginals() {\n    if (! this._waitingForQuiescence()) {\n      this._flushBufferedWrites();\n    }\n\n    Object.values(this._stores).forEach((store) => {\n      store.saveOriginals();\n    });\n  }\n\n  // Retrieves the original versions of all documents modified by the stub for\n  // method 'methodId' from all stores and saves them to _serverDocuments (keyed\n  // by document) and _documentsWrittenByStub (keyed by method ID).\n  _retrieveAndStoreOriginals(methodId) {\n    const self = this;\n    if (self._documentsWrittenByStub[methodId])\n      throw new Error('Duplicate methodId in _retrieveAndStoreOriginals');\n\n    const docsWritten = [];\n\n    Object.entries(self._stores).forEach(([collection, store]) => {\n      const originals = store.retrieveOriginals();\n      // not all stores define retrieveOriginals\n      if (! originals) return;\n      originals.forEach((doc, id) => {\n        docsWritten.push({ collection, id });\n        if (! hasOwn.call(self._serverDocuments, collection)) {\n          self._serverDocuments[collection] = new MongoIDMap();\n        }\n        const serverDoc = self._serverDocuments[collection].setDefault(\n          id,\n          Object.create(null)\n        );\n        if (serverDoc.writtenByStubs) {\n          // We're not the first stub to write this doc. Just add our method ID\n          // to the record.\n          serverDoc.writtenByStubs[methodId] = true;\n        } else {\n          // First stub! Save the original value and our method ID.\n          serverDoc.document = doc;\n          serverDoc.flushCallbacks = [];\n          serverDoc.writtenByStubs = Object.create(null);\n          serverDoc.writtenByStubs[methodId] = true;\n        }\n      });\n    });\n    if (! isEmpty(docsWritten)) {\n      self._documentsWrittenByStub[methodId] = docsWritten;\n    }\n  }\n\n  // This is very much a private function we use to make the tests\n  // take up fewer server resources after they complete.\n  _unsubscribeAll() {\n    Object.values(this._subscriptions).forEach((sub) => {\n      // Avoid killing the autoupdate subscription so that developers\n      // still get hot code pushes when writing tests.\n      //\n      // XXX it's a hack to encode knowledge about autoupdate here,\n      // but it doesn't seem worth it yet to have a special API for\n      // subscriptions to preserve after unit tests.\n      if (sub.name !== 'meteor_autoupdate_clientVersions') {\n        sub.stop();\n      }\n    });\n  }\n\n  // Sends the DDP stringification of the given message object\n  _send(obj) {\n    this._stream.send(DDPCommon.stringifyDDP(obj));\n  }\n\n  // Always queues the call before sending the message\n  // Used, for example, on subscription.[id].stop() to make sure a \"sub\" message is always called before an \"unsub\" message\n  // https://github.com/meteor/meteor/issues/13212\n  //\n  // This is part of the actual fix for the rest check:\n  // https://github.com/meteor/meteor/pull/13236\n  _sendQueued(obj) {\n    this._send(obj, true);\n  }\n\n  // We detected via DDP-level heartbeats that we've lost the\n  // connection.  Unlike `disconnect` or `close`, a lost connection\n  // will be automatically retried.\n  _lostConnection(error) {\n    this._stream._lostConnection(error);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.status\n   * @summary Get the current connection status. A reactive data source.\n   * @locus Client\n   */\n  status(...args) {\n    return this._stream.status(...args);\n  }\n\n  /**\n   * @summary Force an immediate reconnection attempt if the client is not connected to the server.\n\n  This method does nothing if the client is already connected.\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.reconnect\n   * @locus Client\n   */\n  reconnect(...args) {\n    return this._stream.reconnect(...args);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.disconnect\n   * @summary Disconnect the client from the server.\n   * @locus Client\n   */\n  disconnect(...args) {\n    return this._stream.disconnect(...args);\n  }\n\n  close() {\n    return this._stream.disconnect({ _permanent: true });\n  }\n\n  ///\n  /// Reactive user system\n  ///\n  userId() {\n    if (this._userIdDeps) this._userIdDeps.depend();\n    return this._userId;\n  }\n\n  setUserId(userId) {\n    // Avoid invalidating dependents if setUserId is called with current value.\n    if (this._userId === userId) return;\n    this._userId = userId;\n    if (this._userIdDeps) this._userIdDeps.changed();\n  }\n\n  // Returns true if we are in a state after reconnect of waiting for subs to be\n  // revived or early methods to finish their data, or we are waiting for a\n  // \"wait\" method to finish.\n  _waitingForQuiescence() {\n    return (\n      ! isEmpty(this._subsBeingRevived) ||\n      ! isEmpty(this._methodsBlockingQuiescence)\n    );\n  }\n\n  // Returns true if any method whose message has been sent to the server has\n  // not yet invoked its user callback.\n  _anyMethodsAreOutstanding() {\n    const invokers = this._methodInvokers;\n    return Object.values(invokers).some((invoker) => !!invoker.sentMessage);\n  }\n\n  async _processOneDataMessage(msg, updates) {\n    const messageType = msg.msg;\n\n    // msg is one of ['added', 'changed', 'removed', 'ready', 'updated']\n    if (messageType === 'added') {\n      await this._process_added(msg, updates);\n    } else if (messageType === 'changed') {\n      this._process_changed(msg, updates);\n    } else if (messageType === 'removed') {\n      this._process_removed(msg, updates);\n    } else if (messageType === 'ready') {\n      this._process_ready(msg, updates);\n    } else if (messageType === 'updated') {\n      this._process_updated(msg, updates);\n    } else if (messageType === 'nosub') {\n      // ignore this\n    } else {\n      Meteor._debug('discarding unknown livedata data message type', msg);\n    }\n  }\n\n  _prepareBuffersToFlush() {\n    const self = this;\n    if (self._bufferedWritesFlushHandle) {\n      clearTimeout(self._bufferedWritesFlushHandle);\n      self._bufferedWritesFlushHandle = null;\n    }\n\n    self._bufferedWritesFlushAt = null;\n    // We need to clear the buffer before passing it to\n    //  performWrites. As there's no guarantee that it\n    //  will exit cleanly.\n    const writes = self._bufferedWrites;\n    self._bufferedWrites = Object.create(null);\n    return writes;\n  }\n\n  /**\n   * Server-side store updates handled asynchronously\n   * @private\n   */\n  async _performWritesServer(updates) {\n    const self = this;\n\n    if (self._resetStores || !isEmpty(updates)) {\n      // Start all store updates - keeping original loop structure\n      for (const store of Object.values(self._stores)) {\n        await store.beginUpdate(\n          updates[store._name]?.length || 0,\n          self._resetStores\n        );\n      }\n\n      self._resetStores = false;\n\n      // Process each store's updates sequentially as before\n      for (const [storeName, messages] of Object.entries(updates)) {\n        const store = self._stores[storeName];\n        if (store) {\n          // Batch each store's messages in modest chunks to prevent event loop blocking\n          // while maintaining operation order\n          const CHUNK_SIZE = 100;\n          for (let i = 0; i < messages.length; i += CHUNK_SIZE) {\n            const chunk = messages.slice(i, Math.min(i + CHUNK_SIZE, messages.length));\n\n            for (const msg of chunk) {\n              await store.update(msg);\n            }\n\n            await new Promise(resolve => process.nextTick(resolve));\n          }\n        } else {\n          // Queue updates for uninitialized stores\n          self._updatesForUnknownStores[storeName] =\n            self._updatesForUnknownStores[storeName] || [];\n          self._updatesForUnknownStores[storeName].push(...messages);\n        }\n      }\n\n      // Complete all updates\n      for (const store of Object.values(self._stores)) {\n        await store.endUpdate();\n      }\n    }\n\n    self._runAfterUpdateCallbacks();\n  }\n\n  /**\n   * Client-side store updates handled synchronously for optimistic UI\n   * @private\n   */\n  _performWritesClient(updates) {\n    const self = this;\n\n    if (self._resetStores || !isEmpty(updates)) {\n      // Synchronous store updates for client\n      Object.values(self._stores).forEach(store => {\n        store.beginUpdate(\n          updates[store._name]?.length || 0,\n          self._resetStores\n        );\n      });\n\n      self._resetStores = false;\n\n      Object.entries(updates).forEach(([storeName, messages]) => {\n        const store = self._stores[storeName];\n        if (store) {\n          messages.forEach(msg => store.update(msg));\n        } else {\n          self._updatesForUnknownStores[storeName] =\n            self._updatesForUnknownStores[storeName] || [];\n          self._updatesForUnknownStores[storeName].push(...messages);\n        }\n      });\n\n      Object.values(self._stores).forEach(store => store.endUpdate());\n    }\n\n    self._runAfterUpdateCallbacks();\n  }\n\n  /**\n   * Executes buffered writes either synchronously (client) or async (server)\n   * @private\n   */\n  async _flushBufferedWrites() {\n    const self = this;\n    const writes = self._prepareBuffersToFlush();\n\n    return Meteor.isClient\n      ? self._performWritesClient(writes)\n      : self._performWritesServer(writes);\n  }\n\n  // Call any callbacks deferred with _runWhenAllServerDocsAreFlushed whose\n  // relevant docs have been flushed, as well as dataVisible callbacks at\n  // reconnect-quiescence time.\n  _runAfterUpdateCallbacks() {\n    const self = this;\n    const callbacks = self._afterUpdateCallbacks;\n    self._afterUpdateCallbacks = [];\n    callbacks.forEach((c) => {\n      c();\n    });\n  }\n\n  // Ensures that \"f\" will be called after all documents currently in\n  // _serverDocuments have been written to the local cache. f will not be called\n  // if the connection is lost before then!\n  _runWhenAllServerDocsAreFlushed(f) {\n    const self = this;\n    const runFAfterUpdates = () => {\n      self._afterUpdateCallbacks.push(f);\n    };\n    let unflushedServerDocCount = 0;\n    const onServerDocFlush = () => {\n      --unflushedServerDocCount;\n      if (unflushedServerDocCount === 0) {\n        // This was the last doc to flush! Arrange to run f after the updates\n        // have been applied.\n        runFAfterUpdates();\n      }\n    };\n\n    Object.values(self._serverDocuments).forEach((serverDocuments) => {\n      serverDocuments.forEach((serverDoc) => {\n        const writtenByStubForAMethodWithSentMessage =\n          keys(serverDoc.writtenByStubs).some(methodId => {\n            const invoker = self._methodInvokers[methodId];\n            return invoker && invoker.sentMessage;\n          });\n\n        if (writtenByStubForAMethodWithSentMessage) {\n          ++unflushedServerDocCount;\n          serverDoc.flushCallbacks.push(onServerDocFlush);\n        }\n      });\n    });\n    if (unflushedServerDocCount === 0) {\n      // There aren't any buffered docs --- we can call f as soon as the current\n      // round of updates is applied!\n      runFAfterUpdates();\n    }\n  }\n\n  _addOutstandingMethod(methodInvoker, options) {\n    if (options?.wait) {\n      // It's a wait method! Wait methods go in their own block.\n      this._outstandingMethodBlocks.push({\n        wait: true,\n        methods: [methodInvoker]\n      });\n    } else {\n      // Not a wait method. Start a new block if the previous block was a wait\n      // block, and add it to the last block of methods.\n      if (isEmpty(this._outstandingMethodBlocks) ||\n          last(this._outstandingMethodBlocks).wait) {\n        this._outstandingMethodBlocks.push({\n          wait: false,\n          methods: [],\n        });\n      }\n\n      last(this._outstandingMethodBlocks).methods.push(methodInvoker);\n    }\n\n    // If we added it to the first block, send it out now.\n    if (this._outstandingMethodBlocks.length === 1) {\n      methodInvoker.sendMessage();\n    }\n  }\n\n  // Called by MethodInvoker after a method's callback is invoked.  If this was\n  // the last outstanding method in the current block, runs the next block. If\n  // there are no more methods, consider accepting a hot code push.\n  _outstandingMethodFinished() {\n    const self = this;\n    if (self._anyMethodsAreOutstanding()) return;\n\n    // No methods are outstanding. This should mean that the first block of\n    // methods is empty. (Or it might not exist, if this was a method that\n    // half-finished before disconnect/reconnect.)\n    if (! isEmpty(self._outstandingMethodBlocks)) {\n      const firstBlock = self._outstandingMethodBlocks.shift();\n      if (! isEmpty(firstBlock.methods))\n        throw new Error(\n          'No methods outstanding but nonempty block: ' +\n            JSON.stringify(firstBlock)\n        );\n\n      // Send the outstanding methods now in the first block.\n      if (! isEmpty(self._outstandingMethodBlocks))\n        self._sendOutstandingMethods();\n    }\n\n    // Maybe accept a hot code push.\n    self._maybeMigrate();\n  }\n\n  // Sends messages for all the methods in the first block in\n  // _outstandingMethodBlocks.\n  _sendOutstandingMethods() {\n    const self = this;\n\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      return;\n    }\n\n    self._outstandingMethodBlocks[0].methods.forEach(m => {\n      m.sendMessage();\n    });\n  }\n\n  _sendOutstandingMethodBlocksMessages(oldOutstandingMethodBlocks) {\n    const self = this;\n    if (isEmpty(oldOutstandingMethodBlocks)) return;\n\n    // We have at least one block worth of old outstanding methods to try\n    // again. First: did onReconnect actually send anything? If not, we just\n    // restore all outstanding methods and run the first block.\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      self._outstandingMethodBlocks = oldOutstandingMethodBlocks;\n      self._sendOutstandingMethods();\n      return;\n    }\n\n    // OK, there are blocks on both sides. Special case: merge the last block of\n    // the reconnect methods with the first block of the original methods, if\n    // neither of them are \"wait\" blocks.\n    if (\n      !last(self._outstandingMethodBlocks).wait &&\n      !oldOutstandingMethodBlocks[0].wait\n    ) {\n      oldOutstandingMethodBlocks[0].methods.forEach((m) => {\n        last(self._outstandingMethodBlocks).methods.push(m);\n\n        // If this \"last block\" is also the first block, send the message.\n        if (self._outstandingMethodBlocks.length === 1) {\n          m.sendMessage();\n        }\n      });\n\n      oldOutstandingMethodBlocks.shift();\n    }\n\n    // Now add the rest of the original blocks on.\n    self._outstandingMethodBlocks.push(...oldOutstandingMethodBlocks);\n  }\n\n  _callOnReconnectAndSendAppropriateOutstandingMethods() {\n    const self = this;\n    const oldOutstandingMethodBlocks = self._outstandingMethodBlocks;\n    self._outstandingMethodBlocks = [];\n\n    self.onReconnect && self.onReconnect();\n    DDP._reconnectHook.each((callback) => {\n      callback(self);\n      return true;\n    });\n\n    self._sendOutstandingMethodBlocksMessages(oldOutstandingMethodBlocks);\n  }\n\n  // We can accept a hot code push if there are no methods in flight.\n  _readyToMigrate() {\n    return isEmpty(this._methodInvokers);\n  }\n\n  // If we were blocking a migration, see if it's now possible to continue.\n  // Call whenever the set of outstanding/blocked methods shrinks.\n  _maybeMigrate() {\n    const self = this;\n    if (self._retryMigrate && self._readyToMigrate()) {\n      self._retryMigrate();\n      self._retryMigrate = null;\n    }\n  }\n}\n", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\nimport { DDP } from './namespace.js';\nimport { EJSON } from 'meteor/ejson';\nimport { isEmpty, hasOwn } from \"meteor/ddp-common/utils\";\n\nexport class MessageProcessors {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * @summary Process the connection message and set up the session\n   * @param {Object} msg The connection message\n   */\n  async _livedata_connected(msg) {\n    const self = this._connection;\n\n    if (self._version !== 'pre1' && self._heartbeatInterval !== 0) {\n      self._heartbeat = new DDPCommon.Heartbeat({\n        heartbeatInterval: self._heartbeatInterval,\n        heartbeatTimeout: self._heartbeatTimeout,\n        onTimeout() {\n          self._lostConnection(\n            new DDP.ConnectionError('DDP heartbeat timed out')\n          );\n        },\n        sendPing() {\n          self._send({ msg: 'ping' });\n        }\n      });\n      self._heartbeat.start();\n    }\n\n    // If this is a reconnect, we'll have to reset all stores.\n    if (self._lastSessionId) self._resetStores = true;\n\n    let reconnectedToPreviousSession;\n    if (typeof msg.session === 'string') {\n      reconnectedToPreviousSession = self._lastSessionId === msg.session;\n      self._lastSessionId = msg.session;\n    }\n\n    if (reconnectedToPreviousSession) {\n      // Successful reconnection -- pick up where we left off.\n      return;\n    }\n\n    // Server doesn't have our data anymore. Re-sync a new session.\n\n    // Forget about messages we were buffering for unknown collections. They'll\n    // be resent if still relevant.\n    self._updatesForUnknownStores = Object.create(null);\n\n    if (self._resetStores) {\n      // Forget about the effects of stubs. We'll be resetting all collections\n      // anyway.\n      self._documentsWrittenByStub = Object.create(null);\n      self._serverDocuments = Object.create(null);\n    }\n\n    // Clear _afterUpdateCallbacks.\n    self._afterUpdateCallbacks = [];\n\n    // Mark all named subscriptions which are ready as needing to be revived.\n    self._subsBeingRevived = Object.create(null);\n    Object.entries(self._subscriptions).forEach(([id, sub]) => {\n      if (sub.ready) {\n        self._subsBeingRevived[id] = true;\n      }\n    });\n\n    // Arrange for \"half-finished\" methods to have their callbacks run, and\n    // track methods that were sent on this connection so that we don't\n    // quiesce until they are all done.\n    //\n    // Start by clearing _methodsBlockingQuiescence: methods sent before\n    // reconnect don't matter, and any \"wait\" methods sent on the new connection\n    // that we drop here will be restored by the loop below.\n    self._methodsBlockingQuiescence = Object.create(null);\n    if (self._resetStores) {\n      const invokers = self._methodInvokers;\n      Object.keys(invokers).forEach(id => {\n        const invoker = invokers[id];\n        if (invoker.gotResult()) {\n          // This method already got its result, but it didn't call its callback\n          // because its data didn't become visible. We did not resend the\n          // method RPC. We'll call its callback when we get a full quiesce,\n          // since that's as close as we'll get to \"data must be visible\".\n          self._afterUpdateCallbacks.push(\n            (...args) => invoker.dataVisible(...args)\n          );\n        } else if (invoker.sentMessage) {\n          // This method has been sent on this connection (maybe as a resend\n          // from the last connection, maybe from onReconnect, maybe just very\n          // quickly before processing the connected message).\n          //\n          // We don't need to do anything special to ensure its callbacks get\n          // called, but we'll count it as a method which is preventing\n          // reconnect quiescence. (eg, it might be a login method that was run\n          // from onReconnect, and we don't want to see flicker by seeing a\n          // logged-out state.)\n          self._methodsBlockingQuiescence[invoker.methodId] = true;\n        }\n      });\n    }\n\n    self._messagesBufferedUntilQuiescence = [];\n\n    // If we're not waiting on any methods or subs, we can reset the stores and\n    // call the callbacks immediately.\n    if (!self._waitingForQuiescence()) {\n      if (self._resetStores) {\n        for (const store of Object.values(self._stores)) {\n          await store.beginUpdate(0, true);\n          await store.endUpdate();\n        }\n        self._resetStores = false;\n      }\n      self._runAfterUpdateCallbacks();\n    }\n  }\n\n  /**\n   * @summary Process various data messages from the server\n   * @param {Object} msg The data message\n   */\n  async _livedata_data(msg) {\n    const self = this._connection;\n\n    if (self._waitingForQuiescence()) {\n      self._messagesBufferedUntilQuiescence.push(msg);\n\n      if (msg.msg === 'nosub') {\n        delete self._subsBeingRevived[msg.id];\n      }\n\n      if (msg.subs) {\n        msg.subs.forEach(subId => {\n          delete self._subsBeingRevived[subId];\n        });\n      }\n\n      if (msg.methods) {\n        msg.methods.forEach(methodId => {\n          delete self._methodsBlockingQuiescence[methodId];\n        });\n      }\n\n      if (self._waitingForQuiescence()) {\n        return;\n      }\n\n      // No methods or subs are blocking quiescence!\n      // We'll now process and all of our buffered messages, reset all stores,\n      // and apply them all at once.\n      const bufferedMessages = self._messagesBufferedUntilQuiescence;\n      for (const bufferedMessage of Object.values(bufferedMessages)) {\n        await this._processOneDataMessage(\n          bufferedMessage,\n          self._bufferedWrites\n        );\n      }\n      self._messagesBufferedUntilQuiescence = [];\n    } else {\n      await this._processOneDataMessage(msg, self._bufferedWrites);\n    }\n\n    // Immediately flush writes when:\n    //  1. Buffering is disabled. Or;\n    //  2. any non-(added/changed/removed) message arrives.\n    const standardWrite =\n      msg.msg === \"added\" ||\n      msg.msg === \"changed\" ||\n      msg.msg === \"removed\";\n\n    if (self._bufferedWritesInterval === 0 || !standardWrite) {\n      await self._flushBufferedWrites();\n      return;\n    }\n\n    if (self._bufferedWritesFlushAt === null) {\n      self._bufferedWritesFlushAt =\n        new Date().valueOf() + self._bufferedWritesMaxAge;\n    } else if (self._bufferedWritesFlushAt < new Date().valueOf()) {\n      await self._flushBufferedWrites();\n      return;\n    }\n\n    if (self._bufferedWritesFlushHandle) {\n      clearTimeout(self._bufferedWritesFlushHandle);\n    }\n    self._bufferedWritesFlushHandle = setTimeout(() => {\n      self._liveDataWritesPromise = self._flushBufferedWrites();\n      if (Meteor._isPromise(self._liveDataWritesPromise)) {\n        self._liveDataWritesPromise.finally(\n          () => (self._liveDataWritesPromise = undefined)\n        );\n      }\n    }, self._bufferedWritesInterval);\n  }\n\n  /**\n   * @summary Process individual data messages by type\n   * @private\n   */\n  async _processOneDataMessage(msg, updates) {\n    const messageType = msg.msg;\n\n    switch (messageType) {\n      case 'added':\n        await this._connection._process_added(msg, updates);\n        break;\n      case 'changed':\n        this._connection._process_changed(msg, updates);\n        break;\n      case 'removed':\n        this._connection._process_removed(msg, updates);\n        break;\n      case 'ready':\n        this._connection._process_ready(msg, updates);\n        break;\n      case 'updated':\n        this._connection._process_updated(msg, updates);\n        break;\n      case 'nosub':\n        // ignore this\n        break;\n      default:\n        Meteor._debug('discarding unknown livedata data message type', msg);\n    }\n  }\n\n  /**\n   * @summary Handle method results arriving from the server\n   * @param {Object} msg The method result message\n   */\n  async _livedata_result(msg) {\n    const self = this._connection;\n\n    // Lets make sure there are no buffered writes before returning result.\n    if (!isEmpty(self._bufferedWrites)) {\n      await self._flushBufferedWrites();\n    }\n\n    // find the outstanding request\n    // should be O(1) in nearly all realistic use cases\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      Meteor._debug('Received method result but no methods outstanding');\n      return;\n    }\n    const currentMethodBlock = self._outstandingMethodBlocks[0].methods;\n    let i;\n    const m = currentMethodBlock.find((method, idx) => {\n      const found = method.methodId === msg.id;\n      if (found) i = idx;\n      return found;\n    });\n    if (!m) {\n      Meteor._debug(\"Can't match method response to original method call\", msg);\n      return;\n    }\n\n    // Remove from current method block. This may leave the block empty, but we\n    // don't move on to the next block until the callback has been delivered, in\n    // _outstandingMethodFinished.\n    currentMethodBlock.splice(i, 1);\n\n    if (hasOwn.call(msg, 'error')) {\n      m.receiveResult(\n        new Meteor.Error(msg.error.error, msg.error.reason, msg.error.details)\n      );\n    } else {\n      // msg.result may be undefined if the method didn't return a value\n      m.receiveResult(undefined, msg.result);\n    }\n  }\n\n  /**\n   * @summary Handle \"nosub\" messages arriving from the server\n   * @param {Object} msg The nosub message\n   */\n  async _livedata_nosub(msg) {\n    const self = this._connection;\n\n    // First pass it through _livedata_data, which only uses it to help get\n    // towards quiescence.\n    await this._livedata_data(msg);\n\n    // Do the rest of our processing immediately, with no\n    // buffering-until-quiescence.\n\n    // we weren't subbed anyway, or we initiated the unsub.\n    if (!hasOwn.call(self._subscriptions, msg.id)) {\n      return;\n    }\n\n    // XXX COMPAT WITH ******* #errorCallback\n    const errorCallback = self._subscriptions[msg.id].errorCallback;\n    const stopCallback = self._subscriptions[msg.id].stopCallback;\n\n    self._subscriptions[msg.id].remove();\n\n    const meteorErrorFromMsg = msgArg => {\n      return (\n        msgArg &&\n        msgArg.error &&\n        new Meteor.Error(\n          msgArg.error.error,\n          msgArg.error.reason,\n          msgArg.error.details\n        )\n      );\n    };\n\n    // XXX COMPAT WITH ******* #errorCallback\n    if (errorCallback && msg.error) {\n      errorCallback(meteorErrorFromMsg(msg));\n    }\n\n    if (stopCallback) {\n      stopCallback(meteorErrorFromMsg(msg));\n    }\n  }\n\n  /**\n   * @summary Handle errors from the server\n   * @param {Object} msg The error message\n   */\n  _livedata_error(msg) {\n    Meteor._debug('Received error from server: ', msg.reason);\n    if (msg.offendingMessage) Meteor._debug('For: ', msg.offendingMessage);\n  }\n\n  // Document change message processors will be defined in a separate class\n}", "// A MethodInvoker manages sending a method to the server and calling the user's\n// callbacks. On construction, it registers itself in the connection's\n// _methodInvokers map; it removes itself once the method is fully finished and\n// the callback is invoked. This occurs when it has both received a result,\n// and the data written by it is fully visible.\nexport class MethodInvoker {\n  constructor(options) {\n    // Public (within this file) fields.\n    this.methodId = options.methodId;\n    this.sentMessage = false;\n\n    this._callback = options.callback;\n    this._connection = options.connection;\n    this._message = options.message;\n    this._onResultReceived = options.onResultReceived || (() => {});\n    this._wait = options.wait;\n    this.noRetry = options.noRetry;\n    this._methodResult = null;\n    this._dataVisible = false;\n\n    // Register with the connection.\n    this._connection._methodInvokers[this.methodId] = this;\n  }\n  // Sends the method message to the server. May be called additional times if\n  // we lose the connection and reconnect before receiving a result.\n  sendMessage() {\n    // This function is called before sending a method (including resending on\n    // reconnect). We should only (re)send methods where we don't already have a\n    // result!\n    if (this.gotResult())\n      throw new Error('sendingMethod is called on method with result');\n\n    // If we're re-sending it, it doesn't matter if data was written the first\n    // time.\n    this._dataVisible = false;\n    this.sentMessage = true;\n\n    // If this is a wait method, make all data messages be buffered until it is\n    // done.\n    if (this._wait)\n      this._connection._methodsBlockingQuiescence[this.methodId] = true;\n\n    // Actually send the message.\n    this._connection._send(this._message);\n  }\n  // Invoke the callback, if we have both a result and know that all data has\n  // been written to the local cache.\n  _maybeInvokeCallback() {\n    if (this._methodResult && this._dataVisible) {\n      // Call the callback. (This won't throw: the callback was wrapped with\n      // bindEnvironment.)\n      this._callback(this._methodResult[0], this._methodResult[1]);\n\n      // Forget about this method.\n      delete this._connection._methodInvokers[this.methodId];\n\n      // Let the connection know that this method is finished, so it can try to\n      // move on to the next block of methods.\n      this._connection._outstandingMethodFinished();\n    }\n  }\n  // Call with the result of the method from the server. Only may be called\n  // once; once it is called, you should not call sendMessage again.\n  // If the user provided an onResultReceived callback, call it immediately.\n  // Then invoke the main callback if data is also visible.\n  receiveResult(err, result) {\n    if (this.gotResult())\n      throw new Error('Methods should only receive results once');\n    this._methodResult = [err, result];\n    this._onResultReceived(err, result);\n    this._maybeInvokeCallback();\n  }\n  // Call this when all data written by the method is visible. This means that\n  // the method has returns its \"data is done\" message *AND* all server\n  // documents that are buffered at that time have been written to the local\n  // cache. Invokes the main callback if the result has been received.\n  dataVisible() {\n    this._dataVisible = true;\n    this._maybeInvokeCallback();\n  }\n  // True if receiveResult has been called.\n  gotResult() {\n    return !!this._methodResult;\n  }\n}\n", "import { MongoID } from 'meteor/mongo-id';\n\nexport class MongoIDMap extends IdMap {\n  constructor() {\n    super(MongoID.idStringify, MongoID.idParse);\n  }\n}", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\n\nimport { Connection } from './livedata_connection.js';\n\n// This array allows the `_allSubscriptionsReady` method below, which\n// is used by the `spiderable` package, to keep track of whether all\n// data is ready.\nconst allConnections = [];\n\n/**\n * @namespace DDP\n * @summary Namespace for DDP-related methods/classes.\n */\nexport const DDP = {};\n\n// This is private but it's used in a few places. accounts-base uses\n// it to get the current user. Meteor.setTimeout and friends clear\n// it. We can probably find a better way to factor this.\nDDP._CurrentMethodInvocation = new Meteor.EnvironmentVariable();\nDDP._CurrentPublicationInvocation = new Meteor.EnvironmentVariable();\n\n// XXX: Keep DDP._CurrentInvocation for backwards-compatibility.\nDDP._CurrentInvocation = DDP._CurrentMethodInvocation;\n\nDDP._CurrentCallAsyncInvocation = new Meteor.EnvironmentVariable();\n\n// This is passed into a weird `makeErrorType` function that expects its thing\n// to be a constructor\nfunction connectionErrorConstructor(message) {\n  this.message = message;\n}\n\nDDP.ConnectionError = Meteor.makeErrorType(\n  'DDP.ConnectionError',\n  connectionErrorConstructor\n);\n\nDDP.ForcedReconnectError = Meteor.makeErrorType(\n  'DDP.ForcedReconnectError',\n  () => {}\n);\n\n// Returns the named sequence of pseudo-random values.\n// The scope will be DDP._CurrentMethodInvocation.get(), so the stream will produce\n// consistent values for method calls on the client and server.\nDDP.randomStream = name => {\n  const scope = DDP._CurrentMethodInvocation.get();\n  return DDPCommon.RandomStream.get(scope, name);\n};\n\n// @param url {String} URL to Meteor app,\n//     e.g.:\n//     \"subdomain.meteor.com\",\n//     \"http://subdomain.meteor.com\",\n//     \"/\",\n//     \"ddp+sockjs://ddp--****-foo.meteor.com/sockjs\"\n\n/**\n * @summary Connect to the server of a different Meteor application to subscribe to its document sets and invoke its remote methods.\n * @locus Anywhere\n * @param {String} url The URL of another Meteor application.\n * @param {Object} [options]\n * @param {Boolean} options.reloadWithOutstanding is it OK to reload if there are outstanding methods?\n * @param {Object} options.headers extra headers to send on the websockets connection, for server-to-server DDP only\n * @param {Object} options._sockjsOptions Specifies options to pass through to the sockjs client\n * @param {Function} options.onDDPNegotiationVersionFailure callback when version negotiation fails.\n */\nDDP.connect = (url, options) => {\n  const ret = new Connection(url, options);\n  allConnections.push(ret); // hack. see below.\n  return ret;\n};\n\nDDP._reconnectHook = new Hook({ bindEnvironment: false });\n\n/**\n * @summary Register a function to call as the first step of\n * reconnecting. This function can call methods which will be executed before\n * any other outstanding methods. For example, this can be used to re-establish\n * the appropriate authentication context on the connection.\n * @locus Anywhere\n * @param {Function} callback The function to call. It will be called with a\n * single argument, the [connection object](#ddp_connect) that is reconnecting.\n */\nDDP.onReconnect = callback => DDP._reconnectHook.register(callback);\n\n// Hack for `spiderable` package: a way to see if the page is done\n// loading all the data it needs.\n//\nDDP._allSubscriptionsReady = () => allConnections.every(\n  conn => Object.values(conn._subscriptions).every(sub => sub.ready)\n);\n"]}