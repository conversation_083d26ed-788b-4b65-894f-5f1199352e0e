{"version": 3, "sources": ["meteor://💻app/packages/retry/retry.js"], "names": ["module", "export", "Retry", "constructor", "baseTimeout", "exponent", "maxTimeout", "minTimeout", "minCount", "fuzz", "arguments", "length", "undefined", "retryTimer", "clear", "clearTimeout", "_timeout", "count", "timeout", "Math", "min", "pow", "Random", "fraction", "retryLater", "fn", "Meteor", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,KAAK,EAACA,CAAA,KAAIA;AAAK,CAAC,CAAC;AAUzB,MAAMA,KAAK,CAAC;EACjBC,WAAWA,CAAA,EASH;IAAA,IATI;MACVC,WAAW,GAAG,IAAI;MAClBC,QAAQ,GAAG,GAAG;MACd;MACA;MACAC,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;MAC1BC,UAAU,GAAG,EAAE;MACfC,QAAQ,GAAG,CAAC;MACZC,IAAI,GAAG;IACT,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACJ,IAAI,CAACN,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,UAAU,GAAG,IAAI;EACxB;;EAEA;EACAC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACD,UAAU,EAAE;MACnBE,YAAY,CAAC,IAAI,CAACF,UAAU,CAAC;IAC/B;IACA,IAAI,CAACA,UAAU,GAAG,IAAI;EACxB;;EAEA;EACA;EACAG,QAAQA,CAACC,KAAK,EAAE;IACd,IAAIA,KAAK,GAAG,IAAI,CAACT,QAAQ,EAAE;MACzB,OAAO,IAAI,CAACD,UAAU;IACxB;;IAEA;IACA;IACA,IAAIW,OAAO,GAAGC,IAAI,CAACC,GAAG,CACpB,IAAI,CAACd,UAAU,EACf,IAAI,CAACF,WAAW,GAAGe,IAAI,CAACE,GAAG,CAAC,IAAI,CAAChB,QAAQ,EAAEY,KAAK,CAClD,CAAC,IACCK,MAAM,CAACC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACd,IAAI,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC,CAAC,CACpD;IAED,OAAOS,OAAO;EAChB;;EAEA;EACAM,UAAUA,CAACP,KAAK,EAAEQ,EAAE,EAAE;IACpB,IAAIP,OAAO,GAAG,IAAI,CAACF,QAAQ,CAACC,KAAK,CAAC;IAClC,IAAI,IAAI,CAACJ,UAAU,EACjBE,YAAY,CAAC,IAAI,CAACF,UAAU,CAAC;IAC/B,IAAI,CAACA,UAAU,GAAGa,MAAM,CAACC,UAAU,CAACF,EAAE,EAAEP,OAAO,CAAC;IAChD,OAAOA,OAAO;EAChB;AACF,C", "file": "/packages/retry.js", "sourcesContent": ["// Retry logic with an exponential backoff.\n//\n// options:\n//  baseTimeout: time for initial reconnect attempt (ms).\n//  exponent: exponential factor to increase timeout each attempt.\n//  maxTimeout: maximum time between retries (ms).\n//  minCount: how many times to reconnect \"instantly\".\n//  minTimeout: time to wait for the first `minCount` retries (ms).\n//  fuzz: factor to randomize retry times by (to avoid retry storms).\n\nexport class Retry {\n  constructor({\n    baseTimeout = 1000,\n    exponent = 2.2,\n    // The default is high-ish to ensure a server can recover from a\n    // failure caused by load.\n    maxTimeout = 5 * 60 * 1000,\n    minTimeout = 10,\n    minCount = 2,\n    fuzz = 0.5,\n  } = {}) {\n    this.baseTimeout = baseTimeout;\n    this.exponent = exponent;\n    this.maxTimeout = maxTimeout;\n    this.minTimeout = minTimeout;\n    this.minCount = minCount;\n    this.fuzz = fuzz;\n    this.retryTimer = null;\n  }\n\n  // Reset a pending retry, if any.\n  clear() {\n    if (this.retryTimer) {\n      clearTimeout(this.retryTimer);\n    }\n    this.retryTimer = null;\n  }\n\n  // Calculate how long to wait in milliseconds to retry, based on the\n  // `count` of which retry this is.\n  _timeout(count) {\n    if (count < this.minCount) {\n      return this.minTimeout;\n    }\n\n    // fuzz the timeout randomly, to avoid reconnect storms when a\n    // server goes down.\n    var timeout = Math.min(\n      this.maxTimeout,\n      this.baseTimeout * Math.pow(this.exponent, count)\n    ) * (\n      Random.fraction() * this.fuzz + (1 - this.fuzz / 2)\n    );\n\n    return timeout;\n  }\n\n  // Call `fn` after a delay, based on the `count` of which retry this is.\n  retryLater(count, fn) {\n    var timeout = this._timeout(count);\n    if (this.retryTimer)\n      clearTimeout(this.retryTimer);\n    this.retryTimer = Meteor.setTimeout(fn, timeout);\n    return timeout;\n  }\n}\n"]}