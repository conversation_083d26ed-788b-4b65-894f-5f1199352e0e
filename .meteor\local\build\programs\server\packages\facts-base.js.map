{"version": 3, "sources": ["meteor://💻app/packages/facts-base/facts_base_server.js", "meteor://💻app/packages/facts-base/facts_base_common.js"], "names": ["module", "export", "Facts", "FACTS_COLLECTION", "FACTS_PUBLICATION", "link", "v", "__reifyWaitForDeps__", "hasOwn", "Object", "prototype", "hasOwnProperty", "userIdFilter", "userId", "Package", "autopublish", "setUserIdFilter", "filter", "factsByPackage", "activeSubscriptions", "_factsByPackage", "incrementServerFact", "pkg", "fact", "increment", "call", "for<PERSON>ach", "sub", "added", "packageFacts", "changedField", "changed", "resetServerFacts", "Meteor", "defer", "publish", "ready", "push", "keys", "onStop", "activeSub", "is_auto", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,KAAK,EAACA,CAAA,KAAIA,KAAK;MAACC,gBAAgB,EAACA,CAAA,KAAIA,gBAAgB;MAACC,iBAAiB,EAACA,CAAA,KAAIA;IAAiB,CAAC,CAAC;IAAC,IAAIF,KAAK,EAACC,gBAAgB,EAACC,iBAAiB;IAACJ,MAAM,CAACK,IAAI,CAAC,qBAAqB,EAAC;MAACH,KAAKA,CAACI,CAAC,EAAC;QAACJ,KAAK,GAACI,CAAC;MAAA,CAAC;MAACH,gBAAgBA,CAACG,CAAC,EAAC;QAACH,gBAAgB,GAACG,CAAC;MAAA,CAAC;MAACF,iBAAiBA,CAACE,CAAC,EAAC;QAACF,iBAAiB,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEnW,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;;IAE9C;;IAEA;IACA;IACA,IAAIC,YAAY,GAAG,SAAAA,CAAUC,MAAM,EAAE;MACnC,OAAO,CAAC,CAACC,OAAO,CAACC,WAAW;IAC9B,CAAC;;IAED;IACAb,KAAK,CAACc,eAAe,GAAG,UAAUC,MAAM,EAAE;MACxCL,YAAY,GAAGK,MAAM;IACvB,CAAC;;IAED;IACA;IACA,MAAMC,cAAc,GAAG,CAAC,CAAC;IACzB,IAAIC,mBAAmB,GAAG,EAAE;;IAE5B;IACAjB,KAAK,CAACkB,eAAe,GAAGF,cAAc;IAEtChB,KAAK,CAACmB,mBAAmB,GAAG,UAAUC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAE;MAC1D,IAAI,CAAChB,MAAM,CAACiB,IAAI,CAACP,cAAc,EAAEI,GAAG,CAAC,EAAE;QACrCJ,cAAc,CAACI,GAAG,CAAC,GAAG,CAAC,CAAC;QACxBJ,cAAc,CAACI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAGC,SAAS;QACrCL,mBAAmB,CAACO,OAAO,CAAC,UAAUC,GAAG,EAAE;UACzCA,GAAG,CAACC,KAAK,CAACzB,gBAAgB,EAAEmB,GAAG,EAAEJ,cAAc,CAACI,GAAG,CAAC,CAAC;QACvD,CAAC,CAAC;QACF;MACF;MAEA,MAAMO,YAAY,GAAGX,cAAc,CAACI,GAAG,CAAC;MACxC,IAAI,CAACd,MAAM,CAACiB,IAAI,CAACI,YAAY,EAAEN,IAAI,CAAC,EAAE;QACpCL,cAAc,CAACI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAC/B;MACAL,cAAc,CAACI,GAAG,CAAC,CAACC,IAAI,CAAC,IAAIC,SAAS;MACtC,MAAMM,YAAY,GAAG,CAAC,CAAC;MACvBA,YAAY,CAACP,IAAI,CAAC,GAAGL,cAAc,CAACI,GAAG,CAAC,CAACC,IAAI,CAAC;MAC9CJ,mBAAmB,CAACO,OAAO,CAAC,UAAUC,GAAG,EAAE;QACzCA,GAAG,CAACI,OAAO,CAAC5B,gBAAgB,EAAEmB,GAAG,EAAEQ,YAAY,CAAC;MAClD,CAAC,CAAC;IACJ,CAAC;IAED5B,KAAK,CAAC8B,gBAAgB,GAAG,YAAY;MACnC,KAAK,IAAIV,GAAG,IAAIJ,cAAc,EAAE;QAC9B,OAAOA,cAAc,CAACI,GAAG,CAAC;MAC5B;IACF,CAAC;;IAED;IACA;IACA;IACAW,MAAM,CAACC,KAAK,CAAC,YAAY;MACvB;MACAD,MAAM,CAACE,OAAO,CAAC/B,iBAAiB,EAAE,YAAY;QAC5C,MAAMuB,GAAG,GAAG,IAAI;QAChB,IAAI,CAACf,YAAY,CAAC,IAAI,CAACC,MAAM,CAAC,EAAE;UAC9Bc,GAAG,CAACS,KAAK,CAAC,CAAC;UACX;QACF;QAEAjB,mBAAmB,CAACkB,IAAI,CAACV,GAAG,CAAC;QAC7BlB,MAAM,CAAC6B,IAAI,CAACpB,cAAc,CAAC,CAACQ,OAAO,CAAC,UAAUJ,GAAG,EAAE;UACjDK,GAAG,CAACC,KAAK,CAACzB,gBAAgB,EAAEmB,GAAG,EAAEJ,cAAc,CAACI,GAAG,CAAC,CAAC;QACvD,CAAC,CAAC;QACFK,GAAG,CAACY,MAAM,CAAC,YAAY;UACrBpB,mBAAmB,GACjBA,mBAAmB,CAACF,MAAM,CAACuB,SAAS,IAAIA,SAAS,KAAKb,GAAG,CAAC;QAC9D,CAAC,CAAC;QACFA,GAAG,CAACS,KAAK,CAAC,CAAC;MACb,CAAC,EAAE;QAACK,OAAO,EAAE;MAAI,CAAC,CAAC;IACrB,CAAC,CAAC;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AC3EH7C,MAAM,CAACC,MAAM,CAAC;EAACC,KAAK,EAACA,CAAA,KAAIA,KAAK;EAACC,gBAAgB,EAACA,CAAA,KAAIA,gBAAgB;EAACC,iBAAiB,EAACA,CAAA,KAAIA;AAAiB,CAAC,CAAC;AAA9G,MAAMF,KAAK,GAAG,CAAC,CAAC;AAChB,MAAMC,gBAAgB,GAAG,qBAAqB;AAC9C,MAAMC,iBAAiB,GAAG,cAAc,C", "file": "/packages/facts-base.js", "sourcesContent": ["import { Facts, FACTS_COLLECTION, FACTS_PUBLICATION } from './facts_base_common';\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\n// This file is only used server-side, so no need to check Meteor.isServer.\n\n// By default, we publish facts to no user if autopublish is off, and to all\n// users if autopublish is on.\nlet userIdFilter = function (userId) {\n  return !!Package.autopublish;\n};\n\n// XXX make this take effect at runtime too?\nFacts.setUserIdFilter = function (filter) {\n  userIdFilter = filter;\n};\n\n// XXX Use a minimongo collection instead and hook up an observeChanges\n// directly to a publish.\nconst factsByPackage = {};\nlet activeSubscriptions = [];\n\n// Make factsByPackage data available to the server environment\nFacts._factsByPackage = factsByPackage;\n\nFacts.incrementServerFact = function (pkg, fact, increment) {\n  if (!hasOwn.call(factsByPackage, pkg)) {\n    factsByPackage[pkg] = {};\n    factsByPackage[pkg][fact] = increment;\n    activeSubscriptions.forEach(function (sub) {\n      sub.added(FACTS_COLLECTION, pkg, factsByPackage[pkg]);\n    });\n    return;\n  }\n\n  const packageFacts = factsByPackage[pkg];\n  if (!hasOwn.call(packageFacts, fact)) {\n    factsByPackage[pkg][fact] = 0;\n  }\n  factsByPackage[pkg][fact] += increment;\n  const changedField = {};\n  changedField[fact] = factsByPackage[pkg][fact];\n  activeSubscriptions.forEach(function (sub) {\n    sub.changed(FACTS_COLLECTION, pkg, changedField);\n  });\n};\n\nFacts.resetServerFacts = function () {\n  for (let pkg in factsByPackage) {\n    delete factsByPackage[pkg];\n  }\n};\n\n// Deferred, because we have an unordered dependency on livedata.\n// XXX is this safe? could somebody try to connect before Meteor.publish is\n// called?\nMeteor.defer(function () {\n  // XXX Also publish facts-by-package.\n  Meteor.publish(FACTS_PUBLICATION, function () {\n    const sub = this;\n    if (!userIdFilter(this.userId)) {\n      sub.ready();\n      return;\n    }\n\n    activeSubscriptions.push(sub);\n    Object.keys(factsByPackage).forEach(function (pkg) {\n      sub.added(FACTS_COLLECTION, pkg, factsByPackage[pkg]);\n    });\n    sub.onStop(function () {\n      activeSubscriptions =\n        activeSubscriptions.filter(activeSub => activeSub !== sub);\n    });\n    sub.ready();\n  }, {is_auto: true});\n});\n\nexport {\n  Facts,\n  FACTS_COLLECTION,\n  FACTS_PUBLICATION,\n};\n", "const Facts = {};\nconst FACTS_COLLECTION = 'meteor_Facts_server';\nconst FACTS_PUBLICATION = 'meteor_facts';\n\nexport {\n  Facts,\n  FACTS_COLLECTION,\n  FACTS_PUBLICATION,\n};\n"]}