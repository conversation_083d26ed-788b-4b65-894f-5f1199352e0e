{"version": 3, "sources": ["meteor://💻app/packages/autoupdate/autoupdate_client.js", "meteor://💻app/packages/autoupdate/client_versions.js"], "names": ["module1", "export", "Autoupdate", "ClientVersions", "link", "v", "clientArch", "Meteor", "<PERSON><PERSON><PERSON><PERSON>", "isModern", "autoupdateVersions", "__meteor_runtime_config__", "autoupdate", "versions", "version", "versionRefreshable", "versionNonRefreshable", "assets", "clientVersions", "_clientVersions", "connection", "registerStoreClient", "createStore", "newClientAvailable", "knownToSupportCssOnLoad", "retry", "Retry", "minCount", "baseTimeout", "failures", "_retrySubscription", "subscribe", "onError", "error", "_debug", "retryLater", "onReady", "resolved", "Promise", "resolve", "check", "doc", "then", "checkNewVersionDocument", "stop", "watch", "_id", "Package", "reload", "Reload", "_reload", "newCss", "oldLinks", "Array", "prototype", "for<PERSON>ach", "call", "document", "getElementsByTagName", "className", "push", "waitUntilCssLoads", "callback", "called", "onload", "id", "setInterval", "sheet", "clearInterval", "newLinksLeftToLoad", "length", "removeOldLinks", "splice", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "css", "newLink", "createElement", "setAttribute", "url", "setTimeout", "head", "item", "append<PERSON><PERSON><PERSON>", "module", "_objectSpread", "default", "Tracker", "constructor", "_versions", "Map", "_watchCallbacks", "Set", "update", "_ref", "msg", "fields", "set", "hasVersions", "size", "get", "isNew", "Object", "assign", "_ref2", "fn", "filter", "skipInitial", "arguments", "undefined", "add", "delete", "currentVersion", "isNewVersion", "some", "field", "dependency", "Dependency", "depend", "changed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAAA,OAAO,CAACC,MAAM,CAAC;IAACC,UAAU,EAACA,CAAA,KAAIA;EAAU,CAAC,CAAC;EAAC,IAAIC,cAAc;EAACH,OAAO,CAACI,IAAI,CAAC,sBAAsB,EAAC;IAACD,cAAcA,CAACE,CAAC,EAAC;MAACF,cAAc,GAACE,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EA6B3I,MAAMC,UAAU,GAAGC,MAAM,CAACC,SAAS,GAAG,aAAa,GACjDD,MAAM,CAACE,QAAQ,GAAG,aAAa,GAAG,oBAAoB;EAExD,MAAMC,kBAAkB,GACtB,CAAC,CAACC,yBAAyB,CAACC,UAAU,IAAI,CAAC,CAAC,EAAEC,QAAQ,IAAI,CAAC,CAAC,EAAEP,UAAU,CAAC,IAAI;IAC3EQ,OAAO,EAAE,SAAS;IAClBC,kBAAkB,EAAE,SAAS;IAC7BC,qBAAqB,EAAE,SAAS;IAChCC,MAAM,EAAE;EACV,CAAC;EAEI,MAAMf,UAAU,GAAG,CAAC,CAAC;EAE5B;EACA,MAAMgB,cAAc,GAClBhB,UAAU,CAACiB,eAAe;EAAG;EAC7B,IAAIhB,cAAc,CAAC,CAAC;EAEtBI,MAAM,CAACa,UAAU,CAACC,mBAAmB,CACnC,kCAAkC,EAClCH,cAAc,CAACI,WAAW,CAAC,CAC7B,CAAC;EAEDpB,UAAU,CAACqB,kBAAkB,GAAG,YAAY;IAC1C,OAAOL,cAAc,CAACK,kBAAkB,CACtCjB,UAAU,EACV,CAAC,oBAAoB,EAAE,uBAAuB,CAAC,EAC/CI,kBACF,CAAC;EACH,CAAC;;EAED;EACA,IAAIc,uBAAuB,GAAG,KAAK;EAEnC,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,QAAQ,EAAE,CAAC;IAAE;IACbC,WAAW,EAAE,EAAE,GAAC,IAAI,CAAC;EACvB,CAAC,CAAC;EAEF,IAAIC,QAAQ,GAAG,CAAC;EAEhB3B,UAAU,CAAC4B,kBAAkB,GAAG,MAAM;IACpCvB,MAAM,CAACwB,SAAS,CAAC,kCAAkC,EAAE;MACnDC,OAAOA,CAACC,KAAK,EAAE;QACb1B,MAAM,CAAC2B,MAAM,CAAC,gCAAgC,EAAED,KAAK,CAAC;QACtDJ,QAAQ,EAAE;QACVJ,KAAK,CAACU,UAAU,CAACN,QAAQ,EAAE,YAAY;UACrC;UACA;UACA;UACA;UACA;UACA;UACA;UACA3B,UAAU,CAAC4B,kBAAkB,CAAC,CAAC;QACjC,CAAC,CAAC;MACJ,CAAC;MAEDM,OAAOA,CAAA,EAAG;QACR;QACA;QACA;QACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;QAClC,SAASC,KAAKA,CAACC,GAAG,EAAE;UAClBJ,QAAQ,CAACK,IAAI,CAAC,MAAMC,uBAAuB,CAACF,GAAG,CAAC,CAAC;QACnD;QAEA,MAAMG,IAAI,GAAG1B,cAAc,CAAC2B,KAAK,CAACL,KAAK,CAAC;QAExC,SAASG,uBAAuBA,CAACF,GAAG,EAAE;UACpC,IAAIA,GAAG,CAACK,GAAG,KAAKxC,UAAU,EAAE;YAC1B;UACF;UAEA,IAAImC,GAAG,CAACzB,qBAAqB,KACzBN,kBAAkB,CAACM,qBAAqB,EAAE;YAC5C;YACA;YACA,IAAI4B,IAAI,EAAEA,IAAI,CAAC,CAAC;YAChB,IAAIG,OAAO,CAACC,MAAM,EAAE;cAClB;cACA;cACAD,OAAO,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC;YACjC;YACA;UACF;UAEA,IAAIT,GAAG,CAAC1B,kBAAkB,KAAKL,kBAAkB,CAACK,kBAAkB,EAAE;YACpEL,kBAAkB,CAACK,kBAAkB,GAAG0B,GAAG,CAAC1B,kBAAkB;;YAE9D;YACA;YACA,IAAIoC,MAAM,GAAGV,GAAG,CAACxB,MAAM,IAAI,EAAE;YAC7B,IAAImC,QAAQ,GAAG,EAAE;YAEjBC,KAAK,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAC1BC,QAAQ,CAACC,oBAAoB,CAAC,MAAM,CAAC,EACrC,UAAUtD,IAAI,EAAE;cACd,IAAIA,IAAI,CAACuD,SAAS,KAAK,gBAAgB,EAAE;gBACvCP,QAAQ,CAACQ,IAAI,CAACxD,IAAI,CAAC;cACrB;YACF,CACF,CAAC;YAED,SAASyD,iBAAiBA,CAACzD,IAAI,EAAE0D,QAAQ,EAAE;cACzC,IAAIC,MAAM;cAEV3D,IAAI,CAAC4D,MAAM,GAAG,YAAY;gBACxBxC,uBAAuB,GAAG,IAAI;gBAC9B,IAAI,CAAEuC,MAAM,EAAE;kBACZA,MAAM,GAAG,IAAI;kBACbD,QAAQ,CAAC,CAAC;gBACZ;cACF,CAAC;cAED,IAAI,CAAEtC,uBAAuB,EAAE;gBAC7B,IAAIyC,EAAE,GAAG1D,MAAM,CAAC2D,WAAW,CAAC,YAAY;kBACtC,IAAI9D,IAAI,CAAC+D,KAAK,EAAE;oBACd,IAAI,CAAEJ,MAAM,EAAE;sBACZA,MAAM,GAAG,IAAI;sBACbD,QAAQ,CAAC,CAAC;oBACZ;oBACAvD,MAAM,CAAC6D,aAAa,CAACH,EAAE,CAAC;kBAC1B;gBACF,CAAC,EAAE,EAAE,CAAC;cACR;YACF;YAEA,IAAII,kBAAkB,GAAGlB,MAAM,CAACmB,MAAM;YACtC,SAASC,cAAcA,CAAA,EAAG;cACxB,IAAInB,QAAQ,CAACkB,MAAM,GAAG,CAAC,IACnB,EAAED,kBAAkB,GAAG,CAAC,EAAE;gBAC5BjB,QAAQ,CAACoB,MAAM,CAAC,CAAC,CAAC,CAACjB,OAAO,CAACnD,IAAI,IAAI;kBACjCA,IAAI,CAACqE,UAAU,CAACC,WAAW,CAACtE,IAAI,CAAC;gBACnC,CAAC,CAAC;cACJ;YACF;YAEA,IAAI+C,MAAM,CAACmB,MAAM,GAAG,CAAC,EAAE;cACrBnB,MAAM,CAACI,OAAO,CAACoB,GAAG,IAAI;gBACpB,MAAMC,OAAO,GAAGnB,QAAQ,CAACoB,aAAa,CAAC,MAAM,CAAC;gBAC9CD,OAAO,CAACE,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC;gBACzCF,OAAO,CAACE,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;gBACxCF,OAAO,CAACE,YAAY,CAAC,OAAO,EAAE,gBAAgB,CAAC;gBAC/CF,OAAO,CAACE,YAAY,CAAC,MAAM,EAAEH,GAAG,CAACI,GAAG,CAAC;gBAErClB,iBAAiB,CAACe,OAAO,EAAE,YAAY;kBACrCrE,MAAM,CAACyE,UAAU,CAACT,cAAc,EAAE,GAAG,CAAC;gBACxC,CAAC,CAAC;gBAEF,MAAMU,IAAI,GAAGxB,QAAQ,CAACC,oBAAoB,CAAC,MAAM,CAAC,CAACwB,IAAI,CAAC,CAAC,CAAC;gBAC1DD,IAAI,CAACE,WAAW,CAACP,OAAO,CAAC;cAC3B,CAAC,CAAC;YACJ,CAAC,MAAM;cACLL,cAAc,CAAC,CAAC;YAClB;UACF;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAEDrE,UAAU,CAAC4B,kBAAkB,CAAC,CAAC;AAAC,EAAA0B,IAAA,OAAA4B,MAAA,E;;;;;;;;;;;ACvMhC,IAAIC,aAAa;AAACD,MAAM,CAAChF,IAAI,CAAC,sCAAsC,EAAC;EAACkF,OAAOA,CAACjF,CAAC,EAAC;IAACgF,aAAa,GAAChF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArG+E,MAAM,CAACnF,MAAM,CAAC;EAACE,cAAc,EAACA,CAAA,KAAIA;AAAc,CAAC,CAAC;AAAC,IAAIoF,OAAO;AAACH,MAAM,CAAChF,IAAI,CAAC,gBAAgB,EAAC;EAACmF,OAAOA,CAAClF,CAAC,EAAC;IAACkF,OAAO,GAAClF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAE/G,MAAMF,cAAc,CAAC;EAC1BqF,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClC;;EAEA;EACA;EACA;EACAtE,WAAWA,CAAA,EAAG;IACZ,OAAO;MACLuE,MAAM,EAAEC,IAAA,IAAyB;QAAA,IAAxB;UAAE7B,EAAE;UAAE8B,GAAG;UAAEC;QAAO,CAAC,GAAAF,IAAA;QAC1B,IAAIC,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,SAAS,EAAE;UACxC,IAAI,CAACE,GAAG,CAAChC,EAAE,EAAE+B,MAAM,CAAC;QACtB;MACF;IACF,CAAC;EACH;EAEAE,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACT,SAAS,CAACU,IAAI,GAAG,CAAC;EAChC;EAEAC,GAAGA,CAACnC,EAAE,EAAE;IACN,OAAO,IAAI,CAACwB,SAAS,CAACW,GAAG,CAACnC,EAAE,CAAC;EAC/B;;EAEA;EACA;EACA;EACAgC,GAAGA,CAAChC,EAAE,EAAE+B,MAAM,EAAE;IACd,IAAIlF,OAAO,GAAG,IAAI,CAAC2E,SAAS,CAACW,GAAG,CAACnC,EAAE,CAAC;IACpC,IAAIoC,KAAK,GAAG,KAAK;IAEjB,IAAIvF,OAAO,EAAE;MACXwF,MAAM,CAACC,MAAM,CAACzF,OAAO,EAAEkF,MAAM,CAAC;IAChC,CAAC,MAAM;MACLlF,OAAO,GAAAuE,aAAA;QACLvC,GAAG,EAAEmB;MAAE,GACJ+B,MAAM,CACV;MAEDK,KAAK,GAAG,IAAI;MACZ,IAAI,CAACZ,SAAS,CAACQ,GAAG,CAAChC,EAAE,EAAEnD,OAAO,CAAC;IACjC;IAEA,IAAI,CAAC6E,eAAe,CAACpC,OAAO,CAACiD,KAAA,IAAoB;MAAA,IAAnB;QAAEC,EAAE;QAAEC;MAAO,CAAC,GAAAF,KAAA;MAC1C,IAAI,CAAEE,MAAM,IAAIA,MAAM,KAAK5F,OAAO,CAACgC,GAAG,EAAE;QACtC2D,EAAE,CAAC3F,OAAO,EAAEuF,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;EACJ;;EAEA;EACA;EACA;EACA;EACA;EACAxD,KAAKA,CAAC4D,EAAE,EAAgC;IAAA,IAA9B;MAAEE,WAAW;MAAED;IAAO,CAAC,GAAAE,SAAA,CAAAtC,MAAA,QAAAsC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IACpC,IAAI,CAAED,WAAW,EAAE;MACjB,MAAMtE,QAAQ,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;MAElC,IAAI,CAACkD,SAAS,CAAClC,OAAO,CAAEzC,OAAO,IAAK;QAClC,IAAI,CAAE4F,MAAM,IAAIA,MAAM,KAAK5F,OAAO,CAACgC,GAAG,EAAE;UACtCT,QAAQ,CAACK,IAAI,CAAC,MAAM+D,EAAE,CAAC3F,OAAO,EAAE,IAAI,CAAC,CAAC;QACxC;MACF,CAAC,CAAC;IACJ;IAEA,MAAMgD,QAAQ,GAAG;MAAE2C,EAAE;MAAEC;IAAO,CAAC;IAC/B,IAAI,CAACf,eAAe,CAACmB,GAAG,CAAChD,QAAQ,CAAC;IAElC,OAAO,MAAM,IAAI,CAAC6B,eAAe,CAACoB,MAAM,CAACjD,QAAQ,CAAC;EACpD;;EAEA;EACAvC,kBAAkBA,CAAC0C,EAAE,EAAE+B,MAAM,EAAEgB,cAAc,EAAE;IAC7C,SAASC,YAAYA,CAACnG,OAAO,EAAE;MAC7B,OACEA,OAAO,CAACgC,GAAG,KAAKmB,EAAE,IAClB+B,MAAM,CAACkB,IAAI,CAAEC,KAAK,IAAKrG,OAAO,CAACqG,KAAK,CAAC,KAAKH,cAAc,CAACG,KAAK,CAAC,CAAC;IAEpE;IAEA,MAAMC,UAAU,GAAG,IAAI7B,OAAO,CAAC8B,UAAU,CAAC,CAAC;IAC3C,MAAMvG,OAAO,GAAG,IAAI,CAACsF,GAAG,CAACnC,EAAE,CAAC;IAE5BmD,UAAU,CAACE,MAAM,CAAC,CAAC;IAEnB,MAAM1E,IAAI,GAAG,IAAI,CAACC,KAAK,CACpB/B,OAAO,IAAK;MACX,IAAImG,YAAY,CAACnG,OAAO,CAAC,EAAE;QACzBsG,UAAU,CAACG,OAAO,CAAC,CAAC;QACpB3E,IAAI,CAAC,CAAC;MACR;IACF,CAAC,EACD;MAAE+D,WAAW,EAAE;IAAK,CACtB,CAAC;IAED,OAAO,CAAC,CAAE7F,OAAO,IAAImG,YAAY,CAACnG,OAAO,CAAC;EAC5C;AACF,C", "file": "/packages/autoupdate.js", "sourcesContent": ["// Subscribe to the `meteor_autoupdate_clientVersions` collection,\n// which contains the set of acceptable client versions.\n//\n// A \"hard code push\" occurs when the running client version is not in\n// the set of acceptable client versions (or the server updates the\n// collection, there is a published client version marked `current` and\n// the running client version is no longer in the set).\n//\n// When the `reload` package is loaded, a hard code push causes\n// the browser to reload, so that it will load the latest client\n// version from the server.\n//\n// A \"soft code push\" represents the situation when the running client\n// version is in the set of acceptable versions, but there is a newer\n// version available on the server.\n//\n// `Autoupdate.newClientAvailable` is a reactive data source which\n// becomes `true` if a new version of the client is available on\n// the server.\n//\n// This package doesn't implement a soft code reload process itself,\n// but `newClientAvailable` could be used for example to display a\n// \"click to reload\" link to the user.\n\n// The client version of the client code currently running in the\n// browser.\n\nimport { ClientVersions } from \"./client_versions.js\";\n\nconst clientArch = Meteor.isCordova ? \"web.cordova\" :\n  Meteor.isModern ? \"web.browser\" : \"web.browser.legacy\";\n\nconst autoupdateVersions =\n  ((__meteor_runtime_config__.autoupdate || {}).versions || {})[clientArch] || {\n    version: \"unknown\",\n    versionRefreshable: \"unknown\",\n    versionNonRefreshable: \"unknown\",\n    assets: [],\n  };\n\nexport const Autoupdate = {};\n\n// Stores acceptable client versions.\nconst clientVersions =\n  Autoupdate._clientVersions = // Used by a self-test and hot-module-replacement\n  new ClientVersions();\n\nMeteor.connection.registerStoreClient(\n  \"meteor_autoupdate_clientVersions\",\n  clientVersions.createStore()\n);\n\nAutoupdate.newClientAvailable = function () {\n  return clientVersions.newClientAvailable(\n    clientArch,\n    [\"versionRefreshable\", \"versionNonRefreshable\"],\n    autoupdateVersions\n  );\n};\n\n// Set to true if the link.onload callback ever fires for any <link> node.\nlet knownToSupportCssOnLoad = false;\n\nconst retry = new Retry({\n  // Unlike the stream reconnect use of Retry, which we want to be instant\n  // in normal operation, this is a wacky failure. We don't want to retry\n  // right away, we can start slowly.\n  //\n  // A better way than timeconstants here might be to use the knowledge\n  // of when we reconnect to help trigger these retries. Typically, the\n  // server fixing code will result in a restart and reconnect, but\n  // potentially the subscription could have a transient error.\n  minCount: 0, // don't do any immediate retries\n  baseTimeout: 30*1000 // start with 30s\n});\n\nlet failures = 0;\n\nAutoupdate._retrySubscription = () => {\n  Meteor.subscribe(\"meteor_autoupdate_clientVersions\", {\n    onError(error) {\n      Meteor._debug(\"autoupdate subscription failed\", error);\n      failures++;\n      retry.retryLater(failures, function () {\n        // Just retry making the subscription, don't reload the whole\n        // page. While reloading would catch more cases (for example,\n        // the server went back a version and is now doing old-style hot\n        // code push), it would also be more prone to reload loops,\n        // which look really bad to the user. Just retrying the\n        // subscription over DDP means it is at least possible to fix by\n        // updating the server.\n        Autoupdate._retrySubscription();\n      });\n    },\n\n    onReady() {\n      // Call checkNewVersionDocument with a slight delay, so that the\n      // const handle declaration is guaranteed to be initialized, even if\n      // the added or changed callbacks are called synchronously.\n      const resolved = Promise.resolve();\n      function check(doc) {\n        resolved.then(() => checkNewVersionDocument(doc));\n      }\n\n      const stop = clientVersions.watch(check);\n\n      function checkNewVersionDocument(doc) {\n        if (doc._id !== clientArch) {\n          return;\n        }\n\n        if (doc.versionNonRefreshable !==\n            autoupdateVersions.versionNonRefreshable) {\n          // Non-refreshable assets have changed, so we have to reload the\n          // whole page rather than just replacing <link> tags.\n          if (stop) stop();\n          if (Package.reload) {\n            // The reload package should be provided by ddp-client, which\n            // is provided by the ddp package that autoupdate depends on.\n            Package.reload.Reload._reload();\n          }\n          return;\n        }\n\n        if (doc.versionRefreshable !== autoupdateVersions.versionRefreshable) {\n          autoupdateVersions.versionRefreshable = doc.versionRefreshable;\n\n          // Switch out old css links for the new css links. Inspired by:\n          // https://github.com/guard/guard-livereload/blob/master/js/livereload.js#L710\n          var newCss = doc.assets || [];\n          var oldLinks = [];\n\n          Array.prototype.forEach.call(\n            document.getElementsByTagName('link'),\n            function (link) {\n              if (link.className === '__meteor-css__') {\n                oldLinks.push(link);\n              }\n            }\n          );\n\n          function waitUntilCssLoads(link, callback) {\n            var called;\n\n            link.onload = function () {\n              knownToSupportCssOnLoad = true;\n              if (! called) {\n                called = true;\n                callback();\n              }\n            };\n\n            if (! knownToSupportCssOnLoad) {\n              var id = Meteor.setInterval(function () {\n                if (link.sheet) {\n                  if (! called) {\n                    called = true;\n                    callback();\n                  }\n                  Meteor.clearInterval(id);\n                }\n              }, 50);\n            }\n          }\n\n          let newLinksLeftToLoad = newCss.length;\n          function removeOldLinks() {\n            if (oldLinks.length > 0 &&\n                --newLinksLeftToLoad < 1) {\n              oldLinks.splice(0).forEach(link => {\n                link.parentNode.removeChild(link);\n              });\n            }\n          }\n\n          if (newCss.length > 0) {\n            newCss.forEach(css => {\n              const newLink = document.createElement(\"link\");\n              newLink.setAttribute(\"rel\", \"stylesheet\");\n              newLink.setAttribute(\"type\", \"text/css\");\n              newLink.setAttribute(\"class\", \"__meteor-css__\");\n              newLink.setAttribute(\"href\", css.url);\n\n              waitUntilCssLoads(newLink, function () {\n                Meteor.setTimeout(removeOldLinks, 200);\n              });\n\n              const head = document.getElementsByTagName(\"head\").item(0);\n              head.appendChild(newLink);\n            });\n          } else {\n            removeOldLinks();\n          }\n        }\n      }\n    }\n  });\n};\n\nAutoupdate._retrySubscription();\n", "import { Tracker } from \"meteor/tracker\";\n\nexport class ClientVersions {\n  constructor() {\n    this._versions = new Map();\n    this._watchCallbacks = new Set();\n  }\n\n  // Creates a Livedata store for use with `Meteor.connection.registerStore`.\n  // After the store is registered, document updates reported by Livedata are\n  // merged with the documents in this `ClientVersions` instance.\n  createStore() {\n    return {\n      update: ({ id, msg, fields }) => {\n        if (msg === \"added\" || msg === \"changed\") {\n          this.set(id, fields);\n        }\n      }\n    };\n  }\n\n  hasVersions() {\n    return this._versions.size > 0;\n  }\n\n  get(id) {\n    return this._versions.get(id);\n  }\n\n  // Adds or updates a version document and invokes registered callbacks for the\n  // added/updated document. If a document with the given ID already exists, its\n  // fields are merged with `fields`.\n  set(id, fields) {\n    let version = this._versions.get(id);\n    let isNew = false;\n\n    if (version) {\n      Object.assign(version, fields);\n    } else {\n      version = {\n        _id: id,\n        ...fields\n      };\n\n      isNew = true;\n      this._versions.set(id, version);\n    }\n\n    this._watchCallbacks.forEach(({ fn, filter }) => {\n      if (! filter || filter === version._id) {\n        fn(version, isNew);\n      }\n    });\n  }\n\n  // Registers a callback that will be invoked when a version document is added\n  // or changed. Calling the function returned by `watch` removes the callback.\n  // If `skipInitial` is true, the callback isn't be invoked for existing\n  // documents. If `filter` is set, the callback is only invoked for documents\n  // with ID `filter`.\n  watch(fn, { skipInitial, filter } = {}) {\n    if (! skipInitial) {\n      const resolved = Promise.resolve();\n\n      this._versions.forEach((version) => {\n        if (! filter || filter === version._id) {\n          resolved.then(() => fn(version, true));\n        }\n      });\n    }\n\n    const callback = { fn, filter };\n    this._watchCallbacks.add(callback);\n\n    return () => this._watchCallbacks.delete(callback);\n  }\n\n  // A reactive data source for `Autoupdate.newClientAvailable`.\n  newClientAvailable(id, fields, currentVersion) {\n    function isNewVersion(version) {\n      return (\n        version._id === id &&\n        fields.some((field) => version[field] !== currentVersion[field])\n      );\n    }\n\n    const dependency = new Tracker.Dependency();\n    const version = this.get(id);\n\n    dependency.depend();\n\n    const stop = this.watch(\n      (version) => {\n        if (isNewVersion(version)) {\n          dependency.changed();\n          stop();\n        }\n      },\n      { skipInitial: true }\n    );\n\n    return !! version && isNewVersion(version);\n  }\n}\n"]}