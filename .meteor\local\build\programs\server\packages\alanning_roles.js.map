{"version": 3, "sources": ["meteor://💻app/packages/alanning:roles/roles/roles_common_async.js", "meteor://💻app/packages/alanning:roles/roles/roles_server.js"], "names": ["module1", "export", "RolesCollection", "RoleAssignmentCollection", "Meteor", "link", "v", "Mongo", "__reifyWaitForDeps__", "Collection", "roles", "roleAssignment", "Roles", "getGroupsForUserDeprecationWarning", "asyncSome", "arr", "predicate", "e", "Object", "assign", "GLOBAL_GROUP", "createRoleAsync", "<PERSON><PERSON><PERSON>", "options", "_checkRoleName", "unlessExists", "insertedId", "existingRole", "findOneAsync", "_id", "updateAsync", "$setOnInsert", "children", "insertAsync", "Error", "deleteRoleAsync", "inheritedRoles", "removeAsync", "_getParentRoleNamesAsync", "r", "find", "$in", "fetchAsync", "$pull", "_getInheritedRoleNamesAsync", "$set", "map", "r2", "multi", "length", "renameRoleAsync", "old<PERSON>ame", "newName", "count", "role", "addRolesToParentAsync", "rolesNames", "parentName", "Array", "isArray", "_addRoleToParentAsync", "concat", "includes", "$ne", "$push", "$each", "removeRolesFromParentAsync", "_removeRoleFromParentAsync", "fields", "addUsersToRolesAsync", "users", "id", "_normalizeOptions", "_checkScopeName", "scope", "ifExists", "user", "_addUserToRoleAsync", "setUserRolesAsync", "anyScope", "selector", "userId", "existingAssignment", "res", "parentRoles", "Set", "parentRole", "add", "delete", "nestedRoles", "removeUsersFromRolesAsync", "_removeUserFromRoleAsync", "userIsInRoleAsync", "filter", "out", "countDocuments", "limit", "getRolesForUserAsync", "fullObjects", "onlyAssigned", "onlyScoped", "push", "reduce", "rev", "current", "getAllRoles", "queryOptions", "sort", "getUsersInRoleAsync", "ids", "getUserAssignmentsForRole", "a", "_getUsersInRoleCursor", "getGroupsForUserAsync", "console", "warn", "getScopesForUser", "arguments", "getScopesForUserAsync", "scopes", "obi", "renameScopeAsync", "removeScopeAsync", "name", "trim", "isParentOfAsync", "parentRoleName", "child<PERSON>ole<PERSON>ame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "undefined", "_normalizeScopeName", "scopeName", "__reify_async_result__", "_reifyError", "self", "async", "module", "indexFnAssignment", "createIndexAsync", "bind", "indexFnRoles", "indexes", "index"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,OAAO,CAACC,MAAM,CAAC;MAACC,eAAe,EAACA,CAAA,KAAIA,eAAe;MAACC,wBAAwB,EAACA,CAAA,KAAIA;IAAwB,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACJ,OAAO,CAACK,IAAI,CAAC,eAAe,EAAC;MAACD,MAAMA,CAACE,CAAC,EAAC;QAACF,MAAM,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,KAAK;IAACP,OAAO,CAACK,IAAI,CAAC,cAAc,EAAC;MAACE,KAAKA,CAACD,CAAC,EAAC;QAACC,KAAK,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IA4B/R,MAAMN,eAAe,GAAG,IAAIK,KAAK,CAACE,UAAU,CAAC,OAAO,CAAC;IAE5D,IAAI,CAACL,MAAM,CAACM,KAAK,EAAE;MACjBN,MAAM,CAACM,KAAK,GAAGR,eAAe;IAChC;IAEO,MAAMC,wBAAwB,GAAG,IAAII,KAAK,CAACE,UAAU,CAAC,iBAAiB,CAAC;IAE/E,IAAI,CAACL,MAAM,CAACO,cAAc,EAAE;MAC1BP,MAAM,CAACO,cAAc,GAAGR,wBAAwB;IAClD;;IAEA;AACA;AACA;IACA,IAAI,OAAOS,KAAK,KAAK,WAAW,EAAE;MAChCA,KAAK,GAAG,CAAC,CAAC,EAAC;IACb;IAEA,IAAIC,kCAAkC,GAAG,KAAK;;IAE9C;AACA;AACA;AACA;AACA;AACA;IACA,MAAMC,SAAS,GAAG,MAAAA,CAAOC,GAAG,EAAEC,SAAS,KAAK;MAC1C,KAAK,MAAMC,CAAC,IAAIF,GAAG,EAAE;QACnB,IAAI,MAAMC,SAAS,CAACC,CAAC,CAAC,EAAE,OAAO,IAAI;MACrC;MACA,OAAO,KAAK;IACd,CAAC;IAEDC,MAAM,CAACC,MAAM,CAACP,KAAK,EAAE;MACnB;AACF;AACA;AACA;AACA;AACA;AACA;MACEQ,YAAY,EAAE,IAAI;MAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEC,eAAe,EAAE,eAAAA,CAAgBC,QAAQ,EAAEC,OAAO,EAAE;QAClDX,KAAK,CAACY,cAAc,CAACF,QAAQ,CAAC;QAE9BC,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;UACEM,YAAY,EAAE;QAChB,CAAC,EACDF,OACF,CAAC;QAED,IAAIG,UAAU,GAAG,IAAI;QAErB,MAAMC,YAAY,GAAG,MAAMvB,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;UAAEC,GAAG,EAAEP;QAAS,CAAC,CAAC;QAEvE,IAAIK,YAAY,EAAE;UAChB,MAAMvB,MAAM,CAACM,KAAK,CAACoB,WAAW,CAC5B;YAAED,GAAG,EAAEP;UAAS,CAAC,EACjB;YAAES,YAAY,EAAE;cAAEC,QAAQ,EAAE;YAAG;UAAE,CACnC,CAAC;UACD,OAAO,IAAI;QACb,CAAC,MAAM;UACLN,UAAU,GAAG,MAAMtB,MAAM,CAACM,KAAK,CAACuB,WAAW,CAAC;YAC1CJ,GAAG,EAAEP,QAAQ;YACbU,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QAEA,IAAI,CAACN,UAAU,EAAE;UACf,IAAIH,OAAO,CAACE,YAAY,EAAE,OAAO,IAAI;UACrC,MAAM,IAAIS,KAAK,CAAC,QAAQ,GAAGZ,QAAQ,GAAG,mBAAmB,CAAC;QAC5D;QAEA,OAAOI,UAAU;MACnB,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACES,eAAe,EAAE,eAAAA,CAAgBb,QAAQ,EAAE;QACzC,IAAIZ,KAAK;QACT,IAAI0B,cAAc;QAElBxB,KAAK,CAACY,cAAc,CAACF,QAAQ,CAAC;;QAE9B;QACA,MAAMlB,MAAM,CAACO,cAAc,CAAC0B,WAAW,CAAC;UACtC,UAAU,EAAEf;QACd,CAAC,CAAC;QAEF,GAAG;UACD;UACAZ,KAAK,GAAG,MAAME,KAAK,CAAC0B,wBAAwB,CAC1C,MAAMlC,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;YAAEC,GAAG,EAAEP;UAAS,CAAC,CACnD,CAAC;UAED,KAAK,MAAMiB,CAAC,IAAI,MAAMnC,MAAM,CAACM,KAAK,CAC/B8B,IAAI,CAAC;YAAEX,GAAG,EAAE;cAAEY,GAAG,EAAE/B;YAAM;UAAE,CAAC,CAAC,CAC7BgC,UAAU,CAAC,CAAC,EAAE;YACf,MAAMtC,MAAM,CAACM,KAAK,CAACoB,WAAW,CAC5B;cACED,GAAG,EAAEU,CAAC,CAACV;YACT,CAAC,EACD;cACEc,KAAK,EAAE;gBACLX,QAAQ,EAAE;kBACRH,GAAG,EAAEP;gBACP;cACF;YACF,CACF,CAAC;YAEDc,cAAc,GAAG,MAAMxB,KAAK,CAACgC,2BAA2B,CACtD,MAAMxC,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;cAAEC,GAAG,EAAEU,CAAC,CAACV;YAAI,CAAC,CAChD,CAAC;YACD,MAAMzB,MAAM,CAACO,cAAc,CAACmB,WAAW,CACrC;cACE,UAAU,EAAES,CAAC,CAACV;YAChB,CAAC,EACD;cACEgB,IAAI,EAAE;gBACJT,cAAc,EAAE,CAACG,CAAC,CAACV,GAAG,EAAE,GAAGO,cAAc,CAAC,CAACU,GAAG,CAAEC,EAAE,KAAM;kBACtDlB,GAAG,EAAEkB;gBACP,CAAC,CAAC;cACJ;YACF,CAAC,EACD;cAAEC,KAAK,EAAE;YAAK,CAChB,CAAC;UACH;QACF,CAAC,QAAQtC,KAAK,CAACuC,MAAM,GAAG,CAAC;;QAEzB;QACA,MAAM7C,MAAM,CAACM,KAAK,CAAC2B,WAAW,CAAC;UAAER,GAAG,EAAEP;QAAS,CAAC,CAAC;MACnD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE4B,eAAe,EAAE,eAAAA,CAAgBC,OAAO,EAAEC,OAAO,EAAE;QACjD,IAAIC,KAAK;QAETzC,KAAK,CAACY,cAAc,CAAC2B,OAAO,CAAC;QAC7BvC,KAAK,CAACY,cAAc,CAAC4B,OAAO,CAAC;QAE7B,IAAID,OAAO,KAAKC,OAAO,EAAE;QAEzB,MAAME,IAAI,GAAG,MAAMlD,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;UAAEC,GAAG,EAAEsB;QAAQ,CAAC,CAAC;QAE9D,IAAI,CAACG,IAAI,EAAE;UACT,MAAM,IAAIpB,KAAK,CAAC,QAAQ,GAAGiB,OAAO,GAAG,mBAAmB,CAAC;QAC3D;QAEAG,IAAI,CAACzB,GAAG,GAAGuB,OAAO;QAElB,MAAMhD,MAAM,CAACM,KAAK,CAACuB,WAAW,CAACqB,IAAI,CAAC;QAEpC,GAAG;UACDD,KAAK,GAAG,MAAMjD,MAAM,CAACO,cAAc,CAACmB,WAAW,CAC7C;YACE,UAAU,EAAEqB;UACd,CAAC,EACD;YACEN,IAAI,EAAE;cACJ,UAAU,EAAEO;YACd;UACF,CAAC,EACD;YAAEJ,KAAK,EAAE;UAAK,CAChB,CAAC;QACH,CAAC,QAAQK,KAAK,GAAG,CAAC;QAElB,GAAG;UACDA,KAAK,GAAG,MAAMjD,MAAM,CAACO,cAAc,CAACmB,WAAW,CAC7C;YACE,oBAAoB,EAAEqB;UACxB,CAAC,EACD;YACEN,IAAI,EAAE;cACJ,sBAAsB,EAAEO;YAC1B;UACF,CAAC,EACD;YAAEJ,KAAK,EAAE;UAAK,CAChB,CAAC;QACH,CAAC,QAAQK,KAAK,GAAG,CAAC;QAElB,GAAG;UACDA,KAAK,GAAG,MAAMjD,MAAM,CAACM,KAAK,CAACoB,WAAW,CACpC;YACE,cAAc,EAAEqB;UAClB,CAAC,EACD;YACEN,IAAI,EAAE;cACJ,gBAAgB,EAAEO;YACpB;UACF,CAAC,EACD;YAAEJ,KAAK,EAAE;UAAK,CAChB,CAAC;QACH,CAAC,QAAQK,KAAK,GAAG,CAAC;QAElB,MAAMjD,MAAM,CAACM,KAAK,CAAC2B,WAAW,CAAC;UAAER,GAAG,EAAEsB;QAAQ,CAAC,CAAC;MAClD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEI,qBAAqB,EAAE,eAAAA,CAAgBC,UAAU,EAAEC,UAAU,EAAE;QAC7D;QACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;QAEzD,KAAK,MAAMlC,QAAQ,IAAIkC,UAAU,EAAE;UACjC,MAAM5C,KAAK,CAACgD,qBAAqB,CAACtC,QAAQ,EAAEmC,UAAU,CAAC;QACzD;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEG,qBAAqB,EAAE,eAAAA,CAAgBtC,QAAQ,EAAEmC,UAAU,EAAE;QAC3D7C,KAAK,CAACY,cAAc,CAACF,QAAQ,CAAC;QAC9BV,KAAK,CAACY,cAAc,CAACiC,UAAU,CAAC;;QAEhC;QACA,MAAMH,IAAI,GAAG,MAAMlD,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;UAAEC,GAAG,EAAEP;QAAS,CAAC,CAAC;QAE/D,IAAI,CAACgC,IAAI,EAAE;UACT,MAAM,IAAIpB,KAAK,UAAA2B,MAAA,CAAUvC,QAAQ,sBAAmB,CAAC;QACvD;;QAEA;QACA,IAAI,CAAC,MAAMV,KAAK,CAACgC,2BAA2B,CAACU,IAAI,CAAC,EAAEQ,QAAQ,CAACL,UAAU,CAAC,EAAE;UACxE,MAAM,IAAIvB,KAAK,WAAA2B,MAAA,CACHvC,QAAQ,aAAAuC,MAAA,CAAUJ,UAAU,0BACxC,CAAC;QACH;QAEA,MAAMJ,KAAK,GAAG,MAAMjD,MAAM,CAACM,KAAK,CAACoB,WAAW,CAC1C;UACED,GAAG,EAAE4B,UAAU;UACf,cAAc,EAAE;YACdM,GAAG,EAAET,IAAI,CAACzB;UACZ;QACF,CAAC,EACD;UACEmC,KAAK,EAAE;YACLhC,QAAQ,EAAE;cACRH,GAAG,EAAEyB,IAAI,CAACzB;YACZ;UACF;QACF,CACF,CAAC;;QAED;QACA;QACA,IAAI,CAACwB,KAAK,EAAE;QAEZ,MAAMjD,MAAM,CAACO,cAAc,CAACmB,WAAW,CACrC;UACE,oBAAoB,EAAE2B;QACxB,CAAC,EACD;UACEO,KAAK,EAAE;YACL5B,cAAc,EAAE;cACd6B,KAAK,EAAE,CACLX,IAAI,CAACzB,GAAG,EACR,IAAI,MAAMjB,KAAK,CAACgC,2BAA2B,CAACU,IAAI,CAAC,CAAC,CACnD,CAACR,GAAG,CAAEP,CAAC,KAAM;gBAAEV,GAAG,EAAEU;cAAE,CAAC,CAAC;YAC3B;UACF;QACF,CAAC,EACD;UAAES,KAAK,EAAE;QAAK,CAChB,CAAC;MACH,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEkB,0BAA0B,EAAE,eAAAA,CAAgBV,UAAU,EAAEC,UAAU,EAAE;QAClE;QACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;QAEzD,KAAK,MAAMlC,QAAQ,IAAIkC,UAAU,EAAE;UACjC,MAAM5C,KAAK,CAACuD,0BAA0B,CAAC7C,QAAQ,EAAEmC,UAAU,CAAC;QAC9D;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEU,0BAA0B,EAAE,eAAAA,CAAgB7C,QAAQ,EAAEmC,UAAU,EAAE;QAChE7C,KAAK,CAACY,cAAc,CAACF,QAAQ,CAAC;QAC9BV,KAAK,CAACY,cAAc,CAACiC,UAAU,CAAC;;QAEhC;QACA;QACA,MAAMH,IAAI,GAAG,MAAMlD,MAAM,CAACM,KAAK,CAACkB,YAAY,CAC1C;UAAEC,GAAG,EAAEP;QAAS,CAAC,EACjB;UAAE8C,MAAM,EAAE;YAAEvC,GAAG,EAAE;UAAE;QAAE,CACvB,CAAC;QAED,IAAI,CAACyB,IAAI,EAAE;UACT,MAAM,IAAIpB,KAAK,UAAA2B,MAAA,CAAUvC,QAAQ,sBAAmB,CAAC;QACvD;QAEA,MAAM+B,KAAK,GAAG,MAAMjD,MAAM,CAACM,KAAK,CAACoB,WAAW,CAC1C;UACED,GAAG,EAAE4B;QACP,CAAC,EACD;UACEd,KAAK,EAAE;YACLX,QAAQ,EAAE;cACRH,GAAG,EAAEyB,IAAI,CAACzB;YACZ;UACF;QACF,CACF,CAAC;;QAED;QACA;QACA,IAAI,CAACwB,KAAK,EAAE;;QAEZ;QACA,MAAM3C,KAAK,GAAG,CACZ,IAAI,MAAME,KAAK,CAAC0B,wBAAwB,CACtC,MAAMlC,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;UAAEC,GAAG,EAAE4B;QAAW,CAAC,CACrD,CAAC,CAAC,EACFA,UAAU,CACX;QAED,KAAK,MAAMlB,CAAC,IAAI,MAAMnC,MAAM,CAACM,KAAK,CAC/B8B,IAAI,CAAC;UAAEX,GAAG,EAAE;YAAEY,GAAG,EAAE/B;UAAM;QAAE,CAAC,CAAC,CAC7BgC,UAAU,CAAC,CAAC,EAAE;UACf,MAAMN,cAAc,GAAG,MAAMxB,KAAK,CAACgC,2BAA2B,CAC5D,MAAMxC,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;YAAEC,GAAG,EAAEU,CAAC,CAACV;UAAI,CAAC,CAChD,CAAC;UACD,MAAMzB,MAAM,CAACO,cAAc,CAACmB,WAAW,CACrC;YACE,UAAU,EAAES,CAAC,CAACV,GAAG;YACjB,oBAAoB,EAAEyB,IAAI,CAACzB;UAC7B,CAAC,EACD;YACEgB,IAAI,EAAE;cACJT,cAAc,EAAE,CAACG,CAAC,CAACV,GAAG,EAAE,GAAGO,cAAc,CAAC,CAACU,GAAG,CAAEC,EAAE,KAAM;gBACtDlB,GAAG,EAAEkB;cACP,CAAC,CAAC;YACJ;UACF,CAAC,EACD;YAAEC,KAAK,EAAE;UAAK,CAChB,CAAC;QACH;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEqB,oBAAoB,EAAE,eAAAA,CAAgBC,KAAK,EAAE5D,KAAK,EAAEa,OAAO,EAAE;QAC3D,IAAIgD,EAAE;QAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIpC,KAAK,CAAC,wBAAwB,CAAC;QACrD,IAAI,CAACxB,KAAK,EAAE,MAAM,IAAIwB,KAAK,CAAC,wBAAwB,CAAC;QAErDX,OAAO,GAAGX,KAAK,CAAC4D,iBAAiB,CAACjD,OAAO,CAAC;;QAE1C;QACA,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACW,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAC1C,IAAI,CAACZ,KAAK,CAACC,OAAO,CAACjD,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAE1CE,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpCnD,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;UACEwD,QAAQ,EAAE;QACZ,CAAC,EACDpD,OACF,CAAC;QAED,KAAK,MAAMqD,IAAI,IAAIN,KAAK,EAAE;UACxB,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;YAC5BL,EAAE,GAAGK,IAAI,CAAC/C,GAAG;UACf,CAAC,MAAM;YACL0C,EAAE,GAAGK,IAAI;UACX;UAEA,KAAK,MAAMtB,IAAI,IAAI5C,KAAK,EAAE;YACxB,MAAME,KAAK,CAACiE,mBAAmB,CAACN,EAAE,EAAEjB,IAAI,EAAE/B,OAAO,CAAC;UACpD;QACF;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEuD,iBAAiB,EAAE,eAAAA,CAAgBR,KAAK,EAAE5D,KAAK,EAAEa,OAAO,EAAE;QACxD,IAAIgD,EAAE;QAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIpC,KAAK,CAAC,wBAAwB,CAAC;QACrD,IAAI,CAACxB,KAAK,EAAE,MAAM,IAAIwB,KAAK,CAAC,wBAAwB,CAAC;QAErDX,OAAO,GAAGX,KAAK,CAAC4D,iBAAiB,CAACjD,OAAO,CAAC;;QAE1C;QACA,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACW,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAC1C,IAAI,CAACZ,KAAK,CAACC,OAAO,CAACjD,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAE1CE,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpCnD,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;UACEwD,QAAQ,EAAE,KAAK;UACfI,QAAQ,EAAE;QACZ,CAAC,EACDxD,OACF,CAAC;QAED,KAAK,MAAMqD,IAAI,IAAIN,KAAK,EAAE;UACxB,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;YAC5BL,EAAE,GAAGK,IAAI,CAAC/C,GAAG;UACf,CAAC,MAAM;YACL0C,EAAE,GAAGK,IAAI;UACX;UACA;UACA,MAAMI,QAAQ,GAAG;YAAE,UAAU,EAAET;UAAG,CAAC;UACnC,IAAI,CAAChD,OAAO,CAACwD,QAAQ,EAAE;YACrBC,QAAQ,CAACN,KAAK,GAAGnD,OAAO,CAACmD,KAAK;UAChC;UAEA,MAAMtE,MAAM,CAACO,cAAc,CAAC0B,WAAW,CAAC2C,QAAQ,CAAC;;UAEjD;UACA,KAAK,MAAM1B,IAAI,IAAI5C,KAAK,EAAE;YACxB,MAAME,KAAK,CAACiE,mBAAmB,CAACN,EAAE,EAAEjB,IAAI,EAAE/B,OAAO,CAAC;UACpD;QACF;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEsD,mBAAmB,EAAE,eAAAA,CAAgBI,MAAM,EAAE3D,QAAQ,EAAEC,OAAO,EAAE;QAC9DX,KAAK,CAACY,cAAc,CAACF,QAAQ,CAAC;QAC9BV,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpC,IAAI,CAACO,MAAM,EAAE;UACX;QACF;QAEA,MAAM3B,IAAI,GAAG,MAAMlD,MAAM,CAACM,KAAK,CAACkB,YAAY,CAC1C;UAAEC,GAAG,EAAEP;QAAS,CAAC,EACjB;UAAE8C,MAAM,EAAE;YAAEpC,QAAQ,EAAE;UAAE;QAAE,CAC5B,CAAC;QAED,IAAI,CAACsB,IAAI,EAAE;UACT,IAAI/B,OAAO,CAACoD,QAAQ,EAAE;YACpB,OAAO,EAAE;UACX,CAAC,MAAM;YACL,MAAM,IAAIzC,KAAK,CAAC,QAAQ,GAAGZ,QAAQ,GAAG,mBAAmB,CAAC;UAC5D;QACF;;QAEA;QACA;QACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACI,MAAM4D,kBAAkB,GAAG,MAAM9E,MAAM,CAACO,cAAc,CAACiB,YAAY,CAAC;UAClE,UAAU,EAAEqD,MAAM;UAClB,UAAU,EAAE3D,QAAQ;UACpBoD,KAAK,EAAEnD,OAAO,CAACmD;QACjB,CAAC,CAAC;QAEF,IAAIhD,UAAU;QACd,IAAIyD,GAAG;QACP,IAAID,kBAAkB,EAAE;UACtB,MAAM9E,MAAM,CAACO,cAAc,CAACmB,WAAW,CAACoD,kBAAkB,CAACrD,GAAG,EAAE;YAC9DgB,IAAI,EAAE;cACJ+B,IAAI,EAAE;gBAAE/C,GAAG,EAAEoD;cAAO,CAAC;cACrB3B,IAAI,EAAE;gBAAEzB,GAAG,EAAEP;cAAS,CAAC;cACvBoD,KAAK,EAAEnD,OAAO,CAACmD;YACjB;UACF,CAAC,CAAC;UAEFS,GAAG,GAAG,MAAM/E,MAAM,CAACO,cAAc,CAACiB,YAAY,CAACsD,kBAAkB,CAACrD,GAAG,CAAC;QACxE,CAAC,MAAM;UACLH,UAAU,GAAG,MAAMtB,MAAM,CAACO,cAAc,CAACsB,WAAW,CAAC;YACnD2C,IAAI,EAAE;cAAE/C,GAAG,EAAEoD;YAAO,CAAC;YACrB3B,IAAI,EAAE;cAAEzB,GAAG,EAAEP;YAAS,CAAC;YACvBoD,KAAK,EAAEnD,OAAO,CAACmD;UACjB,CAAC,CAAC;QACJ;QAEA,IAAIhD,UAAU,EAAE;UACd,MAAMtB,MAAM,CAACO,cAAc,CAACmB,WAAW,CACrC;YAAED,GAAG,EAAEH;UAAW,CAAC,EACnB;YACEmB,IAAI,EAAE;cACJT,cAAc,EAAE,CACdd,QAAQ,EACR,IAAI,MAAMV,KAAK,CAACgC,2BAA2B,CAACU,IAAI,CAAC,CAAC,CACnD,CAACR,GAAG,CAAEP,CAAC,KAAM;gBAAEV,GAAG,EAAEU;cAAE,CAAC,CAAC;YAC3B;UACF,CACF,CAAC;UAED4C,GAAG,GAAG,MAAM/E,MAAM,CAACO,cAAc,CAACiB,YAAY,CAAC;YAAEC,GAAG,EAAEH;UAAW,CAAC,CAAC;QACrE;QACAyD,GAAG,CAACzD,UAAU,GAAGA,UAAU,EAAC;;QAE5B,OAAOyD,GAAG;MACZ,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE7C,wBAAwB,EAAE,eAAAA,CAAgBgB,IAAI,EAAE;QAC9C,IAAI,CAACA,IAAI,EAAE;UACT,OAAO,EAAE;QACX;QAEA,MAAM8B,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC/B,IAAI,CAACzB,GAAG,CAAC,CAAC;QAEvC,KAAK,MAAMP,QAAQ,IAAI8D,WAAW,EAAE;UAClC,KAAK,MAAME,UAAU,IAAI,MAAMlF,MAAM,CAACM,KAAK,CACxC8B,IAAI,CAAC;YAAE,cAAc,EAAElB;UAAS,CAAC,CAAC,CAClCoB,UAAU,CAAC,CAAC,EAAE;YACf0C,WAAW,CAACG,GAAG,CAACD,UAAU,CAACzD,GAAG,CAAC;UACjC;QACF;QAEAuD,WAAW,CAACI,MAAM,CAAClC,IAAI,CAACzB,GAAG,CAAC;QAE5B,OAAO,CAAC,GAAGuD,WAAW,CAAC;MACzB,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACExC,2BAA2B,EAAE,eAAAA,CAAgBU,IAAI,EAAE;QACjD,MAAMlB,cAAc,GAAG,IAAIiD,GAAG,CAAC,CAAC;QAChC,MAAMI,WAAW,GAAG,IAAIJ,GAAG,CAAC,CAAC/B,IAAI,CAAC,CAAC;QAEnC,KAAK,MAAMf,CAAC,IAAIkD,WAAW,EAAE;UAC3B,MAAM/E,KAAK,GAAG,MAAMN,MAAM,CAACM,KAAK,CAC7B8B,IAAI,CACH;YAAEX,GAAG,EAAE;cAAEY,GAAG,EAAEF,CAAC,CAACP,QAAQ,CAACc,GAAG,CAAEP,CAAC,IAAKA,CAAC,CAACV,GAAG;YAAE;UAAE,CAAC,EAC9C;YAAEuC,MAAM,EAAE;cAAEpC,QAAQ,EAAE;YAAE;UAAE,CAC5B,CAAC,CACAU,UAAU,CAAC,CAAC;UAEf,KAAK,MAAMK,EAAE,IAAIrC,KAAK,EAAE;YACtB0B,cAAc,CAACmD,GAAG,CAACxC,EAAE,CAAClB,GAAG,CAAC;YAC1B4D,WAAW,CAACF,GAAG,CAACxC,EAAE,CAAC;UACrB;QACF;QAEA,OAAO,CAAC,GAAGX,cAAc,CAAC;MAC5B,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEsD,yBAAyB,EAAE,eAAAA,CAAgBpB,KAAK,EAAE5D,KAAK,EAAEa,OAAO,EAAE;QAChE,IAAI,CAAC+C,KAAK,EAAE,MAAM,IAAIpC,KAAK,CAAC,wBAAwB,CAAC;QACrD,IAAI,CAACxB,KAAK,EAAE,MAAM,IAAIwB,KAAK,CAAC,wBAAwB,CAAC;QAErDX,OAAO,GAAGX,KAAK,CAAC4D,iBAAiB,CAACjD,OAAO,CAAC;;QAE1C;QACA,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACW,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAC1C,IAAI,CAACZ,KAAK,CAACC,OAAO,CAACjD,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAE1CE,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpC,KAAK,MAAME,IAAI,IAAIN,KAAK,EAAE;UACxB,IAAI,CAACM,IAAI,EAAE;UAEX,KAAK,MAAMtB,IAAI,IAAI5C,KAAK,EAAE;YACxB,IAAI6D,EAAE;YACN,IAAI,OAAOK,IAAI,KAAK,QAAQ,EAAE;cAC5BL,EAAE,GAAGK,IAAI,CAAC/C,GAAG;YACf,CAAC,MAAM;cACL0C,EAAE,GAAGK,IAAI;YACX;YAEA,MAAMhE,KAAK,CAAC+E,wBAAwB,CAACpB,EAAE,EAAEjB,IAAI,EAAE/B,OAAO,CAAC;UACzD;QACF;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEoE,wBAAwB,EAAE,eAAAA,CAAgBV,MAAM,EAAE3D,QAAQ,EAAEC,OAAO,EAAE;QACnEX,KAAK,CAACY,cAAc,CAACF,QAAQ,CAAC;QAC9BV,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpC,IAAI,CAACO,MAAM,EAAE;QAEb,MAAMD,QAAQ,GAAG;UACf,UAAU,EAAEC,MAAM;UAClB,UAAU,EAAE3D;QACd,CAAC;QAED,IAAI,CAACC,OAAO,CAACwD,QAAQ,EAAE;UACrBC,QAAQ,CAACN,KAAK,GAAGnD,OAAO,CAACmD,KAAK;QAChC;QAEA,MAAMtE,MAAM,CAACO,cAAc,CAAC0B,WAAW,CAAC2C,QAAQ,CAAC;MACnD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEY,iBAAiB,EAAE,eAAAA,CAAgBhB,IAAI,EAAElE,KAAK,EAAEa,OAAO,EAAE;QACvD,IAAIgD,EAAE;QAENhD,OAAO,GAAGX,KAAK,CAAC4D,iBAAiB,CAACjD,OAAO,CAAC;;QAE1C;QACA,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACjD,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAE1CA,KAAK,GAAGA,KAAK,CAACmF,MAAM,CAAEtD,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC;QAEtC,IAAI,CAAC7B,KAAK,CAACuC,MAAM,EAAE,OAAO,KAAK;QAE/BrC,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpCnD,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;UACE4D,QAAQ,EAAE;QACZ,CAAC,EACDxD,OACF,CAAC;QAED,IAAIqD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UACpCL,EAAE,GAAGK,IAAI,CAAC/C,GAAG;QACf,CAAC,MAAM;UACL0C,EAAE,GAAGK,IAAI;QACX;QAEA,IAAI,CAACL,EAAE,EAAE,OAAO,KAAK;QACrB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAO,KAAK;QAExC,MAAMS,QAAQ,GAAG;UACf,UAAU,EAAET;QACd,CAAC;QAED,IAAI,CAAChD,OAAO,CAACwD,QAAQ,EAAE;UACrBC,QAAQ,CAACN,KAAK,GAAG;YAAEjC,GAAG,EAAE,CAAClB,OAAO,CAACmD,KAAK,EAAE,IAAI;UAAE,CAAC;QACjD;QAEA,MAAMS,GAAG,GAAG,MAAMrE,SAAS,CAACJ,KAAK,EAAE,MAAOY,QAAQ,IAAK;UACrD0D,QAAQ,CAAC,oBAAoB,CAAC,GAAG1D,QAAQ;UACzC,MAAMwE,GAAG,GACP,CAAC,MAAM1F,MAAM,CAACO,cAAc,CAACoF,cAAc,CAACf,QAAQ,EAAE;YAAEgB,KAAK,EAAE;UAAE,CAAC,CAAC,IAAI,CAAC;UAC1E,OAAOF,GAAG;QACZ,CAAC,CAAC;QAEF,OAAOX,GAAG;MACZ,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEc,oBAAoB,EAAE,eAAAA,CAAgBrB,IAAI,EAAErD,OAAO,EAAE;QACnD,IAAIgD,EAAE;QAENhD,OAAO,GAAGX,KAAK,CAAC4D,iBAAiB,CAACjD,OAAO,CAAC;QAE1CX,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpCnD,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;UACtB+E,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,KAAK;UACnBpB,QAAQ,EAAE,KAAK;UACfqB,UAAU,EAAE;QACd,CAAC,EAAE7E,OAAO,CAAC;QAEX,IAAIqD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UACpCL,EAAE,GAAGK,IAAI,CAAC/C,GAAG;QACf,CAAC,MAAM;UACL0C,EAAE,GAAGK,IAAI;QACX;QAEA,IAAI,CAACL,EAAE,EAAE,OAAO,EAAE;QAElB,MAAMS,QAAQ,GAAG;UACf,UAAU,EAAET;QACd,CAAC;QAED,MAAMsB,MAAM,GAAG;UACbzB,MAAM,EAAE;YAAE,oBAAoB,EAAE;UAAE;QACpC,CAAC;QAED,IAAI,CAAC7C,OAAO,CAACwD,QAAQ,EAAE;UACrBC,QAAQ,CAACN,KAAK,GAAG;YAAEjC,GAAG,EAAE,CAAClB,OAAO,CAACmD,KAAK;UAAE,CAAC;UAEzC,IAAI,CAACnD,OAAO,CAAC6E,UAAU,EAAE;YACvBpB,QAAQ,CAACN,KAAK,CAACjC,GAAG,CAAC4D,IAAI,CAAC,IAAI,CAAC;UAC/B;QACF;QAEA,IAAI9E,OAAO,CAAC4E,YAAY,EAAE;UACxB,OAAON,MAAM,CAACzB,MAAM,CAAC,oBAAoB,CAAC;UAC1CyB,MAAM,CAACzB,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;QAC/B;QAEA,IAAI7C,OAAO,CAAC2E,WAAW,EAAE;UACvB,OAAOL,MAAM,CAACzB,MAAM;QACtB;QAEA,MAAM1D,KAAK,GAAG,MAAMN,MAAM,CAACO,cAAc,CAAC6B,IAAI,CAACwC,QAAQ,EAAEa,MAAM,CAAC,CAACnD,UAAU,CAAC,CAAC;QAE7E,IAAInB,OAAO,CAAC2E,WAAW,EAAE;UACvB,OAAOxF,KAAK;QACd;QAEA,OAAO,CACL,GAAG,IAAI2E,GAAG,CACR3E,KAAK,CAAC4F,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;UAC7B,IAAIA,OAAO,CAACpE,cAAc,EAAE;YAC1B,OAAOmE,GAAG,CAAC1C,MAAM,CAAC2C,OAAO,CAACpE,cAAc,CAACU,GAAG,CAAEP,CAAC,IAAKA,CAAC,CAACV,GAAG,CAAC,CAAC;UAC7D,CAAC,MAAM,IAAI2E,OAAO,CAAClD,IAAI,EAAE;YACvBiD,GAAG,CAACF,IAAI,CAACG,OAAO,CAAClD,IAAI,CAACzB,GAAG,CAAC;UAC5B;UACA,OAAO0E,GAAG;QACZ,CAAC,EAAE,EAAE,CACP,CAAC,CACF;MACH,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEE,WAAW,EAAE,SAAAA,CAAUC,YAAY,EAAE;QACnCA,YAAY,GAAGA,YAAY,IAAI;UAAEC,IAAI,EAAE;YAAE9E,GAAG,EAAE;UAAE;QAAE,CAAC;QAEnD,OAAOzB,MAAM,CAACM,KAAK,CAAC8B,IAAI,CAAC,CAAC,CAAC,EAAEkE,YAAY,CAAC;MAC5C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEE,mBAAmB,EAAE,eAAAA,CAAgBlG,KAAK,EAAEa,OAAO,EAAEmF,YAAY,EAAE;QACjE,MAAMG,GAAG,GAAG,CACV,MAAMjG,KAAK,CAACkG,yBAAyB,CAACpG,KAAK,EAAEa,OAAO,CAAC,CAACmB,UAAU,CAAC,CAAC,EAClEI,GAAG,CAAEiE,CAAC,IAAKA,CAAC,CAACnC,IAAI,CAAC/C,GAAG,CAAC;QAExB,OAAOzB,MAAM,CAACkE,KAAK,CAAC9B,IAAI,CACtB;UAAEX,GAAG,EAAE;YAAEY,GAAG,EAAEoE;UAAI;QAAE,CAAC,EACpBtF,OAAO,IAAIA,OAAO,CAACmF,YAAY,IAAKA,YAAY,IAAI,CAAC,CACxD,CAAC;MACH,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAEEI,yBAAyB,EAAE,SAAAA,CAAUpG,KAAK,EAAEa,OAAO,EAAE;QACnDA,OAAO,GAAGX,KAAK,CAAC4D,iBAAiB,CAACjD,OAAO,CAAC;QAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;UACE4D,QAAQ,EAAE,KAAK;UACf2B,YAAY,EAAE,CAAC;QACjB,CAAC,EACDnF,OACF,CAAC;QAED,OAAOX,KAAK,CAACoG,qBAAqB,CAACtG,KAAK,EAAEa,OAAO,EAAEA,OAAO,CAACmF,YAAY,CAAC;MAC1E,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEM,qBAAqB,EAAE,SAAAA,CAAUtG,KAAK,EAAEa,OAAO,EAAEsE,MAAM,EAAE;QACvDtE,OAAO,GAAGX,KAAK,CAAC4D,iBAAiB,CAACjD,OAAO,CAAC;QAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;UACE4D,QAAQ,EAAE,KAAK;UACfqB,UAAU,EAAE;QACd,CAAC,EACD7E,OACF,CAAC;;QAED;QACA,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACjD,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAE1CE,KAAK,CAAC6D,eAAe,CAAClD,OAAO,CAACmD,KAAK,CAAC;QAEpCmB,MAAM,GAAG3E,MAAM,CAACC,MAAM,CACpB;UACEiD,MAAM,EAAE;YAAE,UAAU,EAAE;UAAE;QAC1B,CAAC,EACDyB,MACF,CAAC;QAED,MAAMb,QAAQ,GAAG;UACf,oBAAoB,EAAE;YAAEvC,GAAG,EAAE/B;UAAM;QACrC,CAAC;QAED,IAAI,CAACa,OAAO,CAACwD,QAAQ,EAAE;UACrBC,QAAQ,CAACN,KAAK,GAAG;YAAEjC,GAAG,EAAE,CAAClB,OAAO,CAACmD,KAAK;UAAE,CAAC;UAEzC,IAAI,CAACnD,OAAO,CAAC6E,UAAU,EAAE;YACvBpB,QAAQ,CAACN,KAAK,CAACjC,GAAG,CAAC4D,IAAI,CAAC,IAAI,CAAC;UAC/B;QACF;QAEA,OAAOjG,MAAM,CAACO,cAAc,CAAC6B,IAAI,CAACwC,QAAQ,EAAEa,MAAM,CAAC;MACrD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEoB,qBAAqB,EAAE,eAAAA,CAAA,EAAyB;QAC9C,IAAI,CAACpG,kCAAkC,EAAE;UACvCA,kCAAkC,GAAG,IAAI;UACzCqG,OAAO,IACLA,OAAO,CAACC,IAAI,CACV,qEACF,CAAC;QACL;QAEA,OAAO,MAAMvG,KAAK,CAACwG,gBAAgB,CAAC,GAAAC,SAAO,CAAC;MAC9C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEC,qBAAqB,EAAE,eAAAA,CAAgB1C,IAAI,EAAElE,KAAK,EAAE;QAClD,IAAI6D,EAAE;QAEN,IAAI7D,KAAK,IAAI,CAACgD,KAAK,CAACC,OAAO,CAACjD,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;QAEnD,IAAIkE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UACpCL,EAAE,GAAGK,IAAI,CAAC/C,GAAG;QACf,CAAC,MAAM;UACL0C,EAAE,GAAGK,IAAI;QACX;QAEA,IAAI,CAACL,EAAE,EAAE,OAAO,EAAE;QAElB,MAAMS,QAAQ,GAAG;UACf,UAAU,EAAET,EAAE;UACdG,KAAK,EAAE;YAAEX,GAAG,EAAE;UAAK;QACrB,CAAC;QAED,IAAIrD,KAAK,EAAE;UACTsE,QAAQ,CAAC,oBAAoB,CAAC,GAAG;YAAEvC,GAAG,EAAE/B;UAAM,CAAC;QACjD;QAEA,MAAM6G,MAAM,GAAG,CACb,MAAMnH,MAAM,CAACO,cAAc,CACxB6B,IAAI,CAACwC,QAAQ,EAAE;UAAEZ,MAAM,EAAE;YAAEM,KAAK,EAAE;UAAE;QAAE,CAAC,CAAC,CACxChC,UAAU,CAAC,CAAC,EACfI,GAAG,CAAE0E,GAAG,IAAKA,GAAG,CAAC9C,KAAK,CAAC;QAEzB,OAAO,CAAC,GAAG,IAAIW,GAAG,CAACkC,MAAM,CAAC,CAAC;MAC7B,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEE,gBAAgB,EAAE,eAAAA,CAAgBtE,OAAO,EAAEC,OAAO,EAAE;QAClD,IAAIC,KAAK;QAETzC,KAAK,CAAC6D,eAAe,CAACtB,OAAO,CAAC;QAC9BvC,KAAK,CAAC6D,eAAe,CAACrB,OAAO,CAAC;QAE9B,IAAID,OAAO,KAAKC,OAAO,EAAE;QAEzB,GAAG;UACDC,KAAK,GAAG,MAAMjD,MAAM,CAACO,cAAc,CAACmB,WAAW,CAC7C;YACE4C,KAAK,EAAEvB;UACT,CAAC,EACD;YACEN,IAAI,EAAE;cACJ6B,KAAK,EAAEtB;YACT;UACF,CAAC,EACD;YAAEJ,KAAK,EAAE;UAAK,CAChB,CAAC;QACH,CAAC,QAAQK,KAAK,GAAG,CAAC;MACpB,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEqE,gBAAgB,EAAE,eAAAA,CAAgBC,IAAI,EAAE;QACtC/G,KAAK,CAAC6D,eAAe,CAACkD,IAAI,CAAC;QAE3B,MAAMvH,MAAM,CAACO,cAAc,CAAC0B,WAAW,CAAC;UAAEqC,KAAK,EAAEiD;QAAK,CAAC,CAAC;MAC1D,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEnG,cAAc,EAAE,SAAAA,CAAUF,QAAQ,EAAE;QAClC,IACE,CAACA,QAAQ,IACT,OAAOA,QAAQ,KAAK,QAAQ,IAC5BA,QAAQ,CAACsG,IAAI,CAAC,CAAC,KAAKtG,QAAQ,EAC5B;UACA,MAAM,IAAIY,KAAK,uBAAA2B,MAAA,CAAuBvC,QAAQ,OAAI,CAAC;QACrD;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEuG,eAAe,EAAE,eAAAA,CAAgBC,cAAc,EAAEC,aAAa,EAAE;QAC9D,IAAID,cAAc,KAAKC,aAAa,EAAE;UACpC,OAAO,IAAI;QACb;QAEA,IAAID,cAAc,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;UACnD,OAAO,KAAK;QACd;QAEAnH,KAAK,CAACY,cAAc,CAACsG,cAAc,CAAC;QACpClH,KAAK,CAACY,cAAc,CAACuG,aAAa,CAAC;QAEnC,IAAIC,YAAY,GAAG,CAACF,cAAc,CAAC;QACnC,OAAOE,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;UAChC,MAAM3B,QAAQ,GAAG0G,YAAY,CAACC,GAAG,CAAC,CAAC;UAEnC,IAAI3G,QAAQ,KAAKyG,aAAa,EAAE;YAC9B,OAAO,IAAI;UACb;UAEA,MAAMzE,IAAI,GAAG,MAAMlD,MAAM,CAACM,KAAK,CAACkB,YAAY,CAAC;YAAEC,GAAG,EAAEP;UAAS,CAAC,CAAC;;UAE/D;UACA,IAAI,CAACgC,IAAI,EAAE;UAEX0E,YAAY,GAAGA,YAAY,CAACnE,MAAM,CAACP,IAAI,CAACtB,QAAQ,CAACc,GAAG,CAAEP,CAAC,IAAKA,CAAC,CAACV,GAAG,CAAC,CAAC;QACrE;QAEA,OAAO,KAAK;MACd,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE2C,iBAAiB,EAAE,SAAAA,CAAUjD,OAAO,EAAE;QACpCA,OAAO,GAAGA,OAAO,KAAK2G,SAAS,GAAG,CAAC,CAAC,GAAG3G,OAAO;;QAE9C;QACA;QACA,IAAIA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAClFA,OAAO,GAAG;YAAEmD,KAAK,EAAEnD;UAAQ,CAAC;QAC9B;QAEAA,OAAO,CAACmD,KAAK,GAAG9D,KAAK,CAACuH,mBAAmB,CAAC5G,OAAO,CAACmD,KAAK,CAAC;QAExD,OAAOnD,OAAO;MAChB,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE4G,mBAAmB,EAAE,SAAAA,CAAUC,SAAS,EAAE;QACxC;QACA,IAAIA,SAAS,IAAI,IAAI,EAAE;UACrB,OAAO,IAAI;QACb,CAAC,MAAM;UACL,OAAOA,SAAS;QAClB;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACE3D,eAAe,EAAE,SAAAA,CAAU2D,SAAS,EAAE;QACpC,IAAIA,SAAS,KAAK,IAAI,EAAE;QAExB,IACE,CAACA,SAAS,IACV,OAAOA,SAAS,KAAK,QAAQ,IAC7BA,SAAS,CAACR,IAAI,CAAC,CAAC,KAAKQ,SAAS,EAC9B;UACA,MAAM,IAAIlG,KAAK,wBAAA2B,MAAA,CAAwBuE,SAAS,OAAI,CAAC;QACvD;MACF;IACF,CAAC,CAAC;IAAAC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC/yCF,IAAItI,eAAe,EAACC,wBAAwB;IAACsI,MAAM,CAACpI,IAAI,CAAC,sBAAsB,EAAC;MAACH,eAAeA,CAACI,CAAC,EAAC;QAACJ,eAAe,GAACI,CAAC;MAAA,CAAC;MAACH,wBAAwBA,CAACG,CAAC,EAAC;QAACH,wBAAwB,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAE/O,MAAMkI,iBAAiB,GAAGvI,wBAAwB,CAACwI,gBAAgB,CAACC,IAAI,CAACzI,wBAAwB,CAAC;IAClG,MAAM0I,YAAY,GAAG3I,eAAe,CAACyI,gBAAgB,CAACC,IAAI,CAAC1I,eAAe,CAAC;IAE3E,MAAM4I,OAAO,GAAG,CACd;MAAE,UAAU,EAAE,CAAC;MAAE,oBAAoB,EAAE,CAAC;MAAEpE,KAAK,EAAE;IAAE,CAAC,EACpD;MAAE,UAAU,EAAE,CAAC;MAAE,UAAU,EAAE,CAAC;MAAEA,KAAK,EAAE;IAAE,CAAC,EAC1C;MAAE,UAAU,EAAE;IAAE,CAAC,EACjB;MAAEA,KAAK,EAAE,CAAC;MAAE,UAAU,EAAE,CAAC;MAAE,oBAAoB,EAAE;IAAE,CAAC;IAAE;IACtD;MAAE,oBAAoB,EAAE;IAAE,CAAC,CAC5B;IACD,KAAK,MAAMqE,KAAK,IAAID,OAAO,EAAE;MAC3BJ,iBAAiB,CAACK,KAAK,CAAC;IAC1B;IACAF,YAAY,CAAC;MAAE,cAAc,EAAE;IAAE,CAAC,CAAC;IAAAR,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/alanning_roles.js", "sourcesContent": ["/* global Roles */\nimport { Meteor } from 'meteor/meteor'\nimport { Mongo } from 'meteor/mongo'\n\n/**\n * Provides functions related to user authorization. Compatible with built-in Meteor accounts packages.\n *\n * Roles are accessible throgh `Meteor.roles` collection and documents consist of:\n *  - `_id`: role name\n *  - `children`: list of subdocuments:\n *    - `_id`\n *\n * Children list elements are subdocuments so that they can be easier extended in the future or by plugins.\n *\n * Roles can have multiple parents and can be children (subroles) of multiple roles.\n *\n * Example: `{_id: 'admin', children: [{_id: 'editor'}]}`\n *\n * The assignment of a role to a user is stored in a collection, accessible through `Meteor.roleAssignment`.\n * It's documents consist of\n *  - `_id`: Internal MongoDB id\n *  - `role`: A role object which got assigned. Usually only contains the `_id` property\n *  - `user`: A user object, usually only contains the `_id` property\n *  - `scope`: scope name\n *  - `inheritedRoles`: A list of all the roles objects inherited by the assigned role.\n *\n * @module Roles\n */\nexport const RolesCollection = new Mongo.Collection('roles')\n\nif (!Meteor.roles) {\n  Meteor.roles = RolesCollection\n}\n\nexport const RoleAssignmentCollection = new Mongo.Collection('role-assignment')\n\nif (!Meteor.roleAssignment) {\n  Meteor.roleAssignment = RoleAssignmentCollection\n}\n\n/**\n * @class Roles\n */\nif (typeof Roles === 'undefined') {\n  Roles = {} // eslint-disable-line no-global-assign\n}\n\nlet getGroupsForUserDeprecationWarning = false\n\n/**\n * Helper, resolves async some\n * @param {*} arr\n * @param {*} predicate\n * @returns {Promise<Boolean>}\n */\nconst asyncSome = async (arr, predicate) => {\n  for (const e of arr) {\n    if (await predicate(e)) return true\n  }\n  return false\n}\n\nObject.assign(Roles, {\n  /**\n   * Used as a global group (now scope) name. Not used anymore.\n   *\n   * @property GLOBAL_GROUP\n   * @static\n   * @deprecated\n   */\n  GLOBAL_GROUP: null,\n\n  /**\n   * Create a new role.\n   *\n   * @method createRoleAsync\n   * @param {String} roleName Name of role.\n   * @param {Object} [options] Options:\n   *   - `unlessExists`: if `true`, exception will not be thrown in the role already exists\n   * @return {Promise<String>} ID of the new role or null.\n   * @static\n   */\n  createRoleAsync: async function (roleName, options) {\n    Roles._checkRoleName(roleName)\n\n    options = Object.assign(\n      {\n        unlessExists: false\n      },\n      options\n    )\n\n    let insertedId = null\n\n    const existingRole = await Meteor.roles.findOneAsync({ _id: roleName })\n\n    if (existingRole) {\n      await Meteor.roles.updateAsync(\n        { _id: roleName },\n        { $setOnInsert: { children: [] } }\n      )\n      return null\n    } else {\n      insertedId = await Meteor.roles.insertAsync({\n        _id: roleName,\n        children: []\n      })\n    }\n\n    if (!insertedId) {\n      if (options.unlessExists) return null\n      throw new Error(\"Role '\" + roleName + \"' already exists.\")\n    }\n\n    return insertedId\n  },\n\n  /**\n   * Delete an existing role.\n   *\n   * If the role is set for any user, it is automatically unset.\n   *\n   * @method deleteRoleAsync\n   * @param {String} roleName Name of role.\n   * @returns {Promise}\n   * @static\n   */\n  deleteRoleAsync: async function (roleName) {\n    let roles\n    let inheritedRoles\n\n    Roles._checkRoleName(roleName)\n\n    // Remove all assignments\n    await Meteor.roleAssignment.removeAsync({\n      'role._id': roleName\n    })\n\n    do {\n      // For all roles who have it as a dependency ...\n      roles = await Roles._getParentRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: roleName })\n      )\n\n      for (const r of await Meteor.roles\n        .find({ _id: { $in: roles } })\n        .fetchAsync()) {\n        await Meteor.roles.updateAsync(\n          {\n            _id: r._id\n          },\n          {\n            $pull: {\n              children: {\n                _id: roleName\n              }\n            }\n          }\n        )\n\n        inheritedRoles = await Roles._getInheritedRoleNamesAsync(\n          await Meteor.roles.findOneAsync({ _id: r._id })\n        )\n        await Meteor.roleAssignment.updateAsync(\n          {\n            'role._id': r._id\n          },\n          {\n            $set: {\n              inheritedRoles: [r._id, ...inheritedRoles].map((r2) => ({\n                _id: r2\n              }))\n            }\n          },\n          { multi: true }\n        )\n      }\n    } while (roles.length > 0)\n\n    // And finally remove the role itself\n    await Meteor.roles.removeAsync({ _id: roleName })\n  },\n\n  /**\n   * Rename an existing role.\n   *\n   * @method renameRoleAsync\n   * @param {String} oldName Old name of a role.\n   * @param {String} newName New name of a role.\n   * @returns {Promise}\n   * @static\n   */\n  renameRoleAsync: async function (oldName, newName) {\n    let count\n\n    Roles._checkRoleName(oldName)\n    Roles._checkRoleName(newName)\n\n    if (oldName === newName) return\n\n    const role = await Meteor.roles.findOneAsync({ _id: oldName })\n\n    if (!role) {\n      throw new Error(\"Role '\" + oldName + \"' does not exist.\")\n    }\n\n    role._id = newName\n\n    await Meteor.roles.insertAsync(role)\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          'role._id': oldName\n        },\n        {\n          $set: {\n            'role._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          'inheritedRoles._id': oldName\n        },\n        {\n          $set: {\n            'inheritedRoles.$._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    do {\n      count = await Meteor.roles.updateAsync(\n        {\n          'children._id': oldName\n        },\n        {\n          $set: {\n            'children.$._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    await Meteor.roles.removeAsync({ _id: oldName })\n  },\n\n  /**\n   * Add role parent to roles.\n   *\n   * Previous parents are kept (role can have multiple parents). For users which have the\n   * parent role set, new subroles are added automatically.\n   *\n   * @method addRolesToParentAsync\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @static\n   */\n  addRolesToParentAsync: async function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      await Roles._addRoleToParentAsync(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _addRoleToParentAsync\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _addRoleToParentAsync: async function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // query to get role's children\n    const role = await Meteor.roles.findOneAsync({ _id: roleName })\n\n    if (!role) {\n      throw new Error(`Role '${roleName}' does not exist.`)\n    }\n\n    // detect cycles\n    if ((await Roles._getInheritedRoleNamesAsync(role)).includes(parentName)) {\n      throw new Error(\n        `Roles '${roleName}' and '${parentName}' would form a cycle.`\n      )\n    }\n\n    const count = await Meteor.roles.updateAsync(\n      {\n        _id: parentName,\n        'children._id': {\n          $ne: role._id\n        }\n      },\n      {\n        $push: {\n          children: {\n            _id: role._id\n          }\n        }\n      }\n    )\n\n    // if there was no change, parent role might not exist, or role is\n    // already a sub-role; in any case we do not have anything more to do\n    if (!count) return\n\n    await Meteor.roleAssignment.updateAsync(\n      {\n        'inheritedRoles._id': parentName\n      },\n      {\n        $push: {\n          inheritedRoles: {\n            $each: [\n              role._id,\n              ...(await Roles._getInheritedRoleNamesAsync(role))\n            ].map((r) => ({ _id: r }))\n          }\n        }\n      },\n      { multi: true }\n    )\n  },\n\n  /**\n   * Remove role parent from roles.\n   *\n   * Other parents are kept (role can have multiple parents). For users which have the\n   * parent role set, removed subrole is removed automatically.\n   *\n   * @method removeRolesFromParentAsync\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @static\n   */\n  removeRolesFromParentAsync: async function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      await Roles._removeRoleFromParentAsync(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _removeRoleFromParentAsync\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _removeRoleFromParentAsync: async function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // check for role existence\n    // this would not really be needed, but we are trying to match addRolesToParent\n    const role = await Meteor.roles.findOneAsync(\n      { _id: roleName },\n      { fields: { _id: 1 } }\n    )\n\n    if (!role) {\n      throw new Error(`Role '${roleName}' does not exist.`)\n    }\n\n    const count = await Meteor.roles.updateAsync(\n      {\n        _id: parentName\n      },\n      {\n        $pull: {\n          children: {\n            _id: role._id\n          }\n        }\n      }\n    )\n\n    // if there was no change, parent role might not exist, or role was\n    // already not a subrole; in any case we do not have anything more to do\n    if (!count) return\n\n    // For all roles who have had it as a dependency ...\n    const roles = [\n      ...(await Roles._getParentRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: parentName })\n      )),\n      parentName\n    ]\n\n    for (const r of await Meteor.roles\n      .find({ _id: { $in: roles } })\n      .fetchAsync()) {\n      const inheritedRoles = await Roles._getInheritedRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: r._id })\n      )\n      await Meteor.roleAssignment.updateAsync(\n        {\n          'role._id': r._id,\n          'inheritedRoles._id': role._id\n        },\n        {\n          $set: {\n            inheritedRoles: [r._id, ...inheritedRoles].map((r2) => ({\n              _id: r2\n            }))\n          }\n        },\n        { multi: true }\n      )\n    }\n  },\n\n  /**\n   * Add users to roles.\n   *\n   * Adds roles to existing roles for each user.\n   *\n   * @example\n   *     Roles.addUsersToRolesAsync(userId, 'admin')\n   *     Roles.addUsersToRolesAsync(userId, ['view-secrets'], 'example.com')\n   *     Roles.addUsersToRolesAsync([user1, user2], ['user','editor'])\n   *     Roles.addUsersToRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method addUsersToRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  addUsersToRolesAsync: async function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        ifExists: false\n      },\n      options\n    )\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n\n      for (const role of roles) {\n        await Roles._addUserToRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Set users' roles.\n   *\n   * Replaces all existing roles with a new set of roles.\n   *\n   * @example\n   *     await Roles.setUserRolesAsync(userId, 'admin')\n   *     await Roles.setUserRolesAsync(userId, ['view-secrets'], 'example.com')\n   *     await Roles.setUserRolesAsync([user1, user2], ['user','editor'])\n   *     await Roles.setUserRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method setUserRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if `true`, remove all roles the user has, of any scope, if `false`, only the one in the same scope\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  setUserRolesAsync: async function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        ifExists: false,\n        anyScope: false\n      },\n      options\n    )\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n      // we first clear all roles for the user\n      const selector = { 'user._id': id }\n      if (!options.anyScope) {\n        selector.scope = options.scope\n      }\n\n      await Meteor.roleAssignment.removeAsync(selector)\n\n      // and then add all\n      for (const role of roles) {\n        await Roles._addUserToRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Add one user to one role.\n   *\n   * @method _addUserToRoleAsync\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _addUserToRoleAsync: async function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) {\n      return\n    }\n\n    const role = await Meteor.roles.findOneAsync(\n      { _id: roleName },\n      { fields: { children: 1 } }\n    )\n\n    if (!role) {\n      if (options.ifExists) {\n        return []\n      } else {\n        throw new Error(\"Role '\" + roleName + \"' does not exist.\")\n      }\n    }\n\n    // This might create duplicates, because we don't have a unique index, but that's all right. In case there are two, withdrawing the role will effectively kill them both.\n    // TODO revisit this\n    /* const res = await RoleAssignmentCollection.upsertAsync(\n      {\n        \"user._id\": userId,\n        \"role._id\": roleName,\n        scope: options.scope,\n      },\n      {\n        $setOnInsert: {\n          user: { _id: userId },\n          role: { _id: roleName },\n          scope: options.scope,\n        },\n      }\n    ); */\n    const existingAssignment = await Meteor.roleAssignment.findOneAsync({\n      'user._id': userId,\n      'role._id': roleName,\n      scope: options.scope\n    })\n\n    let insertedId\n    let res\n    if (existingAssignment) {\n      await Meteor.roleAssignment.updateAsync(existingAssignment._id, {\n        $set: {\n          user: { _id: userId },\n          role: { _id: roleName },\n          scope: options.scope\n        }\n      })\n\n      res = await Meteor.roleAssignment.findOneAsync(existingAssignment._id)\n    } else {\n      insertedId = await Meteor.roleAssignment.insertAsync({\n        user: { _id: userId },\n        role: { _id: roleName },\n        scope: options.scope\n      })\n    }\n\n    if (insertedId) {\n      await Meteor.roleAssignment.updateAsync(\n        { _id: insertedId },\n        {\n          $set: {\n            inheritedRoles: [\n              roleName,\n              ...(await Roles._getInheritedRoleNamesAsync(role))\n            ].map((r) => ({ _id: r }))\n          }\n        }\n      )\n\n      res = await Meteor.roleAssignment.findOneAsync({ _id: insertedId })\n    }\n    res.insertedId = insertedId // For backward compatibility\n\n    return res\n  },\n\n  /**\n   * Returns an array of role names the given role name is a child of.\n   *\n   * @example\n   *     Roles._getParentRoleNamesAsync({ _id: 'admin', children; [] })\n   *\n   * @method _getParentRoleNamesAsync\n   * @param {object} role The role object\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _getParentRoleNamesAsync: async function (role) {\n    if (!role) {\n      return []\n    }\n\n    const parentRoles = new Set([role._id])\n\n    for (const roleName of parentRoles) {\n      for (const parentRole of await Meteor.roles\n        .find({ 'children._id': roleName })\n        .fetchAsync()) {\n        parentRoles.add(parentRole._id)\n      }\n    }\n\n    parentRoles.delete(role._id)\n\n    return [...parentRoles]\n  },\n\n  /**\n   * Returns an array of role names the given role name is a parent of.\n   *\n   * @example\n   *     Roles._getInheritedRoleNames({ _id: 'admin', children; [] })\n   *\n   * @method _getInheritedRoleNames\n   * @param {object} role The role object\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _getInheritedRoleNamesAsync: async function (role) {\n    const inheritedRoles = new Set()\n    const nestedRoles = new Set([role])\n\n    for (const r of nestedRoles) {\n      const roles = await Meteor.roles\n        .find(\n          { _id: { $in: r.children.map((r) => r._id) } },\n          { fields: { children: 1 } }\n        )\n        .fetchAsync()\n\n      for (const r2 of roles) {\n        inheritedRoles.add(r2._id)\n        nestedRoles.add(r2)\n      }\n    }\n\n    return [...inheritedRoles]\n  },\n\n  /**\n   * Remove users from assigned roles.\n   *\n   * @example\n   *     await Roles.removeUsersFromRolesAsync(userId, 'admin')\n   *     await Roles.removeUsersFromRolesAsync([userId, user2], ['editor'])\n   *     await Roles.removeUsersFromRolesAsync(userId, ['user'], 'group1')\n   *\n   * @method removeUsersFromRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to remove users from. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  removeUsersFromRolesAsync: async function (users, roles, options) {\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    for (const user of users) {\n      if (!user) return\n\n      for (const role of roles) {\n        let id\n        if (typeof user === 'object') {\n          id = user._id\n        } else {\n          id = user\n        }\n\n        await Roles._removeUserFromRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Remove one user from one role.\n   *\n   * @method _removeUserFromRoleAsync\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _removeUserFromRoleAsync: async function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) return\n\n    const selector = {\n      'user._id': userId,\n      'role._id': roleName\n    }\n\n    if (!options.anyScope) {\n      selector.scope = options.scope\n    }\n\n    await Meteor.roleAssignment.removeAsync(selector)\n  },\n\n  /**\n   * Check if user has specified roles.\n   *\n   * @example\n   *     // global roles\n   *     await Roles.userIsInRoleAsync(user, 'admin')\n   *     await Roles.userIsInRoleAsync(user, ['admin','editor'])\n   *     await Roles.userIsInRoleAsync(userId, 'admin')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'])\n   *\n   *     // scope roles (global roles are still checked)\n   *     await Roles.userIsInRoleAsync(user, 'admin', 'group1')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], 'group1')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], {scope: 'group1'})\n   *\n   * @method userIsInRoleAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} roles Name of role or an array of roles to check against. If array,\n   *                             will return `true` if user is in _any_ role.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope; if supplied, limits check to just that scope\n   *     the user's global roles will always be checked whether scope is specified or not\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Promise<Boolean>} `true` if user is in _any_ of the target roles\n   * @static\n   */\n  userIsInRoleAsync: async function (user, roles, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    roles = roles.filter((r) => r != null)\n\n    if (!roles.length) return false\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        anyScope: false\n      },\n      options\n    )\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return false\n    if (typeof id !== 'string') return false\n\n    const selector = {\n      'user._id': id\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope, null] }\n    }\n\n    const res = await asyncSome(roles, async (roleName) => {\n      selector['inheritedRoles._id'] = roleName\n      const out =\n        (await Meteor.roleAssignment.countDocuments(selector, { limit: 1 })) > 0\n      return out\n    })\n\n    return res\n  },\n\n  /**\n   * Retrieve user's roles.\n   *\n   * @method getRolesForUserAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of scope to provide roles for; if not specified, global roles are returned\n   *   - `anyScope`: if set, role can be in any scope (`scope` and `onlyAssigned` options are ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `onlyAssigned`: return only assigned roles and not automatically inferred (like subroles)\n   *   - `fullObjects`: return full roles objects (`true`) or just names (`false`) (`onlyAssigned` option is ignored) (default `false`)\n   *     If you have a use-case for this option, please file a feature-request. You shouldn't need to use it as it's\n   *     result strongly dependent on the internal data structure of this plugin.\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Promise<Array>} Array of user's roles, unsorted.\n   * @static\n   */\n  getRolesForUserAsync: async function (user, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      fullObjects: false,\n      onlyAssigned: false,\n      anyScope: false,\n      onlyScoped: false\n    }, options)\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id\n    }\n\n    const filter = {\n      fields: { 'inheritedRoles._id': 1 }\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    if (options.onlyAssigned) {\n      delete filter.fields['inheritedRoles._id']\n      filter.fields['role._id'] = 1\n    }\n\n    if (options.fullObjects) {\n      delete filter.fields\n    }\n\n    const roles = await Meteor.roleAssignment.find(selector, filter).fetchAsync()\n\n    if (options.fullObjects) {\n      return roles\n    }\n\n    return [\n      ...new Set(\n        roles.reduce((rev, current) => {\n          if (current.inheritedRoles) {\n            return rev.concat(current.inheritedRoles.map((r) => r._id))\n          } else if (current.role) {\n            rev.push(current.role._id)\n          }\n          return rev\n        }, [])\n      )\n    ]\n  },\n\n  /**\n   * Retrieve cursor of all existing roles.\n   *\n   * @method getAllRoles\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `RolesCollection.find(query, options)`.\n   * @return {Cursor} Cursor of existing roles.\n   * @static\n   */\n  getAllRoles: function (queryOptions) {\n    queryOptions = queryOptions || { sort: { _id: 1 } }\n\n    return Meteor.roles.find({}, queryOptions)\n  },\n\n  /**\n   * Retrieve all users who are in target role.\n   *\n   * Options:\n   *\n   * @method getUsersInRoleAsync\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `queryOptions`: options which are passed directly\n   *     through to `Meteor.users.find(query, options)`\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `Meteor.users.find(query, options)`\n   * @return {Promise<Cursor>} Cursor of users in roles.\n   * @static\n   */\n  getUsersInRoleAsync: async function (roles, options, queryOptions) {\n    const ids = (\n      await Roles.getUserAssignmentsForRole(roles, options).fetchAsync()\n    ).map((a) => a.user._id)\n\n    return Meteor.users.find(\n      { _id: { $in: ids } },\n      (options && options.queryOptions) || queryOptions || {}\n    )\n  },\n\n  /**\n   * Retrieve all assignments of a user which are for the target role.\n   *\n   * Options:\n   *\n   * @method getUserAssignmentsForRole\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `queryOptions`: options which are passed directly\n   *     through to `RoleAssignmentCollection.find(query, options)`\n\n   * Alternatively, it can be a scope name string.\n   * @return {Cursor} Cursor of user assignments for roles.\n   * @static\n   */\n  getUserAssignmentsForRole: function (roles, options) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign(\n      {\n        anyScope: false,\n        queryOptions: {}\n      },\n      options\n    )\n\n    return Roles._getUsersInRoleCursor(roles, options, options.queryOptions)\n  },\n\n  /**\n   * @method _getUsersInRoleCursor\n   * @param {Array|String} roles Name of role or an array of roles. If array, ids of users are\n   *                             returned which have at least one of the roles\n   *                             assigned but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [filter] Options which are passed directly\n   *                                through to `RoleAssignmentCollection.find(query, options)`\n   * @return {Object} Cursor to the assignment documents\n   * @private\n   * @static\n   */\n  _getUsersInRoleCursor: function (roles, options, filter) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign(\n      {\n        anyScope: false,\n        onlyScoped: false\n      },\n      options\n    )\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    filter = Object.assign(\n      {\n        fields: { 'user._id': 1 }\n      },\n      filter\n    )\n\n    const selector = {\n      'inheritedRoles._id': { $in: roles }\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    return Meteor.roleAssignment.find(selector, filter)\n  },\n\n  /**\n   * Deprecated. Use `getScopesForUser` instead.\n   *\n   * @method getGroupsForUserAsync\n   * @returns {Promise<Array>}\n   * @static\n   * @deprecated\n   */\n  getGroupsForUserAsync: async function (...args) {\n    if (!getGroupsForUserDeprecationWarning) {\n      getGroupsForUserDeprecationWarning = true\n      console &&\n        console.warn(\n          'getGroupsForUser has been deprecated. Use getScopesForUser instead.'\n        )\n    }\n\n    return await Roles.getScopesForUser(...args)\n  },\n\n  /**\n   * Retrieve users scopes, if any.\n   *\n   * @method getScopesForUserAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} [roles] Name of roles to restrict scopes to.\n   *\n   * @return {Promise<Array>} Array of user's scopes, unsorted.\n   * @static\n   */\n  getScopesForUserAsync: async function (user, roles) {\n    let id\n\n    if (roles && !Array.isArray(roles)) roles = [roles]\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id,\n      scope: { $ne: null }\n    }\n\n    if (roles) {\n      selector['inheritedRoles._id'] = { $in: roles }\n    }\n\n    const scopes = (\n      await Meteor.roleAssignment\n        .find(selector, { fields: { scope: 1 } })\n        .fetchAsync()\n    ).map((obi) => obi.scope)\n\n    return [...new Set(scopes)]\n  },\n\n  /**\n   * Rename a scope.\n   *\n   * Roles assigned with a given scope are changed to be under the new scope.\n   *\n   * @method renameScopeAsync\n   * @param {String} oldName Old name of a scope.\n   * @param {String} newName New name of a scope.\n   * @returns {Promise}\n   * @static\n   */\n  renameScopeAsync: async function (oldName, newName) {\n    let count\n\n    Roles._checkScopeName(oldName)\n    Roles._checkScopeName(newName)\n\n    if (oldName === newName) return\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          scope: oldName\n        },\n        {\n          $set: {\n            scope: newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n  },\n\n  /**\n   * Remove a scope.\n   *\n   * Roles assigned with a given scope are removed.\n   *\n   * @method removeScopeAsync\n   * @param {String} name The name of a scope.\n   * @returns {Promise}\n   * @static\n   */\n  removeScopeAsync: async function (name) {\n    Roles._checkScopeName(name)\n\n    await Meteor.roleAssignment.removeAsync({ scope: name })\n  },\n\n  /**\n   * Throw an exception if `roleName` is an invalid role name.\n   *\n   * @method _checkRoleName\n   * @param {String} roleName A role name to match against.\n   * @private\n   * @static\n   */\n  _checkRoleName: function (roleName) {\n    if (\n      !roleName ||\n      typeof roleName !== 'string' ||\n      roleName.trim() !== roleName\n    ) {\n      throw new Error(`Invalid role name '${roleName}'.`)\n    }\n  },\n\n  /**\n   * Find out if a role is an ancestor of another role.\n   *\n   * WARNING: If you check this on the client, please make sure all roles are published.\n   *\n   * @method isParentOfAsync\n   * @param {String} parentRoleName The role you want to research.\n   * @param {String} childRoleName The role you expect to be among the children of parentRoleName.\n   * @returns {Promise}\n   * @static\n   */\n  isParentOfAsync: async function (parentRoleName, childRoleName) {\n    if (parentRoleName === childRoleName) {\n      return true\n    }\n\n    if (parentRoleName == null || childRoleName == null) {\n      return false\n    }\n\n    Roles._checkRoleName(parentRoleName)\n    Roles._checkRoleName(childRoleName)\n\n    let rolesToCheck = [parentRoleName]\n    while (rolesToCheck.length !== 0) {\n      const roleName = rolesToCheck.pop()\n\n      if (roleName === childRoleName) {\n        return true\n      }\n\n      const role = await Meteor.roles.findOneAsync({ _id: roleName })\n\n      // This should not happen, but this is a problem to address at some other time.\n      if (!role) continue\n\n      rolesToCheck = rolesToCheck.concat(role.children.map((r) => r._id))\n    }\n\n    return false\n  },\n\n  /**\n   * Normalize options.\n   *\n   * @method _normalizeOptions\n   * @param {Object} options Options to normalize.\n   * @return {Object} Normalized options.\n   * @private\n   * @static\n   */\n  _normalizeOptions: function (options) {\n    options = options === undefined ? {} : options\n\n    // TODO Number will error out on scope validation, we can either error it out here\n    // or make it into a string and hence a valid input.\n    if (options === null || typeof options === 'string' || typeof options === 'number') {\n      options = { scope: options }\n    }\n\n    options.scope = Roles._normalizeScopeName(options.scope)\n\n    return options\n  },\n\n  /**\n   * Normalize scope name.\n   *\n   * @method _normalizeScopeName\n   * @param {String} scopeName A scope name to normalize.\n   * @return {String} Normalized scope name.\n   * @private\n   * @static\n   */\n  _normalizeScopeName: function (scopeName) {\n    // map undefined and null to null\n    if (scopeName == null) {\n      return null\n    } else {\n      return scopeName\n    }\n  },\n\n  /**\n   * Throw an exception if `scopeName` is an invalid scope name.\n   *\n   * @method _checkScopeName\n   * @param {String} scopeName A scope name to match against.\n   * @private\n   * @static\n   */\n  _checkScopeName: function (scopeName) {\n    if (scopeName === null) return\n\n    if (\n      !scopeName ||\n      typeof scopeName !== 'string' ||\n      scopeName.trim() !== scopeName\n    ) {\n      throw new Error(`Invalid scope name '${scopeName}'.`)\n    }\n  }\n})\n", "import { RolesCollection, RoleAssignmentCollection } from './roles_common_async'\n\nconst indexFnAssignment = RoleAssignmentCollection.createIndexAsync.bind(RoleAssignmentCollection)\nconst indexFnRoles = RolesCollection.createIndexAsync.bind(RolesCollection)\n\nconst indexes = [\n  { 'user._id': 1, 'inheritedRoles._id': 1, scope: 1 },\n  { 'user._id': 1, 'role._id': 1, scope: 1 },\n  { 'role._id': 1 },\n  { scope: 1, 'user._id': 1, 'inheritedRoles._id': 1 }, // Adding userId and roleId might speed up other queries depending on the first index\n  { 'inheritedRoles._id': 1 }\n]\nfor (const index of indexes) {\n  indexFnAssignment(index)\n}\nindexFnRoles({ 'children._id': 1 })\n"]}