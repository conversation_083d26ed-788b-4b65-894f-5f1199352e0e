{"version": 3, "sources": ["meteor://💻app/packages/check/match.js", "meteor://💻app/packages/check/isPlainObject.js"], "names": ["_toConsumableArray", "module", "link", "default", "v", "_createForOfIteratorHelperLoose", "_typeof", "export", "check", "Match", "isPlainObject", "currentArgumentChecker", "Meteor", "EnvironmentVariable", "hasOwn", "Object", "prototype", "hasOwnProperty", "format", "result", "err", "Error", "message", "path", "value", "pattern", "options", "arguments", "length", "undefined", "throwAllErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getOrNullIfOutsideFiber", "checking", "testSubtree", "Array", "isArray", "map", "r", "Optional", "Maybe", "OneOf", "_len", "args", "_key", "Any", "Where", "condition", "ObjectIncluding", "ObjectWithValues", "Integer", "makeErrorType", "msg", "sanitizedError", "test", "_failIfArgumentsAreNotAllChecked", "f", "context", "description", "<PERSON>rg<PERSON><PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON>", "apply", "throwUnlessAllArgumentsHaveBeenChecked", "choices", "stringForErrorMessage", "onlyShowType", "EJSON", "stringify", "JSON", "stringifyError", "name", "typeofChecks", "String", "Number", "Boolean", "Function", "collectErrors", "errors", "i", "isArguments", "arr<PERSON><PERSON>", "_prependPath", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>llowed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredPatterns", "create", "optionalPatterns", "keys", "for<PERSON>ach", "key", "subPattern", "meteorBabelHelpers", "sanitizeForInObject", "subValue", "obj<PERSON><PERSON>", "call", "createMissingError", "_iterator", "_step", "done", "reverse", "_proto", "_checkingOneValue", "bind", "isNaN", "splice", "_jsKeywords", "base", "match", "indexOf", "isObject", "baseIsArguments", "item", "toString", "callee", "class2type", "fnToString", "ObjectFunctionString", "getProto", "getPrototypeOf", "obj", "proto", "Ctor", "constructor"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,kBAAkB;AAACC,MAAM,CAACC,IAAI,CAAC,0CAA0C,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,kBAAkB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,+BAA+B;AAACJ,MAAM,CAACC,IAAI,CAAC,uDAAuD,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,+BAA+B,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,OAAO;AAACL,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACE,OAAO,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA5XH,MAAM,CAACM,MAAM,CAAC;EAACC,KAAK,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,KAAK;EAAA,CAAC;EAACC,KAAK,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,KAAK;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,aAAa;AAACT,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;EAACQ,aAAa,EAAC,SAAAA,CAASN,CAAC,EAAC;IAACM,aAAa,GAACN,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAG9K;AACA;;AAEA,IAAMO,sBAAsB,GAAG,IAAIC,MAAM,CAACC,mBAAmB,CAAD,CAAC;AAC7D,IAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,IAAMC,MAAM,GAAG,SAAAA,CAAAC,MAAM,EAAI;EACvB,IAAMC,GAAG,GAAG,IAAIX,KAAK,CAACY,KAAK,CAACF,MAAM,CAACG,OAAO,CAAC;EAC3C,IAAIH,MAAM,CAACI,IAAI,EAAE;IACfH,GAAG,CAACE,OAAO,mBAAiBH,MAAM,CAACI,IAAM;IACzCH,GAAG,CAACG,IAAI,GAAGJ,MAAM,CAACI,IAAI;EACxB;EAEA,OAAOH,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASZ,KAAKA,CAACgB,KAAK,EAAEC,OAAO,EAAuC;EAAA,IAArCC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IAAEG,cAAc,EAAE;EAAM,CAAC;EACvE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAMC,UAAU,GAAGpB,sBAAsB,CAACqB,uBAAuB,CAAC,CAAC;EACnE,IAAID,UAAU,EAAE;IACdA,UAAU,CAACE,QAAQ,CAACT,KAAK,CAAC;EAC5B;EAEA,IAAML,MAAM,GAAGe,WAAW,CAACV,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAACI,cAAc,CAAC;EAElE,IAAIX,MAAM,EAAE;IACV,IAAIO,OAAO,CAACI,cAAc,EAAE;MAC1B,MAAMK,KAAK,CAACC,OAAO,CAACjB,MAAM,CAAC,GAAGA,MAAM,CAACkB,GAAG,CAAC,UAAAC,CAAC;QAAA,OAAIpB,MAAM,CAACoB,CAAC,CAAC;MAAA,EAAC,GAAG,CAACpB,MAAM,CAACC,MAAM,CAAC,CAAC;IAC7E,CAAC,MAAM;MACL,MAAMD,MAAM,CAACC,MAAM,CAAC;IACtB;EACF;AACF;AAAC;;AAED;AACA;AACA;AACA;AACO,IAAMV,KAAK,GAAG;EACnB8B,QAAQ,EAAE,SAAAA,CAASd,OAAO,EAAE;IAC1B,OAAO,IAAIc,QAAQ,CAACd,OAAO,CAAC;EAC9B,CAAC;EAEDe,KAAK,EAAE,SAAAA,CAASf,OAAO,EAAE;IACvB,OAAO,IAAIe,KAAK,CAACf,OAAO,CAAC;EAC3B,CAAC;EAEDgB,KAAK,EAAE,SAAAA,CAAA,EAAkB;IAAA,SAAAC,IAAA,GAAAf,SAAA,CAAAC,MAAA,EAANe,IAAI,OAAAR,KAAA,CAAAO,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAJD,IAAI,CAAAC,IAAA,IAAAjB,SAAA,CAAAiB,IAAA;IAAA;IACrB,OAAO,IAAIH,KAAK,CAACE,IAAI,CAAC;EACxB,CAAC;EAEDE,GAAG,EAAE,CAAC,SAAS,CAAC;EAChBC,KAAK,EAAE,SAAAA,CAASC,SAAS,EAAE;IACzB,OAAO,IAAID,KAAK,CAACC,SAAS,CAAC;EAC7B,CAAC;EAEDC,eAAe,EAAE,SAAAA,CAASvB,OAAO,EAAE;IACjC,OAAO,IAAIuB,eAAe,CAACvB,OAAO,CAAC;EACrC,CAAC;EAEDwB,gBAAgB,EAAE,SAAAA,CAASxB,OAAO,EAAE;IAClC,OAAO,IAAIwB,gBAAgB,CAACxB,OAAO,CAAC;EACtC,CAAC;EAED;EACAyB,OAAO,EAAE,CAAC,aAAa,CAAC;EAExB;EACA7B,KAAK,EAAET,MAAM,CAACuC,aAAa,CAAC,aAAa,EAAE,UAAUC,GAAG,EAAE;IACxD,IAAI,CAAC9B,OAAO,qBAAmB8B,GAAK;;IAEpC;IACA;IACA;IACA;IACA,IAAI,CAAC7B,IAAI,GAAG,EAAE;;IAEd;IACA;IACA,IAAI,CAAC8B,cAAc,GAAG,IAAIzC,MAAM,CAACS,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;EAC7D,CAAC,CAAC;EAEF;EACA;EACA;EACA;EACA;EACA;EAEA;AACF;AACA;AACA;AACA;AACA;EACEiC,IAAI,WAAAA,CAAC9B,KAAK,EAAEC,OAAO,EAAE;IACnB,OAAO,CAACS,WAAW,CAACV,KAAK,EAAEC,OAAO,CAAC;EACrC,CAAC;EAED;EACA;EACA;EACA8B,gCAAgC,WAAAA,CAACC,CAAC,EAAEC,OAAO,EAAEd,IAAI,EAAEe,WAAW,EAAE;IAC9D,IAAM3B,UAAU,GAAG,IAAI4B,eAAe,CAAChB,IAAI,EAAEe,WAAW,CAAC;IACzD,IAAMvC,MAAM,GAAGR,sBAAsB,CAACiD,SAAS,CAC7C7B,UAAU,EACV;MAAA,OAAMyB,CAAC,CAACK,KAAK,CAACJ,OAAO,EAAEd,IAAI,CAAC;IAAA,CAC9B,CAAC;;IAED;IACAZ,UAAU,CAAC+B,sCAAsC,CAAC,CAAC;IACnD,OAAO3C,MAAM;EACf;AACF,CAAC;AAAC,IAEIoB,QAAQ;EACZ,SAAAA,SAAYd,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAAC,OAAAc,QAAA;AAAA;AAAA,IAGGC,KAAK;EACT,SAAAA,MAAYf,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAAC,OAAAe,KAAA;AAAA;AAAA,IAGGC,KAAK;EACT,SAAAA,MAAYsB,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACnC,MAAM,KAAK,CAAC,EAAE;MACpC,MAAM,IAAIP,KAAK,CAAC,iDAAiD,CAAC;IACpE;IAEA,IAAI,CAAC0C,OAAO,GAAGA,OAAO;EACxB;EAAC,OAAAtB,KAAA;AAAA;AAAA,IAGGK,KAAK;EACT,SAAAA,MAAYC,SAAS,EAAE;IACrB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC5B;EAAC,OAAAD,KAAA;AAAA;AAAA,IAGGE,eAAe;EACnB,SAAAA,gBAAYvB,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAAC,OAAAuB,eAAA;AAAA;AAAA,IAGGC,gBAAgB;EACpB,SAAAA,iBAAYxB,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAAC,OAAAwB,gBAAA;AAAA;AAGH,IAAMe,qBAAqB,GAAG,SAAAA,CAACxC,KAAK,EAAmB;EAAA,IAAjBE,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAChD,IAAKH,KAAK,KAAK,IAAI,EAAG;IACpB,OAAO,MAAM;EACf;EAEA,IAAKE,OAAO,CAACuC,YAAY,EAAG;IAC1B,OAAA3D,OAAA,CAAckB,KAAK;EACrB;;EAEA;EACA,IAAKlB,OAAA,CAAOkB,KAAK,MAAK,QAAQ,EAAG;IAC/B,OAAO0C,KAAK,CAACC,SAAS,CAAC3C,KAAK,CAAC;EAC/B;EAEA,IAAI;IAEF;IACA;IACA4C,IAAI,CAACD,SAAS,CAAC3C,KAAK,CAAC;EACvB,CAAC,CAAC,OAAO6C,cAAc,EAAE;IACvB,IAAKA,cAAc,CAACC,IAAI,KAAK,WAAW,EAAG;MACzC,OAAAhE,OAAA,CAAckB,KAAK;IACrB;EACF;EAEA,OAAO0C,KAAK,CAACC,SAAS,CAAC3C,KAAK,CAAC;AAC/B,CAAC;AAED,IAAM+C,YAAY,GAAG,CACnB,CAACC,MAAM,EAAE,QAAQ,CAAC,EAClB,CAACC,MAAM,EAAE,QAAQ,CAAC,EAClB,CAACC,OAAO,EAAE,SAAS,CAAC;AAEpB;AACA;AACA,CAACC,QAAQ,EAAE,UAAU,CAAC,EACtB,CAAC9C,SAAS,EAAE,WAAW,CAAC,CACzB;;AAED;AACA,IAAMK,WAAW,GAAG,SAAAA,CAACV,KAAK,EAAEC,OAAO,EAAoD;EAAA,IAAlDmD,aAAa,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAAEkD,MAAM,GAAAlD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAEJ,IAAI,GAAAI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAChF;EACA,IAAIF,OAAO,KAAKhB,KAAK,CAACoC,GAAG,EAAE;IACzB,OAAO,KAAK;EACd;;EAEA;EACA;EACA,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,YAAY,CAAC3C,MAAM,EAAE,EAAEkD,CAAC,EAAE;IAC5C,IAAIrD,OAAO,KAAK8C,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,IAAIxE,OAAA,CAAOkB,KAAK,MAAK+C,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MACd;MAEA,OAAO;QACLxD,OAAO,gBAAciD,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,cAASd,qBAAqB,CAACxC,KAAK,EAAE;UAAEyC,YAAY,EAAE;QAAK,CAAC,CAAG;QACtG1C,IAAI,EAAE;MACR,CAAC;IACH;EACF;EAEA,IAAIE,OAAO,KAAK,IAAI,EAAE;IACpB,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAO,KAAK;IACd;IAEA,OAAO;MACLF,OAAO,0BAAwB0C,qBAAqB,CAACxC,KAAK,CAAG;MAC7DD,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA,IAAI,OAAOE,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;IAC9F,IAAID,KAAK,KAAKC,OAAO,EAAE;MACrB,OAAO,KAAK;IACd;IAEA,OAAO;MACLH,OAAO,gBAAcG,OAAO,cAASuC,qBAAqB,CAACxC,KAAK,CAAG;MACnED,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA,IAAIE,OAAO,KAAKhB,KAAK,CAACyC,OAAO,EAAE;IAE7B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,OAAO1B,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,GAAG,CAAC,MAAMA,KAAK,EAAE;MACtD,OAAO,KAAK;IACd;IAEA,OAAO;MACLF,OAAO,6BAA2B0C,qBAAqB,CAACxC,KAAK,CAAG;MAChED,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA,IAAIE,OAAO,KAAKV,MAAM,EAAE;IACtBU,OAAO,GAAGhB,KAAK,CAACuC,eAAe,CAAC,CAAC,CAAC,CAAC;EACrC;;EAEA;EACA,IAAIvB,OAAO,YAAYU,KAAK,EAAE;IAC5B,IAAIV,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO;QACLN,OAAO,sDAAoD0C,qBAAqB,CAACvC,OAAO,CAAG;QAC3FF,IAAI,EAAE;MACR,CAAC;IACH;IAEA,IAAI,CAACY,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,IAAI,CAACuD,WAAW,CAACvD,KAAK,CAAC,EAAE;MAChD,OAAO;QACLF,OAAO,2BAAyB0C,qBAAqB,CAACxC,KAAK,CAAG;QAC9DD,IAAI,EAAE;MACR,CAAC;IACH;IAGA,KAAK,IAAIuD,EAAC,GAAG,CAAC,EAAElD,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAEkD,EAAC,GAAGlD,MAAM,EAAEkD,EAAC,EAAE,EAAE;MACtD,IAAME,OAAO,GAAMzD,IAAI,SAAIuD,EAAC,MAAG;MAC/B,IAAM3D,MAAM,GAAGe,WAAW,CAACV,KAAK,CAACsD,EAAC,CAAC,EAAErD,OAAO,CAAC,CAAC,CAAC,EAAEmD,aAAa,EAAEC,MAAM,EAAEG,OAAO,CAAC;MAChF,IAAI7D,MAAM,EAAE;QACVA,MAAM,CAACI,IAAI,GAAG0D,YAAY,CAACL,aAAa,GAAGI,OAAO,GAAGF,EAAC,EAAE3D,MAAM,CAACI,IAAI,CAAC;QACpE,IAAI,CAACqD,aAAa,EAAE,OAAOzD,MAAM;QACjC,IAAIb,OAAA,CAAOkB,KAAK,CAACsD,EAAC,CAAC,MAAK,QAAQ,IAAI3D,MAAM,CAACG,OAAO,EAAEuD,MAAM,CAACK,IAAI,CAAC/D,MAAM,CAAC;MACzE;IACF;IAEA,IAAI,CAACyD,aAAa,EAAE,OAAO,KAAK;IAChC,OAAOC,MAAM,CAACjD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAGiD,MAAM;EAC7C;;EAEA;EACA;EACA,IAAIpD,OAAO,YAAYqB,KAAK,EAAE;IAC5B,IAAI3B,OAAM;IACV,IAAI;MACFA,OAAM,GAAGM,OAAO,CAACsB,SAAS,CAACvB,KAAK,CAAC;IACnC,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZ,IAAI,EAAEA,GAAG,YAAYX,KAAK,CAACY,KAAK,CAAC,EAAE;QACjC,MAAMD,GAAG;MACX;MAEA,OAAO;QACLE,OAAO,EAAEF,GAAG,CAACE,OAAO;QACpBC,IAAI,EAAEH,GAAG,CAACG;MACZ,CAAC;IACH;IAEA,IAAIJ,OAAM,EAAE;MACV,OAAO,KAAK;IACd;;IAEA;;IAEA,OAAO;MACLG,OAAO,EAAE,+BAA+B;MACxCC,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAIE,OAAO,YAAYe,KAAK,EAAE;IAC5Bf,OAAO,GAAGhB,KAAK,CAACgC,KAAK,CAACZ,SAAS,EAAE,IAAI,EAAEJ,OAAO,CAACA,OAAO,CAAC;EACzD,CAAC,MAAM,IAAIA,OAAO,YAAYc,QAAQ,EAAE;IACtCd,OAAO,GAAGhB,KAAK,CAACgC,KAAK,CAACZ,SAAS,EAAEJ,OAAO,CAACA,OAAO,CAAC;EACnD;EAEA,IAAIA,OAAO,YAAYgB,KAAK,EAAE;IAC5B,KAAK,IAAIqC,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGrD,OAAO,CAACsC,OAAO,CAACnC,MAAM,EAAE,EAAEkD,GAAC,EAAE;MAC/C,IAAM3D,QAAM,GAAGe,WAAW,CAACV,KAAK,EAAEC,OAAO,CAACsC,OAAO,CAACe,GAAC,CAAC,CAAC;MACrD,IAAI,CAAC3D,QAAM,EAAE;QAEX;QACA,OAAO,KAAK;MACd;;MAEA;IACF;;IAEA;IACA,OAAO;MACLG,OAAO,EAAE,8DAA8D;MACvEC,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA;EACA,IAAIE,OAAO,YAAYkD,QAAQ,EAAE;IAC/B,IAAInD,KAAK,YAAYC,OAAO,EAAE;MAC5B,OAAO,KAAK;IACd;IAEA,OAAO;MACLH,OAAO,iBAAcG,OAAO,CAAC6C,IAAI,IAAI,wBAAwB,CAAE;MAC/D/C,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAI4D,kBAAkB,GAAG,KAAK;EAC9B,IAAIC,iBAAiB;EACrB,IAAI3D,OAAO,YAAYuB,eAAe,EAAE;IACtCmC,kBAAkB,GAAG,IAAI;IACzB1D,OAAO,GAAGA,OAAO,CAACA,OAAO;EAC3B;EAEA,IAAIA,OAAO,YAAYwB,gBAAgB,EAAE;IACvCkC,kBAAkB,GAAG,IAAI;IACzBC,iBAAiB,GAAG,CAAC3D,OAAO,CAACA,OAAO,CAAC;IACrCA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAE;EACjB;EAEA,IAAInB,OAAA,CAAOmB,OAAO,MAAK,QAAQ,EAAE;IAC/B,OAAO;MACLH,OAAO,EAAE,mCAAmC;MAC5CC,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA;EACA;EACA,IAAIjB,OAAA,CAAOkB,KAAK,MAAK,QAAQ,EAAE;IAC7B,OAAO;MACLF,OAAO,4BAAAhB,OAAA,CAAiCkB,KAAK,CAAE;MAC/CD,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAIC,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO;MACLF,OAAO,6BAA6B;MACpCC,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAI,CAAEb,aAAa,CAACc,KAAK,CAAC,EAAE;IAC1B,OAAO;MACLF,OAAO,yBAAyB;MAChCC,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAM8D,gBAAgB,GAAGtE,MAAM,CAACuE,MAAM,CAAC,IAAI,CAAC;EAC5C,IAAMC,gBAAgB,GAAGxE,MAAM,CAACuE,MAAM,CAAC,IAAI,CAAC;EAE5CvE,MAAM,CAACyE,IAAI,CAAC/D,OAAO,CAAC,CAACgE,OAAO,CAAC,UAAAC,GAAG,EAAI;IAClC,IAAMC,UAAU,GAAGlE,OAAO,CAACiE,GAAG,CAAC;IAC/B,IAAIC,UAAU,YAAYpD,QAAQ,IAC9BoD,UAAU,YAAYnD,KAAK,EAAE;MAC/B+C,gBAAgB,CAACG,GAAG,CAAC,GAAGC,UAAU,CAAClE,OAAO;IAC5C,CAAC,MAAM;MACL4D,gBAAgB,CAACK,GAAG,CAAC,GAAGC,UAAU;IACpC;EACF,CAAC,CAAC;EAEF,KAAK,IAAID,GAAG,IAAAE,kBAAA,CAAAC,mBAAA,CAAI9E,MAAM,CAACS,KAAK,CAAC,GAAE;IAC7B,IAAMsE,QAAQ,GAAGtE,KAAK,CAACkE,GAAG,CAAC;IAC3B,IAAMK,OAAO,GAAGxE,IAAI,GAAMA,IAAI,SAAImE,GAAG,GAAKA,GAAG;IAC7C,IAAI5E,MAAM,CAACkF,IAAI,CAACX,gBAAgB,EAAEK,GAAG,CAAC,EAAE;MACtC,IAAMvE,QAAM,GAAGe,WAAW,CAAC4D,QAAQ,EAAET,gBAAgB,CAACK,GAAG,CAAC,EAAEd,aAAa,EAAEC,MAAM,EAAEkB,OAAO,CAAC;MAC3F,IAAI5E,QAAM,EAAE;QACVA,QAAM,CAACI,IAAI,GAAG0D,YAAY,CAACL,aAAa,GAAGmB,OAAO,GAAGL,GAAG,EAAEvE,QAAM,CAACI,IAAI,CAAC;QACtE,IAAI,CAACqD,aAAa,EAAE,OAAOzD,QAAM;QACjC,IAAIb,OAAA,CAAOwF,QAAQ,MAAK,QAAQ,IAAI3E,QAAM,CAACG,OAAO,EAAEuD,MAAM,CAACK,IAAI,CAAC/D,QAAM,CAAC;MACzE;MAEA,OAAOkE,gBAAgB,CAACK,GAAG,CAAC;IAC9B,CAAC,MAAM,IAAI5E,MAAM,CAACkF,IAAI,CAACT,gBAAgB,EAAEG,GAAG,CAAC,EAAE;MAC7C,IAAMvE,QAAM,GAAGe,WAAW,CAAC4D,QAAQ,EAAEP,gBAAgB,CAACG,GAAG,CAAC,EAAEd,aAAa,EAAEC,MAAM,EAAEkB,OAAO,CAAC;MAC3F,IAAI5E,QAAM,EAAE;QACVA,QAAM,CAACI,IAAI,GAAG0D,YAAY,CAACL,aAAa,GAAGmB,OAAO,GAAGL,GAAG,EAAEvE,QAAM,CAACI,IAAI,CAAC;QACtE,IAAI,CAACqD,aAAa,EAAE,OAAOzD,QAAM;QACjC,IAAIb,OAAA,CAAOwF,QAAQ,MAAK,QAAQ,IAAI3E,QAAM,CAACG,OAAO,EAAEuD,MAAM,CAACK,IAAI,CAAC/D,QAAM,CAAC;MACzE;IAEF,CAAC,MAAM;MACL,IAAI,CAACgE,kBAAkB,EAAE;QACvB,IAAMhE,QAAM,GAAG;UACbG,OAAO,EAAE,aAAa;UACtBC,IAAI,EAAEmE;QACR,CAAC;QACD,IAAI,CAACd,aAAa,EAAE,OAAOzD,QAAM;QACjC0D,MAAM,CAACK,IAAI,CAAC/D,QAAM,CAAC;MACrB;MAEA,IAAIiE,iBAAiB,EAAE;QACrB,IAAMjE,QAAM,GAAGe,WAAW,CAAC4D,QAAQ,EAAEV,iBAAiB,CAAC,CAAC,CAAC,EAAER,aAAa,EAAEC,MAAM,EAAEkB,OAAO,CAAC;QAC1F,IAAI5E,QAAM,EAAE;UACVA,QAAM,CAACI,IAAI,GAAG0D,YAAY,CAACL,aAAa,GAAGmB,OAAO,GAAGL,GAAG,EAAEvE,QAAM,CAACI,IAAI,CAAC;UACtE,IAAI,CAACqD,aAAa,EAAE,OAAOzD,QAAM;UACjC,IAAIb,OAAA,CAAOwF,QAAQ,MAAK,QAAQ,IAAI3E,QAAM,CAACG,OAAO,EAAEuD,MAAM,CAACK,IAAI,CAAC/D,QAAM,CAAC;QACzE;MACF;IACF;EACF;EAEA,IAAMqE,IAAI,GAAGzE,MAAM,CAACyE,IAAI,CAACH,gBAAgB,CAAC;EAC1C,IAAIG,IAAI,CAAC5D,MAAM,EAAE;IACf,IAAMqE,kBAAkB,GAAG,SAAAA,CAAAP,GAAG;MAAA,OAAK;QACjCpE,OAAO,oBAAkBoE,GAAG,MAAG;QAC/BnE,IAAI,EAAEqD,aAAa,GAAGrD,IAAI,GAAG;MAC/B,CAAC;IAAA,CAAC;IAEF,IAAI,CAACqD,aAAa,EAAE;MAClB,OAAOqB,kBAAkB,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC;IAEA,SAAAU,SAAA,GAAA7F,+BAAA,CAAkBmF,IAAI,GAAAW,KAAA,IAAAA,KAAA,GAAAD,SAAA,IAAAE,IAAA,GAAE;MAAA,IAAbV,KAAG,GAAAS,KAAA,CAAA3E,KAAA;MACZqD,MAAM,CAACK,IAAI,CAACe,kBAAkB,CAACP,KAAG,CAAC,CAAC;IACtC;EACF;EAEA,IAAI,CAACd,aAAa,EAAE,OAAO,KAAK;EAChC,OAAOC,MAAM,CAACjD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAGiD,MAAM;AAC7C,CAAC;AAAC,IAEIlB,eAAe;EACnB,SAAAA,gBAAahB,IAAI,EAAEe,WAAW,EAAE;IAE9B;IACA;IACA,IAAI,CAACf,IAAI,GAAA3C,kBAAA,CAAO2C,IAAI,CAAC;;IAErB;IACA;IACA;IACA,IAAI,CAACA,IAAI,CAAC0D,OAAO,CAAC,CAAC;IACnB,IAAI,CAAC3C,WAAW,GAAGA,WAAW;EAChC;EAAC,IAAA4C,MAAA,GAAA3C,eAAA,CAAA3C,SAAA;EAAAsF,MAAA,CAEDrE,QAAQ;IAAR,SAAAA,QAAQA,CAACT,KAAK,EAAE;MACd,IAAI,IAAI,CAAC+E,iBAAiB,CAAC/E,KAAK,CAAC,EAAE;QACjC;MACF;;MAEA;MACA;MACA;MACA,IAAIW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,IAAIuD,WAAW,CAACvD,KAAK,CAAC,EAAE;QAC9CW,KAAK,CAACnB,SAAS,CAACyE,OAAO,CAACO,IAAI,CAACxE,KAAK,EAAE,IAAI,CAAC+E,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;IAAC,OAXDvE,QAAQ;EAAA;EAAAqE,MAAA,CAaRC,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAAC/E,KAAK,EAAE;MACvB,KAAK,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnC,IAAI,CAACf,MAAM,EAAE,EAAEkD,CAAC,EAAE;QAEzC;QACA;QACA;QACA;QACA,IAAItD,KAAK,KAAK,IAAI,CAACmB,IAAI,CAACmC,CAAC,CAAC,IACrBL,MAAM,CAACgC,KAAK,CAACjF,KAAK,CAAC,IAAIiD,MAAM,CAACgC,KAAK,CAAC,IAAI,CAAC9D,IAAI,CAACmC,CAAC,CAAC,CAAE,EAAE;UACvD,IAAI,CAACnC,IAAI,CAAC+D,MAAM,CAAC5B,CAAC,EAAE,CAAC,CAAC;UACtB,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd;IAAC,OAdDyB,iBAAiB;EAAA;EAAAD,MAAA,CAgBjBxC,sCAAsC;IAAtC,SAAAA,sCAAsCA,CAAA,EAAG;MACvC,IAAI,IAAI,CAACnB,IAAI,CAACf,MAAM,GAAG,CAAC,EACtB,MAAM,IAAIP,KAAK,2CAAyC,IAAI,CAACqC,WAAa,CAAC;IAC/E;IAAC,OAHDI,sCAAsC;EAAA;EAAA,OAAAH,eAAA;AAAA;AAMxC,IAAMgD,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAC9E,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACvE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EACpE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAC3E,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAC3E,YAAY,CAAC;;AAEf;AACA;AACA,IAAM1B,YAAY,GAAG,SAAAA,CAACS,GAAG,EAAEkB,IAAI,EAAK;EAClC,IAAK,OAAOlB,GAAG,KAAM,QAAQ,IAAIA,GAAG,CAACmB,KAAK,CAAC,UAAU,CAAC,EAAE;IACtDnB,GAAG,SAAOA,GAAG,MAAG;EAClB,CAAC,MAAM,IAAI,CAACA,GAAG,CAACmB,KAAK,CAAC,2BAA2B,CAAC,IACvCF,WAAW,CAACG,OAAO,CAACpB,GAAG,CAAC,IAAI,CAAC,EAAE;IACxCA,GAAG,GAAGtB,IAAI,CAACD,SAAS,CAAC,CAACuB,GAAG,CAAC,CAAC;EAC7B;EAEA,IAAIkB,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3B,OAAUlB,GAAG,SAAIkB,IAAI;EACvB;EAEA,OAAOlB,GAAG,GAAGkB,IAAI;AACnB,CAAC;AAED,IAAMG,QAAQ,GAAG,SAAAA,CAAAvF,KAAK;EAAA,OAAIlB,OAAA,CAAOkB,KAAK,MAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AAAA;AAErE,IAAMwF,eAAe,GAAG,SAAAA,CAAAC,IAAI;EAAA,OAC1BF,QAAQ,CAACE,IAAI,CAAC,IACdlG,MAAM,CAACC,SAAS,CAACkG,QAAQ,CAAClB,IAAI,CAACiB,IAAI,CAAC,KAAK,oBAAoB;AAAA;AAE/D,IAAMlC,WAAW,GAAGiC,eAAe,CAAC,YAAW;EAAE,OAAOrF,SAAS;AAAE,CAAC,CAAC,CAAC,CAAC,GACrEqF,eAAe,GACf,UAAAxF,KAAK;EAAA,OAAIuF,QAAQ,CAACvF,KAAK,CAAC,IAAI,OAAOA,KAAK,CAAC2F,MAAM,KAAK,UAAU;AAAA,E;;;;;;;;;;;ACxkBhElH,MAAM,CAACM,MAAM,CAAC;EAACG,aAAa,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,aAAa;EAAA;AAAC,CAAC,CAAC;AAA/D;;AAEA,IAAM0G,UAAU,GAAG,CAAC,CAAC;AAErB,IAAMF,QAAQ,GAAGE,UAAU,CAACF,QAAQ;AAEpC,IAAMpG,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,IAAMoG,UAAU,GAAGvG,MAAM,CAACoG,QAAQ;AAElC,IAAMI,oBAAoB,GAAGD,UAAU,CAACrB,IAAI,CAACjF,MAAM,CAAC;AAEpD,IAAMwG,QAAQ,GAAGxG,MAAM,CAACyG,cAAc;AAE/B,IAAM9G,aAAa,GAAG,SAAAA,CAAA+G,GAAG,EAAI;EAClC,IAAIC,KAAK;EACT,IAAIC,IAAI;;EAER;EACA;EACA,IAAI,CAACF,GAAG,IAAIP,QAAQ,CAAClB,IAAI,CAACyB,GAAG,CAAC,KAAK,iBAAiB,EAAE;IACpD,OAAO,KAAK;EACd;EAEAC,KAAK,GAAGH,QAAQ,CAACE,GAAG,CAAC;;EAErB;EACA,IAAI,CAACC,KAAK,EAAE;IACV,OAAO,IAAI;EACb;;EAEA;EACAC,IAAI,GAAG7G,MAAM,CAACkF,IAAI,CAAC0B,KAAK,EAAE,aAAa,CAAC,IAAIA,KAAK,CAACE,WAAW;EAC7D,OAAO,OAAOD,IAAI,KAAK,UAAU,IAC/BN,UAAU,CAACrB,IAAI,CAAC2B,IAAI,CAAC,KAAKL,oBAAoB;AAClD,CAAC,C", "file": "/packages/check.js", "sourcesContent": ["// XXX docs\nimport { isPlainObject } from './isPlainObject';\n\n// Things we explicitly do NOT support:\n//    - heterogenous arrays\n\nconst currentArgumentChecker = new Meteor.EnvironmentVariable;\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nconst format = result => {\n  const err = new Match.Error(result.message);\n  if (result.path) {\n    err.message += ` in field ${result.path}`;\n    err.path = result.path;\n  }\n\n  return err;\n}\n\n/**\n * @summary Check that a value matches a [pattern](#matchpatterns).\n * If the value does not match the pattern, throw a `Match.Error`.\n * By default, it will throw immediately at the first error encountered. Pass in { throwAllErrors: true } to throw all errors.\n *\n * Particularly useful to assert that arguments to a function have the right\n * types and structure.\n * @locus Anywhere\n * @param {Any} value The value to check\n * @param {MatchPattern} pattern The pattern to match `value` against\n * @param {Object} [options={}] Additional options for check\n * @param {Boolean} [options.throwAllErrors=false] If true, throw all errors\n */\nexport function check(value, pattern, options = { throwAllErrors: false }) {\n  // Record that check got called, if somebody cared.\n  //\n  // We use getOrNullIfOutsideFiber so that it's OK to call check()\n  // from non-Fiber server contexts; the downside is that if you forget to\n  // bindEnvironment on some random callback in your method/publisher,\n  // it might not find the argumentChecker and you'll get an error about\n  // not checking an argument that it looks like you're checking (instead\n  // of just getting a \"Node code must run in a Fiber\" error).\n  const argChecker = currentArgumentChecker.getOrNullIfOutsideFiber();\n  if (argChecker) {\n    argChecker.checking(value);\n  }\n\n  const result = testSubtree(value, pattern, options.throwAllErrors);\n\n  if (result) {\n    if (options.throwAllErrors) {\n      throw Array.isArray(result) ? result.map(r => format(r)) : [format(result)]\n    } else {\n      throw format(result)\n    }\n  }\n};\n\n/**\n * @namespace Match\n * @summary The namespace for all Match types and methods.\n */\nexport const Match = {\n  Optional: function(pattern) {\n    return new Optional(pattern);\n  },\n\n  Maybe: function(pattern) {\n    return new Maybe(pattern);\n  },\n\n  OneOf: function(...args) {\n    return new OneOf(args);\n  },\n\n  Any: ['__any__'],\n  Where: function(condition) {\n    return new Where(condition);\n  },\n\n  ObjectIncluding: function(pattern) {\n    return new ObjectIncluding(pattern)\n  },\n\n  ObjectWithValues: function(pattern) {\n    return new ObjectWithValues(pattern);\n  },\n\n  // Matches only signed 32-bit integers\n  Integer: ['__integer__'],\n\n  // XXX matchers should know how to describe themselves for errors\n  Error: Meteor.makeErrorType('Match.Error', function (msg) {\n    this.message = `Match error: ${msg}`;\n\n    // The path of the value that failed to match. Initially empty, this gets\n    // populated by catching and rethrowing the exception as it goes back up the\n    // stack.\n    // E.g.: \"vals[3].entity.created\"\n    this.path = '';\n\n    // If this gets sent over DDP, don't give full internal details but at least\n    // provide something better than 500 Internal server error.\n    this.sanitizedError = new Meteor.Error(400, 'Match failed');\n  }),\n\n  // Tests to see if value matches pattern. Unlike check, it merely returns true\n  // or false (unless an error other than Match.Error was thrown). It does not\n  // interact with _failIfArgumentsAreNotAllChecked.\n  // XXX maybe also implement a Match.match which returns more information about\n  //     failures but without using exception handling or doing what check()\n  //     does with _failIfArgumentsAreNotAllChecked and Meteor.Error conversion\n\n  /**\n   * @summary Returns true if the value matches the pattern.\n   * @locus Anywhere\n   * @param {Any} value The value to check\n   * @param {MatchPattern} pattern The pattern to match `value` against\n   */\n  test(value, pattern) {\n    return !testSubtree(value, pattern);\n  },\n\n  // Runs `f.apply(context, args)`. If check() is not called on every element of\n  // `args` (either directly or in the first level of an array), throws an error\n  // (using `description` in the message).\n  _failIfArgumentsAreNotAllChecked(f, context, args, description) {\n    const argChecker = new ArgumentChecker(args, description);\n    const result = currentArgumentChecker.withValue(\n      argChecker,\n      () => f.apply(context, args)\n    );\n\n    // If f didn't itself throw, make sure it checked all of its arguments.\n    argChecker.throwUnlessAllArgumentsHaveBeenChecked();\n    return result;\n  }\n};\n\nclass Optional {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass Maybe {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass OneOf {\n  constructor(choices) {\n    if (!choices || choices.length === 0) {\n      throw new Error('Must provide at least one choice to Match.OneOf');\n    }\n\n    this.choices = choices;\n  }\n}\n\nclass Where {\n  constructor(condition) {\n    this.condition = condition;\n  }\n}\n\nclass ObjectIncluding {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass ObjectWithValues {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nconst stringForErrorMessage = (value, options = {}) => {\n  if ( value === null ) {\n    return 'null';\n  }\n\n  if ( options.onlyShowType ) {\n    return typeof value;\n  }\n\n  // Your average non-object things.  Saves from doing the try/catch below for.\n  if ( typeof value !== 'object' ) {\n    return EJSON.stringify(value)\n  }\n\n  try {\n\n    // Find objects with circular references since EJSON doesn't support them yet (Issue #4778 + Unaccepted PR)\n    // If the native stringify is going to choke, EJSON.stringify is going to choke too.\n    JSON.stringify(value);\n  } catch (stringifyError) {\n    if ( stringifyError.name === 'TypeError' ) {\n      return typeof value;\n    }\n  }\n\n  return EJSON.stringify(value);\n};\n\nconst typeofChecks = [\n  [String, 'string'],\n  [Number, 'number'],\n  [Boolean, 'boolean'],\n\n  // While we don't allow undefined/function in EJSON, this is good for optional\n  // arguments with OneOf.\n  [Function, 'function'],\n  [undefined, 'undefined'],\n];\n\n// Return `false` if it matches. Otherwise, returns an object with a `message` and a `path` field or an array of objects each with a `message` and a `path` field when collecting errors.\nconst testSubtree = (value, pattern, collectErrors = false, errors = [], path = '') => {\n  // Match anything!\n  if (pattern === Match.Any) {\n    return false;\n  }\n\n  // Basic atomic types.\n  // Do not match boxed objects (e.g. String, Boolean)\n  for (let i = 0; i < typeofChecks.length; ++i) {\n    if (pattern === typeofChecks[i][0]) {\n      if (typeof value === typeofChecks[i][1]) {\n        return false;\n      }\n\n      return {\n        message: `Expected ${typeofChecks[i][1]}, got ${stringForErrorMessage(value, { onlyShowType: true })}`,\n        path: '',\n      };\n    }\n  }\n\n  if (pattern === null) {\n    if (value === null) {\n      return false;\n    }\n\n    return {\n      message: `Expected null, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // Strings, numbers, and booleans match literally. Goes well with Match.OneOf.\n  if (typeof pattern === 'string' || typeof pattern === 'number' || typeof pattern === 'boolean') {\n    if (value === pattern) {\n      return false;\n    }\n\n    return {\n      message: `Expected ${pattern}, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // Match.Integer is special type encoded with array\n  if (pattern === Match.Integer) {\n\n    // There is no consistent and reliable way to check if variable is a 64-bit\n    // integer. One of the popular solutions is to get reminder of division by 1\n    // but this method fails on really large floats with big precision.\n    // E.g.: 1.348192308491824e+23 % 1 === 0 in V8\n    // Bitwise operators work consistantly but always cast variable to 32-bit\n    // signed integer according to JavaScript specs.\n    if (typeof value === 'number' && (value | 0) === value) {\n      return false;\n    }\n\n    return {\n      message: `Expected Integer, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // 'Object' is shorthand for Match.ObjectIncluding({});\n  if (pattern === Object) {\n    pattern = Match.ObjectIncluding({});\n  }\n\n  // Array (checked AFTER Any, which is implemented as an Array).\n  if (pattern instanceof Array) {\n    if (pattern.length !== 1) {\n      return {\n        message: `Bad pattern: arrays must have one type element ${stringForErrorMessage(pattern)}`,\n        path: '',\n      };\n    }\n\n    if (!Array.isArray(value) && !isArguments(value)) {\n      return {\n        message: `Expected array, got ${stringForErrorMessage(value)}`,\n        path: '',\n      };\n    }\n\n\n    for (let i = 0, length = value.length; i < length; i++) {\n      const arrPath = `${path}[${i}]`\n      const result = testSubtree(value[i], pattern[0], collectErrors, errors, arrPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? arrPath : i, result.path)\n        if (!collectErrors) return result;\n        if (typeof value[i] !== 'object' || result.message) errors.push(result)\n      }\n    }\n\n    if (!collectErrors) return false;\n    return errors.length === 0 ? false : errors;\n  }\n\n  // Arbitrary validation checks. The condition can return false or throw a\n  // Match.Error (ie, it can internally use check()) to fail.\n  if (pattern instanceof Where) {\n    let result;\n    try {\n      result = pattern.condition(value);\n    } catch (err) {\n      if (!(err instanceof Match.Error)) {\n        throw err;\n      }\n\n      return {\n        message: err.message,\n        path: err.path\n      };\n    }\n\n    if (result) {\n      return false;\n    }\n\n    // XXX this error is terrible\n\n    return {\n      message: 'Failed Match.Where validation',\n      path: '',\n    };\n  }\n\n  if (pattern instanceof Maybe) {\n    pattern = Match.OneOf(undefined, null, pattern.pattern);\n  } else if (pattern instanceof Optional) {\n    pattern = Match.OneOf(undefined, pattern.pattern);\n  }\n\n  if (pattern instanceof OneOf) {\n    for (let i = 0; i < pattern.choices.length; ++i) {\n      const result = testSubtree(value, pattern.choices[i]);\n      if (!result) {\n\n        // No error? Yay, return.\n        return false;\n      }\n\n      // Match errors just mean try another choice.\n    }\n\n    // XXX this error is terrible\n    return {\n      message: 'Failed Match.OneOf, Match.Maybe or Match.Optional validation',\n      path: '',\n    };\n  }\n\n  // A function that isn't something we special-case is assumed to be a\n  // constructor.\n  if (pattern instanceof Function) {\n    if (value instanceof pattern) {\n      return false;\n    }\n\n    return {\n      message: `Expected ${pattern.name || 'particular constructor'}`,\n      path: '',\n    };\n  }\n\n  let unknownKeysAllowed = false;\n  let unknownKeyPattern;\n  if (pattern instanceof ObjectIncluding) {\n    unknownKeysAllowed = true;\n    pattern = pattern.pattern;\n  }\n\n  if (pattern instanceof ObjectWithValues) {\n    unknownKeysAllowed = true;\n    unknownKeyPattern = [pattern.pattern];\n    pattern = {};  // no required keys\n  }\n\n  if (typeof pattern !== 'object') {\n    return {\n      message: 'Bad pattern: unknown pattern type',\n      path: '',\n    };\n  }\n\n  // An object, with required and optional keys. Note that this does NOT do\n  // structural matches against objects of special types that happen to match\n  // the pattern: this really needs to be a plain old {Object}!\n  if (typeof value !== 'object') {\n    return {\n      message: `Expected object, got ${typeof value}`,\n      path: '',\n    };\n  }\n\n  if (value === null) {\n    return {\n      message: `Expected object, got null`,\n      path: '',\n    };\n  }\n\n  if (! isPlainObject(value)) {\n    return {\n      message: `Expected plain object`,\n      path: '',\n    };\n  }\n\n  const requiredPatterns = Object.create(null);\n  const optionalPatterns = Object.create(null);\n\n  Object.keys(pattern).forEach(key => {\n    const subPattern = pattern[key];\n    if (subPattern instanceof Optional ||\n        subPattern instanceof Maybe) {\n      optionalPatterns[key] = subPattern.pattern;\n    } else {\n      requiredPatterns[key] = subPattern;\n    }\n  });\n\n  for (let key in Object(value)) {\n    const subValue = value[key];\n    const objPath = path ? `${path}.${key}` : key;\n    if (hasOwn.call(requiredPatterns, key)) {\n      const result = testSubtree(subValue, requiredPatterns[key], collectErrors, errors, objPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? objPath : key, result.path)\n        if (!collectErrors) return result;\n        if (typeof subValue !== 'object' || result.message) errors.push(result);\n      }\n\n      delete requiredPatterns[key];\n    } else if (hasOwn.call(optionalPatterns, key)) {\n      const result = testSubtree(subValue, optionalPatterns[key], collectErrors, errors, objPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? objPath : key, result.path)\n        if (!collectErrors) return result;\n        if (typeof subValue !== 'object' || result.message) errors.push(result);\n      }\n\n    } else {\n      if (!unknownKeysAllowed) {\n        const result = {\n          message: 'Unknown key',\n          path: key,\n        };\n        if (!collectErrors) return result;\n        errors.push(result);\n      }\n\n      if (unknownKeyPattern) {\n        const result = testSubtree(subValue, unknownKeyPattern[0], collectErrors, errors, objPath);\n        if (result) {\n          result.path = _prependPath(collectErrors ? objPath : key, result.path)\n          if (!collectErrors) return result;\n          if (typeof subValue !== 'object' || result.message) errors.push(result);\n        }\n      }\n    }\n  }\n\n  const keys = Object.keys(requiredPatterns);\n  if (keys.length) {\n    const createMissingError = key => ({\n      message: `Missing key '${key}'`,\n      path: collectErrors ? path : '',\n    });\n\n    if (!collectErrors) {\n      return createMissingError(keys[0]);\n    }\n\n    for (const key of keys) {\n      errors.push(createMissingError(key));\n    }\n  }\n\n  if (!collectErrors) return false;\n  return errors.length === 0 ? false : errors;\n};\n\nclass ArgumentChecker {\n  constructor (args, description) {\n\n    // Make a SHALLOW copy of the arguments. (We'll be doing identity checks\n    // against its contents.)\n    this.args = [...args];\n\n    // Since the common case will be to check arguments in order, and we splice\n    // out arguments when we check them, make it so we splice out from the end\n    // rather than the beginning.\n    this.args.reverse();\n    this.description = description;\n  }\n\n  checking(value) {\n    if (this._checkingOneValue(value)) {\n      return;\n    }\n\n    // Allow check(arguments, [String]) or check(arguments.slice(1), [String])\n    // or check([foo, bar], [String]) to count... but only if value wasn't\n    // itself an argument.\n    if (Array.isArray(value) || isArguments(value)) {\n      Array.prototype.forEach.call(value, this._checkingOneValue.bind(this));\n    }\n  }\n\n  _checkingOneValue(value) {\n    for (let i = 0; i < this.args.length; ++i) {\n\n      // Is this value one of the arguments? (This can have a false positive if\n      // the argument is an interned primitive, but it's still a good enough\n      // check.)\n      // (NaN is not === to itself, so we have to check specially.)\n      if (value === this.args[i] ||\n          (Number.isNaN(value) && Number.isNaN(this.args[i]))) {\n        this.args.splice(i, 1);\n        return true;\n      }\n    }\n    return false;\n  }\n\n  throwUnlessAllArgumentsHaveBeenChecked() {\n    if (this.args.length > 0)\n      throw new Error(`Did not check() all arguments during ${this.description}`);\n  }\n}\n\nconst _jsKeywords = ['do', 'if', 'in', 'for', 'let', 'new', 'try', 'var', 'case',\n  'else', 'enum', 'eval', 'false', 'null', 'this', 'true', 'void', 'with',\n  'break', 'catch', 'class', 'const', 'super', 'throw', 'while', 'yield',\n  'delete', 'export', 'import', 'public', 'return', 'static', 'switch',\n  'typeof', 'default', 'extends', 'finally', 'package', 'private', 'continue',\n  'debugger', 'function', 'arguments', 'interface', 'protected', 'implements',\n  'instanceof'];\n\n// Assumes the base of path is already escaped properly\n// returns key + base\nconst _prependPath = (key, base) => {\n  if ((typeof key) === 'number' || key.match(/^[0-9]+$/)) {\n    key = `[${key}]`;\n  } else if (!key.match(/^[a-z_$][0-9a-z_$.[\\]]*$/i) ||\n             _jsKeywords.indexOf(key) >= 0) {\n    key = JSON.stringify([key]);\n  }\n\n  if (base && base[0] !== '[') {\n    return `${key}.${base}`;\n  }\n\n  return key + base;\n}\n\nconst isObject = value => typeof value === 'object' && value !== null;\n\nconst baseIsArguments = item =>\n  isObject(item) &&\n  Object.prototype.toString.call(item) === '[object Arguments]';\n\nconst isArguments = baseIsArguments(function() { return arguments; }()) ?\n  baseIsArguments :\n  value => isObject(value) && typeof value.callee === 'function';\n", "// Copy of jQuery.isPlainObject for the server side from jQuery v3.1.1.\n\nconst class2type = {};\n\nconst toString = class2type.toString;\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nconst fnToString = hasOwn.toString;\n\nconst ObjectFunctionString = fnToString.call(Object);\n\nconst getProto = Object.getPrototypeOf;\n\nexport const isPlainObject = obj => {\n  let proto;\n  let Ctor;\n\n  // Detect obvious negatives\n  // Use toString instead of jQuery.type to catch host objects\n  if (!obj || toString.call(obj) !== '[object Object]') {\n    return false;\n  }\n\n  proto = getProto(obj);\n\n  // Objects with no prototype (e.g., `Object.create( null )`) are plain\n  if (!proto) {\n    return true;\n  }\n\n  // Objects with prototype are plain iff they were constructed by a global Object function\n  Ctor = hasOwn.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor === 'function' && \n    fnToString.call(Ctor) === ObjectFunctionString;\n};\n"]}