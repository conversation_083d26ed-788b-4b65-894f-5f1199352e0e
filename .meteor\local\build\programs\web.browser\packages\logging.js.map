{"version": 3, "sources": ["meteor://💻app/packages/logging/logging.js", "meteor://💻app/packages/logging/logging_browser.js"], "names": ["_objectSpread", "module", "link", "default", "v", "export", "Log", "Meteor", "hasOwn", "Object", "prototype", "hasOwnProperty", "info", "arguments", "intercept", "interceptedLines", "suppress", "_intercept", "count", "_suppress", "_intercepted", "lines", "outputFormat", "showTime", "LEVEL_COLORS", "debug", "warn", "error", "META_COLOR", "isWin32", "process", "platform", "platformColor", "color", "endsWith", "concat", "RESTRICTED_KEYS", "FORMATTED_KEYS", "logInBrowser", "obj", "str", "format", "level", "console", "log", "apply", "Function", "bind", "call", "_getCallerDetails", "getStack", "err", "Error", "stack", "line", "split", "slice", "match", "file", "details", "exec", "for<PERSON>ach", "arg", "intercepted", "RegExp", "Date", "message", "String", "toString", "key", "omitCallerDetails", "time", "isProduction", "push", "EJSON", "stringify", "isServer", "parse", "startsWith", "e", "options", "length", "undefined", "timeInexact", "lineNumber", "app", "appName", "originApp", "program", "satellite", "stderr", "keys", "pad2", "n", "padStart", "pad3", "dateStamp", "getFullYear", "getMonth", "getDate", "timeStamp", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "utcOffsetStr", "getTimezoneOffset", "appInfo", "sourceInfoParts", "sourceInfo", "join", "stderrIndicator", "timeString", "metaPrefix", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON>", "prettify", "metaColor", "objFromText", "override"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,aAAa;AAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGH,MAAM,CAACI,MAAM,CAAC;EAACC,GAAG,EAACA,CAAA,KAAIA;AAAG,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACN,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACK,MAAMA,CAACH,CAAC,EAAC;IAACG,MAAM,GAACH,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAE5F,MAAMI,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,SAASL,GAAGA,CAAA,EAAU;EACpBA,GAAG,CAACM,IAAI,CAAC,GAAAC,SAAO,CAAC;AACnB;;AAEA;AACA,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,QAAQ,GAAG,CAAC;;AAEhB;AACA;AACA;AACAV,GAAG,CAACW,UAAU,GAAIC,KAAK,IAAK;EAC1BJ,SAAS,IAAII,KAAK;AACpB,CAAC;;AAED;AACA;AACA;AACAZ,GAAG,CAACa,SAAS,GAAID,KAAK,IAAK;EACzBF,QAAQ,IAAIE,KAAK;AACnB,CAAC;;AAED;AACAZ,GAAG,CAACc,YAAY,GAAG,MAAM;EACvB,MAAMC,KAAK,GAAGN,gBAAgB;EAC9BA,gBAAgB,GAAG,EAAE;EACrBD,SAAS,GAAG,CAAC;EACb,OAAOO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAf,GAAG,CAACgB,YAAY,GAAG,MAAM;;AAEzB;AACA;AACA;AACAhB,GAAG,CAACiB,QAAQ,GAAG,IAAI;AAEnB,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,OAAO;EACd;EACAC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,UAAU,GAAG,MAAM;;AAEzB;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,OAAOC,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACC,QAAQ,KAAK,OAAO;AAC3E,MAAMC,aAAa,GAAIC,KAAK,IAAK;EAC/B,IAAIJ,OAAO,IAAI,OAAOI,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;IACrE,UAAAC,MAAA,CAAUF,KAAK;EACjB;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA,MAAMG,eAAe,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAC/C,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC;AAEtE,MAAMC,cAAc,GAAG,CAAC,GAAGD,eAAe,EAAE,KAAK,EAAE,SAAS,CAAC;AAE7D,MAAME,YAAY,GAAGC,GAAG,IAAI;EAC1B,MAAMC,GAAG,GAAGlC,GAAG,CAACmC,MAAM,CAACF,GAAG,CAAC;;EAE3B;EACA,MAAMG,KAAK,GAAGH,GAAG,CAACG,KAAK;EAEvB,IAAK,OAAOC,OAAO,KAAK,WAAW,IAAKA,OAAO,CAACD,KAAK,CAAC,EAAE;IACtDC,OAAO,CAACD,KAAK,CAAC,CAACF,GAAG,CAAC;EACrB,CAAC,MAAM;IACL;IACA;IACA;IACA,IAAI,OAAOG,OAAO,CAACC,GAAG,CAACC,KAAK,KAAK,UAAU,EAAE;MAC3C;MACAF,OAAO,CAACC,GAAG,CAACC,KAAK,CAACF,OAAO,EAAE,CAACH,GAAG,CAAC,CAAC;IAEnC,CAAC,MAAM,IAAI,OAAOM,QAAQ,CAACpC,SAAS,CAACqC,IAAI,KAAK,UAAU,EAAE;MACxD;MACA,MAAMH,GAAG,GAAGE,QAAQ,CAACpC,SAAS,CAACqC,IAAI,CAACC,IAAI,CAACL,OAAO,CAACC,GAAG,EAAED,OAAO,CAAC;MAC9DC,GAAG,CAACC,KAAK,CAACF,OAAO,EAAE,CAACH,GAAG,CAAC,CAAC;IAC3B;EACF;AACF,CAAC;;AAED;AACAlC,GAAG,CAAC2C,iBAAiB,GAAG,MAAM;EAC5B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB;IACA;IACA;IACA,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAD,CAAC;IACrB,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK;IACvB,OAAOA,KAAK;EACd,CAAC;EAED,MAAMA,KAAK,GAAGH,QAAQ,CAAC,CAAC;EAExB,IAAI,CAACG,KAAK,EAAE,OAAO,CAAC,CAAC;;EAErB;EACA;EACA,IAAIC,IAAI;EACR,MAAMjC,KAAK,GAAGgC,KAAK,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;EACxC,KAAKF,IAAI,IAAIjC,KAAK,EAAE;IAClB,IAAIiC,IAAI,CAACG,KAAK,CAAC,8BAA8B,CAAC,EAAE;MAC9C,OAAO;QAACC,IAAI,EAAE;MAAM,CAAC;IACvB;IAEA,IAAI,CAACJ,IAAI,CAACG,KAAK,CAAC,iDAAiD,CAAC,EAAE;MAClE;IACF;EACF;EAEA,MAAME,OAAO,GAAG,CAAC,CAAC;;EAElB;EACA;EACA;EACA,MAAMF,KAAK,GAAG,yCAAyC,CAACG,IAAI,CAACN,IAAI,CAAC;EAClE,IAAI,CAACG,KAAK,EAAE;IACV,OAAOE,OAAO;EAChB;;EAEA;EACAA,OAAO,CAACL,IAAI,GAAGG,KAAK,CAAC,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAErC;EACA;EACA;EACAI,OAAO,CAACD,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAE7D,OAAOI,OAAO;AAChB,CAAC;AAED,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAACE,OAAO,CAAEnB,KAAK,IAAK;EACrD;EACApC,GAAG,CAACoC,KAAK,CAAC,GAAIoB,GAAG,IAAK;IACrB,IAAI9C,QAAQ,EAAE;MACZA,QAAQ,EAAE;MACV;IACF;IAEA,IAAI+C,WAAW,GAAG,KAAK;IACvB,IAAIjD,SAAS,EAAE;MACbA,SAAS,EAAE;MACXiD,WAAW,GAAG,IAAI;IACpB;IAEA,IAAIxB,GAAG,GAAIuB,GAAG,KAAKrD,MAAM,CAACqD,GAAG,CAAC,IACzB,EAAEA,GAAG,YAAYE,MAAM,CAAC,IACxB,EAAEF,GAAG,YAAYG,IAAI,CAAC,GACvBH,GAAG,GACH;MAAEI,OAAO,EAAE,IAAIC,MAAM,CAACL,GAAG,CAAC,CAACM,QAAQ,CAAC;IAAE,CAAC;IAE3ChC,eAAe,CAACyB,OAAO,CAACQ,GAAG,IAAI;MAC7B,IAAI9B,GAAG,CAAC8B,GAAG,CAAC,EAAE;QACZ,MAAM,IAAIjB,KAAK,eAAAjB,MAAA,CAAekC,GAAG,qBAAkB,CAAC;MACtD;IACF,CAAC,CAAC;IAEF,IAAI7D,MAAM,CAACwC,IAAI,CAACT,GAAG,EAAE,SAAS,CAAC,IAAI,OAAOA,GAAG,CAAC2B,OAAO,KAAK,QAAQ,EAAE;MAClE,MAAM,IAAId,KAAK,CAAC,qDAAqD,CAAC;IACxE;IAEA,IAAI,CAACb,GAAG,CAAC+B,iBAAiB,EAAE;MAC1B/B,GAAG,GAAAvC,aAAA,CAAAA,aAAA,KAAQM,GAAG,CAAC2C,iBAAiB,CAAC,CAAC,GAAKV,GAAG,CAAE;IAC9C;IAEAA,GAAG,CAACgC,IAAI,GAAG,IAAIN,IAAI,CAAC,CAAC;IACrB1B,GAAG,CAACG,KAAK,GAAGA,KAAK;;IAEjB;IACA,IAAIA,KAAK,KAAK,OAAO,IAAInC,MAAM,CAACiE,YAAY,EAAE;MAC5C;IACF;IAEA,IAAIT,WAAW,EAAE;MACfhD,gBAAgB,CAAC0D,IAAI,CAACC,KAAK,CAACC,SAAS,CAACpC,GAAG,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIhC,MAAM,CAACqE,QAAQ,EAAE;MAC1B,IAAItE,GAAG,CAACgB,YAAY,KAAK,cAAc,EAAE;QACvCqB,OAAO,CAACC,GAAG,CAACtC,GAAG,CAACmC,MAAM,CAACF,GAAG,EAAE;UAACN,KAAK,EAAE;QAAI,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAI3B,GAAG,CAACgB,YAAY,KAAK,MAAM,EAAE;QACtCqB,OAAO,CAACC,GAAG,CAAC8B,KAAK,CAACC,SAAS,CAACpC,GAAG,CAAC,CAAC;MACnC,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,mCAAAjB,MAAA,CAAmC7B,GAAG,CAACgB,YAAY,CAAE,CAAC;MACvE;IACF,CAAC,MAAM;MACLgB,YAAY,CAACC,GAAG,CAAC;IACnB;EACF,CAAC;AACD,CAAC,CAAC;;AAGF;AACAjC,GAAG,CAACuE,KAAK,GAAIvB,IAAI,IAAK;EACpB,IAAIf,GAAG,GAAG,IAAI;EACd,IAAIe,IAAI,IAAIA,IAAI,CAACwB,UAAU,CAAC,GAAG,CAAC,EAAE;IAAE;IAClC,IAAI;MAAEvC,GAAG,GAAGmC,KAAK,CAACG,KAAK,CAACvB,IAAI,CAAC;IAAE,CAAC,CAAC,OAAOyB,CAAC,EAAE,CAAC;EAC9C;;EAEA;EACA,IAAIxC,GAAG,IAAIA,GAAG,CAACgC,IAAI,IAAKhC,GAAG,CAACgC,IAAI,YAAYN,IAAK,EAAE;IACjD,OAAO1B,GAAG;EACZ,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACAjC,GAAG,CAACmC,MAAM,GAAG,UAACF,GAAG,EAAmB;EAAA,IAAjByC,OAAO,GAAAnE,SAAA,CAAAoE,MAAA,QAAApE,SAAA,QAAAqE,SAAA,GAAArE,SAAA,MAAG,CAAC,CAAC;EAC7B0B,GAAG,GAAAvC,aAAA,KAAQuC,GAAG,CAAE,CAAC,CAAC;EAClB,IAAI;IACFgC,IAAI;IACJY,WAAW;IACXzC,KAAK,GAAG,MAAM;IACdgB,IAAI;IACJJ,IAAI,EAAE8B,UAAU;IAChBC,GAAG,EAAEC,OAAO,GAAG,EAAE;IACjBC,SAAS;IACTrB,OAAO,GAAG,EAAE;IACZsB,OAAO,GAAG,EAAE;IACZC,SAAS,GAAG,EAAE;IACdC,MAAM,GAAG;EACX,CAAC,GAAGnD,GAAG;EAEP,IAAI,EAAEgC,IAAI,YAAYN,IAAI,CAAC,EAAE;IAC3B,MAAM,IAAIb,KAAK,CAAC,8BAA8B,CAAC;EACjD;EAEAf,cAAc,CAACwB,OAAO,CAAEQ,GAAG,IAAK;IAAE,OAAO9B,GAAG,CAAC8B,GAAG,CAAC;EAAE,CAAC,CAAC;EAErD,IAAI5D,MAAM,CAACkF,IAAI,CAACpD,GAAG,CAAC,CAAC0C,MAAM,GAAG,CAAC,EAAE;IAC/B,IAAIf,OAAO,EAAE;MACXA,OAAO,IAAI,GAAG;IAChB;IACAA,OAAO,IAAIQ,KAAK,CAACC,SAAS,CAACpC,GAAG,CAAC;EACjC;EAEA,MAAMqD,IAAI,GAAGC,CAAC,IAAIA,CAAC,CAACzB,QAAQ,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC/C,MAAMC,IAAI,GAAGF,CAAC,IAAIA,CAAC,CAACzB,QAAQ,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAE/C,MAAME,SAAS,GAAGzB,IAAI,CAAC0B,WAAW,CAAC,CAAC,CAAC7B,QAAQ,CAAC,CAAC,GAC7CwB,IAAI,CAACrB,IAAI,CAAC2B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,GACrCN,IAAI,CAACrB,IAAI,CAAC4B,OAAO,CAAC,CAAC,CAAC;EACtB,MAAMC,SAAS,GAAGR,IAAI,CAACrB,IAAI,CAAC8B,QAAQ,CAAC,CAAC,CAAC,GACjC,GAAG,GACHT,IAAI,CAACrB,IAAI,CAAC+B,UAAU,CAAC,CAAC,CAAC,GACvB,GAAG,GACHV,IAAI,CAACrB,IAAI,CAACgC,UAAU,CAAC,CAAC,CAAC,GACvB,GAAG,GACHR,IAAI,CAACxB,IAAI,CAACiC,eAAe,CAAC,CAAC,CAAC;;EAElC;EACA,MAAMC,YAAY,OAAAtE,MAAA,CAAQ,EAAE,IAAI8B,IAAI,CAAC,CAAC,CAACyC,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAC,MAAI;EAEpE,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIrB,OAAO,EAAE;IACXqB,OAAO,IAAIrB,OAAO;EACpB;EACA,IAAIC,SAAS,IAAIA,SAAS,KAAKD,OAAO,EAAE;IACtCqB,OAAO,YAAAxE,MAAA,CAAYoD,SAAS,CAAE;EAChC;EACA,IAAIoB,OAAO,EAAE;IACXA,OAAO,OAAAxE,MAAA,CAAOwE,OAAO,OAAI;EAC3B;EAEA,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIpB,OAAO,EAAE;IACXoB,eAAe,CAACnC,IAAI,CAACe,OAAO,CAAC;EAC/B;EACA,IAAI9B,IAAI,EAAE;IACRkD,eAAe,CAACnC,IAAI,CAACf,IAAI,CAAC;EAC5B;EACA,IAAI0B,UAAU,EAAE;IACdwB,eAAe,CAACnC,IAAI,CAACW,UAAU,CAAC;EAClC;EAEA,IAAIyB,UAAU,GAAG,CAACD,eAAe,CAAC3B,MAAM,GACtC,EAAE,OAAA9C,MAAA,CAAOyE,eAAe,CAACE,IAAI,CAAC,GAAG,CAAC,OAAI;EAExC,IAAIrB,SAAS,EACXoB,UAAU,QAAA1E,MAAA,CAAQsD,SAAS,MAAG;EAEhC,MAAMsB,eAAe,GAAGrB,MAAM,GAAG,WAAW,GAAG,EAAE;EAEjD,MAAMsB,UAAU,GAAG1G,GAAG,CAACiB,QAAQ,MAAAY,MAAA,CACxB6D,SAAS,OAAA7D,MAAA,CAAIiE,SAAS,EAAAjE,MAAA,CAAGsE,YAAY,EAAAtE,MAAA,CAAGgD,WAAW,GAAG,IAAI,GAAG,GAAG,IACnE,GAAG;EAIP,MAAM8B,UAAU,GAAG,CACjBvE,KAAK,CAACwE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAC7BH,UAAU,EACVL,OAAO,EACPE,UAAU,EACVE,eAAe,CAAC,CAACD,IAAI,CAAC,EAAE,CAAC;EAG3B,OAAOM,SAAS,CAACC,QAAQ,CAACJ,UAAU,EAAEjC,OAAO,CAAC/C,KAAK,IAAID,aAAa,CAACgD,OAAO,CAACsC,SAAS,IAAI1F,UAAU,CAAC,CAAC,GAClGwF,SAAS,CAACC,QAAQ,CAACnD,OAAO,EAAEc,OAAO,CAAC/C,KAAK,IAAID,aAAa,CAACR,YAAY,CAACkB,KAAK,CAAC,CAAC,CAAC;AACtF,CAAC;;AAED;AACA;AACA;AACApC,GAAG,CAACiH,WAAW,GAAG,CAACjE,IAAI,EAAEkE,QAAQ,KAAK;EACpC,OAAAxH,aAAA;IACEkE,OAAO,EAAEZ,IAAI;IACbZ,KAAK,EAAE,MAAM;IACb6B,IAAI,EAAE,IAAIN,IAAI,CAAC,CAAC;IAChBkB,WAAW,EAAE;EAAI,GACdqC,QAAQ;AAEf,CAAC,C;;;;;;;;;;;AC5UDJ,SAAS,GAAG,CAAC,CAAC;AACdA,SAAS,CAACC,QAAQ,GAAG,UAAS/D,IAAI,EAAErB,KAAK,EAAC;EACtC,OAAOqB,IAAI;AACf,CAAC,C", "file": "/packages/logging.js", "sourcesContent": ["import { Meteor } from 'meteor/meteor';\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nfunction Log(...args) {\n  Log.info(...args);\n}\n\n/// FOR TESTING\nlet intercept = 0;\nlet interceptedLines = [];\nlet suppress = 0;\n\n// Intercept the next 'count' calls to a Log function. The actual\n// lines printed to the console can be cleared and read by calling\n// Log._intercepted().\nLog._intercept = (count) => {\n  intercept += count;\n};\n\n// Suppress the next 'count' calls to a Log function. Use this to stop\n// tests from spamming the console, especially with red errors that\n// might look like a failing test.\nLog._suppress = (count) => {\n  suppress += count;\n};\n\n// Returns intercepted lines and resets the intercept counter.\nLog._intercepted = () => {\n  const lines = interceptedLines;\n  interceptedLines = [];\n  intercept = 0;\n  return lines;\n};\n\n// Either 'json' or 'colored-text'.\n//\n// When this is set to 'json', print JSON documents that are parsed by another\n// process ('satellite' or 'meteor run'). This other process should call\n// 'Log.format' for nice output.\n//\n// When this is set to 'colored-text', call 'Log.format' before printing.\n// This should be used for logging from within satellite, since there is no\n// other process that will be reading its standard output.\nLog.outputFormat = 'json';\n\n// Defaults to true for local development and for backwards compatibility.\n// for cloud environments is interesting to leave it false as most of them have the timestamp in the console.\n// Only works in server with colored-text\nLog.showTime = true;\n\nconst LEVEL_COLORS = {\n  debug: 'green',\n  // leave info as the default color\n  warn: 'magenta',\n  error: 'red'\n};\n\nconst META_COLOR = 'blue';\n\n// Default colors cause readability problems on Windows Powershell,\n// switch to bright variants. While still capable of millions of\n// operations per second, the benchmark showed a 25%+ increase in\n// ops per second (on Node 8) by caching \"process.platform\".\nconst isWin32 = typeof process === 'object' && process.platform === 'win32';\nconst platformColor = (color) => {\n  if (isWin32 && typeof color === 'string' && !color.endsWith('Bright')) {\n    return `${color}Bright`;\n  }\n  return color;\n};\n\n// XXX package\nconst RESTRICTED_KEYS = ['time', 'timeInexact', 'level', 'file', 'line',\n                        'program', 'originApp', 'satellite', 'stderr'];\n\nconst FORMATTED_KEYS = [...RESTRICTED_KEYS, 'app', 'message'];\n\nconst logInBrowser = obj => {\n  const str = Log.format(obj);\n\n  // XXX Some levels should be probably be sent to the server\n  const level = obj.level;\n\n  if ((typeof console !== 'undefined') && console[level]) {\n    console[level](str);\n  } else {\n    // IE doesn't have console.log.apply, it's not a real Object.\n    // http://stackoverflow.com/questions/5538972/console-log-apply-not-working-in-ie9\n    // http://patik.com/blog/complete-cross-browser-console-log/\n    if (typeof console.log.apply === \"function\") {\n      // Most browsers\n      console.log.apply(console, [str]);\n\n    } else if (typeof Function.prototype.bind === \"function\") {\n      // IE9\n      const log = Function.prototype.bind.call(console.log, console);\n      log.apply(console, [str]);\n    }\n  }\n};\n\n// @returns {Object: { line: Number, file: String }}\nLog._getCallerDetails = () => {\n  const getStack = () => {\n    // We do NOT use Error.prepareStackTrace here (a V8 extension that gets us a\n    // pre-parsed stack) since it's impossible to compose it with the use of\n    // Error.prepareStackTrace used on the server for source maps.\n    const err = new Error;\n    const stack = err.stack;\n    return stack;\n  };\n\n  const stack = getStack();\n\n  if (!stack) return {};\n\n  // looking for the first line outside the logging package (or an\n  // eval if we find that first)\n  let line;\n  const lines = stack.split('\\n').slice(1);\n  for (line of lines) {\n    if (line.match(/^\\s*(at eval \\(eval)|(eval:)/)) {\n      return {file: \"eval\"};\n    }\n\n    if (!line.match(/packages\\/(?:local-test[:_])?logging(?:\\/|\\.js)/)) {\n      break;\n    }\n  }\n\n  const details = {};\n\n  // The format for FF is 'functionName@filePath:lineNumber'\n  // The format for V8 is 'functionName (packages/logging/logging.js:81)' or\n  //                      'packages/logging/logging.js:81'\n  const match = /(?:[@(]| at )([^(]+?):([0-9:]+)(?:\\)|$)/.exec(line);\n  if (!match) {\n    return details;\n  }\n\n  // in case the matched block here is line:column\n  details.line = match[2].split(':')[0];\n\n  // Possible format: https://foo.bar.com/scripts/file.js?random=foobar\n  // XXX: if you can write the following in better way, please do it\n  // XXX: what about evals?\n  details.file = match[1].split('/').slice(-1)[0].split('?')[0];\n\n  return details;\n};\n\n['debug', 'info', 'warn', 'error'].forEach((level) => {\n // @param arg {String|Object}\n Log[level] = (arg) => {\n  if (suppress) {\n    suppress--;\n    return;\n  }\n\n  let intercepted = false;\n  if (intercept) {\n    intercept--;\n    intercepted = true;\n  }\n\n  let obj = (arg === Object(arg)\n    && !(arg instanceof RegExp)\n    && !(arg instanceof Date))\n    ? arg\n    : { message: new String(arg).toString() };\n\n  RESTRICTED_KEYS.forEach(key => {\n    if (obj[key]) {\n      throw new Error(`Can't set '${key}' in log message`);\n    }\n  });\n\n  if (hasOwn.call(obj, 'message') && typeof obj.message !== 'string') {\n    throw new Error(\"The 'message' field in log objects must be a string\");\n  }\n\n  if (!obj.omitCallerDetails) {\n    obj = { ...Log._getCallerDetails(), ...obj };\n  }\n\n  obj.time = new Date();\n  obj.level = level;\n\n  // If we are in production don't write out debug logs.\n  if (level === 'debug' && Meteor.isProduction) {\n    return;\n  }\n\n  if (intercepted) {\n    interceptedLines.push(EJSON.stringify(obj));\n  } else if (Meteor.isServer) {\n    if (Log.outputFormat === 'colored-text') {\n      console.log(Log.format(obj, {color: true}));\n    } else if (Log.outputFormat === 'json') {\n      console.log(EJSON.stringify(obj));\n    } else {\n      throw new Error(`Unknown logging output format: ${Log.outputFormat}`);\n    }\n  } else {\n    logInBrowser(obj);\n  }\n};\n});\n\n\n// tries to parse line as EJSON. returns object if parse is successful, or null if not\nLog.parse = (line) => {\n  let obj = null;\n  if (line && line.startsWith('{')) { // might be json generated from calling 'Log'\n    try { obj = EJSON.parse(line); } catch (e) {}\n  }\n\n  // XXX should probably check fields other than 'time'\n  if (obj && obj.time && (obj.time instanceof Date)) {\n    return obj;\n  } else {\n    return null;\n  }\n};\n\n// formats a log object into colored human and machine-readable text\nLog.format = (obj, options = {}) => {\n  obj = { ...obj }; // don't mutate the argument\n  let {\n    time,\n    timeInexact,\n    level = 'info',\n    file,\n    line: lineNumber,\n    app: appName = '',\n    originApp,\n    message = '',\n    program = '',\n    satellite = '',\n    stderr = '',\n  } = obj;\n\n  if (!(time instanceof Date)) {\n    throw new Error(\"'time' must be a Date object\");\n  }\n\n  FORMATTED_KEYS.forEach((key) => { delete obj[key]; });\n\n  if (Object.keys(obj).length > 0) {\n    if (message) {\n      message += ' ';\n    }\n    message += EJSON.stringify(obj);\n  }\n\n  const pad2 = n => n.toString().padStart(2, '0');\n  const pad3 = n => n.toString().padStart(3, '0');\n\n  const dateStamp = time.getFullYear().toString() +\n    pad2(time.getMonth() + 1 /*0-based*/) +\n    pad2(time.getDate());\n  const timeStamp = pad2(time.getHours()) +\n        ':' +\n        pad2(time.getMinutes()) +\n        ':' +\n        pad2(time.getSeconds()) +\n        '.' +\n        pad3(time.getMilliseconds());\n\n  // eg in San Francisco in June this will be '(-7)'\n  const utcOffsetStr = `(${(-(new Date().getTimezoneOffset() / 60))})`;\n\n  let appInfo = '';\n  if (appName) {\n    appInfo += appName;\n  }\n  if (originApp && originApp !== appName) {\n    appInfo += ` via ${originApp}`;\n  }\n  if (appInfo) {\n    appInfo = `[${appInfo}] `;\n  }\n\n  const sourceInfoParts = [];\n  if (program) {\n    sourceInfoParts.push(program);\n  }\n  if (file) {\n    sourceInfoParts.push(file);\n  }\n  if (lineNumber) {\n    sourceInfoParts.push(lineNumber);\n  }\n\n  let sourceInfo = !sourceInfoParts.length ?\n    '' : `(${sourceInfoParts.join(':')}) `;\n\n  if (satellite)\n    sourceInfo += `[${satellite}]`;\n\n  const stderrIndicator = stderr ? '(STDERR) ' : '';\n\n  const timeString = Log.showTime\n    ? `${dateStamp}-${timeStamp}${utcOffsetStr}${timeInexact ? '? ' : ' '}`\n    : ' ';\n\n\n\n  const metaPrefix = [\n    level.charAt(0).toUpperCase(),\n    timeString,\n    appInfo,\n    sourceInfo,\n    stderrIndicator].join('');\n\n\n  return Formatter.prettify(metaPrefix, options.color && platformColor(options.metaColor || META_COLOR)) +\n      Formatter.prettify(message, options.color && platformColor(LEVEL_COLORS[level]));\n};\n\n// Turn a line of text into a loggable object.\n// @param line {String}\n// @param override {Object}\nLog.objFromText = (line, override) => {\n  return {\n    message: line,\n    level: 'info',\n    time: new Date(),\n    timeInexact: true,\n    ...override\n  };\n};\n\nexport { Log };\n", "Formatter = {};\nFormatter.prettify = function(line, color){\n    return line;\n};\n"]}