{"version": 3, "sources": ["meteor://💻app/packages/callback-hook/hook.js"], "names": ["_regeneratorRuntime", "module", "link", "default", "v", "export", "Hook", "hasOwn", "Object", "prototype", "hasOwnProperty", "options", "nextCallbackId", "callbacks", "create", "bindEnvironment", "wrapAsync", "<PERSON><PERSON><PERSON><PERSON>", "debugPrintExceptions", "Error", "_proto", "register", "callback", "_this2", "exception", "Meteor", "dontBindEnvironment", "wrapFn", "id", "stop", "clear", "for<PERSON>ach", "iterator", "ids", "keys", "i", "length", "call", "forEachAsync", "async", "forEachAsync$", "_context", "prev", "next", "awrap", "sent", "abrupt", "Promise", "_context2", "each", "func", "onException", "_this", "description", "error", "_debug", "ret", "_len", "arguments", "args", "Array", "_key", "apply", "e"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,mBAAmB;AAACC,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,mBAAmB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhHH,MAAM,CAACI,MAAM,CAAC;EAACC,IAAI,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,IAAI;EAAA;AAAC,CAAC,CAAC;AAA7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAAC,IAElCJ,IAAI;EACf,SAAAA,KAAYK,OAAO,EAAE;IACnBA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,SAAS,GAAGL,MAAM,CAACM,MAAM,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAIJ,OAAO,CAACI,eAAe,KAAK,KAAK,EAAE;MACrC,IAAI,CAACA,eAAe,GAAG,KAAK;IAC9B;IAEA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAIL,OAAO,CAACK,SAAS,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACA,SAAS,GAAG,KAAK;IACxB;IAEA,IAAIL,OAAO,CAACM,gBAAgB,EAAE;MAC5B,IAAI,CAACA,gBAAgB,GAAGN,OAAO,CAACM,gBAAgB;IAClD,CAAC,MAAM,IAAIN,OAAO,CAACO,oBAAoB,EAAE;MACvC,IAAI,OAAOP,OAAO,CAACO,oBAAoB,KAAK,QAAQ,EAAE;QACpD,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;MACxE;MACA,IAAI,CAACF,gBAAgB,GAAGN,OAAO,CAACO,oBAAoB;IACtD;EACF;EAAC,IAAAE,MAAA,GAAAd,IAAA,CAAAG,SAAA;EAAAW,MAAA,CAEDC,QAAQ;IAAR,SAAAA,QAAQA,CAACC,QAAQ,EAAE;MAAA,IAAAC,MAAA;MACjB,IAAMN,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI,UAAUO,SAAS,EAAE;QACrE;QACA;QACA;QACA,MAAMA,SAAS;MACjB,CAAC;MAED,IAAI,IAAI,CAACT,eAAe,EAAE;QACxBO,QAAQ,GAAGG,MAAM,CAACV,eAAe,CAACO,QAAQ,EAAEL,gBAAgB,CAAC;MAC/D,CAAC,MAAM;QACLK,QAAQ,GAAGI,mBAAmB,CAACJ,QAAQ,EAAEL,gBAAgB,CAAC;MAC5D;MAEA,IAAI,IAAI,CAACD,SAAS,EAAE;QAClBM,QAAQ,GAAGG,MAAM,CAACE,MAAM,CAACL,QAAQ,CAAC;MACpC;MAEA,IAAMM,EAAE,GAAG,IAAI,CAAChB,cAAc,EAAE;MAChC,IAAI,CAACC,SAAS,CAACe,EAAE,CAAC,GAAGN,QAAQ;MAE7B,OAAO;QACLA,QAAQ,EAARA,QAAQ;QACRO,IAAI,EAAE,SAAAA,CAAA,EAAM;UACV,OAAON,MAAI,CAACV,SAAS,CAACe,EAAE,CAAC;QAC3B;MACF,CAAC;IACH;IAAC,OA3BDP,QAAQ;EAAA;EAAAD,MAAA,CA6BRU,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAI,CAAClB,cAAc,GAAG,CAAC;MACvB,IAAI,CAACC,SAAS,GAAG,EAAE;IACrB;IAAC,OAHDiB,KAAK;EAAA;EAKL;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAVE;EAAAV,MAAA,CAWAW,OAAO;IAAP,SAAAA,OAAOA,CAACC,QAAQ,EAAE;MAEhB,IAAMC,GAAG,GAAGzB,MAAM,CAAC0B,IAAI,CAAC,IAAI,CAACrB,SAAS,CAAC;MACvC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAGA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAG,EAAED,CAAC,EAAE;QACrC,IAAMP,EAAE,GAAGK,GAAG,CAACE,CAAC,CAAC;QACjB;QACA,IAAI5B,MAAM,CAAC8B,IAAI,CAAC,IAAI,CAACxB,SAAS,EAAEe,EAAE,CAAC,EAAE;UACnC,IAAMN,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACe,EAAE,CAAC;UACnC,IAAI,CAAEI,QAAQ,CAACV,QAAQ,CAAC,EAAE;YACxB;UACF;QACF;MACF;IACF;IAAC,OAbDS,OAAO;EAAA;EAAAX,MAAA,CAeDkB,YAAY;IAAlB,SAAMA,YAAYA,CAACN,QAAQ;MAAA,IAAAC,GAAA,EAAAE,CAAA,EAAAP,EAAA,EAAAN,QAAA;MAAA,OAAAtB,mBAAA,CAAAuC,KAAA;QAAA,SAAAC,cAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACnBV,GAAG,GAAGzB,MAAM,CAAC0B,IAAI,CAAC,IAAI,CAACrB,SAAS,CAAC;cAC9BsB,CAAC,GAAG,CAAC;YAAA;cAAA,MAAGA,CAAC,GAAGF,GAAG,CAACG,MAAM;gBAAAK,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACvBf,EAAE,GAAGK,GAAG,CAACE,CAAC,CAAC,EACjB;cAAA,KACI5B,MAAM,CAAC8B,IAAI,CAAC,IAAI,CAACxB,SAAS,EAAEe,EAAE,CAAC;gBAAAa,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAC3BrB,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACe,EAAE,CAAC;cAAAa,QAAA,CAAAE,IAAA;cAAA,OAAA3C,mBAAA,CAAA4C,KAAA,CACxBZ,QAAQ,CAACV,QAAQ,CAAC;YAAA;cAAA,IAAAmB,QAAA,CAAAI,IAAA;gBAAAJ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAK,MAAA;YAAA;cALC,EAAEX,CAAC;cAAAM,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAAZ,IAAA;UAAA;QAAA;QAAA,OAAAW,aAAA;MAAA,uBAAAO,OAAA;IAAA;IAUtC,OAZKT,YAAY;EAAA;EAclB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;EAAAlB,MAAA,CAQMkB,YAAY;IAAlB,SAAMA,YAAYA,CAACN,QAAQ;MAAA,IAAAC,GAAA,EAAAE,CAAA,EAAAP,EAAA,EAAAN,QAAA;MAAA,OAAAtB,mBAAA,CAAAuC,KAAA;QAAA,SAAAC,cAAAQ,SAAA;UAAA,kBAAAA,SAAA,CAAAN,IAAA,GAAAM,SAAA,CAAAL,IAAA;YAAA;cACnBV,GAAG,GAAGzB,MAAM,CAAC0B,IAAI,CAAC,IAAI,CAACrB,SAAS,CAAC;cAC9BsB,CAAC,GAAG,CAAC;YAAA;cAAA,MAAGA,CAAC,GAAGF,GAAG,CAACG,MAAM;gBAAAY,SAAA,CAAAL,IAAA;gBAAA;cAAA;cACvBf,EAAE,GAAGK,GAAG,CAACE,CAAC,CAAC,EACjB;cAAA,KACI5B,MAAM,CAAC8B,IAAI,CAAC,IAAI,CAACxB,SAAS,EAAEe,EAAE,CAAC;gBAAAoB,SAAA,CAAAL,IAAA;gBAAA;cAAA;cAC3BrB,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACe,EAAE,CAAC;cAAAoB,SAAA,CAAAL,IAAA;cAAA,OAAA3C,mBAAA,CAAA4C,KAAA,CACxBZ,QAAQ,CAACV,QAAQ,CAAC;YAAA;cAAA,IAAA0B,SAAA,CAAAH,IAAA;gBAAAG,SAAA,CAAAL,IAAA;gBAAA;cAAA;cAAA,OAAAK,SAAA,CAAAF,MAAA;YAAA;cALC,EAAEX,CAAC;cAAAa,SAAA,CAAAL,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAnB,IAAA;UAAA;QAAA;QAAA,OAAAW,aAAA;MAAA,uBAAAO,OAAA;IAAA;IAUtC,OAZKT,YAAY;EAAA;EAclB;AACF;AACA;AACA;EAHE;EAAAlB,MAAA,CAIA6B,IAAI;IAAJ,SAAAA,IAAIA,CAACjB,QAAQ,EAAE;MACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC;IAC/B;IAAC,OAFDiB,IAAI;EAAA;EAAA,OAAA3C,IAAA;AAAA;AAKN;AACA,SAASoB,mBAAmBA,CAACwB,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAE;EACrD,IAAI,CAACD,WAAW,IAAI,OAAOA,WAAY,KAAK,QAAQ,EAAE;IACpD,IAAME,WAAW,GAAGF,WAAW,IAAI,4BAA4B;IAC/DA,WAAW,GAAG,SAAAA,CAAUG,KAAK,EAAE;MAC7B7B,MAAM,CAAC8B,MAAM,CACX,eAAe,GAAGF,WAAW,EAC7BC,KACF,CAAC;IACH,CAAC;EACH;EAEA,OAAO,YAAmB;IACxB,IAAIE,GAAG;IACP,IAAI;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAtB,MAAA,EAFcuB,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MAGpBL,GAAG,GAAGN,IAAI,CAACY,KAAK,CAACV,KAAK,EAAEO,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOI,CAAC,EAAE;MACVZ,WAAW,CAACY,CAAC,CAAC;IAChB;IACA,OAAOP,GAAG;EACZ,CAAC;AACH,C", "file": "/packages/callback-hook.js", "sourcesContent": ["// XXX This pattern is under development. Do not add more callsites\n// using this package for now. See:\n// https://meteor.hackpad.com/Design-proposal-Hooks-YxvgEW06q6f\n//\n// Encapsulates the pattern of registering callbacks on a hook.\n//\n// The `each` method of the hook calls its iterator function argument\n// with each registered callback.  This allows the hook to\n// conditionally decide not to call the callback (if, for example, the\n// observed object has been closed or terminated).\n//\n// By default, callbacks are bound with `Meteor.bindEnvironment`, so they will be\n// called with the Meteor environment of the calling code that\n// registered the callback. Override by passing { bindEnvironment: false }\n// to the constructor.\n//\n// Registering a callback returns an object with a single `stop`\n// method which unregisters the callback.\n//\n// The code is careful to allow a callback to be safely unregistered\n// while the callbacks are being iterated over.\n//\n// If the hook is configured with the `exceptionHandler` option, the\n// handler will be called if a called callback throws an exception.\n// By default (if the exception handler doesn't itself throw an\n// exception, or if the iterator function doesn't return a falsy value\n// to terminate the calling of callbacks), the remaining callbacks\n// will still be called.\n//\n// Alternatively, the `debugPrintExceptions` option can be specified\n// as string describing the callback.  On an exception the string and\n// the exception will be printed to the console log with\n// `Meteor._debug`, and the exception otherwise ignored.\n//\n// If an exception handler isn't specified, exceptions thrown in the\n// callback will propagate up to the iterator function, and will\n// terminate calling the remaining callbacks if not caught.\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nexport class Hook {\n  constructor(options) {\n    options = options || {};\n    this.nextCallbackId = 0;\n    this.callbacks = Object.create(null);\n    // Whether to wrap callbacks with Meteor.bindEnvironment\n    this.bindEnvironment = true;\n    if (options.bindEnvironment === false) {\n      this.bindEnvironment = false;\n    }\n\n    this.wrapAsync = true;\n    if (options.wrapAsync === false) {\n      this.wrapAsync = false;\n    }\n\n    if (options.exceptionHandler) {\n      this.exceptionHandler = options.exceptionHandler;\n    } else if (options.debugPrintExceptions) {\n      if (typeof options.debugPrintExceptions !== \"string\") {\n        throw new Error(\"Hook option debugPrintExceptions should be a string\");\n      }\n      this.exceptionHandler = options.debugPrintExceptions;\n    }\n  }\n\n  register(callback) {\n    const exceptionHandler = this.exceptionHandler || function (exception) {\n      // Note: this relies on the undocumented fact that if bindEnvironment's\n      // onException throws, and you are invoking the callback either in the\n      // browser or from within a Fiber in Node, the exception is propagated.\n      throw exception;\n    };\n\n    if (this.bindEnvironment) {\n      callback = Meteor.bindEnvironment(callback, exceptionHandler);\n    } else {\n      callback = dontBindEnvironment(callback, exceptionHandler);\n    }\n\n    if (this.wrapAsync) {\n      callback = Meteor.wrapFn(callback);\n    }\n\n    const id = this.nextCallbackId++;\n    this.callbacks[id] = callback;\n\n    return {\n      callback,\n      stop: () => {\n        delete this.callbacks[id];\n      }\n    };\n  }\n\n  clear() {\n    this.nextCallbackId = 0;\n    this.callbacks = [];\n  }\n\n  /**\n   * For each registered callback, call the passed iterator function with the callback.\n   *\n   * The iterator function can choose whether or not to call the\n   * callback.  (For example, it might not call the callback if the\n   * observed object has been closed or terminated).\n   * The iteration is stopped if the iterator function returns a falsy\n   * value or throws an exception.\n   *\n   * @param iterator\n   */\n  forEach(iterator) {\n\n    const ids = Object.keys(this.callbacks);\n    for (let i = 0;  i < ids.length;  ++i) {\n      const id = ids[i];\n      // check to see if the callback was removed during iteration\n      if (hasOwn.call(this.callbacks, id)) {\n        const callback = this.callbacks[id];\n        if (! iterator(callback)) {\n          break;\n        }\n      }\n    }\n  }\n\n  async forEachAsync(iterator) {\n    const ids = Object.keys(this.callbacks);\n    for (let i = 0;  i < ids.length;  ++i) {\n      const id = ids[i];\n      // check to see if the callback was removed during iteration\n      if (hasOwn.call(this.callbacks, id)) {\n        const callback = this.callbacks[id];\n        if (!await iterator(callback)) {\n          break;\n        }\n      }\n    }\n  }\n\n  /**\n   * For each registered callback, call the passed iterator function with the callback.\n   *\n   * it is a counterpart of forEach, but it is async and returns a promise\n   * @param iterator\n   * @return {Promise<void>}\n   * @see forEach\n   */\n  async forEachAsync(iterator) {\n    const ids = Object.keys(this.callbacks);\n    for (let i = 0;  i < ids.length;  ++i) {\n      const id = ids[i];\n      // check to see if the callback was removed during iteration\n      if (hasOwn.call(this.callbacks, id)) {\n        const callback = this.callbacks[id];\n        if (!await iterator(callback)) {\n          break;\n        }\n      }\n    }\n  }\n\n  /**\n   * @deprecated use forEach\n   * @param iterator\n   */\n  each(iterator) {\n    return this.forEach(iterator);\n  }\n}\n\n// Copied from Meteor.bindEnvironment and removed all the env stuff.\nfunction dontBindEnvironment(func, onException, _this) {\n  if (!onException || typeof(onException) === 'string') {\n    const description = onException || \"callback of async function\";\n    onException = function (error) {\n      Meteor._debug(\n        \"Exception in \" + description,\n        error\n      );\n    };\n  }\n\n  return function (...args) {\n    let ret;\n    try {\n      ret = func.apply(_this, args);\n    } catch (e) {\n      onException(e);\n    }\n    return ret;\n  };\n}\n"]}