{"version": 3, "sources": ["meteor://💻app/packages/email/email.js"], "names": ["_objectSpread", "module1", "link", "default", "v", "export", "Email", "EmailTest", "EmailInternals", "Meteor", "Log", "Hook", "url", "nodemailer", "wellKnow", "openpgpEncrypt", "__reifyWaitForDeps__", "NpmModules", "mailcomposer", "version", "Npm", "require", "module", "MailComposer", "makeTransport", "mailUrlString", "options", "mailUrl", "URL", "protocol", "Error", "port", "debug", "query", "pool", "transport", "createTransport", "format", "encryption<PERSON>eys", "shouldSign", "use", "knownHostsTransport", "settings", "arguments", "length", "undefined", "service", "user", "password", "hasSettings", "Object", "keys", "host", "split", "urlObject", "hostname", "username", "_urlObject$pathname$s", "temp", "pathname", "substring", "temp2", "auth", "pass", "knowHostsTransport", "getTransport", "_Meteor$settings$pack", "packageSettings", "packages", "email", "process", "env", "MAIL_URL", "globalThis", "cache<PERSON>ey", "cache", "nextDevModeMailId", "_getAndIncNextDevModeMailId", "resetNextDevModeMailId", "devModeSendAsync", "mail", "stream", "stdout", "Promise", "resolve", "reject", "devModeMailId", "output", "push", "readStream", "compile", "createReadStream", "on", "buffer", "toString", "write", "join", "err", "sendHooks", "hookSend", "f", "register", "customTransport", "sendAsync", "_Meteor$settings$pack3", "mailComposer", "send", "forEachAsync", "sendHook", "_Meteor$settings$pack2", "mailUrlEnv", "mailUrlSettings", "isProduction", "sendMail", "then", "console", "warn", "catch", "e", "error", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAtGH,OAAO,CAACI,MAAM,CAAC;MAACC,KAAK,EAACA,CAAA,KAAIA,KAAK;MAACC,SAAS,EAACA,CAAA,KAAIA,SAAS;MAACC,cAAc,EAACA,CAAA,KAAIA;IAAc,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACR,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;MAACO,MAAMA,CAACL,CAAC,EAAC;QAACK,MAAM,GAACL,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIM,GAAG;IAACT,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAC;MAACQ,GAAGA,CAACN,CAAC,EAAC;QAACM,GAAG,GAACN,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIO,IAAI;IAACV,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACS,IAAIA,CAACP,CAAC,EAAC;QAACO,IAAI,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,GAAG;IAACX,OAAO,CAACC,IAAI,CAAC,KAAK,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACQ,GAAG,GAACR,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIS,UAAU;IAACZ,OAAO,CAACC,IAAI,CAAC,YAAY,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACS,UAAU,GAACT,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIU,QAAQ;IAACb,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACU,QAAQ,GAACV,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIW,cAAc;IAACd,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAC;MAACa,cAAcA,CAACX,CAAC,EAAC;QAACW,cAAc,GAACX,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIY,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAStnB,MAAMV,KAAK,GAAG,CAAC,CAAC;IAChB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,MAAMC,cAAc,GAAG;MAC5BS,UAAU,EAAE;QACVC,YAAY,EAAE;UACZC,OAAO,EAAEC,GAAG,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAACF,OAAO;UACvDG,MAAM,EAAEF,GAAG,CAACC,OAAO,CAAC,8BAA8B;QACpD,CAAC;QACDR,UAAU,EAAE;UACVM,OAAO,EAAEC,GAAG,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAACF,OAAO;UACvDG,MAAM,EAAEF,GAAG,CAACC,OAAO,CAAC,YAAY;QAClC;MACF;IACF,CAAC;IAED,MAAME,YAAY,GAAGf,cAAc,CAACS,UAAU,CAACC,YAAY,CAACI,MAAM;IAElE,MAAME,aAAa,GAAG,SAAAA,CAAUC,aAAa,EAAEC,OAAO,EAAE;MACtD,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAACH,aAAa,CAAC;MAEtC,IAAIE,OAAO,CAACE,QAAQ,KAAK,OAAO,IAAIF,OAAO,CAACE,QAAQ,KAAK,QAAQ,EAAE;QACjE,MAAM,IAAIC,KAAK,CACb,+BAA+B,GAC7BL,aAAa,GACb,6BACJ,CAAC;MACH;MAEA,IAAIE,OAAO,CAACE,QAAQ,KAAK,OAAO,IAAIF,OAAO,CAACI,IAAI,KAAK,KAAK,EAAE;QAC1DrB,GAAG,CAACsB,KAAK,CACP,sCAAsC,GACpC,yDAAyD,GACzD,yCACJ,CAAC;MACH;;MAEA;MACA,IAAI,CAACL,OAAO,CAACM,KAAK,EAAE;QAClBN,OAAO,CAACM,KAAK,GAAG,CAAC,CAAC;MACpB;MAEA,IAAI,CAACN,OAAO,CAACM,KAAK,CAACC,IAAI,EAAE;QACvBP,OAAO,CAACM,KAAK,CAACC,IAAI,GAAG,MAAM;MAC7B;MAEA,MAAMC,SAAS,GAAGtB,UAAU,CAACuB,eAAe,CAACxB,GAAG,CAACyB,MAAM,CAACV,OAAO,CAAC,CAAC;MACjE,IAAID,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEY,cAAc,IAAIZ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEa,UAAU,EAAE;QAClDJ,SAAS,CAACK,GAAG,CAAC,QAAQ,EAAEzB,cAAc,CAACW,OAAO,CAAC,CAAC;MAClD;MACA,OAAOS,SAAS;IAClB,CAAC;;IAED;IACA,MAAMM,mBAAmB,GAAG,SAAAA,CAAA,EAA0D;MAAA,IAAhDC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGE,SAAS;MAAA,IAAEjC,GAAG,GAAA+B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGE,SAAS;MAAA,IAAEnB,OAAO,GAAAiB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MAClF,IAAIC,OAAO,EAAEC,IAAI,EAAEC,QAAQ;MAE3B,MAAMC,WAAW,GAAGP,QAAQ,IAAIQ,MAAM,CAACC,IAAI,CAACT,QAAQ,CAAC,CAACE,MAAM;MAE5D,IAAIhC,GAAG,IAAI,CAACqC,WAAW,EAAE;QACvB,IAAIG,IAAI,GAAGxC,GAAG,CAACyC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,MAAMC,SAAS,GAAG,IAAI1B,GAAG,CAAChB,GAAG,CAAC;QAC9B,IAAIwC,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,EAAE;UACvC;UACAA,IAAI,GAAGE,SAAS,CAACC,QAAQ;UACzBR,IAAI,GAAGO,SAAS,CAACE,QAAQ;UACzBR,QAAQ,GAAGM,SAAS,CAACN,QAAQ;QAC/B,CAAC,MAAM,IAAIM,SAAS,CAACzB,QAAQ,IAAIyB,SAAS,CAACE,QAAQ,IAAIF,SAAS,CAACN,QAAQ,EAAE;UACzE;UACAI,IAAI,GAAGE,SAAS,CAACzB,QAAQ,CAACwB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACvCN,IAAI,GAAGO,SAAS,CAACE,QAAQ;UACzBR,QAAQ,GAAGM,SAAS,CAACN,QAAQ;QAC/B,CAAC,MAAM;UAAA,IAAAS,qBAAA;UACL;UACA;UACA,MAAMC,IAAI,IAAAD,qBAAA,GAAGH,SAAS,CAACK,QAAQ,CAACC,SAAS,CAAC,CAAC,CAAC,cAAAH,qBAAA,uBAA/BA,qBAAA,CAAiCJ,KAAK,CAAC,GAAG,CAAC;UACxDN,IAAI,GAAGW,IAAI,CAAC,CAAC,CAAC;UACd;UACA,MAAMG,KAAK,GAAGH,IAAI,CAAC,CAAC,CAAC,CAACL,KAAK,CAAC,GAAG,CAAC;UAChCL,QAAQ,GAAGa,KAAK,CAAC,CAAC,CAAC;UACnBT,IAAI,GAAGS,KAAK,CAAC,CAAC,CAAC;QACjB;QACAf,OAAO,GAAGM,IAAI;MAChB;MAEA,IAAI,CAACtC,QAAQ,CAAC,CAAA4B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,OAAO,KAAIA,OAAO,CAAC,EAAE;QAC3C,MAAM,IAAIhB,KAAK,CACb,qIACF,CAAC;MACH;MAEA,MAAMK,SAAS,GAAGtB,UAAU,CAACuB,eAAe,CAAC;QAC3CU,OAAO,EAAE,CAAAJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,OAAO,KAAIA,OAAO;QACrCgB,IAAI,EAAE;UACJf,IAAI,EAAE,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEK,IAAI,KAAIA,IAAI;UAC5BgB,IAAI,EAAE,CAAArB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,QAAQ,KAAIA;QAC9B;MACF,CAAC,CAAC;MAEF,IAAItB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEY,cAAc,IAAIZ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEa,UAAU,EAAE;QAClDJ,SAAS,CAACK,GAAG,CAAC,QAAQ,EAAEzB,cAAc,CAACW,OAAO,CAAC,CAAC;MAClD;MACA,OAAOS,SAAS;IAClB,CAAC;IACD5B,SAAS,CAACyD,kBAAkB,GAAGvB,mBAAmB;IAElD,MAAMwB,YAAY,GAAG,SAAAA,CAAUvC,OAAO,EAAE;MAAA,IAAAwC,qBAAA;MACtC,MAAMC,eAAe,GAAG,EAAAD,qBAAA,GAAAzD,MAAM,CAACiC,QAAQ,CAAC0B,QAAQ,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0BG,KAAK,KAAI,CAAC,CAAC;MAC7D;MACA;MACA;MACA,MAAMzD,GAAG,GAAG0D,OAAO,CAACC,GAAG,CAACC,QAAQ;MAChC,IACEC,UAAU,CAACC,QAAQ,KAAK7B,SAAS,IACjC4B,UAAU,CAACC,QAAQ,KAAK9D,GAAG,IAC3B6D,UAAU,CAACC,QAAQ,KAAKP,eAAe,CAACrB,OAAO,IAC/C2B,UAAU,CAACC,QAAQ,KAAK,UAAU,EAClC;QACA,IACGP,eAAe,CAACrB,OAAO,IAAIhC,QAAQ,CAACqD,eAAe,CAACrB,OAAO,CAAC,IAC5DlC,GAAG,IAAIE,QAAQ,CAAC,IAAIc,GAAG,CAAChB,GAAG,CAAC,CAAC2C,QAAQ,CAAE,IACxCzC,QAAQ,CAAC,CAAAF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEyC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE,CAAC,EAClC;UACAoB,UAAU,CAACC,QAAQ,GAAGP,eAAe,CAACrB,OAAO,IAAI,UAAU;UAC3D2B,UAAU,CAACE,KAAK,GAAGlC,mBAAmB,CAAC0B,eAAe,EAAEvD,GAAG,EAAEc,OAAO,CAAC;QACvE,CAAC,MAAM;UACL+C,UAAU,CAACC,QAAQ,GAAG9D,GAAG;UACzB6D,UAAU,CAACE,KAAK,GAAG/D,GAAG,GAAGY,aAAa,CAACZ,GAAG,EAAEc,OAAO,CAAC,GAAG,IAAI;QAC7D;MACF;MACA,OAAO+C,UAAU,CAACE,KAAK;IACzB,CAAC;IAED,IAAIC,iBAAiB,GAAG,CAAC;IAEzBrE,SAAS,CAACsE,2BAA2B,GAAG,YAAY;MAClD,OAAOD,iBAAiB,EAAE;IAC5B,CAAC;;IAED;IACArE,SAAS,CAACuE,sBAAsB,GAAG,YAAY;MAC7CF,iBAAiB,GAAG,CAAC;IACvB,CAAC;IAED,MAAMG,gBAAgB,GAAG,SAAAA,CAAUC,IAAI,EAAEtD,OAAO,EAAE;MAChD,MAAMuD,MAAM,GAAG,CAAAvD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuD,MAAM,KAAIX,OAAO,CAACY,MAAM;MAChD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,IAAIC,aAAa,GAAG/E,SAAS,CAACsE,2BAA2B,CAAC,CAAC;;QAE3D;QACA,MAAMU,MAAM,GAAG,CAAC,qBAAqB,GAAGD,aAAa,GAAG,WAAW,CAAC;QACpEC,MAAM,CAACC,IAAI,CACT,sDAAsD,GACtD,0BACF,CAAC;QACD,MAAMC,UAAU,GAAG,IAAIlE,YAAY,CAACyD,IAAI,CAAC,CAACU,OAAO,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC;QACtEF,UAAU,CAACG,EAAE,CAAC,MAAM,EAAEC,MAAM,IAAI;UAC9BN,MAAM,CAACC,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;QACFL,UAAU,CAACG,EAAE,CAAC,KAAK,EAAE,YAAY;UAC/BL,MAAM,CAACC,IAAI,CAAC,mBAAmB,GAAGF,aAAa,GAAG,WAAW,CAAC;UAC9DL,MAAM,CAACc,KAAK,CAACR,MAAM,CAACS,IAAI,CAAC,EAAE,CAAC,EAAE,MAAMZ,OAAO,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC;QACFK,UAAU,CAACG,EAAE,CAAC,OAAO,EAAGK,GAAG,IAAKZ,MAAM,CAACY,GAAG,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC;IAED,MAAMC,SAAS,GAAG,IAAIvF,IAAI,CAAC,CAAC;;IAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAL,KAAK,CAAC6F,QAAQ,GAAG,UAAUC,CAAC,EAAE;MAC5B,OAAOF,SAAS,CAACG,QAAQ,CAACD,CAAC,CAAC;IAC9B,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;IACA9F,KAAK,CAACgG,eAAe,GAAGzD,SAAS;;IAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAvC,KAAK,CAACiG,SAAS,GAAG,gBAAgB7E,OAAO,EAAE;MAAA,IAAA8E,sBAAA;MACzC,MAAMnC,KAAK,GAAG3C,OAAO,CAAC+E,YAAY,GAAG/E,OAAO,CAAC+E,YAAY,CAACzB,IAAI,GAAGtD,OAAO;MAExE,IAAIgF,IAAI,GAAG,IAAI;MACf,MAAMR,SAAS,CAACS,YAAY,CAAC,MAAOC,QAAQ,IAAK;QAC/CF,IAAI,GAAG,MAAME,QAAQ,CAACvC,KAAK,CAAC;QAC5B,OAAOqC,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MAEA,IAAIpG,KAAK,CAACgG,eAAe,EAAE;QAAA,IAAAO,sBAAA;QACzB,MAAM1C,eAAe,GAAG,EAAA0C,sBAAA,GAAApG,MAAM,CAACiC,QAAQ,CAAC0B,QAAQ,cAAAyC,sBAAA,uBAAxBA,sBAAA,CAA0BxC,KAAK,KAAI,CAAC,CAAC;QAC7D,OAAO/D,KAAK,CAACgG,eAAe,CAAAtG,aAAA;UAAGmE;QAAe,GAAKE,KAAK,CAAE,CAAC;MAC7D;MAEA,MAAMyC,UAAU,GAAGxC,OAAO,CAACC,GAAG,CAACC,QAAQ;MACvC,MAAMuC,eAAe,IAAAP,sBAAA,GAAG/F,MAAM,CAACiC,QAAQ,CAAC0B,QAAQ,cAAAoC,sBAAA,uBAAxBA,sBAAA,CAA0BnC,KAAK;MAEvD,IAAI5D,MAAM,CAACuG,YAAY,IAAI,CAACF,UAAU,IAAI,CAACC,eAAe,EAAE;QAC1D;QACA;QACA,MAAM,IAAIjF,KAAK,CACb,4LACF,CAAC;MACH;MAEA,IAAIgF,UAAU,IAAIC,eAAe,EAAE;QACjC,OAAO9C,YAAY,CAAC,CAAC,CAACgD,QAAQ,CAAC5C,KAAK,CAAC;MACvC;MAEA,OAAOU,gBAAgB,CAACV,KAAK,EAAE3C,OAAO,CAAC;IACzC,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACApB,KAAK,CAACoG,IAAI,GAAG,UAAShF,OAAO,EAAE;MAC7BpB,KAAK,CAACiG,SAAS,CAAC7E,OAAO,CAAC,CACrBwF,IAAI,CAAC,MACJC,OAAO,CAACC,IAAI,sEAEZ,CACF,CAAC,CACAC,KAAK,CAACC,CAAC,IACNH,OAAO,CAACI,KAAK,8DAEXD,CACF,CACF,CAAC;IACL,CAAC;IAACE,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/email.js", "sourcesContent": ["import { Meteor } from 'meteor/meteor';\nimport { Log } from 'meteor/logging';\nimport { Hook } from 'meteor/callback-hook';\n\nimport url from 'url';\nimport nodemailer from 'nodemailer';\nimport wellKnow from 'nodemailer/lib/well-known';\nimport { openpgpEncrypt } from 'nodemailer-openpgp';\n\nexport const Email = {};\nexport const EmailTest = {};\n\nexport const EmailInternals = {\n  NpmModules: {\n    mailcomposer: {\n      version: Npm.require('nodemailer/package.json').version,\n      module: Npm.require('nodemailer/lib/mail-composer'),\n    },\n    nodemailer: {\n      version: Npm.require('nodemailer/package.json').version,\n      module: Npm.require('nodemailer'),\n    },\n  },\n};\n\nconst MailComposer = EmailInternals.NpmModules.mailcomposer.module;\n\nconst makeTransport = function (mailUrlString, options) {\n  const mailUrl = new URL(mailUrlString);\n\n  if (mailUrl.protocol !== 'smtp:' && mailUrl.protocol !== 'smtps:') {\n    throw new Error(\n      'Email protocol in $MAIL_URL (' +\n        mailUrlString +\n        \") must be 'smtp' or 'smtps'\"\n    );\n  }\n\n  if (mailUrl.protocol === 'smtp:' && mailUrl.port === '465') {\n    Log.debug(\n      \"The $MAIL_URL is 'smtp://...:465'.  \" +\n        \"You probably want 'smtps://' (The 's' enables TLS/SSL) \" +\n        \"since '465' is typically a secure port.\"\n    );\n  }\n\n  // Allow overriding pool setting, but default to true.\n  if (!mailUrl.query) {\n    mailUrl.query = {};\n  }\n\n  if (!mailUrl.query.pool) {\n    mailUrl.query.pool = 'true';\n  }\n\n  const transport = nodemailer.createTransport(url.format(mailUrl));\n  if (options?.encryptionKeys || options?.shouldSign) {\n    transport.use('stream', openpgpEncrypt(options));\n  }\n  return transport;\n};\n\n// More info: https://nodemailer.com/smtp/well-known/\nconst knownHostsTransport = function (settings = undefined, url = undefined, options) {\n  let service, user, password;\n\n  const hasSettings = settings && Object.keys(settings).length;\n\n  if (url && !hasSettings) {\n    let host = url.split(':')[0];\n    const urlObject = new URL(url);\n    if (host === 'http' || host === 'https') {\n      // Look to hostname for service\n      host = urlObject.hostname;\n      user = urlObject.username;\n      password = urlObject.password;\n    } else if (urlObject.protocol && urlObject.username && urlObject.password) {\n      // We have some data from urlObject\n      host = urlObject.protocol.split(':')[0];\n      user = urlObject.username;\n      password = urlObject.password;\n    } else {\n      // We need to disect the URL ourselves to get the data\n      // First get rid of the leading '//' and split to username and the rest\n      const temp = urlObject.pathname.substring(2)?.split(':');\n      user = temp[0];\n      // Now we split by '@' to get password and hostname\n      const temp2 = temp[1].split('@');\n      password = temp2[0];\n      host = temp2[1];\n    }\n    service = host;\n  }\n\n  if (!wellKnow(settings?.service || service)) {\n    throw new Error(\n      'Could not recognize e-mail service. See list at https://nodemailer.com/smtp/well-known/ for services that we can configure for you.'\n    );\n  }\n\n  const transport = nodemailer.createTransport({\n    service: settings?.service || service,\n    auth: {\n      user: settings?.user || user,\n      pass: settings?.password || password,\n    },\n  });\n\n  if (options?.encryptionKeys || options?.shouldSign) {\n    transport.use('stream', openpgpEncrypt(options));\n  }\n  return transport;\n};\nEmailTest.knowHostsTransport = knownHostsTransport;\n\nconst getTransport = function (options) {\n  const packageSettings = Meteor.settings.packages?.email || {};\n  // We delay this check until the first call to Email.send, in case someone\n  // set process.env.MAIL_URL in startup code. Then we store in a cache until\n  // process.env.MAIL_URL changes.\n  const url = process.env.MAIL_URL;\n  if (\n    globalThis.cacheKey === undefined ||\n    globalThis.cacheKey !== url ||\n    globalThis.cacheKey !== packageSettings.service ||\n    globalThis.cacheKey !== 'settings'\n  ) {\n    if (\n      (packageSettings.service && wellKnow(packageSettings.service)) ||\n      (url && wellKnow(new URL(url).hostname)) ||\n      wellKnow(url?.split(':')[0] || '')\n    ) {\n      globalThis.cacheKey = packageSettings.service || 'settings';\n      globalThis.cache = knownHostsTransport(packageSettings, url, options);\n    } else {\n      globalThis.cacheKey = url;\n      globalThis.cache = url ? makeTransport(url, options) : null;\n    }\n  }\n  return globalThis.cache;\n};\n\nlet nextDevModeMailId = 0;\n\nEmailTest._getAndIncNextDevModeMailId = function () {\n  return nextDevModeMailId++;\n};\n\n// Testing hooks\nEmailTest.resetNextDevModeMailId = function () {\n  nextDevModeMailId = 0;\n};\n\nconst devModeSendAsync = function (mail, options) {\n  const stream = options?.stream || process.stdout;\n  return new Promise((resolve, reject) => {\n    let devModeMailId = EmailTest._getAndIncNextDevModeMailId();\n\n    // This approach does not prevent other writers to stdout from interleaving.\n    const output = ['====== BEGIN MAIL #' + devModeMailId + ' ======\\n'];\n    output.push(\n      '(Mail not sent; to enable sending, set the MAIL_URL ' +\n      'environment variable.)\\n'\n    );\n    const readStream = new MailComposer(mail).compile().createReadStream();\n    readStream.on('data', buffer => {\n      output.push(buffer.toString());\n    });\n    readStream.on('end', function () {\n      output.push('====== END MAIL #' + devModeMailId + ' ======\\n');\n      stream.write(output.join(''), () => resolve());\n    });\n    readStream.on('error', (err) => reject(err));\n  });\n};\n\nconst sendHooks = new Hook();\n\n/**\n * @summary Hook that runs before email is sent.\n * @locus Server\n *\n * @param f {function} receives the arguments to Email.send and should return true to go\n * ahead and send the email (or at least, try subsequent hooks), or\n * false to skip sending.\n * @returns {{ stop: function, callback: function }}\n */\nEmail.hookSend = function (f) {\n  return sendHooks.register(f);\n};\n\n/**\n * @summary Overrides sending function with your own.\n * @locus Server\n * @since 2.2\n * @param f {function} function that will receive options from the send function and under `packageSettings` will\n * include the package settings from Meteor.settings.packages.email for your custom transport to access.\n */\nEmail.customTransport = undefined;\n\n/**\n * @summary Send an email with asyncronous method. Capture  Throws an `Error` on failure to contact mail server\n * or if mail server returns an error. All fields should match\n * [RFC5322](http://tools.ietf.org/html/rfc5322) specification.\n *\n * If the `MAIL_URL` environment variable is set, actually sends the email.\n * Otherwise, prints the contents of the email to standard out.\n *\n * Note that this package is based on **nodemailer**, so make sure to refer to\n * [the documentation](http://nodemailer.com/)\n * when using the `attachments` or `mailComposer` options.\n *\n * @locus Server\n * @return {Promise}\n * @param {Object} options\n * @param {String} options.from \"From:\" address (required)\n * @param {String|String[]} options.to,cc,bcc,replyTo\n *   \"To:\", \"Cc:\", \"Bcc:\", and \"Reply-To:\" addresses\n * @param {String} [options.inReplyTo] Message-ID this message is replying to\n * @param {String|String[]} [options.references] Array (or space-separated string) of Message-IDs to refer to\n * @param {String} [options.messageId] Message-ID for this message; otherwise, will be set to a random value\n * @param {String} [options.subject]  \"Subject:\" line\n * @param {String} [options.text|html] Mail body (in plain text and/or HTML)\n * @param {String} [options.watchHtml] Mail body in HTML specific for Apple Watch\n * @param {String} [options.icalEvent] iCalendar event attachment\n * @param {Object} [options.headers] Dictionary of custom headers - e.g. `{ \"header name\": \"header value\" }`. To set an object under a header name, use `JSON.stringify` - e.g. `{ \"header name\": JSON.stringify({ tracking: { level: 'full' } }) }`.\n * @param {Object[]} [options.attachments] Array of attachment objects, as\n * described in the [nodemailer documentation](https://nodemailer.com/message/attachments/).\n * @param {MailComposer} [options.mailComposer] A [MailComposer](https://nodemailer.com/extras/mailcomposer/#e-mail-message-fields)\n * object representing the message to be sent.  Overrides all other options.\n * You can create a `MailComposer` object via\n * `new EmailInternals.NpmModules.mailcomposer.module`.\n */\nEmail.sendAsync = async function (options) {\n  const email = options.mailComposer ? options.mailComposer.mail : options;\n\n  let send = true;\n  await sendHooks.forEachAsync(async (sendHook) => {\n    send = await sendHook(email);\n    return send;\n  });\n  if (!send) {\n    return;\n  }\n\n  if (Email.customTransport) {\n    const packageSettings = Meteor.settings.packages?.email || {};\n    return Email.customTransport({ packageSettings, ...email });\n  }\n\n  const mailUrlEnv = process.env.MAIL_URL;\n  const mailUrlSettings = Meteor.settings.packages?.email;\n\n  if (Meteor.isProduction && !mailUrlEnv && !mailUrlSettings) {\n    // This check is mostly necessary when using the flag --production when running locally.\n    // And it works as a reminder to properly set the mail URL when running locally.\n    throw new Error(\n      'You have not provided a mail URL. You can provide it by using the environment variable MAIL_URL or your settings. You can read more about it here: https://docs.meteor.com/api/email.html.'\n    );\n  }\n\n  if (mailUrlEnv || mailUrlSettings) {\n    return getTransport().sendMail(email);\n  }\n\n  return devModeSendAsync(email, options);\n};\n\n/**\n * @deprecated\n * @summary Send an email with asyncronous method. Capture  Throws an `Error` on failure to contact mail server\n * or if mail server returns an error. All fields should match\n * [RFC5322](http://tools.ietf.org/html/rfc5322) specification.\n *\n * If the `MAIL_URL` environment variable is set, actually sends the email.\n * Otherwise, prints the contents of the email to standard out.\n *\n * Note that this package is based on **nodemailer**, so make sure to refer to\n * [the documentation](http://nodemailer.com/)\n * when using the `attachments` or `mailComposer` options.\n *\n * @locus Server\n * @return {Promise}\n * @param {Object} options\n * @param {String} options.from \"From:\" address (required)\n * @param {String|String[]} options.to,cc,bcc,replyTo\n *   \"To:\", \"Cc:\", \"Bcc:\", and \"Reply-To:\" addresses\n * @param {String} [options.inReplyTo] Message-ID this message is replying to\n * @param {String|String[]} [options.references] Array (or space-separated string) of Message-IDs to refer to\n * @param {String} [options.messageId] Message-ID for this message; otherwise, will be set to a random value\n * @param {String} [options.subject]  \"Subject:\" line\n * @param {String} [options.text|html] Mail body (in plain text and/or HTML)\n * @param {String} [options.watchHtml] Mail body in HTML specific for Apple Watch\n * @param {String} [options.icalEvent] iCalendar event attachment\n * @param {Object} [options.headers] Dictionary of custom headers - e.g. `{ \"header name\": \"header value\" }`. To set an object under a header name, use `JSON.stringify` - e.g. `{ \"header name\": JSON.stringify({ tracking: { level: 'full' } }) }`.\n * @param {Object[]} [options.attachments] Array of attachment objects, as\n * described in the [nodemailer documentation](https://nodemailer.com/message/attachments/).\n * @param {MailComposer} [options.mailComposer] A [MailComposer](https://nodemailer.com/extras/mailcomposer/#e-mail-message-fields)\n * object representing the message to be sent.  Overrides all other options.\n * You can create a `MailComposer` object via\n * `new EmailInternals.NpmModules.mailcomposer.module`.\n * @param {String} [options.encryptionKeys] An array that holds the public keys used to encrypt.\n * @param {String} [options.shouldSign] Enables you to allow or disallow email signing.\n*/\nEmail.send = function(options) {\n  Email.sendAsync(options)\n    .then(() =>\n      console.warn(\n        `Email.send is no longer recommended, you should use Email.sendAsync`\n      )\n    )\n    .catch(e =>\n      console.error(\n        `Email.send is no longer recommended and an error happened`,\n        e\n      )\n    );\n};\n"]}