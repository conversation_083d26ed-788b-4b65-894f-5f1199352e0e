{"version": 3, "sources": ["meteor://💻app/packages/callback-hook/hook.js"], "names": ["module", "export", "Hook", "hasOwn", "Object", "prototype", "hasOwnProperty", "constructor", "options", "nextCallbackId", "callbacks", "create", "bindEnvironment", "wrapAsync", "<PERSON><PERSON><PERSON><PERSON>", "debugPrintExceptions", "Error", "register", "callback", "exception", "Meteor", "dontBindEnvironment", "wrapFn", "id", "stop", "clear", "for<PERSON>ach", "iterator", "ids", "keys", "i", "length", "call", "forEachAsync", "each", "func", "onException", "_this", "description", "error", "_debug", "ret", "_len", "arguments", "args", "Array", "_key", "apply", "e"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,IAAI,EAACA,CAAA,KAAIA;AAAI,CAAC,CAAC;AAA9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAEvC,MAAMJ,IAAI,CAAC;EAChBK,WAAWA,CAACC,OAAO,EAAE;IACnBA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,SAAS,GAAGN,MAAM,CAACO,MAAM,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAIJ,OAAO,CAACI,eAAe,KAAK,KAAK,EAAE;MACrC,IAAI,CAACA,eAAe,GAAG,KAAK;IAC9B;IAEA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAIL,OAAO,CAACK,SAAS,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACA,SAAS,GAAG,KAAK;IACxB;IAEA,IAAIL,OAAO,CAACM,gBAAgB,EAAE;MAC5B,IAAI,CAACA,gBAAgB,GAAGN,OAAO,CAACM,gBAAgB;IAClD,CAAC,MAAM,IAAIN,OAAO,CAACO,oBAAoB,EAAE;MACvC,IAAI,OAAOP,OAAO,CAACO,oBAAoB,KAAK,QAAQ,EAAE;QACpD,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;MACxE;MACA,IAAI,CAACF,gBAAgB,GAAGN,OAAO,CAACO,oBAAoB;IACtD;EACF;EAEAE,QAAQA,CAACC,QAAQ,EAAE;IACjB,MAAMJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI,UAAUK,SAAS,EAAE;MACrE;MACA;MACA;MACA,MAAMA,SAAS;IACjB,CAAC;IAED,IAAI,IAAI,CAACP,eAAe,EAAE;MACxBM,QAAQ,GAAGE,MAAM,CAACR,eAAe,CAACM,QAAQ,EAAEJ,gBAAgB,CAAC;IAC/D,CAAC,MAAM;MACLI,QAAQ,GAAGG,mBAAmB,CAACH,QAAQ,EAAEJ,gBAAgB,CAAC;IAC5D;IAEA,IAAI,IAAI,CAACD,SAAS,EAAE;MAClBK,QAAQ,GAAGE,MAAM,CAACE,MAAM,CAACJ,QAAQ,CAAC;IACpC;IAEA,MAAMK,EAAE,GAAG,IAAI,CAACd,cAAc,EAAE;IAChC,IAAI,CAACC,SAAS,CAACa,EAAE,CAAC,GAAGL,QAAQ;IAE7B,OAAO;MACLA,QAAQ;MACRM,IAAI,EAAEA,CAAA,KAAM;QACV,OAAO,IAAI,CAACd,SAAS,CAACa,EAAE,CAAC;MAC3B;IACF,CAAC;EACH;EAEAE,KAAKA,CAAA,EAAG;IACN,IAAI,CAAChB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEgB,OAAOA,CAACC,QAAQ,EAAE;IAEhB,MAAMC,GAAG,GAAGxB,MAAM,CAACyB,IAAI,CAAC,IAAI,CAACnB,SAAS,CAAC;IACvC,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAGA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAG,EAAED,CAAC,EAAE;MACrC,MAAMP,EAAE,GAAGK,GAAG,CAACE,CAAC,CAAC;MACjB;MACA,IAAI3B,MAAM,CAAC6B,IAAI,CAAC,IAAI,CAACtB,SAAS,EAAEa,EAAE,CAAC,EAAE;QACnC,MAAML,QAAQ,GAAG,IAAI,CAACR,SAAS,CAACa,EAAE,CAAC;QACnC,IAAI,CAAEI,QAAQ,CAACT,QAAQ,CAAC,EAAE;UACxB;QACF;MACF;IACF;EACF;EAEA,MAAMe,YAAYA,CAACN,QAAQ,EAAE;IAC3B,MAAMC,GAAG,GAAGxB,MAAM,CAACyB,IAAI,CAAC,IAAI,CAACnB,SAAS,CAAC;IACvC,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAGA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAG,EAAED,CAAC,EAAE;MACrC,MAAMP,EAAE,GAAGK,GAAG,CAACE,CAAC,CAAC;MACjB;MACA,IAAI3B,MAAM,CAAC6B,IAAI,CAAC,IAAI,CAACtB,SAAS,EAAEa,EAAE,CAAC,EAAE;QACnC,MAAML,QAAQ,GAAG,IAAI,CAACR,SAAS,CAACa,EAAE,CAAC;QACnC,IAAI,EAAC,MAAMI,QAAQ,CAACT,QAAQ,CAAC,GAAE;UAC7B;QACF;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMe,YAAYA,CAACN,QAAQ,EAAE;IAC3B,MAAMC,GAAG,GAAGxB,MAAM,CAACyB,IAAI,CAAC,IAAI,CAACnB,SAAS,CAAC;IACvC,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAGA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAG,EAAED,CAAC,EAAE;MACrC,MAAMP,EAAE,GAAGK,GAAG,CAACE,CAAC,CAAC;MACjB;MACA,IAAI3B,MAAM,CAAC6B,IAAI,CAAC,IAAI,CAACtB,SAAS,EAAEa,EAAE,CAAC,EAAE;QACnC,MAAML,QAAQ,GAAG,IAAI,CAACR,SAAS,CAACa,EAAE,CAAC;QACnC,IAAI,EAAC,MAAMI,QAAQ,CAACT,QAAQ,CAAC,GAAE;UAC7B;QACF;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;EACEgB,IAAIA,CAACP,QAAQ,EAAE;IACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC;EAC/B;AACF;AAEA;AACA,SAASN,mBAAmBA,CAACc,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAE;EACrD,IAAI,CAACD,WAAW,IAAI,OAAOA,WAAY,KAAK,QAAQ,EAAE;IACpD,MAAME,WAAW,GAAGF,WAAW,IAAI,4BAA4B;IAC/DA,WAAW,GAAG,SAAAA,CAAUG,KAAK,EAAE;MAC7BnB,MAAM,CAACoB,MAAM,CACX,eAAe,GAAGF,WAAW,EAC7BC,KACF,CAAC;IACH,CAAC;EACH;EAEA,OAAO,YAAmB;IACxB,IAAIE,GAAG;IACP,IAAI;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAZ,MAAA,EAFca,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MAGpBL,GAAG,GAAGN,IAAI,CAACY,KAAK,CAACV,KAAK,EAAEO,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOI,CAAC,EAAE;MACVZ,WAAW,CAACY,CAAC,CAAC;IAChB;IACA,OAAOP,GAAG;EACZ,CAAC;AACH,C", "file": "/packages/callback-hook.js", "sourcesContent": ["// XXX This pattern is under development. Do not add more callsites\n// using this package for now. See:\n// https://meteor.hackpad.com/Design-proposal-Hooks-YxvgEW06q6f\n//\n// Encapsulates the pattern of registering callbacks on a hook.\n//\n// The `each` method of the hook calls its iterator function argument\n// with each registered callback.  This allows the hook to\n// conditionally decide not to call the callback (if, for example, the\n// observed object has been closed or terminated).\n//\n// By default, callbacks are bound with `Meteor.bindEnvironment`, so they will be\n// called with the Meteor environment of the calling code that\n// registered the callback. Override by passing { bindEnvironment: false }\n// to the constructor.\n//\n// Registering a callback returns an object with a single `stop`\n// method which unregisters the callback.\n//\n// The code is careful to allow a callback to be safely unregistered\n// while the callbacks are being iterated over.\n//\n// If the hook is configured with the `exceptionHandler` option, the\n// handler will be called if a called callback throws an exception.\n// By default (if the exception handler doesn't itself throw an\n// exception, or if the iterator function doesn't return a falsy value\n// to terminate the calling of callbacks), the remaining callbacks\n// will still be called.\n//\n// Alternatively, the `debugPrintExceptions` option can be specified\n// as string describing the callback.  On an exception the string and\n// the exception will be printed to the console log with\n// `Meteor._debug`, and the exception otherwise ignored.\n//\n// If an exception handler isn't specified, exceptions thrown in the\n// callback will propagate up to the iterator function, and will\n// terminate calling the remaining callbacks if not caught.\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nexport class Hook {\n  constructor(options) {\n    options = options || {};\n    this.nextCallbackId = 0;\n    this.callbacks = Object.create(null);\n    // Whether to wrap callbacks with Meteor.bindEnvironment\n    this.bindEnvironment = true;\n    if (options.bindEnvironment === false) {\n      this.bindEnvironment = false;\n    }\n\n    this.wrapAsync = true;\n    if (options.wrapAsync === false) {\n      this.wrapAsync = false;\n    }\n\n    if (options.exceptionHandler) {\n      this.exceptionHandler = options.exceptionHandler;\n    } else if (options.debugPrintExceptions) {\n      if (typeof options.debugPrintExceptions !== \"string\") {\n        throw new Error(\"Hook option debugPrintExceptions should be a string\");\n      }\n      this.exceptionHandler = options.debugPrintExceptions;\n    }\n  }\n\n  register(callback) {\n    const exceptionHandler = this.exceptionHandler || function (exception) {\n      // Note: this relies on the undocumented fact that if bindEnvironment's\n      // onException throws, and you are invoking the callback either in the\n      // browser or from within a Fiber in Node, the exception is propagated.\n      throw exception;\n    };\n\n    if (this.bindEnvironment) {\n      callback = Meteor.bindEnvironment(callback, exceptionHandler);\n    } else {\n      callback = dontBindEnvironment(callback, exceptionHandler);\n    }\n\n    if (this.wrapAsync) {\n      callback = Meteor.wrapFn(callback);\n    }\n\n    const id = this.nextCallbackId++;\n    this.callbacks[id] = callback;\n\n    return {\n      callback,\n      stop: () => {\n        delete this.callbacks[id];\n      }\n    };\n  }\n\n  clear() {\n    this.nextCallbackId = 0;\n    this.callbacks = [];\n  }\n\n  /**\n   * For each registered callback, call the passed iterator function with the callback.\n   *\n   * The iterator function can choose whether or not to call the\n   * callback.  (For example, it might not call the callback if the\n   * observed object has been closed or terminated).\n   * The iteration is stopped if the iterator function returns a falsy\n   * value or throws an exception.\n   *\n   * @param iterator\n   */\n  forEach(iterator) {\n\n    const ids = Object.keys(this.callbacks);\n    for (let i = 0;  i < ids.length;  ++i) {\n      const id = ids[i];\n      // check to see if the callback was removed during iteration\n      if (hasOwn.call(this.callbacks, id)) {\n        const callback = this.callbacks[id];\n        if (! iterator(callback)) {\n          break;\n        }\n      }\n    }\n  }\n\n  async forEachAsync(iterator) {\n    const ids = Object.keys(this.callbacks);\n    for (let i = 0;  i < ids.length;  ++i) {\n      const id = ids[i];\n      // check to see if the callback was removed during iteration\n      if (hasOwn.call(this.callbacks, id)) {\n        const callback = this.callbacks[id];\n        if (!await iterator(callback)) {\n          break;\n        }\n      }\n    }\n  }\n\n  /**\n   * For each registered callback, call the passed iterator function with the callback.\n   *\n   * it is a counterpart of forEach, but it is async and returns a promise\n   * @param iterator\n   * @return {Promise<void>}\n   * @see forEach\n   */\n  async forEachAsync(iterator) {\n    const ids = Object.keys(this.callbacks);\n    for (let i = 0;  i < ids.length;  ++i) {\n      const id = ids[i];\n      // check to see if the callback was removed during iteration\n      if (hasOwn.call(this.callbacks, id)) {\n        const callback = this.callbacks[id];\n        if (!await iterator(callback)) {\n          break;\n        }\n      }\n    }\n  }\n\n  /**\n   * @deprecated use forEach\n   * @param iterator\n   */\n  each(iterator) {\n    return this.forEach(iterator);\n  }\n}\n\n// Copied from Meteor.bindEnvironment and removed all the env stuff.\nfunction dontBindEnvironment(func, onException, _this) {\n  if (!onException || typeof(onException) === 'string') {\n    const description = onException || \"callback of async function\";\n    onException = function (error) {\n      Meteor._debug(\n        \"Exception in \" + description,\n        error\n      );\n    };\n  }\n\n  return function (...args) {\n    let ret;\n    try {\n      ret = func.apply(_this, args);\n    } catch (e) {\n      onException(e);\n    }\n    return ret;\n  };\n}\n"]}