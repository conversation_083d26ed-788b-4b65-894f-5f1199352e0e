{"version": 3, "sources": ["meteor://💻app/packages/accounts-base/server_main.js", "meteor://💻app/packages/accounts-base/accounts_common.js", "meteor://💻app/packages/accounts-base/accounts_server.js"], "names": ["_objectSpread", "module1", "link", "default", "v", "_Meteor$settings$pack", "_Meteor$settings$pack2", "export", "AccountsServer", "__reifyWaitForDeps__", "Accounts", "Meteor", "server", "settings", "packages", "accounts", "init", "then", "users", "__reify_async_result__", "_reifyError", "self", "async", "module", "Accounts<PERSON><PERSON><PERSON>", "EXPIRE_TOKENS_INTERVAL_MS", "VALID_CONFIG_KEYS", "constructor", "options", "key", "Object", "keys", "includes", "console", "error", "concat", "_options", "connection", "undefined", "_initConnection", "_initializeCollection", "_on<PERSON>ogin<PERSON><PERSON>", "Hook", "bindEnvironment", "debugPrintExceptions", "_onLoginFailureHook", "_onLogoutHook", "DEFAULT_LOGIN_EXPIRATION_DAYS", "LOGIN_UNEXPIRING_TOKEN_DAYS", "lceName", "LoginCancelledError", "makeErrorType", "description", "message", "prototype", "name", "numericError", "collection", "Mongo", "Collection", "Error", "collectionName", "_preventAutopublish", "userId", "_addDefaultFieldSelector", "arguments", "length", "defaultFieldSelector", "fields", "keys2", "user", "isServer", "warn", "join", "findOne", "isClient", "findOneAsync", "userAsync", "config", "__meteor_runtime_config__", "accountsConfigCalled", "_debug", "hasOwnProperty", "call", "Package", "OAuthEncryption", "loadKey", "oauth<PERSON><PERSON><PERSON>", "isTest", "_name", "onLogin", "func", "ret", "register", "_startupCallback", "callback", "onLoginFailure", "onLogout", "ddpUrl", "DDP", "connect", "ACCOUNTS_CONNECTION_URL", "_getTokenLifetimeMs", "loginExpirationInDays", "loginExpiration", "_getPasswordResetTokenLifetimeMs", "passwordResetTokenExpiration", "passwordResetTokenExpirationInDays", "DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS", "_getPasswordEnrollTokenLifetimeMs", "passwordEnrollTokenExpiration", "passwordEnrollTokenExpirationInDays", "DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS", "_tokenExpiration", "when", "Date", "getTime", "_tokenExpiresSoon", "minLifetimeMs", "minLifetimeCapMs", "MIN_TOKEN_LIFETIME_CAP_SECS", "_objectWithoutProperties", "_asyncIterator", "_Package$oauthEncryp", "_excluded", "crypto", "URL", "hasOwn", "NonEmptyString", "Match", "Where", "x", "check", "String", "_this", "this", "onCreateLoginToken", "_onCreateLoginTokenHook", "_selectorForFastCaseInsensitiveLookup", "fieldName", "string", "prefix", "substring", "Math", "min", "or<PERSON><PERSON><PERSON>", "generateCasePermutationsForString", "map", "prefixPermutation", "selector", "RegExp", "_escapeRegExp", "caseInsensitiveClause", "$and", "$or", "_findUserByQuery", "query", "id", "fieldValue", "username", "email", "candidateUsers", "find", "limit", "fetchAsync", "_handleError", "msg", "_this$_options$ambigu", "throwError", "errorCode", "isErrorAmbiguous", "ambiguousErrorMessages", "_userQueryValidator", "Optional", "_server", "_initServerMethods", "_initAccountDataHooks", "_autopublishFields", "loggedInUser", "otherUsers", "_defaultPublishFields", "projection", "profile", "emails", "_initServerPublications", "_accountData", "_userObservesForConnections", "_nextUserObserveNumber", "_loginHandlers", "setupDefaultLoginHandlers", "setExpireTokensInterval", "_validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "_validateNewUserHooks", "defaultValidateNewUserHook", "bind", "_deleteSavedTokensForAllUsersOnStartup", "_skipCaseInsensitiveChecksForTest", "urls", "resetPassword", "token", "extraParams", "buildEmailUrl", "verifyEmail", "loginToken", "enrollAccount", "addDefaultRateLimit", "path", "url", "absoluteUrl", "params", "entries", "value", "searchParams", "append", "toString", "currentInvocation", "_CurrentMethodInvocation", "get", "_CurrentPublicationInvocation", "setupUsersCollection", "validateLoginAttempt", "validateNewUser", "push", "beforeExternal<PERSON><PERSON>in", "_beforeExternalLoginHook", "onCreateUser", "_onCreateUserHook", "wrapFn", "onExternalLogin", "_onExternalLoginHook", "setAdditionalFindUserOnExternalLogin", "_additionalFindUserOnExternalLogin", "_validate<PERSON>ogin", "attempt", "forEachAsync", "cloneAttemptWithConnection", "e", "allowed", "_successfulLogin", "_failedLogin", "_successfulLogout", "_loginUser", "methodInvocation", "stampedLoginToken", "_generateStampedLoginToken", "_insertLoginToken", "_noYieldsAllowed", "_setLoginToken", "_hashLoginToken", "setUserId", "tokenExpires", "_attemptLogin", "methodName", "methodArgs", "result", "type", "methodArguments", "Array", "from", "o", "_loginMethod", "fn", "tryLoginMethod", "_reportLoginFailure", "registerLoginHandler", "handler", "_runLoginHandlers", "destroyToken", "updateAsync", "$pull", "hashedToken", "methods", "login", "logout", "_getLoginToken", "getNewToken", "currentHashedToken", "currentStampedToken", "services", "resume", "loginTokens", "stampedToken", "newStampedToken", "removeOtherTokens", "currentToken", "$ne", "configureLoginService", "ObjectIncluding", "service", "o<PERSON>h", "serviceNames", "ServiceConfiguration", "configurations", "keyIsLoaded", "secret", "seal", "insertAsync", "onConnection", "onClose", "_removeTokenFromConnection", "publish", "ready", "is_auto", "startup", "customFields", "_id", "autopublish", "toFieldSelector", "reduce", "prev", "field", "addAutopublishFields", "opts", "apply", "forLoggedInUser", "forOtherUsers", "setDefaultPublishFields", "_getAccountData", "connectionId", "data", "_setAccountData", "hash", "createHash", "update", "digest", "_hashStampedToken", "hashedStampedToken", "_insertHashed<PERSON><PERSON>inToken", "$addToSet", "_clearAllLoginTokens", "$set", "_getUserObserve", "observe", "stop", "newToken", "myObserveNumber", "defer", "foundMatchingUser", "observe<PERSON>hanges", "added", "removed", "close", "nonMutatingCallbacks", "Random", "_expirePasswordResetTokens", "oldestValidDate", "tokenLifetimeMs", "tokenFilter", "$exists", "expirePasswordToken", "_expirePasswordEnrollTokens", "_expireTokens", "userFilter", "$lt", "multi", "superResult", "expireTokenInterval", "clearInterval", "insertUserDoc", "createdAt", "for<PERSON>ach", "pinEncry<PERSON>FieldsToUser", "fullUser", "defaultCreateUserHook", "_iteratorAbruptCompletion", "_didIteratorError", "_iteratorError", "_iterator", "_step", "next", "done", "hook", "err", "return", "errmsg", "_testEmailDomain", "domain", "restrictCreationByEmailDomain", "test", "_deleteSavedTokensForUser", "tokensToDelete", "$unset", "$pullAll", "loginTokensToDelete", "_", "catch", "log", "updateOrCreateUserFromExternalService", "serviceName", "serviceData", "serviceIdKey", "isNaN", "parseInt", "setAttrs", "removeDefaultRateLimit", "resp", "DDPRateLimiter", "removeRule", "defaultRateLimiterRuleId", "addRule", "clientAddress", "generateOptionsForEmail", "reason", "extra", "to", "emailTemplates", "subject", "text", "html", "headers", "_checkForCaseInsensitiveDuplicates", "displayName", "ownUserId", "<PERSON><PERSON><PERSON><PERSON>", "matchedUsers", "_createUserCheckingDuplicates", "_ref", "newUser", "address", "verified", "ex", "removeAsync", "clonedAtte<PERSON>", "EJSON", "clone", "defaultResumeLoginHandler", "oldUnhashedStyleToken", "isEnroll", "resetRangeOr", "expireFilter", "setInterval", "isSealed", "open", "emailIsGood", "values", "allow", "modifier", "fetch", "createIndexAsync", "unique", "sparse", "permutations", "i", "ch", "char<PERSON>t", "lowerCaseChar", "toLowerCase", "upperCaseChar", "toUpperCase"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAAC,qBAAA,EAAAC,sBAAA;IAAvGL,OAAO,CAACM,MAAM,CAAC;MAACC,cAAc,EAACA,CAAA,KAAIA;IAAc,CAAC,CAAC;IAAC,IAAIA,cAAc;IAACP,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACM,cAAcA,CAACJ,CAAC,EAAC;QAACI,cAAc,GAACJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIK,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEhN;AACA;AACA;AACA;IACAC,QAAQ,GAAG,IAAIF,cAAc,CAACG,MAAM,CAACC,MAAM,EAAAZ,aAAA,CAAAA,aAAA,MAAAK,qBAAA,GAAOM,MAAM,CAACE,QAAQ,CAACC,QAAQ,cAAAT,qBAAA,uBAAxBA,qBAAA,CAA0BU,QAAQ,IAAAT,sBAAA,GAAKK,MAAM,CAACE,QAAQ,CAACC,QAAQ,cAAAR,sBAAA,uBAAxBA,sBAAA,CAA2B,eAAe,CAAC,CAAE,CAAC;IACvI;IACAI,QAAQ,CAACM,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IACtB;IACA;IACA;;IAEA;AACA;AACA;AACA;AACA;AACA;IACAN,MAAM,CAACO,KAAK,GAAGR,QAAQ,CAACQ,KAAK;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICnB9B,IAAItB,aAAa;IAACuB,MAAM,CAACrB,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGmB,MAAM,CAAChB,MAAM,CAAC;MAACiB,cAAc,EAACA,CAAA,KAAIA,cAAc;MAACC,yBAAyB,EAACA,CAAA,KAAIA;IAAyB,CAAC,CAAC;IAAC,IAAId,MAAM;IAACY,MAAM,CAACrB,IAAI,CAAC,eAAe,EAAC;MAACS,MAAMA,CAACP,CAAC,EAAC;QAACO,MAAM,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIK,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEvO;IACA,MAAMiB,iBAAiB,GAAG,CACxB,uBAAuB,EACvB,6BAA6B,EAC7B,+BAA+B,EAC/B,iBAAiB,EACjB,uBAAuB,EACvB,gBAAgB,EAChB,oCAAoC,EACpC,8BAA8B,EAC9B,qCAAqC,EACrC,+BAA+B,EAC/B,wBAAwB,EACxB,cAAc,EACd,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,YAAY,EACZ,2BAA2B,EAC3B,qBAAqB,EACrB,eAAe,EACf,QAAQ,EACR,YAAY,CACb;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACO,MAAMF,cAAc,CAAC;MAC1BG,WAAWA,CAACC,OAAO,EAAE;QACnB;QACA,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,EAAE;UACtC,IAAI,CAACF,iBAAiB,CAACM,QAAQ,CAACH,GAAG,CAAC,EAAE;YACpCI,OAAO,CAACC,KAAK,kCAAAC,MAAA,CAAkCN,GAAG,CAAE,CAAC;UACvD;QACF;;QAEA;QACA;QACA,IAAI,CAACO,QAAQ,GAAGR,OAAO,IAAI,CAAC,CAAC;;QAE7B;QACA;QACA,IAAI,CAACS,UAAU,GAAGC,SAAS;QAC3B,IAAI,CAACC,eAAe,CAACX,OAAO,IAAI,CAAC,CAAC,CAAC;;QAEnC;QACA;QACA,IAAI,CAACV,KAAK,GAAG,IAAI,CAACsB,qBAAqB,CAACZ,OAAO,IAAI,CAAC,CAAC,CAAC;;QAEtD;QACA,IAAI,CAACa,YAAY,GAAG,IAAIC,IAAI,CAAC;UAC3BC,eAAe,EAAE,KAAK;UACtBC,oBAAoB,EAAE;QACxB,CAAC,CAAC;QAEF,IAAI,CAACC,mBAAmB,GAAG,IAAIH,IAAI,CAAC;UAClCC,eAAe,EAAE,KAAK;UACtBC,oBAAoB,EAAE;QACxB,CAAC,CAAC;QAEF,IAAI,CAACE,aAAa,GAAG,IAAIJ,IAAI,CAAC;UAC5BC,eAAe,EAAE,KAAK;UACtBC,oBAAoB,EAAE;QACxB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACG,6BAA6B,GAAGA,6BAA6B;QAClE,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;;QAE9D;QACA;QACA,MAAMC,OAAO,GAAG,8BAA8B;QAC9C,IAAI,CAACC,mBAAmB,GAAGvC,MAAM,CAACwC,aAAa,CAACF,OAAO,EAAE,UACvDG,WAAW,EACX;UACA,IAAI,CAACC,OAAO,GAAGD,WAAW;QAC5B,CAAC,CAAC;QACF,IAAI,CAACF,mBAAmB,CAACI,SAAS,CAACC,IAAI,GAAGN,OAAO;;QAEjD;QACA;QACA;QACA,IAAI,CAACC,mBAAmB,CAACM,YAAY,GAAG,SAAS;MACnD;MAEAhB,qBAAqBA,CAACZ,OAAO,EAAE;QAC7B,IAAIA,OAAO,CAAC6B,UAAU,IAAI,OAAO7B,OAAO,CAAC6B,UAAU,KAAK,QAAQ,IAAI,EAAE7B,OAAO,CAAC6B,UAAU,YAAYC,KAAK,CAACC,UAAU,CAAC,EAAE;UACrH,MAAM,IAAIhD,MAAM,CAACiD,KAAK,CAAC,uEAAuE,CAAC;QACjG;QAEA,IAAIC,cAAc,GAAG,OAAO;QAC5B,IAAI,OAAOjC,OAAO,CAAC6B,UAAU,KAAK,QAAQ,EAAE;UAC1CI,cAAc,GAAGjC,OAAO,CAAC6B,UAAU;QACrC;QAEA,IAAIA,UAAU;QACd,IAAI7B,OAAO,CAAC6B,UAAU,YAAYC,KAAK,CAACC,UAAU,EAAE;UAClDF,UAAU,GAAG7B,OAAO,CAAC6B,UAAU;QACjC,CAAC,MAAM;UACLA,UAAU,GAAG,IAAIC,KAAK,CAACC,UAAU,CAACE,cAAc,EAAE;YAChDC,mBAAmB,EAAE,IAAI;YACzBzB,UAAU,EAAE,IAAI,CAACA;UACnB,CAAC,CAAC;QACJ;QAEA,OAAOoB,UAAU;MACnB;;MAEA;AACF;AACA;AACA;MACEM,MAAMA,CAAA,EAAG;QACP,MAAM,IAAIH,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACAI,wBAAwBA,CAAA,EAAe;QAAA,IAAdpC,OAAO,GAAAqC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA3B,SAAA,GAAA2B,SAAA,MAAG,CAAC,CAAC;QACnC;QACA,IAAI,CAAC,IAAI,CAAC7B,QAAQ,CAAC+B,oBAAoB,EAAE,OAAOvC,OAAO;;QAEvD;QACA,IAAI,CAACA,OAAO,CAACwC,MAAM,EACjB,OAAApE,aAAA,CAAAA,aAAA,KACK4B,OAAO;UACVwC,MAAM,EAAE,IAAI,CAAChC,QAAQ,CAAC+B;QAAoB;;QAG9C;QACA,MAAMpC,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACH,OAAO,CAACwC,MAAM,CAAC;QACxC,IAAI,CAACrC,IAAI,CAACmC,MAAM,EAAE,OAAOtC,OAAO;;QAEhC;QACA;QACA,IAAI,CAAC,CAACA,OAAO,CAACwC,MAAM,CAACrC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,OAAOH,OAAO;;QAE7C;QACA;QACA,MAAMyC,KAAK,GAAGvC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACK,QAAQ,CAAC+B,oBAAoB,CAAC;QAC7D,OAAO,IAAI,CAAC/B,QAAQ,CAAC+B,oBAAoB,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,GAC/CzC,OAAO,GAAA5B,aAAA,CAAAA,aAAA,KAEF4B,OAAO;UACVwC,MAAM,EAAApE,aAAA,CAAAA,aAAA,KACD4B,OAAO,CAACwC,MAAM,GACd,IAAI,CAAChC,QAAQ,CAAC+B,oBAAoB;QACtC,EACF;MACP;;MAEA;AACF;AACA;AACA;AACA;AACA;MACEG,IAAIA,CAAC1C,OAAO,EAAE;QACZ,IAAIjB,MAAM,CAAC4D,QAAQ,EAAE;UACnBtC,OAAO,CAACuC,IAAI,CAAC,CACX,mDAAmD,EACnD,qDAAqD,EACrD,uCAAuC,CACxC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QACf;QAEA,MAAMpD,IAAI,GAAG,IAAI;QACjB,MAAM0C,MAAM,GAAG1C,IAAI,CAAC0C,MAAM,CAAC,CAAC;QAC5B,MAAMW,OAAO,GAAG,SAAAA,CAAA;UAAA,OAAa/D,MAAM,CAACgE,QAAQ,GACxCtD,IAAI,CAACH,KAAK,CAACwD,OAAO,CAAC,GAAAT,SAAO,CAAC,GAC3B5C,IAAI,CAACH,KAAK,CAAC0D,YAAY,CAAC,GAAAX,SAAO,CAAC;QAAA;QACpC,OAAOF,MAAM,GACTW,OAAO,CAACX,MAAM,EAAE,IAAI,CAACC,wBAAwB,CAACpC,OAAO,CAAC,CAAC,GACvD,IAAI;MACV;;MAEA;AACF;AACA;AACA;AACA;AACA;MACE,MAAMiD,SAASA,CAACjD,OAAO,EAAE;QACvB,MAAMmC,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;QAC5B,OAAOA,MAAM,GACT,IAAI,CAAC7C,KAAK,CAAC0D,YAAY,CAACb,MAAM,EAAE,IAAI,CAACC,wBAAwB,CAACpC,OAAO,CAAC,CAAC,GACvE,IAAI;MACV;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEkD,MAAMA,CAAClD,OAAO,EAAE;QACd;QACA;QACA;QACA;QACA;QACA,IAAIjB,MAAM,CAAC4D,QAAQ,EAAE;UACnBQ,yBAAyB,CAACC,oBAAoB,GAAG,IAAI;QACvD,CAAC,MAAM,IAAI,CAACD,yBAAyB,CAACC,oBAAoB,EAAE;UAC1D;UACA;UACArE,MAAM,CAACsE,MAAM,CACX,0DAA0D,GACxD,yDACJ,CAAC;QACH;;QAEA;QACA;QACA;QACA,IAAInD,MAAM,CAACwB,SAAS,CAAC4B,cAAc,CAACC,IAAI,CAACvD,OAAO,EAAE,gBAAgB,CAAC,EAAE;UACnE,IAAIjB,MAAM,CAACgE,QAAQ,EAAE;YACnB,MAAM,IAAIf,KAAK,CACb,+DACF,CAAC;UACH;UACA,IAAI,CAACwB,OAAO,CAAC,kBAAkB,CAAC,EAAE;YAChC,MAAM,IAAIxB,KAAK,CACb,mEACF,CAAC;UACH;UACAwB,OAAO,CAAC,kBAAkB,CAAC,CAACC,eAAe,CAACC,OAAO,CACjD1D,OAAO,CAAC2D,cACV,CAAC;UACD3D,OAAO,GAAA5B,aAAA,KAAQ4B,OAAO,CAAE;UACxB,OAAOA,OAAO,CAAC2D,cAAc;QAC/B;;QAEA;QACA,KAAK,MAAM1D,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,EAAE;UACtC,IAAI,CAACF,iBAAiB,CAACM,QAAQ,CAACH,GAAG,CAAC,EAAE;YACpCI,OAAO,CAACC,KAAK,kCAAAC,MAAA,CAAkCN,GAAG,CAAE,CAAC;UACvD;QACF;;QAEA;QACA,KAAK,MAAMA,GAAG,IAAIH,iBAAiB,EAAE;UACnC,IAAIG,GAAG,IAAID,OAAO,EAAE;YAClB,IAAIC,GAAG,IAAI,IAAI,CAACO,QAAQ,EAAE;cACxB,IAAIP,GAAG,KAAK,YAAY,IAAKlB,MAAM,CAAC6E,MAAM,IAAI3D,GAAG,KAAK,eAAgB,EAAE;gBACtE,MAAM,IAAIlB,MAAM,CAACiD,KAAK,eAAAzB,MAAA,CAAgBN,GAAG,qBAAmB,CAAC;cAC/D;YACF;YACA,IAAI,CAACO,QAAQ,CAACP,GAAG,CAAC,GAAGD,OAAO,CAACC,GAAG,CAAC;UACnC;QACF;QAEA,IAAID,OAAO,CAAC6B,UAAU,IAAI7B,OAAO,CAAC6B,UAAU,KAAK,IAAI,CAACvC,KAAK,CAACuE,KAAK,IAAI7D,OAAO,CAAC6B,UAAU,KAAK,IAAI,CAACvC,KAAK,EAAE;UACtG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACsB,qBAAqB,CAACZ,OAAO,CAAC;QAClD;MACF;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE8D,OAAOA,CAACC,IAAI,EAAE;QACZ,IAAIC,GAAG,GAAG,IAAI,CAACnD,YAAY,CAACoD,QAAQ,CAACF,IAAI,CAAC;QAC1C;QACA,IAAI,CAACG,gBAAgB,CAACF,GAAG,CAACG,QAAQ,CAAC;QACnC,OAAOH,GAAG;MACZ;;MAEA;AACF;AACA;AACA;AACA;MACEI,cAAcA,CAACL,IAAI,EAAE;QACnB,OAAO,IAAI,CAAC9C,mBAAmB,CAACgD,QAAQ,CAACF,IAAI,CAAC;MAChD;;MAEA;AACF;AACA;AACA;AACA;MACEM,QAAQA,CAACN,IAAI,EAAE;QACb,OAAO,IAAI,CAAC7C,aAAa,CAAC+C,QAAQ,CAACF,IAAI,CAAC;MAC1C;MAEApD,eAAeA,CAACX,OAAO,EAAE;QACvB,IAAI,CAACjB,MAAM,CAACgE,QAAQ,EAAE;UACpB;QACF;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI/C,OAAO,CAACS,UAAU,EAAE;UACtB,IAAI,CAACA,UAAU,GAAGT,OAAO,CAACS,UAAU;QACtC,CAAC,MAAM,IAAIT,OAAO,CAACsE,MAAM,EAAE;UACzB,IAAI,CAAC7D,UAAU,GAAG8D,GAAG,CAACC,OAAO,CAACxE,OAAO,CAACsE,MAAM,CAAC;QAC/C,CAAC,MAAM,IACL,OAAOnB,yBAAyB,KAAK,WAAW,IAChDA,yBAAyB,CAACsB,uBAAuB,EACjD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAAChE,UAAU,GAAG8D,GAAG,CAACC,OAAO,CAC3BrB,yBAAyB,CAACsB,uBAC5B,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAAChE,UAAU,GAAG1B,MAAM,CAAC0B,UAAU;QACrC;MACF;MAEAiE,mBAAmBA,CAAA,EAAG;QACpB;QACA;QACA;QACA,MAAMC,qBAAqB,GACzB,IAAI,CAACnE,QAAQ,CAACmE,qBAAqB,KAAK,IAAI,GACxCvD,2BAA2B,GAC3B,IAAI,CAACZ,QAAQ,CAACmE,qBAAqB;QACzC,OACE,IAAI,CAACnE,QAAQ,CAACoE,eAAe,IAC7B,CAACD,qBAAqB,IAAIxD,6BAA6B,IAAI,QAAQ;MAEvE;MAEA0D,gCAAgCA,CAAA,EAAG;QACjC,OACE,IAAI,CAACrE,QAAQ,CAACsE,4BAA4B,IAC1C,CAAC,IAAI,CAACtE,QAAQ,CAACuE,kCAAkC,IAC/CC,4CAA4C,IAAI,QAAQ;MAE9D;MAEAC,iCAAiCA,CAAA,EAAG;QAClC,OACE,IAAI,CAACzE,QAAQ,CAAC0E,6BAA6B,IAC3C,CAAC,IAAI,CAAC1E,QAAQ,CAAC2E,mCAAmC,IAChDC,6CAA6C,IAAI,QAAQ;MAE/D;MAEAC,gBAAgBA,CAACC,IAAI,EAAE;QACrB;QACA;QACA,OAAO,IAAIC,IAAI,CAAC,IAAIA,IAAI,CAACD,IAAI,CAAC,CAACE,OAAO,CAAC,CAAC,GAAG,IAAI,CAACd,mBAAmB,CAAC,CAAC,CAAC;MACxE;MAEAe,iBAAiBA,CAACH,IAAI,EAAE;QACtB,IAAII,aAAa,GAAG,GAAG,GAAG,IAAI,CAAChB,mBAAmB,CAAC,CAAC;QACpD,MAAMiB,gBAAgB,GAAGC,2BAA2B,GAAG,IAAI;QAC3D,IAAIF,aAAa,GAAGC,gBAAgB,EAAE;UACpCD,aAAa,GAAGC,gBAAgB;QAClC;QACA,OAAO,IAAIJ,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAACD,IAAI,CAAC,GAAGI,aAAa;MACpD;;MAEA;MACAxB,gBAAgBA,CAACC,QAAQ,EAAE,CAAC;IAC9B;IAEA;IACA;;IAEA;AACA;AACA;AACA;AACA;IACApF,MAAM,CAACoD,MAAM,GAAG,MAAMrD,QAAQ,CAACqD,MAAM,CAAC,CAAC;;IAEvC;AACA;AACA;AACA;AACA;AACA;AACA;IACApD,MAAM,CAAC2D,IAAI,GAAG1C,OAAO,IAAIlB,QAAQ,CAAC4D,IAAI,CAAC1C,OAAO,CAAC;;IAE/C;AACA;AACA;AACA;AACA;AACA;AACA;IACAjB,MAAM,CAACkE,SAAS,GAAGjD,OAAO,IAAIlB,QAAQ,CAACmE,SAAS,CAACjD,OAAO,CAAC;;IAEzD;IACA,MAAMmB,6BAA6B,GAAG,EAAE;IACxC;IACA,MAAM6D,4CAA4C,GAAG,CAAC;IACtD;IACA,MAAMI,6CAA6C,GAAG,EAAE;IACxD;IACA;IACA;IACA,MAAMQ,2BAA2B,GAAG,IAAI,CAAC,CAAC;IAC1C;IACO,MAAM/F,yBAAyB,GAAG,GAAG,GAAG,IAAI;IAAE;IACrD;IACA;IACA,MAAMuB,2BAA2B,GAAG,GAAG,GAAG,GAAG;IAAC7B,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICrc9C,IAAImG,wBAAwB;IAAClG,MAAM,CAACrB,IAAI,CAAC,gDAAgD,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACqH,wBAAwB,GAACrH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIJ,aAAa;IAACuB,MAAM,CAACrB,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIsH,cAAc;IAACnG,MAAM,CAACrB,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACsH,cAAc,GAACtH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAAuH,oBAAA;IAAA,MAAAC,SAAA;IAApVrG,MAAM,CAAChB,MAAM,CAAC;MAACC,cAAc,EAACA,CAAA,KAAIA;IAAc,CAAC,CAAC;IAAC,IAAIqH,MAAM;IAACtG,MAAM,CAACrB,IAAI,CAAC,QAAQ,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACyH,MAAM,GAACzH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIO,MAAM;IAACY,MAAM,CAACrB,IAAI,CAAC,eAAe,EAAC;MAACS,MAAMA,CAACP,CAAC,EAAC;QAACO,MAAM,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIoB,cAAc,EAACC,yBAAyB;IAACF,MAAM,CAACrB,IAAI,CAAC,sBAAsB,EAAC;MAACsB,cAAcA,CAACpB,CAAC,EAAC;QAACoB,cAAc,GAACpB,CAAC;MAAA,CAAC;MAACqB,yBAAyBA,CAACrB,CAAC,EAAC;QAACqB,yBAAyB,GAACrB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI0H,GAAG;IAACvG,MAAM,CAACrB,IAAI,CAAC,YAAY,EAAC;MAAC4H,GAAGA,CAAC1H,CAAC,EAAC;QAAC0H,GAAG,GAAC1H,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIK,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAQhd,MAAMsH,MAAM,GAAGjG,MAAM,CAACwB,SAAS,CAAC4B,cAAc;;IAE9C;IACA,MAAM8C,cAAc,GAAGC,KAAK,CAACC,KAAK,CAACC,CAAC,IAAI;MACtCC,KAAK,CAACD,CAAC,EAAEE,MAAM,CAAC;MAChB,OAAOF,CAAC,CAACjE,MAAM,GAAG,CAAC;IACrB,CAAC,CAAC;;IAGF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACO,MAAM1D,cAAc,SAASgB,cAAc,CAAC;MACjD;MACA;MACA;MACAG,WAAWA,CAACf,MAAM,EAAEgB,QAAO,EAAE;QAAA,IAAA0G,KAAA;QAC3B,KAAK,CAAC1G,QAAO,IAAI,CAAC,CAAC,CAAC;QAAA0G,KAAA,GAAAC,IAAA;QAyItB;QACA;QACA;QAEA;AACF;AACA;AACA;AACA;AACA;QALE,KAMAC,kBAAkB,GAAG,UAAS7C,IAAI,EAAE;UAClC,IAAI,IAAI,CAAC8C,uBAAuB,EAAE;YAChC,MAAM,IAAI7E,KAAK,CAAC,uCAAuC,CAAC;UAC1D;UAEA,IAAI,CAAC6E,uBAAuB,GAAG9C,IAAI;QACrC,CAAC;QA2FD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA,KACA+C,qCAAqC,GAAG,CAACC,SAAS,EAAEC,MAAM,KAAK;UAC7D;UACA,MAAMC,MAAM,GAAGD,MAAM,CAACE,SAAS,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAAC1E,MAAM,EAAE,CAAC,CAAC,CAAC;UAC9D,MAAM+E,QAAQ,GAAGC,iCAAiC,CAACL,MAAM,CAAC,CAACM,GAAG,CAC1DC,iBAAiB,IAAI;YACnB,MAAMC,QAAQ,GAAG,CAAC,CAAC;YACnBA,QAAQ,CAACV,SAAS,CAAC,GACf,IAAIW,MAAM,KAAAnH,MAAA,CAAKxB,MAAM,CAAC4I,aAAa,CAACH,iBAAiB,CAAC,CAAE,CAAC;YAC7D,OAAOC,QAAQ;UACjB,CAAC,CAAC;UACN,MAAMG,qBAAqB,GAAG,CAAC,CAAC;UAChCA,qBAAqB,CAACb,SAAS,CAAC,GAC5B,IAAIW,MAAM,KAAAnH,MAAA,CAAKxB,MAAM,CAAC4I,aAAa,CAACX,MAAM,CAAC,QAAK,GAAG,CAAC;UACxD,OAAO;YAACa,IAAI,EAAE,CAAC;cAACC,GAAG,EAAET;YAAQ,CAAC,EAAEO,qBAAqB;UAAC,CAAC;QACzD,CAAC;QAAA,KAEDG,gBAAgB,GAAG,OAAOC,KAAK,EAAEhI,OAAO,KAAK;UAC3C,IAAI0C,IAAI,GAAG,IAAI;UAEf,IAAIsF,KAAK,CAACC,EAAE,EAAE;YACZ;YACAvF,IAAI,GAAG,MAAM3D,MAAM,CAACO,KAAK,CAAC0D,YAAY,CAACgF,KAAK,CAACC,EAAE,EAAE,IAAI,CAAC7F,wBAAwB,CAACpC,OAAO,CAAC,CAAC;UAC1F,CAAC,MAAM;YACLA,OAAO,GAAG,IAAI,CAACoC,wBAAwB,CAACpC,OAAO,CAAC;YAChD,IAAI+G,SAAS;YACb,IAAImB,UAAU;YACd,IAAIF,KAAK,CAACG,QAAQ,EAAE;cAClBpB,SAAS,GAAG,UAAU;cACtBmB,UAAU,GAAGF,KAAK,CAACG,QAAQ;YAC7B,CAAC,MAAM,IAAIH,KAAK,CAACI,KAAK,EAAE;cACtBrB,SAAS,GAAG,gBAAgB;cAC5BmB,UAAU,GAAGF,KAAK,CAACI,KAAK;YAC1B,CAAC,MAAM;cACL,MAAM,IAAIpG,KAAK,CAAC,gDAAgD,CAAC;YACnE;YACA,IAAIyF,QAAQ,GAAG,CAAC,CAAC;YACjBA,QAAQ,CAACV,SAAS,CAAC,GAAGmB,UAAU;YAChCxF,IAAI,GAAG,MAAM3D,MAAM,CAACO,KAAK,CAAC0D,YAAY,CAACyE,QAAQ,EAAEzH,OAAO,CAAC;YACzD;YACA,IAAI,CAAC0C,IAAI,EAAE;cACT+E,QAAQ,GAAG,IAAI,CAACX,qCAAqC,CAACC,SAAS,EAAEmB,UAAU,CAAC;cAC5E,MAAMG,cAAc,GAAG,MAAMtJ,MAAM,CAACO,KAAK,CAACgJ,IAAI,CAACb,QAAQ,EAAArJ,aAAA,CAAAA,aAAA,KAAO4B,OAAO;gBAAEuI,KAAK,EAAE;cAAC,EAAE,CAAC,CAACC,UAAU,CAAC,CAAC;cAC/F;cACA,IAAIH,cAAc,CAAC/F,MAAM,KAAK,CAAC,EAAE;gBAC/BI,IAAI,GAAG2F,cAAc,CAAC,CAAC,CAAC;cAC1B;YACF;UACF;UAEA,OAAO3F,IAAI;QACb,CAAC;QAAA,KAmqCD+F,YAAY,GAAG,UAACC,GAAG,EAAyC;UAAA,IAAAC,qBAAA;UAAA,IAAvCC,UAAU,GAAAvG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA3B,SAAA,GAAA2B,SAAA,MAAG,IAAI;UAAA,IAAEwG,SAAS,GAAAxG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA3B,SAAA,GAAA2B,SAAA,MAAG,GAAG;UACrD,MAAMyG,gBAAgB,IAAAH,qBAAA,GAAGjC,KAAI,CAAClG,QAAQ,CAACuI,sBAAsB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;UACrE,MAAMrI,KAAK,GAAG,IAAIvB,MAAM,CAACiD,KAAK,CAC5B6G,SAAS,EACTC,gBAAgB,GACZ,sDAAsD,GACtDJ,GACN,CAAC;UACD,IAAIE,UAAU,EAAE;YACd,MAAMtI,KAAK;UACb;UACA,OAAOA,KAAK;QACd,CAAC;QAAA,KAED0I,mBAAmB,GAAG3C,KAAK,CAACC,KAAK,CAAC5D,IAAI,IAAI;UACxC8D,KAAK,CAAC9D,IAAI,EAAE;YACVuF,EAAE,EAAE5B,KAAK,CAAC4C,QAAQ,CAAC7C,cAAc,CAAC;YAClC+B,QAAQ,EAAE9B,KAAK,CAAC4C,QAAQ,CAAC7C,cAAc,CAAC;YACxCgC,KAAK,EAAE/B,KAAK,CAAC4C,QAAQ,CAAC7C,cAAc;UACtC,CAAC,CAAC;UACF,IAAIlG,MAAM,CAACC,IAAI,CAACuC,IAAI,CAAC,CAACJ,MAAM,KAAK,CAAC,EAChC,MAAM,IAAI+D,KAAK,CAACrE,KAAK,CAAC,2CAA2C,CAAC;UACpE,OAAO,IAAI;QACb,CAAC,CAAC;QAv+CA,IAAI,CAACkH,OAAO,GAAGlK,MAAM,IAAID,MAAM,CAACC,MAAM;QACtC;QACA,IAAI,CAACmK,kBAAkB,CAAC,CAAC;QAEzB,IAAI,CAACC,qBAAqB,CAAC,CAAC;;QAE5B;QACA;QACA;QACA;QACA;QACA,IAAI,CAACC,kBAAkB,GAAG;UACxBC,YAAY,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;UAC/CC,UAAU,EAAE,CAAC,SAAS,EAAE,UAAU;QACpC,CAAC;;QAED;QACA;QACA;QACA,IAAI,CAACC,qBAAqB,GAAG;UAC3BC,UAAU,EAAE;YACVC,OAAO,EAAE,CAAC;YACVvB,QAAQ,EAAE,CAAC;YACXwB,MAAM,EAAE;UACV;QACF,CAAC;QAED,IAAI,CAACC,uBAAuB,CAAC,CAAC;;QAE9B;QACA,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;;QAEtB;QACA;QACA;QACA;QACA;QACA,IAAI,CAACC,2BAA2B,GAAG,CAAC,CAAC;QACrC,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC,CAAE;;QAElC;QACA,IAAI,CAACC,cAAc,GAAG,EAAE;QACxBC,yBAAyB,CAAC,IAAI,CAAC;QAC/BC,uBAAuB,CAAC,IAAI,CAAC;QAE7B,IAAI,CAACC,kBAAkB,GAAG,IAAIrJ,IAAI,CAAC;UAAEC,eAAe,EAAE;QAAM,CAAC,CAAC;QAC9D,IAAI,CAACqJ,qBAAqB,GAAG,CAC3BC,0BAA0B,CAACC,IAAI,CAAC,IAAI,CAAC,CACtC;QAED,IAAI,CAACC,sCAAsC,CAAC,CAAC;QAE7C,IAAI,CAACC,iCAAiC,GAAG,CAAC,CAAC;QAE3C,IAAI,CAACC,IAAI,GAAG;UACVC,aAAa,EAAEA,CAACC,KAAK,EAAEC,WAAW,KAAK,IAAI,CAACC,aAAa,qBAAAtK,MAAA,CAAqBoK,KAAK,GAAIC,WAAW,CAAC;UACnGE,WAAW,EAAEA,CAACH,KAAK,EAAEC,WAAW,KAAK,IAAI,CAACC,aAAa,mBAAAtK,MAAA,CAAmBoK,KAAK,GAAIC,WAAW,CAAC;UAC/FG,UAAU,EAAEA,CAACtD,QAAQ,EAAEkD,KAAK,EAAEC,WAAW,KACvC,IAAI,CAACC,aAAa,iBAAAtK,MAAA,CAAiBoK,KAAK,gBAAApK,MAAA,CAAakH,QAAQ,GAAImD,WAAW,CAAC;UAC/EI,aAAa,EAAEA,CAACL,KAAK,EAAEC,WAAW,KAAK,IAAI,CAACC,aAAa,qBAAAtK,MAAA,CAAqBoK,KAAK,GAAIC,WAAW;QACpG,CAAC;QAED,IAAI,CAACK,mBAAmB,CAAC,CAAC;QAE1B,IAAI,CAACJ,aAAa,GAAG,UAACK,IAAI,EAAuB;UAAA,IAArBN,WAAW,GAAAvI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA3B,SAAA,GAAA2B,SAAA,MAAG,CAAC,CAAC;UAC1C,MAAM8I,GAAG,GAAG,IAAIjF,GAAG,CAACnH,MAAM,CAACqM,WAAW,CAACF,IAAI,CAAC,CAAC;UAC7C,MAAMG,MAAM,GAAGnL,MAAM,CAACoL,OAAO,CAACV,WAAW,CAAC;UAC1C,IAAIS,MAAM,CAAC/I,MAAM,GAAG,CAAC,EAAE;YACrB;YACA,KAAK,MAAM,CAACrC,GAAG,EAAEsL,KAAK,CAAC,IAAIF,MAAM,EAAE;cACjCF,GAAG,CAACK,YAAY,CAACC,MAAM,CAACxL,GAAG,EAAEsL,KAAK,CAAC;YACrC;UACF;UACA,OAAOJ,GAAG,CAACO,QAAQ,CAAC,CAAC;QACvB,CAAC;MACH;;MAEA;MACA;MACA;;MAEA;MACAvJ,MAAMA,CAAA,EAAG;QACP;QACA;QACA;QACA;QACA;QACA;QACA,MAAMwJ,iBAAiB,GAAGpH,GAAG,CAACqH,wBAAwB,CAACC,GAAG,CAAC,CAAC,IAAItH,GAAG,CAACuH,6BAA6B,CAACD,GAAG,CAAC,CAAC;QACvG,IAAI,CAACF,iBAAiB,EACpB,MAAM,IAAI3J,KAAK,CAAC,oEAAoE,CAAC;QACvF,OAAO2J,iBAAiB,CAACxJ,MAAM;MACjC;MAEA,MAAM/C,IAAIA,CAAA,EAAG;QACX,MAAM2M,oBAAoB,CAAC,IAAI,CAACzM,KAAK,CAAC;MACxC;;MAEA;MACA;MACA;;MAEA;AACF;AACA;AACA;AACA;MACE0M,oBAAoBA,CAACjI,IAAI,EAAE;QACzB;QACA,OAAO,IAAI,CAACoG,kBAAkB,CAAClG,QAAQ,CAACF,IAAI,CAAC;MAC/C;;MAEA;AACF;AACA;AACA;AACA;MACEkI,eAAeA,CAAClI,IAAI,EAAE;QACpB,IAAI,CAACqG,qBAAqB,CAAC8B,IAAI,CAACnI,IAAI,CAAC;MACvC;;MAEA;AACF;AACA;AACA;AACA;MACEoI,mBAAmBA,CAACpI,IAAI,EAAE;QACxB,IAAI,IAAI,CAACqI,wBAAwB,EAAE;UACjC,MAAM,IAAIpK,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,IAAI,CAACoK,wBAAwB,GAAGrI,IAAI;MACtC;MAoBA;AACF;AACA;AACA;AACA;MACEsI,YAAYA,CAACtI,IAAI,EAAE;QACjB,IAAI,IAAI,CAACuI,iBAAiB,EAAE;UAC1B,MAAM,IAAItK,KAAK,CAAC,iCAAiC,CAAC;QACpD;QAEA,IAAI,CAACsK,iBAAiB,GAAGvN,MAAM,CAACwN,MAAM,CAACxI,IAAI,CAAC;MAC9C;;MAEA;AACF;AACA;AACA;AACA;MACEyI,eAAeA,CAACzI,IAAI,EAAE;QACpB,IAAI,IAAI,CAAC0I,oBAAoB,EAAE;UAC7B,MAAM,IAAIzK,KAAK,CAAC,oCAAoC,CAAC;QACvD;QAEA,IAAI,CAACyK,oBAAoB,GAAG1I,IAAI;MAClC;;MAEA;AACF;AACA;AACA;AACA;AACA;MACE2I,oCAAoCA,CAAC3I,IAAI,EAAE;QACzC,IAAI,IAAI,CAAC4I,kCAAkC,EAAE;UAC3C,MAAM,IAAI3K,KAAK,CAAC,yDAAyD,CAAC;QAC5E;QACA,IAAI,CAAC2K,kCAAkC,GAAG5I,IAAI;MAChD;MAEA,MAAM6I,cAAcA,CAACnM,UAAU,EAAEoM,OAAO,EAAE;QACxC,MAAM,IAAI,CAAC1C,kBAAkB,CAAC2C,YAAY,CAAC,MAAO3I,QAAQ,IAAK;UAC7D,IAAIH,GAAG;UACP,IAAI;YACFA,GAAG,GAAG,MAAMG,QAAQ,CAAC4I,0BAA0B,CAACtM,UAAU,EAAEoM,OAAO,CAAC,CAAC;UACvE,CAAC,CACD,OAAOG,CAAC,EAAE;YACRH,OAAO,CAACI,OAAO,GAAG,KAAK;YACvB;YACA;YACA;YACA;YACAJ,OAAO,CAACvM,KAAK,GAAG0M,CAAC;YACjB,OAAO,IAAI;UACb;UACA,IAAI,CAAEhJ,GAAG,EAAE;YACT6I,OAAO,CAACI,OAAO,GAAG,KAAK;YACvB;YACA;YACA,IAAI,CAACJ,OAAO,CAACvM,KAAK,EAChBuM,OAAO,CAACvM,KAAK,GAAG,IAAIvB,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,iBAAiB,CAAC;UAC5D;UACA,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;MAEA,MAAMkL,gBAAgBA,CAACzM,UAAU,EAAEoM,OAAO,EAAE;QAC1C,MAAM,IAAI,CAAChM,YAAY,CAACiM,YAAY,CAAC,MAAO3I,QAAQ,IAAK;UACvD,MAAMA,QAAQ,CAAC4I,0BAA0B,CAACtM,UAAU,EAAEoM,OAAO,CAAC,CAAC;UAC/D,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;MAEA,MAAMM,YAAYA,CAAC1M,UAAU,EAAEoM,OAAO,EAAE;QACtC,MAAM,IAAI,CAAC5L,mBAAmB,CAAC6L,YAAY,CAAC,MAAO3I,QAAQ,IAAK;UAC9D,MAAMA,QAAQ,CAAC4I,0BAA0B,CAACtM,UAAU,EAAEoM,OAAO,CAAC,CAAC;UAC/D,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;MAEA,MAAMO,iBAAiBA,CAAC3M,UAAU,EAAE0B,MAAM,EAAE;QAC1C;QACA,IAAIO,IAAI;QACR,MAAM,IAAI,CAACxB,aAAa,CAAC4L,YAAY,CAAC,MAAM3I,QAAQ,IAAI;UACtD,IAAI,CAACzB,IAAI,IAAIP,MAAM,EAAEO,IAAI,GAAG,MAAM,IAAI,CAACpD,KAAK,CAAC0D,YAAY,CAACb,MAAM,EAAE;YAAEK,MAAM,EAAE,IAAI,CAAChC,QAAQ,CAAC+B;UAAqB,CAAC,CAAC;UACjH4B,QAAQ,CAAC;YAAEzB,IAAI;YAAEjC;UAAW,CAAC,CAAC;UAC9B,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;MA+DA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM4M,UAAUA,CAACC,gBAAgB,EAAEnL,MAAM,EAAEoL,iBAAiB,EAAE;QAC5D,IAAI,CAAEA,iBAAiB,EAAE;UACvBA,iBAAiB,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;UACrD,MAAM,IAAI,CAACC,iBAAiB,CAACtL,MAAM,EAAEoL,iBAAiB,CAAC;QACzD;;QAEA;QACA;QACA;QACA;QACA;QACA;QACAxO,MAAM,CAAC2O,gBAAgB,CAAC,MACtB,IAAI,CAACC,cAAc,CACjBxL,MAAM,EACNmL,gBAAgB,CAAC7M,UAAU,EAC3B,IAAI,CAACmN,eAAe,CAACL,iBAAiB,CAAC5C,KAAK,CAC9C,CACF,CAAC;QAED,MAAM2C,gBAAgB,CAACO,SAAS,CAAC1L,MAAM,CAAC;QAExC,OAAO;UACL8F,EAAE,EAAE9F,MAAM;UACVwI,KAAK,EAAE4C,iBAAiB,CAAC5C,KAAK;UAC9BmD,YAAY,EAAE,IAAI,CAACzI,gBAAgB,CAACkI,iBAAiB,CAACjI,IAAI;QAC5D,CAAC;MACH;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMyI,aAAaA,CACjBT,gBAAgB,EAChBU,UAAU,EACVC,UAAU,EACVC,MAAM,EACN;QACA,IAAI,CAACA,MAAM,EACT,MAAM,IAAIlM,KAAK,CAAC,oBAAoB,CAAC;;QAEvC;QACA;QACA;QACA,IAAI,CAACkM,MAAM,CAAC/L,MAAM,IAAI,CAAC+L,MAAM,CAAC5N,KAAK,EACjC,MAAM,IAAI0B,KAAK,CAAC,kDAAkD,CAAC;QAErE,IAAIU,IAAI;QACR,IAAIwL,MAAM,CAAC/L,MAAM,EACfO,IAAI,GAAG,MAAM,IAAI,CAACpD,KAAK,CAAC0D,YAAY,CAACkL,MAAM,CAAC/L,MAAM,EAAE;UAACK,MAAM,EAAE,IAAI,CAAChC,QAAQ,CAAC+B;QAAoB,CAAC,CAAC;QAEnG,MAAMsK,OAAO,GAAG;UACdsB,IAAI,EAAED,MAAM,CAACC,IAAI,IAAI,SAAS;UAC9BlB,OAAO,EAAE,CAAC,EAAGiB,MAAM,CAAC/L,MAAM,IAAI,CAAC+L,MAAM,CAAC5N,KAAK,CAAC;UAC5C0N,UAAU,EAAEA,UAAU;UACtBI,eAAe,EAAEC,KAAK,CAACC,IAAI,CAACL,UAAU;QACxC,CAAC;QACD,IAAIC,MAAM,CAAC5N,KAAK,EAAE;UAChBuM,OAAO,CAACvM,KAAK,GAAG4N,MAAM,CAAC5N,KAAK;QAC9B;QACA,IAAIoC,IAAI,EAAE;UACRmK,OAAO,CAACnK,IAAI,GAAGA,IAAI;QACrB;;QAEA;QACA;QACA;QACA,MAAM,IAAI,CAACkK,cAAc,CAACU,gBAAgB,CAAC7M,UAAU,EAAEoM,OAAO,CAAC;QAE/D,IAAIA,OAAO,CAACI,OAAO,EAAE;UACnB,MAAMsB,CAAC,GAAG,MAAM,IAAI,CAAClB,UAAU,CAC7BC,gBAAgB,EAChBY,MAAM,CAAC/L,MAAM,EACb+L,MAAM,CAACX,iBACT,CAAC;UACD,MAAMvJ,GAAG,GAAA5F,aAAA,CAAAA,aAAA,KACJmQ,CAAC,GACDL,MAAM,CAAClO,OAAO,CAClB;UACDgE,GAAG,CAACmK,IAAI,GAAGtB,OAAO,CAACsB,IAAI;UACvB,MAAM,IAAI,CAACjB,gBAAgB,CAACI,gBAAgB,CAAC7M,UAAU,EAAEoM,OAAO,CAAC;UACjE,OAAO7I,GAAG;QACZ,CAAC,MACI;UACH,MAAM,IAAI,CAACmJ,YAAY,CAACG,gBAAgB,CAAC7M,UAAU,EAAEoM,OAAO,CAAC;UAC7D,MAAMA,OAAO,CAACvM,KAAK;QACrB;MACF;MAEA;MACA;MACA;MACA;MACA,MAAMkO,YAAYA,CAChBlB,gBAAgB,EAChBU,UAAU,EACVC,UAAU,EACVE,IAAI,EACJM,EAAE,EACF;QACA,OAAO,MAAM,IAAI,CAACV,aAAa,CAC7BT,gBAAgB,EAChBU,UAAU,EACVC,UAAU,EACV,MAAMS,cAAc,CAACP,IAAI,EAAEM,EAAE,CAC/B,CAAC;MACH;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAME,mBAAmBA,CACvBrB,gBAAgB,EAChBU,UAAU,EACVC,UAAU,EACVC,MAAM,EACN;QACA,MAAMrB,OAAO,GAAG;UACdsB,IAAI,EAAED,MAAM,CAACC,IAAI,IAAI,SAAS;UAC9BlB,OAAO,EAAE,KAAK;UACd3M,KAAK,EAAE4N,MAAM,CAAC5N,KAAK;UACnB0N,UAAU,EAAEA,UAAU;UACtBI,eAAe,EAAEC,KAAK,CAACC,IAAI,CAACL,UAAU;QACxC,CAAC;QAED,IAAIC,MAAM,CAAC/L,MAAM,EAAE;UACjB0K,OAAO,CAACnK,IAAI,GAAG,IAAI,CAACpD,KAAK,CAAC0D,YAAY,CAACkL,MAAM,CAAC/L,MAAM,EAAE;YAACK,MAAM,EAAE,IAAI,CAAChC,QAAQ,CAAC+B;UAAoB,CAAC,CAAC;QACrG;QAEA,MAAM,IAAI,CAACqK,cAAc,CAACU,gBAAgB,CAAC7M,UAAU,EAAEoM,OAAO,CAAC;QAC/D,MAAM,IAAI,CAACM,YAAY,CAACG,gBAAgB,CAAC7M,UAAU,EAAEoM,OAAO,CAAC;;QAE7D;QACA;QACA,OAAOA,OAAO;MAChB;MAEA;MACA;MACA;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACE+B,oBAAoBA,CAACjN,IAAI,EAAEkN,OAAO,EAAE;QAClC,IAAI,CAAEA,OAAO,EAAE;UACbA,OAAO,GAAGlN,IAAI;UACdA,IAAI,GAAG,IAAI;QACb;QAEA,IAAI,CAACqI,cAAc,CAACkC,IAAI,CAAC;UACvBvK,IAAI,EAAEA,IAAI;UACVkN,OAAO,EAAE9P,MAAM,CAACwN,MAAM,CAACsC,OAAO;QAChC,CAAC,CAAC;MACJ;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA,MAAMC,iBAAiBA,CAACxB,gBAAgB,EAAEtN,OAAO,EAAE;QACjD,KAAK,IAAI6O,OAAO,IAAI,IAAI,CAAC7E,cAAc,EAAE;UACvC,MAAMkE,MAAM,GAAG,MAAMQ,cAAc,CAACG,OAAO,CAAClN,IAAI,EAAE,YAChD,MAAMkN,OAAO,CAACA,OAAO,CAACtL,IAAI,CAAC+J,gBAAgB,EAAEtN,OAAO,CACtD,CAAC;UAED,IAAIkO,MAAM,EAAE;YACV,OAAOA,MAAM;UACf;UAEA,IAAIA,MAAM,KAAKxN,SAAS,EAAE;YACxB,MAAM,IAAI3B,MAAM,CAACiD,KAAK,CACpB,GAAG,EACH,qDACF,CAAC;UACH;QACF;QAEA,OAAO;UACLmM,IAAI,EAAE,IAAI;UACV7N,KAAK,EAAE,IAAIvB,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,wCAAwC;QACvE,CAAC;MACH;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM+M,YAAYA,CAAC5M,MAAM,EAAE4I,UAAU,EAAE;QACrC,MAAM,IAAI,CAACzL,KAAK,CAAC0P,WAAW,CAAC7M,MAAM,EAAE;UACnC8M,KAAK,EAAE;YACL,6BAA6B,EAAE;cAC7BnH,GAAG,EAAE,CACH;gBAAEoH,WAAW,EAAEnE;cAAW,CAAC,EAC3B;gBAAEJ,KAAK,EAAEI;cAAW,CAAC;YAEzB;UACF;QACF,CAAC,CAAC;MACJ;MAEA5B,kBAAkBA,CAAA,EAAG;QACnB;QACA;QACA,MAAMhK,QAAQ,GAAG,IAAI;;QAGrB;QACA;QACA,MAAMgQ,OAAO,GAAG,CAAC,CAAC;;QAElB;QACA;QACA;QACA;QACAA,OAAO,CAACC,KAAK,GAAG,gBAAgBpP,OAAO,EAAE;UACvC;UACA;UACAwG,KAAK,CAACxG,OAAO,EAAEE,MAAM,CAAC;UAEtB,MAAMgO,MAAM,GAAG,MAAM/O,QAAQ,CAAC2P,iBAAiB,CAAC,IAAI,EAAE9O,OAAO,CAAC;UAC9D;;UAEA,OAAO,MAAMb,QAAQ,CAAC4O,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE1L,SAAS,EAAE6L,MAAM,CAAC;QACvE,CAAC;QAEDiB,OAAO,CAACE,MAAM,GAAG,kBAAkB;UACjC,MAAM1E,KAAK,GAAGxL,QAAQ,CAACmQ,cAAc,CAAC,IAAI,CAAC7O,UAAU,CAACwH,EAAE,CAAC;UACzD9I,QAAQ,CAACwO,cAAc,CAAC,IAAI,CAACxL,MAAM,EAAE,IAAI,CAAC1B,UAAU,EAAE,IAAI,CAAC;UAC3D,IAAIkK,KAAK,IAAI,IAAI,CAACxI,MAAM,EAAE;YACzB,MAAMhD,QAAQ,CAAC4P,YAAY,CAAC,IAAI,CAAC5M,MAAM,EAAEwI,KAAK,CAAC;UAChD;UACA,MAAMxL,QAAQ,CAACiO,iBAAiB,CAAC,IAAI,CAAC3M,UAAU,EAAE,IAAI,CAAC0B,MAAM,CAAC;UAC9D,MAAM,IAAI,CAAC0L,SAAS,CAAC,IAAI,CAAC;QAC5B,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAsB,OAAO,CAACI,WAAW,GAAG,kBAAkB;UACtC,MAAM7M,IAAI,GAAG,MAAMvD,QAAQ,CAACG,KAAK,CAAC0D,YAAY,CAAC,IAAI,CAACb,MAAM,EAAE;YAC1DK,MAAM,EAAE;cAAE,6BAA6B,EAAE;YAAE;UAC7C,CAAC,CAAC;UACF,IAAI,CAAE,IAAI,CAACL,MAAM,IAAI,CAAEO,IAAI,EAAE;YAC3B,MAAM,IAAI3D,MAAM,CAACiD,KAAK,CAAC,wBAAwB,CAAC;UAClD;UACA;UACA;UACA;UACA;UACA,MAAMwN,kBAAkB,GAAGrQ,QAAQ,CAACmQ,cAAc,CAAC,IAAI,CAAC7O,UAAU,CAACwH,EAAE,CAAC;UACtE,MAAMwH,mBAAmB,GAAG/M,IAAI,CAACgN,QAAQ,CAACC,MAAM,CAACC,WAAW,CAACtH,IAAI,CAC/DuH,YAAY,IAAIA,YAAY,CAACX,WAAW,KAAKM,kBAC/C,CAAC;UACD,IAAI,CAAEC,mBAAmB,EAAE;YAAE;YAC3B,MAAM,IAAI1Q,MAAM,CAACiD,KAAK,CAAC,qBAAqB,CAAC;UAC/C;UACA,MAAM8N,eAAe,GAAG3Q,QAAQ,CAACqO,0BAA0B,CAAC,CAAC;UAC7DsC,eAAe,CAACxK,IAAI,GAAGmK,mBAAmB,CAACnK,IAAI;UAC/C,MAAMnG,QAAQ,CAACsO,iBAAiB,CAAC,IAAI,CAACtL,MAAM,EAAE2N,eAAe,CAAC;UAC9D,OAAO,MAAM3Q,QAAQ,CAACkO,UAAU,CAAC,IAAI,EAAE,IAAI,CAAClL,MAAM,EAAE2N,eAAe,CAAC;QACtE,CAAC;;QAED;QACA;QACA;QACAX,OAAO,CAACY,iBAAiB,GAAG,kBAAkB;UAC5C,IAAI,CAAE,IAAI,CAAC5N,MAAM,EAAE;YACjB,MAAM,IAAIpD,MAAM,CAACiD,KAAK,CAAC,wBAAwB,CAAC;UAClD;UACA,MAAMgO,YAAY,GAAG7Q,QAAQ,CAACmQ,cAAc,CAAC,IAAI,CAAC7O,UAAU,CAACwH,EAAE,CAAC;UAChE,MAAM9I,QAAQ,CAACG,KAAK,CAAC0P,WAAW,CAAC,IAAI,CAAC7M,MAAM,EAAE;YAC5C8M,KAAK,EAAE;cACL,6BAA6B,EAAE;gBAAEC,WAAW,EAAE;kBAAEe,GAAG,EAAED;gBAAa;cAAE;YACtE;UACF,CAAC,CAAC;QACJ,CAAC;;QAED;QACA;QACAb,OAAO,CAACe,qBAAqB,GAAG,MAAOlQ,OAAO,IAAK;UACjDwG,KAAK,CAACxG,OAAO,EAAEqG,KAAK,CAAC8J,eAAe,CAAC;YAACC,OAAO,EAAE3J;UAAM,CAAC,CAAC,CAAC;UACxD;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,EAAEtH,QAAQ,CAACkR,KAAK,IACflR,QAAQ,CAACkR,KAAK,CAACC,YAAY,CAAC,CAAC,CAAClQ,QAAQ,CAACJ,OAAO,CAACoQ,OAAO,CAAC,CAAC,EAAE;YAC7D,MAAM,IAAIrR,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,iBAAiB,CAAC;UAChD;UAEA,IAAIwB,OAAO,CAAC,uBAAuB,CAAC,EAAE;YACpC,MAAM;cAAE+M;YAAqB,CAAC,GAAG/M,OAAO,CAAC,uBAAuB,CAAC;YACjE,MAAM4M,OAAO,GAAG,MAAMG,oBAAoB,CAACC,cAAc,CAACxN,YAAY,CAAC;cAACoN,OAAO,EAAEpQ,OAAO,CAACoQ;YAAO,CAAC,CAAC;YAClG,IAAIA,OAAO,EACT,MAAM,IAAIrR,MAAM,CAACiD,KAAK,CAAC,GAAG,aAAAzB,MAAA,CAAaP,OAAO,CAACoQ,OAAO,wBAAqB,CAAC;YAE9E,IAAI5M,OAAO,CAAC,kBAAkB,CAAC,EAAE;cAC/B,MAAM;gBAAEC;cAAgB,CAAC,GAAGD,OAAO,CAAC,kBAAkB,CAAC;cACvD,IAAI2C,MAAM,CAAC5C,IAAI,CAACvD,OAAO,EAAE,QAAQ,CAAC,IAAIyD,eAAe,CAACgN,WAAW,CAAC,CAAC,EACjEzQ,OAAO,CAAC0Q,MAAM,GAAGjN,eAAe,CAACkN,IAAI,CAAC3Q,OAAO,CAAC0Q,MAAM,CAAC;YACzD;YAEA,MAAMH,oBAAoB,CAACC,cAAc,CAACI,WAAW,CAAC5Q,OAAO,CAAC;UAChE;QACF,CAAC;QAEDb,QAAQ,CAAC+J,OAAO,CAACiG,OAAO,CAACA,OAAO,CAAC;MACnC;MAEA/F,qBAAqBA,CAAA,EAAG;QACtB,IAAI,CAACF,OAAO,CAAC2H,YAAY,CAACpQ,UAAU,IAAI;UACtC,IAAI,CAACoJ,YAAY,CAACpJ,UAAU,CAACwH,EAAE,CAAC,GAAG;YACjCxH,UAAU,EAAEA;UACd,CAAC;UAEDA,UAAU,CAACqQ,OAAO,CAAC,MAAM;YACvB,IAAI,CAACC,0BAA0B,CAACtQ,UAAU,CAACwH,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC4B,YAAY,CAACpJ,UAAU,CAACwH,EAAE,CAAC;UACzC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA2B,uBAAuBA,CAAA,EAAG;QACxB;QACA,MAAM;UAAEtK,KAAK;UAAE+J,kBAAkB;UAAEG;QAAsB,CAAC,GAAG,IAAI;;QAEjE;QACA,IAAI,CAACN,OAAO,CAAC8H,OAAO,CAAC,kCAAkC,EAAE,YAAW;UAClE,IAAIxN,OAAO,CAAC,uBAAuB,CAAC,EAAE;YACpC,MAAM;cAAE+M;YAAqB,CAAC,GAAG/M,OAAO,CAAC,uBAAuB,CAAC;YACjE,OAAO+M,oBAAoB,CAACC,cAAc,CAAClI,IAAI,CAAC,CAAC,CAAC,EAAE;cAAC9F,MAAM,EAAE;gBAACkO,MAAM,EAAE;cAAC;YAAC,CAAC,CAAC;UAC5E;UACA,IAAI,CAACO,KAAK,CAAC,CAAC;QACd,CAAC,EAAE;UAACC,OAAO,EAAE;QAAI,CAAC,CAAC,CAAC,CAAC;;QAErB;QACA;QACAnS,MAAM,CAACoS,OAAO,CAAC,MAAM;UACnB;UACA;UACA,MAAMC,YAAY,GAAG,IAAI,CAAChP,wBAAwB,CAAC,CAAC,CAACI,MAAM,IAAI,CAAC,CAAC;UACjE,MAAMrC,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACiR,YAAY,CAAC;UACtC;UACA,MAAM5O,MAAM,GAAGrC,IAAI,CAACmC,MAAM,GAAG,CAAC,IAAI8O,YAAY,CAACjR,IAAI,CAAC,CAAC,CAAC,CAAC,GAAA/B,aAAA,CAAAA,aAAA,KAClD,IAAI,CAACgE,wBAAwB,CAAC,CAAC,CAACI,MAAM,GACtCgH,qBAAqB,CAACC,UAAU,IACjCD,qBAAqB,CAACC,UAAU;UACpC;UACA,IAAI,CAACP,OAAO,CAAC8H,OAAO,CAAC,IAAI,EAAE,YAAY;YACrC,IAAI,IAAI,CAAC7O,MAAM,EAAE;cACf,OAAO7C,KAAK,CAACgJ,IAAI,CAAC;gBAChB+I,GAAG,EAAE,IAAI,CAAClP;cACZ,CAAC,EAAE;gBACDK;cACF,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,OAAO,IAAI;YACb;UACF,CAAC,EAAE,gCAAgC;YAAC0O,OAAO,EAAE;UAAI,CAAC,CAAC;QACrD,CAAC,CAAC;;QAEF;QACA;QACA1N,OAAO,CAAC8N,WAAW,IAAIvS,MAAM,CAACoS,OAAO,CAAC,MAAM;UAC1C;UACA,MAAMI,eAAe,GAAG/O,MAAM,IAAIA,MAAM,CAACgP,MAAM,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAAtT,aAAA,CAAAA,aAAA,KACnDqT,IAAI;YAAE,CAACC,KAAK,GAAG;UAAC,EAAG,EAC1B,CAAC,CACH,CAAC;UACD,IAAI,CAACxI,OAAO,CAAC8H,OAAO,CAAC,IAAI,EAAE,YAAY;YACrC,IAAI,IAAI,CAAC7O,MAAM,EAAE;cACf,OAAO7C,KAAK,CAACgJ,IAAI,CAAC;gBAAE+I,GAAG,EAAE,IAAI,CAAClP;cAAO,CAAC,EAAE;gBACtCK,MAAM,EAAE+O,eAAe,CAAClI,kBAAkB,CAACC,YAAY;cACzD,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,OAAO,IAAI;YACb;UACF,CAAC,EAAE,gCAAgC;YAAC4H,OAAO,EAAE;UAAI,CAAC,CAAC;;UAEnD;UACA;UACA;UACA;UACA;UACA,IAAI,CAAChI,OAAO,CAAC8H,OAAO,CAAC,IAAI,EAAE,YAAY;YACrC,MAAMvJ,QAAQ,GAAG,IAAI,CAACtF,MAAM,GAAG;cAAEkP,GAAG,EAAE;gBAAEpB,GAAG,EAAE,IAAI,CAAC9N;cAAO;YAAE,CAAC,GAAG,CAAC,CAAC;YACjE,OAAO7C,KAAK,CAACgJ,IAAI,CAACb,QAAQ,EAAE;cAC1BjF,MAAM,EAAE+O,eAAe,CAAClI,kBAAkB,CAACE,UAAU;YACvD,CAAC,CAAC;UACJ,CAAC,EAAE,gCAAgC;YAAC2H,OAAO,EAAE;UAAI,CAAC,CAAC;QACrD,CAAC,CAAC;MACJ;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACAS,oBAAoBA,CAACC,IAAI,EAAE;QACzB,IAAI,CAACvI,kBAAkB,CAACC,YAAY,CAAC4C,IAAI,CAAC2F,KAAK,CAC7C,IAAI,CAACxI,kBAAkB,CAACC,YAAY,EAAEsI,IAAI,CAACE,eAAe,CAAC;QAC7D,IAAI,CAACzI,kBAAkB,CAACE,UAAU,CAAC2C,IAAI,CAAC2F,KAAK,CAC3C,IAAI,CAACxI,kBAAkB,CAACE,UAAU,EAAEqI,IAAI,CAACG,aAAa,CAAC;MAC3D;MAEA;MACA;MACA;MACA;MACAC,uBAAuBA,CAACxP,MAAM,EAAE;QAC9B,IAAI,CAACgH,qBAAqB,CAACC,UAAU,GAAGjH,MAAM;MAChD;MAEA;MACA;MACA;;MAEA;MACA;MACAyP,eAAeA,CAACC,YAAY,EAAER,KAAK,EAAE;QACnC,MAAMS,IAAI,GAAG,IAAI,CAACtI,YAAY,CAACqI,YAAY,CAAC;QAC5C,OAAOC,IAAI,IAAIA,IAAI,CAACT,KAAK,CAAC;MAC5B;MAEAU,eAAeA,CAACF,YAAY,EAAER,KAAK,EAAEnG,KAAK,EAAE;QAC1C,MAAM4G,IAAI,GAAG,IAAI,CAACtI,YAAY,CAACqI,YAAY,CAAC;;QAE5C;QACA;QACA,IAAI,CAACC,IAAI,EACP;QAEF,IAAI5G,KAAK,KAAK7K,SAAS,EACrB,OAAOyR,IAAI,CAACT,KAAK,CAAC,CAAC,KAEnBS,IAAI,CAACT,KAAK,CAAC,GAAGnG,KAAK;MACvB;MAEA;MACA;MACA;MACA;;MAEAqC,eAAeA,CAAC7C,UAAU,EAAE;QAC1B,MAAMsH,IAAI,GAAGpM,MAAM,CAACqM,UAAU,CAAC,QAAQ,CAAC;QACxCD,IAAI,CAACE,MAAM,CAACxH,UAAU,CAAC;QACvB,OAAOsH,IAAI,CAACG,MAAM,CAAC,QAAQ,CAAC;MAC9B;MAEA;MACAC,iBAAiBA,CAAC5C,YAAY,EAAE;QAC9B,MAAM;YAAElF;UAA6B,CAAC,GAAGkF,YAAY;UAAnC6C,kBAAkB,GAAA7M,wBAAA,CAAKgK,YAAY,EAAA7J,SAAA;QACrD,OAAA5H,aAAA,CAAAA,aAAA,KACKsU,kBAAkB;UACrBxD,WAAW,EAAE,IAAI,CAACtB,eAAe,CAACjD,KAAK;QAAC;MAE5C;MAEA;MACA;MACA;MACA,MAAMgI,uBAAuBA,CAACxQ,MAAM,EAAE+M,WAAW,EAAElH,KAAK,EAAE;QACxDA,KAAK,GAAGA,KAAK,GAAA5J,aAAA,KAAQ4J,KAAK,IAAK,CAAC,CAAC;QACjCA,KAAK,CAACqJ,GAAG,GAAGlP,MAAM;QAClB,MAAM,IAAI,CAAC7C,KAAK,CAAC0P,WAAW,CAAChH,KAAK,EAAE;UAClC4K,SAAS,EAAE;YACT,6BAA6B,EAAE1D;UACjC;QACF,CAAC,CAAC;MACJ;MAEA;MACA,MAAMzB,iBAAiBA,CAACtL,MAAM,EAAE0N,YAAY,EAAE7H,KAAK,EAAE;QACnD,MAAM,IAAI,CAAC2K,uBAAuB,CAChCxQ,MAAM,EACN,IAAI,CAACsQ,iBAAiB,CAAC5C,YAAY,CAAC,EACpC7H,KACF,CAAC;MACH;MAEA;AACF;AACA;AACA;AACA;AACA;MACE6K,oBAAoBA,CAAC1Q,MAAM,EAAE;QAC3B,IAAI,CAAC7C,KAAK,CAAC0P,WAAW,CAAC7M,MAAM,EAAE;UAC7B2Q,IAAI,EAAE;YACJ,6BAA6B,EAAE;UACjC;QACF,CAAC,CAAC;MACJ;MAEA;MACAC,eAAeA,CAACb,YAAY,EAAE;QAC5B,OAAO,IAAI,CAACpI,2BAA2B,CAACoI,YAAY,CAAC;MACvD;MAEA;MACA;MACA;MACAnB,0BAA0BA,CAACmB,YAAY,EAAE;QACvC,IAAI/L,MAAM,CAAC5C,IAAI,CAAC,IAAI,CAACuG,2BAA2B,EAAEoI,YAAY,CAAC,EAAE;UAC/D,MAAMc,OAAO,GAAG,IAAI,CAAClJ,2BAA2B,CAACoI,YAAY,CAAC;UAC9D,IAAI,OAAOc,OAAO,KAAK,QAAQ,EAAE;YAC/B;YACA;YACA;YACA;YACA,OAAO,IAAI,CAAClJ,2BAA2B,CAACoI,YAAY,CAAC;UACvD,CAAC,MAAM;YACL,OAAO,IAAI,CAACpI,2BAA2B,CAACoI,YAAY,CAAC;YACrDc,OAAO,CAACC,IAAI,CAAC,CAAC;UAChB;QACF;MACF;MAEA3D,cAAcA,CAAC4C,YAAY,EAAE;QAC3B,OAAO,IAAI,CAACD,eAAe,CAACC,YAAY,EAAE,YAAY,CAAC;MACzD;MAEA;MACAvE,cAAcA,CAACxL,MAAM,EAAE1B,UAAU,EAAEyS,QAAQ,EAAE;QAC3C,IAAI,CAACnC,0BAA0B,CAACtQ,UAAU,CAACwH,EAAE,CAAC;QAC9C,IAAI,CAACmK,eAAe,CAAC3R,UAAU,CAACwH,EAAE,EAAE,YAAY,EAAEiL,QAAQ,CAAC;QAE3D,IAAIA,QAAQ,EAAE;UACZ;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAMC,eAAe,GAAG,EAAE,IAAI,CAACpJ,sBAAsB;UACrD,IAAI,CAACD,2BAA2B,CAACrJ,UAAU,CAACwH,EAAE,CAAC,GAAGkL,eAAe;UACjEpU,MAAM,CAACqU,KAAK,CAAC,YAAY;YACvB;YACA;YACA;YACA;YACA,IAAI,IAAI,CAACtJ,2BAA2B,CAACrJ,UAAU,CAACwH,EAAE,CAAC,KAAKkL,eAAe,EAAE;cACvE;YACF;YAEA,IAAIE,iBAAiB;YACrB;YACA;YACA;YACA,MAAML,OAAO,GAAG,MAAM,IAAI,CAAC1T,KAAK,CAACgJ,IAAI,CAAC;cACpC+I,GAAG,EAAElP,MAAM;cACX,yCAAyC,EAAE+Q;YAC7C,CAAC,EAAE;cAAE1Q,MAAM,EAAE;gBAAE6O,GAAG,EAAE;cAAE;YAAE,CAAC,CAAC,CAACiC,cAAc,CAAC;cACxCC,KAAK,EAAEA,CAAA,KAAM;gBACXF,iBAAiB,GAAG,IAAI;cAC1B,CAAC;cACDG,OAAO,EAAE/S,UAAU,CAACgT;cACpB;cACA;cACA;YACF,CAAC,EAAE;cAAEC,oBAAoB,EAAE;YAAK,CAAC,CAAC;;YAElC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAI,IAAI,CAAC5J,2BAA2B,CAACrJ,UAAU,CAACwH,EAAE,CAAC,KAAKkL,eAAe,EAAE;cACvEH,OAAO,CAACC,IAAI,CAAC,CAAC;cACd;YACF;YAEA,IAAI,CAACnJ,2BAA2B,CAACrJ,UAAU,CAACwH,EAAE,CAAC,GAAG+K,OAAO;YAEzD,IAAI,CAAEK,iBAAiB,EAAE;cACvB;cACA;cACA;cACA;cACA;cACA5S,UAAU,CAACgT,KAAK,CAAC,CAAC;YACpB;UACF,CAAC,CAAC;QACJ;MACF;MAEA;MACA;MACAjG,0BAA0BA,CAAA,EAAG;QAC3B,OAAO;UACL7C,KAAK,EAAEgJ,MAAM,CAACjD,MAAM,CAAC,CAAC;UACtBpL,IAAI,EAAE,IAAIC,IAAI,CAAD;QACf,CAAC;MACH;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMqO,0BAA0BA,CAACC,eAAe,EAAE1R,MAAM,EAAE;QACxD,MAAM2R,eAAe,GAAG,IAAI,CAACjP,gCAAgC,CAAC,CAAC;;QAE/D;QACA,IAAKgP,eAAe,IAAI,CAAC1R,MAAM,IAAM,CAAC0R,eAAe,IAAI1R,MAAO,EAAE;UAChE,MAAM,IAAIH,KAAK,CAAC,yDAAyD,CAAC;QAC5E;QAEA6R,eAAe,GAAGA,eAAe,IAC9B,IAAItO,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,GAAGuO,eAAe,CAAE;QAE1C,MAAMC,WAAW,GAAG;UAClBjM,GAAG,EAAE,CACH;YAAE,gCAAgC,EAAE;UAAO,CAAC,EAC5C;YAAE,gCAAgC,EAAE;cAACkM,OAAO,EAAE;YAAK;UAAC,CAAC;QAEzD,CAAC;QAEF,MAAMC,mBAAmB,CAAC,IAAI,EAAEJ,eAAe,EAAEE,WAAW,EAAE5R,MAAM,CAAC;MACtE;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM+R,2BAA2BA,CAACL,eAAe,EAAE1R,MAAM,EAAE;QACzD,MAAM2R,eAAe,GAAG,IAAI,CAAC7O,iCAAiC,CAAC,CAAC;;QAEhE;QACA,IAAK4O,eAAe,IAAI,CAAC1R,MAAM,IAAM,CAAC0R,eAAe,IAAI1R,MAAO,EAAE;UAChE,MAAM,IAAIH,KAAK,CAAC,yDAAyD,CAAC;QAC5E;QAEA6R,eAAe,GAAGA,eAAe,IAC9B,IAAItO,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,GAAGuO,eAAe,CAAE;QAE1C,MAAMC,WAAW,GAAG;UAClB,iCAAiC,EAAE;QACrC,CAAC;QAED,MAAME,mBAAmB,CAAC,IAAI,EAAEJ,eAAe,EAAEE,WAAW,EAAE5R,MAAM,CAAC;MACvE;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;AACF;AACA;AACA;AACA;AACA;AACA;MACE,MAAMgS,aAAaA,CAACN,eAAe,EAAE1R,MAAM,EAAE;QAC3C,MAAM2R,eAAe,GAAG,IAAI,CAACpP,mBAAmB,CAAC,CAAC;;QAElD;QACA,IAAKmP,eAAe,IAAI,CAAC1R,MAAM,IAAM,CAAC0R,eAAe,IAAI1R,MAAO,EAAE;UAChE,MAAM,IAAIH,KAAK,CAAC,yDAAyD,CAAC;QAC5E;QAEA6R,eAAe,GAAGA,eAAe,IAC9B,IAAItO,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,GAAGuO,eAAe,CAAE;QAC1C,MAAMM,UAAU,GAAGjS,MAAM,GAAG;UAACkP,GAAG,EAAElP;QAAM,CAAC,GAAG,CAAC,CAAC;;QAG9C;QACA;QACA,MAAM,IAAI,CAAC7C,KAAK,CAAC0P,WAAW,CAAA5Q,aAAA,CAAAA,aAAA,KAAMgW,UAAU;UAC1CtM,GAAG,EAAE,CACH;YAAE,kCAAkC,EAAE;cAAEuM,GAAG,EAAER;YAAgB;UAAE,CAAC,EAChE;YAAE,kCAAkC,EAAE;cAAEQ,GAAG,EAAE,CAACR;YAAgB;UAAE,CAAC;QAClE,IACA;UACD5E,KAAK,EAAE;YACL,6BAA6B,EAAE;cAC7BnH,GAAG,EAAE,CACH;gBAAExC,IAAI,EAAE;kBAAE+O,GAAG,EAAER;gBAAgB;cAAE,CAAC,EAClC;gBAAEvO,IAAI,EAAE;kBAAE+O,GAAG,EAAE,CAACR;gBAAgB;cAAE,CAAC;YAEvC;UACF;QACF,CAAC,EAAE;UAAES,KAAK,EAAE;QAAK,CAAC,CAAC;QACnB;QACA;MACF;MAEA;MACApR,MAAMA,CAAClD,OAAO,EAAE;QACd;QACA,MAAMuU,WAAW,GAAG3U,cAAc,CAAC8B,SAAS,CAACwB,MAAM,CAAC2O,KAAK,CAAC,IAAI,EAAExP,SAAS,CAAC;;QAE1E;QACA;QACA,IAAI8D,MAAM,CAAC5C,IAAI,CAAC,IAAI,CAAC/C,QAAQ,EAAE,uBAAuB,CAAC,IACrD,IAAI,CAACA,QAAQ,CAACmE,qBAAqB,KAAK,IAAI,IAC5C,IAAI,CAAC6P,mBAAmB,EAAE;UAC1BzV,MAAM,CAAC0V,aAAa,CAAC,IAAI,CAACD,mBAAmB,CAAC;UAC9C,IAAI,CAACA,mBAAmB,GAAG,IAAI;QACjC;QAEA,OAAOD,WAAW;MACpB;MAEA;MACA,MAAMG,aAAaA,CAAC1U,OAAO,EAAE0C,IAAI,EAAE;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA,IAAI,GAAAtE,aAAA;UACFuW,SAAS,EAAE,IAAIpP,IAAI,CAAC,CAAC;UACrB8L,GAAG,EAAEsC,MAAM,CAAC1L,EAAE,CAAC;QAAC,GACbvF,IAAI,CACR;QAED,IAAIA,IAAI,CAACgN,QAAQ,EAAE;UACjBxP,MAAM,CAACC,IAAI,CAACuC,IAAI,CAACgN,QAAQ,CAAC,CAACkF,OAAO,CAACxE,OAAO,IACxCyE,wBAAwB,CAACnS,IAAI,CAACgN,QAAQ,CAACU,OAAO,CAAC,EAAE1N,IAAI,CAAC2O,GAAG,CAC3D,CAAC;QACH;QAEA,IAAIyD,QAAQ;QACZ,IAAI,IAAI,CAACxI,iBAAiB,EAAE;UAC1B;UACAwI,QAAQ,GAAG,MAAM,IAAI,CAACxI,iBAAiB,CAACtM,OAAO,EAAE0C,IAAI,CAAC;;UAEtD;UACA;UACA;UACA,IAAIoS,QAAQ,KAAK,mBAAmB,EAClCA,QAAQ,GAAGC,qBAAqB,CAAC/U,OAAO,EAAE0C,IAAI,CAAC;QACnD,CAAC,MAAM;UACLoS,QAAQ,GAAGC,qBAAqB,CAAC/U,OAAO,EAAE0C,IAAI,CAAC;QACjD;QAAC,IAAAsS,yBAAA;QAAA,IAAAC,iBAAA;QAAA,IAAAC,cAAA;QAAA;UAED,SAAAC,SAAA,GAAArP,cAAA,CAAyB,IAAI,CAACsE,qBAAqB,GAAAgL,KAAA,EAAAJ,yBAAA,KAAAI,KAAA,SAAAD,SAAA,CAAAE,IAAA,IAAAC,IAAA,EAAAN,yBAAA,UAAE;YAAA,MAApCO,IAAI,GAAAH,KAAA,CAAA7J,KAAA;YAAA;cACnB,IAAI,EAAE,MAAMgK,IAAI,CAACT,QAAQ,CAAC,GACxB,MAAM,IAAI/V,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,wBAAwB,CAAC;YAAC;UAC1D;QAAC,SAAAwT,GAAA;UAAAP,iBAAA;UAAAC,cAAA,GAAAM,GAAA;QAAA;UAAA;YAAA,IAAAR,yBAAA,IAAAG,SAAA,CAAAM,MAAA;cAAA,MAAAN,SAAA,CAAAM,MAAA;YAAA;UAAA;YAAA,IAAAR,iBAAA;cAAA,MAAAC,cAAA;YAAA;UAAA;QAAA;QAED,IAAI/S,MAAM;QACV,IAAI;UACFA,MAAM,GAAG,MAAM,IAAI,CAAC7C,KAAK,CAACsR,WAAW,CAACkE,QAAQ,CAAC;QACjD,CAAC,CAAC,OAAO9H,CAAC,EAAE;UACV;UACA;UACA;UACA,IAAI,CAACA,CAAC,CAAC0I,MAAM,EAAE,MAAM1I,CAAC;UACtB,IAAIA,CAAC,CAAC0I,MAAM,CAACtV,QAAQ,CAAC,gBAAgB,CAAC,EACrC,MAAM,IAAIrB,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,uBAAuB,CAAC;UACtD,IAAIgL,CAAC,CAAC0I,MAAM,CAACtV,QAAQ,CAAC,UAAU,CAAC,EAC/B,MAAM,IAAIrB,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,0BAA0B,CAAC;UACzD,MAAMgL,CAAC;QACT;QACA,OAAO7K,MAAM;MACf;MAEA;MACA;MACAwT,gBAAgBA,CAACvN,KAAK,EAAE;QACtB,MAAMwN,MAAM,GAAG,IAAI,CAACpV,QAAQ,CAACqV,6BAA6B;QAE1D,OAAO,CAACD,MAAM,IACX,OAAOA,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACxN,KAAK,CAAE,IAC9C,OAAOwN,MAAM,KAAK,QAAQ,IACxB,IAAIlO,MAAM,KAAAnH,MAAA,CAAKxB,MAAM,CAAC4I,aAAa,CAACiO,MAAM,CAAC,QAAK,GAAG,CAAC,CAAEE,IAAI,CAAC1N,KAAK,CAAE;MACzE;MAEA;MACA;MACA;;MAEA,MAAM2N,yBAAyBA,CAAC5T,MAAM,EAAE6T,cAAc,EAAE;QACtD,IAAIA,cAAc,EAAE;UAClB,MAAM,IAAI,CAAC1W,KAAK,CAAC0P,WAAW,CAAC7M,MAAM,EAAE;YACnC8T,MAAM,EAAE;cACN,yCAAyC,EAAE,CAAC;cAC5C,qCAAqC,EAAE;YACzC,CAAC;YACDC,QAAQ,EAAE;cACR,6BAA6B,EAAEF;YACjC;UACF,CAAC,CAAC;QACJ;MACF;MAEAzL,sCAAsCA,CAAA,EAAG;QACvC;QACA;QACA;QACA;QACA;QACA;QACAxL,MAAM,CAACoS,OAAO,CAAC,YAAY;UACzB,MAAM7R,KAAK,GAAG,MAAM,IAAI,CAACA,KAAK,CAACgJ,IAAI,CAAC;YAClC,yCAAyC,EAAE;UAC7C,CAAC,EAAE;YACD9F,MAAM,EAAE;cACN,qCAAqC,EAAE;YACzC;UACF,CAAC,CAAC;UACFlD,KAAK,CAACsV,OAAO,CAAClS,IAAI,IAAI;YACpB,IAAI,CAACqT,yBAAyB,CAC5BrT,IAAI,CAAC2O,GAAG,EACR3O,IAAI,CAACgN,QAAQ,CAACC,MAAM,CAACwG,mBACvB;YACE;YAAA,CACC9W,IAAI,CAAC+W,CAAC,IAAIA,CAAC,CAAC,CACZC,KAAK,CAACb,GAAG,IAAI;cACZnV,OAAO,CAACiW,GAAG,CAACd,GAAG,CAAC;YAClB,CAAC,CAAC;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMe,qCAAqCA,CACzCC,WAAW,EACXC,WAAW,EACXzW,OAAO,EACP;QACAA,OAAO,GAAA5B,aAAA,KAAQ4B,OAAO,CAAE;QAExB,IAAIwW,WAAW,KAAK,UAAU,IAAIA,WAAW,KAAK,QAAQ,EAAE;UAC1D,MAAM,IAAIxU,KAAK,CACb,wEAAwE,GACtEwU,WAAW,CAAC;QAClB;QACA,IAAI,CAACrQ,MAAM,CAAC5C,IAAI,CAACkT,WAAW,EAAE,IAAI,CAAC,EAAE;UACnC,MAAM,IAAIzU,KAAK,6BAAAzB,MAAA,CACeiW,WAAW,qBAAkB,CAAC;QAC9D;;QAEA;QACA,MAAM/O,QAAQ,GAAG,CAAC,CAAC;QACnB,MAAMiP,YAAY,eAAAnW,MAAA,CAAeiW,WAAW,QAAK;;QAEjD;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,WAAW,KAAK,SAAS,IAAI,CAACG,KAAK,CAACF,WAAW,CAACxO,EAAE,CAAC,EAAE;UACvDR,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;UACzBA,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACiP,YAAY,CAAC,GAAGD,WAAW,CAACxO,EAAE;UACjDR,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACiP,YAAY,CAAC,GAAGE,QAAQ,CAACH,WAAW,CAACxO,EAAE,EAAE,EAAE,CAAC;QACjE,CAAC,MAAM;UACLR,QAAQ,CAACiP,YAAY,CAAC,GAAGD,WAAW,CAACxO,EAAE;QACzC;QACA,IAAIvF,IAAI,GAAG,MAAM,IAAI,CAACpD,KAAK,CAAC0D,YAAY,CAACyE,QAAQ,EAAE;UAACjF,MAAM,EAAE,IAAI,CAAChC,QAAQ,CAAC+B;QAAoB,CAAC,CAAC;QAChG;QACA;QACA,IAAI,CAACG,IAAI,IAAI,IAAI,CAACiK,kCAAkC,EAAE;UACpDjK,IAAI,GAAG,MAAM,IAAI,CAACiK,kCAAkC,CAAC;YAAC6J,WAAW;YAAEC,WAAW;YAAEzW;UAAO,CAAC,CAAC;QAC3F;;QAEA;QACA,IAAI,IAAI,CAACoM,wBAAwB,IAAI,EAAE,MAAM,IAAI,CAACA,wBAAwB,CAACoK,WAAW,EAAEC,WAAW,EAAE/T,IAAI,CAAC,CAAC,EAAE;UAC3G,MAAM,IAAI3D,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,iBAAiB,CAAC;QAChD;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI4P,IAAI,GAAGlP,IAAI,GAAG,CAAC,CAAC,GAAG1C,OAAO;QAC9B,IAAI,IAAI,CAACyM,oBAAoB,EAAE;UAC7BmF,IAAI,GAAG,MAAM,IAAI,CAACnF,oBAAoB,CAACzM,OAAO,EAAE0C,IAAI,CAAC;QACvD;QAEA,IAAIA,IAAI,EAAE;UACR,MAAMmS,wBAAwB,CAAC4B,WAAW,EAAE/T,IAAI,CAAC2O,GAAG,CAAC;UAErD,IAAIwF,QAAQ,GAAG,CAAC,CAAC;UACjB3W,MAAM,CAACC,IAAI,CAACsW,WAAW,CAAC,CAAC7B,OAAO,CAAC3U,GAAG,IAClC4W,QAAQ,aAAAtW,MAAA,CAAaiW,WAAW,OAAAjW,MAAA,CAAIN,GAAG,EAAG,GAAGwW,WAAW,CAACxW,GAAG,CAC9D,CAAC;;UAED;UACA;UACA4W,QAAQ,GAAAzY,aAAA,CAAAA,aAAA,KAAQyY,QAAQ,GAAKjF,IAAI,CAAE;UACnC,MAAM,IAAI,CAACtS,KAAK,CAAC0P,WAAW,CAACtM,IAAI,CAAC2O,GAAG,EAAE;YACrCyB,IAAI,EAAE+D;UACR,CAAC,CAAC;UAEF,OAAO;YACL1I,IAAI,EAAEqI,WAAW;YACjBrU,MAAM,EAAEO,IAAI,CAAC2O;UACf,CAAC;QACH,CAAC,MAAM;UACL;UACA3O,IAAI,GAAG;YAACgN,QAAQ,EAAE,CAAC;UAAC,CAAC;UACrBhN,IAAI,CAACgN,QAAQ,CAAC8G,WAAW,CAAC,GAAGC,WAAW;UACxC,MAAMtU,MAAM,GAAG,MAAM,IAAI,CAACuS,aAAa,CAAC9C,IAAI,EAAElP,IAAI,CAAC;UACnD,OAAO;YACLyL,IAAI,EAAEqI,WAAW;YACjBrU;UACF,CAAC;QACH;MACF;MAEA;AACF;AACA;AACA;AACA;MACE2U,sBAAsBA,CAAA,EAAG;QACvB,MAAMC,IAAI,GAAGC,cAAc,CAACC,UAAU,CAAC,IAAI,CAACC,wBAAwB,CAAC;QACrE,IAAI,CAACA,wBAAwB,GAAG,IAAI;QACpC,OAAOH,IAAI;MACb;MAEA;AACF;AACA;AACA;AACA;AACA;MACE9L,mBAAmBA,CAAA,EAAG;QACpB,IAAI,CAAC,IAAI,CAACiM,wBAAwB,EAAE;UAClC,IAAI,CAACA,wBAAwB,GAAGF,cAAc,CAACG,OAAO,CAAC;YACrDhV,MAAM,EAAE,IAAI;YACZiV,aAAa,EAAE,IAAI;YACnBjJ,IAAI,EAAE,QAAQ;YACdxM,IAAI,EAAEA,IAAI,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,gBAAgB,CAAC,CACrEvB,QAAQ,CAACuB,IAAI,CAAC;YACjBuQ,YAAY,EAAGA,YAAY,IAAK;UAClC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;QACd;MACF;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE,MAAMmF,uBAAuBA,CAACjP,KAAK,EAAE1F,IAAI,EAAEyI,GAAG,EAAEmM,MAAM,EAAa;QAAA,IAAXC,KAAK,GAAAlV,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA3B,SAAA,GAAA2B,SAAA,MAAG,CAAC,CAAC;QAChE,MAAMrC,OAAO,GAAG;UACdwX,EAAE,EAAEpP,KAAK;UACTkG,IAAI,EAAE,IAAI,CAACmJ,cAAc,CAACH,MAAM,CAAC,CAAChJ,IAAI,GAClC,MAAM,IAAI,CAACmJ,cAAc,CAACH,MAAM,CAAC,CAAChJ,IAAI,CAAC5L,IAAI,CAAC,GAC5C,IAAI,CAAC+U,cAAc,CAACnJ,IAAI;UAC5BoJ,OAAO,EAAE,MAAM,IAAI,CAACD,cAAc,CAACH,MAAM,CAAC,CAACI,OAAO,CAAChV,IAAI,EAAEyI,GAAG,EAAEoM,KAAK;QACrE,CAAC;QAED,IAAI,OAAO,IAAI,CAACE,cAAc,CAACH,MAAM,CAAC,CAACK,IAAI,KAAK,UAAU,EAAE;UAC1D3X,OAAO,CAAC2X,IAAI,GAAG,MAAM,IAAI,CAACF,cAAc,CAACH,MAAM,CAAC,CAACK,IAAI,CAACjV,IAAI,EAAEyI,GAAG,EAAEoM,KAAK,CAAC;QACzE;QAEA,IAAI,OAAO,IAAI,CAACE,cAAc,CAACH,MAAM,CAAC,CAACM,IAAI,KAAK,UAAU,EAAE;UAC1D5X,OAAO,CAAC4X,IAAI,GAAG,MAAM,IAAI,CAACH,cAAc,CAACH,MAAM,CAAC,CAACM,IAAI,CAAClV,IAAI,EAAEyI,GAAG,EAAEoM,KAAK,CAAC;QACzE;QAEA,IAAI,OAAO,IAAI,CAACE,cAAc,CAACI,OAAO,KAAK,QAAQ,EAAE;UACnD7X,OAAO,CAAC6X,OAAO,GAAG,IAAI,CAACJ,cAAc,CAACI,OAAO;QAC/C;QAEA,OAAO7X,OAAO;MAChB;MAEA,MAAM8X,kCAAkCA,CACtC/Q,SAAS,EACTgR,WAAW,EACX7P,UAAU,EACV8P,SAAS,EACT;QACA;QACA;QACA,MAAMC,SAAS,GAAG/X,MAAM,CAACwB,SAAS,CAAC4B,cAAc,CAACC,IAAI,CACpD,IAAI,CAACiH,iCAAiC,EACtCtC,UACF,CAAC;QAED,IAAIA,UAAU,IAAI,CAAC+P,SAAS,EAAE;UAC5B,MAAMC,YAAY,GAAG,MAAMnZ,MAAM,CAACO,KAAK,CACpCgJ,IAAI,CACH,IAAI,CAACxB,qCAAqC,CAACC,SAAS,EAAEmB,UAAU,CAAC,EACjE;YACE1F,MAAM,EAAE;cAAE6O,GAAG,EAAE;YAAE,CAAC;YAClB;YACA9I,KAAK,EAAE;UACT,CACF,CAAC,CACAC,UAAU,CAAC,CAAC;UAEf,IACE0P,YAAY,CAAC5V,MAAM,GAAG,CAAC;UACvB;UACC,CAAC0V,SAAS;UACT;UACA;UACAE,YAAY,CAAC5V,MAAM,GAAG,CAAC,IAAI4V,YAAY,CAAC,CAAC,CAAC,CAAC7G,GAAG,KAAK2G,SAAS,CAAC,EAC/D;YACA,IAAI,CAACvP,YAAY,IAAAlI,MAAA,CAAIwX,WAAW,qBAAkB,CAAC;UACrD;QACF;MACF;MAEA,MAAMI,6BAA6BA,CAAAC,IAAA,EAAqC;QAAA,IAApC;UAAE1V,IAAI;UAAE0F,KAAK;UAAED,QAAQ;UAAEnI;QAAQ,CAAC,GAAAoY,IAAA;QACpE,MAAMC,OAAO,GAAAja,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACRsE,IAAI,GACHyF,QAAQ,GAAG;UAAEA;QAAS,CAAC,GAAG,CAAC,CAAC,GAC5BC,KAAK,GAAG;UAAEuB,MAAM,EAAE,CAAC;YAAE2O,OAAO,EAAElQ,KAAK;YAAEmQ,QAAQ,EAAE;UAAM,CAAC;QAAE,CAAC,GAAG,CAAC,CAAC,CACnE;;QAED;QACA,MAAM,IAAI,CAACT,kCAAkC,CAAC,UAAU,EAAE,UAAU,EAAE3P,QAAQ,CAAC;QAC/E,MAAM,IAAI,CAAC2P,kCAAkC,CAAC,gBAAgB,EAAE,OAAO,EAAE1P,KAAK,CAAC;QAE/E,MAAMjG,MAAM,GAAG,MAAM,IAAI,CAACuS,aAAa,CAAC1U,OAAO,EAAEqY,OAAO,CAAC;QACzD;QACA;QACA,IAAI;UACF,MAAM,IAAI,CAACP,kCAAkC,CAAC,UAAU,EAAE,UAAU,EAAE3P,QAAQ,EAAEhG,MAAM,CAAC;UACvF,MAAM,IAAI,CAAC2V,kCAAkC,CAAC,gBAAgB,EAAE,OAAO,EAAE1P,KAAK,EAAEjG,MAAM,CAAC;QACzF,CAAC,CAAC,OAAOqW,EAAE,EAAE;UACX;UACA,MAAMzZ,MAAM,CAACO,KAAK,CAACmZ,WAAW,CAACtW,MAAM,CAAC;UACtC,MAAMqW,EAAE;QACV;QACA,OAAOrW,MAAM;MACf;IA2BF;IAEA;IACA;IACA;IACA,MAAM4K,0BAA0B,GAAGA,CAACtM,UAAU,EAAEoM,OAAO,KAAK;MAC1D,MAAM6L,aAAa,GAAGC,KAAK,CAACC,KAAK,CAAC/L,OAAO,CAAC;MAC1C6L,aAAa,CAACjY,UAAU,GAAGA,UAAU;MACrC,OAAOiY,aAAa;IACtB,CAAC;IAED,MAAMhK,cAAc,GAAG,MAAAA,CAAOP,IAAI,EAAEM,EAAE,KAAK;MACzC,IAAIP,MAAM;MACV,IAAI;QACFA,MAAM,GAAG,MAAMO,EAAE,CAAC,CAAC;MACrB,CAAC,CACD,OAAOzB,CAAC,EAAE;QACRkB,MAAM,GAAG;UAAC5N,KAAK,EAAE0M;QAAC,CAAC;MACrB;MAEA,IAAIkB,MAAM,IAAI,CAACA,MAAM,CAACC,IAAI,IAAIA,IAAI,EAChCD,MAAM,CAACC,IAAI,GAAGA,IAAI;MAEpB,OAAOD,MAAM;IACf,CAAC;IAED,MAAMjE,yBAAyB,GAAG9K,QAAQ,IAAI;MAC5CA,QAAQ,CAACyP,oBAAoB,CAAC,QAAQ,EAAE,UAAU5O,OAAO,EAAE;QACzD,OAAO6Y,yBAAyB,CAACtV,IAAI,CAAC,IAAI,EAAEpE,QAAQ,EAAEa,OAAO,CAAC;MAChE,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAM6Y,yBAAyB,GAAG,MAAAA,CAAO1Z,QAAQ,EAAEa,OAAO,KAAK;MAC7D,IAAI,CAACA,OAAO,CAAC2P,MAAM,EACjB,OAAOjP,SAAS;MAElB8F,KAAK,CAACxG,OAAO,CAAC2P,MAAM,EAAElJ,MAAM,CAAC;MAE7B,MAAMyI,WAAW,GAAG/P,QAAQ,CAACyO,eAAe,CAAC5N,OAAO,CAAC2P,MAAM,CAAC;;MAE5D;MACA;MACA;MACA,IAAIjN,IAAI,GAAG,MAAMvD,QAAQ,CAACG,KAAK,CAAC0D,YAAY,CAC1C;QAAC,yCAAyC,EAAEkM;MAAW,CAAC,EACxD;QAAC1M,MAAM,EAAE;UAAC,+BAA+B,EAAE;QAAC;MAAC,CAAC,CAAC;MAEjD,IAAI,CAAEE,IAAI,EAAE;QACV;QACA;QACA;QACA;QACA;QACAA,IAAI,GAAI,MAAMvD,QAAQ,CAACG,KAAK,CAAC0D,YAAY,CAAC;UACtC8E,GAAG,EAAE,CACH;YAAC,yCAAyC,EAAEoH;UAAW,CAAC,EACxD;YAAC,mCAAmC,EAAElP,OAAO,CAAC2P;UAAM,CAAC;QAEzD,CAAC;QACD;QACA;UAACnN,MAAM,EAAE;YAAC,6BAA6B,EAAE;UAAC;QAAC,CAAC,CAAC;MACjD;MAEA,IAAI,CAAEE,IAAI,EACR,OAAO;QACLpC,KAAK,EAAE,IAAIvB,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,4DAA4D;MAC3F,CAAC;;MAEH;MACA;MACA;MACA,IAAI8W,qBAAqB;MACzB,IAAInO,KAAK,GAAG,MAAMjI,IAAI,CAACgN,QAAQ,CAACC,MAAM,CAACC,WAAW,CAACtH,IAAI,CAACqC,KAAK,IAC3DA,KAAK,CAACuE,WAAW,KAAKA,WACxB,CAAC;MACD,IAAIvE,KAAK,EAAE;QACTmO,qBAAqB,GAAG,KAAK;MAC/B,CAAC,MAAM;QACJnO,KAAK,GAAG,MAAMjI,IAAI,CAACgN,QAAQ,CAACC,MAAM,CAACC,WAAW,CAACtH,IAAI,CAACqC,KAAK,IACxDA,KAAK,CAACA,KAAK,KAAK3K,OAAO,CAAC2P,MAC1B,CAAC;QACDmJ,qBAAqB,GAAG,IAAI;MAC9B;MAEA,MAAMhL,YAAY,GAAG3O,QAAQ,CAACkG,gBAAgB,CAACsF,KAAK,CAACrF,IAAI,CAAC;MAC1D,IAAI,IAAIC,IAAI,CAAC,CAAC,IAAIuI,YAAY,EAC5B,OAAO;QACL3L,MAAM,EAAEO,IAAI,CAAC2O,GAAG;QAChB/Q,KAAK,EAAE,IAAIvB,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,gDAAgD;MAC/E,CAAC;;MAEH;MACA,IAAI8W,qBAAqB,EAAE;QACzB;QACA;QACA;QACA;QACA;QACA,MAAM3Z,QAAQ,CAACG,KAAK,CAAC0P,WAAW,CAC9B;UACEqC,GAAG,EAAE3O,IAAI,CAAC2O,GAAG;UACb,mCAAmC,EAAErR,OAAO,CAAC2P;QAC/C,CAAC,EACD;UAACiD,SAAS,EAAE;YACR,6BAA6B,EAAE;cAC7B,aAAa,EAAE1D,WAAW;cAC1B,MAAM,EAAEvE,KAAK,CAACrF;YAChB;UACF;QAAC,CACL,CAAC;;QAED;QACA;QACA;QACA,MAAMnG,QAAQ,CAACG,KAAK,CAAC0P,WAAW,CAACtM,IAAI,CAAC2O,GAAG,EAAE;UACzCpC,KAAK,EAAE;YACL,6BAA6B,EAAE;cAAE,OAAO,EAAEjP,OAAO,CAAC2P;YAAO;UAC3D;QACF,CAAC,CAAC;MACJ;MAEA,OAAO;QACLxN,MAAM,EAAEO,IAAI,CAAC2O,GAAG;QAChB9D,iBAAiB,EAAE;UACjB5C,KAAK,EAAE3K,OAAO,CAAC2P,MAAM;UACrBrK,IAAI,EAAEqF,KAAK,CAACrF;QACd;MACF,CAAC;IACH,CAAC;IAED,MAAM2O,mBAAmB,GACvB,MAAAA,CACE9U,QAAQ,EACR0U,eAAe,EACfE,WAAW,EACX5R,MAAM,KACH;MACH;MACA,IAAI4W,QAAQ,GAAG,KAAK;MACpB,MAAM3E,UAAU,GAAGjS,MAAM,GAAG;QAAEkP,GAAG,EAAElP;MAAO,CAAC,GAAG,CAAC,CAAC;MAChD;MACA,IAAI4R,WAAW,CAAC,iCAAiC,CAAC,EAAE;QAClDgF,QAAQ,GAAG,IAAI;MACjB;MACA,IAAIC,YAAY,GAAG;QACjBlR,GAAG,EAAE,CACH;UAAE,8BAA8B,EAAE;YAAEuM,GAAG,EAAER;UAAgB;QAAE,CAAC,EAC5D;UAAE,8BAA8B,EAAE;YAAEQ,GAAG,EAAE,CAACR;UAAgB;QAAE,CAAC;MAEjE,CAAC;MACD,IAAIkF,QAAQ,EAAE;QACZC,YAAY,GAAG;UACblR,GAAG,EAAE,CACH;YAAE,+BAA+B,EAAE;cAAEuM,GAAG,EAAER;YAAgB;UAAE,CAAC,EAC7D;YAAE,+BAA+B,EAAE;cAAEQ,GAAG,EAAE,CAACR;YAAgB;UAAE,CAAC;QAElE,CAAC;MACH;MACA,MAAMoF,YAAY,GAAG;QAAEpR,IAAI,EAAE,CAACkM,WAAW,EAAEiF,YAAY;MAAE,CAAC;MAC1D,IAAID,QAAQ,EAAE;QACZ,MAAM5Z,QAAQ,CAACG,KAAK,CAAC0P,WAAW,CAAA5Q,aAAA,CAAAA,aAAA,KAAMgW,UAAU,GAAK6E,YAAY,GAAI;UACnEhD,MAAM,EAAE;YACN,0BAA0B,EAAE;UAC9B;QACF,CAAC,EAAE;UAAE3B,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,MAAMnV,QAAQ,CAACG,KAAK,CAAC0P,WAAW,CAAA5Q,aAAA,CAAAA,aAAA,KAAMgW,UAAU,GAAK6E,YAAY,GAAI;UACnEhD,MAAM,EAAE;YACN,yBAAyB,EAAE;UAC7B;QACF,CAAC,EAAE;UAAE3B,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB;IAEF,CAAC;IAEH,MAAMpK,uBAAuB,GAAG/K,QAAQ,IAAI;MAC1CA,QAAQ,CAACqV,mBAAmB,GAAGzV,MAAM,CAACma,WAAW,CAAC,YAAY;QAC7D,MAAM/Z,QAAQ,CAACgV,aAAa,CAAC,CAAC;QAC9B,MAAMhV,QAAQ,CAACyU,0BAA0B,CAAC,CAAC;QAC3C,MAAMzU,QAAQ,CAAC+U,2BAA2B,CAAC,CAAC;MAC7C,CAAC,EAAErU,yBAAyB,CAAC;IAC/B,CAAC;IAED,MAAM4D,eAAe,IAAAsC,oBAAA,GAAGvC,OAAO,CAAC,kBAAkB,CAAC,cAAAuC,oBAAA,uBAA3BA,oBAAA,CAA6BtC,eAAe;;IAEpE;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMoR,wBAAwB,GAAGA,CAAC4B,WAAW,EAAEtU,MAAM,KAAK;MACxDjC,MAAM,CAACC,IAAI,CAACsW,WAAW,CAAC,CAAC7B,OAAO,CAAC3U,GAAG,IAAI;QACtC,IAAIsL,KAAK,GAAGkL,WAAW,CAACxW,GAAG,CAAC;QAC5B,IAAIwD,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE0V,QAAQ,CAAC5N,KAAK,CAAC,EAClCA,KAAK,GAAG9H,eAAe,CAACkN,IAAI,CAAClN,eAAe,CAAC2V,IAAI,CAAC7N,KAAK,CAAC,EAAEpJ,MAAM,CAAC;QACnEsU,WAAW,CAACxW,GAAG,CAAC,GAAGsL,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;;IAED;IACA;IACA,MAAMwJ,qBAAqB,GAAGA,CAAC/U,OAAO,EAAE0C,IAAI,KAAK;MAC/C,IAAI1C,OAAO,CAAC0J,OAAO,EACjBhH,IAAI,CAACgH,OAAO,GAAG1J,OAAO,CAAC0J,OAAO;MAChC,OAAOhH,IAAI;IACb,CAAC;;IAED;IACA,SAAS2H,0BAA0BA,CAAC3H,IAAI,EAAE;MACxC,MAAMkT,MAAM,GAAG,IAAI,CAACpV,QAAQ,CAACqV,6BAA6B;MAC1D,IAAI,CAACD,MAAM,EAAE;QACX,OAAO,IAAI;MACb;MAEA,IAAIyD,WAAW,GAAG,KAAK;MACvB,IAAI3W,IAAI,CAACiH,MAAM,IAAIjH,IAAI,CAACiH,MAAM,CAACrH,MAAM,GAAG,CAAC,EAAE;QACzC+W,WAAW,GAAG3W,IAAI,CAACiH,MAAM,CAAC6H,MAAM,CAC9B,CAACC,IAAI,EAAErJ,KAAK,KAAKqJ,IAAI,IAAI,IAAI,CAACkE,gBAAgB,CAACvN,KAAK,CAACkQ,OAAO,CAAC,EAAE,KACjE,CAAC;MACH,CAAC,MAAM,IAAI5V,IAAI,CAACgN,QAAQ,IAAIxP,MAAM,CAACoZ,MAAM,CAAC5W,IAAI,CAACgN,QAAQ,CAAC,CAACpN,MAAM,GAAG,CAAC,EAAE;QACnE;QACA+W,WAAW,GAAGnZ,MAAM,CAACoZ,MAAM,CAAC5W,IAAI,CAACgN,QAAQ,CAAC,CAAC8B,MAAM,CAC/C,CAACC,IAAI,EAAErB,OAAO,KAAKA,OAAO,CAAChI,KAAK,IAAI,IAAI,CAACuN,gBAAgB,CAACvF,OAAO,CAAChI,KAAK,CAAC,EACxE,KACF,CAAC;MACH;MAEA,IAAIiR,WAAW,EAAE;QACf,OAAO,IAAI;MACb;MAEA,IAAI,OAAOzD,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,IAAI7W,MAAM,CAACiD,KAAK,CAAC,GAAG,MAAAzB,MAAA,CAAMqV,MAAM,oBAAiB,CAAC;MAC1D,CAAC,MAAM;QACL,MAAM,IAAI7W,MAAM,CAACiD,KAAK,CAAC,GAAG,EAAE,mCAAmC,CAAC;MAClE;IACF;IAEA,MAAM+J,oBAAoB,GAAG,MAAMzM,KAAK,IAAI;MAC1C;MACA;MACA;MACAA,KAAK,CAACia,KAAK,CAAC;QACV;QACA;QACAhH,MAAM,EAAEA,CAACpQ,MAAM,EAAEO,IAAI,EAAEF,MAAM,EAAEgX,QAAQ,KAAK;UAC1C;UACA,IAAI9W,IAAI,CAAC2O,GAAG,KAAKlP,MAAM,EAAE;YACvB,OAAO,KAAK;UACd;;UAEA;UACA;UACA;UACA,IAAIK,MAAM,CAACF,MAAM,KAAK,CAAC,IAAIE,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAClD,OAAO,KAAK;UACd;UAEA,OAAO,IAAI;QACb,CAAC;QACDiX,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;MACjB,CAAC,CAAC;;MAEF;MACA,MAAMna,KAAK,CAACoa,gBAAgB,CAAC,UAAU,EAAE;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAK,CAAC,CAAC;MACxE,MAAMta,KAAK,CAACoa,gBAAgB,CAAC,gBAAgB,EAAE;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAK,CAAC,CAAC;MAC9E,MAAMta,KAAK,CAACoa,gBAAgB,CAAC,yCAAyC,EACpE;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAK,CAAC,CAAC;MACjC,MAAMta,KAAK,CAACoa,gBAAgB,CAAC,mCAAmC,EAC9D;QAAEC,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAK,CAAC,CAAC;MACjC;MACA;MACA,MAAMta,KAAK,CAACoa,gBAAgB,CAAC,yCAAyC,EACpE;QAAEE,MAAM,EAAE;MAAK,CAAC,CAAC;MACnB;MACA,MAAMta,KAAK,CAACoa,gBAAgB,CAAC,kCAAkC,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC,CAAC;MAClF;MACA,MAAMta,KAAK,CAACoa,gBAAgB,CAAC,8BAA8B,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC,CAAC;MAC9E,MAAMta,KAAK,CAACoa,gBAAgB,CAAC,+BAA+B,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC,CAAC;IACjF,CAAC;;IAGD;IACA,MAAMtS,iCAAiC,GAAGN,MAAM,IAAI;MAClD,IAAI6S,YAAY,GAAG,CAAC,EAAE,CAAC;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9S,MAAM,CAAC1E,MAAM,EAAEwX,CAAC,EAAE,EAAE;QACtC,MAAMC,EAAE,GAAG/S,MAAM,CAACgT,MAAM,CAACF,CAAC,CAAC;QAC3BD,YAAY,GAAG,EAAE,CAACtZ,MAAM,CAAC,GAAIsZ,YAAY,CAACtS,GAAG,CAACN,MAAM,IAAI;UACtD,MAAMgT,aAAa,GAAGF,EAAE,CAACG,WAAW,CAAC,CAAC;UACtC,MAAMC,aAAa,GAAGJ,EAAE,CAACK,WAAW,CAAC,CAAC;UACtC;UACA,IAAIH,aAAa,KAAKE,aAAa,EAAE;YACnC,OAAO,CAAClT,MAAM,GAAG8S,EAAE,CAAC;UACtB,CAAC,MAAM;YACL,OAAO,CAAC9S,MAAM,GAAGgT,aAAa,EAAEhT,MAAM,GAAGkT,aAAa,CAAC;UACzD;QACF,CAAC,CAAE,CAAC;MACN;MACA,OAAON,YAAY;IACrB,CAAC;IAAAta,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/accounts-base.js", "sourcesContent": ["import { AccountsServer } from \"./accounts_server.js\";\n\n/**\n * @namespace Accounts\n * @summary The namespace for all server-side accounts-related methods.\n */\nAccounts = new AccountsServer(Meteor.server, { ...Meteor.settings.packages?.accounts, ...Meteor.settings.packages?.['accounts-base'] });\n// TODO[FIBERS]: I need TLA\nAccounts.init().then();\n// Users table. Don't use the normal autopublish, since we want to hide\n// some fields. Code to autopublish this is in accounts_server.js.\n// XXX Allow users to configure this collection name.\n\n/**\n * @summary A [Mongo.Collection](#collections) containing user documents.\n * @locus Anywhere\n * @type {Mongo.Collection}\n * @importFromPackage meteor\n */\nMeteor.users = Accounts.users;\n\nexport {\n  // Since this file is the main module for the server version of the\n  // accounts-base package, properties of non-entry-point modules need to\n  // be re-exported in order to be accessible to modules that import the\n  // accounts-base package.\n  AccountsServer\n};\n", "import { Meteor } from 'meteor/meteor';\n\n// config option keys\nconst VALID_CONFIG_KEYS = [\n  'sendVerificationEmail',\n  'forbidClientAccountCreation',\n  'restrictCreationByEmailDomain',\n  'loginExpiration',\n  'loginExpirationInDays',\n  'oauthSecretKey',\n  'passwordResetTokenExpirationInDays',\n  'passwordResetTokenExpiration',\n  'passwordEnrollTokenExpirationInDays',\n  'passwordEnrollTokenExpiration',\n  'ambiguousErrorMessages',\n  'bcryptRounds',\n  'argon2Enabled',\n  'argon2Type',\n  'argon2TimeCost',\n  'argon2MemoryCost',\n  'argon2Parallelism',\n  'defaultFieldSelector',\n  'collection',\n  'loginTokenExpirationHours',\n  'tokenSequenceLength',\n  'clientStorage',\n  'ddpUrl',\n  'connection',\n];\n\n/**\n * @summary Super-constructor for AccountsClient and AccountsServer.\n * @locus Anywhere\n * @class AccountsCommon\n * @instancename accountsClientOrServer\n * @param options {Object} an object with fields:\n * - connection {Object} Optional DDP connection to reuse.\n * - ddpUrl {String} Optional URL for creating a new DDP connection.\n * - collection {String|Mongo.Collection} The name of the Mongo.Collection\n *     or the Mongo.Collection object to hold the users.\n */\nexport class AccountsCommon {\n  constructor(options) {\n    // Validate config options keys\n    for (const key of Object.keys(options)) {\n      if (!VALID_CONFIG_KEYS.includes(key)) {\n        console.error(`Accounts.config: Invalid key: ${key}`);\n      }\n    }\n\n    // Currently this is read directly by packages like accounts-password\n    // and accounts-ui-unstyled.\n    this._options = options || {};\n\n    // Note that setting this.connection = null causes this.users to be a\n    // LocalCollection, which is not what we want.\n    this.connection = undefined;\n    this._initConnection(options || {});\n\n    // There is an allow call in accounts_server.js that restricts writes to\n    // this collection.\n    this.users = this._initializeCollection(options || {});\n\n    // Callback exceptions are printed with Meteor._debug and ignored.\n    this._onLoginHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLogin callback',\n    });\n\n    this._onLoginFailureHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLoginFailure callback',\n    });\n\n    this._onLogoutHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLogout callback',\n    });\n\n    // Expose for testing.\n    this.DEFAULT_LOGIN_EXPIRATION_DAYS = DEFAULT_LOGIN_EXPIRATION_DAYS;\n    this.LOGIN_UNEXPIRING_TOKEN_DAYS = LOGIN_UNEXPIRING_TOKEN_DAYS;\n\n    // Thrown when the user cancels the login process (eg, closes an oauth\n    // popup, declines retina scan, etc)\n    const lceName = 'Accounts.LoginCancelledError';\n    this.LoginCancelledError = Meteor.makeErrorType(lceName, function(\n      description\n    ) {\n      this.message = description;\n    });\n    this.LoginCancelledError.prototype.name = lceName;\n\n    // This is used to transmit specific subclass errors over the wire. We\n    // should come up with a more generic way to do this (eg, with some sort of\n    // symbolic error code rather than a number).\n    this.LoginCancelledError.numericError = 0x8acdc2f;\n  }\n\n  _initializeCollection(options) {\n    if (options.collection && typeof options.collection !== 'string' && !(options.collection instanceof Mongo.Collection)) {\n      throw new Meteor.Error('Collection parameter can be only of type string or \"Mongo.Collection\"');\n    }\n\n    let collectionName = 'users';\n    if (typeof options.collection === 'string') {\n      collectionName = options.collection;\n    }\n\n    let collection;\n    if (options.collection instanceof Mongo.Collection) {\n      collection = options.collection;\n    } else {\n      collection = new Mongo.Collection(collectionName, {\n        _preventAutopublish: true,\n        connection: this.connection,\n      });\n    }\n\n    return collection;\n  }\n\n  /**\n   * @summary Get the current user id, or `null` if no user is logged in. A reactive data source.\n   * @locus Anywhere\n   */\n  userId() {\n    throw new Error('userId method not implemented');\n  }\n\n  // merge the defaultFieldSelector with an existing options object\n  _addDefaultFieldSelector(options = {}) {\n    // this will be the most common case for most people, so make it quick\n    if (!this._options.defaultFieldSelector) return options;\n\n    // if no field selector then just use defaultFieldSelector\n    if (!options.fields)\n      return {\n        ...options,\n        fields: this._options.defaultFieldSelector,\n      };\n\n    // if empty field selector then the full user object is explicitly requested, so obey\n    const keys = Object.keys(options.fields);\n    if (!keys.length) return options;\n\n    // if the requested fields are +ve then ignore defaultFieldSelector\n    // assume they are all either +ve or -ve because Mongo doesn't like mixed\n    if (!!options.fields[keys[0]]) return options;\n\n    // The requested fields are -ve.\n    // If the defaultFieldSelector is +ve then use requested fields, otherwise merge them\n    const keys2 = Object.keys(this._options.defaultFieldSelector);\n    return this._options.defaultFieldSelector[keys2[0]]\n      ? options\n      : {\n          ...options,\n          fields: {\n            ...options.fields,\n            ...this._options.defaultFieldSelector,\n          },\n        };\n  }\n\n  /**\n   * @summary Get the current user record, or `null` if no user is logged in. A reactive data source. In the server this fuction returns a promise.\n   * @locus Anywhere\n   * @param {Object} [options]\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   */\n  user(options) {\n    if (Meteor.isServer) {\n      console.warn([\n        \"`Meteor.user()` is deprecated on the server side.\",\n        \"    To fetch the current user record on the server,\",\n        \"    use `Meteor.userAsync()` instead.\",\n      ].join(\"\\n\"));\n    }\n\n    const self = this;\n    const userId = self.userId();\n    const findOne = (...args) => Meteor.isClient\n      ? self.users.findOne(...args)\n      : self.users.findOneAsync(...args);\n    return userId\n      ? findOne(userId, this._addDefaultFieldSelector(options))\n      : null;\n  }\n\n  /**\n   * @summary Get the current user record, or `null` if no user is logged in.\n   * @locus Anywhere\n   * @param {Object} [options]\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   */\n  async userAsync(options) {\n    const userId = this.userId();\n    return userId\n      ? this.users.findOneAsync(userId, this._addDefaultFieldSelector(options))\n      : null;\n  }\n\n  /**\n   * @summary Set global accounts options. You can also set these in `Meteor.settings.packages.accounts` without the need to call this function.\n   * @locus Anywhere\n   * @param {Object} options\n   * @param {Boolean} options.sendVerificationEmail New users with an email address will receive an address verification email.\n   * @param {Boolean} options.forbidClientAccountCreation Calls to [`createUser`](#accounts_createuser) from the client will be rejected. In addition, if you are using [accounts-ui](#accountsui), the \"Create account\" link will not be available.\n   * @param {String | Function} options.restrictCreationByEmailDomain If set to a string, only allows new users if the domain part of their email address matches the string. If set to a function, only allows new users if the function returns true.  The function is passed the full email address of the proposed new user.  Works with password-based sign-in and external services that expose email addresses (Google, Facebook, GitHub). All existing users still can log in after enabling this option. Example: `Accounts.config({ restrictCreationByEmailDomain: 'school.edu' })`.\n   * @param {Number} options.loginExpiration The number of milliseconds from when a user logs in until their token expires and they are logged out, for a more granular control. If `loginExpirationInDays` is set, it takes precedent.\n   * @param {Number} options.loginExpirationInDays The number of days from when a user logs in until their token expires and they are logged out. Defaults to 90. Set to `null` to disable login expiration.\n   * @param {String} options.oauthSecretKey When using the `oauth-encryption` package, the 16 byte key using to encrypt sensitive account credentials in the database, encoded in base64.  This option may only be specified on the server.  See packages/oauth-encryption/README.md for details.\n   * @param {Number} options.passwordResetTokenExpirationInDays The number of days from when a link to reset password is sent until token expires and user can't reset password with the link anymore. Defaults to 3.\n   * @param {Number} options.passwordResetTokenExpiration The number of milliseconds from when a link to reset password is sent until token expires and user can't reset password with the link anymore. If `passwordResetTokenExpirationInDays` is set, it takes precedent.\n   * @param {Number} options.passwordEnrollTokenExpirationInDays The number of days from when a link to set initial password is sent until token expires and user can't set password with the link anymore. Defaults to 30.\n   * @param {Number} options.passwordEnrollTokenExpiration The number of milliseconds from when a link to set initial password is sent until token expires and user can't set password with the link anymore. If `passwordEnrollTokenExpirationInDays` is set, it takes precedent.\n   * @param {Boolean} options.ambiguousErrorMessages Return ambiguous error messages from login failures to prevent user enumeration. Defaults to `true`.\n   * @param {Number} options.bcryptRounds Allows override of number of bcrypt rounds (aka work factor) used to store passwords. The default is 10.\n   * @param {Boolean} options.argon2Enabled Enable argon2 algorithm usage in replacement for bcrypt. The default is `false`.\n   * @param {'argon2id' | 'argon2i' | 'argon2d'} options.argon2Type Allows override of the argon2 algorithm type. The default is `argon2id`.\n   * @param {Number} options.argon2TimeCost Allows override of number of argon2 iterations (aka time cost) used to store passwords. The default is 2.\n   * @param {Number} options.argon2MemoryCost Allows override of the amount of memory (in KiB) used by the argon2 algorithm. The default is 19456 (19MB).\n   * @param {Number} options.argon2Parallelism Allows override of the number of threads used by the argon2 algorithm. The default is 1.\n   * @param {MongoFieldSpecifier} options.defaultFieldSelector To exclude by default large custom fields from `Meteor.user()` and `Meteor.findUserBy...()` functions when called without a field selector, and all `onLogin`, `onLoginFailure` and `onLogout` callbacks.  Example: `Accounts.config({ defaultFieldSelector: { myBigArray: 0 }})`. Beware when using this. If, for instance, you do not include `email` when excluding the fields, you can have problems with functions like `forgotPassword` that will break because they won't have the required data available. It's recommend that you always keep the fields `_id`, `username`, and `email`.\n   * @param {String|Mongo.Collection} options.collection A collection name or a Mongo.Collection object to hold the users.\n   * @param {Number} options.loginTokenExpirationHours When using the package `accounts-2fa`, use this to set the amount of time a token sent is valid. As it's just a number, you can use, for example, 0.5 to make the token valid for just half hour. The default is 1 hour.\n   * @param {Number} options.tokenSequenceLength When using the package `accounts-2fa`, use this to the size of the token sequence generated. The default is 6.\n   * @param {'session' | 'local'} options.clientStorage By default login credentials are stored in local storage, setting this to true will switch to using session storage.\n   */\n  config(options) {\n    // We don't want users to accidentally only call Accounts.config on the\n    // client, where some of the options will have partial effects (eg removing\n    // the \"create account\" button from accounts-ui if forbidClientAccountCreation\n    // is set, or redirecting Google login to a specific-domain page) without\n    // having their full effects.\n    if (Meteor.isServer) {\n      __meteor_runtime_config__.accountsConfigCalled = true;\n    } else if (!__meteor_runtime_config__.accountsConfigCalled) {\n      // XXX would be nice to \"crash\" the client and replace the UI with an error\n      // message, but there's no trivial way to do this.\n      Meteor._debug(\n        'Accounts.config was called on the client but not on the ' +\n          'server; some configuration options may not take effect.'\n      );\n    }\n\n    // We need to validate the oauthSecretKey option at the time\n    // Accounts.config is called. We also deliberately don't store the\n    // oauthSecretKey in Accounts._options.\n    if (Object.prototype.hasOwnProperty.call(options, 'oauthSecretKey')) {\n      if (Meteor.isClient) {\n        throw new Error(\n          'The oauthSecretKey option may only be specified on the server'\n        );\n      }\n      if (!Package['oauth-encryption']) {\n        throw new Error(\n          'The oauth-encryption package must be loaded to set oauthSecretKey'\n        );\n      }\n      Package['oauth-encryption'].OAuthEncryption.loadKey(\n        options.oauthSecretKey\n      );\n      options = { ...options };\n      delete options.oauthSecretKey;\n    }\n\n    // Validate config options keys\n    for (const key of Object.keys(options)) {\n      if (!VALID_CONFIG_KEYS.includes(key)) {\n        console.error(`Accounts.config: Invalid key: ${key}`);\n      }\n    }\n\n    // set values in Accounts._options\n    for (const key of VALID_CONFIG_KEYS) {\n      if (key in options) {\n        if (key in this._options) {\n          if (key !== 'collection' && (Meteor.isTest && key !== 'clientStorage')) {\n            throw new Meteor.Error(`Can't set \\`${key}\\` more than once`);\n          }\n        }\n        this._options[key] = options[key];\n      }\n    }\n\n    if (options.collection && options.collection !== this.users._name && options.collection !== this.users) {\n      this.users = this._initializeCollection(options);\n    }\n  }\n\n  /**\n   * @summary Register a callback to be called after a login attempt succeeds.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called when login is successful.\n   *                        The callback receives a single object that\n   *                        holds login details. This object contains the login\n   *                        result type (password, resume, etc.) on both the\n   *                        client and server. `onLogin` callbacks registered\n   *                        on the server also receive extra data, such\n   *                        as user details, connection information, etc.\n   */\n  onLogin(func) {\n    let ret = this._onLoginHook.register(func);\n    // call the just registered callback if already logged in\n    this._startupCallback(ret.callback);\n    return ret;\n  }\n\n  /**\n   * @summary Register a callback to be called after a login attempt fails.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called after the login has failed.\n   */\n  onLoginFailure(func) {\n    return this._onLoginFailureHook.register(func);\n  }\n\n  /**\n   * @summary Register a callback to be called after a logout attempt succeeds.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called when logout is successful.\n   */\n  onLogout(func) {\n    return this._onLogoutHook.register(func);\n  }\n\n  _initConnection(options) {\n    if (!Meteor.isClient) {\n      return;\n    }\n\n    // The connection used by the Accounts system. This is the connection\n    // that will get logged in by Meteor.login(), and this is the\n    // connection whose login state will be reflected by Meteor.userId().\n    //\n    // It would be much preferable for this to be in accounts_client.js,\n    // but it has to be here because it's needed to create the\n    // Meteor.users collection.\n    if (options.connection) {\n      this.connection = options.connection;\n    } else if (options.ddpUrl) {\n      this.connection = DDP.connect(options.ddpUrl);\n    } else if (\n      typeof __meteor_runtime_config__ !== 'undefined' &&\n      __meteor_runtime_config__.ACCOUNTS_CONNECTION_URL\n    ) {\n      // Temporary, internal hook to allow the server to point the client\n      // to a different authentication server. This is for a very\n      // particular use case that comes up when implementing a oauth\n      // server. Unsupported and may go away at any point in time.\n      //\n      // We will eventually provide a general way to use account-base\n      // against any DDP connection, not just one special one.\n      this.connection = DDP.connect(\n        __meteor_runtime_config__.ACCOUNTS_CONNECTION_URL\n      );\n    } else {\n      this.connection = Meteor.connection;\n    }\n  }\n\n  _getTokenLifetimeMs() {\n    // When loginExpirationInDays is set to null, we'll use a really high\n    // number of days (LOGIN_UNEXPIRABLE_TOKEN_DAYS) to simulate an\n    // unexpiring token.\n    const loginExpirationInDays =\n      this._options.loginExpirationInDays === null\n        ? LOGIN_UNEXPIRING_TOKEN_DAYS\n        : this._options.loginExpirationInDays;\n    return (\n      this._options.loginExpiration ||\n      (loginExpirationInDays || DEFAULT_LOGIN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _getPasswordResetTokenLifetimeMs() {\n    return (\n      this._options.passwordResetTokenExpiration ||\n      (this._options.passwordResetTokenExpirationInDays ||\n        DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _getPasswordEnrollTokenLifetimeMs() {\n    return (\n      this._options.passwordEnrollTokenExpiration ||\n      (this._options.passwordEnrollTokenExpirationInDays ||\n        DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _tokenExpiration(when) {\n    // We pass when through the Date constructor for backwards compatibility;\n    // `when` used to be a number.\n    return new Date(new Date(when).getTime() + this._getTokenLifetimeMs());\n  }\n\n  _tokenExpiresSoon(when) {\n    let minLifetimeMs = 0.1 * this._getTokenLifetimeMs();\n    const minLifetimeCapMs = MIN_TOKEN_LIFETIME_CAP_SECS * 1000;\n    if (minLifetimeMs > minLifetimeCapMs) {\n      minLifetimeMs = minLifetimeCapMs;\n    }\n    return new Date() > new Date(when) - minLifetimeMs;\n  }\n\n  // No-op on the server, overridden on the client.\n  _startupCallback(callback) {}\n}\n\n// Note that Accounts is defined separately in accounts_client.js and\n// accounts_server.js.\n\n/**\n * @summary Get the current user id, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n */\nMeteor.userId = () => Accounts.userId();\n\n/**\n * @summary Get the current user record, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n */\nMeteor.user = options => Accounts.user(options);\n\n/**\n * @summary Get the current user record, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n */\nMeteor.userAsync = options => Accounts.userAsync(options);\n\n// how long (in days) until a login token expires\nconst DEFAULT_LOGIN_EXPIRATION_DAYS = 90;\n// how long (in days) until reset password token expires\nconst DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS = 3;\n// how long (in days) until enrol password token expires\nconst DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS = 30;\n// Clients don't try to auto-login with a token that is going to expire within\n// .1 * DEFAULT_LOGIN_EXPIRATION_DAYS, capped at MIN_TOKEN_LIFETIME_CAP_SECS.\n// Tries to avoid abrupt disconnects from expiring tokens.\nconst MIN_TOKEN_LIFETIME_CAP_SECS = 3600; // one hour\n// how often (in milliseconds) we check for expired tokens\nexport const EXPIRE_TOKENS_INTERVAL_MS = 600 * 1000; // 10 minutes\n// A large number of expiration days (approximately 100 years worth) that is\n// used when creating unexpiring tokens.\nconst LOGIN_UNEXPIRING_TOKEN_DAYS = 365 * 100;\n", "import crypto from 'crypto';\nimport { Meteor } from 'meteor/meteor';\nimport {\n  Accounts<PERSON><PERSON>mon,\n  EXPIRE_TOKENS_INTERVAL_MS,\n} from './accounts_common.js';\nimport { URL } from 'meteor/url';\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\n// XXX maybe this belongs in the check package\nconst NonEmptyString = Match.Where(x => {\n  check(x, String);\n  return x.length > 0;\n});\n\n\n/**\n * @summary Constructor for the `Accounts` namespace on the server.\n * @locus Server\n * @class AccountsServer\n * @extends AccountsCommon\n * @instancename accountsServer\n * @param {Object} server A server object such as `Meteor.server`.\n */\nexport class AccountsServer extends AccountsCommon {\n  // Note that this constructor is less likely to be instantiated multiple\n  // times than the `AccountsClient` constructor, because a single server\n  // can provide only one set of methods.\n  constructor(server, options) {\n    super(options || {});\n\n    this._server = server || Meteor.server;\n    // Set up the server's methods, as if by calling Meteor.methods.\n    this._initServerMethods();\n\n    this._initAccountDataHooks();\n\n    // If autopublish is on, publish these user fields. Login service\n    // packages (eg accounts-google) add to these by calling\n    // addAutopublishFields.  Notably, this isn't implemented with multiple\n    // publishes since DDP only merges only across top-level fields, not\n    // subfields (such as 'services.facebook.accessToken')\n    this._autopublishFields = {\n      loggedInUser: ['profile', 'username', 'emails'],\n      otherUsers: ['profile', 'username']\n    };\n\n    // use object to keep the reference when used in functions\n    // where _defaultPublishFields is destructured into lexical scope\n    // for publish callbacks that need `this`\n    this._defaultPublishFields = {\n      projection: {\n        profile: 1,\n        username: 1,\n        emails: 1,\n      }\n    };\n\n    this._initServerPublications();\n\n    // connectionId -> {connection, loginToken}\n    this._accountData = {};\n\n    // connection id -> observe handle for the login token that this connection is\n    // currently associated with, or a number. The number indicates that we are in\n    // the process of setting up the observe (using a number instead of a single\n    // sentinel allows multiple attempts to set up the observe to identify which\n    // one was theirs).\n    this._userObservesForConnections = {};\n    this._nextUserObserveNumber = 1;  // for the number described above.\n\n    // list of all registered handlers.\n    this._loginHandlers = [];\n    setupDefaultLoginHandlers(this);\n    setExpireTokensInterval(this);\n\n    this._validateLoginHook = new Hook({ bindEnvironment: false });\n    this._validateNewUserHooks = [\n      defaultValidateNewUserHook.bind(this)\n    ];\n\n    this._deleteSavedTokensForAllUsersOnStartup();\n\n    this._skipCaseInsensitiveChecksForTest = {};\n\n    this.urls = {\n      resetPassword: (token, extraParams) => this.buildEmailUrl(`#/reset-password/${token}`, extraParams),\n      verifyEmail: (token, extraParams) => this.buildEmailUrl(`#/verify-email/${token}`, extraParams),\n      loginToken: (selector, token, extraParams) =>\n        this.buildEmailUrl(`/?loginToken=${token}&selector=${selector}`, extraParams),\n      enrollAccount: (token, extraParams) => this.buildEmailUrl(`#/enroll-account/${token}`, extraParams),\n    };\n\n    this.addDefaultRateLimit();\n\n    this.buildEmailUrl = (path, extraParams = {}) => {\n      const url = new URL(Meteor.absoluteUrl(path));\n      const params = Object.entries(extraParams);\n      if (params.length > 0) {\n        // Add additional parameters to the url\n        for (const [key, value] of params) {\n          url.searchParams.append(key, value);\n        }\n      }\n      return url.toString();\n    };\n  }\n\n  ///\n  /// CURRENT USER\n  ///\n\n  // @override of \"abstract\" non-implementation in accounts_common.js\n  userId() {\n    // This function only works if called inside a method or a pubication.\n    // Using any of the information from Meteor.user() in a method or\n    // publish function will always use the value from when the function first\n    // runs. This is likely not what the user expects. The way to make this work\n    // in a method or publish function is to do Meteor.find(this.userId).observe\n    // and recompute when the user record changes.\n    const currentInvocation = DDP._CurrentMethodInvocation.get() || DDP._CurrentPublicationInvocation.get();\n    if (!currentInvocation)\n      throw new Error(\"Meteor.userId can only be invoked in method calls or publications.\");\n    return currentInvocation.userId;\n  }\n\n  async init() {\n    await setupUsersCollection(this.users);\n  }\n\n  ///\n  /// LOGIN HOOKS\n  ///\n\n  /**\n   * @summary Validate login attempts.\n   * @locus Server\n   * @param {Function} func Called whenever a login is attempted (either successful or unsuccessful).  A login can be aborted by returning a falsy value or throwing an exception.\n   */\n  validateLoginAttempt(func) {\n    // Exceptions inside the hook callback are passed up to us.\n    return this._validateLoginHook.register(func);\n  }\n\n  /**\n   * @summary Set restrictions on new user creation.\n   * @locus Server\n   * @param {Function} func Called whenever a new user is created. Takes the new user object, and returns true to allow the creation or false to abort.\n   */\n  validateNewUser(func) {\n    this._validateNewUserHooks.push(func);\n  }\n\n  /**\n   * @summary Validate login from external service\n   * @locus Server\n   * @param {Function} func Called whenever login/user creation from external service is attempted. Login or user creation based on this login can be aborted by passing a falsy value or throwing an exception.\n   */\n  beforeExternalLogin(func) {\n    if (this._beforeExternalLoginHook) {\n      throw new Error(\"Can only call beforeExternalLogin once\");\n    }\n\n    this._beforeExternalLoginHook = func;\n  }\n\n  ///\n  /// CREATE USER HOOKS\n  ///\n\n  /**\n   * @summary Customize login token creation.\n   * @locus Server\n   * @param {Function} func Called whenever a new token is created.\n   * Return the sequence and the user object. Return true to keep sending the default email, or false to override the behavior.\n   */\n  onCreateLoginToken = function(func) {\n    if (this._onCreateLoginTokenHook) {\n      throw new Error('Can only call onCreateLoginToken once');\n    }\n\n    this._onCreateLoginTokenHook = func;\n  }\n\n  /**\n   * @summary Customize new user creation.\n   * @locus Server\n   * @param {Function} func Called whenever a new user is created. Return the new user object, or throw an `Error` to abort the creation.\n   */\n  onCreateUser(func) {\n    if (this._onCreateUserHook) {\n      throw new Error(\"Can only call onCreateUser once\");\n    }\n\n    this._onCreateUserHook = Meteor.wrapFn(func);\n  }\n\n  /**\n   * @summary Customize oauth user profile updates\n   * @locus Server\n   * @param {Function} func Called whenever a user is logged in via oauth. Return the profile object to be merged, or throw an `Error` to abort the creation.\n   */\n  onExternalLogin(func) {\n    if (this._onExternalLoginHook) {\n      throw new Error(\"Can only call onExternalLogin once\");\n    }\n\n    this._onExternalLoginHook = func;\n  }\n\n  /**\n   * @summary Customize user selection on external logins\n   * @locus Server\n   * @param {Function} func Called whenever a user is logged in via oauth and a\n   * user is not found with the service id. Return the user or undefined.\n   */\n  setAdditionalFindUserOnExternalLogin(func) {\n    if (this._additionalFindUserOnExternalLogin) {\n      throw new Error(\"Can only call setAdditionalFindUserOnExternalLogin once\");\n    }\n    this._additionalFindUserOnExternalLogin = func;\n  }\n\n  async _validateLogin(connection, attempt) {\n    await this._validateLoginHook.forEachAsync(async (callback) => {\n      let ret;\n      try {\n        ret = await callback(cloneAttemptWithConnection(connection, attempt));\n      }\n      catch (e) {\n        attempt.allowed = false;\n        // XXX this means the last thrown error overrides previous error\n        // messages. Maybe this is surprising to users and we should make\n        // overriding errors more explicit. (see\n        // https://github.com/meteor/meteor/issues/1960)\n        attempt.error = e;\n        return true;\n      }\n      if (! ret) {\n        attempt.allowed = false;\n        // don't override a specific error provided by a previous\n        // validator or the initial attempt (eg \"incorrect password\").\n        if (!attempt.error)\n          attempt.error = new Meteor.Error(403, \"Login forbidden\");\n      }\n      return true;\n    });\n  };\n\n  async _successfulLogin(connection, attempt) {\n    await this._onLoginHook.forEachAsync(async (callback) => {\n      await callback(cloneAttemptWithConnection(connection, attempt));\n      return true;\n    });\n  };\n\n  async _failedLogin(connection, attempt) {\n    await this._onLoginFailureHook.forEachAsync(async (callback) => {\n      await callback(cloneAttemptWithConnection(connection, attempt));\n      return true;\n    });\n  };\n\n  async _successfulLogout(connection, userId) {\n    // don't fetch the user object unless there are some callbacks registered\n    let user;\n    await this._onLogoutHook.forEachAsync(async callback => {\n      if (!user && userId) user = await this.users.findOneAsync(userId, { fields: this._options.defaultFieldSelector });\n      callback({ user, connection });\n      return true;\n    });\n  };\n\n  // Generates a MongoDB selector that can be used to perform a fast case\n  // insensitive lookup for the given fieldName and string. Since MongoDB does\n  // not support case insensitive indexes, and case insensitive regex queries\n  // are slow, we construct a set of prefix selectors for all permutations of\n  // the first 4 characters ourselves. We first attempt to matching against\n  // these, and because 'prefix expression' regex queries do use indexes (see\n  // http://docs.mongodb.org/v2.6/reference/operator/query/regex/#index-use),\n  // this has been found to greatly improve performance (from 1200ms to 5ms in a\n  // test with 1.000.000 users).\n  _selectorForFastCaseInsensitiveLookup = (fieldName, string) => {\n    // Performance seems to improve up to 4 prefix characters\n    const prefix = string.substring(0, Math.min(string.length, 4));\n    const orClause = generateCasePermutationsForString(prefix).map(\n        prefixPermutation => {\n          const selector = {};\n          selector[fieldName] =\n              new RegExp(`^${Meteor._escapeRegExp(prefixPermutation)}`);\n          return selector;\n        });\n    const caseInsensitiveClause = {};\n    caseInsensitiveClause[fieldName] =\n        new RegExp(`^${Meteor._escapeRegExp(string)}$`, 'i')\n    return {$and: [{$or: orClause}, caseInsensitiveClause]};\n  }\n\n  _findUserByQuery = async (query, options) => {\n    let user = null;\n\n    if (query.id) {\n      // default field selector is added within getUserById()\n      user = await Meteor.users.findOneAsync(query.id, this._addDefaultFieldSelector(options));\n    } else {\n      options = this._addDefaultFieldSelector(options);\n      let fieldName;\n      let fieldValue;\n      if (query.username) {\n        fieldName = 'username';\n        fieldValue = query.username;\n      } else if (query.email) {\n        fieldName = 'emails.address';\n        fieldValue = query.email;\n      } else {\n        throw new Error(\"shouldn't happen (validation missed something)\");\n      }\n      let selector = {};\n      selector[fieldName] = fieldValue;\n      user = await Meteor.users.findOneAsync(selector, options);\n      // If user is not found, try a case insensitive lookup\n      if (!user) {\n        selector = this._selectorForFastCaseInsensitiveLookup(fieldName, fieldValue);\n        const candidateUsers = await Meteor.users.find(selector, { ...options, limit: 2 }).fetchAsync();\n        // No match if multiple candidates are found\n        if (candidateUsers.length === 1) {\n          user = candidateUsers[0];\n        }\n      }\n    }\n\n    return user;\n  }\n\n  ///\n  /// LOGIN METHODS\n  ///\n\n  // Login methods return to the client an object containing these\n  // fields when the user was logged in successfully:\n  //\n  //   id: userId\n  //   token: *\n  //   tokenExpires: *\n  //\n  // tokenExpires is optional and intends to provide a hint to the\n  // client as to when the token will expire. If not provided, the\n  // client will call Accounts._tokenExpiration, passing it the date\n  // that it received the token.\n  //\n  // The login method will throw an error back to the client if the user\n  // failed to log in.\n  //\n  //\n  // Login handlers and service specific login methods such as\n  // `createUser` internally return a `result` object containing these\n  // fields:\n  //\n  //   type:\n  //     optional string; the service name, overrides the handler\n  //     default if present.\n  //\n  //   error:\n  //     exception; if the user is not allowed to login, the reason why.\n  //\n  //   userId:\n  //     string; the user id of the user attempting to login (if\n  //     known), required for an allowed login.\n  //\n  //   options:\n  //     optional object merged into the result returned by the login\n  //     method; used by HAMK from SRP.\n  //\n  //   stampedLoginToken:\n  //     optional object with `token` and `when` indicating the login\n  //     token is already present in the database, returned by the\n  //     \"resume\" login handler.\n  //\n  // For convenience, login methods can also throw an exception, which\n  // is converted into an {error} result.  However, if the id of the\n  // user attempting the login is known, a {userId, error} result should\n  // be returned instead since the user id is not captured when an\n  // exception is thrown.\n  //\n  // This internal `result` object is automatically converted into the\n  // public {id, token, tokenExpires} object returned to the client.\n\n  // Try a login method, converting thrown exceptions into an {error}\n  // result.  The `type` argument is a default, inserted into the result\n  // object if not explicitly returned.\n  //\n  // Log in a user on a connection.\n  //\n  // We use the method invocation to set the user id on the connection,\n  // not the connection object directly. setUserId is tied to methods to\n  // enforce clear ordering of method application (using wait methods on\n  // the client, and a no setUserId after unblock restriction on the\n  // server)\n  //\n  // The `stampedLoginToken` parameter is optional.  When present, it\n  // indicates that the login token has already been inserted into the\n  // database and doesn't need to be inserted again.  (It's used by the\n  // \"resume\" login handler).\n  async _loginUser(methodInvocation, userId, stampedLoginToken) {\n    if (! stampedLoginToken) {\n      stampedLoginToken = this._generateStampedLoginToken();\n      await this._insertLoginToken(userId, stampedLoginToken);\n    }\n\n    // This order (and the avoidance of yields) is important to make\n    // sure that when publish functions are rerun, they see a\n    // consistent view of the world: the userId is set and matches\n    // the login token on the connection (not that there is\n    // currently a public API for reading the login token on a\n    // connection).\n    Meteor._noYieldsAllowed(() =>\n      this._setLoginToken(\n        userId,\n        methodInvocation.connection,\n        this._hashLoginToken(stampedLoginToken.token)\n      )\n    );\n\n    await methodInvocation.setUserId(userId);\n\n    return {\n      id: userId,\n      token: stampedLoginToken.token,\n      tokenExpires: this._tokenExpiration(stampedLoginToken.when)\n    };\n  };\n\n  // After a login method has completed, call the login hooks.  Note\n  // that `attemptLogin` is called for *all* login attempts, even ones\n  // which aren't successful (such as an invalid password, etc).\n  //\n  // If the login is allowed and isn't aborted by a validate login hook\n  // callback, log in the user.\n  //\n  async _attemptLogin(\n    methodInvocation,\n    methodName,\n    methodArgs,\n    result\n  ) {\n    if (!result)\n      throw new Error(\"result is required\");\n\n    // XXX A programming error in a login handler can lead to this occurring, and\n    // then we don't call onLogin or onLoginFailure callbacks. Should\n    // tryLoginMethod catch this case and turn it into an error?\n    if (!result.userId && !result.error)\n      throw new Error(\"A login method must specify a userId or an error\");\n\n    let user;\n    if (result.userId)\n      user = await this.users.findOneAsync(result.userId, {fields: this._options.defaultFieldSelector});\n\n    const attempt = {\n      type: result.type || \"unknown\",\n      allowed: !! (result.userId && !result.error),\n      methodName: methodName,\n      methodArguments: Array.from(methodArgs)\n    };\n    if (result.error) {\n      attempt.error = result.error;\n    }\n    if (user) {\n      attempt.user = user;\n    }\n\n    // _validateLogin may mutate `attempt` by adding an error and changing allowed\n    // to false, but that's the only change it can make (and the user's callbacks\n    // only get a clone of `attempt`).\n    await this._validateLogin(methodInvocation.connection, attempt);\n\n    if (attempt.allowed) {\n      const o = await this._loginUser(\n        methodInvocation,\n        result.userId,\n        result.stampedLoginToken\n      )\n      const ret = {\n        ...o,\n        ...result.options\n      };\n      ret.type = attempt.type;\n      await this._successfulLogin(methodInvocation.connection, attempt);\n      return ret;\n    }\n    else {\n      await this._failedLogin(methodInvocation.connection, attempt);\n      throw attempt.error;\n    }\n  };\n\n  // All service specific login methods should go through this function.\n  // Ensure that thrown exceptions are caught and that login hook\n  // callbacks are still called.\n  //\n  async _loginMethod(\n    methodInvocation,\n    methodName,\n    methodArgs,\n    type,\n    fn\n  ) {\n    return await this._attemptLogin(\n      methodInvocation,\n      methodName,\n      methodArgs,\n      await tryLoginMethod(type, fn)\n    );\n  };\n\n\n  // Report a login attempt failed outside the context of a normal login\n  // method. This is for use in the case where there is a multi-step login\n  // procedure (eg SRP based password login). If a method early in the\n  // chain fails, it should call this function to report a failure. There\n  // is no corresponding method for a successful login; methods that can\n  // succeed at logging a user in should always be actual login methods\n  // (using either Accounts._loginMethod or Accounts.registerLoginHandler).\n  async _reportLoginFailure(\n    methodInvocation,\n    methodName,\n    methodArgs,\n    result\n  ) {\n    const attempt = {\n      type: result.type || \"unknown\",\n      allowed: false,\n      error: result.error,\n      methodName: methodName,\n      methodArguments: Array.from(methodArgs)\n    };\n\n    if (result.userId) {\n      attempt.user = this.users.findOneAsync(result.userId, {fields: this._options.defaultFieldSelector});\n    }\n\n    await this._validateLogin(methodInvocation.connection, attempt);\n    await this._failedLogin(methodInvocation.connection, attempt);\n\n    // _validateLogin may mutate attempt to set a new error message. Return\n    // the modified version.\n    return attempt;\n  };\n\n  ///\n  /// LOGIN HANDLERS\n  ///\n\n  /**\n   * @summary Registers a new login handler.\n   * @locus Server\n   * @param {String} [name] The type of login method like oauth, password, etc.\n   * @param {Function} handler A function that receives an options object\n   * (as passed as an argument to the `login` method) and returns one of\n   * `undefined`, meaning don't handle or a login method result object.\n   */\n  registerLoginHandler(name, handler) {\n    if (! handler) {\n      handler = name;\n      name = null;\n    }\n\n    this._loginHandlers.push({\n      name: name,\n      handler: Meteor.wrapFn(handler)\n    });\n  };\n\n\n  // Checks a user's credentials against all the registered login\n  // handlers, and returns a login token if the credentials are valid. It\n  // is like the login method, except that it doesn't set the logged-in\n  // user on the connection. Throws a Meteor.Error if logging in fails,\n  // including the case where none of the login handlers handled the login\n  // request. Otherwise, returns {id: userId, token: *, tokenExpires: *}.\n  //\n  // For example, if you want to login with a plaintext password, `options` could be\n  //   { user: { username: <username> }, password: <password> }, or\n  //   { user: { email: <email> }, password: <password> }.\n\n  // Try all of the registered login handlers until one of them doesn't\n  // return `undefined`, meaning it handled this call to `login`. Return\n  // that return value.\n  async _runLoginHandlers(methodInvocation, options) {\n    for (let handler of this._loginHandlers) {\n      const result = await tryLoginMethod(handler.name, async () =>\n        await handler.handler.call(methodInvocation, options)\n      );\n\n      if (result) {\n        return result;\n      }\n\n      if (result !== undefined) {\n        throw new Meteor.Error(\n          400,\n          'A login handler should return a result or undefined'\n        );\n      }\n    }\n\n    return {\n      type: null,\n      error: new Meteor.Error(400, \"Unrecognized options for login request\")\n    };\n  };\n\n  // Deletes the given loginToken from the database.\n  //\n  // For new-style hashed token, this will cause all connections\n  // associated with the token to be closed.\n  //\n  // Any connections associated with old-style unhashed tokens will be\n  // in the process of becoming associated with hashed tokens and then\n  // they'll get closed.\n  async destroyToken(userId, loginToken) {\n    await this.users.updateAsync(userId, {\n      $pull: {\n        \"services.resume.loginTokens\": {\n          $or: [\n            { hashedToken: loginToken },\n            { token: loginToken }\n          ]\n        }\n      }\n    });\n  };\n\n  _initServerMethods() {\n    // The methods created in this function need to be created here so that\n    // this variable is available in their scope.\n    const accounts = this;\n\n\n    // This object will be populated with methods and then passed to\n    // accounts._server.methods further below.\n    const methods = {};\n\n    // @returns {Object|null}\n    //   If successful, returns {token: reconnectToken, id: userId}\n    //   If unsuccessful (for example, if the user closed the oauth login popup),\n    //     throws an error describing the reason\n    methods.login = async function (options) {\n      // Login handlers should really also check whatever field they look at in\n      // options, but we don't enforce it.\n      check(options, Object);\n\n      const result = await accounts._runLoginHandlers(this, options);\n      //console.log({result});\n\n      return await accounts._attemptLogin(this, \"login\", arguments, result);\n    };\n\n    methods.logout = async function () {\n      const token = accounts._getLoginToken(this.connection.id);\n      accounts._setLoginToken(this.userId, this.connection, null);\n      if (token && this.userId) {\n       await accounts.destroyToken(this.userId, token);\n      }\n      await accounts._successfulLogout(this.connection, this.userId);\n      await this.setUserId(null);\n    };\n\n    // Generates a new login token with the same expiration as the\n    // connection's current token and saves it to the database. Associates\n    // the connection with this new token and returns it. Throws an error\n    // if called on a connection that isn't logged in.\n    //\n    // @returns Object\n    //   If successful, returns { token: <new token>, id: <user id>,\n    //   tokenExpires: <expiration date> }.\n    methods.getNewToken = async function () {\n      const user = await accounts.users.findOneAsync(this.userId, {\n        fields: { \"services.resume.loginTokens\": 1 }\n      });\n      if (! this.userId || ! user) {\n        throw new Meteor.Error(\"You are not logged in.\");\n      }\n      // Be careful not to generate a new token that has a later\n      // expiration than the curren token. Otherwise, a bad guy with a\n      // stolen token could use this method to stop his stolen token from\n      // ever expiring.\n      const currentHashedToken = accounts._getLoginToken(this.connection.id);\n      const currentStampedToken = user.services.resume.loginTokens.find(\n        stampedToken => stampedToken.hashedToken === currentHashedToken\n      );\n      if (! currentStampedToken) { // safety belt: this should never happen\n        throw new Meteor.Error(\"Invalid login token\");\n      }\n      const newStampedToken = accounts._generateStampedLoginToken();\n      newStampedToken.when = currentStampedToken.when;\n      await accounts._insertLoginToken(this.userId, newStampedToken);\n      return await accounts._loginUser(this, this.userId, newStampedToken);\n    };\n\n    // Removes all tokens except the token associated with the current\n    // connection. Throws an error if the connection is not logged\n    // in. Returns nothing on success.\n    methods.removeOtherTokens = async function () {\n      if (! this.userId) {\n        throw new Meteor.Error(\"You are not logged in.\");\n      }\n      const currentToken = accounts._getLoginToken(this.connection.id);\n      await accounts.users.updateAsync(this.userId, {\n        $pull: {\n          \"services.resume.loginTokens\": { hashedToken: { $ne: currentToken } }\n        }\n      });\n    };\n\n    // Allow a one-time configuration for a login service. Modifications\n    // to this collection are also allowed in insecure mode.\n    methods.configureLoginService = async (options) => {\n      check(options, Match.ObjectIncluding({service: String}));\n      // Don't let random users configure a service we haven't added yet (so\n      // that when we do later add it, it's set up with their configuration\n      // instead of ours).\n      // XXX if service configuration is oauth-specific then this code should\n      //     be in accounts-oauth; if it's not then the registry should be\n      //     in this package\n      if (!(accounts.oauth\n        && accounts.oauth.serviceNames().includes(options.service))) {\n        throw new Meteor.Error(403, \"Service unknown\");\n      }\n\n      if (Package['service-configuration']) {\n        const { ServiceConfiguration } = Package['service-configuration'];\n        const service = await ServiceConfiguration.configurations.findOneAsync({service: options.service})\n        if (service)\n          throw new Meteor.Error(403, `Service ${options.service} already configured`);\n\n        if (Package[\"oauth-encryption\"]) {\n          const { OAuthEncryption } = Package[\"oauth-encryption\"]\n          if (hasOwn.call(options, 'secret') && OAuthEncryption.keyIsLoaded())\n            options.secret = OAuthEncryption.seal(options.secret);\n        }\n\n        await ServiceConfiguration.configurations.insertAsync(options);\n      }\n    };\n\n    accounts._server.methods(methods);\n  };\n\n  _initAccountDataHooks() {\n    this._server.onConnection(connection => {\n      this._accountData[connection.id] = {\n        connection: connection\n      };\n\n      connection.onClose(() => {\n        this._removeTokenFromConnection(connection.id);\n        delete this._accountData[connection.id];\n      });\n    });\n  };\n\n  _initServerPublications() {\n    // Bring into lexical scope for publish callbacks that need `this`\n    const { users, _autopublishFields, _defaultPublishFields } = this;\n\n    // Publish all login service configuration fields other than secret.\n    this._server.publish(\"meteor.loginServiceConfiguration\", function() {\n      if (Package['service-configuration']) {\n        const { ServiceConfiguration } = Package['service-configuration'];\n        return ServiceConfiguration.configurations.find({}, {fields: {secret: 0}});\n      }\n      this.ready();\n    }, {is_auto: true}); // not technically autopublish, but stops the warning.\n\n    // Use Meteor.startup to give other packages a chance to call\n    // setDefaultPublishFields.\n    Meteor.startup(() => {\n      // Merge custom fields selector and default publish fields so that the client\n      // gets all the necessary fields to run properly\n      const customFields = this._addDefaultFieldSelector().fields || {};\n      const keys = Object.keys(customFields);\n      // If the custom fields are negative, then ignore them and only send the necessary fields\n      const fields = keys.length > 0 && customFields[keys[0]] ? {\n        ...this._addDefaultFieldSelector().fields,\n        ..._defaultPublishFields.projection\n      } : _defaultPublishFields.projection\n      // Publish the current user's record to the client.\n      this._server.publish(null, function () {\n        if (this.userId) {\n          return users.find({\n            _id: this.userId\n          }, {\n            fields,\n          });\n        } else {\n          return null;\n        }\n      }, /*suppress autopublish warning*/{is_auto: true});\n    });\n\n    // Use Meteor.startup to give other packages a chance to call\n    // addAutopublishFields.\n    Package.autopublish && Meteor.startup(() => {\n      // ['profile', 'username'] -> {profile: 1, username: 1}\n      const toFieldSelector = fields => fields.reduce((prev, field) => (\n          { ...prev, [field]: 1 }),\n        {}\n      );\n      this._server.publish(null, function () {\n        if (this.userId) {\n          return users.find({ _id: this.userId }, {\n            fields: toFieldSelector(_autopublishFields.loggedInUser),\n          })\n        } else {\n          return null;\n        }\n      }, /*suppress autopublish warning*/{is_auto: true});\n\n      // XXX this publish is neither dedup-able nor is it optimized by our special\n      // treatment of queries on a specific _id. Therefore this will have O(n^2)\n      // run-time performance every time a user document is changed (eg someone\n      // logging in). If this is a problem, we can instead write a manual publish\n      // function which filters out fields based on 'this.userId'.\n      this._server.publish(null, function () {\n        const selector = this.userId ? { _id: { $ne: this.userId } } : {};\n        return users.find(selector, {\n          fields: toFieldSelector(_autopublishFields.otherUsers),\n        })\n      }, /*suppress autopublish warning*/{is_auto: true});\n    });\n  };\n\n  // Add to the list of fields or subfields to be automatically\n  // published if autopublish is on. Must be called from top-level\n  // code (ie, before Meteor.startup hooks run).\n  //\n  // @param opts {Object} with:\n  //   - forLoggedInUser {Array} Array of fields published to the logged-in user\n  //   - forOtherUsers {Array} Array of fields published to users that aren't logged in\n  addAutopublishFields(opts) {\n    this._autopublishFields.loggedInUser.push.apply(\n      this._autopublishFields.loggedInUser, opts.forLoggedInUser);\n    this._autopublishFields.otherUsers.push.apply(\n      this._autopublishFields.otherUsers, opts.forOtherUsers);\n  };\n\n  // Replaces the fields to be automatically\n  // published when the user logs in\n  //\n  // @param {MongoFieldSpecifier} fields Dictionary of fields to return or exclude.\n  setDefaultPublishFields(fields) {\n    this._defaultPublishFields.projection = fields;\n  };\n\n  ///\n  /// ACCOUNT DATA\n  ///\n\n  // HACK: This is used by 'meteor-accounts' to get the loginToken for a\n  // connection. Maybe there should be a public way to do that.\n  _getAccountData(connectionId, field) {\n    const data = this._accountData[connectionId];\n    return data && data[field];\n  };\n\n  _setAccountData(connectionId, field, value) {\n    const data = this._accountData[connectionId];\n\n    // safety belt. shouldn't happen. accountData is set in onConnection,\n    // we don't have a connectionId until it is set.\n    if (!data)\n      return;\n\n    if (value === undefined)\n      delete data[field];\n    else\n      data[field] = value;\n  };\n\n  ///\n  /// RECONNECT TOKENS\n  ///\n  /// support reconnecting using a meteor login token\n\n  _hashLoginToken(loginToken) {\n    const hash = crypto.createHash('sha256');\n    hash.update(loginToken);\n    return hash.digest('base64');\n  };\n\n  // {token, when} => {hashedToken, when}\n  _hashStampedToken(stampedToken) {\n    const { token, ...hashedStampedToken } = stampedToken;\n    return {\n      ...hashedStampedToken,\n      hashedToken: this._hashLoginToken(token)\n    };\n  };\n\n  // Using $addToSet avoids getting an index error if another client\n  // logging in simultaneously has already inserted the new hashed\n  // token.\n  async _insertHashedLoginToken(userId, hashedToken, query) {\n    query = query ? { ...query } : {};\n    query._id = userId;\n    await this.users.updateAsync(query, {\n      $addToSet: {\n        \"services.resume.loginTokens\": hashedToken\n      }\n    });\n  };\n\n  // Exported for tests.\n  async _insertLoginToken(userId, stampedToken, query) {\n    await this._insertHashedLoginToken(\n      userId,\n      this._hashStampedToken(stampedToken),\n      query\n    );\n  };\n\n  /**\n   *\n   * @param userId\n   * @private\n   * @returns {Promise<void>}\n   */\n  _clearAllLoginTokens(userId) {\n    this.users.updateAsync(userId, {\n      $set: {\n        'services.resume.loginTokens': []\n      }\n    });\n  };\n\n  // test hook\n  _getUserObserve(connectionId) {\n    return this._userObservesForConnections[connectionId];\n  };\n\n  // Clean up this connection's association with the token: that is, stop\n  // the observe that we started when we associated the connection with\n  // this token.\n  _removeTokenFromConnection(connectionId) {\n    if (hasOwn.call(this._userObservesForConnections, connectionId)) {\n      const observe = this._userObservesForConnections[connectionId];\n      if (typeof observe === 'number') {\n        // We're in the process of setting up an observe for this connection. We\n        // can't clean up that observe yet, but if we delete the placeholder for\n        // this connection, then the observe will get cleaned up as soon as it has\n        // been set up.\n        delete this._userObservesForConnections[connectionId];\n      } else {\n        delete this._userObservesForConnections[connectionId];\n        observe.stop();\n      }\n    }\n  };\n\n  _getLoginToken(connectionId) {\n    return this._getAccountData(connectionId, 'loginToken');\n  };\n\n  // newToken is a hashed token.\n  _setLoginToken(userId, connection, newToken) {\n    this._removeTokenFromConnection(connection.id);\n    this._setAccountData(connection.id, 'loginToken', newToken);\n\n    if (newToken) {\n      // Set up an observe for this token. If the token goes away, we need\n      // to close the connection.  We defer the observe because there's\n      // no need for it to be on the critical path for login; we just need\n      // to ensure that the connection will get closed at some point if\n      // the token gets deleted.\n      //\n      // Initially, we set the observe for this connection to a number; this\n      // signifies to other code (which might run while we yield) that we are in\n      // the process of setting up an observe for this connection. Once the\n      // observe is ready to go, we replace the number with the real observe\n      // handle (unless the placeholder has been deleted or replaced by a\n      // different placehold number, signifying that the connection was closed\n      // already -- in this case we just clean up the observe that we started).\n      const myObserveNumber = ++this._nextUserObserveNumber;\n      this._userObservesForConnections[connection.id] = myObserveNumber;\n      Meteor.defer(async () => {\n        // If something else happened on this connection in the meantime (it got\n        // closed, or another call to _setLoginToken happened), just do\n        // nothing. We don't need to start an observe for an old connection or old\n        // token.\n        if (this._userObservesForConnections[connection.id] !== myObserveNumber) {\n          return;\n        }\n\n        let foundMatchingUser;\n        // Because we upgrade unhashed login tokens to hashed tokens at\n        // login time, sessions will only be logged in with a hashed\n        // token. Thus we only need to observe hashed tokens here.\n        const observe = await this.users.find({\n          _id: userId,\n          'services.resume.loginTokens.hashedToken': newToken\n        }, { fields: { _id: 1 } }).observeChanges({\n          added: () => {\n            foundMatchingUser = true;\n          },\n          removed: connection.close,\n          // The onClose callback for the connection takes care of\n          // cleaning up the observe handle and any other state we have\n          // lying around.\n        }, { nonMutatingCallbacks: true });\n\n        // If the user ran another login or logout command we were waiting for the\n        // defer or added to fire (ie, another call to _setLoginToken occurred),\n        // then we let the later one win (start an observe, etc) and just stop our\n        // observe now.\n        //\n        // Similarly, if the connection was already closed, then the onClose\n        // callback would have called _removeTokenFromConnection and there won't\n        // be an entry in _userObservesForConnections. We can stop the observe.\n        if (this._userObservesForConnections[connection.id] !== myObserveNumber) {\n          observe.stop();\n          return;\n        }\n\n        this._userObservesForConnections[connection.id] = observe;\n\n        if (! foundMatchingUser) {\n          // We've set up an observe on the user associated with `newToken`,\n          // so if the new token is removed from the database, we'll close\n          // the connection. But the token might have already been deleted\n          // before we set up the observe, which wouldn't have closed the\n          // connection because the observe wasn't running yet.\n          connection.close();\n        }\n      });\n    }\n  };\n\n  // (Also used by Meteor Accounts server and tests).\n  //\n  _generateStampedLoginToken() {\n    return {\n      token: Random.secret(),\n      when: new Date\n    };\n  };\n\n  ///\n  /// TOKEN EXPIRATION\n  ///\n\n  // Deletes expired password reset tokens from the database.\n  //\n  // Exported for tests. Also, the arguments are only used by\n  // tests. oldestValidDate is simulate expiring tokens without waiting\n  // for them to actually expire. userId is used by tests to only expire\n  // tokens for the test user.\n  async _expirePasswordResetTokens(oldestValidDate, userId) {\n    const tokenLifetimeMs = this._getPasswordResetTokenLifetimeMs();\n\n    // when calling from a test with extra arguments, you must specify both!\n    if ((oldestValidDate && !userId) || (!oldestValidDate && userId)) {\n      throw new Error(\"Bad test. Must specify both oldestValidDate and userId.\");\n    }\n\n    oldestValidDate = oldestValidDate ||\n      (new Date(new Date() - tokenLifetimeMs));\n\n    const tokenFilter = {\n      $or: [\n        { \"services.password.reset.reason\": \"reset\"},\n        { \"services.password.reset.reason\": {$exists: false}}\n      ]\n    };\n\n   await expirePasswordToken(this, oldestValidDate, tokenFilter, userId);\n  }\n\n  // Deletes expired password enroll tokens from the database.\n  //\n  // Exported for tests. Also, the arguments are only used by\n  // tests. oldestValidDate is simulate expiring tokens without waiting\n  // for them to actually expire. userId is used by tests to only expire\n  // tokens for the test user.\n  async _expirePasswordEnrollTokens(oldestValidDate, userId) {\n    const tokenLifetimeMs = this._getPasswordEnrollTokenLifetimeMs();\n\n    // when calling from a test with extra arguments, you must specify both!\n    if ((oldestValidDate && !userId) || (!oldestValidDate && userId)) {\n      throw new Error(\"Bad test. Must specify both oldestValidDate and userId.\");\n    }\n\n    oldestValidDate = oldestValidDate ||\n      (new Date(new Date() - tokenLifetimeMs));\n\n    const tokenFilter = {\n      \"services.password.enroll.reason\": \"enroll\"\n    };\n\n    await expirePasswordToken(this, oldestValidDate, tokenFilter, userId);\n  }\n\n  // Deletes expired tokens from the database and closes all open connections\n  // associated with these tokens.\n  //\n  // Exported for tests. Also, the arguments are only used by\n  // tests. oldestValidDate is simulate expiring tokens without waiting\n  // for them to actually expire. userId is used by tests to only expire\n  // tokens for the test user.\n  /**\n   *\n   * @param oldestValidDate\n   * @param userId\n   * @private\n   * @return {Promise<void>}\n   */\n  async _expireTokens(oldestValidDate, userId) {\n    const tokenLifetimeMs = this._getTokenLifetimeMs();\n\n    // when calling from a test with extra arguments, you must specify both!\n    if ((oldestValidDate && !userId) || (!oldestValidDate && userId)) {\n      throw new Error(\"Bad test. Must specify both oldestValidDate and userId.\");\n    }\n\n    oldestValidDate = oldestValidDate ||\n      (new Date(new Date() - tokenLifetimeMs));\n    const userFilter = userId ? {_id: userId} : {};\n\n\n    // Backwards compatible with older versions of meteor that stored login token\n    // timestamps as numbers.\n    await this.users.updateAsync({ ...userFilter,\n      $or: [\n        { \"services.resume.loginTokens.when\": { $lt: oldestValidDate } },\n        { \"services.resume.loginTokens.when\": { $lt: +oldestValidDate } }\n      ]\n    }, {\n      $pull: {\n        \"services.resume.loginTokens\": {\n          $or: [\n            { when: { $lt: oldestValidDate } },\n            { when: { $lt: +oldestValidDate } }\n          ]\n        }\n      }\n    }, { multi: true });\n    // The observe on Meteor.users will take care of closing connections for\n    // expired tokens.\n  };\n\n  // @override from accounts_common.js\n  config(options) {\n    // Call the overridden implementation of the method.\n    const superResult = AccountsCommon.prototype.config.apply(this, arguments);\n\n    // If the user set loginExpirationInDays to null, then we need to clear the\n    // timer that periodically expires tokens.\n    if (hasOwn.call(this._options, 'loginExpirationInDays') &&\n      this._options.loginExpirationInDays === null &&\n      this.expireTokenInterval) {\n      Meteor.clearInterval(this.expireTokenInterval);\n      this.expireTokenInterval = null;\n    }\n\n    return superResult;\n  };\n\n  // Called by accounts-password\n  async insertUserDoc(options, user) {\n    // - clone user document, to protect from modification\n    // - add createdAt timestamp\n    // - prepare an _id, so that you can modify other collections (eg\n    // create a first task for every new user)\n    //\n    // XXX If the onCreateUser or validateNewUser hooks fail, we might\n    // end up having modified some other collection\n    // inappropriately. The solution is probably to have onCreateUser\n    // accept two callbacks - one that gets called before inserting\n    // the user document (in which you can modify its contents), and\n    // one that gets called after (in which you should change other\n    // collections)\n    user = {\n      createdAt: new Date(),\n      _id: Random.id(),\n      ...user,\n    };\n\n    if (user.services) {\n      Object.keys(user.services).forEach(service =>\n        pinEncryptedFieldsToUser(user.services[service], user._id)\n      );\n    }\n\n    let fullUser;\n    if (this._onCreateUserHook) {\n      // Allows _onCreateUserHook to be a promise returning func\n      fullUser = await this._onCreateUserHook(options, user);\n\n      // This is *not* part of the API. We need this because we can't isolate\n      // the global server environment between tests, meaning we can't test\n      // both having a create user hook set and not having one set.\n      if (fullUser === 'TEST DEFAULT HOOK')\n        fullUser = defaultCreateUserHook(options, user);\n    } else {\n      fullUser = defaultCreateUserHook(options, user);\n    }\n\n    for await (const hook of this._validateNewUserHooks) {\n      if (! await hook(fullUser))\n        throw new Meteor.Error(403, \"User validation failed\");\n    }\n\n    let userId;\n    try {\n      userId = await this.users.insertAsync(fullUser);\n    } catch (e) {\n      // XXX string parsing sucks, maybe\n      // https://jira.mongodb.org/browse/SERVER-3069 will get fixed one day\n      // https://jira.mongodb.org/browse/SERVER-4637\n      if (!e.errmsg) throw e;\n      if (e.errmsg.includes('emails.address'))\n        throw new Meteor.Error(403, \"Email already exists.\");\n      if (e.errmsg.includes('username'))\n        throw new Meteor.Error(403, \"Username already exists.\");\n      throw e;\n    }\n    return userId;\n  };\n\n  // Helper function: returns false if email does not match company domain from\n  // the configuration.\n  _testEmailDomain(email) {\n    const domain = this._options.restrictCreationByEmailDomain;\n\n    return !domain ||\n      (typeof domain === 'function' && domain(email)) ||\n      (typeof domain === 'string' &&\n        (new RegExp(`@${Meteor._escapeRegExp(domain)}$`, 'i')).test(email));\n  };\n\n  ///\n  /// CLEAN UP FOR `logoutOtherClients`\n  ///\n\n  async _deleteSavedTokensForUser(userId, tokensToDelete) {\n    if (tokensToDelete) {\n      await this.users.updateAsync(userId, {\n        $unset: {\n          \"services.resume.haveLoginTokensToDelete\": 1,\n          \"services.resume.loginTokensToDelete\": 1\n        },\n        $pullAll: {\n          \"services.resume.loginTokens\": tokensToDelete\n        }\n      });\n    }\n  };\n\n  _deleteSavedTokensForAllUsersOnStartup() {\n    // If we find users who have saved tokens to delete on startup, delete\n    // them now. It's possible that the server could have crashed and come\n    // back up before new tokens are found in localStorage, but this\n    // shouldn't happen very often. We shouldn't put a delay here because\n    // that would give a lot of power to an attacker with a stolen login\n    // token and the ability to crash the server.\n    Meteor.startup(async () => {\n      const users = await this.users.find({\n        \"services.resume.haveLoginTokensToDelete\": true\n      }, {\n        fields: {\n          \"services.resume.loginTokensToDelete\": 1\n        }\n      })\n      users.forEach(user => {\n        this._deleteSavedTokensForUser(\n          user._id,\n          user.services.resume.loginTokensToDelete\n        )\n          // We don't need to wait for this to complete.\n          .then(_ => _)\n          .catch(err => {\n            console.log(err);\n          });\n      });\n    });\n  };\n\n  ///\n  /// MANAGING USER OBJECTS\n  ///\n\n  // Updates or creates a user after we authenticate with a 3rd party.\n  //\n  // @param serviceName {String} Service name (eg, twitter).\n  // @param serviceData {Object} Data to store in the user's record\n  //        under services[serviceName]. Must include an \"id\" field\n  //        which is a unique identifier for the user in the service.\n  // @param options {Object, optional} Other options to pass to insertUserDoc\n  //        (eg, profile)\n  // @returns {Object} Object with token and id keys, like the result\n  //        of the \"login\" method.\n  //\n  async updateOrCreateUserFromExternalService(\n    serviceName,\n    serviceData,\n    options\n  ) {\n    options = { ...options };\n\n    if (serviceName === \"password\" || serviceName === \"resume\") {\n      throw new Error(\n        \"Can't use updateOrCreateUserFromExternalService with internal service \"\n        + serviceName);\n    }\n    if (!hasOwn.call(serviceData, 'id')) {\n      throw new Error(\n        `Service data for service ${serviceName} must include id`);\n    }\n\n    // Look for a user with the appropriate service user id.\n    const selector = {};\n    const serviceIdKey = `services.${serviceName}.id`;\n\n    // XXX Temporary special case for Twitter. (Issue #629)\n    //   The serviceData.id will be a string representation of an integer.\n    //   We want it to match either a stored string or int representation.\n    //   This is to cater to earlier versions of Meteor storing twitter\n    //   user IDs in number form, and recent versions storing them as strings.\n    //   This can be removed once migration technology is in place, and twitter\n    //   users stored with integer IDs have been migrated to string IDs.\n    if (serviceName === \"twitter\" && !isNaN(serviceData.id)) {\n      selector[\"$or\"] = [{},{}];\n      selector[\"$or\"][0][serviceIdKey] = serviceData.id;\n      selector[\"$or\"][1][serviceIdKey] = parseInt(serviceData.id, 10);\n    } else {\n      selector[serviceIdKey] = serviceData.id;\n    }\n    let user = await this.users.findOneAsync(selector, {fields: this._options.defaultFieldSelector});\n    // Check to see if the developer has a custom way to find the user outside\n    // of the general selectors above.\n    if (!user && this._additionalFindUserOnExternalLogin) {\n      user = await this._additionalFindUserOnExternalLogin({serviceName, serviceData, options})\n    }\n\n    // Before continuing, run user hook to see if we should continue\n    if (this._beforeExternalLoginHook && !(await this._beforeExternalLoginHook(serviceName, serviceData, user))) {\n      throw new Meteor.Error(403, \"Login forbidden\");\n    }\n\n    // When creating a new user we pass through all options. When updating an\n    // existing user, by default we only process/pass through the serviceData\n    // (eg, so that we keep an unexpired access token and don't cache old email\n    // addresses in serviceData.email). The onExternalLogin hook can be used when\n    // creating or updating a user, to modify or pass through more options as\n    // needed.\n    let opts = user ? {} : options;\n    if (this._onExternalLoginHook) {\n      opts = await this._onExternalLoginHook(options, user);\n    }\n\n    if (user) {\n      await pinEncryptedFieldsToUser(serviceData, user._id);\n\n      let setAttrs = {};\n      Object.keys(serviceData).forEach(key =>\n        setAttrs[`services.${serviceName}.${key}`] = serviceData[key]\n      );\n\n      // XXX Maybe we should re-use the selector above and notice if the update\n      //     touches nothing?\n      setAttrs = { ...setAttrs, ...opts };\n      await this.users.updateAsync(user._id, {\n        $set: setAttrs\n      });\n\n      return {\n        type: serviceName,\n        userId: user._id\n      };\n    } else {\n      // Create a new user with the service data.\n      user = {services: {}};\n      user.services[serviceName] = serviceData;\n      const userId = await this.insertUserDoc(opts, user);\n      return {\n        type: serviceName,\n        userId\n      };\n    }\n  };\n\n  /**\n   * @summary Removes default rate limiting rule\n   * @locus Server\n   * @importFromPackage accounts-base\n   */\n  removeDefaultRateLimit() {\n    const resp = DDPRateLimiter.removeRule(this.defaultRateLimiterRuleId);\n    this.defaultRateLimiterRuleId = null;\n    return resp;\n  };\n\n  /**\n   * @summary Add a default rule of limiting logins, creating new users and password reset\n   * to 5 times every 10 seconds per connection.\n   * @locus Server\n   * @importFromPackage accounts-base\n   */\n  addDefaultRateLimit() {\n    if (!this.defaultRateLimiterRuleId) {\n      this.defaultRateLimiterRuleId = DDPRateLimiter.addRule({\n        userId: null,\n        clientAddress: null,\n        type: 'method',\n        name: name => ['login', 'createUser', 'resetPassword', 'forgotPassword']\n          .includes(name),\n        connectionId: (connectionId) => true,\n      }, 5, 10000);\n    }\n  };\n\n  /**\n   * @summary Creates options for email sending for reset password and enroll account emails.\n   * You can use this function when customizing a reset password or enroll account email sending.\n   * @locus Server\n   * @param {Object} email Which address of the user's to send the email to.\n   * @param {Object} user The user object to generate options for.\n   * @param {String} url URL to which user is directed to confirm the email.\n   * @param {String} reason `resetPassword` or `enrollAccount`.\n   * @returns {Object} Options which can be passed to `Email.send`.\n   * @importFromPackage accounts-base\n   */\n  async generateOptionsForEmail(email, user, url, reason, extra = {}){\n    const options = {\n      to: email,\n      from: this.emailTemplates[reason].from\n        ? await this.emailTemplates[reason].from(user)\n        : this.emailTemplates.from,\n      subject: await this.emailTemplates[reason].subject(user, url, extra),\n    };\n\n    if (typeof this.emailTemplates[reason].text === 'function') {\n      options.text = await this.emailTemplates[reason].text(user, url, extra);\n    }\n\n    if (typeof this.emailTemplates[reason].html === 'function') {\n      options.html = await this.emailTemplates[reason].html(user, url, extra);\n    }\n\n    if (typeof this.emailTemplates.headers === 'object') {\n      options.headers = this.emailTemplates.headers;\n    }\n\n    return options;\n  };\n\n  async _checkForCaseInsensitiveDuplicates(\n    fieldName,\n    displayName,\n    fieldValue,\n    ownUserId\n  ) {\n    // Some tests need the ability to add users with the same case insensitive\n    // value, hence the _skipCaseInsensitiveChecksForTest check\n    const skipCheck = Object.prototype.hasOwnProperty.call(\n      this._skipCaseInsensitiveChecksForTest,\n      fieldValue\n    );\n\n    if (fieldValue && !skipCheck) {\n      const matchedUsers = await Meteor.users\n        .find(\n          this._selectorForFastCaseInsensitiveLookup(fieldName, fieldValue),\n          {\n            fields: { _id: 1 },\n            // we only need a maximum of 2 users for the logic below to work\n            limit: 2,\n          }\n        )\n        .fetchAsync();\n\n      if (\n        matchedUsers.length > 0 &&\n        // If we don't have a userId yet, any match we find is a duplicate\n        (!ownUserId ||\n          // Otherwise, check to see if there are multiple matches or a match\n          // that is not us\n          matchedUsers.length > 1 || matchedUsers[0]._id !== ownUserId)\n      ) {\n        this._handleError(`${displayName} already exists.`);\n      }\n    }\n  };\n\n  async _createUserCheckingDuplicates({ user, email, username, options }) {\n    const newUser = {\n      ...user,\n      ...(username ? { username } : {}),\n      ...(email ? { emails: [{ address: email, verified: false }] } : {}),\n    };\n\n    // Perform a case insensitive check before insert\n    await this._checkForCaseInsensitiveDuplicates('username', 'Username', username);\n    await this._checkForCaseInsensitiveDuplicates('emails.address', 'Email', email);\n\n    const userId = await this.insertUserDoc(options, newUser);\n    // Perform another check after insert, in case a matching user has been\n    // inserted in the meantime\n    try {\n      await this._checkForCaseInsensitiveDuplicates('username', 'Username', username, userId);\n      await this._checkForCaseInsensitiveDuplicates('emails.address', 'Email', email, userId);\n    } catch (ex) {\n      // Remove inserted user if the check fails\n      await Meteor.users.removeAsync(userId);\n      throw ex;\n    }\n    return userId;\n  }\n\n  _handleError = (msg, throwError = true, errorCode = 403) => {\n    const isErrorAmbiguous = this._options.ambiguousErrorMessages ?? true;\n    const error = new Meteor.Error(\n      errorCode,\n      isErrorAmbiguous\n        ? 'Something went wrong. Please check your credentials.'\n        : msg\n    );\n    if (throwError) {\n      throw error;\n    }\n    return error;\n  }\n\n  _userQueryValidator = Match.Where(user => {\n    check(user, {\n      id: Match.Optional(NonEmptyString),\n      username: Match.Optional(NonEmptyString),\n      email: Match.Optional(NonEmptyString)\n    });\n    if (Object.keys(user).length !== 1)\n      throw new Match.Error(\"User property must have exactly one field\");\n    return true;\n  });\n\n}\n\n// Give each login hook callback a fresh cloned copy of the attempt\n// object, but don't clone the connection.\n//\nconst cloneAttemptWithConnection = (connection, attempt) => {\n  const clonedAttempt = EJSON.clone(attempt);\n  clonedAttempt.connection = connection;\n  return clonedAttempt;\n};\n\nconst tryLoginMethod = async (type, fn) => {\n  let result;\n  try {\n    result = await fn();\n  }\n  catch (e) {\n    result = {error: e};\n  }\n\n  if (result && !result.type && type)\n    result.type = type;\n\n  return result;\n};\n\nconst setupDefaultLoginHandlers = accounts => {\n  accounts.registerLoginHandler(\"resume\", function (options) {\n    return defaultResumeLoginHandler.call(this, accounts, options);\n  });\n};\n\n// Login handler for resume tokens.\nconst defaultResumeLoginHandler = async (accounts, options) => {\n  if (!options.resume)\n    return undefined;\n\n  check(options.resume, String);\n\n  const hashedToken = accounts._hashLoginToken(options.resume);\n\n  // First look for just the new-style hashed login token, to avoid\n  // sending the unhashed token to the database in a query if we don't\n  // need to.\n  let user = await accounts.users.findOneAsync(\n    {\"services.resume.loginTokens.hashedToken\": hashedToken},\n    {fields: {\"services.resume.loginTokens.$\": 1}});\n\n  if (! user) {\n    // If we didn't find the hashed login token, try also looking for\n    // the old-style unhashed token.  But we need to look for either\n    // the old-style token OR the new-style token, because another\n    // client connection logging in simultaneously might have already\n    // converted the token.\n    user =  await accounts.users.findOneAsync({\n        $or: [\n          {\"services.resume.loginTokens.hashedToken\": hashedToken},\n          {\"services.resume.loginTokens.token\": options.resume}\n        ]\n      },\n      // Note: Cannot use ...loginTokens.$ positional operator with $or query.\n      {fields: {\"services.resume.loginTokens\": 1}});\n  }\n\n  if (! user)\n    return {\n      error: new Meteor.Error(403, \"You've been logged out by the server. Please log in again.\")\n    };\n\n  // Find the token, which will either be an object with fields\n  // {hashedToken, when} for a hashed token or {token, when} for an\n  // unhashed token.\n  let oldUnhashedStyleToken;\n  let token = await user.services.resume.loginTokens.find(token =>\n    token.hashedToken === hashedToken\n  );\n  if (token) {\n    oldUnhashedStyleToken = false;\n  } else {\n     token = await user.services.resume.loginTokens.find(token =>\n      token.token === options.resume\n    );\n    oldUnhashedStyleToken = true;\n  }\n\n  const tokenExpires = accounts._tokenExpiration(token.when);\n  if (new Date() >= tokenExpires)\n    return {\n      userId: user._id,\n      error: new Meteor.Error(403, \"Your session has expired. Please log in again.\")\n    };\n\n  // Update to a hashed token when an unhashed token is encountered.\n  if (oldUnhashedStyleToken) {\n    // Only add the new hashed token if the old unhashed token still\n    // exists (this avoids resurrecting the token if it was deleted\n    // after we read it).  Using $addToSet avoids getting an index\n    // error if another client logging in simultaneously has already\n    // inserted the new hashed token.\n    await accounts.users.updateAsync(\n      {\n        _id: user._id,\n        \"services.resume.loginTokens.token\": options.resume\n      },\n      {$addToSet: {\n          \"services.resume.loginTokens\": {\n            \"hashedToken\": hashedToken,\n            \"when\": token.when\n          }\n        }}\n    );\n\n    // Remove the old token *after* adding the new, since otherwise\n    // another client trying to login between our removing the old and\n    // adding the new wouldn't find a token to login with.\n    await accounts.users.updateAsync(user._id, {\n      $pull: {\n        \"services.resume.loginTokens\": { \"token\": options.resume }\n      }\n    });\n  }\n\n  return {\n    userId: user._id,\n    stampedLoginToken: {\n      token: options.resume,\n      when: token.when\n    }\n  };\n};\n\nconst expirePasswordToken =\n  async (\n    accounts,\n    oldestValidDate,\n    tokenFilter,\n    userId\n  ) => {\n    // boolean value used to determine if this method was called from enroll account workflow\n    let isEnroll = false;\n    const userFilter = userId ? { _id: userId } : {};\n    // check if this method was called from enroll account workflow\n    if (tokenFilter['services.password.enroll.reason']) {\n      isEnroll = true;\n    }\n    let resetRangeOr = {\n      $or: [\n        { \"services.password.reset.when\": { $lt: oldestValidDate } },\n        { \"services.password.reset.when\": { $lt: +oldestValidDate } }\n      ]\n    };\n    if (isEnroll) {\n      resetRangeOr = {\n        $or: [\n          { \"services.password.enroll.when\": { $lt: oldestValidDate } },\n          { \"services.password.enroll.when\": { $lt: +oldestValidDate } }\n        ]\n      };\n    }\n    const expireFilter = { $and: [tokenFilter, resetRangeOr] };\n    if (isEnroll) {\n      await accounts.users.updateAsync({ ...userFilter, ...expireFilter }, {\n        $unset: {\n          \"services.password.enroll\": \"\"\n        }\n      }, { multi: true });\n    } else {\n      await accounts.users.updateAsync({ ...userFilter, ...expireFilter }, {\n        $unset: {\n          \"services.password.reset\": \"\"\n        }\n      }, { multi: true });\n    }\n\n  };\n\nconst setExpireTokensInterval = accounts => {\n  accounts.expireTokenInterval = Meteor.setInterval(async () => {\n   await accounts._expireTokens();\n   await accounts._expirePasswordResetTokens();\n   await accounts._expirePasswordEnrollTokens();\n  }, EXPIRE_TOKENS_INTERVAL_MS);\n};\n\nconst OAuthEncryption = Package[\"oauth-encryption\"]?.OAuthEncryption;\n\n// OAuth service data is temporarily stored in the pending credentials\n// collection during the oauth authentication process.  Sensitive data\n// such as access tokens are encrypted without the user id because\n// we don't know the user id yet.  We re-encrypt these fields with the\n// user id included when storing the service data permanently in\n// the users collection.\n//\nconst pinEncryptedFieldsToUser = (serviceData, userId) => {\n  Object.keys(serviceData).forEach(key => {\n    let value = serviceData[key];\n    if (OAuthEncryption?.isSealed(value))\n      value = OAuthEncryption.seal(OAuthEncryption.open(value), userId);\n    serviceData[key] = value;\n  });\n};\n\n// XXX see comment on Accounts.createUser in passwords_server about adding a\n// second \"server options\" argument.\nconst defaultCreateUserHook = (options, user) => {\n  if (options.profile)\n    user.profile = options.profile;\n  return user;\n};\n\n// Validate new user's email or Google/Facebook/GitHub account's email\nfunction defaultValidateNewUserHook(user) {\n  const domain = this._options.restrictCreationByEmailDomain;\n  if (!domain) {\n    return true;\n  }\n\n  let emailIsGood = false;\n  if (user.emails && user.emails.length > 0) {\n    emailIsGood = user.emails.reduce(\n      (prev, email) => prev || this._testEmailDomain(email.address), false\n    );\n  } else if (user.services && Object.values(user.services).length > 0) {\n    // Find any email of any service and check it\n    emailIsGood = Object.values(user.services).reduce(\n      (prev, service) => service.email && this._testEmailDomain(service.email),\n      false,\n    );\n  }\n\n  if (emailIsGood) {\n    return true;\n  }\n\n  if (typeof domain === 'string') {\n    throw new Meteor.Error(403, `@${domain} email required`);\n  } else {\n    throw new Meteor.Error(403, \"Email doesn't match the criteria.\");\n  }\n}\n\nconst setupUsersCollection = async users => {\n  ///\n  /// RESTRICTING WRITES TO USER OBJECTS\n  ///\n  users.allow({\n    // clients can modify the profile field of their own document, and\n    // nothing else.\n    update: (userId, user, fields, modifier) => {\n      // make sure it is our record\n      if (user._id !== userId) {\n        return false;\n      }\n\n      // user can only modify the 'profile' field. sets to multiple\n      // sub-keys (eg profile.foo and profile.bar) are merged into entry\n      // in the fields list.\n      if (fields.length !== 1 || fields[0] !== 'profile') {\n        return false;\n      }\n\n      return true;\n    },\n    fetch: ['_id'] // we only look at _id.\n  });\n\n  /// DEFAULT INDEXES ON USERS\n  await users.createIndexAsync('username', { unique: true, sparse: true });\n  await users.createIndexAsync('emails.address', { unique: true, sparse: true });\n  await users.createIndexAsync('services.resume.loginTokens.hashedToken',\n    { unique: true, sparse: true });\n  await users.createIndexAsync('services.resume.loginTokens.token',\n    { unique: true, sparse: true });\n  // For taking care of logoutOtherClients calls that crashed before the\n  // tokens were deleted.\n  await users.createIndexAsync('services.resume.haveLoginTokensToDelete',\n    { sparse: true });\n  // For expiring login tokens\n  await users.createIndexAsync(\"services.resume.loginTokens.when\", { sparse: true });\n  // For expiring password tokens\n  await users.createIndexAsync('services.password.reset.when', { sparse: true });\n  await users.createIndexAsync('services.password.enroll.when', { sparse: true });\n};\n\n\n// Generates permutations of all case variations of a given string.\nconst generateCasePermutationsForString = string => {\n  let permutations = [''];\n  for (let i = 0; i < string.length; i++) {\n    const ch = string.charAt(i);\n    permutations = [].concat(...(permutations.map(prefix => {\n      const lowerCaseChar = ch.toLowerCase();\n      const upperCaseChar = ch.toUpperCase();\n      // Don't add unnecessary permutations when ch is not a letter\n      if (lowerCaseChar === upperCaseChar) {\n        return [prefix + ch];\n      } else {\n        return [prefix + lowerCaseChar, prefix + upperCaseChar];\n      }\n    })));\n  }\n  return permutations;\n}\n"]}