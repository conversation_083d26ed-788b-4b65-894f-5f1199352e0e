{"version": 3, "names": ["exports", "validPid", "pid", "isNaN"], "sources": ["/tools/static-assets/server/boot-utils.js"], "sourcesContent": ["// Separated from boot.js for testing.\n\n// Check that we have a pid that looks like an integer (non-decimal\n// integer is okay).\nexports.validPid = function (pid) {\n  return ! isNaN(+pid);\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACAA,OAAO,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAChC,OAAO,CAAEC,KAAK,CAAC,CAACD,GAAG,CAAC;AACtB,CAAC", "ignoreList": [], "file": "tools/static-assets/server/boot-utils.js.map"}