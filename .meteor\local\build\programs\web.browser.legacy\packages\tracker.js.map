{"version": 3, "sources": ["meteor://💻app/packages/tracker/tracker.js"], "names": ["Tracker", "Deps", "active", "currentComputation", "_debugFunc", "Meteor", "_debug", "console", "error", "apply", "arguments", "_maybeSuppressMoreLogs", "<PERSON><PERSON><PERSON><PERSON>", "_suppressed_log_expected", "_suppress_log", "_throwOrLog", "from", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printArgs", "stack", "message", "name", "idx", "indexOf", "length", "push", "i", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>llowed", "f", "isClient", "args", "_noYieldsAllowed", "nextId", "pendingComputations", "<PERSON><PERSON><PERSON><PERSON>", "inFlush", "inCompute", "afterFlushCallbacks", "requireFlush", "_setImmediate", "_runFlush", "setTimeout", "constructingComputation", "Computation", "parent", "onError", "Error", "stopped", "invalidated", "firstRun", "_id", "_onInvalidateCallbacks", "_onStopCallbacks", "_parent", "_func", "_onError", "_recomputing", "firstRunPromise", "undefined", "errored", "_compute", "stop", "_proto", "prototype", "then", "onResolved", "onRejected", "catch", "onInvalidate", "_this", "nonreactive", "onStop", "_this2", "invalidate", "_this3", "_this4", "_this5", "previousInCompute", "withComputation", "Promise", "resolve", "_needsRecompute", "_recompute", "flush", "run", "Dependency", "_dependentsById", "Object", "create", "_proto2", "depend", "computation", "_this6", "id", "changed", "meteorBabelHelpers", "sanitizeForInObject", "hasDependents", "options", "finishSynchronously", "_throwF<PERSON><PERSON>Error", "recomputedCount", "finishedTry", "comp", "shift", "unshift", "func", "autorun", "c", "previousComputation", "afterFlush"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACAA,OAAO,GAAG,CAAC,CAAC;;AAEZ;AACA;AACA;AACA;AACAC,IAAI,GAAGD,OAAO;;AAEd;;AAEA;AACA;AACA;AACA;AACA;AACAA,OAAO,CAACE,MAAM,GAAG,KAAK;;AAEtB;;AAEA;AACA;AACA;AACA;AACA;AACAF,OAAO,CAACG,kBAAkB,GAAG,IAAI;AAEjC,SAASC,UAAUA,CAAA,EAAG;EACpB;EACA;EACA;EACA;EACA;EACA,OAAQ,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACC,MAAM,GAC3C,OAAOC,OAAO,KAAK,WAAW,IAAKA,OAAO,CAACC,KAAK,GACjD,YAAY;IAAED,OAAO,CAACC,KAAK,CAACC,KAAK,CAACF,OAAO,EAAEG,SAAS,CAAC;EAAE,CAAC,GACxD,YAAY,CAAC,CAAE;AAC1B;AAEA,SAASC,sBAAsBA,CAACC,cAAc,EAAE;EAC9C;EACA;EACA;EACA;EACA,IAAI,OAAOP,MAAM,KAAK,WAAW,EAAE;IACjC,IAAIA,MAAM,CAACQ,wBAAwB,CAAC,CAAC,EAAE;MACrCR,MAAM,CAACS,aAAa,CAACF,cAAc,GAAG,CAAC,CAAC;IAC1C;EACF;AACF;AAEA,SAASG,WAAWA,CAACC,IAAI,EAAEC,CAAC,EAAE;EAC5B,IAAIC,eAAe,EAAE;IACnB,MAAMD,CAAC;EACT,CAAC,MAAM;IACL,IAAIE,SAAS,GAAG,CAAC,yBAAyB,GAAGH,IAAI,GAAG,YAAY,CAAC;IACjE,IAAIC,CAAC,CAACG,KAAK,IAAIH,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,IAAI,EAAE;MAClC,IAAIC,GAAG,GAAGN,CAAC,CAACG,KAAK,CAACI,OAAO,CAACP,CAAC,CAACI,OAAO,CAAC;MACpC,IAAIE,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAGN,CAAC,CAACK,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;QAAE;QACxC;QACA,IAAIJ,OAAO,GAAGJ,CAAC,CAACK,IAAI,GAAG,IAAI,GAAGL,CAAC,CAACI,OAAO;QACvCF,SAAS,CAACO,IAAI,CAACL,OAAO,CAAC;MACzB;IACF;IACAF,SAAS,CAACO,IAAI,CAACT,CAAC,CAACG,KAAK,CAAC;IACvBT,sBAAsB,CAACQ,SAAS,CAACM,MAAM,CAAC;IAExC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,CAACM,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzCvB,UAAU,CAAC,CAAC,CAACe,SAAS,CAACQ,CAAC,CAAC,CAAC;IAC5B;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,CAAC,EAAE;EAC9B,IAAK,OAAOxB,MAAM,KAAK,WAAW,IAAKA,MAAM,CAACyB,QAAQ,EAAE;IACtD,OAAOD,CAAC;EACV,CAAC,MAAM;IACL,OAAO,YAAY;MACjB,IAAIE,IAAI,GAAGrB,SAAS;MACpBL,MAAM,CAAC2B,gBAAgB,CAAC,YAAY;QAClCH,CAAC,CAACpB,KAAK,CAAC,IAAI,EAAEsB,IAAI,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC;EACH;AACF;AAEA,IAAIE,MAAM,GAAG,CAAC;AACd;AACA,IAAIC,mBAAmB,GAAG,EAAE;AAC5B;AACA,IAAIC,SAAS,GAAG,KAAK;AACrB;AACA,IAAIC,OAAO,GAAG,KAAK;AACnB;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,KAAK;AACrB;AACA;AACA;AACA;AACA;AACA,IAAInB,eAAe,GAAG,KAAK;AAE3B,IAAIoB,mBAAmB,GAAG,EAAE;AAE5B,SAASC,YAAYA,CAAA,EAAG;EACtB,IAAI,CAAEJ,SAAS,EAAE;IACf;IACA,IAAI,OAAO9B,MAAM,KAAK,WAAW,EAC/BA,MAAM,CAACmC,aAAa,CAACxC,OAAO,CAACyC,SAAS,CAAC,CAAC,KAExCC,UAAU,CAAC1C,OAAO,CAACyC,SAAS,EAAE,CAAC,CAAC;IAClCN,SAAS,GAAG,IAAI;EAClB;AACF;;AAEA;AACA;AACA,IAAIQ,uBAAuB,GAAG,KAAK;;AAEnC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3C,OAAO,CAAC4C,WAAW;EACjB,SAAAA,YAAYf,CAAC,EAAEgB,MAAM,EAAEC,OAAO,EAAE;IAC9B,IAAI,CAAEH,uBAAuB,EAC3B,MAAM,IAAII,KAAK,CACb,iEAAiE,CAAC;IACtEJ,uBAAuB,GAAG,KAAK;;IAE/B;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACK,OAAO,GAAG,KAAK;;IAEpB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,WAAW,GAAG,KAAK;;IAExB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,QAAQ,GAAG,IAAI;IAEpB,IAAI,CAACC,GAAG,GAAGlB,MAAM,EAAE;IACnB,IAAI,CAACmB,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B;IACA;IACA,IAAI,CAACC,OAAO,GAAGT,MAAM;IACrB,IAAI,CAACU,KAAK,GAAG1B,CAAC;IACd,IAAI,CAAC2B,QAAQ,GAAGV,OAAO;IACvB,IAAI,CAACW,YAAY,GAAG,KAAK;;IAEzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,eAAe,GAAGC,SAAS;IAEhC,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAI;MACF,IAAI,CAACC,QAAQ,CAAC,CAAC;MACfD,OAAO,GAAG,KAAK;IACjB,CAAC,SAAS;MACR,IAAI,CAACV,QAAQ,GAAG,KAAK;MACrB,IAAIU,OAAO,EACT,IAAI,CAACE,IAAI,CAAC,CAAC;IACf;EACF;;EAGE;AACJ;AACA;AACA;AACA;AACA;EALI,IAAAC,MAAA,GAAAnB,WAAA,CAAAoB,SAAA;EAAAD,MAAA,CAMAE,IAAI;IAAJ,SAAAA,IAAIA,CAACC,UAAU,EAAEC,UAAU,EAAE;MAC3B,OAAO,IAAI,CAACT,eAAe,CAACO,IAAI,CAACC,UAAU,EAAEC,UAAU,CAAC;IAC1D;IAAC,OAFDF,IAAI;EAAA;EAAAF,MAAA,CAKJK,KAAK;IAAL,SAAAA,MAAKA,CAACD,UAAU,EAAE;MAChB,OAAO,IAAI,CAACT,eAAe,CAACU,KAAK,CAACD,UAAU,CAAC;IAC/C;IAAC,OAFDC,MAAK;EAAA;EAIP;EAEA;AACF;AACA;AACA;AACA;EAJEL,MAAA,CAKAM,YAAY;IAAZ,SAAAA,YAAYA,CAACxC,CAAC,EAAE;MAAA,IAAAyC,KAAA;MACd,IAAI,OAAOzC,CAAC,KAAK,UAAU,EACzB,MAAM,IAAIkB,KAAK,CAAC,kCAAkC,CAAC;MAErD,IAAI,IAAI,CAACE,WAAW,EAAE;QACpBjD,OAAO,CAACuE,WAAW,CAAC,YAAM;UACxB3C,mBAAmB,CAACC,CAAC,CAAC,CAACyC,KAAI,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAClB,sBAAsB,CAAC1B,IAAI,CAACG,CAAC,CAAC;MACrC;IACF;IAAC,OAXDwC,YAAY;EAAA;EAaZ;AACF;AACA;AACA;AACA;EAJE;EAAAN,MAAA,CAKAS,MAAM;IAAN,SAAAA,MAAMA,CAAC3C,CAAC,EAAE;MAAA,IAAA4C,MAAA;MACR,IAAI,OAAO5C,CAAC,KAAK,UAAU,EACzB,MAAM,IAAIkB,KAAK,CAAC,4BAA4B,CAAC;MAE/C,IAAI,IAAI,CAACC,OAAO,EAAE;QAChBhD,OAAO,CAACuE,WAAW,CAAC,YAAM;UACxB3C,mBAAmB,CAACC,CAAC,CAAC,CAAC4C,MAAI,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACpB,gBAAgB,CAAC3B,IAAI,CAACG,CAAC,CAAC;MAC/B;IACF;IAAC,OAXD2C,MAAM;EAAA,IAaN;EAEA;AACF;AACA;AACA;EAHE;EAAAT,MAAA,CAIAW,UAAU;IAAV,SAAAA,UAAUA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACX,IAAI,CAAE,IAAI,CAAC1B,WAAW,EAAE;QACtB;QACA;QACA,IAAI,CAAE,IAAI,CAACQ,YAAY,IAAI,CAAE,IAAI,CAACT,OAAO,EAAE;UACzCT,YAAY,CAAC,CAAC;UACdL,mBAAmB,CAACR,IAAI,CAAC,IAAI,CAAC;QAChC;QAEA,IAAI,CAACuB,WAAW,GAAG,IAAI;;QAEvB;QACA;QACA,KAAI,IAAItB,CAAC,GAAG,CAAC,EAAEE,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACuB,sBAAsB,CAACzB,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;UACzD3B,OAAO,CAACuE,WAAW,CAAC,YAAM;YACxB3C,mBAAmB,CAACC,CAAC,CAAC,CAAC8C,MAAI,CAAC;UAC9B,CAAC,CAAC;QACJ;QACA,IAAI,CAACvB,sBAAsB,GAAG,EAAE;MAClC;IACF;IAAC,OApBDsB,UAAU;EAAA,IAsBV;EAEA;AACF;AACA;AACA;EAHE;EAAAX,MAAA,CAIAD,IAAI;IAAJ,SAAAA,IAAIA,CAAA,EAAG;MAAA,IAAAc,MAAA;MACL,IAAI,CAAE,IAAI,CAAC5B,OAAO,EAAE;QAClB,IAAI,CAACA,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC0B,UAAU,CAAC,CAAC;QACjB,KAAI,IAAI/C,CAAC,GAAG,CAAC,EAAEE,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACwB,gBAAgB,CAAC1B,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;UACnD3B,OAAO,CAACuE,WAAW,CAAC,YAAM;YACxB3C,mBAAmB,CAACC,CAAC,CAAC,CAAC+C,MAAI,CAAC;UAC9B,CAAC,CAAC;QACJ;QACA,IAAI,CAACvB,gBAAgB,GAAG,EAAE;MAC5B;IACF;IAAC,OAXDS,IAAI;EAAA;EAAAC,MAAA,CAaJF,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAG;MAAA,IAAAgB,MAAA;MACT,IAAI,CAAC5B,WAAW,GAAG,KAAK;MAExB,IAAI6B,iBAAiB,GAAGzC,SAAS;MACjCA,SAAS,GAAG,IAAI;MAEhB,IAAI;QACF;QACA;QACA,IAAMqB,eAAe,GAAG1D,OAAO,CAAC+E,eAAe,CAAC,IAAI,EAAE,YAAM;UAC1D,OAAOnD,mBAAmB,CAACiD,MAAI,CAACtB,KAAK,CAAC,CAACsB,MAAI,CAAC;QAC9C,CAAC,CAAC;QACF;QACA;QACA,IAAI,IAAI,CAAC3B,QAAQ,EAAE;UACjB,IAAI,CAACQ,eAAe,GAAGsB,OAAO,CAACC,OAAO,CAACvB,eAAe,CAAC;QACzD;MACF,CAAC,SAAS;QACRrB,SAAS,GAAGyC,iBAAiB;MAC/B;IACF;IAAC,OApBDjB,QAAQ;EAAA;EAAAE,MAAA,CAsBRmB,eAAe;IAAf,SAAAA,eAAeA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACjC,WAAW,IAAI,CAAE,IAAI,CAACD,OAAO;IAC3C;IAAC,OAFDkC,eAAe;EAAA;EAAAnB,MAAA,CAIfoB,UAAU;IAAV,SAAAA,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC1B,YAAY,GAAG,IAAI;MACxB,IAAI;QACF,IAAI,IAAI,CAACyB,eAAe,CAAC,CAAC,EAAE;UAC1B,IAAI;YACF,IAAI,CAACrB,QAAQ,CAAC,CAAC;UACjB,CAAC,CAAC,OAAO5C,CAAC,EAAE;YACV,IAAI,IAAI,CAACuC,QAAQ,EAAE;cACjB,IAAI,CAACA,QAAQ,CAACvC,CAAC,CAAC;YAClB,CAAC,MAAM;cACLF,WAAW,CAAC,WAAW,EAAEE,CAAC,CAAC;YAC7B;UACF;QACF;MACF,CAAC,SAAS;QACR,IAAI,CAACwC,YAAY,GAAG,KAAK;MAC3B;IACF;IAAC,OAjBD0B,UAAU;EAAA;EAmBV;AACF;AACA;AACA;AACA;AACA;EALE;EAAApB,MAAA,CAMAqB,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAAC3B,YAAY,EACnB;MAEF,IAAI,CAAC0B,UAAU,CAAC,CAAC;IACnB;IAAC,OALDC,KAAK;EAAA;EAOL;AACF;AACA;AACA;AACA;EAJE;EAAArB,MAAA,CAKAsB,GAAG;IAAH,SAAAA,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACX,UAAU,CAAC,CAAC;MACjB,IAAI,CAACU,KAAK,CAAC,CAAC;IACd;IAAC,OAHDC,GAAG;EAAA;EAAA,OAAAzC,WAAA;AAAA,GAIJ;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA5C,OAAO,CAACsF,UAAU;EAChB,SAAAA,WAAA,EAAc;IACZ,IAAI,CAACC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC5C;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE,IAAAC,OAAA,GAAAJ,UAAA,CAAAtB,SAAA;EAAA0B,OAAA,CAUAC,MAAM;IAAN,SAAAA,MAAMA,CAACC,WAAW,EAAE;MAAA,IAAAC,MAAA;MAClB,IAAI,CAAED,WAAW,EAAE;QACjB,IAAI,CAAE5F,OAAO,CAACE,MAAM,EAClB,OAAO,KAAK;QAEd0F,WAAW,GAAG5F,OAAO,CAACG,kBAAkB;MAC1C;MACA,IAAI2F,EAAE,GAAGF,WAAW,CAACzC,GAAG;MACxB,IAAI,EAAG2C,EAAE,IAAI,IAAI,CAACP,eAAe,CAAC,EAAE;QAClC,IAAI,CAACA,eAAe,CAACO,EAAE,CAAC,GAAGF,WAAW;QACtCA,WAAW,CAACvB,YAAY,CAAC,YAAM;UAC7B,OAAOwB,MAAI,CAACN,eAAe,CAACO,EAAE,CAAC;QACjC,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd;IAAC,OAhBDH,MAAM;EAAA,IAkBN;EAEA;AACF;AACA;AACA;EAHE;EAAAD,OAAA,CAIAK,OAAO;IAAP,SAAAA,OAAOA,CAAA,EAAG;MACR,KAAK,IAAID,EAAE,IAAAE,kBAAA,CAAAC,mBAAA,CAAI,IAAI,CAACV,eAAe,GACjC,IAAI,CAACA,eAAe,CAACO,EAAE,CAAC,CAACpB,UAAU,CAAC,CAAC;IACzC;IAAC,OAHDqB,OAAO;EAAA,IAKP;EAEA;AACF;AACA;AACA;AACA;EAJE;EAAAL,OAAA,CAKAQ,aAAa;IAAb,SAAAA,aAAaA,CAAA,EAAG;MACd,KAAK,IAAIJ,EAAE,IAAAE,kBAAA,CAAAC,mBAAA,CAAI,IAAI,CAACV,eAAe,GACjC,OAAO,IAAI;MACb,OAAO,KAAK;IACd;IAAC,OAJDW,aAAa;EAAA;EAAA,OAAAZ,UAAA;AAAA,GAKd;;AAED;;AAEA;AACA;AACA;AACA;AACAtF,OAAO,CAACoF,KAAK,GAAG,UAAUe,OAAO,EAAE;EACjCnG,OAAO,CAACyC,SAAS,CAAC;IAAE2D,mBAAmB,EAAE,IAAI;IACzBlF,eAAe,EAAEiF,OAAO,IAAIA,OAAO,CAACE;EAAiB,CAAC,CAAC;AAC7E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACArG,OAAO,CAACoC,OAAO,GAAG,YAAY;EAC5B,OAAOA,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACApC,OAAO,CAACyC,SAAS,GAAG,UAAU0D,OAAO,EAAE;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAInG,OAAO,CAACoC,OAAO,CAAC,CAAC,EACnB,MAAM,IAAIW,KAAK,CAAC,yCAAyC,CAAC;EAE5D,IAAIV,SAAS,EACX,MAAM,IAAIU,KAAK,CAAC,oCAAoC,CAAC;EAEvDoD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB/D,OAAO,GAAG,IAAI;EACdD,SAAS,GAAG,IAAI;EAChBjB,eAAe,GAAG,CAAC,CAAEiF,OAAO,CAACjF,eAAe;EAE5C,IAAIoF,eAAe,GAAG,CAAC;EACvB,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAI;IACF,OAAOrE,mBAAmB,CAACT,MAAM,IAC1Ba,mBAAmB,CAACb,MAAM,EAAE;MAEjC;MACA,OAAOS,mBAAmB,CAACT,MAAM,EAAE;QACjC,IAAI+E,IAAI,GAAGtE,mBAAmB,CAACuE,KAAK,CAAC,CAAC;QACtCD,IAAI,CAACrB,UAAU,CAAC,CAAC;QACjB,IAAIqB,IAAI,CAACtB,eAAe,CAAC,CAAC,EAAE;UAC1BhD,mBAAmB,CAACwE,OAAO,CAACF,IAAI,CAAC;QACnC;QAEA,IAAI,CAAEL,OAAO,CAACC,mBAAmB,IAAI,EAAEE,eAAe,GAAG,IAAI,EAAE;UAC7DC,WAAW,GAAG,IAAI;UAClB;QACF;MACF;MAEA,IAAIjE,mBAAmB,CAACb,MAAM,EAAE;QAC9B;QACA;QACA,IAAIkF,IAAI,GAAGrE,mBAAmB,CAACmE,KAAK,CAAC,CAAC;QACtC,IAAI;UACFE,IAAI,CAAC,CAAC;QACR,CAAC,CAAC,OAAO1F,CAAC,EAAE;UACVF,WAAW,CAAC,YAAY,EAAEE,CAAC,CAAC;QAC9B;MACF;IACF;IACAsF,WAAW,GAAG,IAAI;EACpB,CAAC,SAAS;IACR,IAAI,CAAEA,WAAW,EAAE;MACjB;MACAnE,OAAO,GAAG,KAAK,CAAC,CAAC;MACjB;MACApC,OAAO,CAACyC,SAAS,CAAC;QAChB2D,mBAAmB,EAAED,OAAO,CAACC,mBAAmB;QAChDlF,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ;IACAiB,SAAS,GAAG,KAAK;IACjBC,OAAO,GAAG,KAAK;IACf,IAAIF,mBAAmB,CAACT,MAAM,IAAIa,mBAAmB,CAACb,MAAM,EAAE;MAC5D;MACA;MACA;MACA,IAAI0E,OAAO,CAACC,mBAAmB,EAAE;QAC/B,MAAM,IAAIrD,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAE;MAC9C;MACAL,UAAU,CAACH,YAAY,EAAE,EAAE,CAAC;IAC9B;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAvC,OAAO,CAAC4G,OAAO,GAAG,UAAU/E,CAAC,EAAgB;EAAA,IAAdsE,OAAO,GAAAzF,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAiD,SAAA,GAAAjD,SAAA,MAAG,CAAC,CAAC;EACzC,IAAI,OAAOmB,CAAC,KAAK,UAAU,EACzB,MAAM,IAAIkB,KAAK,CAAC,8CAA8C,CAAC;EAEjEJ,uBAAuB,GAAG,IAAI;EAC9B,IAAIkE,CAAC,GAAG,IAAI7G,OAAO,CAAC4C,WAAW,CAACf,CAAC,EAAE7B,OAAO,CAACG,kBAAkB,EAAEgG,OAAO,CAACrD,OAAO,CAAC;EAE/E,IAAI9C,OAAO,CAACE,MAAM,EAChBF,OAAO,CAACqE,YAAY,CAAC,YAAY;IAC/BwC,CAAC,CAAC/C,IAAI,CAAC,CAAC;EACV,CAAC,CAAC;EAEJ,OAAO+C,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA7G,OAAO,CAACuE,WAAW,GAAG,UAAU1C,CAAC,EAAE;EACjC,OAAO7B,OAAO,CAAC+E,eAAe,CAAC,IAAI,EAAElD,CAAC,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA7B,OAAO,CAAC+E,eAAe,GAAG,UAAUa,WAAW,EAAE/D,CAAC,EAAE;EAClD,IAAIiF,mBAAmB,GAAG9G,OAAO,CAACG,kBAAkB;EAEpDH,OAAO,CAACG,kBAAkB,GAAGyF,WAAW;EACxC5F,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC0F,WAAW;EAE9B,IAAI;IACF,OAAO/D,CAAC,CAAC,CAAC;EACZ,CAAC,SAAS;IACR7B,OAAO,CAACG,kBAAkB,GAAG2G,mBAAmB;IAChD9G,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC4G,mBAAmB;EACxC;AACF,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA9G,OAAO,CAACqE,YAAY,GAAG,UAAUxC,CAAC,EAAE;EAClC,IAAI,CAAE7B,OAAO,CAACE,MAAM,EAClB,MAAM,IAAI6C,KAAK,CAAC,oDAAoD,CAAC;EAEvE/C,OAAO,CAACG,kBAAkB,CAACkE,YAAY,CAACxC,CAAC,CAAC;AAC5C,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA7B,OAAO,CAAC+G,UAAU,GAAG,UAAUlF,CAAC,EAAE;EAChCS,mBAAmB,CAACZ,IAAI,CAACG,CAAC,CAAC;EAC3BU,YAAY,CAAC,CAAC;AAChB,CAAC,C", "file": "/packages/tracker.js", "sourcesContent": ["/////////////////////////////////////////////////////\n// Package docs at http://docs.meteor.com/#tracker //\n/////////////////////////////////////////////////////\n\n/**\n * @namespace Tracker\n * @summary The namespace for Tracker-related methods.\n */\nTracker = {};\n\n/**\n * @namespace Deps\n * @deprecated\n */\nDeps = Tracker;\n\n// http://docs.meteor.com/#tracker_active\n\n/**\n * @summary True if there is a current computation, meaning that dependencies on reactive data sources will be tracked and potentially cause the current computation to be rerun.\n * @locus Client\n * @type {Boolean}\n */\nTracker.active = false;\n\n// http://docs.meteor.com/#tracker_currentcomputation\n\n/**\n * @summary The current computation, or `null` if there isn't one.  The current computation is the [`Tracker.Computation`](#tracker_computation) object created by the innermost active call to `Tracker.autorun`, and it's the computation that gains dependencies when reactive data sources are accessed.\n * @locus Client\n * @type {Tracker.Computation}\n */\nTracker.currentComputation = null;\n\nfunction _debugFunc() {\n  // We want this code to work without Meteor, and also without\n  // \"console\" (which is technically non-standard and may be missing\n  // on some browser we come across, like it was on IE 7).\n  //\n  // Lazy evaluation because `Meteor` does not exist right away.(??)\n  return (typeof Meteor !== \"undefined\" ? Meteor._debug :\n          ((typeof console !== \"undefined\") && console.error ?\n           function () { console.error.apply(console, arguments); } :\n           function () {}));\n}\n\nfunction _maybeSuppressMoreLogs(messagesLength) {\n  // Sometimes when running tests, we intentionally suppress logs on expected\n  // printed errors. Since the current implementation of _throwOrLog can log\n  // multiple separate log messages, suppress all of them if at least one suppress\n  // is expected as we still want them to count as one.\n  if (typeof Meteor !== \"undefined\") {\n    if (Meteor._suppressed_log_expected()) {\n      Meteor._suppress_log(messagesLength - 1);\n    }\n  }\n}\n\nfunction _throwOrLog(from, e) {\n  if (throwFirstError) {\n    throw e;\n  } else {\n    var printArgs = [\"Exception from Tracker \" + from + \" function:\"];\n    if (e.stack && e.message && e.name) {\n      var idx = e.stack.indexOf(e.message);\n      if (idx < 0 || idx > e.name.length + 2) { // check for \"Error: \"\n        // message is not part of the stack\n        var message = e.name + \": \" + e.message;\n        printArgs.push(message);\n      }\n    }\n    printArgs.push(e.stack);\n    _maybeSuppressMoreLogs(printArgs.length);\n\n    for (var i = 0; i < printArgs.length; i++) {\n      _debugFunc()(printArgs[i]);\n    }\n  }\n}\n\n// Takes a function `f`, and wraps it in a `Meteor._noYieldsAllowed`\n// block if we are running on the server. On the client, returns the\n// original function (since `Meteor._noYieldsAllowed` is a\n// no-op). This has the benefit of not adding an unnecessary stack\n// frame on the client.\nfunction withNoYieldsAllowed(f) {\n  if ((typeof Meteor === 'undefined') || Meteor.isClient) {\n    return f;\n  } else {\n    return function () {\n      var args = arguments;\n      Meteor._noYieldsAllowed(function () {\n        f.apply(null, args);\n      });\n    };\n  }\n}\n\nvar nextId = 1;\n// computations whose callbacks we should call at flush time\nvar pendingComputations = [];\n// `true` if a Tracker.flush is scheduled, or if we are in Tracker.flush now\nvar willFlush = false;\n// `true` if we are in Tracker.flush now\nvar inFlush = false;\n// `true` if we are computing a computation now, either first time\n// or recompute.  This matches Tracker.active unless we are inside\n// Tracker.nonreactive, which nullfies currentComputation even though\n// an enclosing computation may still be running.\nvar inCompute = false;\n// `true` if the `_throwFirstError` option was passed in to the call\n// to Tracker.flush that we are in. When set, throw rather than log the\n// first error encountered while flushing. Before throwing the error,\n// finish flushing (from a finally block), logging any subsequent\n// errors.\nvar throwFirstError = false;\n\nvar afterFlushCallbacks = [];\n\nfunction requireFlush() {\n  if (! willFlush) {\n    // We want this code to work without Meteor, see debugFunc above\n    if (typeof Meteor !== \"undefined\")\n      Meteor._setImmediate(Tracker._runFlush);\n    else\n      setTimeout(Tracker._runFlush, 0);\n    willFlush = true;\n  }\n}\n\n// Tracker.Computation constructor is visible but private\n// (throws an error if you try to call it)\nvar constructingComputation = false;\n\n//\n// http://docs.meteor.com/#tracker_computation\n\n/**\n * @summary A Computation object represents code that is repeatedly rerun\n * in response to\n * reactive data changes. Computations don't have return values; they just\n * perform actions, such as rerendering a template on the screen. Computations\n * are created using Tracker.autorun. Use stop to prevent further rerunning of a\n * computation.\n * @instancename computation\n */\nTracker.Computation = class Computation {\n  constructor(f, parent, onError) {\n    if (! constructingComputation)\n      throw new Error(\n        \"Tracker.Computation constructor is private; use Tracker.autorun\");\n    constructingComputation = false;\n\n    // http://docs.meteor.com/#computation_stopped\n\n    /**\n     * @summary True if this computation has been stopped.\n     * @locus Client\n     * @memberOf Tracker.Computation\n     * @instance\n     * @name  stopped\n     */\n    this.stopped = false;\n\n    // http://docs.meteor.com/#computation_invalidated\n\n    /**\n     * @summary True if this computation has been invalidated (and not yet rerun), or if it has been stopped.\n     * @locus Client\n     * @memberOf Tracker.Computation\n     * @instance\n     * @name  invalidated\n     * @type {Boolean}\n     */\n    this.invalidated = false;\n\n    // http://docs.meteor.com/#computation_firstrun\n\n    /**\n     * @summary True during the initial run of the computation at the time `Tracker.autorun` is called, and false on subsequent reruns and at other times.\n     * @locus Client\n     * @memberOf Tracker.Computation\n     * @instance\n     * @name  firstRun\n     * @type {Boolean}\n     */\n    this.firstRun = true;\n\n    this._id = nextId++;\n    this._onInvalidateCallbacks = [];\n    this._onStopCallbacks = [];\n    // the plan is at some point to use the parent relation\n    // to constrain the order that computations are processed\n    this._parent = parent;\n    this._func = f;\n    this._onError = onError;\n    this._recomputing = false;\n\n    /**\n     * @summary Forces autorun blocks to be executed in synchronous-looking order by storing the value autorun promise thus making it awaitable.\n     * @locus Client\n     * @memberOf Tracker.Computation\n     * @instance\n     * @name  firstRunPromise\n     * @returns {Promise<unknown>}\n     */\n    this.firstRunPromise = undefined;\n\n    var errored = true;\n    try {\n      this._compute();\n      errored = false;\n    } finally {\n      this.firstRun = false;\n      if (errored)\n        this.stop();\n    }\n  }\n\n\n    /**\n   * Resolves the firstRunPromise with the result of the autorun function.\n   * @param {*} onResolved\n   * @param {*} onRejected\n   * @returns{Promise<unknown}\n   */\n    then(onResolved, onRejected) {\n      return this.firstRunPromise.then(onResolved, onRejected);\n    };\n\n\n    catch(onRejected) {\n      return this.firstRunPromise.catch(onRejected)\n    };\n\n  // http://docs.meteor.com/#computation_oninvalidate\n\n  /**\n   * @summary Registers `callback` to run when this computation is next invalidated, or runs it immediately if the computation is already invalidated.  The callback is run exactly once and not upon future invalidations unless `onInvalidate` is called again after the computation becomes valid again.\n   * @locus Client\n   * @param {Function} callback Function to be called on invalidation. Receives one argument, the computation that was invalidated.\n   */\n  onInvalidate(f) {\n    if (typeof f !== 'function')\n      throw new Error(\"onInvalidate requires a function\");\n\n    if (this.invalidated) {\n      Tracker.nonreactive(() => {\n        withNoYieldsAllowed(f)(this);\n      });\n    } else {\n      this._onInvalidateCallbacks.push(f);\n    }\n  }\n\n  /**\n   * @summary Registers `callback` to run when this computation is stopped, or runs it immediately if the computation is already stopped.  The callback is run after any `onInvalidate` callbacks.\n   * @locus Client\n   * @param {Function} callback Function to be called on stop. Receives one argument, the computation that was stopped.\n   */\n  onStop(f) {\n    if (typeof f !== 'function')\n      throw new Error(\"onStop requires a function\");\n\n    if (this.stopped) {\n      Tracker.nonreactive(() => {\n        withNoYieldsAllowed(f)(this);\n      });\n    } else {\n      this._onStopCallbacks.push(f);\n    }\n  }\n\n  // http://docs.meteor.com/#computation_invalidate\n\n  /**\n   * @summary Invalidates this computation so that it will be rerun.\n   * @locus Client\n   */\n  invalidate() {\n    if (! this.invalidated) {\n      // if we're currently in _recompute(), don't enqueue\n      // ourselves, since we'll rerun immediately anyway.\n      if (! this._recomputing && ! this.stopped) {\n        requireFlush();\n        pendingComputations.push(this);\n      }\n\n      this.invalidated = true;\n\n      // callbacks can't add callbacks, because\n      // this.invalidated === true.\n      for(var i = 0, f; f = this._onInvalidateCallbacks[i]; i++) {\n        Tracker.nonreactive(() => {\n          withNoYieldsAllowed(f)(this);\n        });\n      }\n      this._onInvalidateCallbacks = [];\n    }\n  }\n\n  // http://docs.meteor.com/#computation_stop\n\n  /**\n   * @summary Prevents this computation from rerunning.\n   * @locus Client\n   */\n  stop() {\n    if (! this.stopped) {\n      this.stopped = true;\n      this.invalidate();\n      for(var i = 0, f; f = this._onStopCallbacks[i]; i++) {\n        Tracker.nonreactive(() => {\n          withNoYieldsAllowed(f)(this);\n        });\n      }\n      this._onStopCallbacks = [];\n    }\n  }\n\n  _compute() {\n    this.invalidated = false;\n\n    var previousInCompute = inCompute;\n    inCompute = true;\n\n    try {\n      // In case of async functions, the result of this function will contain the promise of the autorun function\n      // & make autoruns await-able.\n      const firstRunPromise = Tracker.withComputation(this, () => {\n        return withNoYieldsAllowed(this._func)(this);\n      });\n      // We'll store the firstRunPromise on the computation so it can be awaited by the callers, but only\n      // during the first run. We don't want things to get mixed up.\n      if (this.firstRun) {\n        this.firstRunPromise = Promise.resolve(firstRunPromise);\n      }\n    } finally {\n      inCompute = previousInCompute;\n    }\n  }\n\n  _needsRecompute() {\n    return this.invalidated && ! this.stopped;\n  }\n\n  _recompute() {\n    this._recomputing = true;\n    try {\n      if (this._needsRecompute()) {\n        try {\n          this._compute();\n        } catch (e) {\n          if (this._onError) {\n            this._onError(e);\n          } else {\n            _throwOrLog(\"recompute\", e);\n          }\n        }\n      }\n    } finally {\n      this._recomputing = false;\n    }\n  }\n\n  /**\n   * @summary Process the reactive updates for this computation immediately\n   * and ensure that the computation is rerun. The computation is rerun only\n   * if it is invalidated.\n   * @locus Client\n   */\n  flush() {\n    if (this._recomputing)\n      return;\n\n    this._recompute();\n  }\n\n  /**\n   * @summary Causes the function inside this computation to run and\n   * synchronously process all reactive updtes.\n   * @locus Client\n   */\n  run() {\n    this.invalidate();\n    this.flush();\n  }\n};\n\n//\n// http://docs.meteor.com/#tracker_dependency\n\n/**\n * @summary A Dependency represents an atomic unit of reactive data that a\n * computation might depend on. Reactive data sources such as Session or\n * Minimongo internally create different Dependency objects for different\n * pieces of data, each of which may be depended on by multiple computations.\n * When the data changes, the computations are invalidated.\n * @class\n * @instanceName dependency\n */\nTracker.Dependency = class Dependency {\n  constructor() {\n    this._dependentsById = Object.create(null);\n  }\n\n  // http://docs.meteor.com/#dependency_depend\n  //\n  // Adds `computation` to this set if it is not already\n  // present.  Returns true if `computation` is a new member of the set.\n  // If no argument, defaults to currentComputation, or does nothing\n  // if there is no currentComputation.\n\n  /**\n   * @summary Declares that the current computation (or `fromComputation` if given) depends on `dependency`.  The computation will be invalidated the next time `dependency` changes.\n\n   If there is no current computation and `depend()` is called with no arguments, it does nothing and returns false.\n\n   Returns true if the computation is a new dependent of `dependency` rather than an existing one.\n   * @locus Client\n   * @param {Tracker.Computation} [fromComputation] An optional computation declared to depend on `dependency` instead of the current computation.\n   * @returns {Boolean}\n   */\n  depend(computation) {\n    if (! computation) {\n      if (! Tracker.active)\n        return false;\n\n      computation = Tracker.currentComputation;\n    }\n    var id = computation._id;\n    if (! (id in this._dependentsById)) {\n      this._dependentsById[id] = computation;\n      computation.onInvalidate(() => {\n        delete this._dependentsById[id];\n      });\n      return true;\n    }\n    return false;\n  }\n\n  // http://docs.meteor.com/#dependency_changed\n\n  /**\n   * @summary Invalidate all dependent computations immediately and remove them as dependents.\n   * @locus Client\n   */\n  changed() {\n    for (var id in this._dependentsById)\n      this._dependentsById[id].invalidate();\n  }\n\n  // http://docs.meteor.com/#dependency_hasdependents\n\n  /**\n   * @summary True if this Dependency has one or more dependent Computations, which would be invalidated if this Dependency were to change.\n   * @locus Client\n   * @returns {Boolean}\n   */\n  hasDependents() {\n    for (var id in this._dependentsById)\n      return true;\n    return false;\n  }\n};\n\n// http://docs.meteor.com/#tracker_flush\n\n/**\n * @summary Process all reactive updates immediately and ensure that all invalidated computations are rerun.\n * @locus Client\n */\nTracker.flush = function (options) {\n  Tracker._runFlush({ finishSynchronously: true,\n                      throwFirstError: options && options._throwFirstError });\n};\n\n/**\n * @summary True if we are computing a computation now, either first time or recompute.  This matches Tracker.active unless we are inside Tracker.nonreactive, which nullfies currentComputation even though an enclosing computation may still be running.\n * @locus Client\n * @returns {Boolean}\n */\nTracker.inFlush = function () {\n  return inFlush;\n}\n\n// Run all pending computations and afterFlush callbacks.  If we were not called\n// directly via Tracker.flush, this may return before they're all done to allow\n// the event loop to run a little before continuing.\nTracker._runFlush = function (options) {\n  // XXX What part of the comment below is still true? (We no longer\n  // have Spark)\n  //\n  // Nested flush could plausibly happen if, say, a flush causes\n  // DOM mutation, which causes a \"blur\" event, which runs an\n  // app event handler that calls Tracker.flush.  At the moment\n  // Spark blocks event handlers during DOM mutation anyway,\n  // because the LiveRange tree isn't valid.  And we don't have\n  // any useful notion of a nested flush.\n  //\n  // https://app.asana.com/0/159908330244/385138233856\n  if (Tracker.inFlush())\n    throw new Error(\"Can't call Tracker.flush while flushing\");\n\n  if (inCompute)\n    throw new Error(\"Can't flush inside Tracker.autorun\");\n\n  options = options || {};\n\n  inFlush = true;\n  willFlush = true;\n  throwFirstError = !! options.throwFirstError;\n\n  var recomputedCount = 0;\n  var finishedTry = false;\n  try {\n    while (pendingComputations.length ||\n           afterFlushCallbacks.length) {\n\n      // recompute all pending computations\n      while (pendingComputations.length) {\n        var comp = pendingComputations.shift();\n        comp._recompute();\n        if (comp._needsRecompute()) {\n          pendingComputations.unshift(comp);\n        }\n\n        if (! options.finishSynchronously && ++recomputedCount > 1000) {\n          finishedTry = true;\n          return;\n        }\n      }\n\n      if (afterFlushCallbacks.length) {\n        // call one afterFlush callback, which may\n        // invalidate more computations\n        var func = afterFlushCallbacks.shift();\n        try {\n          func();\n        } catch (e) {\n          _throwOrLog(\"afterFlush\", e);\n        }\n      }\n    }\n    finishedTry = true;\n  } finally {\n    if (! finishedTry) {\n      // we're erroring due to throwFirstError being true.\n      inFlush = false; // needed before calling `Tracker.flush()` again\n      // finish flushing\n      Tracker._runFlush({\n        finishSynchronously: options.finishSynchronously,\n        throwFirstError: false\n      });\n    }\n    willFlush = false;\n    inFlush = false;\n    if (pendingComputations.length || afterFlushCallbacks.length) {\n      // We're yielding because we ran a bunch of computations and we aren't\n      // required to finish synchronously, so we'd like to give the event loop a\n      // chance. We should flush again soon.\n      if (options.finishSynchronously) {\n        throw new Error(\"still have more to do?\");  // shouldn't happen\n      }\n      setTimeout(requireFlush, 10);\n    }\n  }\n};\n\n// http://docs.meteor.com/#tracker_autorun\n//\n// Run f(). Record its dependencies. Rerun it whenever the\n// dependencies change.\n//\n// Returns a new Computation, which is also passed to f.\n//\n// Links the computation to the current computation\n// so that it is stopped if the current computation is invalidated.\n\n/**\n * @callback Tracker.ComputationFunction\n * @param {Tracker.Computation}\n */\n/**\n * @summary Run a function now and rerun it later whenever its dependencies\n * change. Returns a Computation object that can be used to stop or observe the\n * rerunning.\n * @locus Client\n * @param {Tracker.ComputationFunction} runFunc The function to run. It receives\n * one argument: the Computation object that will be returned.\n * @param {Object} [options]\n * @param {Function} options.onError Optional. The function to run when an error\n * happens in the Computation. The only argument it receives is the Error\n * thrown. Defaults to the error being logged to the console.\n * @returns {Tracker.Computation}\n */\nTracker.autorun = function (f, options = {}) {\n  if (typeof f !== 'function')\n    throw new Error('Tracker.autorun requires a function argument');\n\n  constructingComputation = true;\n  var c = new Tracker.Computation(f, Tracker.currentComputation, options.onError);\n\n  if (Tracker.active)\n    Tracker.onInvalidate(function () {\n      c.stop();\n    });\n\n  return c;\n};\n\n// http://docs.meteor.com/#tracker_nonreactive\n//\n// Run `f` with no current computation, returning the return value\n// of `f`.  Used to turn off reactivity for the duration of `f`,\n// so that reactive data sources accessed by `f` will not result in any\n// computations being invalidated.\n\n/**\n * @summary Run a function without tracking dependencies.\n * @locus Client\n * @param {Function} func A function to call immediately.\n */\nTracker.nonreactive = function (f) {\n  return Tracker.withComputation(null, f);\n};\n\n/**\n * @summary Helper function to make the tracker work with promises.\n * @param computation Computation that tracked\n * @param func async function that needs to be called and be reactive\n */\nTracker.withComputation = function (computation, f) {\n  var previousComputation = Tracker.currentComputation;\n\n  Tracker.currentComputation = computation;\n  Tracker.active = !!computation;\n\n  try {\n    return f();\n  } finally {\n    Tracker.currentComputation = previousComputation;\n    Tracker.active = !!previousComputation;\n  }\n};\n\n// http://docs.meteor.com/#tracker_oninvalidate\n\n/**\n * @summary Registers a new [`onInvalidate`](#computation_oninvalidate) callback on the current computation (which must exist), to be called immediately when the current computation is invalidated or stopped.\n * @locus Client\n * @param {Function} callback A callback function that will be invoked as `func(c)`, where `c` is the computation on which the callback is registered.\n */\nTracker.onInvalidate = function (f) {\n  if (! Tracker.active)\n    throw new Error(\"Tracker.onInvalidate requires a currentComputation\");\n\n  Tracker.currentComputation.onInvalidate(f);\n};\n\n// http://docs.meteor.com/#tracker_afterflush\n\n/**\n * @summary Schedules a function to be called during the next flush, or later in the current flush if one is in progress, after all invalidated computations have been rerun.  The function will be run once and not on subsequent flushes unless `afterFlush` is called again.\n * @locus Client\n * @param {Function} callback A function to call at flush time.\n */\nTracker.afterFlush = function (f) {\n  afterFlushCallbacks.push(f);\n  requireFlush();\n};\n"]}