{"version": 3, "sources": ["meteor://💻app/packages/minimongo/minimongo_client.js", "meteor://💻app/packages/minimongo/common.js", "meteor://💻app/packages/minimongo/constants.js", "meteor://💻app/packages/minimongo/cursor.js", "meteor://💻app/packages/minimongo/local_collection.js", "meteor://💻app/packages/minimongo/matcher.js", "meteor://💻app/packages/minimongo/minimongo_common.js", "meteor://💻app/packages/minimongo/observe_handle.js", "meteor://💻app/packages/minimongo/sorter.js"], "names": ["module", "link", "export", "hasOwn", "ELEMENT_OPERATORS", "compileDocumentSelector", "equalityElementM<PERSON>er", "expandArraysInBranches", "isIndexable", "isNumericKey", "isOperatorObject", "makeLookupFunction", "nothingMatcher", "pathsToTree", "populateDocumentWithQueryFields", "projectionDetails", "regexpElementMatcher", "LocalCollection", "default", "v", "Object", "prototype", "hasOwnProperty", "$lt", "makeInequality", "cmpValue", "$gt", "$lte", "$gte", "$mod", "compileElementSelector", "operand", "Array", "isArray", "length", "Error", "divisor", "remainder", "value", "$in", "elementMatchers", "map", "option", "RegExp", "undefined", "some", "matcher", "$size", "dontExpandLeafArrays", "$type", "dontIncludeLeafArrays", "operandAliasMap", "call", "concat", "_f", "_type", "$bitsAllSet", "mask", "getOperandBitmask", "bitmask", "getValueBitmask", "every", "byte", "i", "$bitsAnySet", "$bitsAllClear", "$bitsAnyClear", "$regex", "valueSelector", "regexp", "$options", "test", "source", "$elemMatch", "_isPlainObject", "isDocMatcher", "keys", "filter", "key", "LOGICAL_OPERATORS", "reduce", "a", "b", "assign", "subMatcher", "inElemMatch", "compileValueSelector", "arrayElement", "arg", "dontIterate", "result", "$and", "subSelector", "andDocumentMatchers", "compileArrayOfDocumentSelectors", "$or", "matchers", "doc", "fn", "$nor", "$where", "selector<PERSON><PERSON><PERSON>", "_recordPathUsed", "_hasWhere", "Function", "$comment", "VALUE_OPERATORS", "$eq", "convertElementMatcherToBranchedMatcher", "$not", "invertBranchedMatcher", "$ne", "$nin", "$exists", "exists", "everythingMatcher", "$maxDistance", "$near", "$all", "branchedMatchers", "criterion", "andBranchedMatchers", "isRoot", "_hasGeoQuery", "maxDistance", "point", "distance", "$geometry", "type", "GeoJSON", "pointDistance", "coordinates", "pointToArray", "geometryWithinRadius", "distanceCoordinatePairs", "branchedValues", "branch", "curDistance", "_isUpdate", "arrayIndices", "andSomeMatchers", "subMatchers", "docOrBranches", "match", "subResult", "selectors", "docSelector", "options", "arguments", "docMatchers", "substr", "_isSimple", "lookUpByIndex", "valueMatcher", "Boolean", "operatorBranchedMatcher", "elementMatcher", "branches", "expanded", "element", "matched", "pointA", "pointB", "Math", "hypot", "elementSelector", "_equal", "docOrBranchedValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "branchesOut", "for<PERSON>ach", "thisIsArray", "push", "selector", "Number", "isInteger", "Uint8Array", "Int32Array", "buffer", "EJSON", "isBinary", "x", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max", "view", "isSafeInteger", "Uint32Array", "BYTES_PER_ELEMENT", "insertIntoDocument", "document", "existingKey", "indexOf", "branchedMatcher", "branchV<PERSON>ues", "obj", "s", "inconsistentOK", "theseAreOperators", "<PERSON><PERSON><PERSON><PERSON>", "thisIsOperator", "JSON", "stringify", "cmpValueComparator", "operandType", "_cmp", "parts", "split", "firstPart", "lookupRest", "slice", "join", "buildResult", "firstLevel", "appendToResult", "more", "forSort", "arrayIndex", "MinimongoTest", "MinimongoError", "message", "field", "error", "name", "operatorMatchers", "operator", "simpleRange", "includes", "simpleEquality", "simpleInclusion", "paths", "newLeafFn", "conflictFn", "root", "path", "pathArray", "tree", "success", "last<PERSON>ey", "y", "populateDocumentWithKeyValue", "getPrototypeOf", "populateDocumentWithObject", "unprefixedKeys", "op", "validateObject", "object", "query", "_selectorIsId", "fields", "fieldsKeys", "sort", "_id", "including", "keyP<PERSON>", "rule", "projectionRulesTree", "node", "fullPath", "currentPath", "another<PERSON><PERSON>", "toString", "lastIndex", "validateKeyInPath", "getAsyncMethodName", "ASYNC_COLLECTION_METHODS", "ASYNC_CURSOR_METHODS", "CLIENT_ONLY_METHODS", "method", "replace", "<PERSON><PERSON><PERSON>", "constructor", "collection", "sorter", "Minimongo", "Matcher", "_selectorIsIdPerhapsAsObject", "_selectorId", "has<PERSON>eo<PERSON><PERSON><PERSON>", "Sorter", "skip", "limit", "projection", "_projectionFn", "_compileProjection", "_transform", "wrapTransform", "transform", "Tracker", "reactive", "count", "_depend", "added", "removed", "_getRawObjects", "ordered", "fetch", "Symbol", "iterator", "addedBefore", "changed", "movedBefore", "index", "objects", "next", "done", "asyncIterator", "syncResult", "Promise", "resolve", "callback", "thisArg", "getTransform", "observe", "_observeFromObserveChanges", "observeAsync", "observe<PERSON>hanges", "_observeChangesCallbacksAreOrdered", "_allow_unordered", "distances", "_IdMap", "cursor", "dirty", "projectionFn", "resultsSnapshot", "qid", "next_qid", "queries", "results", "paused", "wrapCallback", "self", "args", "_observeQueue", "queueTask", "apply", "_suppress_initial", "_query$results", "_query$results$size", "handler", "clone", "size", "handle", "ObserveHandle", "stop", "isReady", "isReadyPromise", "active", "onInvalidate", "drainResult", "drain", "then", "observeChangesAsync", "changers", "dependency", "Dependency", "notify", "bind", "depend", "_getCollectionName", "applySkipLimit", "selectedDoc", "_docs", "get", "set", "clear", "Meteor", "_runFresh", "id", "matchResult", "documentMatches", "getComparator", "_publishCursor", "subscription", "Package", "mongo", "Mongo", "Collection", "asyncName", "_len", "_key", "reject", "_objectSpread", "isClient", "_SynchronousQueue", "_AsynchronousQueue", "create", "_savedOriginals", "countDocuments", "find", "countAsync", "estimatedDocumentCount", "findOne", "findOneAsync", "fetchAsync", "prepareInsert", "assertHasValidFieldNames", "_useOID", "MongoID", "ObjectID", "Random", "has", "_saveOriginal", "insert", "queriesToRecompute", "_insertInResultsSync", "_recomputeResults", "defer", "insertAsync", "_insertInResultsAsync", "pauseObservers", "clearResultQueries", "prepareRemove", "remove", "_eachPossiblyMatchingDocSync", "query<PERSON><PERSON>ove", "removeId", "removeDoc", "equals", "_removeFromResultsSync", "removeAsync", "_removeFromResultsAsync", "_resumeObservers", "_diffQuery<PERSON><PERSON>es", "resumeObserversServer", "resumeObserversClient", "retrieveOriginals", "originals", "saveOriginals", "prepareUpdate", "qidToOriginalResults", "docMap", "idsMatched", "_idsMatchedBySelector", "memoizedCloneIfNeeded", "docToMemoize", "finishUpdate", "_ref", "updateCount", "insertedId", "_returnObject", "numberAffected", "updateAsync", "mod", "recomputeQids", "_eachPossiblyMatchingDocAsync", "query<PERSON><PERSON>ult", "_modifyAndNotifyAsync", "multi", "upsert", "_createUpsertDocument", "update", "_modifyAndNotifySync", "upsertAsync", "specificIds", "forEachAsync", "_getMatchedDocAndModify", "matched_before", "old_doc", "_modify", "afterMatch", "after", "before", "_updateInResultsSync", "_updateInResultsAsync", "oldResults", "_CachingChangeObserver", "orderedFromCallbacks", "callbacks", "docs", "OrderedDict", "idStringify", "applyChange", "putBefore", "moveBefore", "DiffSequence", "applyChanges", "IdMap", "idParse", "__wrappedTransform__", "wrapped", "transformed", "nonreactive", "_binarySearch", "cmp", "array", "first", "range", "<PERSON><PERSON><PERSON><PERSON>", "floor", "_checkSupportedProjection", "_idProjection", "details", "ruleTree", "subdoc", "modifier", "selectorDocument", "isModify", "_isModificationMod", "newDoc", "$set", "isInsert", "replacement", "_diffObjects", "left", "right", "diffObjects", "newResults", "observer", "diff<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_diffQuery<PERSON>rderedChanges", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_diffQueryUnorderedChanges", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_findInOrderedResults", "subIds", "_insertInSortedList", "splice", "isReplace", "isModifier", "setOnInsert", "modFunc", "MODIFIERS", "keypath", "keyparts", "target", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "forbidArray", "noCreate", "NO_CREATE_MODIFIERS", "pop", "observeCallbacks", "suppressed", "observeChangesCallbacks", "_observeCallbacksAreOrdered", "indices", "_no_indices", "check", "addedAt", "changedAt", "oldDoc", "movedTo", "from", "to", "removedAt", "changeObserver", "_fromObserve", "nonMutatingCallbacks", "setSuppressed", "h", "_h$isReadyPromise", "_isPromise", "changed<PERSON>ields", "make<PERSON><PERSON>edFields", "old_idx", "new_idx", "$currentDate", "Date", "$inc", "$min", "$max", "$mul", "$rename", "target2", "setPropertyError", "$setOnInsert", "$unset", "$push", "$each", "to<PERSON>ush", "position", "$position", "$slice", "sortFunction", "$sort", "spliceArguments", "$pushAll", "$addToSet", "isEach", "values", "toAdd", "$pop", "toPop", "$pull", "to<PERSON><PERSON>", "out", "$pullAll", "$bit", "$v", "invalidCharMsg", "$", "assertIsValidFieldName", "usedArrayIndex", "last", "keypart", "parseInt", "Decimal", "_Package$mongoDecima", "DecimalStub", "isUpdate", "_paths", "_matchingDocument", "_selector", "_doc<PERSON><PERSON>er", "_compileSelector", "hasWhere", "isSimple", "_getPaths", "keyOrderSensitive", "_typeorder", "t", "ta", "tb", "oa", "ob", "toHexString", "isNaN", "getTime", "minus", "toNumber", "toArray", "LocalCollection_", "spec", "_sortSpecParts", "_sortFunction", "addSpecPart", "ascending", "char<PERSON>t", "lookup", "affectedByModifier", "_selectorForAffectedByModifier", "_keyComparator", "composeComparators", "_keyFieldComparator", "_getBaseComparator", "_compareKeys", "key1", "key2", "_generateKeysFromDoc", "cb", "pathFromIndices", "knownPaths", "valuesByIndexAndPath", "usedPaths", "<PERSON><PERSON><PERSON>", "doc1", "doc2", "_getMinKeyFromDoc", "<PERSON><PERSON><PERSON>", "part", "invert", "compare", "comparator<PERSON><PERSON>y"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC,C;;;;;;;;;;;ACApCD,MAAM,CAACE,MAAM,CAAC;EAACC,MAAM,EAACA,CAAA,KAAIA,MAAM;EAACC,iBAAiB,EAACA,CAAA,KAAIA,iBAAiB;EAACC,uBAAuB,EAACA,CAAA,KAAIA,uBAAuB;EAACC,sBAAsB,EAACA,CAAA,KAAIA,sBAAsB;EAACC,sBAAsB,EAACA,CAAA,KAAIA,sBAAsB;EAACC,WAAW,EAACA,CAAA,KAAIA,WAAW;EAACC,YAAY,EAACA,CAAA,KAAIA,YAAY;EAACC,gBAAgB,EAACA,CAAA,KAAIA,gBAAgB;EAACC,kBAAkB,EAACA,CAAA,KAAIA,kBAAkB;EAACC,cAAc,EAACA,CAAA,KAAIA,cAAc;EAACC,WAAW,EAACA,CAAA,KAAIA,WAAW;EAACC,+BAA+B,EAACA,CAAA,KAAIA,+BAA+B;EAACC,iBAAiB,EAACA,CAAA,KAAIA,iBAAiB;EAACC,oBAAoB,EAACA,CAAA,KAAIA;AAAoB,CAAC,CAAC;AAAC,IAAIC,eAAe;AAACjB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAACF,eAAe,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAExpB,MAAMhB,MAAM,GAAGiB,MAAM,CAACC,SAAS,CAACC,cAAc;AAc9C,MAAMlB,iBAAiB,GAAG;EAC/BmB,GAAG,EAAEC,cAAc,CAACC,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC;EAC7CC,GAAG,EAAEF,cAAc,CAACC,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC;EAC7CE,IAAI,EAAEH,cAAc,CAACC,QAAQ,IAAIA,QAAQ,IAAI,CAAC,CAAC;EAC/CG,IAAI,EAAEJ,cAAc,CAACC,QAAQ,IAAIA,QAAQ,IAAI,CAAC,CAAC;EAC/CI,IAAI,EAAE;IACJC,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IAAIA,OAAO,CAACG,MAAM,KAAK,CAAC,IAC3C,OAAOH,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC9B,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,EAAE;QACxC,MAAMI,KAAK,CAAC,kDAAkD,CAAC;MACjE;;MAEA;MACA,MAAMC,OAAO,GAAGL,OAAO,CAAC,CAAC,CAAC;MAC1B,MAAMM,SAAS,GAAGN,OAAO,CAAC,CAAC,CAAC;MAC5B,OAAOO,KAAK,IACV,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAGF,OAAO,KAAKC,SAClD;IACH;EACF,CAAC;EACDE,GAAG,EAAE;IACHT,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;QAC3B,MAAMI,KAAK,CAAC,oBAAoB,CAAC;MACnC;MAEA,MAAMK,eAAe,GAAGT,OAAO,CAACU,GAAG,CAACC,MAAM,IAAI;QAC5C,IAAIA,MAAM,YAAYC,MAAM,EAAE;UAC5B,OAAO3B,oBAAoB,CAAC0B,MAAM,CAAC;QACrC;QAEA,IAAIhC,gBAAgB,CAACgC,MAAM,CAAC,EAAE;UAC5B,MAAMP,KAAK,CAAC,yBAAyB,CAAC;QACxC;QAEA,OAAO7B,sBAAsB,CAACoC,MAAM,CAAC;MACvC,CAAC,CAAC;MAEF,OAAOJ,KAAK,IAAI;QACd;QACA,IAAIA,KAAK,KAAKM,SAAS,EAAE;UACvBN,KAAK,GAAG,IAAI;QACd;QAEA,OAAOE,eAAe,CAACK,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACR,KAAK,CAAC,CAAC;MACxD,CAAC;IACH;EACF,CAAC;EACDS,KAAK,EAAE;IACL;IACA;IACA;IACAC,oBAAoB,EAAE,IAAI;IAC1BlB,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACA;QACAA,OAAO,GAAG,CAAC;MACb,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACtC,MAAMI,KAAK,CAAC,sBAAsB,CAAC;MACrC;MAEA,OAAOG,KAAK,IAAIN,KAAK,CAACC,OAAO,CAACK,KAAK,CAAC,IAAIA,KAAK,CAACJ,MAAM,KAAKH,OAAO;IAClE;EACF,CAAC;EACDkB,KAAK,EAAE;IACL;IACA;IACA;IACA;IACAC,qBAAqB,EAAE,IAAI;IAC3BpB,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B,MAAMoB,eAAe,GAAG;UACtB,QAAQ,EAAE,CAAC;UACX,QAAQ,EAAE,CAAC;UACX,QAAQ,EAAE,CAAC;UACX,OAAO,EAAE,CAAC;UACV,SAAS,EAAE,CAAC;UACZ,WAAW,EAAE,CAAC;UACd,UAAU,EAAE,CAAC;UACb,MAAM,EAAE,CAAC;UACT,MAAM,EAAE,CAAC;UACT,MAAM,EAAE,EAAE;UACV,OAAO,EAAE,EAAE;UACX,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,QAAQ,EAAE,EAAE;UACZ,qBAAqB,EAAE,EAAE;UACzB,KAAK,EAAE,EAAE;UACT,WAAW,EAAE,EAAE;UACf,MAAM,EAAE,EAAE;UACV,SAAS,EAAE,EAAE;UACb,QAAQ,EAAE,CAAC,CAAC;UACZ,QAAQ,EAAE;QACZ,CAAC;QACD,IAAI,CAAChD,MAAM,CAACiD,IAAI,CAACD,eAAe,EAAEpB,OAAO,CAAC,EAAE;UAC1C,MAAMI,KAAK,oCAAAkB,MAAA,CAAoCtB,OAAO,CAAE,CAAC;QAC3D;QACAA,OAAO,GAAGoB,eAAe,CAACpB,OAAO,CAAC;MACpC,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACtC,IAAIA,OAAO,KAAK,CAAC,IAAIA,OAAO,GAAG,CAAC,CAAC,IAC3BA,OAAO,GAAG,EAAE,IAAIA,OAAO,KAAK,GAAI,EAAE;UACtC,MAAMI,KAAK,kCAAAkB,MAAA,CAAkCtB,OAAO,CAAE,CAAC;QACzD;MACF,CAAC,MAAM;QACL,MAAMI,KAAK,CAAC,+CAA+C,CAAC;MAC9D;MAEA,OAAOG,KAAK,IACVA,KAAK,KAAKM,SAAS,IAAI3B,eAAe,CAACqC,EAAE,CAACC,KAAK,CAACjB,KAAK,CAAC,KAAKP,OAC5D;IACH;EACF,CAAC;EACDyB,WAAW,EAAE;IACX1B,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,MAAM0B,IAAI,GAAGC,iBAAiB,CAAC3B,OAAO,EAAE,aAAa,CAAC;MACtD,OAAOO,KAAK,IAAI;QACd,MAAMqB,OAAO,GAAGC,eAAe,CAACtB,KAAK,EAAEmB,IAAI,CAACvB,MAAM,CAAC;QACnD,OAAOyB,OAAO,IAAIF,IAAI,CAACI,KAAK,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK,CAACJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,MAAMA,IAAI,CAAC;MACzE,CAAC;IACH;EACF,CAAC;EACDE,WAAW,EAAE;IACXlC,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,MAAM0B,IAAI,GAAGC,iBAAiB,CAAC3B,OAAO,EAAE,aAAa,CAAC;MACtD,OAAOO,KAAK,IAAI;QACd,MAAMqB,OAAO,GAAGC,eAAe,CAACtB,KAAK,EAAEmB,IAAI,CAACvB,MAAM,CAAC;QACnD,OAAOyB,OAAO,IAAIF,IAAI,CAACZ,IAAI,CAAC,CAACiB,IAAI,EAAEC,CAAC,KAAK,CAAC,CAACJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,MAAMA,IAAI,CAAC;MACzE,CAAC;IACH;EACF,CAAC;EACDG,aAAa,EAAE;IACbnC,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,MAAM0B,IAAI,GAAGC,iBAAiB,CAAC3B,OAAO,EAAE,eAAe,CAAC;MACxD,OAAOO,KAAK,IAAI;QACd,MAAMqB,OAAO,GAAGC,eAAe,CAACtB,KAAK,EAAEmB,IAAI,CAACvB,MAAM,CAAC;QACnD,OAAOyB,OAAO,IAAIF,IAAI,CAACI,KAAK,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK,EAAEJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC;MACjE,CAAC;IACH;EACF,CAAC;EACDI,aAAa,EAAE;IACbpC,sBAAsBA,CAACC,OAAO,EAAE;MAC9B,MAAM0B,IAAI,GAAGC,iBAAiB,CAAC3B,OAAO,EAAE,eAAe,CAAC;MACxD,OAAOO,KAAK,IAAI;QACd,MAAMqB,OAAO,GAAGC,eAAe,CAACtB,KAAK,EAAEmB,IAAI,CAACvB,MAAM,CAAC;QACnD,OAAOyB,OAAO,IAAIF,IAAI,CAACZ,IAAI,CAAC,CAACiB,IAAI,EAAEC,CAAC,KAAK,CAACJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,MAAMA,IAAI,CAAC;MACxE,CAAC;IACH;EACF,CAAC;EACDK,MAAM,EAAE;IACNrC,sBAAsBA,CAACC,OAAO,EAAEqC,aAAa,EAAE;MAC7C,IAAI,EAAE,OAAOrC,OAAO,KAAK,QAAQ,IAAIA,OAAO,YAAYY,MAAM,CAAC,EAAE;QAC/D,MAAMR,KAAK,CAAC,qCAAqC,CAAC;MACpD;MAEA,IAAIkC,MAAM;MACV,IAAID,aAAa,CAACE,QAAQ,KAAK1B,SAAS,EAAE;QACxC;QACA;;QAEA;QACA;QACA;QACA,IAAI,QAAQ,CAAC2B,IAAI,CAACH,aAAa,CAACE,QAAQ,CAAC,EAAE;UACzC,MAAM,IAAInC,KAAK,CAAC,mDAAmD,CAAC;QACtE;QAEA,MAAMqC,MAAM,GAAGzC,OAAO,YAAYY,MAAM,GAAGZ,OAAO,CAACyC,MAAM,GAAGzC,OAAO;QACnEsC,MAAM,GAAG,IAAI1B,MAAM,CAAC6B,MAAM,EAAEJ,aAAa,CAACE,QAAQ,CAAC;MACrD,CAAC,MAAM,IAAIvC,OAAO,YAAYY,MAAM,EAAE;QACpC0B,MAAM,GAAGtC,OAAO;MAClB,CAAC,MAAM;QACLsC,MAAM,GAAG,IAAI1B,MAAM,CAACZ,OAAO,CAAC;MAC9B;MAEA,OAAOf,oBAAoB,CAACqD,MAAM,CAAC;IACrC;EACF,CAAC;EACDI,UAAU,EAAE;IACVzB,oBAAoB,EAAE,IAAI;IAC1BlB,sBAAsBA,CAACC,OAAO,EAAEqC,aAAa,EAAEtB,OAAO,EAAE;MACtD,IAAI,CAAC7B,eAAe,CAACyD,cAAc,CAAC3C,OAAO,CAAC,EAAE;QAC5C,MAAMI,KAAK,CAAC,2BAA2B,CAAC;MAC1C;MAEA,MAAMwC,YAAY,GAAG,CAACjE,gBAAgB,CACpCU,MAAM,CAACwD,IAAI,CAAC7C,OAAO,CAAC,CACjB8C,MAAM,CAACC,GAAG,IAAI,CAAC3E,MAAM,CAACiD,IAAI,CAAC2B,iBAAiB,EAAED,GAAG,CAAC,CAAC,CACnDE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK9D,MAAM,CAAC+D,MAAM,CAACF,CAAC,EAAE;QAAC,CAACC,CAAC,GAAGnD,OAAO,CAACmD,CAAC;MAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAC5D,IAAI,CAAC;MAEP,IAAIE,UAAU;MACd,IAAIT,YAAY,EAAE;QAChB;QACA;QACA;QACA;QACAS,UAAU,GACR/E,uBAAuB,CAAC0B,OAAO,EAAEe,OAAO,EAAE;UAACuC,WAAW,EAAE;QAAI,CAAC,CAAC;MAClE,CAAC,MAAM;QACLD,UAAU,GAAGE,oBAAoB,CAACvD,OAAO,EAAEe,OAAO,CAAC;MACrD;MAEA,OAAOR,KAAK,IAAI;QACd,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,KAAK,CAAC,EAAE;UACzB,OAAO,KAAK;QACd;QAEA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,KAAK,CAACJ,MAAM,EAAE,EAAE6B,CAAC,EAAE;UACrC,MAAMwB,YAAY,GAAGjD,KAAK,CAACyB,CAAC,CAAC;UAC7B,IAAIyB,GAAG;UACP,IAAIb,YAAY,EAAE;YAChB;YACA;YACA;YACA,IAAI,CAACnE,WAAW,CAAC+E,YAAY,CAAC,EAAE;cAC9B,OAAO,KAAK;YACd;YAEAC,GAAG,GAAGD,YAAY;UACpB,CAAC,MAAM;YACL;YACA;YACAC,GAAG,GAAG,CAAC;cAAClD,KAAK,EAAEiD,YAAY;cAAEE,WAAW,EAAE;YAAI,CAAC,CAAC;UAClD;UACA;UACA,IAAIL,UAAU,CAACI,GAAG,CAAC,CAACE,MAAM,EAAE;YAC1B,OAAO3B,CAAC,CAAC,CAAC;UACZ;QACF;QAEA,OAAO,KAAK;MACd,CAAC;IACH;EACF;AACF,CAAC;AAED;AACA,MAAMgB,iBAAiB,GAAG;EACxBY,IAAIA,CAACC,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,EAAE;IACtC,OAAOQ,mBAAmB,CACxBC,+BAA+B,CAACF,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,CACnE,CAAC;EACH,CAAC;EAEDU,GAAGA,CAACH,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,EAAE;IACrC,MAAMW,QAAQ,GAAGF,+BAA+B,CAC9CF,WAAW,EACX9C,OAAO,EACPuC,WACF,CAAC;;IAED;IACA;IACA,IAAIW,QAAQ,CAAC9D,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO8D,QAAQ,CAAC,CAAC,CAAC;IACpB;IAEA,OAAOC,GAAG,IAAI;MACZ,MAAMP,MAAM,GAAGM,QAAQ,CAACnD,IAAI,CAACqD,EAAE,IAAIA,EAAE,CAACD,GAAG,CAAC,CAACP,MAAM,CAAC;MAClD;MACA;MACA,OAAO;QAACA;MAAM,CAAC;IACjB,CAAC;EACH,CAAC;EAEDS,IAAIA,CAACP,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,EAAE;IACtC,MAAMW,QAAQ,GAAGF,+BAA+B,CAC9CF,WAAW,EACX9C,OAAO,EACPuC,WACF,CAAC;IACD,OAAOY,GAAG,IAAI;MACZ,MAAMP,MAAM,GAAGM,QAAQ,CAACnC,KAAK,CAACqC,EAAE,IAAI,CAACA,EAAE,CAACD,GAAG,CAAC,CAACP,MAAM,CAAC;MACpD;MACA;MACA,OAAO;QAACA;MAAM,CAAC;IACjB,CAAC;EACH,CAAC;EAEDU,MAAMA,CAACC,aAAa,EAAEvD,OAAO,EAAE;IAC7B;IACAA,OAAO,CAACwD,eAAe,CAAC,EAAE,CAAC;IAC3BxD,OAAO,CAACyD,SAAS,GAAG,IAAI;IAExB,IAAI,EAAEF,aAAa,YAAYG,QAAQ,CAAC,EAAE;MACxC;MACA;MACAH,aAAa,GAAGG,QAAQ,CAAC,KAAK,YAAAnD,MAAA,CAAYgD,aAAa,CAAE,CAAC;IAC5D;;IAEA;IACA;IACA,OAAOJ,GAAG,KAAK;MAACP,MAAM,EAAEW,aAAa,CAACjD,IAAI,CAAC6C,GAAG,EAAEA,GAAG;IAAC,CAAC,CAAC;EACxD,CAAC;EAED;EACA;EACAQ,QAAQA,CAAA,EAAG;IACT,OAAO,OAAO;MAACf,MAAM,EAAE;IAAI,CAAC,CAAC;EAC/B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMgB,eAAe,GAAG;EACtBC,GAAGA,CAAC5E,OAAO,EAAE;IACX,OAAO6E,sCAAsC,CAC3CtG,sBAAsB,CAACyB,OAAO,CAChC,CAAC;EACH,CAAC;EACD8E,IAAIA,CAAC9E,OAAO,EAAEqC,aAAa,EAAEtB,OAAO,EAAE;IACpC,OAAOgE,qBAAqB,CAACxB,oBAAoB,CAACvD,OAAO,EAAEe,OAAO,CAAC,CAAC;EACtE,CAAC;EACDiE,GAAGA,CAAChF,OAAO,EAAE;IACX,OAAO+E,qBAAqB,CAC1BF,sCAAsC,CAACtG,sBAAsB,CAACyB,OAAO,CAAC,CACxE,CAAC;EACH,CAAC;EACDiF,IAAIA,CAACjF,OAAO,EAAE;IACZ,OAAO+E,qBAAqB,CAC1BF,sCAAsC,CACpCxG,iBAAiB,CAACmC,GAAG,CAACT,sBAAsB,CAACC,OAAO,CACtD,CACF,CAAC;EACH,CAAC;EACDkF,OAAOA,CAAClF,OAAO,EAAE;IACf,MAAMmF,MAAM,GAAGN,sCAAsC,CACnDtE,KAAK,IAAIA,KAAK,KAAKM,SACrB,CAAC;IACD,OAAOb,OAAO,GAAGmF,MAAM,GAAGJ,qBAAqB,CAACI,MAAM,CAAC;EACzD,CAAC;EACD;EACA5C,QAAQA,CAACvC,OAAO,EAAEqC,aAAa,EAAE;IAC/B,IAAI,CAACjE,MAAM,CAACiD,IAAI,CAACgB,aAAa,EAAE,QAAQ,CAAC,EAAE;MACzC,MAAMjC,KAAK,CAAC,yBAAyB,CAAC;IACxC;IAEA,OAAOgF,iBAAiB;EAC1B,CAAC;EACD;EACAC,YAAYA,CAACrF,OAAO,EAAEqC,aAAa,EAAE;IACnC,IAAI,CAACA,aAAa,CAACiD,KAAK,EAAE;MACxB,MAAMlF,KAAK,CAAC,4BAA4B,CAAC;IAC3C;IAEA,OAAOgF,iBAAiB;EAC1B,CAAC;EACDG,IAAIA,CAACvF,OAAO,EAAEqC,aAAa,EAAEtB,OAAO,EAAE;IACpC,IAAI,CAACd,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;MAC3B,MAAMI,KAAK,CAAC,qBAAqB,CAAC;IACpC;;IAEA;IACA,IAAIJ,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOtB,cAAc;IACvB;IAEA,MAAM2G,gBAAgB,GAAGxF,OAAO,CAACU,GAAG,CAAC+E,SAAS,IAAI;MAChD;MACA,IAAI9G,gBAAgB,CAAC8G,SAAS,CAAC,EAAE;QAC/B,MAAMrF,KAAK,CAAC,0BAA0B,CAAC;MACzC;;MAEA;MACA,OAAOmD,oBAAoB,CAACkC,SAAS,EAAE1E,OAAO,CAAC;IACjD,CAAC,CAAC;;IAEF;IACA;IACA,OAAO2E,mBAAmB,CAACF,gBAAgB,CAAC;EAC9C,CAAC;EACDF,KAAKA,CAACtF,OAAO,EAAEqC,aAAa,EAAEtB,OAAO,EAAE4E,MAAM,EAAE;IAC7C,IAAI,CAACA,MAAM,EAAE;MACX,MAAMvF,KAAK,CAAC,2CAA2C,CAAC;IAC1D;IAEAW,OAAO,CAAC6E,YAAY,GAAG,IAAI;;IAE3B;IACA;IACA;IACA;IACA,IAAIC,WAAW,EAAEC,KAAK,EAAEC,QAAQ;IAChC,IAAI7G,eAAe,CAACyD,cAAc,CAAC3C,OAAO,CAAC,IAAI5B,MAAM,CAACiD,IAAI,CAACrB,OAAO,EAAE,WAAW,CAAC,EAAE;MAChF;MACA6F,WAAW,GAAG7F,OAAO,CAACqF,YAAY;MAClCS,KAAK,GAAG9F,OAAO,CAACgG,SAAS;MACzBD,QAAQ,GAAGxF,KAAK,IAAI;QAClB;QACA;QACA;QACA,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,IAAI;QACb;QAEA,IAAI,CAACA,KAAK,CAAC0F,IAAI,EAAE;UACf,OAAOC,OAAO,CAACC,aAAa,CAC1BL,KAAK,EACL;YAACG,IAAI,EAAE,OAAO;YAAEG,WAAW,EAAEC,YAAY,CAAC9F,KAAK;UAAC,CAClD,CAAC;QACH;QAEA,IAAIA,KAAK,CAAC0F,IAAI,KAAK,OAAO,EAAE;UAC1B,OAAOC,OAAO,CAACC,aAAa,CAACL,KAAK,EAAEvF,KAAK,CAAC;QAC5C;QAEA,OAAO2F,OAAO,CAACI,oBAAoB,CAAC/F,KAAK,EAAEuF,KAAK,EAAED,WAAW,CAAC,GAC1D,CAAC,GACDA,WAAW,GAAG,CAAC;MACrB,CAAC;IACH,CAAC,MAAM;MACLA,WAAW,GAAGxD,aAAa,CAACgD,YAAY;MAExC,IAAI,CAAC5G,WAAW,CAACuB,OAAO,CAAC,EAAE;QACzB,MAAMI,KAAK,CAAC,mDAAmD,CAAC;MAClE;MAEA0F,KAAK,GAAGO,YAAY,CAACrG,OAAO,CAAC;MAE7B+F,QAAQ,GAAGxF,KAAK,IAAI;QAClB,IAAI,CAAC9B,WAAW,CAAC8B,KAAK,CAAC,EAAE;UACvB,OAAO,IAAI;QACb;QAEA,OAAOgG,uBAAuB,CAACT,KAAK,EAAEvF,KAAK,CAAC;MAC9C,CAAC;IACH;IAEA,OAAOiG,cAAc,IAAI;MACvB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM7C,MAAM,GAAG;QAACA,MAAM,EAAE;MAAK,CAAC;MAC9BnF,sBAAsB,CAACgI,cAAc,CAAC,CAAC1E,KAAK,CAAC2E,MAAM,IAAI;QACrD;QACA;QACA,IAAIC,WAAW;QACf,IAAI,CAAC3F,OAAO,CAAC4F,SAAS,EAAE;UACtB,IAAI,EAAE,OAAOF,MAAM,CAAClG,KAAK,KAAK,QAAQ,CAAC,EAAE;YACvC,OAAO,IAAI;UACb;UAEAmG,WAAW,GAAGX,QAAQ,CAACU,MAAM,CAAClG,KAAK,CAAC;;UAEpC;UACA,IAAImG,WAAW,KAAK,IAAI,IAAIA,WAAW,GAAGb,WAAW,EAAE;YACrD,OAAO,IAAI;UACb;;UAEA;UACA,IAAIlC,MAAM,CAACoC,QAAQ,KAAKlF,SAAS,IAAI8C,MAAM,CAACoC,QAAQ,IAAIW,WAAW,EAAE;YACnE,OAAO,IAAI;UACb;QACF;QAEA/C,MAAM,CAACA,MAAM,GAAG,IAAI;QACpBA,MAAM,CAACoC,QAAQ,GAAGW,WAAW;QAE7B,IAAID,MAAM,CAACG,YAAY,EAAE;UACvBjD,MAAM,CAACiD,YAAY,GAAGH,MAAM,CAACG,YAAY;QAC3C,CAAC,MAAM;UACL,OAAOjD,MAAM,CAACiD,YAAY;QAC5B;QAEA,OAAO,CAAC7F,OAAO,CAAC4F,SAAS;MAC3B,CAAC,CAAC;MAEF,OAAOhD,MAAM;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASkD,eAAeA,CAACC,WAAW,EAAE;EACpC,IAAIA,WAAW,CAAC3G,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAOiF,iBAAiB;EAC1B;EAEA,IAAI0B,WAAW,CAAC3G,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAO2G,WAAW,CAAC,CAAC,CAAC;EACvB;EAEA,OAAOC,aAAa,IAAI;IACtB,MAAMC,KAAK,GAAG,CAAC,CAAC;IAChBA,KAAK,CAACrD,MAAM,GAAGmD,WAAW,CAAChF,KAAK,CAACqC,EAAE,IAAI;MACrC,MAAM8C,SAAS,GAAG9C,EAAE,CAAC4C,aAAa,CAAC;;MAEnC;MACA;MACA;MACA;MACA,IAAIE,SAAS,CAACtD,MAAM,IAChBsD,SAAS,CAAClB,QAAQ,KAAKlF,SAAS,IAChCmG,KAAK,CAACjB,QAAQ,KAAKlF,SAAS,EAAE;QAChCmG,KAAK,CAACjB,QAAQ,GAAGkB,SAAS,CAAClB,QAAQ;MACrC;;MAEA;MACA;MACA;MACA,IAAIkB,SAAS,CAACtD,MAAM,IAAIsD,SAAS,CAACL,YAAY,EAAE;QAC9CI,KAAK,CAACJ,YAAY,GAAGK,SAAS,CAACL,YAAY;MAC7C;MAEA,OAAOK,SAAS,CAACtD,MAAM;IACzB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACqD,KAAK,CAACrD,MAAM,EAAE;MACjB,OAAOqD,KAAK,CAACjB,QAAQ;MACrB,OAAOiB,KAAK,CAACJ,YAAY;IAC3B;IAEA,OAAOI,KAAK;EACd,CAAC;AACH;AAEA,MAAMlD,mBAAmB,GAAG+C,eAAe;AAC3C,MAAMnB,mBAAmB,GAAGmB,eAAe;AAE3C,SAAS9C,+BAA+BA,CAACmD,SAAS,EAAEnG,OAAO,EAAEuC,WAAW,EAAE;EACxE,IAAI,CAACrD,KAAK,CAACC,OAAO,CAACgH,SAAS,CAAC,IAAIA,SAAS,CAAC/G,MAAM,KAAK,CAAC,EAAE;IACvD,MAAMC,KAAK,CAAC,sCAAsC,CAAC;EACrD;EAEA,OAAO8G,SAAS,CAACxG,GAAG,CAACmD,WAAW,IAAI;IAClC,IAAI,CAAC3E,eAAe,CAACyD,cAAc,CAACkB,WAAW,CAAC,EAAE;MAChD,MAAMzD,KAAK,CAAC,+CAA+C,CAAC;IAC9D;IAEA,OAAO9B,uBAAuB,CAACuF,WAAW,EAAE9C,OAAO,EAAE;MAACuC;IAAW,CAAC,CAAC;EACrE,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAShF,uBAAuBA,CAAC6I,WAAW,EAAEpG,OAAO,EAAgB;EAAA,IAAdqG,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACxE,MAAMC,WAAW,GAAGjI,MAAM,CAACwD,IAAI,CAACsE,WAAW,CAAC,CAACzG,GAAG,CAACqC,GAAG,IAAI;IACtD,MAAMc,WAAW,GAAGsD,WAAW,CAACpE,GAAG,CAAC;IAEpC,IAAIA,GAAG,CAACwE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5B;MACA;MACA,IAAI,CAACnJ,MAAM,CAACiD,IAAI,CAAC2B,iBAAiB,EAAED,GAAG,CAAC,EAAE;QACxC,MAAM,IAAI3C,KAAK,mCAAAkB,MAAA,CAAmCyB,GAAG,CAAE,CAAC;MAC1D;MAEAhC,OAAO,CAACyG,SAAS,GAAG,KAAK;MACzB,OAAOxE,iBAAiB,CAACD,GAAG,CAAC,CAACc,WAAW,EAAE9C,OAAO,EAAEqG,OAAO,CAAC9D,WAAW,CAAC;IAC1E;;IAEA;IACA;IACA;IACA,IAAI,CAAC8D,OAAO,CAAC9D,WAAW,EAAE;MACxBvC,OAAO,CAACwD,eAAe,CAACxB,GAAG,CAAC;IAC9B;;IAEA;IACA;IACA;IACA,IAAI,OAAOc,WAAW,KAAK,UAAU,EAAE;MACrC,OAAOhD,SAAS;IAClB;IAEA,MAAM4G,aAAa,GAAG7I,kBAAkB,CAACmE,GAAG,CAAC;IAC7C,MAAM2E,YAAY,GAAGnE,oBAAoB,CACvCM,WAAW,EACX9C,OAAO,EACPqG,OAAO,CAACzB,MACV,CAAC;IAED,OAAOzB,GAAG,IAAIwD,YAAY,CAACD,aAAa,CAACvD,GAAG,CAAC,CAAC;EAChD,CAAC,CAAC,CAACpB,MAAM,CAAC6E,OAAO,CAAC;EAElB,OAAO7D,mBAAmB,CAACwD,WAAW,CAAC;AACzC;AAEA;AACA;AACA;AACA;AACA,SAAS/D,oBAAoBA,CAAClB,aAAa,EAAEtB,OAAO,EAAE4E,MAAM,EAAE;EAC5D,IAAItD,aAAa,YAAYzB,MAAM,EAAE;IACnCG,OAAO,CAACyG,SAAS,GAAG,KAAK;IACzB,OAAO3C,sCAAsC,CAC3C5F,oBAAoB,CAACoD,aAAa,CACpC,CAAC;EACH;EAEA,IAAI1D,gBAAgB,CAAC0D,aAAa,CAAC,EAAE;IACnC,OAAOuF,uBAAuB,CAACvF,aAAa,EAAEtB,OAAO,EAAE4E,MAAM,CAAC;EAChE;EAEA,OAAOd,sCAAsC,CAC3CtG,sBAAsB,CAAC8D,aAAa,CACtC,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASwC,sCAAsCA,CAACgD,cAAc,EAAgB;EAAA,IAAdT,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAC1E,OAAOS,QAAQ,IAAI;IACjB,MAAMC,QAAQ,GAAGX,OAAO,CAACnG,oBAAoB,GACzC6G,QAAQ,GACRtJ,sBAAsB,CAACsJ,QAAQ,EAAEV,OAAO,CAACjG,qBAAqB,CAAC;IAEnE,MAAM6F,KAAK,GAAG,CAAC,CAAC;IAChBA,KAAK,CAACrD,MAAM,GAAGoE,QAAQ,CAACjH,IAAI,CAACkH,OAAO,IAAI;MACtC,IAAIC,OAAO,GAAGJ,cAAc,CAACG,OAAO,CAACzH,KAAK,CAAC;;MAE3C;MACA;MACA,IAAI,OAAO0H,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACA;QACA;QACA,IAAI,CAACD,OAAO,CAACpB,YAAY,EAAE;UACzBoB,OAAO,CAACpB,YAAY,GAAG,CAACqB,OAAO,CAAC;QAClC;QAEAA,OAAO,GAAG,IAAI;MAChB;;MAEA;MACA;MACA,IAAIA,OAAO,IAAID,OAAO,CAACpB,YAAY,EAAE;QACnCI,KAAK,CAACJ,YAAY,GAAGoB,OAAO,CAACpB,YAAY;MAC3C;MAEA,OAAOqB,OAAO;IAChB,CAAC,CAAC;IAEF,OAAOjB,KAAK;EACd,CAAC;AACH;;AAEA;AACA,SAAST,uBAAuBA,CAACrD,CAAC,EAAEC,CAAC,EAAE;EACrC,MAAM+E,MAAM,GAAG7B,YAAY,CAACnD,CAAC,CAAC;EAC9B,MAAMiF,MAAM,GAAG9B,YAAY,CAAClD,CAAC,CAAC;EAE9B,OAAOiF,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAED,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjE;;AAEA;AACA;AACO,SAAS5J,sBAAsBA,CAAC+J,eAAe,EAAE;EACtD,IAAI3J,gBAAgB,CAAC2J,eAAe,CAAC,EAAE;IACrC,MAAMlI,KAAK,CAAC,yDAAyD,CAAC;EACxE;;EAEA;EACA;EACA;EACA;EACA,IAAIkI,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAO/H,KAAK,IAAIA,KAAK,IAAI,IAAI;EAC/B;EAEA,OAAOA,KAAK,IAAIrB,eAAe,CAACqC,EAAE,CAACgH,MAAM,CAACD,eAAe,EAAE/H,KAAK,CAAC;AACnE;AAEA,SAAS6E,iBAAiBA,CAACoD,mBAAmB,EAAE;EAC9C,OAAO;IAAC7E,MAAM,EAAE;EAAI,CAAC;AACvB;AAEO,SAASnF,sBAAsBA,CAACsJ,QAAQ,EAAEW,aAAa,EAAE;EAC9D,MAAMC,WAAW,GAAG,EAAE;EAEtBZ,QAAQ,CAACa,OAAO,CAAClC,MAAM,IAAI;IACzB,MAAMmC,WAAW,GAAG3I,KAAK,CAACC,OAAO,CAACuG,MAAM,CAAClG,KAAK,CAAC;;IAE/C;IACA;IACA;IACA;IACA,IAAI,EAAEkI,aAAa,IAAIG,WAAW,IAAI,CAACnC,MAAM,CAAC/C,WAAW,CAAC,EAAE;MAC1DgF,WAAW,CAACG,IAAI,CAAC;QAACjC,YAAY,EAAEH,MAAM,CAACG,YAAY;QAAErG,KAAK,EAAEkG,MAAM,CAAClG;MAAK,CAAC,CAAC;IAC5E;IAEA,IAAIqI,WAAW,IAAI,CAACnC,MAAM,CAAC/C,WAAW,EAAE;MACtC+C,MAAM,CAAClG,KAAK,CAACoI,OAAO,CAAC,CAACpI,KAAK,EAAEyB,CAAC,KAAK;QACjC0G,WAAW,CAACG,IAAI,CAAC;UACfjC,YAAY,EAAE,CAACH,MAAM,CAACG,YAAY,IAAI,EAAE,EAAEtF,MAAM,CAACU,CAAC,CAAC;UACnDzB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,OAAOmI,WAAW;AACpB;AAEA;AACA,SAAS/G,iBAAiBA,CAAC3B,OAAO,EAAE8I,QAAQ,EAAE;EAC5C;EACA;EACA;EACA;EACA,IAAIC,MAAM,CAACC,SAAS,CAAChJ,OAAO,CAAC,IAAIA,OAAO,IAAI,CAAC,EAAE;IAC7C,OAAO,IAAIiJ,UAAU,CAAC,IAAIC,UAAU,CAAC,CAAClJ,OAAO,CAAC,CAAC,CAACmJ,MAAM,CAAC;EACzD;;EAEA;EACA;EACA,IAAIC,KAAK,CAACC,QAAQ,CAACrJ,OAAO,CAAC,EAAE;IAC3B,OAAO,IAAIiJ,UAAU,CAACjJ,OAAO,CAACmJ,MAAM,CAAC;EACvC;;EAEA;EACA;EACA;EACA,IAAIlJ,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IACtBA,OAAO,CAAC8B,KAAK,CAACwH,CAAC,IAAIP,MAAM,CAACC,SAAS,CAACM,CAAC,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,EAAE;IACrD,MAAMH,MAAM,GAAG,IAAII,WAAW,CAAC,CAACnB,IAAI,CAACoB,GAAG,CAAC,GAAGxJ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D,MAAMyJ,IAAI,GAAG,IAAIR,UAAU,CAACE,MAAM,CAAC;IAEnCnJ,OAAO,CAAC2I,OAAO,CAACW,CAAC,IAAI;MACnBG,IAAI,CAACH,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,CAAC;IAChC,CAAC,CAAC;IAEF,OAAOG,IAAI;EACb;;EAEA;EACA,MAAMrJ,KAAK,CACT,cAAAkB,MAAA,CAAcwH,QAAQ,uDACtB,0EAA0E,GAC1E,uCACF,CAAC;AACH;AAEA,SAASjH,eAAeA,CAACtB,KAAK,EAAEJ,MAAM,EAAE;EACtC;EACA;;EAEA;EACA,IAAI4I,MAAM,CAACW,aAAa,CAACnJ,KAAK,CAAC,EAAE;IAC/B;IACA;IACA;IACA;IACA,MAAM4I,MAAM,GAAG,IAAII,WAAW,CAC5BnB,IAAI,CAACoB,GAAG,CAACrJ,MAAM,EAAE,CAAC,GAAGwJ,WAAW,CAACC,iBAAiB,CACpD,CAAC;IAED,IAAIH,IAAI,GAAG,IAAIE,WAAW,CAACR,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACxCM,IAAI,CAAC,CAAC,CAAC,GAAGlJ,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;IAC7CkJ,IAAI,CAAC,CAAC,CAAC,GAAGlJ,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;;IAE7C;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MACbkJ,IAAI,GAAG,IAAIR,UAAU,CAACE,MAAM,EAAE,CAAC,CAAC;MAChCM,IAAI,CAACd,OAAO,CAAC,CAAC5G,IAAI,EAAEC,CAAC,KAAK;QACxByH,IAAI,CAACzH,CAAC,CAAC,GAAG,IAAI;MAChB,CAAC,CAAC;IACJ;IAEA,OAAO,IAAIiH,UAAU,CAACE,MAAM,CAAC;EAC/B;;EAEA;EACA,IAAIC,KAAK,CAACC,QAAQ,CAAC9I,KAAK,CAAC,EAAE;IACzB,OAAO,IAAI0I,UAAU,CAAC1I,KAAK,CAAC4I,MAAM,CAAC;EACrC;;EAEA;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA,SAASU,kBAAkBA,CAACC,QAAQ,EAAE/G,GAAG,EAAExC,KAAK,EAAE;EAChDlB,MAAM,CAACwD,IAAI,CAACiH,QAAQ,CAAC,CAACnB,OAAO,CAACoB,WAAW,IAAI;IAC3C,IACGA,WAAW,CAAC5J,MAAM,GAAG4C,GAAG,CAAC5C,MAAM,IAAI4J,WAAW,CAACC,OAAO,IAAA1I,MAAA,CAAIyB,GAAG,MAAG,CAAC,KAAK,CAAC,IACvEA,GAAG,CAAC5C,MAAM,GAAG4J,WAAW,CAAC5J,MAAM,IAAI4C,GAAG,CAACiH,OAAO,IAAA1I,MAAA,CAAIyI,WAAW,MAAG,CAAC,KAAK,CAAE,EACzE;MACA,MAAM,IAAI3J,KAAK,CACb,iDAAAkB,MAAA,CAAiDyI,WAAW,kBAAAzI,MAAA,CACxDyB,GAAG,kBACT,CAAC;IACH,CAAC,MAAM,IAAIgH,WAAW,KAAKhH,GAAG,EAAE;MAC9B,MAAM,IAAI3C,KAAK,4CAAAkB,MAAA,CAC8ByB,GAAG,uBAChD,CAAC;IACH;EACF,CAAC,CAAC;EAEF+G,QAAQ,CAAC/G,GAAG,CAAC,GAAGxC,KAAK;AACvB;;AAEA;AACA;AACA;AACA,SAASwE,qBAAqBA,CAACkF,eAAe,EAAE;EAC9C,OAAOC,YAAY,IAAI;IACrB;IACA;IACA;IACA,OAAO;MAACvG,MAAM,EAAE,CAACsG,eAAe,CAACC,YAAY,CAAC,CAACvG;IAAM,CAAC;EACxD,CAAC;AACH;AAEO,SAASlF,WAAWA,CAAC0L,GAAG,EAAE;EAC/B,OAAOlK,KAAK,CAACC,OAAO,CAACiK,GAAG,CAAC,IAAIjL,eAAe,CAACyD,cAAc,CAACwH,GAAG,CAAC;AAClE;AAEO,SAASzL,YAAYA,CAAC0L,CAAC,EAAE;EAC9B,OAAO,UAAU,CAAC5H,IAAI,CAAC4H,CAAC,CAAC;AAC3B;AAKO,SAASzL,gBAAgBA,CAAC0D,aAAa,EAAEgI,cAAc,EAAE;EAC9D,IAAI,CAACnL,eAAe,CAACyD,cAAc,CAACN,aAAa,CAAC,EAAE;IAClD,OAAO,KAAK;EACd;EAEA,IAAIiI,iBAAiB,GAAGzJ,SAAS;EACjCxB,MAAM,CAACwD,IAAI,CAACR,aAAa,CAAC,CAACsG,OAAO,CAAC4B,MAAM,IAAI;IAC3C,MAAMC,cAAc,GAAGD,MAAM,CAAChD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,IAAIgD,MAAM,KAAK,MAAM;IAEvE,IAAID,iBAAiB,KAAKzJ,SAAS,EAAE;MACnCyJ,iBAAiB,GAAGE,cAAc;IACpC,CAAC,MAAM,IAAIF,iBAAiB,KAAKE,cAAc,EAAE;MAC/C,IAAI,CAACH,cAAc,EAAE;QACnB,MAAM,IAAIjK,KAAK,2BAAAkB,MAAA,CACamJ,IAAI,CAACC,SAAS,CAACrI,aAAa,CAAC,CACzD,CAAC;MACH;MAEAiI,iBAAiB,GAAG,KAAK;IAC3B;EACF,CAAC,CAAC;EAEF,OAAO,CAAC,CAACA,iBAAiB,CAAC,CAAC;AAC9B;AAEA;AACA,SAAS7K,cAAcA,CAACkL,kBAAkB,EAAE;EAC1C,OAAO;IACL5K,sBAAsBA,CAACC,OAAO,EAAE;MAC9B;MACA;MACA;MACA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;QAC1B,OAAO,MAAM,KAAK;MACpB;;MAEA;MACA;MACA,IAAIA,OAAO,KAAKa,SAAS,EAAE;QACzBb,OAAO,GAAG,IAAI;MAChB;MAEA,MAAM4K,WAAW,GAAG1L,eAAe,CAACqC,EAAE,CAACC,KAAK,CAACxB,OAAO,CAAC;MAErD,OAAOO,KAAK,IAAI;QACd,IAAIA,KAAK,KAAKM,SAAS,EAAE;UACvBN,KAAK,GAAG,IAAI;QACd;;QAEA;QACA;QACA,IAAIrB,eAAe,CAACqC,EAAE,CAACC,KAAK,CAACjB,KAAK,CAAC,KAAKqK,WAAW,EAAE;UACnD,OAAO,KAAK;QACd;QAEA,OAAOD,kBAAkB,CAACzL,eAAe,CAACqC,EAAE,CAACsJ,IAAI,CAACtK,KAAK,EAAEP,OAAO,CAAC,CAAC;MACpE,CAAC;IACH;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASpB,kBAAkBA,CAACmE,GAAG,EAAgB;EAAA,IAAdqE,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAClD,MAAMyD,KAAK,GAAG/H,GAAG,CAACgI,KAAK,CAAC,GAAG,CAAC;EAC5B,MAAMC,SAAS,GAAGF,KAAK,CAAC3K,MAAM,GAAG2K,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EAC9C,MAAMG,UAAU,GACdH,KAAK,CAAC3K,MAAM,GAAG,CAAC,IAChBvB,kBAAkB,CAACkM,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE/D,OAAO,CACrD;EAED,SAASgE,WAAWA,CAACxE,YAAY,EAAElD,WAAW,EAAEnD,KAAK,EAAE;IACrD,OAAOqG,YAAY,IAAIA,YAAY,CAACzG,MAAM,GACtCuD,WAAW,GACT,CAAC;MAAEkD,YAAY;MAAElD,WAAW;MAAEnD;IAAM,CAAC,CAAC,GACtC,CAAC;MAAEqG,YAAY;MAAErG;IAAM,CAAC,CAAC,GAC3BmD,WAAW,GACT,CAAC;MAAEA,WAAW;MAAEnD;IAAM,CAAC,CAAC,GACxB,CAAC;MAAEA;IAAM,CAAC,CAAC;EACnB;;EAEA;EACA;EACA,OAAO,CAAC2D,GAAG,EAAE0C,YAAY,KAAK;IAC5B,IAAI3G,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,EAAE;MACtB;MACA;MACA;MACA,IAAI,EAAExF,YAAY,CAACsM,SAAS,CAAC,IAAIA,SAAS,GAAG9G,GAAG,CAAC/D,MAAM,CAAC,EAAE;QACxD,OAAO,EAAE;MACX;;MAEA;MACA;MACA;MACAyG,YAAY,GAAGA,YAAY,GAAGA,YAAY,CAACtF,MAAM,CAAC,CAAC0J,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,CAACA,SAAS,EAAE,GAAG,CAAC;IACxF;;IAEA;IACA,MAAMK,UAAU,GAAGnH,GAAG,CAAC8G,SAAS,CAAC;;IAEjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,UAAU,EAAE;MACf,OAAOG,WAAW,CAChBxE,YAAY,EACZ3G,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,IAAIjE,KAAK,CAACC,OAAO,CAACmL,UAAU,CAAC,EAC/CA,UACF,CAAC;IACH;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5M,WAAW,CAAC4M,UAAU,CAAC,EAAE;MAC5B,IAAIpL,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,EAAE;QACtB,OAAO,EAAE;MACX;MAEA,OAAOkH,WAAW,CAACxE,YAAY,EAAE,KAAK,EAAE/F,SAAS,CAAC;IACpD;IAEA,MAAM8C,MAAM,GAAG,EAAE;IACjB,MAAM2H,cAAc,GAAGC,IAAI,IAAI;MAC7B5H,MAAM,CAACkF,IAAI,CAAC,GAAG0C,IAAI,CAAC;IACtB,CAAC;;IAED;IACA;IACA;IACAD,cAAc,CAACL,UAAU,CAACI,UAAU,EAAEzE,YAAY,CAAC,CAAC;;IAEpD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI3G,KAAK,CAACC,OAAO,CAACmL,UAAU,CAAC,IACzB,EAAE3M,YAAY,CAACoM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI1D,OAAO,CAACoE,OAAO,CAAC,EAAE;MAChDH,UAAU,CAAC1C,OAAO,CAAC,CAAClC,MAAM,EAAEgF,UAAU,KAAK;QACzC,IAAIvM,eAAe,CAACyD,cAAc,CAAC8D,MAAM,CAAC,EAAE;UAC1C6E,cAAc,CAACL,UAAU,CAACxE,MAAM,EAAEG,YAAY,GAAGA,YAAY,CAACtF,MAAM,CAACmK,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;QACnG;MACF,CAAC,CAAC;IACJ;IAEA,OAAO9H,MAAM;EACf,CAAC;AACH;AAEA;AACA;AACA+H,aAAa,GAAG;EAAC9M;AAAkB,CAAC;AACpC+M,cAAc,GAAG,SAAAA,CAACC,OAAO,EAAmB;EAAA,IAAjBxE,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACrC,IAAI,OAAOuE,OAAO,KAAK,QAAQ,IAAIxE,OAAO,CAACyE,KAAK,EAAE;IAChDD,OAAO,mBAAAtK,MAAA,CAAmB8F,OAAO,CAACyE,KAAK,MAAG;EAC5C;EAEA,MAAMC,KAAK,GAAG,IAAI1L,KAAK,CAACwL,OAAO,CAAC;EAChCE,KAAK,CAACC,IAAI,GAAG,gBAAgB;EAC7B,OAAOD,KAAK;AACd,CAAC;AAEM,SAASjN,cAAcA,CAAC2J,mBAAmB,EAAE;EAClD,OAAO;IAAC7E,MAAM,EAAE;EAAK,CAAC;AACxB;AAEA;AACA;AACA,SAASiE,uBAAuBA,CAACvF,aAAa,EAAEtB,OAAO,EAAE4E,MAAM,EAAE;EAC/D;EACA;EACA;EACA,MAAMqG,gBAAgB,GAAG3M,MAAM,CAACwD,IAAI,CAACR,aAAa,CAAC,CAAC3B,GAAG,CAACuL,QAAQ,IAAI;IAClE,MAAMjM,OAAO,GAAGqC,aAAa,CAAC4J,QAAQ,CAAC;IAEvC,MAAMC,WAAW,GACf,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACF,QAAQ,CAAC,IACjD,OAAOjM,OAAO,KAAK,QACpB;IAED,MAAMoM,cAAc,GAClB,CAAC,KAAK,EAAE,KAAK,CAAC,CAACD,QAAQ,CAACF,QAAQ,CAAC,IACjCjM,OAAO,KAAKX,MAAM,CAACW,OAAO,CAC3B;IAED,MAAMqM,eAAe,GACnB,CAAC,KAAK,EAAE,MAAM,CAAC,CAACF,QAAQ,CAACF,QAAQ,CAAC,IAC/BhM,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IACtB,CAACA,OAAO,CAACc,IAAI,CAACwI,CAAC,IAAIA,CAAC,KAAKjK,MAAM,CAACiK,CAAC,CAAC,CACtC;IAED,IAAI,EAAE4C,WAAW,IAAIG,eAAe,IAAID,cAAc,CAAC,EAAE;MACvDrL,OAAO,CAACyG,SAAS,GAAG,KAAK;IAC3B;IAEA,IAAIpJ,MAAM,CAACiD,IAAI,CAACsD,eAAe,EAAEsH,QAAQ,CAAC,EAAE;MAC1C,OAAOtH,eAAe,CAACsH,QAAQ,CAAC,CAACjM,OAAO,EAAEqC,aAAa,EAAEtB,OAAO,EAAE4E,MAAM,CAAC;IAC3E;IAEA,IAAIvH,MAAM,CAACiD,IAAI,CAAChD,iBAAiB,EAAE4N,QAAQ,CAAC,EAAE;MAC5C,MAAM7E,OAAO,GAAG/I,iBAAiB,CAAC4N,QAAQ,CAAC;MAC3C,OAAOpH,sCAAsC,CAC3CuC,OAAO,CAACrH,sBAAsB,CAACC,OAAO,EAAEqC,aAAa,EAAEtB,OAAO,CAAC,EAC/DqG,OACF,CAAC;IACH;IAEA,MAAM,IAAIhH,KAAK,2BAAAkB,MAAA,CAA2B2K,QAAQ,CAAE,CAAC;EACvD,CAAC,CAAC;EAEF,OAAOvG,mBAAmB,CAACsG,gBAAgB,CAAC;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASlN,WAAWA,CAACwN,KAAK,EAAEC,SAAS,EAAEC,UAAU,EAAa;EAAA,IAAXC,IAAI,GAAApF,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACjEiF,KAAK,CAAC3D,OAAO,CAAC+D,IAAI,IAAI;IACpB,MAAMC,SAAS,GAAGD,IAAI,CAAC3B,KAAK,CAAC,GAAG,CAAC;IACjC,IAAI6B,IAAI,GAAGH,IAAI;;IAEf;IACA,MAAMI,OAAO,GAAGF,SAAS,CAACzB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACpJ,KAAK,CAAC,CAACiB,GAAG,EAAEf,CAAC,KAAK;MACvD,IAAI,CAAC5D,MAAM,CAACiD,IAAI,CAACuL,IAAI,EAAE7J,GAAG,CAAC,EAAE;QAC3B6J,IAAI,CAAC7J,GAAG,CAAC,GAAG,CAAC,CAAC;MAChB,CAAC,MAAM,IAAI6J,IAAI,CAAC7J,GAAG,CAAC,KAAK1D,MAAM,CAACuN,IAAI,CAAC7J,GAAG,CAAC,CAAC,EAAE;QAC1C6J,IAAI,CAAC7J,GAAG,CAAC,GAAGyJ,UAAU,CACpBI,IAAI,CAAC7J,GAAG,CAAC,EACT4J,SAAS,CAACzB,KAAK,CAAC,CAAC,EAAElJ,CAAC,GAAG,CAAC,CAAC,CAACmJ,IAAI,CAAC,GAAG,CAAC,EACnCuB,IACF,CAAC;;QAED;QACA,IAAIE,IAAI,CAAC7J,GAAG,CAAC,KAAK1D,MAAM,CAACuN,IAAI,CAAC7J,GAAG,CAAC,CAAC,EAAE;UACnC,OAAO,KAAK;QACd;MACF;MAEA6J,IAAI,GAAGA,IAAI,CAAC7J,GAAG,CAAC;MAEhB,OAAO,IAAI;IACb,CAAC,CAAC;IAEF,IAAI8J,OAAO,EAAE;MACX,MAAMC,OAAO,GAAGH,SAAS,CAACA,SAAS,CAACxM,MAAM,GAAG,CAAC,CAAC;MAC/C,IAAI/B,MAAM,CAACiD,IAAI,CAACuL,IAAI,EAAEE,OAAO,CAAC,EAAE;QAC9BF,IAAI,CAACE,OAAO,CAAC,GAAGN,UAAU,CAACI,IAAI,CAACE,OAAO,CAAC,EAAEJ,IAAI,EAAEA,IAAI,CAAC;MACvD,CAAC,MAAM;QACLE,IAAI,CAACE,OAAO,CAAC,GAAGP,SAAS,CAACG,IAAI,CAAC;MACjC;IACF;EACF,CAAC,CAAC;EAEF,OAAOD,IAAI;AACb;AAEA;AACA;AACA;AACA,SAASpG,YAAYA,CAACP,KAAK,EAAE;EAC3B,OAAO7F,KAAK,CAACC,OAAO,CAAC4F,KAAK,CAAC,GAAGA,KAAK,CAACoF,KAAK,CAAC,CAAC,GAAG,CAACpF,KAAK,CAACwD,CAAC,EAAExD,KAAK,CAACiH,CAAC,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASC,4BAA4BA,CAAClD,QAAQ,EAAE/G,GAAG,EAAExC,KAAK,EAAE;EAC1D,IAAIA,KAAK,IAAIlB,MAAM,CAAC4N,cAAc,CAAC1M,KAAK,CAAC,KAAKlB,MAAM,CAACC,SAAS,EAAE;IAC9D4N,0BAA0B,CAACpD,QAAQ,EAAE/G,GAAG,EAAExC,KAAK,CAAC;EAClD,CAAC,MAAM,IAAI,EAAEA,KAAK,YAAYK,MAAM,CAAC,EAAE;IACrCiJ,kBAAkB,CAACC,QAAQ,EAAE/G,GAAG,EAAExC,KAAK,CAAC;EAC1C;AACF;;AAEA;AACA;AACA,SAAS2M,0BAA0BA,CAACpD,QAAQ,EAAE/G,GAAG,EAAExC,KAAK,EAAE;EACxD,MAAMsC,IAAI,GAAGxD,MAAM,CAACwD,IAAI,CAACtC,KAAK,CAAC;EAC/B,MAAM4M,cAAc,GAAGtK,IAAI,CAACC,MAAM,CAACsK,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;EAEvD,IAAID,cAAc,CAAChN,MAAM,GAAG,CAAC,IAAI,CAAC0C,IAAI,CAAC1C,MAAM,EAAE;IAC7C;IACA;IACA,IAAI0C,IAAI,CAAC1C,MAAM,KAAKgN,cAAc,CAAChN,MAAM,EAAE;MACzC,MAAM,IAAIC,KAAK,sBAAAkB,MAAA,CAAsB6L,cAAc,CAAC,CAAC,CAAC,CAAE,CAAC;IAC3D;IAEAE,cAAc,CAAC9M,KAAK,EAAEwC,GAAG,CAAC;IAC1B8G,kBAAkB,CAACC,QAAQ,EAAE/G,GAAG,EAAExC,KAAK,CAAC;EAC1C,CAAC,MAAM;IACLlB,MAAM,CAACwD,IAAI,CAACtC,KAAK,CAAC,CAACoI,OAAO,CAACyE,EAAE,IAAI;MAC/B,MAAME,MAAM,GAAG/M,KAAK,CAAC6M,EAAE,CAAC;MAExB,IAAIA,EAAE,KAAK,KAAK,EAAE;QAChBJ,4BAA4B,CAAClD,QAAQ,EAAE/G,GAAG,EAAEuK,MAAM,CAAC;MACrD,CAAC,MAAM,IAAIF,EAAE,KAAK,MAAM,EAAE;QACxB;QACAE,MAAM,CAAC3E,OAAO,CAACX,OAAO,IACpBgF,4BAA4B,CAAClD,QAAQ,EAAE/G,GAAG,EAAEiF,OAAO,CACrD,CAAC;MACH;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACO,SAASjJ,+BAA+BA,CAACwO,KAAK,EAAiB;EAAA,IAAfzD,QAAQ,GAAAzC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAClE,IAAIhI,MAAM,CAAC4N,cAAc,CAACM,KAAK,CAAC,KAAKlO,MAAM,CAACC,SAAS,EAAE;IACrD;IACAD,MAAM,CAACwD,IAAI,CAAC0K,KAAK,CAAC,CAAC5E,OAAO,CAAC5F,GAAG,IAAI;MAChC,MAAMxC,KAAK,GAAGgN,KAAK,CAACxK,GAAG,CAAC;MAExB,IAAIA,GAAG,KAAK,MAAM,EAAE;QAClB;QACAxC,KAAK,CAACoI,OAAO,CAACX,OAAO,IACnBjJ,+BAA+B,CAACiJ,OAAO,EAAE8B,QAAQ,CACnD,CAAC;MACH,CAAC,MAAM,IAAI/G,GAAG,KAAK,KAAK,EAAE;QACxB;QACA,IAAIxC,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;UACtBpB,+BAA+B,CAACwB,KAAK,CAAC,CAAC,CAAC,EAAEuJ,QAAQ,CAAC;QACrD;MACF,CAAC,MAAM,IAAI/G,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACzB;QACAiK,4BAA4B,CAAClD,QAAQ,EAAE/G,GAAG,EAAExC,KAAK,CAAC;MACpD;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA,IAAIrB,eAAe,CAACsO,aAAa,CAACD,KAAK,CAAC,EAAE;MACxC1D,kBAAkB,CAACC,QAAQ,EAAE,KAAK,EAAEyD,KAAK,CAAC;IAC5C;EACF;EAEA,OAAOzD,QAAQ;AACjB;AAQO,SAAS9K,iBAAiBA,CAACyO,MAAM,EAAE;EACxC;EACA;EACA;EACA,IAAIC,UAAU,GAAGrO,MAAM,CAACwD,IAAI,CAAC4K,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC;;EAE3C;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,EAAED,UAAU,CAACvN,MAAM,KAAK,CAAC,IAAIuN,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,IACrD,EAAEA,UAAU,CAACvB,QAAQ,CAAC,KAAK,CAAC,IAAIsB,MAAM,CAACG,GAAG,CAAC,EAAE;IAC/CF,UAAU,GAAGA,UAAU,CAAC5K,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK,KAAK,CAAC;EACtD;EAEA,IAAI8K,SAAS,GAAG,IAAI,CAAC,CAAC;;EAEtBH,UAAU,CAAC/E,OAAO,CAACmF,OAAO,IAAI;IAC5B,MAAMC,IAAI,GAAG,CAAC,CAACN,MAAM,CAACK,OAAO,CAAC;IAE9B,IAAID,SAAS,KAAK,IAAI,EAAE;MACtBA,SAAS,GAAGE,IAAI;IAClB;;IAEA;IACA,IAAIF,SAAS,KAAKE,IAAI,EAAE;MACtB,MAAMpC,cAAc,CAClB,0DACF,CAAC;IACH;EACF,CAAC,CAAC;EAEF,MAAMqC,mBAAmB,GAAGlP,WAAW,CACrC4O,UAAU,EACVhB,IAAI,IAAImB,SAAS,EACjB,CAACI,IAAI,EAAEvB,IAAI,EAAEwB,QAAQ,KAAK;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGD,QAAQ;IAC5B,MAAME,WAAW,GAAG1B,IAAI;IACxB,MAAMf,cAAc,CAClB,QAAArK,MAAA,CAAQ6M,WAAW,WAAA7M,MAAA,CAAQ8M,WAAW,iCACtC,sEAAsE,GACtE,uBACF,CAAC;EACH,CAAC,CAAC;EAEJ,OAAO;IAACP,SAAS;IAAEjB,IAAI,EAAEoB;EAAmB,CAAC;AAC/C;AAGO,SAAS/O,oBAAoBA,CAACqD,MAAM,EAAE;EAC3C,OAAO/B,KAAK,IAAI;IACd,IAAIA,KAAK,YAAYK,MAAM,EAAE;MAC3B,OAAOL,KAAK,CAAC8N,QAAQ,CAAC,CAAC,KAAK/L,MAAM,CAAC+L,QAAQ,CAAC,CAAC;IAC/C;;IAEA;IACA,IAAI,OAAO9N,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,KAAK;IACd;;IAEA;IACA;IACA;IACA;IACA;IACA+B,MAAM,CAACgM,SAAS,GAAG,CAAC;IAEpB,OAAOhM,MAAM,CAACE,IAAI,CAACjC,KAAK,CAAC;EAC3B,CAAC;AACH;AAEA;AACA;AACA;AACA,SAASgO,iBAAiBA,CAACxL,GAAG,EAAE2J,IAAI,EAAE;EACpC,IAAI3J,GAAG,CAACoJ,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrB,MAAM,IAAI/L,KAAK,sBAAAkB,MAAA,CACQyB,GAAG,YAAAzB,MAAA,CAASoL,IAAI,OAAApL,MAAA,CAAIyB,GAAG,+BAC9C,CAAC;EACH;EAEA,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClB,MAAM,IAAI3C,KAAK,oCAAAkB,MAAA,CACsBoL,IAAI,OAAApL,MAAA,CAAIyB,GAAG,+BAChD,CAAC;EACH;AACF;;AAEA;AACA,SAASsK,cAAcA,CAACC,MAAM,EAAEZ,IAAI,EAAE;EACpC,IAAIY,MAAM,IAAIjO,MAAM,CAAC4N,cAAc,CAACK,MAAM,CAAC,KAAKjO,MAAM,CAACC,SAAS,EAAE;IAChED,MAAM,CAACwD,IAAI,CAACyK,MAAM,CAAC,CAAC3E,OAAO,CAAC5F,GAAG,IAAI;MACjCwL,iBAAiB,CAACxL,GAAG,EAAE2J,IAAI,CAAC;MAC5BW,cAAc,CAACC,MAAM,CAACvK,GAAG,CAAC,EAAE2J,IAAI,GAAG,GAAG,GAAG3J,GAAG,CAAC;IAC/C,CAAC,CAAC;EACJ;AACF,C;;;;;;;;;;;AC/3CA9E,MAAM,CAACE,MAAM,CAAC;EAACqQ,kBAAkB,EAACA,CAAA,KAAIA,kBAAkB;EAACC,wBAAwB,EAACA,CAAA,KAAIA,wBAAwB;EAACC,oBAAoB,EAACA,CAAA,KAAIA,oBAAoB;EAACC,mBAAmB,EAACA,CAAA,KAAIA;AAAmB,CAAC,CAAC;AAGnM,SAASH,kBAAkBA,CAACI,MAAM,EAAE;EACzC,UAAAtN,MAAA,CAAUsN,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;AACnC;AAEO,MAAMJ,wBAAwB,GAAG,CACtC,yBAAyB,EACzB,gBAAgB,EAChB,WAAW;AACX;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,aAAa;AACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,SAAS;AACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ,CACT;AAEM,MAAMC,oBAAoB,GAAG;AAClC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,OAAO;AACP;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,OAAO;AACP;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,SAAS;AACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,KAAK,CACN;AAEM,MAAMC,mBAAmB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,C;;;;;;;;;;;ACpJtF1Q,MAAM,CAACE,MAAM,CAAC;EAACgB,OAAO,EAACA,CAAA,KAAI2P;AAAM,CAAC,CAAC;AAAC,IAAI5P,eAAe;AAACjB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAACF,eAAe,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIhB,MAAM;AAACH,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACE,MAAMA,CAACgB,CAAC,EAAC;IAAChB,MAAM,GAACgB,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsP,oBAAoB,EAACF,kBAAkB;AAACvQ,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACwQ,oBAAoBA,CAACtP,CAAC,EAAC;IAACsP,oBAAoB,GAACtP,CAAC;EAAA,CAAC;EAACoP,kBAAkBA,CAACpP,CAAC,EAAC;IAACoP,kBAAkB,GAACpP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAMpV,MAAM0P,MAAM,CAAC;EAC1B;EACAC,WAAWA,CAACC,UAAU,EAAElG,QAAQ,EAAgB;IAAA,IAAd1B,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IAC5C,IAAI,CAAC2H,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAAClO,OAAO,GAAG,IAAImO,SAAS,CAACC,OAAO,CAACrG,QAAQ,CAAC;IAE9C,IAAI5J,eAAe,CAACkQ,4BAA4B,CAACtG,QAAQ,CAAC,EAAE;MAC1D;MACA,IAAI,CAACuG,WAAW,GAAGjR,MAAM,CAACiD,IAAI,CAACyH,QAAQ,EAAE,KAAK,CAAC,GAAGA,QAAQ,CAAC8E,GAAG,GAAG9E,QAAQ;IAC3E,CAAC,MAAM;MACL,IAAI,CAACuG,WAAW,GAAGxO,SAAS;MAE5B,IAAI,IAAI,CAACE,OAAO,CAACuO,WAAW,CAAC,CAAC,IAAIlI,OAAO,CAACuG,IAAI,EAAE;QAC9C,IAAI,CAACsB,MAAM,GAAG,IAAIC,SAAS,CAACK,MAAM,CAACnI,OAAO,CAACuG,IAAI,IAAI,EAAE,CAAC;MACxD;IACF;IAEA,IAAI,CAAC6B,IAAI,GAAGpI,OAAO,CAACoI,IAAI,IAAI,CAAC;IAC7B,IAAI,CAACC,KAAK,GAAGrI,OAAO,CAACqI,KAAK;IAC1B,IAAI,CAAChC,MAAM,GAAGrG,OAAO,CAACsI,UAAU,IAAItI,OAAO,CAACqG,MAAM;IAElD,IAAI,CAACkC,aAAa,GAAGzQ,eAAe,CAAC0Q,kBAAkB,CAAC,IAAI,CAACnC,MAAM,IAAI,CAAC,CAAC,CAAC;IAE1E,IAAI,CAACoC,UAAU,GAAG3Q,eAAe,CAAC4Q,aAAa,CAAC1I,OAAO,CAAC2I,SAAS,CAAC;;IAElE;IACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;MAClC,IAAI,CAACC,QAAQ,GAAG7I,OAAO,CAAC6I,QAAQ,KAAKpP,SAAS,GAAG,IAAI,GAAGuG,OAAO,CAAC6I,QAAQ;IAC1E;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACD,QAAQ,EAAE;MACjB;MACA,IAAI,CAACE,OAAO,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAK,CAAC,EAAE,IAAI,CAAC;IACpD;IAEA,OAAO,IAAI,CAACC,cAAc,CAAC;MACzBC,OAAO,EAAE;IACX,CAAC,CAAC,CAACpQ,MAAM;EACX;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEqQ,KAAKA,CAAA,EAAG;IACN,MAAM7M,MAAM,GAAG,EAAE;IAEjB,IAAI,CAACgF,OAAO,CAACzE,GAAG,IAAI;MAClBP,MAAM,CAACkF,IAAI,CAAC3E,GAAG,CAAC;IAClB,CAAC,CAAC;IAEF,OAAOP,MAAM;EACf;EAEA,CAAC8M,MAAM,CAACC,QAAQ,IAAI;IAClB,IAAI,IAAI,CAACT,QAAQ,EAAE;MACjB,IAAI,CAACE,OAAO,CAAC;QACXQ,WAAW,EAAE,IAAI;QACjBN,OAAO,EAAE,IAAI;QACbO,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;IAEA,IAAIC,KAAK,GAAG,CAAC;IACb,MAAMC,OAAO,GAAG,IAAI,CAACT,cAAc,CAAC;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAEtD,OAAO;MACLS,IAAI,EAAEA,CAAA,KAAM;QACV,IAAIF,KAAK,GAAGC,OAAO,CAAC5Q,MAAM,EAAE;UAC1B;UACA,IAAI6H,OAAO,GAAG,IAAI,CAAC2H,aAAa,CAACoB,OAAO,CAACD,KAAK,EAAE,CAAC,CAAC;UAElD,IAAI,IAAI,CAACjB,UAAU,EAAE7H,OAAO,GAAG,IAAI,CAAC6H,UAAU,CAAC7H,OAAO,CAAC;UAEvD,OAAO;YAAEzH,KAAK,EAAEyH;UAAQ,CAAC;QAC3B;QAEA,OAAO;UAAEiJ,IAAI,EAAE;QAAK,CAAC;MACvB;IACF,CAAC;EACH;EAEA,CAACR,MAAM,CAACS,aAAa,IAAI;IACvB,MAAMC,UAAU,GAAG,IAAI,CAACV,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC1C,OAAO;MACL,MAAMM,IAAIA,CAAA,EAAG;QACX,OAAOI,OAAO,CAACC,OAAO,CAACF,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC;MAC3C;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErI,OAAOA,CAAC2I,QAAQ,EAAEC,OAAO,EAAE;IACzB,IAAI,IAAI,CAACtB,QAAQ,EAAE;MACjB,IAAI,CAACE,OAAO,CAAC;QACXQ,WAAW,EAAE,IAAI;QACjBN,OAAO,EAAE,IAAI;QACbO,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;IAEA,IAAI,CAACP,cAAc,CAAC;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC5H,OAAO,CAAC,CAACX,OAAO,EAAEhG,CAAC,KAAK;MAC7D;MACAgG,OAAO,GAAG,IAAI,CAAC2H,aAAa,CAAC3H,OAAO,CAAC;MAErC,IAAI,IAAI,CAAC6H,UAAU,EAAE;QACnB7H,OAAO,GAAG,IAAI,CAAC6H,UAAU,CAAC7H,OAAO,CAAC;MACpC;MAEAsJ,QAAQ,CAACjQ,IAAI,CAACkQ,OAAO,EAAEvJ,OAAO,EAAEhG,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEAwP,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC3B,UAAU;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEnP,GAAGA,CAAC4Q,QAAQ,EAAEC,OAAO,EAAE;IACrB,MAAM5N,MAAM,GAAG,EAAE;IAEjB,IAAI,CAACgF,OAAO,CAAC,CAACzE,GAAG,EAAElC,CAAC,KAAK;MACvB2B,MAAM,CAACkF,IAAI,CAACyI,QAAQ,CAACjQ,IAAI,CAACkQ,OAAO,EAAErN,GAAG,EAAElC,CAAC,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC,CAAC;IAEF,OAAO2B,MAAM;EACf;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE8N,OAAOA,CAACrK,OAAO,EAAE;IACf,OAAOlI,eAAe,CAACwS,0BAA0B,CAAC,IAAI,EAAEtK,OAAO,CAAC;EAClE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEuK,YAAYA,CAACvK,OAAO,EAAE;IACpB,OAAO,IAAIgK,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,IAAI,CAACI,OAAO,CAACrK,OAAO,CAAC,CAAC,CAAC;EAC/D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEwK,cAAcA,CAACxK,OAAO,EAAE;IACtB,MAAMmJ,OAAO,GAAGrR,eAAe,CAAC2S,kCAAkC,CAACzK,OAAO,CAAC;;IAE3E;IACA;IACA;IACA;IACA,IAAI,CAACA,OAAO,CAAC0K,gBAAgB,IAAI,CAACvB,OAAO,KAAK,IAAI,CAACf,IAAI,IAAI,IAAI,CAACC,KAAK,CAAC,EAAE;MACtE,MAAM,IAAIrP,KAAK,CACb,qEAAqE,GACnE,mEACJ,CAAC;IACH;IAEA,IAAI,IAAI,CAACqN,MAAM,KAAK,IAAI,CAACA,MAAM,CAACG,GAAG,KAAK,CAAC,IAAI,IAAI,CAACH,MAAM,CAACG,GAAG,KAAK,KAAK,CAAC,EAAE;MACvE,MAAMxN,KAAK,CAAC,sDAAsD,CAAC;IACrE;IAEA,MAAM2R,SAAS,GACb,IAAI,CAAChR,OAAO,CAACuO,WAAW,CAAC,CAAC,IAAIiB,OAAO,IAAI,IAAIrR,eAAe,CAAC8S,MAAM,CAAC,CAAC;IAEvE,MAAMzE,KAAK,GAAG;MACZ0E,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,KAAK;MACZH,SAAS;MACThR,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE;MACvBwP,OAAO;MACP4B,YAAY,EAAE,IAAI,CAACxC,aAAa;MAChCyC,eAAe,EAAE,IAAI;MACrBnD,MAAM,EAAEsB,OAAO,IAAI,IAAI,CAACtB;IAC1B,CAAC;IAED,IAAIoD,GAAG;;IAEP;IACA;IACA,IAAI,IAAI,CAACpC,QAAQ,EAAE;MACjBoC,GAAG,GAAG,IAAI,CAACrD,UAAU,CAACsD,QAAQ,EAAE;MAChC,IAAI,CAACtD,UAAU,CAACuD,OAAO,CAACF,GAAG,CAAC,GAAG9E,KAAK;IACtC;IAEAA,KAAK,CAACiF,OAAO,GAAG,IAAI,CAAClC,cAAc,CAAC;MAClCC,OAAO;MACPwB,SAAS,EAAExE,KAAK,CAACwE;IACnB,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC/C,UAAU,CAACyD,MAAM,EAAE;MAC1BlF,KAAK,CAAC6E,eAAe,GAAG7B,OAAO,GAAG,EAAE,GAAG,IAAIrR,eAAe,CAAC8S,MAAM,CAAC,CAAC;IACrE;;IAEA;IACA;IACA;IACA;;IAEA;IACA;IACA,MAAMU,YAAY,GAAIvO,EAAE,IAAK;MAC3B,IAAI,CAACA,EAAE,EAAE;QACP,OAAO,MAAM,CAAC,CAAC;MACjB;MAEA,MAAMwO,IAAI,GAAG,IAAI;MAEjB,OAAO,SAAU;MAAA,GAAW;QAC1B,IAAIA,IAAI,CAAC3D,UAAU,CAACyD,MAAM,EAAE;UAC1B;QACF;QAEA,MAAMG,IAAI,GAAGvL,SAAS;QAEtBsL,IAAI,CAAC3D,UAAU,CAAC6D,aAAa,CAACC,SAAS,CAAC,MAAM;UAC5C3O,EAAE,CAAC4O,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;IAEDrF,KAAK,CAAC6C,KAAK,GAAGsC,YAAY,CAACtL,OAAO,CAACgJ,KAAK,CAAC;IACzC7C,KAAK,CAACqD,OAAO,GAAG8B,YAAY,CAACtL,OAAO,CAACwJ,OAAO,CAAC;IAC7CrD,KAAK,CAAC8C,OAAO,GAAGqC,YAAY,CAACtL,OAAO,CAACiJ,OAAO,CAAC;IAE7C,IAAIE,OAAO,EAAE;MACXhD,KAAK,CAACoD,WAAW,GAAG+B,YAAY,CAACtL,OAAO,CAACuJ,WAAW,CAAC;MACrDpD,KAAK,CAACsD,WAAW,GAAG6B,YAAY,CAACtL,OAAO,CAACyJ,WAAW,CAAC;IACvD;IAEA,IAAI,CAACzJ,OAAO,CAAC4L,iBAAiB,IAAI,CAAC,IAAI,CAAChE,UAAU,CAACyD,MAAM,EAAE;MAAA,IAAAQ,cAAA,EAAAC,mBAAA;MACzD,MAAMC,OAAO,GAAIjP,GAAG,IAAK;QACvB,MAAMuJ,MAAM,GAAGrE,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;QAE/B,OAAOuJ,MAAM,CAACG,GAAG;QAEjB,IAAI2C,OAAO,EAAE;UACXhD,KAAK,CAACoD,WAAW,CAACzM,GAAG,CAAC0J,GAAG,EAAE,IAAI,CAAC+B,aAAa,CAAClC,MAAM,CAAC,EAAE,IAAI,CAAC;QAC9D;QAEAF,KAAK,CAAC6C,KAAK,CAAClM,GAAG,CAAC0J,GAAG,EAAE,IAAI,CAAC+B,aAAa,CAAClC,MAAM,CAAC,CAAC;MAClD,CAAC;MACD;MACA,IAAIF,KAAK,CAACiF,OAAO,CAACrS,MAAM,EAAE;QACxB,KAAK,MAAM+D,GAAG,IAAIqJ,KAAK,CAACiF,OAAO,EAAE;UAC/BW,OAAO,CAACjP,GAAG,CAAC;QACd;MACF;MACA;MACA,KAAA+O,cAAA,GAAI1F,KAAK,CAACiF,OAAO,cAAAS,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAeI,IAAI,cAAAH,mBAAA,eAAnBA,mBAAA,CAAA7R,IAAA,CAAA4R,cAAsB,CAAC,EAAE;QAC3B1F,KAAK,CAACiF,OAAO,CAAC7J,OAAO,CAACwK,OAAO,CAAC;MAChC;IACF;IAEA,MAAMG,MAAM,GAAGjU,MAAM,CAAC+D,MAAM,CAAC,IAAIlE,eAAe,CAACqU,aAAa,CAAC,CAAC,EAAE;MAChEvE,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BwE,IAAI,EAAEA,CAAA,KAAM;QACV,IAAI,IAAI,CAACvD,QAAQ,EAAE;UACjB,OAAO,IAAI,CAACjB,UAAU,CAACuD,OAAO,CAACF,GAAG,CAAC;QACrC;MACF,CAAC;MACDoB,OAAO,EAAE,KAAK;MACdC,cAAc,EAAE;IAClB,CAAC,CAAC;IAEF,IAAI,IAAI,CAACzD,QAAQ,IAAID,OAAO,CAAC2D,MAAM,EAAE;MACnC;MACA;MACA;MACA;MACA;MACA3D,OAAO,CAAC4D,YAAY,CAAC,MAAM;QACzBN,MAAM,CAACE,IAAI,CAAC,CAAC;MACf,CAAC,CAAC;IACJ;;IAEA;IACA;IACA,MAAMK,WAAW,GAAG,IAAI,CAAC7E,UAAU,CAAC6D,aAAa,CAACiB,KAAK,CAAC,CAAC;IAEzD,IAAID,WAAW,YAAYzC,OAAO,EAAE;MAClCkC,MAAM,CAACI,cAAc,GAAGG,WAAW;MACnCA,WAAW,CAACE,IAAI,CAAC,MAAOT,MAAM,CAACG,OAAO,GAAG,IAAK,CAAC;IACjD,CAAC,MAAM;MACLH,MAAM,CAACG,OAAO,GAAG,IAAI;MACrBH,MAAM,CAACI,cAAc,GAAGtC,OAAO,CAACC,OAAO,CAAC,CAAC;IAC3C;IAEA,OAAOiC,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEU,mBAAmBA,CAAC5M,OAAO,EAAE;IAC3B,OAAO,IAAIgK,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAMiC,MAAM,GAAG,IAAI,CAAC1B,cAAc,CAACxK,OAAO,CAAC;MAC3CkM,MAAM,CAACI,cAAc,CAACK,IAAI,CAAC,MAAM1C,OAAO,CAACiC,MAAM,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ;;EAEA;EACA;EACAnD,OAAOA,CAAC8D,QAAQ,EAAEnC,gBAAgB,EAAE;IAClC,IAAI9B,OAAO,CAAC2D,MAAM,EAAE;MAClB,MAAMO,UAAU,GAAG,IAAIlE,OAAO,CAACmE,UAAU,CAAC,CAAC;MAC3C,MAAMC,MAAM,GAAGF,UAAU,CAACtD,OAAO,CAACyD,IAAI,CAACH,UAAU,CAAC;MAElDA,UAAU,CAACI,MAAM,CAAC,CAAC;MAEnB,MAAMlN,OAAO,GAAG;QAAE0K,gBAAgB;QAAEkB,iBAAiB,EAAE;MAAK,CAAC;MAE7D,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,CAAC,CAACrK,OAAO,CACnExE,EAAE,IAAI;QACJ,IAAI8P,QAAQ,CAAC9P,EAAE,CAAC,EAAE;UAChBiD,OAAO,CAACjD,EAAE,CAAC,GAAGiQ,MAAM;QACtB;MACF,CACF,CAAC;;MAED;MACA,IAAI,CAACxC,cAAc,CAACxK,OAAO,CAAC;IAC9B;EACF;EAEAmN,kBAAkBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACvF,UAAU,CAACjD,IAAI;EAC7B;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAuE,cAAcA,CAAA,EAAe;IAAA,IAAdlJ,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IACzB;IACA;IACA;IACA;IACA,MAAMmN,cAAc,GAAGpN,OAAO,CAACoN,cAAc,KAAK,KAAK;;IAEvD;IACA;IACA,MAAMhC,OAAO,GAAGpL,OAAO,CAACmJ,OAAO,GAAG,EAAE,GAAG,IAAIrR,eAAe,CAAC8S,MAAM,CAAC,CAAC;;IAEnE;IACA,IAAI,IAAI,CAAC3C,WAAW,KAAKxO,SAAS,EAAE;MAClC;MACA;MACA,IAAI2T,cAAc,IAAI,IAAI,CAAChF,IAAI,EAAE;QAC/B,OAAOgD,OAAO;MAChB;MAEA,MAAMiC,WAAW,GAAG,IAAI,CAACzF,UAAU,CAAC0F,KAAK,CAACC,GAAG,CAAC,IAAI,CAACtF,WAAW,CAAC;MAC/D,IAAIoF,WAAW,EAAE;QACf,IAAIrN,OAAO,CAACmJ,OAAO,EAAE;UACnBiC,OAAO,CAAC3J,IAAI,CAAC4L,WAAW,CAAC;QAC3B,CAAC,MAAM;UACLjC,OAAO,CAACoC,GAAG,CAAC,IAAI,CAACvF,WAAW,EAAEoF,WAAW,CAAC;QAC5C;MACF;MACA,OAAOjC,OAAO;IAChB;;IAEA;;IAEA;IACA;IACA;IACA,IAAIT,SAAS;IACb,IAAI,IAAI,CAAChR,OAAO,CAACuO,WAAW,CAAC,CAAC,IAAIlI,OAAO,CAACmJ,OAAO,EAAE;MACjD,IAAInJ,OAAO,CAAC2K,SAAS,EAAE;QACrBA,SAAS,GAAG3K,OAAO,CAAC2K,SAAS;QAC7BA,SAAS,CAAC8C,KAAK,CAAC,CAAC;MACnB,CAAC,MAAM;QACL9C,SAAS,GAAG,IAAI7S,eAAe,CAAC8S,MAAM,CAAC,CAAC;MAC1C;IACF;IAEA8C,MAAM,CAACC,SAAS,CAAC,MAAM;MACrB,IAAI,CAAC/F,UAAU,CAAC0F,KAAK,CAAC/L,OAAO,CAAC,CAACzE,GAAG,EAAE8Q,EAAE,KAAK;QACzC,MAAMC,WAAW,GAAG,IAAI,CAAClU,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC;QACrD,IAAI+Q,WAAW,CAACtR,MAAM,EAAE;UACtB,IAAIyD,OAAO,CAACmJ,OAAO,EAAE;YACnBiC,OAAO,CAAC3J,IAAI,CAAC3E,GAAG,CAAC;YAEjB,IAAI6N,SAAS,IAAIkD,WAAW,CAAClP,QAAQ,KAAKlF,SAAS,EAAE;cACnDkR,SAAS,CAAC6C,GAAG,CAACI,EAAE,EAAEC,WAAW,CAAClP,QAAQ,CAAC;YACzC;UACF,CAAC,MAAM;YACLyM,OAAO,CAACoC,GAAG,CAACI,EAAE,EAAE9Q,GAAG,CAAC;UACtB;QACF;;QAEA;QACA,IAAI,CAACsQ,cAAc,EAAE;UACnB,OAAO,IAAI;QACb;;QAEA;QACA;QACA,OACE,CAAC,IAAI,CAAC/E,KAAK,IAAI,IAAI,CAACD,IAAI,IAAI,IAAI,CAACP,MAAM,IAAIuD,OAAO,CAACrS,MAAM,KAAK,IAAI,CAACsP,KAAK;MAE5E,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACrI,OAAO,CAACmJ,OAAO,EAAE;MACpB,OAAOiC,OAAO;IAChB;IAEA,IAAI,IAAI,CAACvD,MAAM,EAAE;MACfuD,OAAO,CAAC7E,IAAI,CAAC,IAAI,CAACsB,MAAM,CAACkG,aAAa,CAAC;QAAEpD;MAAU,CAAC,CAAC,CAAC;IACxD;;IAEA;IACA;IACA,IAAI,CAACyC,cAAc,IAAK,CAAC,IAAI,CAAC/E,KAAK,IAAI,CAAC,IAAI,CAACD,IAAK,EAAE;MAClD,OAAOgD,OAAO;IAChB;IAEA,OAAOA,OAAO,CAACtH,KAAK,CAClB,IAAI,CAACsE,IAAI,EACT,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACD,IAAI,GAAGgD,OAAO,CAACrS,MAChD,CAAC;EACH;EAEAiV,cAAcA,CAACC,YAAY,EAAE;IAC3B;IACA,IAAI,CAACC,OAAO,CAACC,KAAK,EAAE;MAClB,MAAM,IAAInV,KAAK,CACb,2DACF,CAAC;IACH;IAEA,IAAI,CAAC,IAAI,CAAC4O,UAAU,CAACjD,IAAI,EAAE;MACzB,MAAM,IAAI3L,KAAK,CACb,0DACF,CAAC;IACH;IAEA,OAAOkV,OAAO,CAACC,KAAK,CAACC,KAAK,CAACC,UAAU,CAACL,cAAc,CAClD,IAAI,EACJC,YAAY,EACZ,IAAI,CAACrG,UAAU,CAACjD,IAClB,CAAC;EACH;AACF;AAEA;AACA2C,oBAAoB,CAAC/F,OAAO,CAACiG,MAAM,IAAI;EACrC,MAAM8G,SAAS,GAAGlH,kBAAkB,CAACI,MAAM,CAAC;EAC5CE,MAAM,CAACxP,SAAS,CAACoW,SAAS,CAAC,GAAG,YAAkB;IAC9C,IAAI;MAAA,SAAAC,IAAA,GAAAtO,SAAA,CAAAlH,MAAA,EADoCyS,IAAI,OAAA3S,KAAA,CAAA0V,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;QAAJhD,IAAI,CAAAgD,IAAA,IAAAvO,SAAA,CAAAuO,IAAA;MAAA;MAE1C,OAAOxE,OAAO,CAACC,OAAO,CAAC,IAAI,CAACzC,MAAM,CAAC,CAACmE,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC,OAAO9G,KAAK,EAAE;MACd,OAAOsF,OAAO,CAACyE,MAAM,CAAC/J,KAAK,CAAC;IAC9B;EACF,CAAC;AACH,CAAC,CAAC,C;;;;;;;;;;;AC5jBF,IAAIgK,aAAa;AAAC7X,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAAC0W,aAAa,GAAC1W,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGnB,MAAM,CAACE,MAAM,CAAC;EAACgB,OAAO,EAACA,CAAA,KAAID;AAAe,CAAC,CAAC;AAAC,IAAI4P,MAAM;AAAC7Q,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAAC0P,MAAM,GAAC1P,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAImU,aAAa;AAACtV,MAAM,CAACC,IAAI,CAAC,qBAAqB,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAACmU,aAAa,GAACnU,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIhB,MAAM,EAACK,WAAW,EAACC,YAAY,EAACC,gBAAgB,EAACI,+BAA+B,EAACC,iBAAiB;AAACf,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACE,MAAMA,CAACgB,CAAC,EAAC;IAAChB,MAAM,GAACgB,CAAC;EAAA,CAAC;EAACX,WAAWA,CAACW,CAAC,EAAC;IAACX,WAAW,GAACW,CAAC;EAAA,CAAC;EAACV,YAAYA,CAACU,CAAC,EAAC;IAACV,YAAY,GAACU,CAAC;EAAA,CAAC;EAACT,gBAAgBA,CAACS,CAAC,EAAC;IAACT,gBAAgB,GAACS,CAAC;EAAA,CAAC;EAACL,+BAA+BA,CAACK,CAAC,EAAC;IAACL,+BAA+B,GAACK,CAAC;EAAA,CAAC;EAACJ,iBAAiBA,CAACI,CAAC,EAAC;IAACJ,iBAAiB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIoP,kBAAkB;AAACvQ,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACsQ,kBAAkBA,CAACpP,CAAC,EAAC;IAACoP,kBAAkB,GAACpP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAgBnoB,MAAMF,eAAe,CAAC;EACnC6P,WAAWA,CAAChD,IAAI,EAAE;IAChB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC2I,KAAK,GAAG,IAAIxV,eAAe,CAAC8S,MAAM,CAAD,CAAC;IAEvC,IAAI,CAACa,aAAa,GAAGiC,MAAM,CAACiB,QAAQ,GAChC,IAAIjB,MAAM,CAACkB,iBAAiB,CAAC,CAAC,GAC9B,IAAIlB,MAAM,CAACmB,kBAAkB,CAAC,CAAC;IAEnC,IAAI,CAAC3D,QAAQ,GAAG,CAAC,CAAC,CAAC;;IAEnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,GAAGlT,MAAM,CAAC6W,MAAM,CAAC,IAAI,CAAC;;IAElC;IACA;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;;IAE3B;IACA,IAAI,CAAC1D,MAAM,GAAG,KAAK;EACrB;EAEA2D,cAAcA,CAACtN,QAAQ,EAAE1B,OAAO,EAAE;IAChC,OAAO,IAAI,CAACiP,IAAI,CAACvN,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,CAAC,CAAC,EAAE1B,OAAO,CAAC,CAACkP,UAAU,CAAC,CAAC;EACxD;EAEAC,sBAAsBA,CAACnP,OAAO,EAAE;IAC9B,OAAO,IAAI,CAACiP,IAAI,CAAC,CAAC,CAAC,EAAEjP,OAAO,CAAC,CAACkP,UAAU,CAAC,CAAC;EAC5C;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAD,IAAIA,CAACvN,QAAQ,EAAE1B,OAAO,EAAE;IACtB;IACA;IACA;IACA,IAAIC,SAAS,CAAClH,MAAM,KAAK,CAAC,EAAE;MAC1B2I,QAAQ,GAAG,CAAC,CAAC;IACf;IAEA,OAAO,IAAI5J,eAAe,CAAC4P,MAAM,CAAC,IAAI,EAAEhG,QAAQ,EAAE1B,OAAO,CAAC;EAC5D;EAEAoP,OAAOA,CAAC1N,QAAQ,EAAgB;IAAA,IAAd1B,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IAC5B,IAAIA,SAAS,CAAClH,MAAM,KAAK,CAAC,EAAE;MAC1B2I,QAAQ,GAAG,CAAC,CAAC;IACf;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA1B,OAAO,CAACqI,KAAK,GAAG,CAAC;IAEjB,OAAO,IAAI,CAAC4G,IAAI,CAACvN,QAAQ,EAAE1B,OAAO,CAAC,CAACoJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;EACA,MAAMiG,YAAYA,CAAC3N,QAAQ,EAAgB;IAAA,IAAd1B,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IACvC,IAAIA,SAAS,CAAClH,MAAM,KAAK,CAAC,EAAE;MAC1B2I,QAAQ,GAAG,CAAC,CAAC;IACf;IACA1B,OAAO,CAACqI,KAAK,GAAG,CAAC;IACjB,OAAO,CAAC,MAAM,IAAI,CAAC4G,IAAI,CAACvN,QAAQ,EAAE1B,OAAO,CAAC,CAACsP,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7D;EACAC,aAAaA,CAACzS,GAAG,EAAE;IACjB0S,wBAAwB,CAAC1S,GAAG,CAAC;;IAE7B;IACA;IACA,IAAI,CAAC9F,MAAM,CAACiD,IAAI,CAAC6C,GAAG,EAAE,KAAK,CAAC,EAAE;MAC5BA,GAAG,CAAC0J,GAAG,GAAG1O,eAAe,CAAC2X,OAAO,GAAG,IAAIC,OAAO,CAACC,QAAQ,CAAC,CAAC,GAAGC,MAAM,CAAChC,EAAE,CAAC,CAAC;IAC1E;IAEA,MAAMA,EAAE,GAAG9Q,GAAG,CAAC0J,GAAG;IAElB,IAAI,IAAI,CAAC8G,KAAK,CAACuC,GAAG,CAACjC,EAAE,CAAC,EAAE;MACtB,MAAMrJ,cAAc,mBAAArK,MAAA,CAAmB0T,EAAE,MAAG,CAAC;IAC/C;IAEA,IAAI,CAACkC,aAAa,CAAClC,EAAE,EAAEnU,SAAS,CAAC;IACjC,IAAI,CAAC6T,KAAK,CAACE,GAAG,CAACI,EAAE,EAAE9Q,GAAG,CAAC;IAEvB,OAAO8Q,EAAE;EACX;;EAEA;EACA;EACAmC,MAAMA,CAACjT,GAAG,EAAEoN,QAAQ,EAAE;IACpBpN,GAAG,GAAGkF,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;IACtB,MAAM8Q,EAAE,GAAG,IAAI,CAAC2B,aAAa,CAACzS,GAAG,CAAC;IAClC,MAAMkT,kBAAkB,GAAG,EAAE;;IAE7B;IACA,KAAK,MAAM/E,GAAG,IAAIhT,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,EAAE;MAC3C,MAAMhF,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,CAAC2E,KAAK,EAAE;QACf;MACF;MAEA,MAAM+C,WAAW,GAAG1H,KAAK,CAACxM,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC;MAEtD,IAAI+Q,WAAW,CAACtR,MAAM,EAAE;QACtB,IAAI4J,KAAK,CAACwE,SAAS,IAAIkD,WAAW,CAAClP,QAAQ,KAAKlF,SAAS,EAAE;UACzD0M,KAAK,CAACwE,SAAS,CAAC6C,GAAG,CAACI,EAAE,EAAEC,WAAW,CAAClP,QAAQ,CAAC;QAC/C;QAEA,IAAIwH,KAAK,CAAC0E,MAAM,CAACzC,IAAI,IAAIjC,KAAK,CAAC0E,MAAM,CAACxC,KAAK,EAAE;UAC3C2H,kBAAkB,CAACvO,IAAI,CAACwJ,GAAG,CAAC;QAC9B,CAAC,MAAM;UACLnT,eAAe,CAACmY,oBAAoB,CAAC9J,KAAK,EAAErJ,GAAG,CAAC;QAClD;MACF;IACF;IAEAkT,kBAAkB,CAACzO,OAAO,CAAC0J,GAAG,IAAI;MAChC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,EAAE;QACrB,IAAI,CAACiF,iBAAiB,CAAC,IAAI,CAAC/E,OAAO,CAACF,GAAG,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IAEF,IAAI,CAACQ,aAAa,CAACiB,KAAK,CAAC,CAAC;IAC1B,IAAIxC,QAAQ,EAAE;MACZwD,MAAM,CAACyC,KAAK,CAAC,MAAM;QACjBjG,QAAQ,CAAC,IAAI,EAAE0D,EAAE,CAAC;MACpB,CAAC,CAAC;IACJ;IAEA,OAAOA,EAAE;EACX;EACA,MAAMwC,WAAWA,CAACtT,GAAG,EAAEoN,QAAQ,EAAE;IAC/BpN,GAAG,GAAGkF,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;IACtB,MAAM8Q,EAAE,GAAG,IAAI,CAAC2B,aAAa,CAACzS,GAAG,CAAC;IAClC,MAAMkT,kBAAkB,GAAG,EAAE;;IAE7B;IACA,KAAK,MAAM/E,GAAG,IAAIhT,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,EAAE;MAC3C,MAAMhF,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,CAAC2E,KAAK,EAAE;QACf;MACF;MAEA,MAAM+C,WAAW,GAAG1H,KAAK,CAACxM,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC;MAEtD,IAAI+Q,WAAW,CAACtR,MAAM,EAAE;QACtB,IAAI4J,KAAK,CAACwE,SAAS,IAAIkD,WAAW,CAAClP,QAAQ,KAAKlF,SAAS,EAAE;UACzD0M,KAAK,CAACwE,SAAS,CAAC6C,GAAG,CAACI,EAAE,EAAEC,WAAW,CAAClP,QAAQ,CAAC;QAC/C;QAEA,IAAIwH,KAAK,CAAC0E,MAAM,CAACzC,IAAI,IAAIjC,KAAK,CAAC0E,MAAM,CAACxC,KAAK,EAAE;UAC3C2H,kBAAkB,CAACvO,IAAI,CAACwJ,GAAG,CAAC;QAC9B,CAAC,MAAM;UACL,MAAMnT,eAAe,CAACuY,qBAAqB,CAAClK,KAAK,EAAErJ,GAAG,CAAC;QACzD;MACF;IACF;IAEAkT,kBAAkB,CAACzO,OAAO,CAAC0J,GAAG,IAAI;MAChC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,EAAE;QACrB,IAAI,CAACiF,iBAAiB,CAAC,IAAI,CAAC/E,OAAO,CAACF,GAAG,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IAEF,MAAM,IAAI,CAACQ,aAAa,CAACiB,KAAK,CAAC,CAAC;IAChC,IAAIxC,QAAQ,EAAE;MACZwD,MAAM,CAACyC,KAAK,CAAC,MAAM;QACjBjG,QAAQ,CAAC,IAAI,EAAE0D,EAAE,CAAC;MACpB,CAAC,CAAC;IACJ;IAEA,OAAOA,EAAE;EACX;;EAEA;EACA;EACA0C,cAAcA,CAAA,EAAG;IACf;IACA,IAAI,IAAI,CAACjF,MAAM,EAAE;MACf;IACF;;IAEA;IACA,IAAI,CAACA,MAAM,GAAG,IAAI;;IAElB;IACApT,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,CAAC5J,OAAO,CAAC0J,GAAG,IAAI;MACvC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAC/B9E,KAAK,CAAC6E,eAAe,GAAGhJ,KAAK,CAACgK,KAAK,CAAC7F,KAAK,CAACiF,OAAO,CAAC;IACpD,CAAC,CAAC;EACJ;EAEAmF,kBAAkBA,CAACrG,QAAQ,EAAE;IAC3B,MAAM3N,MAAM,GAAG,IAAI,CAAC+Q,KAAK,CAACrB,IAAI,CAAC,CAAC;IAEhC,IAAI,CAACqB,KAAK,CAACG,KAAK,CAAC,CAAC;IAElBxV,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,CAAC5J,OAAO,CAAC0J,GAAG,IAAI;MACvC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,CAACgD,OAAO,EAAE;QACjBhD,KAAK,CAACiF,OAAO,GAAG,EAAE;MACpB,CAAC,MAAM;QACLjF,KAAK,CAACiF,OAAO,CAACqC,KAAK,CAAC,CAAC;MACvB;IACF,CAAC,CAAC;IAEF,IAAIvD,QAAQ,EAAE;MACZwD,MAAM,CAACyC,KAAK,CAAC,MAAM;QACjBjG,QAAQ,CAAC,IAAI,EAAE3N,MAAM,CAAC;MACxB,CAAC,CAAC;IACJ;IAEA,OAAOA,MAAM;EACf;EAGAiU,aAAaA,CAAC9O,QAAQ,EAAE;IACtB,MAAM/H,OAAO,GAAG,IAAImO,SAAS,CAACC,OAAO,CAACrG,QAAQ,CAAC;IAC/C,MAAM+O,MAAM,GAAG,EAAE;IAEjB,IAAI,CAACC,4BAA4B,CAAChP,QAAQ,EAAE,CAAC5E,GAAG,EAAE8Q,EAAE,KAAK;MACvD,IAAIjU,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC,CAACP,MAAM,EAAE;QACvCkU,MAAM,CAAChP,IAAI,CAACmM,EAAE,CAAC;MACjB;IACF,CAAC,CAAC;IAEF,MAAMoC,kBAAkB,GAAG,EAAE;IAC7B,MAAMW,WAAW,GAAG,EAAE;IAEtB,KAAK,IAAI/V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6V,MAAM,CAAC1X,MAAM,EAAE6B,CAAC,EAAE,EAAE;MACtC,MAAMgW,QAAQ,GAAGH,MAAM,CAAC7V,CAAC,CAAC;MAC1B,MAAMiW,SAAS,GAAG,IAAI,CAACvD,KAAK,CAACC,GAAG,CAACqD,QAAQ,CAAC;MAE1C3Y,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,CAAC5J,OAAO,CAAC0J,GAAG,IAAI;QACvC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI9E,KAAK,CAAC2E,KAAK,EAAE;UACf;QACF;QAEA,IAAI3E,KAAK,CAACxM,OAAO,CAACmU,eAAe,CAAC+C,SAAS,CAAC,CAACtU,MAAM,EAAE;UACnD,IAAI4J,KAAK,CAAC0E,MAAM,CAACzC,IAAI,IAAIjC,KAAK,CAAC0E,MAAM,CAACxC,KAAK,EAAE;YAC3C2H,kBAAkB,CAACvO,IAAI,CAACwJ,GAAG,CAAC;UAC9B,CAAC,MAAM;YACL0F,WAAW,CAAClP,IAAI,CAAC;cAACwJ,GAAG;cAAEnO,GAAG,EAAE+T;YAAS,CAAC,CAAC;UACzC;QACF;MACF,CAAC,CAAC;MAEF,IAAI,CAACf,aAAa,CAACc,QAAQ,EAAEC,SAAS,CAAC;MACvC,IAAI,CAACvD,KAAK,CAACmD,MAAM,CAACG,QAAQ,CAAC;IAC7B;IAEA,OAAO;MAAEZ,kBAAkB;MAAEW,WAAW;MAAEF;IAAO,CAAC;EACpD;EAEAA,MAAMA,CAAC/O,QAAQ,EAAEwI,QAAQ,EAAE;IACzB;IACA;IACA;IACA,IAAI,IAAI,CAACmB,MAAM,IAAI,CAAC,IAAI,CAAC0D,eAAe,IAAI/M,KAAK,CAAC8O,MAAM,CAACpP,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;MACtE,OAAO,IAAI,CAAC6O,kBAAkB,CAACrG,QAAQ,CAAC;IAC1C;IAEA,MAAM;MAAE8F,kBAAkB;MAAEW,WAAW;MAAEF;IAAO,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC9O,QAAQ,CAAC;;IAEhF;IACAiP,WAAW,CAACpP,OAAO,CAACkP,MAAM,IAAI;MAC5B,MAAMtK,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACsF,MAAM,CAACxF,GAAG,CAAC;MAEtC,IAAI9E,KAAK,EAAE;QACTA,KAAK,CAACwE,SAAS,IAAIxE,KAAK,CAACwE,SAAS,CAAC8F,MAAM,CAACA,MAAM,CAAC3T,GAAG,CAAC0J,GAAG,CAAC;QACzD1O,eAAe,CAACiZ,sBAAsB,CAAC5K,KAAK,EAAEsK,MAAM,CAAC3T,GAAG,CAAC;MAC3D;IACF,CAAC,CAAC;IAEFkT,kBAAkB,CAACzO,OAAO,CAAC0J,GAAG,IAAI;MAChC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,EAAE;QACT,IAAI,CAAC+J,iBAAiB,CAAC/J,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF,IAAI,CAACsF,aAAa,CAACiB,KAAK,CAAC,CAAC;IAE1B,MAAMnQ,MAAM,GAAGkU,MAAM,CAAC1X,MAAM;IAE5B,IAAImR,QAAQ,EAAE;MACZwD,MAAM,CAACyC,KAAK,CAAC,MAAM;QACjBjG,QAAQ,CAAC,IAAI,EAAE3N,MAAM,CAAC;MACxB,CAAC,CAAC;IACJ;IAEA,OAAOA,MAAM;EACf;EAEA,MAAMyU,WAAWA,CAACtP,QAAQ,EAAEwI,QAAQ,EAAE;IACpC;IACA;IACA;IACA,IAAI,IAAI,CAACmB,MAAM,IAAI,CAAC,IAAI,CAAC0D,eAAe,IAAI/M,KAAK,CAAC8O,MAAM,CAACpP,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;MACtE,OAAO,IAAI,CAAC6O,kBAAkB,CAACrG,QAAQ,CAAC;IAC1C;IAEA,MAAM;MAAE8F,kBAAkB;MAAEW,WAAW;MAAEF;IAAO,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC9O,QAAQ,CAAC;;IAEhF;IACA,KAAK,MAAM+O,MAAM,IAAIE,WAAW,EAAE;MAChC,MAAMxK,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACsF,MAAM,CAACxF,GAAG,CAAC;MAEtC,IAAI9E,KAAK,EAAE;QACTA,KAAK,CAACwE,SAAS,IAAIxE,KAAK,CAACwE,SAAS,CAAC8F,MAAM,CAACA,MAAM,CAAC3T,GAAG,CAAC0J,GAAG,CAAC;QACzD,MAAM1O,eAAe,CAACmZ,uBAAuB,CAAC9K,KAAK,EAAEsK,MAAM,CAAC3T,GAAG,CAAC;MAClE;IACF;IACAkT,kBAAkB,CAACzO,OAAO,CAAC0J,GAAG,IAAI;MAChC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,EAAE;QACT,IAAI,CAAC+J,iBAAiB,CAAC/J,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF,MAAM,IAAI,CAACsF,aAAa,CAACiB,KAAK,CAAC,CAAC;IAEhC,MAAMnQ,MAAM,GAAGkU,MAAM,CAAC1X,MAAM;IAE5B,IAAImR,QAAQ,EAAE;MACZwD,MAAM,CAACyC,KAAK,CAAC,MAAM;QACjBjG,QAAQ,CAAC,IAAI,EAAE3N,MAAM,CAAC;MACxB,CAAC,CAAC;IACJ;IAEA,OAAOA,MAAM;EACf;;EAEA;EACA;EACA;EACA;EACA2U,gBAAgBA,CAAA,EAAG;IACjB;IACA,IAAI,CAAC,IAAI,CAAC7F,MAAM,EAAE;MAChB;IACF;;IAEA;IACA;IACA,IAAI,CAACA,MAAM,GAAG,KAAK;IAEnBpT,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,CAAC5J,OAAO,CAAC0J,GAAG,IAAI;MACvC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,CAAC2E,KAAK,EAAE;QACf3E,KAAK,CAAC2E,KAAK,GAAG,KAAK;;QAEnB;QACA;QACA,IAAI,CAACoF,iBAAiB,CAAC/J,KAAK,EAAEA,KAAK,CAAC6E,eAAe,CAAC;MACtD,CAAC,MAAM;QACL;QACA;QACAlT,eAAe,CAACqZ,iBAAiB,CAC/BhL,KAAK,CAACgD,OAAO,EACbhD,KAAK,CAAC6E,eAAe,EACrB7E,KAAK,CAACiF,OAAO,EACbjF,KAAK,EACL;UAAC4E,YAAY,EAAE5E,KAAK,CAAC4E;QAAY,CACnC,CAAC;MACH;MAEA5E,KAAK,CAAC6E,eAAe,GAAG,IAAI;IAC9B,CAAC,CAAC;EACJ;EAEA,MAAMoG,qBAAqBA,CAAA,EAAG;IAC5B,IAAI,CAACF,gBAAgB,CAAC,CAAC;IACvB,MAAM,IAAI,CAACzF,aAAa,CAACiB,KAAK,CAAC,CAAC;EAClC;EACA2E,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAACH,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACzF,aAAa,CAACiB,KAAK,CAAC,CAAC;EAC5B;EAEA4E,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACvC,eAAe,EAAE;MACzB,MAAM,IAAI/V,KAAK,CAAC,gDAAgD,CAAC;IACnE;IAEA,MAAMuY,SAAS,GAAG,IAAI,CAACxC,eAAe;IAEtC,IAAI,CAACA,eAAe,GAAG,IAAI;IAE3B,OAAOwC,SAAS;EAClB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACzC,eAAe,EAAE;MACxB,MAAM,IAAI/V,KAAK,CAAC,sDAAsD,CAAC;IACzE;IAEA,IAAI,CAAC+V,eAAe,GAAG,IAAIjX,eAAe,CAAC8S,MAAM,CAAD,CAAC;EACnD;EAEA6G,aAAaA,CAAC/P,QAAQ,EAAE;IACtB;IACA;IACA;IACA;IACA;IACA,MAAMgQ,oBAAoB,GAAG,CAAC,CAAC;;IAE/B;IACA;IACA,MAAMC,MAAM,GAAG,IAAI7Z,eAAe,CAAC8S,MAAM,CAAD,CAAC;IACzC,MAAMgH,UAAU,GAAG9Z,eAAe,CAAC+Z,qBAAqB,CAACnQ,QAAQ,CAAC;IAElEzJ,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,CAAC5J,OAAO,CAAC0J,GAAG,IAAI;MACvC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI,CAAC9E,KAAK,CAAC0E,MAAM,CAACzC,IAAI,IAAIjC,KAAK,CAAC0E,MAAM,CAACxC,KAAK,KAAK,CAAE,IAAI,CAACgD,MAAM,EAAE;QAC9D;QACA;QACA;QACA;QACA;QACA,IAAIlF,KAAK,CAACiF,OAAO,YAAYtT,eAAe,CAAC8S,MAAM,EAAE;UACnD8G,oBAAoB,CAACzG,GAAG,CAAC,GAAG9E,KAAK,CAACiF,OAAO,CAACY,KAAK,CAAC,CAAC;UACjD;QACF;QAEA,IAAI,EAAE7F,KAAK,CAACiF,OAAO,YAAYvS,KAAK,CAAC,EAAE;UACrC,MAAM,IAAIG,KAAK,CAAC,8CAA8C,CAAC;QACjE;;QAEA;QACA;QACA;QACA;QACA,MAAM8Y,qBAAqB,GAAGhV,GAAG,IAAI;UACnC,IAAI6U,MAAM,CAAC9B,GAAG,CAAC/S,GAAG,CAAC0J,GAAG,CAAC,EAAE;YACvB,OAAOmL,MAAM,CAACpE,GAAG,CAACzQ,GAAG,CAAC0J,GAAG,CAAC;UAC5B;UAEA,MAAMuL,YAAY,GAChBH,UAAU,IACV,CAACA,UAAU,CAAClY,IAAI,CAACkU,EAAE,IAAI5L,KAAK,CAAC8O,MAAM,CAAClD,EAAE,EAAE9Q,GAAG,CAAC0J,GAAG,CAAC,CAAC,GAC/C1J,GAAG,GAAGkF,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;UAE1B6U,MAAM,CAACnE,GAAG,CAAC1Q,GAAG,CAAC0J,GAAG,EAAEuL,YAAY,CAAC;UAEjC,OAAOA,YAAY;QACrB,CAAC;QAEDL,oBAAoB,CAACzG,GAAG,CAAC,GAAG9E,KAAK,CAACiF,OAAO,CAAC9R,GAAG,CAACwY,qBAAqB,CAAC;MACtE;IACF,CAAC,CAAC;IAEF,OAAOJ,oBAAoB;EAC7B;EAEAM,YAAYA,CAAAC,IAAA,EAAiD;IAAA,IAAhD;MAAEjS,OAAO;MAAEkS,WAAW;MAAEhI,QAAQ;MAAEiI;IAAW,CAAC,GAAAF,IAAA;IAGzD;IACA;IACA;IACA,IAAI1V,MAAM;IACV,IAAIyD,OAAO,CAACoS,aAAa,EAAE;MACzB7V,MAAM,GAAG;QAAE8V,cAAc,EAAEH;MAAY,CAAC;MAExC,IAAIC,UAAU,KAAK1Y,SAAS,EAAE;QAC5B8C,MAAM,CAAC4V,UAAU,GAAGA,UAAU;MAChC;IACF,CAAC,MAAM;MACL5V,MAAM,GAAG2V,WAAW;IACtB;IAEA,IAAIhI,QAAQ,EAAE;MACZwD,MAAM,CAACyC,KAAK,CAAC,MAAM;QACjBjG,QAAQ,CAAC,IAAI,EAAE3N,MAAM,CAAC;MACxB,CAAC,CAAC;IACJ;IAEA,OAAOA,MAAM;EACf;;EAEA;EACA;EACA,MAAM+V,WAAWA,CAAC5Q,QAAQ,EAAE6Q,GAAG,EAAEvS,OAAO,EAAEkK,QAAQ,EAAE;IAClD,IAAI,CAAEA,QAAQ,IAAIlK,OAAO,YAAY3C,QAAQ,EAAE;MAC7C6M,QAAQ,GAAGlK,OAAO;MAClBA,OAAO,GAAG,IAAI;IAChB;IAEA,IAAI,CAACA,OAAO,EAAE;MACZA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMrG,OAAO,GAAG,IAAImO,SAAS,CAACC,OAAO,CAACrG,QAAQ,EAAE,IAAI,CAAC;IAErD,MAAMgQ,oBAAoB,GAAG,IAAI,CAACD,aAAa,CAAC/P,QAAQ,CAAC;IAEzD,IAAI8Q,aAAa,GAAG,CAAC,CAAC;IAEtB,IAAIN,WAAW,GAAG,CAAC;IAEnB,MAAM,IAAI,CAACO,6BAA6B,CAAC/Q,QAAQ,EAAE,OAAO5E,GAAG,EAAE8Q,EAAE,KAAK;MACpE,MAAM8E,WAAW,GAAG/Y,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC;MAEhD,IAAI4V,WAAW,CAACnW,MAAM,EAAE;QACtB;QACA,IAAI,CAACuT,aAAa,CAAClC,EAAE,EAAE9Q,GAAG,CAAC;QAC3B0V,aAAa,GAAG,MAAM,IAAI,CAACG,qBAAqB,CAC9C7V,GAAG,EACHyV,GAAG,EACHG,WAAW,CAAClT,YACd,CAAC;QAED,EAAE0S,WAAW;QAEb,IAAI,CAAClS,OAAO,CAAC4S,KAAK,EAAE;UAClB,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF3a,MAAM,CAACwD,IAAI,CAAC+W,aAAa,CAAC,CAACjR,OAAO,CAAC0J,GAAG,IAAI;MACxC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,EAAE;QACT,IAAI,CAAC+J,iBAAiB,CAAC/J,KAAK,EAAEuL,oBAAoB,CAACzG,GAAG,CAAC,CAAC;MAC1D;IACF,CAAC,CAAC;IAEF,MAAM,IAAI,CAACQ,aAAa,CAACiB,KAAK,CAAC,CAAC;;IAEhC;IACA;IACA;IACA,IAAIyF,UAAU;IACd,IAAID,WAAW,KAAK,CAAC,IAAIlS,OAAO,CAAC6S,MAAM,EAAE;MACvC,MAAM/V,GAAG,GAAGhF,eAAe,CAACgb,qBAAqB,CAACpR,QAAQ,EAAE6Q,GAAG,CAAC;MAChE,IAAI,CAACzV,GAAG,CAAC0J,GAAG,IAAIxG,OAAO,CAACmS,UAAU,EAAE;QAClCrV,GAAG,CAAC0J,GAAG,GAAGxG,OAAO,CAACmS,UAAU;MAC9B;MAEAA,UAAU,GAAG,MAAM,IAAI,CAAC/B,WAAW,CAACtT,GAAG,CAAC;MACxCoV,WAAW,GAAG,CAAC;IACjB;IAEA,OAAO,IAAI,CAACF,YAAY,CAAC;MACvBhS,OAAO;MACPmS,UAAU;MACVD,WAAW;MACXhI;IACF,CAAC,CAAC;EACJ;EACA;EACA;EACA6I,MAAMA,CAACrR,QAAQ,EAAE6Q,GAAG,EAAEvS,OAAO,EAAEkK,QAAQ,EAAE;IACvC,IAAI,CAAEA,QAAQ,IAAIlK,OAAO,YAAY3C,QAAQ,EAAE;MAC7C6M,QAAQ,GAAGlK,OAAO;MAClBA,OAAO,GAAG,IAAI;IAChB;IAEA,IAAI,CAACA,OAAO,EAAE;MACZA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMrG,OAAO,GAAG,IAAImO,SAAS,CAACC,OAAO,CAACrG,QAAQ,EAAE,IAAI,CAAC;IAErD,MAAMgQ,oBAAoB,GAAG,IAAI,CAACD,aAAa,CAAC/P,QAAQ,CAAC;IAEzD,IAAI8Q,aAAa,GAAG,CAAC,CAAC;IAEtB,IAAIN,WAAW,GAAG,CAAC;IAEnB,IAAI,CAACxB,4BAA4B,CAAChP,QAAQ,EAAE,CAAC5E,GAAG,EAAE8Q,EAAE,KAAK;MACvD,MAAM8E,WAAW,GAAG/Y,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC;MAEhD,IAAI4V,WAAW,CAACnW,MAAM,EAAE;QACtB;QACA,IAAI,CAACuT,aAAa,CAAClC,EAAE,EAAE9Q,GAAG,CAAC;QAC3B0V,aAAa,GAAG,IAAI,CAACQ,oBAAoB,CACvClW,GAAG,EACHyV,GAAG,EACHG,WAAW,CAAClT,YACd,CAAC;QAED,EAAE0S,WAAW;QAEb,IAAI,CAAClS,OAAO,CAAC4S,KAAK,EAAE;UAClB,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF3a,MAAM,CAACwD,IAAI,CAAC+W,aAAa,CAAC,CAACjR,OAAO,CAAC0J,GAAG,IAAI;MACxC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAC/B,IAAI9E,KAAK,EAAE;QACT,IAAI,CAAC+J,iBAAiB,CAAC/J,KAAK,EAAEuL,oBAAoB,CAACzG,GAAG,CAAC,CAAC;MAC1D;IACF,CAAC,CAAC;IAEF,IAAI,CAACQ,aAAa,CAACiB,KAAK,CAAC,CAAC;;IAG1B;IACA;IACA;IACA,IAAIyF,UAAU;IACd,IAAID,WAAW,KAAK,CAAC,IAAIlS,OAAO,CAAC6S,MAAM,EAAE;MACvC,MAAM/V,GAAG,GAAGhF,eAAe,CAACgb,qBAAqB,CAACpR,QAAQ,EAAE6Q,GAAG,CAAC;MAChE,IAAI,CAACzV,GAAG,CAAC0J,GAAG,IAAIxG,OAAO,CAACmS,UAAU,EAAE;QAClCrV,GAAG,CAAC0J,GAAG,GAAGxG,OAAO,CAACmS,UAAU;MAC9B;MAEAA,UAAU,GAAG,IAAI,CAACpC,MAAM,CAACjT,GAAG,CAAC;MAC7BoV,WAAW,GAAG,CAAC;IACjB;IAGA,OAAO,IAAI,CAACF,YAAY,CAAC;MACvBhS,OAAO;MACPkS,WAAW;MACXhI,QAAQ;MACRxI,QAAQ;MACR6Q;IACF,CAAC,CAAC;EACJ;;EAEA;EACA;EACA;EACAM,MAAMA,CAACnR,QAAQ,EAAE6Q,GAAG,EAAEvS,OAAO,EAAEkK,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,IAAI,OAAOlK,OAAO,KAAK,UAAU,EAAE;MAC9CkK,QAAQ,GAAGlK,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,OAAO,IAAI,CAAC+S,MAAM,CAChBrR,QAAQ,EACR6Q,GAAG,EACHta,MAAM,CAAC+D,MAAM,CAAC,CAAC,CAAC,EAAEgE,OAAO,EAAE;MAAC6S,MAAM,EAAE,IAAI;MAAET,aAAa,EAAE;IAAI,CAAC,CAAC,EAC/DlI,QACF,CAAC;EACH;EAEA+I,WAAWA,CAACvR,QAAQ,EAAE6Q,GAAG,EAAEvS,OAAO,EAAEkK,QAAQ,EAAE;IAC5C,IAAI,CAACA,QAAQ,IAAI,OAAOlK,OAAO,KAAK,UAAU,EAAE;MAC9CkK,QAAQ,GAAGlK,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,OAAO,IAAI,CAACsS,WAAW,CACrB5Q,QAAQ,EACR6Q,GAAG,EACHta,MAAM,CAAC+D,MAAM,CAAC,CAAC,CAAC,EAAEgE,OAAO,EAAE;MAAC6S,MAAM,EAAE,IAAI;MAAET,aAAa,EAAE;IAAI,CAAC,CAAC,EAC/DlI,QACF,CAAC;EACH;;EAEA;EACA;EACA;EACA;EACA,MAAMuI,6BAA6BA,CAAC/Q,QAAQ,EAAE3E,EAAE,EAAE;IAChD,MAAMmW,WAAW,GAAGpb,eAAe,CAAC+Z,qBAAqB,CAACnQ,QAAQ,CAAC;IAEnE,IAAIwR,WAAW,EAAE;MACf,KAAK,MAAMtF,EAAE,IAAIsF,WAAW,EAAE;QAC5B,MAAMpW,GAAG,GAAG,IAAI,CAACwQ,KAAK,CAACC,GAAG,CAACK,EAAE,CAAC;QAE9B,IAAI9Q,GAAG,IAAI,EAAG,MAAMC,EAAE,CAACD,GAAG,EAAE8Q,EAAE,CAAC,CAAC,EAAE;UAChC;QACF;MACF;IACF,CAAC,MAAM;MACL,MAAM,IAAI,CAACN,KAAK,CAAC6F,YAAY,CAACpW,EAAE,CAAC;IACnC;EACF;EACA2T,4BAA4BA,CAAChP,QAAQ,EAAE3E,EAAE,EAAE;IACzC,MAAMmW,WAAW,GAAGpb,eAAe,CAAC+Z,qBAAqB,CAACnQ,QAAQ,CAAC;IAEnE,IAAIwR,WAAW,EAAE;MACf,KAAK,MAAMtF,EAAE,IAAIsF,WAAW,EAAE;QAC5B,MAAMpW,GAAG,GAAG,IAAI,CAACwQ,KAAK,CAACC,GAAG,CAACK,EAAE,CAAC;QAE9B,IAAI9Q,GAAG,IAAI,CAACC,EAAE,CAACD,GAAG,EAAE8Q,EAAE,CAAC,EAAE;UACvB;QACF;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAACN,KAAK,CAAC/L,OAAO,CAACxE,EAAE,CAAC;IACxB;EACF;EAEAqW,uBAAuBA,CAACtW,GAAG,EAAEyV,GAAG,EAAE/S,YAAY,EAAE;IAC9C,MAAM6T,cAAc,GAAG,CAAC,CAAC;IAEzBpb,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,CAAC5J,OAAO,CAAC0J,GAAG,IAAI;MACvC,MAAM9E,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,CAAC2E,KAAK,EAAE;QACf;MACF;MAEA,IAAI3E,KAAK,CAACgD,OAAO,EAAE;QACjBkK,cAAc,CAACpI,GAAG,CAAC,GAAG9E,KAAK,CAACxM,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC,CAACP,MAAM;MACjE,CAAC,MAAM;QACL;QACA;QACA8W,cAAc,CAACpI,GAAG,CAAC,GAAG9E,KAAK,CAACiF,OAAO,CAACyE,GAAG,CAAC/S,GAAG,CAAC0J,GAAG,CAAC;MAClD;IACF,CAAC,CAAC;IAEF,OAAO6M,cAAc;EACvB;EAEAL,oBAAoBA,CAAClW,GAAG,EAAEyV,GAAG,EAAE/S,YAAY,EAAE;IAE3C,MAAM6T,cAAc,GAAG,IAAI,CAACD,uBAAuB,CAACtW,GAAG,EAAEyV,GAAG,EAAE/S,YAAY,CAAC;IAE3E,MAAM8T,OAAO,GAAGtR,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;IAChChF,eAAe,CAACyb,OAAO,CAACzW,GAAG,EAAEyV,GAAG,EAAE;MAAC/S;IAAY,CAAC,CAAC;IAEjD,MAAMgT,aAAa,GAAG,CAAC,CAAC;IAExB,KAAK,MAAMvH,GAAG,IAAIhT,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,EAAE;MAC3C,MAAMhF,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,CAAC2E,KAAK,EAAE;QACf;MACF;MAEA,MAAM0I,UAAU,GAAGrN,KAAK,CAACxM,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC;MACrD,MAAM2W,KAAK,GAAGD,UAAU,CAACjX,MAAM;MAC/B,MAAMmX,MAAM,GAAGL,cAAc,CAACpI,GAAG,CAAC;MAElC,IAAIwI,KAAK,IAAItN,KAAK,CAACwE,SAAS,IAAI6I,UAAU,CAAC7U,QAAQ,KAAKlF,SAAS,EAAE;QACjE0M,KAAK,CAACwE,SAAS,CAAC6C,GAAG,CAAC1Q,GAAG,CAAC0J,GAAG,EAAEgN,UAAU,CAAC7U,QAAQ,CAAC;MACnD;MAEA,IAAIwH,KAAK,CAAC0E,MAAM,CAACzC,IAAI,IAAIjC,KAAK,CAAC0E,MAAM,CAACxC,KAAK,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIqL,MAAM,IAAID,KAAK,EAAE;UACnBjB,aAAa,CAACvH,GAAG,CAAC,GAAG,IAAI;QAC3B;MACF,CAAC,MAAM,IAAIyI,MAAM,IAAI,CAACD,KAAK,EAAE;QAC3B3b,eAAe,CAACiZ,sBAAsB,CAAC5K,KAAK,EAAErJ,GAAG,CAAC;MACpD,CAAC,MAAM,IAAI,CAAC4W,MAAM,IAAID,KAAK,EAAE;QAC3B3b,eAAe,CAACmY,oBAAoB,CAAC9J,KAAK,EAAErJ,GAAG,CAAC;MAClD,CAAC,MAAM,IAAI4W,MAAM,IAAID,KAAK,EAAE;QAC1B3b,eAAe,CAAC6b,oBAAoB,CAACxN,KAAK,EAAErJ,GAAG,EAAEwW,OAAO,CAAC;MAC3D;IACF;IACA,OAAOd,aAAa;EACtB;EAEA,MAAMG,qBAAqBA,CAAC7V,GAAG,EAAEyV,GAAG,EAAE/S,YAAY,EAAE;IAElD,MAAM6T,cAAc,GAAG,IAAI,CAACD,uBAAuB,CAACtW,GAAG,EAAEyV,GAAG,EAAE/S,YAAY,CAAC;IAE3E,MAAM8T,OAAO,GAAGtR,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;IAChChF,eAAe,CAACyb,OAAO,CAACzW,GAAG,EAAEyV,GAAG,EAAE;MAAC/S;IAAY,CAAC,CAAC;IAEjD,MAAMgT,aAAa,GAAG,CAAC,CAAC;IACxB,KAAK,MAAMvH,GAAG,IAAIhT,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC0P,OAAO,CAAC,EAAE;MAC3C,MAAMhF,KAAK,GAAG,IAAI,CAACgF,OAAO,CAACF,GAAG,CAAC;MAE/B,IAAI9E,KAAK,CAAC2E,KAAK,EAAE;QACf;MACF;MAEA,MAAM0I,UAAU,GAAGrN,KAAK,CAACxM,OAAO,CAACmU,eAAe,CAAChR,GAAG,CAAC;MACrD,MAAM2W,KAAK,GAAGD,UAAU,CAACjX,MAAM;MAC/B,MAAMmX,MAAM,GAAGL,cAAc,CAACpI,GAAG,CAAC;MAElC,IAAIwI,KAAK,IAAItN,KAAK,CAACwE,SAAS,IAAI6I,UAAU,CAAC7U,QAAQ,KAAKlF,SAAS,EAAE;QACjE0M,KAAK,CAACwE,SAAS,CAAC6C,GAAG,CAAC1Q,GAAG,CAAC0J,GAAG,EAAEgN,UAAU,CAAC7U,QAAQ,CAAC;MACnD;MAEA,IAAIwH,KAAK,CAAC0E,MAAM,CAACzC,IAAI,IAAIjC,KAAK,CAAC0E,MAAM,CAACxC,KAAK,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIqL,MAAM,IAAID,KAAK,EAAE;UACnBjB,aAAa,CAACvH,GAAG,CAAC,GAAG,IAAI;QAC3B;MACF,CAAC,MAAM,IAAIyI,MAAM,IAAI,CAACD,KAAK,EAAE;QAC3B,MAAM3b,eAAe,CAACmZ,uBAAuB,CAAC9K,KAAK,EAAErJ,GAAG,CAAC;MAC3D,CAAC,MAAM,IAAI,CAAC4W,MAAM,IAAID,KAAK,EAAE;QAC3B,MAAM3b,eAAe,CAACuY,qBAAqB,CAAClK,KAAK,EAAErJ,GAAG,CAAC;MACzD,CAAC,MAAM,IAAI4W,MAAM,IAAID,KAAK,EAAE;QAC1B,MAAM3b,eAAe,CAAC8b,qBAAqB,CAACzN,KAAK,EAAErJ,GAAG,EAAEwW,OAAO,CAAC;MAClE;IACF;IACA,OAAOd,aAAa;EACtB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAtC,iBAAiBA,CAAC/J,KAAK,EAAE0N,UAAU,EAAE;IACnC,IAAI,IAAI,CAACxI,MAAM,EAAE;MACf;MACA;MACA;MACAlF,KAAK,CAAC2E,KAAK,GAAG,IAAI;MAClB;IACF;IAEA,IAAI,CAAC,IAAI,CAACO,MAAM,IAAI,CAACwI,UAAU,EAAE;MAC/BA,UAAU,GAAG1N,KAAK,CAACiF,OAAO;IAC5B;IAEA,IAAIjF,KAAK,CAACwE,SAAS,EAAE;MACnBxE,KAAK,CAACwE,SAAS,CAAC8C,KAAK,CAAC,CAAC;IACzB;IAEAtH,KAAK,CAACiF,OAAO,GAAGjF,KAAK,CAAC0E,MAAM,CAAC3B,cAAc,CAAC;MAC1CyB,SAAS,EAAExE,KAAK,CAACwE,SAAS;MAC1BxB,OAAO,EAAEhD,KAAK,CAACgD;IACjB,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAACkC,MAAM,EAAE;MAChBvT,eAAe,CAACqZ,iBAAiB,CAC/BhL,KAAK,CAACgD,OAAO,EACb0K,UAAU,EACV1N,KAAK,CAACiF,OAAO,EACbjF,KAAK,EACL;QAAC4E,YAAY,EAAE5E,KAAK,CAAC4E;MAAY,CACnC,CAAC;IACH;EACF;EAEA+E,aAAaA,CAAClC,EAAE,EAAE9Q,GAAG,EAAE;IACrB;IACA,IAAI,CAAC,IAAI,CAACiS,eAAe,EAAE;MACzB;IACF;;IAEA;IACA;IACA;IACA,IAAI,IAAI,CAACA,eAAe,CAACc,GAAG,CAACjC,EAAE,CAAC,EAAE;MAChC;IACF;IAEA,IAAI,CAACmB,eAAe,CAACvB,GAAG,CAACI,EAAE,EAAE5L,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC,CAAC;EAChD;AACF;AAEAhF,eAAe,CAAC4P,MAAM,GAAGA,MAAM;AAE/B5P,eAAe,CAACqU,aAAa,GAAGA,aAAa;;AAE7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACArU,eAAe,CAACgc,sBAAsB,GAAG,MAAMA,sBAAsB,CAAC;EACpEnM,WAAWA,CAAA,EAAe;IAAA,IAAd3H,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IACtB,MAAM8T,oBAAoB,GACxB/T,OAAO,CAACgU,SAAS,IACjBlc,eAAe,CAAC2S,kCAAkC,CAACzK,OAAO,CAACgU,SAAS,CACrE;IAED,IAAIhd,MAAM,CAACiD,IAAI,CAAC+F,OAAO,EAAE,SAAS,CAAC,EAAE;MACnC,IAAI,CAACmJ,OAAO,GAAGnJ,OAAO,CAACmJ,OAAO;MAE9B,IAAInJ,OAAO,CAACgU,SAAS,IAAIhU,OAAO,CAACmJ,OAAO,KAAK4K,oBAAoB,EAAE;QACjE,MAAM/a,KAAK,CAAC,yCAAyC,CAAC;MACxD;IACF,CAAC,MAAM,IAAIgH,OAAO,CAACgU,SAAS,EAAE;MAC5B,IAAI,CAAC7K,OAAO,GAAG4K,oBAAoB;IACrC,CAAC,MAAM;MACL,MAAM/a,KAAK,CAAC,mCAAmC,CAAC;IAClD;IAEA,MAAMgb,SAAS,GAAGhU,OAAO,CAACgU,SAAS,IAAI,CAAC,CAAC;IAEzC,IAAI,IAAI,CAAC7K,OAAO,EAAE;MAChB,IAAI,CAAC8K,IAAI,GAAG,IAAIC,WAAW,CAACxE,OAAO,CAACyE,WAAW,CAAC;MAChD,IAAI,CAACC,WAAW,GAAG;QACjB7K,WAAW,EAAEA,CAACqE,EAAE,EAAEvH,MAAM,EAAEqN,MAAM,KAAK;UACnC;UACA,MAAM5W,GAAG,GAAA4R,aAAA,KAAQrI,MAAM,CAAE;UAEzBvJ,GAAG,CAAC0J,GAAG,GAAGoH,EAAE;UAEZ,IAAIoG,SAAS,CAACzK,WAAW,EAAE;YACzByK,SAAS,CAACzK,WAAW,CAACtP,IAAI,CAAC,IAAI,EAAE2T,EAAE,EAAE5L,KAAK,CAACgK,KAAK,CAAC3F,MAAM,CAAC,EAAEqN,MAAM,CAAC;UACnE;;UAEA;UACA,IAAIM,SAAS,CAAChL,KAAK,EAAE;YACnBgL,SAAS,CAAChL,KAAK,CAAC/O,IAAI,CAAC,IAAI,EAAE2T,EAAE,EAAE5L,KAAK,CAACgK,KAAK,CAAC3F,MAAM,CAAC,CAAC;UACrD;;UAEA;UACA;UACA;UACA,IAAI,CAAC4N,IAAI,CAACI,SAAS,CAACzG,EAAE,EAAE9Q,GAAG,EAAE4W,MAAM,IAAI,IAAI,CAAC;QAC9C,CAAC;QACDjK,WAAW,EAAEA,CAACmE,EAAE,EAAE8F,MAAM,KAAK;UAC3B,IAAIM,SAAS,CAACvK,WAAW,EAAE;YACzBuK,SAAS,CAACvK,WAAW,CAACxP,IAAI,CAAC,IAAI,EAAE2T,EAAE,EAAE8F,MAAM,CAAC;UAC9C;UAEA,IAAI,CAACO,IAAI,CAACK,UAAU,CAAC1G,EAAE,EAAE8F,MAAM,IAAI,IAAI,CAAC;QAC1C;MACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACO,IAAI,GAAG,IAAInc,eAAe,CAAC8S,MAAM,CAAD,CAAC;MACtC,IAAI,CAACwJ,WAAW,GAAG;QACjBpL,KAAK,EAAEA,CAAC4E,EAAE,EAAEvH,MAAM,KAAK;UACrB;UACA,MAAMvJ,GAAG,GAAA4R,aAAA,KAAQrI,MAAM,CAAE;UAEzB,IAAI2N,SAAS,CAAChL,KAAK,EAAE;YACnBgL,SAAS,CAAChL,KAAK,CAAC/O,IAAI,CAAC,IAAI,EAAE2T,EAAE,EAAE5L,KAAK,CAACgK,KAAK,CAAC3F,MAAM,CAAC,CAAC;UACrD;UAEAvJ,GAAG,CAAC0J,GAAG,GAAGoH,EAAE;UAEZ,IAAI,CAACqG,IAAI,CAACzG,GAAG,CAACI,EAAE,EAAG9Q,GAAG,CAAC;QACzB;MACF,CAAC;IACH;;IAEA;IACA;IACA,IAAI,CAACsX,WAAW,CAAC5K,OAAO,GAAG,CAACoE,EAAE,EAAEvH,MAAM,KAAK;MACzC,MAAMvJ,GAAG,GAAG,IAAI,CAACmX,IAAI,CAAC1G,GAAG,CAACK,EAAE,CAAC;MAE7B,IAAI,CAAC9Q,GAAG,EAAE;QACR,MAAM,IAAI9D,KAAK,4BAAAkB,MAAA,CAA4B0T,EAAE,CAAE,CAAC;MAClD;MAEA,IAAIoG,SAAS,CAACxK,OAAO,EAAE;QACrBwK,SAAS,CAACxK,OAAO,CAACvP,IAAI,CAAC,IAAI,EAAE2T,EAAE,EAAE5L,KAAK,CAACgK,KAAK,CAAC3F,MAAM,CAAC,CAAC;MACvD;MAEAkO,YAAY,CAACC,YAAY,CAAC1X,GAAG,EAAEuJ,MAAM,CAAC;IACxC,CAAC;IAED,IAAI,CAAC+N,WAAW,CAACnL,OAAO,GAAG2E,EAAE,IAAI;MAC/B,IAAIoG,SAAS,CAAC/K,OAAO,EAAE;QACrB+K,SAAS,CAAC/K,OAAO,CAAChP,IAAI,CAAC,IAAI,EAAE2T,EAAE,CAAC;MAClC;MAEA,IAAI,CAACqG,IAAI,CAACxD,MAAM,CAAC7C,EAAE,CAAC;IACtB,CAAC;EACH;AACF,CAAC;AAED9V,eAAe,CAAC8S,MAAM,GAAG,MAAMA,MAAM,SAAS6J,KAAK,CAAC;EAClD9M,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC+H,OAAO,CAACyE,WAAW,EAAEzE,OAAO,CAACgF,OAAO,CAAC;EAC7C;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA5c,eAAe,CAAC4Q,aAAa,GAAGC,SAAS,IAAI;EAC3C,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,IAAI;EACb;;EAEA;EACA,IAAIA,SAAS,CAACgM,oBAAoB,EAAE;IAClC,OAAOhM,SAAS;EAClB;EAEA,MAAMiM,OAAO,GAAG9X,GAAG,IAAI;IACrB,IAAI,CAAC9F,MAAM,CAACiD,IAAI,CAAC6C,GAAG,EAAE,KAAK,CAAC,EAAE;MAC5B;MACA;MACA,MAAM,IAAI9D,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IAEA,MAAM4U,EAAE,GAAG9Q,GAAG,CAAC0J,GAAG;;IAElB;IACA;IACA,MAAMqO,WAAW,GAAGjM,OAAO,CAACkM,WAAW,CAAC,MAAMnM,SAAS,CAAC7L,GAAG,CAAC,CAAC;IAE7D,IAAI,CAAChF,eAAe,CAACyD,cAAc,CAACsZ,WAAW,CAAC,EAAE;MAChD,MAAM,IAAI7b,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,IAAIhC,MAAM,CAACiD,IAAI,CAAC4a,WAAW,EAAE,KAAK,CAAC,EAAE;MACnC,IAAI,CAAC7S,KAAK,CAAC8O,MAAM,CAAC+D,WAAW,CAACrO,GAAG,EAAEoH,EAAE,CAAC,EAAE;QACtC,MAAM,IAAI5U,KAAK,CAAC,gDAAgD,CAAC;MACnE;IACF,CAAC,MAAM;MACL6b,WAAW,CAACrO,GAAG,GAAGoH,EAAE;IACtB;IAEA,OAAOiH,WAAW;EACpB,CAAC;EAEDD,OAAO,CAACD,oBAAoB,GAAG,IAAI;EAEnC,OAAOC,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA9c,eAAe,CAACid,aAAa,GAAG,CAACC,GAAG,EAAEC,KAAK,EAAE9b,KAAK,KAAK;EACrD,IAAI+b,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAGF,KAAK,CAAClc,MAAM;EAExB,OAAOoc,KAAK,GAAG,CAAC,EAAE;IAChB,MAAMC,SAAS,GAAGpU,IAAI,CAACqU,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;IAEvC,IAAIH,GAAG,CAAC7b,KAAK,EAAE8b,KAAK,CAACC,KAAK,GAAGE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;MAC7CF,KAAK,IAAIE,SAAS,GAAG,CAAC;MACtBD,KAAK,IAAIC,SAAS,GAAG,CAAC;IACxB,CAAC,MAAM;MACLD,KAAK,GAAGC,SAAS;IACnB;EACF;EAEA,OAAOF,KAAK;AACd,CAAC;AAEDpd,eAAe,CAACwd,yBAAyB,GAAGjP,MAAM,IAAI;EACpD,IAAIA,MAAM,KAAKpO,MAAM,CAACoO,MAAM,CAAC,IAAIxN,KAAK,CAACC,OAAO,CAACuN,MAAM,CAAC,EAAE;IACtD,MAAM9B,cAAc,CAAC,iCAAiC,CAAC;EACzD;EAEAtM,MAAM,CAACwD,IAAI,CAAC4K,MAAM,CAAC,CAAC9E,OAAO,CAACmF,OAAO,IAAI;IACrC,IAAIA,OAAO,CAAC/C,KAAK,CAAC,GAAG,CAAC,CAACoB,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpC,MAAMR,cAAc,CAClB,2DACF,CAAC;IACH;IAEA,MAAMpL,KAAK,GAAGkN,MAAM,CAACK,OAAO,CAAC;IAE7B,IAAI,OAAOvN,KAAK,KAAK,QAAQ,IACzB,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACO,IAAI,CAACiC,GAAG,IACxC3E,MAAM,CAACiD,IAAI,CAACd,KAAK,EAAEwC,GAAG,CACxB,CAAC,EAAE;MACL,MAAM4I,cAAc,CAClB,0DACF,CAAC;IACH;IAEA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAACQ,QAAQ,CAAC5L,KAAK,CAAC,EAAE;MACxC,MAAMoL,cAAc,CAClB,yDACF,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAzM,eAAe,CAAC0Q,kBAAkB,GAAGnC,MAAM,IAAI;EAC7CvO,eAAe,CAACwd,yBAAyB,CAACjP,MAAM,CAAC;EAEjD,MAAMkP,aAAa,GAAGlP,MAAM,CAACG,GAAG,KAAK/M,SAAS,GAAG,IAAI,GAAG4M,MAAM,CAACG,GAAG;EAClE,MAAMgP,OAAO,GAAG5d,iBAAiB,CAACyO,MAAM,CAAC;;EAEzC;EACA,MAAMsC,SAAS,GAAGA,CAAC7L,GAAG,EAAE2Y,QAAQ,KAAK;IACnC;IACA,IAAI5c,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,EAAE;MACtB,OAAOA,GAAG,CAACxD,GAAG,CAACoc,MAAM,IAAI/M,SAAS,CAAC+M,MAAM,EAAED,QAAQ,CAAC,CAAC;IACvD;IAEA,MAAMlZ,MAAM,GAAGiZ,OAAO,CAAC/O,SAAS,GAAG,CAAC,CAAC,GAAGzE,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;IAExD7E,MAAM,CAACwD,IAAI,CAACga,QAAQ,CAAC,CAAClU,OAAO,CAAC5F,GAAG,IAAI;MACnC,IAAImB,GAAG,IAAI,IAAI,IAAI,CAAC9F,MAAM,CAACiD,IAAI,CAAC6C,GAAG,EAAEnB,GAAG,CAAC,EAAE;QACzC;MACF;MAEA,MAAMgL,IAAI,GAAG8O,QAAQ,CAAC9Z,GAAG,CAAC;MAE1B,IAAIgL,IAAI,KAAK1O,MAAM,CAAC0O,IAAI,CAAC,EAAE;QACzB;QACA,IAAI7J,GAAG,CAACnB,GAAG,CAAC,KAAK1D,MAAM,CAAC6E,GAAG,CAACnB,GAAG,CAAC,CAAC,EAAE;UACjCY,MAAM,CAACZ,GAAG,CAAC,GAAGgN,SAAS,CAAC7L,GAAG,CAACnB,GAAG,CAAC,EAAEgL,IAAI,CAAC;QACzC;MACF,CAAC,MAAM,IAAI6O,OAAO,CAAC/O,SAAS,EAAE;QAC5B;QACAlK,MAAM,CAACZ,GAAG,CAAC,GAAGqG,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAACnB,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOY,MAAM,CAACZ,GAAG,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAOmB,GAAG,IAAI,IAAI,GAAGP,MAAM,GAAGO,GAAG;EACnC,CAAC;EAED,OAAOA,GAAG,IAAI;IACZ,MAAMP,MAAM,GAAGoM,SAAS,CAAC7L,GAAG,EAAE0Y,OAAO,CAAChQ,IAAI,CAAC;IAE3C,IAAI+P,aAAa,IAAIve,MAAM,CAACiD,IAAI,CAAC6C,GAAG,EAAE,KAAK,CAAC,EAAE;MAC5CP,MAAM,CAACiK,GAAG,GAAG1J,GAAG,CAAC0J,GAAG;IACtB;IAEA,IAAI,CAAC+O,aAAa,IAAIve,MAAM,CAACiD,IAAI,CAACsC,MAAM,EAAE,KAAK,CAAC,EAAE;MAChD,OAAOA,MAAM,CAACiK,GAAG;IACnB;IAEA,OAAOjK,MAAM;EACf,CAAC;AACH,CAAC;;AAED;AACA;AACAzE,eAAe,CAACgb,qBAAqB,GAAG,CAACpR,QAAQ,EAAEiU,QAAQ,KAAK;EAC9D,MAAMC,gBAAgB,GAAGje,+BAA+B,CAAC+J,QAAQ,CAAC;EAClE,MAAMmU,QAAQ,GAAG/d,eAAe,CAACge,kBAAkB,CAACH,QAAQ,CAAC;EAE7D,MAAMI,MAAM,GAAG,CAAC,CAAC;EAEjB,IAAIH,gBAAgB,CAACpP,GAAG,EAAE;IACxBuP,MAAM,CAACvP,GAAG,GAAGoP,gBAAgB,CAACpP,GAAG;IACjC,OAAOoP,gBAAgB,CAACpP,GAAG;EAC7B;;EAEA;EACA;EACA;EACA1O,eAAe,CAACyb,OAAO,CAACwC,MAAM,EAAE;IAACC,IAAI,EAAEJ;EAAgB,CAAC,CAAC;EACzD9d,eAAe,CAACyb,OAAO,CAACwC,MAAM,EAAEJ,QAAQ,EAAE;IAACM,QAAQ,EAAE;EAAI,CAAC,CAAC;EAE3D,IAAIJ,QAAQ,EAAE;IACZ,OAAOE,MAAM;EACf;;EAEA;EACA,MAAMG,WAAW,GAAGje,MAAM,CAAC+D,MAAM,CAAC,CAAC,CAAC,EAAE2Z,QAAQ,CAAC;EAC/C,IAAII,MAAM,CAACvP,GAAG,EAAE;IACd0P,WAAW,CAAC1P,GAAG,GAAGuP,MAAM,CAACvP,GAAG;EAC9B;EAEA,OAAO0P,WAAW;AACpB,CAAC;AAEDpe,eAAe,CAACqe,YAAY,GAAG,CAACC,IAAI,EAAEC,KAAK,EAAErC,SAAS,KAAK;EACzD,OAAOO,YAAY,CAAC+B,WAAW,CAACF,IAAI,EAAEC,KAAK,EAAErC,SAAS,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACA;AACAlc,eAAe,CAACqZ,iBAAiB,GAAG,CAAChI,OAAO,EAAE0K,UAAU,EAAE0C,UAAU,EAAEC,QAAQ,EAAExW,OAAO,KACrFuU,YAAY,CAACkC,gBAAgB,CAACtN,OAAO,EAAE0K,UAAU,EAAE0C,UAAU,EAAEC,QAAQ,EAAExW,OAAO,CAAC;AAGnFlI,eAAe,CAAC4e,wBAAwB,GAAG,CAAC7C,UAAU,EAAE0C,UAAU,EAAEC,QAAQ,EAAExW,OAAO,KACnFuU,YAAY,CAACoC,uBAAuB,CAAC9C,UAAU,EAAE0C,UAAU,EAAEC,QAAQ,EAAExW,OAAO,CAAC;AAGjFlI,eAAe,CAAC8e,0BAA0B,GAAG,CAAC/C,UAAU,EAAE0C,UAAU,EAAEC,QAAQ,EAAExW,OAAO,KACrFuU,YAAY,CAACsC,yBAAyB,CAAChD,UAAU,EAAE0C,UAAU,EAAEC,QAAQ,EAAExW,OAAO,CAAC;AAGnFlI,eAAe,CAACgf,qBAAqB,GAAG,CAAC3Q,KAAK,EAAErJ,GAAG,KAAK;EACtD,IAAI,CAACqJ,KAAK,CAACgD,OAAO,EAAE;IAClB,MAAM,IAAInQ,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuL,KAAK,CAACiF,OAAO,CAACrS,MAAM,EAAE6B,CAAC,EAAE,EAAE;IAC7C,IAAIuL,KAAK,CAACiF,OAAO,CAACxQ,CAAC,CAAC,KAAKkC,GAAG,EAAE;MAC5B,OAAOlC,CAAC;IACV;EACF;EAEA,MAAM5B,KAAK,CAAC,2BAA2B,CAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAlB,eAAe,CAAC+Z,qBAAqB,GAAGnQ,QAAQ,IAAI;EAClD;EACA,IAAI5J,eAAe,CAACsO,aAAa,CAAC1E,QAAQ,CAAC,EAAE;IAC3C,OAAO,CAACA,QAAQ,CAAC;EACnB;EAEA,IAAI,CAACA,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;;EAEA;EACA,IAAI1K,MAAM,CAACiD,IAAI,CAACyH,QAAQ,EAAE,KAAK,CAAC,EAAE;IAChC;IACA,IAAI5J,eAAe,CAACsO,aAAa,CAAC1E,QAAQ,CAAC8E,GAAG,CAAC,EAAE;MAC/C,OAAO,CAAC9E,QAAQ,CAAC8E,GAAG,CAAC;IACvB;;IAEA;IACA,IAAI9E,QAAQ,CAAC8E,GAAG,IACT3N,KAAK,CAACC,OAAO,CAAC4I,QAAQ,CAAC8E,GAAG,CAACpN,GAAG,CAAC,IAC/BsI,QAAQ,CAAC8E,GAAG,CAACpN,GAAG,CAACL,MAAM,IACvB2I,QAAQ,CAAC8E,GAAG,CAACpN,GAAG,CAACsB,KAAK,CAAC5C,eAAe,CAACsO,aAAa,CAAC,EAAE;MAC5D,OAAO1E,QAAQ,CAAC8E,GAAG,CAACpN,GAAG;IACzB;IAEA,OAAO,IAAI;EACb;;EAEA;EACA;EACA;EACA,IAAIP,KAAK,CAACC,OAAO,CAAC4I,QAAQ,CAAClF,IAAI,CAAC,EAAE;IAChC,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8G,QAAQ,CAAClF,IAAI,CAACzD,MAAM,EAAE,EAAE6B,CAAC,EAAE;MAC7C,MAAMmc,MAAM,GAAGjf,eAAe,CAAC+Z,qBAAqB,CAACnQ,QAAQ,CAAClF,IAAI,CAAC5B,CAAC,CAAC,CAAC;MAEtE,IAAImc,MAAM,EAAE;QACV,OAAOA,MAAM;MACf;IACF;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AAEDjf,eAAe,CAACmY,oBAAoB,GAAG,CAAC9J,KAAK,EAAErJ,GAAG,KAAK;EACrD,MAAMuJ,MAAM,GAAGrE,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;EAE/B,OAAOuJ,MAAM,CAACG,GAAG;EAEjB,IAAIL,KAAK,CAACgD,OAAO,EAAE;IACjB,IAAI,CAAChD,KAAK,CAAC0B,MAAM,EAAE;MACjB1B,KAAK,CAACoD,WAAW,CAACzM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,EAAE,IAAI,CAAC;MAC5DF,KAAK,CAACiF,OAAO,CAAC3J,IAAI,CAAC3E,GAAG,CAAC;IACzB,CAAC,MAAM;MACL,MAAMlC,CAAC,GAAG9C,eAAe,CAACkf,mBAAmB,CAC3C7Q,KAAK,CAAC0B,MAAM,CAACkG,aAAa,CAAC;QAACpD,SAAS,EAAExE,KAAK,CAACwE;MAAS,CAAC,CAAC,EACxDxE,KAAK,CAACiF,OAAO,EACbtO,GACF,CAAC;MAED,IAAI8M,IAAI,GAAGzD,KAAK,CAACiF,OAAO,CAACxQ,CAAC,GAAG,CAAC,CAAC;MAC/B,IAAIgP,IAAI,EAAE;QACRA,IAAI,GAAGA,IAAI,CAACpD,GAAG;MACjB,CAAC,MAAM;QACLoD,IAAI,GAAG,IAAI;MACb;MAEAzD,KAAK,CAACoD,WAAW,CAACzM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,EAAEuD,IAAI,CAAC;IAC9D;IAEAzD,KAAK,CAAC6C,KAAK,CAAClM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,CAAC;EAClD,CAAC,MAAM;IACLF,KAAK,CAAC6C,KAAK,CAAClM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,CAAC;IAChDF,KAAK,CAACiF,OAAO,CAACoC,GAAG,CAAC1Q,GAAG,CAAC0J,GAAG,EAAE1J,GAAG,CAAC;EACjC;AACF,CAAC;AAEDhF,eAAe,CAACuY,qBAAqB,GAAG,OAAOlK,KAAK,EAAErJ,GAAG,KAAK;EAC5D,MAAMuJ,MAAM,GAAGrE,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC;EAE/B,OAAOuJ,MAAM,CAACG,GAAG;EAEjB,IAAIL,KAAK,CAACgD,OAAO,EAAE;IACjB,IAAI,CAAChD,KAAK,CAAC0B,MAAM,EAAE;MACjB,MAAM1B,KAAK,CAACoD,WAAW,CAACzM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,EAAE,IAAI,CAAC;MAClEF,KAAK,CAACiF,OAAO,CAAC3J,IAAI,CAAC3E,GAAG,CAAC;IACzB,CAAC,MAAM;MACL,MAAMlC,CAAC,GAAG9C,eAAe,CAACkf,mBAAmB,CAC3C7Q,KAAK,CAAC0B,MAAM,CAACkG,aAAa,CAAC;QAACpD,SAAS,EAAExE,KAAK,CAACwE;MAAS,CAAC,CAAC,EACxDxE,KAAK,CAACiF,OAAO,EACbtO,GACF,CAAC;MAED,IAAI8M,IAAI,GAAGzD,KAAK,CAACiF,OAAO,CAACxQ,CAAC,GAAG,CAAC,CAAC;MAC/B,IAAIgP,IAAI,EAAE;QACRA,IAAI,GAAGA,IAAI,CAACpD,GAAG;MACjB,CAAC,MAAM;QACLoD,IAAI,GAAG,IAAI;MACb;MAEA,MAAMzD,KAAK,CAACoD,WAAW,CAACzM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,EAAEuD,IAAI,CAAC;IACpE;IAEA,MAAMzD,KAAK,CAAC6C,KAAK,CAAClM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,CAAC;EACxD,CAAC,MAAM;IACL,MAAMF,KAAK,CAAC6C,KAAK,CAAClM,GAAG,CAAC0J,GAAG,EAAEL,KAAK,CAAC4E,YAAY,CAAC1E,MAAM,CAAC,CAAC;IACtDF,KAAK,CAACiF,OAAO,CAACoC,GAAG,CAAC1Q,GAAG,CAAC0J,GAAG,EAAE1J,GAAG,CAAC;EACjC;AACF,CAAC;AAEDhF,eAAe,CAACkf,mBAAmB,GAAG,CAAChC,GAAG,EAAEC,KAAK,EAAE9b,KAAK,KAAK;EAC3D,IAAI8b,KAAK,CAAClc,MAAM,KAAK,CAAC,EAAE;IACtBkc,KAAK,CAACxT,IAAI,CAACtI,KAAK,CAAC;IACjB,OAAO,CAAC;EACV;EAEA,MAAMyB,CAAC,GAAG9C,eAAe,CAACid,aAAa,CAACC,GAAG,EAAEC,KAAK,EAAE9b,KAAK,CAAC;EAE1D8b,KAAK,CAACgC,MAAM,CAACrc,CAAC,EAAE,CAAC,EAAEzB,KAAK,CAAC;EAEzB,OAAOyB,CAAC;AACV,CAAC;AAED9C,eAAe,CAACge,kBAAkB,GAAGvD,GAAG,IAAI;EAC1C,IAAIsD,QAAQ,GAAG,KAAK;EACpB,IAAIqB,SAAS,GAAG,KAAK;EAErBjf,MAAM,CAACwD,IAAI,CAAC8W,GAAG,CAAC,CAAChR,OAAO,CAAC5F,GAAG,IAAI;IAC9B,IAAIA,GAAG,CAACwE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5B0V,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM;MACLqB,SAAS,GAAG,IAAI;IAClB;EACF,CAAC,CAAC;EAEF,IAAIrB,QAAQ,IAAIqB,SAAS,EAAE;IACzB,MAAM,IAAIle,KAAK,CACb,qEACF,CAAC;EACH;EAEA,OAAO6c,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA/d,eAAe,CAACyD,cAAc,GAAG2G,CAAC,IAAI;EACpC,OAAOA,CAAC,IAAIpK,eAAe,CAACqC,EAAE,CAACC,KAAK,CAAC8H,CAAC,CAAC,KAAK,CAAC;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApK,eAAe,CAACyb,OAAO,GAAG,UAACzW,GAAG,EAAE6Y,QAAQ,EAAmB;EAAA,IAAjB3V,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACpD,IAAI,CAACnI,eAAe,CAACyD,cAAc,CAACoa,QAAQ,CAAC,EAAE;IAC7C,MAAMpR,cAAc,CAAC,4BAA4B,CAAC;EACpD;;EAEA;EACAoR,QAAQ,GAAG3T,KAAK,CAACgK,KAAK,CAAC2J,QAAQ,CAAC;EAEhC,MAAMwB,UAAU,GAAG5f,gBAAgB,CAACoe,QAAQ,CAAC;EAC7C,MAAMI,MAAM,GAAGoB,UAAU,GAAGnV,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC,GAAG6Y,QAAQ;EAEvD,IAAIwB,UAAU,EAAE;IACd;IACAlf,MAAM,CAACwD,IAAI,CAACka,QAAQ,CAAC,CAACpU,OAAO,CAACsD,QAAQ,IAAI;MACxC;MACA,MAAMuS,WAAW,GAAGpX,OAAO,CAACiW,QAAQ,IAAIpR,QAAQ,KAAK,cAAc;MACnE,MAAMwS,OAAO,GAAGC,SAAS,CAACF,WAAW,GAAG,MAAM,GAAGvS,QAAQ,CAAC;MAC1D,MAAMjM,OAAO,GAAG+c,QAAQ,CAAC9Q,QAAQ,CAAC;MAElC,IAAI,CAACwS,OAAO,EAAE;QACZ,MAAM9S,cAAc,+BAAArK,MAAA,CAA+B2K,QAAQ,CAAE,CAAC;MAChE;MAEA5M,MAAM,CAACwD,IAAI,CAAC7C,OAAO,CAAC,CAAC2I,OAAO,CAACgW,OAAO,IAAI;QACtC,MAAMlb,GAAG,GAAGzD,OAAO,CAAC2e,OAAO,CAAC;QAE5B,IAAIA,OAAO,KAAK,EAAE,EAAE;UAClB,MAAMhT,cAAc,CAAC,oCAAoC,CAAC;QAC5D;QAEA,MAAMiT,QAAQ,GAAGD,OAAO,CAAC5T,KAAK,CAAC,GAAG,CAAC;QAEnC,IAAI,CAAC6T,QAAQ,CAAC9c,KAAK,CAAC6F,OAAO,CAAC,EAAE;UAC5B,MAAMgE,cAAc,CAClB,oBAAArK,MAAA,CAAoBqd,OAAO,wCAC3B,uBACF,CAAC;QACH;QAEA,MAAME,MAAM,GAAGC,aAAa,CAAC3B,MAAM,EAAEyB,QAAQ,EAAE;UAC7ChY,YAAY,EAAEQ,OAAO,CAACR,YAAY;UAClCmY,WAAW,EAAE9S,QAAQ,KAAK,SAAS;UACnC+S,QAAQ,EAAEC,mBAAmB,CAAChT,QAAQ;QACxC,CAAC,CAAC;QAEFwS,OAAO,CAACI,MAAM,EAAED,QAAQ,CAACM,GAAG,CAAC,CAAC,EAAEzb,GAAG,EAAEkb,OAAO,EAAExB,MAAM,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIjZ,GAAG,CAAC0J,GAAG,IAAI,CAACxE,KAAK,CAAC8O,MAAM,CAAChU,GAAG,CAAC0J,GAAG,EAAEuP,MAAM,CAACvP,GAAG,CAAC,EAAE;MACjD,MAAMjC,cAAc,CAClB,qDAAArK,MAAA,CAAoD4C,GAAG,CAAC0J,GAAG,iBAC3D,mEAAmE,aAAAtM,MAAA,CAC1D6b,MAAM,CAACvP,GAAG,OACrB,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAI1J,GAAG,CAAC0J,GAAG,IAAImP,QAAQ,CAACnP,GAAG,IAAI,CAACxE,KAAK,CAAC8O,MAAM,CAAChU,GAAG,CAAC0J,GAAG,EAAEmP,QAAQ,CAACnP,GAAG,CAAC,EAAE;MACnE,MAAMjC,cAAc,CAClB,gDAAArK,MAAA,CAA+C4C,GAAG,CAAC0J,GAAG,0BAAAtM,MAAA,CAC5Cyb,QAAQ,CAACnP,GAAG,QACxB,CAAC;IACH;;IAEA;IACAgJ,wBAAwB,CAACmG,QAAQ,CAAC;EACpC;;EAEA;EACA1d,MAAM,CAACwD,IAAI,CAACqB,GAAG,CAAC,CAACyE,OAAO,CAAC5F,GAAG,IAAI;IAC9B;IACA;IACA;IACA,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB,OAAOmB,GAAG,CAACnB,GAAG,CAAC;IACjB;EACF,CAAC,CAAC;EAEF1D,MAAM,CAACwD,IAAI,CAACsa,MAAM,CAAC,CAACxU,OAAO,CAAC5F,GAAG,IAAI;IACjCmB,GAAG,CAACnB,GAAG,CAAC,GAAGoa,MAAM,CAACpa,GAAG,CAAC;EACxB,CAAC,CAAC;AACJ,CAAC;AAED7D,eAAe,CAACwS,0BAA0B,GAAG,CAACO,MAAM,EAAEkN,gBAAgB,KAAK;EACzE,MAAMpP,SAAS,GAAGkC,MAAM,CAACT,YAAY,CAAC,CAAC,KAAKtN,GAAG,IAAIA,GAAG,CAAC;EACvD,IAAIkb,UAAU,GAAG,CAAC,CAACD,gBAAgB,CAACnM,iBAAiB;EAErD,IAAIqM,uBAAuB;EAC3B,IAAIngB,eAAe,CAACogB,2BAA2B,CAACH,gBAAgB,CAAC,EAAE;IACjE;IACA;IACA;IACA;IACA,MAAMI,OAAO,GAAG,CAACJ,gBAAgB,CAACK,WAAW;IAE7CH,uBAAuB,GAAG;MACxB1O,WAAWA,CAACqE,EAAE,EAAEvH,MAAM,EAAEqN,MAAM,EAAE;QAC9B,MAAM2E,KAAK,GAAGL,UAAU,IAAI,EAAED,gBAAgB,CAACO,OAAO,IAAIP,gBAAgB,CAAC/O,KAAK,CAAC;QACjF,IAAIqP,KAAK,EAAE;UACT;QACF;QAEA,MAAMvb,GAAG,GAAG6L,SAAS,CAAC1Q,MAAM,CAAC+D,MAAM,CAACqK,MAAM,EAAE;UAACG,GAAG,EAAEoH;QAAE,CAAC,CAAC,CAAC;QAEvD,IAAImK,gBAAgB,CAACO,OAAO,EAAE;UAC5BP,gBAAgB,CAACO,OAAO,CACpBxb,GAAG,EACHqb,OAAO,GACDzE,MAAM,GACF,IAAI,CAACO,IAAI,CAACrR,OAAO,CAAC8Q,MAAM,CAAC,GACzB,IAAI,CAACO,IAAI,CAAChI,IAAI,CAAC,CAAC,GACpB,CAAC,CAAC,EACRyH,MACJ,CAAC;QACH,CAAC,MAAM;UACLqE,gBAAgB,CAAC/O,KAAK,CAAClM,GAAG,CAAC;QAC7B;MACF,CAAC;MACD0M,OAAOA,CAACoE,EAAE,EAAEvH,MAAM,EAAE;QAElB,IAAI,EAAE0R,gBAAgB,CAACQ,SAAS,IAAIR,gBAAgB,CAACvO,OAAO,CAAC,EAAE;UAC7D;QACF;QAEA,IAAI1M,GAAG,GAAGkF,KAAK,CAACgK,KAAK,CAAC,IAAI,CAACiI,IAAI,CAAC1G,GAAG,CAACK,EAAE,CAAC,CAAC;QACxC,IAAI,CAAC9Q,GAAG,EAAE;UACR,MAAM,IAAI9D,KAAK,4BAAAkB,MAAA,CAA4B0T,EAAE,CAAE,CAAC;QAClD;QAEA,MAAM4K,MAAM,GAAG7P,SAAS,CAAC3G,KAAK,CAACgK,KAAK,CAAClP,GAAG,CAAC,CAAC;QAE1CyX,YAAY,CAACC,YAAY,CAAC1X,GAAG,EAAEuJ,MAAM,CAAC;QAEtC,IAAI0R,gBAAgB,CAACQ,SAAS,EAAE;UAC9BR,gBAAgB,CAACQ,SAAS,CACtB5P,SAAS,CAAC7L,GAAG,CAAC,EACd0b,MAAM,EACNL,OAAO,GAAG,IAAI,CAAClE,IAAI,CAACrR,OAAO,CAACgL,EAAE,CAAC,GAAG,CAAC,CACvC,CAAC;QACH,CAAC,MAAM;UACLmK,gBAAgB,CAACvO,OAAO,CAACb,SAAS,CAAC7L,GAAG,CAAC,EAAE0b,MAAM,CAAC;QAClD;MACF,CAAC;MACD/O,WAAWA,CAACmE,EAAE,EAAE8F,MAAM,EAAE;QACtB,IAAI,CAACqE,gBAAgB,CAACU,OAAO,EAAE;UAC7B;QACF;QAEA,MAAMC,IAAI,GAAGP,OAAO,GAAG,IAAI,CAAClE,IAAI,CAACrR,OAAO,CAACgL,EAAE,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI+K,EAAE,GAAGR,OAAO,GACVzE,MAAM,GACF,IAAI,CAACO,IAAI,CAACrR,OAAO,CAAC8Q,MAAM,CAAC,GACzB,IAAI,CAACO,IAAI,CAAChI,IAAI,CAAC,CAAC,GACpB,CAAC,CAAC;;QAER;QACA;QACA,IAAI0M,EAAE,GAAGD,IAAI,EAAE;UACb,EAAEC,EAAE;QACN;QAEAZ,gBAAgB,CAACU,OAAO,CACpB9P,SAAS,CAAC3G,KAAK,CAACgK,KAAK,CAAC,IAAI,CAACiI,IAAI,CAAC1G,GAAG,CAACK,EAAE,CAAC,CAAC,CAAC,EACzC8K,IAAI,EACJC,EAAE,EACFjF,MAAM,IAAI,IACd,CAAC;MACH,CAAC;MACDzK,OAAOA,CAAC2E,EAAE,EAAE;QACV,IAAI,EAAEmK,gBAAgB,CAACa,SAAS,IAAIb,gBAAgB,CAAC9O,OAAO,CAAC,EAAE;UAC7D;QACF;;QAEA;QACA;QACA,MAAMnM,GAAG,GAAG6L,SAAS,CAAC,IAAI,CAACsL,IAAI,CAAC1G,GAAG,CAACK,EAAE,CAAC,CAAC;QAExC,IAAImK,gBAAgB,CAACa,SAAS,EAAE;UAC9Bb,gBAAgB,CAACa,SAAS,CAAC9b,GAAG,EAAEqb,OAAO,GAAG,IAAI,CAAClE,IAAI,CAACrR,OAAO,CAACgL,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,CAAC,MAAM;UACLmK,gBAAgB,CAAC9O,OAAO,CAACnM,GAAG,CAAC;QAC/B;MACF;IACF,CAAC;EACH,CAAC,MAAM;IACLmb,uBAAuB,GAAG;MACxBjP,KAAKA,CAAC4E,EAAE,EAAEvH,MAAM,EAAE;QAChB,IAAI,CAAC2R,UAAU,IAAID,gBAAgB,CAAC/O,KAAK,EAAE;UACzC+O,gBAAgB,CAAC/O,KAAK,CAACL,SAAS,CAAC1Q,MAAM,CAAC+D,MAAM,CAACqK,MAAM,EAAE;YAACG,GAAG,EAAEoH;UAAE,CAAC,CAAC,CAAC,CAAC;QACrE;MACF,CAAC;MACDpE,OAAOA,CAACoE,EAAE,EAAEvH,MAAM,EAAE;QAClB,IAAI0R,gBAAgB,CAACvO,OAAO,EAAE;UAC5B,MAAMgP,MAAM,GAAG,IAAI,CAACvE,IAAI,CAAC1G,GAAG,CAACK,EAAE,CAAC;UAChC,MAAM9Q,GAAG,GAAGkF,KAAK,CAACgK,KAAK,CAACwM,MAAM,CAAC;UAE/BjE,YAAY,CAACC,YAAY,CAAC1X,GAAG,EAAEuJ,MAAM,CAAC;UAEtC0R,gBAAgB,CAACvO,OAAO,CACpBb,SAAS,CAAC7L,GAAG,CAAC,EACd6L,SAAS,CAAC3G,KAAK,CAACgK,KAAK,CAACwM,MAAM,CAAC,CACjC,CAAC;QACH;MACF,CAAC;MACDvP,OAAOA,CAAC2E,EAAE,EAAE;QACV,IAAImK,gBAAgB,CAAC9O,OAAO,EAAE;UAC5B8O,gBAAgB,CAAC9O,OAAO,CAACN,SAAS,CAAC,IAAI,CAACsL,IAAI,CAAC1G,GAAG,CAACK,EAAE,CAAC,CAAC,CAAC;QACxD;MACF;IACF,CAAC;EACH;EAEA,MAAMiL,cAAc,GAAG,IAAI/gB,eAAe,CAACgc,sBAAsB,CAAC;IAChEE,SAAS,EAAEiE;EACb,CAAC,CAAC;;EAEF;EACA;EACA;EACAY,cAAc,CAACzE,WAAW,CAAC0E,YAAY,GAAG,IAAI;EAC9C,MAAM5M,MAAM,GAAGrB,MAAM,CAACL,cAAc,CAACqO,cAAc,CAACzE,WAAW,EAC3D;IAAE2E,oBAAoB,EAAE;EAAK,CAAC,CAAC;;EAEnC;EACA,MAAMC,aAAa,GAAIC,CAAC,IAAK;IAAA,IAAAC,iBAAA;IAC3B,IAAID,CAAC,CAAC5M,OAAO,EAAE2L,UAAU,GAAG,KAAK,CAAC,KAC7B,CAAAkB,iBAAA,GAAAD,CAAC,CAAC3M,cAAc,cAAA4M,iBAAA,uBAAhBA,iBAAA,CAAkBvM,IAAI,CAAC,MAAOqL,UAAU,GAAG,KAAM,CAAC;EACzD,CAAC;EACD;EACA;EACA,IAAItK,MAAM,CAACyL,UAAU,CAACjN,MAAM,CAAC,EAAE;IAC7BA,MAAM,CAACS,IAAI,CAACqM,aAAa,CAAC;EAC5B,CAAC,MAAM;IACLA,aAAa,CAAC9M,MAAM,CAAC;EACvB;EACA,OAAOA,MAAM;AACf,CAAC;AAEDpU,eAAe,CAACogB,2BAA2B,GAAGlE,SAAS,IAAI;EACzD,IAAIA,SAAS,CAAChL,KAAK,IAAIgL,SAAS,CAACsE,OAAO,EAAE;IACxC,MAAM,IAAItf,KAAK,CAAC,kDAAkD,CAAC;EACrE;EAEA,IAAIgb,SAAS,CAACxK,OAAO,IAAIwK,SAAS,CAACuE,SAAS,EAAE;IAC5C,MAAM,IAAIvf,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,IAAIgb,SAAS,CAAC/K,OAAO,IAAI+K,SAAS,CAAC4E,SAAS,EAAE;IAC5C,MAAM,IAAI5f,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,OAAO,CAAC,EACNgb,SAAS,CAACsE,OAAO,IACjBtE,SAAS,CAACuE,SAAS,IACnBvE,SAAS,CAACyE,OAAO,IACjBzE,SAAS,CAAC4E,SAAS,CACpB;AACH,CAAC;AAED9gB,eAAe,CAAC2S,kCAAkC,GAAGuJ,SAAS,IAAI;EAChE,IAAIA,SAAS,CAAChL,KAAK,IAAIgL,SAAS,CAACzK,WAAW,EAAE;IAC5C,MAAM,IAAIvQ,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,OAAO,CAAC,EAAEgb,SAAS,CAACzK,WAAW,IAAIyK,SAAS,CAACvK,WAAW,CAAC;AAC3D,CAAC;AAED3R,eAAe,CAACiZ,sBAAsB,GAAG,CAAC5K,KAAK,EAAErJ,GAAG,KAAK;EACvD,IAAIqJ,KAAK,CAACgD,OAAO,EAAE;IACjB,MAAMvO,CAAC,GAAG9C,eAAe,CAACgf,qBAAqB,CAAC3Q,KAAK,EAAErJ,GAAG,CAAC;IAE3DqJ,KAAK,CAAC8C,OAAO,CAACnM,GAAG,CAAC0J,GAAG,CAAC;IACtBL,KAAK,CAACiF,OAAO,CAAC6L,MAAM,CAACrc,CAAC,EAAE,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL,MAAMgT,EAAE,GAAG9Q,GAAG,CAAC0J,GAAG,CAAC,CAAE;;IAErBL,KAAK,CAAC8C,OAAO,CAACnM,GAAG,CAAC0J,GAAG,CAAC;IACtBL,KAAK,CAACiF,OAAO,CAACqF,MAAM,CAAC7C,EAAE,CAAC;EAC1B;AACF,CAAC;AAED9V,eAAe,CAACmZ,uBAAuB,GAAG,OAAO9K,KAAK,EAAErJ,GAAG,KAAK;EAC9D,IAAIqJ,KAAK,CAACgD,OAAO,EAAE;IACjB,MAAMvO,CAAC,GAAG9C,eAAe,CAACgf,qBAAqB,CAAC3Q,KAAK,EAAErJ,GAAG,CAAC;IAE3D,MAAMqJ,KAAK,CAAC8C,OAAO,CAACnM,GAAG,CAAC0J,GAAG,CAAC;IAC5BL,KAAK,CAACiF,OAAO,CAAC6L,MAAM,CAACrc,CAAC,EAAE,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL,MAAMgT,EAAE,GAAG9Q,GAAG,CAAC0J,GAAG,CAAC,CAAE;;IAErB,MAAML,KAAK,CAAC8C,OAAO,CAACnM,GAAG,CAAC0J,GAAG,CAAC;IAC5BL,KAAK,CAACiF,OAAO,CAACqF,MAAM,CAAC7C,EAAE,CAAC;EAC1B;AACF,CAAC;;AAED;AACA9V,eAAe,CAACsO,aAAa,GAAG1E,QAAQ,IACtC,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,OAAOA,QAAQ,KAAK,QAAQ,IAC5BA,QAAQ,YAAYgO,OAAO,CAACC,QAAQ;;AAGtC;AACA7X,eAAe,CAACkQ,4BAA4B,GAAGtG,QAAQ,IACrD5J,eAAe,CAACsO,aAAa,CAAC1E,QAAQ,CAAC,IACvC5J,eAAe,CAACsO,aAAa,CAAC1E,QAAQ,IAAIA,QAAQ,CAAC8E,GAAG,CAAC,IACvDvO,MAAM,CAACwD,IAAI,CAACiG,QAAQ,CAAC,CAAC3I,MAAM,KAAK,CAAC;AAGpCjB,eAAe,CAAC6b,oBAAoB,GAAG,CAACxN,KAAK,EAAErJ,GAAG,EAAEwW,OAAO,KAAK;EAC9D,IAAI,CAACtR,KAAK,CAAC8O,MAAM,CAAChU,GAAG,CAAC0J,GAAG,EAAE8M,OAAO,CAAC9M,GAAG,CAAC,EAAE;IACvC,MAAM,IAAIxN,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EAEA,MAAM+R,YAAY,GAAG5E,KAAK,CAAC4E,YAAY;EACvC,MAAMqO,aAAa,GAAG7E,YAAY,CAAC8E,iBAAiB,CAClDtO,YAAY,CAACjO,GAAG,CAAC,EACjBiO,YAAY,CAACuI,OAAO,CACtB,CAAC;EAED,IAAI,CAACnN,KAAK,CAACgD,OAAO,EAAE;IAClB,IAAIlR,MAAM,CAACwD,IAAI,CAAC2d,aAAa,CAAC,CAACrgB,MAAM,EAAE;MACrCoN,KAAK,CAACqD,OAAO,CAAC1M,GAAG,CAAC0J,GAAG,EAAE4S,aAAa,CAAC;MACrCjT,KAAK,CAACiF,OAAO,CAACoC,GAAG,CAAC1Q,GAAG,CAAC0J,GAAG,EAAE1J,GAAG,CAAC;IACjC;IAEA;EACF;EAEA,MAAMwc,OAAO,GAAGxhB,eAAe,CAACgf,qBAAqB,CAAC3Q,KAAK,EAAErJ,GAAG,CAAC;EAEjE,IAAI7E,MAAM,CAACwD,IAAI,CAAC2d,aAAa,CAAC,CAACrgB,MAAM,EAAE;IACrCoN,KAAK,CAACqD,OAAO,CAAC1M,GAAG,CAAC0J,GAAG,EAAE4S,aAAa,CAAC;EACvC;EAEA,IAAI,CAACjT,KAAK,CAAC0B,MAAM,EAAE;IACjB;EACF;;EAEA;EACA1B,KAAK,CAACiF,OAAO,CAAC6L,MAAM,CAACqC,OAAO,EAAE,CAAC,CAAC;EAEhC,MAAMC,OAAO,GAAGzhB,eAAe,CAACkf,mBAAmB,CACjD7Q,KAAK,CAAC0B,MAAM,CAACkG,aAAa,CAAC;IAACpD,SAAS,EAAExE,KAAK,CAACwE;EAAS,CAAC,CAAC,EACxDxE,KAAK,CAACiF,OAAO,EACbtO,GACF,CAAC;EAED,IAAIwc,OAAO,KAAKC,OAAO,EAAE;IACvB,IAAI3P,IAAI,GAAGzD,KAAK,CAACiF,OAAO,CAACmO,OAAO,GAAG,CAAC,CAAC;IACrC,IAAI3P,IAAI,EAAE;MACRA,IAAI,GAAGA,IAAI,CAACpD,GAAG;IACjB,CAAC,MAAM;MACLoD,IAAI,GAAG,IAAI;IACb;IAEAzD,KAAK,CAACsD,WAAW,IAAItD,KAAK,CAACsD,WAAW,CAAC3M,GAAG,CAAC0J,GAAG,EAAEoD,IAAI,CAAC;EACvD;AACF,CAAC;AAED9R,eAAe,CAAC8b,qBAAqB,GAAG,OAAOzN,KAAK,EAAErJ,GAAG,EAAEwW,OAAO,KAAK;EACrE,IAAI,CAACtR,KAAK,CAAC8O,MAAM,CAAChU,GAAG,CAAC0J,GAAG,EAAE8M,OAAO,CAAC9M,GAAG,CAAC,EAAE;IACvC,MAAM,IAAIxN,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EAEA,MAAM+R,YAAY,GAAG5E,KAAK,CAAC4E,YAAY;EACvC,MAAMqO,aAAa,GAAG7E,YAAY,CAAC8E,iBAAiB,CAClDtO,YAAY,CAACjO,GAAG,CAAC,EACjBiO,YAAY,CAACuI,OAAO,CACtB,CAAC;EAED,IAAI,CAACnN,KAAK,CAACgD,OAAO,EAAE;IAClB,IAAIlR,MAAM,CAACwD,IAAI,CAAC2d,aAAa,CAAC,CAACrgB,MAAM,EAAE;MACrC,MAAMoN,KAAK,CAACqD,OAAO,CAAC1M,GAAG,CAAC0J,GAAG,EAAE4S,aAAa,CAAC;MAC3CjT,KAAK,CAACiF,OAAO,CAACoC,GAAG,CAAC1Q,GAAG,CAAC0J,GAAG,EAAE1J,GAAG,CAAC;IACjC;IAEA;EACF;EAEA,MAAMwc,OAAO,GAAGxhB,eAAe,CAACgf,qBAAqB,CAAC3Q,KAAK,EAAErJ,GAAG,CAAC;EAEjE,IAAI7E,MAAM,CAACwD,IAAI,CAAC2d,aAAa,CAAC,CAACrgB,MAAM,EAAE;IACrC,MAAMoN,KAAK,CAACqD,OAAO,CAAC1M,GAAG,CAAC0J,GAAG,EAAE4S,aAAa,CAAC;EAC7C;EAEA,IAAI,CAACjT,KAAK,CAAC0B,MAAM,EAAE;IACjB;EACF;;EAEA;EACA1B,KAAK,CAACiF,OAAO,CAAC6L,MAAM,CAACqC,OAAO,EAAE,CAAC,CAAC;EAEhC,MAAMC,OAAO,GAAGzhB,eAAe,CAACkf,mBAAmB,CACjD7Q,KAAK,CAAC0B,MAAM,CAACkG,aAAa,CAAC;IAACpD,SAAS,EAAExE,KAAK,CAACwE;EAAS,CAAC,CAAC,EACxDxE,KAAK,CAACiF,OAAO,EACbtO,GACF,CAAC;EAED,IAAIwc,OAAO,KAAKC,OAAO,EAAE;IACvB,IAAI3P,IAAI,GAAGzD,KAAK,CAACiF,OAAO,CAACmO,OAAO,GAAG,CAAC,CAAC;IACrC,IAAI3P,IAAI,EAAE;MACRA,IAAI,GAAGA,IAAI,CAACpD,GAAG;IACjB,CAAC,MAAM;MACLoD,IAAI,GAAG,IAAI;IACb;IAEAzD,KAAK,CAACsD,WAAW,KAAI,MAAMtD,KAAK,CAACsD,WAAW,CAAC3M,GAAG,CAAC0J,GAAG,EAAEoD,IAAI,CAAC;EAC7D;AACF,CAAC;AAED,MAAM0N,SAAS,GAAG;EAChBkC,YAAYA,CAAC/B,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IAC/B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIrF,MAAM,CAACiD,IAAI,CAACoC,GAAG,EAAE,OAAO,CAAC,EAAE;MACxD,IAAIA,GAAG,CAACvC,KAAK,KAAK,MAAM,EAAE;QACxB,MAAMyK,cAAc,CAClB,yDAAyD,GACzD,wBAAwB,EACxB;UAACE;QAAK,CACR,CAAC;MACH;IACF,CAAC,MAAM,IAAIpI,GAAG,KAAK,IAAI,EAAE;MACvB,MAAMkI,cAAc,CAAC,+BAA+B,EAAE;QAACE;MAAK,CAAC,CAAC;IAChE;IAEAgT,MAAM,CAAChT,KAAK,CAAC,GAAG,IAAIgV,IAAI,CAAC,CAAC;EAC5B,CAAC;EACDC,IAAIA,CAACjC,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMkI,cAAc,CAAC,wCAAwC,EAAE;QAACE;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAIgT,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAChT,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE;QAAK,CACR,CAAC;MACH;MAEAgT,MAAM,CAAChT,KAAK,CAAC,IAAIpI,GAAG;IACtB,CAAC,MAAM;MACLob,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG;IACrB;EACF,CAAC;EACDsd,IAAIA,CAAClC,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMkI,cAAc,CAAC,wCAAwC,EAAE;QAACE;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAIgT,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAChT,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE;QAAK,CACR,CAAC;MACH;MAEA,IAAIgT,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG,EAAE;QACvBob,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG;MACrB;IACF,CAAC,MAAM;MACLob,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG;IACrB;EACF,CAAC;EACDud,IAAIA,CAACnC,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMkI,cAAc,CAAC,wCAAwC,EAAE;QAACE;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAIgT,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAChT,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE;QAAK,CACR,CAAC;MACH;MAEA,IAAIgT,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG,EAAE;QACvBob,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG;MACrB;IACF,CAAC,MAAM;MACLob,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG;IACrB;EACF,CAAC;EACDwd,IAAIA,CAACpC,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMkI,cAAc,CAAC,wCAAwC,EAAE;QAACE;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAIgT,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAChT,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE;QAAK,CACR,CAAC;MACH;MAEAgT,MAAM,CAAChT,KAAK,CAAC,IAAIpI,GAAG;IACtB,CAAC,MAAM;MACLob,MAAM,CAAChT,KAAK,CAAC,GAAG,CAAC;IACnB;EACF,CAAC;EACDqV,OAAOA,CAACrC,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAEkb,OAAO,EAAEza,GAAG,EAAE;IACxC;IACA,IAAIya,OAAO,KAAKlb,GAAG,EAAE;MACnB,MAAMkI,cAAc,CAAC,wCAAwC,EAAE;QAACE;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIgT,MAAM,KAAK,IAAI,EAAE;MACnB,MAAMlT,cAAc,CAAC,8BAA8B,EAAE;QAACE;MAAK,CAAC,CAAC;IAC/D;IAEA,IAAI,OAAOpI,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMkI,cAAc,CAAC,iCAAiC,EAAE;QAACE;MAAK,CAAC,CAAC;IAClE;IAEA,IAAIpI,GAAG,CAAC0I,QAAQ,CAAC,IAAI,CAAC,EAAE;MACtB;MACA;MACA,MAAMR,cAAc,CAClB,mEAAmE,EACnE;QAACE;MAAK,CACR,CAAC;IACH;IAEA,IAAIgT,MAAM,KAAKhe,SAAS,EAAE;MACxB;IACF;IAEA,MAAMyM,MAAM,GAAGuR,MAAM,CAAChT,KAAK,CAAC;IAE5B,OAAOgT,MAAM,CAAChT,KAAK,CAAC;IAEpB,MAAM+S,QAAQ,GAAGnb,GAAG,CAACsH,KAAK,CAAC,GAAG,CAAC;IAC/B,MAAMoW,OAAO,GAAGrC,aAAa,CAAC5a,GAAG,EAAE0a,QAAQ,EAAE;MAACG,WAAW,EAAE;IAAI,CAAC,CAAC;IAEjE,IAAIoC,OAAO,KAAK,IAAI,EAAE;MACpB,MAAMxV,cAAc,CAAC,8BAA8B,EAAE;QAACE;MAAK,CAAC,CAAC;IAC/D;IAEAsV,OAAO,CAACvC,QAAQ,CAACM,GAAG,CAAC,CAAC,CAAC,GAAG5R,MAAM;EAClC,CAAC;EACD8P,IAAIA,CAACyB,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACvB,IAAIob,MAAM,KAAKxf,MAAM,CAACwf,MAAM,CAAC,EAAE;MAAE;MAC/B,MAAM/S,KAAK,GAAGH,cAAc,CAC1B,yCAAyC,EACzC;QAACE;MAAK,CACR,CAAC;MACDC,KAAK,CAACsV,gBAAgB,GAAG,IAAI;MAC7B,MAAMtV,KAAK;IACb;IAEA,IAAI+S,MAAM,KAAK,IAAI,EAAE;MACnB,MAAM/S,KAAK,GAAGH,cAAc,CAAC,6BAA6B,EAAE;QAACE;MAAK,CAAC,CAAC;MACpEC,KAAK,CAACsV,gBAAgB,GAAG,IAAI;MAC7B,MAAMtV,KAAK;IACb;IAEA8K,wBAAwB,CAACnT,GAAG,CAAC;IAE7Bob,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG;EACrB,CAAC;EACD4d,YAAYA,CAACxC,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IAC/B;EAAA,CACD;EACD6d,MAAMA,CAACzC,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACzB,IAAIob,MAAM,KAAKhe,SAAS,EAAE;MACxB,IAAIge,MAAM,YAAY5e,KAAK,EAAE;QAC3B,IAAI4L,KAAK,IAAIgT,MAAM,EAAE;UACnBA,MAAM,CAAChT,KAAK,CAAC,GAAG,IAAI;QACtB;MACF,CAAC,MAAM;QACL,OAAOgT,MAAM,CAAChT,KAAK,CAAC;MACtB;IACF;EACF,CAAC;EACD0V,KAAKA,CAAC1C,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACxB,IAAIob,MAAM,CAAChT,KAAK,CAAC,KAAKhL,SAAS,EAAE;MAC/Bge,MAAM,CAAChT,KAAK,CAAC,GAAG,EAAE;IACpB;IAEA,IAAI,EAAEgT,MAAM,CAAChT,KAAK,CAAC,YAAY5L,KAAK,CAAC,EAAE;MACrC,MAAM0L,cAAc,CAAC,0CAA0C,EAAE;QAACE;MAAK,CAAC,CAAC;IAC3E;IAEA,IAAI,EAAEpI,GAAG,IAAIA,GAAG,CAAC+d,KAAK,CAAC,EAAE;MACvB;MACA5K,wBAAwB,CAACnT,GAAG,CAAC;MAE7Bob,MAAM,CAAChT,KAAK,CAAC,CAAChD,IAAI,CAACpF,GAAG,CAAC;MAEvB;IACF;;IAEA;IACA,MAAMge,MAAM,GAAGhe,GAAG,CAAC+d,KAAK;IACxB,IAAI,EAAEC,MAAM,YAAYxhB,KAAK,CAAC,EAAE;MAC9B,MAAM0L,cAAc,CAAC,wBAAwB,EAAE;QAACE;MAAK,CAAC,CAAC;IACzD;IAEA+K,wBAAwB,CAAC6K,MAAM,CAAC;;IAEhC;IACA,IAAIC,QAAQ,GAAG7gB,SAAS;IACxB,IAAI,WAAW,IAAI4C,GAAG,EAAE;MACtB,IAAI,OAAOA,GAAG,CAACke,SAAS,KAAK,QAAQ,EAAE;QACrC,MAAMhW,cAAc,CAAC,mCAAmC,EAAE;UAACE;QAAK,CAAC,CAAC;MACpE;;MAEA;MACA,IAAIpI,GAAG,CAACke,SAAS,GAAG,CAAC,EAAE;QACrB,MAAMhW,cAAc,CAClB,6CAA6C,EAC7C;UAACE;QAAK,CACR,CAAC;MACH;MAEA6V,QAAQ,GAAGje,GAAG,CAACke,SAAS;IAC1B;;IAEA;IACA,IAAIzW,KAAK,GAAGrK,SAAS;IACrB,IAAI,QAAQ,IAAI4C,GAAG,EAAE;MACnB,IAAI,OAAOA,GAAG,CAACme,MAAM,KAAK,QAAQ,EAAE;QAClC,MAAMjW,cAAc,CAAC,gCAAgC,EAAE;UAACE;QAAK,CAAC,CAAC;MACjE;;MAEA;MACAX,KAAK,GAAGzH,GAAG,CAACme,MAAM;IACpB;;IAEA;IACA,IAAIC,YAAY,GAAGhhB,SAAS;IAC5B,IAAI4C,GAAG,CAACqe,KAAK,EAAE;MACb,IAAI5W,KAAK,KAAKrK,SAAS,EAAE;QACvB,MAAM8K,cAAc,CAAC,qCAAqC,EAAE;UAACE;QAAK,CAAC,CAAC;MACtE;;MAEA;MACA;MACA;MACA;MACAgW,YAAY,GAAG,IAAI3S,SAAS,CAACK,MAAM,CAAC9L,GAAG,CAACqe,KAAK,CAAC,CAAC3M,aAAa,CAAC,CAAC;MAE9DsM,MAAM,CAAC9Y,OAAO,CAACX,OAAO,IAAI;QACxB,IAAI9I,eAAe,CAACqC,EAAE,CAACC,KAAK,CAACwG,OAAO,CAAC,KAAK,CAAC,EAAE;UAC3C,MAAM2D,cAAc,CAClB,8DAA8D,GAC9D,SAAS,EACT;YAACE;UAAK,CACR,CAAC;QACH;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI6V,QAAQ,KAAK7gB,SAAS,EAAE;MAC1B4gB,MAAM,CAAC9Y,OAAO,CAACX,OAAO,IAAI;QACxB6W,MAAM,CAAChT,KAAK,CAAC,CAAChD,IAAI,CAACb,OAAO,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAM+Z,eAAe,GAAG,CAACL,QAAQ,EAAE,CAAC,CAAC;MAErCD,MAAM,CAAC9Y,OAAO,CAACX,OAAO,IAAI;QACxB+Z,eAAe,CAAClZ,IAAI,CAACb,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF6W,MAAM,CAAChT,KAAK,CAAC,CAACwS,MAAM,CAAC,GAAG0D,eAAe,CAAC;IAC1C;;IAEA;IACA,IAAIF,YAAY,EAAE;MAChBhD,MAAM,CAAChT,KAAK,CAAC,CAAC8B,IAAI,CAACkU,YAAY,CAAC;IAClC;;IAEA;IACA,IAAI3W,KAAK,KAAKrK,SAAS,EAAE;MACvB,IAAIqK,KAAK,KAAK,CAAC,EAAE;QACf2T,MAAM,CAAChT,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;MACtB,CAAC,MAAM,IAAIX,KAAK,GAAG,CAAC,EAAE;QACpB2T,MAAM,CAAChT,KAAK,CAAC,GAAGgT,MAAM,CAAChT,KAAK,CAAC,CAACX,KAAK,CAACA,KAAK,CAAC;MAC5C,CAAC,MAAM;QACL2T,MAAM,CAAChT,KAAK,CAAC,GAAGgT,MAAM,CAAChT,KAAK,CAAC,CAACX,KAAK,CAAC,CAAC,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EACD8W,QAAQA,CAACnD,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IAC3B,IAAI,EAAE,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYxD,KAAK,CAAC,EAAE;MACtD,MAAM0L,cAAc,CAAC,mDAAmD,CAAC;IAC3E;IAEAiL,wBAAwB,CAACnT,GAAG,CAAC;IAE7B,MAAMge,MAAM,GAAG5C,MAAM,CAAChT,KAAK,CAAC;IAE5B,IAAI4V,MAAM,KAAK5gB,SAAS,EAAE;MACxBge,MAAM,CAAChT,KAAK,CAAC,GAAGpI,GAAG;IACrB,CAAC,MAAM,IAAI,EAAEge,MAAM,YAAYxhB,KAAK,CAAC,EAAE;MACrC,MAAM0L,cAAc,CAClB,6CAA6C,EAC7C;QAACE;MAAK,CACR,CAAC;IACH,CAAC,MAAM;MACL4V,MAAM,CAAC5Y,IAAI,CAAC,GAAGpF,GAAG,CAAC;IACrB;EACF,CAAC;EACDwe,SAASA,CAACpD,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IAC5B,IAAIye,MAAM,GAAG,KAAK;IAElB,IAAI,OAAOze,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACA,MAAMZ,IAAI,GAAGxD,MAAM,CAACwD,IAAI,CAACY,GAAG,CAAC;MAC7B,IAAIZ,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;QACvBqf,MAAM,GAAG,IAAI;MACf;IACF;IAEA,MAAMC,MAAM,GAAGD,MAAM,GAAGze,GAAG,CAAC+d,KAAK,GAAG,CAAC/d,GAAG,CAAC;IAEzCmT,wBAAwB,CAACuL,MAAM,CAAC;IAEhC,MAAMC,KAAK,GAAGvD,MAAM,CAAChT,KAAK,CAAC;IAC3B,IAAIuW,KAAK,KAAKvhB,SAAS,EAAE;MACvBge,MAAM,CAAChT,KAAK,CAAC,GAAGsW,MAAM;IACxB,CAAC,MAAM,IAAI,EAAEC,KAAK,YAAYniB,KAAK,CAAC,EAAE;MACpC,MAAM0L,cAAc,CAClB,8CAA8C,EAC9C;QAACE;MAAK,CACR,CAAC;IACH,CAAC,MAAM;MACLsW,MAAM,CAACxZ,OAAO,CAACpI,KAAK,IAAI;QACtB,IAAI6hB,KAAK,CAACthB,IAAI,CAACkH,OAAO,IAAI9I,eAAe,CAACqC,EAAE,CAACgH,MAAM,CAAChI,KAAK,EAAEyH,OAAO,CAAC,CAAC,EAAE;UACpE;QACF;QAEAoa,KAAK,CAACvZ,IAAI,CAACtI,KAAK,CAAC;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;EACD8hB,IAAIA,CAACxD,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACvB,IAAIob,MAAM,KAAKhe,SAAS,EAAE;MACxB;IACF;IAEA,MAAMyhB,KAAK,GAAGzD,MAAM,CAAChT,KAAK,CAAC;IAE3B,IAAIyW,KAAK,KAAKzhB,SAAS,EAAE;MACvB;IACF;IAEA,IAAI,EAAEyhB,KAAK,YAAYriB,KAAK,CAAC,EAAE;MAC7B,MAAM0L,cAAc,CAAC,yCAAyC,EAAE;QAACE;MAAK,CAAC,CAAC;IAC1E;IAEA,IAAI,OAAOpI,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG,CAAC,EAAE;MACtC6e,KAAK,CAACjE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACpB,CAAC,MAAM;MACLiE,KAAK,CAACpD,GAAG,CAAC,CAAC;IACb;EACF,CAAC;EACDqD,KAAKA,CAAC1D,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACxB,IAAIob,MAAM,KAAKhe,SAAS,EAAE;MACxB;IACF;IAEA,MAAM2hB,MAAM,GAAG3D,MAAM,CAAChT,KAAK,CAAC;IAC5B,IAAI2W,MAAM,KAAK3hB,SAAS,EAAE;MACxB;IACF;IAEA,IAAI,EAAE2hB,MAAM,YAAYviB,KAAK,CAAC,EAAE;MAC9B,MAAM0L,cAAc,CAClB,kDAAkD,EAClD;QAACE;MAAK,CACR,CAAC;IACH;IAEA,IAAI4W,GAAG;IACP,IAAIhf,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,EAAEA,GAAG,YAAYxD,KAAK,CAAC,EAAE;MACrE;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA,MAAMc,OAAO,GAAG,IAAImO,SAAS,CAACC,OAAO,CAAC1L,GAAG,CAAC;MAE1Cgf,GAAG,GAAGD,MAAM,CAAC1f,MAAM,CAACkF,OAAO,IAAI,CAACjH,OAAO,CAACmU,eAAe,CAAClN,OAAO,CAAC,CAACrE,MAAM,CAAC;IAC1E,CAAC,MAAM;MACL8e,GAAG,GAAGD,MAAM,CAAC1f,MAAM,CAACkF,OAAO,IAAI,CAAC9I,eAAe,CAACqC,EAAE,CAACgH,MAAM,CAACP,OAAO,EAAEvE,GAAG,CAAC,CAAC;IAC1E;IAEAob,MAAM,CAAChT,KAAK,CAAC,GAAG4W,GAAG;EACrB,CAAC;EACDC,QAAQA,CAAC7D,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IAC3B,IAAI,EAAE,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYxD,KAAK,CAAC,EAAE;MACtD,MAAM0L,cAAc,CAClB,mDAAmD,EACnD;QAACE;MAAK,CACR,CAAC;IACH;IAEA,IAAIgT,MAAM,KAAKhe,SAAS,EAAE;MACxB;IACF;IAEA,MAAM2hB,MAAM,GAAG3D,MAAM,CAAChT,KAAK,CAAC;IAE5B,IAAI2W,MAAM,KAAK3hB,SAAS,EAAE;MACxB;IACF;IAEA,IAAI,EAAE2hB,MAAM,YAAYviB,KAAK,CAAC,EAAE;MAC9B,MAAM0L,cAAc,CAClB,kDAAkD,EAClD;QAACE;MAAK,CACR,CAAC;IACH;IAEAgT,MAAM,CAAChT,KAAK,CAAC,GAAG2W,MAAM,CAAC1f,MAAM,CAACwK,MAAM,IAClC,CAAC7J,GAAG,CAAC3C,IAAI,CAACkH,OAAO,IAAI9I,eAAe,CAACqC,EAAE,CAACgH,MAAM,CAAC+E,MAAM,EAAEtF,OAAO,CAAC,CACjE,CAAC;EACH,CAAC;EACD2a,IAAIA,CAAC9D,MAAM,EAAEhT,KAAK,EAAEpI,GAAG,EAAE;IACvB;IACA;IACA,MAAMkI,cAAc,CAAC,uBAAuB,EAAE;MAACE;IAAK,CAAC,CAAC;EACxD,CAAC;EACD+W,EAAEA,CAAA,EAAG;IACH;IACA;IACA;IACA;EAAA;AAEJ,CAAC;AAED,MAAM3D,mBAAmB,GAAG;EAC1BoD,IAAI,EAAE,IAAI;EACVE,KAAK,EAAE,IAAI;EACXG,QAAQ,EAAE,IAAI;EACdxB,OAAO,EAAE,IAAI;EACbI,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA,MAAMuB,cAAc,GAAG;EACrBC,CAAC,EAAE,kBAAkB;EACrB,GAAG,EAAE,eAAe;EACpB,IAAI,EAAE;AACR,CAAC;;AAED;AACA,SAASlM,wBAAwBA,CAAC1S,GAAG,EAAE;EACrC,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAClCuG,IAAI,CAACC,SAAS,CAACxG,GAAG,EAAE,CAACnB,GAAG,EAAExC,KAAK,KAAK;MAClCwiB,sBAAsB,CAAChgB,GAAG,CAAC;MAC3B,OAAOxC,KAAK;IACd,CAAC,CAAC;EACJ;AACF;AAEA,SAASwiB,sBAAsBA,CAAChgB,GAAG,EAAE;EACnC,IAAIiE,KAAK;EACT,IAAI,OAAOjE,GAAG,KAAK,QAAQ,KAAKiE,KAAK,GAAGjE,GAAG,CAACiE,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;IAC/D,MAAM2E,cAAc,QAAArK,MAAA,CAAQyB,GAAG,gBAAAzB,MAAA,CAAauhB,cAAc,CAAC7b,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;EACzE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8X,aAAaA,CAAC5a,GAAG,EAAE0a,QAAQ,EAAgB;EAAA,IAAdxX,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAChD,IAAI2b,cAAc,GAAG,KAAK;EAE1B,KAAK,IAAIhhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4c,QAAQ,CAACze,MAAM,EAAE6B,CAAC,EAAE,EAAE;IACxC,MAAMihB,IAAI,GAAGjhB,CAAC,KAAK4c,QAAQ,CAACze,MAAM,GAAG,CAAC;IACtC,IAAI+iB,OAAO,GAAGtE,QAAQ,CAAC5c,CAAC,CAAC;IAEzB,IAAI,CAACvD,WAAW,CAACyF,GAAG,CAAC,EAAE;MACrB,IAAIkD,OAAO,CAAC4X,QAAQ,EAAE;QACpB,OAAOne,SAAS;MAClB;MAEA,MAAMiL,KAAK,GAAGH,cAAc,yBAAArK,MAAA,CACF4hB,OAAO,oBAAA5hB,MAAA,CAAiB4C,GAAG,CACrD,CAAC;MACD4H,KAAK,CAACsV,gBAAgB,GAAG,IAAI;MAC7B,MAAMtV,KAAK;IACb;IAEA,IAAI5H,GAAG,YAAYjE,KAAK,EAAE;MACxB,IAAImH,OAAO,CAAC2X,WAAW,EAAE;QACvB,OAAO,IAAI;MACb;MAEA,IAAImE,OAAO,KAAK,GAAG,EAAE;QACnB,IAAIF,cAAc,EAAE;UAClB,MAAMrX,cAAc,CAAC,2CAA2C,CAAC;QACnE;QAEA,IAAI,CAACvE,OAAO,CAACR,YAAY,IAAI,CAACQ,OAAO,CAACR,YAAY,CAACzG,MAAM,EAAE;UACzD,MAAMwL,cAAc,CAClB,iEAAiE,GACjE,OACF,CAAC;QACH;QAEAuX,OAAO,GAAG9b,OAAO,CAACR,YAAY,CAAC,CAAC,CAAC;QACjCoc,cAAc,GAAG,IAAI;MACvB,CAAC,MAAM,IAAItkB,YAAY,CAACwkB,OAAO,CAAC,EAAE;QAChCA,OAAO,GAAGC,QAAQ,CAACD,OAAO,CAAC;MAC7B,CAAC,MAAM;QACL,IAAI9b,OAAO,CAAC4X,QAAQ,EAAE;UACpB,OAAOne,SAAS;QAClB;QAEA,MAAM8K,cAAc,mDAAArK,MAAA,CACgC4hB,OAAO,MAC3D,CAAC;MACH;MAEA,IAAID,IAAI,EAAE;QACRrE,QAAQ,CAAC5c,CAAC,CAAC,GAAGkhB,OAAO,CAAC,CAAC;MACzB;MAEA,IAAI9b,OAAO,CAAC4X,QAAQ,IAAIkE,OAAO,IAAIhf,GAAG,CAAC/D,MAAM,EAAE;QAC7C,OAAOU,SAAS;MAClB;MAEA,OAAOqD,GAAG,CAAC/D,MAAM,GAAG+iB,OAAO,EAAE;QAC3Bhf,GAAG,CAAC2E,IAAI,CAAC,IAAI,CAAC;MAChB;MAEA,IAAI,CAACoa,IAAI,EAAE;QACT,IAAI/e,GAAG,CAAC/D,MAAM,KAAK+iB,OAAO,EAAE;UAC1Bhf,GAAG,CAAC2E,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,MAAM,IAAI,OAAO3E,GAAG,CAACgf,OAAO,CAAC,KAAK,QAAQ,EAAE;UAC3C,MAAMvX,cAAc,CAClB,uBAAArK,MAAA,CAAuBsd,QAAQ,CAAC5c,CAAC,GAAG,CAAC,CAAC,wBACtCyI,IAAI,CAACC,SAAS,CAACxG,GAAG,CAACgf,OAAO,CAAC,CAC7B,CAAC;QACH;MACF;IACF,CAAC,MAAM;MACLH,sBAAsB,CAACG,OAAO,CAAC;MAE/B,IAAI,EAAEA,OAAO,IAAIhf,GAAG,CAAC,EAAE;QACrB,IAAIkD,OAAO,CAAC4X,QAAQ,EAAE;UACpB,OAAOne,SAAS;QAClB;QAEA,IAAI,CAACoiB,IAAI,EAAE;UACT/e,GAAG,CAACgf,OAAO,CAAC,GAAG,CAAC,CAAC;QACnB;MACF;IACF;IAEA,IAAID,IAAI,EAAE;MACR,OAAO/e,GAAG;IACZ;IAEAA,GAAG,GAAGA,GAAG,CAACgf,OAAO,CAAC;EACpB;;EAEA;AACF,C;;;;;;;;;;;;AC93EAjlB,MAAM,CAACE,MAAM,CAAC;EAACgB,OAAO,EAACA,CAAA,KAAIgQ;AAAO,CAAC,CAAC;AAAC,IAAIjQ,eAAe;AAACjB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAACF,eAAe,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAId,uBAAuB,EAACF,MAAM,EAACS,cAAc;AAACZ,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACI,uBAAuBA,CAACc,CAAC,EAAC;IAACd,uBAAuB,GAACc,CAAC;EAAA,CAAC;EAAChB,MAAMA,CAACgB,CAAC,EAAC;IAAChB,MAAM,GAACgB,CAAC;EAAA,CAAC;EAACP,cAAcA,CAACO,CAAC,EAAC;IAACP,cAAc,GAACO,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAO9T,MAAMgkB,OAAO,GAAG,EAAAC,oBAAA,GAAA/N,OAAO,CAAC,eAAe,CAAC,cAAA+N,oBAAA,uBAAxBA,oBAAA,CAA0BD,OAAO,KAAI,MAAME,WAAW,CAAC,EAAE;;AAEzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACe,MAAMnU,OAAO,CAAC;EAC3BJ,WAAWA,CAACjG,QAAQ,EAAEya,QAAQ,EAAE;IAC9B;IACA;IACA;IACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;IACA,IAAI,CAAC5d,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACpB,SAAS,GAAG,KAAK;IACtB;IACA;IACA;IACA,IAAI,CAACgD,SAAS,GAAG,IAAI;IACrB;IACA;IACA,IAAI,CAACic,iBAAiB,GAAG5iB,SAAS;IAClC;IACA;IACA;IACA;IACA,IAAI,CAAC6iB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC9a,QAAQ,CAAC;IAClD;IACA;IACA;IACA,IAAI,CAACnC,SAAS,GAAG4c,QAAQ;EAC3B;EAEArO,eAAeA,CAAChR,GAAG,EAAE;IACnB,IAAIA,GAAG,KAAK7E,MAAM,CAAC6E,GAAG,CAAC,EAAE;MACvB,MAAM9D,KAAK,CAAC,kCAAkC,CAAC;IACjD;IAEA,OAAO,IAAI,CAACujB,WAAW,CAACzf,GAAG,CAAC;EAC9B;EAEAoL,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1J,YAAY;EAC1B;EAEAie,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACrf,SAAS;EACvB;EAEAsf,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACtc,SAAS;EACvB;;EAEA;EACA;EACAoc,gBAAgBA,CAAC9a,QAAQ,EAAE;IACzB;IACA,IAAIA,QAAQ,YAAYrE,QAAQ,EAAE;MAChC,IAAI,CAAC+C,SAAS,GAAG,KAAK;MACtB,IAAI,CAACkc,SAAS,GAAG5a,QAAQ;MACzB,IAAI,CAACvE,eAAe,CAAC,EAAE,CAAC;MAExB,OAAOL,GAAG,KAAK;QAACP,MAAM,EAAE,CAAC,CAACmF,QAAQ,CAACzH,IAAI,CAAC6C,GAAG;MAAC,CAAC,CAAC;IAChD;;IAEA;IACA,IAAIhF,eAAe,CAACsO,aAAa,CAAC1E,QAAQ,CAAC,EAAE;MAC3C,IAAI,CAAC4a,SAAS,GAAG;QAAC9V,GAAG,EAAE9E;MAAQ,CAAC;MAChC,IAAI,CAACvE,eAAe,CAAC,KAAK,CAAC;MAE3B,OAAOL,GAAG,KAAK;QAACP,MAAM,EAAEyF,KAAK,CAAC8O,MAAM,CAAChU,GAAG,CAAC0J,GAAG,EAAE9E,QAAQ;MAAC,CAAC,CAAC;IAC3D;;IAEA;IACA;IACA;IACA,IAAI,CAACA,QAAQ,IAAI1K,MAAM,CAACiD,IAAI,CAACyH,QAAQ,EAAE,KAAK,CAAC,IAAI,CAACA,QAAQ,CAAC8E,GAAG,EAAE;MAC9D,IAAI,CAACpG,SAAS,GAAG,KAAK;MACtB,OAAO3I,cAAc;IACvB;;IAEA;IACA,IAAIoB,KAAK,CAACC,OAAO,CAAC4I,QAAQ,CAAC,IACvBM,KAAK,CAACC,QAAQ,CAACP,QAAQ,CAAC,IACxB,OAAOA,QAAQ,KAAK,SAAS,EAAE;MACjC,MAAM,IAAI1I,KAAK,sBAAAkB,MAAA,CAAsBwH,QAAQ,CAAE,CAAC;IAClD;IAEA,IAAI,CAAC4a,SAAS,GAAGta,KAAK,CAACgK,KAAK,CAACtK,QAAQ,CAAC;IAEtC,OAAOxK,uBAAuB,CAACwK,QAAQ,EAAE,IAAI,EAAE;MAACnD,MAAM,EAAE;IAAI,CAAC,CAAC;EAChE;;EAEA;EACA;EACAoe,SAASA,CAAA,EAAG;IACV,OAAO1kB,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC2gB,MAAM,CAAC;EACjC;EAEAjf,eAAeA,CAACmI,IAAI,EAAE;IACpB,IAAI,CAAC8W,MAAM,CAAC9W,IAAI,CAAC,GAAG,IAAI;EAC1B;AACF;AAEA;AACAxN,eAAe,CAACqC,EAAE,GAAG;EACnB;EACAC,KAAKA,CAACpC,CAAC,EAAE;IACP,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO,CAAC;IACV;IAEA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO,CAAC;IACV;IAEA,IAAI,OAAOA,CAAC,KAAK,SAAS,EAAE;MAC1B,OAAO,CAAC;IACV;IAEA,IAAIa,KAAK,CAACC,OAAO,CAACd,CAAC,CAAC,EAAE;MACpB,OAAO,CAAC;IACV;IAEA,IAAIA,CAAC,KAAK,IAAI,EAAE;MACd,OAAO,EAAE;IACX;;IAEA;IACA,IAAIA,CAAC,YAAYwB,MAAM,EAAE;MACvB,OAAO,EAAE;IACX;IAEA,IAAI,OAAOxB,CAAC,KAAK,UAAU,EAAE;MAC3B,OAAO,EAAE;IACX;IAEA,IAAIA,CAAC,YAAYyhB,IAAI,EAAE;MACrB,OAAO,CAAC;IACV;IAEA,IAAIzX,KAAK,CAACC,QAAQ,CAACjK,CAAC,CAAC,EAAE;MACrB,OAAO,CAAC;IACV;IAEA,IAAIA,CAAC,YAAY0X,OAAO,CAACC,QAAQ,EAAE;MACjC,OAAO,CAAC;IACV;IAEA,IAAI3X,CAAC,YAAYgkB,OAAO,EAAE;MACxB,OAAO,CAAC;IACV;;IAEA;IACA,OAAO,CAAC;;IAER;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC;EAED;EACA7a,MAAMA,CAACrF,CAAC,EAAEC,CAAC,EAAE;IACX,OAAOiG,KAAK,CAAC8O,MAAM,CAAChV,CAAC,EAAEC,CAAC,EAAE;MAAC6gB,iBAAiB,EAAE;IAAI,CAAC,CAAC;EACtD,CAAC;EAED;EACA;EACAC,UAAUA,CAACC,CAAC,EAAE;IACZ;IACA;IACA;IACA;IACA,OAAO,CACL,CAAC,CAAC;IAAG;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC,CAAC;IAAG;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC,CAAC;IAAG;IACL,GAAG;IAAE;IACL,CAAC;IAAI;IACL,GAAG;IAAE;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC,CAAI;IAAA,CACN,CAACA,CAAC,CAAC;EACN,CAAC;EAED;EACA;EACA;EACA;EACArZ,IAAIA,CAAC3H,CAAC,EAAEC,CAAC,EAAE;IACT,IAAID,CAAC,KAAKrC,SAAS,EAAE;MACnB,OAAOsC,CAAC,KAAKtC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC;IAEA,IAAIsC,CAAC,KAAKtC,SAAS,EAAE;MACnB,OAAO,CAAC;IACV;IAEA,IAAIsjB,EAAE,GAAGjlB,eAAe,CAACqC,EAAE,CAACC,KAAK,CAAC0B,CAAC,CAAC;IACpC,IAAIkhB,EAAE,GAAGllB,eAAe,CAACqC,EAAE,CAACC,KAAK,CAAC2B,CAAC,CAAC;IAEpC,MAAMkhB,EAAE,GAAGnlB,eAAe,CAACqC,EAAE,CAAC0iB,UAAU,CAACE,EAAE,CAAC;IAC5C,MAAMG,EAAE,GAAGplB,eAAe,CAACqC,EAAE,CAAC0iB,UAAU,CAACG,EAAE,CAAC;IAE5C,IAAIC,EAAE,KAAKC,EAAE,EAAE;MACb,OAAOD,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;IACzB;;IAEA;IACA;IACA,IAAIH,EAAE,KAAKC,EAAE,EAAE;MACb,MAAMhkB,KAAK,CAAC,qCAAqC,CAAC;IACpD;IAEA,IAAI+jB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACAA,EAAE,GAAGC,EAAE,GAAG,CAAC;MACXlhB,CAAC,GAAGA,CAAC,CAACqhB,WAAW,CAAC,CAAC;MACnBphB,CAAC,GAAGA,CAAC,CAACohB,WAAW,CAAC,CAAC;IACrB;IAEA,IAAIJ,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACAA,EAAE,GAAGC,EAAE,GAAG,CAAC;MACXlhB,CAAC,GAAGshB,KAAK,CAACthB,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAACuhB,OAAO,CAAC,CAAC;MAC9BthB,CAAC,GAAGqhB,KAAK,CAACrhB,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAACshB,OAAO,CAAC,CAAC;IAChC;IAEA,IAAIN,EAAE,KAAK,CAAC,EAAE;MAAE;MACd,IAAIjhB,CAAC,YAAYkgB,OAAO,EAAE;QACxB,OAAOlgB,CAAC,CAACwhB,KAAK,CAACvhB,CAAC,CAAC,CAACwhB,QAAQ,CAAC,CAAC;MAC9B,CAAC,MAAM;QACL,OAAOzhB,CAAC,GAAGC,CAAC;MACd;IACF;IAEA,IAAIihB,EAAE,KAAK,CAAC;MAAE;MACZ,OAAOlhB,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAG,CAAC;IAErC,IAAIghB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACA,MAAMS,OAAO,GAAGtX,MAAM,IAAI;QACxB,MAAM3J,MAAM,GAAG,EAAE;QAEjBtE,MAAM,CAACwD,IAAI,CAACyK,MAAM,CAAC,CAAC3E,OAAO,CAAC5F,GAAG,IAAI;UACjCY,MAAM,CAACkF,IAAI,CAAC9F,GAAG,EAAEuK,MAAM,CAACvK,GAAG,CAAC,CAAC;QAC/B,CAAC,CAAC;QAEF,OAAOY,MAAM;MACf,CAAC;MAED,OAAOzE,eAAe,CAACqC,EAAE,CAACsJ,IAAI,CAAC+Z,OAAO,CAAC1hB,CAAC,CAAC,EAAE0hB,OAAO,CAACzhB,CAAC,CAAC,CAAC;IACxD;IAEA,IAAIghB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd,KAAK,IAAIniB,CAAC,GAAG,CAAC,GAAIA,CAAC,EAAE,EAAE;QACrB,IAAIA,CAAC,KAAKkB,CAAC,CAAC/C,MAAM,EAAE;UAClB,OAAO6B,CAAC,KAAKmB,CAAC,CAAChD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAChC;QAEA,IAAI6B,CAAC,KAAKmB,CAAC,CAAChD,MAAM,EAAE;UAClB,OAAO,CAAC;QACV;QAEA,MAAMiK,CAAC,GAAGlL,eAAe,CAACqC,EAAE,CAACsJ,IAAI,CAAC3H,CAAC,CAAClB,CAAC,CAAC,EAAEmB,CAAC,CAACnB,CAAC,CAAC,CAAC;QAC7C,IAAIoI,CAAC,KAAK,CAAC,EAAE;UACX,OAAOA,CAAC;QACV;MACF;IACF;IAEA,IAAI+Z,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACA;MACA,IAAIjhB,CAAC,CAAC/C,MAAM,KAAKgD,CAAC,CAAChD,MAAM,EAAE;QACzB,OAAO+C,CAAC,CAAC/C,MAAM,GAAGgD,CAAC,CAAChD,MAAM;MAC5B;MAEA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,CAAC/C,MAAM,EAAE6B,CAAC,EAAE,EAAE;QACjC,IAAIkB,CAAC,CAAClB,CAAC,CAAC,GAAGmB,CAAC,CAACnB,CAAC,CAAC,EAAE;UACf,OAAO,CAAC,CAAC;QACX;QAEA,IAAIkB,CAAC,CAAClB,CAAC,CAAC,GAAGmB,CAAC,CAACnB,CAAC,CAAC,EAAE;UACf,OAAO,CAAC;QACV;MACF;MAEA,OAAO,CAAC;IACV;IAEA,IAAImiB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd,IAAIjhB,CAAC,EAAE;QACL,OAAOC,CAAC,GAAG,CAAC,GAAG,CAAC;MAClB;MAEA,OAAOA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACnB;IAEA,IAAIghB,EAAE,KAAK,EAAE;MAAE;MACb,OAAO,CAAC;IAEV,IAAIA,EAAE,KAAK,EAAE;MAAE;MACb,MAAM/jB,KAAK,CAAC,6CAA6C,CAAC,CAAC,CAAC;;IAE9D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI+jB,EAAE,KAAK,EAAE;MAAE;MACb,MAAM/jB,KAAK,CAAC,0CAA0C,CAAC,CAAC,CAAC;;IAE3D,MAAMA,KAAK,CAAC,sBAAsB,CAAC;EACrC;AACF,CAAC,C;;;;;;;;;;;ACtWD,IAAIykB,gBAAgB;AAAC5mB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAACylB,gBAAgB,GAACzlB,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI+P,OAAO;AAAClR,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAAC+P,OAAO,GAAC/P,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAImQ,MAAM;AAACtR,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACiB,OAAOA,CAACC,CAAC,EAAC;IAACmQ,MAAM,GAACnQ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAI7NF,eAAe,GAAG2lB,gBAAgB;AAClC3V,SAAS,GAAG;EACRhQ,eAAe,EAAE2lB,gBAAgB;EACjC1V,OAAO;EACPI;AACJ,CAAC,C;;;;;;;;;;;ACTDtR,MAAM,CAACE,MAAM,CAAC;EAACgB,OAAO,EAACA,CAAA,KAAIoU;AAAa,CAAC,CAAC;AAC3B,MAAMA,aAAa,CAAC,E;;;;;;;;;;;ACDnCtV,MAAM,CAACE,MAAM,CAAC;EAACgB,OAAO,EAACA,CAAA,KAAIoQ;AAAM,CAAC,CAAC;AAAC,IAAIlR,iBAAiB,EAACE,sBAAsB,EAACC,sBAAsB,EAACJ,MAAM,EAACO,gBAAgB,EAACC,kBAAkB,EAACK,oBAAoB;AAAChB,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACG,iBAAiBA,CAACe,CAAC,EAAC;IAACf,iBAAiB,GAACe,CAAC;EAAA,CAAC;EAACb,sBAAsBA,CAACa,CAAC,EAAC;IAACb,sBAAsB,GAACa,CAAC;EAAA,CAAC;EAACZ,sBAAsBA,CAACY,CAAC,EAAC;IAACZ,sBAAsB,GAACY,CAAC;EAAA,CAAC;EAAChB,MAAMA,CAACgB,CAAC,EAAC;IAAChB,MAAM,GAACgB,CAAC;EAAA,CAAC;EAACT,gBAAgBA,CAACS,CAAC,EAAC;IAACT,gBAAgB,GAACS,CAAC;EAAA,CAAC;EAACR,kBAAkBA,CAACQ,CAAC,EAAC;IAACR,kBAAkB,GAACQ,CAAC;EAAA,CAAC;EAACH,oBAAoBA,CAACG,CAAC,EAAC;IAACH,oBAAoB,GAACG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAuBje,MAAMmQ,MAAM,CAAC;EAC1BR,WAAWA,CAAC+V,IAAI,EAAE;IAChB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IAEzB,MAAMC,WAAW,GAAGA,CAACvY,IAAI,EAAEwY,SAAS,KAAK;MACvC,IAAI,CAACxY,IAAI,EAAE;QACT,MAAMtM,KAAK,CAAC,6BAA6B,CAAC;MAC5C;MAEA,IAAIsM,IAAI,CAACyY,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC1B,MAAM/kB,KAAK,0BAAAkB,MAAA,CAA0BoL,IAAI,CAAE,CAAC;MAC9C;MAEA,IAAI,CAACqY,cAAc,CAAClc,IAAI,CAAC;QACvBqc,SAAS;QACTE,MAAM,EAAExmB,kBAAkB,CAAC8N,IAAI,EAAE;UAAClB,OAAO,EAAE;QAAI,CAAC,CAAC;QACjDkB;MACF,CAAC,CAAC;IACJ,CAAC;IAED,IAAIoY,IAAI,YAAY7kB,KAAK,EAAE;MACzB6kB,IAAI,CAACnc,OAAO,CAACX,OAAO,IAAI;QACtB,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAC/Bid,WAAW,CAACjd,OAAO,EAAE,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLid,WAAW,CAACjd,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;QAChD;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAO8c,IAAI,KAAK,QAAQ,EAAE;MACnCzlB,MAAM,CAACwD,IAAI,CAACiiB,IAAI,CAAC,CAACnc,OAAO,CAAC5F,GAAG,IAAI;QAC/BkiB,WAAW,CAACliB,GAAG,EAAE+hB,IAAI,CAAC/hB,GAAG,CAAC,IAAI,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAO+hB,IAAI,KAAK,UAAU,EAAE;MACrC,IAAI,CAACE,aAAa,GAAGF,IAAI;IAC3B,CAAC,MAAM;MACL,MAAM1kB,KAAK,4BAAAkB,MAAA,CAA4BmJ,IAAI,CAACC,SAAS,CAACoa,IAAI,CAAC,CAAE,CAAC;IAChE;;IAEA;IACA,IAAI,IAAI,CAACE,aAAa,EAAE;MACtB;IACF;;IAEA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACK,kBAAkB,EAAE;MAC3B,MAAMvc,QAAQ,GAAG,CAAC,CAAC;MAEnB,IAAI,CAACic,cAAc,CAACpc,OAAO,CAACmc,IAAI,IAAI;QAClChc,QAAQ,CAACgc,IAAI,CAACpY,IAAI,CAAC,GAAG,CAAC;MACzB,CAAC,CAAC;MAEF,IAAI,CAAC4Y,8BAA8B,GAAG,IAAIpW,SAAS,CAACC,OAAO,CAACrG,QAAQ,CAAC;IACvE;IAEA,IAAI,CAACyc,cAAc,GAAGC,kBAAkB,CACtC,IAAI,CAACT,cAAc,CAACrkB,GAAG,CAAC,CAACokB,IAAI,EAAE9iB,CAAC,KAAK,IAAI,CAACyjB,mBAAmB,CAACzjB,CAAC,CAAC,CAClE,CAAC;EACH;EAEAmT,aAAaA,CAAC/N,OAAO,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC2d,cAAc,CAAC5kB,MAAM,IAAI,CAACiH,OAAO,IAAI,CAACA,OAAO,CAAC2K,SAAS,EAAE;MAChE,OAAO,IAAI,CAAC2T,kBAAkB,CAAC,CAAC;IAClC;IAEA,MAAM3T,SAAS,GAAG3K,OAAO,CAAC2K,SAAS;;IAEnC;IACA,OAAO,CAAC7O,CAAC,EAAEC,CAAC,KAAK;MACf,IAAI,CAAC4O,SAAS,CAACkF,GAAG,CAAC/T,CAAC,CAAC0K,GAAG,CAAC,EAAE;QACzB,MAAMxN,KAAK,yBAAAkB,MAAA,CAAyB4B,CAAC,CAAC0K,GAAG,CAAE,CAAC;MAC9C;MAEA,IAAI,CAACmE,SAAS,CAACkF,GAAG,CAAC9T,CAAC,CAACyK,GAAG,CAAC,EAAE;QACzB,MAAMxN,KAAK,yBAAAkB,MAAA,CAAyB6B,CAAC,CAACyK,GAAG,CAAE,CAAC;MAC9C;MAEA,OAAOmE,SAAS,CAAC4C,GAAG,CAACzR,CAAC,CAAC0K,GAAG,CAAC,GAAGmE,SAAS,CAAC4C,GAAG,CAACxR,CAAC,CAACyK,GAAG,CAAC;IACpD,CAAC;EACH;;EAEA;EACA;EACA;EACA+X,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACvB,IAAID,IAAI,CAACzlB,MAAM,KAAK,IAAI,CAAC4kB,cAAc,CAAC5kB,MAAM,IAC1C0lB,IAAI,CAAC1lB,MAAM,KAAK,IAAI,CAAC4kB,cAAc,CAAC5kB,MAAM,EAAE;MAC9C,MAAMC,KAAK,CAAC,sBAAsB,CAAC;IACrC;IAEA,OAAO,IAAI,CAACmlB,cAAc,CAACK,IAAI,EAAEC,IAAI,CAAC;EACxC;;EAEA;EACA;EACAC,oBAAoBA,CAAC5hB,GAAG,EAAE6hB,EAAE,EAAE;IAC5B,IAAI,IAAI,CAAChB,cAAc,CAAC5kB,MAAM,KAAK,CAAC,EAAE;MACpC,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;IACxD;IAEA,MAAM4lB,eAAe,GAAGzG,OAAO,OAAAje,MAAA,CAAOie,OAAO,CAACpU,IAAI,CAAC,GAAG,CAAC,MAAG;IAE1D,IAAI8a,UAAU,GAAG,IAAI;;IAErB;IACA,MAAMC,oBAAoB,GAAG,IAAI,CAACnB,cAAc,CAACrkB,GAAG,CAACokB,IAAI,IAAI;MAC3D;MACA;MACA,IAAIhd,QAAQ,GAAGtJ,sBAAsB,CAACsmB,IAAI,CAACM,MAAM,CAAClhB,GAAG,CAAC,EAAE,IAAI,CAAC;;MAE7D;MACA;MACA,IAAI,CAAC4D,QAAQ,CAAC3H,MAAM,EAAE;QACpB2H,QAAQ,GAAG,CAAC;UAAEvH,KAAK,EAAE,KAAK;QAAE,CAAC,CAAC;MAChC;MAEA,MAAMyH,OAAO,GAAG3I,MAAM,CAAC6W,MAAM,CAAC,IAAI,CAAC;MACnC,IAAIiQ,SAAS,GAAG,KAAK;MAErBre,QAAQ,CAACa,OAAO,CAAClC,MAAM,IAAI;QACzB,IAAI,CAACA,MAAM,CAACG,YAAY,EAAE;UACxB;UACA;UACA;UACA,IAAIkB,QAAQ,CAAC3H,MAAM,GAAG,CAAC,EAAE;YACvB,MAAMC,KAAK,CAAC,sCAAsC,CAAC;UACrD;UAEA4H,OAAO,CAAC,EAAE,CAAC,GAAGvB,MAAM,CAAClG,KAAK;UAC1B;QACF;QAEA4lB,SAAS,GAAG,IAAI;QAEhB,MAAMzZ,IAAI,GAAGsZ,eAAe,CAACvf,MAAM,CAACG,YAAY,CAAC;QAEjD,IAAIxI,MAAM,CAACiD,IAAI,CAAC2G,OAAO,EAAE0E,IAAI,CAAC,EAAE;UAC9B,MAAMtM,KAAK,oBAAAkB,MAAA,CAAoBoL,IAAI,CAAE,CAAC;QACxC;QAEA1E,OAAO,CAAC0E,IAAI,CAAC,GAAGjG,MAAM,CAAClG,KAAK;;QAE5B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI0lB,UAAU,IAAI,CAAC7nB,MAAM,CAACiD,IAAI,CAAC4kB,UAAU,EAAEvZ,IAAI,CAAC,EAAE;UAChD,MAAMtM,KAAK,CAAC,8BAA8B,CAAC;QAC7C;MACF,CAAC,CAAC;MAEF,IAAI6lB,UAAU,EAAE;QACd;QACA;QACA,IAAI,CAAC7nB,MAAM,CAACiD,IAAI,CAAC2G,OAAO,EAAE,EAAE,CAAC,IACzB3I,MAAM,CAACwD,IAAI,CAACojB,UAAU,CAAC,CAAC9lB,MAAM,KAAKd,MAAM,CAACwD,IAAI,CAACmF,OAAO,CAAC,CAAC7H,MAAM,EAAE;UAClE,MAAMC,KAAK,CAAC,+BAA+B,CAAC;QAC9C;MACF,CAAC,MAAM,IAAI+lB,SAAS,EAAE;QACpBF,UAAU,GAAG,CAAC,CAAC;QAEf5mB,MAAM,CAACwD,IAAI,CAACmF,OAAO,CAAC,CAACW,OAAO,CAAC+D,IAAI,IAAI;UACnCuZ,UAAU,CAACvZ,IAAI,CAAC,GAAG,IAAI;QACzB,CAAC,CAAC;MACJ;MAEA,OAAO1E,OAAO;IAChB,CAAC,CAAC;IAEF,IAAI,CAACie,UAAU,EAAE;MACf;MACA,MAAMG,OAAO,GAAGF,oBAAoB,CAACxlB,GAAG,CAACyhB,MAAM,IAAI;QACjD,IAAI,CAAC/jB,MAAM,CAACiD,IAAI,CAAC8gB,MAAM,EAAE,EAAE,CAAC,EAAE;UAC5B,MAAM/hB,KAAK,CAAC,4BAA4B,CAAC;QAC3C;QAEA,OAAO+hB,MAAM,CAAC,EAAE,CAAC;MACnB,CAAC,CAAC;MAEF4D,EAAE,CAACK,OAAO,CAAC;MAEX;IACF;IAEA/mB,MAAM,CAACwD,IAAI,CAACojB,UAAU,CAAC,CAACtd,OAAO,CAAC+D,IAAI,IAAI;MACtC,MAAM3J,GAAG,GAAGmjB,oBAAoB,CAACxlB,GAAG,CAACyhB,MAAM,IAAI;QAC7C,IAAI/jB,MAAM,CAACiD,IAAI,CAAC8gB,MAAM,EAAE,EAAE,CAAC,EAAE;UAC3B,OAAOA,MAAM,CAAC,EAAE,CAAC;QACnB;QAEA,IAAI,CAAC/jB,MAAM,CAACiD,IAAI,CAAC8gB,MAAM,EAAEzV,IAAI,CAAC,EAAE;UAC9B,MAAMtM,KAAK,CAAC,eAAe,CAAC;QAC9B;QAEA,OAAO+hB,MAAM,CAACzV,IAAI,CAAC;MACrB,CAAC,CAAC;MAEFqZ,EAAE,CAAChjB,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;;EAEA;EACA;EACA2iB,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACV,aAAa,EAAE;MACtB,OAAO,IAAI,CAACA,aAAa;IAC3B;;IAEA;IACA;IACA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC5kB,MAAM,EAAE;MAC/B,OAAO,CAACkmB,IAAI,EAAEC,IAAI,KAAK,CAAC;IAC1B;IAEA,OAAO,CAACD,IAAI,EAAEC,IAAI,KAAK;MACrB,MAAMV,IAAI,GAAG,IAAI,CAACW,iBAAiB,CAACF,IAAI,CAAC;MACzC,MAAMR,IAAI,GAAG,IAAI,CAACU,iBAAiB,CAACD,IAAI,CAAC;MACzC,OAAO,IAAI,CAACX,YAAY,CAACC,IAAI,EAAEC,IAAI,CAAC;IACtC,CAAC;EACH;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAU,iBAAiBA,CAACriB,GAAG,EAAE;IACrB,IAAIsiB,MAAM,GAAG,IAAI;IAEjB,IAAI,CAACV,oBAAoB,CAAC5hB,GAAG,EAAEnB,GAAG,IAAI;MACpC,IAAIyjB,MAAM,KAAK,IAAI,EAAE;QACnBA,MAAM,GAAGzjB,GAAG;QACZ;MACF;MAEA,IAAI,IAAI,CAAC4iB,YAAY,CAAC5iB,GAAG,EAAEyjB,MAAM,CAAC,GAAG,CAAC,EAAE;QACtCA,MAAM,GAAGzjB,GAAG;MACd;IACF,CAAC,CAAC;IAEF,OAAOyjB,MAAM;EACf;EAEAzC,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACgB,cAAc,CAACrkB,GAAG,CAAC+lB,IAAI,IAAIA,IAAI,CAAC/Z,IAAI,CAAC;EACnD;;EAEA;EACA;EACA+Y,mBAAmBA,CAACzjB,CAAC,EAAE;IACrB,MAAM0kB,MAAM,GAAG,CAAC,IAAI,CAAC3B,cAAc,CAAC/iB,CAAC,CAAC,CAACkjB,SAAS;IAEhD,OAAO,CAACU,IAAI,EAAEC,IAAI,KAAK;MACrB,MAAMc,OAAO,GAAGznB,eAAe,CAACqC,EAAE,CAACsJ,IAAI,CAAC+a,IAAI,CAAC5jB,CAAC,CAAC,EAAE6jB,IAAI,CAAC7jB,CAAC,CAAC,CAAC;MACzD,OAAO0kB,MAAM,GAAG,CAACC,OAAO,GAAGA,OAAO;IACpC,CAAC;EACH;AACF;AAEA;AACA;AACA;AACA;AACA,SAASnB,kBAAkBA,CAACoB,eAAe,EAAE;EAC3C,OAAO,CAAC1jB,CAAC,EAAEC,CAAC,KAAK;IACf,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4kB,eAAe,CAACzmB,MAAM,EAAE,EAAE6B,CAAC,EAAE;MAC/C,MAAM2kB,OAAO,GAAGC,eAAe,CAAC5kB,CAAC,CAAC,CAACkB,CAAC,EAAEC,CAAC,CAAC;MACxC,IAAIwjB,OAAO,KAAK,CAAC,EAAE;QACjB,OAAOA,OAAO;MAChB;IACF;IAEA,OAAO,CAAC;EACV,CAAC;AACH,C", "file": "/packages/minimongo.js", "sourcesContent": ["import './minimongo_common.js';\n", "import LocalCollection from './local_collection.js';\n\nexport const hasOwn = Object.prototype.hasOwnProperty;\n\n// Each element selector contains:\n//  - compileElementSelector, a function with args:\n//    - operand - the \"right hand side\" of the operator\n//    - valueSelector - the \"context\" for the operator (so that $regex can find\n//      $options)\n//    - matcher - the Matcher this is going into (so that $elemMatch can compile\n//      more things)\n//    returning a function mapping a single value to bool.\n//  - dontExpandLeafArrays, a bool which prevents expandArraysInBranches from\n//    being called\n//  - dontIncludeLeafArrays, a bool which causes an argument to be passed to\n//    expandArraysInBranches if it is called\nexport const ELEMENT_OPERATORS = {\n  $lt: makeInequality(cmpValue => cmpValue < 0),\n  $gt: makeInequality(cmpValue => cmpValue > 0),\n  $lte: makeInequality(cmpValue => cmpValue <= 0),\n  $gte: makeInequality(cmpValue => cmpValue >= 0),\n  $mod: {\n    compileElementSelector(operand) {\n      if (!(Array.isArray(operand) && operand.length === 2\n            && typeof operand[0] === 'number'\n            && typeof operand[1] === 'number')) {\n        throw Error('argument to $mod must be an array of two numbers');\n      }\n\n      // XXX could require to be ints or round or something\n      const divisor = operand[0];\n      const remainder = operand[1];\n      return value => (\n        typeof value === 'number' && value % divisor === remainder\n      );\n    },\n  },\n  $in: {\n    compileElementSelector(operand) {\n      if (!Array.isArray(operand)) {\n        throw Error('$in needs an array');\n      }\n\n      const elementMatchers = operand.map(option => {\n        if (option instanceof RegExp) {\n          return regexpElementMatcher(option);\n        }\n\n        if (isOperatorObject(option)) {\n          throw Error('cannot nest $ under $in');\n        }\n\n        return equalityElementMatcher(option);\n      });\n\n      return value => {\n        // Allow {a: {$in: [null]}} to match when 'a' does not exist.\n        if (value === undefined) {\n          value = null;\n        }\n\n        return elementMatchers.some(matcher => matcher(value));\n      };\n    },\n  },\n  $size: {\n    // {a: [[5, 5]]} must match {a: {$size: 1}} but not {a: {$size: 2}}, so we\n    // don't want to consider the element [5,5] in the leaf array [[5,5]] as a\n    // possible value.\n    dontExpandLeafArrays: true,\n    compileElementSelector(operand) {\n      if (typeof operand === 'string') {\n        // Don't ask me why, but by experimentation, this seems to be what Mongo\n        // does.\n        operand = 0;\n      } else if (typeof operand !== 'number') {\n        throw Error('$size needs a number');\n      }\n\n      return value => Array.isArray(value) && value.length === operand;\n    },\n  },\n  $type: {\n    // {a: [5]} must not match {a: {$type: 4}} (4 means array), but it should\n    // match {a: {$type: 1}} (1 means number), and {a: [[5]]} must match {$a:\n    // {$type: 4}}. Thus, when we see a leaf array, we *should* expand it but\n    // should *not* include it itself.\n    dontIncludeLeafArrays: true,\n    compileElementSelector(operand) {\n      if (typeof operand === 'string') {\n        const operandAliasMap = {\n          'double': 1,\n          'string': 2,\n          'object': 3,\n          'array': 4,\n          'binData': 5,\n          'undefined': 6,\n          'objectId': 7,\n          'bool': 8,\n          'date': 9,\n          'null': 10,\n          'regex': 11,\n          'dbPointer': 12,\n          'javascript': 13,\n          'symbol': 14,\n          'javascriptWithScope': 15,\n          'int': 16,\n          'timestamp': 17,\n          'long': 18,\n          'decimal': 19,\n          'minKey': -1,\n          'maxKey': 127,\n        };\n        if (!hasOwn.call(operandAliasMap, operand)) {\n          throw Error(`unknown string alias for $type: ${operand}`);\n        }\n        operand = operandAliasMap[operand];\n      } else if (typeof operand === 'number') {\n        if (operand === 0 || operand < -1\n          || (operand > 19 && operand !== 127)) {\n          throw Error(`Invalid numerical $type code: ${operand}`);\n        }\n      } else {\n        throw Error('argument to $type is not a number or a string');\n      }\n\n      return value => (\n        value !== undefined && LocalCollection._f._type(value) === operand\n      );\n    },\n  },\n  $bitsAllSet: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAllSet');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.every((byte, i) => (bitmask[i] & byte) === byte);\n      };\n    },\n  },\n  $bitsAnySet: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAnySet');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.some((byte, i) => (~bitmask[i] & byte) !== byte);\n      };\n    },\n  },\n  $bitsAllClear: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAllClear');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.every((byte, i) => !(bitmask[i] & byte));\n      };\n    },\n  },\n  $bitsAnyClear: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAnyClear');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.some((byte, i) => (bitmask[i] & byte) !== byte);\n      };\n    },\n  },\n  $regex: {\n    compileElementSelector(operand, valueSelector) {\n      if (!(typeof operand === 'string' || operand instanceof RegExp)) {\n        throw Error('$regex has to be a string or RegExp');\n      }\n\n      let regexp;\n      if (valueSelector.$options !== undefined) {\n        // Options passed in $options (even the empty string) always overrides\n        // options in the RegExp object itself.\n\n        // Be clear that we only support the JS-supported options, not extended\n        // ones (eg, Mongo supports x and s). Ideally we would implement x and s\n        // by transforming the regexp, but not today...\n        if (/[^gim]/.test(valueSelector.$options)) {\n          throw new Error('Only the i, m, and g regexp options are supported');\n        }\n\n        const source = operand instanceof RegExp ? operand.source : operand;\n        regexp = new RegExp(source, valueSelector.$options);\n      } else if (operand instanceof RegExp) {\n        regexp = operand;\n      } else {\n        regexp = new RegExp(operand);\n      }\n\n      return regexpElementMatcher(regexp);\n    },\n  },\n  $elemMatch: {\n    dontExpandLeafArrays: true,\n    compileElementSelector(operand, valueSelector, matcher) {\n      if (!LocalCollection._isPlainObject(operand)) {\n        throw Error('$elemMatch need an object');\n      }\n\n      const isDocMatcher = !isOperatorObject(\n        Object.keys(operand)\n          .filter(key => !hasOwn.call(LOGICAL_OPERATORS, key))\n          .reduce((a, b) => Object.assign(a, {[b]: operand[b]}), {}),\n        true);\n\n      let subMatcher;\n      if (isDocMatcher) {\n        // This is NOT the same as compileValueSelector(operand), and not just\n        // because of the slightly different calling convention.\n        // {$elemMatch: {x: 3}} means \"an element has a field x:3\", not\n        // \"consists only of a field x:3\". Also, regexps and sub-$ are allowed.\n        subMatcher =\n          compileDocumentSelector(operand, matcher, {inElemMatch: true});\n      } else {\n        subMatcher = compileValueSelector(operand, matcher);\n      }\n\n      return value => {\n        if (!Array.isArray(value)) {\n          return false;\n        }\n\n        for (let i = 0; i < value.length; ++i) {\n          const arrayElement = value[i];\n          let arg;\n          if (isDocMatcher) {\n            // We can only match {$elemMatch: {b: 3}} against objects.\n            // (We can also match against arrays, if there's numeric indices,\n            // eg {$elemMatch: {'0.b': 3}} or {$elemMatch: {0: 3}}.)\n            if (!isIndexable(arrayElement)) {\n              return false;\n            }\n\n            arg = arrayElement;\n          } else {\n            // dontIterate ensures that {a: {$elemMatch: {$gt: 5}}} matches\n            // {a: [8]} but not {a: [[8]]}\n            arg = [{value: arrayElement, dontIterate: true}];\n          }\n          // XXX support $near in $elemMatch by propagating $distance?\n          if (subMatcher(arg).result) {\n            return i; // specially understood to mean \"use as arrayIndices\"\n          }\n        }\n\n        return false;\n      };\n    },\n  },\n};\n\n// Operators that appear at the top level of a document selector.\nconst LOGICAL_OPERATORS = {\n  $and(subSelector, matcher, inElemMatch) {\n    return andDocumentMatchers(\n      compileArrayOfDocumentSelectors(subSelector, matcher, inElemMatch)\n    );\n  },\n\n  $or(subSelector, matcher, inElemMatch) {\n    const matchers = compileArrayOfDocumentSelectors(\n      subSelector,\n      matcher,\n      inElemMatch\n    );\n\n    // Special case: if there is only one matcher, use it directly, *preserving*\n    // any arrayIndices it returns.\n    if (matchers.length === 1) {\n      return matchers[0];\n    }\n\n    return doc => {\n      const result = matchers.some(fn => fn(doc).result);\n      // $or does NOT set arrayIndices when it has multiple\n      // sub-expressions. (Tested against MongoDB.)\n      return {result};\n    };\n  },\n\n  $nor(subSelector, matcher, inElemMatch) {\n    const matchers = compileArrayOfDocumentSelectors(\n      subSelector,\n      matcher,\n      inElemMatch\n    );\n    return doc => {\n      const result = matchers.every(fn => !fn(doc).result);\n      // Never set arrayIndices, because we only match if nothing in particular\n      // 'matched' (and because this is consistent with MongoDB).\n      return {result};\n    };\n  },\n\n  $where(selectorValue, matcher) {\n    // Record that *any* path may be used.\n    matcher._recordPathUsed('');\n    matcher._hasWhere = true;\n\n    if (!(selectorValue instanceof Function)) {\n      // XXX MongoDB seems to have more complex logic to decide where or or not\n      // to add 'return'; not sure exactly what it is.\n      selectorValue = Function('obj', `return ${selectorValue}`);\n    }\n\n    // We make the document available as both `this` and `obj`.\n    // // XXX not sure what we should do if this throws\n    return doc => ({result: selectorValue.call(doc, doc)});\n  },\n\n  // This is just used as a comment in the query (in MongoDB, it also ends up in\n  // query logs); it has no effect on the actual selection.\n  $comment() {\n    return () => ({result: true});\n  },\n};\n\n// Operators that (unlike LOGICAL_OPERATORS) pertain to individual paths in a\n// document, but (unlike ELEMENT_OPERATORS) do not have a simple definition as\n// \"match each branched value independently and combine with\n// convertElementMatcherToBranchedMatcher\".\nconst VALUE_OPERATORS = {\n  $eq(operand) {\n    return convertElementMatcherToBranchedMatcher(\n      equalityElementMatcher(operand)\n    );\n  },\n  $not(operand, valueSelector, matcher) {\n    return invertBranchedMatcher(compileValueSelector(operand, matcher));\n  },\n  $ne(operand) {\n    return invertBranchedMatcher(\n      convertElementMatcherToBranchedMatcher(equalityElementMatcher(operand))\n    );\n  },\n  $nin(operand) {\n    return invertBranchedMatcher(\n      convertElementMatcherToBranchedMatcher(\n        ELEMENT_OPERATORS.$in.compileElementSelector(operand)\n      )\n    );\n  },\n  $exists(operand) {\n    const exists = convertElementMatcherToBranchedMatcher(\n      value => value !== undefined\n    );\n    return operand ? exists : invertBranchedMatcher(exists);\n  },\n  // $options just provides options for $regex; its logic is inside $regex\n  $options(operand, valueSelector) {\n    if (!hasOwn.call(valueSelector, '$regex')) {\n      throw Error('$options needs a $regex');\n    }\n\n    return everythingMatcher;\n  },\n  // $maxDistance is basically an argument to $near\n  $maxDistance(operand, valueSelector) {\n    if (!valueSelector.$near) {\n      throw Error('$maxDistance needs a $near');\n    }\n\n    return everythingMatcher;\n  },\n  $all(operand, valueSelector, matcher) {\n    if (!Array.isArray(operand)) {\n      throw Error('$all requires array');\n    }\n\n    // Not sure why, but this seems to be what MongoDB does.\n    if (operand.length === 0) {\n      return nothingMatcher;\n    }\n\n    const branchedMatchers = operand.map(criterion => {\n      // XXX handle $all/$elemMatch combination\n      if (isOperatorObject(criterion)) {\n        throw Error('no $ expressions in $all');\n      }\n\n      // This is always a regexp or equality selector.\n      return compileValueSelector(criterion, matcher);\n    });\n\n    // andBranchedMatchers does NOT require all selectors to return true on the\n    // SAME branch.\n    return andBranchedMatchers(branchedMatchers);\n  },\n  $near(operand, valueSelector, matcher, isRoot) {\n    if (!isRoot) {\n      throw Error('$near can\\'t be inside another $ operator');\n    }\n\n    matcher._hasGeoQuery = true;\n\n    // There are two kinds of geodata in MongoDB: legacy coordinate pairs and\n    // GeoJSON. They use different distance metrics, too. GeoJSON queries are\n    // marked with a $geometry property, though legacy coordinates can be\n    // matched using $geometry.\n    let maxDistance, point, distance;\n    if (LocalCollection._isPlainObject(operand) && hasOwn.call(operand, '$geometry')) {\n      // GeoJSON \"2dsphere\" mode.\n      maxDistance = operand.$maxDistance;\n      point = operand.$geometry;\n      distance = value => {\n        // XXX: for now, we don't calculate the actual distance between, say,\n        // polygon and circle. If people care about this use-case it will get\n        // a priority.\n        if (!value) {\n          return null;\n        }\n\n        if (!value.type) {\n          return GeoJSON.pointDistance(\n            point,\n            {type: 'Point', coordinates: pointToArray(value)}\n          );\n        }\n\n        if (value.type === 'Point') {\n          return GeoJSON.pointDistance(point, value);\n        }\n\n        return GeoJSON.geometryWithinRadius(value, point, maxDistance)\n          ? 0\n          : maxDistance + 1;\n      };\n    } else {\n      maxDistance = valueSelector.$maxDistance;\n\n      if (!isIndexable(operand)) {\n        throw Error('$near argument must be coordinate pair or GeoJSON');\n      }\n\n      point = pointToArray(operand);\n\n      distance = value => {\n        if (!isIndexable(value)) {\n          return null;\n        }\n\n        return distanceCoordinatePairs(point, value);\n      };\n    }\n\n    return branchedValues => {\n      // There might be multiple points in the document that match the given\n      // field. Only one of them needs to be within $maxDistance, but we need to\n      // evaluate all of them and use the nearest one for the implicit sort\n      // specifier. (That's why we can't just use ELEMENT_OPERATORS here.)\n      //\n      // Note: This differs from MongoDB's implementation, where a document will\n      // actually show up *multiple times* in the result set, with one entry for\n      // each within-$maxDistance branching point.\n      const result = {result: false};\n      expandArraysInBranches(branchedValues).every(branch => {\n        // if operation is an update, don't skip branches, just return the first\n        // one (#3599)\n        let curDistance;\n        if (!matcher._isUpdate) {\n          if (!(typeof branch.value === 'object')) {\n            return true;\n          }\n\n          curDistance = distance(branch.value);\n\n          // Skip branches that aren't real points or are too far away.\n          if (curDistance === null || curDistance > maxDistance) {\n            return true;\n          }\n\n          // Skip anything that's a tie.\n          if (result.distance !== undefined && result.distance <= curDistance) {\n            return true;\n          }\n        }\n\n        result.result = true;\n        result.distance = curDistance;\n\n        if (branch.arrayIndices) {\n          result.arrayIndices = branch.arrayIndices;\n        } else {\n          delete result.arrayIndices;\n        }\n\n        return !matcher._isUpdate;\n      });\n\n      return result;\n    };\n  },\n};\n\n// NB: We are cheating and using this function to implement 'AND' for both\n// 'document matchers' and 'branched matchers'. They both return result objects\n// but the argument is different: for the former it's a whole doc, whereas for\n// the latter it's an array of 'branched values'.\nfunction andSomeMatchers(subMatchers) {\n  if (subMatchers.length === 0) {\n    return everythingMatcher;\n  }\n\n  if (subMatchers.length === 1) {\n    return subMatchers[0];\n  }\n\n  return docOrBranches => {\n    const match = {};\n    match.result = subMatchers.every(fn => {\n      const subResult = fn(docOrBranches);\n\n      // Copy a 'distance' number out of the first sub-matcher that has\n      // one. Yes, this means that if there are multiple $near fields in a\n      // query, something arbitrary happens; this appears to be consistent with\n      // Mongo.\n      if (subResult.result &&\n          subResult.distance !== undefined &&\n          match.distance === undefined) {\n        match.distance = subResult.distance;\n      }\n\n      // Similarly, propagate arrayIndices from sub-matchers... but to match\n      // MongoDB behavior, this time the *last* sub-matcher with arrayIndices\n      // wins.\n      if (subResult.result && subResult.arrayIndices) {\n        match.arrayIndices = subResult.arrayIndices;\n      }\n\n      return subResult.result;\n    });\n\n    // If we didn't actually match, forget any extra metadata we came up with.\n    if (!match.result) {\n      delete match.distance;\n      delete match.arrayIndices;\n    }\n\n    return match;\n  };\n}\n\nconst andDocumentMatchers = andSomeMatchers;\nconst andBranchedMatchers = andSomeMatchers;\n\nfunction compileArrayOfDocumentSelectors(selectors, matcher, inElemMatch) {\n  if (!Array.isArray(selectors) || selectors.length === 0) {\n    throw Error('$and/$or/$nor must be nonempty array');\n  }\n\n  return selectors.map(subSelector => {\n    if (!LocalCollection._isPlainObject(subSelector)) {\n      throw Error('$or/$and/$nor entries need to be full objects');\n    }\n\n    return compileDocumentSelector(subSelector, matcher, {inElemMatch});\n  });\n}\n\n// Takes in a selector that could match a full document (eg, the original\n// selector). Returns a function mapping document->result object.\n//\n// matcher is the Matcher object we are compiling.\n//\n// If this is the root document selector (ie, not wrapped in $and or the like),\n// then isRoot is true. (This is used by $near.)\nexport function compileDocumentSelector(docSelector, matcher, options = {}) {\n  const docMatchers = Object.keys(docSelector).map(key => {\n    const subSelector = docSelector[key];\n\n    if (key.substr(0, 1) === '$') {\n      // Outer operators are either logical operators (they recurse back into\n      // this function), or $where.\n      if (!hasOwn.call(LOGICAL_OPERATORS, key)) {\n        throw new Error(`Unrecognized logical operator: ${key}`);\n      }\n\n      matcher._isSimple = false;\n      return LOGICAL_OPERATORS[key](subSelector, matcher, options.inElemMatch);\n    }\n\n    // Record this path, but only if we aren't in an elemMatcher, since in an\n    // elemMatch this is a path inside an object in an array, not in the doc\n    // root.\n    if (!options.inElemMatch) {\n      matcher._recordPathUsed(key);\n    }\n\n    // Don't add a matcher if subSelector is a function -- this is to match\n    // the behavior of Meteor on the server (inherited from the node mongodb\n    // driver), which is to ignore any part of a selector which is a function.\n    if (typeof subSelector === 'function') {\n      return undefined;\n    }\n\n    const lookUpByIndex = makeLookupFunction(key);\n    const valueMatcher = compileValueSelector(\n      subSelector,\n      matcher,\n      options.isRoot\n    );\n\n    return doc => valueMatcher(lookUpByIndex(doc));\n  }).filter(Boolean);\n\n  return andDocumentMatchers(docMatchers);\n}\n\n// Takes in a selector that could match a key-indexed value in a document; eg,\n// {$gt: 5, $lt: 9}, or a regular expression, or any non-expression object (to\n// indicate equality).  Returns a branched matcher: a function mapping\n// [branched value]->result object.\nfunction compileValueSelector(valueSelector, matcher, isRoot) {\n  if (valueSelector instanceof RegExp) {\n    matcher._isSimple = false;\n    return convertElementMatcherToBranchedMatcher(\n      regexpElementMatcher(valueSelector)\n    );\n  }\n\n  if (isOperatorObject(valueSelector)) {\n    return operatorBranchedMatcher(valueSelector, matcher, isRoot);\n  }\n\n  return convertElementMatcherToBranchedMatcher(\n    equalityElementMatcher(valueSelector)\n  );\n}\n\n// Given an element matcher (which evaluates a single value), returns a branched\n// value (which evaluates the element matcher on all the branches and returns a\n// more structured return value possibly including arrayIndices).\nfunction convertElementMatcherToBranchedMatcher(elementMatcher, options = {}) {\n  return branches => {\n    const expanded = options.dontExpandLeafArrays\n      ? branches\n      : expandArraysInBranches(branches, options.dontIncludeLeafArrays);\n\n    const match = {};\n    match.result = expanded.some(element => {\n      let matched = elementMatcher(element.value);\n\n      // Special case for $elemMatch: it means \"true, and use this as an array\n      // index if I didn't already have one\".\n      if (typeof matched === 'number') {\n        // XXX This code dates from when we only stored a single array index\n        // (for the outermost array). Should we be also including deeper array\n        // indices from the $elemMatch match?\n        if (!element.arrayIndices) {\n          element.arrayIndices = [matched];\n        }\n\n        matched = true;\n      }\n\n      // If some element matched, and it's tagged with array indices, include\n      // those indices in our result object.\n      if (matched && element.arrayIndices) {\n        match.arrayIndices = element.arrayIndices;\n      }\n\n      return matched;\n    });\n\n    return match;\n  };\n}\n\n// Helpers for $near.\nfunction distanceCoordinatePairs(a, b) {\n  const pointA = pointToArray(a);\n  const pointB = pointToArray(b);\n\n  return Math.hypot(pointA[0] - pointB[0], pointA[1] - pointB[1]);\n}\n\n// Takes something that is not an operator object and returns an element matcher\n// for equality with that thing.\nexport function equalityElementMatcher(elementSelector) {\n  if (isOperatorObject(elementSelector)) {\n    throw Error('Can\\'t create equalityValueSelector for operator object');\n  }\n\n  // Special-case: null and undefined are equal (if you got undefined in there\n  // somewhere, or if you got it due to some branch being non-existent in the\n  // weird special case), even though they aren't with EJSON.equals.\n  // undefined or null\n  if (elementSelector == null) {\n    return value => value == null;\n  }\n\n  return value => LocalCollection._f._equal(elementSelector, value);\n}\n\nfunction everythingMatcher(docOrBranchedValues) {\n  return {result: true};\n}\n\nexport function expandArraysInBranches(branches, skipTheArrays) {\n  const branchesOut = [];\n\n  branches.forEach(branch => {\n    const thisIsArray = Array.isArray(branch.value);\n\n    // We include the branch itself, *UNLESS* we it's an array that we're going\n    // to iterate and we're told to skip arrays.  (That's right, we include some\n    // arrays even skipTheArrays is true: these are arrays that were found via\n    // explicit numerical indices.)\n    if (!(skipTheArrays && thisIsArray && !branch.dontIterate)) {\n      branchesOut.push({arrayIndices: branch.arrayIndices, value: branch.value});\n    }\n\n    if (thisIsArray && !branch.dontIterate) {\n      branch.value.forEach((value, i) => {\n        branchesOut.push({\n          arrayIndices: (branch.arrayIndices || []).concat(i),\n          value\n        });\n      });\n    }\n  });\n\n  return branchesOut;\n}\n\n// Helpers for $bitsAllSet/$bitsAnySet/$bitsAllClear/$bitsAnyClear.\nfunction getOperandBitmask(operand, selector) {\n  // numeric bitmask\n  // You can provide a numeric bitmask to be matched against the operand field.\n  // It must be representable as a non-negative 32-bit signed integer.\n  // Otherwise, $bitsAllSet will return an error.\n  if (Number.isInteger(operand) && operand >= 0) {\n    return new Uint8Array(new Int32Array([operand]).buffer);\n  }\n\n  // bindata bitmask\n  // You can also use an arbitrarily large BinData instance as a bitmask.\n  if (EJSON.isBinary(operand)) {\n    return new Uint8Array(operand.buffer);\n  }\n\n  // position list\n  // If querying a list of bit positions, each <position> must be a non-negative\n  // integer. Bit positions start at 0 from the least significant bit.\n  if (Array.isArray(operand) &&\n      operand.every(x => Number.isInteger(x) && x >= 0)) {\n    const buffer = new ArrayBuffer((Math.max(...operand) >> 3) + 1);\n    const view = new Uint8Array(buffer);\n\n    operand.forEach(x => {\n      view[x >> 3] |= 1 << (x & 0x7);\n    });\n\n    return view;\n  }\n\n  // bad operand\n  throw Error(\n    `operand to ${selector} must be a numeric bitmask (representable as a ` +\n    'non-negative 32-bit signed integer), a bindata bitmask or an array with ' +\n    'bit positions (non-negative integers)'\n  );\n}\n\nfunction getValueBitmask(value, length) {\n  // The field value must be either numerical or a BinData instance. Otherwise,\n  // $bits... will not match the current document.\n\n  // numerical\n  if (Number.isSafeInteger(value)) {\n    // $bits... will not match numerical values that cannot be represented as a\n    // signed 64-bit integer. This can be the case if a value is either too\n    // large or small to fit in a signed 64-bit integer, or if it has a\n    // fractional component.\n    const buffer = new ArrayBuffer(\n      Math.max(length, 2 * Uint32Array.BYTES_PER_ELEMENT)\n    );\n\n    let view = new Uint32Array(buffer, 0, 2);\n    view[0] = value % ((1 << 16) * (1 << 16)) | 0;\n    view[1] = value / ((1 << 16) * (1 << 16)) | 0;\n\n    // sign extension\n    if (value < 0) {\n      view = new Uint8Array(buffer, 2);\n      view.forEach((byte, i) => {\n        view[i] = 0xff;\n      });\n    }\n\n    return new Uint8Array(buffer);\n  }\n\n  // bindata\n  if (EJSON.isBinary(value)) {\n    return new Uint8Array(value.buffer);\n  }\n\n  // no match\n  return false;\n}\n\n// Actually inserts a key value into the selector document\n// However, this checks there is no ambiguity in setting\n// the value for the given key, throws otherwise\nfunction insertIntoDocument(document, key, value) {\n  Object.keys(document).forEach(existingKey => {\n    if (\n      (existingKey.length > key.length && existingKey.indexOf(`${key}.`) === 0) ||\n      (key.length > existingKey.length && key.indexOf(`${existingKey}.`) === 0)\n    ) {\n      throw new Error(\n        `cannot infer query fields to set, both paths '${existingKey}' and ` +\n        `'${key}' are matched`\n      );\n    } else if (existingKey === key) {\n      throw new Error(\n        `cannot infer query fields to set, path '${key}' is matched twice`\n      );\n    }\n  });\n\n  document[key] = value;\n}\n\n// Returns a branched matcher that matches iff the given matcher does not.\n// Note that this implicitly \"deMorganizes\" the wrapped function.  ie, it\n// means that ALL branch values need to fail to match innerBranchedMatcher.\nfunction invertBranchedMatcher(branchedMatcher) {\n  return branchValues => {\n    // We explicitly choose to strip arrayIndices here: it doesn't make sense to\n    // say \"update the array element that does not match something\", at least\n    // in mongo-land.\n    return {result: !branchedMatcher(branchValues).result};\n  };\n}\n\nexport function isIndexable(obj) {\n  return Array.isArray(obj) || LocalCollection._isPlainObject(obj);\n}\n\nexport function isNumericKey(s) {\n  return /^[0-9]+$/.test(s);\n}\n\n// Returns true if this is an object with at least one key and all keys begin\n// with $.  Unless inconsistentOK is set, throws if some keys begin with $ and\n// others don't.\nexport function isOperatorObject(valueSelector, inconsistentOK) {\n  if (!LocalCollection._isPlainObject(valueSelector)) {\n    return false;\n  }\n\n  let theseAreOperators = undefined;\n  Object.keys(valueSelector).forEach(selKey => {\n    const thisIsOperator = selKey.substr(0, 1) === '$' || selKey === 'diff';\n\n    if (theseAreOperators === undefined) {\n      theseAreOperators = thisIsOperator;\n    } else if (theseAreOperators !== thisIsOperator) {\n      if (!inconsistentOK) {\n        throw new Error(\n          `Inconsistent operator: ${JSON.stringify(valueSelector)}`\n        );\n      }\n\n      theseAreOperators = false;\n    }\n  });\n\n  return !!theseAreOperators; // {} has no operators\n}\n\n// Helper for $lt/$gt/$lte/$gte.\nfunction makeInequality(cmpValueComparator) {\n  return {\n    compileElementSelector(operand) {\n      // Arrays never compare false with non-arrays for any inequality.\n      // XXX This was behavior we observed in pre-release MongoDB 2.5, but\n      //     it seems to have been reverted.\n      //     See https://jira.mongodb.org/browse/SERVER-11444\n      if (Array.isArray(operand)) {\n        return () => false;\n      }\n\n      // Special case: consider undefined and null the same (so true with\n      // $gte/$lte).\n      if (operand === undefined) {\n        operand = null;\n      }\n\n      const operandType = LocalCollection._f._type(operand);\n\n      return value => {\n        if (value === undefined) {\n          value = null;\n        }\n\n        // Comparisons are never true among things of different type (except\n        // null vs undefined).\n        if (LocalCollection._f._type(value) !== operandType) {\n          return false;\n        }\n\n        return cmpValueComparator(LocalCollection._f._cmp(value, operand));\n      };\n    },\n  };\n}\n\n// makeLookupFunction(key) returns a lookup function.\n//\n// A lookup function takes in a document and returns an array of matching\n// branches.  If no arrays are found while looking up the key, this array will\n// have exactly one branches (possibly 'undefined', if some segment of the key\n// was not found).\n//\n// If arrays are found in the middle, this can have more than one element, since\n// we 'branch'. When we 'branch', if there are more key segments to look up,\n// then we only pursue branches that are plain objects (not arrays or scalars).\n// This means we can actually end up with no branches!\n//\n// We do *NOT* branch on arrays that are found at the end (ie, at the last\n// dotted member of the key). We just return that array; if you want to\n// effectively 'branch' over the array's values, post-process the lookup\n// function with expandArraysInBranches.\n//\n// Each branch is an object with keys:\n//  - value: the value at the branch\n//  - dontIterate: an optional bool; if true, it means that 'value' is an array\n//    that expandArraysInBranches should NOT expand. This specifically happens\n//    when there is a numeric index in the key, and ensures the\n//    perhaps-surprising MongoDB behavior where {'a.0': 5} does NOT\n//    match {a: [[5]]}.\n//  - arrayIndices: if any array indexing was done during lookup (either due to\n//    explicit numeric indices or implicit branching), this will be an array of\n//    the array indices used, from outermost to innermost; it is falsey or\n//    absent if no array index is used. If an explicit numeric index is used,\n//    the index will be followed in arrayIndices by the string 'x'.\n//\n//    Note: arrayIndices is used for two purposes. First, it is used to\n//    implement the '$' modifier feature, which only ever looks at its first\n//    element.\n//\n//    Second, it is used for sort key generation, which needs to be able to tell\n//    the difference between different paths. Moreover, it needs to\n//    differentiate between explicit and implicit branching, which is why\n//    there's the somewhat hacky 'x' entry: this means that explicit and\n//    implicit array lookups will have different full arrayIndices paths. (That\n//    code only requires that different paths have different arrayIndices; it\n//    doesn't actually 'parse' arrayIndices. As an alternative, arrayIndices\n//    could contain objects with flags like 'implicit', but I think that only\n//    makes the code surrounding them more complex.)\n//\n//    (By the way, this field ends up getting passed around a lot without\n//    cloning, so never mutate any arrayIndices field/var in this package!)\n//\n//\n// At the top level, you may only pass in a plain object or array.\n//\n// See the test 'minimongo - lookup' for some examples of what lookup functions\n// return.\nexport function makeLookupFunction(key, options = {}) {\n  const parts = key.split('.');\n  const firstPart = parts.length ? parts[0] : '';\n  const lookupRest = (\n    parts.length > 1 &&\n    makeLookupFunction(parts.slice(1).join('.'), options)\n  );\n\n  function buildResult(arrayIndices, dontIterate, value) {\n    return arrayIndices && arrayIndices.length\n      ? dontIterate\n        ? [{ arrayIndices, dontIterate, value }]\n        : [{ arrayIndices, value }]\n      : dontIterate\n        ? [{ dontIterate, value }]\n        : [{ value }];\n  }\n\n  // Doc will always be a plain object or an array.\n  // apply an explicit numeric index, an array.\n  return (doc, arrayIndices) => {\n    if (Array.isArray(doc)) {\n      // If we're being asked to do an invalid lookup into an array (non-integer\n      // or out-of-bounds), return no results (which is different from returning\n      // a single undefined result, in that `null` equality checks won't match).\n      if (!(isNumericKey(firstPart) && firstPart < doc.length)) {\n        return [];\n      }\n\n      // Remember that we used this array index. Include an 'x' to indicate that\n      // the previous index came from being considered as an explicit array\n      // index (not branching).\n      arrayIndices = arrayIndices ? arrayIndices.concat(+firstPart, 'x') : [+firstPart, 'x'];\n    }\n\n    // Do our first lookup.\n    const firstLevel = doc[firstPart];\n\n    // If there is no deeper to dig, return what we found.\n    //\n    // If what we found is an array, most value selectors will choose to treat\n    // the elements of the array as matchable values in their own right, but\n    // that's done outside of the lookup function. (Exceptions to this are $size\n    // and stuff relating to $elemMatch.  eg, {a: {$size: 2}} does not match {a:\n    // [[1, 2]]}.)\n    //\n    // That said, if we just did an *explicit* array lookup (on doc) to find\n    // firstLevel, and firstLevel is an array too, we do NOT want value\n    // selectors to iterate over it.  eg, {'a.0': 5} does not match {a: [[5]]}.\n    // So in that case, we mark the return value as 'don't iterate'.\n    if (!lookupRest) {\n      return buildResult(\n        arrayIndices,\n        Array.isArray(doc) && Array.isArray(firstLevel),\n        firstLevel,\n      );\n    }\n\n    // We need to dig deeper.  But if we can't, because what we've found is not\n    // an array or plain object, we're done. If we just did a numeric index into\n    // an array, we return nothing here (this is a change in Mongo 2.5 from\n    // Mongo 2.4, where {'a.0.b': null} stopped matching {a: [5]}). Otherwise,\n    // return a single `undefined` (which can, for example, match via equality\n    // with `null`).\n    if (!isIndexable(firstLevel)) {\n      if (Array.isArray(doc)) {\n        return [];\n      }\n\n      return buildResult(arrayIndices, false, undefined);\n    }\n\n    const result = [];\n    const appendToResult = more => {\n      result.push(...more);\n    };\n\n    // Dig deeper: look up the rest of the parts on whatever we've found.\n    // (lookupRest is smart enough to not try to do invalid lookups into\n    // firstLevel if it's an array.)\n    appendToResult(lookupRest(firstLevel, arrayIndices));\n\n    // If we found an array, then in *addition* to potentially treating the next\n    // part as a literal integer lookup, we should also 'branch': try to look up\n    // the rest of the parts on each array element in parallel.\n    //\n    // In this case, we *only* dig deeper into array elements that are plain\n    // objects. (Recall that we only got this far if we have further to dig.)\n    // This makes sense: we certainly don't dig deeper into non-indexable\n    // objects. And it would be weird to dig into an array: it's simpler to have\n    // a rule that explicit integer indexes only apply to an outer array, not to\n    // an array you find after a branching search.\n    //\n    // In the special case of a numeric part in a *sort selector* (not a query\n    // selector), we skip the branching: we ONLY allow the numeric part to mean\n    // 'look up this index' in that case, not 'also look up this index in all\n    // the elements of the array'.\n    if (Array.isArray(firstLevel) &&\n        !(isNumericKey(parts[1]) && options.forSort)) {\n      firstLevel.forEach((branch, arrayIndex) => {\n        if (LocalCollection._isPlainObject(branch)) {\n          appendToResult(lookupRest(branch, arrayIndices ? arrayIndices.concat(arrayIndex) : [arrayIndex]));\n        }\n      });\n    }\n\n    return result;\n  };\n}\n\n// Object exported only for unit testing.\n// Use it to export private functions to test in Tinytest.\nMinimongoTest = {makeLookupFunction};\nMinimongoError = (message, options = {}) => {\n  if (typeof message === 'string' && options.field) {\n    message += ` for field '${options.field}'`;\n  }\n\n  const error = new Error(message);\n  error.name = 'MinimongoError';\n  return error;\n};\n\nexport function nothingMatcher(docOrBranchedValues) {\n  return {result: false};\n}\n\n// Takes an operator object (an object with $ keys) and returns a branched\n// matcher for it.\nfunction operatorBranchedMatcher(valueSelector, matcher, isRoot) {\n  // Each valueSelector works separately on the various branches.  So one\n  // operator can match one branch and another can match another branch.  This\n  // is OK.\n  const operatorMatchers = Object.keys(valueSelector).map(operator => {\n    const operand = valueSelector[operator];\n\n    const simpleRange = (\n      ['$lt', '$lte', '$gt', '$gte'].includes(operator) &&\n      typeof operand === 'number'\n    );\n\n    const simpleEquality = (\n      ['$ne', '$eq'].includes(operator) &&\n      operand !== Object(operand)\n    );\n\n    const simpleInclusion = (\n      ['$in', '$nin'].includes(operator)\n      && Array.isArray(operand)\n      && !operand.some(x => x === Object(x))\n    );\n\n    if (!(simpleRange || simpleInclusion || simpleEquality)) {\n      matcher._isSimple = false;\n    }\n\n    if (hasOwn.call(VALUE_OPERATORS, operator)) {\n      return VALUE_OPERATORS[operator](operand, valueSelector, matcher, isRoot);\n    }\n\n    if (hasOwn.call(ELEMENT_OPERATORS, operator)) {\n      const options = ELEMENT_OPERATORS[operator];\n      return convertElementMatcherToBranchedMatcher(\n        options.compileElementSelector(operand, valueSelector, matcher),\n        options\n      );\n    }\n\n    throw new Error(`Unrecognized operator: ${operator}`);\n  });\n\n  return andBranchedMatchers(operatorMatchers);\n}\n\n// paths - Array: list of mongo style paths\n// newLeafFn - Function: of form function(path) should return a scalar value to\n//                       put into list created for that path\n// conflictFn - Function: of form function(node, path, fullPath) is called\n//                        when building a tree path for 'fullPath' node on\n//                        'path' was already a leaf with a value. Must return a\n//                        conflict resolution.\n// initial tree - Optional Object: starting tree.\n// @returns - Object: tree represented as a set of nested objects\nexport function pathsToTree(paths, newLeafFn, conflictFn, root = {}) {\n  paths.forEach(path => {\n    const pathArray = path.split('.');\n    let tree = root;\n\n    // use .every just for iteration with break\n    const success = pathArray.slice(0, -1).every((key, i) => {\n      if (!hasOwn.call(tree, key)) {\n        tree[key] = {};\n      } else if (tree[key] !== Object(tree[key])) {\n        tree[key] = conflictFn(\n          tree[key],\n          pathArray.slice(0, i + 1).join('.'),\n          path\n        );\n\n        // break out of loop if we are failing for this path\n        if (tree[key] !== Object(tree[key])) {\n          return false;\n        }\n      }\n\n      tree = tree[key];\n\n      return true;\n    });\n\n    if (success) {\n      const lastKey = pathArray[pathArray.length - 1];\n      if (hasOwn.call(tree, lastKey)) {\n        tree[lastKey] = conflictFn(tree[lastKey], path, path);\n      } else {\n        tree[lastKey] = newLeafFn(path);\n      }\n    }\n  });\n\n  return root;\n}\n\n// Makes sure we get 2 elements array and assume the first one to be x and\n// the second one to y no matter what user passes.\n// In case user passes { lon: x, lat: y } returns [x, y]\nfunction pointToArray(point) {\n  return Array.isArray(point) ? point.slice() : [point.x, point.y];\n}\n\n// Creating a document from an upsert is quite tricky.\n// E.g. this selector: {\"$or\": [{\"b.foo\": {\"$all\": [\"bar\"]}}]}, should result\n// in: {\"b.foo\": \"bar\"}\n// But this selector: {\"$or\": [{\"b\": {\"foo\": {\"$all\": [\"bar\"]}}}]} should throw\n// an error\n\n// Some rules (found mainly with trial & error, so there might be more):\n// - handle all childs of $and (or implicit $and)\n// - handle $or nodes with exactly 1 child\n// - ignore $or nodes with more than 1 child\n// - ignore $nor and $not nodes\n// - throw when a value can not be set unambiguously\n// - every value for $all should be dealt with as separate $eq-s\n// - threat all children of $all as $eq setters (=> set if $all.length === 1,\n//   otherwise throw error)\n// - you can not mix '$'-prefixed keys and non-'$'-prefixed keys\n// - you can only have dotted keys on a root-level\n// - you can not have '$'-prefixed keys more than one-level deep in an object\n\n// Handles one key/value pair to put in the selector document\nfunction populateDocumentWithKeyValue(document, key, value) {\n  if (value && Object.getPrototypeOf(value) === Object.prototype) {\n    populateDocumentWithObject(document, key, value);\n  } else if (!(value instanceof RegExp)) {\n    insertIntoDocument(document, key, value);\n  }\n}\n\n// Handles a key, value pair to put in the selector document\n// if the value is an object\nfunction populateDocumentWithObject(document, key, value) {\n  const keys = Object.keys(value);\n  const unprefixedKeys = keys.filter(op => op[0] !== '$');\n\n  if (unprefixedKeys.length > 0 || !keys.length) {\n    // Literal (possibly empty) object ( or empty object )\n    // Don't allow mixing '$'-prefixed with non-'$'-prefixed fields\n    if (keys.length !== unprefixedKeys.length) {\n      throw new Error(`unknown operator: ${unprefixedKeys[0]}`);\n    }\n\n    validateObject(value, key);\n    insertIntoDocument(document, key, value);\n  } else {\n    Object.keys(value).forEach(op => {\n      const object = value[op];\n\n      if (op === '$eq') {\n        populateDocumentWithKeyValue(document, key, object);\n      } else if (op === '$all') {\n        // every value for $all should be dealt with as separate $eq-s\n        object.forEach(element =>\n          populateDocumentWithKeyValue(document, key, element)\n        );\n      }\n    });\n  }\n}\n\n// Fills a document with certain fields from an upsert selector\nexport function populateDocumentWithQueryFields(query, document = {}) {\n  if (Object.getPrototypeOf(query) === Object.prototype) {\n    // handle implicit $and\n    Object.keys(query).forEach(key => {\n      const value = query[key];\n\n      if (key === '$and') {\n        // handle explicit $and\n        value.forEach(element =>\n          populateDocumentWithQueryFields(element, document)\n        );\n      } else if (key === '$or') {\n        // handle $or nodes with exactly 1 child\n        if (value.length === 1) {\n          populateDocumentWithQueryFields(value[0], document);\n        }\n      } else if (key[0] !== '$') {\n        // Ignore other '$'-prefixed logical selectors\n        populateDocumentWithKeyValue(document, key, value);\n      }\n    });\n  } else {\n    // Handle meteor-specific shortcut for selecting _id\n    if (LocalCollection._selectorIsId(query)) {\n      insertIntoDocument(document, '_id', query);\n    }\n  }\n\n  return document;\n}\n\n// Traverses the keys of passed projection and constructs a tree where all\n// leaves are either all True or all False\n// @returns Object:\n//  - tree - Object - tree representation of keys involved in projection\n//  (exception for '_id' as it is a special case handled separately)\n//  - including - Boolean - \"take only certain fields\" type of projection\nexport function projectionDetails(fields) {\n  // Find the non-_id keys (_id is handled specially because it is included\n  // unless explicitly excluded). Sort the keys, so that our code to detect\n  // overlaps like 'foo' and 'foo.bar' can assume that 'foo' comes first.\n  let fieldsKeys = Object.keys(fields).sort();\n\n  // If _id is the only field in the projection, do not remove it, since it is\n  // required to determine if this is an exclusion or exclusion. Also keep an\n  // inclusive _id, since inclusive _id follows the normal rules about mixing\n  // inclusive and exclusive fields. If _id is not the only field in the\n  // projection and is exclusive, remove it so it can be handled later by a\n  // special case, since exclusive _id is always allowed.\n  if (!(fieldsKeys.length === 1 && fieldsKeys[0] === '_id') &&\n      !(fieldsKeys.includes('_id') && fields._id)) {\n    fieldsKeys = fieldsKeys.filter(key => key !== '_id');\n  }\n\n  let including = null; // Unknown\n\n  fieldsKeys.forEach(keyPath => {\n    const rule = !!fields[keyPath];\n\n    if (including === null) {\n      including = rule;\n    }\n\n    // This error message is copied from MongoDB shell\n    if (including !== rule) {\n      throw MinimongoError(\n        'You cannot currently mix including and excluding fields.'\n      );\n    }\n  });\n\n  const projectionRulesTree = pathsToTree(\n    fieldsKeys,\n    path => including,\n    (node, path, fullPath) => {\n      // Check passed projection fields' keys: If you have two rules such as\n      // 'foo.bar' and 'foo.bar.baz', then the result becomes ambiguous. If\n      // that happens, there is a probability you are doing something wrong,\n      // framework should notify you about such mistake earlier on cursor\n      // compilation step than later during runtime.  Note, that real mongo\n      // doesn't do anything about it and the later rule appears in projection\n      // project, more priority it takes.\n      //\n      // Example, assume following in mongo shell:\n      // > db.coll.insert({ a: { b: 23, c: 44 } })\n      // > db.coll.find({}, { 'a': 1, 'a.b': 1 })\n      // {\"_id\": ObjectId(\"520bfe456024608e8ef24af3\"), \"a\": {\"b\": 23}}\n      // > db.coll.find({}, { 'a.b': 1, 'a': 1 })\n      // {\"_id\": ObjectId(\"520bfe456024608e8ef24af3\"), \"a\": {\"b\": 23, \"c\": 44}}\n      //\n      // Note, how second time the return set of keys is different.\n      const currentPath = fullPath;\n      const anotherPath = path;\n      throw MinimongoError(\n        `both ${currentPath} and ${anotherPath} found in fields option, ` +\n        'using both of them may trigger unexpected behavior. Did you mean to ' +\n        'use only one of them?'\n      );\n    });\n\n  return {including, tree: projectionRulesTree};\n}\n\n// Takes a RegExp object and returns an element matcher.\nexport function regexpElementMatcher(regexp) {\n  return value => {\n    if (value instanceof RegExp) {\n      return value.toString() === regexp.toString();\n    }\n\n    // Regexps only work against strings.\n    if (typeof value !== 'string') {\n      return false;\n    }\n\n    // Reset regexp's state to avoid inconsistent matching for objects with the\n    // same value on consecutive calls of regexp.test. This happens only if the\n    // regexp has the 'g' flag. Also note that ES6 introduces a new flag 'y' for\n    // which we should *not* change the lastIndex but MongoDB doesn't support\n    // either of these flags.\n    regexp.lastIndex = 0;\n\n    return regexp.test(value);\n  };\n}\n\n// Validates the key in a path.\n// Objects that are nested more then 1 level cannot have dotted fields\n// or fields starting with '$'\nfunction validateKeyInPath(key, path) {\n  if (key.includes('.')) {\n    throw new Error(\n      `The dotted field '${key}' in '${path}.${key} is not valid for storage.`\n    );\n  }\n\n  if (key[0] === '$') {\n    throw new Error(\n      `The dollar ($) prefixed field  '${path}.${key} is not valid for storage.`\n    );\n  }\n}\n\n// Recursively validates an object that is nested more than one level deep\nfunction validateObject(object, path) {\n  if (object && Object.getPrototypeOf(object) === Object.prototype) {\n    Object.keys(object).forEach(key => {\n      validateKeyInPath(key, path);\n      validateObject(object[key], path + '.' + key);\n    });\n  }\n}\n", "/** Exported values are also used in the mongo package. */\n\n/** @param {string} method */\nexport function getAsyncMethodName(method) {\n  return `${method.replace('_', '')}Async`;\n}\n\nexport const ASYNC_COLLECTION_METHODS = [\n  '_createCappedCollection',\n  'dropCollection',\n  'dropIndex',\n  /**\n   * @summary Creates the specified index on the collection.\n   * @locus server\n   * @method createIndexAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   * @returns {Promise}\n   */\n  'createIndex',\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOneAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Promise}\n   */\n  'findOne',\n  /**\n   * @summary Insert a document in the collection.  Returns its unique _id.\n   * @locus Anywhere\n   * @method  insertAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   * @return {Promise}\n   */\n  'insert',\n  /**\n   * @summary Remove documents from the collection\n   * @locus Anywhere\n   * @method removeAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   * @return {Promise}\n   */\n  'remove',\n  /**\n   * @summary Modify one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method updateAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   * @return {Promise}\n   */\n  'update',\n  /**\n   * @summary Modify one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsertAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @return {Promise}\n   */\n  'upsert',\n];\n\nexport const ASYNC_CURSOR_METHODS = [\n  /**\n   * @deprecated in 2.9\n   * @summary Returns the number of documents that match a query. This method is\n   *          [deprecated since MongoDB 4.0](https://www.mongodb.com/docs/v4.4/reference/command/count/);\n   *          see `Collection.countDocuments` and\n   *          `Collection.estimatedDocumentCount` for a replacement.\n   * @memberOf Mongo.Cursor\n   * @method  countAsync\n   * @instance\n   * @locus Anywhere\n   * @returns {Promise}\n   */\n  'count',\n  /**\n   * @summary Return all matching documents as an Array.\n   * @memberOf Mongo.Cursor\n   * @method  fetchAsync\n   * @instance\n   * @locus Anywhere\n   * @returns {Promise}\n   */\n  'fetch',\n  /**\n   * @summary Call `callback` once for each matching document, sequentially and\n   *          synchronously.\n   * @locus Anywhere\n   * @method  forEachAsync\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   * @returns {Promise}\n   */\n  'forEach',\n  /**\n   * @summary Map callback over all matching documents.  Returns an Array.\n   * @locus Anywhere\n   * @method mapAsync\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   * @returns {Promise}\n   */\n  'map',\n];\n\nexport const CLIENT_ONLY_METHODS = [\"findOne\", \"insert\", \"remove\", \"update\", \"upsert\"];\n", "import LocalCollection from './local_collection.js';\nimport { hasOwn } from './common.js';\nimport { ASYNC_CURSOR_METHODS, getAsyncMethodName } from './constants';\n\n// Cursor: a specification for a particular subset of documents, w/ a defined\n// order, limit, and offset.  creating a Cursor with LocalCollection.find(),\nexport default class Cursor {\n  // don't call this ctor directly.  use LocalCollection.find().\n  constructor(collection, selector, options = {}) {\n    this.collection = collection;\n    this.sorter = null;\n    this.matcher = new Minimongo.Matcher(selector);\n\n    if (LocalCollection._selectorIsIdPerhapsAsObject(selector)) {\n      // stash for fast _id and { _id }\n      this._selectorId = hasOwn.call(selector, '_id') ? selector._id : selector;\n    } else {\n      this._selectorId = undefined;\n\n      if (this.matcher.hasGeoQuery() || options.sort) {\n        this.sorter = new Minimongo.Sorter(options.sort || []);\n      }\n    }\n\n    this.skip = options.skip || 0;\n    this.limit = options.limit;\n    this.fields = options.projection || options.fields;\n\n    this._projectionFn = LocalCollection._compileProjection(this.fields || {});\n\n    this._transform = LocalCollection.wrapTransform(options.transform);\n\n    // by default, queries register w/ Tracker when it is available.\n    if (typeof Tracker !== 'undefined') {\n      this.reactive = options.reactive === undefined ? true : options.reactive;\n    }\n  }\n\n  /**\n   * @deprecated in 2.9\n   * @summary Returns the number of documents that match a query. This method is\n   *          [deprecated since MongoDB 4.0](https://www.mongodb.com/docs/v4.4/reference/command/count/);\n   *          see `Collection.countDocuments` and\n   *          `Collection.estimatedDocumentCount` for a replacement.\n   * @memberOf Mongo.Cursor\n   * @method  count\n   * @instance\n   * @locus Anywhere\n   * @returns {Number}\n   */\n  count() {\n    if (this.reactive) {\n      // allow the observe to be unordered\n      this._depend({ added: true, removed: true }, true);\n    }\n\n    return this._getRawObjects({\n      ordered: true,\n    }).length;\n  }\n\n  /**\n   * @summary Return all matching documents as an Array.\n   * @memberOf Mongo.Cursor\n   * @method  fetch\n   * @instance\n   * @locus Anywhere\n   * @returns {Object[]}\n   */\n  fetch() {\n    const result = [];\n\n    this.forEach(doc => {\n      result.push(doc);\n    });\n\n    return result;\n  }\n\n  [Symbol.iterator]() {\n    if (this.reactive) {\n      this._depend({\n        addedBefore: true,\n        removed: true,\n        changed: true,\n        movedBefore: true,\n      });\n    }\n\n    let index = 0;\n    const objects = this._getRawObjects({ ordered: true });\n\n    return {\n      next: () => {\n        if (index < objects.length) {\n          // This doubles as a clone operation.\n          let element = this._projectionFn(objects[index++]);\n\n          if (this._transform) element = this._transform(element);\n\n          return { value: element };\n        }\n\n        return { done: true };\n      },\n    };\n  }\n\n  [Symbol.asyncIterator]() {\n    const syncResult = this[Symbol.iterator]();\n    return {\n      async next() {\n        return Promise.resolve(syncResult.next());\n      },\n    };\n  }\n\n  /**\n   * @callback IterationCallback\n   * @param {Object} doc\n   * @param {Number} index\n   */\n  /**\n   * @summary Call `callback` once for each matching document, sequentially and\n   *          synchronously.\n   * @locus Anywhere\n   * @method  forEach\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   */\n  forEach(callback, thisArg) {\n    if (this.reactive) {\n      this._depend({\n        addedBefore: true,\n        removed: true,\n        changed: true,\n        movedBefore: true,\n      });\n    }\n\n    this._getRawObjects({ ordered: true }).forEach((element, i) => {\n      // This doubles as a clone operation.\n      element = this._projectionFn(element);\n\n      if (this._transform) {\n        element = this._transform(element);\n      }\n\n      callback.call(thisArg, element, i, this);\n    });\n  }\n\n  getTransform() {\n    return this._transform;\n  }\n\n  /**\n   * @summary Map callback over all matching documents.  Returns an Array.\n   * @locus Anywhere\n   * @method map\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   */\n  map(callback, thisArg) {\n    const result = [];\n\n    this.forEach((doc, i) => {\n      result.push(callback.call(thisArg, doc, i, this));\n    });\n\n    return result;\n  }\n\n  // options to contain:\n  //  * callbacks for observe():\n  //    - addedAt (document, atIndex)\n  //    - added (document)\n  //    - changedAt (newDocument, oldDocument, atIndex)\n  //    - changed (newDocument, oldDocument)\n  //    - removedAt (document, atIndex)\n  //    - removed (document)\n  //    - movedTo (document, oldIndex, newIndex)\n  //\n  // attributes available on returned query handle:\n  //  * stop(): end updates\n  //  * collection: the collection this query is querying\n  //\n  // iff x is a returned query handle, (x instanceof\n  // LocalCollection.ObserveHandle) is true\n  //\n  // initial results delivered through added callback\n  // XXX maybe callbacks should take a list of objects, to expose transactions?\n  // XXX maybe support field limiting (to limit what you're notified on)\n\n  /**\n   * @summary Watch a query.  Receive callbacks as the result set changes.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observe(options) {\n    return LocalCollection._observeFromObserveChanges(this, options);\n  }\n\n  /**\n   * @summary Watch a query.  Receive callbacks as the result set changes.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   */\n  observeAsync(options) {\n    return new Promise(resolve => resolve(this.observe(options)));\n  }\n\n  /**\n   * @summary Watch a query. Receive callbacks as the result set changes. Only\n   *          the differences between the old and new documents are passed to\n   *          the callbacks.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observeChanges(options) {\n    const ordered = LocalCollection._observeChangesCallbacksAreOrdered(options);\n\n    // there are several places that assume you aren't combining skip/limit with\n    // unordered observe.  eg, update's EJSON.clone, and the \"there are several\"\n    // comment in _modifyAndNotify\n    // XXX allow skip/limit with unordered observe\n    if (!options._allow_unordered && !ordered && (this.skip || this.limit)) {\n      throw new Error(\n        \"Must use an ordered observe with skip or limit (i.e. 'addedBefore' \" +\n          \"for observeChanges or 'addedAt' for observe, instead of 'added').\"\n      );\n    }\n\n    if (this.fields && (this.fields._id === 0 || this.fields._id === false)) {\n      throw Error(\"You may not observe a cursor with {fields: {_id: 0}}\");\n    }\n\n    const distances =\n      this.matcher.hasGeoQuery() && ordered && new LocalCollection._IdMap();\n\n    const query = {\n      cursor: this,\n      dirty: false,\n      distances,\n      matcher: this.matcher, // not fast pathed\n      ordered,\n      projectionFn: this._projectionFn,\n      resultsSnapshot: null,\n      sorter: ordered && this.sorter,\n    };\n\n    let qid;\n\n    // Non-reactive queries call added[Before] and then never call anything\n    // else.\n    if (this.reactive) {\n      qid = this.collection.next_qid++;\n      this.collection.queries[qid] = query;\n    }\n\n    query.results = this._getRawObjects({\n      ordered,\n      distances: query.distances,\n    });\n\n    if (this.collection.paused) {\n      query.resultsSnapshot = ordered ? [] : new LocalCollection._IdMap();\n    }\n\n    // wrap callbacks we were passed. callbacks only fire when not paused and\n    // are never undefined\n    // Filters out blacklisted fields according to cursor's projection.\n    // XXX wrong place for this?\n\n    // furthermore, callbacks enqueue until the operation we're working on is\n    // done.\n    const wrapCallback = (fn) => {\n      if (!fn) {\n        return () => {};\n      }\n\n      const self = this;\n\n      return function (/* args*/) {\n        if (self.collection.paused) {\n          return;\n        }\n\n        const args = arguments;\n\n        self.collection._observeQueue.queueTask(() => {\n          fn.apply(this, args);\n        });\n      };\n    };\n\n    query.added = wrapCallback(options.added);\n    query.changed = wrapCallback(options.changed);\n    query.removed = wrapCallback(options.removed);\n\n    if (ordered) {\n      query.addedBefore = wrapCallback(options.addedBefore);\n      query.movedBefore = wrapCallback(options.movedBefore);\n    }\n\n    if (!options._suppress_initial && !this.collection.paused) {\n      const handler = (doc) => {\n        const fields = EJSON.clone(doc);\n\n        delete fields._id;\n\n        if (ordered) {\n          query.addedBefore(doc._id, this._projectionFn(fields), null);\n        }\n\n        query.added(doc._id, this._projectionFn(fields));\n      };\n      // it means it's just an array\n      if (query.results.length) {\n        for (const doc of query.results) {\n          handler(doc);\n        }\n      }\n      // it means it's an id map\n      if (query.results?.size?.()) {\n        query.results.forEach(handler);\n      }\n    }\n\n    const handle = Object.assign(new LocalCollection.ObserveHandle(), {\n      collection: this.collection,\n      stop: () => {\n        if (this.reactive) {\n          delete this.collection.queries[qid];\n        }\n      },\n      isReady: false,\n      isReadyPromise: null,\n    });\n\n    if (this.reactive && Tracker.active) {\n      // XXX in many cases, the same observe will be recreated when\n      // the current autorun is rerun.  we could save work by\n      // letting it linger across rerun and potentially get\n      // repurposed if the same observe is performed, using logic\n      // similar to that of Meteor.subscribe.\n      Tracker.onInvalidate(() => {\n        handle.stop();\n      });\n    }\n\n    // run the observe callbacks resulting from the initial contents\n    // before we leave the observe.\n    const drainResult = this.collection._observeQueue.drain();\n\n    if (drainResult instanceof Promise) {\n      handle.isReadyPromise = drainResult;\n      drainResult.then(() => (handle.isReady = true));\n    } else {\n      handle.isReady = true;\n      handle.isReadyPromise = Promise.resolve();\n    }\n\n    return handle;\n  }\n\n  /**\n   * @summary Watch a query. Receive callbacks as the result set changes. Only\n   *          the differences between the old and new documents are passed to\n   *          the callbacks.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observeChangesAsync(options) {\n    return new Promise((resolve) => {\n      const handle = this.observeChanges(options);\n      handle.isReadyPromise.then(() => resolve(handle));\n    });\n  }\n\n  // XXX Maybe we need a version of observe that just calls a callback if\n  // anything changed.\n  _depend(changers, _allow_unordered) {\n    if (Tracker.active) {\n      const dependency = new Tracker.Dependency();\n      const notify = dependency.changed.bind(dependency);\n\n      dependency.depend();\n\n      const options = { _allow_unordered, _suppress_initial: true };\n\n      ['added', 'addedBefore', 'changed', 'movedBefore', 'removed'].forEach(\n        fn => {\n          if (changers[fn]) {\n            options[fn] = notify;\n          }\n        }\n      );\n\n      // observeChanges will stop() when this computation is invalidated\n      this.observeChanges(options);\n    }\n  }\n\n  _getCollectionName() {\n    return this.collection.name;\n  }\n\n  // Returns a collection of matching objects, but doesn't deep copy them.\n  //\n  // If ordered is set, returns a sorted array, respecting sorter, skip, and\n  // limit properties of the query provided that options.applySkipLimit is\n  // not set to false (#1201). If sorter is falsey, no sort -- you get the\n  // natural order.\n  //\n  // If ordered is not set, returns an object mapping from ID to doc (sorter,\n  // skip and limit should not be set).\n  //\n  // If ordered is set and this cursor is a $near geoquery, then this function\n  // will use an _IdMap to track each distance from the $near argument point in\n  // order to use it as a sort key. If an _IdMap is passed in the 'distances'\n  // argument, this function will clear it and use it for this purpose\n  // (otherwise it will just create its own _IdMap). The observeChanges\n  // implementation uses this to remember the distances after this function\n  // returns.\n  _getRawObjects(options = {}) {\n    // By default this method will respect skip and limit because .fetch(),\n    // .forEach() etc... expect this behaviour. It can be forced to ignore\n    // skip and limit by setting applySkipLimit to false (.count() does this,\n    // for example)\n    const applySkipLimit = options.applySkipLimit !== false;\n\n    // XXX use OrderedDict instead of array, and make IdMap and OrderedDict\n    // compatible\n    const results = options.ordered ? [] : new LocalCollection._IdMap();\n\n    // fast path for single ID value\n    if (this._selectorId !== undefined) {\n      // If you have non-zero skip and ask for a single id, you get nothing.\n      // This is so it matches the behavior of the '{_id: foo}' path.\n      if (applySkipLimit && this.skip) {\n        return results;\n      }\n\n      const selectedDoc = this.collection._docs.get(this._selectorId);\n      if (selectedDoc) {\n        if (options.ordered) {\n          results.push(selectedDoc);\n        } else {\n          results.set(this._selectorId, selectedDoc);\n        }\n      }\n      return results;\n    }\n\n    // slow path for arbitrary selector, sort, skip, limit\n\n    // in the observeChanges case, distances is actually part of the \"query\"\n    // (ie, live results set) object.  in other cases, distances is only used\n    // inside this function.\n    let distances;\n    if (this.matcher.hasGeoQuery() && options.ordered) {\n      if (options.distances) {\n        distances = options.distances;\n        distances.clear();\n      } else {\n        distances = new LocalCollection._IdMap();\n      }\n    }\n\n    Meteor._runFresh(() => {\n      this.collection._docs.forEach((doc, id) => {\n        const matchResult = this.matcher.documentMatches(doc);\n        if (matchResult.result) {\n          if (options.ordered) {\n            results.push(doc);\n\n            if (distances && matchResult.distance !== undefined) {\n              distances.set(id, matchResult.distance);\n            }\n          } else {\n            results.set(id, doc);\n          }\n        }\n\n        // Override to ensure all docs are matched if ignoring skip & limit\n        if (!applySkipLimit) {\n          return true;\n        }\n\n        // Fast path for limited unsorted queries.\n        // XXX 'length' check here seems wrong for ordered\n        return (\n          !this.limit || this.skip || this.sorter || results.length !== this.limit\n        );\n      });\n    });\n\n    if (!options.ordered) {\n      return results;\n    }\n\n    if (this.sorter) {\n      results.sort(this.sorter.getComparator({ distances }));\n    }\n\n    // Return the full set of results if there is no skip or limit or if we're\n    // ignoring them\n    if (!applySkipLimit || (!this.limit && !this.skip)) {\n      return results;\n    }\n\n    return results.slice(\n      this.skip,\n      this.limit ? this.limit + this.skip : results.length\n    );\n  }\n\n  _publishCursor(subscription) {\n    // XXX minimongo should not depend on mongo-livedata!\n    if (!Package.mongo) {\n      throw new Error(\n        \"Can't publish from Minimongo without the `mongo` package.\"\n      );\n    }\n\n    if (!this.collection.name) {\n      throw new Error(\n        \"Can't publish a cursor from a collection without a name.\"\n      );\n    }\n\n    return Package.mongo.Mongo.Collection._publishCursor(\n      this,\n      subscription,\n      this.collection.name\n    );\n  }\n}\n\n// Implements async version of cursor methods to keep collections isomorphic\nASYNC_CURSOR_METHODS.forEach(method => {\n  const asyncName = getAsyncMethodName(method);\n  Cursor.prototype[asyncName] = function(...args) {\n    try {\n      return Promise.resolve(this[method].apply(this, args));\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  };\n});\n", "import Cursor from './cursor.js';\nimport Observe<PERSON><PERSON><PERSON> from './observe_handle.js';\nimport {\n  hasOwn,\n  isIndexable,\n  isNumericKey,\n  isOperatorObject,\n  populateDocumentWithQueryFields,\n  projectionDetails,\n} from './common.js';\n\nimport { getAsyncMethodName } from './constants';\n\n// XXX type checking on selectors (graceful error if malformed)\n\n// LocalCollection: a set of documents that supports queries and modifiers.\nexport default class LocalCollection {\n  constructor(name) {\n    this.name = name;\n    // _id -> document (also containing id)\n    this._docs = new LocalCollection._IdMap;\n\n    this._observeQueue = Meteor.isClient\n      ? new Meteor._SynchronousQueue()\n      : new Meteor._AsynchronousQueue();\n\n    this.next_qid = 1; // live query id generator\n\n    // qid -> live query object. keys:\n    //  ordered: bool. ordered queries have addedBefore/movedBefore callbacks.\n    //  results: array (ordered) or object (unordered) of current results\n    //    (aliased with this._docs!)\n    //  resultsSnapshot: snapshot of results. null if not paused.\n    //  cursor: Cursor object for the query.\n    //  selector, sorter, (callbacks): functions\n    this.queries = Object.create(null);\n\n    // null if not saving originals; an IdMap from id to original document value\n    // if saving originals. See comments before saveOriginals().\n    this._savedOriginals = null;\n\n    // True when observers are paused and we should not send callbacks.\n    this.paused = false;\n  }\n\n  countDocuments(selector, options) {\n    return this.find(selector ?? {}, options).countAsync();\n  }\n\n  estimatedDocumentCount(options) {\n    return this.find({}, options).countAsync();\n  }\n\n  // options may include sort, skip, limit, reactive\n  // sort may be any of these forms:\n  //     {a: 1, b: -1}\n  //     [[\"a\", \"asc\"], [\"b\", \"desc\"]]\n  //     [\"a\", [\"b\", \"desc\"]]\n  //   (in the first form you're beholden to key enumeration order in\n  //   your javascript VM)\n  //\n  // reactive: if given, and false, don't register with Tracker (default\n  // is true)\n  //\n  // XXX possibly should support retrieving a subset of fields? and\n  // have it be a hint (ignored on the client, when not copying the\n  // doc?)\n  //\n  // XXX sort does not yet support subkeys ('a.b') .. fix that!\n  // XXX add one more sort form: \"key\"\n  // XXX tests\n  find(selector, options) {\n    // default syntax for everything is to omit the selector argument.\n    // but if selector is explicitly passed in as false or undefined, we\n    // want a selector that matches nothing.\n    if (arguments.length === 0) {\n      selector = {};\n    }\n\n    return new LocalCollection.Cursor(this, selector, options);\n  }\n\n  findOne(selector, options = {}) {\n    if (arguments.length === 0) {\n      selector = {};\n    }\n\n    // NOTE: by setting limit 1 here, we end up using very inefficient\n    // code that recomputes the whole query on each update. The upside is\n    // that when you reactively depend on a findOne you only get\n    // invalidated when the found object changes, not any object in the\n    // collection. Most findOne will be by id, which has a fast path, so\n    // this might not be a big deal. In most cases, invalidation causes\n    // the called to re-query anyway, so this should be a net performance\n    // improvement.\n    options.limit = 1;\n\n    return this.find(selector, options).fetch()[0];\n  }\n  async findOneAsync(selector, options = {}) {\n    if (arguments.length === 0) {\n      selector = {};\n    }\n    options.limit = 1;\n    return (await this.find(selector, options).fetchAsync())[0];\n  }\n  prepareInsert(doc) {\n    assertHasValidFieldNames(doc);\n\n    // if you really want to use ObjectIDs, set this global.\n    // Mongo.Collection specifies its own ids and does not use this code.\n    if (!hasOwn.call(doc, '_id')) {\n      doc._id = LocalCollection._useOID ? new MongoID.ObjectID() : Random.id();\n    }\n\n    const id = doc._id;\n\n    if (this._docs.has(id)) {\n      throw MinimongoError(`Duplicate _id '${id}'`);\n    }\n\n    this._saveOriginal(id, undefined);\n    this._docs.set(id, doc);\n\n    return id;\n  }\n\n  // XXX possibly enforce that 'undefined' does not appear (we assume\n  // this in our handling of null and $exists)\n  insert(doc, callback) {\n    doc = EJSON.clone(doc);\n    const id = this.prepareInsert(doc);\n    const queriesToRecompute = [];\n\n    // trigger live queries that match\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const matchResult = query.matcher.documentMatches(doc);\n\n      if (matchResult.result) {\n        if (query.distances && matchResult.distance !== undefined) {\n          query.distances.set(id, matchResult.distance);\n        }\n\n        if (query.cursor.skip || query.cursor.limit) {\n          queriesToRecompute.push(qid);\n        } else {\n          LocalCollection._insertInResultsSync(query, doc);\n        }\n      }\n    }\n\n    queriesToRecompute.forEach(qid => {\n      if (this.queries[qid]) {\n        this._recomputeResults(this.queries[qid]);\n      }\n    });\n\n    this._observeQueue.drain();\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, id);\n      });\n    }\n\n    return id;\n  }\n  async insertAsync(doc, callback) {\n    doc = EJSON.clone(doc);\n    const id = this.prepareInsert(doc);\n    const queriesToRecompute = [];\n\n    // trigger live queries that match\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const matchResult = query.matcher.documentMatches(doc);\n\n      if (matchResult.result) {\n        if (query.distances && matchResult.distance !== undefined) {\n          query.distances.set(id, matchResult.distance);\n        }\n\n        if (query.cursor.skip || query.cursor.limit) {\n          queriesToRecompute.push(qid);\n        } else {\n          await LocalCollection._insertInResultsAsync(query, doc);\n        }\n      }\n    }\n\n    queriesToRecompute.forEach(qid => {\n      if (this.queries[qid]) {\n        this._recomputeResults(this.queries[qid]);\n      }\n    });\n\n    await this._observeQueue.drain();\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, id);\n      });\n    }\n\n    return id;\n  }\n\n  // Pause the observers. No callbacks from observers will fire until\n  // 'resumeObservers' is called.\n  pauseObservers() {\n    // No-op if already paused.\n    if (this.paused) {\n      return;\n    }\n\n    // Set the 'paused' flag such that new observer messages don't fire.\n    this.paused = true;\n\n    // Take a snapshot of the query results for each query.\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n      query.resultsSnapshot = EJSON.clone(query.results);\n    });\n  }\n\n  clearResultQueries(callback) {\n    const result = this._docs.size();\n\n    this._docs.clear();\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.ordered) {\n        query.results = [];\n      } else {\n        query.results.clear();\n      }\n    });\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n\n  prepareRemove(selector) {\n    const matcher = new Minimongo.Matcher(selector);\n    const remove = [];\n\n    this._eachPossiblyMatchingDocSync(selector, (doc, id) => {\n      if (matcher.documentMatches(doc).result) {\n        remove.push(id);\n      }\n    });\n\n    const queriesToRecompute = [];\n    const queryRemove = [];\n\n    for (let i = 0; i < remove.length; i++) {\n      const removeId = remove[i];\n      const removeDoc = this._docs.get(removeId);\n\n      Object.keys(this.queries).forEach(qid => {\n        const query = this.queries[qid];\n\n        if (query.dirty) {\n          return;\n        }\n\n        if (query.matcher.documentMatches(removeDoc).result) {\n          if (query.cursor.skip || query.cursor.limit) {\n            queriesToRecompute.push(qid);\n          } else {\n            queryRemove.push({qid, doc: removeDoc});\n          }\n        }\n      });\n\n      this._saveOriginal(removeId, removeDoc);\n      this._docs.remove(removeId);\n    }\n\n    return { queriesToRecompute, queryRemove, remove };\n  }\n\n  remove(selector, callback) {\n    // Easy special case: if we're not calling observeChanges callbacks and\n    // we're not saving originals and we got asked to remove everything, then\n    // just empty everything directly.\n    if (this.paused && !this._savedOriginals && EJSON.equals(selector, {})) {\n      return this.clearResultQueries(callback);\n    }\n\n    const { queriesToRecompute, queryRemove, remove } = this.prepareRemove(selector);\n\n    // run live query callbacks _after_ we've removed the documents.\n    queryRemove.forEach(remove => {\n      const query = this.queries[remove.qid];\n\n      if (query) {\n        query.distances && query.distances.remove(remove.doc._id);\n        LocalCollection._removeFromResultsSync(query, remove.doc);\n      }\n    });\n\n    queriesToRecompute.forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query);\n      }\n    });\n\n    this._observeQueue.drain();\n\n    const result = remove.length;\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  async removeAsync(selector, callback) {\n    // Easy special case: if we're not calling observeChanges callbacks and\n    // we're not saving originals and we got asked to remove everything, then\n    // just empty everything directly.\n    if (this.paused && !this._savedOriginals && EJSON.equals(selector, {})) {\n      return this.clearResultQueries(callback);\n    }\n\n    const { queriesToRecompute, queryRemove, remove } = this.prepareRemove(selector);\n\n    // run live query callbacks _after_ we've removed the documents.\n    for (const remove of queryRemove) {\n      const query = this.queries[remove.qid];\n\n      if (query) {\n        query.distances && query.distances.remove(remove.doc._id);\n        await LocalCollection._removeFromResultsAsync(query, remove.doc);\n      }\n    }\n    queriesToRecompute.forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query);\n      }\n    });\n\n    await this._observeQueue.drain();\n\n    const result = remove.length;\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  // Resume the observers. Observers immediately receive change\n  // notifications to bring them to the current state of the\n  // database. Note that this is not just replaying all the changes that\n  // happened during the pause, it is a smarter 'coalesced' diff.\n  _resumeObservers() {\n    // No-op if not paused.\n    if (!this.paused) {\n      return;\n    }\n\n    // Unset the 'paused' flag. Make sure to do this first, otherwise\n    // observer methods won't actually fire when we trigger them.\n    this.paused = false;\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        query.dirty = false;\n\n        // re-compute results will perform `LocalCollection._diffQueryChanges`\n        // automatically.\n        this._recomputeResults(query, query.resultsSnapshot);\n      } else {\n        // Diff the current results against the snapshot and send to observers.\n        // pass the query object for its observer callbacks.\n        LocalCollection._diffQueryChanges(\n          query.ordered,\n          query.resultsSnapshot,\n          query.results,\n          query,\n          {projectionFn: query.projectionFn}\n        );\n      }\n\n      query.resultsSnapshot = null;\n    });\n  }\n\n  async resumeObserversServer() {\n    this._resumeObservers();\n    await this._observeQueue.drain();\n  }\n  resumeObserversClient() {\n    this._resumeObservers();\n    this._observeQueue.drain();\n  }\n\n  retrieveOriginals() {\n    if (!this._savedOriginals) {\n      throw new Error('Called retrieveOriginals without saveOriginals');\n    }\n\n    const originals = this._savedOriginals;\n\n    this._savedOriginals = null;\n\n    return originals;\n  }\n\n  // To track what documents are affected by a piece of code, call\n  // saveOriginals() before it and retrieveOriginals() after it.\n  // retrieveOriginals returns an object whose keys are the ids of the documents\n  // that were affected since the call to saveOriginals(), and the values are\n  // equal to the document's contents at the time of saveOriginals. (In the case\n  // of an inserted document, undefined is the value.) You must alternate\n  // between calls to saveOriginals() and retrieveOriginals().\n  saveOriginals() {\n    if (this._savedOriginals) {\n      throw new Error('Called saveOriginals twice without retrieveOriginals');\n    }\n\n    this._savedOriginals = new LocalCollection._IdMap;\n  }\n\n  prepareUpdate(selector) {\n    // Save the original results of any query that we might need to\n    // _recomputeResults on, because _modifyAndNotify will mutate the objects in\n    // it. (We don't need to save the original results of paused queries because\n    // they already have a resultsSnapshot and we won't be diffing in\n    // _recomputeResults.)\n    const qidToOriginalResults = {};\n\n    // We should only clone each document once, even if it appears in multiple\n    // queries\n    const docMap = new LocalCollection._IdMap;\n    const idsMatched = LocalCollection._idsMatchedBySelector(selector);\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if ((query.cursor.skip || query.cursor.limit) && ! this.paused) {\n        // Catch the case of a reactive `count()` on a cursor with skip\n        // or limit, which registers an unordered observe. This is a\n        // pretty rare case, so we just clone the entire result set with\n        // no optimizations for documents that appear in these result\n        // sets and other queries.\n        if (query.results instanceof LocalCollection._IdMap) {\n          qidToOriginalResults[qid] = query.results.clone();\n          return;\n        }\n\n        if (!(query.results instanceof Array)) {\n          throw new Error('Assertion failed: query.results not an array');\n        }\n\n        // Clones a document to be stored in `qidToOriginalResults`\n        // because it may be modified before the new and old result sets\n        // are diffed. But if we know exactly which document IDs we're\n        // going to modify, then we only need to clone those.\n        const memoizedCloneIfNeeded = doc => {\n          if (docMap.has(doc._id)) {\n            return docMap.get(doc._id);\n          }\n\n          const docToMemoize = (\n            idsMatched &&\n            !idsMatched.some(id => EJSON.equals(id, doc._id))\n          ) ? doc : EJSON.clone(doc);\n\n          docMap.set(doc._id, docToMemoize);\n\n          return docToMemoize;\n        };\n\n        qidToOriginalResults[qid] = query.results.map(memoizedCloneIfNeeded);\n      }\n    });\n\n    return qidToOriginalResults;\n  }\n\n  finishUpdate({ options, updateCount, callback, insertedId }) {\n\n\n    // Return the number of affected documents, or in the upsert case, an object\n    // containing the number of affected docs and the id of the doc that was\n    // inserted, if any.\n    let result;\n    if (options._returnObject) {\n      result = { numberAffected: updateCount };\n\n      if (insertedId !== undefined) {\n        result.insertedId = insertedId;\n      }\n    } else {\n      result = updateCount;\n    }\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  // XXX atomicity: if multi is true, and one modification fails, do\n  // we rollback the whole operation, or what?\n  async updateAsync(selector, mod, options, callback) {\n    if (! callback && options instanceof Function) {\n      callback = options;\n      options = null;\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    const matcher = new Minimongo.Matcher(selector, true);\n\n    const qidToOriginalResults = this.prepareUpdate(selector);\n\n    let recomputeQids = {};\n\n    let updateCount = 0;\n\n    await this._eachPossiblyMatchingDocAsync(selector, async (doc, id) => {\n      const queryResult = matcher.documentMatches(doc);\n\n      if (queryResult.result) {\n        // XXX Should we save the original even if mod ends up being a no-op?\n        this._saveOriginal(id, doc);\n        recomputeQids = await this._modifyAndNotifyAsync(\n          doc,\n          mod,\n          queryResult.arrayIndices\n        );\n\n        ++updateCount;\n\n        if (!options.multi) {\n          return false; // break\n        }\n      }\n\n      return true;\n    });\n\n    Object.keys(recomputeQids).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query, qidToOriginalResults[qid]);\n      }\n    });\n\n    await this._observeQueue.drain();\n\n    // If we are doing an upsert, and we didn't modify any documents yet, then\n    // it's time to do an insert. Figure out what document we are inserting, and\n    // generate an id for it.\n    let insertedId;\n    if (updateCount === 0 && options.upsert) {\n      const doc = LocalCollection._createUpsertDocument(selector, mod);\n      if (!doc._id && options.insertedId) {\n        doc._id = options.insertedId;\n      }\n\n      insertedId = await this.insertAsync(doc);\n      updateCount = 1;\n    }\n\n    return this.finishUpdate({\n      options,\n      insertedId,\n      updateCount,\n      callback,\n    });\n  }\n  // XXX atomicity: if multi is true, and one modification fails, do\n  // we rollback the whole operation, or what?\n  update(selector, mod, options, callback) {\n    if (! callback && options instanceof Function) {\n      callback = options;\n      options = null;\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    const matcher = new Minimongo.Matcher(selector, true);\n\n    const qidToOriginalResults = this.prepareUpdate(selector);\n\n    let recomputeQids = {};\n\n    let updateCount = 0;\n\n    this._eachPossiblyMatchingDocSync(selector, (doc, id) => {\n      const queryResult = matcher.documentMatches(doc);\n\n      if (queryResult.result) {\n        // XXX Should we save the original even if mod ends up being a no-op?\n        this._saveOriginal(id, doc);\n        recomputeQids = this._modifyAndNotifySync(\n          doc,\n          mod,\n          queryResult.arrayIndices\n        );\n\n        ++updateCount;\n\n        if (!options.multi) {\n          return false; // break\n        }\n      }\n\n      return true;\n    });\n\n    Object.keys(recomputeQids).forEach(qid => {\n      const query = this.queries[qid];\n      if (query) {\n        this._recomputeResults(query, qidToOriginalResults[qid]);\n      }\n    });\n\n    this._observeQueue.drain();\n\n\n    // If we are doing an upsert, and we didn't modify any documents yet, then\n    // it's time to do an insert. Figure out what document we are inserting, and\n    // generate an id for it.\n    let insertedId;\n    if (updateCount === 0 && options.upsert) {\n      const doc = LocalCollection._createUpsertDocument(selector, mod);\n      if (!doc._id && options.insertedId) {\n        doc._id = options.insertedId;\n      }\n\n      insertedId = this.insert(doc);\n      updateCount = 1;\n    }\n\n\n    return this.finishUpdate({\n      options,\n      updateCount,\n      callback,\n      selector,\n      mod,\n    });\n  }\n\n  // A convenience wrapper on update. LocalCollection.upsert(sel, mod) is\n  // equivalent to LocalCollection.update(sel, mod, {upsert: true,\n  // _returnObject: true}).\n  upsert(selector, mod, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.update(\n      selector,\n      mod,\n      Object.assign({}, options, {upsert: true, _returnObject: true}),\n      callback\n    );\n  }\n\n  upsertAsync(selector, mod, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.updateAsync(\n      selector,\n      mod,\n      Object.assign({}, options, {upsert: true, _returnObject: true}),\n      callback\n    );\n  }\n\n  // Iterates over a subset of documents that could match selector; calls\n  // fn(doc, id) on each of them.  Specifically, if selector specifies\n  // specific _id's, it only looks at those.  doc is *not* cloned: it is the\n  // same object that is in _docs.\n  async _eachPossiblyMatchingDocAsync(selector, fn) {\n    const specificIds = LocalCollection._idsMatchedBySelector(selector);\n\n    if (specificIds) {\n      for (const id of specificIds) {\n        const doc = this._docs.get(id);\n\n        if (doc && ! (await fn(doc, id))) {\n          break\n        }\n      }\n    } else {\n      await this._docs.forEachAsync(fn);\n    }\n  }\n  _eachPossiblyMatchingDocSync(selector, fn) {\n    const specificIds = LocalCollection._idsMatchedBySelector(selector);\n\n    if (specificIds) {\n      for (const id of specificIds) {\n        const doc = this._docs.get(id);\n\n        if (doc && !fn(doc, id)) {\n          break\n        }\n      }\n    } else {\n      this._docs.forEach(fn);\n    }\n  }\n\n  _getMatchedDocAndModify(doc, mod, arrayIndices) {\n    const matched_before = {};\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        return;\n      }\n\n      if (query.ordered) {\n        matched_before[qid] = query.matcher.documentMatches(doc).result;\n      } else {\n        // Because we don't support skip or limit (yet) in unordered queries, we\n        // can just do a direct lookup.\n        matched_before[qid] = query.results.has(doc._id);\n      }\n    });\n\n    return matched_before;\n  }\n\n  _modifyAndNotifySync(doc, mod, arrayIndices) {\n\n    const matched_before = this._getMatchedDocAndModify(doc, mod, arrayIndices);\n\n    const old_doc = EJSON.clone(doc);\n    LocalCollection._modify(doc, mod, {arrayIndices});\n\n    const recomputeQids = {};\n\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const afterMatch = query.matcher.documentMatches(doc);\n      const after = afterMatch.result;\n      const before = matched_before[qid];\n\n      if (after && query.distances && afterMatch.distance !== undefined) {\n        query.distances.set(doc._id, afterMatch.distance);\n      }\n\n      if (query.cursor.skip || query.cursor.limit) {\n        // We need to recompute any query where the doc may have been in the\n        // cursor's window either before or after the update. (Note that if skip\n        // or limit is set, \"before\" and \"after\" being true do not necessarily\n        // mean that the document is in the cursor's output after skip/limit is\n        // applied... but if they are false, then the document definitely is NOT\n        // in the output. So it's safe to skip recompute if neither before or\n        // after are true.)\n        if (before || after) {\n          recomputeQids[qid] = true;\n        }\n      } else if (before && !after) {\n        LocalCollection._removeFromResultsSync(query, doc);\n      } else if (!before && after) {\n        LocalCollection._insertInResultsSync(query, doc);\n      } else if (before && after) {\n        LocalCollection._updateInResultsSync(query, doc, old_doc);\n      }\n    }\n    return recomputeQids;\n  }\n\n  async _modifyAndNotifyAsync(doc, mod, arrayIndices) {\n\n    const matched_before = this._getMatchedDocAndModify(doc, mod, arrayIndices);\n\n    const old_doc = EJSON.clone(doc);\n    LocalCollection._modify(doc, mod, {arrayIndices});\n\n    const recomputeQids = {};\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const afterMatch = query.matcher.documentMatches(doc);\n      const after = afterMatch.result;\n      const before = matched_before[qid];\n\n      if (after && query.distances && afterMatch.distance !== undefined) {\n        query.distances.set(doc._id, afterMatch.distance);\n      }\n\n      if (query.cursor.skip || query.cursor.limit) {\n        // We need to recompute any query where the doc may have been in the\n        // cursor's window either before or after the update. (Note that if skip\n        // or limit is set, \"before\" and \"after\" being true do not necessarily\n        // mean that the document is in the cursor's output after skip/limit is\n        // applied... but if they are false, then the document definitely is NOT\n        // in the output. So it's safe to skip recompute if neither before or\n        // after are true.)\n        if (before || after) {\n          recomputeQids[qid] = true;\n        }\n      } else if (before && !after) {\n        await LocalCollection._removeFromResultsAsync(query, doc);\n      } else if (!before && after) {\n        await LocalCollection._insertInResultsAsync(query, doc);\n      } else if (before && after) {\n        await LocalCollection._updateInResultsAsync(query, doc, old_doc);\n      }\n    }\n    return recomputeQids;\n  }\n\n  // Recomputes the results of a query and runs observe callbacks for the\n  // difference between the previous results and the current results (unless\n  // paused). Used for skip/limit queries.\n  //\n  // When this is used by insert or remove, it can just use query.results for\n  // the old results (and there's no need to pass in oldResults), because these\n  // operations don't mutate the documents in the collection. Update needs to\n  // pass in an oldResults which was deep-copied before the modifier was\n  // applied.\n  //\n  // oldResults is guaranteed to be ignored if the query is not paused.\n  _recomputeResults(query, oldResults) {\n    if (this.paused) {\n      // There's no reason to recompute the results now as we're still paused.\n      // By flagging the query as \"dirty\", the recompute will be performed\n      // when resumeObservers is called.\n      query.dirty = true;\n      return;\n    }\n\n    if (!this.paused && !oldResults) {\n      oldResults = query.results;\n    }\n\n    if (query.distances) {\n      query.distances.clear();\n    }\n\n    query.results = query.cursor._getRawObjects({\n      distances: query.distances,\n      ordered: query.ordered\n    });\n\n    if (!this.paused) {\n      LocalCollection._diffQueryChanges(\n        query.ordered,\n        oldResults,\n        query.results,\n        query,\n        {projectionFn: query.projectionFn}\n      );\n    }\n  }\n\n  _saveOriginal(id, doc) {\n    // Are we even trying to save originals?\n    if (!this._savedOriginals) {\n      return;\n    }\n\n    // Have we previously mutated the original (and so 'doc' is not actually\n    // original)?  (Note the 'has' check rather than truth: we store undefined\n    // here for inserted docs!)\n    if (this._savedOriginals.has(id)) {\n      return;\n    }\n\n    this._savedOriginals.set(id, EJSON.clone(doc));\n  }\n}\n\nLocalCollection.Cursor = Cursor;\n\nLocalCollection.ObserveHandle = ObserveHandle;\n\n// XXX maybe move these into another ObserveHelpers package or something\n\n// _CachingChangeObserver is an object which receives observeChanges callbacks\n// and keeps a cache of the current cursor state up to date in this.docs. Users\n// of this class should read the docs field but not modify it. You should pass\n// the \"applyChange\" field as the callbacks to the underlying observeChanges\n// call. Optionally, you can specify your own observeChanges callbacks which are\n// invoked immediately before the docs field is updated; this object is made\n// available as `this` to those callbacks.\nLocalCollection._CachingChangeObserver = class _CachingChangeObserver {\n  constructor(options = {}) {\n    const orderedFromCallbacks = (\n      options.callbacks &&\n      LocalCollection._observeChangesCallbacksAreOrdered(options.callbacks)\n    );\n\n    if (hasOwn.call(options, 'ordered')) {\n      this.ordered = options.ordered;\n\n      if (options.callbacks && options.ordered !== orderedFromCallbacks) {\n        throw Error('ordered option doesn\\'t match callbacks');\n      }\n    } else if (options.callbacks) {\n      this.ordered = orderedFromCallbacks;\n    } else {\n      throw Error('must provide ordered or callbacks');\n    }\n\n    const callbacks = options.callbacks || {};\n\n    if (this.ordered) {\n      this.docs = new OrderedDict(MongoID.idStringify);\n      this.applyChange = {\n        addedBefore: (id, fields, before) => {\n          // Take a shallow copy since the top-level properties can be changed\n          const doc = { ...fields };\n\n          doc._id = id;\n\n          if (callbacks.addedBefore) {\n            callbacks.addedBefore.call(this, id, EJSON.clone(fields), before);\n          }\n\n          // This line triggers if we provide added with movedBefore.\n          if (callbacks.added) {\n            callbacks.added.call(this, id, EJSON.clone(fields));\n          }\n\n          // XXX could `before` be a falsy ID?  Technically\n          // idStringify seems to allow for them -- though\n          // OrderedDict won't call stringify on a falsy arg.\n          this.docs.putBefore(id, doc, before || null);\n        },\n        movedBefore: (id, before) => {\n          if (callbacks.movedBefore) {\n            callbacks.movedBefore.call(this, id, before);\n          }\n\n          this.docs.moveBefore(id, before || null);\n        },\n      };\n    } else {\n      this.docs = new LocalCollection._IdMap;\n      this.applyChange = {\n        added: (id, fields) => {\n          // Take a shallow copy since the top-level properties can be changed\n          const doc = { ...fields };\n\n          if (callbacks.added) {\n            callbacks.added.call(this, id, EJSON.clone(fields));\n          }\n\n          doc._id = id;\n\n          this.docs.set(id,  doc);\n        },\n      };\n    }\n\n    // The methods in _IdMap and OrderedDict used by these callbacks are\n    // identical.\n    this.applyChange.changed = (id, fields) => {\n      const doc = this.docs.get(id);\n\n      if (!doc) {\n        throw new Error(`Unknown id for changed: ${id}`);\n      }\n\n      if (callbacks.changed) {\n        callbacks.changed.call(this, id, EJSON.clone(fields));\n      }\n\n      DiffSequence.applyChanges(doc, fields);\n    };\n\n    this.applyChange.removed = id => {\n      if (callbacks.removed) {\n        callbacks.removed.call(this, id);\n      }\n\n      this.docs.remove(id);\n    };\n  }\n};\n\nLocalCollection._IdMap = class _IdMap extends IdMap {\n  constructor() {\n    super(MongoID.idStringify, MongoID.idParse);\n  }\n};\n\n// Wrap a transform function to return objects that have the _id field\n// of the untransformed document. This ensures that subsystems such as\n// the observe-sequence package that call `observe` can keep track of\n// the documents identities.\n//\n// - Require that it returns objects\n// - If the return value has an _id field, verify that it matches the\n//   original _id field\n// - If the return value doesn't have an _id field, add it back.\nLocalCollection.wrapTransform = transform => {\n  if (!transform) {\n    return null;\n  }\n\n  // No need to doubly-wrap transforms.\n  if (transform.__wrappedTransform__) {\n    return transform;\n  }\n\n  const wrapped = doc => {\n    if (!hasOwn.call(doc, '_id')) {\n      // XXX do we ever have a transform on the oplog's collection? because that\n      // collection has no _id.\n      throw new Error('can only transform documents with _id');\n    }\n\n    const id = doc._id;\n\n    // XXX consider making tracker a weak dependency and checking\n    // Package.tracker here\n    const transformed = Tracker.nonreactive(() => transform(doc));\n\n    if (!LocalCollection._isPlainObject(transformed)) {\n      throw new Error('transform must return object');\n    }\n\n    if (hasOwn.call(transformed, '_id')) {\n      if (!EJSON.equals(transformed._id, id)) {\n        throw new Error('transformed document can\\'t have different _id');\n      }\n    } else {\n      transformed._id = id;\n    }\n\n    return transformed;\n  };\n\n  wrapped.__wrappedTransform__ = true;\n\n  return wrapped;\n};\n\n// XXX the sorted-query logic below is laughably inefficient. we'll\n// need to come up with a better datastructure for this.\n//\n// XXX the logic for observing with a skip or a limit is even more\n// laughably inefficient. we recompute the whole results every time!\n\n// This binary search puts a value between any equal values, and the first\n// lesser value.\nLocalCollection._binarySearch = (cmp, array, value) => {\n  let first = 0;\n  let range = array.length;\n\n  while (range > 0) {\n    const halfRange = Math.floor(range / 2);\n\n    if (cmp(value, array[first + halfRange]) >= 0) {\n      first += halfRange + 1;\n      range -= halfRange + 1;\n    } else {\n      range = halfRange;\n    }\n  }\n\n  return first;\n};\n\nLocalCollection._checkSupportedProjection = fields => {\n  if (fields !== Object(fields) || Array.isArray(fields)) {\n    throw MinimongoError('fields option must be an object');\n  }\n\n  Object.keys(fields).forEach(keyPath => {\n    if (keyPath.split('.').includes('$')) {\n      throw MinimongoError(\n        'Minimongo doesn\\'t support $ operator in projections yet.'\n      );\n    }\n\n    const value = fields[keyPath];\n\n    if (typeof value === 'object' &&\n        ['$elemMatch', '$meta', '$slice'].some(key =>\n          hasOwn.call(value, key)\n        )) {\n      throw MinimongoError(\n        'Minimongo doesn\\'t support operators in projections yet.'\n      );\n    }\n\n    if (![1, 0, true, false].includes(value)) {\n      throw MinimongoError(\n        'Projection values should be one of 1, 0, true, or false'\n      );\n    }\n  });\n};\n\n// Knows how to compile a fields projection to a predicate function.\n// @returns - Function: a closure that filters out an object according to the\n//            fields projection rules:\n//            @param obj - Object: MongoDB-styled document\n//            @returns - Object: a document with the fields filtered out\n//                       according to projection rules. Doesn't retain subfields\n//                       of passed argument.\nLocalCollection._compileProjection = fields => {\n  LocalCollection._checkSupportedProjection(fields);\n\n  const _idProjection = fields._id === undefined ? true : fields._id;\n  const details = projectionDetails(fields);\n\n  // returns transformed doc according to ruleTree\n  const transform = (doc, ruleTree) => {\n    // Special case for \"sets\"\n    if (Array.isArray(doc)) {\n      return doc.map(subdoc => transform(subdoc, ruleTree));\n    }\n\n    const result = details.including ? {} : EJSON.clone(doc);\n\n    Object.keys(ruleTree).forEach(key => {\n      if (doc == null || !hasOwn.call(doc, key)) {\n        return;\n      }\n\n      const rule = ruleTree[key];\n\n      if (rule === Object(rule)) {\n        // For sub-objects/subsets we branch\n        if (doc[key] === Object(doc[key])) {\n          result[key] = transform(doc[key], rule);\n        }\n      } else if (details.including) {\n        // Otherwise we don't even touch this subfield\n        result[key] = EJSON.clone(doc[key]);\n      } else {\n        delete result[key];\n      }\n    });\n\n    return doc != null ? result : doc;\n  };\n\n  return doc => {\n    const result = transform(doc, details.tree);\n\n    if (_idProjection && hasOwn.call(doc, '_id')) {\n      result._id = doc._id;\n    }\n\n    if (!_idProjection && hasOwn.call(result, '_id')) {\n      delete result._id;\n    }\n\n    return result;\n  };\n};\n\n// Calculates the document to insert in case we're doing an upsert and the\n// selector does not match any elements\nLocalCollection._createUpsertDocument = (selector, modifier) => {\n  const selectorDocument = populateDocumentWithQueryFields(selector);\n  const isModify = LocalCollection._isModificationMod(modifier);\n\n  const newDoc = {};\n\n  if (selectorDocument._id) {\n    newDoc._id = selectorDocument._id;\n    delete selectorDocument._id;\n  }\n\n  // This double _modify call is made to help with nested properties (see issue\n  // #8631). We do this even if it's a replacement for validation purposes (e.g.\n  // ambiguous id's)\n  LocalCollection._modify(newDoc, {$set: selectorDocument});\n  LocalCollection._modify(newDoc, modifier, {isInsert: true});\n\n  if (isModify) {\n    return newDoc;\n  }\n\n  // Replacement can take _id from query document\n  const replacement = Object.assign({}, modifier);\n  if (newDoc._id) {\n    replacement._id = newDoc._id;\n  }\n\n  return replacement;\n};\n\nLocalCollection._diffObjects = (left, right, callbacks) => {\n  return DiffSequence.diffObjects(left, right, callbacks);\n};\n\n// ordered: bool.\n// old_results and new_results: collections of documents.\n//    if ordered, they are arrays.\n//    if unordered, they are IdMaps\nLocalCollection._diffQueryChanges = (ordered, oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryChanges(ordered, oldResults, newResults, observer, options)\n;\n\nLocalCollection._diffQueryOrderedChanges = (oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryOrderedChanges(oldResults, newResults, observer, options)\n;\n\nLocalCollection._diffQueryUnorderedChanges = (oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryUnorderedChanges(oldResults, newResults, observer, options)\n;\n\nLocalCollection._findInOrderedResults = (query, doc) => {\n  if (!query.ordered) {\n    throw new Error('Can\\'t call _findInOrderedResults on unordered query');\n  }\n\n  for (let i = 0; i < query.results.length; i++) {\n    if (query.results[i] === doc) {\n      return i;\n    }\n  }\n\n  throw Error('object missing from query');\n};\n\n// If this is a selector which explicitly constrains the match by ID to a finite\n// number of documents, returns a list of their IDs.  Otherwise returns\n// null. Note that the selector may have other restrictions so it may not even\n// match those document!  We care about $in and $and since those are generated\n// access-controlled update and remove.\nLocalCollection._idsMatchedBySelector = selector => {\n  // Is the selector just an ID?\n  if (LocalCollection._selectorIsId(selector)) {\n    return [selector];\n  }\n\n  if (!selector) {\n    return null;\n  }\n\n  // Do we have an _id clause?\n  if (hasOwn.call(selector, '_id')) {\n    // Is the _id clause just an ID?\n    if (LocalCollection._selectorIsId(selector._id)) {\n      return [selector._id];\n    }\n\n    // Is the _id clause {_id: {$in: [\"x\", \"y\", \"z\"]}}?\n    if (selector._id\n        && Array.isArray(selector._id.$in)\n        && selector._id.$in.length\n        && selector._id.$in.every(LocalCollection._selectorIsId)) {\n      return selector._id.$in;\n    }\n\n    return null;\n  }\n\n  // If this is a top-level $and, and any of the clauses constrain their\n  // documents, then the whole selector is constrained by any one clause's\n  // constraint. (Well, by their intersection, but that seems unlikely.)\n  if (Array.isArray(selector.$and)) {\n    for (let i = 0; i < selector.$and.length; ++i) {\n      const subIds = LocalCollection._idsMatchedBySelector(selector.$and[i]);\n\n      if (subIds) {\n        return subIds;\n      }\n    }\n  }\n\n  return null;\n};\n\nLocalCollection._insertInResultsSync = (query, doc) => {\n  const fields = EJSON.clone(doc);\n\n  delete fields._id;\n\n  if (query.ordered) {\n    if (!query.sorter) {\n      query.addedBefore(doc._id, query.projectionFn(fields), null);\n      query.results.push(doc);\n    } else {\n      const i = LocalCollection._insertInSortedList(\n        query.sorter.getComparator({distances: query.distances}),\n        query.results,\n        doc\n      );\n\n      let next = query.results[i + 1];\n      if (next) {\n        next = next._id;\n      } else {\n        next = null;\n      }\n\n      query.addedBefore(doc._id, query.projectionFn(fields), next);\n    }\n\n    query.added(doc._id, query.projectionFn(fields));\n  } else {\n    query.added(doc._id, query.projectionFn(fields));\n    query.results.set(doc._id, doc);\n  }\n};\n\nLocalCollection._insertInResultsAsync = async (query, doc) => {\n  const fields = EJSON.clone(doc);\n\n  delete fields._id;\n\n  if (query.ordered) {\n    if (!query.sorter) {\n      await query.addedBefore(doc._id, query.projectionFn(fields), null);\n      query.results.push(doc);\n    } else {\n      const i = LocalCollection._insertInSortedList(\n        query.sorter.getComparator({distances: query.distances}),\n        query.results,\n        doc\n      );\n\n      let next = query.results[i + 1];\n      if (next) {\n        next = next._id;\n      } else {\n        next = null;\n      }\n\n      await query.addedBefore(doc._id, query.projectionFn(fields), next);\n    }\n\n    await query.added(doc._id, query.projectionFn(fields));\n  } else {\n    await query.added(doc._id, query.projectionFn(fields));\n    query.results.set(doc._id, doc);\n  }\n};\n\nLocalCollection._insertInSortedList = (cmp, array, value) => {\n  if (array.length === 0) {\n    array.push(value);\n    return 0;\n  }\n\n  const i = LocalCollection._binarySearch(cmp, array, value);\n\n  array.splice(i, 0, value);\n\n  return i;\n};\n\nLocalCollection._isModificationMod = mod => {\n  let isModify = false;\n  let isReplace = false;\n\n  Object.keys(mod).forEach(key => {\n    if (key.substr(0, 1) === '$') {\n      isModify = true;\n    } else {\n      isReplace = true;\n    }\n  });\n\n  if (isModify && isReplace) {\n    throw new Error(\n      'Update parameter cannot have both modifier and non-modifier fields.'\n    );\n  }\n\n  return isModify;\n};\n\n// XXX maybe this should be EJSON.isObject, though EJSON doesn't know about\n// RegExp\n// XXX note that _type(undefined) === 3!!!!\nLocalCollection._isPlainObject = x => {\n  return x && LocalCollection._f._type(x) === 3;\n};\n\n// XXX need a strategy for passing the binding of $ into this\n// function, from the compiled selector\n//\n// maybe just {key.up.to.just.before.dollarsign: array_index}\n//\n// XXX atomicity: if one modification fails, do we roll back the whole\n// change?\n//\n// options:\n//   - isInsert is set when _modify is being called to compute the document to\n//     insert as part of an upsert operation. We use this primarily to figure\n//     out when to set the fields in $setOnInsert, if present.\nLocalCollection._modify = (doc, modifier, options = {}) => {\n  if (!LocalCollection._isPlainObject(modifier)) {\n    throw MinimongoError('Modifier must be an object');\n  }\n\n  // Make sure the caller can't mutate our data structures.\n  modifier = EJSON.clone(modifier);\n\n  const isModifier = isOperatorObject(modifier);\n  const newDoc = isModifier ? EJSON.clone(doc) : modifier;\n\n  if (isModifier) {\n    // apply modifiers to the doc.\n    Object.keys(modifier).forEach(operator => {\n      // Treat $setOnInsert as $set if this is an insert.\n      const setOnInsert = options.isInsert && operator === '$setOnInsert';\n      const modFunc = MODIFIERS[setOnInsert ? '$set' : operator];\n      const operand = modifier[operator];\n\n      if (!modFunc) {\n        throw MinimongoError(`Invalid modifier specified ${operator}`);\n      }\n\n      Object.keys(operand).forEach(keypath => {\n        const arg = operand[keypath];\n\n        if (keypath === '') {\n          throw MinimongoError('An empty update path is not valid.');\n        }\n\n        const keyparts = keypath.split('.');\n\n        if (!keyparts.every(Boolean)) {\n          throw MinimongoError(\n            `The update path '${keypath}' contains an empty field name, ` +\n            'which is not allowed.'\n          );\n        }\n\n        const target = findModTarget(newDoc, keyparts, {\n          arrayIndices: options.arrayIndices,\n          forbidArray: operator === '$rename',\n          noCreate: NO_CREATE_MODIFIERS[operator]\n        });\n\n        modFunc(target, keyparts.pop(), arg, keypath, newDoc);\n      });\n    });\n\n    if (doc._id && !EJSON.equals(doc._id, newDoc._id)) {\n      throw MinimongoError(\n        `After applying the update to the document {_id: \"${doc._id}\", ...},` +\n        ' the (immutable) field \\'_id\\' was found to have been altered to ' +\n        `_id: \"${newDoc._id}\"`\n      );\n    }\n  } else {\n    if (doc._id && modifier._id && !EJSON.equals(doc._id, modifier._id)) {\n      throw MinimongoError(\n        `The _id field cannot be changed from {_id: \"${doc._id}\"} to ` +\n        `{_id: \"${modifier._id}\"}`\n      );\n    }\n\n    // replace the whole document\n    assertHasValidFieldNames(modifier);\n  }\n\n  // move new document into place.\n  Object.keys(doc).forEach(key => {\n    // Note: this used to be for (var key in doc) however, this does not\n    // work right in Opera. Deleting from a doc while iterating over it\n    // would sometimes cause opera to skip some keys.\n    if (key !== '_id') {\n      delete doc[key];\n    }\n  });\n\n  Object.keys(newDoc).forEach(key => {\n    doc[key] = newDoc[key];\n  });\n};\n\nLocalCollection._observeFromObserveChanges = (cursor, observeCallbacks) => {\n  const transform = cursor.getTransform() || (doc => doc);\n  let suppressed = !!observeCallbacks._suppress_initial;\n\n  let observeChangesCallbacks;\n  if (LocalCollection._observeCallbacksAreOrdered(observeCallbacks)) {\n    // The \"_no_indices\" option sets all index arguments to -1 and skips the\n    // linear scans required to generate them.  This lets observers that don't\n    // need absolute indices benefit from the other features of this API --\n    // relative order, transforms, and applyChanges -- without the speed hit.\n    const indices = !observeCallbacks._no_indices;\n\n    observeChangesCallbacks = {\n      addedBefore(id, fields, before) {\n        const check = suppressed || !(observeCallbacks.addedAt || observeCallbacks.added)\n        if (check) {\n          return;\n        }\n\n        const doc = transform(Object.assign(fields, {_id: id}));\n\n        if (observeCallbacks.addedAt) {\n          observeCallbacks.addedAt(\n              doc,\n              indices\n                  ? before\n                      ? this.docs.indexOf(before)\n                      : this.docs.size()\n                  : -1,\n              before\n          );\n        } else {\n          observeCallbacks.added(doc);\n        }\n      },\n      changed(id, fields) {\n\n        if (!(observeCallbacks.changedAt || observeCallbacks.changed)) {\n          return;\n        }\n\n        let doc = EJSON.clone(this.docs.get(id));\n        if (!doc) {\n          throw new Error(`Unknown id for changed: ${id}`);\n        }\n\n        const oldDoc = transform(EJSON.clone(doc));\n\n        DiffSequence.applyChanges(doc, fields);\n\n        if (observeCallbacks.changedAt) {\n          observeCallbacks.changedAt(\n              transform(doc),\n              oldDoc,\n              indices ? this.docs.indexOf(id) : -1\n          );\n        } else {\n          observeCallbacks.changed(transform(doc), oldDoc);\n        }\n      },\n      movedBefore(id, before) {\n        if (!observeCallbacks.movedTo) {\n          return;\n        }\n\n        const from = indices ? this.docs.indexOf(id) : -1;\n        let to = indices\n            ? before\n                ? this.docs.indexOf(before)\n                : this.docs.size()\n            : -1;\n\n        // When not moving backwards, adjust for the fact that removing the\n        // document slides everything back one slot.\n        if (to > from) {\n          --to;\n        }\n\n        observeCallbacks.movedTo(\n            transform(EJSON.clone(this.docs.get(id))),\n            from,\n            to,\n            before || null\n        );\n      },\n      removed(id) {\n        if (!(observeCallbacks.removedAt || observeCallbacks.removed)) {\n          return;\n        }\n\n        // technically maybe there should be an EJSON.clone here, but it's about\n        // to be removed from this.docs!\n        const doc = transform(this.docs.get(id));\n\n        if (observeCallbacks.removedAt) {\n          observeCallbacks.removedAt(doc, indices ? this.docs.indexOf(id) : -1);\n        } else {\n          observeCallbacks.removed(doc);\n        }\n      },\n    };\n  } else {\n    observeChangesCallbacks = {\n      added(id, fields) {\n        if (!suppressed && observeCallbacks.added) {\n          observeCallbacks.added(transform(Object.assign(fields, {_id: id})));\n        }\n      },\n      changed(id, fields) {\n        if (observeCallbacks.changed) {\n          const oldDoc = this.docs.get(id);\n          const doc = EJSON.clone(oldDoc);\n\n          DiffSequence.applyChanges(doc, fields);\n\n          observeCallbacks.changed(\n              transform(doc),\n              transform(EJSON.clone(oldDoc))\n          );\n        }\n      },\n      removed(id) {\n        if (observeCallbacks.removed) {\n          observeCallbacks.removed(transform(this.docs.get(id)));\n        }\n      },\n    };\n  }\n\n  const changeObserver = new LocalCollection._CachingChangeObserver({\n    callbacks: observeChangesCallbacks\n  });\n\n  // CachingChangeObserver clones all received input on its callbacks\n  // So we can mark it as safe to reduce the ejson clones.\n  // This is tested by the `mongo-livedata - (extended) scribbling` tests\n  changeObserver.applyChange._fromObserve = true;\n  const handle = cursor.observeChanges(changeObserver.applyChange,\n      { nonMutatingCallbacks: true });\n\n  // If needed, re-enable callbacks as soon as the initial batch is ready.\n  const setSuppressed = (h) => {\n    if (h.isReady) suppressed = false;\n    else h.isReadyPromise?.then(() => (suppressed = false));\n  };\n  // When we call cursor.observeChanges() it can be the on from\n  // the mongo package (instead of the minimongo one) and it doesn't have isReady and isReadyPromise\n  if (Meteor._isPromise(handle)) {\n    handle.then(setSuppressed);\n  } else {\n    setSuppressed(handle);\n  }\n  return handle;\n};\n\nLocalCollection._observeCallbacksAreOrdered = callbacks => {\n  if (callbacks.added && callbacks.addedAt) {\n    throw new Error('Please specify only one of added() and addedAt()');\n  }\n\n  if (callbacks.changed && callbacks.changedAt) {\n    throw new Error('Please specify only one of changed() and changedAt()');\n  }\n\n  if (callbacks.removed && callbacks.removedAt) {\n    throw new Error('Please specify only one of removed() and removedAt()');\n  }\n\n  return !!(\n    callbacks.addedAt ||\n    callbacks.changedAt ||\n    callbacks.movedTo ||\n    callbacks.removedAt\n  );\n};\n\nLocalCollection._observeChangesCallbacksAreOrdered = callbacks => {\n  if (callbacks.added && callbacks.addedBefore) {\n    throw new Error('Please specify only one of added() and addedBefore()');\n  }\n\n  return !!(callbacks.addedBefore || callbacks.movedBefore);\n};\n\nLocalCollection._removeFromResultsSync = (query, doc) => {\n  if (query.ordered) {\n    const i = LocalCollection._findInOrderedResults(query, doc);\n\n    query.removed(doc._id);\n    query.results.splice(i, 1);\n  } else {\n    const id = doc._id;  // in case callback mutates doc\n\n    query.removed(doc._id);\n    query.results.remove(id);\n  }\n};\n\nLocalCollection._removeFromResultsAsync = async (query, doc) => {\n  if (query.ordered) {\n    const i = LocalCollection._findInOrderedResults(query, doc);\n\n    await query.removed(doc._id);\n    query.results.splice(i, 1);\n  } else {\n    const id = doc._id;  // in case callback mutates doc\n\n    await query.removed(doc._id);\n    query.results.remove(id);\n  }\n};\n\n// Is this selector just shorthand for lookup by _id?\nLocalCollection._selectorIsId = selector =>\n  typeof selector === 'number' ||\n  typeof selector === 'string' ||\n  selector instanceof MongoID.ObjectID\n;\n\n// Is the selector just lookup by _id (shorthand or not)?\nLocalCollection._selectorIsIdPerhapsAsObject = selector =>\n  LocalCollection._selectorIsId(selector) ||\n  LocalCollection._selectorIsId(selector && selector._id) &&\n  Object.keys(selector).length === 1\n;\n\nLocalCollection._updateInResultsSync = (query, doc, old_doc) => {\n  if (!EJSON.equals(doc._id, old_doc._id)) {\n    throw new Error('Can\\'t change a doc\\'s _id while updating');\n  }\n\n  const projectionFn = query.projectionFn;\n  const changedFields = DiffSequence.makeChangedFields(\n    projectionFn(doc),\n    projectionFn(old_doc)\n  );\n\n  if (!query.ordered) {\n    if (Object.keys(changedFields).length) {\n      query.changed(doc._id, changedFields);\n      query.results.set(doc._id, doc);\n    }\n\n    return;\n  }\n\n  const old_idx = LocalCollection._findInOrderedResults(query, doc);\n\n  if (Object.keys(changedFields).length) {\n    query.changed(doc._id, changedFields);\n  }\n\n  if (!query.sorter) {\n    return;\n  }\n\n  // just take it out and put it back in again, and see if the index changes\n  query.results.splice(old_idx, 1);\n\n  const new_idx = LocalCollection._insertInSortedList(\n    query.sorter.getComparator({distances: query.distances}),\n    query.results,\n    doc\n  );\n\n  if (old_idx !== new_idx) {\n    let next = query.results[new_idx + 1];\n    if (next) {\n      next = next._id;\n    } else {\n      next = null;\n    }\n\n    query.movedBefore && query.movedBefore(doc._id, next);\n  }\n};\n\nLocalCollection._updateInResultsAsync = async (query, doc, old_doc) => {\n  if (!EJSON.equals(doc._id, old_doc._id)) {\n    throw new Error('Can\\'t change a doc\\'s _id while updating');\n  }\n\n  const projectionFn = query.projectionFn;\n  const changedFields = DiffSequence.makeChangedFields(\n    projectionFn(doc),\n    projectionFn(old_doc)\n  );\n\n  if (!query.ordered) {\n    if (Object.keys(changedFields).length) {\n      await query.changed(doc._id, changedFields);\n      query.results.set(doc._id, doc);\n    }\n\n    return;\n  }\n\n  const old_idx = LocalCollection._findInOrderedResults(query, doc);\n\n  if (Object.keys(changedFields).length) {\n    await query.changed(doc._id, changedFields);\n  }\n\n  if (!query.sorter) {\n    return;\n  }\n\n  // just take it out and put it back in again, and see if the index changes\n  query.results.splice(old_idx, 1);\n\n  const new_idx = LocalCollection._insertInSortedList(\n    query.sorter.getComparator({distances: query.distances}),\n    query.results,\n    doc\n  );\n\n  if (old_idx !== new_idx) {\n    let next = query.results[new_idx + 1];\n    if (next) {\n      next = next._id;\n    } else {\n      next = null;\n    }\n\n    query.movedBefore && await query.movedBefore(doc._id, next);\n  }\n};\n\nconst MODIFIERS = {\n  $currentDate(target, field, arg) {\n    if (typeof arg === 'object' && hasOwn.call(arg, '$type')) {\n      if (arg.$type !== 'date') {\n        throw MinimongoError(\n          'Minimongo does currently only support the date type in ' +\n          '$currentDate modifiers',\n          {field}\n        );\n      }\n    } else if (arg !== true) {\n      throw MinimongoError('Invalid $currentDate modifier', {field});\n    }\n\n    target[field] = new Date();\n  },\n  $inc(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $inc allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $inc modifier to non-number',\n          {field}\n        );\n      }\n\n      target[field] += arg;\n    } else {\n      target[field] = arg;\n    }\n  },\n  $min(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $min allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $min modifier to non-number',\n          {field}\n        );\n      }\n\n      if (target[field] > arg) {\n        target[field] = arg;\n      }\n    } else {\n      target[field] = arg;\n    }\n  },\n  $max(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $max allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $max modifier to non-number',\n          {field}\n        );\n      }\n\n      if (target[field] < arg) {\n        target[field] = arg;\n      }\n    } else {\n      target[field] = arg;\n    }\n  },\n  $mul(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $mul allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $mul modifier to non-number',\n          {field}\n        );\n      }\n\n      target[field] *= arg;\n    } else {\n      target[field] = 0;\n    }\n  },\n  $rename(target, field, arg, keypath, doc) {\n    // no idea why mongo has this restriction..\n    if (keypath === arg) {\n      throw MinimongoError('$rename source must differ from target', {field});\n    }\n\n    if (target === null) {\n      throw MinimongoError('$rename source field invalid', {field});\n    }\n\n    if (typeof arg !== 'string') {\n      throw MinimongoError('$rename target must be a string', {field});\n    }\n\n    if (arg.includes('\\0')) {\n      // Null bytes are not allowed in Mongo field names\n      // https://docs.mongodb.com/manual/reference/limits/#Restrictions-on-Field-Names\n      throw MinimongoError(\n        'The \\'to\\' field for $rename cannot contain an embedded null byte',\n        {field}\n      );\n    }\n\n    if (target === undefined) {\n      return;\n    }\n\n    const object = target[field];\n\n    delete target[field];\n\n    const keyparts = arg.split('.');\n    const target2 = findModTarget(doc, keyparts, {forbidArray: true});\n\n    if (target2 === null) {\n      throw MinimongoError('$rename target field invalid', {field});\n    }\n\n    target2[keyparts.pop()] = object;\n  },\n  $set(target, field, arg) {\n    if (target !== Object(target)) { // not an array or an object\n      const error = MinimongoError(\n        'Cannot set property on non-object field',\n        {field}\n      );\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    if (target === null) {\n      const error = MinimongoError('Cannot set property on null', {field});\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    assertHasValidFieldNames(arg);\n\n    target[field] = arg;\n  },\n  $setOnInsert(target, field, arg) {\n    // converted to `$set` in `_modify`\n  },\n  $unset(target, field, arg) {\n    if (target !== undefined) {\n      if (target instanceof Array) {\n        if (field in target) {\n          target[field] = null;\n        }\n      } else {\n        delete target[field];\n      }\n    }\n  },\n  $push(target, field, arg) {\n    if (target[field] === undefined) {\n      target[field] = [];\n    }\n\n    if (!(target[field] instanceof Array)) {\n      throw MinimongoError('Cannot apply $push modifier to non-array', {field});\n    }\n\n    if (!(arg && arg.$each)) {\n      // Simple mode: not $each\n      assertHasValidFieldNames(arg);\n\n      target[field].push(arg);\n\n      return;\n    }\n\n    // Fancy mode: $each (and maybe $slice and $sort and $position)\n    const toPush = arg.$each;\n    if (!(toPush instanceof Array)) {\n      throw MinimongoError('$each must be an array', {field});\n    }\n\n    assertHasValidFieldNames(toPush);\n\n    // Parse $position\n    let position = undefined;\n    if ('$position' in arg) {\n      if (typeof arg.$position !== 'number') {\n        throw MinimongoError('$position must be a numeric value', {field});\n      }\n\n      // XXX should check to make sure integer\n      if (arg.$position < 0) {\n        throw MinimongoError(\n          '$position in $push must be zero or positive',\n          {field}\n        );\n      }\n\n      position = arg.$position;\n    }\n\n    // Parse $slice.\n    let slice = undefined;\n    if ('$slice' in arg) {\n      if (typeof arg.$slice !== 'number') {\n        throw MinimongoError('$slice must be a numeric value', {field});\n      }\n\n      // XXX should check to make sure integer\n      slice = arg.$slice;\n    }\n\n    // Parse $sort.\n    let sortFunction = undefined;\n    if (arg.$sort) {\n      if (slice === undefined) {\n        throw MinimongoError('$sort requires $slice to be present', {field});\n      }\n\n      // XXX this allows us to use a $sort whose value is an array, but that's\n      // actually an extension of the Node driver, so it won't work\n      // server-side. Could be confusing!\n      // XXX is it correct that we don't do geo-stuff here?\n      sortFunction = new Minimongo.Sorter(arg.$sort).getComparator();\n\n      toPush.forEach(element => {\n        if (LocalCollection._f._type(element) !== 3) {\n          throw MinimongoError(\n            '$push like modifiers using $sort require all elements to be ' +\n            'objects',\n            {field}\n          );\n        }\n      });\n    }\n\n    // Actually push.\n    if (position === undefined) {\n      toPush.forEach(element => {\n        target[field].push(element);\n      });\n    } else {\n      const spliceArguments = [position, 0];\n\n      toPush.forEach(element => {\n        spliceArguments.push(element);\n      });\n\n      target[field].splice(...spliceArguments);\n    }\n\n    // Actually sort.\n    if (sortFunction) {\n      target[field].sort(sortFunction);\n    }\n\n    // Actually slice.\n    if (slice !== undefined) {\n      if (slice === 0) {\n        target[field] = []; // differs from Array.slice!\n      } else if (slice < 0) {\n        target[field] = target[field].slice(slice);\n      } else {\n        target[field] = target[field].slice(0, slice);\n      }\n    }\n  },\n  $pushAll(target, field, arg) {\n    if (!(typeof arg === 'object' && arg instanceof Array)) {\n      throw MinimongoError('Modifier $pushAll/pullAll allowed for arrays only');\n    }\n\n    assertHasValidFieldNames(arg);\n\n    const toPush = target[field];\n\n    if (toPush === undefined) {\n      target[field] = arg;\n    } else if (!(toPush instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pushAll modifier to non-array',\n        {field}\n      );\n    } else {\n      toPush.push(...arg);\n    }\n  },\n  $addToSet(target, field, arg) {\n    let isEach = false;\n\n    if (typeof arg === 'object') {\n      // check if first key is '$each'\n      const keys = Object.keys(arg);\n      if (keys[0] === '$each') {\n        isEach = true;\n      }\n    }\n\n    const values = isEach ? arg.$each : [arg];\n\n    assertHasValidFieldNames(values);\n\n    const toAdd = target[field];\n    if (toAdd === undefined) {\n      target[field] = values;\n    } else if (!(toAdd instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $addToSet modifier to non-array',\n        {field}\n      );\n    } else {\n      values.forEach(value => {\n        if (toAdd.some(element => LocalCollection._f._equal(value, element))) {\n          return;\n        }\n\n        toAdd.push(value);\n      });\n    }\n  },\n  $pop(target, field, arg) {\n    if (target === undefined) {\n      return;\n    }\n\n    const toPop = target[field];\n\n    if (toPop === undefined) {\n      return;\n    }\n\n    if (!(toPop instanceof Array)) {\n      throw MinimongoError('Cannot apply $pop modifier to non-array', {field});\n    }\n\n    if (typeof arg === 'number' && arg < 0) {\n      toPop.splice(0, 1);\n    } else {\n      toPop.pop();\n    }\n  },\n  $pull(target, field, arg) {\n    if (target === undefined) {\n      return;\n    }\n\n    const toPull = target[field];\n    if (toPull === undefined) {\n      return;\n    }\n\n    if (!(toPull instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pull/pullAll modifier to non-array',\n        {field}\n      );\n    }\n\n    let out;\n    if (arg != null && typeof arg === 'object' && !(arg instanceof Array)) {\n      // XXX would be much nicer to compile this once, rather than\n      // for each document we modify.. but usually we're not\n      // modifying that many documents, so we'll let it slide for\n      // now\n\n      // XXX Minimongo.Matcher isn't up for the job, because we need\n      // to permit stuff like {$pull: {a: {$gt: 4}}}.. something\n      // like {$gt: 4} is not normally a complete selector.\n      // same issue as $elemMatch possibly?\n      const matcher = new Minimongo.Matcher(arg);\n\n      out = toPull.filter(element => !matcher.documentMatches(element).result);\n    } else {\n      out = toPull.filter(element => !LocalCollection._f._equal(element, arg));\n    }\n\n    target[field] = out;\n  },\n  $pullAll(target, field, arg) {\n    if (!(typeof arg === 'object' && arg instanceof Array)) {\n      throw MinimongoError(\n        'Modifier $pushAll/pullAll allowed for arrays only',\n        {field}\n      );\n    }\n\n    if (target === undefined) {\n      return;\n    }\n\n    const toPull = target[field];\n\n    if (toPull === undefined) {\n      return;\n    }\n\n    if (!(toPull instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pull/pullAll modifier to non-array',\n        {field}\n      );\n    }\n\n    target[field] = toPull.filter(object =>\n      !arg.some(element => LocalCollection._f._equal(object, element))\n    );\n  },\n  $bit(target, field, arg) {\n    // XXX mongo only supports $bit on integers, and we only support\n    // native javascript numbers (doubles) so far, so we can't support $bit\n    throw MinimongoError('$bit is not supported', {field});\n  },\n  $v() {\n    // As discussed in https://github.com/meteor/meteor/issues/9623,\n    // the `$v` operator is not needed by Meteor, but problems can occur if\n    // it's not at least callable (as of Mongo >= 3.6). It's defined here as\n    // a no-op to work around these problems.\n  }\n};\n\nconst NO_CREATE_MODIFIERS = {\n  $pop: true,\n  $pull: true,\n  $pullAll: true,\n  $rename: true,\n  $unset: true\n};\n\n// Make sure field names do not contain Mongo restricted\n// characters ('.', '$', '\\0').\n// https://docs.mongodb.com/manual/reference/limits/#Restrictions-on-Field-Names\nconst invalidCharMsg = {\n  $: 'start with \\'$\\'',\n  '.': 'contain \\'.\\'',\n  '\\0': 'contain null bytes'\n};\n\n// checks if all field names in an object are valid\nfunction assertHasValidFieldNames(doc) {\n  if (doc && typeof doc === 'object') {\n    JSON.stringify(doc, (key, value) => {\n      assertIsValidFieldName(key);\n      return value;\n    });\n  }\n}\n\nfunction assertIsValidFieldName(key) {\n  let match;\n  if (typeof key === 'string' && (match = key.match(/^\\$|\\.|\\0/))) {\n    throw MinimongoError(`Key ${key} must not ${invalidCharMsg[match[0]]}`);\n  }\n}\n\n// for a.b.c.2.d.e, keyparts should be ['a', 'b', 'c', '2', 'd', 'e'],\n// and then you would operate on the 'e' property of the returned\n// object.\n//\n// if options.noCreate is falsey, creates intermediate levels of\n// structure as necessary, like mkdir -p (and raises an exception if\n// that would mean giving a non-numeric property to an array.) if\n// options.noCreate is true, return undefined instead.\n//\n// may modify the last element of keyparts to signal to the caller that it needs\n// to use a different value to index into the returned object (for example,\n// ['a', '01'] -> ['a', 1]).\n//\n// if forbidArray is true, return null if the keypath goes through an array.\n//\n// if options.arrayIndices is set, use its first element for the (first) '$' in\n// the path.\nfunction findModTarget(doc, keyparts, options = {}) {\n  let usedArrayIndex = false;\n\n  for (let i = 0; i < keyparts.length; i++) {\n    const last = i === keyparts.length - 1;\n    let keypart = keyparts[i];\n\n    if (!isIndexable(doc)) {\n      if (options.noCreate) {\n        return undefined;\n      }\n\n      const error = MinimongoError(\n        `cannot use the part '${keypart}' to traverse ${doc}`\n      );\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    if (doc instanceof Array) {\n      if (options.forbidArray) {\n        return null;\n      }\n\n      if (keypart === '$') {\n        if (usedArrayIndex) {\n          throw MinimongoError('Too many positional (i.e. \\'$\\') elements');\n        }\n\n        if (!options.arrayIndices || !options.arrayIndices.length) {\n          throw MinimongoError(\n            'The positional operator did not find the match needed from the ' +\n            'query'\n          );\n        }\n\n        keypart = options.arrayIndices[0];\n        usedArrayIndex = true;\n      } else if (isNumericKey(keypart)) {\n        keypart = parseInt(keypart);\n      } else {\n        if (options.noCreate) {\n          return undefined;\n        }\n\n        throw MinimongoError(\n          `can't append to array using string field name [${keypart}]`\n        );\n      }\n\n      if (last) {\n        keyparts[i] = keypart; // handle 'a.01'\n      }\n\n      if (options.noCreate && keypart >= doc.length) {\n        return undefined;\n      }\n\n      while (doc.length < keypart) {\n        doc.push(null);\n      }\n\n      if (!last) {\n        if (doc.length === keypart) {\n          doc.push({});\n        } else if (typeof doc[keypart] !== 'object') {\n          throw MinimongoError(\n            `can't modify field '${keyparts[i + 1]}' of list value ` +\n            JSON.stringify(doc[keypart])\n          );\n        }\n      }\n    } else {\n      assertIsValidFieldName(keypart);\n\n      if (!(keypart in doc)) {\n        if (options.noCreate) {\n          return undefined;\n        }\n\n        if (!last) {\n          doc[keypart] = {};\n        }\n      }\n    }\n\n    if (last) {\n      return doc;\n    }\n\n    doc = doc[keypart];\n  }\n\n  // notreached\n}\n", "import LocalCollection from './local_collection.js';\nimport {\n  compileDocumentSelector,\n  hasOwn,\n  nothingMatcher,\n} from './common.js';\n\nconst Decimal = Package['mongo-decimal']?.Decimal || class DecimalStub {}\n\n// The minimongo selector compiler!\n\n// Terminology:\n//  - a 'selector' is the EJSON object representing a selector\n//  - a 'matcher' is its compiled form (whether a full Minimongo.Matcher\n//    object or one of the component lambdas that matches parts of it)\n//  - a 'result object' is an object with a 'result' field and maybe\n//    distance and arrayIndices.\n//  - a 'branched value' is an object with a 'value' field and maybe\n//    'dontIterate' and 'arrayIndices'.\n//  - a 'document' is a top-level object that can be stored in a collection.\n//  - a 'lookup function' is a function that takes in a document and returns\n//    an array of 'branched values'.\n//  - a 'branched matcher' maps from an array of branched values to a result\n//    object.\n//  - an 'element matcher' maps from a single value to a bool.\n\n// Main entry point.\n//   var matcher = new Minimongo.Matcher({a: {$gt: 5}});\n//   if (matcher.documentMatches({a: 7})) ...\nexport default class Matcher {\n  constructor(selector, isUpdate) {\n    // A set (object mapping string -> *) of all of the document paths looked\n    // at by the selector. Also includes the empty string if it may look at any\n    // path (eg, $where).\n    this._paths = {};\n    // Set to true if compilation finds a $near.\n    this._hasGeoQuery = false;\n    // Set to true if compilation finds a $where.\n    this._hasWhere = false;\n    // Set to false if compilation finds anything other than a simple equality\n    // or one or more of '$gt', '$gte', '$lt', '$lte', '$ne', '$in', '$nin' used\n    // with scalars as operands.\n    this._isSimple = true;\n    // Set to a dummy document which always matches this Matcher. Or set to null\n    // if such document is too hard to find.\n    this._matchingDocument = undefined;\n    // A clone of the original selector. It may just be a function if the user\n    // passed in a function; otherwise is definitely an object (eg, IDs are\n    // translated into {_id: ID} first. Used by canBecomeTrueByModifier and\n    // Sorter._useWithMatcher.\n    this._selector = null;\n    this._docMatcher = this._compileSelector(selector);\n    // Set to true if selection is done for an update operation\n    // Default is false\n    // Used for $near array update (issue #3599)\n    this._isUpdate = isUpdate;\n  }\n\n  documentMatches(doc) {\n    if (doc !== Object(doc)) {\n      throw Error('documentMatches needs a document');\n    }\n\n    return this._docMatcher(doc);\n  }\n\n  hasGeoQuery() {\n    return this._hasGeoQuery;\n  }\n\n  hasWhere() {\n    return this._hasWhere;\n  }\n\n  isSimple() {\n    return this._isSimple;\n  }\n\n  // Given a selector, return a function that takes one argument, a\n  // document. It returns a result object.\n  _compileSelector(selector) {\n    // you can pass a literal function instead of a selector\n    if (selector instanceof Function) {\n      this._isSimple = false;\n      this._selector = selector;\n      this._recordPathUsed('');\n\n      return doc => ({result: !!selector.call(doc)});\n    }\n\n    // shorthand -- scalar _id\n    if (LocalCollection._selectorIsId(selector)) {\n      this._selector = {_id: selector};\n      this._recordPathUsed('_id');\n\n      return doc => ({result: EJSON.equals(doc._id, selector)});\n    }\n\n    // protect against dangerous selectors.  falsey and {_id: falsey} are both\n    // likely programmer error, and not what you want, particularly for\n    // destructive operations.\n    if (!selector || hasOwn.call(selector, '_id') && !selector._id) {\n      this._isSimple = false;\n      return nothingMatcher;\n    }\n\n    // Top level can't be an array or true or binary.\n    if (Array.isArray(selector) ||\n        EJSON.isBinary(selector) ||\n        typeof selector === 'boolean') {\n      throw new Error(`Invalid selector: ${selector}`);\n    }\n\n    this._selector = EJSON.clone(selector);\n\n    return compileDocumentSelector(selector, this, {isRoot: true});\n  }\n\n  // Returns a list of key paths the given selector is looking for. It includes\n  // the empty string if there is a $where.\n  _getPaths() {\n    return Object.keys(this._paths);\n  }\n\n  _recordPathUsed(path) {\n    this._paths[path] = true;\n  }\n}\n\n// helpers used by compiled selector code\nLocalCollection._f = {\n  // XXX for _all and _in, consider building 'inquery' at compile time..\n  _type(v) {\n    if (typeof v === 'number') {\n      return 1;\n    }\n\n    if (typeof v === 'string') {\n      return 2;\n    }\n\n    if (typeof v === 'boolean') {\n      return 8;\n    }\n\n    if (Array.isArray(v)) {\n      return 4;\n    }\n\n    if (v === null) {\n      return 10;\n    }\n\n    // note that typeof(/x/) === \"object\"\n    if (v instanceof RegExp) {\n      return 11;\n    }\n\n    if (typeof v === 'function') {\n      return 13;\n    }\n\n    if (v instanceof Date) {\n      return 9;\n    }\n\n    if (EJSON.isBinary(v)) {\n      return 5;\n    }\n\n    if (v instanceof MongoID.ObjectID) {\n      return 7;\n    }\n\n    if (v instanceof Decimal) {\n      return 1;\n    }\n\n    // object\n    return 3;\n\n    // XXX support some/all of these:\n    // 14, symbol\n    // 15, javascript code with scope\n    // 16, 18: 32-bit/64-bit integer\n    // 17, timestamp\n    // 255, minkey\n    // 127, maxkey\n  },\n\n  // deep equality test: use for literal document and array matches\n  _equal(a, b) {\n    return EJSON.equals(a, b, {keyOrderSensitive: true});\n  },\n\n  // maps a type code to a value that can be used to sort values of different\n  // types\n  _typeorder(t) {\n    // http://www.mongodb.org/display/DOCS/What+is+the+Compare+Order+for+BSON+Types\n    // XXX what is the correct sort position for Javascript code?\n    // ('100' in the matrix below)\n    // XXX minkey/maxkey\n    return [\n      -1,  // (not a type)\n      1,   // number\n      2,   // string\n      3,   // object\n      4,   // array\n      5,   // binary\n      -1,  // deprecated\n      6,   // ObjectID\n      7,   // bool\n      8,   // Date\n      0,   // null\n      9,   // RegExp\n      -1,  // deprecated\n      100, // JS code\n      2,   // deprecated (symbol)\n      100, // JS code\n      1,   // 32-bit int\n      8,   // Mongo timestamp\n      1    // 64-bit int\n    ][t];\n  },\n\n  // compare two values of unknown type according to BSON ordering\n  // semantics. (as an extension, consider 'undefined' to be less than\n  // any other value.) return negative if a is less, positive if b is\n  // less, or 0 if equal\n  _cmp(a, b) {\n    if (a === undefined) {\n      return b === undefined ? 0 : -1;\n    }\n\n    if (b === undefined) {\n      return 1;\n    }\n\n    let ta = LocalCollection._f._type(a);\n    let tb = LocalCollection._f._type(b);\n\n    const oa = LocalCollection._f._typeorder(ta);\n    const ob = LocalCollection._f._typeorder(tb);\n\n    if (oa !== ob) {\n      return oa < ob ? -1 : 1;\n    }\n\n    // XXX need to implement this if we implement Symbol or integers, or\n    // Timestamp\n    if (ta !== tb) {\n      throw Error('Missing type coercion logic in _cmp');\n    }\n\n    if (ta === 7) { // ObjectID\n      // Convert to string.\n      ta = tb = 2;\n      a = a.toHexString();\n      b = b.toHexString();\n    }\n\n    if (ta === 9) { // Date\n      // Convert to millis.\n      ta = tb = 1;\n      a = isNaN(a) ? 0 : a.getTime();\n      b = isNaN(b) ? 0 : b.getTime();\n    }\n\n    if (ta === 1) { // double\n      if (a instanceof Decimal) {\n        return a.minus(b).toNumber();\n      } else {\n        return a - b;\n      }\n    }\n\n    if (tb === 2) // string\n      return a < b ? -1 : a === b ? 0 : 1;\n\n    if (ta === 3) { // Object\n      // this could be much more efficient in the expected case ...\n      const toArray = object => {\n        const result = [];\n\n        Object.keys(object).forEach(key => {\n          result.push(key, object[key]);\n        });\n\n        return result;\n      };\n\n      return LocalCollection._f._cmp(toArray(a), toArray(b));\n    }\n\n    if (ta === 4) { // Array\n      for (let i = 0; ; i++) {\n        if (i === a.length) {\n          return i === b.length ? 0 : -1;\n        }\n\n        if (i === b.length) {\n          return 1;\n        }\n\n        const s = LocalCollection._f._cmp(a[i], b[i]);\n        if (s !== 0) {\n          return s;\n        }\n      }\n    }\n\n    if (ta === 5) { // binary\n      // Surprisingly, a small binary blob is always less than a large one in\n      // Mongo.\n      if (a.length !== b.length) {\n        return a.length - b.length;\n      }\n\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] < b[i]) {\n          return -1;\n        }\n\n        if (a[i] > b[i]) {\n          return 1;\n        }\n      }\n\n      return 0;\n    }\n\n    if (ta === 8) { // boolean\n      if (a) {\n        return b ? 0 : 1;\n      }\n\n      return b ? -1 : 0;\n    }\n\n    if (ta === 10) // null\n      return 0;\n\n    if (ta === 11) // regexp\n      throw Error('Sorting not supported on regular expression'); // XXX\n\n    // 13: javascript code\n    // 14: symbol\n    // 15: javascript code with scope\n    // 16: 32-bit integer\n    // 17: timestamp\n    // 18: 64-bit integer\n    // 255: minkey\n    // 127: maxkey\n    if (ta === 13) // javascript code\n      throw Error('Sorting not supported on Javascript code'); // XXX\n\n    throw Error('Unknown type to sort');\n  },\n};\n", "import LocalCollection_ from './local_collection.js';\nimport Matcher from './matcher.js';\nimport Sorter from './sorter.js';\n\nLocalCollection = LocalCollection_;\nMinimongo = {\n    LocalCollection: LocalCollection_,\n    Matcher,\n    Sorter\n};\n", "// ObserveHandle: the return value of a live query.\nexport default class ObserveHandle {}\n", "import {\n  ELEMENT_OPERATORS,\n  equalityElementMatcher,\n  expandArraysInBranches,\n  hasOwn,\n  isOperatorObject,\n  makeLookupFunction,\n  regexpElementMatcher,\n} from './common.js';\n\n// Give a sort spec, which can be in any of these forms:\n//   {\"key1\": 1, \"key2\": -1}\n//   [[\"key1\", \"asc\"], [\"key2\", \"desc\"]]\n//   [\"key1\", [\"key2\", \"desc\"]]\n//\n// (.. with the first form being dependent on the key enumeration\n// behavior of your javascript VM, which usually does what you mean in\n// this case if the key names don't look like integers ..)\n//\n// return a function that takes two objects, and returns -1 if the\n// first object comes first in order, 1 if the second object comes\n// first, or 0 if neither object comes before the other.\n\nexport default class Sorter {\n  constructor(spec) {\n    this._sortSpecParts = [];\n    this._sortFunction = null;\n\n    const addSpecPart = (path, ascending) => {\n      if (!path) {\n        throw Error('sort keys must be non-empty');\n      }\n\n      if (path.charAt(0) === '$') {\n        throw Error(`unsupported sort key: ${path}`);\n      }\n\n      this._sortSpecParts.push({\n        ascending,\n        lookup: makeLookupFunction(path, {forSort: true}),\n        path\n      });\n    };\n\n    if (spec instanceof Array) {\n      spec.forEach(element => {\n        if (typeof element === 'string') {\n          addSpecPart(element, true);\n        } else {\n          addSpecPart(element[0], element[1] !== 'desc');\n        }\n      });\n    } else if (typeof spec === 'object') {\n      Object.keys(spec).forEach(key => {\n        addSpecPart(key, spec[key] >= 0);\n      });\n    } else if (typeof spec === 'function') {\n      this._sortFunction = spec;\n    } else {\n      throw Error(`Bad sort specification: ${JSON.stringify(spec)}`);\n    }\n\n    // If a function is specified for sorting, we skip the rest.\n    if (this._sortFunction) {\n      return;\n    }\n\n    // To implement affectedByModifier, we piggy-back on top of Matcher's\n    // affectedByModifier code; we create a selector that is affected by the\n    // same modifiers as this sort order. This is only implemented on the\n    // server.\n    if (this.affectedByModifier) {\n      const selector = {};\n\n      this._sortSpecParts.forEach(spec => {\n        selector[spec.path] = 1;\n      });\n\n      this._selectorForAffectedByModifier = new Minimongo.Matcher(selector);\n    }\n\n    this._keyComparator = composeComparators(\n      this._sortSpecParts.map((spec, i) => this._keyFieldComparator(i))\n    );\n  }\n\n  getComparator(options) {\n    // If sort is specified or have no distances, just use the comparator from\n    // the source specification (which defaults to \"everything is equal\".\n    // issue #3599\n    // https://docs.mongodb.com/manual/reference/operator/query/near/#sort-operation\n    // sort effectively overrides $near\n    if (this._sortSpecParts.length || !options || !options.distances) {\n      return this._getBaseComparator();\n    }\n\n    const distances = options.distances;\n\n    // Return a comparator which compares using $near distances.\n    return (a, b) => {\n      if (!distances.has(a._id)) {\n        throw Error(`Missing distance for ${a._id}`);\n      }\n\n      if (!distances.has(b._id)) {\n        throw Error(`Missing distance for ${b._id}`);\n      }\n\n      return distances.get(a._id) - distances.get(b._id);\n    };\n  }\n\n  // Takes in two keys: arrays whose lengths match the number of spec\n  // parts. Returns negative, 0, or positive based on using the sort spec to\n  // compare fields.\n  _compareKeys(key1, key2) {\n    if (key1.length !== this._sortSpecParts.length ||\n        key2.length !== this._sortSpecParts.length) {\n      throw Error('Key has wrong length');\n    }\n\n    return this._keyComparator(key1, key2);\n  }\n\n  // Iterates over each possible \"key\" from doc (ie, over each branch), calling\n  // 'cb' with the key.\n  _generateKeysFromDoc(doc, cb) {\n    if (this._sortSpecParts.length === 0) {\n      throw new Error('can\\'t generate keys without a spec');\n    }\n\n    const pathFromIndices = indices => `${indices.join(',')},`;\n\n    let knownPaths = null;\n\n    // maps index -> ({'' -> value} or {path -> value})\n    const valuesByIndexAndPath = this._sortSpecParts.map(spec => {\n      // Expand any leaf arrays that we find, and ignore those arrays\n      // themselves.  (We never sort based on an array itself.)\n      let branches = expandArraysInBranches(spec.lookup(doc), true);\n\n      // If there are no values for a key (eg, key goes to an empty array),\n      // pretend we found one undefined value.\n      if (!branches.length) {\n        branches = [{ value: void 0 }];\n      }\n\n      const element = Object.create(null);\n      let usedPaths = false;\n\n      branches.forEach(branch => {\n        if (!branch.arrayIndices) {\n          // If there are no array indices for a branch, then it must be the\n          // only branch, because the only thing that produces multiple branches\n          // is the use of arrays.\n          if (branches.length > 1) {\n            throw Error('multiple branches but no array used?');\n          }\n\n          element[''] = branch.value;\n          return;\n        }\n\n        usedPaths = true;\n\n        const path = pathFromIndices(branch.arrayIndices);\n\n        if (hasOwn.call(element, path)) {\n          throw Error(`duplicate path: ${path}`);\n        }\n\n        element[path] = branch.value;\n\n        // If two sort fields both go into arrays, they have to go into the\n        // exact same arrays and we have to find the same paths.  This is\n        // roughly the same condition that makes MongoDB throw this strange\n        // error message.  eg, the main thing is that if sort spec is {a: 1,\n        // b:1} then a and b cannot both be arrays.\n        //\n        // (In MongoDB it seems to be OK to have {a: 1, 'a.x.y': 1} where 'a'\n        // and 'a.x.y' are both arrays, but we don't allow this for now.\n        // #NestedArraySort\n        // XXX achieve full compatibility here\n        if (knownPaths && !hasOwn.call(knownPaths, path)) {\n          throw Error('cannot index parallel arrays');\n        }\n      });\n\n      if (knownPaths) {\n        // Similarly to above, paths must match everywhere, unless this is a\n        // non-array field.\n        if (!hasOwn.call(element, '') &&\n            Object.keys(knownPaths).length !== Object.keys(element).length) {\n          throw Error('cannot index parallel arrays!');\n        }\n      } else if (usedPaths) {\n        knownPaths = {};\n\n        Object.keys(element).forEach(path => {\n          knownPaths[path] = true;\n        });\n      }\n\n      return element;\n    });\n\n    if (!knownPaths) {\n      // Easy case: no use of arrays.\n      const soleKey = valuesByIndexAndPath.map(values => {\n        if (!hasOwn.call(values, '')) {\n          throw Error('no value in sole key case?');\n        }\n\n        return values[''];\n      });\n\n      cb(soleKey);\n\n      return;\n    }\n\n    Object.keys(knownPaths).forEach(path => {\n      const key = valuesByIndexAndPath.map(values => {\n        if (hasOwn.call(values, '')) {\n          return values[''];\n        }\n\n        if (!hasOwn.call(values, path)) {\n          throw Error('missing path?');\n        }\n\n        return values[path];\n      });\n\n      cb(key);\n    });\n  }\n\n  // Returns a comparator that represents the sort specification (but not\n  // including a possible geoquery distance tie-breaker).\n  _getBaseComparator() {\n    if (this._sortFunction) {\n      return this._sortFunction;\n    }\n\n    // If we're only sorting on geoquery distance and no specs, just say\n    // everything is equal.\n    if (!this._sortSpecParts.length) {\n      return (doc1, doc2) => 0;\n    }\n\n    return (doc1, doc2) => {\n      const key1 = this._getMinKeyFromDoc(doc1);\n      const key2 = this._getMinKeyFromDoc(doc2);\n      return this._compareKeys(key1, key2);\n    };\n  }\n\n  // Finds the minimum key from the doc, according to the sort specs.  (We say\n  // \"minimum\" here but this is with respect to the sort spec, so \"descending\"\n  // sort fields mean we're finding the max for that field.)\n  //\n  // Note that this is NOT \"find the minimum value of the first field, the\n  // minimum value of the second field, etc\"... it's \"choose the\n  // lexicographically minimum value of the key vector, allowing only keys which\n  // you can find along the same paths\".  ie, for a doc {a: [{x: 0, y: 5}, {x:\n  // 1, y: 3}]} with sort spec {'a.x': 1, 'a.y': 1}, the only keys are [0,5] and\n  // [1,3], and the minimum key is [0,5]; notably, [0,3] is NOT a key.\n  _getMinKeyFromDoc(doc) {\n    let minKey = null;\n\n    this._generateKeysFromDoc(doc, key => {\n      if (minKey === null) {\n        minKey = key;\n        return;\n      }\n\n      if (this._compareKeys(key, minKey) < 0) {\n        minKey = key;\n      }\n    });\n\n    return minKey;\n  }\n\n  _getPaths() {\n    return this._sortSpecParts.map(part => part.path);\n  }\n\n  // Given an index 'i', returns a comparator that compares two key arrays based\n  // on field 'i'.\n  _keyFieldComparator(i) {\n    const invert = !this._sortSpecParts[i].ascending;\n\n    return (key1, key2) => {\n      const compare = LocalCollection._f._cmp(key1[i], key2[i]);\n      return invert ? -compare : compare;\n    };\n  }\n}\n\n// Given an array of comparators\n// (functions (a,b)->(negative or positive or zero)), returns a single\n// comparator which uses each comparator in order and returns the first\n// non-zero value.\nfunction composeComparators(comparatorArray) {\n  return (a, b) => {\n    for (let i = 0; i < comparatorArray.length; ++i) {\n      const compare = comparatorArray[i](a, b);\n      if (compare !== 0) {\n        return compare;\n      }\n    }\n\n    return 0;\n  };\n}\n"]}