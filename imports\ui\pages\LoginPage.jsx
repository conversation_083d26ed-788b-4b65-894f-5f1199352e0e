import React, { useState, useEffect } from 'react';
import { Meteor } from 'meteor/meteor';
import { useNavigate, Link } from 'react-router-dom';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { useTracker } from 'meteor/react-meteor-data';

const LoginSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
});

export const LoginPage = () => {
  const navigate = useNavigate();
  const [error, setError] = useState('');
  const [resendEmailSent, setResendEmailSent] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { user } = useTracker(() => {
    const subscription = Meteor.subscribe('userData');
    return {
      user: Meteor.user(),
      isLoading: !subscription.ready()
    };
  }, []);

  useEffect(() => {
    if (user) {
      const role = user.profile?.role || user.roles?.[0];
      navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');
    }
  }, [user, navigate]);

  const handleResendVerification = (email) => {
    Meteor.call('users.resendVerificationEmail', email, (err) => {
      if (err) {
        setError(err.reason || 'Failed to resend verification email');
      } else {
        setResendEmailSent(true);
      }
    });
  };

  const handleSubmit = ({ email, password }, { setSubmitting }) => {
    setError(''); // Clear any previous errors
    setResendEmailSent(false);
    
    Meteor.loginWithPassword(email, password, (err) => {
      if (err) {
        console.error('Login error details:', {
          error: err.error,
          reason: err.reason,
          message: err.message,
          details: err.details,
          errorType: err.errorType,
          stack: err.stack,
          fullError: JSON.stringify(err, null, 2)
        });
        if (err.error === 'email-not-verified') {
          setError(
            <div>
              Email not verified. 
              <button
                onClick={() => handleResendVerification(email)}
                className="auth-link"
                style={{ 
                  background: 'none',
                  border: 'none',
                  padding: '0 4px',
                  cursor: 'pointer'
                }}
              >
                Resend verification email
              </button>
              {resendEmailSent && (
                <div style={{ 
                  marginTop: '8px',
                  color: 'var(--success)',
                  fontSize: '0.875rem'
                }}>
                  Verification email sent!
                </div>
              )}
            </div>
          );
        } else {
          setError(err.reason || 'Login failed. Please check your credentials.');
        }
        setSubmitting(false);
      }
    });
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <div className="auth-logo">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        <h1 className="auth-title">Welcome Back</h1>
          <p className="auth-subtitle">
            Sign in to access your workspace and manage your tasks efficiently
        </p>
        </div>
        
        <Formik
          initialValues={{ email: '', password: '' }}
          validationSchema={LoginSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="auth-form">
              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  <svg className="form-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Email Address
                </label>
                <Field
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}
                  disabled={isSubmitting}
                />
                {errors.email && touched.email && (
                  <div className="error-message">
                    <svg className="error-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    {errors.email}
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  <svg className="form-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Password
                </label>
                <div className="form-control-password-wrapper">
                <Field
                  id="password"
                  name="password"
                    type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                    className={`form-control ${errors.password && touched.password ? 'form-control-error' : ''}`}
                  disabled={isSubmitting}
                />
                  <button
                    type="button"
                    className="password-toggle-btn"
                    tabIndex={-1}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                    onClick={() => setShowPassword((v) => !v)}
                  >
                    {showPassword ? (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    ) : (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                  </button>
                </div>
                {errors.password && touched.password && (
                  <div className="error-message">
                    <svg className="error-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    {errors.password}
                  </div>
                )}
              </div>

              {error && (
                <div className="error-message error-message-global">
                  <svg className="error-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  {error}
                </div>
              )}

              <button 
                type="submit" 
                className="btn btn-primary btn-auth"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading-spinner"></span>
                    Signing in...
                  </>
                ) : (
                  <>
                    <svg className="btn-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Sign In
                  </>
                )}
              </button>

              <div className="auth-links-row">
                <Link to="/forgot-password" className="auth-link">Forgot password?</Link>
              </div>
            </Form>
          )}
        </Formik>

        <div className="auth-footer">
          <p className="auth-footer-text">
            Don't have an account? 
            <Link to="/signup" className="auth-link">
              Create an account
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}; 