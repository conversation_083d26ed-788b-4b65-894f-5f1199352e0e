{"version": 3, "sources": ["meteor://💻app/packages/allow-deny/allow-deny.js"], "names": ["_regeneratorRuntime", "module", "link", "default", "v", "_objectSpread", "_createForOfIteratorHelperLoose", "hasOwn", "Object", "prototype", "hasOwnProperty", "AllowDeny", "CollectionPrototype", "allow", "options", "addValidator", "deny", "_defineMutationMethods", "self", "_restricted", "_insecure", "undefined", "_validators", "insert", "update", "remove", "insertAsync", "updateAsync", "removeAsync", "upsertAsync", "fetch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_name", "_prefix", "_connection", "Meteor", "server", "isClient", "m", "for<PERSON>ach", "method", "methodName", "useExisting", "handlerPropName", "isInsert", "name", "includes", "check", "arguments", "Match", "Any", "args", "Array", "from", "generatedId", "call", "_makeNewID", "isSimulation", "_id", "_collection", "apply", "throwIfSelectorIsNotId", "syncMethodName", "replace", "syncValidatedMethodName", "char<PERSON>t", "toUpperCase", "slice", "validatedMethodName", "length", "Error", "unshift", "userId", "push", "_isInsecure", "syncMethodsMapper", "e", "toString", "methods", "_updateFetch", "fields", "union", "create", "add", "names", "keys", "Package", "insecure", "asyncSome", "array", "predicate", "_iterator", "_step", "item", "async", "asyncSome$", "_context", "prev", "next", "done", "value", "awrap", "sent", "abrupt", "stop", "Promise", "asyncEvery", "_iterator2", "_step2", "asyncEvery$", "_context2", "_validatedInsertAsync", "_callee3", "doc", "_callee3$", "_context5", "_callee", "validator", "result", "_callee$", "_context3", "docToValidate", "_isPromise", "t0", "_callee2", "_callee2$", "_context4", "_validatedUpdateAsync", "_callee6", "selector", "mutator", "noReplaceError", "mutator<PERSON>eys", "modifiedFields", "findOptions", "_callee6$", "_context8", "assign", "LocalCollection", "_selectorIsIdPerhapsAsObject", "upsert", "op", "params", "ALLOWED_UPDATE_OPERATIONS", "field", "indexOf", "substring", "transform", "fieldName", "findOneAsync", "_callee4", "factoriedDoc", "_callee4$", "_context6", "transformDoc", "_callee5", "_callee5$", "_context7", "_forbid<PERSON><PERSON>lace", "$inc", "$set", "$unset", "$addToSet", "$pop", "$pullAll", "$pull", "$pushAll", "$push", "$bit", "_validatedRemoveAsync", "_callee9", "_callee9$", "_context11", "_callee7", "_callee7$", "_context9", "_callee8", "_callee8$", "_context10", "_callMutatorMethodAsync", "firstArgIsSelector", "alreadyInSimulation", "mutatorMethodName", "applyAsync", "returnStubValue", "resolverType", "returnServerResultPromise", "_stream", "_isStub", "_callMutatorMethod", "callback", "err", "_debug", "ret", "EJSON", "clone", "collection", "allowOrDeny", "validKeysRegEx", "key", "test", "isAsyncKey", "s<PERSON><PERSON><PERSON>", "deprecate", "Function", "_transform", "wrapTransform", "isAsyncName", "validatorSyncName", "CurrentInvocation", "DDP", "_CurrentMethodInvocation", "_CurrentInvocation", "enclosing", "get"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,mBAAmB;AAACC,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,mBAAmB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,aAAa;AAACJ,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,aAAa,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,+BAA+B;AAACL,MAAM,CAACC,IAAI,CAAC,uDAAuD,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACE,+BAA+B,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAnY;AACA;AACA;;AAEA,IAAMG,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAC,SAAS,GAAG;EACVC,mBAAmB,EAAE,CAAC;AACxB,CAAC;;AAED;AACA;AACA,IAAMA,mBAAmB,GAAGD,SAAS,CAACC,mBAAmB;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,mBAAmB,CAACC,KAAK,GAAG,UAASC,OAAO,EAAE;EAC5CC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAED,OAAO,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,mBAAmB,CAACI,IAAI,GAAG,UAASF,OAAO,EAAE;EAC3CC,YAAY,CAAC,IAAI,EAAE,MAAM,EAAED,OAAO,CAAC;AACrC,CAAC;AAEDF,mBAAmB,CAACK,sBAAsB,GAAG,UAASH,OAAO,EAAE;EAC7D,IAAMI,IAAI,GAAG,IAAI;EACjBJ,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;EAEvB;EACA;EACAI,IAAI,CAACC,WAAW,GAAG,KAAK;;EAExB;EACA;EACA;EACA;EACAD,IAAI,CAACE,SAAS,GAAGC,SAAS;EAE1BH,IAAI,CAACI,WAAW,GAAG;IACjBC,MAAM,EAAE;MAACV,KAAK,EAAE,EAAE;MAAEG,IAAI,EAAE;IAAE,CAAC;IAC7BQ,MAAM,EAAE;MAACX,KAAK,EAAE,EAAE;MAAEG,IAAI,EAAE;IAAE,CAAC;IAC7BS,MAAM,EAAE;MAACZ,KAAK,EAAE,EAAE;MAAEG,IAAI,EAAE;IAAE,CAAC;IAC7BU,WAAW,EAAE;MAACb,KAAK,EAAE,EAAE;MAAEG,IAAI,EAAE;IAAE,CAAC;IAClCW,WAAW,EAAE;MAACd,KAAK,EAAE,EAAE;MAAEG,IAAI,EAAE;IAAE,CAAC;IAClCY,WAAW,EAAE;MAACf,KAAK,EAAE,EAAE;MAAEG,IAAI,EAAE;IAAE,CAAC;IAClCa,WAAW,EAAE;MAAChB,KAAK,EAAE,EAAE;MAAEG,IAAI,EAAE;IAAE,CAAC;IAAE;IACpCc,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE;EAClB,CAAC;EAED,IAAI,CAACb,IAAI,CAACc,KAAK,EACb,OAAO,CAAC;;EAEV;EACA;EACAd,IAAI,CAACe,OAAO,GAAG,GAAG,GAAGf,IAAI,CAACc,KAAK,GAAG,GAAG;;EAErC;EACA;EACA;EACA;EACA;EACA,IAAId,IAAI,CAACgB,WAAW,KAAKhB,IAAI,CAACgB,WAAW,KAAKC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACE,QAAQ,CAAC,EAAE;IAC/E,IAAMC,CAAC,GAAG,CAAC,CAAC;IAEZ,CACE,aAAa,EACb,aAAa,EACb,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT,CAACC,OAAO,CAAC,UAAAC,MAAM,EAAI;MAClB,IAAMC,UAAU,GAAGvB,IAAI,CAACe,OAAO,GAAGO,MAAM;MAExC,IAAI1B,OAAO,CAAC4B,WAAW,EAAE;QACvB,IAAMC,eAAe,GAAGR,MAAM,CAACE,QAAQ,GACnC,iBAAiB,GACjB,iBAAiB;QACrB;QACA;QACA,IACEnB,IAAI,CAACgB,WAAW,CAACS,eAAe,CAAC,IACjC,OAAOzB,IAAI,CAACgB,WAAW,CAACS,eAAe,CAAC,CAACF,UAAU,CAAC,KAAK,UAAU,EAEnE;MACJ;MAEA,IAAMG,QAAQ,GAAG,SAAAA,CAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC;MAAA;MAEhDR,CAAC,CAACG,UAAU,CAAC,GAAG,SAAU;MAAA,GAAW;QACnC;QACAM,KAAK,CAACC,SAAS,EAAE,CAACC,KAAK,CAACC,GAAG,CAAC,CAAC;QAC7B,IAAMC,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;QAClC,IAAI;UACF;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIM,WAAW,GAAG,IAAI;UACtB,IAAIV,QAAQ,CAACJ,MAAM,CAAC,IAAI,CAACjC,MAAM,CAACgD,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;YACpDG,WAAW,GAAGpC,IAAI,CAACsC,UAAU,CAAC,CAAC;UACjC;UAEA,IAAI,IAAI,CAACC,YAAY,EAAE;YACrB;YACA;YACA,IAAIH,WAAW,KAAK,IAAI,EAAE;cACxBH,IAAI,CAAC,CAAC,CAAC,CAACO,GAAG,GAAGJ,WAAW;YAC3B;YACA,OAAOpC,IAAI,CAACyC,WAAW,CAACnB,MAAM,CAAC,CAACoB,KAAK,CAAC1C,IAAI,CAACyC,WAAW,EAAER,IAAI,CAAC;UAC/D;;UAEA;;UAEA;UACA;UACA,IAAI,CAACP,QAAQ,CAACJ,MAAM,CAAC,EAAEqB,sBAAsB,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEX,MAAM,CAAC;UAE9D,IAAMsB,cAAc,GAAGtB,MAAM,CAACuB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;UAClD,IAAMC,uBAAuB,GAAG,YAAY,GAAGxB,MAAM,CAACyB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,cAAc,CAACK,KAAK,CAAC,CAAC,CAAC;UACvG;UACA,IAAMC,mBAAmB,GAAGJ,uBAAuB,GAAG,OAAO;UAE7D,IAAI9C,IAAI,CAACC,WAAW,EAAE;YACpB;YACA,IAAID,IAAI,CAACI,WAAW,CAACwC,cAAc,CAAC,CAACjD,KAAK,CAACwD,MAAM,KAAK,CAAC,EAAE;cACvD,MAAM,IAAIlC,MAAM,CAACmC,KAAK,CACpB,GAAG,EACH,uDAAuD,GACrD,yBAAyB,GACzB9B,MAAM,GACN,IACJ,CAAC;YACH;YAEAW,IAAI,CAACoB,OAAO,CAAC,IAAI,CAACC,MAAM,CAAC;YACzB5B,QAAQ,CAACJ,MAAM,CAAC,IAAIW,IAAI,CAACsB,IAAI,CAACnB,WAAW,CAAC;YAC1C,OAAOpC,IAAI,CAACkD,mBAAmB,CAAC,CAACR,KAAK,CAAC1C,IAAI,EAAEiC,IAAI,CAAC;UACpD,CAAC,MAAM,IAAIjC,IAAI,CAACwD,WAAW,CAAC,CAAC,EAAE;YAC7B,IAAIpB,WAAW,KAAK,IAAI,EAAEH,IAAI,CAAC,CAAC,CAAC,CAACO,GAAG,GAAGJ,WAAW;YACnD;YACA;YACA;YACA,IAAMqB,iBAAiB,GAAG;cACxBpD,MAAM,EAAE,aAAa;cACrBC,MAAM,EAAE,aAAa;cACrBC,MAAM,EAAE;YACV,CAAC;;YAGD;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,OAAOP,IAAI,CAACyC,WAAW,CAACgB,iBAAiB,CAACnC,MAAM,CAAC,IAAIA,MAAM,CAAC,CAACoB,KAAK,CAAC1C,IAAI,CAACyC,WAAW,EAAER,IAAI,CAAC;UAC5F,CAAC,MAAM;YACL;YACA;YACA,MAAM,IAAIhB,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAC9C;QACF,CAAC,CAAC,OAAOM,CAAC,EAAE;UACV,IACEA,CAAC,CAAC/B,IAAI,KAAK,YAAY;UACvB;UACA+B,CAAC,CAAC/B,IAAI,KAAK,gBAAgB;UAC3B;UACA+B,CAAC,CAAC/B,IAAI,KAAK,qBAAqB,IAChC+B,CAAC,CAAC/B,IAAI,KAAK,gBAAgB,EAC3B;YACA,MAAM,IAAIV,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAEM,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;UAC3C,CAAC,MAAM;YACL,MAAMD,CAAC;UACT;QACF;MACF,CAAC;IACH,CAAC,CAAC;IAEF1D,IAAI,CAACgB,WAAW,CAAC4C,OAAO,CAACxC,CAAC,CAAC;EAC7B;AACF,CAAC;AAED1B,mBAAmB,CAACmE,YAAY,GAAG,UAAUC,MAAM,EAAE;EACnD,IAAM9D,IAAI,GAAG,IAAI;EAEjB,IAAI,CAACA,IAAI,CAACI,WAAW,CAACS,cAAc,EAAE;IACpC,IAAIiD,MAAM,EAAE;MACV,IAAMC,KAAK,GAAGzE,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;MACjC,IAAMC,GAAG,GAAG,SAAAA,CAAAC,KAAK;QAAA,OAAIA,KAAK,IAAIA,KAAK,CAAC7C,OAAO,CAAC,UAAAM,IAAI;UAAA,OAAIoC,KAAK,CAACpC,IAAI,CAAC,GAAG,CAAC;QAAA,EAAC;MAAA;MACpEsC,GAAG,CAACjE,IAAI,CAACI,WAAW,CAACQ,KAAK,CAAC;MAC3BqD,GAAG,CAACH,MAAM,CAAC;MACX9D,IAAI,CAACI,WAAW,CAACQ,KAAK,GAAGtB,MAAM,CAAC6E,IAAI,CAACJ,KAAK,CAAC;IAC7C,CAAC,MAAM;MACL/D,IAAI,CAACI,WAAW,CAACS,cAAc,GAAG,IAAI;MACtC;MACAb,IAAI,CAACI,WAAW,CAACQ,KAAK,GAAG,IAAI;IAC/B;EACF;AACF,CAAC;AAEDlB,mBAAmB,CAAC8D,WAAW,GAAG,YAAY;EAC5C,IAAMxD,IAAI,GAAG,IAAI;EACjB,IAAIA,IAAI,CAACE,SAAS,KAAKC,SAAS,EAC9B,OAAO,CAAC,CAACiE,OAAO,CAACC,QAAQ;EAC3B,OAAOrE,IAAI,CAACE,SAAS;AACvB,CAAC;AAED,SAAeoE,SAASA,CAACC,KAAK,EAAEC,SAAS;EAAA,IAAAC,SAAA,EAAAC,KAAA,EAAAC,IAAA;EAAA,OAAA7F,mBAAA,CAAA8F,KAAA;IAAA,SAAAC,WAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;QAAA;UAAAP,SAAA,GAAArF,+BAAA,CACtBmF,KAAK;QAAA;UAAA,KAAAG,KAAA,GAAAD,SAAA,IAAAQ,IAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA;UAAA;UAAbL,IAAI,GAAAD,KAAA,CAAAQ,KAAA;UAAAJ,QAAA,CAAAE,IAAA;UAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CACDX,SAAS,CAACG,IAAI,CAAC;QAAA;UAAA,KAAAG,QAAA,CAAAM,IAAA;YAAAN,QAAA,CAAAE,IAAA;YAAA;UAAA;UAAA,OAAAF,QAAA,CAAAO,MAAA,WAChB,IAAI;QAAA;UAAAP,QAAA,CAAAE,IAAA;UAAA;QAAA;UAAA,OAAAF,QAAA,CAAAO,MAAA,WAGR,KAAK;QAAA;QAAA;UAAA,OAAAP,QAAA,CAAAQ,IAAA;MAAA;IAAA;IAAA,OAAAT,UAAA;EAAA,uBAAAU,OAAA;AAAA;AAGd,SAAeC,UAAUA,CAACjB,KAAK,EAAEC,SAAS;EAAA,IAAAiB,UAAA,EAAAC,MAAA,EAAAf,IAAA;EAAA,OAAA7F,mBAAA,CAAA8F,KAAA;IAAA,SAAAe,YAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAb,IAAA,GAAAa,SAAA,CAAAZ,IAAA;QAAA;UAAAS,UAAA,GAAArG,+BAAA,CACvBmF,KAAK;QAAA;UAAA,KAAAmB,MAAA,GAAAD,UAAA,IAAAR,IAAA;YAAAW,SAAA,CAAAZ,IAAA;YAAA;UAAA;UAAbL,IAAI,GAAAe,MAAA,CAAAR,KAAA;UAAAU,SAAA,CAAAZ,IAAA;UAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CACAX,SAAS,CAACG,IAAI,CAAC;QAAA;UAAA,IAAAiB,SAAA,CAAAR,IAAA;YAAAQ,SAAA,CAAAZ,IAAA;YAAA;UAAA;UAAA,OAAAY,SAAA,CAAAP,MAAA,WACjB,KAAK;QAAA;UAAAO,SAAA,CAAAZ,IAAA;UAAA;QAAA;UAAA,OAAAY,SAAA,CAAAP,MAAA,WAGT,IAAI;QAAA;QAAA;UAAA,OAAAO,SAAA,CAAAN,IAAA;MAAA;IAAA;IAAA,OAAAK,WAAA;EAAA,uBAAAJ,OAAA;AAAA;AAGb7F,mBAAmB,CAACmG,qBAAqB;EAAG,SAAAC,SAAexC,MAAM,EAAEyC,GAAG,EACX3D,WAAW;IAAA,IAAApC,IAAA;IAAA,OAAAlB,mBAAA,CAAA8F,KAAA;MAAA,SAAAoB,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;UAAA;YAC9DhF,IAAI,GAAG,IAAI,EACjB;YACA;YAAAiG,SAAA,CAAAjB,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CACUb,SAAS,CAACtE,IAAI,CAACI,WAAW,CAACC,MAAM,CAACP,IAAI;cAAE,SAAAoG,QAAOC,SAAS;gBAAA,IAAAC,MAAA;gBAAA,OAAAtH,mBAAA,CAAA8F,KAAA;kBAAA,SAAAyB,SAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;sBAAA;wBAC1DoB,MAAM,GAAGD,SAAS,CAAC7C,MAAM,EAAEiD,aAAa,CAACJ,SAAS,EAAEJ,GAAG,EAAE3D,WAAW,CAAC,CAAC;wBAAA,KACrEnB,MAAM,CAACuF,UAAU,CAACJ,MAAM,CAAC;0BAAAE,SAAA,CAAAtB,IAAA;0BAAA;wBAAA;wBAAAsB,SAAA,CAAAtB,IAAA;wBAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAASiB,MAAM;sBAAA;wBAAAE,SAAA,CAAAG,EAAA,GAAAH,SAAA,CAAAlB,IAAA;wBAAAkB,SAAA,CAAAtB,IAAA;wBAAA;sBAAA;wBAAAsB,SAAA,CAAAG,EAAA,GAAGL,MAAM;sBAAA;wBAAA,OAAAE,SAAA,CAAAjB,MAAA,WAAAiB,SAAA,CAAAG,EAAA;sBAAA;sBAAA;wBAAA,OAAAH,SAAA,CAAAhB,IAAA;oBAAA;kBAAA;kBAAA,OAAAe,QAAA;gBAAA,uBAAAd,OAAA;cAAA;cACzD,OAAAW,OAAA;YAAA,IAAC;UAAA;YAAA,KAAAD,SAAA,CAAAb,IAAA;cAAAa,SAAA,CAAAjB,IAAA;cAAA;YAAA;YAAA,MACM,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAAA;YAAA6C,SAAA,CAAAjB,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAIpCK,UAAU,CAACxF,IAAI,CAACI,WAAW,CAACC,MAAM,CAACV,KAAK;cAAE,SAAA+G,SAAOP,SAAS;gBAAA,IAAAC,MAAA;gBAAA,OAAAtH,mBAAA,CAAA8F,KAAA;kBAAA,SAAA+B,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;sBAAA;wBAC5DoB,MAAM,GAAGD,SAAS,CAAC7C,MAAM,EAAEiD,aAAa,CAACJ,SAAS,EAAEJ,GAAG,EAAE3D,WAAW,CAAC,CAAC;wBAAA,KACnEnB,MAAM,CAACuF,UAAU,CAACJ,MAAM,CAAC;0BAAAQ,SAAA,CAAA5B,IAAA;0BAAA;wBAAA;wBAAA4B,SAAA,CAAA5B,IAAA;wBAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAASiB,MAAM;sBAAA;wBAAAQ,SAAA,CAAAH,EAAA,GAAAG,SAAA,CAAAxB,IAAA;wBAAAwB,SAAA,CAAA5B,IAAA;wBAAA;sBAAA;wBAAA4B,SAAA,CAAAH,EAAA,GAAGL,MAAM;sBAAA;wBAAA,OAAAQ,SAAA,CAAAvB,MAAA,YAAAuB,SAAA,CAAAH,EAAA;sBAAA;sBAAA;wBAAA,OAAAG,SAAA,CAAAtB,IAAA;oBAAA;kBAAA;kBAAA,OAAAqB,SAAA;gBAAA,uBAAApB,OAAA;cAAA;cAC3D,OAAAmB,QAAA;YAAA,IAAC;UAAA;YAAA,KAAAT,SAAA,CAAAb,IAAA;cAAAa,SAAA,CAAAjB,IAAA;cAAA;YAAA;YAAA,MACM,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAAA;YAG9C;YACA;YACA,IAAIhB,WAAW,KAAK,IAAI,EACtB2D,GAAG,CAACvD,GAAG,GAAGJ,WAAW;YAAC,OAAA6D,SAAA,CAAAZ,MAAA,WAEjBrF,IAAI,CAACyC,WAAW,CAACjC,WAAW,CAAC6B,IAAI,CAACrC,IAAI,CAACyC,WAAW,EAAEsD,GAAG,CAAC;UAAA;UAAA;YAAA,OAAAE,SAAA,CAAAX,IAAA;QAAA;MAAA;MAAA,OAAAU,SAAA;IAAA,uBAAAT,OAAA;EAAA;EAChE,OAAAO,QAAA;AAAA;;AAED;AACA;AACA;AACA;AACApG,mBAAmB,CAACmH,qBAAqB;EAAG,SAAAC,SACxCxD,MAAM,EAAEyD,QAAQ,EAAEC,OAAO,EAAEpH,OAAO;IAAA,IAAAI,IAAA,EAAAiH,cAAA,EAAAC,WAAA,EAAAC,cAAA,EAAArD,MAAA,EAAAsD,WAAA,EAAArB,GAAA;IAAA,OAAAjH,mBAAA,CAAA8F,KAAA;MAAA,SAAAyC,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAvC,IAAA,GAAAuC,SAAA,CAAAtC,IAAA;UAAA;YAC9BhF,IAAI,GAAG,IAAI;YAEjB6B,KAAK,CAACmF,OAAO,EAAE1H,MAAM,CAAC;YAEtBM,OAAO,GAAGN,MAAM,CAACiI,MAAM,CAACjI,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC,EAAEpE,OAAO,CAAC;YAAC,IAEjD4H,eAAe,CAACC,4BAA4B,CAACV,QAAQ,CAAC;cAAAO,SAAA,CAAAtC,IAAA;cAAA;YAAA;YAAA,MACnD,IAAI5B,KAAK,CAAC,2CAA2C,CAAC;UAAA;YAAA,KAI1DxD,OAAO,CAAC8H,MAAM;cAAAJ,SAAA,CAAAtC,IAAA;cAAA;YAAA;YAAA,MACV,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,6BAA6B,GAClC,qCAAqC,CAAC;UAAA;YAEzD6D,cAAc,GAAG,wDAAwD,GACzE,yEAAyE,GACzE,YAAY;YAEZC,WAAW,GAAG5H,MAAM,CAAC6E,IAAI,CAAC6C,OAAO,CAAC,EAExC;YACMG,cAAc,GAAG,CAAC,CAAC;YAAA,MAErBD,WAAW,CAAC/D,MAAM,KAAK,CAAC;cAAAmE,SAAA,CAAAtC,IAAA;cAAA;YAAA;YAAA,MACpB,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE6D,cAAc,CAAC;UAAA;YAE7CC,WAAW,CAAC7F,OAAO,CAAC,UAACsG,EAAE,EAAK;cAC1B,IAAMC,MAAM,GAAGZ,OAAO,CAACW,EAAE,CAAC;cAC1B,IAAIA,EAAE,CAAC5E,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACxB,MAAM,IAAI9B,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE6D,cAAc,CAAC;cAC7C,CAAC,MAAM,IAAI,CAAC5H,MAAM,CAACgD,IAAI,CAACwF,yBAAyB,EAAEF,EAAE,CAAC,EAAE;gBACtD,MAAM,IAAI1G,MAAM,CAACmC,KAAK,CACpB,GAAG,EAAE,0BAA0B,GAAGuE,EAAE,GAAG,0CAA0C,CAAC;cACtF,CAAC,MAAM;gBACLrI,MAAM,CAAC6E,IAAI,CAACyD,MAAM,CAAC,CAACvG,OAAO,CAAC,UAACyG,KAAK,EAAK;kBACrC;kBACA;kBACA,IAAIA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAC3BD,KAAK,GAAGA,KAAK,CAACE,SAAS,CAAC,CAAC,EAAEF,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,CAAC;;kBAEhD;kBACAZ,cAAc,CAACW,KAAK,CAAC,GAAG,IAAI;gBAC9B,CAAC,CAAC;cACJ;YACF,CAAC,CAAC;YAEIhE,MAAM,GAAGxE,MAAM,CAAC6E,IAAI,CAACgD,cAAc,CAAC;YAEpCC,WAAW,GAAG;cAACa,SAAS,EAAE;YAAI,CAAC;YACrC,IAAI,CAACjI,IAAI,CAACI,WAAW,CAACS,cAAc,EAAE;cACpCuG,WAAW,CAACtD,MAAM,GAAG,CAAC,CAAC;cACvB9D,IAAI,CAACI,WAAW,CAACQ,KAAK,CAACS,OAAO,CAAC,UAAC6G,SAAS,EAAK;gBAC5Cd,WAAW,CAACtD,MAAM,CAACoE,SAAS,CAAC,GAAG,CAAC;cACnC,CAAC,CAAC;YACJ;YAACZ,SAAA,CAAAtC,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAEiBnF,IAAI,CAACyC,WAAW,CAAC0F,YAAY,CAACpB,QAAQ,EAAEK,WAAW,CAAC;UAAA;YAAhErB,GAAG,GAAAuB,SAAA,CAAAlC,IAAA;YAAA,IACJW,GAAG;cAAAuB,SAAA,CAAAtC,IAAA;cAAA;YAAA;YAAA,OAAAsC,SAAA,CAAAjC,MAAA,WACC,CAAC;UAAA;YAAAiC,SAAA,CAAAtC,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAIAb,SAAS,CAACtE,IAAI,CAACI,WAAW,CAACE,MAAM,CAACR,IAAI;cAAE,SAAAsI,SAAOjC,SAAS;gBAAA,IAAAkC,YAAA,EAAAjC,MAAA;gBAAA,OAAAtH,mBAAA,CAAA8F,KAAA;kBAAA,SAAA0D,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAxD,IAAA,GAAAwD,SAAA,CAAAvD,IAAA;sBAAA;wBAC1DqD,YAAY,GAAGG,YAAY,CAACrC,SAAS,EAAEJ,GAAG,CAAC;wBAC3CK,MAAM,GAAGD,SAAS,CAAC7C,MAAM,EAC7B+E,YAAY,EACZvE,MAAM,EACNkD,OAAO,CAAC;wBAAA,KACH/F,MAAM,CAACuF,UAAU,CAACJ,MAAM,CAAC;0BAAAmC,SAAA,CAAAvD,IAAA;0BAAA;wBAAA;wBAAAuD,SAAA,CAAAvD,IAAA;wBAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAASiB,MAAM;sBAAA;wBAAAmC,SAAA,CAAA9B,EAAA,GAAA8B,SAAA,CAAAnD,IAAA;wBAAAmD,SAAA,CAAAvD,IAAA;wBAAA;sBAAA;wBAAAuD,SAAA,CAAA9B,EAAA,GAAGL,MAAM;sBAAA;wBAAA,OAAAmC,SAAA,CAAAlD,MAAA,WAAAkD,SAAA,CAAA9B,EAAA;sBAAA;sBAAA;wBAAA,OAAA8B,SAAA,CAAAjD,IAAA;oBAAA;kBAAA;kBAAA,OAAAgD,SAAA;gBAAA,uBAAA/C,OAAA;cAAA;cACzD,OAAA6C,QAAA;YAAA,IAAC;UAAA;YAAA,KAAAd,SAAA,CAAAlC,IAAA;cAAAkC,SAAA,CAAAtC,IAAA;cAAA;YAAA;YAAA,MACM,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAAA;YAAAkE,SAAA,CAAAtC,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAIpCK,UAAU,CAACxF,IAAI,CAACI,WAAW,CAACE,MAAM,CAACX,KAAK;cAAE,SAAA8I,SAAOtC,SAAS;gBAAA,IAAAkC,YAAA,EAAAjC,MAAA;gBAAA,OAAAtH,mBAAA,CAAA8F,KAAA;kBAAA,SAAA8D,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;sBAAA;wBAC5DqD,YAAY,GAAGG,YAAY,CAACrC,SAAS,EAAEJ,GAAG,CAAC;wBAC3CK,MAAM,GAAGD,SAAS,CAAC7C,MAAM,EAC7B+E,YAAY,EACZvE,MAAM,EACNkD,OAAO,CAAC;wBAAA,KACD/F,MAAM,CAACuF,UAAU,CAACJ,MAAM,CAAC;0BAAAuC,SAAA,CAAA3D,IAAA;0BAAA;wBAAA;wBAAA2D,SAAA,CAAA3D,IAAA;wBAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAASiB,MAAM;sBAAA;wBAAAuC,SAAA,CAAAlC,EAAA,GAAAkC,SAAA,CAAAvD,IAAA;wBAAAuD,SAAA,CAAA3D,IAAA;wBAAA;sBAAA;wBAAA2D,SAAA,CAAAlC,EAAA,GAAGL,MAAM;sBAAA;wBAAA,OAAAuC,SAAA,CAAAtD,MAAA,YAAAsD,SAAA,CAAAlC,EAAA;sBAAA;sBAAA;wBAAA,OAAAkC,SAAA,CAAArD,IAAA;oBAAA;kBAAA;kBAAA,OAAAoD,SAAA;gBAAA,uBAAAnD,OAAA;cAAA;cAC3D,OAAAkD,QAAA;YAAA,IAAC;UAAA;YAAA,KAAAnB,SAAA,CAAAlC,IAAA;cAAAkC,SAAA,CAAAtC,IAAA;cAAA;YAAA;YAAA,MACM,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAAA;YAG9CxD,OAAO,CAACgJ,cAAc,GAAG,IAAI;;YAE7B;YACA;YACA;YACA;YAAA,OAAAtB,SAAA,CAAAjC,MAAA,WAEOrF,IAAI,CAACyC,WAAW,CAAChC,WAAW,CAAC4B,IAAI,CACtCrC,IAAI,CAACyC,WAAW,EAAEsE,QAAQ,EAAEC,OAAO,EAAEpH,OAAO,CAAC;UAAA;UAAA;YAAA,OAAA0H,SAAA,CAAAhC,IAAA;QAAA;MAAA;MAAA,OAAA+B,SAAA;IAAA,uBAAA9B,OAAA;EAAA;EAChD,OAAAuB,QAAA;AAAA;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAMe,yBAAyB,GAAG;EAChCgB,IAAI,EAAC,CAAC;EAAEC,IAAI,EAAC,CAAC;EAAEC,MAAM,EAAC,CAAC;EAAEC,SAAS,EAAC,CAAC;EAAEC,IAAI,EAAC,CAAC;EAAEC,QAAQ,EAAC,CAAC;EAAEC,KAAK,EAAC,CAAC;EAClEC,QAAQ,EAAC,CAAC;EAAEC,KAAK,EAAC,CAAC;EAAEC,IAAI,EAAC;AAC5B,CAAC;;AAED;AACA;AACA5J,mBAAmB,CAAC6J,qBAAqB;EAAG,SAAAC,SAAelG,MAAM,EAAEyD,QAAQ;IAAA,IAAA/G,IAAA,EAAAoH,WAAA,EAAArB,GAAA;IAAA,OAAAjH,mBAAA,CAAA8F,KAAA;MAAA,SAAA6E,UAAAC,UAAA;QAAA,kBAAAA,UAAA,CAAA3E,IAAA,GAAA2E,UAAA,CAAA1E,IAAA;UAAA;YACnEhF,IAAI,GAAG,IAAI;YAEXoH,WAAW,GAAG;cAACa,SAAS,EAAE;YAAI,CAAC;YACrC,IAAI,CAACjI,IAAI,CAACI,WAAW,CAACS,cAAc,EAAE;cACpCuG,WAAW,CAACtD,MAAM,GAAG,CAAC,CAAC;cACvB9D,IAAI,CAACI,WAAW,CAACQ,KAAK,CAACS,OAAO,CAAC,UAAC6G,SAAS,EAAK;gBAC5Cd,WAAW,CAACtD,MAAM,CAACoE,SAAS,CAAC,GAAG,CAAC;cACnC,CAAC,CAAC;YACJ;YAACwB,UAAA,CAAA1E,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAEiBnF,IAAI,CAACyC,WAAW,CAAC0F,YAAY,CAACpB,QAAQ,EAAEK,WAAW,CAAC;UAAA;YAAhErB,GAAG,GAAA2D,UAAA,CAAAtE,IAAA;YAAA,IACJW,GAAG;cAAA2D,UAAA,CAAA1E,IAAA;cAAA;YAAA;YAAA,OAAA0E,UAAA,CAAArE,MAAA,WACC,CAAC;UAAA;YAAAqE,UAAA,CAAA1E,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAIAb,SAAS,CAACtE,IAAI,CAACI,WAAW,CAACG,MAAM,CAACT,IAAI;cAAE,SAAA6J,SAAOxD,SAAS;gBAAA,IAAAC,MAAA;gBAAA,OAAAtH,mBAAA,CAAA8F,KAAA;kBAAA,SAAAgF,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA9E,IAAA,GAAA8E,SAAA,CAAA7E,IAAA;sBAAA;wBAC1DoB,MAAM,GAAGD,SAAS,CAAC7C,MAAM,EAAEkF,YAAY,CAACrC,SAAS,EAAEJ,GAAG,CAAC,CAAC;wBAAA,KACvD9E,MAAM,CAACuF,UAAU,CAACJ,MAAM,CAAC;0BAAAyD,SAAA,CAAA7E,IAAA;0BAAA;wBAAA;wBAAA6E,SAAA,CAAA7E,IAAA;wBAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAASiB,MAAM;sBAAA;wBAAAyD,SAAA,CAAApD,EAAA,GAAAoD,SAAA,CAAAzE,IAAA;wBAAAyE,SAAA,CAAA7E,IAAA;wBAAA;sBAAA;wBAAA6E,SAAA,CAAApD,EAAA,GAAGL,MAAM;sBAAA;wBAAA,OAAAyD,SAAA,CAAAxE,MAAA,WAAAwE,SAAA,CAAApD,EAAA;sBAAA;sBAAA;wBAAA,OAAAoD,SAAA,CAAAvE,IAAA;oBAAA;kBAAA;kBAAA,OAAAsE,SAAA;gBAAA,uBAAArE,OAAA;cAAA;cACzD,OAAAoE,QAAA;YAAA,IAAC;UAAA;YAAA,KAAAD,UAAA,CAAAtE,IAAA;cAAAsE,UAAA,CAAA1E,IAAA;cAAA;YAAA;YAAA,MACM,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAAA;YAAAsG,UAAA,CAAA1E,IAAA;YAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAGpCK,UAAU,CAACxF,IAAI,CAACI,WAAW,CAACG,MAAM,CAACZ,KAAK;cAAE,SAAAmK,SAAO3D,SAAS;gBAAA,IAAAC,MAAA;gBAAA,OAAAtH,mBAAA,CAAA8F,KAAA;kBAAA,SAAAmF,UAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAjF,IAAA,GAAAiF,UAAA,CAAAhF,IAAA;sBAAA;wBAC5DoB,MAAM,GAAGD,SAAS,CAAC7C,MAAM,EAAEkF,YAAY,CAACrC,SAAS,EAAEJ,GAAG,CAAC,CAAC;wBAAA,KACrD9E,MAAM,CAACuF,UAAU,CAACJ,MAAM,CAAC;0BAAA4D,UAAA,CAAAhF,IAAA;0BAAA;wBAAA;wBAAAgF,UAAA,CAAAhF,IAAA;wBAAA,OAAAlG,mBAAA,CAAAqG,KAAA,CAASiB,MAAM;sBAAA;wBAAA4D,UAAA,CAAAvD,EAAA,GAAAuD,UAAA,CAAA5E,IAAA;wBAAA4E,UAAA,CAAAhF,IAAA;wBAAA;sBAAA;wBAAAgF,UAAA,CAAAvD,EAAA,GAAGL,MAAM;sBAAA;wBAAA,OAAA4D,UAAA,CAAA3E,MAAA,YAAA2E,UAAA,CAAAvD,EAAA;sBAAA;sBAAA;wBAAA,OAAAuD,UAAA,CAAA1E,IAAA;oBAAA;kBAAA;kBAAA,OAAAyE,SAAA;gBAAA,uBAAAxE,OAAA;cAAA;cAC3D,OAAAuE,QAAA;YAAA,IAAC;UAAA;YAAA,KAAAJ,UAAA,CAAAtE,IAAA;cAAAsE,UAAA,CAAA1E,IAAA;cAAA;YAAA;YAAA,MACM,IAAI/D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;UAAA;YAAA,OAAAsG,UAAA,CAAArE,MAAA,WAQvCrF,IAAI,CAACyC,WAAW,CAAC/B,WAAW,CAAC2B,IAAI,CAACrC,IAAI,CAACyC,WAAW,EAAEsE,QAAQ,CAAC;UAAA;UAAA;YAAA,OAAA2C,UAAA,CAAApE,IAAA;QAAA;MAAA;MAAA,OAAAmE,SAAA;IAAA,uBAAAlE,OAAA;EAAA;EACrE,OAAAiE,QAAA;AAAA;AAED9J,mBAAmB,CAACuK,uBAAuB;EAAG,SAASA,uBAAuBA,CAACtI,IAAI,EAAEM,IAAI,EAAgB;IAAA,IAAdrC,OAAO,GAAAkC,SAAA,CAAAqB,MAAA,QAAArB,SAAA,QAAA3B,SAAA,GAAA2B,SAAA,MAAG,CAAC,CAAC;IAErG;IACA,IAAMoI,kBAAkB,GAAGvI,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa;IAC3E,IAAIuI,kBAAkB,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;MAChD;MACA;MACA;MACAxH,sBAAsB,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEN,IAAI,CAAC;IACvC;IAEA,IAAMyI,iBAAiB,GAAG,IAAI,CAACrJ,OAAO,GAAGY,IAAI;IAC7C,OAAO,IAAI,CAACX,WAAW,CAACqJ,UAAU,CAACD,iBAAiB,EAAEnI,IAAI,EAAA9C,aAAA;MACxDmL,eAAe,EAAE,IAAI,CAACC,YAAY,KAAK,MAAM,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI;MAC1E;MACAC,yBAAyB,EAAE,CAAC,IAAI,CAACxJ,WAAW,CAACyJ,OAAO,CAACC,OAAO,IAAI,IAAI,CAACH,YAAY,KAAK;IAAM,GACzF3K,OAAO,CACX,CAAC;EACJ;EAAC,OAlBsDqK,uBAAuB;AAAA,GAkB7E;AAEDvK,mBAAmB,CAACiL,kBAAkB;EAAG,SAASA,kBAAkBA,CAAChJ,IAAI,EAAEM,IAAI,EAAE2I,QAAQ,EAAE;IACzF,IAAI3J,MAAM,CAACE,QAAQ,IAAI,CAACyJ,QAAQ,IAAI,CAACT,mBAAmB,CAAC,CAAC,EAAE;MAC1D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAS,QAAQ,GAAG,SAAAA,CAAUC,GAAG,EAAE;QACxB,IAAIA,GAAG,EACL5J,MAAM,CAAC6J,MAAM,CAACnJ,IAAI,GAAG,SAAS,EAAEkJ,GAAG,CAAC;MACxC,CAAC;IACH;;IAEA;IACA,IAAMX,kBAAkB,GAAGvI,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;IACjE,IAAIuI,kBAAkB,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;MAChD;MACA;MACA;MACAxH,sBAAsB,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEN,IAAI,CAAC;IACvC;IAEA,IAAMyI,iBAAiB,GAAG,IAAI,CAACrJ,OAAO,GAAGY,IAAI;IAC7C,OAAO,IAAI,CAACX,WAAW,CAAC0B,KAAK,CAC3B0H,iBAAiB,EAAEnI,IAAI,EAAE;MAAEqI,eAAe,EAAE;IAAK,CAAC,EAAEM,QAAQ,CAAC;EACjE;EAAC,OA5BiDD,kBAAkB;AAAA,GA4BnE;AAED,SAASnC,YAAYA,CAACrC,SAAS,EAAEJ,GAAG,EAAE;EACpC,IAAII,SAAS,CAAC8B,SAAS,EACrB,OAAO9B,SAAS,CAAC8B,SAAS,CAAClC,GAAG,CAAC;EACjC,OAAOA,GAAG;AACZ;AAEA,SAASQ,aAAaA,CAACJ,SAAS,EAAEJ,GAAG,EAAE3D,WAAW,EAAE;EAClD,IAAI2I,GAAG,GAAGhF,GAAG;EACb,IAAII,SAAS,CAAC8B,SAAS,EAAE;IACvB8C,GAAG,GAAGC,KAAK,CAACC,KAAK,CAAClF,GAAG,CAAC;IACtB;IACA;IACA;IACA;IACA;IACA,IAAI3D,WAAW,KAAK,IAAI,EAAE;MACxB2I,GAAG,CAACvI,GAAG,GAAGJ,WAAW;IACvB;IACA2I,GAAG,GAAG5E,SAAS,CAAC8B,SAAS,CAAC8C,GAAG,CAAC;EAChC;EACA,OAAOA,GAAG;AACZ;AAEA,SAASlL,YAAYA,CAACqL,UAAU,EAAEC,WAAW,EAAEvL,OAAO,EAAE;EACtD;EACA,IAAMwL,cAAc,GAAG,gFAAgF;EACvG9L,MAAM,CAAC6E,IAAI,CAACvE,OAAO,CAAC,CAACyB,OAAO,CAAC,UAACgK,GAAG,EAAK;IACpC,IAAI,CAACD,cAAc,CAACE,IAAI,CAACD,GAAG,CAAC,EAC3B,MAAM,IAAIjI,KAAK,CAAC+H,WAAW,GAAG,iBAAiB,GAAGE,GAAG,CAAC;;IAExD;IACA,IAAME,UAAU,GAAGF,GAAG,CAACzJ,QAAQ,CAAC,OAAO,CAAC;IACxC,IAAI2J,UAAU,EAAE;MACd,IAAMC,OAAO,GAAGH,GAAG,CAACxI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MACxC5B,MAAM,CAACwK,SAAS,CAACN,WAAW,iBAAaE,GAAG,oCAA6BG,OAAO,iBAAY,CAAC;IAC/F;EACF,CAAC,CAAC;EAEFN,UAAU,CAACjL,WAAW,GAAG,IAAI;EAE7B,CACE,aAAa,EACb,aAAa,EACb,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT,CAACoB,OAAO,CAAC,UAAAM,IAAI,EAAI;IAChB,IAAItC,MAAM,CAACgD,IAAI,CAACzC,OAAO,EAAE+B,IAAI,CAAC,EAAE;MAC9B,IAAI,EAAE/B,OAAO,CAAC+B,IAAI,CAAC,YAAY+J,QAAQ,CAAC,EAAE;QACxC,MAAM,IAAItI,KAAK,CACb+H,WAAW,GAAG,eAAe,GAAGxJ,IAAI,GAAG,sBACzC,CAAC;MACH;;MAEA;MACA;MACA;MACA,IAAI/B,OAAO,CAACqI,SAAS,KAAK9H,SAAS,EAAE;QACnCP,OAAO,CAAC+B,IAAI,CAAC,CAACsG,SAAS,GAAGiD,UAAU,CAACS,UAAU,CAAC,CAAC;MACnD,CAAC,MAAM;QACL/L,OAAO,CAAC+B,IAAI,CAAC,CAACsG,SAAS,GAAGT,eAAe,CAACoE,aAAa,CACrDhM,OAAO,CAACqI,SACV,CAAC;MACH;MACA,IAAM4D,WAAW,GAAGlK,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC;MAC1C,IAAMkK,iBAAiB,GAAGD,WAAW,GAAGlK,IAAI,CAACkB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAGlB,IAAI;MACxEuJ,UAAU,CAAC9K,WAAW,CAAC0L,iBAAiB,CAAC,CAACX,WAAW,CAAC,CAAC5H,IAAI,CAAC3D,OAAO,CAAC+B,IAAI,CAAC,CAAC;IAC5E;EACF,CAAC,CAAC;;EAEF;EACA;EACA;EACA,IAAI/B,OAAO,CAACa,WAAW,IAAIb,OAAO,CAACc,WAAW,IAAId,OAAO,CAACgB,KAAK,EAAE;IAC/D,IAAIhB,OAAO,CAACgB,KAAK,IAAI,EAAEhB,OAAO,CAACgB,KAAK,YAAYsB,KAAK,CAAC,EAAE;MACtD,MAAM,IAAIkB,KAAK,CAAC+H,WAAW,GAAG,sCAAsC,CAAC;IACvE;IACAD,UAAU,CAACrH,YAAY,CAACjE,OAAO,CAACgB,KAAK,CAAC;EACxC;AACF;AAEA,SAAS+B,sBAAsBA,CAACoE,QAAQ,EAAExF,UAAU,EAAE;EACpD,IAAI,CAACiG,eAAe,CAACC,4BAA4B,CAACV,QAAQ,CAAC,EAAE;IAC3D,MAAM,IAAI9F,MAAM,CAACmC,KAAK,CACpB,GAAG,EAAE,yCAAyC,GAAG7B,UAAU,GACzD,mBAAmB,CAAC;EAC1B;AACF;AAAC;;AAED;AACA,SAAS4I,mBAAmBA,CAAA,EAAG;EAC7B,IAAI4B,iBAAiB,GACnBC,GAAG,CAACC,wBAAwB;EAC5B;EACA;EACAD,GAAG,CAACE,kBAAkB;EAExB,IAAMC,SAAS,GAAGJ,iBAAiB,CAACK,GAAG,CAAC,CAAC;EACzC,OAAOD,SAAS,IAAIA,SAAS,CAAC5J,YAAY;AAC5C,C", "file": "/packages/allow-deny.js", "sourcesContent": ["///\n/// Remote methods and access control.\n///\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\n// Restrict default mutators on collection. allow() and deny() take the\n// same options:\n//\n// options.insertAsync {Function(userId, doc)}\n//   return true to allow/deny adding this document\n//\n// options.updateAsync {Function(userId, docs, fields, modifier)}\n//   return true to allow/deny updating these documents.\n//   `fields` is passed as an array of fields that are to be modified\n//\n// options.removeAsync {Function(userId, docs)}\n//   return true to allow/deny removing these documents\n//\n// options.fetch {Array}\n//   Fields to fetch for these validators. If any call to allow or deny\n//   does not have this option then all fields are loaded.\n//\n// allow and deny can be called multiple times. The validators are\n// evaluated as follows:\n// - If neither deny() nor allow() has been called on the collection,\n//   then the request is allowed if and only if the \"insecure\" smart\n//   package is in use.\n// - Otherwise, if any deny() function returns true, the request is denied.\n// - Otherwise, if any allow() function returns true, the request is allowed.\n// - Otherwise, the request is denied.\n//\n// Meteor may call your deny() and allow() functions in any order, and may not\n// call all of them if it is able to make a decision without calling them all\n// (so don't include side effects).\n\nAllowDeny = {\n  CollectionPrototype: {}\n};\n\n// In the `mongo` package, we will extend Mongo.Collection.prototype with these\n// methods\nconst CollectionPrototype = AllowDeny.CollectionPrototype;\n\n/**\n * @summary Allow users to write directly to this collection from client code, subject to limitations you define.\n * @locus Server\n * @method allow\n * @memberOf Mongo.Collection\n * @instance\n * @param {Object} options\n * @param {Function} options.insert,update,remove Functions that look at a proposed modification to the database and return true if it should be allowed.\n * @param {String[]} options.fetch Optional performance enhancement. Limits the fields that will be fetched from the database for inspection by your `update` and `remove` functions.\n * @param {Function} options.transform Overrides `transform` on the  [`Collection`](#collections).  Pass `null` to disable transformation.\n */\nCollectionPrototype.allow = function(options) {\n  addValidator(this, 'allow', options);\n};\n\n/**\n * @summary Override `allow` rules.\n * @locus Server\n * @method deny\n * @memberOf Mongo.Collection\n * @instance\n * @param {Object} options\n * @param {Function} options.insert,update,remove Functions that look at a proposed modification to the database and return true if it should be denied, even if an [allow](#allow) rule says otherwise.\n * @param {String[]} options.fetch Optional performance enhancement. Limits the fields that will be fetched from the database for inspection by your `update` and `remove` functions.\n * @param {Function} options.transform Overrides `transform` on the  [`Collection`](#collections).  Pass `null` to disable transformation.\n */\nCollectionPrototype.deny = function(options) {\n  addValidator(this, 'deny', options);\n};\n\nCollectionPrototype._defineMutationMethods = function(options) {\n  const self = this;\n  options = options || {};\n\n  // set to true once we call any allow or deny methods. If true, use\n  // allow/deny semantics. If false, use insecure mode semantics.\n  self._restricted = false;\n\n  // Insecure mode (default to allowing writes). Defaults to 'undefined' which\n  // means insecure iff the insecure package is loaded. This property can be\n  // overriden by tests or packages wishing to change insecure mode behavior of\n  // their collections.\n  self._insecure = undefined;\n\n  self._validators = {\n    insert: {allow: [], deny: []},\n    update: {allow: [], deny: []},\n    remove: {allow: [], deny: []},\n    insertAsync: {allow: [], deny: []},\n    updateAsync: {allow: [], deny: []},\n    removeAsync: {allow: [], deny: []},\n    upsertAsync: {allow: [], deny: []}, // dummy arrays; can't set these!\n    fetch: [],\n    fetchAllFields: false\n  };\n\n  if (!self._name)\n    return; // anonymous collection\n\n  // XXX Think about method namespacing. Maybe methods should be\n  // \"Meteor:Mongo:insertAsync/NAME\"?\n  self._prefix = '/' + self._name + '/';\n\n  // Mutation Methods\n  // Minimongo on the server gets no stubs; instead, by default\n  // it wait()s until its result is ready, yielding.\n  // This matches the behavior of macromongo on the server better.\n  // XXX see #MeteorServerNull\n  if (self._connection && (self._connection === Meteor.server || Meteor.isClient)) {\n    const m = {};\n\n    [\n      'insertAsync',\n      'updateAsync',\n      'removeAsync',\n      'insert',\n      'update',\n      'remove',\n    ].forEach(method => {\n      const methodName = self._prefix + method;\n\n      if (options.useExisting) {\n        const handlerPropName = Meteor.isClient\n          ? '_methodHandlers'\n          : 'method_handlers';\n        // Do not try to create additional methods if this has already been called.\n        // (Otherwise the .methods() call below will throw an error.)\n        if (\n          self._connection[handlerPropName] &&\n          typeof self._connection[handlerPropName][methodName] === 'function'\n        )\n          return;\n      }\n\n      const isInsert = name => name.includes('insert');\n\n      m[methodName] = function (/* ... */) {\n        // All the methods do their own validation, instead of using check().\n        check(arguments, [Match.Any]);\n        const args = Array.from(arguments);\n        try {\n          // For an insert/insertAsync, if the client didn't specify an _id, generate one\n          // now; because this uses DDP.randomStream, it will be consistent with\n          // what the client generated. We generate it now rather than later so\n          // that if (eg) an allow/deny rule does an insert/insertAsync to the same\n          // collection (not that it really should), the generated _id will\n          // still be the first use of the stream and will be consistent.\n          //\n          // However, we don't actually stick the _id onto the document yet,\n          // because we want allow/deny rules to be able to differentiate\n          // between arbitrary client-specified _id fields and merely\n          // client-controlled-via-randomSeed fields.\n          let generatedId = null;\n          if (isInsert(method) && !hasOwn.call(args[0], '_id')) {\n            generatedId = self._makeNewID();\n          }\n\n          if (this.isSimulation) {\n            // In a client simulation, you can do any mutation (even with a\n            // complex selector).\n            if (generatedId !== null) {\n              args[0]._id = generatedId;\n            }\n            return self._collection[method].apply(self._collection, args);\n          }\n\n          // This is the server receiving a method call from the client.\n\n          // We don't allow arbitrary selectors in mutations from the client: only\n          // single-ID selectors.\n          if (!isInsert(method)) throwIfSelectorIsNotId(args[0], method);\n\n          const syncMethodName = method.replace('Async', '');\n          const syncValidatedMethodName = '_validated' + method.charAt(0).toUpperCase() + syncMethodName.slice(1);\n          // it forces to use async validated behavior\n          const validatedMethodName = syncValidatedMethodName + 'Async';\n\n          if (self._restricted) {\n            // short circuit if there is no way it will pass.\n            if (self._validators[syncMethodName].allow.length === 0) {\n              throw new Meteor.Error(\n                403,\n                'Access denied. No allow validators set on restricted ' +\n                  \"collection for method '\" +\n                  method +\n                  \"'.\"\n              );\n            }\n\n            args.unshift(this.userId);\n            isInsert(method) && args.push(generatedId);\n            return self[validatedMethodName].apply(self, args);\n          } else if (self._isInsecure()) {\n            if (generatedId !== null) args[0]._id = generatedId;\n            // In insecure mode we use the server _collection methods, and these sync methods\n            // do not exist in the server anymore, so we have this mapper to call the async methods\n            // instead.\n            const syncMethodsMapper = {\n              insert: \"insertAsync\",\n              update: \"updateAsync\",\n              remove: \"removeAsync\",\n            };\n\n\n            // In insecure mode, allow any mutation (with a simple selector).\n            // XXX This is kind of bogus.  Instead of blindly passing whatever\n            //     we get from the network to this function, we should actually\n            //     know the correct arguments for the function and pass just\n            //     them.  For example, if you have an extraneous extra null\n            //     argument and this is Mongo on the server, the .wrapAsync'd\n            //     functions like update will get confused and pass the\n            //     \"fut.resolver()\" in the wrong slot, where _update will never\n            //     invoke it. Bam, broken DDP connection.  Probably should just\n            //     take this whole method and write it three times, invoking\n            //     helpers for the common code.\n            return self._collection[syncMethodsMapper[method] || method].apply(self._collection, args);\n          } else {\n            // In secure mode, if we haven't called allow or deny, then nothing\n            // is permitted.\n            throw new Meteor.Error(403, 'Access denied');\n          }\n        } catch (e) {\n          if (\n            e.name === 'MongoError' ||\n            // for old versions of MongoDB (probably not necessary but it's here just in case)\n            e.name === 'BulkWriteError' ||\n            // for newer versions of MongoDB (https://docs.mongodb.com/drivers/node/current/whats-new/#bulkwriteerror---mongobulkwriteerror)\n            e.name === 'MongoBulkWriteError' ||\n            e.name === 'MinimongoError'\n          ) {\n            throw new Meteor.Error(409, e.toString());\n          } else {\n            throw e;\n          }\n        }\n      };\n    });\n\n    self._connection.methods(m);\n  }\n};\n\nCollectionPrototype._updateFetch = function (fields) {\n  const self = this;\n\n  if (!self._validators.fetchAllFields) {\n    if (fields) {\n      const union = Object.create(null);\n      const add = names => names && names.forEach(name => union[name] = 1);\n      add(self._validators.fetch);\n      add(fields);\n      self._validators.fetch = Object.keys(union);\n    } else {\n      self._validators.fetchAllFields = true;\n      // clear fetch just to make sure we don't accidentally read it\n      self._validators.fetch = null;\n    }\n  }\n};\n\nCollectionPrototype._isInsecure = function () {\n  const self = this;\n  if (self._insecure === undefined)\n    return !!Package.insecure;\n  return self._insecure;\n};\n\nasync function asyncSome(array, predicate) {\n  for (let item of array) {\n    if (await predicate(item)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nasync function asyncEvery(array, predicate) {\n  for (let item of array) {\n    if (!await predicate(item)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nCollectionPrototype._validatedInsertAsync = async function(userId, doc,\n                                                           generatedId) {\n  const self = this;\n  // call user validators.\n  // Any deny returns true means denied.\n  if (await asyncSome(self._validators.insert.deny, async (validator) => {\n    const result = validator(userId, docToValidate(validator, doc, generatedId));\n    return Meteor._isPromise(result) ? await result : result;\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n  // Any allow returns true means proceed. Throw error if they all fail.\n\n  if (await asyncEvery(self._validators.insert.allow, async (validator) => {\n    const result = validator(userId, docToValidate(validator, doc, generatedId));\n    return !(Meteor._isPromise(result) ? await result : result);\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  // If we generated an ID above, insertAsync it now: after the validation, but\n  // before actually inserting.\n  if (generatedId !== null)\n    doc._id = generatedId;\n\n  return self._collection.insertAsync.call(self._collection, doc);\n};\n\n// Simulate a mongo `update` operation while validating that the access\n// control rules set by calls to `allow/deny` are satisfied. If all\n// pass, rewrite the mongo operation to use $in to set the list of\n// document ids to change ##ValidatedChange\nCollectionPrototype._validatedUpdateAsync = async function(\n    userId, selector, mutator, options) {\n  const self = this;\n\n  check(mutator, Object);\n\n  options = Object.assign(Object.create(null), options);\n\n  if (!LocalCollection._selectorIsIdPerhapsAsObject(selector))\n    throw new Error(\"validated update should be of a single ID\");\n\n  // We don't support upserts because they don't fit nicely into allow/deny\n  // rules.\n  if (options.upsert)\n    throw new Meteor.Error(403, \"Access denied. Upserts not \" +\n                           \"allowed in a restricted collection.\");\n\n  const noReplaceError = \"Access denied. In a restricted collection you can only\" +\n        \" update documents, not replace them. Use a Mongo update operator, such \" +\n        \"as '$set'.\";\n\n  const mutatorKeys = Object.keys(mutator);\n\n  // compute modified fields\n  const modifiedFields = {};\n\n  if (mutatorKeys.length === 0) {\n    throw new Meteor.Error(403, noReplaceError);\n  }\n  mutatorKeys.forEach((op) => {\n    const params = mutator[op];\n    if (op.charAt(0) !== '$') {\n      throw new Meteor.Error(403, noReplaceError);\n    } else if (!hasOwn.call(ALLOWED_UPDATE_OPERATIONS, op)) {\n      throw new Meteor.Error(\n        403, \"Access denied. Operator \" + op + \" not allowed in a restricted collection.\");\n    } else {\n      Object.keys(params).forEach((field) => {\n        // treat dotted fields as if they are replacing their\n        // top-level part\n        if (field.indexOf('.') !== -1)\n          field = field.substring(0, field.indexOf('.'));\n\n        // record the field we are trying to change\n        modifiedFields[field] = true;\n      });\n    }\n  });\n\n  const fields = Object.keys(modifiedFields);\n\n  const findOptions = {transform: null};\n  if (!self._validators.fetchAllFields) {\n    findOptions.fields = {};\n    self._validators.fetch.forEach((fieldName) => {\n      findOptions.fields[fieldName] = 1;\n    });\n  }\n\n  const doc = await self._collection.findOneAsync(selector, findOptions);\n  if (!doc)  // none satisfied!\n    return 0;\n\n  // call user validators.\n  // Any deny returns true means denied.\n  if (await asyncSome(self._validators.update.deny, async (validator) => {\n    const factoriedDoc = transformDoc(validator, doc);\n    const result = validator(userId,\n      factoriedDoc,\n      fields,\n      mutator);\n    return Meteor._isPromise(result) ? await result : result;\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  // Any allow returns true means proceed. Throw error if they all fail.\n  if (await asyncEvery(self._validators.update.allow, async (validator) => {\n    const factoriedDoc = transformDoc(validator, doc);\n    const result = validator(userId,\n      factoriedDoc,\n      fields,\n      mutator);\n    return !(Meteor._isPromise(result) ? await result : result);\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  options._forbidReplace = true;\n\n  // Back when we supported arbitrary client-provided selectors, we actually\n  // rewrote the selector to include an _id clause before passing to Mongo to\n  // avoid races, but since selector is guaranteed to already just be an ID, we\n  // don't have to any more.\n\n  return self._collection.updateAsync.call(\n    self._collection, selector, mutator, options);\n};\n\n// Only allow these operations in validated updates. Specifically\n// whitelist operations, rather than blacklist, so new complex\n// operations that are added aren't automatically allowed. A complex\n// operation is one that does more than just modify its target\n// field. For now this contains all update operations except '$rename'.\n// http://docs.mongodb.org/manual/reference/operators/#update\nconst ALLOWED_UPDATE_OPERATIONS = {\n  $inc:1, $set:1, $unset:1, $addToSet:1, $pop:1, $pullAll:1, $pull:1,\n  $pushAll:1, $push:1, $bit:1\n};\n\n// Simulate a mongo `remove` operation while validating access control\n// rules. See #ValidatedChange\nCollectionPrototype._validatedRemoveAsync = async function(userId, selector) {\n  const self = this;\n\n  const findOptions = {transform: null};\n  if (!self._validators.fetchAllFields) {\n    findOptions.fields = {};\n    self._validators.fetch.forEach((fieldName) => {\n      findOptions.fields[fieldName] = 1;\n    });\n  }\n\n  const doc = await self._collection.findOneAsync(selector, findOptions);\n  if (!doc)\n    return 0;\n\n  // call user validators.\n  // Any deny returns true means denied.\n  if (await asyncSome(self._validators.remove.deny, async (validator) => {\n    const result = validator(userId, transformDoc(validator, doc));\n    return Meteor._isPromise(result) ? await result : result;\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n  // Any allow returns true means proceed. Throw error if they all fail.\n  if (await asyncEvery(self._validators.remove.allow, async (validator) => {\n    const result = validator(userId, transformDoc(validator, doc));\n    return !(Meteor._isPromise(result) ? await result : result);\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  // Back when we supported arbitrary client-provided selectors, we actually\n  // rewrote the selector to {_id: {$in: [ids that we found]}} before passing to\n  // Mongo to avoid races, but since selector is guaranteed to already just be\n  // an ID, we don't have to any more.\n\n  return self._collection.removeAsync.call(self._collection, selector);\n};\n\nCollectionPrototype._callMutatorMethodAsync = function _callMutatorMethodAsync(name, args, options = {}) {\n\n  // For two out of three mutator methods, the first argument is a selector\n  const firstArgIsSelector = name === \"updateAsync\" || name === \"removeAsync\";\n  if (firstArgIsSelector && !alreadyInSimulation()) {\n    // If we're about to actually send an RPC, we should throw an error if\n    // this is a non-ID selector, because the mutation methods only allow\n    // single-ID selectors. (If we don't throw here, we'll see flicker.)\n    throwIfSelectorIsNotId(args[0], name);\n  }\n\n  const mutatorMethodName = this._prefix + name;\n  return this._connection.applyAsync(mutatorMethodName, args, {\n    returnStubValue: this.resolverType === 'stub' || this.resolverType == null,\n    // StubStream is only used for testing where you don't care about the server\n    returnServerResultPromise: !this._connection._stream._isStub && this.resolverType !== 'stub',\n    ...options,\n  });\n}\n\nCollectionPrototype._callMutatorMethod = function _callMutatorMethod(name, args, callback) {\n  if (Meteor.isClient && !callback && !alreadyInSimulation()) {\n    // Client can't block, so it can't report errors by exception,\n    // only by callback. If they forget the callback, give them a\n    // default one that logs the error, so they aren't totally\n    // baffled if their writes don't work because their database is\n    // down.\n    // Don't give a default callback in simulation, because inside stubs we\n    // want to return the results from the local collection immediately and\n    // not force a callback.\n    callback = function (err) {\n      if (err)\n        Meteor._debug(name + \" failed\", err);\n    };\n  }\n\n  // For two out of three mutator methods, the first argument is a selector\n  const firstArgIsSelector = name === \"update\" || name === \"remove\";\n  if (firstArgIsSelector && !alreadyInSimulation()) {\n    // If we're about to actually send an RPC, we should throw an error if\n    // this is a non-ID selector, because the mutation methods only allow\n    // single-ID selectors. (If we don't throw here, we'll see flicker.)\n    throwIfSelectorIsNotId(args[0], name);\n  }\n\n  const mutatorMethodName = this._prefix + name;\n  return this._connection.apply(\n    mutatorMethodName, args, { returnStubValue: true }, callback);\n}\n\nfunction transformDoc(validator, doc) {\n  if (validator.transform)\n    return validator.transform(doc);\n  return doc;\n}\n\nfunction docToValidate(validator, doc, generatedId) {\n  let ret = doc;\n  if (validator.transform) {\n    ret = EJSON.clone(doc);\n    // If you set a server-side transform on your collection, then you don't get\n    // to tell the difference between \"client specified the ID\" and \"server\n    // generated the ID\", because transforms expect to get _id.  If you want to\n    // do that check, you can do it with a specific\n    // `C.allow({insertAsync: f, transform: null})` validator.\n    if (generatedId !== null) {\n      ret._id = generatedId;\n    }\n    ret = validator.transform(ret);\n  }\n  return ret;\n}\n\nfunction addValidator(collection, allowOrDeny, options) {\n  // validate keys\n  const validKeysRegEx = /^(?:insertAsync|updateAsync|removeAsync|insert|update|remove|fetch|transform)$/;\n  Object.keys(options).forEach((key) => {\n    if (!validKeysRegEx.test(key))\n      throw new Error(allowOrDeny + \": Invalid key: \" + key);\n\n    // TODO deprecated async config on future versions\n    const isAsyncKey = key.includes('Async');\n    if (isAsyncKey) {\n      const syncKey = key.replace('Async', '');\n      Meteor.deprecate(allowOrDeny + `: The \"${key}\" key is deprecated. Use \"${syncKey}\" instead.`);\n    }\n  });\n\n  collection._restricted = true;\n\n  [\n    'insertAsync',\n    'updateAsync',\n    'removeAsync',\n    'insert',\n    'update',\n    'remove',\n  ].forEach(name => {\n    if (hasOwn.call(options, name)) {\n      if (!(options[name] instanceof Function)) {\n        throw new Error(\n          allowOrDeny + ': Value for `' + name + '` must be a function'\n        );\n      }\n\n      // If the transform is specified at all (including as 'null') in this\n      // call, then take that; otherwise, take the transform from the\n      // collection.\n      if (options.transform === undefined) {\n        options[name].transform = collection._transform; // already wrapped\n      } else {\n        options[name].transform = LocalCollection.wrapTransform(\n          options.transform\n        );\n      }\n      const isAsyncName = name.includes('Async');\n      const validatorSyncName = isAsyncName ? name.replace('Async', '') : name;\n      collection._validators[validatorSyncName][allowOrDeny].push(options[name]);\n    }\n  });\n\n  // Only updateAsync the fetch fields if we're passed things that affect\n  // fetching. This way allow({}) and allow({insertAsync: f}) don't result in\n  // setting fetchAllFields\n  if (options.updateAsync || options.removeAsync || options.fetch) {\n    if (options.fetch && !(options.fetch instanceof Array)) {\n      throw new Error(allowOrDeny + \": Value for `fetch` must be an array\");\n    }\n    collection._updateFetch(options.fetch);\n  }\n}\n\nfunction throwIfSelectorIsNotId(selector, methodName) {\n  if (!LocalCollection._selectorIsIdPerhapsAsObject(selector)) {\n    throw new Meteor.Error(\n      403, \"Not permitted. Untrusted code may only \" + methodName +\n        \" documents by ID.\");\n  }\n};\n\n// Determine if we are in a DDP method simulation\nfunction alreadyInSimulation() {\n  var CurrentInvocation =\n    DDP._CurrentMethodInvocation ||\n    // For backwards compatibility, as explained in this issue:\n    // https://github.com/meteor/meteor/issues/8947\n    DDP._CurrentInvocation;\n\n  const enclosing = CurrentInvocation.get();\n  return enclosing && enclosing.isSimulation;\n}\n"]}