{"version": 3, "sources": ["meteor://💻app/packages/ddp-server/stream_server.js", "meteor://💻app/packages/ddp-server/livedata_server.js", "meteor://💻app/packages/ddp-server/writefence.js", "meteor://💻app/packages/ddp-server/crossbar.js", "meteor://💻app/packages/ddp-server/server_convenience.js", "meteor://💻app/packages/ddp-server/dummy_document_view.ts", "meteor://💻app/packages/ddp-server/session_collection_view.ts", "meteor://💻app/packages/ddp-server/session_document_view.ts"], "names": ["_objectSpread", "module", "link", "default", "v", "once", "zlib", "__reifyWaitForDeps__", "websocketExtensions", "extensions", "websocketCompressionConfig", "process", "env", "SERVER_WEBSOCKET_COMPRESSION", "JSON", "parse", "push", "Npm", "require", "configure", "threshold", "level", "constants", "Z_BEST_SPEED", "memLevel", "Z_MIN_MEMLEVEL", "noContextTakeover", "maxWindowBits", "Z_MIN_WINDOWBITS", "pathPrefix", "__meteor_runtime_config__", "ROOT_URL_PATH_PREFIX", "StreamServer", "self", "registration_callbacks", "open_sockets", "prefix", "RoutePolicy", "declare", "sockjs", "serverOptions", "log", "heartbeat_delay", "disconnect_delay", "disable_cors", "DISABLE_SOCKJS_CORS", "jsessionid", "USE_JSESSIONID", "DISABLE_WEBSOCKETS", "websocket", "faye_server_options", "server", "createServer", "WebApp", "httpServer", "removeListener", "_timeoutAdjustmentRequestCallback", "installHandlers", "addListener", "_redirectWebsocketEndpoint", "on", "socket", "setWebsocketTimeout", "timeout", "protocol", "_session", "recv", "connection", "setTimeout", "send", "data", "write", "filter", "value", "TEST_METADATA", "stringify", "testMessageOnConnect", "for<PERSON>ach", "callback", "Object", "assign", "prototype", "register", "all_sockets", "values", "event", "oldHttpServerListeners", "listeners", "slice", "removeAllListeners", "newListener", "request", "args", "arguments", "url", "parsedUrl", "pathname", "format", "old<PERSON><PERSON><PERSON>", "apply", "__reify_async_result__", "_reifyError", "async", "isEmpty", "isObject", "isString", "SessionCollectionView", "SessionDocumentView", "DDPServer", "publicationStrategies", "SERVER_MERGE", "useDummyDocumentView", "useCollectionView", "doAccountingForCollection", "NO_MERGE_NO_HISTORY", "NO_MERGE", "NO_MERGE_MULTI", "_SessionDocumentView", "_getCurrentFence", "currentInvocation", "_CurrentWriteFence", "get", "DDP", "_CurrentMethodInvocation", "fence", "undefined", "_SessionCollectionView", "Session", "version", "options", "id", "Random", "initialized", "inQueue", "Meteor", "_DoubleEndedQueue", "blocked", "workerRunning", "cachedUnblock", "_namedSubs", "Map", "_universalSubs", "userId", "collectionViews", "_isSending", "_dontStartNewUniversalSubs", "_pendingReady", "_closeCallbacks", "_socketUrl", "_respondToPings", "respondToPings", "connectionHandle", "close", "onClose", "fn", "cb", "bindEnvironment", "defer", "clientAddress", "_clientAddress", "httpHeaders", "headers", "msg", "session", "startUniversalSubs", "heartbeatInterval", "heartbeat", "DDPCommon", "Heartbeat", "heartbeatTimeout", "onTimeout", "sendPing", "start", "Package", "Facts", "incrementServerFact", "sendReady", "subscriptionIds", "subs", "subscriptionId", "_canSend", "collectionName", "getPublicationStrategy", "sendAdded", "fields", "collection", "send<PERSON><PERSON>ed", "sendRemoved", "getSendCallbacks", "added", "bind", "changed", "removed", "getCollectionView", "ret", "set", "subscriptionHandle", "view", "delete", "handlers", "universal_publish_handlers", "handler", "_startSubscription", "stop", "_meteorSession", "_deactivateAllSubscriptions", "_removeSession", "_printSentDDP", "_debug", "stringifyDDP", "sendError", "reason", "offendingMessage", "processMessage", "msg_in", "messageReceived", "processNext", "shift", "runHandlers", "unblock", "setImmediate", "onMessageHook", "each", "protocol_handlers", "result", "call", "_isPromise", "finally", "sub", "name", "params", "Array", "publish_handlers", "error", "Error", "concat", "has", "DDPRateLimiter", "rateLimiterInput", "type", "connectionId", "_increment", "rateLimitResult", "_check", "allowed", "getErrorMessage", "timeToReset", "unsub", "_stopSubscription", "method", "randomSeed", "_WriteFence", "onAllCommitted", "retire", "methods", "method_handlers", "arm", "invocation", "MethodInvocation", "isSimulation", "setUserId", "_setUserId", "promise", "Promise", "resolve", "reject", "with<PERSON><PERSON><PERSON>", "maybeAuditArgumentChecks", "finish", "payload", "then", "exception", "wrapInternalException", "_eachSub", "f", "_diffCollectionViews", "beforeCVs", "DiffSequence", "diffMaps", "both", "leftValue", "rightValue", "diff", "rightOnly", "documents", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getFields", "leftOnly", "doc", "_deactivate", "oldNamedSubs", "all", "map", "_ref", "newSub", "_recreate", "_run<PERSON><PERSON><PERSON>", "_noYieldsAllowed", "subId", "Subscription", "unblockHander", "subName", "maybeSub", "_name", "_removeAllDocuments", "response", "httpForwardedCount", "parseInt", "remoteAddress", "forwardedFor", "split", "length", "ip", "trim", "_handler", "_subscriptionId", "_params", "_subscriptionHandle", "_deactivated", "_stopCallbacks", "_documents", "_ready", "_idFilter", "idStringify", "MongoID", "idParse", "resultOrThenable", "_CurrentPublicationInvocation", "EJSON", "clone", "e", "_isDeactivated", "isThenable", "_publishHandlerResult", "res", "isCursor", "c", "_publishCursor", "ready", "isArray", "every", "collectionNames", "i", "_getCollectionName", "cur", "_callStopCallbacks", "callbacks", "collectionDocs", "strId", "onStop", "ids", "Set", "add", "Server", "defaultPublicationStrategy", "onConnectionHook", "Hook", "debugPrintExceptions", "_publicationStrategies", "sessions", "stream_server", "raw_msg", "_printReceivedDDP", "parseDDP", "err", "_handleConnect", "onConnection", "setPublicationStrategy", "strategy", "includes", "onMessage", "support", "SUPPORTED_DDP_VERSIONS", "calculateVersion", "publish", "autopublish", "is_auto", "warned_about_autopublish", "entries", "_ref2", "key", "isAsyncCall", "_isCallAsyncMethodRunning", "_ref3", "func", "_len", "_key", "pop", "callAsync", "_args$", "_len2", "_key2", "hasOwnProperty", "_setCallAsyncMethodRunning", "_CurrentCallAsyncInvocation", "_set", "hasCallAsyncParent", "applyAsync", "isFromCallAsync", "catch", "currentMethodInvocation", "currentPublicationInvocation", "makeRpcSeed", "r", "_urlForSession", "sessionId", "clientSupportedVersions", "serverSupportedVersions", "correctVersion", "find", "_calculateVersion", "context", "isClientSafe", "originalMessage", "message", "details", "_expectedByTest", "stack", "sanitizedError", "description", "Match", "_failIfArgumentsAreNotAllChecked", "constructor", "armed", "fired", "retired", "outstanding_writes", "before_fire_callbacks", "completion_callbacks", "beginWrite", "committed", "_maybe<PERSON>ire", "onBeforeFire", "_armAndWait", "resolver", "returnValue", "armAndWait", "invokeCallback", "beforeCallbacks", "EnvironmentVariable", "_Crossbar", "nextId", "listenersByCollection", "listenersByCollectionCount", "factPackage", "factName", "_collectionForMessage", "listen", "trigger", "record", "fire", "notification", "listenersForCollection", "callbackIds", "l", "_matches", "ObjectID", "equals", "keys", "_InvalidationCrossbar", "DDP_DEFAULT_CONNECTION_URL", "refresh", "export", "DummyDocumentView", "existsIn", "dataByKey", "clearField", "changeCollector", "changeField", "isAdd", "sessionCallbacks", "size", "previous", "diffDocument", "nowDV", "prevDV", "diffObjects", "prev", "now", "changedResult", "precedenceList", "removedValue", "precedence", "splice", "elt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAIC,IAAI;IAACJ,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACC,IAAI,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,IAAI;IAACL,MAAM,CAACC,IAAI,CAAC,WAAW,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACE,IAAI,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAGhL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,mBAAmB,GAAGH,IAAI,CAAC,YAAY;MACzC,IAAII,UAAU,GAAG,EAAE;MAEnB,IAAIC,0BAA0B,GAAGC,OAAO,CAACC,GAAG,CAACC,4BAA4B,GACvEC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAACC,GAAG,CAACC,4BAA4B,CAAC,GAAG,CAAC,CAAC;MAE3D,IAAIH,0BAA0B,EAAE;QAC9BD,UAAU,CAACO,IAAI,CAACC,GAAG,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAACC,SAAS,CAAAnB,aAAA;UAC1DoB,SAAS,EAAE,IAAI;UACfC,KAAK,EAAEf,IAAI,CAACgB,SAAS,CAACC,YAAY;UAClCC,QAAQ,EAAElB,IAAI,CAACgB,SAAS,CAACG,cAAc;UACvCC,iBAAiB,EAAE,IAAI;UACvBC,aAAa,EAAErB,IAAI,CAACgB,SAAS,CAACM;QAAgB,GAC1ClB,0BAA0B,IAAI,CAAC,CAAC,CACrC,CAAC,CAAC;MACL;MAEA,OAAOD,UAAU;IACnB,CAAC,CAAC;IAEF,IAAIoB,UAAU,GAAGC,yBAAyB,CAACC,oBAAoB,IAAK,EAAE;IAEtEC,YAAY,GAAG,SAAAA,CAAA,EAAY;MACzB,IAAIC,IAAI,GAAG,IAAI;MACfA,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAChCD,IAAI,CAACE,YAAY,GAAG,EAAE;;MAEtB;MACA;MACAF,IAAI,CAACG,MAAM,GAAGP,UAAU,GAAG,SAAS;MACpCQ,WAAW,CAACC,OAAO,CAACL,IAAI,CAACG,MAAM,GAAG,GAAG,EAAE,SAAS,CAAC;;MAEjD;MACA,IAAIG,MAAM,GAAGtB,GAAG,CAACC,OAAO,CAAC,QAAQ,CAAC;MAClC,IAAIsB,aAAa,GAAG;QAClBJ,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBK,GAAG,EAAE,SAAAA,CAAA,EAAW,CAAC,CAAC;QAClB;QACA;QACAC,eAAe,EAAE,KAAK;QACtB;QACA;QACA;QACA;QACA;QACA;QACAC,gBAAgB,EAAE,EAAE,GAAG,IAAI;QAC3B;QACA;QACAC,YAAY,EAAE,CAAC,CAACjC,OAAO,CAACC,GAAG,CAACiC,mBAAmB;QAC/C;QACA;QACA;QACAC,UAAU,EAAE,CAAC,CAACnC,OAAO,CAACC,GAAG,CAACmC;MAC5B,CAAC;;MAED;MACA;MACA;MACA;MACA,IAAIpC,OAAO,CAACC,GAAG,CAACoC,kBAAkB,EAAE;QAClCR,aAAa,CAACS,SAAS,GAAG,KAAK;MACjC,CAAC,MAAM;QACLT,aAAa,CAACU,mBAAmB,GAAG;UAClCzC,UAAU,EAAED,mBAAmB,CAAC;QAClC,CAAC;MACH;MAEAyB,IAAI,CAACkB,MAAM,GAAGZ,MAAM,CAACa,YAAY,CAACZ,aAAa,CAAC;;MAEhD;MACA;MACA;MACA;MACAa,MAAM,CAACC,UAAU,CAACC,cAAc,CAC9B,SAAS,EAAEF,MAAM,CAACG,iCAAiC,CAAC;MACtDvB,IAAI,CAACkB,MAAM,CAACM,eAAe,CAACJ,MAAM,CAACC,UAAU,CAAC;MAC9CD,MAAM,CAACC,UAAU,CAACI,WAAW,CAC3B,SAAS,EAAEL,MAAM,CAACG,iCAAiC,CAAC;;MAEtD;MACAvB,IAAI,CAAC0B,0BAA0B,CAAC,CAAC;MAEjC1B,IAAI,CAACkB,MAAM,CAACS,EAAE,CAAC,YAAY,EAAE,UAAUC,MAAM,EAAE;QAC7C;QACA;QACA;QACA;QACA,IAAI,CAACA,MAAM,EAAE;;QAEb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAA,MAAM,CAACC,mBAAmB,GAAG,UAAUC,OAAO,EAAE;UAC9C,IAAI,CAACF,MAAM,CAACG,QAAQ,KAAK,WAAW,IAC/BH,MAAM,CAACG,QAAQ,KAAK,eAAe,KACjCH,MAAM,CAACI,QAAQ,CAACC,IAAI,EAAE;YAC3BL,MAAM,CAACI,QAAQ,CAACC,IAAI,CAACC,UAAU,CAACC,UAAU,CAACL,OAAO,CAAC;UACrD;QACF,CAAC;QACDF,MAAM,CAACC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC;QAErCD,MAAM,CAACQ,IAAI,GAAG,UAAUC,IAAI,EAAE;UAC5BT,MAAM,CAACU,KAAK,CAACD,IAAI,CAAC;QACpB,CAAC;QACDT,MAAM,CAACD,EAAE,CAAC,OAAO,EAAE,YAAY;UAC7B3B,IAAI,CAACE,YAAY,GAAGF,IAAI,CAACE,YAAY,CAACqC,MAAM,CAAC,UAASC,KAAK,EAAE;YAC3D,OAAOA,KAAK,KAAKZ,MAAM;UACzB,CAAC,CAAC;QACJ,CAAC,CAAC;QACF5B,IAAI,CAACE,YAAY,CAACnB,IAAI,CAAC6C,MAAM,CAAC;;QAE9B;QACA;QACA,IAAIlD,OAAO,CAACC,GAAG,CAAC8D,aAAa,IAAI/D,OAAO,CAACC,GAAG,CAAC8D,aAAa,KAAK,IAAI,EAAE;UACnEb,MAAM,CAACQ,IAAI,CAACvD,IAAI,CAAC6D,SAAS,CAAC;YAAEC,oBAAoB,EAAE;UAAK,CAAC,CAAC,CAAC;QAC7D;;QAEA;QACA;QACA3C,IAAI,CAACC,sBAAsB,CAAC2C,OAAO,CAAC,UAAUC,QAAQ,EAAE;UACtDA,QAAQ,CAACjB,MAAM,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IAEJ,CAAC;IAEDkB,MAAM,CAACC,MAAM,CAAChD,YAAY,CAACiD,SAAS,EAAE;MACpC;MACA;MACAC,QAAQ,EAAE,SAAAA,CAAUJ,QAAQ,EAAE;QAC5B,IAAI7C,IAAI,GAAG,IAAI;QACfA,IAAI,CAACC,sBAAsB,CAAClB,IAAI,CAAC8D,QAAQ,CAAC;QAC1C7C,IAAI,CAACkD,WAAW,CAAC,CAAC,CAACN,OAAO,CAAC,UAAUhB,MAAM,EAAE;UAC3CiB,QAAQ,CAACjB,MAAM,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC;MAED;MACAsB,WAAW,EAAE,SAAAA,CAAA,EAAY;QACvB,IAAIlD,IAAI,GAAG,IAAI;QACf,OAAO8C,MAAM,CAACK,MAAM,CAACnD,IAAI,CAACE,YAAY,CAAC;MACzC,CAAC;MAED;MACA;MACAwB,0BAA0B,EAAE,SAAAA,CAAA,EAAW;QACrC,IAAI1B,IAAI,GAAG,IAAI;QACf;QACA;QACA;QACA;QACA;QACA,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC4C,OAAO,CAAEQ,KAAK,IAAK;UACxC,IAAI/B,UAAU,GAAGD,MAAM,CAACC,UAAU;UAClC,IAAIgC,sBAAsB,GAAGhC,UAAU,CAACiC,SAAS,CAACF,KAAK,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;UACjElC,UAAU,CAACmC,kBAAkB,CAACJ,KAAK,CAAC;;UAEpC;UACA;UACA,IAAIK,WAAW,GAAG,SAAAA,CAASC,OAAO,CAAC,sBAAsB;YACvD;YACA,IAAIC,IAAI,GAAGC,SAAS;;YAEpB;YACA,IAAIC,GAAG,GAAG7E,GAAG,CAACC,OAAO,CAAC,KAAK,CAAC;;YAE5B;YACA;YACA,IAAI6E,SAAS,GAAGD,GAAG,CAAC/E,KAAK,CAAC4E,OAAO,CAACG,GAAG,CAAC;YACtC,IAAIC,SAAS,CAACC,QAAQ,KAAKnE,UAAU,GAAG,YAAY,IAChDkE,SAAS,CAACC,QAAQ,KAAKnE,UAAU,GAAG,aAAa,EAAE;cACrDkE,SAAS,CAACC,QAAQ,GAAG/D,IAAI,CAACG,MAAM,GAAG,YAAY;cAC/CuD,OAAO,CAACG,GAAG,GAAGA,GAAG,CAACG,MAAM,CAACF,SAAS,CAAC;YACrC;YACAT,sBAAsB,CAACT,OAAO,CAAC,UAASqB,WAAW,EAAE;cACnDA,WAAW,CAACC,KAAK,CAAC7C,UAAU,EAAEsC,IAAI,CAAC;YACrC,CAAC,CAAC;UACJ,CAAC;UACDtC,UAAU,CAACI,WAAW,CAAC2B,KAAK,EAAEK,WAAW,CAAC;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAACU,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAnE,IAAA;EAAAqE,KAAA;AAAA,G;;;;;;;;;;;;;;IC3MH,IAAItG,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAImG,OAAO;IAACtG,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACmG,OAAO,GAACnG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIoG,QAAQ;IAACvG,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACoG,QAAQ,GAACpG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIqG,QAAQ;IAACxG,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACqG,QAAQ,GAACrG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIsG,qBAAqB;IAACzG,MAAM,CAACC,IAAI,CAAC,2BAA2B,EAAC;MAACwG,qBAAqBA,CAACtG,CAAC,EAAC;QAACsG,qBAAqB,GAACtG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIuG,mBAAmB;IAAC1G,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;MAACyG,mBAAmBA,CAACvG,CAAC,EAAC;QAACuG,mBAAmB,GAACvG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAMxfqG,SAAS,GAAG,CAAC,CAAC;;IAGd;IACA;IACA;IACA;IACA,MAAMC,qBAAqB,GAAG;MAC5B;MACA;MACA;MACAC,YAAY,EAAE;QACZC,oBAAoB,EAAE,KAAK;QAC3BC,iBAAiB,EAAE,IAAI;QACvBC,yBAAyB,EAAE;MAC7B,CAAC;MACD;MACA;MACA;MACA;MACAC,mBAAmB,EAAE;QACnBH,oBAAoB,EAAE,KAAK;QAC3BC,iBAAiB,EAAE,KAAK;QACxBC,yBAAyB,EAAE;MAC7B,CAAC;MACD;MACA;MACA;MACAE,QAAQ,EAAE;QACRJ,oBAAoB,EAAE,KAAK;QAC3BC,iBAAiB,EAAE,KAAK;QACxBC,yBAAyB,EAAE;MAC7B,CAAC;MACD;MACA;MACA;MACAG,cAAc,EAAE;QACdL,oBAAoB,EAAE,IAAI;QAC1BC,iBAAiB,EAAE,IAAI;QACvBC,yBAAyB,EAAE;MAC7B;IACF,CAAC;IAEDL,SAAS,CAACC,qBAAqB,GAAGA,qBAAqB;;IAEvD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAGAD,SAAS,CAACS,oBAAoB,GAAGV,mBAAmB;IAEpDC,SAAS,CAACU,gBAAgB,GAAG,YAAY;MACvC,IAAIC,iBAAiB,GAAG,IAAI,CAACC,kBAAkB,CAACC,GAAG,CAAC,CAAC;MACrD,IAAIF,iBAAiB,EAAE;QACrB,OAAOA,iBAAiB;MAC1B;MACAA,iBAAiB,GAAGG,GAAG,CAACC,wBAAwB,CAACF,GAAG,CAAC,CAAC;MACtD,OAAOF,iBAAiB,GAAGA,iBAAiB,CAACK,KAAK,GAAGC,SAAS;IAChE,CAAC;IAGDjB,SAAS,CAACkB,sBAAsB,GAAGpB,qBAAqB;;IAExD;IACA;IACA;;IAEA,IAAIqB,OAAO,GAAG,SAAAA,CAAU5E,MAAM,EAAE6E,OAAO,EAAEnE,MAAM,EAAEoE,OAAO,EAAE;MACxD,IAAIhG,IAAI,GAAG,IAAI;MACfA,IAAI,CAACiG,EAAE,GAAGC,MAAM,CAACD,EAAE,CAAC,CAAC;MAErBjG,IAAI,CAACkB,MAAM,GAAGA,MAAM;MACpBlB,IAAI,CAAC+F,OAAO,GAAGA,OAAO;MAEtB/F,IAAI,CAACmG,WAAW,GAAG,KAAK;MACxBnG,IAAI,CAAC4B,MAAM,GAAGA,MAAM;;MAEpB;MACA;MACA5B,IAAI,CAACoG,OAAO,GAAG,IAAIC,MAAM,CAACC,iBAAiB,CAAC,CAAC;MAE7CtG,IAAI,CAACuG,OAAO,GAAG,KAAK;MACpBvG,IAAI,CAACwG,aAAa,GAAG,KAAK;MAE1BxG,IAAI,CAACyG,aAAa,GAAG,IAAI;;MAEzB;MACAzG,IAAI,CAAC0G,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC3B3G,IAAI,CAAC4G,cAAc,GAAG,EAAE;MAExB5G,IAAI,CAAC6G,MAAM,GAAG,IAAI;MAElB7G,IAAI,CAAC8G,eAAe,GAAG,IAAIH,GAAG,CAAC,CAAC;;MAEhC;MACA;MACA;MACA3G,IAAI,CAAC+G,UAAU,GAAG,IAAI;;MAEtB;MACA;MACA/G,IAAI,CAACgH,0BAA0B,GAAG,KAAK;;MAEvC;MACA;MACAhH,IAAI,CAACiH,aAAa,GAAG,EAAE;;MAEvB;MACAjH,IAAI,CAACkH,eAAe,GAAG,EAAE;;MAGzB;MACA;MACAlH,IAAI,CAACmH,UAAU,GAAGvF,MAAM,CAACiC,GAAG;;MAE5B;MACA7D,IAAI,CAACoH,eAAe,GAAGpB,OAAO,CAACqB,cAAc;;MAE7C;MACA;MACA;MACArH,IAAI,CAACsH,gBAAgB,GAAG;QACtBrB,EAAE,EAAEjG,IAAI,CAACiG,EAAE;QACXsB,KAAK,EAAE,SAAAA,CAAA,EAAY;UACjBvH,IAAI,CAACuH,KAAK,CAAC,CAAC;QACd,CAAC;QACDC,OAAO,EAAE,SAAAA,CAAUC,EAAE,EAAE;UACrB,IAAIC,EAAE,GAAGrB,MAAM,CAACsB,eAAe,CAACF,EAAE,EAAE,6BAA6B,CAAC;UAClE,IAAIzH,IAAI,CAACoG,OAAO,EAAE;YAChBpG,IAAI,CAACkH,eAAe,CAACnI,IAAI,CAAC2I,EAAE,CAAC;UAC/B,CAAC,MAAM;YACL;YACArB,MAAM,CAACuB,KAAK,CAACF,EAAE,CAAC;UAClB;QACF,CAAC;QACDG,aAAa,EAAE7H,IAAI,CAAC8H,cAAc,CAAC,CAAC;QACpCC,WAAW,EAAE/H,IAAI,CAAC4B,MAAM,CAACoG;MAC3B,CAAC;MAEDhI,IAAI,CAACoC,IAAI,CAAC;QAAE6F,GAAG,EAAE,WAAW;QAAEC,OAAO,EAAElI,IAAI,CAACiG;MAAG,CAAC,CAAC;;MAEjD;MACAjG,IAAI,CAACmI,kBAAkB,CAAC,CAAC;MAEzB,IAAIpC,OAAO,KAAK,MAAM,IAAIC,OAAO,CAACoC,iBAAiB,KAAK,CAAC,EAAE;QACzD;QACAxG,MAAM,CAACC,mBAAmB,CAAC,CAAC,CAAC;QAE7B7B,IAAI,CAACqI,SAAS,GAAG,IAAIC,SAAS,CAACC,SAAS,CAAC;UACvCH,iBAAiB,EAAEpC,OAAO,CAACoC,iBAAiB;UAC5CI,gBAAgB,EAAExC,OAAO,CAACwC,gBAAgB;UAC1CC,SAAS,EAAE,SAAAA,CAAA,EAAY;YACrBzI,IAAI,CAACuH,KAAK,CAAC,CAAC;UACd,CAAC;UACDmB,QAAQ,EAAE,SAAAA,CAAA,EAAY;YACpB1I,IAAI,CAACoC,IAAI,CAAC;cAAC6F,GAAG,EAAE;YAAM,CAAC,CAAC;UAC1B;QACF,CAAC,CAAC;QACFjI,IAAI,CAACqI,SAAS,CAACM,KAAK,CAAC,CAAC;MACxB;MAEAC,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;IAC9B,CAAC;IAEDhG,MAAM,CAACC,MAAM,CAAC+C,OAAO,CAAC9C,SAAS,EAAE;MAC/B+F,SAAS,EAAE,SAAAA,CAAUC,eAAe,EAAE;QACpC,IAAIhJ,IAAI,GAAG,IAAI;QACf,IAAIA,IAAI,CAAC+G,UAAU,EAAE;UACnB/G,IAAI,CAACoC,IAAI,CAAC;YAAC6F,GAAG,EAAE,OAAO;YAAEgB,IAAI,EAAED;UAAe,CAAC,CAAC;QAClD,CAAC,MAAM;UACLA,eAAe,CAACpG,OAAO,CAAC,UAAUsG,cAAc,EAAE;YAChDlJ,IAAI,CAACiH,aAAa,CAAClI,IAAI,CAACmK,cAAc,CAAC;UACzC,CAAC,CAAC;QACJ;MACF,CAAC;MAEDC,QAAQA,CAACC,cAAc,EAAE;QACvB,OAAO,IAAI,CAACrC,UAAU,IAAI,CAAC,IAAI,CAAC7F,MAAM,CAACmI,sBAAsB,CAACD,cAAc,CAAC,CAACrE,iBAAiB;MACjG,CAAC;MAGDuE,SAASA,CAACF,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,EAAE;QACpC,IAAI,IAAI,CAACJ,QAAQ,CAACC,cAAc,CAAC,EAAE;UACjC,IAAI,CAAChH,IAAI,CAAC;YAAE6F,GAAG,EAAE,OAAO;YAAEuB,UAAU,EAAEJ,cAAc;YAAEnD,EAAE;YAAEsD;UAAO,CAAC,CAAC;QACrE;MACF,CAAC;MAEDE,WAAWA,CAACL,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,EAAE;QACtC,IAAIjF,OAAO,CAACiF,MAAM,CAAC,EACjB;QAEF,IAAI,IAAI,CAACJ,QAAQ,CAACC,cAAc,CAAC,EAAE;UACjC,IAAI,CAAChH,IAAI,CAAC;YACR6F,GAAG,EAAE,SAAS;YACduB,UAAU,EAAEJ,cAAc;YAC1BnD,EAAE;YACFsD;UACF,CAAC,CAAC;QACJ;MACF,CAAC;MAEDG,WAAWA,CAACN,cAAc,EAAEnD,EAAE,EAAE;QAC9B,IAAI,IAAI,CAACkD,QAAQ,CAACC,cAAc,CAAC,EAAE;UACjC,IAAI,CAAChH,IAAI,CAAC;YAAC6F,GAAG,EAAE,SAAS;YAAEuB,UAAU,EAAEJ,cAAc;YAAEnD;UAAE,CAAC,CAAC;QAC7D;MACF,CAAC;MAED0D,gBAAgB,EAAE,SAAAA,CAAA,EAAY;QAC5B,IAAI3J,IAAI,GAAG,IAAI;QACf,OAAO;UACL4J,KAAK,EAAE5J,IAAI,CAACsJ,SAAS,CAACO,IAAI,CAAC7J,IAAI,CAAC;UAChC8J,OAAO,EAAE9J,IAAI,CAACyJ,WAAW,CAACI,IAAI,CAAC7J,IAAI,CAAC;UACpC+J,OAAO,EAAE/J,IAAI,CAAC0J,WAAW,CAACG,IAAI,CAAC7J,IAAI;QACrC,CAAC;MACH,CAAC;MAEDgK,iBAAiB,EAAE,SAAAA,CAAUZ,cAAc,EAAE;QAC3C,IAAIpJ,IAAI,GAAG,IAAI;QACf,IAAIiK,GAAG,GAAGjK,IAAI,CAAC8G,eAAe,CAACtB,GAAG,CAAC4D,cAAc,CAAC;QAClD,IAAI,CAACa,GAAG,EAAE;UACRA,GAAG,GAAG,IAAIxF,qBAAqB,CAAC2E,cAAc,EACZpJ,IAAI,CAAC2J,gBAAgB,CAAC,CAAC,CAAC;UAC1D3J,IAAI,CAAC8G,eAAe,CAACoD,GAAG,CAACd,cAAc,EAAEa,GAAG,CAAC;QAC/C;QACA,OAAOA,GAAG;MACZ,CAAC;MAEDL,KAAKA,CAACO,kBAAkB,EAAEf,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,EAAE;QACpD,IAAI,IAAI,CAACrI,MAAM,CAACmI,sBAAsB,CAACD,cAAc,CAAC,CAACrE,iBAAiB,EAAE;UACxE,MAAMqF,IAAI,GAAG,IAAI,CAACJ,iBAAiB,CAACZ,cAAc,CAAC;UACnDgB,IAAI,CAACR,KAAK,CAACO,kBAAkB,EAAElE,EAAE,EAAEsD,MAAM,CAAC;QAC5C,CAAC,MAAM;UACL,IAAI,CAACD,SAAS,CAACF,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,CAAC;QAC5C;MACF,CAAC;MAEDQ,OAAOA,CAACI,kBAAkB,EAAEf,cAAc,EAAEnD,EAAE,EAAE;QAC9C,IAAI,IAAI,CAAC/E,MAAM,CAACmI,sBAAsB,CAACD,cAAc,CAAC,CAACrE,iBAAiB,EAAE;UACxE,MAAMqF,IAAI,GAAG,IAAI,CAACJ,iBAAiB,CAACZ,cAAc,CAAC;UACnDgB,IAAI,CAACL,OAAO,CAACI,kBAAkB,EAAElE,EAAE,CAAC;UACpC,IAAImE,IAAI,CAAC9F,OAAO,CAAC,CAAC,EAAE;YACjB,IAAI,CAACwC,eAAe,CAACuD,MAAM,CAACjB,cAAc,CAAC;UAC9C;QACF,CAAC,MAAM;UACL,IAAI,CAACM,WAAW,CAACN,cAAc,EAAEnD,EAAE,CAAC;QACtC;MACF,CAAC;MAED6D,OAAOA,CAACK,kBAAkB,EAAEf,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,EAAE;QACtD,IAAI,IAAI,CAACrI,MAAM,CAACmI,sBAAsB,CAACD,cAAc,CAAC,CAACrE,iBAAiB,EAAE;UACxE,MAAMqF,IAAI,GAAG,IAAI,CAACJ,iBAAiB,CAACZ,cAAc,CAAC;UACnDgB,IAAI,CAACN,OAAO,CAACK,kBAAkB,EAAElE,EAAE,EAAEsD,MAAM,CAAC;QAC9C,CAAC,MAAM;UACL,IAAI,CAACE,WAAW,CAACL,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,CAAC;QAC9C;MACF,CAAC;MAEDpB,kBAAkB,EAAE,SAAAA,CAAA,EAAY;QAC9B,IAAInI,IAAI,GAAG,IAAI;QACf;QACA;QACA;QACA,IAAIsK,QAAQ,GAAG,CAAC,GAAGtK,IAAI,CAACkB,MAAM,CAACqJ,0BAA0B,CAAC;QAC1DD,QAAQ,CAAC1H,OAAO,CAAC,UAAU4H,OAAO,EAAE;UAClCxK,IAAI,CAACyK,kBAAkB,CAACD,OAAO,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC;MAED;MACAjD,KAAK,EAAE,SAAAA,CAAA,EAAY;QACjB,IAAIvH,IAAI,GAAG,IAAI;;QAEf;QACA;QACA;;QAEA;QACA,IAAI,CAAEA,IAAI,CAACoG,OAAO,EAChB;;QAEF;QACApG,IAAI,CAACoG,OAAO,GAAG,IAAI;QACnBpG,IAAI,CAAC8G,eAAe,GAAG,IAAIH,GAAG,CAAC,CAAC;QAEhC,IAAI3G,IAAI,CAACqI,SAAS,EAAE;UAClBrI,IAAI,CAACqI,SAAS,CAACqC,IAAI,CAAC,CAAC;UACrB1K,IAAI,CAACqI,SAAS,GAAG,IAAI;QACvB;QAEA,IAAIrI,IAAI,CAAC4B,MAAM,EAAE;UACf5B,IAAI,CAAC4B,MAAM,CAAC2F,KAAK,CAAC,CAAC;UACnBvH,IAAI,CAAC4B,MAAM,CAAC+I,cAAc,GAAG,IAAI;QACnC;QAEA/B,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;QAE7BzC,MAAM,CAACuB,KAAK,CAAC,YAAY;UACvB;UACA;UACA;UACA5H,IAAI,CAAC4K,2BAA2B,CAAC,CAAC;;UAElC;UACA;UACA5K,IAAI,CAACkH,eAAe,CAACtE,OAAO,CAAC,UAAUC,QAAQ,EAAE;YAC/CA,QAAQ,CAAC,CAAC;UACZ,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA7C,IAAI,CAACkB,MAAM,CAAC2J,cAAc,CAAC7K,IAAI,CAAC;MAClC,CAAC;MAED;MACA;MACAoC,IAAI,EAAE,SAAAA,CAAU6F,GAAG,EAAE;QACnB,MAAMjI,IAAI,GAAG,IAAI;QACjB,IAAIA,IAAI,CAAC4B,MAAM,EAAE;UACf,IAAIyE,MAAM,CAACyE,aAAa,EACtBzE,MAAM,CAAC0E,MAAM,CAAC,UAAU,EAAEzC,SAAS,CAAC0C,YAAY,CAAC/C,GAAG,CAAC,CAAC;UACxDjI,IAAI,CAAC4B,MAAM,CAACQ,IAAI,CAACkG,SAAS,CAAC0C,YAAY,CAAC/C,GAAG,CAAC,CAAC;QAC/C;MACF,CAAC;MAED;MACAgD,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAEC,gBAAgB,EAAE;QAC7C,IAAInL,IAAI,GAAG,IAAI;QACf,IAAIiI,GAAG,GAAG;UAACA,GAAG,EAAE,OAAO;UAAEiD,MAAM,EAAEA;QAAM,CAAC;QACxC,IAAIC,gBAAgB,EAClBlD,GAAG,CAACkD,gBAAgB,GAAGA,gBAAgB;QACzCnL,IAAI,CAACoC,IAAI,CAAC6F,GAAG,CAAC;MAChB,CAAC;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAmD,cAAc,EAAE,SAAAA,CAAUC,MAAM,EAAE;QAChC,IAAIrL,IAAI,GAAG,IAAI;QACf,IAAI,CAACA,IAAI,CAACoG,OAAO;UAAE;UACjB;;QAEF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIpG,IAAI,CAACqI,SAAS,EAAE;UAClBrI,IAAI,CAACqI,SAAS,CAACiD,eAAe,CAAC,CAAC;QAClC;QAAC;QAED,IAAItL,IAAI,CAAC+F,OAAO,KAAK,MAAM,IAAIsF,MAAM,CAACpD,GAAG,KAAK,MAAM,EAAE;UACpD,IAAIjI,IAAI,CAACoH,eAAe,EACtBpH,IAAI,CAACoC,IAAI,CAAC;YAAC6F,GAAG,EAAE,MAAM;YAAEhC,EAAE,EAAEoF,MAAM,CAACpF;UAAE,CAAC,CAAC;UACzC;QACF;QACA,IAAIjG,IAAI,CAAC+F,OAAO,KAAK,MAAM,IAAIsF,MAAM,CAACpD,GAAG,KAAK,MAAM,EAAE;UACpD;UACA;QACF;QAEAjI,IAAI,CAACoG,OAAO,CAACrH,IAAI,CAACsM,MAAM,CAAC;QACzB,IAAIrL,IAAI,CAACwG,aAAa,EACpB;QACFxG,IAAI,CAACwG,aAAa,GAAG,IAAI;QAEzB,IAAI+E,WAAW,GAAG,SAAAA,CAAA,EAAY;UAC5B,IAAItD,GAAG,GAAGjI,IAAI,CAACoG,OAAO,IAAIpG,IAAI,CAACoG,OAAO,CAACoF,KAAK,CAAC,CAAC;UAE9C,IAAI,CAACvD,GAAG,EAAE;YACRjI,IAAI,CAACwG,aAAa,GAAG,KAAK;YAC1B;UACF;UAEA,SAASiF,WAAWA,CAAA,EAAG;YACrB,IAAIlF,OAAO,GAAG,IAAI;YAElB,IAAImF,OAAO,GAAG,SAAAA,CAAA,EAAY;cACxB,IAAI,CAACnF,OAAO,EACV,OAAO,CAAC;cACVA,OAAO,GAAG,KAAK;cACfoF,YAAY,CAACJ,WAAW,CAAC;YAC3B,CAAC;YAEDvL,IAAI,CAACkB,MAAM,CAAC0K,aAAa,CAACC,IAAI,CAAC,UAAUhJ,QAAQ,EAAE;cACjDA,QAAQ,CAACoF,GAAG,EAAEjI,IAAI,CAAC;cACnB,OAAO,IAAI;YACb,CAAC,CAAC;YAEF,IAAIiI,GAAG,CAACA,GAAG,IAAIjI,IAAI,CAAC8L,iBAAiB,EAAE;cACrC,MAAMC,MAAM,GAAG/L,IAAI,CAAC8L,iBAAiB,CAAC7D,GAAG,CAACA,GAAG,CAAC,CAAC+D,IAAI,CACjDhM,IAAI,EACJiI,GAAG,EACHyD,OACF,CAAC;cAED,IAAIrF,MAAM,CAAC4F,UAAU,CAACF,MAAM,CAAC,EAAE;gBAC7BA,MAAM,CAACG,OAAO,CAAC,MAAMR,OAAO,CAAC,CAAC,CAAC;cACjC,CAAC,MAAM;gBACLA,OAAO,CAAC,CAAC;cACX;YACF,CAAC,MAAM;cACL1L,IAAI,CAACiL,SAAS,CAAC,aAAa,EAAEhD,GAAG,CAAC;cAClCyD,OAAO,CAAC,CAAC,CAAC,CAAC;YACb;UACF;UAEAD,WAAW,CAAC,CAAC;QACf,CAAC;QAEDF,WAAW,CAAC,CAAC;MACf,CAAC;MAEDO,iBAAiB,EAAE;QACjBK,GAAG,EAAE,eAAAA,CAAgBlE,GAAG,EAAEyD,OAAO,EAAE;UACjC,IAAI1L,IAAI,GAAG,IAAI;;UAEf;UACA;UACAA,IAAI,CAACyG,aAAa,GAAGiF,OAAO;;UAE5B;UACA,IAAI,OAAQzD,GAAG,CAAChC,EAAG,KAAK,QAAQ,IAC5B,OAAQgC,GAAG,CAACmE,IAAK,KAAK,QAAQ,IAC7B,QAAQ,IAAInE,GAAG,IAAI,EAAEA,GAAG,CAACoE,MAAM,YAAYC,KAAK,CAAE,EAAE;YACvDtM,IAAI,CAACiL,SAAS,CAAC,wBAAwB,EAAEhD,GAAG,CAAC;YAC7C;UACF;UAEA,IAAI,CAACjI,IAAI,CAACkB,MAAM,CAACqL,gBAAgB,CAACtE,GAAG,CAACmE,IAAI,CAAC,EAAE;YAC3CpM,IAAI,CAACoC,IAAI,CAAC;cACR6F,GAAG,EAAE,OAAO;cAAEhC,EAAE,EAAEgC,GAAG,CAAChC,EAAE;cACxBuG,KAAK,EAAE,IAAInG,MAAM,CAACoG,KAAK,CAAC,GAAG,mBAAAC,MAAA,CAAmBzE,GAAG,CAACmE,IAAI,gBAAa;YAAC,CAAC,CAAC;YACxE;UACF;UAEA,IAAIpM,IAAI,CAAC0G,UAAU,CAACiG,GAAG,CAAC1E,GAAG,CAAChC,EAAE,CAAC;YAC7B;YACA;YACA;YACA;;UAEF;UACA;UACA;UACA;UACA;UACA,IAAI2C,OAAO,CAAC,kBAAkB,CAAC,EAAE;YAC/B,IAAIgE,cAAc,GAAGhE,OAAO,CAAC,kBAAkB,CAAC,CAACgE,cAAc;YAC/D,IAAIC,gBAAgB,GAAG;cACrBhG,MAAM,EAAE7G,IAAI,CAAC6G,MAAM;cACnBgB,aAAa,EAAE7H,IAAI,CAACsH,gBAAgB,CAACO,aAAa;cAClDiF,IAAI,EAAE,cAAc;cACpBV,IAAI,EAAEnE,GAAG,CAACmE,IAAI;cACdW,YAAY,EAAE/M,IAAI,CAACiG;YACrB,CAAC;YAED2G,cAAc,CAACI,UAAU,CAACH,gBAAgB,CAAC;YAC3C,IAAII,eAAe,GAAGL,cAAc,CAACM,MAAM,CAACL,gBAAgB,CAAC;YAC7D,IAAI,CAACI,eAAe,CAACE,OAAO,EAAE;cAC5BnN,IAAI,CAACoC,IAAI,CAAC;gBACR6F,GAAG,EAAE,OAAO;gBAAEhC,EAAE,EAAEgC,GAAG,CAAChC,EAAE;gBACxBuG,KAAK,EAAE,IAAInG,MAAM,CAACoG,KAAK,CACrB,mBAAmB,EACnBG,cAAc,CAACQ,eAAe,CAACH,eAAe,CAAC,EAC/C;kBAACI,WAAW,EAAEJ,eAAe,CAACI;gBAAW,CAAC;cAC9C,CAAC,CAAC;cACF;YACF;UACF;UAEA,IAAI7C,OAAO,GAAGxK,IAAI,CAACkB,MAAM,CAACqL,gBAAgB,CAACtE,GAAG,CAACmE,IAAI,CAAC;UAEpD,MAAMpM,IAAI,CAACyK,kBAAkB,CAACD,OAAO,EAAEvC,GAAG,CAAChC,EAAE,EAAEgC,GAAG,CAACoE,MAAM,EAAEpE,GAAG,CAACmE,IAAI,CAAC;;UAEpE;UACApM,IAAI,CAACyG,aAAa,GAAG,IAAI;QAC3B,CAAC;QAED6G,KAAK,EAAE,SAAAA,CAAUrF,GAAG,EAAE;UACpB,IAAIjI,IAAI,GAAG,IAAI;UAEfA,IAAI,CAACuN,iBAAiB,CAACtF,GAAG,CAAChC,EAAE,CAAC;QAChC,CAAC;QAEDuH,MAAM,EAAE,eAAAA,CAAgBvF,GAAG,EAAEyD,OAAO,EAAE;UACpC,IAAI1L,IAAI,GAAG,IAAI;;UAEf;UACA;UACA;UACA,IAAI,OAAQiI,GAAG,CAAChC,EAAG,KAAK,QAAQ,IAC5B,OAAQgC,GAAG,CAACuF,MAAO,KAAK,QAAQ,IAC/B,QAAQ,IAAIvF,GAAG,IAAI,EAAEA,GAAG,CAACoE,MAAM,YAAYC,KAAK,CAAE,IACjD,YAAY,IAAIrE,GAAG,IAAM,OAAOA,GAAG,CAACwF,UAAU,KAAK,QAAU,EAAE;YACnEzN,IAAI,CAACiL,SAAS,CAAC,6BAA6B,EAAEhD,GAAG,CAAC;YAClD;UACF;UAEA,IAAIwF,UAAU,GAAGxF,GAAG,CAACwF,UAAU,IAAI,IAAI;;UAEvC;UACA;UACA;UACA,IAAI9H,KAAK,GAAG,IAAIhB,SAAS,CAAC+I,WAAW,CAAD,CAAC;UACrC/H,KAAK,CAACgI,cAAc,CAAC,YAAY;YAC/B;YACA;YACA;YACA;YACA;YACAhI,KAAK,CAACiI,MAAM,CAAC,CAAC;YACd5N,IAAI,CAACoC,IAAI,CAAC;cAAC6F,GAAG,EAAE,SAAS;cAAE4F,OAAO,EAAE,CAAC5F,GAAG,CAAChC,EAAE;YAAC,CAAC,CAAC;UAChD,CAAC,CAAC;;UAEF;UACA,IAAIuE,OAAO,GAAGxK,IAAI,CAACkB,MAAM,CAAC4M,eAAe,CAAC7F,GAAG,CAACuF,MAAM,CAAC;UACrD,IAAI,CAAChD,OAAO,EAAE;YACZxK,IAAI,CAACoC,IAAI,CAAC;cACR6F,GAAG,EAAE,QAAQ;cAAEhC,EAAE,EAAEgC,GAAG,CAAChC,EAAE;cACzBuG,KAAK,EAAE,IAAInG,MAAM,CAACoG,KAAK,CAAC,GAAG,aAAAC,MAAA,CAAazE,GAAG,CAACuF,MAAM,gBAAa;YAAC,CAAC,CAAC;YACpE,MAAM7H,KAAK,CAACoI,GAAG,CAAC,CAAC;YACjB;UACF;UAEA,IAAIC,UAAU,GAAG,IAAI1F,SAAS,CAAC2F,gBAAgB,CAAC;YAC9C7B,IAAI,EAAEnE,GAAG,CAACuF,MAAM;YAChBU,YAAY,EAAE,KAAK;YACnBrH,MAAM,EAAE7G,IAAI,CAAC6G,MAAM;YACnBsH,SAASA,CAACtH,MAAM,EAAE;cAChB,OAAO7G,IAAI,CAACoO,UAAU,CAACvH,MAAM,CAAC;YAChC,CAAC;YACD6E,OAAO,EAAEA,OAAO;YAChBxJ,UAAU,EAAElC,IAAI,CAACsH,gBAAgB;YACjCmG,UAAU,EAAEA,UAAU;YACtB9H;UACF,CAAC,CAAC;UAEF,MAAM0I,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;YAC/C;YACA;YACA;YACA;YACA,IAAI5F,OAAO,CAAC,kBAAkB,CAAC,EAAE;cAC/B,IAAIgE,cAAc,GAAGhE,OAAO,CAAC,kBAAkB,CAAC,CAACgE,cAAc;cAC/D,IAAIC,gBAAgB,GAAG;gBACrBhG,MAAM,EAAE7G,IAAI,CAAC6G,MAAM;gBACnBgB,aAAa,EAAE7H,IAAI,CAACsH,gBAAgB,CAACO,aAAa;gBAClDiF,IAAI,EAAE,QAAQ;gBACdV,IAAI,EAAEnE,GAAG,CAACuF,MAAM;gBAChBT,YAAY,EAAE/M,IAAI,CAACiG;cACrB,CAAC;cACD2G,cAAc,CAACI,UAAU,CAACH,gBAAgB,CAAC;cAC3C,IAAII,eAAe,GAAGL,cAAc,CAACM,MAAM,CAACL,gBAAgB,CAAC;cAC7D,IAAI,CAACI,eAAe,CAACE,OAAO,EAAE;gBAC5BqB,MAAM,CAAC,IAAInI,MAAM,CAACoG,KAAK,CACrB,mBAAmB,EACnBG,cAAc,CAACQ,eAAe,CAACH,eAAe,CAAC,EAC/C;kBAACI,WAAW,EAAEJ,eAAe,CAACI;gBAAW,CAC3C,CAAC,CAAC;gBACF;cACF;YACF;YAEAkB,OAAO,CAAC5J,SAAS,CAACY,kBAAkB,CAACkJ,SAAS,CAC5C9I,KAAK,EACL,MAAMF,GAAG,CAACC,wBAAwB,CAAC+I,SAAS,CAC1CT,UAAU,EACV,MAAMU,wBAAwB,CAC5BlE,OAAO,EAAEwD,UAAU,EAAE/F,GAAG,CAACoE,MAAM,EAC/B,WAAW,GAAGpE,GAAG,CAACuF,MAAM,GAAG,GAC7B,CACF,CACF,CAAC,CAAC;UACJ,CAAC,CAAC;UAEF,eAAemB,MAAMA,CAAA,EAAG;YACtB,MAAMhJ,KAAK,CAACoI,GAAG,CAAC,CAAC;YACjBrC,OAAO,CAAC,CAAC;UACX;UAEA,MAAMkD,OAAO,GAAG;YACd3G,GAAG,EAAE,QAAQ;YACbhC,EAAE,EAAEgC,GAAG,CAAChC;UACV,CAAC;UACD,OAAOoI,OAAO,CAACQ,IAAI,CAAC,MAAM9C,MAAM,IAAI;YAClC,MAAM4C,MAAM,CAAC,CAAC;YACd,IAAI5C,MAAM,KAAKnG,SAAS,EAAE;cACxBgJ,OAAO,CAAC7C,MAAM,GAAGA,MAAM;YACzB;YACA/L,IAAI,CAACoC,IAAI,CAACwM,OAAO,CAAC;UACpB,CAAC,EAAE,MAAOE,SAAS,IAAK;YACtB,MAAMH,MAAM,CAAC,CAAC;YACdC,OAAO,CAACpC,KAAK,GAAGuC,qBAAqB,CACnCD,SAAS,4BAAApC,MAAA,CACiBzE,GAAG,CAACuF,MAAM,MACtC,CAAC;YACDxN,IAAI,CAACoC,IAAI,CAACwM,OAAO,CAAC;UACpB,CAAC,CAAC;QACJ;MACF,CAAC;MAEDI,QAAQ,EAAE,SAAAA,CAAUC,CAAC,EAAE;QACrB,IAAIjP,IAAI,GAAG,IAAI;QACfA,IAAI,CAAC0G,UAAU,CAAC9D,OAAO,CAACqM,CAAC,CAAC;QAC1BjP,IAAI,CAAC4G,cAAc,CAAChE,OAAO,CAACqM,CAAC,CAAC;MAChC,CAAC;MAEDC,oBAAoB,EAAE,SAAAA,CAAUC,SAAS,EAAE;QACzC,IAAInP,IAAI,GAAG,IAAI;QACfoP,YAAY,CAACC,QAAQ,CAACF,SAAS,EAAEnP,IAAI,CAAC8G,eAAe,EAAE;UACrDwI,IAAI,EAAE,SAAAA,CAAUlG,cAAc,EAAEmG,SAAS,EAAEC,UAAU,EAAE;YACrDA,UAAU,CAACC,IAAI,CAACF,SAAS,CAAC;UAC5B,CAAC;UACDG,SAAS,EAAE,SAAAA,CAAUtG,cAAc,EAAEoG,UAAU,EAAE;YAC/CA,UAAU,CAACG,SAAS,CAAC/M,OAAO,CAAC,UAAUgN,OAAO,EAAE3J,EAAE,EAAE;cAClDjG,IAAI,CAACsJ,SAAS,CAACF,cAAc,EAAEnD,EAAE,EAAE2J,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC;UACJ,CAAC;UACDC,QAAQ,EAAE,SAAAA,CAAU1G,cAAc,EAAEmG,SAAS,EAAE;YAC7CA,SAAS,CAACI,SAAS,CAAC/M,OAAO,CAAC,UAAUmN,GAAG,EAAE9J,EAAE,EAAE;cAC7CjG,IAAI,CAAC0J,WAAW,CAACN,cAAc,EAAEnD,EAAE,CAAC;YACtC,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;MAED;MACA;MACA,MAAMmI,UAAUA,CAACvH,MAAM,EAAE;QACvB,IAAI7G,IAAI,GAAG,IAAI;QAEf,IAAI6G,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAC/C,MAAM,IAAI4F,KAAK,CAAC,kDAAkD,GAClD,OAAO5F,MAAM,CAAC;;QAEhC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA7G,IAAI,CAACgH,0BAA0B,GAAG,IAAI;;QAEtC;QACA;QACAhH,IAAI,CAACgP,QAAQ,CAAC,UAAU7C,GAAG,EAAE;UAC3BA,GAAG,CAAC6D,WAAW,CAAC,CAAC;QACnB,CAAC,CAAC;;QAEF;QACA;QACA;QACAhQ,IAAI,CAAC+G,UAAU,GAAG,KAAK;QACvB,IAAIoI,SAAS,GAAGnP,IAAI,CAAC8G,eAAe;QACpC9G,IAAI,CAAC8G,eAAe,GAAG,IAAIH,GAAG,CAAC,CAAC;QAChC3G,IAAI,CAAC6G,MAAM,GAAGA,MAAM;;QAEpB;QACA;QACA;QACA;QACA,MAAMpB,GAAG,CAACC,wBAAwB,CAAC+I,SAAS,CAAC7I,SAAS,EAAE,kBAAkB;UACxE;UACA,IAAIqK,YAAY,GAAGjQ,IAAI,CAAC0G,UAAU;UAClC1G,IAAI,CAAC0G,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;UAC3B3G,IAAI,CAAC4G,cAAc,GAAG,EAAE;UAIxB,MAAM0H,OAAO,CAAC4B,GAAG,CAAC,CAAC,GAAGD,YAAY,CAAC,CAACE,GAAG,CAAC,MAAAC,IAAA,IAAiC;YAAA,IAA1B,CAAClH,cAAc,EAAEiD,GAAG,CAAC,GAAAiE,IAAA;YAClE,MAAMC,MAAM,GAAGlE,GAAG,CAACmE,SAAS,CAAC,CAAC;YAC9BtQ,IAAI,CAAC0G,UAAU,CAACwD,GAAG,CAAChB,cAAc,EAAEmH,MAAM,CAAC;YAC3C;YACA;YACA,MAAMA,MAAM,CAACE,WAAW,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC;;UAEH;UACA;UACA;UACAvQ,IAAI,CAACgH,0BAA0B,GAAG,KAAK;UACvChH,IAAI,CAACmI,kBAAkB,CAAC,CAAC;QAC3B,CAAC,EAAE;UAAEiE,IAAI,EAAE;QAAa,CAAC,CAAC;;QAE1B;QACA;QACA;QACA/F,MAAM,CAACmK,gBAAgB,CAAC,YAAY;UAClCxQ,IAAI,CAAC+G,UAAU,GAAG,IAAI;UACtB/G,IAAI,CAACkP,oBAAoB,CAACC,SAAS,CAAC;UACpC,IAAI,CAAC7K,OAAO,CAACtE,IAAI,CAACiH,aAAa,CAAC,EAAE;YAChCjH,IAAI,CAAC+I,SAAS,CAAC/I,IAAI,CAACiH,aAAa,CAAC;YAClCjH,IAAI,CAACiH,aAAa,GAAG,EAAE;UACzB;QACF,CAAC,CAAC;MACJ,CAAC;MAEDwD,kBAAkB,EAAE,SAAAA,CAAUD,OAAO,EAAEiG,KAAK,EAAEpE,MAAM,EAAED,IAAI,EAAE;QAC1D,IAAIpM,IAAI,GAAG,IAAI;QAEf,IAAImM,GAAG,GAAG,IAAIuE,YAAY,CACxB1Q,IAAI,EAAEwK,OAAO,EAAEiG,KAAK,EAAEpE,MAAM,EAAED,IAAI,CAAC;QAErC,IAAIuE,aAAa,GAAG3Q,IAAI,CAACyG,aAAa;QACtC;QACA;QACA;QACA0F,GAAG,CAACT,OAAO,GAAGiF,aAAa,KAAK,MAAM,CAAC,CAAC,CAAC;QAEzC,IAAIF,KAAK,EACPzQ,IAAI,CAAC0G,UAAU,CAACwD,GAAG,CAACuG,KAAK,EAAEtE,GAAG,CAAC,CAAC,KAEhCnM,IAAI,CAAC4G,cAAc,CAAC7H,IAAI,CAACoN,GAAG,CAAC;QAE/B,OAAOA,GAAG,CAACoE,WAAW,CAAC,CAAC;MAC1B,CAAC;MAED;MACAhD,iBAAiB,EAAE,SAAAA,CAAUkD,KAAK,EAAEjE,KAAK,EAAE;QACzC,IAAIxM,IAAI,GAAG,IAAI;QAEf,IAAI4Q,OAAO,GAAG,IAAI;QAClB,IAAIH,KAAK,EAAE;UACT,IAAII,QAAQ,GAAG7Q,IAAI,CAAC0G,UAAU,CAAClB,GAAG,CAACiL,KAAK,CAAC;UACzC,IAAII,QAAQ,EAAE;YACZD,OAAO,GAAGC,QAAQ,CAACC,KAAK;YACxBD,QAAQ,CAACE,mBAAmB,CAAC,CAAC;YAC9BF,QAAQ,CAACb,WAAW,CAAC,CAAC;YACtBhQ,IAAI,CAAC0G,UAAU,CAAC2D,MAAM,CAACoG,KAAK,CAAC;UAC/B;QACF;QAEA,IAAIO,QAAQ,GAAG;UAAC/I,GAAG,EAAE,OAAO;UAAEhC,EAAE,EAAEwK;QAAK,CAAC;QAExC,IAAIjE,KAAK,EAAE;UACTwE,QAAQ,CAACxE,KAAK,GAAGuC,qBAAqB,CACpCvC,KAAK,EACLoE,OAAO,GAAI,WAAW,GAAGA,OAAO,GAAG,MAAM,GAAGH,KAAK,GAC5C,cAAc,GAAGA,KAAM,CAAC;QACjC;QAEAzQ,IAAI,CAACoC,IAAI,CAAC4O,QAAQ,CAAC;MACrB,CAAC;MAED;MACA;MACApG,2BAA2B,EAAE,SAAAA,CAAA,EAAY;QACvC,IAAI5K,IAAI,GAAG,IAAI;QAEfA,IAAI,CAAC0G,UAAU,CAAC9D,OAAO,CAAC,UAAUuJ,GAAG,EAAElG,EAAE,EAAE;UACzCkG,GAAG,CAAC6D,WAAW,CAAC,CAAC;QACnB,CAAC,CAAC;QACFhQ,IAAI,CAAC0G,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;QAE3B3G,IAAI,CAAC4G,cAAc,CAAChE,OAAO,CAAC,UAAUuJ,GAAG,EAAE;UACzCA,GAAG,CAAC6D,WAAW,CAAC,CAAC;QACnB,CAAC,CAAC;QACFhQ,IAAI,CAAC4G,cAAc,GAAG,EAAE;MAC1B,CAAC;MAED;MACA;MACA;MACAkB,cAAc,EAAE,SAAAA,CAAA,EAAY;QAC1B,IAAI9H,IAAI,GAAG,IAAI;;QAEf;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIiR,kBAAkB,GAAGC,QAAQ,CAACxS,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC;QAE3E,IAAIsS,kBAAkB,KAAK,CAAC,EAC1B,OAAOjR,IAAI,CAAC4B,MAAM,CAACuP,aAAa;QAElC,IAAIC,YAAY,GAAGpR,IAAI,CAAC4B,MAAM,CAACoG,OAAO,CAAC,iBAAiB,CAAC;QACzD,IAAI,CAACxD,QAAQ,CAAC4M,YAAY,CAAC,EACzB,OAAO,IAAI;QACbA,YAAY,GAAGA,YAAY,CAACC,KAAK,CAAC,GAAG,CAAC;;QAEtC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,IAAIJ,kBAAkB,GAAG,CAAC,IAAIA,kBAAkB,KAAKG,YAAY,CAACE,MAAM,EACtE,OAAO,IAAI;QACbF,YAAY,GAAGA,YAAY,CAACjB,GAAG,CAAEoB,EAAE,IAAKA,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC;QAClD,OAAOJ,YAAY,CAACA,YAAY,CAACE,MAAM,GAAGL,kBAAkB,CAAC;MAC/D;IACF,CAAC,CAAC;;IAEF;IACA;IACA;;IAEA;;IAEA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACA,IAAIP,YAAY,GAAG,SAAAA,CACfxI,OAAO,EAAEsC,OAAO,EAAEtB,cAAc,EAAEmD,MAAM,EAAED,IAAI,EAAE;MAClD,IAAIpM,IAAI,GAAG,IAAI;MACfA,IAAI,CAACgC,QAAQ,GAAGkG,OAAO,CAAC,CAAC;;MAEzB;AACF;AACA;AACA;AACA;AACA;AACA;MACElI,IAAI,CAACkC,UAAU,GAAGgG,OAAO,CAACZ,gBAAgB,CAAC,CAAC;;MAE5CtH,IAAI,CAACyR,QAAQ,GAAGjH,OAAO;;MAEvB;MACAxK,IAAI,CAAC0R,eAAe,GAAGxI,cAAc;MACrC;MACAlJ,IAAI,CAAC8Q,KAAK,GAAG1E,IAAI;MAEjBpM,IAAI,CAAC2R,OAAO,GAAGtF,MAAM,IAAI,EAAE;;MAE3B;MACA;MACA;MACA,IAAIrM,IAAI,CAAC0R,eAAe,EAAE;QACxB1R,IAAI,CAAC4R,mBAAmB,GAAG,GAAG,GAAG5R,IAAI,CAAC0R,eAAe;MACvD,CAAC,MAAM;QACL1R,IAAI,CAAC4R,mBAAmB,GAAG,GAAG,GAAG1L,MAAM,CAACD,EAAE,CAAC,CAAC;MAC9C;;MAEA;MACAjG,IAAI,CAAC6R,YAAY,GAAG,KAAK;;MAEzB;MACA7R,IAAI,CAAC8R,cAAc,GAAG,EAAE;;MAExB;MACA;MACA9R,IAAI,CAAC+R,UAAU,GAAG,IAAIpL,GAAG,CAAC,CAAC;;MAE3B;MACA3G,IAAI,CAACgS,MAAM,GAAG,KAAK;;MAEnB;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;MACEhS,IAAI,CAAC6G,MAAM,GAAGqB,OAAO,CAACrB,MAAM;;MAE5B;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA7G,IAAI,CAACiS,SAAS,GAAG;QACfC,WAAW,EAAEC,OAAO,CAACD,WAAW;QAChCE,OAAO,EAAED,OAAO,CAACC;MACnB,CAAC;MAEDxJ,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,UAAU,EAAE,eAAe,EAAE,CAAC,CAAC;IACnC,CAAC;IAEDhG,MAAM,CAACC,MAAM,CAAC2N,YAAY,CAAC1N,SAAS,EAAE;MACpCuN,WAAW,EAAE,eAAAA,CAAA,EAAiB;QAC5B;QACA;QACA;QACA;QACA;QACA;;QAEA,IAAI,CAAC,IAAI,CAAC7E,OAAO,EAAE;UACjB,IAAI,CAACA,OAAO,GAAG,MAAM,CAAC,CAAC;QACzB;QAEA,MAAM1L,IAAI,GAAG,IAAI;QACjB,IAAIqS,gBAAgB,GAAG,IAAI;QAC3B,IAAI;UACFA,gBAAgB,GAAG5M,GAAG,CAAC6M,6BAA6B,CAAC7D,SAAS,CAC5DzO,IAAI,EACJ,MACE0O,wBAAwB,CACtB1O,IAAI,CAACyR,QAAQ,EACbzR,IAAI,EACJuS,KAAK,CAACC,KAAK,CAACxS,IAAI,CAAC2R,OAAO,CAAC;UACzB;UACA;UACA;UACA,aAAa,GAAG3R,IAAI,CAAC8Q,KAAK,GAAG,GAC/B,CAAC,EACH;YAAE1E,IAAI,EAAEpM,IAAI,CAAC8Q;UAAM,CACrB,CAAC;QACH,CAAC,CAAC,OAAO2B,CAAC,EAAE;UACVzS,IAAI,CAACwM,KAAK,CAACiG,CAAC,CAAC;UACb;QACF;;QAEA;QACA,IAAIzS,IAAI,CAAC0S,cAAc,CAAC,CAAC,EAAE;;QAE3B;QACA;QACA;QACA,MAAMC,UAAU,GACdN,gBAAgB,IAAI,OAAOA,gBAAgB,CAACxD,IAAI,KAAK,UAAU;QACjE,IAAI8D,UAAU,EAAE;UACd,IAAI;YACF,MAAM3S,IAAI,CAAC4S,qBAAqB,CAAC,MAAMP,gBAAgB,CAAC;UAC1D,CAAC,CAAC,OAAMI,CAAC,EAAE;YACTzS,IAAI,CAACwM,KAAK,CAACiG,CAAC,CAAC;UACf;QACF,CAAC,MAAM;UACL,MAAMzS,IAAI,CAAC4S,qBAAqB,CAACP,gBAAgB,CAAC;QACpD;MACF,CAAC;MAED,MAAMO,qBAAqBA,CAAEC,GAAG,EAAE;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,IAAI7S,IAAI,GAAG,IAAI;QACf,IAAI8S,QAAQ,GAAG,SAAAA,CAAUC,CAAC,EAAE;UAC1B,OAAOA,CAAC,IAAIA,CAAC,CAACC,cAAc;QAC9B,CAAC;QACD,IAAIF,QAAQ,CAACD,GAAG,CAAC,EAAE;UACjB,IAAI;YACF,MAAMA,GAAG,CAACG,cAAc,CAAChT,IAAI,CAAC;UAChC,CAAC,CAAC,OAAOyS,CAAC,EAAE;YACVzS,IAAI,CAACwM,KAAK,CAACiG,CAAC,CAAC;YACb;UACF;UACA;UACA;UACAzS,IAAI,CAACiT,KAAK,CAAC,CAAC;QACd,CAAC,MAAM,IAAI3G,KAAK,CAAC4G,OAAO,CAACL,GAAG,CAAC,EAAE;UAC7B;UACA,IAAI,CAAEA,GAAG,CAACM,KAAK,CAACL,QAAQ,CAAC,EAAE;YACzB9S,IAAI,CAACwM,KAAK,CAAC,IAAIC,KAAK,CAAC,mDAAmD,CAAC,CAAC;YAC1E;UACF;UACA;UACA;UACA;UACA,IAAI2G,eAAe,GAAG,CAAC,CAAC;UAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,CAACvB,MAAM,EAAE,EAAE+B,CAAC,EAAE;YACnC,IAAIjK,cAAc,GAAGyJ,GAAG,CAACQ,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAChD,IAAIF,eAAe,CAAChK,cAAc,CAAC,EAAE;cACnCpJ,IAAI,CAACwM,KAAK,CAAC,IAAIC,KAAK,CAClB,4DAA4D,GAC1DrD,cAAc,CAAC,CAAC;cACpB;YACF;YACAgK,eAAe,CAAChK,cAAc,CAAC,GAAG,IAAI;UACxC;UAEA,IAAI;YACF,MAAMkF,OAAO,CAAC4B,GAAG,CAAC2C,GAAG,CAAC1C,GAAG,CAACoD,GAAG,IAAIA,GAAG,CAACP,cAAc,CAAChT,IAAI,CAAC,CAAC,CAAC;UAC7D,CAAC,CAAC,OAAOyS,CAAC,EAAE;YACVzS,IAAI,CAACwM,KAAK,CAACiG,CAAC,CAAC;YACb;UACF;UACAzS,IAAI,CAACiT,KAAK,CAAC,CAAC;QACd,CAAC,MAAM,IAAIJ,GAAG,EAAE;UACd;UACA;UACA;UACA7S,IAAI,CAACwM,KAAK,CAAC,IAAIC,KAAK,CAAC,+CAA+C,GAC7C,qBAAqB,CAAC,CAAC;QAChD;MACF,CAAC;MAED;MACA;MACA;MACA;MACA;MACAuD,WAAW,EAAE,SAAAA,CAAA,EAAW;QACtB,IAAIhQ,IAAI,GAAG,IAAI;QACf,IAAIA,IAAI,CAAC6R,YAAY,EACnB;QACF7R,IAAI,CAAC6R,YAAY,GAAG,IAAI;QACxB7R,IAAI,CAACwT,kBAAkB,CAAC,CAAC;QACzB5K,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,UAAU,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;MACpC,CAAC;MAED0K,kBAAkB,EAAE,SAAAA,CAAA,EAAY;QAC9B,IAAIxT,IAAI,GAAG,IAAI;QACf;QACA,IAAIyT,SAAS,GAAGzT,IAAI,CAAC8R,cAAc;QACnC9R,IAAI,CAAC8R,cAAc,GAAG,EAAE;QACxB2B,SAAS,CAAC7Q,OAAO,CAAC,UAAUC,QAAQ,EAAE;UACpCA,QAAQ,CAAC,CAAC;QACZ,CAAC,CAAC;MACJ,CAAC;MAED;MACAkO,mBAAmB,EAAE,SAAAA,CAAA,EAAY;QAC/B,IAAI/Q,IAAI,GAAG,IAAI;QACfqG,MAAM,CAACmK,gBAAgB,CAAC,YAAY;UAClCxQ,IAAI,CAAC+R,UAAU,CAACnP,OAAO,CAAC,UAAU8Q,cAAc,EAAEtK,cAAc,EAAE;YAChEsK,cAAc,CAAC9Q,OAAO,CAAC,UAAU+Q,KAAK,EAAE;cACtC3T,IAAI,CAAC+J,OAAO,CAACX,cAAc,EAAEpJ,IAAI,CAACiS,SAAS,CAACG,OAAO,CAACuB,KAAK,CAAC,CAAC;YAC7D,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MAED;MACA;MACA;MACA;MACA;MACArD,SAAS,EAAE,SAAAA,CAAA,EAAY;QACrB,IAAItQ,IAAI,GAAG,IAAI;QACf,OAAO,IAAI0Q,YAAY,CACrB1Q,IAAI,CAACgC,QAAQ,EAAEhC,IAAI,CAACyR,QAAQ,EAAEzR,IAAI,CAAC0R,eAAe,EAAE1R,IAAI,CAAC2R,OAAO,EAChE3R,IAAI,CAAC8Q,KAAK,CAAC;MACf,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;MACEtE,KAAK,EAAE,SAAAA,CAAUA,KAAK,EAAE;QACtB,IAAIxM,IAAI,GAAG,IAAI;QACf,IAAIA,IAAI,CAAC0S,cAAc,CAAC,CAAC,EACvB;QACF1S,IAAI,CAACgC,QAAQ,CAACuL,iBAAiB,CAACvN,IAAI,CAAC0R,eAAe,EAAElF,KAAK,CAAC;MAC9D,CAAC;MAED;MACA;MACA;MACA;;MAEA;AACF;AACA;AACA;AACA;AACA;MACE9B,IAAI,EAAE,SAAAA,CAAA,EAAY;QAChB,IAAI1K,IAAI,GAAG,IAAI;QACf,IAAIA,IAAI,CAAC0S,cAAc,CAAC,CAAC,EACvB;QACF1S,IAAI,CAACgC,QAAQ,CAACuL,iBAAiB,CAACvN,IAAI,CAAC0R,eAAe,CAAC;MACvD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;MACEkC,MAAM,EAAE,SAAAA,CAAU/Q,QAAQ,EAAE;QAC1B,IAAI7C,IAAI,GAAG,IAAI;QACf6C,QAAQ,GAAGwD,MAAM,CAACsB,eAAe,CAAC9E,QAAQ,EAAE,iBAAiB,EAAE7C,IAAI,CAAC;QACpE,IAAIA,IAAI,CAAC0S,cAAc,CAAC,CAAC,EACvB7P,QAAQ,CAAC,CAAC,CAAC,KAEX7C,IAAI,CAAC8R,cAAc,CAAC/S,IAAI,CAAC8D,QAAQ,CAAC;MACtC,CAAC;MAED;MACA;MACA;MACA6P,cAAc,EAAE,SAAAA,CAAA,EAAY;QAC1B,IAAI1S,IAAI,GAAG,IAAI;QACf,OAAOA,IAAI,CAAC6R,YAAY,IAAI7R,IAAI,CAACgC,QAAQ,CAACoE,OAAO,KAAK,IAAI;MAC5D,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEwD,KAAKA,CAAER,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,EAAE;QACjC,IAAI,IAAI,CAACmJ,cAAc,CAAC,CAAC,EACvB;QACFzM,EAAE,GAAG,IAAI,CAACgM,SAAS,CAACC,WAAW,CAACjM,EAAE,CAAC;QAEnC,IAAI,IAAI,CAACjE,QAAQ,CAACd,MAAM,CAACmI,sBAAsB,CAACD,cAAc,CAAC,CAACpE,yBAAyB,EAAE;UACzF,IAAI6O,GAAG,GAAG,IAAI,CAAC9B,UAAU,CAACvM,GAAG,CAAC4D,cAAc,CAAC;UAC7C,IAAIyK,GAAG,IAAI,IAAI,EAAE;YACfA,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;YACf,IAAI,CAAC/B,UAAU,CAAC7H,GAAG,CAACd,cAAc,EAAEyK,GAAG,CAAC;UAC1C;UACAA,GAAG,CAACE,GAAG,CAAC9N,EAAE,CAAC;QACb;QAEA,IAAI,CAACjE,QAAQ,CAAC4H,KAAK,CAAC,IAAI,CAACgI,mBAAmB,EAAExI,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,CAAC;MAC3E,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEO,OAAOA,CAAEV,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,EAAE;QACnC,IAAI,IAAI,CAACmJ,cAAc,CAAC,CAAC,EACvB;QACFzM,EAAE,GAAG,IAAI,CAACgM,SAAS,CAACC,WAAW,CAACjM,EAAE,CAAC;QACnC,IAAI,CAACjE,QAAQ,CAAC8H,OAAO,CAAC,IAAI,CAAC8H,mBAAmB,EAAExI,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,CAAC;MAC7E,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEQ,OAAOA,CAAEX,cAAc,EAAEnD,EAAE,EAAE;QAC3B,IAAI,IAAI,CAACyM,cAAc,CAAC,CAAC,EACvB;QACFzM,EAAE,GAAG,IAAI,CAACgM,SAAS,CAACC,WAAW,CAACjM,EAAE,CAAC;QAEnC,IAAI,IAAI,CAACjE,QAAQ,CAACd,MAAM,CAACmI,sBAAsB,CAACD,cAAc,CAAC,CAACpE,yBAAyB,EAAE;UACzF;UACA;UACA,IAAI,CAAC+M,UAAU,CAACvM,GAAG,CAAC4D,cAAc,CAAC,CAACiB,MAAM,CAACpE,EAAE,CAAC;QAChD;QAEA,IAAI,CAACjE,QAAQ,CAAC+H,OAAO,CAAC,IAAI,CAAC6H,mBAAmB,EAAExI,cAAc,EAAEnD,EAAE,CAAC;MACrE,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;MACEgN,KAAK,EAAE,SAAAA,CAAA,EAAY;QACjB,IAAIjT,IAAI,GAAG,IAAI;QACf,IAAIA,IAAI,CAAC0S,cAAc,CAAC,CAAC,EACvB;QACF,IAAI,CAAC1S,IAAI,CAAC0R,eAAe,EACvB,OAAO,CAAE;QACX,IAAI,CAAC1R,IAAI,CAACgS,MAAM,EAAE;UAChBhS,IAAI,CAACgC,QAAQ,CAAC+G,SAAS,CAAC,CAAC/I,IAAI,CAAC0R,eAAe,CAAC,CAAC;UAC/C1R,IAAI,CAACgS,MAAM,GAAG,IAAI;QACpB;MACF;IACF,CAAC,CAAC;;IAEF;IACA;IACA;;IAEAgC,MAAM,GAAG,SAAAA,CAAA,EAAwB;MAAA,IAAdhO,OAAO,GAAApC,SAAA,CAAA0N,MAAA,QAAA1N,SAAA,QAAAgC,SAAA,GAAAhC,SAAA,MAAG,CAAC,CAAC;MAC7B,IAAI5D,IAAI,GAAG,IAAI;;MAEf;MACA;MACA;MACA;MACA;MACA;MACA;MACAA,IAAI,CAACgG,OAAO,GAAAjI,aAAA;QACVqK,iBAAiB,EAAE,KAAK;QACxBI,gBAAgB,EAAE,KAAK;QACvB;QACAnB,cAAc,EAAE,IAAI;QACpB4M,0BAA0B,EAAErP,qBAAqB,CAACC;MAAY,GAC3DmB,OAAO,CACX;;MAED;MACA;MACA;MACA;MACAhG,IAAI,CAACkU,gBAAgB,GAAG,IAAIC,IAAI,CAAC;QAC/BC,oBAAoB,EAAE;MACxB,CAAC,CAAC;;MAEF;MACApU,IAAI,CAAC4L,aAAa,GAAG,IAAIuI,IAAI,CAAC;QAC5BC,oBAAoB,EAAE;MACxB,CAAC,CAAC;MAEFpU,IAAI,CAACuM,gBAAgB,GAAG,CAAC,CAAC;MAC1BvM,IAAI,CAACuK,0BAA0B,GAAG,EAAE;MAEpCvK,IAAI,CAAC8N,eAAe,GAAG,CAAC,CAAC;MAEzB9N,IAAI,CAACqU,sBAAsB,GAAG,CAAC,CAAC;MAEhCrU,IAAI,CAACsU,QAAQ,GAAG,IAAI3N,GAAG,CAAC,CAAC,CAAC,CAAC;;MAE3B3G,IAAI,CAACuU,aAAa,GAAG,IAAIxU,YAAY,CAAC,CAAC;MAEvCC,IAAI,CAACuU,aAAa,CAACtR,QAAQ,CAAC,UAAUrB,MAAM,EAAE;QAC5C;QACAA,MAAM,CAAC+I,cAAc,GAAG,IAAI;QAE5B,IAAIM,SAAS,GAAG,SAAAA,CAAUC,MAAM,EAAEC,gBAAgB,EAAE;UAClD,IAAIlD,GAAG,GAAG;YAACA,GAAG,EAAE,OAAO;YAAEiD,MAAM,EAAEA;UAAM,CAAC;UACxC,IAAIC,gBAAgB,EAClBlD,GAAG,CAACkD,gBAAgB,GAAGA,gBAAgB;UACzCvJ,MAAM,CAACQ,IAAI,CAACkG,SAAS,CAAC0C,YAAY,CAAC/C,GAAG,CAAC,CAAC;QAC1C,CAAC;QAEDrG,MAAM,CAACD,EAAE,CAAC,MAAM,EAAE,UAAU6S,OAAO,EAAE;UACnC,IAAInO,MAAM,CAACoO,iBAAiB,EAAE;YAC5BpO,MAAM,CAAC0E,MAAM,CAAC,cAAc,EAAEyJ,OAAO,CAAC;UACxC;UACA,IAAI;YACF,IAAI;cACF,IAAIvM,GAAG,GAAGK,SAAS,CAACoM,QAAQ,CAACF,OAAO,CAAC;YACvC,CAAC,CAAC,OAAOG,GAAG,EAAE;cACZ1J,SAAS,CAAC,aAAa,CAAC;cACxB;YACF;YACA,IAAIhD,GAAG,KAAK,IAAI,IAAI,CAACA,GAAG,CAACA,GAAG,EAAE;cAC5BgD,SAAS,CAAC,aAAa,EAAEhD,GAAG,CAAC;cAC7B;YACF;YAEA,IAAIA,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;cACzB,IAAIrG,MAAM,CAAC+I,cAAc,EAAE;gBACzBM,SAAS,CAAC,mBAAmB,EAAEhD,GAAG,CAAC;gBACnC;cACF;cAEAjI,IAAI,CAAC4U,cAAc,CAAChT,MAAM,EAAEqG,GAAG,CAAC;cAEhC;YACF;YAEA,IAAI,CAACrG,MAAM,CAAC+I,cAAc,EAAE;cAC1BM,SAAS,CAAC,oBAAoB,EAAEhD,GAAG,CAAC;cACpC;YACF;YACArG,MAAM,CAAC+I,cAAc,CAACS,cAAc,CAACnD,GAAG,CAAC;UAC3C,CAAC,CAAC,OAAOwK,CAAC,EAAE;YACV;YACApM,MAAM,CAAC0E,MAAM,CAAC,6CAA6C,EAAE9C,GAAG,EAAEwK,CAAC,CAAC;UACtE;QACF,CAAC,CAAC;QAEF7Q,MAAM,CAACD,EAAE,CAAC,OAAO,EAAE,YAAY;UAC7B,IAAIC,MAAM,CAAC+I,cAAc,EAAE;YACzB/I,MAAM,CAAC+I,cAAc,CAACpD,KAAK,CAAC,CAAC;UAC/B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAEDzE,MAAM,CAACC,MAAM,CAACiR,MAAM,CAAChR,SAAS,EAAE;MAE9B;AACF;AACA;AACA;AACA;AACA;AACA;MACE6R,YAAY,EAAE,SAAAA,CAAUpN,EAAE,EAAE;QAC1B,IAAIzH,IAAI,GAAG,IAAI;QACf,OAAOA,IAAI,CAACkU,gBAAgB,CAACjR,QAAQ,CAACwE,EAAE,CAAC;MAC3C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEqN,sBAAsBA,CAAC1L,cAAc,EAAE2L,QAAQ,EAAE;QAC/C,IAAI,CAACjS,MAAM,CAACK,MAAM,CAACyB,qBAAqB,CAAC,CAACoQ,QAAQ,CAACD,QAAQ,CAAC,EAAE;UAC5D,MAAM,IAAItI,KAAK,4BAAAC,MAAA,CAA4BqI,QAAQ,gCAAArI,MAAA,CAChCtD,cAAc,CAAE,CAAC;QACtC;QACA,IAAI,CAACiL,sBAAsB,CAACjL,cAAc,CAAC,GAAG2L,QAAQ;MACxD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE1L,sBAAsBA,CAACD,cAAc,EAAE;QACrC,OAAO,IAAI,CAACiL,sBAAsB,CAACjL,cAAc,CAAC,IAC7C,IAAI,CAACpD,OAAO,CAACiO,0BAA0B;MAC9C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;MACEgB,SAAS,EAAE,SAAAA,CAAUxN,EAAE,EAAE;QACvB,IAAIzH,IAAI,GAAG,IAAI;QACf,OAAOA,IAAI,CAAC4L,aAAa,CAAC3I,QAAQ,CAACwE,EAAE,CAAC;MACxC,CAAC;MAEDmN,cAAc,EAAE,SAAAA,CAAUhT,MAAM,EAAEqG,GAAG,EAAE;QACrC,IAAIjI,IAAI,GAAG,IAAI;;QAEf;QACA;QACA,IAAI,EAAE,OAAQiI,GAAG,CAAClC,OAAQ,KAAK,QAAQ,IACjCuG,KAAK,CAAC4G,OAAO,CAACjL,GAAG,CAACiN,OAAO,CAAC,IAC1BjN,GAAG,CAACiN,OAAO,CAAC/B,KAAK,CAAC3O,QAAQ,CAAC,IAC3ByD,GAAG,CAACiN,OAAO,CAACF,QAAQ,CAAC/M,GAAG,CAAClC,OAAO,CAAC,CAAC,EAAE;UACxCnE,MAAM,CAACQ,IAAI,CAACkG,SAAS,CAAC0C,YAAY,CAAC;YAAC/C,GAAG,EAAE,QAAQ;YACvBlC,OAAO,EAAEuC,SAAS,CAAC6M,sBAAsB,CAAC,CAAC;UAAC,CAAC,CAAC,CAAC;UACzEvT,MAAM,CAAC2F,KAAK,CAAC,CAAC;UACd;QACF;;QAEA;QACA;QACA,IAAIxB,OAAO,GAAGqP,gBAAgB,CAACnN,GAAG,CAACiN,OAAO,EAAE5M,SAAS,CAAC6M,sBAAsB,CAAC;QAE7E,IAAIlN,GAAG,CAAClC,OAAO,KAAKA,OAAO,EAAE;UAC3B;UACA;UACA;UACAnE,MAAM,CAACQ,IAAI,CAACkG,SAAS,CAAC0C,YAAY,CAAC;YAAC/C,GAAG,EAAE,QAAQ;YAAElC,OAAO,EAAEA;UAAO,CAAC,CAAC,CAAC;UACtEnE,MAAM,CAAC2F,KAAK,CAAC,CAAC;UACd;QACF;;QAEA;QACA;QACA;QACA3F,MAAM,CAAC+I,cAAc,GAAG,IAAI7E,OAAO,CAAC9F,IAAI,EAAE+F,OAAO,EAAEnE,MAAM,EAAE5B,IAAI,CAACgG,OAAO,CAAC;QACxEhG,IAAI,CAACsU,QAAQ,CAACpK,GAAG,CAACtI,MAAM,CAAC+I,cAAc,CAAC1E,EAAE,EAAErE,MAAM,CAAC+I,cAAc,CAAC;QAClE3K,IAAI,CAACkU,gBAAgB,CAACrI,IAAI,CAAC,UAAUhJ,QAAQ,EAAE;UAC7C,IAAIjB,MAAM,CAAC+I,cAAc,EACvB9H,QAAQ,CAACjB,MAAM,CAAC+I,cAAc,CAACrD,gBAAgB,CAAC;UAClD,OAAO,IAAI;QACb,CAAC,CAAC;MACJ,CAAC;MACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACE+N,OAAO,EAAE,SAAAA,CAAUjJ,IAAI,EAAE5B,OAAO,EAAExE,OAAO,EAAE;QACzC,IAAIhG,IAAI,GAAG,IAAI;QAEf,IAAI,CAACuE,QAAQ,CAAC6H,IAAI,CAAC,EAAE;UACnBpG,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;UAEvB,IAAIoG,IAAI,IAAIA,IAAI,IAAIpM,IAAI,CAACuM,gBAAgB,EAAE;YACzClG,MAAM,CAAC0E,MAAM,CAAC,oCAAoC,GAAGqB,IAAI,GAAG,GAAG,CAAC;YAChE;UACF;UAEA,IAAIxD,OAAO,CAAC0M,WAAW,IAAI,CAACtP,OAAO,CAACuP,OAAO,EAAE;YAC3C;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAI,CAACvV,IAAI,CAACwV,wBAAwB,EAAE;cAClCxV,IAAI,CAACwV,wBAAwB,GAAG,IAAI;cACpCnP,MAAM,CAAC0E,MAAM,CACnB,uEAAuE,GACvE,yEAAyE,GACzE,uEAAuE,GACvE,yCAAyC,GACzC,MAAM,GACN,gEAAgE,GAChE,MAAM,GACN,oCAAoC,GACpC,MAAM,GACN,8EAA8E,GAC9E,wDAAwD,CAAC;YACrD;UACF;UAEA,IAAIqB,IAAI,EACNpM,IAAI,CAACuM,gBAAgB,CAACH,IAAI,CAAC,GAAG5B,OAAO,CAAC,KACnC;YACHxK,IAAI,CAACuK,0BAA0B,CAACxL,IAAI,CAACyL,OAAO,CAAC;YAC7C;YACA;YACA;YACAxK,IAAI,CAACsU,QAAQ,CAAC1R,OAAO,CAAC,UAAUsF,OAAO,EAAE;cACvC,IAAI,CAACA,OAAO,CAAClB,0BAA0B,EAAE;gBACvCkB,OAAO,CAACuC,kBAAkB,CAACD,OAAO,CAAC;cACrC;YACF,CAAC,CAAC;UACJ;QACF,CAAC,MACG;UACF1H,MAAM,CAAC2S,OAAO,CAACrJ,IAAI,CAAC,CAACxJ,OAAO,CAAC,UAAA8S,KAAA,EAAuB;YAAA,IAAd,CAACC,GAAG,EAAEnT,KAAK,CAAC,GAAAkT,KAAA;YAChD1V,IAAI,CAACqV,OAAO,CAACM,GAAG,EAAEnT,KAAK,EAAE,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC;MAEDqI,cAAc,EAAE,SAAAA,CAAU3C,OAAO,EAAE;QACjC,IAAIlI,IAAI,GAAG,IAAI;QACfA,IAAI,CAACsU,QAAQ,CAACjK,MAAM,CAACnC,OAAO,CAACjC,EAAE,CAAC;MAClC,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;MACE2P,WAAW,EAAE,SAAAA,CAAA,EAAU;QACrB,OAAOnQ,GAAG,CAACC,wBAAwB,CAACmQ,yBAAyB,CAAC,CAAC;MACjE,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;MACEhI,OAAO,EAAE,SAAAA,CAAUA,OAAO,EAAE;QAC1B,IAAI7N,IAAI,GAAG,IAAI;QACf8C,MAAM,CAAC2S,OAAO,CAAC5H,OAAO,CAAC,CAACjL,OAAO,CAAC,UAAAkT,KAAA,EAAwB;UAAA,IAAd,CAAC1J,IAAI,EAAE2J,IAAI,CAAC,GAAAD,KAAA;UACpD,IAAI,OAAOC,IAAI,KAAK,UAAU,EAC5B,MAAM,IAAItJ,KAAK,CAAC,UAAU,GAAGL,IAAI,GAAG,sBAAsB,CAAC;UAC7D,IAAIpM,IAAI,CAAC8N,eAAe,CAAC1B,IAAI,CAAC,EAC5B,MAAM,IAAIK,KAAK,CAAC,kBAAkB,GAAGL,IAAI,GAAG,sBAAsB,CAAC;UACrEpM,IAAI,CAAC8N,eAAe,CAAC1B,IAAI,CAAC,GAAG2J,IAAI;QACnC,CAAC,CAAC;MACJ,CAAC;MAED/J,IAAI,EAAE,SAAAA,CAAUI,IAAI,EAAW;QAAA,SAAA4J,IAAA,GAAApS,SAAA,CAAA0N,MAAA,EAAN3N,IAAI,OAAA2I,KAAA,CAAA0J,IAAA,OAAAA,IAAA,WAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;UAAJtS,IAAI,CAAAsS,IAAA,QAAArS,SAAA,CAAAqS,IAAA;QAAA;QAC3B,IAAItS,IAAI,CAAC2N,MAAM,IAAI,OAAO3N,IAAI,CAACA,IAAI,CAAC2N,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;UAC9D;UACA;UACA,IAAIzO,QAAQ,GAAGc,IAAI,CAACuS,GAAG,CAAC,CAAC;QAC3B;QAEA,OAAO,IAAI,CAAChS,KAAK,CAACkI,IAAI,EAAEzI,IAAI,EAAEd,QAAQ,CAAC;MACzC,CAAC;MAED;MACAsT,SAAS,EAAE,SAAAA,CAAU/J,IAAI,EAAW;QAAA,IAAAgK,MAAA;QAAA,SAAAC,KAAA,GAAAzS,SAAA,CAAA0N,MAAA,EAAN3N,IAAI,OAAA2I,KAAA,CAAA+J,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJ3S,IAAI,CAAA2S,KAAA,QAAA1S,SAAA,CAAA0S,KAAA;QAAA;QAChC,MAAMtQ,OAAO,GAAG,CAAAoQ,MAAA,GAAAzS,IAAI,CAAC,CAAC,CAAC,cAAAyS,MAAA,eAAPA,MAAA,CAASG,cAAc,CAAC,iBAAiB,CAAC,GACtD5S,IAAI,CAAC6H,KAAK,CAAC,CAAC,GACZ,CAAC,CAAC;QACN/F,GAAG,CAACC,wBAAwB,CAAC8Q,0BAA0B,CAAC,IAAI,CAAC;QAC7D,MAAMnI,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UAC/C/I,GAAG,CAACgR,2BAA2B,CAACC,IAAI,CAAC;YAAEtK,IAAI;YAAEuK,kBAAkB,EAAE;UAAK,CAAC,CAAC;UACxE,IAAI,CAACC,UAAU,CAACxK,IAAI,EAAEzI,IAAI,EAAA5F,aAAA;YAAI8Y,eAAe,EAAE;UAAI,GAAK7Q,OAAO,CAAE,CAAC,CAC/D6I,IAAI,CAACN,OAAO,CAAC,CACbuI,KAAK,CAACtI,MAAM,CAAC,CACbtC,OAAO,CAAC,MAAM;YACbzG,GAAG,CAACgR,2BAA2B,CAACC,IAAI,CAAC,CAAC;UACxC,CAAC,CAAC;QACN,CAAC,CAAC;QACF,OAAOrI,OAAO,CAACnC,OAAO,CAAC,MACrBzG,GAAG,CAACC,wBAAwB,CAAC8Q,0BAA0B,CAAC,KAAK,CAC/D,CAAC;MACH,CAAC;MAEDtS,KAAK,EAAE,SAAAA,CAAUkI,IAAI,EAAEzI,IAAI,EAAEqC,OAAO,EAAEnD,QAAQ,EAAE;QAC9C;QACA;QACA,IAAI,CAAEA,QAAQ,IAAI,OAAOmD,OAAO,KAAK,UAAU,EAAE;UAC/CnD,QAAQ,GAAGmD,OAAO;UAClBA,OAAO,GAAG,CAAC,CAAC;QACd,CAAC,MAAM;UACLA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;QACzB;QACA,MAAMqI,OAAO,GAAG,IAAI,CAACuI,UAAU,CAACxK,IAAI,EAAEzI,IAAI,EAAEqC,OAAO,CAAC;;QAEpD;QACA;QACA;QACA;QACA;QACA,IAAInD,QAAQ,EAAE;UACZwL,OAAO,CAACQ,IAAI,CACV9C,MAAM,IAAIlJ,QAAQ,CAAC+C,SAAS,EAAEmG,MAAM,CAAC,EACrC+C,SAAS,IAAIjM,QAAQ,CAACiM,SAAS,CACjC,CAAC;QACH,CAAC,MAAM;UACL,OAAOT,OAAO;QAChB;MACF,CAAC;MAED;MACAuI,UAAU,EAAE,SAAAA,CAAUxK,IAAI,EAAEzI,IAAI,EAAEqC,OAAO,EAAE;QACzC;QACA,IAAIwE,OAAO,GAAG,IAAI,CAACsD,eAAe,CAAC1B,IAAI,CAAC;QAExC,IAAI,CAAE5B,OAAO,EAAE;UACb,OAAO8D,OAAO,CAACE,MAAM,CACnB,IAAInI,MAAM,CAACoG,KAAK,CAAC,GAAG,aAAAC,MAAA,CAAaN,IAAI,gBAAa,CACpD,CAAC;QACH;QACA;QACA;QACA;QACA,IAAIvF,MAAM,GAAG,IAAI;QACjB,IAAIsH,SAAS,GAAGA,CAAA,KAAM;UACpB,MAAM,IAAI1B,KAAK,CAAC,wDAAwD,CAAC;QAC3E,CAAC;QACD,IAAIvK,UAAU,GAAG,IAAI;QACrB,IAAI6U,uBAAuB,GAAGtR,GAAG,CAACC,wBAAwB,CAACF,GAAG,CAAC,CAAC;QAChE,IAAIwR,4BAA4B,GAAGvR,GAAG,CAAC6M,6BAA6B,CAAC9M,GAAG,CAAC,CAAC;QAC1E,IAAIiI,UAAU,GAAG,IAAI;QAErB,IAAIsJ,uBAAuB,EAAE;UAC3BlQ,MAAM,GAAGkQ,uBAAuB,CAAClQ,MAAM;UACvCsH,SAAS,GAAItH,MAAM,IAAKkQ,uBAAuB,CAAC5I,SAAS,CAACtH,MAAM,CAAC;UACjE3E,UAAU,GAAG6U,uBAAuB,CAAC7U,UAAU;UAC/CuL,UAAU,GAAGnF,SAAS,CAAC2O,WAAW,CAACF,uBAAuB,EAAE3K,IAAI,CAAC;QACnE,CAAC,MAAM,IAAI4K,4BAA4B,EAAE;UACvCnQ,MAAM,GAAGmQ,4BAA4B,CAACnQ,MAAM;UAC5CsH,SAAS,GAAItH,MAAM,IAAKmQ,4BAA4B,CAAChV,QAAQ,CAACoM,UAAU,CAACvH,MAAM,CAAC;UAChF3E,UAAU,GAAG8U,4BAA4B,CAAC9U,UAAU;QACtD;QAEA,IAAI8L,UAAU,GAAG,IAAI1F,SAAS,CAAC2F,gBAAgB,CAAC;UAC9CC,YAAY,EAAE,KAAK;UACnBrH,MAAM;UACNsH,SAAS;UACTjM,UAAU;UACVuL;QACF,CAAC,CAAC;QAEF,OAAO,IAAIa,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACtC,IAAIzC,MAAM;UACV,IAAI;YACFA,MAAM,GAAGtG,GAAG,CAACC,wBAAwB,CAAC+I,SAAS,CAACT,UAAU,EAAE,MAC1DU,wBAAwB,CACtBlE,OAAO,EACPwD,UAAU,EACVuE,KAAK,CAACC,KAAK,CAAC7O,IAAI,CAAC,EACjB,oBAAoB,GAAGyI,IAAI,GAAG,GAChC,CACF,CAAC;UACH,CAAC,CAAC,OAAOqG,CAAC,EAAE;YACV,OAAOjE,MAAM,CAACiE,CAAC,CAAC;UAClB;UACA,IAAI,CAACpM,MAAM,CAAC4F,UAAU,CAACF,MAAM,CAAC,EAAE;YAC9B,OAAOwC,OAAO,CAACxC,MAAM,CAAC;UACxB;UACAA,MAAM,CAAC8C,IAAI,CAACqI,CAAC,IAAI3I,OAAO,CAAC2I,CAAC,CAAC,CAAC,CAACJ,KAAK,CAACtI,MAAM,CAAC;QAC5C,CAAC,CAAC,CAACK,IAAI,CAAC0D,KAAK,CAACC,KAAK,CAAC;MACtB,CAAC;MAED2E,cAAc,EAAE,SAAAA,CAAUC,SAAS,EAAE;QACnC,IAAIpX,IAAI,GAAG,IAAI;QACf,IAAIkI,OAAO,GAAGlI,IAAI,CAACsU,QAAQ,CAAC9O,GAAG,CAAC4R,SAAS,CAAC;QAC1C,IAAIlP,OAAO,EACT,OAAOA,OAAO,CAACf,UAAU,CAAC,KAE1B,OAAO,IAAI;MACf;IACF,CAAC,CAAC;IAEF,IAAIiO,gBAAgB,GAAG,SAAAA,CAAUiC,uBAAuB,EACvBC,uBAAuB,EAAE;MACxD,IAAIC,cAAc,GAAGF,uBAAuB,CAACG,IAAI,CAAC,UAAUzR,OAAO,EAAE;QACnE,OAAOuR,uBAAuB,CAACtC,QAAQ,CAACjP,OAAO,CAAC;MAClD,CAAC,CAAC;MACF,IAAI,CAACwR,cAAc,EAAE;QACnBA,cAAc,GAAGD,uBAAuB,CAAC,CAAC,CAAC;MAC7C;MACA,OAAOC,cAAc;IACvB,CAAC;IAED5S,SAAS,CAAC8S,iBAAiB,GAAGrC,gBAAgB;;IAG9C;IACA;IACA,IAAIrG,qBAAqB,GAAG,SAAAA,CAAUD,SAAS,EAAE4I,OAAO,EAAE;MACxD,IAAI,CAAC5I,SAAS,EAAE,OAAOA,SAAS;;MAEhC;MACA;MACA;MACA,IAAIA,SAAS,CAAC6I,YAAY,EAAE;QAC1B,IAAI,EAAE7I,SAAS,YAAYzI,MAAM,CAACoG,KAAK,CAAC,EAAE;UACxC,MAAMmL,eAAe,GAAG9I,SAAS,CAAC+I,OAAO;UACzC/I,SAAS,GAAG,IAAIzI,MAAM,CAACoG,KAAK,CAACqC,SAAS,CAACtC,KAAK,EAAEsC,SAAS,CAAC5D,MAAM,EAAE4D,SAAS,CAACgJ,OAAO,CAAC;UAClFhJ,SAAS,CAAC+I,OAAO,GAAGD,eAAe;QACrC;QACA,OAAO9I,SAAS;MAClB;;MAEA;MACA;MACA,IAAI,CAACA,SAAS,CAACiJ,eAAe,EAAE;QAC9B1R,MAAM,CAAC0E,MAAM,CAAC,YAAY,GAAG2M,OAAO,EAAE5I,SAAS,CAACkJ,KAAK,CAAC;QACtD,IAAIlJ,SAAS,CAACmJ,cAAc,EAAE;UAC5B5R,MAAM,CAAC0E,MAAM,CAAC,0CAA0C,EAAE+D,SAAS,CAACmJ,cAAc,CAAC;UACnF5R,MAAM,CAAC0E,MAAM,CAAC,CAAC;QACjB;MACF;;MAEA;MACA;MACA;MACA,IAAI+D,SAAS,CAACmJ,cAAc,EAAE;QAC5B,IAAInJ,SAAS,CAACmJ,cAAc,CAACN,YAAY,EACvC,OAAO7I,SAAS,CAACmJ,cAAc;QACjC5R,MAAM,CAAC0E,MAAM,CAAC,YAAY,GAAG2M,OAAO,GAAG,kCAAkC,GAC3D,mDAAmD,CAAC;MACpE;MAEA,OAAO,IAAIrR,MAAM,CAACoG,KAAK,CAAC,GAAG,EAAE,uBAAuB,CAAC;IACvD,CAAC;;IAGD;IACA;IACA,IAAIiC,wBAAwB,GAAG,SAAAA,CAAUO,CAAC,EAAEyI,OAAO,EAAE/T,IAAI,EAAEuU,WAAW,EAAE;MACtEvU,IAAI,GAAGA,IAAI,IAAI,EAAE;MACjB,IAAIiF,OAAO,CAAC,uBAAuB,CAAC,EAAE;QACpC,OAAOuP,KAAK,CAACC,gCAAgC,CAC3CnJ,CAAC,EAAEyI,OAAO,EAAE/T,IAAI,EAAEuU,WAAW,CAAC;MAClC;MACA,OAAOjJ,CAAC,CAAC/K,KAAK,CAACwT,OAAO,EAAE/T,IAAI,CAAC;IAC/B,CAAC;IAACQ,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAnE,IAAA;EAAAqE,KAAA;AAAA,G;;;;;;;;;;;ACptDFM,SAAS,CAAC+I,WAAW,GAAG,MAAM;EAC5B2K,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,oBAAoB,GAAG,EAAE;EAChC;EAEAC,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACJ,OAAO,EAAE;MAChB,OAAO;QAAEK,SAAS,EAAEA,CAAA,KAAM,CAAC;MAAE,CAAC;IAChC;IAEA,IAAI,IAAI,CAACN,KAAK,EAAE;MACd,MAAM,IAAI9L,KAAK,CAAC,uDAAuD,CAAC;IAC1E;IAEA,IAAI,CAACgM,kBAAkB,EAAE;IACzB,IAAII,SAAS,GAAG,KAAK;IAErB,OAAO;MACLA,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAIA,SAAS,EAAE;UACb,MAAM,IAAIpM,KAAK,CAAC,0CAA0C,CAAC;QAC7D;QACAoM,SAAS,GAAG,IAAI;QAChB,IAAI,CAACJ,kBAAkB,EAAE;QACzB,MAAM,IAAI,CAACK,UAAU,CAAC,CAAC;MACzB;IACF,CAAC;EACH;EAEA/K,GAAGA,CAAA,EAAG;IACJ,IAAI,IAAI,KAAKpJ,SAAS,CAACU,gBAAgB,CAAC,CAAC,EAAE;MACzC,MAAMoH,KAAK,CAAC,6BAA6B,CAAC;IAC5C;IACA,IAAI,CAAC6L,KAAK,GAAG,IAAI;IACjB,OAAO,IAAI,CAACQ,UAAU,CAAC,CAAC;EAC1B;EAEAC,YAAYA,CAAChD,IAAI,EAAE;IACjB,IAAI,IAAI,CAACwC,KAAK,EAAE;MACd,MAAM,IAAI9L,KAAK,CAAC,2DAA2D,CAAC;IAC9E;IACA,IAAI,CAACiM,qBAAqB,CAAC3Z,IAAI,CAACgX,IAAI,CAAC;EACvC;EAEApI,cAAcA,CAACoI,IAAI,EAAE;IACnB,IAAI,IAAI,CAACwC,KAAK,EAAE;MACd,MAAM,IAAI9L,KAAK,CAAC,2DAA2D,CAAC;IAC9E;IACA,IAAI,CAACkM,oBAAoB,CAAC5Z,IAAI,CAACgX,IAAI,CAAC;EACtC;EAEA,MAAMiD,WAAWA,CAAA,EAAG;IAClB,IAAIC,QAAQ;IACZ,MAAMC,WAAW,GAAG,IAAI5K,OAAO,CAAC4I,CAAC,IAAI+B,QAAQ,GAAG/B,CAAC,CAAC;IAClD,IAAI,CAACvJ,cAAc,CAACsL,QAAQ,CAAC;IAC7B,MAAM,IAAI,CAAClL,GAAG,CAAC,CAAC;IAChB,OAAOmL,WAAW;EACpB;EAEAC,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACH,WAAW,CAAC,CAAC;EAC3B;EAEA,MAAMF,UAAUA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACP,KAAK,EAAE;MACd,MAAM,IAAI9L,KAAK,CAAC,gCAAgC,CAAC;IACnD;IAEA,IAAI,CAAC,IAAI,CAAC6L,KAAK,IAAI,IAAI,CAACG,kBAAkB,GAAG,CAAC,EAAE;MAC9C;IACF;IAEA,MAAMW,cAAc,GAAG,MAAOrD,IAAI,IAAK;MACrC,IAAI;QACF,MAAMA,IAAI,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC,OAAOpB,GAAG,EAAE;QACZtO,MAAM,CAAC0E,MAAM,CAAC,oCAAoC,EAAE4J,GAAG,CAAC;MAC1D;IACF,CAAC;IAED,IAAI,CAAC8D,kBAAkB,EAAE;;IAEzB;IACA,MAAMY,eAAe,GAAG,CAAC,GAAG,IAAI,CAACX,qBAAqB,CAAC;IACvD,IAAI,CAACA,qBAAqB,GAAG,EAAE;IAC/B,MAAMpK,OAAO,CAAC4B,GAAG,CAACmJ,eAAe,CAAClJ,GAAG,CAACzI,EAAE,IAAI0R,cAAc,CAAC1R,EAAE,CAAC,CAAC,CAAC;IAEhE,IAAI,CAAC+Q,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAACA,kBAAkB,KAAK,CAAC,EAAE;MACjC,IAAI,CAACF,KAAK,GAAG,IAAI;MACjB;MACA,MAAM9E,SAAS,GAAG,CAAC,GAAG,IAAI,CAACkF,oBAAoB,CAAC;MAChD,IAAI,CAACA,oBAAoB,GAAG,EAAE;MAC9B,MAAMrK,OAAO,CAAC4B,GAAG,CAACuD,SAAS,CAACtD,GAAG,CAACzI,EAAE,IAAI0R,cAAc,CAAC1R,EAAE,CAAC,CAAC,CAAC;IAC5D;EACF;EAEAkG,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC2K,KAAK,EAAE;MACf,MAAM,IAAI9L,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IACA,IAAI,CAAC+L,OAAO,GAAG,IAAI;EACrB;AACF,CAAC;AAED7T,SAAS,CAACY,kBAAkB,GAAG,IAAIc,MAAM,CAACiT,mBAAmB,CAAD,CAAC,C;;;;;;;;;;;AC/G7D;AACA;AACA;;AAEA3U,SAAS,CAAC4U,SAAS,GAAG,UAAUvT,OAAO,EAAE;EACvC,IAAIhG,IAAI,GAAG,IAAI;EACfgG,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvBhG,IAAI,CAACwZ,MAAM,GAAG,CAAC;EACf;EACA;EACA;EACAxZ,IAAI,CAACyZ,qBAAqB,GAAG,CAAC,CAAC;EAC/BzZ,IAAI,CAAC0Z,0BAA0B,GAAG,CAAC,CAAC;EACpC1Z,IAAI,CAAC2Z,WAAW,GAAG3T,OAAO,CAAC2T,WAAW,IAAI,UAAU;EACpD3Z,IAAI,CAAC4Z,QAAQ,GAAG5T,OAAO,CAAC4T,QAAQ,IAAI,IAAI;AAC1C,CAAC;AAED9W,MAAM,CAACC,MAAM,CAAC4B,SAAS,CAAC4U,SAAS,CAACvW,SAAS,EAAE;EAC3C;EACA6W,qBAAqB,EAAE,SAAAA,CAAU5R,GAAG,EAAE;IACpC,IAAIjI,IAAI,GAAG,IAAI;IACf,IAAI,EAAE,YAAY,IAAIiI,GAAG,CAAC,EAAE;MAC1B,OAAO,EAAE;IACX,CAAC,MAAM,IAAI,OAAOA,GAAG,CAACuB,UAAW,KAAK,QAAQ,EAAE;MAC9C,IAAIvB,GAAG,CAACuB,UAAU,KAAK,EAAE,EACvB,MAAMiD,KAAK,CAAC,+BAA+B,CAAC;MAC9C,OAAOxE,GAAG,CAACuB,UAAU;IACvB,CAAC,MAAM;MACL,MAAMiD,KAAK,CAAC,oCAAoC,CAAC;IACnD;EACF,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAqN,MAAM,EAAE,SAAAA,CAAUC,OAAO,EAAElX,QAAQ,EAAE;IACnC,IAAI7C,IAAI,GAAG,IAAI;IACf,IAAIiG,EAAE,GAAGjG,IAAI,CAACwZ,MAAM,EAAE;IAEtB,IAAIhQ,UAAU,GAAGxJ,IAAI,CAAC6Z,qBAAqB,CAACE,OAAO,CAAC;IACpD,IAAIC,MAAM,GAAG;MAACD,OAAO,EAAExH,KAAK,CAACC,KAAK,CAACuH,OAAO,CAAC;MAAElX,QAAQ,EAAEA;IAAQ,CAAC;IAChE,IAAI,EAAG2G,UAAU,IAAIxJ,IAAI,CAACyZ,qBAAqB,CAAC,EAAE;MAChDzZ,IAAI,CAACyZ,qBAAqB,CAACjQ,UAAU,CAAC,GAAG,CAAC,CAAC;MAC3CxJ,IAAI,CAAC0Z,0BAA0B,CAAClQ,UAAU,CAAC,GAAG,CAAC;IACjD;IACAxJ,IAAI,CAACyZ,qBAAqB,CAACjQ,UAAU,CAAC,CAACvD,EAAE,CAAC,GAAG+T,MAAM;IACnDha,IAAI,CAAC0Z,0BAA0B,CAAClQ,UAAU,CAAC,EAAE;IAE7C,IAAIxJ,IAAI,CAAC4Z,QAAQ,IAAIhR,OAAO,CAAC,YAAY,CAAC,EAAE;MAC1CA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CAC7C9I,IAAI,CAAC2Z,WAAW,EAAE3Z,IAAI,CAAC4Z,QAAQ,EAAE,CAAC,CAAC;IACvC;IAEA,OAAO;MACLlP,IAAI,EAAE,SAAAA,CAAA,EAAY;QAChB,IAAI1K,IAAI,CAAC4Z,QAAQ,IAAIhR,OAAO,CAAC,YAAY,CAAC,EAAE;UAC1CA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CAC7C9I,IAAI,CAAC2Z,WAAW,EAAE3Z,IAAI,CAAC4Z,QAAQ,EAAE,CAAC,CAAC,CAAC;QACxC;QACA,OAAO5Z,IAAI,CAACyZ,qBAAqB,CAACjQ,UAAU,CAAC,CAACvD,EAAE,CAAC;QACjDjG,IAAI,CAAC0Z,0BAA0B,CAAClQ,UAAU,CAAC,EAAE;QAC7C,IAAIxJ,IAAI,CAAC0Z,0BAA0B,CAAClQ,UAAU,CAAC,KAAK,CAAC,EAAE;UACrD,OAAOxJ,IAAI,CAACyZ,qBAAqB,CAACjQ,UAAU,CAAC;UAC7C,OAAOxJ,IAAI,CAAC0Z,0BAA0B,CAAClQ,UAAU,CAAC;QACpD;MACF;IACF,CAAC;EACH,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAyQ,IAAI,EAAE,eAAAA,CAAgBC,YAAY,EAAE;IAClC,IAAIla,IAAI,GAAG,IAAI;IAEf,IAAIwJ,UAAU,GAAGxJ,IAAI,CAAC6Z,qBAAqB,CAACK,YAAY,CAAC;IAEzD,IAAI,EAAE1Q,UAAU,IAAIxJ,IAAI,CAACyZ,qBAAqB,CAAC,EAAE;MAC/C;IACF;IAEA,IAAIU,sBAAsB,GAAGna,IAAI,CAACyZ,qBAAqB,CAACjQ,UAAU,CAAC;IACnE,IAAI4Q,WAAW,GAAG,EAAE;IACpBtX,MAAM,CAAC2S,OAAO,CAAC0E,sBAAsB,CAAC,CAACvX,OAAO,CAAC,UAAAwN,IAAA,EAAmB;MAAA,IAAT,CAACnK,EAAE,EAAEoU,CAAC,CAAC,GAAAjK,IAAA;MAC9D,IAAIpQ,IAAI,CAACsa,QAAQ,CAACJ,YAAY,EAAEG,CAAC,CAACN,OAAO,CAAC,EAAE;QAC1CK,WAAW,CAACrb,IAAI,CAACkH,EAAE,CAAC;MACtB;IACF,CAAC,CAAC;;IAEF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,MAAMA,EAAE,IAAImU,WAAW,EAAE;MAC5B,IAAInU,EAAE,IAAIkU,sBAAsB,EAAE;QAChC,MAAMA,sBAAsB,CAAClU,EAAE,CAAC,CAACpD,QAAQ,CAACqX,YAAY,CAAC;MACzD;IACF;EACF,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAI,QAAQ,EAAE,SAAAA,CAAUJ,YAAY,EAAEH,OAAO,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI,OAAOG,YAAY,CAACjU,EAAG,KAAK,QAAQ,IACpC,OAAO8T,OAAO,CAAC9T,EAAG,KAAK,QAAQ,IAC/BiU,YAAY,CAACjU,EAAE,KAAK8T,OAAO,CAAC9T,EAAE,EAAE;MAClC,OAAO,KAAK;IACd;IACA,IAAIiU,YAAY,CAACjU,EAAE,YAAYkM,OAAO,CAACoI,QAAQ,IAC3CR,OAAO,CAAC9T,EAAE,YAAYkM,OAAO,CAACoI,QAAQ,IACtC,CAAEL,YAAY,CAACjU,EAAE,CAACuU,MAAM,CAACT,OAAO,CAAC9T,EAAE,CAAC,EAAE;MACxC,OAAO,KAAK;IACd;IAEA,OAAOnD,MAAM,CAAC2X,IAAI,CAACV,OAAO,CAAC,CAAC5G,KAAK,CAAC,UAAUwC,GAAG,EAAE;MAC/C,OAAO,EAAEA,GAAG,IAAIuE,YAAY,CAAC,IAAI3H,KAAK,CAACiI,MAAM,CAACT,OAAO,CAACpE,GAAG,CAAC,EAAEuE,YAAY,CAACvE,GAAG,CAAC,CAAC;IAC/E,CAAC,CAAC;EACL;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACAhR,SAAS,CAAC+V,qBAAqB,GAAG,IAAI/V,SAAS,CAAC4U,SAAS,CAAC;EACxDK,QAAQ,EAAE;AACZ,CAAC,CAAC,C;;;;;;;;;;;ACrKF,IAAIlb,OAAO,CAACC,GAAG,CAACgc,0BAA0B,EAAE;EAC1C9a,yBAAyB,CAAC8a,0BAA0B,GAClDjc,OAAO,CAACC,GAAG,CAACgc,0BAA0B;AAC1C;AAEAtU,MAAM,CAACnF,MAAM,GAAG,IAAI8S,MAAM,CAAC,CAAC;AAE5B3N,MAAM,CAACuU,OAAO,GAAG,gBAAgBV,YAAY,EAAE;EAC7C,MAAMvV,SAAS,CAAC+V,qBAAqB,CAACT,IAAI,CAACC,YAAY,CAAC;AAC1D,CAAC;;AAED;AACA;;AAEE,CACE,SAAS,EACT,aAAa,EACb,SAAS,EACT,MAAM,EACN,WAAW,EACX,OAAO,EACP,YAAY,EACZ,cAAc,EACd,WAAW,CACZ,CAACtX,OAAO,CACT,UAASwJ,IAAI,EAAE;EACb/F,MAAM,CAAC+F,IAAI,CAAC,GAAG/F,MAAM,CAACnF,MAAM,CAACkL,IAAI,CAAC,CAACvC,IAAI,CAACxD,MAAM,CAACnF,MAAM,CAAC;AACxD,CACF,CAAC,C;;;;;;;;;;;ACnBDlD,MAAM,CAAA6c,MAAO;EAAAC,iBAAiB,EAAAA,CAAA,KAAAA;AAAA;AAAxB,MAAOA,iBAAiB;EAI5BzC,YAAA;IAAA,KAHQ0C,QAAQ;IAAA,KACRC,SAAS;IAGf,IAAI,CAACD,QAAQ,GAAG,IAAIjH,GAAG,EAAU,CAAC,CAAC;IACnC,IAAI,CAACkH,SAAS,GAAG,IAAIrU,GAAG,EAAuB,CAAC,CAAC;EACnD;EAEAkJ,SAASA,CAAA;IACP,OAAO,EAAE;EACX;EAEAoL,UAAUA,CACR9Q,kBAA0B,EAC1BwL,GAAW,EACXuF,eAAgC;IAEhCA,eAAe,CAACvF,GAAG,CAAC,GAAG/P,SAAS;EAClC;EAEAuV,WAAWA,CACThR,kBAA0B,EAC1BwL,GAAW,EACXnT,KAAU,EACV0Y,eAAgC,EAChCE,KAAe;IAEfF,eAAe,CAACvF,GAAG,CAAC,GAAGnT,KAAK;EAC9B;;;;;;;;;;;;;;;ICtCFxE,MAAA,CAAO6c,MAAE;MAAApW,qBAAyB,EAAAA,CAAA,KAAAA;IAAwB;IAAA,IAAAqW,iBAAA;IAAA9c,MAAA,CAAAC,IAAA;MAAA6c,kBAAA3c,CAAA;QAAA2c,iBAAA,GAAA3c,CAAA;MAAA;IAAA;IAAA,IAAAuG,mBAAA;IAAA1G,MAAA,CAAAC,IAAA;MAAAyG,oBAAAvG,CAAA;QAAAuG,mBAAA,GAAAvG,CAAA;MAAA;IAAA;IAAA,IAAAG,oBAAA,WAAAA,oBAAA;IAWpD,MAAOmG,qBAAqB;MAKhC;;;;;MAKA4T,YAAYjP,cAAsB,EAAEiS,gBAAkC;QAAA,KATrDjS,cAAc;QAAA,KACduG,SAAS;QAAA,KACT8D,SAAS;QAQxB,IAAI,CAACrK,cAAc,GAAGA,cAAc;QACpC,IAAI,CAACuG,SAAS,GAAG,IAAIhJ,GAAG,EAAE;QAC1B,IAAI,CAAC8M,SAAS,GAAG4H,gBAAgB;MACnC;MAEO/W,OAAOA,CAAA;QACZ,OAAO,IAAI,CAACqL,SAAS,CAAC2L,IAAI,KAAK,CAAC;MAClC;MAEO7L,IAAIA,CAAC8L,QAA+B;QACzCnM,YAAY,CAACC,QAAQ,CAACkM,QAAQ,CAAC5L,SAAS,EAAE,IAAI,CAACA,SAAS,EAAE;UACxDL,IAAI,EAAE,IAAI,CAACkM,YAAY,CAAC3R,IAAI,CAAC,IAAI,CAAC;UAClC6F,SAAS,EAAEA,CAACzJ,EAAU,EAAEwV,KAAmB,KAAI;YAC7C,IAAI,CAAChI,SAAS,CAAC7J,KAAK,CAAC,IAAI,CAACR,cAAc,EAAEnD,EAAE,EAAEwV,KAAK,CAAC5L,SAAS,EAAE,CAAC;UAClE,CAAC;UACDC,QAAQ,EAAEA,CAAC7J,EAAU,EAAEyV,MAAoB,KAAI;YAC7C,IAAI,CAACjI,SAAS,CAAC1J,OAAO,CAAC,IAAI,CAACX,cAAc,EAAEnD,EAAE,CAAC;UACjD;SACD,CAAC;MACJ;MAEQuV,YAAYA,CAACvV,EAAU,EAAEyV,MAAoB,EAAED,KAAmB;QACxE,MAAMlS,MAAM,GAAwB,EAAE;QAEtC6F,YAAY,CAACuM,WAAW,CAACD,MAAM,CAAC7L,SAAS,EAAE,EAAE4L,KAAK,CAAC5L,SAAS,EAAE,EAAE;UAC9DP,IAAI,EAAEA,CAACqG,GAAW,EAAEiG,IAAS,EAAEC,GAAQ,KAAI;YACzC,IAAI,CAACtJ,KAAK,CAACiI,MAAM,CAACoB,IAAI,EAAEC,GAAG,CAAC,EAAE;cAC5BtS,MAAM,CAACoM,GAAG,CAAC,GAAGkG,GAAG;YACnB;UACF,CAAC;UACDnM,SAAS,EAAEA,CAACiG,GAAW,EAAEkG,GAAQ,KAAI;YACnCtS,MAAM,CAACoM,GAAG,CAAC,GAAGkG,GAAG;UACnB,CAAC;UACD/L,QAAQ,EAAEA,CAAC6F,GAAW,EAAEiG,IAAS,KAAI;YACnCrS,MAAM,CAACoM,GAAG,CAAC,GAAG/P,SAAS;UACzB;SACD,CAAC;QAEF,IAAI,CAAC6N,SAAS,CAAC3J,OAAO,CAAC,IAAI,CAACV,cAAc,EAAEnD,EAAE,EAAEsD,MAAM,CAAC;MACzD;MAEOK,KAAKA,CAACO,kBAA0B,EAAElE,EAAU,EAAEsD,MAA2B;QAC9E,IAAIqG,OAAO,GAA6B,IAAI,CAACD,SAAS,CAACnK,GAAG,CAACS,EAAE,CAAC;QAC9D,IAAI2D,KAAK,GAAG,KAAK;QAEjB,IAAI,CAACgG,OAAO,EAAE;UACZhG,KAAK,GAAG,IAAI;UACZ,IAAIvD,MAAM,CAACnF,MAAM,CAACmI,sBAAsB,CAAC,IAAI,CAACD,cAAc,CAAC,CAACtE,oBAAoB,EAAE;YAClF8K,OAAO,GAAG,IAAIkL,iBAAiB,EAAE;UACnC,CAAC,MAAM;YACLlL,OAAO,GAAG,IAAIlL,mBAAmB,EAAE;UACrC;UACA,IAAI,CAACiL,SAAS,CAACzF,GAAG,CAACjE,EAAE,EAAE2J,OAAO,CAAC;QACjC;QAEAA,OAAO,CAACmL,QAAQ,CAAChH,GAAG,CAAC5J,kBAAkB,CAAC;QACxC,MAAM+Q,eAAe,GAAwB,EAAE;QAE/CpY,MAAM,CAAC2S,OAAO,CAAClM,MAAM,CAAC,CAAC3G,OAAO,CAACwN,IAAA,IAAiB;UAAA,IAAhB,CAACuF,GAAG,EAAEnT,KAAK,CAAC,GAAA4N,IAAA;UAC1CR,OAAQ,CAACuL,WAAW,CAClBhR,kBAAkB,EAClBwL,GAAG,EACHnT,KAAK,EACL0Y,eAAe,EACf,IAAI,CACL;QACH,CAAC,CAAC;QAEF,IAAItR,KAAK,EAAE;UACT,IAAI,CAAC6J,SAAS,CAAC7J,KAAK,CAAC,IAAI,CAACR,cAAc,EAAEnD,EAAE,EAAEiV,eAAe,CAAC;QAChE,CAAC,MAAM;UACL,IAAI,CAACzH,SAAS,CAAC3J,OAAO,CAAC,IAAI,CAACV,cAAc,EAAEnD,EAAE,EAAEiV,eAAe,CAAC;QAClE;MACF;MAEOpR,OAAOA,CAACK,kBAA0B,EAAElE,EAAU,EAAE6D,OAA4B;QACjF,MAAMgS,aAAa,GAAwB,EAAE;QAC7C,MAAMlM,OAAO,GAAG,IAAI,CAACD,SAAS,CAACnK,GAAG,CAACS,EAAE,CAAC;QAEtC,IAAI,CAAC2J,OAAO,EAAE;UACZ,MAAM,IAAInD,KAAK,mCAAAC,MAAA,CAAmCzG,EAAE,eAAY,CAAC;QACnE;QAEAnD,MAAM,CAAC2S,OAAO,CAAC3L,OAAO,CAAC,CAAClH,OAAO,CAAC8S,KAAA,IAAiB;UAAA,IAAhB,CAACC,GAAG,EAAEnT,KAAK,CAAC,GAAAkT,KAAA;UAC3C,IAAIlT,KAAK,KAAKoD,SAAS,EAAE;YACvBgK,OAAO,CAACqL,UAAU,CAAC9Q,kBAAkB,EAAEwL,GAAG,EAAEmG,aAAa,CAAC;UAC5D,CAAC,MAAM;YACLlM,OAAO,CAACuL,WAAW,CAAChR,kBAAkB,EAAEwL,GAAG,EAAEnT,KAAK,EAAEsZ,aAAa,CAAC;UACpE;QACF,CAAC,CAAC;QAEF,IAAI,CAACrI,SAAS,CAAC3J,OAAO,CAAC,IAAI,CAACV,cAAc,EAAEnD,EAAE,EAAE6V,aAAa,CAAC;MAChE;MAEO/R,OAAOA,CAACI,kBAA0B,EAAElE,EAAU;QACnD,MAAM2J,OAAO,GAAG,IAAI,CAACD,SAAS,CAACnK,GAAG,CAACS,EAAE,CAAC;QAEtC,IAAI,CAAC2J,OAAO,EAAE;UACZ,MAAM,IAAInD,KAAK,iCAAAC,MAAA,CAAiCzG,EAAE,CAAE,CAAC;QACvD;QAEA2J,OAAO,CAACmL,QAAQ,CAAC1Q,MAAM,CAACF,kBAAkB,CAAC;QAE3C,IAAIyF,OAAO,CAACmL,QAAQ,CAACO,IAAI,KAAK,CAAC,EAAE;UAC/B;UACA,IAAI,CAAC7H,SAAS,CAAC1J,OAAO,CAAC,IAAI,CAACX,cAAc,EAAEnD,EAAE,CAAC;UAC/C,IAAI,CAAC0J,SAAS,CAACtF,MAAM,CAACpE,EAAE,CAAC;QAC3B,CAAC,MAAM;UACL,MAAM6D,OAAO,GAAwB,EAAE;UACvC;UACA;UACA8F,OAAO,CAACoL,SAAS,CAACpY,OAAO,CAAC,CAACmZ,cAAc,EAAEpG,GAAG,KAAI;YAChD/F,OAAO,CAACqL,UAAU,CAAC9Q,kBAAkB,EAAEwL,GAAG,EAAE7L,OAAO,CAAC;UACtD,CAAC,CAAC;UACF,IAAI,CAAC2J,SAAS,CAAC3J,OAAO,CAAC,IAAI,CAACV,cAAc,EAAEnD,EAAE,EAAE6D,OAAO,CAAC;QAC1D;MACF;;IACD3F,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAnE,IAAA;EAAAqE,KAAA;AAAA,G;;;;;;;;;;;AClIDrG,MAAM,CAAA6c,MAAO;EAAAnW,mBAAmB,EAAAA,CAAA,KAAAA;AAAA;AAA1B,MAAOA,mBAAmB;EAI9B2T,YAAA;IAAA,KAHQ0C,QAAQ;IAAA,KACRC,SAAS;IAGf,IAAI,CAACD,QAAQ,GAAG,IAAIjH,GAAG,EAAE,CAAC,CAAC;IAC3B;IACA,IAAI,CAACkH,SAAS,GAAG,IAAIrU,GAAG,EAAE,CAAC,CAAC;EAC9B;EAEAkJ,SAASA,CAAA;IACP,MAAM5F,GAAG,GAAwB,EAAE;IACnC,IAAI,CAAC+Q,SAAS,CAACpY,OAAO,CAAC,CAACmZ,cAAc,EAAEpG,GAAG,KAAI;MAC7C1L,GAAG,CAAC0L,GAAG,CAAC,GAAGoG,cAAc,CAAC,CAAC,CAAC,CAACvZ,KAAK;IACpC,CAAC,CAAC;IACF,OAAOyH,GAAG;EACZ;EAEAgR,UAAUA,CACR9Q,kBAA0B,EAC1BwL,GAAW,EACXuF,eAAgC;IAEhC;IACA,IAAIvF,GAAG,KAAK,KAAK,EAAE;IAEnB,MAAMoG,cAAc,GAAG,IAAI,CAACf,SAAS,CAACxV,GAAG,CAACmQ,GAAG,CAAC;IAC9C;IACA;IACA,IAAI,CAACoG,cAAc,EAAE;IAErB,IAAIC,YAAY,GAAQpW,SAAS;IAEjC,KAAK,IAAIyN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0I,cAAc,CAACzK,MAAM,EAAE+B,CAAC,EAAE,EAAE;MAC9C,MAAM4I,UAAU,GAAGF,cAAc,CAAC1I,CAAC,CAAC;MACpC,IAAI4I,UAAU,CAAC9R,kBAAkB,KAAKA,kBAAkB,EAAE;QACxD;QACA;QACA,IAAIkJ,CAAC,KAAK,CAAC,EAAE2I,YAAY,GAAGC,UAAU,CAACzZ,KAAK;QAC5CuZ,cAAc,CAACG,MAAM,CAAC7I,CAAC,EAAE,CAAC,CAAC;QAC3B;MACF;IACF;IAEA,IAAI0I,cAAc,CAACzK,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC0J,SAAS,CAAC3Q,MAAM,CAACsL,GAAG,CAAC;MAC1BuF,eAAe,CAACvF,GAAG,CAAC,GAAG/P,SAAS;IAClC,CAAC,MAAM,IACLoW,YAAY,KAAKpW,SAAS,IAC1B,CAAC2M,KAAK,CAACiI,MAAM,CAACwB,YAAY,EAAED,cAAc,CAAC,CAAC,CAAC,CAACvZ,KAAK,CAAC,EACpD;MACA0Y,eAAe,CAACvF,GAAG,CAAC,GAAGoG,cAAc,CAAC,CAAC,CAAC,CAACvZ,KAAK;IAChD;EACF;EAEA2Y,WAAWA,CACThR,kBAA0B,EAC1BwL,GAAW,EACXnT,KAAU,EACV0Y,eAAgC,EACV;IAAA,IAAtBE,KAAA,GAAAxX,SAAA,CAAA0N,MAAA,QAAA1N,SAAA,QAAAgC,SAAA,GAAAhC,SAAA,MAAiB,KAAK;IAEtB;IACA,IAAI+R,GAAG,KAAK,KAAK,EAAE;IAEnB;IACAnT,KAAK,GAAG+P,KAAK,CAACC,KAAK,CAAChQ,KAAK,CAAC;IAE1B,IAAI,CAAC,IAAI,CAACwY,SAAS,CAACrO,GAAG,CAACgJ,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACqF,SAAS,CAAC9Q,GAAG,CAACyL,GAAG,EAAE,CACtB;QAAExL,kBAAkB,EAAEA,kBAAkB;QAAE3H,KAAK,EAAEA;MAAK,CAAE,CACzD,CAAC;MACF0Y,eAAe,CAACvF,GAAG,CAAC,GAAGnT,KAAK;MAC5B;IACF;IAEA,MAAMuZ,cAAc,GAAG,IAAI,CAACf,SAAS,CAACxV,GAAG,CAACmQ,GAAG,CAAE;IAC/C,IAAIwG,GAA+B;IAEnC,IAAI,CAACf,KAAK,EAAE;MACVe,GAAG,GAAGJ,cAAc,CAACvE,IAAI,CACtByE,UAAU,IAAKA,UAAU,CAAC9R,kBAAkB,KAAKA,kBAAkB,CACrE;IACH;IAEA,IAAIgS,GAAG,EAAE;MACP,IAAIA,GAAG,KAAKJ,cAAc,CAAC,CAAC,CAAC,IAAI,CAACxJ,KAAK,CAACiI,MAAM,CAAChY,KAAK,EAAE2Z,GAAG,CAAC3Z,KAAK,CAAC,EAAE;QAChE;QACA0Y,eAAe,CAACvF,GAAG,CAAC,GAAGnT,KAAK;MAC9B;MACA2Z,GAAG,CAAC3Z,KAAK,GAAGA,KAAK;IACnB,CAAC,MAAM;MACL;MACAuZ,cAAc,CAAChd,IAAI,CAAC;QAAEoL,kBAAkB,EAAEA,kBAAkB;QAAE3H,KAAK,EAAEA;MAAK,CAAE,CAAC;IAC/E;EACF", "file": "/packages/ddp-server.js", "sourcesContent": ["import once from 'lodash.once';\nimport zlib from 'node:zlib';\n\n// By default, we use the permessage-deflate extension with default\n// configuration. If $SERVER_WEBSOCKET_COMPRESSION is set, then it must be valid\n// JSON. If it represents a falsey value, then we do not use permessage-deflate\n// at all; otherwise, the JSON value is used as an argument to deflate's\n// configure method; see\n// https://github.com/faye/permessage-deflate-node/blob/master/README.md\n//\n// (We do this in an _.once instead of at startup, because we don't want to\n// crash the tool during isopacket load if your JSON doesn't parse. This is only\n// a problem because the tool has to load the DDP server code just in order to\n// be a DDP client; see https://github.com/meteor/meteor/issues/3452 .)\nvar websocketExtensions = once(function () {\n  var extensions = [];\n\n  var websocketCompressionConfig = process.env.SERVER_WEBSOCKET_COMPRESSION ?\n    JSON.parse(process.env.SERVER_WEBSOCKET_COMPRESSION) : {};\n\n  if (websocketCompressionConfig) {\n    extensions.push(Npm.require('permessage-deflate2').configure({\n      threshold: 1024,\n      level: zlib.constants.Z_BEST_SPEED,\n      memLevel: zlib.constants.Z_MIN_MEMLEVEL,\n      noContextTakeover: true,\n      maxWindowBits: zlib.constants.Z_MIN_WINDOWBITS,\n      ...(websocketCompressionConfig || {})\n    }));\n  }\n\n  return extensions;\n});\n\nvar pathPrefix = __meteor_runtime_config__.ROOT_URL_PATH_PREFIX ||  \"\";\n\nStreamServer = function () {\n  var self = this;\n  self.registration_callbacks = [];\n  self.open_sockets = [];\n\n  // Because we are installing directly onto WebApp.httpServer instead of using\n  // WebApp.app, we have to process the path prefix ourselves.\n  self.prefix = pathPrefix + '/sockjs';\n  RoutePolicy.declare(self.prefix + '/', 'network');\n\n  // set up sockjs\n  var sockjs = Npm.require('sockjs');\n  var serverOptions = {\n    prefix: self.prefix,\n    log: function() {},\n    // this is the default, but we code it explicitly because we depend\n    // on it in stream_client:HEARTBEAT_TIMEOUT\n    heartbeat_delay: 45000,\n    // The default disconnect_delay is 5 seconds, but if the server ends up CPU\n    // bound for that much time, SockJS might not notice that the user has\n    // reconnected because the timer (of disconnect_delay ms) can fire before\n    // SockJS processes the new connection. Eventually we'll fix this by not\n    // combining CPU-heavy processing with SockJS termination (eg a proxy which\n    // converts to Unix sockets) but for now, raise the delay.\n    disconnect_delay: 60 * 1000,\n    // Allow disabling of CORS requests to address\n    // https://github.com/meteor/meteor/issues/8317.\n    disable_cors: !!process.env.DISABLE_SOCKJS_CORS,\n    // Set the USE_JSESSIONID environment variable to enable setting the\n    // JSESSIONID cookie. This is useful for setting up proxies with\n    // session affinity.\n    jsessionid: !!process.env.USE_JSESSIONID\n  };\n\n  // If you know your server environment (eg, proxies) will prevent websockets\n  // from ever working, set $DISABLE_WEBSOCKETS and SockJS clients (ie,\n  // browsers) will not waste time attempting to use them.\n  // (Your server will still have a /websocket endpoint.)\n  if (process.env.DISABLE_WEBSOCKETS) {\n    serverOptions.websocket = false;\n  } else {\n    serverOptions.faye_server_options = {\n      extensions: websocketExtensions()\n    };\n  }\n\n  self.server = sockjs.createServer(serverOptions);\n\n  // Install the sockjs handlers, but we want to keep around our own particular\n  // request handler that adjusts idle timeouts while we have an outstanding\n  // request.  This compensates for the fact that sockjs removes all listeners\n  // for \"request\" to add its own.\n  WebApp.httpServer.removeListener(\n    'request', WebApp._timeoutAdjustmentRequestCallback);\n  self.server.installHandlers(WebApp.httpServer);\n  WebApp.httpServer.addListener(\n    'request', WebApp._timeoutAdjustmentRequestCallback);\n\n  // Support the /websocket endpoint\n  self._redirectWebsocketEndpoint();\n\n  self.server.on('connection', function (socket) {\n    // sockjs sometimes passes us null instead of a socket object\n    // so we need to guard against that. see:\n    // https://github.com/sockjs/sockjs-node/issues/121\n    // https://github.com/meteor/meteor/issues/10468\n    if (!socket) return;\n\n    // We want to make sure that if a client connects to us and does the initial\n    // Websocket handshake but never gets to the DDP handshake, that we\n    // eventually kill the socket.  Once the DDP handshake happens, DDP\n    // heartbeating will work. And before the Websocket handshake, the timeouts\n    // we set at the server level in webapp_server.js will work. But\n    // faye-websocket calls setTimeout(0) on any socket it takes over, so there\n    // is an \"in between\" state where this doesn't happen.  We work around this\n    // by explicitly setting the socket timeout to a relatively large time here,\n    // and setting it back to zero when we set up the heartbeat in\n    // livedata_server.js.\n    socket.setWebsocketTimeout = function (timeout) {\n      if ((socket.protocol === 'websocket' ||\n           socket.protocol === 'websocket-raw')\n          && socket._session.recv) {\n        socket._session.recv.connection.setTimeout(timeout);\n      }\n    };\n    socket.setWebsocketTimeout(45 * 1000);\n\n    socket.send = function (data) {\n      socket.write(data);\n    };\n    socket.on('close', function () {\n      self.open_sockets = self.open_sockets.filter(function(value) {\n        return value !== socket;\n      });\n    });\n    self.open_sockets.push(socket);\n\n    // only to send a message after connection on tests, useful for\n    // socket-stream-client/server-tests.js\n    if (process.env.TEST_METADATA && process.env.TEST_METADATA !== \"{}\") {\n      socket.send(JSON.stringify({ testMessageOnConnect: true }));\n    }\n\n    // call all our callbacks when we get a new socket. they will do the\n    // work of setting up handlers and such for specific messages.\n    self.registration_callbacks.forEach(function (callback) {\n      callback(socket);\n    });\n  });\n\n};\n\nObject.assign(StreamServer.prototype, {\n  // call my callback when a new socket connects.\n  // also call it for all current connections.\n  register: function (callback) {\n    var self = this;\n    self.registration_callbacks.push(callback);\n    self.all_sockets().forEach(function (socket) {\n      callback(socket);\n    });\n  },\n\n  // get a list of all sockets\n  all_sockets: function () {\n    var self = this;\n    return Object.values(self.open_sockets);\n  },\n\n  // Redirect /websocket to /sockjs/websocket in order to not expose\n  // sockjs to clients that want to use raw websockets\n  _redirectWebsocketEndpoint: function() {\n    var self = this;\n    // Unfortunately we can't use a connect middleware here since\n    // sockjs installs itself prior to all existing listeners\n    // (meaning prior to any connect middlewares) so we need to take\n    // an approach similar to overshadowListeners in\n    // https://github.com/sockjs/sockjs-node/blob/cf820c55af6a9953e16558555a31decea554f70e/src/utils.coffee\n    ['request', 'upgrade'].forEach((event) => {\n      var httpServer = WebApp.httpServer;\n      var oldHttpServerListeners = httpServer.listeners(event).slice(0);\n      httpServer.removeAllListeners(event);\n\n      // request and upgrade have different arguments passed but\n      // we only care about the first one which is always request\n      var newListener = function(request /*, moreArguments */) {\n        // Store arguments for use within the closure below\n        var args = arguments;\n\n        // TODO replace with url package\n        var url = Npm.require('url');\n\n        // Rewrite /websocket and /websocket/ urls to /sockjs/websocket while\n        // preserving query string.\n        var parsedUrl = url.parse(request.url);\n        if (parsedUrl.pathname === pathPrefix + '/websocket' ||\n            parsedUrl.pathname === pathPrefix + '/websocket/') {\n          parsedUrl.pathname = self.prefix + '/websocket';\n          request.url = url.format(parsedUrl);\n        }\n        oldHttpServerListeners.forEach(function(oldListener) {\n          oldListener.apply(httpServer, args);\n        });\n      };\n      httpServer.addListener(event, newListener);\n    });\n  }\n});", "import isEmpty from 'lodash.isempty';\nimport isObject from 'lodash.isobject';\nimport isString from 'lodash.isstring';\nimport { SessionCollectionView } from './session_collection_view';\nimport { SessionDocumentView } from './session_document_view';\n\nDDPServer = {};\n\n\n// Publication strategies define how we handle data from published cursors at the collection level\n// This allows someone to:\n// - Choose a trade-off between client-server bandwidth and server memory usage\n// - Implement special (non-mongo) collections like volatile message queues\nconst publicationStrategies = {\n  // SERVER_MERGE is the default strategy.\n  // When using this strategy, the server maintains a copy of all data a connection is subscribed to.\n  // This allows us to only send deltas over multiple publications.\n  SERVER_MERGE: {\n    useDummyDocumentView: false,\n    useCollectionView: true,\n    doAccountingForCollection: true,\n  },\n  // The NO_MERGE_NO_HISTORY strategy results in the server sending all publication data\n  // directly to the client. It does not remember what it has previously sent\n  // to it will not trigger removed messages when a subscription is stopped.\n  // This should only be chosen for special use cases like send-and-forget queues.\n  NO_MERGE_NO_HISTORY: {\n    useDummyDocumentView: false,\n    useCollectionView: false,\n    doAccountingForCollection: false,\n  },\n  // NO_MERGE is similar to NO_MERGE_NO_HISTORY but the server will remember the IDs it has\n  // sent to the client so it can remove them when a subscription is stopped.\n  // This strategy can be used when a collection is only used in a single publication.\n  NO_MERGE: {\n    useDummyDocumentView: false,\n    useCollectionView: false,\n    doAccountingForCollection: true,\n  },\n  // NO_MERGE_MULTI is similar to `NO_MERGE`, but it does track whether a document is\n  // used by multiple publications. This has some memory overhead, but it still does not do\n  // diffing so it's faster and slimmer than SERVER_MERGE.\n  NO_MERGE_MULTI: {\n    useDummyDocumentView: true,\n    useCollectionView: true,\n    doAccountingForCollection: true\n  }\n};\n\nDDPServer.publicationStrategies = publicationStrategies;\n\n// This file contains classes:\n// * Session - The server's connection to a single DDP client\n// * Subscription - A single subscription for a single client\n// * Server - An entire server that may talk to > 1 client. A DDP endpoint.\n//\n// Session and Subscription are file scope. For now, until we freeze\n// the interface, Server is package scope (in the future it should be\n// exported).\n\n\nDDPServer._SessionDocumentView = SessionDocumentView;\n\nDDPServer._getCurrentFence = function () {\n  let currentInvocation = this._CurrentWriteFence.get();\n  if (currentInvocation) {\n    return currentInvocation;\n  }\n  currentInvocation = DDP._CurrentMethodInvocation.get();\n  return currentInvocation ? currentInvocation.fence : undefined;\n};\n\n\nDDPServer._SessionCollectionView = SessionCollectionView;\n\n/******************************************************************************/\n/* Session                                                                    */\n/******************************************************************************/\n\nvar Session = function (server, version, socket, options) {\n  var self = this;\n  self.id = Random.id();\n\n  self.server = server;\n  self.version = version;\n\n  self.initialized = false;\n  self.socket = socket;\n\n  // Set to null when the session is destroyed. Multiple places below\n  // use this to determine if the session is alive or not.\n  self.inQueue = new Meteor._DoubleEndedQueue();\n\n  self.blocked = false;\n  self.workerRunning = false;\n\n  self.cachedUnblock = null;\n\n  // Sub objects for active subscriptions\n  self._namedSubs = new Map();\n  self._universalSubs = [];\n\n  self.userId = null;\n\n  self.collectionViews = new Map();\n\n  // Set this to false to not send messages when collectionViews are\n  // modified. This is done when rerunning subs in _setUserId and those messages\n  // are calculated via a diff instead.\n  self._isSending = true;\n\n  // If this is true, don't start a newly-created universal publisher on this\n  // session. The session will take care of starting it when appropriate.\n  self._dontStartNewUniversalSubs = false;\n\n  // When we are rerunning subscriptions, any ready messages\n  // we want to buffer up for when we are done rerunning subscriptions\n  self._pendingReady = [];\n\n  // List of callbacks to call when this connection is closed.\n  self._closeCallbacks = [];\n\n\n  // XXX HACK: If a sockjs connection, save off the URL. This is\n  // temporary and will go away in the near future.\n  self._socketUrl = socket.url;\n\n  // Allow tests to disable responding to pings.\n  self._respondToPings = options.respondToPings;\n\n  // This object is the public interface to the session. In the public\n  // API, it is called the `connection` object.  Internally we call it\n  // a `connectionHandle` to avoid ambiguity.\n  self.connectionHandle = {\n    id: self.id,\n    close: function () {\n      self.close();\n    },\n    onClose: function (fn) {\n      var cb = Meteor.bindEnvironment(fn, \"connection onClose callback\");\n      if (self.inQueue) {\n        self._closeCallbacks.push(cb);\n      } else {\n        // if we're already closed, call the callback.\n        Meteor.defer(cb);\n      }\n    },\n    clientAddress: self._clientAddress(),\n    httpHeaders: self.socket.headers\n  };\n\n  self.send({ msg: 'connected', session: self.id });\n\n  // On initial connect, spin up all the universal publishers.\n  self.startUniversalSubs();\n\n  if (version !== 'pre1' && options.heartbeatInterval !== 0) {\n    // We no longer need the low level timeout because we have heartbeats.\n    socket.setWebsocketTimeout(0);\n\n    self.heartbeat = new DDPCommon.Heartbeat({\n      heartbeatInterval: options.heartbeatInterval,\n      heartbeatTimeout: options.heartbeatTimeout,\n      onTimeout: function () {\n        self.close();\n      },\n      sendPing: function () {\n        self.send({msg: 'ping'});\n      }\n    });\n    self.heartbeat.start();\n  }\n\n  Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n    \"livedata\", \"sessions\", 1);\n};\n\nObject.assign(Session.prototype, {\n  sendReady: function (subscriptionIds) {\n    var self = this;\n    if (self._isSending) {\n      self.send({msg: \"ready\", subs: subscriptionIds});\n    } else {\n      subscriptionIds.forEach(function (subscriptionId) {\n        self._pendingReady.push(subscriptionId);\n      });\n    }\n  },\n\n  _canSend(collectionName) {\n    return this._isSending || !this.server.getPublicationStrategy(collectionName).useCollectionView;\n  },\n\n\n  sendAdded(collectionName, id, fields) {\n    if (this._canSend(collectionName)) {\n      this.send({ msg: 'added', collection: collectionName, id, fields });\n    }\n  },\n\n  sendChanged(collectionName, id, fields) {\n    if (isEmpty(fields))\n      return;\n\n    if (this._canSend(collectionName)) {\n      this.send({\n        msg: \"changed\",\n        collection: collectionName,\n        id,\n        fields\n      });\n    }\n  },\n\n  sendRemoved(collectionName, id) {\n    if (this._canSend(collectionName)) {\n      this.send({msg: \"removed\", collection: collectionName, id});\n    }\n  },\n\n  getSendCallbacks: function () {\n    var self = this;\n    return {\n      added: self.sendAdded.bind(self),\n      changed: self.sendChanged.bind(self),\n      removed: self.sendRemoved.bind(self)\n    };\n  },\n\n  getCollectionView: function (collectionName) {\n    var self = this;\n    var ret = self.collectionViews.get(collectionName);\n    if (!ret) {\n      ret = new SessionCollectionView(collectionName,\n                                        self.getSendCallbacks());\n      self.collectionViews.set(collectionName, ret);\n    }\n    return ret;\n  },\n\n  added(subscriptionHandle, collectionName, id, fields) {\n    if (this.server.getPublicationStrategy(collectionName).useCollectionView) {\n      const view = this.getCollectionView(collectionName);\n      view.added(subscriptionHandle, id, fields);\n    } else {\n      this.sendAdded(collectionName, id, fields);\n    }\n  },\n\n  removed(subscriptionHandle, collectionName, id) {\n    if (this.server.getPublicationStrategy(collectionName).useCollectionView) {\n      const view = this.getCollectionView(collectionName);\n      view.removed(subscriptionHandle, id);\n      if (view.isEmpty()) {\n         this.collectionViews.delete(collectionName);\n      }\n    } else {\n      this.sendRemoved(collectionName, id);\n    }\n  },\n\n  changed(subscriptionHandle, collectionName, id, fields) {\n    if (this.server.getPublicationStrategy(collectionName).useCollectionView) {\n      const view = this.getCollectionView(collectionName);\n      view.changed(subscriptionHandle, id, fields);\n    } else {\n      this.sendChanged(collectionName, id, fields);\n    }\n  },\n\n  startUniversalSubs: function () {\n    var self = this;\n    // Make a shallow copy of the set of universal handlers and start them. If\n    // additional universal publishers start while we're running them (due to\n    // yielding), they will run separately as part of Server.publish.\n    var handlers = [...self.server.universal_publish_handlers];\n    handlers.forEach(function (handler) {\n      self._startSubscription(handler);\n    });\n  },\n\n  // Destroy this session and unregister it at the server.\n  close: function () {\n    var self = this;\n\n    // Destroy this session, even if it's not registered at the\n    // server. Stop all processing and tear everything down. If a socket\n    // was attached, close it.\n\n    // Already destroyed.\n    if (! self.inQueue)\n      return;\n\n    // Drop the merge box data immediately.\n    self.inQueue = null;\n    self.collectionViews = new Map();\n\n    if (self.heartbeat) {\n      self.heartbeat.stop();\n      self.heartbeat = null;\n    }\n\n    if (self.socket) {\n      self.socket.close();\n      self.socket._meteorSession = null;\n    }\n\n    Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n      \"livedata\", \"sessions\", -1);\n\n    Meteor.defer(function () {\n      // Stop callbacks can yield, so we defer this on close.\n      // sub._isDeactivated() detects that we set inQueue to null and\n      // treats it as semi-deactivated (it will ignore incoming callbacks, etc).\n      self._deactivateAllSubscriptions();\n\n      // Defer calling the close callbacks, so that the caller closing\n      // the session isn't waiting for all the callbacks to complete.\n      self._closeCallbacks.forEach(function (callback) {\n        callback();\n      });\n    });\n\n    // Unregister the session.\n    self.server._removeSession(self);\n  },\n\n  // Send a message (doing nothing if no socket is connected right now).\n  // It should be a JSON object (it will be stringified).\n  send: function (msg) {\n    const self = this;\n    if (self.socket) {\n      if (Meteor._printSentDDP)\n        Meteor._debug(\"Sent DDP\", DDPCommon.stringifyDDP(msg));\n      self.socket.send(DDPCommon.stringifyDDP(msg));\n    }\n  },\n\n  // Send a connection error.\n  sendError: function (reason, offendingMessage) {\n    var self = this;\n    var msg = {msg: 'error', reason: reason};\n    if (offendingMessage)\n      msg.offendingMessage = offendingMessage;\n    self.send(msg);\n  },\n\n  // Process 'msg' as an incoming message. As a guard against\n  // race conditions during reconnection, ignore the message if\n  // 'socket' is not the currently connected socket.\n  //\n  // We run the messages from the client one at a time, in the order\n  // given by the client. The message handler is passed an idempotent\n  // function 'unblock' which it may call to allow other messages to\n  // begin running in parallel in another fiber (for example, a method\n  // that wants to yield). Otherwise, it is automatically unblocked\n  // when it returns.\n  //\n  // Actually, we don't have to 'totally order' the messages in this\n  // way, but it's the easiest thing that's correct. (unsub needs to\n  // be ordered against sub, methods need to be ordered against each\n  // other).\n  processMessage: function (msg_in) {\n    var self = this;\n    if (!self.inQueue) // we have been destroyed.\n      return;\n\n    // Respond to ping and pong messages immediately without queuing.\n    // If the negotiated DDP version is \"pre1\" which didn't support\n    // pings, preserve the \"pre1\" behavior of responding with a \"bad\n    // request\" for the unknown messages.\n    //\n    // Fibers are needed because heartbeats use Meteor.setTimeout, which\n    // needs a Fiber. We could actually use regular setTimeout and avoid\n    // these new fibers, but it is easier to just make everything use\n    // Meteor.setTimeout and not think too hard.\n    //\n    // Any message counts as receiving a pong, as it demonstrates that\n    // the client is still alive.\n    if (self.heartbeat) {\n      self.heartbeat.messageReceived();\n    };\n\n    if (self.version !== 'pre1' && msg_in.msg === 'ping') {\n      if (self._respondToPings)\n        self.send({msg: \"pong\", id: msg_in.id});\n      return;\n    }\n    if (self.version !== 'pre1' && msg_in.msg === 'pong') {\n      // Since everything is a pong, there is nothing to do\n      return;\n    }\n\n    self.inQueue.push(msg_in);\n    if (self.workerRunning)\n      return;\n    self.workerRunning = true;\n\n    var processNext = function () {\n      var msg = self.inQueue && self.inQueue.shift();\n\n      if (!msg) {\n        self.workerRunning = false;\n        return;\n      }\n\n      function runHandlers() {\n        var blocked = true;\n\n        var unblock = function () {\n          if (!blocked)\n            return; // idempotent\n          blocked = false;\n          setImmediate(processNext);\n        };\n\n        self.server.onMessageHook.each(function (callback) {\n          callback(msg, self);\n          return true;\n        });\n\n        if (msg.msg in self.protocol_handlers) {\n          const result = self.protocol_handlers[msg.msg].call(\n            self,\n            msg,\n            unblock\n          );\n\n          if (Meteor._isPromise(result)) {\n            result.finally(() => unblock());\n          } else {\n            unblock();\n          }\n        } else {\n          self.sendError('Bad request', msg);\n          unblock(); // in case the handler didn't already do it\n        }\n      }\n\n      runHandlers();\n    };\n\n    processNext();\n  },\n\n  protocol_handlers: {\n    sub: async function (msg, unblock) {\n      var self = this;\n\n      // cacheUnblock temporarly, so we can capture it later\n      // we will use unblock in current eventLoop, so this is safe\n      self.cachedUnblock = unblock;\n\n      // reject malformed messages\n      if (typeof (msg.id) !== \"string\" ||\n          typeof (msg.name) !== \"string\" ||\n          ('params' in msg && !(msg.params instanceof Array))) {\n        self.sendError(\"Malformed subscription\", msg);\n        return;\n      }\n\n      if (!self.server.publish_handlers[msg.name]) {\n        self.send({\n          msg: 'nosub', id: msg.id,\n          error: new Meteor.Error(404, `Subscription '${msg.name}' not found`)});\n        return;\n      }\n\n      if (self._namedSubs.has(msg.id))\n        // subs are idempotent, or rather, they are ignored if a sub\n        // with that id already exists. this is important during\n        // reconnect.\n        return;\n\n      // XXX It'd be much better if we had generic hooks where any package can\n      // hook into subscription handling, but in the mean while we special case\n      // ddp-rate-limiter package. This is also done for weak requirements to\n      // add the ddp-rate-limiter package in case we don't have Accounts. A\n      // user trying to use the ddp-rate-limiter must explicitly require it.\n      if (Package['ddp-rate-limiter']) {\n        var DDPRateLimiter = Package['ddp-rate-limiter'].DDPRateLimiter;\n        var rateLimiterInput = {\n          userId: self.userId,\n          clientAddress: self.connectionHandle.clientAddress,\n          type: \"subscription\",\n          name: msg.name,\n          connectionId: self.id\n        };\n\n        DDPRateLimiter._increment(rateLimiterInput);\n        var rateLimitResult = DDPRateLimiter._check(rateLimiterInput);\n        if (!rateLimitResult.allowed) {\n          self.send({\n            msg: 'nosub', id: msg.id,\n            error: new Meteor.Error(\n              'too-many-requests',\n              DDPRateLimiter.getErrorMessage(rateLimitResult),\n              {timeToReset: rateLimitResult.timeToReset})\n          });\n          return;\n        }\n      }\n\n      var handler = self.server.publish_handlers[msg.name];\n\n      await self._startSubscription(handler, msg.id, msg.params, msg.name);\n\n      // cleaning cached unblock\n      self.cachedUnblock = null;\n    },\n\n    unsub: function (msg) {\n      var self = this;\n\n      self._stopSubscription(msg.id);\n    },\n\n    method: async function (msg, unblock) {\n      var self = this;\n\n      // Reject malformed messages.\n      // For now, we silently ignore unknown attributes,\n      // for forwards compatibility.\n      if (typeof (msg.id) !== \"string\" ||\n          typeof (msg.method) !== \"string\" ||\n          ('params' in msg && !(msg.params instanceof Array)) ||\n          (('randomSeed' in msg) && (typeof msg.randomSeed !== \"string\"))) {\n        self.sendError(\"Malformed method invocation\", msg);\n        return;\n      }\n\n      var randomSeed = msg.randomSeed || null;\n\n      // Set up to mark the method as satisfied once all observers\n      // (and subscriptions) have reacted to any writes that were\n      // done.\n      var fence = new DDPServer._WriteFence;\n      fence.onAllCommitted(function () {\n        // Retire the fence so that future writes are allowed.\n        // This means that callbacks like timers are free to use\n        // the fence, and if they fire before it's armed (for\n        // example, because the method waits for them) their\n        // writes will be included in the fence.\n        fence.retire();\n        self.send({msg: 'updated', methods: [msg.id]});\n      });\n\n      // Find the handler\n      var handler = self.server.method_handlers[msg.method];\n      if (!handler) {\n        self.send({\n          msg: 'result', id: msg.id,\n          error: new Meteor.Error(404, `Method '${msg.method}' not found`)});\n        await fence.arm();\n        return;\n      }\n\n      var invocation = new DDPCommon.MethodInvocation({\n        name: msg.method,\n        isSimulation: false,\n        userId: self.userId,\n        setUserId(userId) {\n          return self._setUserId(userId);\n        },\n        unblock: unblock,\n        connection: self.connectionHandle,\n        randomSeed: randomSeed,\n        fence,\n      });\n\n      const promise = new Promise((resolve, reject) => {\n        // XXX It'd be better if we could hook into method handlers better but\n        // for now, we need to check if the ddp-rate-limiter exists since we\n        // have a weak requirement for the ddp-rate-limiter package to be added\n        // to our application.\n        if (Package['ddp-rate-limiter']) {\n          var DDPRateLimiter = Package['ddp-rate-limiter'].DDPRateLimiter;\n          var rateLimiterInput = {\n            userId: self.userId,\n            clientAddress: self.connectionHandle.clientAddress,\n            type: \"method\",\n            name: msg.method,\n            connectionId: self.id\n          };\n          DDPRateLimiter._increment(rateLimiterInput);\n          var rateLimitResult = DDPRateLimiter._check(rateLimiterInput)\n          if (!rateLimitResult.allowed) {\n            reject(new Meteor.Error(\n              \"too-many-requests\",\n              DDPRateLimiter.getErrorMessage(rateLimitResult),\n              {timeToReset: rateLimitResult.timeToReset}\n            ));\n            return;\n          }\n        }\n\n        resolve(DDPServer._CurrentWriteFence.withValue(\n          fence,\n          () => DDP._CurrentMethodInvocation.withValue(\n            invocation,\n            () => maybeAuditArgumentChecks(\n              handler, invocation, msg.params,\n              \"call to '\" + msg.method + \"'\"\n            )\n          )\n        ));\n      });\n\n      async function finish() {\n        await fence.arm();\n        unblock();\n      }\n\n      const payload = {\n        msg: \"result\",\n        id: msg.id\n      };\n      return promise.then(async result => {\n        await finish();\n        if (result !== undefined) {\n          payload.result = result;\n        }\n        self.send(payload);\n      }, async (exception) => {\n        await finish();\n        payload.error = wrapInternalException(\n          exception,\n          `while invoking method '${msg.method}'`\n        );\n        self.send(payload);\n      });\n    }\n  },\n\n  _eachSub: function (f) {\n    var self = this;\n    self._namedSubs.forEach(f);\n    self._universalSubs.forEach(f);\n  },\n\n  _diffCollectionViews: function (beforeCVs) {\n    var self = this;\n    DiffSequence.diffMaps(beforeCVs, self.collectionViews, {\n      both: function (collectionName, leftValue, rightValue) {\n        rightValue.diff(leftValue);\n      },\n      rightOnly: function (collectionName, rightValue) {\n        rightValue.documents.forEach(function (docView, id) {\n          self.sendAdded(collectionName, id, docView.getFields());\n        });\n      },\n      leftOnly: function (collectionName, leftValue) {\n        leftValue.documents.forEach(function (doc, id) {\n          self.sendRemoved(collectionName, id);\n        });\n      }\n    });\n  },\n\n  // Sets the current user id in all appropriate contexts and reruns\n  // all subscriptions\n  async _setUserId(userId) {\n    var self = this;\n\n    if (userId !== null && typeof userId !== \"string\")\n      throw new Error(\"setUserId must be called on string or null, not \" +\n                      typeof userId);\n\n    // Prevent newly-created universal subscriptions from being added to our\n    // session. They will be found below when we call startUniversalSubs.\n    //\n    // (We don't have to worry about named subscriptions, because we only add\n    // them when we process a 'sub' message. We are currently processing a\n    // 'method' message, and the method did not unblock, because it is illegal\n    // to call setUserId after unblock. Thus we cannot be concurrently adding a\n    // new named subscription).\n    self._dontStartNewUniversalSubs = true;\n\n    // Prevent current subs from updating our collectionViews and call their\n    // stop callbacks. This may yield.\n    self._eachSub(function (sub) {\n      sub._deactivate();\n    });\n\n    // All subs should now be deactivated. Stop sending messages to the client,\n    // save the state of the published collections, reset to an empty view, and\n    // update the userId.\n    self._isSending = false;\n    var beforeCVs = self.collectionViews;\n    self.collectionViews = new Map();\n    self.userId = userId;\n\n    // _setUserId is normally called from a Meteor method with\n    // DDP._CurrentMethodInvocation set. But DDP._CurrentMethodInvocation is not\n    // expected to be set inside a publish function, so we temporary unset it.\n    // Inside a publish function DDP._CurrentPublicationInvocation is set.\n    await DDP._CurrentMethodInvocation.withValue(undefined, async function () {\n      // Save the old named subs, and reset to having no subscriptions.\n      var oldNamedSubs = self._namedSubs;\n      self._namedSubs = new Map();\n      self._universalSubs = [];\n\n\n\n      await Promise.all([...oldNamedSubs].map(async ([subscriptionId, sub]) => {\n        const newSub = sub._recreate();\n        self._namedSubs.set(subscriptionId, newSub);\n        // nb: if the handler throws or calls this.error(), it will in fact\n        // immediately send its 'nosub'. This is OK, though.\n        await newSub._runHandler();\n      }));\n\n      // Allow newly-created universal subs to be started on our connection in\n      // parallel with the ones we're spinning up here, and spin up universal\n      // subs.\n      self._dontStartNewUniversalSubs = false;\n      self.startUniversalSubs();\n    }, { name: '_setUserId' });\n\n    // Start sending messages again, beginning with the diff from the previous\n    // state of the world to the current state. No yields are allowed during\n    // this diff, so that other changes cannot interleave.\n    Meteor._noYieldsAllowed(function () {\n      self._isSending = true;\n      self._diffCollectionViews(beforeCVs);\n      if (!isEmpty(self._pendingReady)) {\n        self.sendReady(self._pendingReady);\n        self._pendingReady = [];\n      }\n    });\n  },\n\n  _startSubscription: function (handler, subId, params, name) {\n    var self = this;\n\n    var sub = new Subscription(\n      self, handler, subId, params, name);\n\n    let unblockHander = self.cachedUnblock;\n    // _startSubscription may call from a lot places\n    // so cachedUnblock might be null in somecases\n    // assign the cachedUnblock\n    sub.unblock = unblockHander || (() => {});\n\n    if (subId)\n      self._namedSubs.set(subId, sub);\n    else\n      self._universalSubs.push(sub);\n\n    return sub._runHandler();\n  },\n\n  // Tear down specified subscription\n  _stopSubscription: function (subId, error) {\n    var self = this;\n\n    var subName = null;\n    if (subId) {\n      var maybeSub = self._namedSubs.get(subId);\n      if (maybeSub) {\n        subName = maybeSub._name;\n        maybeSub._removeAllDocuments();\n        maybeSub._deactivate();\n        self._namedSubs.delete(subId);\n      }\n    }\n\n    var response = {msg: 'nosub', id: subId};\n\n    if (error) {\n      response.error = wrapInternalException(\n        error,\n        subName ? (\"from sub \" + subName + \" id \" + subId)\n          : (\"from sub id \" + subId));\n    }\n\n    self.send(response);\n  },\n\n  // Tear down all subscriptions. Note that this does NOT send removed or nosub\n  // messages, since we assume the client is gone.\n  _deactivateAllSubscriptions: function () {\n    var self = this;\n\n    self._namedSubs.forEach(function (sub, id) {\n      sub._deactivate();\n    });\n    self._namedSubs = new Map();\n\n    self._universalSubs.forEach(function (sub) {\n      sub._deactivate();\n    });\n    self._universalSubs = [];\n  },\n\n  // Determine the remote client's IP address, based on the\n  // HTTP_FORWARDED_COUNT environment variable representing how many\n  // proxies the server is behind.\n  _clientAddress: function () {\n    var self = this;\n\n    // For the reported client address for a connection to be correct,\n    // the developer must set the HTTP_FORWARDED_COUNT environment\n    // variable to an integer representing the number of hops they\n    // expect in the `x-forwarded-for` header. E.g., set to \"1\" if the\n    // server is behind one proxy.\n    //\n    // This could be computed once at startup instead of every time.\n    var httpForwardedCount = parseInt(process.env['HTTP_FORWARDED_COUNT']) || 0;\n\n    if (httpForwardedCount === 0)\n      return self.socket.remoteAddress;\n\n    var forwardedFor = self.socket.headers[\"x-forwarded-for\"];\n    if (!isString(forwardedFor))\n      return null;\n    forwardedFor = forwardedFor.split(',')\n\n    // Typically the first value in the `x-forwarded-for` header is\n    // the original IP address of the client connecting to the first\n    // proxy.  However, the end user can easily spoof the header, in\n    // which case the first value(s) will be the fake IP address from\n    // the user pretending to be a proxy reporting the original IP\n    // address value.  By counting HTTP_FORWARDED_COUNT back from the\n    // end of the list, we ensure that we get the IP address being\n    // reported by *our* first proxy.\n\n    if (httpForwardedCount < 0 || httpForwardedCount !== forwardedFor.length)\n      return null;\n    forwardedFor = forwardedFor.map((ip) => ip.trim());\n    return forwardedFor[forwardedFor.length - httpForwardedCount];\n  }\n});\n\n/******************************************************************************/\n/* Subscription                                                               */\n/******************************************************************************/\n\n// Ctor for a sub handle: the input to each publish function\n\n// Instance name is this because it's usually referred to as this inside a\n// publish\n/**\n * @summary The server's side of a subscription\n * @class Subscription\n * @instanceName this\n * @showInstanceName true\n */\nvar Subscription = function (\n    session, handler, subscriptionId, params, name) {\n  var self = this;\n  self._session = session; // type is Session\n\n  /**\n   * @summary Access inside the publish function. The incoming [connection](#meteor_onconnection) for this subscription.\n   * @locus Server\n   * @name  connection\n   * @memberOf Subscription\n   * @instance\n   */\n  self.connection = session.connectionHandle; // public API object\n\n  self._handler = handler;\n\n  // My subscription ID (generated by client, undefined for universal subs).\n  self._subscriptionId = subscriptionId;\n  // Undefined for universal subs\n  self._name = name;\n\n  self._params = params || [];\n\n  // Only named subscriptions have IDs, but we need some sort of string\n  // internally to keep track of all subscriptions inside\n  // SessionDocumentViews. We use this subscriptionHandle for that.\n  if (self._subscriptionId) {\n    self._subscriptionHandle = 'N' + self._subscriptionId;\n  } else {\n    self._subscriptionHandle = 'U' + Random.id();\n  }\n\n  // Has _deactivate been called?\n  self._deactivated = false;\n\n  // Stop callbacks to g/c this sub.  called w/ zero arguments.\n  self._stopCallbacks = [];\n\n  // The set of (collection, documentid) that this subscription has\n  // an opinion about.\n  self._documents = new Map();\n\n  // Remember if we are ready.\n  self._ready = false;\n\n  // Part of the public API: the user of this sub.\n\n  /**\n   * @summary Access inside the publish function. The id of the logged-in user, or `null` if no user is logged in.\n   * @locus Server\n   * @memberOf Subscription\n   * @name  userId\n   * @instance\n   */\n  self.userId = session.userId;\n\n  // For now, the id filter is going to default to\n  // the to/from DDP methods on MongoID, to\n  // specifically deal with mongo/minimongo ObjectIds.\n\n  // Later, you will be able to make this be \"raw\"\n  // if you want to publish a collection that you know\n  // just has strings for keys and no funny business, to\n  // a DDP consumer that isn't minimongo.\n\n  self._idFilter = {\n    idStringify: MongoID.idStringify,\n    idParse: MongoID.idParse\n  };\n\n  Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n    \"livedata\", \"subscriptions\", 1);\n};\n\nObject.assign(Subscription.prototype, {\n  _runHandler: async function() {\n    // XXX should we unblock() here? Either before running the publish\n    // function, or before running _publishCursor.\n    //\n    // Right now, each publish function blocks all future publishes and\n    // methods waiting on data from Mongo (or whatever else the function\n    // blocks on). This probably slows page load in common cases.\n\n    if (!this.unblock) {\n      this.unblock = () => {};\n    }\n\n    const self = this;\n    let resultOrThenable = null;\n    try {\n      resultOrThenable = DDP._CurrentPublicationInvocation.withValue(\n        self,\n        () =>\n          maybeAuditArgumentChecks(\n            self._handler,\n            self,\n            EJSON.clone(self._params),\n            // It's OK that this would look weird for universal subscriptions,\n            // because they have no arguments so there can never be an\n            // audit-argument-checks failure.\n            \"publisher '\" + self._name + \"'\"\n          ),\n        { name: self._name }\n      );\n    } catch (e) {\n      self.error(e);\n      return;\n    }\n\n    // Did the handler call this.error or this.stop?\n    if (self._isDeactivated()) return;\n\n    // Both conventional and async publish handler functions are supported.\n    // If an object is returned with a then() function, it is either a promise\n    // or thenable and will be resolved asynchronously.\n    const isThenable =\n      resultOrThenable && typeof resultOrThenable.then === 'function';\n    if (isThenable) {\n      try {\n        await self._publishHandlerResult(await resultOrThenable);\n      } catch(e) {\n        self.error(e)\n      }\n    } else {\n      await self._publishHandlerResult(resultOrThenable);\n    }\n  },\n\n  async _publishHandlerResult (res) {\n    // SPECIAL CASE: Instead of writing their own callbacks that invoke\n    // this.added/changed/ready/etc, the user can just return a collection\n    // cursor or array of cursors from the publish function; we call their\n    // _publishCursor method which starts observing the cursor and publishes the\n    // results. Note that _publishCursor does NOT call ready().\n    //\n    // XXX This uses an undocumented interface which only the Mongo cursor\n    // interface publishes. Should we make this interface public and encourage\n    // users to implement it themselves? Arguably, it's unnecessary; users can\n    // already write their own functions like\n    //   var publishMyReactiveThingy = function (name, handler) {\n    //     Meteor.publish(name, function () {\n    //       var reactiveThingy = handler();\n    //       reactiveThingy.publishMe();\n    //     });\n    //   };\n\n    var self = this;\n    var isCursor = function (c) {\n      return c && c._publishCursor;\n    };\n    if (isCursor(res)) {\n      try {\n        await res._publishCursor(self);\n      } catch (e) {\n        self.error(e);\n        return;\n      }\n      // _publishCursor only returns after the initial added callbacks have run.\n      // mark subscription as ready.\n      self.ready();\n    } else if (Array.isArray(res)) {\n      // Check all the elements are cursors\n      if (! res.every(isCursor)) {\n        self.error(new Error(\"Publish function returned an array of non-Cursors\"));\n        return;\n      }\n      // Find duplicate collection names\n      // XXX we should support overlapping cursors, but that would require the\n      // merge box to allow overlap within a subscription\n      var collectionNames = {};\n\n      for (var i = 0; i < res.length; ++i) {\n        var collectionName = res[i]._getCollectionName();\n        if (collectionNames[collectionName]) {\n          self.error(new Error(\n            \"Publish function returned multiple cursors for collection \" +\n              collectionName));\n          return;\n        }\n        collectionNames[collectionName] = true;\n      }\n\n      try {\n        await Promise.all(res.map(cur => cur._publishCursor(self)));\n      } catch (e) {\n        self.error(e);\n        return;\n      }\n      self.ready();\n    } else if (res) {\n      // Truthy values other than cursors or arrays are probably a\n      // user mistake (possible returning a Mongo document via, say,\n      // `coll.findOne()`).\n      self.error(new Error(\"Publish function can only return a Cursor or \"\n                           + \"an array of Cursors\"));\n    }\n  },\n\n  // This calls all stop callbacks and prevents the handler from updating any\n  // SessionCollectionViews further. It's used when the user unsubscribes or\n  // disconnects, as well as during setUserId re-runs. It does *NOT* send\n  // removed messages for the published objects; if that is necessary, call\n  // _removeAllDocuments first.\n  _deactivate: function() {\n    var self = this;\n    if (self._deactivated)\n      return;\n    self._deactivated = true;\n    self._callStopCallbacks();\n    Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n      \"livedata\", \"subscriptions\", -1);\n  },\n\n  _callStopCallbacks: function () {\n    var self = this;\n    // Tell listeners, so they can clean up\n    var callbacks = self._stopCallbacks;\n    self._stopCallbacks = [];\n    callbacks.forEach(function (callback) {\n      callback();\n    });\n  },\n\n  // Send remove messages for every document.\n  _removeAllDocuments: function () {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      self._documents.forEach(function (collectionDocs, collectionName) {\n        collectionDocs.forEach(function (strId) {\n          self.removed(collectionName, self._idFilter.idParse(strId));\n        });\n      });\n    });\n  },\n\n  // Returns a new Subscription for the same session with the same\n  // initial creation parameters. This isn't a clone: it doesn't have\n  // the same _documents cache, stopped state or callbacks; may have a\n  // different _subscriptionHandle, and gets its userId from the\n  // session, not from this object.\n  _recreate: function () {\n    var self = this;\n    return new Subscription(\n      self._session, self._handler, self._subscriptionId, self._params,\n      self._name);\n  },\n\n  /**\n   * @summary Call inside the publish function.  Stops this client's subscription, triggering a call on the client to the `onStop` callback passed to [`Meteor.subscribe`](#meteor_subscribe), if any. If `error` is not a [`Meteor.Error`](#meteor_error), it will be [sanitized](#meteor_error).\n   * @locus Server\n   * @param {Error} error The error to pass to the client.\n   * @instance\n   * @memberOf Subscription\n   */\n  error: function (error) {\n    var self = this;\n    if (self._isDeactivated())\n      return;\n    self._session._stopSubscription(self._subscriptionId, error);\n  },\n\n  // Note that while our DDP client will notice that you've called stop() on the\n  // server (and clean up its _subscriptions table) we don't actually provide a\n  // mechanism for an app to notice this (the subscribe onError callback only\n  // triggers if there is an error).\n\n  /**\n   * @summary Call inside the publish function.  Stops this client's subscription and invokes the client's `onStop` callback with no error.\n   * @locus Server\n   * @instance\n   * @memberOf Subscription\n   */\n  stop: function () {\n    var self = this;\n    if (self._isDeactivated())\n      return;\n    self._session._stopSubscription(self._subscriptionId);\n  },\n\n  /**\n   * @summary Call inside the publish function.  Registers a callback function to run when the subscription is stopped.\n   * @locus Server\n   * @memberOf Subscription\n   * @instance\n   * @param {Function} func The callback function\n   */\n  onStop: function (callback) {\n    var self = this;\n    callback = Meteor.bindEnvironment(callback, 'onStop callback', self);\n    if (self._isDeactivated())\n      callback();\n    else\n      self._stopCallbacks.push(callback);\n  },\n\n  // This returns true if the sub has been deactivated, *OR* if the session was\n  // destroyed but the deferred call to _deactivateAllSubscriptions hasn't\n  // happened yet.\n  _isDeactivated: function () {\n    var self = this;\n    return self._deactivated || self._session.inQueue === null;\n  },\n\n  /**\n   * @summary Call inside the publish function.  Informs the subscriber that a document has been added to the record set.\n   * @locus Server\n   * @memberOf Subscription\n   * @instance\n   * @param {String} collection The name of the collection that contains the new document.\n   * @param {String} id The new document's ID.\n   * @param {Object} fields The fields in the new document.  If `_id` is present it is ignored.\n   */\n  added (collectionName, id, fields) {\n    if (this._isDeactivated())\n      return;\n    id = this._idFilter.idStringify(id);\n\n    if (this._session.server.getPublicationStrategy(collectionName).doAccountingForCollection) {\n      let ids = this._documents.get(collectionName);\n      if (ids == null) {\n        ids = new Set();\n        this._documents.set(collectionName, ids);\n      }\n      ids.add(id);\n    }\n\n    this._session.added(this._subscriptionHandle, collectionName, id, fields);\n  },\n\n  /**\n   * @summary Call inside the publish function.  Informs the subscriber that a document in the record set has been modified.\n   * @locus Server\n   * @memberOf Subscription\n   * @instance\n   * @param {String} collection The name of the collection that contains the changed document.\n   * @param {String} id The changed document's ID.\n   * @param {Object} fields The fields in the document that have changed, together with their new values.  If a field is not present in `fields` it was left unchanged; if it is present in `fields` and has a value of `undefined` it was removed from the document.  If `_id` is present it is ignored.\n   */\n  changed (collectionName, id, fields) {\n    if (this._isDeactivated())\n      return;\n    id = this._idFilter.idStringify(id);\n    this._session.changed(this._subscriptionHandle, collectionName, id, fields);\n  },\n\n  /**\n   * @summary Call inside the publish function.  Informs the subscriber that a document has been removed from the record set.\n   * @locus Server\n   * @memberOf Subscription\n   * @instance\n   * @param {String} collection The name of the collection that the document has been removed from.\n   * @param {String} id The ID of the document that has been removed.\n   */\n  removed (collectionName, id) {\n    if (this._isDeactivated())\n      return;\n    id = this._idFilter.idStringify(id);\n\n    if (this._session.server.getPublicationStrategy(collectionName).doAccountingForCollection) {\n      // We don't bother to delete sets of things in a collection if the\n      // collection is empty.  It could break _removeAllDocuments.\n      this._documents.get(collectionName).delete(id);\n    }\n\n    this._session.removed(this._subscriptionHandle, collectionName, id);\n  },\n\n  /**\n   * @summary Call inside the publish function.  Informs the subscriber that an initial, complete snapshot of the record set has been sent.  This will trigger a call on the client to the `onReady` callback passed to  [`Meteor.subscribe`](#meteor_subscribe), if any.\n   * @locus Server\n   * @memberOf Subscription\n   * @instance\n   */\n  ready: function () {\n    var self = this;\n    if (self._isDeactivated())\n      return;\n    if (!self._subscriptionId)\n      return;  // Unnecessary but ignored for universal sub\n    if (!self._ready) {\n      self._session.sendReady([self._subscriptionId]);\n      self._ready = true;\n    }\n  }\n});\n\n/******************************************************************************/\n/* Server                                                                     */\n/******************************************************************************/\n\nServer = function (options = {}) {\n  var self = this;\n\n  // The default heartbeat interval is 30 seconds on the server and 35\n  // seconds on the client.  Since the client doesn't need to send a\n  // ping as long as it is receiving pings, this means that pings\n  // normally go from the server to the client.\n  //\n  // Note: Troposphere depends on the ability to mutate\n  // Meteor.server.options.heartbeatTimeout! This is a hack, but it's life.\n  self.options = {\n    heartbeatInterval: 15000,\n    heartbeatTimeout: 15000,\n    // For testing, allow responding to pings to be disabled.\n    respondToPings: true,\n    defaultPublicationStrategy: publicationStrategies.SERVER_MERGE,\n    ...options,\n  };\n\n  // Map of callbacks to call when a new connection comes in to the\n  // server and completes DDP version negotiation. Use an object instead\n  // of an array so we can safely remove one from the list while\n  // iterating over it.\n  self.onConnectionHook = new Hook({\n    debugPrintExceptions: \"onConnection callback\"\n  });\n\n  // Map of callbacks to call when a new message comes in.\n  self.onMessageHook = new Hook({\n    debugPrintExceptions: \"onMessage callback\"\n  });\n\n  self.publish_handlers = {};\n  self.universal_publish_handlers = [];\n\n  self.method_handlers = {};\n\n  self._publicationStrategies = {};\n\n  self.sessions = new Map(); // map from id to session\n\n  self.stream_server = new StreamServer();\n\n  self.stream_server.register(function (socket) {\n    // socket implements the SockJSConnection interface\n    socket._meteorSession = null;\n\n    var sendError = function (reason, offendingMessage) {\n      var msg = {msg: 'error', reason: reason};\n      if (offendingMessage)\n        msg.offendingMessage = offendingMessage;\n      socket.send(DDPCommon.stringifyDDP(msg));\n    };\n\n    socket.on('data', function (raw_msg) {\n      if (Meteor._printReceivedDDP) {\n        Meteor._debug(\"Received DDP\", raw_msg);\n      }\n      try {\n        try {\n          var msg = DDPCommon.parseDDP(raw_msg);\n        } catch (err) {\n          sendError('Parse error');\n          return;\n        }\n        if (msg === null || !msg.msg) {\n          sendError('Bad request', msg);\n          return;\n        }\n\n        if (msg.msg === 'connect') {\n          if (socket._meteorSession) {\n            sendError(\"Already connected\", msg);\n            return;\n          }\n\n          self._handleConnect(socket, msg);\n\n          return;\n        }\n\n        if (!socket._meteorSession) {\n          sendError('Must connect first', msg);\n          return;\n        }\n        socket._meteorSession.processMessage(msg);\n      } catch (e) {\n        // XXX print stack nicely\n        Meteor._debug(\"Internal exception while processing message\", msg, e);\n      }\n    });\n\n    socket.on('close', function () {\n      if (socket._meteorSession) {\n        socket._meteorSession.close();\n      }\n    });\n  });\n};\n\nObject.assign(Server.prototype, {\n\n  /**\n   * @summary Register a callback to be called when a new DDP connection is made to the server.\n   * @locus Server\n   * @param {function} callback The function to call when a new DDP connection is established.\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   */\n  onConnection: function (fn) {\n    var self = this;\n    return self.onConnectionHook.register(fn);\n  },\n\n  /**\n   * @summary Set publication strategy for the given collection. Publications strategies are available from `DDPServer.publicationStrategies`. You call this method from `Meteor.server`, like `Meteor.server.setPublicationStrategy()`\n   * @locus Server\n   * @alias setPublicationStrategy\n   * @param collectionName {String}\n   * @param strategy {{useCollectionView: boolean, doAccountingForCollection: boolean}}\n   * @memberOf Meteor.server\n   * @importFromPackage meteor\n   */\n  setPublicationStrategy(collectionName, strategy) {\n    if (!Object.values(publicationStrategies).includes(strategy)) {\n      throw new Error(`Invalid merge strategy: ${strategy} \n        for collection ${collectionName}`);\n    }\n    this._publicationStrategies[collectionName] = strategy;\n  },\n\n  /**\n   * @summary Gets the publication strategy for the requested collection. You call this method from `Meteor.server`, like `Meteor.server.getPublicationStrategy()`\n   * @locus Server\n   * @alias getPublicationStrategy\n   * @param collectionName {String}\n   * @memberOf Meteor.server\n   * @importFromPackage meteor\n   * @return {{useCollectionView: boolean, doAccountingForCollection: boolean}}\n   */\n  getPublicationStrategy(collectionName) {\n    return this._publicationStrategies[collectionName]\n      || this.options.defaultPublicationStrategy;\n  },\n\n  /**\n   * @summary Register a callback to be called when a new DDP message is received.\n   * @locus Server\n   * @param {function} callback The function to call when a new DDP message is received.\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   */\n  onMessage: function (fn) {\n    var self = this;\n    return self.onMessageHook.register(fn);\n  },\n\n  _handleConnect: function (socket, msg) {\n    var self = this;\n\n    // The connect message must specify a version and an array of supported\n    // versions, and it must claim to support what it is proposing.\n    if (!(typeof (msg.version) === 'string' &&\n          Array.isArray(msg.support) &&\n          msg.support.every(isString) &&\n          msg.support.includes(msg.version))) {\n      socket.send(DDPCommon.stringifyDDP({msg: 'failed',\n                                version: DDPCommon.SUPPORTED_DDP_VERSIONS[0]}));\n      socket.close();\n      return;\n    }\n\n    // In the future, handle session resumption: something like:\n    //  socket._meteorSession = self.sessions[msg.session]\n    var version = calculateVersion(msg.support, DDPCommon.SUPPORTED_DDP_VERSIONS);\n\n    if (msg.version !== version) {\n      // The best version to use (according to the client's stated preferences)\n      // is not the one the client is trying to use. Inform them about the best\n      // version to use.\n      socket.send(DDPCommon.stringifyDDP({msg: 'failed', version: version}));\n      socket.close();\n      return;\n    }\n\n    // Yay, version matches! Create a new session.\n    // Note: Troposphere depends on the ability to mutate\n    // Meteor.server.options.heartbeatTimeout! This is a hack, but it's life.\n    socket._meteorSession = new Session(self, version, socket, self.options);\n    self.sessions.set(socket._meteorSession.id, socket._meteorSession);\n    self.onConnectionHook.each(function (callback) {\n      if (socket._meteorSession)\n        callback(socket._meteorSession.connectionHandle);\n      return true;\n    });\n  },\n  /**\n   * Register a publish handler function.\n   *\n   * @param name {String} identifier for query\n   * @param handler {Function} publish handler\n   * @param options {Object}\n   *\n   * Server will call handler function on each new subscription,\n   * either when receiving DDP sub message for a named subscription, or on\n   * DDP connect for a universal subscription.\n   *\n   * If name is null, this will be a subscription that is\n   * automatically established and permanently on for all connected\n   * client, instead of a subscription that can be turned on and off\n   * with subscribe().\n   *\n   * options to contain:\n   *  - (mostly internal) is_auto: true if generated automatically\n   *    from an autopublish hook. this is for cosmetic purposes only\n   *    (it lets us determine whether to print a warning suggesting\n   *    that you turn off autopublish).\n   */\n\n  /**\n   * @summary Publish a record set.\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @locus Server\n   * @param {String|Object} name If String, name of the record set.  If Object, publications Dictionary of publish functions by name.  If `null`, the set has no name, and the record set is automatically sent to all connected clients.\n   * @param {Function} func Function called on the server each time a client subscribes.  Inside the function, `this` is the publish handler object, described below.  If the client passed arguments to `subscribe`, the function is called with the same arguments.\n   */\n  publish: function (name, handler, options) {\n    var self = this;\n\n    if (!isObject(name)) {\n      options = options || {};\n\n      if (name && name in self.publish_handlers) {\n        Meteor._debug(\"Ignoring duplicate publish named '\" + name + \"'\");\n        return;\n      }\n\n      if (Package.autopublish && !options.is_auto) {\n        // They have autopublish on, yet they're trying to manually\n        // pick stuff to publish. They probably should turn off\n        // autopublish. (This check isn't perfect -- if you create a\n        // publish before you turn on autopublish, it won't catch\n        // it, but this will definitely handle the simple case where\n        // you've added the autopublish package to your app, and are\n        // calling publish from your app code).\n        if (!self.warned_about_autopublish) {\n          self.warned_about_autopublish = true;\n          Meteor._debug(\n    \"** You've set up some data subscriptions with Meteor.publish(), but\\n\" +\n    \"** you still have autopublish turned on. Because autopublish is still\\n\" +\n    \"** on, your Meteor.publish() calls won't have much effect. All data\\n\" +\n    \"** will still be sent to all clients.\\n\" +\n    \"**\\n\" +\n    \"** Turn off autopublish by removing the autopublish package:\\n\" +\n    \"**\\n\" +\n    \"**   $ meteor remove autopublish\\n\" +\n    \"**\\n\" +\n    \"** .. and make sure you have Meteor.publish() and Meteor.subscribe() calls\\n\" +\n    \"** for each collection that you want clients to see.\\n\");\n        }\n      }\n\n      if (name)\n        self.publish_handlers[name] = handler;\n      else {\n        self.universal_publish_handlers.push(handler);\n        // Spin up the new publisher on any existing session too. Run each\n        // session's subscription in a new Fiber, so that there's no change for\n        // self.sessions to change while we're running this loop.\n        self.sessions.forEach(function (session) {\n          if (!session._dontStartNewUniversalSubs) {\n            session._startSubscription(handler);\n          }\n        });\n      }\n    }\n    else{\n      Object.entries(name).forEach(function([key, value]) {\n        self.publish(key, value, {});\n      });\n    }\n  },\n\n  _removeSession: function (session) {\n    var self = this;\n    self.sessions.delete(session.id);\n  },\n\n  /**\n   * @summary Tells if the method call came from a call or a callAsync.\n   * @locus Anywhere\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @returns boolean\n   */\n  isAsyncCall: function(){\n    return DDP._CurrentMethodInvocation._isCallAsyncMethodRunning()\n  },\n\n  /**\n   * @summary Defines functions that can be invoked over the network by clients.\n   * @locus Anywhere\n   * @param {Object} methods Dictionary whose keys are method names and values are functions.\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   */\n  methods: function (methods) {\n    var self = this;\n    Object.entries(methods).forEach(function ([name, func]) {\n      if (typeof func !== 'function')\n        throw new Error(\"Method '\" + name + \"' must be a function\");\n      if (self.method_handlers[name])\n        throw new Error(\"A method named '\" + name + \"' is already defined\");\n      self.method_handlers[name] = func;\n    });\n  },\n\n  call: function (name, ...args) {\n    if (args.length && typeof args[args.length - 1] === \"function\") {\n      // If it's a function, the last argument is the result callback, not\n      // a parameter to the remote method.\n      var callback = args.pop();\n    }\n\n    return this.apply(name, args, callback);\n  },\n\n  // A version of the call method that always returns a Promise.\n  callAsync: function (name, ...args) {\n    const options = args[0]?.hasOwnProperty('returnStubValue')\n      ? args.shift()\n      : {};\n    DDP._CurrentMethodInvocation._setCallAsyncMethodRunning(true);\n    const promise = new Promise((resolve, reject) => {\n      DDP._CurrentCallAsyncInvocation._set({ name, hasCallAsyncParent: true });\n      this.applyAsync(name, args, { isFromCallAsync: true, ...options })\n        .then(resolve)\n        .catch(reject)\n        .finally(() => {\n          DDP._CurrentCallAsyncInvocation._set();\n        });\n    });\n    return promise.finally(() =>\n      DDP._CurrentMethodInvocation._setCallAsyncMethodRunning(false)\n    );\n  },\n\n  apply: function (name, args, options, callback) {\n    // We were passed 3 arguments. They may be either (name, args, options)\n    // or (name, args, callback)\n    if (! callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    } else {\n      options = options || {};\n    }\n    const promise = this.applyAsync(name, args, options);\n\n    // Return the result in whichever way the caller asked for it. Note that we\n    // do NOT block on the write fence in an analogous way to how the client\n    // blocks on the relevant data being visible, so you are NOT guaranteed that\n    // cursor observe callbacks have fired when your callback is invoked. (We\n    // can change this if there's a real use case).\n    if (callback) {\n      promise.then(\n        result => callback(undefined, result),\n        exception => callback(exception)\n      );\n    } else {\n      return promise;\n    }\n  },\n\n  // @param options {Optional Object}\n  applyAsync: function (name, args, options) {\n    // Run the handler\n    var handler = this.method_handlers[name];\n\n    if (! handler) {\n      return Promise.reject(\n        new Meteor.Error(404, `Method '${name}' not found`)\n      );\n    }\n    // If this is a method call from within another method or publish function,\n    // get the user state from the outer method or publish function, otherwise\n    // don't allow setUserId to be called\n    var userId = null;\n    let setUserId = () => {\n      throw new Error(\"Can't call setUserId on a server initiated method call\");\n    };\n    var connection = null;\n    var currentMethodInvocation = DDP._CurrentMethodInvocation.get();\n    var currentPublicationInvocation = DDP._CurrentPublicationInvocation.get();\n    var randomSeed = null;\n\n    if (currentMethodInvocation) {\n      userId = currentMethodInvocation.userId;\n      setUserId = (userId) => currentMethodInvocation.setUserId(userId);\n      connection = currentMethodInvocation.connection;\n      randomSeed = DDPCommon.makeRpcSeed(currentMethodInvocation, name);\n    } else if (currentPublicationInvocation) {\n      userId = currentPublicationInvocation.userId;\n      setUserId = (userId) => currentPublicationInvocation._session._setUserId(userId);\n      connection = currentPublicationInvocation.connection;\n    }\n\n    var invocation = new DDPCommon.MethodInvocation({\n      isSimulation: false,\n      userId,\n      setUserId,\n      connection,\n      randomSeed\n    });\n\n    return new Promise((resolve, reject) => {\n      let result;\n      try {\n        result = DDP._CurrentMethodInvocation.withValue(invocation, () =>\n          maybeAuditArgumentChecks(\n            handler,\n            invocation,\n            EJSON.clone(args),\n            \"internal call to '\" + name + \"'\"\n          )\n        );\n      } catch (e) {\n        return reject(e);\n      }\n      if (!Meteor._isPromise(result)) {\n        return resolve(result);\n      }\n      result.then(r => resolve(r)).catch(reject);\n    }).then(EJSON.clone);\n  },\n\n  _urlForSession: function (sessionId) {\n    var self = this;\n    var session = self.sessions.get(sessionId);\n    if (session)\n      return session._socketUrl;\n    else\n      return null;\n  }\n});\n\nvar calculateVersion = function (clientSupportedVersions,\n                                 serverSupportedVersions) {\n  var correctVersion = clientSupportedVersions.find(function (version) {\n    return serverSupportedVersions.includes(version);\n  });\n  if (!correctVersion) {\n    correctVersion = serverSupportedVersions[0];\n  }\n  return correctVersion;\n};\n\nDDPServer._calculateVersion = calculateVersion;\n\n\n// \"blind\" exceptions other than those that were deliberately thrown to signal\n// errors to the client\nvar wrapInternalException = function (exception, context) {\n  if (!exception) return exception;\n\n  // To allow packages to throw errors intended for the client but not have to\n  // depend on the Meteor.Error class, `isClientSafe` can be set to true on any\n  // error before it is thrown.\n  if (exception.isClientSafe) {\n    if (!(exception instanceof Meteor.Error)) {\n      const originalMessage = exception.message;\n      exception = new Meteor.Error(exception.error, exception.reason, exception.details);\n      exception.message = originalMessage;\n    }\n    return exception;\n  }\n\n  // Tests can set the '_expectedByTest' flag on an exception so it won't go to\n  // the server log.\n  if (!exception._expectedByTest) {\n    Meteor._debug(\"Exception \" + context, exception.stack);\n    if (exception.sanitizedError) {\n      Meteor._debug(\"Sanitized and reported to the client as:\", exception.sanitizedError);\n      Meteor._debug();\n    }\n  }\n\n  // Did the error contain more details that could have been useful if caught in\n  // server code (or if thrown from non-client-originated code), but also\n  // provided a \"sanitized\" version with more context than 500 Internal server error? Use that.\n  if (exception.sanitizedError) {\n    if (exception.sanitizedError.isClientSafe)\n      return exception.sanitizedError;\n    Meteor._debug(\"Exception \" + context + \" provides a sanitizedError that \" +\n                  \"does not have isClientSafe property set; ignoring\");\n  }\n\n  return new Meteor.Error(500, \"Internal server error\");\n};\n\n\n// Audit argument checks, if the audit-argument-checks package exists (it is a\n// weak dependency of this package).\nvar maybeAuditArgumentChecks = function (f, context, args, description) {\n  args = args || [];\n  if (Package['audit-argument-checks']) {\n    return Match._failIfArgumentsAreNotAllChecked(\n      f, context, args, description);\n  }\n  return f.apply(context, args);\n};", "DDPServer._WriteFence = class {\n  constructor() {\n    this.armed = false;\n    this.fired = false;\n    this.retired = false;\n    this.outstanding_writes = 0;\n    this.before_fire_callbacks = [];\n    this.completion_callbacks = [];\n  }\n\n  beginWrite() {\n    if (this.retired) {\n      return { committed: () => {} };\n    }\n\n    if (this.fired) {\n      throw new Error(\"fence has already activated -- too late to add writes\");\n    }\n\n    this.outstanding_writes++;\n    let committed = false;\n\n    return {\n      committed: async () => {\n        if (committed) {\n          throw new Error(\"committed called twice on the same write\");\n        }\n        committed = true;\n        this.outstanding_writes--;\n        await this._maybeFire();\n      }\n    };\n  }\n\n  arm() {\n    if (this === DDPServer._getCurrentFence()) {\n      throw Error(\"Can't arm the current fence\");\n    }\n    this.armed = true;\n    return this._maybeFire();\n  }\n\n  onBeforeFire(func) {\n    if (this.fired) {\n      throw new Error(\"fence has already activated -- too late to add a callback\");\n    }\n    this.before_fire_callbacks.push(func);\n  }\n\n  onAllCommitted(func) {\n    if (this.fired) {\n      throw new Error(\"fence has already activated -- too late to add a callback\");\n    }\n    this.completion_callbacks.push(func);\n  }\n\n  async _armAndWait() {\n    let resolver;\n    const returnValue = new Promise(r => resolver = r);\n    this.onAllCommitted(resolver);\n    await this.arm();\n    return returnValue;\n  }\n\n  armAndWait() {\n    return this._armAndWait();\n  }\n\n  async _maybeFire() {\n    if (this.fired) {\n      throw new Error(\"write fence already activated?\");\n    }\n\n    if (!this.armed || this.outstanding_writes > 0) {\n      return;\n    }\n\n    const invokeCallback = async (func) => {\n      try {\n        await func(this);\n      } catch (err) {\n        Meteor._debug(\"exception in write fence callback:\", err);\n      }\n    };\n\n    this.outstanding_writes++;\n\n    // Process all before_fire callbacks in parallel\n    const beforeCallbacks = [...this.before_fire_callbacks];\n    this.before_fire_callbacks = [];\n    await Promise.all(beforeCallbacks.map(cb => invokeCallback(cb)));\n\n    this.outstanding_writes--;\n\n    if (this.outstanding_writes === 0) {\n      this.fired = true;\n      // Process all completion callbacks in parallel\n      const callbacks = [...this.completion_callbacks];\n      this.completion_callbacks = [];\n      await Promise.all(callbacks.map(cb => invokeCallback(cb)));\n    }\n  }\n\n  retire() {\n    if (!this.fired) {\n      throw new Error(\"Can't retire a fence that hasn't fired.\");\n    }\n    this.retired = true;\n  }\n};\n\nDDPServer._CurrentWriteFence = new Meteor.EnvironmentVariable;", "// A \"crossbar\" is a class that provides structured notification registration.\n// See _match for the definition of how a notification matches a trigger.\n// All notifications and triggers must have a string key named 'collection'.\n\nDDPServer._Crossbar = function (options) {\n  var self = this;\n  options = options || {};\n\n  self.nextId = 1;\n  // map from collection name (string) -> listener id -> object. each object has\n  // keys 'trigger', 'callback'.  As a hack, the empty string means \"no\n  // collection\".\n  self.listenersByCollection = {};\n  self.listenersByCollectionCount = {};\n  self.factPackage = options.factPackage || \"livedata\";\n  self.factName = options.factName || null;\n};\n\nObject.assign(DDPServer._Crossbar.prototype, {\n  // msg is a trigger or a notification\n  _collectionForMessage: function (msg) {\n    var self = this;\n    if (!('collection' in msg)) {\n      return '';\n    } else if (typeof(msg.collection) === 'string') {\n      if (msg.collection === '')\n        throw Error(\"Message has empty collection!\");\n      return msg.collection;\n    } else {\n      throw Error(\"Message has non-string collection!\");\n    }\n  },\n\n  // Listen for notification that match 'trigger'. A notification\n  // matches if it has the key-value pairs in trigger as a\n  // subset. When a notification matches, call 'callback', passing\n  // the actual notification.\n  //\n  // Returns a listen handle, which is an object with a method\n  // stop(). Call stop() to stop listening.\n  //\n  // XXX It should be legal to call fire() from inside a listen()\n  // callback?\n  listen: function (trigger, callback) {\n    var self = this;\n    var id = self.nextId++;\n\n    var collection = self._collectionForMessage(trigger);\n    var record = {trigger: EJSON.clone(trigger), callback: callback};\n    if (! (collection in self.listenersByCollection)) {\n      self.listenersByCollection[collection] = {};\n      self.listenersByCollectionCount[collection] = 0;\n    }\n    self.listenersByCollection[collection][id] = record;\n    self.listenersByCollectionCount[collection]++;\n\n    if (self.factName && Package['facts-base']) {\n      Package['facts-base'].Facts.incrementServerFact(\n        self.factPackage, self.factName, 1);\n    }\n\n    return {\n      stop: function () {\n        if (self.factName && Package['facts-base']) {\n          Package['facts-base'].Facts.incrementServerFact(\n            self.factPackage, self.factName, -1);\n        }\n        delete self.listenersByCollection[collection][id];\n        self.listenersByCollectionCount[collection]--;\n        if (self.listenersByCollectionCount[collection] === 0) {\n          delete self.listenersByCollection[collection];\n          delete self.listenersByCollectionCount[collection];\n        }\n      }\n    };\n  },\n\n  // Fire the provided 'notification' (an object whose attribute\n  // values are all JSON-compatibile) -- inform all matching listeners\n  // (registered with listen()).\n  //\n  // If fire() is called inside a write fence, then each of the\n  // listener callbacks will be called inside the write fence as well.\n  //\n  // The listeners may be invoked in parallel, rather than serially.\n  fire: async function (notification) {\n    var self = this;\n\n    var collection = self._collectionForMessage(notification);\n\n    if (!(collection in self.listenersByCollection)) {\n      return;\n    }\n\n    var listenersForCollection = self.listenersByCollection[collection];\n    var callbackIds = [];\n    Object.entries(listenersForCollection).forEach(function ([id, l]) {\n      if (self._matches(notification, l.trigger)) {\n        callbackIds.push(id);\n      }\n    });\n\n    // Listener callbacks can yield, so we need to first find all the ones that\n    // match in a single iteration over self.listenersByCollection (which can't\n    // be mutated during this iteration), and then invoke the matching\n    // callbacks, checking before each call to ensure they haven't stopped.\n    // Note that we don't have to check that\n    // self.listenersByCollection[collection] still === listenersForCollection,\n    // because the only way that stops being true is if listenersForCollection\n    // first gets reduced down to the empty object (and then never gets\n    // increased again).\n    for (const id of callbackIds) {\n      if (id in listenersForCollection) {\n        await listenersForCollection[id].callback(notification);\n      }\n    }\n  },\n\n  // A notification matches a trigger if all keys that exist in both are equal.\n  //\n  // Examples:\n  //  N:{collection: \"C\"} matches T:{collection: \"C\"}\n  //    (a non-targeted write to a collection matches a\n  //     non-targeted query)\n  //  N:{collection: \"C\", id: \"X\"} matches T:{collection: \"C\"}\n  //    (a targeted write to a collection matches a non-targeted query)\n  //  N:{collection: \"C\"} matches T:{collection: \"C\", id: \"X\"}\n  //    (a non-targeted write to a collection matches a\n  //     targeted query)\n  //  N:{collection: \"C\", id: \"X\"} matches T:{collection: \"C\", id: \"X\"}\n  //    (a targeted write to a collection matches a targeted query targeted\n  //     at the same document)\n  //  N:{collection: \"C\", id: \"X\"} does not match T:{collection: \"C\", id: \"Y\"}\n  //    (a targeted write to a collection does not match a targeted query\n  //     targeted at a different document)\n  _matches: function (notification, trigger) {\n    // Most notifications that use the crossbar have a string `collection` and\n    // maybe an `id` that is a string or ObjectID. We're already dividing up\n    // triggers by collection, but let's fast-track \"nope, different ID\" (and\n    // avoid the overly generic EJSON.equals). This makes a noticeable\n    // performance difference; see https://github.com/meteor/meteor/pull/3697\n    if (typeof(notification.id) === 'string' &&\n        typeof(trigger.id) === 'string' &&\n        notification.id !== trigger.id) {\n      return false;\n    }\n    if (notification.id instanceof MongoID.ObjectID &&\n        trigger.id instanceof MongoID.ObjectID &&\n        ! notification.id.equals(trigger.id)) {\n      return false;\n    }\n\n    return Object.keys(trigger).every(function (key) {\n      return !(key in notification) || EJSON.equals(trigger[key], notification[key]);\n     });\n  }\n});\n\n// The \"invalidation crossbar\" is a specific instance used by the DDP server to\n// implement write fence notifications. Listener callbacks on this crossbar\n// should call beginWrite on the current write fence before they return, if they\n// want to delay the write fence from firing (ie, the DDP method-data-updated\n// message from being sent).\nDDPServer._InvalidationCrossbar = new DDPServer._Crossbar({\n  factName: \"invalidation-crossbar-listeners\"\n});", "if (process.env.DDP_DEFAULT_CONNECTION_URL) {\n  __meteor_runtime_config__.DDP_DEFAULT_CONNECTION_URL =\n    process.env.DDP_DEFAULT_CONNECTION_URL;\n}\n\nMeteor.server = new Server();\n\nMeteor.refresh = async function (notification) {\n  await DDPServer._InvalidationCrossbar.fire(notification);\n};\n\n// Proxy the public methods of Meteor.server so they can\n// be called directly on Meteor.\n\n  [\n    'publish',\n    'isAsyncCall',\n    'methods',\n    'call',\n    'callAsync',\n    'apply',\n    'applyAsync',\n    'onConnection',\n    'onMessage',\n  ].forEach(\n  function(name) {\n    Meteor[name] = Meteor.server[name].bind(Meteor.server);\n  }\n);\n", "interface ChangeCollector {\n  [key: string]: any;\n}\n\ninterface DataEntry {\n  subscriptionHandle: string;\n  value: any;\n}\n\nexport class DummyDocumentView {\n  private existsIn: Set<string>;\n  private dataByKey: Map<string, DataEntry[]>;\n\n  constructor() {\n    this.existsIn = new Set<string>(); // set of subscriptionHandle\n    this.dataByKey = new Map<string, DataEntry[]>(); // key-> [ {subscriptionHandle, value} by precedence]\n  }\n\n  getFields(): Record<string, never> {\n    return {};\n  }\n\n  clearField(\n    subscriptionHandle: string, \n    key: string, \n    changeCollector: ChangeCollector\n  ): void {\n    changeCollector[key] = undefined;\n  }\n\n  changeField(\n    subscriptionHandle: string,\n    key: string,\n    value: any,\n    changeCollector: ChangeCollector,\n    isAdd?: boolean\n  ): void {\n    changeCollector[key] = value;\n  }\n}", "import { DummyDocumentView } from \"./dummy_document_view\";\nimport { SessionDocumentView } from \"./session_document_view\";\n\ninterface SessionCallbacks {\n  added: (collectionName: string, id: string, fields: Record<string, any>) => void;\n  changed: (collectionName: string, id: string, fields: Record<string, any>) => void;\n  removed: (collectionName: string, id: string) => void;\n}\n\ntype DocumentView = SessionDocumentView | DummyDocumentView;\n\nexport class SessionCollectionView {\n  private readonly collectionName: string;\n  private readonly documents: Map<string, DocumentView>;\n  private readonly callbacks: SessionCallbacks;\n\n  /**\n   * Represents a client's view of a single collection\n   * @param collectionName - Name of the collection it represents\n   * @param sessionCallbacks - The callbacks for added, changed, removed\n   */\n  constructor(collectionName: string, sessionCallbacks: SessionCallbacks) {\n    this.collectionName = collectionName;\n    this.documents = new Map();\n    this.callbacks = sessionCallbacks;\n  }\n\n  public isEmpty(): boolean {\n    return this.documents.size === 0;\n  }\n\n  public diff(previous: SessionCollectionView): void {\n    DiffSequence.diffMaps(previous.documents, this.documents, {\n      both: this.diffDocument.bind(this),\n      rightOnly: (id: string, nowDV: DocumentView) => {\n        this.callbacks.added(this.collectionName, id, nowDV.getFields());\n      },\n      leftOnly: (id: string, prevDV: DocumentView) => {\n        this.callbacks.removed(this.collectionName, id);\n      }\n    });\n  }\n\n  private diffDocument(id: string, prevDV: DocumentView, nowDV: DocumentView): void {\n    const fields: Record<string, any> = {};\n    \n    DiffSequence.diffObjects(prevDV.getFields(), nowDV.getFields(), {\n      both: (key: string, prev: any, now: any) => {\n        if (!EJSON.equals(prev, now)) {\n          fields[key] = now;\n        }\n      },\n      rightOnly: (key: string, now: any) => {\n        fields[key] = now;\n      },\n      leftOnly: (key: string, prev: any) => {\n        fields[key] = undefined;\n      }\n    });\n    \n    this.callbacks.changed(this.collectionName, id, fields);\n  }\n\n  public added(subscriptionHandle: string, id: string, fields: Record<string, any>): void {\n    let docView: DocumentView | undefined = this.documents.get(id);\n    let added = false;\n\n    if (!docView) {\n      added = true;\n      if (Meteor.server.getPublicationStrategy(this.collectionName).useDummyDocumentView) {\n        docView = new DummyDocumentView();\n      } else {\n        docView = new SessionDocumentView();\n      }\n      this.documents.set(id, docView);\n    }\n\n    docView.existsIn.add(subscriptionHandle);\n    const changeCollector: Record<string, any> = {};\n\n    Object.entries(fields).forEach(([key, value]) => {\n      docView!.changeField(\n        subscriptionHandle,\n        key,\n        value,\n        changeCollector,\n        true\n      );\n    });\n\n    if (added) {\n      this.callbacks.added(this.collectionName, id, changeCollector);\n    } else {\n      this.callbacks.changed(this.collectionName, id, changeCollector);\n    }\n  }\n\n  public changed(subscriptionHandle: string, id: string, changed: Record<string, any>): void {\n    const changedResult: Record<string, any> = {};\n    const docView = this.documents.get(id);\n\n    if (!docView) {\n      throw new Error(`Could not find element with id ${id} to change`);\n    }\n\n    Object.entries(changed).forEach(([key, value]) => {\n      if (value === undefined) {\n        docView.clearField(subscriptionHandle, key, changedResult);\n      } else {\n        docView.changeField(subscriptionHandle, key, value, changedResult);\n      }\n    });\n\n    this.callbacks.changed(this.collectionName, id, changedResult);\n  }\n\n  public removed(subscriptionHandle: string, id: string): void {\n    const docView = this.documents.get(id);\n\n    if (!docView) {\n      throw new Error(`Removed nonexistent document ${id}`);\n    }\n\n    docView.existsIn.delete(subscriptionHandle);\n\n    if (docView.existsIn.size === 0) {\n      // it is gone from everyone\n      this.callbacks.removed(this.collectionName, id);\n      this.documents.delete(id);\n    } else {\n      const changed: Record<string, any> = {};\n      // remove this subscription from every precedence list\n      // and record the changes\n      docView.dataByKey.forEach((precedenceList, key) => {\n        docView.clearField(subscriptionHandle, key, changed);\n      });\n      this.callbacks.changed(this.collectionName, id, changed);\n    }\n  }\n}", "interface PrecedenceItem {\n  subscriptionHandle: string;\n  value: any;\n}\n\ninterface ChangeCollector {\n  [key: string]: any;\n}\n\nexport class SessionDocumentView {\n  private existsIn: Set<string>;\n  private dataByKey: Map<string, PrecedenceItem[]>;\n\n  constructor() {\n    this.existsIn = new Set(); // set of subscriptionHandle\n    // Memory Growth\n    this.dataByKey = new Map(); // key-> [ {subscriptionHandle, value} by precedence]\n  }\n\n  getFields(): Record<string, any> {\n    const ret: Record<string, any> = {};\n    this.dataByKey.forEach((precedenceList, key) => {\n      ret[key] = precedenceList[0].value;\n    });\n    return ret;\n  }\n\n  clearField(\n    subscriptionHandle: string,\n    key: string,\n    changeCollector: ChangeCollector\n  ): void {\n    // Publish API ignores _id if present in fields\n    if (key === \"_id\") return;\n\n    const precedenceList = this.dataByKey.get(key);\n    // It's okay to clear fields that didn't exist. No need to throw\n    // an error.\n    if (!precedenceList) return;\n\n    let removedValue: any = undefined;\n\n    for (let i = 0; i < precedenceList.length; i++) {\n      const precedence = precedenceList[i];\n      if (precedence.subscriptionHandle === subscriptionHandle) {\n        // The view's value can only change if this subscription is the one that\n        // used to have precedence.\n        if (i === 0) removedValue = precedence.value;\n        precedenceList.splice(i, 1);\n        break;\n      }\n    }\n\n    if (precedenceList.length === 0) {\n      this.dataByKey.delete(key);\n      changeCollector[key] = undefined;\n    } else if (\n      removedValue !== undefined &&\n      !EJSON.equals(removedValue, precedenceList[0].value)\n    ) {\n      changeCollector[key] = precedenceList[0].value;\n    }\n  }\n\n  changeField(\n    subscriptionHandle: string,\n    key: string,\n    value: any,\n    changeCollector: ChangeCollector,\n    isAdd: boolean = false\n  ): void {\n    // Publish API ignores _id if present in fields\n    if (key === \"_id\") return;\n\n    // Don't share state with the data passed in by the user.\n    value = EJSON.clone(value);\n\n    if (!this.dataByKey.has(key)) {\n      this.dataByKey.set(key, [\n        { subscriptionHandle: subscriptionHandle, value: value },\n      ]);\n      changeCollector[key] = value;\n      return;\n    }\n\n    const precedenceList = this.dataByKey.get(key)!;\n    let elt: PrecedenceItem | undefined;\n\n    if (!isAdd) {\n      elt = precedenceList.find(\n        (precedence) => precedence.subscriptionHandle === subscriptionHandle\n      );\n    }\n\n    if (elt) {\n      if (elt === precedenceList[0] && !EJSON.equals(value, elt.value)) {\n        // this subscription is changing the value of this field.\n        changeCollector[key] = value;\n      }\n      elt.value = value;\n    } else {\n      // this subscription is newly caring about this field\n      precedenceList.push({ subscriptionHandle: subscriptionHandle, value: value });\n    }\n  }\n}"]}