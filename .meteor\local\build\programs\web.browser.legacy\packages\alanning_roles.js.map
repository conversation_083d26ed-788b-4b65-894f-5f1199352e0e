{"version": 3, "sources": ["meteor://💻app/packages/alanning:roles/roles/roles_client.js", "meteor://💻app/packages/alanning:roles/roles/roles_common_async.js", "meteor://💻app/packages/alanning:roles/roles/client/debug.js", "meteor://💻app/packages/alanning:roles/roles/client/uiHelpers.js"], "names": ["_typeof", "module1", "link", "default", "v", "_toConsumableArray", "_createForOfIteratorHelperLoose", "Meteor", "Roles", "getGroupsForUserDeprecationWarning", "Object", "assign", "GLOBAL_GROUP", "createRole", "<PERSON><PERSON><PERSON>", "options", "_checkRoleName", "unlessExists", "result", "roles", "upsert", "_id", "$setOnInsert", "children", "insertedId", "Error", "deleteRole", "inheritedRoles", "roleAssignment", "remove", "_getParentRoleNames", "findOne", "_iterator", "find", "$in", "fetch", "_step", "done", "r", "value", "update", "$pull", "_getInheritedRoleNames", "$set", "concat", "map", "r2", "multi", "length", "renameRole", "old<PERSON>ame", "newName", "count", "role", "insert", "addRolesToParent", "rolesNames", "parentName", "Array", "isArray", "_iterator2", "_step2", "_addRoleToParent", "includes", "$ne", "$push", "$each", "removeRolesFromParent", "_iterator3", "_step3", "_removeRoleFromParent", "fields", "_iterator4", "_step4", "addUsersToRoles", "users", "id", "_normalizeOptions", "_checkScopeName", "scope", "ifExists", "_iterator5", "_step5", "user", "_iterator6", "_step6", "_addUserToRole", "setUserRoles", "anyScope", "_iterator7", "_step7", "selector", "_iterator8", "_step8", "userId", "res", "parentRoles", "Set", "_iterator9", "_step9", "for<PERSON>ach", "parentRole", "add", "delete", "nestedRoles", "_iterator10", "_step10", "_iterator11", "_step11", "removeUsersFromRoles", "_iterator12", "_step12", "_iterator13", "_step13", "_removeUserFromRole", "userIsInRole", "filter", "some", "limit", "getRolesForUser", "fullObjects", "onlyAssigned", "onlyScoped", "push", "reduce", "rev", "current", "getAllRoles", "queryOptions", "sort", "getUsersInRole", "ids", "getUserAssignmentsForRole", "a", "_getUsersInRoleCursor", "getGroupsForUser", "_Roles", "console", "warn", "getScopesForUser", "apply", "arguments", "scopes", "obi", "renameScope", "removeScope", "name", "trim", "isParentOf", "parentRoleName", "child<PERSON>ole<PERSON>ame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "undefined", "_normalizeScopeName", "scopeName", "call", "module", "_regeneratorRuntime", "export", "RolesCollection", "RoleAssignmentCollection", "Mongo", "Collection", "asyncSome", "_callee", "arr", "predicate", "e", "async", "_callee$", "_context", "prev", "next", "awrap", "sent", "abrupt", "stop", "Promise", "createRoleAsync", "_callee2", "existingRole", "_callee2$", "_context2", "findOneAsync", "updateAsync", "insertAsync", "deleteRoleAsync", "_callee3", "_callee3$", "_context3", "removeAsync", "t0", "t1", "t2", "t3", "_getParentRoleNamesAsync", "t4", "fetchAsync", "t5", "t6", "t7", "t8", "t9", "_getInheritedRoleNamesAsync", "renameRoleAsync", "_callee4", "_callee4$", "_context4", "addRolesToParentAsync", "_callee5", "_callee5$", "_context5", "_addRoleToParentAsync", "_callee6", "_callee6$", "_context6", "t10", "t11", "t12", "removeRolesFromParentAsync", "_callee7", "_callee7$", "_context7", "_removeRoleFromParentAsync", "_callee8", "_callee8$", "_context8", "t13", "t14", "addUsersToRolesAsync", "_callee9", "_callee9$", "_context9", "_addUserToRoleAsync", "setUserRolesAsync", "_callee10", "_callee10$", "_context10", "_callee11", "existingAssignment", "_callee11$", "_context11", "_callee12", "_callee12$", "_context12", "_callee13", "_callee13$", "_context13", "removeUsersFromRolesAsync", "_callee14", "_iterator14", "_step14", "_iterator15", "_step15", "_callee14$", "_context14", "_removeUserFromRoleAsync", "_callee15", "_callee15$", "_context15", "userIsInRoleAsync", "_callee17", "_callee17$", "_context17", "_callee16", "out", "_callee16$", "_context16", "countDocuments", "getRolesForUserAsync", "_callee18", "_callee18$", "_context18", "getUsersInRoleAsync", "_callee19", "_callee19$", "_context19", "getGroupsForUserAsync", "_callee20", "_args20", "_callee20$", "_context20", "getScopesForUserAsync", "_callee21", "_callee21$", "_context21", "renameScopeAsync", "_callee22", "_callee22$", "_context22", "removeScopeAsync", "_callee23", "_callee23$", "_context23", "isParentOfAsync", "_callee24", "_callee24$", "_context24", "debug", "localStorage", "temp", "getItem", "ex", "_slicedToArray", "_uiHelpers", "isInRole", "comma", "indexOf", "Match", "test", "String", "split", "memo", "Package", "blaze", "Blaze", "registerHelper", "entries", "_ref", "_ref2", "func"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAIA,OAAO;EAACC,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACJ,OAAO,GAACI,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIC,kBAAkB;EAACJ,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACC,kBAAkB,GAACD,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIE,+BAA+B;EAACL,OAAO,CAACC,IAAI,CAAC,uDAAuD,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACE,+BAA+B,GAACF,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAA/X,IAAIG,MAAM;EAACN,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;IAACK,MAAM,EAAC,SAAAA,CAASH,CAAC,EAAC;MAACG,MAAM,GAACH,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAGzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEA;AACA;AACA;EACA,IAAI,OAAOI,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAG,CAAC,CAAC,EAAC;EACb;EAEA,IAAIC,kCAAkC,GAAG,KAAK;EAE9CC,MAAM,CAACC,MAAM,CAACH,KAAK,EAAE;IAEnB;AACF;AACA;AACA;AACA;AACA;AACA;IACEI,YAAY,EAAE,IAAI;IAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,UAAU,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,OAAO,EAAE;MACvCP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAE9BC,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtBM,YAAY,EAAE;MAChB,CAAC,EAAEF,OAAO,CAAC;MAEX,IAAMG,MAAM,GAAGX,MAAM,CAACY,KAAK,CAACC,MAAM,CAAC;QAAEC,GAAG,EAAEP;MAAS,CAAC,EAAE;QAAEQ,YAAY,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE,CAAC,CAAC;MAEzF,IAAI,CAACL,MAAM,CAACM,UAAU,EAAE;QACtB,IAAIT,OAAO,CAACE,YAAY,EAAE,OAAO,IAAI;QACrC,MAAM,IAAIQ,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;MAC9D;MAEA,OAAOI,MAAM,CAACM,UAAU;IAC1B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEE,UAAU,EAAE,SAAAA,CAAUZ,QAAQ,EAAE;MAC9B,IAAIK,KAAK;MACT,IAAIQ,cAAc;MAElBnB,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;;MAE9B;MACAP,MAAM,CAACqB,cAAc,CAACC,MAAM,CAAC;QAC3B,UAAU,EAAEf;MACd,CAAC,CAAC;MAEF,GAAG;QACD;QACAK,KAAK,GAAGX,KAAK,CAACsB,mBAAmB,CAACvB,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;UAAEV,GAAG,EAAEP;QAAS,CAAC,CAAC,CAAC;QAE1E,SAAAkB,SAAA,GAAA1B,+BAAA,CAAgBC,MAAM,CAACY,KAAK,CAACc,IAAI,CAAC;YAAEZ,GAAG,EAAE;cAAEa,GAAG,EAAEf;YAAM;UAAE,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC,GAAAC,KAAA,IAAAA,KAAA,GAAAJ,SAAA,IAAAK,IAAA,GAAE;UAAA,IAAzDC,CAAC,GAAAF,KAAA,CAAAG,KAAA;UACVhC,MAAM,CAACY,KAAK,CAACqB,MAAM,CAAC;YAClBnB,GAAG,EAAEiB,CAAC,CAACjB;UACT,CAAC,EAAE;YACDoB,KAAK,EAAE;cACLlB,QAAQ,EAAE;gBACRF,GAAG,EAAEP;cACP;YACF;UACF,CAAC,CAAC;UAEFa,cAAc,GAAGnB,KAAK,CAACkC,sBAAsB,CAACnC,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;YAAEV,GAAG,EAAEiB,CAAC,CAACjB;UAAI,CAAC,CAAC,CAAC;UACnFd,MAAM,CAACqB,cAAc,CAACY,MAAM,CAAC;YAC3B,UAAU,EAAEF,CAAC,CAACjB;UAChB,CAAC,EAAE;YACDsB,IAAI,EAAE;cACJhB,cAAc,EAAE,CAACW,CAAC,CAACjB,GAAG,EAAAuB,MAAA,CAAAvC,kBAAA,CAAKsB,cAAc,GAAEkB,GAAG,CAAC,UAAAC,EAAE;gBAAA,OAAK;kBAAEzB,GAAG,EAAEyB;gBAAG,CAAC;cAAA,CAAC;YACpE;UACF,CAAC,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAC,CAAC;QACrB;MACF,CAAC,QAAQ5B,KAAK,CAAC6B,MAAM,GAAG,CAAC;;MAEzB;MACAzC,MAAM,CAACY,KAAK,CAACU,MAAM,CAAC;QAAER,GAAG,EAAEP;MAAS,CAAC,CAAC;IACxC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEmC,UAAU,EAAE,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;MACtC,IAAIC,KAAK;MAET5C,KAAK,CAACQ,cAAc,CAACkC,OAAO,CAAC;MAC7B1C,KAAK,CAACQ,cAAc,CAACmC,OAAO,CAAC;MAE7B,IAAID,OAAO,KAAKC,OAAO,EAAE;MAEzB,IAAME,IAAI,GAAG9C,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAE6B;MAAQ,CAAC,CAAC;MAEnD,IAAI,CAACG,IAAI,EAAE;QACT,MAAM,IAAI5B,KAAK,CAAC,SAAS,GAAGyB,OAAO,GAAG,oBAAoB,CAAC;MAC7D;MAEAG,IAAI,CAAChC,GAAG,GAAG8B,OAAO;MAElB5C,MAAM,CAACY,KAAK,CAACmC,MAAM,CAACD,IAAI,CAAC;MAEzB,GAAG;QACDD,KAAK,GAAG7C,MAAM,CAACqB,cAAc,CAACY,MAAM,CAAC;UACnC,UAAU,EAAEU;QACd,CAAC,EAAE;UACDP,IAAI,EAAE;YACJ,UAAU,EAAEQ;UACd;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB,GAAG;QACDA,KAAK,GAAG7C,MAAM,CAACqB,cAAc,CAACY,MAAM,CAAC;UACnC,oBAAoB,EAAEU;QACxB,CAAC,EAAE;UACDP,IAAI,EAAE;YACJ,sBAAsB,EAAEQ;UAC1B;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB,GAAG;QACDA,KAAK,GAAG7C,MAAM,CAACY,KAAK,CAACqB,MAAM,CAAC;UAC1B,cAAc,EAAEU;QAClB,CAAC,EAAE;UACDP,IAAI,EAAE;YACJ,gBAAgB,EAAEQ;UACpB;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB7C,MAAM,CAACY,KAAK,CAACU,MAAM,CAAC;QAAER,GAAG,EAAE6B;MAAQ,CAAC,CAAC;IACvC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEK,gBAAgB,EAAE,SAAAA,CAAUC,UAAU,EAAEC,UAAU,EAAE;MAClD;MACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAEzD,SAAAI,UAAA,GAAAtD,+BAAA,CAAuBkD,UAAU,GAAAK,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAvB,IAAA,GAAE;QAAA,IAAxBvB,QAAQ,GAAA+C,MAAA,CAAAtB,KAAA;QACjB/B,KAAK,CAACsD,gBAAgB,CAAChD,QAAQ,EAAE2C,UAAU,CAAC;MAC9C;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACEK,gBAAgB,EAAE,SAAAA,CAAUhD,QAAQ,EAAE2C,UAAU,EAAE;MAChDjD,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACQ,cAAc,CAACyC,UAAU,CAAC;;MAEhC;MACA,IAAMJ,IAAI,GAAG9C,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEP;MAAS,CAAC,CAAC;MAEpD,IAAI,CAACuC,IAAI,EAAE;QACT,MAAM,IAAI5B,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;MAC9D;;MAEA;MACA,IAAIN,KAAK,CAACkC,sBAAsB,CAACW,IAAI,CAAC,CAACU,QAAQ,CAACN,UAAU,CAAC,EAAE;QAC3D,MAAM,IAAIhC,KAAK,CAAC,UAAU,GAAGX,QAAQ,GAAG,WAAW,GAAG2C,UAAU,GAAG,wBAAwB,CAAC;MAC9F;MAEA,IAAML,KAAK,GAAG7C,MAAM,CAACY,KAAK,CAACqB,MAAM,CAAC;QAChCnB,GAAG,EAAEoC,UAAU;QACf,cAAc,EAAE;UACdO,GAAG,EAAEX,IAAI,CAAChC;QACZ;MACF,CAAC,EAAE;QACD4C,KAAK,EAAE;UACL1C,QAAQ,EAAE;YACRF,GAAG,EAAEgC,IAAI,CAAChC;UACZ;QACF;MACF,CAAC,CAAC;;MAEF;MACA;MACA,IAAI,CAAC+B,KAAK,EAAE;MAEZ7C,MAAM,CAACqB,cAAc,CAACY,MAAM,CAAC;QAC3B,oBAAoB,EAAEiB;MACxB,CAAC,EAAE;QACDQ,KAAK,EAAE;UACLtC,cAAc,EAAE;YAAEuC,KAAK,EAAE,CAACb,IAAI,CAAChC,GAAG,EAAAuB,MAAA,CAAAvC,kBAAA,CAAKG,KAAK,CAACkC,sBAAsB,CAACW,IAAI,CAAC,GAAER,GAAG,CAAC,UAAAP,CAAC;cAAA,OAAK;gBAAEjB,GAAG,EAAEiB;cAAE,CAAC;YAAA,CAAC;UAAE;QACpG;MACF,CAAC,EAAE;QAAES,KAAK,EAAE;MAAK,CAAC,CAAC;IACrB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEoB,qBAAqB,EAAE,SAAAA,CAAUX,UAAU,EAAEC,UAAU,EAAE;MACvD;MACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAEzD,SAAAY,UAAA,GAAA9D,+BAAA,CAAuBkD,UAAU,GAAAa,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA/B,IAAA,GAAE;QAAA,IAAxBvB,QAAQ,GAAAuD,MAAA,CAAA9B,KAAA;QACjB/B,KAAK,CAAC8D,qBAAqB,CAACxD,QAAQ,EAAE2C,UAAU,CAAC;MACnD;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACEa,qBAAqB,EAAE,SAAAA,CAAUxD,QAAQ,EAAE2C,UAAU,EAAE;MACrDjD,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACQ,cAAc,CAACyC,UAAU,CAAC;;MAEhC;MACA;MACA,IAAMJ,IAAI,GAAG9C,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEP;MAAS,CAAC,EAAE;QAAEyD,MAAM,EAAE;UAAElD,GAAG,EAAE;QAAE;MAAE,CAAC,CAAC;MAE5E,IAAI,CAACgC,IAAI,EAAE;QACT,MAAM,IAAI5B,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;MAC9D;MAEA,IAAMsC,KAAK,GAAG7C,MAAM,CAACY,KAAK,CAACqB,MAAM,CAAC;QAChCnB,GAAG,EAAEoC;MACP,CAAC,EAAE;QACDhB,KAAK,EAAE;UACLlB,QAAQ,EAAE;YACRF,GAAG,EAAEgC,IAAI,CAAChC;UACZ;QACF;MACF,CAAC,CAAC;;MAEF;MACA;MACA,IAAI,CAAC+B,KAAK,EAAE;;MAEZ;MACA,IAAMjC,KAAK,MAAAyB,MAAA,CAAAvC,kBAAA,CAAOG,KAAK,CAACsB,mBAAmB,CAACvB,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEoC;MAAW,CAAC,CAAC,CAAC,IAAEA,UAAU,EAAC;MAEnG,SAAAe,UAAA,GAAAlE,+BAAA,CAAgBC,MAAM,CAACY,KAAK,CAACc,IAAI,CAAC;UAAEZ,GAAG,EAAE;YAAEa,GAAG,EAAEf;UAAM;QAAE,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC,GAAAsC,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAnC,IAAA,GAAE;QAAA,IAAzDC,CAAC,GAAAmC,MAAA,CAAAlC,KAAA;QACV,IAAMZ,cAAc,GAAGnB,KAAK,CAACkC,sBAAsB,CAACnC,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;UAAEV,GAAG,EAAEiB,CAAC,CAACjB;QAAI,CAAC,CAAC,CAAC;QACzFd,MAAM,CAACqB,cAAc,CAACY,MAAM,CAAC;UAC3B,UAAU,EAAEF,CAAC,CAACjB,GAAG;UACjB,oBAAoB,EAAEgC,IAAI,CAAChC;QAC7B,CAAC,EAAE;UACDsB,IAAI,EAAE;YACJhB,cAAc,EAAE,CAACW,CAAC,CAACjB,GAAG,EAAAuB,MAAA,CAAAvC,kBAAA,CAAKsB,cAAc,GAAEkB,GAAG,CAAC,UAAAC,EAAE;cAAA,OAAK;gBAAEzB,GAAG,EAAEyB;cAAG,CAAC;YAAA,CAAC;UACpE;QACF,CAAC,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE2B,eAAe,EAAE,SAAAA,CAAUC,KAAK,EAAExD,KAAK,EAAEJ,OAAO,EAAE;MAChD,IAAI6D,EAAE;MAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIlD,KAAK,CAAC,0BAA0B,CAAC;MACvD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;MAEvDV,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;MAE1C;MACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtBqE,QAAQ,EAAE;MACZ,CAAC,EAAEjE,OAAO,CAAC;MAEX,SAAAkE,UAAA,GAAA3E,+BAAA,CAAmBqE,KAAK,GAAAO,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA5C,IAAA,GAAE;QAAA,IAAf8C,IAAI,GAAAD,MAAA,CAAA3C,KAAA;QACb,IAAIvC,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;UAC5BP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;QACf,CAAC,MAAM;UACLuD,EAAE,GAAGO,IAAI;QACX;QAEA,SAAAC,UAAA,GAAA9E,+BAAA,CAAmBa,KAAK,GAAAkE,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA/C,IAAA,GAAE;UAAA,IAAfgB,IAAI,GAAAgC,MAAA,CAAA9C,KAAA;UACb/B,KAAK,CAAC8E,cAAc,CAACV,EAAE,EAAEvB,IAAI,EAAEtC,OAAO,CAAC;QACzC;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEwE,YAAY,EAAE,SAAAA,CAAUZ,KAAK,EAAExD,KAAK,EAAEJ,OAAO,EAAE;MAC7C,IAAI6D,EAAE;MAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIlD,KAAK,CAAC,0BAA0B,CAAC;MACvD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;MAEvDV,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;MAE1C;MACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtBqE,QAAQ,EAAE,KAAK;QACfQ,QAAQ,EAAE;MACZ,CAAC,EAAEzE,OAAO,CAAC;MAEX,SAAA0E,UAAA,GAAAnF,+BAAA,CAAmBqE,KAAK,GAAAe,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAApD,IAAA,GAAE;QAAA,IAAf8C,IAAI,GAAAO,MAAA,CAAAnD,KAAA;QACb,IAAIvC,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;UAC5BP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;QACf,CAAC,MAAM;UACLuD,EAAE,GAAGO,IAAI;QACX;QACA;QACA,IAAMQ,QAAQ,GAAG;UAAE,UAAU,EAAEf;QAAG,CAAC;QACnC,IAAI,CAAC7D,OAAO,CAACyE,QAAQ,EAAE;UACrBG,QAAQ,CAACZ,KAAK,GAAGhE,OAAO,CAACgE,KAAK;QAChC;QAEAxE,MAAM,CAACqB,cAAc,CAACC,MAAM,CAAC8D,QAAQ,CAAC;;QAEtC;QACA,SAAAC,UAAA,GAAAtF,+BAAA,CAAmBa,KAAK,GAAA0E,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAvD,IAAA,GAAE;UAAA,IAAfgB,IAAI,GAAAwC,MAAA,CAAAtD,KAAA;UACb/B,KAAK,CAAC8E,cAAc,CAACV,EAAE,EAAEvB,IAAI,EAAEtC,OAAO,CAAC;QACzC;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEuE,cAAc,EAAE,SAAAA,CAAUQ,MAAM,EAAEhF,QAAQ,EAAEC,OAAO,EAAE;MACnDP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpC,IAAI,CAACe,MAAM,EAAE;QACX;MACF;MAEA,IAAMzC,IAAI,GAAG9C,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEP;MAAS,CAAC,EAAE;QAAEyD,MAAM,EAAE;UAAEhD,QAAQ,EAAE;QAAE;MAAE,CAAC,CAAC;MAEjF,IAAI,CAAC8B,IAAI,EAAE;QACT,IAAItC,OAAO,CAACiE,QAAQ,EAAE;UACpB,OAAO,EAAE;QACX,CAAC,MAAM;UACL,MAAM,IAAIvD,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;QAC9D;MACF;;MAEA;MACA,IAAMiF,GAAG,GAAGxF,MAAM,CAACqB,cAAc,CAACR,MAAM,CAAC;QACvC,UAAU,EAAE0E,MAAM;QAClB,UAAU,EAAEhF,QAAQ;QACpBiE,KAAK,EAAEhE,OAAO,CAACgE;MACjB,CAAC,EAAE;QACDzD,YAAY,EAAE;UACZ6D,IAAI,EAAE;YAAE9D,GAAG,EAAEyE;UAAO,CAAC;UACrBzC,IAAI,EAAE;YAAEhC,GAAG,EAAEP;UAAS,CAAC;UACvBiE,KAAK,EAAEhE,OAAO,CAACgE;QACjB;MACF,CAAC,CAAC;MAEF,IAAIgB,GAAG,CAACvE,UAAU,EAAE;QAClBjB,MAAM,CAACqB,cAAc,CAACY,MAAM,CAAC;UAAEnB,GAAG,EAAE0E,GAAG,CAACvE;QAAW,CAAC,EAAE;UACpDmB,IAAI,EAAE;YACJhB,cAAc,EAAE,CAACb,QAAQ,EAAA8B,MAAA,CAAAvC,kBAAA,CAAKG,KAAK,CAACkC,sBAAsB,CAACW,IAAI,CAAC,GAAER,GAAG,CAAC,UAAAP,CAAC;cAAA,OAAK;gBAAEjB,GAAG,EAAEiB;cAAE,CAAC;YAAA,CAAC;UACzF;QACF,CAAC,CAAC;MACJ;MAEA,OAAOyD,GAAG;IACZ,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEjE,mBAAmB,EAAE,SAAAA,CAAUuB,IAAI,EAAE;MACnC,IAAI,CAACA,IAAI,EAAE;QACT,OAAO,EAAE;MACX;MAEA,IAAM2C,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC5C,IAAI,CAAChC,GAAG,CAAC,CAAC;MAEvC,SAAA6E,UAAA,GAAA5F,+BAAA,CAAuB0F,WAAW,GAAAG,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA7D,IAAA,GAAE;QAAA,IAAzBvB,QAAQ,GAAAqF,MAAA,CAAA5D,KAAA;QACjBhC,MAAM,CAACY,KAAK,CAACc,IAAI,CAAC;UAAE,cAAc,EAAEnB;QAAS,CAAC,CAAC,CAACqB,KAAK,CAAC,CAAC,CAACiE,OAAO,CAAC,UAAAC,UAAU,EAAI;UAC5EL,WAAW,CAACM,GAAG,CAACD,UAAU,CAAChF,GAAG,CAAC;QACjC,CAAC,CAAC;MACJ;MAEA2E,WAAW,CAACO,MAAM,CAAClD,IAAI,CAAChC,GAAG,CAAC;MAE5B,OAAAhB,kBAAA,CAAW2F,WAAW;IACxB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEtD,sBAAsB,EAAE,SAAAA,CAAUW,IAAI,EAAE;MACtC,IAAM1B,cAAc,GAAG,IAAIsE,GAAG,CAAC,CAAC;MAChC,IAAMO,WAAW,GAAG,IAAIP,GAAG,CAAC,CAAC5C,IAAI,CAAC,CAAC;MAEnC,SAAAoD,WAAA,GAAAnG,+BAAA,CAAgBkG,WAAW,GAAAE,OAAA,IAAAA,OAAA,GAAAD,WAAA,IAAApE,IAAA,GAAE;QAAA,IAAlBC,CAAC,GAAAoE,OAAA,CAAAnE,KAAA;QACV,IAAMpB,KAAK,GAAGZ,MAAM,CAACY,KAAK,CAACc,IAAI,CAAC;UAAEZ,GAAG,EAAE;YAAEa,GAAG,EAAEI,CAAC,CAACf,QAAQ,CAACsB,GAAG,CAAC,UAAAP,CAAC;cAAA,OAAIA,CAAC,CAACjB,GAAG;YAAA;UAAE;QAAE,CAAC,EAAE;UAAEkD,MAAM,EAAE;YAAEhD,QAAQ,EAAE;UAAE;QAAE,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC;QAElH,SAAAwE,WAAA,GAAArG,+BAAA,CAAiBa,KAAK,GAAAyF,OAAA,IAAAA,OAAA,GAAAD,WAAA,IAAAtE,IAAA,GAAE;UAAA,IAAbS,EAAE,GAAA8D,OAAA,CAAArE,KAAA;UACXZ,cAAc,CAAC2E,GAAG,CAACxD,EAAE,CAACzB,GAAG,CAAC;UAC1BmF,WAAW,CAACF,GAAG,CAACxD,EAAE,CAAC;QACrB;MACF;MAEA,OAAAzC,kBAAA,CAAWsB,cAAc;IAC3B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEkF,oBAAoB,EAAE,SAAAA,CAAUlC,KAAK,EAAExD,KAAK,EAAEJ,OAAO,EAAE;MACrD,IAAI,CAAC4D,KAAK,EAAE,MAAM,IAAIlD,KAAK,CAAC,0BAA0B,CAAC;MACvD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;MAEvDV,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;MAE1C;MACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpC,SAAA+B,WAAA,GAAAxG,+BAAA,CAAmBqE,KAAK,GAAAoC,OAAA,IAAAA,OAAA,GAAAD,WAAA,IAAAzE,IAAA,GAAE;QAAA,IAAf8C,IAAI,GAAA4B,OAAA,CAAAxE,KAAA;QACb,IAAI,CAAC4C,IAAI,EAAE;QAEX,SAAA6B,WAAA,GAAA1G,+BAAA,CAAmBa,KAAK,GAAA8F,OAAA,IAAAA,OAAA,GAAAD,WAAA,IAAA3E,IAAA,GAAE;UAAA,IAAfgB,IAAI,GAAA4D,OAAA,CAAA1E,KAAA;UACb,IAAIqC,EAAE;UACN,IAAI5E,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;YAC5BP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;UACf,CAAC,MAAM;YACLuD,EAAE,GAAGO,IAAI;UACX;UAEA3E,KAAK,CAAC0G,mBAAmB,CAACtC,EAAE,EAAEvB,IAAI,EAAEtC,OAAO,CAAC;QAC9C;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEmG,mBAAmB,EAAE,SAAAA,CAAUpB,MAAM,EAAEhF,QAAQ,EAAEC,OAAO,EAAE;MACxDP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpC,IAAI,CAACe,MAAM,EAAE;MAEb,IAAMH,QAAQ,GAAG;QACf,UAAU,EAAEG,MAAM;QAClB,UAAU,EAAEhF;MACd,CAAC;MAED,IAAI,CAACC,OAAO,CAACyE,QAAQ,EAAE;QACrBG,QAAQ,CAACZ,KAAK,GAAGhE,OAAO,CAACgE,KAAK;MAChC;MAEAxE,MAAM,CAACqB,cAAc,CAACC,MAAM,CAAC8D,QAAQ,CAAC;IACxC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEwB,YAAY,EAAE,SAAAA,CAAUhC,IAAI,EAAEhE,KAAK,EAAEJ,OAAO,EAAE;MAC5C,IAAI6D,EAAE;MACN7D,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;MAE1C;MACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CA,KAAK,GAAGA,KAAK,CAACiG,MAAM,CAAC,UAAA9E,CAAC;QAAA,OAAIA,CAAC,IAAI,IAAI;MAAA,EAAC;MAEpC,IAAI,CAACnB,KAAK,CAAC6B,MAAM,EAAE,OAAO,KAAK;MAE/BxC,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB6E,QAAQ,EAAE;MACZ,CAAC,EAAEzE,OAAO,CAAC;MAEX,IAAIoE,IAAI,IAAInF,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;QACpCP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;MACf,CAAC,MAAM;QACLuD,EAAE,GAAGO,IAAI;MACX;MAEA,IAAI,CAACP,EAAE,EAAE,OAAO,KAAK;MACrB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAO,KAAK;MAExC,IAAMe,QAAQ,GAAG;QAAE,UAAU,EAAEf;MAAG,CAAC;MAEnC,IAAI,CAAC7D,OAAO,CAACyE,QAAQ,EAAE;QACrBG,QAAQ,CAACZ,KAAK,GAAG;UAAE7C,GAAG,EAAE,CAACnB,OAAO,CAACgE,KAAK,EAAE,IAAI;QAAE,CAAC;MACjD;MAEA,OAAO5D,KAAK,CAACkG,IAAI,CAAC,UAACvG,QAAQ,EAAK;QAC9B6E,QAAQ,CAAC,oBAAoB,CAAC,GAAG7E,QAAQ;QAEzC,OAAOP,MAAM,CAACqB,cAAc,CAACK,IAAI,CAAC0D,QAAQ,EAAE;UAAE2B,KAAK,EAAE;QAAE,CAAC,CAAC,CAAClE,KAAK,CAAC,CAAC,GAAG,CAAC;MACvE,CAAC,CAAC;IACJ,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEmE,eAAe,EAAE,SAAAA,CAAUpC,IAAI,EAAEpE,OAAO,EAAE;MACxC,IAAI6D,EAAE;MAEN7D,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;MAE1CP,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB6G,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,KAAK;QACnBjC,QAAQ,EAAE,KAAK;QACfkC,UAAU,EAAE;MACd,CAAC,EAAE3G,OAAO,CAAC;MAEX,IAAIoE,IAAI,IAAInF,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;QACpCP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;MACf,CAAC,MAAM;QACLuD,EAAE,GAAGO,IAAI;MACX;MAEA,IAAI,CAACP,EAAE,EAAE,OAAO,EAAE;MAElB,IAAMe,QAAQ,GAAG;QAAE,UAAU,EAAEf;MAAG,CAAC;MACnC,IAAMwC,MAAM,GAAG;QAAE7C,MAAM,EAAE;UAAE,oBAAoB,EAAE;QAAE;MAAE,CAAC;MAEtD,IAAI,CAACxD,OAAO,CAACyE,QAAQ,EAAE;QACrBG,QAAQ,CAACZ,KAAK,GAAG;UAAE7C,GAAG,EAAE,CAACnB,OAAO,CAACgE,KAAK;QAAE,CAAC;QAEzC,IAAI,CAAChE,OAAO,CAAC2G,UAAU,EAAE;UACvB/B,QAAQ,CAACZ,KAAK,CAAC7C,GAAG,CAACyF,IAAI,CAAC,IAAI,CAAC;QAC/B;MACF;MAEA,IAAI5G,OAAO,CAAC0G,YAAY,EAAE;QACxB,OAAOL,MAAM,CAAC7C,MAAM,CAAC,oBAAoB,CAAC;QAC1C6C,MAAM,CAAC7C,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;MAC/B;MAEA,IAAIxD,OAAO,CAACyG,WAAW,EAAE;QACvB,OAAOJ,MAAM,CAAC7C,MAAM;MACtB;MAEA,IAAMpD,KAAK,GAAGZ,MAAM,CAACqB,cAAc,CAACK,IAAI,CAAC0D,QAAQ,EAAEyB,MAAM,CAAC,CAACjF,KAAK,CAAC,CAAC;MAElE,IAAIpB,OAAO,CAACyG,WAAW,EAAE;QACvB,OAAOrG,KAAK;MACd;MAEA,OAAAd,kBAAA,CAAW,IAAI4F,GAAG,CAAC9E,KAAK,CAACyG,MAAM,CAAC,UAACC,GAAG,EAAEC,OAAO,EAAK;QAChD,IAAIA,OAAO,CAACnG,cAAc,EAAE;UAC1B,OAAOkG,GAAG,CAACjF,MAAM,CAACkF,OAAO,CAACnG,cAAc,CAACkB,GAAG,CAAC,UAAAP,CAAC;YAAA,OAAIA,CAAC,CAACjB,GAAG;UAAA,EAAC,CAAC;QAC3D,CAAC,MAAM,IAAIyG,OAAO,CAACzE,IAAI,EAAE;UACvBwE,GAAG,CAACF,IAAI,CAACG,OAAO,CAACzE,IAAI,CAAChC,GAAG,CAAC;QAC5B;QACA,OAAOwG,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEE,WAAW,EAAE,SAAAA,CAAUC,YAAY,EAAE;MACnCA,YAAY,GAAGA,YAAY,IAAI;QAAEC,IAAI,EAAE;UAAE5G,GAAG,EAAE;QAAE;MAAE,CAAC;MAEnD,OAAOd,MAAM,CAACY,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE+F,YAAY,CAAC;IAC5C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEE,cAAc,EAAE,SAAAA,CAAU/G,KAAK,EAAEJ,OAAO,EAAEiH,YAAY,EAAE;MACtD,IAAMG,GAAG,GAAG3H,KAAK,CAAC4H,yBAAyB,CAACjH,KAAK,EAAEJ,OAAO,CAAC,CAACoB,KAAK,CAAC,CAAC,CAACU,GAAG,CAAC,UAAAwF,CAAC;QAAA,OAAIA,CAAC,CAAClD,IAAI,CAAC9D,GAAG;MAAA,EAAC;MAExF,OAAOd,MAAM,CAACoE,KAAK,CAAC1C,IAAI,CAAC;QAAEZ,GAAG,EAAE;UAAEa,GAAG,EAAEiG;QAAI;MAAE,CAAC,EAAIpH,OAAO,IAAIA,OAAO,CAACiH,YAAY,IAAKA,YAAY,IAAK,CAAC,CAAC,CAAC;IAC5G,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEI,yBAAyB,EAAE,SAAAA,CAAUjH,KAAK,EAAEJ,OAAO,EAAE;MACnDA,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB6E,QAAQ,EAAE,KAAK;QACfwC,YAAY,EAAE,CAAC;MACjB,CAAC,EAAEjH,OAAO,CAAC;MAEX,OAAOP,KAAK,CAAC8H,qBAAqB,CAACnH,KAAK,EAAEJ,OAAO,EAAEA,OAAO,CAACiH,YAAY,CAAC;IAC1E,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEM,qBAAqB,EAAE,SAAAA,CAAUnH,KAAK,EAAEJ,OAAO,EAAEqG,MAAM,EAAE;MACvDrG,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB6E,QAAQ,EAAE,KAAK;QACfkC,UAAU,EAAE;MACd,CAAC,EAAE3G,OAAO,CAAC;;MAEX;MACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpCqC,MAAM,GAAG1G,MAAM,CAACC,MAAM,CAAC;QACrB4D,MAAM,EAAE;UAAE,UAAU,EAAE;QAAE;MAC1B,CAAC,EAAE6C,MAAM,CAAC;MAEV,IAAMzB,QAAQ,GAAG;QAAE,oBAAoB,EAAE;UAAEzD,GAAG,EAAEf;QAAM;MAAE,CAAC;MAEzD,IAAI,CAACJ,OAAO,CAACyE,QAAQ,EAAE;QACrBG,QAAQ,CAACZ,KAAK,GAAG;UAAE7C,GAAG,EAAE,CAACnB,OAAO,CAACgE,KAAK;QAAE,CAAC;QAEzC,IAAI,CAAChE,OAAO,CAAC2G,UAAU,EAAE;UACvB/B,QAAQ,CAACZ,KAAK,CAAC7C,GAAG,CAACyF,IAAI,CAAC,IAAI,CAAC;QAC/B;MACF;MAEA,OAAOpH,MAAM,CAACqB,cAAc,CAACK,IAAI,CAAC0D,QAAQ,EAAEyB,MAAM,CAAC;IACrD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACEmB,gBAAgB,EAAE,SAAAA,CAAA,EAAmB;MAAA,IAAAC,MAAA;MACnC,IAAI,CAAC/H,kCAAkC,EAAE;QACvCA,kCAAkC,GAAG,IAAI;QACzCgI,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,qEAAqE,CAAC;MAChG;MAEA,OAAO,CAAAF,MAAA,GAAAhI,KAAK,EAACmI,gBAAgB,CAAAC,KAAA,CAAAJ,MAAA,EAAAK,SAAQ,CAAC;IACxC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEF,gBAAgB,EAAE,SAAAA,CAAUxD,IAAI,EAAEhE,KAAK,EAAE;MACvC,IAAIyD,EAAE;MAEN,IAAIzD,KAAK,IAAI,CAACuC,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAEnD,IAAIgE,IAAI,IAAInF,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;QACpCP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;MACf,CAAC,MAAM;QACLuD,EAAE,GAAGO,IAAI;MACX;MAEA,IAAI,CAACP,EAAE,EAAE,OAAO,EAAE;MAElB,IAAMe,QAAQ,GAAG;QACf,UAAU,EAAEf,EAAE;QACdG,KAAK,EAAE;UAAEf,GAAG,EAAE;QAAK;MACrB,CAAC;MAED,IAAI7C,KAAK,EAAE;QACTwE,QAAQ,CAAC,oBAAoB,CAAC,GAAG;UAAEzD,GAAG,EAAEf;QAAM,CAAC;MACjD;MAEA,IAAM2H,MAAM,GAAGvI,MAAM,CAACqB,cAAc,CAACK,IAAI,CAAC0D,QAAQ,EAAE;QAAEpB,MAAM,EAAE;UAAEQ,KAAK,EAAE;QAAE;MAAE,CAAC,CAAC,CAAC5C,KAAK,CAAC,CAAC,CAACU,GAAG,CAAC,UAAAkG,GAAG;QAAA,OAAIA,GAAG,CAAChE,KAAK;MAAA,EAAC;MAE3G,OAAA1E,kBAAA,CAAW,IAAI4F,GAAG,CAAC6C,MAAM,CAAC;IAC5B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEE,WAAW,EAAE,SAAAA,CAAU9F,OAAO,EAAEC,OAAO,EAAE;MACvC,IAAIC,KAAK;MAET5C,KAAK,CAACsE,eAAe,CAAC5B,OAAO,CAAC;MAC9B1C,KAAK,CAACsE,eAAe,CAAC3B,OAAO,CAAC;MAE9B,IAAID,OAAO,KAAKC,OAAO,EAAE;MAEzB,GAAG;QACDC,KAAK,GAAG7C,MAAM,CAACqB,cAAc,CAACY,MAAM,CAAC;UACnCuC,KAAK,EAAE7B;QACT,CAAC,EAAE;UACDP,IAAI,EAAE;YACJoC,KAAK,EAAE5B;UACT;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;IACpB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE6F,WAAW,EAAE,SAAAA,CAAUC,IAAI,EAAE;MAC3B1I,KAAK,CAACsE,eAAe,CAACoE,IAAI,CAAC;MAE3B3I,MAAM,CAACqB,cAAc,CAACC,MAAM,CAAC;QAAEkD,KAAK,EAAEmE;MAAK,CAAC,CAAC;IAC/C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACElI,cAAc,EAAE,SAAAA,CAAUF,QAAQ,EAAE;MAClC,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACqI,IAAI,CAAC,CAAC,KAAKrI,QAAQ,EAAE;QAC7E,MAAM,IAAIW,KAAK,CAAC,sBAAsB,GAAGX,QAAQ,GAAG,KAAK,CAAC;MAC5D;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEsI,UAAU,EAAE,SAAAA,CAAUC,cAAc,EAAEC,aAAa,EAAE;MACnD,IAAID,cAAc,KAAKC,aAAa,EAAE;QACpC,OAAO,IAAI;MACb;MAEA,IAAID,cAAc,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;QACnD,OAAO,KAAK;MACd;MAEA9I,KAAK,CAACQ,cAAc,CAACqI,cAAc,CAAC;MACpC7I,KAAK,CAACQ,cAAc,CAACsI,aAAa,CAAC;MAEnC,IAAIC,YAAY,GAAG,CAACF,cAAc,CAAC;MACnC,OAAOE,YAAY,CAACvG,MAAM,KAAK,CAAC,EAAE;QAChC,IAAMlC,QAAQ,GAAGyI,YAAY,CAACC,GAAG,CAAC,CAAC;QAEnC,IAAI1I,QAAQ,KAAKwI,aAAa,EAAE;UAC9B,OAAO,IAAI;QACb;QAEA,IAAMjG,IAAI,GAAG9C,MAAM,CAACY,KAAK,CAACY,OAAO,CAAC;UAAEV,GAAG,EAAEP;QAAS,CAAC,CAAC;;QAEpD;QACA,IAAI,CAACuC,IAAI,EAAE;QAEXkG,YAAY,GAAGA,YAAY,CAAC3G,MAAM,CAACS,IAAI,CAAC9B,QAAQ,CAACsB,GAAG,CAAC,UAAAP,CAAC;UAAA,OAAIA,CAAC,CAACjB,GAAG;QAAA,EAAC,CAAC;MACnE;MAEA,OAAO,KAAK;IACd,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEwD,iBAAiB,EAAE,SAAAA,CAAU9D,OAAO,EAAE;MACpCA,OAAO,GAAGA,OAAO,KAAK0I,SAAS,GAAG,CAAC,CAAC,GAAG1I,OAAO;MAE9C,IAAIA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACnDA,OAAO,GAAG;UAAEgE,KAAK,EAAEhE;QAAQ,CAAC;MAC9B;MAEAA,OAAO,CAACgE,KAAK,GAAGvE,KAAK,CAACkJ,mBAAmB,CAAC3I,OAAO,CAACgE,KAAK,CAAC;MAExD,OAAOhE,OAAO;IAChB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE2I,mBAAmB,EAAE,SAAAA,CAAUC,SAAS,EAAE;MACxC;MACA,IAAIA,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOA,SAAS;MAClB;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE7E,eAAe,EAAE,SAAAA,CAAU6E,SAAS,EAAE;MACpC,IAAIA,SAAS,KAAK,IAAI,EAAE;MAExB,IACE,CAACA,SAAS,IACV,OAAOA,SAAS,KAAK,QAAQ,IAC7BA,SAAS,CAACR,IAAI,CAAC,CAAC,KAAKQ,SAAS,EAC9B;QACA,MAAM,IAAIlI,KAAK,0BAAwBkI,SAAS,OAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;AAAA,EAAAC,IAAA,OAAAC,MAAA,E;;;;;;;;;;;;EC7kCF,IAAIC,mBAAmB;EAAC7J,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAAC0J,mBAAmB,GAAC1J,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIJ,OAAO;EAACC,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACJ,OAAO,GAACI,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIC,kBAAkB;EAACJ,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACC,kBAAkB,GAACD,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIE,+BAA+B;EAACL,OAAO,CAACC,IAAI,CAAC,uDAAuD,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACE,+BAA+B,GAACF,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAjfH,OAAO,CAAC8J,MAAM,CAAC;IAACC,eAAe,EAAC,SAAAA,CAAA,EAAU;MAAC,OAAOA,eAAe;IAAA,CAAC;IAACC,wBAAwB,EAAC,SAAAA,CAAA,EAAU;MAAC,OAAOA,wBAAwB;IAAA;EAAC,CAAC,CAAC;EAAC,IAAI1J,MAAM;EAACN,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;IAACK,MAAM,EAAC,SAAAA,CAASH,CAAC,EAAC;MAACG,MAAM,GAACH,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAI8J,KAAK;EAACjK,OAAO,CAACC,IAAI,CAAC,cAAc,EAAC;IAACgK,KAAK,EAAC,SAAAA,CAAS9J,CAAC,EAAC;MAAC8J,KAAK,GAAC9J,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EA4BlR,IAAM4J,eAAe,GAAG,IAAIE,KAAK,CAACC,UAAU,CAAC,OAAO,CAAC;EAE5D,IAAI,CAAC5J,MAAM,CAACY,KAAK,EAAE;IACjBZ,MAAM,CAACY,KAAK,GAAG6I,eAAe;EAChC;EAEO,IAAMC,wBAAwB,GAAG,IAAIC,KAAK,CAACC,UAAU,CAAC,iBAAiB,CAAC;EAE/E,IAAI,CAAC5J,MAAM,CAACqB,cAAc,EAAE;IAC1BrB,MAAM,CAACqB,cAAc,GAAGqI,wBAAwB;EAClD;;EAEA;AACA;AACA;EACA,IAAI,OAAOzJ,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAG,CAAC,CAAC,EAAC;EACb;EAEA,IAAIC,kCAAkC,GAAG,KAAK;;EAE9C;AACA;AACA;AACA;AACA;AACA;EACA,IAAM2J,SAAS;IAAG,SAAAC,QAAOC,GAAG,EAAEC,SAAS;MAAA,IAAAvI,SAAA,EAAAI,KAAA,EAAAoI,CAAA;MAAA,OAAAV,mBAAA,CAAAW,KAAA;QAAA,SAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA7I,SAAA,GAAA1B,+BAAA,CACrBgK,GAAG;YAAA;cAAA,KAAAlI,KAAA,GAAAJ,SAAA,IAAAK,IAAA;gBAAAsI,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAARL,CAAC,GAAApI,KAAA,CAAAG,KAAA;cAAAoI,QAAA,CAAAE,IAAA;cAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACAP,SAAS,CAACC,CAAC,CAAC;YAAA;cAAA,KAAAG,QAAA,CAAAI,IAAA;gBAAAJ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAK,MAAA,WAAS,IAAI;YAAA;cAAAL,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAA,OAAAF,QAAA,CAAAK,MAAA,WAE9B,KAAK;YAAA;YAAA;cAAA,OAAAL,QAAA,CAAAM,IAAA;UAAA;QAAA;QAAA,OAAAP,QAAA;MAAA,uBAAAQ,OAAA;IAAA;IACb,OAAAb,OAAA;EAAA;EAED3J,MAAM,CAACC,MAAM,CAACH,KAAK,EAAE;IACnB;AACF;AACA;AACA;AACA;AACA;AACA;IACEI,YAAY,EAAE,IAAI;IAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEuK,eAAe;MAAE,SAAAC,SAAgBtK,QAAQ,EAAEC,OAAO;QAAA,IAAAS,UAAA,EAAA6J,YAAA;QAAA,OAAAvB,mBAAA,CAAAW,KAAA;UAAA,SAAAa,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAX,IAAA,GAAAW,SAAA,CAAAV,IAAA;cAAA;gBAChDrK,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;gBAE9BC,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;kBACEM,YAAY,EAAE;gBAChB,CAAC,EACDF,OACF,CAAC;gBAEGS,UAAU,GAAG,IAAI;gBAAA+J,SAAA,CAAAV,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEMvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAEP;gBAAS,CAAC,CAAC;cAAA;gBAAjEuK,YAAY,GAAAE,SAAA,CAAAR,IAAA;gBAAA,KAEdM,YAAY;kBAAAE,SAAA,CAAAV,IAAA;kBAAA;gBAAA;gBAAAU,SAAA,CAAAV,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACRvK,MAAM,CAACY,KAAK,CAACsK,WAAW,CAC5B;kBAAEpK,GAAG,EAAEP;gBAAS,CAAC,EACjB;kBAAEQ,YAAY,EAAE;oBAAEC,QAAQ,EAAE;kBAAG;gBAAE,CACnC,CAAC;cAAA;gBAAA,OAAAgK,SAAA,CAAAP,MAAA,WACM,IAAI;cAAA;gBAAAO,SAAA,CAAAV,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEQvK,MAAM,CAACY,KAAK,CAACuK,WAAW,CAAC;kBAC1CrK,GAAG,EAAEP,QAAQ;kBACbS,QAAQ,EAAE;gBACZ,CAAC,CAAC;cAAA;gBAHFC,UAAU,GAAA+J,SAAA,CAAAR,IAAA;cAAA;gBAAA,IAMPvJ,UAAU;kBAAA+J,SAAA,CAAAV,IAAA;kBAAA;gBAAA;gBAAA,KACT9J,OAAO,CAACE,YAAY;kBAAAsK,SAAA,CAAAV,IAAA;kBAAA;gBAAA;gBAAA,OAAAU,SAAA,CAAAP,MAAA,WAAS,IAAI;cAAA;gBAAA,MAC/B,IAAIvJ,KAAK,CAAC,QAAQ,GAAGX,QAAQ,GAAG,mBAAmB,CAAC;cAAA;gBAAA,OAAAyK,SAAA,CAAAP,MAAA,WAGrDxJ,UAAU;cAAA;cAAA;gBAAA,OAAA+J,SAAA,CAAAN,IAAA;YAAA;UAAA;UAAA,OAAAK,SAAA;QAAA,uBAAAJ,OAAA;MAAA;MAClB,OAAAE,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEO,eAAe;MAAE,SAAAC,SAAgB9K,QAAQ;QAAA,IAAAK,KAAA,EAAAQ,cAAA,EAAAiC,UAAA,EAAAC,MAAA,EAAAvB,CAAA;QAAA,OAAAwH,mBAAA,CAAAW,KAAA;UAAA,SAAAoB,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;cAAA;gBAIvCrK,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;;gBAE9B;gBAAAgL,SAAA,CAAAjB,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACMvK,MAAM,CAACqB,cAAc,CAACmK,WAAW,CAAC;kBACtC,UAAU,EAAEjL;gBACd,CAAC,CAAC;cAAA;gBAAAgL,SAAA,CAAAE,EAAA,GAAAlC,mBAAA;gBAAAgC,SAAA,CAAAG,EAAA,GAIczL,KAAK;gBAAAsL,SAAA,CAAAjB,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACXvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAEP;gBAAS,CAAC,CAAC;cAAA;gBAAAgL,SAAA,CAAAI,EAAA,GAAAJ,SAAA,CAAAf,IAAA;gBAAAe,SAAA,CAAAK,EAAA,GAAAL,SAAA,CAAAG,EAAA,CADhCG,wBAAwB,CAAAxC,IAAA,CAAAkC,SAAA,CAAAG,EAAA,EAAAH,SAAA,CAAAI,EAAA;gBAAAJ,SAAA,CAAAjB,IAAA;gBAAA,OAAAiB,SAAA,CAAAE,EAAA,CAAAlB,KAAA,CAAAlB,IAAA,CAAAkC,SAAA,CAAAE,EAAA,EAAAF,SAAA,CAAAK,EAAA;cAAA;gBAA5ChL,KAAK,GAAA2K,SAAA,CAAAf,IAAA;gBAAAe,SAAA,CAAAO,EAAA,GAAA/L,+BAAA;gBAAAwL,SAAA,CAAAjB,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAIiBvK,MAAM,CAACY,KAAK,CAC/Bc,IAAI,CAAC;kBAAEZ,GAAG,EAAE;oBAAEa,GAAG,EAAEf;kBAAM;gBAAE,CAAC,CAAC,CAC7BmL,UAAU,CAAC,CAAC;cAAA;gBAAAR,SAAA,CAAAS,EAAA,GAAAT,SAAA,CAAAf,IAAA;gBAAAnH,UAAA,OAAAkI,SAAA,CAAAO,EAAA,EAAAP,SAAA,CAAAS,EAAA;cAAA;gBAAA,KAAA1I,MAAA,GAAAD,UAAA,IAAAvB,IAAA;kBAAAyJ,SAAA,CAAAjB,IAAA;kBAAA;gBAAA;gBAFJvI,CAAC,GAAAuB,MAAA,CAAAtB,KAAA;gBAAAuJ,SAAA,CAAAjB,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGJvK,MAAM,CAACY,KAAK,CAACsK,WAAW,CAC5B;kBACEpK,GAAG,EAAEiB,CAAC,CAACjB;gBACT,CAAC,EACD;kBACEoB,KAAK,EAAE;oBACLlB,QAAQ,EAAE;sBACRF,GAAG,EAAEP;oBACP;kBACF;gBACF,CACF,CAAC;cAAA;gBAAAgL,SAAA,CAAAU,EAAA,GAAA1C,mBAAA;gBAAAgC,SAAA,CAAAW,EAAA,GAEsBjM,KAAK;gBAAAsL,SAAA,CAAAjB,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACpBvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAEiB,CAAC,CAACjB;gBAAI,CAAC,CAAC;cAAA;gBAAAyK,SAAA,CAAAY,EAAA,GAAAZ,SAAA,CAAAf,IAAA;gBAAAe,SAAA,CAAAa,EAAA,GAAAb,SAAA,CAAAW,EAAA,CADpBG,2BAA2B,CAAAhD,IAAA,CAAAkC,SAAA,CAAAW,EAAA,EAAAX,SAAA,CAAAY,EAAA;gBAAAZ,SAAA,CAAAjB,IAAA;gBAAA,OAAAiB,SAAA,CAAAU,EAAA,CAAA1B,KAAA,CAAAlB,IAAA,CAAAkC,SAAA,CAAAU,EAAA,EAAAV,SAAA,CAAAa,EAAA;cAAA;gBAAxDhL,cAAc,GAAAmK,SAAA,CAAAf,IAAA;gBAAAe,SAAA,CAAAjB,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGRvK,MAAM,CAACqB,cAAc,CAAC6J,WAAW,CACrC;kBACE,UAAU,EAAEnJ,CAAC,CAACjB;gBAChB,CAAC,EACD;kBACEsB,IAAI,EAAE;oBACJhB,cAAc,EAAE,CAACW,CAAC,CAACjB,GAAG,EAAAuB,MAAA,CAAAvC,kBAAA,CAAKsB,cAAc,GAAEkB,GAAG,CAAC,UAACC,EAAE;sBAAA,OAAM;wBACtDzB,GAAG,EAAEyB;sBACP,CAAC;oBAAA,CAAC;kBACJ;gBACF,CAAC,EACD;kBAAEC,KAAK,EAAE;gBAAK,CAChB,CAAC;cAAA;gBAAA+I,SAAA,CAAAjB,IAAA;gBAAA;cAAA;gBAAA,IAEI1J,KAAK,CAAC6B,MAAM,GAAG,CAAC;kBAAA8I,SAAA,CAAAjB,IAAA;kBAAA;gBAAA;cAAA;gBAAAiB,SAAA,CAAAjB,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGnBvK,MAAM,CAACY,KAAK,CAAC4K,WAAW,CAAC;kBAAE1K,GAAG,EAAEP;gBAAS,CAAC,CAAC;cAAA;cAAA;gBAAA,OAAAgL,SAAA,CAAAb,IAAA;YAAA;UAAA;UAAA,OAAAY,SAAA;QAAA,uBAAAX,OAAA;MAAA;MAClD,OAAAU,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEiB,eAAe;MAAE,SAAAC,SAAgB5J,OAAO,EAAEC,OAAO;QAAA,IAAAC,KAAA,EAAAC,IAAA;QAAA,OAAAyG,mBAAA,CAAAW,KAAA;UAAA,SAAAsC,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAApC,IAAA,GAAAoC,SAAA,CAAAnC,IAAA;cAAA;gBAG/CrK,KAAK,CAACQ,cAAc,CAACkC,OAAO,CAAC;gBAC7B1C,KAAK,CAACQ,cAAc,CAACmC,OAAO,CAAC;gBAAA,MAEzBD,OAAO,KAAKC,OAAO;kBAAA6J,SAAA,CAAAnC,IAAA;kBAAA;gBAAA;gBAAA,OAAAmC,SAAA,CAAAhC,MAAA;cAAA;gBAAAgC,SAAA,CAAAnC,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEJvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAE6B;gBAAQ,CAAC,CAAC;cAAA;gBAAxDG,IAAI,GAAA2J,SAAA,CAAAjC,IAAA;gBAAA,IAEL1H,IAAI;kBAAA2J,SAAA,CAAAnC,IAAA;kBAAA;gBAAA;gBAAA,MACD,IAAIpJ,KAAK,CAAC,QAAQ,GAAGyB,OAAO,GAAG,mBAAmB,CAAC;cAAA;gBAG3DG,IAAI,CAAChC,GAAG,GAAG8B,OAAO;gBAAA6J,SAAA,CAAAnC,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEZvK,MAAM,CAACY,KAAK,CAACuK,WAAW,CAACrI,IAAI,CAAC;cAAA;gBAAA2J,SAAA,CAAAnC,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGpBvK,MAAM,CAACqB,cAAc,CAAC6J,WAAW,CAC7C;kBACE,UAAU,EAAEvI;gBACd,CAAC,EACD;kBACEP,IAAI,EAAE;oBACJ,UAAU,EAAEQ;kBACd;gBACF,CAAC,EACD;kBAAEJ,KAAK,EAAE;gBAAK,CAChB,CAAC;cAAA;gBAVDK,KAAK,GAAA4J,SAAA,CAAAjC,IAAA;cAAA;gBAAA,IAWE3H,KAAK,GAAG,CAAC;kBAAA4J,SAAA,CAAAnC,IAAA;kBAAA;gBAAA;cAAA;gBAAAmC,SAAA,CAAAnC,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGFvK,MAAM,CAACqB,cAAc,CAAC6J,WAAW,CAC7C;kBACE,oBAAoB,EAAEvI;gBACxB,CAAC,EACD;kBACEP,IAAI,EAAE;oBACJ,sBAAsB,EAAEQ;kBAC1B;gBACF,CAAC,EACD;kBAAEJ,KAAK,EAAE;gBAAK,CAChB,CAAC;cAAA;gBAVDK,KAAK,GAAA4J,SAAA,CAAAjC,IAAA;cAAA;gBAAA,IAWE3H,KAAK,GAAG,CAAC;kBAAA4J,SAAA,CAAAnC,IAAA;kBAAA;gBAAA;cAAA;gBAAAmC,SAAA,CAAAnC,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGFvK,MAAM,CAACY,KAAK,CAACsK,WAAW,CACpC;kBACE,cAAc,EAAEvI;gBAClB,CAAC,EACD;kBACEP,IAAI,EAAE;oBACJ,gBAAgB,EAAEQ;kBACpB;gBACF,CAAC,EACD;kBAAEJ,KAAK,EAAE;gBAAK,CAChB,CAAC;cAAA;gBAVDK,KAAK,GAAA4J,SAAA,CAAAjC,IAAA;cAAA;gBAAA,IAWE3H,KAAK,GAAG,CAAC;kBAAA4J,SAAA,CAAAnC,IAAA;kBAAA;gBAAA;cAAA;gBAAAmC,SAAA,CAAAnC,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEZvK,MAAM,CAACY,KAAK,CAAC4K,WAAW,CAAC;kBAAE1K,GAAG,EAAE6B;gBAAQ,CAAC,CAAC;cAAA;cAAA;gBAAA,OAAA8J,SAAA,CAAA/B,IAAA;YAAA;UAAA;UAAA,OAAA8B,SAAA;QAAA,uBAAA7B,OAAA;MAAA;MACjD,OAAA4B,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,qBAAqB;MAAE,SAAAC,SAAgB1J,UAAU,EAAEC,UAAU;QAAA,IAAAW,UAAA,EAAAC,MAAA,EAAAvD,QAAA;QAAA,OAAAgJ,mBAAA,CAAAW,KAAA;UAAA,SAAA0C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;cAAA;gBAC3D;gBACA,IAAI,CAACnH,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;gBAAAY,UAAA,GAAA9D,+BAAA,CAElCkD,UAAU;cAAA;gBAAA,KAAAa,MAAA,GAAAD,UAAA,IAAA/B,IAAA;kBAAA+K,SAAA,CAAAvC,IAAA;kBAAA;gBAAA;gBAAtB/J,QAAQ,GAAAuD,MAAA,CAAA9B,KAAA;gBAAA6K,SAAA,CAAAvC,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACXtK,KAAK,CAAC6M,qBAAqB,CAACvM,QAAQ,EAAE2C,UAAU,CAAC;cAAA;gBAAA2J,SAAA,CAAAvC,IAAA;gBAAA;cAAA;cAAA;gBAAA,OAAAuC,SAAA,CAAAnC,IAAA;YAAA;UAAA;UAAA,OAAAkC,SAAA;QAAA,uBAAAjC,OAAA;MAAA;MAE1D,OAAAgC,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,qBAAqB;MAAE,SAAAC,SAAgBxM,QAAQ,EAAE2C,UAAU;QAAA,IAAAJ,IAAA,EAAAD,KAAA;QAAA,OAAA0G,mBAAA,CAAAW,KAAA;UAAA,SAAA8C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;cAAA;gBACzDrK,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;gBAC9BN,KAAK,CAACQ,cAAc,CAACyC,UAAU,CAAC;;gBAEhC;gBAAA+J,SAAA,CAAA3C,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACmBvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAEP;gBAAS,CAAC,CAAC;cAAA;gBAAzDuC,IAAI,GAAAmK,SAAA,CAAAzC,IAAA;gBAAA,IAEL1H,IAAI;kBAAAmK,SAAA,CAAA3C,IAAA;kBAAA;gBAAA;gBAAA,MACD,IAAIpJ,KAAK,YAAUX,QAAQ,sBAAmB,CAAC;cAAA;gBAAA0M,SAAA,CAAA3C,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAI5CtK,KAAK,CAACoM,2BAA2B,CAACvJ,IAAI,CAAC;cAAA;gBAAA,KAAAmK,SAAA,CAAAzC,IAAA,CAAEhH,QAAQ,CAACN,UAAU;kBAAA+J,SAAA,CAAA3C,IAAA;kBAAA;gBAAA;gBAAA,MAC/D,IAAIpJ,KAAK,aACHX,QAAQ,eAAU2C,UAAU,0BACxC,CAAC;cAAA;gBAAA+J,SAAA,CAAA3C,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGiBvK,MAAM,CAACY,KAAK,CAACsK,WAAW,CAC1C;kBACEpK,GAAG,EAAEoC,UAAU;kBACf,cAAc,EAAE;oBACdO,GAAG,EAAEX,IAAI,CAAChC;kBACZ;gBACF,CAAC,EACD;kBACE4C,KAAK,EAAE;oBACL1C,QAAQ,EAAE;sBACRF,GAAG,EAAEgC,IAAI,CAAChC;oBACZ;kBACF;gBACF,CACF,CAAC;cAAA;gBAdK+B,KAAK,GAAAoK,SAAA,CAAAzC,IAAA;gBAAA,IAkBN3H,KAAK;kBAAAoK,SAAA,CAAA3C,IAAA;kBAAA;gBAAA;gBAAA,OAAA2C,SAAA,CAAAxC,MAAA;cAAA;gBAAAwC,SAAA,CAAAxB,EAAA,GAAAlC,mBAAA;gBAAA0D,SAAA,CAAAvB,EAAA,GAEJ1L,MAAM,CAACqB,cAAc;gBAAA4L,SAAA,CAAAtB,EAAA,GACzB;kBACE,oBAAoB,EAAEzI;gBACxB,CAAC;gBAAA+J,SAAA,CAAArB,EAAA,IAKO9I,IAAI,CAAChC,GAAG;gBAAAmM,SAAA,CAAAnB,EAAA,GAAAhM,kBAAA;gBAAAmN,SAAA,CAAA3C,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACEtK,KAAK,CAACoM,2BAA2B,CAACvJ,IAAI,CAAC;cAAA;gBAAAmK,SAAA,CAAAjB,EAAA,GAAAiB,SAAA,CAAAzC,IAAA;gBAAAyC,SAAA,CAAAhB,EAAA,OAAAgB,SAAA,CAAAnB,EAAA,EAAAmB,SAAA,CAAAjB,EAAA;gBAAAiB,SAAA,CAAAf,EAAA,GAAAe,SAAA,CAAArB,EAAA,CAAAvJ,MAAA,CAAAgH,IAAA,CAAA4D,SAAA,CAAArB,EAAA,EAAAqB,SAAA,CAAAhB,EAAA,EACjD3J,GAAG,CAAC,UAACP,CAAC;kBAAA,OAAM;oBAAEjB,GAAG,EAAEiB;kBAAE,CAAC;gBAAA,CAAC;gBAAAkL,SAAA,CAAAd,EAAA;kBAHzBxI,KAAK,EAAAsJ,SAAA,CAAAf;gBAAA;gBAAAe,SAAA,CAAAb,EAAA;kBADPhL,cAAc,EAAA6L,SAAA,CAAAd;gBAAA;gBAAAc,SAAA,CAAAC,GAAA;kBADhBxJ,KAAK,EAAAuJ,SAAA,CAAAb;gBAAA;gBAAAa,SAAA,CAAAE,GAAA,GASP;kBAAE3K,KAAK,EAAE;gBAAK,CAAC;gBAAAyK,SAAA,CAAAG,GAAA,GAAAH,SAAA,CAAAvB,EAAA,CAdWR,WAAW,CAAA7B,IAAA,CAAA4D,SAAA,CAAAvB,EAAA,EAAAuB,SAAA,CAAAtB,EAAA,EAAAsB,SAAA,CAAAC,GAAA,EAAAD,SAAA,CAAAE,GAAA;gBAAAF,SAAA,CAAA3C,IAAA;gBAAA,OAAA2C,SAAA,CAAAxB,EAAA,CAAAlB,KAAA,CAAAlB,IAAA,CAAA4D,SAAA,CAAAxB,EAAA,EAAAwB,SAAA,CAAAG,GAAA;cAAA;cAAA;gBAAA,OAAAH,SAAA,CAAAvC,IAAA;YAAA;UAAA;UAAA,OAAAsC,SAAA;QAAA,uBAAArC,OAAA;MAAA;MAgBxC,OAAAoC,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEM,0BAA0B;MAAE,SAAAC,SAAgBrK,UAAU,EAAEC,UAAU;QAAA,IAAAe,UAAA,EAAAC,MAAA,EAAA3D,QAAA;QAAA,OAAAgJ,mBAAA,CAAAW,KAAA;UAAA,SAAAqD,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAnD,IAAA,GAAAmD,SAAA,CAAAlD,IAAA;cAAA;gBAChE;gBACA,IAAI,CAACnH,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;gBAAAgB,UAAA,GAAAlE,+BAAA,CAElCkD,UAAU;cAAA;gBAAA,KAAAiB,MAAA,GAAAD,UAAA,IAAAnC,IAAA;kBAAA0L,SAAA,CAAAlD,IAAA;kBAAA;gBAAA;gBAAtB/J,QAAQ,GAAA2D,MAAA,CAAAlC,KAAA;gBAAAwL,SAAA,CAAAlD,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACXtK,KAAK,CAACwN,0BAA0B,CAAClN,QAAQ,EAAE2C,UAAU,CAAC;cAAA;gBAAAsK,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cAAA;gBAAA,OAAAkD,SAAA,CAAA9C,IAAA;YAAA;UAAA;UAAA,OAAA6C,SAAA;QAAA,uBAAA5C,OAAA;MAAA;MAE/D,OAAA2C,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,0BAA0B;MAAE,SAAAC,SAAgBnN,QAAQ,EAAE2C,UAAU;QAAA,IAAAJ,IAAA,EAAAD,KAAA,EAAAjC,KAAA,EAAA8D,UAAA,EAAAC,MAAA,EAAA5C,CAAA,EAAAX,cAAA;QAAA,OAAAmI,mBAAA,CAAAW,KAAA;UAAA,SAAAyD,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAvD,IAAA,GAAAuD,SAAA,CAAAtD,IAAA;cAAA;gBAC9DrK,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;gBAC9BN,KAAK,CAACQ,cAAc,CAACyC,UAAU,CAAC;;gBAEhC;gBACA;gBAAA0K,SAAA,CAAAtD,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACmBvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAC1C;kBAAEnK,GAAG,EAAEP;gBAAS,CAAC,EACjB;kBAAEyD,MAAM,EAAE;oBAAElD,GAAG,EAAE;kBAAE;gBAAE,CACvB,CAAC;cAAA;gBAHKgC,IAAI,GAAA8K,SAAA,CAAApD,IAAA;gBAAA,IAKL1H,IAAI;kBAAA8K,SAAA,CAAAtD,IAAA;kBAAA;gBAAA;gBAAA,MACD,IAAIpJ,KAAK,YAAUX,QAAQ,sBAAmB,CAAC;cAAA;gBAAAqN,SAAA,CAAAtD,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGnCvK,MAAM,CAACY,KAAK,CAACsK,WAAW,CAC1C;kBACEpK,GAAG,EAAEoC;gBACP,CAAC,EACD;kBACEhB,KAAK,EAAE;oBACLlB,QAAQ,EAAE;sBACRF,GAAG,EAAEgC,IAAI,CAAChC;oBACZ;kBACF;gBACF,CACF,CAAC;cAAA;gBAXK+B,KAAK,GAAA+K,SAAA,CAAApD,IAAA;gBAAA,IAeN3H,KAAK;kBAAA+K,SAAA,CAAAtD,IAAA;kBAAA;gBAAA;gBAAA,OAAAsD,SAAA,CAAAnD,MAAA;cAAA;gBAAAmD,SAAA,CAAAnC,EAAA;gBAAAmC,SAAA,CAAAlC,EAAA,GAAA5L,kBAAA;gBAAA8N,SAAA,CAAAjC,EAAA,GAAApC,mBAAA;gBAAAqE,SAAA,CAAAhC,EAAA,GAIE3L,KAAK;gBAAA2N,SAAA,CAAAtD,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACPvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAEoC;gBAAW,CAAC,CAAC;cAAA;gBAAA0K,SAAA,CAAA9B,EAAA,GAAA8B,SAAA,CAAApD,IAAA;gBAAAoD,SAAA,CAAA5B,EAAA,GAAA4B,SAAA,CAAAhC,EAAA,CADtCC,wBAAwB,CAAAxC,IAAA,CAAAuE,SAAA,CAAAhC,EAAA,EAAAgC,SAAA,CAAA9B,EAAA;gBAAA8B,SAAA,CAAAtD,IAAA;gBAAA,OAAAsD,SAAA,CAAAjC,EAAA,CAAApB,KAAA,CAAAlB,IAAA,CAAAuE,SAAA,CAAAjC,EAAA,EAAAiC,SAAA,CAAA5B,EAAA;cAAA;gBAAA4B,SAAA,CAAA3B,EAAA,GAAA2B,SAAA,CAAApD,IAAA;gBAAAoD,SAAA,CAAA1B,EAAA,OAAA0B,SAAA,CAAAlC,EAAA,EAAAkC,SAAA,CAAA3B,EAAA;gBAAA2B,SAAA,CAAAzB,EAAA,IAGxCjJ,UAAU;gBAJNtC,KAAK,GAAAgN,SAAA,CAAAnC,EAAA,CAAApJ,MAAA,CAAAgH,IAAA,CAAAuE,SAAA,CAAAnC,EAAA,EAAAmC,SAAA,CAAA1B,EAAA,EAAA0B,SAAA,CAAAzB,EAAA;gBAAAyB,SAAA,CAAAxB,EAAA,GAAArM,+BAAA;gBAAA6N,SAAA,CAAAtD,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAOWvK,MAAM,CAACY,KAAK,CAC/Bc,IAAI,CAAC;kBAAEZ,GAAG,EAAE;oBAAEa,GAAG,EAAEf;kBAAM;gBAAE,CAAC,CAAC,CAC7BmL,UAAU,CAAC,CAAC;cAAA;gBAAA6B,SAAA,CAAAV,GAAA,GAAAU,SAAA,CAAApD,IAAA;gBAAA9F,UAAA,OAAAkJ,SAAA,CAAAxB,EAAA,EAAAwB,SAAA,CAAAV,GAAA;cAAA;gBAAA,KAAAvI,MAAA,GAAAD,UAAA,IAAA5C,IAAA;kBAAA8L,SAAA,CAAAtD,IAAA;kBAAA;gBAAA;gBAFJvI,CAAC,GAAA4C,MAAA,CAAA3C,KAAA;gBAAA4L,SAAA,CAAAT,GAAA,GAAA5D,mBAAA;gBAAAqE,SAAA,CAAAR,GAAA,GAGmBnN,KAAK;gBAAA2N,SAAA,CAAAtD,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAC1BvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAEiB,CAAC,CAACjB;gBAAI,CAAC,CAAC;cAAA;gBAAA8M,SAAA,CAAAC,GAAA,GAAAD,SAAA,CAAApD,IAAA;gBAAAoD,SAAA,CAAAE,GAAA,GAAAF,SAAA,CAAAR,GAAA,CADdf,2BAA2B,CAAAhD,IAAA,CAAAuE,SAAA,CAAAR,GAAA,EAAAQ,SAAA,CAAAC,GAAA;gBAAAD,SAAA,CAAAtD,IAAA;gBAAA,OAAAsD,SAAA,CAAAT,GAAA,CAAA5C,KAAA,CAAAlB,IAAA,CAAAuE,SAAA,CAAAT,GAAA,EAAAS,SAAA,CAAAE,GAAA;cAAA;gBAAxD1M,cAAc,GAAAwM,SAAA,CAAApD,IAAA;gBAAAoD,SAAA,CAAAtD,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGdvK,MAAM,CAACqB,cAAc,CAAC6J,WAAW,CACrC;kBACE,UAAU,EAAEnJ,CAAC,CAACjB,GAAG;kBACjB,oBAAoB,EAAEgC,IAAI,CAAChC;gBAC7B,CAAC,EACD;kBACEsB,IAAI,EAAE;oBACJhB,cAAc,EAAE,CAACW,CAAC,CAACjB,GAAG,EAAAuB,MAAA,CAAAvC,kBAAA,CAAKsB,cAAc,GAAEkB,GAAG,CAAC,UAACC,EAAE;sBAAA,OAAM;wBACtDzB,GAAG,EAAEyB;sBACP,CAAC;oBAAA,CAAC;kBACJ;gBACF,CAAC,EACD;kBAAEC,KAAK,EAAE;gBAAK,CAChB,CAAC;cAAA;gBAAAoL,SAAA,CAAAtD,IAAA;gBAAA;cAAA;cAAA;gBAAA,OAAAsD,SAAA,CAAAlD,IAAA;YAAA;UAAA;UAAA,OAAAiD,SAAA;QAAA,uBAAAhD,OAAA;MAAA;MAEJ,OAAA+C,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEK,oBAAoB;MAAE,SAAAC,SAAgB5J,KAAK,EAAExD,KAAK,EAAEJ,OAAO;QAAA,IAAA6D,EAAA,EAAAQ,UAAA,EAAAC,MAAA,EAAAF,IAAA,EAAAM,UAAA,EAAAC,MAAA,EAAArC,IAAA;QAAA,OAAAyG,mBAAA,CAAAW,KAAA;UAAA,SAAA+D,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA7D,IAAA,GAAA6D,SAAA,CAAA5D,IAAA;cAAA;gBAAA,IAGpDlG,KAAK;kBAAA8J,SAAA,CAAA5D,IAAA;kBAAA;gBAAA;gBAAA,MAAQ,IAAIpJ,KAAK,CAAC,wBAAwB,CAAC;cAAA;gBAAA,IAChDN,KAAK;kBAAAsN,SAAA,CAAA5D,IAAA;kBAAA;gBAAA;gBAAA,MAAQ,IAAIpJ,KAAK,CAAC,wBAAwB,CAAC;cAAA;gBAErDV,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;gBAE1C;gBACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAC1C,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;gBAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;kBACEqE,QAAQ,EAAE;gBACZ,CAAC,EACDjE,OACF,CAAC;gBAAAqE,UAAA,GAAA9E,+BAAA,CAEkBqE,KAAK;cAAA;gBAAA,KAAAU,MAAA,GAAAD,UAAA,IAAA/C,IAAA;kBAAAoM,SAAA,CAAA5D,IAAA;kBAAA;gBAAA;gBAAb1F,IAAI,GAAAE,MAAA,CAAA9C,KAAA;gBACb,IAAIvC,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;kBAC5BP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;gBACf,CAAC,MAAM;kBACLuD,EAAE,GAAGO,IAAI;gBACX;gBAACM,UAAA,GAAAnF,+BAAA,CAEkBa,KAAK;cAAA;gBAAA,KAAAuE,MAAA,GAAAD,UAAA,IAAApD,IAAA;kBAAAoM,SAAA,CAAA5D,IAAA;kBAAA;gBAAA;gBAAbxH,IAAI,GAAAqC,MAAA,CAAAnD,KAAA;gBAAAkM,SAAA,CAAA5D,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACPtK,KAAK,CAACkO,mBAAmB,CAAC9J,EAAE,EAAEvB,IAAI,EAAEtC,OAAO,CAAC;cAAA;gBAAA0N,SAAA,CAAA5D,IAAA;gBAAA;cAAA;gBAAA4D,SAAA,CAAA5D,IAAA;gBAAA;cAAA;cAAA;gBAAA,OAAA4D,SAAA,CAAAxD,IAAA;YAAA;UAAA;UAAA,OAAAuD,SAAA;QAAA,uBAAAtD,OAAA;MAAA;MAGvD,OAAAqD,QAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEI,iBAAiB;MAAE,SAAAC,UAAgBjK,KAAK,EAAExD,KAAK,EAAEJ,OAAO;QAAA,IAAA6D,EAAA,EAAAgB,UAAA,EAAAC,MAAA,EAAAV,IAAA,EAAAQ,QAAA,EAAAO,UAAA,EAAAC,MAAA,EAAA9C,IAAA;QAAA,OAAAyG,mBAAA,CAAAW,KAAA;UAAA,SAAAoE,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAlE,IAAA,GAAAkE,UAAA,CAAAjE,IAAA;cAAA;gBAAA,IAGjDlG,KAAK;kBAAAmK,UAAA,CAAAjE,IAAA;kBAAA;gBAAA;gBAAA,MAAQ,IAAIpJ,KAAK,CAAC,wBAAwB,CAAC;cAAA;gBAAA,IAChDN,KAAK;kBAAA2N,UAAA,CAAAjE,IAAA;kBAAA;gBAAA;gBAAA,MAAQ,IAAIpJ,KAAK,CAAC,wBAAwB,CAAC;cAAA;gBAErDV,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;gBAE1C;gBACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAC1C,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;gBAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;kBACEqE,QAAQ,EAAE,KAAK;kBACfQ,QAAQ,EAAE;gBACZ,CAAC,EACDzE,OACF,CAAC;gBAAA6E,UAAA,GAAAtF,+BAAA,CAEkBqE,KAAK;cAAA;gBAAA,KAAAkB,MAAA,GAAAD,UAAA,IAAAvD,IAAA;kBAAAyM,UAAA,CAAAjE,IAAA;kBAAA;gBAAA;gBAAb1F,IAAI,GAAAU,MAAA,CAAAtD,KAAA;gBACb,IAAIvC,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;kBAC5BP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;gBACf,CAAC,MAAM;kBACLuD,EAAE,GAAGO,IAAI;gBACX;gBACA;gBACMQ,QAAQ,GAAG;kBAAE,UAAU,EAAEf;gBAAG,CAAC;gBACnC,IAAI,CAAC7D,OAAO,CAACyE,QAAQ,EAAE;kBACrBG,QAAQ,CAACZ,KAAK,GAAGhE,OAAO,CAACgE,KAAK;gBAChC;gBAAC+J,UAAA,CAAAjE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEKvK,MAAM,CAACqB,cAAc,CAACmK,WAAW,CAACpG,QAAQ,CAAC;cAAA;gBAAAO,UAAA,GAAA5F,+BAAA,CAG9Ba,KAAK;cAAA;gBAAA,KAAAgF,MAAA,GAAAD,UAAA,IAAA7D,IAAA;kBAAAyM,UAAA,CAAAjE,IAAA;kBAAA;gBAAA;gBAAbxH,IAAI,GAAA8C,MAAA,CAAA5D,KAAA;gBAAAuM,UAAA,CAAAjE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACPtK,KAAK,CAACkO,mBAAmB,CAAC9J,EAAE,EAAEvB,IAAI,EAAEtC,OAAO,CAAC;cAAA;gBAAA+N,UAAA,CAAAjE,IAAA;gBAAA;cAAA;gBAAAiE,UAAA,CAAAjE,IAAA;gBAAA;cAAA;cAAA;gBAAA,OAAAiE,UAAA,CAAA7D,IAAA;YAAA;UAAA;UAAA,OAAA4D,UAAA;QAAA,uBAAA3D,OAAA;MAAA;MAGvD,OAAA0D,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEF,mBAAmB;MAAE,SAAAK,UAAgBjJ,MAAM,EAAEhF,QAAQ,EAAEC,OAAO;QAAA,IAAAsC,IAAA,EAAA2L,kBAAA,EAAAxN,UAAA,EAAAuE,GAAA;QAAA,OAAA+D,mBAAA,CAAAW,KAAA;UAAA,SAAAwE,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAtE,IAAA,GAAAsE,UAAA,CAAArE,IAAA;cAAA;gBAC5DrK,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;gBAC9BN,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;gBAAA,IAE/Be,MAAM;kBAAAoJ,UAAA,CAAArE,IAAA;kBAAA;gBAAA;gBAAA,OAAAqE,UAAA,CAAAlE,MAAA;cAAA;gBAAAkE,UAAA,CAAArE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAIQvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAC1C;kBAAEnK,GAAG,EAAEP;gBAAS,CAAC,EACjB;kBAAEyD,MAAM,EAAE;oBAAEhD,QAAQ,EAAE;kBAAE;gBAAE,CAC5B,CAAC;cAAA;gBAHK8B,IAAI,GAAA6L,UAAA,CAAAnE,IAAA;gBAAA,IAKL1H,IAAI;kBAAA6L,UAAA,CAAArE,IAAA;kBAAA;gBAAA;gBAAA,KACH9J,OAAO,CAACiE,QAAQ;kBAAAkK,UAAA,CAAArE,IAAA;kBAAA;gBAAA;gBAAA,OAAAqE,UAAA,CAAAlE,MAAA,WACX,EAAE;cAAA;gBAAA,MAEH,IAAIvJ,KAAK,CAAC,QAAQ,GAAGX,QAAQ,GAAG,mBAAmB,CAAC;cAAA;gBAAAoO,UAAA,CAAArE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAoB7BvK,MAAM,CAACqB,cAAc,CAAC4J,YAAY,CAAC;kBAClE,UAAU,EAAE1F,MAAM;kBAClB,UAAU,EAAEhF,QAAQ;kBACpBiE,KAAK,EAAEhE,OAAO,CAACgE;gBACjB,CAAC,CAAC;cAAA;gBAJIiK,kBAAkB,GAAAE,UAAA,CAAAnE,IAAA;gBAAA,KAQpBiE,kBAAkB;kBAAAE,UAAA,CAAArE,IAAA;kBAAA;gBAAA;gBAAAqE,UAAA,CAAArE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACdvK,MAAM,CAACqB,cAAc,CAAC6J,WAAW,CAACuD,kBAAkB,CAAC3N,GAAG,EAAE;kBAC9DsB,IAAI,EAAE;oBACJwC,IAAI,EAAE;sBAAE9D,GAAG,EAAEyE;oBAAO,CAAC;oBACrBzC,IAAI,EAAE;sBAAEhC,GAAG,EAAEP;oBAAS,CAAC;oBACvBiE,KAAK,EAAEhE,OAAO,CAACgE;kBACjB;gBACF,CAAC,CAAC;cAAA;gBAAAmK,UAAA,CAAArE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEUvK,MAAM,CAACqB,cAAc,CAAC4J,YAAY,CAACwD,kBAAkB,CAAC3N,GAAG,CAAC;cAAA;gBAAtE0E,GAAG,GAAAmJ,UAAA,CAAAnE,IAAA;gBAAAmE,UAAA,CAAArE,IAAA;gBAAA;cAAA;gBAAAqE,UAAA,CAAArE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEgBvK,MAAM,CAACqB,cAAc,CAAC8J,WAAW,CAAC;kBACnDvG,IAAI,EAAE;oBAAE9D,GAAG,EAAEyE;kBAAO,CAAC;kBACrBzC,IAAI,EAAE;oBAAEhC,GAAG,EAAEP;kBAAS,CAAC;kBACvBiE,KAAK,EAAEhE,OAAO,CAACgE;gBACjB,CAAC,CAAC;cAAA;gBAJFvD,UAAU,GAAA0N,UAAA,CAAAnE,IAAA;cAAA;gBAAA,KAORvJ,UAAU;kBAAA0N,UAAA,CAAArE,IAAA;kBAAA;gBAAA;gBAAAqE,UAAA,CAAAlD,EAAA,GAAAlC,mBAAA;gBAAAoF,UAAA,CAAAjD,EAAA,GACN1L,MAAM,CAACqB,cAAc;gBAAAsN,UAAA,CAAAhD,EAAA,GACzB;kBAAE7K,GAAG,EAAEG;gBAAW,CAAC;gBAAA0N,UAAA,CAAA/C,EAAA,IAIbrL,QAAQ;gBAAAoO,UAAA,CAAA7C,EAAA,GAAAhM,kBAAA;gBAAA6O,UAAA,CAAArE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACEtK,KAAK,CAACoM,2BAA2B,CAACvJ,IAAI,CAAC;cAAA;gBAAA6L,UAAA,CAAA3C,EAAA,GAAA2C,UAAA,CAAAnE,IAAA;gBAAAmE,UAAA,CAAA1C,EAAA,OAAA0C,UAAA,CAAA7C,EAAA,EAAA6C,UAAA,CAAA3C,EAAA;gBAAA2C,UAAA,CAAAzC,EAAA,GAAAyC,UAAA,CAAA/C,EAAA,CAAAvJ,MAAA,CAAAgH,IAAA,CAAAsF,UAAA,CAAA/C,EAAA,EAAA+C,UAAA,CAAA1C,EAAA,EACjD3J,GAAG,CAAC,UAACP,CAAC;kBAAA,OAAM;oBAAEjB,GAAG,EAAEiB;kBAAE,CAAC;gBAAA,CAAC;gBAAA4M,UAAA,CAAAxC,EAAA;kBAHzB/K,cAAc,EAAAuN,UAAA,CAAAzC;gBAAA;gBAAAyC,UAAA,CAAAvC,EAAA;kBADhBhK,IAAI,EAAAuM,UAAA,CAAAxC;gBAAA;gBAAAwC,UAAA,CAAAzB,GAAA,GAAAyB,UAAA,CAAAjD,EAAA,CAHoBR,WAAW,CAAA7B,IAAA,CAAAsF,UAAA,CAAAjD,EAAA,EAAAiD,UAAA,CAAAhD,EAAA,EAAAgD,UAAA,CAAAvC,EAAA;gBAAAuC,UAAA,CAAArE,IAAA;gBAAA,OAAAqE,UAAA,CAAAlD,EAAA,CAAAlB,KAAA,CAAAlB,IAAA,CAAAsF,UAAA,CAAAlD,EAAA,EAAAkD,UAAA,CAAAzB,GAAA;cAAA;gBAAAyB,UAAA,CAAArE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAY3BvK,MAAM,CAACqB,cAAc,CAAC4J,YAAY,CAAC;kBAAEnK,GAAG,EAAEG;gBAAW,CAAC,CAAC;cAAA;gBAAnEuE,GAAG,GAAAmJ,UAAA,CAAAnE,IAAA;cAAA;gBAELhF,GAAG,CAACvE,UAAU,GAAGA,UAAU,EAAC;gBAAA,OAAA0N,UAAA,CAAAlE,MAAA,WAErBjF,GAAG;cAAA;cAAA;gBAAA,OAAAmJ,UAAA,CAAAjE,IAAA;YAAA;UAAA;UAAA,OAAAgE,UAAA;QAAA,uBAAA/D,OAAA;MAAA;MACX,OAAA6D,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE3C,wBAAwB;MAAE,SAAA+C,UAAgB9L,IAAI;QAAA,IAAA2C,WAAA,EAAAS,WAAA,EAAAC,OAAA,EAAA5F,QAAA,EAAA6F,WAAA,EAAAC,OAAA,EAAAP,UAAA;QAAA,OAAAyD,mBAAA,CAAAW,KAAA;UAAA,SAAA2E,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAzE,IAAA,GAAAyE,UAAA,CAAAxE,IAAA;cAAA;gBAAA,IACvCxH,IAAI;kBAAAgM,UAAA,CAAAxE,IAAA;kBAAA;gBAAA;gBAAA,OAAAwE,UAAA,CAAArE,MAAA,WACA,EAAE;cAAA;gBAGLhF,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC5C,IAAI,CAAChC,GAAG,CAAC,CAAC;gBAAAoF,WAAA,GAAAnG,+BAAA,CAEhB0F,WAAW;cAAA;gBAAA,KAAAU,OAAA,GAAAD,WAAA,IAAApE,IAAA;kBAAAgN,UAAA,CAAAxE,IAAA;kBAAA;gBAAA;gBAAvB/J,QAAQ,GAAA4F,OAAA,CAAAnE,KAAA;gBAAA8M,UAAA,CAAArD,EAAA,GAAA1L,+BAAA;gBAAA+O,UAAA,CAAAxE,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACcvK,MAAM,CAACY,KAAK,CACxCc,IAAI,CAAC;kBAAE,cAAc,EAAEnB;gBAAS,CAAC,CAAC,CAClCwL,UAAU,CAAC,CAAC;cAAA;gBAAA+C,UAAA,CAAApD,EAAA,GAAAoD,UAAA,CAAAtE,IAAA;gBAAApE,WAAA,OAAA0I,UAAA,CAAArD,EAAA,EAAAqD,UAAA,CAAApD,EAAA;cAAA;gBAAA,KAAArF,OAAA,GAAAD,WAAA,IAAAtE,IAAA;kBAAAgN,UAAA,CAAAxE,IAAA;kBAAA;gBAAA;gBAFJxE,UAAU,GAAAO,OAAA,CAAArE,KAAA;gBAGnByD,WAAW,CAACM,GAAG,CAACD,UAAU,CAAChF,GAAG,CAAC;cAAA;gBAAAgO,UAAA,CAAAxE,IAAA;gBAAA;cAAA;gBAAAwE,UAAA,CAAAxE,IAAA;gBAAA;cAAA;gBAInC7E,WAAW,CAACO,MAAM,CAAClD,IAAI,CAAChC,GAAG,CAAC;gBAAA,OAAAgO,UAAA,CAAArE,MAAA,WAAA3K,kBAAA,CAEjB2F,WAAW;cAAA;cAAA;gBAAA,OAAAqJ,UAAA,CAAApE,IAAA;YAAA;UAAA;UAAA,OAAAmE,UAAA;QAAA,uBAAAlE,OAAA;MAAA;MACvB,OAAAiE,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEvC,2BAA2B;MAAE,SAAA0C,UAAgBjM,IAAI;QAAA,IAAA1B,cAAA,EAAA6E,WAAA,EAAAM,WAAA,EAAAC,OAAA,EAAAzE,CAAA,EAAAnB,KAAA,EAAA6F,WAAA,EAAAC,OAAA,EAAAnE,EAAA;QAAA,OAAAgH,mBAAA,CAAAW,KAAA;UAAA,SAAA8E,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAA5E,IAAA,GAAA4E,UAAA,CAAA3E,IAAA;cAAA;gBACzClJ,cAAc,GAAG,IAAIsE,GAAG,CAAC,CAAC;gBAC1BO,WAAW,GAAG,IAAIP,GAAG,CAAC,CAAC5C,IAAI,CAAC,CAAC;gBAAAyD,WAAA,GAAAxG,+BAAA,CAEnBkG,WAAW;cAAA;gBAAA,KAAAO,OAAA,GAAAD,WAAA,IAAAzE,IAAA;kBAAAmN,UAAA,CAAA3E,IAAA;kBAAA;gBAAA;gBAAhBvI,CAAC,GAAAyE,OAAA,CAAAxE,KAAA;gBAAAiN,UAAA,CAAA3E,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CACUvK,MAAM,CAACY,KAAK,CAC7Bc,IAAI,CACH;kBAAEZ,GAAG,EAAE;oBAAEa,GAAG,EAAEI,CAAC,CAACf,QAAQ,CAACsB,GAAG,CAAC,UAACP,CAAC;sBAAA,OAAKA,CAAC,CAACjB,GAAG;oBAAA;kBAAE;gBAAE,CAAC,EAC9C;kBAAEkD,MAAM,EAAE;oBAAEhD,QAAQ,EAAE;kBAAE;gBAAE,CAC5B,CAAC,CACA+K,UAAU,CAAC,CAAC;cAAA;gBALTnL,KAAK,GAAAqO,UAAA,CAAAzE,IAAA;gBAOX,KAAA/D,WAAA,GAAA1G,+BAAA,CAAiBa,KAAK,KAAA8F,OAAA,GAAAD,WAAA,IAAA3E,IAAA,GAAE;kBAAbS,EAAE,GAAAmE,OAAA,CAAA1E,KAAA;kBACXZ,cAAc,CAAC2E,GAAG,CAACxD,EAAE,CAACzB,GAAG,CAAC;kBAC1BmF,WAAW,CAACF,GAAG,CAACxD,EAAE,CAAC;gBACrB;cAAC;gBAAA0M,UAAA,CAAA3E,IAAA;gBAAA;cAAA;gBAAA,OAAA2E,UAAA,CAAAxE,MAAA,WAAA3K,kBAAA,CAGQsB,cAAc;cAAA;cAAA;gBAAA,OAAA6N,UAAA,CAAAvE,IAAA;YAAA;UAAA;UAAA,OAAAsE,UAAA;QAAA,uBAAArE,OAAA;MAAA;MAC1B,OAAAoE,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,yBAAyB;MAAE,SAAAC,UAAgB/K,KAAK,EAAExD,KAAK,EAAEJ,OAAO;QAAA,IAAA4O,WAAA,EAAAC,OAAA,EAAAzK,IAAA,EAAA0K,WAAA,EAAAC,OAAA,EAAAzM,IAAA,EAAAuB,EAAA;QAAA,OAAAkF,mBAAA,CAAAW,KAAA;UAAA,SAAAsF,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAApF,IAAA,GAAAoF,UAAA,CAAAnF,IAAA;cAAA;gBAAA,IACzDlG,KAAK;kBAAAqL,UAAA,CAAAnF,IAAA;kBAAA;gBAAA;gBAAA,MAAQ,IAAIpJ,KAAK,CAAC,wBAAwB,CAAC;cAAA;gBAAA,IAChDN,KAAK;kBAAA6O,UAAA,CAAAnF,IAAA;kBAAA;gBAAA;gBAAA,MAAQ,IAAIpJ,KAAK,CAAC,wBAAwB,CAAC;cAAA;gBAErDV,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;gBAE1C;gBACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACgB,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAC1C,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;gBAAA4K,WAAA,GAAArP,+BAAA,CAEjBqE,KAAK;cAAA;gBAAA,KAAAiL,OAAA,GAAAD,WAAA,IAAAtN,IAAA;kBAAA2N,UAAA,CAAAnF,IAAA;kBAAA;gBAAA;gBAAb1F,IAAI,GAAAyK,OAAA,CAAArN,KAAA;gBAAA,IACR4C,IAAI;kBAAA6K,UAAA,CAAAnF,IAAA;kBAAA;gBAAA;gBAAA,OAAAmF,UAAA,CAAAhF,MAAA;cAAA;gBAAA6E,WAAA,GAAAvP,+BAAA,CAEUa,KAAK;cAAA;gBAAA,KAAA2O,OAAA,GAAAD,WAAA,IAAAxN,IAAA;kBAAA2N,UAAA,CAAAnF,IAAA;kBAAA;gBAAA;gBAAbxH,IAAI,GAAAyM,OAAA,CAAAvN,KAAA;gBACTqC,EAAE;gBACN,IAAI5E,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;kBAC5BP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;gBACf,CAAC,MAAM;kBACLuD,EAAE,GAAGO,IAAI;gBACX;gBAAC6K,UAAA,CAAAnF,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEKtK,KAAK,CAACyP,wBAAwB,CAACrL,EAAE,EAAEvB,IAAI,EAAEtC,OAAO,CAAC;cAAA;gBAAAiP,UAAA,CAAAnF,IAAA;gBAAA;cAAA;gBAAAmF,UAAA,CAAAnF,IAAA;gBAAA;cAAA;cAAA;gBAAA,OAAAmF,UAAA,CAAA/E,IAAA;YAAA;UAAA;UAAA,OAAA8E,UAAA;QAAA,uBAAA7E,OAAA;MAAA;MAG5D,OAAAwE,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEO,wBAAwB;MAAE,SAAAC,UAAgBpK,MAAM,EAAEhF,QAAQ,EAAEC,OAAO;QAAA,IAAA4E,QAAA;QAAA,OAAAmE,mBAAA,CAAAW,KAAA;UAAA,SAAA0F,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAxF,IAAA,GAAAwF,UAAA,CAAAvF,IAAA;cAAA;gBACjErK,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;gBAC9BN,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;gBAAA,IAE/Be,MAAM;kBAAAsK,UAAA,CAAAvF,IAAA;kBAAA;gBAAA;gBAAA,OAAAuF,UAAA,CAAApF,MAAA;cAAA;gBAELrF,QAAQ,GAAG;kBACf,UAAU,EAAEG,MAAM;kBAClB,UAAU,EAAEhF;gBACd,CAAC;gBAED,IAAI,CAACC,OAAO,CAACyE,QAAQ,EAAE;kBACrBG,QAAQ,CAACZ,KAAK,GAAGhE,OAAO,CAACgE,KAAK;gBAChC;gBAACqL,UAAA,CAAAvF,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEKvK,MAAM,CAACqB,cAAc,CAACmK,WAAW,CAACpG,QAAQ,CAAC;cAAA;cAAA;gBAAA,OAAAyK,UAAA,CAAAnF,IAAA;YAAA;UAAA;UAAA,OAAAkF,UAAA;QAAA,uBAAAjF,OAAA;MAAA;MAClD,OAAAgF,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,iBAAiB;MAAE,SAAAC,UAAgBnL,IAAI,EAAEhE,KAAK,EAAEJ,OAAO;QAAA,IAAA6D,EAAA,EAAAe,QAAA,EAAAI,GAAA;QAAA,OAAA+D,mBAAA,CAAAW,KAAA;UAAA,SAAA8F,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAA5F,IAAA,GAAA4F,UAAA,CAAA3F,IAAA;cAAA;gBAGrD9J,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;;gBAE1C;gBACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAE1CA,KAAK,GAAGA,KAAK,CAACiG,MAAM,CAAC,UAAC9E,CAAC;kBAAA,OAAKA,CAAC,IAAI,IAAI;gBAAA,EAAC;gBAAA,IAEjCnB,KAAK,CAAC6B,MAAM;kBAAAwN,UAAA,CAAA3F,IAAA;kBAAA;gBAAA;gBAAA,OAAA2F,UAAA,CAAAxF,MAAA,WAAS,KAAK;cAAA;gBAE/BxK,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;gBAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;kBACE6E,QAAQ,EAAE;gBACZ,CAAC,EACDzE,OACF,CAAC;gBAED,IAAIoE,IAAI,IAAInF,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;kBACpCP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;gBACf,CAAC,MAAM;kBACLuD,EAAE,GAAGO,IAAI;gBACX;gBAAC,IAEIP,EAAE;kBAAA4L,UAAA,CAAA3F,IAAA;kBAAA;gBAAA;gBAAA,OAAA2F,UAAA,CAAAxF,MAAA,WAAS,KAAK;cAAA;gBAAA,MACjB,OAAOpG,EAAE,KAAK,QAAQ;kBAAA4L,UAAA,CAAA3F,IAAA;kBAAA;gBAAA;gBAAA,OAAA2F,UAAA,CAAAxF,MAAA,WAAS,KAAK;cAAA;gBAElCrF,QAAQ,GAAG;kBACf,UAAU,EAAEf;gBACd,CAAC;gBAED,IAAI,CAAC7D,OAAO,CAACyE,QAAQ,EAAE;kBACrBG,QAAQ,CAACZ,KAAK,GAAG;oBAAE7C,GAAG,EAAE,CAACnB,OAAO,CAACgE,KAAK,EAAE,IAAI;kBAAE,CAAC;gBACjD;gBAACyL,UAAA,CAAA3F,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEiBV,SAAS,CAACjJ,KAAK;kBAAE,SAAAsP,UAAO3P,QAAQ;oBAAA,IAAA4P,GAAA;oBAAA,OAAA5G,mBAAA,CAAAW,KAAA;sBAAA,SAAAkG,WAAAC,UAAA;wBAAA,kBAAAA,UAAA,CAAAhG,IAAA,GAAAgG,UAAA,CAAA/F,IAAA;0BAAA;4BAChDlF,QAAQ,CAAC,oBAAoB,CAAC,GAAG7E,QAAQ;4BAAA8P,UAAA,CAAA/F,IAAA;4BAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEhCvK,MAAM,CAACqB,cAAc,CAACiP,cAAc,CAAClL,QAAQ,EAAE;8BAAE2B,KAAK,EAAE;4BAAE,CAAC,CAAC;0BAAA;4BAAAsJ,UAAA,CAAA5E,EAAA,GAAA4E,UAAA,CAAA7F,IAAA;4BAD/D2F,GAAG,GAAAE,UAAA,CAAA5E,EAAA,GACgE,CAAC;4BAAA,OAAA4E,UAAA,CAAA5F,MAAA,WACnE0F,GAAG;0BAAA;0BAAA;4BAAA,OAAAE,UAAA,CAAA3F,IAAA;wBAAA;sBAAA;sBAAA,OAAA0F,UAAA;oBAAA,uBAAAzF,OAAA;kBAAA;kBACX,OAAAuF,SAAA;gBAAA,IAAC;cAAA;gBALI1K,GAAG,GAAAyK,UAAA,CAAAzF,IAAA;gBAAA,OAAAyF,UAAA,CAAAxF,MAAA,WAOFjF,GAAG;cAAA;cAAA;gBAAA,OAAAyK,UAAA,CAAAvF,IAAA;YAAA;UAAA;UAAA,OAAAsF,UAAA;QAAA,uBAAArF,OAAA;MAAA;MACX,OAAAoF,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEQ,oBAAoB;MAAE,SAAAC,UAAgB5L,IAAI,EAAEpE,OAAO;QAAA,IAAA6D,EAAA,EAAAe,QAAA,EAAAyB,MAAA,EAAAjG,KAAA;QAAA,OAAA2I,mBAAA,CAAAW,KAAA;UAAA,SAAAuG,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAArG,IAAA,GAAAqG,UAAA,CAAApG,IAAA;cAAA;gBAGjD9J,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;gBAE1CP,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;gBAEpChE,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;kBACtB6G,WAAW,EAAE,KAAK;kBAClBC,YAAY,EAAE,KAAK;kBACnBjC,QAAQ,EAAE,KAAK;kBACfkC,UAAU,EAAE;gBACd,CAAC,EAAE3G,OAAO,CAAC;gBAEX,IAAIoE,IAAI,IAAInF,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;kBACpCP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;gBACf,CAAC,MAAM;kBACLuD,EAAE,GAAGO,IAAI;gBACX;gBAAC,IAEIP,EAAE;kBAAAqM,UAAA,CAAApG,IAAA;kBAAA;gBAAA;gBAAA,OAAAoG,UAAA,CAAAjG,MAAA,WAAS,EAAE;cAAA;gBAEZrF,QAAQ,GAAG;kBACf,UAAU,EAAEf;gBACd,CAAC;gBAEKwC,MAAM,GAAG;kBACb7C,MAAM,EAAE;oBAAE,oBAAoB,EAAE;kBAAE;gBACpC,CAAC;gBAED,IAAI,CAACxD,OAAO,CAACyE,QAAQ,EAAE;kBACrBG,QAAQ,CAACZ,KAAK,GAAG;oBAAE7C,GAAG,EAAE,CAACnB,OAAO,CAACgE,KAAK;kBAAE,CAAC;kBAEzC,IAAI,CAAChE,OAAO,CAAC2G,UAAU,EAAE;oBACvB/B,QAAQ,CAACZ,KAAK,CAAC7C,GAAG,CAACyF,IAAI,CAAC,IAAI,CAAC;kBAC/B;gBACF;gBAEA,IAAI5G,OAAO,CAAC0G,YAAY,EAAE;kBACxB,OAAOL,MAAM,CAAC7C,MAAM,CAAC,oBAAoB,CAAC;kBAC1C6C,MAAM,CAAC7C,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;gBAC/B;gBAEA,IAAIxD,OAAO,CAACyG,WAAW,EAAE;kBACvB,OAAOJ,MAAM,CAAC7C,MAAM;gBACtB;gBAAC0M,UAAA,CAAApG,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEmBvK,MAAM,CAACqB,cAAc,CAACK,IAAI,CAAC0D,QAAQ,EAAEyB,MAAM,CAAC,CAACkF,UAAU,CAAC,CAAC;cAAA;gBAAvEnL,KAAK,GAAA8P,UAAA,CAAAlG,IAAA;gBAAA,KAEPhK,OAAO,CAACyG,WAAW;kBAAAyJ,UAAA,CAAApG,IAAA;kBAAA;gBAAA;gBAAA,OAAAoG,UAAA,CAAAjG,MAAA,WACd7J,KAAK;cAAA;gBAAA,OAAA8P,UAAA,CAAAjG,MAAA,WAAA3K,kBAAA,CAIT,IAAI4F,GAAG,CACR9E,KAAK,CAACyG,MAAM,CAAC,UAACC,GAAG,EAAEC,OAAO,EAAK;kBAC7B,IAAIA,OAAO,CAACnG,cAAc,EAAE;oBAC1B,OAAOkG,GAAG,CAACjF,MAAM,CAACkF,OAAO,CAACnG,cAAc,CAACkB,GAAG,CAAC,UAACP,CAAC;sBAAA,OAAKA,CAAC,CAACjB,GAAG;oBAAA,EAAC,CAAC;kBAC7D,CAAC,MAAM,IAAIyG,OAAO,CAACzE,IAAI,EAAE;oBACvBwE,GAAG,CAACF,IAAI,CAACG,OAAO,CAACzE,IAAI,CAAChC,GAAG,CAAC;kBAC5B;kBACA,OAAOwG,GAAG;gBACZ,CAAC,EAAE,EAAE,CACP,CAAC;cAAA;cAAA;gBAAA,OAAAoJ,UAAA,CAAAhG,IAAA;YAAA;UAAA;UAAA,OAAA+F,UAAA;QAAA,uBAAA9F,OAAA;MAAA;MAEJ,OAAA6F,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEhJ,WAAW,EAAE,SAAAA,CAAUC,YAAY,EAAE;MACnCA,YAAY,GAAGA,YAAY,IAAI;QAAEC,IAAI,EAAE;UAAE5G,GAAG,EAAE;QAAE;MAAE,CAAC;MAEnD,OAAOd,MAAM,CAACY,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE+F,YAAY,CAAC;IAC5C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEkJ,mBAAmB;MAAE,SAAAC,UAAgBhQ,KAAK,EAAEJ,OAAO,EAAEiH,YAAY;QAAA,IAAAG,GAAA;QAAA,OAAA2B,mBAAA,CAAAW,KAAA;UAAA,SAAA2G,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAzG,IAAA,GAAAyG,UAAA,CAAAxG,IAAA;cAAA;gBAAAwG,UAAA,CAAAxG,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEvDtK,KAAK,CAAC4H,yBAAyB,CAACjH,KAAK,EAAEJ,OAAO,CAAC,CAACuL,UAAU,CAAC,CAAC;cAAA;gBAD9DnE,GAAG,GAAAkJ,UAAA,CAAAtG,IAAA,CAEPlI,GAAG,CAAC,UAACwF,CAAC;kBAAA,OAAKA,CAAC,CAAClD,IAAI,CAAC9D,GAAG;gBAAA;gBAAA,OAAAgQ,UAAA,CAAArG,MAAA,WAEhBzK,MAAM,CAACoE,KAAK,CAAC1C,IAAI,CACtB;kBAAEZ,GAAG,EAAE;oBAAEa,GAAG,EAAEiG;kBAAI;gBAAE,CAAC,EACpBpH,OAAO,IAAIA,OAAO,CAACiH,YAAY,IAAKA,YAAY,IAAI,CAAC,CACxD,CAAC;cAAA;cAAA;gBAAA,OAAAqJ,UAAA,CAAApG,IAAA;YAAA;UAAA;UAAA,OAAAmG,UAAA;QAAA,uBAAAlG,OAAA;MAAA;MACF,OAAAiG,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEE/I,yBAAyB,EAAE,SAAAA,CAAUjH,KAAK,EAAEJ,OAAO,EAAE;MACnDA,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACE6E,QAAQ,EAAE,KAAK;QACfwC,YAAY,EAAE,CAAC;MACjB,CAAC,EACDjH,OACF,CAAC;MAED,OAAOP,KAAK,CAAC8H,qBAAqB,CAACnH,KAAK,EAAEJ,OAAO,EAAEA,OAAO,CAACiH,YAAY,CAAC;IAC1E,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEM,qBAAqB,EAAE,SAAAA,CAAUnH,KAAK,EAAEJ,OAAO,EAAEqG,MAAM,EAAE;MACvDrG,OAAO,GAAGP,KAAK,CAACqE,iBAAiB,CAAC9D,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACE6E,QAAQ,EAAE,KAAK;QACfkC,UAAU,EAAE;MACd,CAAC,EACD3G,OACF,CAAC;;MAED;MACA,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAACsE,eAAe,CAAC/D,OAAO,CAACgE,KAAK,CAAC;MAEpCqC,MAAM,GAAG1G,MAAM,CAACC,MAAM,CACpB;QACE4D,MAAM,EAAE;UAAE,UAAU,EAAE;QAAE;MAC1B,CAAC,EACD6C,MACF,CAAC;MAED,IAAMzB,QAAQ,GAAG;QACf,oBAAoB,EAAE;UAAEzD,GAAG,EAAEf;QAAM;MACrC,CAAC;MAED,IAAI,CAACJ,OAAO,CAACyE,QAAQ,EAAE;QACrBG,QAAQ,CAACZ,KAAK,GAAG;UAAE7C,GAAG,EAAE,CAACnB,OAAO,CAACgE,KAAK;QAAE,CAAC;QAEzC,IAAI,CAAChE,OAAO,CAAC2G,UAAU,EAAE;UACvB/B,QAAQ,CAACZ,KAAK,CAAC7C,GAAG,CAACyF,IAAI,CAAC,IAAI,CAAC;QAC/B;MACF;MAEA,OAAOpH,MAAM,CAACqB,cAAc,CAACK,IAAI,CAAC0D,QAAQ,EAAEyB,MAAM,CAAC;IACrD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEkK,qBAAqB;MAAE,SAAAC,UAAA;QAAA,IAAA/I,MAAA;QAAA,IAAAgJ,OAAA,GAAA3I,SAAA;QAAA,OAAAiB,mBAAA,CAAAW,KAAA;UAAA,SAAAgH,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAA9G,IAAA,GAAA8G,UAAA,CAAA7G,IAAA;cAAA;gBACrB,IAAI,CAACpK,kCAAkC,EAAE;kBACvCA,kCAAkC,GAAG,IAAI;kBACzCgI,OAAO,IACLA,OAAO,CAACC,IAAI,CACV,qEACF,CAAC;gBACL;gBAACgJ,UAAA,CAAA7G,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAEY,CAAAtC,MAAA,GAAAhI,KAAK,EAACmI,gBAAgB,CAAAC,KAAA,CAAAJ,MAAA,EAAAgJ,OAAQ,CAAC;cAAA;gBAAA,OAAAE,UAAA,CAAA1G,MAAA,WAAA0G,UAAA,CAAA3G,IAAA;cAAA;cAAA;gBAAA,OAAA2G,UAAA,CAAAzG,IAAA;YAAA;UAAA;UAAA,OAAAwG,UAAA;QAAA,uBAAAvG,OAAA;MAAA;MAC7C,OAAAqG,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEI,qBAAqB;MAAE,SAAAC,UAAgBzM,IAAI,EAAEhE,KAAK;QAAA,IAAAyD,EAAA,EAAAe,QAAA,EAAAmD,MAAA;QAAA,OAAAgB,mBAAA,CAAAW,KAAA;UAAA,SAAAoH,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAlH,IAAA,GAAAkH,UAAA,CAAAjH,IAAA;cAAA;gBAGhD,IAAI1J,KAAK,IAAI,CAACuC,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;gBAEnD,IAAIgE,IAAI,IAAInF,OAAA,CAAOmF,IAAI,MAAK,QAAQ,EAAE;kBACpCP,EAAE,GAAGO,IAAI,CAAC9D,GAAG;gBACf,CAAC,MAAM;kBACLuD,EAAE,GAAGO,IAAI;gBACX;gBAAC,IAEIP,EAAE;kBAAAkN,UAAA,CAAAjH,IAAA;kBAAA;gBAAA;gBAAA,OAAAiH,UAAA,CAAA9G,MAAA,WAAS,EAAE;cAAA;gBAEZrF,QAAQ,GAAG;kBACf,UAAU,EAAEf,EAAE;kBACdG,KAAK,EAAE;oBAAEf,GAAG,EAAE;kBAAK;gBACrB,CAAC;gBAED,IAAI7C,KAAK,EAAE;kBACTwE,QAAQ,CAAC,oBAAoB,CAAC,GAAG;oBAAEzD,GAAG,EAAEf;kBAAM,CAAC;gBACjD;gBAAC2Q,UAAA,CAAAjH,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGOvK,MAAM,CAACqB,cAAc,CACxBK,IAAI,CAAC0D,QAAQ,EAAE;kBAAEpB,MAAM,EAAE;oBAAEQ,KAAK,EAAE;kBAAE;gBAAE,CAAC,CAAC,CACxCuH,UAAU,CAAC,CAAC;cAAA;gBAHXxD,MAAM,GAAAgJ,UAAA,CAAA/G,IAAA,CAIVlI,GAAG,CAAC,UAACkG,GAAG;kBAAA,OAAKA,GAAG,CAAChE,KAAK;gBAAA;gBAAA,OAAA+M,UAAA,CAAA9G,MAAA,WAAA3K,kBAAA,CAEb,IAAI4F,GAAG,CAAC6C,MAAM,CAAC;cAAA;cAAA;gBAAA,OAAAgJ,UAAA,CAAA7G,IAAA;YAAA;UAAA;UAAA,OAAA4G,UAAA;QAAA,uBAAA3G,OAAA;MAAA;MAC3B,OAAA0G,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,gBAAgB;MAAE,SAAAC,UAAgB9O,OAAO,EAAEC,OAAO;QAAA,IAAAC,KAAA;QAAA,OAAA0G,mBAAA,CAAAW,KAAA;UAAA,SAAAwH,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAtH,IAAA,GAAAsH,UAAA,CAAArH,IAAA;cAAA;gBAGhDrK,KAAK,CAACsE,eAAe,CAAC5B,OAAO,CAAC;gBAC9B1C,KAAK,CAACsE,eAAe,CAAC3B,OAAO,CAAC;gBAAA,MAE1BD,OAAO,KAAKC,OAAO;kBAAA+O,UAAA,CAAArH,IAAA;kBAAA;gBAAA;gBAAA,OAAAqH,UAAA,CAAAlH,MAAA;cAAA;gBAAAkH,UAAA,CAAArH,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGPvK,MAAM,CAACqB,cAAc,CAAC6J,WAAW,CAC7C;kBACE1G,KAAK,EAAE7B;gBACT,CAAC,EACD;kBACEP,IAAI,EAAE;oBACJoC,KAAK,EAAE5B;kBACT;gBACF,CAAC,EACD;kBAAEJ,KAAK,EAAE;gBAAK,CAChB,CAAC;cAAA;gBAVDK,KAAK,GAAA8O,UAAA,CAAAnH,IAAA;cAAA;gBAAA,IAWE3H,KAAK,GAAG,CAAC;kBAAA8O,UAAA,CAAArH,IAAA;kBAAA;gBAAA;cAAA;cAAA;gBAAA,OAAAqH,UAAA,CAAAjH,IAAA;YAAA;UAAA;UAAA,OAAAgH,UAAA;QAAA,uBAAA/G,OAAA;MAAA;MACnB,OAAA8G,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,gBAAgB;MAAE,SAAAC,UAAgBlJ,IAAI;QAAA,OAAAY,mBAAA,CAAAW,KAAA;UAAA,SAAA4H,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAA1H,IAAA,GAAA0H,UAAA,CAAAzH,IAAA;cAAA;gBACpCrK,KAAK,CAACsE,eAAe,CAACoE,IAAI,CAAC;gBAAAoJ,UAAA,CAAAzH,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAErBvK,MAAM,CAACqB,cAAc,CAACmK,WAAW,CAAC;kBAAEhH,KAAK,EAAEmE;gBAAK,CAAC,CAAC;cAAA;cAAA;gBAAA,OAAAoJ,UAAA,CAAArH,IAAA;YAAA;UAAA;UAAA,OAAAoH,UAAA;QAAA,uBAAAnH,OAAA;MAAA;MACzD,OAAAkH,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEpR,cAAc,EAAE,SAAAA,CAAUF,QAAQ,EAAE;MAClC,IACE,CAACA,QAAQ,IACT,OAAOA,QAAQ,KAAK,QAAQ,IAC5BA,QAAQ,CAACqI,IAAI,CAAC,CAAC,KAAKrI,QAAQ,EAC5B;QACA,MAAM,IAAIW,KAAK,yBAAuBX,QAAQ,OAAI,CAAC;MACrD;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEyR,eAAe;MAAE,SAAAC,UAAgBnJ,cAAc,EAAEC,aAAa;QAAA,IAAAC,YAAA,EAAAzI,QAAA,EAAAuC,IAAA;QAAA,OAAAyG,mBAAA,CAAAW,KAAA;UAAA,SAAAgI,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAA9H,IAAA,GAAA8H,UAAA,CAAA7H,IAAA;cAAA;gBAAA,MACxDxB,cAAc,KAAKC,aAAa;kBAAAoJ,UAAA,CAAA7H,IAAA;kBAAA;gBAAA;gBAAA,OAAA6H,UAAA,CAAA1H,MAAA,WAC3B,IAAI;cAAA;gBAAA,MAGT3B,cAAc,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI;kBAAAoJ,UAAA,CAAA7H,IAAA;kBAAA;gBAAA;gBAAA,OAAA6H,UAAA,CAAA1H,MAAA,WAC1C,KAAK;cAAA;gBAGdxK,KAAK,CAACQ,cAAc,CAACqI,cAAc,CAAC;gBACpC7I,KAAK,CAACQ,cAAc,CAACsI,aAAa,CAAC;gBAE/BC,YAAY,GAAG,CAACF,cAAc,CAAC;cAAA;gBAAA,MAC5BE,YAAY,CAACvG,MAAM,KAAK,CAAC;kBAAA0P,UAAA,CAAA7H,IAAA;kBAAA;gBAAA;gBACxB/J,QAAQ,GAAGyI,YAAY,CAACC,GAAG,CAAC,CAAC;gBAAA,MAE/B1I,QAAQ,KAAKwI,aAAa;kBAAAoJ,UAAA,CAAA7H,IAAA;kBAAA;gBAAA;gBAAA,OAAA6H,UAAA,CAAA1H,MAAA,WACrB,IAAI;cAAA;gBAAA0H,UAAA,CAAA7H,IAAA;gBAAA,OAAAf,mBAAA,CAAAgB,KAAA,CAGMvK,MAAM,CAACY,KAAK,CAACqK,YAAY,CAAC;kBAAEnK,GAAG,EAAEP;gBAAS,CAAC,CAAC;cAAA;gBAAzDuC,IAAI,GAAAqP,UAAA,CAAA3H,IAAA;gBAAA,IAGL1H,IAAI;kBAAAqP,UAAA,CAAA7H,IAAA;kBAAA;gBAAA;gBAAA,OAAA6H,UAAA,CAAA1H,MAAA;cAAA;gBAETzB,YAAY,GAAGA,YAAY,CAAC3G,MAAM,CAACS,IAAI,CAAC9B,QAAQ,CAACsB,GAAG,CAAC,UAACP,CAAC;kBAAA,OAAKA,CAAC,CAACjB,GAAG;gBAAA,EAAC,CAAC;gBAAAqR,UAAA,CAAA7H,IAAA;gBAAA;cAAA;gBAAA,OAAA6H,UAAA,CAAA1H,MAAA,WAG9D,KAAK;cAAA;cAAA;gBAAA,OAAA0H,UAAA,CAAAzH,IAAA;YAAA;UAAA;UAAA,OAAAwH,UAAA;QAAA,uBAAAvH,OAAA;MAAA;MACb,OAAAsH,SAAA;IAAA;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE3N,iBAAiB,EAAE,SAAAA,CAAU9D,OAAO,EAAE;MACpCA,OAAO,GAAGA,OAAO,KAAK0I,SAAS,GAAG,CAAC,CAAC,GAAG1I,OAAO;;MAE9C;MACA;MACA,IAAIA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAClFA,OAAO,GAAG;UAAEgE,KAAK,EAAEhE;QAAQ,CAAC;MAC9B;MAEAA,OAAO,CAACgE,KAAK,GAAGvE,KAAK,CAACkJ,mBAAmB,CAAC3I,OAAO,CAACgE,KAAK,CAAC;MAExD,OAAOhE,OAAO;IAChB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE2I,mBAAmB,EAAE,SAAAA,CAAUC,SAAS,EAAE;MACxC;MACA,IAAIA,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOA,SAAS;MAClB;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE7E,eAAe,EAAE,SAAAA,CAAU6E,SAAS,EAAE;MACpC,IAAIA,SAAS,KAAK,IAAI,EAAE;MAExB,IACE,CAACA,SAAS,IACV,OAAOA,SAAS,KAAK,QAAQ,IAC7BA,SAAS,CAACR,IAAI,CAAC,CAAC,KAAKQ,SAAS,EAC9B;QACA,MAAM,IAAIlI,KAAK,0BAAwBkI,SAAS,OAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;AAAA,EAAAC,IAAA,OAAAC,MAAA,E;;;;;;;;;;;AC/yCF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEArJ,KAAK,CAACmS,KAAK,GAAG,KAAK;AAEnB,IAAI;EACF,IAAIC,YAAY,EAAE;IAChB,IAAMC,IAAI,GAAGD,YAAY,CAACE,OAAO,CAAC,aAAa,CAAC;IAEhD,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/BrS,KAAK,CAACmS,KAAK,GAAG,CAAC,CAACE,IAAI;IACtB;EACF;AACF,CAAC,CAAC,OAAOE,EAAE,EAAE;EACX;EACA;AAAA,C;;;;;;;;;;;;ECvBF,IAAIC,cAAc;EAAC/S,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAAC4S,cAAc,GAAC5S,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAjH;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAI,KAAK,CAACyS,UAAU,GAAG;IAEjB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,QAAQ,EAAE,SAAAA,CAAU7P,IAAI,EAAE0B,KAAK,EAAE;MAC/B,IAAMI,IAAI,GAAG5E,MAAM,CAAC4E,IAAI,CAAC,CAAC;MAC1B,IAAMgO,KAAK,GAAG,CAAC9P,IAAI,IAAI,EAAE,EAAE+P,OAAO,CAAC,GAAG,CAAC;MACvC,IAAIjS,KAAK;MAET,IAAI,CAACgE,IAAI,EAAE,OAAO,KAAK;MACvB,IAAI,CAACkO,KAAK,CAACC,IAAI,CAACjQ,IAAI,EAAEkQ,MAAM,CAAC,EAAE,OAAO,KAAK;MAE3C,IAAIJ,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBhS,KAAK,GAAGkC,IAAI,CAACmQ,KAAK,CAAC,GAAG,CAAC,CAAC5L,MAAM,CAAC,UAAU6L,IAAI,EAAEnR,CAAC,EAAE;UAChD,IAAI,CAACA,CAAC,EAAE;YACN,OAAOmR,IAAI;UACb;UACAA,IAAI,CAAC9L,IAAI,CAACrF,CAAC,CAAC;UACZ,OAAOmR,IAAI;QACb,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,MAAM;QACLtS,KAAK,GAAG,CAACkC,IAAI,CAAC;MAChB;MAEA,IAAIgQ,KAAK,CAACC,IAAI,CAACvO,KAAK,EAAEwO,MAAM,CAAC,EAAE;QAC7B,OAAO/S,KAAK,CAAC2G,YAAY,CAAChC,IAAI,EAAEhE,KAAK,EAAE4D,KAAK,CAAC;MAC/C;MAEA,OAAOvE,KAAK,CAAC2G,YAAY,CAAChC,IAAI,EAAEhE,KAAK,CAAC;IACxC;EACF,CAAC;;EAED;EACA;EACA;;EAEA,IAAIX,KAAK,CAACmS,KAAK,IAAIlK,OAAO,CAACkK,KAAK,EAAE;IAChClK,OAAO,CAACkK,KAAK,CAAC,uBAAuB,EAAEnS,KAAK,CAACmS,KAAK,CAAC;EACrD;EAEA,IAAI,OAAOe,OAAO,CAACC,KAAK,KAAK,WAAW,IACpC,OAAOD,OAAO,CAACC,KAAK,CAACC,KAAK,KAAK,WAAW,IAC1C,OAAOF,OAAO,CAACC,KAAK,CAACC,KAAK,CAACC,cAAc,KAAK,UAAU,EAAE;IAC5DnT,MAAM,CAACoT,OAAO,CAACtT,KAAK,CAACyS,UAAU,CAAC,CAAC7M,OAAO,CAAC,UAAA2N,IAAA,EAAkB;MAAA,IAAAC,KAAA,GAAAhB,cAAA,CAAAe,IAAA;QAAhB7K,IAAI,GAAA8K,KAAA;QAAEC,IAAI,GAAAD,KAAA;MACnD,IAAIxT,KAAK,CAACmS,KAAK,IAAIlK,OAAO,CAACkK,KAAK,EAAE;QAChClK,OAAO,CAACkK,KAAK,CAAC,qCAAqC,GAAGzJ,IAAI,GAAG,IAAI,CAAC;MACpE;MACAwK,OAAO,CAACC,KAAK,CAACC,KAAK,CAACC,cAAc,CAAC3K,IAAI,EAAE+K,IAAI,CAAC;IAChD,CAAC,CAAC;EACJ;AAAC,EAAArK,IAAA,OAAAC,MAAA,E", "file": "/packages/alanning_roles.js", "sourcesContent": ["/* global Roles */\nimport { Meteor } from 'meteor/meteor'\n\n/**\n * Provides functions related to user authorization. Compatible with built-in Meteor accounts packages.\n *\n * Roles are accessible through `Meteor.roles` collection and documents consist of:\n *  - `_id`: role name\n *  - `children`: list of subdocuments:\n *    - `_id`\n *\n * Children list elements are subdocuments so that they can be easier extended in the future or by plugins.\n *\n * Roles can have multiple parents and can be children (subroles) of multiple roles.\n *\n * Example: `{_id: 'admin', children: [{_id: 'editor'}]}`\n *\n * The assignment of a role to a user is stored in a collection, accessible through `RoleAssignmentCollection`.\n * It's documents consist of\n *  - `_id`: Internal MongoDB id\n *  - `role`: A role object which got assigned. Usually only contains the `_id` property\n *  - `user`: A user object, usually only contains the `_id` property\n *  - `scope`: scope name\n *  - `inheritedRoles`: A list of all the roles objects inherited by the assigned role.\n *\n * @module Roles\n */\n\n/**\n * @class Roles\n */\nif (typeof Roles === 'undefined') {\n  Roles = {} // eslint-disable-line no-global-assign\n}\n\nlet getGroupsForUserDeprecationWarning = false\n\nObject.assign(Roles, {\n\n  /**\n   * Used as a global group (now scope) name. Not used anymore.\n   *\n   * @property GLOBAL_GROUP\n   * @static\n   * @deprecated\n   */\n  GLOBAL_GROUP: null,\n\n  /**\n   * Create a new role.\n   *\n   * @method createRole\n   * @param {String} roleName Name of role.\n   * @param {Object} [options] Options:\n   *   - `unlessExists`: if `true`, exception will not be thrown in the role already exists\n   * @return {String} ID of the new role or null.\n   * @static\n   */\n  createRole: function (roleName, options) {\n    Roles._checkRoleName(roleName)\n\n    options = Object.assign({\n      unlessExists: false\n    }, options)\n\n    const result = Meteor.roles.upsert({ _id: roleName }, { $setOnInsert: { children: [] } })\n\n    if (!result.insertedId) {\n      if (options.unlessExists) return null\n      throw new Error('Role \\'' + roleName + '\\' already exists.')\n    }\n\n    return result.insertedId\n  },\n\n  /**\n   * Delete an existing role.\n   *\n   * If the role is set for any user, it is automatically unset.\n   *\n   * @method deleteRole\n   * @param {String} roleName Name of role.\n   * @static\n   */\n  deleteRole: function (roleName) {\n    let roles\n    let inheritedRoles\n\n    Roles._checkRoleName(roleName)\n\n    // Remove all assignments\n    Meteor.roleAssignment.remove({\n      'role._id': roleName\n    })\n\n    do {\n      // For all roles who have it as a dependency ...\n      roles = Roles._getParentRoleNames(Meteor.roles.findOne({ _id: roleName }))\n\n      for (const r of Meteor.roles.find({ _id: { $in: roles } }).fetch()) {\n        Meteor.roles.update({\n          _id: r._id\n        }, {\n          $pull: {\n            children: {\n              _id: roleName\n            }\n          }\n        })\n\n        inheritedRoles = Roles._getInheritedRoleNames(Meteor.roles.findOne({ _id: r._id }))\n        Meteor.roleAssignment.update({\n          'role._id': r._id\n        }, {\n          $set: {\n            inheritedRoles: [r._id, ...inheritedRoles].map(r2 => ({ _id: r2 }))\n          }\n        }, { multi: true })\n      }\n    } while (roles.length > 0)\n\n    // And finally remove the role itself\n    Meteor.roles.remove({ _id: roleName })\n  },\n\n  /**\n   * Rename an existing role.\n   *\n   * @method renameRole\n   * @param {String} oldName Old name of a role.\n   * @param {String} newName New name of a role.\n   * @static\n   */\n  renameRole: function (oldName, newName) {\n    let count\n\n    Roles._checkRoleName(oldName)\n    Roles._checkRoleName(newName)\n\n    if (oldName === newName) return\n\n    const role = Meteor.roles.findOne({ _id: oldName })\n\n    if (!role) {\n      throw new Error('Role \\'' + oldName + '\\' does not exist.')\n    }\n\n    role._id = newName\n\n    Meteor.roles.insert(role)\n\n    do {\n      count = Meteor.roleAssignment.update({\n        'role._id': oldName\n      }, {\n        $set: {\n          'role._id': newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n\n    do {\n      count = Meteor.roleAssignment.update({\n        'inheritedRoles._id': oldName\n      }, {\n        $set: {\n          'inheritedRoles.$._id': newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n\n    do {\n      count = Meteor.roles.update({\n        'children._id': oldName\n      }, {\n        $set: {\n          'children.$._id': newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n\n    Meteor.roles.remove({ _id: oldName })\n  },\n\n  /**\n   * Add role parent to roles.\n   *\n   * Previous parents are kept (role can have multiple parents). For users which have the\n   * parent role set, new subroles are added automatically.\n   *\n   * @method addRolesToParent\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @static\n   */\n  addRolesToParent: function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      Roles._addRoleToParent(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _addRoleToParent\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @private\n   * @static\n   */\n  _addRoleToParent: function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // query to get role's children\n    const role = Meteor.roles.findOne({ _id: roleName })\n\n    if (!role) {\n      throw new Error('Role \\'' + roleName + '\\' does not exist.')\n    }\n\n    // detect cycles\n    if (Roles._getInheritedRoleNames(role).includes(parentName)) {\n      throw new Error('Roles \\'' + roleName + '\\' and \\'' + parentName + '\\' would form a cycle.')\n    }\n\n    const count = Meteor.roles.update({\n      _id: parentName,\n      'children._id': {\n        $ne: role._id\n      }\n    }, {\n      $push: {\n        children: {\n          _id: role._id\n        }\n      }\n    })\n\n    // if there was no change, parent role might not exist, or role is\n    // already a subrole; in any case we do not have anything more to do\n    if (!count) return\n\n    Meteor.roleAssignment.update({\n      'inheritedRoles._id': parentName\n    }, {\n      $push: {\n        inheritedRoles: { $each: [role._id, ...Roles._getInheritedRoleNames(role)].map(r => ({ _id: r })) }\n      }\n    }, { multi: true })\n  },\n\n  /**\n   * Remove role parent from roles.\n   *\n   * Other parents are kept (role can have multiple parents). For users which have the\n   * parent role set, removed subrole is removed automatically.\n   *\n   * @method removeRolesFromParent\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @static\n   */\n  removeRolesFromParent: function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      Roles._removeRoleFromParent(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _removeRoleFromParent\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @private\n   * @static\n   */\n  _removeRoleFromParent: function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // check for role existence\n    // this would not really be needed, but we are trying to match addRolesToParent\n    const role = Meteor.roles.findOne({ _id: roleName }, { fields: { _id: 1 } })\n\n    if (!role) {\n      throw new Error('Role \\'' + roleName + '\\' does not exist.')\n    }\n\n    const count = Meteor.roles.update({\n      _id: parentName\n    }, {\n      $pull: {\n        children: {\n          _id: role._id\n        }\n      }\n    })\n\n    // if there was no change, parent role might not exist, or role was\n    // already not a subrole; in any case we do not have anything more to do\n    if (!count) return\n\n    // For all roles who have had it as a dependency ...\n    const roles = [...Roles._getParentRoleNames(Meteor.roles.findOne({ _id: parentName })), parentName]\n\n    for (const r of Meteor.roles.find({ _id: { $in: roles } }).fetch()) {\n      const inheritedRoles = Roles._getInheritedRoleNames(Meteor.roles.findOne({ _id: r._id }))\n      Meteor.roleAssignment.update({\n        'role._id': r._id,\n        'inheritedRoles._id': role._id\n      }, {\n        $set: {\n          inheritedRoles: [r._id, ...inheritedRoles].map(r2 => ({ _id: r2 }))\n        }\n      }, { multi: true })\n    }\n  },\n\n  /**\n   * Add users to roles.\n   *\n   * Adds roles to existing roles for each user.\n   *\n   * @example\n   *     Roles.addUsersToRoles(userId, 'admin')\n   *     Roles.addUsersToRoles(userId, ['view-secrets'], 'example.com')\n   *     Roles.addUsersToRoles([user1, user2], ['user','editor'])\n   *     Roles.addUsersToRoles([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method addUsersToRoles\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  addUsersToRoles: function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error('Missing \\'users\\' param.')\n    if (!roles) throw new Error('Missing \\'roles\\' param.')\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      ifExists: false\n    }, options)\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n\n      for (const role of roles) {\n        Roles._addUserToRole(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Set users' roles.\n   *\n   * Replaces all existing roles with a new set of roles.\n   *\n   * @example\n   *     Roles.setUserRoles(userId, 'admin')\n   *     Roles.setUserRoles(userId, ['view-secrets'], 'example.com')\n   *     Roles.setUserRoles([user1, user2], ['user','editor'])\n   *     Roles.setUserRoles([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method setUserRoles\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if `true`, remove all roles the user has, of any scope, if `false`, only the one in the same scope\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  setUserRoles: function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error('Missing \\'users\\' param.')\n    if (!roles) throw new Error('Missing \\'roles\\' param.')\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      ifExists: false,\n      anyScope: false\n    }, options)\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n      // we first clear all roles for the user\n      const selector = { 'user._id': id }\n      if (!options.anyScope) {\n        selector.scope = options.scope\n      }\n\n      Meteor.roleAssignment.remove(selector)\n\n      // and then add all\n      for (const role of roles) {\n        Roles._addUserToRole(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Add one user to one role.\n   *\n   * @method _addUserToRole\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @private\n   * @static\n   */\n  _addUserToRole: function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) {\n      return\n    }\n\n    const role = Meteor.roles.findOne({ _id: roleName }, { fields: { children: 1 } })\n\n    if (!role) {\n      if (options.ifExists) {\n        return []\n      } else {\n        throw new Error('Role \\'' + roleName + '\\' does not exist.')\n      }\n    }\n\n    // This might create duplicates, because we don't have a unique index, but that's all right. In case there are two, withdrawing the role will effectively kill them both.\n    const res = Meteor.roleAssignment.upsert({\n      'user._id': userId,\n      'role._id': roleName,\n      scope: options.scope\n    }, {\n      $setOnInsert: {\n        user: { _id: userId },\n        role: { _id: roleName },\n        scope: options.scope\n      }\n    })\n\n    if (res.insertedId) {\n      Meteor.roleAssignment.update({ _id: res.insertedId }, {\n        $set: {\n          inheritedRoles: [roleName, ...Roles._getInheritedRoleNames(role)].map(r => ({ _id: r }))\n        }\n      })\n    }\n\n    return res\n  },\n\n  /**\n   * Returns an array of role names the given role name is a child of.\n   *\n   * @example\n   *     Roles._getParentRoleNames({ _id: 'admin', children; [] })\n   *\n   * @method _getParentRoleNames\n   * @param {object} role The role object\n   * @private\n   * @static\n   */\n  _getParentRoleNames: function (role) {\n    if (!role) {\n      return []\n    }\n\n    const parentRoles = new Set([role._id])\n\n    for (const roleName of parentRoles) {\n      Meteor.roles.find({ 'children._id': roleName }).fetch().forEach(parentRole => {\n        parentRoles.add(parentRole._id)\n      })\n    }\n\n    parentRoles.delete(role._id)\n\n    return [...parentRoles]\n  },\n\n  /**\n   * Returns an array of role names the given role name is a parent of.\n   *\n   * @example\n   *     Roles._getInheritedRoleNames({ _id: 'admin', children; [] })\n   *\n   * @method _getInheritedRoleNames\n   * @param {object} role The role object\n   * @private\n   * @static\n   */\n  _getInheritedRoleNames: function (role) {\n    const inheritedRoles = new Set()\n    const nestedRoles = new Set([role])\n\n    for (const r of nestedRoles) {\n      const roles = Meteor.roles.find({ _id: { $in: r.children.map(r => r._id) } }, { fields: { children: 1 } }).fetch()\n\n      for (const r2 of roles) {\n        inheritedRoles.add(r2._id)\n        nestedRoles.add(r2)\n      }\n    }\n\n    return [...inheritedRoles]\n  },\n\n  /**\n   * Remove users from assigned roles.\n   *\n   * @example\n   *     Roles.removeUsersFromRoles(userId, 'admin')\n   *     Roles.removeUsersFromRoles([userId, user2], ['editor'])\n   *     Roles.removeUsersFromRoles(userId, ['user'], 'group1')\n   *\n   * @method removeUsersFromRoles\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to remove users from. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  removeUsersFromRoles: function (users, roles, options) {\n    if (!users) throw new Error('Missing \\'users\\' param.')\n    if (!roles) throw new Error('Missing \\'roles\\' param.')\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    for (const user of users) {\n      if (!user) continue\n\n      for (const role of roles) {\n        let id\n        if (typeof user === 'object') {\n          id = user._id\n        } else {\n          id = user\n        }\n\n        Roles._removeUserFromRole(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Remove one user from one role.\n   *\n   * @method _removeUserFromRole\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @private\n   * @static\n   */\n  _removeUserFromRole: function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) return\n\n    const selector = {\n      'user._id': userId,\n      'role._id': roleName\n    }\n\n    if (!options.anyScope) {\n      selector.scope = options.scope\n    }\n\n    Meteor.roleAssignment.remove(selector)\n  },\n\n  /**\n   * Check if user has specified roles.\n   *\n   * @example\n   *     // global roles\n   *     Roles.userIsInRole(user, 'admin')\n   *     Roles.userIsInRole(user, ['admin','editor'])\n   *     Roles.userIsInRole(userId, 'admin')\n   *     Roles.userIsInRole(userId, ['admin','editor'])\n   *\n   *     // scope roles (global roles are still checked)\n   *     Roles.userIsInRole(user, 'admin', 'group1')\n   *     Roles.userIsInRole(userId, ['admin','editor'], 'group1')\n   *     Roles.userIsInRole(userId, ['admin','editor'], {scope: 'group1'})\n   *\n   * @method userIsInRole\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} roles Name of role or an array of roles to check against. If array,\n   *                             will return `true` if user is in _any_ role.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope; if supplied, limits check to just that scope\n   *     the user's global roles will always be checked whether scope is specified or not\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Boolean} `true` if user is in _any_ of the target roles\n   * @static\n   */\n  userIsInRole: function (user, roles, options) {\n    let id\n    options = Roles._normalizeOptions(options)\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    roles = roles.filter(r => r != null)\n\n    if (!roles.length) return false\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      anyScope: false\n    }, options)\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return false\n    if (typeof id !== 'string') return false\n\n    const selector = { 'user._id': id }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope, null] }\n    }\n\n    return roles.some((roleName) => {\n      selector['inheritedRoles._id'] = roleName\n\n      return Meteor.roleAssignment.find(selector, { limit: 1 }).count() > 0\n    })\n  },\n\n  /**\n   * Retrieve user's roles.\n   *\n   * @method getRolesForUser\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of scope to provide roles for; if not specified, global roles are returned\n   *   - `anyScope`: if set, role can be in any scope (`scope` and `onlyAssigned` options are ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `onlyAssigned`: return only assigned roles and not automatically inferred (like subroles)\n   *   - `fullObjects`: return full roles objects (`true`) or just names (`false`) (`onlyAssigned` option is ignored) (default `false`)\n   *     If you have a use-case for this option, please file a feature-request. You shouldn't need to use it as it's\n   *     result strongly dependent on the internal data structure of this plugin.\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Array} Array of user's roles, unsorted.\n   * @static\n   */\n  getRolesForUser: function (user, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      fullObjects: false,\n      onlyAssigned: false,\n      anyScope: false,\n      onlyScoped: false\n    }, options)\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = { 'user._id': id }\n    const filter = { fields: { 'inheritedRoles._id': 1 } }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    if (options.onlyAssigned) {\n      delete filter.fields['inheritedRoles._id']\n      filter.fields['role._id'] = 1\n    }\n\n    if (options.fullObjects) {\n      delete filter.fields\n    }\n\n    const roles = Meteor.roleAssignment.find(selector, filter).fetch()\n\n    if (options.fullObjects) {\n      return roles\n    }\n\n    return [...new Set(roles.reduce((rev, current) => {\n      if (current.inheritedRoles) {\n        return rev.concat(current.inheritedRoles.map(r => r._id))\n      } else if (current.role) {\n        rev.push(current.role._id)\n      }\n      return rev\n    }, []))]\n  },\n\n  /**\n   * Retrieve cursor of all existing roles.\n   *\n   * @method getAllRoles\n   * @param {Object} queryOptions Options which are passed directly\n   *                                through to `RolesCollection.find(query, options)`.\n   * @return {Cursor} Cursor of existing roles.\n   * @static\n   */\n  getAllRoles: function (queryOptions) {\n    queryOptions = queryOptions || { sort: { _id: 1 } }\n\n    return Meteor.roles.find({}, queryOptions)\n  },\n\n  /**\n   * Retrieve all users who are in target role.\n   *\n   * Options:\n   *\n   * @method getUsersInRole\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `queryOptions`: options which are passed directly\n   *     through to `Meteor.users.find(query, options)`\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `Meteor.users.find(query, options)`\n   * @return {Cursor} Cursor of users in roles.\n   * @static\n   */\n  getUsersInRole: function (roles, options, queryOptions) {\n    const ids = Roles.getUserAssignmentsForRole(roles, options).fetch().map(a => a.user._id)\n\n    return Meteor.users.find({ _id: { $in: ids } }, ((options && options.queryOptions) || queryOptions) || {})\n  },\n\n  /**\n   * Retrieve all assignments of a user which are for the target role.\n   *\n   * Options:\n   *\n   * @method getUserAssignmentsForRole\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `queryOptions`: options which are passed directly\n   *     through to `RoleAssignmentCollection.find(query, options)`\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Cursor} Cursor of user assignments for roles.\n   * @static\n   */\n  getUserAssignmentsForRole: function (roles, options) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign({\n      anyScope: false,\n      queryOptions: {}\n    }, options)\n\n    return Roles._getUsersInRoleCursor(roles, options, options.queryOptions)\n  },\n\n  /**\n   * @method _getUsersInRoleCursor\n   * @param {Array|String} roles Name of role or an array of roles. If array, ids of users are\n   *                             returned which have at least one of the roles\n   *                             assigned but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [filter] Options which are passed directly\n   *                                through to `RoleAssignmentCollection.find(query, options)`\n   * @return {Object} Cursor to the assignment documents\n   * @private\n   * @static\n   */\n  _getUsersInRoleCursor: function (roles, options, filter) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign({\n      anyScope: false,\n      onlyScoped: false\n    }, options)\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    filter = Object.assign({\n      fields: { 'user._id': 1 }\n    }, filter)\n\n    const selector = { 'inheritedRoles._id': { $in: roles } }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    return Meteor.roleAssignment.find(selector, filter)\n  },\n\n  /**\n   * Deprecated. Use `getScopesForUser` instead.\n   *\n   * @method getGroupsForUser\n   * @static\n   * @deprecated\n   */\n  getGroupsForUser: function (...args) {\n    if (!getGroupsForUserDeprecationWarning) {\n      getGroupsForUserDeprecationWarning = true\n      console && console.warn('getGroupsForUser has been deprecated. Use getScopesForUser instead.')\n    }\n\n    return Roles.getScopesForUser(...args)\n  },\n\n  /**\n   * Retrieve users scopes, if any.\n   *\n   * @method getScopesForUser\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} [roles] Name of roles to restrict scopes to.\n   *\n   * @return {Array} Array of user's scopes, unsorted.\n   * @static\n   */\n  getScopesForUser: function (user, roles) {\n    let id\n\n    if (roles && !Array.isArray(roles)) roles = [roles]\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id,\n      scope: { $ne: null }\n    }\n\n    if (roles) {\n      selector['inheritedRoles._id'] = { $in: roles }\n    }\n\n    const scopes = Meteor.roleAssignment.find(selector, { fields: { scope: 1 } }).fetch().map(obi => obi.scope)\n\n    return [...new Set(scopes)]\n  },\n\n  /**\n   * Rename a scope.\n   *\n   * Roles assigned with a given scope are changed to be under the new scope.\n   *\n   * @method renameScope\n   * @param {String} oldName Old name of a scope.\n   * @param {String} newName New name of a scope.\n   * @static\n   */\n  renameScope: function (oldName, newName) {\n    let count\n\n    Roles._checkScopeName(oldName)\n    Roles._checkScopeName(newName)\n\n    if (oldName === newName) return\n\n    do {\n      count = Meteor.roleAssignment.update({\n        scope: oldName\n      }, {\n        $set: {\n          scope: newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n  },\n\n  /**\n   * Remove a scope.\n   *\n   * Roles assigned with a given scope are removed.\n   *\n   * @method removeScope\n   * @param {String} name The name of a scope.\n   * @static\n   */\n  removeScope: function (name) {\n    Roles._checkScopeName(name)\n\n    Meteor.roleAssignment.remove({ scope: name })\n  },\n\n  /**\n   * Throw an exception if `roleName` is an invalid role name.\n   *\n   * @method _checkRoleName\n   * @param {String} roleName A role name to match against.\n   * @private\n   * @static\n   */\n  _checkRoleName: function (roleName) {\n    if (!roleName || typeof roleName !== 'string' || roleName.trim() !== roleName) {\n      throw new Error('Invalid role name \\'' + roleName + '\\'.')\n    }\n  },\n\n  /**\n   * Find out if a role is an ancestor of another role.\n   *\n   * WARNING: If you check this on the client, please make sure all roles are published.\n   *\n   * @method isParentOf\n   * @param {String} parentRoleName The role you want to research.\n   * @param {String} childRoleName The role you expect to be among the children of parentRoleName.\n   * @static\n   */\n  isParentOf: function (parentRoleName, childRoleName) {\n    if (parentRoleName === childRoleName) {\n      return true\n    }\n\n    if (parentRoleName == null || childRoleName == null) {\n      return false\n    }\n\n    Roles._checkRoleName(parentRoleName)\n    Roles._checkRoleName(childRoleName)\n\n    let rolesToCheck = [parentRoleName]\n    while (rolesToCheck.length !== 0) {\n      const roleName = rolesToCheck.pop()\n\n      if (roleName === childRoleName) {\n        return true\n      }\n\n      const role = Meteor.roles.findOne({ _id: roleName })\n\n      // This should not happen, but this is a problem to address at some other time.\n      if (!role) continue\n\n      rolesToCheck = rolesToCheck.concat(role.children.map(r => r._id))\n    }\n\n    return false\n  },\n\n  /**\n   * Normalize options.\n   *\n   * @method _normalizeOptions\n   * @param {Object} options Options to normalize.\n   * @return {Object} Normalized options.\n   * @private\n   * @static\n   */\n  _normalizeOptions: function (options) {\n    options = options === undefined ? {} : options\n\n    if (options === null || typeof options === 'string') {\n      options = { scope: options }\n    }\n\n    options.scope = Roles._normalizeScopeName(options.scope)\n\n    return options\n  },\n\n  /**\n   * Normalize scope name.\n   *\n   * @method _normalizeScopeName\n   * @param {String} scopeName A scope name to normalize.\n   * @return {String} Normalized scope name.\n   * @private\n   * @static\n   */\n  _normalizeScopeName: function (scopeName) {\n    // map undefined and null to null\n    if (scopeName == null) {\n      return null\n    } else {\n      return scopeName\n    }\n  },\n\n  /**\n   * Throw an exception if `scopeName` is an invalid scope name.\n   *\n   * @method _checkRoleName\n   * @param {String} scopeName A scope name to match against.\n   * @private\n   * @static\n   */\n  _checkScopeName: function (scopeName) {\n    if (scopeName === null) return\n\n    if (\n      !scopeName ||\n      typeof scopeName !== 'string' ||\n      scopeName.trim() !== scopeName\n    ) {\n      throw new Error(`Invalid scope name '${scopeName}'.`)\n    }\n  }\n})\n", "/* global Roles */\nimport { Meteor } from 'meteor/meteor'\nimport { Mongo } from 'meteor/mongo'\n\n/**\n * Provides functions related to user authorization. Compatible with built-in Meteor accounts packages.\n *\n * Roles are accessible throgh `Meteor.roles` collection and documents consist of:\n *  - `_id`: role name\n *  - `children`: list of subdocuments:\n *    - `_id`\n *\n * Children list elements are subdocuments so that they can be easier extended in the future or by plugins.\n *\n * Roles can have multiple parents and can be children (subroles) of multiple roles.\n *\n * Example: `{_id: 'admin', children: [{_id: 'editor'}]}`\n *\n * The assignment of a role to a user is stored in a collection, accessible through `Meteor.roleAssignment`.\n * It's documents consist of\n *  - `_id`: Internal MongoDB id\n *  - `role`: A role object which got assigned. Usually only contains the `_id` property\n *  - `user`: A user object, usually only contains the `_id` property\n *  - `scope`: scope name\n *  - `inheritedRoles`: A list of all the roles objects inherited by the assigned role.\n *\n * @module Roles\n */\nexport const RolesCollection = new Mongo.Collection('roles')\n\nif (!Meteor.roles) {\n  Meteor.roles = RolesCollection\n}\n\nexport const RoleAssignmentCollection = new Mongo.Collection('role-assignment')\n\nif (!Meteor.roleAssignment) {\n  Meteor.roleAssignment = RoleAssignmentCollection\n}\n\n/**\n * @class Roles\n */\nif (typeof Roles === 'undefined') {\n  Roles = {} // eslint-disable-line no-global-assign\n}\n\nlet getGroupsForUserDeprecationWarning = false\n\n/**\n * Helper, resolves async some\n * @param {*} arr\n * @param {*} predicate\n * @returns {Promise<Boolean>}\n */\nconst asyncSome = async (arr, predicate) => {\n  for (const e of arr) {\n    if (await predicate(e)) return true\n  }\n  return false\n}\n\nObject.assign(Roles, {\n  /**\n   * Used as a global group (now scope) name. Not used anymore.\n   *\n   * @property GLOBAL_GROUP\n   * @static\n   * @deprecated\n   */\n  GLOBAL_GROUP: null,\n\n  /**\n   * Create a new role.\n   *\n   * @method createRoleAsync\n   * @param {String} roleName Name of role.\n   * @param {Object} [options] Options:\n   *   - `unlessExists`: if `true`, exception will not be thrown in the role already exists\n   * @return {Promise<String>} ID of the new role or null.\n   * @static\n   */\n  createRoleAsync: async function (roleName, options) {\n    Roles._checkRoleName(roleName)\n\n    options = Object.assign(\n      {\n        unlessExists: false\n      },\n      options\n    )\n\n    let insertedId = null\n\n    const existingRole = await Meteor.roles.findOneAsync({ _id: roleName })\n\n    if (existingRole) {\n      await Meteor.roles.updateAsync(\n        { _id: roleName },\n        { $setOnInsert: { children: [] } }\n      )\n      return null\n    } else {\n      insertedId = await Meteor.roles.insertAsync({\n        _id: roleName,\n        children: []\n      })\n    }\n\n    if (!insertedId) {\n      if (options.unlessExists) return null\n      throw new Error(\"Role '\" + roleName + \"' already exists.\")\n    }\n\n    return insertedId\n  },\n\n  /**\n   * Delete an existing role.\n   *\n   * If the role is set for any user, it is automatically unset.\n   *\n   * @method deleteRoleAsync\n   * @param {String} roleName Name of role.\n   * @returns {Promise}\n   * @static\n   */\n  deleteRoleAsync: async function (roleName) {\n    let roles\n    let inheritedRoles\n\n    Roles._checkRoleName(roleName)\n\n    // Remove all assignments\n    await Meteor.roleAssignment.removeAsync({\n      'role._id': roleName\n    })\n\n    do {\n      // For all roles who have it as a dependency ...\n      roles = await Roles._getParentRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: roleName })\n      )\n\n      for (const r of await Meteor.roles\n        .find({ _id: { $in: roles } })\n        .fetchAsync()) {\n        await Meteor.roles.updateAsync(\n          {\n            _id: r._id\n          },\n          {\n            $pull: {\n              children: {\n                _id: roleName\n              }\n            }\n          }\n        )\n\n        inheritedRoles = await Roles._getInheritedRoleNamesAsync(\n          await Meteor.roles.findOneAsync({ _id: r._id })\n        )\n        await Meteor.roleAssignment.updateAsync(\n          {\n            'role._id': r._id\n          },\n          {\n            $set: {\n              inheritedRoles: [r._id, ...inheritedRoles].map((r2) => ({\n                _id: r2\n              }))\n            }\n          },\n          { multi: true }\n        )\n      }\n    } while (roles.length > 0)\n\n    // And finally remove the role itself\n    await Meteor.roles.removeAsync({ _id: roleName })\n  },\n\n  /**\n   * Rename an existing role.\n   *\n   * @method renameRoleAsync\n   * @param {String} oldName Old name of a role.\n   * @param {String} newName New name of a role.\n   * @returns {Promise}\n   * @static\n   */\n  renameRoleAsync: async function (oldName, newName) {\n    let count\n\n    Roles._checkRoleName(oldName)\n    Roles._checkRoleName(newName)\n\n    if (oldName === newName) return\n\n    const role = await Meteor.roles.findOneAsync({ _id: oldName })\n\n    if (!role) {\n      throw new Error(\"Role '\" + oldName + \"' does not exist.\")\n    }\n\n    role._id = newName\n\n    await Meteor.roles.insertAsync(role)\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          'role._id': oldName\n        },\n        {\n          $set: {\n            'role._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          'inheritedRoles._id': oldName\n        },\n        {\n          $set: {\n            'inheritedRoles.$._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    do {\n      count = await Meteor.roles.updateAsync(\n        {\n          'children._id': oldName\n        },\n        {\n          $set: {\n            'children.$._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    await Meteor.roles.removeAsync({ _id: oldName })\n  },\n\n  /**\n   * Add role parent to roles.\n   *\n   * Previous parents are kept (role can have multiple parents). For users which have the\n   * parent role set, new subroles are added automatically.\n   *\n   * @method addRolesToParentAsync\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @static\n   */\n  addRolesToParentAsync: async function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      await Roles._addRoleToParentAsync(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _addRoleToParentAsync\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _addRoleToParentAsync: async function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // query to get role's children\n    const role = await Meteor.roles.findOneAsync({ _id: roleName })\n\n    if (!role) {\n      throw new Error(`Role '${roleName}' does not exist.`)\n    }\n\n    // detect cycles\n    if ((await Roles._getInheritedRoleNamesAsync(role)).includes(parentName)) {\n      throw new Error(\n        `Roles '${roleName}' and '${parentName}' would form a cycle.`\n      )\n    }\n\n    const count = await Meteor.roles.updateAsync(\n      {\n        _id: parentName,\n        'children._id': {\n          $ne: role._id\n        }\n      },\n      {\n        $push: {\n          children: {\n            _id: role._id\n          }\n        }\n      }\n    )\n\n    // if there was no change, parent role might not exist, or role is\n    // already a sub-role; in any case we do not have anything more to do\n    if (!count) return\n\n    await Meteor.roleAssignment.updateAsync(\n      {\n        'inheritedRoles._id': parentName\n      },\n      {\n        $push: {\n          inheritedRoles: {\n            $each: [\n              role._id,\n              ...(await Roles._getInheritedRoleNamesAsync(role))\n            ].map((r) => ({ _id: r }))\n          }\n        }\n      },\n      { multi: true }\n    )\n  },\n\n  /**\n   * Remove role parent from roles.\n   *\n   * Other parents are kept (role can have multiple parents). For users which have the\n   * parent role set, removed subrole is removed automatically.\n   *\n   * @method removeRolesFromParentAsync\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @static\n   */\n  removeRolesFromParentAsync: async function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      await Roles._removeRoleFromParentAsync(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _removeRoleFromParentAsync\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _removeRoleFromParentAsync: async function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // check for role existence\n    // this would not really be needed, but we are trying to match addRolesToParent\n    const role = await Meteor.roles.findOneAsync(\n      { _id: roleName },\n      { fields: { _id: 1 } }\n    )\n\n    if (!role) {\n      throw new Error(`Role '${roleName}' does not exist.`)\n    }\n\n    const count = await Meteor.roles.updateAsync(\n      {\n        _id: parentName\n      },\n      {\n        $pull: {\n          children: {\n            _id: role._id\n          }\n        }\n      }\n    )\n\n    // if there was no change, parent role might not exist, or role was\n    // already not a subrole; in any case we do not have anything more to do\n    if (!count) return\n\n    // For all roles who have had it as a dependency ...\n    const roles = [\n      ...(await Roles._getParentRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: parentName })\n      )),\n      parentName\n    ]\n\n    for (const r of await Meteor.roles\n      .find({ _id: { $in: roles } })\n      .fetchAsync()) {\n      const inheritedRoles = await Roles._getInheritedRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: r._id })\n      )\n      await Meteor.roleAssignment.updateAsync(\n        {\n          'role._id': r._id,\n          'inheritedRoles._id': role._id\n        },\n        {\n          $set: {\n            inheritedRoles: [r._id, ...inheritedRoles].map((r2) => ({\n              _id: r2\n            }))\n          }\n        },\n        { multi: true }\n      )\n    }\n  },\n\n  /**\n   * Add users to roles.\n   *\n   * Adds roles to existing roles for each user.\n   *\n   * @example\n   *     Roles.addUsersToRolesAsync(userId, 'admin')\n   *     Roles.addUsersToRolesAsync(userId, ['view-secrets'], 'example.com')\n   *     Roles.addUsersToRolesAsync([user1, user2], ['user','editor'])\n   *     Roles.addUsersToRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method addUsersToRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  addUsersToRolesAsync: async function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        ifExists: false\n      },\n      options\n    )\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n\n      for (const role of roles) {\n        await Roles._addUserToRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Set users' roles.\n   *\n   * Replaces all existing roles with a new set of roles.\n   *\n   * @example\n   *     await Roles.setUserRolesAsync(userId, 'admin')\n   *     await Roles.setUserRolesAsync(userId, ['view-secrets'], 'example.com')\n   *     await Roles.setUserRolesAsync([user1, user2], ['user','editor'])\n   *     await Roles.setUserRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method setUserRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if `true`, remove all roles the user has, of any scope, if `false`, only the one in the same scope\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  setUserRolesAsync: async function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        ifExists: false,\n        anyScope: false\n      },\n      options\n    )\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n      // we first clear all roles for the user\n      const selector = { 'user._id': id }\n      if (!options.anyScope) {\n        selector.scope = options.scope\n      }\n\n      await Meteor.roleAssignment.removeAsync(selector)\n\n      // and then add all\n      for (const role of roles) {\n        await Roles._addUserToRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Add one user to one role.\n   *\n   * @method _addUserToRoleAsync\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _addUserToRoleAsync: async function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) {\n      return\n    }\n\n    const role = await Meteor.roles.findOneAsync(\n      { _id: roleName },\n      { fields: { children: 1 } }\n    )\n\n    if (!role) {\n      if (options.ifExists) {\n        return []\n      } else {\n        throw new Error(\"Role '\" + roleName + \"' does not exist.\")\n      }\n    }\n\n    // This might create duplicates, because we don't have a unique index, but that's all right. In case there are two, withdrawing the role will effectively kill them both.\n    // TODO revisit this\n    /* const res = await RoleAssignmentCollection.upsertAsync(\n      {\n        \"user._id\": userId,\n        \"role._id\": roleName,\n        scope: options.scope,\n      },\n      {\n        $setOnInsert: {\n          user: { _id: userId },\n          role: { _id: roleName },\n          scope: options.scope,\n        },\n      }\n    ); */\n    const existingAssignment = await Meteor.roleAssignment.findOneAsync({\n      'user._id': userId,\n      'role._id': roleName,\n      scope: options.scope\n    })\n\n    let insertedId\n    let res\n    if (existingAssignment) {\n      await Meteor.roleAssignment.updateAsync(existingAssignment._id, {\n        $set: {\n          user: { _id: userId },\n          role: { _id: roleName },\n          scope: options.scope\n        }\n      })\n\n      res = await Meteor.roleAssignment.findOneAsync(existingAssignment._id)\n    } else {\n      insertedId = await Meteor.roleAssignment.insertAsync({\n        user: { _id: userId },\n        role: { _id: roleName },\n        scope: options.scope\n      })\n    }\n\n    if (insertedId) {\n      await Meteor.roleAssignment.updateAsync(\n        { _id: insertedId },\n        {\n          $set: {\n            inheritedRoles: [\n              roleName,\n              ...(await Roles._getInheritedRoleNamesAsync(role))\n            ].map((r) => ({ _id: r }))\n          }\n        }\n      )\n\n      res = await Meteor.roleAssignment.findOneAsync({ _id: insertedId })\n    }\n    res.insertedId = insertedId // For backward compatibility\n\n    return res\n  },\n\n  /**\n   * Returns an array of role names the given role name is a child of.\n   *\n   * @example\n   *     Roles._getParentRoleNamesAsync({ _id: 'admin', children; [] })\n   *\n   * @method _getParentRoleNamesAsync\n   * @param {object} role The role object\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _getParentRoleNamesAsync: async function (role) {\n    if (!role) {\n      return []\n    }\n\n    const parentRoles = new Set([role._id])\n\n    for (const roleName of parentRoles) {\n      for (const parentRole of await Meteor.roles\n        .find({ 'children._id': roleName })\n        .fetchAsync()) {\n        parentRoles.add(parentRole._id)\n      }\n    }\n\n    parentRoles.delete(role._id)\n\n    return [...parentRoles]\n  },\n\n  /**\n   * Returns an array of role names the given role name is a parent of.\n   *\n   * @example\n   *     Roles._getInheritedRoleNames({ _id: 'admin', children; [] })\n   *\n   * @method _getInheritedRoleNames\n   * @param {object} role The role object\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _getInheritedRoleNamesAsync: async function (role) {\n    const inheritedRoles = new Set()\n    const nestedRoles = new Set([role])\n\n    for (const r of nestedRoles) {\n      const roles = await Meteor.roles\n        .find(\n          { _id: { $in: r.children.map((r) => r._id) } },\n          { fields: { children: 1 } }\n        )\n        .fetchAsync()\n\n      for (const r2 of roles) {\n        inheritedRoles.add(r2._id)\n        nestedRoles.add(r2)\n      }\n    }\n\n    return [...inheritedRoles]\n  },\n\n  /**\n   * Remove users from assigned roles.\n   *\n   * @example\n   *     await Roles.removeUsersFromRolesAsync(userId, 'admin')\n   *     await Roles.removeUsersFromRolesAsync([userId, user2], ['editor'])\n   *     await Roles.removeUsersFromRolesAsync(userId, ['user'], 'group1')\n   *\n   * @method removeUsersFromRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to remove users from. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  removeUsersFromRolesAsync: async function (users, roles, options) {\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    for (const user of users) {\n      if (!user) return\n\n      for (const role of roles) {\n        let id\n        if (typeof user === 'object') {\n          id = user._id\n        } else {\n          id = user\n        }\n\n        await Roles._removeUserFromRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Remove one user from one role.\n   *\n   * @method _removeUserFromRoleAsync\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _removeUserFromRoleAsync: async function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) return\n\n    const selector = {\n      'user._id': userId,\n      'role._id': roleName\n    }\n\n    if (!options.anyScope) {\n      selector.scope = options.scope\n    }\n\n    await Meteor.roleAssignment.removeAsync(selector)\n  },\n\n  /**\n   * Check if user has specified roles.\n   *\n   * @example\n   *     // global roles\n   *     await Roles.userIsInRoleAsync(user, 'admin')\n   *     await Roles.userIsInRoleAsync(user, ['admin','editor'])\n   *     await Roles.userIsInRoleAsync(userId, 'admin')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'])\n   *\n   *     // scope roles (global roles are still checked)\n   *     await Roles.userIsInRoleAsync(user, 'admin', 'group1')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], 'group1')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], {scope: 'group1'})\n   *\n   * @method userIsInRoleAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} roles Name of role or an array of roles to check against. If array,\n   *                             will return `true` if user is in _any_ role.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope; if supplied, limits check to just that scope\n   *     the user's global roles will always be checked whether scope is specified or not\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Promise<Boolean>} `true` if user is in _any_ of the target roles\n   * @static\n   */\n  userIsInRoleAsync: async function (user, roles, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    roles = roles.filter((r) => r != null)\n\n    if (!roles.length) return false\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        anyScope: false\n      },\n      options\n    )\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return false\n    if (typeof id !== 'string') return false\n\n    const selector = {\n      'user._id': id\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope, null] }\n    }\n\n    const res = await asyncSome(roles, async (roleName) => {\n      selector['inheritedRoles._id'] = roleName\n      const out =\n        (await Meteor.roleAssignment.countDocuments(selector, { limit: 1 })) > 0\n      return out\n    })\n\n    return res\n  },\n\n  /**\n   * Retrieve user's roles.\n   *\n   * @method getRolesForUserAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of scope to provide roles for; if not specified, global roles are returned\n   *   - `anyScope`: if set, role can be in any scope (`scope` and `onlyAssigned` options are ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `onlyAssigned`: return only assigned roles and not automatically inferred (like subroles)\n   *   - `fullObjects`: return full roles objects (`true`) or just names (`false`) (`onlyAssigned` option is ignored) (default `false`)\n   *     If you have a use-case for this option, please file a feature-request. You shouldn't need to use it as it's\n   *     result strongly dependent on the internal data structure of this plugin.\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Promise<Array>} Array of user's roles, unsorted.\n   * @static\n   */\n  getRolesForUserAsync: async function (user, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      fullObjects: false,\n      onlyAssigned: false,\n      anyScope: false,\n      onlyScoped: false\n    }, options)\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id\n    }\n\n    const filter = {\n      fields: { 'inheritedRoles._id': 1 }\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    if (options.onlyAssigned) {\n      delete filter.fields['inheritedRoles._id']\n      filter.fields['role._id'] = 1\n    }\n\n    if (options.fullObjects) {\n      delete filter.fields\n    }\n\n    const roles = await Meteor.roleAssignment.find(selector, filter).fetchAsync()\n\n    if (options.fullObjects) {\n      return roles\n    }\n\n    return [\n      ...new Set(\n        roles.reduce((rev, current) => {\n          if (current.inheritedRoles) {\n            return rev.concat(current.inheritedRoles.map((r) => r._id))\n          } else if (current.role) {\n            rev.push(current.role._id)\n          }\n          return rev\n        }, [])\n      )\n    ]\n  },\n\n  /**\n   * Retrieve cursor of all existing roles.\n   *\n   * @method getAllRoles\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `RolesCollection.find(query, options)`.\n   * @return {Cursor} Cursor of existing roles.\n   * @static\n   */\n  getAllRoles: function (queryOptions) {\n    queryOptions = queryOptions || { sort: { _id: 1 } }\n\n    return Meteor.roles.find({}, queryOptions)\n  },\n\n  /**\n   * Retrieve all users who are in target role.\n   *\n   * Options:\n   *\n   * @method getUsersInRoleAsync\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `queryOptions`: options which are passed directly\n   *     through to `Meteor.users.find(query, options)`\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `Meteor.users.find(query, options)`\n   * @return {Promise<Cursor>} Cursor of users in roles.\n   * @static\n   */\n  getUsersInRoleAsync: async function (roles, options, queryOptions) {\n    const ids = (\n      await Roles.getUserAssignmentsForRole(roles, options).fetchAsync()\n    ).map((a) => a.user._id)\n\n    return Meteor.users.find(\n      { _id: { $in: ids } },\n      (options && options.queryOptions) || queryOptions || {}\n    )\n  },\n\n  /**\n   * Retrieve all assignments of a user which are for the target role.\n   *\n   * Options:\n   *\n   * @method getUserAssignmentsForRole\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `queryOptions`: options which are passed directly\n   *     through to `RoleAssignmentCollection.find(query, options)`\n\n   * Alternatively, it can be a scope name string.\n   * @return {Cursor} Cursor of user assignments for roles.\n   * @static\n   */\n  getUserAssignmentsForRole: function (roles, options) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign(\n      {\n        anyScope: false,\n        queryOptions: {}\n      },\n      options\n    )\n\n    return Roles._getUsersInRoleCursor(roles, options, options.queryOptions)\n  },\n\n  /**\n   * @method _getUsersInRoleCursor\n   * @param {Array|String} roles Name of role or an array of roles. If array, ids of users are\n   *                             returned which have at least one of the roles\n   *                             assigned but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [filter] Options which are passed directly\n   *                                through to `RoleAssignmentCollection.find(query, options)`\n   * @return {Object} Cursor to the assignment documents\n   * @private\n   * @static\n   */\n  _getUsersInRoleCursor: function (roles, options, filter) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign(\n      {\n        anyScope: false,\n        onlyScoped: false\n      },\n      options\n    )\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    filter = Object.assign(\n      {\n        fields: { 'user._id': 1 }\n      },\n      filter\n    )\n\n    const selector = {\n      'inheritedRoles._id': { $in: roles }\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    return Meteor.roleAssignment.find(selector, filter)\n  },\n\n  /**\n   * Deprecated. Use `getScopesForUser` instead.\n   *\n   * @method getGroupsForUserAsync\n   * @returns {Promise<Array>}\n   * @static\n   * @deprecated\n   */\n  getGroupsForUserAsync: async function (...args) {\n    if (!getGroupsForUserDeprecationWarning) {\n      getGroupsForUserDeprecationWarning = true\n      console &&\n        console.warn(\n          'getGroupsForUser has been deprecated. Use getScopesForUser instead.'\n        )\n    }\n\n    return await Roles.getScopesForUser(...args)\n  },\n\n  /**\n   * Retrieve users scopes, if any.\n   *\n   * @method getScopesForUserAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} [roles] Name of roles to restrict scopes to.\n   *\n   * @return {Promise<Array>} Array of user's scopes, unsorted.\n   * @static\n   */\n  getScopesForUserAsync: async function (user, roles) {\n    let id\n\n    if (roles && !Array.isArray(roles)) roles = [roles]\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id,\n      scope: { $ne: null }\n    }\n\n    if (roles) {\n      selector['inheritedRoles._id'] = { $in: roles }\n    }\n\n    const scopes = (\n      await Meteor.roleAssignment\n        .find(selector, { fields: { scope: 1 } })\n        .fetchAsync()\n    ).map((obi) => obi.scope)\n\n    return [...new Set(scopes)]\n  },\n\n  /**\n   * Rename a scope.\n   *\n   * Roles assigned with a given scope are changed to be under the new scope.\n   *\n   * @method renameScopeAsync\n   * @param {String} oldName Old name of a scope.\n   * @param {String} newName New name of a scope.\n   * @returns {Promise}\n   * @static\n   */\n  renameScopeAsync: async function (oldName, newName) {\n    let count\n\n    Roles._checkScopeName(oldName)\n    Roles._checkScopeName(newName)\n\n    if (oldName === newName) return\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          scope: oldName\n        },\n        {\n          $set: {\n            scope: newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n  },\n\n  /**\n   * Remove a scope.\n   *\n   * Roles assigned with a given scope are removed.\n   *\n   * @method removeScopeAsync\n   * @param {String} name The name of a scope.\n   * @returns {Promise}\n   * @static\n   */\n  removeScopeAsync: async function (name) {\n    Roles._checkScopeName(name)\n\n    await Meteor.roleAssignment.removeAsync({ scope: name })\n  },\n\n  /**\n   * Throw an exception if `roleName` is an invalid role name.\n   *\n   * @method _checkRoleName\n   * @param {String} roleName A role name to match against.\n   * @private\n   * @static\n   */\n  _checkRoleName: function (roleName) {\n    if (\n      !roleName ||\n      typeof roleName !== 'string' ||\n      roleName.trim() !== roleName\n    ) {\n      throw new Error(`Invalid role name '${roleName}'.`)\n    }\n  },\n\n  /**\n   * Find out if a role is an ancestor of another role.\n   *\n   * WARNING: If you check this on the client, please make sure all roles are published.\n   *\n   * @method isParentOfAsync\n   * @param {String} parentRoleName The role you want to research.\n   * @param {String} childRoleName The role you expect to be among the children of parentRoleName.\n   * @returns {Promise}\n   * @static\n   */\n  isParentOfAsync: async function (parentRoleName, childRoleName) {\n    if (parentRoleName === childRoleName) {\n      return true\n    }\n\n    if (parentRoleName == null || childRoleName == null) {\n      return false\n    }\n\n    Roles._checkRoleName(parentRoleName)\n    Roles._checkRoleName(childRoleName)\n\n    let rolesToCheck = [parentRoleName]\n    while (rolesToCheck.length !== 0) {\n      const roleName = rolesToCheck.pop()\n\n      if (roleName === childRoleName) {\n        return true\n      }\n\n      const role = await Meteor.roles.findOneAsync({ _id: roleName })\n\n      // This should not happen, but this is a problem to address at some other time.\n      if (!role) continue\n\n      rolesToCheck = rolesToCheck.concat(role.children.map((r) => r._id))\n    }\n\n    return false\n  },\n\n  /**\n   * Normalize options.\n   *\n   * @method _normalizeOptions\n   * @param {Object} options Options to normalize.\n   * @return {Object} Normalized options.\n   * @private\n   * @static\n   */\n  _normalizeOptions: function (options) {\n    options = options === undefined ? {} : options\n\n    // TODO Number will error out on scope validation, we can either error it out here\n    // or make it into a string and hence a valid input.\n    if (options === null || typeof options === 'string' || typeof options === 'number') {\n      options = { scope: options }\n    }\n\n    options.scope = Roles._normalizeScopeName(options.scope)\n\n    return options\n  },\n\n  /**\n   * Normalize scope name.\n   *\n   * @method _normalizeScopeName\n   * @param {String} scopeName A scope name to normalize.\n   * @return {String} Normalized scope name.\n   * @private\n   * @static\n   */\n  _normalizeScopeName: function (scopeName) {\n    // map undefined and null to null\n    if (scopeName == null) {\n      return null\n    } else {\n      return scopeName\n    }\n  },\n\n  /**\n   * Throw an exception if `scopeName` is an invalid scope name.\n   *\n   * @method _checkScopeName\n   * @param {String} scopeName A scope name to match against.\n   * @private\n   * @static\n   */\n  _checkScopeName: function (scopeName) {\n    if (scopeName === null) return\n\n    if (\n      !scopeName ||\n      typeof scopeName !== 'string' ||\n      scopeName.trim() !== scopeName\n    ) {\n      throw new Error(`Invalid scope name '${scopeName}'.`)\n    }\n  }\n})\n", "/* global Roles, localStorage */\n\n// //////////////////////////////////////////////////////////\n// Debugging helpers\n//\n// Run this in your browser console to turn on debugging\n// for this package:\n//\n//   localstorage.setItem('Roles.debug', true)\n//\n\nRoles.debug = false\n\ntry {\n  if (localStorage) {\n    const temp = localStorage.getItem('Roles.debug')\n\n    if (typeof temp !== 'undefined') {\n      Roles.debug = !!temp\n    }\n  }\n} catch (ex) {\n  // ignore: accessing localStorage when its disabled throws\n  // https://github.com/meteor/meteor/issues/5759\n}\n", "/* global Meteor, Roles, Match, Package */\n\n/**\n * Convenience functions for use on client.\n *\n * NOTE: You must restrict user actions on the server-side; any\n * client-side checks are strictly for convenience and must not be\n * trusted.\n *\n * @module UIHelpers\n */\n\n// //////////////////////////////////////////////////////////\n// UI helpers\n//\n// Use a semi-private variable rather than declaring UI\n// helpers directly so that we can unit test the helpers.\n// XXX For some reason, the UI helpers are not registered\n// before the tests run.\n//\nRoles._uiHelpers = {\n\n  /**\n   * UI helper to check if current user is in at least one\n   * of the target roles.  For use in client-side templates.\n   *\n   * @example\n   *     {{#if isInRole 'admin'}}\n   *     {{/if}}\n   *\n   *     {{#if isInRole 'editor,user'}}\n   *     {{/if}}\n   *\n   *     {{#if isInRole 'editor,user' 'scope1'}}\n   *     {{/if}}\n   *\n   * @method isInRole\n   * @param {String} role Name of role or comma-seperated list of roles.\n   * @param {String} [scope] Optional, name of scope to check.\n   * @return {Boolean} `true` if current user is in at least one of the target roles.\n   * @static\n   * @for UIHelpers\n   */\n  isInRole: function (role, scope) {\n    const user = Meteor.user()\n    const comma = (role || '').indexOf(',')\n    let roles\n\n    if (!user) return false\n    if (!Match.test(role, String)) return false\n\n    if (comma !== -1) {\n      roles = role.split(',').reduce(function (memo, r) {\n        if (!r) {\n          return memo\n        }\n        memo.push(r)\n        return memo\n      }, [])\n    } else {\n      roles = [role]\n    }\n\n    if (Match.test(scope, String)) {\n      return Roles.userIsInRole(user, roles, scope)\n    }\n\n    return Roles.userIsInRole(user, roles)\n  }\n}\n\n// //////////////////////////////////////////////////////////\n// Register UI helpers\n//\n\nif (Roles.debug && console.debug) {\n  console.debug('[roles] Roles.debug =', Roles.debug)\n}\n\nif (typeof Package.blaze !== 'undefined' &&\n    typeof Package.blaze.Blaze !== 'undefined' &&\n    typeof Package.blaze.Blaze.registerHelper === 'function') {\n  Object.entries(Roles._uiHelpers).forEach(([name, func]) => {\n    if (Roles.debug && console.debug) {\n      console.debug('[roles] registering Blaze helper \\'' + name + '\\'')\n    }\n    Package.blaze.Blaze.registerHelper(name, func)\n  })\n}\n"]}