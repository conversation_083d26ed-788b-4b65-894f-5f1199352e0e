import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Chip,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Meteor } from 'meteor/meteor';
import { AIInsightsPanel } from '../components/AIInsightsPanel';

export const TaskDetailPage = () => {
  const { taskId } = useParams();
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTask = async () => {
      try {
        const result = await Meteor.call('tasks.findOne', taskId);
        setTask(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTask();
  }, [taskId]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (!task) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">Task not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h4" gutterBottom>
              {task.title}
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Chip
                label={task.status}
                color={task.status === 'completed' ? 'success' : 'primary'}
                sx={{ mr: 1 }}
              />
              <Chip
                label={task.priority}
                color={
                  task.priority === 'high'
                    ? 'error'
                    : task.priority === 'medium'
                    ? 'warning'
                    : 'info'
                }
                sx={{ mr: 1 }}
              />
              <Chip label={task.category} variant="outlined" />
            </Box>

            <Divider sx={{ my: 2 }} />

            <Typography variant="h6" gutterBottom>
              Description
            </Typography>
            <Typography variant="body1" paragraph>
              {task.description}
            </Typography>

            <Typography variant="h6" gutterBottom>
              Progress
            </Typography>
            <Box sx={{ width: '100%', bgcolor: 'background.paper' }}>
              <Typography variant="body2" color="text.secondary">
                {task.progress}% Complete
              </Typography>
            </Box>

            {task.checklist && task.checklist.length > 0 && (
              <>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Checklist
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  {task.checklist.map((item, index) => (
                    <Typography
                      component="li"
                      key={index}
                      sx={{
                        textDecoration: item.completed ? 'line-through' : 'none',
                        color: item.completed ? 'text.secondary' : 'text.primary',
                      }}
                    >
                      {item.text}
                    </Typography>
                  ))}
                </Box>
              </>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <AIInsightsPanel taskId={taskId} />
        </Grid>
      </Grid>
    </Container>
  );
}; 