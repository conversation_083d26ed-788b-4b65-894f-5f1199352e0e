import React from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useTracker } from 'meteor/react-meteor-data';
import { Meteor } from 'meteor/meteor';
import { LoginPage } from './pages/LoginPage';
import { SignupPage } from './pages/SignupPage';
import { ForgotPasswordPage } from './pages/ForgotPasswordPage';
import { AdminDashboard } from './pages/AdminDashboard';
import { TeamDashboard } from './pages/TeamDashboard';
import { TeamTasksPage } from './pages/TeamTasksPage';
import { TeamMembersPage } from './pages/TeamMembersPage';
import { TaskDetailPage } from './pages/TaskDetailPage';

// Protected Route component
const ProtectedRoute = ({ children, allowedRoles }) => {
  const location = useLocation();
  
  const { user, userRole, isLoading } = useTracker(() => {
    const subscription = Meteor.subscribe('userData');
    const user = Meteor.user();
    const userRole = user?.profile?.role || user?.roles?.[0];
    
    return {
      user,
      userRole,
      isLoading: !subscription.ready()
    };
  }, []);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return <Navigate to={userRole === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace />;
  }

  return children;
};

export const App = () => {
  const { user, isLoading } = useTracker(() => {
    const subscription = Meteor.subscribe('userData');
    return {
      user: Meteor.user(),
      isLoading: !subscription.ready()
    };
  }, []);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container">
      <Routes>
        <Route 
          path="/login" 
          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <LoginPage />} 
        />
        <Route
          path="/signup"
          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <SignupPage />}
        />
        <Route
          path="/forgot-password"
          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <ForgotPasswordPage />}
        />
        <Route
          path="/admin-dashboard/*"
          element={
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/team-dashboard/*"
          element={
            <ProtectedRoute allowedRoles={['team-member']}>
              <TeamDashboard />
            </ProtectedRoute>
          }
        />
        <Route path="/" element={<Navigate to={user ? (user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard') : '/login'} replace />} />
        <Route path="/tasks/:taskId" element={<TaskDetailPage />} />
      </Routes>
    </div>
  );
};