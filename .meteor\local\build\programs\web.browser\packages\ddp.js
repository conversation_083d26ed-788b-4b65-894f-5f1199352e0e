//////////////////////////////////////////////////////////////////////////
//                                                                      //
// This is a generated file. You can view the original                  //
// source in your browser if your browser supports source maps.         //
// Source maps are supported by all recent versions of Chrome, Safari,  //
// and Firefox, and by Internet Explorer 11.                            //
//                                                                      //
//////////////////////////////////////////////////////////////////////////


Package["core-runtime"].queue("ddp",function () {/* Imports */
var DDP = Package['ddp-client'].DDP;



/* Exports */
return {
  export: function () { return {
      DDP: DDP
    };}
}});
