{"version": 3, "sources": ["meteor://💻app/packages/accounts-base/client_main.js", "meteor://💻app/packages/accounts-base/accounts_client.js", "meteor://💻app/packages/accounts-base/accounts_common.js"], "names": ["module", "export", "Accounts", "AccountsClient", "AccountsTest", "default", "exports", "link", "v", "runSetters", "_Meteor$settings", "Meteor", "settings", "_Meteor$settings$publ", "public", "_Meteor$settings$publ2", "packages", "accounts", "users", "_regeneratorRuntime", "_objectSpread", "_inherits<PERSON><PERSON>e", "Accounts<PERSON><PERSON><PERSON>", "_Accounts<PERSON><PERSON><PERSON>", "options", "_this", "call", "_loggingIn", "ReactiveVar", "_loggingOut", "_loginServicesHandle", "connection", "subscribe", "_pageLoadLoginCallbacks", "_pageLoadLoginAttemptInfo", "savedHash", "window", "location", "hash", "_initUrlMatching", "initStorageLocation", "_initLocalStorage", "_loginFuncs", "_loginCallbacksCalled", "_proto", "prototype", "_Meteor$settings$publ3", "storageLocation", "clientStorage", "sessionStorage", "_localStorage", "config", "userId", "_setLoggingIn", "x", "set", "loggingIn", "get", "loggingOut", "registerClientLoginFunction", "funcName", "func", "Error", "callLoginFunction", "_len", "arguments", "length", "funcArgs", "Array", "_key", "apply", "applyLoginFunction", "logout", "callback", "_this2", "applyAsync", "wait", "then", "result", "makeClientLoggedOut", "catch", "e", "logoutOtherClients", "_this3", "err", "_storeLoginToken", "token", "tokenExpires", "callLoginMethod", "_this4", "methodName", "methodArguments", "_suppressLoggingIn", "for<PERSON>ach", "f", "called", "loginCallbacks", "_ref", "error", "loginDetails", "_on<PERSON>ogin<PERSON><PERSON>", "_onLoginFailureHook", "userCallback", "reconnected", "onResultReceived", "_reconnectStopper", "stop", "DDP", "onReconnect", "conn", "storedToken", "_storedLoginToken", "_storedLoginTokenExpires", "_tokenExpiration", "Date", "_tokenExpiresSoon", "resume", "storedTokenNow", "loggedInAndDataReadyCallback", "validateResult", "makeClientLoggedIn", "id", "Tracker", "autorun", "_callee", "computation", "user", "async", "_callee$", "_context", "prev", "next", "awrap", "withComputation", "userAsync", "sent", "Promise", "_userId", "_onLogoutHook", "each", "_unstoreLoginToken", "setUserId", "loginServicesConfigured", "ready", "onPageLoadLogin", "push", "_pageLoadLogin", "attemptInfo", "_debug", "_startupCallback", "setTimeout", "type", "loginWithToken", "_enableAutoLogin", "_autoLoginEnabled", "_pollStoredLoginToken", "_isolateLoginTokenForTest", "LOGIN_TOKEN_KEY", "Random", "USER_ID_KEY", "setItem", "LOGIN_TOKEN_EXPIRES_KEY", "_last<PERSON><PERSON>in<PERSON><PERSON><PERSON>henPolled", "removeItem", "getItem", "_storedUserId", "_unstoreLoginTokenIfExpiresSoon", "_this5", "rootUrlPathPrefix", "__meteor_runtime_config__", "ROOT_URL_PATH_PREFIX", "namespace", "_stream", "rawUrl", "allowed", "_pollIntervalTimer", "clearInterval", "setInterval", "_this6", "currentLoginToken", "_accountsCallbacks", "_attemptToMatchHash", "attemptToMatchHash", "defaultSuccessHandler", "onResetPasswordLink", "onEmailVerificationLink", "onEnrollmentLink", "Package", "blaze", "Template", "Blaze", "registerHelper", "urlPart", "_this7", "startup", "success", "tokenRegex", "RegExp", "match", "_resetPasswordToken", "_verifyEmailToken", "_enrollAccountToken", "EXPIRE_TOKENS_INTERVAL_MS", "VALID_CONFIG_KEYS", "_i", "_Object$keys", "Object", "keys", "key", "includes", "console", "_options", "undefined", "_initConnection", "_initializeCollection", "Hook", "bindEnvironment", "debugPrintExceptions", "DEFAULT_LOGIN_EXPIRATION_DAYS", "LOGIN_UNEXPIRING_TOKEN_DAYS", "lceName", "LoginCancelledError", "makeErrorType", "description", "message", "name", "numericError", "collection", "Mongo", "Collection", "collectionName", "_preventAutopublish", "_addDefaultFieldSelector", "defaultFieldSelector", "fields", "keys2", "isServer", "warn", "join", "self", "findOne", "_self$users", "_self$users2", "isClient", "findOneAsync", "userAsync$", "abrupt", "accountsConfigCalled", "hasOwnProperty", "OAuthEncryption", "loadKey", "oauth<PERSON><PERSON><PERSON>", "_i2", "_Object$keys2", "_i3", "_VALID_CONFIG_KEYS", "isTest", "_name", "onLogin", "ret", "register", "onLoginFailure", "onLogout", "ddpUrl", "connect", "ACCOUNTS_CONNECTION_URL", "_getTokenLifetimeMs", "loginExpirationInDays", "loginExpiration", "_getPasswordResetTokenLifetimeMs", "passwordResetTokenExpiration", "passwordResetTokenExpirationInDays", "DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS", "_getPasswordEnrollTokenLifetimeMs", "passwordEnrollTokenExpiration", "passwordEnrollTokenExpirationInDays", "DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS", "when", "getTime", "minLifetimeMs", "minLifetimeCapMs", "MIN_TOKEN_LIFETIME_CAP_SECS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,QAAQ,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,QAAQ;EAAA,CAAC;EAACC,cAAc,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,cAAc;EAAA,CAAC;EAACC,YAAY,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,YAAY;EAAA,CAAC;EAAC,WAAQ,SAAAC,CAAA,EAAU;IAAC,OAAOC,OAAO;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIH,cAAc,EAACC,YAAY;AAACJ,MAAM,CAACO,IAAI,CAAC,sBAAsB,EAAC;EAACJ,cAAc,EAAC,SAAAA,CAASK,CAAC,EAAC;IAACL,cAAc,GAACK,CAAC;EAAA,CAAC;EAACJ,YAAY,EAAC,SAAAA,CAASI,CAAC,EAAC;IAACJ,YAAY,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAKpV;AACA;AACA;AACA;AACAR,MAAA,CAAAS,UAAA,CAAAP,QAAQ,GAAG,IAAIC,cAAc,CAAC,EAAAO,gBAAA,GAAAC,MAAM,CAACC,QAAQ,cAAAF,gBAAA,wBAAAG,qBAAA,GAAfH,gBAAA,CAAiBI,MAAM,cAAAD,qBAAA,wBAAAE,sBAAA,GAAvBF,qBAAA,CAAyBG,QAAQ,cAAAD,sBAAA,uBAAjCA,sBAAA,CAAmCE,QAAQ,KAAI,CAAC,CAAC,CAAC;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACAN,MAAM,CAACO,KAAK,GAAGhB,QAAQ,CAACgB,KAAK,C;;;;;;;;;;;ACjB7B,IAAIC,mBAAmB;AAACnB,MAAM,CAACO,IAAI,CAAC,4BAA4B,EAAC;EAACF,OAAO,EAAC,SAAAA,CAASG,CAAC,EAAC;IAACW,mBAAmB,GAACX,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIY,aAAa;AAACpB,MAAM,CAACO,IAAI,CAAC,sCAAsC,EAAC;EAACF,OAAO,EAAC,SAAAA,CAASG,CAAC,EAAC;IAACY,aAAa,GAACZ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIa,cAAc;AAACrB,MAAM,CAACO,IAAI,CAAC,sCAAsC,EAAC;EAACF,OAAO,EAAC,SAAAA,CAASG,CAAC,EAAC;IAACa,cAAc,GAACb,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhVR,MAAM,CAACC,MAAM,CAAC;EAACE,cAAc,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,cAAc;EAAA,CAAC;EAACC,YAAY,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,YAAY;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIkB,cAAc;AAACtB,MAAM,CAACO,IAAI,CAAC,sBAAsB,EAAC;EAACe,cAAc,EAAC,SAAAA,CAASd,CAAC,EAAC;IAACc,cAAc,GAACd,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAa1ML,cAAc,0BAAAoB,eAAA;EACzB,SAAApB,eAAYqB,OAAO,EAAE;IAAA,IAAAC,KAAA;IACnBA,KAAA,GAAAF,eAAA,CAAAG,IAAA,OAAMF,OAAO,CAAC;IAEdC,KAAA,CAAKE,UAAU,GAAG,IAAIC,WAAW,CAAC,KAAK,CAAC;IACxCH,KAAA,CAAKI,WAAW,GAAG,IAAID,WAAW,CAAC,KAAK,CAAC;IAEzCH,KAAA,CAAKK,oBAAoB,GACvBL,KAAA,CAAKM,UAAU,CAACC,SAAS,CAAC,kCAAkC,CAAC;IAE/DP,KAAA,CAAKQ,uBAAuB,GAAG,EAAE;IACjCR,KAAA,CAAKS,yBAAyB,GAAG,IAAI;IAErCT,KAAA,CAAKU,SAAS,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;IACrCb,KAAA,CAAKc,gBAAgB,CAAC,CAAC;IAEvBd,KAAA,CAAKe,mBAAmB,CAAC,CAAC;;IAE1B;IACAf,KAAA,CAAKgB,iBAAiB,CAAC,CAAC;;IAExB;IACAhB,KAAA,CAAKiB,WAAW,GAAG,CAAC,CAAC;;IAErB;IACA;IACAjB,KAAA,CAAKkB,qBAAqB,GAAG,KAAK;IAAC,OAAAlB,KAAA;EACrC;EAACJ,cAAA,CAAAlB,cAAA,EAAAoB,eAAA;EAAA,IAAAqB,MAAA,GAAAzC,cAAA,CAAA0C,SAAA;EAAAD,MAAA,CAEDJ,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAAChB,OAAO,EAAE;MAAA,IAAAd,gBAAA,EAAAG,qBAAA,EAAAE,sBAAA,EAAA+B,sBAAA;MAC3B;MACA,IAAI,CAACC,eAAe,GAAI,CAAAvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB,aAAa,MAAK,SAAS,IAAI,EAAAtC,gBAAA,GAAAC,MAAM,CAACC,QAAQ,cAAAF,gBAAA,wBAAAG,qBAAA,GAAfH,gBAAA,CAAiBI,MAAM,cAAAD,qBAAA,wBAAAE,sBAAA,GAAvBF,qBAAA,CAAyBG,QAAQ,cAAAD,sBAAA,wBAAA+B,sBAAA,GAAjC/B,sBAAA,CAAmCE,QAAQ,cAAA6B,sBAAA,uBAA3CA,sBAAA,CAA6CE,aAAa,MAAK,SAAS,GAAIZ,MAAM,CAACa,cAAc,GAAGtC,MAAM,CAACuC,aAAa;IAC1L;IAAC,OAHDV,mBAAmB;EAAA;EAAAI,MAAA,CAKnBO,MAAM;IAAN,SAAAA,MAAMA,CAAC3B,OAAO,EAAE;MACdD,eAAA,CAAAsB,SAAA,CAAMM,MAAM,CAAAzB,IAAA,OAACF,OAAO;MAEpB,IAAI,CAACgB,mBAAmB,CAAChB,OAAO,CAAC;IACnC;IAAC,OAJD2B,MAAM;EAAA,IAMN;EACA;EACA;EAEA;EAAA;EAAAP,MAAA,CACAQ,MAAM;IAAN,SAAAA,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAACrB,UAAU,CAACqB,MAAM,CAAC,CAAC;IACjC;IAAC,OAFDA,MAAM;EAAA,IAIN;EACA;EACA;EAAA;EAAAR,MAAA,CACAS,aAAa;IAAb,SAAAA,aAAaA,CAACC,CAAC,EAAE;MACf,IAAI,CAAC3B,UAAU,CAAC4B,GAAG,CAACD,CAAC,CAAC;IACxB;IAAC,OAFDD,aAAa;EAAA;EAIb;AACF;AACA;AACA;EAHE;EAAAT,MAAA,CAIAY,SAAS;IAAT,SAAAA,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAAC7B,UAAU,CAAC8B,GAAG,CAAC,CAAC;IAC9B;IAAC,OAFDD,SAAS;EAAA;EAIT;AACF;AACA;AACA;EAHE;EAAAZ,MAAA,CAIAc,UAAU;IAAV,SAAAA,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC7B,WAAW,CAAC4B,GAAG,CAAC,CAAC;IAC/B;IAAC,OAFDC,UAAU;EAAA;EAIV;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;EAAAd,MAAA,CAQAe,2BAA2B;IAA3B,SAAAA,2BAA2BA,CAACC,QAAQ,EAAEC,IAAI,EAAE;MAC1C,IAAI,IAAI,CAACnB,WAAW,CAACkB,QAAQ,CAAC,EAAE;QAC9B,MAAM,IAAIE,KAAK,CAAIF,QAAQ,8BAA2B,CAAC;MACzD;MACA,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAAC,GAAGC,IAAI;IACnC;IAAC,OALDF,2BAA2B;EAAA;EAO3B;AACF;AACA;AACA;AACA;AACA;AACA;EANE;EAAAf,MAAA,CAOAmB,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAACH,QAAQ,EAAe;MACvC,IAAI,CAAC,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAAC,EAAE;QAC/B,MAAM,IAAIE,KAAK,CAAIF,QAAQ,qBAAkB,CAAC;MAChD;MAAC,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAH4BC,QAAQ,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAARF,QAAQ,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAIrC,OAAO,IAAI,CAAC3B,WAAW,CAACkB,QAAQ,CAAC,CAACU,KAAK,CAAC,IAAI,EAAEH,QAAQ,CAAC;IACzD;IAAC,OALDJ,iBAAiB;EAAA;EAOjB;AACF;AACA;AACA;AACA;AACA;AACA;EANE;EAAAnB,MAAA,CAOA2B,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAACX,QAAQ,EAAEO,QAAQ,EAAE;MACrC,IAAI,CAAC,IAAI,CAACzB,WAAW,CAACkB,QAAQ,CAAC,EAAE;QAC/B,MAAM,IAAIE,KAAK,CAAIF,QAAQ,qBAAkB,CAAC;MAChD;MACA,OAAO,IAAI,CAAClB,WAAW,CAACkB,QAAQ,CAAC,CAACU,KAAK,CAAC,IAAI,EAAEH,QAAQ,CAAC;IACzD;IAAC,OALDI,kBAAkB;EAAA;EAOlB;AACF;AACA;AACA;AACA;EAJE;EAAA3B,MAAA,CAKA4B,MAAM;IAAN,SAAAA,MAAMA,CAACC,QAAQ,EAAE;MAAA,IAAAC,MAAA;MACf,IAAI,CAAC7C,WAAW,CAAC0B,GAAG,CAAC,IAAI,CAAC;MAE1B,IAAI,CAACxB,UAAU,CAAC4C,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE;QACvC;QACAC,IAAI,EAAE;MACR,CAAC,CAAC,CACCC,IAAI,CAAC,UAACC,MAAM,EAAK;QAChBJ,MAAI,CAAC7C,WAAW,CAAC0B,GAAG,CAAC,KAAK,CAAC;QAC3BmB,MAAI,CAAC/B,qBAAqB,GAAG,KAAK;QAClC+B,MAAI,CAACK,mBAAmB,CAAC,CAAC;QAC1BN,QAAQ,IAAIA,QAAQ,CAAC,CAAC;MACxB,CAAC,CAAC,CACDO,KAAK,CAAC,UAACC,CAAC,EAAK;QACZP,MAAI,CAAC7C,WAAW,CAAC0B,GAAG,CAAC,KAAK,CAAC;QAC3BkB,QAAQ,IAAIA,QAAQ,CAACQ,CAAC,CAAC;MACzB,CAAC,CAAC;IACN;IAAC,OAjBDT,MAAM;EAAA;EAmBN;AACF;AACA;AACA;AACA;EAJE;EAAA5B,MAAA,CAKAsC,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAACT,QAAQ,EAAE;MAAA,IAAAU,MAAA;MAC3B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACpD,UAAU,CAACuC,KAAK,CACnB,aAAa,EACb,EAAE,EACF;QAAEM,IAAI,EAAE;MAAK,CAAC,EACd,UAACQ,GAAG,EAAEN,MAAM,EAAK;QACf,IAAI,CAAEM,GAAG,EAAE;UACTD,MAAI,CAACE,gBAAgB,CACnBF,MAAI,CAAC/B,MAAM,CAAC,CAAC,EACb0B,MAAM,CAACQ,KAAK,EACZR,MAAM,CAACS,YACT,CAAC;QACH;MACF,CACF,CAAC;MAED,IAAI,CAACxD,UAAU,CAACuC,KAAK,CACnB,mBAAmB,EACnB,EAAE,EACF;QAAEM,IAAI,EAAE;MAAK,CAAC,EACd,UAAAQ,GAAG;QAAA,OAAIX,QAAQ,IAAIA,QAAQ,CAACW,GAAG,CAAC;MAAA,CAClC,CAAC;IACH;IAAC,OAtCDF,kBAAkB;EAAA,IAwClB;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAAtC,MAAA,CACA4C,eAAe;IAAf,SAAAA,eAAeA,CAAChE,OAAO,EAAE;MAAA,IAAAiE,MAAA;MACvBjE,OAAO,GAAAJ,aAAA;QACLsE,UAAU,EAAE,OAAO;QACnBC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC;QACrBC,kBAAkB,EAAE;MAAK,GACtBpE,OAAO,CACX;;MAED;MACA;MACA,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAACqE,OAAO,CAAC,UAAAC,CAAC,EAAI;QAC9C,IAAI,CAACtE,OAAO,CAACsE,CAAC,CAAC,EACbtE,OAAO,CAACsE,CAAC,CAAC,GAAG;UAAA,OAAM,IAAI;QAAA;MAC3B,CAAC,CAAC;MAEF,IAAIC,MAAM;MACV;MACA,IAAMC,cAAc,GAAG,SAAAA,CAAAC,IAAA,EAA6B;QAAA,IAA1BC,KAAK,GAAAD,IAAA,CAALC,KAAK;UAAEC,YAAY,GAAAF,IAAA,CAAZE,YAAY;QAC3C,IAAI,CAACJ,MAAM,EAAE;UACXA,MAAM,GAAG,IAAI;UACb,IAAI,CAACG,KAAK,EAAE;YACVT,MAAI,CAACW,YAAY,CAACP,OAAO,CAAC,UAAApB,QAAQ,EAAI;cACpCA,QAAQ,CAAC0B,YAAY,CAAC;cACtB,OAAO,IAAI;YACb,CAAC,CAAC;YACFV,MAAI,CAAC9C,qBAAqB,GAAG,IAAI;UACnC,CAAC,MAAM;YACL8C,MAAI,CAAC9C,qBAAqB,GAAG,KAAK;YAClC8C,MAAI,CAACY,mBAAmB,CAACR,OAAO,CAAC,UAAApB,QAAQ,EAAI;cAC3CA,QAAQ,CAAC;gBAAEyB,KAAK,EAALA;cAAM,CAAC,CAAC;cACnB,OAAO,IAAI;YACb,CAAC,CAAC;UACJ;UACA1E,OAAO,CAAC8E,YAAY,CAACJ,KAAK,EAAEC,YAAY,CAAC;QAC3C;MACF,CAAC;MAED,IAAII,WAAW,GAAG,KAAK;;MAEvB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAMC,gBAAgB,GAAG,SAAAA,CAACpB,GAAG,EAAEN,MAAM,EAAK;QACxC,IAAIM,GAAG,IAAI,CAACN,MAAM,IAAI,CAACA,MAAM,CAACQ,KAAK,EAAE;UACnC;UACA;UACA;QAAA,CACD,MAAM;UACL;UACA;UACA;UACA,IAAIG,MAAI,CAACgB,iBAAiB,EAAE;YAC1BhB,MAAI,CAACgB,iBAAiB,CAACC,IAAI,CAAC,CAAC;UAC/B;UAEAjB,MAAI,CAACgB,iBAAiB,GAAGE,GAAG,CAACC,WAAW,CAAC,UAAAC,IAAI,EAAI;YAC/C,IAAIA,IAAI,IAAIpB,MAAI,CAAC1D,UAAU,EAAE;cAC3B;YACF;YACAwE,WAAW,GAAG,IAAI;YAClB;YACA,IAAMO,WAAW,GAAGrB,MAAI,CAACsB,iBAAiB,CAAC,CAAC;YAC5C,IAAID,WAAW,EAAE;cACfhC,MAAM,GAAG;gBACPQ,KAAK,EAAEwB,WAAW;gBAClBvB,YAAY,EAAEE,MAAI,CAACuB,wBAAwB,CAAC;cAC9C,CAAC;YACH;YACA,IAAI,CAAClC,MAAM,CAACS,YAAY,EACtBT,MAAM,CAACS,YAAY,GAAGE,MAAI,CAACwB,gBAAgB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;YACzD,IAAIzB,MAAI,CAAC0B,iBAAiB,CAACrC,MAAM,CAACS,YAAY,CAAC,EAAE;cAC/CE,MAAI,CAACV,mBAAmB,CAAC,CAAC;YAC5B,CAAC,MAAM;cACLU,MAAI,CAACD,eAAe,CAAC;gBACnBG,eAAe,EAAE,CAAC;kBAACyB,MAAM,EAAEtC,MAAM,CAACQ;gBAAK,CAAC,CAAC;gBACzC;gBACA;gBACA;gBACAM,kBAAkB,EAAE,IAAI;gBACxBU,YAAY,EAAE,SAAAA,CAACJ,KAAK,EAAEC,YAAY,EAAK;kBACrC,IAAMkB,cAAc,GAAG5B,MAAI,CAACsB,iBAAiB,CAAC,CAAC;kBAC/C,IAAIb,KAAK,EAAE;oBACT;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,IAAImB,cAAc,IAAIA,cAAc,KAAKvC,MAAM,CAACQ,KAAK,EAAE;sBACrDG,MAAI,CAACV,mBAAmB,CAAC,CAAC;oBAC5B;kBACF;kBACA;kBACA;kBACA;kBACAiB,cAAc,CAAC;oBAAEE,KAAK,EAALA,KAAK;oBAAEC,YAAY,EAAZA;kBAAa,CAAC,CAAC;gBACzC;cAAC,CAAC,CAAC;YACP;UACF,CAAC,CAAC;QACJ;MACF,CAAC;;MAED;MACA;MACA;MACA,IAAMmB,4BAA4B,GAAG,SAAAA,CAACpB,KAAK,EAAEpB,MAAM,EAAK;QACtD;QACA;QACA;QACA;QACA;QACA,IAAIyB,WAAW,EACb;;QAEF;QACA;QACA;QACA,IAAIL,KAAK,IAAI,CAACpB,MAAM,EAAE;UACpBoB,KAAK,GAAGA,KAAK,IAAI,IAAIpC,KAAK,6BACEtC,OAAO,CAACkE,UACpC,CAAC;UACDM,cAAc,CAAC;YAAEE,KAAK,EAALA;UAAM,CAAC,CAAC;UACzBT,MAAI,CAACpC,aAAa,CAAC,KAAK,CAAC;UACzB;QACF;QACA,IAAI;UACF7B,OAAO,CAAC+F,cAAc,CAACzC,MAAM,CAAC;QAChC,CAAC,CAAC,OAAOG,CAAC,EAAE;UACVe,cAAc,CAAC;YAAEE,KAAK,EAAEjB;UAAE,CAAC,CAAC;UAC5BQ,MAAI,CAACpC,aAAa,CAAC,KAAK,CAAC;UACzB;QACF;;QAEA;QACAoC,MAAI,CAAC+B,kBAAkB,CAAC1C,MAAM,CAAC2C,EAAE,EAAE3C,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,YAAY,CAAC;;QAErE;QACAmC,OAAO,CAACC,OAAO;UAAC,SAAAC,QAAOC,WAAW;YAAA,IAAAC,IAAA;YAAA,OAAA3G,mBAAA,CAAA4G,KAAA;cAAA,SAAAC,SAAAC,QAAA;gBAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;kBAAA;oBAAAF,QAAA,CAAAE,IAAA;oBAAA,OAAAhH,mBAAA,CAAAiH,KAAA,CACbV,OAAO,CAACW,eAAe,CAACR,WAAW,EAAE;sBAAA,OACtDlH,MAAM,CAAC2H,SAAS,CAAC,CAAC;oBAAA,CACpB,CAAC;kBAAA;oBAFKR,IAAI,GAAAG,QAAA,CAAAM,IAAA;oBAIV,IAAIT,IAAI,EAAE;sBACR9B,cAAc,CAAC;wBAAEG,YAAY,EAAErB;sBAAO,CAAC,CAAC;sBACxCW,MAAI,CAACpC,aAAa,CAAC,KAAK,CAAC;sBACzBwE,WAAW,CAACnB,IAAI,CAAC,CAAC;oBACpB;kBAAC;kBAAA;oBAAA,OAAAuB,QAAA,CAAAvB,IAAA;gBAAA;cAAA;cAAA,OAAAsB,QAAA;YAAA,uBAAAQ,OAAA;UAAA;UACF,OAAAZ,OAAA;QAAA,IAAC;MAEJ,CAAC;MAED,IAAI,CAACpG,OAAO,CAACoE,kBAAkB,EAAE;QAC/B,IAAI,CAACvC,aAAa,CAAC,IAAI,CAAC;MAC1B;MACA,IAAI,CAACtB,UAAU,CAAC4C,UAAU,CACxBnD,OAAO,CAACkE,UAAU,EAClBlE,OAAO,CAACmE,eAAe,EACvB;QAAEf,IAAI,EAAE,IAAI;QAAE4B,gBAAgB,EAAhBA;MAAiB,CAAC,EAChCc,4BAA4B,CAAC;IACjC;IAAC,OArLD9B,eAAe;EAAA;EAAA5C,MAAA,CAuLfmC,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAAA,EAAG;MACpB;MACA,IAAI,IAAI,CAAChD,UAAU,CAAC0G,OAAO,EAAE;QAC3B,IAAI,CAACC,aAAa,CAACC,IAAI,CAAC,UAAAlE,QAAQ,EAAI;UAClCA,QAAQ,CAAC,CAAC;UACV,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;MACA,IAAI,CAACmE,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC7G,UAAU,CAAC8G,SAAS,CAAC,IAAI,CAAC;MAC/B,IAAI,CAACpC,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAC,CAAC;IACzD;IAAC,OAXD3B,mBAAmB;EAAA;EAAAnC,MAAA,CAanB4E,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAACpE,MAAM,EAAEkC,KAAK,EAAEC,YAAY,EAAE;MAC9C,IAAI,CAACF,gBAAgB,CAACjC,MAAM,EAAEkC,KAAK,EAAEC,YAAY,CAAC;MAClD,IAAI,CAACxD,UAAU,CAAC8G,SAAS,CAACzF,MAAM,CAAC;IACnC;IAAC,OAHDoE,kBAAkB;EAAA,IAKlB;EACA;EACA;EAEA;EACA;EACA;EACA;EAAA;EAAA5E,MAAA,CACAkG,uBAAuB;IAAvB,SAAAA,uBAAuBA,CAAA,EAAG;MACxB,OAAO,IAAI,CAAChH,oBAAoB,CAACiH,KAAK,CAAC,CAAC;IAC1C;IAAC,OAFDD,uBAAuB;EAAA;EAIvB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAAAlG,MAAA,CACAoG,eAAe;IAAf,SAAAA,eAAeA,CAAClD,CAAC,EAAE;MACjB,IAAI,IAAI,CAAC5D,yBAAyB,EAAE;QAClC4D,CAAC,CAAC,IAAI,CAAC5D,yBAAyB,CAAC;MACnC,CAAC,MAAM;QACL,IAAI,CAACD,uBAAuB,CAACgH,IAAI,CAACnD,CAAC,CAAC;MACtC;IACF;IAAC,OANDkD,eAAe;EAAA;EAQf;EACA;EACA;EACA;EAAApG,MAAA,CACAsG,cAAc;IAAd,SAAAA,cAAcA,CAACC,WAAW,EAAE;MAC1B,IAAI,IAAI,CAACjH,yBAAyB,EAAE;QAClCvB,MAAM,CAACyI,MAAM,CACX,4DACF,CAAC;QACD;MACF;MAEA,IAAI,CAACnH,uBAAuB,CAAC4D,OAAO,CAAC,UAAApB,QAAQ;QAAA,OAAIA,QAAQ,CAAC0E,WAAW,CAAC;MAAA,EAAC;MACvE,IAAI,CAAClH,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACC,yBAAyB,GAAGiH,WAAW;IAC9C;IAAC,OAXDD,cAAc;EAAA,IAad;EACA;EACA;EACA;EAAA;EAAAtG,MAAA,CACAyG,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAAC5E,QAAQ,EAAE;MACzB;MACA,IAAI,IAAI,CAAC9B,qBAAqB,EAAE;QAC9B;QACA;QACA;QACA;QACAhC,MAAM,CAAC2I,UAAU,CAAC;UAAA,OAAM7E,QAAQ,CAAC;YAAE8E,IAAI,EAAE;UAAS,CAAC,CAAC;QAAA,GAAE,CAAC,CAAC;MAC1D;IACF;IAAC,OATDF,gBAAgB;EAAA,IAWhB;EACA;EACA;EAEA;EACA;EACA;EACA;EAAA;EAAAzG,MAAA,CAEA4G,cAAc;IAAd,SAAAA,cAAcA,CAAClE,KAAK,EAAEb,QAAQ,EAAE;MAC9B,IAAI,CAACe,eAAe,CAAC;QACnBG,eAAe,EAAE,CAAC;UAChByB,MAAM,EAAE9B;QACV,CAAC,CAAC;QACFgB,YAAY,EAAE7B;MAChB,CAAC,CAAC;IACJ;IAAC,OAPD+E,cAAc;EAAA;EASd;EACA;EAAA5G,MAAA,CACA6G,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC9B;IAAC,OAHDF,gBAAgB;EAAA;EAKhB;EACA;EACA;EAEA;EACA;EACA;EAAA7G,MAAA,CACAgH,yBAAyB;IAAzB,SAAAA,yBAAyBA,CAAA,EAAG;MAC1B,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,GAAGC,MAAM,CAACrC,EAAE,CAAC,CAAC;MACzD,IAAI,CAACsC,WAAW,GAAG,IAAI,CAACA,WAAW,GAAGD,MAAM,CAACrC,EAAE,CAAC,CAAC;IACnD;IAAC,OAHDmC,yBAAyB;EAAA;EAAAhH,MAAA,CAKzByC,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAACjC,MAAM,EAAEkC,KAAK,EAAEC,YAAY,EAAE;MAC5C,IAAI,CAACxC,eAAe,CAACiH,OAAO,CAAC,IAAI,CAACD,WAAW,EAAE3G,MAAM,CAAC;MACtD,IAAI,CAACL,eAAe,CAACiH,OAAO,CAAC,IAAI,CAACH,eAAe,EAAEvE,KAAK,CAAC;MACzD,IAAI,CAAEC,YAAY,EAChBA,YAAY,GAAG,IAAI,CAAC0B,gBAAgB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MAClD,IAAI,CAACnE,eAAe,CAACiH,OAAO,CAAC,IAAI,CAACC,uBAAuB,EAAE1E,YAAY,CAAC;;MAExE;MACA;MACA,IAAI,CAAC2E,yBAAyB,GAAG5E,KAAK;IACxC;IAAC,OAVDD,gBAAgB;EAAA;EAAAzC,MAAA,CAYhBgG,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAC7F,eAAe,CAACoH,UAAU,CAAC,IAAI,CAACJ,WAAW,CAAC;MACjD,IAAI,CAAChH,eAAe,CAACoH,UAAU,CAAC,IAAI,CAACN,eAAe,CAAC;MACrD,IAAI,CAAC9G,eAAe,CAACoH,UAAU,CAAC,IAAI,CAACF,uBAAuB,CAAC;;MAE7D;MACA;MACA,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACvC;IAAC,OARDtB,kBAAkB;EAAA;EAUlB;EACA;EAAAhG,MAAA,CACAmE,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAChE,eAAe,CAACqH,OAAO,CAAC,IAAI,CAACP,eAAe,CAAC;IAC3D;IAAC,OAFD9C,iBAAiB;EAAA;EAAAnE,MAAA,CAIjBoE,wBAAwB;IAAxB,SAAAA,wBAAwBA,CAAA,EAAG;MACzB,OAAO,IAAI,CAACjE,eAAe,CAACqH,OAAO,CAAC,IAAI,CAACH,uBAAuB,CAAC;IACnE;IAAC,OAFDjD,wBAAwB;EAAA;EAAApE,MAAA,CAIxByH,aAAa;IAAb,SAAAA,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAACtH,eAAe,CAACqH,OAAO,CAAC,IAAI,CAACL,WAAW,CAAC;IACvD;IAAC,OAFDM,aAAa;EAAA;EAAAzH,MAAA,CAIb0H,+BAA+B;IAA/B,SAAAA,+BAA+BA,CAAA,EAAG;MAChC,IAAM/E,YAAY,GAAG,IAAI,CAACyB,wBAAwB,CAAC,CAAC;MACpD,IAAIzB,YAAY,IAAI,IAAI,CAAC4B,iBAAiB,CAAC,IAAID,IAAI,CAAC3B,YAAY,CAAC,CAAC,EAAE;QAClE,IAAI,CAACqD,kBAAkB,CAAC,CAAC;MAC3B;IACF;IAAC,OALD0B,+BAA+B;EAAA;EAO/B;EACA;EACA;EAAA1H,MAAA,CAEAH,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAAA,EAAG;MAAA,IAAA8H,MAAA;MAClB;MACA,IAAI,CAACV,eAAe,GAAG,mBAAmB;MAC1C,IAAI,CAACI,uBAAuB,GAAG,0BAA0B;MACzD,IAAI,CAACF,WAAW,GAAG,eAAe;MAElC,IAAMS,iBAAiB,GAAGC,yBAAyB,CAACC,oBAAoB;MACxE,IAAIF,iBAAiB,IAAI,IAAI,CAACzI,UAAU,KAAKpB,MAAM,CAACoB,UAAU,EAAE;QAC9D;QACA;QACA;QACA;QACA;QACA;QACA,IAAI4I,SAAS,SAAO,IAAI,CAAC5I,UAAU,CAAC6I,OAAO,CAACC,MAAQ;QACpD,IAAIL,iBAAiB,EAAE;UACrBG,SAAS,UAAQH,iBAAmB;QACtC;QACA,IAAI,CAACX,eAAe,IAAIc,SAAS;QACjC,IAAI,CAACV,uBAAuB,IAAIU,SAAS;QACzC,IAAI,CAACZ,WAAW,IAAIY,SAAS;MAC/B;MAEA,IAAIrF,KAAK;MACT,IAAI,IAAI,CAACoE,iBAAiB,EAAE;QAC1B;QACA;QACA,IAAI,CAACY,+BAA+B,CAAC,CAAC;QACtChF,KAAK,GAAG,IAAI,CAACyB,iBAAiB,CAAC,CAAC;QAChC,IAAIzB,KAAK,EAAE;UACT;UACA;UACA,IAAMlC,MAAM,GAAG,IAAI,CAACiH,aAAa,CAAC,CAAC;UACnCjH,MAAM,IAAI,IAAI,CAACrB,UAAU,CAAC8G,SAAS,CAACzF,MAAM,CAAC;UAC3C,IAAI,CAACoG,cAAc,CAAClE,KAAK,EAAE,UAAAF,GAAG,EAAI;YAChC,IAAIA,GAAG,EAAE;cACPzE,MAAM,CAACyI,MAAM,mCAAiChE,GAAK,CAAC;cACpDmF,MAAI,CAACxF,mBAAmB,CAAC,CAAC;YAC5B;YAEAwF,MAAI,CAACrB,cAAc,CAAC;cAClBK,IAAI,EAAE,QAAQ;cACduB,OAAO,EAAE,CAAC1F,GAAG;cACbc,KAAK,EAAEd,GAAG;cACVM,UAAU,EAAE,OAAO;cACnB;cACA;cACA;cACAC,eAAe,EAAE,CAAC;gBAACyB,MAAM,EAAE9B;cAAK,CAAC;YACnC,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;;MAEA;MACA;MACA,IAAI,CAAC4E,yBAAyB,GAAG5E,KAAK;MAEtC,IAAI,IAAI,CAACyF,kBAAkB,EAAE;QAC3B;QACA;QACAC,aAAa,CAAC,IAAI,CAACD,kBAAkB,CAAC;MACxC;MAEA,IAAI,CAACA,kBAAkB,GAAGE,WAAW,CAAC,YAAM;QAC1CV,MAAI,CAACZ,qBAAqB,CAAC,CAAC;MAC9B,CAAC,EAAE,IAAI,CAAC;IACV;IAAC,OAnEDlH,iBAAiB;EAAA;EAAAG,MAAA,CAqEjB+G,qBAAqB;IAArB,SAAAA,qBAAqBA,CAAA,EAAG;MAAA,IAAAuB,MAAA;MACtB,IAAI,CAAE,IAAI,CAACxB,iBAAiB,EAAE;QAC5B;MACF;MAEA,IAAMyB,iBAAiB,GAAG,IAAI,CAACpE,iBAAiB,CAAC,CAAC;;MAElD;MACA,IAAI,IAAI,CAACmD,yBAAyB,IAAIiB,iBAAiB,EAAE;QACvD,IAAIA,iBAAiB,EAAE;UACrB,IAAI,CAAC3B,cAAc,CAAC2B,iBAAiB,EAAE,UAAC/F,GAAG,EAAK;YAC9C,IAAIA,GAAG,EAAE;cACP8F,MAAI,CAACnG,mBAAmB,CAAC,CAAC;YAC5B;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACP,MAAM,CAAC,CAAC;QACf;MACF;MAEA,IAAI,CAAC0F,yBAAyB,GAAGiB,iBAAiB;IACpD;IAAC,OArBDxB,qBAAqB;EAAA;EAuBrB;EACA;EACA;EAAA/G,MAAA,CAEAL,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAAA,EAAG;MACjB;MACA,IAAI,CAACmH,iBAAiB,GAAG,IAAI;;MAE7B;MACA,IAAI,CAAC0B,kBAAkB,GAAG,CAAC,CAAC;;MAE5B;MACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC5B;IAAC,OATD9I,gBAAgB;EAAA;EAWhB;EAAAK,MAAA,CACAyI,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAAA,EAAG;MACpBC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAACnJ,SAAS,EAAEoJ,qBAAqB,CAAC;IACjE;IAAC,OAFDF,mBAAmB;EAAA;EAInB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAhBEzI,MAAA,CAiBA4I,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAAC/G,QAAQ,EAAE;MAC5B,IAAI,IAAI,CAAC2G,kBAAkB,CAAC,gBAAgB,CAAC,EAAE;QAC7CzK,MAAM,CAACyI,MAAM,CAAC,0DAA0D,GACtE,2CAA2C,CAAC;MAChD;MAEA,IAAI,CAACgC,kBAAkB,CAAC,gBAAgB,CAAC,GAAG3G,QAAQ;IACtD;IAAC,OAPD+G,mBAAmB;EAAA;EASnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAjBE5I,MAAA,CAkBA6I,uBAAuB;IAAvB,SAAAA,uBAAuBA,CAAChH,QAAQ,EAAE;MAChC,IAAI,IAAI,CAAC2G,kBAAkB,CAAC,cAAc,CAAC,EAAE;QAC3CzK,MAAM,CAACyI,MAAM,CAAC,8DAA8D,GAC1E,2CAA2C,CAAC;MAChD;MAEA,IAAI,CAACgC,kBAAkB,CAAC,cAAc,CAAC,GAAG3G,QAAQ;IACpD;IAAC,OAPDgH,uBAAuB;EAAA;EASvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAjBE7I,MAAA,CAkBA8I,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAACjH,QAAQ,EAAE;MACzB,IAAI,IAAI,CAAC2G,kBAAkB,CAAC,gBAAgB,CAAC,EAAE;QAC7CzK,MAAM,CAACyI,MAAM,CAAC,uDAAuD,GACnE,2CAA2C,CAAC;MAChD;MAEA,IAAI,CAACgC,kBAAkB,CAAC,gBAAgB,CAAC,GAAG3G,QAAQ;IACtD;IAAC,OAPDiH,gBAAgB;EAAA;EAAA,OAAAvL,cAAA;AAAA,EA1uBkBmB,cAAc;AAqvBlD;AACA;AACA;AACA;AACA;AACA;AACA;AACAX,MAAM,CAAC6C,SAAS,GAAG;EAAA,OAAMtD,QAAQ,CAACsD,SAAS,CAAC,CAAC;AAAA;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA7C,MAAM,CAAC+C,UAAU,GAAG;EAAA,OAAMxD,QAAQ,CAACwD,UAAU,CAAC,CAAC;AAAA;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA/C,MAAM,CAAC6D,MAAM,GAAG,UAAAC,QAAQ;EAAA,OAAIvE,QAAQ,CAACsE,MAAM,CAACC,QAAQ,CAAC;AAAA;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA9D,MAAM,CAACuE,kBAAkB,GAAG,UAAAT,QAAQ;EAAA,OAAIvE,QAAQ,CAACgF,kBAAkB,CAACT,QAAQ,CAAC;AAAA;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9D,MAAM,CAAC6I,cAAc,GAAG,UAAClE,KAAK,EAAEb,QAAQ;EAAA,OACtCvE,QAAQ,CAACsJ,cAAc,CAAClE,KAAK,EAAEb,QAAQ,CAAC;AAAA;;AAE1C;AACA;AACA;;AAEA;AACA;AACA,IAAIkH,OAAO,CAACC,KAAK,EAAE;EACjB,IAAQC,QAAQ,GAAKF,OAAO,CAACC,KAAK,CAACE,KAAK,CAAhCD,QAAQ;;EAEhB;AACF;AACA;AACA;AACA;AACA;EACEA,QAAQ,CAACE,cAAc,CAAC,aAAa,EAAE;IAAA,OAAMpL,MAAM,CAACmH,IAAI,CAAC,CAAC;EAAA,EAAC;;EAE3D;EACA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE+D,QAAQ,CAACE,cAAc,CAAC,WAAW,EAAE;IAAA,OAAMpL,MAAM,CAAC6C,SAAS,CAAC,CAAC;EAAA,EAAC;;EAE9D;AACF;AACA;AACA;AACA;AACA;EACEqI,QAAQ,CAACE,cAAc,CAAC,YAAY,EAAE;IAAA,OAAMpL,MAAM,CAAC+C,UAAU,CAAC,CAAC;EAAA,EAAC;;EAEhE;AACF;AACA;AACA;AACA;AACA;EACEmI,QAAQ,CAACE,cAAc,CACrB,gBAAgB,EAChB;IAAA,OAAMpL,MAAM,CAAC6C,SAAS,CAAC,CAAC,IAAI7C,MAAM,CAAC+C,UAAU,CAAC,CAAC;EAAA,CACjD,CAAC;AACH;AAEA,IAAM6H,qBAAqB,GAAG,SAAAA,CAASjG,KAAK,EAAE0G,OAAO,EAAE;EAAA,IAAAC,MAAA;EACrD;EACA,IAAI,CAACvC,iBAAiB,GAAG,KAAK;;EAE9B;EACA/I,MAAM,CAACuL,OAAO,CAAC,YAAM;IACnB;IACA,IAAID,MAAI,CAACb,kBAAkB,CAACY,OAAO,CAAC,EAAE;MACpCC,MAAI,CAACb,kBAAkB,CAACY,OAAO,CAAC,CAAC1G,KAAK,EAAE;QAAA,OAAM2G,MAAI,CAACxC,gBAAgB,CAAC,CAAC;MAAA,EAAC;IACxE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA,IAAM6B,kBAAkB,GAAG,SAAAA,CAACrK,QAAQ,EAAEqB,IAAI,EAAE6J,OAAO,EAAK;EACtD;EACA,CAAC,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAACtG,OAAO,CAAC,UAAAmG,OAAO,EAAI;IACtE,IAAI1G,KAAK;IAET,IAAM8G,UAAU,GAAG,IAAIC,MAAM,aAAWL,OAAO,aAAU,CAAC;IAC1D,IAAMM,KAAK,GAAGhK,IAAI,CAACgK,KAAK,CAACF,UAAU,CAAC;IAEpC,IAAIE,KAAK,EAAE;MACThH,KAAK,GAAGgH,KAAK,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIN,OAAO,KAAK,gBAAgB,EAAE;QAChC/K,QAAQ,CAACsL,mBAAmB,GAAGjH,KAAK;MACtC,CAAC,MAAM,IAAI0G,OAAO,KAAK,cAAc,EAAE;QACrC/K,QAAQ,CAACuL,iBAAiB,GAAGlH,KAAK;MACpC,CAAC,MAAM,IAAI0G,OAAO,KAAK,gBAAgB,EAAE;QACvC/K,QAAQ,CAACwL,mBAAmB,GAAGnH,KAAK;MACtC;IACF,CAAC,MAAM;MACL;IACF;;IAEA;IACA;IACA;IACA;IACA;IACAlD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,EAAE;;IAEzB;IACA6J,OAAO,CAACzK,IAAI,CAACT,QAAQ,EAAEqE,KAAK,EAAE0G,OAAO,CAAC;EACxC,CAAC,CAAC;AACJ,CAAC;;AAED;AACO,IAAM5L,YAAY,GAAG;EAC1BkL,kBAAkB,EAAE,SAAAA,CAAChJ,IAAI,EAAE6J,OAAO;IAAA,OAChCb,kBAAkB,CAACpL,QAAQ,EAAEoC,IAAI,EAAE6J,OAAO,CAAC;EAAA;AAC/C,CAAC,C;;;;;;;;;;;ACv5BD,IAAIhL,mBAAmB;AAACnB,MAAM,CAACO,IAAI,CAAC,4BAA4B,EAAC;EAACF,OAAO,EAAC,SAAAA,CAASG,CAAC,EAAC;IAACW,mBAAmB,GAACX,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIY,aAAa;AAACpB,MAAM,CAACO,IAAI,CAAC,sCAAsC,EAAC;EAACF,OAAO,EAAC,SAAAA,CAASG,CAAC,EAAC;IAACY,aAAa,GAACZ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA/NR,MAAM,CAACC,MAAM,CAAC;EAACqB,cAAc,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,cAAc;EAAA,CAAC;EAACoL,yBAAyB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,yBAAyB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI/L,MAAM;AAACX,MAAM,CAACO,IAAI,CAAC,eAAe,EAAC;EAACI,MAAM,EAAC,SAAAA,CAASH,CAAC,EAAC;IAACG,MAAM,GAACH,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAEjN;AACA,IAAMmM,iBAAiB,GAAG,CACxB,uBAAuB,EACvB,6BAA6B,EAC7B,+BAA+B,EAC/B,iBAAiB,EACjB,uBAAuB,EACvB,gBAAgB,EAChB,oCAAoC,EACpC,8BAA8B,EAC9B,qCAAqC,EACrC,+BAA+B,EAC/B,wBAAwB,EACxB,cAAc,EACd,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,YAAY,EACZ,2BAA2B,EAC3B,qBAAqB,EACrB,eAAe,EACf,QAAQ,EACR,YAAY,CACb;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,IAWarL,cAAc;EACzB,SAAAA,eAAYE,OAAO,EAAE;IACnB;IACA,SAAAoL,EAAA,MAAAC,YAAA,GAAkBC,MAAM,CAACC,IAAI,CAACvL,OAAO,CAAC,EAAAoL,EAAA,GAAAC,YAAA,CAAA3I,MAAA,EAAA0I,EAAA,IAAE;MAAnC,IAAMI,GAAG,GAAAH,YAAA,CAAAD,EAAA;MACZ,IAAI,CAACD,iBAAiB,CAACM,QAAQ,CAACD,GAAG,CAAC,EAAE;QACpCE,OAAO,CAAChH,KAAK,oCAAkC8G,GAAK,CAAC;MACvD;IACF;;IAEA;IACA;IACA,IAAI,CAACG,QAAQ,GAAG3L,OAAO,IAAI,CAAC,CAAC;;IAE7B;IACA;IACA,IAAI,CAACO,UAAU,GAAGqL,SAAS;IAC3B,IAAI,CAACC,eAAe,CAAC7L,OAAO,IAAI,CAAC,CAAC,CAAC;;IAEnC;IACA;IACA,IAAI,CAACN,KAAK,GAAG,IAAI,CAACoM,qBAAqB,CAAC9L,OAAO,IAAI,CAAC,CAAC,CAAC;;IAEtD;IACA,IAAI,CAAC4E,YAAY,GAAG,IAAImH,IAAI,CAAC;MAC3BC,eAAe,EAAE,KAAK;MACtBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;IAEF,IAAI,CAACpH,mBAAmB,GAAG,IAAIkH,IAAI,CAAC;MAClCC,eAAe,EAAE,KAAK;MACtBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;IAEF,IAAI,CAAC/E,aAAa,GAAG,IAAI6E,IAAI,CAAC;MAC5BC,eAAe,EAAE,KAAK;MACtBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;;IAE9D;IACA;IACA,IAAMC,OAAO,GAAG,8BAA8B;IAC9C,IAAI,CAACC,mBAAmB,GAAGlN,MAAM,CAACmN,aAAa,CAACF,OAAO,EAAE,UACvDG,WAAW,EACX;MACA,IAAI,CAACC,OAAO,GAAGD,WAAW;IAC5B,CAAC,CAAC;IACF,IAAI,CAACF,mBAAmB,CAAChL,SAAS,CAACoL,IAAI,GAAGL,OAAO;;IAEjD;IACA;IACA;IACA,IAAI,CAACC,mBAAmB,CAACK,YAAY,GAAG,SAAS;EACnD;EAAC,IAAAtL,MAAA,GAAAtB,cAAA,CAAAuB,SAAA;EAAAD,MAAA,CAED0K,qBAAqB;IAArB,SAAAA,qBAAqBA,CAAC9L,OAAO,EAAE;MAC7B,IAAIA,OAAO,CAAC2M,UAAU,IAAI,OAAO3M,OAAO,CAAC2M,UAAU,KAAK,QAAQ,IAAI,EAAE3M,OAAO,CAAC2M,UAAU,YAAYC,KAAK,CAACC,UAAU,CAAC,EAAE;QACrH,MAAM,IAAI1N,MAAM,CAACmD,KAAK,CAAC,uEAAuE,CAAC;MACjG;MAEA,IAAIwK,cAAc,GAAG,OAAO;MAC5B,IAAI,OAAO9M,OAAO,CAAC2M,UAAU,KAAK,QAAQ,EAAE;QAC1CG,cAAc,GAAG9M,OAAO,CAAC2M,UAAU;MACrC;MAEA,IAAIA,UAAU;MACd,IAAI3M,OAAO,CAAC2M,UAAU,YAAYC,KAAK,CAACC,UAAU,EAAE;QAClDF,UAAU,GAAG3M,OAAO,CAAC2M,UAAU;MACjC,CAAC,MAAM;QACLA,UAAU,GAAG,IAAIC,KAAK,CAACC,UAAU,CAACC,cAAc,EAAE;UAChDC,mBAAmB,EAAE,IAAI;UACzBxM,UAAU,EAAE,IAAI,CAACA;QACnB,CAAC,CAAC;MACJ;MAEA,OAAOoM,UAAU;IACnB;IAAC,OArBDb,qBAAqB;EAAA;EAuBrB;AACF;AACA;AACA;EAHE;EAAA1K,MAAA,CAIAQ,MAAM;IAAN,SAAAA,MAAMA,CAAA,EAAG;MACP,MAAM,IAAIU,KAAK,CAAC,+BAA+B,CAAC;IAClD;IAAC,OAFDV,MAAM;EAAA,IAIN;EAAA;EAAAR,MAAA,CACA4L,wBAAwB;IAAxB,SAAAA,wBAAwBA,CAAA,EAAe;MAAA,IAAdhN,OAAO,GAAAyC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAmJ,SAAA,GAAAnJ,SAAA,MAAG,CAAC,CAAC;MACnC;MACA,IAAI,CAAC,IAAI,CAACkJ,QAAQ,CAACsB,oBAAoB,EAAE,OAAOjN,OAAO;;MAEvD;MACA,IAAI,CAACA,OAAO,CAACkN,MAAM,EACjB,OAAAtN,aAAA,CAAAA,aAAA,KACKI,OAAO;QACVkN,MAAM,EAAE,IAAI,CAACvB,QAAQ,CAACsB;MAAoB;;MAG9C;MACA,IAAM1B,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACvL,OAAO,CAACkN,MAAM,CAAC;MACxC,IAAI,CAAC3B,IAAI,CAAC7I,MAAM,EAAE,OAAO1C,OAAO;;MAEhC;MACA;MACA,IAAI,CAAC,CAACA,OAAO,CAACkN,MAAM,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,OAAOvL,OAAO;;MAE7C;MACA;MACA,IAAMmN,KAAK,GAAG7B,MAAM,CAACC,IAAI,CAAC,IAAI,CAACI,QAAQ,CAACsB,oBAAoB,CAAC;MAC7D,OAAO,IAAI,CAACtB,QAAQ,CAACsB,oBAAoB,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,GAC/CnN,OAAO,GAAAJ,aAAA,CAAAA,aAAA,KAEFI,OAAO;QACVkN,MAAM,EAAAtN,aAAA,CAAAA,aAAA,KACDI,OAAO,CAACkN,MAAM,GACd,IAAI,CAACvB,QAAQ,CAACsB,oBAAoB;MACtC,EACF;IACP;IAAC,OA/BDD,wBAAwB;EAAA;EAiCxB;AACF;AACA;AACA;AACA;AACA;EALE;EAAA5L,MAAA,CAMAkF,IAAI;IAAJ,SAAAA,IAAIA,CAACtG,OAAO,EAAE;MACZ,IAAIb,MAAM,CAACiO,QAAQ,EAAE;QACnB1B,OAAO,CAAC2B,IAAI,CAAC,CACX,mDAAmD,EACnD,qDAAqD,EACrD,uCAAuC,CACxC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACf;MAEA,IAAMC,IAAI,GAAG,IAAI;MACjB,IAAM3L,MAAM,GAAG2L,IAAI,CAAC3L,MAAM,CAAC,CAAC;MAC5B,IAAM4L,OAAO,GAAG,SAAAA,CAAA;QAAA,IAAAC,WAAA,EAAAC,YAAA;QAAA,OAAavO,MAAM,CAACwO,QAAQ,GACxC,CAAAF,WAAA,GAAAF,IAAI,CAAC7N,KAAK,EAAC8N,OAAO,CAAA1K,KAAA,CAAA2K,WAAA,EAAAhL,SAAQ,CAAC,GAC3B,CAAAiL,YAAA,GAAAH,IAAI,CAAC7N,KAAK,EAACkO,YAAY,CAAA9K,KAAA,CAAA4K,YAAA,EAAAjL,SAAQ,CAAC;MAAA;MACpC,OAAOb,MAAM,GACT4L,OAAO,CAAC5L,MAAM,EAAE,IAAI,CAACoL,wBAAwB,CAAChN,OAAO,CAAC,CAAC,GACvD,IAAI;IACV;IAAC,OAjBDsG,IAAI;EAAA;EAmBJ;AACF;AACA;AACA;AACA;AACA;EALE;EAAAlF,MAAA,CAMM0F,SAAS;IAAf,SAAMA,SAASA,CAAC9G,OAAO;MAAA,IAAA4B,MAAA;MAAA,OAAAjC,mBAAA,CAAA4G,KAAA;QAAA,SAAAsH,WAAApH,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACf/E,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;cAAA,OAAA6E,QAAA,CAAAqH,MAAA,WACrBlM,MAAM,GACT,IAAI,CAAClC,KAAK,CAACkO,YAAY,CAAChM,MAAM,EAAE,IAAI,CAACoL,wBAAwB,CAAChN,OAAO,CAAC,CAAC,GACvE,IAAI;YAAA;YAAA;cAAA,OAAAyG,QAAA,CAAAvB,IAAA;UAAA;QAAA;QAAA,OAAA2I,UAAA;MAAA,uBAAA7G,OAAA;IAAA;IACT,OALKF,SAAS;EAAA;EAOf;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EA1BE;EAAA1F,MAAA,CA2BAO,MAAM;IAAN,SAAAA,MAAMA,CAAC3B,OAAO,EAAE;MACd;MACA;MACA;MACA;MACA;MACA,IAAIb,MAAM,CAACiO,QAAQ,EAAE;QACnBnE,yBAAyB,CAAC8E,oBAAoB,GAAG,IAAI;MACvD,CAAC,MAAM,IAAI,CAAC9E,yBAAyB,CAAC8E,oBAAoB,EAAE;QAC1D;QACA;QACA5O,MAAM,CAACyI,MAAM,CACX,0DAA0D,GACxD,yDACJ,CAAC;MACH;;MAEA;MACA;MACA;MACA,IAAI0D,MAAM,CAACjK,SAAS,CAAC2M,cAAc,CAAC9N,IAAI,CAACF,OAAO,EAAE,gBAAgB,CAAC,EAAE;QACnE,IAAIb,MAAM,CAACwO,QAAQ,EAAE;UACnB,MAAM,IAAIrL,KAAK,CACb,+DACF,CAAC;QACH;QACA,IAAI,CAAC6H,OAAO,CAAC,kBAAkB,CAAC,EAAE;UAChC,MAAM,IAAI7H,KAAK,CACb,mEACF,CAAC;QACH;QACA6H,OAAO,CAAC,kBAAkB,CAAC,CAAC8D,eAAe,CAACC,OAAO,CACjDlO,OAAO,CAACmO,cACV,CAAC;QACDnO,OAAO,GAAAJ,aAAA,KAAQI,OAAO,CAAE;QACxB,OAAOA,OAAO,CAACmO,cAAc;MAC/B;;MAEA;MACA,SAAAC,GAAA,MAAAC,aAAA,GAAkB/C,MAAM,CAACC,IAAI,CAACvL,OAAO,CAAC,EAAAoO,GAAA,GAAAC,aAAA,CAAA3L,MAAA,EAAA0L,GAAA,IAAE;QAAnC,IAAM5C,GAAG,GAAA6C,aAAA,CAAAD,GAAA;QACZ,IAAI,CAACjD,iBAAiB,CAACM,QAAQ,CAACD,GAAG,CAAC,EAAE;UACpCE,OAAO,CAAChH,KAAK,oCAAkC8G,GAAK,CAAC;QACvD;MACF;;MAEA;MACA,SAAA8C,GAAA,MAAAC,kBAAA,GAAkBpD,iBAAiB,EAAAmD,GAAA,GAAAC,kBAAA,CAAA7L,MAAA,EAAA4L,GAAA,IAAE;QAAhC,IAAM9C,IAAG,GAAA+C,kBAAA,CAAAD,GAAA;QACZ,IAAI9C,IAAG,IAAIxL,OAAO,EAAE;UAClB,IAAIwL,IAAG,IAAI,IAAI,CAACG,QAAQ,EAAE;YACxB,IAAIH,IAAG,KAAK,YAAY,IAAKrM,MAAM,CAACqP,MAAM,IAAIhD,IAAG,KAAK,eAAgB,EAAE;cACtE,MAAM,IAAIrM,MAAM,CAACmD,KAAK,iBAAgBkJ,IAAG,qBAAmB,CAAC;YAC/D;UACF;UACA,IAAI,CAACG,QAAQ,CAACH,IAAG,CAAC,GAAGxL,OAAO,CAACwL,IAAG,CAAC;QACnC;MACF;MAEA,IAAIxL,OAAO,CAAC2M,UAAU,IAAI3M,OAAO,CAAC2M,UAAU,KAAK,IAAI,CAACjN,KAAK,CAAC+O,KAAK,IAAIzO,OAAO,CAAC2M,UAAU,KAAK,IAAI,CAACjN,KAAK,EAAE;QACtG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACoM,qBAAqB,CAAC9L,OAAO,CAAC;MAClD;IACF;IAAC,OA5DD2B,MAAM;EAAA;EA8DN;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAVE;EAAAP,MAAA,CAWAsN,OAAO;IAAP,SAAAA,OAAOA,CAACrM,IAAI,EAAE;MACZ,IAAIsM,GAAG,GAAG,IAAI,CAAC/J,YAAY,CAACgK,QAAQ,CAACvM,IAAI,CAAC;MAC1C;MACA,IAAI,CAACwF,gBAAgB,CAAC8G,GAAG,CAAC1L,QAAQ,CAAC;MACnC,OAAO0L,GAAG;IACZ;IAAC,OALDD,OAAO;EAAA;EAOP;AACF;AACA;AACA;AACA;EAJE;EAAAtN,MAAA,CAKAyN,cAAc;IAAd,SAAAA,cAAcA,CAACxM,IAAI,EAAE;MACnB,OAAO,IAAI,CAACwC,mBAAmB,CAAC+J,QAAQ,CAACvM,IAAI,CAAC;IAChD;IAAC,OAFDwM,cAAc;EAAA;EAId;AACF;AACA;AACA;AACA;EAJE;EAAAzN,MAAA,CAKA0N,QAAQ;IAAR,SAAAA,QAAQA,CAACzM,IAAI,EAAE;MACb,OAAO,IAAI,CAAC6E,aAAa,CAAC0H,QAAQ,CAACvM,IAAI,CAAC;IAC1C;IAAC,OAFDyM,QAAQ;EAAA;EAAA1N,MAAA,CAIRyK,eAAe;IAAf,SAAAA,eAAeA,CAAC7L,OAAO,EAAE;MACvB,IAAI,CAACb,MAAM,CAACwO,QAAQ,EAAE;QACpB;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI3N,OAAO,CAACO,UAAU,EAAE;QACtB,IAAI,CAACA,UAAU,GAAGP,OAAO,CAACO,UAAU;MACtC,CAAC,MAAM,IAAIP,OAAO,CAAC+O,MAAM,EAAE;QACzB,IAAI,CAACxO,UAAU,GAAG4E,GAAG,CAAC6J,OAAO,CAAChP,OAAO,CAAC+O,MAAM,CAAC;MAC/C,CAAC,MAAM,IACL,OAAO9F,yBAAyB,KAAK,WAAW,IAChDA,yBAAyB,CAACgG,uBAAuB,EACjD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC1O,UAAU,GAAG4E,GAAG,CAAC6J,OAAO,CAC3B/F,yBAAyB,CAACgG,uBAC5B,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAAC1O,UAAU,GAAGpB,MAAM,CAACoB,UAAU;MACrC;IACF;IAAC,OAjCDsL,eAAe;EAAA;EAAAzK,MAAA,CAmCf8N,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAAA,EAAG;MACpB;MACA;MACA;MACA,IAAMC,qBAAqB,GACzB,IAAI,CAACxD,QAAQ,CAACwD,qBAAqB,KAAK,IAAI,GACxChD,2BAA2B,GAC3B,IAAI,CAACR,QAAQ,CAACwD,qBAAqB;MACzC,OACE,IAAI,CAACxD,QAAQ,CAACyD,eAAe,IAC7B,CAACD,qBAAqB,IAAIjD,6BAA6B,IAAI,QAAQ;IAEvE;IAAC,OAZDgD,mBAAmB;EAAA;EAAA9N,MAAA,CAcnBiO,gCAAgC;IAAhC,SAAAA,gCAAgCA,CAAA,EAAG;MACjC,OACE,IAAI,CAAC1D,QAAQ,CAAC2D,4BAA4B,IAC1C,CAAC,IAAI,CAAC3D,QAAQ,CAAC4D,kCAAkC,IAC/CC,4CAA4C,IAAI,QAAQ;IAE9D;IAAC,OANDH,gCAAgC;EAAA;EAAAjO,MAAA,CAQhCqO,iCAAiC;IAAjC,SAAAA,iCAAiCA,CAAA,EAAG;MAClC,OACE,IAAI,CAAC9D,QAAQ,CAAC+D,6BAA6B,IAC3C,CAAC,IAAI,CAAC/D,QAAQ,CAACgE,mCAAmC,IAChDC,6CAA6C,IAAI,QAAQ;IAE/D;IAAC,OANDH,iCAAiC;EAAA;EAAArO,MAAA,CAQjCqE,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAACoK,IAAI,EAAE;MACrB;MACA;MACA,OAAO,IAAInK,IAAI,CAAC,IAAIA,IAAI,CAACmK,IAAI,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAACZ,mBAAmB,CAAC,CAAC,CAAC;IACxE;IAAC,OAJDzJ,gBAAgB;EAAA;EAAArE,MAAA,CAMhBuE,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAACkK,IAAI,EAAE;MACtB,IAAIE,aAAa,GAAG,GAAG,GAAG,IAAI,CAACb,mBAAmB,CAAC,CAAC;MACpD,IAAMc,gBAAgB,GAAGC,2BAA2B,GAAG,IAAI;MAC3D,IAAIF,aAAa,GAAGC,gBAAgB,EAAE;QACpCD,aAAa,GAAGC,gBAAgB;MAClC;MACA,OAAO,IAAItK,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAACmK,IAAI,CAAC,GAAGE,aAAa;IACpD;IAAC,OAPDpK,iBAAiB;EAAA,IASjB;EAAA;EAAAvE,MAAA,CACAyG,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAAC5E,QAAQ,EAAE,CAAC;IAAC,OAA7B4E,gBAAgB;EAAA;EAAA,OAAA/H,cAAA;AAAA;AAGlB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACAX,MAAM,CAACyC,MAAM,GAAG;EAAA,OAAMlD,QAAQ,CAACkD,MAAM,CAAC,CAAC;AAAA;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACAzC,MAAM,CAACmH,IAAI,GAAG,UAAAtG,OAAO;EAAA,OAAItB,QAAQ,CAAC4H,IAAI,CAACtG,OAAO,CAAC;AAAA;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACAb,MAAM,CAAC2H,SAAS,GAAG,UAAA9G,OAAO;EAAA,OAAItB,QAAQ,CAACoI,SAAS,CAAC9G,OAAO,CAAC;AAAA;;AAEzD;AACA,IAAMkM,6BAA6B,GAAG,EAAE;AACxC;AACA,IAAMsD,4CAA4C,GAAG,CAAC;AACtD;AACA,IAAMI,6CAA6C,GAAG,EAAE;AACxD;AACA;AACA;AACA,IAAMK,2BAA2B,GAAG,IAAI,CAAC,CAAC;AAC1C;AACO,IAAM/E,yBAAyB,GAAG,GAAG,GAAG,IAAI;AAAE;AACrD;AACA;AACA,IAAMiB,2BAA2B,GAAG,GAAG,GAAG,GAAG,C", "file": "/packages/accounts-base.js", "sourcesContent": ["import {\n  AccountsClient,\n  AccountsTest,\n} from \"./accounts_client.js\";\n\n/**\n * @namespace Accounts\n * @summary The namespace for all client-side accounts-related methods.\n */\nAccounts = new AccountsClient(Meteor.settings?.public?.packages?.accounts || {});\n\n/**\n * @summary A [Mongo.Collection](#collections) containing user documents.\n * @locus Anywhere\n * @type {Mongo.Collection}\n * @importFromPackage meteor\n */\nMeteor.users = Accounts.users;\n\nexport {\n  Accounts,\n  AccountsClient,\n  AccountsTest,\n  // For backwards compatibility. Note that exporting an object as the\n  // default export is *not* the same as exporting its properties as named\n  // exports, as was previously assumed.\n  exports as default,\n};\n", "import {AccountsCommon} from \"./accounts_common.js\";\n\n/**\n * @summary Constructor for the `Accounts` object on the client.\n * @locus Client\n * @class AccountsClient\n * @extends AccountsCommon\n * @instancename accountsClient\n * @param {Object} options an object with fields:\n * @param {Object} options.connection Optional DDP connection to reuse.\n * @param {String} options.ddpUrl Optional URL for creating a new DDP connection.\n * @param {'session' | 'local'} options.clientStorage Optional Define what kind of storage you want for credentials on the client. Default is 'local' to use `localStorage`. Set to 'session' to use session storage.\n */\nexport class AccountsClient extends AccountsCommon {\n  constructor(options) {\n    super(options);\n\n    this._loggingIn = new ReactiveVar(false);\n    this._loggingOut = new ReactiveVar(false);\n\n    this._loginServicesHandle =\n      this.connection.subscribe(\"meteor.loginServiceConfiguration\");\n\n    this._pageLoadLoginCallbacks = [];\n    this._pageLoadLoginAttemptInfo = null;\n\n    this.savedHash = window.location.hash;\n    this._initUrlMatching();\n\n    this.initStorageLocation();\n\n    // Defined in localstorage_token.js.\n    this._initLocalStorage();\n\n    // This is for .registerClientLoginFunction & .callLoginFunction.\n    this._loginFuncs = {};\n\n    // This tracks whether callbacks registered with\n    // Accounts.onLogin have been called\n    this._loginCallbacksCalled = false;\n  }\n\n  initStorageLocation(options) {\n    // Determine whether to use local or session storage to storage credentials and anything else.\n    this.storageLocation = (options?.clientStorage === 'session' || Meteor.settings?.public?.packages?.accounts?.clientStorage === 'session') ? window.sessionStorage : Meteor._localStorage;\n  }\n\n  config(options) {\n    super.config(options);\n\n    this.initStorageLocation(options);\n  }\n\n  ///\n  /// CURRENT USER\n  ///\n\n  // @override\n  userId() {\n    return this.connection.userId();\n  }\n\n  // This is mostly just called within this file, but Meteor.loginWithPassword\n  // also uses it to make loggingIn() be true during the beginPasswordExchange\n  // method call too.\n  _setLoggingIn(x) {\n    this._loggingIn.set(x);\n  }\n\n  /**\n   * @summary True if a login method (such as `Meteor.loginWithPassword`, `Meteor.loginWithFacebook`, or `Accounts.createUser`) is currently in progress. A reactive data source.\n   * @locus Client\n   */\n  loggingIn() {\n    return this._loggingIn.get();\n  }\n\n  /**\n   * @summary True if a logout method (such as `Meteor.logout`) is currently in progress. A reactive data source.\n   * @locus Client\n   */\n  loggingOut() {\n    return this._loggingOut.get();\n  }\n\n  /**\n   * @summary Register a new login function on the client. Intended for OAuth package authors. You can call the login function by using\n   `Accounts.callLoginFunction` or `Accounts.callLoginFunction`.\n   * @locus Client\n   * @param {String} funcName The name of your login function. Used by `Accounts.callLoginFunction` and `Accounts.applyLoginFunction`.\n   Should be the OAuth provider name accordingly.\n   * @param {Function} func The actual function you want to call. Just write it in the manner of `loginWithFoo`.\n   */\n  registerClientLoginFunction(funcName, func) {\n    if (this._loginFuncs[funcName]) {\n      throw new Error(`${funcName} has been defined already`);\n    }\n    this._loginFuncs[funcName] = func;\n  }\n\n  /**\n   * @summary Call a login function defined using `Accounts.registerClientLoginFunction`. Excluding the first argument, all remaining\n   arguments are passed to the login function accordingly. Use `applyLoginFunction` if you want to pass in an arguments array that contains\n   all arguments for the login function.\n   * @locus Client\n   * @param {String} funcName The name of the login function you wanted to call.\n   */\n  callLoginFunction(funcName, ...funcArgs) {\n    if (!this._loginFuncs[funcName]) {\n      throw new Error(`${funcName} was not defined`);\n    }\n    return this._loginFuncs[funcName].apply(this, funcArgs);\n  }\n\n  /**\n   * @summary Same as ``callLoginFunction` but accept an `arguments` which contains all arguments for the login\n   function.\n   * @locus Client\n   * @param {String} funcName The name of the login function you wanted to call.\n   * @param {Array} funcArgs The `arguments` for the login function.\n   */\n  applyLoginFunction(funcName, funcArgs) {\n    if (!this._loginFuncs[funcName]) {\n      throw new Error(`${funcName} was not defined`);\n    }\n    return this._loginFuncs[funcName].apply(this, funcArgs);\n  }\n\n  /**\n   * @summary Log the user out.\n   * @locus Client\n   * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n   */\n  logout(callback) {\n    this._loggingOut.set(true);\n\n    this.connection.applyAsync('logout', [], {\n      // TODO[FIBERS]: Look this { wait: true } later.\n      wait: true\n    })\n      .then((result) => {\n        this._loggingOut.set(false);\n        this._loginCallbacksCalled = false;\n        this.makeClientLoggedOut();\n        callback && callback();\n      })\n      .catch((e) => {\n        this._loggingOut.set(false);\n        callback && callback(e);\n      });\n  }\n\n  /**\n   * @summary Log out other clients logged in as the current user, but does not log out the client that calls this function.\n   * @locus Client\n   * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n   */\n  logoutOtherClients(callback) {\n    // We need to make two method calls: one to replace our current token,\n    // and another to remove all tokens except the current one. We want to\n    // call these two methods one after the other, without any other\n    // methods running between them. For example, we don't want `logout`\n    // to be called in between our two method calls (otherwise the second\n    // method call would return an error). Another example: we don't want\n    // logout to be called before the callback for `getNewToken`;\n    // otherwise we would momentarily log the user out and then write a\n    // new token to localStorage.\n    //\n    // To accomplish this, we make both calls as wait methods, and queue\n    // them one after the other, without spinning off the event loop in\n    // between. Even though we queue `removeOtherTokens` before\n    // `getNewToken`, we won't actually send the `removeOtherTokens` call\n    // until the `getNewToken` callback has finished running, because they\n    // are both wait methods.\n    this.connection.apply(\n      'getNewToken',\n      [],\n      { wait: true },\n      (err, result) => {\n        if (! err) {\n          this._storeLoginToken(\n            this.userId(),\n            result.token,\n            result.tokenExpires\n          );\n        }\n      }\n    );\n\n    this.connection.apply(\n      'removeOtherTokens',\n      [],\n      { wait: true },\n      err => callback && callback(err)\n    );\n  }\n\n  ///\n  /// LOGIN METHODS\n  ///\n\n  // Call a login method on the server.\n  //\n  // A login method is a method which on success calls `this.setUserId(id)` and\n  // `Accounts._setLoginToken` on the server and returns an object with fields\n  // 'id' (containing the user id), 'token' (containing a resume token), and\n  // optionally `tokenExpires`.\n  //\n  // This function takes care of:\n  //   - Updating the Meteor.loggingIn() reactive data source\n  //   - Calling the method in 'wait' mode\n  //   - On success, saving the resume token to localStorage\n  //   - On success, calling Accounts.connection.setUserId()\n  //   - Setting up an onReconnect handler which logs in with\n  //     the resume token\n  //\n  // Options:\n  // - methodName: The method to call (default 'login')\n  // - methodArguments: The arguments for the method\n  // - validateResult: If provided, will be called with the result of the\n  //                 method. If it throws, the client will not be logged in (and\n  //                 its error will be passed to the callback).\n  // - userCallback: Will be called with no arguments once the user is fully\n  //                 logged in, or with the error on error.\n  //\n  callLoginMethod(options) {\n    options = {\n      methodName: 'login',\n      methodArguments: [{}],\n      _suppressLoggingIn: false,\n      ...options,\n    };\n\n    // Set defaults for callback arguments to no-op functions; make sure we\n    // override falsey values too.\n    ['validateResult', 'userCallback'].forEach(f => {\n      if (!options[f])\n        options[f] = () => null;\n    });\n\n    let called;\n    // Prepare callbacks: user provided and onLogin/onLoginFailure hooks.\n    const loginCallbacks = ({ error, loginDetails }) => {\n      if (!called) {\n        called = true;\n        if (!error) {\n          this._onLoginHook.forEach(callback => {\n            callback(loginDetails);\n            return true;\n          });\n          this._loginCallbacksCalled = true;\n        } else {\n          this._loginCallbacksCalled = false;\n          this._onLoginFailureHook.forEach(callback => {\n            callback({ error });\n            return true;\n          });\n        }\n        options.userCallback(error, loginDetails);\n      }\n    };\n\n    let reconnected = false;\n\n    // We want to set up onReconnect as soon as we get a result token back from\n    // the server, without having to wait for subscriptions to rerun. This is\n    // because if we disconnect and reconnect between getting the result and\n    // getting the results of subscription rerun, we WILL NOT re-send this\n    // method (because we never re-send methods whose results we've received)\n    // but we WILL call loggedInAndDataReadyCallback at \"reconnect quiesce\"\n    // time. This will lead to makeClientLoggedIn(result.id) even though we\n    // haven't actually sent a login method!\n    //\n    // But by making sure that we send this \"resume\" login in that case (and\n    // calling makeClientLoggedOut if it fails), we'll end up with an accurate\n    // client-side userId. (It's important that livedata_connection guarantees\n    // that the \"reconnect quiesce\"-time call to loggedInAndDataReadyCallback\n    // will occur before the callback from the resume login call.)\n    const onResultReceived = (err, result) => {\n      if (err || !result || !result.token) {\n        // Leave onReconnect alone if there was an error, so that if the user was\n        // already logged in they will still get logged in on reconnect.\n        // See issue #4970.\n      } else {\n        // First clear out any previously set Acccounts login onReconnect\n        // callback (to make sure we don't keep piling up duplicate callbacks,\n        // which would then all be triggered when reconnecting).\n        if (this._reconnectStopper) {\n          this._reconnectStopper.stop();\n        }\n\n        this._reconnectStopper = DDP.onReconnect(conn => {\n          if (conn != this.connection) {\n            return;\n          }\n          reconnected = true;\n          // If our token was updated in storage, use the latest one.\n          const storedToken = this._storedLoginToken();\n          if (storedToken) {\n            result = {\n              token: storedToken,\n              tokenExpires: this._storedLoginTokenExpires()\n            };\n          }\n          if (!result.tokenExpires)\n            result.tokenExpires = this._tokenExpiration(new Date());\n          if (this._tokenExpiresSoon(result.tokenExpires)) {\n            this.makeClientLoggedOut();\n          } else {\n            this.callLoginMethod({\n              methodArguments: [{resume: result.token}],\n              // Reconnect quiescence ensures that the user doesn't see an\n              // intermediate state before the login method finishes. So we don't\n              // need to show a logging-in animation.\n              _suppressLoggingIn: true,\n              userCallback: (error, loginDetails) => {\n                const storedTokenNow = this._storedLoginToken();\n                if (error) {\n                  // If we had a login error AND the current stored token is the\n                  // one that we tried to log in with, then declare ourselves\n                  // logged out. If there's a token in storage but it's not the\n                  // token that we tried to log in with, we don't know anything\n                  // about whether that token is valid or not, so do nothing. The\n                  // periodic localStorage poll will decide if we are logged in or\n                  // out with this token, if it hasn't already. Of course, even\n                  // with this check, another tab could insert a new valid token\n                  // immediately before we clear localStorage here, which would\n                  // lead to both tabs being logged out, but by checking the token\n                  // in storage right now we hope to make that unlikely to happen.\n                  //\n                  // If there is no token in storage right now, we don't have to\n                  // do anything; whatever code removed the token from storage was\n                  // responsible for calling `makeClientLoggedOut()`, or the\n                  // periodic localStorage poll will call `makeClientLoggedOut`\n                  // eventually if another tab wiped the token from storage.\n                  if (storedTokenNow && storedTokenNow === result.token) {\n                    this.makeClientLoggedOut();\n                  }\n                }\n                // Possibly a weird callback to call, but better than nothing if\n                // there is a reconnect between \"login result received\" and \"data\n                // ready\".\n                loginCallbacks({ error, loginDetails });\n              }});\n          }\n        });\n      }\n    };\n\n    // This callback is called once the local cache of the current-user\n    // subscription (and all subscriptions, in fact) are guaranteed to be up to\n    // date.\n    const loggedInAndDataReadyCallback = (error, result) => {\n      // If the login method returns its result but the connection is lost\n      // before the data is in the local cache, it'll set an onReconnect (see\n      // above). The onReconnect will try to log in using the token, and *it*\n      // will call userCallback via its own version of this\n      // loggedInAndDataReadyCallback. So we don't have to do anything here.\n      if (reconnected)\n        return;\n\n      // Note that we need to call this even if _suppressLoggingIn is true,\n      // because it could be matching a _setLoggingIn(true) from a\n      // half-completed pre-reconnect login method.\n      if (error || !result) {\n        error = error || new Error(\n          `No result from call to ${options.methodName}`\n        );\n        loginCallbacks({ error });\n        this._setLoggingIn(false);\n        return;\n      }\n      try {\n        options.validateResult(result);\n      } catch (e) {\n        loginCallbacks({ error: e });\n        this._setLoggingIn(false);\n        return;\n      }\n\n      // Make the client logged in. (The user data should already be loaded!)\n      this.makeClientLoggedIn(result.id, result.token, result.tokenExpires);\n\n      // use Tracker to make we sure have a user before calling the callbacks\n      Tracker.autorun(async (computation) => {\n        const user = await Tracker.withComputation(computation, () =>\n          Meteor.userAsync(),\n        );\n\n        if (user) {\n          loginCallbacks({ loginDetails: result });\n          this._setLoggingIn(false);\n          computation.stop();\n        }\n      });\n\n    };\n\n    if (!options._suppressLoggingIn) {\n      this._setLoggingIn(true);\n    }\n    this.connection.applyAsync(\n      options.methodName,\n      options.methodArguments,\n      { wait: true, onResultReceived },\n      loggedInAndDataReadyCallback);\n  }\n\n  makeClientLoggedOut() {\n    // Ensure client was successfully logged in before running logout hooks.\n    if (this.connection._userId) {\n      this._onLogoutHook.each(callback => {\n        callback();\n        return true;\n      });\n    }\n    this._unstoreLoginToken();\n    this.connection.setUserId(null);\n    this._reconnectStopper && this._reconnectStopper.stop();\n  }\n\n  makeClientLoggedIn(userId, token, tokenExpires) {\n    this._storeLoginToken(userId, token, tokenExpires);\n    this.connection.setUserId(userId);\n  }\n\n  ///\n  /// LOGIN SERVICES\n  ///\n\n  // A reactive function returning whether the loginServiceConfiguration\n  // subscription is ready. Used by accounts-ui to hide the login button\n  // until we have all the configuration loaded\n  //\n  loginServicesConfigured() {\n    return this._loginServicesHandle.ready();\n  };\n\n  // Some login services such as the redirect login flow or the resume\n  // login handler can log the user in at page load time.  The\n  // Meteor.loginWithX functions have a callback argument, but the\n  // callback function instance won't be in memory any longer if the\n  // page was reloaded.  The `onPageLoadLogin` function allows a\n  // callback to be registered for the case where the login was\n  // initiated in a previous VM, and we now have the result of the login\n  // attempt in a new VM.\n\n  // Register a callback to be called if we have information about a\n  // login attempt at page load time.  Call the callback immediately if\n  // we already have the page load login attempt info, otherwise stash\n  // the callback to be called if and when we do get the attempt info.\n  //\n  onPageLoadLogin(f) {\n    if (this._pageLoadLoginAttemptInfo) {\n      f(this._pageLoadLoginAttemptInfo);\n    } else {\n      this._pageLoadLoginCallbacks.push(f);\n    }\n  };\n\n  // Receive the information about the login attempt at page load time.\n  // Call registered callbacks, and also record the info in case\n  // someone's callback hasn't been registered yet.\n  //\n  _pageLoadLogin(attemptInfo) {\n    if (this._pageLoadLoginAttemptInfo) {\n      Meteor._debug(\n        'Ignoring unexpected duplicate page load login attempt info'\n      );\n      return;\n    }\n\n    this._pageLoadLoginCallbacks.forEach(callback => callback(attemptInfo));\n    this._pageLoadLoginCallbacks = [];\n    this._pageLoadLoginAttemptInfo = attemptInfo;\n  }\n\n  // _startupCallback executes on onLogin callbacks\n  // at registration time if already logged in\n  // this can happen when new AccountsClient is created\n  // before callbacks are registered see #10157\n  _startupCallback(callback) {\n    // Are we already logged in?\n    if (this._loginCallbacksCalled) {\n      // If already logged in before handler is registered, it's safe to\n      // assume type is a 'resume', so we execute the callback at the end\n      // of the queue so that Meteor.startup can complete before any\n      // embedded onLogin callbacks would execute.\n      Meteor.setTimeout(() => callback({ type: 'resume' }), 0);\n    }\n  }\n\n  ///\n  /// LOGIN TOKENS\n  ///\n\n  // These methods deal with storing a login token and user id in the\n  // browser's localStorage facility. It polls local storage every few\n  // seconds to synchronize login state between multiple tabs in the same\n  // browser.\n\n  loginWithToken(token, callback) {\n    this.callLoginMethod({\n      methodArguments: [{\n        resume: token\n      }],\n      userCallback: callback\n    });\n  };\n\n  // Semi-internal API. Call this function to re-enable auto login after\n  // if it was disabled at startup.\n  _enableAutoLogin() {\n    this._autoLoginEnabled = true;\n    this._pollStoredLoginToken();\n  };\n\n  ///\n  /// STORING\n  ///\n\n  // Call this from the top level of the test file for any test that does\n  // logging in and out, to protect multiple tabs running the same tests\n  // simultaneously from interfering with each others' localStorage.\n  _isolateLoginTokenForTest() {\n    this.LOGIN_TOKEN_KEY = this.LOGIN_TOKEN_KEY + Random.id();\n    this.USER_ID_KEY = this.USER_ID_KEY + Random.id();\n  };\n\n  _storeLoginToken(userId, token, tokenExpires) {\n    this.storageLocation.setItem(this.USER_ID_KEY, userId);\n    this.storageLocation.setItem(this.LOGIN_TOKEN_KEY, token);\n    if (! tokenExpires)\n      tokenExpires = this._tokenExpiration(new Date());\n    this.storageLocation.setItem(this.LOGIN_TOKEN_EXPIRES_KEY, tokenExpires);\n\n    // to ensure that the localstorage poller doesn't end up trying to\n    // connect a second time\n    this._lastLoginTokenWhenPolled = token;\n  };\n\n  _unstoreLoginToken() {\n    this.storageLocation.removeItem(this.USER_ID_KEY);\n    this.storageLocation.removeItem(this.LOGIN_TOKEN_KEY);\n    this.storageLocation.removeItem(this.LOGIN_TOKEN_EXPIRES_KEY);\n\n    // to ensure that the localstorage poller doesn't end up trying to\n    // connect a second time\n    this._lastLoginTokenWhenPolled = null;\n  };\n\n  // This is private, but it is exported for now because it is used by a\n  // test in accounts-password.\n  _storedLoginToken() {\n    return this.storageLocation.getItem(this.LOGIN_TOKEN_KEY);\n  };\n\n  _storedLoginTokenExpires() {\n    return this.storageLocation.getItem(this.LOGIN_TOKEN_EXPIRES_KEY);\n  };\n\n  _storedUserId() {\n    return this.storageLocation.getItem(this.USER_ID_KEY);\n  };\n\n  _unstoreLoginTokenIfExpiresSoon() {\n    const tokenExpires = this._storedLoginTokenExpires();\n    if (tokenExpires && this._tokenExpiresSoon(new Date(tokenExpires))) {\n      this._unstoreLoginToken();\n    }\n  };\n\n  ///\n  /// AUTO-LOGIN\n  ///\n\n  _initLocalStorage() {\n    // Key names to use in localStorage\n    this.LOGIN_TOKEN_KEY = \"Meteor.loginToken\";\n    this.LOGIN_TOKEN_EXPIRES_KEY = \"Meteor.loginTokenExpires\";\n    this.USER_ID_KEY = \"Meteor.userId\";\n\n    const rootUrlPathPrefix = __meteor_runtime_config__.ROOT_URL_PATH_PREFIX;\n    if (rootUrlPathPrefix || this.connection !== Meteor.connection) {\n      // We want to keep using the same keys for existing apps that do not\n      // set a custom ROOT_URL_PATH_PREFIX, so that most users will not have\n      // to log in again after an app updates to a version of Meteor that\n      // contains this code, but it's generally preferable to namespace the\n      // keys so that connections from distinct apps to distinct DDP URLs\n      // will be distinct in Meteor._localStorage.\n      let namespace = `:${this.connection._stream.rawUrl}`;\n      if (rootUrlPathPrefix) {\n        namespace += `:${rootUrlPathPrefix}`;\n      }\n      this.LOGIN_TOKEN_KEY += namespace;\n      this.LOGIN_TOKEN_EXPIRES_KEY += namespace;\n      this.USER_ID_KEY += namespace;\n    }\n\n    let token;\n    if (this._autoLoginEnabled) {\n      // Immediately try to log in via local storage, so that any DDP\n      // messages are sent after we have established our user account\n      this._unstoreLoginTokenIfExpiresSoon();\n      token = this._storedLoginToken();\n      if (token) {\n        // On startup, optimistically present us as logged in while the\n        // request is in flight. This reduces page flicker on startup.\n        const userId = this._storedUserId();\n        userId && this.connection.setUserId(userId);\n        this.loginWithToken(token, err => {\n          if (err) {\n            Meteor._debug(`Error logging in with token: ${err}`);\n            this.makeClientLoggedOut();\n          }\n\n          this._pageLoadLogin({\n            type: \"resume\",\n            allowed: !err,\n            error: err,\n            methodName: \"login\",\n            // XXX This is duplicate code with loginWithToken, but\n            // loginWithToken can also be called at other times besides\n            // page load.\n            methodArguments: [{resume: token}]\n          });\n        });\n      }\n    }\n\n    // Poll local storage every 3 seconds to login if someone logged in in\n    // another tab\n    this._lastLoginTokenWhenPolled = token;\n\n    if (this._pollIntervalTimer) {\n      // Unlikely that _initLocalStorage will be called more than once for\n      // the same AccountsClient instance, but just in case...\n      clearInterval(this._pollIntervalTimer);\n    }\n\n    this._pollIntervalTimer = setInterval(() => {\n      this._pollStoredLoginToken();\n    }, 3000);\n  };\n\n  _pollStoredLoginToken() {\n    if (! this._autoLoginEnabled) {\n      return;\n    }\n\n    const currentLoginToken = this._storedLoginToken();\n\n    // != instead of !== just to make sure undefined and null are treated the same\n    if (this._lastLoginTokenWhenPolled != currentLoginToken) {\n      if (currentLoginToken) {\n        this.loginWithToken(currentLoginToken, (err) => {\n          if (err) {\n            this.makeClientLoggedOut();\n          }\n        });\n      } else {\n        this.logout();\n      }\n    }\n\n    this._lastLoginTokenWhenPolled = currentLoginToken;\n  };\n\n  ///\n  /// URLS\n  ///\n\n  _initUrlMatching() {\n    // By default, allow the autologin process to happen.\n    this._autoLoginEnabled = true;\n\n    // We only support one callback per URL.\n    this._accountsCallbacks = {};\n\n    // Try to match the saved value of window.location.hash.\n    this._attemptToMatchHash();\n  };\n\n  // Separate out this functionality for testing\n  _attemptToMatchHash() {\n    attemptToMatchHash(this, this.savedHash, defaultSuccessHandler);\n  };\n\n  /**\n   * @summary Register a function to call when a reset password link is clicked\n   * in an email sent by\n   * [`Accounts.sendResetPasswordEmail`](#accounts_sendresetpasswordemail).\n   * This function should be called in top-level code, not inside\n   * `Meteor.startup()`.\n   * @memberof! Accounts\n   * @name onResetPasswordLink\n   * @param  {Function} callback The function to call. It is given two arguments:\n   *\n   * 1. `token`: A password reset token that can be passed to\n   * [`Accounts.resetPassword`](#accounts_resetpassword).\n   * 2. `done`: A function to call when the password reset UI flow is complete. The normal\n   * login process is suspended until this function is called, so that the\n   * password for user A can be reset even if user B was logged in.\n   * @locus Client\n   */\n  onResetPasswordLink(callback) {\n    if (this._accountsCallbacks[\"reset-password\"]) {\n      Meteor._debug(\"Accounts.onResetPasswordLink was called more than once. \" +\n        \"Only one callback added will be executed.\");\n    }\n\n    this._accountsCallbacks[\"reset-password\"] = callback;\n  };\n\n  /**\n   * @summary Register a function to call when an email verification link is\n   * clicked in an email sent by\n   * [`Accounts.sendVerificationEmail`](#accounts_sendverificationemail).\n   * This function should be called in top-level code, not inside\n   * `Meteor.startup()`.\n   * @memberof! Accounts\n   * @name onEmailVerificationLink\n   * @param  {Function} callback The function to call. It is given two arguments:\n   *\n   * 1. `token`: An email verification token that can be passed to\n   * [`Accounts.verifyEmail`](#accounts_verifyemail).\n   * 2. `done`: A function to call when the email verification UI flow is complete.\n   * The normal login process is suspended until this function is called, so\n   * that the user can be notified that they are verifying their email before\n   * being logged in.\n   * @locus Client\n   */\n  onEmailVerificationLink(callback) {\n    if (this._accountsCallbacks[\"verify-email\"]) {\n      Meteor._debug(\"Accounts.onEmailVerificationLink was called more than once. \" +\n        \"Only one callback added will be executed.\");\n    }\n\n    this._accountsCallbacks[\"verify-email\"] = callback;\n  };\n\n  /**\n   * @summary Register a function to call when an account enrollment link is\n   * clicked in an email sent by\n   * [`Accounts.sendEnrollmentEmail`](#accounts_sendenrollmentemail).\n   * This function should be called in top-level code, not inside\n   * `Meteor.startup()`.\n   * @memberof! Accounts\n   * @name onEnrollmentLink\n   * @param  {Function} callback The function to call. It is given two arguments:\n   *\n   * 1. `token`: A password reset token that can be passed to\n   * [`Accounts.resetPassword`](#accounts_resetpassword) to give the newly\n   * enrolled account a password.\n   * 2. `done`: A function to call when the enrollment UI flow is complete.\n   * The normal login process is suspended until this function is called, so that\n   * user A can be enrolled even if user B was logged in.\n   * @locus Client\n   */\n  onEnrollmentLink(callback) {\n    if (this._accountsCallbacks[\"enroll-account\"]) {\n      Meteor._debug(\"Accounts.onEnrollmentLink was called more than once. \" +\n        \"Only one callback added will be executed.\");\n    }\n\n    this._accountsCallbacks[\"enroll-account\"] = callback;\n  };\n\n}\n\n/**\n * @summary True if a login method (such as `Meteor.loginWithPassword`,\n * `Meteor.loginWithFacebook`, or `Accounts.createUser`) is currently in\n * progress. A reactive data source.\n * @locus Client\n * @importFromPackage meteor\n */\nMeteor.loggingIn = () => Accounts.loggingIn();\n\n/**\n * @summary True if a logout method (such as `Meteor.logout`) is currently in\n * progress. A reactive data source.\n * @locus Client\n * @importFromPackage meteor\n */\nMeteor.loggingOut = () => Accounts.loggingOut();\n\n/**\n * @summary Log the user out.\n * @locus Client\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage meteor\n */\nMeteor.logout = callback => Accounts.logout(callback);\n\n/**\n * @summary Log out other clients logged in as the current user, but does not log out the client that calls this function.\n * @locus Client\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage meteor\n */\nMeteor.logoutOtherClients = callback => Accounts.logoutOtherClients(callback);\n\n/**\n * @summary Login with a Meteor access token.\n * @locus Client\n * @param {Object} [token] Local storage token for use with login across\n * multiple tabs in the same browser.\n * @param {Function} [callback] Optional callback. Called with no arguments on\n * success.\n * @importFromPackage meteor\n */\nMeteor.loginWithToken = (token, callback) =>\n  Accounts.loginWithToken(token, callback);\n\n///\n/// HANDLEBARS HELPERS\n///\n\n// If our app has a Blaze, register the {{currentUser}} and {{loggingIn}}\n// global helpers.\nif (Package.blaze) {\n  const { Template } = Package.blaze.Blaze;\n\n  /**\n   * @global\n   * @name  currentUser\n   * @isHelper true\n   * @summary Calls [Meteor.user()](#meteor_user). Use `{{#if currentUser}}` to check whether the user is logged in.\n   */\n  Template.registerHelper('currentUser', () => Meteor.user());\n\n  // TODO: the code above needs to be changed to Meteor.userAsync() when we have\n  // a way to make it reactive using async.\n  // Template.registerHelper('currentUserAsync',\n  //  async () => await Meteor.userAsync());\n\n  /**\n   * @global\n   * @name  loggingIn\n   * @isHelper true\n   * @summary Calls [Meteor.loggingIn()](#meteor_loggingin).\n   */\n  Template.registerHelper('loggingIn', () => Meteor.loggingIn());\n\n  /**\n   * @global\n   * @name  loggingOut\n   * @isHelper true\n   * @summary Calls [Meteor.loggingOut()](#meteor_loggingout).\n   */\n  Template.registerHelper('loggingOut', () => Meteor.loggingOut());\n\n  /**\n   * @global\n   * @name  loggingInOrOut\n   * @isHelper true\n   * @summary Calls [Meteor.loggingIn()](#meteor_loggingin) or [Meteor.loggingOut()](#meteor_loggingout).\n   */\n  Template.registerHelper(\n    'loggingInOrOut',\n    () => Meteor.loggingIn() || Meteor.loggingOut()\n  );\n}\n\nconst defaultSuccessHandler = function(token, urlPart) {\n  // put login in a suspended state to wait for the interaction to finish\n  this._autoLoginEnabled = false;\n\n  // wait for other packages to register callbacks\n  Meteor.startup(() => {\n    // if a callback has been registered for this kind of token, call it\n    if (this._accountsCallbacks[urlPart]) {\n      this._accountsCallbacks[urlPart](token, () => this._enableAutoLogin());\n    }\n  });\n}\n\n// Note that both arguments are optional and are currently only passed by\n// accounts_url_tests.js.\nconst attemptToMatchHash = (accounts, hash, success) => {\n  // All of the special hash URLs we support for accounts interactions\n  [\"reset-password\", \"verify-email\", \"enroll-account\"].forEach(urlPart => {\n    let token;\n\n    const tokenRegex = new RegExp(`^\\\\#\\\\/${urlPart}\\\\/(.*)$`);\n    const match = hash.match(tokenRegex);\n\n    if (match) {\n      token = match[1];\n\n      // XXX COMPAT WITH 0.9.3\n      if (urlPart === \"reset-password\") {\n        accounts._resetPasswordToken = token;\n      } else if (urlPart === \"verify-email\") {\n        accounts._verifyEmailToken = token;\n      } else if (urlPart === \"enroll-account\") {\n        accounts._enrollAccountToken = token;\n      }\n    } else {\n      return;\n    }\n\n    // If no handlers match the hash, then maybe it's meant to be consumed\n    // by some entirely different code, so we only clear it the first time\n    // a handler successfully matches. Note that later handlers reuse the\n    // savedHash, so clearing window.location.hash here will not interfere\n    // with their needs.\n    window.location.hash = \"\";\n\n    // Do some stuff with the token we matched\n    success.call(accounts, token, urlPart);\n  });\n}\n\n// Export for testing\nexport const AccountsTest = {\n  attemptToMatchHash: (hash, success) =>\n    attemptToMatchHash(Accounts, hash, success),\n};\n", "import { Meteor } from 'meteor/meteor';\n\n// config option keys\nconst VALID_CONFIG_KEYS = [\n  'sendVerificationEmail',\n  'forbidClientAccountCreation',\n  'restrictCreationByEmailDomain',\n  'loginExpiration',\n  'loginExpirationInDays',\n  'oauthSecretKey',\n  'passwordResetTokenExpirationInDays',\n  'passwordResetTokenExpiration',\n  'passwordEnrollTokenExpirationInDays',\n  'passwordEnrollTokenExpiration',\n  'ambiguousErrorMessages',\n  'bcryptRounds',\n  'argon2Enabled',\n  'argon2Type',\n  'argon2TimeCost',\n  'argon2MemoryCost',\n  'argon2Parallelism',\n  'defaultFieldSelector',\n  'collection',\n  'loginTokenExpirationHours',\n  'tokenSequenceLength',\n  'clientStorage',\n  'ddpUrl',\n  'connection',\n];\n\n/**\n * @summary Super-constructor for AccountsClient and AccountsServer.\n * @locus Anywhere\n * @class AccountsCommon\n * @instancename accountsClientOrServer\n * @param options {Object} an object with fields:\n * - connection {Object} Optional DDP connection to reuse.\n * - ddpUrl {String} Optional URL for creating a new DDP connection.\n * - collection {String|Mongo.Collection} The name of the Mongo.Collection\n *     or the Mongo.Collection object to hold the users.\n */\nexport class AccountsCommon {\n  constructor(options) {\n    // Validate config options keys\n    for (const key of Object.keys(options)) {\n      if (!VALID_CONFIG_KEYS.includes(key)) {\n        console.error(`Accounts.config: Invalid key: ${key}`);\n      }\n    }\n\n    // Currently this is read directly by packages like accounts-password\n    // and accounts-ui-unstyled.\n    this._options = options || {};\n\n    // Note that setting this.connection = null causes this.users to be a\n    // LocalCollection, which is not what we want.\n    this.connection = undefined;\n    this._initConnection(options || {});\n\n    // There is an allow call in accounts_server.js that restricts writes to\n    // this collection.\n    this.users = this._initializeCollection(options || {});\n\n    // Callback exceptions are printed with Meteor._debug and ignored.\n    this._onLoginHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLogin callback',\n    });\n\n    this._onLoginFailureHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLoginFailure callback',\n    });\n\n    this._onLogoutHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLogout callback',\n    });\n\n    // Expose for testing.\n    this.DEFAULT_LOGIN_EXPIRATION_DAYS = DEFAULT_LOGIN_EXPIRATION_DAYS;\n    this.LOGIN_UNEXPIRING_TOKEN_DAYS = LOGIN_UNEXPIRING_TOKEN_DAYS;\n\n    // Thrown when the user cancels the login process (eg, closes an oauth\n    // popup, declines retina scan, etc)\n    const lceName = 'Accounts.LoginCancelledError';\n    this.LoginCancelledError = Meteor.makeErrorType(lceName, function(\n      description\n    ) {\n      this.message = description;\n    });\n    this.LoginCancelledError.prototype.name = lceName;\n\n    // This is used to transmit specific subclass errors over the wire. We\n    // should come up with a more generic way to do this (eg, with some sort of\n    // symbolic error code rather than a number).\n    this.LoginCancelledError.numericError = 0x8acdc2f;\n  }\n\n  _initializeCollection(options) {\n    if (options.collection && typeof options.collection !== 'string' && !(options.collection instanceof Mongo.Collection)) {\n      throw new Meteor.Error('Collection parameter can be only of type string or \"Mongo.Collection\"');\n    }\n\n    let collectionName = 'users';\n    if (typeof options.collection === 'string') {\n      collectionName = options.collection;\n    }\n\n    let collection;\n    if (options.collection instanceof Mongo.Collection) {\n      collection = options.collection;\n    } else {\n      collection = new Mongo.Collection(collectionName, {\n        _preventAutopublish: true,\n        connection: this.connection,\n      });\n    }\n\n    return collection;\n  }\n\n  /**\n   * @summary Get the current user id, or `null` if no user is logged in. A reactive data source.\n   * @locus Anywhere\n   */\n  userId() {\n    throw new Error('userId method not implemented');\n  }\n\n  // merge the defaultFieldSelector with an existing options object\n  _addDefaultFieldSelector(options = {}) {\n    // this will be the most common case for most people, so make it quick\n    if (!this._options.defaultFieldSelector) return options;\n\n    // if no field selector then just use defaultFieldSelector\n    if (!options.fields)\n      return {\n        ...options,\n        fields: this._options.defaultFieldSelector,\n      };\n\n    // if empty field selector then the full user object is explicitly requested, so obey\n    const keys = Object.keys(options.fields);\n    if (!keys.length) return options;\n\n    // if the requested fields are +ve then ignore defaultFieldSelector\n    // assume they are all either +ve or -ve because Mongo doesn't like mixed\n    if (!!options.fields[keys[0]]) return options;\n\n    // The requested fields are -ve.\n    // If the defaultFieldSelector is +ve then use requested fields, otherwise merge them\n    const keys2 = Object.keys(this._options.defaultFieldSelector);\n    return this._options.defaultFieldSelector[keys2[0]]\n      ? options\n      : {\n          ...options,\n          fields: {\n            ...options.fields,\n            ...this._options.defaultFieldSelector,\n          },\n        };\n  }\n\n  /**\n   * @summary Get the current user record, or `null` if no user is logged in. A reactive data source. In the server this fuction returns a promise.\n   * @locus Anywhere\n   * @param {Object} [options]\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   */\n  user(options) {\n    if (Meteor.isServer) {\n      console.warn([\n        \"`Meteor.user()` is deprecated on the server side.\",\n        \"    To fetch the current user record on the server,\",\n        \"    use `Meteor.userAsync()` instead.\",\n      ].join(\"\\n\"));\n    }\n\n    const self = this;\n    const userId = self.userId();\n    const findOne = (...args) => Meteor.isClient\n      ? self.users.findOne(...args)\n      : self.users.findOneAsync(...args);\n    return userId\n      ? findOne(userId, this._addDefaultFieldSelector(options))\n      : null;\n  }\n\n  /**\n   * @summary Get the current user record, or `null` if no user is logged in.\n   * @locus Anywhere\n   * @param {Object} [options]\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   */\n  async userAsync(options) {\n    const userId = this.userId();\n    return userId\n      ? this.users.findOneAsync(userId, this._addDefaultFieldSelector(options))\n      : null;\n  }\n\n  /**\n   * @summary Set global accounts options. You can also set these in `Meteor.settings.packages.accounts` without the need to call this function.\n   * @locus Anywhere\n   * @param {Object} options\n   * @param {Boolean} options.sendVerificationEmail New users with an email address will receive an address verification email.\n   * @param {Boolean} options.forbidClientAccountCreation Calls to [`createUser`](#accounts_createuser) from the client will be rejected. In addition, if you are using [accounts-ui](#accountsui), the \"Create account\" link will not be available.\n   * @param {String | Function} options.restrictCreationByEmailDomain If set to a string, only allows new users if the domain part of their email address matches the string. If set to a function, only allows new users if the function returns true.  The function is passed the full email address of the proposed new user.  Works with password-based sign-in and external services that expose email addresses (Google, Facebook, GitHub). All existing users still can log in after enabling this option. Example: `Accounts.config({ restrictCreationByEmailDomain: 'school.edu' })`.\n   * @param {Number} options.loginExpiration The number of milliseconds from when a user logs in until their token expires and they are logged out, for a more granular control. If `loginExpirationInDays` is set, it takes precedent.\n   * @param {Number} options.loginExpirationInDays The number of days from when a user logs in until their token expires and they are logged out. Defaults to 90. Set to `null` to disable login expiration.\n   * @param {String} options.oauthSecretKey When using the `oauth-encryption` package, the 16 byte key using to encrypt sensitive account credentials in the database, encoded in base64.  This option may only be specified on the server.  See packages/oauth-encryption/README.md for details.\n   * @param {Number} options.passwordResetTokenExpirationInDays The number of days from when a link to reset password is sent until token expires and user can't reset password with the link anymore. Defaults to 3.\n   * @param {Number} options.passwordResetTokenExpiration The number of milliseconds from when a link to reset password is sent until token expires and user can't reset password with the link anymore. If `passwordResetTokenExpirationInDays` is set, it takes precedent.\n   * @param {Number} options.passwordEnrollTokenExpirationInDays The number of days from when a link to set initial password is sent until token expires and user can't set password with the link anymore. Defaults to 30.\n   * @param {Number} options.passwordEnrollTokenExpiration The number of milliseconds from when a link to set initial password is sent until token expires and user can't set password with the link anymore. If `passwordEnrollTokenExpirationInDays` is set, it takes precedent.\n   * @param {Boolean} options.ambiguousErrorMessages Return ambiguous error messages from login failures to prevent user enumeration. Defaults to `true`.\n   * @param {Number} options.bcryptRounds Allows override of number of bcrypt rounds (aka work factor) used to store passwords. The default is 10.\n   * @param {Boolean} options.argon2Enabled Enable argon2 algorithm usage in replacement for bcrypt. The default is `false`.\n   * @param {'argon2id' | 'argon2i' | 'argon2d'} options.argon2Type Allows override of the argon2 algorithm type. The default is `argon2id`.\n   * @param {Number} options.argon2TimeCost Allows override of number of argon2 iterations (aka time cost) used to store passwords. The default is 2.\n   * @param {Number} options.argon2MemoryCost Allows override of the amount of memory (in KiB) used by the argon2 algorithm. The default is 19456 (19MB).\n   * @param {Number} options.argon2Parallelism Allows override of the number of threads used by the argon2 algorithm. The default is 1.\n   * @param {MongoFieldSpecifier} options.defaultFieldSelector To exclude by default large custom fields from `Meteor.user()` and `Meteor.findUserBy...()` functions when called without a field selector, and all `onLogin`, `onLoginFailure` and `onLogout` callbacks.  Example: `Accounts.config({ defaultFieldSelector: { myBigArray: 0 }})`. Beware when using this. If, for instance, you do not include `email` when excluding the fields, you can have problems with functions like `forgotPassword` that will break because they won't have the required data available. It's recommend that you always keep the fields `_id`, `username`, and `email`.\n   * @param {String|Mongo.Collection} options.collection A collection name or a Mongo.Collection object to hold the users.\n   * @param {Number} options.loginTokenExpirationHours When using the package `accounts-2fa`, use this to set the amount of time a token sent is valid. As it's just a number, you can use, for example, 0.5 to make the token valid for just half hour. The default is 1 hour.\n   * @param {Number} options.tokenSequenceLength When using the package `accounts-2fa`, use this to the size of the token sequence generated. The default is 6.\n   * @param {'session' | 'local'} options.clientStorage By default login credentials are stored in local storage, setting this to true will switch to using session storage.\n   */\n  config(options) {\n    // We don't want users to accidentally only call Accounts.config on the\n    // client, where some of the options will have partial effects (eg removing\n    // the \"create account\" button from accounts-ui if forbidClientAccountCreation\n    // is set, or redirecting Google login to a specific-domain page) without\n    // having their full effects.\n    if (Meteor.isServer) {\n      __meteor_runtime_config__.accountsConfigCalled = true;\n    } else if (!__meteor_runtime_config__.accountsConfigCalled) {\n      // XXX would be nice to \"crash\" the client and replace the UI with an error\n      // message, but there's no trivial way to do this.\n      Meteor._debug(\n        'Accounts.config was called on the client but not on the ' +\n          'server; some configuration options may not take effect.'\n      );\n    }\n\n    // We need to validate the oauthSecretKey option at the time\n    // Accounts.config is called. We also deliberately don't store the\n    // oauthSecretKey in Accounts._options.\n    if (Object.prototype.hasOwnProperty.call(options, 'oauthSecretKey')) {\n      if (Meteor.isClient) {\n        throw new Error(\n          'The oauthSecretKey option may only be specified on the server'\n        );\n      }\n      if (!Package['oauth-encryption']) {\n        throw new Error(\n          'The oauth-encryption package must be loaded to set oauthSecretKey'\n        );\n      }\n      Package['oauth-encryption'].OAuthEncryption.loadKey(\n        options.oauthSecretKey\n      );\n      options = { ...options };\n      delete options.oauthSecretKey;\n    }\n\n    // Validate config options keys\n    for (const key of Object.keys(options)) {\n      if (!VALID_CONFIG_KEYS.includes(key)) {\n        console.error(`Accounts.config: Invalid key: ${key}`);\n      }\n    }\n\n    // set values in Accounts._options\n    for (const key of VALID_CONFIG_KEYS) {\n      if (key in options) {\n        if (key in this._options) {\n          if (key !== 'collection' && (Meteor.isTest && key !== 'clientStorage')) {\n            throw new Meteor.Error(`Can't set \\`${key}\\` more than once`);\n          }\n        }\n        this._options[key] = options[key];\n      }\n    }\n\n    if (options.collection && options.collection !== this.users._name && options.collection !== this.users) {\n      this.users = this._initializeCollection(options);\n    }\n  }\n\n  /**\n   * @summary Register a callback to be called after a login attempt succeeds.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called when login is successful.\n   *                        The callback receives a single object that\n   *                        holds login details. This object contains the login\n   *                        result type (password, resume, etc.) on both the\n   *                        client and server. `onLogin` callbacks registered\n   *                        on the server also receive extra data, such\n   *                        as user details, connection information, etc.\n   */\n  onLogin(func) {\n    let ret = this._onLoginHook.register(func);\n    // call the just registered callback if already logged in\n    this._startupCallback(ret.callback);\n    return ret;\n  }\n\n  /**\n   * @summary Register a callback to be called after a login attempt fails.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called after the login has failed.\n   */\n  onLoginFailure(func) {\n    return this._onLoginFailureHook.register(func);\n  }\n\n  /**\n   * @summary Register a callback to be called after a logout attempt succeeds.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called when logout is successful.\n   */\n  onLogout(func) {\n    return this._onLogoutHook.register(func);\n  }\n\n  _initConnection(options) {\n    if (!Meteor.isClient) {\n      return;\n    }\n\n    // The connection used by the Accounts system. This is the connection\n    // that will get logged in by Meteor.login(), and this is the\n    // connection whose login state will be reflected by Meteor.userId().\n    //\n    // It would be much preferable for this to be in accounts_client.js,\n    // but it has to be here because it's needed to create the\n    // Meteor.users collection.\n    if (options.connection) {\n      this.connection = options.connection;\n    } else if (options.ddpUrl) {\n      this.connection = DDP.connect(options.ddpUrl);\n    } else if (\n      typeof __meteor_runtime_config__ !== 'undefined' &&\n      __meteor_runtime_config__.ACCOUNTS_CONNECTION_URL\n    ) {\n      // Temporary, internal hook to allow the server to point the client\n      // to a different authentication server. This is for a very\n      // particular use case that comes up when implementing a oauth\n      // server. Unsupported and may go away at any point in time.\n      //\n      // We will eventually provide a general way to use account-base\n      // against any DDP connection, not just one special one.\n      this.connection = DDP.connect(\n        __meteor_runtime_config__.ACCOUNTS_CONNECTION_URL\n      );\n    } else {\n      this.connection = Meteor.connection;\n    }\n  }\n\n  _getTokenLifetimeMs() {\n    // When loginExpirationInDays is set to null, we'll use a really high\n    // number of days (LOGIN_UNEXPIRABLE_TOKEN_DAYS) to simulate an\n    // unexpiring token.\n    const loginExpirationInDays =\n      this._options.loginExpirationInDays === null\n        ? LOGIN_UNEXPIRING_TOKEN_DAYS\n        : this._options.loginExpirationInDays;\n    return (\n      this._options.loginExpiration ||\n      (loginExpirationInDays || DEFAULT_LOGIN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _getPasswordResetTokenLifetimeMs() {\n    return (\n      this._options.passwordResetTokenExpiration ||\n      (this._options.passwordResetTokenExpirationInDays ||\n        DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _getPasswordEnrollTokenLifetimeMs() {\n    return (\n      this._options.passwordEnrollTokenExpiration ||\n      (this._options.passwordEnrollTokenExpirationInDays ||\n        DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _tokenExpiration(when) {\n    // We pass when through the Date constructor for backwards compatibility;\n    // `when` used to be a number.\n    return new Date(new Date(when).getTime() + this._getTokenLifetimeMs());\n  }\n\n  _tokenExpiresSoon(when) {\n    let minLifetimeMs = 0.1 * this._getTokenLifetimeMs();\n    const minLifetimeCapMs = MIN_TOKEN_LIFETIME_CAP_SECS * 1000;\n    if (minLifetimeMs > minLifetimeCapMs) {\n      minLifetimeMs = minLifetimeCapMs;\n    }\n    return new Date() > new Date(when) - minLifetimeMs;\n  }\n\n  // No-op on the server, overridden on the client.\n  _startupCallback(callback) {}\n}\n\n// Note that Accounts is defined separately in accounts_client.js and\n// accounts_server.js.\n\n/**\n * @summary Get the current user id, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n */\nMeteor.userId = () => Accounts.userId();\n\n/**\n * @summary Get the current user record, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n */\nMeteor.user = options => Accounts.user(options);\n\n/**\n * @summary Get the current user record, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n */\nMeteor.userAsync = options => Accounts.userAsync(options);\n\n// how long (in days) until a login token expires\nconst DEFAULT_LOGIN_EXPIRATION_DAYS = 90;\n// how long (in days) until reset password token expires\nconst DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS = 3;\n// how long (in days) until enrol password token expires\nconst DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS = 30;\n// Clients don't try to auto-login with a token that is going to expire within\n// .1 * DEFAULT_LOGIN_EXPIRATION_DAYS, capped at MIN_TOKEN_LIFETIME_CAP_SECS.\n// Tries to avoid abrupt disconnects from expiring tokens.\nconst MIN_TOKEN_LIFETIME_CAP_SECS = 3600; // one hour\n// how often (in milliseconds) we check for expired tokens\nexport const EXPIRE_TOKENS_INTERVAL_MS = 600 * 1000; // 10 minutes\n// A large number of expiration days (approximately 100 years worth) that is\n// used when creating unexpiring tokens.\nconst LOGIN_UNEXPIRING_TOKEN_DAYS = 365 * 100;\n"]}