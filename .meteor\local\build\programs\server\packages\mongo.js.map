{"version": 3, "sources": ["meteor://💻app/packages/mongo/mongo_driver.js", "meteor://💻app/packages/mongo/oplog_tailing.ts", "meteor://💻app/packages/mongo/observe_multiplex.ts", "meteor://💻app/packages/mongo/doc_fetcher.js", "meteor://💻app/packages/mongo/polling_observe_driver.ts", "meteor://💻app/packages/mongo/oplog_observe_driver.js", "meteor://💻app/packages/mongo/oplog_v2_converter.ts", "meteor://💻app/packages/mongo/cursor_description.ts", "meteor://💻app/packages/mongo/mongo_connection.js", "meteor://💻app/packages/mongo/mongo_common.js", "meteor://💻app/packages/mongo/asynchronous_cursor.js", "meteor://💻app/packages/mongo/cursor.ts", "meteor://💻app/packages/mongo/local_collection_driver.js", "meteor://💻app/packages/mongo/remote_collection_driver.ts", "meteor://💻app/packages/mongo/collection/collection.js", "meteor://💻app/packages/mongo/collection/collection_utils.js", "meteor://💻app/packages/mongo/collection/methods_async.js", "meteor://💻app/packages/mongo/collection/methods_index.js", "meteor://💻app/packages/mongo/collection/methods_replication.js", "meteor://💻app/packages/mongo/collection/methods_sync.js", "meteor://💻app/packages/mongo/connection_options.ts", "meteor://💻app/packages/mongo/mongo_utils.js", "meteor://💻app/packages/mongo/observe_handle.ts"], "names": ["module1", "export", "listenAll", "forEachTrigger", "OplogHandle", "link", "v", "MongoConnection", "OplogObserveDriver", "MongoDB", "__reifyWaitForDeps__", "MongoInternals", "global", "__packageName", "NpmModules", "mongodb", "version", "NpmModuleMongodbVersion", "module", "NpmModule", "Proxy", "get", "target", "propertyKey", "receiver", "Meteor", "deprecate", "Reflect", "Connection", "Timestamp", "prototype", "clone", "cursorDescription", "listenCallback", "listeners", "trigger", "push", "DDPServer", "_InvalidationCrossbar", "listen", "stop", "for<PERSON>ach", "listener", "triggerCallback", "key", "collection", "collectionName", "specificIds", "LocalCollection", "_idsMatchedBySelector", "selector", "id", "Object", "assign", "dropCollection", "dropDatabase", "MongoTimestamp", "__reify_async_result__", "_reifyError", "self", "async", "_objectSpread", "default", "OPLOG_COLLECTION", "idForOp", "isEmpty", "CursorDescription", "NpmModuleMongodb", "<PERSON>", "TOO_FAR_BEHIND", "process", "env", "METEOR_OPLOG_TOO_FAR_BEHIND", "TAIL_TIMEOUT", "METEOR_OPLOG_TAIL_TIMEOUT", "constructor", "oplogUrl", "dbN<PERSON>", "_oplogUrl", "_dbName", "_oplogLastEntryConnection", "_oplogTailConnection", "_oplogOptions", "_stopped", "_tail<PERSON><PERSON>le", "_readyPromiseResolver", "_readyPromise", "_crossbar", "_baseOplogSelector", "_catchingUpResolvers", "_lastProcessedTS", "_onSkippedEntriesHook", "_startTrailingPromise", "_resolveTimeout", "_entryQueue", "_DoubleEndedQueue", "_workerActive", "_workerPromise", "Promise", "r", "_Crossbar", "factPackage", "factName", "ns", "RegExp", "_escapeRegExp", "join", "$or", "op", "$in", "$exists", "Hook", "debugPrintExceptions", "_startTailing", "_onOplogEntry", "callback", "Error", "originalCallback", "bindEnvironment", "notification", "err", "_debug", "<PERSON><PERSON><PERSON><PERSON>", "onOplogEntry", "onSkippedEntries", "register", "_waitUntilCaughtUp", "lastEntry", "findOneAsync", "projection", "ts", "sort", "$natural", "e", "sleep", "JSON", "stringify", "lessThanOrEqual", "insertAfter", "length", "greaterThan", "promiseResolver", "promiseToAwait", "clearTimeout", "setTimeout", "console", "error", "splice", "resolver", "waitUntilCaughtUp", "mongodbUri", "require", "parse", "database", "maxPoolSize", "minPoolSize", "_Meteor$settings", "_Meteor$settings$pack", "_Meteor$settings$pack2", "_Meteor$settings2", "_Meteor$settings2$pac", "_Meteor$settings2$pac2", "isMasterDoc", "db", "admin", "command", "ismaster", "setName", "lastOplogEntry", "oplogSelector", "$gt", "includeCollections", "settings", "packages", "mongo", "oplogIncludeCollections", "excludeCollections", "oplogExcludeCollections", "$regex", "$nin", "map", "collName", "concat", "$and", "tailable", "tail", "doc", "_maybeStartWorker", "pop", "clear", "each", "_setLastProcessedTS", "shift", "handleDoc", "sequencer", "_defineTooFarBehind", "value", "_resetTooFarBehind", "o", "_id", "o2", "handle", "applyOps", "nextTimestamp", "add", "ONE", "startsWith", "slice", "drop", "fire", "resolve", "setImmediate", "_objectWithoutProperties", "_excluded", "ObserveMultiplexer", "_ref", "_this", "ordered", "onStop", "_ordered", "_onStop", "_queue", "_handles", "_resolver", "_isReady", "_cache", "_addHandleTasksScheduledButNotPerformed", "undefined", "Package", "Facts", "incrementServerFact", "_AsynchronousQueue", "then", "_CachingChangeObserver", "callback<PERSON><PERSON><PERSON>", "callback<PERSON><PERSON>", "_len", "arguments", "args", "Array", "_key", "_applyCallback", "addHandleAndSendInitialAdds", "_addHandleAndSendInitialAdds", "runTask", "_sendAdds", "<PERSON><PERSON><PERSON><PERSON>", "_ready", "_stop", "options", "fromQueryError", "ready", "queueTask", "queryError", "onFlush", "cb", "applyChange", "apply", "handleId", "keys", "initialAddsSent", "nonMutatingCallbacks", "EJSON", "_addedBefore", "_added", "addPromises", "docs", "_ref2", "fields", "promise", "all", "initialAddsSentResolver", "<PERSON><PERSON><PERSON><PERSON>", "mongoConnection", "_mongoConnection", "_callbacksForOp", "Map", "fetch", "check", "String", "has", "callbacks", "set", "delete", "PollingObserveDriver", "throttle", "POLLING_THROTTLE_MS", "METEOR_POLLING_THROTTLE_MS", "POLLING_INTERVAL_MS", "METEOR_POLLING_INTERVAL_MS", "_options", "_cursorDescription", "_mongo<PERSON>andle", "_multiplexer", "_stopCallbacks", "_cursor", "_results", "_pollsScheduledButNotStarted", "_pendingWrites", "_ensurePollIsScheduled", "_taskQueue", "_testOn<PERSON><PERSON>oll<PERSON><PERSON>back", "mongoHandle", "multiplexer", "_createAsynchronousCursor", "_unthrottledEnsurePollIsScheduled", "bind", "pollingThrottleMs", "_init", "_Package$factsBase", "listenersHandle", "fence", "_getCurrentFence", "beginWrite", "pollingInterval", "pollingIntervalMs", "_pollingInterval", "intervalHandle", "setInterval", "clearInterval", "_pollMongo", "_suspendPolling", "_resumePolling", "_this$_testOnlyPollCa", "first", "newResults", "oldResults", "_IdMap", "call", "writesForCycle", "getRawObjects", "code", "message", "_diffQuery<PERSON><PERSON>es", "w", "committed", "_Package$factsBase2", "_asyncIterator", "oplogV2V1Converter", "Match", "<PERSON><PERSON><PERSON>", "PHASE", "QUERYING", "FETCHING", "STEADY", "SwitchedToQuery", "finishIfNeedToPollQuery", "f", "currentId", "_usesOplog", "sorter", "comparator", "getComparator", "limit", "heapOptions", "IdMap", "_limit", "_comparator", "_sorter", "_<PERSON><PERSON><PERSON>er", "MinMaxHeap", "_published", "MaxHeap", "_safeAppendToBuffer", "_stop<PERSON><PERSON><PERSON>", "_addStopHandles", "newStopHandles", "expectedPattern", "ObjectIncluding", "Function", "OneOf", "_registerPhaseChange", "_matcher", "matcher", "_projectionFn", "_compileProjection", "_sharedProjection", "combineIntoProjection", "_sharedProjectionFn", "_needToFetch", "_currentlyFetching", "_fetchGeneration", "_requeryWhenDoneThisQuery", "_writesToCommitWhenWeReachSteady", "_oplogHandle", "_needToPollQuery", "_phase", "_handleOplogEntryQuerying", "_handleOplogEntrySteadyOrFetching", "fired", "_oplogObserveDrivers", "onBeforeFire", "drivers", "driver", "values", "write", "_onFailover", "_runInitialQuery", "_addPublished", "_noYieldsAllowed", "added", "size", "overflowingDocId", "maxElementId", "overflowingDoc", "equals", "remove", "removed", "_addBuffered", "_removePublished", "empty", "newDocId", "minElementId", "newDoc", "_removeBuffered", "_changePublished", "oldDoc", "projectedNew", "projectedOld", "changed", "DiffSequence", "make<PERSON><PERSON>edFields", "maxBufferedId", "_addMatching", "maxPublished", "maxBuffered", "toPublish", "canAppendToBuffer", "canInsertIntoBuffer", "<PERSON><PERSON><PERSON><PERSON>", "_removeMatching", "_handleDoc", "matchesNow", "documentMatches", "result", "publishedBefore", "bufferedBefore", "cachedBefore", "minBuffered", "staysInPublished", "staysInBuffer", "_fetchModifiedDocuments", "defer", "thisGeneration", "fetchPromises", "fetchPromise", "reject", "_doc<PERSON><PERSON>cher", "results", "allSettled", "errors", "filter", "status", "reason", "_beSteady", "writes", "isReplace", "canDirectlyModifyDoc", "modifierCanBeDirectlyApplied", "_modify", "name", "canBecomeTrueByModifier", "affectedByModifier", "_runInitialQueryAsync", "_runQuery", "initial", "_doneQuerying", "_pollQuery", "_runQueryAsync", "new<PERSON>uffer", "cursor", "_cursor<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "_sleepForMs", "_publishNewResults", "optionsOverwrite", "transform", "description", "idsToRemove", "_oplogEntryHandle", "_listeners<PERSON><PERSON>le", "_iteratorAbruptCompletion", "_didIteratorError", "_iteratorError", "_iterator", "_step", "next", "done", "return", "phase", "now", "Date", "timeDiff", "_phaseStartTime", "cursorSupported", "disableOplog", "_disableOplog", "skip", "_checkSupportedProjection", "hasWhere", "has<PERSON>eo<PERSON><PERSON><PERSON>", "modifier", "entries", "every", "operation", "field", "test", "arrayOperatorKeyRegex", "isArrayOperatorKey", "isArrayOperator", "operator", "a", "prefix", "flattenObjectInto", "source", "isArray", "Mongo", "ObjectID", "_isCustomType", "convertOplogDiff", "oplogEntry", "diff", "diff<PERSON><PERSON>", "_oplogEntry$$unset", "$unset", "_oplogEntry$$set", "$set", "_oplogEntry$$set2", "_ref3", "fieldValue", "_ref4", "position", "<PERSON><PERSON><PERSON>", "_oplogEntry$$unset2", "_oplogEntry$$set3", "$v", "convertedOplogEntry", "Collection", "_rewriteSelector", "CLIENT_ONLY_METHODS", "getAsyncMethodName", "path", "AsynchronousCursor", "replaceMeteorAtomWithMongo", "replaceTypes", "transformResult", "ObserveHandle", "FILE_ASSET_SUFFIX", "ASSETS_FOLDER", "APP_FOLDER", "oplogCollectionWarnings", "url", "_observeMultiplexers", "_onFailoverHook", "userOptions", "_connectionOptions", "mongoOptions", "ignoreUndefined", "endsWith", "optionName", "replace", "Assets", "getServerDir", "driverInfo", "release", "client", "MongoClient", "on", "event", "previousDescription", "type", "newDescription", "databaseName", "_close", "oplogHandle", "close", "_setOplogHandle", "rawCollection", "createCappedCollectionAsync", "byteSize", "maxDocuments", "createCollection", "capped", "max", "_maybeBeginWrite", "insertAsync", "collection_name", "document", "_expectedByTest", "_isPlainObject", "refresh", "insertOne", "safe", "insertedId", "catch", "_refresh", "refresh<PERSON><PERSON>", "removeAsync", "deleteMany", "deletedCount", "modifiedCount", "numberAffected", "dropCollectionAsync", "dropDatabaseAsync", "_dropDatabase", "updateAsync", "mod", "mongoOpts", "arrayFilters", "upsert", "multi", "fullResult", "mongoSelector", "mongoMod", "isModify", "_isModificationMod", "_forbid<PERSON><PERSON>lace", "knownId", "_createUpsertDocument", "generatedId", "simulateUpsertWithInsertedId", "_returnObject", "hasOwnProperty", "$setOnInsert", "strings", "updateMethod", "meteorResult", "ObjectId", "toHexString", "_isCannotChangeIdError", "errmsg", "indexOf", "upsertAsync", "find", "createIndexAsync", "index", "createIndex", "countDocuments", "arg", "estimatedDocumentCount", "_len2", "_key2", "ensureIndexAsync", "dropIndexAsync", "indexName", "dropIndex", "m", "NUM_OPTIMISTIC_TRIES", "mongoOptsForUpdate", "mongoOptsForInsert", "replacementWithId", "tries", "doUpdate", "method", "updateMany", "some", "replaceOne", "upsertedCount", "upsertedId", "doConditionalInsert", "_observeChangesTailable", "addedBefore", "selfForIteration", "useTransform", "cursorOptions", "readPreference", "numberOfRetries", "dbCursor", "addCursorFlag", "maxTimeMs", "maxTimeMS", "hint", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeoutMS", "stopped", "lastTS", "loop", "_nextObjectPromiseWithTimeout", "newSelector", "_observe<PERSON><PERSON>es", "_self$_oplogHandle", "fieldsOptions", "<PERSON><PERSON><PERSON>", "observeDriver", "firstHandle", "<PERSON><PERSON><PERSON><PERSON>", "oplogOptions", "canUseOplog", "includes", "warn", "Minimongo", "Matcher", "Sorter", "driverClass", "_observeDriver", "writeCallback", "replaceMongoAtomWithMeteor", "replaceNames", "refreshErr", "driver<PERSON><PERSON><PERSON>", "mongoResult", "n", "matchedCount", "isBinary", "Binary", "<PERSON><PERSON><PERSON>", "from", "Decimal", "Decimal128", "fromString", "toString", "makeMongoLegal", "toJSONValue", "atomTransformer", "replacedTopLevelAtom", "ret", "val", "valReplaced", "sub_type", "buffer", "Uint8Array", "fromJSONValue", "unmakeMongoLegal", "substr", "thing", "_dbCursor", "_selfForIteration", "_transform", "wrapTransform", "_visitedIds", "Symbol", "asyncIterator", "_nextObjectPromise", "_rawNextObjectPromise", "nextObjectPromise", "timeoutErr", "timeoutPromise", "race", "thisArg", "_rewind", "idx", "rewind", "count", "ASYNC_CURSOR_METHODS", "_mongo", "_synchronousCursor", "countAsync", "getTransform", "_publishCursor", "sub", "_getCollectionName", "observe", "_observeFromObserveChanges", "observeAsync", "observe<PERSON>hanges", "_observeChangesCallbacksAreOrdered", "observeChangesAsync", "iterator", "methodName", "setupAsynchronousCursor", "methodNameAsync", "LocalCollectionDriver", "noConnCollections", "create", "open", "conn", "ensureCollection", "_mongo_livedata_collections", "collections", "RemoteCollectionDriver", "once", "ASYNC_COLLECTION_METHODS", "mongoUrl", "REMOTE_COLLECTION_METHODS", "mongoMethod", "asyncMethodName", "defaultRemoteCollectionDriver", "connectionOptions", "MONGO_URL", "MONGO_OPLOG_URL", "startup", "connect", "normalizeProjection", "AsyncMethods", "SyncMethods", "IndexMethods", "ID_GENERATORS", "normalizeOptions", "setupAutopublish", "setupConnection", "setupDriver", "setupMutationMethods", "validateCollectionName", "ReplicationMethods", "_ID_GENERATORS$option", "_ID_GENERATORS", "_makeNewID", "idGeneration", "resolverType", "_connection", "_driver", "_collection", "_name", "_settingUpReplicationPromise", "_maybeSetUpReplication", "_collections", "_getFindSelector", "_getFindOptions", "newOptions", "Optional", "Number", "fallbackId", "_selectorIsId", "Random", "_isRemoteCollection", "server", "rawDatabase", "getCollection", "MongoID", "AllowDeny", "CollectionPrototype", "MONGO", "src", "DDP", "randomStream", "insecure", "hexString", "STRING", "connection", "isClient", "autopublish", "_preventAutopublish", "publish", "is_auto", "defineMutationMethods", "_defineMutationMethods", "useExisting", "_suppressSameNameError", "methods", "manager", "_insertAsync", "getPrototypeOf", "getOwnPropertyDescriptors", "generateId", "enclosing", "_CurrentMethodInvocation", "chooseReturnValueFromCollectionResult", "_isPromise", "_callMutatorMethodAsync", "stubPromise", "serverPromise", "Log", "debug", "reCreateIndexOnOptionMismatch", "info", "_registerStoreResult", "_registerStoreResult$", "registerStoreClient", "registerStoreServer", "wrappedStoreCommon", "saveOriginals", "retrieveOriginals", "_getCollection", "wrappedStoreClient", "beginUpdate", "batchSize", "reset", "pauseObservers", "update", "msg", "mongoId", "idParse", "_docs", "insert", "endUpdate", "resumeObserversClient", "getDoc", "findOne", "wrappedStoreServer", "resumeObserversServer", "registerStoreResult", "log<PERSON>arn", "log", "ok", "_insert", "wrappedCallback", "wrapCallback", "_callMutatorMethod", "_len3", "optionsAndCallback", "_key3", "popCallbackFromArgs", "convertResult", "setConnectionOptions", "otherOptions", "nextObserveHandleId", "_changed", "_movedBefore", "_removed", "before", "timeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,OAAO,CAACC,MAAM,CAAC;MAACC,SAAS,EAACA,CAAA,KAAIA,SAAS;MAACC,cAAc,EAACA,CAAA,KAAIA;IAAc,CAAC,CAAC;IAAC,IAAIC,WAAW;IAACJ,OAAO,CAACK,IAAI,CAAC,iBAAiB,EAAC;MAACD,WAAWA,CAACE,CAAC,EAAC;QAACF,WAAW,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,eAAe;IAACP,OAAO,CAACK,IAAI,CAAC,oBAAoB,EAAC;MAACE,eAAeA,CAACD,CAAC,EAAC;QAACC,eAAe,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,kBAAkB;IAACR,OAAO,CAACK,IAAI,CAAC,wBAAwB,EAAC;MAACG,kBAAkBA,CAACF,CAAC,EAAC;QAACE,kBAAkB,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,OAAO;IAACT,OAAO,CAACK,IAAI,CAAC,gBAAgB,EAAC;MAACI,OAAOA,CAACH,CAAC,EAAC;QAACG,OAAO,GAACH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAK9eC,cAAc,GAAGC,MAAM,CAACD,cAAc,GAAG,CAAC,CAAC;IAE3CA,cAAc,CAACE,aAAa,GAAG,OAAO;IAEtCF,cAAc,CAACG,UAAU,GAAG;MAC1BC,OAAO,EAAE;QACPC,OAAO,EAAEC,uBAAuB;QAChCC,MAAM,EAAET;MACV;IACF,CAAC;;IAED;IACA;IACA;IACA;IACAE,cAAc,CAACQ,SAAS,GAAG,IAAIC,KAAK,CAACX,OAAO,EAAE;MAC5CY,GAAGA,CAACC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,EAAE;QACjC,IAAID,WAAW,KAAK,UAAU,EAAE;UAC9BE,MAAM,CAACC,SAAS,CACd,6HAEF,CAAC;QACH;QACA,OAAOC,OAAO,CAACN,GAAG,CAACC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,CAAC;MACnD;IACF,CAAC,CAAC;IAEFb,cAAc,CAACP,WAAW,GAAGA,WAAW;IAExCO,cAAc,CAACiB,UAAU,GAAGrB,eAAe;IAE3CI,cAAc,CAACH,kBAAkB,GAAGA,kBAAkB;;IAEtD;IACA;;IAGA;IACA;IACA;IACAC,OAAO,CAACoB,SAAS,CAACC,SAAS,CAACC,KAAK,GAAG,YAAY;MAC9C;MACA,OAAO,IAAI;IACb,CAAC;;IAED;IACA;IACA;IACA;IACA;;IAEO,MAAM7B,SAAS,GAAG,eAAAA,CAAgB8B,iBAAiB,EAAEC,cAAc,EAAE;MAC1E,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAM/B,cAAc,CAAC6B,iBAAiB,EAAE,UAAUG,OAAO,EAAE;QACzDD,SAAS,CAACE,IAAI,CAACC,SAAS,CAACC,qBAAqB,CAACC,MAAM,CACnDJ,OAAO,EAAEF,cAAc,CAAC,CAAC;MAC7B,CAAC,CAAC;MAEF,OAAO;QACLO,IAAI,EAAE,SAAAA,CAAA,EAAY;UAChBN,SAAS,CAACO,OAAO,CAAC,UAAUC,QAAQ,EAAE;YACpCA,QAAQ,CAACF,IAAI,CAAC,CAAC;UACjB,CAAC,CAAC;QACJ;MACF,CAAC;IACH,CAAC;IAEM,MAAMrC,cAAc,GAAG,eAAAA,CAAgB6B,iBAAiB,EAAEW,eAAe,EAAE;MAChF,MAAMC,GAAG,GAAG;QAACC,UAAU,EAAEb,iBAAiB,CAACc;MAAc,CAAC;MAC1D,MAAMC,WAAW,GAAGC,eAAe,CAACC,qBAAqB,CACvDjB,iBAAiB,CAACkB,QAAQ,CAAC;MAC7B,IAAIH,WAAW,EAAE;QACf,KAAK,MAAMI,EAAE,IAAIJ,WAAW,EAAE;UAC5B,MAAMJ,eAAe,CAACS,MAAM,CAACC,MAAM,CAAC;YAACF,EAAE,EAAEA;UAAE,CAAC,EAAEP,GAAG,CAAC,CAAC;QACrD;QACA,MAAMD,eAAe,CAACS,MAAM,CAACC,MAAM,CAAC;UAACC,cAAc,EAAE,IAAI;UAAEH,EAAE,EAAE;QAAI,CAAC,EAAEP,GAAG,CAAC,CAAC;MAC7E,CAAC,MAAM;QACL,MAAMD,eAAe,CAACC,GAAG,CAAC;MAC5B;MACA;MACA,MAAMD,eAAe,CAAC;QAAEY,YAAY,EAAE;MAAK,CAAC,CAAC;IAC/C,CAAC;IAID;IACA;IACA;IACA5C,cAAc,CAAC6C,cAAc,GAAG/C,OAAO,CAACoB,SAAS;IAAC4B,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC7FlD,IAAAC,aAAc;IAAA3C,MAAM,CAAAb,IAAA,uCAAiB;MAAAyD,QAAAxD,CAAA;QAAAuD,aAAA,GAAAvD,CAAA;MAAA;IAAA;IAArCY,MAAA,CAAOjB,MAAA,CAAO;MAAA8D,gBAAM,EAAAA,CAAA,KAAgBA,gBAAC;MAAA3D,WAAA,EAAAA,CAAA,KAAAA,WAAA;MAAA4D,OAAA,EAAAA,CAAA,KAAAA;IAAA;IAAA,IAAAC,OAAA;IAAA/C,MAAA,CAAAb,IAAA;MAAAyD,QAAAxD,CAAA;QAAA2D,OAAA,GAAA3D,CAAA;MAAA;IAAA;IAAA,IAAAmB,MAAA;IAAAP,MAAA,CAAAb,IAAA;MAAAoB,OAAAnB,CAAA;QAAAmB,MAAA,GAAAnB,CAAA;MAAA;IAAA;IAAA,IAAA4D,iBAAA;IAAAhD,MAAA,CAAAb,IAAA;MAAA6D,kBAAA5D,CAAA;QAAA4D,iBAAA,GAAA5D,CAAA;MAAA;IAAA;IAAA,IAAAC,eAAA;IAAAW,MAAA,CAAAb,IAAA;MAAAE,gBAAAD,CAAA;QAAAC,eAAA,GAAAD,CAAA;MAAA;IAAA;IAAA,IAAA6D,gBAAA;IAAAjD,MAAA,CAAAb,IAAA;MAAA8D,iBAAA7D,CAAA;QAAA6D,gBAAA,GAAA7D,CAAA;MAAA;IAAA;IAAA,IAAAI,oBAAA,WAAAA,oBAAA;IAMrC,MAAM;MAAE0D;IAAI,CAAE,GAAGD,gBAAgB;IAE1B,MAAMJ,gBAAgB,GAAG,UAAU;IAE1C,IAAIM,cAAc,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,2BAA2B,IAAI,IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,EAAEH,OAAO,CAACC,GAAG,CAACG,yBAAyB,IAAI,KAAK,CAAC;IAuBhE,MAAOtE,WAAW;MAsBtBuE,YAAYC,QAAgB,EAAEC,MAAc;QAAA,KArBpCC,SAAS;QAAA,KACVC,OAAO;QAAA,KACNC,yBAAyB;QAAA,KACzBC,oBAAoB;QAAA,KACpBC,aAAa;QAAA,KACbC,QAAQ;QAAA,KACRC,WAAW;QAAA,KACXC,qBAAqB;QAAA,KACrBC,aAAa;QAAA,KACdC,SAAS;QAAA,KACRC,kBAAkB;QAAA,KAClBC,oBAAoB;QAAA,KACpBC,gBAAgB;QAAA,KAChBC,qBAAqB;QAAA,KACrBC,qBAAqB;QAAA,KACrBC,eAAe;QAAA,KAEfC,WAAW,GAAG,IAAIrE,MAAM,CAACsE,iBAAiB,EAAE;QAAA,KAC5CC,aAAa,GAAG,KAAK;QAAA,KACrBC,cAAc,GAAyB,IAAI;QAGjD,IAAI,CAACnB,SAAS,GAAGF,QAAQ;QACzB,IAAI,CAACG,OAAO,GAAGF,MAAM;QAErB,IAAI,CAACgB,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACb,yBAAyB,GAAG,IAAI;QACrC,IAAI,CAACC,oBAAoB,GAAG,IAAI;QAChC,IAAI,CAACC,aAAa,GAAG,IAAI;QACzB,IAAI,CAACC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACC,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,qBAAqB,GAAG,IAAI;QACjC,IAAI,CAACC,aAAa,GAAG,IAAIY,OAAO,CAACC,CAAC,IAAI,IAAI,CAACd,qBAAqB,GAAGc,CAAC,CAAC;QACrE,IAAI,CAACZ,SAAS,GAAG,IAAIlD,SAAS,CAAC+D,SAAS,CAAC;UACvCC,WAAW,EAAE,gBAAgB;UAAEC,QAAQ,EAAE;SAC1C,CAAC;QACF,IAAI,CAACd,kBAAkB,GAAG;UACxBe,EAAE,EAAE,IAAIC,MAAM,CAAC,MAAM,GAAG;UACtB;UACA/E,MAAM,CAACgF,aAAa,CAAC,IAAI,CAAC1B,OAAO,GAAG,GAAG,CAAC;UACxC;UACAtD,MAAM,CAACgF,aAAa,CAAC,YAAY,CAAC,CACnC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;UAElBC,GAAG,EAAE,CACH;YAAEC,EAAE,EAAE;cAAEC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;YAAC;UAAE,CAAE,EAChC;YAAED,EAAE,EAAE,GAAG;YAAE,QAAQ,EAAE;cAAEE,OAAO,EAAE;YAAI;UAAE,CAAE,EACxC;YAAEF,EAAE,EAAE,GAAG;YAAE,gBAAgB,EAAE;UAAC,CAAE,EAChC;YAAEA,EAAE,EAAE,GAAG;YAAE,YAAY,EAAE;cAAEE,OAAO,EAAE;YAAI;UAAE,CAAE;SAE/C;QAED,IAAI,CAACrB,oBAAoB,GAAG,EAAE;QAC9B,IAAI,CAACC,gBAAgB,GAAG,IAAI;QAE5B,IAAI,CAACC,qBAAqB,GAAG,IAAIoB,IAAI,CAAC;UACpCC,oBAAoB,EAAE;SACvB,CAAC;QAEF,IAAI,CAACpB,qBAAqB,GAAG,IAAI,CAACqB,aAAa,EAAE;MACnD;MAEA,MAAMzE,IAAIA,CAAA;QACR,IAAI,IAAI,CAAC2C,QAAQ,EAAE;QACnB,IAAI,CAACA,QAAQ,GAAG,IAAI;QACpB,IAAI,IAAI,CAACC,WAAW,EAAE;UACpB,MAAM,IAAI,CAACA,WAAW,CAAC5C,IAAI,EAAE;QAC/B;MACF;MAEA,MAAM0E,aAAaA,CAAC/E,OAAqB,EAAEgF,QAAkB;QAC3D,IAAI,IAAI,CAAChC,QAAQ,EAAE;UACjB,MAAM,IAAIiC,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,MAAM,IAAI,CAAC9B,aAAa;QAExB,MAAM+B,gBAAgB,GAAGF,QAAQ;QAEjC;;;;;QAKAA,QAAQ,GAAG1F,MAAM,CAAC6F,eAAe,CAC/B,UAAUC,YAAiB;UACzBF,gBAAgB,CAACE,YAAY,CAAC;QAChC,CAAC;QACD;QACA,UAAUC,GAAG;UACX/F,MAAM,CAACgG,MAAM,CAAC,yBAAyB,EAAED,GAAG,CAAC;QAC/C,CAAC,CACF;QAED,MAAME,YAAY,GAAG,IAAI,CAACnC,SAAS,CAAChD,MAAM,CAACJ,OAAO,EAAEgF,QAAQ,CAAC;QAC7D,OAAO;UACL3E,IAAI,EAAE,eAAAA,CAAA,EAAK;YACT,MAAMkF,YAAY,CAAClF,IAAI,EAAE;UAC3B;SACD;MACH;MAEAmF,YAAYA,CAACxF,OAAqB,EAAEgF,QAAkB;QACpD,OAAO,IAAI,CAACD,aAAa,CAAC/E,OAAO,EAAEgF,QAAQ,CAAC;MAC9C;MAEAS,gBAAgBA,CAACT,QAAkB;QACjC,IAAI,IAAI,CAAChC,QAAQ,EAAE;UACjB,MAAM,IAAIiC,KAAK,CAAC,4CAA4C,CAAC;QAC/D;QACA,OAAO,IAAI,CAACzB,qBAAqB,CAACkC,QAAQ,CAACV,QAAQ,CAAC;MACtD;MAEA,MAAMW,kBAAkBA,CAAA;QACtB,IAAI,IAAI,CAAC3C,QAAQ,EAAE;UACjB,MAAM,IAAIiC,KAAK,CAAC,6CAA6C,CAAC;QAChE;QAEA,MAAM,IAAI,CAAC9B,aAAa;QAExB,IAAIyC,SAAS,GAAsB,IAAI;QAEvC,OAAO,CAAC,IAAI,CAAC5C,QAAQ,EAAE;UACrB,IAAI;YACF4C,SAAS,GAAG,MAAM,IAAI,CAAC/C,yBAAyB,CAACgD,YAAY,CAC3DjE,gBAAgB,EAChB,IAAI,CAACyB,kBAAkB,EACvB;cAAEyC,UAAU,EAAE;gBAAEC,EAAE,EAAE;cAAC,CAAE;cAAEC,IAAI,EAAE;gBAAEC,QAAQ,EAAE,CAAC;cAAC;YAAE,CAAE,CAClD;YACD;UACF,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV5G,MAAM,CAACgG,MAAM,CAAC,wCAAwC,EAAEY,CAAC,CAAC;YAC1D;YACA,MAAM5G,MAAM,CAAC6G,KAAK,CAAC,GAAG,CAAC;UACzB;QACF;QAEA,IAAI,IAAI,CAACnD,QAAQ,EAAE;QAEnB,IAAI,CAAC4C,SAAS,EAAE;QAEhB,MAAMG,EAAE,GAAGH,SAAS,CAACG,EAAE;QACvB,IAAI,CAACA,EAAE,EAAE;UACP,MAAMd,KAAK,CAAC,0BAA0B,GAAGmB,IAAI,CAACC,SAAS,CAACT,SAAS,CAAC,CAAC;QACrE;QAEA,IAAI,IAAI,CAACrC,gBAAgB,IAAIwC,EAAE,CAACO,eAAe,CAAC,IAAI,CAAC/C,gBAAgB,CAAC,EAAE;UACtE;QACF;QAEA,IAAIgD,WAAW,GAAG,IAAI,CAACjD,oBAAoB,CAACkD,MAAM;QAElD,OAAOD,WAAW,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAACjD,oBAAoB,CAACiD,WAAW,GAAG,CAAC,CAAC,CAACR,EAAE,CAACU,WAAW,CAACV,EAAE,CAAC,EAAE;UAC3FQ,WAAW,EAAE;QACf;QAEA,IAAIG,eAAe,GAAG,IAAI;QAE1B,MAAMC,cAAc,GAAG,IAAI5C,OAAO,CAACC,CAAC,IAAI0C,eAAe,GAAG1C,CAAC,CAAC;QAE5D4C,YAAY,CAAC,IAAI,CAAClD,eAAe,CAAC;QAElC,IAAI,CAACA,eAAe,GAAGmD,UAAU,CAAC,MAAK;UACrCC,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAE;YAAEhB;UAAE,CAAE,CAAC;QAClE,CAAC,EAAE,KAAK,CAAC;QAET,IAAI,CAACzC,oBAAoB,CAAC0D,MAAM,CAACT,WAAW,EAAE,CAAC,EAAE;UAAER,EAAE;UAAEkB,QAAQ,EAAEP;QAAgB,CAAE,CAAC;QAEpF,MAAMC,cAAc;QAEpBC,YAAY,CAAC,IAAI,CAAClD,eAAe,CAAC;MACpC;MAEA,MAAMwD,iBAAiBA,CAAA;QACrB,OAAO,IAAI,CAACvB,kBAAkB,EAAE;MAClC;MAEA,MAAMb,aAAaA,CAAA;QACjB,MAAMqC,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;QACzC,IAAID,UAAU,CAACE,KAAK,CAAC,IAAI,CAAC1E,SAAS,CAAC,CAAC2E,QAAQ,KAAK,OAAO,EAAE;UACzD,MAAM,IAAIrC,KAAK,CAAC,6EAA6E,CAAC;QAChG;QAEA,IAAI,CAACnC,oBAAoB,GAAG,IAAI1E,eAAe,CAC7C,IAAI,CAACuE,SAAS,EAAE;UAAE4E,WAAW,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAC,CAAE,CACnD;QACD,IAAI,CAAC3E,yBAAyB,GAAG,IAAIzE,eAAe,CAClD,IAAI,CAACuE,SAAS,EAAE;UAAE4E,WAAW,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAC,CAAE,CACnD;QAED,IAAI;UAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACF,MAAMC,WAAW,GAAG,MAAM,IAAI,CAAClF,yBAA0B,CAACmF,EAAE,CACzDC,KAAK,EAAE,CACPC,OAAO,CAAC;YAAEC,QAAQ,EAAE;UAAC,CAAE,CAAC;UAE3B,IAAI,EAAEJ,WAAW,IAAIA,WAAW,CAACK,OAAO,CAAC,EAAE;YACzC,MAAM,IAAInD,KAAK,CAAC,6EAA6E,CAAC;UAChG;UAEA,MAAMoD,cAAc,GAAG,MAAM,IAAI,CAACxF,yBAAyB,CAACgD,YAAY,CACtEjE,gBAAgB,EAChB,EAAE,EACF;YAAEoE,IAAI,EAAE;cAAEC,QAAQ,EAAE,CAAC;YAAC,CAAE;YAAEH,UAAU,EAAE;cAAEC,EAAE,EAAE;YAAC;UAAE,CAAE,CAClD;UAED,IAAIuC,aAAa,GAAA5G,aAAA,KAAa,IAAI,CAAC2B,kBAAkB,CAAE;UACvD,IAAIgF,cAAc,EAAE;YAClBC,aAAa,CAACvC,EAAE,GAAG;cAAEwC,GAAG,EAAEF,cAAc,CAACtC;YAAE,CAAE;YAC7C,IAAI,CAACxC,gBAAgB,GAAG8E,cAAc,CAACtC,EAAE;UAC3C;UAEA,MAAMyC,kBAAkB,IAAAf,gBAAA,GAAGnI,MAAM,CAACmJ,QAAQ,cAAAhB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBiB,QAAQ,cAAAhB,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BiB,KAAK,cAAAhB,sBAAA,uBAAhCA,sBAAA,CAAkCiB,uBAAuB;UACpF,MAAMC,kBAAkB,IAAAjB,iBAAA,GAAGtI,MAAM,CAACmJ,QAAQ,cAAAb,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiBc,QAAQ,cAAAb,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2Bc,KAAK,cAAAb,sBAAA,uBAAhCA,sBAAA,CAAkCgB,uBAAuB;UAEpF,IAAIN,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEhC,MAAM,IAAIqC,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAErC,MAAM,EAAE;YAC5D,MAAM,IAAIvB,KAAK,CAAC,2GAA2G,CAAC;UAC9H;UAEA,IAAI4D,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAErC,MAAM,EAAE;YAC9B8B,aAAa,CAAClE,EAAE,GAAG;cACjB2E,MAAM,EAAET,aAAa,CAAClE,EAAE;cACxB4E,IAAI,EAAEH,kBAAkB,CAACI,GAAG,CAAEC,QAAgB,OAAAC,MAAA,CAAQ,IAAI,CAACvG,OAAO,OAAAuG,MAAA,CAAID,QAAQ,CAAE;aACjF;YACD,IAAI,CAACnG,aAAa,GAAG;cAAE8F;YAAkB,CAAE;UAC7C,CAAC,MAAM,IAAIL,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEhC,MAAM,EAAE;YACrC8B,aAAa,GAAG;cACdc,IAAI,EAAE,CACJ;gBACE5E,GAAG,EAAE,CACH;kBAAEJ,EAAE,EAAE;gBAAe,CAAE,EACvB;kBAAEA,EAAE,EAAE;oBAAEM,GAAG,EAAE8D,kBAAkB,CAACS,GAAG,CAAEC,QAAgB,OAAAC,MAAA,CAAQ,IAAI,CAACvG,OAAO,OAAAuG,MAAA,CAAID,QAAQ,CAAE;kBAAC;gBAAE,CAAE;eAE/F,EACD;gBAAE1E,GAAG,EAAE8D,aAAa,CAAC9D;cAAG,CAAE,EAC1B;gBAAEuB,EAAE,EAAEuC,aAAa,CAACvC;cAAE,CAAE;aAE3B;YACD,IAAI,CAAChD,aAAa,GAAG;cAAEyF;YAAkB,CAAE;UAC7C;UAEA,MAAM3I,iBAAiB,GAAG,IAAIkC,iBAAiB,CAC7CH,gBAAgB,EAChB0G,aAAa,EACb;YAAEe,QAAQ,EAAE;UAAI,CAAE,CACnB;UAED,IAAI,CAACpG,WAAW,GAAG,IAAI,CAACH,oBAAoB,CAACwG,IAAI,CAC/CzJ,iBAAiB,EAChB0J,GAAQ,IAAI;YACX,IAAI,CAAC5F,WAAW,CAAC1D,IAAI,CAACsJ,GAAG,CAAC;YAC1B,IAAI,CAACC,iBAAiB,EAAE;UAC1B,CAAC,EACDlH,YAAY,CACb;UAED,IAAI,CAACY,qBAAsB,EAAE;QAC/B,CAAC,CAAC,OAAO6D,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAMA,KAAK;QACb;MACF;MAEQyC,iBAAiBA,CAAA;QACvB,IAAI,IAAI,CAAC1F,cAAc,EAAE;QACzB,IAAI,CAACD,aAAa,GAAG,IAAI;QAEzB;QACA,IAAI,CAACC,cAAc,GAAG,CAAC,YAAW;UAChC,IAAI;YACF,OAAO,CAAC,IAAI,CAACd,QAAQ,IAAI,CAAC,IAAI,CAACW,WAAW,CAAC7B,OAAO,EAAE,EAAE;cACpD;cACA;cACA,IAAI,IAAI,CAAC6B,WAAW,CAAC6C,MAAM,GAAGtE,cAAc,EAAE;gBAC5C,MAAM0D,SAAS,GAAG,IAAI,CAACjC,WAAW,CAAC8F,GAAG,EAAE;gBACxC,IAAI,CAAC9F,WAAW,CAAC+F,KAAK,EAAE;gBAExB,IAAI,CAAClG,qBAAqB,CAACmG,IAAI,CAAE3E,QAAkB,IAAI;kBACrDA,QAAQ,EAAE;kBACV,OAAO,IAAI;gBACb,CAAC,CAAC;gBAEF;gBACA;gBACA,IAAI,CAAC4E,mBAAmB,CAAChE,SAAS,CAACG,EAAE,CAAC;gBACtC;cACF;cAEA;cACA,MAAMwD,GAAG,GAAG,IAAI,CAAC5F,WAAW,CAACkG,KAAK,EAAE;cAEpC,IAAI;gBACF,MAAMC,SAAS,CAAC,IAAI,EAAEP,GAAG,CAAC;gBAC1B;gBACA,IAAIA,GAAG,CAACxD,EAAE,EAAE;kBACV,IAAI,CAAC6D,mBAAmB,CAACL,GAAG,CAACxD,EAAE,CAAC;gBAClC;cACF,CAAC,CAAC,OAAOG,CAAC,EAAE;gBACV;gBACAY,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEb,CAAC,CAAC;cACnD;YACF;UACF,CAAC,SAAS;YACR,IAAI,CAACpC,cAAc,GAAG,IAAI;YAC1B,IAAI,CAACD,aAAa,GAAG,KAAK;UAC5B;QACF,CAAC,EAAC,CAAE;MACN;MAEA+F,mBAAmBA,CAAC7D,EAAO;QACzB,IAAI,CAACxC,gBAAgB,GAAGwC,EAAE;QAC1B,OAAO,CAACjE,OAAO,CAAC,IAAI,CAACwB,oBAAoB,CAAC,IAAI,IAAI,CAACA,oBAAoB,CAAC,CAAC,CAAC,CAACyC,EAAE,CAACO,eAAe,CAAC,IAAI,CAAC/C,gBAAgB,CAAC,EAAE;UACpH,MAAMwG,SAAS,GAAG,IAAI,CAACzG,oBAAoB,CAACuG,KAAK,EAAG;UACpDE,SAAS,CAAC9C,QAAQ,EAAE;QACtB;MACF;MAEA+C,mBAAmBA,CAACC,KAAa;QAC/B/H,cAAc,GAAG+H,KAAK;MACxB;MAEAC,kBAAkBA,CAAA;QAChBhI,cAAc,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,2BAA2B,IAAI,IAAI,CAAC;MACrE;;IAGI,SAAUR,OAAOA,CAAC4C,EAAc;MACpC,IAAIA,EAAE,CAACA,EAAE,KAAK,GAAG,IAAIA,EAAE,CAACA,EAAE,KAAK,GAAG,EAAE;QAClC,OAAOA,EAAE,CAAC0F,CAAC,CAACC,GAAG;MACjB,CAAC,MAAM,IAAI3F,EAAE,CAACA,EAAE,KAAK,GAAG,EAAE;QACxB,OAAOA,EAAE,CAAC4F,EAAE,CAACD,GAAG;MAClB,CAAC,MAAM,IAAI3F,EAAE,CAACA,EAAE,KAAK,GAAG,EAAE;QACxB,MAAMQ,KAAK,CAAC,iDAAiD,GAAGmB,IAAI,CAACC,SAAS,CAAC5B,EAAE,CAAC,CAAC;MACrF,CAAC,MAAM;QACL,MAAMQ,KAAK,CAAC,cAAc,GAAGmB,IAAI,CAACC,SAAS,CAAC5B,EAAE,CAAC,CAAC;MAClD;IACF;IAEA,eAAeqF,SAASA,CAACQ,MAAmB,EAAEf,GAAe;MAC3D,IAAIA,GAAG,CAACnF,EAAE,KAAK,YAAY,EAAE;QAC3B,IAAImF,GAAG,CAACY,CAAC,CAACI,QAAQ,EAAE;UAClB;UACA;UACA,IAAIC,aAAa,GAAGjB,GAAG,CAACxD,EAAE;UAC1B,KAAK,MAAMtB,EAAE,IAAI8E,GAAG,CAACY,CAAC,CAACI,QAAQ,EAAE;YAC/B;YACA,IAAI,CAAC9F,EAAE,CAACsB,EAAE,EAAE;cACVtB,EAAE,CAACsB,EAAE,GAAGyE,aAAa;cACrBA,aAAa,GAAGA,aAAa,CAACC,GAAG,CAACxI,IAAI,CAACyI,GAAG,CAAC;YAC7C;YACA,MAAMZ,SAAS,CAACQ,MAAM,EAAE7F,EAAE,CAAC;UAC7B;UACA;QACF;QACA,MAAM,IAAIQ,KAAK,CAAC,kBAAkB,GAAGmB,IAAI,CAACC,SAAS,CAACkD,GAAG,CAAC,CAAC;MAC3D;MAEA,MAAMvJ,OAAO,GAAiB;QAC5BmB,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE,KAAK;QACnBqD,EAAE,EAAE8E;OACL;MAED,IAAI,OAAOA,GAAG,CAACnF,EAAE,KAAK,QAAQ,IAAImF,GAAG,CAACnF,EAAE,CAACuG,UAAU,CAACL,MAAM,CAAC1H,OAAO,GAAG,GAAG,CAAC,EAAE;QACzE5C,OAAO,CAACU,UAAU,GAAG6I,GAAG,CAACnF,EAAE,CAACwG,KAAK,CAACN,MAAM,CAAC1H,OAAO,CAAC4D,MAAM,GAAG,CAAC,CAAC;MAC9D;MAEA;MACA;MACA,IAAIxG,OAAO,CAACU,UAAU,KAAK,MAAM,EAAE;QACjC,IAAI6I,GAAG,CAACY,CAAC,CAAC/I,YAAY,EAAE;UACtB,OAAOpB,OAAO,CAACU,UAAU;UACzBV,OAAO,CAACoB,YAAY,GAAG,IAAI;QAC7B,CAAC,MAAM,IAAI,MAAM,IAAImI,GAAG,CAACY,CAAC,EAAE;UAC1BnK,OAAO,CAACU,UAAU,GAAG6I,GAAG,CAACY,CAAC,CAACU,IAAI;UAC/B7K,OAAO,CAACmB,cAAc,GAAG,IAAI;UAC7BnB,OAAO,CAACgB,EAAE,GAAG,IAAI;QACnB,CAAC,MAAM,IAAI,QAAQ,IAAIuI,GAAG,CAACY,CAAC,IAAI,SAAS,IAAIZ,GAAG,CAACY,CAAC,EAAE;UAClD;UACA;QAAA,CACD,MAAM;UACL,MAAMlF,KAAK,CAAC,kBAAkB,GAAGmB,IAAI,CAACC,SAAS,CAACkD,GAAG,CAAC,CAAC;QACvD;MACF,CAAC,MAAM;QACL;QACAvJ,OAAO,CAACgB,EAAE,GAAGa,OAAO,CAAC0H,GAAG,CAAC;MAC3B;MAEA,MAAMe,MAAM,CAAClH,SAAS,CAAC0H,IAAI,CAAC9K,OAAO,CAAC;MAEpC,MAAM,IAAI+D,OAAO,CAACgH,OAAO,IAAIC,YAAY,CAACD,OAAO,CAAC,CAAC;IACrD;IAACzJ,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICzaD,IAAAwJ,wBAAoB;IAAAlM,MAAA,CAAgBb,IAAC;MAAAyD,QAAAxD,CAAA;QAAA8M,wBAAA,GAAA9M,CAAA;MAAA;IAAA;IAAA,MAAA+M,SAAA;IAArCnM,MAAA,CAAOjB,MAAA,CAAO;MAAAqN,kBAAM,EAAAA,CAAA,KAAiBA;IAAA;IAAA,IAAArJ,OAAA;IAAA/C,MAAA,CAAAb,IAAA;MAAAyD,QAAAxD,CAAA;QAAA2D,OAAA,GAAA3D,CAAA;MAAA;IAAA;IAAA,IAAAI,oBAAA,WAAAA,oBAAA;IAgB/B,MAAO4M,kBAAkB;MAW7B3I,YAAA4I,IAAA,EAAqE;QAAA,IAAAC,KAAA;QAAA,IAAzD;UAAEC,OAAO;UAAEC,MAAM,GAAGA,CAAA,KAAK,CAAE;QAAC,CAA6B,GAAAH,IAAA;QAAA,KAVpDI,QAAQ;QAAA,KACRC,OAAO;QAAA,KAChBC,MAAM;QAAA,KACNC,QAAQ;QAAA,KACRC,SAAS;QAAA,KACAzI,aAAa;QAAA,KACtB0I,QAAQ;QAAA,KACRC,MAAM;QAAA,KACNC,uCAAuC;QAG7C,IAAIT,OAAO,KAAKU,SAAS,EAAE,MAAM/G,KAAK,CAAC,sBAAsB,CAAC;QAE9D;QACAgH,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CACzCC,KAAK,CAACC,mBAAmB,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,CAAC,CAAC;QAE3E,IAAI,CAACX,QAAQ,GAAGF,OAAO;QACvB,IAAI,CAACG,OAAO,GAAGF,MAAM;QACrB,IAAI,CAACG,MAAM,GAAG,IAAIpM,MAAM,CAAC8M,kBAAkB,EAAE;QAC7C,IAAI,CAACT,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACC,SAAS,GAAG,IAAI;QACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC1I,aAAa,GAAG,IAAIY,OAAO,CAACC,CAAC,IAAI,IAAI,CAAC4H,SAAS,GAAG5H,CAAC,CAAC,CAACqI,IAAI,CAAC,MAAM,IAAI,CAACR,QAAQ,GAAG,IAAI,CAAC;QAC1F;QACA,IAAI,CAACC,MAAM,GAAG,IAAIjL,eAAe,CAACyL,sBAAsB,CAAC;UAAEhB;QAAO,CAAE,CAAC;QACrE,IAAI,CAACS,uCAAuC,GAAG,CAAC;QAEhD,IAAI,CAACQ,aAAa,EAAE,CAACjM,OAAO,CAACkM,YAAY,IAAG;UACzC,IAAY,CAACA,YAAY,CAAC,GAAG,YAAmB;YAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAlG,MAAA,EAAfmG,IAAW,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;cAAXF,IAAW,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;YAAA;YAC3CxB,KAAI,CAACyB,cAAc,CAACN,YAAY,EAAEG,IAAI,CAAC;UACzC,CAAC;QACH,CAAC,CAAC;MACJ;MAEAI,2BAA2BA,CAACzC,MAAqB;QAC/C,OAAO,IAAI,CAAC0C,4BAA4B,CAAC1C,MAAM,CAAC;MAClD;MAEA,MAAM0C,4BAA4BA,CAAC1C,MAAqB;QACtD,EAAE,IAAI,CAACyB,uCAAuC;QAE9C;QACAE,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,gBAAgB,EAAE,iBAAiB,EAAE,CAAC,CAAC;QAEzC,MAAM,IAAI,CAACT,MAAM,CAACuB,OAAO,CAAC,YAAW;UACnC,IAAI,CAACtB,QAAS,CAACrB,MAAM,CAACF,GAAG,CAAC,GAAGE,MAAM;UACnC,MAAM,IAAI,CAAC4C,SAAS,CAAC5C,MAAM,CAAC;UAC5B,EAAE,IAAI,CAACyB,uCAAuC;QAChD,CAAC,CAAC;QACF,MAAM,IAAI,CAAC5I,aAAa;MAC1B;MAEA,MAAMgK,YAAYA,CAACnM,EAAU;QAC3B,IAAI,CAAC,IAAI,CAACoM,MAAM,EAAE,EAChB,MAAM,IAAInI,KAAK,CAAC,mDAAmD,CAAC;QAEtE,OAAO,IAAI,CAAC0G,QAAS,CAAC3K,EAAE,CAAC;QAEzB;QACAiL,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,gBAAgB,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;QAE1C,IAAIrK,OAAO,CAAC,IAAI,CAAC6J,QAAQ,CAAC,IACxB,IAAI,CAACI,uCAAuC,KAAK,CAAC,EAAE;UACpD,MAAM,IAAI,CAACsB,KAAK,EAAE;QACpB;MACF;MAEA,MAAMA,KAAKA,CAAA,EAA2C;QAAA,IAA1CC,OAAA,GAAAZ,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAwC,EAAE;QACpD,IAAI,CAAC,IAAI,CAACU,MAAM,EAAE,IAAI,CAACE,OAAO,CAACC,cAAc,EAC3C,MAAMtI,KAAK,CAAC,6BAA6B,CAAC;QAE5C,MAAM,IAAI,CAACwG,OAAO,EAAE;QAEpB;QACAQ,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CACzCC,KAAK,CAACC,mBAAmB,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,CAAC,CAAC,CAAC;QAE5E,IAAI,CAACR,QAAQ,GAAG,IAAI;MACtB;MAEA,MAAM6B,KAAKA,CAAA;QACT,MAAM,IAAI,CAAC9B,MAAM,CAAC+B,SAAS,CAAC,MAAK;UAC/B,IAAI,IAAI,CAACL,MAAM,EAAE,EACf,MAAMnI,KAAK,CAAC,0CAA0C,CAAC;UAEzD,IAAI,CAAC,IAAI,CAAC2G,SAAS,EAAE;YACnB,MAAM,IAAI3G,KAAK,CAAC,kBAAkB,CAAC;UACrC;UAEA,IAAI,CAAC2G,SAAS,EAAE;UAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;QACtB,CAAC,CAAC;MACJ;MAEA,MAAM6B,UAAUA,CAACrI,GAAU;QACzB,MAAM,IAAI,CAACqG,MAAM,CAACuB,OAAO,CAAC,MAAK;UAC7B,IAAI,IAAI,CAACG,MAAM,EAAE,EACf,MAAMnI,KAAK,CAAC,iDAAiD,CAAC;UAChE,IAAI,CAACoI,KAAK,CAAC;YAAEE,cAAc,EAAE;UAAI,CAAE,CAAC;UACpC,MAAMlI,GAAG;QACX,CAAC,CAAC;MACJ;MAEA,MAAMsI,OAAOA,CAACC,EAAc;QAC1B,MAAM,IAAI,CAAClC,MAAM,CAAC+B,SAAS,CAAC,YAAW;UACrC,IAAI,CAAC,IAAI,CAACL,MAAM,EAAE,EAChB,MAAMnI,KAAK,CAAC,uDAAuD,CAAC;UACtE,MAAM2I,EAAE,EAAE;QACZ,CAAC,CAAC;MACJ;MAEArB,aAAaA,CAAA;QACX,OAAO,IAAI,CAACf,QAAQ,GAChB,CAAC,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,CAAC,GACpD,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;MACrC;MAEA4B,MAAMA,CAAA;QACJ,OAAO,CAAC,CAAC,IAAI,CAACvB,QAAQ;MACxB;MAEAiB,cAAcA,CAACN,YAAoB,EAAEG,IAAW;QAC9C,IAAI,CAACjB,MAAM,CAAC+B,SAAS,CAAC,YAAW;UAC/B,IAAI,CAAC,IAAI,CAAC9B,QAAQ,EAAE;UAEpB,MAAM,IAAI,CAACG,MAAM,CAAC+B,WAAW,CAACrB,YAAY,CAAC,CAACsB,KAAK,CAAC,IAAI,EAAEnB,IAAI,CAAC;UAC7D,IAAI,CAAC,IAAI,CAACS,MAAM,EAAE,IACfZ,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,aAAc,EAAE;YAC9D,MAAM,IAAIvH,KAAK,QAAAkE,MAAA,CAAQqD,YAAY,yBAAsB,CAAC;UAC5D;UAEA,KAAK,MAAMuB,QAAQ,IAAI9M,MAAM,CAAC+M,IAAI,CAAC,IAAI,CAACrC,QAAQ,CAAC,EAAE;YACjD,MAAMrB,MAAM,GAAG,IAAI,CAACqB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACoC,QAAQ,CAAC;YAEvD,IAAI,CAACzD,MAAM,EAAE;YAEb,MAAMtF,QAAQ,GAAIsF,MAAc,KAAAnB,MAAA,CAAKqD,YAAY,EAAG;YAEpD,IAAI,CAACxH,QAAQ,EAAE;YAEfsF,MAAM,CAAC2D,eAAe,CAAC5B,IAAI,CAACrH,QAAQ,CAAC8I,KAAK,CACxC,IAAI,EACJxD,MAAM,CAAC4D,oBAAoB,GAAGvB,IAAI,GAAGwB,KAAK,CAACvO,KAAK,CAAC+M,IAAI,CAAC,CACvD,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,MAAMO,SAASA,CAAC5C,MAAqB;QACnC,MAAMG,GAAG,GAAG,IAAI,CAACe,QAAQ,GAAGlB,MAAM,CAAC8D,YAAY,GAAG9D,MAAM,CAAC+D,MAAM;QAC/D,IAAI,CAAC5D,GAAG,EAAE;QAEV,MAAM6D,WAAW,GAAoB,EAAE;QAEvC,IAAI,CAACxC,MAAM,CAACyC,IAAI,CAACjO,OAAO,CAAC,CAACiJ,GAAQ,EAAEvI,EAAU,KAAI;UAChD,IAAI,EAAEsJ,MAAM,CAACF,GAAG,IAAI,IAAI,CAACuB,QAAS,CAAC,EAAE;YACnC,MAAM1G,KAAK,CAAC,iDAAiD,CAAC;UAChE;UAEA,MAAAuJ,KAAA,GAA2BlE,MAAM,CAAC4D,oBAAoB,GAAG3E,GAAG,GAAG4E,KAAK,CAACvO,KAAK,CAAC2J,GAAG,CAAC;YAAzE;cAAEa;YAAc,CAAE,GAAAoE,KAAA;YAARC,MAAM,GAAAxD,wBAAA,CAAAuD,KAAA,EAAAtD,SAAA;UAEtB,MAAMwD,OAAO,GAAG,IAAI,CAAClD,QAAQ,GAC3Bf,GAAG,CAACzJ,EAAE,EAAEyN,MAAM,EAAE,IAAI,CAAC,GACrBhE,GAAG,CAACzJ,EAAE,EAAEyN,MAAM,CAAC;UAEjBH,WAAW,CAACrO,IAAI,CAACyO,OAAO,CAAC;QAC3B,CAAC,CAAC;QAEF,MAAM3K,OAAO,CAAC4K,GAAG,CAACL,WAAW,CAAC;QAE9BhE,MAAM,CAACsE,uBAAuB,EAAE;MAClC;;IACDtN,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AChMD1C,MAAM,CAACjB,MAAM,CAAC;EAAC+Q,UAAU,EAACA,CAAA,KAAIA;AAAU,CAAC,CAAC;AAAnC,MAAMA,UAAU,CAAC;EACtBrM,WAAWA,CAACsM,eAAe,EAAE;IAC3B,IAAI,CAACC,gBAAgB,GAAGD,eAAe;IACvC;IACA,IAAI,CAACE,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClC;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,KAAKA,CAACvO,cAAc,EAAEK,EAAE,EAAEyD,EAAE,EAAEO,QAAQ,EAAE;IAC5C,MAAMxD,IAAI,GAAG,IAAI;IAGjB2N,KAAK,CAACxO,cAAc,EAAEyO,MAAM,CAAC;IAC7BD,KAAK,CAAC1K,EAAE,EAAExD,MAAM,CAAC;;IAGjB;IACA;IACA,IAAIO,IAAI,CAACwN,eAAe,CAACK,GAAG,CAAC5K,EAAE,CAAC,EAAE;MAChCjD,IAAI,CAACwN,eAAe,CAAC9P,GAAG,CAACuF,EAAE,CAAC,CAACxE,IAAI,CAAC+E,QAAQ,CAAC;MAC3C;IACF;IAEA,MAAMsK,SAAS,GAAG,CAACtK,QAAQ,CAAC;IAC5BxD,IAAI,CAACwN,eAAe,CAACO,GAAG,CAAC9K,EAAE,EAAE6K,SAAS,CAAC;IAEvC,IAAI;MACF,IAAI/F,GAAG,GACL,CAAC,MAAM/H,IAAI,CAACuN,gBAAgB,CAAClJ,YAAY,CAAClF,cAAc,EAAE;QACxDyJ,GAAG,EAAEpJ;MACP,CAAC,CAAC,KAAK,IAAI;MACb;MACA;MACA,OAAOsO,SAAS,CAAC9I,MAAM,GAAG,CAAC,EAAE;QAC3B;QACA;QACA;QACA;QACA8I,SAAS,CAAC7F,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE0E,KAAK,CAACvO,KAAK,CAAC2J,GAAG,CAAC,CAAC;MACzC;IACF,CAAC,CAAC,OAAOrD,CAAC,EAAE;MACV,OAAOoJ,SAAS,CAAC9I,MAAM,GAAG,CAAC,EAAE;QAC3B8I,SAAS,CAAC7F,GAAG,CAAC,CAAC,CAACvD,CAAC,CAAC;MACpB;IACF,CAAC,SAAS;MACR;MACA;MACA1E,IAAI,CAACwN,eAAe,CAACQ,MAAM,CAAC/K,EAAE,CAAC;IACjC;EACF;AACF,C;;;;;;;;;;;;;;IC1DA1F,MAAA,CAAOjB,MAAA;MAAQ2R,oBAAM,EAAAA,CAAA,KAAkBA;IAAA;IAAA,IAAAC,QAAA;IAAA3Q,MAAA,CAAAb,IAAA;MAAAyD,QAAAxD,CAAA;QAAAuR,QAAA,GAAAvR,CAAA;MAAA;IAAA;IAAA,IAAAJ,SAAA;IAAAgB,MAAA,CAAAb,IAAA;MAAAH,UAAAI,CAAA;QAAAJ,SAAA,GAAAI,CAAA;MAAA;IAAA;IAAA,IAAAI,oBAAA,WAAAA,oBAAA;IAYvC,MAAMoR,mBAAmB,GAAG,EAAExN,OAAO,CAACC,GAAG,CAACwN,0BAA0B,IAAI,EAAE,CAAC,IAAI,EAAE;IACjF,MAAMC,mBAAmB,GAAG,EAAE1N,OAAO,CAACC,GAAG,CAAC0N,0BAA0B,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI;IAExF;;;;;;;;;;IAUM,MAAOL,oBAAoB;MAgB/BjN,YAAY8K,OAAoC;QAAA,KAfxCyC,QAAQ;QAAA,KACRC,kBAAkB;QAAA,KAClBC,YAAY;QAAA,KACZzE,QAAQ;QAAA,KACR0E,YAAY;QAAA,KACZC,cAAc;QAAA,KACdnN,QAAQ;QAAA,KACRoN,OAAO;QAAA,KACPC,QAAQ;QAAA,KACRC,4BAA4B;QAAA,KAC5BC,cAAc;QAAA,KACdC,sBAAsB;QAAA,KACtBC,UAAU;QAAA,KACVC,qBAAqB;QAG3B,IAAI,CAACX,QAAQ,GAAGzC,OAAO;QACvB,IAAI,CAAC0C,kBAAkB,GAAG1C,OAAO,CAACzN,iBAAiB;QACnD,IAAI,CAACoQ,YAAY,GAAG3C,OAAO,CAACqD,WAAW;QACvC,IAAI,CAACnF,QAAQ,GAAG8B,OAAO,CAAChC,OAAO;QAC/B,IAAI,CAAC4E,YAAY,GAAG5C,OAAO,CAACsD,WAAW;QACvC,IAAI,CAACT,cAAc,GAAG,EAAE;QACxB,IAAI,CAACnN,QAAQ,GAAG,KAAK;QAErB,IAAI,CAACoN,OAAO,GAAG,IAAI,CAACH,YAAY,CAACY,yBAAyB,CACxD,IAAI,CAACb,kBAAkB,CAAC;QAE1B,IAAI,CAACK,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACC,4BAA4B,GAAG,CAAC;QACrC,IAAI,CAACC,cAAc,GAAG,EAAE;QAExB,IAAI,CAACC,sBAAsB,GAAGd,QAAQ,CACpC,IAAI,CAACoB,iCAAiC,CAACC,IAAI,CAAC,IAAI,CAAC,EACjD,IAAI,CAACf,kBAAkB,CAAC1C,OAAO,CAAC0D,iBAAiB,IAAIrB,mBAAmB,CACzE;QAED,IAAI,CAACc,UAAU,GAAG,IAAKnR,MAAc,CAAC8M,kBAAkB,EAAE;MAC5D;MAEA,MAAM6E,KAAKA,CAAA;QAAA,IAAAC,kBAAA;QACT,MAAM5D,OAAO,GAAG,IAAI,CAACyC,QAAQ;QAC7B,MAAMoB,eAAe,GAAG,MAAMpT,SAAS,CACrC,IAAI,CAACiS,kBAAkB,EACtB5K,YAAiB,IAAI;UACpB,MAAMgM,KAAK,GAAIlR,SAAiB,CAACmR,gBAAgB,EAAE;UACnD,IAAID,KAAK,EAAE;YACT,IAAI,CAACb,cAAc,CAACtQ,IAAI,CAACmR,KAAK,CAACE,UAAU,EAAE,CAAC;UAC9C;UACA,IAAI,IAAI,CAAChB,4BAA4B,KAAK,CAAC,EAAE;YAC3C,IAAI,CAACE,sBAAsB,EAAE;UAC/B;QACF,CAAC,CACF;QAED,IAAI,CAACL,cAAc,CAAClQ,IAAI,CAAC,YAAW;UAAG,MAAMkR,eAAe,CAAC9Q,IAAI,EAAE;QAAE,CAAC,CAAC;QAEvE,IAAIiN,OAAO,CAACoD,qBAAqB,EAAE;UACjC,IAAI,CAACA,qBAAqB,GAAGpD,OAAO,CAACoD,qBAAqB;QAC5D,CAAC,MAAM;UACL,MAAMa,eAAe,GACnB,IAAI,CAACvB,kBAAkB,CAAC1C,OAAO,CAACkE,iBAAiB,IACjD,IAAI,CAACxB,kBAAkB,CAAC1C,OAAO,CAACmE,gBAAgB,IAChD5B,mBAAmB;UAErB,MAAM6B,cAAc,GAAGpS,MAAM,CAACqS,WAAW,CACvC,IAAI,CAACnB,sBAAsB,CAACO,IAAI,CAAC,IAAI,CAAC,EACtCQ,eAAe,CAChB;UAED,IAAI,CAACpB,cAAc,CAAClQ,IAAI,CAAC,MAAK;YAC5BX,MAAM,CAACsS,aAAa,CAACF,cAAc,CAAC;UACtC,CAAC,CAAC;QACJ;QAEA,MAAM,IAAI,CAACZ,iCAAiC,EAAE;QAE7C,CAAAI,kBAAA,GAAAjF,OAAO,CAAC,YAAY,CAAS,cAAAiF,kBAAA,uBAA7BA,kBAAA,CAA+BhF,KAAK,CAACC,mBAAmB,CACvD,gBAAgB,EAAE,yBAAyB,EAAE,CAAC,CAAC;MACnD;MAEA,MAAM2E,iCAAiCA,CAAA;QACrC,IAAI,IAAI,CAACR,4BAA4B,GAAG,CAAC,EAAE;QAC3C,EAAE,IAAI,CAACA,4BAA4B;QACnC,MAAM,IAAI,CAACG,UAAU,CAACxD,OAAO,CAAC,YAAW;UACvC,MAAM,IAAI,CAAC4E,UAAU,EAAE;QACzB,CAAC,CAAC;MACJ;MAEAC,eAAeA,CAAA;QACb,EAAE,IAAI,CAACxB,4BAA4B;QACnC,IAAI,CAACG,UAAU,CAACxD,OAAO,CAAC,MAAK,CAAE,CAAC,CAAC;QAEjC,IAAI,IAAI,CAACqD,4BAA4B,KAAK,CAAC,EAAE;UAC3C,MAAM,IAAIrL,KAAK,oCAAAkE,MAAA,CAAoC,IAAI,CAACmH,4BAA4B,CAAE,CAAC;QACzF;MACF;MAEA,MAAMyB,cAAcA,CAAA;QAClB,IAAI,IAAI,CAACzB,4BAA4B,KAAK,CAAC,EAAE;UAC3C,MAAM,IAAIrL,KAAK,oCAAAkE,MAAA,CAAoC,IAAI,CAACmH,4BAA4B,CAAE,CAAC;QACzF;QACA,MAAM,IAAI,CAACG,UAAU,CAACxD,OAAO,CAAC,YAAW;UACvC,MAAM,IAAI,CAAC4E,UAAU,EAAE;QACzB,CAAC,CAAC;MACJ;MAEA,MAAMA,UAAUA,CAAA;QAAA,IAAAG,qBAAA;QACd,EAAE,IAAI,CAAC1B,4BAA4B;QAEnC,IAAI,IAAI,CAACtN,QAAQ,EAAE;QAEnB,IAAIiP,KAAK,GAAG,KAAK;QACjB,IAAIC,UAAU;QACd,IAAIC,UAAU,GAAG,IAAI,CAAC9B,QAAQ;QAE9B,IAAI,CAAC8B,UAAU,EAAE;UACfF,KAAK,GAAG,IAAI;UACZE,UAAU,GAAG,IAAI,CAAC3G,QAAQ,GAAG,EAAE,GAAG,IAAK3K,eAAuB,CAACuR,MAAM,CAAN,CAAM;QACvE;QAEA,CAAAJ,qBAAA,OAAI,CAACtB,qBAAqB,cAAAsB,qBAAA,uBAA1BA,qBAAA,CAAAK,IAAA,KAA4B,CAAE;QAE9B,MAAMC,cAAc,GAAG,IAAI,CAAC/B,cAAc;QAC1C,IAAI,CAACA,cAAc,GAAG,EAAE;QAExB,IAAI;UACF2B,UAAU,GAAG,MAAM,IAAI,CAAC9B,OAAO,CAACmC,aAAa,CAAC,IAAI,CAAC/G,QAAQ,CAAC;QAC9D,CAAC,CAAC,OAAOtF,CAAM,EAAE;UACf,IAAI+L,KAAK,IAAI,OAAO/L,CAAC,CAACsM,IAAK,KAAK,QAAQ,EAAE;YACxC,MAAM,IAAI,CAACtC,YAAY,CAACxC,UAAU,CAChC,IAAIzI,KAAK,kCAAAkE,MAAA,CAEL/C,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC2J,kBAAkB,CACxC,QAAA7G,MAAA,CAAKjD,CAAC,CAACuM,OAAO,CAAE,CACjB,CACF;UACH;UAEA7F,KAAK,CAACjN,SAAS,CAACM,IAAI,CAAC6N,KAAK,CAAC,IAAI,CAACyC,cAAc,EAAE+B,cAAc,CAAC;UAC/DhT,MAAM,CAACgG,MAAM,kCAAA6D,MAAA,CACX/C,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC2J,kBAAkB,CAAC,GAAI9J,CAAC,CAAC;UAC/C;QACF;QAEA,IAAI,CAAC,IAAI,CAAClD,QAAQ,EAAE;UACjBnC,eAAuB,CAAC6R,iBAAiB,CACxC,IAAI,CAAClH,QAAQ,EAAE2G,UAAU,EAAED,UAAU,EAAE,IAAI,CAAChC,YAAY,CAAC;QAC7D;QAEA,IAAI+B,KAAK,EAAE,IAAI,CAAC/B,YAAY,CAAC1C,KAAK,EAAE;QAEpC,IAAI,CAAC6C,QAAQ,GAAG6B,UAAU;QAE1B,MAAM,IAAI,CAAChC,YAAY,CAACvC,OAAO,CAAC,YAAW;UACzC,KAAK,MAAMgF,CAAC,IAAIL,cAAc,EAAE;YAC9B,MAAMK,CAAC,CAACC,SAAS,EAAE;UACrB;QACF,CAAC,CAAC;MACJ;MAEA,MAAMvS,IAAIA,CAAA;QAAA,IAAAwS,mBAAA;QACR,IAAI,CAAC7P,QAAQ,GAAG,IAAI;QAEpB,KAAK,MAAMgC,QAAQ,IAAI,IAAI,CAACmL,cAAc,EAAE;UAC1C,MAAMnL,QAAQ,EAAE;QAClB;QAEA,KAAK,MAAM2N,CAAC,IAAI,IAAI,CAACpC,cAAc,EAAE;UACnC,MAAMoC,CAAC,CAACC,SAAS,EAAE;QACrB;QAEC,CAAAC,mBAAA,GAAA5G,OAAO,CAAC,YAAY,CAAS,cAAA4G,mBAAA,uBAA7BA,mBAAA,CAA+B3G,KAAK,CAACC,mBAAmB,CACvD,gBAAgB,EAAE,yBAAyB,EAAE,CAAC,CAAC,CAAC;MACpD;;IACD7K,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICxMD,IAAIqR,cAAc;IAAC/T,MAAM,CAACb,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAAC2U,cAAc,GAAC3U,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAvGY,MAAM,CAACjB,MAAM,CAAC;MAACO,kBAAkB,EAACA,CAAA,KAAIA;IAAkB,CAAC,CAAC;IAAC,IAAIgR,GAAG;IAACtQ,MAAM,CAACb,IAAI,CAAC,YAAY,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACkR,GAAG,GAAClR,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI2D,OAAO;IAAC/C,MAAM,CAACb,IAAI,CAAC,gBAAgB,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAAC2D,OAAO,GAAC3D,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4U,kBAAkB;IAAChU,MAAM,CAACb,IAAI,CAAC,sBAAsB,EAAC;MAAC6U,kBAAkBA,CAAC5U,CAAC,EAAC;QAAC4U,kBAAkB,GAAC5U,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIgR,KAAK,EAAC6D,KAAK;IAACjU,MAAM,CAACb,IAAI,CAAC,cAAc,EAAC;MAACiR,KAAKA,CAAChR,CAAC,EAAC;QAACgR,KAAK,GAAChR,CAAC;MAAA,CAAC;MAAC6U,KAAKA,CAAC7U,CAAC,EAAC;QAAC6U,KAAK,GAAC7U,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4D,iBAAiB;IAAChD,MAAM,CAACb,IAAI,CAAC,sBAAsB,EAAC;MAAC6D,iBAAiBA,CAAC5D,CAAC,EAAC;QAAC4D,iBAAiB,GAAC5D,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIH,cAAc,EAACD,SAAS;IAACgB,MAAM,CAACb,IAAI,CAAC,gBAAgB,EAAC;MAACF,cAAcA,CAACG,CAAC,EAAC;QAACH,cAAc,GAACG,CAAC;MAAA,CAAC;MAACJ,SAASA,CAACI,CAAC,EAAC;QAACJ,SAAS,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI8U,MAAM;IAAClU,MAAM,CAACb,IAAI,CAAC,UAAU,EAAC;MAAC+U,MAAMA,CAAC9U,CAAC,EAAC;QAAC8U,MAAM,GAAC9U,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI0C,eAAe;IAAC9B,MAAM,CAACb,IAAI,CAAC,mCAAmC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAAC0C,eAAe,GAAC1C,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI0D,OAAO;IAAC9C,MAAM,CAACb,IAAI,CAAC,iBAAiB,EAAC;MAAC2D,OAAOA,CAAC1D,CAAC,EAAC;QAAC0D,OAAO,GAAC1D,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAU93B,IAAI2U,KAAK,GAAG;MACVC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE;IACV,CAAC;;IAED;IACA;IACA,IAAIC,eAAe,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;IACpC,IAAIC,uBAAuB,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACzC,OAAO,YAAY;QACjB,IAAI;UACFA,CAAC,CAAC1F,KAAK,CAAC,IAAI,EAAEpB,SAAS,CAAC;QAC1B,CAAC,CAAC,OAAOxG,CAAC,EAAE;UACV,IAAI,EAAEA,CAAC,YAAYoN,eAAe,CAAC,EACjC,MAAMpN,CAAC;QACX;MACF,CAAC;IACH,CAAC;IAED,IAAIuN,SAAS,GAAG,CAAC;;IAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACO,MAAMpV,kBAAkB,GAAG,SAAAA,CAAUiP,OAAO,EAAE;MACnD,MAAM9L,IAAI,GAAG,IAAI;MACjBA,IAAI,CAACkS,UAAU,GAAG,IAAI,CAAC,CAAE;;MAEzBlS,IAAI,CAAC4I,GAAG,GAAGqJ,SAAS;MACpBA,SAAS,EAAE;MAEXjS,IAAI,CAACwO,kBAAkB,GAAG1C,OAAO,CAACzN,iBAAiB;MACnD2B,IAAI,CAACyO,YAAY,GAAG3C,OAAO,CAACqD,WAAW;MACvCnP,IAAI,CAAC0O,YAAY,GAAG5C,OAAO,CAACsD,WAAW;MAEvC,IAAItD,OAAO,CAAChC,OAAO,EAAE;QACnB,MAAMrG,KAAK,CAAC,2DAA2D,CAAC;MAC1E;MAEA,MAAM0O,MAAM,GAAGrG,OAAO,CAACqG,MAAM;MAC7B;MACA;MACA,MAAMC,UAAU,GAAGD,MAAM,IAAIA,MAAM,CAACE,aAAa,CAAC,CAAC;MAEnD,IAAIvG,OAAO,CAACzN,iBAAiB,CAACyN,OAAO,CAACwG,KAAK,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,MAAMC,WAAW,GAAG;UAAEC,KAAK,EAAEnT,eAAe,CAACuR;QAAO,CAAC;QACrD5Q,IAAI,CAACyS,MAAM,GAAGzS,IAAI,CAACwO,kBAAkB,CAAC1C,OAAO,CAACwG,KAAK;QACnDtS,IAAI,CAAC0S,WAAW,GAAGN,UAAU;QAC7BpS,IAAI,CAAC2S,OAAO,GAAGR,MAAM;QACrBnS,IAAI,CAAC4S,kBAAkB,GAAG,IAAIC,UAAU,CAACT,UAAU,EAAEG,WAAW,CAAC;QACjE;QACAvS,IAAI,CAAC8S,UAAU,GAAG,IAAIC,OAAO,CAACX,UAAU,EAAEG,WAAW,CAAC;MACxD,CAAC,MAAM;QACLvS,IAAI,CAACyS,MAAM,GAAG,CAAC;QACfzS,IAAI,CAAC0S,WAAW,GAAG,IAAI;QACvB1S,IAAI,CAAC2S,OAAO,GAAG,IAAI;QACnB3S,IAAI,CAAC4S,kBAAkB,GAAG,IAAI;QAC9B;QACA5S,IAAI,CAAC8S,UAAU,GAAG,IAAIzT,eAAe,CAACuR,MAAM,CAAD,CAAC;MAC9C;;MAEA;MACA;MACA;MACA5Q,IAAI,CAACgT,mBAAmB,GAAG,KAAK;MAEhChT,IAAI,CAACwB,QAAQ,GAAG,KAAK;MACrBxB,IAAI,CAACiT,YAAY,GAAG,EAAE;MACtBjT,IAAI,CAACkT,eAAe,GAAG,UAAUC,cAAc,EAAE;QAC/C,MAAMC,eAAe,GAAG5B,KAAK,CAAC6B,eAAe,CAAC;UAAExU,IAAI,EAAEyU;QAAS,CAAC,CAAC;QACjE;QACA3F,KAAK,CAACwF,cAAc,EAAE3B,KAAK,CAAC+B,KAAK,CAAC,CAACH,eAAe,CAAC,EAAEA,eAAe,CAAC,CAAC;QACtEpT,IAAI,CAACiT,YAAY,CAACxU,IAAI,CAAC0U,cAAc,CAAC;MACxC,CAAC;MAED1I,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,gBAAgB,EAAE,uBAAuB,EAAE,CAAC,CAAC;MAE/C3K,IAAI,CAACwT,oBAAoB,CAAC9B,KAAK,CAACC,QAAQ,CAAC;MAEzC3R,IAAI,CAACyT,QAAQ,GAAG3H,OAAO,CAAC4H,OAAO;MAC/B;MACA;MACA,MAAMpP,UAAU,GAAGtE,IAAI,CAACwO,kBAAkB,CAAC1C,OAAO,CAACmB,MAAM,IAAIjN,IAAI,CAACwO,kBAAkB,CAAC1C,OAAO,CAACxH,UAAU,IAAI,CAAC,CAAC;MAC7GtE,IAAI,CAAC2T,aAAa,GAAGtU,eAAe,CAACuU,kBAAkB,CAACtP,UAAU,CAAC;MACnE;MACA;MACAtE,IAAI,CAAC6T,iBAAiB,GAAG7T,IAAI,CAACyT,QAAQ,CAACK,qBAAqB,CAACxP,UAAU,CAAC;MACxE,IAAI6N,MAAM,EACRnS,IAAI,CAAC6T,iBAAiB,GAAG1B,MAAM,CAAC2B,qBAAqB,CAAC9T,IAAI,CAAC6T,iBAAiB,CAAC;MAC/E7T,IAAI,CAAC+T,mBAAmB,GAAG1U,eAAe,CAACuU,kBAAkB,CAC3D5T,IAAI,CAAC6T,iBAAiB,CAAC;MAEzB7T,IAAI,CAACgU,YAAY,GAAG,IAAI3U,eAAe,CAACuR,MAAM,CAAD,CAAC;MAC9C5Q,IAAI,CAACiU,kBAAkB,GAAG,IAAI;MAC9BjU,IAAI,CAACkU,gBAAgB,GAAG,CAAC;MAEzBlU,IAAI,CAACmU,yBAAyB,GAAG,KAAK;MACtCnU,IAAI,CAACoU,gCAAgC,GAAG,EAAE;IAC3C,CAAC;IAEF3U,MAAM,CAACC,MAAM,CAAC7C,kBAAkB,CAACsB,SAAS,EAAE;MAC1CsR,KAAK,EAAE,eAAAA,CAAA,EAAiB;QACtB,MAAMzP,IAAI,GAAG,IAAI;;QAEjB;QACA;QACAA,IAAI,CAACkT,eAAe,CAAClT,IAAI,CAACyO,YAAY,CAAC4F,YAAY,CAACpQ,gBAAgB,CAClE8N,uBAAuB,CAAC,YAAY;UAClC,OAAO/R,IAAI,CAACsU,gBAAgB,CAAC,CAAC;QAChC,CAAC,CACH,CAAC,CAAC;QAEF,MAAM9X,cAAc,CAACwD,IAAI,CAACwO,kBAAkB,EAAE,gBAAgBhQ,OAAO,EAAE;UACrEwB,IAAI,CAACkT,eAAe,CAAC,MAAMlT,IAAI,CAACyO,YAAY,CAAC4F,YAAY,CAACrQ,YAAY,CACpExF,OAAO,EAAE,UAAUoF,YAAY,EAAE;YAC/BmO,uBAAuB,CAAC,YAAY;cAClC,MAAM9O,EAAE,GAAGW,YAAY,CAACX,EAAE;cAC1B,IAAIW,YAAY,CAACjE,cAAc,IAAIiE,YAAY,CAAChE,YAAY,EAAE;gBAC5D;gBACA;gBACA;gBACA,OAAOI,IAAI,CAACsU,gBAAgB,CAAC,CAAC;cAChC,CAAC,MAAM;gBACL;gBACA,IAAItU,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAAE;kBAClC,OAAO3R,IAAI,CAACwU,yBAAyB,CAACvR,EAAE,CAAC;gBAC3C,CAAC,MAAM;kBACL,OAAOjD,IAAI,CAACyU,iCAAiC,CAACxR,EAAE,CAAC;gBACnD;cACF;YACF,CAAC,CAAC,CAAC,CAAC;UACN,CACF,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACAjD,IAAI,CAACkT,eAAe,CAAC,MAAM3W,SAAS,CAClCyD,IAAI,CAACwO,kBAAkB,EAAE,YAAY;UACnC;UACA,MAAMoB,KAAK,GAAGlR,SAAS,CAACmR,gBAAgB,CAAC,CAAC;UAC1C,IAAI,CAACD,KAAK,IAAIA,KAAK,CAAC8E,KAAK,EACvB;UAEF,IAAI9E,KAAK,CAAC+E,oBAAoB,EAAE;YAC9B/E,KAAK,CAAC+E,oBAAoB,CAAC3U,IAAI,CAAC4I,GAAG,CAAC,GAAG5I,IAAI;YAC3C;UACF;UAEA4P,KAAK,CAAC+E,oBAAoB,GAAG,CAAC,CAAC;UAC/B/E,KAAK,CAAC+E,oBAAoB,CAAC3U,IAAI,CAAC4I,GAAG,CAAC,GAAG5I,IAAI;UAE3C4P,KAAK,CAACgF,YAAY,CAAC,kBAAkB;YACnC,MAAMC,OAAO,GAAGjF,KAAK,CAAC+E,oBAAoB;YAC1C,OAAO/E,KAAK,CAAC+E,oBAAoB;;YAEjC;YACA;YACA,MAAM3U,IAAI,CAACyO,YAAY,CAAC4F,YAAY,CAAC3O,iBAAiB,CAAC,CAAC;YAExD,KAAK,MAAMoP,MAAM,IAAIrV,MAAM,CAACsV,MAAM,CAACF,OAAO,CAAC,EAAE;cAC3C,IAAIC,MAAM,CAACtT,QAAQ,EACjB;cAEF,MAAMwT,KAAK,GAAG,MAAMpF,KAAK,CAACE,UAAU,CAAC,CAAC;cACtC,IAAIgF,MAAM,CAACP,MAAM,KAAK7C,KAAK,CAACG,MAAM,EAAE;gBAClC;gBACA;gBACA;gBACA,MAAMiD,MAAM,CAACpG,YAAY,CAACvC,OAAO,CAAC6I,KAAK,CAAC5D,SAAS,CAAC;cACpD,CAAC,MAAM;gBACL0D,MAAM,CAACV,gCAAgC,CAAC3V,IAAI,CAACuW,KAAK,CAAC;cACrD;YACF;UACF,CAAC,CAAC;QACJ,CACF,CAAC,CAAC;;QAEF;QACA;QACAhV,IAAI,CAACkT,eAAe,CAAClT,IAAI,CAACyO,YAAY,CAACwG,WAAW,CAAClD,uBAAuB,CACxE,YAAY;UACV,OAAO/R,IAAI,CAACsU,gBAAgB,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC;;QAEN;QACA;QACA,OAAOtU,IAAI,CAACkV,gBAAgB,CAAC,CAAC;MAChC,CAAC;MACDC,aAAa,EAAE,SAAAA,CAAU3V,EAAE,EAAEuI,GAAG,EAAE;QAChC,IAAI/H,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAInI,MAAM,GAAGxN,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqI,GAAG,CAAC;UACnC,OAAOkF,MAAM,CAACrE,GAAG;UACjB5I,IAAI,CAAC8S,UAAU,CAAC/E,GAAG,CAACvO,EAAE,EAAEQ,IAAI,CAAC+T,mBAAmB,CAAChM,GAAG,CAAC,CAAC;UACtD/H,IAAI,CAAC0O,YAAY,CAAC2G,KAAK,CAAC7V,EAAE,EAAEQ,IAAI,CAAC2T,aAAa,CAAC1G,MAAM,CAAC,CAAC;;UAEvD;UACA;UACA;UACA;UACA,IAAIjN,IAAI,CAACyS,MAAM,IAAIzS,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,GAAGtV,IAAI,CAACyS,MAAM,EAAE;YACvD;YACA,IAAIzS,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,KAAKtV,IAAI,CAACyS,MAAM,GAAG,CAAC,EAAE;cAC9C,MAAM,IAAIhP,KAAK,CAAC,6BAA6B,IAC5BzD,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,GAAGtV,IAAI,CAACyS,MAAM,CAAC,GACtC,oCAAoC,CAAC;YACvD;YAEA,IAAI8C,gBAAgB,GAAGvV,IAAI,CAAC8S,UAAU,CAAC0C,YAAY,CAAC,CAAC;YACrD,IAAIC,cAAc,GAAGzV,IAAI,CAAC8S,UAAU,CAACpV,GAAG,CAAC6X,gBAAgB,CAAC;YAE1D,IAAI5I,KAAK,CAAC+I,MAAM,CAACH,gBAAgB,EAAE/V,EAAE,CAAC,EAAE;cACtC,MAAM,IAAIiE,KAAK,CAAC,0DAA0D,CAAC;YAC7E;YAEAzD,IAAI,CAAC8S,UAAU,CAAC6C,MAAM,CAACJ,gBAAgB,CAAC;YACxCvV,IAAI,CAAC0O,YAAY,CAACkH,OAAO,CAACL,gBAAgB,CAAC;YAC3CvV,IAAI,CAAC6V,YAAY,CAACN,gBAAgB,EAAEE,cAAc,CAAC;UACrD;QACF,CAAC,CAAC;MACJ,CAAC;MACDK,gBAAgB,EAAE,SAAAA,CAAUtW,EAAE,EAAE;QAC9B,IAAIQ,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClCpV,IAAI,CAAC8S,UAAU,CAAC6C,MAAM,CAACnW,EAAE,CAAC;UAC1BQ,IAAI,CAAC0O,YAAY,CAACkH,OAAO,CAACpW,EAAE,CAAC;UAC7B,IAAI,CAAEQ,IAAI,CAACyS,MAAM,IAAIzS,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,KAAKtV,IAAI,CAACyS,MAAM,EACzD;UAEF,IAAIzS,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,GAAGtV,IAAI,CAACyS,MAAM,EACtC,MAAMhP,KAAK,CAAC,6BAA6B,CAAC;;UAE5C;UACA;;UAEA,IAAI,CAACzD,IAAI,CAAC4S,kBAAkB,CAACmD,KAAK,CAAC,CAAC,EAAE;YACpC;YACA;YACA,IAAIC,QAAQ,GAAGhW,IAAI,CAAC4S,kBAAkB,CAACqD,YAAY,CAAC,CAAC;YACrD,IAAIC,MAAM,GAAGlW,IAAI,CAAC4S,kBAAkB,CAAClV,GAAG,CAACsY,QAAQ,CAAC;YAClDhW,IAAI,CAACmW,eAAe,CAACH,QAAQ,CAAC;YAC9BhW,IAAI,CAACmV,aAAa,CAACa,QAAQ,EAAEE,MAAM,CAAC;YACpC;UACF;;UAEA;;UAEA;UACA;UACA;UACA;UACA;UACA,IAAIlW,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAChC;;UAEF;UACA;UACA;UACA;UACA,IAAI3R,IAAI,CAACgT,mBAAmB,EAC1B;;UAEF;UACA;UACA;UACA;UACA;UACA;;UAEA,MAAM,IAAIvP,KAAK,CAAC,2BAA2B,CAAC;QAC9C,CAAC,CAAC;MACJ,CAAC;MACD2S,gBAAgB,EAAE,SAAAA,CAAU5W,EAAE,EAAE6W,MAAM,EAAEH,MAAM,EAAE;QAC9C,IAAIlW,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClCpV,IAAI,CAAC8S,UAAU,CAAC/E,GAAG,CAACvO,EAAE,EAAEQ,IAAI,CAAC+T,mBAAmB,CAACmC,MAAM,CAAC,CAAC;UACzD,IAAII,YAAY,GAAGtW,IAAI,CAAC2T,aAAa,CAACuC,MAAM,CAAC;UAC7C,IAAIK,YAAY,GAAGvW,IAAI,CAAC2T,aAAa,CAAC0C,MAAM,CAAC;UAC7C,IAAIG,OAAO,GAAGC,YAAY,CAACC,iBAAiB,CAC1CJ,YAAY,EAAEC,YAAY,CAAC;UAC7B,IAAI,CAACjW,OAAO,CAACkW,OAAO,CAAC,EACnBxW,IAAI,CAAC0O,YAAY,CAAC8H,OAAO,CAAChX,EAAE,EAAEgX,OAAO,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MACDX,YAAY,EAAE,SAAAA,CAAUrW,EAAE,EAAEuI,GAAG,EAAE;QAC/B,IAAI/H,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClCpV,IAAI,CAAC4S,kBAAkB,CAAC7E,GAAG,CAACvO,EAAE,EAAEQ,IAAI,CAAC+T,mBAAmB,CAAChM,GAAG,CAAC,CAAC;;UAE9D;UACA,IAAI/H,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,GAAGtV,IAAI,CAACyS,MAAM,EAAE;YAChD,IAAIkE,aAAa,GAAG3W,IAAI,CAAC4S,kBAAkB,CAAC4C,YAAY,CAAC,CAAC;YAE1DxV,IAAI,CAAC4S,kBAAkB,CAAC+C,MAAM,CAACgB,aAAa,CAAC;;YAE7C;YACA;YACA3W,IAAI,CAACgT,mBAAmB,GAAG,KAAK;UAClC;QACF,CAAC,CAAC;MACJ,CAAC;MACD;MACA;MACAmD,eAAe,EAAE,SAAAA,CAAU3W,EAAE,EAAE;QAC7B,IAAIQ,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClCpV,IAAI,CAAC4S,kBAAkB,CAAC+C,MAAM,CAACnW,EAAE,CAAC;UAClC;UACA;UACA;UACA,IAAI,CAAEQ,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,IAAI,CAAEtV,IAAI,CAACgT,mBAAmB,EAChEhT,IAAI,CAACsU,gBAAgB,CAAC,CAAC;QAC3B,CAAC,CAAC;MACJ,CAAC;MACD;MACA;MACA;MACAsC,YAAY,EAAE,SAAAA,CAAU7O,GAAG,EAAE;QAC3B,IAAI/H,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAI5V,EAAE,GAAGuI,GAAG,CAACa,GAAG;UAChB,IAAI5I,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC,EACzB,MAAMiE,KAAK,CAAC,2CAA2C,GAAGjE,EAAE,CAAC;UAC/D,IAAIQ,IAAI,CAACyS,MAAM,IAAIzS,IAAI,CAAC4S,kBAAkB,CAAC/E,GAAG,CAACrO,EAAE,CAAC,EAChD,MAAMiE,KAAK,CAAC,mDAAmD,GAAGjE,EAAE,CAAC;UAEvE,IAAI8S,KAAK,GAAGtS,IAAI,CAACyS,MAAM;UACvB,IAAIL,UAAU,GAAGpS,IAAI,CAAC0S,WAAW;UACjC,IAAImE,YAAY,GAAIvE,KAAK,IAAItS,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,GAAG,CAAC,GACrDtV,IAAI,CAAC8S,UAAU,CAACpV,GAAG,CAACsC,IAAI,CAAC8S,UAAU,CAAC0C,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;UAC5D,IAAIsB,WAAW,GAAIxE,KAAK,IAAItS,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,GAAG,CAAC,GAC1DtV,IAAI,CAAC4S,kBAAkB,CAAClV,GAAG,CAACsC,IAAI,CAAC4S,kBAAkB,CAAC4C,YAAY,CAAC,CAAC,CAAC,GACnE,IAAI;UACR;UACA;UACA;UACA,IAAIuB,SAAS,GAAG,CAAEzE,KAAK,IAAItS,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,GAAGhD,KAAK,IACvDF,UAAU,CAACrK,GAAG,EAAE8O,YAAY,CAAC,GAAG,CAAC;;UAEnC;UACA;UACA;UACA,IAAIG,iBAAiB,GAAG,CAACD,SAAS,IAAI/W,IAAI,CAACgT,mBAAmB,IAC5DhT,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,GAAGhD,KAAK;;UAExC;UACA;UACA,IAAI2E,mBAAmB,GAAG,CAACF,SAAS,IAAID,WAAW,IACjD1E,UAAU,CAACrK,GAAG,EAAE+O,WAAW,CAAC,IAAI,CAAC;UAEnC,IAAII,QAAQ,GAAGF,iBAAiB,IAAIC,mBAAmB;UAEvD,IAAIF,SAAS,EAAE;YACb/W,IAAI,CAACmV,aAAa,CAAC3V,EAAE,EAAEuI,GAAG,CAAC;UAC7B,CAAC,MAAM,IAAImP,QAAQ,EAAE;YACnBlX,IAAI,CAAC6V,YAAY,CAACrW,EAAE,EAAEuI,GAAG,CAAC;UAC5B,CAAC,MAAM;YACL;YACA/H,IAAI,CAACgT,mBAAmB,GAAG,KAAK;UAClC;QACF,CAAC,CAAC;MACJ,CAAC;MACD;MACA;MACA;MACAmE,eAAe,EAAE,SAAAA,CAAU3X,EAAE,EAAE;QAC7B,IAAIQ,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAI,CAAEpV,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC,IAAI,CAAEQ,IAAI,CAACyS,MAAM,EAC5C,MAAMhP,KAAK,CAAC,oDAAoD,GAAGjE,EAAE,CAAC;UAExE,IAAIQ,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC,EAAE;YAC3BQ,IAAI,CAAC8V,gBAAgB,CAACtW,EAAE,CAAC;UAC3B,CAAC,MAAM,IAAIQ,IAAI,CAAC4S,kBAAkB,CAAC/E,GAAG,CAACrO,EAAE,CAAC,EAAE;YAC1CQ,IAAI,CAACmW,eAAe,CAAC3W,EAAE,CAAC;UAC1B;QACF,CAAC,CAAC;MACJ,CAAC;MACD4X,UAAU,EAAE,SAAAA,CAAU5X,EAAE,EAAE0W,MAAM,EAAE;QAChC,IAAIlW,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAIiC,UAAU,GAAGnB,MAAM,IAAIlW,IAAI,CAACyT,QAAQ,CAAC6D,eAAe,CAACpB,MAAM,CAAC,CAACqB,MAAM;UAEvE,IAAIC,eAAe,GAAGxX,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC;UAC7C,IAAIiY,cAAc,GAAGzX,IAAI,CAACyS,MAAM,IAAIzS,IAAI,CAAC4S,kBAAkB,CAAC/E,GAAG,CAACrO,EAAE,CAAC;UACnE,IAAIkY,YAAY,GAAGF,eAAe,IAAIC,cAAc;UAEpD,IAAIJ,UAAU,IAAI,CAACK,YAAY,EAAE;YAC/B1X,IAAI,CAAC4W,YAAY,CAACV,MAAM,CAAC;UAC3B,CAAC,MAAM,IAAIwB,YAAY,IAAI,CAACL,UAAU,EAAE;YACtCrX,IAAI,CAACmX,eAAe,CAAC3X,EAAE,CAAC;UAC1B,CAAC,MAAM,IAAIkY,YAAY,IAAIL,UAAU,EAAE;YACrC,IAAIhB,MAAM,GAAGrW,IAAI,CAAC8S,UAAU,CAACpV,GAAG,CAAC8B,EAAE,CAAC;YACpC,IAAI4S,UAAU,GAAGpS,IAAI,CAAC0S,WAAW;YACjC,IAAIiF,WAAW,GAAG3X,IAAI,CAACyS,MAAM,IAAIzS,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,IAC7DtV,IAAI,CAAC4S,kBAAkB,CAAClV,GAAG,CAACsC,IAAI,CAAC4S,kBAAkB,CAACqD,YAAY,CAAC,CAAC,CAAC;YACrE,IAAIa,WAAW;YAEf,IAAIU,eAAe,EAAE;cACnB;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA,IAAII,gBAAgB,GAAG,CAAE5X,IAAI,CAACyS,MAAM,IAClCzS,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,KAAK,CAAC,IACpClD,UAAU,CAAC8D,MAAM,EAAEyB,WAAW,CAAC,IAAI,CAAC;cAEtC,IAAIC,gBAAgB,EAAE;gBACpB5X,IAAI,CAACoW,gBAAgB,CAAC5W,EAAE,EAAE6W,MAAM,EAAEH,MAAM,CAAC;cAC3C,CAAC,MAAM;gBACL;gBACAlW,IAAI,CAAC8V,gBAAgB,CAACtW,EAAE,CAAC;gBACzB;gBACAsX,WAAW,GAAG9W,IAAI,CAAC4S,kBAAkB,CAAClV,GAAG,CACvCsC,IAAI,CAAC4S,kBAAkB,CAAC4C,YAAY,CAAC,CAAC,CAAC;gBAEzC,IAAI0B,QAAQ,GAAGlX,IAAI,CAACgT,mBAAmB,IAChC8D,WAAW,IAAI1E,UAAU,CAAC8D,MAAM,EAAEY,WAAW,CAAC,IAAI,CAAE;gBAE3D,IAAII,QAAQ,EAAE;kBACZlX,IAAI,CAAC6V,YAAY,CAACrW,EAAE,EAAE0W,MAAM,CAAC;gBAC/B,CAAC,MAAM;kBACL;kBACAlW,IAAI,CAACgT,mBAAmB,GAAG,KAAK;gBAClC;cACF;YACF,CAAC,MAAM,IAAIyE,cAAc,EAAE;cACzBpB,MAAM,GAAGrW,IAAI,CAAC4S,kBAAkB,CAAClV,GAAG,CAAC8B,EAAE,CAAC;cACxC;cACA;cACA;cACA;cACAQ,IAAI,CAAC4S,kBAAkB,CAAC+C,MAAM,CAACnW,EAAE,CAAC;cAElC,IAAIqX,YAAY,GAAG7W,IAAI,CAAC8S,UAAU,CAACpV,GAAG,CACpCsC,IAAI,CAAC8S,UAAU,CAAC0C,YAAY,CAAC,CAAC,CAAC;cACjCsB,WAAW,GAAG9W,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,IACtCtV,IAAI,CAAC4S,kBAAkB,CAAClV,GAAG,CACzBsC,IAAI,CAAC4S,kBAAkB,CAAC4C,YAAY,CAAC,CAAC,CAAC;;cAE/C;cACA,IAAIuB,SAAS,GAAG3E,UAAU,CAAC8D,MAAM,EAAEW,YAAY,CAAC,GAAG,CAAC;;cAEpD;cACA,IAAIgB,aAAa,GAAI,CAAEd,SAAS,IAAI/W,IAAI,CAACgT,mBAAmB,IACrD,CAAC+D,SAAS,IAAID,WAAW,IACzB1E,UAAU,CAAC8D,MAAM,EAAEY,WAAW,CAAC,IAAI,CAAE;cAE5C,IAAIC,SAAS,EAAE;gBACb/W,IAAI,CAACmV,aAAa,CAAC3V,EAAE,EAAE0W,MAAM,CAAC;cAChC,CAAC,MAAM,IAAI2B,aAAa,EAAE;gBACxB;gBACA7X,IAAI,CAAC4S,kBAAkB,CAAC7E,GAAG,CAACvO,EAAE,EAAE0W,MAAM,CAAC;cACzC,CAAC,MAAM;gBACL;gBACAlW,IAAI,CAACgT,mBAAmB,GAAG,KAAK;gBAChC;gBACA;gBACA,IAAI,CAAEhT,IAAI,CAAC4S,kBAAkB,CAAC0C,IAAI,CAAC,CAAC,EAAE;kBACpCtV,IAAI,CAACsU,gBAAgB,CAAC,CAAC;gBACzB;cACF;YACF,CAAC,MAAM;cACL,MAAM,IAAI7Q,KAAK,CAAC,2EAA2E,CAAC;YAC9F;UACF;QACF,CAAC,CAAC;MACJ,CAAC;MACDqU,uBAAuB,EAAE,SAAAA,CAAA,EAAY;QACnC,IAAI9X,IAAI,GAAG,IAAI;QACfA,IAAI,CAACwT,oBAAoB,CAAC9B,KAAK,CAACE,QAAQ,CAAC;QACzC;QACA;QACA9T,MAAM,CAACia,KAAK,CAAChG,uBAAuB,CAAC,kBAAkB;UACrD,OAAO,CAAC/R,IAAI,CAACwB,QAAQ,IAAI,CAACxB,IAAI,CAACgU,YAAY,CAAC+B,KAAK,CAAC,CAAC,EAAE;YACnD,IAAI/V,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAAE;cAClC;cACA;cACA;cACA;YACF;;YAEA;YACA,IAAI3R,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACE,QAAQ,EAChC,MAAM,IAAInO,KAAK,CAAC,mCAAmC,GAAGzD,IAAI,CAACuU,MAAM,CAAC;YAEpEvU,IAAI,CAACiU,kBAAkB,GAAGjU,IAAI,CAACgU,YAAY;YAC3C,IAAIgE,cAAc,GAAG,EAAEhY,IAAI,CAACkU,gBAAgB;YAC5ClU,IAAI,CAACgU,YAAY,GAAG,IAAI3U,eAAe,CAACuR,MAAM,CAAD,CAAC;;YAE9C;YACA,MAAMqH,aAAa,GAAG,EAAE;YAExBjY,IAAI,CAACiU,kBAAkB,CAACnV,OAAO,CAAC,UAAUmE,EAAE,EAAEzD,EAAE,EAAE;cAChD,MAAM0Y,YAAY,GAAG,IAAI3V,OAAO,CAAC,CAACgH,OAAO,EAAE4O,MAAM,KAAK;gBACpDnY,IAAI,CAACyO,YAAY,CAAC2J,WAAW,CAAC1K,KAAK,CACjC1N,IAAI,CAACwO,kBAAkB,CAACrP,cAAc,EACtCK,EAAE,EACFyD,EAAE,EACF8O,uBAAuB,CAAC,UAASlO,GAAG,EAAEkE,GAAG,EAAE;kBACzC,IAAIlE,GAAG,EAAE;oBACP/F,MAAM,CAACgG,MAAM,CAAC,wCAAwC,EAAED,GAAG,CAAC;oBAC5D;oBACA;oBACA;oBACA;oBACA,IAAI7D,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAAE;sBAClC3R,IAAI,CAACsU,gBAAgB,CAAC,CAAC;oBACzB;oBACA/K,OAAO,CAAC,CAAC;oBACT;kBACF;kBAEA,IACE,CAACvJ,IAAI,CAACwB,QAAQ,IACdxB,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACE,QAAQ,IAC9B5R,IAAI,CAACkU,gBAAgB,KAAK8D,cAAc,EACxC;oBACA;oBACA;oBACA;oBACA;oBACA,IAAI;sBACFhY,IAAI,CAACoX,UAAU,CAAC5X,EAAE,EAAEuI,GAAG,CAAC;sBACxBwB,OAAO,CAAC,CAAC;oBACX,CAAC,CAAC,OAAO1F,GAAG,EAAE;sBACZsU,MAAM,CAACtU,GAAG,CAAC;oBACb;kBACF,CAAC,MAAM;oBACL0F,OAAO,CAAC,CAAC;kBACX;gBACF,CAAC,CACH,CAAC;cACH,CAAC,CAAC;cACF0O,aAAa,CAACxZ,IAAI,CAACyZ,YAAY,CAAC;YAClC,CAAC,CAAC;YACF;YACA,IAAI;cACF,MAAMG,OAAO,GAAG,MAAM9V,OAAO,CAAC+V,UAAU,CAACL,aAAa,CAAC;cACvD,MAAMM,MAAM,GAAGF,OAAO,CACnBG,MAAM,CAACjB,MAAM,IAAIA,MAAM,CAACkB,MAAM,KAAK,UAAU,CAAC,CAC9ChR,GAAG,CAAC8P,MAAM,IAAIA,MAAM,CAACmB,MAAM,CAAC;cAE/B,IAAIH,MAAM,CAACvT,MAAM,GAAG,CAAC,EAAE;gBACrBlH,MAAM,CAACgG,MAAM,CAAC,4BAA4B,EAAEyU,MAAM,CAAC;cACrD;YACF,CAAC,CAAC,OAAO1U,GAAG,EAAE;cACZ/F,MAAM,CAACgG,MAAM,CAAC,mCAAmC,EAAED,GAAG,CAAC;YACzD;YACA;YACA,IAAI7D,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAChC;YACF3R,IAAI,CAACiU,kBAAkB,GAAG,IAAI;UAChC;UACA;UACA;UACA,IAAIjU,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAChC,MAAM3R,IAAI,CAAC2Y,SAAS,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC;MACDA,SAAS,EAAE,eAAAA,CAAA,EAAkB;QAC3B,IAAI3Y,IAAI,GAAG,IAAI;QACfA,IAAI,CAACwT,oBAAoB,CAAC9B,KAAK,CAACG,MAAM,CAAC;QACvC,IAAI+G,MAAM,GAAG5Y,IAAI,CAACoU,gCAAgC,IAAI,EAAE;QACxDpU,IAAI,CAACoU,gCAAgC,GAAG,EAAE;QAC1C,MAAMpU,IAAI,CAAC0O,YAAY,CAACvC,OAAO,CAAC,kBAAkB;UAChD,IAAI;YACF,KAAK,MAAMgF,CAAC,IAAIyH,MAAM,EAAE;cACtB,MAAMzH,CAAC,CAACC,SAAS,CAAC,CAAC;YACrB;UACF,CAAC,CAAC,OAAO1M,CAAC,EAAE;YACVY,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAE;cAACqT;YAAM,CAAC,EAAElU,CAAC,CAAC;UAC/C;QACF,CAAC,CAAC;MACJ,CAAC;MACD8P,yBAAyB,EAAE,SAAAA,CAAUvR,EAAE,EAAE;QACvC,IAAIjD,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClCpV,IAAI,CAACgU,YAAY,CAACjG,GAAG,CAAC1N,OAAO,CAAC4C,EAAE,CAAC,EAAEA,EAAE,CAAC;QACxC,CAAC,CAAC;MACJ,CAAC;MACDwR,iCAAiC,EAAE,SAAAA,CAAUxR,EAAE,EAAE;QAC/C,IAAIjD,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAI5V,EAAE,GAAGa,OAAO,CAAC4C,EAAE,CAAC;UACpB;UACA;;UAEA,IAAIjD,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACE,QAAQ,KAC5B5R,IAAI,CAACiU,kBAAkB,IAAIjU,IAAI,CAACiU,kBAAkB,CAACpG,GAAG,CAACrO,EAAE,CAAC,IAC3DQ,IAAI,CAACgU,YAAY,CAACnG,GAAG,CAACrO,EAAE,CAAC,CAAC,EAAE;YAC/BQ,IAAI,CAACgU,YAAY,CAACjG,GAAG,CAACvO,EAAE,EAAEyD,EAAE,CAAC;YAC7B;UACF;UAEA,IAAIA,EAAE,CAACA,EAAE,KAAK,GAAG,EAAE;YACjB,IAAIjD,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC,IACtBQ,IAAI,CAACyS,MAAM,IAAIzS,IAAI,CAAC4S,kBAAkB,CAAC/E,GAAG,CAACrO,EAAE,CAAE,EAClDQ,IAAI,CAACmX,eAAe,CAAC3X,EAAE,CAAC;UAC5B,CAAC,MAAM,IAAIyD,EAAE,CAACA,EAAE,KAAK,GAAG,EAAE;YACxB,IAAIjD,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC,EACzB,MAAM,IAAIiE,KAAK,CAAC,mDAAmD,CAAC;YACtE,IAAIzD,IAAI,CAAC4S,kBAAkB,IAAI5S,IAAI,CAAC4S,kBAAkB,CAAC/E,GAAG,CAACrO,EAAE,CAAC,EAC5D,MAAM,IAAIiE,KAAK,CAAC,gDAAgD,CAAC;;YAEnE;YACA;YACA,IAAIzD,IAAI,CAACyT,QAAQ,CAAC6D,eAAe,CAACrU,EAAE,CAAC0F,CAAC,CAAC,CAAC4O,MAAM,EAC5CvX,IAAI,CAAC4W,YAAY,CAAC3T,EAAE,CAAC0F,CAAC,CAAC;UAC3B,CAAC,MAAM,IAAI1F,EAAE,CAACA,EAAE,KAAK,GAAG,EAAE;YACxB;YACA;YACAA,EAAE,CAAC0F,CAAC,GAAG4I,kBAAkB,CAACtO,EAAE,CAAC0F,CAAC,CAAC;YAC/B;YACA;YACA;YACA;YACA;YACA;YACA,IAAIkQ,SAAS,GAAG,CAAChL,GAAG,CAAC5K,EAAE,CAAC0F,CAAC,EAAE,MAAM,CAAC,IAAI,CAACkF,GAAG,CAAC5K,EAAE,CAAC0F,CAAC,EAAE,MAAM,CAAC,IAAI,CAACkF,GAAG,CAAC5K,EAAE,CAAC0F,CAAC,EAAE,QAAQ,CAAC;YAChF;YACA;YACA;YACA;YACA,IAAImQ,oBAAoB,GACtB,CAACD,SAAS,IAAIE,4BAA4B,CAAC9V,EAAE,CAAC0F,CAAC,CAAC;YAElD,IAAI6O,eAAe,GAAGxX,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC;YAC7C,IAAIiY,cAAc,GAAGzX,IAAI,CAACyS,MAAM,IAAIzS,IAAI,CAAC4S,kBAAkB,CAAC/E,GAAG,CAACrO,EAAE,CAAC;YAEnE,IAAIqZ,SAAS,EAAE;cACb7Y,IAAI,CAACoX,UAAU,CAAC5X,EAAE,EAAEC,MAAM,CAACC,MAAM,CAAC;gBAACkJ,GAAG,EAAEpJ;cAAE,CAAC,EAAEyD,EAAE,CAAC0F,CAAC,CAAC,CAAC;YACrD,CAAC,MAAM,IAAI,CAAC6O,eAAe,IAAIC,cAAc,KAClCqB,oBAAoB,EAAE;cAC/B;cACA;cACA,IAAI5C,MAAM,GAAGlW,IAAI,CAAC8S,UAAU,CAACjF,GAAG,CAACrO,EAAE,CAAC,GAChCQ,IAAI,CAAC8S,UAAU,CAACpV,GAAG,CAAC8B,EAAE,CAAC,GAAGQ,IAAI,CAAC4S,kBAAkB,CAAClV,GAAG,CAAC8B,EAAE,CAAC;cAC7D0W,MAAM,GAAGvJ,KAAK,CAACvO,KAAK,CAAC8X,MAAM,CAAC;cAE5BA,MAAM,CAACtN,GAAG,GAAGpJ,EAAE;cACf,IAAI;gBACFH,eAAe,CAAC2Z,OAAO,CAAC9C,MAAM,EAAEjT,EAAE,CAAC0F,CAAC,CAAC;cACvC,CAAC,CAAC,OAAOjE,CAAC,EAAE;gBACV,IAAIA,CAAC,CAACuU,IAAI,KAAK,gBAAgB,EAC7B,MAAMvU,CAAC;gBACT;gBACA1E,IAAI,CAACgU,YAAY,CAACjG,GAAG,CAACvO,EAAE,EAAEyD,EAAE,CAAC;gBAC7B,IAAIjD,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACG,MAAM,EAAE;kBAChC7R,IAAI,CAAC8X,uBAAuB,CAAC,CAAC;gBAChC;gBACA;cACF;cACA9X,IAAI,CAACoX,UAAU,CAAC5X,EAAE,EAAEQ,IAAI,CAAC+T,mBAAmB,CAACmC,MAAM,CAAC,CAAC;YACvD,CAAC,MAAM,IAAI,CAAC4C,oBAAoB,IACrB9Y,IAAI,CAACyT,QAAQ,CAACyF,uBAAuB,CAACjW,EAAE,CAAC0F,CAAC,CAAC,IAC1C3I,IAAI,CAAC2S,OAAO,IAAI3S,IAAI,CAAC2S,OAAO,CAACwG,kBAAkB,CAAClW,EAAE,CAAC0F,CAAC,CAAE,EAAE;cAClE3I,IAAI,CAACgU,YAAY,CAACjG,GAAG,CAACvO,EAAE,EAAEyD,EAAE,CAAC;cAC7B,IAAIjD,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACG,MAAM,EAC9B7R,IAAI,CAAC8X,uBAAuB,CAAC,CAAC;YAClC;UACF,CAAC,MAAM;YACL,MAAMrU,KAAK,CAAC,4BAA4B,GAAGR,EAAE,CAAC;UAChD;QACF,CAAC,CAAC;MACJ,CAAC;MAED,MAAMmW,qBAAqBA,CAAA,EAAG;QAC5B,IAAIpZ,IAAI,GAAG,IAAI;QACf,IAAIA,IAAI,CAACwB,QAAQ,EACf,MAAM,IAAIiC,KAAK,CAAC,kCAAkC,CAAC;QAErD,MAAMzD,IAAI,CAACqZ,SAAS,CAAC;UAACC,OAAO,EAAE;QAAI,CAAC,CAAC,CAAC,CAAE;;QAExC,IAAItZ,IAAI,CAACwB,QAAQ,EACf,OAAO,CAAE;;QAEX;QACA;QACA,MAAMxB,IAAI,CAAC0O,YAAY,CAAC1C,KAAK,CAAC,CAAC;QAE/B,MAAMhM,IAAI,CAACuZ,aAAa,CAAC,CAAC,CAAC,CAAE;MAC/B,CAAC;MAED;MACArE,gBAAgB,EAAE,SAAAA,CAAA,EAAY;QAC5B,OAAO,IAAI,CAACkE,qBAAqB,CAAC,CAAC;MACrC,CAAC;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAI,UAAU,EAAE,SAAAA,CAAA,EAAY;QACtB,IAAIxZ,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAIpV,IAAI,CAACwB,QAAQ,EACf;;UAEF;UACAxB,IAAI,CAACgU,YAAY,GAAG,IAAI3U,eAAe,CAACuR,MAAM,CAAD,CAAC;UAC9C5Q,IAAI,CAACiU,kBAAkB,GAAG,IAAI;UAC9B,EAAEjU,IAAI,CAACkU,gBAAgB,CAAC,CAAE;UAC1BlU,IAAI,CAACwT,oBAAoB,CAAC9B,KAAK,CAACC,QAAQ,CAAC;;UAEzC;UACA;UACA7T,MAAM,CAACia,KAAK,CAAC,kBAAkB;YAC7B,MAAM/X,IAAI,CAACqZ,SAAS,CAAC,CAAC;YACtB,MAAMrZ,IAAI,CAACuZ,aAAa,CAAC,CAAC;UAC5B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MAED;MACA,MAAME,cAAcA,CAAC3N,OAAO,EAAE;QAC5B,IAAI9L,IAAI,GAAG,IAAI;QACf8L,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;QACvB,IAAI4E,UAAU,EAAEgJ,SAAS;;QAEzB;QACA,OAAO,IAAI,EAAE;UACX;UACA,IAAI1Z,IAAI,CAACwB,QAAQ,EACf;UAEFkP,UAAU,GAAG,IAAIrR,eAAe,CAACuR,MAAM,CAAD,CAAC;UACvC8I,SAAS,GAAG,IAAIra,eAAe,CAACuR,MAAM,CAAD,CAAC;;UAEtC;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI+I,MAAM,GAAG3Z,IAAI,CAAC4Z,eAAe,CAAC;YAAEtH,KAAK,EAAEtS,IAAI,CAACyS,MAAM,GAAG;UAAE,CAAC,CAAC;UAC7D,IAAI;YACF,MAAMkH,MAAM,CAAC7a,OAAO,CAAC,UAAUiJ,GAAG,EAAE8R,CAAC,EAAE;cAAG;cACxC,IAAI,CAAC7Z,IAAI,CAACyS,MAAM,IAAIoH,CAAC,GAAG7Z,IAAI,CAACyS,MAAM,EAAE;gBACnC/B,UAAU,CAAC3C,GAAG,CAAChG,GAAG,CAACa,GAAG,EAAEb,GAAG,CAAC;cAC9B,CAAC,MAAM;gBACL2R,SAAS,CAAC3L,GAAG,CAAChG,GAAG,CAACa,GAAG,EAAEb,GAAG,CAAC;cAC7B;YACF,CAAC,CAAC;YACF;UACF,CAAC,CAAC,OAAOrD,CAAC,EAAE;YACV,IAAIoH,OAAO,CAACwN,OAAO,IAAI,OAAO5U,CAAC,CAACsM,IAAK,KAAK,QAAQ,EAAE;cAClD;cACA;cACA;cACA;cACA;cACA,MAAMhR,IAAI,CAAC0O,YAAY,CAACxC,UAAU,CAACxH,CAAC,CAAC;cACrC;YACF;;YAEA;YACA;YACA5G,MAAM,CAACgG,MAAM,CAAC,mCAAmC,EAAEY,CAAC,CAAC;YACrD,MAAM5G,MAAM,CAACgc,WAAW,CAAC,GAAG,CAAC;UAC/B;QACF;QAEA,IAAI9Z,IAAI,CAACwB,QAAQ,EACf;QAEFxB,IAAI,CAAC+Z,kBAAkB,CAACrJ,UAAU,EAAEgJ,SAAS,CAAC;MAChD,CAAC;MAED;MACAL,SAAS,EAAE,SAAAA,CAAUvN,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC2N,cAAc,CAAC3N,OAAO,CAAC;MACrC,CAAC;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAwI,gBAAgB,EAAE,SAAAA,CAAA,EAAY;QAC5B,IAAItU,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAIpV,IAAI,CAACwB,QAAQ,EACf;;UAEF;UACA;UACA,IAAIxB,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAAE;YAClC3R,IAAI,CAACwZ,UAAU,CAAC,CAAC;YACjB,MAAM,IAAI1H,eAAe,CAAD,CAAC;UAC3B;;UAEA;UACA;UACA9R,IAAI,CAACmU,yBAAyB,GAAG,IAAI;QACvC,CAAC,CAAC;MACJ,CAAC;MAED;MACAoF,aAAa,EAAE,eAAAA,CAAA,EAAkB;QAC/B,IAAIvZ,IAAI,GAAG,IAAI;QAEf,IAAIA,IAAI,CAACwB,QAAQ,EACf;QAEF,MAAMxB,IAAI,CAACyO,YAAY,CAAC4F,YAAY,CAAC3O,iBAAiB,CAAC,CAAC;QAExD,IAAI1F,IAAI,CAACwB,QAAQ,EACf;QAEF,IAAIxB,IAAI,CAACuU,MAAM,KAAK7C,KAAK,CAACC,QAAQ,EAChC,MAAMlO,KAAK,CAAC,qBAAqB,GAAGzD,IAAI,CAACuU,MAAM,CAAC;QAElD,IAAIvU,IAAI,CAACmU,yBAAyB,EAAE;UAClCnU,IAAI,CAACmU,yBAAyB,GAAG,KAAK;UACtCnU,IAAI,CAACwZ,UAAU,CAAC,CAAC;QACnB,CAAC,MAAM,IAAIxZ,IAAI,CAACgU,YAAY,CAAC+B,KAAK,CAAC,CAAC,EAAE;UACpC,MAAM/V,IAAI,CAAC2Y,SAAS,CAAC,CAAC;QACxB,CAAC,MAAM;UACL3Y,IAAI,CAAC8X,uBAAuB,CAAC,CAAC;QAChC;MACF,CAAC;MAED8B,eAAe,EAAE,SAAAA,CAAUI,gBAAgB,EAAE;QAC3C,IAAIha,IAAI,GAAG,IAAI;QACf,OAAOlC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UACzC;UACA;UACA;UACA;UACA;UACA,IAAItJ,OAAO,GAAGrM,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEM,IAAI,CAACwO,kBAAkB,CAAC1C,OAAO,CAAC;;UAEhE;UACA;UACArM,MAAM,CAACC,MAAM,CAACoM,OAAO,EAAEkO,gBAAgB,CAAC;UAExClO,OAAO,CAACmB,MAAM,GAAGjN,IAAI,CAAC6T,iBAAiB;UACvC,OAAO/H,OAAO,CAACmO,SAAS;UACxB;UACA,IAAIC,WAAW,GAAG,IAAI3Z,iBAAiB,CACrCP,IAAI,CAACwO,kBAAkB,CAACrP,cAAc,EACtCa,IAAI,CAACwO,kBAAkB,CAACjP,QAAQ,EAChCuM,OAAO,CAAC;UACV,OAAO,IAAI2F,MAAM,CAACzR,IAAI,CAACyO,YAAY,EAAEyL,WAAW,CAAC;QACnD,CAAC,CAAC;MACJ,CAAC;MAGD;MACA;MACA;MACA;MACA;MACA;MACA;MACAH,kBAAkB,EAAE,SAAAA,CAAUrJ,UAAU,EAAEgJ,SAAS,EAAE;QACnD,IAAI1Z,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAElC;UACA;UACA,IAAIpV,IAAI,CAACyS,MAAM,EAAE;YACfzS,IAAI,CAAC4S,kBAAkB,CAAC1K,KAAK,CAAC,CAAC;UACjC;;UAEA;UACA;UACA,IAAIiS,WAAW,GAAG,EAAE;UACpBna,IAAI,CAAC8S,UAAU,CAAChU,OAAO,CAAC,UAAUiJ,GAAG,EAAEvI,EAAE,EAAE;YACzC,IAAI,CAACkR,UAAU,CAAC7C,GAAG,CAACrO,EAAE,CAAC,EACrB2a,WAAW,CAAC1b,IAAI,CAACe,EAAE,CAAC;UACxB,CAAC,CAAC;UACF2a,WAAW,CAACrb,OAAO,CAAC,UAAUU,EAAE,EAAE;YAChCQ,IAAI,CAAC8V,gBAAgB,CAACtW,EAAE,CAAC;UAC3B,CAAC,CAAC;;UAEF;UACA;UACA;UACAkR,UAAU,CAAC5R,OAAO,CAAC,UAAUiJ,GAAG,EAAEvI,EAAE,EAAE;YACpCQ,IAAI,CAACoX,UAAU,CAAC5X,EAAE,EAAEuI,GAAG,CAAC;UAC1B,CAAC,CAAC;;UAEF;UACA;UACA;UACA,IAAI/H,IAAI,CAAC8S,UAAU,CAACwC,IAAI,CAAC,CAAC,KAAK5E,UAAU,CAAC4E,IAAI,CAAC,CAAC,EAAE;YAChDxX,MAAM,CAACgG,MAAM,CAAC,wDAAwD,GACpE,uDAAuD,EACvD9D,IAAI,CAACwO,kBAAkB,CAAC;UAC5B;UAEAxO,IAAI,CAAC8S,UAAU,CAAChU,OAAO,CAAC,UAAUiJ,GAAG,EAAEvI,EAAE,EAAE;YACzC,IAAI,CAACkR,UAAU,CAAC7C,GAAG,CAACrO,EAAE,CAAC,EACrB,MAAMiE,KAAK,CAAC,gDAAgD,GAAGjE,EAAE,CAAC;UACtE,CAAC,CAAC;;UAEF;UACAka,SAAS,CAAC5a,OAAO,CAAC,UAAUiJ,GAAG,EAAEvI,EAAE,EAAE;YACnCQ,IAAI,CAAC6V,YAAY,CAACrW,EAAE,EAAEuI,GAAG,CAAC;UAC5B,CAAC,CAAC;UAEF/H,IAAI,CAACgT,mBAAmB,GAAG0G,SAAS,CAACpE,IAAI,CAAC,CAAC,GAAGtV,IAAI,CAACyS,MAAM;QAC3D,CAAC,CAAC;MACJ,CAAC;MAED;MACA;MACA;MACA;MACA;MACA;MACA5G,KAAK,EAAE,eAAAA,CAAA,EAAiB;QACtB,IAAI7L,IAAI,GAAG,IAAI;QACf,IAAIA,IAAI,CAACwB,QAAQ,EACf;QACFxB,IAAI,CAACwB,QAAQ,GAAG,IAAI;;QAEpB;QACA;QACA;QACA;QACA;QACA,KAAK,MAAM2P,CAAC,IAAInR,IAAI,CAACoU,gCAAgC,EAAE;UACrD,MAAMjD,CAAC,CAACC,SAAS,CAAC,CAAC;QACrB;QACApR,IAAI,CAACoU,gCAAgC,GAAG,IAAI;;QAE5C;QACApU,IAAI,CAAC8S,UAAU,GAAG,IAAI;QACtB9S,IAAI,CAAC4S,kBAAkB,GAAG,IAAI;QAC9B5S,IAAI,CAACgU,YAAY,GAAG,IAAI;QACxBhU,IAAI,CAACiU,kBAAkB,GAAG,IAAI;QAC9BjU,IAAI,CAACoa,iBAAiB,GAAG,IAAI;QAC7Bpa,IAAI,CAACqa,gBAAgB,GAAG,IAAI;QAE5B5P,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACpE,gBAAgB,EAAE,uBAAuB,EAAE,CAAC,CAAC,CAAC;QAAC,IAAA2P,yBAAA;QAAA,IAAAC,iBAAA;QAAA,IAAAC,cAAA;QAAA;UAEnD,SAAAC,SAAA,GAAAnJ,cAAA,CAA2BtR,IAAI,CAACiT,YAAY,GAAAyH,KAAA,EAAAJ,yBAAA,KAAAI,KAAA,SAAAD,SAAA,CAAAE,IAAA,IAAAC,IAAA,EAAAN,yBAAA,UAAE;YAAA,MAA7BxR,MAAM,GAAA4R,KAAA,CAAAjS,KAAA;YAAA;cACrB,MAAMK,MAAM,CAACjK,IAAI,CAAC,CAAC;YAAC;UACtB;QAAC,SAAAgF,GAAA;UAAA0W,iBAAA;UAAAC,cAAA,GAAA3W,GAAA;QAAA;UAAA;YAAA,IAAAyW,yBAAA,IAAAG,SAAA,CAAAI,MAAA;cAAA,MAAAJ,SAAA,CAAAI,MAAA;YAAA;UAAA;YAAA,IAAAN,iBAAA;cAAA,MAAAC,cAAA;YAAA;UAAA;QAAA;MACH,CAAC;MACD3b,IAAI,EAAE,eAAAA,CAAA,EAAiB;QACrB,MAAMmB,IAAI,GAAG,IAAI;QACjB,OAAO,MAAMA,IAAI,CAAC6L,KAAK,CAAC,CAAC;MAC3B,CAAC;MAED2H,oBAAoB,EAAE,SAAAA,CAAUsH,KAAK,EAAE;QACrC,IAAI9a,IAAI,GAAG,IAAI;QACflC,MAAM,CAACsX,gBAAgB,CAAC,YAAY;UAClC,IAAI2F,GAAG,GAAG,IAAIC,IAAI,CAAD,CAAC;UAElB,IAAIhb,IAAI,CAACuU,MAAM,EAAE;YACf,IAAI0G,QAAQ,GAAGF,GAAG,GAAG/a,IAAI,CAACkb,eAAe;YACzCzQ,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK,CAACC,mBAAmB,CACtE,gBAAgB,EAAE,gBAAgB,GAAG3K,IAAI,CAACuU,MAAM,GAAG,QAAQ,EAAE0G,QAAQ,CAAC;UAC1E;UAEAjb,IAAI,CAACuU,MAAM,GAAGuG,KAAK;UACnB9a,IAAI,CAACkb,eAAe,GAAGH,GAAG;QAC5B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA;IACA;IACAle,kBAAkB,CAACse,eAAe,GAAG,UAAU9c,iBAAiB,EAAEqV,OAAO,EAAE;MACzE;MACA,IAAI5H,OAAO,GAAGzN,iBAAiB,CAACyN,OAAO;;MAEvC;MACA;MACA,IAAIA,OAAO,CAACsP,YAAY,IAAItP,OAAO,CAACuP,aAAa,EAC/C,OAAO,KAAK;;MAEd;MACA;MACA;MACA;MACA,IAAIvP,OAAO,CAACwP,IAAI,IAAKxP,OAAO,CAACwG,KAAK,IAAI,CAACxG,OAAO,CAACtH,IAAK,EAAE,OAAO,KAAK;;MAElE;MACA;MACA,MAAMyI,MAAM,GAAGnB,OAAO,CAACmB,MAAM,IAAInB,OAAO,CAACxH,UAAU;MACnD,IAAI2I,MAAM,EAAE;QACV,IAAI;UACF5N,eAAe,CAACkc,yBAAyB,CAACtO,MAAM,CAAC;QACnD,CAAC,CAAC,OAAOvI,CAAC,EAAE;UACV,IAAIA,CAAC,CAACuU,IAAI,KAAK,gBAAgB,EAAE;YAC/B,OAAO,KAAK;UACd,CAAC,MAAM;YACL,MAAMvU,CAAC;UACT;QACF;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,CAACgP,OAAO,CAAC8H,QAAQ,CAAC,CAAC,IAAI,CAAC9H,OAAO,CAAC+H,WAAW,CAAC,CAAC;IACtD,CAAC;IAED,IAAI1C,4BAA4B,GAAG,SAAAA,CAAU2C,QAAQ,EAAE;MACrD,OAAOjc,MAAM,CAACkc,OAAO,CAACD,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAhS,IAAA,EAA+B;QAAA,IAArB,CAACiS,SAAS,EAAE5O,MAAM,CAAC,GAAArD,IAAA;QACjE,OAAOnK,MAAM,CAACkc,OAAO,CAAC1O,MAAM,CAAC,CAAC2O,KAAK,CAAC,UAAA5O,KAAA,EAA0B;UAAA,IAAhB,CAAC8O,KAAK,EAAErT,KAAK,CAAC,GAAAuE,KAAA;UAC1D,OAAO,CAAC,SAAS,CAAC+O,IAAI,CAACD,KAAK,CAAC;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAAChc,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IChjCF5D,OAAA,CAAAC,MAAA;MAAAiV,kBAAA,EAAAA,CAAA,KAAAA;IAAA;IAAA,IAAA5E,KAAA;IAAAtQ,OAAA,CAAAK,IAAA;MAAAiQ,MAAAhQ,CAAA;QAAAgQ,KAAA,GAAAhQ,CAAA;MAAA;IAAA;IAAA,IAAAI,oBAAA,WAAAA,oBAAA;IA4DA,MAAMif,qBAAqB,GAAG,eAAe;IAE7C;;;IAGA,SAASC,kBAAkBA,CAACH,KAAa;MACvC,OAAOE,qBAAqB,CAACD,IAAI,CAACD,KAAK,CAAC;IAC1C;IAEA;;;;IAIA,SAASI,eAAeA,CAACC,QAAiB;MACxC,OACEA,QAAQ,KAAK,IAAI,IACjB,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,GAAG,IAAIA,QAAQ,IACdA,QAA0B,CAACC,CAAC,KAAK,IAAI,IACtC3c,MAAM,CAAC+M,IAAI,CAAC2P,QAAQ,CAAC,CAACP,KAAK,CAACK,kBAAkB,CAAC;IAEnD;IAEA;;;;IAIA,SAASlZ,IAAIA,CAACsZ,MAAc,EAAEpd,GAAW;MACvC,OAAOod,MAAM,MAAA1U,MAAA,CAAM0U,MAAM,OAAA1U,MAAA,CAAI1I,GAAG,IAAKA,GAAG;IAC1C;IAEA;;;;;;;;;IASA,SAASqd,iBAAiBA,CACxB3e,MAA2B,EAC3B4e,MAAW,EACXF,MAAc;MAEd,IACEjR,KAAK,CAACoR,OAAO,CAACD,MAAM,CAAC,IACrB,OAAOA,MAAM,KAAK,QAAQ,IAC1BA,MAAM,KAAK,IAAI,IACfA,MAAM,YAAYE,KAAK,CAACC,QAAQ,IAChC/P,KAAK,CAACgQ,aAAa,CAACJ,MAAM,CAAC,EAC3B;QACA5e,MAAM,CAAC0e,MAAM,CAAC,GAAGE,MAAM;QACvB;MACF;MAEA,MAAMZ,OAAO,GAAGlc,MAAM,CAACkc,OAAO,CAACY,MAAM,CAAC;MACtC,IAAIZ,OAAO,CAAC3W,MAAM,EAAE;QAClB2W,OAAO,CAAC7c,OAAO,CAAC8K,IAAA,IAAiB;UAAA,IAAhB,CAAC3K,GAAG,EAAEwJ,KAAK,CAAC,GAAAmB,IAAA;UAC3B0S,iBAAiB,CAAC3e,MAAM,EAAE8K,KAAK,EAAE1F,IAAI,CAACsZ,MAAM,EAAEpd,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC;MACJ,CAAC,MAAM;QACLtB,MAAM,CAAC0e,MAAM,CAAC,GAAGE,MAAM;MACzB;IACF;IAEA;;;;;;;;;;;IAWA,SAASK,gBAAgBA,CACvBC,UAAsB,EACtBC,IAAe,EACJ;MAAA,IAAXT,MAAM,GAAAnR,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,EAAE;MAEXzL,MAAM,CAACkc,OAAO,CAACmB,IAAI,CAAC,CAAChe,OAAO,CAACkO,KAAA,IAAqB;QAAA,IAApB,CAAC+P,OAAO,EAAEtU,KAAK,CAAC,GAAAuE,KAAA;QAC5C,IAAI+P,OAAO,KAAK,GAAG,EAAE;UAAA,IAAAC,kBAAA;UACnB;UACA,CAAAA,kBAAA,GAAAH,UAAU,CAACI,MAAM,cAAAD,kBAAA,cAAAA,kBAAA,GAAjBH,UAAU,CAACI,MAAM,GAAK,EAAE;UACxBxd,MAAM,CAAC+M,IAAI,CAAC/D,KAAK,CAAC,CAAC3J,OAAO,CAACG,GAAG,IAAG;YAC/B4d,UAAU,CAACI,MAAO,CAACla,IAAI,CAACsZ,MAAM,EAAEpd,GAAG,CAAC,CAAC,GAAG,IAAI;UAC9C,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI8d,OAAO,KAAK,GAAG,EAAE;UAAA,IAAAG,gBAAA;UAC1B;UACA,CAAAA,gBAAA,GAAAL,UAAU,CAACM,IAAI,cAAAD,gBAAA,cAAAA,gBAAA,GAAfL,UAAU,CAACM,IAAI,GAAK,EAAE;UACtBb,iBAAiB,CAACO,UAAU,CAACM,IAAI,EAAE1U,KAAK,EAAE4T,MAAM,CAAC;QACnD,CAAC,MAAM,IAAIU,OAAO,KAAK,GAAG,EAAE;UAAA,IAAAK,iBAAA;UAC1B;UACA,CAAAA,iBAAA,GAAAP,UAAU,CAACM,IAAI,cAAAC,iBAAA,cAAAA,iBAAA,GAAfP,UAAU,CAACM,IAAI,GAAK,EAAE;UACtB1d,MAAM,CAACkc,OAAO,CAAClT,KAAK,CAAC,CAAC3J,OAAO,CAACue,KAAA,IAAsB;YAAA,IAArB,CAACpe,GAAG,EAAEqe,UAAU,CAAC,GAAAD,KAAA;YAC9CR,UAAU,CAACM,IAAK,CAACpa,IAAI,CAACsZ,MAAM,EAAEpd,GAAG,CAAC,CAAC,GAAGqe,UAAU;UAClD,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIP,OAAO,CAAC5T,UAAU,CAAC,GAAG,CAAC,EAAE;UAClC;UACA,MAAMlK,GAAG,GAAG8d,OAAO,CAAC3T,KAAK,CAAC,CAAC,CAAC;UAC5B,IAAI8S,eAAe,CAACzT,KAAK,CAAC,EAAE;YAC1B;YACAhJ,MAAM,CAACkc,OAAO,CAAClT,KAAK,CAAC,CAAC3J,OAAO,CAACye,KAAA,IAA2B;cAAA,IAA1B,CAACC,QAAQ,EAAEF,UAAU,CAAC,GAAAC,KAAA;cACnD,IAAIC,QAAQ,KAAK,GAAG,EAAE;cAEtB,MAAMC,WAAW,GAAG1a,IAAI,CAACsZ,MAAM,KAAA1U,MAAA,CAAK1I,GAAG,OAAA0I,MAAA,CAAI6V,QAAQ,CAACpU,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;cAC/D,IAAIoU,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACvBZ,gBAAgB,CAACC,UAAU,EAAES,UAAU,EAAEG,WAAW,CAAC;cACvD,CAAC,MAAM,IAAIH,UAAU,KAAK,IAAI,EAAE;gBAAA,IAAAI,mBAAA;gBAC9B,CAAAA,mBAAA,GAAAb,UAAU,CAACI,MAAM,cAAAS,mBAAA,cAAAA,mBAAA,GAAjBb,UAAU,CAACI,MAAM,GAAK,EAAE;gBACxBJ,UAAU,CAACI,MAAM,CAACQ,WAAW,CAAC,GAAG,IAAI;cACvC,CAAC,MAAM;gBAAA,IAAAE,iBAAA;gBACL,CAAAA,iBAAA,GAAAd,UAAU,CAACM,IAAI,cAAAQ,iBAAA,cAAAA,iBAAA,GAAfd,UAAU,CAACM,IAAI,GAAK,EAAE;gBACtBN,UAAU,CAACM,IAAI,CAACM,WAAW,CAAC,GAAGH,UAAU;cAC3C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIre,GAAG,EAAE;YACd;YACA2d,gBAAgB,CAACC,UAAU,EAAEpU,KAAK,EAAE1F,IAAI,CAACsZ,MAAM,EAAEpd,GAAG,CAAC,CAAC;UACxD;QACF;MACF,CAAC,CAAC;IACJ;IAEA;;;;;;;;;IASM,SAAUsS,kBAAkBA,CAACsL,UAAsB;MACvD,IAAIA,UAAU,CAACe,EAAE,KAAK,CAAC,IAAI,CAACf,UAAU,CAACC,IAAI,EAAE;QAC3C,OAAOD,UAAU;MACnB;MAEA,MAAMgB,mBAAmB,GAAe;QAAED,EAAE,EAAE;MAAC,CAAE;MACjDhB,gBAAgB,CAACiB,mBAAmB,EAAEhB,UAAU,CAACC,IAAI,CAAC;MACtD,OAAOe,mBAAmB;IAC5B;IAAC/d,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AC/LD1C,MAAA,CAAAjB,MAAA;EAAAiE,iBAAA,EAAAA,CAAA,KAAAA;AAAA;AAQM,MAAOA,iBAAiB;EAK5BS,YAAY7B,cAAsB,EAAEI,QAAa,EAAEuM,OAAuB;IAAA,KAJ1E3M,cAAc;IAAA,KACdI,QAAQ;IAAA,KACRuM,OAAO;IAGL,IAAI,CAAC3M,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACI,QAAQ,GAAGkd,KAAK,CAACqB,UAAU,CAACC,gBAAgB,CAACxe,QAAQ,CAAC;IAC3D,IAAI,CAACuM,OAAO,GAAGA,OAAO,IAAI,EAAE;EAC9B;;;;;;;;;;;;;;;IC9BF,IAAI5L,aAAa;IAAC3C,MAAM,CAACb,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuD,aAAa,GAACvD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGY,MAAM,CAACjB,MAAM,CAAC;MAACM,eAAe,EAACA,CAAA,KAAIA;IAAe,CAAC,CAAC;IAAC,IAAIkB,MAAM;IAACP,MAAM,CAACb,IAAI,CAAC,eAAe,EAAC;MAACoB,MAAMA,CAACnB,CAAC,EAAC;QAACmB,MAAM,GAACnB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIqhB,mBAAmB,EAACC,kBAAkB;IAAC1gB,MAAM,CAACb,IAAI,CAAC,4BAA4B,EAAC;MAACshB,mBAAmBA,CAACrhB,CAAC,EAAC;QAACqhB,mBAAmB,GAACrhB,CAAC;MAAA,CAAC;MAACshB,kBAAkBA,CAACthB,CAAC,EAAC;QAACshB,kBAAkB,GAACthB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIuhB,IAAI;IAAC3gB,MAAM,CAACb,IAAI,CAAC,MAAM,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuhB,IAAI,GAACvhB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIwhB,kBAAkB;IAAC5gB,MAAM,CAACb,IAAI,CAAC,uBAAuB,EAAC;MAACyhB,kBAAkBA,CAACxhB,CAAC,EAAC;QAACwhB,kBAAkB,GAACxhB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI8U,MAAM;IAAClU,MAAM,CAACb,IAAI,CAAC,UAAU,EAAC;MAAC+U,MAAMA,CAAC9U,CAAC,EAAC;QAAC8U,MAAM,GAAC9U,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4D,iBAAiB;IAAChD,MAAM,CAACb,IAAI,CAAC,sBAAsB,EAAC;MAAC6D,iBAAiBA,CAAC5D,CAAC,EAAC;QAAC4D,iBAAiB,GAAC5D,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI0Q,UAAU;IAAC9P,MAAM,CAACb,IAAI,CAAC,eAAe,EAAC;MAAC2Q,UAAUA,CAAC1Q,CAAC,EAAC;QAAC0Q,UAAU,GAAC1Q,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,OAAO,EAACshB,0BAA0B,EAACC,YAAY,EAACC,eAAe;IAAC/gB,MAAM,CAACb,IAAI,CAAC,gBAAgB,EAAC;MAACI,OAAOA,CAACH,CAAC,EAAC;QAACG,OAAO,GAACH,CAAC;MAAA,CAAC;MAACyhB,0BAA0BA,CAACzhB,CAAC,EAAC;QAACyhB,0BAA0B,GAACzhB,CAAC;MAAA,CAAC;MAAC0hB,YAAYA,CAAC1hB,CAAC,EAAC;QAAC0hB,YAAY,GAAC1hB,CAAC;MAAA,CAAC;MAAC2hB,eAAeA,CAAC3hB,CAAC,EAAC;QAAC2hB,eAAe,GAAC3hB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4hB,aAAa;IAAChhB,MAAM,CAACb,IAAI,CAAC,kBAAkB,EAAC;MAAC6hB,aAAaA,CAAC5hB,CAAC,EAAC;QAAC4hB,aAAa,GAAC5hB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIgN,kBAAkB;IAACpM,MAAM,CAACb,IAAI,CAAC,qBAAqB,EAAC;MAACiN,kBAAkBA,CAAChN,CAAC,EAAC;QAACgN,kBAAkB,GAAChN,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,kBAAkB;IAACU,MAAM,CAACb,IAAI,CAAC,wBAAwB,EAAC;MAACG,kBAAkBA,CAACF,CAAC,EAAC;QAACE,kBAAkB,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIyD,gBAAgB,EAAC3D,WAAW;IAACc,MAAM,CAACb,IAAI,CAAC,iBAAiB,EAAC;MAAC0D,gBAAgBA,CAACzD,CAAC,EAAC;QAACyD,gBAAgB,GAACzD,CAAC;MAAA,CAAC;MAACF,WAAWA,CAACE,CAAC,EAAC;QAACF,WAAW,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIsR,oBAAoB;IAAC1Q,MAAM,CAACb,IAAI,CAAC,0BAA0B,EAAC;MAACuR,oBAAoBA,CAACtR,CAAC,EAAC;QAACsR,oBAAoB,GAACtR,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAcliD,MAAMyhB,iBAAiB,GAAG,OAAO;IACjC,MAAMC,aAAa,GAAG,QAAQ;IAC9B,MAAMC,UAAU,GAAG,KAAK;IAExB,MAAMC,uBAAuB,GAAG,EAAE;IAE3B,MAAM/hB,eAAe,GAAG,SAAAA,CAAUgiB,GAAG,EAAE9S,OAAO,EAAE;MAAA,IAAA7F,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACrD,IAAInG,IAAI,GAAG,IAAI;MACf8L,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;MACvB9L,IAAI,CAAC6e,oBAAoB,GAAG,CAAC,CAAC;MAC9B7e,IAAI,CAAC8e,eAAe,GAAG,IAAI1b,IAAI,CAAD,CAAC;MAE/B,MAAM2b,WAAW,GAAA7e,aAAA,CAAAA,aAAA,KACXuc,KAAK,CAACuC,kBAAkB,IAAI,CAAC,CAAC,GAC9B,EAAA/Y,gBAAA,GAAAnI,MAAM,CAACmJ,QAAQ,cAAAhB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBiB,QAAQ,cAAAhB,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BiB,KAAK,cAAAhB,sBAAA,uBAAhCA,sBAAA,CAAkC2F,OAAO,KAAI,CAAC,CAAC,CACpD;MAED,IAAImT,YAAY,GAAGxf,MAAM,CAACC,MAAM,CAAC;QAC/Bwf,eAAe,EAAE;MACnB,CAAC,EAAEH,WAAW,CAAC;;MAIf;MACA;MACA,IAAI,aAAa,IAAIjT,OAAO,EAAE;QAC5B;QACA;QACAmT,YAAY,CAAClZ,WAAW,GAAG+F,OAAO,CAAC/F,WAAW;MAChD;MACA,IAAI,aAAa,IAAI+F,OAAO,EAAE;QAC5BmT,YAAY,CAACjZ,WAAW,GAAG8F,OAAO,CAAC9F,WAAW;MAChD;;MAEA;MACA;MACAvG,MAAM,CAACkc,OAAO,CAACsD,YAAY,IAAI,CAAC,CAAC,CAAC,CAC/BzG,MAAM,CAAC5O,IAAA;QAAA,IAAC,CAAC3K,GAAG,CAAC,GAAA2K,IAAA;QAAA,OAAK3K,GAAG,IAAIA,GAAG,CAACkgB,QAAQ,CAACX,iBAAiB,CAAC;MAAA,EAAC,CACzD1f,OAAO,CAACkO,KAAA,IAAkB;QAAA,IAAjB,CAAC/N,GAAG,EAAEwJ,KAAK,CAAC,GAAAuE,KAAA;QACpB,MAAMoS,UAAU,GAAGngB,GAAG,CAACogB,OAAO,CAACb,iBAAiB,EAAE,EAAE,CAAC;QACrDS,YAAY,CAACG,UAAU,CAAC,GAAGlB,IAAI,CAACnb,IAAI,CAACuc,MAAM,CAACC,YAAY,CAAC,CAAC,EACxDd,aAAa,EAAEC,UAAU,EAAEjW,KAAK,CAAC;QACnC,OAAOwW,YAAY,CAAChgB,GAAG,CAAC;MAC1B,CAAC,CAAC;MAEJe,IAAI,CAACwG,EAAE,GAAG,IAAI;MACdxG,IAAI,CAACqU,YAAY,GAAG,IAAI;MACxBrU,IAAI,CAACoY,WAAW,GAAG,IAAI;MAEvB6G,YAAY,CAACO,UAAU,GAAG;QACxBvG,IAAI,EAAE,QAAQ;QACd5b,OAAO,EAAES,MAAM,CAAC2hB;MAClB,CAAC;MAEDzf,IAAI,CAAC0f,MAAM,GAAG,IAAI5iB,OAAO,CAAC6iB,WAAW,CAACf,GAAG,EAAEK,YAAY,CAAC;MACxDjf,IAAI,CAACwG,EAAE,GAAGxG,IAAI,CAAC0f,MAAM,CAAClZ,EAAE,CAAC,CAAC;MAE1BxG,IAAI,CAAC0f,MAAM,CAACE,EAAE,CAAC,0BAA0B,EAAE9hB,MAAM,CAAC6F,eAAe,CAACkc,KAAK,IAAI;QACzE;QACA;QACA;QACA,IACEA,KAAK,CAACC,mBAAmB,CAACC,IAAI,KAAK,WAAW,IAC9CF,KAAK,CAACG,cAAc,CAACD,IAAI,KAAK,WAAW,EACzC;UACA/f,IAAI,CAAC8e,eAAe,CAAC3W,IAAI,CAAC3E,QAAQ,IAAI;YACpCA,QAAQ,CAAC,CAAC;YACV,OAAO,IAAI;UACb,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAAC;MAEH,IAAIsI,OAAO,CAAC7K,QAAQ,IAAI,CAAEwJ,OAAO,CAAC,eAAe,CAAC,EAAE;QAClDzK,IAAI,CAACqU,YAAY,GAAG,IAAI5X,WAAW,CAACqP,OAAO,CAAC7K,QAAQ,EAAEjB,IAAI,CAACwG,EAAE,CAACyZ,YAAY,CAAC;QAC3EjgB,IAAI,CAACoY,WAAW,GAAG,IAAI/K,UAAU,CAACrN,IAAI,CAAC;MACzC;IAEF,CAAC;IAEDpD,eAAe,CAACuB,SAAS,CAAC+hB,MAAM,GAAG,kBAAiB;MAClD,IAAIlgB,IAAI,GAAG,IAAI;MAEf,IAAI,CAAEA,IAAI,CAACwG,EAAE,EACX,MAAM/C,KAAK,CAAC,yCAAyC,CAAC;;MAExD;MACA,IAAI0c,WAAW,GAAGngB,IAAI,CAACqU,YAAY;MACnCrU,IAAI,CAACqU,YAAY,GAAG,IAAI;MACxB,IAAI8L,WAAW,EACb,MAAMA,WAAW,CAACthB,IAAI,CAAC,CAAC;;MAE1B;MACA;MACA;MACA,MAAMmB,IAAI,CAAC0f,MAAM,CAACU,KAAK,CAAC,CAAC;IAC3B,CAAC;IAEDxjB,eAAe,CAACuB,SAAS,CAACiiB,KAAK,GAAG,YAAY;MAC5C,OAAO,IAAI,CAACF,MAAM,CAAC,CAAC;IACtB,CAAC;IAEDtjB,eAAe,CAACuB,SAAS,CAACkiB,eAAe,GAAG,UAASF,WAAW,EAAE;MAChE,IAAI,CAAC9L,YAAY,GAAG8L,WAAW;MAC/B,OAAO,IAAI;IACb,CAAC;;IAED;IACAvjB,eAAe,CAACuB,SAAS,CAACmiB,aAAa,GAAG,UAAUnhB,cAAc,EAAE;MAClE,IAAIa,IAAI,GAAG,IAAI;MAEf,IAAI,CAAEA,IAAI,CAACwG,EAAE,EACX,MAAM/C,KAAK,CAAC,iDAAiD,CAAC;MAEhE,OAAOzD,IAAI,CAACwG,EAAE,CAACtH,UAAU,CAACC,cAAc,CAAC;IAC3C,CAAC;IAEDvC,eAAe,CAACuB,SAAS,CAACoiB,2BAA2B,GAAG,gBACtDphB,cAAc,EAAEqhB,QAAQ,EAAEC,YAAY,EAAE;MACxC,IAAIzgB,IAAI,GAAG,IAAI;MAEf,IAAI,CAAEA,IAAI,CAACwG,EAAE,EACX,MAAM/C,KAAK,CAAC,+DAA+D,CAAC;MAG9E,MAAMzD,IAAI,CAACwG,EAAE,CAACka,gBAAgB,CAACvhB,cAAc,EAC3C;QAAEwhB,MAAM,EAAE,IAAI;QAAErL,IAAI,EAAEkL,QAAQ;QAAEI,GAAG,EAAEH;MAAa,CAAC,CAAC;IACxD,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA7jB,eAAe,CAACuB,SAAS,CAAC0iB,gBAAgB,GAAG,YAAY;MACvD,MAAMjR,KAAK,GAAGlR,SAAS,CAACmR,gBAAgB,CAAC,CAAC;MAC1C,IAAID,KAAK,EAAE;QACT,OAAOA,KAAK,CAACE,UAAU,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,OAAO;UAACsB,SAAS,EAAE,SAAAA,CAAA,EAAY,CAAC;QAAC,CAAC;MACpC;IACF,CAAC;;IAED;IACA;IACAxU,eAAe,CAACuB,SAAS,CAAC8W,WAAW,GAAG,UAAUzR,QAAQ,EAAE;MAC1D,OAAO,IAAI,CAACsb,eAAe,CAAC5a,QAAQ,CAACV,QAAQ,CAAC;IAChD,CAAC;IAED5G,eAAe,CAACuB,SAAS,CAAC2iB,WAAW,GAAG,gBAAgBC,eAAe,EAAEC,QAAQ,EAAE;MACjF,MAAMhhB,IAAI,GAAG,IAAI;MAEjB,IAAI+gB,eAAe,KAAK,mCAAmC,EAAE;QAC3D,MAAMrc,CAAC,GAAG,IAAIjB,KAAK,CAAC,cAAc,CAAC;QACnCiB,CAAC,CAACuc,eAAe,GAAG,IAAI;QACxB,MAAMvc,CAAC;MACT;MAEA,IAAI,EAAErF,eAAe,CAAC6hB,cAAc,CAACF,QAAQ,CAAC,IAC5C,CAACrU,KAAK,CAACgQ,aAAa,CAACqE,QAAQ,CAAC,CAAC,EAAE;QACjC,MAAM,IAAIvd,KAAK,CAAC,iDAAiD,CAAC;MACpE;MAEA,IAAIuR,KAAK,GAAGhV,IAAI,CAAC6gB,gBAAgB,CAAC,CAAC;MACnC,IAAIM,OAAO,GAAG,eAAAA,CAAA,EAAkB;QAC9B,MAAMrjB,MAAM,CAACqjB,OAAO,CAAC;UAACjiB,UAAU,EAAE6hB,eAAe;UAAEvhB,EAAE,EAAEwhB,QAAQ,CAACpY;QAAI,CAAC,CAAC;MACxE,CAAC;MACD,OAAO5I,IAAI,CAACsgB,aAAa,CAACS,eAAe,CAAC,CAACK,SAAS,CAClD/C,YAAY,CAAC2C,QAAQ,EAAE5C,0BAA0B,CAAC,EAClD;QACEiD,IAAI,EAAE;MACR,CACF,CAAC,CAACxW,IAAI,CAAC,MAAAwS,KAAA,IAAwB;QAAA,IAAjB;UAACiE;QAAU,CAAC,GAAAjE,KAAA;QACxB,MAAM8D,OAAO,CAAC,CAAC;QACf,MAAMnM,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACvB,OAAOkQ,UAAU;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM7c,CAAC,IAAI;QAClB,MAAMsQ,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACvB,MAAM1M,CAAC;MACT,CAAC,CAAC;IACJ,CAAC;;IAGD;IACA;IACA9H,eAAe,CAACuB,SAAS,CAACqjB,QAAQ,GAAG,gBAAgBriB,cAAc,EAAEI,QAAQ,EAAE;MAC7E,IAAIkiB,UAAU,GAAG;QAACviB,UAAU,EAAEC;MAAc,CAAC;MAC7C;MACA;MACA;MACA;MACA,IAAIC,WAAW,GAAGC,eAAe,CAACC,qBAAqB,CAACC,QAAQ,CAAC;MACjE,IAAIH,WAAW,EAAE;QACf,KAAK,MAAMI,EAAE,IAAIJ,WAAW,EAAE;UAC5B,MAAMtB,MAAM,CAACqjB,OAAO,CAAC1hB,MAAM,CAACC,MAAM,CAAC;YAACF,EAAE,EAAEA;UAAE,CAAC,EAAEiiB,UAAU,CAAC,CAAC;QAC3D;QAAC;MACH,CAAC,MAAM;QACL,MAAM3jB,MAAM,CAACqjB,OAAO,CAACM,UAAU,CAAC;MAClC;IACF,CAAC;IAED7kB,eAAe,CAACuB,SAAS,CAACujB,WAAW,GAAG,gBAAgBX,eAAe,EAAExhB,QAAQ,EAAE;MACjF,IAAIS,IAAI,GAAG,IAAI;MAEf,IAAI+gB,eAAe,KAAK,mCAAmC,EAAE;QAC3D,IAAIrc,CAAC,GAAG,IAAIjB,KAAK,CAAC,cAAc,CAAC;QACjCiB,CAAC,CAACuc,eAAe,GAAG,IAAI;QACxB,MAAMvc,CAAC;MACT;MAEA,IAAIsQ,KAAK,GAAGhV,IAAI,CAAC6gB,gBAAgB,CAAC,CAAC;MACnC,IAAIM,OAAO,GAAG,eAAAA,CAAA,EAAkB;QAC9B,MAAMnhB,IAAI,CAACwhB,QAAQ,CAACT,eAAe,EAAExhB,QAAQ,CAAC;MAChD,CAAC;MAED,OAAOS,IAAI,CAACsgB,aAAa,CAACS,eAAe,CAAC,CACvCY,UAAU,CAACtD,YAAY,CAAC9e,QAAQ,EAAE6e,0BAA0B,CAAC,EAAE;QAC9DiD,IAAI,EAAE;MACR,CAAC,CAAC,CACDxW,IAAI,CAAC,MAAA0S,KAAA,IAA4B;QAAA,IAArB;UAAEqE;QAAa,CAAC,GAAArE,KAAA;QAC3B,MAAM4D,OAAO,CAAC,CAAC;QACf,MAAMnM,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACvB,OAAOkN,eAAe,CAAC;UAAE/G,MAAM,EAAG;YAACsK,aAAa,EAAGD;UAAY;QAAE,CAAC,CAAC,CAACE,cAAc;MACpF,CAAC,CAAC,CAACP,KAAK,CAAC,MAAO1d,GAAG,IAAK;QACtB,MAAMmR,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACvB,MAAMvN,GAAG;MACX,CAAC,CAAC;IACN,CAAC;IAEDjH,eAAe,CAACuB,SAAS,CAAC4jB,mBAAmB,GAAG,gBAAe5iB,cAAc,EAAE;MAC7E,IAAIa,IAAI,GAAG,IAAI;MAGf,IAAIgV,KAAK,GAAGhV,IAAI,CAAC6gB,gBAAgB,CAAC,CAAC;MACnC,IAAIM,OAAO,GAAG,SAAAA,CAAA,EAAW;QACvB,OAAOrjB,MAAM,CAACqjB,OAAO,CAAC;UACpBjiB,UAAU,EAAEC,cAAc;UAC1BK,EAAE,EAAE,IAAI;UACRG,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC;MAED,OAAOK,IAAI,CACRsgB,aAAa,CAACnhB,cAAc,CAAC,CAC7BkK,IAAI,CAAC,CAAC,CACNwB,IAAI,CAAC,MAAM0M,MAAM,IAAI;QACpB,MAAM4J,OAAO,CAAC,CAAC;QACf,MAAMnM,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACvB,OAAOmG,MAAM;MACf,CAAC,CAAC,CACDgK,KAAK,CAAC,MAAM7c,CAAC,IAAI;QAChB,MAAMsQ,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACvB,MAAM1M,CAAC;MACT,CAAC,CAAC;IACN,CAAC;;IAED;IACA;IACA9H,eAAe,CAACuB,SAAS,CAAC6jB,iBAAiB,GAAG,kBAAkB;MAC9D,IAAIhiB,IAAI,GAAG,IAAI;MAEf,IAAIgV,KAAK,GAAGhV,IAAI,CAAC6gB,gBAAgB,CAAC,CAAC;MACnC,IAAIM,OAAO,GAAG,eAAAA,CAAA,EAAkB;QAC9B,MAAMrjB,MAAM,CAACqjB,OAAO,CAAC;UAAEvhB,YAAY,EAAE;QAAK,CAAC,CAAC;MAC9C,CAAC;MAED,IAAI;QACF,MAAMI,IAAI,CAACwG,EAAE,CAACyb,aAAa,CAAC,CAAC;QAC7B,MAAMd,OAAO,CAAC,CAAC;QACf,MAAMnM,KAAK,CAAC5D,SAAS,CAAC,CAAC;MACzB,CAAC,CAAC,OAAO1M,CAAC,EAAE;QACV,MAAMsQ,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACvB,MAAM1M,CAAC;MACT;IACF,CAAC;IAED9H,eAAe,CAACuB,SAAS,CAAC+jB,WAAW,GAAG,gBAAgBnB,eAAe,EAAExhB,QAAQ,EAAE4iB,GAAG,EAAErW,OAAO,EAAE;MAC/F,IAAI9L,IAAI,GAAG,IAAI;MAEf,IAAI+gB,eAAe,KAAK,mCAAmC,EAAE;QAC3D,IAAIrc,CAAC,GAAG,IAAIjB,KAAK,CAAC,cAAc,CAAC;QACjCiB,CAAC,CAACuc,eAAe,GAAG,IAAI;QACxB,MAAMvc,CAAC;MACT;;MAEA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACyd,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QACnC,MAAM5c,KAAK,GAAG,IAAI9B,KAAK,CAAC,+CAA+C,CAAC;QAExE,MAAM8B,KAAK;MACb;MAEA,IAAI,EAAElG,eAAe,CAAC6hB,cAAc,CAACiB,GAAG,CAAC,IAAI,CAACxV,KAAK,CAACgQ,aAAa,CAACwF,GAAG,CAAC,CAAC,EAAE;QACvE,MAAM5c,KAAK,GAAG,IAAI9B,KAAK,CACrB,+CAA+C,GAC/C,uBAAuB,CAAC;QAE1B,MAAM8B,KAAK;MACb;MAEA,IAAI,CAACuG,OAAO,EAAEA,OAAO,GAAG,CAAC,CAAC;MAE1B,IAAIkJ,KAAK,GAAGhV,IAAI,CAAC6gB,gBAAgB,CAAC,CAAC;MACnC,IAAIM,OAAO,GAAG,eAAAA,CAAA,EAAkB;QAC9B,MAAMnhB,IAAI,CAACwhB,QAAQ,CAACT,eAAe,EAAExhB,QAAQ,CAAC;MAChD,CAAC;MAED,IAAIL,UAAU,GAAGc,IAAI,CAACsgB,aAAa,CAACS,eAAe,CAAC;MACpD,IAAIqB,SAAS,GAAG;QAACf,IAAI,EAAE;MAAI,CAAC;MAC5B;MACA,IAAIvV,OAAO,CAACuW,YAAY,KAAK7X,SAAS,EAAE4X,SAAS,CAACC,YAAY,GAAGvW,OAAO,CAACuW,YAAY;MACrF;MACA,IAAIvW,OAAO,CAACwW,MAAM,EAAEF,SAAS,CAACE,MAAM,GAAG,IAAI;MAC3C,IAAIxW,OAAO,CAACyW,KAAK,EAAEH,SAAS,CAACG,KAAK,GAAG,IAAI;MACzC;MACA;MACA;MACA,IAAIzW,OAAO,CAAC0W,UAAU,EAAEJ,SAAS,CAACI,UAAU,GAAG,IAAI;MAEnD,IAAIC,aAAa,GAAGpE,YAAY,CAAC9e,QAAQ,EAAE6e,0BAA0B,CAAC;MACtE,IAAIsE,QAAQ,GAAGrE,YAAY,CAAC8D,GAAG,EAAE/D,0BAA0B,CAAC;MAE5D,IAAIuE,QAAQ,GAAGtjB,eAAe,CAACujB,kBAAkB,CAACF,QAAQ,CAAC;MAE3D,IAAI5W,OAAO,CAAC+W,cAAc,IAAI,CAACF,QAAQ,EAAE;QACvC,IAAI9e,GAAG,GAAG,IAAIJ,KAAK,CAAC,+CAA+C,CAAC;QACpE,MAAMI,GAAG;MACX;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA,IAAIif,OAAO;MACX,IAAIhX,OAAO,CAACwW,MAAM,EAAE;QAClB,IAAI;UACF,IAAIpM,MAAM,GAAG7W,eAAe,CAAC0jB,qBAAqB,CAACxjB,QAAQ,EAAE4iB,GAAG,CAAC;UACjEW,OAAO,GAAG5M,MAAM,CAACtN,GAAG;QACtB,CAAC,CAAC,OAAO/E,GAAG,EAAE;UACZ,MAAMA,GAAG;QACX;MACF;MACA,IAAIiI,OAAO,CAACwW,MAAM,IAChB,CAAEK,QAAQ,IACV,CAAEG,OAAO,IACThX,OAAO,CAACwV,UAAU,IAClB,EAAGxV,OAAO,CAACwV,UAAU,YAAY7E,KAAK,CAACC,QAAQ,IAC7C5Q,OAAO,CAACkX,WAAW,CAAC,EAAE;QACxB;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA,OAAO,MAAMC,4BAA4B,CAAC/jB,UAAU,EAAEujB,aAAa,EAAEC,QAAQ,EAAE5W,OAAO,CAAC,CACpFjB,IAAI,CAAC,MAAM0M,MAAM,IAAI;UACpB,MAAM4J,OAAO,CAAC,CAAC;UACf,MAAMnM,KAAK,CAAC5D,SAAS,CAAC,CAAC;UACvB,IAAImG,MAAM,IAAI,CAAEzL,OAAO,CAACoX,aAAa,EAAE;YACrC,OAAO3L,MAAM,CAACuK,cAAc;UAC9B,CAAC,MAAM;YACL,OAAOvK,MAAM;UACf;QACF,CAAC,CAAC;MACN,CAAC,MAAM;QACL,IAAIzL,OAAO,CAACwW,MAAM,IAAI,CAACQ,OAAO,IAAIhX,OAAO,CAACwV,UAAU,IAAIqB,QAAQ,EAAE;UAChE,IAAI,CAACD,QAAQ,CAACS,cAAc,CAAC,cAAc,CAAC,EAAE;YAC5CT,QAAQ,CAACU,YAAY,GAAG,CAAC,CAAC;UAC5B;UACAN,OAAO,GAAGhX,OAAO,CAACwV,UAAU;UAC5B7hB,MAAM,CAACC,MAAM,CAACgjB,QAAQ,CAACU,YAAY,EAAE/E,YAAY,CAAC;YAACzV,GAAG,EAAEkD,OAAO,CAACwV;UAAU,CAAC,EAAElD,0BAA0B,CAAC,CAAC;QAC3G;QAEA,MAAMiF,OAAO,GAAG5jB,MAAM,CAAC+M,IAAI,CAACkW,QAAQ,CAAC,CAAClK,MAAM,CAAEvZ,GAAG,IAAK,CAACA,GAAG,CAACkK,UAAU,CAAC,GAAG,CAAC,CAAC;QAC3E,IAAIma,YAAY,GAAGD,OAAO,CAACre,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,YAAY;QACnEse,YAAY,GACVA,YAAY,KAAK,YAAY,IAAI,CAAClB,SAAS,CAACG,KAAK,GAC7C,WAAW,GACXe,YAAY;QAClB,OAAOpkB,UAAU,CAACokB,YAAY,CAAC,CAC5B/T,IAAI,CAACrQ,UAAU,CAAC,CAACujB,aAAa,EAAEC,QAAQ,EAAEN,SAAS,CAAC,CACpDvX,IAAI,CAAC,MAAM0M,MAAM,IAAI;UACpB,IAAIgM,YAAY,GAAGjF,eAAe,CAAC;YAAC/G;UAAM,CAAC,CAAC;UAC5C,IAAIgM,YAAY,IAAIzX,OAAO,CAACoX,aAAa,EAAE;YACzC;YACA;YACA;YACA,IAAIpX,OAAO,CAACwW,MAAM,IAAIiB,YAAY,CAACjC,UAAU,EAAE;cAC7C,IAAIwB,OAAO,EAAE;gBACXS,YAAY,CAACjC,UAAU,GAAGwB,OAAO;cACnC,CAAC,MAAM,IAAIS,YAAY,CAACjC,UAAU,YAAYxkB,OAAO,CAAC0mB,QAAQ,EAAE;gBAC9DD,YAAY,CAACjC,UAAU,GAAG,IAAI7E,KAAK,CAACC,QAAQ,CAAC6G,YAAY,CAACjC,UAAU,CAACmC,WAAW,CAAC,CAAC,CAAC;cACrF;YACF;YACA,MAAMtC,OAAO,CAAC,CAAC;YACf,MAAMnM,KAAK,CAAC5D,SAAS,CAAC,CAAC;YACvB,OAAOmS,YAAY;UACrB,CAAC,MAAM;YACL,MAAMpC,OAAO,CAAC,CAAC;YACf,MAAMnM,KAAK,CAAC5D,SAAS,CAAC,CAAC;YACvB,OAAOmS,YAAY,CAACzB,cAAc;UACpC;QACF,CAAC,CAAC,CAACP,KAAK,CAAC,MAAO1d,GAAG,IAAK;UACtB,MAAMmR,KAAK,CAAC5D,SAAS,CAAC,CAAC;UACvB,MAAMvN,GAAG;QACX,CAAC,CAAC;MACN;IACF,CAAC;;IAED;IACAjH,eAAe,CAAC8mB,sBAAsB,GAAG,UAAU7f,GAAG,EAAE;MAEtD;MACA;MACA;MACA;MACA,IAAI0B,KAAK,GAAG1B,GAAG,CAAC8f,MAAM,IAAI9f,GAAG,CAACA,GAAG;;MAEjC;MACA;MACA;MACA,IAAI0B,KAAK,CAACqe,OAAO,CAAC,iCAAiC,CAAC,KAAK,CAAC,IACrDre,KAAK,CAACqe,OAAO,CAAC,mEAAmE,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9F,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC;;IAED;IACA;IACA;IACAhnB,eAAe,CAACuB,SAAS,CAAC0lB,WAAW,GAAG,gBAAgB1kB,cAAc,EAAEI,QAAQ,EAAE4iB,GAAG,EAAErW,OAAO,EAAE;MAC9F,IAAI9L,IAAI,GAAG,IAAI;MAIf,IAAI,OAAO8L,OAAO,KAAK,UAAU,IAAI,CAAEtI,QAAQ,EAAE;QAC/CA,QAAQ,GAAGsI,OAAO;QAClBA,OAAO,GAAG,CAAC,CAAC;MACd;MAEA,OAAO9L,IAAI,CAACkiB,WAAW,CAAC/iB,cAAc,EAAEI,QAAQ,EAAE4iB,GAAG,EACnD1iB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoM,OAAO,EAAE;QACzBwW,MAAM,EAAE,IAAI;QACZY,aAAa,EAAE;MACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAEDtmB,eAAe,CAACuB,SAAS,CAAC2lB,IAAI,GAAG,UAAU3kB,cAAc,EAAEI,QAAQ,EAAEuM,OAAO,EAAE;MAC5E,IAAI9L,IAAI,GAAG,IAAI;MAEf,IAAIkL,SAAS,CAAClG,MAAM,KAAK,CAAC,EACxBzF,QAAQ,GAAG,CAAC,CAAC;MAEf,OAAO,IAAIkS,MAAM,CACfzR,IAAI,EAAE,IAAIO,iBAAiB,CAACpB,cAAc,EAAEI,QAAQ,EAAEuM,OAAO,CAAC,CAAC;IACnE,CAAC;IAEDlP,eAAe,CAACuB,SAAS,CAACkG,YAAY,GAAG,gBAAgB0c,eAAe,EAAExhB,QAAQ,EAAEuM,OAAO,EAAE;MAC3F,IAAI9L,IAAI,GAAG,IAAI;MACf,IAAIkL,SAAS,CAAClG,MAAM,KAAK,CAAC,EAAE;QAC1BzF,QAAQ,GAAG,CAAC,CAAC;MACf;MAEAuM,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;MACvBA,OAAO,CAACwG,KAAK,GAAG,CAAC;MAEjB,MAAM+F,OAAO,GAAG,MAAMrY,IAAI,CAAC8jB,IAAI,CAAC/C,eAAe,EAAExhB,QAAQ,EAAEuM,OAAO,CAAC,CAAC4B,KAAK,CAAC,CAAC;MAE3E,OAAO2K,OAAO,CAAC,CAAC,CAAC;IACnB,CAAC;;IAED;IACA;IACAzb,eAAe,CAACuB,SAAS,CAAC4lB,gBAAgB,GAAG,gBAAgB5kB,cAAc,EAAE6kB,KAAK,EACrBlY,OAAO,EAAE;MACpE,IAAI9L,IAAI,GAAG,IAAI;;MAEf;MACA;MACA,IAAId,UAAU,GAAGc,IAAI,CAACsgB,aAAa,CAACnhB,cAAc,CAAC;MACnD,MAAMD,UAAU,CAAC+kB,WAAW,CAACD,KAAK,EAAElY,OAAO,CAAC;IAC9C,CAAC;;IAED;IACAlP,eAAe,CAACuB,SAAS,CAAC8lB,WAAW,GACnCrnB,eAAe,CAACuB,SAAS,CAAC4lB,gBAAgB;IAE5CnnB,eAAe,CAACuB,SAAS,CAAC+lB,cAAc,GAAG,UAAU/kB,cAAc,EAAW;MAAA,SAAA8L,IAAA,GAAAC,SAAA,CAAAlG,MAAA,EAANmG,IAAI,OAAAC,KAAA,CAAAH,IAAA,OAAAA,IAAA,WAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAH,SAAA,CAAAG,IAAA;MAAA;MAC1EF,IAAI,GAAGA,IAAI,CAAC1D,GAAG,CAAC0c,GAAG,IAAI9F,YAAY,CAAC8F,GAAG,EAAE/F,0BAA0B,CAAC,CAAC;MACrE,MAAMlf,UAAU,GAAG,IAAI,CAACohB,aAAa,CAACnhB,cAAc,CAAC;MACrD,OAAOD,UAAU,CAACglB,cAAc,CAAC,GAAG/Y,IAAI,CAAC;IAC3C,CAAC;IAEDvO,eAAe,CAACuB,SAAS,CAACimB,sBAAsB,GAAG,UAAUjlB,cAAc,EAAW;MAAA,SAAAklB,KAAA,GAAAnZ,SAAA,CAAAlG,MAAA,EAANmG,IAAI,OAAAC,KAAA,CAAAiZ,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJnZ,IAAI,CAAAmZ,KAAA,QAAApZ,SAAA,CAAAoZ,KAAA;MAAA;MAClFnZ,IAAI,GAAGA,IAAI,CAAC1D,GAAG,CAAC0c,GAAG,IAAI9F,YAAY,CAAC8F,GAAG,EAAE/F,0BAA0B,CAAC,CAAC;MACrE,MAAMlf,UAAU,GAAG,IAAI,CAACohB,aAAa,CAACnhB,cAAc,CAAC;MACrD,OAAOD,UAAU,CAACklB,sBAAsB,CAAC,GAAGjZ,IAAI,CAAC;IACnD,CAAC;IAEDvO,eAAe,CAACuB,SAAS,CAAComB,gBAAgB,GAAG3nB,eAAe,CAACuB,SAAS,CAAC4lB,gBAAgB;IAEvFnnB,eAAe,CAACuB,SAAS,CAACqmB,cAAc,GAAG,gBAAgBrlB,cAAc,EAAE6kB,KAAK,EAAE;MAChF,IAAIhkB,IAAI,GAAG,IAAI;;MAGf;MACA;MACA,IAAId,UAAU,GAAGc,IAAI,CAACsgB,aAAa,CAACnhB,cAAc,CAAC;MACnD,IAAIslB,SAAS,GAAI,MAAMvlB,UAAU,CAACwlB,SAAS,CAACV,KAAK,CAAC;IACpD,CAAC;IAGDhG,mBAAmB,CAAClf,OAAO,CAAC,UAAU6lB,CAAC,EAAE;MACvC/nB,eAAe,CAACuB,SAAS,CAACwmB,CAAC,CAAC,GAAG,YAAY;QACzC,MAAM,IAAIlhB,KAAK,IAAAkE,MAAA,CACVgd,CAAC,qDAAAhd,MAAA,CAAkDsW,kBAAkB,CACtE0G,CACF,CAAC,gBACH,CAAC;MACH,CAAC;IACH,CAAC,CAAC;IAGF,IAAIC,oBAAoB,GAAG,CAAC;IAI5B,IAAI3B,4BAA4B,GAAG,eAAAA,CAAgB/jB,UAAU,EAAEK,QAAQ,EAAE4iB,GAAG,EAAErW,OAAO,EAAE;MACrF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAIwV,UAAU,GAAGxV,OAAO,CAACwV,UAAU,CAAC,CAAC;MACrC,IAAIuD,kBAAkB,GAAG;QACvBxD,IAAI,EAAE,IAAI;QACVkB,KAAK,EAAEzW,OAAO,CAACyW;MACjB,CAAC;MACD,IAAIuC,kBAAkB,GAAG;QACvBzD,IAAI,EAAE,IAAI;QACViB,MAAM,EAAE;MACV,CAAC;MAED,IAAIyC,iBAAiB,GAAGtlB,MAAM,CAACC,MAAM,CACnC2e,YAAY,CAAC;QAACzV,GAAG,EAAE0Y;MAAU,CAAC,EAAElD,0BAA0B,CAAC,EAC3D+D,GAAG,CAAC;MAEN,IAAI6C,KAAK,GAAGJ,oBAAoB;MAEhC,IAAIK,QAAQ,GAAG,eAAAA,CAAA,EAAkB;QAC/BD,KAAK,EAAE;QACP,IAAI,CAAEA,KAAK,EAAE;UACX,MAAM,IAAIvhB,KAAK,CAAC,sBAAsB,GAAGmhB,oBAAoB,GAAG,SAAS,CAAC;QAC5E,CAAC,MAAM;UACL,IAAIM,MAAM,GAAGhmB,UAAU,CAACimB,UAAU;UAClC,IAAG,CAAC1lB,MAAM,CAAC+M,IAAI,CAAC2V,GAAG,CAAC,CAACiD,IAAI,CAACnmB,GAAG,IAAIA,GAAG,CAACkK,UAAU,CAAC,GAAG,CAAC,CAAC,EAAC;YACpD+b,MAAM,GAAGhmB,UAAU,CAACmmB,UAAU,CAAC9V,IAAI,CAACrQ,UAAU,CAAC;UACjD;UACA,OAAOgmB,MAAM,CACX3lB,QAAQ,EACR4iB,GAAG,EACH0C,kBAAkB,CAAC,CAACha,IAAI,CAAC0M,MAAM,IAAI;YACnC,IAAIA,MAAM,KAAKA,MAAM,CAACsK,aAAa,IAAItK,MAAM,CAAC+N,aAAa,CAAC,EAAE;cAC5D,OAAO;gBACLxD,cAAc,EAAEvK,MAAM,CAACsK,aAAa,IAAItK,MAAM,CAAC+N,aAAa;gBAC5DhE,UAAU,EAAE/J,MAAM,CAACgO,UAAU,IAAI/a;cACnC,CAAC;YACH,CAAC,MAAM;cACL,OAAOgb,mBAAmB,CAAC,CAAC;YAC9B;UACF,CAAC,CAAC;QACJ;MACF,CAAC;MAED,IAAIA,mBAAmB,GAAG,SAAAA,CAAA,EAAW;QACnC,OAAOtmB,UAAU,CAACmmB,UAAU,CAAC9lB,QAAQ,EAAEwlB,iBAAiB,EAAED,kBAAkB,CAAC,CAC1Eja,IAAI,CAAC0M,MAAM,KAAK;UACfuK,cAAc,EAAEvK,MAAM,CAAC+N,aAAa;UACpChE,UAAU,EAAE/J,MAAM,CAACgO;QACrB,CAAC,CAAC,CAAC,CAAChE,KAAK,CAAC1d,GAAG,IAAI;UACf,IAAIjH,eAAe,CAAC8mB,sBAAsB,CAAC7f,GAAG,CAAC,EAAE;YAC/C,OAAOohB,QAAQ,CAAC,CAAC;UACnB,CAAC,MAAM;YACL,MAAMphB,GAAG;UACX;QACF,CAAC,CAAC;MAEN,CAAC;MACD,OAAOohB,QAAQ,CAAC,CAAC;IACnB,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAroB,eAAe,CAACuB,SAAS,CAACsnB,uBAAuB,GAAG,UAClDpnB,iBAAiB,EAAEyL,OAAO,EAAEgE,SAAS,EAAE;MACvC,IAAI9N,IAAI,GAAG,IAAI;;MAEf;MACA;MACA,IAAK8J,OAAO,IAAI,CAACgE,SAAS,CAAC4X,WAAW,IACnC,CAAC5b,OAAO,IAAI,CAACgE,SAAS,CAACuH,KAAM,EAAE;QAChC,MAAM,IAAI5R,KAAK,CAAC,mBAAmB,IAAIqG,OAAO,GAAG,SAAS,GAAG,WAAW,CAAC,GACrE,6BAA6B,IAC5BA,OAAO,GAAG,aAAa,GAAG,OAAO,CAAC,GAAG,WAAW,CAAC;MACxD;MAEA,OAAO9J,IAAI,CAAC8H,IAAI,CAACzJ,iBAAiB,EAAE,UAAU0J,GAAG,EAAE;QACjD,IAAIvI,EAAE,GAAGuI,GAAG,CAACa,GAAG;QAChB,OAAOb,GAAG,CAACa,GAAG;QACd;QACA,OAAOb,GAAG,CAACxD,EAAE;QACb,IAAIuF,OAAO,EAAE;UACXgE,SAAS,CAAC4X,WAAW,CAAClmB,EAAE,EAAEuI,GAAG,EAAE,IAAI,CAAC;QACtC,CAAC,MAAM;UACL+F,SAAS,CAACuH,KAAK,CAAC7V,EAAE,EAAEuI,GAAG,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC;IAEDnL,eAAe,CAACuB,SAAS,CAACkR,yBAAyB,GAAG,UACpDhR,iBAAiB,EAAgB;MAAA,IAAdyN,OAAO,GAAAZ,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,CAAC,CAAC;MAC/B,IAAIlL,IAAI,GAAG,IAAI;MACf,MAAM;QAAE2lB,gBAAgB;QAAEC;MAAa,CAAC,GAAG9Z,OAAO;MAClDA,OAAO,GAAG;QAAE6Z,gBAAgB;QAAEC;MAAa,CAAC;MAE5C,IAAI1mB,UAAU,GAAGc,IAAI,CAACsgB,aAAa,CAACjiB,iBAAiB,CAACc,cAAc,CAAC;MACrE,IAAI0mB,aAAa,GAAGxnB,iBAAiB,CAACyN,OAAO;MAC7C,IAAImT,YAAY,GAAG;QACjBza,IAAI,EAAEqhB,aAAa,CAACrhB,IAAI;QACxB8N,KAAK,EAAEuT,aAAa,CAACvT,KAAK;QAC1BgJ,IAAI,EAAEuK,aAAa,CAACvK,IAAI;QACxBhX,UAAU,EAAEuhB,aAAa,CAAC5Y,MAAM,IAAI4Y,aAAa,CAACvhB,UAAU;QAC5DwhB,cAAc,EAAED,aAAa,CAACC;MAChC,CAAC;;MAED;MACA,IAAID,aAAa,CAAChe,QAAQ,EAAE;QAC1BoX,YAAY,CAAC8G,eAAe,GAAG,CAAC,CAAC;MACnC;MAEA,IAAIC,QAAQ,GAAG9mB,UAAU,CAAC4kB,IAAI,CAC5BzF,YAAY,CAAChgB,iBAAiB,CAACkB,QAAQ,EAAE6e,0BAA0B,CAAC,EACpEa,YAAY,CAAC;;MAEf;MACA,IAAI4G,aAAa,CAAChe,QAAQ,EAAE;QAC1B;QACAme,QAAQ,CAACC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC;QACxC;QACA;QACAD,QAAQ,CAACC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC;;QAEzC;QACA;QACA;QACA;QACA;QACA,IAAI5nB,iBAAiB,CAACc,cAAc,KAAKiB,gBAAgB,IACvD/B,iBAAiB,CAACkB,QAAQ,CAACgF,EAAE,EAAE;UAC/ByhB,QAAQ,CAACC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC;QAC7C;MACF;MAEA,IAAI,OAAOJ,aAAa,CAACK,SAAS,KAAK,WAAW,EAAE;QAClDF,QAAQ,GAAGA,QAAQ,CAACG,SAAS,CAACN,aAAa,CAACK,SAAS,CAAC;MACxD;MACA,IAAI,OAAOL,aAAa,CAACO,IAAI,KAAK,WAAW,EAAE;QAC7CJ,QAAQ,GAAGA,QAAQ,CAACI,IAAI,CAACP,aAAa,CAACO,IAAI,CAAC;MAC9C;MAEA,OAAO,IAAIjI,kBAAkB,CAAC6H,QAAQ,EAAE3nB,iBAAiB,EAAEyN,OAAO,EAAE5M,UAAU,CAAC;IACjF,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACAtC,eAAe,CAACuB,SAAS,CAAC2J,IAAI,GAAG,UAAUzJ,iBAAiB,EAAEgoB,WAAW,EAAEC,SAAS,EAAE;MACpF,IAAItmB,IAAI,GAAG,IAAI;MACf,IAAI,CAAC3B,iBAAiB,CAACyN,OAAO,CAACjE,QAAQ,EACrC,MAAM,IAAIpE,KAAK,CAAC,iCAAiC,CAAC;MAEpD,IAAIkW,MAAM,GAAG3Z,IAAI,CAACqP,yBAAyB,CAAChR,iBAAiB,CAAC;MAE9D,IAAIkoB,OAAO,GAAG,KAAK;MACnB,IAAIC,MAAM;MAEV1oB,MAAM,CAACia,KAAK,CAAC,eAAe0O,IAAIA,CAAA,EAAG;QACjC,IAAI1e,GAAG,GAAG,IAAI;QACd,OAAO,IAAI,EAAE;UACX,IAAIwe,OAAO,EACT;UACF,IAAI;YACFxe,GAAG,GAAG,MAAM4R,MAAM,CAAC+M,6BAA6B,CAACJ,SAAS,CAAC;UAC7D,CAAC,CAAC,OAAOziB,GAAG,EAAE;YACZ;YACAyB,OAAO,CAACC,KAAK,CAAC1B,GAAG,CAAC;YAClB;YACA;YACA;YACA;YACAkE,GAAG,GAAG,IAAI;UACZ;UACA;UACA;UACA,IAAIwe,OAAO,EACT;UACF,IAAIxe,GAAG,EAAE;YACP;YACA;YACA;YACA;YACAye,MAAM,GAAGze,GAAG,CAACxD,EAAE;YACf8hB,WAAW,CAACte,GAAG,CAAC;UAClB,CAAC,MAAM;YACL,IAAI4e,WAAW,GAAGlnB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,iBAAiB,CAACkB,QAAQ,CAAC;YAC/D,IAAIinB,MAAM,EAAE;cACVG,WAAW,CAACpiB,EAAE,GAAG;gBAACwC,GAAG,EAAEyf;cAAM,CAAC;YAChC;YACA7M,MAAM,GAAG3Z,IAAI,CAACqP,yBAAyB,CAAC,IAAI9O,iBAAiB,CAC3DlC,iBAAiB,CAACc,cAAc,EAChCwnB,WAAW,EACXtoB,iBAAiB,CAACyN,OAAO,CAAC,CAAC;YAC7B;YACA;YACA;YACAzG,UAAU,CAACohB,IAAI,EAAE,GAAG,CAAC;YACrB;UACF;QACF;MACF,CAAC,CAAC;MAEF,OAAO;QACL5nB,IAAI,EAAE,SAAAA,CAAA,EAAY;UAChB0nB,OAAO,GAAG,IAAI;UACd5M,MAAM,CAACyG,KAAK,CAAC,CAAC;QAChB;MACF,CAAC;IACH,CAAC;IAED3gB,MAAM,CAACC,MAAM,CAAC9C,eAAe,CAACuB,SAAS,EAAE;MACvCyoB,eAAe,EAAE,eAAAA,CACfvoB,iBAAiB,EAAEyL,OAAO,EAAEgE,SAAS,EAAEpB,oBAAoB,EAAE;QAAA,IAAAma,kBAAA;QAC7D,IAAI7mB,IAAI,GAAG,IAAI;QACf,MAAMb,cAAc,GAAGd,iBAAiB,CAACc,cAAc;QAEvD,IAAId,iBAAiB,CAACyN,OAAO,CAACjE,QAAQ,EAAE;UACtC,OAAO7H,IAAI,CAACylB,uBAAuB,CAACpnB,iBAAiB,EAAEyL,OAAO,EAAEgE,SAAS,CAAC;QAC5E;;QAEA;QACA;QACA,MAAMgZ,aAAa,GAAGzoB,iBAAiB,CAACyN,OAAO,CAACxH,UAAU,IAAIjG,iBAAiB,CAACyN,OAAO,CAACmB,MAAM;QAC9F,IAAI6Z,aAAa,KACdA,aAAa,CAACle,GAAG,KAAK,CAAC,IACtBke,aAAa,CAACle,GAAG,KAAK,KAAK,CAAC,EAAE;UAChC,MAAMnF,KAAK,CAAC,sDAAsD,CAAC;QACrE;QAEA,IAAIsjB,UAAU,GAAGpa,KAAK,CAAC9H,SAAS,CAC9BpF,MAAM,CAACC,MAAM,CAAC;UAACoK,OAAO,EAAEA;QAAO,CAAC,EAAEzL,iBAAiB,CAAC,CAAC;QAEvD,IAAI+Q,WAAW,EAAE4X,aAAa;QAC9B,IAAIC,WAAW,GAAG,KAAK;;QAEvB;QACA;QACA;QACA,IAAIF,UAAU,IAAI/mB,IAAI,CAAC6e,oBAAoB,EAAE;UAC3CzP,WAAW,GAAGpP,IAAI,CAAC6e,oBAAoB,CAACkI,UAAU,CAAC;QACrD,CAAC,MAAM;UACLE,WAAW,GAAG,IAAI;UAClB;UACA7X,WAAW,GAAG,IAAIzF,kBAAkB,CAAC;YACnCG,OAAO,EAAEA,OAAO;YAChBC,MAAM,EAAE,SAAAA,CAAA,EAAY;cAClB,OAAO/J,IAAI,CAAC6e,oBAAoB,CAACkI,UAAU,CAAC;cAC5C,OAAOC,aAAa,CAACnoB,IAAI,CAAC,CAAC;YAC7B;UACF,CAAC,CAAC;QACJ;QAEA,IAAIqoB,aAAa,GAAG,IAAI3I,aAAa,CAACnP,WAAW,EAC/CtB,SAAS,EACTpB,oBACF,CAAC;QAED,MAAMya,YAAY,GAAG,CAAAnnB,IAAI,aAAJA,IAAI,wBAAA6mB,kBAAA,GAAJ7mB,IAAI,CAAEqU,YAAY,cAAAwS,kBAAA,uBAAlBA,kBAAA,CAAoBtlB,aAAa,KAAI,CAAC,CAAC;QAC5D,MAAM;UAAEyF,kBAAkB;UAAEK;QAAmB,CAAC,GAAG8f,YAAY;QAC/D,IAAIF,WAAW,EAAE;UACf,IAAIvT,OAAO,EAAEvB,MAAM;UACnB,IAAIiV,WAAW,GAAG,CAChB,YAAY;YACV;YACA;YACA;YACA,OAAOpnB,IAAI,CAACqU,YAAY,IAAI,CAACvK,OAAO,IAClC,CAACgE,SAAS,CAACoB,qBAAqB;UACpC,CAAC,EACD,YAAY;YACV;YACA;YACA,IAAI7H,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAErC,MAAM,IAAIqC,kBAAkB,CAACggB,QAAQ,CAACloB,cAAc,CAAC,EAAE;cAC7E,IAAI,CAACwf,uBAAuB,CAAC0I,QAAQ,CAACloB,cAAc,CAAC,EAAE;gBACrDmG,OAAO,CAACgiB,IAAI,mFAAA3f,MAAA,CAAmFxI,cAAc,sDAAmD,CAAC;gBACjKwf,uBAAuB,CAAClgB,IAAI,CAACU,cAAc,CAAC,CAAC,CAAC;cAChD;cACA,OAAO,KAAK;YACd;YACA,IAAI6H,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEhC,MAAM,IAAI,CAACgC,kBAAkB,CAACqgB,QAAQ,CAACloB,cAAc,CAAC,EAAE;cAC9E,IAAI,CAACwf,uBAAuB,CAAC0I,QAAQ,CAACloB,cAAc,CAAC,EAAE;gBACrDmG,OAAO,CAACgiB,IAAI,2FAAA3f,MAAA,CAA2FxI,cAAc,sDAAmD,CAAC;gBACzKwf,uBAAuB,CAAClgB,IAAI,CAACU,cAAc,CAAC,CAAC,CAAC;cAChD;cACA,OAAO,KAAK;YACd;YACA,OAAO,IAAI;UACb,CAAC,EACD,YAAY;YACV;YACA;YACA,IAAI;cACFuU,OAAO,GAAG,IAAI6T,SAAS,CAACC,OAAO,CAACnpB,iBAAiB,CAACkB,QAAQ,CAAC;cAC3D,OAAO,IAAI;YACb,CAAC,CAAC,OAAOmF,CAAC,EAAE;cACV;cACA;cACA,OAAO,KAAK;YACd;UACF,CAAC,EACD,YAAY;YACV;YACA,OAAO7H,kBAAkB,CAACse,eAAe,CAAC9c,iBAAiB,EAAEqV,OAAO,CAAC;UACvE,CAAC,EACD,YAAY;YACV;YACA;YACA,IAAI,CAACrV,iBAAiB,CAACyN,OAAO,CAACtH,IAAI,EACjC,OAAO,IAAI;YACb,IAAI;cACF2N,MAAM,GAAG,IAAIoV,SAAS,CAACE,MAAM,CAACppB,iBAAiB,CAACyN,OAAO,CAACtH,IAAI,CAAC;cAC7D,OAAO,IAAI;YACb,CAAC,CAAC,OAAOE,CAAC,EAAE;cACV;cACA;cACA,OAAO,KAAK;YACd;UACF,CAAC,CACF,CAACkX,KAAK,CAAC5J,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;;UAEpB,IAAI0V,WAAW,GAAGN,WAAW,GAAGvqB,kBAAkB,GAAGoR,oBAAoB;UACzE+Y,aAAa,GAAG,IAAIU,WAAW,CAAC;YAC9BrpB,iBAAiB,EAAEA,iBAAiB;YACpC8Q,WAAW,EAAEnP,IAAI;YACjBoP,WAAW,EAAEA,WAAW;YACxBtF,OAAO,EAAEA,OAAO;YAChB4J,OAAO,EAAEA,OAAO;YAAG;YACnBvB,MAAM,EAAEA,MAAM;YAAG;YACjBjD,qBAAqB,EAAEpB,SAAS,CAACoB;UACnC,CAAC,CAAC;UAEF,IAAI8X,aAAa,CAACvX,KAAK,EAAE;YACvB,MAAMuX,aAAa,CAACvX,KAAK,CAAC,CAAC;UAC7B;;UAEA;UACAL,WAAW,CAACuY,cAAc,GAAGX,aAAa;QAC5C;QACAhnB,IAAI,CAAC6e,oBAAoB,CAACkI,UAAU,CAAC,GAAG3X,WAAW;QACnD;QACA,MAAMA,WAAW,CAAC7D,2BAA2B,CAAC2b,aAAa,CAAC;QAE5D,OAAOA,aAAa;MACtB;IAEF,CAAC,CAAC;IAACpnB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICx6BH1C,MAAM,CAACjB,MAAM,CAAC;MAACQ,OAAO,EAACA,CAAA,KAAIA,OAAO;MAAC8qB,aAAa,EAACA,CAAA,KAAIA,aAAa;MAACtJ,eAAe,EAACA,CAAA,KAAIA,eAAe;MAACF,0BAA0B,EAACA,CAAA,KAAIA,0BAA0B;MAACC,YAAY,EAACA,CAAA,KAAIA,YAAY;MAACwJ,0BAA0B,EAACA,CAAA,KAAIA,0BAA0B;MAACC,YAAY,EAACA,CAAA,KAAIA;IAAY,CAAC,CAAC;IAAC,IAAI1pB,KAAK;IAACb,MAAM,CAACb,IAAI,CAAC,cAAc,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACyB,KAAK,GAACzB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAG5Y,MAAMD,OAAO,GAAG2C,MAAM,CAACC,MAAM,CAACc,gBAAgB,EAAE;MACrDkc,QAAQ,EAAElc,gBAAgB,CAACgjB;IAC7B,CAAC,CAAC;IAkBK,MAAMoE,aAAa,GAAG,SAAAA,CAAU5S,KAAK,EAAEmM,OAAO,EAAE3d,QAAQ,EAAE;MAC/D,OAAO,UAAUK,GAAG,EAAE0T,MAAM,EAAE;QAC5B,IAAI,CAAE1T,GAAG,EAAE;UACT;UACA,IAAI;YACFsd,OAAO,CAAC,CAAC;UACX,CAAC,CAAC,OAAO4G,UAAU,EAAE;YACnB,IAAIvkB,QAAQ,EAAE;cACZA,QAAQ,CAACukB,UAAU,CAAC;cACpB;YACF,CAAC,MAAM;cACL,MAAMA,UAAU;YAClB;UACF;QACF;QACA/S,KAAK,CAAC5D,SAAS,CAAC,CAAC;QACjB,IAAI5N,QAAQ,EAAE;UACZA,QAAQ,CAACK,GAAG,EAAE0T,MAAM,CAAC;QACvB,CAAC,MAAM,IAAI1T,GAAG,EAAE;UACd,MAAMA,GAAG;QACX;MACF,CAAC;IACH,CAAC;IAGM,MAAMya,eAAe,GAAG,SAAAA,CAAU0J,YAAY,EAAE;MACrD,IAAIzE,YAAY,GAAG;QAAEzB,cAAc,EAAE;MAAE,CAAC;MACxC,IAAIkG,YAAY,EAAE;QAChB,IAAIC,WAAW,GAAGD,YAAY,CAACzQ,MAAM;QACrC;QACA;QACA;QACA,IAAI0Q,WAAW,CAAC3C,aAAa,EAAE;UAC7B/B,YAAY,CAACzB,cAAc,GAAGmG,WAAW,CAAC3C,aAAa;UAEvD,IAAI2C,WAAW,CAAC1C,UAAU,EAAE;YAC1BhC,YAAY,CAACjC,UAAU,GAAG2G,WAAW,CAAC1C,UAAU;UAClD;QACF,CAAC,MAAM;UACL;UACA;UACAhC,YAAY,CAACzB,cAAc,GAAGmG,WAAW,CAACC,CAAC,IAAID,WAAW,CAACE,YAAY,IAAIF,WAAW,CAACpG,aAAa;QACtG;MACF;MAEA,OAAO0B,YAAY;IACrB,CAAC;IAEM,MAAMnF,0BAA0B,GAAG,SAAAA,CAAU4C,QAAQ,EAAE;MAC5D,IAAIrU,KAAK,CAACyb,QAAQ,CAACpH,QAAQ,CAAC,EAAE;QAC5B;QACA;QACA;QACA,OAAO,IAAIlkB,OAAO,CAACurB,MAAM,CAACC,MAAM,CAACC,IAAI,CAACvH,QAAQ,CAAC,CAAC;MAClD;MACA,IAAIA,QAAQ,YAAYlkB,OAAO,CAACurB,MAAM,EAAE;QACtC,OAAOrH,QAAQ;MACjB;MACA,IAAIA,QAAQ,YAAYvE,KAAK,CAACC,QAAQ,EAAE;QACtC,OAAO,IAAI5f,OAAO,CAAC0mB,QAAQ,CAACxC,QAAQ,CAACyC,WAAW,CAAC,CAAC,CAAC;MACrD;MACA,IAAIzC,QAAQ,YAAYlkB,OAAO,CAAC0mB,QAAQ,EAAE;QACxC,OAAO,IAAI1mB,OAAO,CAAC0mB,QAAQ,CAACxC,QAAQ,CAACyC,WAAW,CAAC,CAAC,CAAC;MACrD;MACA,IAAIzC,QAAQ,YAAYlkB,OAAO,CAACoB,SAAS,EAAE;QACzC;QACA;QACA;QACA;QACA,OAAO8iB,QAAQ;MACjB;MACA,IAAIA,QAAQ,YAAYwH,OAAO,EAAE;QAC/B,OAAO1rB,OAAO,CAAC2rB,UAAU,CAACC,UAAU,CAAC1H,QAAQ,CAAC2H,QAAQ,CAAC,CAAC,CAAC;MAC3D;MACA,IAAIhc,KAAK,CAACgQ,aAAa,CAACqE,QAAQ,CAAC,EAAE;QACjC,OAAO8G,YAAY,CAACc,cAAc,EAAEjc,KAAK,CAACkc,WAAW,CAAC7H,QAAQ,CAAC,CAAC;MAClE;MACA;MACA;MACA,OAAOxW,SAAS;IAClB,CAAC;IAEM,MAAM6T,YAAY,GAAG,SAAAA,CAAU2C,QAAQ,EAAE8H,eAAe,EAAE;MAC/D,IAAI,OAAO9H,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EACnD,OAAOA,QAAQ;MAEjB,IAAI+H,oBAAoB,GAAGD,eAAe,CAAC9H,QAAQ,CAAC;MACpD,IAAI+H,oBAAoB,KAAKve,SAAS,EACpC,OAAOue,oBAAoB;MAE7B,IAAIC,GAAG,GAAGhI,QAAQ;MAClBvhB,MAAM,CAACkc,OAAO,CAACqF,QAAQ,CAAC,CAACliB,OAAO,CAAC,UAAA8K,IAAA,EAAsB;QAAA,IAAZ,CAAC3K,GAAG,EAAEgqB,GAAG,CAAC,GAAArf,IAAA;QACnD,IAAIsf,WAAW,GAAG7K,YAAY,CAAC4K,GAAG,EAAEH,eAAe,CAAC;QACpD,IAAIG,GAAG,KAAKC,WAAW,EAAE;UACvB;UACA,IAAIF,GAAG,KAAKhI,QAAQ,EAClBgI,GAAG,GAAG5qB,KAAK,CAAC4iB,QAAQ,CAAC;UACvBgI,GAAG,CAAC/pB,GAAG,CAAC,GAAGiqB,WAAW;QACxB;MACF,CAAC,CAAC;MACF,OAAOF,GAAG;IACZ,CAAC;IAEM,MAAMnB,0BAA0B,GAAG,SAAAA,CAAU7G,QAAQ,EAAE;MAC5D,IAAIA,QAAQ,YAAYlkB,OAAO,CAACurB,MAAM,EAAE;QACtC;QACA,IAAIrH,QAAQ,CAACmI,QAAQ,KAAK,CAAC,EAAE;UAC3B,OAAOnI,QAAQ;QACjB;QACA,IAAIoI,MAAM,GAAGpI,QAAQ,CAACvY,KAAK,CAAC,IAAI,CAAC;QACjC,OAAO,IAAI4gB,UAAU,CAACD,MAAM,CAAC;MAC/B;MACA,IAAIpI,QAAQ,YAAYlkB,OAAO,CAAC0mB,QAAQ,EAAE;QACxC,OAAO,IAAI/G,KAAK,CAACC,QAAQ,CAACsE,QAAQ,CAACyC,WAAW,CAAC,CAAC,CAAC;MACnD;MACA,IAAIzC,QAAQ,YAAYlkB,OAAO,CAAC2rB,UAAU,EAAE;QAC1C,OAAOD,OAAO,CAACxH,QAAQ,CAAC2H,QAAQ,CAAC,CAAC,CAAC;MACrC;MACA,IAAI3H,QAAQ,CAAC,YAAY,CAAC,IAAIA,QAAQ,CAAC,aAAa,CAAC,IAAIvhB,MAAM,CAAC+M,IAAI,CAACwU,QAAQ,CAAC,CAAChc,MAAM,KAAK,CAAC,EAAE;QAC3F,OAAO2H,KAAK,CAAC2c,aAAa,CAACxB,YAAY,CAACyB,gBAAgB,EAAEvI,QAAQ,CAAC,CAAC;MACtE;MACA,IAAIA,QAAQ,YAAYlkB,OAAO,CAACoB,SAAS,EAAE;QACzC;QACA;QACA;QACA;QACA,OAAO8iB,QAAQ;MACjB;MACA,OAAOxW,SAAS;IAClB,CAAC;IAED,MAAMoe,cAAc,GAAG3P,IAAI,IAAI,OAAO,GAAGA,IAAI;IAC7C,MAAMsQ,gBAAgB,GAAGtQ,IAAI,IAAIA,IAAI,CAACuQ,MAAM,CAAC,CAAC,CAAC;IAExC,SAAS1B,YAAYA,CAACtP,MAAM,EAAEiR,KAAK,EAAE;MAC1C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;QAC/C,IAAIre,KAAK,CAACoR,OAAO,CAACiN,KAAK,CAAC,EAAE;UACxB,OAAOA,KAAK,CAAChiB,GAAG,CAACqgB,YAAY,CAACvY,IAAI,CAAC,IAAI,EAAEiJ,MAAM,CAAC,CAAC;QACnD;QACA,IAAIwQ,GAAG,GAAG,CAAC,CAAC;QACZvpB,MAAM,CAACkc,OAAO,CAAC8N,KAAK,CAAC,CAAC3qB,OAAO,CAAC,UAAAkO,KAAA,EAAwB;UAAA,IAAd,CAAC/N,GAAG,EAAEwJ,KAAK,CAAC,GAAAuE,KAAA;UAClDgc,GAAG,CAACxQ,MAAM,CAACvZ,GAAG,CAAC,CAAC,GAAG6oB,YAAY,CAACtP,MAAM,EAAE/P,KAAK,CAAC;QAChD,CAAC,CAAC;QACF,OAAOugB,GAAG;MACZ;MACA,OAAOS,KAAK;IACd;IAAC3pB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICzKD1C,MAAM,CAACjB,MAAM,CAAC;MAAC6hB,kBAAkB,EAACA,CAAA,KAAIA;IAAkB,CAAC,CAAC;IAAC,IAAI9e,eAAe;IAAC9B,MAAM,CAACb,IAAI,CAAC,mCAAmC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAAC0C,eAAe,GAAC1C,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIkrB,0BAA0B,EAACxJ,YAAY;IAAC9gB,MAAM,CAACb,IAAI,CAAC,gBAAgB,EAAC;MAACmrB,0BAA0BA,CAAClrB,CAAC,EAAC;QAACkrB,0BAA0B,GAAClrB,CAAC;MAAA,CAAC;MAAC0hB,YAAYA,CAAC1hB,CAAC,EAAC;QAAC0hB,YAAY,GAAC1hB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IASjY,MAAMohB,kBAAkB,CAAC;MAC9Bnd,WAAWA,CAACglB,QAAQ,EAAE3nB,iBAAiB,EAAEyN,OAAO,EAAE;QAChD,IAAI,CAAC4d,SAAS,GAAG1D,QAAQ;QACzB,IAAI,CAACxX,kBAAkB,GAAGnQ,iBAAiB;QAE3C,IAAI,CAACsrB,iBAAiB,GAAG7d,OAAO,CAAC6Z,gBAAgB,IAAI,IAAI;QACzD,IAAI7Z,OAAO,CAAC8Z,YAAY,IAAIvnB,iBAAiB,CAACyN,OAAO,CAACmO,SAAS,EAAE;UAC/D,IAAI,CAAC2P,UAAU,GAAGvqB,eAAe,CAACwqB,aAAa,CAC7CxrB,iBAAiB,CAACyN,OAAO,CAACmO,SAAS,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAAC2P,UAAU,GAAG,IAAI;QACxB;QAEA,IAAI,CAACE,WAAW,GAAG,IAAIzqB,eAAe,CAACuR,MAAM,CAAD,CAAC;MAC/C;MAEA,CAACmZ,MAAM,CAACC,aAAa,IAAI;QACvB,IAAIrQ,MAAM,GAAG,IAAI;QACjB,OAAO;UACL,MAAMgB,IAAIA,CAAA,EAAG;YACX,MAAMlS,KAAK,GAAG,MAAMkR,MAAM,CAACsQ,kBAAkB,CAAC,CAAC;YAC/C,OAAO;cAAErP,IAAI,EAAE,CAACnS,KAAK;cAAEA;YAAM,CAAC;UAChC;QACF,CAAC;MACH;;MAEA;MACA;MACA,MAAMyhB,qBAAqBA,CAAA,EAAG;QAC5B,IAAI;UACF,OAAO,IAAI,CAACR,SAAS,CAAC/O,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,OAAOjW,CAAC,EAAE;UACVY,OAAO,CAACC,KAAK,CAACb,CAAC,CAAC;QAClB;MACF;;MAEA;MACA;MACA,MAAMulB,kBAAkBA,CAAA,EAAI;QAC1B,OAAO,IAAI,EAAE;UACX,IAAIliB,GAAG,GAAG,MAAM,IAAI,CAACmiB,qBAAqB,CAAC,CAAC;UAE5C,IAAI,CAACniB,GAAG,EAAE,OAAO,IAAI;UACrBA,GAAG,GAAGsW,YAAY,CAACtW,GAAG,EAAE8f,0BAA0B,CAAC;UAEnD,IAAI,CAAC,IAAI,CAACrZ,kBAAkB,CAAC1C,OAAO,CAACjE,QAAQ,IAAI,KAAK,IAAIE,GAAG,EAAE;YAC7D;YACA;YACA;YACA;YACA;YACA;YACA,IAAI,IAAI,CAAC+hB,WAAW,CAACjc,GAAG,CAAC9F,GAAG,CAACa,GAAG,CAAC,EAAE;YACnC,IAAI,CAACkhB,WAAW,CAAC/b,GAAG,CAAChG,GAAG,CAACa,GAAG,EAAE,IAAI,CAAC;UACrC;UAEA,IAAI,IAAI,CAACghB,UAAU,EACjB7hB,GAAG,GAAG,IAAI,CAAC6hB,UAAU,CAAC7hB,GAAG,CAAC;UAE5B,OAAOA,GAAG;QACZ;MACF;;MAEA;MACA;MACA;MACA2e,6BAA6BA,CAACJ,SAAS,EAAE;QACvC,IAAI,CAACA,SAAS,EAAE;UACd,OAAO,IAAI,CAAC2D,kBAAkB,CAAC,CAAC;QAClC;QACA,MAAME,iBAAiB,GAAG,IAAI,CAACF,kBAAkB,CAAC,CAAC;QACnD,MAAMG,UAAU,GAAG,IAAI3mB,KAAK,CAAC,6CAA6C,CAAC;QAC3E,MAAM4mB,cAAc,GAAG,IAAI9nB,OAAO,CAAC,CAACgH,OAAO,EAAE4O,MAAM,KAAK;UACtD9S,UAAU,CAAC,MAAM;YACf8S,MAAM,CAACiS,UAAU,CAAC;UACpB,CAAC,EAAE9D,SAAS,CAAC;QACf,CAAC,CAAC;QACF,OAAO/jB,OAAO,CAAC+nB,IAAI,CAAC,CAACH,iBAAiB,EAAEE,cAAc,CAAC,CAAC,CACrD9I,KAAK,CAAE1d,GAAG,IAAK;UACd,IAAIA,GAAG,KAAKumB,UAAU,EAAE;YACtB,IAAI,CAAChK,KAAK,CAAC,CAAC;YACZ;UACF;UACA,MAAMvc,GAAG;QACX,CAAC,CAAC;MACN;MAEA,MAAM/E,OAAOA,CAAC0E,QAAQ,EAAE+mB,OAAO,EAAE;QAC/B;QACA,IAAI,CAACC,OAAO,CAAC,CAAC;QAEd,IAAIC,GAAG,GAAG,CAAC;QACX,OAAO,IAAI,EAAE;UACX,MAAM1iB,GAAG,GAAG,MAAM,IAAI,CAACkiB,kBAAkB,CAAC,CAAC;UAC3C,IAAI,CAACliB,GAAG,EAAE;UACV,MAAMvE,QAAQ,CAACqN,IAAI,CAAC0Z,OAAO,EAAExiB,GAAG,EAAE0iB,GAAG,EAAE,EAAE,IAAI,CAACd,iBAAiB,CAAC;QAClE;MACF;MAEA,MAAMliB,GAAGA,CAACjE,QAAQ,EAAE+mB,OAAO,EAAE;QAC3B,MAAMlS,OAAO,GAAG,EAAE;QAClB,MAAM,IAAI,CAACvZ,OAAO,CAAC,OAAOiJ,GAAG,EAAEic,KAAK,KAAK;UACvC3L,OAAO,CAAC5Z,IAAI,CAAC,MAAM+E,QAAQ,CAACqN,IAAI,CAAC0Z,OAAO,EAAExiB,GAAG,EAAEic,KAAK,EAAE,IAAI,CAAC2F,iBAAiB,CAAC,CAAC;QAChF,CAAC,CAAC;QAEF,OAAOtR,OAAO;MAChB;MAEAmS,OAAOA,CAAA,EAAG;QACR;QACA,IAAI,CAACd,SAAS,CAACgB,MAAM,CAAC,CAAC;QAEvB,IAAI,CAACZ,WAAW,GAAG,IAAIzqB,eAAe,CAACuR,MAAM,CAAD,CAAC;MAC/C;;MAEA;MACAwP,KAAKA,CAAA,EAAG;QACN,IAAI,CAACsJ,SAAS,CAACtJ,KAAK,CAAC,CAAC;MACxB;MAEA1S,KAAKA,CAAA,EAAG;QACN,OAAO,IAAI,CAACjG,GAAG,CAACM,GAAG,IAAIA,GAAG,CAAC;MAC7B;;MAEA;AACF;AACA;AACA;AACA;MACE4iB,KAAKA,CAAA,EAAG;QACN,OAAO,IAAI,CAACjB,SAAS,CAACiB,KAAK,CAAC,CAAC;MAC/B;;MAEA;MACA,MAAM5Z,aAAaA,CAACjH,OAAO,EAAE;QAC3B,IAAI9J,IAAI,GAAG,IAAI;QACf,IAAI8J,OAAO,EAAE;UACX,OAAO9J,IAAI,CAAC0N,KAAK,CAAC,CAAC;QACrB,CAAC,MAAM;UACL,IAAI2K,OAAO,GAAG,IAAIhZ,eAAe,CAACuR,MAAM,CAAD,CAAC;UACxC,MAAM5Q,IAAI,CAAClB,OAAO,CAAC,UAAUiJ,GAAG,EAAE;YAChCsQ,OAAO,CAACtK,GAAG,CAAChG,GAAG,CAACa,GAAG,EAAEb,GAAG,CAAC;UAC3B,CAAC,CAAC;UACF,OAAOsQ,OAAO;QAChB;MACF;IACF;IAACvY,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC3JD1C,MAAA,CAAOjB,MAAE;MAAAmV,MAAA,EAAAA,CAAA,KAAAA;IAAsB;IAAA,IAAAmZ,oBAA0B,EAAA3M,kBAAA;IAAA1gB,MAA4B,CAACb,IAAA;MAAAkuB,qBAAAjuB,CAAA;QAAAiuB,oBAAA,GAAAjuB,CAAA;MAAA;MAAAshB,mBAAAthB,CAAA;QAAAshB,kBAAA,GAAAthB,CAAA;MAAA;IAAA;IAAA,IAAAyhB,0BAAA,EAAAC,YAAA;IAAA9gB,MAAA,CAAAb,IAAA;MAAA0hB,2BAAAzhB,CAAA;QAAAyhB,0BAAA,GAAAzhB,CAAA;MAAA;MAAA0hB,aAAA1hB,CAAA;QAAA0hB,YAAA,GAAA1hB,CAAA;MAAA;IAAA;IAAA,IAAA0C,eAAA;IAAA9B,MAAA,CAAAb,IAAA;MAAAyD,QAAAxD,CAAA;QAAA0C,eAAA,GAAA1C,CAAA;MAAA;IAAA;IAAA,IAAAI,oBAAA,WAAAA,oBAAA;IA0BhF,MAAO0U,MAAM;MAKjBzQ,YAAYmG,KAAqB,EAAE9I,iBAAoC;QAAA,KAJhEwsB,MAAM;QAAA,KACNrc,kBAAkB;QAAA,KAClBsc,kBAAkB;QAGvB,IAAI,CAACD,MAAM,GAAG1jB,KAAK;QACnB,IAAI,CAACqH,kBAAkB,GAAGnQ,iBAAiB;QAC3C,IAAI,CAACysB,kBAAkB,GAAG,IAAI;MAChC;MAEA,MAAMC,UAAUA,CAAA;QACd,MAAM7rB,UAAU,GAAG,IAAI,CAAC2rB,MAAM,CAACvK,aAAa,CAAC,IAAI,CAAC9R,kBAAkB,CAACrP,cAAc,CAAC;QACpF,OAAO,MAAMD,UAAU,CAACglB,cAAc,CACpC7F,YAAY,CAAC,IAAI,CAAC7P,kBAAkB,CAACjP,QAAQ,EAAE6e,0BAA0B,CAAC,EAC1EC,YAAY,CAAC,IAAI,CAAC7P,kBAAkB,CAAC1C,OAAO,EAAEsS,0BAA0B,CAAC,CAC1E;MACH;MAEAuM,KAAKA,CAAA;QACH,MAAM,IAAIlnB,KAAK,CACb,0EAA0E,CAC3E;MACH;MAEAunB,YAAYA,CAAA;QACV,OAAO,IAAI,CAACxc,kBAAkB,CAAC1C,OAAO,CAACmO,SAAS;MAClD;MAEAgR,cAAcA,CAACC,GAAQ;QACrB,MAAMhsB,UAAU,GAAG,IAAI,CAACsP,kBAAkB,CAACrP,cAAc;QACzD,OAAOsd,KAAK,CAACqB,UAAU,CAACmN,cAAc,CAAC,IAAI,EAAEC,GAAG,EAAEhsB,UAAU,CAAC;MAC/D;MAEAisB,kBAAkBA,CAAA;QAChB,OAAO,IAAI,CAAC3c,kBAAkB,CAACrP,cAAc;MAC/C;MAEAisB,OAAOA,CAACtd,SAA8B;QACpC,OAAOzO,eAAe,CAACgsB,0BAA0B,CAAC,IAAI,EAAEvd,SAAS,CAAC;MACpE;MAEA,MAAMwd,YAAYA,CAACxd,SAA8B;QAC/C,OAAO,IAAIvL,OAAO,CAACgH,OAAO,IAAIA,OAAO,CAAC,IAAI,CAAC6hB,OAAO,CAACtd,SAAS,CAAC,CAAC,CAAC;MACjE;MAEAyd,cAAcA,CAACzd,SAAqC,EAAkD;QAAA,IAAhDhC,OAAA,GAAAZ,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAA8C,EAAE;QACpG,MAAMpB,OAAO,GAAGzK,eAAe,CAACmsB,kCAAkC,CAAC1d,SAAS,CAAC;QAC7E,OAAO,IAAI,CAAC+c,MAAM,CAACjE,eAAe,CAChC,IAAI,CAACpY,kBAAkB,EACvB1E,OAAO,EACPgE,SAAS,EACThC,OAAO,CAACY,oBAAoB,CAC7B;MACH;MAEA,MAAM+e,mBAAmBA,CAAC3d,SAAqC,EAAkD;QAAA,IAAhDhC,OAAA,GAAAZ,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAA8C,EAAE;QAC/G,OAAO,IAAI,CAACqgB,cAAc,CAACzd,SAAS,EAAEhC,OAAO,CAAC;MAChD;;IAGF;IACA,CAAC,GAAG8e,oBAAoB,EAAEb,MAAM,CAAC2B,QAAQ,EAAE3B,MAAM,CAACC,aAAa,CAAC,CAAClrB,OAAO,CAAC6sB,UAAU,IAAG;MACpF,IAAIA,UAAU,KAAK,OAAO,EAAE;MAE3Bla,MAAM,CAACtT,SAAiB,CAACwtB,UAAU,CAAC,GAAG,YAA0C;QAChF,MAAMhS,MAAM,GAAGiS,uBAAuB,CAAC,IAAI,EAAED,UAAU,CAAC;QACxD,OAAOhS,MAAM,CAACgS,UAAU,CAAC,CAAC,GAAAzgB,SAAO,CAAC;MACpC,CAAC;MAED,IAAIygB,UAAU,KAAK5B,MAAM,CAAC2B,QAAQ,IAAIC,UAAU,KAAK5B,MAAM,CAACC,aAAa,EAAE;MAE3E,MAAM6B,eAAe,GAAG5N,kBAAkB,CAAC0N,UAAU,CAAC;MAErDla,MAAM,CAACtT,SAAiB,CAAC0tB,eAAe,CAAC,GAAG,YAA0C;QACrF,OAAO,IAAI,CAACF,UAAU,CAAC,CAAC,GAAAzgB,SAAO,CAAC;MAClC,CAAC;IACH,CAAC,CAAC;IAEF,SAAS0gB,uBAAuBA,CAACjS,MAAmB,EAAEuL,MAAuB;MAC3E,IAAIvL,MAAM,CAACnL,kBAAkB,CAAC1C,OAAO,CAACjE,QAAQ,EAAE;QAC9C,MAAM,IAAIpE,KAAK,gBAAAkE,MAAA,CAAgBiG,MAAM,CAACsX,MAAM,CAAC,0BAAuB,CAAC;MACvE;MAEA,IAAI,CAACvL,MAAM,CAACmR,kBAAkB,EAAE;QAC9BnR,MAAM,CAACmR,kBAAkB,GAAGnR,MAAM,CAACkR,MAAM,CAACxb,yBAAyB,CACjEsK,MAAM,CAACnL,kBAAkB,EACzB;UACEmX,gBAAgB,EAAEhM,MAAM;UACxBiM,YAAY,EAAE;SACf,CACF;MACH;MAEA,OAAOjM,MAAM,CAACmR,kBAAkB;IAClC;IAAChrB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;ACzHD1C,MAAM,CAACjB,MAAM,CAAC;EAACwvB,qBAAqB,EAACA,CAAA,KAAIA;AAAqB,CAAC,CAAC;AACzD,MAAMA,qBAAqB,GAAG,IAAK,MAAMA,qBAAqB,CAAC;EACpE9qB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC+qB,iBAAiB,GAAGtsB,MAAM,CAACusB,MAAM,CAAC,IAAI,CAAC;EAC9C;EAEAC,IAAIA,CAAChT,IAAI,EAAEiT,IAAI,EAAE;IACf,IAAI,CAAEjT,IAAI,EAAE;MACV,OAAO,IAAI5Z,eAAe,CAAD,CAAC;IAC5B;IAEA,IAAI,CAAE6sB,IAAI,EAAE;MACV,OAAOC,gBAAgB,CAAClT,IAAI,EAAE,IAAI,CAAC8S,iBAAiB,CAAC;IACvD;IAEA,IAAI,CAAEG,IAAI,CAACE,2BAA2B,EAAE;MACtCF,IAAI,CAACE,2BAA2B,GAAG3sB,MAAM,CAACusB,MAAM,CAAC,IAAI,CAAC;IACxD;;IAEA;IACA;IACA,OAAOG,gBAAgB,CAAClT,IAAI,EAAEiT,IAAI,CAACE,2BAA2B,CAAC;EACjE;AACF,CAAC,EAAC;AAEF,SAASD,gBAAgBA,CAAClT,IAAI,EAAEoT,WAAW,EAAE;EAC3C,OAAQpT,IAAI,IAAIoT,WAAW,GACvBA,WAAW,CAACpT,IAAI,CAAC,GACjBoT,WAAW,CAACpT,IAAI,CAAC,GAAG,IAAI5Z,eAAe,CAAC4Z,IAAI,CAAC;AACnD,C;;;;;;;;;;;;;;IC7BA1b,MAAA,CAAOjB,MAAI;MAAAgwB,sBAAoB,EAAAA,CAAA,KAAAA;IAAA;IAAA,IAAAC,IAAA;IAAAhvB,MAAA,CAAAb,IAAA;MAAAyD,QAAAxD,CAAA;QAAA4vB,IAAA,GAAA5vB,CAAA;MAAA;IAAA;IAAA,IAAA6vB,wBAAA,EAAAvO,kBAAA,EAAAD,mBAAA;IAAAzgB,MAAA,CAAAb,IAAA;MAAA8vB,yBAAA7vB,CAAA;QAAA6vB,wBAAA,GAAA7vB,CAAA;MAAA;MAAAshB,mBAAAthB,CAAA;QAAAshB,kBAAA,GAAAthB,CAAA;MAAA;MAAAqhB,oBAAArhB,CAAA;QAAAqhB,mBAAA,GAAArhB,CAAA;MAAA;IAAA;IAAA,IAAAC,eAAA;IAAAW,MAAA,CAAAb,IAAA;MAAAE,gBAAAD,CAAA;QAAAC,eAAA,GAAAD,CAAA;MAAA;IAAA;IAAA,IAAAI,oBAAA,WAAAA,oBAAA;IAiD/B,MAAMuvB,sBAAsB;MAoB1BtrB,YAAYyrB,QAAgB,EAAE3gB,OAA2B;QAAA,KAnBxC3E,KAAK;QAoBpB,IAAI,CAACA,KAAK,GAAG,IAAIvK,eAAe,CAAC6vB,QAAQ,EAAE3gB,OAAO,CAAC;MACrD;MAEOmgB,IAAIA,CAAChT,IAAY;QACtB,MAAM+P,GAAG,GAAuB,EAAE;QAElC;QACAsD,sBAAsB,CAACI,yBAAyB,CAAC5tB,OAAO,CAAEomB,MAAM,IAAI;UAClE;UACA,MAAMyH,WAAW,GAAG,IAAI,CAACxlB,KAAK,CAAC+d,MAAM,CAAwB;UAC7D8D,GAAG,CAAC9D,MAAM,CAAC,GAAGyH,WAAW,CAACpd,IAAI,CAAC,IAAI,CAACpI,KAAK,EAAE8R,IAAI,CAAC;UAEhD,IAAI,CAACuT,wBAAwB,CAACnF,QAAQ,CAACnC,MAAM,CAAC,EAAE;UAEhD,MAAM0H,eAAe,GAAG3O,kBAAkB,CAACiH,MAAM,CAAC;UAClD8D,GAAG,CAAC4D,eAAe,CAAC,GAAG;YAAA,OAAwB5D,GAAG,CAAC9D,MAAM,CAAC,CAAC,GAAAha,SAAO,CAAC;UAAA;QACrE,CAAC,CAAC;QAEF;QACA8S,mBAAmB,CAAClf,OAAO,CAAEomB,MAAM,IAAI;UACrC8D,GAAG,CAAC9D,MAAM,CAAC,GAAG,YAA8B;YAC1C,MAAM,IAAIzhB,KAAK,IAAAkE,MAAA,CACVud,MAAM,kDAAAvd,MAAA,CAA+CsW,kBAAkB,CACxEiH,MAAM,CACP,gBAAa,CACf;UACH,CAAC;QACH,CAAC,CAAC;QAEF,OAAO8D,GAAG;MACZ;;IAGF;IAtDMsD,sBAAsB,CAGFI,yBAAyB,GAAG,CAClD,6BAA6B,EAC7B,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,qBAAqB,EACrB,wBAAwB,EACxB,MAAM,EACN,cAAc,EACd,aAAa,EACb,eAAe,EACf,aAAa,EACb,aAAa,EACb,aAAa,CACL;IAqCZ1vB,cAAc,CAACsvB,sBAAsB,GAAGA,sBAAsB;IAE9D;IACAtvB,cAAc,CAAC6vB,6BAA6B,GAAGN,IAAI,CAAC,MAA6B;MAC/E,MAAMO,iBAAiB,GAAuB,EAAE;MAChD,MAAML,QAAQ,GAAG9rB,OAAO,CAACC,GAAG,CAACmsB,SAAS;MAEtC,IAAI,CAACN,QAAQ,EAAE;QACb,MAAM,IAAIhpB,KAAK,CAAC,sCAAsC,CAAC;MACzD;MAEA,IAAI9C,OAAO,CAACC,GAAG,CAACosB,eAAe,EAAE;QAC/BF,iBAAiB,CAAC7rB,QAAQ,GAAGN,OAAO,CAACC,GAAG,CAACosB,eAAe;MAC1D;MAEA,MAAMlY,MAAM,GAAG,IAAIwX,sBAAsB,CAACG,QAAQ,EAAEK,iBAAiB,CAAC;MAEtE;MACAhvB,MAAM,CAACmvB,OAAO,CAAC,YAA0B;QACvC,MAAMnY,MAAM,CAAC3N,KAAK,CAACuY,MAAM,CAACwN,OAAO,EAAE;MACrC,CAAC,CAAC;MAEF,OAAOpY,MAAM;IACf,CAAC,CAAC;IAAChV,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC/HH,IAAIC,aAAa;IAAC7D,OAAO,CAACK,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuD,aAAa,GAACvD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAtG,IAAIwwB,mBAAmB;IAAC9wB,OAAO,CAACK,IAAI,CAAC,gBAAgB,EAAC;MAACywB,mBAAmBA,CAACxwB,CAAC,EAAC;QAACwwB,mBAAmB,GAACxwB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIywB,YAAY;IAAC/wB,OAAO,CAACK,IAAI,CAAC,iBAAiB,EAAC;MAAC0wB,YAAYA,CAACzwB,CAAC,EAAC;QAACywB,YAAY,GAACzwB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI0wB,WAAW;IAAChxB,OAAO,CAACK,IAAI,CAAC,gBAAgB,EAAC;MAAC2wB,WAAWA,CAAC1wB,CAAC,EAAC;QAAC0wB,WAAW,GAAC1wB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI2wB,YAAY;IAACjxB,OAAO,CAACK,IAAI,CAAC,iBAAiB,EAAC;MAAC4wB,YAAYA,CAAC3wB,CAAC,EAAC;QAAC2wB,YAAY,GAAC3wB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4wB,aAAa,EAACC,gBAAgB,EAACC,gBAAgB,EAACC,eAAe,EAACC,WAAW,EAACC,oBAAoB,EAACC,sBAAsB;IAACxxB,OAAO,CAACK,IAAI,CAAC,oBAAoB,EAAC;MAAC6wB,aAAaA,CAAC5wB,CAAC,EAAC;QAAC4wB,aAAa,GAAC5wB,CAAC;MAAA,CAAC;MAAC6wB,gBAAgBA,CAAC7wB,CAAC,EAAC;QAAC6wB,gBAAgB,GAAC7wB,CAAC;MAAA,CAAC;MAAC8wB,gBAAgBA,CAAC9wB,CAAC,EAAC;QAAC8wB,gBAAgB,GAAC9wB,CAAC;MAAA,CAAC;MAAC+wB,eAAeA,CAAC/wB,CAAC,EAAC;QAAC+wB,eAAe,GAAC/wB,CAAC;MAAA,CAAC;MAACgxB,WAAWA,CAAChxB,CAAC,EAAC;QAACgxB,WAAW,GAAChxB,CAAC;MAAA,CAAC;MAACixB,oBAAoBA,CAACjxB,CAAC,EAAC;QAACixB,oBAAoB,GAACjxB,CAAC;MAAA,CAAC;MAACkxB,sBAAsBA,CAAClxB,CAAC,EAAC;QAACkxB,sBAAsB,GAAClxB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAImxB,kBAAkB;IAACzxB,OAAO,CAACK,IAAI,CAAC,uBAAuB,EAAC;MAACoxB,kBAAkBA,CAACnxB,CAAC,EAAC;QAACmxB,kBAAkB,GAACnxB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAc18B;AACA;AACA;AACA;IACA0f,KAAK,GAAG,CAAC,CAAC;;IAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAA,KAAK,CAACqB,UAAU,GAAG,SAASA,UAAUA,CAAC7E,IAAI,EAAEnN,OAAO,EAAE;MAAA,IAAAiiB,qBAAA,EAAAC,cAAA;MACpD/U,IAAI,GAAG4U,sBAAsB,CAAC5U,IAAI,CAAC;MAEnCnN,OAAO,GAAG0hB,gBAAgB,CAAC1hB,OAAO,CAAC;MAEnC,IAAI,CAACmiB,UAAU,IAAAF,qBAAA,GAAG,CAAAC,cAAA,GAAAT,aAAa,EAACzhB,OAAO,CAACoiB,YAAY,CAAC,cAAAH,qBAAA,uBAAnCA,qBAAA,CAAAld,IAAA,CAAAmd,cAAA,EAAsC/U,IAAI,CAAC;MAE7D,IAAI,CAAC2Q,UAAU,GAAGvqB,eAAe,CAACwqB,aAAa,CAAC/d,OAAO,CAACmO,SAAS,CAAC;MAClE,IAAI,CAACkU,YAAY,GAAGriB,OAAO,CAACqiB,YAAY;MAExC,IAAI,CAACC,WAAW,GAAGV,eAAe,CAACzU,IAAI,EAAEnN,OAAO,CAAC;MAEjD,MAAMgJ,MAAM,GAAG6Y,WAAW,CAAC1U,IAAI,EAAE,IAAI,CAACmV,WAAW,EAAEtiB,OAAO,CAAC;MAC3D,IAAI,CAACuiB,OAAO,GAAGvZ,MAAM;MAErB,IAAI,CAACwZ,WAAW,GAAGxZ,MAAM,CAACmX,IAAI,CAAChT,IAAI,EAAE,IAAI,CAACmV,WAAW,CAAC;MACtD,IAAI,CAACG,KAAK,GAAGtV,IAAI;MAEjB,IAAI,CAACuV,4BAA4B,GAAG,IAAI,CAACC,sBAAsB,CAACxV,IAAI,EAAEnN,OAAO,CAAC;MAE9E8hB,oBAAoB,CAAC,IAAI,EAAE3U,IAAI,EAAEnN,OAAO,CAAC;MAEzC2hB,gBAAgB,CAAC,IAAI,EAAExU,IAAI,EAAEnN,OAAO,CAAC;MAErC2Q,KAAK,CAACiS,YAAY,CAAC3gB,GAAG,CAACkL,IAAI,EAAE,IAAI,CAAC;IACpC,CAAC;IAEDxZ,MAAM,CAACC,MAAM,CAAC+c,KAAK,CAACqB,UAAU,CAAC3f,SAAS,EAAE;MACxCwwB,gBAAgBA,CAACxjB,IAAI,EAAE;QACrB,IAAIA,IAAI,CAACnG,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAC3B,OAAOmG,IAAI,CAAC,CAAC,CAAC;MACrB,CAAC;MAEDyjB,eAAeA,CAACzjB,IAAI,EAAE;QACpB,MAAM,GAAGW,OAAO,CAAC,GAAGX,IAAI,IAAI,EAAE;QAC9B,MAAM0jB,UAAU,GAAG1B,mBAAmB,CAACrhB,OAAO,CAAC;QAE/C,IAAI9L,IAAI,GAAG,IAAI;QACf,IAAImL,IAAI,CAACnG,MAAM,GAAG,CAAC,EAAE;UACnB,OAAO;YAAEiV,SAAS,EAAEja,IAAI,CAAC4pB;UAAW,CAAC;QACvC,CAAC,MAAM;UACLjc,KAAK,CACHkhB,UAAU,EACVrd,KAAK,CAACsd,QAAQ,CACZtd,KAAK,CAAC6B,eAAe,CAAC;YACpB/O,UAAU,EAAEkN,KAAK,CAACsd,QAAQ,CAACtd,KAAK,CAAC+B,KAAK,CAAC9T,MAAM,EAAE+K,SAAS,CAAC,CAAC;YAC1DhG,IAAI,EAAEgN,KAAK,CAACsd,QAAQ,CAClBtd,KAAK,CAAC+B,KAAK,CAAC9T,MAAM,EAAE2L,KAAK,EAAEkI,QAAQ,EAAE9I,SAAS,CAChD,CAAC;YACD8H,KAAK,EAAEd,KAAK,CAACsd,QAAQ,CAACtd,KAAK,CAAC+B,KAAK,CAACwb,MAAM,EAAEvkB,SAAS,CAAC,CAAC;YACrD8Q,IAAI,EAAE9J,KAAK,CAACsd,QAAQ,CAACtd,KAAK,CAAC+B,KAAK,CAACwb,MAAM,EAAEvkB,SAAS,CAAC;UACrD,CAAC,CACH,CACF,CAAC;UAED,OAAAtK,aAAA;YACE+Z,SAAS,EAAEja,IAAI,CAAC4pB;UAAU,GACvBiF,UAAU;QAEjB;MACF;IACF,CAAC,CAAC;IAEFpvB,MAAM,CAACC,MAAM,CAAC+c,KAAK,CAACqB,UAAU,EAAE;MAC9B,MAAMmN,cAAcA,CAACtR,MAAM,EAAEuR,GAAG,EAAEhsB,UAAU,EAAE;QAC5C,IAAIgoB,aAAa,GAAG,MAAMvN,MAAM,CAAC4R,cAAc,CAC3C;UACElW,KAAK,EAAE,SAAAA,CAAS7V,EAAE,EAAEyN,MAAM,EAAE;YAC1Bie,GAAG,CAAC7V,KAAK,CAACnW,UAAU,EAAEM,EAAE,EAAEyN,MAAM,CAAC;UACnC,CAAC;UACDuJ,OAAO,EAAE,SAAAA,CAAShX,EAAE,EAAEyN,MAAM,EAAE;YAC5Bie,GAAG,CAAC1U,OAAO,CAACtX,UAAU,EAAEM,EAAE,EAAEyN,MAAM,CAAC;UACrC,CAAC;UACD2I,OAAO,EAAE,SAAAA,CAASpW,EAAE,EAAE;YACpB0rB,GAAG,CAACtV,OAAO,CAAC1W,UAAU,EAAEM,EAAE,CAAC;UAC7B;QACF,CAAC;QACD;QACA;QACA;UAAEkN,oBAAoB,EAAE;QAAK,CACjC,CAAC;;QAED;QACA;;QAEA;QACAwe,GAAG,CAACnhB,MAAM,CAAC,kBAAiB;UAC1B,OAAO,MAAMmd,aAAa,CAACroB,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;;QAEF;QACA,OAAOqoB,aAAa;MACtB,CAAC;MAED;MACA;MACA;MACA;MACA;MACAnJ,gBAAgBA,CAACxe,QAAQ,EAAuB;QAAA,IAArB;UAAEyvB;QAAW,CAAC,GAAA9jB,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,CAAC,CAAC;QAC5C;QACA,IAAI7L,eAAe,CAAC4vB,aAAa,CAAC1vB,QAAQ,CAAC,EAAEA,QAAQ,GAAG;UAAEqJ,GAAG,EAAErJ;QAAS,CAAC;QAEzE,IAAI6L,KAAK,CAACoR,OAAO,CAACjd,QAAQ,CAAC,EAAE;UAC3B;UACA;UACA,MAAM,IAAIkE,KAAK,CAAC,mCAAmC,CAAC;QACtD;QAEA,IAAI,CAAClE,QAAQ,IAAK,KAAK,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACqJ,GAAI,EAAE;UACrD;UACA,OAAO;YAAEA,GAAG,EAAEomB,UAAU,IAAIE,MAAM,CAAC1vB,EAAE,CAAC;UAAE,CAAC;QAC3C;QAEA,OAAOD,QAAQ;MACjB;IACF,CAAC,CAAC;IAEFE,MAAM,CAACC,MAAM,CAAC+c,KAAK,CAACqB,UAAU,CAAC3f,SAAS,EAAE2vB,kBAAkB,EAAET,WAAW,EAAED,YAAY,EAAEE,YAAY,CAAC;IAEtG7tB,MAAM,CAACC,MAAM,CAAC+c,KAAK,CAACqB,UAAU,CAAC3f,SAAS,EAAE;MACxC;MACA;MACAgxB,mBAAmBA,CAAA,EAAG;QACpB;QACA,OAAO,IAAI,CAACf,WAAW,IAAI,IAAI,CAACA,WAAW,KAAKtwB,MAAM,CAACsxB,MAAM;MAC/D,CAAC;MAED,MAAMrN,mBAAmBA,CAAA,EAAG;QAC1B,IAAI/hB,IAAI,GAAG,IAAI;QACf,IAAI,CAACA,IAAI,CAACsuB,WAAW,CAACvM,mBAAmB,EACvC,MAAM,IAAIte,KAAK,CAAC,yDAAyD,CAAC;QAC7E,MAAMzD,IAAI,CAACsuB,WAAW,CAACvM,mBAAmB,CAAC,CAAC;MAC7C,CAAC;MAED,MAAMxB,2BAA2BA,CAACC,QAAQ,EAAEC,YAAY,EAAE;QACxD,IAAIzgB,IAAI,GAAG,IAAI;QACf,IAAI,EAAE,MAAMA,IAAI,CAACsuB,WAAW,CAAC/N,2BAA2B,GACtD,MAAM,IAAI9c,KAAK,CACb,iEACF,CAAC;QACH,MAAMzD,IAAI,CAACsuB,WAAW,CAAC/N,2BAA2B,CAACC,QAAQ,EAAEC,YAAY,CAAC;MAC5E,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;MACEH,aAAaA,CAAA,EAAG;QACd,IAAItgB,IAAI,GAAG,IAAI;QACf,IAAI,CAACA,IAAI,CAACsuB,WAAW,CAAChO,aAAa,EAAE;UACnC,MAAM,IAAI7c,KAAK,CAAC,mDAAmD,CAAC;QACtE;QACA,OAAOzD,IAAI,CAACsuB,WAAW,CAAChO,aAAa,CAAC,CAAC;MACzC,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;MACE+O,WAAWA,CAAA,EAAG;QACZ,IAAIrvB,IAAI,GAAG,IAAI;QACf,IAAI,EAAEA,IAAI,CAACquB,OAAO,CAAClnB,KAAK,IAAInH,IAAI,CAACquB,OAAO,CAAClnB,KAAK,CAACX,EAAE,CAAC,EAAE;UAClD,MAAM,IAAI/C,KAAK,CAAC,iDAAiD,CAAC;QACpE;QACA,OAAOzD,IAAI,CAACquB,OAAO,CAAClnB,KAAK,CAACX,EAAE;MAC9B;IACF,CAAC,CAAC;IAEF/G,MAAM,CAACC,MAAM,CAAC+c,KAAK,EAAE;MACnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACE6S,aAAaA,CAACrW,IAAI,EAAE;QAClB,OAAO,IAAI,CAACyV,YAAY,CAAChxB,GAAG,CAACub,IAAI,CAAC;MACpC,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;MACEyV,YAAY,EAAE,IAAIjhB,GAAG,CAAC;IACxB,CAAC,CAAC;;IAIF;AACA;AACA;AACA;AACA;AACA;IACAgP,KAAK,CAACC,QAAQ,GAAG6S,OAAO,CAAC7S,QAAQ;;IAEjC;AACA;AACA;AACA;AACA;IACAD,KAAK,CAAChL,MAAM,GAAGpS,eAAe,CAACoS,MAAM;;IAErC;AACA;AACA;IACAgL,KAAK,CAACqB,UAAU,CAACrM,MAAM,GAAGgL,KAAK,CAAChL,MAAM;;IAEtC;AACA;AACA;IACAgL,KAAK,CAACqB,UAAU,CAACpB,QAAQ,GAAGD,KAAK,CAACC,QAAQ;;IAE1C;AACA;AACA;IACA5e,MAAM,CAACggB,UAAU,GAAGrB,KAAK,CAACqB,UAAU;;IAEpC;IACAre,MAAM,CAACC,MAAM,CAAC+c,KAAK,CAACqB,UAAU,CAAC3f,SAAS,EAAEqxB,SAAS,CAACC,mBAAmB,CAAC;IAAC3vB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC1QzE,IAAIC,aAAa;IAAC3C,MAAM,CAACb,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuD,aAAa,GAACvD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAAlKQ,MAAM,CAACjB,MAAM,CAAC;MAACixB,aAAa,EAACA,CAAA,KAAIA,aAAa;MAACG,eAAe,EAACA,CAAA,KAAIA,eAAe;MAACC,WAAW,EAACA,CAAA,KAAIA,WAAW;MAACF,gBAAgB,EAACA,CAAA,KAAIA,gBAAgB;MAACG,oBAAoB,EAACA,CAAA,KAAIA,oBAAoB;MAACC,sBAAsB,EAACA,CAAA,KAAIA,sBAAsB;MAACL,gBAAgB,EAACA,CAAA,KAAIA;IAAgB,CAAC,CAAC;IAArR,MAAMD,aAAa,GAAG;MAC3BmC,KAAKA,CAACzW,IAAI,EAAE;QACV,OAAO,YAAW;UAChB,MAAM0W,GAAG,GAAG1W,IAAI,GAAG2W,GAAG,CAACC,YAAY,CAAC,cAAc,GAAG5W,IAAI,CAAC,GAAGiW,MAAM,CAACY,QAAQ;UAC5E,OAAO,IAAIrT,KAAK,CAACC,QAAQ,CAACiT,GAAG,CAACI,SAAS,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC;MACH,CAAC;MACDC,MAAMA,CAAC/W,IAAI,EAAE;QACX,OAAO,YAAW;UAChB,MAAM0W,GAAG,GAAG1W,IAAI,GAAG2W,GAAG,CAACC,YAAY,CAAC,cAAc,GAAG5W,IAAI,CAAC,GAAGiW,MAAM,CAACY,QAAQ;UAC5E,OAAOH,GAAG,CAACnwB,EAAE,CAAC,CAAC;QACjB,CAAC;MACH;IACF,CAAC;IAEM,SAASkuB,eAAeA,CAACzU,IAAI,EAAEnN,OAAO,EAAE;MAC7C,IAAI,CAACmN,IAAI,IAAInN,OAAO,CAACmkB,UAAU,KAAK,IAAI,EAAE,OAAO,IAAI;MACrD,IAAInkB,OAAO,CAACmkB,UAAU,EAAE,OAAOnkB,OAAO,CAACmkB,UAAU;MACjD,OAAOnyB,MAAM,CAACoyB,QAAQ,GAAGpyB,MAAM,CAACmyB,UAAU,GAAGnyB,MAAM,CAACsxB,MAAM;IAC5D;IAEO,SAASzB,WAAWA,CAAC1U,IAAI,EAAEgX,UAAU,EAAEnkB,OAAO,EAAE;MACrD,IAAIA,OAAO,CAACuiB,OAAO,EAAE,OAAOviB,OAAO,CAACuiB,OAAO;MAE3C,IAAIpV,IAAI,IACNgX,UAAU,KAAKnyB,MAAM,CAACsxB,MAAM,IAC5B,OAAOpyB,cAAc,KAAK,WAAW,IACrCA,cAAc,CAAC6vB,6BAA6B,EAAE;QAC9C,OAAO7vB,cAAc,CAAC6vB,6BAA6B,CAAC,CAAC;MACvD;MAEA,MAAM;QAAEf;MAAsB,CAAC,GAAGlmB,OAAO,CAAC,+BAA+B,CAAC;MAC1E,OAAOkmB,qBAAqB;IAC9B;IAEO,SAAS2B,gBAAgBA,CAACvuB,UAAU,EAAE+Z,IAAI,EAAEnN,OAAO,EAAE;MAC1D,IAAIrB,OAAO,CAAC0lB,WAAW,IACrB,CAACrkB,OAAO,CAACskB,mBAAmB,IAC5BlxB,UAAU,CAACkvB,WAAW,IACtBlvB,UAAU,CAACkvB,WAAW,CAACiC,OAAO,EAAE;QAChCnxB,UAAU,CAACkvB,WAAW,CAACiC,OAAO,CAAC,IAAI,EAAE,MAAMnxB,UAAU,CAAC4kB,IAAI,CAAC,CAAC,EAAE;UAC5DwM,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;IAEO,SAAS1C,oBAAoBA,CAAC1uB,UAAU,EAAE+Z,IAAI,EAAEnN,OAAO,EAAE;MAC9D,IAAIA,OAAO,CAACykB,qBAAqB,KAAK,KAAK,EAAE;MAE7C,IAAI;QACFrxB,UAAU,CAACsxB,sBAAsB,CAAC;UAChCC,WAAW,EAAE3kB,OAAO,CAAC4kB,sBAAsB,KAAK;QAClD,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOnrB,KAAK,EAAE;QACd,IAAIA,KAAK,CAAC0L,OAAO,yBAAAtJ,MAAA,CAAyBsR,IAAI,qCAAkC,EAAE;UAChF,MAAM,IAAIxV,KAAK,0CAAAkE,MAAA,CAAyCsR,IAAI,OAAG,CAAC;QAClE;QACA,MAAM1T,KAAK;MACb;IACF;IAEO,SAASsoB,sBAAsBA,CAAC5U,IAAI,EAAE;MAC3C,IAAI,CAACA,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;QAC1Bnb,MAAM,CAACgG,MAAM,CACX,yDAAyD,GACzD,yDAAyD,GACzD,gDACF,CAAC;QACDmV,IAAI,GAAG,IAAI;MACb;MAEA,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC7C,MAAM,IAAIxV,KAAK,CACb,iEACF,CAAC;MACH;MAEA,OAAOwV,IAAI;IACb;IAEO,SAASuU,gBAAgBA,CAAC1hB,OAAO,EAAE;MACxC,IAAIA,OAAO,IAAIA,OAAO,CAAC6kB,OAAO,EAAE;QAC9B;QACA7kB,OAAO,GAAG;UAAEmkB,UAAU,EAAEnkB;QAAQ,CAAC;MACnC;MACA;MACA,IAAIA,OAAO,IAAIA,OAAO,CAAC8kB,OAAO,IAAI,CAAC9kB,OAAO,CAACmkB,UAAU,EAAE;QACrDnkB,OAAO,CAACmkB,UAAU,GAAGnkB,OAAO,CAAC8kB,OAAO;MACtC;MAEA,OAAA1wB,aAAA;QACE+vB,UAAU,EAAEzlB,SAAS;QACrB0jB,YAAY,EAAE,QAAQ;QACtBjU,SAAS,EAAE,IAAI;QACfoU,OAAO,EAAE7jB,SAAS;QAClB4lB,mBAAmB,EAAE;MAAK,GACvBtkB,OAAO;IAEd;IAAChM,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IClGD,IAAIC,aAAa;IAAC3C,MAAM,CAACb,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuD,aAAa,GAACvD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAAlKQ,MAAM,CAACjB,MAAM,CAAC;MAAC8wB,YAAY,EAACA,CAAA,KAAIA;IAAY,CAAC,CAAC;IAAvC,MAAMA,YAAY,GAAG;MAC1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE/oB,YAAYA,CAAA,EAAU;QAAA,SAAA4G,IAAA,GAAAC,SAAA,CAAAlG,MAAA,EAANmG,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;QAAA;QAClB,OAAO,IAAI,CAACijB,WAAW,CAACjqB,YAAY,CAClC,IAAI,CAACsqB,gBAAgB,CAACxjB,IAAI,CAAC,EAC3B,IAAI,CAACyjB,eAAe,CAACzjB,IAAI,CAC3B,CAAC;MACH,CAAC;MAED0lB,YAAYA,CAAC9oB,GAAG,EAAgB;QAAA,IAAd+D,OAAO,GAAAZ,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,CAAC,CAAC;QAC5B;QACA,IAAI,CAACnD,GAAG,EAAE;UACR,MAAM,IAAItE,KAAK,CAAC,6BAA6B,CAAC;QAChD;;QAEA;QACAsE,GAAG,GAAGtI,MAAM,CAACusB,MAAM,CACjBvsB,MAAM,CAACqxB,cAAc,CAAC/oB,GAAG,CAAC,EAC1BtI,MAAM,CAACsxB,yBAAyB,CAAChpB,GAAG,CACtC,CAAC;QAED,IAAI,KAAK,IAAIA,GAAG,EAAE;UAChB,IACE,CAACA,GAAG,CAACa,GAAG,IACR,EAAE,OAAOb,GAAG,CAACa,GAAG,KAAK,QAAQ,IAAIb,GAAG,CAACa,GAAG,YAAY6T,KAAK,CAACC,QAAQ,CAAC,EACnE;YACA,MAAM,IAAIjZ,KAAK,CACb,0EACF,CAAC;UACH;QACF,CAAC,MAAM;UACL,IAAIutB,UAAU,GAAG,IAAI;;UAErB;UACA;UACA;UACA,IAAI,IAAI,CAAC7B,mBAAmB,CAAC,CAAC,EAAE;YAC9B,MAAM8B,SAAS,GAAGrB,GAAG,CAACsB,wBAAwB,CAACxzB,GAAG,CAAC,CAAC;YACpD,IAAI,CAACuzB,SAAS,EAAE;cACdD,UAAU,GAAG,KAAK;YACpB;UACF;UAEA,IAAIA,UAAU,EAAE;YACdjpB,GAAG,CAACa,GAAG,GAAG,IAAI,CAACqlB,UAAU,CAAC,CAAC;UAC7B;QACF;;QAEA;QACA;QACA,IAAIkD,qCAAqC,GAAG,SAAAA,CAAS5Z,MAAM,EAAE;UAC3D,IAAIzZ,MAAM,CAACszB,UAAU,CAAC7Z,MAAM,CAAC,EAAE,OAAOA,MAAM;UAE5C,IAAIxP,GAAG,CAACa,GAAG,EAAE;YACX,OAAOb,GAAG,CAACa,GAAG;UAChB;;UAEA;UACA;UACA;UACAb,GAAG,CAACa,GAAG,GAAG2O,MAAM;UAEhB,OAAOA,MAAM;QACf,CAAC;QAED,IAAI,IAAI,CAAC4X,mBAAmB,CAAC,CAAC,EAAE;UAC9B,MAAMjiB,OAAO,GAAG,IAAI,CAACmkB,uBAAuB,CAAC,aAAa,EAAE,CAACtpB,GAAG,CAAC,EAAE+D,OAAO,CAAC;UAC3EoB,OAAO,CAACrC,IAAI,CAACsmB,qCAAqC,CAAC;UACnDjkB,OAAO,CAACokB,WAAW,GAAGpkB,OAAO,CAACokB,WAAW,CAACzmB,IAAI,CAACsmB,qCAAqC,CAAC;UACrFjkB,OAAO,CAACqkB,aAAa,GAAGrkB,OAAO,CAACqkB,aAAa,CAAC1mB,IAAI,CAACsmB,qCAAqC,CAAC;UACzF,OAAOjkB,OAAO;QAChB;;QAEA;QACA;QACA,OAAO,IAAI,CAACohB,WAAW,CAACxN,WAAW,CAAC/Y,GAAG,CAAC,CACrC8C,IAAI,CAACsmB,qCAAqC,CAAC;MAChD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACErQ,WAAWA,CAAC/Y,GAAG,EAAE+D,OAAO,EAAE;QACxB,OAAO,IAAI,CAAC+kB,YAAY,CAAC9oB,GAAG,EAAE+D,OAAO,CAAC;MACxC,CAAC;MAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEoW,WAAWA,CAAC3iB,QAAQ,EAAEmc,QAAQ,EAAyB;QAErD;QACA;QACA,MAAM5P,OAAO,GAAA5L,aAAA,KAAS,CAAAgL,SAAA,CAAAlG,MAAA,QAAAwF,SAAA,GAAAU,SAAA,QAAyB,IAAI,CAAG;QACtD,IAAIoW,UAAU;QACd,IAAIxV,OAAO,IAAIA,OAAO,CAACwW,MAAM,EAAE;UAC7B;UACA,IAAIxW,OAAO,CAACwV,UAAU,EAAE;YACtB,IACE,EACE,OAAOxV,OAAO,CAACwV,UAAU,KAAK,QAAQ,IACtCxV,OAAO,CAACwV,UAAU,YAAY7E,KAAK,CAACC,QAAQ,CAC7C,EAED,MAAM,IAAIjZ,KAAK,CAAC,uCAAuC,CAAC;YAC1D6d,UAAU,GAAGxV,OAAO,CAACwV,UAAU;UACjC,CAAC,MAAM,IAAI,CAAC/hB,QAAQ,IAAI,CAACA,QAAQ,CAACqJ,GAAG,EAAE;YACrC0Y,UAAU,GAAG,IAAI,CAAC2M,UAAU,CAAC,CAAC;YAC9BniB,OAAO,CAACkX,WAAW,GAAG,IAAI;YAC1BlX,OAAO,CAACwV,UAAU,GAAGA,UAAU;UACjC;QACF;QAEA/hB,QAAQ,GAAGkd,KAAK,CAACqB,UAAU,CAACC,gBAAgB,CAACxe,QAAQ,EAAE;UACrDyvB,UAAU,EAAE1N;QACd,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC6N,mBAAmB,CAAC,CAAC,EAAE;UAC9B,MAAMhkB,IAAI,GAAG,CAAC5L,QAAQ,EAAEmc,QAAQ,EAAE5P,OAAO,CAAC;UAE1C,OAAO,IAAI,CAACulB,uBAAuB,CAAC,aAAa,EAAElmB,IAAI,EAAEW,OAAO,CAAC;QACnE;;QAEA;QACA;QACA;QACA;QACA;;QAEA,OAAO,IAAI,CAACwiB,WAAW,CAACpM,WAAW,CACjC3iB,QAAQ,EACRmc,QAAQ,EACR5P,OACF,CAAC;MACH,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACE4V,WAAWA,CAACniB,QAAQ,EAAgB;QAAA,IAAduM,OAAO,GAAAZ,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,CAAC,CAAC;QAChC3L,QAAQ,GAAGkd,KAAK,CAACqB,UAAU,CAACC,gBAAgB,CAACxe,QAAQ,CAAC;QAEtD,IAAI,IAAI,CAAC4vB,mBAAmB,CAAC,CAAC,EAAE;UAC9B,OAAO,IAAI,CAACkC,uBAAuB,CAAC,aAAa,EAAE,CAAC9xB,QAAQ,CAAC,EAAEuM,OAAO,CAAC;QACzE;;QAEA;QACA;QACA,OAAO,IAAI,CAACwiB,WAAW,CAAC5M,WAAW,CAACniB,QAAQ,CAAC;MAC/C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE,MAAMskB,WAAWA,CAACtkB,QAAQ,EAAEmc,QAAQ,EAAE5P,OAAO,EAAE;QAC7C,OAAO,IAAI,CAACoW,WAAW,CACrB3iB,QAAQ,EACRmc,QAAQ,EAAAxb,aAAA,CAAAA,aAAA,KAEH4L,OAAO;UACVoX,aAAa,EAAE,IAAI;UACnBZ,MAAM,EAAE;QAAI,EACb,CAAC;MACN,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE4B,cAAcA,CAAA,EAAU;QACtB,OAAO,IAAI,CAACoK,WAAW,CAACpK,cAAc,CAAC,GAAAhZ,SAAO,CAAC;MACjD,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEkZ,sBAAsBA,CAAA,EAAU;QAC9B,OAAO,IAAI,CAACkK,WAAW,CAAClK,sBAAsB,CAAC,GAAAlZ,SAAO,CAAC;MACzD;IACF,CAAC;IAAApL,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AC3OD1C,MAAM,CAACjB,MAAM,CAAC;EAACgxB,YAAY,EAACA,CAAA,KAAIA;AAAY,CAAC,CAAC;AAAvC,MAAMA,YAAY,GAAG;EAC1B;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM/I,gBAAgBA,CAACP,KAAK,EAAElY,OAAO,EAAE;IACrC,IAAI9L,IAAI,GAAG,IAAI;IACf,IAAI,CAACA,IAAI,CAACsuB,WAAW,CAAC/J,gBAAgB,IAAI,CAACvkB,IAAI,CAACsuB,WAAW,CAACvK,gBAAgB,EAC1E,MAAM,IAAItgB,KAAK,CAAC,sDAAsD,CAAC;IACzE,IAAIzD,IAAI,CAACsuB,WAAW,CAACvK,gBAAgB,EAAE;MACrC,MAAM/jB,IAAI,CAACsuB,WAAW,CAACvK,gBAAgB,CAACC,KAAK,EAAElY,OAAO,CAAC;IACzD,CAAC,MAAM;MAtBX,IAAI0lB,GAAG;MAACj0B,MAAM,CAACb,IAAI,CAAC,gBAAgB,EAAC;QAAC80B,GAAGA,CAAC70B,CAAC,EAAC;UAAC60B,GAAG,GAAC70B,CAAC;QAAA;MAAC,CAAC,EAAC,CAAC,CAAC;MAyBjD60B,GAAG,CAACC,KAAK,uFAAA9pB,MAAA,CAAwFmE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmN,IAAI,oBAAAtR,MAAA,CAAqBmE,OAAO,CAACmN,IAAI,gBAAAtR,MAAA,CAAmB/C,IAAI,CAACC,SAAS,CAACmf,KAAK,CAAC,CAAG,CAAG,CAAC;MAC9L,MAAMhkB,IAAI,CAACsuB,WAAW,CAAC/J,gBAAgB,CAACP,KAAK,EAAElY,OAAO,CAAC;IACzD;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMiY,gBAAgBA,CAACC,KAAK,EAAElY,OAAO,EAAE;IACrC,IAAI9L,IAAI,GAAG,IAAI;IACf,IAAI,CAACA,IAAI,CAACsuB,WAAW,CAACvK,gBAAgB,EACpC,MAAM,IAAItgB,KAAK,CAAC,sDAAsD,CAAC;IAEzE,IAAI;MACF,MAAMzD,IAAI,CAACsuB,WAAW,CAACvK,gBAAgB,CAACC,KAAK,EAAElY,OAAO,CAAC;IACzD,CAAC,CAAC,OAAOpH,CAAC,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACV,IACEzB,CAAC,CAACuM,OAAO,CAACoW,QAAQ,CAChB,8EACF,CAAC,KAAAphB,gBAAA,GACDnI,MAAM,CAACmJ,QAAQ,cAAAhB,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBiB,QAAQ,cAAAhB,qBAAA,gBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BiB,KAAK,cAAAhB,sBAAA,eAAhCA,sBAAA,CAAkCurB,6BAA6B,EAC/D;QAvDR,IAAIF,GAAG;QAACj0B,MAAM,CAACb,IAAI,CAAC,gBAAgB,EAAC;UAAC80B,GAAGA,CAAC70B,CAAC,EAAC;YAAC60B,GAAG,GAAC70B,CAAC;UAAA;QAAC,CAAC,EAAC,CAAC,CAAC;QA0D/C60B,GAAG,CAACG,IAAI,sBAAAhqB,MAAA,CAAuBqc,KAAK,WAAArc,MAAA,CAAU3H,IAAI,CAACuuB,KAAK,8BAA4B,CAAC;QACrF,MAAMvuB,IAAI,CAACsuB,WAAW,CAAC9J,cAAc,CAACR,KAAK,CAAC;QAC5C,MAAMhkB,IAAI,CAACsuB,WAAW,CAACvK,gBAAgB,CAACC,KAAK,EAAElY,OAAO,CAAC;MACzD,CAAC,MAAM;QACLxG,OAAO,CAACC,KAAK,CAACb,CAAC,CAAC;QAChB,MAAM,IAAI5G,MAAM,CAAC2F,KAAK,8DAAAkE,MAAA,CAA8D3H,IAAI,CAACuuB,KAAK,QAAA5mB,MAAA,CAAOjD,CAAC,CAACuM,OAAO,CAAG,CAAC;MACpH;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEgT,WAAWA,CAACD,KAAK,EAAElY,OAAO,EAAC;IACzB,OAAO,IAAI,CAACiY,gBAAgB,CAACC,KAAK,EAAElY,OAAO,CAAC;EAC9C,CAAC;EAED,MAAM0Y,cAAcA,CAACR,KAAK,EAAE;IAC1B,IAAIhkB,IAAI,GAAG,IAAI;IACf,IAAI,CAACA,IAAI,CAACsuB,WAAW,CAAC9J,cAAc,EAClC,MAAM,IAAI/gB,KAAK,CAAC,oDAAoD,CAAC;IACvE,MAAMzD,IAAI,CAACsuB,WAAW,CAAC9J,cAAc,CAACR,KAAK,CAAC;EAC9C;AACF,CAAC,C;;;;;;;;;;;;;;IC1FD,IAAI9jB,aAAa;IAAC3C,MAAM,CAACb,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuD,aAAa,GAACvD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAAlKQ,MAAM,CAACjB,MAAM,CAAC;MAACwxB,kBAAkB,EAACA,CAAA,KAAIA;IAAkB,CAAC,CAAC;IAAnD,MAAMA,kBAAkB,GAAG;MAChC,MAAMW,sBAAsBA,CAACxV,IAAI,EAAE;QAAA,IAAA2Y,oBAAA,EAAAC,qBAAA;QACjC,MAAM7xB,IAAI,GAAG,IAAI;QACjB,IACE,EACEA,IAAI,CAACouB,WAAW,IAChBpuB,IAAI,CAACouB,WAAW,CAAC0D,mBAAmB,IACpC9xB,IAAI,CAACouB,WAAW,CAAC2D,mBAAmB,CACrC,EACD;UACA;QACF;QAGA,MAAMC,kBAAkB,GAAG;UACzB;UACA;UACAC,aAAaA,CAAA,EAAG;YACdjyB,IAAI,CAACsuB,WAAW,CAAC2D,aAAa,CAAC,CAAC;UAClC,CAAC;UACDC,iBAAiBA,CAAA,EAAG;YAClB,OAAOlyB,IAAI,CAACsuB,WAAW,CAAC4D,iBAAiB,CAAC,CAAC;UAC7C,CAAC;UACD;UACAC,cAAcA,CAAA,EAAG;YACf,OAAOnyB,IAAI;UACb;QACF,CAAC;QACD,MAAMoyB,kBAAkB,GAAAlyB,aAAA;UACtB;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAMmyB,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;YAClC;YACA;YACA;YACA;YACA;YACA,IAAID,SAAS,GAAG,CAAC,IAAIC,KAAK,EAAEvyB,IAAI,CAACsuB,WAAW,CAACkE,cAAc,CAAC,CAAC;YAE7D,IAAID,KAAK,EAAE,MAAMvyB,IAAI,CAACsuB,WAAW,CAAC3Y,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC;UAED;UACA;UACA8c,MAAMA,CAACC,GAAG,EAAE;YACV,IAAIC,OAAO,GAAGpD,OAAO,CAACqD,OAAO,CAACF,GAAG,CAAClzB,EAAE,CAAC;YACrC,IAAIuI,GAAG,GAAG/H,IAAI,CAACsuB,WAAW,CAACuE,KAAK,CAACn1B,GAAG,CAACi1B,OAAO,CAAC;;YAE7C;YACA;YACA;YACA;;YAEA;YACA;;YAEA;YACA;YACA,IAAI70B,MAAM,CAACoyB,QAAQ,EAAE;cACnB,IAAIwC,GAAG,CAACA,GAAG,KAAK,OAAO,IAAI3qB,GAAG,EAAE;gBAC9B2qB,GAAG,CAACA,GAAG,GAAG,SAAS;cACrB,CAAC,MAAM,IAAIA,GAAG,CAACA,GAAG,KAAK,SAAS,IAAI,CAAC3qB,GAAG,EAAE;gBACxC;cACF,CAAC,MAAM,IAAI2qB,GAAG,CAACA,GAAG,KAAK,SAAS,IAAI,CAAC3qB,GAAG,EAAE;gBACxC2qB,GAAG,CAACA,GAAG,GAAG,OAAO;gBACjB,MAAM9oB,IAAI,GAAG8oB,GAAG,CAACzlB,MAAM;gBACvB,KAAK,IAAI6O,KAAK,IAAIlS,IAAI,EAAE;kBACtB,MAAMnB,KAAK,GAAGmB,IAAI,CAACkS,KAAK,CAAC;kBACzB,IAAIrT,KAAK,KAAK,KAAK,CAAC,EAAE;oBACpB,OAAOiqB,GAAG,CAACzlB,MAAM,CAAC6O,KAAK,CAAC;kBAC1B;gBACF;cACF;YACF;YACA;YACA;YACA;YACA,IAAI4W,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;cACzB,IAAIrT,OAAO,GAAGqT,GAAG,CAACrT,OAAO;cACzB,IAAI,CAACA,OAAO,EAAE;gBACZ,IAAItX,GAAG,EAAE/H,IAAI,CAACsuB,WAAW,CAAC3Y,MAAM,CAACgd,OAAO,CAAC;cAC3C,CAAC,MAAM,IAAI,CAAC5qB,GAAG,EAAE;gBACf/H,IAAI,CAACsuB,WAAW,CAACwE,MAAM,CAACzT,OAAO,CAAC;cAClC,CAAC,MAAM;gBACL;gBACArf,IAAI,CAACsuB,WAAW,CAACmE,MAAM,CAACE,OAAO,EAAEtT,OAAO,CAAC;cAC3C;cACA;YACF,CAAC,MAAM,IAAIqT,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;cAC9B,IAAI3qB,GAAG,EAAE;gBACP,MAAM,IAAItE,KAAK,CACb,4DACF,CAAC;cACH;cACAzD,IAAI,CAACsuB,WAAW,CAACwE,MAAM,CAAA5yB,aAAA;gBAAG0I,GAAG,EAAE+pB;cAAO,GAAKD,GAAG,CAACzlB,MAAM,CAAE,CAAC;YAC1D,CAAC,MAAM,IAAIylB,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;cAChC,IAAI,CAAC3qB,GAAG,EACN,MAAM,IAAItE,KAAK,CACb,yDACF,CAAC;cACHzD,IAAI,CAACsuB,WAAW,CAAC3Y,MAAM,CAACgd,OAAO,CAAC;YAClC,CAAC,MAAM,IAAID,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;cAChC,IAAI,CAAC3qB,GAAG,EAAE,MAAM,IAAItE,KAAK,CAAC,uCAAuC,CAAC;cAClE,MAAM+I,IAAI,GAAG/M,MAAM,CAAC+M,IAAI,CAACkmB,GAAG,CAACzlB,MAAM,CAAC;cACpC,IAAIT,IAAI,CAACxH,MAAM,GAAG,CAAC,EAAE;gBACnB,IAAI0W,QAAQ,GAAG,CAAC,CAAC;gBACjBlP,IAAI,CAAC1N,OAAO,CAACG,GAAG,IAAI;kBAClB,MAAMwJ,KAAK,GAAGiqB,GAAG,CAACzlB,MAAM,CAAChO,GAAG,CAAC;kBAC7B,IAAI0N,KAAK,CAAC+I,MAAM,CAAC3N,GAAG,CAAC9I,GAAG,CAAC,EAAEwJ,KAAK,CAAC,EAAE;oBACjC;kBACF;kBACA,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;oBAChC,IAAI,CAACiT,QAAQ,CAACuB,MAAM,EAAE;sBACpBvB,QAAQ,CAACuB,MAAM,GAAG,CAAC,CAAC;oBACtB;oBACAvB,QAAQ,CAACuB,MAAM,CAAChe,GAAG,CAAC,GAAG,CAAC;kBAC1B,CAAC,MAAM;oBACL,IAAI,CAACyc,QAAQ,CAACyB,IAAI,EAAE;sBAClBzB,QAAQ,CAACyB,IAAI,GAAG,CAAC,CAAC;oBACpB;oBACAzB,QAAQ,CAACyB,IAAI,CAACle,GAAG,CAAC,GAAGwJ,KAAK;kBAC5B;gBACF,CAAC,CAAC;gBACF,IAAIhJ,MAAM,CAAC+M,IAAI,CAACkP,QAAQ,CAAC,CAAC1W,MAAM,GAAG,CAAC,EAAE;kBACpChF,IAAI,CAACsuB,WAAW,CAACmE,MAAM,CAACE,OAAO,EAAEjX,QAAQ,CAAC;gBAC5C;cACF;YACF,CAAC,MAAM;cACL,MAAM,IAAIjY,KAAK,CAAC,4CAA4C,CAAC;YAC/D;UACF,CAAC;UAED;UACAsvB,SAASA,CAAA,EAAG;YACV/yB,IAAI,CAACsuB,WAAW,CAAC0E,qBAAqB,CAAC,CAAC;UAC1C,CAAC;UAED;UACAC,MAAMA,CAACzzB,EAAE,EAAE;YACT,OAAOQ,IAAI,CAACkzB,OAAO,CAAC1zB,EAAE,CAAC;UACzB;QAAC,GAEEwyB,kBAAkB,CACtB;QACD,MAAMmB,kBAAkB,GAAAjzB,aAAA;UACtB,MAAMmyB,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;YAClC,IAAID,SAAS,GAAG,CAAC,IAAIC,KAAK,EAAEvyB,IAAI,CAACsuB,WAAW,CAACkE,cAAc,CAAC,CAAC;YAE7D,IAAID,KAAK,EAAE,MAAMvyB,IAAI,CAACsuB,WAAW,CAAC5M,WAAW,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC;UAED,MAAM+Q,MAAMA,CAACC,GAAG,EAAE;YAChB,IAAIC,OAAO,GAAGpD,OAAO,CAACqD,OAAO,CAACF,GAAG,CAAClzB,EAAE,CAAC;YACrC,IAAIuI,GAAG,GAAG/H,IAAI,CAACsuB,WAAW,CAACuE,KAAK,CAACn1B,GAAG,CAACi1B,OAAO,CAAC;;YAE7C;YACA;YACA;YACA,IAAID,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;cACzB,IAAIrT,OAAO,GAAGqT,GAAG,CAACrT,OAAO;cACzB,IAAI,CAACA,OAAO,EAAE;gBACZ,IAAItX,GAAG,EAAE,MAAM/H,IAAI,CAACsuB,WAAW,CAAC5M,WAAW,CAACiR,OAAO,CAAC;cACtD,CAAC,MAAM,IAAI,CAAC5qB,GAAG,EAAE;gBACf,MAAM/H,IAAI,CAACsuB,WAAW,CAACxN,WAAW,CAACzB,OAAO,CAAC;cAC7C,CAAC,MAAM;gBACL;gBACA,MAAMrf,IAAI,CAACsuB,WAAW,CAACpM,WAAW,CAACyQ,OAAO,EAAEtT,OAAO,CAAC;cACtD;cACA;YACF,CAAC,MAAM,IAAIqT,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;cAC9B,IAAI3qB,GAAG,EAAE;gBACP,MAAM,IAAItE,KAAK,CACb,4DACF,CAAC;cACH;cACA,MAAMzD,IAAI,CAACsuB,WAAW,CAACxN,WAAW,CAAA5gB,aAAA;gBAAG0I,GAAG,EAAE+pB;cAAO,GAAKD,GAAG,CAACzlB,MAAM,CAAE,CAAC;YACrE,CAAC,MAAM,IAAIylB,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;cAChC,IAAI,CAAC3qB,GAAG,EACN,MAAM,IAAItE,KAAK,CACb,yDACF,CAAC;cACH,MAAMzD,IAAI,CAACsuB,WAAW,CAAC5M,WAAW,CAACiR,OAAO,CAAC;YAC7C,CAAC,MAAM,IAAID,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;cAChC,IAAI,CAAC3qB,GAAG,EAAE,MAAM,IAAItE,KAAK,CAAC,uCAAuC,CAAC;cAClE,MAAM+I,IAAI,GAAG/M,MAAM,CAAC+M,IAAI,CAACkmB,GAAG,CAACzlB,MAAM,CAAC;cACpC,IAAIT,IAAI,CAACxH,MAAM,GAAG,CAAC,EAAE;gBACnB,IAAI0W,QAAQ,GAAG,CAAC,CAAC;gBACjBlP,IAAI,CAAC1N,OAAO,CAACG,GAAG,IAAI;kBAClB,MAAMwJ,KAAK,GAAGiqB,GAAG,CAACzlB,MAAM,CAAChO,GAAG,CAAC;kBAC7B,IAAI0N,KAAK,CAAC+I,MAAM,CAAC3N,GAAG,CAAC9I,GAAG,CAAC,EAAEwJ,KAAK,CAAC,EAAE;oBACjC;kBACF;kBACA,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;oBAChC,IAAI,CAACiT,QAAQ,CAACuB,MAAM,EAAE;sBACpBvB,QAAQ,CAACuB,MAAM,GAAG,CAAC,CAAC;oBACtB;oBACAvB,QAAQ,CAACuB,MAAM,CAAChe,GAAG,CAAC,GAAG,CAAC;kBAC1B,CAAC,MAAM;oBACL,IAAI,CAACyc,QAAQ,CAACyB,IAAI,EAAE;sBAClBzB,QAAQ,CAACyB,IAAI,GAAG,CAAC,CAAC;oBACpB;oBACAzB,QAAQ,CAACyB,IAAI,CAACle,GAAG,CAAC,GAAGwJ,KAAK;kBAC5B;gBACF,CAAC,CAAC;gBACF,IAAIhJ,MAAM,CAAC+M,IAAI,CAACkP,QAAQ,CAAC,CAAC1W,MAAM,GAAG,CAAC,EAAE;kBACpC,MAAMhF,IAAI,CAACsuB,WAAW,CAACpM,WAAW,CAACyQ,OAAO,EAAEjX,QAAQ,CAAC;gBACvD;cACF;YACF,CAAC,MAAM;cACL,MAAM,IAAIjY,KAAK,CAAC,4CAA4C,CAAC;YAC/D;UACF,CAAC;UAED;UACA,MAAMsvB,SAASA,CAAA,EAAG;YAChB,MAAM/yB,IAAI,CAACsuB,WAAW,CAAC8E,qBAAqB,CAAC,CAAC;UAChD,CAAC;UAED;UACA,MAAMH,MAAMA,CAACzzB,EAAE,EAAE;YACf,OAAOQ,IAAI,CAACqE,YAAY,CAAC7E,EAAE,CAAC;UAC9B;QAAC,GACEwyB,kBAAkB,CACtB;;QAGD;QACA;QACA;QACA,IAAIqB,mBAAmB;QACvB,IAAIv1B,MAAM,CAACoyB,QAAQ,EAAE;UACnBmD,mBAAmB,GAAGrzB,IAAI,CAACouB,WAAW,CAAC0D,mBAAmB,CACxD7Y,IAAI,EACJmZ,kBACF,CAAC;QACH,CAAC,MAAM;UACLiB,mBAAmB,GAAGrzB,IAAI,CAACouB,WAAW,CAAC2D,mBAAmB,CACxD9Y,IAAI,EACJka,kBACF,CAAC;QACH;QAEA,MAAMliB,OAAO,4CAAAtJ,MAAA,CAA2CsR,IAAI,OAAG;QAC/D,MAAMqa,OAAO,GAAGA,CAAA,KAAM;UACpBhuB,OAAO,CAACgiB,IAAI,GAAGhiB,OAAO,CAACgiB,IAAI,CAACrW,OAAO,CAAC,GAAG3L,OAAO,CAACiuB,GAAG,CAACtiB,OAAO,CAAC;QAC7D,CAAC;QAED,IAAI,CAACoiB,mBAAmB,EAAE;UACxB,OAAOC,OAAO,CAAC,CAAC;QAClB;QAEA,QAAA1B,oBAAA,GAAOyB,mBAAmB,cAAAzB,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqB/mB,IAAI,cAAAgnB,qBAAA,uBAAzBA,qBAAA,CAAAhhB,IAAA,CAAA+gB,oBAAA,EAA4B4B,EAAE,IAAI;UACvC,IAAI,CAACA,EAAE,EAAE;YACPF,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAAAxzB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICzQD,IAAIC,aAAa;IAAC3C,MAAM,CAACb,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuD,aAAa,GAACvD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAAlKQ,MAAM,CAACjB,MAAM,CAAC;MAAC+wB,WAAW,EAACA,CAAA,KAAIA;IAAW,CAAC,CAAC;IAArC,MAAMA,WAAW,GAAG;MACzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEvJ,IAAIA,CAAA,EAAU;QAAA,SAAA7Y,IAAA,GAAAC,SAAA,CAAAlG,MAAA,EAANmG,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;QAAA;QACV;QACA;QACA;QACA,OAAO,IAAI,CAACijB,WAAW,CAACxK,IAAI,CAC1B,IAAI,CAAC6K,gBAAgB,CAACxjB,IAAI,CAAC,EAC3B,IAAI,CAACyjB,eAAe,CAACzjB,IAAI,CAC3B,CAAC;MACH,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE+nB,OAAOA,CAAA,EAAU;QAAA,SAAA7O,KAAA,GAAAnZ,SAAA,CAAAlG,MAAA,EAANmG,IAAI,OAAAC,KAAA,CAAAiZ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJnZ,IAAI,CAAAmZ,KAAA,IAAApZ,SAAA,CAAAoZ,KAAA;QAAA;QACb,OAAO,IAAI,CAACgK,WAAW,CAAC4E,OAAO,CAC7B,IAAI,CAACvE,gBAAgB,CAACxjB,IAAI,CAAC,EAC3B,IAAI,CAACyjB,eAAe,CAACzjB,IAAI,CAC3B,CAAC;MACH,CAAC;MAGD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEAsoB,OAAOA,CAAC1rB,GAAG,EAAEvE,QAAQ,EAAE;QACrB;QACA,IAAI,CAACuE,GAAG,EAAE;UACR,MAAM,IAAItE,KAAK,CAAC,6BAA6B,CAAC;QAChD;;QAGA;QACAsE,GAAG,GAAGtI,MAAM,CAACusB,MAAM,CACjBvsB,MAAM,CAACqxB,cAAc,CAAC/oB,GAAG,CAAC,EAC1BtI,MAAM,CAACsxB,yBAAyB,CAAChpB,GAAG,CACtC,CAAC;QAED,IAAI,KAAK,IAAIA,GAAG,EAAE;UAChB,IACE,CAACA,GAAG,CAACa,GAAG,IACR,EAAE,OAAOb,GAAG,CAACa,GAAG,KAAK,QAAQ,IAAIb,GAAG,CAACa,GAAG,YAAY6T,KAAK,CAACC,QAAQ,CAAC,EACnE;YACA,MAAM,IAAIjZ,KAAK,CACb,0EACF,CAAC;UACH;QACF,CAAC,MAAM;UACL,IAAIutB,UAAU,GAAG,IAAI;;UAErB;UACA;UACA;UACA,IAAI,IAAI,CAAC7B,mBAAmB,CAAC,CAAC,EAAE;YAC9B,MAAM8B,SAAS,GAAGrB,GAAG,CAACsB,wBAAwB,CAACxzB,GAAG,CAAC,CAAC;YACpD,IAAI,CAACuzB,SAAS,EAAE;cACdD,UAAU,GAAG,KAAK;YACpB;UACF;UAEA,IAAIA,UAAU,EAAE;YACdjpB,GAAG,CAACa,GAAG,GAAG,IAAI,CAACqlB,UAAU,CAAC,CAAC;UAC7B;QACF;;QAGA;QACA;QACA,IAAIkD,qCAAqC,GAAG,SAAAA,CAAS5Z,MAAM,EAAE;UAC3D,IAAIzZ,MAAM,CAACszB,UAAU,CAAC7Z,MAAM,CAAC,EAAE,OAAOA,MAAM;UAE5C,IAAIxP,GAAG,CAACa,GAAG,EAAE;YACX,OAAOb,GAAG,CAACa,GAAG;UAChB;;UAEA;UACA;UACA;UACAb,GAAG,CAACa,GAAG,GAAG2O,MAAM;UAEhB,OAAOA,MAAM;QACf,CAAC;QAED,MAAMmc,eAAe,GAAGC,YAAY,CAClCnwB,QAAQ,EACR2tB,qCACF,CAAC;QAED,IAAI,IAAI,CAAChC,mBAAmB,CAAC,CAAC,EAAE;UAC9B,MAAM5X,MAAM,GAAG,IAAI,CAACqc,kBAAkB,CAAC,QAAQ,EAAE,CAAC7rB,GAAG,CAAC,EAAE2rB,eAAe,CAAC;UACxE,OAAOvC,qCAAqC,CAAC5Z,MAAM,CAAC;QACtD;;QAEA;QACA;QACA,IAAI;UACF;UACA;UACA;UACA,IAAIA,MAAM;UACV,IAAI,CAAC,CAACmc,eAAe,EAAE;YACrB,IAAI,CAACpF,WAAW,CAACwE,MAAM,CAAC/qB,GAAG,EAAE2rB,eAAe,CAAC;UAC/C,CAAC,MAAM;YACL;YACA;YACAnc,MAAM,GAAG,IAAI,CAAC+W,WAAW,CAACwE,MAAM,CAAC/qB,GAAG,CAAC;UACvC;UAEA,OAAOopB,qCAAqC,CAAC5Z,MAAM,CAAC;QACtD,CAAC,CAAC,OAAO7S,CAAC,EAAE;UACV,IAAIlB,QAAQ,EAAE;YACZA,QAAQ,CAACkB,CAAC,CAAC;YACX,OAAO,IAAI;UACb;UACA,MAAMA,CAAC;QACT;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEouB,MAAMA,CAAC/qB,GAAG,EAAEvE,QAAQ,EAAE;QACpB,OAAO,IAAI,CAACiwB,OAAO,CAAC1rB,GAAG,EAAEvE,QAAQ,CAAC;MACpC,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEivB,MAAMA,CAAClzB,QAAQ,EAAEmc,QAAQ,EAAyB;QAAA,SAAAmY,KAAA,GAAA3oB,SAAA,CAAAlG,MAAA,EAApB8uB,kBAAkB,OAAA1oB,KAAA,CAAAyoB,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;UAAlBD,kBAAkB,CAAAC,KAAA,QAAA7oB,SAAA,CAAA6oB,KAAA;QAAA;QAC9C,MAAMvwB,QAAQ,GAAGwwB,mBAAmB,CAACF,kBAAkB,CAAC;;QAExD;QACA;QACA,MAAMhoB,OAAO,GAAA5L,aAAA,KAAS4zB,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAG;QACtD,IAAIxS,UAAU;QACd,IAAIxV,OAAO,IAAIA,OAAO,CAACwW,MAAM,EAAE;UAC7B;UACA,IAAIxW,OAAO,CAACwV,UAAU,EAAE;YACtB,IACE,EACE,OAAOxV,OAAO,CAACwV,UAAU,KAAK,QAAQ,IACtCxV,OAAO,CAACwV,UAAU,YAAY7E,KAAK,CAACC,QAAQ,CAC7C,EAED,MAAM,IAAIjZ,KAAK,CAAC,uCAAuC,CAAC;YAC1D6d,UAAU,GAAGxV,OAAO,CAACwV,UAAU;UACjC,CAAC,MAAM,IAAI,CAAC/hB,QAAQ,IAAI,CAACA,QAAQ,CAACqJ,GAAG,EAAE;YACrC0Y,UAAU,GAAG,IAAI,CAAC2M,UAAU,CAAC,CAAC;YAC9BniB,OAAO,CAACkX,WAAW,GAAG,IAAI;YAC1BlX,OAAO,CAACwV,UAAU,GAAGA,UAAU;UACjC;QACF;QAEA/hB,QAAQ,GAAGkd,KAAK,CAACqB,UAAU,CAACC,gBAAgB,CAACxe,QAAQ,EAAE;UACrDyvB,UAAU,EAAE1N;QACd,CAAC,CAAC;QAEF,MAAMoS,eAAe,GAAGC,YAAY,CAACnwB,QAAQ,CAAC;QAE9C,IAAI,IAAI,CAAC2rB,mBAAmB,CAAC,CAAC,EAAE;UAC9B,MAAMhkB,IAAI,GAAG,CAAC5L,QAAQ,EAAEmc,QAAQ,EAAE5P,OAAO,CAAC;UAC1C,OAAO,IAAI,CAAC8nB,kBAAkB,CAAC,QAAQ,EAAEzoB,IAAI,EAAE3H,QAAQ,CAAC;QAC1D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI;UACF;UACA;UACA;UACA,OAAO,IAAI,CAAC8qB,WAAW,CAACmE,MAAM,CAC5BlzB,QAAQ,EACRmc,QAAQ,EACR5P,OAAO,EACP4nB,eACF,CAAC;QACH,CAAC,CAAC,OAAOhvB,CAAC,EAAE;UACV,IAAIlB,QAAQ,EAAE;YACZA,QAAQ,CAACkB,CAAC,CAAC;YACX,OAAO,IAAI;UACb;UACA,MAAMA,CAAC;QACT;MACF,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEiR,MAAMA,CAACpW,QAAQ,EAAEiE,QAAQ,EAAE;QACzBjE,QAAQ,GAAGkd,KAAK,CAACqB,UAAU,CAACC,gBAAgB,CAACxe,QAAQ,CAAC;QAEtD,IAAI,IAAI,CAAC4vB,mBAAmB,CAAC,CAAC,EAAE;UAC9B,OAAO,IAAI,CAACyE,kBAAkB,CAAC,QAAQ,EAAE,CAACr0B,QAAQ,CAAC,EAAEiE,QAAQ,CAAC;QAChE;;QAGA;QACA;QACA,OAAO,IAAI,CAAC8qB,WAAW,CAAC3Y,MAAM,CAACpW,QAAQ,CAAC;MAC1C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE+iB,MAAMA,CAAC/iB,QAAQ,EAAEmc,QAAQ,EAAE5P,OAAO,EAAEtI,QAAQ,EAAE;QAC5C,IAAI,CAACA,QAAQ,IAAI,OAAOsI,OAAO,KAAK,UAAU,EAAE;UAC9CtI,QAAQ,GAAGsI,OAAO;UAClBA,OAAO,GAAG,CAAC,CAAC;QACd;QAEA,OAAO,IAAI,CAAC2mB,MAAM,CAChBlzB,QAAQ,EACRmc,QAAQ,EAAAxb,aAAA,CAAAA,aAAA,KAEH4L,OAAO;UACVoX,aAAa,EAAE,IAAI;UACnBZ,MAAM,EAAE;QAAI,EACb,CAAC;MACN;IACF,CAAC;IAED;IACA,SAASqR,YAAYA,CAACnwB,QAAQ,EAAEywB,aAAa,EAAE;MAC7C,OACEzwB,QAAQ,IACR,UAAS+B,KAAK,EAAEgS,MAAM,EAAE;QACtB,IAAIhS,KAAK,EAAE;UACT/B,QAAQ,CAAC+B,KAAK,CAAC;QACjB,CAAC,MAAM,IAAI,OAAO0uB,aAAa,KAAK,UAAU,EAAE;UAC9CzwB,QAAQ,CAAC+B,KAAK,EAAE0uB,aAAa,CAAC1c,MAAM,CAAC,CAAC;QACxC,CAAC,MAAM;UACL/T,QAAQ,CAAC+B,KAAK,EAAEgS,MAAM,CAAC;QACzB;MACF,CAAC;IAEL;IAEA,SAASyc,mBAAmBA,CAAC7oB,IAAI,EAAE;MACjC;MACA;MACA,IACEA,IAAI,CAACnG,MAAM,KACVmG,IAAI,CAACA,IAAI,CAACnG,MAAM,GAAG,CAAC,CAAC,KAAKwF,SAAS,IAClCW,IAAI,CAACA,IAAI,CAACnG,MAAM,GAAG,CAAC,CAAC,YAAYsO,QAAQ,CAAC,EAC5C;QACA,OAAOnI,IAAI,CAAClD,GAAG,CAAC,CAAC;MACnB;IACF;IAACnI,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;ACzVD;;;;;;AAMAwc,KAAK,CAACyX,oBAAoB,GAAG,SAASA,oBAAoBA,CAAEpoB,OAAO;EACjE6B,KAAK,CAAC7B,OAAO,EAAErM,MAAM,CAAC;EACtBgd,KAAK,CAACuC,kBAAkB,GAAGlT,OAAO;AACpC,CAAC,C;;;;;;;;;;;;;;ICTD,IAAI5L,aAAa;IAAC3C,MAAM,CAACb,IAAI,CAAC,sCAAsC,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAACuD,aAAa,GAACvD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI8M,wBAAwB;IAAClM,MAAM,CAACb,IAAI,CAAC,gDAAgD,EAAC;MAACyD,OAAOA,CAACxD,CAAC,EAAC;QAAC8M,wBAAwB,GAAC9M,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAAC,MAAA2M,SAAA;IAAzSnM,MAAM,CAACjB,MAAM,CAAC;MAAC6wB,mBAAmB,EAACA,CAAA,KAAIA;IAAmB,CAAC,CAAC;IAArD,MAAMA,mBAAmB,GAAGrhB,OAAO,IAAI;MAC5C;MACA,MAAAlC,IAAA,GAAgDkC,OAAO,IAAI,CAAC,CAAC;QAAvD;UAAEmB,MAAM;UAAE3I;QAA4B,CAAC,GAAAsF,IAAA;QAAduqB,YAAY,GAAA1qB,wBAAA,CAAAG,IAAA,EAAAF,SAAA;MAC3C;MACA;;MAEA,OAAAxJ,aAAA,CAAAA,aAAA,KACKi0B,YAAY,GACX7vB,UAAU,IAAI2I,MAAM,GAAG;QAAE3I,UAAU,EAAE2I,MAAM,IAAI3I;MAAW,CAAC,GAAG,CAAC,CAAC;IAExE,CAAC;IAACxE,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;ACRF1C,MAAI,CAAAjB,MAAA;EAAAiiB,aAAwB,EAAAA,CAAA,KAAAA;AAAA;AAA5B,IAAI6V,mBAAmB,GAAG,CAAC;AAO3B;;;;;AAKM,MAAO7V,aAAa;EAexBvd,YAAYoO,WAA+B,EAAEtB,SAAqD,EAAEpB,oBAA6B;IAAA,KAdjI9D,GAAG;IAAA,KACH8F,YAAY;IAAA,KACZhC,oBAAoB;IAAA,KACpBlL,QAAQ;IAAA,KAED4L,uBAAuB,GAA0B,MAAK,CAAE,CAAC;IAAA,KACzDX,eAAe;IAAA,KAEtBI,MAAM;IAAA,KACND,YAAY;IAAA,KACZynB,QAAQ;IAAA,KACRC,YAAY;IAAA,KACZC,QAAQ;IAqCR;;;IAAA,KAGA11B,IAAI,GAAG,YAAW;MAChB,IAAI,IAAI,CAAC2C,QAAQ,EAAE;MACnB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,MAAM,IAAI,CAACkN,YAAY,CAAC/C,YAAY,CAAC,IAAI,CAAC/C,GAAG,CAAC;IAChD,CAAC;IAzCC,IAAI,CAAC8F,YAAY,GAAGU,WAAW;IAE/BA,WAAW,CAACrE,aAAa,EAAE,CAACjM,OAAO,CAAEma,IAA2B,IAAI;MAClE,IAAInL,SAAS,CAACmL,IAAI,CAAC,EAAE;QACnB,IAAI,KAAAtR,MAAA,CAAKsR,IAAI,EAAoC,GAAGnL,SAAS,CAACmL,IAAI,CAAC;QACnE;MACF;MAEA,IAAIA,IAAI,KAAK,aAAa,IAAInL,SAAS,CAACuH,KAAK,EAAE;QAC7C,IAAI,CAACzI,YAAY,GAAG,gBAAgBpN,EAAE,EAAEyN,MAAM,EAAEunB,MAAM;UACpD,MAAM1mB,SAAS,CAACuH,KAAK,CAAC7V,EAAE,EAAEyN,MAAM,CAAC;QACnC,CAAC;MACH;IACF,CAAC,CAAC;IAEF,IAAI,CAACzL,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACoH,GAAG,GAAGwrB,mBAAmB,EAAE;IAChC,IAAI,CAAC1nB,oBAAoB,GAAGA,oBAAoB;IAEhD,IAAI,CAACD,eAAe,GAAG,IAAIlK,OAAO,CAACgH,OAAO,IAAG;MAC3C,MAAMyC,KAAK,GAAGA,CAAA,KAAK;QACjBzC,OAAO,EAAE;QACT,IAAI,CAACkD,eAAe,GAAGlK,OAAO,CAACgH,OAAO,EAAE;MAC1C,CAAC;MAED,MAAMkrB,OAAO,GAAGpvB,UAAU,CAAC2G,KAAK,EAAE,KAAK,CAAC;MAExC,IAAI,CAACoB,uBAAuB,GAAG,MAAK;QAClCpB,KAAK,EAAE;QACP5G,YAAY,CAACqvB,OAAO,CAAC;MACvB,CAAC;IACH,CAAC,CAAC;EACJ", "file": "/packages/mongo.js", "sourcesContent": ["import { OplogHandle } from './oplog_tailing';\nimport { MongoConnection } from './mongo_connection';\nimport { OplogObserveDriver } from './oplog_observe_driver';\nimport { MongoDB } from './mongo_common';\n\nMongoInternals = global.MongoInternals = {};\n\nMongoInternals.__packageName = 'mongo';\n\nMongoInternals.NpmModules = {\n  mongodb: {\n    version: NpmModuleMongodbVersion,\n    module: MongoDB\n  }\n};\n\n// Older version of what is now available via\n// MongoInternals.NpmModules.mongodb.module.  It was never documented, but\n// people do use it.\n// XXX COMPAT WITH 1.0.3.2\nMongoInternals.NpmModule = new Proxy(MongoDB, {\n  get(target, propertyKey, receiver) {\n    if (propertyKey === 'ObjectID') {\n      Meteor.deprecate(\n        `Accessing 'MongoInternals.NpmModule.ObjectID' directly is deprecated. ` +\n        `Use 'MongoInternals.NpmModule.ObjectId' instead.`\n      );\n    }\n    return Reflect.get(target, propertyKey, receiver);\n  },\n});\n\nMongoInternals.OplogHandle = OplogHandle;\n\nMongoInternals.Connection = MongoConnection;\n\nMongoInternals.OplogObserveDriver = OplogObserveDriver;\n\n// This is used to add or remove EJSON from the beginning of everything nested\n// inside an EJSON custom type. It should only be called on pure JSON!\n\n\n// Ensure that EJSON.clone keeps a Timestamp as a Timestamp (instead of just\n// doing a structural clone).\n// XXX how ok is this? what if there are multiple copies of MongoDB loaded?\nMongoDB.Timestamp.prototype.clone = function () {\n  // Timestamps should be immutable.\n  return this;\n};\n\n// Listen for the invalidation messages that will trigger us to poll the\n// database for changes. If this selector specifies specific IDs, specify them\n// here, so that updates to different specific IDs don't cause us to poll.\n// listenCallback is the same kind of (notification, complete) callback passed\n// to InvalidationCrossbar.listen.\n\nexport const listenAll = async function (cursorDescription, listenCallback) {\n  const listeners = [];\n  await forEachTrigger(cursorDescription, function (trigger) {\n    listeners.push(DDPServer._InvalidationCrossbar.listen(\n      trigger, listenCallback));\n  });\n\n  return {\n    stop: function () {\n      listeners.forEach(function (listener) {\n        listener.stop();\n      });\n    }\n  };\n};\n\nexport const forEachTrigger = async function (cursorDescription, triggerCallback) {\n  const key = {collection: cursorDescription.collectionName};\n  const specificIds = LocalCollection._idsMatchedBySelector(\n    cursorDescription.selector);\n  if (specificIds) {\n    for (const id of specificIds) {\n      await triggerCallback(Object.assign({id: id}, key));\n    }\n    await triggerCallback(Object.assign({dropCollection: true, id: null}, key));\n  } else {\n    await triggerCallback(key);\n  }\n  // Everyone cares about the database being dropped.\n  await triggerCallback({ dropDatabase: true });\n};\n\n\n\n// XXX We probably need to find a better way to expose this. Right now\n// it's only used by tests, but in fact you need it in normal\n// operation to interact with capped collections.\nMongoInternals.MongoTimestamp = MongoDB.Timestamp;\n", "import isEmpty from 'lodash.isempty';\nimport { Meteor } from 'meteor/meteor';\nimport { CursorDescription } from './cursor_description';\nimport { MongoConnection } from './mongo_connection';\n\nimport { NpmModuleMongodb } from \"meteor/npm-mongo\";\nconst { Long } = NpmModuleMongodb;\n\nexport const OPLOG_COLLECTION = 'oplog.rs';\n\nlet TOO_FAR_BEHIND = +(process.env.METEOR_OPLOG_TOO_FAR_BEHIND || 2000);\nconst TAIL_TIMEOUT = +(process.env.METEOR_OPLOG_TAIL_TIMEOUT || 30000);\n\nexport interface OplogEntry {\n  op: string;\n  o: any;\n  o2?: any;\n  ts: any;\n  ns: string;\n}\n\nexport interface CatchingUpResolver {\n  ts: any;\n  resolver: () => void;\n}\n\nexport interface OplogTrigger {\n  dropCollection: boolean;\n  dropDatabase: boolean;\n  op: OplogEntry;\n  collection?: string;\n  id?: string | null;\n}\n\nexport class OplogHandle {\n  private _oplogUrl: string;\n  public _dbName: string;\n  private _oplogLastEntryConnection: MongoConnection | null;\n  private _oplogTailConnection: MongoConnection | null;\n  private _oplogOptions: { excludeCollections?: string[]; includeCollections?: string[] } | null;\n  private _stopped: boolean;\n  private _tailHandle: any;\n  private _readyPromiseResolver: (() => void) | null;\n  private _readyPromise: Promise<void>;\n  public _crossbar: any;\n  private _baseOplogSelector: any;\n  private _catchingUpResolvers: CatchingUpResolver[];\n  private _lastProcessedTS: any;\n  private _onSkippedEntriesHook: any;\n  private _startTrailingPromise: Promise<void>;\n  private _resolveTimeout: any;\n\n  private _entryQueue = new Meteor._DoubleEndedQueue();\n  private _workerActive = false;\n  private _workerPromise: Promise<void> | null = null;\n\n  constructor(oplogUrl: string, dbName: string) {\n    this._oplogUrl = oplogUrl;\n    this._dbName = dbName;\n\n    this._resolveTimeout = null;\n    this._oplogLastEntryConnection = null;\n    this._oplogTailConnection = null;\n    this._oplogOptions = null;\n    this._stopped = false;\n    this._tailHandle = null;\n    this._readyPromiseResolver = null;\n    this._readyPromise = new Promise(r => this._readyPromiseResolver = r);\n    this._crossbar = new DDPServer._Crossbar({\n      factPackage: \"mongo-livedata\", factName: \"oplog-watchers\"\n    });\n    this._baseOplogSelector = {\n      ns: new RegExp(\"^(?:\" + [\n        // @ts-ignore\n        Meteor._escapeRegExp(this._dbName + \".\"),\n        // @ts-ignore\n        Meteor._escapeRegExp(\"admin.$cmd\"),\n      ].join(\"|\") + \")\"),\n\n      $or: [\n        { op: { $in: ['i', 'u', 'd'] } },\n        { op: 'c', 'o.drop': { $exists: true } },\n        { op: 'c', 'o.dropDatabase': 1 },\n        { op: 'c', 'o.applyOps': { $exists: true } },\n      ]\n    };\n\n    this._catchingUpResolvers = [];\n    this._lastProcessedTS = null;\n\n    this._onSkippedEntriesHook = new Hook({\n      debugPrintExceptions: \"onSkippedEntries callback\"\n    });\n\n    this._startTrailingPromise = this._startTailing();\n  }\n\n  async stop(): Promise<void> {\n    if (this._stopped) return;\n    this._stopped = true;\n    if (this._tailHandle) {\n      await this._tailHandle.stop();\n    }\n  }\n\n  async _onOplogEntry(trigger: OplogTrigger, callback: Function): Promise<{ stop: () => Promise<void> }> {\n    if (this._stopped) {\n      throw new Error(\"Called onOplogEntry on stopped handle!\");\n    }\n\n    await this._readyPromise;\n\n    const originalCallback = callback;\n\n    /**\n     * This depends on AsynchronousQueue tasks being wrapped in `bindEnvironment` too.\n     *\n     * @todo Check after we simplify the `bindEnvironment` implementation if we can remove the second wrap.\n     */\n    callback = Meteor.bindEnvironment(\n      function (notification: any) {\n        originalCallback(notification);\n      },\n      // @ts-ignore\n      function (err) {\n        Meteor._debug(\"Error in oplog callback\", err);\n      }\n    );\n\n    const listenHandle = this._crossbar.listen(trigger, callback);\n    return {\n      stop: async function () {\n        await listenHandle.stop();\n      }\n    };\n  }\n\n  onOplogEntry(trigger: OplogTrigger, callback: Function): Promise<{ stop: () => Promise<void> }> {\n    return this._onOplogEntry(trigger, callback);\n  }\n\n  onSkippedEntries(callback: Function): { stop: () => void } {\n    if (this._stopped) {\n      throw new Error(\"Called onSkippedEntries on stopped handle!\");\n    }\n    return this._onSkippedEntriesHook.register(callback);\n  }\n\n  async _waitUntilCaughtUp(): Promise<void> {\n    if (this._stopped) {\n      throw new Error(\"Called waitUntilCaughtUp on stopped handle!\");\n    }\n\n    await this._readyPromise;\n\n    let lastEntry: OplogEntry | null = null;\n\n    while (!this._stopped) {\n      try {\n        lastEntry = await this._oplogLastEntryConnection.findOneAsync(\n          OPLOG_COLLECTION,\n          this._baseOplogSelector,\n          { projection: { ts: 1 }, sort: { $natural: -1 } }\n        );\n        break;\n      } catch (e) {\n        Meteor._debug(\"Got exception while reading last entry\", e);\n        // @ts-ignore\n        await Meteor.sleep(100);\n      }\n    }\n\n    if (this._stopped) return;\n\n    if (!lastEntry) return;\n\n    const ts = lastEntry.ts;\n    if (!ts) {\n      throw Error(\"oplog entry without ts: \" + JSON.stringify(lastEntry));\n    }\n\n    if (this._lastProcessedTS && ts.lessThanOrEqual(this._lastProcessedTS)) {\n      return;\n    }\n\n    let insertAfter = this._catchingUpResolvers.length;\n\n    while (insertAfter - 1 > 0 && this._catchingUpResolvers[insertAfter - 1].ts.greaterThan(ts)) {\n      insertAfter--;\n    }\n\n    let promiseResolver = null;\n\n    const promiseToAwait = new Promise(r => promiseResolver = r);\n\n    clearTimeout(this._resolveTimeout);\n\n    this._resolveTimeout = setTimeout(() => {\n      console.error(\"Meteor: oplog catching up took too long\", { ts });\n    }, 10000);\n\n    this._catchingUpResolvers.splice(insertAfter, 0, { ts, resolver: promiseResolver! });\n\n    await promiseToAwait;\n\n    clearTimeout(this._resolveTimeout);\n  }\n\n  async waitUntilCaughtUp(): Promise<void> {\n    return this._waitUntilCaughtUp();\n  }\n\n  async _startTailing(): Promise<void> {\n    const mongodbUri = require('mongodb-uri');\n    if (mongodbUri.parse(this._oplogUrl).database !== 'local') {\n      throw new Error(\"$MONGO_OPLOG_URL must be set to the 'local' database of a Mongo replica set\");\n    }\n\n    this._oplogTailConnection = new MongoConnection(\n      this._oplogUrl, { maxPoolSize: 1, minPoolSize: 1 }\n    );\n    this._oplogLastEntryConnection = new MongoConnection(\n      this._oplogUrl, { maxPoolSize: 1, minPoolSize: 1 }\n    );\n\n    try {\n      const isMasterDoc = await this._oplogLastEntryConnection!.db\n        .admin()\n        .command({ ismaster: 1 });\n\n      if (!(isMasterDoc && isMasterDoc.setName)) {\n        throw new Error(\"$MONGO_OPLOG_URL must be set to the 'local' database of a Mongo replica set\");\n      }\n\n      const lastOplogEntry = await this._oplogLastEntryConnection.findOneAsync(\n        OPLOG_COLLECTION,\n        {},\n        { sort: { $natural: -1 }, projection: { ts: 1 } }\n      );\n\n      let oplogSelector: any = { ...this._baseOplogSelector };\n      if (lastOplogEntry) {\n        oplogSelector.ts = { $gt: lastOplogEntry.ts };\n        this._lastProcessedTS = lastOplogEntry.ts;\n      }\n\n      const includeCollections = Meteor.settings?.packages?.mongo?.oplogIncludeCollections;\n      const excludeCollections = Meteor.settings?.packages?.mongo?.oplogExcludeCollections;\n\n      if (includeCollections?.length && excludeCollections?.length) {\n        throw new Error(\"Can't use both mongo oplog settings oplogIncludeCollections and oplogExcludeCollections at the same time.\");\n      }\n\n      if (excludeCollections?.length) {\n        oplogSelector.ns = {\n          $regex: oplogSelector.ns,\n          $nin: excludeCollections.map((collName: string) => `${this._dbName}.${collName}`)\n        };\n        this._oplogOptions = { excludeCollections };\n      } else if (includeCollections?.length) {\n        oplogSelector = {\n          $and: [\n            {\n              $or: [\n                { ns: /^admin\\.\\$cmd/ },\n                { ns: { $in: includeCollections.map((collName: string) => `${this._dbName}.${collName}`) } }\n              ]\n            },\n            { $or: oplogSelector.$or },\n            { ts: oplogSelector.ts }\n          ]\n        };\n        this._oplogOptions = { includeCollections };\n      }\n\n      const cursorDescription = new CursorDescription(\n        OPLOG_COLLECTION,\n        oplogSelector,\n        { tailable: true }\n      );\n\n      this._tailHandle = this._oplogTailConnection.tail(\n        cursorDescription,\n        (doc: any) => {\n          this._entryQueue.push(doc);\n          this._maybeStartWorker();\n        },\n        TAIL_TIMEOUT\n      );\n\n      this._readyPromiseResolver!();\n    } catch (error) {\n      console.error('Error in _startTailing:', error);\n      throw error;\n    }\n  }\n\n  private _maybeStartWorker(): void {\n    if (this._workerPromise) return;\n    this._workerActive = true;\n\n    // Convert to a proper promise-based queue processor\n    this._workerPromise = (async () => {\n      try {\n        while (!this._stopped && !this._entryQueue.isEmpty()) {\n          // Are we too far behind? Just tell our observers that they need to\n          // repoll, and drop our queue.\n          if (this._entryQueue.length > TOO_FAR_BEHIND) {\n            const lastEntry = this._entryQueue.pop();\n            this._entryQueue.clear();\n\n            this._onSkippedEntriesHook.each((callback: Function) => {\n              callback();\n              return true;\n            });\n\n            // Free any waitUntilCaughtUp() calls that were waiting for us to\n            // pass something that we just skipped.\n            this._setLastProcessedTS(lastEntry.ts);\n            continue;\n          }\n\n          // Process next batch from the queue\n          const doc = this._entryQueue.shift();\n\n          try {\n            await handleDoc(this, doc);\n            // Process any waiting fence callbacks\n            if (doc.ts) {\n              this._setLastProcessedTS(doc.ts);\n            }\n          } catch (e) {\n            // Keep processing queue even if one entry fails\n            console.error('Error processing oplog entry:', e);\n          }\n        }\n      } finally {\n        this._workerPromise = null;\n        this._workerActive = false;\n      }\n    })();\n  }\n\n  _setLastProcessedTS(ts: any): void {\n    this._lastProcessedTS = ts;\n    while (!isEmpty(this._catchingUpResolvers) && this._catchingUpResolvers[0].ts.lessThanOrEqual(this._lastProcessedTS)) {\n      const sequencer = this._catchingUpResolvers.shift()!;\n      sequencer.resolver();\n    }\n  }\n\n  _defineTooFarBehind(value: number): void {\n    TOO_FAR_BEHIND = value;\n  }\n\n  _resetTooFarBehind(): void {\n    TOO_FAR_BEHIND = +(process.env.METEOR_OPLOG_TOO_FAR_BEHIND || 2000);\n  }\n}\n\nexport function idForOp(op: OplogEntry): string {\n  if (op.op === 'd' || op.op === 'i') {\n    return op.o._id;\n  } else if (op.op === 'u') {\n    return op.o2._id;\n  } else if (op.op === 'c') {\n    throw Error(\"Operator 'c' doesn't supply an object with id: \" + JSON.stringify(op));\n  } else {\n    throw Error(\"Unknown op: \" + JSON.stringify(op));\n  }\n}\n\nasync function handleDoc(handle: OplogHandle, doc: OplogEntry): Promise<void> {\n  if (doc.ns === \"admin.$cmd\") {\n    if (doc.o.applyOps) {\n      // This was a successful transaction, so we need to apply the\n      // operations that were involved.\n      let nextTimestamp = doc.ts;\n      for (const op of doc.o.applyOps) {\n        // See https://github.com/meteor/meteor/issues/10420.\n        if (!op.ts) {\n          op.ts = nextTimestamp;\n          nextTimestamp = nextTimestamp.add(Long.ONE);\n        }\n        await handleDoc(handle, op);\n      }\n      return;\n    }\n    throw new Error(\"Unknown command \" + JSON.stringify(doc));\n  }\n\n  const trigger: OplogTrigger = {\n    dropCollection: false,\n    dropDatabase: false,\n    op: doc,\n  };\n\n  if (typeof doc.ns === \"string\" && doc.ns.startsWith(handle._dbName + \".\")) {\n    trigger.collection = doc.ns.slice(handle._dbName.length + 1);\n  }\n\n  // Is it a special command and the collection name is hidden\n  // somewhere in operator?\n  if (trigger.collection === \"$cmd\") {\n    if (doc.o.dropDatabase) {\n      delete trigger.collection;\n      trigger.dropDatabase = true;\n    } else if (\"drop\" in doc.o) {\n      trigger.collection = doc.o.drop;\n      trigger.dropCollection = true;\n      trigger.id = null;\n    } else if (\"create\" in doc.o && \"idIndex\" in doc.o) {\n      // A collection got implicitly created within a transaction. There's\n      // no need to do anything about it.\n    } else {\n      throw Error(\"Unknown command \" + JSON.stringify(doc));\n    }\n  } else {\n    // All other ops have an id.\n    trigger.id = idForOp(doc);\n  }\n\n  await handle._crossbar.fire(trigger);\n\n  await new Promise(resolve => setImmediate(resolve));\n}", "import isEmpty from 'lodash.isempty';\nimport { ObserveHandle } from './observe_handle';\n\ninterface ObserveMultiplexerOptions {\n  ordered: boolean;\n  onStop?: () => void;\n}\n\nexport type ObserveHandleCallback = 'added' | 'addedBefore' | 'changed' | 'movedBefore' | 'removed';\n\n/**\n * Allows multiple identical ObserveHandles to be driven by a single observe driver.\n *\n * This optimization ensures that multiple identical observations\n * don't result in duplicate database queries.\n */\nexport class ObserveMultiplexer {\n  private readonly _ordered: boolean;\n  private readonly _onStop: () => void;\n  private _queue: any;\n  private _handles: { [key: string]: ObserveHandle } | null;\n  private _resolver: ((value?: unknown) => void) | null;\n  private readonly _readyPromise: Promise<boolean | void>;\n  private _isReady: boolean;\n  private _cache: any;\n  private _addHandleTasksScheduledButNotPerformed: number;\n\n  constructor({ ordered, onStop = () => {} }: ObserveMultiplexerOptions) {\n    if (ordered === undefined) throw Error(\"must specify ordered\");\n\n    // @ts-ignore\n    Package['facts-base'] && Package['facts-base']\n        .Facts.incrementServerFact(\"mongo-livedata\", \"observe-multiplexers\", 1);\n\n    this._ordered = ordered;\n    this._onStop = onStop;\n    this._queue = new Meteor._AsynchronousQueue();\n    this._handles = {};\n    this._resolver = null;\n    this._isReady = false;\n    this._readyPromise = new Promise(r => this._resolver = r).then(() => this._isReady = true);\n    // @ts-ignore\n    this._cache = new LocalCollection._CachingChangeObserver({ ordered });\n    this._addHandleTasksScheduledButNotPerformed = 0;\n\n    this.callbackNames().forEach(callbackName => {\n      (this as any)[callbackName] = (...args: any[]) => {\n        this._applyCallback(callbackName, args);\n      };\n    });\n  }\n\n  addHandleAndSendInitialAdds(handle: ObserveHandle): Promise<void> {\n    return this._addHandleAndSendInitialAdds(handle);\n  }\n\n  async _addHandleAndSendInitialAdds(handle: ObserveHandle): Promise<void> {\n    ++this._addHandleTasksScheduledButNotPerformed;\n\n    // @ts-ignore\n    Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n      \"mongo-livedata\", \"observe-handles\", 1);\n\n    await this._queue.runTask(async () => {\n      this._handles![handle._id] = handle;\n      await this._sendAdds(handle);\n      --this._addHandleTasksScheduledButNotPerformed;\n    });\n    await this._readyPromise;\n  }\n\n  async removeHandle(id: number): Promise<void> {\n    if (!this._ready())\n      throw new Error(\"Can't remove handles until the multiplex is ready\");\n\n    delete this._handles![id];\n\n    // @ts-ignore\n    Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n      \"mongo-livedata\", \"observe-handles\", -1);\n\n    if (isEmpty(this._handles) &&\n      this._addHandleTasksScheduledButNotPerformed === 0) {\n      await this._stop();\n    }\n  }\n\n  async _stop(options: { fromQueryError?: boolean } = {}): Promise<void> {\n    if (!this._ready() && !options.fromQueryError)\n      throw Error(\"surprising _stop: not ready\");\n\n    await this._onStop();\n\n    // @ts-ignore\n    Package['facts-base'] && Package['facts-base']\n        .Facts.incrementServerFact(\"mongo-livedata\", \"observe-multiplexers\", -1);\n\n    this._handles = null;\n  }\n\n  async ready(): Promise<void> {\n    await this._queue.queueTask(() => {\n      if (this._ready())\n        throw Error(\"can't make ObserveMultiplex ready twice!\");\n\n      if (!this._resolver) {\n        throw new Error(\"Missing resolver\");\n      }\n\n      this._resolver();\n      this._isReady = true;\n    });\n  }\n\n  async queryError(err: Error): Promise<void> {\n    await this._queue.runTask(() => {\n      if (this._ready())\n        throw Error(\"can't claim query has an error after it worked!\");\n      this._stop({ fromQueryError: true });\n      throw err;\n    });\n  }\n\n  async onFlush(cb: () => void): Promise<void> {\n    await this._queue.queueTask(async () => {\n      if (!this._ready())\n        throw Error(\"only call onFlush on a multiplexer that will be ready\");\n      await cb();\n    });\n  }\n\n  callbackNames(): ObserveHandleCallback[] {\n    return this._ordered\n      ? [\"addedBefore\", \"changed\", \"movedBefore\", \"removed\"]\n      : [\"added\", \"changed\", \"removed\"];\n  }\n\n  _ready(): boolean {\n    return !!this._isReady;\n  }\n\n  _applyCallback(callbackName: string, args: any[]) {\n    this._queue.queueTask(async () => {\n      if (!this._handles) return;\n\n      await this._cache.applyChange[callbackName].apply(null, args);\n      if (!this._ready() &&\n        (callbackName !== 'added' && callbackName !== 'addedBefore')) {\n        throw new Error(`Got ${callbackName} during initial adds`);\n      }\n\n      for (const handleId of Object.keys(this._handles)) {\n        const handle = this._handles && this._handles[handleId];\n\n        if (!handle) return;\n\n        const callback = (handle as any)[`_${callbackName}`];\n\n        if (!callback) continue;\n\n        handle.initialAddsSent.then(callback.apply(\n          null,\n          handle.nonMutatingCallbacks ? args : EJSON.clone(args)\n        ))\n      }\n    });\n  }\n\n  async _sendAdds(handle: ObserveHandle): Promise<void> {\n    const add = this._ordered ? handle._addedBefore : handle._added;\n    if (!add) return;\n\n    const addPromises: Promise<void>[] = [];\n\n    this._cache.docs.forEach((doc: any, id: string) => {\n      if (!(handle._id in this._handles!)) {\n        throw Error(\"handle got removed before sending initial adds!\");\n      }\n\n      const { _id, ...fields } = handle.nonMutatingCallbacks ? doc : EJSON.clone(doc);\n\n      const promise = this._ordered ?\n        add(id, fields, null) :\n        add(id, fields);\n\n      addPromises.push(promise);\n    });\n\n    await Promise.all(addPromises);\n\n    handle.initialAddsSentResolver();\n  }\n}", "export class DocFetcher {\n  constructor(mongoConnection) {\n    this._mongoConnection = mongoConnection;\n    // Map from op -> [callback]\n    this._callbacksForOp = new Map();\n  }\n\n  // Fetches document \"id\" from collectionName, returning it or null if not\n  // found.\n  //\n  // If you make multiple calls to fetch() with the same op reference,\n  // DocFetcher may assume that they all return the same document. (It does\n  // not check to see if collectionName/id match.)\n  //\n  // You may assume that callback is never called synchronously (and in fact\n  // OplogObserveDriver does so).\n  async fetch(collectionName, id, op, callback) {\n    const self = this;\n\n    \n    check(collectionName, String);\n    check(op, Object);\n\n\n    // If there's already an in-progress fetch for this cache key, yield until\n    // it's done and return whatever it returns.\n    if (self._callbacksForOp.has(op)) {\n      self._callbacksForOp.get(op).push(callback);\n      return;\n    }\n\n    const callbacks = [callback];\n    self._callbacksForOp.set(op, callbacks);\n\n    try {\n      var doc =\n        (await self._mongoConnection.findOneAsync(collectionName, {\n          _id: id,\n        })) || null;\n      // Return doc to all relevant callbacks. Note that this array can\n      // continue to grow during callback excecution.\n      while (callbacks.length > 0) {\n        // Clone the document so that the various calls to fetch don't return\n        // objects that are intertwingled with each other. Clone before\n        // popping the future, so that if clone throws, the error gets passed\n        // to the next callback.\n        callbacks.pop()(null, EJSON.clone(doc));\n      }\n    } catch (e) {\n      while (callbacks.length > 0) {\n        callbacks.pop()(e);\n      }\n    } finally {\n      // XXX consider keeping the doc around for a period of time before\n      // removing from the cache\n      self._callbacksForOp.delete(op);\n    }\n  }\n}\n", "import throttle from 'lodash.throttle';\nimport { listenAll } from './mongo_driver';\nimport { ObserveMultiplexer } from './observe_multiplex';\n\ninterface PollingObserveDriverOptions {\n  cursorDescription: any;\n  mongoHandle: any;\n  ordered: boolean;\n  multiplexer: ObserveMultiplexer;\n  _testOnlyPollCallback?: () => void;\n}\n\nconst POLLING_THROTTLE_MS = +(process.env.METEOR_POLLING_THROTTLE_MS || '') || 50;\nconst POLLING_INTERVAL_MS = +(process.env.METEOR_POLLING_INTERVAL_MS || '') || 10 * 1000;\n\n/**\n * @class PollingObserveDriver\n *\n * One of two observe driver implementations.\n *\n * Characteristics:\n * - Caches the results of a query\n * - Reruns the query when necessary\n * - Suitable for cases where oplog tailing is not available or practical\n */\nexport class PollingObserveDriver {\n  private _options: PollingObserveDriverOptions;\n  private _cursorDescription: any;\n  private _mongoHandle: any;\n  private _ordered: boolean;\n  private _multiplexer: any;\n  private _stopCallbacks: Array<() => Promise<void>>;\n  private _stopped: boolean;\n  private _cursor: any;\n  private _results: any;\n  private _pollsScheduledButNotStarted: number;\n  private _pendingWrites: any[];\n  private _ensurePollIsScheduled: Function;\n  private _taskQueue: any;\n  private _testOnlyPollCallback?: () => void;\n\n  constructor(options: PollingObserveDriverOptions) {\n    this._options = options;\n    this._cursorDescription = options.cursorDescription;\n    this._mongoHandle = options.mongoHandle;\n    this._ordered = options.ordered;\n    this._multiplexer = options.multiplexer;\n    this._stopCallbacks = [];\n    this._stopped = false;\n\n    this._cursor = this._mongoHandle._createAsynchronousCursor(\n      this._cursorDescription);\n\n    this._results = null;\n    this._pollsScheduledButNotStarted = 0;\n    this._pendingWrites = [];\n\n    this._ensurePollIsScheduled = throttle(\n      this._unthrottledEnsurePollIsScheduled.bind(this),\n      this._cursorDescription.options.pollingThrottleMs || POLLING_THROTTLE_MS\n    );\n\n    this._taskQueue = new (Meteor as any)._AsynchronousQueue();\n  }\n\n  async _init(): Promise<void> {\n    const options = this._options;\n    const listenersHandle = await listenAll(\n      this._cursorDescription,\n      (notification: any) => {\n        const fence = (DDPServer as any)._getCurrentFence();\n        if (fence) {\n          this._pendingWrites.push(fence.beginWrite());\n        }\n        if (this._pollsScheduledButNotStarted === 0) {\n          this._ensurePollIsScheduled();\n        }\n      }\n    );\n\n    this._stopCallbacks.push(async () => { await listenersHandle.stop(); });\n\n    if (options._testOnlyPollCallback) {\n      this._testOnlyPollCallback = options._testOnlyPollCallback;\n    } else {\n      const pollingInterval =\n        this._cursorDescription.options.pollingIntervalMs ||\n        this._cursorDescription.options._pollingInterval ||\n        POLLING_INTERVAL_MS;\n\n      const intervalHandle = Meteor.setInterval(\n        this._ensurePollIsScheduled.bind(this),\n        pollingInterval\n      );\n\n      this._stopCallbacks.push(() => {\n        Meteor.clearInterval(intervalHandle);\n      });\n    }\n\n    await this._unthrottledEnsurePollIsScheduled();\n\n    (Package['facts-base'] as any)?.Facts.incrementServerFact(\n      \"mongo-livedata\", \"observe-drivers-polling\", 1);\n  }\n\n  async _unthrottledEnsurePollIsScheduled(): Promise<void> {\n    if (this._pollsScheduledButNotStarted > 0) return;\n    ++this._pollsScheduledButNotStarted;\n    await this._taskQueue.runTask(async () => {\n      await this._pollMongo();\n    });\n  }\n\n  _suspendPolling(): void {\n    ++this._pollsScheduledButNotStarted;\n    this._taskQueue.runTask(() => {});\n\n    if (this._pollsScheduledButNotStarted !== 1) {\n      throw new Error(`_pollsScheduledButNotStarted is ${this._pollsScheduledButNotStarted}`);\n    }\n  }\n\n  async _resumePolling(): Promise<void> {\n    if (this._pollsScheduledButNotStarted !== 1) {\n      throw new Error(`_pollsScheduledButNotStarted is ${this._pollsScheduledButNotStarted}`);\n    }\n    await this._taskQueue.runTask(async () => {\n      await this._pollMongo();\n    });\n  }\n\n  async _pollMongo(): Promise<void> {\n    --this._pollsScheduledButNotStarted;\n\n    if (this._stopped) return;\n\n    let first = false;\n    let newResults;\n    let oldResults = this._results;\n\n    if (!oldResults) {\n      first = true;\n      oldResults = this._ordered ? [] : new (LocalCollection as any)._IdMap;\n    }\n\n    this._testOnlyPollCallback?.();\n\n    const writesForCycle = this._pendingWrites;\n    this._pendingWrites = [];\n\n    try {\n      newResults = await this._cursor.getRawObjects(this._ordered);\n    } catch (e: any) {\n      if (first && typeof(e.code) === 'number') {\n        await this._multiplexer.queryError(\n          new Error(\n            `Exception while polling query ${\n              JSON.stringify(this._cursorDescription)\n            }: ${e.message}`\n          )\n        );\n      }\n\n      Array.prototype.push.apply(this._pendingWrites, writesForCycle);\n      Meteor._debug(`Exception while polling query ${\n        JSON.stringify(this._cursorDescription)}`, e);\n      return;\n    }\n\n    if (!this._stopped) {\n      (LocalCollection as any)._diffQueryChanges(\n        this._ordered, oldResults, newResults, this._multiplexer);\n    }\n\n    if (first) this._multiplexer.ready();\n\n    this._results = newResults;\n\n    await this._multiplexer.onFlush(async () => {\n      for (const w of writesForCycle) {\n        await w.committed();\n      }\n    });\n  }\n\n  async stop(): Promise<void> {\n    this._stopped = true;\n\n    for (const callback of this._stopCallbacks) {\n      await callback();\n    }\n\n    for (const w of this._pendingWrites) {\n      await w.committed();\n    }\n\n    (Package['facts-base'] as any)?.Facts.incrementServerFact(\n      \"mongo-livedata\", \"observe-drivers-polling\", -1);\n  }\n}", "import has from 'lodash.has';\nimport isEmpty from 'lodash.isempty';\nimport { oplogV2V1Converter } from \"./oplog_v2_converter\";\nimport { check, Match } from 'meteor/check';\nimport { CursorDescription } from './cursor_description';\nimport { forEachTrigger, listenAll } from './mongo_driver';\nimport { Cursor } from './cursor';\nimport LocalCollection from 'meteor/minimongo/local_collection';\nimport { idForOp } from './oplog_tailing';\n\nvar PHASE = {\n  QUERYING: \"QUERYING\",\n  FETCHING: \"FETCHING\",\n  STEADY: \"STEADY\"\n};\n\n// Exception thrown by _needToPollQuery which unrolls the stack up to the\n// enclosing call to finishIfNeedToPollQuery.\nvar SwitchedToQuery = function () {};\nvar finishIfNeedToPollQuery = function (f) {\n  return function () {\n    try {\n      f.apply(this, arguments);\n    } catch (e) {\n      if (!(e instanceof SwitchedToQuery))\n        throw e;\n    }\n  };\n};\n\nvar currentId = 0;\n\n/**\n * @class OplogObserveDriver\n * An alternative to PollingObserveDriver which follows the MongoDB operation log\n * instead of re-polling the query.\n *\n * Characteristics:\n * - Follows the MongoDB operation log\n * - Directly observes database changes\n * - More efficient than polling for most use cases\n * - Requires access to MongoDB oplog\n *\n * Interface:\n * - Construction initiates observeChanges callbacks and ready() invocation to the ObserveMultiplexer\n * - Observation can be terminated via the stop() method\n */\nexport const OplogObserveDriver = function (options) {\n  const self = this;\n  self._usesOplog = true;  // tests look at this\n\n  self._id = currentId;\n  currentId++;\n\n  self._cursorDescription = options.cursorDescription;\n  self._mongoHandle = options.mongoHandle;\n  self._multiplexer = options.multiplexer;\n\n  if (options.ordered) {\n    throw Error(\"OplogObserveDriver only supports unordered observeChanges\");\n  }\n\n  const sorter = options.sorter;\n  // We don't support $near and other geo-queries so it's OK to initialize the\n  // comparator only once in the constructor.\n  const comparator = sorter && sorter.getComparator();\n\n  if (options.cursorDescription.options.limit) {\n    // There are several properties ordered driver implements:\n    // - _limit is a positive number\n    // - _comparator is a function-comparator by which the query is ordered\n    // - _unpublishedBuffer is non-null Min/Max Heap,\n    //                      the empty buffer in STEADY phase implies that the\n    //                      everything that matches the queries selector fits\n    //                      into published set.\n    // - _published - Max Heap (also implements IdMap methods)\n\n    const heapOptions = { IdMap: LocalCollection._IdMap };\n    self._limit = self._cursorDescription.options.limit;\n    self._comparator = comparator;\n    self._sorter = sorter;\n    self._unpublishedBuffer = new MinMaxHeap(comparator, heapOptions);\n    // We need something that can find Max value in addition to IdMap interface\n    self._published = new MaxHeap(comparator, heapOptions);\n  } else {\n    self._limit = 0;\n    self._comparator = null;\n    self._sorter = null;\n    self._unpublishedBuffer = null;\n    // Memory Growth\n    self._published = new LocalCollection._IdMap;\n  }\n\n  // Indicates if it is safe to insert a new document at the end of the buffer\n  // for this query. i.e. it is known that there are no documents matching the\n  // selector those are not in published or buffer.\n  self._safeAppendToBuffer = false;\n\n  self._stopped = false;\n  self._stopHandles = [];\n  self._addStopHandles = function (newStopHandles) {\n    const expectedPattern = Match.ObjectIncluding({ stop: Function });\n    // Single item or array\n    check(newStopHandles, Match.OneOf([expectedPattern], expectedPattern));\n    self._stopHandles.push(newStopHandles);\n  }\n\n  Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n    \"mongo-livedata\", \"observe-drivers-oplog\", 1);\n\n  self._registerPhaseChange(PHASE.QUERYING);\n\n  self._matcher = options.matcher;\n  // we are now using projection, not fields in the cursor description even if you pass {fields}\n  // in the cursor construction\n  const projection = self._cursorDescription.options.fields || self._cursorDescription.options.projection || {};\n  self._projectionFn = LocalCollection._compileProjection(projection);\n  // Projection function, result of combining important fields for selector and\n  // existing fields projection\n  self._sharedProjection = self._matcher.combineIntoProjection(projection);\n  if (sorter)\n    self._sharedProjection = sorter.combineIntoProjection(self._sharedProjection);\n  self._sharedProjectionFn = LocalCollection._compileProjection(\n    self._sharedProjection);\n\n  self._needToFetch = new LocalCollection._IdMap;\n  self._currentlyFetching = null;\n  self._fetchGeneration = 0;\n\n  self._requeryWhenDoneThisQuery = false;\n  self._writesToCommitWhenWeReachSteady = [];\n };\n\nObject.assign(OplogObserveDriver.prototype, {\n  _init: async function() {\n    const self = this;\n\n    // If the oplog handle tells us that it skipped some entries (because it got\n    // behind, say), re-poll.\n    self._addStopHandles(self._mongoHandle._oplogHandle.onSkippedEntries(\n      finishIfNeedToPollQuery(function () {\n        return self._needToPollQuery();\n      })\n    ));\n    \n    await forEachTrigger(self._cursorDescription, async function (trigger) {\n      self._addStopHandles(await self._mongoHandle._oplogHandle.onOplogEntry(\n        trigger, function (notification) {\n          finishIfNeedToPollQuery(function () {\n            const op = notification.op;\n            if (notification.dropCollection || notification.dropDatabase) {\n              // Note: this call is not allowed to block on anything (especially\n              // on waiting for oplog entries to catch up) because that will block\n              // onOplogEntry!\n              return self._needToPollQuery();\n            } else {\n              // All other operators should be handled depending on phase\n              if (self._phase === PHASE.QUERYING) {\n                return self._handleOplogEntryQuerying(op);\n              } else {\n                return self._handleOplogEntrySteadyOrFetching(op);\n              }\n            }\n          })();\n        }\n      ));\n    });\n  \n    // XXX ordering w.r.t. everything else?\n    self._addStopHandles(await listenAll(\n      self._cursorDescription, function () {\n        // If we're not in a pre-fire write fence, we don't have to do anything.\n        const fence = DDPServer._getCurrentFence();\n        if (!fence || fence.fired)\n          return;\n  \n        if (fence._oplogObserveDrivers) {\n          fence._oplogObserveDrivers[self._id] = self;\n          return;\n        }\n  \n        fence._oplogObserveDrivers = {};\n        fence._oplogObserveDrivers[self._id] = self;\n  \n        fence.onBeforeFire(async function () {\n          const drivers = fence._oplogObserveDrivers;\n          delete fence._oplogObserveDrivers;\n  \n          // This fence cannot fire until we've caught up to \"this point\" in the\n          // oplog, and all observers made it back to the steady state.\n          await self._mongoHandle._oplogHandle.waitUntilCaughtUp();\n  \n          for (const driver of Object.values(drivers)) {\n            if (driver._stopped)\n              continue;\n  \n            const write = await fence.beginWrite();\n            if (driver._phase === PHASE.STEADY) {\n              // Make sure that all of the callbacks have made it through the\n              // multiplexer and been delivered to ObserveHandles before committing\n              // writes.\n              await driver._multiplexer.onFlush(write.committed);\n            } else {\n              driver._writesToCommitWhenWeReachSteady.push(write);\n            }\n          }\n        });\n      }\n    ));\n  \n    // When Mongo fails over, we need to repoll the query, in case we processed an\n    // oplog entry that got rolled back.\n    self._addStopHandles(self._mongoHandle._onFailover(finishIfNeedToPollQuery(\n      function () {\n        return self._needToPollQuery();\n      })));\n  \n    // Give _observeChanges a chance to add the new ObserveHandle to our\n    // multiplexer, so that the added calls get streamed.\n    return self._runInitialQuery();\n  },\n  _addPublished: function (id, doc) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      var fields = Object.assign({}, doc);\n      delete fields._id;\n      self._published.set(id, self._sharedProjectionFn(doc));\n      self._multiplexer.added(id, self._projectionFn(fields));\n\n      // After adding this document, the published set might be overflowed\n      // (exceeding capacity specified by limit). If so, push the maximum\n      // element to the buffer, we might want to save it in memory to reduce the\n      // amount of Mongo lookups in the future.\n      if (self._limit && self._published.size() > self._limit) {\n        // XXX in theory the size of published is no more than limit+1\n        if (self._published.size() !== self._limit + 1) {\n          throw new Error(\"After adding to published, \" +\n                          (self._published.size() - self._limit) +\n                          \" documents are overflowing the set\");\n        }\n\n        var overflowingDocId = self._published.maxElementId();\n        var overflowingDoc = self._published.get(overflowingDocId);\n\n        if (EJSON.equals(overflowingDocId, id)) {\n          throw new Error(\"The document just added is overflowing the published set\");\n        }\n\n        self._published.remove(overflowingDocId);\n        self._multiplexer.removed(overflowingDocId);\n        self._addBuffered(overflowingDocId, overflowingDoc);\n      }\n    });\n  },\n  _removePublished: function (id) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      self._published.remove(id);\n      self._multiplexer.removed(id);\n      if (! self._limit || self._published.size() === self._limit)\n        return;\n\n      if (self._published.size() > self._limit)\n        throw Error(\"self._published got too big\");\n\n      // OK, we are publishing less than the limit. Maybe we should look in the\n      // buffer to find the next element past what we were publishing before.\n\n      if (!self._unpublishedBuffer.empty()) {\n        // There's something in the buffer; move the first thing in it to\n        // _published.\n        var newDocId = self._unpublishedBuffer.minElementId();\n        var newDoc = self._unpublishedBuffer.get(newDocId);\n        self._removeBuffered(newDocId);\n        self._addPublished(newDocId, newDoc);\n        return;\n      }\n\n      // There's nothing in the buffer.  This could mean one of a few things.\n\n      // (a) We could be in the middle of re-running the query (specifically, we\n      // could be in _publishNewResults). In that case, _unpublishedBuffer is\n      // empty because we clear it at the beginning of _publishNewResults. In\n      // this case, our caller already knows the entire answer to the query and\n      // we don't need to do anything fancy here.  Just return.\n      if (self._phase === PHASE.QUERYING)\n        return;\n\n      // (b) We're pretty confident that the union of _published and\n      // _unpublishedBuffer contain all documents that match selector. Because\n      // _unpublishedBuffer is empty, that means we're confident that _published\n      // contains all documents that match selector. So we have nothing to do.\n      if (self._safeAppendToBuffer)\n        return;\n\n      // (c) Maybe there are other documents out there that should be in our\n      // buffer. But in that case, when we emptied _unpublishedBuffer in\n      // _removeBuffered, we should have called _needToPollQuery, which will\n      // either put something in _unpublishedBuffer or set _safeAppendToBuffer\n      // (or both), and it will put us in QUERYING for that whole time. So in\n      // fact, we shouldn't be able to get here.\n\n      throw new Error(\"Buffer inexplicably empty\");\n    });\n  },\n  _changePublished: function (id, oldDoc, newDoc) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      self._published.set(id, self._sharedProjectionFn(newDoc));\n      var projectedNew = self._projectionFn(newDoc);\n      var projectedOld = self._projectionFn(oldDoc);\n      var changed = DiffSequence.makeChangedFields(\n        projectedNew, projectedOld);\n      if (!isEmpty(changed))\n        self._multiplexer.changed(id, changed);\n    });\n  },\n  _addBuffered: function (id, doc) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      self._unpublishedBuffer.set(id, self._sharedProjectionFn(doc));\n\n      // If something is overflowing the buffer, we just remove it from cache\n      if (self._unpublishedBuffer.size() > self._limit) {\n        var maxBufferedId = self._unpublishedBuffer.maxElementId();\n\n        self._unpublishedBuffer.remove(maxBufferedId);\n\n        // Since something matching is removed from cache (both published set and\n        // buffer), set flag to false\n        self._safeAppendToBuffer = false;\n      }\n    });\n  },\n  // Is called either to remove the doc completely from matching set or to move\n  // it to the published set later.\n  _removeBuffered: function (id) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      self._unpublishedBuffer.remove(id);\n      // To keep the contract \"buffer is never empty in STEADY phase unless the\n      // everything matching fits into published\" true, we poll everything as\n      // soon as we see the buffer becoming empty.\n      if (! self._unpublishedBuffer.size() && ! self._safeAppendToBuffer)\n        self._needToPollQuery();\n    });\n  },\n  // Called when a document has joined the \"Matching\" results set.\n  // Takes responsibility of keeping _unpublishedBuffer in sync with _published\n  // and the effect of limit enforced.\n  _addMatching: function (doc) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      var id = doc._id;\n      if (self._published.has(id))\n        throw Error(\"tried to add something already published \" + id);\n      if (self._limit && self._unpublishedBuffer.has(id))\n        throw Error(\"tried to add something already existed in buffer \" + id);\n\n      var limit = self._limit;\n      var comparator = self._comparator;\n      var maxPublished = (limit && self._published.size() > 0) ?\n        self._published.get(self._published.maxElementId()) : null;\n      var maxBuffered = (limit && self._unpublishedBuffer.size() > 0)\n        ? self._unpublishedBuffer.get(self._unpublishedBuffer.maxElementId())\n        : null;\n      // The query is unlimited or didn't publish enough documents yet or the\n      // new document would fit into published set pushing the maximum element\n      // out, then we need to publish the doc.\n      var toPublish = ! limit || self._published.size() < limit ||\n        comparator(doc, maxPublished) < 0;\n\n      // Otherwise we might need to buffer it (only in case of limited query).\n      // Buffering is allowed if the buffer is not filled up yet and all\n      // matching docs are either in the published set or in the buffer.\n      var canAppendToBuffer = !toPublish && self._safeAppendToBuffer &&\n        self._unpublishedBuffer.size() < limit;\n\n      // Or if it is small enough to be safely inserted to the middle or the\n      // beginning of the buffer.\n      var canInsertIntoBuffer = !toPublish && maxBuffered &&\n        comparator(doc, maxBuffered) <= 0;\n\n      var toBuffer = canAppendToBuffer || canInsertIntoBuffer;\n\n      if (toPublish) {\n        self._addPublished(id, doc);\n      } else if (toBuffer) {\n        self._addBuffered(id, doc);\n      } else {\n        // dropping it and not saving to the cache\n        self._safeAppendToBuffer = false;\n      }\n    });\n  },\n  // Called when a document leaves the \"Matching\" results set.\n  // Takes responsibility of keeping _unpublishedBuffer in sync with _published\n  // and the effect of limit enforced.\n  _removeMatching: function (id) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      if (! self._published.has(id) && ! self._limit)\n        throw Error(\"tried to remove something matching but not cached \" + id);\n\n      if (self._published.has(id)) {\n        self._removePublished(id);\n      } else if (self._unpublishedBuffer.has(id)) {\n        self._removeBuffered(id);\n      }\n    });\n  },\n  _handleDoc: function (id, newDoc) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      var matchesNow = newDoc && self._matcher.documentMatches(newDoc).result;\n\n      var publishedBefore = self._published.has(id);\n      var bufferedBefore = self._limit && self._unpublishedBuffer.has(id);\n      var cachedBefore = publishedBefore || bufferedBefore;\n\n      if (matchesNow && !cachedBefore) {\n        self._addMatching(newDoc);\n      } else if (cachedBefore && !matchesNow) {\n        self._removeMatching(id);\n      } else if (cachedBefore && matchesNow) {\n        var oldDoc = self._published.get(id);\n        var comparator = self._comparator;\n        var minBuffered = self._limit && self._unpublishedBuffer.size() &&\n          self._unpublishedBuffer.get(self._unpublishedBuffer.minElementId());\n        var maxBuffered;\n\n        if (publishedBefore) {\n          // Unlimited case where the document stays in published once it\n          // matches or the case when we don't have enough matching docs to\n          // publish or the changed but matching doc will stay in published\n          // anyways.\n          //\n          // XXX: We rely on the emptiness of buffer. Be sure to maintain the\n          // fact that buffer can't be empty if there are matching documents not\n          // published. Notably, we don't want to schedule repoll and continue\n          // relying on this property.\n          var staysInPublished = ! self._limit ||\n            self._unpublishedBuffer.size() === 0 ||\n            comparator(newDoc, minBuffered) <= 0;\n\n          if (staysInPublished) {\n            self._changePublished(id, oldDoc, newDoc);\n          } else {\n            // after the change doc doesn't stay in the published, remove it\n            self._removePublished(id);\n            // but it can move into buffered now, check it\n            maxBuffered = self._unpublishedBuffer.get(\n              self._unpublishedBuffer.maxElementId());\n\n            var toBuffer = self._safeAppendToBuffer ||\n                  (maxBuffered && comparator(newDoc, maxBuffered) <= 0);\n\n            if (toBuffer) {\n              self._addBuffered(id, newDoc);\n            } else {\n              // Throw away from both published set and buffer\n              self._safeAppendToBuffer = false;\n            }\n          }\n        } else if (bufferedBefore) {\n          oldDoc = self._unpublishedBuffer.get(id);\n          // remove the old version manually instead of using _removeBuffered so\n          // we don't trigger the querying immediately.  if we end this block\n          // with the buffer empty, we will need to trigger the query poll\n          // manually too.\n          self._unpublishedBuffer.remove(id);\n\n          var maxPublished = self._published.get(\n            self._published.maxElementId());\n          maxBuffered = self._unpublishedBuffer.size() &&\n                self._unpublishedBuffer.get(\n                  self._unpublishedBuffer.maxElementId());\n\n          // the buffered doc was updated, it could move to published\n          var toPublish = comparator(newDoc, maxPublished) < 0;\n\n          // or stays in buffer even after the change\n          var staysInBuffer = (! toPublish && self._safeAppendToBuffer) ||\n                (!toPublish && maxBuffered &&\n                 comparator(newDoc, maxBuffered) <= 0);\n\n          if (toPublish) {\n            self._addPublished(id, newDoc);\n          } else if (staysInBuffer) {\n            // stays in buffer but changes\n            self._unpublishedBuffer.set(id, newDoc);\n          } else {\n            // Throw away from both published set and buffer\n            self._safeAppendToBuffer = false;\n            // Normally this check would have been done in _removeBuffered but\n            // we didn't use it, so we need to do it ourself now.\n            if (! self._unpublishedBuffer.size()) {\n              self._needToPollQuery();\n            }\n          }\n        } else {\n          throw new Error(\"cachedBefore implies either of publishedBefore or bufferedBefore is true.\");\n        }\n      }\n    });\n  },\n  _fetchModifiedDocuments: function () {\n    var self = this;\n    self._registerPhaseChange(PHASE.FETCHING);\n    // Defer, because nothing called from the oplog entry handler may yield,\n    // but fetch() yields.\n    Meteor.defer(finishIfNeedToPollQuery(async function () {\n      while (!self._stopped && !self._needToFetch.empty()) {\n        if (self._phase === PHASE.QUERYING) {\n          // While fetching, we decided to go into QUERYING mode, and then we\n          // saw another oplog entry, so _needToFetch is not empty. But we\n          // shouldn't fetch these documents until AFTER the query is done.\n          break;\n        }\n\n        // Being in steady phase here would be surprising.\n        if (self._phase !== PHASE.FETCHING)\n          throw new Error(\"phase in fetchModifiedDocuments: \" + self._phase);\n\n        self._currentlyFetching = self._needToFetch;\n        var thisGeneration = ++self._fetchGeneration;\n        self._needToFetch = new LocalCollection._IdMap;\n\n        // Create an array of promises for all the fetch operations\n        const fetchPromises = [];\n\n        self._currentlyFetching.forEach(function (op, id) {\n          const fetchPromise = new Promise((resolve, reject) => {\n            self._mongoHandle._docFetcher.fetch(\n              self._cursorDescription.collectionName,\n              id,\n              op,\n              finishIfNeedToPollQuery(function(err, doc) {\n                if (err) {\n                  Meteor._debug('Got exception while fetching documents', err);\n                  // If we get an error from the fetcher (eg, trouble\n                  // connecting to Mongo), let's just abandon the fetch phase\n                  // altogether and fall back to polling. It's not like we're\n                  // getting live updates anyway.\n                  if (self._phase !== PHASE.QUERYING) {\n                    self._needToPollQuery();\n                  }\n                  resolve();\n                  return;\n                }\n\n                if (\n                  !self._stopped &&\n                  self._phase === PHASE.FETCHING &&\n                  self._fetchGeneration === thisGeneration\n                ) {\n                  // We re-check the generation in case we've had an explicit\n                  // _pollQuery call (eg, in another fiber) which should\n                  // effectively cancel this round of fetches.  (_pollQuery\n                  // increments the generation.)\n                  try {\n                    self._handleDoc(id, doc);\n                    resolve();\n                  } catch (err) {\n                    reject(err);\n                  }\n                } else {\n                  resolve();\n                }\n              })\n            )\n          })\n          fetchPromises.push(fetchPromise);\n        });\n        // Wait for all fetch operations to complete\n        try {\n          const results = await Promise.allSettled(fetchPromises);\n          const errors = results\n            .filter(result => result.status === 'rejected')\n            .map(result => result.reason);\n\n          if (errors.length > 0) {\n            Meteor._debug('Some fetch queries failed:', errors);\n          }\n        } catch (err) {\n          Meteor._debug('Got an exception in a fetch query', err);\n        }\n        // Exit now if we've had a _pollQuery call (here or in another fiber).\n        if (self._phase === PHASE.QUERYING)\n          return;\n        self._currentlyFetching = null;\n      }\n      // We're done fetching, so we can be steady, unless we've had a\n      // _pollQuery call (here or in another fiber).\n      if (self._phase !== PHASE.QUERYING)\n        await self._beSteady();\n    }));\n  },\n  _beSteady: async function () {\n    var self = this;\n    self._registerPhaseChange(PHASE.STEADY);\n    var writes = self._writesToCommitWhenWeReachSteady || [];\n    self._writesToCommitWhenWeReachSteady = [];\n    await self._multiplexer.onFlush(async function () {\n      try {\n        for (const w of writes) {\n          await w.committed();\n        }\n      } catch (e) {\n        console.error(\"_beSteady error\", {writes}, e);\n      }\n    });\n  },\n  _handleOplogEntryQuerying: function (op) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      self._needToFetch.set(idForOp(op), op);\n    });\n  },\n  _handleOplogEntrySteadyOrFetching: function (op) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      var id = idForOp(op);\n      // If we're already fetching this one, or about to, we can't optimize;\n      // make sure that we fetch it again if necessary.\n\n      if (self._phase === PHASE.FETCHING &&\n          ((self._currentlyFetching && self._currentlyFetching.has(id)) ||\n           self._needToFetch.has(id))) {\n        self._needToFetch.set(id, op);\n        return;\n      }\n\n      if (op.op === 'd') {\n        if (self._published.has(id) ||\n            (self._limit && self._unpublishedBuffer.has(id)))\n          self._removeMatching(id);\n      } else if (op.op === 'i') {\n        if (self._published.has(id))\n          throw new Error(\"insert found for already-existing ID in published\");\n        if (self._unpublishedBuffer && self._unpublishedBuffer.has(id))\n          throw new Error(\"insert found for already-existing ID in buffer\");\n\n        // XXX what if selector yields?  for now it can't but later it could\n        // have $where\n        if (self._matcher.documentMatches(op.o).result)\n          self._addMatching(op.o);\n      } else if (op.op === 'u') {\n        // we are mapping the new oplog format on mongo 5\n        // to what we know better, $set\n        op.o = oplogV2V1Converter(op.o)\n        // Is this a modifier ($set/$unset, which may require us to poll the\n        // database to figure out if the whole document matches the selector) or\n        // a replacement (in which case we can just directly re-evaluate the\n        // selector)?\n        // oplog format has changed on mongodb 5, we have to support both now\n        // diff is the format in Mongo 5+ (oplog v2)\n        var isReplace = !has(op.o, '$set') && !has(op.o, 'diff') && !has(op.o, '$unset');\n        // If this modifier modifies something inside an EJSON custom type (ie,\n        // anything with EJSON$), then we can't try to use\n        // LocalCollection._modify, since that just mutates the EJSON encoding,\n        // not the actual object.\n        var canDirectlyModifyDoc =\n          !isReplace && modifierCanBeDirectlyApplied(op.o);\n\n        var publishedBefore = self._published.has(id);\n        var bufferedBefore = self._limit && self._unpublishedBuffer.has(id);\n\n        if (isReplace) {\n          self._handleDoc(id, Object.assign({_id: id}, op.o));\n        } else if ((publishedBefore || bufferedBefore) &&\n                   canDirectlyModifyDoc) {\n          // Oh great, we actually know what the document is, so we can apply\n          // this directly.\n          var newDoc = self._published.has(id)\n            ? self._published.get(id) : self._unpublishedBuffer.get(id);\n          newDoc = EJSON.clone(newDoc);\n\n          newDoc._id = id;\n          try {\n            LocalCollection._modify(newDoc, op.o);\n          } catch (e) {\n            if (e.name !== \"MinimongoError\")\n              throw e;\n            // We didn't understand the modifier.  Re-fetch.\n            self._needToFetch.set(id, op);\n            if (self._phase === PHASE.STEADY) {\n              self._fetchModifiedDocuments();\n            }\n            return;\n          }\n          self._handleDoc(id, self._sharedProjectionFn(newDoc));\n        } else if (!canDirectlyModifyDoc ||\n                   self._matcher.canBecomeTrueByModifier(op.o) ||\n                   (self._sorter && self._sorter.affectedByModifier(op.o))) {\n          self._needToFetch.set(id, op);\n          if (self._phase === PHASE.STEADY)\n            self._fetchModifiedDocuments();\n        }\n      } else {\n        throw Error(\"XXX SURPRISING OPERATION: \" + op);\n      }\n    });\n  },\n\n  async _runInitialQueryAsync() {\n    var self = this;\n    if (self._stopped)\n      throw new Error(\"oplog stopped surprisingly early\");\n\n    await self._runQuery({initial: true});  // yields\n\n    if (self._stopped)\n      return;  // can happen on queryError\n\n    // Allow observeChanges calls to return. (After this, it's possible for\n    // stop() to be called.)\n    await self._multiplexer.ready();\n\n    await self._doneQuerying();  // yields\n  },\n\n  // Yields!\n  _runInitialQuery: function () {\n    return this._runInitialQueryAsync();\n  },\n\n  // In various circumstances, we may just want to stop processing the oplog and\n  // re-run the initial query, just as if we were a PollingObserveDriver.\n  //\n  // This function may not block, because it is called from an oplog entry\n  // handler.\n  //\n  // XXX We should call this when we detect that we've been in FETCHING for \"too\n  // long\".\n  //\n  // XXX We should call this when we detect Mongo failover (since that might\n  // mean that some of the oplog entries we have processed have been rolled\n  // back). The Node Mongo driver is in the middle of a bunch of huge\n  // refactorings, including the way that it notifies you when primary\n  // changes. Will put off implementing this until driver 1.4 is out.\n  _pollQuery: function () {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      if (self._stopped)\n        return;\n\n      // Yay, we get to forget about all the things we thought we had to fetch.\n      self._needToFetch = new LocalCollection._IdMap;\n      self._currentlyFetching = null;\n      ++self._fetchGeneration;  // ignore any in-flight fetches\n      self._registerPhaseChange(PHASE.QUERYING);\n\n      // Defer so that we don't yield.  We don't need finishIfNeedToPollQuery\n      // here because SwitchedToQuery is not thrown in QUERYING mode.\n      Meteor.defer(async function () {\n        await self._runQuery();\n        await self._doneQuerying();\n      });\n    });\n  },\n\n  // Yields!\n  async _runQueryAsync(options) {\n    var self = this;\n    options = options || {};\n    var newResults, newBuffer;\n\n    // This while loop is just to retry failures.\n    while (true) {\n      // If we've been stopped, we don't have to run anything any more.\n      if (self._stopped)\n        return;\n\n      newResults = new LocalCollection._IdMap;\n      newBuffer = new LocalCollection._IdMap;\n\n      // Query 2x documents as the half excluded from the original query will go\n      // into unpublished buffer to reduce additional Mongo lookups in cases\n      // when documents are removed from the published set and need a\n      // replacement.\n      // XXX needs more thought on non-zero skip\n      // XXX 2 is a \"magic number\" meaning there is an extra chunk of docs for\n      // buffer if such is needed.\n      var cursor = self._cursorForQuery({ limit: self._limit * 2 });\n      try {\n        await cursor.forEach(function (doc, i) {  // yields\n          if (!self._limit || i < self._limit) {\n            newResults.set(doc._id, doc);\n          } else {\n            newBuffer.set(doc._id, doc);\n          }\n        });\n        break;\n      } catch (e) {\n        if (options.initial && typeof(e.code) === 'number') {\n          // This is an error document sent to us by mongod, not a connection\n          // error generated by the client. And we've never seen this query work\n          // successfully. Probably it's a bad selector or something, so we\n          // should NOT retry. Instead, we should halt the observe (which ends\n          // up calling `stop` on us).\n          await self._multiplexer.queryError(e);\n          return;\n        }\n\n        // During failover (eg) if we get an exception we should log and retry\n        // instead of crashing.\n        Meteor._debug(\"Got exception while polling query\", e);\n        await Meteor._sleepForMs(100);\n      }\n    }\n\n    if (self._stopped)\n      return;\n\n    self._publishNewResults(newResults, newBuffer);\n  },\n\n  // Yields!\n  _runQuery: function (options) {\n    return this._runQueryAsync(options);\n  },\n\n  // Transitions to QUERYING and runs another query, or (if already in QUERYING)\n  // ensures that we will query again later.\n  //\n  // This function may not block, because it is called from an oplog entry\n  // handler. However, if we were not already in the QUERYING phase, it throws\n  // an exception that is caught by the closest surrounding\n  // finishIfNeedToPollQuery call; this ensures that we don't continue running\n  // close that was designed for another phase inside PHASE.QUERYING.\n  //\n  // (It's also necessary whenever logic in this file yields to check that other\n  // phases haven't put us into QUERYING mode, though; eg,\n  // _fetchModifiedDocuments does this.)\n  _needToPollQuery: function () {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      if (self._stopped)\n        return;\n\n      // If we're not already in the middle of a query, we can query now\n      // (possibly pausing FETCHING).\n      if (self._phase !== PHASE.QUERYING) {\n        self._pollQuery();\n        throw new SwitchedToQuery;\n      }\n\n      // We're currently in QUERYING. Set a flag to ensure that we run another\n      // query when we're done.\n      self._requeryWhenDoneThisQuery = true;\n    });\n  },\n\n  // Yields!\n  _doneQuerying: async function () {\n    var self = this;\n\n    if (self._stopped)\n      return;\n\n    await self._mongoHandle._oplogHandle.waitUntilCaughtUp();\n\n    if (self._stopped)\n      return;\n\n    if (self._phase !== PHASE.QUERYING)\n      throw Error(\"Phase unexpectedly \" + self._phase);\n\n    if (self._requeryWhenDoneThisQuery) {\n      self._requeryWhenDoneThisQuery = false;\n      self._pollQuery();\n    } else if (self._needToFetch.empty()) {\n      await self._beSteady();\n    } else {\n      self._fetchModifiedDocuments();\n    }\n  },\n\n  _cursorForQuery: function (optionsOverwrite) {\n    var self = this;\n    return Meteor._noYieldsAllowed(function () {\n      // The query we run is almost the same as the cursor we are observing,\n      // with a few changes. We need to read all the fields that are relevant to\n      // the selector, not just the fields we are going to publish (that's the\n      // \"shared\" projection). And we don't want to apply any transform in the\n      // cursor, because observeChanges shouldn't use the transform.\n      var options = Object.assign({}, self._cursorDescription.options);\n\n      // Allow the caller to modify the options. Useful to specify different\n      // skip and limit values.\n      Object.assign(options, optionsOverwrite);\n\n      options.fields = self._sharedProjection;\n      delete options.transform;\n      // We are NOT deep cloning fields or selector here, which should be OK.\n      var description = new CursorDescription(\n        self._cursorDescription.collectionName,\n        self._cursorDescription.selector,\n        options);\n      return new Cursor(self._mongoHandle, description);\n    });\n  },\n\n\n  // Replace self._published with newResults (both are IdMaps), invoking observe\n  // callbacks on the multiplexer.\n  // Replace self._unpublishedBuffer with newBuffer.\n  //\n  // XXX This is very similar to LocalCollection._diffQueryUnorderedChanges. We\n  // should really: (a) Unify IdMap and OrderedDict into Unordered/OrderedDict\n  // (b) Rewrite diff.js to use these classes instead of arrays and objects.\n  _publishNewResults: function (newResults, newBuffer) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n\n      // If the query is limited and there is a buffer, shut down so it doesn't\n      // stay in a way.\n      if (self._limit) {\n        self._unpublishedBuffer.clear();\n      }\n\n      // First remove anything that's gone. Be careful not to modify\n      // self._published while iterating over it.\n      var idsToRemove = [];\n      self._published.forEach(function (doc, id) {\n        if (!newResults.has(id))\n          idsToRemove.push(id);\n      });\n      idsToRemove.forEach(function (id) {\n        self._removePublished(id);\n      });\n\n      // Now do adds and changes.\n      // If self has a buffer and limit, the new fetched result will be\n      // limited correctly as the query has sort specifier.\n      newResults.forEach(function (doc, id) {\n        self._handleDoc(id, doc);\n      });\n\n      // Sanity-check that everything we tried to put into _published ended up\n      // there.\n      // XXX if this is slow, remove it later\n      if (self._published.size() !== newResults.size()) {\n        Meteor._debug('The Mongo server and the Meteor query disagree on how ' +\n          'many documents match your query. Cursor description: ',\n          self._cursorDescription);\n      }\n      \n      self._published.forEach(function (doc, id) {\n        if (!newResults.has(id))\n          throw Error(\"_published has a doc that newResults doesn't; \" + id);\n      });\n\n      // Finally, replace the buffer\n      newBuffer.forEach(function (doc, id) {\n        self._addBuffered(id, doc);\n      });\n\n      self._safeAppendToBuffer = newBuffer.size() < self._limit;\n    });\n  },\n\n  // This stop function is invoked from the onStop of the ObserveMultiplexer, so\n  // it shouldn't actually be possible to call it until the multiplexer is\n  // ready.\n  //\n  // It's important to check self._stopped after every call in this file that\n  // can yield!\n  _stop: async function() {\n    var self = this;\n    if (self._stopped)\n      return;\n    self._stopped = true;\n\n    // Note: we *don't* use multiplexer.onFlush here because this stop\n    // callback is actually invoked by the multiplexer itself when it has\n    // determined that there are no handles left. So nothing is actually going\n    // to get flushed (and it's probably not valid to call methods on the\n    // dying multiplexer).\n    for (const w of self._writesToCommitWhenWeReachSteady) {\n      await w.committed();\n    }\n    self._writesToCommitWhenWeReachSteady = null;\n\n    // Proactively drop references to potentially big things.\n    self._published = null;\n    self._unpublishedBuffer = null;\n    self._needToFetch = null;\n    self._currentlyFetching = null;\n    self._oplogEntryHandle = null;\n    self._listenersHandle = null;\n\n    Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n        \"mongo-livedata\", \"observe-drivers-oplog\", -1);\n\n    for await (const handle of self._stopHandles) {\n      await handle.stop();\n    }\n  },\n  stop: async function() {\n    const self = this;\n    return await self._stop();\n  },\n\n  _registerPhaseChange: function (phase) {\n    var self = this;\n    Meteor._noYieldsAllowed(function () {\n      var now = new Date;\n\n      if (self._phase) {\n        var timeDiff = now - self._phaseStartTime;\n        Package['facts-base'] && Package['facts-base'].Facts.incrementServerFact(\n          \"mongo-livedata\", \"time-spent-in-\" + self._phase + \"-phase\", timeDiff);\n      }\n\n      self._phase = phase;\n      self._phaseStartTime = now;\n    });\n  }\n});\n\n// Does our oplog tailing code support this cursor? For now, we are being very\n// conservative and allowing only simple queries with simple options.\n// (This is a \"static method\".)\nOplogObserveDriver.cursorSupported = function (cursorDescription, matcher) {\n  // First, check the options.\n  var options = cursorDescription.options;\n\n  // Did the user say no explicitly?\n  // underscored version of the option is COMPAT with 1.2\n  if (options.disableOplog || options._disableOplog)\n    return false;\n\n  // skip is not supported: to support it we would need to keep track of all\n  // \"skipped\" documents or at least their ids.\n  // limit w/o a sort specifier is not supported: current implementation needs a\n  // deterministic way to order documents.\n  if (options.skip || (options.limit && !options.sort)) return false;\n\n  // If a fields projection option is given check if it is supported by\n  // minimongo (some operators are not supported).\n  const fields = options.fields || options.projection;\n  if (fields) {\n    try {\n      LocalCollection._checkSupportedProjection(fields);\n    } catch (e) {\n      if (e.name === \"MinimongoError\") {\n        return false;\n      } else {\n        throw e;\n      }\n    }\n  }\n\n  // We don't allow the following selectors:\n  //   - $where (not confident that we provide the same JS environment\n  //             as Mongo, and can yield!)\n  //   - $near (has \"interesting\" properties in MongoDB, like the possibility\n  //            of returning an ID multiple times, though even polling maybe\n  //            have a bug there)\n  //           XXX: once we support it, we would need to think more on how we\n  //           initialize the comparators when we create the driver.\n  return !matcher.hasWhere() && !matcher.hasGeoQuery();\n};\n\nvar modifierCanBeDirectlyApplied = function (modifier) {\n  return Object.entries(modifier).every(function ([operation, fields]) {\n    return Object.entries(fields).every(function ([field, value]) {\n      return !/EJSON\\$/.test(field);\n    });\n  });\n};", "/**\n * Converter module for the new MongoDB Oplog format (>=5.0) to the one that Meteor\n * handles well, i.e., `$set` and `$unset`. The new format is completely new,\n * and looks as follows:\n *\n * ```js\n * { $v: 2, diff: Diff }\n * ```\n *\n * where `Diff` is a recursive structure:\n * ```js\n * {\n *   // Nested updates (sometimes also represented with an s-field).\n *   // Example: `{ $set: { 'foo.bar': 1 } }`.\n *   i: { <key>: <value>, ... },\n *\n *   // Top-level updates.\n *   // Example: `{ $set: { foo: { bar: 1 } } }`.\n *   u: { <key>: <value>, ... },\n *\n *   // Unsets.\n *   // Example: `{ $unset: { foo: '' } }`.\n *   d: { <key>: false, ... },\n *\n *   // Array operations.\n *   // Example: `{ $push: { foo: 'bar' } }`.\n *   s<key>: { a: true, u<index>: <value>, ... },\n *   ...\n *\n *   // Nested operations (sometimes also represented in the `i` field).\n *   // Example: `{ $set: { 'foo.bar': 1 } }`.\n *   s<key>: Diff,\n *   ...\n * }\n * ```\n *\n * (all fields are optional)\n */\n\nimport { EJSON } from 'meteor/ejson';\n\ninterface OplogEntry {\n  $v: number;\n  diff?: OplogDiff;\n  $set?: Record<string, any>;\n  $unset?: Record<string, true>;\n}\n\ninterface OplogDiff {\n  i?: Record<string, any>;\n  u?: Record<string, any>;\n  d?: Record<string, boolean>;\n  [key: `s${string}`]: ArrayOperator | Record<string, any>;\n}\n\ninterface ArrayOperator {\n  a: true;\n  [key: `u${number}`]: any;\n}\n\nconst arrayOperatorKeyRegex = /^(a|[su]\\d+)$/;\n\n/**\n * Checks if a field is an array operator key of form 'a' or 's1' or 'u1' etc\n */\nfunction isArrayOperatorKey(field: string): boolean {\n  return arrayOperatorKeyRegex.test(field);\n}\n\n/**\n * Type guard to check if an operator is a valid array operator.\n * Array operators have 'a: true' and keys that match the arrayOperatorKeyRegex\n */\nfunction isArrayOperator(operator: unknown): operator is ArrayOperator {\n  return (\n    operator !== null &&\n    typeof operator === 'object' &&\n    'a' in operator &&\n    (operator as ArrayOperator).a === true &&\n    Object.keys(operator).every(isArrayOperatorKey)\n  );\n}\n\n/**\n * Joins two parts of a field path with a dot.\n * Returns the key itself if prefix is empty.\n */\nfunction join(prefix: string, key: string): string {\n  return prefix ? `${prefix}.${key}` : key;\n}\n\n/**\n * Recursively flattens an object into a target object with dot notation paths.\n * Handles special cases:\n * - Arrays are assigned directly\n * - Custom EJSON types are preserved\n * - Mongo.ObjectIDs are preserved\n * - Plain objects are recursively flattened\n * - Empty objects are assigned directly\n */\nfunction flattenObjectInto(\n  target: Record<string, any>,\n  source: any,\n  prefix: string\n): void {\n  if (\n    Array.isArray(source) ||\n    typeof source !== 'object' ||\n    source === null ||\n    source instanceof Mongo.ObjectID ||\n    EJSON._isCustomType(source)\n  ) {\n    target[prefix] = source;\n    return;\n  }\n\n  const entries = Object.entries(source);\n  if (entries.length) {\n    entries.forEach(([key, value]) => {\n      flattenObjectInto(target, value, join(prefix, key));\n    });\n  } else {\n    target[prefix] = source;\n  }\n}\n\n/**\n * Converts an oplog diff to a series of $set and $unset operations.\n * Handles several types of operations:\n * - Direct unsets via 'd' field\n * - Nested sets via 'i' field\n * - Top-level sets via 'u' field\n * - Array operations and nested objects via 's' prefixed fields\n *\n * Preserves the structure of EJSON custom types and ObjectIDs while\n * flattening paths into dot notation for MongoDB updates.\n */\nfunction convertOplogDiff(\n  oplogEntry: OplogEntry,\n  diff: OplogDiff,\n  prefix = ''\n): void {\n  Object.entries(diff).forEach(([diffKey, value]) => {\n    if (diffKey === 'd') {\n      // Handle `$unset`s\n      oplogEntry.$unset ??= {};\n      Object.keys(value).forEach(key => {\n        oplogEntry.$unset![join(prefix, key)] = true;\n      });\n    } else if (diffKey === 'i') {\n      // Handle (potentially) nested `$set`s\n      oplogEntry.$set ??= {};\n      flattenObjectInto(oplogEntry.$set, value, prefix);\n    } else if (diffKey === 'u') {\n      // Handle flat `$set`s\n      oplogEntry.$set ??= {};\n      Object.entries(value).forEach(([key, fieldValue]) => {\n        oplogEntry.$set![join(prefix, key)] = fieldValue;\n      });\n    } else if (diffKey.startsWith('s')) {\n      // Handle s-fields (array operations and nested objects)\n      const key = diffKey.slice(1);\n      if (isArrayOperator(value)) {\n        // Array operator\n        Object.entries(value).forEach(([position, fieldValue]) => {\n          if (position === 'a') return;\n\n          const positionKey = join(prefix, `${key}.${position.slice(1)}`);\n          if (position[0] === 's') {\n            convertOplogDiff(oplogEntry, fieldValue, positionKey);\n          } else if (fieldValue === null) {\n            oplogEntry.$unset ??= {};\n            oplogEntry.$unset[positionKey] = true;\n          } else {\n            oplogEntry.$set ??= {};\n            oplogEntry.$set[positionKey] = fieldValue;\n          }\n        });\n      } else if (key) {\n        // Nested object\n        convertOplogDiff(oplogEntry, value, join(prefix, key));\n      }\n    }\n  });\n}\n\n/**\n * Converts a MongoDB v2 oplog entry to v1 format.\n * Returns the original entry unchanged if it's not a v2 oplog entry\n * or doesn't contain a diff field.\n *\n * The converted entry will contain $set and $unset operations that are\n * equivalent to the v2 diff format, with paths flattened to dot notation\n * and special handling for EJSON custom types and ObjectIDs.\n */\nexport function oplogV2V1Converter(oplogEntry: OplogEntry): OplogEntry {\n  if (oplogEntry.$v !== 2 || !oplogEntry.diff) {\n    return oplogEntry;\n  }\n\n  const convertedOplogEntry: OplogEntry = { $v: 2 };\n  convertOplogDiff(convertedOplogEntry, oplogEntry.diff);\n  return convertedOplogEntry;\n}", "interface CursorOptions {\n  limit?: number;\n  skip?: number;\n  sort?: Record<string, 1 | -1>;\n  fields?: Record<string, 1 | 0>;\n  projection?: Record<string, 1 | 0>;\n  disableOplog?: boolean;\n  _disableOplog?: boolean;\n  tailable?: boolean;\n  transform?: (doc: any) => any;\n}\n\n/**\n * Represents the arguments used to construct a cursor.\n * Used as a key for cursor de-duplication.\n *\n * All properties must be either:\n * - JSON-stringifiable, or\n * - Not affect observeChanges output (e.g., options.transform functions)\n */\nexport class CursorDescription {\n  collectionName: string;\n  selector: Record<string, any>;\n  options: CursorOptions;\n\n  constructor(collectionName: string, selector: any, options?: CursorOptions) {\n    this.collectionName = collectionName;\n    // @ts-ignore\n    this.selector = Mongo.Collection._rewriteSelector(selector);\n    this.options = options || {};\n  }\n}", "import { Meteor } from 'meteor/meteor';\nimport { CLIENT_ONLY_METHODS, getAsyncMethodName } from 'meteor/minimongo/constants';\nimport path from 'path';\nimport { AsynchronousCursor } from './asynchronous_cursor';\nimport { <PERSON><PERSON><PERSON> } from './cursor';\nimport { CursorDescription } from './cursor_description';\nimport { DocFetcher } from './doc_fetcher';\nimport { MongoDB, replaceMeteorAtomWithMongo, replaceTypes, transformResult } from './mongo_common';\nimport { ObserveHandle } from './observe_handle';\nimport { ObserveMultiplexer } from './observe_multiplex';\nimport { OplogObserveDriver } from './oplog_observe_driver';\nimport { OPLOG_COLLECTION, OplogHandle } from './oplog_tailing';\nimport { PollingObserveDriver } from './polling_observe_driver';\n\nconst FILE_ASSET_SUFFIX = 'Asset';\nconst ASSETS_FOLDER = 'assets';\nconst APP_FOLDER = 'app';\n\nconst oplogCollectionWarnings = [];\n\nexport const MongoConnection = function (url, options) {\n  var self = this;\n  options = options || {};\n  self._observeMultiplexers = {};\n  self._onFailoverHook = new Hook;\n\n  const userOptions = {\n    ...(Mongo._connectionOptions || {}),\n    ...(Meteor.settings?.packages?.mongo?.options || {})\n  };\n\n  var mongoOptions = Object.assign({\n    ignoreUndefined: true,\n  }, userOptions);\n\n\n\n  // Internally the oplog connections specify their own maxPoolSize\n  // which we don't want to overwrite with any user defined value\n  if ('maxPoolSize' in options) {\n    // If we just set this for \"server\", replSet will override it. If we just\n    // set it for replSet, it will be ignored if we're not using a replSet.\n    mongoOptions.maxPoolSize = options.maxPoolSize;\n  }\n  if ('minPoolSize' in options) {\n    mongoOptions.minPoolSize = options.minPoolSize;\n  }\n\n  // Transform options like \"tlsCAFileAsset\": \"filename.pem\" into\n  // \"tlsCAFile\": \"/<fullpath>/filename.pem\"\n  Object.entries(mongoOptions || {})\n    .filter(([key]) => key && key.endsWith(FILE_ASSET_SUFFIX))\n    .forEach(([key, value]) => {\n      const optionName = key.replace(FILE_ASSET_SUFFIX, '');\n      mongoOptions[optionName] = path.join(Assets.getServerDir(),\n        ASSETS_FOLDER, APP_FOLDER, value);\n      delete mongoOptions[key];\n    });\n\n  self.db = null;\n  self._oplogHandle = null;\n  self._docFetcher = null;\n\n  mongoOptions.driverInfo = {\n    name: 'Meteor',\n    version: Meteor.release\n  }\n\n  self.client = new MongoDB.MongoClient(url, mongoOptions);\n  self.db = self.client.db();\n\n  self.client.on('serverDescriptionChanged', Meteor.bindEnvironment(event => {\n    // When the connection is no longer against the primary node, execute all\n    // failover hooks. This is important for the driver as it has to re-pool the\n    // query when it happens.\n    if (\n      event.previousDescription.type !== 'RSPrimary' &&\n      event.newDescription.type === 'RSPrimary'\n    ) {\n      self._onFailoverHook.each(callback => {\n        callback();\n        return true;\n      });\n    }\n  }));\n\n  if (options.oplogUrl && ! Package['disable-oplog']) {\n    self._oplogHandle = new OplogHandle(options.oplogUrl, self.db.databaseName);\n    self._docFetcher = new DocFetcher(self);\n  }\n\n};\n\nMongoConnection.prototype._close = async function() {\n  var self = this;\n\n  if (! self.db)\n    throw Error(\"close called before Connection created?\");\n\n  // XXX probably untested\n  var oplogHandle = self._oplogHandle;\n  self._oplogHandle = null;\n  if (oplogHandle)\n    await oplogHandle.stop();\n\n  // Use Future.wrap so that errors get thrown. This happens to\n  // work even outside a fiber since the 'close' method is not\n  // actually asynchronous.\n  await self.client.close();\n};\n\nMongoConnection.prototype.close = function () {\n  return this._close();\n};\n\nMongoConnection.prototype._setOplogHandle = function(oplogHandle) {\n  this._oplogHandle = oplogHandle;\n  return this;\n};\n\n// Returns the Mongo Collection object; may yield.\nMongoConnection.prototype.rawCollection = function (collectionName) {\n  var self = this;\n\n  if (! self.db)\n    throw Error(\"rawCollection called before Connection created?\");\n\n  return self.db.collection(collectionName);\n};\n\nMongoConnection.prototype.createCappedCollectionAsync = async function (\n  collectionName, byteSize, maxDocuments) {\n  var self = this;\n\n  if (! self.db)\n    throw Error(\"createCappedCollectionAsync called before Connection created?\");\n\n\n  await self.db.createCollection(collectionName,\n    { capped: true, size: byteSize, max: maxDocuments });\n};\n\n// This should be called synchronously with a write, to create a\n// transaction on the current write fence, if any. After we can read\n// the write, and after observers have been notified (or at least,\n// after the observer notifiers have added themselves to the write\n// fence), you should call 'committed()' on the object returned.\nMongoConnection.prototype._maybeBeginWrite = function () {\n  const fence = DDPServer._getCurrentFence();\n  if (fence) {\n    return fence.beginWrite();\n  } else {\n    return {committed: function () {}};\n  }\n};\n\n// Internal interface: adds a callback which is called when the Mongo primary\n// changes. Returns a stop handle.\nMongoConnection.prototype._onFailover = function (callback) {\n  return this._onFailoverHook.register(callback);\n};\n\nMongoConnection.prototype.insertAsync = async function (collection_name, document) {\n  const self = this;\n\n  if (collection_name === \"___meteor_failure_test_collection\") {\n    const e = new Error(\"Failure test\");\n    e._expectedByTest = true;\n    throw e;\n  }\n\n  if (!(LocalCollection._isPlainObject(document) &&\n    !EJSON._isCustomType(document))) {\n    throw new Error(\"Only plain objects may be inserted into MongoDB\");\n  }\n\n  var write = self._maybeBeginWrite();\n  var refresh = async function () {\n    await Meteor.refresh({collection: collection_name, id: document._id });\n  };\n  return self.rawCollection(collection_name).insertOne(\n    replaceTypes(document, replaceMeteorAtomWithMongo),\n    {\n      safe: true,\n    }\n  ).then(async ({insertedId}) => {\n    await refresh();\n    await write.committed();\n    return insertedId;\n  }).catch(async e => {\n    await write.committed();\n    throw e;\n  });\n};\n\n\n// Cause queries that may be affected by the selector to poll in this write\n// fence.\nMongoConnection.prototype._refresh = async function (collectionName, selector) {\n  var refreshKey = {collection: collectionName};\n  // If we know which documents we're removing, don't poll queries that are\n  // specific to other documents. (Note that multiple notifications here should\n  // not cause multiple polls, since all our listener is doing is enqueueing a\n  // poll.)\n  var specificIds = LocalCollection._idsMatchedBySelector(selector);\n  if (specificIds) {\n    for (const id of specificIds) {\n      await Meteor.refresh(Object.assign({id: id}, refreshKey));\n    };\n  } else {\n    await Meteor.refresh(refreshKey);\n  }\n};\n\nMongoConnection.prototype.removeAsync = async function (collection_name, selector) {\n  var self = this;\n\n  if (collection_name === \"___meteor_failure_test_collection\") {\n    var e = new Error(\"Failure test\");\n    e._expectedByTest = true;\n    throw e;\n  }\n\n  var write = self._maybeBeginWrite();\n  var refresh = async function () {\n    await self._refresh(collection_name, selector);\n  };\n\n  return self.rawCollection(collection_name)\n    .deleteMany(replaceTypes(selector, replaceMeteorAtomWithMongo), {\n      safe: true,\n    })\n    .then(async ({ deletedCount }) => {\n      await refresh();\n      await write.committed();\n      return transformResult({ result : {modifiedCount : deletedCount} }).numberAffected;\n    }).catch(async (err) => {\n      await write.committed();\n      throw err;\n    });\n};\n\nMongoConnection.prototype.dropCollectionAsync = async function(collectionName) {\n  var self = this;\n\n\n  var write = self._maybeBeginWrite();\n  var refresh = function() {\n    return Meteor.refresh({\n      collection: collectionName,\n      id: null,\n      dropCollection: true,\n    });\n  };\n\n  return self\n    .rawCollection(collectionName)\n    .drop()\n    .then(async result => {\n      await refresh();\n      await write.committed();\n      return result;\n    })\n    .catch(async e => {\n      await write.committed();\n      throw e;\n    });\n};\n\n// For testing only.  Slightly better than `c.rawDatabase().dropDatabase()`\n// because it lets the test's fence wait for it to be complete.\nMongoConnection.prototype.dropDatabaseAsync = async function () {\n  var self = this;\n\n  var write = self._maybeBeginWrite();\n  var refresh = async function () {\n    await Meteor.refresh({ dropDatabase: true });\n  };\n\n  try {\n    await self.db._dropDatabase();\n    await refresh();\n    await write.committed();\n  } catch (e) {\n    await write.committed();\n    throw e;\n  }\n};\n\nMongoConnection.prototype.updateAsync = async function (collection_name, selector, mod, options) {\n  var self = this;\n\n  if (collection_name === \"___meteor_failure_test_collection\") {\n    var e = new Error(\"Failure test\");\n    e._expectedByTest = true;\n    throw e;\n  }\n\n  // explicit safety check. null and undefined can crash the mongo\n  // driver. Although the node driver and minimongo do 'support'\n  // non-object modifier in that they don't crash, they are not\n  // meaningful operations and do not do anything. Defensively throw an\n  // error here.\n  if (!mod || typeof mod !== 'object') {\n    const error = new Error(\"Invalid modifier. Modifier must be an object.\");\n\n    throw error;\n  }\n\n  if (!(LocalCollection._isPlainObject(mod) && !EJSON._isCustomType(mod))) {\n    const error = new Error(\n      \"Only plain objects may be used as replacement\" +\n      \" documents in MongoDB\");\n\n    throw error;\n  }\n\n  if (!options) options = {};\n\n  var write = self._maybeBeginWrite();\n  var refresh = async function () {\n    await self._refresh(collection_name, selector);\n  };\n\n  var collection = self.rawCollection(collection_name);\n  var mongoOpts = {safe: true};\n  // Add support for filtered positional operator\n  if (options.arrayFilters !== undefined) mongoOpts.arrayFilters = options.arrayFilters;\n  // explictly enumerate options that minimongo supports\n  if (options.upsert) mongoOpts.upsert = true;\n  if (options.multi) mongoOpts.multi = true;\n  // Lets you get a more more full result from MongoDB. Use with caution:\n  // might not work with C.upsert (as opposed to C.update({upsert:true}) or\n  // with simulated upsert.\n  if (options.fullResult) mongoOpts.fullResult = true;\n\n  var mongoSelector = replaceTypes(selector, replaceMeteorAtomWithMongo);\n  var mongoMod = replaceTypes(mod, replaceMeteorAtomWithMongo);\n\n  var isModify = LocalCollection._isModificationMod(mongoMod);\n\n  if (options._forbidReplace && !isModify) {\n    var err = new Error(\"Invalid modifier. Replacements are forbidden.\");\n    throw err;\n  }\n\n  // We've already run replaceTypes/replaceMeteorAtomWithMongo on\n  // selector and mod.  We assume it doesn't matter, as far as\n  // the behavior of modifiers is concerned, whether `_modify`\n  // is run on EJSON or on mongo-converted EJSON.\n\n  // Run this code up front so that it fails fast if someone uses\n  // a Mongo update operator we don't support.\n  let knownId;\n  if (options.upsert) {\n    try {\n      let newDoc = LocalCollection._createUpsertDocument(selector, mod);\n      knownId = newDoc._id;\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (options.upsert &&\n    ! isModify &&\n    ! knownId &&\n    options.insertedId &&\n    ! (options.insertedId instanceof Mongo.ObjectID &&\n      options.generatedId)) {\n    // In case of an upsert with a replacement, where there is no _id defined\n    // in either the query or the replacement doc, mongo will generate an id itself.\n    // Therefore we need this special strategy if we want to control the id ourselves.\n\n    // We don't need to do this when:\n    // - This is not a replacement, so we can add an _id to $setOnInsert\n    // - The id is defined by query or mod we can just add it to the replacement doc\n    // - The user did not specify any id preference and the id is a Mongo ObjectId,\n    //     then we can just let Mongo generate the id\n    return await simulateUpsertWithInsertedId(collection, mongoSelector, mongoMod, options)\n      .then(async result => {\n        await refresh();\n        await write.committed();\n        if (result && ! options._returnObject) {\n          return result.numberAffected;\n        } else {\n          return result;\n        }\n      });\n  } else {\n    if (options.upsert && !knownId && options.insertedId && isModify) {\n      if (!mongoMod.hasOwnProperty('$setOnInsert')) {\n        mongoMod.$setOnInsert = {};\n      }\n      knownId = options.insertedId;\n      Object.assign(mongoMod.$setOnInsert, replaceTypes({_id: options.insertedId}, replaceMeteorAtomWithMongo));\n    }\n\n    const strings = Object.keys(mongoMod).filter((key) => !key.startsWith(\"$\"));\n    let updateMethod = strings.length > 0 ? 'replaceOne' : 'updateMany';\n    updateMethod =\n      updateMethod === 'updateMany' && !mongoOpts.multi\n        ? 'updateOne'\n        : updateMethod;\n    return collection[updateMethod]\n      .bind(collection)(mongoSelector, mongoMod, mongoOpts)\n      .then(async result => {\n        var meteorResult = transformResult({result});\n        if (meteorResult && options._returnObject) {\n          // If this was an upsertAsync() call, and we ended up\n          // inserting a new doc and we know its id, then\n          // return that id as well.\n          if (options.upsert && meteorResult.insertedId) {\n            if (knownId) {\n              meteorResult.insertedId = knownId;\n            } else if (meteorResult.insertedId instanceof MongoDB.ObjectId) {\n              meteorResult.insertedId = new Mongo.ObjectID(meteorResult.insertedId.toHexString());\n            }\n          }\n          await refresh();\n          await write.committed();\n          return meteorResult;\n        } else {\n          await refresh();\n          await write.committed();\n          return meteorResult.numberAffected;\n        }\n      }).catch(async (err) => {\n        await write.committed();\n        throw err;\n      });\n  }\n};\n\n// exposed for testing\nMongoConnection._isCannotChangeIdError = function (err) {\n\n  // Mongo 3.2.* returns error as next Object:\n  // {name: String, code: Number, errmsg: String}\n  // Older Mongo returns:\n  // {name: String, code: Number, err: String}\n  var error = err.errmsg || err.err;\n\n  // We don't use the error code here\n  // because the error code we observed it producing (16837) appears to be\n  // a far more generic error code based on examining the source.\n  if (error.indexOf('The _id field cannot be changed') === 0\n    || error.indexOf(\"the (immutable) field '_id' was found to have been altered to _id\") !== -1) {\n    return true;\n  }\n\n  return false;\n};\n\n// XXX MongoConnection.upsertAsync() does not return the id of the inserted document\n// unless you set it explicitly in the selector or modifier (as a replacement\n// doc).\nMongoConnection.prototype.upsertAsync = async function (collectionName, selector, mod, options) {\n  var self = this;\n\n\n\n  if (typeof options === \"function\" && ! callback) {\n    callback = options;\n    options = {};\n  }\n\n  return self.updateAsync(collectionName, selector, mod,\n    Object.assign({}, options, {\n      upsert: true,\n      _returnObject: true\n    }));\n};\n\nMongoConnection.prototype.find = function (collectionName, selector, options) {\n  var self = this;\n\n  if (arguments.length === 1)\n    selector = {};\n\n  return new Cursor(\n    self, new CursorDescription(collectionName, selector, options));\n};\n\nMongoConnection.prototype.findOneAsync = async function (collection_name, selector, options) {\n  var self = this;\n  if (arguments.length === 1) {\n    selector = {};\n  }\n\n  options = options || {};\n  options.limit = 1;\n\n  const results = await self.find(collection_name, selector, options).fetch();\n\n  return results[0];\n};\n\n// We'll actually design an index API later. For now, we just pass through to\n// Mongo's, but make it synchronous.\nMongoConnection.prototype.createIndexAsync = async function (collectionName, index,\n                                                             options) {\n  var self = this;\n\n  // We expect this function to be called at startup, not from within a method,\n  // so we don't interact with the write fence.\n  var collection = self.rawCollection(collectionName);\n  await collection.createIndex(index, options);\n};\n\n// just to be consistent with the other methods\nMongoConnection.prototype.createIndex =\n  MongoConnection.prototype.createIndexAsync;\n\nMongoConnection.prototype.countDocuments = function (collectionName, ...args) {\n  args = args.map(arg => replaceTypes(arg, replaceMeteorAtomWithMongo));\n  const collection = this.rawCollection(collectionName);\n  return collection.countDocuments(...args);\n};\n\nMongoConnection.prototype.estimatedDocumentCount = function (collectionName, ...args) {\n  args = args.map(arg => replaceTypes(arg, replaceMeteorAtomWithMongo));\n  const collection = this.rawCollection(collectionName);\n  return collection.estimatedDocumentCount(...args);\n};\n\nMongoConnection.prototype.ensureIndexAsync = MongoConnection.prototype.createIndexAsync;\n\nMongoConnection.prototype.dropIndexAsync = async function (collectionName, index) {\n  var self = this;\n\n\n  // This function is only used by test code, not within a method, so we don't\n  // interact with the write fence.\n  var collection = self.rawCollection(collectionName);\n  var indexName =  await collection.dropIndex(index);\n};\n\n\nCLIENT_ONLY_METHODS.forEach(function (m) {\n  MongoConnection.prototype[m] = function () {\n    throw new Error(\n      `${m} +  is not available on the server. Please use ${getAsyncMethodName(\n        m\n      )}() instead.`\n    );\n  };\n});\n\n\nvar NUM_OPTIMISTIC_TRIES = 3;\n\n\n\nvar simulateUpsertWithInsertedId = async function (collection, selector, mod, options) {\n  // STRATEGY: First try doing an upsert with a generated ID.\n  // If this throws an error about changing the ID on an existing document\n  // then without affecting the database, we know we should probably try\n  // an update without the generated ID. If it affected 0 documents,\n  // then without affecting the database, we the document that first\n  // gave the error is probably removed and we need to try an insert again\n  // We go back to step one and repeat.\n  // Like all \"optimistic write\" schemes, we rely on the fact that it's\n  // unlikely our writes will continue to be interfered with under normal\n  // circumstances (though sufficiently heavy contention with writers\n  // disagreeing on the existence of an object will cause writes to fail\n  // in theory).\n\n  var insertedId = options.insertedId; // must exist\n  var mongoOptsForUpdate = {\n    safe: true,\n    multi: options.multi\n  };\n  var mongoOptsForInsert = {\n    safe: true,\n    upsert: true\n  };\n\n  var replacementWithId = Object.assign(\n    replaceTypes({_id: insertedId}, replaceMeteorAtomWithMongo),\n    mod);\n\n  var tries = NUM_OPTIMISTIC_TRIES;\n\n  var doUpdate = async function () {\n    tries--;\n    if (! tries) {\n      throw new Error(\"Upsert failed after \" + NUM_OPTIMISTIC_TRIES + \" tries.\");\n    } else {\n      let method = collection.updateMany;\n      if(!Object.keys(mod).some(key => key.startsWith(\"$\"))){\n        method = collection.replaceOne.bind(collection);\n      }\n      return method(\n        selector,\n        mod,\n        mongoOptsForUpdate).then(result => {\n        if (result && (result.modifiedCount || result.upsertedCount)) {\n          return {\n            numberAffected: result.modifiedCount || result.upsertedCount,\n            insertedId: result.upsertedId || undefined,\n          };\n        } else {\n          return doConditionalInsert();\n        }\n      });\n    }\n  };\n\n  var doConditionalInsert = function() {\n    return collection.replaceOne(selector, replacementWithId, mongoOptsForInsert)\n      .then(result => ({\n        numberAffected: result.upsertedCount,\n        insertedId: result.upsertedId,\n      })).catch(err => {\n        if (MongoConnection._isCannotChangeIdError(err)) {\n          return doUpdate();\n        } else {\n          throw err;\n        }\n      });\n\n  };\n  return doUpdate();\n};\n\n// observeChanges for tailable cursors on capped collections.\n//\n// Some differences from normal cursors:\n//   - Will never produce anything other than 'added' or 'addedBefore'. If you\n//     do update a document that has already been produced, this will not notice\n//     it.\n//   - If you disconnect and reconnect from Mongo, it will essentially restart\n//     the query, which will lead to duplicate results. This is pretty bad,\n//     but if you include a field called 'ts' which is inserted as\n//     new MongoInternals.MongoTimestamp(0, 0) (which is initialized to the\n//     current Mongo-style timestamp), we'll be able to find the place to\n//     restart properly. (This field is specifically understood by Mongo with an\n//     optimization which allows it to find the right place to start without\n//     an index on ts. It's how the oplog works.)\n//   - No callbacks are triggered synchronously with the call (there's no\n//     differentiation between \"initial data\" and \"later changes\"; everything\n//     that matches the query gets sent asynchronously).\n//   - De-duplication is not implemented.\n//   - Does not yet interact with the write fence. Probably, this should work by\n//     ignoring removes (which don't work on capped collections) and updates\n//     (which don't affect tailable cursors), and just keeping track of the ID\n//     of the inserted object, and closing the write fence once you get to that\n//     ID (or timestamp?).  This doesn't work well if the document doesn't match\n//     the query, though.  On the other hand, the write fence can close\n//     immediately if it does not match the query. So if we trust minimongo\n//     enough to accurately evaluate the query against the write fence, we\n//     should be able to do this...  Of course, minimongo doesn't even support\n//     Mongo Timestamps yet.\nMongoConnection.prototype._observeChangesTailable = function (\n  cursorDescription, ordered, callbacks) {\n  var self = this;\n\n  // Tailable cursors only ever call added/addedBefore callbacks, so it's an\n  // error if you didn't provide them.\n  if ((ordered && !callbacks.addedBefore) ||\n    (!ordered && !callbacks.added)) {\n    throw new Error(\"Can't observe an \" + (ordered ? \"ordered\" : \"unordered\")\n      + \" tailable cursor without a \"\n      + (ordered ? \"addedBefore\" : \"added\") + \" callback\");\n  }\n\n  return self.tail(cursorDescription, function (doc) {\n    var id = doc._id;\n    delete doc._id;\n    // The ts is an implementation detail. Hide it.\n    delete doc.ts;\n    if (ordered) {\n      callbacks.addedBefore(id, doc, null);\n    } else {\n      callbacks.added(id, doc);\n    }\n  });\n};\n\nMongoConnection.prototype._createAsynchronousCursor = function(\n  cursorDescription, options = {}) {\n  var self = this;\n  const { selfForIteration, useTransform } = options;\n  options = { selfForIteration, useTransform };\n\n  var collection = self.rawCollection(cursorDescription.collectionName);\n  var cursorOptions = cursorDescription.options;\n  var mongoOptions = {\n    sort: cursorOptions.sort,\n    limit: cursorOptions.limit,\n    skip: cursorOptions.skip,\n    projection: cursorOptions.fields || cursorOptions.projection,\n    readPreference: cursorOptions.readPreference,\n  };\n\n  // Do we want a tailable cursor (which only works on capped collections)?\n  if (cursorOptions.tailable) {\n    mongoOptions.numberOfRetries = -1;\n  }\n\n  var dbCursor = collection.find(\n    replaceTypes(cursorDescription.selector, replaceMeteorAtomWithMongo),\n    mongoOptions);\n\n  // Do we want a tailable cursor (which only works on capped collections)?\n  if (cursorOptions.tailable) {\n    // We want a tailable cursor...\n    dbCursor.addCursorFlag(\"tailable\", true)\n    // ... and for the server to wait a bit if any getMore has no data (rather\n    // than making us put the relevant sleeps in the client)...\n    dbCursor.addCursorFlag(\"awaitData\", true)\n\n    // And if this is on the oplog collection and the cursor specifies a 'ts',\n    // then set the undocumented oplog replay flag, which does a special scan to\n    // find the first document (instead of creating an index on ts). This is a\n    // very hard-coded Mongo flag which only works on the oplog collection and\n    // only works with the ts field.\n    if (cursorDescription.collectionName === OPLOG_COLLECTION &&\n      cursorDescription.selector.ts) {\n      dbCursor.addCursorFlag(\"oplogReplay\", true)\n    }\n  }\n\n  if (typeof cursorOptions.maxTimeMs !== 'undefined') {\n    dbCursor = dbCursor.maxTimeMS(cursorOptions.maxTimeMs);\n  }\n  if (typeof cursorOptions.hint !== 'undefined') {\n    dbCursor = dbCursor.hint(cursorOptions.hint);\n  }\n\n  return new AsynchronousCursor(dbCursor, cursorDescription, options, collection);\n};\n\n// Tails the cursor described by cursorDescription, most likely on the\n// oplog. Calls docCallback with each document found. Ignores errors and just\n// restarts the tail on error.\n//\n// If timeoutMS is set, then if we don't get a new document every timeoutMS,\n// kill and restart the cursor. This is primarily a workaround for #8598.\nMongoConnection.prototype.tail = function (cursorDescription, docCallback, timeoutMS) {\n  var self = this;\n  if (!cursorDescription.options.tailable)\n    throw new Error(\"Can only tail a tailable cursor\");\n\n  var cursor = self._createAsynchronousCursor(cursorDescription);\n\n  var stopped = false;\n  var lastTS;\n\n  Meteor.defer(async function loop() {\n    var doc = null;\n    while (true) {\n      if (stopped)\n        return;\n      try {\n        doc = await cursor._nextObjectPromiseWithTimeout(timeoutMS);\n      } catch (err) {\n        // We should not ignore errors here unless we want to spend a lot of time debugging\n        console.error(err);\n        // There's no good way to figure out if this was actually an error from\n        // Mongo, or just client-side (including our own timeout error). Ah\n        // well. But either way, we need to retry the cursor (unless the failure\n        // was because the observe got stopped).\n        doc = null;\n      }\n      // Since we awaited a promise above, we need to check again to see if\n      // we've been stopped before calling the callback.\n      if (stopped)\n        return;\n      if (doc) {\n        // If a tailable cursor contains a \"ts\" field, use it to recreate the\n        // cursor on error. (\"ts\" is a standard that Mongo uses internally for\n        // the oplog, and there's a special flag that lets you do binary search\n        // on it instead of needing to use an index.)\n        lastTS = doc.ts;\n        docCallback(doc);\n      } else {\n        var newSelector = Object.assign({}, cursorDescription.selector);\n        if (lastTS) {\n          newSelector.ts = {$gt: lastTS};\n        }\n        cursor = self._createAsynchronousCursor(new CursorDescription(\n          cursorDescription.collectionName,\n          newSelector,\n          cursorDescription.options));\n        // Mongo failover takes many seconds.  Retry in a bit.  (Without this\n        // setTimeout, we peg the CPU at 100% and never notice the actual\n        // failover.\n        setTimeout(loop, 100);\n        break;\n      }\n    }\n  });\n\n  return {\n    stop: function () {\n      stopped = true;\n      cursor.close();\n    }\n  };\n};\n\nObject.assign(MongoConnection.prototype, {\n  _observeChanges: async function (\n    cursorDescription, ordered, callbacks, nonMutatingCallbacks) {\n    var self = this;\n    const collectionName = cursorDescription.collectionName;\n\n    if (cursorDescription.options.tailable) {\n      return self._observeChangesTailable(cursorDescription, ordered, callbacks);\n    }\n\n    // You may not filter out _id when observing changes, because the id is a core\n    // part of the observeChanges API.\n    const fieldsOptions = cursorDescription.options.projection || cursorDescription.options.fields;\n    if (fieldsOptions &&\n      (fieldsOptions._id === 0 ||\n        fieldsOptions._id === false)) {\n      throw Error(\"You may not observe a cursor with {fields: {_id: 0}}\");\n    }\n\n    var observeKey = EJSON.stringify(\n      Object.assign({ordered: ordered}, cursorDescription));\n\n    var multiplexer, observeDriver;\n    var firstHandle = false;\n\n    // Find a matching ObserveMultiplexer, or create a new one. This next block is\n    // guaranteed to not yield (and it doesn't call anything that can observe a\n    // new query), so no other calls to this function can interleave with it.\n    if (observeKey in self._observeMultiplexers) {\n      multiplexer = self._observeMultiplexers[observeKey];\n    } else {\n      firstHandle = true;\n      // Create a new ObserveMultiplexer.\n      multiplexer = new ObserveMultiplexer({\n        ordered: ordered,\n        onStop: function () {\n          delete self._observeMultiplexers[observeKey];\n          return observeDriver.stop();\n        }\n      });\n    }\n\n    var observeHandle = new ObserveHandle(multiplexer,\n      callbacks,\n      nonMutatingCallbacks,\n    );\n\n    const oplogOptions = self?._oplogHandle?._oplogOptions || {};\n    const { includeCollections, excludeCollections } = oplogOptions;\n    if (firstHandle) {\n      var matcher, sorter;\n      var canUseOplog = [\n        function () {\n          // At a bare minimum, using the oplog requires us to have an oplog, to\n          // want unordered callbacks, and to not want a callback on the polls\n          // that won't happen.\n          return self._oplogHandle && !ordered &&\n            !callbacks._testOnlyPollCallback;\n        },\n        function () {\n          // We also need to check, if the collection of this Cursor is actually being \"watched\" by the Oplog handle\n          // if not, we have to fallback to long polling\n          if (excludeCollections?.length && excludeCollections.includes(collectionName)) {\n            if (!oplogCollectionWarnings.includes(collectionName)) {\n              console.warn(`Meteor.settings.packages.mongo.oplogExcludeCollections includes the collection ${collectionName} - your subscriptions will only use long polling!`);\n              oplogCollectionWarnings.push(collectionName); // we only want to show the warnings once per collection!\n            }\n            return false;\n          }\n          if (includeCollections?.length && !includeCollections.includes(collectionName)) {\n            if (!oplogCollectionWarnings.includes(collectionName)) {\n              console.warn(`Meteor.settings.packages.mongo.oplogIncludeCollections does not include the collection ${collectionName} - your subscriptions will only use long polling!`);\n              oplogCollectionWarnings.push(collectionName); // we only want to show the warnings once per collection!\n            }\n            return false;\n          }\n          return true;\n        },\n        function () {\n          // We need to be able to compile the selector. Fall back to polling for\n          // some newfangled $selector that minimongo doesn't support yet.\n          try {\n            matcher = new Minimongo.Matcher(cursorDescription.selector);\n            return true;\n          } catch (e) {\n            // XXX make all compilation errors MinimongoError or something\n            //     so that this doesn't ignore unrelated exceptions\n            return false;\n          }\n        },\n        function () {\n          // ... and the selector itself needs to support oplog.\n          return OplogObserveDriver.cursorSupported(cursorDescription, matcher);\n        },\n        function () {\n          // And we need to be able to compile the sort, if any.  eg, can't be\n          // {$natural: 1}.\n          if (!cursorDescription.options.sort)\n            return true;\n          try {\n            sorter = new Minimongo.Sorter(cursorDescription.options.sort);\n            return true;\n          } catch (e) {\n            // XXX make all compilation errors MinimongoError or something\n            //     so that this doesn't ignore unrelated exceptions\n            return false;\n          }\n        }\n      ].every(f => f());  // invoke each function and check if all return true\n\n      var driverClass = canUseOplog ? OplogObserveDriver : PollingObserveDriver;\n      observeDriver = new driverClass({\n        cursorDescription: cursorDescription,\n        mongoHandle: self,\n        multiplexer: multiplexer,\n        ordered: ordered,\n        matcher: matcher,  // ignored by polling\n        sorter: sorter,  // ignored by polling\n        _testOnlyPollCallback: callbacks._testOnlyPollCallback\n      });\n\n      if (observeDriver._init) {\n        await observeDriver._init();\n      }\n\n      // This field is only set for use in tests.\n      multiplexer._observeDriver = observeDriver;\n    }\n    self._observeMultiplexers[observeKey] = multiplexer;\n    // Blocks until the initial adds have been sent.\n    await multiplexer.addHandleAndSendInitialAdds(observeHandle);\n\n    return observeHandle;\n  },\n\n});\n", "import clone from 'lodash.clone'\n\n/** @type {import('mongodb')} */\nexport const MongoDB = Object.assign(NpmModuleMongodb, {\n  ObjectID: NpmModuleMongodb.ObjectId,\n});\n\n// The write methods block until the database has confirmed the write (it may\n// not be replicated or stable on disk, but one server has confirmed it) if no\n// callback is provided. If a callback is provided, then they call the callback\n// when the write is confirmed. They return nothing on success, and raise an\n// exception on failure.\n//\n// After making a write (with insert, update, remove), observers are\n// notified asynchronously. If you want to receive a callback once all\n// of the observer notifications have landed for your write, do the\n// writes inside a write fence (set DDPServer._CurrentWriteFence to a new\n// _WriteFence, and then set a callback on the write fence.)\n//\n// Since our execution environment is single-threaded, this is\n// well-defined -- a write \"has been made\" if it's returned, and an\n// observer \"has been notified\" if its callback has returned.\n\nexport const writeCallback = function (write, refresh, callback) {\n  return function (err, result) {\n    if (! err) {\n      // XXX We don't have to run this on error, right?\n      try {\n        refresh();\n      } catch (refreshErr) {\n        if (callback) {\n          callback(refreshErr);\n          return;\n        } else {\n          throw refreshErr;\n        }\n      }\n    }\n    write.committed();\n    if (callback) {\n      callback(err, result);\n    } else if (err) {\n      throw err;\n    }\n  };\n};\n\n\nexport const transformResult = function (driverResult) {\n  var meteorResult = { numberAffected: 0 };\n  if (driverResult) {\n    var mongoResult = driverResult.result;\n    // On updates with upsert:true, the inserted values come as a list of\n    // upserted values -- even with options.multi, when the upsert does insert,\n    // it only inserts one element.\n    if (mongoResult.upsertedCount) {\n      meteorResult.numberAffected = mongoResult.upsertedCount;\n\n      if (mongoResult.upsertedId) {\n        meteorResult.insertedId = mongoResult.upsertedId;\n      }\n    } else {\n      // n was used before Mongo 5.0, in Mongo 5.0 we are not receiving this n\n      // field and so we are using modifiedCount instead\n      meteorResult.numberAffected = mongoResult.n || mongoResult.matchedCount || mongoResult.modifiedCount;\n    }\n  }\n\n  return meteorResult;\n};\n\nexport const replaceMeteorAtomWithMongo = function (document) {\n  if (EJSON.isBinary(document)) {\n    // This does more copies than we'd like, but is necessary because\n    // MongoDB.BSON only looks like it takes a Uint8Array (and doesn't actually\n    // serialize it correctly).\n    return new MongoDB.Binary(Buffer.from(document));\n  }\n  if (document instanceof MongoDB.Binary) {\n    return document;\n  }\n  if (document instanceof Mongo.ObjectID) {\n    return new MongoDB.ObjectId(document.toHexString());\n  }\n  if (document instanceof MongoDB.ObjectId) {\n    return new MongoDB.ObjectId(document.toHexString());\n  }\n  if (document instanceof MongoDB.Timestamp) {\n    // For now, the Meteor representation of a Mongo timestamp type (not a date!\n    // this is a weird internal thing used in the oplog!) is the same as the\n    // Mongo representation. We need to do this explicitly or else we would do a\n    // structural clone and lose the prototype.\n    return document;\n  }\n  if (document instanceof Decimal) {\n    return MongoDB.Decimal128.fromString(document.toString());\n  }\n  if (EJSON._isCustomType(document)) {\n    return replaceNames(makeMongoLegal, EJSON.toJSONValue(document));\n  }\n  // It is not ordinarily possible to stick dollar-sign keys into mongo\n  // so we don't bother checking for things that need escaping at this time.\n  return undefined;\n};\n\nexport const replaceTypes = function (document, atomTransformer) {\n  if (typeof document !== 'object' || document === null)\n    return document;\n\n  var replacedTopLevelAtom = atomTransformer(document);\n  if (replacedTopLevelAtom !== undefined)\n    return replacedTopLevelAtom;\n\n  var ret = document;\n  Object.entries(document).forEach(function ([key, val]) {\n    var valReplaced = replaceTypes(val, atomTransformer);\n    if (val !== valReplaced) {\n      // Lazy clone. Shallow copy.\n      if (ret === document)\n        ret = clone(document);\n      ret[key] = valReplaced;\n    }\n  });\n  return ret;\n};\n\nexport const replaceMongoAtomWithMeteor = function (document) {\n  if (document instanceof MongoDB.Binary) {\n    // for backwards compatibility\n    if (document.sub_type !== 0) {\n      return document;\n    }\n    var buffer = document.value(true);\n    return new Uint8Array(buffer);\n  }\n  if (document instanceof MongoDB.ObjectId) {\n    return new Mongo.ObjectID(document.toHexString());\n  }\n  if (document instanceof MongoDB.Decimal128) {\n    return Decimal(document.toString());\n  }\n  if (document[\"EJSON$type\"] && document[\"EJSON$value\"] && Object.keys(document).length === 2) {\n    return EJSON.fromJSONValue(replaceNames(unmakeMongoLegal, document));\n  }\n  if (document instanceof MongoDB.Timestamp) {\n    // For now, the Meteor representation of a Mongo timestamp type (not a date!\n    // this is a weird internal thing used in the oplog!) is the same as the\n    // Mongo representation. We need to do this explicitly or else we would do a\n    // structural clone and lose the prototype.\n    return document;\n  }\n  return undefined;\n};\n\nconst makeMongoLegal = name => \"EJSON\" + name;\nconst unmakeMongoLegal = name => name.substr(5);\n\nexport function replaceNames(filter, thing) {\n  if (typeof thing === \"object\" && thing !== null) {\n    if (Array.isArray(thing)) {\n      return thing.map(replaceNames.bind(null, filter));\n    }\n    var ret = {};\n    Object.entries(thing).forEach(function ([key, value]) {\n      ret[filter(key)] = replaceNames(filter, value);\n    });\n    return ret;\n  }\n  return thing;\n}\n", "import LocalCollection from 'meteor/minimongo/local_collection';\nimport { replaceMongoAtomWithMeteor, replaceTypes } from './mongo_common';\n\n/**\n * This is just a light wrapper for the cursor. The goal here is to ensure compatibility even if\n * there are breaking changes on the MongoDB driver.\n *\n * This is an internal implementation detail and is created lazily by the main Cursor class.\n */\nexport class AsynchronousCursor {\n  constructor(dbCursor, cursorDescription, options) {\n    this._dbCursor = dbCursor;\n    this._cursorDescription = cursorDescription;\n\n    this._selfForIteration = options.selfForIteration || this;\n    if (options.useTransform && cursorDescription.options.transform) {\n      this._transform = LocalCollection.wrapTransform(\n        cursorDescription.options.transform);\n    } else {\n      this._transform = null;\n    }\n\n    this._visitedIds = new LocalCollection._IdMap;\n  }\n\n  [Symbol.asyncIterator]() {\n    var cursor = this;\n    return {\n      async next() {\n        const value = await cursor._nextObjectPromise();\n        return { done: !value, value };\n      },\n    };\n  }\n\n  // Returns a Promise for the next object from the underlying cursor (before\n  // the Mongo->Meteor type replacement).\n  async _rawNextObjectPromise() {\n    try {\n      return this._dbCursor.next();\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  // Returns a Promise for the next object from the cursor, skipping those whose\n  // IDs we've already seen and replacing Mongo atoms with Meteor atoms.\n  async _nextObjectPromise () {\n    while (true) {\n      var doc = await this._rawNextObjectPromise();\n\n      if (!doc) return null;\n      doc = replaceTypes(doc, replaceMongoAtomWithMeteor);\n\n      if (!this._cursorDescription.options.tailable && '_id' in doc) {\n        // Did Mongo give us duplicate documents in the same cursor? If so,\n        // ignore this one. (Do this before the transform, since transform might\n        // return some unrelated value.) We don't do this for tailable cursors,\n        // because we want to maintain O(1) memory usage. And if there isn't _id\n        // for some reason (maybe it's the oplog), then we don't do this either.\n        // (Be careful to do this for falsey but existing _id, though.)\n        if (this._visitedIds.has(doc._id)) continue;\n        this._visitedIds.set(doc._id, true);\n      }\n\n      if (this._transform)\n        doc = this._transform(doc);\n\n      return doc;\n    }\n  }\n\n  // Returns a promise which is resolved with the next object (like with\n  // _nextObjectPromise) or rejected if the cursor doesn't return within\n  // timeoutMS ms.\n  _nextObjectPromiseWithTimeout(timeoutMS) {\n    if (!timeoutMS) {\n      return this._nextObjectPromise();\n    }\n    const nextObjectPromise = this._nextObjectPromise();\n    const timeoutErr = new Error('Client-side timeout waiting for next object');\n    const timeoutPromise = new Promise((resolve, reject) => {\n      setTimeout(() => {\n        reject(timeoutErr);\n      }, timeoutMS);\n    });\n    return Promise.race([nextObjectPromise, timeoutPromise])\n      .catch((err) => {\n        if (err === timeoutErr) {\n          this.close();\n          return;\n        }\n        throw err;\n      });\n  }\n\n  async forEach(callback, thisArg) {\n    // Get back to the beginning.\n    this._rewind();\n\n    let idx = 0;\n    while (true) {\n      const doc = await this._nextObjectPromise();\n      if (!doc) return;\n      await callback.call(thisArg, doc, idx++, this._selfForIteration);\n    }\n  }\n\n  async map(callback, thisArg) {\n    const results = [];\n    await this.forEach(async (doc, index) => {\n      results.push(await callback.call(thisArg, doc, index, this._selfForIteration));\n    });\n\n    return results;\n  }\n\n  _rewind() {\n    // known to be synchronous\n    this._dbCursor.rewind();\n\n    this._visitedIds = new LocalCollection._IdMap;\n  }\n\n  // Mostly usable for tailable cursors.\n  close() {\n    this._dbCursor.close();\n  }\n\n  fetch() {\n    return this.map(doc => doc);\n  }\n\n  /**\n   * FIXME: (node:34680) [MONGODB DRIVER] Warning: cursor.count is deprecated and will be\n   *  removed in the next major version, please use `collection.estimatedDocumentCount` or\n   *  `collection.countDocuments` instead.\n   */\n  count() {\n    return this._dbCursor.count();\n  }\n\n  // This method is NOT wrapped in Cursor.\n  async getRawObjects(ordered) {\n    var self = this;\n    if (ordered) {\n      return self.fetch();\n    } else {\n      var results = new LocalCollection._IdMap;\n      await self.forEach(function (doc) {\n        results.set(doc._id, doc);\n      });\n      return results;\n    }\n  }\n}", "import { ASYNC_CURSOR_METHODS, getAsyncMethodName } from 'meteor/minimongo/constants';\nimport { replaceMeteorAtomWithMongo, replaceTypes } from './mongo_common';\nimport LocalCollection from 'meteor/minimongo/local_collection';\nimport { CursorDescription } from './cursor_description';\nimport { ObserveCallbacks, ObserveChangesCallbacks } from './types';\n\ninterface MongoInterface {\n  rawCollection: (collectionName: string) => any;\n  _createAsynchronousCursor: (cursorDescription: CursorDescription, options: CursorOptions) => any;\n  _observeChanges: (cursorDescription: CursorDescription, ordered: boolean, callbacks: any, nonMutatingCallbacks?: boolean) => any;\n}\n\ninterface CursorOptions {\n  selfForIteration: Cursor<any>;\n  useTransform: boolean;\n}\n\n/**\n * @class Cursor\n *\n * The main cursor object returned from find(), implementing the documented\n * Mongo.Collection cursor API.\n *\n * Wraps a CursorDescription and lazily creates an AsynchronousCursor\n * (only contacts MongoDB when methods like fetch or forEach are called).\n */\nexport class Cursor<T, U = T> {\n  public _mongo: MongoInterface;\n  public _cursorDescription: CursorDescription;\n  public _synchronousCursor: any | null;\n\n  constructor(mongo: MongoInterface, cursorDescription: CursorDescription) {\n    this._mongo = mongo;\n    this._cursorDescription = cursorDescription;\n    this._synchronousCursor = null;\n  }\n\n  async countAsync(): Promise<number> {\n    const collection = this._mongo.rawCollection(this._cursorDescription.collectionName);\n    return await collection.countDocuments(\n      replaceTypes(this._cursorDescription.selector, replaceMeteorAtomWithMongo),\n      replaceTypes(this._cursorDescription.options, replaceMeteorAtomWithMongo),\n    );\n  }\n\n  count(): never {\n    throw new Error(\n      \"count() is not available on the server. Please use countAsync() instead.\"\n    );\n  }\n\n  getTransform(): ((doc: any) => any) | undefined {\n    return this._cursorDescription.options.transform;\n  }\n\n  _publishCursor(sub: any): any {\n    const collection = this._cursorDescription.collectionName;\n    return Mongo.Collection._publishCursor(this, sub, collection);\n  }\n\n  _getCollectionName(): string {\n    return this._cursorDescription.collectionName;\n  }\n\n  observe(callbacks: ObserveCallbacks<U>): any {\n    return LocalCollection._observeFromObserveChanges(this, callbacks);\n  }\n\n  async observeAsync(callbacks: ObserveCallbacks<U>): Promise<any> {\n    return new Promise(resolve => resolve(this.observe(callbacks)));\n  }\n\n  observeChanges(callbacks: ObserveChangesCallbacks<U>, options: { nonMutatingCallbacks?: boolean } = {}): any {\n    const ordered = LocalCollection._observeChangesCallbacksAreOrdered(callbacks);\n    return this._mongo._observeChanges(\n      this._cursorDescription,\n      ordered,\n      callbacks,\n      options.nonMutatingCallbacks\n    );\n  }\n\n  async observeChangesAsync(callbacks: ObserveChangesCallbacks<U>, options: { nonMutatingCallbacks?: boolean } = {}): Promise<any> {\n    return this.observeChanges(callbacks, options);\n  }\n}\n\n// Add cursor methods dynamically\n[...ASYNC_CURSOR_METHODS, Symbol.iterator, Symbol.asyncIterator].forEach(methodName => {\n  if (methodName === 'count') return;\n\n  (Cursor.prototype as any)[methodName] = function(this: Cursor<any>, ...args: any[]): any {\n    const cursor = setupAsynchronousCursor(this, methodName);\n    return cursor[methodName](...args);\n  };\n\n  if (methodName === Symbol.iterator || methodName === Symbol.asyncIterator) return;\n\n  const methodNameAsync = getAsyncMethodName(methodName);\n\n  (Cursor.prototype as any)[methodNameAsync] = function(this: Cursor<any>, ...args: any[]): Promise<any> {\n    return this[methodName](...args);\n  };\n});\n\nfunction setupAsynchronousCursor(cursor: Cursor<any>, method: string | symbol): any {\n  if (cursor._cursorDescription.options.tailable) {\n    throw new Error(`Cannot call ${String(method)} on a tailable cursor`);\n  }\n\n  if (!cursor._synchronousCursor) {\n    cursor._synchronousCursor = cursor._mongo._createAsynchronousCursor(\n      cursor._cursorDescription,\n      {\n        selfForIteration: cursor,\n        useTransform: true,\n      }\n    );\n  }\n\n  return cursor._synchronousCursor;\n}", "// singleton\nexport const LocalCollectionDriver = new (class LocalCollectionDriver {\n  constructor() {\n    this.noConnCollections = Object.create(null);\n  }\n\n  open(name, conn) {\n    if (! name) {\n      return new LocalCollection;\n    }\n\n    if (! conn) {\n      return ensureCollection(name, this.noConnCollections);\n    }\n\n    if (! conn._mongo_livedata_collections) {\n      conn._mongo_livedata_collections = Object.create(null);\n    }\n\n    // XXX is there a way to keep track of a connection's collections without\n    // dangling it off the connection object?\n    return ensureCollection(name, conn._mongo_livedata_collections);\n  }\n});\n\nfunction ensureCollection(name, collections) {\n  return (name in collections)\n    ? collections[name]\n    : collections[name] = new LocalCollection(name);\n}\n", "import once from 'lodash.once';\nimport {\n  ASYNC_COLLECTION_METHODS,\n  getAsyncMethodName,\n  CLIENT_ONLY_METHODS\n} from \"meteor/minimongo/constants\";\nimport { MongoConnection } from './mongo_connection';\n\n// Define interfaces and types\ninterface IConnectionOptions {\n  oplogUrl?: string;\n  [key: string]: unknown;  // Changed from 'any' to 'unknown' for better type safety\n}\n\ninterface IMongoInternals {\n  RemoteCollectionDriver: typeof RemoteCollectionDriver;\n  defaultRemoteCollectionDriver: () => RemoteCollectionDriver;\n}\n\n// More specific typing for collection methods\ntype MongoMethodFunction = (...args: unknown[]) => unknown;\ninterface ICollectionMethods {\n  [key: string]: MongoMethodFunction;\n}\n\n// Type for MongoConnection\ninterface IMongoClient {\n  connect: () => Promise<void>;\n}\n\ninterface IMongoConnection {\n  client: IMongoClient;\n  [key: string]: MongoMethodFunction | IMongoClient;\n}\n\ndeclare global {\n  namespace NodeJS {\n    interface ProcessEnv {\n      MONGO_URL: string;\n      MONGO_OPLOG_URL?: string;\n    }\n  }\n\n  const MongoInternals: IMongoInternals;\n  const Meteor: {\n    startup: (callback: () => Promise<void>) => void;\n  };\n}\n\nclass RemoteCollectionDriver {\n  private readonly mongo: MongoConnection;\n\n  private static readonly REMOTE_COLLECTION_METHODS = [\n    'createCappedCollectionAsync',\n    'dropIndexAsync',\n    'ensureIndexAsync',\n    'createIndexAsync',\n    'countDocuments',\n    'dropCollectionAsync',\n    'estimatedDocumentCount',\n    'find',\n    'findOneAsync',\n    'insertAsync',\n    'rawCollection',\n    'removeAsync',\n    'updateAsync',\n    'upsertAsync',\n  ] as const;\n\n  constructor(mongoUrl: string, options: IConnectionOptions) {\n    this.mongo = new MongoConnection(mongoUrl, options);\n  }\n\n  public open(name: string): ICollectionMethods {\n    const ret: ICollectionMethods = {};\n\n    // Handle remote collection methods\n    RemoteCollectionDriver.REMOTE_COLLECTION_METHODS.forEach((method) => {\n      // Type assertion needed because we know these methods exist on MongoConnection\n      const mongoMethod = this.mongo[method] as MongoMethodFunction;\n      ret[method] = mongoMethod.bind(this.mongo, name);\n\n      if (!ASYNC_COLLECTION_METHODS.includes(method)) return;\n\n      const asyncMethodName = getAsyncMethodName(method);\n      ret[asyncMethodName] = (...args: unknown[]) => ret[method](...args);\n    });\n\n    // Handle client-only methods\n    CLIENT_ONLY_METHODS.forEach((method) => {\n      ret[method] = (...args: unknown[]): never => {\n        throw new Error(\n          `${method} is not available on the server. Please use ${getAsyncMethodName(\n            method\n          )}() instead.`\n        );\n      };\n    });\n\n    return ret;\n  }\n}\n\n// Assign the class to MongoInternals\nMongoInternals.RemoteCollectionDriver = RemoteCollectionDriver;\n\n// Create the singleton RemoteCollectionDriver only on demand\nMongoInternals.defaultRemoteCollectionDriver = once((): RemoteCollectionDriver => {\n  const connectionOptions: IConnectionOptions = {};\n  const mongoUrl = process.env.MONGO_URL;\n\n  if (!mongoUrl) {\n    throw new Error(\"MONGO_URL must be set in environment\");\n  }\n\n  if (process.env.MONGO_OPLOG_URL) {\n    connectionOptions.oplogUrl = process.env.MONGO_OPLOG_URL;\n  }\n\n  const driver = new RemoteCollectionDriver(mongoUrl, connectionOptions);\n\n  // Initialize database connection on startup\n  Meteor.startup(async (): Promise<void> => {\n    await driver.mongo.client.connect();\n  });\n\n  return driver;\n});\n\nexport { RemoteCollectionDriver, IConnectionOptions, ICollectionMethods };", "import { normalizeProjection } from \"../mongo_utils\";\nimport { AsyncMethods } from './methods_async';\nimport { SyncMethods } from './methods_sync';\nimport { IndexMethods } from './methods_index';\nimport {\n  ID_GENERATORS, normalizeOptions,\n  setupAutopublish,\n  setupConnection,\n  setupDriver,\n  setupMutationMethods,\n  validateCollectionName\n} from './collection_utils';\nimport { ReplicationMethods } from './methods_replication';\n\n/**\n * @summary Namespace for MongoDB-related items\n * @namespace\n */\nMongo = {};\n\n/**\n * @summary Constructor for a Collection\n * @locus Anywhere\n * @instancename collection\n * @class\n * @param {String} name The name of the collection.  If null, creates an unmanaged (unsynchronized) local collection.\n * @param {Object} [options]\n * @param {Object} options.connection The server connection that will manage this collection. Uses the default connection if not specified.  Pass the return value of calling [`DDP.connect`](#DDP-connect) to specify a different server. Pass `null` to specify no connection. Unmanaged (`name` is null) collections cannot specify a connection.\n * @param {String} options.idGeneration The method of generating the `_id` fields of new documents in this collection.  Possible values:\n\n - **`'STRING'`**: random strings\n - **`'MONGO'`**:  random [`Mongo.ObjectID`](#mongo_object_id) values\n\nThe default id generation technique is `'STRING'`.\n * @param {Function} options.transform An optional transformation function. Documents will be passed through this function before being returned from `fetch` or `findOneAsync`, and before being passed to callbacks of `observe`, `map`, `forEach`, `allow`, and `deny`. Transforms are *not* applied for the callbacks of `observeChanges` or to cursors returned from publish functions.\n * @param {Boolean} options.defineMutationMethods Set to `false` to skip setting up the mutation methods that enable insert/update/remove from client code. Default `true`.\n */\n// Main Collection constructor\nMongo.Collection = function Collection(name, options) {\n  name = validateCollectionName(name);\n\n  options = normalizeOptions(options);\n\n  this._makeNewID = ID_GENERATORS[options.idGeneration]?.(name);\n\n  this._transform = LocalCollection.wrapTransform(options.transform);\n  this.resolverType = options.resolverType;\n\n  this._connection = setupConnection(name, options);\n\n  const driver = setupDriver(name, this._connection, options);\n  this._driver = driver;\n\n  this._collection = driver.open(name, this._connection);\n  this._name = name;\n\n  this._settingUpReplicationPromise = this._maybeSetUpReplication(name, options);\n\n  setupMutationMethods(this, name, options);\n\n  setupAutopublish(this, name, options);\n\n  Mongo._collections.set(name, this);\n};\n\nObject.assign(Mongo.Collection.prototype, {\n  _getFindSelector(args) {\n    if (args.length == 0) return {};\n    else return args[0];\n  },\n\n  _getFindOptions(args) {\n    const [, options] = args || [];\n    const newOptions = normalizeProjection(options);\n\n    var self = this;\n    if (args.length < 2) {\n      return { transform: self._transform };\n    } else {\n      check(\n        newOptions,\n        Match.Optional(\n          Match.ObjectIncluding({\n            projection: Match.Optional(Match.OneOf(Object, undefined)),\n            sort: Match.Optional(\n              Match.OneOf(Object, Array, Function, undefined)\n            ),\n            limit: Match.Optional(Match.OneOf(Number, undefined)),\n            skip: Match.Optional(Match.OneOf(Number, undefined)),\n          })\n        )\n      );\n\n      return {\n        transform: self._transform,\n        ...newOptions,\n      };\n    }\n  },\n});\n\nObject.assign(Mongo.Collection, {\n  async _publishCursor(cursor, sub, collection) {\n    var observeHandle = await cursor.observeChanges(\n        {\n          added: function(id, fields) {\n            sub.added(collection, id, fields);\n          },\n          changed: function(id, fields) {\n            sub.changed(collection, id, fields);\n          },\n          removed: function(id) {\n            sub.removed(collection, id);\n          },\n        },\n        // Publications don't mutate the documents\n        // This is tested by the `livedata - publish callbacks clone` test\n        { nonMutatingCallbacks: true }\n    );\n\n    // We don't call sub.ready() here: it gets called in livedata_server, after\n    // possibly calling _publishCursor on multiple returned cursors.\n\n    // register stop callback (expects lambda w/ no args).\n    sub.onStop(async function() {\n      return await observeHandle.stop();\n    });\n\n    // return the observeHandle in case it needs to be stopped early\n    return observeHandle;\n  },\n\n  // protect against dangerous selectors.  falsey and {_id: falsey} are both\n  // likely programmer error, and not what you want, particularly for destructive\n  // operations. If a falsey _id is sent in, a new string _id will be\n  // generated and returned; if a fallbackId is provided, it will be returned\n  // instead.\n  _rewriteSelector(selector, { fallbackId } = {}) {\n    // shorthand -- scalars match _id\n    if (LocalCollection._selectorIsId(selector)) selector = { _id: selector };\n\n    if (Array.isArray(selector)) {\n      // This is consistent with the Mongo console itself; if we don't do this\n      // check passing an empty array ends up selecting all items\n      throw new Error(\"Mongo selector can't be an array.\");\n    }\n\n    if (!selector || ('_id' in selector && !selector._id)) {\n      // can't match anything\n      return { _id: fallbackId || Random.id() };\n    }\n\n    return selector;\n  },\n});\n\nObject.assign(Mongo.Collection.prototype, ReplicationMethods, SyncMethods, AsyncMethods, IndexMethods);\n\nObject.assign(Mongo.Collection.prototype, {\n  // Determine if this collection is simply a minimongo representation of a real\n  // database on another server\n  _isRemoteCollection() {\n    // XXX see #MeteorServerNull\n    return this._connection && this._connection !== Meteor.server;\n  },\n\n  async dropCollectionAsync() {\n    var self = this;\n    if (!self._collection.dropCollectionAsync)\n      throw new Error('Can only call dropCollectionAsync on server collections');\n   await self._collection.dropCollectionAsync();\n  },\n\n  async createCappedCollectionAsync(byteSize, maxDocuments) {\n    var self = this;\n    if (! await self._collection.createCappedCollectionAsync)\n      throw new Error(\n        'Can only call createCappedCollectionAsync on server collections'\n      );\n    await self._collection.createCappedCollectionAsync(byteSize, maxDocuments);\n  },\n\n  /**\n   * @summary Returns the [`Collection`](http://mongodb.github.io/node-mongodb-native/3.0/api/Collection.html) object corresponding to this collection from the [npm `mongodb` driver module](https://www.npmjs.com/package/mongodb) which is wrapped by `Mongo.Collection`.\n   * @locus Server\n   * @memberof Mongo.Collection\n   * @instance\n   */\n  rawCollection() {\n    var self = this;\n    if (!self._collection.rawCollection) {\n      throw new Error('Can only call rawCollection on server collections');\n    }\n    return self._collection.rawCollection();\n  },\n\n  /**\n   * @summary Returns the [`Db`](http://mongodb.github.io/node-mongodb-native/3.0/api/Db.html) object corresponding to this collection's database connection from the [npm `mongodb` driver module](https://www.npmjs.com/package/mongodb) which is wrapped by `Mongo.Collection`.\n   * @locus Server\n   * @memberof Mongo.Collection\n   * @instance\n   */\n  rawDatabase() {\n    var self = this;\n    if (!(self._driver.mongo && self._driver.mongo.db)) {\n      throw new Error('Can only call rawDatabase on server collections');\n    }\n    return self._driver.mongo.db;\n  },\n});\n\nObject.assign(Mongo, {\n  /**\n   * @summary Retrieve a Meteor collection instance by name. Only collections defined with [`new Mongo.Collection(...)`](#collections) are available with this method. For plain MongoDB collections, you'll want to look at [`rawDatabase()`](#Mongo-Collection-rawDatabase).\n   * @locus Anywhere\n   * @memberof Mongo\n   * @static\n   * @param {string} name Name of your collection as it was defined with `new Mongo.Collection()`.\n   * @returns {Mongo.Collection | undefined}\n   */\n  getCollection(name) {\n    return this._collections.get(name);\n  },\n\n  /**\n   * @summary A record of all defined Mongo.Collection instances, indexed by collection name.\n   * @type {Map<string, Mongo.Collection>}\n   * @memberof Mongo\n   * @protected\n   */\n  _collections: new Map(),\n})\n\n\n\n/**\n * @summary Create a Mongo-style `ObjectID`.  If you don't specify a `hexString`, the `ObjectID` will be generated randomly (not using MongoDB's ID construction rules).\n * @locus Anywhere\n * @class\n * @param {String} [hexString] Optional.  The 24-character hexadecimal contents of the ObjectID to create\n */\nMongo.ObjectID = MongoID.ObjectID;\n\n/**\n * @summary To create a cursor, use find. To access the documents in a cursor, use forEach, map, or fetch.\n * @class\n * @instanceName cursor\n */\nMongo.Cursor = LocalCollection.Cursor;\n\n/**\n * @deprecated in 0.9.1\n */\nMongo.Collection.Cursor = Mongo.Cursor;\n\n/**\n * @deprecated in 0.9.1\n */\nMongo.Collection.ObjectID = Mongo.ObjectID;\n\n/**\n * @deprecated in 0.9.1\n */\nMeteor.Collection = Mongo.Collection;\n\n// Allow deny stuff is now in the allow-deny package\nObject.assign(Mongo.Collection.prototype, AllowDeny.CollectionPrototype);\n\n", "export const ID_GENERATORS = {\n  MONGO(name) {\n    return function() {\n      const src = name ? DDP.randomStream('/collection/' + name) : Random.insecure;\n      return new Mongo.ObjectID(src.hexString(24));\n    }\n  },\n  STRING(name) {\n    return function() {\n      const src = name ? DDP.randomStream('/collection/' + name) : Random.insecure;\n      return src.id();\n    }\n  }\n};\n\nexport function setupConnection(name, options) {\n  if (!name || options.connection === null) return null;\n  if (options.connection) return options.connection;\n  return Meteor.isClient ? Meteor.connection : Meteor.server;\n}\n\nexport function setupDriver(name, connection, options) {\n  if (options._driver) return options._driver;\n\n  if (name &&\n    connection === Meteor.server &&\n    typeof MongoInternals !== 'undefined' &&\n    MongoInternals.defaultRemoteCollectionDriver) {\n    return MongoInternals.defaultRemoteCollectionDriver();\n  }\n\n  const { LocalCollectionDriver } = require('../local_collection_driver.js');\n  return LocalCollectionDriver;\n}\n\nexport function setupAutopublish(collection, name, options) {\n  if (Package.autopublish &&\n    !options._preventAutopublish &&\n    collection._connection &&\n    collection._connection.publish) {\n    collection._connection.publish(null, () => collection.find(), {\n      is_auto: true\n    });\n  }\n}\n\nexport function setupMutationMethods(collection, name, options) {\n  if (options.defineMutationMethods === false) return;\n\n  try {\n    collection._defineMutationMethods({\n      useExisting: options._suppressSameNameError === true\n    });\n  } catch (error) {\n    if (error.message === `A method named '/${name}/insertAsync' is already defined`) {\n      throw new Error(`There is already a collection named \"${name}\"`);\n    }\n    throw error;\n  }\n}\n\nexport function validateCollectionName(name) {\n  if (!name && name !== null) {\n    Meteor._debug(\n      'Warning: creating anonymous collection. It will not be ' +\n      'saved or synchronized over the network. (Pass null for ' +\n      'the collection name to turn off this warning.)'\n    );\n    name = null;\n  }\n\n  if (name !== null && typeof name !== 'string') {\n    throw new Error(\n      'First argument to new Mongo.Collection must be a string or null'\n    );\n  }\n\n  return name;\n}\n\nexport function normalizeOptions(options) {\n  if (options && options.methods) {\n    // Backwards compatibility hack with original signature\n    options = { connection: options };\n  }\n  // Backwards compatibility: \"connection\" used to be called \"manager\".\n  if (options && options.manager && !options.connection) {\n    options.connection = options.manager;\n  }\n\n  return {\n    connection: undefined,\n    idGeneration: 'STRING',\n    transform: null,\n    _driver: undefined,\n    _preventAutopublish: false,\n    ...options,\n  };\n}\n", "export const AsyncMethods = {\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOneAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Object}\n   */\n  findOneAsync(...args) {\n    return this._collection.findOneAsync(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n  _insertAsync(doc, options = {}) {\n    // Make sure we were passed a document to insert\n    if (!doc) {\n      throw new Error('insert requires an argument');\n    }\n\n    // Make a shallow clone of the document, preserving its prototype.\n    doc = Object.create(\n      Object.getPrototypeOf(doc),\n      Object.getOwnPropertyDescriptors(doc)\n    );\n\n    if ('_id' in doc) {\n      if (\n        !doc._id ||\n        !(typeof doc._id === 'string' || doc._id instanceof Mongo.ObjectID)\n      ) {\n        throw new Error(\n          'Meteor requires document _id fields to be non-empty strings or ObjectIDs'\n        );\n      }\n    } else {\n      let generateId = true;\n\n      // Don't generate the id if we're the client and the 'outermost' call\n      // This optimization saves us passing both the randomSeed and the id\n      // Passing both is redundant.\n      if (this._isRemoteCollection()) {\n        const enclosing = DDP._CurrentMethodInvocation.get();\n        if (!enclosing) {\n          generateId = false;\n        }\n      }\n\n      if (generateId) {\n        doc._id = this._makeNewID();\n      }\n    }\n\n    // On inserts, always return the id that we generated; on all other\n    // operations, just return the result from the collection.\n    var chooseReturnValueFromCollectionResult = function(result) {\n      if (Meteor._isPromise(result)) return result;\n\n      if (doc._id) {\n        return doc._id;\n      }\n\n      // XXX what is this for??\n      // It's some iteraction between the callback to _callMutatorMethod and\n      // the return value conversion\n      doc._id = result;\n\n      return result;\n    };\n\n    if (this._isRemoteCollection()) {\n      const promise = this._callMutatorMethodAsync('insertAsync', [doc], options);\n      promise.then(chooseReturnValueFromCollectionResult);\n      promise.stubPromise = promise.stubPromise.then(chooseReturnValueFromCollectionResult);\n      promise.serverPromise = promise.serverPromise.then(chooseReturnValueFromCollectionResult);\n      return promise;\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    return this._collection.insertAsync(doc)\n      .then(chooseReturnValueFromCollectionResult);\n  },\n\n  /**\n   * @summary Insert a document in the collection.  Returns a promise that will return the document's unique _id when solved.\n   * @locus Anywhere\n   * @method  insert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   */\n  insertAsync(doc, options) {\n    return this._insertAsync(doc, options);\n  },\n\n\n  /**\n   * @summary Modify one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method update\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   */\n  updateAsync(selector, modifier, ...optionsAndCallback) {\n\n    // We've already popped off the callback, so we are left with an array\n    // of one or zero items\n    const options = { ...(optionsAndCallback[0] || null) };\n    let insertedId;\n    if (options && options.upsert) {\n      // set `insertedId` if absent.  `insertedId` is a Meteor extension.\n      if (options.insertedId) {\n        if (\n          !(\n            typeof options.insertedId === 'string' ||\n            options.insertedId instanceof Mongo.ObjectID\n          )\n        )\n          throw new Error('insertedId must be string or ObjectID');\n        insertedId = options.insertedId;\n      } else if (!selector || !selector._id) {\n        insertedId = this._makeNewID();\n        options.generatedId = true;\n        options.insertedId = insertedId;\n      }\n    }\n\n    selector = Mongo.Collection._rewriteSelector(selector, {\n      fallbackId: insertedId,\n    });\n\n    if (this._isRemoteCollection()) {\n      const args = [selector, modifier, options];\n\n      return this._callMutatorMethodAsync('updateAsync', args, options);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    // If the user provided a callback and the collection implements this\n    // operation asynchronously, then queryRet will be undefined, and the\n    // result will be returned through the callback instead.\n\n    return this._collection.updateAsync(\n      selector,\n      modifier,\n      options\n    );\n  },\n\n  /**\n   * @summary Asynchronously removes documents from the collection.\n   * @locus Anywhere\n   * @method remove\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   */\n  removeAsync(selector, options = {}) {\n    selector = Mongo.Collection._rewriteSelector(selector);\n\n    if (this._isRemoteCollection()) {\n      return this._callMutatorMethodAsync('removeAsync', [selector], options);\n    }\n\n    // it's my collection.  descend into the collection1 object\n    // and propagate any exception.\n    return this._collection.removeAsync(selector);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   */\n  async upsertAsync(selector, modifier, options) {\n    return this.updateAsync(\n      selector,\n      modifier,\n      {\n        ...options,\n        _returnObject: true,\n        upsert: true,\n      });\n  },\n\n  /**\n   * @summary Gets the number of documents matching the filter. For a fast count of the total documents in a collection see `estimatedDocumentCount`.\n   * @locus Anywhere\n   * @method countDocuments\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to count\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://mongodb.github.io/node-mongodb-native/4.11/interfaces/CountDocumentsOptions.html). Please note that not all of them are available on the client.\n   * @returns {Promise<number>}\n   */\n  countDocuments(...args) {\n    return this._collection.countDocuments(...args);\n  },\n\n  /**\n   * @summary Gets an estimate of the count of documents in a collection using collection metadata. For an exact count of the documents in a collection see `countDocuments`.\n   * @locus Anywhere\n   * @method estimatedDocumentCount\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://mongodb.github.io/node-mongodb-native/4.11/interfaces/EstimatedDocumentCountOptions.html). Please note that not all of them are available on the client.\n   * @returns {Promise<number>}\n   */\n  estimatedDocumentCount(...args) {\n    return this._collection.estimatedDocumentCount(...args);\n  },\n}", "export const IndexMethods = {\n  // We'll actually design an index API later. For now, we just pass through to\n  // Mongo's, but make it synchronous.\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method ensureIndexAsync\n   * @deprecated in 3.0\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  async ensureIndexAsync(index, options) {\n    var self = this;\n    if (!self._collection.ensureIndexAsync || !self._collection.createIndexAsync)\n      throw new Error('Can only call createIndexAsync on server collections');\n    if (self._collection.createIndexAsync) {\n      await self._collection.createIndexAsync(index, options);\n    } else {\n      import { Log } from 'meteor/logging';\n\n      Log.debug(`ensureIndexAsync has been deprecated, please use the new 'createIndexAsync' instead${ options?.name ? `, index name: ${ options.name }` : `, index: ${ JSON.stringify(index) }` }`)\n      await self._collection.ensureIndexAsync(index, options);\n    }\n  },\n\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method createIndexAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  async createIndexAsync(index, options) {\n    var self = this;\n    if (!self._collection.createIndexAsync)\n      throw new Error('Can only call createIndexAsync on server collections');\n\n    try {\n      await self._collection.createIndexAsync(index, options);\n    } catch (e) {\n      if (\n        e.message.includes(\n          'An equivalent index already exists with the same name but different options.'\n        ) &&\n        Meteor.settings?.packages?.mongo?.reCreateIndexOnOptionMismatch\n      ) {\n        import { Log } from 'meteor/logging';\n\n        Log.info(`Re-creating index ${ index } for ${ self._name } due to options mismatch.`);\n        await self._collection.dropIndexAsync(index);\n        await self._collection.createIndexAsync(index, options);\n      } else {\n        console.error(e);\n        throw new Meteor.Error(`An error occurred when creating an index for collection \"${ self._name }: ${ e.message }`);\n      }\n    }\n  },\n\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method createIndex\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  createIndex(index, options){\n    return this.createIndexAsync(index, options);\n  },\n\n  async dropIndexAsync(index) {\n    var self = this;\n    if (!self._collection.dropIndexAsync)\n      throw new Error('Can only call dropIndexAsync on server collections');\n    await self._collection.dropIndexAsync(index);\n  },\n}", "export const ReplicationMethods = {\n  async _maybeSetUpReplication(name) {\n    const self = this;\n    if (\n      !(\n        self._connection &&\n        self._connection.registerStoreClient &&\n        self._connection.registerStoreServer\n      )\n    ) {\n      return;\n    }\n\n\n    const wrappedStoreCommon = {\n      // Called around method stub invocations to capture the original versions\n      // of modified documents.\n      saveOriginals() {\n        self._collection.saveOriginals();\n      },\n      retrieveOriginals() {\n        return self._collection.retrieveOriginals();\n      },\n      // To be able to get back to the collection from the store.\n      _getCollection() {\n        return self;\n      },\n    };\n    const wrappedStoreClient = {\n      // Called at the beginning of a batch of updates. batchSize is the number\n      // of update calls to expect.\n      //\n      // XXX This interface is pretty janky. reset probably ought to go back to\n      // being its own function, and callers shouldn't have to calculate\n      // batchSize. The optimization of not calling pause/remove should be\n      // delayed until later: the first call to update() should buffer its\n      // message, and then we can either directly apply it at endUpdate time if\n      // it was the only update, or do pauseObservers/apply/apply at the next\n      // update() if there's another one.\n      async beginUpdate(batchSize, reset) {\n        // pause observers so users don't see flicker when updating several\n        // objects at once (including the post-reconnect reset-and-reapply\n        // stage), and so that a re-sorting of a query can take advantage of the\n        // full _diffQuery moved calculation instead of applying change one at a\n        // time.\n        if (batchSize > 1 || reset) self._collection.pauseObservers();\n\n        if (reset) await self._collection.remove({});\n      },\n\n      // Apply an update.\n      // XXX better specify this interface (not in terms of a wire message)?\n      update(msg) {\n        var mongoId = MongoID.idParse(msg.id);\n        var doc = self._collection._docs.get(mongoId);\n\n        //When the server's mergebox is disabled for a collection, the client must gracefully handle it when:\n        // *We receive an added message for a document that is already there. Instead, it will be changed\n        // *We reeive a change message for a document that is not there. Instead, it will be added\n        // *We receive a removed messsage for a document that is not there. Instead, noting wil happen.\n\n        //Code is derived from client-side code originally in peerlibrary:control-mergebox\n        //https://github.com/peerlibrary/meteor-control-mergebox/blob/master/client.coffee\n\n        //For more information, refer to discussion \"Initial support for publication strategies in livedata server\":\n        //https://github.com/meteor/meteor/pull/11151\n        if (Meteor.isClient) {\n          if (msg.msg === 'added' && doc) {\n            msg.msg = 'changed';\n          } else if (msg.msg === 'removed' && !doc) {\n            return;\n          } else if (msg.msg === 'changed' && !doc) {\n            msg.msg = 'added';\n            const _ref = msg.fields;\n            for (let field in _ref) {\n              const value = _ref[field];\n              if (value === void 0) {\n                delete msg.fields[field];\n              }\n            }\n          }\n        }\n        // Is this a \"replace the whole doc\" message coming from the quiescence\n        // of method writes to an object? (Note that 'undefined' is a valid\n        // value meaning \"remove it\".)\n        if (msg.msg === 'replace') {\n          var replace = msg.replace;\n          if (!replace) {\n            if (doc) self._collection.remove(mongoId);\n          } else if (!doc) {\n            self._collection.insert(replace);\n          } else {\n            // XXX check that replace has no $ ops\n            self._collection.update(mongoId, replace);\n          }\n          return;\n        } else if (msg.msg === 'added') {\n          if (doc) {\n            throw new Error(\n              'Expected not to find a document already present for an add'\n            );\n          }\n          self._collection.insert({ _id: mongoId, ...msg.fields });\n        } else if (msg.msg === 'removed') {\n          if (!doc)\n            throw new Error(\n              'Expected to find a document already present for removed'\n            );\n          self._collection.remove(mongoId);\n        } else if (msg.msg === 'changed') {\n          if (!doc) throw new Error('Expected to find a document to change');\n          const keys = Object.keys(msg.fields);\n          if (keys.length > 0) {\n            var modifier = {};\n            keys.forEach(key => {\n              const value = msg.fields[key];\n              if (EJSON.equals(doc[key], value)) {\n                return;\n              }\n              if (typeof value === 'undefined') {\n                if (!modifier.$unset) {\n                  modifier.$unset = {};\n                }\n                modifier.$unset[key] = 1;\n              } else {\n                if (!modifier.$set) {\n                  modifier.$set = {};\n                }\n                modifier.$set[key] = value;\n              }\n            });\n            if (Object.keys(modifier).length > 0) {\n              self._collection.update(mongoId, modifier);\n            }\n          }\n        } else {\n          throw new Error(\"I don't know how to deal with this message\");\n        }\n      },\n\n      // Called at the end of a batch of updates.livedata_connection.js:1287\n      endUpdate() {\n        self._collection.resumeObserversClient();\n      },\n\n      // Used to preserve current versions of documents across a store reset.\n      getDoc(id) {\n        return self.findOne(id);\n      },\n\n      ...wrappedStoreCommon,\n    };\n    const wrappedStoreServer = {\n      async beginUpdate(batchSize, reset) {\n        if (batchSize > 1 || reset) self._collection.pauseObservers();\n\n        if (reset) await self._collection.removeAsync({});\n      },\n\n      async update(msg) {\n        var mongoId = MongoID.idParse(msg.id);\n        var doc = self._collection._docs.get(mongoId);\n\n        // Is this a \"replace the whole doc\" message coming from the quiescence\n        // of method writes to an object? (Note that 'undefined' is a valid\n        // value meaning \"remove it\".)\n        if (msg.msg === 'replace') {\n          var replace = msg.replace;\n          if (!replace) {\n            if (doc) await self._collection.removeAsync(mongoId);\n          } else if (!doc) {\n            await self._collection.insertAsync(replace);\n          } else {\n            // XXX check that replace has no $ ops\n            await self._collection.updateAsync(mongoId, replace);\n          }\n          return;\n        } else if (msg.msg === 'added') {\n          if (doc) {\n            throw new Error(\n              'Expected not to find a document already present for an add'\n            );\n          }\n          await self._collection.insertAsync({ _id: mongoId, ...msg.fields });\n        } else if (msg.msg === 'removed') {\n          if (!doc)\n            throw new Error(\n              'Expected to find a document already present for removed'\n            );\n          await self._collection.removeAsync(mongoId);\n        } else if (msg.msg === 'changed') {\n          if (!doc) throw new Error('Expected to find a document to change');\n          const keys = Object.keys(msg.fields);\n          if (keys.length > 0) {\n            var modifier = {};\n            keys.forEach(key => {\n              const value = msg.fields[key];\n              if (EJSON.equals(doc[key], value)) {\n                return;\n              }\n              if (typeof value === 'undefined') {\n                if (!modifier.$unset) {\n                  modifier.$unset = {};\n                }\n                modifier.$unset[key] = 1;\n              } else {\n                if (!modifier.$set) {\n                  modifier.$set = {};\n                }\n                modifier.$set[key] = value;\n              }\n            });\n            if (Object.keys(modifier).length > 0) {\n              await self._collection.updateAsync(mongoId, modifier);\n            }\n          }\n        } else {\n          throw new Error(\"I don't know how to deal with this message\");\n        }\n      },\n\n      // Called at the end of a batch of updates.\n      async endUpdate() {\n        await self._collection.resumeObserversServer();\n      },\n\n      // Used to preserve current versions of documents across a store reset.\n      async getDoc(id) {\n        return self.findOneAsync(id);\n      },\n      ...wrappedStoreCommon,\n    };\n\n\n    // OK, we're going to be a slave, replicating some remote\n    // database, except possibly with some temporary divergence while\n    // we have unacknowledged RPC's.\n    let registerStoreResult;\n    if (Meteor.isClient) {\n      registerStoreResult = self._connection.registerStoreClient(\n        name,\n        wrappedStoreClient\n      );\n    } else {\n      registerStoreResult = self._connection.registerStoreServer(\n        name,\n        wrappedStoreServer\n      );\n    }\n\n    const message = `There is already a collection named \"${name}\"`;\n    const logWarn = () => {\n      console.warn ? console.warn(message) : console.log(message);\n    };\n\n    if (!registerStoreResult) {\n      return logWarn();\n    }\n\n    return registerStoreResult?.then?.(ok => {\n      if (!ok) {\n        logWarn();\n      }\n    });\n  },\n}", "export const SyncMethods = {\n  /**\n   * @summary Find the documents in a collection that match the selector.\n   * @locus Anywhere\n   * @method find\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {Number} options.limit Maximum number of results to return\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default `true`; pass `false` to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the  [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {Boolean} options.disableOplog (Server only) Pass true to disable oplog-tailing on this query. This affects the way server processes calls to `observe` on this query. Disabling the oplog can be useful when working with data that updates in large batches.\n   * @param {Number} options.pollingIntervalMs (Server only) When oplog is disabled (through the use of `disableOplog` or when otherwise not available), the frequency (in milliseconds) of how often to poll this query when observing on the server. Defaults to 10000ms (10 seconds).\n   * @param {Number} options.pollingThrottleMs (Server only) When oplog is disabled (through the use of `disableOplog` or when otherwise not available), the minimum time (in milliseconds) to allow between re-polling when observing on the server. Increasing this will save CPU and mongo load at the expense of slower updates to users. Decreasing this is not recommended. Defaults to 50ms.\n   * @param {Number} options.maxTimeMs (Server only) If set, instructs MongoDB to set a time limit for this cursor's operations. If the operation reaches the specified time limit (in milliseconds) without the having been completed, an exception will be thrown. Useful to prevent an (accidental or malicious) unoptimized query from causing a full collection scan that would disrupt other database users, at the expense of needing to handle the resulting error.\n   * @param {String|Object} options.hint (Server only) Overrides MongoDB's default index selection and query optimization process. Specify an index to force its use, either by its name or index specification. You can also specify `{ $natural : 1 }` to force a forwards collection scan, or `{ $natural : -1 }` for a reverse collection scan. Setting this is only recommended for advanced users.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for this particular cursor. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Mongo.Cursor}\n   */\n  find(...args) {\n    // Collection.find() (return all docs) behaves differently\n    // from Collection.find(undefined) (return 0 docs).  so be\n    // careful about the length of arguments.\n    return this._collection.find(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOne\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Object}\n   */\n  findOne(...args) {\n    return this._collection.findOne(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n\n  // 'insert' immediately returns the inserted document's new _id.\n  // The others return values immediately if you are in a stub, an in-memory\n  // unmanaged collection, or a mongo-backed collection and you don't pass a\n  // callback. 'update' and 'remove' return the number of affected\n  // documents. 'upsert' returns an object with keys 'numberAffected' and, if an\n  // insert happened, 'insertedId'.\n  //\n  // Otherwise, the semantics are exactly like other methods: they take\n  // a callback as an optional last argument; if no callback is\n  // provided, they block until the operation is complete, and throw an\n  // exception if it fails; if a callback is provided, then they don't\n  // necessarily block, and they call the callback when they finish with error and\n  // result arguments.  (The insert method provides the document ID as its result;\n  // update and remove provide the number of affected docs as the result; upsert\n  // provides an object with numberAffected and maybe insertedId.)\n  //\n  // On the client, blocking is impossible, so if a callback\n  // isn't provided, they just return immediately and any error\n  // information is lost.\n  //\n  // There's one more tweak. On the client, if you don't provide a\n  // callback, then if there is an error, a message will be logged with\n  // Meteor._debug.\n  //\n  // The intent (though this is actually determined by the underlying\n  // drivers) is that the operations should be done synchronously, not\n  // generating their result until the database has acknowledged\n  // them. In the future maybe we should provide a flag to turn this\n  // off.\n\n  _insert(doc, callback) {\n    // Make sure we were passed a document to insert\n    if (!doc) {\n      throw new Error('insert requires an argument');\n    }\n\n\n    // Make a shallow clone of the document, preserving its prototype.\n    doc = Object.create(\n      Object.getPrototypeOf(doc),\n      Object.getOwnPropertyDescriptors(doc)\n    );\n\n    if ('_id' in doc) {\n      if (\n        !doc._id ||\n        !(typeof doc._id === 'string' || doc._id instanceof Mongo.ObjectID)\n      ) {\n        throw new Error(\n          'Meteor requires document _id fields to be non-empty strings or ObjectIDs'\n        );\n      }\n    } else {\n      let generateId = true;\n\n      // Don't generate the id if we're the client and the 'outermost' call\n      // This optimization saves us passing both the randomSeed and the id\n      // Passing both is redundant.\n      if (this._isRemoteCollection()) {\n        const enclosing = DDP._CurrentMethodInvocation.get();\n        if (!enclosing) {\n          generateId = false;\n        }\n      }\n\n      if (generateId) {\n        doc._id = this._makeNewID();\n      }\n    }\n\n\n    // On inserts, always return the id that we generated; on all other\n    // operations, just return the result from the collection.\n    var chooseReturnValueFromCollectionResult = function(result) {\n      if (Meteor._isPromise(result)) return result;\n\n      if (doc._id) {\n        return doc._id;\n      }\n\n      // XXX what is this for??\n      // It's some iteraction between the callback to _callMutatorMethod and\n      // the return value conversion\n      doc._id = result;\n\n      return result;\n    };\n\n    const wrappedCallback = wrapCallback(\n      callback,\n      chooseReturnValueFromCollectionResult\n    );\n\n    if (this._isRemoteCollection()) {\n      const result = this._callMutatorMethod('insert', [doc], wrappedCallback);\n      return chooseReturnValueFromCollectionResult(result);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    try {\n      // If the user provided a callback and the collection implements this\n      // operation asynchronously, then queryRet will be undefined, and the\n      // result will be returned through the callback instead.\n      let result;\n      if (!!wrappedCallback) {\n        this._collection.insert(doc, wrappedCallback);\n      } else {\n        // If we don't have the callback, we assume the user is using the promise.\n        // We can't just pass this._collection.insert to the promisify because it would lose the context.\n        result = this._collection.insert(doc);\n      }\n\n      return chooseReturnValueFromCollectionResult(result);\n    } catch (e) {\n      if (callback) {\n        callback(e);\n        return null;\n      }\n      throw e;\n    }\n  },\n\n  /**\n   * @summary Insert a document in the collection.  Returns its unique _id.\n   * @locus Anywhere\n   * @method  insert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the _id as the second.\n   */\n  insert(doc, callback) {\n    return this._insert(doc, callback);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method update\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  update(selector, modifier, ...optionsAndCallback) {\n    const callback = popCallbackFromArgs(optionsAndCallback);\n\n    // We've already popped off the callback, so we are left with an array\n    // of one or zero items\n    const options = { ...(optionsAndCallback[0] || null) };\n    let insertedId;\n    if (options && options.upsert) {\n      // set `insertedId` if absent.  `insertedId` is a Meteor extension.\n      if (options.insertedId) {\n        if (\n          !(\n            typeof options.insertedId === 'string' ||\n            options.insertedId instanceof Mongo.ObjectID\n          )\n        )\n          throw new Error('insertedId must be string or ObjectID');\n        insertedId = options.insertedId;\n      } else if (!selector || !selector._id) {\n        insertedId = this._makeNewID();\n        options.generatedId = true;\n        options.insertedId = insertedId;\n      }\n    }\n\n    selector = Mongo.Collection._rewriteSelector(selector, {\n      fallbackId: insertedId,\n    });\n\n    const wrappedCallback = wrapCallback(callback);\n\n    if (this._isRemoteCollection()) {\n      const args = [selector, modifier, options];\n      return this._callMutatorMethod('update', args, callback);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    // If the user provided a callback and the collection implements this\n    // operation asynchronously, then queryRet will be undefined, and the\n    // result will be returned through the callback instead.\n    //console.log({callback, options, selector, modifier, coll: this._collection});\n    try {\n      // If the user provided a callback and the collection implements this\n      // operation asynchronously, then queryRet will be undefined, and the\n      // result will be returned through the callback instead.\n      return this._collection.update(\n        selector,\n        modifier,\n        options,\n        wrappedCallback\n      );\n    } catch (e) {\n      if (callback) {\n        callback(e);\n        return null;\n      }\n      throw e;\n    }\n  },\n\n  /**\n   * @summary Remove documents from the collection\n   * @locus Anywhere\n   * @method remove\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  remove(selector, callback) {\n    selector = Mongo.Collection._rewriteSelector(selector);\n\n    if (this._isRemoteCollection()) {\n      return this._callMutatorMethod('remove', [selector], callback);\n    }\n\n\n    // it's my collection.  descend into the collection1 object\n    // and propagate any exception.\n    return this._collection.remove(selector);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  upsert(selector, modifier, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.update(\n      selector,\n      modifier,\n      {\n        ...options,\n        _returnObject: true,\n        upsert: true,\n      });\n  },\n}\n\n// Convert the callback to not return a result if there is an error\nfunction wrapCallback(callback, convertResult) {\n  return (\n    callback &&\n    function(error, result) {\n      if (error) {\n        callback(error);\n      } else if (typeof convertResult === 'function') {\n        callback(error, convertResult(result));\n      } else {\n        callback(error, result);\n      }\n    }\n  );\n}\n\nfunction popCallbackFromArgs(args) {\n  // Pull off any callback (or perhaps a 'callback' variable that was passed\n  // in undefined, like how 'upsert' does it).\n  if (\n    args.length &&\n    (args[args.length - 1] === undefined ||\n      args[args.length - 1] instanceof Function)\n  ) {\n    return args.pop();\n  }\n}\n", "/**\n * @summary Allows for user specified connection options\n * @example http://mongodb.github.io/node-mongodb-native/3.0/reference/connecting/connection-settings/\n * @locus Server\n * @param {Object} options User specified Mongo connection options\n */\nMongo.setConnectionOptions = function setConnectionOptions (options) {\n  check(options, Object);\n  Mongo._connectionOptions = options;\n};", "export const normalizeProjection = options => {\n  // transform fields key in projection\n  const { fields, projection, ...otherOptions } = options || {};\n  // TODO: enable this comment when deprecating the fields option\n  // Log.debug(`fields option has been deprecated, please use the new 'projection' instead`)\n\n  return {\n    ...otherOptions,\n    ...(projection || fields ? { projection: fields || projection } : {}),\n  };\n};\n", "import { ObserveHandleCallback, ObserveMultiplexer } from './observe_multiplex';\n\nlet nextObserveHandleId = 1;\n\nexport type ObserveHandleCallbackInternal = '_added' | '_addedBefore' | '_changed' | '_movedBefore' | '_removed';\n\n\nexport type Callback<T = any> = (...args: T[]) => Promise<void> | void;\n\n/**\n * The \"observe handle\" returned from observeChanges.\n * Contains a reference to an ObserveMultiplexer.\n * Used to stop observation and clean up resources.\n */\nexport class ObserveHandle<T = any> {\n  _id: number;\n  _multiplexer: ObserveMultiplexer;\n  nonMutatingCallbacks: boolean;\n  _stopped: boolean;\n\n  public initialAddsSentResolver: (value: void) => void = () => {};\n  public initialAddsSent: Promise<void>\n\n  _added?: Callback<T>;\n  _addedBefore?: Callback<T>;\n  _changed?: Callback<T>;\n  _movedBefore?: Callback<T>;\n  _removed?: Callback<T>;\n\n  constructor(multiplexer: ObserveMultiplexer, callbacks: Record<ObserveHandleCallback, Callback<T>>, nonMutatingCallbacks: boolean) {\n    this._multiplexer = multiplexer;\n\n    multiplexer.callbackNames().forEach((name: ObserveHandleCallback) => {\n      if (callbacks[name]) {\n        this[`_${name}` as ObserveHandleCallbackInternal] = callbacks[name];\n        return;\n      }\n\n      if (name === \"addedBefore\" && callbacks.added) {\n        this._addedBefore = async function (id, fields, before) {\n          await callbacks.added(id, fields);\n        };\n      }\n    });\n\n    this._stopped = false;\n    this._id = nextObserveHandleId++;\n    this.nonMutatingCallbacks = nonMutatingCallbacks;\n\n    this.initialAddsSent = new Promise(resolve => {\n      const ready = () => {\n        resolve();\n        this.initialAddsSent = Promise.resolve();\n      }\n\n      const timeout = setTimeout(ready, 30000)\n\n      this.initialAddsSentResolver = () => {\n        ready();\n        clearTimeout(timeout);\n      };\n    });\n  }\n\n  /**\n   * Using property syntax and arrow function syntax to avoid binding the wrong context on callbacks.\n   */\n  stop = async () => {\n    if (this._stopped) return;\n    this._stopped = true;\n    await this._multiplexer.removeHandle(this._id);\n  }\n}"]}