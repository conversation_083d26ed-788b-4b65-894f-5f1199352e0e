{"version": 3, "sources": ["meteor://💻app/packages/minimongo/minimongo_server.js", "meteor://💻app/packages/minimongo/common.js", "meteor://💻app/packages/minimongo/constants.js", "meteor://💻app/packages/minimongo/cursor.js", "meteor://💻app/packages/minimongo/local_collection.js", "meteor://💻app/packages/minimongo/matcher.js", "meteor://💻app/packages/minimongo/minimongo_common.js", "meteor://💻app/packages/minimongo/observe_handle.js", "meteor://💻app/packages/minimongo/sorter.js"], "names": ["module", "link", "hasOwn", "isNumericKey", "isOperatorObject", "pathsToTree", "projectionDetails", "v", "__reifyWaitForDeps__", "Minimongo", "_pathsElidingNumericKeys", "paths", "map", "path", "split", "filter", "part", "join", "Matcher", "prototype", "affectedByModifier", "modifier", "Object", "assign", "$set", "$unset", "meaningfulPaths", "_getPaths", "modifiedPaths", "concat", "keys", "some", "mod", "meaningfulPath", "sel", "i", "j", "length", "canBecomeTrueByModifier", "isSimple", "modifierPaths", "pathHasNumericKeys", "expectedScalarIsObject", "_selector", "modifierPath", "startsWith", "matchingDocument", "EJSON", "clone", "LocalCollection", "_modify", "error", "name", "setPropertyError", "documentMatches", "result", "combineIntoProjection", "projection", "selectorPaths", "includes", "combineImportantPathsIntoProjection", "_matchingDocument", "undefined", "fallback", "valueSelector", "$eq", "$in", "matcher", "placeholder", "find", "onlyContains<PERSON>eys", "lowerBound", "Infinity", "upperBound", "for<PERSON>ach", "op", "call", "middle", "x", "Sorter", "_selectorForAffectedByModifier", "details", "tree", "node", "fullPath", "mergedProjection", "treeToPaths", "including", "mergedExclProjection", "getPaths", "selector", "_paths", "obj", "every", "k", "prefix", "arguments", "key", "value", "__reify_async_result__", "_reifyError", "self", "async", "export", "ELEMENT_OPERATORS", "compileDocumentSelector", "equalityElementM<PERSON>er", "expandArraysInBranches", "isIndexable", "makeLookupFunction", "nothingMatcher", "populateDocumentWithQueryFields", "regexpElementMatcher", "default", "hasOwnProperty", "$lt", "makeInequality", "cmpValue", "$gt", "$lte", "$gte", "$mod", "compileElementSelector", "operand", "Array", "isArray", "Error", "divisor", "remainder", "elementMatchers", "option", "RegExp", "$size", "dontExpandLeafArrays", "$type", "dontIncludeLeafArrays", "operandAliasMap", "_f", "_type", "$bitsAllSet", "mask", "getOperandBitmask", "bitmask", "getValueBitmask", "byte", "$bitsAnySet", "$bitsAllClear", "$bitsAnyClear", "$regex", "regexp", "$options", "test", "source", "$elemMatch", "_isPlainObject", "isDocMatcher", "LOGICAL_OPERATORS", "reduce", "a", "b", "subMatcher", "inElemMatch", "compileValueSelector", "arrayElement", "arg", "dontIterate", "$and", "subSelector", "andDocumentMatchers", "compileArrayOfDocumentSelectors", "$or", "matchers", "doc", "fn", "$nor", "$where", "selector<PERSON><PERSON><PERSON>", "_recordPathUsed", "_hasWhere", "Function", "$comment", "VALUE_OPERATORS", "convertElementMatcherToBranchedMatcher", "$not", "invertBranchedMatcher", "$ne", "$nin", "$exists", "exists", "everythingMatcher", "$maxDistance", "$near", "$all", "branchedMatchers", "criterion", "andBranchedMatchers", "isRoot", "_hasGeoQuery", "maxDistance", "point", "distance", "$geometry", "type", "GeoJSON", "pointDistance", "coordinates", "pointToArray", "geometryWithinRadius", "distanceCoordinatePairs", "branchedValues", "branch", "curDistance", "_isUpdate", "arrayIndices", "andSomeMatchers", "subMatchers", "docOrBranches", "match", "subResult", "selectors", "docSelector", "options", "docMatchers", "substr", "_isSimple", "lookUpByIndex", "valueMatcher", "Boolean", "operatorBranchedMatcher", "elementMatcher", "branches", "expanded", "element", "matched", "pointA", "pointB", "Math", "hypot", "elementSelector", "_equal", "docOrBranchedValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "branchesOut", "thisIsArray", "push", "Number", "isInteger", "Uint8Array", "Int32Array", "buffer", "isBinary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max", "view", "isSafeInteger", "Uint32Array", "BYTES_PER_ELEMENT", "insertIntoDocument", "document", "existingKey", "indexOf", "branchedMatcher", "branchV<PERSON>ues", "s", "inconsistentOK", "theseAreOperators", "<PERSON><PERSON><PERSON><PERSON>", "thisIsOperator", "JSON", "stringify", "cmpValueComparator", "operandType", "_cmp", "parts", "firstPart", "lookupRest", "slice", "buildResult", "firstLevel", "appendToResult", "more", "forSort", "arrayIndex", "MinimongoTest", "MinimongoError", "message", "field", "operatorMatchers", "operator", "simpleRange", "simpleEquality", "simpleInclusion", "newLeafFn", "conflictFn", "root", "pathArray", "success", "last<PERSON>ey", "y", "populateDocumentWithKeyValue", "getPrototypeOf", "populateDocumentWithObject", "unprefixedKeys", "validateObject", "object", "query", "_selectorIsId", "fields", "fieldsKeys", "sort", "_id", "keyP<PERSON>", "rule", "projectionRulesTree", "currentPath", "another<PERSON><PERSON>", "toString", "lastIndex", "validateKeyInPath", "getAsyncMethodName", "ASYNC_COLLECTION_METHODS", "ASYNC_CURSOR_METHODS", "CLIENT_ONLY_METHODS", "method", "replace", "<PERSON><PERSON><PERSON>", "constructor", "collection", "sorter", "_selectorIsIdPerhapsAsObject", "_selectorId", "has<PERSON>eo<PERSON><PERSON><PERSON>", "skip", "limit", "_projectionFn", "_compileProjection", "_transform", "wrapTransform", "transform", "Tracker", "reactive", "count", "_depend", "added", "removed", "_getRawObjects", "ordered", "fetch", "Symbol", "iterator", "addedBefore", "changed", "movedBefore", "index", "objects", "next", "done", "asyncIterator", "syncResult", "Promise", "resolve", "callback", "thisArg", "getTransform", "observe", "_observeFromObserveChanges", "observeAsync", "observe<PERSON>hanges", "_observeChangesCallbacksAreOrdered", "_allow_unordered", "distances", "_IdMap", "cursor", "dirty", "projectionFn", "resultsSnapshot", "qid", "next_qid", "queries", "results", "paused", "wrapCallback", "args", "_observeQueue", "queueTask", "apply", "_suppress_initial", "_query$results", "_query$results$size", "handler", "size", "handle", "ObserveHandle", "stop", "isReady", "isReadyPromise", "active", "onInvalidate", "drainResult", "drain", "then", "observeChangesAsync", "changers", "dependency", "Dependency", "notify", "bind", "depend", "_getCollectionName", "applySkipLimit", "selectedDoc", "_docs", "get", "set", "clear", "Meteor", "_runFresh", "id", "matchResult", "getComparator", "_publishCursor", "subscription", "Package", "mongo", "Mongo", "Collection", "asyncName", "_len", "_key", "reject", "_objectSpread", "isClient", "_SynchronousQueue", "_AsynchronousQueue", "create", "_savedOriginals", "countDocuments", "countAsync", "estimatedDocumentCount", "findOne", "findOneAsync", "fetchAsync", "prepareInsert", "assertHasValidFieldNames", "_useOID", "MongoID", "ObjectID", "Random", "has", "_saveOriginal", "insert", "queriesToRecompute", "_insertInResultsSync", "_recomputeResults", "defer", "insertAsync", "_insertInResultsAsync", "pauseObservers", "clearResultQueries", "prepareRemove", "remove", "_eachPossiblyMatchingDocSync", "query<PERSON><PERSON>ove", "removeId", "removeDoc", "equals", "_removeFromResultsSync", "removeAsync", "_removeFromResultsAsync", "_resumeObservers", "_diffQuery<PERSON><PERSON>es", "resumeObserversServer", "resumeObserversClient", "retrieveOriginals", "originals", "saveOriginals", "prepareUpdate", "qidToOriginalResults", "docMap", "idsMatched", "_idsMatchedBySelector", "memoizedCloneIfNeeded", "docToMemoize", "finishUpdate", "_ref", "updateCount", "insertedId", "_returnObject", "numberAffected", "updateAsync", "recomputeQids", "_eachPossiblyMatchingDocAsync", "query<PERSON><PERSON>ult", "_modifyAndNotifyAsync", "multi", "upsert", "_createUpsertDocument", "update", "_modifyAndNotifySync", "upsertAsync", "specificIds", "forEachAsync", "_getMatchedDocAndModify", "matched_before", "old_doc", "afterMatch", "after", "before", "_updateInResultsSync", "_updateInResultsAsync", "oldResults", "_CachingChangeObserver", "orderedFromCallbacks", "callbacks", "docs", "OrderedDict", "idStringify", "applyChange", "putBefore", "moveBefore", "DiffSequence", "applyChanges", "IdMap", "idParse", "__wrappedTransform__", "wrapped", "transformed", "nonreactive", "_binarySearch", "cmp", "array", "first", "range", "<PERSON><PERSON><PERSON><PERSON>", "floor", "_checkSupportedProjection", "_idProjection", "ruleTree", "subdoc", "selectorDocument", "isModify", "_isModificationMod", "newDoc", "isInsert", "replacement", "_diffObjects", "left", "right", "diffObjects", "newResults", "observer", "diff<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_diffQuery<PERSON>rderedChanges", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_diffQueryUnorderedChanges", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_findInOrderedResults", "subIds", "_insertInSortedList", "splice", "isReplace", "isModifier", "setOnInsert", "modFunc", "MODIFIERS", "keypath", "keyparts", "target", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "forbidArray", "noCreate", "NO_CREATE_MODIFIERS", "pop", "observeCallbacks", "suppressed", "observeChangesCallbacks", "_observeCallbacksAreOrdered", "indices", "_no_indices", "check", "addedAt", "changedAt", "oldDoc", "movedTo", "from", "to", "removedAt", "changeObserver", "_fromObserve", "nonMutatingCallbacks", "setSuppressed", "h", "_h$isReadyPromise", "_isPromise", "changed<PERSON>ields", "make<PERSON><PERSON>edFields", "old_idx", "new_idx", "$currentDate", "Date", "$inc", "$min", "$max", "$mul", "$rename", "target2", "$setOnInsert", "$push", "$each", "to<PERSON>ush", "position", "$position", "$slice", "sortFunction", "$sort", "spliceArguments", "$pushAll", "$addToSet", "isEach", "values", "toAdd", "$pop", "toPop", "$pull", "to<PERSON><PERSON>", "out", "$pullAll", "$bit", "$v", "invalidCharMsg", "$", "assertIsValidFieldName", "usedArrayIndex", "last", "keypart", "parseInt", "Decimal", "_Package$mongoDecima", "DecimalStub", "isUpdate", "_doc<PERSON><PERSON>er", "_compileSelector", "hasWhere", "keyOrderSensitive", "_typeorder", "t", "ta", "tb", "oa", "ob", "toHexString", "isNaN", "getTime", "minus", "toNumber", "toArray", "LocalCollection_", "spec", "_sortSpecParts", "_sortFunction", "addSpecPart", "ascending", "char<PERSON>t", "lookup", "_keyComparator", "composeComparators", "_keyFieldComparator", "_getBaseComparator", "_compareKeys", "key1", "key2", "_generateKeysFromDoc", "cb", "pathFromIndices", "knownPaths", "valuesByIndexAndPath", "usedPaths", "<PERSON><PERSON><PERSON>", "doc1", "doc2", "_getMinKeyFromDoc", "<PERSON><PERSON><PERSON>", "invert", "compare", "comparator<PERSON><PERSON>y"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAAC,IAAIC,MAAM,EAACC,YAAY,EAACC,gBAAgB,EAACC,WAAW,EAACC,iBAAiB;IAACN,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACC,MAAMA,CAACK,CAAC,EAAC;QAACL,MAAM,GAACK,CAAC;MAAA,CAAC;MAACJ,YAAYA,CAACI,CAAC,EAAC;QAACJ,YAAY,GAACI,CAAC;MAAA,CAAC;MAACH,gBAAgBA,CAACG,CAAC,EAAC;QAACH,gBAAgB,GAACG,CAAC;MAAA,CAAC;MAACF,WAAWA,CAACE,CAAC,EAAC;QAACF,WAAW,GAACE,CAAC;MAAA,CAAC;MAACD,iBAAiBA,CAACC,CAAC,EAAC;QAACD,iBAAiB,GAACC,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAS3WC,SAAS,CAACC,wBAAwB,GAAGC,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACC,IAAI,IAC1DA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,IAAI,IAAI,CAACb,YAAY,CAACa,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAC9D,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAR,SAAS,CAACS,OAAO,CAACC,SAAS,CAACC,kBAAkB,GAAG,UAASC,QAAQ,EAAE;MAClE;MACAA,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC;QAACC,IAAI,EAAE,CAAC,CAAC;QAAEC,MAAM,EAAE,CAAC;MAAC,CAAC,EAAEJ,QAAQ,CAAC;MAE1D,MAAMK,eAAe,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MACxC,MAAMC,aAAa,GAAG,EAAE,CAACC,MAAM,CAC7BP,MAAM,CAACQ,IAAI,CAACT,QAAQ,CAACG,IAAI,CAAC,EAC1BF,MAAM,CAACQ,IAAI,CAACT,QAAQ,CAACI,MAAM,CAC7B,CAAC;MAED,OAAOG,aAAa,CAACG,IAAI,CAAClB,IAAI,IAAI;QAChC,MAAMmB,GAAG,GAAGnB,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;QAE3B,OAAOY,eAAe,CAACK,IAAI,CAACE,cAAc,IAAI;UAC5C,MAAMC,GAAG,GAAGD,cAAc,CAACnB,KAAK,CAAC,GAAG,CAAC;UAErC,IAAIqB,CAAC,GAAG,CAAC;YAAEC,CAAC,GAAG,CAAC;UAEhB,OAAOD,CAAC,GAAGD,GAAG,CAACG,MAAM,IAAID,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAE;YACvC,IAAIlC,YAAY,CAAC+B,GAAG,CAACC,CAAC,CAAC,CAAC,IAAIhC,YAAY,CAAC6B,GAAG,CAACI,CAAC,CAAC,CAAC,EAAE;cAChD;cACA;cACA,IAAIF,GAAG,CAACC,CAAC,CAAC,KAAKH,GAAG,CAACI,CAAC,CAAC,EAAE;gBACrBD,CAAC,EAAE;gBACHC,CAAC,EAAE;cACL,CAAC,MAAM;gBACL,OAAO,KAAK;cACd;YACF,CAAC,MAAM,IAAIjC,YAAY,CAAC+B,GAAG,CAACC,CAAC,CAAC,CAAC,EAAE;cAC/B;cACA,OAAO,KAAK;YACd,CAAC,MAAM,IAAIhC,YAAY,CAAC6B,GAAG,CAACI,CAAC,CAAC,CAAC,EAAE;cAC/BA,CAAC,EAAE;YACL,CAAC,MAAM,IAAIF,GAAG,CAACC,CAAC,CAAC,KAAKH,GAAG,CAACI,CAAC,CAAC,EAAE;cAC5BD,CAAC,EAAE;cACHC,CAAC,EAAE;YACL,CAAC,MAAM;cACL,OAAO,KAAK;YACd;UACF;;UAEA;UACA,OAAO,IAAI;QACb,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA3B,SAAS,CAACS,OAAO,CAACC,SAAS,CAACmB,uBAAuB,GAAG,UAASjB,QAAQ,EAAE;MACvE,IAAI,CAAC,IAAI,CAACD,kBAAkB,CAACC,QAAQ,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;MAEA,IAAI,CAAC,IAAI,CAACkB,QAAQ,CAAC,CAAC,EAAE;QACpB,OAAO,IAAI;MACb;MAEAlB,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC;QAACC,IAAI,EAAE,CAAC,CAAC;QAAEC,MAAM,EAAE,CAAC;MAAC,CAAC,EAAEJ,QAAQ,CAAC;MAE1D,MAAMmB,aAAa,GAAG,EAAE,CAACX,MAAM,CAC7BP,MAAM,CAACQ,IAAI,CAACT,QAAQ,CAACG,IAAI,CAAC,EAC1BF,MAAM,CAACQ,IAAI,CAACT,QAAQ,CAACI,MAAM,CAC7B,CAAC;MAED,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC,CAACI,IAAI,CAACU,kBAAkB,CAAC,IACzCD,aAAa,CAACT,IAAI,CAACU,kBAAkB,CAAC,EAAE;QAC1C,OAAO,IAAI;MACb;;MAEA;MACA;MACA;MACA;MACA;MACA,MAAMC,sBAAsB,GAAGpB,MAAM,CAACQ,IAAI,CAAC,IAAI,CAACa,SAAS,CAAC,CAACZ,IAAI,CAAClB,IAAI,IAAI;QACtE,IAAI,CAACT,gBAAgB,CAAC,IAAI,CAACuC,SAAS,CAAC9B,IAAI,CAAC,CAAC,EAAE;UAC3C,OAAO,KAAK;QACd;QAEA,OAAO2B,aAAa,CAACT,IAAI,CAACa,YAAY,IACpCA,YAAY,CAACC,UAAU,IAAAhB,MAAA,CAAIhB,IAAI,MAAG,CACpC,CAAC;MACH,CAAC,CAAC;MAEF,IAAI6B,sBAAsB,EAAE;QAC1B,OAAO,KAAK;MACd;;MAEA;MACA;MACA;MACA,MAAMI,gBAAgB,GAAGC,KAAK,CAACC,KAAK,CAAC,IAAI,CAACF,gBAAgB,CAAC,CAAC,CAAC;;MAE7D;MACA,IAAIA,gBAAgB,KAAK,IAAI,EAAE;QAC7B,OAAO,IAAI;MACb;MAEA,IAAI;QACFG,eAAe,CAACC,OAAO,CAACJ,gBAAgB,EAAEzB,QAAQ,CAAC;MACrD,CAAC,CAAC,OAAO8B,KAAK,EAAE;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,KAAK,CAACC,IAAI,KAAK,gBAAgB,IAAID,KAAK,CAACE,gBAAgB,EAAE;UAC7D,OAAO,KAAK;QACd;QAEA,MAAMF,KAAK;MACb;MAEA,OAAO,IAAI,CAACG,eAAe,CAACR,gBAAgB,CAAC,CAACS,MAAM;IACtD,CAAC;;IAED;IACA;IACA;IACA9C,SAAS,CAACS,OAAO,CAACC,SAAS,CAACqC,qBAAqB,GAAG,UAASC,UAAU,EAAE;MACvE,MAAMC,aAAa,GAAGjD,SAAS,CAACC,wBAAwB,CAAC,IAAI,CAACiB,SAAS,CAAC,CAAC,CAAC;;MAE1E;MACA;MACA;MACA;MACA,IAAI+B,aAAa,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAE;QAC9B,OAAO,CAAC,CAAC;MACX;MAEA,OAAOC,mCAAmC,CAACF,aAAa,EAAED,UAAU,CAAC;IACvE,CAAC;;IAED;IACA;IACA;IACA;IACAhD,SAAS,CAACS,OAAO,CAACC,SAAS,CAAC2B,gBAAgB,GAAG,YAAW;MACxD;MACA,IAAI,IAAI,CAACe,iBAAiB,KAAKC,SAAS,EAAE;QACxC,OAAO,IAAI,CAACD,iBAAiB;MAC/B;;MAEA;MACA;MACA,IAAIE,QAAQ,GAAG,KAAK;MAEpB,IAAI,CAACF,iBAAiB,GAAGxD,WAAW,CAClC,IAAI,CAACsB,SAAS,CAAC,CAAC,EAChBd,IAAI,IAAI;QACN,MAAMmD,aAAa,GAAG,IAAI,CAACrB,SAAS,CAAC9B,IAAI,CAAC;QAE1C,IAAIT,gBAAgB,CAAC4D,aAAa,CAAC,EAAE;UACnC;UACA;UACA;UACA,IAAIA,aAAa,CAACC,GAAG,EAAE;YACrB,OAAOD,aAAa,CAACC,GAAG;UAC1B;UAEA,IAAID,aAAa,CAACE,GAAG,EAAE;YACrB,MAAMC,OAAO,GAAG,IAAI1D,SAAS,CAACS,OAAO,CAAC;cAACkD,WAAW,EAAEJ;YAAa,CAAC,CAAC;;YAEnE;YACA;YACA;YACA,OAAOA,aAAa,CAACE,GAAG,CAACG,IAAI,CAACD,WAAW,IACvCD,OAAO,CAACb,eAAe,CAAC;cAACc;YAAW,CAAC,CAAC,CAACb,MACzC,CAAC;UACH;UAEA,IAAIe,gBAAgB,CAACN,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;YACnE,IAAIO,UAAU,GAAG,CAACC,QAAQ;YAC1B,IAAIC,UAAU,GAAGD,QAAQ;YAEzB,CAAC,MAAM,EAAE,KAAK,CAAC,CAACE,OAAO,CAACC,EAAE,IAAI;cAC5B,IAAIzE,MAAM,CAAC0E,IAAI,CAACZ,aAAa,EAAEW,EAAE,CAAC,IAC9BX,aAAa,CAACW,EAAE,CAAC,GAAGF,UAAU,EAAE;gBAClCA,UAAU,GAAGT,aAAa,CAACW,EAAE,CAAC;cAChC;YACF,CAAC,CAAC;YAEF,CAAC,MAAM,EAAE,KAAK,CAAC,CAACD,OAAO,CAACC,EAAE,IAAI;cAC5B,IAAIzE,MAAM,CAAC0E,IAAI,CAACZ,aAAa,EAAEW,EAAE,CAAC,IAC9BX,aAAa,CAACW,EAAE,CAAC,GAAGJ,UAAU,EAAE;gBAClCA,UAAU,GAAGP,aAAa,CAACW,EAAE,CAAC;cAChC;YACF,CAAC,CAAC;YAEF,MAAME,MAAM,GAAG,CAACN,UAAU,GAAGE,UAAU,IAAI,CAAC;YAC5C,MAAMN,OAAO,GAAG,IAAI1D,SAAS,CAACS,OAAO,CAAC;cAACkD,WAAW,EAAEJ;YAAa,CAAC,CAAC;YAEnE,IAAI,CAACG,OAAO,CAACb,eAAe,CAAC;cAACc,WAAW,EAAES;YAAM,CAAC,CAAC,CAACtB,MAAM,KACrDsB,MAAM,KAAKN,UAAU,IAAIM,MAAM,KAAKJ,UAAU,CAAC,EAAE;cACpDV,QAAQ,GAAG,IAAI;YACjB;YAEA,OAAOc,MAAM;UACf;UAEA,IAAIP,gBAAgB,CAACN,aAAa,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;YACpD;YACA;YACA;YACA,OAAO,CAAC,CAAC;UACX;UAEAD,QAAQ,GAAG,IAAI;QACjB;QAEA,OAAO,IAAI,CAACpB,SAAS,CAAC9B,IAAI,CAAC;MAC7B,CAAC,EACDiE,CAAC,IAAIA,CAAC,CAAC;MAET,IAAIf,QAAQ,EAAE;QACZ,IAAI,CAACF,iBAAiB,GAAG,IAAI;MAC/B;MAEA,OAAO,IAAI,CAACA,iBAAiB;IAC/B,CAAC;;IAED;IACA;IACApD,SAAS,CAACsE,MAAM,CAAC5D,SAAS,CAACC,kBAAkB,GAAG,UAASC,QAAQ,EAAE;MACjE,OAAO,IAAI,CAAC2D,8BAA8B,CAAC5D,kBAAkB,CAACC,QAAQ,CAAC;IACzE,CAAC;IAEDZ,SAAS,CAACsE,MAAM,CAAC5D,SAAS,CAACqC,qBAAqB,GAAG,UAASC,UAAU,EAAE;MACtE,OAAOG,mCAAmC,CACxCnD,SAAS,CAACC,wBAAwB,CAAC,IAAI,CAACiB,SAAS,CAAC,CAAC,CAAC,EACpD8B,UACF,CAAC;IACH,CAAC;IAED,SAASG,mCAAmCA,CAACjD,KAAK,EAAE8C,UAAU,EAAE;MAC9D,MAAMwB,OAAO,GAAG3E,iBAAiB,CAACmD,UAAU,CAAC;;MAE7C;MACA,MAAMyB,IAAI,GAAG7E,WAAW,CACtBM,KAAK,EACLE,IAAI,IAAI,IAAI,EACZ,CAACsE,IAAI,EAAEtE,IAAI,EAAEuE,QAAQ,KAAK,IAAI,EAC9BH,OAAO,CAACC,IACV,CAAC;MACD,MAAMG,gBAAgB,GAAGC,WAAW,CAACJ,IAAI,CAAC;MAE1C,IAAID,OAAO,CAACM,SAAS,EAAE;QACrB;QACA;QACA,OAAOF,gBAAgB;MACzB;;MAEA;MACA;MACA;MACA,MAAMG,oBAAoB,GAAG,CAAC,CAAC;MAE/BlE,MAAM,CAACQ,IAAI,CAACuD,gBAAgB,CAAC,CAACX,OAAO,CAAC7D,IAAI,IAAI;QAC5C,IAAI,CAACwE,gBAAgB,CAACxE,IAAI,CAAC,EAAE;UAC3B2E,oBAAoB,CAAC3E,IAAI,CAAC,GAAG,KAAK;QACpC;MACF,CAAC,CAAC;MAEF,OAAO2E,oBAAoB;IAC7B;IAEA,SAASC,QAAQA,CAACC,QAAQ,EAAE;MAC1B,OAAOpE,MAAM,CAACQ,IAAI,CAAC,IAAIrB,SAAS,CAACS,OAAO,CAACwE,QAAQ,CAAC,CAACC,MAAM,CAAC;;MAE1D;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;IACF;;IAEA;IACA,SAASrB,gBAAgBA,CAACsB,GAAG,EAAE9D,IAAI,EAAE;MACnC,OAAOR,MAAM,CAACQ,IAAI,CAAC8D,GAAG,CAAC,CAACC,KAAK,CAACC,CAAC,IAAIhE,IAAI,CAAC6B,QAAQ,CAACmC,CAAC,CAAC,CAAC;IACtD;IAEA,SAASrD,kBAAkBA,CAAC5B,IAAI,EAAE;MAChC,OAAOA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACiB,IAAI,CAAC5B,YAAY,CAAC;IAC3C;;IAEA;IACA;IACA,SAASmF,WAAWA,CAACJ,IAAI,EAAe;MAAA,IAAba,MAAM,GAAAC,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,EAAE;MACpC,MAAMzC,MAAM,GAAG,CAAC,CAAC;MAEjBjC,MAAM,CAACQ,IAAI,CAACoD,IAAI,CAAC,CAACR,OAAO,CAACuB,GAAG,IAAI;QAC/B,MAAMC,KAAK,GAAGhB,IAAI,CAACe,GAAG,CAAC;QACvB,IAAIC,KAAK,KAAK5E,MAAM,CAAC4E,KAAK,CAAC,EAAE;UAC3B5E,MAAM,CAACC,MAAM,CAACgC,MAAM,EAAE+B,WAAW,CAACY,KAAK,KAAArE,MAAA,CAAKkE,MAAM,GAAGE,GAAG,MAAG,CAAC,CAAC;QAC/D,CAAC,MAAM;UACL1C,MAAM,CAACwC,MAAM,GAAGE,GAAG,CAAC,GAAGC,KAAK;QAC9B;MACF,CAAC,CAAC;MAEF,OAAO3C,MAAM;IACf;IAAC4C,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICzVDtG,MAAM,CAACuG,MAAM,CAAC;MAACrG,MAAM,EAACA,CAAA,KAAIA,MAAM;MAACsG,iBAAiB,EAACA,CAAA,KAAIA,iBAAiB;MAACC,uBAAuB,EAACA,CAAA,KAAIA,uBAAuB;MAACC,sBAAsB,EAACA,CAAA,KAAIA,sBAAsB;MAACC,sBAAsB,EAACA,CAAA,KAAIA,sBAAsB;MAACC,WAAW,EAACA,CAAA,KAAIA,WAAW;MAACzG,YAAY,EAACA,CAAA,KAAIA,YAAY;MAACC,gBAAgB,EAACA,CAAA,KAAIA,gBAAgB;MAACyG,kBAAkB,EAACA,CAAA,KAAIA,kBAAkB;MAACC,cAAc,EAACA,CAAA,KAAIA,cAAc;MAACzG,WAAW,EAACA,CAAA,KAAIA,WAAW;MAAC0G,+BAA+B,EAACA,CAAA,KAAIA,+BAA+B;MAACzG,iBAAiB,EAACA,CAAA,KAAIA,iBAAiB;MAAC0G,oBAAoB,EAACA,CAAA,KAAIA;IAAoB,CAAC,CAAC;IAAC,IAAI/D,eAAe;IAACjD,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAAC0C,eAAe,GAAC1C,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAErtB,MAAMN,MAAM,GAAGoB,MAAM,CAACH,SAAS,CAAC+F,cAAc;IAc9C,MAAMV,iBAAiB,GAAG;MAC/BW,GAAG,EAAEC,cAAc,CAACC,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC;MAC7CC,GAAG,EAAEF,cAAc,CAACC,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC;MAC7CE,IAAI,EAAEH,cAAc,CAACC,QAAQ,IAAIA,QAAQ,IAAI,CAAC,CAAC;MAC/CG,IAAI,EAAEJ,cAAc,CAACC,QAAQ,IAAIA,QAAQ,IAAI,CAAC,CAAC;MAC/CI,IAAI,EAAE;QACJC,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IAAIA,OAAO,CAACtF,MAAM,KAAK,CAAC,IAC3C,OAAOsF,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC9B,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,EAAE;YACxC,MAAMG,KAAK,CAAC,kDAAkD,CAAC;UACjE;;UAEA;UACA,MAAMC,OAAO,GAAGJ,OAAO,CAAC,CAAC,CAAC;UAC1B,MAAMK,SAAS,GAAGL,OAAO,CAAC,CAAC,CAAC;UAC5B,OAAOzB,KAAK,IACV,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG6B,OAAO,KAAKC,SAClD;QACH;MACF,CAAC;MACD9D,GAAG,EAAE;QACHwD,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;YAC3B,MAAMG,KAAK,CAAC,oBAAoB,CAAC;UACnC;UAEA,MAAMG,eAAe,GAAGN,OAAO,CAAC/G,GAAG,CAACsH,MAAM,IAAI;YAC5C,IAAIA,MAAM,YAAYC,MAAM,EAAE;cAC5B,OAAOnB,oBAAoB,CAACkB,MAAM,CAAC;YACrC;YAEA,IAAI9H,gBAAgB,CAAC8H,MAAM,CAAC,EAAE;cAC5B,MAAMJ,KAAK,CAAC,yBAAyB,CAAC;YACxC;YAEA,OAAOpB,sBAAsB,CAACwB,MAAM,CAAC;UACvC,CAAC,CAAC;UAEF,OAAOhC,KAAK,IAAI;YACd;YACA,IAAIA,KAAK,KAAKpC,SAAS,EAAE;cACvBoC,KAAK,GAAG,IAAI;YACd;YAEA,OAAO+B,eAAe,CAAClG,IAAI,CAACoC,OAAO,IAAIA,OAAO,CAAC+B,KAAK,CAAC,CAAC;UACxD,CAAC;QACH;MACF,CAAC;MACDkC,KAAK,EAAE;QACL;QACA;QACA;QACAC,oBAAoB,EAAE,IAAI;QAC1BX,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;YAC/B;YACA;YACAA,OAAO,GAAG,CAAC;UACb,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;YACtC,MAAMG,KAAK,CAAC,sBAAsB,CAAC;UACrC;UAEA,OAAO5B,KAAK,IAAI0B,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,IAAIA,KAAK,CAAC7D,MAAM,KAAKsF,OAAO;QAClE;MACF,CAAC;MACDW,KAAK,EAAE;QACL;QACA;QACA;QACA;QACAC,qBAAqB,EAAE,IAAI;QAC3Bb,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAMa,eAAe,GAAG;cACtB,QAAQ,EAAE,CAAC;cACX,QAAQ,EAAE,CAAC;cACX,QAAQ,EAAE,CAAC;cACX,OAAO,EAAE,CAAC;cACV,SAAS,EAAE,CAAC;cACZ,WAAW,EAAE,CAAC;cACd,UAAU,EAAE,CAAC;cACb,MAAM,EAAE,CAAC;cACT,MAAM,EAAE,CAAC;cACT,MAAM,EAAE,EAAE;cACV,OAAO,EAAE,EAAE;cACX,WAAW,EAAE,EAAE;cACf,YAAY,EAAE,EAAE;cAChB,QAAQ,EAAE,EAAE;cACZ,qBAAqB,EAAE,EAAE;cACzB,KAAK,EAAE,EAAE;cACT,WAAW,EAAE,EAAE;cACf,MAAM,EAAE,EAAE;cACV,SAAS,EAAE,EAAE;cACb,QAAQ,EAAE,CAAC,CAAC;cACZ,QAAQ,EAAE;YACZ,CAAC;YACD,IAAI,CAACtI,MAAM,CAAC0E,IAAI,CAAC4D,eAAe,EAAEb,OAAO,CAAC,EAAE;cAC1C,MAAMG,KAAK,oCAAAjG,MAAA,CAAoC8F,OAAO,CAAE,CAAC;YAC3D;YACAA,OAAO,GAAGa,eAAe,CAACb,OAAO,CAAC;UACpC,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;YACtC,IAAIA,OAAO,KAAK,CAAC,IAAIA,OAAO,GAAG,CAAC,CAAC,IAC3BA,OAAO,GAAG,EAAE,IAAIA,OAAO,KAAK,GAAI,EAAE;cACtC,MAAMG,KAAK,kCAAAjG,MAAA,CAAkC8F,OAAO,CAAE,CAAC;YACzD;UACF,CAAC,MAAM;YACL,MAAMG,KAAK,CAAC,+CAA+C,CAAC;UAC9D;UAEA,OAAO5B,KAAK,IACVA,KAAK,KAAKpC,SAAS,IAAIb,eAAe,CAACwF,EAAE,CAACC,KAAK,CAACxC,KAAK,CAAC,KAAKyB,OAC5D;QACH;MACF,CAAC;MACDgB,WAAW,EAAE;QACXjB,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,MAAMiB,IAAI,GAAGC,iBAAiB,CAAClB,OAAO,EAAE,aAAa,CAAC;UACtD,OAAOzB,KAAK,IAAI;YACd,MAAM4C,OAAO,GAAGC,eAAe,CAAC7C,KAAK,EAAE0C,IAAI,CAACvG,MAAM,CAAC;YACnD,OAAOyG,OAAO,IAAIF,IAAI,CAAC/C,KAAK,CAAC,CAACmD,IAAI,EAAE7G,CAAC,KAAK,CAAC2G,OAAO,CAAC3G,CAAC,CAAC,GAAG6G,IAAI,MAAMA,IAAI,CAAC;UACzE,CAAC;QACH;MACF,CAAC;MACDC,WAAW,EAAE;QACXvB,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,MAAMiB,IAAI,GAAGC,iBAAiB,CAAClB,OAAO,EAAE,aAAa,CAAC;UACtD,OAAOzB,KAAK,IAAI;YACd,MAAM4C,OAAO,GAAGC,eAAe,CAAC7C,KAAK,EAAE0C,IAAI,CAACvG,MAAM,CAAC;YACnD,OAAOyG,OAAO,IAAIF,IAAI,CAAC7G,IAAI,CAAC,CAACiH,IAAI,EAAE7G,CAAC,KAAK,CAAC,CAAC2G,OAAO,CAAC3G,CAAC,CAAC,GAAG6G,IAAI,MAAMA,IAAI,CAAC;UACzE,CAAC;QACH;MACF,CAAC;MACDE,aAAa,EAAE;QACbxB,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,MAAMiB,IAAI,GAAGC,iBAAiB,CAAClB,OAAO,EAAE,eAAe,CAAC;UACxD,OAAOzB,KAAK,IAAI;YACd,MAAM4C,OAAO,GAAGC,eAAe,CAAC7C,KAAK,EAAE0C,IAAI,CAACvG,MAAM,CAAC;YACnD,OAAOyG,OAAO,IAAIF,IAAI,CAAC/C,KAAK,CAAC,CAACmD,IAAI,EAAE7G,CAAC,KAAK,EAAE2G,OAAO,CAAC3G,CAAC,CAAC,GAAG6G,IAAI,CAAC,CAAC;UACjE,CAAC;QACH;MACF,CAAC;MACDG,aAAa,EAAE;QACbzB,sBAAsBA,CAACC,OAAO,EAAE;UAC9B,MAAMiB,IAAI,GAAGC,iBAAiB,CAAClB,OAAO,EAAE,eAAe,CAAC;UACxD,OAAOzB,KAAK,IAAI;YACd,MAAM4C,OAAO,GAAGC,eAAe,CAAC7C,KAAK,EAAE0C,IAAI,CAACvG,MAAM,CAAC;YACnD,OAAOyG,OAAO,IAAIF,IAAI,CAAC7G,IAAI,CAAC,CAACiH,IAAI,EAAE7G,CAAC,KAAK,CAAC2G,OAAO,CAAC3G,CAAC,CAAC,GAAG6G,IAAI,MAAMA,IAAI,CAAC;UACxE,CAAC;QACH;MACF,CAAC;MACDI,MAAM,EAAE;QACN1B,sBAAsBA,CAACC,OAAO,EAAE3D,aAAa,EAAE;UAC7C,IAAI,EAAE,OAAO2D,OAAO,KAAK,QAAQ,IAAIA,OAAO,YAAYQ,MAAM,CAAC,EAAE;YAC/D,MAAML,KAAK,CAAC,qCAAqC,CAAC;UACpD;UAEA,IAAIuB,MAAM;UACV,IAAIrF,aAAa,CAACsF,QAAQ,KAAKxF,SAAS,EAAE;YACxC;YACA;;YAEA;YACA;YACA;YACA,IAAI,QAAQ,CAACyF,IAAI,CAACvF,aAAa,CAACsF,QAAQ,CAAC,EAAE;cACzC,MAAM,IAAIxB,KAAK,CAAC,mDAAmD,CAAC;YACtE;YAEA,MAAM0B,MAAM,GAAG7B,OAAO,YAAYQ,MAAM,GAAGR,OAAO,CAAC6B,MAAM,GAAG7B,OAAO;YACnE0B,MAAM,GAAG,IAAIlB,MAAM,CAACqB,MAAM,EAAExF,aAAa,CAACsF,QAAQ,CAAC;UACrD,CAAC,MAAM,IAAI3B,OAAO,YAAYQ,MAAM,EAAE;YACpCkB,MAAM,GAAG1B,OAAO;UAClB,CAAC,MAAM;YACL0B,MAAM,GAAG,IAAIlB,MAAM,CAACR,OAAO,CAAC;UAC9B;UAEA,OAAOX,oBAAoB,CAACqC,MAAM,CAAC;QACrC;MACF,CAAC;MACDI,UAAU,EAAE;QACVpB,oBAAoB,EAAE,IAAI;QAC1BX,sBAAsBA,CAACC,OAAO,EAAE3D,aAAa,EAAEG,OAAO,EAAE;UACtD,IAAI,CAAClB,eAAe,CAACyG,cAAc,CAAC/B,OAAO,CAAC,EAAE;YAC5C,MAAMG,KAAK,CAAC,2BAA2B,CAAC;UAC1C;UAEA,MAAM6B,YAAY,GAAG,CAACvJ,gBAAgB,CACpCkB,MAAM,CAACQ,IAAI,CAAC6F,OAAO,CAAC,CACjB5G,MAAM,CAACkF,GAAG,IAAI,CAAC/F,MAAM,CAAC0E,IAAI,CAACgF,iBAAiB,EAAE3D,GAAG,CAAC,CAAC,CACnD4D,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKzI,MAAM,CAACC,MAAM,CAACuI,CAAC,EAAE;YAAC,CAACC,CAAC,GAAGpC,OAAO,CAACoC,CAAC;UAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAC5D,IAAI,CAAC;UAEP,IAAIC,UAAU;UACd,IAAIL,YAAY,EAAE;YAChB;YACA;YACA;YACA;YACAK,UAAU,GACRvD,uBAAuB,CAACkB,OAAO,EAAExD,OAAO,EAAE;cAAC8F,WAAW,EAAE;YAAI,CAAC,CAAC;UAClE,CAAC,MAAM;YACLD,UAAU,GAAGE,oBAAoB,CAACvC,OAAO,EAAExD,OAAO,CAAC;UACrD;UAEA,OAAO+B,KAAK,IAAI;YACd,IAAI,CAAC0B,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,EAAE;cACzB,OAAO,KAAK;YACd;YAEA,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,KAAK,CAAC7D,MAAM,EAAE,EAAEF,CAAC,EAAE;cACrC,MAAMgI,YAAY,GAAGjE,KAAK,CAAC/D,CAAC,CAAC;cAC7B,IAAIiI,GAAG;cACP,IAAIT,YAAY,EAAE;gBAChB;gBACA;gBACA;gBACA,IAAI,CAAC/C,WAAW,CAACuD,YAAY,CAAC,EAAE;kBAC9B,OAAO,KAAK;gBACd;gBAEAC,GAAG,GAAGD,YAAY;cACpB,CAAC,MAAM;gBACL;gBACA;gBACAC,GAAG,GAAG,CAAC;kBAAClE,KAAK,EAAEiE,YAAY;kBAAEE,WAAW,EAAE;gBAAI,CAAC,CAAC;cAClD;cACA;cACA,IAAIL,UAAU,CAACI,GAAG,CAAC,CAAC7G,MAAM,EAAE;gBAC1B,OAAOpB,CAAC,CAAC,CAAC;cACZ;YACF;YAEA,OAAO,KAAK;UACd,CAAC;QACH;MACF;IACF,CAAC;IAED;IACA,MAAMyH,iBAAiB,GAAG;MACxBU,IAAIA,CAACC,WAAW,EAAEpG,OAAO,EAAE8F,WAAW,EAAE;QACtC,OAAOO,mBAAmB,CACxBC,+BAA+B,CAACF,WAAW,EAAEpG,OAAO,EAAE8F,WAAW,CACnE,CAAC;MACH,CAAC;MAEDS,GAAGA,CAACH,WAAW,EAAEpG,OAAO,EAAE8F,WAAW,EAAE;QACrC,MAAMU,QAAQ,GAAGF,+BAA+B,CAC9CF,WAAW,EACXpG,OAAO,EACP8F,WACF,CAAC;;QAED;QACA;QACA,IAAIU,QAAQ,CAACtI,MAAM,KAAK,CAAC,EAAE;UACzB,OAAOsI,QAAQ,CAAC,CAAC,CAAC;QACpB;QAEA,OAAOC,GAAG,IAAI;UACZ,MAAMrH,MAAM,GAAGoH,QAAQ,CAAC5I,IAAI,CAAC8I,EAAE,IAAIA,EAAE,CAACD,GAAG,CAAC,CAACrH,MAAM,CAAC;UAClD;UACA;UACA,OAAO;YAACA;UAAM,CAAC;QACjB,CAAC;MACH,CAAC;MAEDuH,IAAIA,CAACP,WAAW,EAAEpG,OAAO,EAAE8F,WAAW,EAAE;QACtC,MAAMU,QAAQ,GAAGF,+BAA+B,CAC9CF,WAAW,EACXpG,OAAO,EACP8F,WACF,CAAC;QACD,OAAOW,GAAG,IAAI;UACZ,MAAMrH,MAAM,GAAGoH,QAAQ,CAAC9E,KAAK,CAACgF,EAAE,IAAI,CAACA,EAAE,CAACD,GAAG,CAAC,CAACrH,MAAM,CAAC;UACpD;UACA;UACA,OAAO;YAACA;UAAM,CAAC;QACjB,CAAC;MACH,CAAC;MAEDwH,MAAMA,CAACC,aAAa,EAAE7G,OAAO,EAAE;QAC7B;QACAA,OAAO,CAAC8G,eAAe,CAAC,EAAE,CAAC;QAC3B9G,OAAO,CAAC+G,SAAS,GAAG,IAAI;QAExB,IAAI,EAAEF,aAAa,YAAYG,QAAQ,CAAC,EAAE;UACxC;UACA;UACAH,aAAa,GAAGG,QAAQ,CAAC,KAAK,YAAAtJ,MAAA,CAAYmJ,aAAa,CAAE,CAAC;QAC5D;;QAEA;QACA;QACA,OAAOJ,GAAG,KAAK;UAACrH,MAAM,EAAEyH,aAAa,CAACpG,IAAI,CAACgG,GAAG,EAAEA,GAAG;QAAC,CAAC,CAAC;MACxD,CAAC;MAED;MACA;MACAQ,QAAQA,CAAA,EAAG;QACT,OAAO,OAAO;UAAC7H,MAAM,EAAE;QAAI,CAAC,CAAC;MAC/B;IACF,CAAC;;IAED;IACA;IACA;IACA;IACA,MAAM8H,eAAe,GAAG;MACtBpH,GAAGA,CAAC0D,OAAO,EAAE;QACX,OAAO2D,sCAAsC,CAC3C5E,sBAAsB,CAACiB,OAAO,CAChC,CAAC;MACH,CAAC;MACD4D,IAAIA,CAAC5D,OAAO,EAAE3D,aAAa,EAAEG,OAAO,EAAE;QACpC,OAAOqH,qBAAqB,CAACtB,oBAAoB,CAACvC,OAAO,EAAExD,OAAO,CAAC,CAAC;MACtE,CAAC;MACDsH,GAAGA,CAAC9D,OAAO,EAAE;QACX,OAAO6D,qBAAqB,CAC1BF,sCAAsC,CAAC5E,sBAAsB,CAACiB,OAAO,CAAC,CACxE,CAAC;MACH,CAAC;MACD+D,IAAIA,CAAC/D,OAAO,EAAE;QACZ,OAAO6D,qBAAqB,CAC1BF,sCAAsC,CACpC9E,iBAAiB,CAACtC,GAAG,CAACwD,sBAAsB,CAACC,OAAO,CACtD,CACF,CAAC;MACH,CAAC;MACDgE,OAAOA,CAAChE,OAAO,EAAE;QACf,MAAMiE,MAAM,GAAGN,sCAAsC,CACnDpF,KAAK,IAAIA,KAAK,KAAKpC,SACrB,CAAC;QACD,OAAO6D,OAAO,GAAGiE,MAAM,GAAGJ,qBAAqB,CAACI,MAAM,CAAC;MACzD,CAAC;MACD;MACAtC,QAAQA,CAAC3B,OAAO,EAAE3D,aAAa,EAAE;QAC/B,IAAI,CAAC9D,MAAM,CAAC0E,IAAI,CAACZ,aAAa,EAAE,QAAQ,CAAC,EAAE;UACzC,MAAM8D,KAAK,CAAC,yBAAyB,CAAC;QACxC;QAEA,OAAO+D,iBAAiB;MAC1B,CAAC;MACD;MACAC,YAAYA,CAACnE,OAAO,EAAE3D,aAAa,EAAE;QACnC,IAAI,CAACA,aAAa,CAAC+H,KAAK,EAAE;UACxB,MAAMjE,KAAK,CAAC,4BAA4B,CAAC;QAC3C;QAEA,OAAO+D,iBAAiB;MAC1B,CAAC;MACDG,IAAIA,CAACrE,OAAO,EAAE3D,aAAa,EAAEG,OAAO,EAAE;QACpC,IAAI,CAACyD,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;UAC3B,MAAMG,KAAK,CAAC,qBAAqB,CAAC;QACpC;;QAEA;QACA,IAAIH,OAAO,CAACtF,MAAM,KAAK,CAAC,EAAE;UACxB,OAAOyE,cAAc;QACvB;QAEA,MAAMmF,gBAAgB,GAAGtE,OAAO,CAAC/G,GAAG,CAACsL,SAAS,IAAI;UAChD;UACA,IAAI9L,gBAAgB,CAAC8L,SAAS,CAAC,EAAE;YAC/B,MAAMpE,KAAK,CAAC,0BAA0B,CAAC;UACzC;;UAEA;UACA,OAAOoC,oBAAoB,CAACgC,SAAS,EAAE/H,OAAO,CAAC;QACjD,CAAC,CAAC;;QAEF;QACA;QACA,OAAOgI,mBAAmB,CAACF,gBAAgB,CAAC;MAC9C,CAAC;MACDF,KAAKA,CAACpE,OAAO,EAAE3D,aAAa,EAAEG,OAAO,EAAEiI,MAAM,EAAE;QAC7C,IAAI,CAACA,MAAM,EAAE;UACX,MAAMtE,KAAK,CAAC,2CAA2C,CAAC;QAC1D;QAEA3D,OAAO,CAACkI,YAAY,GAAG,IAAI;;QAE3B;QACA;QACA;QACA;QACA,IAAIC,WAAW,EAAEC,KAAK,EAAEC,QAAQ;QAChC,IAAIvJ,eAAe,CAACyG,cAAc,CAAC/B,OAAO,CAAC,IAAIzH,MAAM,CAAC0E,IAAI,CAAC+C,OAAO,EAAE,WAAW,CAAC,EAAE;UAChF;UACA2E,WAAW,GAAG3E,OAAO,CAACmE,YAAY;UAClCS,KAAK,GAAG5E,OAAO,CAAC8E,SAAS;UACzBD,QAAQ,GAAGtG,KAAK,IAAI;YAClB;YACA;YACA;YACA,IAAI,CAACA,KAAK,EAAE;cACV,OAAO,IAAI;YACb;YAEA,IAAI,CAACA,KAAK,CAACwG,IAAI,EAAE;cACf,OAAOC,OAAO,CAACC,aAAa,CAC1BL,KAAK,EACL;gBAACG,IAAI,EAAE,OAAO;gBAAEG,WAAW,EAAEC,YAAY,CAAC5G,KAAK;cAAC,CAClD,CAAC;YACH;YAEA,IAAIA,KAAK,CAACwG,IAAI,KAAK,OAAO,EAAE;cAC1B,OAAOC,OAAO,CAACC,aAAa,CAACL,KAAK,EAAErG,KAAK,CAAC;YAC5C;YAEA,OAAOyG,OAAO,CAACI,oBAAoB,CAAC7G,KAAK,EAAEqG,KAAK,EAAED,WAAW,CAAC,GAC1D,CAAC,GACDA,WAAW,GAAG,CAAC;UACrB,CAAC;QACH,CAAC,MAAM;UACLA,WAAW,GAAGtI,aAAa,CAAC8H,YAAY;UAExC,IAAI,CAAClF,WAAW,CAACe,OAAO,CAAC,EAAE;YACzB,MAAMG,KAAK,CAAC,mDAAmD,CAAC;UAClE;UAEAyE,KAAK,GAAGO,YAAY,CAACnF,OAAO,CAAC;UAE7B6E,QAAQ,GAAGtG,KAAK,IAAI;YAClB,IAAI,CAACU,WAAW,CAACV,KAAK,CAAC,EAAE;cACvB,OAAO,IAAI;YACb;YAEA,OAAO8G,uBAAuB,CAACT,KAAK,EAAErG,KAAK,CAAC;UAC9C,CAAC;QACH;QAEA,OAAO+G,cAAc,IAAI;UACvB;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAM1J,MAAM,GAAG;YAACA,MAAM,EAAE;UAAK,CAAC;UAC9BoD,sBAAsB,CAACsG,cAAc,CAAC,CAACpH,KAAK,CAACqH,MAAM,IAAI;YACrD;YACA;YACA,IAAIC,WAAW;YACf,IAAI,CAAChJ,OAAO,CAACiJ,SAAS,EAAE;cACtB,IAAI,EAAE,OAAOF,MAAM,CAAChH,KAAK,KAAK,QAAQ,CAAC,EAAE;gBACvC,OAAO,IAAI;cACb;cAEAiH,WAAW,GAAGX,QAAQ,CAACU,MAAM,CAAChH,KAAK,CAAC;;cAEpC;cACA,IAAIiH,WAAW,KAAK,IAAI,IAAIA,WAAW,GAAGb,WAAW,EAAE;gBACrD,OAAO,IAAI;cACb;;cAEA;cACA,IAAI/I,MAAM,CAACiJ,QAAQ,KAAK1I,SAAS,IAAIP,MAAM,CAACiJ,QAAQ,IAAIW,WAAW,EAAE;gBACnE,OAAO,IAAI;cACb;YACF;YAEA5J,MAAM,CAACA,MAAM,GAAG,IAAI;YACpBA,MAAM,CAACiJ,QAAQ,GAAGW,WAAW;YAE7B,IAAID,MAAM,CAACG,YAAY,EAAE;cACvB9J,MAAM,CAAC8J,YAAY,GAAGH,MAAM,CAACG,YAAY;YAC3C,CAAC,MAAM;cACL,OAAO9J,MAAM,CAAC8J,YAAY;YAC5B;YAEA,OAAO,CAAClJ,OAAO,CAACiJ,SAAS;UAC3B,CAAC,CAAC;UAEF,OAAO7J,MAAM;QACf,CAAC;MACH;IACF,CAAC;;IAED;IACA;IACA;IACA;IACA,SAAS+J,eAAeA,CAACC,WAAW,EAAE;MACpC,IAAIA,WAAW,CAAClL,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAOwJ,iBAAiB;MAC1B;MAEA,IAAI0B,WAAW,CAAClL,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAOkL,WAAW,CAAC,CAAC,CAAC;MACvB;MAEA,OAAOC,aAAa,IAAI;QACtB,MAAMC,KAAK,GAAG,CAAC,CAAC;QAChBA,KAAK,CAAClK,MAAM,GAAGgK,WAAW,CAAC1H,KAAK,CAACgF,EAAE,IAAI;UACrC,MAAM6C,SAAS,GAAG7C,EAAE,CAAC2C,aAAa,CAAC;;UAEnC;UACA;UACA;UACA;UACA,IAAIE,SAAS,CAACnK,MAAM,IAChBmK,SAAS,CAAClB,QAAQ,KAAK1I,SAAS,IAChC2J,KAAK,CAACjB,QAAQ,KAAK1I,SAAS,EAAE;YAChC2J,KAAK,CAACjB,QAAQ,GAAGkB,SAAS,CAAClB,QAAQ;UACrC;;UAEA;UACA;UACA;UACA,IAAIkB,SAAS,CAACnK,MAAM,IAAImK,SAAS,CAACL,YAAY,EAAE;YAC9CI,KAAK,CAACJ,YAAY,GAAGK,SAAS,CAACL,YAAY;UAC7C;UAEA,OAAOK,SAAS,CAACnK,MAAM;QACzB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACkK,KAAK,CAAClK,MAAM,EAAE;UACjB,OAAOkK,KAAK,CAACjB,QAAQ;UACrB,OAAOiB,KAAK,CAACJ,YAAY;QAC3B;QAEA,OAAOI,KAAK;MACd,CAAC;IACH;IAEA,MAAMjD,mBAAmB,GAAG8C,eAAe;IAC3C,MAAMnB,mBAAmB,GAAGmB,eAAe;IAE3C,SAAS7C,+BAA+BA,CAACkD,SAAS,EAAExJ,OAAO,EAAE8F,WAAW,EAAE;MACxE,IAAI,CAACrC,KAAK,CAACC,OAAO,CAAC8F,SAAS,CAAC,IAAIA,SAAS,CAACtL,MAAM,KAAK,CAAC,EAAE;QACvD,MAAMyF,KAAK,CAAC,sCAAsC,CAAC;MACrD;MAEA,OAAO6F,SAAS,CAAC/M,GAAG,CAAC2J,WAAW,IAAI;QAClC,IAAI,CAACtH,eAAe,CAACyG,cAAc,CAACa,WAAW,CAAC,EAAE;UAChD,MAAMzC,KAAK,CAAC,+CAA+C,CAAC;QAC9D;QAEA,OAAOrB,uBAAuB,CAAC8D,WAAW,EAAEpG,OAAO,EAAE;UAAC8F;QAAW,CAAC,CAAC;MACrE,CAAC,CAAC;IACJ;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASxD,uBAAuBA,CAACmH,WAAW,EAAEzJ,OAAO,EAAgB;MAAA,IAAd0J,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MACxE,MAAM8H,WAAW,GAAGxM,MAAM,CAACQ,IAAI,CAAC8L,WAAW,CAAC,CAAChN,GAAG,CAACqF,GAAG,IAAI;QACtD,MAAMsE,WAAW,GAAGqD,WAAW,CAAC3H,GAAG,CAAC;QAEpC,IAAIA,GAAG,CAAC8H,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;UAC5B;UACA;UACA,IAAI,CAAC7N,MAAM,CAAC0E,IAAI,CAACgF,iBAAiB,EAAE3D,GAAG,CAAC,EAAE;YACxC,MAAM,IAAI6B,KAAK,mCAAAjG,MAAA,CAAmCoE,GAAG,CAAE,CAAC;UAC1D;UAEA9B,OAAO,CAAC6J,SAAS,GAAG,KAAK;UACzB,OAAOpE,iBAAiB,CAAC3D,GAAG,CAAC,CAACsE,WAAW,EAAEpG,OAAO,EAAE0J,OAAO,CAAC5D,WAAW,CAAC;QAC1E;;QAEA;QACA;QACA;QACA,IAAI,CAAC4D,OAAO,CAAC5D,WAAW,EAAE;UACxB9F,OAAO,CAAC8G,eAAe,CAAChF,GAAG,CAAC;QAC9B;;QAEA;QACA;QACA;QACA,IAAI,OAAOsE,WAAW,KAAK,UAAU,EAAE;UACrC,OAAOzG,SAAS;QAClB;QAEA,MAAMmK,aAAa,GAAGpH,kBAAkB,CAACZ,GAAG,CAAC;QAC7C,MAAMiI,YAAY,GAAGhE,oBAAoB,CACvCK,WAAW,EACXpG,OAAO,EACP0J,OAAO,CAACzB,MACV,CAAC;QAED,OAAOxB,GAAG,IAAIsD,YAAY,CAACD,aAAa,CAACrD,GAAG,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC7J,MAAM,CAACoN,OAAO,CAAC;MAElB,OAAO3D,mBAAmB,CAACsD,WAAW,CAAC;IACzC;IAEA;IACA;IACA;IACA;IACA,SAAS5D,oBAAoBA,CAAClG,aAAa,EAAEG,OAAO,EAAEiI,MAAM,EAAE;MAC5D,IAAIpI,aAAa,YAAYmE,MAAM,EAAE;QACnChE,OAAO,CAAC6J,SAAS,GAAG,KAAK;QACzB,OAAO1C,sCAAsC,CAC3CtE,oBAAoB,CAAChD,aAAa,CACpC,CAAC;MACH;MAEA,IAAI5D,gBAAgB,CAAC4D,aAAa,CAAC,EAAE;QACnC,OAAOoK,uBAAuB,CAACpK,aAAa,EAAEG,OAAO,EAAEiI,MAAM,CAAC;MAChE;MAEA,OAAOd,sCAAsC,CAC3C5E,sBAAsB,CAAC1C,aAAa,CACtC,CAAC;IACH;;IAEA;IACA;IACA;IACA,SAASsH,sCAAsCA,CAAC+C,cAAc,EAAgB;MAAA,IAAdR,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MAC1E,OAAOsI,QAAQ,IAAI;QACjB,MAAMC,QAAQ,GAAGV,OAAO,CAACxF,oBAAoB,GACzCiG,QAAQ,GACR3H,sBAAsB,CAAC2H,QAAQ,EAAET,OAAO,CAACtF,qBAAqB,CAAC;QAEnE,MAAMkF,KAAK,GAAG,CAAC,CAAC;QAChBA,KAAK,CAAClK,MAAM,GAAGgL,QAAQ,CAACxM,IAAI,CAACyM,OAAO,IAAI;UACtC,IAAIC,OAAO,GAAGJ,cAAc,CAACG,OAAO,CAACtI,KAAK,CAAC;;UAE3C;UACA;UACA,IAAI,OAAOuI,OAAO,KAAK,QAAQ,EAAE;YAC/B;YACA;YACA;YACA,IAAI,CAACD,OAAO,CAACnB,YAAY,EAAE;cACzBmB,OAAO,CAACnB,YAAY,GAAG,CAACoB,OAAO,CAAC;YAClC;YAEAA,OAAO,GAAG,IAAI;UAChB;;UAEA;UACA;UACA,IAAIA,OAAO,IAAID,OAAO,CAACnB,YAAY,EAAE;YACnCI,KAAK,CAACJ,YAAY,GAAGmB,OAAO,CAACnB,YAAY;UAC3C;UAEA,OAAOoB,OAAO;QAChB,CAAC,CAAC;QAEF,OAAOhB,KAAK;MACd,CAAC;IACH;;IAEA;IACA,SAAST,uBAAuBA,CAAClD,CAAC,EAAEC,CAAC,EAAE;MACrC,MAAM2E,MAAM,GAAG5B,YAAY,CAAChD,CAAC,CAAC;MAC9B,MAAM6E,MAAM,GAAG7B,YAAY,CAAC/C,CAAC,CAAC;MAE9B,OAAO6E,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAED,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC;IACjE;;IAEA;IACA;IACO,SAASjI,sBAAsBA,CAACoI,eAAe,EAAE;MACtD,IAAI1O,gBAAgB,CAAC0O,eAAe,CAAC,EAAE;QACrC,MAAMhH,KAAK,CAAC,yDAAyD,CAAC;MACxE;;MAEA;MACA;MACA;MACA;MACA,IAAIgH,eAAe,IAAI,IAAI,EAAE;QAC3B,OAAO5I,KAAK,IAAIA,KAAK,IAAI,IAAI;MAC/B;MAEA,OAAOA,KAAK,IAAIjD,eAAe,CAACwF,EAAE,CAACsG,MAAM,CAACD,eAAe,EAAE5I,KAAK,CAAC;IACnE;IAEA,SAAS2F,iBAAiBA,CAACmD,mBAAmB,EAAE;MAC9C,OAAO;QAACzL,MAAM,EAAE;MAAI,CAAC;IACvB;IAEO,SAASoD,sBAAsBA,CAAC2H,QAAQ,EAAEW,aAAa,EAAE;MAC9D,MAAMC,WAAW,GAAG,EAAE;MAEtBZ,QAAQ,CAAC5J,OAAO,CAACwI,MAAM,IAAI;QACzB,MAAMiC,WAAW,GAAGvH,KAAK,CAACC,OAAO,CAACqF,MAAM,CAAChH,KAAK,CAAC;;QAE/C;QACA;QACA;QACA;QACA,IAAI,EAAE+I,aAAa,IAAIE,WAAW,IAAI,CAACjC,MAAM,CAAC7C,WAAW,CAAC,EAAE;UAC1D6E,WAAW,CAACE,IAAI,CAAC;YAAC/B,YAAY,EAAEH,MAAM,CAACG,YAAY;YAAEnH,KAAK,EAAEgH,MAAM,CAAChH;UAAK,CAAC,CAAC;QAC5E;QAEA,IAAIiJ,WAAW,IAAI,CAACjC,MAAM,CAAC7C,WAAW,EAAE;UACtC6C,MAAM,CAAChH,KAAK,CAACxB,OAAO,CAAC,CAACwB,KAAK,EAAE/D,CAAC,KAAK;YACjC+M,WAAW,CAACE,IAAI,CAAC;cACf/B,YAAY,EAAE,CAACH,MAAM,CAACG,YAAY,IAAI,EAAE,EAAExL,MAAM,CAACM,CAAC,CAAC;cACnD+D;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,OAAOgJ,WAAW;IACpB;IAEA;IACA,SAASrG,iBAAiBA,CAAClB,OAAO,EAAEjC,QAAQ,EAAE;MAC5C;MACA;MACA;MACA;MACA,IAAI2J,MAAM,CAACC,SAAS,CAAC3H,OAAO,CAAC,IAAIA,OAAO,IAAI,CAAC,EAAE;QAC7C,OAAO,IAAI4H,UAAU,CAAC,IAAIC,UAAU,CAAC,CAAC7H,OAAO,CAAC,CAAC,CAAC8H,MAAM,CAAC;MACzD;;MAEA;MACA;MACA,IAAI1M,KAAK,CAAC2M,QAAQ,CAAC/H,OAAO,CAAC,EAAE;QAC3B,OAAO,IAAI4H,UAAU,CAAC5H,OAAO,CAAC8H,MAAM,CAAC;MACvC;;MAEA;MACA;MACA;MACA,IAAI7H,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IACtBA,OAAO,CAAC9B,KAAK,CAACf,CAAC,IAAIuK,MAAM,CAACC,SAAS,CAACxK,CAAC,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,EAAE;QACrD,MAAM2K,MAAM,GAAG,IAAIE,WAAW,CAAC,CAACf,IAAI,CAACgB,GAAG,CAAC,GAAGjI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,MAAMkI,IAAI,GAAG,IAAIN,UAAU,CAACE,MAAM,CAAC;QAEnC9H,OAAO,CAACjD,OAAO,CAACI,CAAC,IAAI;UACnB+K,IAAI,CAAC/K,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,CAAC;QAChC,CAAC,CAAC;QAEF,OAAO+K,IAAI;MACb;;MAEA;MACA,MAAM/H,KAAK,CACT,cAAAjG,MAAA,CAAc6D,QAAQ,uDACtB,0EAA0E,GAC1E,uCACF,CAAC;IACH;IAEA,SAASqD,eAAeA,CAAC7C,KAAK,EAAE7D,MAAM,EAAE;MACtC;MACA;;MAEA;MACA,IAAIgN,MAAM,CAACS,aAAa,CAAC5J,KAAK,CAAC,EAAE;QAC/B;QACA;QACA;QACA;QACA,MAAMuJ,MAAM,GAAG,IAAIE,WAAW,CAC5Bf,IAAI,CAACgB,GAAG,CAACvN,MAAM,EAAE,CAAC,GAAG0N,WAAW,CAACC,iBAAiB,CACpD,CAAC;QAED,IAAIH,IAAI,GAAG,IAAIE,WAAW,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QACxCI,IAAI,CAAC,CAAC,CAAC,GAAG3J,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;QAC7C2J,IAAI,CAAC,CAAC,CAAC,GAAG3J,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;;QAE7C;QACA,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb2J,IAAI,GAAG,IAAIN,UAAU,CAACE,MAAM,EAAE,CAAC,CAAC;UAChCI,IAAI,CAACnL,OAAO,CAAC,CAACsE,IAAI,EAAE7G,CAAC,KAAK;YACxB0N,IAAI,CAAC1N,CAAC,CAAC,GAAG,IAAI;UAChB,CAAC,CAAC;QACJ;QAEA,OAAO,IAAIoN,UAAU,CAACE,MAAM,CAAC;MAC/B;;MAEA;MACA,IAAI1M,KAAK,CAAC2M,QAAQ,CAACxJ,KAAK,CAAC,EAAE;QACzB,OAAO,IAAIqJ,UAAU,CAACrJ,KAAK,CAACuJ,MAAM,CAAC;MACrC;;MAEA;MACA,OAAO,KAAK;IACd;;IAEA;IACA;IACA;IACA,SAASQ,kBAAkBA,CAACC,QAAQ,EAAEjK,GAAG,EAAEC,KAAK,EAAE;MAChD5E,MAAM,CAACQ,IAAI,CAACoO,QAAQ,CAAC,CAACxL,OAAO,CAACyL,WAAW,IAAI;QAC3C,IACGA,WAAW,CAAC9N,MAAM,GAAG4D,GAAG,CAAC5D,MAAM,IAAI8N,WAAW,CAACC,OAAO,IAAAvO,MAAA,CAAIoE,GAAG,MAAG,CAAC,KAAK,CAAC,IACvEA,GAAG,CAAC5D,MAAM,GAAG8N,WAAW,CAAC9N,MAAM,IAAI4D,GAAG,CAACmK,OAAO,IAAAvO,MAAA,CAAIsO,WAAW,MAAG,CAAC,KAAK,CAAE,EACzE;UACA,MAAM,IAAIrI,KAAK,CACb,iDAAAjG,MAAA,CAAiDsO,WAAW,kBAAAtO,MAAA,CACxDoE,GAAG,kBACT,CAAC;QACH,CAAC,MAAM,IAAIkK,WAAW,KAAKlK,GAAG,EAAE;UAC9B,MAAM,IAAI6B,KAAK,4CAAAjG,MAAA,CAC8BoE,GAAG,uBAChD,CAAC;QACH;MACF,CAAC,CAAC;MAEFiK,QAAQ,CAACjK,GAAG,CAAC,GAAGC,KAAK;IACvB;;IAEA;IACA;IACA;IACA,SAASsF,qBAAqBA,CAAC6E,eAAe,EAAE;MAC9C,OAAOC,YAAY,IAAI;QACrB;QACA;QACA;QACA,OAAO;UAAC/M,MAAM,EAAE,CAAC8M,eAAe,CAACC,YAAY,CAAC,CAAC/M;QAAM,CAAC;MACxD,CAAC;IACH;IAEO,SAASqD,WAAWA,CAAChB,GAAG,EAAE;MAC/B,OAAOgC,KAAK,CAACC,OAAO,CAACjC,GAAG,CAAC,IAAI3C,eAAe,CAACyG,cAAc,CAAC9D,GAAG,CAAC;IAClE;IAEO,SAASzF,YAAYA,CAACoQ,CAAC,EAAE;MAC9B,OAAO,UAAU,CAAChH,IAAI,CAACgH,CAAC,CAAC;IAC3B;IAKO,SAASnQ,gBAAgBA,CAAC4D,aAAa,EAAEwM,cAAc,EAAE;MAC9D,IAAI,CAACvN,eAAe,CAACyG,cAAc,CAAC1F,aAAa,CAAC,EAAE;QAClD,OAAO,KAAK;MACd;MAEA,IAAIyM,iBAAiB,GAAG3M,SAAS;MACjCxC,MAAM,CAACQ,IAAI,CAACkC,aAAa,CAAC,CAACU,OAAO,CAACgM,MAAM,IAAI;QAC3C,MAAMC,cAAc,GAAGD,MAAM,CAAC3C,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,IAAI2C,MAAM,KAAK,MAAM;QAEvE,IAAID,iBAAiB,KAAK3M,SAAS,EAAE;UACnC2M,iBAAiB,GAAGE,cAAc;QACpC,CAAC,MAAM,IAAIF,iBAAiB,KAAKE,cAAc,EAAE;UAC/C,IAAI,CAACH,cAAc,EAAE;YACnB,MAAM,IAAI1I,KAAK,2BAAAjG,MAAA,CACa+O,IAAI,CAACC,SAAS,CAAC7M,aAAa,CAAC,CACzD,CAAC;UACH;UAEAyM,iBAAiB,GAAG,KAAK;QAC3B;MACF,CAAC,CAAC;MAEF,OAAO,CAAC,CAACA,iBAAiB,CAAC,CAAC;IAC9B;IAEA;IACA,SAASrJ,cAAcA,CAAC0J,kBAAkB,EAAE;MAC1C,OAAO;QACLpJ,sBAAsBA,CAACC,OAAO,EAAE;UAC9B;UACA;UACA;UACA;UACA,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;YAC1B,OAAO,MAAM,KAAK;UACpB;;UAEA;UACA;UACA,IAAIA,OAAO,KAAK7D,SAAS,EAAE;YACzB6D,OAAO,GAAG,IAAI;UAChB;UAEA,MAAMoJ,WAAW,GAAG9N,eAAe,CAACwF,EAAE,CAACC,KAAK,CAACf,OAAO,CAAC;UAErD,OAAOzB,KAAK,IAAI;YACd,IAAIA,KAAK,KAAKpC,SAAS,EAAE;cACvBoC,KAAK,GAAG,IAAI;YACd;;YAEA;YACA;YACA,IAAIjD,eAAe,CAACwF,EAAE,CAACC,KAAK,CAACxC,KAAK,CAAC,KAAK6K,WAAW,EAAE;cACnD,OAAO,KAAK;YACd;YAEA,OAAOD,kBAAkB,CAAC7N,eAAe,CAACwF,EAAE,CAACuI,IAAI,CAAC9K,KAAK,EAAEyB,OAAO,CAAC,CAAC;UACpE,CAAC;QACH;MACF,CAAC;IACH;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASd,kBAAkBA,CAACZ,GAAG,EAAgB;MAAA,IAAd4H,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MAClD,MAAMiL,KAAK,GAAGhL,GAAG,CAACnF,KAAK,CAAC,GAAG,CAAC;MAC5B,MAAMoQ,SAAS,GAAGD,KAAK,CAAC5O,MAAM,GAAG4O,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;MAC9C,MAAME,UAAU,GACdF,KAAK,CAAC5O,MAAM,GAAG,CAAC,IAChBwE,kBAAkB,CAACoK,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAACnQ,IAAI,CAAC,GAAG,CAAC,EAAE4M,OAAO,CACrD;MAED,SAASwD,WAAWA,CAAChE,YAAY,EAAEhD,WAAW,EAAEnE,KAAK,EAAE;QACrD,OAAOmH,YAAY,IAAIA,YAAY,CAAChL,MAAM,GACtCgI,WAAW,GACT,CAAC;UAAEgD,YAAY;UAAEhD,WAAW;UAAEnE;QAAM,CAAC,CAAC,GACtC,CAAC;UAAEmH,YAAY;UAAEnH;QAAM,CAAC,CAAC,GAC3BmE,WAAW,GACT,CAAC;UAAEA,WAAW;UAAEnE;QAAM,CAAC,CAAC,GACxB,CAAC;UAAEA;QAAM,CAAC,CAAC;MACnB;;MAEA;MACA;MACA,OAAO,CAAC0E,GAAG,EAAEyC,YAAY,KAAK;QAC5B,IAAIzF,KAAK,CAACC,OAAO,CAAC+C,GAAG,CAAC,EAAE;UACtB;UACA;UACA;UACA,IAAI,EAAEzK,YAAY,CAAC+Q,SAAS,CAAC,IAAIA,SAAS,GAAGtG,GAAG,CAACvI,MAAM,CAAC,EAAE;YACxD,OAAO,EAAE;UACX;;UAEA;UACA;UACA;UACAgL,YAAY,GAAGA,YAAY,GAAGA,YAAY,CAACxL,MAAM,CAAC,CAACqP,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,CAACA,SAAS,EAAE,GAAG,CAAC;QACxF;;QAEA;QACA,MAAMI,UAAU,GAAG1G,GAAG,CAACsG,SAAS,CAAC;;QAEjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAACC,UAAU,EAAE;UACf,OAAOE,WAAW,CAChBhE,YAAY,EACZzF,KAAK,CAACC,OAAO,CAAC+C,GAAG,CAAC,IAAIhD,KAAK,CAACC,OAAO,CAACyJ,UAAU,CAAC,EAC/CA,UACF,CAAC;QACH;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC1K,WAAW,CAAC0K,UAAU,CAAC,EAAE;UAC5B,IAAI1J,KAAK,CAACC,OAAO,CAAC+C,GAAG,CAAC,EAAE;YACtB,OAAO,EAAE;UACX;UAEA,OAAOyG,WAAW,CAAChE,YAAY,EAAE,KAAK,EAAEvJ,SAAS,CAAC;QACpD;QAEA,MAAMP,MAAM,GAAG,EAAE;QACjB,MAAMgO,cAAc,GAAGC,IAAI,IAAI;UAC7BjO,MAAM,CAAC6L,IAAI,CAAC,GAAGoC,IAAI,CAAC;QACtB,CAAC;;QAED;QACA;QACA;QACAD,cAAc,CAACJ,UAAU,CAACG,UAAU,EAAEjE,YAAY,CAAC,CAAC;;QAEpD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIzF,KAAK,CAACC,OAAO,CAACyJ,UAAU,CAAC,IACzB,EAAEnR,YAAY,CAAC8Q,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIpD,OAAO,CAAC4D,OAAO,CAAC,EAAE;UAChDH,UAAU,CAAC5M,OAAO,CAAC,CAACwI,MAAM,EAAEwE,UAAU,KAAK;YACzC,IAAIzO,eAAe,CAACyG,cAAc,CAACwD,MAAM,CAAC,EAAE;cAC1CqE,cAAc,CAACJ,UAAU,CAACjE,MAAM,EAAEG,YAAY,GAAGA,YAAY,CAACxL,MAAM,CAAC6P,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;YACnG;UACF,CAAC,CAAC;QACJ;QAEA,OAAOnO,MAAM;MACf,CAAC;IACH;IAEA;IACA;IACAoO,aAAa,GAAG;MAAC9K;IAAkB,CAAC;IACpC+K,cAAc,GAAG,SAAAA,CAACC,OAAO,EAAmB;MAAA,IAAjBhE,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MACrC,IAAI,OAAO6L,OAAO,KAAK,QAAQ,IAAIhE,OAAO,CAACiE,KAAK,EAAE;QAChDD,OAAO,mBAAAhQ,MAAA,CAAmBgM,OAAO,CAACiE,KAAK,MAAG;MAC5C;MAEA,MAAM3O,KAAK,GAAG,IAAI2E,KAAK,CAAC+J,OAAO,CAAC;MAChC1O,KAAK,CAACC,IAAI,GAAG,gBAAgB;MAC7B,OAAOD,KAAK;IACd,CAAC;IAEM,SAAS2D,cAAcA,CAACkI,mBAAmB,EAAE;MAClD,OAAO;QAACzL,MAAM,EAAE;MAAK,CAAC;IACxB;IAEA;IACA;IACA,SAAS6K,uBAAuBA,CAACpK,aAAa,EAAEG,OAAO,EAAEiI,MAAM,EAAE;MAC/D;MACA;MACA;MACA,MAAM2F,gBAAgB,GAAGzQ,MAAM,CAACQ,IAAI,CAACkC,aAAa,CAAC,CAACpD,GAAG,CAACoR,QAAQ,IAAI;QAClE,MAAMrK,OAAO,GAAG3D,aAAa,CAACgO,QAAQ,CAAC;QAEvC,MAAMC,WAAW,GACf,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAACtO,QAAQ,CAACqO,QAAQ,CAAC,IACjD,OAAOrK,OAAO,KAAK,QACpB;QAED,MAAMuK,cAAc,GAClB,CAAC,KAAK,EAAE,KAAK,CAAC,CAACvO,QAAQ,CAACqO,QAAQ,CAAC,IACjCrK,OAAO,KAAKrG,MAAM,CAACqG,OAAO,CAC3B;QAED,MAAMwK,eAAe,GACnB,CAAC,KAAK,EAAE,MAAM,CAAC,CAACxO,QAAQ,CAACqO,QAAQ,CAAC,IAC/BpK,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IACtB,CAACA,OAAO,CAAC5F,IAAI,CAAC+C,CAAC,IAAIA,CAAC,KAAKxD,MAAM,CAACwD,CAAC,CAAC,CACtC;QAED,IAAI,EAAEmN,WAAW,IAAIE,eAAe,IAAID,cAAc,CAAC,EAAE;UACvD/N,OAAO,CAAC6J,SAAS,GAAG,KAAK;QAC3B;QAEA,IAAI9N,MAAM,CAAC0E,IAAI,CAACyG,eAAe,EAAE2G,QAAQ,CAAC,EAAE;UAC1C,OAAO3G,eAAe,CAAC2G,QAAQ,CAAC,CAACrK,OAAO,EAAE3D,aAAa,EAAEG,OAAO,EAAEiI,MAAM,CAAC;QAC3E;QAEA,IAAIlM,MAAM,CAAC0E,IAAI,CAAC4B,iBAAiB,EAAEwL,QAAQ,CAAC,EAAE;UAC5C,MAAMnE,OAAO,GAAGrH,iBAAiB,CAACwL,QAAQ,CAAC;UAC3C,OAAO1G,sCAAsC,CAC3CuC,OAAO,CAACnG,sBAAsB,CAACC,OAAO,EAAE3D,aAAa,EAAEG,OAAO,CAAC,EAC/D0J,OACF,CAAC;QACH;QAEA,MAAM,IAAI/F,KAAK,2BAAAjG,MAAA,CAA2BmQ,QAAQ,CAAE,CAAC;MACvD,CAAC,CAAC;MAEF,OAAO7F,mBAAmB,CAAC4F,gBAAgB,CAAC;IAC9C;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAAS1R,WAAWA,CAACM,KAAK,EAAEyR,SAAS,EAAEC,UAAU,EAAa;MAAA,IAAXC,IAAI,GAAAtM,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MACjErF,KAAK,CAAC+D,OAAO,CAAC7D,IAAI,IAAI;QACpB,MAAM0R,SAAS,GAAG1R,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;QACjC,IAAIoE,IAAI,GAAGoN,IAAI;;QAEf;QACA,MAAME,OAAO,GAAGD,SAAS,CAACnB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACvL,KAAK,CAAC,CAACI,GAAG,EAAE9D,CAAC,KAAK;UACvD,IAAI,CAACjC,MAAM,CAAC0E,IAAI,CAACM,IAAI,EAAEe,GAAG,CAAC,EAAE;YAC3Bf,IAAI,CAACe,GAAG,CAAC,GAAG,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIf,IAAI,CAACe,GAAG,CAAC,KAAK3E,MAAM,CAAC4D,IAAI,CAACe,GAAG,CAAC,CAAC,EAAE;YAC1Cf,IAAI,CAACe,GAAG,CAAC,GAAGoM,UAAU,CACpBnN,IAAI,CAACe,GAAG,CAAC,EACTsM,SAAS,CAACnB,KAAK,CAAC,CAAC,EAAEjP,CAAC,GAAG,CAAC,CAAC,CAAClB,IAAI,CAAC,GAAG,CAAC,EACnCJ,IACF,CAAC;;YAED;YACA,IAAIqE,IAAI,CAACe,GAAG,CAAC,KAAK3E,MAAM,CAAC4D,IAAI,CAACe,GAAG,CAAC,CAAC,EAAE;cACnC,OAAO,KAAK;YACd;UACF;UAEAf,IAAI,GAAGA,IAAI,CAACe,GAAG,CAAC;UAEhB,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,IAAIuM,OAAO,EAAE;UACX,MAAMC,OAAO,GAAGF,SAAS,CAACA,SAAS,CAAClQ,MAAM,GAAG,CAAC,CAAC;UAC/C,IAAInC,MAAM,CAAC0E,IAAI,CAACM,IAAI,EAAEuN,OAAO,CAAC,EAAE;YAC9BvN,IAAI,CAACuN,OAAO,CAAC,GAAGJ,UAAU,CAACnN,IAAI,CAACuN,OAAO,CAAC,EAAE5R,IAAI,EAAEA,IAAI,CAAC;UACvD,CAAC,MAAM;YACLqE,IAAI,CAACuN,OAAO,CAAC,GAAGL,SAAS,CAACvR,IAAI,CAAC;UACjC;QACF;MACF,CAAC,CAAC;MAEF,OAAOyR,IAAI;IACb;IAEA;IACA;IACA;IACA,SAASxF,YAAYA,CAACP,KAAK,EAAE;MAC3B,OAAO3E,KAAK,CAACC,OAAO,CAAC0E,KAAK,CAAC,GAAGA,KAAK,CAAC6E,KAAK,CAAC,CAAC,GAAG,CAAC7E,KAAK,CAACzH,CAAC,EAAEyH,KAAK,CAACmG,CAAC,CAAC;IAClE;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,SAASC,4BAA4BA,CAACzC,QAAQ,EAAEjK,GAAG,EAAEC,KAAK,EAAE;MAC1D,IAAIA,KAAK,IAAI5E,MAAM,CAACsR,cAAc,CAAC1M,KAAK,CAAC,KAAK5E,MAAM,CAACH,SAAS,EAAE;QAC9D0R,0BAA0B,CAAC3C,QAAQ,EAAEjK,GAAG,EAAEC,KAAK,CAAC;MAClD,CAAC,MAAM,IAAI,EAAEA,KAAK,YAAYiC,MAAM,CAAC,EAAE;QACrC8H,kBAAkB,CAACC,QAAQ,EAAEjK,GAAG,EAAEC,KAAK,CAAC;MAC1C;IACF;;IAEA;IACA;IACA,SAAS2M,0BAA0BA,CAAC3C,QAAQ,EAAEjK,GAAG,EAAEC,KAAK,EAAE;MACxD,MAAMpE,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACoE,KAAK,CAAC;MAC/B,MAAM4M,cAAc,GAAGhR,IAAI,CAACf,MAAM,CAAC4D,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;MAEvD,IAAImO,cAAc,CAACzQ,MAAM,GAAG,CAAC,IAAI,CAACP,IAAI,CAACO,MAAM,EAAE;QAC7C;QACA;QACA,IAAIP,IAAI,CAACO,MAAM,KAAKyQ,cAAc,CAACzQ,MAAM,EAAE;UACzC,MAAM,IAAIyF,KAAK,sBAAAjG,MAAA,CAAsBiR,cAAc,CAAC,CAAC,CAAC,CAAE,CAAC;QAC3D;QAEAC,cAAc,CAAC7M,KAAK,EAAED,GAAG,CAAC;QAC1BgK,kBAAkB,CAACC,QAAQ,EAAEjK,GAAG,EAAEC,KAAK,CAAC;MAC1C,CAAC,MAAM;QACL5E,MAAM,CAACQ,IAAI,CAACoE,KAAK,CAAC,CAACxB,OAAO,CAACC,EAAE,IAAI;UAC/B,MAAMqO,MAAM,GAAG9M,KAAK,CAACvB,EAAE,CAAC;UAExB,IAAIA,EAAE,KAAK,KAAK,EAAE;YAChBgO,4BAA4B,CAACzC,QAAQ,EAAEjK,GAAG,EAAE+M,MAAM,CAAC;UACrD,CAAC,MAAM,IAAIrO,EAAE,KAAK,MAAM,EAAE;YACxB;YACAqO,MAAM,CAACtO,OAAO,CAAC8J,OAAO,IACpBmE,4BAA4B,CAACzC,QAAQ,EAAEjK,GAAG,EAAEuI,OAAO,CACrD,CAAC;UACH;QACF,CAAC,CAAC;MACJ;IACF;;IAEA;IACO,SAASzH,+BAA+BA,CAACkM,KAAK,EAAiB;MAAA,IAAf/C,QAAQ,GAAAlK,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MAClE,IAAI1E,MAAM,CAACsR,cAAc,CAACK,KAAK,CAAC,KAAK3R,MAAM,CAACH,SAAS,EAAE;QACrD;QACAG,MAAM,CAACQ,IAAI,CAACmR,KAAK,CAAC,CAACvO,OAAO,CAACuB,GAAG,IAAI;UAChC,MAAMC,KAAK,GAAG+M,KAAK,CAAChN,GAAG,CAAC;UAExB,IAAIA,GAAG,KAAK,MAAM,EAAE;YAClB;YACAC,KAAK,CAACxB,OAAO,CAAC8J,OAAO,IACnBzH,+BAA+B,CAACyH,OAAO,EAAE0B,QAAQ,CACnD,CAAC;UACH,CAAC,MAAM,IAAIjK,GAAG,KAAK,KAAK,EAAE;YACxB;YACA,IAAIC,KAAK,CAAC7D,MAAM,KAAK,CAAC,EAAE;cACtB0E,+BAA+B,CAACb,KAAK,CAAC,CAAC,CAAC,EAAEgK,QAAQ,CAAC;YACrD;UACF,CAAC,MAAM,IAAIjK,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACzB;YACA0M,4BAA4B,CAACzC,QAAQ,EAAEjK,GAAG,EAAEC,KAAK,CAAC;UACpD;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAIjD,eAAe,CAACiQ,aAAa,CAACD,KAAK,CAAC,EAAE;UACxChD,kBAAkB,CAACC,QAAQ,EAAE,KAAK,EAAE+C,KAAK,CAAC;QAC5C;MACF;MAEA,OAAO/C,QAAQ;IACjB;IAQO,SAAS5P,iBAAiBA,CAAC6S,MAAM,EAAE;MACxC;MACA;MACA;MACA,IAAIC,UAAU,GAAG9R,MAAM,CAACQ,IAAI,CAACqR,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC;;MAE3C;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,EAAED,UAAU,CAAC/Q,MAAM,KAAK,CAAC,IAAI+Q,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,IACrD,EAAEA,UAAU,CAACzP,QAAQ,CAAC,KAAK,CAAC,IAAIwP,MAAM,CAACG,GAAG,CAAC,EAAE;QAC/CF,UAAU,GAAGA,UAAU,CAACrS,MAAM,CAACkF,GAAG,IAAIA,GAAG,KAAK,KAAK,CAAC;MACtD;MAEA,IAAIV,SAAS,GAAG,IAAI,CAAC,CAAC;;MAEtB6N,UAAU,CAAC1O,OAAO,CAAC6O,OAAO,IAAI;QAC5B,MAAMC,IAAI,GAAG,CAAC,CAACL,MAAM,CAACI,OAAO,CAAC;QAE9B,IAAIhO,SAAS,KAAK,IAAI,EAAE;UACtBA,SAAS,GAAGiO,IAAI;QAClB;;QAEA;QACA,IAAIjO,SAAS,KAAKiO,IAAI,EAAE;UACtB,MAAM5B,cAAc,CAClB,0DACF,CAAC;QACH;MACF,CAAC,CAAC;MAEF,MAAM6B,mBAAmB,GAAGpT,WAAW,CACrC+S,UAAU,EACVvS,IAAI,IAAI0E,SAAS,EACjB,CAACJ,IAAI,EAAEtE,IAAI,EAAEuE,QAAQ,KAAK;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMsO,WAAW,GAAGtO,QAAQ;QAC5B,MAAMuO,WAAW,GAAG9S,IAAI;QACxB,MAAM+Q,cAAc,CAClB,QAAA/P,MAAA,CAAQ6R,WAAW,WAAA7R,MAAA,CAAQ8R,WAAW,iCACtC,sEAAsE,GACtE,uBACF,CAAC;MACH,CAAC,CAAC;MAEJ,OAAO;QAACpO,SAAS;QAAEL,IAAI,EAAEuO;MAAmB,CAAC;IAC/C;IAGO,SAASzM,oBAAoBA,CAACqC,MAAM,EAAE;MAC3C,OAAOnD,KAAK,IAAI;QACd,IAAIA,KAAK,YAAYiC,MAAM,EAAE;UAC3B,OAAOjC,KAAK,CAAC0N,QAAQ,CAAC,CAAC,KAAKvK,MAAM,CAACuK,QAAQ,CAAC,CAAC;QAC/C;;QAEA;QACA,IAAI,OAAO1N,KAAK,KAAK,QAAQ,EAAE;UAC7B,OAAO,KAAK;QACd;;QAEA;QACA;QACA;QACA;QACA;QACAmD,MAAM,CAACwK,SAAS,GAAG,CAAC;QAEpB,OAAOxK,MAAM,CAACE,IAAI,CAACrD,KAAK,CAAC;MAC3B,CAAC;IACH;IAEA;IACA;IACA;IACA,SAAS4N,iBAAiBA,CAAC7N,GAAG,EAAEpF,IAAI,EAAE;MACpC,IAAIoF,GAAG,CAACtC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrB,MAAM,IAAImE,KAAK,sBAAAjG,MAAA,CACQoE,GAAG,YAAApE,MAAA,CAAShB,IAAI,OAAAgB,MAAA,CAAIoE,GAAG,+BAC9C,CAAC;MACH;MAEA,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClB,MAAM,IAAI6B,KAAK,oCAAAjG,MAAA,CACsBhB,IAAI,OAAAgB,MAAA,CAAIoE,GAAG,+BAChD,CAAC;MACH;IACF;;IAEA;IACA,SAAS8M,cAAcA,CAACC,MAAM,EAAEnS,IAAI,EAAE;MACpC,IAAImS,MAAM,IAAI1R,MAAM,CAACsR,cAAc,CAACI,MAAM,CAAC,KAAK1R,MAAM,CAACH,SAAS,EAAE;QAChEG,MAAM,CAACQ,IAAI,CAACkR,MAAM,CAAC,CAACtO,OAAO,CAACuB,GAAG,IAAI;UACjC6N,iBAAiB,CAAC7N,GAAG,EAAEpF,IAAI,CAAC;UAC5BkS,cAAc,CAACC,MAAM,CAAC/M,GAAG,CAAC,EAAEpF,IAAI,GAAG,GAAG,GAAGoF,GAAG,CAAC;QAC/C,CAAC,CAAC;MACJ;IACF;IAACE,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AC/3CDtG,MAAM,CAACuG,MAAM,CAAC;EAACwN,kBAAkB,EAACA,CAAA,KAAIA,kBAAkB;EAACC,wBAAwB,EAACA,CAAA,KAAIA,wBAAwB;EAACC,oBAAoB,EAACA,CAAA,KAAIA,oBAAoB;EAACC,mBAAmB,EAACA,CAAA,KAAIA;AAAmB,CAAC,CAAC;AAGnM,SAASH,kBAAkBA,CAACI,MAAM,EAAE;EACzC,UAAAtS,MAAA,CAAUsS,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;AACnC;AAEO,MAAMJ,wBAAwB,GAAG,CACtC,yBAAyB,EACzB,gBAAgB,EAChB,WAAW;AACX;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,aAAa;AACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,SAAS;AACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ,CACT;AAEM,MAAMC,oBAAoB,GAAG;AAClC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,OAAO;AACP;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,OAAO;AACP;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,SAAS;AACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,KAAK,CACN;AAEM,MAAMC,mBAAmB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,C;;;;;;;;;;;;;;ICpJtFlU,MAAM,CAACuG,MAAM,CAAC;MAACU,OAAO,EAACA,CAAA,KAAIoN;IAAM,CAAC,CAAC;IAAC,IAAIpR,eAAe;IAACjD,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAAC0C,eAAe,GAAC1C,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIL,MAAM;IAACF,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACC,MAAMA,CAACK,CAAC,EAAC;QAACL,MAAM,GAACK,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI0T,oBAAoB,EAACF,kBAAkB;IAAC/T,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACgU,oBAAoBA,CAAC1T,CAAC,EAAC;QAAC0T,oBAAoB,GAAC1T,CAAC;MAAA,CAAC;MAACwT,kBAAkBA,CAACxT,CAAC,EAAC;QAACwT,kBAAkB,GAACxT,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAMjZ,MAAM6T,MAAM,CAAC;MAC1B;MACAC,WAAWA,CAACC,UAAU,EAAE7O,QAAQ,EAAgB;QAAA,IAAdmI,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;QAC5C,IAAI,CAACuO,UAAU,GAAGA,UAAU;QAC5B,IAAI,CAACC,MAAM,GAAG,IAAI;QAClB,IAAI,CAACrQ,OAAO,GAAG,IAAI1D,SAAS,CAACS,OAAO,CAACwE,QAAQ,CAAC;QAE9C,IAAIzC,eAAe,CAACwR,4BAA4B,CAAC/O,QAAQ,CAAC,EAAE;UAC1D;UACA,IAAI,CAACgP,WAAW,GAAGxU,MAAM,CAAC0E,IAAI,CAACc,QAAQ,EAAE,KAAK,CAAC,GAAGA,QAAQ,CAAC4N,GAAG,GAAG5N,QAAQ;QAC3E,CAAC,MAAM;UACL,IAAI,CAACgP,WAAW,GAAG5Q,SAAS;UAE5B,IAAI,IAAI,CAACK,OAAO,CAACwQ,WAAW,CAAC,CAAC,IAAI9G,OAAO,CAACwF,IAAI,EAAE;YAC9C,IAAI,CAACmB,MAAM,GAAG,IAAI/T,SAAS,CAACsE,MAAM,CAAC8I,OAAO,CAACwF,IAAI,IAAI,EAAE,CAAC;UACxD;QACF;QAEA,IAAI,CAACuB,IAAI,GAAG/G,OAAO,CAAC+G,IAAI,IAAI,CAAC;QAC7B,IAAI,CAACC,KAAK,GAAGhH,OAAO,CAACgH,KAAK;QAC1B,IAAI,CAAC1B,MAAM,GAAGtF,OAAO,CAACpK,UAAU,IAAIoK,OAAO,CAACsF,MAAM;QAElD,IAAI,CAAC2B,aAAa,GAAG7R,eAAe,CAAC8R,kBAAkB,CAAC,IAAI,CAAC5B,MAAM,IAAI,CAAC,CAAC,CAAC;QAE1E,IAAI,CAAC6B,UAAU,GAAG/R,eAAe,CAACgS,aAAa,CAACpH,OAAO,CAACqH,SAAS,CAAC;;QAElE;QACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;UAClC,IAAI,CAACC,QAAQ,GAAGvH,OAAO,CAACuH,QAAQ,KAAKtR,SAAS,GAAG,IAAI,GAAG+J,OAAO,CAACuH,QAAQ;QAC1E;MACF;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEC,KAAKA,CAAA,EAAG;QACN,IAAI,IAAI,CAACD,QAAQ,EAAE;UACjB;UACA,IAAI,CAACE,OAAO,CAAC;YAAEC,KAAK,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAK,CAAC,EAAE,IAAI,CAAC;QACpD;QAEA,OAAO,IAAI,CAACC,cAAc,CAAC;UACzBC,OAAO,EAAE;QACX,CAAC,CAAC,CAACrT,MAAM;MACX;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEsT,KAAKA,CAAA,EAAG;QACN,MAAMpS,MAAM,GAAG,EAAE;QAEjB,IAAI,CAACmB,OAAO,CAACkG,GAAG,IAAI;UAClBrH,MAAM,CAAC6L,IAAI,CAACxE,GAAG,CAAC;QAClB,CAAC,CAAC;QAEF,OAAOrH,MAAM;MACf;MAEA,CAACqS,MAAM,CAACC,QAAQ,IAAI;QAClB,IAAI,IAAI,CAACT,QAAQ,EAAE;UACjB,IAAI,CAACE,OAAO,CAAC;YACXQ,WAAW,EAAE,IAAI;YACjBN,OAAO,EAAE,IAAI;YACbO,OAAO,EAAE,IAAI;YACbC,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;QAEA,IAAIC,KAAK,GAAG,CAAC;QACb,MAAMC,OAAO,GAAG,IAAI,CAACT,cAAc,CAAC;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC;QAEtD,OAAO;UACLS,IAAI,EAAEA,CAAA,KAAM;YACV,IAAIF,KAAK,GAAGC,OAAO,CAAC7T,MAAM,EAAE;cAC1B;cACA,IAAImM,OAAO,GAAG,IAAI,CAACsG,aAAa,CAACoB,OAAO,CAACD,KAAK,EAAE,CAAC,CAAC;cAElD,IAAI,IAAI,CAACjB,UAAU,EAAExG,OAAO,GAAG,IAAI,CAACwG,UAAU,CAACxG,OAAO,CAAC;cAEvD,OAAO;gBAAEtI,KAAK,EAAEsI;cAAQ,CAAC;YAC3B;YAEA,OAAO;cAAE4H,IAAI,EAAE;YAAK,CAAC;UACvB;QACF,CAAC;MACH;MAEA,CAACR,MAAM,CAACS,aAAa,IAAI;QACvB,MAAMC,UAAU,GAAG,IAAI,CAACV,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;QAC1C,OAAO;UACL,MAAMM,IAAIA,CAAA,EAAG;YACX,OAAOI,OAAO,CAACC,OAAO,CAACF,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC;UAC3C;QACF,CAAC;MACH;;MAEA;AACF;AACA;AACA;AACA;MACE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEzR,OAAOA,CAAC+R,QAAQ,EAAEC,OAAO,EAAE;QACzB,IAAI,IAAI,CAACtB,QAAQ,EAAE;UACjB,IAAI,CAACE,OAAO,CAAC;YACXQ,WAAW,EAAE,IAAI;YACjBN,OAAO,EAAE,IAAI;YACbO,OAAO,EAAE,IAAI;YACbC,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;QAEA,IAAI,CAACP,cAAc,CAAC;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC,CAAChR,OAAO,CAAC,CAAC8J,OAAO,EAAErM,CAAC,KAAK;UAC7D;UACAqM,OAAO,GAAG,IAAI,CAACsG,aAAa,CAACtG,OAAO,CAAC;UAErC,IAAI,IAAI,CAACwG,UAAU,EAAE;YACnBxG,OAAO,GAAG,IAAI,CAACwG,UAAU,CAACxG,OAAO,CAAC;UACpC;UAEAiI,QAAQ,CAAC7R,IAAI,CAAC8R,OAAO,EAAElI,OAAO,EAAErM,CAAC,EAAE,IAAI,CAAC;QAC1C,CAAC,CAAC;MACJ;MAEAwU,YAAYA,CAAA,EAAG;QACb,OAAO,IAAI,CAAC3B,UAAU;MACxB;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEpU,GAAGA,CAAC6V,QAAQ,EAAEC,OAAO,EAAE;QACrB,MAAMnT,MAAM,GAAG,EAAE;QAEjB,IAAI,CAACmB,OAAO,CAAC,CAACkG,GAAG,EAAEzI,CAAC,KAAK;UACvBoB,MAAM,CAAC6L,IAAI,CAACqH,QAAQ,CAAC7R,IAAI,CAAC8R,OAAO,EAAE9L,GAAG,EAAEzI,CAAC,EAAE,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC;QAEF,OAAOoB,MAAM;MACf;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEqT,OAAOA,CAAC/I,OAAO,EAAE;QACf,OAAO5K,eAAe,CAAC4T,0BAA0B,CAAC,IAAI,EAAEhJ,OAAO,CAAC;MAClE;;MAEA;AACF;AACA;AACA;AACA;AACA;MACEiJ,YAAYA,CAACjJ,OAAO,EAAE;QACpB,OAAO,IAAI0I,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,IAAI,CAACI,OAAO,CAAC/I,OAAO,CAAC,CAAC,CAAC;MAC/D;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEkJ,cAAcA,CAAClJ,OAAO,EAAE;QACtB,MAAM6H,OAAO,GAAGzS,eAAe,CAAC+T,kCAAkC,CAACnJ,OAAO,CAAC;;QAE3E;QACA;QACA;QACA;QACA,IAAI,CAACA,OAAO,CAACoJ,gBAAgB,IAAI,CAACvB,OAAO,KAAK,IAAI,CAACd,IAAI,IAAI,IAAI,CAACC,KAAK,CAAC,EAAE;UACtE,MAAM,IAAI/M,KAAK,CACb,qEAAqE,GACnE,mEACJ,CAAC;QACH;QAEA,IAAI,IAAI,CAACqL,MAAM,KAAK,IAAI,CAACA,MAAM,CAACG,GAAG,KAAK,CAAC,IAAI,IAAI,CAACH,MAAM,CAACG,GAAG,KAAK,KAAK,CAAC,EAAE;UACvE,MAAMxL,KAAK,CAAC,sDAAsD,CAAC;QACrE;QAEA,MAAMoP,SAAS,GACb,IAAI,CAAC/S,OAAO,CAACwQ,WAAW,CAAC,CAAC,IAAIe,OAAO,IAAI,IAAIzS,eAAe,CAACkU,MAAM,CAAC,CAAC;QAEvE,MAAMlE,KAAK,GAAG;UACZmE,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE,KAAK;UACZH,SAAS;UACT/S,OAAO,EAAE,IAAI,CAACA,OAAO;UAAE;UACvBuR,OAAO;UACP4B,YAAY,EAAE,IAAI,CAACxC,aAAa;UAChCyC,eAAe,EAAE,IAAI;UACrB/C,MAAM,EAAEkB,OAAO,IAAI,IAAI,CAAClB;QAC1B,CAAC;QAED,IAAIgD,GAAG;;QAEP;QACA;QACA,IAAI,IAAI,CAACpC,QAAQ,EAAE;UACjBoC,GAAG,GAAG,IAAI,CAACjD,UAAU,CAACkD,QAAQ,EAAE;UAChC,IAAI,CAAClD,UAAU,CAACmD,OAAO,CAACF,GAAG,CAAC,GAAGvE,KAAK;QACtC;QAEAA,KAAK,CAAC0E,OAAO,GAAG,IAAI,CAAClC,cAAc,CAAC;UAClCC,OAAO;UACPwB,SAAS,EAAEjE,KAAK,CAACiE;QACnB,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC3C,UAAU,CAACqD,MAAM,EAAE;UAC1B3E,KAAK,CAACsE,eAAe,GAAG7B,OAAO,GAAG,EAAE,GAAG,IAAIzS,eAAe,CAACkU,MAAM,CAAC,CAAC;QACrE;;QAEA;QACA;QACA;QACA;;QAEA;QACA;QACA,MAAMU,YAAY,GAAIhN,EAAE,IAAK;UAC3B,IAAI,CAACA,EAAE,EAAE;YACP,OAAO,MAAM,CAAC,CAAC;UACjB;UAEA,MAAMxE,IAAI,GAAG,IAAI;UAEjB,OAAO,SAAU;UAAA,GAAW;YAC1B,IAAIA,IAAI,CAACkO,UAAU,CAACqD,MAAM,EAAE;cAC1B;YACF;YAEA,MAAME,IAAI,GAAG9R,SAAS;YAEtBK,IAAI,CAACkO,UAAU,CAACwD,aAAa,CAACC,SAAS,CAAC,MAAM;cAC5CnN,EAAE,CAACoN,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;YACtB,CAAC,CAAC;UACJ,CAAC;QACH,CAAC;QAED7E,KAAK,CAACsC,KAAK,GAAGsC,YAAY,CAAChK,OAAO,CAAC0H,KAAK,CAAC;QACzCtC,KAAK,CAAC8C,OAAO,GAAG8B,YAAY,CAAChK,OAAO,CAACkI,OAAO,CAAC;QAC7C9C,KAAK,CAACuC,OAAO,GAAGqC,YAAY,CAAChK,OAAO,CAAC2H,OAAO,CAAC;QAE7C,IAAIE,OAAO,EAAE;UACXzC,KAAK,CAAC6C,WAAW,GAAG+B,YAAY,CAAChK,OAAO,CAACiI,WAAW,CAAC;UACrD7C,KAAK,CAAC+C,WAAW,GAAG6B,YAAY,CAAChK,OAAO,CAACmI,WAAW,CAAC;QACvD;QAEA,IAAI,CAACnI,OAAO,CAACqK,iBAAiB,IAAI,CAAC,IAAI,CAAC3D,UAAU,CAACqD,MAAM,EAAE;UAAA,IAAAO,cAAA,EAAAC,mBAAA;UACzD,MAAMC,OAAO,GAAIzN,GAAG,IAAK;YACvB,MAAMuI,MAAM,GAAGpQ,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;YAE/B,OAAOuI,MAAM,CAACG,GAAG;YAEjB,IAAIoC,OAAO,EAAE;cACXzC,KAAK,CAAC6C,WAAW,CAAClL,GAAG,CAAC0I,GAAG,EAAE,IAAI,CAACwB,aAAa,CAAC3B,MAAM,CAAC,EAAE,IAAI,CAAC;YAC9D;YAEAF,KAAK,CAACsC,KAAK,CAAC3K,GAAG,CAAC0I,GAAG,EAAE,IAAI,CAACwB,aAAa,CAAC3B,MAAM,CAAC,CAAC;UAClD,CAAC;UACD;UACA,IAAIF,KAAK,CAAC0E,OAAO,CAACtV,MAAM,EAAE;YACxB,KAAK,MAAMuI,GAAG,IAAIqI,KAAK,CAAC0E,OAAO,EAAE;cAC/BU,OAAO,CAACzN,GAAG,CAAC;YACd;UACF;UACA;UACA,KAAAuN,cAAA,GAAIlF,KAAK,CAAC0E,OAAO,cAAAQ,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAeG,IAAI,cAAAF,mBAAA,eAAnBA,mBAAA,CAAAxT,IAAA,CAAAuT,cAAsB,CAAC,EAAE;YAC3BlF,KAAK,CAAC0E,OAAO,CAACjT,OAAO,CAAC2T,OAAO,CAAC;UAChC;QACF;QAEA,MAAME,MAAM,GAAGjX,MAAM,CAACC,MAAM,CAAC,IAAI0B,eAAe,CAACuV,aAAa,CAAC,CAAC,EAAE;UAChEjE,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BkE,IAAI,EAAEA,CAAA,KAAM;YACV,IAAI,IAAI,CAACrD,QAAQ,EAAE;cACjB,OAAO,IAAI,CAACb,UAAU,CAACmD,OAAO,CAACF,GAAG,CAAC;YACrC;UACF,CAAC;UACDkB,OAAO,EAAE,KAAK;UACdC,cAAc,EAAE;QAClB,CAAC,CAAC;QAEF,IAAI,IAAI,CAACvD,QAAQ,IAAID,OAAO,CAACyD,MAAM,EAAE;UACnC;UACA;UACA;UACA;UACA;UACAzD,OAAO,CAAC0D,YAAY,CAAC,MAAM;YACzBN,MAAM,CAACE,IAAI,CAAC,CAAC;UACf,CAAC,CAAC;QACJ;;QAEA;QACA;QACA,MAAMK,WAAW,GAAG,IAAI,CAACvE,UAAU,CAACwD,aAAa,CAACgB,KAAK,CAAC,CAAC;QAEzD,IAAID,WAAW,YAAYvC,OAAO,EAAE;UAClCgC,MAAM,CAACI,cAAc,GAAGG,WAAW;UACnCA,WAAW,CAACE,IAAI,CAAC,MAAOT,MAAM,CAACG,OAAO,GAAG,IAAK,CAAC;QACjD,CAAC,MAAM;UACLH,MAAM,CAACG,OAAO,GAAG,IAAI;UACrBH,MAAM,CAACI,cAAc,GAAGpC,OAAO,CAACC,OAAO,CAAC,CAAC;QAC3C;QAEA,OAAO+B,MAAM;MACf;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEU,mBAAmBA,CAACpL,OAAO,EAAE;QAC3B,OAAO,IAAI0I,OAAO,CAAEC,OAAO,IAAK;UAC9B,MAAM+B,MAAM,GAAG,IAAI,CAACxB,cAAc,CAAClJ,OAAO,CAAC;UAC3C0K,MAAM,CAACI,cAAc,CAACK,IAAI,CAAC,MAAMxC,OAAO,CAAC+B,MAAM,CAAC,CAAC;QACnD,CAAC,CAAC;MACJ;;MAEA;MACA;MACAjD,OAAOA,CAAC4D,QAAQ,EAAEjC,gBAAgB,EAAE;QAClC,IAAI9B,OAAO,CAACyD,MAAM,EAAE;UAClB,MAAMO,UAAU,GAAG,IAAIhE,OAAO,CAACiE,UAAU,CAAC,CAAC;UAC3C,MAAMC,MAAM,GAAGF,UAAU,CAACpD,OAAO,CAACuD,IAAI,CAACH,UAAU,CAAC;UAElDA,UAAU,CAACI,MAAM,CAAC,CAAC;UAEnB,MAAM1L,OAAO,GAAG;YAAEoJ,gBAAgB;YAAEiB,iBAAiB,EAAE;UAAK,CAAC;UAE7D,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,CAAC,CAACxT,OAAO,CACnEmG,EAAE,IAAI;YACJ,IAAIqO,QAAQ,CAACrO,EAAE,CAAC,EAAE;cAChBgD,OAAO,CAAChD,EAAE,CAAC,GAAGwO,MAAM;YACtB;UACF,CACF,CAAC;;UAED;UACA,IAAI,CAACtC,cAAc,CAAClJ,OAAO,CAAC;QAC9B;MACF;MAEA2L,kBAAkBA,CAAA,EAAG;QACnB,OAAO,IAAI,CAACjF,UAAU,CAACnR,IAAI;MAC7B;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAqS,cAAcA,CAAA,EAAe;QAAA,IAAd5H,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;QACzB;QACA;QACA;QACA;QACA,MAAMyT,cAAc,GAAG5L,OAAO,CAAC4L,cAAc,KAAK,KAAK;;QAEvD;QACA;QACA,MAAM9B,OAAO,GAAG9J,OAAO,CAAC6H,OAAO,GAAG,EAAE,GAAG,IAAIzS,eAAe,CAACkU,MAAM,CAAC,CAAC;;QAEnE;QACA,IAAI,IAAI,CAACzC,WAAW,KAAK5Q,SAAS,EAAE;UAClC;UACA;UACA,IAAI2V,cAAc,IAAI,IAAI,CAAC7E,IAAI,EAAE;YAC/B,OAAO+C,OAAO;UAChB;UAEA,MAAM+B,WAAW,GAAG,IAAI,CAACnF,UAAU,CAACoF,KAAK,CAACC,GAAG,CAAC,IAAI,CAAClF,WAAW,CAAC;UAC/D,IAAIgF,WAAW,EAAE;YACf,IAAI7L,OAAO,CAAC6H,OAAO,EAAE;cACnBiC,OAAO,CAACvI,IAAI,CAACsK,WAAW,CAAC;YAC3B,CAAC,MAAM;cACL/B,OAAO,CAACkC,GAAG,CAAC,IAAI,CAACnF,WAAW,EAAEgF,WAAW,CAAC;YAC5C;UACF;UACA,OAAO/B,OAAO;QAChB;;QAEA;;QAEA;QACA;QACA;QACA,IAAIT,SAAS;QACb,IAAI,IAAI,CAAC/S,OAAO,CAACwQ,WAAW,CAAC,CAAC,IAAI9G,OAAO,CAAC6H,OAAO,EAAE;UACjD,IAAI7H,OAAO,CAACqJ,SAAS,EAAE;YACrBA,SAAS,GAAGrJ,OAAO,CAACqJ,SAAS;YAC7BA,SAAS,CAAC4C,KAAK,CAAC,CAAC;UACnB,CAAC,MAAM;YACL5C,SAAS,GAAG,IAAIjU,eAAe,CAACkU,MAAM,CAAC,CAAC;UAC1C;QACF;QAEA4C,MAAM,CAACC,SAAS,CAAC,MAAM;UACrB,IAAI,CAACzF,UAAU,CAACoF,KAAK,CAACjV,OAAO,CAAC,CAACkG,GAAG,EAAEqP,EAAE,KAAK;YACzC,MAAMC,WAAW,GAAG,IAAI,CAAC/V,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC;YACrD,IAAIsP,WAAW,CAAC3W,MAAM,EAAE;cACtB,IAAIsK,OAAO,CAAC6H,OAAO,EAAE;gBACnBiC,OAAO,CAACvI,IAAI,CAACxE,GAAG,CAAC;gBAEjB,IAAIsM,SAAS,IAAIgD,WAAW,CAAC1N,QAAQ,KAAK1I,SAAS,EAAE;kBACnDoT,SAAS,CAAC2C,GAAG,CAACI,EAAE,EAAEC,WAAW,CAAC1N,QAAQ,CAAC;gBACzC;cACF,CAAC,MAAM;gBACLmL,OAAO,CAACkC,GAAG,CAACI,EAAE,EAAErP,GAAG,CAAC;cACtB;YACF;;YAEA;YACA,IAAI,CAAC6O,cAAc,EAAE;cACnB,OAAO,IAAI;YACb;;YAEA;YACA;YACA,OACE,CAAC,IAAI,CAAC5E,KAAK,IAAI,IAAI,CAACD,IAAI,IAAI,IAAI,CAACJ,MAAM,IAAImD,OAAO,CAACtV,MAAM,KAAK,IAAI,CAACwS,KAAK;UAE5E,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,IAAI,CAAChH,OAAO,CAAC6H,OAAO,EAAE;UACpB,OAAOiC,OAAO;QAChB;QAEA,IAAI,IAAI,CAACnD,MAAM,EAAE;UACfmD,OAAO,CAACtE,IAAI,CAAC,IAAI,CAACmB,MAAM,CAAC2F,aAAa,CAAC;YAAEjD;UAAU,CAAC,CAAC,CAAC;QACxD;;QAEA;QACA;QACA,IAAI,CAACuC,cAAc,IAAK,CAAC,IAAI,CAAC5E,KAAK,IAAI,CAAC,IAAI,CAACD,IAAK,EAAE;UAClD,OAAO+C,OAAO;QAChB;QAEA,OAAOA,OAAO,CAACvG,KAAK,CAClB,IAAI,CAACwD,IAAI,EACT,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACD,IAAI,GAAG+C,OAAO,CAACtV,MAChD,CAAC;MACH;MAEA+X,cAAcA,CAACC,YAAY,EAAE;QAC3B;QACA,IAAI,CAACC,OAAO,CAACC,KAAK,EAAE;UAClB,MAAM,IAAIzS,KAAK,CACb,2DACF,CAAC;QACH;QAEA,IAAI,CAAC,IAAI,CAACyM,UAAU,CAACnR,IAAI,EAAE;UACzB,MAAM,IAAI0E,KAAK,CACb,0DACF,CAAC;QACH;QAEA,OAAOwS,OAAO,CAACC,KAAK,CAACC,KAAK,CAACC,UAAU,CAACL,cAAc,CAClD,IAAI,EACJC,YAAY,EACZ,IAAI,CAAC9F,UAAU,CAACnR,IAClB,CAAC;MACH;IACF;IAEA;IACA6Q,oBAAoB,CAACvP,OAAO,CAACyP,MAAM,IAAI;MACrC,MAAMuG,SAAS,GAAG3G,kBAAkB,CAACI,MAAM,CAAC;MAC5CE,MAAM,CAAClT,SAAS,CAACuZ,SAAS,CAAC,GAAG,YAAkB;QAC9C,IAAI;UAAA,SAAAC,IAAA,GAAA3U,SAAA,CAAA3D,MAAA,EADoCyV,IAAI,OAAAlQ,KAAA,CAAA+S,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;YAAJ9C,IAAI,CAAA8C,IAAA,IAAA5U,SAAA,CAAA4U,IAAA;UAAA;UAE1C,OAAOrE,OAAO,CAACC,OAAO,CAAC,IAAI,CAACrC,MAAM,CAAC,CAAC8D,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,OAAO3U,KAAK,EAAE;UACd,OAAOoT,OAAO,CAACsE,MAAM,CAAC1X,KAAK,CAAC;QAC9B;MACF,CAAC;IACH,CAAC,CAAC;IAACgD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC5jBH,IAAIwU,aAAa;IAAC9a,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAACua,aAAa,GAACva,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGP,MAAM,CAACuG,MAAM,CAAC;MAACU,OAAO,EAACA,CAAA,KAAIhE;IAAe,CAAC,CAAC;IAAC,IAAIoR,MAAM;IAACrU,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAAC8T,MAAM,GAAC9T,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIiY,aAAa;IAACxY,MAAM,CAACC,IAAI,CAAC,qBAAqB,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAACiY,aAAa,GAACjY,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIL,MAAM,EAAC0G,WAAW,EAACzG,YAAY,EAACC,gBAAgB,EAAC2G,+BAA+B,EAACzG,iBAAiB;IAACN,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACC,MAAMA,CAACK,CAAC,EAAC;QAACL,MAAM,GAACK,CAAC;MAAA,CAAC;MAACqG,WAAWA,CAACrG,CAAC,EAAC;QAACqG,WAAW,GAACrG,CAAC;MAAA,CAAC;MAACJ,YAAYA,CAACI,CAAC,EAAC;QAACJ,YAAY,GAACI,CAAC;MAAA,CAAC;MAACH,gBAAgBA,CAACG,CAAC,EAAC;QAACH,gBAAgB,GAACG,CAAC;MAAA,CAAC;MAACwG,+BAA+BA,CAACxG,CAAC,EAAC;QAACwG,+BAA+B,GAACxG,CAAC;MAAA,CAAC;MAACD,iBAAiBA,CAACC,CAAC,EAAC;QAACD,iBAAiB,GAACC,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIwT,kBAAkB;IAAC/T,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAAC8T,kBAAkBA,CAACxT,CAAC,EAAC;QAACwT,kBAAkB,GAACxT,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAgBhsB,MAAMyC,eAAe,CAAC;MACnCqR,WAAWA,CAAClR,IAAI,EAAE;QAChB,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB;QACA,IAAI,CAACuW,KAAK,GAAG,IAAI1W,eAAe,CAACkU,MAAM,CAAD,CAAC;QAEvC,IAAI,CAACY,aAAa,GAAGgC,MAAM,CAACgB,QAAQ,GAChC,IAAIhB,MAAM,CAACiB,iBAAiB,CAAC,CAAC,GAC9B,IAAIjB,MAAM,CAACkB,kBAAkB,CAAC,CAAC;QAEnC,IAAI,CAACxD,QAAQ,GAAG,CAAC,CAAC,CAAC;;QAEnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAACC,OAAO,GAAGpW,MAAM,CAAC4Z,MAAM,CAAC,IAAI,CAAC;;QAElC;QACA;QACA,IAAI,CAACC,eAAe,GAAG,IAAI;;QAE3B;QACA,IAAI,CAACvD,MAAM,GAAG,KAAK;MACrB;MAEAwD,cAAcA,CAAC1V,QAAQ,EAAEmI,OAAO,EAAE;QAChC,OAAO,IAAI,CAACxJ,IAAI,CAACqB,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,CAAC,CAAC,EAAEmI,OAAO,CAAC,CAACwN,UAAU,CAAC,CAAC;MACxD;MAEAC,sBAAsBA,CAACzN,OAAO,EAAE;QAC9B,OAAO,IAAI,CAACxJ,IAAI,CAAC,CAAC,CAAC,EAAEwJ,OAAO,CAAC,CAACwN,UAAU,CAAC,CAAC;MAC5C;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAhX,IAAIA,CAACqB,QAAQ,EAAEmI,OAAO,EAAE;QACtB;QACA;QACA;QACA,IAAI7H,SAAS,CAAC3D,MAAM,KAAK,CAAC,EAAE;UAC1BqD,QAAQ,GAAG,CAAC,CAAC;QACf;QAEA,OAAO,IAAIzC,eAAe,CAACoR,MAAM,CAAC,IAAI,EAAE3O,QAAQ,EAAEmI,OAAO,CAAC;MAC5D;MAEA0N,OAAOA,CAAC7V,QAAQ,EAAgB;QAAA,IAAdmI,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;QAC5B,IAAIA,SAAS,CAAC3D,MAAM,KAAK,CAAC,EAAE;UAC1BqD,QAAQ,GAAG,CAAC,CAAC;QACf;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAmI,OAAO,CAACgH,KAAK,GAAG,CAAC;QAEjB,OAAO,IAAI,CAACxQ,IAAI,CAACqB,QAAQ,EAAEmI,OAAO,CAAC,CAAC8H,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD;MACA,MAAM6F,YAAYA,CAAC9V,QAAQ,EAAgB;QAAA,IAAdmI,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;QACvC,IAAIA,SAAS,CAAC3D,MAAM,KAAK,CAAC,EAAE;UAC1BqD,QAAQ,GAAG,CAAC,CAAC;QACf;QACAmI,OAAO,CAACgH,KAAK,GAAG,CAAC;QACjB,OAAO,CAAC,MAAM,IAAI,CAACxQ,IAAI,CAACqB,QAAQ,EAAEmI,OAAO,CAAC,CAAC4N,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7D;MACAC,aAAaA,CAAC9Q,GAAG,EAAE;QACjB+Q,wBAAwB,CAAC/Q,GAAG,CAAC;;QAE7B;QACA;QACA,IAAI,CAAC1K,MAAM,CAAC0E,IAAI,CAACgG,GAAG,EAAE,KAAK,CAAC,EAAE;UAC5BA,GAAG,CAAC0I,GAAG,GAAGrQ,eAAe,CAAC2Y,OAAO,GAAG,IAAIC,OAAO,CAACC,QAAQ,CAAC,CAAC,GAAGC,MAAM,CAAC9B,EAAE,CAAC,CAAC;QAC1E;QAEA,MAAMA,EAAE,GAAGrP,GAAG,CAAC0I,GAAG;QAElB,IAAI,IAAI,CAACqG,KAAK,CAACqC,GAAG,CAAC/B,EAAE,CAAC,EAAE;UACtB,MAAMrI,cAAc,mBAAA/P,MAAA,CAAmBoY,EAAE,MAAG,CAAC;QAC/C;QAEA,IAAI,CAACgC,aAAa,CAAChC,EAAE,EAAEnW,SAAS,CAAC;QACjC,IAAI,CAAC6V,KAAK,CAACE,GAAG,CAACI,EAAE,EAAErP,GAAG,CAAC;QAEvB,OAAOqP,EAAE;MACX;;MAEA;MACA;MACAiC,MAAMA,CAACtR,GAAG,EAAE6L,QAAQ,EAAE;QACpB7L,GAAG,GAAG7H,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;QACtB,MAAMqP,EAAE,GAAG,IAAI,CAACyB,aAAa,CAAC9Q,GAAG,CAAC;QAClC,MAAMuR,kBAAkB,GAAG,EAAE;;QAE7B;QACA,KAAK,MAAM3E,GAAG,IAAIlW,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,EAAE;UAC3C,MAAMzE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,CAACoE,KAAK,EAAE;YACf;UACF;UAEA,MAAM6C,WAAW,GAAGjH,KAAK,CAAC9O,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC;UAEtD,IAAIsP,WAAW,CAAC3W,MAAM,EAAE;YACtB,IAAI0P,KAAK,CAACiE,SAAS,IAAIgD,WAAW,CAAC1N,QAAQ,KAAK1I,SAAS,EAAE;cACzDmP,KAAK,CAACiE,SAAS,CAAC2C,GAAG,CAACI,EAAE,EAAEC,WAAW,CAAC1N,QAAQ,CAAC;YAC/C;YAEA,IAAIyG,KAAK,CAACmE,MAAM,CAACxC,IAAI,IAAI3B,KAAK,CAACmE,MAAM,CAACvC,KAAK,EAAE;cAC3CsH,kBAAkB,CAAC/M,IAAI,CAACoI,GAAG,CAAC;YAC9B,CAAC,MAAM;cACLvU,eAAe,CAACmZ,oBAAoB,CAACnJ,KAAK,EAAErI,GAAG,CAAC;YAClD;UACF;QACF;QAEAuR,kBAAkB,CAACzX,OAAO,CAAC8S,GAAG,IAAI;UAChC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC6E,iBAAiB,CAAC,IAAI,CAAC3E,OAAO,CAACF,GAAG,CAAC,CAAC;UAC3C;QACF,CAAC,CAAC;QAEF,IAAI,CAACO,aAAa,CAACgB,KAAK,CAAC,CAAC;QAC1B,IAAItC,QAAQ,EAAE;UACZsD,MAAM,CAACuC,KAAK,CAAC,MAAM;YACjB7F,QAAQ,CAAC,IAAI,EAAEwD,EAAE,CAAC;UACpB,CAAC,CAAC;QACJ;QAEA,OAAOA,EAAE;MACX;MACA,MAAMsC,WAAWA,CAAC3R,GAAG,EAAE6L,QAAQ,EAAE;QAC/B7L,GAAG,GAAG7H,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;QACtB,MAAMqP,EAAE,GAAG,IAAI,CAACyB,aAAa,CAAC9Q,GAAG,CAAC;QAClC,MAAMuR,kBAAkB,GAAG,EAAE;;QAE7B;QACA,KAAK,MAAM3E,GAAG,IAAIlW,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,EAAE;UAC3C,MAAMzE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,CAACoE,KAAK,EAAE;YACf;UACF;UAEA,MAAM6C,WAAW,GAAGjH,KAAK,CAAC9O,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC;UAEtD,IAAIsP,WAAW,CAAC3W,MAAM,EAAE;YACtB,IAAI0P,KAAK,CAACiE,SAAS,IAAIgD,WAAW,CAAC1N,QAAQ,KAAK1I,SAAS,EAAE;cACzDmP,KAAK,CAACiE,SAAS,CAAC2C,GAAG,CAACI,EAAE,EAAEC,WAAW,CAAC1N,QAAQ,CAAC;YAC/C;YAEA,IAAIyG,KAAK,CAACmE,MAAM,CAACxC,IAAI,IAAI3B,KAAK,CAACmE,MAAM,CAACvC,KAAK,EAAE;cAC3CsH,kBAAkB,CAAC/M,IAAI,CAACoI,GAAG,CAAC;YAC9B,CAAC,MAAM;cACL,MAAMvU,eAAe,CAACuZ,qBAAqB,CAACvJ,KAAK,EAAErI,GAAG,CAAC;YACzD;UACF;QACF;QAEAuR,kBAAkB,CAACzX,OAAO,CAAC8S,GAAG,IAAI;UAChC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC6E,iBAAiB,CAAC,IAAI,CAAC3E,OAAO,CAACF,GAAG,CAAC,CAAC;UAC3C;QACF,CAAC,CAAC;QAEF,MAAM,IAAI,CAACO,aAAa,CAACgB,KAAK,CAAC,CAAC;QAChC,IAAItC,QAAQ,EAAE;UACZsD,MAAM,CAACuC,KAAK,CAAC,MAAM;YACjB7F,QAAQ,CAAC,IAAI,EAAEwD,EAAE,CAAC;UACpB,CAAC,CAAC;QACJ;QAEA,OAAOA,EAAE;MACX;;MAEA;MACA;MACAwC,cAAcA,CAAA,EAAG;QACf;QACA,IAAI,IAAI,CAAC7E,MAAM,EAAE;UACf;QACF;;QAEA;QACA,IAAI,CAACA,MAAM,GAAG,IAAI;;QAElB;QACAtW,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,CAAChT,OAAO,CAAC8S,GAAG,IAAI;UACvC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAC/BvE,KAAK,CAACsE,eAAe,GAAGxU,KAAK,CAACC,KAAK,CAACiQ,KAAK,CAAC0E,OAAO,CAAC;QACpD,CAAC,CAAC;MACJ;MAEA+E,kBAAkBA,CAACjG,QAAQ,EAAE;QAC3B,MAAMlT,MAAM,GAAG,IAAI,CAACoW,KAAK,CAACrB,IAAI,CAAC,CAAC;QAEhC,IAAI,CAACqB,KAAK,CAACG,KAAK,CAAC,CAAC;QAElBxY,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,CAAChT,OAAO,CAAC8S,GAAG,IAAI;UACvC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,CAACyC,OAAO,EAAE;YACjBzC,KAAK,CAAC0E,OAAO,GAAG,EAAE;UACpB,CAAC,MAAM;YACL1E,KAAK,CAAC0E,OAAO,CAACmC,KAAK,CAAC,CAAC;UACvB;QACF,CAAC,CAAC;QAEF,IAAIrD,QAAQ,EAAE;UACZsD,MAAM,CAACuC,KAAK,CAAC,MAAM;YACjB7F,QAAQ,CAAC,IAAI,EAAElT,MAAM,CAAC;UACxB,CAAC,CAAC;QACJ;QAEA,OAAOA,MAAM;MACf;MAGAoZ,aAAaA,CAACjX,QAAQ,EAAE;QACtB,MAAMvB,OAAO,GAAG,IAAI1D,SAAS,CAACS,OAAO,CAACwE,QAAQ,CAAC;QAC/C,MAAMkX,MAAM,GAAG,EAAE;QAEjB,IAAI,CAACC,4BAA4B,CAACnX,QAAQ,EAAE,CAACkF,GAAG,EAAEqP,EAAE,KAAK;UACvD,IAAI9V,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC,CAACrH,MAAM,EAAE;YACvCqZ,MAAM,CAACxN,IAAI,CAAC6K,EAAE,CAAC;UACjB;QACF,CAAC,CAAC;QAEF,MAAMkC,kBAAkB,GAAG,EAAE;QAC7B,MAAMW,WAAW,GAAG,EAAE;QAEtB,KAAK,IAAI3a,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGya,MAAM,CAACva,MAAM,EAAEF,CAAC,EAAE,EAAE;UACtC,MAAM4a,QAAQ,GAAGH,MAAM,CAACza,CAAC,CAAC;UAC1B,MAAM6a,SAAS,GAAG,IAAI,CAACrD,KAAK,CAACC,GAAG,CAACmD,QAAQ,CAAC;UAE1Czb,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,CAAChT,OAAO,CAAC8S,GAAG,IAAI;YACvC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;YAE/B,IAAIvE,KAAK,CAACoE,KAAK,EAAE;cACf;YACF;YAEA,IAAIpE,KAAK,CAAC9O,OAAO,CAACb,eAAe,CAAC0Z,SAAS,CAAC,CAACzZ,MAAM,EAAE;cACnD,IAAI0P,KAAK,CAACmE,MAAM,CAACxC,IAAI,IAAI3B,KAAK,CAACmE,MAAM,CAACvC,KAAK,EAAE;gBAC3CsH,kBAAkB,CAAC/M,IAAI,CAACoI,GAAG,CAAC;cAC9B,CAAC,MAAM;gBACLsF,WAAW,CAAC1N,IAAI,CAAC;kBAACoI,GAAG;kBAAE5M,GAAG,EAAEoS;gBAAS,CAAC,CAAC;cACzC;YACF;UACF,CAAC,CAAC;UAEF,IAAI,CAACf,aAAa,CAACc,QAAQ,EAAEC,SAAS,CAAC;UACvC,IAAI,CAACrD,KAAK,CAACiD,MAAM,CAACG,QAAQ,CAAC;QAC7B;QAEA,OAAO;UAAEZ,kBAAkB;UAAEW,WAAW;UAAEF;QAAO,CAAC;MACpD;MAEAA,MAAMA,CAAClX,QAAQ,EAAE+Q,QAAQ,EAAE;QACzB;QACA;QACA;QACA,IAAI,IAAI,CAACmB,MAAM,IAAI,CAAC,IAAI,CAACuD,eAAe,IAAIpY,KAAK,CAACka,MAAM,CAACvX,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;UACtE,OAAO,IAAI,CAACgX,kBAAkB,CAACjG,QAAQ,CAAC;QAC1C;QAEA,MAAM;UAAE0F,kBAAkB;UAAEW,WAAW;UAAEF;QAAO,CAAC,GAAG,IAAI,CAACD,aAAa,CAACjX,QAAQ,CAAC;;QAEhF;QACAoX,WAAW,CAACpY,OAAO,CAACkY,MAAM,IAAI;UAC5B,MAAM3J,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACkF,MAAM,CAACpF,GAAG,CAAC;UAEtC,IAAIvE,KAAK,EAAE;YACTA,KAAK,CAACiE,SAAS,IAAIjE,KAAK,CAACiE,SAAS,CAAC0F,MAAM,CAACA,MAAM,CAAChS,GAAG,CAAC0I,GAAG,CAAC;YACzDrQ,eAAe,CAACia,sBAAsB,CAACjK,KAAK,EAAE2J,MAAM,CAAChS,GAAG,CAAC;UAC3D;QACF,CAAC,CAAC;QAEFuR,kBAAkB,CAACzX,OAAO,CAAC8S,GAAG,IAAI;UAChC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,EAAE;YACT,IAAI,CAACoJ,iBAAiB,CAACpJ,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QAEF,IAAI,CAAC8E,aAAa,CAACgB,KAAK,CAAC,CAAC;QAE1B,MAAMxV,MAAM,GAAGqZ,MAAM,CAACva,MAAM;QAE5B,IAAIoU,QAAQ,EAAE;UACZsD,MAAM,CAACuC,KAAK,CAAC,MAAM;YACjB7F,QAAQ,CAAC,IAAI,EAAElT,MAAM,CAAC;UACxB,CAAC,CAAC;QACJ;QAEA,OAAOA,MAAM;MACf;MAEA,MAAM4Z,WAAWA,CAACzX,QAAQ,EAAE+Q,QAAQ,EAAE;QACpC;QACA;QACA;QACA,IAAI,IAAI,CAACmB,MAAM,IAAI,CAAC,IAAI,CAACuD,eAAe,IAAIpY,KAAK,CAACka,MAAM,CAACvX,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;UACtE,OAAO,IAAI,CAACgX,kBAAkB,CAACjG,QAAQ,CAAC;QAC1C;QAEA,MAAM;UAAE0F,kBAAkB;UAAEW,WAAW;UAAEF;QAAO,CAAC,GAAG,IAAI,CAACD,aAAa,CAACjX,QAAQ,CAAC;;QAEhF;QACA,KAAK,MAAMkX,MAAM,IAAIE,WAAW,EAAE;UAChC,MAAM7J,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACkF,MAAM,CAACpF,GAAG,CAAC;UAEtC,IAAIvE,KAAK,EAAE;YACTA,KAAK,CAACiE,SAAS,IAAIjE,KAAK,CAACiE,SAAS,CAAC0F,MAAM,CAACA,MAAM,CAAChS,GAAG,CAAC0I,GAAG,CAAC;YACzD,MAAMrQ,eAAe,CAACma,uBAAuB,CAACnK,KAAK,EAAE2J,MAAM,CAAChS,GAAG,CAAC;UAClE;QACF;QACAuR,kBAAkB,CAACzX,OAAO,CAAC8S,GAAG,IAAI;UAChC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,EAAE;YACT,IAAI,CAACoJ,iBAAiB,CAACpJ,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QAEF,MAAM,IAAI,CAAC8E,aAAa,CAACgB,KAAK,CAAC,CAAC;QAEhC,MAAMxV,MAAM,GAAGqZ,MAAM,CAACva,MAAM;QAE5B,IAAIoU,QAAQ,EAAE;UACZsD,MAAM,CAACuC,KAAK,CAAC,MAAM;YACjB7F,QAAQ,CAAC,IAAI,EAAElT,MAAM,CAAC;UACxB,CAAC,CAAC;QACJ;QAEA,OAAOA,MAAM;MACf;;MAEA;MACA;MACA;MACA;MACA8Z,gBAAgBA,CAAA,EAAG;QACjB;QACA,IAAI,CAAC,IAAI,CAACzF,MAAM,EAAE;UAChB;QACF;;QAEA;QACA;QACA,IAAI,CAACA,MAAM,GAAG,KAAK;QAEnBtW,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,CAAChT,OAAO,CAAC8S,GAAG,IAAI;UACvC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,CAACoE,KAAK,EAAE;YACfpE,KAAK,CAACoE,KAAK,GAAG,KAAK;;YAEnB;YACA;YACA,IAAI,CAACgF,iBAAiB,CAACpJ,KAAK,EAAEA,KAAK,CAACsE,eAAe,CAAC;UACtD,CAAC,MAAM;YACL;YACA;YACAtU,eAAe,CAACqa,iBAAiB,CAC/BrK,KAAK,CAACyC,OAAO,EACbzC,KAAK,CAACsE,eAAe,EACrBtE,KAAK,CAAC0E,OAAO,EACb1E,KAAK,EACL;cAACqE,YAAY,EAAErE,KAAK,CAACqE;YAAY,CACnC,CAAC;UACH;UAEArE,KAAK,CAACsE,eAAe,GAAG,IAAI;QAC9B,CAAC,CAAC;MACJ;MAEA,MAAMgG,qBAAqBA,CAAA,EAAG;QAC5B,IAAI,CAACF,gBAAgB,CAAC,CAAC;QACvB,MAAM,IAAI,CAACtF,aAAa,CAACgB,KAAK,CAAC,CAAC;MAClC;MACAyE,qBAAqBA,CAAA,EAAG;QACtB,IAAI,CAACH,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACtF,aAAa,CAACgB,KAAK,CAAC,CAAC;MAC5B;MAEA0E,iBAAiBA,CAAA,EAAG;QAClB,IAAI,CAAC,IAAI,CAACtC,eAAe,EAAE;UACzB,MAAM,IAAIrT,KAAK,CAAC,gDAAgD,CAAC;QACnE;QAEA,MAAM4V,SAAS,GAAG,IAAI,CAACvC,eAAe;QAEtC,IAAI,CAACA,eAAe,GAAG,IAAI;QAE3B,OAAOuC,SAAS;MAClB;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,aAAaA,CAAA,EAAG;QACd,IAAI,IAAI,CAACxC,eAAe,EAAE;UACxB,MAAM,IAAIrT,KAAK,CAAC,sDAAsD,CAAC;QACzE;QAEA,IAAI,CAACqT,eAAe,GAAG,IAAIlY,eAAe,CAACkU,MAAM,CAAD,CAAC;MACnD;MAEAyG,aAAaA,CAAClY,QAAQ,EAAE;QACtB;QACA;QACA;QACA;QACA;QACA,MAAMmY,oBAAoB,GAAG,CAAC,CAAC;;QAE/B;QACA;QACA,MAAMC,MAAM,GAAG,IAAI7a,eAAe,CAACkU,MAAM,CAAD,CAAC;QACzC,MAAM4G,UAAU,GAAG9a,eAAe,CAAC+a,qBAAqB,CAACtY,QAAQ,CAAC;QAElEpE,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,CAAChT,OAAO,CAAC8S,GAAG,IAAI;UACvC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAI,CAACvE,KAAK,CAACmE,MAAM,CAACxC,IAAI,IAAI3B,KAAK,CAACmE,MAAM,CAACvC,KAAK,KAAK,CAAE,IAAI,CAAC+C,MAAM,EAAE;YAC9D;YACA;YACA;YACA;YACA;YACA,IAAI3E,KAAK,CAAC0E,OAAO,YAAY1U,eAAe,CAACkU,MAAM,EAAE;cACnD0G,oBAAoB,CAACrG,GAAG,CAAC,GAAGvE,KAAK,CAAC0E,OAAO,CAAC3U,KAAK,CAAC,CAAC;cACjD;YACF;YAEA,IAAI,EAAEiQ,KAAK,CAAC0E,OAAO,YAAY/P,KAAK,CAAC,EAAE;cACrC,MAAM,IAAIE,KAAK,CAAC,8CAA8C,CAAC;YACjE;;YAEA;YACA;YACA;YACA;YACA,MAAMmW,qBAAqB,GAAGrT,GAAG,IAAI;cACnC,IAAIkT,MAAM,CAAC9B,GAAG,CAACpR,GAAG,CAAC0I,GAAG,CAAC,EAAE;gBACvB,OAAOwK,MAAM,CAAClE,GAAG,CAAChP,GAAG,CAAC0I,GAAG,CAAC;cAC5B;cAEA,MAAM4K,YAAY,GAChBH,UAAU,IACV,CAACA,UAAU,CAAChc,IAAI,CAACkY,EAAE,IAAIlX,KAAK,CAACka,MAAM,CAAChD,EAAE,EAAErP,GAAG,CAAC0I,GAAG,CAAC,CAAC,GAC/C1I,GAAG,GAAG7H,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;cAE1BkT,MAAM,CAACjE,GAAG,CAACjP,GAAG,CAAC0I,GAAG,EAAE4K,YAAY,CAAC;cAEjC,OAAOA,YAAY;YACrB,CAAC;YAEDL,oBAAoB,CAACrG,GAAG,CAAC,GAAGvE,KAAK,CAAC0E,OAAO,CAAC/W,GAAG,CAACqd,qBAAqB,CAAC;UACtE;QACF,CAAC,CAAC;QAEF,OAAOJ,oBAAoB;MAC7B;MAEAM,YAAYA,CAAAC,IAAA,EAAiD;QAAA,IAAhD;UAAEvQ,OAAO;UAAEwQ,WAAW;UAAE5H,QAAQ;UAAE6H;QAAW,CAAC,GAAAF,IAAA;QAGzD;QACA;QACA;QACA,IAAI7a,MAAM;QACV,IAAIsK,OAAO,CAAC0Q,aAAa,EAAE;UACzBhb,MAAM,GAAG;YAAEib,cAAc,EAAEH;UAAY,CAAC;UAExC,IAAIC,UAAU,KAAKxa,SAAS,EAAE;YAC5BP,MAAM,CAAC+a,UAAU,GAAGA,UAAU;UAChC;QACF,CAAC,MAAM;UACL/a,MAAM,GAAG8a,WAAW;QACtB;QAEA,IAAI5H,QAAQ,EAAE;UACZsD,MAAM,CAACuC,KAAK,CAAC,MAAM;YACjB7F,QAAQ,CAAC,IAAI,EAAElT,MAAM,CAAC;UACxB,CAAC,CAAC;QACJ;QAEA,OAAOA,MAAM;MACf;;MAEA;MACA;MACA,MAAMkb,WAAWA,CAAC/Y,QAAQ,EAAE1D,GAAG,EAAE6L,OAAO,EAAE4I,QAAQ,EAAE;QAClD,IAAI,CAAEA,QAAQ,IAAI5I,OAAO,YAAY1C,QAAQ,EAAE;UAC7CsL,QAAQ,GAAG5I,OAAO;UAClBA,OAAO,GAAG,IAAI;QAChB;QAEA,IAAI,CAACA,OAAO,EAAE;UACZA,OAAO,GAAG,CAAC,CAAC;QACd;QAEA,MAAM1J,OAAO,GAAG,IAAI1D,SAAS,CAACS,OAAO,CAACwE,QAAQ,EAAE,IAAI,CAAC;QAErD,MAAMmY,oBAAoB,GAAG,IAAI,CAACD,aAAa,CAAClY,QAAQ,CAAC;QAEzD,IAAIgZ,aAAa,GAAG,CAAC,CAAC;QAEtB,IAAIL,WAAW,GAAG,CAAC;QAEnB,MAAM,IAAI,CAACM,6BAA6B,CAACjZ,QAAQ,EAAE,OAAOkF,GAAG,EAAEqP,EAAE,KAAK;UACpE,MAAM2E,WAAW,GAAGza,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC;UAEhD,IAAIgU,WAAW,CAACrb,MAAM,EAAE;YACtB;YACA,IAAI,CAAC0Y,aAAa,CAAChC,EAAE,EAAErP,GAAG,CAAC;YAC3B8T,aAAa,GAAG,MAAM,IAAI,CAACG,qBAAqB,CAC9CjU,GAAG,EACH5I,GAAG,EACH4c,WAAW,CAACvR,YACd,CAAC;YAED,EAAEgR,WAAW;YAEb,IAAI,CAACxQ,OAAO,CAACiR,KAAK,EAAE;cAClB,OAAO,KAAK,CAAC,CAAC;YAChB;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEFxd,MAAM,CAACQ,IAAI,CAAC4c,aAAa,CAAC,CAACha,OAAO,CAAC8S,GAAG,IAAI;UACxC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,EAAE;YACT,IAAI,CAACoJ,iBAAiB,CAACpJ,KAAK,EAAE4K,oBAAoB,CAACrG,GAAG,CAAC,CAAC;UAC1D;QACF,CAAC,CAAC;QAEF,MAAM,IAAI,CAACO,aAAa,CAACgB,KAAK,CAAC,CAAC;;QAEhC;QACA;QACA;QACA,IAAIuF,UAAU;QACd,IAAID,WAAW,KAAK,CAAC,IAAIxQ,OAAO,CAACkR,MAAM,EAAE;UACvC,MAAMnU,GAAG,GAAG3H,eAAe,CAAC+b,qBAAqB,CAACtZ,QAAQ,EAAE1D,GAAG,CAAC;UAChE,IAAI,CAAC4I,GAAG,CAAC0I,GAAG,IAAIzF,OAAO,CAACyQ,UAAU,EAAE;YAClC1T,GAAG,CAAC0I,GAAG,GAAGzF,OAAO,CAACyQ,UAAU;UAC9B;UAEAA,UAAU,GAAG,MAAM,IAAI,CAAC/B,WAAW,CAAC3R,GAAG,CAAC;UACxCyT,WAAW,GAAG,CAAC;QACjB;QAEA,OAAO,IAAI,CAACF,YAAY,CAAC;UACvBtQ,OAAO;UACPyQ,UAAU;UACVD,WAAW;UACX5H;QACF,CAAC,CAAC;MACJ;MACA;MACA;MACAwI,MAAMA,CAACvZ,QAAQ,EAAE1D,GAAG,EAAE6L,OAAO,EAAE4I,QAAQ,EAAE;QACvC,IAAI,CAAEA,QAAQ,IAAI5I,OAAO,YAAY1C,QAAQ,EAAE;UAC7CsL,QAAQ,GAAG5I,OAAO;UAClBA,OAAO,GAAG,IAAI;QAChB;QAEA,IAAI,CAACA,OAAO,EAAE;UACZA,OAAO,GAAG,CAAC,CAAC;QACd;QAEA,MAAM1J,OAAO,GAAG,IAAI1D,SAAS,CAACS,OAAO,CAACwE,QAAQ,EAAE,IAAI,CAAC;QAErD,MAAMmY,oBAAoB,GAAG,IAAI,CAACD,aAAa,CAAClY,QAAQ,CAAC;QAEzD,IAAIgZ,aAAa,GAAG,CAAC,CAAC;QAEtB,IAAIL,WAAW,GAAG,CAAC;QAEnB,IAAI,CAACxB,4BAA4B,CAACnX,QAAQ,EAAE,CAACkF,GAAG,EAAEqP,EAAE,KAAK;UACvD,MAAM2E,WAAW,GAAGza,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC;UAEhD,IAAIgU,WAAW,CAACrb,MAAM,EAAE;YACtB;YACA,IAAI,CAAC0Y,aAAa,CAAChC,EAAE,EAAErP,GAAG,CAAC;YAC3B8T,aAAa,GAAG,IAAI,CAACQ,oBAAoB,CACvCtU,GAAG,EACH5I,GAAG,EACH4c,WAAW,CAACvR,YACd,CAAC;YAED,EAAEgR,WAAW;YAEb,IAAI,CAACxQ,OAAO,CAACiR,KAAK,EAAE;cAClB,OAAO,KAAK,CAAC,CAAC;YAChB;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEFxd,MAAM,CAACQ,IAAI,CAAC4c,aAAa,CAAC,CAACha,OAAO,CAAC8S,GAAG,IAAI;UACxC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAC/B,IAAIvE,KAAK,EAAE;YACT,IAAI,CAACoJ,iBAAiB,CAACpJ,KAAK,EAAE4K,oBAAoB,CAACrG,GAAG,CAAC,CAAC;UAC1D;QACF,CAAC,CAAC;QAEF,IAAI,CAACO,aAAa,CAACgB,KAAK,CAAC,CAAC;;QAG1B;QACA;QACA;QACA,IAAIuF,UAAU;QACd,IAAID,WAAW,KAAK,CAAC,IAAIxQ,OAAO,CAACkR,MAAM,EAAE;UACvC,MAAMnU,GAAG,GAAG3H,eAAe,CAAC+b,qBAAqB,CAACtZ,QAAQ,EAAE1D,GAAG,CAAC;UAChE,IAAI,CAAC4I,GAAG,CAAC0I,GAAG,IAAIzF,OAAO,CAACyQ,UAAU,EAAE;YAClC1T,GAAG,CAAC0I,GAAG,GAAGzF,OAAO,CAACyQ,UAAU;UAC9B;UAEAA,UAAU,GAAG,IAAI,CAACpC,MAAM,CAACtR,GAAG,CAAC;UAC7ByT,WAAW,GAAG,CAAC;QACjB;QAGA,OAAO,IAAI,CAACF,YAAY,CAAC;UACvBtQ,OAAO;UACPwQ,WAAW;UACX5H,QAAQ;UACR/Q,QAAQ;UACR1D;QACF,CAAC,CAAC;MACJ;;MAEA;MACA;MACA;MACA+c,MAAMA,CAACrZ,QAAQ,EAAE1D,GAAG,EAAE6L,OAAO,EAAE4I,QAAQ,EAAE;QACvC,IAAI,CAACA,QAAQ,IAAI,OAAO5I,OAAO,KAAK,UAAU,EAAE;UAC9C4I,QAAQ,GAAG5I,OAAO;UAClBA,OAAO,GAAG,CAAC,CAAC;QACd;QAEA,OAAO,IAAI,CAACoR,MAAM,CAChBvZ,QAAQ,EACR1D,GAAG,EACHV,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsM,OAAO,EAAE;UAACkR,MAAM,EAAE,IAAI;UAAER,aAAa,EAAE;QAAI,CAAC,CAAC,EAC/D9H,QACF,CAAC;MACH;MAEA0I,WAAWA,CAACzZ,QAAQ,EAAE1D,GAAG,EAAE6L,OAAO,EAAE4I,QAAQ,EAAE;QAC5C,IAAI,CAACA,QAAQ,IAAI,OAAO5I,OAAO,KAAK,UAAU,EAAE;UAC9C4I,QAAQ,GAAG5I,OAAO;UAClBA,OAAO,GAAG,CAAC,CAAC;QACd;QAEA,OAAO,IAAI,CAAC4Q,WAAW,CACrB/Y,QAAQ,EACR1D,GAAG,EACHV,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsM,OAAO,EAAE;UAACkR,MAAM,EAAE,IAAI;UAAER,aAAa,EAAE;QAAI,CAAC,CAAC,EAC/D9H,QACF,CAAC;MACH;;MAEA;MACA;MACA;MACA;MACA,MAAMkI,6BAA6BA,CAACjZ,QAAQ,EAAEmF,EAAE,EAAE;QAChD,MAAMuU,WAAW,GAAGnc,eAAe,CAAC+a,qBAAqB,CAACtY,QAAQ,CAAC;QAEnE,IAAI0Z,WAAW,EAAE;UACf,KAAK,MAAMnF,EAAE,IAAImF,WAAW,EAAE;YAC5B,MAAMxU,GAAG,GAAG,IAAI,CAAC+O,KAAK,CAACC,GAAG,CAACK,EAAE,CAAC;YAE9B,IAAIrP,GAAG,IAAI,EAAG,MAAMC,EAAE,CAACD,GAAG,EAAEqP,EAAE,CAAC,CAAC,EAAE;cAChC;YACF;UACF;QACF,CAAC,MAAM;UACL,MAAM,IAAI,CAACN,KAAK,CAAC0F,YAAY,CAACxU,EAAE,CAAC;QACnC;MACF;MACAgS,4BAA4BA,CAACnX,QAAQ,EAAEmF,EAAE,EAAE;QACzC,MAAMuU,WAAW,GAAGnc,eAAe,CAAC+a,qBAAqB,CAACtY,QAAQ,CAAC;QAEnE,IAAI0Z,WAAW,EAAE;UACf,KAAK,MAAMnF,EAAE,IAAImF,WAAW,EAAE;YAC5B,MAAMxU,GAAG,GAAG,IAAI,CAAC+O,KAAK,CAACC,GAAG,CAACK,EAAE,CAAC;YAE9B,IAAIrP,GAAG,IAAI,CAACC,EAAE,CAACD,GAAG,EAAEqP,EAAE,CAAC,EAAE;cACvB;YACF;UACF;QACF,CAAC,MAAM;UACL,IAAI,CAACN,KAAK,CAACjV,OAAO,CAACmG,EAAE,CAAC;QACxB;MACF;MAEAyU,uBAAuBA,CAAC1U,GAAG,EAAE5I,GAAG,EAAEqL,YAAY,EAAE;QAC9C,MAAMkS,cAAc,GAAG,CAAC,CAAC;QAEzBje,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,CAAChT,OAAO,CAAC8S,GAAG,IAAI;UACvC,MAAMvE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,CAACoE,KAAK,EAAE;YACf;UACF;UAEA,IAAIpE,KAAK,CAACyC,OAAO,EAAE;YACjB6J,cAAc,CAAC/H,GAAG,CAAC,GAAGvE,KAAK,CAAC9O,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC,CAACrH,MAAM;UACjE,CAAC,MAAM;YACL;YACA;YACAgc,cAAc,CAAC/H,GAAG,CAAC,GAAGvE,KAAK,CAAC0E,OAAO,CAACqE,GAAG,CAACpR,GAAG,CAAC0I,GAAG,CAAC;UAClD;QACF,CAAC,CAAC;QAEF,OAAOiM,cAAc;MACvB;MAEAL,oBAAoBA,CAACtU,GAAG,EAAE5I,GAAG,EAAEqL,YAAY,EAAE;QAE3C,MAAMkS,cAAc,GAAG,IAAI,CAACD,uBAAuB,CAAC1U,GAAG,EAAE5I,GAAG,EAAEqL,YAAY,CAAC;QAE3E,MAAMmS,OAAO,GAAGzc,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;QAChC3H,eAAe,CAACC,OAAO,CAAC0H,GAAG,EAAE5I,GAAG,EAAE;UAACqL;QAAY,CAAC,CAAC;QAEjD,MAAMqR,aAAa,GAAG,CAAC,CAAC;QAExB,KAAK,MAAMlH,GAAG,IAAIlW,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,EAAE;UAC3C,MAAMzE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,CAACoE,KAAK,EAAE;YACf;UACF;UAEA,MAAMoI,UAAU,GAAGxM,KAAK,CAAC9O,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC;UACrD,MAAM8U,KAAK,GAAGD,UAAU,CAAClc,MAAM;UAC/B,MAAMoc,MAAM,GAAGJ,cAAc,CAAC/H,GAAG,CAAC;UAElC,IAAIkI,KAAK,IAAIzM,KAAK,CAACiE,SAAS,IAAIuI,UAAU,CAACjT,QAAQ,KAAK1I,SAAS,EAAE;YACjEmP,KAAK,CAACiE,SAAS,CAAC2C,GAAG,CAACjP,GAAG,CAAC0I,GAAG,EAAEmM,UAAU,CAACjT,QAAQ,CAAC;UACnD;UAEA,IAAIyG,KAAK,CAACmE,MAAM,CAACxC,IAAI,IAAI3B,KAAK,CAACmE,MAAM,CAACvC,KAAK,EAAE;YAC3C;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAI8K,MAAM,IAAID,KAAK,EAAE;cACnBhB,aAAa,CAAClH,GAAG,CAAC,GAAG,IAAI;YAC3B;UACF,CAAC,MAAM,IAAImI,MAAM,IAAI,CAACD,KAAK,EAAE;YAC3Bzc,eAAe,CAACia,sBAAsB,CAACjK,KAAK,EAAErI,GAAG,CAAC;UACpD,CAAC,MAAM,IAAI,CAAC+U,MAAM,IAAID,KAAK,EAAE;YAC3Bzc,eAAe,CAACmZ,oBAAoB,CAACnJ,KAAK,EAAErI,GAAG,CAAC;UAClD,CAAC,MAAM,IAAI+U,MAAM,IAAID,KAAK,EAAE;YAC1Bzc,eAAe,CAAC2c,oBAAoB,CAAC3M,KAAK,EAAErI,GAAG,EAAE4U,OAAO,CAAC;UAC3D;QACF;QACA,OAAOd,aAAa;MACtB;MAEA,MAAMG,qBAAqBA,CAACjU,GAAG,EAAE5I,GAAG,EAAEqL,YAAY,EAAE;QAElD,MAAMkS,cAAc,GAAG,IAAI,CAACD,uBAAuB,CAAC1U,GAAG,EAAE5I,GAAG,EAAEqL,YAAY,CAAC;QAE3E,MAAMmS,OAAO,GAAGzc,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;QAChC3H,eAAe,CAACC,OAAO,CAAC0H,GAAG,EAAE5I,GAAG,EAAE;UAACqL;QAAY,CAAC,CAAC;QAEjD,MAAMqR,aAAa,GAAG,CAAC,CAAC;QACxB,KAAK,MAAMlH,GAAG,IAAIlW,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC4V,OAAO,CAAC,EAAE;UAC3C,MAAMzE,KAAK,GAAG,IAAI,CAACyE,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAIvE,KAAK,CAACoE,KAAK,EAAE;YACf;UACF;UAEA,MAAMoI,UAAU,GAAGxM,KAAK,CAAC9O,OAAO,CAACb,eAAe,CAACsH,GAAG,CAAC;UACrD,MAAM8U,KAAK,GAAGD,UAAU,CAAClc,MAAM;UAC/B,MAAMoc,MAAM,GAAGJ,cAAc,CAAC/H,GAAG,CAAC;UAElC,IAAIkI,KAAK,IAAIzM,KAAK,CAACiE,SAAS,IAAIuI,UAAU,CAACjT,QAAQ,KAAK1I,SAAS,EAAE;YACjEmP,KAAK,CAACiE,SAAS,CAAC2C,GAAG,CAACjP,GAAG,CAAC0I,GAAG,EAAEmM,UAAU,CAACjT,QAAQ,CAAC;UACnD;UAEA,IAAIyG,KAAK,CAACmE,MAAM,CAACxC,IAAI,IAAI3B,KAAK,CAACmE,MAAM,CAACvC,KAAK,EAAE;YAC3C;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAI8K,MAAM,IAAID,KAAK,EAAE;cACnBhB,aAAa,CAAClH,GAAG,CAAC,GAAG,IAAI;YAC3B;UACF,CAAC,MAAM,IAAImI,MAAM,IAAI,CAACD,KAAK,EAAE;YAC3B,MAAMzc,eAAe,CAACma,uBAAuB,CAACnK,KAAK,EAAErI,GAAG,CAAC;UAC3D,CAAC,MAAM,IAAI,CAAC+U,MAAM,IAAID,KAAK,EAAE;YAC3B,MAAMzc,eAAe,CAACuZ,qBAAqB,CAACvJ,KAAK,EAAErI,GAAG,CAAC;UACzD,CAAC,MAAM,IAAI+U,MAAM,IAAID,KAAK,EAAE;YAC1B,MAAMzc,eAAe,CAAC4c,qBAAqB,CAAC5M,KAAK,EAAErI,GAAG,EAAE4U,OAAO,CAAC;UAClE;QACF;QACA,OAAOd,aAAa;MACtB;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACArC,iBAAiBA,CAACpJ,KAAK,EAAE6M,UAAU,EAAE;QACnC,IAAI,IAAI,CAAClI,MAAM,EAAE;UACf;UACA;UACA;UACA3E,KAAK,CAACoE,KAAK,GAAG,IAAI;UAClB;QACF;QAEA,IAAI,CAAC,IAAI,CAACO,MAAM,IAAI,CAACkI,UAAU,EAAE;UAC/BA,UAAU,GAAG7M,KAAK,CAAC0E,OAAO;QAC5B;QAEA,IAAI1E,KAAK,CAACiE,SAAS,EAAE;UACnBjE,KAAK,CAACiE,SAAS,CAAC4C,KAAK,CAAC,CAAC;QACzB;QAEA7G,KAAK,CAAC0E,OAAO,GAAG1E,KAAK,CAACmE,MAAM,CAAC3B,cAAc,CAAC;UAC1CyB,SAAS,EAAEjE,KAAK,CAACiE,SAAS;UAC1BxB,OAAO,EAAEzC,KAAK,CAACyC;QACjB,CAAC,CAAC;QAEF,IAAI,CAAC,IAAI,CAACkC,MAAM,EAAE;UAChB3U,eAAe,CAACqa,iBAAiB,CAC/BrK,KAAK,CAACyC,OAAO,EACboK,UAAU,EACV7M,KAAK,CAAC0E,OAAO,EACb1E,KAAK,EACL;YAACqE,YAAY,EAAErE,KAAK,CAACqE;UAAY,CACnC,CAAC;QACH;MACF;MAEA2E,aAAaA,CAAChC,EAAE,EAAErP,GAAG,EAAE;QACrB;QACA,IAAI,CAAC,IAAI,CAACuQ,eAAe,EAAE;UACzB;QACF;;QAEA;QACA;QACA;QACA,IAAI,IAAI,CAACA,eAAe,CAACa,GAAG,CAAC/B,EAAE,CAAC,EAAE;UAChC;QACF;QAEA,IAAI,CAACkB,eAAe,CAACtB,GAAG,CAACI,EAAE,EAAElX,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC,CAAC;MAChD;IACF;IAEA3H,eAAe,CAACoR,MAAM,GAAGA,MAAM;IAE/BpR,eAAe,CAACuV,aAAa,GAAGA,aAAa;;IAE7C;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACAvV,eAAe,CAAC8c,sBAAsB,GAAG,MAAMA,sBAAsB,CAAC;MACpEzL,WAAWA,CAAA,EAAe;QAAA,IAAdzG,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;QACtB,MAAMga,oBAAoB,GACxBnS,OAAO,CAACoS,SAAS,IACjBhd,eAAe,CAAC+T,kCAAkC,CAACnJ,OAAO,CAACoS,SAAS,CACrE;QAED,IAAI/f,MAAM,CAAC0E,IAAI,CAACiJ,OAAO,EAAE,SAAS,CAAC,EAAE;UACnC,IAAI,CAAC6H,OAAO,GAAG7H,OAAO,CAAC6H,OAAO;UAE9B,IAAI7H,OAAO,CAACoS,SAAS,IAAIpS,OAAO,CAAC6H,OAAO,KAAKsK,oBAAoB,EAAE;YACjE,MAAMlY,KAAK,CAAC,yCAAyC,CAAC;UACxD;QACF,CAAC,MAAM,IAAI+F,OAAO,CAACoS,SAAS,EAAE;UAC5B,IAAI,CAACvK,OAAO,GAAGsK,oBAAoB;QACrC,CAAC,MAAM;UACL,MAAMlY,KAAK,CAAC,mCAAmC,CAAC;QAClD;QAEA,MAAMmY,SAAS,GAAGpS,OAAO,CAACoS,SAAS,IAAI,CAAC,CAAC;QAEzC,IAAI,IAAI,CAACvK,OAAO,EAAE;UAChB,IAAI,CAACwK,IAAI,GAAG,IAAIC,WAAW,CAACtE,OAAO,CAACuE,WAAW,CAAC;UAChD,IAAI,CAACC,WAAW,GAAG;YACjBvK,WAAW,EAAEA,CAACmE,EAAE,EAAE9G,MAAM,EAAEwM,MAAM,KAAK;cACnC;cACA,MAAM/U,GAAG,GAAAkQ,aAAA,KAAQ3H,MAAM,CAAE;cAEzBvI,GAAG,CAAC0I,GAAG,GAAG2G,EAAE;cAEZ,IAAIgG,SAAS,CAACnK,WAAW,EAAE;gBACzBmK,SAAS,CAACnK,WAAW,CAAClR,IAAI,CAAC,IAAI,EAAEqV,EAAE,EAAElX,KAAK,CAACC,KAAK,CAACmQ,MAAM,CAAC,EAAEwM,MAAM,CAAC;cACnE;;cAEA;cACA,IAAIM,SAAS,CAAC1K,KAAK,EAAE;gBACnB0K,SAAS,CAAC1K,KAAK,CAAC3Q,IAAI,CAAC,IAAI,EAAEqV,EAAE,EAAElX,KAAK,CAACC,KAAK,CAACmQ,MAAM,CAAC,CAAC;cACrD;;cAEA;cACA;cACA;cACA,IAAI,CAAC+M,IAAI,CAACI,SAAS,CAACrG,EAAE,EAAErP,GAAG,EAAE+U,MAAM,IAAI,IAAI,CAAC;YAC9C,CAAC;YACD3J,WAAW,EAAEA,CAACiE,EAAE,EAAE0F,MAAM,KAAK;cAC3B,IAAIM,SAAS,CAACjK,WAAW,EAAE;gBACzBiK,SAAS,CAACjK,WAAW,CAACpR,IAAI,CAAC,IAAI,EAAEqV,EAAE,EAAE0F,MAAM,CAAC;cAC9C;cAEA,IAAI,CAACO,IAAI,CAACK,UAAU,CAACtG,EAAE,EAAE0F,MAAM,IAAI,IAAI,CAAC;YAC1C;UACF,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACO,IAAI,GAAG,IAAIjd,eAAe,CAACkU,MAAM,CAAD,CAAC;UACtC,IAAI,CAACkJ,WAAW,GAAG;YACjB9K,KAAK,EAAEA,CAAC0E,EAAE,EAAE9G,MAAM,KAAK;cACrB;cACA,MAAMvI,GAAG,GAAAkQ,aAAA,KAAQ3H,MAAM,CAAE;cAEzB,IAAI8M,SAAS,CAAC1K,KAAK,EAAE;gBACnB0K,SAAS,CAAC1K,KAAK,CAAC3Q,IAAI,CAAC,IAAI,EAAEqV,EAAE,EAAElX,KAAK,CAACC,KAAK,CAACmQ,MAAM,CAAC,CAAC;cACrD;cAEAvI,GAAG,CAAC0I,GAAG,GAAG2G,EAAE;cAEZ,IAAI,CAACiG,IAAI,CAACrG,GAAG,CAACI,EAAE,EAAGrP,GAAG,CAAC;YACzB;UACF,CAAC;QACH;;QAEA;QACA;QACA,IAAI,CAACyV,WAAW,CAACtK,OAAO,GAAG,CAACkE,EAAE,EAAE9G,MAAM,KAAK;UACzC,MAAMvI,GAAG,GAAG,IAAI,CAACsV,IAAI,CAACtG,GAAG,CAACK,EAAE,CAAC;UAE7B,IAAI,CAACrP,GAAG,EAAE;YACR,MAAM,IAAI9C,KAAK,4BAAAjG,MAAA,CAA4BoY,EAAE,CAAE,CAAC;UAClD;UAEA,IAAIgG,SAAS,CAAClK,OAAO,EAAE;YACrBkK,SAAS,CAAClK,OAAO,CAACnR,IAAI,CAAC,IAAI,EAAEqV,EAAE,EAAElX,KAAK,CAACC,KAAK,CAACmQ,MAAM,CAAC,CAAC;UACvD;UAEAqN,YAAY,CAACC,YAAY,CAAC7V,GAAG,EAAEuI,MAAM,CAAC;QACxC,CAAC;QAED,IAAI,CAACkN,WAAW,CAAC7K,OAAO,GAAGyE,EAAE,IAAI;UAC/B,IAAIgG,SAAS,CAACzK,OAAO,EAAE;YACrByK,SAAS,CAACzK,OAAO,CAAC5Q,IAAI,CAAC,IAAI,EAAEqV,EAAE,CAAC;UAClC;UAEA,IAAI,CAACiG,IAAI,CAACtD,MAAM,CAAC3C,EAAE,CAAC;QACtB,CAAC;MACH;IACF,CAAC;IAEDhX,eAAe,CAACkU,MAAM,GAAG,MAAMA,MAAM,SAASuJ,KAAK,CAAC;MAClDpM,WAAWA,CAAA,EAAG;QACZ,KAAK,CAACuH,OAAO,CAACuE,WAAW,EAAEvE,OAAO,CAAC8E,OAAO,CAAC;MAC7C;IACF,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA1d,eAAe,CAACgS,aAAa,GAAGC,SAAS,IAAI;MAC3C,IAAI,CAACA,SAAS,EAAE;QACd,OAAO,IAAI;MACb;;MAEA;MACA,IAAIA,SAAS,CAAC0L,oBAAoB,EAAE;QAClC,OAAO1L,SAAS;MAClB;MAEA,MAAM2L,OAAO,GAAGjW,GAAG,IAAI;QACrB,IAAI,CAAC1K,MAAM,CAAC0E,IAAI,CAACgG,GAAG,EAAE,KAAK,CAAC,EAAE;UAC5B;UACA;UACA,MAAM,IAAI9C,KAAK,CAAC,uCAAuC,CAAC;QAC1D;QAEA,MAAMmS,EAAE,GAAGrP,GAAG,CAAC0I,GAAG;;QAElB;QACA;QACA,MAAMwN,WAAW,GAAG3L,OAAO,CAAC4L,WAAW,CAAC,MAAM7L,SAAS,CAACtK,GAAG,CAAC,CAAC;QAE7D,IAAI,CAAC3H,eAAe,CAACyG,cAAc,CAACoX,WAAW,CAAC,EAAE;UAChD,MAAM,IAAIhZ,KAAK,CAAC,8BAA8B,CAAC;QACjD;QAEA,IAAI5H,MAAM,CAAC0E,IAAI,CAACkc,WAAW,EAAE,KAAK,CAAC,EAAE;UACnC,IAAI,CAAC/d,KAAK,CAACka,MAAM,CAAC6D,WAAW,CAACxN,GAAG,EAAE2G,EAAE,CAAC,EAAE;YACtC,MAAM,IAAInS,KAAK,CAAC,gDAAgD,CAAC;UACnE;QACF,CAAC,MAAM;UACLgZ,WAAW,CAACxN,GAAG,GAAG2G,EAAE;QACtB;QAEA,OAAO6G,WAAW;MACpB,CAAC;MAEDD,OAAO,CAACD,oBAAoB,GAAG,IAAI;MAEnC,OAAOC,OAAO;IAChB,CAAC;;IAED;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA5d,eAAe,CAAC+d,aAAa,GAAG,CAACC,GAAG,EAAEC,KAAK,EAAEhb,KAAK,KAAK;MACrD,IAAIib,KAAK,GAAG,CAAC;MACb,IAAIC,KAAK,GAAGF,KAAK,CAAC7e,MAAM;MAExB,OAAO+e,KAAK,GAAG,CAAC,EAAE;QAChB,MAAMC,SAAS,GAAGzS,IAAI,CAAC0S,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;QAEvC,IAAIH,GAAG,CAAC/a,KAAK,EAAEgb,KAAK,CAACC,KAAK,GAAGE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;UAC7CF,KAAK,IAAIE,SAAS,GAAG,CAAC;UACtBD,KAAK,IAAIC,SAAS,GAAG,CAAC;QACxB,CAAC,MAAM;UACLD,KAAK,GAAGC,SAAS;QACnB;MACF;MAEA,OAAOF,KAAK;IACd,CAAC;IAEDle,eAAe,CAACse,yBAAyB,GAAGpO,MAAM,IAAI;MACpD,IAAIA,MAAM,KAAK7R,MAAM,CAAC6R,MAAM,CAAC,IAAIvL,KAAK,CAACC,OAAO,CAACsL,MAAM,CAAC,EAAE;QACtD,MAAMvB,cAAc,CAAC,iCAAiC,CAAC;MACzD;MAEAtQ,MAAM,CAACQ,IAAI,CAACqR,MAAM,CAAC,CAACzO,OAAO,CAAC6O,OAAO,IAAI;QACrC,IAAIA,OAAO,CAACzS,KAAK,CAAC,GAAG,CAAC,CAAC6C,QAAQ,CAAC,GAAG,CAAC,EAAE;UACpC,MAAMiO,cAAc,CAClB,2DACF,CAAC;QACH;QAEA,MAAM1L,KAAK,GAAGiN,MAAM,CAACI,OAAO,CAAC;QAE7B,IAAI,OAAOrN,KAAK,KAAK,QAAQ,IACzB,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACnE,IAAI,CAACkE,GAAG,IACxC/F,MAAM,CAAC0E,IAAI,CAACsB,KAAK,EAAED,GAAG,CACxB,CAAC,EAAE;UACL,MAAM2L,cAAc,CAClB,0DACF,CAAC;QACH;QAEA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAACjO,QAAQ,CAACuC,KAAK,CAAC,EAAE;UACxC,MAAM0L,cAAc,CAClB,yDACF,CAAC;QACH;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA3O,eAAe,CAAC8R,kBAAkB,GAAG5B,MAAM,IAAI;MAC7ClQ,eAAe,CAACse,yBAAyB,CAACpO,MAAM,CAAC;MAEjD,MAAMqO,aAAa,GAAGrO,MAAM,CAACG,GAAG,KAAKxP,SAAS,GAAG,IAAI,GAAGqP,MAAM,CAACG,GAAG;MAClE,MAAMrO,OAAO,GAAG3E,iBAAiB,CAAC6S,MAAM,CAAC;;MAEzC;MACA,MAAM+B,SAAS,GAAGA,CAACtK,GAAG,EAAE6W,QAAQ,KAAK;QACnC;QACA,IAAI7Z,KAAK,CAACC,OAAO,CAAC+C,GAAG,CAAC,EAAE;UACtB,OAAOA,GAAG,CAAChK,GAAG,CAAC8gB,MAAM,IAAIxM,SAAS,CAACwM,MAAM,EAAED,QAAQ,CAAC,CAAC;QACvD;QAEA,MAAMle,MAAM,GAAG0B,OAAO,CAACM,SAAS,GAAG,CAAC,CAAC,GAAGxC,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;QAExDtJ,MAAM,CAACQ,IAAI,CAAC2f,QAAQ,CAAC,CAAC/c,OAAO,CAACuB,GAAG,IAAI;UACnC,IAAI2E,GAAG,IAAI,IAAI,IAAI,CAAC1K,MAAM,CAAC0E,IAAI,CAACgG,GAAG,EAAE3E,GAAG,CAAC,EAAE;YACzC;UACF;UAEA,MAAMuN,IAAI,GAAGiO,QAAQ,CAACxb,GAAG,CAAC;UAE1B,IAAIuN,IAAI,KAAKlS,MAAM,CAACkS,IAAI,CAAC,EAAE;YACzB;YACA,IAAI5I,GAAG,CAAC3E,GAAG,CAAC,KAAK3E,MAAM,CAACsJ,GAAG,CAAC3E,GAAG,CAAC,CAAC,EAAE;cACjC1C,MAAM,CAAC0C,GAAG,CAAC,GAAGiP,SAAS,CAACtK,GAAG,CAAC3E,GAAG,CAAC,EAAEuN,IAAI,CAAC;YACzC;UACF,CAAC,MAAM,IAAIvO,OAAO,CAACM,SAAS,EAAE;YAC5B;YACAhC,MAAM,CAAC0C,GAAG,CAAC,GAAGlD,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC3E,GAAG,CAAC,CAAC;UACrC,CAAC,MAAM;YACL,OAAO1C,MAAM,CAAC0C,GAAG,CAAC;UACpB;QACF,CAAC,CAAC;QAEF,OAAO2E,GAAG,IAAI,IAAI,GAAGrH,MAAM,GAAGqH,GAAG;MACnC,CAAC;MAED,OAAOA,GAAG,IAAI;QACZ,MAAMrH,MAAM,GAAG2R,SAAS,CAACtK,GAAG,EAAE3F,OAAO,CAACC,IAAI,CAAC;QAE3C,IAAIsc,aAAa,IAAIthB,MAAM,CAAC0E,IAAI,CAACgG,GAAG,EAAE,KAAK,CAAC,EAAE;UAC5CrH,MAAM,CAAC+P,GAAG,GAAG1I,GAAG,CAAC0I,GAAG;QACtB;QAEA,IAAI,CAACkO,aAAa,IAAIthB,MAAM,CAAC0E,IAAI,CAACrB,MAAM,EAAE,KAAK,CAAC,EAAE;UAChD,OAAOA,MAAM,CAAC+P,GAAG;QACnB;QAEA,OAAO/P,MAAM;MACf,CAAC;IACH,CAAC;;IAED;IACA;IACAN,eAAe,CAAC+b,qBAAqB,GAAG,CAACtZ,QAAQ,EAAErE,QAAQ,KAAK;MAC9D,MAAMsgB,gBAAgB,GAAG5a,+BAA+B,CAACrB,QAAQ,CAAC;MAClE,MAAMkc,QAAQ,GAAG3e,eAAe,CAAC4e,kBAAkB,CAACxgB,QAAQ,CAAC;MAE7D,MAAMygB,MAAM,GAAG,CAAC,CAAC;MAEjB,IAAIH,gBAAgB,CAACrO,GAAG,EAAE;QACxBwO,MAAM,CAACxO,GAAG,GAAGqO,gBAAgB,CAACrO,GAAG;QACjC,OAAOqO,gBAAgB,CAACrO,GAAG;MAC7B;;MAEA;MACA;MACA;MACArQ,eAAe,CAACC,OAAO,CAAC4e,MAAM,EAAE;QAACtgB,IAAI,EAAEmgB;MAAgB,CAAC,CAAC;MACzD1e,eAAe,CAACC,OAAO,CAAC4e,MAAM,EAAEzgB,QAAQ,EAAE;QAAC0gB,QAAQ,EAAE;MAAI,CAAC,CAAC;MAE3D,IAAIH,QAAQ,EAAE;QACZ,OAAOE,MAAM;MACf;;MAEA;MACA,MAAME,WAAW,GAAG1gB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAAC;MAC/C,IAAIygB,MAAM,CAACxO,GAAG,EAAE;QACd0O,WAAW,CAAC1O,GAAG,GAAGwO,MAAM,CAACxO,GAAG;MAC9B;MAEA,OAAO0O,WAAW;IACpB,CAAC;IAED/e,eAAe,CAACgf,YAAY,GAAG,CAACC,IAAI,EAAEC,KAAK,EAAElC,SAAS,KAAK;MACzD,OAAOO,YAAY,CAAC4B,WAAW,CAACF,IAAI,EAAEC,KAAK,EAAElC,SAAS,CAAC;IACzD,CAAC;;IAED;IACA;IACA;IACA;IACAhd,eAAe,CAACqa,iBAAiB,GAAG,CAAC5H,OAAO,EAAEoK,UAAU,EAAEuC,UAAU,EAAEC,QAAQ,EAAEzU,OAAO,KACrF2S,YAAY,CAAC+B,gBAAgB,CAAC7M,OAAO,EAAEoK,UAAU,EAAEuC,UAAU,EAAEC,QAAQ,EAAEzU,OAAO,CAAC;IAGnF5K,eAAe,CAACuf,wBAAwB,GAAG,CAAC1C,UAAU,EAAEuC,UAAU,EAAEC,QAAQ,EAAEzU,OAAO,KACnF2S,YAAY,CAACiC,uBAAuB,CAAC3C,UAAU,EAAEuC,UAAU,EAAEC,QAAQ,EAAEzU,OAAO,CAAC;IAGjF5K,eAAe,CAACyf,0BAA0B,GAAG,CAAC5C,UAAU,EAAEuC,UAAU,EAAEC,QAAQ,EAAEzU,OAAO,KACrF2S,YAAY,CAACmC,yBAAyB,CAAC7C,UAAU,EAAEuC,UAAU,EAAEC,QAAQ,EAAEzU,OAAO,CAAC;IAGnF5K,eAAe,CAAC2f,qBAAqB,GAAG,CAAC3P,KAAK,EAAErI,GAAG,KAAK;MACtD,IAAI,CAACqI,KAAK,CAACyC,OAAO,EAAE;QAClB,MAAM,IAAI5N,KAAK,CAAC,sDAAsD,CAAC;MACzE;MAEA,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Q,KAAK,CAAC0E,OAAO,CAACtV,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC7C,IAAI8Q,KAAK,CAAC0E,OAAO,CAACxV,CAAC,CAAC,KAAKyI,GAAG,EAAE;UAC5B,OAAOzI,CAAC;QACV;MACF;MAEA,MAAM2F,KAAK,CAAC,2BAA2B,CAAC;IAC1C,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA7E,eAAe,CAAC+a,qBAAqB,GAAGtY,QAAQ,IAAI;MAClD;MACA,IAAIzC,eAAe,CAACiQ,aAAa,CAACxN,QAAQ,CAAC,EAAE;QAC3C,OAAO,CAACA,QAAQ,CAAC;MACnB;MAEA,IAAI,CAACA,QAAQ,EAAE;QACb,OAAO,IAAI;MACb;;MAEA;MACA,IAAIxF,MAAM,CAAC0E,IAAI,CAACc,QAAQ,EAAE,KAAK,CAAC,EAAE;QAChC;QACA,IAAIzC,eAAe,CAACiQ,aAAa,CAACxN,QAAQ,CAAC4N,GAAG,CAAC,EAAE;UAC/C,OAAO,CAAC5N,QAAQ,CAAC4N,GAAG,CAAC;QACvB;;QAEA;QACA,IAAI5N,QAAQ,CAAC4N,GAAG,IACT1L,KAAK,CAACC,OAAO,CAACnC,QAAQ,CAAC4N,GAAG,CAACpP,GAAG,CAAC,IAC/BwB,QAAQ,CAAC4N,GAAG,CAACpP,GAAG,CAAC7B,MAAM,IACvBqD,QAAQ,CAAC4N,GAAG,CAACpP,GAAG,CAAC2B,KAAK,CAAC5C,eAAe,CAACiQ,aAAa,CAAC,EAAE;UAC5D,OAAOxN,QAAQ,CAAC4N,GAAG,CAACpP,GAAG;QACzB;QAEA,OAAO,IAAI;MACb;;MAEA;MACA;MACA;MACA,IAAI0D,KAAK,CAACC,OAAO,CAACnC,QAAQ,CAAC4E,IAAI,CAAC,EAAE;QAChC,KAAK,IAAInI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,QAAQ,CAAC4E,IAAI,CAACjI,MAAM,EAAE,EAAEF,CAAC,EAAE;UAC7C,MAAM0gB,MAAM,GAAG5f,eAAe,CAAC+a,qBAAqB,CAACtY,QAAQ,CAAC4E,IAAI,CAACnI,CAAC,CAAC,CAAC;UAEtE,IAAI0gB,MAAM,EAAE;YACV,OAAOA,MAAM;UACf;QACF;MACF;MAEA,OAAO,IAAI;IACb,CAAC;IAED5f,eAAe,CAACmZ,oBAAoB,GAAG,CAACnJ,KAAK,EAAErI,GAAG,KAAK;MACrD,MAAMuI,MAAM,GAAGpQ,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;MAE/B,OAAOuI,MAAM,CAACG,GAAG;MAEjB,IAAIL,KAAK,CAACyC,OAAO,EAAE;QACjB,IAAI,CAACzC,KAAK,CAACuB,MAAM,EAAE;UACjBvB,KAAK,CAAC6C,WAAW,CAAClL,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,EAAE,IAAI,CAAC;UAC5DF,KAAK,CAAC0E,OAAO,CAACvI,IAAI,CAACxE,GAAG,CAAC;QACzB,CAAC,MAAM;UACL,MAAMzI,CAAC,GAAGc,eAAe,CAAC6f,mBAAmB,CAC3C7P,KAAK,CAACuB,MAAM,CAAC2F,aAAa,CAAC;YAACjD,SAAS,EAAEjE,KAAK,CAACiE;UAAS,CAAC,CAAC,EACxDjE,KAAK,CAAC0E,OAAO,EACb/M,GACF,CAAC;UAED,IAAIuL,IAAI,GAAGlD,KAAK,CAAC0E,OAAO,CAACxV,CAAC,GAAG,CAAC,CAAC;UAC/B,IAAIgU,IAAI,EAAE;YACRA,IAAI,GAAGA,IAAI,CAAC7C,GAAG;UACjB,CAAC,MAAM;YACL6C,IAAI,GAAG,IAAI;UACb;UAEAlD,KAAK,CAAC6C,WAAW,CAAClL,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,EAAEgD,IAAI,CAAC;QAC9D;QAEAlD,KAAK,CAACsC,KAAK,CAAC3K,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,CAAC;MAClD,CAAC,MAAM;QACLF,KAAK,CAACsC,KAAK,CAAC3K,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,CAAC;QAChDF,KAAK,CAAC0E,OAAO,CAACkC,GAAG,CAACjP,GAAG,CAAC0I,GAAG,EAAE1I,GAAG,CAAC;MACjC;IACF,CAAC;IAED3H,eAAe,CAACuZ,qBAAqB,GAAG,OAAOvJ,KAAK,EAAErI,GAAG,KAAK;MAC5D,MAAMuI,MAAM,GAAGpQ,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC;MAE/B,OAAOuI,MAAM,CAACG,GAAG;MAEjB,IAAIL,KAAK,CAACyC,OAAO,EAAE;QACjB,IAAI,CAACzC,KAAK,CAACuB,MAAM,EAAE;UACjB,MAAMvB,KAAK,CAAC6C,WAAW,CAAClL,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,EAAE,IAAI,CAAC;UAClEF,KAAK,CAAC0E,OAAO,CAACvI,IAAI,CAACxE,GAAG,CAAC;QACzB,CAAC,MAAM;UACL,MAAMzI,CAAC,GAAGc,eAAe,CAAC6f,mBAAmB,CAC3C7P,KAAK,CAACuB,MAAM,CAAC2F,aAAa,CAAC;YAACjD,SAAS,EAAEjE,KAAK,CAACiE;UAAS,CAAC,CAAC,EACxDjE,KAAK,CAAC0E,OAAO,EACb/M,GACF,CAAC;UAED,IAAIuL,IAAI,GAAGlD,KAAK,CAAC0E,OAAO,CAACxV,CAAC,GAAG,CAAC,CAAC;UAC/B,IAAIgU,IAAI,EAAE;YACRA,IAAI,GAAGA,IAAI,CAAC7C,GAAG;UACjB,CAAC,MAAM;YACL6C,IAAI,GAAG,IAAI;UACb;UAEA,MAAMlD,KAAK,CAAC6C,WAAW,CAAClL,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,EAAEgD,IAAI,CAAC;QACpE;QAEA,MAAMlD,KAAK,CAACsC,KAAK,CAAC3K,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,CAAC;MACxD,CAAC,MAAM;QACL,MAAMF,KAAK,CAACsC,KAAK,CAAC3K,GAAG,CAAC0I,GAAG,EAAEL,KAAK,CAACqE,YAAY,CAACnE,MAAM,CAAC,CAAC;QACtDF,KAAK,CAAC0E,OAAO,CAACkC,GAAG,CAACjP,GAAG,CAAC0I,GAAG,EAAE1I,GAAG,CAAC;MACjC;IACF,CAAC;IAED3H,eAAe,CAAC6f,mBAAmB,GAAG,CAAC7B,GAAG,EAAEC,KAAK,EAAEhb,KAAK,KAAK;MAC3D,IAAIgb,KAAK,CAAC7e,MAAM,KAAK,CAAC,EAAE;QACtB6e,KAAK,CAAC9R,IAAI,CAAClJ,KAAK,CAAC;QACjB,OAAO,CAAC;MACV;MAEA,MAAM/D,CAAC,GAAGc,eAAe,CAAC+d,aAAa,CAACC,GAAG,EAAEC,KAAK,EAAEhb,KAAK,CAAC;MAE1Dgb,KAAK,CAAC6B,MAAM,CAAC5gB,CAAC,EAAE,CAAC,EAAE+D,KAAK,CAAC;MAEzB,OAAO/D,CAAC;IACV,CAAC;IAEDc,eAAe,CAAC4e,kBAAkB,GAAG7f,GAAG,IAAI;MAC1C,IAAI4f,QAAQ,GAAG,KAAK;MACpB,IAAIoB,SAAS,GAAG,KAAK;MAErB1hB,MAAM,CAACQ,IAAI,CAACE,GAAG,CAAC,CAAC0C,OAAO,CAACuB,GAAG,IAAI;QAC9B,IAAIA,GAAG,CAAC8H,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;UAC5B6T,QAAQ,GAAG,IAAI;QACjB,CAAC,MAAM;UACLoB,SAAS,GAAG,IAAI;QAClB;MACF,CAAC,CAAC;MAEF,IAAIpB,QAAQ,IAAIoB,SAAS,EAAE;QACzB,MAAM,IAAIlb,KAAK,CACb,qEACF,CAAC;MACH;MAEA,OAAO8Z,QAAQ;IACjB,CAAC;;IAED;IACA;IACA;IACA3e,eAAe,CAACyG,cAAc,GAAG5E,CAAC,IAAI;MACpC,OAAOA,CAAC,IAAI7B,eAAe,CAACwF,EAAE,CAACC,KAAK,CAAC5D,CAAC,CAAC,KAAK,CAAC;IAC/C,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA7B,eAAe,CAACC,OAAO,GAAG,UAAC0H,GAAG,EAAEvJ,QAAQ,EAAmB;MAAA,IAAjBwM,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MACpD,IAAI,CAAC/C,eAAe,CAACyG,cAAc,CAACrI,QAAQ,CAAC,EAAE;QAC7C,MAAMuQ,cAAc,CAAC,4BAA4B,CAAC;MACpD;;MAEA;MACAvQ,QAAQ,GAAG0B,KAAK,CAACC,KAAK,CAAC3B,QAAQ,CAAC;MAEhC,MAAM4hB,UAAU,GAAG7iB,gBAAgB,CAACiB,QAAQ,CAAC;MAC7C,MAAMygB,MAAM,GAAGmB,UAAU,GAAGlgB,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC,GAAGvJ,QAAQ;MAEvD,IAAI4hB,UAAU,EAAE;QACd;QACA3hB,MAAM,CAACQ,IAAI,CAACT,QAAQ,CAAC,CAACqD,OAAO,CAACsN,QAAQ,IAAI;UACxC;UACA,MAAMkR,WAAW,GAAGrV,OAAO,CAACkU,QAAQ,IAAI/P,QAAQ,KAAK,cAAc;UACnE,MAAMmR,OAAO,GAAGC,SAAS,CAACF,WAAW,GAAG,MAAM,GAAGlR,QAAQ,CAAC;UAC1D,MAAMrK,OAAO,GAAGtG,QAAQ,CAAC2Q,QAAQ,CAAC;UAElC,IAAI,CAACmR,OAAO,EAAE;YACZ,MAAMvR,cAAc,+BAAA/P,MAAA,CAA+BmQ,QAAQ,CAAE,CAAC;UAChE;UAEA1Q,MAAM,CAACQ,IAAI,CAAC6F,OAAO,CAAC,CAACjD,OAAO,CAAC2e,OAAO,IAAI;YACtC,MAAMjZ,GAAG,GAAGzC,OAAO,CAAC0b,OAAO,CAAC;YAE5B,IAAIA,OAAO,KAAK,EAAE,EAAE;cAClB,MAAMzR,cAAc,CAAC,oCAAoC,CAAC;YAC5D;YAEA,MAAM0R,QAAQ,GAAGD,OAAO,CAACviB,KAAK,CAAC,GAAG,CAAC;YAEnC,IAAI,CAACwiB,QAAQ,CAACzd,KAAK,CAACsI,OAAO,CAAC,EAAE;cAC5B,MAAMyD,cAAc,CAClB,oBAAA/P,MAAA,CAAoBwhB,OAAO,wCAC3B,uBACF,CAAC;YACH;YAEA,MAAME,MAAM,GAAGC,aAAa,CAAC1B,MAAM,EAAEwB,QAAQ,EAAE;cAC7CjW,YAAY,EAAEQ,OAAO,CAACR,YAAY;cAClCoW,WAAW,EAAEzR,QAAQ,KAAK,SAAS;cACnC0R,QAAQ,EAAEC,mBAAmB,CAAC3R,QAAQ;YACxC,CAAC,CAAC;YAEFmR,OAAO,CAACI,MAAM,EAAED,QAAQ,CAACM,GAAG,CAAC,CAAC,EAAExZ,GAAG,EAAEiZ,OAAO,EAAEvB,MAAM,CAAC;UACvD,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,IAAIlX,GAAG,CAAC0I,GAAG,IAAI,CAACvQ,KAAK,CAACka,MAAM,CAACrS,GAAG,CAAC0I,GAAG,EAAEwO,MAAM,CAACxO,GAAG,CAAC,EAAE;UACjD,MAAM1B,cAAc,CAClB,qDAAA/P,MAAA,CAAoD+I,GAAG,CAAC0I,GAAG,iBAC3D,mEAAmE,aAAAzR,MAAA,CAC1DigB,MAAM,CAACxO,GAAG,OACrB,CAAC;QACH;MACF,CAAC,MAAM;QACL,IAAI1I,GAAG,CAAC0I,GAAG,IAAIjS,QAAQ,CAACiS,GAAG,IAAI,CAACvQ,KAAK,CAACka,MAAM,CAACrS,GAAG,CAAC0I,GAAG,EAAEjS,QAAQ,CAACiS,GAAG,CAAC,EAAE;UACnE,MAAM1B,cAAc,CAClB,gDAAA/P,MAAA,CAA+C+I,GAAG,CAAC0I,GAAG,0BAAAzR,MAAA,CAC5CR,QAAQ,CAACiS,GAAG,QACxB,CAAC;QACH;;QAEA;QACAqI,wBAAwB,CAACta,QAAQ,CAAC;MACpC;;MAEA;MACAC,MAAM,CAACQ,IAAI,CAAC8I,GAAG,CAAC,CAAClG,OAAO,CAACuB,GAAG,IAAI;QAC9B;QACA;QACA;QACA,IAAIA,GAAG,KAAK,KAAK,EAAE;UACjB,OAAO2E,GAAG,CAAC3E,GAAG,CAAC;QACjB;MACF,CAAC,CAAC;MAEF3E,MAAM,CAACQ,IAAI,CAACggB,MAAM,CAAC,CAACpd,OAAO,CAACuB,GAAG,IAAI;QACjC2E,GAAG,CAAC3E,GAAG,CAAC,GAAG6b,MAAM,CAAC7b,GAAG,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC;IAEDhD,eAAe,CAAC4T,0BAA0B,GAAG,CAACO,MAAM,EAAEyM,gBAAgB,KAAK;MACzE,MAAM3O,SAAS,GAAGkC,MAAM,CAACT,YAAY,CAAC,CAAC,KAAK/L,GAAG,IAAIA,GAAG,CAAC;MACvD,IAAIkZ,UAAU,GAAG,CAAC,CAACD,gBAAgB,CAAC3L,iBAAiB;MAErD,IAAI6L,uBAAuB;MAC3B,IAAI9gB,eAAe,CAAC+gB,2BAA2B,CAACH,gBAAgB,CAAC,EAAE;QACjE;QACA;QACA;QACA;QACA,MAAMI,OAAO,GAAG,CAACJ,gBAAgB,CAACK,WAAW;QAE7CH,uBAAuB,GAAG;UACxBjO,WAAWA,CAACmE,EAAE,EAAE9G,MAAM,EAAEwM,MAAM,EAAE;YAC9B,MAAMwE,KAAK,GAAGL,UAAU,IAAI,EAAED,gBAAgB,CAACO,OAAO,IAAIP,gBAAgB,CAACtO,KAAK,CAAC;YACjF,IAAI4O,KAAK,EAAE;cACT;YACF;YAEA,MAAMvZ,GAAG,GAAGsK,SAAS,CAAC5T,MAAM,CAACC,MAAM,CAAC4R,MAAM,EAAE;cAACG,GAAG,EAAE2G;YAAE,CAAC,CAAC,CAAC;YAEvD,IAAI4J,gBAAgB,CAACO,OAAO,EAAE;cAC5BP,gBAAgB,CAACO,OAAO,CACpBxZ,GAAG,EACHqZ,OAAO,GACDtE,MAAM,GACF,IAAI,CAACO,IAAI,CAAC9P,OAAO,CAACuP,MAAM,CAAC,GACzB,IAAI,CAACO,IAAI,CAAC5H,IAAI,CAAC,CAAC,GACpB,CAAC,CAAC,EACRqH,MACJ,CAAC;YACH,CAAC,MAAM;cACLkE,gBAAgB,CAACtO,KAAK,CAAC3K,GAAG,CAAC;YAC7B;UACF,CAAC;UACDmL,OAAOA,CAACkE,EAAE,EAAE9G,MAAM,EAAE;YAElB,IAAI,EAAE0Q,gBAAgB,CAACQ,SAAS,IAAIR,gBAAgB,CAAC9N,OAAO,CAAC,EAAE;cAC7D;YACF;YAEA,IAAInL,GAAG,GAAG7H,KAAK,CAACC,KAAK,CAAC,IAAI,CAACkd,IAAI,CAACtG,GAAG,CAACK,EAAE,CAAC,CAAC;YACxC,IAAI,CAACrP,GAAG,EAAE;cACR,MAAM,IAAI9C,KAAK,4BAAAjG,MAAA,CAA4BoY,EAAE,CAAE,CAAC;YAClD;YAEA,MAAMqK,MAAM,GAAGpP,SAAS,CAACnS,KAAK,CAACC,KAAK,CAAC4H,GAAG,CAAC,CAAC;YAE1C4V,YAAY,CAACC,YAAY,CAAC7V,GAAG,EAAEuI,MAAM,CAAC;YAEtC,IAAI0Q,gBAAgB,CAACQ,SAAS,EAAE;cAC9BR,gBAAgB,CAACQ,SAAS,CACtBnP,SAAS,CAACtK,GAAG,CAAC,EACd0Z,MAAM,EACNL,OAAO,GAAG,IAAI,CAAC/D,IAAI,CAAC9P,OAAO,CAAC6J,EAAE,CAAC,GAAG,CAAC,CACvC,CAAC;YACH,CAAC,MAAM;cACL4J,gBAAgB,CAAC9N,OAAO,CAACb,SAAS,CAACtK,GAAG,CAAC,EAAE0Z,MAAM,CAAC;YAClD;UACF,CAAC;UACDtO,WAAWA,CAACiE,EAAE,EAAE0F,MAAM,EAAE;YACtB,IAAI,CAACkE,gBAAgB,CAACU,OAAO,EAAE;cAC7B;YACF;YAEA,MAAMC,IAAI,GAAGP,OAAO,GAAG,IAAI,CAAC/D,IAAI,CAAC9P,OAAO,CAAC6J,EAAE,CAAC,GAAG,CAAC,CAAC;YACjD,IAAIwK,EAAE,GAAGR,OAAO,GACVtE,MAAM,GACF,IAAI,CAACO,IAAI,CAAC9P,OAAO,CAACuP,MAAM,CAAC,GACzB,IAAI,CAACO,IAAI,CAAC5H,IAAI,CAAC,CAAC,GACpB,CAAC,CAAC;;YAER;YACA;YACA,IAAImM,EAAE,GAAGD,IAAI,EAAE;cACb,EAAEC,EAAE;YACN;YAEAZ,gBAAgB,CAACU,OAAO,CACpBrP,SAAS,CAACnS,KAAK,CAACC,KAAK,CAAC,IAAI,CAACkd,IAAI,CAACtG,GAAG,CAACK,EAAE,CAAC,CAAC,CAAC,EACzCuK,IAAI,EACJC,EAAE,EACF9E,MAAM,IAAI,IACd,CAAC;UACH,CAAC;UACDnK,OAAOA,CAACyE,EAAE,EAAE;YACV,IAAI,EAAE4J,gBAAgB,CAACa,SAAS,IAAIb,gBAAgB,CAACrO,OAAO,CAAC,EAAE;cAC7D;YACF;;YAEA;YACA;YACA,MAAM5K,GAAG,GAAGsK,SAAS,CAAC,IAAI,CAACgL,IAAI,CAACtG,GAAG,CAACK,EAAE,CAAC,CAAC;YAExC,IAAI4J,gBAAgB,CAACa,SAAS,EAAE;cAC9Bb,gBAAgB,CAACa,SAAS,CAAC9Z,GAAG,EAAEqZ,OAAO,GAAG,IAAI,CAAC/D,IAAI,CAAC9P,OAAO,CAAC6J,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACvE,CAAC,MAAM;cACL4J,gBAAgB,CAACrO,OAAO,CAAC5K,GAAG,CAAC;YAC/B;UACF;QACF,CAAC;MACH,CAAC,MAAM;QACLmZ,uBAAuB,GAAG;UACxBxO,KAAKA,CAAC0E,EAAE,EAAE9G,MAAM,EAAE;YAChB,IAAI,CAAC2Q,UAAU,IAAID,gBAAgB,CAACtO,KAAK,EAAE;cACzCsO,gBAAgB,CAACtO,KAAK,CAACL,SAAS,CAAC5T,MAAM,CAACC,MAAM,CAAC4R,MAAM,EAAE;gBAACG,GAAG,EAAE2G;cAAE,CAAC,CAAC,CAAC,CAAC;YACrE;UACF,CAAC;UACDlE,OAAOA,CAACkE,EAAE,EAAE9G,MAAM,EAAE;YAClB,IAAI0Q,gBAAgB,CAAC9N,OAAO,EAAE;cAC5B,MAAMuO,MAAM,GAAG,IAAI,CAACpE,IAAI,CAACtG,GAAG,CAACK,EAAE,CAAC;cAChC,MAAMrP,GAAG,GAAG7H,KAAK,CAACC,KAAK,CAACshB,MAAM,CAAC;cAE/B9D,YAAY,CAACC,YAAY,CAAC7V,GAAG,EAAEuI,MAAM,CAAC;cAEtC0Q,gBAAgB,CAAC9N,OAAO,CACpBb,SAAS,CAACtK,GAAG,CAAC,EACdsK,SAAS,CAACnS,KAAK,CAACC,KAAK,CAACshB,MAAM,CAAC,CACjC,CAAC;YACH;UACF,CAAC;UACD9O,OAAOA,CAACyE,EAAE,EAAE;YACV,IAAI4J,gBAAgB,CAACrO,OAAO,EAAE;cAC5BqO,gBAAgB,CAACrO,OAAO,CAACN,SAAS,CAAC,IAAI,CAACgL,IAAI,CAACtG,GAAG,CAACK,EAAE,CAAC,CAAC,CAAC;YACxD;UACF;QACF,CAAC;MACH;MAEA,MAAM0K,cAAc,GAAG,IAAI1hB,eAAe,CAAC8c,sBAAsB,CAAC;QAChEE,SAAS,EAAE8D;MACb,CAAC,CAAC;;MAEF;MACA;MACA;MACAY,cAAc,CAACtE,WAAW,CAACuE,YAAY,GAAG,IAAI;MAC9C,MAAMrM,MAAM,GAAGnB,MAAM,CAACL,cAAc,CAAC4N,cAAc,CAACtE,WAAW,EAC3D;QAAEwE,oBAAoB,EAAE;MAAK,CAAC,CAAC;;MAEnC;MACA,MAAMC,aAAa,GAAIC,CAAC,IAAK;QAAA,IAAAC,iBAAA;QAC3B,IAAID,CAAC,CAACrM,OAAO,EAAEoL,UAAU,GAAG,KAAK,CAAC,KAC7B,CAAAkB,iBAAA,GAAAD,CAAC,CAACpM,cAAc,cAAAqM,iBAAA,uBAAhBA,iBAAA,CAAkBhM,IAAI,CAAC,MAAO8K,UAAU,GAAG,KAAM,CAAC;MACzD,CAAC;MACD;MACA;MACA,IAAI/J,MAAM,CAACkL,UAAU,CAAC1M,MAAM,CAAC,EAAE;QAC7BA,MAAM,CAACS,IAAI,CAAC8L,aAAa,CAAC;MAC5B,CAAC,MAAM;QACLA,aAAa,CAACvM,MAAM,CAAC;MACvB;MACA,OAAOA,MAAM;IACf,CAAC;IAEDtV,eAAe,CAAC+gB,2BAA2B,GAAG/D,SAAS,IAAI;MACzD,IAAIA,SAAS,CAAC1K,KAAK,IAAI0K,SAAS,CAACmE,OAAO,EAAE;QACxC,MAAM,IAAItc,KAAK,CAAC,kDAAkD,CAAC;MACrE;MAEA,IAAImY,SAAS,CAAClK,OAAO,IAAIkK,SAAS,CAACoE,SAAS,EAAE;QAC5C,MAAM,IAAIvc,KAAK,CAAC,sDAAsD,CAAC;MACzE;MAEA,IAAImY,SAAS,CAACzK,OAAO,IAAIyK,SAAS,CAACyE,SAAS,EAAE;QAC5C,MAAM,IAAI5c,KAAK,CAAC,sDAAsD,CAAC;MACzE;MAEA,OAAO,CAAC,EACNmY,SAAS,CAACmE,OAAO,IACjBnE,SAAS,CAACoE,SAAS,IACnBpE,SAAS,CAACsE,OAAO,IACjBtE,SAAS,CAACyE,SAAS,CACpB;IACH,CAAC;IAEDzhB,eAAe,CAAC+T,kCAAkC,GAAGiJ,SAAS,IAAI;MAChE,IAAIA,SAAS,CAAC1K,KAAK,IAAI0K,SAAS,CAACnK,WAAW,EAAE;QAC5C,MAAM,IAAIhO,KAAK,CAAC,sDAAsD,CAAC;MACzE;MAEA,OAAO,CAAC,EAAEmY,SAAS,CAACnK,WAAW,IAAImK,SAAS,CAACjK,WAAW,CAAC;IAC3D,CAAC;IAED/S,eAAe,CAACia,sBAAsB,GAAG,CAACjK,KAAK,EAAErI,GAAG,KAAK;MACvD,IAAIqI,KAAK,CAACyC,OAAO,EAAE;QACjB,MAAMvT,CAAC,GAAGc,eAAe,CAAC2f,qBAAqB,CAAC3P,KAAK,EAAErI,GAAG,CAAC;QAE3DqI,KAAK,CAACuC,OAAO,CAAC5K,GAAG,CAAC0I,GAAG,CAAC;QACtBL,KAAK,CAAC0E,OAAO,CAACoL,MAAM,CAAC5gB,CAAC,EAAE,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL,MAAM8X,EAAE,GAAGrP,GAAG,CAAC0I,GAAG,CAAC,CAAE;;QAErBL,KAAK,CAACuC,OAAO,CAAC5K,GAAG,CAAC0I,GAAG,CAAC;QACtBL,KAAK,CAAC0E,OAAO,CAACiF,MAAM,CAAC3C,EAAE,CAAC;MAC1B;IACF,CAAC;IAEDhX,eAAe,CAACma,uBAAuB,GAAG,OAAOnK,KAAK,EAAErI,GAAG,KAAK;MAC9D,IAAIqI,KAAK,CAACyC,OAAO,EAAE;QACjB,MAAMvT,CAAC,GAAGc,eAAe,CAAC2f,qBAAqB,CAAC3P,KAAK,EAAErI,GAAG,CAAC;QAE3D,MAAMqI,KAAK,CAACuC,OAAO,CAAC5K,GAAG,CAAC0I,GAAG,CAAC;QAC5BL,KAAK,CAAC0E,OAAO,CAACoL,MAAM,CAAC5gB,CAAC,EAAE,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL,MAAM8X,EAAE,GAAGrP,GAAG,CAAC0I,GAAG,CAAC,CAAE;;QAErB,MAAML,KAAK,CAACuC,OAAO,CAAC5K,GAAG,CAAC0I,GAAG,CAAC;QAC5BL,KAAK,CAAC0E,OAAO,CAACiF,MAAM,CAAC3C,EAAE,CAAC;MAC1B;IACF,CAAC;;IAED;IACAhX,eAAe,CAACiQ,aAAa,GAAGxN,QAAQ,IACtC,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,OAAOA,QAAQ,KAAK,QAAQ,IAC5BA,QAAQ,YAAYmW,OAAO,CAACC,QAAQ;;IAGtC;IACA7Y,eAAe,CAACwR,4BAA4B,GAAG/O,QAAQ,IACrDzC,eAAe,CAACiQ,aAAa,CAACxN,QAAQ,CAAC,IACvCzC,eAAe,CAACiQ,aAAa,CAACxN,QAAQ,IAAIA,QAAQ,CAAC4N,GAAG,CAAC,IACvDhS,MAAM,CAACQ,IAAI,CAAC4D,QAAQ,CAAC,CAACrD,MAAM,KAAK,CAAC;IAGpCY,eAAe,CAAC2c,oBAAoB,GAAG,CAAC3M,KAAK,EAAErI,GAAG,EAAE4U,OAAO,KAAK;MAC9D,IAAI,CAACzc,KAAK,CAACka,MAAM,CAACrS,GAAG,CAAC0I,GAAG,EAAEkM,OAAO,CAAClM,GAAG,CAAC,EAAE;QACvC,MAAM,IAAIxL,KAAK,CAAC,2CAA2C,CAAC;MAC9D;MAEA,MAAMwP,YAAY,GAAGrE,KAAK,CAACqE,YAAY;MACvC,MAAM4N,aAAa,GAAG1E,YAAY,CAAC2E,iBAAiB,CAClD7N,YAAY,CAAC1M,GAAG,CAAC,EACjB0M,YAAY,CAACkI,OAAO,CACtB,CAAC;MAED,IAAI,CAACvM,KAAK,CAACyC,OAAO,EAAE;QAClB,IAAIpU,MAAM,CAACQ,IAAI,CAACojB,aAAa,CAAC,CAAC7iB,MAAM,EAAE;UACrC4Q,KAAK,CAAC8C,OAAO,CAACnL,GAAG,CAAC0I,GAAG,EAAE4R,aAAa,CAAC;UACrCjS,KAAK,CAAC0E,OAAO,CAACkC,GAAG,CAACjP,GAAG,CAAC0I,GAAG,EAAE1I,GAAG,CAAC;QACjC;QAEA;MACF;MAEA,MAAMwa,OAAO,GAAGniB,eAAe,CAAC2f,qBAAqB,CAAC3P,KAAK,EAAErI,GAAG,CAAC;MAEjE,IAAItJ,MAAM,CAACQ,IAAI,CAACojB,aAAa,CAAC,CAAC7iB,MAAM,EAAE;QACrC4Q,KAAK,CAAC8C,OAAO,CAACnL,GAAG,CAAC0I,GAAG,EAAE4R,aAAa,CAAC;MACvC;MAEA,IAAI,CAACjS,KAAK,CAACuB,MAAM,EAAE;QACjB;MACF;;MAEA;MACAvB,KAAK,CAAC0E,OAAO,CAACoL,MAAM,CAACqC,OAAO,EAAE,CAAC,CAAC;MAEhC,MAAMC,OAAO,GAAGpiB,eAAe,CAAC6f,mBAAmB,CACjD7P,KAAK,CAACuB,MAAM,CAAC2F,aAAa,CAAC;QAACjD,SAAS,EAAEjE,KAAK,CAACiE;MAAS,CAAC,CAAC,EACxDjE,KAAK,CAAC0E,OAAO,EACb/M,GACF,CAAC;MAED,IAAIwa,OAAO,KAAKC,OAAO,EAAE;QACvB,IAAIlP,IAAI,GAAGlD,KAAK,CAAC0E,OAAO,CAAC0N,OAAO,GAAG,CAAC,CAAC;QACrC,IAAIlP,IAAI,EAAE;UACRA,IAAI,GAAGA,IAAI,CAAC7C,GAAG;QACjB,CAAC,MAAM;UACL6C,IAAI,GAAG,IAAI;QACb;QAEAlD,KAAK,CAAC+C,WAAW,IAAI/C,KAAK,CAAC+C,WAAW,CAACpL,GAAG,CAAC0I,GAAG,EAAE6C,IAAI,CAAC;MACvD;IACF,CAAC;IAEDlT,eAAe,CAAC4c,qBAAqB,GAAG,OAAO5M,KAAK,EAAErI,GAAG,EAAE4U,OAAO,KAAK;MACrE,IAAI,CAACzc,KAAK,CAACka,MAAM,CAACrS,GAAG,CAAC0I,GAAG,EAAEkM,OAAO,CAAClM,GAAG,CAAC,EAAE;QACvC,MAAM,IAAIxL,KAAK,CAAC,2CAA2C,CAAC;MAC9D;MAEA,MAAMwP,YAAY,GAAGrE,KAAK,CAACqE,YAAY;MACvC,MAAM4N,aAAa,GAAG1E,YAAY,CAAC2E,iBAAiB,CAClD7N,YAAY,CAAC1M,GAAG,CAAC,EACjB0M,YAAY,CAACkI,OAAO,CACtB,CAAC;MAED,IAAI,CAACvM,KAAK,CAACyC,OAAO,EAAE;QAClB,IAAIpU,MAAM,CAACQ,IAAI,CAACojB,aAAa,CAAC,CAAC7iB,MAAM,EAAE;UACrC,MAAM4Q,KAAK,CAAC8C,OAAO,CAACnL,GAAG,CAAC0I,GAAG,EAAE4R,aAAa,CAAC;UAC3CjS,KAAK,CAAC0E,OAAO,CAACkC,GAAG,CAACjP,GAAG,CAAC0I,GAAG,EAAE1I,GAAG,CAAC;QACjC;QAEA;MACF;MAEA,MAAMwa,OAAO,GAAGniB,eAAe,CAAC2f,qBAAqB,CAAC3P,KAAK,EAAErI,GAAG,CAAC;MAEjE,IAAItJ,MAAM,CAACQ,IAAI,CAACojB,aAAa,CAAC,CAAC7iB,MAAM,EAAE;QACrC,MAAM4Q,KAAK,CAAC8C,OAAO,CAACnL,GAAG,CAAC0I,GAAG,EAAE4R,aAAa,CAAC;MAC7C;MAEA,IAAI,CAACjS,KAAK,CAACuB,MAAM,EAAE;QACjB;MACF;;MAEA;MACAvB,KAAK,CAAC0E,OAAO,CAACoL,MAAM,CAACqC,OAAO,EAAE,CAAC,CAAC;MAEhC,MAAMC,OAAO,GAAGpiB,eAAe,CAAC6f,mBAAmB,CACjD7P,KAAK,CAACuB,MAAM,CAAC2F,aAAa,CAAC;QAACjD,SAAS,EAAEjE,KAAK,CAACiE;MAAS,CAAC,CAAC,EACxDjE,KAAK,CAAC0E,OAAO,EACb/M,GACF,CAAC;MAED,IAAIwa,OAAO,KAAKC,OAAO,EAAE;QACvB,IAAIlP,IAAI,GAAGlD,KAAK,CAAC0E,OAAO,CAAC0N,OAAO,GAAG,CAAC,CAAC;QACrC,IAAIlP,IAAI,EAAE;UACRA,IAAI,GAAGA,IAAI,CAAC7C,GAAG;QACjB,CAAC,MAAM;UACL6C,IAAI,GAAG,IAAI;QACb;QAEAlD,KAAK,CAAC+C,WAAW,KAAI,MAAM/C,KAAK,CAAC+C,WAAW,CAACpL,GAAG,CAAC0I,GAAG,EAAE6C,IAAI,CAAC;MAC7D;IACF,CAAC;IAED,MAAMiN,SAAS,GAAG;MAChBkC,YAAYA,CAAC/B,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QAC/B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIlK,MAAM,CAAC0E,IAAI,CAACwF,GAAG,EAAE,OAAO,CAAC,EAAE;UACxD,IAAIA,GAAG,CAAC9B,KAAK,KAAK,MAAM,EAAE;YACxB,MAAMsJ,cAAc,CAClB,yDAAyD,GACzD,wBAAwB,EACxB;cAACE;YAAK,CACR,CAAC;UACH;QACF,CAAC,MAAM,IAAI1H,GAAG,KAAK,IAAI,EAAE;UACvB,MAAMwH,cAAc,CAAC,+BAA+B,EAAE;YAACE;UAAK,CAAC,CAAC;QAChE;QAEAyR,MAAM,CAACzR,KAAK,CAAC,GAAG,IAAIyT,IAAI,CAAC,CAAC;MAC5B,CAAC;MACDC,IAAIA,CAACjC,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UAC3B,MAAMwH,cAAc,CAAC,wCAAwC,EAAE;YAACE;UAAK,CAAC,CAAC;QACzE;QAEA,IAAIA,KAAK,IAAIyR,MAAM,EAAE;UACnB,IAAI,OAAOA,MAAM,CAACzR,KAAK,CAAC,KAAK,QAAQ,EAAE;YACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;cAACE;YAAK,CACR,CAAC;UACH;UAEAyR,MAAM,CAACzR,KAAK,CAAC,IAAI1H,GAAG;QACtB,CAAC,MAAM;UACLmZ,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG;QACrB;MACF,CAAC;MACDqb,IAAIA,CAAClC,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UAC3B,MAAMwH,cAAc,CAAC,wCAAwC,EAAE;YAACE;UAAK,CAAC,CAAC;QACzE;QAEA,IAAIA,KAAK,IAAIyR,MAAM,EAAE;UACnB,IAAI,OAAOA,MAAM,CAACzR,KAAK,CAAC,KAAK,QAAQ,EAAE;YACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;cAACE;YAAK,CACR,CAAC;UACH;UAEA,IAAIyR,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG,EAAE;YACvBmZ,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG;UACrB;QACF,CAAC,MAAM;UACLmZ,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG;QACrB;MACF,CAAC;MACDsb,IAAIA,CAACnC,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UAC3B,MAAMwH,cAAc,CAAC,wCAAwC,EAAE;YAACE;UAAK,CAAC,CAAC;QACzE;QAEA,IAAIA,KAAK,IAAIyR,MAAM,EAAE;UACnB,IAAI,OAAOA,MAAM,CAACzR,KAAK,CAAC,KAAK,QAAQ,EAAE;YACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;cAACE;YAAK,CACR,CAAC;UACH;UAEA,IAAIyR,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG,EAAE;YACvBmZ,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG;UACrB;QACF,CAAC,MAAM;UACLmZ,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG;QACrB;MACF,CAAC;MACDub,IAAIA,CAACpC,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UAC3B,MAAMwH,cAAc,CAAC,wCAAwC,EAAE;YAACE;UAAK,CAAC,CAAC;QACzE;QAEA,IAAIA,KAAK,IAAIyR,MAAM,EAAE;UACnB,IAAI,OAAOA,MAAM,CAACzR,KAAK,CAAC,KAAK,QAAQ,EAAE;YACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;cAACE;YAAK,CACR,CAAC;UACH;UAEAyR,MAAM,CAACzR,KAAK,CAAC,IAAI1H,GAAG;QACtB,CAAC,MAAM;UACLmZ,MAAM,CAACzR,KAAK,CAAC,GAAG,CAAC;QACnB;MACF,CAAC;MACD8T,OAAOA,CAACrC,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAEiZ,OAAO,EAAEzY,GAAG,EAAE;QACxC;QACA,IAAIyY,OAAO,KAAKjZ,GAAG,EAAE;UACnB,MAAMwH,cAAc,CAAC,wCAAwC,EAAE;YAACE;UAAK,CAAC,CAAC;QACzE;QAEA,IAAIyR,MAAM,KAAK,IAAI,EAAE;UACnB,MAAM3R,cAAc,CAAC,8BAA8B,EAAE;YAACE;UAAK,CAAC,CAAC;QAC/D;QAEA,IAAI,OAAO1H,GAAG,KAAK,QAAQ,EAAE;UAC3B,MAAMwH,cAAc,CAAC,iCAAiC,EAAE;YAACE;UAAK,CAAC,CAAC;QAClE;QAEA,IAAI1H,GAAG,CAACzG,QAAQ,CAAC,IAAI,CAAC,EAAE;UACtB;UACA;UACA,MAAMiO,cAAc,CAClB,mEAAmE,EACnE;YAACE;UAAK,CACR,CAAC;QACH;QAEA,IAAIyR,MAAM,KAAKzf,SAAS,EAAE;UACxB;QACF;QAEA,MAAMkP,MAAM,GAAGuQ,MAAM,CAACzR,KAAK,CAAC;QAE5B,OAAOyR,MAAM,CAACzR,KAAK,CAAC;QAEpB,MAAMwR,QAAQ,GAAGlZ,GAAG,CAACtJ,KAAK,CAAC,GAAG,CAAC;QAC/B,MAAM+kB,OAAO,GAAGrC,aAAa,CAAC5Y,GAAG,EAAE0Y,QAAQ,EAAE;UAACG,WAAW,EAAE;QAAI,CAAC,CAAC;QAEjE,IAAIoC,OAAO,KAAK,IAAI,EAAE;UACpB,MAAMjU,cAAc,CAAC,8BAA8B,EAAE;YAACE;UAAK,CAAC,CAAC;QAC/D;QAEA+T,OAAO,CAACvC,QAAQ,CAACM,GAAG,CAAC,CAAC,CAAC,GAAG5Q,MAAM;MAClC,CAAC;MACDxR,IAAIA,CAAC+hB,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACvB,IAAImZ,MAAM,KAAKjiB,MAAM,CAACiiB,MAAM,CAAC,EAAE;UAAE;UAC/B,MAAMpgB,KAAK,GAAGyO,cAAc,CAC1B,yCAAyC,EACzC;YAACE;UAAK,CACR,CAAC;UACD3O,KAAK,CAACE,gBAAgB,GAAG,IAAI;UAC7B,MAAMF,KAAK;QACb;QAEA,IAAIogB,MAAM,KAAK,IAAI,EAAE;UACnB,MAAMpgB,KAAK,GAAGyO,cAAc,CAAC,6BAA6B,EAAE;YAACE;UAAK,CAAC,CAAC;UACpE3O,KAAK,CAACE,gBAAgB,GAAG,IAAI;UAC7B,MAAMF,KAAK;QACb;QAEAwY,wBAAwB,CAACvR,GAAG,CAAC;QAE7BmZ,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG;MACrB,CAAC;MACD0b,YAAYA,CAACvC,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QAC/B;MAAA,CACD;MACD3I,MAAMA,CAAC8hB,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACzB,IAAImZ,MAAM,KAAKzf,SAAS,EAAE;UACxB,IAAIyf,MAAM,YAAY3b,KAAK,EAAE;YAC3B,IAAIkK,KAAK,IAAIyR,MAAM,EAAE;cACnBA,MAAM,CAACzR,KAAK,CAAC,GAAG,IAAI;YACtB;UACF,CAAC,MAAM;YACL,OAAOyR,MAAM,CAACzR,KAAK,CAAC;UACtB;QACF;MACF,CAAC;MACDiU,KAAKA,CAACxC,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACxB,IAAImZ,MAAM,CAACzR,KAAK,CAAC,KAAKhO,SAAS,EAAE;UAC/Byf,MAAM,CAACzR,KAAK,CAAC,GAAG,EAAE;QACpB;QAEA,IAAI,EAAEyR,MAAM,CAACzR,KAAK,CAAC,YAAYlK,KAAK,CAAC,EAAE;UACrC,MAAMgK,cAAc,CAAC,0CAA0C,EAAE;YAACE;UAAK,CAAC,CAAC;QAC3E;QAEA,IAAI,EAAE1H,GAAG,IAAIA,GAAG,CAAC4b,KAAK,CAAC,EAAE;UACvB;UACArK,wBAAwB,CAACvR,GAAG,CAAC;UAE7BmZ,MAAM,CAACzR,KAAK,CAAC,CAAC1C,IAAI,CAAChF,GAAG,CAAC;UAEvB;QACF;;QAEA;QACA,MAAM6b,MAAM,GAAG7b,GAAG,CAAC4b,KAAK;QACxB,IAAI,EAAEC,MAAM,YAAYre,KAAK,CAAC,EAAE;UAC9B,MAAMgK,cAAc,CAAC,wBAAwB,EAAE;YAACE;UAAK,CAAC,CAAC;QACzD;QAEA6J,wBAAwB,CAACsK,MAAM,CAAC;;QAEhC;QACA,IAAIC,QAAQ,GAAGpiB,SAAS;QACxB,IAAI,WAAW,IAAIsG,GAAG,EAAE;UACtB,IAAI,OAAOA,GAAG,CAAC+b,SAAS,KAAK,QAAQ,EAAE;YACrC,MAAMvU,cAAc,CAAC,mCAAmC,EAAE;cAACE;YAAK,CAAC,CAAC;UACpE;;UAEA;UACA,IAAI1H,GAAG,CAAC+b,SAAS,GAAG,CAAC,EAAE;YACrB,MAAMvU,cAAc,CAClB,6CAA6C,EAC7C;cAACE;YAAK,CACR,CAAC;UACH;UAEAoU,QAAQ,GAAG9b,GAAG,CAAC+b,SAAS;QAC1B;;QAEA;QACA,IAAI/U,KAAK,GAAGtN,SAAS;QACrB,IAAI,QAAQ,IAAIsG,GAAG,EAAE;UACnB,IAAI,OAAOA,GAAG,CAACgc,MAAM,KAAK,QAAQ,EAAE;YAClC,MAAMxU,cAAc,CAAC,gCAAgC,EAAE;cAACE;YAAK,CAAC,CAAC;UACjE;;UAEA;UACAV,KAAK,GAAGhH,GAAG,CAACgc,MAAM;QACpB;;QAEA;QACA,IAAIC,YAAY,GAAGviB,SAAS;QAC5B,IAAIsG,GAAG,CAACkc,KAAK,EAAE;UACb,IAAIlV,KAAK,KAAKtN,SAAS,EAAE;YACvB,MAAM8N,cAAc,CAAC,qCAAqC,EAAE;cAACE;YAAK,CAAC,CAAC;UACtE;;UAEA;UACA;UACA;UACA;UACAuU,YAAY,GAAG,IAAI5lB,SAAS,CAACsE,MAAM,CAACqF,GAAG,CAACkc,KAAK,CAAC,CAACnM,aAAa,CAAC,CAAC;UAE9D8L,MAAM,CAACvhB,OAAO,CAAC8J,OAAO,IAAI;YACxB,IAAIvL,eAAe,CAACwF,EAAE,CAACC,KAAK,CAAC8F,OAAO,CAAC,KAAK,CAAC,EAAE;cAC3C,MAAMoD,cAAc,CAClB,8DAA8D,GAC9D,SAAS,EACT;gBAACE;cAAK,CACR,CAAC;YACH;UACF,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIoU,QAAQ,KAAKpiB,SAAS,EAAE;UAC1BmiB,MAAM,CAACvhB,OAAO,CAAC8J,OAAO,IAAI;YACxB+U,MAAM,CAACzR,KAAK,CAAC,CAAC1C,IAAI,CAACZ,OAAO,CAAC;UAC7B,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,MAAM+X,eAAe,GAAG,CAACL,QAAQ,EAAE,CAAC,CAAC;UAErCD,MAAM,CAACvhB,OAAO,CAAC8J,OAAO,IAAI;YACxB+X,eAAe,CAACnX,IAAI,CAACZ,OAAO,CAAC;UAC/B,CAAC,CAAC;UAEF+U,MAAM,CAACzR,KAAK,CAAC,CAACiR,MAAM,CAAC,GAAGwD,eAAe,CAAC;QAC1C;;QAEA;QACA,IAAIF,YAAY,EAAE;UAChB9C,MAAM,CAACzR,KAAK,CAAC,CAACuB,IAAI,CAACgT,YAAY,CAAC;QAClC;;QAEA;QACA,IAAIjV,KAAK,KAAKtN,SAAS,EAAE;UACvB,IAAIsN,KAAK,KAAK,CAAC,EAAE;YACfmS,MAAM,CAACzR,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;UACtB,CAAC,MAAM,IAAIV,KAAK,GAAG,CAAC,EAAE;YACpBmS,MAAM,CAACzR,KAAK,CAAC,GAAGyR,MAAM,CAACzR,KAAK,CAAC,CAACV,KAAK,CAACA,KAAK,CAAC;UAC5C,CAAC,MAAM;YACLmS,MAAM,CAACzR,KAAK,CAAC,GAAGyR,MAAM,CAACzR,KAAK,CAAC,CAACV,KAAK,CAAC,CAAC,EAAEA,KAAK,CAAC;UAC/C;QACF;MACF,CAAC;MACDoV,QAAQA,CAACjD,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QAC3B,IAAI,EAAE,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYxC,KAAK,CAAC,EAAE;UACtD,MAAMgK,cAAc,CAAC,mDAAmD,CAAC;QAC3E;QAEA+J,wBAAwB,CAACvR,GAAG,CAAC;QAE7B,MAAM6b,MAAM,GAAG1C,MAAM,CAACzR,KAAK,CAAC;QAE5B,IAAImU,MAAM,KAAKniB,SAAS,EAAE;UACxByf,MAAM,CAACzR,KAAK,CAAC,GAAG1H,GAAG;QACrB,CAAC,MAAM,IAAI,EAAE6b,MAAM,YAAYre,KAAK,CAAC,EAAE;UACrC,MAAMgK,cAAc,CAClB,6CAA6C,EAC7C;YAACE;UAAK,CACR,CAAC;QACH,CAAC,MAAM;UACLmU,MAAM,CAAC7W,IAAI,CAAC,GAAGhF,GAAG,CAAC;QACrB;MACF,CAAC;MACDqc,SAASA,CAAClD,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QAC5B,IAAIsc,MAAM,GAAG,KAAK;QAElB,IAAI,OAAOtc,GAAG,KAAK,QAAQ,EAAE;UAC3B;UACA,MAAMtI,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACsI,GAAG,CAAC;UAC7B,IAAItI,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;YACvB4kB,MAAM,GAAG,IAAI;UACf;QACF;QAEA,MAAMC,MAAM,GAAGD,MAAM,GAAGtc,GAAG,CAAC4b,KAAK,GAAG,CAAC5b,GAAG,CAAC;QAEzCuR,wBAAwB,CAACgL,MAAM,CAAC;QAEhC,MAAMC,KAAK,GAAGrD,MAAM,CAACzR,KAAK,CAAC;QAC3B,IAAI8U,KAAK,KAAK9iB,SAAS,EAAE;UACvByf,MAAM,CAACzR,KAAK,CAAC,GAAG6U,MAAM;QACxB,CAAC,MAAM,IAAI,EAAEC,KAAK,YAAYhf,KAAK,CAAC,EAAE;UACpC,MAAMgK,cAAc,CAClB,8CAA8C,EAC9C;YAACE;UAAK,CACR,CAAC;QACH,CAAC,MAAM;UACL6U,MAAM,CAACjiB,OAAO,CAACwB,KAAK,IAAI;YACtB,IAAI0gB,KAAK,CAAC7kB,IAAI,CAACyM,OAAO,IAAIvL,eAAe,CAACwF,EAAE,CAACsG,MAAM,CAAC7I,KAAK,EAAEsI,OAAO,CAAC,CAAC,EAAE;cACpE;YACF;YAEAoY,KAAK,CAACxX,IAAI,CAAClJ,KAAK,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC;MACD2gB,IAAIA,CAACtD,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACvB,IAAImZ,MAAM,KAAKzf,SAAS,EAAE;UACxB;QACF;QAEA,MAAMgjB,KAAK,GAAGvD,MAAM,CAACzR,KAAK,CAAC;QAE3B,IAAIgV,KAAK,KAAKhjB,SAAS,EAAE;UACvB;QACF;QAEA,IAAI,EAAEgjB,KAAK,YAAYlf,KAAK,CAAC,EAAE;UAC7B,MAAMgK,cAAc,CAAC,yCAAyC,EAAE;YAACE;UAAK,CAAC,CAAC;QAC1E;QAEA,IAAI,OAAO1H,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG,CAAC,EAAE;UACtC0c,KAAK,CAAC/D,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC,MAAM;UACL+D,KAAK,CAAClD,GAAG,CAAC,CAAC;QACb;MACF,CAAC;MACDmD,KAAKA,CAACxD,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACxB,IAAImZ,MAAM,KAAKzf,SAAS,EAAE;UACxB;QACF;QAEA,MAAMkjB,MAAM,GAAGzD,MAAM,CAACzR,KAAK,CAAC;QAC5B,IAAIkV,MAAM,KAAKljB,SAAS,EAAE;UACxB;QACF;QAEA,IAAI,EAAEkjB,MAAM,YAAYpf,KAAK,CAAC,EAAE;UAC9B,MAAMgK,cAAc,CAClB,kDAAkD,EAClD;YAACE;UAAK,CACR,CAAC;QACH;QAEA,IAAImV,GAAG;QACP,IAAI7c,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,EAAEA,GAAG,YAAYxC,KAAK,CAAC,EAAE;UACrE;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;UACA,MAAMzD,OAAO,GAAG,IAAI1D,SAAS,CAACS,OAAO,CAACkJ,GAAG,CAAC;UAE1C6c,GAAG,GAAGD,MAAM,CAACjmB,MAAM,CAACyN,OAAO,IAAI,CAACrK,OAAO,CAACb,eAAe,CAACkL,OAAO,CAAC,CAACjL,MAAM,CAAC;QAC1E,CAAC,MAAM;UACL0jB,GAAG,GAAGD,MAAM,CAACjmB,MAAM,CAACyN,OAAO,IAAI,CAACvL,eAAe,CAACwF,EAAE,CAACsG,MAAM,CAACP,OAAO,EAAEpE,GAAG,CAAC,CAAC;QAC1E;QAEAmZ,MAAM,CAACzR,KAAK,CAAC,GAAGmV,GAAG;MACrB,CAAC;MACDC,QAAQA,CAAC3D,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QAC3B,IAAI,EAAE,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYxC,KAAK,CAAC,EAAE;UACtD,MAAMgK,cAAc,CAClB,mDAAmD,EACnD;YAACE;UAAK,CACR,CAAC;QACH;QAEA,IAAIyR,MAAM,KAAKzf,SAAS,EAAE;UACxB;QACF;QAEA,MAAMkjB,MAAM,GAAGzD,MAAM,CAACzR,KAAK,CAAC;QAE5B,IAAIkV,MAAM,KAAKljB,SAAS,EAAE;UACxB;QACF;QAEA,IAAI,EAAEkjB,MAAM,YAAYpf,KAAK,CAAC,EAAE;UAC9B,MAAMgK,cAAc,CAClB,kDAAkD,EAClD;YAACE;UAAK,CACR,CAAC;QACH;QAEAyR,MAAM,CAACzR,KAAK,CAAC,GAAGkV,MAAM,CAACjmB,MAAM,CAACiS,MAAM,IAClC,CAAC5I,GAAG,CAACrI,IAAI,CAACyM,OAAO,IAAIvL,eAAe,CAACwF,EAAE,CAACsG,MAAM,CAACiE,MAAM,EAAExE,OAAO,CAAC,CACjE,CAAC;MACH,CAAC;MACD2Y,IAAIA,CAAC5D,MAAM,EAAEzR,KAAK,EAAE1H,GAAG,EAAE;QACvB;QACA;QACA,MAAMwH,cAAc,CAAC,uBAAuB,EAAE;UAACE;QAAK,CAAC,CAAC;MACxD,CAAC;MACDsV,EAAEA,CAAA,EAAG;QACH;QACA;QACA;QACA;MAAA;IAEJ,CAAC;IAED,MAAMzD,mBAAmB,GAAG;MAC1BkD,IAAI,EAAE,IAAI;MACVE,KAAK,EAAE,IAAI;MACXG,QAAQ,EAAE,IAAI;MACdtB,OAAO,EAAE,IAAI;MACbnkB,MAAM,EAAE;IACV,CAAC;;IAED;IACA;IACA;IACA,MAAM4lB,cAAc,GAAG;MACrBC,CAAC,EAAE,kBAAkB;MACrB,GAAG,EAAE,eAAe;MACpB,IAAI,EAAE;IACR,CAAC;;IAED;IACA,SAAS3L,wBAAwBA,CAAC/Q,GAAG,EAAE;MACrC,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAClCgG,IAAI,CAACC,SAAS,CAACjG,GAAG,EAAE,CAAC3E,GAAG,EAAEC,KAAK,KAAK;UAClCqhB,sBAAsB,CAACthB,GAAG,CAAC;UAC3B,OAAOC,KAAK;QACd,CAAC,CAAC;MACJ;IACF;IAEA,SAASqhB,sBAAsBA,CAACthB,GAAG,EAAE;MACnC,IAAIwH,KAAK;MACT,IAAI,OAAOxH,GAAG,KAAK,QAAQ,KAAKwH,KAAK,GAAGxH,GAAG,CAACwH,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;QAC/D,MAAMmE,cAAc,QAAA/P,MAAA,CAAQoE,GAAG,gBAAApE,MAAA,CAAawlB,cAAc,CAAC5Z,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;MACzE;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS+V,aAAaA,CAAC5Y,GAAG,EAAE0Y,QAAQ,EAAgB;MAAA,IAAdzV,OAAO,GAAA7H,SAAA,CAAA3D,MAAA,QAAA2D,SAAA,QAAAlC,SAAA,GAAAkC,SAAA,MAAG,CAAC,CAAC;MAChD,IAAIwhB,cAAc,GAAG,KAAK;MAE1B,KAAK,IAAIrlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmhB,QAAQ,CAACjhB,MAAM,EAAEF,CAAC,EAAE,EAAE;QACxC,MAAMslB,IAAI,GAAGtlB,CAAC,KAAKmhB,QAAQ,CAACjhB,MAAM,GAAG,CAAC;QACtC,IAAIqlB,OAAO,GAAGpE,QAAQ,CAACnhB,CAAC,CAAC;QAEzB,IAAI,CAACyE,WAAW,CAACgE,GAAG,CAAC,EAAE;UACrB,IAAIiD,OAAO,CAAC6V,QAAQ,EAAE;YACpB,OAAO5f,SAAS;UAClB;UAEA,MAAMX,KAAK,GAAGyO,cAAc,yBAAA/P,MAAA,CACF6lB,OAAO,oBAAA7lB,MAAA,CAAiB+I,GAAG,CACrD,CAAC;UACDzH,KAAK,CAACE,gBAAgB,GAAG,IAAI;UAC7B,MAAMF,KAAK;QACb;QAEA,IAAIyH,GAAG,YAAYhD,KAAK,EAAE;UACxB,IAAIiG,OAAO,CAAC4V,WAAW,EAAE;YACvB,OAAO,IAAI;UACb;UAEA,IAAIiE,OAAO,KAAK,GAAG,EAAE;YACnB,IAAIF,cAAc,EAAE;cAClB,MAAM5V,cAAc,CAAC,2CAA2C,CAAC;YACnE;YAEA,IAAI,CAAC/D,OAAO,CAACR,YAAY,IAAI,CAACQ,OAAO,CAACR,YAAY,CAAChL,MAAM,EAAE;cACzD,MAAMuP,cAAc,CAClB,iEAAiE,GACjE,OACF,CAAC;YACH;YAEA8V,OAAO,GAAG7Z,OAAO,CAACR,YAAY,CAAC,CAAC,CAAC;YACjCma,cAAc,GAAG,IAAI;UACvB,CAAC,MAAM,IAAIrnB,YAAY,CAACunB,OAAO,CAAC,EAAE;YAChCA,OAAO,GAAGC,QAAQ,CAACD,OAAO,CAAC;UAC7B,CAAC,MAAM;YACL,IAAI7Z,OAAO,CAAC6V,QAAQ,EAAE;cACpB,OAAO5f,SAAS;YAClB;YAEA,MAAM8N,cAAc,mDAAA/P,MAAA,CACgC6lB,OAAO,MAC3D,CAAC;UACH;UAEA,IAAID,IAAI,EAAE;YACRnE,QAAQ,CAACnhB,CAAC,CAAC,GAAGulB,OAAO,CAAC,CAAC;UACzB;UAEA,IAAI7Z,OAAO,CAAC6V,QAAQ,IAAIgE,OAAO,IAAI9c,GAAG,CAACvI,MAAM,EAAE;YAC7C,OAAOyB,SAAS;UAClB;UAEA,OAAO8G,GAAG,CAACvI,MAAM,GAAGqlB,OAAO,EAAE;YAC3B9c,GAAG,CAACwE,IAAI,CAAC,IAAI,CAAC;UAChB;UAEA,IAAI,CAACqY,IAAI,EAAE;YACT,IAAI7c,GAAG,CAACvI,MAAM,KAAKqlB,OAAO,EAAE;cAC1B9c,GAAG,CAACwE,IAAI,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,MAAM,IAAI,OAAOxE,GAAG,CAAC8c,OAAO,CAAC,KAAK,QAAQ,EAAE;cAC3C,MAAM9V,cAAc,CAClB,uBAAA/P,MAAA,CAAuByhB,QAAQ,CAACnhB,CAAC,GAAG,CAAC,CAAC,wBACtCyO,IAAI,CAACC,SAAS,CAACjG,GAAG,CAAC8c,OAAO,CAAC,CAC7B,CAAC;YACH;UACF;QACF,CAAC,MAAM;UACLH,sBAAsB,CAACG,OAAO,CAAC;UAE/B,IAAI,EAAEA,OAAO,IAAI9c,GAAG,CAAC,EAAE;YACrB,IAAIiD,OAAO,CAAC6V,QAAQ,EAAE;cACpB,OAAO5f,SAAS;YAClB;YAEA,IAAI,CAAC2jB,IAAI,EAAE;cACT7c,GAAG,CAAC8c,OAAO,CAAC,GAAG,CAAC,CAAC;YACnB;UACF;QACF;QAEA,IAAID,IAAI,EAAE;UACR,OAAO7c,GAAG;QACZ;QAEAA,GAAG,GAAGA,GAAG,CAAC8c,OAAO,CAAC;MACpB;;MAEA;IACF;IAACvhB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;;IC93EDtG,MAAM,CAACuG,MAAM,CAAC;MAACU,OAAO,EAACA,CAAA,KAAI/F;IAAO,CAAC,CAAC;IAAC,IAAI+B,eAAe;IAACjD,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAAC0C,eAAe,GAAC1C,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIkG,uBAAuB,EAACvG,MAAM,EAAC4G,cAAc;IAAC9G,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACwG,uBAAuBA,CAAClG,CAAC,EAAC;QAACkG,uBAAuB,GAAClG,CAAC;MAAA,CAAC;MAACL,MAAMA,CAACK,CAAC,EAAC;QAACL,MAAM,GAACK,CAAC;MAAA,CAAC;MAACuG,cAAcA,CAACvG,CAAC,EAAC;QAACuG,cAAc,GAACvG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAO3X,MAAMonB,OAAO,GAAG,EAAAC,oBAAA,GAAAvN,OAAO,CAAC,eAAe,CAAC,cAAAuN,oBAAA,uBAAxBA,oBAAA,CAA0BD,OAAO,KAAI,MAAME,WAAW,CAAC,EAAE;;IAEzE;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACe,MAAM5mB,OAAO,CAAC;MAC3BoT,WAAWA,CAAC5O,QAAQ,EAAEqiB,QAAQ,EAAE;QAC9B;QACA;QACA;QACA,IAAI,CAACpiB,MAAM,GAAG,CAAC,CAAC;QAChB;QACA,IAAI,CAAC0G,YAAY,GAAG,KAAK;QACzB;QACA,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB;QACA;QACA;QACA,IAAI,CAAC8C,SAAS,GAAG,IAAI;QACrB;QACA;QACA,IAAI,CAACnK,iBAAiB,GAAGC,SAAS;QAClC;QACA;QACA;QACA;QACA,IAAI,CAACnB,SAAS,GAAG,IAAI;QACrB,IAAI,CAACqlB,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACviB,QAAQ,CAAC;QAClD;QACA;QACA;QACA,IAAI,CAAC0H,SAAS,GAAG2a,QAAQ;MAC3B;MAEAzkB,eAAeA,CAACsH,GAAG,EAAE;QACnB,IAAIA,GAAG,KAAKtJ,MAAM,CAACsJ,GAAG,CAAC,EAAE;UACvB,MAAM9C,KAAK,CAAC,kCAAkC,CAAC;QACjD;QAEA,OAAO,IAAI,CAACkgB,WAAW,CAACpd,GAAG,CAAC;MAC9B;MAEA+J,WAAWA,CAAA,EAAG;QACZ,OAAO,IAAI,CAACtI,YAAY;MAC1B;MAEA6b,QAAQA,CAAA,EAAG;QACT,OAAO,IAAI,CAAChd,SAAS;MACvB;MAEA3I,QAAQA,CAAA,EAAG;QACT,OAAO,IAAI,CAACyL,SAAS;MACvB;;MAEA;MACA;MACAia,gBAAgBA,CAACviB,QAAQ,EAAE;QACzB;QACA,IAAIA,QAAQ,YAAYyF,QAAQ,EAAE;UAChC,IAAI,CAAC6C,SAAS,GAAG,KAAK;UACtB,IAAI,CAACrL,SAAS,GAAG+C,QAAQ;UACzB,IAAI,CAACuF,eAAe,CAAC,EAAE,CAAC;UAExB,OAAOL,GAAG,KAAK;YAACrH,MAAM,EAAE,CAAC,CAACmC,QAAQ,CAACd,IAAI,CAACgG,GAAG;UAAC,CAAC,CAAC;QAChD;;QAEA;QACA,IAAI3H,eAAe,CAACiQ,aAAa,CAACxN,QAAQ,CAAC,EAAE;UAC3C,IAAI,CAAC/C,SAAS,GAAG;YAAC2Q,GAAG,EAAE5N;UAAQ,CAAC;UAChC,IAAI,CAACuF,eAAe,CAAC,KAAK,CAAC;UAE3B,OAAOL,GAAG,KAAK;YAACrH,MAAM,EAAER,KAAK,CAACka,MAAM,CAACrS,GAAG,CAAC0I,GAAG,EAAE5N,QAAQ;UAAC,CAAC,CAAC;QAC3D;;QAEA;QACA;QACA;QACA,IAAI,CAACA,QAAQ,IAAIxF,MAAM,CAAC0E,IAAI,CAACc,QAAQ,EAAE,KAAK,CAAC,IAAI,CAACA,QAAQ,CAAC4N,GAAG,EAAE;UAC9D,IAAI,CAACtF,SAAS,GAAG,KAAK;UACtB,OAAOlH,cAAc;QACvB;;QAEA;QACA,IAAIc,KAAK,CAACC,OAAO,CAACnC,QAAQ,CAAC,IACvB3C,KAAK,CAAC2M,QAAQ,CAAChK,QAAQ,CAAC,IACxB,OAAOA,QAAQ,KAAK,SAAS,EAAE;UACjC,MAAM,IAAIoC,KAAK,sBAAAjG,MAAA,CAAsB6D,QAAQ,CAAE,CAAC;QAClD;QAEA,IAAI,CAAC/C,SAAS,GAAGI,KAAK,CAACC,KAAK,CAAC0C,QAAQ,CAAC;QAEtC,OAAOe,uBAAuB,CAACf,QAAQ,EAAE,IAAI,EAAE;UAAC0G,MAAM,EAAE;QAAI,CAAC,CAAC;MAChE;;MAEA;MACA;MACAzK,SAASA,CAAA,EAAG;QACV,OAAOL,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC6D,MAAM,CAAC;MACjC;MAEAsF,eAAeA,CAACpK,IAAI,EAAE;QACpB,IAAI,CAAC8E,MAAM,CAAC9E,IAAI,CAAC,GAAG,IAAI;MAC1B;IACF;IAEA;IACAoC,eAAe,CAACwF,EAAE,GAAG;MACnB;MACAC,KAAKA,CAACnI,CAAC,EAAE;QACP,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;UACzB,OAAO,CAAC;QACV;QAEA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;UACzB,OAAO,CAAC;QACV;QAEA,IAAI,OAAOA,CAAC,KAAK,SAAS,EAAE;UAC1B,OAAO,CAAC;QACV;QAEA,IAAIqH,KAAK,CAACC,OAAO,CAACtH,CAAC,CAAC,EAAE;UACpB,OAAO,CAAC;QACV;QAEA,IAAIA,CAAC,KAAK,IAAI,EAAE;UACd,OAAO,EAAE;QACX;;QAEA;QACA,IAAIA,CAAC,YAAY4H,MAAM,EAAE;UACvB,OAAO,EAAE;QACX;QAEA,IAAI,OAAO5H,CAAC,KAAK,UAAU,EAAE;UAC3B,OAAO,EAAE;QACX;QAEA,IAAIA,CAAC,YAAYglB,IAAI,EAAE;UACrB,OAAO,CAAC;QACV;QAEA,IAAIxiB,KAAK,CAAC2M,QAAQ,CAACnP,CAAC,CAAC,EAAE;UACrB,OAAO,CAAC;QACV;QAEA,IAAIA,CAAC,YAAYsb,OAAO,CAACC,QAAQ,EAAE;UACjC,OAAO,CAAC;QACV;QAEA,IAAIvb,CAAC,YAAYqnB,OAAO,EAAE;UACxB,OAAO,CAAC;QACV;;QAEA;QACA,OAAO,CAAC;;QAER;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC;MAED;MACA7Y,MAAMA,CAACjF,CAAC,EAAEC,CAAC,EAAE;QACX,OAAOhH,KAAK,CAACka,MAAM,CAACnT,CAAC,EAAEC,CAAC,EAAE;UAACoe,iBAAiB,EAAE;QAAI,CAAC,CAAC;MACtD,CAAC;MAED;MACA;MACAC,UAAUA,CAACC,CAAC,EAAE;QACZ;QACA;QACA;QACA;QACA,OAAO,CACL,CAAC,CAAC;QAAG;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC,CAAC;QAAG;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC,CAAC;QAAG;QACL,GAAG;QAAE;QACL,CAAC;QAAI;QACL,GAAG;QAAE;QACL,CAAC;QAAI;QACL,CAAC;QAAI;QACL,CAAC,CAAI;QAAA,CACN,CAACA,CAAC,CAAC;MACN,CAAC;MAED;MACA;MACA;MACA;MACArX,IAAIA,CAAClH,CAAC,EAAEC,CAAC,EAAE;QACT,IAAID,CAAC,KAAKhG,SAAS,EAAE;UACnB,OAAOiG,CAAC,KAAKjG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC;QAEA,IAAIiG,CAAC,KAAKjG,SAAS,EAAE;UACnB,OAAO,CAAC;QACV;QAEA,IAAIwkB,EAAE,GAAGrlB,eAAe,CAACwF,EAAE,CAACC,KAAK,CAACoB,CAAC,CAAC;QACpC,IAAIye,EAAE,GAAGtlB,eAAe,CAACwF,EAAE,CAACC,KAAK,CAACqB,CAAC,CAAC;QAEpC,MAAMye,EAAE,GAAGvlB,eAAe,CAACwF,EAAE,CAAC2f,UAAU,CAACE,EAAE,CAAC;QAC5C,MAAMG,EAAE,GAAGxlB,eAAe,CAACwF,EAAE,CAAC2f,UAAU,CAACG,EAAE,CAAC;QAE5C,IAAIC,EAAE,KAAKC,EAAE,EAAE;UACb,OAAOD,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QACzB;;QAEA;QACA;QACA,IAAIH,EAAE,KAAKC,EAAE,EAAE;UACb,MAAMzgB,KAAK,CAAC,qCAAqC,CAAC;QACpD;QAEA,IAAIwgB,EAAE,KAAK,CAAC,EAAE;UAAE;UACd;UACAA,EAAE,GAAGC,EAAE,GAAG,CAAC;UACXze,CAAC,GAAGA,CAAC,CAAC4e,WAAW,CAAC,CAAC;UACnB3e,CAAC,GAAGA,CAAC,CAAC2e,WAAW,CAAC,CAAC;QACrB;QAEA,IAAIJ,EAAE,KAAK,CAAC,EAAE;UAAE;UACd;UACAA,EAAE,GAAGC,EAAE,GAAG,CAAC;UACXze,CAAC,GAAG6e,KAAK,CAAC7e,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC8e,OAAO,CAAC,CAAC;UAC9B7e,CAAC,GAAG4e,KAAK,CAAC5e,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC6e,OAAO,CAAC,CAAC;QAChC;QAEA,IAAIN,EAAE,KAAK,CAAC,EAAE;UAAE;UACd,IAAIxe,CAAC,YAAY8d,OAAO,EAAE;YACxB,OAAO9d,CAAC,CAAC+e,KAAK,CAAC9e,CAAC,CAAC,CAAC+e,QAAQ,CAAC,CAAC;UAC9B,CAAC,MAAM;YACL,OAAOhf,CAAC,GAAGC,CAAC;UACd;QACF;QAEA,IAAIwe,EAAE,KAAK,CAAC;UAAE;UACZ,OAAOze,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAG,CAAC;QAErC,IAAIue,EAAE,KAAK,CAAC,EAAE;UAAE;UACd;UACA,MAAMS,OAAO,GAAG/V,MAAM,IAAI;YACxB,MAAMzP,MAAM,GAAG,EAAE;YAEjBjC,MAAM,CAACQ,IAAI,CAACkR,MAAM,CAAC,CAACtO,OAAO,CAACuB,GAAG,IAAI;cACjC1C,MAAM,CAAC6L,IAAI,CAACnJ,GAAG,EAAE+M,MAAM,CAAC/M,GAAG,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,OAAO1C,MAAM;UACf,CAAC;UAED,OAAON,eAAe,CAACwF,EAAE,CAACuI,IAAI,CAAC+X,OAAO,CAACjf,CAAC,CAAC,EAAEif,OAAO,CAAChf,CAAC,CAAC,CAAC;QACxD;QAEA,IAAIue,EAAE,KAAK,CAAC,EAAE;UAAE;UACd,KAAK,IAAInmB,CAAC,GAAG,CAAC,GAAIA,CAAC,EAAE,EAAE;YACrB,IAAIA,CAAC,KAAK2H,CAAC,CAACzH,MAAM,EAAE;cAClB,OAAOF,CAAC,KAAK4H,CAAC,CAAC1H,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;YAChC;YAEA,IAAIF,CAAC,KAAK4H,CAAC,CAAC1H,MAAM,EAAE;cAClB,OAAO,CAAC;YACV;YAEA,MAAMkO,CAAC,GAAGtN,eAAe,CAACwF,EAAE,CAACuI,IAAI,CAAClH,CAAC,CAAC3H,CAAC,CAAC,EAAE4H,CAAC,CAAC5H,CAAC,CAAC,CAAC;YAC7C,IAAIoO,CAAC,KAAK,CAAC,EAAE;cACX,OAAOA,CAAC;YACV;UACF;QACF;QAEA,IAAI+X,EAAE,KAAK,CAAC,EAAE;UAAE;UACd;UACA;UACA,IAAIxe,CAAC,CAACzH,MAAM,KAAK0H,CAAC,CAAC1H,MAAM,EAAE;YACzB,OAAOyH,CAAC,CAACzH,MAAM,GAAG0H,CAAC,CAAC1H,MAAM;UAC5B;UAEA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2H,CAAC,CAACzH,MAAM,EAAEF,CAAC,EAAE,EAAE;YACjC,IAAI2H,CAAC,CAAC3H,CAAC,CAAC,GAAG4H,CAAC,CAAC5H,CAAC,CAAC,EAAE;cACf,OAAO,CAAC,CAAC;YACX;YAEA,IAAI2H,CAAC,CAAC3H,CAAC,CAAC,GAAG4H,CAAC,CAAC5H,CAAC,CAAC,EAAE;cACf,OAAO,CAAC;YACV;UACF;UAEA,OAAO,CAAC;QACV;QAEA,IAAImmB,EAAE,KAAK,CAAC,EAAE;UAAE;UACd,IAAIxe,CAAC,EAAE;YACL,OAAOC,CAAC,GAAG,CAAC,GAAG,CAAC;UAClB;UAEA,OAAOA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QACnB;QAEA,IAAIue,EAAE,KAAK,EAAE;UAAE;UACb,OAAO,CAAC;QAEV,IAAIA,EAAE,KAAK,EAAE;UAAE;UACb,MAAMxgB,KAAK,CAAC,6CAA6C,CAAC,CAAC,CAAC;;QAE9D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIwgB,EAAE,KAAK,EAAE;UAAE;UACb,MAAMxgB,KAAK,CAAC,0CAA0C,CAAC,CAAC,CAAC;;QAE3D,MAAMA,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF,CAAC;IAAC3B,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICtWF,IAAI0iB,gBAAgB;IAAChpB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAACyoB,gBAAgB,GAACzoB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIW,OAAO;IAAClB,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAACW,OAAO,GAACX,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIwE,MAAM;IAAC/E,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACgH,OAAOA,CAAC1G,CAAC,EAAC;QAACwE,MAAM,GAACxE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAI1RyC,eAAe,GAAG+lB,gBAAgB;IAClCvoB,SAAS,GAAG;MACRwC,eAAe,EAAE+lB,gBAAgB;MACjC9nB,OAAO;MACP6D;IACJ,CAAC;IAACoB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;ACTFtG,MAAM,CAACuG,MAAM,CAAC;EAACU,OAAO,EAACA,CAAA,KAAIuR;AAAa,CAAC,CAAC;AAC3B,MAAMA,aAAa,CAAC,E;;;;;;;;;;;;;;ICDnCxY,MAAM,CAACuG,MAAM,CAAC;MAACU,OAAO,EAACA,CAAA,KAAIlC;IAAM,CAAC,CAAC;IAAC,IAAIyB,iBAAiB,EAACE,sBAAsB,EAACC,sBAAsB,EAACzG,MAAM,EAACE,gBAAgB,EAACyG,kBAAkB,EAACG,oBAAoB;IAAChH,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;MAACuG,iBAAiBA,CAACjG,CAAC,EAAC;QAACiG,iBAAiB,GAACjG,CAAC;MAAA,CAAC;MAACmG,sBAAsBA,CAACnG,CAAC,EAAC;QAACmG,sBAAsB,GAACnG,CAAC;MAAA,CAAC;MAACoG,sBAAsBA,CAACpG,CAAC,EAAC;QAACoG,sBAAsB,GAACpG,CAAC;MAAA,CAAC;MAACL,MAAMA,CAACK,CAAC,EAAC;QAACL,MAAM,GAACK,CAAC;MAAA,CAAC;MAACH,gBAAgBA,CAACG,CAAC,EAAC;QAACH,gBAAgB,GAACG,CAAC;MAAA,CAAC;MAACsG,kBAAkBA,CAACtG,CAAC,EAAC;QAACsG,kBAAkB,GAACtG,CAAC;MAAA,CAAC;MAACyG,oBAAoBA,CAACzG,CAAC,EAAC;QAACyG,oBAAoB,GAACzG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAuB9hB,MAAMuE,MAAM,CAAC;MAC1BuP,WAAWA,CAAC2U,IAAI,EAAE;QAChB,IAAI,CAACC,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,aAAa,GAAG,IAAI;QAEzB,MAAMC,WAAW,GAAGA,CAACvoB,IAAI,EAAEwoB,SAAS,KAAK;UACvC,IAAI,CAACxoB,IAAI,EAAE;YACT,MAAMiH,KAAK,CAAC,6BAA6B,CAAC;UAC5C;UAEA,IAAIjH,IAAI,CAACyoB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC1B,MAAMxhB,KAAK,0BAAAjG,MAAA,CAA0BhB,IAAI,CAAE,CAAC;UAC9C;UAEA,IAAI,CAACqoB,cAAc,CAAC9Z,IAAI,CAAC;YACvBia,SAAS;YACTE,MAAM,EAAE1iB,kBAAkB,CAAChG,IAAI,EAAE;cAAC4Q,OAAO,EAAE;YAAI,CAAC,CAAC;YACjD5Q;UACF,CAAC,CAAC;QACJ,CAAC;QAED,IAAIooB,IAAI,YAAYrhB,KAAK,EAAE;UACzBqhB,IAAI,CAACvkB,OAAO,CAAC8J,OAAO,IAAI;YACtB,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;cAC/B4a,WAAW,CAAC5a,OAAO,EAAE,IAAI,CAAC;YAC5B,CAAC,MAAM;cACL4a,WAAW,CAAC5a,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;YAChD;UACF,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI,OAAOya,IAAI,KAAK,QAAQ,EAAE;UACnC3nB,MAAM,CAACQ,IAAI,CAACmnB,IAAI,CAAC,CAACvkB,OAAO,CAACuB,GAAG,IAAI;YAC/BmjB,WAAW,CAACnjB,GAAG,EAAEgjB,IAAI,CAAChjB,GAAG,CAAC,IAAI,CAAC,CAAC;UAClC,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI,OAAOgjB,IAAI,KAAK,UAAU,EAAE;UACrC,IAAI,CAACE,aAAa,GAAGF,IAAI;QAC3B,CAAC,MAAM;UACL,MAAMnhB,KAAK,4BAAAjG,MAAA,CAA4B+O,IAAI,CAACC,SAAS,CAACoY,IAAI,CAAC,CAAE,CAAC;QAChE;;QAEA;QACA,IAAI,IAAI,CAACE,aAAa,EAAE;UACtB;QACF;;QAEA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC/nB,kBAAkB,EAAE;UAC3B,MAAMsE,QAAQ,GAAG,CAAC,CAAC;UAEnB,IAAI,CAACwjB,cAAc,CAACxkB,OAAO,CAACukB,IAAI,IAAI;YAClCvjB,QAAQ,CAACujB,IAAI,CAACpoB,IAAI,CAAC,GAAG,CAAC;UACzB,CAAC,CAAC;UAEF,IAAI,CAACmE,8BAA8B,GAAG,IAAIvE,SAAS,CAACS,OAAO,CAACwE,QAAQ,CAAC;QACvE;QAEA,IAAI,CAAC8jB,cAAc,GAAGC,kBAAkB,CACtC,IAAI,CAACP,cAAc,CAACtoB,GAAG,CAAC,CAACqoB,IAAI,EAAE9mB,CAAC,KAAK,IAAI,CAACunB,mBAAmB,CAACvnB,CAAC,CAAC,CAClE,CAAC;MACH;MAEAgY,aAAaA,CAACtM,OAAO,EAAE;QACrB;QACA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAACqb,cAAc,CAAC7mB,MAAM,IAAI,CAACwL,OAAO,IAAI,CAACA,OAAO,CAACqJ,SAAS,EAAE;UAChE,OAAO,IAAI,CAACyS,kBAAkB,CAAC,CAAC;QAClC;QAEA,MAAMzS,SAAS,GAAGrJ,OAAO,CAACqJ,SAAS;;QAEnC;QACA,OAAO,CAACpN,CAAC,EAAEC,CAAC,KAAK;UACf,IAAI,CAACmN,SAAS,CAAC8E,GAAG,CAAClS,CAAC,CAACwJ,GAAG,CAAC,EAAE;YACzB,MAAMxL,KAAK,yBAAAjG,MAAA,CAAyBiI,CAAC,CAACwJ,GAAG,CAAE,CAAC;UAC9C;UAEA,IAAI,CAAC4D,SAAS,CAAC8E,GAAG,CAACjS,CAAC,CAACuJ,GAAG,CAAC,EAAE;YACzB,MAAMxL,KAAK,yBAAAjG,MAAA,CAAyBkI,CAAC,CAACuJ,GAAG,CAAE,CAAC;UAC9C;UAEA,OAAO4D,SAAS,CAAC0C,GAAG,CAAC9P,CAAC,CAACwJ,GAAG,CAAC,GAAG4D,SAAS,CAAC0C,GAAG,CAAC7P,CAAC,CAACuJ,GAAG,CAAC;QACpD,CAAC;MACH;;MAEA;MACA;MACA;MACAsW,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;QACvB,IAAID,IAAI,CAACxnB,MAAM,KAAK,IAAI,CAAC6mB,cAAc,CAAC7mB,MAAM,IAC1CynB,IAAI,CAACznB,MAAM,KAAK,IAAI,CAAC6mB,cAAc,CAAC7mB,MAAM,EAAE;UAC9C,MAAMyF,KAAK,CAAC,sBAAsB,CAAC;QACrC;QAEA,OAAO,IAAI,CAAC0hB,cAAc,CAACK,IAAI,EAAEC,IAAI,CAAC;MACxC;;MAEA;MACA;MACAC,oBAAoBA,CAACnf,GAAG,EAAEof,EAAE,EAAE;QAC5B,IAAI,IAAI,CAACd,cAAc,CAAC7mB,MAAM,KAAK,CAAC,EAAE;UACpC,MAAM,IAAIyF,KAAK,CAAC,qCAAqC,CAAC;QACxD;QAEA,MAAMmiB,eAAe,GAAGhG,OAAO,OAAApiB,MAAA,CAAOoiB,OAAO,CAAChjB,IAAI,CAAC,GAAG,CAAC,MAAG;QAE1D,IAAIipB,UAAU,GAAG,IAAI;;QAErB;QACA,MAAMC,oBAAoB,GAAG,IAAI,CAACjB,cAAc,CAACtoB,GAAG,CAACqoB,IAAI,IAAI;UAC3D;UACA;UACA,IAAI3a,QAAQ,GAAG3H,sBAAsB,CAACsiB,IAAI,CAACM,MAAM,CAAC3e,GAAG,CAAC,EAAE,IAAI,CAAC;;UAE7D;UACA;UACA,IAAI,CAAC0D,QAAQ,CAACjM,MAAM,EAAE;YACpBiM,QAAQ,GAAG,CAAC;cAAEpI,KAAK,EAAE,KAAK;YAAE,CAAC,CAAC;UAChC;UAEA,MAAMsI,OAAO,GAAGlN,MAAM,CAAC4Z,MAAM,CAAC,IAAI,CAAC;UACnC,IAAIkP,SAAS,GAAG,KAAK;UAErB9b,QAAQ,CAAC5J,OAAO,CAACwI,MAAM,IAAI;YACzB,IAAI,CAACA,MAAM,CAACG,YAAY,EAAE;cACxB;cACA;cACA;cACA,IAAIiB,QAAQ,CAACjM,MAAM,GAAG,CAAC,EAAE;gBACvB,MAAMyF,KAAK,CAAC,sCAAsC,CAAC;cACrD;cAEA0G,OAAO,CAAC,EAAE,CAAC,GAAGtB,MAAM,CAAChH,KAAK;cAC1B;YACF;YAEAkkB,SAAS,GAAG,IAAI;YAEhB,MAAMvpB,IAAI,GAAGopB,eAAe,CAAC/c,MAAM,CAACG,YAAY,CAAC;YAEjD,IAAInN,MAAM,CAAC0E,IAAI,CAAC4J,OAAO,EAAE3N,IAAI,CAAC,EAAE;cAC9B,MAAMiH,KAAK,oBAAAjG,MAAA,CAAoBhB,IAAI,CAAE,CAAC;YACxC;YAEA2N,OAAO,CAAC3N,IAAI,CAAC,GAAGqM,MAAM,CAAChH,KAAK;;YAE5B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAIgkB,UAAU,IAAI,CAAChqB,MAAM,CAAC0E,IAAI,CAACslB,UAAU,EAAErpB,IAAI,CAAC,EAAE;cAChD,MAAMiH,KAAK,CAAC,8BAA8B,CAAC;YAC7C;UACF,CAAC,CAAC;UAEF,IAAIoiB,UAAU,EAAE;YACd;YACA;YACA,IAAI,CAAChqB,MAAM,CAAC0E,IAAI,CAAC4J,OAAO,EAAE,EAAE,CAAC,IACzBlN,MAAM,CAACQ,IAAI,CAACooB,UAAU,CAAC,CAAC7nB,MAAM,KAAKf,MAAM,CAACQ,IAAI,CAAC0M,OAAO,CAAC,CAACnM,MAAM,EAAE;cAClE,MAAMyF,KAAK,CAAC,+BAA+B,CAAC;YAC9C;UACF,CAAC,MAAM,IAAIsiB,SAAS,EAAE;YACpBF,UAAU,GAAG,CAAC,CAAC;YAEf5oB,MAAM,CAACQ,IAAI,CAAC0M,OAAO,CAAC,CAAC9J,OAAO,CAAC7D,IAAI,IAAI;cACnCqpB,UAAU,CAACrpB,IAAI,CAAC,GAAG,IAAI;YACzB,CAAC,CAAC;UACJ;UAEA,OAAO2N,OAAO;QAChB,CAAC,CAAC;QAEF,IAAI,CAAC0b,UAAU,EAAE;UACf;UACA,MAAMG,OAAO,GAAGF,oBAAoB,CAACvpB,GAAG,CAAC+lB,MAAM,IAAI;YACjD,IAAI,CAACzmB,MAAM,CAAC0E,IAAI,CAAC+hB,MAAM,EAAE,EAAE,CAAC,EAAE;cAC5B,MAAM7e,KAAK,CAAC,4BAA4B,CAAC;YAC3C;YAEA,OAAO6e,MAAM,CAAC,EAAE,CAAC;UACnB,CAAC,CAAC;UAEFqD,EAAE,CAACK,OAAO,CAAC;UAEX;QACF;QAEA/oB,MAAM,CAACQ,IAAI,CAACooB,UAAU,CAAC,CAACxlB,OAAO,CAAC7D,IAAI,IAAI;UACtC,MAAMoF,GAAG,GAAGkkB,oBAAoB,CAACvpB,GAAG,CAAC+lB,MAAM,IAAI;YAC7C,IAAIzmB,MAAM,CAAC0E,IAAI,CAAC+hB,MAAM,EAAE,EAAE,CAAC,EAAE;cAC3B,OAAOA,MAAM,CAAC,EAAE,CAAC;YACnB;YAEA,IAAI,CAACzmB,MAAM,CAAC0E,IAAI,CAAC+hB,MAAM,EAAE9lB,IAAI,CAAC,EAAE;cAC9B,MAAMiH,KAAK,CAAC,eAAe,CAAC;YAC9B;YAEA,OAAO6e,MAAM,CAAC9lB,IAAI,CAAC;UACrB,CAAC,CAAC;UAEFmpB,EAAE,CAAC/jB,GAAG,CAAC;QACT,CAAC,CAAC;MACJ;;MAEA;MACA;MACA0jB,kBAAkBA,CAAA,EAAG;QACnB,IAAI,IAAI,CAACR,aAAa,EAAE;UACtB,OAAO,IAAI,CAACA,aAAa;QAC3B;;QAEA;QACA;QACA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC7mB,MAAM,EAAE;UAC/B,OAAO,CAACioB,IAAI,EAAEC,IAAI,KAAK,CAAC;QAC1B;QAEA,OAAO,CAACD,IAAI,EAAEC,IAAI,KAAK;UACrB,MAAMV,IAAI,GAAG,IAAI,CAACW,iBAAiB,CAACF,IAAI,CAAC;UACzC,MAAMR,IAAI,GAAG,IAAI,CAACU,iBAAiB,CAACD,IAAI,CAAC;UACzC,OAAO,IAAI,CAACX,YAAY,CAACC,IAAI,EAAEC,IAAI,CAAC;QACtC,CAAC;MACH;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAU,iBAAiBA,CAAC5f,GAAG,EAAE;QACrB,IAAI6f,MAAM,GAAG,IAAI;QAEjB,IAAI,CAACV,oBAAoB,CAACnf,GAAG,EAAE3E,GAAG,IAAI;UACpC,IAAIwkB,MAAM,KAAK,IAAI,EAAE;YACnBA,MAAM,GAAGxkB,GAAG;YACZ;UACF;UAEA,IAAI,IAAI,CAAC2jB,YAAY,CAAC3jB,GAAG,EAAEwkB,MAAM,CAAC,GAAG,CAAC,EAAE;YACtCA,MAAM,GAAGxkB,GAAG;UACd;QACF,CAAC,CAAC;QAEF,OAAOwkB,MAAM;MACf;MAEA9oB,SAASA,CAAA,EAAG;QACV,OAAO,IAAI,CAACunB,cAAc,CAACtoB,GAAG,CAACI,IAAI,IAAIA,IAAI,CAACH,IAAI,CAAC;MACnD;;MAEA;MACA;MACA6oB,mBAAmBA,CAACvnB,CAAC,EAAE;QACrB,MAAMuoB,MAAM,GAAG,CAAC,IAAI,CAACxB,cAAc,CAAC/mB,CAAC,CAAC,CAACknB,SAAS;QAEhD,OAAO,CAACQ,IAAI,EAAEC,IAAI,KAAK;UACrB,MAAMa,OAAO,GAAG1nB,eAAe,CAACwF,EAAE,CAACuI,IAAI,CAAC6Y,IAAI,CAAC1nB,CAAC,CAAC,EAAE2nB,IAAI,CAAC3nB,CAAC,CAAC,CAAC;UACzD,OAAOuoB,MAAM,GAAG,CAACC,OAAO,GAAGA,OAAO;QACpC,CAAC;MACH;IACF;IAEA;IACA;IACA;IACA;IACA,SAASlB,kBAAkBA,CAACmB,eAAe,EAAE;MAC3C,OAAO,CAAC9gB,CAAC,EAAEC,CAAC,KAAK;QACf,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyoB,eAAe,CAACvoB,MAAM,EAAE,EAAEF,CAAC,EAAE;UAC/C,MAAMwoB,OAAO,GAAGC,eAAe,CAACzoB,CAAC,CAAC,CAAC2H,CAAC,EAAEC,CAAC,CAAC;UACxC,IAAI4gB,OAAO,KAAK,CAAC,EAAE;YACjB,OAAOA,OAAO;UAChB;QACF;QAEA,OAAO,CAAC;MACV,CAAC;IACH;IAACxkB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/minimongo.js", "sourcesContent": ["import './minimongo_common.js';\nimport {\n  hasOwn,\n  isNumericKey,\n  isOperatorObject,\n  pathsToTree,\n  projectionDetails,\n} from './common.js';\n\nMinimongo._pathsElidingNumericKeys = paths => paths.map(path =>\n  path.split('.').filter(part => !isNumericKey(part)).join('.')\n);\n\n// Returns true if the modifier applied to some document may change the result\n// of matching the document by selector\n// The modifier is always in a form of Object:\n//  - $set\n//    - 'a.b.22.z': value\n//    - 'foo.bar': 42\n//  - $unset\n//    - 'abc.d': 1\nMinimongo.Matcher.prototype.affectedByModifier = function(modifier) {\n  // safe check for $set/$unset being objects\n  modifier = Object.assign({$set: {}, $unset: {}}, modifier);\n\n  const meaningfulPaths = this._getPaths();\n  const modifiedPaths = [].concat(\n    Object.keys(modifier.$set),\n    Object.keys(modifier.$unset)\n  );\n\n  return modifiedPaths.some(path => {\n    const mod = path.split('.');\n\n    return meaningfulPaths.some(meaningfulPath => {\n      const sel = meaningfulPath.split('.');\n\n      let i = 0, j = 0;\n\n      while (i < sel.length && j < mod.length) {\n        if (isNumericKey(sel[i]) && isNumericKey(mod[j])) {\n          // foo.4.bar selector affected by foo.4 modifier\n          // foo.3.bar selector unaffected by foo.4 modifier\n          if (sel[i] === mod[j]) {\n            i++;\n            j++;\n          } else {\n            return false;\n          }\n        } else if (isNumericKey(sel[i])) {\n          // foo.4.bar selector unaffected by foo.bar modifier\n          return false;\n        } else if (isNumericKey(mod[j])) {\n          j++;\n        } else if (sel[i] === mod[j]) {\n          i++;\n          j++;\n        } else {\n          return false;\n        }\n      }\n\n      // One is a prefix of another, taking numeric fields into account\n      return true;\n    });\n  });\n};\n\n// @param modifier - Object: MongoDB-styled modifier with `$set`s and `$unsets`\n//                           only. (assumed to come from oplog)\n// @returns - Boolean: if after applying the modifier, selector can start\n//                     accepting the modified value.\n// NOTE: assumes that document affected by modifier didn't match this Matcher\n// before, so if modifier can't convince selector in a positive change it would\n// stay 'false'.\n// Currently doesn't support $-operators and numeric indices precisely.\nMinimongo.Matcher.prototype.canBecomeTrueByModifier = function(modifier) {\n  if (!this.affectedByModifier(modifier)) {\n    return false;\n  }\n\n  if (!this.isSimple()) {\n    return true;\n  }\n\n  modifier = Object.assign({$set: {}, $unset: {}}, modifier);\n\n  const modifierPaths = [].concat(\n    Object.keys(modifier.$set),\n    Object.keys(modifier.$unset)\n  );\n\n  if (this._getPaths().some(pathHasNumericKeys) ||\n      modifierPaths.some(pathHasNumericKeys)) {\n    return true;\n  }\n\n  // check if there is a $set or $unset that indicates something is an\n  // object rather than a scalar in the actual object where we saw $-operator\n  // NOTE: it is correct since we allow only scalars in $-operators\n  // Example: for selector {'a.b': {$gt: 5}} the modifier {'a.b.c':7} would\n  // definitely set the result to false as 'a.b' appears to be an object.\n  const expectedScalarIsObject = Object.keys(this._selector).some(path => {\n    if (!isOperatorObject(this._selector[path])) {\n      return false;\n    }\n\n    return modifierPaths.some(modifierPath =>\n      modifierPath.startsWith(`${path}.`)\n    );\n  });\n\n  if (expectedScalarIsObject) {\n    return false;\n  }\n\n  // See if we can apply the modifier on the ideally matching object. If it\n  // still matches the selector, then the modifier could have turned the real\n  // object in the database into something matching.\n  const matchingDocument = EJSON.clone(this.matchingDocument());\n\n  // The selector is too complex, anything can happen.\n  if (matchingDocument === null) {\n    return true;\n  }\n\n  try {\n    LocalCollection._modify(matchingDocument, modifier);\n  } catch (error) {\n    // Couldn't set a property on a field which is a scalar or null in the\n    // selector.\n    // Example:\n    // real document: { 'a.b': 3 }\n    // selector: { 'a': 12 }\n    // converted selector (ideal document): { 'a': 12 }\n    // modifier: { $set: { 'a.b': 4 } }\n    // We don't know what real document was like but from the error raised by\n    // $set on a scalar field we can reason that the structure of real document\n    // is completely different.\n    if (error.name === 'MinimongoError' && error.setPropertyError) {\n      return false;\n    }\n\n    throw error;\n  }\n\n  return this.documentMatches(matchingDocument).result;\n};\n\n// Knows how to combine a mongo selector and a fields projection to a new fields\n// projection taking into account active fields from the passed selector.\n// @returns Object - projection object (same as fields option of mongo cursor)\nMinimongo.Matcher.prototype.combineIntoProjection = function(projection) {\n  const selectorPaths = Minimongo._pathsElidingNumericKeys(this._getPaths());\n\n  // Special case for $where operator in the selector - projection should depend\n  // on all fields of the document. getSelectorPaths returns a list of paths\n  // selector depends on. If one of the paths is '' (empty string) representing\n  // the root or the whole document, complete projection should be returned.\n  if (selectorPaths.includes('')) {\n    return {};\n  }\n\n  return combineImportantPathsIntoProjection(selectorPaths, projection);\n};\n\n// Returns an object that would match the selector if possible or null if the\n// selector is too complex for us to analyze\n// { 'a.b': { ans: 42 }, 'foo.bar': null, 'foo.baz': \"something\" }\n// => { a: { b: { ans: 42 } }, foo: { bar: null, baz: \"something\" } }\nMinimongo.Matcher.prototype.matchingDocument = function() {\n  // check if it was computed before\n  if (this._matchingDocument !== undefined) {\n    return this._matchingDocument;\n  }\n\n  // If the analysis of this selector is too hard for our implementation\n  // fallback to \"YES\"\n  let fallback = false;\n\n  this._matchingDocument = pathsToTree(\n    this._getPaths(),\n    path => {\n      const valueSelector = this._selector[path];\n\n      if (isOperatorObject(valueSelector)) {\n        // if there is a strict equality, there is a good\n        // chance we can use one of those as \"matching\"\n        // dummy value\n        if (valueSelector.$eq) {\n          return valueSelector.$eq;\n        }\n\n        if (valueSelector.$in) {\n          const matcher = new Minimongo.Matcher({placeholder: valueSelector});\n\n          // Return anything from $in that matches the whole selector for this\n          // path. If nothing matches, returns `undefined` as nothing can make\n          // this selector into `true`.\n          return valueSelector.$in.find(placeholder =>\n            matcher.documentMatches({placeholder}).result\n          );\n        }\n\n        if (onlyContainsKeys(valueSelector, ['$gt', '$gte', '$lt', '$lte'])) {\n          let lowerBound = -Infinity;\n          let upperBound = Infinity;\n\n          ['$lte', '$lt'].forEach(op => {\n            if (hasOwn.call(valueSelector, op) &&\n                valueSelector[op] < upperBound) {\n              upperBound = valueSelector[op];\n            }\n          });\n\n          ['$gte', '$gt'].forEach(op => {\n            if (hasOwn.call(valueSelector, op) &&\n                valueSelector[op] > lowerBound) {\n              lowerBound = valueSelector[op];\n            }\n          });\n\n          const middle = (lowerBound + upperBound) / 2;\n          const matcher = new Minimongo.Matcher({placeholder: valueSelector});\n\n          if (!matcher.documentMatches({placeholder: middle}).result &&\n              (middle === lowerBound || middle === upperBound)) {\n            fallback = true;\n          }\n\n          return middle;\n        }\n\n        if (onlyContainsKeys(valueSelector, ['$nin', '$ne'])) {\n          // Since this._isSimple makes sure $nin and $ne are not combined with\n          // objects or arrays, we can confidently return an empty object as it\n          // never matches any scalar.\n          return {};\n        }\n\n        fallback = true;\n      }\n\n      return this._selector[path];\n    },\n    x => x);\n\n  if (fallback) {\n    this._matchingDocument = null;\n  }\n\n  return this._matchingDocument;\n};\n\n// Minimongo.Sorter gets a similar method, which delegates to a Matcher it made\n// for this exact purpose.\nMinimongo.Sorter.prototype.affectedByModifier = function(modifier) {\n  return this._selectorForAffectedByModifier.affectedByModifier(modifier);\n};\n\nMinimongo.Sorter.prototype.combineIntoProjection = function(projection) {\n  return combineImportantPathsIntoProjection(\n    Minimongo._pathsElidingNumericKeys(this._getPaths()),\n    projection\n  );\n};\n\nfunction combineImportantPathsIntoProjection(paths, projection) {\n  const details = projectionDetails(projection);\n\n  // merge the paths to include\n  const tree = pathsToTree(\n    paths,\n    path => true,\n    (node, path, fullPath) => true,\n    details.tree\n  );\n  const mergedProjection = treeToPaths(tree);\n\n  if (details.including) {\n    // both selector and projection are pointing on fields to include\n    // so we can just return the merged tree\n    return mergedProjection;\n  }\n\n  // selector is pointing at fields to include\n  // projection is pointing at fields to exclude\n  // make sure we don't exclude important paths\n  const mergedExclProjection = {};\n\n  Object.keys(mergedProjection).forEach(path => {\n    if (!mergedProjection[path]) {\n      mergedExclProjection[path] = false;\n    }\n  });\n\n  return mergedExclProjection;\n}\n\nfunction getPaths(selector) {\n  return Object.keys(new Minimongo.Matcher(selector)._paths);\n\n  // XXX remove it?\n  // return Object.keys(selector).map(k => {\n  //   // we don't know how to handle $where because it can be anything\n  //   if (k === '$where') {\n  //     return ''; // matches everything\n  //   }\n\n  //   // we branch from $or/$and/$nor operator\n  //   if (['$or', '$and', '$nor'].includes(k)) {\n  //     return selector[k].map(getPaths);\n  //   }\n\n  //   // the value is a literal or some comparison operator\n  //   return k;\n  // })\n  //   .reduce((a, b) => a.concat(b), [])\n  //   .filter((a, b, c) => c.indexOf(a) === b);\n}\n\n// A helper to ensure object has only certain keys\nfunction onlyContainsKeys(obj, keys) {\n  return Object.keys(obj).every(k => keys.includes(k));\n}\n\nfunction pathHasNumericKeys(path) {\n  return path.split('.').some(isNumericKey);\n}\n\n// Returns a set of key paths similar to\n// { 'foo.bar': 1, 'a.b.c': 1 }\nfunction treeToPaths(tree, prefix = '') {\n  const result = {};\n\n  Object.keys(tree).forEach(key => {\n    const value = tree[key];\n    if (value === Object(value)) {\n      Object.assign(result, treeToPaths(value, `${prefix + key}.`));\n    } else {\n      result[prefix + key] = value;\n    }\n  });\n\n  return result;\n}\n", "import LocalCollection from './local_collection.js';\n\nexport const hasOwn = Object.prototype.hasOwnProperty;\n\n// Each element selector contains:\n//  - compileElementSelector, a function with args:\n//    - operand - the \"right hand side\" of the operator\n//    - valueSelector - the \"context\" for the operator (so that $regex can find\n//      $options)\n//    - matcher - the Matcher this is going into (so that $elemMatch can compile\n//      more things)\n//    returning a function mapping a single value to bool.\n//  - dontExpandLeafArrays, a bool which prevents expandArraysInBranches from\n//    being called\n//  - dontIncludeLeafArrays, a bool which causes an argument to be passed to\n//    expandArraysInBranches if it is called\nexport const ELEMENT_OPERATORS = {\n  $lt: makeInequality(cmpValue => cmpValue < 0),\n  $gt: makeInequality(cmpValue => cmpValue > 0),\n  $lte: makeInequality(cmpValue => cmpValue <= 0),\n  $gte: makeInequality(cmpValue => cmpValue >= 0),\n  $mod: {\n    compileElementSelector(operand) {\n      if (!(Array.isArray(operand) && operand.length === 2\n            && typeof operand[0] === 'number'\n            && typeof operand[1] === 'number')) {\n        throw Error('argument to $mod must be an array of two numbers');\n      }\n\n      // XXX could require to be ints or round or something\n      const divisor = operand[0];\n      const remainder = operand[1];\n      return value => (\n        typeof value === 'number' && value % divisor === remainder\n      );\n    },\n  },\n  $in: {\n    compileElementSelector(operand) {\n      if (!Array.isArray(operand)) {\n        throw Error('$in needs an array');\n      }\n\n      const elementMatchers = operand.map(option => {\n        if (option instanceof RegExp) {\n          return regexpElementMatcher(option);\n        }\n\n        if (isOperatorObject(option)) {\n          throw Error('cannot nest $ under $in');\n        }\n\n        return equalityElementMatcher(option);\n      });\n\n      return value => {\n        // Allow {a: {$in: [null]}} to match when 'a' does not exist.\n        if (value === undefined) {\n          value = null;\n        }\n\n        return elementMatchers.some(matcher => matcher(value));\n      };\n    },\n  },\n  $size: {\n    // {a: [[5, 5]]} must match {a: {$size: 1}} but not {a: {$size: 2}}, so we\n    // don't want to consider the element [5,5] in the leaf array [[5,5]] as a\n    // possible value.\n    dontExpandLeafArrays: true,\n    compileElementSelector(operand) {\n      if (typeof operand === 'string') {\n        // Don't ask me why, but by experimentation, this seems to be what Mongo\n        // does.\n        operand = 0;\n      } else if (typeof operand !== 'number') {\n        throw Error('$size needs a number');\n      }\n\n      return value => Array.isArray(value) && value.length === operand;\n    },\n  },\n  $type: {\n    // {a: [5]} must not match {a: {$type: 4}} (4 means array), but it should\n    // match {a: {$type: 1}} (1 means number), and {a: [[5]]} must match {$a:\n    // {$type: 4}}. Thus, when we see a leaf array, we *should* expand it but\n    // should *not* include it itself.\n    dontIncludeLeafArrays: true,\n    compileElementSelector(operand) {\n      if (typeof operand === 'string') {\n        const operandAliasMap = {\n          'double': 1,\n          'string': 2,\n          'object': 3,\n          'array': 4,\n          'binData': 5,\n          'undefined': 6,\n          'objectId': 7,\n          'bool': 8,\n          'date': 9,\n          'null': 10,\n          'regex': 11,\n          'dbPointer': 12,\n          'javascript': 13,\n          'symbol': 14,\n          'javascriptWithScope': 15,\n          'int': 16,\n          'timestamp': 17,\n          'long': 18,\n          'decimal': 19,\n          'minKey': -1,\n          'maxKey': 127,\n        };\n        if (!hasOwn.call(operandAliasMap, operand)) {\n          throw Error(`unknown string alias for $type: ${operand}`);\n        }\n        operand = operandAliasMap[operand];\n      } else if (typeof operand === 'number') {\n        if (operand === 0 || operand < -1\n          || (operand > 19 && operand !== 127)) {\n          throw Error(`Invalid numerical $type code: ${operand}`);\n        }\n      } else {\n        throw Error('argument to $type is not a number or a string');\n      }\n\n      return value => (\n        value !== undefined && LocalCollection._f._type(value) === operand\n      );\n    },\n  },\n  $bitsAllSet: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAllSet');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.every((byte, i) => (bitmask[i] & byte) === byte);\n      };\n    },\n  },\n  $bitsAnySet: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAnySet');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.some((byte, i) => (~bitmask[i] & byte) !== byte);\n      };\n    },\n  },\n  $bitsAllClear: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAllClear');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.every((byte, i) => !(bitmask[i] & byte));\n      };\n    },\n  },\n  $bitsAnyClear: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAnyClear');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.some((byte, i) => (bitmask[i] & byte) !== byte);\n      };\n    },\n  },\n  $regex: {\n    compileElementSelector(operand, valueSelector) {\n      if (!(typeof operand === 'string' || operand instanceof RegExp)) {\n        throw Error('$regex has to be a string or RegExp');\n      }\n\n      let regexp;\n      if (valueSelector.$options !== undefined) {\n        // Options passed in $options (even the empty string) always overrides\n        // options in the RegExp object itself.\n\n        // Be clear that we only support the JS-supported options, not extended\n        // ones (eg, Mongo supports x and s). Ideally we would implement x and s\n        // by transforming the regexp, but not today...\n        if (/[^gim]/.test(valueSelector.$options)) {\n          throw new Error('Only the i, m, and g regexp options are supported');\n        }\n\n        const source = operand instanceof RegExp ? operand.source : operand;\n        regexp = new RegExp(source, valueSelector.$options);\n      } else if (operand instanceof RegExp) {\n        regexp = operand;\n      } else {\n        regexp = new RegExp(operand);\n      }\n\n      return regexpElementMatcher(regexp);\n    },\n  },\n  $elemMatch: {\n    dontExpandLeafArrays: true,\n    compileElementSelector(operand, valueSelector, matcher) {\n      if (!LocalCollection._isPlainObject(operand)) {\n        throw Error('$elemMatch need an object');\n      }\n\n      const isDocMatcher = !isOperatorObject(\n        Object.keys(operand)\n          .filter(key => !hasOwn.call(LOGICAL_OPERATORS, key))\n          .reduce((a, b) => Object.assign(a, {[b]: operand[b]}), {}),\n        true);\n\n      let subMatcher;\n      if (isDocMatcher) {\n        // This is NOT the same as compileValueSelector(operand), and not just\n        // because of the slightly different calling convention.\n        // {$elemMatch: {x: 3}} means \"an element has a field x:3\", not\n        // \"consists only of a field x:3\". Also, regexps and sub-$ are allowed.\n        subMatcher =\n          compileDocumentSelector(operand, matcher, {inElemMatch: true});\n      } else {\n        subMatcher = compileValueSelector(operand, matcher);\n      }\n\n      return value => {\n        if (!Array.isArray(value)) {\n          return false;\n        }\n\n        for (let i = 0; i < value.length; ++i) {\n          const arrayElement = value[i];\n          let arg;\n          if (isDocMatcher) {\n            // We can only match {$elemMatch: {b: 3}} against objects.\n            // (We can also match against arrays, if there's numeric indices,\n            // eg {$elemMatch: {'0.b': 3}} or {$elemMatch: {0: 3}}.)\n            if (!isIndexable(arrayElement)) {\n              return false;\n            }\n\n            arg = arrayElement;\n          } else {\n            // dontIterate ensures that {a: {$elemMatch: {$gt: 5}}} matches\n            // {a: [8]} but not {a: [[8]]}\n            arg = [{value: arrayElement, dontIterate: true}];\n          }\n          // XXX support $near in $elemMatch by propagating $distance?\n          if (subMatcher(arg).result) {\n            return i; // specially understood to mean \"use as arrayIndices\"\n          }\n        }\n\n        return false;\n      };\n    },\n  },\n};\n\n// Operators that appear at the top level of a document selector.\nconst LOGICAL_OPERATORS = {\n  $and(subSelector, matcher, inElemMatch) {\n    return andDocumentMatchers(\n      compileArrayOfDocumentSelectors(subSelector, matcher, inElemMatch)\n    );\n  },\n\n  $or(subSelector, matcher, inElemMatch) {\n    const matchers = compileArrayOfDocumentSelectors(\n      subSelector,\n      matcher,\n      inElemMatch\n    );\n\n    // Special case: if there is only one matcher, use it directly, *preserving*\n    // any arrayIndices it returns.\n    if (matchers.length === 1) {\n      return matchers[0];\n    }\n\n    return doc => {\n      const result = matchers.some(fn => fn(doc).result);\n      // $or does NOT set arrayIndices when it has multiple\n      // sub-expressions. (Tested against MongoDB.)\n      return {result};\n    };\n  },\n\n  $nor(subSelector, matcher, inElemMatch) {\n    const matchers = compileArrayOfDocumentSelectors(\n      subSelector,\n      matcher,\n      inElemMatch\n    );\n    return doc => {\n      const result = matchers.every(fn => !fn(doc).result);\n      // Never set arrayIndices, because we only match if nothing in particular\n      // 'matched' (and because this is consistent with MongoDB).\n      return {result};\n    };\n  },\n\n  $where(selectorValue, matcher) {\n    // Record that *any* path may be used.\n    matcher._recordPathUsed('');\n    matcher._hasWhere = true;\n\n    if (!(selectorValue instanceof Function)) {\n      // XXX MongoDB seems to have more complex logic to decide where or or not\n      // to add 'return'; not sure exactly what it is.\n      selectorValue = Function('obj', `return ${selectorValue}`);\n    }\n\n    // We make the document available as both `this` and `obj`.\n    // // XXX not sure what we should do if this throws\n    return doc => ({result: selectorValue.call(doc, doc)});\n  },\n\n  // This is just used as a comment in the query (in MongoDB, it also ends up in\n  // query logs); it has no effect on the actual selection.\n  $comment() {\n    return () => ({result: true});\n  },\n};\n\n// Operators that (unlike LOGICAL_OPERATORS) pertain to individual paths in a\n// document, but (unlike ELEMENT_OPERATORS) do not have a simple definition as\n// \"match each branched value independently and combine with\n// convertElementMatcherToBranchedMatcher\".\nconst VALUE_OPERATORS = {\n  $eq(operand) {\n    return convertElementMatcherToBranchedMatcher(\n      equalityElementMatcher(operand)\n    );\n  },\n  $not(operand, valueSelector, matcher) {\n    return invertBranchedMatcher(compileValueSelector(operand, matcher));\n  },\n  $ne(operand) {\n    return invertBranchedMatcher(\n      convertElementMatcherToBranchedMatcher(equalityElementMatcher(operand))\n    );\n  },\n  $nin(operand) {\n    return invertBranchedMatcher(\n      convertElementMatcherToBranchedMatcher(\n        ELEMENT_OPERATORS.$in.compileElementSelector(operand)\n      )\n    );\n  },\n  $exists(operand) {\n    const exists = convertElementMatcherToBranchedMatcher(\n      value => value !== undefined\n    );\n    return operand ? exists : invertBranchedMatcher(exists);\n  },\n  // $options just provides options for $regex; its logic is inside $regex\n  $options(operand, valueSelector) {\n    if (!hasOwn.call(valueSelector, '$regex')) {\n      throw Error('$options needs a $regex');\n    }\n\n    return everythingMatcher;\n  },\n  // $maxDistance is basically an argument to $near\n  $maxDistance(operand, valueSelector) {\n    if (!valueSelector.$near) {\n      throw Error('$maxDistance needs a $near');\n    }\n\n    return everythingMatcher;\n  },\n  $all(operand, valueSelector, matcher) {\n    if (!Array.isArray(operand)) {\n      throw Error('$all requires array');\n    }\n\n    // Not sure why, but this seems to be what MongoDB does.\n    if (operand.length === 0) {\n      return nothingMatcher;\n    }\n\n    const branchedMatchers = operand.map(criterion => {\n      // XXX handle $all/$elemMatch combination\n      if (isOperatorObject(criterion)) {\n        throw Error('no $ expressions in $all');\n      }\n\n      // This is always a regexp or equality selector.\n      return compileValueSelector(criterion, matcher);\n    });\n\n    // andBranchedMatchers does NOT require all selectors to return true on the\n    // SAME branch.\n    return andBranchedMatchers(branchedMatchers);\n  },\n  $near(operand, valueSelector, matcher, isRoot) {\n    if (!isRoot) {\n      throw Error('$near can\\'t be inside another $ operator');\n    }\n\n    matcher._hasGeoQuery = true;\n\n    // There are two kinds of geodata in MongoDB: legacy coordinate pairs and\n    // GeoJSON. They use different distance metrics, too. GeoJSON queries are\n    // marked with a $geometry property, though legacy coordinates can be\n    // matched using $geometry.\n    let maxDistance, point, distance;\n    if (LocalCollection._isPlainObject(operand) && hasOwn.call(operand, '$geometry')) {\n      // GeoJSON \"2dsphere\" mode.\n      maxDistance = operand.$maxDistance;\n      point = operand.$geometry;\n      distance = value => {\n        // XXX: for now, we don't calculate the actual distance between, say,\n        // polygon and circle. If people care about this use-case it will get\n        // a priority.\n        if (!value) {\n          return null;\n        }\n\n        if (!value.type) {\n          return GeoJSON.pointDistance(\n            point,\n            {type: 'Point', coordinates: pointToArray(value)}\n          );\n        }\n\n        if (value.type === 'Point') {\n          return GeoJSON.pointDistance(point, value);\n        }\n\n        return GeoJSON.geometryWithinRadius(value, point, maxDistance)\n          ? 0\n          : maxDistance + 1;\n      };\n    } else {\n      maxDistance = valueSelector.$maxDistance;\n\n      if (!isIndexable(operand)) {\n        throw Error('$near argument must be coordinate pair or GeoJSON');\n      }\n\n      point = pointToArray(operand);\n\n      distance = value => {\n        if (!isIndexable(value)) {\n          return null;\n        }\n\n        return distanceCoordinatePairs(point, value);\n      };\n    }\n\n    return branchedValues => {\n      // There might be multiple points in the document that match the given\n      // field. Only one of them needs to be within $maxDistance, but we need to\n      // evaluate all of them and use the nearest one for the implicit sort\n      // specifier. (That's why we can't just use ELEMENT_OPERATORS here.)\n      //\n      // Note: This differs from MongoDB's implementation, where a document will\n      // actually show up *multiple times* in the result set, with one entry for\n      // each within-$maxDistance branching point.\n      const result = {result: false};\n      expandArraysInBranches(branchedValues).every(branch => {\n        // if operation is an update, don't skip branches, just return the first\n        // one (#3599)\n        let curDistance;\n        if (!matcher._isUpdate) {\n          if (!(typeof branch.value === 'object')) {\n            return true;\n          }\n\n          curDistance = distance(branch.value);\n\n          // Skip branches that aren't real points or are too far away.\n          if (curDistance === null || curDistance > maxDistance) {\n            return true;\n          }\n\n          // Skip anything that's a tie.\n          if (result.distance !== undefined && result.distance <= curDistance) {\n            return true;\n          }\n        }\n\n        result.result = true;\n        result.distance = curDistance;\n\n        if (branch.arrayIndices) {\n          result.arrayIndices = branch.arrayIndices;\n        } else {\n          delete result.arrayIndices;\n        }\n\n        return !matcher._isUpdate;\n      });\n\n      return result;\n    };\n  },\n};\n\n// NB: We are cheating and using this function to implement 'AND' for both\n// 'document matchers' and 'branched matchers'. They both return result objects\n// but the argument is different: for the former it's a whole doc, whereas for\n// the latter it's an array of 'branched values'.\nfunction andSomeMatchers(subMatchers) {\n  if (subMatchers.length === 0) {\n    return everythingMatcher;\n  }\n\n  if (subMatchers.length === 1) {\n    return subMatchers[0];\n  }\n\n  return docOrBranches => {\n    const match = {};\n    match.result = subMatchers.every(fn => {\n      const subResult = fn(docOrBranches);\n\n      // Copy a 'distance' number out of the first sub-matcher that has\n      // one. Yes, this means that if there are multiple $near fields in a\n      // query, something arbitrary happens; this appears to be consistent with\n      // Mongo.\n      if (subResult.result &&\n          subResult.distance !== undefined &&\n          match.distance === undefined) {\n        match.distance = subResult.distance;\n      }\n\n      // Similarly, propagate arrayIndices from sub-matchers... but to match\n      // MongoDB behavior, this time the *last* sub-matcher with arrayIndices\n      // wins.\n      if (subResult.result && subResult.arrayIndices) {\n        match.arrayIndices = subResult.arrayIndices;\n      }\n\n      return subResult.result;\n    });\n\n    // If we didn't actually match, forget any extra metadata we came up with.\n    if (!match.result) {\n      delete match.distance;\n      delete match.arrayIndices;\n    }\n\n    return match;\n  };\n}\n\nconst andDocumentMatchers = andSomeMatchers;\nconst andBranchedMatchers = andSomeMatchers;\n\nfunction compileArrayOfDocumentSelectors(selectors, matcher, inElemMatch) {\n  if (!Array.isArray(selectors) || selectors.length === 0) {\n    throw Error('$and/$or/$nor must be nonempty array');\n  }\n\n  return selectors.map(subSelector => {\n    if (!LocalCollection._isPlainObject(subSelector)) {\n      throw Error('$or/$and/$nor entries need to be full objects');\n    }\n\n    return compileDocumentSelector(subSelector, matcher, {inElemMatch});\n  });\n}\n\n// Takes in a selector that could match a full document (eg, the original\n// selector). Returns a function mapping document->result object.\n//\n// matcher is the Matcher object we are compiling.\n//\n// If this is the root document selector (ie, not wrapped in $and or the like),\n// then isRoot is true. (This is used by $near.)\nexport function compileDocumentSelector(docSelector, matcher, options = {}) {\n  const docMatchers = Object.keys(docSelector).map(key => {\n    const subSelector = docSelector[key];\n\n    if (key.substr(0, 1) === '$') {\n      // Outer operators are either logical operators (they recurse back into\n      // this function), or $where.\n      if (!hasOwn.call(LOGICAL_OPERATORS, key)) {\n        throw new Error(`Unrecognized logical operator: ${key}`);\n      }\n\n      matcher._isSimple = false;\n      return LOGICAL_OPERATORS[key](subSelector, matcher, options.inElemMatch);\n    }\n\n    // Record this path, but only if we aren't in an elemMatcher, since in an\n    // elemMatch this is a path inside an object in an array, not in the doc\n    // root.\n    if (!options.inElemMatch) {\n      matcher._recordPathUsed(key);\n    }\n\n    // Don't add a matcher if subSelector is a function -- this is to match\n    // the behavior of Meteor on the server (inherited from the node mongodb\n    // driver), which is to ignore any part of a selector which is a function.\n    if (typeof subSelector === 'function') {\n      return undefined;\n    }\n\n    const lookUpByIndex = makeLookupFunction(key);\n    const valueMatcher = compileValueSelector(\n      subSelector,\n      matcher,\n      options.isRoot\n    );\n\n    return doc => valueMatcher(lookUpByIndex(doc));\n  }).filter(Boolean);\n\n  return andDocumentMatchers(docMatchers);\n}\n\n// Takes in a selector that could match a key-indexed value in a document; eg,\n// {$gt: 5, $lt: 9}, or a regular expression, or any non-expression object (to\n// indicate equality).  Returns a branched matcher: a function mapping\n// [branched value]->result object.\nfunction compileValueSelector(valueSelector, matcher, isRoot) {\n  if (valueSelector instanceof RegExp) {\n    matcher._isSimple = false;\n    return convertElementMatcherToBranchedMatcher(\n      regexpElementMatcher(valueSelector)\n    );\n  }\n\n  if (isOperatorObject(valueSelector)) {\n    return operatorBranchedMatcher(valueSelector, matcher, isRoot);\n  }\n\n  return convertElementMatcherToBranchedMatcher(\n    equalityElementMatcher(valueSelector)\n  );\n}\n\n// Given an element matcher (which evaluates a single value), returns a branched\n// value (which evaluates the element matcher on all the branches and returns a\n// more structured return value possibly including arrayIndices).\nfunction convertElementMatcherToBranchedMatcher(elementMatcher, options = {}) {\n  return branches => {\n    const expanded = options.dontExpandLeafArrays\n      ? branches\n      : expandArraysInBranches(branches, options.dontIncludeLeafArrays);\n\n    const match = {};\n    match.result = expanded.some(element => {\n      let matched = elementMatcher(element.value);\n\n      // Special case for $elemMatch: it means \"true, and use this as an array\n      // index if I didn't already have one\".\n      if (typeof matched === 'number') {\n        // XXX This code dates from when we only stored a single array index\n        // (for the outermost array). Should we be also including deeper array\n        // indices from the $elemMatch match?\n        if (!element.arrayIndices) {\n          element.arrayIndices = [matched];\n        }\n\n        matched = true;\n      }\n\n      // If some element matched, and it's tagged with array indices, include\n      // those indices in our result object.\n      if (matched && element.arrayIndices) {\n        match.arrayIndices = element.arrayIndices;\n      }\n\n      return matched;\n    });\n\n    return match;\n  };\n}\n\n// Helpers for $near.\nfunction distanceCoordinatePairs(a, b) {\n  const pointA = pointToArray(a);\n  const pointB = pointToArray(b);\n\n  return Math.hypot(pointA[0] - pointB[0], pointA[1] - pointB[1]);\n}\n\n// Takes something that is not an operator object and returns an element matcher\n// for equality with that thing.\nexport function equalityElementMatcher(elementSelector) {\n  if (isOperatorObject(elementSelector)) {\n    throw Error('Can\\'t create equalityValueSelector for operator object');\n  }\n\n  // Special-case: null and undefined are equal (if you got undefined in there\n  // somewhere, or if you got it due to some branch being non-existent in the\n  // weird special case), even though they aren't with EJSON.equals.\n  // undefined or null\n  if (elementSelector == null) {\n    return value => value == null;\n  }\n\n  return value => LocalCollection._f._equal(elementSelector, value);\n}\n\nfunction everythingMatcher(docOrBranchedValues) {\n  return {result: true};\n}\n\nexport function expandArraysInBranches(branches, skipTheArrays) {\n  const branchesOut = [];\n\n  branches.forEach(branch => {\n    const thisIsArray = Array.isArray(branch.value);\n\n    // We include the branch itself, *UNLESS* we it's an array that we're going\n    // to iterate and we're told to skip arrays.  (That's right, we include some\n    // arrays even skipTheArrays is true: these are arrays that were found via\n    // explicit numerical indices.)\n    if (!(skipTheArrays && thisIsArray && !branch.dontIterate)) {\n      branchesOut.push({arrayIndices: branch.arrayIndices, value: branch.value});\n    }\n\n    if (thisIsArray && !branch.dontIterate) {\n      branch.value.forEach((value, i) => {\n        branchesOut.push({\n          arrayIndices: (branch.arrayIndices || []).concat(i),\n          value\n        });\n      });\n    }\n  });\n\n  return branchesOut;\n}\n\n// Helpers for $bitsAllSet/$bitsAnySet/$bitsAllClear/$bitsAnyClear.\nfunction getOperandBitmask(operand, selector) {\n  // numeric bitmask\n  // You can provide a numeric bitmask to be matched against the operand field.\n  // It must be representable as a non-negative 32-bit signed integer.\n  // Otherwise, $bitsAllSet will return an error.\n  if (Number.isInteger(operand) && operand >= 0) {\n    return new Uint8Array(new Int32Array([operand]).buffer);\n  }\n\n  // bindata bitmask\n  // You can also use an arbitrarily large BinData instance as a bitmask.\n  if (EJSON.isBinary(operand)) {\n    return new Uint8Array(operand.buffer);\n  }\n\n  // position list\n  // If querying a list of bit positions, each <position> must be a non-negative\n  // integer. Bit positions start at 0 from the least significant bit.\n  if (Array.isArray(operand) &&\n      operand.every(x => Number.isInteger(x) && x >= 0)) {\n    const buffer = new ArrayBuffer((Math.max(...operand) >> 3) + 1);\n    const view = new Uint8Array(buffer);\n\n    operand.forEach(x => {\n      view[x >> 3] |= 1 << (x & 0x7);\n    });\n\n    return view;\n  }\n\n  // bad operand\n  throw Error(\n    `operand to ${selector} must be a numeric bitmask (representable as a ` +\n    'non-negative 32-bit signed integer), a bindata bitmask or an array with ' +\n    'bit positions (non-negative integers)'\n  );\n}\n\nfunction getValueBitmask(value, length) {\n  // The field value must be either numerical or a BinData instance. Otherwise,\n  // $bits... will not match the current document.\n\n  // numerical\n  if (Number.isSafeInteger(value)) {\n    // $bits... will not match numerical values that cannot be represented as a\n    // signed 64-bit integer. This can be the case if a value is either too\n    // large or small to fit in a signed 64-bit integer, or if it has a\n    // fractional component.\n    const buffer = new ArrayBuffer(\n      Math.max(length, 2 * Uint32Array.BYTES_PER_ELEMENT)\n    );\n\n    let view = new Uint32Array(buffer, 0, 2);\n    view[0] = value % ((1 << 16) * (1 << 16)) | 0;\n    view[1] = value / ((1 << 16) * (1 << 16)) | 0;\n\n    // sign extension\n    if (value < 0) {\n      view = new Uint8Array(buffer, 2);\n      view.forEach((byte, i) => {\n        view[i] = 0xff;\n      });\n    }\n\n    return new Uint8Array(buffer);\n  }\n\n  // bindata\n  if (EJSON.isBinary(value)) {\n    return new Uint8Array(value.buffer);\n  }\n\n  // no match\n  return false;\n}\n\n// Actually inserts a key value into the selector document\n// However, this checks there is no ambiguity in setting\n// the value for the given key, throws otherwise\nfunction insertIntoDocument(document, key, value) {\n  Object.keys(document).forEach(existingKey => {\n    if (\n      (existingKey.length > key.length && existingKey.indexOf(`${key}.`) === 0) ||\n      (key.length > existingKey.length && key.indexOf(`${existingKey}.`) === 0)\n    ) {\n      throw new Error(\n        `cannot infer query fields to set, both paths '${existingKey}' and ` +\n        `'${key}' are matched`\n      );\n    } else if (existingKey === key) {\n      throw new Error(\n        `cannot infer query fields to set, path '${key}' is matched twice`\n      );\n    }\n  });\n\n  document[key] = value;\n}\n\n// Returns a branched matcher that matches iff the given matcher does not.\n// Note that this implicitly \"deMorganizes\" the wrapped function.  ie, it\n// means that ALL branch values need to fail to match innerBranchedMatcher.\nfunction invertBranchedMatcher(branchedMatcher) {\n  return branchValues => {\n    // We explicitly choose to strip arrayIndices here: it doesn't make sense to\n    // say \"update the array element that does not match something\", at least\n    // in mongo-land.\n    return {result: !branchedMatcher(branchValues).result};\n  };\n}\n\nexport function isIndexable(obj) {\n  return Array.isArray(obj) || LocalCollection._isPlainObject(obj);\n}\n\nexport function isNumericKey(s) {\n  return /^[0-9]+$/.test(s);\n}\n\n// Returns true if this is an object with at least one key and all keys begin\n// with $.  Unless inconsistentOK is set, throws if some keys begin with $ and\n// others don't.\nexport function isOperatorObject(valueSelector, inconsistentOK) {\n  if (!LocalCollection._isPlainObject(valueSelector)) {\n    return false;\n  }\n\n  let theseAreOperators = undefined;\n  Object.keys(valueSelector).forEach(selKey => {\n    const thisIsOperator = selKey.substr(0, 1) === '$' || selKey === 'diff';\n\n    if (theseAreOperators === undefined) {\n      theseAreOperators = thisIsOperator;\n    } else if (theseAreOperators !== thisIsOperator) {\n      if (!inconsistentOK) {\n        throw new Error(\n          `Inconsistent operator: ${JSON.stringify(valueSelector)}`\n        );\n      }\n\n      theseAreOperators = false;\n    }\n  });\n\n  return !!theseAreOperators; // {} has no operators\n}\n\n// Helper for $lt/$gt/$lte/$gte.\nfunction makeInequality(cmpValueComparator) {\n  return {\n    compileElementSelector(operand) {\n      // Arrays never compare false with non-arrays for any inequality.\n      // XXX This was behavior we observed in pre-release MongoDB 2.5, but\n      //     it seems to have been reverted.\n      //     See https://jira.mongodb.org/browse/SERVER-11444\n      if (Array.isArray(operand)) {\n        return () => false;\n      }\n\n      // Special case: consider undefined and null the same (so true with\n      // $gte/$lte).\n      if (operand === undefined) {\n        operand = null;\n      }\n\n      const operandType = LocalCollection._f._type(operand);\n\n      return value => {\n        if (value === undefined) {\n          value = null;\n        }\n\n        // Comparisons are never true among things of different type (except\n        // null vs undefined).\n        if (LocalCollection._f._type(value) !== operandType) {\n          return false;\n        }\n\n        return cmpValueComparator(LocalCollection._f._cmp(value, operand));\n      };\n    },\n  };\n}\n\n// makeLookupFunction(key) returns a lookup function.\n//\n// A lookup function takes in a document and returns an array of matching\n// branches.  If no arrays are found while looking up the key, this array will\n// have exactly one branches (possibly 'undefined', if some segment of the key\n// was not found).\n//\n// If arrays are found in the middle, this can have more than one element, since\n// we 'branch'. When we 'branch', if there are more key segments to look up,\n// then we only pursue branches that are plain objects (not arrays or scalars).\n// This means we can actually end up with no branches!\n//\n// We do *NOT* branch on arrays that are found at the end (ie, at the last\n// dotted member of the key). We just return that array; if you want to\n// effectively 'branch' over the array's values, post-process the lookup\n// function with expandArraysInBranches.\n//\n// Each branch is an object with keys:\n//  - value: the value at the branch\n//  - dontIterate: an optional bool; if true, it means that 'value' is an array\n//    that expandArraysInBranches should NOT expand. This specifically happens\n//    when there is a numeric index in the key, and ensures the\n//    perhaps-surprising MongoDB behavior where {'a.0': 5} does NOT\n//    match {a: [[5]]}.\n//  - arrayIndices: if any array indexing was done during lookup (either due to\n//    explicit numeric indices or implicit branching), this will be an array of\n//    the array indices used, from outermost to innermost; it is falsey or\n//    absent if no array index is used. If an explicit numeric index is used,\n//    the index will be followed in arrayIndices by the string 'x'.\n//\n//    Note: arrayIndices is used for two purposes. First, it is used to\n//    implement the '$' modifier feature, which only ever looks at its first\n//    element.\n//\n//    Second, it is used for sort key generation, which needs to be able to tell\n//    the difference between different paths. Moreover, it needs to\n//    differentiate between explicit and implicit branching, which is why\n//    there's the somewhat hacky 'x' entry: this means that explicit and\n//    implicit array lookups will have different full arrayIndices paths. (That\n//    code only requires that different paths have different arrayIndices; it\n//    doesn't actually 'parse' arrayIndices. As an alternative, arrayIndices\n//    could contain objects with flags like 'implicit', but I think that only\n//    makes the code surrounding them more complex.)\n//\n//    (By the way, this field ends up getting passed around a lot without\n//    cloning, so never mutate any arrayIndices field/var in this package!)\n//\n//\n// At the top level, you may only pass in a plain object or array.\n//\n// See the test 'minimongo - lookup' for some examples of what lookup functions\n// return.\nexport function makeLookupFunction(key, options = {}) {\n  const parts = key.split('.');\n  const firstPart = parts.length ? parts[0] : '';\n  const lookupRest = (\n    parts.length > 1 &&\n    makeLookupFunction(parts.slice(1).join('.'), options)\n  );\n\n  function buildResult(arrayIndices, dontIterate, value) {\n    return arrayIndices && arrayIndices.length\n      ? dontIterate\n        ? [{ arrayIndices, dontIterate, value }]\n        : [{ arrayIndices, value }]\n      : dontIterate\n        ? [{ dontIterate, value }]\n        : [{ value }];\n  }\n\n  // Doc will always be a plain object or an array.\n  // apply an explicit numeric index, an array.\n  return (doc, arrayIndices) => {\n    if (Array.isArray(doc)) {\n      // If we're being asked to do an invalid lookup into an array (non-integer\n      // or out-of-bounds), return no results (which is different from returning\n      // a single undefined result, in that `null` equality checks won't match).\n      if (!(isNumericKey(firstPart) && firstPart < doc.length)) {\n        return [];\n      }\n\n      // Remember that we used this array index. Include an 'x' to indicate that\n      // the previous index came from being considered as an explicit array\n      // index (not branching).\n      arrayIndices = arrayIndices ? arrayIndices.concat(+firstPart, 'x') : [+firstPart, 'x'];\n    }\n\n    // Do our first lookup.\n    const firstLevel = doc[firstPart];\n\n    // If there is no deeper to dig, return what we found.\n    //\n    // If what we found is an array, most value selectors will choose to treat\n    // the elements of the array as matchable values in their own right, but\n    // that's done outside of the lookup function. (Exceptions to this are $size\n    // and stuff relating to $elemMatch.  eg, {a: {$size: 2}} does not match {a:\n    // [[1, 2]]}.)\n    //\n    // That said, if we just did an *explicit* array lookup (on doc) to find\n    // firstLevel, and firstLevel is an array too, we do NOT want value\n    // selectors to iterate over it.  eg, {'a.0': 5} does not match {a: [[5]]}.\n    // So in that case, we mark the return value as 'don't iterate'.\n    if (!lookupRest) {\n      return buildResult(\n        arrayIndices,\n        Array.isArray(doc) && Array.isArray(firstLevel),\n        firstLevel,\n      );\n    }\n\n    // We need to dig deeper.  But if we can't, because what we've found is not\n    // an array or plain object, we're done. If we just did a numeric index into\n    // an array, we return nothing here (this is a change in Mongo 2.5 from\n    // Mongo 2.4, where {'a.0.b': null} stopped matching {a: [5]}). Otherwise,\n    // return a single `undefined` (which can, for example, match via equality\n    // with `null`).\n    if (!isIndexable(firstLevel)) {\n      if (Array.isArray(doc)) {\n        return [];\n      }\n\n      return buildResult(arrayIndices, false, undefined);\n    }\n\n    const result = [];\n    const appendToResult = more => {\n      result.push(...more);\n    };\n\n    // Dig deeper: look up the rest of the parts on whatever we've found.\n    // (lookupRest is smart enough to not try to do invalid lookups into\n    // firstLevel if it's an array.)\n    appendToResult(lookupRest(firstLevel, arrayIndices));\n\n    // If we found an array, then in *addition* to potentially treating the next\n    // part as a literal integer lookup, we should also 'branch': try to look up\n    // the rest of the parts on each array element in parallel.\n    //\n    // In this case, we *only* dig deeper into array elements that are plain\n    // objects. (Recall that we only got this far if we have further to dig.)\n    // This makes sense: we certainly don't dig deeper into non-indexable\n    // objects. And it would be weird to dig into an array: it's simpler to have\n    // a rule that explicit integer indexes only apply to an outer array, not to\n    // an array you find after a branching search.\n    //\n    // In the special case of a numeric part in a *sort selector* (not a query\n    // selector), we skip the branching: we ONLY allow the numeric part to mean\n    // 'look up this index' in that case, not 'also look up this index in all\n    // the elements of the array'.\n    if (Array.isArray(firstLevel) &&\n        !(isNumericKey(parts[1]) && options.forSort)) {\n      firstLevel.forEach((branch, arrayIndex) => {\n        if (LocalCollection._isPlainObject(branch)) {\n          appendToResult(lookupRest(branch, arrayIndices ? arrayIndices.concat(arrayIndex) : [arrayIndex]));\n        }\n      });\n    }\n\n    return result;\n  };\n}\n\n// Object exported only for unit testing.\n// Use it to export private functions to test in Tinytest.\nMinimongoTest = {makeLookupFunction};\nMinimongoError = (message, options = {}) => {\n  if (typeof message === 'string' && options.field) {\n    message += ` for field '${options.field}'`;\n  }\n\n  const error = new Error(message);\n  error.name = 'MinimongoError';\n  return error;\n};\n\nexport function nothingMatcher(docOrBranchedValues) {\n  return {result: false};\n}\n\n// Takes an operator object (an object with $ keys) and returns a branched\n// matcher for it.\nfunction operatorBranchedMatcher(valueSelector, matcher, isRoot) {\n  // Each valueSelector works separately on the various branches.  So one\n  // operator can match one branch and another can match another branch.  This\n  // is OK.\n  const operatorMatchers = Object.keys(valueSelector).map(operator => {\n    const operand = valueSelector[operator];\n\n    const simpleRange = (\n      ['$lt', '$lte', '$gt', '$gte'].includes(operator) &&\n      typeof operand === 'number'\n    );\n\n    const simpleEquality = (\n      ['$ne', '$eq'].includes(operator) &&\n      operand !== Object(operand)\n    );\n\n    const simpleInclusion = (\n      ['$in', '$nin'].includes(operator)\n      && Array.isArray(operand)\n      && !operand.some(x => x === Object(x))\n    );\n\n    if (!(simpleRange || simpleInclusion || simpleEquality)) {\n      matcher._isSimple = false;\n    }\n\n    if (hasOwn.call(VALUE_OPERATORS, operator)) {\n      return VALUE_OPERATORS[operator](operand, valueSelector, matcher, isRoot);\n    }\n\n    if (hasOwn.call(ELEMENT_OPERATORS, operator)) {\n      const options = ELEMENT_OPERATORS[operator];\n      return convertElementMatcherToBranchedMatcher(\n        options.compileElementSelector(operand, valueSelector, matcher),\n        options\n      );\n    }\n\n    throw new Error(`Unrecognized operator: ${operator}`);\n  });\n\n  return andBranchedMatchers(operatorMatchers);\n}\n\n// paths - Array: list of mongo style paths\n// newLeafFn - Function: of form function(path) should return a scalar value to\n//                       put into list created for that path\n// conflictFn - Function: of form function(node, path, fullPath) is called\n//                        when building a tree path for 'fullPath' node on\n//                        'path' was already a leaf with a value. Must return a\n//                        conflict resolution.\n// initial tree - Optional Object: starting tree.\n// @returns - Object: tree represented as a set of nested objects\nexport function pathsToTree(paths, newLeafFn, conflictFn, root = {}) {\n  paths.forEach(path => {\n    const pathArray = path.split('.');\n    let tree = root;\n\n    // use .every just for iteration with break\n    const success = pathArray.slice(0, -1).every((key, i) => {\n      if (!hasOwn.call(tree, key)) {\n        tree[key] = {};\n      } else if (tree[key] !== Object(tree[key])) {\n        tree[key] = conflictFn(\n          tree[key],\n          pathArray.slice(0, i + 1).join('.'),\n          path\n        );\n\n        // break out of loop if we are failing for this path\n        if (tree[key] !== Object(tree[key])) {\n          return false;\n        }\n      }\n\n      tree = tree[key];\n\n      return true;\n    });\n\n    if (success) {\n      const lastKey = pathArray[pathArray.length - 1];\n      if (hasOwn.call(tree, lastKey)) {\n        tree[lastKey] = conflictFn(tree[lastKey], path, path);\n      } else {\n        tree[lastKey] = newLeafFn(path);\n      }\n    }\n  });\n\n  return root;\n}\n\n// Makes sure we get 2 elements array and assume the first one to be x and\n// the second one to y no matter what user passes.\n// In case user passes { lon: x, lat: y } returns [x, y]\nfunction pointToArray(point) {\n  return Array.isArray(point) ? point.slice() : [point.x, point.y];\n}\n\n// Creating a document from an upsert is quite tricky.\n// E.g. this selector: {\"$or\": [{\"b.foo\": {\"$all\": [\"bar\"]}}]}, should result\n// in: {\"b.foo\": \"bar\"}\n// But this selector: {\"$or\": [{\"b\": {\"foo\": {\"$all\": [\"bar\"]}}}]} should throw\n// an error\n\n// Some rules (found mainly with trial & error, so there might be more):\n// - handle all childs of $and (or implicit $and)\n// - handle $or nodes with exactly 1 child\n// - ignore $or nodes with more than 1 child\n// - ignore $nor and $not nodes\n// - throw when a value can not be set unambiguously\n// - every value for $all should be dealt with as separate $eq-s\n// - threat all children of $all as $eq setters (=> set if $all.length === 1,\n//   otherwise throw error)\n// - you can not mix '$'-prefixed keys and non-'$'-prefixed keys\n// - you can only have dotted keys on a root-level\n// - you can not have '$'-prefixed keys more than one-level deep in an object\n\n// Handles one key/value pair to put in the selector document\nfunction populateDocumentWithKeyValue(document, key, value) {\n  if (value && Object.getPrototypeOf(value) === Object.prototype) {\n    populateDocumentWithObject(document, key, value);\n  } else if (!(value instanceof RegExp)) {\n    insertIntoDocument(document, key, value);\n  }\n}\n\n// Handles a key, value pair to put in the selector document\n// if the value is an object\nfunction populateDocumentWithObject(document, key, value) {\n  const keys = Object.keys(value);\n  const unprefixedKeys = keys.filter(op => op[0] !== '$');\n\n  if (unprefixedKeys.length > 0 || !keys.length) {\n    // Literal (possibly empty) object ( or empty object )\n    // Don't allow mixing '$'-prefixed with non-'$'-prefixed fields\n    if (keys.length !== unprefixedKeys.length) {\n      throw new Error(`unknown operator: ${unprefixedKeys[0]}`);\n    }\n\n    validateObject(value, key);\n    insertIntoDocument(document, key, value);\n  } else {\n    Object.keys(value).forEach(op => {\n      const object = value[op];\n\n      if (op === '$eq') {\n        populateDocumentWithKeyValue(document, key, object);\n      } else if (op === '$all') {\n        // every value for $all should be dealt with as separate $eq-s\n        object.forEach(element =>\n          populateDocumentWithKeyValue(document, key, element)\n        );\n      }\n    });\n  }\n}\n\n// Fills a document with certain fields from an upsert selector\nexport function populateDocumentWithQueryFields(query, document = {}) {\n  if (Object.getPrototypeOf(query) === Object.prototype) {\n    // handle implicit $and\n    Object.keys(query).forEach(key => {\n      const value = query[key];\n\n      if (key === '$and') {\n        // handle explicit $and\n        value.forEach(element =>\n          populateDocumentWithQueryFields(element, document)\n        );\n      } else if (key === '$or') {\n        // handle $or nodes with exactly 1 child\n        if (value.length === 1) {\n          populateDocumentWithQueryFields(value[0], document);\n        }\n      } else if (key[0] !== '$') {\n        // Ignore other '$'-prefixed logical selectors\n        populateDocumentWithKeyValue(document, key, value);\n      }\n    });\n  } else {\n    // Handle meteor-specific shortcut for selecting _id\n    if (LocalCollection._selectorIsId(query)) {\n      insertIntoDocument(document, '_id', query);\n    }\n  }\n\n  return document;\n}\n\n// Traverses the keys of passed projection and constructs a tree where all\n// leaves are either all True or all False\n// @returns Object:\n//  - tree - Object - tree representation of keys involved in projection\n//  (exception for '_id' as it is a special case handled separately)\n//  - including - Boolean - \"take only certain fields\" type of projection\nexport function projectionDetails(fields) {\n  // Find the non-_id keys (_id is handled specially because it is included\n  // unless explicitly excluded). Sort the keys, so that our code to detect\n  // overlaps like 'foo' and 'foo.bar' can assume that 'foo' comes first.\n  let fieldsKeys = Object.keys(fields).sort();\n\n  // If _id is the only field in the projection, do not remove it, since it is\n  // required to determine if this is an exclusion or exclusion. Also keep an\n  // inclusive _id, since inclusive _id follows the normal rules about mixing\n  // inclusive and exclusive fields. If _id is not the only field in the\n  // projection and is exclusive, remove it so it can be handled later by a\n  // special case, since exclusive _id is always allowed.\n  if (!(fieldsKeys.length === 1 && fieldsKeys[0] === '_id') &&\n      !(fieldsKeys.includes('_id') && fields._id)) {\n    fieldsKeys = fieldsKeys.filter(key => key !== '_id');\n  }\n\n  let including = null; // Unknown\n\n  fieldsKeys.forEach(keyPath => {\n    const rule = !!fields[keyPath];\n\n    if (including === null) {\n      including = rule;\n    }\n\n    // This error message is copied from MongoDB shell\n    if (including !== rule) {\n      throw MinimongoError(\n        'You cannot currently mix including and excluding fields.'\n      );\n    }\n  });\n\n  const projectionRulesTree = pathsToTree(\n    fieldsKeys,\n    path => including,\n    (node, path, fullPath) => {\n      // Check passed projection fields' keys: If you have two rules such as\n      // 'foo.bar' and 'foo.bar.baz', then the result becomes ambiguous. If\n      // that happens, there is a probability you are doing something wrong,\n      // framework should notify you about such mistake earlier on cursor\n      // compilation step than later during runtime.  Note, that real mongo\n      // doesn't do anything about it and the later rule appears in projection\n      // project, more priority it takes.\n      //\n      // Example, assume following in mongo shell:\n      // > db.coll.insert({ a: { b: 23, c: 44 } })\n      // > db.coll.find({}, { 'a': 1, 'a.b': 1 })\n      // {\"_id\": ObjectId(\"520bfe456024608e8ef24af3\"), \"a\": {\"b\": 23}}\n      // > db.coll.find({}, { 'a.b': 1, 'a': 1 })\n      // {\"_id\": ObjectId(\"520bfe456024608e8ef24af3\"), \"a\": {\"b\": 23, \"c\": 44}}\n      //\n      // Note, how second time the return set of keys is different.\n      const currentPath = fullPath;\n      const anotherPath = path;\n      throw MinimongoError(\n        `both ${currentPath} and ${anotherPath} found in fields option, ` +\n        'using both of them may trigger unexpected behavior. Did you mean to ' +\n        'use only one of them?'\n      );\n    });\n\n  return {including, tree: projectionRulesTree};\n}\n\n// Takes a RegExp object and returns an element matcher.\nexport function regexpElementMatcher(regexp) {\n  return value => {\n    if (value instanceof RegExp) {\n      return value.toString() === regexp.toString();\n    }\n\n    // Regexps only work against strings.\n    if (typeof value !== 'string') {\n      return false;\n    }\n\n    // Reset regexp's state to avoid inconsistent matching for objects with the\n    // same value on consecutive calls of regexp.test. This happens only if the\n    // regexp has the 'g' flag. Also note that ES6 introduces a new flag 'y' for\n    // which we should *not* change the lastIndex but MongoDB doesn't support\n    // either of these flags.\n    regexp.lastIndex = 0;\n\n    return regexp.test(value);\n  };\n}\n\n// Validates the key in a path.\n// Objects that are nested more then 1 level cannot have dotted fields\n// or fields starting with '$'\nfunction validateKeyInPath(key, path) {\n  if (key.includes('.')) {\n    throw new Error(\n      `The dotted field '${key}' in '${path}.${key} is not valid for storage.`\n    );\n  }\n\n  if (key[0] === '$') {\n    throw new Error(\n      `The dollar ($) prefixed field  '${path}.${key} is not valid for storage.`\n    );\n  }\n}\n\n// Recursively validates an object that is nested more than one level deep\nfunction validateObject(object, path) {\n  if (object && Object.getPrototypeOf(object) === Object.prototype) {\n    Object.keys(object).forEach(key => {\n      validateKeyInPath(key, path);\n      validateObject(object[key], path + '.' + key);\n    });\n  }\n}\n", "/** Exported values are also used in the mongo package. */\n\n/** @param {string} method */\nexport function getAsyncMethodName(method) {\n  return `${method.replace('_', '')}Async`;\n}\n\nexport const ASYNC_COLLECTION_METHODS = [\n  '_createCappedCollection',\n  'dropCollection',\n  'dropIndex',\n  /**\n   * @summary Creates the specified index on the collection.\n   * @locus server\n   * @method createIndexAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   * @returns {Promise}\n   */\n  'createIndex',\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOneAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Promise}\n   */\n  'findOne',\n  /**\n   * @summary Insert a document in the collection.  Returns its unique _id.\n   * @locus Anywhere\n   * @method  insertAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   * @return {Promise}\n   */\n  'insert',\n  /**\n   * @summary Remove documents from the collection\n   * @locus Anywhere\n   * @method removeAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   * @return {Promise}\n   */\n  'remove',\n  /**\n   * @summary Modify one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method updateAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   * @return {Promise}\n   */\n  'update',\n  /**\n   * @summary Modify one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsertAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @return {Promise}\n   */\n  'upsert',\n];\n\nexport const ASYNC_CURSOR_METHODS = [\n  /**\n   * @deprecated in 2.9\n   * @summary Returns the number of documents that match a query. This method is\n   *          [deprecated since MongoDB 4.0](https://www.mongodb.com/docs/v4.4/reference/command/count/);\n   *          see `Collection.countDocuments` and\n   *          `Collection.estimatedDocumentCount` for a replacement.\n   * @memberOf Mongo.Cursor\n   * @method  countAsync\n   * @instance\n   * @locus Anywhere\n   * @returns {Promise}\n   */\n  'count',\n  /**\n   * @summary Return all matching documents as an Array.\n   * @memberOf Mongo.Cursor\n   * @method  fetchAsync\n   * @instance\n   * @locus Anywhere\n   * @returns {Promise}\n   */\n  'fetch',\n  /**\n   * @summary Call `callback` once for each matching document, sequentially and\n   *          synchronously.\n   * @locus Anywhere\n   * @method  forEachAsync\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   * @returns {Promise}\n   */\n  'forEach',\n  /**\n   * @summary Map callback over all matching documents.  Returns an Array.\n   * @locus Anywhere\n   * @method mapAsync\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   * @returns {Promise}\n   */\n  'map',\n];\n\nexport const CLIENT_ONLY_METHODS = [\"findOne\", \"insert\", \"remove\", \"update\", \"upsert\"];\n", "import LocalCollection from './local_collection.js';\nimport { hasOwn } from './common.js';\nimport { ASYNC_CURSOR_METHODS, getAsyncMethodName } from './constants';\n\n// Cursor: a specification for a particular subset of documents, w/ a defined\n// order, limit, and offset.  creating a Cursor with LocalCollection.find(),\nexport default class Cursor {\n  // don't call this ctor directly.  use LocalCollection.find().\n  constructor(collection, selector, options = {}) {\n    this.collection = collection;\n    this.sorter = null;\n    this.matcher = new Minimongo.Matcher(selector);\n\n    if (LocalCollection._selectorIsIdPerhapsAsObject(selector)) {\n      // stash for fast _id and { _id }\n      this._selectorId = hasOwn.call(selector, '_id') ? selector._id : selector;\n    } else {\n      this._selectorId = undefined;\n\n      if (this.matcher.hasGeoQuery() || options.sort) {\n        this.sorter = new Minimongo.Sorter(options.sort || []);\n      }\n    }\n\n    this.skip = options.skip || 0;\n    this.limit = options.limit;\n    this.fields = options.projection || options.fields;\n\n    this._projectionFn = LocalCollection._compileProjection(this.fields || {});\n\n    this._transform = LocalCollection.wrapTransform(options.transform);\n\n    // by default, queries register w/ Tracker when it is available.\n    if (typeof Tracker !== 'undefined') {\n      this.reactive = options.reactive === undefined ? true : options.reactive;\n    }\n  }\n\n  /**\n   * @deprecated in 2.9\n   * @summary Returns the number of documents that match a query. This method is\n   *          [deprecated since MongoDB 4.0](https://www.mongodb.com/docs/v4.4/reference/command/count/);\n   *          see `Collection.countDocuments` and\n   *          `Collection.estimatedDocumentCount` for a replacement.\n   * @memberOf Mongo.Cursor\n   * @method  count\n   * @instance\n   * @locus Anywhere\n   * @returns {Number}\n   */\n  count() {\n    if (this.reactive) {\n      // allow the observe to be unordered\n      this._depend({ added: true, removed: true }, true);\n    }\n\n    return this._getRawObjects({\n      ordered: true,\n    }).length;\n  }\n\n  /**\n   * @summary Return all matching documents as an Array.\n   * @memberOf Mongo.Cursor\n   * @method  fetch\n   * @instance\n   * @locus Anywhere\n   * @returns {Object[]}\n   */\n  fetch() {\n    const result = [];\n\n    this.forEach(doc => {\n      result.push(doc);\n    });\n\n    return result;\n  }\n\n  [Symbol.iterator]() {\n    if (this.reactive) {\n      this._depend({\n        addedBefore: true,\n        removed: true,\n        changed: true,\n        movedBefore: true,\n      });\n    }\n\n    let index = 0;\n    const objects = this._getRawObjects({ ordered: true });\n\n    return {\n      next: () => {\n        if (index < objects.length) {\n          // This doubles as a clone operation.\n          let element = this._projectionFn(objects[index++]);\n\n          if (this._transform) element = this._transform(element);\n\n          return { value: element };\n        }\n\n        return { done: true };\n      },\n    };\n  }\n\n  [Symbol.asyncIterator]() {\n    const syncResult = this[Symbol.iterator]();\n    return {\n      async next() {\n        return Promise.resolve(syncResult.next());\n      },\n    };\n  }\n\n  /**\n   * @callback IterationCallback\n   * @param {Object} doc\n   * @param {Number} index\n   */\n  /**\n   * @summary Call `callback` once for each matching document, sequentially and\n   *          synchronously.\n   * @locus Anywhere\n   * @method  forEach\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   */\n  forEach(callback, thisArg) {\n    if (this.reactive) {\n      this._depend({\n        addedBefore: true,\n        removed: true,\n        changed: true,\n        movedBefore: true,\n      });\n    }\n\n    this._getRawObjects({ ordered: true }).forEach((element, i) => {\n      // This doubles as a clone operation.\n      element = this._projectionFn(element);\n\n      if (this._transform) {\n        element = this._transform(element);\n      }\n\n      callback.call(thisArg, element, i, this);\n    });\n  }\n\n  getTransform() {\n    return this._transform;\n  }\n\n  /**\n   * @summary Map callback over all matching documents.  Returns an Array.\n   * @locus Anywhere\n   * @method map\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   */\n  map(callback, thisArg) {\n    const result = [];\n\n    this.forEach((doc, i) => {\n      result.push(callback.call(thisArg, doc, i, this));\n    });\n\n    return result;\n  }\n\n  // options to contain:\n  //  * callbacks for observe():\n  //    - addedAt (document, atIndex)\n  //    - added (document)\n  //    - changedAt (newDocument, oldDocument, atIndex)\n  //    - changed (newDocument, oldDocument)\n  //    - removedAt (document, atIndex)\n  //    - removed (document)\n  //    - movedTo (document, oldIndex, newIndex)\n  //\n  // attributes available on returned query handle:\n  //  * stop(): end updates\n  //  * collection: the collection this query is querying\n  //\n  // iff x is a returned query handle, (x instanceof\n  // LocalCollection.ObserveHandle) is true\n  //\n  // initial results delivered through added callback\n  // XXX maybe callbacks should take a list of objects, to expose transactions?\n  // XXX maybe support field limiting (to limit what you're notified on)\n\n  /**\n   * @summary Watch a query.  Receive callbacks as the result set changes.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observe(options) {\n    return LocalCollection._observeFromObserveChanges(this, options);\n  }\n\n  /**\n   * @summary Watch a query.  Receive callbacks as the result set changes.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   */\n  observeAsync(options) {\n    return new Promise(resolve => resolve(this.observe(options)));\n  }\n\n  /**\n   * @summary Watch a query. Receive callbacks as the result set changes. Only\n   *          the differences between the old and new documents are passed to\n   *          the callbacks.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observeChanges(options) {\n    const ordered = LocalCollection._observeChangesCallbacksAreOrdered(options);\n\n    // there are several places that assume you aren't combining skip/limit with\n    // unordered observe.  eg, update's EJSON.clone, and the \"there are several\"\n    // comment in _modifyAndNotify\n    // XXX allow skip/limit with unordered observe\n    if (!options._allow_unordered && !ordered && (this.skip || this.limit)) {\n      throw new Error(\n        \"Must use an ordered observe with skip or limit (i.e. 'addedBefore' \" +\n          \"for observeChanges or 'addedAt' for observe, instead of 'added').\"\n      );\n    }\n\n    if (this.fields && (this.fields._id === 0 || this.fields._id === false)) {\n      throw Error(\"You may not observe a cursor with {fields: {_id: 0}}\");\n    }\n\n    const distances =\n      this.matcher.hasGeoQuery() && ordered && new LocalCollection._IdMap();\n\n    const query = {\n      cursor: this,\n      dirty: false,\n      distances,\n      matcher: this.matcher, // not fast pathed\n      ordered,\n      projectionFn: this._projectionFn,\n      resultsSnapshot: null,\n      sorter: ordered && this.sorter,\n    };\n\n    let qid;\n\n    // Non-reactive queries call added[Before] and then never call anything\n    // else.\n    if (this.reactive) {\n      qid = this.collection.next_qid++;\n      this.collection.queries[qid] = query;\n    }\n\n    query.results = this._getRawObjects({\n      ordered,\n      distances: query.distances,\n    });\n\n    if (this.collection.paused) {\n      query.resultsSnapshot = ordered ? [] : new LocalCollection._IdMap();\n    }\n\n    // wrap callbacks we were passed. callbacks only fire when not paused and\n    // are never undefined\n    // Filters out blacklisted fields according to cursor's projection.\n    // XXX wrong place for this?\n\n    // furthermore, callbacks enqueue until the operation we're working on is\n    // done.\n    const wrapCallback = (fn) => {\n      if (!fn) {\n        return () => {};\n      }\n\n      const self = this;\n\n      return function (/* args*/) {\n        if (self.collection.paused) {\n          return;\n        }\n\n        const args = arguments;\n\n        self.collection._observeQueue.queueTask(() => {\n          fn.apply(this, args);\n        });\n      };\n    };\n\n    query.added = wrapCallback(options.added);\n    query.changed = wrapCallback(options.changed);\n    query.removed = wrapCallback(options.removed);\n\n    if (ordered) {\n      query.addedBefore = wrapCallback(options.addedBefore);\n      query.movedBefore = wrapCallback(options.movedBefore);\n    }\n\n    if (!options._suppress_initial && !this.collection.paused) {\n      const handler = (doc) => {\n        const fields = EJSON.clone(doc);\n\n        delete fields._id;\n\n        if (ordered) {\n          query.addedBefore(doc._id, this._projectionFn(fields), null);\n        }\n\n        query.added(doc._id, this._projectionFn(fields));\n      };\n      // it means it's just an array\n      if (query.results.length) {\n        for (const doc of query.results) {\n          handler(doc);\n        }\n      }\n      // it means it's an id map\n      if (query.results?.size?.()) {\n        query.results.forEach(handler);\n      }\n    }\n\n    const handle = Object.assign(new LocalCollection.ObserveHandle(), {\n      collection: this.collection,\n      stop: () => {\n        if (this.reactive) {\n          delete this.collection.queries[qid];\n        }\n      },\n      isReady: false,\n      isReadyPromise: null,\n    });\n\n    if (this.reactive && Tracker.active) {\n      // XXX in many cases, the same observe will be recreated when\n      // the current autorun is rerun.  we could save work by\n      // letting it linger across rerun and potentially get\n      // repurposed if the same observe is performed, using logic\n      // similar to that of Meteor.subscribe.\n      Tracker.onInvalidate(() => {\n        handle.stop();\n      });\n    }\n\n    // run the observe callbacks resulting from the initial contents\n    // before we leave the observe.\n    const drainResult = this.collection._observeQueue.drain();\n\n    if (drainResult instanceof Promise) {\n      handle.isReadyPromise = drainResult;\n      drainResult.then(() => (handle.isReady = true));\n    } else {\n      handle.isReady = true;\n      handle.isReadyPromise = Promise.resolve();\n    }\n\n    return handle;\n  }\n\n  /**\n   * @summary Watch a query. Receive callbacks as the result set changes. Only\n   *          the differences between the old and new documents are passed to\n   *          the callbacks.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observeChangesAsync(options) {\n    return new Promise((resolve) => {\n      const handle = this.observeChanges(options);\n      handle.isReadyPromise.then(() => resolve(handle));\n    });\n  }\n\n  // XXX Maybe we need a version of observe that just calls a callback if\n  // anything changed.\n  _depend(changers, _allow_unordered) {\n    if (Tracker.active) {\n      const dependency = new Tracker.Dependency();\n      const notify = dependency.changed.bind(dependency);\n\n      dependency.depend();\n\n      const options = { _allow_unordered, _suppress_initial: true };\n\n      ['added', 'addedBefore', 'changed', 'movedBefore', 'removed'].forEach(\n        fn => {\n          if (changers[fn]) {\n            options[fn] = notify;\n          }\n        }\n      );\n\n      // observeChanges will stop() when this computation is invalidated\n      this.observeChanges(options);\n    }\n  }\n\n  _getCollectionName() {\n    return this.collection.name;\n  }\n\n  // Returns a collection of matching objects, but doesn't deep copy them.\n  //\n  // If ordered is set, returns a sorted array, respecting sorter, skip, and\n  // limit properties of the query provided that options.applySkipLimit is\n  // not set to false (#1201). If sorter is falsey, no sort -- you get the\n  // natural order.\n  //\n  // If ordered is not set, returns an object mapping from ID to doc (sorter,\n  // skip and limit should not be set).\n  //\n  // If ordered is set and this cursor is a $near geoquery, then this function\n  // will use an _IdMap to track each distance from the $near argument point in\n  // order to use it as a sort key. If an _IdMap is passed in the 'distances'\n  // argument, this function will clear it and use it for this purpose\n  // (otherwise it will just create its own _IdMap). The observeChanges\n  // implementation uses this to remember the distances after this function\n  // returns.\n  _getRawObjects(options = {}) {\n    // By default this method will respect skip and limit because .fetch(),\n    // .forEach() etc... expect this behaviour. It can be forced to ignore\n    // skip and limit by setting applySkipLimit to false (.count() does this,\n    // for example)\n    const applySkipLimit = options.applySkipLimit !== false;\n\n    // XXX use OrderedDict instead of array, and make IdMap and OrderedDict\n    // compatible\n    const results = options.ordered ? [] : new LocalCollection._IdMap();\n\n    // fast path for single ID value\n    if (this._selectorId !== undefined) {\n      // If you have non-zero skip and ask for a single id, you get nothing.\n      // This is so it matches the behavior of the '{_id: foo}' path.\n      if (applySkipLimit && this.skip) {\n        return results;\n      }\n\n      const selectedDoc = this.collection._docs.get(this._selectorId);\n      if (selectedDoc) {\n        if (options.ordered) {\n          results.push(selectedDoc);\n        } else {\n          results.set(this._selectorId, selectedDoc);\n        }\n      }\n      return results;\n    }\n\n    // slow path for arbitrary selector, sort, skip, limit\n\n    // in the observeChanges case, distances is actually part of the \"query\"\n    // (ie, live results set) object.  in other cases, distances is only used\n    // inside this function.\n    let distances;\n    if (this.matcher.hasGeoQuery() && options.ordered) {\n      if (options.distances) {\n        distances = options.distances;\n        distances.clear();\n      } else {\n        distances = new LocalCollection._IdMap();\n      }\n    }\n\n    Meteor._runFresh(() => {\n      this.collection._docs.forEach((doc, id) => {\n        const matchResult = this.matcher.documentMatches(doc);\n        if (matchResult.result) {\n          if (options.ordered) {\n            results.push(doc);\n\n            if (distances && matchResult.distance !== undefined) {\n              distances.set(id, matchResult.distance);\n            }\n          } else {\n            results.set(id, doc);\n          }\n        }\n\n        // Override to ensure all docs are matched if ignoring skip & limit\n        if (!applySkipLimit) {\n          return true;\n        }\n\n        // Fast path for limited unsorted queries.\n        // XXX 'length' check here seems wrong for ordered\n        return (\n          !this.limit || this.skip || this.sorter || results.length !== this.limit\n        );\n      });\n    });\n\n    if (!options.ordered) {\n      return results;\n    }\n\n    if (this.sorter) {\n      results.sort(this.sorter.getComparator({ distances }));\n    }\n\n    // Return the full set of results if there is no skip or limit or if we're\n    // ignoring them\n    if (!applySkipLimit || (!this.limit && !this.skip)) {\n      return results;\n    }\n\n    return results.slice(\n      this.skip,\n      this.limit ? this.limit + this.skip : results.length\n    );\n  }\n\n  _publishCursor(subscription) {\n    // XXX minimongo should not depend on mongo-livedata!\n    if (!Package.mongo) {\n      throw new Error(\n        \"Can't publish from Minimongo without the `mongo` package.\"\n      );\n    }\n\n    if (!this.collection.name) {\n      throw new Error(\n        \"Can't publish a cursor from a collection without a name.\"\n      );\n    }\n\n    return Package.mongo.Mongo.Collection._publishCursor(\n      this,\n      subscription,\n      this.collection.name\n    );\n  }\n}\n\n// Implements async version of cursor methods to keep collections isomorphic\nASYNC_CURSOR_METHODS.forEach(method => {\n  const asyncName = getAsyncMethodName(method);\n  Cursor.prototype[asyncName] = function(...args) {\n    try {\n      return Promise.resolve(this[method].apply(this, args));\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  };\n});\n", "import Cursor from './cursor.js';\nimport Observe<PERSON><PERSON><PERSON> from './observe_handle.js';\nimport {\n  hasOwn,\n  isIndexable,\n  isNumericKey,\n  isOperatorObject,\n  populateDocumentWithQueryFields,\n  projectionDetails,\n} from './common.js';\n\nimport { getAsyncMethodName } from './constants';\n\n// XXX type checking on selectors (graceful error if malformed)\n\n// LocalCollection: a set of documents that supports queries and modifiers.\nexport default class LocalCollection {\n  constructor(name) {\n    this.name = name;\n    // _id -> document (also containing id)\n    this._docs = new LocalCollection._IdMap;\n\n    this._observeQueue = Meteor.isClient\n      ? new Meteor._SynchronousQueue()\n      : new Meteor._AsynchronousQueue();\n\n    this.next_qid = 1; // live query id generator\n\n    // qid -> live query object. keys:\n    //  ordered: bool. ordered queries have addedBefore/movedBefore callbacks.\n    //  results: array (ordered) or object (unordered) of current results\n    //    (aliased with this._docs!)\n    //  resultsSnapshot: snapshot of results. null if not paused.\n    //  cursor: Cursor object for the query.\n    //  selector, sorter, (callbacks): functions\n    this.queries = Object.create(null);\n\n    // null if not saving originals; an IdMap from id to original document value\n    // if saving originals. See comments before saveOriginals().\n    this._savedOriginals = null;\n\n    // True when observers are paused and we should not send callbacks.\n    this.paused = false;\n  }\n\n  countDocuments(selector, options) {\n    return this.find(selector ?? {}, options).countAsync();\n  }\n\n  estimatedDocumentCount(options) {\n    return this.find({}, options).countAsync();\n  }\n\n  // options may include sort, skip, limit, reactive\n  // sort may be any of these forms:\n  //     {a: 1, b: -1}\n  //     [[\"a\", \"asc\"], [\"b\", \"desc\"]]\n  //     [\"a\", [\"b\", \"desc\"]]\n  //   (in the first form you're beholden to key enumeration order in\n  //   your javascript VM)\n  //\n  // reactive: if given, and false, don't register with Tracker (default\n  // is true)\n  //\n  // XXX possibly should support retrieving a subset of fields? and\n  // have it be a hint (ignored on the client, when not copying the\n  // doc?)\n  //\n  // XXX sort does not yet support subkeys ('a.b') .. fix that!\n  // XXX add one more sort form: \"key\"\n  // XXX tests\n  find(selector, options) {\n    // default syntax for everything is to omit the selector argument.\n    // but if selector is explicitly passed in as false or undefined, we\n    // want a selector that matches nothing.\n    if (arguments.length === 0) {\n      selector = {};\n    }\n\n    return new LocalCollection.Cursor(this, selector, options);\n  }\n\n  findOne(selector, options = {}) {\n    if (arguments.length === 0) {\n      selector = {};\n    }\n\n    // NOTE: by setting limit 1 here, we end up using very inefficient\n    // code that recomputes the whole query on each update. The upside is\n    // that when you reactively depend on a findOne you only get\n    // invalidated when the found object changes, not any object in the\n    // collection. Most findOne will be by id, which has a fast path, so\n    // this might not be a big deal. In most cases, invalidation causes\n    // the called to re-query anyway, so this should be a net performance\n    // improvement.\n    options.limit = 1;\n\n    return this.find(selector, options).fetch()[0];\n  }\n  async findOneAsync(selector, options = {}) {\n    if (arguments.length === 0) {\n      selector = {};\n    }\n    options.limit = 1;\n    return (await this.find(selector, options).fetchAsync())[0];\n  }\n  prepareInsert(doc) {\n    assertHasValidFieldNames(doc);\n\n    // if you really want to use ObjectIDs, set this global.\n    // Mongo.Collection specifies its own ids and does not use this code.\n    if (!hasOwn.call(doc, '_id')) {\n      doc._id = LocalCollection._useOID ? new MongoID.ObjectID() : Random.id();\n    }\n\n    const id = doc._id;\n\n    if (this._docs.has(id)) {\n      throw MinimongoError(`Duplicate _id '${id}'`);\n    }\n\n    this._saveOriginal(id, undefined);\n    this._docs.set(id, doc);\n\n    return id;\n  }\n\n  // XXX possibly enforce that 'undefined' does not appear (we assume\n  // this in our handling of null and $exists)\n  insert(doc, callback) {\n    doc = EJSON.clone(doc);\n    const id = this.prepareInsert(doc);\n    const queriesToRecompute = [];\n\n    // trigger live queries that match\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const matchResult = query.matcher.documentMatches(doc);\n\n      if (matchResult.result) {\n        if (query.distances && matchResult.distance !== undefined) {\n          query.distances.set(id, matchResult.distance);\n        }\n\n        if (query.cursor.skip || query.cursor.limit) {\n          queriesToRecompute.push(qid);\n        } else {\n          LocalCollection._insertInResultsSync(query, doc);\n        }\n      }\n    }\n\n    queriesToRecompute.forEach(qid => {\n      if (this.queries[qid]) {\n        this._recomputeResults(this.queries[qid]);\n      }\n    });\n\n    this._observeQueue.drain();\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, id);\n      });\n    }\n\n    return id;\n  }\n  async insertAsync(doc, callback) {\n    doc = EJSON.clone(doc);\n    const id = this.prepareInsert(doc);\n    const queriesToRecompute = [];\n\n    // trigger live queries that match\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const matchResult = query.matcher.documentMatches(doc);\n\n      if (matchResult.result) {\n        if (query.distances && matchResult.distance !== undefined) {\n          query.distances.set(id, matchResult.distance);\n        }\n\n        if (query.cursor.skip || query.cursor.limit) {\n          queriesToRecompute.push(qid);\n        } else {\n          await LocalCollection._insertInResultsAsync(query, doc);\n        }\n      }\n    }\n\n    queriesToRecompute.forEach(qid => {\n      if (this.queries[qid]) {\n        this._recomputeResults(this.queries[qid]);\n      }\n    });\n\n    await this._observeQueue.drain();\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, id);\n      });\n    }\n\n    return id;\n  }\n\n  // Pause the observers. No callbacks from observers will fire until\n  // 'resumeObservers' is called.\n  pauseObservers() {\n    // No-op if already paused.\n    if (this.paused) {\n      return;\n    }\n\n    // Set the 'paused' flag such that new observer messages don't fire.\n    this.paused = true;\n\n    // Take a snapshot of the query results for each query.\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n      query.resultsSnapshot = EJSON.clone(query.results);\n    });\n  }\n\n  clearResultQueries(callback) {\n    const result = this._docs.size();\n\n    this._docs.clear();\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.ordered) {\n        query.results = [];\n      } else {\n        query.results.clear();\n      }\n    });\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n\n  prepareRemove(selector) {\n    const matcher = new Minimongo.Matcher(selector);\n    const remove = [];\n\n    this._eachPossiblyMatchingDocSync(selector, (doc, id) => {\n      if (matcher.documentMatches(doc).result) {\n        remove.push(id);\n      }\n    });\n\n    const queriesToRecompute = [];\n    const queryRemove = [];\n\n    for (let i = 0; i < remove.length; i++) {\n      const removeId = remove[i];\n      const removeDoc = this._docs.get(removeId);\n\n      Object.keys(this.queries).forEach(qid => {\n        const query = this.queries[qid];\n\n        if (query.dirty) {\n          return;\n        }\n\n        if (query.matcher.documentMatches(removeDoc).result) {\n          if (query.cursor.skip || query.cursor.limit) {\n            queriesToRecompute.push(qid);\n          } else {\n            queryRemove.push({qid, doc: removeDoc});\n          }\n        }\n      });\n\n      this._saveOriginal(removeId, removeDoc);\n      this._docs.remove(removeId);\n    }\n\n    return { queriesToRecompute, queryRemove, remove };\n  }\n\n  remove(selector, callback) {\n    // Easy special case: if we're not calling observeChanges callbacks and\n    // we're not saving originals and we got asked to remove everything, then\n    // just empty everything directly.\n    if (this.paused && !this._savedOriginals && EJSON.equals(selector, {})) {\n      return this.clearResultQueries(callback);\n    }\n\n    const { queriesToRecompute, queryRemove, remove } = this.prepareRemove(selector);\n\n    // run live query callbacks _after_ we've removed the documents.\n    queryRemove.forEach(remove => {\n      const query = this.queries[remove.qid];\n\n      if (query) {\n        query.distances && query.distances.remove(remove.doc._id);\n        LocalCollection._removeFromResultsSync(query, remove.doc);\n      }\n    });\n\n    queriesToRecompute.forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query);\n      }\n    });\n\n    this._observeQueue.drain();\n\n    const result = remove.length;\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  async removeAsync(selector, callback) {\n    // Easy special case: if we're not calling observeChanges callbacks and\n    // we're not saving originals and we got asked to remove everything, then\n    // just empty everything directly.\n    if (this.paused && !this._savedOriginals && EJSON.equals(selector, {})) {\n      return this.clearResultQueries(callback);\n    }\n\n    const { queriesToRecompute, queryRemove, remove } = this.prepareRemove(selector);\n\n    // run live query callbacks _after_ we've removed the documents.\n    for (const remove of queryRemove) {\n      const query = this.queries[remove.qid];\n\n      if (query) {\n        query.distances && query.distances.remove(remove.doc._id);\n        await LocalCollection._removeFromResultsAsync(query, remove.doc);\n      }\n    }\n    queriesToRecompute.forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query);\n      }\n    });\n\n    await this._observeQueue.drain();\n\n    const result = remove.length;\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  // Resume the observers. Observers immediately receive change\n  // notifications to bring them to the current state of the\n  // database. Note that this is not just replaying all the changes that\n  // happened during the pause, it is a smarter 'coalesced' diff.\n  _resumeObservers() {\n    // No-op if not paused.\n    if (!this.paused) {\n      return;\n    }\n\n    // Unset the 'paused' flag. Make sure to do this first, otherwise\n    // observer methods won't actually fire when we trigger them.\n    this.paused = false;\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        query.dirty = false;\n\n        // re-compute results will perform `LocalCollection._diffQueryChanges`\n        // automatically.\n        this._recomputeResults(query, query.resultsSnapshot);\n      } else {\n        // Diff the current results against the snapshot and send to observers.\n        // pass the query object for its observer callbacks.\n        LocalCollection._diffQueryChanges(\n          query.ordered,\n          query.resultsSnapshot,\n          query.results,\n          query,\n          {projectionFn: query.projectionFn}\n        );\n      }\n\n      query.resultsSnapshot = null;\n    });\n  }\n\n  async resumeObserversServer() {\n    this._resumeObservers();\n    await this._observeQueue.drain();\n  }\n  resumeObserversClient() {\n    this._resumeObservers();\n    this._observeQueue.drain();\n  }\n\n  retrieveOriginals() {\n    if (!this._savedOriginals) {\n      throw new Error('Called retrieveOriginals without saveOriginals');\n    }\n\n    const originals = this._savedOriginals;\n\n    this._savedOriginals = null;\n\n    return originals;\n  }\n\n  // To track what documents are affected by a piece of code, call\n  // saveOriginals() before it and retrieveOriginals() after it.\n  // retrieveOriginals returns an object whose keys are the ids of the documents\n  // that were affected since the call to saveOriginals(), and the values are\n  // equal to the document's contents at the time of saveOriginals. (In the case\n  // of an inserted document, undefined is the value.) You must alternate\n  // between calls to saveOriginals() and retrieveOriginals().\n  saveOriginals() {\n    if (this._savedOriginals) {\n      throw new Error('Called saveOriginals twice without retrieveOriginals');\n    }\n\n    this._savedOriginals = new LocalCollection._IdMap;\n  }\n\n  prepareUpdate(selector) {\n    // Save the original results of any query that we might need to\n    // _recomputeResults on, because _modifyAndNotify will mutate the objects in\n    // it. (We don't need to save the original results of paused queries because\n    // they already have a resultsSnapshot and we won't be diffing in\n    // _recomputeResults.)\n    const qidToOriginalResults = {};\n\n    // We should only clone each document once, even if it appears in multiple\n    // queries\n    const docMap = new LocalCollection._IdMap;\n    const idsMatched = LocalCollection._idsMatchedBySelector(selector);\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if ((query.cursor.skip || query.cursor.limit) && ! this.paused) {\n        // Catch the case of a reactive `count()` on a cursor with skip\n        // or limit, which registers an unordered observe. This is a\n        // pretty rare case, so we just clone the entire result set with\n        // no optimizations for documents that appear in these result\n        // sets and other queries.\n        if (query.results instanceof LocalCollection._IdMap) {\n          qidToOriginalResults[qid] = query.results.clone();\n          return;\n        }\n\n        if (!(query.results instanceof Array)) {\n          throw new Error('Assertion failed: query.results not an array');\n        }\n\n        // Clones a document to be stored in `qidToOriginalResults`\n        // because it may be modified before the new and old result sets\n        // are diffed. But if we know exactly which document IDs we're\n        // going to modify, then we only need to clone those.\n        const memoizedCloneIfNeeded = doc => {\n          if (docMap.has(doc._id)) {\n            return docMap.get(doc._id);\n          }\n\n          const docToMemoize = (\n            idsMatched &&\n            !idsMatched.some(id => EJSON.equals(id, doc._id))\n          ) ? doc : EJSON.clone(doc);\n\n          docMap.set(doc._id, docToMemoize);\n\n          return docToMemoize;\n        };\n\n        qidToOriginalResults[qid] = query.results.map(memoizedCloneIfNeeded);\n      }\n    });\n\n    return qidToOriginalResults;\n  }\n\n  finishUpdate({ options, updateCount, callback, insertedId }) {\n\n\n    // Return the number of affected documents, or in the upsert case, an object\n    // containing the number of affected docs and the id of the doc that was\n    // inserted, if any.\n    let result;\n    if (options._returnObject) {\n      result = { numberAffected: updateCount };\n\n      if (insertedId !== undefined) {\n        result.insertedId = insertedId;\n      }\n    } else {\n      result = updateCount;\n    }\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  // XXX atomicity: if multi is true, and one modification fails, do\n  // we rollback the whole operation, or what?\n  async updateAsync(selector, mod, options, callback) {\n    if (! callback && options instanceof Function) {\n      callback = options;\n      options = null;\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    const matcher = new Minimongo.Matcher(selector, true);\n\n    const qidToOriginalResults = this.prepareUpdate(selector);\n\n    let recomputeQids = {};\n\n    let updateCount = 0;\n\n    await this._eachPossiblyMatchingDocAsync(selector, async (doc, id) => {\n      const queryResult = matcher.documentMatches(doc);\n\n      if (queryResult.result) {\n        // XXX Should we save the original even if mod ends up being a no-op?\n        this._saveOriginal(id, doc);\n        recomputeQids = await this._modifyAndNotifyAsync(\n          doc,\n          mod,\n          queryResult.arrayIndices\n        );\n\n        ++updateCount;\n\n        if (!options.multi) {\n          return false; // break\n        }\n      }\n\n      return true;\n    });\n\n    Object.keys(recomputeQids).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query, qidToOriginalResults[qid]);\n      }\n    });\n\n    await this._observeQueue.drain();\n\n    // If we are doing an upsert, and we didn't modify any documents yet, then\n    // it's time to do an insert. Figure out what document we are inserting, and\n    // generate an id for it.\n    let insertedId;\n    if (updateCount === 0 && options.upsert) {\n      const doc = LocalCollection._createUpsertDocument(selector, mod);\n      if (!doc._id && options.insertedId) {\n        doc._id = options.insertedId;\n      }\n\n      insertedId = await this.insertAsync(doc);\n      updateCount = 1;\n    }\n\n    return this.finishUpdate({\n      options,\n      insertedId,\n      updateCount,\n      callback,\n    });\n  }\n  // XXX atomicity: if multi is true, and one modification fails, do\n  // we rollback the whole operation, or what?\n  update(selector, mod, options, callback) {\n    if (! callback && options instanceof Function) {\n      callback = options;\n      options = null;\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    const matcher = new Minimongo.Matcher(selector, true);\n\n    const qidToOriginalResults = this.prepareUpdate(selector);\n\n    let recomputeQids = {};\n\n    let updateCount = 0;\n\n    this._eachPossiblyMatchingDocSync(selector, (doc, id) => {\n      const queryResult = matcher.documentMatches(doc);\n\n      if (queryResult.result) {\n        // XXX Should we save the original even if mod ends up being a no-op?\n        this._saveOriginal(id, doc);\n        recomputeQids = this._modifyAndNotifySync(\n          doc,\n          mod,\n          queryResult.arrayIndices\n        );\n\n        ++updateCount;\n\n        if (!options.multi) {\n          return false; // break\n        }\n      }\n\n      return true;\n    });\n\n    Object.keys(recomputeQids).forEach(qid => {\n      const query = this.queries[qid];\n      if (query) {\n        this._recomputeResults(query, qidToOriginalResults[qid]);\n      }\n    });\n\n    this._observeQueue.drain();\n\n\n    // If we are doing an upsert, and we didn't modify any documents yet, then\n    // it's time to do an insert. Figure out what document we are inserting, and\n    // generate an id for it.\n    let insertedId;\n    if (updateCount === 0 && options.upsert) {\n      const doc = LocalCollection._createUpsertDocument(selector, mod);\n      if (!doc._id && options.insertedId) {\n        doc._id = options.insertedId;\n      }\n\n      insertedId = this.insert(doc);\n      updateCount = 1;\n    }\n\n\n    return this.finishUpdate({\n      options,\n      updateCount,\n      callback,\n      selector,\n      mod,\n    });\n  }\n\n  // A convenience wrapper on update. LocalCollection.upsert(sel, mod) is\n  // equivalent to LocalCollection.update(sel, mod, {upsert: true,\n  // _returnObject: true}).\n  upsert(selector, mod, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.update(\n      selector,\n      mod,\n      Object.assign({}, options, {upsert: true, _returnObject: true}),\n      callback\n    );\n  }\n\n  upsertAsync(selector, mod, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.updateAsync(\n      selector,\n      mod,\n      Object.assign({}, options, {upsert: true, _returnObject: true}),\n      callback\n    );\n  }\n\n  // Iterates over a subset of documents that could match selector; calls\n  // fn(doc, id) on each of them.  Specifically, if selector specifies\n  // specific _id's, it only looks at those.  doc is *not* cloned: it is the\n  // same object that is in _docs.\n  async _eachPossiblyMatchingDocAsync(selector, fn) {\n    const specificIds = LocalCollection._idsMatchedBySelector(selector);\n\n    if (specificIds) {\n      for (const id of specificIds) {\n        const doc = this._docs.get(id);\n\n        if (doc && ! (await fn(doc, id))) {\n          break\n        }\n      }\n    } else {\n      await this._docs.forEachAsync(fn);\n    }\n  }\n  _eachPossiblyMatchingDocSync(selector, fn) {\n    const specificIds = LocalCollection._idsMatchedBySelector(selector);\n\n    if (specificIds) {\n      for (const id of specificIds) {\n        const doc = this._docs.get(id);\n\n        if (doc && !fn(doc, id)) {\n          break\n        }\n      }\n    } else {\n      this._docs.forEach(fn);\n    }\n  }\n\n  _getMatchedDocAndModify(doc, mod, arrayIndices) {\n    const matched_before = {};\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        return;\n      }\n\n      if (query.ordered) {\n        matched_before[qid] = query.matcher.documentMatches(doc).result;\n      } else {\n        // Because we don't support skip or limit (yet) in unordered queries, we\n        // can just do a direct lookup.\n        matched_before[qid] = query.results.has(doc._id);\n      }\n    });\n\n    return matched_before;\n  }\n\n  _modifyAndNotifySync(doc, mod, arrayIndices) {\n\n    const matched_before = this._getMatchedDocAndModify(doc, mod, arrayIndices);\n\n    const old_doc = EJSON.clone(doc);\n    LocalCollection._modify(doc, mod, {arrayIndices});\n\n    const recomputeQids = {};\n\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const afterMatch = query.matcher.documentMatches(doc);\n      const after = afterMatch.result;\n      const before = matched_before[qid];\n\n      if (after && query.distances && afterMatch.distance !== undefined) {\n        query.distances.set(doc._id, afterMatch.distance);\n      }\n\n      if (query.cursor.skip || query.cursor.limit) {\n        // We need to recompute any query where the doc may have been in the\n        // cursor's window either before or after the update. (Note that if skip\n        // or limit is set, \"before\" and \"after\" being true do not necessarily\n        // mean that the document is in the cursor's output after skip/limit is\n        // applied... but if they are false, then the document definitely is NOT\n        // in the output. So it's safe to skip recompute if neither before or\n        // after are true.)\n        if (before || after) {\n          recomputeQids[qid] = true;\n        }\n      } else if (before && !after) {\n        LocalCollection._removeFromResultsSync(query, doc);\n      } else if (!before && after) {\n        LocalCollection._insertInResultsSync(query, doc);\n      } else if (before && after) {\n        LocalCollection._updateInResultsSync(query, doc, old_doc);\n      }\n    }\n    return recomputeQids;\n  }\n\n  async _modifyAndNotifyAsync(doc, mod, arrayIndices) {\n\n    const matched_before = this._getMatchedDocAndModify(doc, mod, arrayIndices);\n\n    const old_doc = EJSON.clone(doc);\n    LocalCollection._modify(doc, mod, {arrayIndices});\n\n    const recomputeQids = {};\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const afterMatch = query.matcher.documentMatches(doc);\n      const after = afterMatch.result;\n      const before = matched_before[qid];\n\n      if (after && query.distances && afterMatch.distance !== undefined) {\n        query.distances.set(doc._id, afterMatch.distance);\n      }\n\n      if (query.cursor.skip || query.cursor.limit) {\n        // We need to recompute any query where the doc may have been in the\n        // cursor's window either before or after the update. (Note that if skip\n        // or limit is set, \"before\" and \"after\" being true do not necessarily\n        // mean that the document is in the cursor's output after skip/limit is\n        // applied... but if they are false, then the document definitely is NOT\n        // in the output. So it's safe to skip recompute if neither before or\n        // after are true.)\n        if (before || after) {\n          recomputeQids[qid] = true;\n        }\n      } else if (before && !after) {\n        await LocalCollection._removeFromResultsAsync(query, doc);\n      } else if (!before && after) {\n        await LocalCollection._insertInResultsAsync(query, doc);\n      } else if (before && after) {\n        await LocalCollection._updateInResultsAsync(query, doc, old_doc);\n      }\n    }\n    return recomputeQids;\n  }\n\n  // Recomputes the results of a query and runs observe callbacks for the\n  // difference between the previous results and the current results (unless\n  // paused). Used for skip/limit queries.\n  //\n  // When this is used by insert or remove, it can just use query.results for\n  // the old results (and there's no need to pass in oldResults), because these\n  // operations don't mutate the documents in the collection. Update needs to\n  // pass in an oldResults which was deep-copied before the modifier was\n  // applied.\n  //\n  // oldResults is guaranteed to be ignored if the query is not paused.\n  _recomputeResults(query, oldResults) {\n    if (this.paused) {\n      // There's no reason to recompute the results now as we're still paused.\n      // By flagging the query as \"dirty\", the recompute will be performed\n      // when resumeObservers is called.\n      query.dirty = true;\n      return;\n    }\n\n    if (!this.paused && !oldResults) {\n      oldResults = query.results;\n    }\n\n    if (query.distances) {\n      query.distances.clear();\n    }\n\n    query.results = query.cursor._getRawObjects({\n      distances: query.distances,\n      ordered: query.ordered\n    });\n\n    if (!this.paused) {\n      LocalCollection._diffQueryChanges(\n        query.ordered,\n        oldResults,\n        query.results,\n        query,\n        {projectionFn: query.projectionFn}\n      );\n    }\n  }\n\n  _saveOriginal(id, doc) {\n    // Are we even trying to save originals?\n    if (!this._savedOriginals) {\n      return;\n    }\n\n    // Have we previously mutated the original (and so 'doc' is not actually\n    // original)?  (Note the 'has' check rather than truth: we store undefined\n    // here for inserted docs!)\n    if (this._savedOriginals.has(id)) {\n      return;\n    }\n\n    this._savedOriginals.set(id, EJSON.clone(doc));\n  }\n}\n\nLocalCollection.Cursor = Cursor;\n\nLocalCollection.ObserveHandle = ObserveHandle;\n\n// XXX maybe move these into another ObserveHelpers package or something\n\n// _CachingChangeObserver is an object which receives observeChanges callbacks\n// and keeps a cache of the current cursor state up to date in this.docs. Users\n// of this class should read the docs field but not modify it. You should pass\n// the \"applyChange\" field as the callbacks to the underlying observeChanges\n// call. Optionally, you can specify your own observeChanges callbacks which are\n// invoked immediately before the docs field is updated; this object is made\n// available as `this` to those callbacks.\nLocalCollection._CachingChangeObserver = class _CachingChangeObserver {\n  constructor(options = {}) {\n    const orderedFromCallbacks = (\n      options.callbacks &&\n      LocalCollection._observeChangesCallbacksAreOrdered(options.callbacks)\n    );\n\n    if (hasOwn.call(options, 'ordered')) {\n      this.ordered = options.ordered;\n\n      if (options.callbacks && options.ordered !== orderedFromCallbacks) {\n        throw Error('ordered option doesn\\'t match callbacks');\n      }\n    } else if (options.callbacks) {\n      this.ordered = orderedFromCallbacks;\n    } else {\n      throw Error('must provide ordered or callbacks');\n    }\n\n    const callbacks = options.callbacks || {};\n\n    if (this.ordered) {\n      this.docs = new OrderedDict(MongoID.idStringify);\n      this.applyChange = {\n        addedBefore: (id, fields, before) => {\n          // Take a shallow copy since the top-level properties can be changed\n          const doc = { ...fields };\n\n          doc._id = id;\n\n          if (callbacks.addedBefore) {\n            callbacks.addedBefore.call(this, id, EJSON.clone(fields), before);\n          }\n\n          // This line triggers if we provide added with movedBefore.\n          if (callbacks.added) {\n            callbacks.added.call(this, id, EJSON.clone(fields));\n          }\n\n          // XXX could `before` be a falsy ID?  Technically\n          // idStringify seems to allow for them -- though\n          // OrderedDict won't call stringify on a falsy arg.\n          this.docs.putBefore(id, doc, before || null);\n        },\n        movedBefore: (id, before) => {\n          if (callbacks.movedBefore) {\n            callbacks.movedBefore.call(this, id, before);\n          }\n\n          this.docs.moveBefore(id, before || null);\n        },\n      };\n    } else {\n      this.docs = new LocalCollection._IdMap;\n      this.applyChange = {\n        added: (id, fields) => {\n          // Take a shallow copy since the top-level properties can be changed\n          const doc = { ...fields };\n\n          if (callbacks.added) {\n            callbacks.added.call(this, id, EJSON.clone(fields));\n          }\n\n          doc._id = id;\n\n          this.docs.set(id,  doc);\n        },\n      };\n    }\n\n    // The methods in _IdMap and OrderedDict used by these callbacks are\n    // identical.\n    this.applyChange.changed = (id, fields) => {\n      const doc = this.docs.get(id);\n\n      if (!doc) {\n        throw new Error(`Unknown id for changed: ${id}`);\n      }\n\n      if (callbacks.changed) {\n        callbacks.changed.call(this, id, EJSON.clone(fields));\n      }\n\n      DiffSequence.applyChanges(doc, fields);\n    };\n\n    this.applyChange.removed = id => {\n      if (callbacks.removed) {\n        callbacks.removed.call(this, id);\n      }\n\n      this.docs.remove(id);\n    };\n  }\n};\n\nLocalCollection._IdMap = class _IdMap extends IdMap {\n  constructor() {\n    super(MongoID.idStringify, MongoID.idParse);\n  }\n};\n\n// Wrap a transform function to return objects that have the _id field\n// of the untransformed document. This ensures that subsystems such as\n// the observe-sequence package that call `observe` can keep track of\n// the documents identities.\n//\n// - Require that it returns objects\n// - If the return value has an _id field, verify that it matches the\n//   original _id field\n// - If the return value doesn't have an _id field, add it back.\nLocalCollection.wrapTransform = transform => {\n  if (!transform) {\n    return null;\n  }\n\n  // No need to doubly-wrap transforms.\n  if (transform.__wrappedTransform__) {\n    return transform;\n  }\n\n  const wrapped = doc => {\n    if (!hasOwn.call(doc, '_id')) {\n      // XXX do we ever have a transform on the oplog's collection? because that\n      // collection has no _id.\n      throw new Error('can only transform documents with _id');\n    }\n\n    const id = doc._id;\n\n    // XXX consider making tracker a weak dependency and checking\n    // Package.tracker here\n    const transformed = Tracker.nonreactive(() => transform(doc));\n\n    if (!LocalCollection._isPlainObject(transformed)) {\n      throw new Error('transform must return object');\n    }\n\n    if (hasOwn.call(transformed, '_id')) {\n      if (!EJSON.equals(transformed._id, id)) {\n        throw new Error('transformed document can\\'t have different _id');\n      }\n    } else {\n      transformed._id = id;\n    }\n\n    return transformed;\n  };\n\n  wrapped.__wrappedTransform__ = true;\n\n  return wrapped;\n};\n\n// XXX the sorted-query logic below is laughably inefficient. we'll\n// need to come up with a better datastructure for this.\n//\n// XXX the logic for observing with a skip or a limit is even more\n// laughably inefficient. we recompute the whole results every time!\n\n// This binary search puts a value between any equal values, and the first\n// lesser value.\nLocalCollection._binarySearch = (cmp, array, value) => {\n  let first = 0;\n  let range = array.length;\n\n  while (range > 0) {\n    const halfRange = Math.floor(range / 2);\n\n    if (cmp(value, array[first + halfRange]) >= 0) {\n      first += halfRange + 1;\n      range -= halfRange + 1;\n    } else {\n      range = halfRange;\n    }\n  }\n\n  return first;\n};\n\nLocalCollection._checkSupportedProjection = fields => {\n  if (fields !== Object(fields) || Array.isArray(fields)) {\n    throw MinimongoError('fields option must be an object');\n  }\n\n  Object.keys(fields).forEach(keyPath => {\n    if (keyPath.split('.').includes('$')) {\n      throw MinimongoError(\n        'Minimongo doesn\\'t support $ operator in projections yet.'\n      );\n    }\n\n    const value = fields[keyPath];\n\n    if (typeof value === 'object' &&\n        ['$elemMatch', '$meta', '$slice'].some(key =>\n          hasOwn.call(value, key)\n        )) {\n      throw MinimongoError(\n        'Minimongo doesn\\'t support operators in projections yet.'\n      );\n    }\n\n    if (![1, 0, true, false].includes(value)) {\n      throw MinimongoError(\n        'Projection values should be one of 1, 0, true, or false'\n      );\n    }\n  });\n};\n\n// Knows how to compile a fields projection to a predicate function.\n// @returns - Function: a closure that filters out an object according to the\n//            fields projection rules:\n//            @param obj - Object: MongoDB-styled document\n//            @returns - Object: a document with the fields filtered out\n//                       according to projection rules. Doesn't retain subfields\n//                       of passed argument.\nLocalCollection._compileProjection = fields => {\n  LocalCollection._checkSupportedProjection(fields);\n\n  const _idProjection = fields._id === undefined ? true : fields._id;\n  const details = projectionDetails(fields);\n\n  // returns transformed doc according to ruleTree\n  const transform = (doc, ruleTree) => {\n    // Special case for \"sets\"\n    if (Array.isArray(doc)) {\n      return doc.map(subdoc => transform(subdoc, ruleTree));\n    }\n\n    const result = details.including ? {} : EJSON.clone(doc);\n\n    Object.keys(ruleTree).forEach(key => {\n      if (doc == null || !hasOwn.call(doc, key)) {\n        return;\n      }\n\n      const rule = ruleTree[key];\n\n      if (rule === Object(rule)) {\n        // For sub-objects/subsets we branch\n        if (doc[key] === Object(doc[key])) {\n          result[key] = transform(doc[key], rule);\n        }\n      } else if (details.including) {\n        // Otherwise we don't even touch this subfield\n        result[key] = EJSON.clone(doc[key]);\n      } else {\n        delete result[key];\n      }\n    });\n\n    return doc != null ? result : doc;\n  };\n\n  return doc => {\n    const result = transform(doc, details.tree);\n\n    if (_idProjection && hasOwn.call(doc, '_id')) {\n      result._id = doc._id;\n    }\n\n    if (!_idProjection && hasOwn.call(result, '_id')) {\n      delete result._id;\n    }\n\n    return result;\n  };\n};\n\n// Calculates the document to insert in case we're doing an upsert and the\n// selector does not match any elements\nLocalCollection._createUpsertDocument = (selector, modifier) => {\n  const selectorDocument = populateDocumentWithQueryFields(selector);\n  const isModify = LocalCollection._isModificationMod(modifier);\n\n  const newDoc = {};\n\n  if (selectorDocument._id) {\n    newDoc._id = selectorDocument._id;\n    delete selectorDocument._id;\n  }\n\n  // This double _modify call is made to help with nested properties (see issue\n  // #8631). We do this even if it's a replacement for validation purposes (e.g.\n  // ambiguous id's)\n  LocalCollection._modify(newDoc, {$set: selectorDocument});\n  LocalCollection._modify(newDoc, modifier, {isInsert: true});\n\n  if (isModify) {\n    return newDoc;\n  }\n\n  // Replacement can take _id from query document\n  const replacement = Object.assign({}, modifier);\n  if (newDoc._id) {\n    replacement._id = newDoc._id;\n  }\n\n  return replacement;\n};\n\nLocalCollection._diffObjects = (left, right, callbacks) => {\n  return DiffSequence.diffObjects(left, right, callbacks);\n};\n\n// ordered: bool.\n// old_results and new_results: collections of documents.\n//    if ordered, they are arrays.\n//    if unordered, they are IdMaps\nLocalCollection._diffQueryChanges = (ordered, oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryChanges(ordered, oldResults, newResults, observer, options)\n;\n\nLocalCollection._diffQueryOrderedChanges = (oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryOrderedChanges(oldResults, newResults, observer, options)\n;\n\nLocalCollection._diffQueryUnorderedChanges = (oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryUnorderedChanges(oldResults, newResults, observer, options)\n;\n\nLocalCollection._findInOrderedResults = (query, doc) => {\n  if (!query.ordered) {\n    throw new Error('Can\\'t call _findInOrderedResults on unordered query');\n  }\n\n  for (let i = 0; i < query.results.length; i++) {\n    if (query.results[i] === doc) {\n      return i;\n    }\n  }\n\n  throw Error('object missing from query');\n};\n\n// If this is a selector which explicitly constrains the match by ID to a finite\n// number of documents, returns a list of their IDs.  Otherwise returns\n// null. Note that the selector may have other restrictions so it may not even\n// match those document!  We care about $in and $and since those are generated\n// access-controlled update and remove.\nLocalCollection._idsMatchedBySelector = selector => {\n  // Is the selector just an ID?\n  if (LocalCollection._selectorIsId(selector)) {\n    return [selector];\n  }\n\n  if (!selector) {\n    return null;\n  }\n\n  // Do we have an _id clause?\n  if (hasOwn.call(selector, '_id')) {\n    // Is the _id clause just an ID?\n    if (LocalCollection._selectorIsId(selector._id)) {\n      return [selector._id];\n    }\n\n    // Is the _id clause {_id: {$in: [\"x\", \"y\", \"z\"]}}?\n    if (selector._id\n        && Array.isArray(selector._id.$in)\n        && selector._id.$in.length\n        && selector._id.$in.every(LocalCollection._selectorIsId)) {\n      return selector._id.$in;\n    }\n\n    return null;\n  }\n\n  // If this is a top-level $and, and any of the clauses constrain their\n  // documents, then the whole selector is constrained by any one clause's\n  // constraint. (Well, by their intersection, but that seems unlikely.)\n  if (Array.isArray(selector.$and)) {\n    for (let i = 0; i < selector.$and.length; ++i) {\n      const subIds = LocalCollection._idsMatchedBySelector(selector.$and[i]);\n\n      if (subIds) {\n        return subIds;\n      }\n    }\n  }\n\n  return null;\n};\n\nLocalCollection._insertInResultsSync = (query, doc) => {\n  const fields = EJSON.clone(doc);\n\n  delete fields._id;\n\n  if (query.ordered) {\n    if (!query.sorter) {\n      query.addedBefore(doc._id, query.projectionFn(fields), null);\n      query.results.push(doc);\n    } else {\n      const i = LocalCollection._insertInSortedList(\n        query.sorter.getComparator({distances: query.distances}),\n        query.results,\n        doc\n      );\n\n      let next = query.results[i + 1];\n      if (next) {\n        next = next._id;\n      } else {\n        next = null;\n      }\n\n      query.addedBefore(doc._id, query.projectionFn(fields), next);\n    }\n\n    query.added(doc._id, query.projectionFn(fields));\n  } else {\n    query.added(doc._id, query.projectionFn(fields));\n    query.results.set(doc._id, doc);\n  }\n};\n\nLocalCollection._insertInResultsAsync = async (query, doc) => {\n  const fields = EJSON.clone(doc);\n\n  delete fields._id;\n\n  if (query.ordered) {\n    if (!query.sorter) {\n      await query.addedBefore(doc._id, query.projectionFn(fields), null);\n      query.results.push(doc);\n    } else {\n      const i = LocalCollection._insertInSortedList(\n        query.sorter.getComparator({distances: query.distances}),\n        query.results,\n        doc\n      );\n\n      let next = query.results[i + 1];\n      if (next) {\n        next = next._id;\n      } else {\n        next = null;\n      }\n\n      await query.addedBefore(doc._id, query.projectionFn(fields), next);\n    }\n\n    await query.added(doc._id, query.projectionFn(fields));\n  } else {\n    await query.added(doc._id, query.projectionFn(fields));\n    query.results.set(doc._id, doc);\n  }\n};\n\nLocalCollection._insertInSortedList = (cmp, array, value) => {\n  if (array.length === 0) {\n    array.push(value);\n    return 0;\n  }\n\n  const i = LocalCollection._binarySearch(cmp, array, value);\n\n  array.splice(i, 0, value);\n\n  return i;\n};\n\nLocalCollection._isModificationMod = mod => {\n  let isModify = false;\n  let isReplace = false;\n\n  Object.keys(mod).forEach(key => {\n    if (key.substr(0, 1) === '$') {\n      isModify = true;\n    } else {\n      isReplace = true;\n    }\n  });\n\n  if (isModify && isReplace) {\n    throw new Error(\n      'Update parameter cannot have both modifier and non-modifier fields.'\n    );\n  }\n\n  return isModify;\n};\n\n// XXX maybe this should be EJSON.isObject, though EJSON doesn't know about\n// RegExp\n// XXX note that _type(undefined) === 3!!!!\nLocalCollection._isPlainObject = x => {\n  return x && LocalCollection._f._type(x) === 3;\n};\n\n// XXX need a strategy for passing the binding of $ into this\n// function, from the compiled selector\n//\n// maybe just {key.up.to.just.before.dollarsign: array_index}\n//\n// XXX atomicity: if one modification fails, do we roll back the whole\n// change?\n//\n// options:\n//   - isInsert is set when _modify is being called to compute the document to\n//     insert as part of an upsert operation. We use this primarily to figure\n//     out when to set the fields in $setOnInsert, if present.\nLocalCollection._modify = (doc, modifier, options = {}) => {\n  if (!LocalCollection._isPlainObject(modifier)) {\n    throw MinimongoError('Modifier must be an object');\n  }\n\n  // Make sure the caller can't mutate our data structures.\n  modifier = EJSON.clone(modifier);\n\n  const isModifier = isOperatorObject(modifier);\n  const newDoc = isModifier ? EJSON.clone(doc) : modifier;\n\n  if (isModifier) {\n    // apply modifiers to the doc.\n    Object.keys(modifier).forEach(operator => {\n      // Treat $setOnInsert as $set if this is an insert.\n      const setOnInsert = options.isInsert && operator === '$setOnInsert';\n      const modFunc = MODIFIERS[setOnInsert ? '$set' : operator];\n      const operand = modifier[operator];\n\n      if (!modFunc) {\n        throw MinimongoError(`Invalid modifier specified ${operator}`);\n      }\n\n      Object.keys(operand).forEach(keypath => {\n        const arg = operand[keypath];\n\n        if (keypath === '') {\n          throw MinimongoError('An empty update path is not valid.');\n        }\n\n        const keyparts = keypath.split('.');\n\n        if (!keyparts.every(Boolean)) {\n          throw MinimongoError(\n            `The update path '${keypath}' contains an empty field name, ` +\n            'which is not allowed.'\n          );\n        }\n\n        const target = findModTarget(newDoc, keyparts, {\n          arrayIndices: options.arrayIndices,\n          forbidArray: operator === '$rename',\n          noCreate: NO_CREATE_MODIFIERS[operator]\n        });\n\n        modFunc(target, keyparts.pop(), arg, keypath, newDoc);\n      });\n    });\n\n    if (doc._id && !EJSON.equals(doc._id, newDoc._id)) {\n      throw MinimongoError(\n        `After applying the update to the document {_id: \"${doc._id}\", ...},` +\n        ' the (immutable) field \\'_id\\' was found to have been altered to ' +\n        `_id: \"${newDoc._id}\"`\n      );\n    }\n  } else {\n    if (doc._id && modifier._id && !EJSON.equals(doc._id, modifier._id)) {\n      throw MinimongoError(\n        `The _id field cannot be changed from {_id: \"${doc._id}\"} to ` +\n        `{_id: \"${modifier._id}\"}`\n      );\n    }\n\n    // replace the whole document\n    assertHasValidFieldNames(modifier);\n  }\n\n  // move new document into place.\n  Object.keys(doc).forEach(key => {\n    // Note: this used to be for (var key in doc) however, this does not\n    // work right in Opera. Deleting from a doc while iterating over it\n    // would sometimes cause opera to skip some keys.\n    if (key !== '_id') {\n      delete doc[key];\n    }\n  });\n\n  Object.keys(newDoc).forEach(key => {\n    doc[key] = newDoc[key];\n  });\n};\n\nLocalCollection._observeFromObserveChanges = (cursor, observeCallbacks) => {\n  const transform = cursor.getTransform() || (doc => doc);\n  let suppressed = !!observeCallbacks._suppress_initial;\n\n  let observeChangesCallbacks;\n  if (LocalCollection._observeCallbacksAreOrdered(observeCallbacks)) {\n    // The \"_no_indices\" option sets all index arguments to -1 and skips the\n    // linear scans required to generate them.  This lets observers that don't\n    // need absolute indices benefit from the other features of this API --\n    // relative order, transforms, and applyChanges -- without the speed hit.\n    const indices = !observeCallbacks._no_indices;\n\n    observeChangesCallbacks = {\n      addedBefore(id, fields, before) {\n        const check = suppressed || !(observeCallbacks.addedAt || observeCallbacks.added)\n        if (check) {\n          return;\n        }\n\n        const doc = transform(Object.assign(fields, {_id: id}));\n\n        if (observeCallbacks.addedAt) {\n          observeCallbacks.addedAt(\n              doc,\n              indices\n                  ? before\n                      ? this.docs.indexOf(before)\n                      : this.docs.size()\n                  : -1,\n              before\n          );\n        } else {\n          observeCallbacks.added(doc);\n        }\n      },\n      changed(id, fields) {\n\n        if (!(observeCallbacks.changedAt || observeCallbacks.changed)) {\n          return;\n        }\n\n        let doc = EJSON.clone(this.docs.get(id));\n        if (!doc) {\n          throw new Error(`Unknown id for changed: ${id}`);\n        }\n\n        const oldDoc = transform(EJSON.clone(doc));\n\n        DiffSequence.applyChanges(doc, fields);\n\n        if (observeCallbacks.changedAt) {\n          observeCallbacks.changedAt(\n              transform(doc),\n              oldDoc,\n              indices ? this.docs.indexOf(id) : -1\n          );\n        } else {\n          observeCallbacks.changed(transform(doc), oldDoc);\n        }\n      },\n      movedBefore(id, before) {\n        if (!observeCallbacks.movedTo) {\n          return;\n        }\n\n        const from = indices ? this.docs.indexOf(id) : -1;\n        let to = indices\n            ? before\n                ? this.docs.indexOf(before)\n                : this.docs.size()\n            : -1;\n\n        // When not moving backwards, adjust for the fact that removing the\n        // document slides everything back one slot.\n        if (to > from) {\n          --to;\n        }\n\n        observeCallbacks.movedTo(\n            transform(EJSON.clone(this.docs.get(id))),\n            from,\n            to,\n            before || null\n        );\n      },\n      removed(id) {\n        if (!(observeCallbacks.removedAt || observeCallbacks.removed)) {\n          return;\n        }\n\n        // technically maybe there should be an EJSON.clone here, but it's about\n        // to be removed from this.docs!\n        const doc = transform(this.docs.get(id));\n\n        if (observeCallbacks.removedAt) {\n          observeCallbacks.removedAt(doc, indices ? this.docs.indexOf(id) : -1);\n        } else {\n          observeCallbacks.removed(doc);\n        }\n      },\n    };\n  } else {\n    observeChangesCallbacks = {\n      added(id, fields) {\n        if (!suppressed && observeCallbacks.added) {\n          observeCallbacks.added(transform(Object.assign(fields, {_id: id})));\n        }\n      },\n      changed(id, fields) {\n        if (observeCallbacks.changed) {\n          const oldDoc = this.docs.get(id);\n          const doc = EJSON.clone(oldDoc);\n\n          DiffSequence.applyChanges(doc, fields);\n\n          observeCallbacks.changed(\n              transform(doc),\n              transform(EJSON.clone(oldDoc))\n          );\n        }\n      },\n      removed(id) {\n        if (observeCallbacks.removed) {\n          observeCallbacks.removed(transform(this.docs.get(id)));\n        }\n      },\n    };\n  }\n\n  const changeObserver = new LocalCollection._CachingChangeObserver({\n    callbacks: observeChangesCallbacks\n  });\n\n  // CachingChangeObserver clones all received input on its callbacks\n  // So we can mark it as safe to reduce the ejson clones.\n  // This is tested by the `mongo-livedata - (extended) scribbling` tests\n  changeObserver.applyChange._fromObserve = true;\n  const handle = cursor.observeChanges(changeObserver.applyChange,\n      { nonMutatingCallbacks: true });\n\n  // If needed, re-enable callbacks as soon as the initial batch is ready.\n  const setSuppressed = (h) => {\n    if (h.isReady) suppressed = false;\n    else h.isReadyPromise?.then(() => (suppressed = false));\n  };\n  // When we call cursor.observeChanges() it can be the on from\n  // the mongo package (instead of the minimongo one) and it doesn't have isReady and isReadyPromise\n  if (Meteor._isPromise(handle)) {\n    handle.then(setSuppressed);\n  } else {\n    setSuppressed(handle);\n  }\n  return handle;\n};\n\nLocalCollection._observeCallbacksAreOrdered = callbacks => {\n  if (callbacks.added && callbacks.addedAt) {\n    throw new Error('Please specify only one of added() and addedAt()');\n  }\n\n  if (callbacks.changed && callbacks.changedAt) {\n    throw new Error('Please specify only one of changed() and changedAt()');\n  }\n\n  if (callbacks.removed && callbacks.removedAt) {\n    throw new Error('Please specify only one of removed() and removedAt()');\n  }\n\n  return !!(\n    callbacks.addedAt ||\n    callbacks.changedAt ||\n    callbacks.movedTo ||\n    callbacks.removedAt\n  );\n};\n\nLocalCollection._observeChangesCallbacksAreOrdered = callbacks => {\n  if (callbacks.added && callbacks.addedBefore) {\n    throw new Error('Please specify only one of added() and addedBefore()');\n  }\n\n  return !!(callbacks.addedBefore || callbacks.movedBefore);\n};\n\nLocalCollection._removeFromResultsSync = (query, doc) => {\n  if (query.ordered) {\n    const i = LocalCollection._findInOrderedResults(query, doc);\n\n    query.removed(doc._id);\n    query.results.splice(i, 1);\n  } else {\n    const id = doc._id;  // in case callback mutates doc\n\n    query.removed(doc._id);\n    query.results.remove(id);\n  }\n};\n\nLocalCollection._removeFromResultsAsync = async (query, doc) => {\n  if (query.ordered) {\n    const i = LocalCollection._findInOrderedResults(query, doc);\n\n    await query.removed(doc._id);\n    query.results.splice(i, 1);\n  } else {\n    const id = doc._id;  // in case callback mutates doc\n\n    await query.removed(doc._id);\n    query.results.remove(id);\n  }\n};\n\n// Is this selector just shorthand for lookup by _id?\nLocalCollection._selectorIsId = selector =>\n  typeof selector === 'number' ||\n  typeof selector === 'string' ||\n  selector instanceof MongoID.ObjectID\n;\n\n// Is the selector just lookup by _id (shorthand or not)?\nLocalCollection._selectorIsIdPerhapsAsObject = selector =>\n  LocalCollection._selectorIsId(selector) ||\n  LocalCollection._selectorIsId(selector && selector._id) &&\n  Object.keys(selector).length === 1\n;\n\nLocalCollection._updateInResultsSync = (query, doc, old_doc) => {\n  if (!EJSON.equals(doc._id, old_doc._id)) {\n    throw new Error('Can\\'t change a doc\\'s _id while updating');\n  }\n\n  const projectionFn = query.projectionFn;\n  const changedFields = DiffSequence.makeChangedFields(\n    projectionFn(doc),\n    projectionFn(old_doc)\n  );\n\n  if (!query.ordered) {\n    if (Object.keys(changedFields).length) {\n      query.changed(doc._id, changedFields);\n      query.results.set(doc._id, doc);\n    }\n\n    return;\n  }\n\n  const old_idx = LocalCollection._findInOrderedResults(query, doc);\n\n  if (Object.keys(changedFields).length) {\n    query.changed(doc._id, changedFields);\n  }\n\n  if (!query.sorter) {\n    return;\n  }\n\n  // just take it out and put it back in again, and see if the index changes\n  query.results.splice(old_idx, 1);\n\n  const new_idx = LocalCollection._insertInSortedList(\n    query.sorter.getComparator({distances: query.distances}),\n    query.results,\n    doc\n  );\n\n  if (old_idx !== new_idx) {\n    let next = query.results[new_idx + 1];\n    if (next) {\n      next = next._id;\n    } else {\n      next = null;\n    }\n\n    query.movedBefore && query.movedBefore(doc._id, next);\n  }\n};\n\nLocalCollection._updateInResultsAsync = async (query, doc, old_doc) => {\n  if (!EJSON.equals(doc._id, old_doc._id)) {\n    throw new Error('Can\\'t change a doc\\'s _id while updating');\n  }\n\n  const projectionFn = query.projectionFn;\n  const changedFields = DiffSequence.makeChangedFields(\n    projectionFn(doc),\n    projectionFn(old_doc)\n  );\n\n  if (!query.ordered) {\n    if (Object.keys(changedFields).length) {\n      await query.changed(doc._id, changedFields);\n      query.results.set(doc._id, doc);\n    }\n\n    return;\n  }\n\n  const old_idx = LocalCollection._findInOrderedResults(query, doc);\n\n  if (Object.keys(changedFields).length) {\n    await query.changed(doc._id, changedFields);\n  }\n\n  if (!query.sorter) {\n    return;\n  }\n\n  // just take it out and put it back in again, and see if the index changes\n  query.results.splice(old_idx, 1);\n\n  const new_idx = LocalCollection._insertInSortedList(\n    query.sorter.getComparator({distances: query.distances}),\n    query.results,\n    doc\n  );\n\n  if (old_idx !== new_idx) {\n    let next = query.results[new_idx + 1];\n    if (next) {\n      next = next._id;\n    } else {\n      next = null;\n    }\n\n    query.movedBefore && await query.movedBefore(doc._id, next);\n  }\n};\n\nconst MODIFIERS = {\n  $currentDate(target, field, arg) {\n    if (typeof arg === 'object' && hasOwn.call(arg, '$type')) {\n      if (arg.$type !== 'date') {\n        throw MinimongoError(\n          'Minimongo does currently only support the date type in ' +\n          '$currentDate modifiers',\n          {field}\n        );\n      }\n    } else if (arg !== true) {\n      throw MinimongoError('Invalid $currentDate modifier', {field});\n    }\n\n    target[field] = new Date();\n  },\n  $inc(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $inc allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $inc modifier to non-number',\n          {field}\n        );\n      }\n\n      target[field] += arg;\n    } else {\n      target[field] = arg;\n    }\n  },\n  $min(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $min allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $min modifier to non-number',\n          {field}\n        );\n      }\n\n      if (target[field] > arg) {\n        target[field] = arg;\n      }\n    } else {\n      target[field] = arg;\n    }\n  },\n  $max(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $max allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $max modifier to non-number',\n          {field}\n        );\n      }\n\n      if (target[field] < arg) {\n        target[field] = arg;\n      }\n    } else {\n      target[field] = arg;\n    }\n  },\n  $mul(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $mul allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $mul modifier to non-number',\n          {field}\n        );\n      }\n\n      target[field] *= arg;\n    } else {\n      target[field] = 0;\n    }\n  },\n  $rename(target, field, arg, keypath, doc) {\n    // no idea why mongo has this restriction..\n    if (keypath === arg) {\n      throw MinimongoError('$rename source must differ from target', {field});\n    }\n\n    if (target === null) {\n      throw MinimongoError('$rename source field invalid', {field});\n    }\n\n    if (typeof arg !== 'string') {\n      throw MinimongoError('$rename target must be a string', {field});\n    }\n\n    if (arg.includes('\\0')) {\n      // Null bytes are not allowed in Mongo field names\n      // https://docs.mongodb.com/manual/reference/limits/#Restrictions-on-Field-Names\n      throw MinimongoError(\n        'The \\'to\\' field for $rename cannot contain an embedded null byte',\n        {field}\n      );\n    }\n\n    if (target === undefined) {\n      return;\n    }\n\n    const object = target[field];\n\n    delete target[field];\n\n    const keyparts = arg.split('.');\n    const target2 = findModTarget(doc, keyparts, {forbidArray: true});\n\n    if (target2 === null) {\n      throw MinimongoError('$rename target field invalid', {field});\n    }\n\n    target2[keyparts.pop()] = object;\n  },\n  $set(target, field, arg) {\n    if (target !== Object(target)) { // not an array or an object\n      const error = MinimongoError(\n        'Cannot set property on non-object field',\n        {field}\n      );\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    if (target === null) {\n      const error = MinimongoError('Cannot set property on null', {field});\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    assertHasValidFieldNames(arg);\n\n    target[field] = arg;\n  },\n  $setOnInsert(target, field, arg) {\n    // converted to `$set` in `_modify`\n  },\n  $unset(target, field, arg) {\n    if (target !== undefined) {\n      if (target instanceof Array) {\n        if (field in target) {\n          target[field] = null;\n        }\n      } else {\n        delete target[field];\n      }\n    }\n  },\n  $push(target, field, arg) {\n    if (target[field] === undefined) {\n      target[field] = [];\n    }\n\n    if (!(target[field] instanceof Array)) {\n      throw MinimongoError('Cannot apply $push modifier to non-array', {field});\n    }\n\n    if (!(arg && arg.$each)) {\n      // Simple mode: not $each\n      assertHasValidFieldNames(arg);\n\n      target[field].push(arg);\n\n      return;\n    }\n\n    // Fancy mode: $each (and maybe $slice and $sort and $position)\n    const toPush = arg.$each;\n    if (!(toPush instanceof Array)) {\n      throw MinimongoError('$each must be an array', {field});\n    }\n\n    assertHasValidFieldNames(toPush);\n\n    // Parse $position\n    let position = undefined;\n    if ('$position' in arg) {\n      if (typeof arg.$position !== 'number') {\n        throw MinimongoError('$position must be a numeric value', {field});\n      }\n\n      // XXX should check to make sure integer\n      if (arg.$position < 0) {\n        throw MinimongoError(\n          '$position in $push must be zero or positive',\n          {field}\n        );\n      }\n\n      position = arg.$position;\n    }\n\n    // Parse $slice.\n    let slice = undefined;\n    if ('$slice' in arg) {\n      if (typeof arg.$slice !== 'number') {\n        throw MinimongoError('$slice must be a numeric value', {field});\n      }\n\n      // XXX should check to make sure integer\n      slice = arg.$slice;\n    }\n\n    // Parse $sort.\n    let sortFunction = undefined;\n    if (arg.$sort) {\n      if (slice === undefined) {\n        throw MinimongoError('$sort requires $slice to be present', {field});\n      }\n\n      // XXX this allows us to use a $sort whose value is an array, but that's\n      // actually an extension of the Node driver, so it won't work\n      // server-side. Could be confusing!\n      // XXX is it correct that we don't do geo-stuff here?\n      sortFunction = new Minimongo.Sorter(arg.$sort).getComparator();\n\n      toPush.forEach(element => {\n        if (LocalCollection._f._type(element) !== 3) {\n          throw MinimongoError(\n            '$push like modifiers using $sort require all elements to be ' +\n            'objects',\n            {field}\n          );\n        }\n      });\n    }\n\n    // Actually push.\n    if (position === undefined) {\n      toPush.forEach(element => {\n        target[field].push(element);\n      });\n    } else {\n      const spliceArguments = [position, 0];\n\n      toPush.forEach(element => {\n        spliceArguments.push(element);\n      });\n\n      target[field].splice(...spliceArguments);\n    }\n\n    // Actually sort.\n    if (sortFunction) {\n      target[field].sort(sortFunction);\n    }\n\n    // Actually slice.\n    if (slice !== undefined) {\n      if (slice === 0) {\n        target[field] = []; // differs from Array.slice!\n      } else if (slice < 0) {\n        target[field] = target[field].slice(slice);\n      } else {\n        target[field] = target[field].slice(0, slice);\n      }\n    }\n  },\n  $pushAll(target, field, arg) {\n    if (!(typeof arg === 'object' && arg instanceof Array)) {\n      throw MinimongoError('Modifier $pushAll/pullAll allowed for arrays only');\n    }\n\n    assertHasValidFieldNames(arg);\n\n    const toPush = target[field];\n\n    if (toPush === undefined) {\n      target[field] = arg;\n    } else if (!(toPush instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pushAll modifier to non-array',\n        {field}\n      );\n    } else {\n      toPush.push(...arg);\n    }\n  },\n  $addToSet(target, field, arg) {\n    let isEach = false;\n\n    if (typeof arg === 'object') {\n      // check if first key is '$each'\n      const keys = Object.keys(arg);\n      if (keys[0] === '$each') {\n        isEach = true;\n      }\n    }\n\n    const values = isEach ? arg.$each : [arg];\n\n    assertHasValidFieldNames(values);\n\n    const toAdd = target[field];\n    if (toAdd === undefined) {\n      target[field] = values;\n    } else if (!(toAdd instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $addToSet modifier to non-array',\n        {field}\n      );\n    } else {\n      values.forEach(value => {\n        if (toAdd.some(element => LocalCollection._f._equal(value, element))) {\n          return;\n        }\n\n        toAdd.push(value);\n      });\n    }\n  },\n  $pop(target, field, arg) {\n    if (target === undefined) {\n      return;\n    }\n\n    const toPop = target[field];\n\n    if (toPop === undefined) {\n      return;\n    }\n\n    if (!(toPop instanceof Array)) {\n      throw MinimongoError('Cannot apply $pop modifier to non-array', {field});\n    }\n\n    if (typeof arg === 'number' && arg < 0) {\n      toPop.splice(0, 1);\n    } else {\n      toPop.pop();\n    }\n  },\n  $pull(target, field, arg) {\n    if (target === undefined) {\n      return;\n    }\n\n    const toPull = target[field];\n    if (toPull === undefined) {\n      return;\n    }\n\n    if (!(toPull instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pull/pullAll modifier to non-array',\n        {field}\n      );\n    }\n\n    let out;\n    if (arg != null && typeof arg === 'object' && !(arg instanceof Array)) {\n      // XXX would be much nicer to compile this once, rather than\n      // for each document we modify.. but usually we're not\n      // modifying that many documents, so we'll let it slide for\n      // now\n\n      // XXX Minimongo.Matcher isn't up for the job, because we need\n      // to permit stuff like {$pull: {a: {$gt: 4}}}.. something\n      // like {$gt: 4} is not normally a complete selector.\n      // same issue as $elemMatch possibly?\n      const matcher = new Minimongo.Matcher(arg);\n\n      out = toPull.filter(element => !matcher.documentMatches(element).result);\n    } else {\n      out = toPull.filter(element => !LocalCollection._f._equal(element, arg));\n    }\n\n    target[field] = out;\n  },\n  $pullAll(target, field, arg) {\n    if (!(typeof arg === 'object' && arg instanceof Array)) {\n      throw MinimongoError(\n        'Modifier $pushAll/pullAll allowed for arrays only',\n        {field}\n      );\n    }\n\n    if (target === undefined) {\n      return;\n    }\n\n    const toPull = target[field];\n\n    if (toPull === undefined) {\n      return;\n    }\n\n    if (!(toPull instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pull/pullAll modifier to non-array',\n        {field}\n      );\n    }\n\n    target[field] = toPull.filter(object =>\n      !arg.some(element => LocalCollection._f._equal(object, element))\n    );\n  },\n  $bit(target, field, arg) {\n    // XXX mongo only supports $bit on integers, and we only support\n    // native javascript numbers (doubles) so far, so we can't support $bit\n    throw MinimongoError('$bit is not supported', {field});\n  },\n  $v() {\n    // As discussed in https://github.com/meteor/meteor/issues/9623,\n    // the `$v` operator is not needed by Meteor, but problems can occur if\n    // it's not at least callable (as of Mongo >= 3.6). It's defined here as\n    // a no-op to work around these problems.\n  }\n};\n\nconst NO_CREATE_MODIFIERS = {\n  $pop: true,\n  $pull: true,\n  $pullAll: true,\n  $rename: true,\n  $unset: true\n};\n\n// Make sure field names do not contain Mongo restricted\n// characters ('.', '$', '\\0').\n// https://docs.mongodb.com/manual/reference/limits/#Restrictions-on-Field-Names\nconst invalidCharMsg = {\n  $: 'start with \\'$\\'',\n  '.': 'contain \\'.\\'',\n  '\\0': 'contain null bytes'\n};\n\n// checks if all field names in an object are valid\nfunction assertHasValidFieldNames(doc) {\n  if (doc && typeof doc === 'object') {\n    JSON.stringify(doc, (key, value) => {\n      assertIsValidFieldName(key);\n      return value;\n    });\n  }\n}\n\nfunction assertIsValidFieldName(key) {\n  let match;\n  if (typeof key === 'string' && (match = key.match(/^\\$|\\.|\\0/))) {\n    throw MinimongoError(`Key ${key} must not ${invalidCharMsg[match[0]]}`);\n  }\n}\n\n// for a.b.c.2.d.e, keyparts should be ['a', 'b', 'c', '2', 'd', 'e'],\n// and then you would operate on the 'e' property of the returned\n// object.\n//\n// if options.noCreate is falsey, creates intermediate levels of\n// structure as necessary, like mkdir -p (and raises an exception if\n// that would mean giving a non-numeric property to an array.) if\n// options.noCreate is true, return undefined instead.\n//\n// may modify the last element of keyparts to signal to the caller that it needs\n// to use a different value to index into the returned object (for example,\n// ['a', '01'] -> ['a', 1]).\n//\n// if forbidArray is true, return null if the keypath goes through an array.\n//\n// if options.arrayIndices is set, use its first element for the (first) '$' in\n// the path.\nfunction findModTarget(doc, keyparts, options = {}) {\n  let usedArrayIndex = false;\n\n  for (let i = 0; i < keyparts.length; i++) {\n    const last = i === keyparts.length - 1;\n    let keypart = keyparts[i];\n\n    if (!isIndexable(doc)) {\n      if (options.noCreate) {\n        return undefined;\n      }\n\n      const error = MinimongoError(\n        `cannot use the part '${keypart}' to traverse ${doc}`\n      );\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    if (doc instanceof Array) {\n      if (options.forbidArray) {\n        return null;\n      }\n\n      if (keypart === '$') {\n        if (usedArrayIndex) {\n          throw MinimongoError('Too many positional (i.e. \\'$\\') elements');\n        }\n\n        if (!options.arrayIndices || !options.arrayIndices.length) {\n          throw MinimongoError(\n            'The positional operator did not find the match needed from the ' +\n            'query'\n          );\n        }\n\n        keypart = options.arrayIndices[0];\n        usedArrayIndex = true;\n      } else if (isNumericKey(keypart)) {\n        keypart = parseInt(keypart);\n      } else {\n        if (options.noCreate) {\n          return undefined;\n        }\n\n        throw MinimongoError(\n          `can't append to array using string field name [${keypart}]`\n        );\n      }\n\n      if (last) {\n        keyparts[i] = keypart; // handle 'a.01'\n      }\n\n      if (options.noCreate && keypart >= doc.length) {\n        return undefined;\n      }\n\n      while (doc.length < keypart) {\n        doc.push(null);\n      }\n\n      if (!last) {\n        if (doc.length === keypart) {\n          doc.push({});\n        } else if (typeof doc[keypart] !== 'object') {\n          throw MinimongoError(\n            `can't modify field '${keyparts[i + 1]}' of list value ` +\n            JSON.stringify(doc[keypart])\n          );\n        }\n      }\n    } else {\n      assertIsValidFieldName(keypart);\n\n      if (!(keypart in doc)) {\n        if (options.noCreate) {\n          return undefined;\n        }\n\n        if (!last) {\n          doc[keypart] = {};\n        }\n      }\n    }\n\n    if (last) {\n      return doc;\n    }\n\n    doc = doc[keypart];\n  }\n\n  // notreached\n}\n", "import LocalCollection from './local_collection.js';\nimport {\n  compileDocumentSelector,\n  hasOwn,\n  nothingMatcher,\n} from './common.js';\n\nconst Decimal = Package['mongo-decimal']?.Decimal || class DecimalStub {}\n\n// The minimongo selector compiler!\n\n// Terminology:\n//  - a 'selector' is the EJSON object representing a selector\n//  - a 'matcher' is its compiled form (whether a full Minimongo.Matcher\n//    object or one of the component lambdas that matches parts of it)\n//  - a 'result object' is an object with a 'result' field and maybe\n//    distance and arrayIndices.\n//  - a 'branched value' is an object with a 'value' field and maybe\n//    'dontIterate' and 'arrayIndices'.\n//  - a 'document' is a top-level object that can be stored in a collection.\n//  - a 'lookup function' is a function that takes in a document and returns\n//    an array of 'branched values'.\n//  - a 'branched matcher' maps from an array of branched values to a result\n//    object.\n//  - an 'element matcher' maps from a single value to a bool.\n\n// Main entry point.\n//   var matcher = new Minimongo.Matcher({a: {$gt: 5}});\n//   if (matcher.documentMatches({a: 7})) ...\nexport default class Matcher {\n  constructor(selector, isUpdate) {\n    // A set (object mapping string -> *) of all of the document paths looked\n    // at by the selector. Also includes the empty string if it may look at any\n    // path (eg, $where).\n    this._paths = {};\n    // Set to true if compilation finds a $near.\n    this._hasGeoQuery = false;\n    // Set to true if compilation finds a $where.\n    this._hasWhere = false;\n    // Set to false if compilation finds anything other than a simple equality\n    // or one or more of '$gt', '$gte', '$lt', '$lte', '$ne', '$in', '$nin' used\n    // with scalars as operands.\n    this._isSimple = true;\n    // Set to a dummy document which always matches this Matcher. Or set to null\n    // if such document is too hard to find.\n    this._matchingDocument = undefined;\n    // A clone of the original selector. It may just be a function if the user\n    // passed in a function; otherwise is definitely an object (eg, IDs are\n    // translated into {_id: ID} first. Used by canBecomeTrueByModifier and\n    // Sorter._useWithMatcher.\n    this._selector = null;\n    this._docMatcher = this._compileSelector(selector);\n    // Set to true if selection is done for an update operation\n    // Default is false\n    // Used for $near array update (issue #3599)\n    this._isUpdate = isUpdate;\n  }\n\n  documentMatches(doc) {\n    if (doc !== Object(doc)) {\n      throw Error('documentMatches needs a document');\n    }\n\n    return this._docMatcher(doc);\n  }\n\n  hasGeoQuery() {\n    return this._hasGeoQuery;\n  }\n\n  hasWhere() {\n    return this._hasWhere;\n  }\n\n  isSimple() {\n    return this._isSimple;\n  }\n\n  // Given a selector, return a function that takes one argument, a\n  // document. It returns a result object.\n  _compileSelector(selector) {\n    // you can pass a literal function instead of a selector\n    if (selector instanceof Function) {\n      this._isSimple = false;\n      this._selector = selector;\n      this._recordPathUsed('');\n\n      return doc => ({result: !!selector.call(doc)});\n    }\n\n    // shorthand -- scalar _id\n    if (LocalCollection._selectorIsId(selector)) {\n      this._selector = {_id: selector};\n      this._recordPathUsed('_id');\n\n      return doc => ({result: EJSON.equals(doc._id, selector)});\n    }\n\n    // protect against dangerous selectors.  falsey and {_id: falsey} are both\n    // likely programmer error, and not what you want, particularly for\n    // destructive operations.\n    if (!selector || hasOwn.call(selector, '_id') && !selector._id) {\n      this._isSimple = false;\n      return nothingMatcher;\n    }\n\n    // Top level can't be an array or true or binary.\n    if (Array.isArray(selector) ||\n        EJSON.isBinary(selector) ||\n        typeof selector === 'boolean') {\n      throw new Error(`Invalid selector: ${selector}`);\n    }\n\n    this._selector = EJSON.clone(selector);\n\n    return compileDocumentSelector(selector, this, {isRoot: true});\n  }\n\n  // Returns a list of key paths the given selector is looking for. It includes\n  // the empty string if there is a $where.\n  _getPaths() {\n    return Object.keys(this._paths);\n  }\n\n  _recordPathUsed(path) {\n    this._paths[path] = true;\n  }\n}\n\n// helpers used by compiled selector code\nLocalCollection._f = {\n  // XXX for _all and _in, consider building 'inquery' at compile time..\n  _type(v) {\n    if (typeof v === 'number') {\n      return 1;\n    }\n\n    if (typeof v === 'string') {\n      return 2;\n    }\n\n    if (typeof v === 'boolean') {\n      return 8;\n    }\n\n    if (Array.isArray(v)) {\n      return 4;\n    }\n\n    if (v === null) {\n      return 10;\n    }\n\n    // note that typeof(/x/) === \"object\"\n    if (v instanceof RegExp) {\n      return 11;\n    }\n\n    if (typeof v === 'function') {\n      return 13;\n    }\n\n    if (v instanceof Date) {\n      return 9;\n    }\n\n    if (EJSON.isBinary(v)) {\n      return 5;\n    }\n\n    if (v instanceof MongoID.ObjectID) {\n      return 7;\n    }\n\n    if (v instanceof Decimal) {\n      return 1;\n    }\n\n    // object\n    return 3;\n\n    // XXX support some/all of these:\n    // 14, symbol\n    // 15, javascript code with scope\n    // 16, 18: 32-bit/64-bit integer\n    // 17, timestamp\n    // 255, minkey\n    // 127, maxkey\n  },\n\n  // deep equality test: use for literal document and array matches\n  _equal(a, b) {\n    return EJSON.equals(a, b, {keyOrderSensitive: true});\n  },\n\n  // maps a type code to a value that can be used to sort values of different\n  // types\n  _typeorder(t) {\n    // http://www.mongodb.org/display/DOCS/What+is+the+Compare+Order+for+BSON+Types\n    // XXX what is the correct sort position for Javascript code?\n    // ('100' in the matrix below)\n    // XXX minkey/maxkey\n    return [\n      -1,  // (not a type)\n      1,   // number\n      2,   // string\n      3,   // object\n      4,   // array\n      5,   // binary\n      -1,  // deprecated\n      6,   // ObjectID\n      7,   // bool\n      8,   // Date\n      0,   // null\n      9,   // RegExp\n      -1,  // deprecated\n      100, // JS code\n      2,   // deprecated (symbol)\n      100, // JS code\n      1,   // 32-bit int\n      8,   // Mongo timestamp\n      1    // 64-bit int\n    ][t];\n  },\n\n  // compare two values of unknown type according to BSON ordering\n  // semantics. (as an extension, consider 'undefined' to be less than\n  // any other value.) return negative if a is less, positive if b is\n  // less, or 0 if equal\n  _cmp(a, b) {\n    if (a === undefined) {\n      return b === undefined ? 0 : -1;\n    }\n\n    if (b === undefined) {\n      return 1;\n    }\n\n    let ta = LocalCollection._f._type(a);\n    let tb = LocalCollection._f._type(b);\n\n    const oa = LocalCollection._f._typeorder(ta);\n    const ob = LocalCollection._f._typeorder(tb);\n\n    if (oa !== ob) {\n      return oa < ob ? -1 : 1;\n    }\n\n    // XXX need to implement this if we implement Symbol or integers, or\n    // Timestamp\n    if (ta !== tb) {\n      throw Error('Missing type coercion logic in _cmp');\n    }\n\n    if (ta === 7) { // ObjectID\n      // Convert to string.\n      ta = tb = 2;\n      a = a.toHexString();\n      b = b.toHexString();\n    }\n\n    if (ta === 9) { // Date\n      // Convert to millis.\n      ta = tb = 1;\n      a = isNaN(a) ? 0 : a.getTime();\n      b = isNaN(b) ? 0 : b.getTime();\n    }\n\n    if (ta === 1) { // double\n      if (a instanceof Decimal) {\n        return a.minus(b).toNumber();\n      } else {\n        return a - b;\n      }\n    }\n\n    if (tb === 2) // string\n      return a < b ? -1 : a === b ? 0 : 1;\n\n    if (ta === 3) { // Object\n      // this could be much more efficient in the expected case ...\n      const toArray = object => {\n        const result = [];\n\n        Object.keys(object).forEach(key => {\n          result.push(key, object[key]);\n        });\n\n        return result;\n      };\n\n      return LocalCollection._f._cmp(toArray(a), toArray(b));\n    }\n\n    if (ta === 4) { // Array\n      for (let i = 0; ; i++) {\n        if (i === a.length) {\n          return i === b.length ? 0 : -1;\n        }\n\n        if (i === b.length) {\n          return 1;\n        }\n\n        const s = LocalCollection._f._cmp(a[i], b[i]);\n        if (s !== 0) {\n          return s;\n        }\n      }\n    }\n\n    if (ta === 5) { // binary\n      // Surprisingly, a small binary blob is always less than a large one in\n      // Mongo.\n      if (a.length !== b.length) {\n        return a.length - b.length;\n      }\n\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] < b[i]) {\n          return -1;\n        }\n\n        if (a[i] > b[i]) {\n          return 1;\n        }\n      }\n\n      return 0;\n    }\n\n    if (ta === 8) { // boolean\n      if (a) {\n        return b ? 0 : 1;\n      }\n\n      return b ? -1 : 0;\n    }\n\n    if (ta === 10) // null\n      return 0;\n\n    if (ta === 11) // regexp\n      throw Error('Sorting not supported on regular expression'); // XXX\n\n    // 13: javascript code\n    // 14: symbol\n    // 15: javascript code with scope\n    // 16: 32-bit integer\n    // 17: timestamp\n    // 18: 64-bit integer\n    // 255: minkey\n    // 127: maxkey\n    if (ta === 13) // javascript code\n      throw Error('Sorting not supported on Javascript code'); // XXX\n\n    throw Error('Unknown type to sort');\n  },\n};\n", "import LocalCollection_ from './local_collection.js';\nimport Matcher from './matcher.js';\nimport Sorter from './sorter.js';\n\nLocalCollection = LocalCollection_;\nMinimongo = {\n    LocalCollection: LocalCollection_,\n    Matcher,\n    Sorter\n};\n", "// ObserveHandle: the return value of a live query.\nexport default class ObserveHandle {}\n", "import {\n  ELEMENT_OPERATORS,\n  equalityElementMatcher,\n  expandArraysInBranches,\n  hasOwn,\n  isOperatorObject,\n  makeLookupFunction,\n  regexpElementMatcher,\n} from './common.js';\n\n// Give a sort spec, which can be in any of these forms:\n//   {\"key1\": 1, \"key2\": -1}\n//   [[\"key1\", \"asc\"], [\"key2\", \"desc\"]]\n//   [\"key1\", [\"key2\", \"desc\"]]\n//\n// (.. with the first form being dependent on the key enumeration\n// behavior of your javascript VM, which usually does what you mean in\n// this case if the key names don't look like integers ..)\n//\n// return a function that takes two objects, and returns -1 if the\n// first object comes first in order, 1 if the second object comes\n// first, or 0 if neither object comes before the other.\n\nexport default class Sorter {\n  constructor(spec) {\n    this._sortSpecParts = [];\n    this._sortFunction = null;\n\n    const addSpecPart = (path, ascending) => {\n      if (!path) {\n        throw Error('sort keys must be non-empty');\n      }\n\n      if (path.charAt(0) === '$') {\n        throw Error(`unsupported sort key: ${path}`);\n      }\n\n      this._sortSpecParts.push({\n        ascending,\n        lookup: makeLookupFunction(path, {forSort: true}),\n        path\n      });\n    };\n\n    if (spec instanceof Array) {\n      spec.forEach(element => {\n        if (typeof element === 'string') {\n          addSpecPart(element, true);\n        } else {\n          addSpecPart(element[0], element[1] !== 'desc');\n        }\n      });\n    } else if (typeof spec === 'object') {\n      Object.keys(spec).forEach(key => {\n        addSpecPart(key, spec[key] >= 0);\n      });\n    } else if (typeof spec === 'function') {\n      this._sortFunction = spec;\n    } else {\n      throw Error(`Bad sort specification: ${JSON.stringify(spec)}`);\n    }\n\n    // If a function is specified for sorting, we skip the rest.\n    if (this._sortFunction) {\n      return;\n    }\n\n    // To implement affectedByModifier, we piggy-back on top of Matcher's\n    // affectedByModifier code; we create a selector that is affected by the\n    // same modifiers as this sort order. This is only implemented on the\n    // server.\n    if (this.affectedByModifier) {\n      const selector = {};\n\n      this._sortSpecParts.forEach(spec => {\n        selector[spec.path] = 1;\n      });\n\n      this._selectorForAffectedByModifier = new Minimongo.Matcher(selector);\n    }\n\n    this._keyComparator = composeComparators(\n      this._sortSpecParts.map((spec, i) => this._keyFieldComparator(i))\n    );\n  }\n\n  getComparator(options) {\n    // If sort is specified or have no distances, just use the comparator from\n    // the source specification (which defaults to \"everything is equal\".\n    // issue #3599\n    // https://docs.mongodb.com/manual/reference/operator/query/near/#sort-operation\n    // sort effectively overrides $near\n    if (this._sortSpecParts.length || !options || !options.distances) {\n      return this._getBaseComparator();\n    }\n\n    const distances = options.distances;\n\n    // Return a comparator which compares using $near distances.\n    return (a, b) => {\n      if (!distances.has(a._id)) {\n        throw Error(`Missing distance for ${a._id}`);\n      }\n\n      if (!distances.has(b._id)) {\n        throw Error(`Missing distance for ${b._id}`);\n      }\n\n      return distances.get(a._id) - distances.get(b._id);\n    };\n  }\n\n  // Takes in two keys: arrays whose lengths match the number of spec\n  // parts. Returns negative, 0, or positive based on using the sort spec to\n  // compare fields.\n  _compareKeys(key1, key2) {\n    if (key1.length !== this._sortSpecParts.length ||\n        key2.length !== this._sortSpecParts.length) {\n      throw Error('Key has wrong length');\n    }\n\n    return this._keyComparator(key1, key2);\n  }\n\n  // Iterates over each possible \"key\" from doc (ie, over each branch), calling\n  // 'cb' with the key.\n  _generateKeysFromDoc(doc, cb) {\n    if (this._sortSpecParts.length === 0) {\n      throw new Error('can\\'t generate keys without a spec');\n    }\n\n    const pathFromIndices = indices => `${indices.join(',')},`;\n\n    let knownPaths = null;\n\n    // maps index -> ({'' -> value} or {path -> value})\n    const valuesByIndexAndPath = this._sortSpecParts.map(spec => {\n      // Expand any leaf arrays that we find, and ignore those arrays\n      // themselves.  (We never sort based on an array itself.)\n      let branches = expandArraysInBranches(spec.lookup(doc), true);\n\n      // If there are no values for a key (eg, key goes to an empty array),\n      // pretend we found one undefined value.\n      if (!branches.length) {\n        branches = [{ value: void 0 }];\n      }\n\n      const element = Object.create(null);\n      let usedPaths = false;\n\n      branches.forEach(branch => {\n        if (!branch.arrayIndices) {\n          // If there are no array indices for a branch, then it must be the\n          // only branch, because the only thing that produces multiple branches\n          // is the use of arrays.\n          if (branches.length > 1) {\n            throw Error('multiple branches but no array used?');\n          }\n\n          element[''] = branch.value;\n          return;\n        }\n\n        usedPaths = true;\n\n        const path = pathFromIndices(branch.arrayIndices);\n\n        if (hasOwn.call(element, path)) {\n          throw Error(`duplicate path: ${path}`);\n        }\n\n        element[path] = branch.value;\n\n        // If two sort fields both go into arrays, they have to go into the\n        // exact same arrays and we have to find the same paths.  This is\n        // roughly the same condition that makes MongoDB throw this strange\n        // error message.  eg, the main thing is that if sort spec is {a: 1,\n        // b:1} then a and b cannot both be arrays.\n        //\n        // (In MongoDB it seems to be OK to have {a: 1, 'a.x.y': 1} where 'a'\n        // and 'a.x.y' are both arrays, but we don't allow this for now.\n        // #NestedArraySort\n        // XXX achieve full compatibility here\n        if (knownPaths && !hasOwn.call(knownPaths, path)) {\n          throw Error('cannot index parallel arrays');\n        }\n      });\n\n      if (knownPaths) {\n        // Similarly to above, paths must match everywhere, unless this is a\n        // non-array field.\n        if (!hasOwn.call(element, '') &&\n            Object.keys(knownPaths).length !== Object.keys(element).length) {\n          throw Error('cannot index parallel arrays!');\n        }\n      } else if (usedPaths) {\n        knownPaths = {};\n\n        Object.keys(element).forEach(path => {\n          knownPaths[path] = true;\n        });\n      }\n\n      return element;\n    });\n\n    if (!knownPaths) {\n      // Easy case: no use of arrays.\n      const soleKey = valuesByIndexAndPath.map(values => {\n        if (!hasOwn.call(values, '')) {\n          throw Error('no value in sole key case?');\n        }\n\n        return values[''];\n      });\n\n      cb(soleKey);\n\n      return;\n    }\n\n    Object.keys(knownPaths).forEach(path => {\n      const key = valuesByIndexAndPath.map(values => {\n        if (hasOwn.call(values, '')) {\n          return values[''];\n        }\n\n        if (!hasOwn.call(values, path)) {\n          throw Error('missing path?');\n        }\n\n        return values[path];\n      });\n\n      cb(key);\n    });\n  }\n\n  // Returns a comparator that represents the sort specification (but not\n  // including a possible geoquery distance tie-breaker).\n  _getBaseComparator() {\n    if (this._sortFunction) {\n      return this._sortFunction;\n    }\n\n    // If we're only sorting on geoquery distance and no specs, just say\n    // everything is equal.\n    if (!this._sortSpecParts.length) {\n      return (doc1, doc2) => 0;\n    }\n\n    return (doc1, doc2) => {\n      const key1 = this._getMinKeyFromDoc(doc1);\n      const key2 = this._getMinKeyFromDoc(doc2);\n      return this._compareKeys(key1, key2);\n    };\n  }\n\n  // Finds the minimum key from the doc, according to the sort specs.  (We say\n  // \"minimum\" here but this is with respect to the sort spec, so \"descending\"\n  // sort fields mean we're finding the max for that field.)\n  //\n  // Note that this is NOT \"find the minimum value of the first field, the\n  // minimum value of the second field, etc\"... it's \"choose the\n  // lexicographically minimum value of the key vector, allowing only keys which\n  // you can find along the same paths\".  ie, for a doc {a: [{x: 0, y: 5}, {x:\n  // 1, y: 3}]} with sort spec {'a.x': 1, 'a.y': 1}, the only keys are [0,5] and\n  // [1,3], and the minimum key is [0,5]; notably, [0,3] is NOT a key.\n  _getMinKeyFromDoc(doc) {\n    let minKey = null;\n\n    this._generateKeysFromDoc(doc, key => {\n      if (minKey === null) {\n        minKey = key;\n        return;\n      }\n\n      if (this._compareKeys(key, minKey) < 0) {\n        minKey = key;\n      }\n    });\n\n    return minKey;\n  }\n\n  _getPaths() {\n    return this._sortSpecParts.map(part => part.path);\n  }\n\n  // Given an index 'i', returns a comparator that compares two key arrays based\n  // on field 'i'.\n  _keyFieldComparator(i) {\n    const invert = !this._sortSpecParts[i].ascending;\n\n    return (key1, key2) => {\n      const compare = LocalCollection._f._cmp(key1[i], key2[i]);\n      return invert ? -compare : compare;\n    };\n  }\n}\n\n// Given an array of comparators\n// (functions (a,b)->(negative or positive or zero)), returns a single\n// comparator which uses each comparator in order and returns the first\n// non-zero value.\nfunction composeComparators(comparatorArray) {\n  return (a, b) => {\n    for (let i = 0; i < comparatorArray.length; ++i) {\n      const compare = comparatorArray[i](a, b);\n      if (compare !== 0) {\n        return compare;\n      }\n    }\n\n    return 0;\n  };\n}\n"]}