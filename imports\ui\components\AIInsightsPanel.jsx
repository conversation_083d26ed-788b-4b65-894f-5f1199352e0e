import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Tooltip,
  Button,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Assignment as AssignmentIcon,
  Group as GroupIcon,
  Timeline as TimelineIcon,
  Refresh as RefreshIcon,
  Speed as SpeedIcon,
  Lightbulb as LightbulbIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { Meteor } from 'meteor/meteor';
import { OllamaService } from '../../api/ai/ollamaService';

const TabPanel = ({ children, value, index }) => (
  <div hidden={value !== index} style={{ padding: '16px' }}>
    {value === index && children}
  </div>
);

export const AIInsightsPanel = ({ taskId, task: propTask }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [insights, setInsights] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [retryCount, setRetryCount] = useState(0);

  const fetchInsights = async () => {
    setLoading(true);
    setError(null);
    try {
      let result;
      let taskObj = propTask;
      if (!taskObj || !taskObj.title) {
        setError('Task data is missing or incomplete.');
        setLoading(false);
        return;
      }
      switch (selectedTab) {
        case 0:
          result = await OllamaService.analyzeTask(taskObj);
          break;
        case 1:
          result = await OllamaService.suggestTaskOptimization(taskObj._id);
          break;
        case 2:
          result = await OllamaService.predictTaskCompletion(taskObj._id);
          break;
        case 3:
          result = await OllamaService.generateTaskSummary(taskObj._id);
          break;
        case 4:
          result = await OllamaService.recommendTaskAssignments(taskObj._id);
          break;
        default:
          result = await OllamaService.analyzeTask(taskObj);
      }
      setInsights(result);
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      if (error.error === 'connection-failed') {
        setError('Could not connect to Ollama. Please make sure Ollama is running on your machine.');
      } else {
        setError(error.message || 'Failed to fetch insights');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInsights();
  }, [taskId, selectedTab]);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handleRefresh = () => {
    setRetryCount(prev => prev + 1);
    fetchInsights();
  };

  const renderInsightContent = (content) => {
    if (!content) return null;
    
    // Split content into sections based on numbered points
    const sections = content.split(/\d\./).filter(Boolean);
    
    return (
      <List>
        {sections.map((section, index) => (
          <ListItem key={index} alignItems="flex-start">
            <ListItemText
              primary={
                <Typography variant="subtitle1" color="primary">
                  {section.split('\n')[0].trim()}
                </Typography>
              }
              secondary={
                <Typography
                  component="span"
                  variant="body2"
                  color="text.primary"
                  style={{ whiteSpace: 'pre-line' }}
                >
                  {section.split('\n').slice(1).join('\n').trim()}
                </Typography>
              }
            />
          </ListItem>
        ))}
      </List>
    );
  };

  const renderErrorContent = () => {
    if (!error) return null;

    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <ErrorIcon color="error" sx={{ fontSize: 48, mb: 2 }} />
        <Typography variant="h6" color="error" gutterBottom>
          {error}
        </Typography>
        {retryCount < 3 && (
          <Button
            variant="contained"
            color="primary"
            onClick={handleRefresh}
            startIcon={<RefreshIcon />}
            sx={{ mt: 2 }}
          >
            Try Again
          </Button>
        )}
        {retryCount >= 3 && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Multiple attempts failed. Please try again later.
          </Typography>
        )}
      </Box>
    );
  };

  return (
    <Card sx={{ maxWidth: 800, margin: '20px auto', p: 2, height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            AI Insights
          </Typography>
          <Tooltip title="Refresh Insights">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>

        <Tabs
          value={selectedTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ mb: 2 }}
        >
          <Tooltip title="Analysis">
            <Tab icon={<AnalyticsIcon />} aria-label="Analysis" />
          </Tooltip>
          <Tooltip title="Optimization">
            <Tab icon={<SpeedIcon />} aria-label="Optimization" />
          </Tooltip>
          <Tooltip title="Prediction">
            <Tab icon={<TimelineIcon />} aria-label="Prediction" />
          </Tooltip>
          <Tooltip title="Recommendations">
            <Tab icon={<GroupIcon />} aria-label="Recommendations" />
          </Tooltip>
        </Tabs>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        )}

        {error && renderErrorContent()}

        {!loading && !error && insights && (
          <Box sx={{ p: 1 }}>
            {renderInsightContent(insights)}
          </Box>
        )}

        {!loading && !error && !insights && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
            <LightbulbIcon sx={{ mr: 1 }} />
            <Typography>No insights available</Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}; 
