{"version": 3, "sources": ["meteor://💻app/packages/minimongo/minimongo_client.js", "meteor://💻app/packages/minimongo/common.js", "meteor://💻app/packages/minimongo/constants.js", "meteor://💻app/packages/minimongo/cursor.js", "meteor://💻app/packages/minimongo/local_collection.js", "meteor://💻app/packages/minimongo/matcher.js", "meteor://💻app/packages/minimongo/minimongo_common.js", "meteor://💻app/packages/minimongo/observe_handle.js", "meteor://💻app/packages/minimongo/sorter.js"], "names": ["module", "link", "_toConsumableArray", "default", "v", "_typeof", "export", "hasOwn", "ELEMENT_OPERATORS", "compileDocumentSelector", "equalityElementM<PERSON>er", "expandArraysInBranches", "isIndexable", "isNumericKey", "isOperatorObject", "makeLookupFunction", "nothingMatcher", "pathsToTree", "populateDocumentWithQueryFields", "projectionDetails", "regexpElementMatcher", "LocalCollection", "Object", "prototype", "hasOwnProperty", "$lt", "makeInequality", "cmpValue", "$gt", "$lte", "$gte", "$mod", "compileElementSelector", "operand", "Array", "isArray", "length", "Error", "divisor", "remainder", "value", "$in", "elementMatchers", "map", "option", "RegExp", "undefined", "some", "matcher", "$size", "dontExpandLeafArrays", "$type", "dontIncludeLeafArrays", "operandAliasMap", "call", "_f", "_type", "$bitsAllSet", "mask", "getOperandBitmask", "bitmask", "getValueBitmask", "every", "byte", "i", "$bitsAnySet", "$bitsAllClear", "$bitsAnyClear", "$regex", "valueSelector", "regexp", "$options", "test", "source", "$elemMatch", "_isPlainObject", "isDocMatcher", "keys", "filter", "key", "LOGICAL_OPERATORS", "reduce", "a", "b", "_Object$assign", "assign", "subMatcher", "inElemMatch", "compileValueSelector", "arrayElement", "arg", "dontIterate", "result", "$and", "subSelector", "andDocumentMatchers", "compileArrayOfDocumentSelectors", "$or", "matchers", "doc", "fn", "$nor", "$where", "selector<PERSON><PERSON><PERSON>", "_recordPathUsed", "_hasWhere", "Function", "$comment", "VALUE_OPERATORS", "$eq", "convertElementMatcherToBranchedMatcher", "$not", "invertBranchedMatcher", "$ne", "$nin", "$exists", "exists", "everythingMatcher", "$maxDistance", "$near", "$all", "branchedMatchers", "criterion", "andBranchedMatchers", "isRoot", "_hasGeoQuery", "maxDistance", "point", "distance", "$geometry", "type", "GeoJSON", "pointDistance", "coordinates", "pointToArray", "geometryWithinRadius", "distanceCoordinatePairs", "branchedValues", "branch", "curDistance", "_isUpdate", "arrayIndices", "andSomeMatchers", "subMatchers", "docOrBranches", "match", "subResult", "selectors", "docSelector", "options", "arguments", "docMatchers", "substr", "_isSimple", "lookUpByIndex", "valueMatcher", "Boolean", "operatorBranchedMatcher", "elementMatcher", "branches", "expanded", "element", "matched", "pointA", "pointB", "Math", "hypot", "elementSelector", "_equal", "docOrBranchedValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "branchesOut", "for<PERSON>ach", "thisIsArray", "push", "concat", "selector", "Number", "isInteger", "Uint8Array", "Int32Array", "buffer", "EJSON", "isBinary", "x", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max", "apply", "view", "isSafeInteger", "Uint32Array", "BYTES_PER_ELEMENT", "insertIntoDocument", "document", "existingKey", "indexOf", "branchedMatcher", "branchV<PERSON>ues", "obj", "s", "inconsistentOK", "theseAreOperators", "<PERSON><PERSON><PERSON><PERSON>", "thisIsOperator", "JSON", "stringify", "cmpValueComparator", "operandType", "_cmp", "parts", "split", "firstPart", "lookupRest", "slice", "join", "buildResult", "firstLevel", "appendToResult", "more", "forSort", "arrayIndex", "MinimongoTest", "MinimongoError", "message", "field", "error", "name", "operatorMatchers", "operator", "simpleRange", "includes", "simpleEquality", "simpleInclusion", "paths", "newLeafFn", "conflictFn", "root", "path", "pathArray", "tree", "success", "last<PERSON>ey", "y", "populateDocumentWithKeyValue", "getPrototypeOf", "populateDocumentWithObject", "unprefixedKeys", "op", "validateObject", "object", "query", "_selectorIsId", "fields", "fieldsKeys", "sort", "_id", "including", "keyP<PERSON>", "rule", "projectionRulesTree", "node", "fullPath", "currentPath", "another<PERSON><PERSON>", "toString", "lastIndex", "validateKeyInPath", "getAsyncMethodName", "ASYNC_COLLECTION_METHODS", "ASYNC_CURSOR_METHODS", "CLIENT_ONLY_METHODS", "method", "replace", "_createForOfIteratorHelperLoose", "_regeneratorRuntime", "<PERSON><PERSON><PERSON>", "collection", "sorter", "Minimongo", "Matcher", "_selectorIsIdPerhapsAsObject", "_selectorId", "has<PERSON>eo<PERSON><PERSON><PERSON>", "Sorter", "skip", "limit", "projection", "_projectionFn", "_compileProjection", "_transform", "wrapTransform", "transform", "Tracker", "reactive", "_proto", "count", "_depend", "added", "removed", "_getRawObjects", "ordered", "fetch", "Symbol", "iterator", "_this", "addedBefore", "changed", "movedBefore", "index", "objects", "next", "done", "asyncIterator", "syncResult", "_callee", "async", "_callee$", "_context", "prev", "abrupt", "Promise", "resolve", "stop", "callback", "thisArg", "_this2", "getTransform", "_this3", "observe", "_observeFromObserveChanges", "observeAsync", "_this4", "observe<PERSON>hanges", "_this5", "_observeChangesCallbacksAreOrdered", "_allow_unordered", "distances", "_IdMap", "cursor", "dirty", "projectionFn", "resultsSnapshot", "qid", "next_qid", "queries", "results", "paused", "wrapCallback", "self", "_this6", "args", "_observeQueue", "queueTask", "_suppress_initial", "_query$results", "_query$results$size", "handler", "clone", "_iterator", "_step", "size", "handle", "ObserveHandle", "isReady", "isReadyPromise", "active", "onInvalidate", "drainResult", "drain", "then", "observeChangesAsync", "_this7", "changers", "dependency", "Dependency", "notify", "bind", "depend", "_getCollectionName", "_this8", "applySkipLimit", "selectedDoc", "_docs", "get", "set", "clear", "Meteor", "_runFresh", "id", "matchResult", "documentMatches", "getComparator", "_publishCursor", "subscription", "Package", "mongo", "Mongo", "Collection", "asyncName", "_len", "_key", "reject", "_inherits<PERSON><PERSON>e", "_objectSpread", "_readOnly<PERSON><PERSON>r", "isClient", "_SynchronousQueue", "_AsynchronousQueue", "create", "_savedOriginals", "countDocuments", "find", "countAsync", "estimatedDocumentCount", "findOne", "findOneAsync", "_args", "findOneAsync$", "awrap", "fetchAsync", "sent", "prepareInsert", "assertHasValidFieldNames", "_useOID", "MongoID", "ObjectID", "Random", "has", "_saveOriginal", "insert", "queriesToRecompute", "_i", "_Object$keys", "_insertInResultsSync", "_recomputeResults", "defer", "insertAsync", "_i2", "_Object$keys2", "insertAsync$", "_context2", "_insertInResultsAsync", "pauseObservers", "clearResultQueries", "prepareRemove", "remove", "_eachPossiblyMatchingDocSync", "query<PERSON><PERSON>ove", "_loop", "removeId", "removeDoc", "equals", "_this$prepareRemove", "_removeFromResultsSync", "removeAsync", "_this$prepareRemove2", "_remove", "removeAsync$", "_context3", "_removeFromResultsAsync", "_resumeObservers", "_diffQuery<PERSON><PERSON>es", "resumeObserversServer", "resumeObserversServer$", "_context4", "resumeObserversClient", "retrieveOriginals", "originals", "saveOriginals", "prepareUpdate", "_this9", "qidToOriginalResults", "docMap", "idsMatched", "_idsMatchedBySelector", "memoizedCloneIfNeeded", "docToMemoize", "finishUpdate", "_ref", "updateCount", "insertedId", "_returnObject", "numberAffected", "updateAsync", "mod", "_this10", "recomputeQids", "updateAsync$", "_context6", "_eachPossiblyMatchingDocAsync", "query<PERSON><PERSON>ult", "_context5", "_modifyAndNotifyAsync", "multi", "upsert", "_createUpsertDocument", "update", "_this11", "_modifyAndNotifySync", "upsertAsync", "specificIds", "_iterator2", "_step2", "_eachPossiblyMatchingDocAsync$", "_context7", "t0", "forEachAsync", "_iterator3", "_step3", "_getMatchedDocAndModify", "_this12", "matched_before", "old_doc", "_modify", "_i3", "_Object$keys3", "afterMatch", "after", "before", "_updateInResultsSync", "_i4", "_Object$keys4", "_modifyAndNotifyAsync$", "_context8", "_updateInResultsAsync", "oldResults", "_CachingChangeObserver", "_this13", "orderedFromCallbacks", "callbacks", "docs", "OrderedDict", "idStringify", "applyChange", "putBefore", "moveBefore", "DiffSequence", "applyChanges", "_IdMap2", "idParse", "IdMap", "__wrappedTransform__", "wrapped", "transformed", "nonreactive", "_binarySearch", "cmp", "array", "first", "range", "<PERSON><PERSON><PERSON><PERSON>", "floor", "_checkSupportedProjection", "_idProjection", "details", "ruleTree", "subdoc", "modifier", "selectorDocument", "isModify", "_isModificationMod", "newDoc", "$set", "isInsert", "replacement", "_diffObjects", "left", "right", "diffObjects", "newResults", "observer", "diff<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_diffQuery<PERSON>rderedChanges", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_diffQueryUnorderedChanges", "diffQuery<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_findInOrderedResults", "subIds", "_insertInSortedList", "_callee2", "_callee2$", "_context9", "splice", "isReplace", "isModifier", "setOnInsert", "modFunc", "MODIFIERS", "keypath", "keyparts", "target", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "forbidArray", "noCreate", "NO_CREATE_MODIFIERS", "pop", "observeCallbacks", "suppressed", "observeChangesCallbacks", "_observeCallbacksAreOrdered", "indices", "_no_indices", "check", "addedAt", "changedAt", "oldDoc", "movedTo", "from", "to", "removedAt", "changeObserver", "_fromObserve", "nonMutatingCallbacks", "setSuppressed", "h", "_h$isReadyPromise", "_isPromise", "_callee3", "_callee3$", "_context10", "changed<PERSON>ields", "make<PERSON><PERSON>edFields", "old_idx", "new_idx", "_callee4", "_callee4$", "_context11", "$currentDate", "Date", "$inc", "$min", "$max", "$mul", "$rename", "target2", "setPropertyError", "$setOnInsert", "$unset", "$push", "$each", "to<PERSON>ush", "position", "$position", "$slice", "sortFunction", "$sort", "_target$field", "spliceArguments", "$pushAll", "$addToSet", "isEach", "values", "toAdd", "$pop", "toPop", "$pull", "to<PERSON><PERSON>", "out", "$pullAll", "$bit", "$v", "invalidCharMsg", "$", "assertIsValidFieldName", "usedArrayIndex", "last", "keypart", "parseInt", "Decimal", "_Package$mongoDecima", "DecimalStub", "isUpdate", "_paths", "_matchingDocument", "_selector", "_doc<PERSON><PERSON>er", "_compileSelector", "hasWhere", "isSimple", "_getPaths", "keyOrderSensitive", "_typeorder", "t", "ta", "tb", "oa", "ob", "toHexString", "isNaN", "getTime", "minus", "toNumber", "toArray", "LocalCollection_", "spec", "_sortSpecParts", "_sortFunction", "addSpecPart", "ascending", "char<PERSON>t", "lookup", "affectedByModifier", "_selectorForAffectedByModifier", "_keyComparator", "composeComparators", "_keyFieldComparator", "_getBaseComparator", "_compareKeys", "key1", "key2", "_generateKeysFromDoc", "cb", "pathFromIndices", "knownPaths", "valuesByIndexAndPath", "usedPaths", "<PERSON><PERSON><PERSON>", "doc1", "doc2", "_getMinKeyFromDoc", "<PERSON><PERSON><PERSON>", "part", "invert", "compare", "comparator<PERSON><PERSON>y"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC,C;;;;;;;;;;;ACApC,IAAIC,kBAAkB;AAACF,MAAM,CAACC,IAAI,CAAC,0CAA0C,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACF,kBAAkB,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,OAAO;AAACL,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,OAAO,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAxNJ,MAAM,CAACM,MAAM,CAAC;EAACC,MAAM,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,MAAM;EAAA,CAAC;EAACC,iBAAiB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,iBAAiB;EAAA,CAAC;EAACC,uBAAuB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,uBAAuB;EAAA,CAAC;EAACC,sBAAsB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,sBAAsB;EAAA,CAAC;EAACC,sBAAsB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,sBAAsB;EAAA,CAAC;EAACC,WAAW,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,WAAW;EAAA,CAAC;EAACC,YAAY,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,YAAY;EAAA,CAAC;EAACC,gBAAgB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,gBAAgB;EAAA,CAAC;EAACC,kBAAkB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,kBAAkB;EAAA,CAAC;EAACC,cAAc,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,cAAc;EAAA,CAAC;EAACC,WAAW,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,WAAW;EAAA,CAAC;EAACC,+BAA+B,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,+BAA+B;EAAA,CAAC;EAACC,iBAAiB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,iBAAiB;EAAA,CAAC;EAACC,oBAAoB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,oBAAoB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,eAAe;AAACrB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAACiB,eAAe,GAACjB,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAEn3B,IAAMG,MAAM,GAAGe,MAAM,CAACC,SAAS,CAACC,cAAc;AAc9C,IAAMhB,iBAAiB,GAAG;EAC/BiB,GAAG,EAAEC,cAAc,CAAC,UAAAC,QAAQ;IAAA,OAAIA,QAAQ,GAAG,CAAC;EAAA,EAAC;EAC7CC,GAAG,EAAEF,cAAc,CAAC,UAAAC,QAAQ;IAAA,OAAIA,QAAQ,GAAG,CAAC;EAAA,EAAC;EAC7CE,IAAI,EAAEH,cAAc,CAAC,UAAAC,QAAQ;IAAA,OAAIA,QAAQ,IAAI,CAAC;EAAA,EAAC;EAC/CG,IAAI,EAAEJ,cAAc,CAAC,UAAAC,QAAQ;IAAA,OAAIA,QAAQ,IAAI,CAAC;EAAA,EAAC;EAC/CI,IAAI,EAAE;IACJC,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IAAIA,OAAO,CAACG,MAAM,KAAK,CAAC,IAC3C,OAAOH,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC9B,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,EAAE;QACxC,MAAMI,KAAK,CAAC,kDAAkD,CAAC;MACjE;;MAEA;MACA,IAAMC,OAAO,GAAGL,OAAO,CAAC,CAAC,CAAC;MAC1B,IAAMM,SAAS,GAAGN,OAAO,CAAC,CAAC,CAAC;MAC5B,OAAO,UAAAO,KAAK;QAAA,OACV,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAGF,OAAO,KAAKC,SAAS;MAAA,CAC3D;IACH;EACF,CAAC;EACDE,GAAG,EAAE;IACHT,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;QAC3B,MAAMI,KAAK,CAAC,oBAAoB,CAAC;MACnC;MAEA,IAAMK,eAAe,GAAGT,OAAO,CAACU,GAAG,CAAC,UAAAC,MAAM,EAAI;QAC5C,IAAIA,MAAM,YAAYC,MAAM,EAAE;UAC5B,OAAOzB,oBAAoB,CAACwB,MAAM,CAAC;QACrC;QAEA,IAAI9B,gBAAgB,CAAC8B,MAAM,CAAC,EAAE;UAC5B,MAAMP,KAAK,CAAC,yBAAyB,CAAC;QACxC;QAEA,OAAO3B,sBAAsB,CAACkC,MAAM,CAAC;MACvC,CAAC,CAAC;MAEF,OAAO,UAAAJ,KAAK,EAAI;QACd;QACA,IAAIA,KAAK,KAAKM,SAAS,EAAE;UACvBN,KAAK,GAAG,IAAI;QACd;QAEA,OAAOE,eAAe,CAACK,IAAI,CAAC,UAAAC,OAAO;UAAA,OAAIA,OAAO,CAACR,KAAK,CAAC;QAAA,EAAC;MACxD,CAAC;IACH;EACF,CAAC;EACDS,KAAK,EAAE;IACL;IACA;IACA;IACAC,oBAAoB,EAAE,IAAI;IAC1BlB,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACA;QACAA,OAAO,GAAG,CAAC;MACb,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACtC,MAAMI,KAAK,CAAC,sBAAsB,CAAC;MACrC;MAEA,OAAO,UAAAG,KAAK;QAAA,OAAIN,KAAK,CAACC,OAAO,CAACK,KAAK,CAAC,IAAIA,KAAK,CAACJ,MAAM,KAAKH,OAAO;MAAA;IAClE;EACF,CAAC;EACDkB,KAAK,EAAE;IACL;IACA;IACA;IACA;IACAC,qBAAqB,EAAE,IAAI;IAC3BpB,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B,IAAMoB,eAAe,GAAG;UACtB,QAAQ,EAAE,CAAC;UACX,QAAQ,EAAE,CAAC;UACX,QAAQ,EAAE,CAAC;UACX,OAAO,EAAE,CAAC;UACV,SAAS,EAAE,CAAC;UACZ,WAAW,EAAE,CAAC;UACd,UAAU,EAAE,CAAC;UACb,MAAM,EAAE,CAAC;UACT,MAAM,EAAE,CAAC;UACT,MAAM,EAAE,EAAE;UACV,OAAO,EAAE,EAAE;UACX,WAAW,EAAE,EAAE;UACf,YAAY,EAAE,EAAE;UAChB,QAAQ,EAAE,EAAE;UACZ,qBAAqB,EAAE,EAAE;UACzB,KAAK,EAAE,EAAE;UACT,WAAW,EAAE,EAAE;UACf,MAAM,EAAE,EAAE;UACV,SAAS,EAAE,EAAE;UACb,QAAQ,EAAE,CAAC,CAAC;UACZ,QAAQ,EAAE;QACZ,CAAC;QACD,IAAI,CAAC9C,MAAM,CAAC+C,IAAI,CAACD,eAAe,EAAEpB,OAAO,CAAC,EAAE;UAC1C,MAAMI,KAAK,sCAAoCJ,OAAS,CAAC;QAC3D;QACAA,OAAO,GAAGoB,eAAe,CAACpB,OAAO,CAAC;MACpC,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACtC,IAAIA,OAAO,KAAK,CAAC,IAAIA,OAAO,GAAG,CAAC,CAAC,IAC3BA,OAAO,GAAG,EAAE,IAAIA,OAAO,KAAK,GAAI,EAAE;UACtC,MAAMI,KAAK,oCAAkCJ,OAAS,CAAC;QACzD;MACF,CAAC,MAAM;QACL,MAAMI,KAAK,CAAC,+CAA+C,CAAC;MAC9D;MAEA,OAAO,UAAAG,KAAK;QAAA,OACVA,KAAK,KAAKM,SAAS,IAAIzB,eAAe,CAACkC,EAAE,CAACC,KAAK,CAAChB,KAAK,CAAC,KAAKP,OAAO;MAAA,CACnE;IACH;EACF,CAAC;EACDwB,WAAW,EAAE;IACXzB,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAMyB,IAAI,GAAGC,iBAAiB,CAAC1B,OAAO,EAAE,aAAa,CAAC;MACtD,OAAO,UAAAO,KAAK,EAAI;QACd,IAAMoB,OAAO,GAAGC,eAAe,CAACrB,KAAK,EAAEkB,IAAI,CAACtB,MAAM,CAAC;QACnD,OAAOwB,OAAO,IAAIF,IAAI,CAACI,KAAK,CAAC,UAACC,IAAI,EAAEC,CAAC;UAAA,OAAK,CAACJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,MAAMA,IAAI;QAAA,EAAC;MACzE,CAAC;IACH;EACF,CAAC;EACDE,WAAW,EAAE;IACXjC,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAMyB,IAAI,GAAGC,iBAAiB,CAAC1B,OAAO,EAAE,aAAa,CAAC;MACtD,OAAO,UAAAO,KAAK,EAAI;QACd,IAAMoB,OAAO,GAAGC,eAAe,CAACrB,KAAK,EAAEkB,IAAI,CAACtB,MAAM,CAAC;QACnD,OAAOwB,OAAO,IAAIF,IAAI,CAACX,IAAI,CAAC,UAACgB,IAAI,EAAEC,CAAC;UAAA,OAAK,CAAC,CAACJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,MAAMA,IAAI;QAAA,EAAC;MACzE,CAAC;IACH;EACF,CAAC;EACDG,aAAa,EAAE;IACblC,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAMyB,IAAI,GAAGC,iBAAiB,CAAC1B,OAAO,EAAE,eAAe,CAAC;MACxD,OAAO,UAAAO,KAAK,EAAI;QACd,IAAMoB,OAAO,GAAGC,eAAe,CAACrB,KAAK,EAAEkB,IAAI,CAACtB,MAAM,CAAC;QACnD,OAAOwB,OAAO,IAAIF,IAAI,CAACI,KAAK,CAAC,UAACC,IAAI,EAAEC,CAAC;UAAA,OAAK,EAAEJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,CAAC;QAAA,EAAC;MACjE,CAAC;IACH;EACF,CAAC;EACDI,aAAa,EAAE;IACbnC,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B,IAAMyB,IAAI,GAAGC,iBAAiB,CAAC1B,OAAO,EAAE,eAAe,CAAC;MACxD,OAAO,UAAAO,KAAK,EAAI;QACd,IAAMoB,OAAO,GAAGC,eAAe,CAACrB,KAAK,EAAEkB,IAAI,CAACtB,MAAM,CAAC;QACnD,OAAOwB,OAAO,IAAIF,IAAI,CAACX,IAAI,CAAC,UAACgB,IAAI,EAAEC,CAAC;UAAA,OAAK,CAACJ,OAAO,CAACI,CAAC,CAAC,GAAGD,IAAI,MAAMA,IAAI;QAAA,EAAC;MACxE,CAAC;IACH;EACF,CAAC;EACDK,MAAM,EAAE;IACNpC,sBAAsB,WAAAA,CAACC,OAAO,EAAEoC,aAAa,EAAE;MAC7C,IAAI,EAAE,OAAOpC,OAAO,KAAK,QAAQ,IAAIA,OAAO,YAAYY,MAAM,CAAC,EAAE;QAC/D,MAAMR,KAAK,CAAC,qCAAqC,CAAC;MACpD;MAEA,IAAIiC,MAAM;MACV,IAAID,aAAa,CAACE,QAAQ,KAAKzB,SAAS,EAAE;QACxC;QACA;;QAEA;QACA;QACA;QACA,IAAI,QAAQ,CAAC0B,IAAI,CAACH,aAAa,CAACE,QAAQ,CAAC,EAAE;UACzC,MAAM,IAAIlC,KAAK,CAAC,mDAAmD,CAAC;QACtE;QAEA,IAAMoC,MAAM,GAAGxC,OAAO,YAAYY,MAAM,GAAGZ,OAAO,CAACwC,MAAM,GAAGxC,OAAO;QACnEqC,MAAM,GAAG,IAAIzB,MAAM,CAAC4B,MAAM,EAAEJ,aAAa,CAACE,QAAQ,CAAC;MACrD,CAAC,MAAM,IAAItC,OAAO,YAAYY,MAAM,EAAE;QACpCyB,MAAM,GAAGrC,OAAO;MAClB,CAAC,MAAM;QACLqC,MAAM,GAAG,IAAIzB,MAAM,CAACZ,OAAO,CAAC;MAC9B;MAEA,OAAOb,oBAAoB,CAACkD,MAAM,CAAC;IACrC;EACF,CAAC;EACDI,UAAU,EAAE;IACVxB,oBAAoB,EAAE,IAAI;IAC1BlB,sBAAsB,WAAAA,CAACC,OAAO,EAAEoC,aAAa,EAAErB,OAAO,EAAE;MACtD,IAAI,CAAC3B,eAAe,CAACsD,cAAc,CAAC1C,OAAO,CAAC,EAAE;QAC5C,MAAMI,KAAK,CAAC,2BAA2B,CAAC;MAC1C;MAEA,IAAMuC,YAAY,GAAG,CAAC9D,gBAAgB,CACpCQ,MAAM,CAACuD,IAAI,CAAC5C,OAAO,CAAC,CACjB6C,MAAM,CAAC,UAAAC,GAAG;QAAA,OAAI,CAACxE,MAAM,CAAC+C,IAAI,CAAC0B,iBAAiB,EAAED,GAAG,CAAC;MAAA,EAAC,CACnDE,MAAM,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,IAAAC,cAAA;QAAA,OAAK9D,MAAM,CAAC+D,MAAM,CAACH,CAAC,GAAAE,cAAA,OAAAA,cAAA,CAAID,CAAC,IAAGlD,OAAO,CAACkD,CAAC,CAAC,EAAAC,cAAA,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,CAAC,EAC5D,IAAI,CAAC;MAEP,IAAIE,UAAU;MACd,IAAIV,YAAY,EAAE;QAChB;QACA;QACA;QACA;QACAU,UAAU,GACR7E,uBAAuB,CAACwB,OAAO,EAAEe,OAAO,EAAE;UAACuC,WAAW,EAAE;QAAI,CAAC,CAAC;MAClE,CAAC,MAAM;QACLD,UAAU,GAAGE,oBAAoB,CAACvD,OAAO,EAAEe,OAAO,CAAC;MACrD;MAEA,OAAO,UAAAR,KAAK,EAAI;QACd,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,KAAK,CAAC,EAAE;UACzB,OAAO,KAAK;QACd;QAEA,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,KAAK,CAACJ,MAAM,EAAE,EAAE4B,CAAC,EAAE;UACrC,IAAMyB,YAAY,GAAGjD,KAAK,CAACwB,CAAC,CAAC;UAC7B,IAAI0B,GAAG;UACP,IAAId,YAAY,EAAE;YAChB;YACA;YACA;YACA,IAAI,CAAChE,WAAW,CAAC6E,YAAY,CAAC,EAAE;cAC9B,OAAO,KAAK;YACd;YAEAC,GAAG,GAAGD,YAAY;UACpB,CAAC,MAAM;YACL;YACA;YACAC,GAAG,GAAG,CAAC;cAAClD,KAAK,EAAEiD,YAAY;cAAEE,WAAW,EAAE;YAAI,CAAC,CAAC;UAClD;UACA;UACA,IAAIL,UAAU,CAACI,GAAG,CAAC,CAACE,MAAM,EAAE;YAC1B,OAAO5B,CAAC,CAAC,CAAC;UACZ;QACF;QAEA,OAAO,KAAK;MACd,CAAC;IACH;EACF;AACF,CAAC;AAED;AACA,IAAMgB,iBAAiB,GAAG;EACxBa,IAAI,WAAAA,CAACC,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,EAAE;IACtC,OAAOQ,mBAAmB,CACxBC,+BAA+B,CAACF,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,CACnE,CAAC;EACH,CAAC;EAEDU,GAAG,WAAAA,CAACH,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,EAAE;IACrC,IAAMW,QAAQ,GAAGF,+BAA+B,CAC9CF,WAAW,EACX9C,OAAO,EACPuC,WACF,CAAC;;IAED;IACA;IACA,IAAIW,QAAQ,CAAC9D,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO8D,QAAQ,CAAC,CAAC,CAAC;IACpB;IAEA,OAAO,UAAAC,GAAG,EAAI;MACZ,IAAMP,MAAM,GAAGM,QAAQ,CAACnD,IAAI,CAAC,UAAAqD,EAAE;QAAA,OAAIA,EAAE,CAACD,GAAG,CAAC,CAACP,MAAM;MAAA,EAAC;MAClD;MACA;MACA,OAAO;QAACA,MAAM,EAANA;MAAM,CAAC;IACjB,CAAC;EACH,CAAC;EAEDS,IAAI,WAAAA,CAACP,WAAW,EAAE9C,OAAO,EAAEuC,WAAW,EAAE;IACtC,IAAMW,QAAQ,GAAGF,+BAA+B,CAC9CF,WAAW,EACX9C,OAAO,EACPuC,WACF,CAAC;IACD,OAAO,UAAAY,GAAG,EAAI;MACZ,IAAMP,MAAM,GAAGM,QAAQ,CAACpC,KAAK,CAAC,UAAAsC,EAAE;QAAA,OAAI,CAACA,EAAE,CAACD,GAAG,CAAC,CAACP,MAAM;MAAA,EAAC;MACpD;MACA;MACA,OAAO;QAACA,MAAM,EAANA;MAAM,CAAC;IACjB,CAAC;EACH,CAAC;EAEDU,MAAM,WAAAA,CAACC,aAAa,EAAEvD,OAAO,EAAE;IAC7B;IACAA,OAAO,CAACwD,eAAe,CAAC,EAAE,CAAC;IAC3BxD,OAAO,CAACyD,SAAS,GAAG,IAAI;IAExB,IAAI,EAAEF,aAAa,YAAYG,QAAQ,CAAC,EAAE;MACxC;MACA;MACAH,aAAa,GAAGG,QAAQ,CAAC,KAAK,cAAYH,aAAe,CAAC;IAC5D;;IAEA;IACA;IACA,OAAO,UAAAJ,GAAG;MAAA,OAAK;QAACP,MAAM,EAAEW,aAAa,CAACjD,IAAI,CAAC6C,GAAG,EAAEA,GAAG;MAAC,CAAC;IAAA,CAAC;EACxD,CAAC;EAED;EACA;EACAQ,QAAQ,WAAAA,CAAA,EAAG;IACT,OAAO;MAAA,OAAO;QAACf,MAAM,EAAE;MAAI,CAAC;IAAA,CAAC;EAC/B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAMgB,eAAe,GAAG;EACtBC,GAAG,WAAAA,CAAC5E,OAAO,EAAE;IACX,OAAO6E,sCAAsC,CAC3CpG,sBAAsB,CAACuB,OAAO,CAChC,CAAC;EACH,CAAC;EACD8E,IAAI,WAAAA,CAAC9E,OAAO,EAAEoC,aAAa,EAAErB,OAAO,EAAE;IACpC,OAAOgE,qBAAqB,CAACxB,oBAAoB,CAACvD,OAAO,EAAEe,OAAO,CAAC,CAAC;EACtE,CAAC;EACDiE,GAAG,WAAAA,CAAChF,OAAO,EAAE;IACX,OAAO+E,qBAAqB,CAC1BF,sCAAsC,CAACpG,sBAAsB,CAACuB,OAAO,CAAC,CACxE,CAAC;EACH,CAAC;EACDiF,IAAI,WAAAA,CAACjF,OAAO,EAAE;IACZ,OAAO+E,qBAAqB,CAC1BF,sCAAsC,CACpCtG,iBAAiB,CAACiC,GAAG,CAACT,sBAAsB,CAACC,OAAO,CACtD,CACF,CAAC;EACH,CAAC;EACDkF,OAAO,WAAAA,CAAClF,OAAO,EAAE;IACf,IAAMmF,MAAM,GAAGN,sCAAsC,CACnD,UAAAtE,KAAK;MAAA,OAAIA,KAAK,KAAKM,SAAS;IAAA,CAC9B,CAAC;IACD,OAAOb,OAAO,GAAGmF,MAAM,GAAGJ,qBAAqB,CAACI,MAAM,CAAC;EACzD,CAAC;EACD;EACA7C,QAAQ,WAAAA,CAACtC,OAAO,EAAEoC,aAAa,EAAE;IAC/B,IAAI,CAAC9D,MAAM,CAAC+C,IAAI,CAACe,aAAa,EAAE,QAAQ,CAAC,EAAE;MACzC,MAAMhC,KAAK,CAAC,yBAAyB,CAAC;IACxC;IAEA,OAAOgF,iBAAiB;EAC1B,CAAC;EACD;EACAC,YAAY,WAAAA,CAACrF,OAAO,EAAEoC,aAAa,EAAE;IACnC,IAAI,CAACA,aAAa,CAACkD,KAAK,EAAE;MACxB,MAAMlF,KAAK,CAAC,4BAA4B,CAAC;IAC3C;IAEA,OAAOgF,iBAAiB;EAC1B,CAAC;EACDG,IAAI,WAAAA,CAACvF,OAAO,EAAEoC,aAAa,EAAErB,OAAO,EAAE;IACpC,IAAI,CAACd,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;MAC3B,MAAMI,KAAK,CAAC,qBAAqB,CAAC;IACpC;;IAEA;IACA,IAAIJ,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOpB,cAAc;IACvB;IAEA,IAAMyG,gBAAgB,GAAGxF,OAAO,CAACU,GAAG,CAAC,UAAA+E,SAAS,EAAI;MAChD;MACA,IAAI5G,gBAAgB,CAAC4G,SAAS,CAAC,EAAE;QAC/B,MAAMrF,KAAK,CAAC,0BAA0B,CAAC;MACzC;;MAEA;MACA,OAAOmD,oBAAoB,CAACkC,SAAS,EAAE1E,OAAO,CAAC;IACjD,CAAC,CAAC;;IAEF;IACA;IACA,OAAO2E,mBAAmB,CAACF,gBAAgB,CAAC;EAC9C,CAAC;EACDF,KAAK,WAAAA,CAACtF,OAAO,EAAEoC,aAAa,EAAErB,OAAO,EAAE4E,MAAM,EAAE;IAC7C,IAAI,CAACA,MAAM,EAAE;MACX,MAAMvF,KAAK,CAAC,2CAA2C,CAAC;IAC1D;IAEAW,OAAO,CAAC6E,YAAY,GAAG,IAAI;;IAE3B;IACA;IACA;IACA;IACA,IAAIC,WAAW,EAAEC,KAAK,EAAEC,QAAQ;IAChC,IAAI3G,eAAe,CAACsD,cAAc,CAAC1C,OAAO,CAAC,IAAI1B,MAAM,CAAC+C,IAAI,CAACrB,OAAO,EAAE,WAAW,CAAC,EAAE;MAChF;MACA6F,WAAW,GAAG7F,OAAO,CAACqF,YAAY;MAClCS,KAAK,GAAG9F,OAAO,CAACgG,SAAS;MACzBD,QAAQ,GAAG,SAAAA,CAAAxF,KAAK,EAAI;QAClB;QACA;QACA;QACA,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,IAAI;QACb;QAEA,IAAI,CAACA,KAAK,CAAC0F,IAAI,EAAE;UACf,OAAOC,OAAO,CAACC,aAAa,CAC1BL,KAAK,EACL;YAACG,IAAI,EAAE,OAAO;YAAEG,WAAW,EAAEC,YAAY,CAAC9F,KAAK;UAAC,CAClD,CAAC;QACH;QAEA,IAAIA,KAAK,CAAC0F,IAAI,KAAK,OAAO,EAAE;UAC1B,OAAOC,OAAO,CAACC,aAAa,CAACL,KAAK,EAAEvF,KAAK,CAAC;QAC5C;QAEA,OAAO2F,OAAO,CAACI,oBAAoB,CAAC/F,KAAK,EAAEuF,KAAK,EAAED,WAAW,CAAC,GAC1D,CAAC,GACDA,WAAW,GAAG,CAAC;MACrB,CAAC;IACH,CAAC,MAAM;MACLA,WAAW,GAAGzD,aAAa,CAACiD,YAAY;MAExC,IAAI,CAAC1G,WAAW,CAACqB,OAAO,CAAC,EAAE;QACzB,MAAMI,KAAK,CAAC,mDAAmD,CAAC;MAClE;MAEA0F,KAAK,GAAGO,YAAY,CAACrG,OAAO,CAAC;MAE7B+F,QAAQ,GAAG,SAAAA,CAAAxF,KAAK,EAAI;QAClB,IAAI,CAAC5B,WAAW,CAAC4B,KAAK,CAAC,EAAE;UACvB,OAAO,IAAI;QACb;QAEA,OAAOgG,uBAAuB,CAACT,KAAK,EAAEvF,KAAK,CAAC;MAC9C,CAAC;IACH;IAEA,OAAO,UAAAiG,cAAc,EAAI;MACvB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAM7C,MAAM,GAAG;QAACA,MAAM,EAAE;MAAK,CAAC;MAC9BjF,sBAAsB,CAAC8H,cAAc,CAAC,CAAC3E,KAAK,CAAC,UAAA4E,MAAM,EAAI;QACrD;QACA;QACA,IAAIC,WAAW;QACf,IAAI,CAAC3F,OAAO,CAAC4F,SAAS,EAAE;UACtB,IAAI,EAAEvI,OAAA,CAAOqI,MAAM,CAAClG,KAAK,MAAK,QAAQ,CAAC,EAAE;YACvC,OAAO,IAAI;UACb;UAEAmG,WAAW,GAAGX,QAAQ,CAACU,MAAM,CAAClG,KAAK,CAAC;;UAEpC;UACA,IAAImG,WAAW,KAAK,IAAI,IAAIA,WAAW,GAAGb,WAAW,EAAE;YACrD,OAAO,IAAI;UACb;;UAEA;UACA,IAAIlC,MAAM,CAACoC,QAAQ,KAAKlF,SAAS,IAAI8C,MAAM,CAACoC,QAAQ,IAAIW,WAAW,EAAE;YACnE,OAAO,IAAI;UACb;QACF;QAEA/C,MAAM,CAACA,MAAM,GAAG,IAAI;QACpBA,MAAM,CAACoC,QAAQ,GAAGW,WAAW;QAE7B,IAAID,MAAM,CAACG,YAAY,EAAE;UACvBjD,MAAM,CAACiD,YAAY,GAAGH,MAAM,CAACG,YAAY;QAC3C,CAAC,MAAM;UACL,OAAOjD,MAAM,CAACiD,YAAY;QAC5B;QAEA,OAAO,CAAC7F,OAAO,CAAC4F,SAAS;MAC3B,CAAC,CAAC;MAEF,OAAOhD,MAAM;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASkD,eAAeA,CAACC,WAAW,EAAE;EACpC,IAAIA,WAAW,CAAC3G,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAOiF,iBAAiB;EAC1B;EAEA,IAAI0B,WAAW,CAAC3G,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAO2G,WAAW,CAAC,CAAC,CAAC;EACvB;EAEA,OAAO,UAAAC,aAAa,EAAI;IACtB,IAAMC,KAAK,GAAG,CAAC,CAAC;IAChBA,KAAK,CAACrD,MAAM,GAAGmD,WAAW,CAACjF,KAAK,CAAC,UAAAsC,EAAE,EAAI;MACrC,IAAM8C,SAAS,GAAG9C,EAAE,CAAC4C,aAAa,CAAC;;MAEnC;MACA;MACA;MACA;MACA,IAAIE,SAAS,CAACtD,MAAM,IAChBsD,SAAS,CAAClB,QAAQ,KAAKlF,SAAS,IAChCmG,KAAK,CAACjB,QAAQ,KAAKlF,SAAS,EAAE;QAChCmG,KAAK,CAACjB,QAAQ,GAAGkB,SAAS,CAAClB,QAAQ;MACrC;;MAEA;MACA;MACA;MACA,IAAIkB,SAAS,CAACtD,MAAM,IAAIsD,SAAS,CAACL,YAAY,EAAE;QAC9CI,KAAK,CAACJ,YAAY,GAAGK,SAAS,CAACL,YAAY;MAC7C;MAEA,OAAOK,SAAS,CAACtD,MAAM;IACzB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACqD,KAAK,CAACrD,MAAM,EAAE;MACjB,OAAOqD,KAAK,CAACjB,QAAQ;MACrB,OAAOiB,KAAK,CAACJ,YAAY;IAC3B;IAEA,OAAOI,KAAK;EACd,CAAC;AACH;AAEA,IAAMlD,mBAAmB,GAAG+C,eAAe;AAC3C,IAAMnB,mBAAmB,GAAGmB,eAAe;AAE3C,SAAS9C,+BAA+BA,CAACmD,SAAS,EAAEnG,OAAO,EAAEuC,WAAW,EAAE;EACxE,IAAI,CAACrD,KAAK,CAACC,OAAO,CAACgH,SAAS,CAAC,IAAIA,SAAS,CAAC/G,MAAM,KAAK,CAAC,EAAE;IACvD,MAAMC,KAAK,CAAC,sCAAsC,CAAC;EACrD;EAEA,OAAO8G,SAAS,CAACxG,GAAG,CAAC,UAAAmD,WAAW,EAAI;IAClC,IAAI,CAACzE,eAAe,CAACsD,cAAc,CAACmB,WAAW,CAAC,EAAE;MAChD,MAAMzD,KAAK,CAAC,+CAA+C,CAAC;IAC9D;IAEA,OAAO5B,uBAAuB,CAACqF,WAAW,EAAE9C,OAAO,EAAE;MAACuC,WAAW,EAAXA;IAAW,CAAC,CAAC;EACrE,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS9E,uBAAuBA,CAAC2I,WAAW,EAAEpG,OAAO,EAAgB;EAAA,IAAdqG,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACxE,IAAMC,WAAW,GAAGjI,MAAM,CAACuD,IAAI,CAACuE,WAAW,CAAC,CAACzG,GAAG,CAAC,UAAAoC,GAAG,EAAI;IACtD,IAAMe,WAAW,GAAGsD,WAAW,CAACrE,GAAG,CAAC;IAEpC,IAAIA,GAAG,CAACyE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5B;MACA;MACA,IAAI,CAACjJ,MAAM,CAAC+C,IAAI,CAAC0B,iBAAiB,EAAED,GAAG,CAAC,EAAE;QACxC,MAAM,IAAI1C,KAAK,qCAAmC0C,GAAK,CAAC;MAC1D;MAEA/B,OAAO,CAACyG,SAAS,GAAG,KAAK;MACzB,OAAOzE,iBAAiB,CAACD,GAAG,CAAC,CAACe,WAAW,EAAE9C,OAAO,EAAEqG,OAAO,CAAC9D,WAAW,CAAC;IAC1E;;IAEA;IACA;IACA;IACA,IAAI,CAAC8D,OAAO,CAAC9D,WAAW,EAAE;MACxBvC,OAAO,CAACwD,eAAe,CAACzB,GAAG,CAAC;IAC9B;;IAEA;IACA;IACA;IACA,IAAI,OAAOe,WAAW,KAAK,UAAU,EAAE;MACrC,OAAOhD,SAAS;IAClB;IAEA,IAAM4G,aAAa,GAAG3I,kBAAkB,CAACgE,GAAG,CAAC;IAC7C,IAAM4E,YAAY,GAAGnE,oBAAoB,CACvCM,WAAW,EACX9C,OAAO,EACPqG,OAAO,CAACzB,MACV,CAAC;IAED,OAAO,UAAAzB,GAAG;MAAA,OAAIwD,YAAY,CAACD,aAAa,CAACvD,GAAG,CAAC,CAAC;IAAA;EAChD,CAAC,CAAC,CAACrB,MAAM,CAAC8E,OAAO,CAAC;EAElB,OAAO7D,mBAAmB,CAACwD,WAAW,CAAC;AACzC;AAEA;AACA;AACA;AACA;AACA,SAAS/D,oBAAoBA,CAACnB,aAAa,EAAErB,OAAO,EAAE4E,MAAM,EAAE;EAC5D,IAAIvD,aAAa,YAAYxB,MAAM,EAAE;IACnCG,OAAO,CAACyG,SAAS,GAAG,KAAK;IACzB,OAAO3C,sCAAsC,CAC3C1F,oBAAoB,CAACiD,aAAa,CACpC,CAAC;EACH;EAEA,IAAIvD,gBAAgB,CAACuD,aAAa,CAAC,EAAE;IACnC,OAAOwF,uBAAuB,CAACxF,aAAa,EAAErB,OAAO,EAAE4E,MAAM,CAAC;EAChE;EAEA,OAAOd,sCAAsC,CAC3CpG,sBAAsB,CAAC2D,aAAa,CACtC,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASyC,sCAAsCA,CAACgD,cAAc,EAAgB;EAAA,IAAdT,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAC1E,OAAO,UAAAS,QAAQ,EAAI;IACjB,IAAMC,QAAQ,GAAGX,OAAO,CAACnG,oBAAoB,GACzC6G,QAAQ,GACRpJ,sBAAsB,CAACoJ,QAAQ,EAAEV,OAAO,CAACjG,qBAAqB,CAAC;IAEnE,IAAM6F,KAAK,GAAG,CAAC,CAAC;IAChBA,KAAK,CAACrD,MAAM,GAAGoE,QAAQ,CAACjH,IAAI,CAAC,UAAAkH,OAAO,EAAI;MACtC,IAAIC,OAAO,GAAGJ,cAAc,CAACG,OAAO,CAACzH,KAAK,CAAC;;MAE3C;MACA;MACA,IAAI,OAAO0H,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACA;QACA;QACA,IAAI,CAACD,OAAO,CAACpB,YAAY,EAAE;UACzBoB,OAAO,CAACpB,YAAY,GAAG,CAACqB,OAAO,CAAC;QAClC;QAEAA,OAAO,GAAG,IAAI;MAChB;;MAEA;MACA;MACA,IAAIA,OAAO,IAAID,OAAO,CAACpB,YAAY,EAAE;QACnCI,KAAK,CAACJ,YAAY,GAAGoB,OAAO,CAACpB,YAAY;MAC3C;MAEA,OAAOqB,OAAO;IAChB,CAAC,CAAC;IAEF,OAAOjB,KAAK;EACd,CAAC;AACH;;AAEA;AACA,SAAST,uBAAuBA,CAACtD,CAAC,EAAEC,CAAC,EAAE;EACrC,IAAMgF,MAAM,GAAG7B,YAAY,CAACpD,CAAC,CAAC;EAC9B,IAAMkF,MAAM,GAAG9B,YAAY,CAACnD,CAAC,CAAC;EAE9B,OAAOkF,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAED,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjE;;AAEA;AACA;AACO,SAAS1J,sBAAsBA,CAAC6J,eAAe,EAAE;EACtD,IAAIzJ,gBAAgB,CAACyJ,eAAe,CAAC,EAAE;IACrC,MAAMlI,KAAK,CAAC,yDAAyD,CAAC;EACxE;;EAEA;EACA;EACA;EACA;EACA,IAAIkI,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAO,UAAA/H,KAAK;MAAA,OAAIA,KAAK,IAAI,IAAI;IAAA;EAC/B;EAEA,OAAO,UAAAA,KAAK;IAAA,OAAInB,eAAe,CAACkC,EAAE,CAACiH,MAAM,CAACD,eAAe,EAAE/H,KAAK,CAAC;EAAA;AACnE;AAEA,SAAS6E,iBAAiBA,CAACoD,mBAAmB,EAAE;EAC9C,OAAO;IAAC7E,MAAM,EAAE;EAAI,CAAC;AACvB;AAEO,SAASjF,sBAAsBA,CAACoJ,QAAQ,EAAEW,aAAa,EAAE;EAC9D,IAAMC,WAAW,GAAG,EAAE;EAEtBZ,QAAQ,CAACa,OAAO,CAAC,UAAAlC,MAAM,EAAI;IACzB,IAAMmC,WAAW,GAAG3I,KAAK,CAACC,OAAO,CAACuG,MAAM,CAAClG,KAAK,CAAC;;IAE/C;IACA;IACA;IACA;IACA,IAAI,EAAEkI,aAAa,IAAIG,WAAW,IAAI,CAACnC,MAAM,CAAC/C,WAAW,CAAC,EAAE;MAC1DgF,WAAW,CAACG,IAAI,CAAC;QAACjC,YAAY,EAAEH,MAAM,CAACG,YAAY;QAAErG,KAAK,EAAEkG,MAAM,CAAClG;MAAK,CAAC,CAAC;IAC5E;IAEA,IAAIqI,WAAW,IAAI,CAACnC,MAAM,CAAC/C,WAAW,EAAE;MACtC+C,MAAM,CAAClG,KAAK,CAACoI,OAAO,CAAC,UAACpI,KAAK,EAAEwB,CAAC,EAAK;QACjC2G,WAAW,CAACG,IAAI,CAAC;UACfjC,YAAY,EAAE,CAACH,MAAM,CAACG,YAAY,IAAI,EAAE,EAAEkC,MAAM,CAAC/G,CAAC,CAAC;UACnDxB,KAAK,EAALA;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,OAAOmI,WAAW;AACpB;AAEA;AACA,SAAShH,iBAAiBA,CAAC1B,OAAO,EAAE+I,QAAQ,EAAE;EAC5C;EACA;EACA;EACA;EACA,IAAIC,MAAM,CAACC,SAAS,CAACjJ,OAAO,CAAC,IAAIA,OAAO,IAAI,CAAC,EAAE;IAC7C,OAAO,IAAIkJ,UAAU,CAAC,IAAIC,UAAU,CAAC,CAACnJ,OAAO,CAAC,CAAC,CAACoJ,MAAM,CAAC;EACzD;;EAEA;EACA;EACA,IAAIC,KAAK,CAACC,QAAQ,CAACtJ,OAAO,CAAC,EAAE;IAC3B,OAAO,IAAIkJ,UAAU,CAAClJ,OAAO,CAACoJ,MAAM,CAAC;EACvC;;EAEA;EACA;EACA;EACA,IAAInJ,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IACtBA,OAAO,CAAC6B,KAAK,CAAC,UAAA0H,CAAC;IAAA,OAAIP,MAAM,CAACC,SAAS,CAACM,CAAC,CAAC,IAAIA,CAAC,IAAI,CAAC;EAAA,EAAC,EAAE;IACrD,IAAMH,MAAM,GAAG,IAAII,WAAW,CAAC,CAACpB,IAAI,CAACqB,GAAG,CAAAC,KAAA,CAARtB,IAAI,EAAAnK,kBAAA,CAAQ+B,OAAO,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAM2J,IAAI,GAAG,IAAIT,UAAU,CAACE,MAAM,CAAC;IAEnCpJ,OAAO,CAAC2I,OAAO,CAAC,UAAAY,CAAC,EAAI;MACnBI,IAAI,CAACJ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,CAAC;IAChC,CAAC,CAAC;IAEF,OAAOI,IAAI;EACb;;EAEA;EACA,MAAMvJ,KAAK,CACT,gBAAc2I,QAAQ,uDACtB,0EAA0E,GAC1E,uCACF,CAAC;AACH;AAEA,SAASnH,eAAeA,CAACrB,KAAK,EAAEJ,MAAM,EAAE;EACtC;EACA;;EAEA;EACA,IAAI6I,MAAM,CAACY,aAAa,CAACrJ,KAAK,CAAC,EAAE;IAC/B;IACA;IACA;IACA;IACA,IAAM6I,MAAM,GAAG,IAAII,WAAW,CAC5BpB,IAAI,CAACqB,GAAG,CAACtJ,MAAM,EAAE,CAAC,GAAG0J,WAAW,CAACC,iBAAiB,CACpD,CAAC;IAED,IAAIH,IAAI,GAAG,IAAIE,WAAW,CAACT,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACxCO,IAAI,CAAC,CAAC,CAAC,GAAGpJ,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;IAC7CoJ,IAAI,CAAC,CAAC,CAAC,GAAGpJ,KAAK,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;;IAE7C;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MACboJ,IAAI,GAAG,IAAIT,UAAU,CAACE,MAAM,EAAE,CAAC,CAAC;MAChCO,IAAI,CAAChB,OAAO,CAAC,UAAC7G,IAAI,EAAEC,CAAC,EAAK;QACxB4H,IAAI,CAAC5H,CAAC,CAAC,GAAG,IAAI;MAChB,CAAC,CAAC;IACJ;IAEA,OAAO,IAAImH,UAAU,CAACE,MAAM,CAAC;EAC/B;;EAEA;EACA,IAAIC,KAAK,CAACC,QAAQ,CAAC/I,KAAK,CAAC,EAAE;IACzB,OAAO,IAAI2I,UAAU,CAAC3I,KAAK,CAAC6I,MAAM,CAAC;EACrC;;EAEA;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA,SAASW,kBAAkBA,CAACC,QAAQ,EAAElH,GAAG,EAAEvC,KAAK,EAAE;EAChDlB,MAAM,CAACuD,IAAI,CAACoH,QAAQ,CAAC,CAACrB,OAAO,CAAC,UAAAsB,WAAW,EAAI;IAC3C,IACGA,WAAW,CAAC9J,MAAM,GAAG2C,GAAG,CAAC3C,MAAM,IAAI8J,WAAW,CAACC,OAAO,CAAIpH,GAAG,MAAG,CAAC,KAAK,CAAC,IACvEA,GAAG,CAAC3C,MAAM,GAAG8J,WAAW,CAAC9J,MAAM,IAAI2C,GAAG,CAACoH,OAAO,CAAID,WAAW,MAAG,CAAC,KAAK,CAAE,EACzE;MACA,MAAM,IAAI7J,KAAK,CACb,mDAAiD6J,WAAW,qBACxDnH,GAAG,mBACT,CAAC;IACH,CAAC,MAAM,IAAImH,WAAW,KAAKnH,GAAG,EAAE;MAC9B,MAAM,IAAI1C,KAAK,8CAC8B0C,GAAG,uBAChD,CAAC;IACH;EACF,CAAC,CAAC;EAEFkH,QAAQ,CAAClH,GAAG,CAAC,GAAGvC,KAAK;AACvB;;AAEA;AACA;AACA;AACA,SAASwE,qBAAqBA,CAACoF,eAAe,EAAE;EAC9C,OAAO,UAAAC,YAAY,EAAI;IACrB;IACA;IACA;IACA,OAAO;MAACzG,MAAM,EAAE,CAACwG,eAAe,CAACC,YAAY,CAAC,CAACzG;IAAM,CAAC;EACxD,CAAC;AACH;AAEO,SAAShF,WAAWA,CAAC0L,GAAG,EAAE;EAC/B,OAAOpK,KAAK,CAACC,OAAO,CAACmK,GAAG,CAAC,IAAIjL,eAAe,CAACsD,cAAc,CAAC2H,GAAG,CAAC;AAClE;AAEO,SAASzL,YAAYA,CAAC0L,CAAC,EAAE;EAC9B,OAAO,UAAU,CAAC/H,IAAI,CAAC+H,CAAC,CAAC;AAC3B;AAKO,SAASzL,gBAAgBA,CAACuD,aAAa,EAAEmI,cAAc,EAAE;EAC9D,IAAI,CAACnL,eAAe,CAACsD,cAAc,CAACN,aAAa,CAAC,EAAE;IAClD,OAAO,KAAK;EACd;EAEA,IAAIoI,iBAAiB,GAAG3J,SAAS;EACjCxB,MAAM,CAACuD,IAAI,CAACR,aAAa,CAAC,CAACuG,OAAO,CAAC,UAAA8B,MAAM,EAAI;IAC3C,IAAMC,cAAc,GAAGD,MAAM,CAAClD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,IAAIkD,MAAM,KAAK,MAAM;IAEvE,IAAID,iBAAiB,KAAK3J,SAAS,EAAE;MACnC2J,iBAAiB,GAAGE,cAAc;IACpC,CAAC,MAAM,IAAIF,iBAAiB,KAAKE,cAAc,EAAE;MAC/C,IAAI,CAACH,cAAc,EAAE;QACnB,MAAM,IAAInK,KAAK,6BACauK,IAAI,CAACC,SAAS,CAACxI,aAAa,CACxD,CAAC;MACH;MAEAoI,iBAAiB,GAAG,KAAK;IAC3B;EACF,CAAC,CAAC;EAEF,OAAO,CAAC,CAACA,iBAAiB,CAAC,CAAC;AAC9B;AAEA;AACA,SAAS/K,cAAcA,CAACoL,kBAAkB,EAAE;EAC1C,OAAO;IACL9K,sBAAsB,WAAAA,CAACC,OAAO,EAAE;MAC9B;MACA;MACA;MACA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;QAC1B,OAAO;UAAA,OAAM,KAAK;QAAA;MACpB;;MAEA;MACA;MACA,IAAIA,OAAO,KAAKa,SAAS,EAAE;QACzBb,OAAO,GAAG,IAAI;MAChB;MAEA,IAAM8K,WAAW,GAAG1L,eAAe,CAACkC,EAAE,CAACC,KAAK,CAACvB,OAAO,CAAC;MAErD,OAAO,UAAAO,KAAK,EAAI;QACd,IAAIA,KAAK,KAAKM,SAAS,EAAE;UACvBN,KAAK,GAAG,IAAI;QACd;;QAEA;QACA;QACA,IAAInB,eAAe,CAACkC,EAAE,CAACC,KAAK,CAAChB,KAAK,CAAC,KAAKuK,WAAW,EAAE;UACnD,OAAO,KAAK;QACd;QAEA,OAAOD,kBAAkB,CAACzL,eAAe,CAACkC,EAAE,CAACyJ,IAAI,CAACxK,KAAK,EAAEP,OAAO,CAAC,CAAC;MACpE,CAAC;IACH;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASlB,kBAAkBA,CAACgE,GAAG,EAAgB;EAAA,IAAdsE,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAClD,IAAM2D,KAAK,GAAGlI,GAAG,CAACmI,KAAK,CAAC,GAAG,CAAC;EAC5B,IAAMC,SAAS,GAAGF,KAAK,CAAC7K,MAAM,GAAG6K,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EAC9C,IAAMG,UAAU,GACdH,KAAK,CAAC7K,MAAM,GAAG,CAAC,IAChBrB,kBAAkB,CAACkM,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAEjE,OAAO,CACrD;EAED,SAASkE,WAAWA,CAAC1E,YAAY,EAAElD,WAAW,EAAEnD,KAAK,EAAE;IACrD,OAAOqG,YAAY,IAAIA,YAAY,CAACzG,MAAM,GACtCuD,WAAW,GACT,CAAC;MAAEkD,YAAY,EAAZA,YAAY;MAAElD,WAAW,EAAXA,WAAW;MAAEnD,KAAK,EAALA;IAAM,CAAC,CAAC,GACtC,CAAC;MAAEqG,YAAY,EAAZA,YAAY;MAAErG,KAAK,EAALA;IAAM,CAAC,CAAC,GAC3BmD,WAAW,GACT,CAAC;MAAEA,WAAW,EAAXA,WAAW;MAAEnD,KAAK,EAALA;IAAM,CAAC,CAAC,GACxB,CAAC;MAAEA,KAAK,EAALA;IAAM,CAAC,CAAC;EACnB;;EAEA;EACA;EACA,OAAO,UAAC2D,GAAG,EAAE0C,YAAY,EAAK;IAC5B,IAAI3G,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,EAAE;MACtB;MACA;MACA;MACA,IAAI,EAAEtF,YAAY,CAACsM,SAAS,CAAC,IAAIA,SAAS,GAAGhH,GAAG,CAAC/D,MAAM,CAAC,EAAE;QACxD,OAAO,EAAE;MACX;;MAEA;MACA;MACA;MACAyG,YAAY,GAAGA,YAAY,GAAGA,YAAY,CAACkC,MAAM,CAAC,CAACoC,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,CAACA,SAAS,EAAE,GAAG,CAAC;IACxF;;IAEA;IACA,IAAMK,UAAU,GAAGrH,GAAG,CAACgH,SAAS,CAAC;;IAEjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,UAAU,EAAE;MACf,OAAOG,WAAW,CAChB1E,YAAY,EACZ3G,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,IAAIjE,KAAK,CAACC,OAAO,CAACqL,UAAU,CAAC,EAC/CA,UACF,CAAC;IACH;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5M,WAAW,CAAC4M,UAAU,CAAC,EAAE;MAC5B,IAAItL,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,EAAE;QACtB,OAAO,EAAE;MACX;MAEA,OAAOoH,WAAW,CAAC1E,YAAY,EAAE,KAAK,EAAE/F,SAAS,CAAC;IACpD;IAEA,IAAM8C,MAAM,GAAG,EAAE;IACjB,IAAM6H,cAAc,GAAG,SAAAA,CAAAC,IAAI,EAAI;MAC7B9H,MAAM,CAACkF,IAAI,CAAAa,KAAA,CAAX/F,MAAM,EAAA1F,kBAAA,CAASwN,IAAI,EAAC;IACtB,CAAC;;IAED;IACA;IACA;IACAD,cAAc,CAACL,UAAU,CAACI,UAAU,EAAE3E,YAAY,CAAC,CAAC;;IAEpD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI3G,KAAK,CAACC,OAAO,CAACqL,UAAU,CAAC,IACzB,EAAE3M,YAAY,CAACoM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI5D,OAAO,CAACsE,OAAO,CAAC,EAAE;MAChDH,UAAU,CAAC5C,OAAO,CAAC,UAAClC,MAAM,EAAEkF,UAAU,EAAK;QACzC,IAAIvM,eAAe,CAACsD,cAAc,CAAC+D,MAAM,CAAC,EAAE;UAC1C+E,cAAc,CAACL,UAAU,CAAC1E,MAAM,EAAEG,YAAY,GAAGA,YAAY,CAACkC,MAAM,CAAC6C,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;QACnG;MACF,CAAC,CAAC;IACJ;IAEA,OAAOhI,MAAM;EACf,CAAC;AACH;AAEA;AACA;AACAiI,aAAa,GAAG;EAAC9M,kBAAkB,EAAlBA;AAAkB,CAAC;AACpC+M,cAAc,GAAG,SAAAA,CAACC,OAAO,EAAmB;EAAA,IAAjB1E,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACrC,IAAI,OAAOyE,OAAO,KAAK,QAAQ,IAAI1E,OAAO,CAAC2E,KAAK,EAAE;IAChDD,OAAO,qBAAmB1E,OAAO,CAAC2E,KAAK,MAAG;EAC5C;EAEA,IAAMC,KAAK,GAAG,IAAI5L,KAAK,CAAC0L,OAAO,CAAC;EAChCE,KAAK,CAACC,IAAI,GAAG,gBAAgB;EAC7B,OAAOD,KAAK;AACd,CAAC;AAEM,SAASjN,cAAcA,CAACyJ,mBAAmB,EAAE;EAClD,OAAO;IAAC7E,MAAM,EAAE;EAAK,CAAC;AACxB;AAEA;AACA;AACA,SAASiE,uBAAuBA,CAACxF,aAAa,EAAErB,OAAO,EAAE4E,MAAM,EAAE;EAC/D;EACA;EACA;EACA,IAAMuG,gBAAgB,GAAG7M,MAAM,CAACuD,IAAI,CAACR,aAAa,CAAC,CAAC1B,GAAG,CAAC,UAAAyL,QAAQ,EAAI;IAClE,IAAMnM,OAAO,GAAGoC,aAAa,CAAC+J,QAAQ,CAAC;IAEvC,IAAMC,WAAW,GACf,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACF,QAAQ,CAAC,IACjD,OAAOnM,OAAO,KAAK,QACpB;IAED,IAAMsM,cAAc,GAClB,CAAC,KAAK,EAAE,KAAK,CAAC,CAACD,QAAQ,CAACF,QAAQ,CAAC,IACjCnM,OAAO,KAAKX,MAAM,CAACW,OAAO,CAC3B;IAED,IAAMuM,eAAe,GACnB,CAAC,KAAK,EAAE,MAAM,CAAC,CAACF,QAAQ,CAACF,QAAQ,CAAC,IAC/BlM,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IACtB,CAACA,OAAO,CAACc,IAAI,CAAC,UAAAyI,CAAC;MAAA,OAAIA,CAAC,KAAKlK,MAAM,CAACkK,CAAC,CAAC;IAAA,EACtC;IAED,IAAI,EAAE6C,WAAW,IAAIG,eAAe,IAAID,cAAc,CAAC,EAAE;MACvDvL,OAAO,CAACyG,SAAS,GAAG,KAAK;IAC3B;IAEA,IAAIlJ,MAAM,CAAC+C,IAAI,CAACsD,eAAe,EAAEwH,QAAQ,CAAC,EAAE;MAC1C,OAAOxH,eAAe,CAACwH,QAAQ,CAAC,CAACnM,OAAO,EAAEoC,aAAa,EAAErB,OAAO,EAAE4E,MAAM,CAAC;IAC3E;IAEA,IAAIrH,MAAM,CAAC+C,IAAI,CAAC9C,iBAAiB,EAAE4N,QAAQ,CAAC,EAAE;MAC5C,IAAM/E,OAAO,GAAG7I,iBAAiB,CAAC4N,QAAQ,CAAC;MAC3C,OAAOtH,sCAAsC,CAC3CuC,OAAO,CAACrH,sBAAsB,CAACC,OAAO,EAAEoC,aAAa,EAAErB,OAAO,CAAC,EAC/DqG,OACF,CAAC;IACH;IAEA,MAAM,IAAIhH,KAAK,6BAA2B+L,QAAU,CAAC;EACvD,CAAC,CAAC;EAEF,OAAOzG,mBAAmB,CAACwG,gBAAgB,CAAC;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASlN,WAAWA,CAACwN,KAAK,EAAEC,SAAS,EAAEC,UAAU,EAAa;EAAA,IAAXC,IAAI,GAAAtF,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACjEmF,KAAK,CAAC7D,OAAO,CAAC,UAAAiE,IAAI,EAAI;IACpB,IAAMC,SAAS,GAAGD,IAAI,CAAC3B,KAAK,CAAC,GAAG,CAAC;IACjC,IAAI6B,IAAI,GAAGH,IAAI;;IAEf;IACA,IAAMI,OAAO,GAAGF,SAAS,CAACzB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACvJ,KAAK,CAAC,UAACiB,GAAG,EAAEf,CAAC,EAAK;MACvD,IAAI,CAACzD,MAAM,CAAC+C,IAAI,CAACyL,IAAI,EAAEhK,GAAG,CAAC,EAAE;QAC3BgK,IAAI,CAAChK,GAAG,CAAC,GAAG,CAAC,CAAC;MAChB,CAAC,MAAM,IAAIgK,IAAI,CAAChK,GAAG,CAAC,KAAKzD,MAAM,CAACyN,IAAI,CAAChK,GAAG,CAAC,CAAC,EAAE;QAC1CgK,IAAI,CAAChK,GAAG,CAAC,GAAG4J,UAAU,CACpBI,IAAI,CAAChK,GAAG,CAAC,EACT+J,SAAS,CAACzB,KAAK,CAAC,CAAC,EAAErJ,CAAC,GAAG,CAAC,CAAC,CAACsJ,IAAI,CAAC,GAAG,CAAC,EACnCuB,IACF,CAAC;;QAED;QACA,IAAIE,IAAI,CAAChK,GAAG,CAAC,KAAKzD,MAAM,CAACyN,IAAI,CAAChK,GAAG,CAAC,CAAC,EAAE;UACnC,OAAO,KAAK;QACd;MACF;MAEAgK,IAAI,GAAGA,IAAI,CAAChK,GAAG,CAAC;MAEhB,OAAO,IAAI;IACb,CAAC,CAAC;IAEF,IAAIiK,OAAO,EAAE;MACX,IAAMC,OAAO,GAAGH,SAAS,CAACA,SAAS,CAAC1M,MAAM,GAAG,CAAC,CAAC;MAC/C,IAAI7B,MAAM,CAAC+C,IAAI,CAACyL,IAAI,EAAEE,OAAO,CAAC,EAAE;QAC9BF,IAAI,CAACE,OAAO,CAAC,GAAGN,UAAU,CAACI,IAAI,CAACE,OAAO,CAAC,EAAEJ,IAAI,EAAEA,IAAI,CAAC;MACvD,CAAC,MAAM;QACLE,IAAI,CAACE,OAAO,CAAC,GAAGP,SAAS,CAACG,IAAI,CAAC;MACjC;IACF;EACF,CAAC,CAAC;EAEF,OAAOD,IAAI;AACb;AAEA;AACA;AACA;AACA,SAAStG,YAAYA,CAACP,KAAK,EAAE;EAC3B,OAAO7F,KAAK,CAACC,OAAO,CAAC4F,KAAK,CAAC,GAAGA,KAAK,CAACsF,KAAK,CAAC,CAAC,GAAG,CAACtF,KAAK,CAACyD,CAAC,EAAEzD,KAAK,CAACmH,CAAC,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASC,4BAA4BA,CAAClD,QAAQ,EAAElH,GAAG,EAAEvC,KAAK,EAAE;EAC1D,IAAIA,KAAK,IAAIlB,MAAM,CAAC8N,cAAc,CAAC5M,KAAK,CAAC,KAAKlB,MAAM,CAACC,SAAS,EAAE;IAC9D8N,0BAA0B,CAACpD,QAAQ,EAAElH,GAAG,EAAEvC,KAAK,CAAC;EAClD,CAAC,MAAM,IAAI,EAAEA,KAAK,YAAYK,MAAM,CAAC,EAAE;IACrCmJ,kBAAkB,CAACC,QAAQ,EAAElH,GAAG,EAAEvC,KAAK,CAAC;EAC1C;AACF;;AAEA;AACA;AACA,SAAS6M,0BAA0BA,CAACpD,QAAQ,EAAElH,GAAG,EAAEvC,KAAK,EAAE;EACxD,IAAMqC,IAAI,GAAGvD,MAAM,CAACuD,IAAI,CAACrC,KAAK,CAAC;EAC/B,IAAM8M,cAAc,GAAGzK,IAAI,CAACC,MAAM,CAAC,UAAAyK,EAAE;IAAA,OAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG;EAAA,EAAC;EAEvD,IAAID,cAAc,CAAClN,MAAM,GAAG,CAAC,IAAI,CAACyC,IAAI,CAACzC,MAAM,EAAE;IAC7C;IACA;IACA,IAAIyC,IAAI,CAACzC,MAAM,KAAKkN,cAAc,CAAClN,MAAM,EAAE;MACzC,MAAM,IAAIC,KAAK,wBAAsBiN,cAAc,CAAC,CAAC,CAAG,CAAC;IAC3D;IAEAE,cAAc,CAAChN,KAAK,EAAEuC,GAAG,CAAC;IAC1BiH,kBAAkB,CAACC,QAAQ,EAAElH,GAAG,EAAEvC,KAAK,CAAC;EAC1C,CAAC,MAAM;IACLlB,MAAM,CAACuD,IAAI,CAACrC,KAAK,CAAC,CAACoI,OAAO,CAAC,UAAA2E,EAAE,EAAI;MAC/B,IAAME,MAAM,GAAGjN,KAAK,CAAC+M,EAAE,CAAC;MAExB,IAAIA,EAAE,KAAK,KAAK,EAAE;QAChBJ,4BAA4B,CAAClD,QAAQ,EAAElH,GAAG,EAAE0K,MAAM,CAAC;MACrD,CAAC,MAAM,IAAIF,EAAE,KAAK,MAAM,EAAE;QACxB;QACAE,MAAM,CAAC7E,OAAO,CAAC,UAAAX,OAAO;UAAA,OACpBkF,4BAA4B,CAAClD,QAAQ,EAAElH,GAAG,EAAEkF,OAAO,CAAC;QAAA,CACtD,CAAC;MACH;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACO,SAAS/I,+BAA+BA,CAACwO,KAAK,EAAiB;EAAA,IAAfzD,QAAQ,GAAA3C,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAClE,IAAIhI,MAAM,CAAC8N,cAAc,CAACM,KAAK,CAAC,KAAKpO,MAAM,CAACC,SAAS,EAAE;IACrD;IACAD,MAAM,CAACuD,IAAI,CAAC6K,KAAK,CAAC,CAAC9E,OAAO,CAAC,UAAA7F,GAAG,EAAI;MAChC,IAAMvC,KAAK,GAAGkN,KAAK,CAAC3K,GAAG,CAAC;MAExB,IAAIA,GAAG,KAAK,MAAM,EAAE;QAClB;QACAvC,KAAK,CAACoI,OAAO,CAAC,UAAAX,OAAO;UAAA,OACnB/I,+BAA+B,CAAC+I,OAAO,EAAEgC,QAAQ,CAAC;QAAA,CACpD,CAAC;MACH,CAAC,MAAM,IAAIlH,GAAG,KAAK,KAAK,EAAE;QACxB;QACA,IAAIvC,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;UACtBlB,+BAA+B,CAACsB,KAAK,CAAC,CAAC,CAAC,EAAEyJ,QAAQ,CAAC;QACrD;MACF,CAAC,MAAM,IAAIlH,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACzB;QACAoK,4BAA4B,CAAClD,QAAQ,EAAElH,GAAG,EAAEvC,KAAK,CAAC;MACpD;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA,IAAInB,eAAe,CAACsO,aAAa,CAACD,KAAK,CAAC,EAAE;MACxC1D,kBAAkB,CAACC,QAAQ,EAAE,KAAK,EAAEyD,KAAK,CAAC;IAC5C;EACF;EAEA,OAAOzD,QAAQ;AACjB;AAQO,SAAS9K,iBAAiBA,CAACyO,MAAM,EAAE;EACxC;EACA;EACA;EACA,IAAIC,UAAU,GAAGvO,MAAM,CAACuD,IAAI,CAAC+K,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC;;EAE3C;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,EAAED,UAAU,CAACzN,MAAM,KAAK,CAAC,IAAIyN,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,IACrD,EAAEA,UAAU,CAACvB,QAAQ,CAAC,KAAK,CAAC,IAAIsB,MAAM,CAACG,GAAG,CAAC,EAAE;IAC/CF,UAAU,GAAGA,UAAU,CAAC/K,MAAM,CAAC,UAAAC,GAAG;MAAA,OAAIA,GAAG,KAAK,KAAK;IAAA,EAAC;EACtD;EAEA,IAAIiL,SAAS,GAAG,IAAI,CAAC,CAAC;;EAEtBH,UAAU,CAACjF,OAAO,CAAC,UAAAqF,OAAO,EAAI;IAC5B,IAAMC,IAAI,GAAG,CAAC,CAACN,MAAM,CAACK,OAAO,CAAC;IAE9B,IAAID,SAAS,KAAK,IAAI,EAAE;MACtBA,SAAS,GAAGE,IAAI;IAClB;;IAEA;IACA,IAAIF,SAAS,KAAKE,IAAI,EAAE;MACtB,MAAMpC,cAAc,CAClB,0DACF,CAAC;IACH;EACF,CAAC,CAAC;EAEF,IAAMqC,mBAAmB,GAAGlP,WAAW,CACrC4O,UAAU,EACV,UAAAhB,IAAI;IAAA,OAAImB,SAAS;EAAA,GACjB,UAACI,IAAI,EAAEvB,IAAI,EAAEwB,QAAQ,EAAK;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAMC,WAAW,GAAGD,QAAQ;IAC5B,IAAME,WAAW,GAAG1B,IAAI;IACxB,MAAMf,cAAc,CAClB,UAAQwC,WAAW,aAAQC,WAAW,iCACtC,sEAAsE,GACtE,uBACF,CAAC;EACH,CAAC,CAAC;EAEJ,OAAO;IAACP,SAAS,EAATA,SAAS;IAAEjB,IAAI,EAAEoB;EAAmB,CAAC;AAC/C;AAGO,SAAS/O,oBAAoBA,CAACkD,MAAM,EAAE;EAC3C,OAAO,UAAA9B,KAAK,EAAI;IACd,IAAIA,KAAK,YAAYK,MAAM,EAAE;MAC3B,OAAOL,KAAK,CAACgO,QAAQ,CAAC,CAAC,KAAKlM,MAAM,CAACkM,QAAQ,CAAC,CAAC;IAC/C;;IAEA;IACA,IAAI,OAAOhO,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,KAAK;IACd;;IAEA;IACA;IACA;IACA;IACA;IACA8B,MAAM,CAACmM,SAAS,GAAG,CAAC;IAEpB,OAAOnM,MAAM,CAACE,IAAI,CAAChC,KAAK,CAAC;EAC3B,CAAC;AACH;AAEA;AACA;AACA;AACA,SAASkO,iBAAiBA,CAAC3L,GAAG,EAAE8J,IAAI,EAAE;EACpC,IAAI9J,GAAG,CAACuJ,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrB,MAAM,IAAIjM,KAAK,wBACQ0C,GAAG,cAAS8J,IAAI,SAAI9J,GAAG,+BAC9C,CAAC;EACH;EAEA,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClB,MAAM,IAAI1C,KAAK,sCACsBwM,IAAI,SAAI9J,GAAG,+BAChD,CAAC;EACH;AACF;;AAEA;AACA,SAASyK,cAAcA,CAACC,MAAM,EAAEZ,IAAI,EAAE;EACpC,IAAIY,MAAM,IAAInO,MAAM,CAAC8N,cAAc,CAACK,MAAM,CAAC,KAAKnO,MAAM,CAACC,SAAS,EAAE;IAChED,MAAM,CAACuD,IAAI,CAAC4K,MAAM,CAAC,CAAC7E,OAAO,CAAC,UAAA7F,GAAG,EAAI;MACjC2L,iBAAiB,CAAC3L,GAAG,EAAE8J,IAAI,CAAC;MAC5BW,cAAc,CAACC,MAAM,CAAC1K,GAAG,CAAC,EAAE8J,IAAI,GAAG,GAAG,GAAG9J,GAAG,CAAC;IAC/C,CAAC,CAAC;EACJ;AACF,C;;;;;;;;;;;AC/3CA/E,MAAM,CAACM,MAAM,CAAC;EAACqQ,kBAAkB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,kBAAkB;EAAA,CAAC;EAACC,wBAAwB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,wBAAwB;EAAA,CAAC;EAACC,oBAAoB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,oBAAoB;EAAA,CAAC;EAACC,mBAAmB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,mBAAmB;EAAA;AAAC,CAAC,CAAC;AAG/P,SAASH,kBAAkBA,CAACI,MAAM,EAAE;EACzC,OAAUA,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;AACnC;AAEO,IAAMJ,wBAAwB,GAAG,CACtC,yBAAyB,EACzB,gBAAgB,EAChB,WAAW;AACX;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,aAAa;AACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,SAAS;AACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ;AACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,QAAQ,CACT;AAEM,IAAMC,oBAAoB,GAAG;AAClC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,OAAO;AACP;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,OAAO;AACP;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,SAAS;AACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,KAAK,CACN;AAEM,IAAMC,mBAAmB,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,C;;;;;;;;;;;ACpJtF,IAAIG,+BAA+B;AAACjR,MAAM,CAACC,IAAI,CAAC,uDAAuD,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAAC6Q,+BAA+B,GAAC7Q,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI8Q,mBAAmB;AAAClR,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAAC8Q,mBAAmB,GAAC9Q,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAApRJ,MAAM,CAACM,MAAM,CAAC;EAAC,WAAQ,SAAAH,CAAA,EAAU;IAAC,OAAOgR,MAAM;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI9P,eAAe;AAACrB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAACiB,eAAe,GAACjB,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIG,MAAM;AAACP,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACM,MAAM,EAAC,SAAAA,CAASH,CAAC,EAAC;IAACG,MAAM,GAACH,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIyQ,oBAAoB,EAACF,kBAAkB;AAAC3Q,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAAC4Q,oBAAoB,EAAC,SAAAA,CAASzQ,CAAC,EAAC;IAACyQ,oBAAoB,GAACzQ,CAAC;EAAA,CAAC;EAACuQ,kBAAkB,EAAC,SAAAA,CAASvQ,CAAC,EAAC;IAACuQ,kBAAkB,GAACvQ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAMlY+Q,MAAM;EACzB;EACA,SAAAA,OAAYC,UAAU,EAAEpG,QAAQ,EAAgB;IAAA,IAAd3B,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IAC5C,IAAI,CAAC8H,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACrO,OAAO,GAAG,IAAIsO,SAAS,CAACC,OAAO,CAACvG,QAAQ,CAAC;IAE9C,IAAI3J,eAAe,CAACmQ,4BAA4B,CAACxG,QAAQ,CAAC,EAAE;MAC1D;MACA,IAAI,CAACyG,WAAW,GAAGlR,MAAM,CAAC+C,IAAI,CAAC0H,QAAQ,EAAE,KAAK,CAAC,GAAGA,QAAQ,CAAC+E,GAAG,GAAG/E,QAAQ;IAC3E,CAAC,MAAM;MACL,IAAI,CAACyG,WAAW,GAAG3O,SAAS;MAE5B,IAAI,IAAI,CAACE,OAAO,CAAC0O,WAAW,CAAC,CAAC,IAAIrI,OAAO,CAACyG,IAAI,EAAE;QAC9C,IAAI,CAACuB,MAAM,GAAG,IAAIC,SAAS,CAACK,MAAM,CAACtI,OAAO,CAACyG,IAAI,IAAI,EAAE,CAAC;MACxD;IACF;IAEA,IAAI,CAAC8B,IAAI,GAAGvI,OAAO,CAACuI,IAAI,IAAI,CAAC;IAC7B,IAAI,CAACC,KAAK,GAAGxI,OAAO,CAACwI,KAAK;IAC1B,IAAI,CAACjC,MAAM,GAAGvG,OAAO,CAACyI,UAAU,IAAIzI,OAAO,CAACuG,MAAM;IAElD,IAAI,CAACmC,aAAa,GAAG1Q,eAAe,CAAC2Q,kBAAkB,CAAC,IAAI,CAACpC,MAAM,IAAI,CAAC,CAAC,CAAC;IAE1E,IAAI,CAACqC,UAAU,GAAG5Q,eAAe,CAAC6Q,aAAa,CAAC7I,OAAO,CAAC8I,SAAS,CAAC;;IAElE;IACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;MAClC,IAAI,CAACC,QAAQ,GAAGhJ,OAAO,CAACgJ,QAAQ,KAAKvP,SAAS,GAAG,IAAI,GAAGuG,OAAO,CAACgJ,QAAQ;IAC1E;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE,IAAAC,MAAA,GAAAnB,MAAA,CAAA5P,SAAA;EAAA+Q,MAAA,CAYAC,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAACF,QAAQ,EAAE;QACjB;QACA,IAAI,CAACG,OAAO,CAAC;UAAEC,KAAK,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAK,CAAC,EAAE,IAAI,CAAC;MACpD;MAEA,OAAO,IAAI,CAACC,cAAc,CAAC;QACzBC,OAAO,EAAE;MACX,CAAC,CAAC,CAACxQ,MAAM;IACX;IAAC,OATDmQ,KAAK;EAAA;EAWL;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;EAAAD,MAAA,CAQAO,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAMjN,MAAM,GAAG,EAAE;MAEjB,IAAI,CAACgF,OAAO,CAAC,UAAAzE,GAAG,EAAI;QAClBP,MAAM,CAACkF,IAAI,CAAC3E,GAAG,CAAC;MAClB,CAAC,CAAC;MAEF,OAAOP,MAAM;IACf;IAAC,OARDiN,KAAK;EAAA;EAAAP,MAAA,CAUJQ,MAAM,CAACC,QAAQ,IAAhB,YAAoB;IAAA,IAAAC,KAAA;IAClB,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACG,OAAO,CAAC;QACXS,WAAW,EAAE,IAAI;QACjBP,OAAO,EAAE,IAAI;QACbQ,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;IAEA,IAAIC,KAAK,GAAG,CAAC;IACb,IAAMC,OAAO,GAAG,IAAI,CAACV,cAAc,CAAC;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAEtD,OAAO;MACLU,IAAI,EAAE,SAAAA,CAAA,EAAM;QACV,IAAIF,KAAK,GAAGC,OAAO,CAACjR,MAAM,EAAE;UAC1B;UACA,IAAI6H,OAAO,GAAG+I,KAAI,CAACjB,aAAa,CAACsB,OAAO,CAACD,KAAK,EAAE,CAAC,CAAC;UAElD,IAAIJ,KAAI,CAACf,UAAU,EAAEhI,OAAO,GAAG+I,KAAI,CAACf,UAAU,CAAChI,OAAO,CAAC;UAEvD,OAAO;YAAEzH,KAAK,EAAEyH;UAAQ,CAAC;QAC3B;QAEA,OAAO;UAAEsJ,IAAI,EAAE;QAAK,CAAC;MACvB;IACF,CAAC;EACH,CAAC;EAAAjB,MAAA,CAEAQ,MAAM,CAACU,aAAa,IAArB,YAAyB;IACvB,IAAMC,UAAU,GAAG,IAAI,CAACX,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC1C,OAAO;MACCO,IAAI;QAAA,SAAAI,QAAA;UAAA,OAAAxC,mBAAA,CAAAyC,KAAA;YAAA,SAAAC,SAAAC,QAAA;cAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAP,IAAA;gBAAA;kBAAA,OAAAO,QAAA,CAAAE,MAAA,WACDC,OAAO,CAACC,OAAO,CAACR,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC;gBAAA;gBAAA;kBAAA,OAAAO,QAAA,CAAAK,IAAA;cAAA;YAAA;YAAA,OAAAN,QAAA;UAAA,uBAAAI,OAAA;QAAA;QAAA,OAAAN,OAAA;MAAA;IAE7C,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAbE;EAAApB,MAAA,CAcA1H,OAAO;IAAP,SAAAA,OAAOA,CAACuJ,QAAQ,EAAEC,OAAO,EAAE;MAAA,IAAAC,MAAA;MACzB,IAAI,IAAI,CAAChC,QAAQ,EAAE;QACjB,IAAI,CAACG,OAAO,CAAC;UACXS,WAAW,EAAE,IAAI;UACjBP,OAAO,EAAE,IAAI;UACbQ,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE;QACf,CAAC,CAAC;MACJ;MAEA,IAAI,CAACR,cAAc,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC,CAAChI,OAAO,CAAC,UAACX,OAAO,EAAEjG,CAAC,EAAK;QAC7D;QACAiG,OAAO,GAAGoK,MAAI,CAACtC,aAAa,CAAC9H,OAAO,CAAC;QAErC,IAAIoK,MAAI,CAACpC,UAAU,EAAE;UACnBhI,OAAO,GAAGoK,MAAI,CAACpC,UAAU,CAAChI,OAAO,CAAC;QACpC;QAEAkK,QAAQ,CAAC7Q,IAAI,CAAC8Q,OAAO,EAAEnK,OAAO,EAAEjG,CAAC,EAAEqQ,MAAI,CAAC;MAC1C,CAAC,CAAC;IACJ;IAAC,OApBDzJ,OAAO;EAAA;EAAA0H,MAAA,CAsBPgC,YAAY;IAAZ,SAAAA,YAAYA,CAAA,EAAG;MACb,OAAO,IAAI,CAACrC,UAAU;IACxB;IAAC,OAFDqC,YAAY;EAAA;EAIZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAZE;EAAAhC,MAAA,CAaA3P,GAAG;IAAH,SAAAA,GAAGA,CAACwR,QAAQ,EAAEC,OAAO,EAAE;MAAA,IAAAG,MAAA;MACrB,IAAM3O,MAAM,GAAG,EAAE;MAEjB,IAAI,CAACgF,OAAO,CAAC,UAACzE,GAAG,EAAEnC,CAAC,EAAK;QACvB4B,MAAM,CAACkF,IAAI,CAACqJ,QAAQ,CAAC7Q,IAAI,CAAC8Q,OAAO,EAAEjO,GAAG,EAAEnC,CAAC,EAAEuQ,MAAI,CAAC,CAAC;MACnD,CAAC,CAAC;MAEF,OAAO3O,MAAM;IACf;IAAC,OARDjD,GAAG;EAAA,IAUH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;EAAA2P,MAAA,CAQAkC,OAAO;IAAP,SAAAA,OAAOA,CAACnL,OAAO,EAAE;MACf,OAAOhI,eAAe,CAACoT,0BAA0B,CAAC,IAAI,EAAEpL,OAAO,CAAC;IAClE;IAAC,OAFDmL,OAAO;EAAA;EAIP;AACF;AACA;AACA;AACA;AACA;EALE;EAAAlC,MAAA,CAMAoC,YAAY;IAAZ,SAAAA,YAAYA,CAACrL,OAAO,EAAE;MAAA,IAAAsL,MAAA;MACpB,OAAO,IAAIX,OAAO,CAAC,UAAAC,OAAO;QAAA,OAAIA,OAAO,CAACU,MAAI,CAACH,OAAO,CAACnL,OAAO,CAAC,CAAC;MAAA,EAAC;IAC/D;IAAC,OAFDqL,YAAY;EAAA;EAIZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE;EAAApC,MAAA,CAUAsC,cAAc;IAAd,SAAAA,cAAcA,CAACvL,OAAO,EAAE;MAAA,IAAAwL,MAAA;MACtB,IAAMjC,OAAO,GAAGvR,eAAe,CAACyT,kCAAkC,CAACzL,OAAO,CAAC;;MAE3E;MACA;MACA;MACA;MACA,IAAI,CAACA,OAAO,CAAC0L,gBAAgB,IAAI,CAACnC,OAAO,KAAK,IAAI,CAAChB,IAAI,IAAI,IAAI,CAACC,KAAK,CAAC,EAAE;QACtE,MAAM,IAAIxP,KAAK,CACb,qEAAqE,GACnE,mEACJ,CAAC;MACH;MAEA,IAAI,IAAI,CAACuN,MAAM,KAAK,IAAI,CAACA,MAAM,CAACG,GAAG,KAAK,CAAC,IAAI,IAAI,CAACH,MAAM,CAACG,GAAG,KAAK,KAAK,CAAC,EAAE;QACvE,MAAM1N,KAAK,CAAC,sDAAsD,CAAC;MACrE;MAEA,IAAM2S,SAAS,GACb,IAAI,CAAChS,OAAO,CAAC0O,WAAW,CAAC,CAAC,IAAIkB,OAAO,IAAI,IAAIvR,eAAe,CAAC4T,MAAM,CAAC,CAAC;MAEvE,IAAMvF,KAAK,GAAG;QACZwF,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,KAAK;QACZH,SAAS,EAATA,SAAS;QACThS,OAAO,EAAE,IAAI,CAACA,OAAO;QAAE;QACvB4P,OAAO,EAAPA,OAAO;QACPwC,YAAY,EAAE,IAAI,CAACrD,aAAa;QAChCsD,eAAe,EAAE,IAAI;QACrBhE,MAAM,EAAEuB,OAAO,IAAI,IAAI,CAACvB;MAC1B,CAAC;MAED,IAAIiE,GAAG;;MAEP;MACA;MACA,IAAI,IAAI,CAACjD,QAAQ,EAAE;QACjBiD,GAAG,GAAG,IAAI,CAAClE,UAAU,CAACmE,QAAQ,EAAE;QAChC,IAAI,CAACnE,UAAU,CAACoE,OAAO,CAACF,GAAG,CAAC,GAAG5F,KAAK;MACtC;MAEAA,KAAK,CAAC+F,OAAO,GAAG,IAAI,CAAC9C,cAAc,CAAC;QAClCC,OAAO,EAAPA,OAAO;QACPoC,SAAS,EAAEtF,KAAK,CAACsF;MACnB,CAAC,CAAC;MAEF,IAAI,IAAI,CAAC5D,UAAU,CAACsE,MAAM,EAAE;QAC1BhG,KAAK,CAAC2F,eAAe,GAAGzC,OAAO,GAAG,EAAE,GAAG,IAAIvR,eAAe,CAAC4T,MAAM,CAAC,CAAC;MACrE;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA,IAAMU,YAAY,GAAG,SAAAA,CAACvP,EAAE,EAAK;QAC3B,IAAI,CAACA,EAAE,EAAE;UACP,OAAO,YAAM,CAAC,CAAC;QACjB;QAEA,IAAMwP,IAAI,GAAGf,MAAI;QAEjB,OAAO,SAAU;QAAA,GAAW;UAAA,IAAAgB,MAAA;UAC1B,IAAID,IAAI,CAACxE,UAAU,CAACsE,MAAM,EAAE;YAC1B;UACF;UAEA,IAAMI,IAAI,GAAGxM,SAAS;UAEtBsM,IAAI,CAACxE,UAAU,CAAC2E,aAAa,CAACC,SAAS,CAAC,YAAM;YAC5C5P,EAAE,CAACuF,KAAK,CAACkK,MAAI,EAAEC,IAAI,CAAC;UACtB,CAAC,CAAC;QACJ,CAAC;MACH,CAAC;MAEDpG,KAAK,CAAC+C,KAAK,GAAGkD,YAAY,CAACtM,OAAO,CAACoJ,KAAK,CAAC;MACzC/C,KAAK,CAACwD,OAAO,GAAGyC,YAAY,CAACtM,OAAO,CAAC6J,OAAO,CAAC;MAC7CxD,KAAK,CAACgD,OAAO,GAAGiD,YAAY,CAACtM,OAAO,CAACqJ,OAAO,CAAC;MAE7C,IAAIE,OAAO,EAAE;QACXlD,KAAK,CAACuD,WAAW,GAAG0C,YAAY,CAACtM,OAAO,CAAC4J,WAAW,CAAC;QACrDvD,KAAK,CAACyD,WAAW,GAAGwC,YAAY,CAACtM,OAAO,CAAC8J,WAAW,CAAC;MACvD;MAEA,IAAI,CAAC9J,OAAO,CAAC4M,iBAAiB,IAAI,CAAC,IAAI,CAAC7E,UAAU,CAACsE,MAAM,EAAE;QAAA,IAAAQ,cAAA,EAAAC,mBAAA;QACzD,IAAMC,OAAO,GAAG,SAAAA,CAACjQ,GAAG,EAAK;UACvB,IAAMyJ,MAAM,GAAGtE,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;UAE/B,OAAOyJ,MAAM,CAACG,GAAG;UAEjB,IAAI6C,OAAO,EAAE;YACXlD,KAAK,CAACuD,WAAW,CAAC9M,GAAG,CAAC4J,GAAG,EAAE8E,MAAI,CAAC9C,aAAa,CAACnC,MAAM,CAAC,EAAE,IAAI,CAAC;UAC9D;UAEAF,KAAK,CAAC+C,KAAK,CAACtM,GAAG,CAAC4J,GAAG,EAAE8E,MAAI,CAAC9C,aAAa,CAACnC,MAAM,CAAC,CAAC;QAClD,CAAC;QACD;QACA,IAAIF,KAAK,CAAC+F,OAAO,CAACrT,MAAM,EAAE;UACxB,SAAAkU,SAAA,GAAArF,+BAAA,CAAkBvB,KAAK,CAAC+F,OAAO,GAAAc,KAAA,IAAAA,KAAA,GAAAD,SAAA,IAAA/C,IAAA,GAAE;YAAA,IAAtBpN,GAAG,GAAAoQ,KAAA,CAAA/T,KAAA;YACZ4T,OAAO,CAACjQ,GAAG,CAAC;UACd;QACF;QACA;QACA,KAAA+P,cAAA,GAAIxG,KAAK,CAAC+F,OAAO,cAAAS,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAeM,IAAI,cAAAL,mBAAA,eAAnBA,mBAAA,CAAA7S,IAAA,CAAA4S,cAAsB,CAAC,EAAE;UAC3BxG,KAAK,CAAC+F,OAAO,CAAC7K,OAAO,CAACwL,OAAO,CAAC;QAChC;MACF;MAEA,IAAMK,MAAM,GAAGnV,MAAM,CAAC+D,MAAM,CAAC,IAAIhE,eAAe,CAACqV,aAAa,CAAC,CAAC,EAAE;QAChEtF,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B8C,IAAI,EAAE,SAAAA,CAAA,EAAM;UACV,IAAIW,MAAI,CAACxC,QAAQ,EAAE;YACjB,OAAOwC,MAAI,CAACzD,UAAU,CAACoE,OAAO,CAACF,GAAG,CAAC;UACrC;QACF,CAAC;QACDqB,OAAO,EAAE,KAAK;QACdC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAI,IAAI,CAACvE,QAAQ,IAAID,OAAO,CAACyE,MAAM,EAAE;QACnC;QACA;QACA;QACA;QACA;QACAzE,OAAO,CAAC0E,YAAY,CAAC,YAAM;UACzBL,MAAM,CAACvC,IAAI,CAAC,CAAC;QACf,CAAC,CAAC;MACJ;;MAEA;MACA;MACA,IAAM6C,WAAW,GAAG,IAAI,CAAC3F,UAAU,CAAC2E,aAAa,CAACiB,KAAK,CAAC,CAAC;MAEzD,IAAID,WAAW,YAAY/C,OAAO,EAAE;QAClCyC,MAAM,CAACG,cAAc,GAAGG,WAAW;QACnCA,WAAW,CAACE,IAAI,CAAC;UAAA,OAAOR,MAAM,CAACE,OAAO,GAAG,IAAI;QAAA,CAAC,CAAC;MACjD,CAAC,MAAM;QACLF,MAAM,CAACE,OAAO,GAAG,IAAI;QACrBF,MAAM,CAACG,cAAc,GAAG5C,OAAO,CAACC,OAAO,CAAC,CAAC;MAC3C;MAEA,OAAOwC,MAAM;IACf;IAAC,OAjJD7B,cAAc;EAAA;EAmJd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE;EAAAtC,MAAA,CAUA4E,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAAC7N,OAAO,EAAE;MAAA,IAAA8N,MAAA;MAC3B,OAAO,IAAInD,OAAO,CAAC,UAACC,OAAO,EAAK;QAC9B,IAAMwC,MAAM,GAAGU,MAAI,CAACvC,cAAc,CAACvL,OAAO,CAAC;QAC3CoN,MAAM,CAACG,cAAc,CAACK,IAAI,CAAC;UAAA,OAAMhD,OAAO,CAACwC,MAAM,CAAC;QAAA,EAAC;MACnD,CAAC,CAAC;IACJ;IAAC,OALDS,mBAAmB;EAAA,IAOnB;EACA;EAAA;EAAA5E,MAAA,CACAE,OAAO;IAAP,SAAAA,OAAOA,CAAC4E,QAAQ,EAAErC,gBAAgB,EAAE;MAClC,IAAI3C,OAAO,CAACyE,MAAM,EAAE;QAClB,IAAMQ,UAAU,GAAG,IAAIjF,OAAO,CAACkF,UAAU,CAAC,CAAC;QAC3C,IAAMC,MAAM,GAAGF,UAAU,CAACnE,OAAO,CAACsE,IAAI,CAACH,UAAU,CAAC;QAElDA,UAAU,CAACI,MAAM,CAAC,CAAC;QAEnB,IAAMpO,OAAO,GAAG;UAAE0L,gBAAgB,EAAhBA,gBAAgB;UAAEkB,iBAAiB,EAAE;QAAK,CAAC;QAE7D,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,CAAC,CAACrL,OAAO,CACnE,UAAAxE,EAAE,EAAI;UACJ,IAAIgR,QAAQ,CAAChR,EAAE,CAAC,EAAE;YAChBiD,OAAO,CAACjD,EAAE,CAAC,GAAGmR,MAAM;UACtB;QACF,CACF,CAAC;;QAED;QACA,IAAI,CAAC3C,cAAc,CAACvL,OAAO,CAAC;MAC9B;IACF;IAAC,OApBDmJ,OAAO;EAAA;EAAAF,MAAA,CAsBPoF,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACtG,UAAU,CAAClD,IAAI;IAC7B;IAAC,OAFDwJ,kBAAkB;EAAA,IAIlB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAApF,MAAA,CACAK,cAAc;IAAd,SAAAA,cAAcA,CAAA,EAAe;MAAA,IAAAgF,MAAA;MAAA,IAAdtO,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;MACzB;MACA;MACA;MACA;MACA,IAAMsO,cAAc,GAAGvO,OAAO,CAACuO,cAAc,KAAK,KAAK;;MAEvD;MACA;MACA,IAAMnC,OAAO,GAAGpM,OAAO,CAACuJ,OAAO,GAAG,EAAE,GAAG,IAAIvR,eAAe,CAAC4T,MAAM,CAAC,CAAC;;MAEnE;MACA,IAAI,IAAI,CAACxD,WAAW,KAAK3O,SAAS,EAAE;QAClC;QACA;QACA,IAAI8U,cAAc,IAAI,IAAI,CAAChG,IAAI,EAAE;UAC/B,OAAO6D,OAAO;QAChB;QAEA,IAAMoC,WAAW,GAAG,IAAI,CAACzG,UAAU,CAAC0G,KAAK,CAACC,GAAG,CAAC,IAAI,CAACtG,WAAW,CAAC;QAC/D,IAAIoG,WAAW,EAAE;UACf,IAAIxO,OAAO,CAACuJ,OAAO,EAAE;YACnB6C,OAAO,CAAC3K,IAAI,CAAC+M,WAAW,CAAC;UAC3B,CAAC,MAAM;YACLpC,OAAO,CAACuC,GAAG,CAAC,IAAI,CAACvG,WAAW,EAAEoG,WAAW,CAAC;UAC5C;QACF;QACA,OAAOpC,OAAO;MAChB;;MAEA;;MAEA;MACA;MACA;MACA,IAAIT,SAAS;MACb,IAAI,IAAI,CAAChS,OAAO,CAAC0O,WAAW,CAAC,CAAC,IAAIrI,OAAO,CAACuJ,OAAO,EAAE;QACjD,IAAIvJ,OAAO,CAAC2L,SAAS,EAAE;UACrBA,SAAS,GAAG3L,OAAO,CAAC2L,SAAS;UAC7BA,SAAS,CAACiD,KAAK,CAAC,CAAC;QACnB,CAAC,MAAM;UACLjD,SAAS,GAAG,IAAI3T,eAAe,CAAC4T,MAAM,CAAC,CAAC;QAC1C;MACF;MAEAiD,MAAM,CAACC,SAAS,CAAC,YAAM;QACrBR,MAAI,CAACvG,UAAU,CAAC0G,KAAK,CAAClN,OAAO,CAAC,UAACzE,GAAG,EAAEiS,EAAE,EAAK;UACzC,IAAMC,WAAW,GAAGV,MAAI,CAAC3U,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC;UACrD,IAAIkS,WAAW,CAACzS,MAAM,EAAE;YACtB,IAAIyD,OAAO,CAACuJ,OAAO,EAAE;cACnB6C,OAAO,CAAC3K,IAAI,CAAC3E,GAAG,CAAC;cAEjB,IAAI6O,SAAS,IAAIqD,WAAW,CAACrQ,QAAQ,KAAKlF,SAAS,EAAE;gBACnDkS,SAAS,CAACgD,GAAG,CAACI,EAAE,EAAEC,WAAW,CAACrQ,QAAQ,CAAC;cACzC;YACF,CAAC,MAAM;cACLyN,OAAO,CAACuC,GAAG,CAACI,EAAE,EAAEjS,GAAG,CAAC;YACtB;UACF;;UAEA;UACA,IAAI,CAACyR,cAAc,EAAE;YACnB,OAAO,IAAI;UACb;;UAEA;UACA;UACA,OACE,CAACD,MAAI,CAAC9F,KAAK,IAAI8F,MAAI,CAAC/F,IAAI,IAAI+F,MAAI,CAACtG,MAAM,IAAIoE,OAAO,CAACrT,MAAM,KAAKuV,MAAI,CAAC9F,KAAK;QAE5E,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAACxI,OAAO,CAACuJ,OAAO,EAAE;QACpB,OAAO6C,OAAO;MAChB;MAEA,IAAI,IAAI,CAACpE,MAAM,EAAE;QACfoE,OAAO,CAAC3F,IAAI,CAAC,IAAI,CAACuB,MAAM,CAACkH,aAAa,CAAC;UAAEvD,SAAS,EAATA;QAAU,CAAC,CAAC,CAAC;MACxD;;MAEA;MACA;MACA,IAAI,CAAC4C,cAAc,IAAK,CAAC,IAAI,CAAC/F,KAAK,IAAI,CAAC,IAAI,CAACD,IAAK,EAAE;QAClD,OAAO6D,OAAO;MAChB;MAEA,OAAOA,OAAO,CAACpI,KAAK,CAClB,IAAI,CAACuE,IAAI,EACT,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACD,IAAI,GAAG6D,OAAO,CAACrT,MAChD,CAAC;IACH;IAAC,OA3FDuQ,cAAc;EAAA;EAAAL,MAAA,CA6FdkG,cAAc;IAAd,SAAAA,cAAcA,CAACC,YAAY,EAAE;MAC3B;MACA,IAAI,CAACC,OAAO,CAACC,KAAK,EAAE;QAClB,MAAM,IAAItW,KAAK,CACb,2DACF,CAAC;MACH;MAEA,IAAI,CAAC,IAAI,CAAC+O,UAAU,CAAClD,IAAI,EAAE;QACzB,MAAM,IAAI7L,KAAK,CACb,0DACF,CAAC;MACH;MAEA,OAAOqW,OAAO,CAACC,KAAK,CAACC,KAAK,CAACC,UAAU,CAACL,cAAc,CAClD,IAAI,EACJC,YAAY,EACZ,IAAI,CAACrH,UAAU,CAAClD,IAClB,CAAC;IACH;IAAC,OAnBDsK,cAAc;EAAA;EAAA,OAAArH,MAAA;AAAA;AAsBhB;AACAN,oBAAoB,CAACjG,OAAO,CAAC,UAAAmG,MAAM,EAAI;EACrC,IAAM+H,SAAS,GAAGnI,kBAAkB,CAACI,MAAM,CAAC;EAC5CI,MAAM,CAAC5P,SAAS,CAACuX,SAAS,CAAC,GAAG,YAAkB;IAC9C,IAAI;MAAA,SAAAC,IAAA,GAAAzP,SAAA,CAAAlH,MAAA,EADoC0T,IAAI,OAAA5T,KAAA,CAAA6W,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;QAAJlD,IAAI,CAAAkD,IAAA,IAAA1P,SAAA,CAAA0P,IAAA;MAAA;MAE1C,OAAOhF,OAAO,CAACC,OAAO,CAAC,IAAI,CAAClD,MAAM,CAAC,CAACpF,KAAK,CAAC,IAAI,EAAEmK,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC,OAAO7H,KAAK,EAAE;MACd,OAAO+F,OAAO,CAACiF,MAAM,CAAChL,KAAK,CAAC;IAC9B;EACF,CAAC;AACH,CAAC,CAAC,C;;;;;;;;;;;AC5jBF,IAAI/N,kBAAkB;AAACF,MAAM,CAACC,IAAI,CAAC,0CAA0C,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACF,kBAAkB,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,OAAO;AAACL,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,OAAO,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI8Y,cAAc;AAAClZ,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAAC8Y,cAAc,GAAC9Y,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI+Y,aAAa;AAACnZ,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAAC+Y,aAAa,GAAC/Y,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI6Q,+BAA+B;AAACjR,MAAM,CAACC,IAAI,CAAC,uDAAuD,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAAC6Q,+BAA+B,GAAC7Q,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIgZ,cAAc;AAACpZ,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACgZ,cAAc,GAAChZ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI8Q,mBAAmB;AAAClR,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAAC8Q,mBAAmB,GAAC9Q,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA9zBJ,MAAM,CAACM,MAAM,CAAC;EAAC,WAAQ,SAAAH,CAAA,EAAU;IAAC,OAAOkB,eAAe;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI8P,MAAM;AAACnR,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAAC+Q,MAAM,GAAC/Q,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsW,aAAa;AAAC1W,MAAM,CAACC,IAAI,CAAC,qBAAqB,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAACsW,aAAa,GAACtW,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIG,MAAM,EAACK,WAAW,EAACC,YAAY,EAACC,gBAAgB,EAACI,+BAA+B,EAACC,iBAAiB;AAACnB,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACM,MAAM,EAAC,SAAAA,CAASH,CAAC,EAAC;IAACG,MAAM,GAACH,CAAC;EAAA,CAAC;EAACQ,WAAW,EAAC,SAAAA,CAASR,CAAC,EAAC;IAACQ,WAAW,GAACR,CAAC;EAAA,CAAC;EAACS,YAAY,EAAC,SAAAA,CAAST,CAAC,EAAC;IAACS,YAAY,GAACT,CAAC;EAAA,CAAC;EAACU,gBAAgB,EAAC,SAAAA,CAASV,CAAC,EAAC;IAACU,gBAAgB,GAACV,CAAC;EAAA,CAAC;EAACc,+BAA+B,EAAC,SAAAA,CAASd,CAAC,EAAC;IAACc,+BAA+B,GAACd,CAAC;EAAA,CAAC;EAACe,iBAAiB,EAAC,SAAAA,CAASf,CAAC,EAAC;IAACe,iBAAiB,GAACf,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIuQ,kBAAkB;AAAC3Q,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAAC0Q,kBAAkB,EAAC,SAAAA,CAASvQ,CAAC,EAAC;IAACuQ,kBAAkB,GAACvQ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAgB9tBiB,eAAe;EAClC,SAAAA,gBAAY6M,IAAI,EAAE;IAChB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC4J,KAAK,GAAG,IAAIzW,eAAe,CAAC4T,MAAM,CAAD,CAAC;IAEvC,IAAI,CAACc,aAAa,GAAGmC,MAAM,CAACmB,QAAQ,GAChC,IAAInB,MAAM,CAACoB,iBAAiB,CAAC,CAAC,GAC9B,IAAIpB,MAAM,CAACqB,kBAAkB,CAAC,CAAC;IAEnC,IAAI,CAAChE,QAAQ,GAAG,CAAC,CAAC,CAAC;;IAEnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,GAAGlU,MAAM,CAACkY,MAAM,CAAC,IAAI,CAAC;;IAElC;IACA;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;;IAE3B;IACA,IAAI,CAAC/D,MAAM,GAAG,KAAK;EACrB;EAAC,IAAApD,MAAA,GAAAjR,eAAA,CAAAE,SAAA;EAAA+Q,MAAA,CAEDoH,cAAc;IAAd,SAAAA,cAAcA,CAAC1O,QAAQ,EAAE3B,OAAO,EAAE;MAChC,OAAO,IAAI,CAACsQ,IAAI,CAAC3O,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,CAAC,CAAC,EAAE3B,OAAO,CAAC,CAACuQ,UAAU,CAAC,CAAC;IACxD;IAAC,OAFDF,cAAc;EAAA;EAAApH,MAAA,CAIduH,sBAAsB;IAAtB,SAAAA,sBAAsBA,CAACxQ,OAAO,EAAE;MAC9B,OAAO,IAAI,CAACsQ,IAAI,CAAC,CAAC,CAAC,EAAEtQ,OAAO,CAAC,CAACuQ,UAAU,CAAC,CAAC;IAC5C;IAAC,OAFDC,sBAAsB;EAAA,IAItB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAAvH,MAAA,CACAqH,IAAI;IAAJ,SAAAA,IAAIA,CAAC3O,QAAQ,EAAE3B,OAAO,EAAE;MACtB;MACA;MACA;MACA,IAAIC,SAAS,CAAClH,MAAM,KAAK,CAAC,EAAE;QAC1B4I,QAAQ,GAAG,CAAC,CAAC;MACf;MAEA,OAAO,IAAI3J,eAAe,CAAC8P,MAAM,CAAC,IAAI,EAAEnG,QAAQ,EAAE3B,OAAO,CAAC;IAC5D;IAAC,OATDsQ,IAAI;EAAA;EAAArH,MAAA,CAWJwH,OAAO;IAAP,SAAAA,OAAOA,CAAC9O,QAAQ,EAAgB;MAAA,IAAd3B,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;MAC5B,IAAIA,SAAS,CAAClH,MAAM,KAAK,CAAC,EAAE;QAC1B4I,QAAQ,GAAG,CAAC,CAAC;MACf;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA3B,OAAO,CAACwI,KAAK,GAAG,CAAC;MAEjB,OAAO,IAAI,CAAC8H,IAAI,CAAC3O,QAAQ,EAAE3B,OAAO,CAAC,CAACwJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD;IAAC,OAhBDiH,OAAO;EAAA;EAAAxH,MAAA,CAiBDyH,YAAY;IAAlB,SAAMA,YAAYA,CAAC/O,QAAQ;MAAA,IAAA3B,OAAA;QAAA2Q,KAAA,GAAA1Q,SAAA;MAAA,OAAA4H,mBAAA,CAAAyC,KAAA;QAAA,SAAAsG,cAAApG,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAP,IAAA;YAAA;cAAEjK,OAAO,GAAA2Q,KAAA,CAAA5X,MAAA,QAAA4X,KAAA,QAAAlX,SAAA,GAAAkX,KAAA,MAAG,CAAC,CAAC;cACvC,IAAIA,KAAA,CAAU5X,MAAM,KAAK,CAAC,EAAE;gBAC1B4I,QAAQ,GAAG,CAAC,CAAC;cACf;cACA3B,OAAO,CAACwI,KAAK,GAAG,CAAC;cAACgC,QAAA,CAAAP,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CACJ,IAAI,CAACP,IAAI,CAAC3O,QAAQ,EAAE3B,OAAO,CAAC,CAAC8Q,UAAU,CAAC,CAAC;YAAA;cAAA,OAAAtG,QAAA,CAAAE,MAAA,WAAAF,QAAA,CAAAuG,IAAA,CAAE,CAAC;YAAA;YAAA;cAAA,OAAAvG,QAAA,CAAAK,IAAA;UAAA;QAAA;QAAA,OAAA+F,aAAA;MAAA,uBAAAjG,OAAA;IAAA;IAC3D,OANK+F,YAAY;EAAA;EAAAzH,MAAA,CAOlB+H,aAAa;IAAb,SAAAA,aAAaA,CAAClU,GAAG,EAAE;MACjBmU,wBAAwB,CAACnU,GAAG,CAAC;;MAE7B;MACA;MACA,IAAI,CAAC5F,MAAM,CAAC+C,IAAI,CAAC6C,GAAG,EAAE,KAAK,CAAC,EAAE;QAC5BA,GAAG,CAAC4J,GAAG,GAAG1O,eAAe,CAACkZ,OAAO,GAAG,IAAIC,OAAO,CAACC,QAAQ,CAAC,CAAC,GAAGC,MAAM,CAACtC,EAAE,CAAC,CAAC;MAC1E;MAEA,IAAMA,EAAE,GAAGjS,GAAG,CAAC4J,GAAG;MAElB,IAAI,IAAI,CAAC+H,KAAK,CAAC6C,GAAG,CAACvC,EAAE,CAAC,EAAE;QACtB,MAAMtK,cAAc,qBAAmBsK,EAAE,MAAG,CAAC;MAC/C;MAEA,IAAI,CAACwC,aAAa,CAACxC,EAAE,EAAEtV,SAAS,CAAC;MACjC,IAAI,CAACgV,KAAK,CAACE,GAAG,CAACI,EAAE,EAAEjS,GAAG,CAAC;MAEvB,OAAOiS,EAAE;IACX;IAAC,OAnBDiC,aAAa;EAAA,IAqBb;EACA;EAAA;EAAA/H,MAAA,CACAuI,MAAM;IAAN,SAAAA,MAAMA,CAAC1U,GAAG,EAAEgO,QAAQ,EAAE;MAAA,IAAAnB,KAAA;MACpB7M,GAAG,GAAGmF,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;MACtB,IAAMiS,EAAE,GAAG,IAAI,CAACiC,aAAa,CAAClU,GAAG,CAAC;MAClC,IAAM2U,kBAAkB,GAAG,EAAE;;MAE7B;MACA,SAAAC,EAAA,MAAAC,YAAA,GAAkB1Z,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC,EAAAuF,EAAA,GAAAC,YAAA,CAAA5Y,MAAA,EAAA2Y,EAAA,IAAE;QAAxC,IAAMzF,GAAG,GAAA0F,YAAA,CAAAD,EAAA;QACZ,IAAMrL,KAAK,GAAG,IAAI,CAAC8F,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI5F,KAAK,CAACyF,KAAK,EAAE;UACf;QACF;QAEA,IAAMkD,WAAW,GAAG3I,KAAK,CAAC1M,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC;QAEtD,IAAIkS,WAAW,CAACzS,MAAM,EAAE;UACtB,IAAI8J,KAAK,CAACsF,SAAS,IAAIqD,WAAW,CAACrQ,QAAQ,KAAKlF,SAAS,EAAE;YACzD4M,KAAK,CAACsF,SAAS,CAACgD,GAAG,CAACI,EAAE,EAAEC,WAAW,CAACrQ,QAAQ,CAAC;UAC/C;UAEA,IAAI0H,KAAK,CAACwF,MAAM,CAACtD,IAAI,IAAIlC,KAAK,CAACwF,MAAM,CAACrD,KAAK,EAAE;YAC3CiJ,kBAAkB,CAAChQ,IAAI,CAACwK,GAAG,CAAC;UAC9B,CAAC,MAAM;YACLjU,eAAe,CAAC4Z,oBAAoB,CAACvL,KAAK,EAAEvJ,GAAG,CAAC;UAClD;QACF;MACF;MAEA2U,kBAAkB,CAAClQ,OAAO,CAAC,UAAA0K,GAAG,EAAI;QAChC,IAAItC,KAAI,CAACwC,OAAO,CAACF,GAAG,CAAC,EAAE;UACrBtC,KAAI,CAACkI,iBAAiB,CAAClI,KAAI,CAACwC,OAAO,CAACF,GAAG,CAAC,CAAC;QAC3C;MACF,CAAC,CAAC;MAEF,IAAI,CAACS,aAAa,CAACiB,KAAK,CAAC,CAAC;MAC1B,IAAI7C,QAAQ,EAAE;QACZ+D,MAAM,CAACiD,KAAK,CAAC,YAAM;UACjBhH,QAAQ,CAAC,IAAI,EAAEiE,EAAE,CAAC;QACpB,CAAC,CAAC;MACJ;MAEA,OAAOA,EAAE;IACX;IAAC,OA1CDyC,MAAM;EAAA;EAAAvI,MAAA,CA2CA8I,WAAW;IAAjB,SAAMA,WAAWA,CAACjV,GAAG,EAAEgO,QAAQ;MAAA,IAAAE,MAAA;MAAA,IAAA+D,EAAA,EAAA0C,kBAAA,EAAAO,GAAA,EAAAC,aAAA,EAAAhG,GAAA,EAAA5F,KAAA,EAAA2I,WAAA;MAAA,OAAAnH,mBAAA,CAAAyC,KAAA;QAAA,SAAA4H,aAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,IAAA,GAAA0H,SAAA,CAAAlI,IAAA;YAAA;cAC7BnN,GAAG,GAAGmF,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;cAChBiS,EAAE,GAAG,IAAI,CAACiC,aAAa,CAAClU,GAAG,CAAC;cAC5B2U,kBAAkB,GAAG,EAAE,EAE7B;cAAAO,GAAA,MAAAC,aAAA,GACkBha,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC;YAAA;cAAA,MAAA6F,GAAA,GAAAC,aAAA,CAAAlZ,MAAA;gBAAAoZ,SAAA,CAAAlI,IAAA;gBAAA;cAAA;cAAhCgC,GAAG,GAAAgG,aAAA,CAAAD,GAAA;cACN3L,KAAK,GAAG,IAAI,CAAC8F,OAAO,CAACF,GAAG,CAAC;cAAA,KAE3B5F,KAAK,CAACyF,KAAK;gBAAAqG,SAAA,CAAAlI,IAAA;gBAAA;cAAA;cAAA,OAAAkI,SAAA,CAAAzH,MAAA;YAAA;cAITsE,WAAW,GAAG3I,KAAK,CAAC1M,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC;cAAA,KAElDkS,WAAW,CAACzS,MAAM;gBAAA4V,SAAA,CAAAlI,IAAA;gBAAA;cAAA;cACpB,IAAI5D,KAAK,CAACsF,SAAS,IAAIqD,WAAW,CAACrQ,QAAQ,KAAKlF,SAAS,EAAE;gBACzD4M,KAAK,CAACsF,SAAS,CAACgD,GAAG,CAACI,EAAE,EAAEC,WAAW,CAACrQ,QAAQ,CAAC;cAC/C;cAAC,MAEG0H,KAAK,CAACwF,MAAM,CAACtD,IAAI,IAAIlC,KAAK,CAACwF,MAAM,CAACrD,KAAK;gBAAA2J,SAAA,CAAAlI,IAAA;gBAAA;cAAA;cACzCwH,kBAAkB,CAAChQ,IAAI,CAACwK,GAAG,CAAC;cAACkG,SAAA,CAAAlI,IAAA;cAAA;YAAA;cAAAkI,SAAA,CAAAlI,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEvB7Y,eAAe,CAACoa,qBAAqB,CAAC/L,KAAK,EAAEvJ,GAAG,CAAC;YAAA;cAAAkV,GAAA;cAAAG,SAAA,CAAAlI,IAAA;cAAA;YAAA;cAK7DwH,kBAAkB,CAAClQ,OAAO,CAAC,UAAA0K,GAAG,EAAI;gBAChC,IAAIjB,MAAI,CAACmB,OAAO,CAACF,GAAG,CAAC,EAAE;kBACrBjB,MAAI,CAAC6G,iBAAiB,CAAC7G,MAAI,CAACmB,OAAO,CAACF,GAAG,CAAC,CAAC;gBAC3C;cACF,CAAC,CAAC;cAACkG,SAAA,CAAAlI,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEG,IAAI,CAACnE,aAAa,CAACiB,KAAK,CAAC,CAAC;YAAA;cAChC,IAAI7C,QAAQ,EAAE;gBACZ+D,MAAM,CAACiD,KAAK,CAAC,YAAM;kBACjBhH,QAAQ,CAAC,IAAI,EAAEiE,EAAE,CAAC;gBACpB,CAAC,CAAC;cACJ;cAAC,OAAAoD,SAAA,CAAAzH,MAAA,WAEMqE,EAAE;YAAA;YAAA;cAAA,OAAAoD,SAAA,CAAAtH,IAAA;UAAA;QAAA;QAAA,OAAAqH,YAAA;MAAA,uBAAAvH,OAAA;IAAA;IACV,OA1CKoH,WAAW;EAAA,IA4CjB;EACA;EAAA;EAAA9I,MAAA,CACAoJ,cAAc;IAAd,SAAAA,cAAcA,CAAA,EAAG;MAAA,IAAAnH,MAAA;MACf;MACA,IAAI,IAAI,CAACmB,MAAM,EAAE;QACf;MACF;;MAEA;MACA,IAAI,CAACA,MAAM,GAAG,IAAI;;MAElB;MACApU,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC,CAAC5K,OAAO,CAAC,UAAA0K,GAAG,EAAI;QACvC,IAAM5F,KAAK,GAAG6E,MAAI,CAACiB,OAAO,CAACF,GAAG,CAAC;QAC/B5F,KAAK,CAAC2F,eAAe,GAAG/J,KAAK,CAAC+K,KAAK,CAAC3G,KAAK,CAAC+F,OAAO,CAAC;MACpD,CAAC,CAAC;IACJ;IAAC,OAdDiG,cAAc;EAAA;EAAApJ,MAAA,CAgBdqJ,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAACxH,QAAQ,EAAE;MAAA,IAAAQ,MAAA;MAC3B,IAAM/O,MAAM,GAAG,IAAI,CAACkS,KAAK,CAACtB,IAAI,CAAC,CAAC;MAEhC,IAAI,CAACsB,KAAK,CAACG,KAAK,CAAC,CAAC;MAElB3W,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC,CAAC5K,OAAO,CAAC,UAAA0K,GAAG,EAAI;QACvC,IAAM5F,KAAK,GAAGiF,MAAI,CAACa,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI5F,KAAK,CAACkD,OAAO,EAAE;UACjBlD,KAAK,CAAC+F,OAAO,GAAG,EAAE;QACpB,CAAC,MAAM;UACL/F,KAAK,CAAC+F,OAAO,CAACwC,KAAK,CAAC,CAAC;QACvB;MACF,CAAC,CAAC;MAEF,IAAI9D,QAAQ,EAAE;QACZ+D,MAAM,CAACiD,KAAK,CAAC,YAAM;UACjBhH,QAAQ,CAAC,IAAI,EAAEvO,MAAM,CAAC;QACxB,CAAC,CAAC;MACJ;MAEA,OAAOA,MAAM;IACf;IAAC,OAtBD+V,kBAAkB;EAAA;EAAArJ,MAAA,CAyBlBsJ,aAAa;IAAb,SAAAA,aAAaA,CAAC5Q,QAAQ,EAAE;MAAA,IAAA6J,MAAA;MACtB,IAAM7R,OAAO,GAAG,IAAIsO,SAAS,CAACC,OAAO,CAACvG,QAAQ,CAAC;MAC/C,IAAM6Q,MAAM,GAAG,EAAE;MAEjB,IAAI,CAACC,4BAA4B,CAAC9Q,QAAQ,EAAE,UAAC7E,GAAG,EAAEiS,EAAE,EAAK;QACvD,IAAIpV,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC,CAACP,MAAM,EAAE;UACvCiW,MAAM,CAAC/Q,IAAI,CAACsN,EAAE,CAAC;QACjB;MACF,CAAC,CAAC;MAEF,IAAM0C,kBAAkB,GAAG,EAAE;MAC7B,IAAMiB,WAAW,GAAG,EAAE;MAAC,IAAAC,KAAA,YAAAA,CAAA,EAEiB;QACtC,IAAMC,QAAQ,GAAGJ,MAAM,CAAC7X,CAAC,CAAC;QAC1B,IAAMkY,SAAS,GAAGrH,MAAI,CAACiD,KAAK,CAACC,GAAG,CAACkE,QAAQ,CAAC;QAE1C3a,MAAM,CAACuD,IAAI,CAACgQ,MAAI,CAACW,OAAO,CAAC,CAAC5K,OAAO,CAAC,UAAA0K,GAAG,EAAI;UACvC,IAAM5F,KAAK,GAAGmF,MAAI,CAACW,OAAO,CAACF,GAAG,CAAC;UAE/B,IAAI5F,KAAK,CAACyF,KAAK,EAAE;YACf;UACF;UAEA,IAAIzF,KAAK,CAAC1M,OAAO,CAACsV,eAAe,CAAC4D,SAAS,CAAC,CAACtW,MAAM,EAAE;YACnD,IAAI8J,KAAK,CAACwF,MAAM,CAACtD,IAAI,IAAIlC,KAAK,CAACwF,MAAM,CAACrD,KAAK,EAAE;cAC3CiJ,kBAAkB,CAAChQ,IAAI,CAACwK,GAAG,CAAC;YAC9B,CAAC,MAAM;cACLyG,WAAW,CAACjR,IAAI,CAAC;gBAACwK,GAAG,EAAHA,GAAG;gBAAEnP,GAAG,EAAE+V;cAAS,CAAC,CAAC;YACzC;UACF;QACF,CAAC,CAAC;QAEFrH,MAAI,CAAC+F,aAAa,CAACqB,QAAQ,EAAEC,SAAS,CAAC;QACvCrH,MAAI,CAACiD,KAAK,CAAC+D,MAAM,CAACI,QAAQ,CAAC;MAC7B,CAAC;MAtBD,KAAK,IAAIjY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6X,MAAM,CAACzZ,MAAM,EAAE4B,CAAC,EAAE;QAAAgY,KAAA;MAAA;MAwBtC,OAAO;QAAElB,kBAAkB,EAAlBA,kBAAkB;QAAEiB,WAAW,EAAXA,WAAW;QAAEF,MAAM,EAANA;MAAO,CAAC;IACpD;IAAC,OAtCDD,aAAa;EAAA;EAAAtJ,MAAA,CAwCbuJ,MAAM;IAAN,SAAAA,MAAMA,CAAC7Q,QAAQ,EAAEmJ,QAAQ,EAAE;MAAA,IAAA0B,MAAA;MACzB;MACA;MACA;MACA,IAAI,IAAI,CAACH,MAAM,IAAI,CAAC,IAAI,CAAC+D,eAAe,IAAInO,KAAK,CAAC6Q,MAAM,CAACnR,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;QACtE,OAAO,IAAI,CAAC2Q,kBAAkB,CAACxH,QAAQ,CAAC;MAC1C;MAEA,IAAAiI,mBAAA,GAAoD,IAAI,CAACR,aAAa,CAAC5Q,QAAQ,CAAC;QAAxE8P,kBAAkB,GAAAsB,mBAAA,CAAlBtB,kBAAkB;QAAEiB,WAAW,GAAAK,mBAAA,CAAXL,WAAW;QAAEF,MAAM,GAAAO,mBAAA,CAANP,MAAM;;MAE/C;MACAE,WAAW,CAACnR,OAAO,CAAC,UAAAiR,MAAM,EAAI;QAC5B,IAAMnM,KAAK,GAAGmG,MAAI,CAACL,OAAO,CAACqG,MAAM,CAACvG,GAAG,CAAC;QAEtC,IAAI5F,KAAK,EAAE;UACTA,KAAK,CAACsF,SAAS,IAAItF,KAAK,CAACsF,SAAS,CAAC6G,MAAM,CAACA,MAAM,CAAC1V,GAAG,CAAC4J,GAAG,CAAC;UACzD1O,eAAe,CAACgb,sBAAsB,CAAC3M,KAAK,EAAEmM,MAAM,CAAC1V,GAAG,CAAC;QAC3D;MACF,CAAC,CAAC;MAEF2U,kBAAkB,CAAClQ,OAAO,CAAC,UAAA0K,GAAG,EAAI;QAChC,IAAM5F,KAAK,GAAGmG,MAAI,CAACL,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI5F,KAAK,EAAE;UACTmG,MAAI,CAACqF,iBAAiB,CAACxL,KAAK,CAAC;QAC/B;MACF,CAAC,CAAC;MAEF,IAAI,CAACqG,aAAa,CAACiB,KAAK,CAAC,CAAC;MAE1B,IAAMpR,MAAM,GAAGiW,MAAM,CAACzZ,MAAM;MAE5B,IAAI+R,QAAQ,EAAE;QACZ+D,MAAM,CAACiD,KAAK,CAAC,YAAM;UACjBhH,QAAQ,CAAC,IAAI,EAAEvO,MAAM,CAAC;QACxB,CAAC,CAAC;MACJ;MAEA,OAAOA,MAAM;IACf;IAAC,OAvCDiW,MAAM;EAAA;EAAAvJ,MAAA,CAyCAgK,WAAW;IAAjB,SAAMA,WAAWA,CAACtR,QAAQ,EAAEmJ,QAAQ;MAAA,IAAAgD,MAAA;MAAA,IAAAoF,oBAAA,EAAAzB,kBAAA,EAAAiB,WAAA,EAAAF,MAAA,EAAAvF,SAAA,EAAAC,KAAA,EAAAiG,OAAA,EAAA9M,KAAA,EAAA9J,MAAA;MAAA,OAAAsL,mBAAA,CAAAyC,KAAA;QAAA,SAAA8I,aAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5I,IAAA,GAAA4I,SAAA,CAAApJ,IAAA;YAAA;cAAA,MAI9B,IAAI,CAACoC,MAAM,IAAI,CAAC,IAAI,CAAC+D,eAAe,IAAInO,KAAK,CAAC6Q,MAAM,CAACnR,QAAQ,EAAE,CAAC,CAAC,CAAC;gBAAA0R,SAAA,CAAApJ,IAAA;gBAAA;cAAA;cAAA,OAAAoJ,SAAA,CAAA3I,MAAA,WAC7D,IAAI,CAAC4H,kBAAkB,CAACxH,QAAQ,CAAC;YAAA;cAAAoI,oBAAA,GAGU,IAAI,CAACX,aAAa,CAAC5Q,QAAQ,CAAC,EAAxE8P,kBAAkB,GAAAyB,oBAAA,CAAlBzB,kBAAkB,EAAEiB,WAAW,GAAAQ,oBAAA,CAAXR,WAAW,EAAEF,MAAM,GAAAU,oBAAA,CAANV,MAAM,EAE/C;cAAAvF,SAAA,GAAArF,+BAAA,CACqB8K,WAAW;YAAA;cAAA,KAAAxF,KAAA,GAAAD,SAAA,IAAA/C,IAAA;gBAAAmJ,SAAA,CAAApJ,IAAA;gBAAA;cAAA;cAArBuI,OAAM,GAAAtF,KAAA,CAAA/T,KAAA;cACTkN,KAAK,GAAG,IAAI,CAAC8F,OAAO,CAACqG,OAAM,CAACvG,GAAG,CAAC;cAAA,KAElC5F,KAAK;gBAAAgN,SAAA,CAAApJ,IAAA;gBAAA;cAAA;cACP5D,KAAK,CAACsF,SAAS,IAAItF,KAAK,CAACsF,SAAS,CAAC6G,MAAM,CAACA,OAAM,CAAC1V,GAAG,CAAC4J,GAAG,CAAC;cAAC2M,SAAA,CAAApJ,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CACpD7Y,eAAe,CAACsb,uBAAuB,CAACjN,KAAK,EAAEmM,OAAM,CAAC1V,GAAG,CAAC;YAAA;cAAAuW,SAAA,CAAApJ,IAAA;cAAA;YAAA;cAGpEwH,kBAAkB,CAAClQ,OAAO,CAAC,UAAA0K,GAAG,EAAI;gBAChC,IAAM5F,KAAK,GAAGyH,MAAI,CAAC3B,OAAO,CAACF,GAAG,CAAC;gBAE/B,IAAI5F,KAAK,EAAE;kBACTyH,MAAI,CAAC+D,iBAAiB,CAACxL,KAAK,CAAC;gBAC/B;cACF,CAAC,CAAC;cAACgN,SAAA,CAAApJ,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEG,IAAI,CAACnE,aAAa,CAACiB,KAAK,CAAC,CAAC;YAAA;cAE1BpR,MAAM,GAAGiW,MAAM,CAACzZ,MAAM;cAE5B,IAAI+R,QAAQ,EAAE;gBACZ+D,MAAM,CAACiD,KAAK,CAAC,YAAM;kBACjBhH,QAAQ,CAAC,IAAI,EAAEvO,MAAM,CAAC;gBACxB,CAAC,CAAC;cACJ;cAAC,OAAA8W,SAAA,CAAA3I,MAAA,WAEMnO,MAAM;YAAA;YAAA;cAAA,OAAA8W,SAAA,CAAAxI,IAAA;UAAA;QAAA;QAAA,OAAAuI,YAAA;MAAA,uBAAAzI,OAAA;IAAA;IACd,OAtCKsI,WAAW;EAAA,IAwCjB;EACA;EACA;EACA;EAAA;EAAAhK,MAAA,CACAsK,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAAA,EAAG;MAAA,IAAAjF,MAAA;MACjB;MACA,IAAI,CAAC,IAAI,CAACjC,MAAM,EAAE;QAChB;MACF;;MAEA;MACA;MACA,IAAI,CAACA,MAAM,GAAG,KAAK;MAEnBpU,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC,CAAC5K,OAAO,CAAC,UAAA0K,GAAG,EAAI;QACvC,IAAM5F,KAAK,GAAGiI,MAAI,CAACnC,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI5F,KAAK,CAACyF,KAAK,EAAE;UACfzF,KAAK,CAACyF,KAAK,GAAG,KAAK;;UAEnB;UACA;UACAwC,MAAI,CAACuD,iBAAiB,CAACxL,KAAK,EAAEA,KAAK,CAAC2F,eAAe,CAAC;QACtD,CAAC,MAAM;UACL;UACA;UACAhU,eAAe,CAACwb,iBAAiB,CAC/BnN,KAAK,CAACkD,OAAO,EACblD,KAAK,CAAC2F,eAAe,EACrB3F,KAAK,CAAC+F,OAAO,EACb/F,KAAK,EACL;YAAC0F,YAAY,EAAE1F,KAAK,CAAC0F;UAAY,CACnC,CAAC;QACH;QAEA1F,KAAK,CAAC2F,eAAe,GAAG,IAAI;MAC9B,CAAC,CAAC;IACJ;IAAC,OAjCDuH,gBAAgB;EAAA;EAAAtK,MAAA,CAmCVwK,qBAAqB;IAA3B,SAAMA,qBAAqBA,CAAA;MAAA,OAAA5L,mBAAA,CAAAyC,KAAA;QAAA,SAAAoJ,uBAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlJ,IAAA,GAAAkJ,SAAA,CAAA1J,IAAA;YAAA;cACzB,IAAI,CAACsJ,gBAAgB,CAAC,CAAC;cAACI,SAAA,CAAA1J,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAClB,IAAI,CAACnE,aAAa,CAACiB,KAAK,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAgG,SAAA,CAAA9I,IAAA;UAAA;QAAA;QAAA,OAAA6I,sBAAA;MAAA,uBAAA/I,OAAA;IAAA;IACjC,OAHK8I,qBAAqB;EAAA;EAAAxK,MAAA,CAI3B2K,qBAAqB;IAArB,SAAAA,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAACL,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAAC7G,aAAa,CAACiB,KAAK,CAAC,CAAC;IAC5B;IAAC,OAHDiG,qBAAqB;EAAA;EAAA3K,MAAA,CAKrB4K,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAAC,IAAI,CAACzD,eAAe,EAAE;QACzB,MAAM,IAAIpX,KAAK,CAAC,gDAAgD,CAAC;MACnE;MAEA,IAAM8a,SAAS,GAAG,IAAI,CAAC1D,eAAe;MAEtC,IAAI,CAACA,eAAe,GAAG,IAAI;MAE3B,OAAO0D,SAAS;IAClB;IAAC,OAVDD,iBAAiB;EAAA,IAYjB;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAA5K,MAAA,CACA8K,aAAa;IAAb,SAAAA,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAAC3D,eAAe,EAAE;QACxB,MAAM,IAAIpX,KAAK,CAAC,sDAAsD,CAAC;MACzE;MAEA,IAAI,CAACoX,eAAe,GAAG,IAAIpY,eAAe,CAAC4T,MAAM,CAAD,CAAC;IACnD;IAAC,OANDmI,aAAa;EAAA;EAAA9K,MAAA,CAQb+K,aAAa;IAAb,SAAAA,aAAaA,CAACrS,QAAQ,EAAE;MAAA,IAAAsS,MAAA;MACtB;MACA;MACA;MACA;MACA;MACA,IAAMC,oBAAoB,GAAG,CAAC,CAAC;;MAE/B;MACA;MACA,IAAMC,MAAM,GAAG,IAAInc,eAAe,CAAC4T,MAAM,CAAD,CAAC;MACzC,IAAMwI,UAAU,GAAGpc,eAAe,CAACqc,qBAAqB,CAAC1S,QAAQ,CAAC;MAElE1J,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC,CAAC5K,OAAO,CAAC,UAAA0K,GAAG,EAAI;QACvC,IAAM5F,KAAK,GAAG4N,MAAI,CAAC9H,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI,CAAC5F,KAAK,CAACwF,MAAM,CAACtD,IAAI,IAAIlC,KAAK,CAACwF,MAAM,CAACrD,KAAK,KAAK,CAAEyL,MAAI,CAAC5H,MAAM,EAAE;UAC9D;UACA;UACA;UACA;UACA;UACA,IAAIhG,KAAK,CAAC+F,OAAO,YAAYpU,eAAe,CAAC4T,MAAM,EAAE;YACnDsI,oBAAoB,CAACjI,GAAG,CAAC,GAAG5F,KAAK,CAAC+F,OAAO,CAACY,KAAK,CAAC,CAAC;YACjD;UACF;UAEA,IAAI,EAAE3G,KAAK,CAAC+F,OAAO,YAAYvT,KAAK,CAAC,EAAE;YACrC,MAAM,IAAIG,KAAK,CAAC,8CAA8C,CAAC;UACjE;;UAEA;UACA;UACA;UACA;UACA,IAAMsb,qBAAqB,GAAG,SAAAA,CAAAxX,GAAG,EAAI;YACnC,IAAIqX,MAAM,CAAC7C,GAAG,CAACxU,GAAG,CAAC4J,GAAG,CAAC,EAAE;cACvB,OAAOyN,MAAM,CAACzF,GAAG,CAAC5R,GAAG,CAAC4J,GAAG,CAAC;YAC5B;YAEA,IAAM6N,YAAY,GAChBH,UAAU,IACV,CAACA,UAAU,CAAC1a,IAAI,CAAC,UAAAqV,EAAE;cAAA,OAAI9M,KAAK,CAAC6Q,MAAM,CAAC/D,EAAE,EAAEjS,GAAG,CAAC4J,GAAG,CAAC;YAAA,EAAC,GAC/C5J,GAAG,GAAGmF,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;YAE1BqX,MAAM,CAACxF,GAAG,CAAC7R,GAAG,CAAC4J,GAAG,EAAE6N,YAAY,CAAC;YAEjC,OAAOA,YAAY;UACrB,CAAC;UAEDL,oBAAoB,CAACjI,GAAG,CAAC,GAAG5F,KAAK,CAAC+F,OAAO,CAAC9S,GAAG,CAACgb,qBAAqB,CAAC;QACtE;MACF,CAAC,CAAC;MAEF,OAAOJ,oBAAoB;IAC7B;IAAC,OAvDDF,aAAa;EAAA;EAAA/K,MAAA,CAyDbuL,YAAY;IAAZ,SAAAA,YAAYA,CAAAC,IAAA,EAAiD;MAAA,IAA9CzU,OAAO,GAAAyU,IAAA,CAAPzU,OAAO;QAAE0U,WAAW,GAAAD,IAAA,CAAXC,WAAW;QAAE5J,QAAQ,GAAA2J,IAAA,CAAR3J,QAAQ;QAAE6J,UAAU,GAAAF,IAAA,CAAVE,UAAU;MAGvD;MACA;MACA;MACA,IAAIpY,MAAM;MACV,IAAIyD,OAAO,CAAC4U,aAAa,EAAE;QACzBrY,MAAM,GAAG;UAAEsY,cAAc,EAAEH;QAAY,CAAC;QAExC,IAAIC,UAAU,KAAKlb,SAAS,EAAE;UAC5B8C,MAAM,CAACoY,UAAU,GAAGA,UAAU;QAChC;MACF,CAAC,MAAM;QACLpY,MAAM,GAAGmY,WAAW;MACtB;MAEA,IAAI5J,QAAQ,EAAE;QACZ+D,MAAM,CAACiD,KAAK,CAAC,YAAM;UACjBhH,QAAQ,CAAC,IAAI,EAAEvO,MAAM,CAAC;QACxB,CAAC,CAAC;MACJ;MAEA,OAAOA,MAAM;IACf;IAAC,OAxBDiY,YAAY;EAAA,IA0BZ;EACA;EAAA;EAAAvL,MAAA,CACM6L,WAAW;IAAjB,SAAMA,WAAWA,CAACnT,QAAQ,EAAEoT,GAAG,EAAE/U,OAAO,EAAE8K,QAAQ;MAAA,IAAAkK,OAAA;MAAA,IAAArb,OAAA,EAAAua,oBAAA,EAAAe,aAAA,EAAAP,WAAA,EAAAC,UAAA,EAAA7X,GAAA;MAAA,OAAA+K,mBAAA,CAAAyC,KAAA;QAAA,SAAA4K,aAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1K,IAAA,GAAA0K,SAAA,CAAAlL,IAAA;YAAA;cAChD,IAAI,CAAEa,QAAQ,IAAI9K,OAAO,YAAY3C,QAAQ,EAAE;gBAC7CyN,QAAQ,GAAG9K,OAAO;gBAClBA,OAAO,GAAG,IAAI;cAChB;cAEA,IAAI,CAACA,OAAO,EAAE;gBACZA,OAAO,GAAG,CAAC,CAAC;cACd;cAEMrG,OAAO,GAAG,IAAIsO,SAAS,CAACC,OAAO,CAACvG,QAAQ,EAAE,IAAI,CAAC;cAE/CuS,oBAAoB,GAAG,IAAI,CAACF,aAAa,CAACrS,QAAQ,CAAC;cAErDsT,aAAa,GAAG,CAAC,CAAC;cAElBP,WAAW,GAAG,CAAC;cAAAS,SAAA,CAAAlL,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEb,IAAI,CAACuE,6BAA6B,CAACzT,QAAQ;gBAAE,SAAA0I,QAAOvN,GAAG,EAAEiS,EAAE;kBAAA,IAAAsG,WAAA;kBAAA,OAAAxN,mBAAA,CAAAyC,KAAA;oBAAA,SAAAC,SAAA+K,SAAA;sBAAA,kBAAAA,SAAA,CAAA7K,IAAA,GAAA6K,SAAA,CAAArL,IAAA;wBAAA;0BACzDoL,WAAW,GAAG1b,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC;0BAAA,KAE5CuY,WAAW,CAAC9Y,MAAM;4BAAA+Y,SAAA,CAAArL,IAAA;4BAAA;0BAAA;0BACpB;0BACA+K,OAAI,CAACzD,aAAa,CAACxC,EAAE,EAAEjS,GAAG,CAAC;0BAACwY,SAAA,CAAArL,IAAA;0BAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CACNmE,OAAI,CAACO,qBAAqB,CAC9CzY,GAAG,EACHiY,GAAG,EACHM,WAAW,CAAC7V,YACd,CAAC;wBAAA;0BAJDyV,aAAa,GAAAK,SAAA,CAAAvE,IAAA;0BAMb,EAAE2D,WAAW;0BAAC,IAET1U,OAAO,CAACwV,KAAK;4BAAAF,SAAA,CAAArL,IAAA;4BAAA;0BAAA;0BAAA,OAAAqL,SAAA,CAAA5K,MAAA,WACT,KAAK;wBAAA;0BAAA,OAAA4K,SAAA,CAAA5K,MAAA,WAIT,IAAI;wBAAA;wBAAA;0BAAA,OAAA4K,SAAA,CAAAzK,IAAA;sBAAA;oBAAA;oBAAA,OAAAN,QAAA;kBAAA,uBAAAI,OAAA;gBAAA;gBACZ,OAAAN,OAAA;cAAA,IAAC;YAAA;cAEFpS,MAAM,CAACuD,IAAI,CAACyZ,aAAa,CAAC,CAAC1T,OAAO,CAAC,UAAA0K,GAAG,EAAI;gBACxC,IAAM5F,KAAK,GAAG2O,OAAI,CAAC7I,OAAO,CAACF,GAAG,CAAC;gBAE/B,IAAI5F,KAAK,EAAE;kBACT2O,OAAI,CAACnD,iBAAiB,CAACxL,KAAK,EAAE6N,oBAAoB,CAACjI,GAAG,CAAC,CAAC;gBAC1D;cACF,CAAC,CAAC;cAACkJ,SAAA,CAAAlL,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEG,IAAI,CAACnE,aAAa,CAACiB,KAAK,CAAC,CAAC;YAAA;cAAA,MAM5B+G,WAAW,KAAK,CAAC,IAAI1U,OAAO,CAACyV,MAAM;gBAAAN,SAAA,CAAAlL,IAAA;gBAAA;cAAA;cAC/BnN,GAAG,GAAG9E,eAAe,CAAC0d,qBAAqB,CAAC/T,QAAQ,EAAEoT,GAAG,CAAC;cAChE,IAAI,CAACjY,GAAG,CAAC4J,GAAG,IAAI1G,OAAO,CAAC2U,UAAU,EAAE;gBAClC7X,GAAG,CAAC4J,GAAG,GAAG1G,OAAO,CAAC2U,UAAU;cAC9B;cAACQ,SAAA,CAAAlL,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEkB,IAAI,CAACkB,WAAW,CAACjV,GAAG,CAAC;YAAA;cAAxC6X,UAAU,GAAAQ,SAAA,CAAApE,IAAA;cACV2D,WAAW,GAAG,CAAC;YAAC;cAAA,OAAAS,SAAA,CAAAzK,MAAA,WAGX,IAAI,CAAC8J,YAAY,CAAC;gBACvBxU,OAAO,EAAPA,OAAO;gBACP2U,UAAU,EAAVA,UAAU;gBACVD,WAAW,EAAXA,WAAW;gBACX5J,QAAQ,EAARA;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAqK,SAAA,CAAAtK,IAAA;UAAA;QAAA;QAAA,OAAAqK,YAAA;MAAA,uBAAAvK,OAAA;IAAA;IACH,OAtEKmK,WAAW;EAAA,IAuEjB;EACA;EAAA;EAAA7L,MAAA,CACA0M,MAAM;IAAN,SAAAA,MAAMA,CAAChU,QAAQ,EAAEoT,GAAG,EAAE/U,OAAO,EAAE8K,QAAQ,EAAE;MAAA,IAAA8K,OAAA;MACvC,IAAI,CAAE9K,QAAQ,IAAI9K,OAAO,YAAY3C,QAAQ,EAAE;QAC7CyN,QAAQ,GAAG9K,OAAO;QAClBA,OAAO,GAAG,IAAI;MAChB;MAEA,IAAI,CAACA,OAAO,EAAE;QACZA,OAAO,GAAG,CAAC,CAAC;MACd;MAEA,IAAMrG,OAAO,GAAG,IAAIsO,SAAS,CAACC,OAAO,CAACvG,QAAQ,EAAE,IAAI,CAAC;MAErD,IAAMuS,oBAAoB,GAAG,IAAI,CAACF,aAAa,CAACrS,QAAQ,CAAC;MAEzD,IAAIsT,aAAa,GAAG,CAAC,CAAC;MAEtB,IAAIP,WAAW,GAAG,CAAC;MAEnB,IAAI,CAACjC,4BAA4B,CAAC9Q,QAAQ,EAAE,UAAC7E,GAAG,EAAEiS,EAAE,EAAK;QACvD,IAAMsG,WAAW,GAAG1b,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC;QAEhD,IAAIuY,WAAW,CAAC9Y,MAAM,EAAE;UACtB;UACAqZ,OAAI,CAACrE,aAAa,CAACxC,EAAE,EAAEjS,GAAG,CAAC;UAC3BmY,aAAa,GAAGW,OAAI,CAACC,oBAAoB,CACvC/Y,GAAG,EACHiY,GAAG,EACHM,WAAW,CAAC7V,YACd,CAAC;UAED,EAAEkV,WAAW;UAEb,IAAI,CAAC1U,OAAO,CAACwV,KAAK,EAAE;YAClB,OAAO,KAAK,CAAC,CAAC;UAChB;QACF;QAEA,OAAO,IAAI;MACb,CAAC,CAAC;MAEFvd,MAAM,CAACuD,IAAI,CAACyZ,aAAa,CAAC,CAAC1T,OAAO,CAAC,UAAA0K,GAAG,EAAI;QACxC,IAAM5F,KAAK,GAAGuP,OAAI,CAACzJ,OAAO,CAACF,GAAG,CAAC;QAC/B,IAAI5F,KAAK,EAAE;UACTuP,OAAI,CAAC/D,iBAAiB,CAACxL,KAAK,EAAE6N,oBAAoB,CAACjI,GAAG,CAAC,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAI,CAACS,aAAa,CAACiB,KAAK,CAAC,CAAC;;MAG1B;MACA;MACA;MACA,IAAIgH,UAAU;MACd,IAAID,WAAW,KAAK,CAAC,IAAI1U,OAAO,CAACyV,MAAM,EAAE;QACvC,IAAM3Y,GAAG,GAAG9E,eAAe,CAAC0d,qBAAqB,CAAC/T,QAAQ,EAAEoT,GAAG,CAAC;QAChE,IAAI,CAACjY,GAAG,CAAC4J,GAAG,IAAI1G,OAAO,CAAC2U,UAAU,EAAE;UAClC7X,GAAG,CAAC4J,GAAG,GAAG1G,OAAO,CAAC2U,UAAU;QAC9B;QAEAA,UAAU,GAAG,IAAI,CAACnD,MAAM,CAAC1U,GAAG,CAAC;QAC7B4X,WAAW,GAAG,CAAC;MACjB;MAGA,OAAO,IAAI,CAACF,YAAY,CAAC;QACvBxU,OAAO,EAAPA,OAAO;QACP0U,WAAW,EAAXA,WAAW;QACX5J,QAAQ,EAARA,QAAQ;QACRnJ,QAAQ,EAARA,QAAQ;QACRoT,GAAG,EAAHA;MACF,CAAC,CAAC;IACJ;IAAC,OAxEDY,MAAM;EAAA,IA0EN;EACA;EACA;EAAA;EAAA1M,MAAA,CACAwM,MAAM;IAAN,SAAAA,MAAMA,CAAC9T,QAAQ,EAAEoT,GAAG,EAAE/U,OAAO,EAAE8K,QAAQ,EAAE;MACvC,IAAI,CAACA,QAAQ,IAAI,OAAO9K,OAAO,KAAK,UAAU,EAAE;QAC9C8K,QAAQ,GAAG9K,OAAO;QAClBA,OAAO,GAAG,CAAC,CAAC;MACd;MAEA,OAAO,IAAI,CAAC2V,MAAM,CAChBhU,QAAQ,EACRoT,GAAG,EACH9c,MAAM,CAAC+D,MAAM,CAAC,CAAC,CAAC,EAAEgE,OAAO,EAAE;QAACyV,MAAM,EAAE,IAAI;QAAEb,aAAa,EAAE;MAAI,CAAC,CAAC,EAC/D9J,QACF,CAAC;IACH;IAAC,OAZD2K,MAAM;EAAA;EAAAxM,MAAA,CAcN6M,WAAW;IAAX,SAAAA,WAAWA,CAACnU,QAAQ,EAAEoT,GAAG,EAAE/U,OAAO,EAAE8K,QAAQ,EAAE;MAC5C,IAAI,CAACA,QAAQ,IAAI,OAAO9K,OAAO,KAAK,UAAU,EAAE;QAC9C8K,QAAQ,GAAG9K,OAAO;QAClBA,OAAO,GAAG,CAAC,CAAC;MACd;MAEA,OAAO,IAAI,CAAC8U,WAAW,CACrBnT,QAAQ,EACRoT,GAAG,EACH9c,MAAM,CAAC+D,MAAM,CAAC,CAAC,CAAC,EAAEgE,OAAO,EAAE;QAACyV,MAAM,EAAE,IAAI;QAAEb,aAAa,EAAE;MAAI,CAAC,CAAC,EAC/D9J,QACF,CAAC;IACH;IAAC,OAZDgL,WAAW;EAAA,IAcX;EACA;EACA;EACA;EAAA;EAAA7M,MAAA,CACMmM,6BAA6B;IAAnC,SAAMA,6BAA6BA,CAACzT,QAAQ,EAAE5E,EAAE;MAAA,IAAAgZ,WAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAlH,EAAA,EAAAjS,GAAA;MAAA,OAAA+K,mBAAA,CAAAyC,KAAA;QAAA,SAAA4L,+BAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1L,IAAA,GAAA0L,SAAA,CAAAlM,IAAA;YAAA;cACxC8L,WAAW,GAAG/d,eAAe,CAACqc,qBAAqB,CAAC1S,QAAQ,CAAC;cAAA,KAE/DoU,WAAW;gBAAAI,SAAA,CAAAlM,IAAA;gBAAA;cAAA;cAAA+L,UAAA,GAAApO,+BAAA,CACImO,WAAW;YAAA;cAAA,KAAAE,MAAA,GAAAD,UAAA,IAAA9L,IAAA;gBAAAiM,SAAA,CAAAlM,IAAA;gBAAA;cAAA;cAAjB8E,EAAE,GAAAkH,MAAA,CAAA9c,KAAA;cACL2D,GAAG,GAAG,IAAI,CAAC2R,KAAK,CAACC,GAAG,CAACK,EAAE,CAAC;cAAAoH,SAAA,CAAAC,EAAA,GAE1BtZ,GAAG;cAAA,KAAAqZ,SAAA,CAAAC,EAAA;gBAAAD,SAAA,CAAAlM,IAAA;gBAAA;cAAA;cAAAkM,SAAA,CAAAlM,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAAa9T,EAAE,CAACD,GAAG,EAAEiS,EAAE,CAAC;YAAA;cAAAoH,SAAA,CAAAC,EAAA,IAAAD,SAAA,CAAApF,IAAA;YAAA;cAAA,KAAAoF,SAAA,CAAAC,EAAA;gBAAAD,SAAA,CAAAlM,IAAA;gBAAA;cAAA;cAAA,OAAAkM,SAAA,CAAAzL,MAAA;YAAA;cAAAyL,SAAA,CAAAlM,IAAA;cAAA;YAAA;cAAAkM,SAAA,CAAAlM,IAAA;cAAA;YAAA;cAAAkM,SAAA,CAAAlM,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAK3B,IAAI,CAACpC,KAAK,CAAC4H,YAAY,CAACtZ,EAAE,CAAC;YAAA;YAAA;cAAA,OAAAoZ,SAAA,CAAAtL,IAAA;UAAA;QAAA;QAAA,OAAAqL,8BAAA;MAAA,uBAAAvL,OAAA;IAAA;IAEpC,OAdKyK,6BAA6B;EAAA;EAAAnM,MAAA,CAenCwJ,4BAA4B;IAA5B,SAAAA,4BAA4BA,CAAC9Q,QAAQ,EAAE5E,EAAE,EAAE;MACzC,IAAMgZ,WAAW,GAAG/d,eAAe,CAACqc,qBAAqB,CAAC1S,QAAQ,CAAC;MAEnE,IAAIoU,WAAW,EAAE;QACf,SAAAO,UAAA,GAAA1O,+BAAA,CAAiBmO,WAAW,GAAAQ,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAApM,IAAA,GAAE;UAAA,IAAnB6E,EAAE,GAAAwH,MAAA,CAAApd,KAAA;UACX,IAAM2D,GAAG,GAAG,IAAI,CAAC2R,KAAK,CAACC,GAAG,CAACK,EAAE,CAAC;UAE9B,IAAIjS,GAAG,IAAI,CAACC,EAAE,CAACD,GAAG,EAAEiS,EAAE,CAAC,EAAE;YACvB;UACF;QACF;MACF,CAAC,MAAM;QACL,IAAI,CAACN,KAAK,CAAClN,OAAO,CAACxE,EAAE,CAAC;MACxB;IACF;IAAC,OAdD0V,4BAA4B;EAAA;EAAAxJ,MAAA,CAgB5BuN,uBAAuB;IAAvB,SAAAA,uBAAuBA,CAAC1Z,GAAG,EAAEiY,GAAG,EAAEvV,YAAY,EAAE;MAAA,IAAAiX,OAAA;MAC9C,IAAMC,cAAc,GAAG,CAAC,CAAC;MAEzBze,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC,CAAC5K,OAAO,CAAC,UAAA0K,GAAG,EAAI;QACvC,IAAM5F,KAAK,GAAGoQ,OAAI,CAACtK,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI5F,KAAK,CAACyF,KAAK,EAAE;UACf;QACF;QAEA,IAAIzF,KAAK,CAACkD,OAAO,EAAE;UACjBmN,cAAc,CAACzK,GAAG,CAAC,GAAG5F,KAAK,CAAC1M,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC,CAACP,MAAM;QACjE,CAAC,MAAM;UACL;UACA;UACAma,cAAc,CAACzK,GAAG,CAAC,GAAG5F,KAAK,CAAC+F,OAAO,CAACkF,GAAG,CAACxU,GAAG,CAAC4J,GAAG,CAAC;QAClD;MACF,CAAC,CAAC;MAEF,OAAOgQ,cAAc;IACvB;IAAC,OApBDF,uBAAuB;EAAA;EAAAvN,MAAA,CAsBvB4M,oBAAoB;IAApB,SAAAA,oBAAoBA,CAAC/Y,GAAG,EAAEiY,GAAG,EAAEvV,YAAY,EAAE;MAE3C,IAAMkX,cAAc,GAAG,IAAI,CAACF,uBAAuB,CAAC1Z,GAAG,EAAEiY,GAAG,EAAEvV,YAAY,CAAC;MAE3E,IAAMmX,OAAO,GAAG1U,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;MAChC9E,eAAe,CAAC4e,OAAO,CAAC9Z,GAAG,EAAEiY,GAAG,EAAE;QAACvV,YAAY,EAAZA;MAAY,CAAC,CAAC;MAEjD,IAAMyV,aAAa,GAAG,CAAC,CAAC;MAExB,SAAA4B,GAAA,MAAAC,aAAA,GAAkB7e,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC,EAAA0K,GAAA,GAAAC,aAAA,CAAA/d,MAAA,EAAA8d,GAAA,IAAE;QAAxC,IAAM5K,GAAG,GAAA6K,aAAA,CAAAD,GAAA;QACZ,IAAMxQ,KAAK,GAAG,IAAI,CAAC8F,OAAO,CAACF,GAAG,CAAC;QAE/B,IAAI5F,KAAK,CAACyF,KAAK,EAAE;UACf;QACF;QAEA,IAAMiL,UAAU,GAAG1Q,KAAK,CAAC1M,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC;QACrD,IAAMka,KAAK,GAAGD,UAAU,CAACxa,MAAM;QAC/B,IAAM0a,MAAM,GAAGP,cAAc,CAACzK,GAAG,CAAC;QAElC,IAAI+K,KAAK,IAAI3Q,KAAK,CAACsF,SAAS,IAAIoL,UAAU,CAACpY,QAAQ,KAAKlF,SAAS,EAAE;UACjE4M,KAAK,CAACsF,SAAS,CAACgD,GAAG,CAAC7R,GAAG,CAAC4J,GAAG,EAAEqQ,UAAU,CAACpY,QAAQ,CAAC;QACnD;QAEA,IAAI0H,KAAK,CAACwF,MAAM,CAACtD,IAAI,IAAIlC,KAAK,CAACwF,MAAM,CAACrD,KAAK,EAAE;UAC3C;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIyO,MAAM,IAAID,KAAK,EAAE;YACnB/B,aAAa,CAAChJ,GAAG,CAAC,GAAG,IAAI;UAC3B;QACF,CAAC,MAAM,IAAIgL,MAAM,IAAI,CAACD,KAAK,EAAE;UAC3Bhf,eAAe,CAACgb,sBAAsB,CAAC3M,KAAK,EAAEvJ,GAAG,CAAC;QACpD,CAAC,MAAM,IAAI,CAACma,MAAM,IAAID,KAAK,EAAE;UAC3Bhf,eAAe,CAAC4Z,oBAAoB,CAACvL,KAAK,EAAEvJ,GAAG,CAAC;QAClD,CAAC,MAAM,IAAIma,MAAM,IAAID,KAAK,EAAE;UAC1Bhf,eAAe,CAACkf,oBAAoB,CAAC7Q,KAAK,EAAEvJ,GAAG,EAAE6Z,OAAO,CAAC;QAC3D;MACF;MACA,OAAO1B,aAAa;IACtB;IAAC,OA5CDY,oBAAoB;EAAA;EAAA5M,MAAA,CA8CdsM,qBAAqB;IAA3B,SAAMA,qBAAqBA,CAACzY,GAAG,EAAEiY,GAAG,EAAEvV,YAAY;MAAA,IAAAkX,cAAA,EAAAC,OAAA,EAAA1B,aAAA,EAAAkC,GAAA,EAAAC,aAAA,EAAAnL,GAAA,EAAA5F,KAAA,EAAA0Q,UAAA,EAAAC,KAAA,EAAAC,MAAA;MAAA,OAAApP,mBAAA,CAAAyC,KAAA;QAAA,SAAA+M,uBAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7M,IAAA,GAAA6M,SAAA,CAAArN,IAAA;YAAA;cAE1CyM,cAAc,GAAG,IAAI,CAACF,uBAAuB,CAAC1Z,GAAG,EAAEiY,GAAG,EAAEvV,YAAY,CAAC;cAErEmX,OAAO,GAAG1U,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;cAChC9E,eAAe,CAAC4e,OAAO,CAAC9Z,GAAG,EAAEiY,GAAG,EAAE;gBAACvV,YAAY,EAAZA;cAAY,CAAC,CAAC;cAE3CyV,aAAa,GAAG,CAAC,CAAC;cAAAkC,GAAA,MAAAC,aAAA,GACNnf,MAAM,CAACuD,IAAI,CAAC,IAAI,CAAC2Q,OAAO,CAAC;YAAA;cAAA,MAAAgL,GAAA,GAAAC,aAAA,CAAAre,MAAA;gBAAAue,SAAA,CAAArN,IAAA;gBAAA;cAAA;cAAhCgC,GAAG,GAAAmL,aAAA,CAAAD,GAAA;cACN9Q,KAAK,GAAG,IAAI,CAAC8F,OAAO,CAACF,GAAG,CAAC;cAAA,KAE3B5F,KAAK,CAACyF,KAAK;gBAAAwL,SAAA,CAAArN,IAAA;gBAAA;cAAA;cAAA,OAAAqN,SAAA,CAAA5M,MAAA;YAAA;cAITqM,UAAU,GAAG1Q,KAAK,CAAC1M,OAAO,CAACsV,eAAe,CAACnS,GAAG,CAAC;cAC/Cka,KAAK,GAAGD,UAAU,CAACxa,MAAM;cACzB0a,MAAM,GAAGP,cAAc,CAACzK,GAAG,CAAC;cAElC,IAAI+K,KAAK,IAAI3Q,KAAK,CAACsF,SAAS,IAAIoL,UAAU,CAACpY,QAAQ,KAAKlF,SAAS,EAAE;gBACjE4M,KAAK,CAACsF,SAAS,CAACgD,GAAG,CAAC7R,GAAG,CAAC4J,GAAG,EAAEqQ,UAAU,CAACpY,QAAQ,CAAC;cACnD;cAAC,MAEG0H,KAAK,CAACwF,MAAM,CAACtD,IAAI,IAAIlC,KAAK,CAACwF,MAAM,CAACrD,KAAK;gBAAA8O,SAAA,CAAArN,IAAA;gBAAA;cAAA;cACzC;cACA;cACA;cACA;cACA;cACA;cACA;cACA,IAAIgN,MAAM,IAAID,KAAK,EAAE;gBACnB/B,aAAa,CAAChJ,GAAG,CAAC,GAAG,IAAI;cAC3B;cAACqL,SAAA,CAAArN,IAAA;cAAA;YAAA;cAAA,MACQgN,MAAM,IAAI,CAACD,KAAK;gBAAAM,SAAA,CAAArN,IAAA;gBAAA;cAAA;cAAAqN,SAAA,CAAArN,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CACnB7Y,eAAe,CAACsb,uBAAuB,CAACjN,KAAK,EAAEvJ,GAAG,CAAC;YAAA;cAAAwa,SAAA,CAAArN,IAAA;cAAA;YAAA;cAAA,MAChD,CAACgN,MAAM,IAAID,KAAK;gBAAAM,SAAA,CAAArN,IAAA;gBAAA;cAAA;cAAAqN,SAAA,CAAArN,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CACnB7Y,eAAe,CAACoa,qBAAqB,CAAC/L,KAAK,EAAEvJ,GAAG,CAAC;YAAA;cAAAwa,SAAA,CAAArN,IAAA;cAAA;YAAA;cAAA,MAC9CgN,MAAM,IAAID,KAAK;gBAAAM,SAAA,CAAArN,IAAA;gBAAA;cAAA;cAAAqN,SAAA,CAAArN,IAAA;cAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAClB7Y,eAAe,CAACuf,qBAAqB,CAAClR,KAAK,EAAEvJ,GAAG,EAAE6Z,OAAO,CAAC;YAAA;cAAAQ,GAAA;cAAAG,SAAA,CAAArN,IAAA;cAAA;YAAA;cAAA,OAAAqN,SAAA,CAAA5M,MAAA,WAG7DuK,aAAa;YAAA;YAAA;cAAA,OAAAqC,SAAA,CAAAzM,IAAA;UAAA;QAAA;QAAA,OAAAwM,sBAAA;MAAA,uBAAA1M,OAAA;IAAA;IACrB,OA3CK4K,qBAAqB;EAAA,IA6C3B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAAtM,MAAA,CACA4I,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAACxL,KAAK,EAAEmR,UAAU,EAAE;MACnC,IAAI,IAAI,CAACnL,MAAM,EAAE;QACf;QACA;QACA;QACAhG,KAAK,CAACyF,KAAK,GAAG,IAAI;QAClB;MACF;MAEA,IAAI,CAAC,IAAI,CAACO,MAAM,IAAI,CAACmL,UAAU,EAAE;QAC/BA,UAAU,GAAGnR,KAAK,CAAC+F,OAAO;MAC5B;MAEA,IAAI/F,KAAK,CAACsF,SAAS,EAAE;QACnBtF,KAAK,CAACsF,SAAS,CAACiD,KAAK,CAAC,CAAC;MACzB;MAEAvI,KAAK,CAAC+F,OAAO,GAAG/F,KAAK,CAACwF,MAAM,CAACvC,cAAc,CAAC;QAC1CqC,SAAS,EAAEtF,KAAK,CAACsF,SAAS;QAC1BpC,OAAO,EAAElD,KAAK,CAACkD;MACjB,CAAC,CAAC;MAEF,IAAI,CAAC,IAAI,CAAC8C,MAAM,EAAE;QAChBrU,eAAe,CAACwb,iBAAiB,CAC/BnN,KAAK,CAACkD,OAAO,EACbiO,UAAU,EACVnR,KAAK,CAAC+F,OAAO,EACb/F,KAAK,EACL;UAAC0F,YAAY,EAAE1F,KAAK,CAAC0F;QAAY,CACnC,CAAC;MACH;IACF;IAAC,OA/BD8F,iBAAiB;EAAA;EAAA5I,MAAA,CAiCjBsI,aAAa;IAAb,SAAAA,aAAaA,CAACxC,EAAE,EAAEjS,GAAG,EAAE;MACrB;MACA,IAAI,CAAC,IAAI,CAACsT,eAAe,EAAE;QACzB;MACF;;MAEA;MACA;MACA;MACA,IAAI,IAAI,CAACA,eAAe,CAACkB,GAAG,CAACvC,EAAE,CAAC,EAAE;QAChC;MACF;MAEA,IAAI,CAACqB,eAAe,CAACzB,GAAG,CAACI,EAAE,EAAE9M,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC,CAAC;IAChD;IAAC,OAdDyU,aAAa;EAAA;EAAA,OAAAvZ,eAAA;AAAA;AAiBfA,eAAe,CAAC8P,MAAM,GAAGA,MAAM;AAE/B9P,eAAe,CAACqV,aAAa,GAAGA,aAAa;;AAE7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACArV,eAAe,CAACyf,sBAAsB;EACpC,SAAAA,uBAAA,EAA0B;IAAA,IAAAC,OAAA;IAAA,IAAd1X,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IACtB,IAAM0X,oBAAoB,GACxB3X,OAAO,CAAC4X,SAAS,IACjB5f,eAAe,CAACyT,kCAAkC,CAACzL,OAAO,CAAC4X,SAAS,CACrE;IAED,IAAI1gB,MAAM,CAAC+C,IAAI,CAAC+F,OAAO,EAAE,SAAS,CAAC,EAAE;MACnC,IAAI,CAACuJ,OAAO,GAAGvJ,OAAO,CAACuJ,OAAO;MAE9B,IAAIvJ,OAAO,CAAC4X,SAAS,IAAI5X,OAAO,CAACuJ,OAAO,KAAKoO,oBAAoB,EAAE;QACjE,MAAM3e,KAAK,CAAC,yCAAyC,CAAC;MACxD;IACF,CAAC,MAAM,IAAIgH,OAAO,CAAC4X,SAAS,EAAE;MAC5B,IAAI,CAACrO,OAAO,GAAGoO,oBAAoB;IACrC,CAAC,MAAM;MACL,MAAM3e,KAAK,CAAC,mCAAmC,CAAC;IAClD;IAEA,IAAM4e,SAAS,GAAG5X,OAAO,CAAC4X,SAAS,IAAI,CAAC,CAAC;IAEzC,IAAI,IAAI,CAACrO,OAAO,EAAE;MAChB,IAAI,CAACsO,IAAI,GAAG,IAAIC,WAAW,CAAC3G,OAAO,CAAC4G,WAAW,CAAC;MAChD,IAAI,CAACC,WAAW,GAAG;QACjBpO,WAAW,EAAE,SAAAA,CAACmF,EAAE,EAAExI,MAAM,EAAE0Q,MAAM,EAAK;UACnC;UACA,IAAMna,GAAG,GAAAgT,aAAA,KAAQvJ,MAAM,CAAE;UAEzBzJ,GAAG,CAAC4J,GAAG,GAAGqI,EAAE;UAEZ,IAAI6I,SAAS,CAAChO,WAAW,EAAE;YACzBgO,SAAS,CAAChO,WAAW,CAAC3P,IAAI,CAACyd,OAAI,EAAE3I,EAAE,EAAE9M,KAAK,CAAC+K,KAAK,CAACzG,MAAM,CAAC,EAAE0Q,MAAM,CAAC;UACnE;;UAEA;UACA,IAAIW,SAAS,CAACxO,KAAK,EAAE;YACnBwO,SAAS,CAACxO,KAAK,CAACnP,IAAI,CAACyd,OAAI,EAAE3I,EAAE,EAAE9M,KAAK,CAAC+K,KAAK,CAACzG,MAAM,CAAC,CAAC;UACrD;;UAEA;UACA;UACA;UACAmR,OAAI,CAACG,IAAI,CAACI,SAAS,CAAClJ,EAAE,EAAEjS,GAAG,EAAEma,MAAM,IAAI,IAAI,CAAC;QAC9C,CAAC;QACDnN,WAAW,EAAE,SAAAA,CAACiF,EAAE,EAAEkI,MAAM,EAAK;UAC3B,IAAIW,SAAS,CAAC9N,WAAW,EAAE;YACzB8N,SAAS,CAAC9N,WAAW,CAAC7P,IAAI,CAACyd,OAAI,EAAE3I,EAAE,EAAEkI,MAAM,CAAC;UAC9C;UAEAS,OAAI,CAACG,IAAI,CAACK,UAAU,CAACnJ,EAAE,EAAEkI,MAAM,IAAI,IAAI,CAAC;QAC1C;MACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACY,IAAI,GAAG,IAAI7f,eAAe,CAAC4T,MAAM,CAAD,CAAC;MACtC,IAAI,CAACoM,WAAW,GAAG;QACjB5O,KAAK,EAAE,SAAAA,CAAC2F,EAAE,EAAExI,MAAM,EAAK;UACrB;UACA,IAAMzJ,GAAG,GAAAgT,aAAA,KAAQvJ,MAAM,CAAE;UAEzB,IAAIqR,SAAS,CAACxO,KAAK,EAAE;YACnBwO,SAAS,CAACxO,KAAK,CAACnP,IAAI,CAACyd,OAAI,EAAE3I,EAAE,EAAE9M,KAAK,CAAC+K,KAAK,CAACzG,MAAM,CAAC,CAAC;UACrD;UAEAzJ,GAAG,CAAC4J,GAAG,GAAGqI,EAAE;UAEZ2I,OAAI,CAACG,IAAI,CAAClJ,GAAG,CAACI,EAAE,EAAGjS,GAAG,CAAC;QACzB;MACF,CAAC;IACH;;IAEA;IACA;IACA,IAAI,CAACkb,WAAW,CAACnO,OAAO,GAAG,UAACkF,EAAE,EAAExI,MAAM,EAAK;MACzC,IAAMzJ,GAAG,GAAG4a,OAAI,CAACG,IAAI,CAACnJ,GAAG,CAACK,EAAE,CAAC;MAE7B,IAAI,CAACjS,GAAG,EAAE;QACR,MAAM,IAAI9D,KAAK,8BAA4B+V,EAAI,CAAC;MAClD;MAEA,IAAI6I,SAAS,CAAC/N,OAAO,EAAE;QACrB+N,SAAS,CAAC/N,OAAO,CAAC5P,IAAI,CAACyd,OAAI,EAAE3I,EAAE,EAAE9M,KAAK,CAAC+K,KAAK,CAACzG,MAAM,CAAC,CAAC;MACvD;MAEA4R,YAAY,CAACC,YAAY,CAACtb,GAAG,EAAEyJ,MAAM,CAAC;IACxC,CAAC;IAED,IAAI,CAACyR,WAAW,CAAC3O,OAAO,GAAG,UAAA0F,EAAE,EAAI;MAC/B,IAAI6I,SAAS,CAACvO,OAAO,EAAE;QACrBuO,SAAS,CAACvO,OAAO,CAACpP,IAAI,CAACyd,OAAI,EAAE3I,EAAE,CAAC;MAClC;MAEA2I,OAAI,CAACG,IAAI,CAACrF,MAAM,CAACzD,EAAE,CAAC;IACtB,CAAC;EACH;EAAC,OAAA0I,sBAAA;AAAA,GACF;AAEDzf,eAAe,CAAC4T,MAAM,0BAAAyM,OAAA;EACpB,SAAAzM,OAAA,EAAc;IAAA,OACZyM,OAAA,CAAApe,IAAA,OAAMkX,OAAO,CAAC4G,WAAW,EAAE5G,OAAO,CAACmH,OAAO,CAAC;EAC7C;EAACzI,cAAA,CAAAjE,MAAA,EAAAyM,OAAA;EAAA,OAAAzM,MAAA;AAAA,EAH2C2M,KAAK,CAIlD;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAvgB,eAAe,CAAC6Q,aAAa,GAAG,UAAAC,SAAS,EAAI;EAC3C,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,IAAI;EACb;;EAEA;EACA,IAAIA,SAAS,CAAC0P,oBAAoB,EAAE;IAClC,OAAO1P,SAAS;EAClB;EAEA,IAAM2P,OAAO,GAAG,SAAAA,CAAA3b,GAAG,EAAI;IACrB,IAAI,CAAC5F,MAAM,CAAC+C,IAAI,CAAC6C,GAAG,EAAE,KAAK,CAAC,EAAE;MAC5B;MACA;MACA,MAAM,IAAI9D,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IAEA,IAAM+V,EAAE,GAAGjS,GAAG,CAAC4J,GAAG;;IAElB;IACA;IACA,IAAMgS,WAAW,GAAG3P,OAAO,CAAC4P,WAAW,CAAC;MAAA,OAAM7P,SAAS,CAAChM,GAAG,CAAC;IAAA,EAAC;IAE7D,IAAI,CAAC9E,eAAe,CAACsD,cAAc,CAACod,WAAW,CAAC,EAAE;MAChD,MAAM,IAAI1f,KAAK,CAAC,8BAA8B,CAAC;IACjD;IAEA,IAAI9B,MAAM,CAAC+C,IAAI,CAACye,WAAW,EAAE,KAAK,CAAC,EAAE;MACnC,IAAI,CAACzW,KAAK,CAAC6Q,MAAM,CAAC4F,WAAW,CAAChS,GAAG,EAAEqI,EAAE,CAAC,EAAE;QACtC,MAAM,IAAI/V,KAAK,CAAC,gDAAgD,CAAC;MACnE;IACF,CAAC,MAAM;MACL0f,WAAW,CAAChS,GAAG,GAAGqI,EAAE;IACtB;IAEA,OAAO2J,WAAW;EACpB,CAAC;EAEDD,OAAO,CAACD,oBAAoB,GAAG,IAAI;EAEnC,OAAOC,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACAzgB,eAAe,CAAC4gB,aAAa,GAAG,UAACC,GAAG,EAAEC,KAAK,EAAE3f,KAAK,EAAK;EACrD,IAAI4f,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAGF,KAAK,CAAC/f,MAAM;EAExB,OAAOigB,KAAK,GAAG,CAAC,EAAE;IAChB,IAAMC,SAAS,GAAGjY,IAAI,CAACkY,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;IAEvC,IAAIH,GAAG,CAAC1f,KAAK,EAAE2f,KAAK,CAACC,KAAK,GAAGE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;MAC7CF,KAAK,IAAIE,SAAS,GAAG,CAAC;MACtBD,KAAK,IAAIC,SAAS,GAAG,CAAC;IACxB,CAAC,MAAM;MACLD,KAAK,GAAGC,SAAS;IACnB;EACF;EAEA,OAAOF,KAAK;AACd,CAAC;AAED/gB,eAAe,CAACmhB,yBAAyB,GAAG,UAAA5S,MAAM,EAAI;EACpD,IAAIA,MAAM,KAAKtO,MAAM,CAACsO,MAAM,CAAC,IAAI1N,KAAK,CAACC,OAAO,CAACyN,MAAM,CAAC,EAAE;IACtD,MAAM9B,cAAc,CAAC,iCAAiC,CAAC;EACzD;EAEAxM,MAAM,CAACuD,IAAI,CAAC+K,MAAM,CAAC,CAAChF,OAAO,CAAC,UAAAqF,OAAO,EAAI;IACrC,IAAIA,OAAO,CAAC/C,KAAK,CAAC,GAAG,CAAC,CAACoB,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpC,MAAMR,cAAc,CAClB,2DACF,CAAC;IACH;IAEA,IAAMtL,KAAK,GAAGoN,MAAM,CAACK,OAAO,CAAC;IAE7B,IAAI5P,OAAA,CAAOmC,KAAK,MAAK,QAAQ,IACzB,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACO,IAAI,CAAC,UAAAgC,GAAG;MAAA,OACxCxE,MAAM,CAAC+C,IAAI,CAACd,KAAK,EAAEuC,GAAG,CAAC;IAAA,CACzB,CAAC,EAAE;MACL,MAAM+I,cAAc,CAClB,0DACF,CAAC;IACH;IAEA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAACQ,QAAQ,CAAC9L,KAAK,CAAC,EAAE;MACxC,MAAMsL,cAAc,CAClB,yDACF,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAzM,eAAe,CAAC2Q,kBAAkB,GAAG,UAAApC,MAAM,EAAI;EAC7CvO,eAAe,CAACmhB,yBAAyB,CAAC5S,MAAM,CAAC;EAEjD,IAAM6S,aAAa,GAAG7S,MAAM,CAACG,GAAG,KAAKjN,SAAS,GAAG,IAAI,GAAG8M,MAAM,CAACG,GAAG;EAClE,IAAM2S,OAAO,GAAGvhB,iBAAiB,CAACyO,MAAM,CAAC;;EAEzC;EACA,IAAMuC,SAAS,GAAG,SAAAA,CAAChM,GAAG,EAAEwc,QAAQ,EAAK;IACnC;IACA,IAAIzgB,KAAK,CAACC,OAAO,CAACgE,GAAG,CAAC,EAAE;MACtB,OAAOA,GAAG,CAACxD,GAAG,CAAC,UAAAigB,MAAM;QAAA,OAAIzQ,SAAS,CAACyQ,MAAM,EAAED,QAAQ,CAAC;MAAA,EAAC;IACvD;IAEA,IAAM/c,MAAM,GAAG8c,OAAO,CAAC1S,SAAS,GAAG,CAAC,CAAC,GAAG1E,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;IAExD7E,MAAM,CAACuD,IAAI,CAAC8d,QAAQ,CAAC,CAAC/X,OAAO,CAAC,UAAA7F,GAAG,EAAI;MACnC,IAAIoB,GAAG,IAAI,IAAI,IAAI,CAAC5F,MAAM,CAAC+C,IAAI,CAAC6C,GAAG,EAAEpB,GAAG,CAAC,EAAE;QACzC;MACF;MAEA,IAAMmL,IAAI,GAAGyS,QAAQ,CAAC5d,GAAG,CAAC;MAE1B,IAAImL,IAAI,KAAK5O,MAAM,CAAC4O,IAAI,CAAC,EAAE;QACzB;QACA,IAAI/J,GAAG,CAACpB,GAAG,CAAC,KAAKzD,MAAM,CAAC6E,GAAG,CAACpB,GAAG,CAAC,CAAC,EAAE;UACjCa,MAAM,CAACb,GAAG,CAAC,GAAGoN,SAAS,CAAChM,GAAG,CAACpB,GAAG,CAAC,EAAEmL,IAAI,CAAC;QACzC;MACF,CAAC,MAAM,IAAIwS,OAAO,CAAC1S,SAAS,EAAE;QAC5B;QACApK,MAAM,CAACb,GAAG,CAAC,GAAGuG,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAACpB,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOa,MAAM,CAACb,GAAG,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAOoB,GAAG,IAAI,IAAI,GAAGP,MAAM,GAAGO,GAAG;EACnC,CAAC;EAED,OAAO,UAAAA,GAAG,EAAI;IACZ,IAAMP,MAAM,GAAGuM,SAAS,CAAChM,GAAG,EAAEuc,OAAO,CAAC3T,IAAI,CAAC;IAE3C,IAAI0T,aAAa,IAAIliB,MAAM,CAAC+C,IAAI,CAAC6C,GAAG,EAAE,KAAK,CAAC,EAAE;MAC5CP,MAAM,CAACmK,GAAG,GAAG5J,GAAG,CAAC4J,GAAG;IACtB;IAEA,IAAI,CAAC0S,aAAa,IAAIliB,MAAM,CAAC+C,IAAI,CAACsC,MAAM,EAAE,KAAK,CAAC,EAAE;MAChD,OAAOA,MAAM,CAACmK,GAAG;IACnB;IAEA,OAAOnK,MAAM;EACf,CAAC;AACH,CAAC;;AAED;AACA;AACAvE,eAAe,CAAC0d,qBAAqB,GAAG,UAAC/T,QAAQ,EAAE6X,QAAQ,EAAK;EAC9D,IAAMC,gBAAgB,GAAG5hB,+BAA+B,CAAC8J,QAAQ,CAAC;EAClE,IAAM+X,QAAQ,GAAG1hB,eAAe,CAAC2hB,kBAAkB,CAACH,QAAQ,CAAC;EAE7D,IAAMI,MAAM,GAAG,CAAC,CAAC;EAEjB,IAAIH,gBAAgB,CAAC/S,GAAG,EAAE;IACxBkT,MAAM,CAAClT,GAAG,GAAG+S,gBAAgB,CAAC/S,GAAG;IACjC,OAAO+S,gBAAgB,CAAC/S,GAAG;EAC7B;;EAEA;EACA;EACA;EACA1O,eAAe,CAAC4e,OAAO,CAACgD,MAAM,EAAE;IAACC,IAAI,EAAEJ;EAAgB,CAAC,CAAC;EACzDzhB,eAAe,CAAC4e,OAAO,CAACgD,MAAM,EAAEJ,QAAQ,EAAE;IAACM,QAAQ,EAAE;EAAI,CAAC,CAAC;EAE3D,IAAIJ,QAAQ,EAAE;IACZ,OAAOE,MAAM;EACf;;EAEA;EACA,IAAMG,WAAW,GAAG9hB,MAAM,CAAC+D,MAAM,CAAC,CAAC,CAAC,EAAEwd,QAAQ,CAAC;EAC/C,IAAII,MAAM,CAAClT,GAAG,EAAE;IACdqT,WAAW,CAACrT,GAAG,GAAGkT,MAAM,CAAClT,GAAG;EAC9B;EAEA,OAAOqT,WAAW;AACpB,CAAC;AAED/hB,eAAe,CAACgiB,YAAY,GAAG,UAACC,IAAI,EAAEC,KAAK,EAAEtC,SAAS,EAAK;EACzD,OAAOO,YAAY,CAACgC,WAAW,CAACF,IAAI,EAAEC,KAAK,EAAEtC,SAAS,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACA;AACA5f,eAAe,CAACwb,iBAAiB,GAAG,UAACjK,OAAO,EAAEiO,UAAU,EAAE4C,UAAU,EAAEC,QAAQ,EAAEra,OAAO;EAAA,OACrFmY,YAAY,CAACmC,gBAAgB,CAAC/Q,OAAO,EAAEiO,UAAU,EAAE4C,UAAU,EAAEC,QAAQ,EAAEra,OAAO,CAAC;AAAA;AAGnFhI,eAAe,CAACuiB,wBAAwB,GAAG,UAAC/C,UAAU,EAAE4C,UAAU,EAAEC,QAAQ,EAAEra,OAAO;EAAA,OACnFmY,YAAY,CAACqC,uBAAuB,CAAChD,UAAU,EAAE4C,UAAU,EAAEC,QAAQ,EAAEra,OAAO,CAAC;AAAA;AAGjFhI,eAAe,CAACyiB,0BAA0B,GAAG,UAACjD,UAAU,EAAE4C,UAAU,EAAEC,QAAQ,EAAEra,OAAO;EAAA,OACrFmY,YAAY,CAACuC,yBAAyB,CAAClD,UAAU,EAAE4C,UAAU,EAAEC,QAAQ,EAAEra,OAAO,CAAC;AAAA;AAGnFhI,eAAe,CAAC2iB,qBAAqB,GAAG,UAACtU,KAAK,EAAEvJ,GAAG,EAAK;EACtD,IAAI,CAACuJ,KAAK,CAACkD,OAAO,EAAE;IAClB,MAAM,IAAIvQ,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0L,KAAK,CAAC+F,OAAO,CAACrT,MAAM,EAAE4B,CAAC,EAAE,EAAE;IAC7C,IAAI0L,KAAK,CAAC+F,OAAO,CAACzR,CAAC,CAAC,KAAKmC,GAAG,EAAE;MAC5B,OAAOnC,CAAC;IACV;EACF;EAEA,MAAM3B,KAAK,CAAC,2BAA2B,CAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAhB,eAAe,CAACqc,qBAAqB,GAAG,UAAA1S,QAAQ,EAAI;EAClD;EACA,IAAI3J,eAAe,CAACsO,aAAa,CAAC3E,QAAQ,CAAC,EAAE;IAC3C,OAAO,CAACA,QAAQ,CAAC;EACnB;EAEA,IAAI,CAACA,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;;EAEA;EACA,IAAIzK,MAAM,CAAC+C,IAAI,CAAC0H,QAAQ,EAAE,KAAK,CAAC,EAAE;IAChC;IACA,IAAI3J,eAAe,CAACsO,aAAa,CAAC3E,QAAQ,CAAC+E,GAAG,CAAC,EAAE;MAC/C,OAAO,CAAC/E,QAAQ,CAAC+E,GAAG,CAAC;IACvB;;IAEA;IACA,IAAI/E,QAAQ,CAAC+E,GAAG,IACT7N,KAAK,CAACC,OAAO,CAAC6I,QAAQ,CAAC+E,GAAG,CAACtN,GAAG,CAAC,IAC/BuI,QAAQ,CAAC+E,GAAG,CAACtN,GAAG,CAACL,MAAM,IACvB4I,QAAQ,CAAC+E,GAAG,CAACtN,GAAG,CAACqB,KAAK,CAACzC,eAAe,CAACsO,aAAa,CAAC,EAAE;MAC5D,OAAO3E,QAAQ,CAAC+E,GAAG,CAACtN,GAAG;IACzB;IAEA,OAAO,IAAI;EACb;;EAEA;EACA;EACA;EACA,IAAIP,KAAK,CAACC,OAAO,CAAC6I,QAAQ,CAACnF,IAAI,CAAC,EAAE;IAChC,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgH,QAAQ,CAACnF,IAAI,CAACzD,MAAM,EAAE,EAAE4B,CAAC,EAAE;MAC7C,IAAMigB,MAAM,GAAG5iB,eAAe,CAACqc,qBAAqB,CAAC1S,QAAQ,CAACnF,IAAI,CAAC7B,CAAC,CAAC,CAAC;MAEtE,IAAIigB,MAAM,EAAE;QACV,OAAOA,MAAM;MACf;IACF;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AAED5iB,eAAe,CAAC4Z,oBAAoB,GAAG,UAACvL,KAAK,EAAEvJ,GAAG,EAAK;EACrD,IAAMyJ,MAAM,GAAGtE,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;EAE/B,OAAOyJ,MAAM,CAACG,GAAG;EAEjB,IAAIL,KAAK,CAACkD,OAAO,EAAE;IACjB,IAAI,CAAClD,KAAK,CAAC2B,MAAM,EAAE;MACjB3B,KAAK,CAACuD,WAAW,CAAC9M,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,EAAE,IAAI,CAAC;MAC5DF,KAAK,CAAC+F,OAAO,CAAC3K,IAAI,CAAC3E,GAAG,CAAC;IACzB,CAAC,MAAM;MACL,IAAMnC,CAAC,GAAG3C,eAAe,CAAC6iB,mBAAmB,CAC3CxU,KAAK,CAAC2B,MAAM,CAACkH,aAAa,CAAC;QAACvD,SAAS,EAAEtF,KAAK,CAACsF;MAAS,CAAC,CAAC,EACxDtF,KAAK,CAAC+F,OAAO,EACbtP,GACF,CAAC;MAED,IAAImN,IAAI,GAAG5D,KAAK,CAAC+F,OAAO,CAACzR,CAAC,GAAG,CAAC,CAAC;MAC/B,IAAIsP,IAAI,EAAE;QACRA,IAAI,GAAGA,IAAI,CAACvD,GAAG;MACjB,CAAC,MAAM;QACLuD,IAAI,GAAG,IAAI;MACb;MAEA5D,KAAK,CAACuD,WAAW,CAAC9M,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,EAAE0D,IAAI,CAAC;IAC9D;IAEA5D,KAAK,CAAC+C,KAAK,CAACtM,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,CAAC;EAClD,CAAC,MAAM;IACLF,KAAK,CAAC+C,KAAK,CAACtM,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,CAAC;IAChDF,KAAK,CAAC+F,OAAO,CAACuC,GAAG,CAAC7R,GAAG,CAAC4J,GAAG,EAAE5J,GAAG,CAAC;EACjC;AACF,CAAC;AAED9E,eAAe,CAACoa,qBAAqB;EAAG,SAAA0I,SAAOzU,KAAK,EAAEvJ,GAAG;IAAA,IAAAyJ,MAAA,EAAA5L,CAAA,EAAAsP,IAAA;IAAA,OAAApC,mBAAA,CAAAyC,KAAA;MAAA,SAAAyQ,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAvQ,IAAA,GAAAuQ,SAAA,CAAA/Q,IAAA;UAAA;YACjD1D,MAAM,GAAGtE,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC;YAE/B,OAAOyJ,MAAM,CAACG,GAAG;YAAC,KAEdL,KAAK,CAACkD,OAAO;cAAAyR,SAAA,CAAA/Q,IAAA;cAAA;YAAA;YAAA,IACV5D,KAAK,CAAC2B,MAAM;cAAAgT,SAAA,CAAA/Q,IAAA;cAAA;YAAA;YAAA+Q,SAAA,CAAA/Q,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CACTxK,KAAK,CAACuD,WAAW,CAAC9M,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,EAAE,IAAI,CAAC;UAAA;YAClEF,KAAK,CAAC+F,OAAO,CAAC3K,IAAI,CAAC3E,GAAG,CAAC;YAACke,SAAA,CAAA/Q,IAAA;YAAA;UAAA;YAElBtP,CAAC,GAAG3C,eAAe,CAAC6iB,mBAAmB,CAC3CxU,KAAK,CAAC2B,MAAM,CAACkH,aAAa,CAAC;cAACvD,SAAS,EAAEtF,KAAK,CAACsF;YAAS,CAAC,CAAC,EACxDtF,KAAK,CAAC+F,OAAO,EACbtP,GACF,CAAC;YAEGmN,IAAI,GAAG5D,KAAK,CAAC+F,OAAO,CAACzR,CAAC,GAAG,CAAC,CAAC;YAC/B,IAAIsP,IAAI,EAAE;cACRA,IAAI,GAAGA,IAAI,CAACvD,GAAG;YACjB,CAAC,MAAM;cACLuD,IAAI,GAAG,IAAI;YACb;YAAC+Q,SAAA,CAAA/Q,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEKxK,KAAK,CAACuD,WAAW,CAAC9M,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,EAAE0D,IAAI,CAAC;UAAA;YAAA+Q,SAAA,CAAA/Q,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAG9DxK,KAAK,CAAC+C,KAAK,CAACtM,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,CAAC;UAAA;YAAAyU,SAAA,CAAA/Q,IAAA;YAAA;UAAA;YAAA+Q,SAAA,CAAA/Q,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEhDxK,KAAK,CAAC+C,KAAK,CAACtM,GAAG,CAAC4J,GAAG,EAAEL,KAAK,CAAC0F,YAAY,CAACxF,MAAM,CAAC,CAAC;UAAA;YACtDF,KAAK,CAAC+F,OAAO,CAACuC,GAAG,CAAC7R,GAAG,CAAC4J,GAAG,EAAE5J,GAAG,CAAC;UAAC;UAAA;YAAA,OAAAke,SAAA,CAAAnQ,IAAA;QAAA;MAAA;MAAA,OAAAkQ,SAAA;IAAA,uBAAApQ,OAAA;EAAA;EAEnC,OAAAmQ,QAAA;AAAA;AAED9iB,eAAe,CAAC6iB,mBAAmB,GAAG,UAAChC,GAAG,EAAEC,KAAK,EAAE3f,KAAK,EAAK;EAC3D,IAAI2f,KAAK,CAAC/f,MAAM,KAAK,CAAC,EAAE;IACtB+f,KAAK,CAACrX,IAAI,CAACtI,KAAK,CAAC;IACjB,OAAO,CAAC;EACV;EAEA,IAAMwB,CAAC,GAAG3C,eAAe,CAAC4gB,aAAa,CAACC,GAAG,EAAEC,KAAK,EAAE3f,KAAK,CAAC;EAE1D2f,KAAK,CAACmC,MAAM,CAACtgB,CAAC,EAAE,CAAC,EAAExB,KAAK,CAAC;EAEzB,OAAOwB,CAAC;AACV,CAAC;AAED3C,eAAe,CAAC2hB,kBAAkB,GAAG,UAAA5E,GAAG,EAAI;EAC1C,IAAI2E,QAAQ,GAAG,KAAK;EACpB,IAAIwB,SAAS,GAAG,KAAK;EAErBjjB,MAAM,CAACuD,IAAI,CAACuZ,GAAG,CAAC,CAACxT,OAAO,CAAC,UAAA7F,GAAG,EAAI;IAC9B,IAAIA,GAAG,CAACyE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5BuZ,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM;MACLwB,SAAS,GAAG,IAAI;IAClB;EACF,CAAC,CAAC;EAEF,IAAIxB,QAAQ,IAAIwB,SAAS,EAAE;IACzB,MAAM,IAAIliB,KAAK,CACb,qEACF,CAAC;EACH;EAEA,OAAO0gB,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA1hB,eAAe,CAACsD,cAAc,GAAG,UAAA6G,CAAC,EAAI;EACpC,OAAOA,CAAC,IAAInK,eAAe,CAACkC,EAAE,CAACC,KAAK,CAACgI,CAAC,CAAC,KAAK,CAAC;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAnK,eAAe,CAAC4e,OAAO,GAAG,UAAC9Z,GAAG,EAAE0c,QAAQ,EAAmB;EAAA,IAAjBxZ,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EACpD,IAAI,CAACjI,eAAe,CAACsD,cAAc,CAACke,QAAQ,CAAC,EAAE;IAC7C,MAAM/U,cAAc,CAAC,4BAA4B,CAAC;EACpD;;EAEA;EACA+U,QAAQ,GAAGvX,KAAK,CAAC+K,KAAK,CAACwM,QAAQ,CAAC;EAEhC,IAAM2B,UAAU,GAAG1jB,gBAAgB,CAAC+hB,QAAQ,CAAC;EAC7C,IAAMI,MAAM,GAAGuB,UAAU,GAAGlZ,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC,GAAG0c,QAAQ;EAEvD,IAAI2B,UAAU,EAAE;IACd;IACAljB,MAAM,CAACuD,IAAI,CAACge,QAAQ,CAAC,CAACjY,OAAO,CAAC,UAAAwD,QAAQ,EAAI;MACxC;MACA,IAAMqW,WAAW,GAAGpb,OAAO,CAAC8Z,QAAQ,IAAI/U,QAAQ,KAAK,cAAc;MACnE,IAAMsW,OAAO,GAAGC,SAAS,CAACF,WAAW,GAAG,MAAM,GAAGrW,QAAQ,CAAC;MAC1D,IAAMnM,OAAO,GAAG4gB,QAAQ,CAACzU,QAAQ,CAAC;MAElC,IAAI,CAACsW,OAAO,EAAE;QACZ,MAAM5W,cAAc,iCAA+BM,QAAU,CAAC;MAChE;MAEA9M,MAAM,CAACuD,IAAI,CAAC5C,OAAO,CAAC,CAAC2I,OAAO,CAAC,UAAAga,OAAO,EAAI;QACtC,IAAMlf,GAAG,GAAGzD,OAAO,CAAC2iB,OAAO,CAAC;QAE5B,IAAIA,OAAO,KAAK,EAAE,EAAE;UAClB,MAAM9W,cAAc,CAAC,oCAAoC,CAAC;QAC5D;QAEA,IAAM+W,QAAQ,GAAGD,OAAO,CAAC1X,KAAK,CAAC,GAAG,CAAC;QAEnC,IAAI,CAAC2X,QAAQ,CAAC/gB,KAAK,CAAC8F,OAAO,CAAC,EAAE;UAC5B,MAAMkE,cAAc,CAClB,sBAAoB8W,OAAO,wCAC3B,uBACF,CAAC;QACH;QAEA,IAAME,MAAM,GAAGC,aAAa,CAAC9B,MAAM,EAAE4B,QAAQ,EAAE;UAC7Chc,YAAY,EAAEQ,OAAO,CAACR,YAAY;UAClCmc,WAAW,EAAE5W,QAAQ,KAAK,SAAS;UACnC6W,QAAQ,EAAEC,mBAAmB,CAAC9W,QAAQ;QACxC,CAAC,CAAC;QAEFsW,OAAO,CAACI,MAAM,EAAED,QAAQ,CAACM,GAAG,CAAC,CAAC,EAAEzf,GAAG,EAAEkf,OAAO,EAAE3B,MAAM,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI9c,GAAG,CAAC4J,GAAG,IAAI,CAACzE,KAAK,CAAC6Q,MAAM,CAAChW,GAAG,CAAC4J,GAAG,EAAEkT,MAAM,CAAClT,GAAG,CAAC,EAAE;MACjD,MAAMjC,cAAc,CAClB,uDAAoD3H,GAAG,CAAC4J,GAAG,iBAC3D,mEAAmE,gBAC1DkT,MAAM,CAAClT,GAAG,QACrB,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAI5J,GAAG,CAAC4J,GAAG,IAAI8S,QAAQ,CAAC9S,GAAG,IAAI,CAACzE,KAAK,CAAC6Q,MAAM,CAAChW,GAAG,CAAC4J,GAAG,EAAE8S,QAAQ,CAAC9S,GAAG,CAAC,EAAE;MACnE,MAAMjC,cAAc,CAClB,kDAA+C3H,GAAG,CAAC4J,GAAG,6BAC5C8S,QAAQ,CAAC9S,GAAG,SACxB,CAAC;IACH;;IAEA;IACAuK,wBAAwB,CAACuI,QAAQ,CAAC;EACpC;;EAEA;EACAvhB,MAAM,CAACuD,IAAI,CAACsB,GAAG,CAAC,CAACyE,OAAO,CAAC,UAAA7F,GAAG,EAAI;IAC9B;IACA;IACA;IACA,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB,OAAOoB,GAAG,CAACpB,GAAG,CAAC;IACjB;EACF,CAAC,CAAC;EAEFzD,MAAM,CAACuD,IAAI,CAACoe,MAAM,CAAC,CAACrY,OAAO,CAAC,UAAA7F,GAAG,EAAI;IACjCoB,GAAG,CAACpB,GAAG,CAAC,GAAGke,MAAM,CAACle,GAAG,CAAC;EACxB,CAAC,CAAC;AACJ,CAAC;AAED1D,eAAe,CAACoT,0BAA0B,GAAG,UAACS,MAAM,EAAEkQ,gBAAgB,EAAK;EACzE,IAAMjT,SAAS,GAAG+C,MAAM,CAACZ,YAAY,CAAC,CAAC,IAAK,UAAAnO,GAAG;IAAA,OAAIA,GAAG;EAAA,CAAC;EACvD,IAAIkf,UAAU,GAAG,CAAC,CAACD,gBAAgB,CAACnP,iBAAiB;EAErD,IAAIqP,uBAAuB;EAC3B,IAAIjkB,eAAe,CAACkkB,2BAA2B,CAACH,gBAAgB,CAAC,EAAE;IACjE;IACA;IACA;IACA;IACA,IAAMI,OAAO,GAAG,CAACJ,gBAAgB,CAACK,WAAW;IAE7CH,uBAAuB,GAAG;MACxBrS,WAAW,WAAAA,CAACmF,EAAE,EAAExI,MAAM,EAAE0Q,MAAM,EAAE;QAC9B,IAAMoF,KAAK,GAAGL,UAAU,IAAI,EAAED,gBAAgB,CAACO,OAAO,IAAIP,gBAAgB,CAAC3S,KAAK,CAAC;QACjF,IAAIiT,KAAK,EAAE;UACT;QACF;QAEA,IAAMvf,GAAG,GAAGgM,SAAS,CAAC7Q,MAAM,CAAC+D,MAAM,CAACuK,MAAM,EAAE;UAACG,GAAG,EAAEqI;QAAE,CAAC,CAAC,CAAC;QAEvD,IAAIgN,gBAAgB,CAACO,OAAO,EAAE;UAC5BP,gBAAgB,CAACO,OAAO,CACpBxf,GAAG,EACHqf,OAAO,GACDlF,MAAM,GACF,IAAI,CAACY,IAAI,CAAC/U,OAAO,CAACmU,MAAM,CAAC,GACzB,IAAI,CAACY,IAAI,CAAC1K,IAAI,CAAC,CAAC,GACpB,CAAC,CAAC,EACR8J,MACJ,CAAC;QACH,CAAC,MAAM;UACL8E,gBAAgB,CAAC3S,KAAK,CAACtM,GAAG,CAAC;QAC7B;MACF,CAAC;MACD+M,OAAO,WAAAA,CAACkF,EAAE,EAAExI,MAAM,EAAE;QAElB,IAAI,EAAEwV,gBAAgB,CAACQ,SAAS,IAAIR,gBAAgB,CAAClS,OAAO,CAAC,EAAE;UAC7D;QACF;QAEA,IAAI/M,GAAG,GAAGmF,KAAK,CAAC+K,KAAK,CAAC,IAAI,CAAC6K,IAAI,CAACnJ,GAAG,CAACK,EAAE,CAAC,CAAC;QACxC,IAAI,CAACjS,GAAG,EAAE;UACR,MAAM,IAAI9D,KAAK,8BAA4B+V,EAAI,CAAC;QAClD;QAEA,IAAMyN,MAAM,GAAG1T,SAAS,CAAC7G,KAAK,CAAC+K,KAAK,CAAClQ,GAAG,CAAC,CAAC;QAE1Cqb,YAAY,CAACC,YAAY,CAACtb,GAAG,EAAEyJ,MAAM,CAAC;QAEtC,IAAIwV,gBAAgB,CAACQ,SAAS,EAAE;UAC9BR,gBAAgB,CAACQ,SAAS,CACtBzT,SAAS,CAAChM,GAAG,CAAC,EACd0f,MAAM,EACNL,OAAO,GAAG,IAAI,CAACtE,IAAI,CAAC/U,OAAO,CAACiM,EAAE,CAAC,GAAG,CAAC,CACvC,CAAC;QACH,CAAC,MAAM;UACLgN,gBAAgB,CAAClS,OAAO,CAACf,SAAS,CAAChM,GAAG,CAAC,EAAE0f,MAAM,CAAC;QAClD;MACF,CAAC;MACD1S,WAAW,WAAAA,CAACiF,EAAE,EAAEkI,MAAM,EAAE;QACtB,IAAI,CAAC8E,gBAAgB,CAACU,OAAO,EAAE;UAC7B;QACF;QAEA,IAAMC,IAAI,GAAGP,OAAO,GAAG,IAAI,CAACtE,IAAI,CAAC/U,OAAO,CAACiM,EAAE,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI4N,EAAE,GAAGR,OAAO,GACVlF,MAAM,GACF,IAAI,CAACY,IAAI,CAAC/U,OAAO,CAACmU,MAAM,CAAC,GACzB,IAAI,CAACY,IAAI,CAAC1K,IAAI,CAAC,CAAC,GACpB,CAAC,CAAC;;QAER;QACA;QACA,IAAIwP,EAAE,GAAGD,IAAI,EAAE;UACb,EAAEC,EAAE;QACN;QAEAZ,gBAAgB,CAACU,OAAO,CACpB3T,SAAS,CAAC7G,KAAK,CAAC+K,KAAK,CAAC,IAAI,CAAC6K,IAAI,CAACnJ,GAAG,CAACK,EAAE,CAAC,CAAC,CAAC,EACzC2N,IAAI,EACJC,EAAE,EACF1F,MAAM,IAAI,IACd,CAAC;MACH,CAAC;MACD5N,OAAO,WAAAA,CAAC0F,EAAE,EAAE;QACV,IAAI,EAAEgN,gBAAgB,CAACa,SAAS,IAAIb,gBAAgB,CAAC1S,OAAO,CAAC,EAAE;UAC7D;QACF;;QAEA;QACA;QACA,IAAMvM,GAAG,GAAGgM,SAAS,CAAC,IAAI,CAAC+O,IAAI,CAACnJ,GAAG,CAACK,EAAE,CAAC,CAAC;QAExC,IAAIgN,gBAAgB,CAACa,SAAS,EAAE;UAC9Bb,gBAAgB,CAACa,SAAS,CAAC9f,GAAG,EAAEqf,OAAO,GAAG,IAAI,CAACtE,IAAI,CAAC/U,OAAO,CAACiM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,CAAC,MAAM;UACLgN,gBAAgB,CAAC1S,OAAO,CAACvM,GAAG,CAAC;QAC/B;MACF;IACF,CAAC;EACH,CAAC,MAAM;IACLmf,uBAAuB,GAAG;MACxB7S,KAAK,WAAAA,CAAC2F,EAAE,EAAExI,MAAM,EAAE;QAChB,IAAI,CAACyV,UAAU,IAAID,gBAAgB,CAAC3S,KAAK,EAAE;UACzC2S,gBAAgB,CAAC3S,KAAK,CAACN,SAAS,CAAC7Q,MAAM,CAAC+D,MAAM,CAACuK,MAAM,EAAE;YAACG,GAAG,EAAEqI;UAAE,CAAC,CAAC,CAAC,CAAC;QACrE;MACF,CAAC;MACDlF,OAAO,WAAAA,CAACkF,EAAE,EAAExI,MAAM,EAAE;QAClB,IAAIwV,gBAAgB,CAAClS,OAAO,EAAE;UAC5B,IAAM2S,MAAM,GAAG,IAAI,CAAC3E,IAAI,CAACnJ,GAAG,CAACK,EAAE,CAAC;UAChC,IAAMjS,GAAG,GAAGmF,KAAK,CAAC+K,KAAK,CAACwP,MAAM,CAAC;UAE/BrE,YAAY,CAACC,YAAY,CAACtb,GAAG,EAAEyJ,MAAM,CAAC;UAEtCwV,gBAAgB,CAAClS,OAAO,CACpBf,SAAS,CAAChM,GAAG,CAAC,EACdgM,SAAS,CAAC7G,KAAK,CAAC+K,KAAK,CAACwP,MAAM,CAAC,CACjC,CAAC;QACH;MACF,CAAC;MACDnT,OAAO,WAAAA,CAAC0F,EAAE,EAAE;QACV,IAAIgN,gBAAgB,CAAC1S,OAAO,EAAE;UAC5B0S,gBAAgB,CAAC1S,OAAO,CAACP,SAAS,CAAC,IAAI,CAAC+O,IAAI,CAACnJ,GAAG,CAACK,EAAE,CAAC,CAAC,CAAC;QACxD;MACF;IACF,CAAC;EACH;EAEA,IAAM8N,cAAc,GAAG,IAAI7kB,eAAe,CAACyf,sBAAsB,CAAC;IAChEG,SAAS,EAAEqE;EACb,CAAC,CAAC;;EAEF;EACA;EACA;EACAY,cAAc,CAAC7E,WAAW,CAAC8E,YAAY,GAAG,IAAI;EAC9C,IAAM1P,MAAM,GAAGvB,MAAM,CAACN,cAAc,CAACsR,cAAc,CAAC7E,WAAW,EAC3D;IAAE+E,oBAAoB,EAAE;EAAK,CAAC,CAAC;;EAEnC;EACA,IAAMC,aAAa,GAAG,SAAAA,CAACC,CAAC,EAAK;IAAA,IAAAC,iBAAA;IAC3B,IAAID,CAAC,CAAC3P,OAAO,EAAE0O,UAAU,GAAG,KAAK,CAAC,KAC7B,CAAAkB,iBAAA,GAAAD,CAAC,CAAC1P,cAAc,cAAA2P,iBAAA,uBAAhBA,iBAAA,CAAkBtP,IAAI,CAAC;MAAA,OAAOoO,UAAU,GAAG,KAAK;IAAA,CAAC,CAAC;EACzD,CAAC;EACD;EACA;EACA,IAAInN,MAAM,CAACsO,UAAU,CAAC/P,MAAM,CAAC,EAAE;IAC7BA,MAAM,CAACQ,IAAI,CAACoP,aAAa,CAAC;EAC5B,CAAC,MAAM;IACLA,aAAa,CAAC5P,MAAM,CAAC;EACvB;EACA,OAAOA,MAAM;AACf,CAAC;AAEDpV,eAAe,CAACkkB,2BAA2B,GAAG,UAAAtE,SAAS,EAAI;EACzD,IAAIA,SAAS,CAACxO,KAAK,IAAIwO,SAAS,CAAC0E,OAAO,EAAE;IACxC,MAAM,IAAItjB,KAAK,CAAC,kDAAkD,CAAC;EACrE;EAEA,IAAI4e,SAAS,CAAC/N,OAAO,IAAI+N,SAAS,CAAC2E,SAAS,EAAE;IAC5C,MAAM,IAAIvjB,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,IAAI4e,SAAS,CAACvO,OAAO,IAAIuO,SAAS,CAACgF,SAAS,EAAE;IAC5C,MAAM,IAAI5jB,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,OAAO,CAAC,EACN4e,SAAS,CAAC0E,OAAO,IACjB1E,SAAS,CAAC2E,SAAS,IACnB3E,SAAS,CAAC6E,OAAO,IACjB7E,SAAS,CAACgF,SAAS,CACpB;AACH,CAAC;AAED5kB,eAAe,CAACyT,kCAAkC,GAAG,UAAAmM,SAAS,EAAI;EAChE,IAAIA,SAAS,CAACxO,KAAK,IAAIwO,SAAS,CAAChO,WAAW,EAAE;IAC5C,MAAM,IAAI5Q,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,OAAO,CAAC,EAAE4e,SAAS,CAAChO,WAAW,IAAIgO,SAAS,CAAC9N,WAAW,CAAC;AAC3D,CAAC;AAED9R,eAAe,CAACgb,sBAAsB,GAAG,UAAC3M,KAAK,EAAEvJ,GAAG,EAAK;EACvD,IAAIuJ,KAAK,CAACkD,OAAO,EAAE;IACjB,IAAM5O,CAAC,GAAG3C,eAAe,CAAC2iB,qBAAqB,CAACtU,KAAK,EAAEvJ,GAAG,CAAC;IAE3DuJ,KAAK,CAACgD,OAAO,CAACvM,GAAG,CAAC4J,GAAG,CAAC;IACtBL,KAAK,CAAC+F,OAAO,CAAC6O,MAAM,CAACtgB,CAAC,EAAE,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL,IAAMoU,EAAE,GAAGjS,GAAG,CAAC4J,GAAG,CAAC,CAAE;;IAErBL,KAAK,CAACgD,OAAO,CAACvM,GAAG,CAAC4J,GAAG,CAAC;IACtBL,KAAK,CAAC+F,OAAO,CAACoG,MAAM,CAACzD,EAAE,CAAC;EAC1B;AACF,CAAC;AAED/W,eAAe,CAACsb,uBAAuB;EAAG,SAAA8J,SAAO/W,KAAK,EAAEvJ,GAAG;IAAA,IAAAnC,CAAA,EAAAoU,EAAA;IAAA,OAAAlH,mBAAA,CAAAyC,KAAA;MAAA,SAAA+S,UAAAC,UAAA;QAAA,kBAAAA,UAAA,CAAA7S,IAAA,GAAA6S,UAAA,CAAArT,IAAA;UAAA;YAAA,KACrD5D,KAAK,CAACkD,OAAO;cAAA+T,UAAA,CAAArT,IAAA;cAAA;YAAA;YACTtP,CAAC,GAAG3C,eAAe,CAAC2iB,qBAAqB,CAACtU,KAAK,EAAEvJ,GAAG,CAAC;YAAAwgB,UAAA,CAAArT,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAErDxK,KAAK,CAACgD,OAAO,CAACvM,GAAG,CAAC4J,GAAG,CAAC;UAAA;YAC5BL,KAAK,CAAC+F,OAAO,CAAC6O,MAAM,CAACtgB,CAAC,EAAE,CAAC,CAAC;YAAC2iB,UAAA,CAAArT,IAAA;YAAA;UAAA;YAErB8E,EAAE,GAAGjS,GAAG,CAAC4J,GAAG,EAAG;YAAA4W,UAAA,CAAArT,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAEfxK,KAAK,CAACgD,OAAO,CAACvM,GAAG,CAAC4J,GAAG,CAAC;UAAA;YAC5BL,KAAK,CAAC+F,OAAO,CAACoG,MAAM,CAACzD,EAAE,CAAC;UAAC;UAAA;YAAA,OAAAuO,UAAA,CAAAzS,IAAA;QAAA;MAAA;MAAA,OAAAwS,SAAA;IAAA,uBAAA1S,OAAA;EAAA;EAE5B,OAAAyS,QAAA;AAAA;;AAED;AACAplB,eAAe,CAACsO,aAAa,GAAG,UAAA3E,QAAQ;EAAA,OACtC,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,OAAOA,QAAQ,KAAK,QAAQ,IAC5BA,QAAQ,YAAYwP,OAAO,CAACC,QAAQ;AAAA;;AAGtC;AACApZ,eAAe,CAACmQ,4BAA4B,GAAG,UAAAxG,QAAQ;EAAA,OACrD3J,eAAe,CAACsO,aAAa,CAAC3E,QAAQ,CAAC,IACvC3J,eAAe,CAACsO,aAAa,CAAC3E,QAAQ,IAAIA,QAAQ,CAAC+E,GAAG,CAAC,IACvDzO,MAAM,CAACuD,IAAI,CAACmG,QAAQ,CAAC,CAAC5I,MAAM,KAAK,CAAC;AAAA;AAGpCf,eAAe,CAACkf,oBAAoB,GAAG,UAAC7Q,KAAK,EAAEvJ,GAAG,EAAE6Z,OAAO,EAAK;EAC9D,IAAI,CAAC1U,KAAK,CAAC6Q,MAAM,CAAChW,GAAG,CAAC4J,GAAG,EAAEiQ,OAAO,CAACjQ,GAAG,CAAC,EAAE;IACvC,MAAM,IAAI1N,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EAEA,IAAM+S,YAAY,GAAG1F,KAAK,CAAC0F,YAAY;EACvC,IAAMwR,aAAa,GAAGpF,YAAY,CAACqF,iBAAiB,CAClDzR,YAAY,CAACjP,GAAG,CAAC,EACjBiP,YAAY,CAAC4K,OAAO,CACtB,CAAC;EAED,IAAI,CAACtQ,KAAK,CAACkD,OAAO,EAAE;IAClB,IAAItR,MAAM,CAACuD,IAAI,CAAC+hB,aAAa,CAAC,CAACxkB,MAAM,EAAE;MACrCsN,KAAK,CAACwD,OAAO,CAAC/M,GAAG,CAAC4J,GAAG,EAAE6W,aAAa,CAAC;MACrClX,KAAK,CAAC+F,OAAO,CAACuC,GAAG,CAAC7R,GAAG,CAAC4J,GAAG,EAAE5J,GAAG,CAAC;IACjC;IAEA;EACF;EAEA,IAAM2gB,OAAO,GAAGzlB,eAAe,CAAC2iB,qBAAqB,CAACtU,KAAK,EAAEvJ,GAAG,CAAC;EAEjE,IAAI7E,MAAM,CAACuD,IAAI,CAAC+hB,aAAa,CAAC,CAACxkB,MAAM,EAAE;IACrCsN,KAAK,CAACwD,OAAO,CAAC/M,GAAG,CAAC4J,GAAG,EAAE6W,aAAa,CAAC;EACvC;EAEA,IAAI,CAAClX,KAAK,CAAC2B,MAAM,EAAE;IACjB;EACF;;EAEA;EACA3B,KAAK,CAAC+F,OAAO,CAAC6O,MAAM,CAACwC,OAAO,EAAE,CAAC,CAAC;EAEhC,IAAMC,OAAO,GAAG1lB,eAAe,CAAC6iB,mBAAmB,CACjDxU,KAAK,CAAC2B,MAAM,CAACkH,aAAa,CAAC;IAACvD,SAAS,EAAEtF,KAAK,CAACsF;EAAS,CAAC,CAAC,EACxDtF,KAAK,CAAC+F,OAAO,EACbtP,GACF,CAAC;EAED,IAAI2gB,OAAO,KAAKC,OAAO,EAAE;IACvB,IAAIzT,IAAI,GAAG5D,KAAK,CAAC+F,OAAO,CAACsR,OAAO,GAAG,CAAC,CAAC;IACrC,IAAIzT,IAAI,EAAE;MACRA,IAAI,GAAGA,IAAI,CAACvD,GAAG;IACjB,CAAC,MAAM;MACLuD,IAAI,GAAG,IAAI;IACb;IAEA5D,KAAK,CAACyD,WAAW,IAAIzD,KAAK,CAACyD,WAAW,CAAChN,GAAG,CAAC4J,GAAG,EAAEuD,IAAI,CAAC;EACvD;AACF,CAAC;AAEDjS,eAAe,CAACuf,qBAAqB;EAAG,SAAAoG,SAAOtX,KAAK,EAAEvJ,GAAG,EAAE6Z,OAAO;IAAA,IAAA5K,YAAA,EAAAwR,aAAA,EAAAE,OAAA,EAAAC,OAAA,EAAAzT,IAAA;IAAA,OAAApC,mBAAA,CAAAyC,KAAA;MAAA,SAAAsT,UAAAC,UAAA;QAAA,kBAAAA,UAAA,CAAApT,IAAA,GAAAoT,UAAA,CAAA5T,IAAA;UAAA;YAAA,IAC3DhI,KAAK,CAAC6Q,MAAM,CAAChW,GAAG,CAAC4J,GAAG,EAAEiQ,OAAO,CAACjQ,GAAG,CAAC;cAAAmX,UAAA,CAAA5T,IAAA;cAAA;YAAA;YAAA,MAC/B,IAAIjR,KAAK,CAAC,2CAA2C,CAAC;UAAA;YAGxD+S,YAAY,GAAG1F,KAAK,CAAC0F,YAAY;YACjCwR,aAAa,GAAGpF,YAAY,CAACqF,iBAAiB,CAClDzR,YAAY,CAACjP,GAAG,CAAC,EACjBiP,YAAY,CAAC4K,OAAO,CACtB,CAAC;YAAA,IAEItQ,KAAK,CAACkD,OAAO;cAAAsU,UAAA,CAAA5T,IAAA;cAAA;YAAA;YAAA,KACZhS,MAAM,CAACuD,IAAI,CAAC+hB,aAAa,CAAC,CAACxkB,MAAM;cAAA8kB,UAAA,CAAA5T,IAAA;cAAA;YAAA;YAAA4T,UAAA,CAAA5T,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAC7BxK,KAAK,CAACwD,OAAO,CAAC/M,GAAG,CAAC4J,GAAG,EAAE6W,aAAa,CAAC;UAAA;YAC3ClX,KAAK,CAAC+F,OAAO,CAACuC,GAAG,CAAC7R,GAAG,CAAC4J,GAAG,EAAE5J,GAAG,CAAC;UAAC;YAAA,OAAA+gB,UAAA,CAAAnT,MAAA;UAAA;YAM9B+S,OAAO,GAAGzlB,eAAe,CAAC2iB,qBAAqB,CAACtU,KAAK,EAAEvJ,GAAG,CAAC;YAAA,KAE7D7E,MAAM,CAACuD,IAAI,CAAC+hB,aAAa,CAAC,CAACxkB,MAAM;cAAA8kB,UAAA,CAAA5T,IAAA;cAAA;YAAA;YAAA4T,UAAA,CAAA5T,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAC7BxK,KAAK,CAACwD,OAAO,CAAC/M,GAAG,CAAC4J,GAAG,EAAE6W,aAAa,CAAC;UAAA;YAAA,IAGxClX,KAAK,CAAC2B,MAAM;cAAA6V,UAAA,CAAA5T,IAAA;cAAA;YAAA;YAAA,OAAA4T,UAAA,CAAAnT,MAAA;UAAA;YAIjB;YACArE,KAAK,CAAC+F,OAAO,CAAC6O,MAAM,CAACwC,OAAO,EAAE,CAAC,CAAC;YAE1BC,OAAO,GAAG1lB,eAAe,CAAC6iB,mBAAmB,CACjDxU,KAAK,CAAC2B,MAAM,CAACkH,aAAa,CAAC;cAACvD,SAAS,EAAEtF,KAAK,CAACsF;YAAS,CAAC,CAAC,EACxDtF,KAAK,CAAC+F,OAAO,EACbtP,GACF,CAAC;YAAA,MAEG2gB,OAAO,KAAKC,OAAO;cAAAG,UAAA,CAAA5T,IAAA;cAAA;YAAA;YACjBA,IAAI,GAAG5D,KAAK,CAAC+F,OAAO,CAACsR,OAAO,GAAG,CAAC,CAAC;YACrC,IAAIzT,IAAI,EAAE;cACRA,IAAI,GAAGA,IAAI,CAACvD,GAAG;YACjB,CAAC,MAAM;cACLuD,IAAI,GAAG,IAAI;YACb;YAAC4T,UAAA,CAAAzH,EAAA,GAED/P,KAAK,CAACyD,WAAW;YAAA,KAAA+T,UAAA,CAAAzH,EAAA;cAAAyH,UAAA,CAAA5T,IAAA;cAAA;YAAA;YAAA4T,UAAA,CAAA5T,IAAA;YAAA,OAAApC,mBAAA,CAAAgJ,KAAA,CAAUxK,KAAK,CAACyD,WAAW,CAAChN,GAAG,CAAC4J,GAAG,EAAEuD,IAAI,CAAC;UAAA;UAAA;YAAA,OAAA4T,UAAA,CAAAhT,IAAA;QAAA;MAAA;MAAA,OAAA+S,SAAA;IAAA,uBAAAjT,OAAA;EAAA;EAE9D,OAAAgT,QAAA;AAAA;AAED,IAAMrC,SAAS,GAAG;EAChBwC,YAAY,WAAAA,CAACrC,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IAC/B,IAAIrF,OAAA,CAAOqF,GAAG,MAAK,QAAQ,IAAInF,MAAM,CAAC+C,IAAI,CAACoC,GAAG,EAAE,OAAO,CAAC,EAAE;MACxD,IAAIA,GAAG,CAACvC,KAAK,KAAK,MAAM,EAAE;QACxB,MAAM2K,cAAc,CAClB,yDAAyD,GACzD,wBAAwB,EACxB;UAACE,KAAK,EAALA;QAAK,CACR,CAAC;MACH;IACF,CAAC,MAAM,IAAItI,GAAG,KAAK,IAAI,EAAE;MACvB,MAAMoI,cAAc,CAAC,+BAA+B,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IAChE;IAEA8W,MAAM,CAAC9W,KAAK,CAAC,GAAG,IAAIoZ,IAAI,CAAC,CAAC;EAC5B,CAAC;EACDC,IAAI,WAAAA,CAACvC,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMoI,cAAc,CAAC,wCAAwC,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAI8W,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAC9W,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE,KAAK,EAALA;QAAK,CACR,CAAC;MACH;MAEA8W,MAAM,CAAC9W,KAAK,CAAC,IAAItI,GAAG;IACtB,CAAC,MAAM;MACLof,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG;IACrB;EACF,CAAC;EACD4hB,IAAI,WAAAA,CAACxC,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMoI,cAAc,CAAC,wCAAwC,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAI8W,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAC9W,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE,KAAK,EAALA;QAAK,CACR,CAAC;MACH;MAEA,IAAI8W,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG,EAAE;QACvBof,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG;MACrB;IACF,CAAC,MAAM;MACLof,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG;IACrB;EACF,CAAC;EACD6hB,IAAI,WAAAA,CAACzC,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMoI,cAAc,CAAC,wCAAwC,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAI8W,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAC9W,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE,KAAK,EAALA;QAAK,CACR,CAAC;MACH;MAEA,IAAI8W,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG,EAAE;QACvBof,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG;MACrB;IACF,CAAC,MAAM;MACLof,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG;IACrB;EACF,CAAC;EACD8hB,IAAI,WAAAA,CAAC1C,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMoI,cAAc,CAAC,wCAAwC,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IACzE;IAEA,IAAIA,KAAK,IAAI8W,MAAM,EAAE;MACnB,IAAI,OAAOA,MAAM,CAAC9W,KAAK,CAAC,KAAK,QAAQ,EAAE;QACrC,MAAMF,cAAc,CAClB,0CAA0C,EAC1C;UAACE,KAAK,EAALA;QAAK,CACR,CAAC;MACH;MAEA8W,MAAM,CAAC9W,KAAK,CAAC,IAAItI,GAAG;IACtB,CAAC,MAAM;MACLof,MAAM,CAAC9W,KAAK,CAAC,GAAG,CAAC;IACnB;EACF,CAAC;EACDyZ,OAAO,WAAAA,CAAC3C,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAEkf,OAAO,EAAEze,GAAG,EAAE;IACxC;IACA,IAAIye,OAAO,KAAKlf,GAAG,EAAE;MACnB,MAAMoI,cAAc,CAAC,wCAAwC,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IACzE;IAEA,IAAI8W,MAAM,KAAK,IAAI,EAAE;MACnB,MAAMhX,cAAc,CAAC,8BAA8B,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IAC/D;IAEA,IAAI,OAAOtI,GAAG,KAAK,QAAQ,EAAE;MAC3B,MAAMoI,cAAc,CAAC,iCAAiC,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IAClE;IAEA,IAAItI,GAAG,CAAC4I,QAAQ,CAAC,IAAI,CAAC,EAAE;MACtB;MACA;MACA,MAAMR,cAAc,CAClB,mEAAmE,EACnE;QAACE,KAAK,EAALA;MAAK,CACR,CAAC;IACH;IAEA,IAAI8W,MAAM,KAAKhiB,SAAS,EAAE;MACxB;IACF;IAEA,IAAM2M,MAAM,GAAGqV,MAAM,CAAC9W,KAAK,CAAC;IAE5B,OAAO8W,MAAM,CAAC9W,KAAK,CAAC;IAEpB,IAAM6W,QAAQ,GAAGnf,GAAG,CAACwH,KAAK,CAAC,GAAG,CAAC;IAC/B,IAAMwa,OAAO,GAAG3C,aAAa,CAAC5e,GAAG,EAAE0e,QAAQ,EAAE;MAACG,WAAW,EAAE;IAAI,CAAC,CAAC;IAEjE,IAAI0C,OAAO,KAAK,IAAI,EAAE;MACpB,MAAM5Z,cAAc,CAAC,8BAA8B,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IAC/D;IAEA0Z,OAAO,CAAC7C,QAAQ,CAACM,GAAG,CAAC,CAAC,CAAC,GAAG1V,MAAM;EAClC,CAAC;EACDyT,IAAI,WAAAA,CAAC4B,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACvB,IAAIof,MAAM,KAAKxjB,MAAM,CAACwjB,MAAM,CAAC,EAAE;MAAE;MAC/B,IAAM7W,KAAK,GAAGH,cAAc,CAC1B,yCAAyC,EACzC;QAACE,KAAK,EAALA;MAAK,CACR,CAAC;MACDC,KAAK,CAAC0Z,gBAAgB,GAAG,IAAI;MAC7B,MAAM1Z,KAAK;IACb;IAEA,IAAI6W,MAAM,KAAK,IAAI,EAAE;MACnB,IAAM7W,MAAK,GAAGH,cAAc,CAAC,6BAA6B,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;MACpEC,MAAK,CAAC0Z,gBAAgB,GAAG,IAAI;MAC7B,MAAM1Z,MAAK;IACb;IAEAqM,wBAAwB,CAAC5U,GAAG,CAAC;IAE7Bof,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG;EACrB,CAAC;EACDkiB,YAAY,WAAAA,CAAC9C,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IAC/B;EAAA,CACD;EACDmiB,MAAM,WAAAA,CAAC/C,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACzB,IAAIof,MAAM,KAAKhiB,SAAS,EAAE;MACxB,IAAIgiB,MAAM,YAAY5iB,KAAK,EAAE;QAC3B,IAAI8L,KAAK,IAAI8W,MAAM,EAAE;UACnBA,MAAM,CAAC9W,KAAK,CAAC,GAAG,IAAI;QACtB;MACF,CAAC,MAAM;QACL,OAAO8W,MAAM,CAAC9W,KAAK,CAAC;MACtB;IACF;EACF,CAAC;EACD8Z,KAAK,WAAAA,CAAChD,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACxB,IAAIof,MAAM,CAAC9W,KAAK,CAAC,KAAKlL,SAAS,EAAE;MAC/BgiB,MAAM,CAAC9W,KAAK,CAAC,GAAG,EAAE;IACpB;IAEA,IAAI,EAAE8W,MAAM,CAAC9W,KAAK,CAAC,YAAY9L,KAAK,CAAC,EAAE;MACrC,MAAM4L,cAAc,CAAC,0CAA0C,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IAC3E;IAEA,IAAI,EAAEtI,GAAG,IAAIA,GAAG,CAACqiB,KAAK,CAAC,EAAE;MACvB;MACAzN,wBAAwB,CAAC5U,GAAG,CAAC;MAE7Bof,MAAM,CAAC9W,KAAK,CAAC,CAAClD,IAAI,CAACpF,GAAG,CAAC;MAEvB;IACF;;IAEA;IACA,IAAMsiB,MAAM,GAAGtiB,GAAG,CAACqiB,KAAK;IACxB,IAAI,EAAEC,MAAM,YAAY9lB,KAAK,CAAC,EAAE;MAC9B,MAAM4L,cAAc,CAAC,wBAAwB,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IACzD;IAEAsM,wBAAwB,CAAC0N,MAAM,CAAC;;IAEhC;IACA,IAAIC,QAAQ,GAAGnlB,SAAS;IACxB,IAAI,WAAW,IAAI4C,GAAG,EAAE;MACtB,IAAI,OAAOA,GAAG,CAACwiB,SAAS,KAAK,QAAQ,EAAE;QACrC,MAAMpa,cAAc,CAAC,mCAAmC,EAAE;UAACE,KAAK,EAALA;QAAK,CAAC,CAAC;MACpE;;MAEA;MACA,IAAItI,GAAG,CAACwiB,SAAS,GAAG,CAAC,EAAE;QACrB,MAAMpa,cAAc,CAClB,6CAA6C,EAC7C;UAACE,KAAK,EAALA;QAAK,CACR,CAAC;MACH;MAEAia,QAAQ,GAAGviB,GAAG,CAACwiB,SAAS;IAC1B;;IAEA;IACA,IAAI7a,KAAK,GAAGvK,SAAS;IACrB,IAAI,QAAQ,IAAI4C,GAAG,EAAE;MACnB,IAAI,OAAOA,GAAG,CAACyiB,MAAM,KAAK,QAAQ,EAAE;QAClC,MAAMra,cAAc,CAAC,gCAAgC,EAAE;UAACE,KAAK,EAALA;QAAK,CAAC,CAAC;MACjE;;MAEA;MACAX,KAAK,GAAG3H,GAAG,CAACyiB,MAAM;IACpB;;IAEA;IACA,IAAIC,YAAY,GAAGtlB,SAAS;IAC5B,IAAI4C,GAAG,CAAC2iB,KAAK,EAAE;MACb,IAAIhb,KAAK,KAAKvK,SAAS,EAAE;QACvB,MAAMgL,cAAc,CAAC,qCAAqC,EAAE;UAACE,KAAK,EAALA;QAAK,CAAC,CAAC;MACtE;;MAEA;MACA;MACA;MACA;MACAoa,YAAY,GAAG,IAAI9W,SAAS,CAACK,MAAM,CAACjM,GAAG,CAAC2iB,KAAK,CAAC,CAAC9P,aAAa,CAAC,CAAC;MAE9DyP,MAAM,CAACpd,OAAO,CAAC,UAAAX,OAAO,EAAI;QACxB,IAAI5I,eAAe,CAACkC,EAAE,CAACC,KAAK,CAACyG,OAAO,CAAC,KAAK,CAAC,EAAE;UAC3C,MAAM6D,cAAc,CAClB,8DAA8D,GAC9D,SAAS,EACT;YAACE,KAAK,EAALA;UAAK,CACR,CAAC;QACH;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIia,QAAQ,KAAKnlB,SAAS,EAAE;MAC1BklB,MAAM,CAACpd,OAAO,CAAC,UAAAX,OAAO,EAAI;QACxB6a,MAAM,CAAC9W,KAAK,CAAC,CAAClD,IAAI,CAACb,OAAO,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MAAA,IAAAqe,aAAA;MACL,IAAMC,eAAe,GAAG,CAACN,QAAQ,EAAE,CAAC,CAAC;MAErCD,MAAM,CAACpd,OAAO,CAAC,UAAAX,OAAO,EAAI;QACxBse,eAAe,CAACzd,IAAI,CAACb,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,CAAAqe,aAAA,GAAAxD,MAAM,CAAC9W,KAAK,CAAC,EAACsW,MAAM,CAAA3Y,KAAA,CAAA2c,aAAA,EAAIC,eAAe,CAAC;IAC1C;;IAEA;IACA,IAAIH,YAAY,EAAE;MAChBtD,MAAM,CAAC9W,KAAK,CAAC,CAAC8B,IAAI,CAACsY,YAAY,CAAC;IAClC;;IAEA;IACA,IAAI/a,KAAK,KAAKvK,SAAS,EAAE;MACvB,IAAIuK,KAAK,KAAK,CAAC,EAAE;QACfyX,MAAM,CAAC9W,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;MACtB,CAAC,MAAM,IAAIX,KAAK,GAAG,CAAC,EAAE;QACpByX,MAAM,CAAC9W,KAAK,CAAC,GAAG8W,MAAM,CAAC9W,KAAK,CAAC,CAACX,KAAK,CAACA,KAAK,CAAC;MAC5C,CAAC,MAAM;QACLyX,MAAM,CAAC9W,KAAK,CAAC,GAAG8W,MAAM,CAAC9W,KAAK,CAAC,CAACX,KAAK,CAAC,CAAC,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EACDmb,QAAQ,WAAAA,CAAC1D,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IAC3B,IAAI,EAAErF,OAAA,CAAOqF,GAAG,MAAK,QAAQ,IAAIA,GAAG,YAAYxD,KAAK,CAAC,EAAE;MACtD,MAAM4L,cAAc,CAAC,mDAAmD,CAAC;IAC3E;IAEAwM,wBAAwB,CAAC5U,GAAG,CAAC;IAE7B,IAAMsiB,MAAM,GAAGlD,MAAM,CAAC9W,KAAK,CAAC;IAE5B,IAAIga,MAAM,KAAKllB,SAAS,EAAE;MACxBgiB,MAAM,CAAC9W,KAAK,CAAC,GAAGtI,GAAG;IACrB,CAAC,MAAM,IAAI,EAAEsiB,MAAM,YAAY9lB,KAAK,CAAC,EAAE;MACrC,MAAM4L,cAAc,CAClB,6CAA6C,EAC7C;QAACE,KAAK,EAALA;MAAK,CACR,CAAC;IACH,CAAC,MAAM;MACLga,MAAM,CAACld,IAAI,CAAAa,KAAA,CAAXqc,MAAM,EAAA9nB,kBAAA,CAASwF,GAAG,EAAC;IACrB;EACF,CAAC;EACD+iB,SAAS,WAAAA,CAAC3D,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IAC5B,IAAIgjB,MAAM,GAAG,KAAK;IAElB,IAAIroB,OAAA,CAAOqF,GAAG,MAAK,QAAQ,EAAE;MAC3B;MACA,IAAMb,IAAI,GAAGvD,MAAM,CAACuD,IAAI,CAACa,GAAG,CAAC;MAC7B,IAAIb,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;QACvB6jB,MAAM,GAAG,IAAI;MACf;IACF;IAEA,IAAMC,MAAM,GAAGD,MAAM,GAAGhjB,GAAG,CAACqiB,KAAK,GAAG,CAACriB,GAAG,CAAC;IAEzC4U,wBAAwB,CAACqO,MAAM,CAAC;IAEhC,IAAMC,KAAK,GAAG9D,MAAM,CAAC9W,KAAK,CAAC;IAC3B,IAAI4a,KAAK,KAAK9lB,SAAS,EAAE;MACvBgiB,MAAM,CAAC9W,KAAK,CAAC,GAAG2a,MAAM;IACxB,CAAC,MAAM,IAAI,EAAEC,KAAK,YAAY1mB,KAAK,CAAC,EAAE;MACpC,MAAM4L,cAAc,CAClB,8CAA8C,EAC9C;QAACE,KAAK,EAALA;MAAK,CACR,CAAC;IACH,CAAC,MAAM;MACL2a,MAAM,CAAC/d,OAAO,CAAC,UAAApI,KAAK,EAAI;QACtB,IAAIomB,KAAK,CAAC7lB,IAAI,CAAC,UAAAkH,OAAO;UAAA,OAAI5I,eAAe,CAACkC,EAAE,CAACiH,MAAM,CAAChI,KAAK,EAAEyH,OAAO,CAAC;QAAA,EAAC,EAAE;UACpE;QACF;QAEA2e,KAAK,CAAC9d,IAAI,CAACtI,KAAK,CAAC;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;EACDqmB,IAAI,WAAAA,CAAC/D,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACvB,IAAIof,MAAM,KAAKhiB,SAAS,EAAE;MACxB;IACF;IAEA,IAAMgmB,KAAK,GAAGhE,MAAM,CAAC9W,KAAK,CAAC;IAE3B,IAAI8a,KAAK,KAAKhmB,SAAS,EAAE;MACvB;IACF;IAEA,IAAI,EAAEgmB,KAAK,YAAY5mB,KAAK,CAAC,EAAE;MAC7B,MAAM4L,cAAc,CAAC,yCAAyC,EAAE;QAACE,KAAK,EAALA;MAAK,CAAC,CAAC;IAC1E;IAEA,IAAI,OAAOtI,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG,CAAC,EAAE;MACtCojB,KAAK,CAACxE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACpB,CAAC,MAAM;MACLwE,KAAK,CAAC3D,GAAG,CAAC,CAAC;IACb;EACF,CAAC;EACD4D,KAAK,WAAAA,CAACjE,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACxB,IAAIof,MAAM,KAAKhiB,SAAS,EAAE;MACxB;IACF;IAEA,IAAMkmB,MAAM,GAAGlE,MAAM,CAAC9W,KAAK,CAAC;IAC5B,IAAIgb,MAAM,KAAKlmB,SAAS,EAAE;MACxB;IACF;IAEA,IAAI,EAAEkmB,MAAM,YAAY9mB,KAAK,CAAC,EAAE;MAC9B,MAAM4L,cAAc,CAClB,kDAAkD,EAClD;QAACE,KAAK,EAALA;MAAK,CACR,CAAC;IACH;IAEA,IAAIib,GAAG;IACP,IAAIvjB,GAAG,IAAI,IAAI,IAAIrF,OAAA,CAAOqF,GAAG,MAAK,QAAQ,IAAI,EAAEA,GAAG,YAAYxD,KAAK,CAAC,EAAE;MACrE;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA,IAAMc,OAAO,GAAG,IAAIsO,SAAS,CAACC,OAAO,CAAC7L,GAAG,CAAC;MAE1CujB,GAAG,GAAGD,MAAM,CAAClkB,MAAM,CAAC,UAAAmF,OAAO;QAAA,OAAI,CAACjH,OAAO,CAACsV,eAAe,CAACrO,OAAO,CAAC,CAACrE,MAAM;MAAA,EAAC;IAC1E,CAAC,MAAM;MACLqjB,GAAG,GAAGD,MAAM,CAAClkB,MAAM,CAAC,UAAAmF,OAAO;QAAA,OAAI,CAAC5I,eAAe,CAACkC,EAAE,CAACiH,MAAM,CAACP,OAAO,EAAEvE,GAAG,CAAC;MAAA,EAAC;IAC1E;IAEAof,MAAM,CAAC9W,KAAK,CAAC,GAAGib,GAAG;EACrB,CAAC;EACDC,QAAQ,WAAAA,CAACpE,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IAC3B,IAAI,EAAErF,OAAA,CAAOqF,GAAG,MAAK,QAAQ,IAAIA,GAAG,YAAYxD,KAAK,CAAC,EAAE;MACtD,MAAM4L,cAAc,CAClB,mDAAmD,EACnD;QAACE,KAAK,EAALA;MAAK,CACR,CAAC;IACH;IAEA,IAAI8W,MAAM,KAAKhiB,SAAS,EAAE;MACxB;IACF;IAEA,IAAMkmB,MAAM,GAAGlE,MAAM,CAAC9W,KAAK,CAAC;IAE5B,IAAIgb,MAAM,KAAKlmB,SAAS,EAAE;MACxB;IACF;IAEA,IAAI,EAAEkmB,MAAM,YAAY9mB,KAAK,CAAC,EAAE;MAC9B,MAAM4L,cAAc,CAClB,kDAAkD,EAClD;QAACE,KAAK,EAALA;MAAK,CACR,CAAC;IACH;IAEA8W,MAAM,CAAC9W,KAAK,CAAC,GAAGgb,MAAM,CAAClkB,MAAM,CAAC,UAAA2K,MAAM;MAAA,OAClC,CAAC/J,GAAG,CAAC3C,IAAI,CAAC,UAAAkH,OAAO;QAAA,OAAI5I,eAAe,CAACkC,EAAE,CAACiH,MAAM,CAACiF,MAAM,EAAExF,OAAO,CAAC;MAAA,EAAC;IAAA,CAClE,CAAC;EACH,CAAC;EACDkf,IAAI,WAAAA,CAACrE,MAAM,EAAE9W,KAAK,EAAEtI,GAAG,EAAE;IACvB;IACA;IACA,MAAMoI,cAAc,CAAC,uBAAuB,EAAE;MAACE,KAAK,EAALA;IAAK,CAAC,CAAC;EACxD,CAAC;EACDob,EAAE,WAAAA,CAAA,EAAG;IACH;IACA;IACA;IACA;EAAA;AAEJ,CAAC;AAED,IAAMlE,mBAAmB,GAAG;EAC1B2D,IAAI,EAAE,IAAI;EACVE,KAAK,EAAE,IAAI;EACXG,QAAQ,EAAE,IAAI;EACdzB,OAAO,EAAE,IAAI;EACbI,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA,IAAMwB,cAAc,GAAG;EACrBC,CAAC,EAAE,kBAAkB;EACrB,GAAG,EAAE,eAAe;EACpB,IAAI,EAAE;AACR,CAAC;;AAED;AACA,SAAShP,wBAAwBA,CAACnU,GAAG,EAAE;EACrC,IAAIA,GAAG,IAAI9F,OAAA,CAAO8F,GAAG,MAAK,QAAQ,EAAE;IAClCyG,IAAI,CAACC,SAAS,CAAC1G,GAAG,EAAE,UAACpB,GAAG,EAAEvC,KAAK,EAAK;MAClC+mB,sBAAsB,CAACxkB,GAAG,CAAC;MAC3B,OAAOvC,KAAK;IACd,CAAC,CAAC;EACJ;AACF;AAEA,SAAS+mB,sBAAsBA,CAACxkB,GAAG,EAAE;EACnC,IAAIkE,KAAK;EACT,IAAI,OAAOlE,GAAG,KAAK,QAAQ,KAAKkE,KAAK,GAAGlE,GAAG,CAACkE,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;IAC/D,MAAM6E,cAAc,UAAQ/I,GAAG,kBAAaskB,cAAc,CAACpgB,KAAK,CAAC,CAAC,CAAC,CAAG,CAAC;EACzE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8b,aAAaA,CAAC5e,GAAG,EAAE0e,QAAQ,EAAgB;EAAA,IAAdxb,OAAO,GAAAC,SAAA,CAAAlH,MAAA,QAAAkH,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;EAChD,IAAIkgB,cAAc,GAAG,KAAK;EAE1B,KAAK,IAAIxlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6gB,QAAQ,CAACziB,MAAM,EAAE4B,CAAC,EAAE,EAAE;IACxC,IAAMylB,IAAI,GAAGzlB,CAAC,KAAK6gB,QAAQ,CAACziB,MAAM,GAAG,CAAC;IACtC,IAAIsnB,OAAO,GAAG7E,QAAQ,CAAC7gB,CAAC,CAAC;IAEzB,IAAI,CAACpD,WAAW,CAACuF,GAAG,CAAC,EAAE;MACrB,IAAIkD,OAAO,CAAC4b,QAAQ,EAAE;QACpB,OAAOniB,SAAS;MAClB;MAEA,IAAMmL,KAAK,GAAGH,cAAc,2BACF4b,OAAO,sBAAiBvjB,GAClD,CAAC;MACD8H,KAAK,CAAC0Z,gBAAgB,GAAG,IAAI;MAC7B,MAAM1Z,KAAK;IACb;IAEA,IAAI9H,GAAG,YAAYjE,KAAK,EAAE;MACxB,IAAImH,OAAO,CAAC2b,WAAW,EAAE;QACvB,OAAO,IAAI;MACb;MAEA,IAAI0E,OAAO,KAAK,GAAG,EAAE;QACnB,IAAIF,cAAc,EAAE;UAClB,MAAM1b,cAAc,CAAC,2CAA2C,CAAC;QACnE;QAEA,IAAI,CAACzE,OAAO,CAACR,YAAY,IAAI,CAACQ,OAAO,CAACR,YAAY,CAACzG,MAAM,EAAE;UACzD,MAAM0L,cAAc,CAClB,iEAAiE,GACjE,OACF,CAAC;QACH;QAEA4b,OAAO,GAAGrgB,OAAO,CAACR,YAAY,CAAC,CAAC,CAAC;QACjC2gB,cAAc,GAAG,IAAI;MACvB,CAAC,MAAM,IAAI3oB,YAAY,CAAC6oB,OAAO,CAAC,EAAE;QAChCA,OAAO,GAAGC,QAAQ,CAACD,OAAO,CAAC;MAC7B,CAAC,MAAM;QACL,IAAIrgB,OAAO,CAAC4b,QAAQ,EAAE;UACpB,OAAOniB,SAAS;QAClB;QAEA,MAAMgL,cAAc,qDACgC4b,OAAO,MAC3D,CAAC;MACH;MAEA,IAAID,IAAI,EAAE;QACR5E,QAAQ,CAAC7gB,CAAC,CAAC,GAAG0lB,OAAO,CAAC,CAAC;MACzB;MAEA,IAAIrgB,OAAO,CAAC4b,QAAQ,IAAIyE,OAAO,IAAIvjB,GAAG,CAAC/D,MAAM,EAAE;QAC7C,OAAOU,SAAS;MAClB;MAEA,OAAOqD,GAAG,CAAC/D,MAAM,GAAGsnB,OAAO,EAAE;QAC3BvjB,GAAG,CAAC2E,IAAI,CAAC,IAAI,CAAC;MAChB;MAEA,IAAI,CAAC2e,IAAI,EAAE;QACT,IAAItjB,GAAG,CAAC/D,MAAM,KAAKsnB,OAAO,EAAE;UAC1BvjB,GAAG,CAAC2E,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,MAAM,IAAIzK,OAAA,CAAO8F,GAAG,CAACujB,OAAO,CAAC,MAAK,QAAQ,EAAE;UAC3C,MAAM5b,cAAc,CAClB,yBAAuB+W,QAAQ,CAAC7gB,CAAC,GAAG,CAAC,CAAC,wBACtC4I,IAAI,CAACC,SAAS,CAAC1G,GAAG,CAACujB,OAAO,CAAC,CAC7B,CAAC;QACH;MACF;IACF,CAAC,MAAM;MACLH,sBAAsB,CAACG,OAAO,CAAC;MAE/B,IAAI,EAAEA,OAAO,IAAIvjB,GAAG,CAAC,EAAE;QACrB,IAAIkD,OAAO,CAAC4b,QAAQ,EAAE;UACpB,OAAOniB,SAAS;QAClB;QAEA,IAAI,CAAC2mB,IAAI,EAAE;UACTtjB,GAAG,CAACujB,OAAO,CAAC,GAAG,CAAC,CAAC;QACnB;MACF;IACF;IAEA,IAAID,IAAI,EAAE;MACR,OAAOtjB,GAAG;IACZ;IAEAA,GAAG,GAAGA,GAAG,CAACujB,OAAO,CAAC;EACpB;;EAEA;AACF,C;;;;;;;;;;;;AC93EA1pB,MAAM,CAACM,MAAM,CAAC;EAAC,WAAQ,SAAAH,CAAA,EAAU;IAAC,OAAOoR,OAAO;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIlQ,eAAe;AAACrB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAACiB,eAAe,GAACjB,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIK,uBAAuB,EAACF,MAAM,EAACS,cAAc;AAAChB,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACQ,uBAAuB,EAAC,SAAAA,CAASL,CAAC,EAAC;IAACK,uBAAuB,GAACL,CAAC;EAAA,CAAC;EAACG,MAAM,EAAC,SAAAA,CAASH,CAAC,EAAC;IAACG,MAAM,GAACH,CAAC;EAAA,CAAC;EAACY,cAAc,EAAC,SAAAA,CAASZ,CAAC,EAAC;IAACY,cAAc,GAACZ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAOjX,IAAMwpB,OAAO,GAAG,EAAAC,oBAAA,GAAAnR,OAAO,CAAC,eAAe,CAAC,cAAAmR,oBAAA,uBAAxBA,oBAAA,CAA0BD,OAAO;EAAA,SAAAE,YAAA;EAAA,OAAAA,WAAA;AAAA,GAAwB;;AAEzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAAA,IACqBvY,OAAO;EAC1B,SAAAA,QAAYvG,QAAQ,EAAE+e,QAAQ,EAAE;IAC9B;IACA;IACA;IACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;IACA,IAAI,CAACniB,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACpB,SAAS,GAAG,KAAK;IACtB;IACA;IACA;IACA,IAAI,CAACgD,SAAS,GAAG,IAAI;IACrB;IACA;IACA,IAAI,CAACwgB,iBAAiB,GAAGnnB,SAAS;IAClC;IACA;IACA;IACA;IACA,IAAI,CAAConB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACpf,QAAQ,CAAC;IAClD;IACA;IACA;IACA,IAAI,CAACpC,SAAS,GAAGmhB,QAAQ;EAC3B;EAAC,IAAAzX,MAAA,GAAAf,OAAA,CAAAhQ,SAAA;EAAA+Q,MAAA,CAEDgG,eAAe;IAAf,SAAAA,eAAeA,CAACnS,GAAG,EAAE;MACnB,IAAIA,GAAG,KAAK7E,MAAM,CAAC6E,GAAG,CAAC,EAAE;QACvB,MAAM9D,KAAK,CAAC,kCAAkC,CAAC;MACjD;MAEA,OAAO,IAAI,CAAC8nB,WAAW,CAAChkB,GAAG,CAAC;IAC9B;IAAC,OANDmS,eAAe;EAAA;EAAAhG,MAAA,CAQfZ,WAAW;IAAX,SAAAA,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAC7J,YAAY;IAC1B;IAAC,OAFD6J,WAAW;EAAA;EAAAY,MAAA,CAIX+X,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAAC5jB,SAAS;IACvB;IAAC,OAFD4jB,QAAQ;EAAA;EAAA/X,MAAA,CAIRgY,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAAC7gB,SAAS;IACvB;IAAC,OAFD6gB,QAAQ;EAAA,IAIR;EACA;EAAA;EAAAhY,MAAA,CACA8X,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAACpf,QAAQ,EAAE;MACzB;MACA,IAAIA,QAAQ,YAAYtE,QAAQ,EAAE;QAChC,IAAI,CAAC+C,SAAS,GAAG,KAAK;QACtB,IAAI,CAACygB,SAAS,GAAGlf,QAAQ;QACzB,IAAI,CAACxE,eAAe,CAAC,EAAE,CAAC;QAExB,OAAO,UAAAL,GAAG;UAAA,OAAK;YAACP,MAAM,EAAE,CAAC,CAACoF,QAAQ,CAAC1H,IAAI,CAAC6C,GAAG;UAAC,CAAC;QAAA,CAAC;MAChD;;MAEA;MACA,IAAI9E,eAAe,CAACsO,aAAa,CAAC3E,QAAQ,CAAC,EAAE;QAC3C,IAAI,CAACkf,SAAS,GAAG;UAACna,GAAG,EAAE/E;QAAQ,CAAC;QAChC,IAAI,CAACxE,eAAe,CAAC,KAAK,CAAC;QAE3B,OAAO,UAAAL,GAAG;UAAA,OAAK;YAACP,MAAM,EAAE0F,KAAK,CAAC6Q,MAAM,CAAChW,GAAG,CAAC4J,GAAG,EAAE/E,QAAQ;UAAC,CAAC;QAAA,CAAC;MAC3D;;MAEA;MACA;MACA;MACA,IAAI,CAACA,QAAQ,IAAIzK,MAAM,CAAC+C,IAAI,CAAC0H,QAAQ,EAAE,KAAK,CAAC,IAAI,CAACA,QAAQ,CAAC+E,GAAG,EAAE;QAC9D,IAAI,CAACtG,SAAS,GAAG,KAAK;QACtB,OAAOzI,cAAc;MACvB;;MAEA;MACA,IAAIkB,KAAK,CAACC,OAAO,CAAC6I,QAAQ,CAAC,IACvBM,KAAK,CAACC,QAAQ,CAACP,QAAQ,CAAC,IACxB,OAAOA,QAAQ,KAAK,SAAS,EAAE;QACjC,MAAM,IAAI3I,KAAK,wBAAsB2I,QAAU,CAAC;MAClD;MAEA,IAAI,CAACkf,SAAS,GAAG5e,KAAK,CAAC+K,KAAK,CAACrL,QAAQ,CAAC;MAEtC,OAAOvK,uBAAuB,CAACuK,QAAQ,EAAE,IAAI,EAAE;QAACpD,MAAM,EAAE;MAAI,CAAC,CAAC;IAChE;IAAC,OApCDwiB,gBAAgB;EAAA,IAsChB;EACA;EAAA;EAAA9X,MAAA,CACAiY,SAAS;IAAT,SAAAA,SAASA,CAAA,EAAG;MACV,OAAOjpB,MAAM,CAACuD,IAAI,CAAC,IAAI,CAACmlB,MAAM,CAAC;IACjC;IAAC,OAFDO,SAAS;EAAA;EAAAjY,MAAA,CAIT9L,eAAe;IAAf,SAAAA,eAAeA,CAACqI,IAAI,EAAE;MACpB,IAAI,CAACmb,MAAM,CAACnb,IAAI,CAAC,GAAG,IAAI;IAC1B;IAAC,OAFDrI,eAAe;EAAA;EAAA,OAAA+K,OAAA;AAAA;AAKjB;AACAlQ,eAAe,CAACkC,EAAE,GAAG;EACnB;EACAC,KAAK,WAAAA,CAACpD,CAAC,EAAE;IACP,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO,CAAC;IACV;IAEA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO,CAAC;IACV;IAEA,IAAI,OAAOA,CAAC,KAAK,SAAS,EAAE;MAC1B,OAAO,CAAC;IACV;IAEA,IAAI8B,KAAK,CAACC,OAAO,CAAC/B,CAAC,CAAC,EAAE;MACpB,OAAO,CAAC;IACV;IAEA,IAAIA,CAAC,KAAK,IAAI,EAAE;MACd,OAAO,EAAE;IACX;;IAEA;IACA,IAAIA,CAAC,YAAYyC,MAAM,EAAE;MACvB,OAAO,EAAE;IACX;IAEA,IAAI,OAAOzC,CAAC,KAAK,UAAU,EAAE;MAC3B,OAAO,EAAE;IACX;IAEA,IAAIA,CAAC,YAAYgnB,IAAI,EAAE;MACrB,OAAO,CAAC;IACV;IAEA,IAAI9b,KAAK,CAACC,QAAQ,CAACnL,CAAC,CAAC,EAAE;MACrB,OAAO,CAAC;IACV;IAEA,IAAIA,CAAC,YAAYoa,OAAO,CAACC,QAAQ,EAAE;MACjC,OAAO,CAAC;IACV;IAEA,IAAIra,CAAC,YAAYwpB,OAAO,EAAE;MACxB,OAAO,CAAC;IACV;;IAEA;IACA,OAAO,CAAC;;IAER;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC;EAED;EACApf,MAAM,WAAAA,CAACtF,CAAC,EAAEC,CAAC,EAAE;IACX,OAAOmG,KAAK,CAAC6Q,MAAM,CAACjX,CAAC,EAAEC,CAAC,EAAE;MAACqlB,iBAAiB,EAAE;IAAI,CAAC,CAAC;EACtD,CAAC;EAED;EACA;EACAC,UAAU,WAAAA,CAACC,CAAC,EAAE;IACZ;IACA;IACA;IACA;IACA,OAAO,CACL,CAAC,CAAC;IAAG;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC,CAAC;IAAG;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC,CAAC;IAAG;IACL,GAAG;IAAE;IACL,CAAC;IAAI;IACL,GAAG;IAAE;IACL,CAAC;IAAI;IACL,CAAC;IAAI;IACL,CAAC,CAAI;IAAA,CACN,CAACA,CAAC,CAAC;EACN,CAAC;EAED;EACA;EACA;EACA;EACA1d,IAAI,WAAAA,CAAC9H,CAAC,EAAEC,CAAC,EAAE;IACT,IAAID,CAAC,KAAKpC,SAAS,EAAE;MACnB,OAAOqC,CAAC,KAAKrC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC;IAEA,IAAIqC,CAAC,KAAKrC,SAAS,EAAE;MACnB,OAAO,CAAC;IACV;IAEA,IAAI6nB,EAAE,GAAGtpB,eAAe,CAACkC,EAAE,CAACC,KAAK,CAAC0B,CAAC,CAAC;IACpC,IAAI0lB,EAAE,GAAGvpB,eAAe,CAACkC,EAAE,CAACC,KAAK,CAAC2B,CAAC,CAAC;IAEpC,IAAM0lB,EAAE,GAAGxpB,eAAe,CAACkC,EAAE,CAACknB,UAAU,CAACE,EAAE,CAAC;IAC5C,IAAMG,EAAE,GAAGzpB,eAAe,CAACkC,EAAE,CAACknB,UAAU,CAACG,EAAE,CAAC;IAE5C,IAAIC,EAAE,KAAKC,EAAE,EAAE;MACb,OAAOD,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;IACzB;;IAEA;IACA;IACA,IAAIH,EAAE,KAAKC,EAAE,EAAE;MACb,MAAMvoB,KAAK,CAAC,qCAAqC,CAAC;IACpD;IAEA,IAAIsoB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACAA,EAAE,GAAGC,EAAE,GAAG,CAAC;MACX1lB,CAAC,GAAGA,CAAC,CAAC6lB,WAAW,CAAC,CAAC;MACnB5lB,CAAC,GAAGA,CAAC,CAAC4lB,WAAW,CAAC,CAAC;IACrB;IAEA,IAAIJ,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACAA,EAAE,GAAGC,EAAE,GAAG,CAAC;MACX1lB,CAAC,GAAG8lB,KAAK,CAAC9lB,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC+lB,OAAO,CAAC,CAAC;MAC9B9lB,CAAC,GAAG6lB,KAAK,CAAC7lB,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC8lB,OAAO,CAAC,CAAC;IAChC;IAEA,IAAIN,EAAE,KAAK,CAAC,EAAE;MAAE;MACd,IAAIzlB,CAAC,YAAY0kB,OAAO,EAAE;QACxB,OAAO1kB,CAAC,CAACgmB,KAAK,CAAC/lB,CAAC,CAAC,CAACgmB,QAAQ,CAAC,CAAC;MAC9B,CAAC,MAAM;QACL,OAAOjmB,CAAC,GAAGC,CAAC;MACd;IACF;IAEA,IAAIylB,EAAE,KAAK,CAAC;MAAE;MACZ,OAAO1lB,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAG,CAAC;IAErC,IAAIwlB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACA,IAAMS,OAAO,GAAG,SAAAA,CAAA3b,MAAM,EAAI;QACxB,IAAM7J,MAAM,GAAG,EAAE;QAEjBtE,MAAM,CAACuD,IAAI,CAAC4K,MAAM,CAAC,CAAC7E,OAAO,CAAC,UAAA7F,GAAG,EAAI;UACjCa,MAAM,CAACkF,IAAI,CAAC/F,GAAG,EAAE0K,MAAM,CAAC1K,GAAG,CAAC,CAAC;QAC/B,CAAC,CAAC;QAEF,OAAOa,MAAM;MACf,CAAC;MAED,OAAOvE,eAAe,CAACkC,EAAE,CAACyJ,IAAI,CAACoe,OAAO,CAAClmB,CAAC,CAAC,EAAEkmB,OAAO,CAACjmB,CAAC,CAAC,CAAC;IACxD;IAEA,IAAIwlB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd,KAAK,IAAI3mB,CAAC,GAAG,CAAC,GAAIA,CAAC,EAAE,EAAE;QACrB,IAAIA,CAAC,KAAKkB,CAAC,CAAC9C,MAAM,EAAE;UAClB,OAAO4B,CAAC,KAAKmB,CAAC,CAAC/C,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAChC;QAEA,IAAI4B,CAAC,KAAKmB,CAAC,CAAC/C,MAAM,EAAE;UAClB,OAAO,CAAC;QACV;QAEA,IAAMmK,CAAC,GAAGlL,eAAe,CAACkC,EAAE,CAACyJ,IAAI,CAAC9H,CAAC,CAAClB,CAAC,CAAC,EAAEmB,CAAC,CAACnB,CAAC,CAAC,CAAC;QAC7C,IAAIuI,CAAC,KAAK,CAAC,EAAE;UACX,OAAOA,CAAC;QACV;MACF;IACF;IAEA,IAAIoe,EAAE,KAAK,CAAC,EAAE;MAAE;MACd;MACA;MACA,IAAIzlB,CAAC,CAAC9C,MAAM,KAAK+C,CAAC,CAAC/C,MAAM,EAAE;QACzB,OAAO8C,CAAC,CAAC9C,MAAM,GAAG+C,CAAC,CAAC/C,MAAM;MAC5B;MAEA,KAAK,IAAI4B,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGkB,CAAC,CAAC9C,MAAM,EAAE4B,EAAC,EAAE,EAAE;QACjC,IAAIkB,CAAC,CAAClB,EAAC,CAAC,GAAGmB,CAAC,CAACnB,EAAC,CAAC,EAAE;UACf,OAAO,CAAC,CAAC;QACX;QAEA,IAAIkB,CAAC,CAAClB,EAAC,CAAC,GAAGmB,CAAC,CAACnB,EAAC,CAAC,EAAE;UACf,OAAO,CAAC;QACV;MACF;MAEA,OAAO,CAAC;IACV;IAEA,IAAI2mB,EAAE,KAAK,CAAC,EAAE;MAAE;MACd,IAAIzlB,CAAC,EAAE;QACL,OAAOC,CAAC,GAAG,CAAC,GAAG,CAAC;MAClB;MAEA,OAAOA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACnB;IAEA,IAAIwlB,EAAE,KAAK,EAAE;MAAE;MACb,OAAO,CAAC;IAEV,IAAIA,EAAE,KAAK,EAAE;MAAE;MACb,MAAMtoB,KAAK,CAAC,6CAA6C,CAAC,CAAC,CAAC;;IAE9D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIsoB,EAAE,KAAK,EAAE;MAAE;MACb,MAAMtoB,KAAK,CAAC,0CAA0C,CAAC,CAAC,CAAC;;IAE3D,MAAMA,KAAK,CAAC,sBAAsB,CAAC;EACrC;AACF,CAAC,C;;;;;;;;;;;ACtWD,IAAIgpB,gBAAgB;AAACrrB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAACirB,gBAAgB,GAACjrB,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAImR,OAAO;AAACvR,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAACmR,OAAO,GAACnR,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIuR,MAAM;AAAC3R,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAAC,WAAQ,SAAAE,CAASC,CAAC,EAAC;IAACuR,MAAM,GAACvR,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAIxPiB,eAAe,GAAGgqB,gBAAgB;AAClC/Z,SAAS,GAAG;EACRjQ,eAAe,EAAEgqB,gBAAgB;EACjC9Z,OAAO,EAAPA,OAAO;EACPI,MAAM,EAANA;AACJ,CAAC,C;;;;;;;;;;;ACTD3R,MAAM,CAACM,MAAM,CAAC;EAAC,WAAQ,SAAAH,CAAA,EAAU;IAAC,OAAOuW,aAAa;EAAA;AAAC,CAAC,CAAC;AAAC,IACrCA,aAAa;EAAA,SAAAA,cAAA;EAAA,OAAAA,aAAA;AAAA,I;;;;;;;;;;;ACDlC,IAAIrW,OAAO;AAACL,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACE,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,OAAO,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA3FJ,MAAM,CAACM,MAAM,CAAC;EAAC,WAAQ,SAAAH,CAAA,EAAU;IAAC,OAAOwR,MAAM;EAAA;AAAC,CAAC,CAAC;AAAC,IAAInR,iBAAiB,EAACE,sBAAsB,EAACC,sBAAsB,EAACJ,MAAM,EAACO,gBAAgB,EAACC,kBAAkB,EAACK,oBAAoB;AAACpB,MAAM,CAACC,IAAI,CAAC,aAAa,EAAC;EAACO,iBAAiB,EAAC,SAAAA,CAASJ,CAAC,EAAC;IAACI,iBAAiB,GAACJ,CAAC;EAAA,CAAC;EAACM,sBAAsB,EAAC,SAAAA,CAASN,CAAC,EAAC;IAACM,sBAAsB,GAACN,CAAC;EAAA,CAAC;EAACO,sBAAsB,EAAC,SAAAA,CAASP,CAAC,EAAC;IAACO,sBAAsB,GAACP,CAAC;EAAA,CAAC;EAACG,MAAM,EAAC,SAAAA,CAASH,CAAC,EAAC;IAACG,MAAM,GAACH,CAAC;EAAA,CAAC;EAACU,gBAAgB,EAAC,SAAAA,CAASV,CAAC,EAAC;IAACU,gBAAgB,GAACV,CAAC;EAAA,CAAC;EAACW,kBAAkB,EAAC,SAAAA,CAASX,CAAC,EAAC;IAACW,kBAAkB,GAACX,CAAC;EAAA,CAAC;EAACgB,oBAAoB,EAAC,SAAAA,CAAShB,CAAC,EAAC;IAACgB,oBAAoB,GAAChB,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAuB1iBuR,MAAM;EACzB,SAAAA,OAAY2Z,IAAI,EAAE;IAAA,IAAAtY,KAAA;IAChB,IAAI,CAACuY,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IAEzB,IAAMC,WAAW,GAAG,SAAAA,CAAC5c,IAAI,EAAE6c,SAAS,EAAK;MACvC,IAAI,CAAC7c,IAAI,EAAE;QACT,MAAMxM,KAAK,CAAC,6BAA6B,CAAC;MAC5C;MAEA,IAAIwM,IAAI,CAAC8c,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC1B,MAAMtpB,KAAK,4BAA0BwM,IAAM,CAAC;MAC9C;MAEAmE,KAAI,CAACuY,cAAc,CAACzgB,IAAI,CAAC;QACvB4gB,SAAS,EAATA,SAAS;QACTE,MAAM,EAAE7qB,kBAAkB,CAAC8N,IAAI,EAAE;UAAClB,OAAO,EAAE;QAAI,CAAC,CAAC;QACjDkB,IAAI,EAAJA;MACF,CAAC,CAAC;IACJ,CAAC;IAED,IAAIyc,IAAI,YAAYppB,KAAK,EAAE;MACzBopB,IAAI,CAAC1gB,OAAO,CAAC,UAAAX,OAAO,EAAI;QACtB,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAC/BwhB,WAAW,CAACxhB,OAAO,EAAE,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLwhB,WAAW,CAACxhB,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;QAChD;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI5J,OAAA,CAAOirB,IAAI,MAAK,QAAQ,EAAE;MACnChqB,MAAM,CAACuD,IAAI,CAACymB,IAAI,CAAC,CAAC1gB,OAAO,CAAC,UAAA7F,GAAG,EAAI;QAC/B0mB,WAAW,CAAC1mB,GAAG,EAAEumB,IAAI,CAACvmB,GAAG,CAAC,IAAI,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOumB,IAAI,KAAK,UAAU,EAAE;MACrC,IAAI,CAACE,aAAa,GAAGF,IAAI;IAC3B,CAAC,MAAM;MACL,MAAMjpB,KAAK,8BAA4BuK,IAAI,CAACC,SAAS,CAACye,IAAI,CAAG,CAAC;IAChE;;IAEA;IACA,IAAI,IAAI,CAACE,aAAa,EAAE;MACtB;IACF;;IAEA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACK,kBAAkB,EAAE;MAC3B,IAAM7gB,QAAQ,GAAG,CAAC,CAAC;MAEnB,IAAI,CAACugB,cAAc,CAAC3gB,OAAO,CAAC,UAAA0gB,IAAI,EAAI;QAClCtgB,QAAQ,CAACsgB,IAAI,CAACzc,IAAI,CAAC,GAAG,CAAC;MACzB,CAAC,CAAC;MAEF,IAAI,CAACid,8BAA8B,GAAG,IAAIxa,SAAS,CAACC,OAAO,CAACvG,QAAQ,CAAC;IACvE;IAEA,IAAI,CAAC+gB,cAAc,GAAGC,kBAAkB,CACtC,IAAI,CAACT,cAAc,CAAC5oB,GAAG,CAAC,UAAC2oB,IAAI,EAAEtnB,CAAC;MAAA,OAAKgP,KAAI,CAACiZ,mBAAmB,CAACjoB,CAAC,CAAC;IAAA,EAClE,CAAC;EACH;EAAC,IAAAsO,MAAA,GAAAX,MAAA,CAAApQ,SAAA;EAAA+Q,MAAA,CAEDiG,aAAa;IAAb,SAAAA,aAAaA,CAAClP,OAAO,EAAE;MACrB;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACkiB,cAAc,CAACnpB,MAAM,IAAI,CAACiH,OAAO,IAAI,CAACA,OAAO,CAAC2L,SAAS,EAAE;QAChE,OAAO,IAAI,CAACkX,kBAAkB,CAAC,CAAC;MAClC;MAEA,IAAMlX,SAAS,GAAG3L,OAAO,CAAC2L,SAAS;;MAEnC;MACA,OAAO,UAAC9P,CAAC,EAAEC,CAAC,EAAK;QACf,IAAI,CAAC6P,SAAS,CAAC2F,GAAG,CAACzV,CAAC,CAAC6K,GAAG,CAAC,EAAE;UACzB,MAAM1N,KAAK,2BAAyB6C,CAAC,CAAC6K,GAAK,CAAC;QAC9C;QAEA,IAAI,CAACiF,SAAS,CAAC2F,GAAG,CAACxV,CAAC,CAAC4K,GAAG,CAAC,EAAE;UACzB,MAAM1N,KAAK,2BAAyB8C,CAAC,CAAC4K,GAAK,CAAC;QAC9C;QAEA,OAAOiF,SAAS,CAAC+C,GAAG,CAAC7S,CAAC,CAAC6K,GAAG,CAAC,GAAGiF,SAAS,CAAC+C,GAAG,CAAC5S,CAAC,CAAC4K,GAAG,CAAC;MACpD,CAAC;IACH;IAAC,OAxBDwI,aAAa;EAAA,IA0Bb;EACA;EACA;EAAA;EAAAjG,MAAA,CACA6Z,YAAY;IAAZ,SAAAA,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;MACvB,IAAID,IAAI,CAAChqB,MAAM,KAAK,IAAI,CAACmpB,cAAc,CAACnpB,MAAM,IAC1CiqB,IAAI,CAACjqB,MAAM,KAAK,IAAI,CAACmpB,cAAc,CAACnpB,MAAM,EAAE;QAC9C,MAAMC,KAAK,CAAC,sBAAsB,CAAC;MACrC;MAEA,OAAO,IAAI,CAAC0pB,cAAc,CAACK,IAAI,EAAEC,IAAI,CAAC;IACxC;IAAC,OAPDF,YAAY;EAAA,IASZ;EACA;EAAA;EAAA7Z,MAAA,CACAga,oBAAoB;IAApB,SAAAA,oBAAoBA,CAACnmB,GAAG,EAAEomB,EAAE,EAAE;MAC5B,IAAI,IAAI,CAAChB,cAAc,CAACnpB,MAAM,KAAK,CAAC,EAAE;QACpC,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;MACxD;MAEA,IAAMmqB,eAAe,GAAG,SAAAA,CAAAhH,OAAO;QAAA,OAAOA,OAAO,CAAClY,IAAI,CAAC,GAAG,CAAC;MAAA,CAAG;MAE1D,IAAImf,UAAU,GAAG,IAAI;;MAErB;MACA,IAAMC,oBAAoB,GAAG,IAAI,CAACnB,cAAc,CAAC5oB,GAAG,CAAC,UAAA2oB,IAAI,EAAI;QAC3D;QACA;QACA,IAAIvhB,QAAQ,GAAGpJ,sBAAsB,CAAC2qB,IAAI,CAACM,MAAM,CAACzlB,GAAG,CAAC,EAAE,IAAI,CAAC;;QAE7D;QACA;QACA,IAAI,CAAC4D,QAAQ,CAAC3H,MAAM,EAAE;UACpB2H,QAAQ,GAAG,CAAC;YAAEvH,KAAK,EAAE,KAAK;UAAE,CAAC,CAAC;QAChC;QAEA,IAAMyH,OAAO,GAAG3I,MAAM,CAACkY,MAAM,CAAC,IAAI,CAAC;QACnC,IAAImT,SAAS,GAAG,KAAK;QAErB5iB,QAAQ,CAACa,OAAO,CAAC,UAAAlC,MAAM,EAAI;UACzB,IAAI,CAACA,MAAM,CAACG,YAAY,EAAE;YACxB;YACA;YACA;YACA,IAAIkB,QAAQ,CAAC3H,MAAM,GAAG,CAAC,EAAE;cACvB,MAAMC,KAAK,CAAC,sCAAsC,CAAC;YACrD;YAEA4H,OAAO,CAAC,EAAE,CAAC,GAAGvB,MAAM,CAAClG,KAAK;YAC1B;UACF;UAEAmqB,SAAS,GAAG,IAAI;UAEhB,IAAM9d,IAAI,GAAG2d,eAAe,CAAC9jB,MAAM,CAACG,YAAY,CAAC;UAEjD,IAAItI,MAAM,CAAC+C,IAAI,CAAC2G,OAAO,EAAE4E,IAAI,CAAC,EAAE;YAC9B,MAAMxM,KAAK,sBAAoBwM,IAAM,CAAC;UACxC;UAEA5E,OAAO,CAAC4E,IAAI,CAAC,GAAGnG,MAAM,CAAClG,KAAK;;UAE5B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIiqB,UAAU,IAAI,CAAClsB,MAAM,CAAC+C,IAAI,CAACmpB,UAAU,EAAE5d,IAAI,CAAC,EAAE;YAChD,MAAMxM,KAAK,CAAC,8BAA8B,CAAC;UAC7C;QACF,CAAC,CAAC;QAEF,IAAIoqB,UAAU,EAAE;UACd;UACA;UACA,IAAI,CAAClsB,MAAM,CAAC+C,IAAI,CAAC2G,OAAO,EAAE,EAAE,CAAC,IACzB3I,MAAM,CAACuD,IAAI,CAAC4nB,UAAU,CAAC,CAACrqB,MAAM,KAAKd,MAAM,CAACuD,IAAI,CAACoF,OAAO,CAAC,CAAC7H,MAAM,EAAE;YAClE,MAAMC,KAAK,CAAC,+BAA+B,CAAC;UAC9C;QACF,CAAC,MAAM,IAAIsqB,SAAS,EAAE;UACpBF,UAAU,GAAG,CAAC,CAAC;UAEfnrB,MAAM,CAACuD,IAAI,CAACoF,OAAO,CAAC,CAACW,OAAO,CAAC,UAAAiE,IAAI,EAAI;YACnC4d,UAAU,CAAC5d,IAAI,CAAC,GAAG,IAAI;UACzB,CAAC,CAAC;QACJ;QAEA,OAAO5E,OAAO;MAChB,CAAC,CAAC;MAEF,IAAI,CAACwiB,UAAU,EAAE;QACf;QACA,IAAMG,OAAO,GAAGF,oBAAoB,CAAC/pB,GAAG,CAAC,UAAAgmB,MAAM,EAAI;UACjD,IAAI,CAACpoB,MAAM,CAAC+C,IAAI,CAACqlB,MAAM,EAAE,EAAE,CAAC,EAAE;YAC5B,MAAMtmB,KAAK,CAAC,4BAA4B,CAAC;UAC3C;UAEA,OAAOsmB,MAAM,CAAC,EAAE,CAAC;QACnB,CAAC,CAAC;QAEF4D,EAAE,CAACK,OAAO,CAAC;QAEX;MACF;MAEAtrB,MAAM,CAACuD,IAAI,CAAC4nB,UAAU,CAAC,CAAC7hB,OAAO,CAAC,UAAAiE,IAAI,EAAI;QACtC,IAAM9J,GAAG,GAAG2nB,oBAAoB,CAAC/pB,GAAG,CAAC,UAAAgmB,MAAM,EAAI;UAC7C,IAAIpoB,MAAM,CAAC+C,IAAI,CAACqlB,MAAM,EAAE,EAAE,CAAC,EAAE;YAC3B,OAAOA,MAAM,CAAC,EAAE,CAAC;UACnB;UAEA,IAAI,CAACpoB,MAAM,CAAC+C,IAAI,CAACqlB,MAAM,EAAE9Z,IAAI,CAAC,EAAE;YAC9B,MAAMxM,KAAK,CAAC,eAAe,CAAC;UAC9B;UAEA,OAAOsmB,MAAM,CAAC9Z,IAAI,CAAC;QACrB,CAAC,CAAC;QAEF0d,EAAE,CAACxnB,GAAG,CAAC;MACT,CAAC,CAAC;IACJ;IAAC,OA9GDunB,oBAAoB;EAAA,IAgHpB;EACA;EAAA;EAAAha,MAAA,CACA4Z,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAAA,EAAG;MAAA,IAAA7X,MAAA;MACnB,IAAI,IAAI,CAACmX,aAAa,EAAE;QACtB,OAAO,IAAI,CAACA,aAAa;MAC3B;;MAEA;MACA;MACA,IAAI,CAAC,IAAI,CAACD,cAAc,CAACnpB,MAAM,EAAE;QAC/B,OAAO,UAACyqB,IAAI,EAAEC,IAAI;UAAA,OAAK,CAAC;QAAA;MAC1B;MAEA,OAAO,UAACD,IAAI,EAAEC,IAAI,EAAK;QACrB,IAAMV,IAAI,GAAG/X,MAAI,CAAC0Y,iBAAiB,CAACF,IAAI,CAAC;QACzC,IAAMR,IAAI,GAAGhY,MAAI,CAAC0Y,iBAAiB,CAACD,IAAI,CAAC;QACzC,OAAOzY,MAAI,CAAC8X,YAAY,CAACC,IAAI,EAAEC,IAAI,CAAC;MACtC,CAAC;IACH;IAAC,OAhBDH,kBAAkB;EAAA,IAkBlB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAAA5Z,MAAA,CACAya,iBAAiB;IAAjB,SAAAA,iBAAiBA,CAAC5mB,GAAG,EAAE;MAAA,IAAAoO,MAAA;MACrB,IAAIyY,MAAM,GAAG,IAAI;MAEjB,IAAI,CAACV,oBAAoB,CAACnmB,GAAG,EAAE,UAAApB,GAAG,EAAI;QACpC,IAAIioB,MAAM,KAAK,IAAI,EAAE;UACnBA,MAAM,GAAGjoB,GAAG;UACZ;QACF;QAEA,IAAIwP,MAAI,CAAC4X,YAAY,CAACpnB,GAAG,EAAEioB,MAAM,CAAC,GAAG,CAAC,EAAE;UACtCA,MAAM,GAAGjoB,GAAG;QACd;MACF,CAAC,CAAC;MAEF,OAAOioB,MAAM;IACf;IAAC,OAfDD,iBAAiB;EAAA;EAAAza,MAAA,CAiBjBiY,SAAS;IAAT,SAAAA,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAACgB,cAAc,CAAC5oB,GAAG,CAAC,UAAAsqB,IAAI;QAAA,OAAIA,IAAI,CAACpe,IAAI;MAAA,EAAC;IACnD;IAAC,OAFD0b,SAAS;EAAA,IAIT;EACA;EAAA;EAAAjY,MAAA,CACA2Z,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAACjoB,CAAC,EAAE;MACrB,IAAMkpB,MAAM,GAAG,CAAC,IAAI,CAAC3B,cAAc,CAACvnB,CAAC,CAAC,CAAC0nB,SAAS;MAEhD,OAAO,UAACU,IAAI,EAAEC,IAAI,EAAK;QACrB,IAAMc,OAAO,GAAG9rB,eAAe,CAACkC,EAAE,CAACyJ,IAAI,CAACof,IAAI,CAACpoB,CAAC,CAAC,EAAEqoB,IAAI,CAACroB,CAAC,CAAC,CAAC;QACzD,OAAOkpB,MAAM,GAAG,CAACC,OAAO,GAAGA,OAAO;MACpC,CAAC;IACH;IAAC,OAPDlB,mBAAmB;EAAA;EAAA,OAAAta,MAAA;AAAA;AAUrB;AACA;AACA;AACA;AACA,SAASqa,kBAAkBA,CAACoB,eAAe,EAAE;EAC3C,OAAO,UAACloB,CAAC,EAAEC,CAAC,EAAK;IACf,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGopB,eAAe,CAAChrB,MAAM,EAAE,EAAE4B,CAAC,EAAE;MAC/C,IAAMmpB,OAAO,GAAGC,eAAe,CAACppB,CAAC,CAAC,CAACkB,CAAC,EAAEC,CAAC,CAAC;MACxC,IAAIgoB,OAAO,KAAK,CAAC,EAAE;QACjB,OAAOA,OAAO;MAChB;IACF;IAEA,OAAO,CAAC;EACV,CAAC;AACH,C", "file": "/packages/minimongo.js", "sourcesContent": ["import './minimongo_common.js';\n", "import LocalCollection from './local_collection.js';\n\nexport const hasOwn = Object.prototype.hasOwnProperty;\n\n// Each element selector contains:\n//  - compileElementSelector, a function with args:\n//    - operand - the \"right hand side\" of the operator\n//    - valueSelector - the \"context\" for the operator (so that $regex can find\n//      $options)\n//    - matcher - the Matcher this is going into (so that $elemMatch can compile\n//      more things)\n//    returning a function mapping a single value to bool.\n//  - dontExpandLeafArrays, a bool which prevents expandArraysInBranches from\n//    being called\n//  - dontIncludeLeafArrays, a bool which causes an argument to be passed to\n//    expandArraysInBranches if it is called\nexport const ELEMENT_OPERATORS = {\n  $lt: makeInequality(cmpValue => cmpValue < 0),\n  $gt: makeInequality(cmpValue => cmpValue > 0),\n  $lte: makeInequality(cmpValue => cmpValue <= 0),\n  $gte: makeInequality(cmpValue => cmpValue >= 0),\n  $mod: {\n    compileElementSelector(operand) {\n      if (!(Array.isArray(operand) && operand.length === 2\n            && typeof operand[0] === 'number'\n            && typeof operand[1] === 'number')) {\n        throw Error('argument to $mod must be an array of two numbers');\n      }\n\n      // XXX could require to be ints or round or something\n      const divisor = operand[0];\n      const remainder = operand[1];\n      return value => (\n        typeof value === 'number' && value % divisor === remainder\n      );\n    },\n  },\n  $in: {\n    compileElementSelector(operand) {\n      if (!Array.isArray(operand)) {\n        throw Error('$in needs an array');\n      }\n\n      const elementMatchers = operand.map(option => {\n        if (option instanceof RegExp) {\n          return regexpElementMatcher(option);\n        }\n\n        if (isOperatorObject(option)) {\n          throw Error('cannot nest $ under $in');\n        }\n\n        return equalityElementMatcher(option);\n      });\n\n      return value => {\n        // Allow {a: {$in: [null]}} to match when 'a' does not exist.\n        if (value === undefined) {\n          value = null;\n        }\n\n        return elementMatchers.some(matcher => matcher(value));\n      };\n    },\n  },\n  $size: {\n    // {a: [[5, 5]]} must match {a: {$size: 1}} but not {a: {$size: 2}}, so we\n    // don't want to consider the element [5,5] in the leaf array [[5,5]] as a\n    // possible value.\n    dontExpandLeafArrays: true,\n    compileElementSelector(operand) {\n      if (typeof operand === 'string') {\n        // Don't ask me why, but by experimentation, this seems to be what Mongo\n        // does.\n        operand = 0;\n      } else if (typeof operand !== 'number') {\n        throw Error('$size needs a number');\n      }\n\n      return value => Array.isArray(value) && value.length === operand;\n    },\n  },\n  $type: {\n    // {a: [5]} must not match {a: {$type: 4}} (4 means array), but it should\n    // match {a: {$type: 1}} (1 means number), and {a: [[5]]} must match {$a:\n    // {$type: 4}}. Thus, when we see a leaf array, we *should* expand it but\n    // should *not* include it itself.\n    dontIncludeLeafArrays: true,\n    compileElementSelector(operand) {\n      if (typeof operand === 'string') {\n        const operandAliasMap = {\n          'double': 1,\n          'string': 2,\n          'object': 3,\n          'array': 4,\n          'binData': 5,\n          'undefined': 6,\n          'objectId': 7,\n          'bool': 8,\n          'date': 9,\n          'null': 10,\n          'regex': 11,\n          'dbPointer': 12,\n          'javascript': 13,\n          'symbol': 14,\n          'javascriptWithScope': 15,\n          'int': 16,\n          'timestamp': 17,\n          'long': 18,\n          'decimal': 19,\n          'minKey': -1,\n          'maxKey': 127,\n        };\n        if (!hasOwn.call(operandAliasMap, operand)) {\n          throw Error(`unknown string alias for $type: ${operand}`);\n        }\n        operand = operandAliasMap[operand];\n      } else if (typeof operand === 'number') {\n        if (operand === 0 || operand < -1\n          || (operand > 19 && operand !== 127)) {\n          throw Error(`Invalid numerical $type code: ${operand}`);\n        }\n      } else {\n        throw Error('argument to $type is not a number or a string');\n      }\n\n      return value => (\n        value !== undefined && LocalCollection._f._type(value) === operand\n      );\n    },\n  },\n  $bitsAllSet: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAllSet');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.every((byte, i) => (bitmask[i] & byte) === byte);\n      };\n    },\n  },\n  $bitsAnySet: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAnySet');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.some((byte, i) => (~bitmask[i] & byte) !== byte);\n      };\n    },\n  },\n  $bitsAllClear: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAllClear');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.every((byte, i) => !(bitmask[i] & byte));\n      };\n    },\n  },\n  $bitsAnyClear: {\n    compileElementSelector(operand) {\n      const mask = getOperandBitmask(operand, '$bitsAnyClear');\n      return value => {\n        const bitmask = getValueBitmask(value, mask.length);\n        return bitmask && mask.some((byte, i) => (bitmask[i] & byte) !== byte);\n      };\n    },\n  },\n  $regex: {\n    compileElementSelector(operand, valueSelector) {\n      if (!(typeof operand === 'string' || operand instanceof RegExp)) {\n        throw Error('$regex has to be a string or RegExp');\n      }\n\n      let regexp;\n      if (valueSelector.$options !== undefined) {\n        // Options passed in $options (even the empty string) always overrides\n        // options in the RegExp object itself.\n\n        // Be clear that we only support the JS-supported options, not extended\n        // ones (eg, Mongo supports x and s). Ideally we would implement x and s\n        // by transforming the regexp, but not today...\n        if (/[^gim]/.test(valueSelector.$options)) {\n          throw new Error('Only the i, m, and g regexp options are supported');\n        }\n\n        const source = operand instanceof RegExp ? operand.source : operand;\n        regexp = new RegExp(source, valueSelector.$options);\n      } else if (operand instanceof RegExp) {\n        regexp = operand;\n      } else {\n        regexp = new RegExp(operand);\n      }\n\n      return regexpElementMatcher(regexp);\n    },\n  },\n  $elemMatch: {\n    dontExpandLeafArrays: true,\n    compileElementSelector(operand, valueSelector, matcher) {\n      if (!LocalCollection._isPlainObject(operand)) {\n        throw Error('$elemMatch need an object');\n      }\n\n      const isDocMatcher = !isOperatorObject(\n        Object.keys(operand)\n          .filter(key => !hasOwn.call(LOGICAL_OPERATORS, key))\n          .reduce((a, b) => Object.assign(a, {[b]: operand[b]}), {}),\n        true);\n\n      let subMatcher;\n      if (isDocMatcher) {\n        // This is NOT the same as compileValueSelector(operand), and not just\n        // because of the slightly different calling convention.\n        // {$elemMatch: {x: 3}} means \"an element has a field x:3\", not\n        // \"consists only of a field x:3\". Also, regexps and sub-$ are allowed.\n        subMatcher =\n          compileDocumentSelector(operand, matcher, {inElemMatch: true});\n      } else {\n        subMatcher = compileValueSelector(operand, matcher);\n      }\n\n      return value => {\n        if (!Array.isArray(value)) {\n          return false;\n        }\n\n        for (let i = 0; i < value.length; ++i) {\n          const arrayElement = value[i];\n          let arg;\n          if (isDocMatcher) {\n            // We can only match {$elemMatch: {b: 3}} against objects.\n            // (We can also match against arrays, if there's numeric indices,\n            // eg {$elemMatch: {'0.b': 3}} or {$elemMatch: {0: 3}}.)\n            if (!isIndexable(arrayElement)) {\n              return false;\n            }\n\n            arg = arrayElement;\n          } else {\n            // dontIterate ensures that {a: {$elemMatch: {$gt: 5}}} matches\n            // {a: [8]} but not {a: [[8]]}\n            arg = [{value: arrayElement, dontIterate: true}];\n          }\n          // XXX support $near in $elemMatch by propagating $distance?\n          if (subMatcher(arg).result) {\n            return i; // specially understood to mean \"use as arrayIndices\"\n          }\n        }\n\n        return false;\n      };\n    },\n  },\n};\n\n// Operators that appear at the top level of a document selector.\nconst LOGICAL_OPERATORS = {\n  $and(subSelector, matcher, inElemMatch) {\n    return andDocumentMatchers(\n      compileArrayOfDocumentSelectors(subSelector, matcher, inElemMatch)\n    );\n  },\n\n  $or(subSelector, matcher, inElemMatch) {\n    const matchers = compileArrayOfDocumentSelectors(\n      subSelector,\n      matcher,\n      inElemMatch\n    );\n\n    // Special case: if there is only one matcher, use it directly, *preserving*\n    // any arrayIndices it returns.\n    if (matchers.length === 1) {\n      return matchers[0];\n    }\n\n    return doc => {\n      const result = matchers.some(fn => fn(doc).result);\n      // $or does NOT set arrayIndices when it has multiple\n      // sub-expressions. (Tested against MongoDB.)\n      return {result};\n    };\n  },\n\n  $nor(subSelector, matcher, inElemMatch) {\n    const matchers = compileArrayOfDocumentSelectors(\n      subSelector,\n      matcher,\n      inElemMatch\n    );\n    return doc => {\n      const result = matchers.every(fn => !fn(doc).result);\n      // Never set arrayIndices, because we only match if nothing in particular\n      // 'matched' (and because this is consistent with MongoDB).\n      return {result};\n    };\n  },\n\n  $where(selectorValue, matcher) {\n    // Record that *any* path may be used.\n    matcher._recordPathUsed('');\n    matcher._hasWhere = true;\n\n    if (!(selectorValue instanceof Function)) {\n      // XXX MongoDB seems to have more complex logic to decide where or or not\n      // to add 'return'; not sure exactly what it is.\n      selectorValue = Function('obj', `return ${selectorValue}`);\n    }\n\n    // We make the document available as both `this` and `obj`.\n    // // XXX not sure what we should do if this throws\n    return doc => ({result: selectorValue.call(doc, doc)});\n  },\n\n  // This is just used as a comment in the query (in MongoDB, it also ends up in\n  // query logs); it has no effect on the actual selection.\n  $comment() {\n    return () => ({result: true});\n  },\n};\n\n// Operators that (unlike LOGICAL_OPERATORS) pertain to individual paths in a\n// document, but (unlike ELEMENT_OPERATORS) do not have a simple definition as\n// \"match each branched value independently and combine with\n// convertElementMatcherToBranchedMatcher\".\nconst VALUE_OPERATORS = {\n  $eq(operand) {\n    return convertElementMatcherToBranchedMatcher(\n      equalityElementMatcher(operand)\n    );\n  },\n  $not(operand, valueSelector, matcher) {\n    return invertBranchedMatcher(compileValueSelector(operand, matcher));\n  },\n  $ne(operand) {\n    return invertBranchedMatcher(\n      convertElementMatcherToBranchedMatcher(equalityElementMatcher(operand))\n    );\n  },\n  $nin(operand) {\n    return invertBranchedMatcher(\n      convertElementMatcherToBranchedMatcher(\n        ELEMENT_OPERATORS.$in.compileElementSelector(operand)\n      )\n    );\n  },\n  $exists(operand) {\n    const exists = convertElementMatcherToBranchedMatcher(\n      value => value !== undefined\n    );\n    return operand ? exists : invertBranchedMatcher(exists);\n  },\n  // $options just provides options for $regex; its logic is inside $regex\n  $options(operand, valueSelector) {\n    if (!hasOwn.call(valueSelector, '$regex')) {\n      throw Error('$options needs a $regex');\n    }\n\n    return everythingMatcher;\n  },\n  // $maxDistance is basically an argument to $near\n  $maxDistance(operand, valueSelector) {\n    if (!valueSelector.$near) {\n      throw Error('$maxDistance needs a $near');\n    }\n\n    return everythingMatcher;\n  },\n  $all(operand, valueSelector, matcher) {\n    if (!Array.isArray(operand)) {\n      throw Error('$all requires array');\n    }\n\n    // Not sure why, but this seems to be what MongoDB does.\n    if (operand.length === 0) {\n      return nothingMatcher;\n    }\n\n    const branchedMatchers = operand.map(criterion => {\n      // XXX handle $all/$elemMatch combination\n      if (isOperatorObject(criterion)) {\n        throw Error('no $ expressions in $all');\n      }\n\n      // This is always a regexp or equality selector.\n      return compileValueSelector(criterion, matcher);\n    });\n\n    // andBranchedMatchers does NOT require all selectors to return true on the\n    // SAME branch.\n    return andBranchedMatchers(branchedMatchers);\n  },\n  $near(operand, valueSelector, matcher, isRoot) {\n    if (!isRoot) {\n      throw Error('$near can\\'t be inside another $ operator');\n    }\n\n    matcher._hasGeoQuery = true;\n\n    // There are two kinds of geodata in MongoDB: legacy coordinate pairs and\n    // GeoJSON. They use different distance metrics, too. GeoJSON queries are\n    // marked with a $geometry property, though legacy coordinates can be\n    // matched using $geometry.\n    let maxDistance, point, distance;\n    if (LocalCollection._isPlainObject(operand) && hasOwn.call(operand, '$geometry')) {\n      // GeoJSON \"2dsphere\" mode.\n      maxDistance = operand.$maxDistance;\n      point = operand.$geometry;\n      distance = value => {\n        // XXX: for now, we don't calculate the actual distance between, say,\n        // polygon and circle. If people care about this use-case it will get\n        // a priority.\n        if (!value) {\n          return null;\n        }\n\n        if (!value.type) {\n          return GeoJSON.pointDistance(\n            point,\n            {type: 'Point', coordinates: pointToArray(value)}\n          );\n        }\n\n        if (value.type === 'Point') {\n          return GeoJSON.pointDistance(point, value);\n        }\n\n        return GeoJSON.geometryWithinRadius(value, point, maxDistance)\n          ? 0\n          : maxDistance + 1;\n      };\n    } else {\n      maxDistance = valueSelector.$maxDistance;\n\n      if (!isIndexable(operand)) {\n        throw Error('$near argument must be coordinate pair or GeoJSON');\n      }\n\n      point = pointToArray(operand);\n\n      distance = value => {\n        if (!isIndexable(value)) {\n          return null;\n        }\n\n        return distanceCoordinatePairs(point, value);\n      };\n    }\n\n    return branchedValues => {\n      // There might be multiple points in the document that match the given\n      // field. Only one of them needs to be within $maxDistance, but we need to\n      // evaluate all of them and use the nearest one for the implicit sort\n      // specifier. (That's why we can't just use ELEMENT_OPERATORS here.)\n      //\n      // Note: This differs from MongoDB's implementation, where a document will\n      // actually show up *multiple times* in the result set, with one entry for\n      // each within-$maxDistance branching point.\n      const result = {result: false};\n      expandArraysInBranches(branchedValues).every(branch => {\n        // if operation is an update, don't skip branches, just return the first\n        // one (#3599)\n        let curDistance;\n        if (!matcher._isUpdate) {\n          if (!(typeof branch.value === 'object')) {\n            return true;\n          }\n\n          curDistance = distance(branch.value);\n\n          // Skip branches that aren't real points or are too far away.\n          if (curDistance === null || curDistance > maxDistance) {\n            return true;\n          }\n\n          // Skip anything that's a tie.\n          if (result.distance !== undefined && result.distance <= curDistance) {\n            return true;\n          }\n        }\n\n        result.result = true;\n        result.distance = curDistance;\n\n        if (branch.arrayIndices) {\n          result.arrayIndices = branch.arrayIndices;\n        } else {\n          delete result.arrayIndices;\n        }\n\n        return !matcher._isUpdate;\n      });\n\n      return result;\n    };\n  },\n};\n\n// NB: We are cheating and using this function to implement 'AND' for both\n// 'document matchers' and 'branched matchers'. They both return result objects\n// but the argument is different: for the former it's a whole doc, whereas for\n// the latter it's an array of 'branched values'.\nfunction andSomeMatchers(subMatchers) {\n  if (subMatchers.length === 0) {\n    return everythingMatcher;\n  }\n\n  if (subMatchers.length === 1) {\n    return subMatchers[0];\n  }\n\n  return docOrBranches => {\n    const match = {};\n    match.result = subMatchers.every(fn => {\n      const subResult = fn(docOrBranches);\n\n      // Copy a 'distance' number out of the first sub-matcher that has\n      // one. Yes, this means that if there are multiple $near fields in a\n      // query, something arbitrary happens; this appears to be consistent with\n      // Mongo.\n      if (subResult.result &&\n          subResult.distance !== undefined &&\n          match.distance === undefined) {\n        match.distance = subResult.distance;\n      }\n\n      // Similarly, propagate arrayIndices from sub-matchers... but to match\n      // MongoDB behavior, this time the *last* sub-matcher with arrayIndices\n      // wins.\n      if (subResult.result && subResult.arrayIndices) {\n        match.arrayIndices = subResult.arrayIndices;\n      }\n\n      return subResult.result;\n    });\n\n    // If we didn't actually match, forget any extra metadata we came up with.\n    if (!match.result) {\n      delete match.distance;\n      delete match.arrayIndices;\n    }\n\n    return match;\n  };\n}\n\nconst andDocumentMatchers = andSomeMatchers;\nconst andBranchedMatchers = andSomeMatchers;\n\nfunction compileArrayOfDocumentSelectors(selectors, matcher, inElemMatch) {\n  if (!Array.isArray(selectors) || selectors.length === 0) {\n    throw Error('$and/$or/$nor must be nonempty array');\n  }\n\n  return selectors.map(subSelector => {\n    if (!LocalCollection._isPlainObject(subSelector)) {\n      throw Error('$or/$and/$nor entries need to be full objects');\n    }\n\n    return compileDocumentSelector(subSelector, matcher, {inElemMatch});\n  });\n}\n\n// Takes in a selector that could match a full document (eg, the original\n// selector). Returns a function mapping document->result object.\n//\n// matcher is the Matcher object we are compiling.\n//\n// If this is the root document selector (ie, not wrapped in $and or the like),\n// then isRoot is true. (This is used by $near.)\nexport function compileDocumentSelector(docSelector, matcher, options = {}) {\n  const docMatchers = Object.keys(docSelector).map(key => {\n    const subSelector = docSelector[key];\n\n    if (key.substr(0, 1) === '$') {\n      // Outer operators are either logical operators (they recurse back into\n      // this function), or $where.\n      if (!hasOwn.call(LOGICAL_OPERATORS, key)) {\n        throw new Error(`Unrecognized logical operator: ${key}`);\n      }\n\n      matcher._isSimple = false;\n      return LOGICAL_OPERATORS[key](subSelector, matcher, options.inElemMatch);\n    }\n\n    // Record this path, but only if we aren't in an elemMatcher, since in an\n    // elemMatch this is a path inside an object in an array, not in the doc\n    // root.\n    if (!options.inElemMatch) {\n      matcher._recordPathUsed(key);\n    }\n\n    // Don't add a matcher if subSelector is a function -- this is to match\n    // the behavior of Meteor on the server (inherited from the node mongodb\n    // driver), which is to ignore any part of a selector which is a function.\n    if (typeof subSelector === 'function') {\n      return undefined;\n    }\n\n    const lookUpByIndex = makeLookupFunction(key);\n    const valueMatcher = compileValueSelector(\n      subSelector,\n      matcher,\n      options.isRoot\n    );\n\n    return doc => valueMatcher(lookUpByIndex(doc));\n  }).filter(Boolean);\n\n  return andDocumentMatchers(docMatchers);\n}\n\n// Takes in a selector that could match a key-indexed value in a document; eg,\n// {$gt: 5, $lt: 9}, or a regular expression, or any non-expression object (to\n// indicate equality).  Returns a branched matcher: a function mapping\n// [branched value]->result object.\nfunction compileValueSelector(valueSelector, matcher, isRoot) {\n  if (valueSelector instanceof RegExp) {\n    matcher._isSimple = false;\n    return convertElementMatcherToBranchedMatcher(\n      regexpElementMatcher(valueSelector)\n    );\n  }\n\n  if (isOperatorObject(valueSelector)) {\n    return operatorBranchedMatcher(valueSelector, matcher, isRoot);\n  }\n\n  return convertElementMatcherToBranchedMatcher(\n    equalityElementMatcher(valueSelector)\n  );\n}\n\n// Given an element matcher (which evaluates a single value), returns a branched\n// value (which evaluates the element matcher on all the branches and returns a\n// more structured return value possibly including arrayIndices).\nfunction convertElementMatcherToBranchedMatcher(elementMatcher, options = {}) {\n  return branches => {\n    const expanded = options.dontExpandLeafArrays\n      ? branches\n      : expandArraysInBranches(branches, options.dontIncludeLeafArrays);\n\n    const match = {};\n    match.result = expanded.some(element => {\n      let matched = elementMatcher(element.value);\n\n      // Special case for $elemMatch: it means \"true, and use this as an array\n      // index if I didn't already have one\".\n      if (typeof matched === 'number') {\n        // XXX This code dates from when we only stored a single array index\n        // (for the outermost array). Should we be also including deeper array\n        // indices from the $elemMatch match?\n        if (!element.arrayIndices) {\n          element.arrayIndices = [matched];\n        }\n\n        matched = true;\n      }\n\n      // If some element matched, and it's tagged with array indices, include\n      // those indices in our result object.\n      if (matched && element.arrayIndices) {\n        match.arrayIndices = element.arrayIndices;\n      }\n\n      return matched;\n    });\n\n    return match;\n  };\n}\n\n// Helpers for $near.\nfunction distanceCoordinatePairs(a, b) {\n  const pointA = pointToArray(a);\n  const pointB = pointToArray(b);\n\n  return Math.hypot(pointA[0] - pointB[0], pointA[1] - pointB[1]);\n}\n\n// Takes something that is not an operator object and returns an element matcher\n// for equality with that thing.\nexport function equalityElementMatcher(elementSelector) {\n  if (isOperatorObject(elementSelector)) {\n    throw Error('Can\\'t create equalityValueSelector for operator object');\n  }\n\n  // Special-case: null and undefined are equal (if you got undefined in there\n  // somewhere, or if you got it due to some branch being non-existent in the\n  // weird special case), even though they aren't with EJSON.equals.\n  // undefined or null\n  if (elementSelector == null) {\n    return value => value == null;\n  }\n\n  return value => LocalCollection._f._equal(elementSelector, value);\n}\n\nfunction everythingMatcher(docOrBranchedValues) {\n  return {result: true};\n}\n\nexport function expandArraysInBranches(branches, skipTheArrays) {\n  const branchesOut = [];\n\n  branches.forEach(branch => {\n    const thisIsArray = Array.isArray(branch.value);\n\n    // We include the branch itself, *UNLESS* we it's an array that we're going\n    // to iterate and we're told to skip arrays.  (That's right, we include some\n    // arrays even skipTheArrays is true: these are arrays that were found via\n    // explicit numerical indices.)\n    if (!(skipTheArrays && thisIsArray && !branch.dontIterate)) {\n      branchesOut.push({arrayIndices: branch.arrayIndices, value: branch.value});\n    }\n\n    if (thisIsArray && !branch.dontIterate) {\n      branch.value.forEach((value, i) => {\n        branchesOut.push({\n          arrayIndices: (branch.arrayIndices || []).concat(i),\n          value\n        });\n      });\n    }\n  });\n\n  return branchesOut;\n}\n\n// Helpers for $bitsAllSet/$bitsAnySet/$bitsAllClear/$bitsAnyClear.\nfunction getOperandBitmask(operand, selector) {\n  // numeric bitmask\n  // You can provide a numeric bitmask to be matched against the operand field.\n  // It must be representable as a non-negative 32-bit signed integer.\n  // Otherwise, $bitsAllSet will return an error.\n  if (Number.isInteger(operand) && operand >= 0) {\n    return new Uint8Array(new Int32Array([operand]).buffer);\n  }\n\n  // bindata bitmask\n  // You can also use an arbitrarily large BinData instance as a bitmask.\n  if (EJSON.isBinary(operand)) {\n    return new Uint8Array(operand.buffer);\n  }\n\n  // position list\n  // If querying a list of bit positions, each <position> must be a non-negative\n  // integer. Bit positions start at 0 from the least significant bit.\n  if (Array.isArray(operand) &&\n      operand.every(x => Number.isInteger(x) && x >= 0)) {\n    const buffer = new ArrayBuffer((Math.max(...operand) >> 3) + 1);\n    const view = new Uint8Array(buffer);\n\n    operand.forEach(x => {\n      view[x >> 3] |= 1 << (x & 0x7);\n    });\n\n    return view;\n  }\n\n  // bad operand\n  throw Error(\n    `operand to ${selector} must be a numeric bitmask (representable as a ` +\n    'non-negative 32-bit signed integer), a bindata bitmask or an array with ' +\n    'bit positions (non-negative integers)'\n  );\n}\n\nfunction getValueBitmask(value, length) {\n  // The field value must be either numerical or a BinData instance. Otherwise,\n  // $bits... will not match the current document.\n\n  // numerical\n  if (Number.isSafeInteger(value)) {\n    // $bits... will not match numerical values that cannot be represented as a\n    // signed 64-bit integer. This can be the case if a value is either too\n    // large or small to fit in a signed 64-bit integer, or if it has a\n    // fractional component.\n    const buffer = new ArrayBuffer(\n      Math.max(length, 2 * Uint32Array.BYTES_PER_ELEMENT)\n    );\n\n    let view = new Uint32Array(buffer, 0, 2);\n    view[0] = value % ((1 << 16) * (1 << 16)) | 0;\n    view[1] = value / ((1 << 16) * (1 << 16)) | 0;\n\n    // sign extension\n    if (value < 0) {\n      view = new Uint8Array(buffer, 2);\n      view.forEach((byte, i) => {\n        view[i] = 0xff;\n      });\n    }\n\n    return new Uint8Array(buffer);\n  }\n\n  // bindata\n  if (EJSON.isBinary(value)) {\n    return new Uint8Array(value.buffer);\n  }\n\n  // no match\n  return false;\n}\n\n// Actually inserts a key value into the selector document\n// However, this checks there is no ambiguity in setting\n// the value for the given key, throws otherwise\nfunction insertIntoDocument(document, key, value) {\n  Object.keys(document).forEach(existingKey => {\n    if (\n      (existingKey.length > key.length && existingKey.indexOf(`${key}.`) === 0) ||\n      (key.length > existingKey.length && key.indexOf(`${existingKey}.`) === 0)\n    ) {\n      throw new Error(\n        `cannot infer query fields to set, both paths '${existingKey}' and ` +\n        `'${key}' are matched`\n      );\n    } else if (existingKey === key) {\n      throw new Error(\n        `cannot infer query fields to set, path '${key}' is matched twice`\n      );\n    }\n  });\n\n  document[key] = value;\n}\n\n// Returns a branched matcher that matches iff the given matcher does not.\n// Note that this implicitly \"deMorganizes\" the wrapped function.  ie, it\n// means that ALL branch values need to fail to match innerBranchedMatcher.\nfunction invertBranchedMatcher(branchedMatcher) {\n  return branchValues => {\n    // We explicitly choose to strip arrayIndices here: it doesn't make sense to\n    // say \"update the array element that does not match something\", at least\n    // in mongo-land.\n    return {result: !branchedMatcher(branchValues).result};\n  };\n}\n\nexport function isIndexable(obj) {\n  return Array.isArray(obj) || LocalCollection._isPlainObject(obj);\n}\n\nexport function isNumericKey(s) {\n  return /^[0-9]+$/.test(s);\n}\n\n// Returns true if this is an object with at least one key and all keys begin\n// with $.  Unless inconsistentOK is set, throws if some keys begin with $ and\n// others don't.\nexport function isOperatorObject(valueSelector, inconsistentOK) {\n  if (!LocalCollection._isPlainObject(valueSelector)) {\n    return false;\n  }\n\n  let theseAreOperators = undefined;\n  Object.keys(valueSelector).forEach(selKey => {\n    const thisIsOperator = selKey.substr(0, 1) === '$' || selKey === 'diff';\n\n    if (theseAreOperators === undefined) {\n      theseAreOperators = thisIsOperator;\n    } else if (theseAreOperators !== thisIsOperator) {\n      if (!inconsistentOK) {\n        throw new Error(\n          `Inconsistent operator: ${JSON.stringify(valueSelector)}`\n        );\n      }\n\n      theseAreOperators = false;\n    }\n  });\n\n  return !!theseAreOperators; // {} has no operators\n}\n\n// Helper for $lt/$gt/$lte/$gte.\nfunction makeInequality(cmpValueComparator) {\n  return {\n    compileElementSelector(operand) {\n      // Arrays never compare false with non-arrays for any inequality.\n      // XXX This was behavior we observed in pre-release MongoDB 2.5, but\n      //     it seems to have been reverted.\n      //     See https://jira.mongodb.org/browse/SERVER-11444\n      if (Array.isArray(operand)) {\n        return () => false;\n      }\n\n      // Special case: consider undefined and null the same (so true with\n      // $gte/$lte).\n      if (operand === undefined) {\n        operand = null;\n      }\n\n      const operandType = LocalCollection._f._type(operand);\n\n      return value => {\n        if (value === undefined) {\n          value = null;\n        }\n\n        // Comparisons are never true among things of different type (except\n        // null vs undefined).\n        if (LocalCollection._f._type(value) !== operandType) {\n          return false;\n        }\n\n        return cmpValueComparator(LocalCollection._f._cmp(value, operand));\n      };\n    },\n  };\n}\n\n// makeLookupFunction(key) returns a lookup function.\n//\n// A lookup function takes in a document and returns an array of matching\n// branches.  If no arrays are found while looking up the key, this array will\n// have exactly one branches (possibly 'undefined', if some segment of the key\n// was not found).\n//\n// If arrays are found in the middle, this can have more than one element, since\n// we 'branch'. When we 'branch', if there are more key segments to look up,\n// then we only pursue branches that are plain objects (not arrays or scalars).\n// This means we can actually end up with no branches!\n//\n// We do *NOT* branch on arrays that are found at the end (ie, at the last\n// dotted member of the key). We just return that array; if you want to\n// effectively 'branch' over the array's values, post-process the lookup\n// function with expandArraysInBranches.\n//\n// Each branch is an object with keys:\n//  - value: the value at the branch\n//  - dontIterate: an optional bool; if true, it means that 'value' is an array\n//    that expandArraysInBranches should NOT expand. This specifically happens\n//    when there is a numeric index in the key, and ensures the\n//    perhaps-surprising MongoDB behavior where {'a.0': 5} does NOT\n//    match {a: [[5]]}.\n//  - arrayIndices: if any array indexing was done during lookup (either due to\n//    explicit numeric indices or implicit branching), this will be an array of\n//    the array indices used, from outermost to innermost; it is falsey or\n//    absent if no array index is used. If an explicit numeric index is used,\n//    the index will be followed in arrayIndices by the string 'x'.\n//\n//    Note: arrayIndices is used for two purposes. First, it is used to\n//    implement the '$' modifier feature, which only ever looks at its first\n//    element.\n//\n//    Second, it is used for sort key generation, which needs to be able to tell\n//    the difference between different paths. Moreover, it needs to\n//    differentiate between explicit and implicit branching, which is why\n//    there's the somewhat hacky 'x' entry: this means that explicit and\n//    implicit array lookups will have different full arrayIndices paths. (That\n//    code only requires that different paths have different arrayIndices; it\n//    doesn't actually 'parse' arrayIndices. As an alternative, arrayIndices\n//    could contain objects with flags like 'implicit', but I think that only\n//    makes the code surrounding them more complex.)\n//\n//    (By the way, this field ends up getting passed around a lot without\n//    cloning, so never mutate any arrayIndices field/var in this package!)\n//\n//\n// At the top level, you may only pass in a plain object or array.\n//\n// See the test 'minimongo - lookup' for some examples of what lookup functions\n// return.\nexport function makeLookupFunction(key, options = {}) {\n  const parts = key.split('.');\n  const firstPart = parts.length ? parts[0] : '';\n  const lookupRest = (\n    parts.length > 1 &&\n    makeLookupFunction(parts.slice(1).join('.'), options)\n  );\n\n  function buildResult(arrayIndices, dontIterate, value) {\n    return arrayIndices && arrayIndices.length\n      ? dontIterate\n        ? [{ arrayIndices, dontIterate, value }]\n        : [{ arrayIndices, value }]\n      : dontIterate\n        ? [{ dontIterate, value }]\n        : [{ value }];\n  }\n\n  // Doc will always be a plain object or an array.\n  // apply an explicit numeric index, an array.\n  return (doc, arrayIndices) => {\n    if (Array.isArray(doc)) {\n      // If we're being asked to do an invalid lookup into an array (non-integer\n      // or out-of-bounds), return no results (which is different from returning\n      // a single undefined result, in that `null` equality checks won't match).\n      if (!(isNumericKey(firstPart) && firstPart < doc.length)) {\n        return [];\n      }\n\n      // Remember that we used this array index. Include an 'x' to indicate that\n      // the previous index came from being considered as an explicit array\n      // index (not branching).\n      arrayIndices = arrayIndices ? arrayIndices.concat(+firstPart, 'x') : [+firstPart, 'x'];\n    }\n\n    // Do our first lookup.\n    const firstLevel = doc[firstPart];\n\n    // If there is no deeper to dig, return what we found.\n    //\n    // If what we found is an array, most value selectors will choose to treat\n    // the elements of the array as matchable values in their own right, but\n    // that's done outside of the lookup function. (Exceptions to this are $size\n    // and stuff relating to $elemMatch.  eg, {a: {$size: 2}} does not match {a:\n    // [[1, 2]]}.)\n    //\n    // That said, if we just did an *explicit* array lookup (on doc) to find\n    // firstLevel, and firstLevel is an array too, we do NOT want value\n    // selectors to iterate over it.  eg, {'a.0': 5} does not match {a: [[5]]}.\n    // So in that case, we mark the return value as 'don't iterate'.\n    if (!lookupRest) {\n      return buildResult(\n        arrayIndices,\n        Array.isArray(doc) && Array.isArray(firstLevel),\n        firstLevel,\n      );\n    }\n\n    // We need to dig deeper.  But if we can't, because what we've found is not\n    // an array or plain object, we're done. If we just did a numeric index into\n    // an array, we return nothing here (this is a change in Mongo 2.5 from\n    // Mongo 2.4, where {'a.0.b': null} stopped matching {a: [5]}). Otherwise,\n    // return a single `undefined` (which can, for example, match via equality\n    // with `null`).\n    if (!isIndexable(firstLevel)) {\n      if (Array.isArray(doc)) {\n        return [];\n      }\n\n      return buildResult(arrayIndices, false, undefined);\n    }\n\n    const result = [];\n    const appendToResult = more => {\n      result.push(...more);\n    };\n\n    // Dig deeper: look up the rest of the parts on whatever we've found.\n    // (lookupRest is smart enough to not try to do invalid lookups into\n    // firstLevel if it's an array.)\n    appendToResult(lookupRest(firstLevel, arrayIndices));\n\n    // If we found an array, then in *addition* to potentially treating the next\n    // part as a literal integer lookup, we should also 'branch': try to look up\n    // the rest of the parts on each array element in parallel.\n    //\n    // In this case, we *only* dig deeper into array elements that are plain\n    // objects. (Recall that we only got this far if we have further to dig.)\n    // This makes sense: we certainly don't dig deeper into non-indexable\n    // objects. And it would be weird to dig into an array: it's simpler to have\n    // a rule that explicit integer indexes only apply to an outer array, not to\n    // an array you find after a branching search.\n    //\n    // In the special case of a numeric part in a *sort selector* (not a query\n    // selector), we skip the branching: we ONLY allow the numeric part to mean\n    // 'look up this index' in that case, not 'also look up this index in all\n    // the elements of the array'.\n    if (Array.isArray(firstLevel) &&\n        !(isNumericKey(parts[1]) && options.forSort)) {\n      firstLevel.forEach((branch, arrayIndex) => {\n        if (LocalCollection._isPlainObject(branch)) {\n          appendToResult(lookupRest(branch, arrayIndices ? arrayIndices.concat(arrayIndex) : [arrayIndex]));\n        }\n      });\n    }\n\n    return result;\n  };\n}\n\n// Object exported only for unit testing.\n// Use it to export private functions to test in Tinytest.\nMinimongoTest = {makeLookupFunction};\nMinimongoError = (message, options = {}) => {\n  if (typeof message === 'string' && options.field) {\n    message += ` for field '${options.field}'`;\n  }\n\n  const error = new Error(message);\n  error.name = 'MinimongoError';\n  return error;\n};\n\nexport function nothingMatcher(docOrBranchedValues) {\n  return {result: false};\n}\n\n// Takes an operator object (an object with $ keys) and returns a branched\n// matcher for it.\nfunction operatorBranchedMatcher(valueSelector, matcher, isRoot) {\n  // Each valueSelector works separately on the various branches.  So one\n  // operator can match one branch and another can match another branch.  This\n  // is OK.\n  const operatorMatchers = Object.keys(valueSelector).map(operator => {\n    const operand = valueSelector[operator];\n\n    const simpleRange = (\n      ['$lt', '$lte', '$gt', '$gte'].includes(operator) &&\n      typeof operand === 'number'\n    );\n\n    const simpleEquality = (\n      ['$ne', '$eq'].includes(operator) &&\n      operand !== Object(operand)\n    );\n\n    const simpleInclusion = (\n      ['$in', '$nin'].includes(operator)\n      && Array.isArray(operand)\n      && !operand.some(x => x === Object(x))\n    );\n\n    if (!(simpleRange || simpleInclusion || simpleEquality)) {\n      matcher._isSimple = false;\n    }\n\n    if (hasOwn.call(VALUE_OPERATORS, operator)) {\n      return VALUE_OPERATORS[operator](operand, valueSelector, matcher, isRoot);\n    }\n\n    if (hasOwn.call(ELEMENT_OPERATORS, operator)) {\n      const options = ELEMENT_OPERATORS[operator];\n      return convertElementMatcherToBranchedMatcher(\n        options.compileElementSelector(operand, valueSelector, matcher),\n        options\n      );\n    }\n\n    throw new Error(`Unrecognized operator: ${operator}`);\n  });\n\n  return andBranchedMatchers(operatorMatchers);\n}\n\n// paths - Array: list of mongo style paths\n// newLeafFn - Function: of form function(path) should return a scalar value to\n//                       put into list created for that path\n// conflictFn - Function: of form function(node, path, fullPath) is called\n//                        when building a tree path for 'fullPath' node on\n//                        'path' was already a leaf with a value. Must return a\n//                        conflict resolution.\n// initial tree - Optional Object: starting tree.\n// @returns - Object: tree represented as a set of nested objects\nexport function pathsToTree(paths, newLeafFn, conflictFn, root = {}) {\n  paths.forEach(path => {\n    const pathArray = path.split('.');\n    let tree = root;\n\n    // use .every just for iteration with break\n    const success = pathArray.slice(0, -1).every((key, i) => {\n      if (!hasOwn.call(tree, key)) {\n        tree[key] = {};\n      } else if (tree[key] !== Object(tree[key])) {\n        tree[key] = conflictFn(\n          tree[key],\n          pathArray.slice(0, i + 1).join('.'),\n          path\n        );\n\n        // break out of loop if we are failing for this path\n        if (tree[key] !== Object(tree[key])) {\n          return false;\n        }\n      }\n\n      tree = tree[key];\n\n      return true;\n    });\n\n    if (success) {\n      const lastKey = pathArray[pathArray.length - 1];\n      if (hasOwn.call(tree, lastKey)) {\n        tree[lastKey] = conflictFn(tree[lastKey], path, path);\n      } else {\n        tree[lastKey] = newLeafFn(path);\n      }\n    }\n  });\n\n  return root;\n}\n\n// Makes sure we get 2 elements array and assume the first one to be x and\n// the second one to y no matter what user passes.\n// In case user passes { lon: x, lat: y } returns [x, y]\nfunction pointToArray(point) {\n  return Array.isArray(point) ? point.slice() : [point.x, point.y];\n}\n\n// Creating a document from an upsert is quite tricky.\n// E.g. this selector: {\"$or\": [{\"b.foo\": {\"$all\": [\"bar\"]}}]}, should result\n// in: {\"b.foo\": \"bar\"}\n// But this selector: {\"$or\": [{\"b\": {\"foo\": {\"$all\": [\"bar\"]}}}]} should throw\n// an error\n\n// Some rules (found mainly with trial & error, so there might be more):\n// - handle all childs of $and (or implicit $and)\n// - handle $or nodes with exactly 1 child\n// - ignore $or nodes with more than 1 child\n// - ignore $nor and $not nodes\n// - throw when a value can not be set unambiguously\n// - every value for $all should be dealt with as separate $eq-s\n// - threat all children of $all as $eq setters (=> set if $all.length === 1,\n//   otherwise throw error)\n// - you can not mix '$'-prefixed keys and non-'$'-prefixed keys\n// - you can only have dotted keys on a root-level\n// - you can not have '$'-prefixed keys more than one-level deep in an object\n\n// Handles one key/value pair to put in the selector document\nfunction populateDocumentWithKeyValue(document, key, value) {\n  if (value && Object.getPrototypeOf(value) === Object.prototype) {\n    populateDocumentWithObject(document, key, value);\n  } else if (!(value instanceof RegExp)) {\n    insertIntoDocument(document, key, value);\n  }\n}\n\n// Handles a key, value pair to put in the selector document\n// if the value is an object\nfunction populateDocumentWithObject(document, key, value) {\n  const keys = Object.keys(value);\n  const unprefixedKeys = keys.filter(op => op[0] !== '$');\n\n  if (unprefixedKeys.length > 0 || !keys.length) {\n    // Literal (possibly empty) object ( or empty object )\n    // Don't allow mixing '$'-prefixed with non-'$'-prefixed fields\n    if (keys.length !== unprefixedKeys.length) {\n      throw new Error(`unknown operator: ${unprefixedKeys[0]}`);\n    }\n\n    validateObject(value, key);\n    insertIntoDocument(document, key, value);\n  } else {\n    Object.keys(value).forEach(op => {\n      const object = value[op];\n\n      if (op === '$eq') {\n        populateDocumentWithKeyValue(document, key, object);\n      } else if (op === '$all') {\n        // every value for $all should be dealt with as separate $eq-s\n        object.forEach(element =>\n          populateDocumentWithKeyValue(document, key, element)\n        );\n      }\n    });\n  }\n}\n\n// Fills a document with certain fields from an upsert selector\nexport function populateDocumentWithQueryFields(query, document = {}) {\n  if (Object.getPrototypeOf(query) === Object.prototype) {\n    // handle implicit $and\n    Object.keys(query).forEach(key => {\n      const value = query[key];\n\n      if (key === '$and') {\n        // handle explicit $and\n        value.forEach(element =>\n          populateDocumentWithQueryFields(element, document)\n        );\n      } else if (key === '$or') {\n        // handle $or nodes with exactly 1 child\n        if (value.length === 1) {\n          populateDocumentWithQueryFields(value[0], document);\n        }\n      } else if (key[0] !== '$') {\n        // Ignore other '$'-prefixed logical selectors\n        populateDocumentWithKeyValue(document, key, value);\n      }\n    });\n  } else {\n    // Handle meteor-specific shortcut for selecting _id\n    if (LocalCollection._selectorIsId(query)) {\n      insertIntoDocument(document, '_id', query);\n    }\n  }\n\n  return document;\n}\n\n// Traverses the keys of passed projection and constructs a tree where all\n// leaves are either all True or all False\n// @returns Object:\n//  - tree - Object - tree representation of keys involved in projection\n//  (exception for '_id' as it is a special case handled separately)\n//  - including - Boolean - \"take only certain fields\" type of projection\nexport function projectionDetails(fields) {\n  // Find the non-_id keys (_id is handled specially because it is included\n  // unless explicitly excluded). Sort the keys, so that our code to detect\n  // overlaps like 'foo' and 'foo.bar' can assume that 'foo' comes first.\n  let fieldsKeys = Object.keys(fields).sort();\n\n  // If _id is the only field in the projection, do not remove it, since it is\n  // required to determine if this is an exclusion or exclusion. Also keep an\n  // inclusive _id, since inclusive _id follows the normal rules about mixing\n  // inclusive and exclusive fields. If _id is not the only field in the\n  // projection and is exclusive, remove it so it can be handled later by a\n  // special case, since exclusive _id is always allowed.\n  if (!(fieldsKeys.length === 1 && fieldsKeys[0] === '_id') &&\n      !(fieldsKeys.includes('_id') && fields._id)) {\n    fieldsKeys = fieldsKeys.filter(key => key !== '_id');\n  }\n\n  let including = null; // Unknown\n\n  fieldsKeys.forEach(keyPath => {\n    const rule = !!fields[keyPath];\n\n    if (including === null) {\n      including = rule;\n    }\n\n    // This error message is copied from MongoDB shell\n    if (including !== rule) {\n      throw MinimongoError(\n        'You cannot currently mix including and excluding fields.'\n      );\n    }\n  });\n\n  const projectionRulesTree = pathsToTree(\n    fieldsKeys,\n    path => including,\n    (node, path, fullPath) => {\n      // Check passed projection fields' keys: If you have two rules such as\n      // 'foo.bar' and 'foo.bar.baz', then the result becomes ambiguous. If\n      // that happens, there is a probability you are doing something wrong,\n      // framework should notify you about such mistake earlier on cursor\n      // compilation step than later during runtime.  Note, that real mongo\n      // doesn't do anything about it and the later rule appears in projection\n      // project, more priority it takes.\n      //\n      // Example, assume following in mongo shell:\n      // > db.coll.insert({ a: { b: 23, c: 44 } })\n      // > db.coll.find({}, { 'a': 1, 'a.b': 1 })\n      // {\"_id\": ObjectId(\"520bfe456024608e8ef24af3\"), \"a\": {\"b\": 23}}\n      // > db.coll.find({}, { 'a.b': 1, 'a': 1 })\n      // {\"_id\": ObjectId(\"520bfe456024608e8ef24af3\"), \"a\": {\"b\": 23, \"c\": 44}}\n      //\n      // Note, how second time the return set of keys is different.\n      const currentPath = fullPath;\n      const anotherPath = path;\n      throw MinimongoError(\n        `both ${currentPath} and ${anotherPath} found in fields option, ` +\n        'using both of them may trigger unexpected behavior. Did you mean to ' +\n        'use only one of them?'\n      );\n    });\n\n  return {including, tree: projectionRulesTree};\n}\n\n// Takes a RegExp object and returns an element matcher.\nexport function regexpElementMatcher(regexp) {\n  return value => {\n    if (value instanceof RegExp) {\n      return value.toString() === regexp.toString();\n    }\n\n    // Regexps only work against strings.\n    if (typeof value !== 'string') {\n      return false;\n    }\n\n    // Reset regexp's state to avoid inconsistent matching for objects with the\n    // same value on consecutive calls of regexp.test. This happens only if the\n    // regexp has the 'g' flag. Also note that ES6 introduces a new flag 'y' for\n    // which we should *not* change the lastIndex but MongoDB doesn't support\n    // either of these flags.\n    regexp.lastIndex = 0;\n\n    return regexp.test(value);\n  };\n}\n\n// Validates the key in a path.\n// Objects that are nested more then 1 level cannot have dotted fields\n// or fields starting with '$'\nfunction validateKeyInPath(key, path) {\n  if (key.includes('.')) {\n    throw new Error(\n      `The dotted field '${key}' in '${path}.${key} is not valid for storage.`\n    );\n  }\n\n  if (key[0] === '$') {\n    throw new Error(\n      `The dollar ($) prefixed field  '${path}.${key} is not valid for storage.`\n    );\n  }\n}\n\n// Recursively validates an object that is nested more than one level deep\nfunction validateObject(object, path) {\n  if (object && Object.getPrototypeOf(object) === Object.prototype) {\n    Object.keys(object).forEach(key => {\n      validateKeyInPath(key, path);\n      validateObject(object[key], path + '.' + key);\n    });\n  }\n}\n", "/** Exported values are also used in the mongo package. */\n\n/** @param {string} method */\nexport function getAsyncMethodName(method) {\n  return `${method.replace('_', '')}Async`;\n}\n\nexport const ASYNC_COLLECTION_METHODS = [\n  '_createCappedCollection',\n  'dropCollection',\n  'dropIndex',\n  /**\n   * @summary Creates the specified index on the collection.\n   * @locus server\n   * @method createIndexAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   * @returns {Promise}\n   */\n  'createIndex',\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOneAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Promise}\n   */\n  'findOne',\n  /**\n   * @summary Insert a document in the collection.  Returns its unique _id.\n   * @locus Anywhere\n   * @method  insertAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   * @return {Promise}\n   */\n  'insert',\n  /**\n   * @summary Remove documents from the collection\n   * @locus Anywhere\n   * @method removeAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   * @return {Promise}\n   */\n  'remove',\n  /**\n   * @summary Modify one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method updateAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   * @return {Promise}\n   */\n  'update',\n  /**\n   * @summary Modify one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsertAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @return {Promise}\n   */\n  'upsert',\n];\n\nexport const ASYNC_CURSOR_METHODS = [\n  /**\n   * @deprecated in 2.9\n   * @summary Returns the number of documents that match a query. This method is\n   *          [deprecated since MongoDB 4.0](https://www.mongodb.com/docs/v4.4/reference/command/count/);\n   *          see `Collection.countDocuments` and\n   *          `Collection.estimatedDocumentCount` for a replacement.\n   * @memberOf Mongo.Cursor\n   * @method  countAsync\n   * @instance\n   * @locus Anywhere\n   * @returns {Promise}\n   */\n  'count',\n  /**\n   * @summary Return all matching documents as an Array.\n   * @memberOf Mongo.Cursor\n   * @method  fetchAsync\n   * @instance\n   * @locus Anywhere\n   * @returns {Promise}\n   */\n  'fetch',\n  /**\n   * @summary Call `callback` once for each matching document, sequentially and\n   *          synchronously.\n   * @locus Anywhere\n   * @method  forEachAsync\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   * @returns {Promise}\n   */\n  'forEach',\n  /**\n   * @summary Map callback over all matching documents.  Returns an Array.\n   * @locus Anywhere\n   * @method mapAsync\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   * @returns {Promise}\n   */\n  'map',\n];\n\nexport const CLIENT_ONLY_METHODS = [\"findOne\", \"insert\", \"remove\", \"update\", \"upsert\"];\n", "import LocalCollection from './local_collection.js';\nimport { hasOwn } from './common.js';\nimport { ASYNC_CURSOR_METHODS, getAsyncMethodName } from './constants';\n\n// Cursor: a specification for a particular subset of documents, w/ a defined\n// order, limit, and offset.  creating a Cursor with LocalCollection.find(),\nexport default class Cursor {\n  // don't call this ctor directly.  use LocalCollection.find().\n  constructor(collection, selector, options = {}) {\n    this.collection = collection;\n    this.sorter = null;\n    this.matcher = new Minimongo.Matcher(selector);\n\n    if (LocalCollection._selectorIsIdPerhapsAsObject(selector)) {\n      // stash for fast _id and { _id }\n      this._selectorId = hasOwn.call(selector, '_id') ? selector._id : selector;\n    } else {\n      this._selectorId = undefined;\n\n      if (this.matcher.hasGeoQuery() || options.sort) {\n        this.sorter = new Minimongo.Sorter(options.sort || []);\n      }\n    }\n\n    this.skip = options.skip || 0;\n    this.limit = options.limit;\n    this.fields = options.projection || options.fields;\n\n    this._projectionFn = LocalCollection._compileProjection(this.fields || {});\n\n    this._transform = LocalCollection.wrapTransform(options.transform);\n\n    // by default, queries register w/ Tracker when it is available.\n    if (typeof Tracker !== 'undefined') {\n      this.reactive = options.reactive === undefined ? true : options.reactive;\n    }\n  }\n\n  /**\n   * @deprecated in 2.9\n   * @summary Returns the number of documents that match a query. This method is\n   *          [deprecated since MongoDB 4.0](https://www.mongodb.com/docs/v4.4/reference/command/count/);\n   *          see `Collection.countDocuments` and\n   *          `Collection.estimatedDocumentCount` for a replacement.\n   * @memberOf Mongo.Cursor\n   * @method  count\n   * @instance\n   * @locus Anywhere\n   * @returns {Number}\n   */\n  count() {\n    if (this.reactive) {\n      // allow the observe to be unordered\n      this._depend({ added: true, removed: true }, true);\n    }\n\n    return this._getRawObjects({\n      ordered: true,\n    }).length;\n  }\n\n  /**\n   * @summary Return all matching documents as an Array.\n   * @memberOf Mongo.Cursor\n   * @method  fetch\n   * @instance\n   * @locus Anywhere\n   * @returns {Object[]}\n   */\n  fetch() {\n    const result = [];\n\n    this.forEach(doc => {\n      result.push(doc);\n    });\n\n    return result;\n  }\n\n  [Symbol.iterator]() {\n    if (this.reactive) {\n      this._depend({\n        addedBefore: true,\n        removed: true,\n        changed: true,\n        movedBefore: true,\n      });\n    }\n\n    let index = 0;\n    const objects = this._getRawObjects({ ordered: true });\n\n    return {\n      next: () => {\n        if (index < objects.length) {\n          // This doubles as a clone operation.\n          let element = this._projectionFn(objects[index++]);\n\n          if (this._transform) element = this._transform(element);\n\n          return { value: element };\n        }\n\n        return { done: true };\n      },\n    };\n  }\n\n  [Symbol.asyncIterator]() {\n    const syncResult = this[Symbol.iterator]();\n    return {\n      async next() {\n        return Promise.resolve(syncResult.next());\n      },\n    };\n  }\n\n  /**\n   * @callback IterationCallback\n   * @param {Object} doc\n   * @param {Number} index\n   */\n  /**\n   * @summary Call `callback` once for each matching document, sequentially and\n   *          synchronously.\n   * @locus Anywhere\n   * @method  forEach\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   */\n  forEach(callback, thisArg) {\n    if (this.reactive) {\n      this._depend({\n        addedBefore: true,\n        removed: true,\n        changed: true,\n        movedBefore: true,\n      });\n    }\n\n    this._getRawObjects({ ordered: true }).forEach((element, i) => {\n      // This doubles as a clone operation.\n      element = this._projectionFn(element);\n\n      if (this._transform) {\n        element = this._transform(element);\n      }\n\n      callback.call(thisArg, element, i, this);\n    });\n  }\n\n  getTransform() {\n    return this._transform;\n  }\n\n  /**\n   * @summary Map callback over all matching documents.  Returns an Array.\n   * @locus Anywhere\n   * @method map\n   * @instance\n   * @memberOf Mongo.Cursor\n   * @param {IterationCallback} callback Function to call. It will be called\n   *                                     with three arguments: the document, a\n   *                                     0-based index, and <em>cursor</em>\n   *                                     itself.\n   * @param {Any} [thisArg] An object which will be the value of `this` inside\n   *                        `callback`.\n   */\n  map(callback, thisArg) {\n    const result = [];\n\n    this.forEach((doc, i) => {\n      result.push(callback.call(thisArg, doc, i, this));\n    });\n\n    return result;\n  }\n\n  // options to contain:\n  //  * callbacks for observe():\n  //    - addedAt (document, atIndex)\n  //    - added (document)\n  //    - changedAt (newDocument, oldDocument, atIndex)\n  //    - changed (newDocument, oldDocument)\n  //    - removedAt (document, atIndex)\n  //    - removed (document)\n  //    - movedTo (document, oldIndex, newIndex)\n  //\n  // attributes available on returned query handle:\n  //  * stop(): end updates\n  //  * collection: the collection this query is querying\n  //\n  // iff x is a returned query handle, (x instanceof\n  // LocalCollection.ObserveHandle) is true\n  //\n  // initial results delivered through added callback\n  // XXX maybe callbacks should take a list of objects, to expose transactions?\n  // XXX maybe support field limiting (to limit what you're notified on)\n\n  /**\n   * @summary Watch a query.  Receive callbacks as the result set changes.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observe(options) {\n    return LocalCollection._observeFromObserveChanges(this, options);\n  }\n\n  /**\n   * @summary Watch a query.  Receive callbacks as the result set changes.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   */\n  observeAsync(options) {\n    return new Promise(resolve => resolve(this.observe(options)));\n  }\n\n  /**\n   * @summary Watch a query. Receive callbacks as the result set changes. Only\n   *          the differences between the old and new documents are passed to\n   *          the callbacks.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observeChanges(options) {\n    const ordered = LocalCollection._observeChangesCallbacksAreOrdered(options);\n\n    // there are several places that assume you aren't combining skip/limit with\n    // unordered observe.  eg, update's EJSON.clone, and the \"there are several\"\n    // comment in _modifyAndNotify\n    // XXX allow skip/limit with unordered observe\n    if (!options._allow_unordered && !ordered && (this.skip || this.limit)) {\n      throw new Error(\n        \"Must use an ordered observe with skip or limit (i.e. 'addedBefore' \" +\n          \"for observeChanges or 'addedAt' for observe, instead of 'added').\"\n      );\n    }\n\n    if (this.fields && (this.fields._id === 0 || this.fields._id === false)) {\n      throw Error(\"You may not observe a cursor with {fields: {_id: 0}}\");\n    }\n\n    const distances =\n      this.matcher.hasGeoQuery() && ordered && new LocalCollection._IdMap();\n\n    const query = {\n      cursor: this,\n      dirty: false,\n      distances,\n      matcher: this.matcher, // not fast pathed\n      ordered,\n      projectionFn: this._projectionFn,\n      resultsSnapshot: null,\n      sorter: ordered && this.sorter,\n    };\n\n    let qid;\n\n    // Non-reactive queries call added[Before] and then never call anything\n    // else.\n    if (this.reactive) {\n      qid = this.collection.next_qid++;\n      this.collection.queries[qid] = query;\n    }\n\n    query.results = this._getRawObjects({\n      ordered,\n      distances: query.distances,\n    });\n\n    if (this.collection.paused) {\n      query.resultsSnapshot = ordered ? [] : new LocalCollection._IdMap();\n    }\n\n    // wrap callbacks we were passed. callbacks only fire when not paused and\n    // are never undefined\n    // Filters out blacklisted fields according to cursor's projection.\n    // XXX wrong place for this?\n\n    // furthermore, callbacks enqueue until the operation we're working on is\n    // done.\n    const wrapCallback = (fn) => {\n      if (!fn) {\n        return () => {};\n      }\n\n      const self = this;\n\n      return function (/* args*/) {\n        if (self.collection.paused) {\n          return;\n        }\n\n        const args = arguments;\n\n        self.collection._observeQueue.queueTask(() => {\n          fn.apply(this, args);\n        });\n      };\n    };\n\n    query.added = wrapCallback(options.added);\n    query.changed = wrapCallback(options.changed);\n    query.removed = wrapCallback(options.removed);\n\n    if (ordered) {\n      query.addedBefore = wrapCallback(options.addedBefore);\n      query.movedBefore = wrapCallback(options.movedBefore);\n    }\n\n    if (!options._suppress_initial && !this.collection.paused) {\n      const handler = (doc) => {\n        const fields = EJSON.clone(doc);\n\n        delete fields._id;\n\n        if (ordered) {\n          query.addedBefore(doc._id, this._projectionFn(fields), null);\n        }\n\n        query.added(doc._id, this._projectionFn(fields));\n      };\n      // it means it's just an array\n      if (query.results.length) {\n        for (const doc of query.results) {\n          handler(doc);\n        }\n      }\n      // it means it's an id map\n      if (query.results?.size?.()) {\n        query.results.forEach(handler);\n      }\n    }\n\n    const handle = Object.assign(new LocalCollection.ObserveHandle(), {\n      collection: this.collection,\n      stop: () => {\n        if (this.reactive) {\n          delete this.collection.queries[qid];\n        }\n      },\n      isReady: false,\n      isReadyPromise: null,\n    });\n\n    if (this.reactive && Tracker.active) {\n      // XXX in many cases, the same observe will be recreated when\n      // the current autorun is rerun.  we could save work by\n      // letting it linger across rerun and potentially get\n      // repurposed if the same observe is performed, using logic\n      // similar to that of Meteor.subscribe.\n      Tracker.onInvalidate(() => {\n        handle.stop();\n      });\n    }\n\n    // run the observe callbacks resulting from the initial contents\n    // before we leave the observe.\n    const drainResult = this.collection._observeQueue.drain();\n\n    if (drainResult instanceof Promise) {\n      handle.isReadyPromise = drainResult;\n      drainResult.then(() => (handle.isReady = true));\n    } else {\n      handle.isReady = true;\n      handle.isReadyPromise = Promise.resolve();\n    }\n\n    return handle;\n  }\n\n  /**\n   * @summary Watch a query. Receive callbacks as the result set changes. Only\n   *          the differences between the old and new documents are passed to\n   *          the callbacks.\n   * @locus Anywhere\n   * @memberOf Mongo.Cursor\n   * @instance\n   * @param {Object} callbacks Functions to call to deliver the result set as it\n   *                           changes\n   */\n  observeChangesAsync(options) {\n    return new Promise((resolve) => {\n      const handle = this.observeChanges(options);\n      handle.isReadyPromise.then(() => resolve(handle));\n    });\n  }\n\n  // XXX Maybe we need a version of observe that just calls a callback if\n  // anything changed.\n  _depend(changers, _allow_unordered) {\n    if (Tracker.active) {\n      const dependency = new Tracker.Dependency();\n      const notify = dependency.changed.bind(dependency);\n\n      dependency.depend();\n\n      const options = { _allow_unordered, _suppress_initial: true };\n\n      ['added', 'addedBefore', 'changed', 'movedBefore', 'removed'].forEach(\n        fn => {\n          if (changers[fn]) {\n            options[fn] = notify;\n          }\n        }\n      );\n\n      // observeChanges will stop() when this computation is invalidated\n      this.observeChanges(options);\n    }\n  }\n\n  _getCollectionName() {\n    return this.collection.name;\n  }\n\n  // Returns a collection of matching objects, but doesn't deep copy them.\n  //\n  // If ordered is set, returns a sorted array, respecting sorter, skip, and\n  // limit properties of the query provided that options.applySkipLimit is\n  // not set to false (#1201). If sorter is falsey, no sort -- you get the\n  // natural order.\n  //\n  // If ordered is not set, returns an object mapping from ID to doc (sorter,\n  // skip and limit should not be set).\n  //\n  // If ordered is set and this cursor is a $near geoquery, then this function\n  // will use an _IdMap to track each distance from the $near argument point in\n  // order to use it as a sort key. If an _IdMap is passed in the 'distances'\n  // argument, this function will clear it and use it for this purpose\n  // (otherwise it will just create its own _IdMap). The observeChanges\n  // implementation uses this to remember the distances after this function\n  // returns.\n  _getRawObjects(options = {}) {\n    // By default this method will respect skip and limit because .fetch(),\n    // .forEach() etc... expect this behaviour. It can be forced to ignore\n    // skip and limit by setting applySkipLimit to false (.count() does this,\n    // for example)\n    const applySkipLimit = options.applySkipLimit !== false;\n\n    // XXX use OrderedDict instead of array, and make IdMap and OrderedDict\n    // compatible\n    const results = options.ordered ? [] : new LocalCollection._IdMap();\n\n    // fast path for single ID value\n    if (this._selectorId !== undefined) {\n      // If you have non-zero skip and ask for a single id, you get nothing.\n      // This is so it matches the behavior of the '{_id: foo}' path.\n      if (applySkipLimit && this.skip) {\n        return results;\n      }\n\n      const selectedDoc = this.collection._docs.get(this._selectorId);\n      if (selectedDoc) {\n        if (options.ordered) {\n          results.push(selectedDoc);\n        } else {\n          results.set(this._selectorId, selectedDoc);\n        }\n      }\n      return results;\n    }\n\n    // slow path for arbitrary selector, sort, skip, limit\n\n    // in the observeChanges case, distances is actually part of the \"query\"\n    // (ie, live results set) object.  in other cases, distances is only used\n    // inside this function.\n    let distances;\n    if (this.matcher.hasGeoQuery() && options.ordered) {\n      if (options.distances) {\n        distances = options.distances;\n        distances.clear();\n      } else {\n        distances = new LocalCollection._IdMap();\n      }\n    }\n\n    Meteor._runFresh(() => {\n      this.collection._docs.forEach((doc, id) => {\n        const matchResult = this.matcher.documentMatches(doc);\n        if (matchResult.result) {\n          if (options.ordered) {\n            results.push(doc);\n\n            if (distances && matchResult.distance !== undefined) {\n              distances.set(id, matchResult.distance);\n            }\n          } else {\n            results.set(id, doc);\n          }\n        }\n\n        // Override to ensure all docs are matched if ignoring skip & limit\n        if (!applySkipLimit) {\n          return true;\n        }\n\n        // Fast path for limited unsorted queries.\n        // XXX 'length' check here seems wrong for ordered\n        return (\n          !this.limit || this.skip || this.sorter || results.length !== this.limit\n        );\n      });\n    });\n\n    if (!options.ordered) {\n      return results;\n    }\n\n    if (this.sorter) {\n      results.sort(this.sorter.getComparator({ distances }));\n    }\n\n    // Return the full set of results if there is no skip or limit or if we're\n    // ignoring them\n    if (!applySkipLimit || (!this.limit && !this.skip)) {\n      return results;\n    }\n\n    return results.slice(\n      this.skip,\n      this.limit ? this.limit + this.skip : results.length\n    );\n  }\n\n  _publishCursor(subscription) {\n    // XXX minimongo should not depend on mongo-livedata!\n    if (!Package.mongo) {\n      throw new Error(\n        \"Can't publish from Minimongo without the `mongo` package.\"\n      );\n    }\n\n    if (!this.collection.name) {\n      throw new Error(\n        \"Can't publish a cursor from a collection without a name.\"\n      );\n    }\n\n    return Package.mongo.Mongo.Collection._publishCursor(\n      this,\n      subscription,\n      this.collection.name\n    );\n  }\n}\n\n// Implements async version of cursor methods to keep collections isomorphic\nASYNC_CURSOR_METHODS.forEach(method => {\n  const asyncName = getAsyncMethodName(method);\n  Cursor.prototype[asyncName] = function(...args) {\n    try {\n      return Promise.resolve(this[method].apply(this, args));\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  };\n});\n", "import Cursor from './cursor.js';\nimport Observe<PERSON><PERSON><PERSON> from './observe_handle.js';\nimport {\n  hasOwn,\n  isIndexable,\n  isNumericKey,\n  isOperatorObject,\n  populateDocumentWithQueryFields,\n  projectionDetails,\n} from './common.js';\n\nimport { getAsyncMethodName } from './constants';\n\n// XXX type checking on selectors (graceful error if malformed)\n\n// LocalCollection: a set of documents that supports queries and modifiers.\nexport default class LocalCollection {\n  constructor(name) {\n    this.name = name;\n    // _id -> document (also containing id)\n    this._docs = new LocalCollection._IdMap;\n\n    this._observeQueue = Meteor.isClient\n      ? new Meteor._SynchronousQueue()\n      : new Meteor._AsynchronousQueue();\n\n    this.next_qid = 1; // live query id generator\n\n    // qid -> live query object. keys:\n    //  ordered: bool. ordered queries have addedBefore/movedBefore callbacks.\n    //  results: array (ordered) or object (unordered) of current results\n    //    (aliased with this._docs!)\n    //  resultsSnapshot: snapshot of results. null if not paused.\n    //  cursor: Cursor object for the query.\n    //  selector, sorter, (callbacks): functions\n    this.queries = Object.create(null);\n\n    // null if not saving originals; an IdMap from id to original document value\n    // if saving originals. See comments before saveOriginals().\n    this._savedOriginals = null;\n\n    // True when observers are paused and we should not send callbacks.\n    this.paused = false;\n  }\n\n  countDocuments(selector, options) {\n    return this.find(selector ?? {}, options).countAsync();\n  }\n\n  estimatedDocumentCount(options) {\n    return this.find({}, options).countAsync();\n  }\n\n  // options may include sort, skip, limit, reactive\n  // sort may be any of these forms:\n  //     {a: 1, b: -1}\n  //     [[\"a\", \"asc\"], [\"b\", \"desc\"]]\n  //     [\"a\", [\"b\", \"desc\"]]\n  //   (in the first form you're beholden to key enumeration order in\n  //   your javascript VM)\n  //\n  // reactive: if given, and false, don't register with Tracker (default\n  // is true)\n  //\n  // XXX possibly should support retrieving a subset of fields? and\n  // have it be a hint (ignored on the client, when not copying the\n  // doc?)\n  //\n  // XXX sort does not yet support subkeys ('a.b') .. fix that!\n  // XXX add one more sort form: \"key\"\n  // XXX tests\n  find(selector, options) {\n    // default syntax for everything is to omit the selector argument.\n    // but if selector is explicitly passed in as false or undefined, we\n    // want a selector that matches nothing.\n    if (arguments.length === 0) {\n      selector = {};\n    }\n\n    return new LocalCollection.Cursor(this, selector, options);\n  }\n\n  findOne(selector, options = {}) {\n    if (arguments.length === 0) {\n      selector = {};\n    }\n\n    // NOTE: by setting limit 1 here, we end up using very inefficient\n    // code that recomputes the whole query on each update. The upside is\n    // that when you reactively depend on a findOne you only get\n    // invalidated when the found object changes, not any object in the\n    // collection. Most findOne will be by id, which has a fast path, so\n    // this might not be a big deal. In most cases, invalidation causes\n    // the called to re-query anyway, so this should be a net performance\n    // improvement.\n    options.limit = 1;\n\n    return this.find(selector, options).fetch()[0];\n  }\n  async findOneAsync(selector, options = {}) {\n    if (arguments.length === 0) {\n      selector = {};\n    }\n    options.limit = 1;\n    return (await this.find(selector, options).fetchAsync())[0];\n  }\n  prepareInsert(doc) {\n    assertHasValidFieldNames(doc);\n\n    // if you really want to use ObjectIDs, set this global.\n    // Mongo.Collection specifies its own ids and does not use this code.\n    if (!hasOwn.call(doc, '_id')) {\n      doc._id = LocalCollection._useOID ? new MongoID.ObjectID() : Random.id();\n    }\n\n    const id = doc._id;\n\n    if (this._docs.has(id)) {\n      throw MinimongoError(`Duplicate _id '${id}'`);\n    }\n\n    this._saveOriginal(id, undefined);\n    this._docs.set(id, doc);\n\n    return id;\n  }\n\n  // XXX possibly enforce that 'undefined' does not appear (we assume\n  // this in our handling of null and $exists)\n  insert(doc, callback) {\n    doc = EJSON.clone(doc);\n    const id = this.prepareInsert(doc);\n    const queriesToRecompute = [];\n\n    // trigger live queries that match\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const matchResult = query.matcher.documentMatches(doc);\n\n      if (matchResult.result) {\n        if (query.distances && matchResult.distance !== undefined) {\n          query.distances.set(id, matchResult.distance);\n        }\n\n        if (query.cursor.skip || query.cursor.limit) {\n          queriesToRecompute.push(qid);\n        } else {\n          LocalCollection._insertInResultsSync(query, doc);\n        }\n      }\n    }\n\n    queriesToRecompute.forEach(qid => {\n      if (this.queries[qid]) {\n        this._recomputeResults(this.queries[qid]);\n      }\n    });\n\n    this._observeQueue.drain();\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, id);\n      });\n    }\n\n    return id;\n  }\n  async insertAsync(doc, callback) {\n    doc = EJSON.clone(doc);\n    const id = this.prepareInsert(doc);\n    const queriesToRecompute = [];\n\n    // trigger live queries that match\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const matchResult = query.matcher.documentMatches(doc);\n\n      if (matchResult.result) {\n        if (query.distances && matchResult.distance !== undefined) {\n          query.distances.set(id, matchResult.distance);\n        }\n\n        if (query.cursor.skip || query.cursor.limit) {\n          queriesToRecompute.push(qid);\n        } else {\n          await LocalCollection._insertInResultsAsync(query, doc);\n        }\n      }\n    }\n\n    queriesToRecompute.forEach(qid => {\n      if (this.queries[qid]) {\n        this._recomputeResults(this.queries[qid]);\n      }\n    });\n\n    await this._observeQueue.drain();\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, id);\n      });\n    }\n\n    return id;\n  }\n\n  // Pause the observers. No callbacks from observers will fire until\n  // 'resumeObservers' is called.\n  pauseObservers() {\n    // No-op if already paused.\n    if (this.paused) {\n      return;\n    }\n\n    // Set the 'paused' flag such that new observer messages don't fire.\n    this.paused = true;\n\n    // Take a snapshot of the query results for each query.\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n      query.resultsSnapshot = EJSON.clone(query.results);\n    });\n  }\n\n  clearResultQueries(callback) {\n    const result = this._docs.size();\n\n    this._docs.clear();\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.ordered) {\n        query.results = [];\n      } else {\n        query.results.clear();\n      }\n    });\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n\n  prepareRemove(selector) {\n    const matcher = new Minimongo.Matcher(selector);\n    const remove = [];\n\n    this._eachPossiblyMatchingDocSync(selector, (doc, id) => {\n      if (matcher.documentMatches(doc).result) {\n        remove.push(id);\n      }\n    });\n\n    const queriesToRecompute = [];\n    const queryRemove = [];\n\n    for (let i = 0; i < remove.length; i++) {\n      const removeId = remove[i];\n      const removeDoc = this._docs.get(removeId);\n\n      Object.keys(this.queries).forEach(qid => {\n        const query = this.queries[qid];\n\n        if (query.dirty) {\n          return;\n        }\n\n        if (query.matcher.documentMatches(removeDoc).result) {\n          if (query.cursor.skip || query.cursor.limit) {\n            queriesToRecompute.push(qid);\n          } else {\n            queryRemove.push({qid, doc: removeDoc});\n          }\n        }\n      });\n\n      this._saveOriginal(removeId, removeDoc);\n      this._docs.remove(removeId);\n    }\n\n    return { queriesToRecompute, queryRemove, remove };\n  }\n\n  remove(selector, callback) {\n    // Easy special case: if we're not calling observeChanges callbacks and\n    // we're not saving originals and we got asked to remove everything, then\n    // just empty everything directly.\n    if (this.paused && !this._savedOriginals && EJSON.equals(selector, {})) {\n      return this.clearResultQueries(callback);\n    }\n\n    const { queriesToRecompute, queryRemove, remove } = this.prepareRemove(selector);\n\n    // run live query callbacks _after_ we've removed the documents.\n    queryRemove.forEach(remove => {\n      const query = this.queries[remove.qid];\n\n      if (query) {\n        query.distances && query.distances.remove(remove.doc._id);\n        LocalCollection._removeFromResultsSync(query, remove.doc);\n      }\n    });\n\n    queriesToRecompute.forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query);\n      }\n    });\n\n    this._observeQueue.drain();\n\n    const result = remove.length;\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  async removeAsync(selector, callback) {\n    // Easy special case: if we're not calling observeChanges callbacks and\n    // we're not saving originals and we got asked to remove everything, then\n    // just empty everything directly.\n    if (this.paused && !this._savedOriginals && EJSON.equals(selector, {})) {\n      return this.clearResultQueries(callback);\n    }\n\n    const { queriesToRecompute, queryRemove, remove } = this.prepareRemove(selector);\n\n    // run live query callbacks _after_ we've removed the documents.\n    for (const remove of queryRemove) {\n      const query = this.queries[remove.qid];\n\n      if (query) {\n        query.distances && query.distances.remove(remove.doc._id);\n        await LocalCollection._removeFromResultsAsync(query, remove.doc);\n      }\n    }\n    queriesToRecompute.forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query);\n      }\n    });\n\n    await this._observeQueue.drain();\n\n    const result = remove.length;\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  // Resume the observers. Observers immediately receive change\n  // notifications to bring them to the current state of the\n  // database. Note that this is not just replaying all the changes that\n  // happened during the pause, it is a smarter 'coalesced' diff.\n  _resumeObservers() {\n    // No-op if not paused.\n    if (!this.paused) {\n      return;\n    }\n\n    // Unset the 'paused' flag. Make sure to do this first, otherwise\n    // observer methods won't actually fire when we trigger them.\n    this.paused = false;\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        query.dirty = false;\n\n        // re-compute results will perform `LocalCollection._diffQueryChanges`\n        // automatically.\n        this._recomputeResults(query, query.resultsSnapshot);\n      } else {\n        // Diff the current results against the snapshot and send to observers.\n        // pass the query object for its observer callbacks.\n        LocalCollection._diffQueryChanges(\n          query.ordered,\n          query.resultsSnapshot,\n          query.results,\n          query,\n          {projectionFn: query.projectionFn}\n        );\n      }\n\n      query.resultsSnapshot = null;\n    });\n  }\n\n  async resumeObserversServer() {\n    this._resumeObservers();\n    await this._observeQueue.drain();\n  }\n  resumeObserversClient() {\n    this._resumeObservers();\n    this._observeQueue.drain();\n  }\n\n  retrieveOriginals() {\n    if (!this._savedOriginals) {\n      throw new Error('Called retrieveOriginals without saveOriginals');\n    }\n\n    const originals = this._savedOriginals;\n\n    this._savedOriginals = null;\n\n    return originals;\n  }\n\n  // To track what documents are affected by a piece of code, call\n  // saveOriginals() before it and retrieveOriginals() after it.\n  // retrieveOriginals returns an object whose keys are the ids of the documents\n  // that were affected since the call to saveOriginals(), and the values are\n  // equal to the document's contents at the time of saveOriginals. (In the case\n  // of an inserted document, undefined is the value.) You must alternate\n  // between calls to saveOriginals() and retrieveOriginals().\n  saveOriginals() {\n    if (this._savedOriginals) {\n      throw new Error('Called saveOriginals twice without retrieveOriginals');\n    }\n\n    this._savedOriginals = new LocalCollection._IdMap;\n  }\n\n  prepareUpdate(selector) {\n    // Save the original results of any query that we might need to\n    // _recomputeResults on, because _modifyAndNotify will mutate the objects in\n    // it. (We don't need to save the original results of paused queries because\n    // they already have a resultsSnapshot and we won't be diffing in\n    // _recomputeResults.)\n    const qidToOriginalResults = {};\n\n    // We should only clone each document once, even if it appears in multiple\n    // queries\n    const docMap = new LocalCollection._IdMap;\n    const idsMatched = LocalCollection._idsMatchedBySelector(selector);\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if ((query.cursor.skip || query.cursor.limit) && ! this.paused) {\n        // Catch the case of a reactive `count()` on a cursor with skip\n        // or limit, which registers an unordered observe. This is a\n        // pretty rare case, so we just clone the entire result set with\n        // no optimizations for documents that appear in these result\n        // sets and other queries.\n        if (query.results instanceof LocalCollection._IdMap) {\n          qidToOriginalResults[qid] = query.results.clone();\n          return;\n        }\n\n        if (!(query.results instanceof Array)) {\n          throw new Error('Assertion failed: query.results not an array');\n        }\n\n        // Clones a document to be stored in `qidToOriginalResults`\n        // because it may be modified before the new and old result sets\n        // are diffed. But if we know exactly which document IDs we're\n        // going to modify, then we only need to clone those.\n        const memoizedCloneIfNeeded = doc => {\n          if (docMap.has(doc._id)) {\n            return docMap.get(doc._id);\n          }\n\n          const docToMemoize = (\n            idsMatched &&\n            !idsMatched.some(id => EJSON.equals(id, doc._id))\n          ) ? doc : EJSON.clone(doc);\n\n          docMap.set(doc._id, docToMemoize);\n\n          return docToMemoize;\n        };\n\n        qidToOriginalResults[qid] = query.results.map(memoizedCloneIfNeeded);\n      }\n    });\n\n    return qidToOriginalResults;\n  }\n\n  finishUpdate({ options, updateCount, callback, insertedId }) {\n\n\n    // Return the number of affected documents, or in the upsert case, an object\n    // containing the number of affected docs and the id of the doc that was\n    // inserted, if any.\n    let result;\n    if (options._returnObject) {\n      result = { numberAffected: updateCount };\n\n      if (insertedId !== undefined) {\n        result.insertedId = insertedId;\n      }\n    } else {\n      result = updateCount;\n    }\n\n    if (callback) {\n      Meteor.defer(() => {\n        callback(null, result);\n      });\n    }\n\n    return result;\n  }\n\n  // XXX atomicity: if multi is true, and one modification fails, do\n  // we rollback the whole operation, or what?\n  async updateAsync(selector, mod, options, callback) {\n    if (! callback && options instanceof Function) {\n      callback = options;\n      options = null;\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    const matcher = new Minimongo.Matcher(selector, true);\n\n    const qidToOriginalResults = this.prepareUpdate(selector);\n\n    let recomputeQids = {};\n\n    let updateCount = 0;\n\n    await this._eachPossiblyMatchingDocAsync(selector, async (doc, id) => {\n      const queryResult = matcher.documentMatches(doc);\n\n      if (queryResult.result) {\n        // XXX Should we save the original even if mod ends up being a no-op?\n        this._saveOriginal(id, doc);\n        recomputeQids = await this._modifyAndNotifyAsync(\n          doc,\n          mod,\n          queryResult.arrayIndices\n        );\n\n        ++updateCount;\n\n        if (!options.multi) {\n          return false; // break\n        }\n      }\n\n      return true;\n    });\n\n    Object.keys(recomputeQids).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query) {\n        this._recomputeResults(query, qidToOriginalResults[qid]);\n      }\n    });\n\n    await this._observeQueue.drain();\n\n    // If we are doing an upsert, and we didn't modify any documents yet, then\n    // it's time to do an insert. Figure out what document we are inserting, and\n    // generate an id for it.\n    let insertedId;\n    if (updateCount === 0 && options.upsert) {\n      const doc = LocalCollection._createUpsertDocument(selector, mod);\n      if (!doc._id && options.insertedId) {\n        doc._id = options.insertedId;\n      }\n\n      insertedId = await this.insertAsync(doc);\n      updateCount = 1;\n    }\n\n    return this.finishUpdate({\n      options,\n      insertedId,\n      updateCount,\n      callback,\n    });\n  }\n  // XXX atomicity: if multi is true, and one modification fails, do\n  // we rollback the whole operation, or what?\n  update(selector, mod, options, callback) {\n    if (! callback && options instanceof Function) {\n      callback = options;\n      options = null;\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    const matcher = new Minimongo.Matcher(selector, true);\n\n    const qidToOriginalResults = this.prepareUpdate(selector);\n\n    let recomputeQids = {};\n\n    let updateCount = 0;\n\n    this._eachPossiblyMatchingDocSync(selector, (doc, id) => {\n      const queryResult = matcher.documentMatches(doc);\n\n      if (queryResult.result) {\n        // XXX Should we save the original even if mod ends up being a no-op?\n        this._saveOriginal(id, doc);\n        recomputeQids = this._modifyAndNotifySync(\n          doc,\n          mod,\n          queryResult.arrayIndices\n        );\n\n        ++updateCount;\n\n        if (!options.multi) {\n          return false; // break\n        }\n      }\n\n      return true;\n    });\n\n    Object.keys(recomputeQids).forEach(qid => {\n      const query = this.queries[qid];\n      if (query) {\n        this._recomputeResults(query, qidToOriginalResults[qid]);\n      }\n    });\n\n    this._observeQueue.drain();\n\n\n    // If we are doing an upsert, and we didn't modify any documents yet, then\n    // it's time to do an insert. Figure out what document we are inserting, and\n    // generate an id for it.\n    let insertedId;\n    if (updateCount === 0 && options.upsert) {\n      const doc = LocalCollection._createUpsertDocument(selector, mod);\n      if (!doc._id && options.insertedId) {\n        doc._id = options.insertedId;\n      }\n\n      insertedId = this.insert(doc);\n      updateCount = 1;\n    }\n\n\n    return this.finishUpdate({\n      options,\n      updateCount,\n      callback,\n      selector,\n      mod,\n    });\n  }\n\n  // A convenience wrapper on update. LocalCollection.upsert(sel, mod) is\n  // equivalent to LocalCollection.update(sel, mod, {upsert: true,\n  // _returnObject: true}).\n  upsert(selector, mod, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.update(\n      selector,\n      mod,\n      Object.assign({}, options, {upsert: true, _returnObject: true}),\n      callback\n    );\n  }\n\n  upsertAsync(selector, mod, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.updateAsync(\n      selector,\n      mod,\n      Object.assign({}, options, {upsert: true, _returnObject: true}),\n      callback\n    );\n  }\n\n  // Iterates over a subset of documents that could match selector; calls\n  // fn(doc, id) on each of them.  Specifically, if selector specifies\n  // specific _id's, it only looks at those.  doc is *not* cloned: it is the\n  // same object that is in _docs.\n  async _eachPossiblyMatchingDocAsync(selector, fn) {\n    const specificIds = LocalCollection._idsMatchedBySelector(selector);\n\n    if (specificIds) {\n      for (const id of specificIds) {\n        const doc = this._docs.get(id);\n\n        if (doc && ! (await fn(doc, id))) {\n          break\n        }\n      }\n    } else {\n      await this._docs.forEachAsync(fn);\n    }\n  }\n  _eachPossiblyMatchingDocSync(selector, fn) {\n    const specificIds = LocalCollection._idsMatchedBySelector(selector);\n\n    if (specificIds) {\n      for (const id of specificIds) {\n        const doc = this._docs.get(id);\n\n        if (doc && !fn(doc, id)) {\n          break\n        }\n      }\n    } else {\n      this._docs.forEach(fn);\n    }\n  }\n\n  _getMatchedDocAndModify(doc, mod, arrayIndices) {\n    const matched_before = {};\n\n    Object.keys(this.queries).forEach(qid => {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        return;\n      }\n\n      if (query.ordered) {\n        matched_before[qid] = query.matcher.documentMatches(doc).result;\n      } else {\n        // Because we don't support skip or limit (yet) in unordered queries, we\n        // can just do a direct lookup.\n        matched_before[qid] = query.results.has(doc._id);\n      }\n    });\n\n    return matched_before;\n  }\n\n  _modifyAndNotifySync(doc, mod, arrayIndices) {\n\n    const matched_before = this._getMatchedDocAndModify(doc, mod, arrayIndices);\n\n    const old_doc = EJSON.clone(doc);\n    LocalCollection._modify(doc, mod, {arrayIndices});\n\n    const recomputeQids = {};\n\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const afterMatch = query.matcher.documentMatches(doc);\n      const after = afterMatch.result;\n      const before = matched_before[qid];\n\n      if (after && query.distances && afterMatch.distance !== undefined) {\n        query.distances.set(doc._id, afterMatch.distance);\n      }\n\n      if (query.cursor.skip || query.cursor.limit) {\n        // We need to recompute any query where the doc may have been in the\n        // cursor's window either before or after the update. (Note that if skip\n        // or limit is set, \"before\" and \"after\" being true do not necessarily\n        // mean that the document is in the cursor's output after skip/limit is\n        // applied... but if they are false, then the document definitely is NOT\n        // in the output. So it's safe to skip recompute if neither before or\n        // after are true.)\n        if (before || after) {\n          recomputeQids[qid] = true;\n        }\n      } else if (before && !after) {\n        LocalCollection._removeFromResultsSync(query, doc);\n      } else if (!before && after) {\n        LocalCollection._insertInResultsSync(query, doc);\n      } else if (before && after) {\n        LocalCollection._updateInResultsSync(query, doc, old_doc);\n      }\n    }\n    return recomputeQids;\n  }\n\n  async _modifyAndNotifyAsync(doc, mod, arrayIndices) {\n\n    const matched_before = this._getMatchedDocAndModify(doc, mod, arrayIndices);\n\n    const old_doc = EJSON.clone(doc);\n    LocalCollection._modify(doc, mod, {arrayIndices});\n\n    const recomputeQids = {};\n    for (const qid of Object.keys(this.queries)) {\n      const query = this.queries[qid];\n\n      if (query.dirty) {\n        continue;\n      }\n\n      const afterMatch = query.matcher.documentMatches(doc);\n      const after = afterMatch.result;\n      const before = matched_before[qid];\n\n      if (after && query.distances && afterMatch.distance !== undefined) {\n        query.distances.set(doc._id, afterMatch.distance);\n      }\n\n      if (query.cursor.skip || query.cursor.limit) {\n        // We need to recompute any query where the doc may have been in the\n        // cursor's window either before or after the update. (Note that if skip\n        // or limit is set, \"before\" and \"after\" being true do not necessarily\n        // mean that the document is in the cursor's output after skip/limit is\n        // applied... but if they are false, then the document definitely is NOT\n        // in the output. So it's safe to skip recompute if neither before or\n        // after are true.)\n        if (before || after) {\n          recomputeQids[qid] = true;\n        }\n      } else if (before && !after) {\n        await LocalCollection._removeFromResultsAsync(query, doc);\n      } else if (!before && after) {\n        await LocalCollection._insertInResultsAsync(query, doc);\n      } else if (before && after) {\n        await LocalCollection._updateInResultsAsync(query, doc, old_doc);\n      }\n    }\n    return recomputeQids;\n  }\n\n  // Recomputes the results of a query and runs observe callbacks for the\n  // difference between the previous results and the current results (unless\n  // paused). Used for skip/limit queries.\n  //\n  // When this is used by insert or remove, it can just use query.results for\n  // the old results (and there's no need to pass in oldResults), because these\n  // operations don't mutate the documents in the collection. Update needs to\n  // pass in an oldResults which was deep-copied before the modifier was\n  // applied.\n  //\n  // oldResults is guaranteed to be ignored if the query is not paused.\n  _recomputeResults(query, oldResults) {\n    if (this.paused) {\n      // There's no reason to recompute the results now as we're still paused.\n      // By flagging the query as \"dirty\", the recompute will be performed\n      // when resumeObservers is called.\n      query.dirty = true;\n      return;\n    }\n\n    if (!this.paused && !oldResults) {\n      oldResults = query.results;\n    }\n\n    if (query.distances) {\n      query.distances.clear();\n    }\n\n    query.results = query.cursor._getRawObjects({\n      distances: query.distances,\n      ordered: query.ordered\n    });\n\n    if (!this.paused) {\n      LocalCollection._diffQueryChanges(\n        query.ordered,\n        oldResults,\n        query.results,\n        query,\n        {projectionFn: query.projectionFn}\n      );\n    }\n  }\n\n  _saveOriginal(id, doc) {\n    // Are we even trying to save originals?\n    if (!this._savedOriginals) {\n      return;\n    }\n\n    // Have we previously mutated the original (and so 'doc' is not actually\n    // original)?  (Note the 'has' check rather than truth: we store undefined\n    // here for inserted docs!)\n    if (this._savedOriginals.has(id)) {\n      return;\n    }\n\n    this._savedOriginals.set(id, EJSON.clone(doc));\n  }\n}\n\nLocalCollection.Cursor = Cursor;\n\nLocalCollection.ObserveHandle = ObserveHandle;\n\n// XXX maybe move these into another ObserveHelpers package or something\n\n// _CachingChangeObserver is an object which receives observeChanges callbacks\n// and keeps a cache of the current cursor state up to date in this.docs. Users\n// of this class should read the docs field but not modify it. You should pass\n// the \"applyChange\" field as the callbacks to the underlying observeChanges\n// call. Optionally, you can specify your own observeChanges callbacks which are\n// invoked immediately before the docs field is updated; this object is made\n// available as `this` to those callbacks.\nLocalCollection._CachingChangeObserver = class _CachingChangeObserver {\n  constructor(options = {}) {\n    const orderedFromCallbacks = (\n      options.callbacks &&\n      LocalCollection._observeChangesCallbacksAreOrdered(options.callbacks)\n    );\n\n    if (hasOwn.call(options, 'ordered')) {\n      this.ordered = options.ordered;\n\n      if (options.callbacks && options.ordered !== orderedFromCallbacks) {\n        throw Error('ordered option doesn\\'t match callbacks');\n      }\n    } else if (options.callbacks) {\n      this.ordered = orderedFromCallbacks;\n    } else {\n      throw Error('must provide ordered or callbacks');\n    }\n\n    const callbacks = options.callbacks || {};\n\n    if (this.ordered) {\n      this.docs = new OrderedDict(MongoID.idStringify);\n      this.applyChange = {\n        addedBefore: (id, fields, before) => {\n          // Take a shallow copy since the top-level properties can be changed\n          const doc = { ...fields };\n\n          doc._id = id;\n\n          if (callbacks.addedBefore) {\n            callbacks.addedBefore.call(this, id, EJSON.clone(fields), before);\n          }\n\n          // This line triggers if we provide added with movedBefore.\n          if (callbacks.added) {\n            callbacks.added.call(this, id, EJSON.clone(fields));\n          }\n\n          // XXX could `before` be a falsy ID?  Technically\n          // idStringify seems to allow for them -- though\n          // OrderedDict won't call stringify on a falsy arg.\n          this.docs.putBefore(id, doc, before || null);\n        },\n        movedBefore: (id, before) => {\n          if (callbacks.movedBefore) {\n            callbacks.movedBefore.call(this, id, before);\n          }\n\n          this.docs.moveBefore(id, before || null);\n        },\n      };\n    } else {\n      this.docs = new LocalCollection._IdMap;\n      this.applyChange = {\n        added: (id, fields) => {\n          // Take a shallow copy since the top-level properties can be changed\n          const doc = { ...fields };\n\n          if (callbacks.added) {\n            callbacks.added.call(this, id, EJSON.clone(fields));\n          }\n\n          doc._id = id;\n\n          this.docs.set(id,  doc);\n        },\n      };\n    }\n\n    // The methods in _IdMap and OrderedDict used by these callbacks are\n    // identical.\n    this.applyChange.changed = (id, fields) => {\n      const doc = this.docs.get(id);\n\n      if (!doc) {\n        throw new Error(`Unknown id for changed: ${id}`);\n      }\n\n      if (callbacks.changed) {\n        callbacks.changed.call(this, id, EJSON.clone(fields));\n      }\n\n      DiffSequence.applyChanges(doc, fields);\n    };\n\n    this.applyChange.removed = id => {\n      if (callbacks.removed) {\n        callbacks.removed.call(this, id);\n      }\n\n      this.docs.remove(id);\n    };\n  }\n};\n\nLocalCollection._IdMap = class _IdMap extends IdMap {\n  constructor() {\n    super(MongoID.idStringify, MongoID.idParse);\n  }\n};\n\n// Wrap a transform function to return objects that have the _id field\n// of the untransformed document. This ensures that subsystems such as\n// the observe-sequence package that call `observe` can keep track of\n// the documents identities.\n//\n// - Require that it returns objects\n// - If the return value has an _id field, verify that it matches the\n//   original _id field\n// - If the return value doesn't have an _id field, add it back.\nLocalCollection.wrapTransform = transform => {\n  if (!transform) {\n    return null;\n  }\n\n  // No need to doubly-wrap transforms.\n  if (transform.__wrappedTransform__) {\n    return transform;\n  }\n\n  const wrapped = doc => {\n    if (!hasOwn.call(doc, '_id')) {\n      // XXX do we ever have a transform on the oplog's collection? because that\n      // collection has no _id.\n      throw new Error('can only transform documents with _id');\n    }\n\n    const id = doc._id;\n\n    // XXX consider making tracker a weak dependency and checking\n    // Package.tracker here\n    const transformed = Tracker.nonreactive(() => transform(doc));\n\n    if (!LocalCollection._isPlainObject(transformed)) {\n      throw new Error('transform must return object');\n    }\n\n    if (hasOwn.call(transformed, '_id')) {\n      if (!EJSON.equals(transformed._id, id)) {\n        throw new Error('transformed document can\\'t have different _id');\n      }\n    } else {\n      transformed._id = id;\n    }\n\n    return transformed;\n  };\n\n  wrapped.__wrappedTransform__ = true;\n\n  return wrapped;\n};\n\n// XXX the sorted-query logic below is laughably inefficient. we'll\n// need to come up with a better datastructure for this.\n//\n// XXX the logic for observing with a skip or a limit is even more\n// laughably inefficient. we recompute the whole results every time!\n\n// This binary search puts a value between any equal values, and the first\n// lesser value.\nLocalCollection._binarySearch = (cmp, array, value) => {\n  let first = 0;\n  let range = array.length;\n\n  while (range > 0) {\n    const halfRange = Math.floor(range / 2);\n\n    if (cmp(value, array[first + halfRange]) >= 0) {\n      first += halfRange + 1;\n      range -= halfRange + 1;\n    } else {\n      range = halfRange;\n    }\n  }\n\n  return first;\n};\n\nLocalCollection._checkSupportedProjection = fields => {\n  if (fields !== Object(fields) || Array.isArray(fields)) {\n    throw MinimongoError('fields option must be an object');\n  }\n\n  Object.keys(fields).forEach(keyPath => {\n    if (keyPath.split('.').includes('$')) {\n      throw MinimongoError(\n        'Minimongo doesn\\'t support $ operator in projections yet.'\n      );\n    }\n\n    const value = fields[keyPath];\n\n    if (typeof value === 'object' &&\n        ['$elemMatch', '$meta', '$slice'].some(key =>\n          hasOwn.call(value, key)\n        )) {\n      throw MinimongoError(\n        'Minimongo doesn\\'t support operators in projections yet.'\n      );\n    }\n\n    if (![1, 0, true, false].includes(value)) {\n      throw MinimongoError(\n        'Projection values should be one of 1, 0, true, or false'\n      );\n    }\n  });\n};\n\n// Knows how to compile a fields projection to a predicate function.\n// @returns - Function: a closure that filters out an object according to the\n//            fields projection rules:\n//            @param obj - Object: MongoDB-styled document\n//            @returns - Object: a document with the fields filtered out\n//                       according to projection rules. Doesn't retain subfields\n//                       of passed argument.\nLocalCollection._compileProjection = fields => {\n  LocalCollection._checkSupportedProjection(fields);\n\n  const _idProjection = fields._id === undefined ? true : fields._id;\n  const details = projectionDetails(fields);\n\n  // returns transformed doc according to ruleTree\n  const transform = (doc, ruleTree) => {\n    // Special case for \"sets\"\n    if (Array.isArray(doc)) {\n      return doc.map(subdoc => transform(subdoc, ruleTree));\n    }\n\n    const result = details.including ? {} : EJSON.clone(doc);\n\n    Object.keys(ruleTree).forEach(key => {\n      if (doc == null || !hasOwn.call(doc, key)) {\n        return;\n      }\n\n      const rule = ruleTree[key];\n\n      if (rule === Object(rule)) {\n        // For sub-objects/subsets we branch\n        if (doc[key] === Object(doc[key])) {\n          result[key] = transform(doc[key], rule);\n        }\n      } else if (details.including) {\n        // Otherwise we don't even touch this subfield\n        result[key] = EJSON.clone(doc[key]);\n      } else {\n        delete result[key];\n      }\n    });\n\n    return doc != null ? result : doc;\n  };\n\n  return doc => {\n    const result = transform(doc, details.tree);\n\n    if (_idProjection && hasOwn.call(doc, '_id')) {\n      result._id = doc._id;\n    }\n\n    if (!_idProjection && hasOwn.call(result, '_id')) {\n      delete result._id;\n    }\n\n    return result;\n  };\n};\n\n// Calculates the document to insert in case we're doing an upsert and the\n// selector does not match any elements\nLocalCollection._createUpsertDocument = (selector, modifier) => {\n  const selectorDocument = populateDocumentWithQueryFields(selector);\n  const isModify = LocalCollection._isModificationMod(modifier);\n\n  const newDoc = {};\n\n  if (selectorDocument._id) {\n    newDoc._id = selectorDocument._id;\n    delete selectorDocument._id;\n  }\n\n  // This double _modify call is made to help with nested properties (see issue\n  // #8631). We do this even if it's a replacement for validation purposes (e.g.\n  // ambiguous id's)\n  LocalCollection._modify(newDoc, {$set: selectorDocument});\n  LocalCollection._modify(newDoc, modifier, {isInsert: true});\n\n  if (isModify) {\n    return newDoc;\n  }\n\n  // Replacement can take _id from query document\n  const replacement = Object.assign({}, modifier);\n  if (newDoc._id) {\n    replacement._id = newDoc._id;\n  }\n\n  return replacement;\n};\n\nLocalCollection._diffObjects = (left, right, callbacks) => {\n  return DiffSequence.diffObjects(left, right, callbacks);\n};\n\n// ordered: bool.\n// old_results and new_results: collections of documents.\n//    if ordered, they are arrays.\n//    if unordered, they are IdMaps\nLocalCollection._diffQueryChanges = (ordered, oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryChanges(ordered, oldResults, newResults, observer, options)\n;\n\nLocalCollection._diffQueryOrderedChanges = (oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryOrderedChanges(oldResults, newResults, observer, options)\n;\n\nLocalCollection._diffQueryUnorderedChanges = (oldResults, newResults, observer, options) =>\n  DiffSequence.diffQueryUnorderedChanges(oldResults, newResults, observer, options)\n;\n\nLocalCollection._findInOrderedResults = (query, doc) => {\n  if (!query.ordered) {\n    throw new Error('Can\\'t call _findInOrderedResults on unordered query');\n  }\n\n  for (let i = 0; i < query.results.length; i++) {\n    if (query.results[i] === doc) {\n      return i;\n    }\n  }\n\n  throw Error('object missing from query');\n};\n\n// If this is a selector which explicitly constrains the match by ID to a finite\n// number of documents, returns a list of their IDs.  Otherwise returns\n// null. Note that the selector may have other restrictions so it may not even\n// match those document!  We care about $in and $and since those are generated\n// access-controlled update and remove.\nLocalCollection._idsMatchedBySelector = selector => {\n  // Is the selector just an ID?\n  if (LocalCollection._selectorIsId(selector)) {\n    return [selector];\n  }\n\n  if (!selector) {\n    return null;\n  }\n\n  // Do we have an _id clause?\n  if (hasOwn.call(selector, '_id')) {\n    // Is the _id clause just an ID?\n    if (LocalCollection._selectorIsId(selector._id)) {\n      return [selector._id];\n    }\n\n    // Is the _id clause {_id: {$in: [\"x\", \"y\", \"z\"]}}?\n    if (selector._id\n        && Array.isArray(selector._id.$in)\n        && selector._id.$in.length\n        && selector._id.$in.every(LocalCollection._selectorIsId)) {\n      return selector._id.$in;\n    }\n\n    return null;\n  }\n\n  // If this is a top-level $and, and any of the clauses constrain their\n  // documents, then the whole selector is constrained by any one clause's\n  // constraint. (Well, by their intersection, but that seems unlikely.)\n  if (Array.isArray(selector.$and)) {\n    for (let i = 0; i < selector.$and.length; ++i) {\n      const subIds = LocalCollection._idsMatchedBySelector(selector.$and[i]);\n\n      if (subIds) {\n        return subIds;\n      }\n    }\n  }\n\n  return null;\n};\n\nLocalCollection._insertInResultsSync = (query, doc) => {\n  const fields = EJSON.clone(doc);\n\n  delete fields._id;\n\n  if (query.ordered) {\n    if (!query.sorter) {\n      query.addedBefore(doc._id, query.projectionFn(fields), null);\n      query.results.push(doc);\n    } else {\n      const i = LocalCollection._insertInSortedList(\n        query.sorter.getComparator({distances: query.distances}),\n        query.results,\n        doc\n      );\n\n      let next = query.results[i + 1];\n      if (next) {\n        next = next._id;\n      } else {\n        next = null;\n      }\n\n      query.addedBefore(doc._id, query.projectionFn(fields), next);\n    }\n\n    query.added(doc._id, query.projectionFn(fields));\n  } else {\n    query.added(doc._id, query.projectionFn(fields));\n    query.results.set(doc._id, doc);\n  }\n};\n\nLocalCollection._insertInResultsAsync = async (query, doc) => {\n  const fields = EJSON.clone(doc);\n\n  delete fields._id;\n\n  if (query.ordered) {\n    if (!query.sorter) {\n      await query.addedBefore(doc._id, query.projectionFn(fields), null);\n      query.results.push(doc);\n    } else {\n      const i = LocalCollection._insertInSortedList(\n        query.sorter.getComparator({distances: query.distances}),\n        query.results,\n        doc\n      );\n\n      let next = query.results[i + 1];\n      if (next) {\n        next = next._id;\n      } else {\n        next = null;\n      }\n\n      await query.addedBefore(doc._id, query.projectionFn(fields), next);\n    }\n\n    await query.added(doc._id, query.projectionFn(fields));\n  } else {\n    await query.added(doc._id, query.projectionFn(fields));\n    query.results.set(doc._id, doc);\n  }\n};\n\nLocalCollection._insertInSortedList = (cmp, array, value) => {\n  if (array.length === 0) {\n    array.push(value);\n    return 0;\n  }\n\n  const i = LocalCollection._binarySearch(cmp, array, value);\n\n  array.splice(i, 0, value);\n\n  return i;\n};\n\nLocalCollection._isModificationMod = mod => {\n  let isModify = false;\n  let isReplace = false;\n\n  Object.keys(mod).forEach(key => {\n    if (key.substr(0, 1) === '$') {\n      isModify = true;\n    } else {\n      isReplace = true;\n    }\n  });\n\n  if (isModify && isReplace) {\n    throw new Error(\n      'Update parameter cannot have both modifier and non-modifier fields.'\n    );\n  }\n\n  return isModify;\n};\n\n// XXX maybe this should be EJSON.isObject, though EJSON doesn't know about\n// RegExp\n// XXX note that _type(undefined) === 3!!!!\nLocalCollection._isPlainObject = x => {\n  return x && LocalCollection._f._type(x) === 3;\n};\n\n// XXX need a strategy for passing the binding of $ into this\n// function, from the compiled selector\n//\n// maybe just {key.up.to.just.before.dollarsign: array_index}\n//\n// XXX atomicity: if one modification fails, do we roll back the whole\n// change?\n//\n// options:\n//   - isInsert is set when _modify is being called to compute the document to\n//     insert as part of an upsert operation. We use this primarily to figure\n//     out when to set the fields in $setOnInsert, if present.\nLocalCollection._modify = (doc, modifier, options = {}) => {\n  if (!LocalCollection._isPlainObject(modifier)) {\n    throw MinimongoError('Modifier must be an object');\n  }\n\n  // Make sure the caller can't mutate our data structures.\n  modifier = EJSON.clone(modifier);\n\n  const isModifier = isOperatorObject(modifier);\n  const newDoc = isModifier ? EJSON.clone(doc) : modifier;\n\n  if (isModifier) {\n    // apply modifiers to the doc.\n    Object.keys(modifier).forEach(operator => {\n      // Treat $setOnInsert as $set if this is an insert.\n      const setOnInsert = options.isInsert && operator === '$setOnInsert';\n      const modFunc = MODIFIERS[setOnInsert ? '$set' : operator];\n      const operand = modifier[operator];\n\n      if (!modFunc) {\n        throw MinimongoError(`Invalid modifier specified ${operator}`);\n      }\n\n      Object.keys(operand).forEach(keypath => {\n        const arg = operand[keypath];\n\n        if (keypath === '') {\n          throw MinimongoError('An empty update path is not valid.');\n        }\n\n        const keyparts = keypath.split('.');\n\n        if (!keyparts.every(Boolean)) {\n          throw MinimongoError(\n            `The update path '${keypath}' contains an empty field name, ` +\n            'which is not allowed.'\n          );\n        }\n\n        const target = findModTarget(newDoc, keyparts, {\n          arrayIndices: options.arrayIndices,\n          forbidArray: operator === '$rename',\n          noCreate: NO_CREATE_MODIFIERS[operator]\n        });\n\n        modFunc(target, keyparts.pop(), arg, keypath, newDoc);\n      });\n    });\n\n    if (doc._id && !EJSON.equals(doc._id, newDoc._id)) {\n      throw MinimongoError(\n        `After applying the update to the document {_id: \"${doc._id}\", ...},` +\n        ' the (immutable) field \\'_id\\' was found to have been altered to ' +\n        `_id: \"${newDoc._id}\"`\n      );\n    }\n  } else {\n    if (doc._id && modifier._id && !EJSON.equals(doc._id, modifier._id)) {\n      throw MinimongoError(\n        `The _id field cannot be changed from {_id: \"${doc._id}\"} to ` +\n        `{_id: \"${modifier._id}\"}`\n      );\n    }\n\n    // replace the whole document\n    assertHasValidFieldNames(modifier);\n  }\n\n  // move new document into place.\n  Object.keys(doc).forEach(key => {\n    // Note: this used to be for (var key in doc) however, this does not\n    // work right in Opera. Deleting from a doc while iterating over it\n    // would sometimes cause opera to skip some keys.\n    if (key !== '_id') {\n      delete doc[key];\n    }\n  });\n\n  Object.keys(newDoc).forEach(key => {\n    doc[key] = newDoc[key];\n  });\n};\n\nLocalCollection._observeFromObserveChanges = (cursor, observeCallbacks) => {\n  const transform = cursor.getTransform() || (doc => doc);\n  let suppressed = !!observeCallbacks._suppress_initial;\n\n  let observeChangesCallbacks;\n  if (LocalCollection._observeCallbacksAreOrdered(observeCallbacks)) {\n    // The \"_no_indices\" option sets all index arguments to -1 and skips the\n    // linear scans required to generate them.  This lets observers that don't\n    // need absolute indices benefit from the other features of this API --\n    // relative order, transforms, and applyChanges -- without the speed hit.\n    const indices = !observeCallbacks._no_indices;\n\n    observeChangesCallbacks = {\n      addedBefore(id, fields, before) {\n        const check = suppressed || !(observeCallbacks.addedAt || observeCallbacks.added)\n        if (check) {\n          return;\n        }\n\n        const doc = transform(Object.assign(fields, {_id: id}));\n\n        if (observeCallbacks.addedAt) {\n          observeCallbacks.addedAt(\n              doc,\n              indices\n                  ? before\n                      ? this.docs.indexOf(before)\n                      : this.docs.size()\n                  : -1,\n              before\n          );\n        } else {\n          observeCallbacks.added(doc);\n        }\n      },\n      changed(id, fields) {\n\n        if (!(observeCallbacks.changedAt || observeCallbacks.changed)) {\n          return;\n        }\n\n        let doc = EJSON.clone(this.docs.get(id));\n        if (!doc) {\n          throw new Error(`Unknown id for changed: ${id}`);\n        }\n\n        const oldDoc = transform(EJSON.clone(doc));\n\n        DiffSequence.applyChanges(doc, fields);\n\n        if (observeCallbacks.changedAt) {\n          observeCallbacks.changedAt(\n              transform(doc),\n              oldDoc,\n              indices ? this.docs.indexOf(id) : -1\n          );\n        } else {\n          observeCallbacks.changed(transform(doc), oldDoc);\n        }\n      },\n      movedBefore(id, before) {\n        if (!observeCallbacks.movedTo) {\n          return;\n        }\n\n        const from = indices ? this.docs.indexOf(id) : -1;\n        let to = indices\n            ? before\n                ? this.docs.indexOf(before)\n                : this.docs.size()\n            : -1;\n\n        // When not moving backwards, adjust for the fact that removing the\n        // document slides everything back one slot.\n        if (to > from) {\n          --to;\n        }\n\n        observeCallbacks.movedTo(\n            transform(EJSON.clone(this.docs.get(id))),\n            from,\n            to,\n            before || null\n        );\n      },\n      removed(id) {\n        if (!(observeCallbacks.removedAt || observeCallbacks.removed)) {\n          return;\n        }\n\n        // technically maybe there should be an EJSON.clone here, but it's about\n        // to be removed from this.docs!\n        const doc = transform(this.docs.get(id));\n\n        if (observeCallbacks.removedAt) {\n          observeCallbacks.removedAt(doc, indices ? this.docs.indexOf(id) : -1);\n        } else {\n          observeCallbacks.removed(doc);\n        }\n      },\n    };\n  } else {\n    observeChangesCallbacks = {\n      added(id, fields) {\n        if (!suppressed && observeCallbacks.added) {\n          observeCallbacks.added(transform(Object.assign(fields, {_id: id})));\n        }\n      },\n      changed(id, fields) {\n        if (observeCallbacks.changed) {\n          const oldDoc = this.docs.get(id);\n          const doc = EJSON.clone(oldDoc);\n\n          DiffSequence.applyChanges(doc, fields);\n\n          observeCallbacks.changed(\n              transform(doc),\n              transform(EJSON.clone(oldDoc))\n          );\n        }\n      },\n      removed(id) {\n        if (observeCallbacks.removed) {\n          observeCallbacks.removed(transform(this.docs.get(id)));\n        }\n      },\n    };\n  }\n\n  const changeObserver = new LocalCollection._CachingChangeObserver({\n    callbacks: observeChangesCallbacks\n  });\n\n  // CachingChangeObserver clones all received input on its callbacks\n  // So we can mark it as safe to reduce the ejson clones.\n  // This is tested by the `mongo-livedata - (extended) scribbling` tests\n  changeObserver.applyChange._fromObserve = true;\n  const handle = cursor.observeChanges(changeObserver.applyChange,\n      { nonMutatingCallbacks: true });\n\n  // If needed, re-enable callbacks as soon as the initial batch is ready.\n  const setSuppressed = (h) => {\n    if (h.isReady) suppressed = false;\n    else h.isReadyPromise?.then(() => (suppressed = false));\n  };\n  // When we call cursor.observeChanges() it can be the on from\n  // the mongo package (instead of the minimongo one) and it doesn't have isReady and isReadyPromise\n  if (Meteor._isPromise(handle)) {\n    handle.then(setSuppressed);\n  } else {\n    setSuppressed(handle);\n  }\n  return handle;\n};\n\nLocalCollection._observeCallbacksAreOrdered = callbacks => {\n  if (callbacks.added && callbacks.addedAt) {\n    throw new Error('Please specify only one of added() and addedAt()');\n  }\n\n  if (callbacks.changed && callbacks.changedAt) {\n    throw new Error('Please specify only one of changed() and changedAt()');\n  }\n\n  if (callbacks.removed && callbacks.removedAt) {\n    throw new Error('Please specify only one of removed() and removedAt()');\n  }\n\n  return !!(\n    callbacks.addedAt ||\n    callbacks.changedAt ||\n    callbacks.movedTo ||\n    callbacks.removedAt\n  );\n};\n\nLocalCollection._observeChangesCallbacksAreOrdered = callbacks => {\n  if (callbacks.added && callbacks.addedBefore) {\n    throw new Error('Please specify only one of added() and addedBefore()');\n  }\n\n  return !!(callbacks.addedBefore || callbacks.movedBefore);\n};\n\nLocalCollection._removeFromResultsSync = (query, doc) => {\n  if (query.ordered) {\n    const i = LocalCollection._findInOrderedResults(query, doc);\n\n    query.removed(doc._id);\n    query.results.splice(i, 1);\n  } else {\n    const id = doc._id;  // in case callback mutates doc\n\n    query.removed(doc._id);\n    query.results.remove(id);\n  }\n};\n\nLocalCollection._removeFromResultsAsync = async (query, doc) => {\n  if (query.ordered) {\n    const i = LocalCollection._findInOrderedResults(query, doc);\n\n    await query.removed(doc._id);\n    query.results.splice(i, 1);\n  } else {\n    const id = doc._id;  // in case callback mutates doc\n\n    await query.removed(doc._id);\n    query.results.remove(id);\n  }\n};\n\n// Is this selector just shorthand for lookup by _id?\nLocalCollection._selectorIsId = selector =>\n  typeof selector === 'number' ||\n  typeof selector === 'string' ||\n  selector instanceof MongoID.ObjectID\n;\n\n// Is the selector just lookup by _id (shorthand or not)?\nLocalCollection._selectorIsIdPerhapsAsObject = selector =>\n  LocalCollection._selectorIsId(selector) ||\n  LocalCollection._selectorIsId(selector && selector._id) &&\n  Object.keys(selector).length === 1\n;\n\nLocalCollection._updateInResultsSync = (query, doc, old_doc) => {\n  if (!EJSON.equals(doc._id, old_doc._id)) {\n    throw new Error('Can\\'t change a doc\\'s _id while updating');\n  }\n\n  const projectionFn = query.projectionFn;\n  const changedFields = DiffSequence.makeChangedFields(\n    projectionFn(doc),\n    projectionFn(old_doc)\n  );\n\n  if (!query.ordered) {\n    if (Object.keys(changedFields).length) {\n      query.changed(doc._id, changedFields);\n      query.results.set(doc._id, doc);\n    }\n\n    return;\n  }\n\n  const old_idx = LocalCollection._findInOrderedResults(query, doc);\n\n  if (Object.keys(changedFields).length) {\n    query.changed(doc._id, changedFields);\n  }\n\n  if (!query.sorter) {\n    return;\n  }\n\n  // just take it out and put it back in again, and see if the index changes\n  query.results.splice(old_idx, 1);\n\n  const new_idx = LocalCollection._insertInSortedList(\n    query.sorter.getComparator({distances: query.distances}),\n    query.results,\n    doc\n  );\n\n  if (old_idx !== new_idx) {\n    let next = query.results[new_idx + 1];\n    if (next) {\n      next = next._id;\n    } else {\n      next = null;\n    }\n\n    query.movedBefore && query.movedBefore(doc._id, next);\n  }\n};\n\nLocalCollection._updateInResultsAsync = async (query, doc, old_doc) => {\n  if (!EJSON.equals(doc._id, old_doc._id)) {\n    throw new Error('Can\\'t change a doc\\'s _id while updating');\n  }\n\n  const projectionFn = query.projectionFn;\n  const changedFields = DiffSequence.makeChangedFields(\n    projectionFn(doc),\n    projectionFn(old_doc)\n  );\n\n  if (!query.ordered) {\n    if (Object.keys(changedFields).length) {\n      await query.changed(doc._id, changedFields);\n      query.results.set(doc._id, doc);\n    }\n\n    return;\n  }\n\n  const old_idx = LocalCollection._findInOrderedResults(query, doc);\n\n  if (Object.keys(changedFields).length) {\n    await query.changed(doc._id, changedFields);\n  }\n\n  if (!query.sorter) {\n    return;\n  }\n\n  // just take it out and put it back in again, and see if the index changes\n  query.results.splice(old_idx, 1);\n\n  const new_idx = LocalCollection._insertInSortedList(\n    query.sorter.getComparator({distances: query.distances}),\n    query.results,\n    doc\n  );\n\n  if (old_idx !== new_idx) {\n    let next = query.results[new_idx + 1];\n    if (next) {\n      next = next._id;\n    } else {\n      next = null;\n    }\n\n    query.movedBefore && await query.movedBefore(doc._id, next);\n  }\n};\n\nconst MODIFIERS = {\n  $currentDate(target, field, arg) {\n    if (typeof arg === 'object' && hasOwn.call(arg, '$type')) {\n      if (arg.$type !== 'date') {\n        throw MinimongoError(\n          'Minimongo does currently only support the date type in ' +\n          '$currentDate modifiers',\n          {field}\n        );\n      }\n    } else if (arg !== true) {\n      throw MinimongoError('Invalid $currentDate modifier', {field});\n    }\n\n    target[field] = new Date();\n  },\n  $inc(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $inc allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $inc modifier to non-number',\n          {field}\n        );\n      }\n\n      target[field] += arg;\n    } else {\n      target[field] = arg;\n    }\n  },\n  $min(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $min allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $min modifier to non-number',\n          {field}\n        );\n      }\n\n      if (target[field] > arg) {\n        target[field] = arg;\n      }\n    } else {\n      target[field] = arg;\n    }\n  },\n  $max(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $max allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $max modifier to non-number',\n          {field}\n        );\n      }\n\n      if (target[field] < arg) {\n        target[field] = arg;\n      }\n    } else {\n      target[field] = arg;\n    }\n  },\n  $mul(target, field, arg) {\n    if (typeof arg !== 'number') {\n      throw MinimongoError('Modifier $mul allowed for numbers only', {field});\n    }\n\n    if (field in target) {\n      if (typeof target[field] !== 'number') {\n        throw MinimongoError(\n          'Cannot apply $mul modifier to non-number',\n          {field}\n        );\n      }\n\n      target[field] *= arg;\n    } else {\n      target[field] = 0;\n    }\n  },\n  $rename(target, field, arg, keypath, doc) {\n    // no idea why mongo has this restriction..\n    if (keypath === arg) {\n      throw MinimongoError('$rename source must differ from target', {field});\n    }\n\n    if (target === null) {\n      throw MinimongoError('$rename source field invalid', {field});\n    }\n\n    if (typeof arg !== 'string') {\n      throw MinimongoError('$rename target must be a string', {field});\n    }\n\n    if (arg.includes('\\0')) {\n      // Null bytes are not allowed in Mongo field names\n      // https://docs.mongodb.com/manual/reference/limits/#Restrictions-on-Field-Names\n      throw MinimongoError(\n        'The \\'to\\' field for $rename cannot contain an embedded null byte',\n        {field}\n      );\n    }\n\n    if (target === undefined) {\n      return;\n    }\n\n    const object = target[field];\n\n    delete target[field];\n\n    const keyparts = arg.split('.');\n    const target2 = findModTarget(doc, keyparts, {forbidArray: true});\n\n    if (target2 === null) {\n      throw MinimongoError('$rename target field invalid', {field});\n    }\n\n    target2[keyparts.pop()] = object;\n  },\n  $set(target, field, arg) {\n    if (target !== Object(target)) { // not an array or an object\n      const error = MinimongoError(\n        'Cannot set property on non-object field',\n        {field}\n      );\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    if (target === null) {\n      const error = MinimongoError('Cannot set property on null', {field});\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    assertHasValidFieldNames(arg);\n\n    target[field] = arg;\n  },\n  $setOnInsert(target, field, arg) {\n    // converted to `$set` in `_modify`\n  },\n  $unset(target, field, arg) {\n    if (target !== undefined) {\n      if (target instanceof Array) {\n        if (field in target) {\n          target[field] = null;\n        }\n      } else {\n        delete target[field];\n      }\n    }\n  },\n  $push(target, field, arg) {\n    if (target[field] === undefined) {\n      target[field] = [];\n    }\n\n    if (!(target[field] instanceof Array)) {\n      throw MinimongoError('Cannot apply $push modifier to non-array', {field});\n    }\n\n    if (!(arg && arg.$each)) {\n      // Simple mode: not $each\n      assertHasValidFieldNames(arg);\n\n      target[field].push(arg);\n\n      return;\n    }\n\n    // Fancy mode: $each (and maybe $slice and $sort and $position)\n    const toPush = arg.$each;\n    if (!(toPush instanceof Array)) {\n      throw MinimongoError('$each must be an array', {field});\n    }\n\n    assertHasValidFieldNames(toPush);\n\n    // Parse $position\n    let position = undefined;\n    if ('$position' in arg) {\n      if (typeof arg.$position !== 'number') {\n        throw MinimongoError('$position must be a numeric value', {field});\n      }\n\n      // XXX should check to make sure integer\n      if (arg.$position < 0) {\n        throw MinimongoError(\n          '$position in $push must be zero or positive',\n          {field}\n        );\n      }\n\n      position = arg.$position;\n    }\n\n    // Parse $slice.\n    let slice = undefined;\n    if ('$slice' in arg) {\n      if (typeof arg.$slice !== 'number') {\n        throw MinimongoError('$slice must be a numeric value', {field});\n      }\n\n      // XXX should check to make sure integer\n      slice = arg.$slice;\n    }\n\n    // Parse $sort.\n    let sortFunction = undefined;\n    if (arg.$sort) {\n      if (slice === undefined) {\n        throw MinimongoError('$sort requires $slice to be present', {field});\n      }\n\n      // XXX this allows us to use a $sort whose value is an array, but that's\n      // actually an extension of the Node driver, so it won't work\n      // server-side. Could be confusing!\n      // XXX is it correct that we don't do geo-stuff here?\n      sortFunction = new Minimongo.Sorter(arg.$sort).getComparator();\n\n      toPush.forEach(element => {\n        if (LocalCollection._f._type(element) !== 3) {\n          throw MinimongoError(\n            '$push like modifiers using $sort require all elements to be ' +\n            'objects',\n            {field}\n          );\n        }\n      });\n    }\n\n    // Actually push.\n    if (position === undefined) {\n      toPush.forEach(element => {\n        target[field].push(element);\n      });\n    } else {\n      const spliceArguments = [position, 0];\n\n      toPush.forEach(element => {\n        spliceArguments.push(element);\n      });\n\n      target[field].splice(...spliceArguments);\n    }\n\n    // Actually sort.\n    if (sortFunction) {\n      target[field].sort(sortFunction);\n    }\n\n    // Actually slice.\n    if (slice !== undefined) {\n      if (slice === 0) {\n        target[field] = []; // differs from Array.slice!\n      } else if (slice < 0) {\n        target[field] = target[field].slice(slice);\n      } else {\n        target[field] = target[field].slice(0, slice);\n      }\n    }\n  },\n  $pushAll(target, field, arg) {\n    if (!(typeof arg === 'object' && arg instanceof Array)) {\n      throw MinimongoError('Modifier $pushAll/pullAll allowed for arrays only');\n    }\n\n    assertHasValidFieldNames(arg);\n\n    const toPush = target[field];\n\n    if (toPush === undefined) {\n      target[field] = arg;\n    } else if (!(toPush instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pushAll modifier to non-array',\n        {field}\n      );\n    } else {\n      toPush.push(...arg);\n    }\n  },\n  $addToSet(target, field, arg) {\n    let isEach = false;\n\n    if (typeof arg === 'object') {\n      // check if first key is '$each'\n      const keys = Object.keys(arg);\n      if (keys[0] === '$each') {\n        isEach = true;\n      }\n    }\n\n    const values = isEach ? arg.$each : [arg];\n\n    assertHasValidFieldNames(values);\n\n    const toAdd = target[field];\n    if (toAdd === undefined) {\n      target[field] = values;\n    } else if (!(toAdd instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $addToSet modifier to non-array',\n        {field}\n      );\n    } else {\n      values.forEach(value => {\n        if (toAdd.some(element => LocalCollection._f._equal(value, element))) {\n          return;\n        }\n\n        toAdd.push(value);\n      });\n    }\n  },\n  $pop(target, field, arg) {\n    if (target === undefined) {\n      return;\n    }\n\n    const toPop = target[field];\n\n    if (toPop === undefined) {\n      return;\n    }\n\n    if (!(toPop instanceof Array)) {\n      throw MinimongoError('Cannot apply $pop modifier to non-array', {field});\n    }\n\n    if (typeof arg === 'number' && arg < 0) {\n      toPop.splice(0, 1);\n    } else {\n      toPop.pop();\n    }\n  },\n  $pull(target, field, arg) {\n    if (target === undefined) {\n      return;\n    }\n\n    const toPull = target[field];\n    if (toPull === undefined) {\n      return;\n    }\n\n    if (!(toPull instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pull/pullAll modifier to non-array',\n        {field}\n      );\n    }\n\n    let out;\n    if (arg != null && typeof arg === 'object' && !(arg instanceof Array)) {\n      // XXX would be much nicer to compile this once, rather than\n      // for each document we modify.. but usually we're not\n      // modifying that many documents, so we'll let it slide for\n      // now\n\n      // XXX Minimongo.Matcher isn't up for the job, because we need\n      // to permit stuff like {$pull: {a: {$gt: 4}}}.. something\n      // like {$gt: 4} is not normally a complete selector.\n      // same issue as $elemMatch possibly?\n      const matcher = new Minimongo.Matcher(arg);\n\n      out = toPull.filter(element => !matcher.documentMatches(element).result);\n    } else {\n      out = toPull.filter(element => !LocalCollection._f._equal(element, arg));\n    }\n\n    target[field] = out;\n  },\n  $pullAll(target, field, arg) {\n    if (!(typeof arg === 'object' && arg instanceof Array)) {\n      throw MinimongoError(\n        'Modifier $pushAll/pullAll allowed for arrays only',\n        {field}\n      );\n    }\n\n    if (target === undefined) {\n      return;\n    }\n\n    const toPull = target[field];\n\n    if (toPull === undefined) {\n      return;\n    }\n\n    if (!(toPull instanceof Array)) {\n      throw MinimongoError(\n        'Cannot apply $pull/pullAll modifier to non-array',\n        {field}\n      );\n    }\n\n    target[field] = toPull.filter(object =>\n      !arg.some(element => LocalCollection._f._equal(object, element))\n    );\n  },\n  $bit(target, field, arg) {\n    // XXX mongo only supports $bit on integers, and we only support\n    // native javascript numbers (doubles) so far, so we can't support $bit\n    throw MinimongoError('$bit is not supported', {field});\n  },\n  $v() {\n    // As discussed in https://github.com/meteor/meteor/issues/9623,\n    // the `$v` operator is not needed by Meteor, but problems can occur if\n    // it's not at least callable (as of Mongo >= 3.6). It's defined here as\n    // a no-op to work around these problems.\n  }\n};\n\nconst NO_CREATE_MODIFIERS = {\n  $pop: true,\n  $pull: true,\n  $pullAll: true,\n  $rename: true,\n  $unset: true\n};\n\n// Make sure field names do not contain Mongo restricted\n// characters ('.', '$', '\\0').\n// https://docs.mongodb.com/manual/reference/limits/#Restrictions-on-Field-Names\nconst invalidCharMsg = {\n  $: 'start with \\'$\\'',\n  '.': 'contain \\'.\\'',\n  '\\0': 'contain null bytes'\n};\n\n// checks if all field names in an object are valid\nfunction assertHasValidFieldNames(doc) {\n  if (doc && typeof doc === 'object') {\n    JSON.stringify(doc, (key, value) => {\n      assertIsValidFieldName(key);\n      return value;\n    });\n  }\n}\n\nfunction assertIsValidFieldName(key) {\n  let match;\n  if (typeof key === 'string' && (match = key.match(/^\\$|\\.|\\0/))) {\n    throw MinimongoError(`Key ${key} must not ${invalidCharMsg[match[0]]}`);\n  }\n}\n\n// for a.b.c.2.d.e, keyparts should be ['a', 'b', 'c', '2', 'd', 'e'],\n// and then you would operate on the 'e' property of the returned\n// object.\n//\n// if options.noCreate is falsey, creates intermediate levels of\n// structure as necessary, like mkdir -p (and raises an exception if\n// that would mean giving a non-numeric property to an array.) if\n// options.noCreate is true, return undefined instead.\n//\n// may modify the last element of keyparts to signal to the caller that it needs\n// to use a different value to index into the returned object (for example,\n// ['a', '01'] -> ['a', 1]).\n//\n// if forbidArray is true, return null if the keypath goes through an array.\n//\n// if options.arrayIndices is set, use its first element for the (first) '$' in\n// the path.\nfunction findModTarget(doc, keyparts, options = {}) {\n  let usedArrayIndex = false;\n\n  for (let i = 0; i < keyparts.length; i++) {\n    const last = i === keyparts.length - 1;\n    let keypart = keyparts[i];\n\n    if (!isIndexable(doc)) {\n      if (options.noCreate) {\n        return undefined;\n      }\n\n      const error = MinimongoError(\n        `cannot use the part '${keypart}' to traverse ${doc}`\n      );\n      error.setPropertyError = true;\n      throw error;\n    }\n\n    if (doc instanceof Array) {\n      if (options.forbidArray) {\n        return null;\n      }\n\n      if (keypart === '$') {\n        if (usedArrayIndex) {\n          throw MinimongoError('Too many positional (i.e. \\'$\\') elements');\n        }\n\n        if (!options.arrayIndices || !options.arrayIndices.length) {\n          throw MinimongoError(\n            'The positional operator did not find the match needed from the ' +\n            'query'\n          );\n        }\n\n        keypart = options.arrayIndices[0];\n        usedArrayIndex = true;\n      } else if (isNumericKey(keypart)) {\n        keypart = parseInt(keypart);\n      } else {\n        if (options.noCreate) {\n          return undefined;\n        }\n\n        throw MinimongoError(\n          `can't append to array using string field name [${keypart}]`\n        );\n      }\n\n      if (last) {\n        keyparts[i] = keypart; // handle 'a.01'\n      }\n\n      if (options.noCreate && keypart >= doc.length) {\n        return undefined;\n      }\n\n      while (doc.length < keypart) {\n        doc.push(null);\n      }\n\n      if (!last) {\n        if (doc.length === keypart) {\n          doc.push({});\n        } else if (typeof doc[keypart] !== 'object') {\n          throw MinimongoError(\n            `can't modify field '${keyparts[i + 1]}' of list value ` +\n            JSON.stringify(doc[keypart])\n          );\n        }\n      }\n    } else {\n      assertIsValidFieldName(keypart);\n\n      if (!(keypart in doc)) {\n        if (options.noCreate) {\n          return undefined;\n        }\n\n        if (!last) {\n          doc[keypart] = {};\n        }\n      }\n    }\n\n    if (last) {\n      return doc;\n    }\n\n    doc = doc[keypart];\n  }\n\n  // notreached\n}\n", "import LocalCollection from './local_collection.js';\nimport {\n  compileDocumentSelector,\n  hasOwn,\n  nothingMatcher,\n} from './common.js';\n\nconst Decimal = Package['mongo-decimal']?.Decimal || class DecimalStub {}\n\n// The minimongo selector compiler!\n\n// Terminology:\n//  - a 'selector' is the EJSON object representing a selector\n//  - a 'matcher' is its compiled form (whether a full Minimongo.Matcher\n//    object or one of the component lambdas that matches parts of it)\n//  - a 'result object' is an object with a 'result' field and maybe\n//    distance and arrayIndices.\n//  - a 'branched value' is an object with a 'value' field and maybe\n//    'dontIterate' and 'arrayIndices'.\n//  - a 'document' is a top-level object that can be stored in a collection.\n//  - a 'lookup function' is a function that takes in a document and returns\n//    an array of 'branched values'.\n//  - a 'branched matcher' maps from an array of branched values to a result\n//    object.\n//  - an 'element matcher' maps from a single value to a bool.\n\n// Main entry point.\n//   var matcher = new Minimongo.Matcher({a: {$gt: 5}});\n//   if (matcher.documentMatches({a: 7})) ...\nexport default class Matcher {\n  constructor(selector, isUpdate) {\n    // A set (object mapping string -> *) of all of the document paths looked\n    // at by the selector. Also includes the empty string if it may look at any\n    // path (eg, $where).\n    this._paths = {};\n    // Set to true if compilation finds a $near.\n    this._hasGeoQuery = false;\n    // Set to true if compilation finds a $where.\n    this._hasWhere = false;\n    // Set to false if compilation finds anything other than a simple equality\n    // or one or more of '$gt', '$gte', '$lt', '$lte', '$ne', '$in', '$nin' used\n    // with scalars as operands.\n    this._isSimple = true;\n    // Set to a dummy document which always matches this Matcher. Or set to null\n    // if such document is too hard to find.\n    this._matchingDocument = undefined;\n    // A clone of the original selector. It may just be a function if the user\n    // passed in a function; otherwise is definitely an object (eg, IDs are\n    // translated into {_id: ID} first. Used by canBecomeTrueByModifier and\n    // Sorter._useWithMatcher.\n    this._selector = null;\n    this._docMatcher = this._compileSelector(selector);\n    // Set to true if selection is done for an update operation\n    // Default is false\n    // Used for $near array update (issue #3599)\n    this._isUpdate = isUpdate;\n  }\n\n  documentMatches(doc) {\n    if (doc !== Object(doc)) {\n      throw Error('documentMatches needs a document');\n    }\n\n    return this._docMatcher(doc);\n  }\n\n  hasGeoQuery() {\n    return this._hasGeoQuery;\n  }\n\n  hasWhere() {\n    return this._hasWhere;\n  }\n\n  isSimple() {\n    return this._isSimple;\n  }\n\n  // Given a selector, return a function that takes one argument, a\n  // document. It returns a result object.\n  _compileSelector(selector) {\n    // you can pass a literal function instead of a selector\n    if (selector instanceof Function) {\n      this._isSimple = false;\n      this._selector = selector;\n      this._recordPathUsed('');\n\n      return doc => ({result: !!selector.call(doc)});\n    }\n\n    // shorthand -- scalar _id\n    if (LocalCollection._selectorIsId(selector)) {\n      this._selector = {_id: selector};\n      this._recordPathUsed('_id');\n\n      return doc => ({result: EJSON.equals(doc._id, selector)});\n    }\n\n    // protect against dangerous selectors.  falsey and {_id: falsey} are both\n    // likely programmer error, and not what you want, particularly for\n    // destructive operations.\n    if (!selector || hasOwn.call(selector, '_id') && !selector._id) {\n      this._isSimple = false;\n      return nothingMatcher;\n    }\n\n    // Top level can't be an array or true or binary.\n    if (Array.isArray(selector) ||\n        EJSON.isBinary(selector) ||\n        typeof selector === 'boolean') {\n      throw new Error(`Invalid selector: ${selector}`);\n    }\n\n    this._selector = EJSON.clone(selector);\n\n    return compileDocumentSelector(selector, this, {isRoot: true});\n  }\n\n  // Returns a list of key paths the given selector is looking for. It includes\n  // the empty string if there is a $where.\n  _getPaths() {\n    return Object.keys(this._paths);\n  }\n\n  _recordPathUsed(path) {\n    this._paths[path] = true;\n  }\n}\n\n// helpers used by compiled selector code\nLocalCollection._f = {\n  // XXX for _all and _in, consider building 'inquery' at compile time..\n  _type(v) {\n    if (typeof v === 'number') {\n      return 1;\n    }\n\n    if (typeof v === 'string') {\n      return 2;\n    }\n\n    if (typeof v === 'boolean') {\n      return 8;\n    }\n\n    if (Array.isArray(v)) {\n      return 4;\n    }\n\n    if (v === null) {\n      return 10;\n    }\n\n    // note that typeof(/x/) === \"object\"\n    if (v instanceof RegExp) {\n      return 11;\n    }\n\n    if (typeof v === 'function') {\n      return 13;\n    }\n\n    if (v instanceof Date) {\n      return 9;\n    }\n\n    if (EJSON.isBinary(v)) {\n      return 5;\n    }\n\n    if (v instanceof MongoID.ObjectID) {\n      return 7;\n    }\n\n    if (v instanceof Decimal) {\n      return 1;\n    }\n\n    // object\n    return 3;\n\n    // XXX support some/all of these:\n    // 14, symbol\n    // 15, javascript code with scope\n    // 16, 18: 32-bit/64-bit integer\n    // 17, timestamp\n    // 255, minkey\n    // 127, maxkey\n  },\n\n  // deep equality test: use for literal document and array matches\n  _equal(a, b) {\n    return EJSON.equals(a, b, {keyOrderSensitive: true});\n  },\n\n  // maps a type code to a value that can be used to sort values of different\n  // types\n  _typeorder(t) {\n    // http://www.mongodb.org/display/DOCS/What+is+the+Compare+Order+for+BSON+Types\n    // XXX what is the correct sort position for Javascript code?\n    // ('100' in the matrix below)\n    // XXX minkey/maxkey\n    return [\n      -1,  // (not a type)\n      1,   // number\n      2,   // string\n      3,   // object\n      4,   // array\n      5,   // binary\n      -1,  // deprecated\n      6,   // ObjectID\n      7,   // bool\n      8,   // Date\n      0,   // null\n      9,   // RegExp\n      -1,  // deprecated\n      100, // JS code\n      2,   // deprecated (symbol)\n      100, // JS code\n      1,   // 32-bit int\n      8,   // Mongo timestamp\n      1    // 64-bit int\n    ][t];\n  },\n\n  // compare two values of unknown type according to BSON ordering\n  // semantics. (as an extension, consider 'undefined' to be less than\n  // any other value.) return negative if a is less, positive if b is\n  // less, or 0 if equal\n  _cmp(a, b) {\n    if (a === undefined) {\n      return b === undefined ? 0 : -1;\n    }\n\n    if (b === undefined) {\n      return 1;\n    }\n\n    let ta = LocalCollection._f._type(a);\n    let tb = LocalCollection._f._type(b);\n\n    const oa = LocalCollection._f._typeorder(ta);\n    const ob = LocalCollection._f._typeorder(tb);\n\n    if (oa !== ob) {\n      return oa < ob ? -1 : 1;\n    }\n\n    // XXX need to implement this if we implement Symbol or integers, or\n    // Timestamp\n    if (ta !== tb) {\n      throw Error('Missing type coercion logic in _cmp');\n    }\n\n    if (ta === 7) { // ObjectID\n      // Convert to string.\n      ta = tb = 2;\n      a = a.toHexString();\n      b = b.toHexString();\n    }\n\n    if (ta === 9) { // Date\n      // Convert to millis.\n      ta = tb = 1;\n      a = isNaN(a) ? 0 : a.getTime();\n      b = isNaN(b) ? 0 : b.getTime();\n    }\n\n    if (ta === 1) { // double\n      if (a instanceof Decimal) {\n        return a.minus(b).toNumber();\n      } else {\n        return a - b;\n      }\n    }\n\n    if (tb === 2) // string\n      return a < b ? -1 : a === b ? 0 : 1;\n\n    if (ta === 3) { // Object\n      // this could be much more efficient in the expected case ...\n      const toArray = object => {\n        const result = [];\n\n        Object.keys(object).forEach(key => {\n          result.push(key, object[key]);\n        });\n\n        return result;\n      };\n\n      return LocalCollection._f._cmp(toArray(a), toArray(b));\n    }\n\n    if (ta === 4) { // Array\n      for (let i = 0; ; i++) {\n        if (i === a.length) {\n          return i === b.length ? 0 : -1;\n        }\n\n        if (i === b.length) {\n          return 1;\n        }\n\n        const s = LocalCollection._f._cmp(a[i], b[i]);\n        if (s !== 0) {\n          return s;\n        }\n      }\n    }\n\n    if (ta === 5) { // binary\n      // Surprisingly, a small binary blob is always less than a large one in\n      // Mongo.\n      if (a.length !== b.length) {\n        return a.length - b.length;\n      }\n\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] < b[i]) {\n          return -1;\n        }\n\n        if (a[i] > b[i]) {\n          return 1;\n        }\n      }\n\n      return 0;\n    }\n\n    if (ta === 8) { // boolean\n      if (a) {\n        return b ? 0 : 1;\n      }\n\n      return b ? -1 : 0;\n    }\n\n    if (ta === 10) // null\n      return 0;\n\n    if (ta === 11) // regexp\n      throw Error('Sorting not supported on regular expression'); // XXX\n\n    // 13: javascript code\n    // 14: symbol\n    // 15: javascript code with scope\n    // 16: 32-bit integer\n    // 17: timestamp\n    // 18: 64-bit integer\n    // 255: minkey\n    // 127: maxkey\n    if (ta === 13) // javascript code\n      throw Error('Sorting not supported on Javascript code'); // XXX\n\n    throw Error('Unknown type to sort');\n  },\n};\n", "import LocalCollection_ from './local_collection.js';\nimport Matcher from './matcher.js';\nimport Sorter from './sorter.js';\n\nLocalCollection = LocalCollection_;\nMinimongo = {\n    LocalCollection: LocalCollection_,\n    Matcher,\n    Sorter\n};\n", "// ObserveHandle: the return value of a live query.\nexport default class ObserveHandle {}\n", "import {\n  ELEMENT_OPERATORS,\n  equalityElementMatcher,\n  expandArraysInBranches,\n  hasOwn,\n  isOperatorObject,\n  makeLookupFunction,\n  regexpElementMatcher,\n} from './common.js';\n\n// Give a sort spec, which can be in any of these forms:\n//   {\"key1\": 1, \"key2\": -1}\n//   [[\"key1\", \"asc\"], [\"key2\", \"desc\"]]\n//   [\"key1\", [\"key2\", \"desc\"]]\n//\n// (.. with the first form being dependent on the key enumeration\n// behavior of your javascript VM, which usually does what you mean in\n// this case if the key names don't look like integers ..)\n//\n// return a function that takes two objects, and returns -1 if the\n// first object comes first in order, 1 if the second object comes\n// first, or 0 if neither object comes before the other.\n\nexport default class Sorter {\n  constructor(spec) {\n    this._sortSpecParts = [];\n    this._sortFunction = null;\n\n    const addSpecPart = (path, ascending) => {\n      if (!path) {\n        throw Error('sort keys must be non-empty');\n      }\n\n      if (path.charAt(0) === '$') {\n        throw Error(`unsupported sort key: ${path}`);\n      }\n\n      this._sortSpecParts.push({\n        ascending,\n        lookup: makeLookupFunction(path, {forSort: true}),\n        path\n      });\n    };\n\n    if (spec instanceof Array) {\n      spec.forEach(element => {\n        if (typeof element === 'string') {\n          addSpecPart(element, true);\n        } else {\n          addSpecPart(element[0], element[1] !== 'desc');\n        }\n      });\n    } else if (typeof spec === 'object') {\n      Object.keys(spec).forEach(key => {\n        addSpecPart(key, spec[key] >= 0);\n      });\n    } else if (typeof spec === 'function') {\n      this._sortFunction = spec;\n    } else {\n      throw Error(`Bad sort specification: ${JSON.stringify(spec)}`);\n    }\n\n    // If a function is specified for sorting, we skip the rest.\n    if (this._sortFunction) {\n      return;\n    }\n\n    // To implement affectedByModifier, we piggy-back on top of Matcher's\n    // affectedByModifier code; we create a selector that is affected by the\n    // same modifiers as this sort order. This is only implemented on the\n    // server.\n    if (this.affectedByModifier) {\n      const selector = {};\n\n      this._sortSpecParts.forEach(spec => {\n        selector[spec.path] = 1;\n      });\n\n      this._selectorForAffectedByModifier = new Minimongo.Matcher(selector);\n    }\n\n    this._keyComparator = composeComparators(\n      this._sortSpecParts.map((spec, i) => this._keyFieldComparator(i))\n    );\n  }\n\n  getComparator(options) {\n    // If sort is specified or have no distances, just use the comparator from\n    // the source specification (which defaults to \"everything is equal\".\n    // issue #3599\n    // https://docs.mongodb.com/manual/reference/operator/query/near/#sort-operation\n    // sort effectively overrides $near\n    if (this._sortSpecParts.length || !options || !options.distances) {\n      return this._getBaseComparator();\n    }\n\n    const distances = options.distances;\n\n    // Return a comparator which compares using $near distances.\n    return (a, b) => {\n      if (!distances.has(a._id)) {\n        throw Error(`Missing distance for ${a._id}`);\n      }\n\n      if (!distances.has(b._id)) {\n        throw Error(`Missing distance for ${b._id}`);\n      }\n\n      return distances.get(a._id) - distances.get(b._id);\n    };\n  }\n\n  // Takes in two keys: arrays whose lengths match the number of spec\n  // parts. Returns negative, 0, or positive based on using the sort spec to\n  // compare fields.\n  _compareKeys(key1, key2) {\n    if (key1.length !== this._sortSpecParts.length ||\n        key2.length !== this._sortSpecParts.length) {\n      throw Error('Key has wrong length');\n    }\n\n    return this._keyComparator(key1, key2);\n  }\n\n  // Iterates over each possible \"key\" from doc (ie, over each branch), calling\n  // 'cb' with the key.\n  _generateKeysFromDoc(doc, cb) {\n    if (this._sortSpecParts.length === 0) {\n      throw new Error('can\\'t generate keys without a spec');\n    }\n\n    const pathFromIndices = indices => `${indices.join(',')},`;\n\n    let knownPaths = null;\n\n    // maps index -> ({'' -> value} or {path -> value})\n    const valuesByIndexAndPath = this._sortSpecParts.map(spec => {\n      // Expand any leaf arrays that we find, and ignore those arrays\n      // themselves.  (We never sort based on an array itself.)\n      let branches = expandArraysInBranches(spec.lookup(doc), true);\n\n      // If there are no values for a key (eg, key goes to an empty array),\n      // pretend we found one undefined value.\n      if (!branches.length) {\n        branches = [{ value: void 0 }];\n      }\n\n      const element = Object.create(null);\n      let usedPaths = false;\n\n      branches.forEach(branch => {\n        if (!branch.arrayIndices) {\n          // If there are no array indices for a branch, then it must be the\n          // only branch, because the only thing that produces multiple branches\n          // is the use of arrays.\n          if (branches.length > 1) {\n            throw Error('multiple branches but no array used?');\n          }\n\n          element[''] = branch.value;\n          return;\n        }\n\n        usedPaths = true;\n\n        const path = pathFromIndices(branch.arrayIndices);\n\n        if (hasOwn.call(element, path)) {\n          throw Error(`duplicate path: ${path}`);\n        }\n\n        element[path] = branch.value;\n\n        // If two sort fields both go into arrays, they have to go into the\n        // exact same arrays and we have to find the same paths.  This is\n        // roughly the same condition that makes MongoDB throw this strange\n        // error message.  eg, the main thing is that if sort spec is {a: 1,\n        // b:1} then a and b cannot both be arrays.\n        //\n        // (In MongoDB it seems to be OK to have {a: 1, 'a.x.y': 1} where 'a'\n        // and 'a.x.y' are both arrays, but we don't allow this for now.\n        // #NestedArraySort\n        // XXX achieve full compatibility here\n        if (knownPaths && !hasOwn.call(knownPaths, path)) {\n          throw Error('cannot index parallel arrays');\n        }\n      });\n\n      if (knownPaths) {\n        // Similarly to above, paths must match everywhere, unless this is a\n        // non-array field.\n        if (!hasOwn.call(element, '') &&\n            Object.keys(knownPaths).length !== Object.keys(element).length) {\n          throw Error('cannot index parallel arrays!');\n        }\n      } else if (usedPaths) {\n        knownPaths = {};\n\n        Object.keys(element).forEach(path => {\n          knownPaths[path] = true;\n        });\n      }\n\n      return element;\n    });\n\n    if (!knownPaths) {\n      // Easy case: no use of arrays.\n      const soleKey = valuesByIndexAndPath.map(values => {\n        if (!hasOwn.call(values, '')) {\n          throw Error('no value in sole key case?');\n        }\n\n        return values[''];\n      });\n\n      cb(soleKey);\n\n      return;\n    }\n\n    Object.keys(knownPaths).forEach(path => {\n      const key = valuesByIndexAndPath.map(values => {\n        if (hasOwn.call(values, '')) {\n          return values[''];\n        }\n\n        if (!hasOwn.call(values, path)) {\n          throw Error('missing path?');\n        }\n\n        return values[path];\n      });\n\n      cb(key);\n    });\n  }\n\n  // Returns a comparator that represents the sort specification (but not\n  // including a possible geoquery distance tie-breaker).\n  _getBaseComparator() {\n    if (this._sortFunction) {\n      return this._sortFunction;\n    }\n\n    // If we're only sorting on geoquery distance and no specs, just say\n    // everything is equal.\n    if (!this._sortSpecParts.length) {\n      return (doc1, doc2) => 0;\n    }\n\n    return (doc1, doc2) => {\n      const key1 = this._getMinKeyFromDoc(doc1);\n      const key2 = this._getMinKeyFromDoc(doc2);\n      return this._compareKeys(key1, key2);\n    };\n  }\n\n  // Finds the minimum key from the doc, according to the sort specs.  (We say\n  // \"minimum\" here but this is with respect to the sort spec, so \"descending\"\n  // sort fields mean we're finding the max for that field.)\n  //\n  // Note that this is NOT \"find the minimum value of the first field, the\n  // minimum value of the second field, etc\"... it's \"choose the\n  // lexicographically minimum value of the key vector, allowing only keys which\n  // you can find along the same paths\".  ie, for a doc {a: [{x: 0, y: 5}, {x:\n  // 1, y: 3}]} with sort spec {'a.x': 1, 'a.y': 1}, the only keys are [0,5] and\n  // [1,3], and the minimum key is [0,5]; notably, [0,3] is NOT a key.\n  _getMinKeyFromDoc(doc) {\n    let minKey = null;\n\n    this._generateKeysFromDoc(doc, key => {\n      if (minKey === null) {\n        minKey = key;\n        return;\n      }\n\n      if (this._compareKeys(key, minKey) < 0) {\n        minKey = key;\n      }\n    });\n\n    return minKey;\n  }\n\n  _getPaths() {\n    return this._sortSpecParts.map(part => part.path);\n  }\n\n  // Given an index 'i', returns a comparator that compares two key arrays based\n  // on field 'i'.\n  _keyFieldComparator(i) {\n    const invert = !this._sortSpecParts[i].ascending;\n\n    return (key1, key2) => {\n      const compare = LocalCollection._f._cmp(key1[i], key2[i]);\n      return invert ? -compare : compare;\n    };\n  }\n}\n\n// Given an array of comparators\n// (functions (a,b)->(negative or positive or zero)), returns a single\n// comparator which uses each comparator in order and returns the first\n// non-zero value.\nfunction composeComparators(comparatorArray) {\n  return (a, b) => {\n    for (let i = 0; i < comparatorArray.length; ++i) {\n      const compare = comparatorArray[i](a, b);\n      if (compare !== 0) {\n        return compare;\n      }\n    }\n\n    return 0;\n  };\n}\n"]}