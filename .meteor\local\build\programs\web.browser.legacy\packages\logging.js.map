{"version": 3, "sources": ["meteor://💻app/packages/logging/logging.js", "meteor://💻app/packages/logging/logging_browser.js"], "names": ["_objectSpread", "module", "link", "default", "v", "_createForOfIteratorHelperLoose", "_typeof", "export", "Log", "Meteor", "hasOwn", "Object", "prototype", "hasOwnProperty", "info", "apply", "arguments", "intercept", "interceptedLines", "suppress", "_intercept", "count", "_suppress", "_intercepted", "lines", "outputFormat", "showTime", "LEVEL_COLORS", "debug", "warn", "error", "META_COLOR", "isWin32", "process", "platform", "platformColor", "color", "endsWith", "RESTRICTED_KEYS", "FORMATTED_KEYS", "concat", "logInBrowser", "obj", "str", "format", "level", "console", "log", "Function", "bind", "call", "_getCallerDetails", "getStack", "err", "Error", "stack", "line", "split", "slice", "_iterator", "_step", "done", "value", "match", "file", "details", "exec", "for<PERSON>ach", "arg", "intercepted", "RegExp", "Date", "message", "String", "toString", "key", "omitCallerDetails", "time", "isProduction", "push", "EJSON", "stringify", "isServer", "parse", "startsWith", "e", "options", "length", "undefined", "_obj", "timeInexact", "_obj$level", "lineNumber", "_obj$app", "app", "appName", "originApp", "_obj$message", "_obj$program", "program", "_obj$satellite", "satellite", "_obj$stderr", "stderr", "keys", "pad2", "n", "padStart", "pad3", "dateStamp", "getFullYear", "getMonth", "getDate", "timeStamp", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "utcOffsetStr", "getTimezoneOffset", "appInfo", "sourceInfoParts", "sourceInfo", "join", "stderrIndicator", "timeString", "metaPrefix", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON>", "prettify", "metaColor", "objFromText", "override"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,aAAa;AAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,+BAA+B;AAACJ,MAAM,CAACC,IAAI,CAAC,uDAAuD,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,+BAA+B,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,OAAO;AAACL,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACE,OAAO,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA9WH,MAAM,CAACM,MAAM,CAAC;EAACC,GAAG,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,GAAG;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACR,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACO,MAAM,EAAC,SAAAA,CAASL,CAAC,EAAC;IAACK,MAAM,GAACL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAEpH,IAAMM,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,SAASL,GAAGA,CAAA,EAAU;EACpBA,GAAG,CAACM,IAAI,CAAAC,KAAA,CAARP,GAAG,EAAAQ,SAAa,CAAC;AACnB;;AAEA;AACA,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,QAAQ,GAAG,CAAC;;AAEhB;AACA;AACA;AACAX,GAAG,CAACY,UAAU,GAAG,UAACC,KAAK,EAAK;EAC1BJ,SAAS,IAAII,KAAK;AACpB,CAAC;;AAED;AACA;AACA;AACAb,GAAG,CAACc,SAAS,GAAG,UAACD,KAAK,EAAK;EACzBF,QAAQ,IAAIE,KAAK;AACnB,CAAC;;AAED;AACAb,GAAG,CAACe,YAAY,GAAG,YAAM;EACvB,IAAMC,KAAK,GAAGN,gBAAgB;EAC9BA,gBAAgB,GAAG,EAAE;EACrBD,SAAS,GAAG,CAAC;EACb,OAAOO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAhB,GAAG,CAACiB,YAAY,GAAG,MAAM;;AAEzB;AACA;AACA;AACAjB,GAAG,CAACkB,QAAQ,GAAG,IAAI;AAEnB,IAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,OAAO;EACd;EACAC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;AACT,CAAC;AAED,IAAMC,UAAU,GAAG,MAAM;;AAEzB;AACA;AACA;AACA;AACA,IAAMC,OAAO,GAAG,QAAOC,OAAO,iCAAA3B,OAAA,CAAP2B,OAAO,OAAK,QAAQ,IAAIA,OAAO,CAACC,QAAQ,KAAK,OAAO;AAC3E,IAAMC,aAAa,GAAG,SAAAA,CAACC,KAAK,EAAK;EAC/B,IAAIJ,OAAO,IAAI,OAAOI,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;IACrE,OAAUD,KAAK;EACjB;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA,IAAME,eAAe,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAC/C,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC;AAEtE,IAAMC,cAAc,MAAAC,MAAA,CAAOF,eAAe,GAAE,KAAK,EAAE,SAAS,EAAC;AAE7D,IAAMG,YAAY,GAAG,SAAAA,CAAAC,GAAG,EAAI;EAC1B,IAAMC,GAAG,GAAGnC,GAAG,CAACoC,MAAM,CAACF,GAAG,CAAC;;EAE3B;EACA,IAAMG,KAAK,GAAGH,GAAG,CAACG,KAAK;EAEvB,IAAK,OAAOC,OAAO,KAAK,WAAW,IAAKA,OAAO,CAACD,KAAK,CAAC,EAAE;IACtDC,OAAO,CAACD,KAAK,CAAC,CAACF,GAAG,CAAC;EACrB,CAAC,MAAM;IACL;IACA;IACA;IACA,IAAI,OAAOG,OAAO,CAACC,GAAG,CAAChC,KAAK,KAAK,UAAU,EAAE;MAC3C;MACA+B,OAAO,CAACC,GAAG,CAAChC,KAAK,CAAC+B,OAAO,EAAE,CAACH,GAAG,CAAC,CAAC;IAEnC,CAAC,MAAM,IAAI,OAAOK,QAAQ,CAACpC,SAAS,CAACqC,IAAI,KAAK,UAAU,EAAE;MACxD;MACA,IAAMF,GAAG,GAAGC,QAAQ,CAACpC,SAAS,CAACqC,IAAI,CAACC,IAAI,CAACJ,OAAO,CAACC,GAAG,EAAED,OAAO,CAAC;MAC9DC,GAAG,CAAChC,KAAK,CAAC+B,OAAO,EAAE,CAACH,GAAG,CAAC,CAAC;IAC3B;EACF;AACF,CAAC;;AAED;AACAnC,GAAG,CAAC2C,iBAAiB,GAAG,YAAM;EAC5B,IAAMC,QAAQ,GAAG,SAAAA,CAAA,EAAM;IACrB;IACA;IACA;IACA,IAAMC,GAAG,GAAG,IAAIC,KAAK,CAAD,CAAC;IACrB,IAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK;IACvB,OAAOA,KAAK;EACd,CAAC;EAED,IAAMA,KAAK,GAAGH,QAAQ,CAAC,CAAC;EAExB,IAAI,CAACG,KAAK,EAAE,OAAO,CAAC,CAAC;;EAErB;EACA;EACA,IAAIC,IAAI;EACR,IAAMhC,KAAK,GAAG+B,KAAK,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;EACxC,SAAAC,SAAA,GAAAtD,+BAAA,CAAamB,KAAK,GAAAoC,KAAA,IAAAA,KAAA,GAAAD,SAAA,IAAAE,IAAA,GAAE;IAAfL,IAAI,GAAAI,KAAA,CAAAE,KAAA;IACP,IAAIN,IAAI,CAACO,KAAK,CAAC,8BAA8B,CAAC,EAAE;MAC9C,OAAO;QAACC,IAAI,EAAE;MAAM,CAAC;IACvB;IAEA,IAAI,CAACR,IAAI,CAACO,KAAK,CAAC,iDAAiD,CAAC,EAAE;MAClE;IACF;EACF;EAEA,IAAME,OAAO,GAAG,CAAC,CAAC;;EAElB;EACA;EACA;EACA,IAAMF,KAAK,GAAG,yCAAyC,CAACG,IAAI,CAACV,IAAI,CAAC;EAClE,IAAI,CAACO,KAAK,EAAE;IACV,OAAOE,OAAO;EAChB;;EAEA;EACAA,OAAO,CAACT,IAAI,GAAGO,KAAK,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAErC;EACA;EACA;EACAQ,OAAO,CAACD,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAE7D,OAAOQ,OAAO;AAChB,CAAC;AAED,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAACE,OAAO,CAAC,UAACtB,KAAK,EAAK;EACrD;EACArC,GAAG,CAACqC,KAAK,CAAC,GAAG,UAACuB,GAAG,EAAK;IACrB,IAAIjD,QAAQ,EAAE;MACZA,QAAQ,EAAE;MACV;IACF;IAEA,IAAIkD,WAAW,GAAG,KAAK;IACvB,IAAIpD,SAAS,EAAE;MACbA,SAAS,EAAE;MACXoD,WAAW,GAAG,IAAI;IACpB;IAEA,IAAI3B,GAAG,GAAI0B,GAAG,KAAKzD,MAAM,CAACyD,GAAG,CAAC,IACzB,EAAEA,GAAG,YAAYE,MAAM,CAAC,IACxB,EAAEF,GAAG,YAAYG,IAAI,CAAC,GACvBH,GAAG,GACH;MAAEI,OAAO,EAAE,IAAIC,MAAM,CAACL,GAAG,CAAC,CAACM,QAAQ,CAAC;IAAE,CAAC;IAE3CpC,eAAe,CAAC6B,OAAO,CAAC,UAAAQ,GAAG,EAAI;MAC7B,IAAIjC,GAAG,CAACiC,GAAG,CAAC,EAAE;QACZ,MAAM,IAAIrB,KAAK,iBAAeqB,GAAG,qBAAkB,CAAC;MACtD;IACF,CAAC,CAAC;IAEF,IAAIjE,MAAM,CAACwC,IAAI,CAACR,GAAG,EAAE,SAAS,CAAC,IAAI,OAAOA,GAAG,CAAC8B,OAAO,KAAK,QAAQ,EAAE;MAClE,MAAM,IAAIlB,KAAK,CAAC,qDAAqD,CAAC;IACxE;IAEA,IAAI,CAACZ,GAAG,CAACkC,iBAAiB,EAAE;MAC1BlC,GAAG,GAAA1C,aAAA,CAAAA,aAAA,KAAQQ,GAAG,CAAC2C,iBAAiB,CAAC,CAAC,GAAKT,GAAG,CAAE;IAC9C;IAEAA,GAAG,CAACmC,IAAI,GAAG,IAAIN,IAAI,CAAC,CAAC;IACrB7B,GAAG,CAACG,KAAK,GAAGA,KAAK;;IAEjB;IACA,IAAIA,KAAK,KAAK,OAAO,IAAIpC,MAAM,CAACqE,YAAY,EAAE;MAC5C;IACF;IAEA,IAAIT,WAAW,EAAE;MACfnD,gBAAgB,CAAC6D,IAAI,CAACC,KAAK,CAACC,SAAS,CAACvC,GAAG,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIjC,MAAM,CAACyE,QAAQ,EAAE;MAC1B,IAAI1E,GAAG,CAACiB,YAAY,KAAK,cAAc,EAAE;QACvCqB,OAAO,CAACC,GAAG,CAACvC,GAAG,CAACoC,MAAM,CAACF,GAAG,EAAE;UAACN,KAAK,EAAE;QAAI,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAI5B,GAAG,CAACiB,YAAY,KAAK,MAAM,EAAE;QACtCqB,OAAO,CAACC,GAAG,CAACiC,KAAK,CAACC,SAAS,CAACvC,GAAG,CAAC,CAAC;MACnC,CAAC,MAAM;QACL,MAAM,IAAIY,KAAK,qCAAmC9C,GAAG,CAACiB,YAAc,CAAC;MACvE;IACF,CAAC,MAAM;MACLgB,YAAY,CAACC,GAAG,CAAC;IACnB;EACF,CAAC;AACD,CAAC,CAAC;;AAGF;AACAlC,GAAG,CAAC2E,KAAK,GAAG,UAAC3B,IAAI,EAAK;EACpB,IAAId,GAAG,GAAG,IAAI;EACd,IAAIc,IAAI,IAAIA,IAAI,CAAC4B,UAAU,CAAC,GAAG,CAAC,EAAE;IAAE;IAClC,IAAI;MAAE1C,GAAG,GAAGsC,KAAK,CAACG,KAAK,CAAC3B,IAAI,CAAC;IAAE,CAAC,CAAC,OAAO6B,CAAC,EAAE,CAAC;EAC9C;;EAEA;EACA,IAAI3C,GAAG,IAAIA,GAAG,CAACmC,IAAI,IAAKnC,GAAG,CAACmC,IAAI,YAAYN,IAAK,EAAE;IACjD,OAAO7B,GAAG;EACZ,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACAlC,GAAG,CAACoC,MAAM,GAAG,UAACF,GAAG,EAAmB;EAAA,IAAjB4C,OAAO,GAAAtE,SAAA,CAAAuE,MAAA,QAAAvE,SAAA,QAAAwE,SAAA,GAAAxE,SAAA,MAAG,CAAC,CAAC;EAC7B0B,GAAG,GAAA1C,aAAA,KAAQ0C,GAAG,CAAE,CAAC,CAAC;EAClB,IAAA+C,IAAA,GAYI/C,GAAG;IAXLmC,IAAI,GAAAY,IAAA,CAAJZ,IAAI;IACJa,WAAW,GAAAD,IAAA,CAAXC,WAAW;IAAAC,UAAA,GAAAF,IAAA,CACX5C,KAAK;IAALA,KAAK,GAAA8C,UAAA,cAAG,MAAM,GAAAA,UAAA;IACd3B,IAAI,GAAAyB,IAAA,CAAJzB,IAAI;IACE4B,UAAU,GAAAH,IAAA,CAAhBjC,IAAI;IAAAqC,QAAA,GAAAJ,IAAA,CACJK,GAAG;IAAEC,OAAO,GAAAF,QAAA,cAAG,EAAE,GAAAA,QAAA;IACjBG,SAAS,GAAAP,IAAA,CAATO,SAAS;IAAAC,YAAA,GAAAR,IAAA,CACTjB,OAAO;IAAPA,OAAO,GAAAyB,YAAA,cAAG,EAAE,GAAAA,YAAA;IAAAC,YAAA,GAAAT,IAAA,CACZU,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,EAAE,GAAAA,YAAA;IAAAE,cAAA,GAAAX,IAAA,CACZY,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG,EAAE,GAAAA,cAAA;IAAAE,WAAA,GAAAb,IAAA,CACdc,MAAM;IAANA,MAAM,GAAAD,WAAA,cAAG,EAAE,GAAAA,WAAA;EAGb,IAAI,EAAEzB,IAAI,YAAYN,IAAI,CAAC,EAAE;IAC3B,MAAM,IAAIjB,KAAK,CAAC,8BAA8B,CAAC;EACjD;EAEAf,cAAc,CAAC4B,OAAO,CAAC,UAACQ,GAAG,EAAK;IAAE,OAAOjC,GAAG,CAACiC,GAAG,CAAC;EAAE,CAAC,CAAC;EAErD,IAAIhE,MAAM,CAAC6F,IAAI,CAAC9D,GAAG,CAAC,CAAC6C,MAAM,GAAG,CAAC,EAAE;IAC/B,IAAIf,OAAO,EAAE;MACXA,OAAO,IAAI,GAAG;IAChB;IACAA,OAAO,IAAIQ,KAAK,CAACC,SAAS,CAACvC,GAAG,CAAC;EACjC;EAEA,IAAM+D,IAAI,GAAG,SAAAA,CAAAC,CAAC;IAAA,OAAIA,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAACiC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAAA;EAC/C,IAAMC,IAAI,GAAG,SAAAA,CAAAF,CAAC;IAAA,OAAIA,CAAC,CAAChC,QAAQ,CAAC,CAAC,CAACiC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAAA;EAE/C,IAAME,SAAS,GAAGhC,IAAI,CAACiC,WAAW,CAAC,CAAC,CAACpC,QAAQ,CAAC,CAAC,GAC7C+B,IAAI,CAAC5B,IAAI,CAACkC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,GACrCN,IAAI,CAAC5B,IAAI,CAACmC,OAAO,CAAC,CAAC,CAAC;EACtB,IAAMC,SAAS,GAAGR,IAAI,CAAC5B,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAAC,GACjC,GAAG,GACHT,IAAI,CAAC5B,IAAI,CAACsC,UAAU,CAAC,CAAC,CAAC,GACvB,GAAG,GACHV,IAAI,CAAC5B,IAAI,CAACuC,UAAU,CAAC,CAAC,CAAC,GACvB,GAAG,GACHR,IAAI,CAAC/B,IAAI,CAACwC,eAAe,CAAC,CAAC,CAAC;;EAElC;EACA,IAAMC,YAAY,SAAQ,EAAE,IAAI/C,IAAI,CAAC,CAAC,CAACgD,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAC,MAAI;EAEpE,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIzB,OAAO,EAAE;IACXyB,OAAO,IAAIzB,OAAO;EACpB;EACA,IAAIC,SAAS,IAAIA,SAAS,KAAKD,OAAO,EAAE;IACtCyB,OAAO,cAAYxB,SAAW;EAChC;EACA,IAAIwB,OAAO,EAAE;IACXA,OAAO,SAAOA,OAAO,OAAI;EAC3B;EAEA,IAAMC,eAAe,GAAG,EAAE;EAC1B,IAAItB,OAAO,EAAE;IACXsB,eAAe,CAAC1C,IAAI,CAACoB,OAAO,CAAC;EAC/B;EACA,IAAInC,IAAI,EAAE;IACRyD,eAAe,CAAC1C,IAAI,CAACf,IAAI,CAAC;EAC5B;EACA,IAAI4B,UAAU,EAAE;IACd6B,eAAe,CAAC1C,IAAI,CAACa,UAAU,CAAC;EAClC;EAEA,IAAI8B,UAAU,GAAG,CAACD,eAAe,CAAClC,MAAM,GACtC,EAAE,SAAOkC,eAAe,CAACE,IAAI,CAAC,GAAG,CAAC,OAAI;EAExC,IAAItB,SAAS,EACXqB,UAAU,UAAQrB,SAAS,MAAG;EAEhC,IAAMuB,eAAe,GAAGrB,MAAM,GAAG,WAAW,GAAG,EAAE;EAEjD,IAAMsB,UAAU,GAAGrH,GAAG,CAACkB,QAAQ,GACxBmF,SAAS,SAAII,SAAS,GAAGK,YAAY,IAAG5B,WAAW,GAAG,IAAI,GAAG,GAAG,IACnE,GAAG;EAIP,IAAMoC,UAAU,GAAG,CACjBjF,KAAK,CAACkF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAC7BH,UAAU,EACVL,OAAO,EACPE,UAAU,EACVE,eAAe,CAAC,CAACD,IAAI,CAAC,EAAE,CAAC;EAG3B,OAAOM,SAAS,CAACC,QAAQ,CAACJ,UAAU,EAAExC,OAAO,CAAClD,KAAK,IAAID,aAAa,CAACmD,OAAO,CAAC6C,SAAS,IAAIpG,UAAU,CAAC,CAAC,GAClGkG,SAAS,CAACC,QAAQ,CAAC1D,OAAO,EAAEc,OAAO,CAAClD,KAAK,IAAID,aAAa,CAACR,YAAY,CAACkB,KAAK,CAAC,CAAC,CAAC;AACtF,CAAC;;AAED;AACA;AACA;AACArC,GAAG,CAAC4H,WAAW,GAAG,UAAC5E,IAAI,EAAE6E,QAAQ,EAAK;EACpC,OAAArI,aAAA;IACEwE,OAAO,EAAEhB,IAAI;IACbX,KAAK,EAAE,MAAM;IACbgC,IAAI,EAAE,IAAIN,IAAI,CAAC,CAAC;IAChBmB,WAAW,EAAE;EAAI,GACd2C,QAAQ;AAEf,CAAC,C;;;;;;;;;;;AC5UDJ,SAAS,GAAG,CAAC,CAAC;AACdA,SAAS,CAACC,QAAQ,GAAG,UAAS1E,IAAI,EAAEpB,KAAK,EAAC;EACtC,OAAOoB,IAAI;AACf,CAAC,C", "file": "/packages/logging.js", "sourcesContent": ["import { Meteor } from 'meteor/meteor';\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nfunction Log(...args) {\n  Log.info(...args);\n}\n\n/// FOR TESTING\nlet intercept = 0;\nlet interceptedLines = [];\nlet suppress = 0;\n\n// Intercept the next 'count' calls to a Log function. The actual\n// lines printed to the console can be cleared and read by calling\n// Log._intercepted().\nLog._intercept = (count) => {\n  intercept += count;\n};\n\n// Suppress the next 'count' calls to a Log function. Use this to stop\n// tests from spamming the console, especially with red errors that\n// might look like a failing test.\nLog._suppress = (count) => {\n  suppress += count;\n};\n\n// Returns intercepted lines and resets the intercept counter.\nLog._intercepted = () => {\n  const lines = interceptedLines;\n  interceptedLines = [];\n  intercept = 0;\n  return lines;\n};\n\n// Either 'json' or 'colored-text'.\n//\n// When this is set to 'json', print JSON documents that are parsed by another\n// process ('satellite' or 'meteor run'). This other process should call\n// 'Log.format' for nice output.\n//\n// When this is set to 'colored-text', call 'Log.format' before printing.\n// This should be used for logging from within satellite, since there is no\n// other process that will be reading its standard output.\nLog.outputFormat = 'json';\n\n// Defaults to true for local development and for backwards compatibility.\n// for cloud environments is interesting to leave it false as most of them have the timestamp in the console.\n// Only works in server with colored-text\nLog.showTime = true;\n\nconst LEVEL_COLORS = {\n  debug: 'green',\n  // leave info as the default color\n  warn: 'magenta',\n  error: 'red'\n};\n\nconst META_COLOR = 'blue';\n\n// Default colors cause readability problems on Windows Powershell,\n// switch to bright variants. While still capable of millions of\n// operations per second, the benchmark showed a 25%+ increase in\n// ops per second (on Node 8) by caching \"process.platform\".\nconst isWin32 = typeof process === 'object' && process.platform === 'win32';\nconst platformColor = (color) => {\n  if (isWin32 && typeof color === 'string' && !color.endsWith('Bright')) {\n    return `${color}Bright`;\n  }\n  return color;\n};\n\n// XXX package\nconst RESTRICTED_KEYS = ['time', 'timeInexact', 'level', 'file', 'line',\n                        'program', 'originApp', 'satellite', 'stderr'];\n\nconst FORMATTED_KEYS = [...RESTRICTED_KEYS, 'app', 'message'];\n\nconst logInBrowser = obj => {\n  const str = Log.format(obj);\n\n  // XXX Some levels should be probably be sent to the server\n  const level = obj.level;\n\n  if ((typeof console !== 'undefined') && console[level]) {\n    console[level](str);\n  } else {\n    // IE doesn't have console.log.apply, it's not a real Object.\n    // http://stackoverflow.com/questions/5538972/console-log-apply-not-working-in-ie9\n    // http://patik.com/blog/complete-cross-browser-console-log/\n    if (typeof console.log.apply === \"function\") {\n      // Most browsers\n      console.log.apply(console, [str]);\n\n    } else if (typeof Function.prototype.bind === \"function\") {\n      // IE9\n      const log = Function.prototype.bind.call(console.log, console);\n      log.apply(console, [str]);\n    }\n  }\n};\n\n// @returns {Object: { line: Number, file: String }}\nLog._getCallerDetails = () => {\n  const getStack = () => {\n    // We do NOT use Error.prepareStackTrace here (a V8 extension that gets us a\n    // pre-parsed stack) since it's impossible to compose it with the use of\n    // Error.prepareStackTrace used on the server for source maps.\n    const err = new Error;\n    const stack = err.stack;\n    return stack;\n  };\n\n  const stack = getStack();\n\n  if (!stack) return {};\n\n  // looking for the first line outside the logging package (or an\n  // eval if we find that first)\n  let line;\n  const lines = stack.split('\\n').slice(1);\n  for (line of lines) {\n    if (line.match(/^\\s*(at eval \\(eval)|(eval:)/)) {\n      return {file: \"eval\"};\n    }\n\n    if (!line.match(/packages\\/(?:local-test[:_])?logging(?:\\/|\\.js)/)) {\n      break;\n    }\n  }\n\n  const details = {};\n\n  // The format for FF is 'functionName@filePath:lineNumber'\n  // The format for V8 is 'functionName (packages/logging/logging.js:81)' or\n  //                      'packages/logging/logging.js:81'\n  const match = /(?:[@(]| at )([^(]+?):([0-9:]+)(?:\\)|$)/.exec(line);\n  if (!match) {\n    return details;\n  }\n\n  // in case the matched block here is line:column\n  details.line = match[2].split(':')[0];\n\n  // Possible format: https://foo.bar.com/scripts/file.js?random=foobar\n  // XXX: if you can write the following in better way, please do it\n  // XXX: what about evals?\n  details.file = match[1].split('/').slice(-1)[0].split('?')[0];\n\n  return details;\n};\n\n['debug', 'info', 'warn', 'error'].forEach((level) => {\n // @param arg {String|Object}\n Log[level] = (arg) => {\n  if (suppress) {\n    suppress--;\n    return;\n  }\n\n  let intercepted = false;\n  if (intercept) {\n    intercept--;\n    intercepted = true;\n  }\n\n  let obj = (arg === Object(arg)\n    && !(arg instanceof RegExp)\n    && !(arg instanceof Date))\n    ? arg\n    : { message: new String(arg).toString() };\n\n  RESTRICTED_KEYS.forEach(key => {\n    if (obj[key]) {\n      throw new Error(`Can't set '${key}' in log message`);\n    }\n  });\n\n  if (hasOwn.call(obj, 'message') && typeof obj.message !== 'string') {\n    throw new Error(\"The 'message' field in log objects must be a string\");\n  }\n\n  if (!obj.omitCallerDetails) {\n    obj = { ...Log._getCallerDetails(), ...obj };\n  }\n\n  obj.time = new Date();\n  obj.level = level;\n\n  // If we are in production don't write out debug logs.\n  if (level === 'debug' && Meteor.isProduction) {\n    return;\n  }\n\n  if (intercepted) {\n    interceptedLines.push(EJSON.stringify(obj));\n  } else if (Meteor.isServer) {\n    if (Log.outputFormat === 'colored-text') {\n      console.log(Log.format(obj, {color: true}));\n    } else if (Log.outputFormat === 'json') {\n      console.log(EJSON.stringify(obj));\n    } else {\n      throw new Error(`Unknown logging output format: ${Log.outputFormat}`);\n    }\n  } else {\n    logInBrowser(obj);\n  }\n};\n});\n\n\n// tries to parse line as EJSON. returns object if parse is successful, or null if not\nLog.parse = (line) => {\n  let obj = null;\n  if (line && line.startsWith('{')) { // might be json generated from calling 'Log'\n    try { obj = EJSON.parse(line); } catch (e) {}\n  }\n\n  // XXX should probably check fields other than 'time'\n  if (obj && obj.time && (obj.time instanceof Date)) {\n    return obj;\n  } else {\n    return null;\n  }\n};\n\n// formats a log object into colored human and machine-readable text\nLog.format = (obj, options = {}) => {\n  obj = { ...obj }; // don't mutate the argument\n  let {\n    time,\n    timeInexact,\n    level = 'info',\n    file,\n    line: lineNumber,\n    app: appName = '',\n    originApp,\n    message = '',\n    program = '',\n    satellite = '',\n    stderr = '',\n  } = obj;\n\n  if (!(time instanceof Date)) {\n    throw new Error(\"'time' must be a Date object\");\n  }\n\n  FORMATTED_KEYS.forEach((key) => { delete obj[key]; });\n\n  if (Object.keys(obj).length > 0) {\n    if (message) {\n      message += ' ';\n    }\n    message += EJSON.stringify(obj);\n  }\n\n  const pad2 = n => n.toString().padStart(2, '0');\n  const pad3 = n => n.toString().padStart(3, '0');\n\n  const dateStamp = time.getFullYear().toString() +\n    pad2(time.getMonth() + 1 /*0-based*/) +\n    pad2(time.getDate());\n  const timeStamp = pad2(time.getHours()) +\n        ':' +\n        pad2(time.getMinutes()) +\n        ':' +\n        pad2(time.getSeconds()) +\n        '.' +\n        pad3(time.getMilliseconds());\n\n  // eg in San Francisco in June this will be '(-7)'\n  const utcOffsetStr = `(${(-(new Date().getTimezoneOffset() / 60))})`;\n\n  let appInfo = '';\n  if (appName) {\n    appInfo += appName;\n  }\n  if (originApp && originApp !== appName) {\n    appInfo += ` via ${originApp}`;\n  }\n  if (appInfo) {\n    appInfo = `[${appInfo}] `;\n  }\n\n  const sourceInfoParts = [];\n  if (program) {\n    sourceInfoParts.push(program);\n  }\n  if (file) {\n    sourceInfoParts.push(file);\n  }\n  if (lineNumber) {\n    sourceInfoParts.push(lineNumber);\n  }\n\n  let sourceInfo = !sourceInfoParts.length ?\n    '' : `(${sourceInfoParts.join(':')}) `;\n\n  if (satellite)\n    sourceInfo += `[${satellite}]`;\n\n  const stderrIndicator = stderr ? '(STDERR) ' : '';\n\n  const timeString = Log.showTime\n    ? `${dateStamp}-${timeStamp}${utcOffsetStr}${timeInexact ? '? ' : ' '}`\n    : ' ';\n\n\n\n  const metaPrefix = [\n    level.charAt(0).toUpperCase(),\n    timeString,\n    appInfo,\n    sourceInfo,\n    stderrIndicator].join('');\n\n\n  return Formatter.prettify(metaPrefix, options.color && platformColor(options.metaColor || META_COLOR)) +\n      Formatter.prettify(message, options.color && platformColor(LEVEL_COLORS[level]));\n};\n\n// Turn a line of text into a loggable object.\n// @param line {String}\n// @param override {Object}\nLog.objFromText = (line, override) => {\n  return {\n    message: line,\n    level: 'info',\n    time: new Date(),\n    timeInexact: true,\n    ...override\n  };\n};\n\nexport { Log };\n", "Formatter = {};\nFormatter.prettify = function(line, color){\n    return line;\n};\n"]}