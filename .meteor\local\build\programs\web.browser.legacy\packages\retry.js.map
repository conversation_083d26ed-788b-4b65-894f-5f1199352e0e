{"version": 3, "sources": ["meteor://💻app/packages/retry/retry.js"], "names": ["module", "export", "Retry", "_ref", "arguments", "length", "undefined", "_ref$baseTimeout", "baseTimeout", "_ref$exponent", "exponent", "_ref$maxTimeout", "maxTimeout", "_ref$minTimeout", "minTimeout", "_ref$minCount", "minCount", "_ref$fuzz", "fuzz", "retryTimer", "_proto", "prototype", "clear", "clearTimeout", "_timeout", "count", "timeout", "Math", "min", "pow", "Random", "fraction", "retryLater", "fn", "Meteor", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,KAAK,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,KAAK;EAAA;AAAC,CAAC,CAAC;AAAC,IAUnCA,KAAK;EAChB,SAAAA,MAAA,EASQ;IAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;MAAAG,gBAAA,GAAAJ,IAAA,CARJK,WAAW;MAAXA,WAAW,GAAAD,gBAAA,cAAG,IAAI,GAAAA,gBAAA;MAAAE,aAAA,GAAAN,IAAA,CAClBO,QAAQ;MAARA,QAAQ,GAAAD,aAAA,cAAG,GAAG,GAAAA,aAAA;MAAAE,eAAA,GAAAR,IAAA,CAGdS,UAAU;MAAVA,UAAU,GAAAD,eAAA,cAAG,CAAC,GAAG,EAAE,GAAG,IAAI,GAAAA,eAAA;MAAAE,eAAA,GAAAV,IAAA,CAC1BW,UAAU;MAAVA,UAAU,GAAAD,eAAA,cAAG,EAAE,GAAAA,eAAA;MAAAE,aAAA,GAAAZ,IAAA,CACfa,QAAQ;MAARA,QAAQ,GAAAD,aAAA,cAAG,CAAC,GAAAA,aAAA;MAAAE,SAAA,GAAAd,IAAA,CACZe,IAAI;MAAJA,IAAI,GAAAD,SAAA,cAAG,GAAG,GAAAA,SAAA;IAEV,IAAI,CAACT,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAG,IAAI;EACxB;;EAEA;EAAA,IAAAC,MAAA,GAAAlB,KAAA,CAAAmB,SAAA;EAAAD,MAAA,CACAE,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAACH,UAAU,EAAE;QACnBI,YAAY,CAAC,IAAI,CAACJ,UAAU,CAAC;MAC/B;MACA,IAAI,CAACA,UAAU,GAAG,IAAI;IACxB;IAAC,OALDG,KAAK;EAAA,IAOL;EACA;EAAA;EAAAF,MAAA,CACAI,QAAQ;IAAR,SAAAA,QAAQA,CAACC,KAAK,EAAE;MACd,IAAIA,KAAK,GAAG,IAAI,CAACT,QAAQ,EAAE;QACzB,OAAO,IAAI,CAACF,UAAU;MACxB;;MAEA;MACA;MACA,IAAIY,OAAO,GAAGC,IAAI,CAACC,GAAG,CACpB,IAAI,CAAChB,UAAU,EACf,IAAI,CAACJ,WAAW,GAAGmB,IAAI,CAACE,GAAG,CAAC,IAAI,CAACnB,QAAQ,EAAEe,KAAK,CAClD,CAAC,IACCK,MAAM,CAACC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACb,IAAI,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC,CAAC,CACpD;MAED,OAAOQ,OAAO;IAChB;IAAC,OAfDF,QAAQ;EAAA,IAiBR;EAAA;EAAAJ,MAAA,CACAY,UAAU;IAAV,SAAAA,UAAUA,CAACP,KAAK,EAAEQ,EAAE,EAAE;MACpB,IAAIP,OAAO,GAAG,IAAI,CAACF,QAAQ,CAACC,KAAK,CAAC;MAClC,IAAI,IAAI,CAACN,UAAU,EACjBI,YAAY,CAAC,IAAI,CAACJ,UAAU,CAAC;MAC/B,IAAI,CAACA,UAAU,GAAGe,MAAM,CAACC,UAAU,CAACF,EAAE,EAAEP,OAAO,CAAC;MAChD,OAAOA,OAAO;IAChB;IAAC,OANDM,UAAU;EAAA;EAAA,OAAA9B,KAAA;AAAA,I", "file": "/packages/retry.js", "sourcesContent": ["// Retry logic with an exponential backoff.\n//\n// options:\n//  baseTimeout: time for initial reconnect attempt (ms).\n//  exponent: exponential factor to increase timeout each attempt.\n//  maxTimeout: maximum time between retries (ms).\n//  minCount: how many times to reconnect \"instantly\".\n//  minTimeout: time to wait for the first `minCount` retries (ms).\n//  fuzz: factor to randomize retry times by (to avoid retry storms).\n\nexport class Retry {\n  constructor({\n    baseTimeout = 1000,\n    exponent = 2.2,\n    // The default is high-ish to ensure a server can recover from a\n    // failure caused by load.\n    maxTimeout = 5 * 60 * 1000,\n    minTimeout = 10,\n    minCount = 2,\n    fuzz = 0.5,\n  } = {}) {\n    this.baseTimeout = baseTimeout;\n    this.exponent = exponent;\n    this.maxTimeout = maxTimeout;\n    this.minTimeout = minTimeout;\n    this.minCount = minCount;\n    this.fuzz = fuzz;\n    this.retryTimer = null;\n  }\n\n  // Reset a pending retry, if any.\n  clear() {\n    if (this.retryTimer) {\n      clearTimeout(this.retryTimer);\n    }\n    this.retryTimer = null;\n  }\n\n  // Calculate how long to wait in milliseconds to retry, based on the\n  // `count` of which retry this is.\n  _timeout(count) {\n    if (count < this.minCount) {\n      return this.minTimeout;\n    }\n\n    // fuzz the timeout randomly, to avoid reconnect storms when a\n    // server goes down.\n    var timeout = Math.min(\n      this.maxTimeout,\n      this.baseTimeout * Math.pow(this.exponent, count)\n    ) * (\n      Random.fraction() * this.fuzz + (1 - this.fuzz / 2)\n    );\n\n    return timeout;\n  }\n\n  // Call `fn` after a delay, based on the `count` of which retry this is.\n  retryLater(count, fn) {\n    var timeout = this._timeout(count);\n    if (this.retryTimer)\n      clearTimeout(this.retryTimer);\n    this.retryTimer = Meteor.setTimeout(fn, timeout);\n    return timeout;\n  }\n}\n"]}