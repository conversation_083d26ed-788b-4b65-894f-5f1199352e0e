Package["core-runtime"].queue("akryum:postcss",function () {/* Imports */
var Meteor = Package.meteor.Meteor;
var global = Package.meteor.global;
var meteorEnv = Package.meteor.meteorEnv;
var EmitterPromise = Package.meteor.EmitterPromise;
var ECMAScript = Package.ecmascript.ECMAScript;
var meteorInstall = Package.modules.meteorInstall;
var Promise = Package.promise.Promise;

var require = meteorInstall({"node_modules":{"meteor":{"akryum:postcss":{"postcss.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                        //
// packages/akryum_postcss/postcss.js                                                     //
//                                                                                        //
////////////////////////////////////////////////////////////////////////////////////////////
                                                                                          //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module.export({
      name: () => name,
      postcss: () => postcss,
      selectorParser: () => selectorParser,
      autoprefixer: () => autoprefixer,
      addHash: () => addHash
    });
    let postcssLib;
    module.link("postcss", {
      default(v) {
        postcssLib = v;
      }
    }, 0);
    let selectorParserLib;
    module.link("postcss-selector-parser", {
      default(v) {
        selectorParserLib = v;
      }
    }, 1);
    let autoprefixerLib;
    module.link("autoprefixer", {
      default(v) {
        autoprefixerLib = v;
      }
    }, 2);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const name = 'postcss';
    const postcss = postcssLib;
    const selectorParser = selectorParserLib;
    const autoprefixer = autoprefixerLib;
    const addHash = postcss.plugin('add-hash', function (opts) {
      return function (root) {
        root.each(function rewriteSelector(node) {
          if (!node.selector) {
            // handle media queries
            if (node.type === 'atrule' && node.name === 'media') {
              node.each(rewriteSelector);
            }
            return;
          }
          node.selector = selectorParser(function (selectors) {
            selectors.each(function (selector) {
              var node = null;
              selector.each(function (n) {
                if (n.type !== 'pseudo') node = n;
              });
              selector.insertAfter(node, selectorParser.attribute({
                attribute: opts.hash
              }));
            });
          }).process(node.selector).result;
        });
      };
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
////////////////////////////////////////////////////////////////////////////////////////////

},"node_modules":{"postcss":{"package.json":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                        //
// node_modules/meteor/akryum_postcss/node_modules/postcss/package.json                   //
//                                                                                        //
////////////////////////////////////////////////////////////////////////////////////////////
                                                                                          //
module.exports = {
  "name": "postcss",
  "version": "5.0.21",
  "main": "lib/postcss"
};

////////////////////////////////////////////////////////////////////////////////////////////

},"lib":{"postcss.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                        //
// node_modules/meteor/akryum_postcss/node_modules/postcss/lib/postcss.js                 //
//                                                                                        //
////////////////////////////////////////////////////////////////////////////////////////////
                                                                                          //
module.useNode();
////////////////////////////////////////////////////////////////////////////////////////////

}}},"postcss-selector-parser":{"package.json":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                        //
// node_modules/meteor/akryum_postcss/node_modules/postcss-selector-parser/package.json   //
//                                                                                        //
////////////////////////////////////////////////////////////////////////////////////////////
                                                                                          //
module.exports = {
  "name": "postcss-selector-parser",
  "version": "2.0.0",
  "main": "dist/index.js"
};

////////////////////////////////////////////////////////////////////////////////////////////

},"dist":{"index.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                        //
// node_modules/meteor/akryum_postcss/node_modules/postcss-selector-parser/dist/index.js  //
//                                                                                        //
////////////////////////////////////////////////////////////////////////////////////////////
                                                                                          //
module.useNode();
////////////////////////////////////////////////////////////////////////////////////////////

}}},"autoprefixer":{"package.json":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                        //
// node_modules/meteor/akryum_postcss/node_modules/autoprefixer/package.json              //
//                                                                                        //
////////////////////////////////////////////////////////////////////////////////////////////
                                                                                          //
module.exports = {
  "name": "autoprefixer",
  "version": "6.3.6",
  "main": "lib/autoprefixer"
};

////////////////////////////////////////////////////////////////////////////////////////////

},"lib":{"autoprefixer.js":function module(require,exports,module){

////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                        //
// node_modules/meteor/akryum_postcss/node_modules/autoprefixer/lib/autoprefixer.js       //
//                                                                                        //
////////////////////////////////////////////////////////////////////////////////////////////
                                                                                          //
module.useNode();
////////////////////////////////////////////////////////////////////////////////////////////

}}}}}}}},{
  "extensions": [
    ".js",
    ".json"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/node_modules/meteor/akryum:postcss/postcss.js"
  ],
  mainModulePath: "/node_modules/meteor/akryum:postcss/postcss.js"
}});

//# sourceURL=meteor://💻app/packages/akryum_postcss.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
