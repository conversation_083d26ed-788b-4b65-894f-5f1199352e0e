{"version": 3, "sources": ["meteor://💻app/packages/ddp-client/client/client.js", "meteor://💻app/packages/ddp-client/client/client_convenience.js", "meteor://💻app/packages/ddp-client/client/queue_stub_helpers.js", "meteor://💻app/packages/ddp-client/common/connection_stream_handlers.js", "meteor://💻app/packages/ddp-client/common/document_processors.js", "meteor://💻app/packages/ddp-client/common/livedata_connection.js", "meteor://💻app/packages/ddp-client/common/message_processors.js", "meteor://💻app/packages/ddp-client/common/method_invoker.js", "meteor://💻app/packages/ddp-client/common/mongo_id_map.js", "meteor://💻app/packages/ddp-client/common/namespace.js"], "names": ["module", "link", "DDP", "v", "Meteor", "loadAsyncStubHelpers", "refresh", "runtimeConfig", "__meteor_runtime_config__", "Object", "create", "ddpUrl", "DDP_DEFAULT_CONNECTION_URL", "retry", "Retry", "onDDPVersionNegotiationFailure", "description", "_debug", "Package", "reload", "migrationData", "Reload", "_migrationData", "failures", "DDPVersionNegotiationFailures", "_onMigrate", "retryLater", "_reload", "immediateMigration", "connection", "connect", "for<PERSON>ach", "name", "bind", "_objectSpread", "default", "export", "Connection", "queueSize", "queue", "Promise", "resolve", "queueFunction", "fn", "promiseProps", "arguments", "length", "undefined", "reject", "promise", "_resolve", "_reject", "finally", "_promise$stubPromise", "stubPromise", "catch", "_maybeMigrate", "serverPromise", "oldReadyToMigrate", "prototype", "_readyToMigrate", "apply", "currentMethodInvocation", "oldApplyAsync", "applyAsync", "_this", "args", "_CurrentMethodInvocation", "_set", "enclosing", "get", "alreadyInSimulation", "isSimulation", "isFromCallAsync", "_isFromCallAsync", "_getIsSimulation", "stubPromiseResolver", "serverPromiseResolver", "r", "finished", "_setImmediate", "applyAsyncPromise", "then", "result", "err", "console", "warn", "oldApply", "options", "callback", "_this2", "_stream", "_neverQueued", "_oldApply$call", "call", "_returnMethodInvoker", "methodInvoker", "_addOutstandingMethod", "queueSend", "oldSubscribe", "subscribe", "oldSend", "_send", "params", "shouldQueue", "_this3", "_oldSendOutstandingMethodBlocksMessages", "_sendOutstandingMethodBlocksMessages", "_arguments", "_this4", "_slicedToArray", "_regeneratorRuntime", "ConnectionStreamHandlers", "DDPCommon", "_connection", "_proto", "onMessage", "raw_msg", "msg", "async", "onMessage$", "_context", "prev", "next", "parseDDP", "t0", "abrupt", "_heartbeat", "messageReceived", "testMessageOnConnect", "keys", "server_id", "_version", "_versionSuggestion", "awrap", "_routeMessage", "stop", "_routeMessage$", "_context2", "_livedata_connected", "onConnected", "_handleFailedMessage", "respondToPings", "id", "_livedata_data", "_livedata_nosub", "_livedata_result", "_livedata_error", "_supportedDDPVersions", "indexOf", "version", "reconnect", "_force", "disconnect", "_permanent", "_error", "onReset", "_buildConnectMessage", "_handleOutstandingMethodsOnReset", "_callOnReconnectAndSendAppropriateOutstandingMethods", "_resendSubscriptions", "_lastSessionId", "session", "support", "blocks", "_outstandingMethodBlocks", "currentMethodBlock", "methods", "filter", "sentMessage", "noRetry", "receiveResult", "Error", "shift", "values", "_methodInvokers", "invoker", "entries", "_subscriptions", "_ref", "_ref2", "sub", "_sendQueued", "DocumentProcessors", "MongoID", "DiffSequence", "hasOwn", "isEmpty", "_process_added", "updates", "self", "serverDoc", "isExisting", "currentDoc", "_process_added$", "idParse", "_getServerDoc", "collection", "document", "fields", "_id", "_resetStores", "_stores", "getDoc", "sent", "_pushUpdate", "_process_changed", "applyChanges", "_process_removed", "_process_ready", "subs", "subId", "_runWhenAllServerDocsAreFlushed", "subRecord", "ready", "readyCallback", "readyDeps", "changed", "_process_updated", "methodId", "docs", "_documentsWrittenByStub", "written", "JSON", "stringify", "writtenByStubs", "idStringify", "replace", "flushCallbacks", "c", "_serverDocuments", "remove", "callbackInvoker", "dataVisible", "push", "serverDocsForCollection", "_toConsumableArray", "_objectWithoutProperties", "_createForOfIteratorHelperLoose", "_typeof", "Tracker", "EJSON", "Random", "MethodInvoker", "slice", "last", "MongoIDMap", "MessageProcessors", "url", "heartbeatInterval", "heartbeatTimeout", "npmFayeOptions", "reloadWithOutstanding", "supportedDDPVersions", "SUPPORTED_DDP_VERSIONS", "bufferedWritesInterval", "bufferedWritesMaxAge", "onReconnect", "ClientStream", "ConnectionError", "headers", "_sockjsOptions", "_dontPrintErrors", "connectTimeoutMs", "_methodHandlers", "_nextMethodId", "_heartbeatInterval", "_heartbeatTimeout", "_afterUpdateCallbacks", "_messagesBufferedUntilQuiescence", "_methodsBlockingQuiescence", "_subsBeingRevived", "_updatesForUnknownStores", "_retryMigrate", "_bufferedWrites", "_bufferedWritesFlushAt", "_bufferedWritesFlushHandle", "_bufferedWritesInterval", "_bufferedWritesMaxAge", "_userId", "_userIdDeps", "Dependency", "isClient", "_streamHandlers", "onDisconnect", "isServer", "on", "bindEnvironment", "_messageProcessors", "_documentProcessors", "createStoreMethods", "wrappedStore", "store", "keysOfStore", "method", "registerStoreClient", "queued", "Array", "isArray", "beginUpdate", "update", "endUpdate", "registerStoreServer", "_iterator", "_step", "registerStoreServer$", "done", "value", "callbacks", "lastPara<PERSON>", "onReady", "pop", "onError", "onStop", "some", "f", "existing", "find", "inactive", "equals", "<PERSON><PERSON><PERSON><PERSON>", "stopCallback", "clone", "handle", "record", "depend", "subscriptionId", "active", "onInvalidate", "afterFlush", "isAsyncCall", "_isCallAsyncMethodRunning", "func", "_ref3", "callAsync", "returnServerResultPromise", "_this$_stubCall", "_stubCall", "stubInvocation", "invocation", "stubOptions", "_excluded", "hasStub", "_saveOriginals", "stubReturnValue", "with<PERSON><PERSON><PERSON>", "_isPromise", "e", "exception", "_apply", "_applyAsyncStubInvocation", "_applyAsync", "o", "_this$_stubCall2", "currentContext", "_applyAsyncStubInvocation$", "_excluded2", "_setNewContextAndGetCurrent", "finish", "t1", "_ref4", "_applyAsync$", "_context3", "stubCallValue", "randomSeed", "_retrieveAndStoreOriginals", "message", "throwStubExceptions", "_expectedByTest", "returnStubValue", "_len", "allArgs", "_key", "from", "onResultReceived", "wait", "stub", "defaultReturn", "randomSeedGenerator", "makeRpcSeed", "setUserId", "userId", "MethodInvocation", "_noYieldsAllowed", "_waitingFor<PERSON>uiescence", "_flushBufferedWrites", "saveOriginals", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref5", "_ref6", "originals", "retrieveOriginals", "doc", "<PERSON><PERSON><PERSON><PERSON>", "_unsubscribeAll", "obj", "send", "stringifyDDP", "_lostConnection", "error", "status", "_this$_stream", "_this$_stream2", "_this$_stream3", "close", "_anyMethodsAreOutstanding", "invokers", "_processOneDataMessage", "messageType", "_processOneDataMessage$", "_context4", "_prepareBuffersToFlush", "clearTimeout", "writes", "_performWritesServer", "_i", "_Object$values", "_updates$store$_name", "_i2", "_Object$entries", "_ref7", "_ref8", "storeName", "messages", "_store", "CHUNK_SIZE", "i", "chunk", "_iterator2", "_step2", "_self$_updatesForUnkn", "_i3", "_Object$values2", "_store2", "_performWritesServer$", "_context5", "_name", "Math", "min", "process", "nextTick", "_runAfterUpdateCallbacks", "_performWritesClient", "_updates$store$_name2", "_ref9", "_ref10", "_self$_updatesForUnkn2", "_flushBufferedWrites$", "_context6", "runFAfterUpdates", "unflushedServerDocCount", "onServerDocFlush", "serverDocuments", "writtenByStubForAMethodWithSentMessage", "sendMessage", "_outstandingMethodFinished", "firstBlock", "_sendOutstandingMethods", "m", "oldOutstandingMethodBlocks", "_self$_outstandingMet", "_reconnectHook", "each", "reconnectedToPreviousSession", "_livedata_connected$", "Heartbeat", "onTimeout", "sendPing", "start", "gotResult", "bufferedMessages", "bufferedMessage", "standardWrite", "_livedata_data$", "Date", "valueOf", "setTimeout", "_liveDataWritesPromise", "_livedata_result$", "idx", "found", "splice", "reason", "details", "meteorErrorFromMsg", "_livedata_nosub$", "msgArg", "offendingMessage", "_callback", "_message", "_onResultReceived", "_wait", "_methodResult", "_dataVisible", "_maybeInvokeCallback", "_inherits<PERSON><PERSON>e", "_IdMap", "IdMap", "allConnections", "EnvironmentVariable", "_CurrentPublicationInvocation", "_CurrentInvocation", "_CurrentCallAsyncInvocation", "connectionErrorConstructor", "makeErrorType", "ForcedReconnectError", "randomStream", "scope", "RandomStream", "ret", "Hook", "register", "_allSubscriptionsReady", "every", "conn"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;EAACC,GAAG,EAAC;AAAK,CAAC,EAAC,CAAC,CAAC;AAACF,MAAM,CAACC,IAAI,CAAC,+BAA+B,CAAC;AAACD,MAAM,CAACC,IAAI,CAAC,sBAAsB,CAAC,C;;;;;;;;;;;ACApI,IAAIC,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;EAACC,GAAG,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAM,EAAC,SAAAA,CAASD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,oBAAoB;AAACL,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;EAACI,oBAAoB,EAAC,SAAAA,CAASF,CAAC,EAAC;IAACE,oBAAoB,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAI3Q;AACA;AACAC,MAAM,CAACE,OAAO,GAAG,YAAM,CAAC,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,aAAa,GAAG,OAAOC,yBAAyB,KAAK,WAAW,GAAGA,yBAAyB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACxH,IAAMC,MAAM,GAAGJ,aAAa,CAACK,0BAA0B,IAAI,GAAG;AAE9D,IAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;AAEzB,SAASC,8BAA8BA,CAACC,WAAW,EAAE;EACnDZ,MAAM,CAACa,MAAM,CAACD,WAAW,CAAC;EAC1B,IAAIE,OAAO,CAACC,MAAM,EAAE;IAClB,IAAMC,aAAa,GAAGF,OAAO,CAACC,MAAM,CAACE,MAAM,CAACC,cAAc,CAAC,UAAU,CAAC,IAAIb,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7F,IAAIa,QAAQ,GAAGH,aAAa,CAACI,6BAA6B,IAAI,CAAC;IAC/D,EAAED,QAAQ;IACVL,OAAO,CAACC,MAAM,CAACE,MAAM,CAACI,UAAU,CAAC,UAAU,EAAE;MAAA,OAAM,CAAC,IAAI,EAAE;QAAED,6BAA6B,EAAED;MAAS,CAAC,CAAC;IAAA,EAAC;IACvGV,KAAK,CAACa,UAAU,CAACH,QAAQ,EAAE,YAAM;MAC/BL,OAAO,CAACC,MAAM,CAACE,MAAM,CAACM,OAAO,CAAC;QAAEC,kBAAkB,EAAE;MAAK,CAAC,CAAC;IAC7D,CAAC,CAAC;EACJ;AACF;;AAEA;AACAvB,oBAAoB,CAAC,CAAC;AAEtBD,MAAM,CAACyB,UAAU,GAAG3B,GAAG,CAAC4B,OAAO,CAACnB,MAAM,EAAE;EACtCI,8BAA8B,EAAEA;AAClC,CAAC,CAAC;;AAEF;AACA;AACA,CACE,WAAW,EACX,SAAS,EACT,aAAa,EACb,MAAM,EACN,WAAW,EACX,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,YAAY,CACb,CAACgB,OAAO,CAAC,UAAAC,IAAI,EAAI;EAChB5B,MAAM,CAAC4B,IAAI,CAAC,GAAG5B,MAAM,CAACyB,UAAU,CAACG,IAAI,CAAC,CAACC,IAAI,CAAC7B,MAAM,CAACyB,UAAU,CAAC;AAChE,CAAC,CAAC,C;;;;;;;;;;;AC/DF,IAAIK,aAAa;AAAClC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC+B,aAAa,GAAC/B,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA9GH,MAAM,CAACoC,MAAM,CAAC;EAAC/B,oBAAoB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,oBAAoB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIH,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;EAACC,GAAG,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIkC,UAAU;AAACrC,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACoC,UAAU,EAAC,SAAAA,CAASlC,CAAC,EAAC;IAACkC,UAAU,GAAClC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAG3P;;AAEA,IAAImC,SAAS,GAAG,CAAC;AACjB,IAAIC,KAAK,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;AAEtB,IAAMpC,oBAAoB,GAAG,SAAAA,CAAA,EAAM;EACxC,SAASqC,aAAaA,CAACC,EAAE,EAAqB;IAAA,IAAnBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC1CP,SAAS,IAAI,CAAC;IAEd,IAAIG,OAAO;IACX,IAAIO,MAAM;IACV,IAAMC,OAAO,GAAG,IAAIT,OAAO,CAAC,UAACU,QAAQ,EAAEC,OAAO,EAAK;MACjDV,OAAO,GAAGS,QAAQ;MAClBF,MAAM,GAAGG,OAAO;IAClB,CAAC,CAAC;IAEFZ,KAAK,GAAGA,KAAK,CAACa,OAAO,CAAC,YAAM;MAAA,IAAAC,oBAAA;MAC1BV,EAAE,CAACF,OAAO,EAAEO,MAAM,CAAC;MAEnB,QAAAK,oBAAA,GAAOJ,OAAO,CAACK,WAAW,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,KAAK,CAAC,YAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEFN,OAAO,CACJM,KAAK,CAAC,YAAM,CAAC,CAAC,CAAC,CAAC;IAAA,CAChBH,OAAO,CAAC,YAAM;MACbd,SAAS,IAAI,CAAC;MACd,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnBlC,MAAM,CAACyB,UAAU,CAAC2B,aAAa,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;IAEJP,OAAO,CAACK,WAAW,GAAGV,YAAY,CAACU,WAAW;IAC9CL,OAAO,CAACQ,aAAa,GAAGb,YAAY,CAACa,aAAa;IAElD,OAAOR,OAAO;EAChB;EAEA,IAAIS,iBAAiB,GAAGrB,UAAU,CAACsB,SAAS,CAACC,eAAe;EAC5DvB,UAAU,CAACsB,SAAS,CAACC,eAAe,GAAG,YAAY;IACjD,IAAItB,SAAS,GAAG,CAAC,EAAE;MACjB,OAAO,KAAK;IACd;IAEA,OAAOoB,iBAAiB,CAACG,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;EACjD,CAAC;EAED,IAAIiB,uBAAuB,GAAG,IAAI;;EAElC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAIC,aAAa,GAAG1B,UAAU,CAACsB,SAAS,CAACK,UAAU;EACnD3B,UAAU,CAACsB,SAAS,CAACK,UAAU,GAAG,YAAY;IAAA,IAAAC,KAAA;IAC5C,IAAIC,IAAI,GAAGrB,SAAS;IACpB,IAAIb,IAAI,GAAGkC,IAAI,CAAC,CAAC,CAAC;IAElB,IAAIJ,uBAAuB,EAAE;MAC3B5D,GAAG,CAACiE,wBAAwB,CAACC,IAAI,CAACN,uBAAuB,CAAC;MAC1DA,uBAAuB,GAAG,IAAI;IAChC;IAEA,IAAMO,SAAS,GAAGnE,GAAG,CAACiE,wBAAwB,CAACG,GAAG,CAAC,CAAC;IACpD,IAAMC,mBAAmB,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,YAAY;IACnD,IAAMC,eAAe,GAAGJ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,gBAAgB;IAEnD,IACEtE,MAAM,CAACyB,UAAU,CAAC8C,gBAAgB,CAAC;MACjCF,eAAe,EAAfA,eAAe;MACfF,mBAAmB,EAAnBA;IACF,CAAC,CAAC,EACF;MACA;MACA,OAAOR,aAAa,CAACF,KAAK,CAAC,IAAI,EAAEK,IAAI,CAAC;IACxC;IAEA,IAAIU,mBAAmB;IACvB,IAAIC,qBAAqB;IACzB,IAAMvB,WAAW,GAAG,IAAId,OAAO,CAAC,UAACsC,CAAC;MAAA,OAAMF,mBAAmB,GAAGE,CAAC;IAAA,CAAC,CAAC;IACjE,IAAMrB,aAAa,GAAG,IAAIjB,OAAO,CAAC,UAACsC,CAAC;MAAA,OAAMD,qBAAqB,GAAGC,CAAC;IAAA,CAAC,CAAC;IAErE,OAAOpC,aAAa,CAClB,UAACD,OAAO,EAAEO,MAAM,EAAK;MACnB,IAAI+B,QAAQ,GAAG,KAAK;MAEpB3E,MAAM,CAAC4E,aAAa,CAAC,YAAM;QACzB,IAAMC,iBAAiB,GAAGlB,aAAa,CAACF,KAAK,CAACI,KAAI,EAAEC,IAAI,CAAC;QACzDU,mBAAmB,CAACK,iBAAiB,CAAC3B,WAAW,CAAC;QAClDuB,qBAAqB,CAACI,iBAAiB,CAACxB,aAAa,CAAC;QAEtDwB,iBAAiB,CAAC3B,WAAW,CAC1BC,KAAK,CAAC,YAAM,CAAC,CAAC,CAAC,CAAC;QAAA,CAChBH,OAAO,CAAC,YAAM;UACb2B,QAAQ,GAAG,IAAI;QACjB,CAAC,CAAC;QAEJE,iBAAiB,CACdC,IAAI,CAAC,UAACC,MAAM,EAAK;UAChB1C,OAAO,CAAC0C,MAAM,CAAC;QACjB,CAAC,CAAC,CACD5B,KAAK,CAAC,UAAC6B,GAAG,EAAK;UACdpC,MAAM,CAACoC,GAAG,CAAC;QACb,CAAC,CAAC;QAEJ3B,aAAa,CAACF,KAAK,CAAC,YAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC;MAEFnD,MAAM,CAAC4E,aAAa,CAAC,YAAM;QACzB,IAAI,CAACD,QAAQ,EAAE;UACbM,OAAO,CAACC,IAAI,mBACMtD,IAAI,wMACtB,CAAC;QACH;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MACEsB,WAAW,EAAXA,WAAW;MACXG,aAAa,EAAbA;IACF,CACF,CAAC;EACH,CAAC;EAED,IAAI8B,QAAQ,GAAGlD,UAAU,CAACsB,SAAS,CAACE,KAAK;EACzCxB,UAAU,CAACsB,SAAS,CAACE,KAAK,GAAG,UAAU7B,IAAI,EAAEkC,IAAI,EAAEsB,OAAO,EAAEC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IACpE,IAAI,IAAI,CAACC,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOL,QAAQ,CAAC1B,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IACxC;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAAC4C,QAAQ,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MAC9CC,QAAQ,GAAGD,OAAO;MAClBA,OAAO,GAAGzC,SAAS;IACrB;IAEA,IAAA8C,cAAA,GAAgCN,QAAQ,CAACO,IAAI,CAAC,IAAI,EAAE9D,IAAI,EAAEkC,IAAI,EAAAhC,aAAA,CAAAA,aAAA,KACzDsD,OAAO;QACVO,oBAAoB,EAAE;MAAI,IACzBN,QAAQ,CAAC;MAHNO,aAAa,GAAAH,cAAA,CAAbG,aAAa;MAAEb,MAAM,GAAAU,cAAA,CAANV,MAAM;IAK3B,IAAIa,aAAa,EAAE;MACjBtD,aAAa,CAAC,UAACD,OAAO,EAAK;QACzBiD,MAAI,CAACO,qBAAqB,CAACD,aAAa,EAAER,OAAO,CAAC;QAClD/C,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;IAEA,OAAO0C,MAAM;EACf,CAAC;;EAED;AACF;AACA;EACE,IAAIe,SAAS,GAAG,KAAK;EACrB,IAAIC,YAAY,GAAG9D,UAAU,CAACsB,SAAS,CAACyC,SAAS;EACjD/D,UAAU,CAACsB,SAAS,CAACyC,SAAS,GAAG,YAAY;IAC3C,IAAI,IAAI,CAACT,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOO,YAAY,CAACtC,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IAC5C;IAEAqD,SAAS,GAAG,IAAI;IAChB,IAAI;MACF,OAAOC,YAAY,CAACtC,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IAC5C,CAAC,SAAS;MACRqD,SAAS,GAAG,KAAK;IACnB;EACF,CAAC;EAED,IAAIG,OAAO,GAAGhE,UAAU,CAACsB,SAAS,CAAC2C,KAAK;EACxCjE,UAAU,CAACsB,SAAS,CAAC2C,KAAK,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAE;IAAA,IAAAC,MAAA;IAC1D,IAAI,IAAI,CAACd,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOS,OAAO,CAACxC,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IACvC;IAEA,IAAI,CAACqD,SAAS,IAAI,CAACM,WAAW,EAAE;MAC9B,OAAOH,OAAO,CAACP,IAAI,CAAC,IAAI,EAAES,MAAM,CAAC;IACnC;IAEAL,SAAS,GAAG,KAAK;IACjBxD,aAAa,CAAC,UAACD,OAAO,EAAK;MACzB,IAAI;QACF4D,OAAO,CAACP,IAAI,CAACW,MAAI,EAAEF,MAAM,CAAC;MAC5B,CAAC,SAAS;QACR9D,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIiE,uCAAuC,GACzCrE,UAAU,CAACsB,SAAS,CAACgD,oCAAoC;EAC3DtE,UAAU,CAACsB,SAAS,CAACgD,oCAAoC,GAAG,YAAY;IAAA,IAAAC,UAAA,GAAA/D,SAAA;MAAAgE,MAAA;IACtE,IAAI,IAAI,CAAClB,OAAO,CAACC,YAAY,EAAE;MAC7B,OAAOc,uCAAuC,CAAC7C,KAAK,CAAC,IAAI,EAAEhB,SAAS,CAAC;IACvE;IACAH,aAAa,CAAC,UAACD,OAAO,EAAK;MACzB,IAAI;QACFiE,uCAAuC,CAAC7C,KAAK,CAACgD,MAAI,EAAEhE,UAAS,CAAC;MAChE,CAAC,SAAS;QACRJ,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,C;;;;;;;;;;;AC1ND,IAAIqE,cAAc;AAAC9G,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC2G,cAAc,GAAC3G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI4G,mBAAmB;AAAC/G,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC4G,mBAAmB,GAAC5G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAjOH,MAAM,CAACoC,MAAM,CAAC;EAAC4E,wBAAwB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,wBAAwB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,SAAS;AAACjH,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACgH,SAAS,EAAC,SAAAA,CAAS9G,CAAC,EAAC;IAAC8G,SAAS,GAAC9G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAM,EAAC,SAAAA,CAASD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAGxO6G,wBAAwB;EACnC,SAAAA,yBAAYnF,UAAU,EAAE;IACtB,IAAI,CAACqF,WAAW,GAAGrF,UAAU;EAC/B;;EAEA;AACF;AACA;AACA;EAHE,IAAAsF,MAAA,GAAAH,wBAAA,CAAArD,SAAA;EAAAwD,MAAA,CAIMC,SAAS;IAAf,SAAMA,SAASA,CAACC,OAAO;MAAA,IAAAC,GAAA;MAAA,OAAAP,mBAAA,CAAAQ,KAAA;QAAA,SAAAC,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAGnBJ,GAAG,GAAGL,SAAS,CAACW,QAAQ,CAACP,OAAO,CAAC;cAACI,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAI,EAAA,GAAAJ,QAAA;cAElCrH,MAAM,CAACa,MAAM,CAAC,6BAA6B,EAAAwG,QAAA,CAAAI,EAAG,CAAC;cAAC,OAAAJ,QAAA,CAAAK,MAAA;YAAA;cAIlD;cACA;cACA,IAAI,IAAI,CAACZ,WAAW,CAACa,UAAU,EAAE;gBAC/B,IAAI,CAACb,WAAW,CAACa,UAAU,CAACC,eAAe,CAAC,CAAC;cAC/C;cAAC,MAEGV,GAAG,KAAK,IAAI,IAAI,CAACA,GAAG,CAACA,GAAG;gBAAAG,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MACvB,CAACL,GAAG,IAAI,CAACA,GAAG,CAACW,oBAAoB;gBAAAR,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MAC9BlH,MAAM,CAACyH,IAAI,CAACZ,GAAG,CAAC,CAACxE,MAAM,KAAK,CAAC,IAAIwE,GAAG,CAACa,SAAS;gBAAAV,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAK,MAAA;YAAA;cAClD1H,MAAM,CAACa,MAAM,CAAC,qCAAqC,EAAEqG,GAAG,CAAC;YAAC;cAAA,OAAAG,QAAA,CAAAK,MAAA;YAAA;cAK9D;cACA;cACA,IAAIR,GAAG,CAACA,GAAG,KAAK,WAAW,EAAE;gBAC3B,IAAI,CAACJ,WAAW,CAACkB,QAAQ,GAAG,IAAI,CAAClB,WAAW,CAACmB,kBAAkB;cACjE;cAACZ,QAAA,CAAAE,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAEK,IAAI,CAACC,aAAa,CAACjB,GAAG,CAAC;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAe,IAAA;UAAA;QAAA;QAAA,OAAAhB,UAAA;MAAA,2BAAAhF,OAAA;IAAA;IAC9B,OA9BK4E,SAAS;EAAA;EAgCf;AACF;AACA;AACA;AACA;EAJE;EAAAD,MAAA,CAKMoB,aAAa;IAAnB,SAAMA,aAAaA,CAACjB,GAAG;MAAA,OAAAP,mBAAA,CAAAQ,KAAA;QAAA,SAAAkB,eAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cAAAe,SAAA,CAAAb,EAAA,GACbP,GAAG,CAACA,GAAG;cAAAoB,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAb,EAAA,KACR,WAAW,OAAAa,SAAA,CAAAb,EAAA,KAKX,QAAQ,OAAAa,SAAA,CAAAb,EAAA,KAIR,MAAM,QAAAa,SAAA,CAAAb,EAAA,KAMN,MAAM,QAAAa,SAAA,CAAAb,EAAA,KAIN,OAAO,QAAAa,SAAA,CAAAb,EAAA,KACP,SAAS,QAAAa,SAAA,CAAAb,EAAA,KACT,SAAS,QAAAa,SAAA,CAAAb,EAAA,KACT,OAAO,QAAAa,SAAA,CAAAb,EAAA,KACP,SAAS,QAAAa,SAAA,CAAAb,EAAA,KAIT,OAAO,QAAAa,SAAA,CAAAb,EAAA,KAIP,QAAQ,QAAAa,SAAA,CAAAb,EAAA,KAIR,OAAO;cAAA;YAAA;cAAAa,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAlCJ,IAAI,CAACpB,WAAW,CAACyB,mBAAmB,CAACrB,GAAG,CAAC;YAAA;cAC/C,IAAI,CAACJ,WAAW,CAAC1B,OAAO,CAACoD,WAAW,CAAC,CAAC;cAAC,OAAAF,SAAA,CAAAZ,MAAA;YAAA;cAAAY,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAIjC,IAAI,CAACO,oBAAoB,CAACvB,GAAG,CAAC;YAAA;cAAA,OAAAoB,SAAA,CAAAZ,MAAA;YAAA;cAIpC,IAAI,IAAI,CAACZ,WAAW,CAAC1B,OAAO,CAACsD,cAAc,EAAE;gBAC3C,IAAI,CAAC5B,WAAW,CAACZ,KAAK,CAAC;kBAAEgB,GAAG,EAAE,MAAM;kBAAEyB,EAAE,EAAEzB,GAAG,CAACyB;gBAAG,CAAC,CAAC;cACrD;cAAC,OAAAL,SAAA,CAAAZ,MAAA;YAAA;cAAA,OAAAY,SAAA,CAAAZ,MAAA;YAAA;cAAAY,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAYK,IAAI,CAACpB,WAAW,CAAC8B,cAAc,CAAC1B,GAAG,CAAC;YAAA;cAAA,OAAAoB,SAAA,CAAAZ,MAAA;YAAA;cAAAY,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAIpC,IAAI,CAACpB,WAAW,CAAC+B,eAAe,CAAC3B,GAAG,CAAC;YAAA;cAAA,OAAAoB,SAAA,CAAAZ,MAAA;YAAA;cAAAY,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAIrC,IAAI,CAACpB,WAAW,CAACgC,gBAAgB,CAAC5B,GAAG,CAAC;YAAA;cAAA,OAAAoB,SAAA,CAAAZ,MAAA;YAAA;cAI5C,IAAI,CAACZ,WAAW,CAACiC,eAAe,CAAC7B,GAAG,CAAC;cAAC,OAAAoB,SAAA,CAAAZ,MAAA;YAAA;cAItC1H,MAAM,CAACa,MAAM,CAAC,0CAA0C,EAAEqG,GAAG,CAAC;YAAC;YAAA;cAAA,OAAAoB,SAAA,CAAAF,IAAA;UAAA;QAAA;QAAA,OAAAC,cAAA;MAAA,uBAAAjG,OAAA;IAAA;IAEpE,OA5CK+F,aAAa;EAAA;EA8CnB;AACF;AACA;AACA;AACA;EAJE;EAAApB,MAAA,CAKA0B,oBAAoB;IAApB,SAAAA,oBAAoBA,CAACvB,GAAG,EAAE;MACxB,IAAI,IAAI,CAACJ,WAAW,CAACkC,qBAAqB,CAACC,OAAO,CAAC/B,GAAG,CAACgC,OAAO,CAAC,IAAI,CAAC,EAAE;QACpE,IAAI,CAACpC,WAAW,CAACmB,kBAAkB,GAAGf,GAAG,CAACgC,OAAO;QACjD,IAAI,CAACpC,WAAW,CAACvB,OAAO,CAAC4D,SAAS,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC,CAAC;MACtD,CAAC,MAAM;QACL,IAAMxI,WAAW,GACf,2DAA2D,GAC3DsG,GAAG,CAACgC,OAAO;QACb,IAAI,CAACpC,WAAW,CAACvB,OAAO,CAAC8D,UAAU,CAAC;UAAEC,UAAU,EAAE,IAAI;UAAEC,MAAM,EAAE3I;QAAY,CAAC,CAAC;QAC9E,IAAI,CAACkG,WAAW,CAAC1B,OAAO,CAACzE,8BAA8B,CAACC,WAAW,CAAC;MACtE;IACF;IAAC,OAXD6H,oBAAoB;EAAA;EAapB;AACF;AACA;EAFE;EAAA1B,MAAA,CAGAyC,OAAO;IAAP,SAAAA,OAAOA,CAAA,EAAG;MACR;MACA;MACA,IAAMtC,GAAG,GAAG,IAAI,CAACuC,oBAAoB,CAAC,CAAC;MACvC,IAAI,CAAC3C,WAAW,CAACZ,KAAK,CAACgB,GAAG,CAAC;;MAE3B;MACA,IAAI,CAACwC,gCAAgC,CAAC,CAAC;;MAEvC;MACA;MACA;MACA,IAAI,CAAC5C,WAAW,CAAC6C,oDAAoD,CAAC,CAAC;MACvE,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC7B;IAAC,OAdDJ,OAAO;EAAA;EAgBP;AACF;AACA;AACA;AACA;EAJE;EAAAzC,MAAA,CAKA0C,oBAAoB;IAApB,SAAAA,oBAAoBA,CAAA,EAAG;MACrB,IAAMvC,GAAG,GAAG;QAAEA,GAAG,EAAE;MAAU,CAAC;MAC9B,IAAI,IAAI,CAACJ,WAAW,CAAC+C,cAAc,EAAE;QACnC3C,GAAG,CAAC4C,OAAO,GAAG,IAAI,CAAChD,WAAW,CAAC+C,cAAc;MAC/C;MACA3C,GAAG,CAACgC,OAAO,GAAG,IAAI,CAACpC,WAAW,CAACmB,kBAAkB,IAAI,IAAI,CAACnB,WAAW,CAACkC,qBAAqB,CAAC,CAAC,CAAC;MAC9F,IAAI,CAAClC,WAAW,CAACmB,kBAAkB,GAAGf,GAAG,CAACgC,OAAO;MACjDhC,GAAG,CAAC6C,OAAO,GAAG,IAAI,CAACjD,WAAW,CAACkC,qBAAqB;MACpD,OAAO9B,GAAG;IACZ;IAAC,OATDuC,oBAAoB;EAAA;EAWpB;AACF;AACA;AACA;EAHE;EAAA1C,MAAA,CAIA2C,gCAAgC;IAAhC,SAAAA,gCAAgCA,CAAA,EAAG;MACjC,IAAMM,MAAM,GAAG,IAAI,CAAClD,WAAW,CAACmD,wBAAwB;MACxD,IAAID,MAAM,CAACtH,MAAM,KAAK,CAAC,EAAE;MAEzB,IAAMwH,kBAAkB,GAAGF,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO;MAC5CH,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,GAAGD,kBAAkB,CAACE,MAAM,CAC3C,UAAAxE,aAAa,EAAI;QACf;QACA;QACA,IAAIA,aAAa,CAACyE,WAAW,IAAIzE,aAAa,CAAC0E,OAAO,EAAE;UACtD1E,aAAa,CAAC2E,aAAa,CACzB,IAAIvK,MAAM,CAACwK,KAAK,CACd,mBAAmB,EACnB,iEAAiE,GACjE,8DACF,CACF,CAAC;QACH;;QAEA;QACA,OAAO,EAAE5E,aAAa,CAACyE,WAAW,IAAIzE,aAAa,CAAC0E,OAAO,CAAC;MAC9D,CACF,CAAC;;MAED;MACA,IAAIN,MAAM,CAACtH,MAAM,GAAG,CAAC,IAAIsH,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,CAACzH,MAAM,KAAK,CAAC,EAAE;QACvDsH,MAAM,CAACS,KAAK,CAAC,CAAC;MAChB;;MAEA;MACApK,MAAM,CAACqK,MAAM,CAAC,IAAI,CAAC5D,WAAW,CAAC6D,eAAe,CAAC,CAAChJ,OAAO,CAAC,UAAAiJ,OAAO,EAAI;QACjEA,OAAO,CAACP,WAAW,GAAG,KAAK;MAC7B,CAAC,CAAC;IACJ;IAAC,OAjCDX,gCAAgC;EAAA;EAmChC;AACF;AACA;AACA;EAHE;EAAA3C,MAAA,CAIA6C,oBAAoB;IAApB,SAAAA,oBAAoBA,CAAA,EAAG;MAAA,IAAA/F,KAAA;MACrBxD,MAAM,CAACwK,OAAO,CAAC,IAAI,CAAC/D,WAAW,CAACgE,cAAc,CAAC,CAACnJ,OAAO,CAAC,UAAAoJ,IAAA,EAAe;QAAA,IAAAC,KAAA,GAAAtE,cAAA,CAAAqE,IAAA;UAAbpC,EAAE,GAAAqC,KAAA;UAAEC,GAAG,GAAAD,KAAA;QAC/DnH,KAAI,CAACiD,WAAW,CAACoE,WAAW,CAAC;UAC3BhE,GAAG,EAAE,KAAK;UACVyB,EAAE,EAAEA,EAAE;UACN/G,IAAI,EAAEqJ,GAAG,CAACrJ,IAAI;UACduE,MAAM,EAAE8E,GAAG,CAAC9E;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAAC,OATDyD,oBAAoB;EAAA;EAAA,OAAAhD,wBAAA;AAAA,I;;;;;;;;;;;AC/LtB,IAAID,mBAAmB;AAAC/G,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC4G,mBAAmB,GAAC5G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhHH,MAAM,CAACoC,MAAM,CAAC;EAACmJ,kBAAkB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,kBAAkB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,OAAO;AAACxL,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;EAACuL,OAAO,EAAC,SAAAA,CAASrL,CAAC,EAAC;IAACqL,OAAO,GAACrL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsL,YAAY;AAACzL,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;EAACwL,YAAY,EAAC,SAAAA,CAAStL,CAAC,EAAC;IAACsL,YAAY,GAACtL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIuL,MAAM;AAAC1L,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAACyL,MAAM,EAAC,SAAAA,CAASvL,CAAC,EAAC;IAACuL,MAAM,GAACvL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIwL,OAAO;AAAC3L,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAAC0L,OAAO,EAAC,SAAAA,CAASxL,CAAC,EAAC;IAACwL,OAAO,GAACxL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAKtZoL,kBAAkB;EAC7B,SAAAA,mBAAY1J,UAAU,EAAE;IACtB,IAAI,CAACqF,WAAW,GAAGrF,UAAU;EAC/B;;EAEA;AACF;AACA;AACA;AACA;EAJE,IAAAsF,MAAA,GAAAoE,kBAAA,CAAA5H,SAAA;EAAAwD,MAAA,CAKMyE,cAAc;IAApB,SAAMA,cAAcA,CAACtE,GAAG,EAAEuE,OAAO;MAAA,IAAAC,IAAA,EAAA/C,EAAA,EAAAgD,SAAA,EAAAC,UAAA,EAAAC,UAAA;MAAA,OAAAlF,mBAAA,CAAAQ,KAAA;QAAA,SAAA2E,gBAAAzE,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACzBmE,IAAI,GAAG,IAAI,CAAC5E,WAAW;cACvB6B,EAAE,GAAGyC,OAAO,CAACW,OAAO,CAAC7E,GAAG,CAACyB,EAAE,CAAC;cAC5BgD,SAAS,GAAGD,IAAI,CAACM,aAAa,CAAC9E,GAAG,CAAC+E,UAAU,EAAEtD,EAAE,CAAC;cAAA,KAEpDgD,SAAS;gBAAAtE,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACX;cACMqE,UAAU,GAAGD,SAAS,CAACO,QAAQ,KAAKvJ,SAAS;cAEnDgJ,SAAS,CAACO,QAAQ,GAAGhF,GAAG,CAACiF,MAAM,IAAI9L,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;cACtDqL,SAAS,CAACO,QAAQ,CAACE,GAAG,GAAGzD,EAAE;cAAC,KAExB+C,IAAI,CAACW,YAAY;gBAAAhF,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAKMwD,IAAI,CAACY,OAAO,CAACpF,GAAG,CAAC+E,UAAU,CAAC,CAACM,MAAM,CAACrF,GAAG,CAACyB,EAAE,CAAC;YAAA;cAA9DkD,UAAU,GAAAxE,QAAA,CAAAmF,IAAA;cAChB,IAAIX,UAAU,KAAKlJ,SAAS,EAAEuE,GAAG,CAACiF,MAAM,GAAGN,UAAU;cAErDH,IAAI,CAACe,WAAW,CAAChB,OAAO,EAAEvE,GAAG,CAAC+E,UAAU,EAAE/E,GAAG,CAAC;cAACG,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAA,KACtCqE,UAAU;gBAAAvE,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MACb,IAAIiD,KAAK,CAAC,mCAAmC,GAAGtD,GAAG,CAACyB,EAAE,CAAC;YAAA;cAAAtB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAG/DmE,IAAI,CAACe,WAAW,CAAChB,OAAO,EAAEvE,GAAG,CAAC+E,UAAU,EAAE/E,GAAG,CAAC;YAAC;YAAA;cAAA,OAAAG,QAAA,CAAAe,IAAA;UAAA;QAAA;QAAA,OAAA0D,eAAA;MAAA,uBAAA1J,OAAA;IAAA;IAElD,OA3BKoJ,cAAc;EAAA;EA6BpB;AACF;AACA;AACA;AACA;EAJE;EAAAzE,MAAA,CAKA2F,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAACxF,GAAG,EAAEuE,OAAO,EAAE;MAC7B,IAAMC,IAAI,GAAG,IAAI,CAAC5E,WAAW;MAC7B,IAAM6E,SAAS,GAAGD,IAAI,CAACM,aAAa,CAAC9E,GAAG,CAAC+E,UAAU,EAAEb,OAAO,CAACW,OAAO,CAAC7E,GAAG,CAACyB,EAAE,CAAC,CAAC;MAE7E,IAAIgD,SAAS,EAAE;QACb,IAAIA,SAAS,CAACO,QAAQ,KAAKvJ,SAAS,EAAE;UACpC,MAAM,IAAI6H,KAAK,CAAC,0CAA0C,GAAGtD,GAAG,CAACyB,EAAE,CAAC;QACtE;QACA0C,YAAY,CAACsB,YAAY,CAAChB,SAAS,CAACO,QAAQ,EAAEhF,GAAG,CAACiF,MAAM,CAAC;MAC3D,CAAC,MAAM;QACLT,IAAI,CAACe,WAAW,CAAChB,OAAO,EAAEvE,GAAG,CAAC+E,UAAU,EAAE/E,GAAG,CAAC;MAChD;IACF;IAAC,OAZDwF,gBAAgB;EAAA;EAchB;AACF;AACA;AACA;AACA;EAJE;EAAA3F,MAAA,CAKA6F,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAAC1F,GAAG,EAAEuE,OAAO,EAAE;MAC7B,IAAMC,IAAI,GAAG,IAAI,CAAC5E,WAAW;MAC7B,IAAM6E,SAAS,GAAGD,IAAI,CAACM,aAAa,CAAC9E,GAAG,CAAC+E,UAAU,EAAEb,OAAO,CAACW,OAAO,CAAC7E,GAAG,CAACyB,EAAE,CAAC,CAAC;MAE7E,IAAIgD,SAAS,EAAE;QACb;QACA,IAAIA,SAAS,CAACO,QAAQ,KAAKvJ,SAAS,EAAE;UACpC,MAAM,IAAI6H,KAAK,CAAC,yCAAyC,GAAGtD,GAAG,CAACyB,EAAE,CAAC;QACrE;QACAgD,SAAS,CAACO,QAAQ,GAAGvJ,SAAS;MAChC,CAAC,MAAM;QACL+I,IAAI,CAACe,WAAW,CAAChB,OAAO,EAAEvE,GAAG,CAAC+E,UAAU,EAAE;UACxC/E,GAAG,EAAE,SAAS;UACd+E,UAAU,EAAE/E,GAAG,CAAC+E,UAAU;UAC1BtD,EAAE,EAAEzB,GAAG,CAACyB;QACV,CAAC,CAAC;MACJ;IACF;IAAC,OAjBDiE,gBAAgB;EAAA;EAmBhB;AACF;AACA;AACA;AACA;EAJE;EAAA7F,MAAA,CAKA8F,cAAc;IAAd,SAAAA,cAAcA,CAAC3F,GAAG,EAAEuE,OAAO,EAAE;MAC3B,IAAMC,IAAI,GAAG,IAAI,CAAC5E,WAAW;;MAE7B;MACA;MACA;MACAI,GAAG,CAAC4F,IAAI,CAACnL,OAAO,CAAC,UAACoL,KAAK,EAAK;QAC1BrB,IAAI,CAACsB,+BAA+B,CAAC,YAAM;UACzC,IAAMC,SAAS,GAAGvB,IAAI,CAACZ,cAAc,CAACiC,KAAK,CAAC;UAC5C;UACA,IAAI,CAACE,SAAS,EAAE;UAChB;UACA,IAAIA,SAAS,CAACC,KAAK,EAAE;UACrBD,SAAS,CAACC,KAAK,GAAG,IAAI;UACtBD,SAAS,CAACE,aAAa,IAAIF,SAAS,CAACE,aAAa,CAAC,CAAC;UACpDF,SAAS,CAACG,SAAS,CAACC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAAC,OAlBDR,cAAc;EAAA;EAoBd;AACF;AACA;AACA;AACA;EAJE;EAAA9F,MAAA,CAKAuG,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAACpG,GAAG,EAAEuE,OAAO,EAAE;MAC7B,IAAMC,IAAI,GAAG,IAAI,CAAC5E,WAAW;MAC7B;MACAI,GAAG,CAACiD,OAAO,CAACxI,OAAO,CAAC,UAAC4L,QAAQ,EAAK;QAChC,IAAMC,IAAI,GAAG9B,IAAI,CAAC+B,uBAAuB,CAACF,QAAQ,CAAC,IAAI,CAAC,CAAC;QACzDlN,MAAM,CAACqK,MAAM,CAAC8C,IAAI,CAAC,CAAC7L,OAAO,CAAC,UAAC+L,OAAO,EAAK;UACvC,IAAM/B,SAAS,GAAGD,IAAI,CAACM,aAAa,CAAC0B,OAAO,CAACzB,UAAU,EAAEyB,OAAO,CAAC/E,EAAE,CAAC;UACpE,IAAI,CAACgD,SAAS,EAAE;YACd,MAAM,IAAInB,KAAK,CAAC,qBAAqB,GAAGmD,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,CAAC;UAClE;UACA,IAAI,CAAC/B,SAAS,CAACkC,cAAc,CAACN,QAAQ,CAAC,EAAE;YACvC,MAAM,IAAI/C,KAAK,CACb,MAAM,GACNmD,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,GACvB,yBAAyB,GACzBH,QACF,CAAC;UACH;UACA,OAAO5B,SAAS,CAACkC,cAAc,CAACN,QAAQ,CAAC;UACzC,IAAIhC,OAAO,CAACI,SAAS,CAACkC,cAAc,CAAC,EAAE;YACrC;YACA;YACA;YACA;;YAEA;YACA;YACA;YACAnC,IAAI,CAACe,WAAW,CAAChB,OAAO,EAAEiC,OAAO,CAACzB,UAAU,EAAE;cAC5C/E,GAAG,EAAE,SAAS;cACdyB,EAAE,EAAEyC,OAAO,CAAC0C,WAAW,CAACJ,OAAO,CAAC/E,EAAE,CAAC;cACnCoF,OAAO,EAAEpC,SAAS,CAACO;YACrB,CAAC,CAAC;YACF;YACAP,SAAS,CAACqC,cAAc,CAACrM,OAAO,CAAC,UAACsM,CAAC,EAAK;cACtCA,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;;YAEF;YACA;YACA;YACAvC,IAAI,CAACwC,gBAAgB,CAACR,OAAO,CAACzB,UAAU,CAAC,CAACkC,MAAM,CAACT,OAAO,CAAC/E,EAAE,CAAC;UAC9D;QACF,CAAC,CAAC;QACF,OAAO+C,IAAI,CAAC+B,uBAAuB,CAACF,QAAQ,CAAC;;QAE7C;QACA;QACA,IAAMa,eAAe,GAAG1C,IAAI,CAACf,eAAe,CAAC4C,QAAQ,CAAC;QACtD,IAAI,CAACa,eAAe,EAAE;UACpB,MAAM,IAAI5D,KAAK,CAAC,iCAAiC,GAAG+C,QAAQ,CAAC;QAC/D;QAEA7B,IAAI,CAACsB,+BAA+B,CAClC;UAAA,OAAaoB,eAAe,CAACC,WAAW,CAAA5K,KAAA,CAA3B2K,eAAe,EAAA3L,SAAoB,CAAC;QAAA,CACnD,CAAC;MACH,CAAC,CAAC;IACJ;IAAC,OAzDD6K,gBAAgB;EAAA;EA2DhB;AACF;AACA;AACA;AACA;AACA;AACA;EANE;EAAAvG,MAAA,CAOA0F,WAAW;IAAX,SAAAA,WAAWA,CAAChB,OAAO,EAAEQ,UAAU,EAAE/E,GAAG,EAAE;MACpC,IAAI,CAACoE,MAAM,CAAC5F,IAAI,CAAC+F,OAAO,EAAEQ,UAAU,CAAC,EAAE;QACrCR,OAAO,CAACQ,UAAU,CAAC,GAAG,EAAE;MAC1B;MACAR,OAAO,CAACQ,UAAU,CAAC,CAACqC,IAAI,CAACpH,GAAG,CAAC;IAC/B;IAAC,OALDuF,WAAW;EAAA;EAOX;AACF;AACA;AACA;AACA;AACA;AACA;EANE;EAAA1F,MAAA,CAOAiF,aAAa;IAAb,SAAAA,aAAaA,CAACC,UAAU,EAAEtD,EAAE,EAAE;MAC5B,IAAM+C,IAAI,GAAG,IAAI,CAAC5E,WAAW;MAC7B,IAAI,CAACwE,MAAM,CAAC5F,IAAI,CAACgG,IAAI,CAACwC,gBAAgB,EAAEjC,UAAU,CAAC,EAAE;QACnD,OAAO,IAAI;MACb;MACA,IAAMsC,uBAAuB,GAAG7C,IAAI,CAACwC,gBAAgB,CAACjC,UAAU,CAAC;MACjE,OAAOsC,uBAAuB,CAACrK,GAAG,CAACyE,EAAE,CAAC,IAAI,IAAI;IAChD;IAAC,OAPDqD,aAAa;EAAA;EAAA,OAAAb,kBAAA;AAAA,I;;;;;;;;;;;;;ACrMf,IAAIqD,kBAAkB;AAAC5O,MAAM,CAACC,IAAI,CAAC,0CAA0C,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAACyO,kBAAkB,GAACzO,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI0O,wBAAwB;AAAC7O,MAAM,CAACC,IAAI,CAAC,gDAAgD,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC0O,wBAAwB,GAAC1O,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI4G,mBAAmB;AAAC/G,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC4G,mBAAmB,GAAC5G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI2G,cAAc;AAAC9G,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC2G,cAAc,GAAC3G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI2O,+BAA+B;AAAC9O,MAAM,CAACC,IAAI,CAAC,uDAAuD,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC2O,+BAA+B,GAAC3O,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI4O,OAAO;AAAC/O,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC4O,OAAO,GAAC5O,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI+B,aAAa;AAAClC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC+B,aAAa,GAAC/B,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA51BH,MAAM,CAACoC,MAAM,CAAC;EAACC,UAAU,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,UAAU;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIjC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAM,EAAC,SAAAA,CAASD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI8G,SAAS;AAACjH,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACgH,SAAS,EAAC,SAAAA,CAAS9G,CAAC,EAAC;IAAC8G,SAAS,GAAC9G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI6O,OAAO;AAAChP,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAAC+O,OAAO,EAAC,SAAAA,CAAS7O,CAAC,EAAC;IAAC6O,OAAO,GAAC7O,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI8O,KAAK;AAACjP,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACgP,KAAK,EAAC,SAAAA,CAAS9O,CAAC,EAAC;IAAC8O,KAAK,GAAC9O,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI+O,MAAM;AAAClP,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACiP,MAAM,EAAC,SAAAA,CAAS/O,CAAC,EAAC;IAAC+O,MAAM,GAAC/O,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIqL,OAAO;AAACxL,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;EAACuL,OAAO,EAAC,SAAAA,CAASrL,CAAC,EAAC;IAACqL,OAAO,GAACrL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAID,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACC,GAAG,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIgP,aAAa;AAACnP,MAAM,CAACC,IAAI,CAAC,kBAAkB,EAAC;EAACkP,aAAa,EAAC,SAAAA,CAAShP,CAAC,EAAC;IAACgP,aAAa,GAAChP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIuL,MAAM,EAAC0D,KAAK,EAAClH,IAAI,EAACyD,OAAO,EAAC0D,IAAI;AAACrP,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAACyL,MAAM,EAAC,SAAAA,CAASvL,CAAC,EAAC;IAACuL,MAAM,GAACvL,CAAC;EAAA,CAAC;EAACiP,KAAK,EAAC,SAAAA,CAASjP,CAAC,EAAC;IAACiP,KAAK,GAACjP,CAAC;EAAA,CAAC;EAAC+H,IAAI,EAAC,SAAAA,CAAS/H,CAAC,EAAC;IAAC+H,IAAI,GAAC/H,CAAC;EAAA,CAAC;EAACwL,OAAO,EAAC,SAAAA,CAASxL,CAAC,EAAC;IAACwL,OAAO,GAACxL,CAAC;EAAA,CAAC;EAACkP,IAAI,EAAC,SAAAA,CAASlP,CAAC,EAAC;IAACkP,IAAI,GAAClP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI6G,wBAAwB;AAAChH,MAAM,CAACC,IAAI,CAAC,8BAA8B,EAAC;EAAC+G,wBAAwB,EAAC,SAAAA,CAAS7G,CAAC,EAAC;IAAC6G,wBAAwB,GAAC7G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAImP,UAAU;AAACtP,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACqP,UAAU,EAAC,SAAAA,CAASnP,CAAC,EAAC;IAACmP,UAAU,GAACnP,CAAC;EAAA;AAAC,CAAC,EAAC,EAAE,CAAC;AAAC,IAAIoP,iBAAiB;AAACvP,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;EAACsP,iBAAiB,EAAC,SAAAA,CAASpP,CAAC,EAAC;IAACoP,iBAAiB,GAACpP,CAAC;EAAA;AAAC,CAAC,EAAC,EAAE,CAAC;AAAC,IAAIoL,kBAAkB;AAACvL,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;EAACsL,kBAAkB,EAAC,SAAAA,CAASpL,CAAC,EAAC;IAACoL,kBAAkB,GAACpL,CAAC;EAAA;AAAC,CAAC,EAAC,EAAE,CAAC;AAAC,IAwC3zCkC,UAAU;EACrB,SAAAA,WAAYmN,GAAG,EAAEhK,OAAO,EAAE;IAAA,IAAAvB,KAAA;IACxB,IAAM6H,IAAI,GAAG,IAAI;IAEjB,IAAI,CAACtG,OAAO,GAAGA,OAAO,GAAAtD,aAAA;MACpB0G,WAAW,WAAAA,CAAA,EAAG,CAAC,CAAC;MAChB7H,8BAA8B,WAAAA,CAACC,WAAW,EAAE;QAC1CZ,MAAM,CAACa,MAAM,CAACD,WAAW,CAAC;MAC5B,CAAC;MACDyO,iBAAiB,EAAE,KAAK;MACxBC,gBAAgB,EAAE,KAAK;MACvBC,cAAc,EAAElP,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACnC;MACAkP,qBAAqB,EAAE,KAAK;MAC5BC,oBAAoB,EAAE5I,SAAS,CAAC6I,sBAAsB;MACtDjP,KAAK,EAAE,IAAI;MACXiI,cAAc,EAAE,IAAI;MACpB;MACAiH,sBAAsB,EAAE,CAAC;MACzB;MACAC,oBAAoB,EAAE;IAAG,GAEtBxK,OAAO,CACX;;IAED;IACA;IACA;IACA;IACA;IACAsG,IAAI,CAACmE,WAAW,GAAG,IAAI;;IAEvB;IACA,IAAIlB,OAAA,CAAOS,GAAG,MAAK,QAAQ,EAAE;MAC3B1D,IAAI,CAACnG,OAAO,GAAG6J,GAAG;IACpB,CAAC,MAAM;MA3EX,IAAIU,YAAY;MAAClQ,MAAM,CAACC,IAAI,CAAC,6BAA6B,EAAC;QAACiQ,YAAY,EAAC,SAAAA,CAAS/P,CAAC,EAAC;UAAC+P,YAAY,GAAC/P,CAAC;QAAA;MAAC,CAAC,EAAC,EAAE,CAAC;MA8EnG2L,IAAI,CAACnG,OAAO,GAAG,IAAIuK,YAAY,CAACV,GAAG,EAAE;QACnC3O,KAAK,EAAE2E,OAAO,CAAC3E,KAAK;QACpBsP,eAAe,EAAEjQ,GAAG,CAACiQ,eAAe;QACpCC,OAAO,EAAE5K,OAAO,CAAC4K,OAAO;QACxBC,cAAc,EAAE7K,OAAO,CAAC6K,cAAc;QACtC;QACA;QACA;QACA;QACA;QACAC,gBAAgB,EAAE9K,OAAO,CAAC8K,gBAAgB;QAC1CC,gBAAgB,EAAE/K,OAAO,CAAC+K,gBAAgB;QAC1CZ,cAAc,EAAEnK,OAAO,CAACmK;MAC1B,CAAC,CAAC;IACJ;IAEA7D,IAAI,CAAC7B,cAAc,GAAG,IAAI;IAC1B6B,IAAI,CAACzD,kBAAkB,GAAG,IAAI,CAAC,CAAC;IAChCyD,IAAI,CAAC1D,QAAQ,GAAG,IAAI,CAAC,CAAC;IACtB0D,IAAI,CAACY,OAAO,GAAGjM,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACpCoL,IAAI,CAAC0E,eAAe,GAAG/P,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5CoL,IAAI,CAAC2E,aAAa,GAAG,CAAC;IACtB3E,IAAI,CAAC1C,qBAAqB,GAAG5D,OAAO,CAACqK,oBAAoB;IAEzD/D,IAAI,CAAC4E,kBAAkB,GAAGlL,OAAO,CAACiK,iBAAiB;IACnD3D,IAAI,CAAC6E,iBAAiB,GAAGnL,OAAO,CAACkK,gBAAgB;;IAEjD;IACA;IACA;IACA;IACA5D,IAAI,CAACf,eAAe,GAAGtK,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;;IAE1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAoL,IAAI,CAACzB,wBAAwB,GAAG,EAAE;;IAElC;IACA;IACA;IACA;IACAyB,IAAI,CAAC+B,uBAAuB,GAAG,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA/B,IAAI,CAACwC,gBAAgB,GAAG,CAAC,CAAC;;IAE1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAxC,IAAI,CAAC8E,qBAAqB,GAAG,EAAE;;IAE/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA9E,IAAI,CAAC+E,gCAAgC,GAAG,EAAE;IAC1C;IACA;IACA;IACA/E,IAAI,CAACgF,0BAA0B,GAAG,CAAC,CAAC;IACpC;IACA;IACAhF,IAAI,CAACiF,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA;IACAjF,IAAI,CAACW,YAAY,GAAG,KAAK;;IAEzB;IACAX,IAAI,CAACkF,wBAAwB,GAAG,CAAC,CAAC;IAClC;IACAlF,IAAI,CAACmF,aAAa,GAAG,IAAI;IACzB;IACAnF,IAAI,CAACoF,eAAe,GAAG,CAAC,CAAC;IACzB;IACApF,IAAI,CAACqF,sBAAsB,GAAG,IAAI;IAClC;IACArF,IAAI,CAACsF,0BAA0B,GAAG,IAAI;IAEtCtF,IAAI,CAACuF,uBAAuB,GAAG7L,OAAO,CAACuK,sBAAsB;IAC7DjE,IAAI,CAACwF,qBAAqB,GAAG9L,OAAO,CAACwK,oBAAoB;;IAEzD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAlE,IAAI,CAACZ,cAAc,GAAG,CAAC,CAAC;;IAExB;IACAY,IAAI,CAACyF,OAAO,GAAG,IAAI;IACnBzF,IAAI,CAAC0F,WAAW,GAAG,IAAIxC,OAAO,CAACyC,UAAU,CAAC,CAAC;;IAE3C;IACA,IAAIrR,MAAM,CAACsR,QAAQ,IACjBxQ,OAAO,CAACC,MAAM,IACd,CAAEqE,OAAO,CAACoK,qBAAqB,EAAE;MACjC1O,OAAO,CAACC,MAAM,CAACE,MAAM,CAACI,UAAU,CAAC,UAAAZ,KAAK,EAAI;QACxC,IAAI,CAAEiL,IAAI,CAAClI,eAAe,CAAC,CAAC,EAAE;UAC5BkI,IAAI,CAACmF,aAAa,GAAGpQ,KAAK;UAC1B,OAAO,CAAC,KAAK,CAAC;QAChB,CAAC,MAAM;UACL,OAAO,CAAC,IAAI,CAAC;QACf;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,CAAC8Q,eAAe,GAAG,IAAI3K,wBAAwB,CAAC,IAAI,CAAC;IAEzD,IAAM4K,YAAY,GAAG,SAAAA,CAAA,EAAM;MACzB,IAAI3N,KAAI,CAAC8D,UAAU,EAAE;QACnB9D,KAAI,CAAC8D,UAAU,CAACS,IAAI,CAAC,CAAC;QACtBvE,KAAI,CAAC8D,UAAU,GAAG,IAAI;MACxB;IACF,CAAC;IAED,IAAI3H,MAAM,CAACyR,QAAQ,EAAE;MACnB,IAAI,CAAClM,OAAO,CAACmM,EAAE,CACb,SAAS,EACT1R,MAAM,CAAC2R,eAAe,CACpB,UAAAzK,GAAG;QAAA,OAAIrD,KAAI,CAAC0N,eAAe,CAACvK,SAAS,CAACE,GAAG,CAAC;MAAA,GAC1C,sBACF,CACF,CAAC;MACD,IAAI,CAAC3B,OAAO,CAACmM,EAAE,CACb,OAAO,EACP1R,MAAM,CAAC2R,eAAe,CACpB;QAAA,OAAM9N,KAAI,CAAC0N,eAAe,CAAC/H,OAAO,CAAC,CAAC;MAAA,GACpC,oBACF,CACF,CAAC;MACD,IAAI,CAACjE,OAAO,CAACmM,EAAE,CACb,YAAY,EACZ1R,MAAM,CAAC2R,eAAe,CAACH,YAAY,EAAE,yBAAyB,CAChE,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACjM,OAAO,CAACmM,EAAE,CAAC,SAAS,EAAE,UAAAxK,GAAG;QAAA,OAAIrD,KAAI,CAAC0N,eAAe,CAACvK,SAAS,CAACE,GAAG,CAAC;MAAA,EAAC;MACtE,IAAI,CAAC3B,OAAO,CAACmM,EAAE,CAAC,OAAO,EAAE;QAAA,OAAM7N,KAAI,CAAC0N,eAAe,CAAC/H,OAAO,CAAC,CAAC;MAAA,EAAC;MAC9D,IAAI,CAACjE,OAAO,CAACmM,EAAE,CAAC,YAAY,EAAEF,YAAY,CAAC;IAC7C;IAEA,IAAI,CAACI,kBAAkB,GAAG,IAAIzC,iBAAiB,CAAC,IAAI,CAAC;;IAErD;IACA,IAAI,CAAC5G,mBAAmB,GAAG,UAACrB,GAAG;MAAA,OAAKrD,KAAI,CAAC+N,kBAAkB,CAACrJ,mBAAmB,CAACrB,GAAG,CAAC;IAAA;IACpF,IAAI,CAAC0B,cAAc,GAAG,UAAC1B,GAAG;MAAA,OAAKrD,KAAI,CAAC+N,kBAAkB,CAAChJ,cAAc,CAAC1B,GAAG,CAAC;IAAA;IAC1E,IAAI,CAAC2B,eAAe,GAAG,UAAC3B,GAAG;MAAA,OAAKrD,KAAI,CAAC+N,kBAAkB,CAAC/I,eAAe,CAAC3B,GAAG,CAAC;IAAA;IAC5E,IAAI,CAAC4B,gBAAgB,GAAG,UAAC5B,GAAG;MAAA,OAAKrD,KAAI,CAAC+N,kBAAkB,CAAC9I,gBAAgB,CAAC5B,GAAG,CAAC;IAAA;IAC9E,IAAI,CAAC6B,eAAe,GAAG,UAAC7B,GAAG;MAAA,OAAKrD,KAAI,CAAC+N,kBAAkB,CAAC7I,eAAe,CAAC7B,GAAG,CAAC;IAAA;IAE5E,IAAI,CAAC2K,mBAAmB,GAAG,IAAI1G,kBAAkB,CAAC,IAAI,CAAC;;IAEvD;IACA,IAAI,CAACK,cAAc,GAAG,UAACtE,GAAG,EAAEuE,OAAO;MAAA,OAAK5H,KAAI,CAACgO,mBAAmB,CAACrG,cAAc,CAACtE,GAAG,EAAEuE,OAAO,CAAC;IAAA;IAC7F,IAAI,CAACiB,gBAAgB,GAAG,UAACxF,GAAG,EAAEuE,OAAO;MAAA,OAAK5H,KAAI,CAACgO,mBAAmB,CAACnF,gBAAgB,CAACxF,GAAG,EAAEuE,OAAO,CAAC;IAAA;IACjG,IAAI,CAACmB,gBAAgB,GAAG,UAAC1F,GAAG,EAAEuE,OAAO;MAAA,OAAK5H,KAAI,CAACgO,mBAAmB,CAACjF,gBAAgB,CAAC1F,GAAG,EAAEuE,OAAO,CAAC;IAAA;IACjG,IAAI,CAACoB,cAAc,GAAG,UAAC3F,GAAG,EAAEuE,OAAO;MAAA,OAAK5H,KAAI,CAACgO,mBAAmB,CAAChF,cAAc,CAAC3F,GAAG,EAAEuE,OAAO,CAAC;IAAA;IAC7F,IAAI,CAAC6B,gBAAgB,GAAG,UAACpG,GAAG,EAAEuE,OAAO;MAAA,OAAK5H,KAAI,CAACgO,mBAAmB,CAACvE,gBAAgB,CAACpG,GAAG,EAAEuE,OAAO,CAAC;IAAA;;IAEjG;IACA,IAAI,CAACgB,WAAW,GAAG,UAAChB,OAAO,EAAEQ,UAAU,EAAE/E,GAAG;MAAA,OAC1CrD,KAAI,CAACgO,mBAAmB,CAACpF,WAAW,CAAChB,OAAO,EAAEQ,UAAU,EAAE/E,GAAG,CAAC;IAAA;IAChE,IAAI,CAAC8E,aAAa,GAAG,UAACC,UAAU,EAAEtD,EAAE;MAAA,OAClC9E,KAAI,CAACgO,mBAAmB,CAAC7F,aAAa,CAACC,UAAU,EAAEtD,EAAE,CAAC;IAAA;EAC1D;;EAEA;EACA;EACA;EAAA,IAAA5B,MAAA,GAAA9E,UAAA,CAAAsB,SAAA;EAAAwD,MAAA,CACA+K,kBAAkB;IAAlB,SAAAA,kBAAkBA,CAAClQ,IAAI,EAAEmQ,YAAY,EAAE;MACrC,IAAMrG,IAAI,GAAG,IAAI;MAEjB,IAAI9J,IAAI,IAAI8J,IAAI,CAACY,OAAO,EAAE,OAAO,KAAK;;MAEtC;MACA;MACA,IAAM0F,KAAK,GAAG3R,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACjC,IAAM2R,WAAW,GAAG,CAClB,QAAQ,EACR,aAAa,EACb,WAAW,EACX,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,gBAAgB,CACjB;MACDA,WAAW,CAACtQ,OAAO,CAAC,UAACuQ,MAAM,EAAK;QAC9BF,KAAK,CAACE,MAAM,CAAC,GAAG,YAAa;UAC3B,IAAIH,YAAY,CAACG,MAAM,CAAC,EAAE;YACxB,OAAOH,YAAY,CAACG,MAAM,CAAC,CAAAzO,KAAA,CAApBsO,YAAY,EAAAtP,SAAgB,CAAC;UACtC;QACF,CAAC;MACH,CAAC,CAAC;MACFiJ,IAAI,CAACY,OAAO,CAAC1K,IAAI,CAAC,GAAGoQ,KAAK;MAC1B,OAAOA,KAAK;IACd;IAAC,OA1BDF,kBAAkB;EAAA;EAAA/K,MAAA,CA4BlBoL,mBAAmB;IAAnB,SAAAA,mBAAmBA,CAACvQ,IAAI,EAAEmQ,YAAY,EAAE;MACtC,IAAMrG,IAAI,GAAG,IAAI;MAEjB,IAAMsG,KAAK,GAAGtG,IAAI,CAACoG,kBAAkB,CAAClQ,IAAI,EAAEmQ,YAAY,CAAC;MAEzD,IAAMK,MAAM,GAAG1G,IAAI,CAACkF,wBAAwB,CAAChP,IAAI,CAAC;MAClD,IAAIyQ,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;QACzBJ,KAAK,CAACO,WAAW,CAACH,MAAM,CAAC1P,MAAM,EAAE,KAAK,CAAC;QACvC0P,MAAM,CAACzQ,OAAO,CAAC,UAAAuF,GAAG,EAAI;UACpB8K,KAAK,CAACQ,MAAM,CAACtL,GAAG,CAAC;QACnB,CAAC,CAAC;QACF8K,KAAK,CAACS,SAAS,CAAC,CAAC;QACjB,OAAO/G,IAAI,CAACkF,wBAAwB,CAAChP,IAAI,CAAC;MAC5C;MAEA,OAAO,IAAI;IACb;IAAC,OAhBDuQ,mBAAmB;EAAA;EAAApL,MAAA,CAiBb2L,mBAAmB;IAAzB,SAAMA,mBAAmBA,CAAC9Q,IAAI,EAAEmQ,YAAY;MAAA,IAAArG,IAAA,EAAAsG,KAAA,EAAAI,MAAA,EAAAO,SAAA,EAAAC,KAAA,EAAA1L,GAAA;MAAA,OAAAP,mBAAA,CAAAQ,KAAA;QAAA,SAAA0L,qBAAAxL,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACpCmE,IAAI,GAAG,IAAI;cAEXsG,KAAK,GAAGtG,IAAI,CAACoG,kBAAkB,CAAClQ,IAAI,EAAEmQ,YAAY,CAAC;cAEnDK,MAAM,GAAG1G,IAAI,CAACkF,wBAAwB,CAAChP,IAAI,CAAC;cAAA,KAC9CyQ,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC;gBAAA/K,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACjB8J,KAAK,CAACO,WAAW,CAACH,MAAM,CAAC1P,MAAM,EAAE,KAAK,CAAC;YAAA;cAAAiQ,SAAA,GAAAjE,+BAAA,CAC3B0D,MAAM;YAAA;cAAA,KAAAQ,KAAA,GAAAD,SAAA,IAAAG,IAAA;gBAAAzL,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAbL,GAAG,GAAA0L,KAAA,CAAAG,KAAA;cAAA1L,QAAA,CAAAE,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACN8J,KAAK,CAACQ,MAAM,CAACtL,GAAG,CAAC;YAAA;cAAAG,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAEnB8J,KAAK,CAACS,SAAS,CAAC,CAAC;YAAA;cACvB,OAAO/G,IAAI,CAACkF,wBAAwB,CAAChP,IAAI,CAAC;YAAC;cAAA,OAAAyF,QAAA,CAAAK,MAAA,WAGtC,IAAI;YAAA;YAAA;cAAA,OAAAL,QAAA,CAAAe,IAAA;UAAA;QAAA;QAAA,OAAAyK,oBAAA;MAAA,uBAAAzQ,OAAA;IAAA;IACZ,OAhBKsQ,mBAAmB;EAAA;EAkBzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAfE;EAAA3L,MAAA,CAgBAf,SAAS;IAAT,SAAAA,SAASA,CAACpE,IAAI,CAAC,8CAA8C;MAC3D,IAAM8J,IAAI,GAAG,IAAI;MAEjB,IAAMvF,MAAM,GAAG6I,KAAK,CAACtJ,IAAI,CAACjD,SAAS,EAAE,CAAC,CAAC;MACvC,IAAIuQ,SAAS,GAAG3S,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACnC,IAAI6F,MAAM,CAACzD,MAAM,EAAE;QACjB,IAAMuQ,SAAS,GAAG9M,MAAM,CAACA,MAAM,CAACzD,MAAM,GAAG,CAAC,CAAC;QAC3C,IAAI,OAAOuQ,SAAS,KAAK,UAAU,EAAE;UACnCD,SAAS,CAACE,OAAO,GAAG/M,MAAM,CAACgN,GAAG,CAAC,CAAC;QAClC,CAAC,MAAM,IAAIF,SAAS,IAAI,CACtBA,SAAS,CAACC,OAAO;QACjB;QACA;QACAD,SAAS,CAACG,OAAO,EACjBH,SAAS,CAACI,MAAM,CACjB,CAACC,IAAI,CAAC,UAAAC,CAAC;UAAA,OAAI,OAAOA,CAAC,KAAK,UAAU;QAAA,EAAC,EAAE;UACpCP,SAAS,GAAG7M,MAAM,CAACgN,GAAG,CAAC,CAAC;QAC1B;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAMK,QAAQ,GAAGnT,MAAM,CAACqK,MAAM,CAACgB,IAAI,CAACZ,cAAc,CAAC,CAAC2I,IAAI,CACtD,UAAAxI,GAAG;QAAA,OAAKA,GAAG,CAACyI,QAAQ,IAAIzI,GAAG,CAACrJ,IAAI,KAAKA,IAAI,IAAIiN,KAAK,CAAC8E,MAAM,CAAC1I,GAAG,CAAC9E,MAAM,EAAEA,MAAM,CAAC;MAAA,CAC/E,CAAC;MAED,IAAIwC,EAAE;MACN,IAAI6K,QAAQ,EAAE;QACZ7K,EAAE,GAAG6K,QAAQ,CAAC7K,EAAE;QAChB6K,QAAQ,CAACE,QAAQ,GAAG,KAAK,CAAC,CAAC;;QAE3B,IAAIV,SAAS,CAACE,OAAO,EAAE;UACrB;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIM,QAAQ,CAACtG,KAAK,EAAE;YAClB8F,SAAS,CAACE,OAAO,CAAC,CAAC;UACrB,CAAC,MAAM;YACLM,QAAQ,CAACrG,aAAa,GAAG6F,SAAS,CAACE,OAAO;UAC5C;QACF;;QAEA;QACA;QACA,IAAIF,SAAS,CAACI,OAAO,EAAE;UACrB;UACA;UACAI,QAAQ,CAACI,aAAa,GAAGZ,SAAS,CAACI,OAAO;QAC5C;QAEA,IAAIJ,SAAS,CAACK,MAAM,EAAE;UACpBG,QAAQ,CAACK,YAAY,GAAGb,SAAS,CAACK,MAAM;QAC1C;MACF,CAAC,MAAM;QACL;QACA1K,EAAE,GAAGmG,MAAM,CAACnG,EAAE,CAAC,CAAC;QAChB+C,IAAI,CAACZ,cAAc,CAACnC,EAAE,CAAC,GAAG;UACxBA,EAAE,EAAEA,EAAE;UACN/G,IAAI,EAAEA,IAAI;UACVuE,MAAM,EAAE0I,KAAK,CAACiF,KAAK,CAAC3N,MAAM,CAAC;UAC3BuN,QAAQ,EAAE,KAAK;UACfxG,KAAK,EAAE,KAAK;UACZE,SAAS,EAAE,IAAIwB,OAAO,CAACyC,UAAU,CAAC,CAAC;UACnClE,aAAa,EAAE6F,SAAS,CAACE,OAAO;UAChC;UACAU,aAAa,EAAEZ,SAAS,CAACI,OAAO;UAChCS,YAAY,EAAEb,SAAS,CAACK,MAAM;UAC9B5R,UAAU,EAAEiK,IAAI;UAChByC,MAAM,WAAAA,CAAA,EAAG;YACP,OAAO,IAAI,CAAC1M,UAAU,CAACqJ,cAAc,CAAC,IAAI,CAACnC,EAAE,CAAC;YAC9C,IAAI,CAACuE,KAAK,IAAI,IAAI,CAACE,SAAS,CAACC,OAAO,CAAC,CAAC;UACxC,CAAC;UACDjF,IAAI,WAAAA,CAAA,EAAG;YACL,IAAI,CAAC3G,UAAU,CAACyJ,WAAW,CAAC;cAAEhE,GAAG,EAAE,OAAO;cAAEyB,EAAE,EAAEA;YAAG,CAAC,CAAC;YACrD,IAAI,CAACwF,MAAM,CAAC,CAAC;YAEb,IAAI6E,SAAS,CAACK,MAAM,EAAE;cACpBL,SAAS,CAACK,MAAM,CAAC,CAAC;YACpB;UACF;QACF,CAAC;QACD3H,IAAI,CAACxF,KAAK,CAAC;UAAEgB,GAAG,EAAE,KAAK;UAAEyB,EAAE,EAAEA,EAAE;UAAE/G,IAAI,EAAEA,IAAI;UAAEuE,MAAM,EAAEA;QAAO,CAAC,CAAC;MAChE;;MAEA;MACA,IAAM4N,MAAM,GAAG;QACb3L,IAAI,WAAAA,CAAA,EAAG;UACL,IAAI,CAAEkD,MAAM,CAAC5F,IAAI,CAACgG,IAAI,CAACZ,cAAc,EAAEnC,EAAE,CAAC,EAAE;YAC1C;UACF;UACA+C,IAAI,CAACZ,cAAc,CAACnC,EAAE,CAAC,CAACP,IAAI,CAAC,CAAC;QAChC,CAAC;QACD8E,KAAK,WAAAA,CAAA,EAAG;UACN;UACA,IAAI,CAAC5B,MAAM,CAAC5F,IAAI,CAACgG,IAAI,CAACZ,cAAc,EAAEnC,EAAE,CAAC,EAAE;YACzC,OAAO,KAAK;UACd;UACA,IAAMqL,MAAM,GAAGtI,IAAI,CAACZ,cAAc,CAACnC,EAAE,CAAC;UACtCqL,MAAM,CAAC5G,SAAS,CAAC6G,MAAM,CAAC,CAAC;UACzB,OAAOD,MAAM,CAAC9G,KAAK;QACrB,CAAC;QACDgH,cAAc,EAAEvL;MAClB,CAAC;MAED,IAAIiG,OAAO,CAACuF,MAAM,EAAE;QAClB;QACA;QACA;QACA;QACA;QACA;QACAvF,OAAO,CAACwF,YAAY,CAAC,UAACnG,CAAC,EAAK;UAC1B,IAAI3C,MAAM,CAAC5F,IAAI,CAACgG,IAAI,CAACZ,cAAc,EAAEnC,EAAE,CAAC,EAAE;YACxC+C,IAAI,CAACZ,cAAc,CAACnC,EAAE,CAAC,CAAC+K,QAAQ,GAAG,IAAI;UACzC;UAEA9E,OAAO,CAACyF,UAAU,CAAC,YAAM;YACvB,IAAI/I,MAAM,CAAC5F,IAAI,CAACgG,IAAI,CAACZ,cAAc,EAAEnC,EAAE,CAAC,IACpC+C,IAAI,CAACZ,cAAc,CAACnC,EAAE,CAAC,CAAC+K,QAAQ,EAAE;cACpCK,MAAM,CAAC3L,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA,OAAO2L,MAAM;IACf;IAAC,OApJD/N,SAAS;EAAA;EAsJT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;EAAAe,MAAA,CAQAuN,WAAW;IAAX,SAAAA,WAAWA,CAAA,EAAE;MACX,OAAOxU,GAAG,CAACiE,wBAAwB,CAACwQ,yBAAyB,CAAC,CAAC;IACjE;IAAC,OAFDD,WAAW;EAAA;EAAAvN,MAAA,CAGXoD,OAAO;IAAP,SAAAA,OAAOA,CAACA,QAAO,EAAE;MAAA,IAAA7E,MAAA;MACfjF,MAAM,CAACwK,OAAO,CAACV,QAAO,CAAC,CAACxI,OAAO,CAAC,UAAAoJ,IAAA,EAAkB;QAAA,IAAAC,KAAA,GAAAtE,cAAA,CAAAqE,IAAA;UAAhBnJ,IAAI,GAAAoJ,KAAA;UAAEwJ,IAAI,GAAAxJ,KAAA;QAC1C,IAAI,OAAOwJ,IAAI,KAAK,UAAU,EAAE;UAC9B,MAAM,IAAIhK,KAAK,CAAC,UAAU,GAAG5I,IAAI,GAAG,sBAAsB,CAAC;QAC7D;QACA,IAAI0D,MAAI,CAAC8K,eAAe,CAACxO,IAAI,CAAC,EAAE;UAC9B,MAAM,IAAI4I,KAAK,CAAC,kBAAkB,GAAG5I,IAAI,GAAG,sBAAsB,CAAC;QACrE;QACA0D,MAAI,CAAC8K,eAAe,CAACxO,IAAI,CAAC,GAAG4S,IAAI;MACnC,CAAC,CAAC;IACJ;IAAC,OAVDrK,OAAO;EAAA;EAAApD,MAAA,CAYPxC,gBAAgB;IAAhB,SAAAA,gBAAgBA,CAAAkQ,KAAA,EAAyC;MAAA,IAAvCpQ,eAAe,GAAAoQ,KAAA,CAAfpQ,eAAe;QAAEF,mBAAmB,GAAAsQ,KAAA,CAAnBtQ,mBAAmB;MACpD,IAAI,CAACE,eAAe,EAAE;QACpB,OAAOF,mBAAmB;MAC5B;MACA,OAAOA,mBAAmB,IAAIrE,GAAG,CAACiE,wBAAwB,CAACwQ,yBAAyB,CAAC,CAAC;IACxF;IAAC,OALDhQ,gBAAgB;EAAA;EAOhB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE;EAAAwC,MAAA,CAUArB,IAAI;IAAJ,SAAAA,IAAIA,CAAC9D,IAAI,CAAC,kCAAkC;MAC1C;MACA;MACA,IAAMkC,IAAI,GAAGkL,KAAK,CAACtJ,IAAI,CAACjD,SAAS,EAAE,CAAC,CAAC;MACrC,IAAI4C,QAAQ;MACZ,IAAIvB,IAAI,CAACpB,MAAM,IAAI,OAAOoB,IAAI,CAACA,IAAI,CAACpB,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;QAC9D2C,QAAQ,GAAGvB,IAAI,CAACqP,GAAG,CAAC,CAAC;MACvB;MACA,OAAO,IAAI,CAAC1P,KAAK,CAAC7B,IAAI,EAAEkC,IAAI,EAAEuB,QAAQ,CAAC;IACzC;IAAC,OATDK,IAAI;EAAA;EAUJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE;EAAAqB,MAAA,CAUA2N,SAAS;IAAT,SAAAA,SAASA,CAAC9S,IAAI,CAAC,yBAAyB;MACtC,IAAMkC,IAAI,GAAGkL,KAAK,CAACtJ,IAAI,CAACjD,SAAS,EAAE,CAAC,CAAC;MACrC,IAAIqB,IAAI,CAACpB,MAAM,IAAI,OAAOoB,IAAI,CAACA,IAAI,CAACpB,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;QAC9D,MAAM,IAAI8H,KAAK,CACb,+FACF,CAAC;MACH;MAEA,OAAO,IAAI,CAAC5G,UAAU,CAAChC,IAAI,EAAEkC,IAAI,EAAE;QAAE6Q,yBAAyB,EAAE;MAAK,CAAC,CAAC;IACzE;IAAC,OATDD,SAAS;EAAA;EAWT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAfE;EAAA3N,MAAA,CAgBAtD,KAAK;IAAL,SAAAA,KAAKA,CAAC7B,IAAI,EAAEkC,IAAI,EAAEsB,OAAO,EAAEC,QAAQ,EAAE;MACnC,IAAAuP,eAAA,GAAuD,IAAI,CAACC,SAAS,CAACjT,IAAI,EAAEiN,KAAK,CAACiF,KAAK,CAAChQ,IAAI,CAAC,CAAC;QAAtFgR,cAAc,GAAAF,eAAA,CAAdE,cAAc;QAAEC,UAAU,GAAAH,eAAA,CAAVG,UAAU;QAAKC,WAAW,GAAAvG,wBAAA,CAAAmG,eAAA,EAAAK,SAAA;MAElD,IAAID,WAAW,CAACE,OAAO,EAAE;QACvB,IACE,CAAC,IAAI,CAAC3Q,gBAAgB,CAAC;UACrBJ,mBAAmB,EAAE6Q,WAAW,CAAC7Q,mBAAmB;UACpDE,eAAe,EAAE2Q,WAAW,CAAC3Q;QAC/B,CAAC,CAAC,EACF;UACA,IAAI,CAAC8Q,cAAc,CAAC,CAAC;QACvB;QACA,IAAI;UACFH,WAAW,CAACI,eAAe,GAAGtV,GAAG,CAACiE,wBAAwB,CACvDsR,SAAS,CAACN,UAAU,EAAED,cAAc,CAAC;UACxC,IAAI9U,MAAM,CAACsV,UAAU,CAACN,WAAW,CAACI,eAAe,CAAC,EAAE;YAClDpV,MAAM,CAACa,MAAM,aACDe,IAAI,yIAChB,CAAC;UACH;QACF,CAAC,CAAC,OAAO2T,CAAC,EAAE;UACVP,WAAW,CAACQ,SAAS,GAAGD,CAAC;QAC3B;MACF;MACA,OAAO,IAAI,CAACE,MAAM,CAAC7T,IAAI,EAAEoT,WAAW,EAAElR,IAAI,EAAEsB,OAAO,EAAEC,QAAQ,CAAC;IAChE;IAAC,OAzBD5B,KAAK;EAAA;EA2BL;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAfE;EAAAsD,MAAA,CAgBAnD,UAAU;IAAV,SAAAA,UAAUA,CAAChC,IAAI,EAAEkC,IAAI,EAAEsB,OAAO,EAAmB;MAAA,IAAjBC,QAAQ,GAAA5C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC7C,IAAMS,WAAW,GAAG,IAAI,CAACwS,yBAAyB,CAAC9T,IAAI,EAAEkC,IAAI,EAAEsB,OAAO,CAAC;MAEvE,IAAMvC,OAAO,GAAG,IAAI,CAAC8S,WAAW,CAAC;QAC/B/T,IAAI,EAAJA,IAAI;QACJkC,IAAI,EAAJA,IAAI;QACJsB,OAAO,EAAPA,OAAO;QACPC,QAAQ,EAARA,QAAQ;QACRnC,WAAW,EAAXA;MACF,CAAC,CAAC;MACF,IAAIlD,MAAM,CAACsR,QAAQ,EAAE;QACnB;QACAzO,OAAO,CAACK,WAAW,GAAGA,WAAW,CAAC4B,IAAI,CAAC,UAAA8Q,CAAC,EAAI;UAC1C,IAAIA,CAAC,CAACJ,SAAS,EAAE;YACf,MAAMI,CAAC,CAACJ,SAAS;UACnB;UACA,OAAOI,CAAC,CAACR,eAAe;QAC1B,CAAC,CAAC;QACF;QACAvS,OAAO,CAACQ,aAAa,GAAG,IAAIjB,OAAO,CAAC,UAACC,OAAO,EAAEO,MAAM;UAAA,OAClDC,OAAO,CAACiC,IAAI,CAACzC,OAAO,CAAC,CAACc,KAAK,CAACP,MAAM,CAAC;QAAA,CACrC,CAAC;MACH;MACA,OAAOC,OAAO;IAChB;IAAC,OAxBDe,UAAU;EAAA;EAAAmD,MAAA,CAyBJ2O,yBAAyB;IAA/B,SAAMA,yBAAyBA,CAAC9T,IAAI,EAAEkC,IAAI,EAAEsB,OAAO;MAAA,IAAAyQ,gBAAA,EAAAf,cAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAc,cAAA;MAAA,OAAAnP,mBAAA,CAAAQ,KAAA;QAAA,SAAA4O,2BAAAzN,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cAAAsO,gBAAA,GACM,IAAI,CAAChB,SAAS,CAACjT,IAAI,EAAEiN,KAAK,CAACiF,KAAK,CAAChQ,IAAI,CAAC,EAAEsB,OAAO,CAAC,EAA/F0P,cAAc,GAAAe,gBAAA,CAAdf,cAAc,EAAEC,UAAU,GAAAc,gBAAA,CAAVd,UAAU,EAAKC,WAAW,GAAAvG,wBAAA,CAAAoH,gBAAA,EAAAG,UAAA;cAAA,KAC9ChB,WAAW,CAACE,OAAO;gBAAA5M,SAAA,CAAAf,IAAA;gBAAA;cAAA;cACrB,IACE,CAAC,IAAI,CAAChD,gBAAgB,CAAC;gBACrBJ,mBAAmB,EAAE6Q,WAAW,CAAC7Q,mBAAmB;gBACpDE,eAAe,EAAE2Q,WAAW,CAAC3Q;cAC/B,CAAC,CAAC,EACF;gBACA,IAAI,CAAC8Q,cAAc,CAAC,CAAC;cACvB;cAAC7M,SAAA,CAAAhB,IAAA;cAEC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;cACcwO,cAAc,GAAGhW,GAAG,CAACiE,wBAAwB,CAACkS,2BAA2B,CAC7ElB,UACF,CAAC;cAAAzM,SAAA,CAAAhB,IAAA;cAAAgB,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAEqC4M,cAAc,CAAC,CAAC;YAAA;cAApDE,WAAW,CAACI,eAAe,GAAA9M,SAAA,CAAAkE,IAAA;cAAAlE,SAAA,CAAAf,IAAA;cAAA;YAAA;cAAAe,SAAA,CAAAhB,IAAA;cAAAgB,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAE3B0M,WAAW,CAACQ,SAAS,GAAAlN,SAAA,CAAAb,EAAI;YAAC;cAAAa,SAAA,CAAAhB,IAAA;cAE1BxH,GAAG,CAACiE,wBAAwB,CAACC,IAAI,CAAC8R,cAAc,CAAC;cAAC,OAAAxN,SAAA,CAAA4N,MAAA;YAAA;cAAA5N,SAAA,CAAAf,IAAA;cAAA;YAAA;cAAAe,SAAA,CAAAhB,IAAA;cAAAgB,SAAA,CAAA6N,EAAA,GAAA7N,SAAA;cAGpD0M,WAAW,CAACQ,SAAS,GAAAlN,SAAA,CAAA6N,EAAI;YAAC;cAAA,OAAA7N,SAAA,CAAAZ,MAAA,WAGvBsN,WAAW;YAAA;YAAA;cAAA,OAAA1M,SAAA,CAAAF,IAAA;UAAA;QAAA;QAAA,OAAA2N,0BAAA;MAAA,6CAAA3T,OAAA;IAAA;IACnB,OAnCKsT,yBAAyB;EAAA;EAAA3O,MAAA,CAoCzB4O,WAAW;IAAjB,SAAMA,WAAWA,CAAAS,KAAA;MAAA,IAAAxU,IAAA,EAAAkC,IAAA,EAAAsB,OAAA,EAAAC,QAAA,EAAAnC,WAAA,EAAA8R,WAAA;MAAA,OAAArO,mBAAA,CAAAQ,KAAA;QAAA,SAAAkP,aAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhP,IAAA,GAAAgP,SAAA,CAAA/O,IAAA;YAAA;cAAG3F,IAAI,GAAAwU,KAAA,CAAJxU,IAAI,EAAEkC,IAAI,GAAAsS,KAAA,CAAJtS,IAAI,EAAEsB,OAAO,GAAAgR,KAAA,CAAPhR,OAAO,EAAEC,QAAQ,GAAA+Q,KAAA,CAAR/Q,QAAQ,EAAEnC,WAAW,GAAAkT,KAAA,CAAXlT,WAAW;cAAAoT,SAAA,CAAA/O,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAClChF,WAAW;YAAA;cAA/B8R,WAAW,GAAAsB,SAAA,CAAA9J,IAAA;cAAA,OAAA8J,SAAA,CAAA5O,MAAA,WACV,IAAI,CAAC+N,MAAM,CAAC7T,IAAI,EAAEoT,WAAW,EAAElR,IAAI,EAAEsB,OAAO,EAAEC,QAAQ,CAAC;YAAA;YAAA;cAAA,OAAAiR,SAAA,CAAAlO,IAAA;UAAA;QAAA;QAAA,OAAAiO,YAAA;MAAA,uBAAAjU,OAAA;IAAA;IAC/D,OAHKuT,WAAW;EAAA;EAAA5O,MAAA,CAKjB0O,MAAM;IAAN,SAAAA,MAAMA,CAAC7T,IAAI,EAAE2U,aAAa,EAAEzS,IAAI,EAAEsB,OAAO,EAAEC,QAAQ,EAAE;MACnD,IAAMqG,IAAI,GAAG,IAAI;;MAEjB;MACA;MACA,IAAI,CAACrG,QAAQ,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;QAC9CC,QAAQ,GAAGD,OAAO;QAClBA,OAAO,GAAG/E,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC/B;MACA8E,OAAO,GAAGA,OAAO,IAAI/E,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAExC,IAAI+E,QAAQ,EAAE;QACZ;QACA;QACA;QACAA,QAAQ,GAAGrF,MAAM,CAAC2R,eAAe,CAC/BtM,QAAQ,EACR,iCAAiC,GAAGzD,IAAI,GAAG,GAC7C,CAAC;MACH;MACA,IACEsT,OAAO,GAKLqB,aAAa,CALfrB,OAAO;QACPM,SAAS,GAIPe,aAAa,CAJff,SAAS;QACTJ,eAAe,GAGbmB,aAAa,CAHfnB,eAAe;QACfjR,mBAAmB,GAEjBoS,aAAa,CAFfpS,mBAAmB;QACnBqS,UAAU,GACRD,aAAa,CADfC,UAAU;;MAGZ;MACA;MACA1S,IAAI,GAAG+K,KAAK,CAACiF,KAAK,CAAChQ,IAAI,CAAC;MACxB;MACA;MACA;MACA,IACE,IAAI,CAACS,gBAAgB,CAAC;QACpBJ,mBAAmB,EAAnBA,mBAAmB;QACnBE,eAAe,EAAEkS,aAAa,CAAClS;MACjC,CAAC,CAAC,EACF;QACA,IAAIU,OAAM;QAEV,IAAIM,QAAQ,EAAE;UACZA,QAAQ,CAACmQ,SAAS,EAAEJ,eAAe,CAAC;QACtC,CAAC,MAAM;UACL,IAAII,SAAS,EAAE,MAAMA,SAAS;UAC9BzQ,OAAM,GAAGqQ,eAAe;QAC1B;QAEA,OAAOhQ,OAAO,CAACO,oBAAoB,GAAG;UAAEZ,MAAM,EAANA;QAAO,CAAC,GAAGA,OAAM;MAC3D;;MAEA;MACA;MACA,IAAMwI,QAAQ,GAAG,EAAE,GAAG7B,IAAI,CAAC2E,aAAa,EAAE;MAC1C,IAAI6E,OAAO,EAAE;QACXxJ,IAAI,CAAC+K,0BAA0B,CAAClJ,QAAQ,CAAC;MAC3C;;MAEA;MACA;MACA;MACA;MACA,IAAMmJ,OAAO,GAAG;QACdxP,GAAG,EAAE,QAAQ;QACbyB,EAAE,EAAE4E,QAAQ;QACZ2E,MAAM,EAAEtQ,IAAI;QACZuE,MAAM,EAAErC;MACV,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI0R,SAAS,EAAE;QACb,IAAIpQ,OAAO,CAACuR,mBAAmB,EAAE;UAC/B,MAAMnB,SAAS;QACjB,CAAC,MAAM,IAAI,CAACA,SAAS,CAACoB,eAAe,EAAE;UACrC5W,MAAM,CAACa,MAAM,CACX,qDAAqD,GAAGe,IAAI,GAAG,GAAG,EAClE4T,SACF,CAAC;QACH;MACF;;MAEA;MACA;;MAEA;MACA,IAAI3S,OAAO;MACX,IAAI,CAACwC,QAAQ,EAAE;QACb,IACErF,MAAM,CAACsR,QAAQ,IACf,CAAClM,OAAO,CAACuP,yBAAyB,KACjC,CAACvP,OAAO,CAACf,eAAe,IAAIe,OAAO,CAACyR,eAAe,CAAC,EACrD;UACAxR,QAAQ,GAAG,SAAAA,CAACL,GAAG,EAAK;YAClBA,GAAG,IAAIhF,MAAM,CAACa,MAAM,CAAC,yBAAyB,GAAGe,IAAI,GAAG,GAAG,EAAEoD,GAAG,CAAC;UACnE,CAAC;QACH,CAAC,MAAM;UACLnC,OAAO,GAAG,IAAIT,OAAO,CAAC,UAACC,OAAO,EAAEO,MAAM,EAAK;YACzCyC,QAAQ,GAAG,SAAAA,CAAA,EAAgB;cAAA,SAAAyR,IAAA,GAAArU,SAAA,CAAAC,MAAA,EAAZqU,OAAO,OAAA1E,KAAA,CAAAyE,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;gBAAPD,OAAO,CAAAC,IAAA,IAAAvU,SAAA,CAAAuU,IAAA;cAAA;cACpB,IAAIlT,IAAI,GAAGuO,KAAK,CAAC4E,IAAI,CAACF,OAAO,CAAC;cAC9B,IAAI/R,GAAG,GAAGlB,IAAI,CAAC2G,KAAK,CAAC,CAAC;cACtB,IAAIzF,GAAG,EAAE;gBACPpC,MAAM,CAACoC,GAAG,CAAC;gBACX;cACF;cACA3C,OAAO,CAAAoB,KAAA,SAAIK,IAAI,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAI0S,UAAU,CAACzD,KAAK,KAAK,IAAI,EAAE;QAC7B2D,OAAO,CAACF,UAAU,GAAGA,UAAU,CAACzD,KAAK;MACvC;MAEA,IAAMnN,aAAa,GAAG,IAAImJ,aAAa,CAAC;QACtCxB,QAAQ,EAARA,QAAQ;QACRlI,QAAQ,EAAEA,QAAQ;QAClB5D,UAAU,EAAEiK,IAAI;QAChBwL,gBAAgB,EAAE9R,OAAO,CAAC8R,gBAAgB;QAC1CC,IAAI,EAAE,CAAC,CAAC/R,OAAO,CAAC+R,IAAI;QACpBT,OAAO,EAAEA,OAAO;QAChBpM,OAAO,EAAE,CAAC,CAAClF,OAAO,CAACkF;MACrB,CAAC,CAAC;MAEF,IAAIvF,MAAM;MAEV,IAAIlC,OAAO,EAAE;QACXkC,MAAM,GAAGK,OAAO,CAACyR,eAAe,GAAGhU,OAAO,CAACiC,IAAI,CAAC;UAAA,OAAMsQ,eAAe;QAAA,EAAC,GAAGvS,OAAO;MAClF,CAAC,MAAM;QACLkC,MAAM,GAAGK,OAAO,CAACyR,eAAe,GAAGzB,eAAe,GAAGzS,SAAS;MAChE;MAEA,IAAIyC,OAAO,CAACO,oBAAoB,EAAE;QAChC,OAAO;UACLC,aAAa,EAAbA,aAAa;UACbb,MAAM,EAANA;QACF,CAAC;MACH;MAEA2G,IAAI,CAAC7F,qBAAqB,CAACD,aAAa,EAAER,OAAO,CAAC;MAClD,OAAOL,MAAM;IACf;IAAC,OArJD0Q,MAAM;EAAA;EAAA1O,MAAA,CAuJN8N,SAAS;IAAT,SAAAA,SAASA,CAACjT,IAAI,EAAEkC,IAAI,EAAEsB,OAAO,EAAE;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAMsG,IAAI,GAAG,IAAI;MACjB,IAAMzH,SAAS,GAAGnE,GAAG,CAACiE,wBAAwB,CAACG,GAAG,CAAC,CAAC;MACpD,IAAMkT,IAAI,GAAG1L,IAAI,CAAC0E,eAAe,CAACxO,IAAI,CAAC;MACvC,IAAMuC,mBAAmB,GAAGF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,YAAY;MACnD,IAAMC,eAAe,GAAGJ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,gBAAgB;MACnD,IAAMkS,UAAU,GAAG;QAAEzD,KAAK,EAAE;MAAI,CAAC;MAEjC,IAAMsE,aAAa,GAAG;QACpBlT,mBAAmB,EAAnBA,mBAAmB;QACnBqS,UAAU,EAAVA,UAAU;QACVnS,eAAe,EAAfA;MACF,CAAC;MACD,IAAI,CAAC+S,IAAI,EAAE;QACT,OAAAtV,aAAA,CAAAA,aAAA,KAAYuV,aAAa;UAAEnC,OAAO,EAAE;QAAK;MAC3C;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAMoC,mBAAmB,GAAG,SAAAA,CAAA,EAAM;QAChC,IAAId,UAAU,CAACzD,KAAK,KAAK,IAAI,EAAE;UAC7ByD,UAAU,CAACzD,KAAK,GAAGlM,SAAS,CAAC0Q,WAAW,CAACtT,SAAS,EAAErC,IAAI,CAAC;QAC3D;QACA,OAAO4U,UAAU,CAACzD,KAAK;MACzB,CAAC;MAED,IAAMyE,SAAS,GAAG,SAAAA,CAAAC,MAAM,EAAI;QAC1B/L,IAAI,CAAC8L,SAAS,CAACC,MAAM,CAAC;MACxB,CAAC;MAED,IAAM1C,UAAU,GAAG,IAAIlO,SAAS,CAAC6Q,gBAAgB,CAAC;QAChD9V,IAAI,EAAJA,IAAI;QACJwC,YAAY,EAAE,IAAI;QAClBqT,MAAM,EAAE/L,IAAI,CAAC+L,MAAM,CAAC,CAAC;QACrBpT,eAAe,EAAEe,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEf,eAAe;QACzCmT,SAAS,EAAEA,SAAS;QACpBhB,UAAU,WAAAA,CAAA,EAAG;UACX,OAAOc,mBAAmB,CAAC,CAAC;QAC9B;MACF,CAAC,CAAC;;MAEF;MACA;MACA,IAAMxC,cAAc,GAAG,SAAAA,CAAA,EAAM;QACzB,IAAI9U,MAAM,CAACyR,QAAQ,EAAE;UACnB;UACA;UACA,OAAOzR,MAAM,CAAC2X,gBAAgB,CAAC,YAAM;YACnC;YACA,OAAOP,IAAI,CAAC3T,KAAK,CAACsR,UAAU,EAAElG,KAAK,CAACiF,KAAK,CAAChQ,IAAI,CAAC,CAAC;UAClD,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,OAAOsT,IAAI,CAAC3T,KAAK,CAACsR,UAAU,EAAElG,KAAK,CAACiF,KAAK,CAAChQ,IAAI,CAAC,CAAC;QAClD;MACJ,CAAC;MACD,OAAAhC,aAAA,CAAAA,aAAA,KAAYuV,aAAa;QAAEnC,OAAO,EAAE,IAAI;QAAEJ,cAAc,EAAdA,cAAc;QAAEC,UAAU,EAAVA;MAAU;IACtE;IAAC,OA5EDF,SAAS;EAAA,IA8ET;EACA;EACA;EAAA;EAAA9N,MAAA,CACAoO,cAAc;IAAd,SAAAA,cAAcA,CAAA,EAAG;MACf,IAAI,CAAE,IAAI,CAACyC,qBAAqB,CAAC,CAAC,EAAE;QAClC,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC7B;MAEAxX,MAAM,CAACqK,MAAM,CAAC,IAAI,CAAC4B,OAAO,CAAC,CAAC3K,OAAO,CAAC,UAACqQ,KAAK,EAAK;QAC7CA,KAAK,CAAC8F,aAAa,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ;IAAC,OARD3C,cAAc;EAAA,IAUd;EACA;EACA;EAAA;EAAApO,MAAA,CACA0P,0BAA0B;IAA1B,SAAAA,0BAA0BA,CAAClJ,QAAQ,EAAE;MACnC,IAAM7B,IAAI,GAAG,IAAI;MACjB,IAAIA,IAAI,CAAC+B,uBAAuB,CAACF,QAAQ,CAAC,EACxC,MAAM,IAAI/C,KAAK,CAAC,kDAAkD,CAAC;MAErE,IAAMuN,WAAW,GAAG,EAAE;MAEtB1X,MAAM,CAACwK,OAAO,CAACa,IAAI,CAACY,OAAO,CAAC,CAAC3K,OAAO,CAAC,UAAAqW,KAAA,EAAyB;QAAA,IAAAC,KAAA,GAAAvR,cAAA,CAAAsR,KAAA;UAAvB/L,UAAU,GAAAgM,KAAA;UAAEjG,KAAK,GAAAiG,KAAA;QACtD,IAAMC,SAAS,GAAGlG,KAAK,CAACmG,iBAAiB,CAAC,CAAC;QAC3C;QACA,IAAI,CAAED,SAAS,EAAE;QACjBA,SAAS,CAACvW,OAAO,CAAC,UAACyW,GAAG,EAAEzP,EAAE,EAAK;UAC7BoP,WAAW,CAACzJ,IAAI,CAAC;YAAErC,UAAU,EAAVA,UAAU;YAAEtD,EAAE,EAAFA;UAAG,CAAC,CAAC;UACpC,IAAI,CAAE2C,MAAM,CAAC5F,IAAI,CAACgG,IAAI,CAACwC,gBAAgB,EAAEjC,UAAU,CAAC,EAAE;YACpDP,IAAI,CAACwC,gBAAgB,CAACjC,UAAU,CAAC,GAAG,IAAIiD,UAAU,CAAC,CAAC;UACtD;UACA,IAAMvD,SAAS,GAAGD,IAAI,CAACwC,gBAAgB,CAACjC,UAAU,CAAC,CAACoM,UAAU,CAC5D1P,EAAE,EACFtI,MAAM,CAACC,MAAM,CAAC,IAAI,CACpB,CAAC;UACD,IAAIqL,SAAS,CAACkC,cAAc,EAAE;YAC5B;YACA;YACAlC,SAAS,CAACkC,cAAc,CAACN,QAAQ,CAAC,GAAG,IAAI;UAC3C,CAAC,MAAM;YACL;YACA5B,SAAS,CAACO,QAAQ,GAAGkM,GAAG;YACxBzM,SAAS,CAACqC,cAAc,GAAG,EAAE;YAC7BrC,SAAS,CAACkC,cAAc,GAAGxN,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;YAC9CqL,SAAS,CAACkC,cAAc,CAACN,QAAQ,CAAC,GAAG,IAAI;UAC3C;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAAEhC,OAAO,CAACwM,WAAW,CAAC,EAAE;QAC1BrM,IAAI,CAAC+B,uBAAuB,CAACF,QAAQ,CAAC,GAAGwK,WAAW;MACtD;IACF;IAAC,OApCDtB,0BAA0B;EAAA,IAsC1B;EACA;EAAA;EAAA1P,MAAA,CACAuR,eAAe;IAAf,SAAAA,eAAeA,CAAA,EAAG;MAChBjY,MAAM,CAACqK,MAAM,CAAC,IAAI,CAACI,cAAc,CAAC,CAACnJ,OAAO,CAAC,UAACsJ,GAAG,EAAK;QAClD;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,GAAG,CAACrJ,IAAI,KAAK,kCAAkC,EAAE;UACnDqJ,GAAG,CAAC7C,IAAI,CAAC,CAAC;QACZ;MACF,CAAC,CAAC;IACJ;IAAC,OAZDkQ,eAAe;EAAA,IAcf;EAAA;EAAAvR,MAAA,CACAb,KAAK;IAAL,SAAAA,KAAKA,CAACqS,GAAG,EAAE;MACT,IAAI,CAAChT,OAAO,CAACiT,IAAI,CAAC3R,SAAS,CAAC4R,YAAY,CAACF,GAAG,CAAC,CAAC;IAChD;IAAC,OAFDrS,KAAK;EAAA,IAIL;EACA;EACA;EACA;EACA;EACA;EAAA;EAAAa,MAAA,CACAmE,WAAW;IAAX,SAAAA,WAAWA,CAACqN,GAAG,EAAE;MACf,IAAI,CAACrS,KAAK,CAACqS,GAAG,EAAE,IAAI,CAAC;IACvB;IAAC,OAFDrN,WAAW;EAAA,IAIX;EACA;EACA;EAAA;EAAAnE,MAAA,CACA2R,eAAe;IAAf,SAAAA,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAI,CAACpT,OAAO,CAACmT,eAAe,CAACC,KAAK,CAAC;IACrC;IAAC,OAFDD,eAAe;EAAA;EAIf;AACF;AACA;AACA;AACA;AACA;AACA;EANE;EAAA3R,MAAA,CAOA6R,MAAM;IAAN,SAAAA,MAAMA,CAAA,EAAU;MAAA,IAAAC,aAAA;MACd,OAAO,CAAAA,aAAA,OAAI,CAACtT,OAAO,EAACqT,MAAM,CAAAnV,KAAA,CAAAoV,aAAA,EAAApW,SAAQ,CAAC;IACrC;IAAC,OAFDmW,MAAM;EAAA;EAIN;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;EAAA7R,MAAA,CASAoC,SAAS;IAAT,SAAAA,SAASA,CAAA,EAAU;MAAA,IAAA2P,cAAA;MACjB,OAAO,CAAAA,cAAA,OAAI,CAACvT,OAAO,EAAC4D,SAAS,CAAA1F,KAAA,CAAAqV,cAAA,EAAArW,SAAQ,CAAC;IACxC;IAAC,OAFD0G,SAAS;EAAA;EAIT;AACF;AACA;AACA;AACA;AACA;AACA;EANE;EAAApC,MAAA,CAOAsC,UAAU;IAAV,SAAAA,UAAUA,CAAA,EAAU;MAAA,IAAA0P,cAAA;MAClB,OAAO,CAAAA,cAAA,OAAI,CAACxT,OAAO,EAAC8D,UAAU,CAAA5F,KAAA,CAAAsV,cAAA,EAAAtW,SAAQ,CAAC;IACzC;IAAC,OAFD4G,UAAU;EAAA;EAAAtC,MAAA,CAIViS,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACzT,OAAO,CAAC8D,UAAU,CAAC;QAAEC,UAAU,EAAE;MAAK,CAAC,CAAC;IACtD;IAAC,OAFD0P,KAAK;EAAA,IAIL;EACA;EACA;EAAA;EAAAjS,MAAA,CACA0Q,MAAM;IAAN,SAAAA,MAAMA,CAAA,EAAG;MACP,IAAI,IAAI,CAACrG,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC6C,MAAM,CAAC,CAAC;MAC/C,OAAO,IAAI,CAAC9C,OAAO;IACrB;IAAC,OAHDsG,MAAM;EAAA;EAAA1Q,MAAA,CAKNyQ,SAAS;IAAT,SAAAA,SAASA,CAACC,MAAM,EAAE;MAChB;MACA,IAAI,IAAI,CAACtG,OAAO,KAAKsG,MAAM,EAAE;MAC7B,IAAI,CAACtG,OAAO,GAAGsG,MAAM;MACrB,IAAI,IAAI,CAACrG,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC/D,OAAO,CAAC,CAAC;IAClD;IAAC,OALDmK,SAAS;EAAA,IAOT;EACA;EACA;EAAA;EAAAzQ,MAAA,CACA6Q,qBAAqB;IAArB,SAAAA,qBAAqBA,CAAA,EAAG;MACtB,OACE,CAAErM,OAAO,CAAC,IAAI,CAACoF,iBAAiB,CAAC,IACjC,CAAEpF,OAAO,CAAC,IAAI,CAACmF,0BAA0B,CAAC;IAE9C;IAAC,OALDkH,qBAAqB;EAAA,IAOrB;EACA;EAAA;EAAA7Q,MAAA,CACAkS,yBAAyB;IAAzB,SAAAA,yBAAyBA,CAAA,EAAG;MAC1B,IAAMC,QAAQ,GAAG,IAAI,CAACvO,eAAe;MACrC,OAAOtK,MAAM,CAACqK,MAAM,CAACwO,QAAQ,CAAC,CAAC5F,IAAI,CAAC,UAAC1I,OAAO;QAAA,OAAK,CAAC,CAACA,OAAO,CAACP,WAAW;MAAA,EAAC;IACzE;IAAC,OAHD4O,yBAAyB;EAAA;EAAAlS,MAAA,CAKnBoS,sBAAsB;IAA5B,SAAMA,sBAAsBA,CAACjS,GAAG,EAAEuE,OAAO;MAAA,IAAA2N,WAAA;MAAA,OAAAzS,mBAAA,CAAAQ,KAAA;QAAA,SAAAkS,wBAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhS,IAAA,GAAAgS,SAAA,CAAA/R,IAAA;YAAA;cACjC6R,WAAW,GAAGlS,GAAG,CAACA,GAAG,EAE3B;cAAA,MACIkS,WAAW,KAAK,OAAO;gBAAAE,SAAA,CAAA/R,IAAA;gBAAA;cAAA;cAAA+R,SAAA,CAAA/R,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACnB,IAAI,CAACsD,cAAc,CAACtE,GAAG,EAAEuE,OAAO,CAAC;YAAA;cAAA6N,SAAA,CAAA/R,IAAA;cAAA;YAAA;cAClC,IAAI6R,WAAW,KAAK,SAAS,EAAE;gBACpC,IAAI,CAAC1M,gBAAgB,CAACxF,GAAG,EAAEuE,OAAO,CAAC;cACrC,CAAC,MAAM,IAAI2N,WAAW,KAAK,SAAS,EAAE;gBACpC,IAAI,CAACxM,gBAAgB,CAAC1F,GAAG,EAAEuE,OAAO,CAAC;cACrC,CAAC,MAAM,IAAI2N,WAAW,KAAK,OAAO,EAAE;gBAClC,IAAI,CAACvM,cAAc,CAAC3F,GAAG,EAAEuE,OAAO,CAAC;cACnC,CAAC,MAAM,IAAI2N,WAAW,KAAK,SAAS,EAAE;gBACpC,IAAI,CAAC9L,gBAAgB,CAACpG,GAAG,EAAEuE,OAAO,CAAC;cACrC,CAAC,MAAM,IAAI2N,WAAW,KAAK,OAAO,EAAE;gBAClC;cAAA,CACD,MAAM;gBACLpZ,MAAM,CAACa,MAAM,CAAC,+CAA+C,EAAEqG,GAAG,CAAC;cACrE;YAAC;YAAA;cAAA,OAAAoS,SAAA,CAAAlR,IAAA;UAAA;QAAA;QAAA,OAAAiR,uBAAA;MAAA,uBAAAjX,OAAA;IAAA;IACF,OAnBK+W,sBAAsB;EAAA;EAAApS,MAAA,CAqB5BwS,sBAAsB;IAAtB,SAAAA,sBAAsBA,CAAA,EAAG;MACvB,IAAM7N,IAAI,GAAG,IAAI;MACjB,IAAIA,IAAI,CAACsF,0BAA0B,EAAE;QACnCwI,YAAY,CAAC9N,IAAI,CAACsF,0BAA0B,CAAC;QAC7CtF,IAAI,CAACsF,0BAA0B,GAAG,IAAI;MACxC;MAEAtF,IAAI,CAACqF,sBAAsB,GAAG,IAAI;MAClC;MACA;MACA;MACA,IAAM0I,MAAM,GAAG/N,IAAI,CAACoF,eAAe;MACnCpF,IAAI,CAACoF,eAAe,GAAGzQ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC1C,OAAOmZ,MAAM;IACf;IAAC,OAdDF,sBAAsB;EAAA;EAgBtB;AACF;AACA;AACA;EAHE;EAAAxS,MAAA,CAIM2S,oBAAoB;IAA1B,SAAMA,oBAAoBA,CAACjO,OAAO;MAAA,IAAAC,IAAA,EAAAiO,EAAA,EAAAC,cAAA,EAAAC,oBAAA,EAAA7H,KAAA,EAAA8H,GAAA,EAAAC,eAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,SAAA,EAAAC,QAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAC,CAAA,EAAAC,KAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAvT,GAAA,EAAAwT,qBAAA,EAAAC,GAAA,EAAAC,eAAA,EAAAC,OAAA;MAAA,OAAAlU,mBAAA,CAAAQ,KAAA;QAAA,SAAA2T,sBAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzT,IAAA,GAAAyT,SAAA,CAAAxT,IAAA;YAAA;cAC1BmE,IAAI,GAAG,IAAI;cAAA,MAEbA,IAAI,CAACW,YAAY,IAAI,CAACd,OAAO,CAACE,OAAO,CAAC;gBAAAsP,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAAAoS,EAAA,MAAAC,cAAA,GAEpBvZ,MAAM,CAACqK,MAAM,CAACgB,IAAI,CAACY,OAAO,CAAC;YAAA;cAAA,MAAAqN,EAAA,GAAAC,cAAA,CAAAlX,MAAA;gBAAAqY,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAApCyK,KAAK,GAAA4H,cAAA,CAAAD,EAAA;cAAAoB,SAAA,CAAAxT,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACR8J,KAAK,CAACO,WAAW,CACrB,EAAAsH,oBAAA,GAAApO,OAAO,CAACuG,KAAK,CAACgJ,KAAK,CAAC,cAAAnB,oBAAA,uBAApBA,oBAAA,CAAsBnX,MAAM,KAAI,CAAC,EACjCgJ,IAAI,CAACW,YACP,CAAC;YAAA;cAAAsN,EAAA;cAAAoB,SAAA,CAAAxT,IAAA;cAAA;YAAA;cAGHmE,IAAI,CAACW,YAAY,GAAG,KAAK;;cAEzB;cAAAyN,GAAA,MAAAC,eAAA,GACoC1Z,MAAM,CAACwK,OAAO,CAACY,OAAO,CAAC;YAAA;cAAA,MAAAqO,GAAA,GAAAC,eAAA,CAAArX,MAAA;gBAAAqY,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAAAyS,KAAA,GAAAD,eAAA,CAAAD,GAAA;cAAAG,KAAA,GAAAvT,cAAA,CAAAsT,KAAA;cAA/CE,SAAS,GAAAD,KAAA;cAAEE,QAAQ,GAAAF,KAAA;cACvBjI,MAAK,GAAGtG,IAAI,CAACY,OAAO,CAAC4N,SAAS,CAAC;cAAA,KACjClI,MAAK;gBAAA+I,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cACP;cACA;cACM8S,UAAU,GAAG,GAAG;cACbC,CAAC,GAAG,CAAC;YAAA;cAAA,MAAEA,CAAC,GAAGH,QAAQ,CAACzX,MAAM;gBAAAqY,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAC3BgT,KAAK,GAAGJ,QAAQ,CAACnL,KAAK,CAACsL,CAAC,EAAEW,IAAI,CAACC,GAAG,CAACZ,CAAC,GAAGD,UAAU,EAAEF,QAAQ,CAACzX,MAAM,CAAC,CAAC;cAAA8X,UAAA,GAAA9L,+BAAA,CAExD6L,KAAK;YAAA;cAAA,KAAAE,MAAA,GAAAD,UAAA,IAAA1H,IAAA;gBAAAiI,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAAZL,GAAG,GAAAuT,MAAA,CAAA1H,KAAA;cAAAgI,SAAA,CAAAxT,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACN8J,MAAK,CAACQ,MAAM,CAACtL,GAAG,CAAC;YAAA;cAAA6T,SAAA,CAAAxT,IAAA;cAAA;YAAA;cAAAwT,SAAA,CAAAxT,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAGnB,IAAI9F,OAAO,CAAC,UAAAC,OAAO;gBAAA,OAAI8Y,OAAO,CAACC,QAAQ,CAAC/Y,OAAO,CAAC;cAAA,EAAC;YAAA;cAPpBiY,CAAC,IAAID,UAAU;cAAAU,SAAA,CAAAxT,IAAA;cAAA;YAAA;cAAAwT,SAAA,CAAAxT,IAAA;cAAA;YAAA;cAUpD;cACAmE,IAAI,CAACkF,wBAAwB,CAACsJ,SAAS,CAAC,GACtCxO,IAAI,CAACkF,wBAAwB,CAACsJ,SAAS,CAAC,IAAI,EAAE;cAChD,CAAAQ,qBAAA,GAAAhP,IAAI,CAACkF,wBAAwB,CAACsJ,SAAS,CAAC,EAAC5L,IAAI,CAAA7K,KAAA,CAAAiX,qBAAA,EAAAlM,kBAAA,CAAI2L,QAAQ,EAAC;YAAC;cAAAL,GAAA;cAAAiB,SAAA,CAAAxT,IAAA;cAAA;YAAA;cAAAoT,GAAA,MAAAC,eAAA,GAK3Cva,MAAM,CAACqK,MAAM,CAACgB,IAAI,CAACY,OAAO,CAAC;YAAA;cAAA,MAAAqO,GAAA,GAAAC,eAAA,CAAAlY,MAAA;gBAAAqY,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAApCyK,OAAK,GAAA4I,eAAA,CAAAD,GAAA;cAAAI,SAAA,CAAAxT,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACR8J,OAAK,CAACS,SAAS,CAAC,CAAC;YAAA;cAAAkI,GAAA;cAAAI,SAAA,CAAAxT,IAAA;cAAA;YAAA;cAI3BmE,IAAI,CAAC2P,wBAAwB,CAAC,CAAC;YAAC;YAAA;cAAA,OAAAN,SAAA,CAAA3S,IAAA;UAAA;QAAA;QAAA,OAAA0S,qBAAA;MAAA,uBAAA1Y,OAAA;IAAA;IACjC,OA7CKsX,oBAAoB;EAAA;EA+C1B;AACF;AACA;AACA;EAHE;EAAA3S,MAAA,CAIAuU,oBAAoB;IAApB,SAAAA,oBAAoBA,CAAC7P,OAAO,EAAE;MAC5B,IAAMC,IAAI,GAAG,IAAI;MAEjB,IAAIA,IAAI,CAACW,YAAY,IAAI,CAACd,OAAO,CAACE,OAAO,CAAC,EAAE;QAC1C;QACApL,MAAM,CAACqK,MAAM,CAACgB,IAAI,CAACY,OAAO,CAAC,CAAC3K,OAAO,CAAC,UAAAqQ,KAAK,EAAI;UAAA,IAAAuJ,qBAAA;UAC3CvJ,KAAK,CAACO,WAAW,CACf,EAAAgJ,qBAAA,GAAA9P,OAAO,CAACuG,KAAK,CAACgJ,KAAK,CAAC,cAAAO,qBAAA,uBAApBA,qBAAA,CAAsB7Y,MAAM,KAAI,CAAC,EACjCgJ,IAAI,CAACW,YACP,CAAC;QACH,CAAC,CAAC;QAEFX,IAAI,CAACW,YAAY,GAAG,KAAK;QAEzBhM,MAAM,CAACwK,OAAO,CAACY,OAAO,CAAC,CAAC9J,OAAO,CAAC,UAAA6Z,KAAA,EAA2B;UAAA,IAAAC,MAAA,GAAA/U,cAAA,CAAA8U,KAAA;YAAzBtB,SAAS,GAAAuB,MAAA;YAAEtB,QAAQ,GAAAsB,MAAA;UACnD,IAAMzJ,KAAK,GAAGtG,IAAI,CAACY,OAAO,CAAC4N,SAAS,CAAC;UACrC,IAAIlI,KAAK,EAAE;YACTmI,QAAQ,CAACxY,OAAO,CAAC,UAAAuF,GAAG;cAAA,OAAI8K,KAAK,CAACQ,MAAM,CAACtL,GAAG,CAAC;YAAA,EAAC;UAC5C,CAAC,MAAM;YAAA,IAAAwU,sBAAA;YACLhQ,IAAI,CAACkF,wBAAwB,CAACsJ,SAAS,CAAC,GACtCxO,IAAI,CAACkF,wBAAwB,CAACsJ,SAAS,CAAC,IAAI,EAAE;YAChD,CAAAwB,sBAAA,GAAAhQ,IAAI,CAACkF,wBAAwB,CAACsJ,SAAS,CAAC,EAAC5L,IAAI,CAAA7K,KAAA,CAAAiY,sBAAA,EAAAlN,kBAAA,CAAI2L,QAAQ,EAAC;UAC5D;QACF,CAAC,CAAC;QAEF9Z,MAAM,CAACqK,MAAM,CAACgB,IAAI,CAACY,OAAO,CAAC,CAAC3K,OAAO,CAAC,UAAAqQ,KAAK;UAAA,OAAIA,KAAK,CAACS,SAAS,CAAC,CAAC;QAAA,EAAC;MACjE;MAEA/G,IAAI,CAAC2P,wBAAwB,CAAC,CAAC;IACjC;IAAC,OA7BDC,oBAAoB;EAAA;EA+BpB;AACF;AACA;AACA;EAHE;EAAAvU,MAAA,CAIM8Q,oBAAoB;IAA1B,SAAMA,oBAAoBA,CAAA;MAAA,IAAAnM,IAAA,EAAA+N,MAAA;MAAA,OAAA9S,mBAAA,CAAAQ,KAAA;QAAA,SAAAwU,sBAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtU,IAAA,GAAAsU,SAAA,CAAArU,IAAA;YAAA;cAClBmE,IAAI,GAAG,IAAI;cACX+N,MAAM,GAAG/N,IAAI,CAAC6N,sBAAsB,CAAC,CAAC;cAAA,OAAAqC,SAAA,CAAAlU,MAAA,WAErC1H,MAAM,CAACsR,QAAQ,GAClB5F,IAAI,CAAC4P,oBAAoB,CAAC7B,MAAM,CAAC,GACjC/N,IAAI,CAACgO,oBAAoB,CAACD,MAAM,CAAC;YAAA;YAAA;cAAA,OAAAmC,SAAA,CAAAxT,IAAA;UAAA;QAAA;QAAA,OAAAuT,qBAAA;MAAA,uBAAAvZ,OAAA;IAAA;IACtC,OAPKyV,oBAAoB;EAAA,IAS1B;EACA;EACA;EAAA;EAAA9Q,MAAA,CACAsU,wBAAwB;IAAxB,SAAAA,wBAAwBA,CAAA,EAAG;MACzB,IAAM3P,IAAI,GAAG,IAAI;MACjB,IAAMsH,SAAS,GAAGtH,IAAI,CAAC8E,qBAAqB;MAC5C9E,IAAI,CAAC8E,qBAAqB,GAAG,EAAE;MAC/BwC,SAAS,CAACrR,OAAO,CAAC,UAACsM,CAAC,EAAK;QACvBA,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;IAAC,OAPDoN,wBAAwB;EAAA,IASxB;EACA;EACA;EAAA;EAAAtU,MAAA,CACAiG,+BAA+B;IAA/B,SAAAA,+BAA+BA,CAACuG,CAAC,EAAE;MACjC,IAAM7H,IAAI,GAAG,IAAI;MACjB,IAAMmQ,gBAAgB,GAAG,SAAAA,CAAA,EAAM;QAC7BnQ,IAAI,CAAC8E,qBAAqB,CAAClC,IAAI,CAACiF,CAAC,CAAC;MACpC,CAAC;MACD,IAAIuI,uBAAuB,GAAG,CAAC;MAC/B,IAAMC,gBAAgB,GAAG,SAAAA,CAAA,EAAM;QAC7B,EAAED,uBAAuB;QACzB,IAAIA,uBAAuB,KAAK,CAAC,EAAE;UACjC;UACA;UACAD,gBAAgB,CAAC,CAAC;QACpB;MACF,CAAC;MAEDxb,MAAM,CAACqK,MAAM,CAACgB,IAAI,CAACwC,gBAAgB,CAAC,CAACvM,OAAO,CAAC,UAACqa,eAAe,EAAK;QAChEA,eAAe,CAACra,OAAO,CAAC,UAACgK,SAAS,EAAK;UACrC,IAAMsQ,sCAAsC,GAC1CnU,IAAI,CAAC6D,SAAS,CAACkC,cAAc,CAAC,CAACyF,IAAI,CAAC,UAAA/F,QAAQ,EAAI;YAC9C,IAAM3C,OAAO,GAAGc,IAAI,CAACf,eAAe,CAAC4C,QAAQ,CAAC;YAC9C,OAAO3C,OAAO,IAAIA,OAAO,CAACP,WAAW;UACvC,CAAC,CAAC;UAEJ,IAAI4R,sCAAsC,EAAE;YAC1C,EAAEH,uBAAuB;YACzBnQ,SAAS,CAACqC,cAAc,CAACM,IAAI,CAACyN,gBAAgB,CAAC;UACjD;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAID,uBAAuB,KAAK,CAAC,EAAE;QACjC;QACA;QACAD,gBAAgB,CAAC,CAAC;MACpB;IACF;IAAC,OAlCD7O,+BAA+B;EAAA;EAAAjG,MAAA,CAoC/BlB,qBAAqB;IAArB,SAAAA,qBAAqBA,CAACD,aAAa,EAAER,OAAO,EAAE;MAC5C,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE+R,IAAI,EAAE;QACjB;QACA,IAAI,CAAClN,wBAAwB,CAACqE,IAAI,CAAC;UACjC6I,IAAI,EAAE,IAAI;UACVhN,OAAO,EAAE,CAACvE,aAAa;QACzB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;QACA,IAAI2F,OAAO,CAAC,IAAI,CAACtB,wBAAwB,CAAC,IACtCgF,IAAI,CAAC,IAAI,CAAChF,wBAAwB,CAAC,CAACkN,IAAI,EAAE;UAC5C,IAAI,CAAClN,wBAAwB,CAACqE,IAAI,CAAC;YACjC6I,IAAI,EAAE,KAAK;YACXhN,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QAEA8E,IAAI,CAAC,IAAI,CAAChF,wBAAwB,CAAC,CAACE,OAAO,CAACmE,IAAI,CAAC1I,aAAa,CAAC;MACjE;;MAEA;MACA,IAAI,IAAI,CAACqE,wBAAwB,CAACvH,MAAM,KAAK,CAAC,EAAE;QAC9CkD,aAAa,CAACsW,WAAW,CAAC,CAAC;MAC7B;IACF;IAAC,OAzBDrW,qBAAqB;EAAA,IA2BrB;EACA;EACA;EAAA;EAAAkB,MAAA,CACAoV,0BAA0B;IAA1B,SAAAA,0BAA0BA,CAAA,EAAG;MAC3B,IAAMzQ,IAAI,GAAG,IAAI;MACjB,IAAIA,IAAI,CAACuN,yBAAyB,CAAC,CAAC,EAAE;;MAEtC;MACA;MACA;MACA,IAAI,CAAE1N,OAAO,CAACG,IAAI,CAACzB,wBAAwB,CAAC,EAAE;QAC5C,IAAMmS,UAAU,GAAG1Q,IAAI,CAACzB,wBAAwB,CAACQ,KAAK,CAAC,CAAC;QACxD,IAAI,CAAEc,OAAO,CAAC6Q,UAAU,CAACjS,OAAO,CAAC,EAC/B,MAAM,IAAIK,KAAK,CACb,6CAA6C,GAC3CmD,IAAI,CAACC,SAAS,CAACwO,UAAU,CAC7B,CAAC;;QAEH;QACA,IAAI,CAAE7Q,OAAO,CAACG,IAAI,CAACzB,wBAAwB,CAAC,EAC1CyB,IAAI,CAAC2Q,uBAAuB,CAAC,CAAC;MAClC;;MAEA;MACA3Q,IAAI,CAACtI,aAAa,CAAC,CAAC;IACtB;IAAC,OAtBD+Y,0BAA0B;EAAA,IAwB1B;EACA;EAAA;EAAApV,MAAA,CACAsV,uBAAuB;IAAvB,SAAAA,uBAAuBA,CAAA,EAAG;MACxB,IAAM3Q,IAAI,GAAG,IAAI;MAEjB,IAAIH,OAAO,CAACG,IAAI,CAACzB,wBAAwB,CAAC,EAAE;QAC1C;MACF;MAEAyB,IAAI,CAACzB,wBAAwB,CAAC,CAAC,CAAC,CAACE,OAAO,CAACxI,OAAO,CAAC,UAAA2a,CAAC,EAAI;QACpDA,CAAC,CAACJ,WAAW,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ;IAAC,OAVDG,uBAAuB;EAAA;EAAAtV,MAAA,CAYvBR,oCAAoC;IAApC,SAAAA,oCAAoCA,CAACgW,0BAA0B,EAAE;MAAA,IAAAC,qBAAA;MAC/D,IAAM9Q,IAAI,GAAG,IAAI;MACjB,IAAIH,OAAO,CAACgR,0BAA0B,CAAC,EAAE;;MAEzC;MACA;MACA;MACA,IAAIhR,OAAO,CAACG,IAAI,CAACzB,wBAAwB,CAAC,EAAE;QAC1CyB,IAAI,CAACzB,wBAAwB,GAAGsS,0BAA0B;QAC1D7Q,IAAI,CAAC2Q,uBAAuB,CAAC,CAAC;QAC9B;MACF;;MAEA;MACA;MACA;MACA,IACE,CAACpN,IAAI,CAACvD,IAAI,CAACzB,wBAAwB,CAAC,CAACkN,IAAI,IACzC,CAACoF,0BAA0B,CAAC,CAAC,CAAC,CAACpF,IAAI,EACnC;QACAoF,0BAA0B,CAAC,CAAC,CAAC,CAACpS,OAAO,CAACxI,OAAO,CAAC,UAAC2a,CAAC,EAAK;UACnDrN,IAAI,CAACvD,IAAI,CAACzB,wBAAwB,CAAC,CAACE,OAAO,CAACmE,IAAI,CAACgO,CAAC,CAAC;;UAEnD;UACA,IAAI5Q,IAAI,CAACzB,wBAAwB,CAACvH,MAAM,KAAK,CAAC,EAAE;YAC9C4Z,CAAC,CAACJ,WAAW,CAAC,CAAC;UACjB;QACF,CAAC,CAAC;QAEFK,0BAA0B,CAAC9R,KAAK,CAAC,CAAC;MACpC;;MAEA;MACA,CAAA+R,qBAAA,GAAA9Q,IAAI,CAACzB,wBAAwB,EAACqE,IAAI,CAAA7K,KAAA,CAAA+Y,qBAAA,EAAAhO,kBAAA,CAAI+N,0BAA0B,EAAC;IACnE;IAAC,OAlCDhW,oCAAoC;EAAA;EAAAQ,MAAA,CAoCpC4C,oDAAoD;IAApD,SAAAA,oDAAoDA,CAAA,EAAG;MACrD,IAAM+B,IAAI,GAAG,IAAI;MACjB,IAAM6Q,0BAA0B,GAAG7Q,IAAI,CAACzB,wBAAwB;MAChEyB,IAAI,CAACzB,wBAAwB,GAAG,EAAE;MAElCyB,IAAI,CAACmE,WAAW,IAAInE,IAAI,CAACmE,WAAW,CAAC,CAAC;MACtC/P,GAAG,CAAC2c,cAAc,CAACC,IAAI,CAAC,UAACrX,QAAQ,EAAK;QACpCA,QAAQ,CAACqG,IAAI,CAAC;QACd,OAAO,IAAI;MACb,CAAC,CAAC;MAEFA,IAAI,CAACnF,oCAAoC,CAACgW,0BAA0B,CAAC;IACvE;IAAC,OAZD5S,oDAAoD;EAAA,IAcpD;EAAA;EAAA5C,MAAA,CACAvD,eAAe;IAAf,SAAAA,eAAeA,CAAA,EAAG;MAChB,OAAO+H,OAAO,CAAC,IAAI,CAACZ,eAAe,CAAC;IACtC;IAAC,OAFDnH,eAAe;EAAA,IAIf;EACA;EAAA;EAAAuD,MAAA,CACA3D,aAAa;IAAb,SAAAA,aAAaA,CAAA,EAAG;MACd,IAAMsI,IAAI,GAAG,IAAI;MACjB,IAAIA,IAAI,CAACmF,aAAa,IAAInF,IAAI,CAAClI,eAAe,CAAC,CAAC,EAAE;QAChDkI,IAAI,CAACmF,aAAa,CAAC,CAAC;QACpBnF,IAAI,CAACmF,aAAa,GAAG,IAAI;MAC3B;IACF;IAAC,OANDzN,aAAa;EAAA;EAAA,OAAAnB,UAAA;AAAA,I;;;;;;;;;;;AC15Cf,IAAI0E,mBAAmB;AAAC/G,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC4G,mBAAmB,GAAC5G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI2G,cAAc;AAAC9G,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC2G,cAAc,GAAC3G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAjOH,MAAM,CAACoC,MAAM,CAAC;EAACmN,iBAAiB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,iBAAiB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAItI,SAAS;AAACjH,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACgH,SAAS,EAAC,SAAAA,CAAS9G,CAAC,EAAC;IAAC8G,SAAS,GAAC9G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAM,EAAC,SAAAA,CAASD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAID,GAAG;AAACF,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACC,GAAG,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACD,GAAG,GAACC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAI8O,KAAK;AAACjP,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACgP,KAAK,EAAC,SAAAA,CAAS9O,CAAC,EAAC;IAAC8O,KAAK,GAAC9O,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIwL,OAAO,EAACD,MAAM;AAAC1L,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;EAAC0L,OAAO,EAAC,SAAAA,CAASxL,CAAC,EAAC;IAACwL,OAAO,GAACxL,CAAC;EAAA,CAAC;EAACuL,MAAM,EAAC,SAAAA,CAASvL,CAAC,EAAC;IAACuL,MAAM,GAACvL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAM1doP,iBAAiB;EAC5B,SAAAA,kBAAY1N,UAAU,EAAE;IACtB,IAAI,CAACqF,WAAW,GAAGrF,UAAU;EAC/B;;EAEA;AACF;AACA;AACA;EAHE,IAAAsF,MAAA,GAAAoI,iBAAA,CAAA5L,SAAA;EAAAwD,MAAA,CAIMwB,mBAAmB;IAAzB,SAAMA,mBAAmBA,CAACrB,GAAG;MAAA,IAAAwE,IAAA,EAAAiR,4BAAA,EAAAzD,QAAA,EAAAS,EAAA,EAAAC,cAAA,EAAA5H,KAAA;MAAA,OAAArL,mBAAA,CAAAQ,KAAA;QAAA,SAAAyV,qBAAAvV,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACrBmE,IAAI,GAAG,IAAI,CAAC5E,WAAW;cAE7B,IAAI4E,IAAI,CAAC1D,QAAQ,KAAK,MAAM,IAAI0D,IAAI,CAAC4E,kBAAkB,KAAK,CAAC,EAAE;gBAC7D5E,IAAI,CAAC/D,UAAU,GAAG,IAAId,SAAS,CAACgW,SAAS,CAAC;kBACxCxN,iBAAiB,EAAE3D,IAAI,CAAC4E,kBAAkB;kBAC1ChB,gBAAgB,EAAE5D,IAAI,CAAC6E,iBAAiB;kBACxCuM,SAAS,WAAAA,CAAA,EAAG;oBACVpR,IAAI,CAACgN,eAAe,CAClB,IAAI5Y,GAAG,CAACiQ,eAAe,CAAC,yBAAyB,CACnD,CAAC;kBACH,CAAC;kBACDgN,QAAQ,WAAAA,CAAA,EAAG;oBACTrR,IAAI,CAACxF,KAAK,CAAC;sBAAEgB,GAAG,EAAE;oBAAO,CAAC,CAAC;kBAC7B;gBACF,CAAC,CAAC;gBACFwE,IAAI,CAAC/D,UAAU,CAACqV,KAAK,CAAC,CAAC;cACzB;;cAEA;cACA,IAAItR,IAAI,CAAC7B,cAAc,EAAE6B,IAAI,CAACW,YAAY,GAAG,IAAI;cAGjD,IAAI,OAAOnF,GAAG,CAAC4C,OAAO,KAAK,QAAQ,EAAE;gBACnC6S,4BAA4B,GAAGjR,IAAI,CAAC7B,cAAc,KAAK3C,GAAG,CAAC4C,OAAO;gBAClE4B,IAAI,CAAC7B,cAAc,GAAG3C,GAAG,CAAC4C,OAAO;cACnC;cAAC,KAEG6S,4BAA4B;gBAAAtV,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAK,MAAA;YAAA;cAKhC;;cAEA;cACA;cACAgE,IAAI,CAACkF,wBAAwB,GAAGvQ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;cAEnD,IAAIoL,IAAI,CAACW,YAAY,EAAE;gBACrB;gBACA;gBACAX,IAAI,CAAC+B,uBAAuB,GAAGpN,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;gBAClDoL,IAAI,CAACwC,gBAAgB,GAAG7N,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;cAC7C;;cAEA;cACAoL,IAAI,CAAC8E,qBAAqB,GAAG,EAAE;;cAE/B;cACA9E,IAAI,CAACiF,iBAAiB,GAAGtQ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;cAC5CD,MAAM,CAACwK,OAAO,CAACa,IAAI,CAACZ,cAAc,CAAC,CAACnJ,OAAO,CAAC,UAAAoJ,IAAA,EAAe;gBAAA,IAAAC,KAAA,GAAAtE,cAAA,CAAAqE,IAAA;kBAAbpC,EAAE,GAAAqC,KAAA;kBAAEC,GAAG,GAAAD,KAAA;gBACnD,IAAIC,GAAG,CAACiC,KAAK,EAAE;kBACbxB,IAAI,CAACiF,iBAAiB,CAAChI,EAAE,CAAC,GAAG,IAAI;gBACnC;cACF,CAAC,CAAC;;cAEF;cACA;cACA;cACA;cACA;cACA;cACA;cACA+C,IAAI,CAACgF,0BAA0B,GAAGrQ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;cACrD,IAAIoL,IAAI,CAACW,YAAY,EAAE;gBACf6M,QAAQ,GAAGxN,IAAI,CAACf,eAAe;gBACrCtK,MAAM,CAACyH,IAAI,CAACoR,QAAQ,CAAC,CAACvX,OAAO,CAAC,UAAAgH,EAAE,EAAI;kBAClC,IAAMiC,OAAO,GAAGsO,QAAQ,CAACvQ,EAAE,CAAC;kBAC5B,IAAIiC,OAAO,CAACqS,SAAS,CAAC,CAAC,EAAE;oBACvB;oBACA;oBACA;oBACA;oBACAvR,IAAI,CAAC8E,qBAAqB,CAAClC,IAAI,CAC7B;sBAAA,OAAa1D,OAAO,CAACyD,WAAW,CAAA5K,KAAA,CAAnBmH,OAAO,EAAAnI,SAAoB,CAAC;oBAAA,CAC3C,CAAC;kBACH,CAAC,MAAM,IAAImI,OAAO,CAACP,WAAW,EAAE;oBAC9B;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACAqB,IAAI,CAACgF,0BAA0B,CAAC9F,OAAO,CAAC2C,QAAQ,CAAC,GAAG,IAAI;kBAC1D;gBACF,CAAC,CAAC;cACJ;cAEA7B,IAAI,CAAC+E,gCAAgC,GAAG,EAAE;;cAE1C;cACA;cAAA,IACK/E,IAAI,CAACkM,qBAAqB,CAAC,CAAC;gBAAAvQ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,KAC3BmE,IAAI,CAACW,YAAY;gBAAAhF,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAoS,EAAA,MAAAC,cAAA,GACCvZ,MAAM,CAACqK,MAAM,CAACgB,IAAI,CAACY,OAAO,CAAC;YAAA;cAAA,MAAAqN,EAAA,GAAAC,cAAA,CAAAlX,MAAA;gBAAA2E,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAApCyK,KAAK,GAAA4H,cAAA,CAAAD,EAAA;cAAAtS,QAAA,CAAAE,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACR8J,KAAK,CAACO,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;YAAA;cAAAlL,QAAA,CAAAE,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAC1B8J,KAAK,CAACS,SAAS,CAAC,CAAC;YAAA;cAAAkH,EAAA;cAAAtS,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEzBmE,IAAI,CAACW,YAAY,GAAG,KAAK;YAAC;cAE5BX,IAAI,CAAC2P,wBAAwB,CAAC,CAAC;YAAC;YAAA;cAAA,OAAAhU,QAAA,CAAAe,IAAA;UAAA;QAAA;QAAA,OAAAwU,oBAAA;MAAA,uBAAAxa,OAAA;IAAA;IAEnC,OA1GKmG,mBAAmB;EAAA;EA4GzB;AACF;AACA;AACA;EAHE;EAAAxB,MAAA,CAIM6B,cAAc;IAApB,SAAMA,cAAcA,CAAC1B,GAAG;MAAA,IAAAwE,IAAA,EAAAwR,gBAAA,EAAApD,GAAA,EAAAc,eAAA,EAAAuC,eAAA,EAAAC,aAAA;MAAA,OAAAzW,mBAAA,CAAAQ,KAAA;QAAA,SAAAkW,gBAAA/U,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cAChBmE,IAAI,GAAG,IAAI,CAAC5E,WAAW;cAAA,KAEzB4E,IAAI,CAACkM,qBAAqB,CAAC,CAAC;gBAAAtP,SAAA,CAAAf,IAAA;gBAAA;cAAA;cAC9BmE,IAAI,CAAC+E,gCAAgC,CAACnC,IAAI,CAACpH,GAAG,CAAC;cAE/C,IAAIA,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;gBACvB,OAAOwE,IAAI,CAACiF,iBAAiB,CAACzJ,GAAG,CAACyB,EAAE,CAAC;cACvC;cAEA,IAAIzB,GAAG,CAAC4F,IAAI,EAAE;gBACZ5F,GAAG,CAAC4F,IAAI,CAACnL,OAAO,CAAC,UAAAoL,KAAK,EAAI;kBACxB,OAAOrB,IAAI,CAACiF,iBAAiB,CAAC5D,KAAK,CAAC;gBACtC,CAAC,CAAC;cACJ;cAEA,IAAI7F,GAAG,CAACiD,OAAO,EAAE;gBACfjD,GAAG,CAACiD,OAAO,CAACxI,OAAO,CAAC,UAAA4L,QAAQ,EAAI;kBAC9B,OAAO7B,IAAI,CAACgF,0BAA0B,CAACnD,QAAQ,CAAC;gBAClD,CAAC,CAAC;cACJ;cAAC,KAEG7B,IAAI,CAACkM,qBAAqB,CAAC,CAAC;gBAAAtP,SAAA,CAAAf,IAAA;gBAAA;cAAA;cAAA,OAAAe,SAAA,CAAAZ,MAAA;YAAA;cAIhC;cACA;cACA;cACMwV,gBAAgB,GAAGxR,IAAI,CAAC+E,gCAAgC;cAAAqJ,GAAA,MAAAc,eAAA,GAChCva,MAAM,CAACqK,MAAM,CAACwS,gBAAgB,CAAC;YAAA;cAAA,MAAApD,GAAA,GAAAc,eAAA,CAAAlY,MAAA;gBAAA4F,SAAA,CAAAf,IAAA;gBAAA;cAAA;cAAlD4V,eAAe,GAAAvC,eAAA,CAAAd,GAAA;cAAAxR,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAClB,IAAI,CAACiR,sBAAsB,CAC/BgE,eAAe,EACfzR,IAAI,CAACoF,eACP,CAAC;YAAA;cAAAgJ,GAAA;cAAAxR,SAAA,CAAAf,IAAA;cAAA;YAAA;cAEHmE,IAAI,CAAC+E,gCAAgC,GAAG,EAAE;cAACnI,SAAA,CAAAf,IAAA;cAAA;YAAA;cAAAe,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAErC,IAAI,CAACiR,sBAAsB,CAACjS,GAAG,EAAEwE,IAAI,CAACoF,eAAe,CAAC;YAAA;cAG9D;cACA;cACA;cACMsM,aAAa,GACjBlW,GAAG,CAACA,GAAG,KAAK,OAAO,IACnBA,GAAG,CAACA,GAAG,KAAK,SAAS,IACrBA,GAAG,CAACA,GAAG,KAAK,SAAS;cAAA,MAEnBwE,IAAI,CAACuF,uBAAuB,KAAK,CAAC,IAAI,CAACmM,aAAa;gBAAA9U,SAAA,CAAAf,IAAA;gBAAA;cAAA;cAAAe,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAChDwD,IAAI,CAACmM,oBAAoB,CAAC,CAAC;YAAA;cAAA,OAAAvP,SAAA,CAAAZ,MAAA;YAAA;cAAA,MAI/BgE,IAAI,CAACqF,sBAAsB,KAAK,IAAI;gBAAAzI,SAAA,CAAAf,IAAA;gBAAA;cAAA;cACtCmE,IAAI,CAACqF,sBAAsB,GACzB,IAAIuM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG7R,IAAI,CAACwF,qBAAqB;cAAC5I,SAAA,CAAAf,IAAA;cAAA;YAAA;cAAA,MAC3CmE,IAAI,CAACqF,sBAAsB,GAAG,IAAIuM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;gBAAAjV,SAAA,CAAAf,IAAA;gBAAA;cAAA;cAAAe,SAAA,CAAAf,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACrDwD,IAAI,CAACmM,oBAAoB,CAAC,CAAC;YAAA;cAAA,OAAAvP,SAAA,CAAAZ,MAAA;YAAA;cAInC,IAAIgE,IAAI,CAACsF,0BAA0B,EAAE;gBACnCwI,YAAY,CAAC9N,IAAI,CAACsF,0BAA0B,CAAC;cAC/C;cACAtF,IAAI,CAACsF,0BAA0B,GAAGwM,UAAU,CAAC,YAAM;gBACjD9R,IAAI,CAAC+R,sBAAsB,GAAG/R,IAAI,CAACmM,oBAAoB,CAAC,CAAC;gBACzD,IAAI7X,MAAM,CAACsV,UAAU,CAAC5J,IAAI,CAAC+R,sBAAsB,CAAC,EAAE;kBAClD/R,IAAI,CAAC+R,sBAAsB,CAACza,OAAO,CACjC;oBAAA,OAAO0I,IAAI,CAAC+R,sBAAsB,GAAG9a,SAAS;kBAAA,CAChD,CAAC;gBACH;cACF,CAAC,EAAE+I,IAAI,CAACuF,uBAAuB,CAAC;YAAC;YAAA;cAAA,OAAA3I,SAAA,CAAAF,IAAA;UAAA;QAAA;QAAA,OAAAiV,eAAA;MAAA,uBAAAjb,OAAA;IAAA;IAClC,OAzEKwG,cAAc;EAAA;EA2EpB;AACF;AACA;AACA;EAHE;EAAA7B,MAAA,CAIMoS,sBAAsB;IAA5B,SAAMA,sBAAsBA,CAACjS,GAAG,EAAEuE,OAAO;MAAA,IAAA2N,WAAA;MAAA,OAAAzS,mBAAA,CAAAQ,KAAA;QAAA,SAAAkS,wBAAA/C,SAAA;UAAA,kBAAAA,SAAA,CAAAhP,IAAA,GAAAgP,SAAA,CAAA/O,IAAA;YAAA;cACjC6R,WAAW,GAAGlS,GAAG,CAACA,GAAG;cAAAoP,SAAA,CAAA7O,EAAA,GAEnB2R,WAAW;cAAA9C,SAAA,CAAA/O,IAAA,GAAA+O,SAAA,CAAA7O,EAAA,KACZ,OAAO,OAAA6O,SAAA,CAAA7O,EAAA,KAGP,SAAS,OAAA6O,SAAA,CAAA7O,EAAA,KAGT,SAAS,OAAA6O,SAAA,CAAA7O,EAAA,KAGT,OAAO,QAAA6O,SAAA,CAAA7O,EAAA,KAGP,SAAS,QAAA6O,SAAA,CAAA7O,EAAA,KAGT,OAAO;cAAA;YAAA;cAAA6O,SAAA,CAAA/O,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAdJ,IAAI,CAACpB,WAAW,CAAC0E,cAAc,CAACtE,GAAG,EAAEuE,OAAO,CAAC;YAAA;cAAA,OAAA6K,SAAA,CAAA5O,MAAA;YAAA;cAGnD,IAAI,CAACZ,WAAW,CAAC4F,gBAAgB,CAACxF,GAAG,EAAEuE,OAAO,CAAC;cAAC,OAAA6K,SAAA,CAAA5O,MAAA;YAAA;cAGhD,IAAI,CAACZ,WAAW,CAAC8F,gBAAgB,CAAC1F,GAAG,EAAEuE,OAAO,CAAC;cAAC,OAAA6K,SAAA,CAAA5O,MAAA;YAAA;cAGhD,IAAI,CAACZ,WAAW,CAAC+F,cAAc,CAAC3F,GAAG,EAAEuE,OAAO,CAAC;cAAC,OAAA6K,SAAA,CAAA5O,MAAA;YAAA;cAG9C,IAAI,CAACZ,WAAW,CAACwG,gBAAgB,CAACpG,GAAG,EAAEuE,OAAO,CAAC;cAAC,OAAA6K,SAAA,CAAA5O,MAAA;YAAA;cAAA,OAAA4O,SAAA,CAAA5O,MAAA;YAAA;cAMhD1H,MAAM,CAACa,MAAM,CAAC,+CAA+C,EAAEqG,GAAG,CAAC;YAAC;YAAA;cAAA,OAAAoP,SAAA,CAAAlO,IAAA;UAAA;QAAA;QAAA,OAAAiR,uBAAA;MAAA,uBAAAjX,OAAA;IAAA;IAEzE,OAzBK+W,sBAAsB;EAAA;EA2B5B;AACF;AACA;AACA;EAHE;EAAApS,MAAA,CAIM+B,gBAAgB;IAAtB,SAAMA,gBAAgBA,CAAC5B,GAAG;MAAA,IAAAwE,IAAA,EAAAxB,kBAAA,EAAAoQ,CAAA,EAAAgC,CAAA;MAAA,OAAA3V,mBAAA,CAAAQ,KAAA;QAAA,SAAAuW,kBAAApE,SAAA;UAAA,kBAAAA,SAAA,CAAAhS,IAAA,GAAAgS,SAAA,CAAA/R,IAAA;YAAA;cAClBmE,IAAI,GAAG,IAAI,CAAC5E,WAAW,EAE7B;cAAA,IACKyE,OAAO,CAACG,IAAI,CAACoF,eAAe,CAAC;gBAAAwI,SAAA,CAAA/R,IAAA;gBAAA;cAAA;cAAA+R,SAAA,CAAA/R,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CAC1BwD,IAAI,CAACmM,oBAAoB,CAAC,CAAC;YAAA;cAAA,KAK/BtM,OAAO,CAACG,IAAI,CAACzB,wBAAwB,CAAC;gBAAAqP,SAAA,CAAA/R,IAAA;gBAAA;cAAA;cACxCvH,MAAM,CAACa,MAAM,CAAC,mDAAmD,CAAC;cAAC,OAAAyY,SAAA,CAAA5R,MAAA;YAAA;cAG/DwC,kBAAkB,GAAGwB,IAAI,CAACzB,wBAAwB,CAAC,CAAC,CAAC,CAACE,OAAO;cAE7DmS,CAAC,GAAGpS,kBAAkB,CAACuJ,IAAI,CAAC,UAACvB,MAAM,EAAEyL,GAAG,EAAK;gBACjD,IAAMC,KAAK,GAAG1L,MAAM,CAAC3E,QAAQ,KAAKrG,GAAG,CAACyB,EAAE;gBACxC,IAAIiV,KAAK,EAAEtD,CAAC,GAAGqD,GAAG;gBAClB,OAAOC,KAAK;cACd,CAAC,CAAC;cAAA,IACGtB,CAAC;gBAAAhD,SAAA,CAAA/R,IAAA;gBAAA;cAAA;cACJvH,MAAM,CAACa,MAAM,CAAC,qDAAqD,EAAEqG,GAAG,CAAC;cAAC,OAAAoS,SAAA,CAAA5R,MAAA;YAAA;cAI5E;cACA;cACA;cACAwC,kBAAkB,CAAC2T,MAAM,CAACvD,CAAC,EAAE,CAAC,CAAC;cAE/B,IAAIhP,MAAM,CAAC5F,IAAI,CAACwB,GAAG,EAAE,OAAO,CAAC,EAAE;gBAC7BoV,CAAC,CAAC/R,aAAa,CACb,IAAIvK,MAAM,CAACwK,KAAK,CAACtD,GAAG,CAACyR,KAAK,CAACA,KAAK,EAAEzR,GAAG,CAACyR,KAAK,CAACmF,MAAM,EAAE5W,GAAG,CAACyR,KAAK,CAACoF,OAAO,CACvE,CAAC;cACH,CAAC,MAAM;gBACL;gBACAzB,CAAC,CAAC/R,aAAa,CAAC5H,SAAS,EAAEuE,GAAG,CAACnC,MAAM,CAAC;cACxC;YAAC;YAAA;cAAA,OAAAuU,SAAA,CAAAlR,IAAA;UAAA;QAAA;QAAA,OAAAsV,iBAAA;MAAA,uBAAAtb,OAAA;IAAA;IACF,OAvCK0G,gBAAgB;EAAA;EAyCtB;AACF;AACA;AACA;EAHE;EAAA/B,MAAA,CAIM8B,eAAe;IAArB,SAAMA,eAAeA,CAAC3B,GAAG;MAAA,IAAAwE,IAAA,EAAAkI,aAAA,EAAAC,YAAA,EAAAmK,kBAAA;MAAA,OAAArX,mBAAA,CAAAQ,KAAA;QAAA,SAAA8W,iBAAAlD,SAAA;UAAA,kBAAAA,SAAA,CAAAzT,IAAA,GAAAyT,SAAA,CAAAxT,IAAA;YAAA;cACjBmE,IAAI,GAAG,IAAI,CAAC5E,WAAW,EAE7B;cACA;cAAAiU,SAAA,CAAAxT,IAAA;cAAA,OAAAZ,mBAAA,CAAAuB,KAAA,CACM,IAAI,CAACU,cAAc,CAAC1B,GAAG,CAAC;YAAA;cAAA,IAMzBoE,MAAM,CAAC5F,IAAI,CAACgG,IAAI,CAACZ,cAAc,EAAE5D,GAAG,CAACyB,EAAE,CAAC;gBAAAoS,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAAA,OAAAwT,SAAA,CAAArT,MAAA;YAAA;cAI7C;cACMkM,aAAa,GAAGlI,IAAI,CAACZ,cAAc,CAAC5D,GAAG,CAACyB,EAAE,CAAC,CAACiL,aAAa;cACzDC,YAAY,GAAGnI,IAAI,CAACZ,cAAc,CAAC5D,GAAG,CAACyB,EAAE,CAAC,CAACkL,YAAY;cAE7DnI,IAAI,CAACZ,cAAc,CAAC5D,GAAG,CAACyB,EAAE,CAAC,CAACwF,MAAM,CAAC,CAAC;cAE9B6P,kBAAkB,GAAG,SAAAA,CAAAE,MAAM,EAAI;gBACnC,OACEA,MAAM,IACNA,MAAM,CAACvF,KAAK,IACZ,IAAI3Y,MAAM,CAACwK,KAAK,CACd0T,MAAM,CAACvF,KAAK,CAACA,KAAK,EAClBuF,MAAM,CAACvF,KAAK,CAACmF,MAAM,EACnBI,MAAM,CAACvF,KAAK,CAACoF,OACf,CAAC;cAEL,CAAC,EAED;cACA,IAAInK,aAAa,IAAI1M,GAAG,CAACyR,KAAK,EAAE;gBAC9B/E,aAAa,CAACoK,kBAAkB,CAAC9W,GAAG,CAAC,CAAC;cACxC;cAEA,IAAI2M,YAAY,EAAE;gBAChBA,YAAY,CAACmK,kBAAkB,CAAC9W,GAAG,CAAC,CAAC;cACvC;YAAC;YAAA;cAAA,OAAA6T,SAAA,CAAA3S,IAAA;UAAA;QAAA;QAAA,OAAA6V,gBAAA;MAAA,uBAAA7b,OAAA;IAAA;IACF,OAzCKyG,eAAe;EAAA;EA2CrB;AACF;AACA;AACA;EAHE;EAAA9B,MAAA,CAIAgC,eAAe;IAAf,SAAAA,eAAeA,CAAC7B,GAAG,EAAE;MACnBlH,MAAM,CAACa,MAAM,CAAC,8BAA8B,EAAEqG,GAAG,CAAC4W,MAAM,CAAC;MACzD,IAAI5W,GAAG,CAACiX,gBAAgB,EAAEne,MAAM,CAACa,MAAM,CAAC,OAAO,EAAEqG,GAAG,CAACiX,gBAAgB,CAAC;IACxE;IAAC,OAHDpV,eAAe;EAAA,IAKf;EAAA;EAAA,OAAAoG,iBAAA;AAAA,I;;;;;;;;;;;AC9UFvP,MAAM,CAACoC,MAAM,CAAC;EAAC+M,aAAa,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,aAAa;EAAA;AAAC,CAAC,CAAC;AAAC,IAKnDA,aAAa;EACxB,SAAAA,cAAY3J,OAAO,EAAE;IACnB;IACA,IAAI,CAACmI,QAAQ,GAAGnI,OAAO,CAACmI,QAAQ;IAChC,IAAI,CAAClD,WAAW,GAAG,KAAK;IAExB,IAAI,CAAC+T,SAAS,GAAGhZ,OAAO,CAACC,QAAQ;IACjC,IAAI,CAACyB,WAAW,GAAG1B,OAAO,CAAC3D,UAAU;IACrC,IAAI,CAAC4c,QAAQ,GAAGjZ,OAAO,CAACsR,OAAO;IAC/B,IAAI,CAAC4H,iBAAiB,GAAGlZ,OAAO,CAAC8R,gBAAgB,IAAK,YAAM,CAAC,CAAE;IAC/D,IAAI,CAACqH,KAAK,GAAGnZ,OAAO,CAAC+R,IAAI;IACzB,IAAI,CAAC7M,OAAO,GAAGlF,OAAO,CAACkF,OAAO;IAC9B,IAAI,CAACkU,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,YAAY,GAAG,KAAK;;IAEzB;IACA,IAAI,CAAC3X,WAAW,CAAC6D,eAAe,CAAC,IAAI,CAAC4C,QAAQ,CAAC,GAAG,IAAI;EACxD;EACA;EACA;EAAA,IAAAxG,MAAA,GAAAgI,aAAA,CAAAxL,SAAA;EAAAwD,MAAA,CACAmV,WAAW;IAAX,SAAAA,WAAWA,CAAA,EAAG;MACZ;MACA;MACA;MACA,IAAI,IAAI,CAACe,SAAS,CAAC,CAAC,EAClB,MAAM,IAAIzS,KAAK,CAAC,+CAA+C,CAAC;;MAElE;MACA;MACA,IAAI,CAACiU,YAAY,GAAG,KAAK;MACzB,IAAI,CAACpU,WAAW,GAAG,IAAI;;MAEvB;MACA;MACA,IAAI,IAAI,CAACkU,KAAK,EACZ,IAAI,CAACzX,WAAW,CAAC4J,0BAA0B,CAAC,IAAI,CAACnD,QAAQ,CAAC,GAAG,IAAI;;MAEnE;MACA,IAAI,CAACzG,WAAW,CAACZ,KAAK,CAAC,IAAI,CAACmY,QAAQ,CAAC;IACvC;IAAC,OAnBDnC,WAAW;EAAA,IAoBX;EACA;EAAA;EAAAnV,MAAA,CACA2X,oBAAoB;IAApB,SAAAA,oBAAoBA,CAAA,EAAG;MACrB,IAAI,IAAI,CAACF,aAAa,IAAI,IAAI,CAACC,YAAY,EAAE;QAC3C;QACA;QACA,IAAI,CAACL,SAAS,CAAC,IAAI,CAACI,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,CAAC;;QAE5D;QACA,OAAO,IAAI,CAAC1X,WAAW,CAAC6D,eAAe,CAAC,IAAI,CAAC4C,QAAQ,CAAC;;QAEtD;QACA;QACA,IAAI,CAACzG,WAAW,CAACqV,0BAA0B,CAAC,CAAC;MAC/C;IACF;IAAC,OAbDuC,oBAAoB;EAAA,IAcpB;EACA;EACA;EACA;EAAA;EAAA3X,MAAA,CACAwD,aAAa;IAAb,SAAAA,aAAaA,CAACvF,GAAG,EAAED,MAAM,EAAE;MACzB,IAAI,IAAI,CAACkY,SAAS,CAAC,CAAC,EAClB,MAAM,IAAIzS,KAAK,CAAC,0CAA0C,CAAC;MAC7D,IAAI,CAACgU,aAAa,GAAG,CAACxZ,GAAG,EAAED,MAAM,CAAC;MAClC,IAAI,CAACuZ,iBAAiB,CAACtZ,GAAG,EAAED,MAAM,CAAC;MACnC,IAAI,CAAC2Z,oBAAoB,CAAC,CAAC;IAC7B;IAAC,OANDnU,aAAa;EAAA,IAOb;EACA;EACA;EACA;EAAA;EAAAxD,MAAA,CACAsH,WAAW;IAAX,SAAAA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACoQ,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC7B;IAAC,OAHDrQ,WAAW;EAAA,IAIX;EAAA;EAAAtH,MAAA,CACAkW,SAAS;IAAT,SAAAA,SAASA,CAAA,EAAG;MACV,OAAO,CAAC,CAAC,IAAI,CAACuB,aAAa;IAC7B;IAAC,OAFDvB,SAAS;EAAA;EAAA,OAAAlO,aAAA;AAAA,I;;;;;;;;;;;ACjFX,IAAI4P,cAAc;AAAC/e,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACkC,OAAO,EAAC,SAAAA,CAAShC,CAAC,EAAC;IAAC4e,cAAc,GAAC5e,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhHH,MAAM,CAACoC,MAAM,CAAC;EAACkN,UAAU,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,UAAU;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI9D,OAAO;AAACxL,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;EAACuL,OAAO,EAAC,SAAAA,CAASrL,CAAC,EAAC;IAACqL,OAAO,GAACrL,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAE3HmP,UAAU,0BAAA0P,MAAA;EACrB,SAAA1P,WAAA,EAAc;IAAA,OACZ0P,MAAA,CAAAlZ,IAAA,OAAM0F,OAAO,CAAC0C,WAAW,EAAE1C,OAAO,CAACW,OAAO,CAAC;EAC7C;EAAC4S,cAAA,CAAAzP,UAAA,EAAA0P,MAAA;EAAA,OAAA1P,UAAA;AAAA,EAH6B2P,KAAK,E;;;;;;;;;;;ACFrCjf,MAAM,CAACoC,MAAM,CAAC;EAAClC,GAAG,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,GAAG;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI+G,SAAS;AAACjH,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACgH,SAAS,EAAC,SAAAA,CAAS9G,CAAC,EAAC;IAAC8G,SAAS,GAAC9G,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;EAACG,MAAM,EAAC,SAAAA,CAASD,CAAC,EAAC;IAACC,MAAM,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIkC,UAAU;AAACrC,MAAM,CAACC,IAAI,CAAC,0BAA0B,EAAC;EAACoC,UAAU,EAAC,SAAAA,CAASlC,CAAC,EAAC;IAACkC,UAAU,GAAClC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAK1S;AACA;AACA;AACA,IAAM+e,cAAc,GAAG,EAAE;;AAEzB;AACA;AACA;AACA;AACO,IAAMhf,GAAG,GAAG,CAAC,CAAC;AAErB;AACA;AACA;AACAA,GAAG,CAACiE,wBAAwB,GAAG,IAAI/D,MAAM,CAAC+e,mBAAmB,CAAC,CAAC;AAC/Djf,GAAG,CAACkf,6BAA6B,GAAG,IAAIhf,MAAM,CAAC+e,mBAAmB,CAAC,CAAC;;AAEpE;AACAjf,GAAG,CAACmf,kBAAkB,GAAGnf,GAAG,CAACiE,wBAAwB;AAErDjE,GAAG,CAACof,2BAA2B,GAAG,IAAIlf,MAAM,CAAC+e,mBAAmB,CAAC,CAAC;;AAElE;AACA;AACA,SAASI,0BAA0BA,CAACzI,OAAO,EAAE;EAC3C,IAAI,CAACA,OAAO,GAAGA,OAAO;AACxB;AAEA5W,GAAG,CAACiQ,eAAe,GAAG/P,MAAM,CAACof,aAAa,CACxC,qBAAqB,EACrBD,0BACF,CAAC;AAEDrf,GAAG,CAACuf,oBAAoB,GAAGrf,MAAM,CAACof,aAAa,CAC7C,0BAA0B,EAC1B,YAAM,CAAC,CACT,CAAC;;AAED;AACA;AACA;AACAtf,GAAG,CAACwf,YAAY,GAAG,UAAA1d,IAAI,EAAI;EACzB,IAAM2d,KAAK,GAAGzf,GAAG,CAACiE,wBAAwB,CAACG,GAAG,CAAC,CAAC;EAChD,OAAO2C,SAAS,CAAC2Y,YAAY,CAACtb,GAAG,CAACqb,KAAK,EAAE3d,IAAI,CAAC;AAChD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9B,GAAG,CAAC4B,OAAO,GAAG,UAAC0N,GAAG,EAAEhK,OAAO,EAAK;EAC9B,IAAMqa,GAAG,GAAG,IAAIxd,UAAU,CAACmN,GAAG,EAAEhK,OAAO,CAAC;EACxC0Z,cAAc,CAACxQ,IAAI,CAACmR,GAAG,CAAC,CAAC,CAAC;EAC1B,OAAOA,GAAG;AACZ,CAAC;AAED3f,GAAG,CAAC2c,cAAc,GAAG,IAAIiD,IAAI,CAAC;EAAE/N,eAAe,EAAE;AAAM,CAAC,CAAC;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA7R,GAAG,CAAC+P,WAAW,GAAG,UAAAxK,QAAQ;EAAA,OAAIvF,GAAG,CAAC2c,cAAc,CAACkD,QAAQ,CAACta,QAAQ,CAAC;AAAA;;AAEnE;AACA;AACA;AACAvF,GAAG,CAAC8f,sBAAsB,GAAG;EAAA,OAAMd,cAAc,CAACe,KAAK,CACrD,UAAAC,IAAI;IAAA,OAAIzf,MAAM,CAACqK,MAAM,CAACoV,IAAI,CAAChV,cAAc,CAAC,CAAC+U,KAAK,CAAC,UAAA5U,GAAG;MAAA,OAAIA,GAAG,CAACiC,KAAK;IAAA,EAAC;EAAA,CACpE,CAAC;AAAA,E", "file": "/packages/ddp-client.js", "sourcesContent": ["export { DDP } from '../common/namespace.js';\n\nimport '../common/livedata_connection';\n\n// Initialize the default server connection and put it on Meteor.connection\nimport './client_convenience';\n", "import { DDP } from '../common/namespace.js';\nimport { Meteor } from 'meteor/meteor';\nimport { loadAsyncStubHelpers } from \"./queue_stub_helpers\";\n\n// Meteor.refresh can be called on the client (if you're in common code) but it\n// only has an effect on the server.\nMeteor.refresh = () => {};\n\n// By default, try to connect back to the same endpoint as the page\n// was served from.\n//\n// XXX We should be doing this a different way. Right now we don't\n// include ROOT_URL_PATH_PREFIX when computing ddpUrl. (We don't\n// include it on the server when computing\n// DDP_DEFAULT_CONNECTION_URL, and we don't include it in our\n// default, '/'.) We get by with this because DDP.connect then\n// forces the URL passed to it to be interpreted relative to the\n// app's deploy path, even if it is absolute. Instead, we should\n// make DDP_DEFAULT_CONNECTION_URL, if set, include the path prefix;\n// make the default ddpUrl be '' rather that '/'; and make\n// _translateUrl in stream_client_common.js not force absolute paths\n// to be treated like relative paths. See also\n// stream_client_common.js #RationalizingRelativeDDPURLs\nconst runtimeConfig = typeof __meteor_runtime_config__ !== 'undefined' ? __meteor_runtime_config__ : Object.create(null);\nconst ddpUrl = runtimeConfig.DDP_DEFAULT_CONNECTION_URL || '/';\n\nconst retry = new Retry();\n\nfunction onDDPVersionNegotiationFailure(description) {\n  Meteor._debug(description);\n  if (Package.reload) {\n    const migrationData = Package.reload.Reload._migrationData('livedata') || Object.create(null);\n    let failures = migrationData.DDPVersionNegotiationFailures || 0;\n    ++failures;\n    Package.reload.Reload._onMigrate('livedata', () => [true, { DDPVersionNegotiationFailures: failures }]);\n    retry.retryLater(failures, () => {\n      Package.reload.Reload._reload({ immediateMigration: true });\n    });\n  }\n}\n\n// Makes sure to inject the stub async helpers before creating the connection\nloadAsyncStubHelpers();\n\nMeteor.connection = DDP.connect(ddpUrl, {\n  onDDPVersionNegotiationFailure: onDDPVersionNegotiationFailure\n});\n\n// Proxy the public methods of Meteor.connection so they can\n// be called directly on Meteor.\n[\n  'subscribe',\n  'methods',\n  'isAsyncCall',\n  'call',\n  'callAsync',\n  'apply',\n  'applyAsync',\n  'status',\n  'reconnect',\n  'disconnect'\n].forEach(name => {\n  Meteor[name] = Meteor.connection[name].bind(Meteor.connection);\n});\n", "import { DDP } from \"../common/namespace.js\";\nimport { Connection } from \"../common/livedata_connection\";\n\n// https://forums.meteor.com/t/proposal-to-fix-issues-with-async-method-stubs/60826\n\nlet queueSize = 0;\nlet queue = Promise.resolve();\n\nexport const loadAsyncStubHelpers = () => {\n  function queueFunction(fn, promiseProps = {}) {\n    queueSize += 1;\n\n    let resolve;\n    let reject;\n    const promise = new Promise((_resolve, _reject) => {\n      resolve = _resolve;\n      reject = _reject;\n    });\n\n    queue = queue.finally(() => {\n      fn(resolve, reject);\n\n      return promise.stubPromise?.catch(() => {}); // silent uncaught promise\n    });\n\n    promise\n      .catch(() => {}) // silent uncaught promise\n      .finally(() => {\n        queueSize -= 1;\n        if (queueSize === 0) {\n          Meteor.connection._maybeMigrate();\n        }\n      });\n\n    promise.stubPromise = promiseProps.stubPromise;\n    promise.serverPromise = promiseProps.serverPromise;\n\n    return promise;\n  }\n\n  let oldReadyToMigrate = Connection.prototype._readyToMigrate;\n  Connection.prototype._readyToMigrate = function () {\n    if (queueSize > 0) {\n      return false;\n    }\n\n    return oldReadyToMigrate.apply(this, arguments);\n  };\n\n  let currentMethodInvocation = null;\n\n  /**\n   * Meteor sets CurrentMethodInvocation to undefined for the reasons explained at\n   * https://github.com/meteor/meteor/blob/c9e3551b9673a7ed607f18cb1128563ff49ca96f/packages/ddp-client/common/livedata_connection.js#L578-L605\n   * The app code could call `.then` on a promise while the async stub is running,\n   * causing the `then` callback to think it is inside the stub.\n   *\n   * With the queueing we are doing, this is no longer necessary. The point\n   * of the queueing is to prevent app/package code from running while\n   * the stub is running, so we don't need to worry about this.\n   */\n\n  let oldApplyAsync = Connection.prototype.applyAsync;\n  Connection.prototype.applyAsync = function () {\n    let args = arguments;\n    let name = args[0];\n\n    if (currentMethodInvocation) {\n      DDP._CurrentMethodInvocation._set(currentMethodInvocation);\n      currentMethodInvocation = null;\n    }\n\n    const enclosing = DDP._CurrentMethodInvocation.get();\n    const alreadyInSimulation = enclosing?.isSimulation;\n    const isFromCallAsync = enclosing?._isFromCallAsync;\n\n    if (\n      Meteor.connection._getIsSimulation({\n        isFromCallAsync,\n        alreadyInSimulation,\n      })\n    ) {\n      // In stub - call immediately\n      return oldApplyAsync.apply(this, args);\n    }\n\n    let stubPromiseResolver;\n    let serverPromiseResolver;\n    const stubPromise = new Promise((r) => (stubPromiseResolver = r));\n    const serverPromise = new Promise((r) => (serverPromiseResolver = r));\n\n    return queueFunction(\n      (resolve, reject) => {\n        let finished = false;\n\n        Meteor._setImmediate(() => {\n          const applyAsyncPromise = oldApplyAsync.apply(this, args);\n          stubPromiseResolver(applyAsyncPromise.stubPromise);\n          serverPromiseResolver(applyAsyncPromise.serverPromise);\n\n          applyAsyncPromise.stubPromise\n            .catch(() => {}) // silent uncaught promise\n            .finally(() => {\n              finished = true;\n            });\n\n          applyAsyncPromise\n            .then((result) => {\n              resolve(result);\n            })\n            .catch((err) => {\n              reject(err);\n            });\n\n          serverPromise.catch(() => {}); // silent uncaught promise\n        });\n\n        Meteor._setImmediate(() => {\n          if (!finished) {\n            console.warn(\n              `Method stub (${name}) took too long and could cause unexpected problems. Learn more at https://v3-migration-docs.meteor.com/breaking-changes/call-x-callAsync.html#considerations-for-effective-use-of-meteor-callasync`\n            );\n          }\n        });\n      },\n      {\n        stubPromise,\n        serverPromise,\n      }\n    );\n  };\n\n  let oldApply = Connection.prototype.apply;\n  Connection.prototype.apply = function (name, args, options, callback) {\n    if (this._stream._neverQueued) {\n      return oldApply.apply(this, arguments);\n    }\n\n    // Apply runs the stub before synchronously returning.\n    //\n    // However, we want the server to run the methods in the original call order\n    // so we have to queue sending the message to the server until any previous async\n    // methods run.\n    // This does mean the stubs run in a different order than the methods on the\n    // server.\n\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = undefined;\n    }\n\n    let { methodInvoker, result } = oldApply.call(this, name, args, {\n      ...options,\n      _returnMethodInvoker: true,\n    }, callback);\n\n    if (methodInvoker) {\n      queueFunction((resolve) => {\n        this._addOutstandingMethod(methodInvoker, options);\n        resolve();\n      });\n    }\n\n    return result;\n  };\n\n  /**\n   * Queue subscriptions in case they rely on previous method calls\n   */\n  let queueSend = false;\n  let oldSubscribe = Connection.prototype.subscribe;\n  Connection.prototype.subscribe = function () {\n    if (this._stream._neverQueued) {\n      return oldSubscribe.apply(this, arguments);\n    }\n\n    queueSend = true;\n    try {\n      return oldSubscribe.apply(this, arguments);\n    } finally {\n      queueSend = false;\n    }\n  };\n\n  let oldSend = Connection.prototype._send;\n  Connection.prototype._send = function (params, shouldQueue) {\n    if (this._stream._neverQueued) {\n      return oldSend.apply(this, arguments);\n    }\n\n    if (!queueSend && !shouldQueue) {\n      return oldSend.call(this, params);\n    }\n\n    queueSend = false;\n    queueFunction((resolve) => {\n      try {\n        oldSend.call(this, params);\n      } finally {\n        resolve();\n      }\n    });\n  };\n\n  let _oldSendOutstandingMethodBlocksMessages =\n    Connection.prototype._sendOutstandingMethodBlocksMessages;\n  Connection.prototype._sendOutstandingMethodBlocksMessages = function () {\n    if (this._stream._neverQueued) {\n      return _oldSendOutstandingMethodBlocksMessages.apply(this, arguments);\n    }\n    queueFunction((resolve) => {\n      try {\n        _oldSendOutstandingMethodBlocksMessages.apply(this, arguments);\n      } finally {\n        resolve();\n      }\n    });\n  };\n};\n", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\n\nexport class ConnectionStreamHandlers {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * Handles incoming raw messages from the DDP stream\n   * @param {String} raw_msg The raw message received from the stream\n   */\n  async onMessage(raw_msg) {\n    let msg;\n    try {\n      msg = DDPCommon.parseDDP(raw_msg);\n    } catch (e) {\n      Meteor._debug('Exception while parsing DDP', e);\n      return;\n    }\n\n    // Any message counts as receiving a pong, as it demonstrates that\n    // the server is still alive.\n    if (this._connection._heartbeat) {\n      this._connection._heartbeat.messageReceived();\n    }\n\n    if (msg === null || !msg.msg) {\n      if(!msg || !msg.testMessageOnConnect) {\n        if (Object.keys(msg).length === 1 && msg.server_id) return;\n        Meteor._debug('discarding invalid livedata message', msg);\n      }\n      return;\n    }\n\n    // Important: This was missing from previous version\n    // We need to set the current version before routing the message\n    if (msg.msg === 'connected') {\n      this._connection._version = this._connection._versionSuggestion;\n    }\n\n    await this._routeMessage(msg);\n  }\n\n  /**\n   * Routes messages to their appropriate handlers based on message type\n   * @private\n   * @param {Object} msg The parsed DDP message\n   */\n  async _routeMessage(msg) {\n    switch (msg.msg) {\n      case 'connected':\n        await this._connection._livedata_connected(msg);\n        this._connection.options.onConnected();\n        break;\n\n      case 'failed':\n        await this._handleFailedMessage(msg);\n        break;\n\n      case 'ping':\n        if (this._connection.options.respondToPings) {\n          this._connection._send({ msg: 'pong', id: msg.id });\n        }\n        break;\n\n      case 'pong':\n        // noop, as we assume everything's a pong\n        break;\n\n      case 'added':\n      case 'changed':\n      case 'removed':\n      case 'ready':\n      case 'updated':\n        await this._connection._livedata_data(msg);\n        break;\n\n      case 'nosub':\n        await this._connection._livedata_nosub(msg);\n        break;\n\n      case 'result':\n        await this._connection._livedata_result(msg);\n        break;\n\n      case 'error':\n        this._connection._livedata_error(msg);\n        break;\n\n      default:\n        Meteor._debug('discarding unknown livedata message type', msg);\n    }\n  }\n\n  /**\n   * Handles failed connection messages\n   * @private\n   * @param {Object} msg The failed message object\n   */\n  _handleFailedMessage(msg) {\n    if (this._connection._supportedDDPVersions.indexOf(msg.version) >= 0) {\n      this._connection._versionSuggestion = msg.version;\n      this._connection._stream.reconnect({ _force: true });\n    } else {\n      const description =\n        'DDP version negotiation failed; server requested version ' +\n        msg.version;\n      this._connection._stream.disconnect({ _permanent: true, _error: description });\n      this._connection.options.onDDPVersionNegotiationFailure(description);\n    }\n  }\n\n  /**\n   * Handles connection reset events\n   */\n  onReset() {\n    // Reset is called even on the first connection, so this is\n    // the only place we send this message.\n    const msg = this._buildConnectMessage();\n    this._connection._send(msg);\n\n    // Mark non-retry calls as failed and handle outstanding methods\n    this._handleOutstandingMethodsOnReset();\n\n    // Now, to minimize setup latency, go ahead and blast out all of\n    // our pending methods ands subscriptions before we've even taken\n    // the necessary RTT to know if we successfully reconnected.\n    this._connection._callOnReconnectAndSendAppropriateOutstandingMethods();\n    this._resendSubscriptions();\n  }\n\n  /**\n   * Builds the initial connect message\n   * @private\n   * @returns {Object} The connect message object\n   */\n  _buildConnectMessage() {\n    const msg = { msg: 'connect' };\n    if (this._connection._lastSessionId) {\n      msg.session = this._connection._lastSessionId;\n    }\n    msg.version = this._connection._versionSuggestion || this._connection._supportedDDPVersions[0];\n    this._connection._versionSuggestion = msg.version;\n    msg.support = this._connection._supportedDDPVersions;\n    return msg;\n  }\n\n  /**\n   * Handles outstanding methods during a reset\n   * @private\n   */\n  _handleOutstandingMethodsOnReset() {\n    const blocks = this._connection._outstandingMethodBlocks;\n    if (blocks.length === 0) return;\n\n    const currentMethodBlock = blocks[0].methods;\n    blocks[0].methods = currentMethodBlock.filter(\n      methodInvoker => {\n        // Methods with 'noRetry' option set are not allowed to re-send after\n        // recovering dropped connection.\n        if (methodInvoker.sentMessage && methodInvoker.noRetry) {\n          methodInvoker.receiveResult(\n            new Meteor.Error(\n              'invocation-failed',\n              'Method invocation might have failed due to dropped connection. ' +\n              'Failing because `noRetry` option was passed to Meteor.apply.'\n            )\n          );\n        }\n\n        // Only keep a method if it wasn't sent or it's allowed to retry.\n        return !(methodInvoker.sentMessage && methodInvoker.noRetry);\n      }\n    );\n\n    // Clear empty blocks\n    if (blocks.length > 0 && blocks[0].methods.length === 0) {\n      blocks.shift();\n    }\n\n    // Reset all method invokers as unsent\n    Object.values(this._connection._methodInvokers).forEach(invoker => {\n      invoker.sentMessage = false;\n    });\n  }\n\n  /**\n   * Resends all active subscriptions\n   * @private\n   */\n  _resendSubscriptions() {\n    Object.entries(this._connection._subscriptions).forEach(([id, sub]) => {\n      this._connection._sendQueued({\n        msg: 'sub',\n        id: id,\n        name: sub.name,\n        params: sub.params\n      });\n    });\n  }\n}", "import { MongoID } from 'meteor/mongo-id';\nimport { DiffSequence } from 'meteor/diff-sequence';\nimport { hasOwn } from \"meteor/ddp-common/utils\";\nimport { isEmpty } from \"meteor/ddp-common/utils\";\n\nexport class DocumentProcessors {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * @summary Process an 'added' message from the server\n   * @param {Object} msg The added message\n   * @param {Object} updates The updates accumulator\n   */\n  async _process_added(msg, updates) {\n    const self = this._connection;\n    const id = MongoID.idParse(msg.id);\n    const serverDoc = self._getServerDoc(msg.collection, id);\n\n    if (serverDoc) {\n      // Some outstanding stub wrote here.\n      const isExisting = serverDoc.document !== undefined;\n\n      serverDoc.document = msg.fields || Object.create(null);\n      serverDoc.document._id = id;\n\n      if (self._resetStores) {\n        // During reconnect the server is sending adds for existing ids.\n        // Always push an update so that document stays in the store after\n        // reset. Use current version of the document for this update, so\n        // that stub-written values are preserved.\n        const currentDoc = await self._stores[msg.collection].getDoc(msg.id);\n        if (currentDoc !== undefined) msg.fields = currentDoc;\n\n        self._pushUpdate(updates, msg.collection, msg);\n      } else if (isExisting) {\n        throw new Error('Server sent add for existing id: ' + msg.id);\n      }\n    } else {\n      self._pushUpdate(updates, msg.collection, msg);\n    }\n  }\n\n  /**\n   * @summary Process a 'changed' message from the server\n   * @param {Object} msg The changed message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_changed(msg, updates) {\n    const self = this._connection;\n    const serverDoc = self._getServerDoc(msg.collection, MongoID.idParse(msg.id));\n\n    if (serverDoc) {\n      if (serverDoc.document === undefined) {\n        throw new Error('Server sent changed for nonexisting id: ' + msg.id);\n      }\n      DiffSequence.applyChanges(serverDoc.document, msg.fields);\n    } else {\n      self._pushUpdate(updates, msg.collection, msg);\n    }\n  }\n\n  /**\n   * @summary Process a 'removed' message from the server\n   * @param {Object} msg The removed message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_removed(msg, updates) {\n    const self = this._connection;\n    const serverDoc = self._getServerDoc(msg.collection, MongoID.idParse(msg.id));\n\n    if (serverDoc) {\n      // Some outstanding stub wrote here.\n      if (serverDoc.document === undefined) {\n        throw new Error('Server sent removed for nonexisting id:' + msg.id);\n      }\n      serverDoc.document = undefined;\n    } else {\n      self._pushUpdate(updates, msg.collection, {\n        msg: 'removed',\n        collection: msg.collection,\n        id: msg.id\n      });\n    }\n  }\n\n  /**\n   * @summary Process a 'ready' message from the server\n   * @param {Object} msg The ready message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_ready(msg, updates) {\n    const self = this._connection;\n\n    // Process \"sub ready\" messages. \"sub ready\" messages don't take effect\n    // until all current server documents have been flushed to the local\n    // database. We can use a write fence to implement this.\n    msg.subs.forEach((subId) => {\n      self._runWhenAllServerDocsAreFlushed(() => {\n        const subRecord = self._subscriptions[subId];\n        // Did we already unsubscribe?\n        if (!subRecord) return;\n        // Did we already receive a ready message? (Oops!)\n        if (subRecord.ready) return;\n        subRecord.ready = true;\n        subRecord.readyCallback && subRecord.readyCallback();\n        subRecord.readyDeps.changed();\n      });\n    });\n  }\n\n  /**\n   * @summary Process an 'updated' message from the server\n   * @param {Object} msg The updated message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_updated(msg, updates) {\n    const self = this._connection;\n    // Process \"method done\" messages.\n    msg.methods.forEach((methodId) => {\n      const docs = self._documentsWrittenByStub[methodId] || {};\n      Object.values(docs).forEach((written) => {\n        const serverDoc = self._getServerDoc(written.collection, written.id);\n        if (!serverDoc) {\n          throw new Error('Lost serverDoc for ' + JSON.stringify(written));\n        }\n        if (!serverDoc.writtenByStubs[methodId]) {\n          throw new Error(\n            'Doc ' +\n            JSON.stringify(written) +\n            ' not written by method ' +\n            methodId\n          );\n        }\n        delete serverDoc.writtenByStubs[methodId];\n        if (isEmpty(serverDoc.writtenByStubs)) {\n          // All methods whose stubs wrote this method have completed! We can\n          // now copy the saved document to the database (reverting the stub's\n          // change if the server did not write to this object, or applying the\n          // server's writes if it did).\n\n          // This is a fake ddp 'replace' message.  It's just for talking\n          // between livedata connections and minimongo.  (We have to stringify\n          // the ID because it's supposed to look like a wire message.)\n          self._pushUpdate(updates, written.collection, {\n            msg: 'replace',\n            id: MongoID.idStringify(written.id),\n            replace: serverDoc.document\n          });\n          // Call all flush callbacks.\n          serverDoc.flushCallbacks.forEach((c) => {\n            c();\n          });\n\n          // Delete this completed serverDocument. Don't bother to GC empty\n          // IdMaps inside self._serverDocuments, since there probably aren't\n          // many collections and they'll be written repeatedly.\n          self._serverDocuments[written.collection].remove(written.id);\n        }\n      });\n      delete self._documentsWrittenByStub[methodId];\n\n      // We want to call the data-written callback, but we can't do so until all\n      // currently buffered messages are flushed.\n      const callbackInvoker = self._methodInvokers[methodId];\n      if (!callbackInvoker) {\n        throw new Error('No callback invoker for method ' + methodId);\n      }\n\n      self._runWhenAllServerDocsAreFlushed(\n        (...args) => callbackInvoker.dataVisible(...args)\n      );\n    });\n  }\n\n  /**\n   * @summary Push an update to the buffer\n   * @private\n   * @param {Object} updates The updates accumulator\n   * @param {String} collection The collection name\n   * @param {Object} msg The update message\n   */\n  _pushUpdate(updates, collection, msg) {\n    if (!hasOwn.call(updates, collection)) {\n      updates[collection] = [];\n    }\n    updates[collection].push(msg);\n  }\n\n  /**\n   * @summary Get a server document by collection and id\n   * @private\n   * @param {String} collection The collection name\n   * @param {String} id The document id\n   * @returns {Object|null} The server document or null\n   */\n  _getServerDoc(collection, id) {\n    const self = this._connection;\n    if (!hasOwn.call(self._serverDocuments, collection)) {\n      return null;\n    }\n    const serverDocsForCollection = self._serverDocuments[collection];\n    return serverDocsForCollection.get(id) || null;\n  }\n}", "import { Meteor } from 'meteor/meteor';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'meteor/ddp-common';\nimport { Tracker } from 'meteor/tracker';\nimport { EJSON } from 'meteor/ejson';\nimport { Random } from 'meteor/random';\nimport { MongoID } from 'meteor/mongo-id';\nimport { DDP } from './namespace.js';\nimport { MethodInvoker } from './method_invoker';\nimport {\n  hasOwn,\n  slice,\n  keys,\n  isEmpty,\n  last,\n} from \"meteor/ddp-common/utils\";\nimport { ConnectionStreamHandlers } from './connection_stream_handlers';\nimport { MongoIDMap } from './mongo_id_map';\nimport { MessageProcessors } from './message_processors';\nimport { DocumentProcessors } from './document_processors';\n\n// @param url {String|Object} URL to Meteor app,\n//   or an object as a test hook (see code)\n// Options:\n//   reloadWithOutstanding: is it OK to reload if there are outstanding methods?\n//   headers: extra headers to send on the websockets connection, for\n//     server-to-server DDP only\n//   _sockjsOptions: Specifies options to pass through to the sockjs client\n//   onDDPNegotiationVersionFailure: callback when version negotiation fails.\n//\n// XXX There should be a way to destroy a DDP connection, causing all\n// outstanding method calls to fail.\n//\n// XXX Our current way of handling failure and reconnection is great\n// for an app (where we want to tolerate being disconnected as an\n// expect state, and keep trying forever to reconnect) but cumbersome\n// for something like a command line tool that wants to make a\n// connection, call a method, and print an error if connection\n// fails. We should have better usability in the latter case (while\n// still transparently reconnecting if it's just a transient failure\n// or the server migrating us).\nexport class Connection {\n  constructor(url, options) {\n    const self = this;\n\n    this.options = options = {\n      onConnected() {},\n      onDDPVersionNegotiationFailure(description) {\n        Meteor._debug(description);\n      },\n      heartbeatInterval: 17500,\n      heartbeatTimeout: 15000,\n      npmFayeOptions: Object.create(null),\n      // These options are only for testing.\n      reloadWithOutstanding: false,\n      supportedDDPVersions: DDPCommon.SUPPORTED_DDP_VERSIONS,\n      retry: true,\n      respondToPings: true,\n      // When updates are coming within this ms interval, batch them together.\n      bufferedWritesInterval: 5,\n      // Flush buffers immediately if writes are happening continuously for more than this many ms.\n      bufferedWritesMaxAge: 500,\n\n      ...options\n    };\n\n    // If set, called when we reconnect, queuing method calls _before_ the\n    // existing outstanding ones.\n    // NOTE: This feature has been preserved for backwards compatibility. The\n    // preferred method of setting a callback on reconnect is to use\n    // DDP.onReconnect.\n    self.onReconnect = null;\n\n    // as a test hook, allow passing a stream instead of a url.\n    if (typeof url === 'object') {\n      self._stream = url;\n    } else {\n      import { ClientStream } from \"meteor/socket-stream-client\";\n\n      self._stream = new ClientStream(url, {\n        retry: options.retry,\n        ConnectionError: DDP.ConnectionError,\n        headers: options.headers,\n        _sockjsOptions: options._sockjsOptions,\n        // Used to keep some tests quiet, or for other cases in which\n        // the right thing to do with connection errors is to silently\n        // fail (e.g. sending package usage stats). At some point we\n        // should have a real API for handling client-stream-level\n        // errors.\n        _dontPrintErrors: options._dontPrintErrors,\n        connectTimeoutMs: options.connectTimeoutMs,\n        npmFayeOptions: options.npmFayeOptions\n      });\n    }\n\n    self._lastSessionId = null;\n    self._versionSuggestion = null; // The last proposed DDP version.\n    self._version = null; // The DDP version agreed on by client and server.\n    self._stores = Object.create(null); // name -> object with methods\n    self._methodHandlers = Object.create(null); // name -> func\n    self._nextMethodId = 1;\n    self._supportedDDPVersions = options.supportedDDPVersions;\n\n    self._heartbeatInterval = options.heartbeatInterval;\n    self._heartbeatTimeout = options.heartbeatTimeout;\n\n    // Tracks methods which the user has tried to call but which have not yet\n    // called their user callback (ie, they are waiting on their result or for all\n    // of their writes to be written to the local cache). Map from method ID to\n    // MethodInvoker object.\n    self._methodInvokers = Object.create(null);\n\n    // Tracks methods which the user has called but whose result messages have not\n    // arrived yet.\n    //\n    // _outstandingMethodBlocks is an array of blocks of methods. Each block\n    // represents a set of methods that can run at the same time. The first block\n    // represents the methods which are currently in flight; subsequent blocks\n    // must wait for previous blocks to be fully finished before they can be sent\n    // to the server.\n    //\n    // Each block is an object with the following fields:\n    // - methods: a list of MethodInvoker objects\n    // - wait: a boolean; if true, this block had a single method invoked with\n    //         the \"wait\" option\n    //\n    // There will never be adjacent blocks with wait=false, because the only thing\n    // that makes methods need to be serialized is a wait method.\n    //\n    // Methods are removed from the first block when their \"result\" is\n    // received. The entire first block is only removed when all of the in-flight\n    // methods have received their results (so the \"methods\" list is empty) *AND*\n    // all of the data written by those methods are visible in the local cache. So\n    // it is possible for the first block's methods list to be empty, if we are\n    // still waiting for some objects to quiesce.\n    //\n    // Example:\n    //  _outstandingMethodBlocks = [\n    //    {wait: false, methods: []},\n    //    {wait: true, methods: [<MethodInvoker for 'login'>]},\n    //    {wait: false, methods: [<MethodInvoker for 'foo'>,\n    //                            <MethodInvoker for 'bar'>]}]\n    // This means that there were some methods which were sent to the server and\n    // which have returned their results, but some of the data written by\n    // the methods may not be visible in the local cache. Once all that data is\n    // visible, we will send a 'login' method. Once the login method has returned\n    // and all the data is visible (including re-running subs if userId changes),\n    // we will send the 'foo' and 'bar' methods in parallel.\n    self._outstandingMethodBlocks = [];\n\n    // method ID -> array of objects with keys 'collection' and 'id', listing\n    // documents written by a given method's stub. keys are associated with\n    // methods whose stub wrote at least one document, and whose data-done message\n    // has not yet been received.\n    self._documentsWrittenByStub = {};\n    // collection -> IdMap of \"server document\" object. A \"server document\" has:\n    // - \"document\": the version of the document according the\n    //   server (ie, the snapshot before a stub wrote it, amended by any changes\n    //   received from the server)\n    //   It is undefined if we think the document does not exist\n    // - \"writtenByStubs\": a set of method IDs whose stubs wrote to the document\n    //   whose \"data done\" messages have not yet been processed\n    self._serverDocuments = {};\n\n    // Array of callbacks to be called after the next update of the local\n    // cache. Used for:\n    //  - Calling methodInvoker.dataVisible and sub ready callbacks after\n    //    the relevant data is flushed.\n    //  - Invoking the callbacks of \"half-finished\" methods after reconnect\n    //    quiescence. Specifically, methods whose result was received over the old\n    //    connection (so we don't re-send it) but whose data had not been made\n    //    visible.\n    self._afterUpdateCallbacks = [];\n\n    // In two contexts, we buffer all incoming data messages and then process them\n    // all at once in a single update:\n    //   - During reconnect, we buffer all data messages until all subs that had\n    //     been ready before reconnect are ready again, and all methods that are\n    //     active have returned their \"data done message\"; then\n    //   - During the execution of a \"wait\" method, we buffer all data messages\n    //     until the wait method gets its \"data done\" message. (If the wait method\n    //     occurs during reconnect, it doesn't get any special handling.)\n    // all data messages are processed in one update.\n    //\n    // The following fields are used for this \"quiescence\" process.\n\n    // This buffers the messages that aren't being processed yet.\n    self._messagesBufferedUntilQuiescence = [];\n    // Map from method ID -> true. Methods are removed from this when their\n    // \"data done\" message is received, and we will not quiesce until it is\n    // empty.\n    self._methodsBlockingQuiescence = {};\n    // map from sub ID -> true for subs that were ready (ie, called the sub\n    // ready callback) before reconnect but haven't become ready again yet\n    self._subsBeingRevived = {}; // map from sub._id -> true\n    // if true, the next data update should reset all stores. (set during\n    // reconnect.)\n    self._resetStores = false;\n\n    // name -> array of updates for (yet to be created) collections\n    self._updatesForUnknownStores = {};\n    // if we're blocking a migration, the retry func\n    self._retryMigrate = null;\n    // Collection name -> array of messages.\n    self._bufferedWrites = {};\n    // When current buffer of updates must be flushed at, in ms timestamp.\n    self._bufferedWritesFlushAt = null;\n    // Timeout handle for the next processing of all pending writes\n    self._bufferedWritesFlushHandle = null;\n\n    self._bufferedWritesInterval = options.bufferedWritesInterval;\n    self._bufferedWritesMaxAge = options.bufferedWritesMaxAge;\n\n    // metadata for subscriptions.  Map from sub ID to object with keys:\n    //   - id\n    //   - name\n    //   - params\n    //   - inactive (if true, will be cleaned up if not reused in re-run)\n    //   - ready (has the 'ready' message been received?)\n    //   - readyCallback (an optional callback to call when ready)\n    //   - errorCallback (an optional callback to call if the sub terminates with\n    //                    an error, XXX COMPAT WITH *******)\n    //   - stopCallback (an optional callback to call when the sub terminates\n    //     for any reason, with an error argument if an error triggered the stop)\n    self._subscriptions = {};\n\n    // Reactive userId.\n    self._userId = null;\n    self._userIdDeps = new Tracker.Dependency();\n\n    // Block auto-reload while we're waiting for method responses.\n    if (Meteor.isClient &&\n      Package.reload &&\n      ! options.reloadWithOutstanding) {\n      Package.reload.Reload._onMigrate(retry => {\n        if (! self._readyToMigrate()) {\n          self._retryMigrate = retry;\n          return [false];\n        } else {\n          return [true];\n        }\n      });\n    }\n\n    this._streamHandlers = new ConnectionStreamHandlers(this);\n\n    const onDisconnect = () => {\n      if (this._heartbeat) {\n        this._heartbeat.stop();\n        this._heartbeat = null;\n      }\n    };\n\n    if (Meteor.isServer) {\n      this._stream.on(\n        'message',\n        Meteor.bindEnvironment(\n          msg => this._streamHandlers.onMessage(msg),\n          'handling DDP message'\n        )\n      );\n      this._stream.on(\n        'reset',\n        Meteor.bindEnvironment(\n          () => this._streamHandlers.onReset(),\n          'handling DDP reset'\n        )\n      );\n      this._stream.on(\n        'disconnect',\n        Meteor.bindEnvironment(onDisconnect, 'handling DDP disconnect')\n      );\n    } else {\n      this._stream.on('message', msg => this._streamHandlers.onMessage(msg));\n      this._stream.on('reset', () => this._streamHandlers.onReset());\n      this._stream.on('disconnect', onDisconnect);\n    }\n\n    this._messageProcessors = new MessageProcessors(this);\n\n    // Expose message processor methods to maintain backward compatibility\n    this._livedata_connected = (msg) => this._messageProcessors._livedata_connected(msg);\n    this._livedata_data = (msg) => this._messageProcessors._livedata_data(msg);\n    this._livedata_nosub = (msg) => this._messageProcessors._livedata_nosub(msg);\n    this._livedata_result = (msg) => this._messageProcessors._livedata_result(msg);\n    this._livedata_error = (msg) => this._messageProcessors._livedata_error(msg);\n\n    this._documentProcessors = new DocumentProcessors(this);\n\n    // Expose document processor methods to maintain backward compatibility\n    this._process_added = (msg, updates) => this._documentProcessors._process_added(msg, updates);\n    this._process_changed = (msg, updates) => this._documentProcessors._process_changed(msg, updates);\n    this._process_removed = (msg, updates) => this._documentProcessors._process_removed(msg, updates);\n    this._process_ready = (msg, updates) => this._documentProcessors._process_ready(msg, updates);\n    this._process_updated = (msg, updates) => this._documentProcessors._process_updated(msg, updates);\n\n    // Also expose utility methods used by other parts of the system\n    this._pushUpdate = (updates, collection, msg) =>\n      this._documentProcessors._pushUpdate(updates, collection, msg);\n    this._getServerDoc = (collection, id) =>\n      this._documentProcessors._getServerDoc(collection, id);\n  }\n\n  // 'name' is the name of the data on the wire that should go in the\n  // store. 'wrappedStore' should be an object with methods beginUpdate, update,\n  // endUpdate, saveOriginals, retrieveOriginals. see Collection for an example.\n  createStoreMethods(name, wrappedStore) {\n    const self = this;\n\n    if (name in self._stores) return false;\n\n    // Wrap the input object in an object which makes any store method not\n    // implemented by 'store' into a no-op.\n    const store = Object.create(null);\n    const keysOfStore = [\n      'update',\n      'beginUpdate',\n      'endUpdate',\n      'saveOriginals',\n      'retrieveOriginals',\n      'getDoc',\n      '_getCollection'\n    ];\n    keysOfStore.forEach((method) => {\n      store[method] = (...args) => {\n        if (wrappedStore[method]) {\n          return wrappedStore[method](...args);\n        }\n      };\n    });\n    self._stores[name] = store;\n    return store;\n  }\n\n  registerStoreClient(name, wrappedStore) {\n    const self = this;\n\n    const store = self.createStoreMethods(name, wrappedStore);\n\n    const queued = self._updatesForUnknownStores[name];\n    if (Array.isArray(queued)) {\n      store.beginUpdate(queued.length, false);\n      queued.forEach(msg => {\n        store.update(msg);\n      });\n      store.endUpdate();\n      delete self._updatesForUnknownStores[name];\n    }\n\n    return true;\n  }\n  async registerStoreServer(name, wrappedStore) {\n    const self = this;\n\n    const store = self.createStoreMethods(name, wrappedStore);\n\n    const queued = self._updatesForUnknownStores[name];\n    if (Array.isArray(queued)) {\n      await store.beginUpdate(queued.length, false);\n      for (const msg of queued) {\n        await store.update(msg);\n      }\n      await store.endUpdate();\n      delete self._updatesForUnknownStores[name];\n    }\n\n    return true;\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.subscribe\n   * @summary Subscribe to a record set.  Returns a handle that provides\n   * `stop()` and `ready()` methods.\n   * @locus Client\n   * @param {String} name Name of the subscription.  Matches the name of the\n   * server's `publish()` call.\n   * @param {EJSONable} [arg1,arg2...] Optional arguments passed to publisher\n   * function on server.\n   * @param {Function|Object} [callbacks] Optional. May include `onStop`\n   * and `onReady` callbacks. If there is an error, it is passed as an\n   * argument to `onStop`. If a function is passed instead of an object, it\n   * is interpreted as an `onReady` callback.\n   */\n  subscribe(name /* .. [arguments] .. (callback|callbacks) */) {\n    const self = this;\n\n    const params = slice.call(arguments, 1);\n    let callbacks = Object.create(null);\n    if (params.length) {\n      const lastParam = params[params.length - 1];\n      if (typeof lastParam === 'function') {\n        callbacks.onReady = params.pop();\n      } else if (lastParam && [\n        lastParam.onReady,\n        // XXX COMPAT WITH ******* onError used to exist, but now we use\n        // onStop with an error callback instead.\n        lastParam.onError,\n        lastParam.onStop\n      ].some(f => typeof f === \"function\")) {\n        callbacks = params.pop();\n      }\n    }\n\n    // Is there an existing sub with the same name and param, run in an\n    // invalidated Computation? This will happen if we are rerunning an\n    // existing computation.\n    //\n    // For example, consider a rerun of:\n    //\n    //     Tracker.autorun(function () {\n    //       Meteor.subscribe(\"foo\", Session.get(\"foo\"));\n    //       Meteor.subscribe(\"bar\", Session.get(\"bar\"));\n    //     });\n    //\n    // If \"foo\" has changed but \"bar\" has not, we will match the \"bar\"\n    // subcribe to an existing inactive subscription in order to not\n    // unsub and resub the subscription unnecessarily.\n    //\n    // We only look for one such sub; if there are N apparently-identical subs\n    // being invalidated, we will require N matching subscribe calls to keep\n    // them all active.\n    const existing = Object.values(self._subscriptions).find(\n      sub => (sub.inactive && sub.name === name && EJSON.equals(sub.params, params))\n    );\n\n    let id;\n    if (existing) {\n      id = existing.id;\n      existing.inactive = false; // reactivate\n\n      if (callbacks.onReady) {\n        // If the sub is not already ready, replace any ready callback with the\n        // one provided now. (It's not really clear what users would expect for\n        // an onReady callback inside an autorun; the semantics we provide is\n        // that at the time the sub first becomes ready, we call the last\n        // onReady callback provided, if any.)\n        // If the sub is already ready, run the ready callback right away.\n        // It seems that users would expect an onReady callback inside an\n        // autorun to trigger once the sub first becomes ready and also\n        // when re-subs happens.\n        if (existing.ready) {\n          callbacks.onReady();\n        } else {\n          existing.readyCallback = callbacks.onReady;\n        }\n      }\n\n      // XXX COMPAT WITH ******* we used to have onError but now we call\n      // onStop with an optional error argument\n      if (callbacks.onError) {\n        // Replace existing callback if any, so that errors aren't\n        // double-reported.\n        existing.errorCallback = callbacks.onError;\n      }\n\n      if (callbacks.onStop) {\n        existing.stopCallback = callbacks.onStop;\n      }\n    } else {\n      // New sub! Generate an id, save it locally, and send message.\n      id = Random.id();\n      self._subscriptions[id] = {\n        id: id,\n        name: name,\n        params: EJSON.clone(params),\n        inactive: false,\n        ready: false,\n        readyDeps: new Tracker.Dependency(),\n        readyCallback: callbacks.onReady,\n        // XXX COMPAT WITH ******* #errorCallback\n        errorCallback: callbacks.onError,\n        stopCallback: callbacks.onStop,\n        connection: self,\n        remove() {\n          delete this.connection._subscriptions[this.id];\n          this.ready && this.readyDeps.changed();\n        },\n        stop() {\n          this.connection._sendQueued({ msg: 'unsub', id: id });\n          this.remove();\n\n          if (callbacks.onStop) {\n            callbacks.onStop();\n          }\n        }\n      };\n      self._send({ msg: 'sub', id: id, name: name, params: params });\n    }\n\n    // return a handle to the application.\n    const handle = {\n      stop() {\n        if (! hasOwn.call(self._subscriptions, id)) {\n          return;\n        }\n        self._subscriptions[id].stop();\n      },\n      ready() {\n        // return false if we've unsubscribed.\n        if (!hasOwn.call(self._subscriptions, id)) {\n          return false;\n        }\n        const record = self._subscriptions[id];\n        record.readyDeps.depend();\n        return record.ready;\n      },\n      subscriptionId: id\n    };\n\n    if (Tracker.active) {\n      // We're in a reactive computation, so we'd like to unsubscribe when the\n      // computation is invalidated... but not if the rerun just re-subscribes\n      // to the same subscription!  When a rerun happens, we use onInvalidate\n      // as a change to mark the subscription \"inactive\" so that it can\n      // be reused from the rerun.  If it isn't reused, it's killed from\n      // an afterFlush.\n      Tracker.onInvalidate((c) => {\n        if (hasOwn.call(self._subscriptions, id)) {\n          self._subscriptions[id].inactive = true;\n        }\n\n        Tracker.afterFlush(() => {\n          if (hasOwn.call(self._subscriptions, id) &&\n              self._subscriptions[id].inactive) {\n            handle.stop();\n          }\n        });\n      });\n    }\n\n    return handle;\n  }\n\n  /**\n   * @summary Tells if the method call came from a call or a callAsync.\n   * @alias Meteor.isAsyncCall\n   * @locus Anywhere\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @returns boolean\n   */\n  isAsyncCall(){\n    return DDP._CurrentMethodInvocation._isCallAsyncMethodRunning()\n  }\n  methods(methods) {\n    Object.entries(methods).forEach(([name, func]) => {\n      if (typeof func !== 'function') {\n        throw new Error(\"Method '\" + name + \"' must be a function\");\n      }\n      if (this._methodHandlers[name]) {\n        throw new Error(\"A method named '\" + name + \"' is already defined\");\n      }\n      this._methodHandlers[name] = func;\n    });\n  }\n\n  _getIsSimulation({isFromCallAsync, alreadyInSimulation}) {\n    if (!isFromCallAsync) {\n      return alreadyInSimulation;\n    }\n    return alreadyInSimulation && DDP._CurrentMethodInvocation._isCallAsyncMethodRunning();\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.call\n   * @summary Invokes a method with a sync stub, passing any number of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable} [arg1,arg2...] Optional method arguments\n   * @param {Function} [asyncCallback] Optional callback, which is called asynchronously with the error or result after the method is complete. If not provided, the method runs synchronously if possible (see below).\n   */\n  call(name /* .. [arguments] .. callback */) {\n    // if it's a function, the last argument is the result callback,\n    // not a parameter to the remote method.\n    const args = slice.call(arguments, 1);\n    let callback;\n    if (args.length && typeof args[args.length - 1] === 'function') {\n      callback = args.pop();\n    }\n    return this.apply(name, args, callback);\n  }\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.callAsync\n   * @summary Invokes a method with an async stub, passing any number of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable} [arg1,arg2...] Optional method arguments\n   * @returns {Promise}\n   */\n  callAsync(name /* .. [arguments] .. */) {\n    const args = slice.call(arguments, 1);\n    if (args.length && typeof args[args.length - 1] === 'function') {\n      throw new Error(\n        \"Meteor.callAsync() does not accept a callback. You should 'await' the result, or use .then().\"\n      );\n    }\n\n    return this.applyAsync(name, args, { returnServerResultPromise: true });\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.apply\n   * @summary Invoke a method passing an array of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable[]} args Method arguments\n   * @param {Object} [options]\n   * @param {Boolean} options.wait (Client only) If true, don't send this method until all previous method calls have completed, and don't send any subsequent method calls until this one is completed.\n   * @param {Function} options.onResultReceived (Client only) This callback is invoked with the error or result of the method (just like `asyncCallback`) as soon as the error or result is available. The local cache may not yet reflect the writes performed by the method.\n   * @param {Boolean} options.noRetry (Client only) if true, don't send this method again on reload, simply call the callback an error with the error code 'invocation-failed'.\n   * @param {Boolean} options.throwStubExceptions (Client only) If true, exceptions thrown by method stubs will be thrown instead of logged, and the method will not be invoked on the server.\n   * @param {Boolean} options.returnStubValue (Client only) If true then in cases where we would have otherwise discarded the stub's return value and returned undefined, instead we go ahead and return it. Specifically, this is any time other than when (a) we are already inside a stub or (b) we are in Node and no callback was provided. Currently we require this flag to be explicitly passed to reduce the likelihood that stub return values will be confused with server return values; we may improve this in future.\n   * @param {Function} [asyncCallback] Optional callback; same semantics as in [`Meteor.call`](#meteor_call).\n   */\n  apply(name, args, options, callback) {\n    const { stubInvocation, invocation, ...stubOptions } = this._stubCall(name, EJSON.clone(args));\n\n    if (stubOptions.hasStub) {\n      if (\n        !this._getIsSimulation({\n          alreadyInSimulation: stubOptions.alreadyInSimulation,\n          isFromCallAsync: stubOptions.isFromCallAsync,\n        })\n      ) {\n        this._saveOriginals();\n      }\n      try {\n        stubOptions.stubReturnValue = DDP._CurrentMethodInvocation\n          .withValue(invocation, stubInvocation);\n        if (Meteor._isPromise(stubOptions.stubReturnValue)) {\n          Meteor._debug(\n            `Method ${name}: Calling a method that has an async method stub with call/apply can lead to unexpected behaviors. Use callAsync/applyAsync instead.`\n          );\n        }\n      } catch (e) {\n        stubOptions.exception = e;\n      }\n    }\n    return this._apply(name, stubOptions, args, options, callback);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.applyAsync\n   * @summary Invoke a method passing an array of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable[]} args Method arguments\n   * @param {Object} [options]\n   * @param {Boolean} options.wait (Client only) If true, don't send this method until all previous method calls have completed, and don't send any subsequent method calls until this one is completed.\n   * @param {Function} options.onResultReceived (Client only) This callback is invoked with the error or result of the method (just like `asyncCallback`) as soon as the error or result is available. The local cache may not yet reflect the writes performed by the method.\n   * @param {Boolean} options.noRetry (Client only) if true, don't send this method again on reload, simply call the callback an error with the error code 'invocation-failed'.\n   * @param {Boolean} options.throwStubExceptions (Client only) If true, exceptions thrown by method stubs will be thrown instead of logged, and the method will not be invoked on the server.\n   * @param {Boolean} options.returnStubValue (Client only) If true then in cases where we would have otherwise discarded the stub's return value and returned undefined, instead we go ahead and return it. Specifically, this is any time other than when (a) we are already inside a stub or (b) we are in Node and no callback was provided. Currently we require this flag to be explicitly passed to reduce the likelihood that stub return values will be confused with server return values; we may improve this in future.\n   * @param {Boolean} options.returnServerResultPromise (Client only) If true, the promise returned by applyAsync will resolve to the server's return value, rather than the stub's return value. This is useful when you want to ensure that the server's return value is used, even if the stub returns a promise. The same behavior as `callAsync`.\n   */\n  applyAsync(name, args, options, callback = null) {\n    const stubPromise = this._applyAsyncStubInvocation(name, args, options);\n\n    const promise = this._applyAsync({\n      name,\n      args,\n      options,\n      callback,\n      stubPromise,\n    });\n    if (Meteor.isClient) {\n      // only return the stubReturnValue\n      promise.stubPromise = stubPromise.then(o => {\n        if (o.exception) {\n          throw o.exception;\n        }\n        return o.stubReturnValue;\n      });\n      // this avoids attribute recursion\n      promise.serverPromise = new Promise((resolve, reject) =>\n        promise.then(resolve).catch(reject),\n      );\n    }\n    return promise;\n  }\n  async _applyAsyncStubInvocation(name, args, options) {\n    const { stubInvocation, invocation, ...stubOptions } = this._stubCall(name, EJSON.clone(args), options);\n    if (stubOptions.hasStub) {\n      if (\n        !this._getIsSimulation({\n          alreadyInSimulation: stubOptions.alreadyInSimulation,\n          isFromCallAsync: stubOptions.isFromCallAsync,\n        })\n      ) {\n        this._saveOriginals();\n      }\n      try {\n        /*\n         * The code below follows the same logic as the function withValues().\n         *\n         * But as the Meteor package is not compiled by ecmascript, it is unable to use newer syntax in the browser,\n         * such as, the async/await.\n         *\n         * So, to keep supporting old browsers, like IE 11, we're creating the logic one level above.\n         */\n        const currentContext = DDP._CurrentMethodInvocation._setNewContextAndGetCurrent(\n          invocation\n        );\n        try {\n          stubOptions.stubReturnValue = await stubInvocation();\n        } catch (e) {\n          stubOptions.exception = e;\n        } finally {\n          DDP._CurrentMethodInvocation._set(currentContext);\n        }\n      } catch (e) {\n        stubOptions.exception = e;\n      }\n    }\n    return stubOptions;\n  }\n  async _applyAsync({ name, args, options, callback, stubPromise }) {\n    const stubOptions = await stubPromise;\n    return this._apply(name, stubOptions, args, options, callback);\n  }\n\n  _apply(name, stubCallValue, args, options, callback) {\n    const self = this;\n\n    // We were passed 3 arguments. They may be either (name, args, options)\n    // or (name, args, callback)\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = Object.create(null);\n    }\n    options = options || Object.create(null);\n\n    if (callback) {\n      // XXX would it be better form to do the binding in stream.on,\n      // or caller, instead of here?\n      // XXX improve error message (and how we report it)\n      callback = Meteor.bindEnvironment(\n        callback,\n        \"delivering result of invoking '\" + name + \"'\"\n      );\n    }\n    const {\n      hasStub,\n      exception,\n      stubReturnValue,\n      alreadyInSimulation,\n      randomSeed,\n    } = stubCallValue;\n\n    // Keep our args safe from mutation (eg if we don't send the message for a\n    // while because of a wait method).\n    args = EJSON.clone(args);\n    // If we're in a simulation, stop and return the result we have,\n    // rather than going on to do an RPC. If there was no stub,\n    // we'll end up returning undefined.\n    if (\n      this._getIsSimulation({\n        alreadyInSimulation,\n        isFromCallAsync: stubCallValue.isFromCallAsync,\n      })\n    ) {\n      let result;\n\n      if (callback) {\n        callback(exception, stubReturnValue);\n      } else {\n        if (exception) throw exception;\n        result = stubReturnValue;\n      }\n\n      return options._returnMethodInvoker ? { result } : result;\n    }\n\n    // We only create the methodId here because we don't actually need one if\n    // we're already in a simulation\n    const methodId = '' + self._nextMethodId++;\n    if (hasStub) {\n      self._retrieveAndStoreOriginals(methodId);\n    }\n\n    // Generate the DDP message for the method call. Note that on the client,\n    // it is important that the stub have finished before we send the RPC, so\n    // that we know we have a complete list of which local documents the stub\n    // wrote.\n    const message = {\n      msg: 'method',\n      id: methodId,\n      method: name,\n      params: args\n    };\n\n    // If an exception occurred in a stub, and we're ignoring it\n    // because we're doing an RPC and want to use what the server\n    // returns instead, log it so the developer knows\n    // (unless they explicitly ask to see the error).\n    //\n    // Tests can set the '_expectedByTest' flag on an exception so it won't\n    // go to log.\n    if (exception) {\n      if (options.throwStubExceptions) {\n        throw exception;\n      } else if (!exception._expectedByTest) {\n        Meteor._debug(\n          \"Exception while simulating the effect of invoking '\" + name + \"'\",\n          exception\n        );\n      }\n    }\n\n    // At this point we're definitely doing an RPC, and we're going to\n    // return the value of the RPC to the caller.\n\n    // If the caller didn't give a callback, decide what to do.\n    let promise;\n    if (!callback) {\n      if (\n        Meteor.isClient &&\n        !options.returnServerResultPromise &&\n        (!options.isFromCallAsync || options.returnStubValue)\n      ) {\n        callback = (err) => {\n          err && Meteor._debug(\"Error invoking Method '\" + name + \"'\", err);\n        };\n      } else {\n        promise = new Promise((resolve, reject) => {\n          callback = (...allArgs) => {\n            let args = Array.from(allArgs);\n            let err = args.shift();\n            if (err) {\n              reject(err);\n              return;\n            }\n            resolve(...args);\n          };\n        });\n      }\n    }\n\n    // Send the randomSeed only if we used it\n    if (randomSeed.value !== null) {\n      message.randomSeed = randomSeed.value;\n    }\n\n    const methodInvoker = new MethodInvoker({\n      methodId,\n      callback: callback,\n      connection: self,\n      onResultReceived: options.onResultReceived,\n      wait: !!options.wait,\n      message: message,\n      noRetry: !!options.noRetry\n    });\n\n    let result;\n\n    if (promise) {\n      result = options.returnStubValue ? promise.then(() => stubReturnValue) : promise;\n    } else {\n      result = options.returnStubValue ? stubReturnValue : undefined;\n    }\n\n    if (options._returnMethodInvoker) {\n      return {\n        methodInvoker,\n        result,\n      };\n    }\n\n    self._addOutstandingMethod(methodInvoker, options);\n    return result;\n  }\n\n  _stubCall(name, args, options) {\n    // Run the stub, if we have one. The stub is supposed to make some\n    // temporary writes to the database to give the user a smooth experience\n    // until the actual result of executing the method comes back from the\n    // server (whereupon the temporary writes to the database will be reversed\n    // during the beginUpdate/endUpdate process.)\n    //\n    // Normally, we ignore the return value of the stub (even if it is an\n    // exception), in favor of the real return value from the server. The\n    // exception is if the *caller* is a stub. In that case, we're not going\n    // to do a RPC, so we use the return value of the stub as our return\n    // value.\n    const self = this;\n    const enclosing = DDP._CurrentMethodInvocation.get();\n    const stub = self._methodHandlers[name];\n    const alreadyInSimulation = enclosing?.isSimulation;\n    const isFromCallAsync = enclosing?._isFromCallAsync;\n    const randomSeed = { value: null};\n\n    const defaultReturn = {\n      alreadyInSimulation,\n      randomSeed,\n      isFromCallAsync,\n    };\n    if (!stub) {\n      return { ...defaultReturn, hasStub: false };\n    }\n\n    // Lazily generate a randomSeed, only if it is requested by the stub.\n    // The random streams only have utility if they're used on both the client\n    // and the server; if the client doesn't generate any 'random' values\n    // then we don't expect the server to generate any either.\n    // Less commonly, the server may perform different actions from the client,\n    // and may in fact generate values where the client did not, but we don't\n    // have any client-side values to match, so even here we may as well just\n    // use a random seed on the server.  In that case, we don't pass the\n    // randomSeed to save bandwidth, and we don't even generate it to save a\n    // bit of CPU and to avoid consuming entropy.\n\n    const randomSeedGenerator = () => {\n      if (randomSeed.value === null) {\n        randomSeed.value = DDPCommon.makeRpcSeed(enclosing, name);\n      }\n      return randomSeed.value;\n    };\n\n    const setUserId = userId => {\n      self.setUserId(userId);\n    };\n\n    const invocation = new DDPCommon.MethodInvocation({\n      name,\n      isSimulation: true,\n      userId: self.userId(),\n      isFromCallAsync: options?.isFromCallAsync,\n      setUserId: setUserId,\n      randomSeed() {\n        return randomSeedGenerator();\n      }\n    });\n\n    // Note that unlike in the corresponding server code, we never audit\n    // that stubs check() their arguments.\n    const stubInvocation = () => {\n        if (Meteor.isServer) {\n          // Because saveOriginals and retrieveOriginals aren't reentrant,\n          // don't allow stubs to yield.\n          return Meteor._noYieldsAllowed(() => {\n            // re-clone, so that the stub can't affect our caller's values\n            return stub.apply(invocation, EJSON.clone(args));\n          });\n        } else {\n          return stub.apply(invocation, EJSON.clone(args));\n        }\n    };\n    return { ...defaultReturn, hasStub: true, stubInvocation, invocation };\n  }\n\n  // Before calling a method stub, prepare all stores to track changes and allow\n  // _retrieveAndStoreOriginals to get the original versions of changed\n  // documents.\n  _saveOriginals() {\n    if (! this._waitingForQuiescence()) {\n      this._flushBufferedWrites();\n    }\n\n    Object.values(this._stores).forEach((store) => {\n      store.saveOriginals();\n    });\n  }\n\n  // Retrieves the original versions of all documents modified by the stub for\n  // method 'methodId' from all stores and saves them to _serverDocuments (keyed\n  // by document) and _documentsWrittenByStub (keyed by method ID).\n  _retrieveAndStoreOriginals(methodId) {\n    const self = this;\n    if (self._documentsWrittenByStub[methodId])\n      throw new Error('Duplicate methodId in _retrieveAndStoreOriginals');\n\n    const docsWritten = [];\n\n    Object.entries(self._stores).forEach(([collection, store]) => {\n      const originals = store.retrieveOriginals();\n      // not all stores define retrieveOriginals\n      if (! originals) return;\n      originals.forEach((doc, id) => {\n        docsWritten.push({ collection, id });\n        if (! hasOwn.call(self._serverDocuments, collection)) {\n          self._serverDocuments[collection] = new MongoIDMap();\n        }\n        const serverDoc = self._serverDocuments[collection].setDefault(\n          id,\n          Object.create(null)\n        );\n        if (serverDoc.writtenByStubs) {\n          // We're not the first stub to write this doc. Just add our method ID\n          // to the record.\n          serverDoc.writtenByStubs[methodId] = true;\n        } else {\n          // First stub! Save the original value and our method ID.\n          serverDoc.document = doc;\n          serverDoc.flushCallbacks = [];\n          serverDoc.writtenByStubs = Object.create(null);\n          serverDoc.writtenByStubs[methodId] = true;\n        }\n      });\n    });\n    if (! isEmpty(docsWritten)) {\n      self._documentsWrittenByStub[methodId] = docsWritten;\n    }\n  }\n\n  // This is very much a private function we use to make the tests\n  // take up fewer server resources after they complete.\n  _unsubscribeAll() {\n    Object.values(this._subscriptions).forEach((sub) => {\n      // Avoid killing the autoupdate subscription so that developers\n      // still get hot code pushes when writing tests.\n      //\n      // XXX it's a hack to encode knowledge about autoupdate here,\n      // but it doesn't seem worth it yet to have a special API for\n      // subscriptions to preserve after unit tests.\n      if (sub.name !== 'meteor_autoupdate_clientVersions') {\n        sub.stop();\n      }\n    });\n  }\n\n  // Sends the DDP stringification of the given message object\n  _send(obj) {\n    this._stream.send(DDPCommon.stringifyDDP(obj));\n  }\n\n  // Always queues the call before sending the message\n  // Used, for example, on subscription.[id].stop() to make sure a \"sub\" message is always called before an \"unsub\" message\n  // https://github.com/meteor/meteor/issues/13212\n  //\n  // This is part of the actual fix for the rest check:\n  // https://github.com/meteor/meteor/pull/13236\n  _sendQueued(obj) {\n    this._send(obj, true);\n  }\n\n  // We detected via DDP-level heartbeats that we've lost the\n  // connection.  Unlike `disconnect` or `close`, a lost connection\n  // will be automatically retried.\n  _lostConnection(error) {\n    this._stream._lostConnection(error);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.status\n   * @summary Get the current connection status. A reactive data source.\n   * @locus Client\n   */\n  status(...args) {\n    return this._stream.status(...args);\n  }\n\n  /**\n   * @summary Force an immediate reconnection attempt if the client is not connected to the server.\n\n  This method does nothing if the client is already connected.\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.reconnect\n   * @locus Client\n   */\n  reconnect(...args) {\n    return this._stream.reconnect(...args);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.disconnect\n   * @summary Disconnect the client from the server.\n   * @locus Client\n   */\n  disconnect(...args) {\n    return this._stream.disconnect(...args);\n  }\n\n  close() {\n    return this._stream.disconnect({ _permanent: true });\n  }\n\n  ///\n  /// Reactive user system\n  ///\n  userId() {\n    if (this._userIdDeps) this._userIdDeps.depend();\n    return this._userId;\n  }\n\n  setUserId(userId) {\n    // Avoid invalidating dependents if setUserId is called with current value.\n    if (this._userId === userId) return;\n    this._userId = userId;\n    if (this._userIdDeps) this._userIdDeps.changed();\n  }\n\n  // Returns true if we are in a state after reconnect of waiting for subs to be\n  // revived or early methods to finish their data, or we are waiting for a\n  // \"wait\" method to finish.\n  _waitingForQuiescence() {\n    return (\n      ! isEmpty(this._subsBeingRevived) ||\n      ! isEmpty(this._methodsBlockingQuiescence)\n    );\n  }\n\n  // Returns true if any method whose message has been sent to the server has\n  // not yet invoked its user callback.\n  _anyMethodsAreOutstanding() {\n    const invokers = this._methodInvokers;\n    return Object.values(invokers).some((invoker) => !!invoker.sentMessage);\n  }\n\n  async _processOneDataMessage(msg, updates) {\n    const messageType = msg.msg;\n\n    // msg is one of ['added', 'changed', 'removed', 'ready', 'updated']\n    if (messageType === 'added') {\n      await this._process_added(msg, updates);\n    } else if (messageType === 'changed') {\n      this._process_changed(msg, updates);\n    } else if (messageType === 'removed') {\n      this._process_removed(msg, updates);\n    } else if (messageType === 'ready') {\n      this._process_ready(msg, updates);\n    } else if (messageType === 'updated') {\n      this._process_updated(msg, updates);\n    } else if (messageType === 'nosub') {\n      // ignore this\n    } else {\n      Meteor._debug('discarding unknown livedata data message type', msg);\n    }\n  }\n\n  _prepareBuffersToFlush() {\n    const self = this;\n    if (self._bufferedWritesFlushHandle) {\n      clearTimeout(self._bufferedWritesFlushHandle);\n      self._bufferedWritesFlushHandle = null;\n    }\n\n    self._bufferedWritesFlushAt = null;\n    // We need to clear the buffer before passing it to\n    //  performWrites. As there's no guarantee that it\n    //  will exit cleanly.\n    const writes = self._bufferedWrites;\n    self._bufferedWrites = Object.create(null);\n    return writes;\n  }\n\n  /**\n   * Server-side store updates handled asynchronously\n   * @private\n   */\n  async _performWritesServer(updates) {\n    const self = this;\n\n    if (self._resetStores || !isEmpty(updates)) {\n      // Start all store updates - keeping original loop structure\n      for (const store of Object.values(self._stores)) {\n        await store.beginUpdate(\n          updates[store._name]?.length || 0,\n          self._resetStores\n        );\n      }\n\n      self._resetStores = false;\n\n      // Process each store's updates sequentially as before\n      for (const [storeName, messages] of Object.entries(updates)) {\n        const store = self._stores[storeName];\n        if (store) {\n          // Batch each store's messages in modest chunks to prevent event loop blocking\n          // while maintaining operation order\n          const CHUNK_SIZE = 100;\n          for (let i = 0; i < messages.length; i += CHUNK_SIZE) {\n            const chunk = messages.slice(i, Math.min(i + CHUNK_SIZE, messages.length));\n\n            for (const msg of chunk) {\n              await store.update(msg);\n            }\n\n            await new Promise(resolve => process.nextTick(resolve));\n          }\n        } else {\n          // Queue updates for uninitialized stores\n          self._updatesForUnknownStores[storeName] =\n            self._updatesForUnknownStores[storeName] || [];\n          self._updatesForUnknownStores[storeName].push(...messages);\n        }\n      }\n\n      // Complete all updates\n      for (const store of Object.values(self._stores)) {\n        await store.endUpdate();\n      }\n    }\n\n    self._runAfterUpdateCallbacks();\n  }\n\n  /**\n   * Client-side store updates handled synchronously for optimistic UI\n   * @private\n   */\n  _performWritesClient(updates) {\n    const self = this;\n\n    if (self._resetStores || !isEmpty(updates)) {\n      // Synchronous store updates for client\n      Object.values(self._stores).forEach(store => {\n        store.beginUpdate(\n          updates[store._name]?.length || 0,\n          self._resetStores\n        );\n      });\n\n      self._resetStores = false;\n\n      Object.entries(updates).forEach(([storeName, messages]) => {\n        const store = self._stores[storeName];\n        if (store) {\n          messages.forEach(msg => store.update(msg));\n        } else {\n          self._updatesForUnknownStores[storeName] =\n            self._updatesForUnknownStores[storeName] || [];\n          self._updatesForUnknownStores[storeName].push(...messages);\n        }\n      });\n\n      Object.values(self._stores).forEach(store => store.endUpdate());\n    }\n\n    self._runAfterUpdateCallbacks();\n  }\n\n  /**\n   * Executes buffered writes either synchronously (client) or async (server)\n   * @private\n   */\n  async _flushBufferedWrites() {\n    const self = this;\n    const writes = self._prepareBuffersToFlush();\n\n    return Meteor.isClient\n      ? self._performWritesClient(writes)\n      : self._performWritesServer(writes);\n  }\n\n  // Call any callbacks deferred with _runWhenAllServerDocsAreFlushed whose\n  // relevant docs have been flushed, as well as dataVisible callbacks at\n  // reconnect-quiescence time.\n  _runAfterUpdateCallbacks() {\n    const self = this;\n    const callbacks = self._afterUpdateCallbacks;\n    self._afterUpdateCallbacks = [];\n    callbacks.forEach((c) => {\n      c();\n    });\n  }\n\n  // Ensures that \"f\" will be called after all documents currently in\n  // _serverDocuments have been written to the local cache. f will not be called\n  // if the connection is lost before then!\n  _runWhenAllServerDocsAreFlushed(f) {\n    const self = this;\n    const runFAfterUpdates = () => {\n      self._afterUpdateCallbacks.push(f);\n    };\n    let unflushedServerDocCount = 0;\n    const onServerDocFlush = () => {\n      --unflushedServerDocCount;\n      if (unflushedServerDocCount === 0) {\n        // This was the last doc to flush! Arrange to run f after the updates\n        // have been applied.\n        runFAfterUpdates();\n      }\n    };\n\n    Object.values(self._serverDocuments).forEach((serverDocuments) => {\n      serverDocuments.forEach((serverDoc) => {\n        const writtenByStubForAMethodWithSentMessage =\n          keys(serverDoc.writtenByStubs).some(methodId => {\n            const invoker = self._methodInvokers[methodId];\n            return invoker && invoker.sentMessage;\n          });\n\n        if (writtenByStubForAMethodWithSentMessage) {\n          ++unflushedServerDocCount;\n          serverDoc.flushCallbacks.push(onServerDocFlush);\n        }\n      });\n    });\n    if (unflushedServerDocCount === 0) {\n      // There aren't any buffered docs --- we can call f as soon as the current\n      // round of updates is applied!\n      runFAfterUpdates();\n    }\n  }\n\n  _addOutstandingMethod(methodInvoker, options) {\n    if (options?.wait) {\n      // It's a wait method! Wait methods go in their own block.\n      this._outstandingMethodBlocks.push({\n        wait: true,\n        methods: [methodInvoker]\n      });\n    } else {\n      // Not a wait method. Start a new block if the previous block was a wait\n      // block, and add it to the last block of methods.\n      if (isEmpty(this._outstandingMethodBlocks) ||\n          last(this._outstandingMethodBlocks).wait) {\n        this._outstandingMethodBlocks.push({\n          wait: false,\n          methods: [],\n        });\n      }\n\n      last(this._outstandingMethodBlocks).methods.push(methodInvoker);\n    }\n\n    // If we added it to the first block, send it out now.\n    if (this._outstandingMethodBlocks.length === 1) {\n      methodInvoker.sendMessage();\n    }\n  }\n\n  // Called by MethodInvoker after a method's callback is invoked.  If this was\n  // the last outstanding method in the current block, runs the next block. If\n  // there are no more methods, consider accepting a hot code push.\n  _outstandingMethodFinished() {\n    const self = this;\n    if (self._anyMethodsAreOutstanding()) return;\n\n    // No methods are outstanding. This should mean that the first block of\n    // methods is empty. (Or it might not exist, if this was a method that\n    // half-finished before disconnect/reconnect.)\n    if (! isEmpty(self._outstandingMethodBlocks)) {\n      const firstBlock = self._outstandingMethodBlocks.shift();\n      if (! isEmpty(firstBlock.methods))\n        throw new Error(\n          'No methods outstanding but nonempty block: ' +\n            JSON.stringify(firstBlock)\n        );\n\n      // Send the outstanding methods now in the first block.\n      if (! isEmpty(self._outstandingMethodBlocks))\n        self._sendOutstandingMethods();\n    }\n\n    // Maybe accept a hot code push.\n    self._maybeMigrate();\n  }\n\n  // Sends messages for all the methods in the first block in\n  // _outstandingMethodBlocks.\n  _sendOutstandingMethods() {\n    const self = this;\n\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      return;\n    }\n\n    self._outstandingMethodBlocks[0].methods.forEach(m => {\n      m.sendMessage();\n    });\n  }\n\n  _sendOutstandingMethodBlocksMessages(oldOutstandingMethodBlocks) {\n    const self = this;\n    if (isEmpty(oldOutstandingMethodBlocks)) return;\n\n    // We have at least one block worth of old outstanding methods to try\n    // again. First: did onReconnect actually send anything? If not, we just\n    // restore all outstanding methods and run the first block.\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      self._outstandingMethodBlocks = oldOutstandingMethodBlocks;\n      self._sendOutstandingMethods();\n      return;\n    }\n\n    // OK, there are blocks on both sides. Special case: merge the last block of\n    // the reconnect methods with the first block of the original methods, if\n    // neither of them are \"wait\" blocks.\n    if (\n      !last(self._outstandingMethodBlocks).wait &&\n      !oldOutstandingMethodBlocks[0].wait\n    ) {\n      oldOutstandingMethodBlocks[0].methods.forEach((m) => {\n        last(self._outstandingMethodBlocks).methods.push(m);\n\n        // If this \"last block\" is also the first block, send the message.\n        if (self._outstandingMethodBlocks.length === 1) {\n          m.sendMessage();\n        }\n      });\n\n      oldOutstandingMethodBlocks.shift();\n    }\n\n    // Now add the rest of the original blocks on.\n    self._outstandingMethodBlocks.push(...oldOutstandingMethodBlocks);\n  }\n\n  _callOnReconnectAndSendAppropriateOutstandingMethods() {\n    const self = this;\n    const oldOutstandingMethodBlocks = self._outstandingMethodBlocks;\n    self._outstandingMethodBlocks = [];\n\n    self.onReconnect && self.onReconnect();\n    DDP._reconnectHook.each((callback) => {\n      callback(self);\n      return true;\n    });\n\n    self._sendOutstandingMethodBlocksMessages(oldOutstandingMethodBlocks);\n  }\n\n  // We can accept a hot code push if there are no methods in flight.\n  _readyToMigrate() {\n    return isEmpty(this._methodInvokers);\n  }\n\n  // If we were blocking a migration, see if it's now possible to continue.\n  // Call whenever the set of outstanding/blocked methods shrinks.\n  _maybeMigrate() {\n    const self = this;\n    if (self._retryMigrate && self._readyToMigrate()) {\n      self._retryMigrate();\n      self._retryMigrate = null;\n    }\n  }\n}\n", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\nimport { DDP } from './namespace.js';\nimport { EJSON } from 'meteor/ejson';\nimport { isEmpty, hasOwn } from \"meteor/ddp-common/utils\";\n\nexport class MessageProcessors {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * @summary Process the connection message and set up the session\n   * @param {Object} msg The connection message\n   */\n  async _livedata_connected(msg) {\n    const self = this._connection;\n\n    if (self._version !== 'pre1' && self._heartbeatInterval !== 0) {\n      self._heartbeat = new DDPCommon.Heartbeat({\n        heartbeatInterval: self._heartbeatInterval,\n        heartbeatTimeout: self._heartbeatTimeout,\n        onTimeout() {\n          self._lostConnection(\n            new DDP.ConnectionError('DDP heartbeat timed out')\n          );\n        },\n        sendPing() {\n          self._send({ msg: 'ping' });\n        }\n      });\n      self._heartbeat.start();\n    }\n\n    // If this is a reconnect, we'll have to reset all stores.\n    if (self._lastSessionId) self._resetStores = true;\n\n    let reconnectedToPreviousSession;\n    if (typeof msg.session === 'string') {\n      reconnectedToPreviousSession = self._lastSessionId === msg.session;\n      self._lastSessionId = msg.session;\n    }\n\n    if (reconnectedToPreviousSession) {\n      // Successful reconnection -- pick up where we left off.\n      return;\n    }\n\n    // Server doesn't have our data anymore. Re-sync a new session.\n\n    // Forget about messages we were buffering for unknown collections. They'll\n    // be resent if still relevant.\n    self._updatesForUnknownStores = Object.create(null);\n\n    if (self._resetStores) {\n      // Forget about the effects of stubs. We'll be resetting all collections\n      // anyway.\n      self._documentsWrittenByStub = Object.create(null);\n      self._serverDocuments = Object.create(null);\n    }\n\n    // Clear _afterUpdateCallbacks.\n    self._afterUpdateCallbacks = [];\n\n    // Mark all named subscriptions which are ready as needing to be revived.\n    self._subsBeingRevived = Object.create(null);\n    Object.entries(self._subscriptions).forEach(([id, sub]) => {\n      if (sub.ready) {\n        self._subsBeingRevived[id] = true;\n      }\n    });\n\n    // Arrange for \"half-finished\" methods to have their callbacks run, and\n    // track methods that were sent on this connection so that we don't\n    // quiesce until they are all done.\n    //\n    // Start by clearing _methodsBlockingQuiescence: methods sent before\n    // reconnect don't matter, and any \"wait\" methods sent on the new connection\n    // that we drop here will be restored by the loop below.\n    self._methodsBlockingQuiescence = Object.create(null);\n    if (self._resetStores) {\n      const invokers = self._methodInvokers;\n      Object.keys(invokers).forEach(id => {\n        const invoker = invokers[id];\n        if (invoker.gotResult()) {\n          // This method already got its result, but it didn't call its callback\n          // because its data didn't become visible. We did not resend the\n          // method RPC. We'll call its callback when we get a full quiesce,\n          // since that's as close as we'll get to \"data must be visible\".\n          self._afterUpdateCallbacks.push(\n            (...args) => invoker.dataVisible(...args)\n          );\n        } else if (invoker.sentMessage) {\n          // This method has been sent on this connection (maybe as a resend\n          // from the last connection, maybe from onReconnect, maybe just very\n          // quickly before processing the connected message).\n          //\n          // We don't need to do anything special to ensure its callbacks get\n          // called, but we'll count it as a method which is preventing\n          // reconnect quiescence. (eg, it might be a login method that was run\n          // from onReconnect, and we don't want to see flicker by seeing a\n          // logged-out state.)\n          self._methodsBlockingQuiescence[invoker.methodId] = true;\n        }\n      });\n    }\n\n    self._messagesBufferedUntilQuiescence = [];\n\n    // If we're not waiting on any methods or subs, we can reset the stores and\n    // call the callbacks immediately.\n    if (!self._waitingForQuiescence()) {\n      if (self._resetStores) {\n        for (const store of Object.values(self._stores)) {\n          await store.beginUpdate(0, true);\n          await store.endUpdate();\n        }\n        self._resetStores = false;\n      }\n      self._runAfterUpdateCallbacks();\n    }\n  }\n\n  /**\n   * @summary Process various data messages from the server\n   * @param {Object} msg The data message\n   */\n  async _livedata_data(msg) {\n    const self = this._connection;\n\n    if (self._waitingForQuiescence()) {\n      self._messagesBufferedUntilQuiescence.push(msg);\n\n      if (msg.msg === 'nosub') {\n        delete self._subsBeingRevived[msg.id];\n      }\n\n      if (msg.subs) {\n        msg.subs.forEach(subId => {\n          delete self._subsBeingRevived[subId];\n        });\n      }\n\n      if (msg.methods) {\n        msg.methods.forEach(methodId => {\n          delete self._methodsBlockingQuiescence[methodId];\n        });\n      }\n\n      if (self._waitingForQuiescence()) {\n        return;\n      }\n\n      // No methods or subs are blocking quiescence!\n      // We'll now process and all of our buffered messages, reset all stores,\n      // and apply them all at once.\n      const bufferedMessages = self._messagesBufferedUntilQuiescence;\n      for (const bufferedMessage of Object.values(bufferedMessages)) {\n        await this._processOneDataMessage(\n          bufferedMessage,\n          self._bufferedWrites\n        );\n      }\n      self._messagesBufferedUntilQuiescence = [];\n    } else {\n      await this._processOneDataMessage(msg, self._bufferedWrites);\n    }\n\n    // Immediately flush writes when:\n    //  1. Buffering is disabled. Or;\n    //  2. any non-(added/changed/removed) message arrives.\n    const standardWrite =\n      msg.msg === \"added\" ||\n      msg.msg === \"changed\" ||\n      msg.msg === \"removed\";\n\n    if (self._bufferedWritesInterval === 0 || !standardWrite) {\n      await self._flushBufferedWrites();\n      return;\n    }\n\n    if (self._bufferedWritesFlushAt === null) {\n      self._bufferedWritesFlushAt =\n        new Date().valueOf() + self._bufferedWritesMaxAge;\n    } else if (self._bufferedWritesFlushAt < new Date().valueOf()) {\n      await self._flushBufferedWrites();\n      return;\n    }\n\n    if (self._bufferedWritesFlushHandle) {\n      clearTimeout(self._bufferedWritesFlushHandle);\n    }\n    self._bufferedWritesFlushHandle = setTimeout(() => {\n      self._liveDataWritesPromise = self._flushBufferedWrites();\n      if (Meteor._isPromise(self._liveDataWritesPromise)) {\n        self._liveDataWritesPromise.finally(\n          () => (self._liveDataWritesPromise = undefined)\n        );\n      }\n    }, self._bufferedWritesInterval);\n  }\n\n  /**\n   * @summary Process individual data messages by type\n   * @private\n   */\n  async _processOneDataMessage(msg, updates) {\n    const messageType = msg.msg;\n\n    switch (messageType) {\n      case 'added':\n        await this._connection._process_added(msg, updates);\n        break;\n      case 'changed':\n        this._connection._process_changed(msg, updates);\n        break;\n      case 'removed':\n        this._connection._process_removed(msg, updates);\n        break;\n      case 'ready':\n        this._connection._process_ready(msg, updates);\n        break;\n      case 'updated':\n        this._connection._process_updated(msg, updates);\n        break;\n      case 'nosub':\n        // ignore this\n        break;\n      default:\n        Meteor._debug('discarding unknown livedata data message type', msg);\n    }\n  }\n\n  /**\n   * @summary Handle method results arriving from the server\n   * @param {Object} msg The method result message\n   */\n  async _livedata_result(msg) {\n    const self = this._connection;\n\n    // Lets make sure there are no buffered writes before returning result.\n    if (!isEmpty(self._bufferedWrites)) {\n      await self._flushBufferedWrites();\n    }\n\n    // find the outstanding request\n    // should be O(1) in nearly all realistic use cases\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      Meteor._debug('Received method result but no methods outstanding');\n      return;\n    }\n    const currentMethodBlock = self._outstandingMethodBlocks[0].methods;\n    let i;\n    const m = currentMethodBlock.find((method, idx) => {\n      const found = method.methodId === msg.id;\n      if (found) i = idx;\n      return found;\n    });\n    if (!m) {\n      Meteor._debug(\"Can't match method response to original method call\", msg);\n      return;\n    }\n\n    // Remove from current method block. This may leave the block empty, but we\n    // don't move on to the next block until the callback has been delivered, in\n    // _outstandingMethodFinished.\n    currentMethodBlock.splice(i, 1);\n\n    if (hasOwn.call(msg, 'error')) {\n      m.receiveResult(\n        new Meteor.Error(msg.error.error, msg.error.reason, msg.error.details)\n      );\n    } else {\n      // msg.result may be undefined if the method didn't return a value\n      m.receiveResult(undefined, msg.result);\n    }\n  }\n\n  /**\n   * @summary Handle \"nosub\" messages arriving from the server\n   * @param {Object} msg The nosub message\n   */\n  async _livedata_nosub(msg) {\n    const self = this._connection;\n\n    // First pass it through _livedata_data, which only uses it to help get\n    // towards quiescence.\n    await this._livedata_data(msg);\n\n    // Do the rest of our processing immediately, with no\n    // buffering-until-quiescence.\n\n    // we weren't subbed anyway, or we initiated the unsub.\n    if (!hasOwn.call(self._subscriptions, msg.id)) {\n      return;\n    }\n\n    // XXX COMPAT WITH ******* #errorCallback\n    const errorCallback = self._subscriptions[msg.id].errorCallback;\n    const stopCallback = self._subscriptions[msg.id].stopCallback;\n\n    self._subscriptions[msg.id].remove();\n\n    const meteorErrorFromMsg = msgArg => {\n      return (\n        msgArg &&\n        msgArg.error &&\n        new Meteor.Error(\n          msgArg.error.error,\n          msgArg.error.reason,\n          msgArg.error.details\n        )\n      );\n    };\n\n    // XXX COMPAT WITH ******* #errorCallback\n    if (errorCallback && msg.error) {\n      errorCallback(meteorErrorFromMsg(msg));\n    }\n\n    if (stopCallback) {\n      stopCallback(meteorErrorFromMsg(msg));\n    }\n  }\n\n  /**\n   * @summary Handle errors from the server\n   * @param {Object} msg The error message\n   */\n  _livedata_error(msg) {\n    Meteor._debug('Received error from server: ', msg.reason);\n    if (msg.offendingMessage) Meteor._debug('For: ', msg.offendingMessage);\n  }\n\n  // Document change message processors will be defined in a separate class\n}", "// A MethodInvoker manages sending a method to the server and calling the user's\n// callbacks. On construction, it registers itself in the connection's\n// _methodInvokers map; it removes itself once the method is fully finished and\n// the callback is invoked. This occurs when it has both received a result,\n// and the data written by it is fully visible.\nexport class MethodInvoker {\n  constructor(options) {\n    // Public (within this file) fields.\n    this.methodId = options.methodId;\n    this.sentMessage = false;\n\n    this._callback = options.callback;\n    this._connection = options.connection;\n    this._message = options.message;\n    this._onResultReceived = options.onResultReceived || (() => {});\n    this._wait = options.wait;\n    this.noRetry = options.noRetry;\n    this._methodResult = null;\n    this._dataVisible = false;\n\n    // Register with the connection.\n    this._connection._methodInvokers[this.methodId] = this;\n  }\n  // Sends the method message to the server. May be called additional times if\n  // we lose the connection and reconnect before receiving a result.\n  sendMessage() {\n    // This function is called before sending a method (including resending on\n    // reconnect). We should only (re)send methods where we don't already have a\n    // result!\n    if (this.gotResult())\n      throw new Error('sendingMethod is called on method with result');\n\n    // If we're re-sending it, it doesn't matter if data was written the first\n    // time.\n    this._dataVisible = false;\n    this.sentMessage = true;\n\n    // If this is a wait method, make all data messages be buffered until it is\n    // done.\n    if (this._wait)\n      this._connection._methodsBlockingQuiescence[this.methodId] = true;\n\n    // Actually send the message.\n    this._connection._send(this._message);\n  }\n  // Invoke the callback, if we have both a result and know that all data has\n  // been written to the local cache.\n  _maybeInvokeCallback() {\n    if (this._methodResult && this._dataVisible) {\n      // Call the callback. (This won't throw: the callback was wrapped with\n      // bindEnvironment.)\n      this._callback(this._methodResult[0], this._methodResult[1]);\n\n      // Forget about this method.\n      delete this._connection._methodInvokers[this.methodId];\n\n      // Let the connection know that this method is finished, so it can try to\n      // move on to the next block of methods.\n      this._connection._outstandingMethodFinished();\n    }\n  }\n  // Call with the result of the method from the server. Only may be called\n  // once; once it is called, you should not call sendMessage again.\n  // If the user provided an onResultReceived callback, call it immediately.\n  // Then invoke the main callback if data is also visible.\n  receiveResult(err, result) {\n    if (this.gotResult())\n      throw new Error('Methods should only receive results once');\n    this._methodResult = [err, result];\n    this._onResultReceived(err, result);\n    this._maybeInvokeCallback();\n  }\n  // Call this when all data written by the method is visible. This means that\n  // the method has returns its \"data is done\" message *AND* all server\n  // documents that are buffered at that time have been written to the local\n  // cache. Invokes the main callback if the result has been received.\n  dataVisible() {\n    this._dataVisible = true;\n    this._maybeInvokeCallback();\n  }\n  // True if receiveResult has been called.\n  gotResult() {\n    return !!this._methodResult;\n  }\n}\n", "import { MongoID } from 'meteor/mongo-id';\n\nexport class MongoIDMap extends IdMap {\n  constructor() {\n    super(MongoID.idStringify, MongoID.idParse);\n  }\n}", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\n\nimport { Connection } from './livedata_connection.js';\n\n// This array allows the `_allSubscriptionsReady` method below, which\n// is used by the `spiderable` package, to keep track of whether all\n// data is ready.\nconst allConnections = [];\n\n/**\n * @namespace DDP\n * @summary Namespace for DDP-related methods/classes.\n */\nexport const DDP = {};\n\n// This is private but it's used in a few places. accounts-base uses\n// it to get the current user. Meteor.setTimeout and friends clear\n// it. We can probably find a better way to factor this.\nDDP._CurrentMethodInvocation = new Meteor.EnvironmentVariable();\nDDP._CurrentPublicationInvocation = new Meteor.EnvironmentVariable();\n\n// XXX: Keep DDP._CurrentInvocation for backwards-compatibility.\nDDP._CurrentInvocation = DDP._CurrentMethodInvocation;\n\nDDP._CurrentCallAsyncInvocation = new Meteor.EnvironmentVariable();\n\n// This is passed into a weird `makeErrorType` function that expects its thing\n// to be a constructor\nfunction connectionErrorConstructor(message) {\n  this.message = message;\n}\n\nDDP.ConnectionError = Meteor.makeErrorType(\n  'DDP.ConnectionError',\n  connectionErrorConstructor\n);\n\nDDP.ForcedReconnectError = Meteor.makeErrorType(\n  'DDP.ForcedReconnectError',\n  () => {}\n);\n\n// Returns the named sequence of pseudo-random values.\n// The scope will be DDP._CurrentMethodInvocation.get(), so the stream will produce\n// consistent values for method calls on the client and server.\nDDP.randomStream = name => {\n  const scope = DDP._CurrentMethodInvocation.get();\n  return DDPCommon.RandomStream.get(scope, name);\n};\n\n// @param url {String} URL to Meteor app,\n//     e.g.:\n//     \"subdomain.meteor.com\",\n//     \"http://subdomain.meteor.com\",\n//     \"/\",\n//     \"ddp+sockjs://ddp--****-foo.meteor.com/sockjs\"\n\n/**\n * @summary Connect to the server of a different Meteor application to subscribe to its document sets and invoke its remote methods.\n * @locus Anywhere\n * @param {String} url The URL of another Meteor application.\n * @param {Object} [options]\n * @param {Boolean} options.reloadWithOutstanding is it OK to reload if there are outstanding methods?\n * @param {Object} options.headers extra headers to send on the websockets connection, for server-to-server DDP only\n * @param {Object} options._sockjsOptions Specifies options to pass through to the sockjs client\n * @param {Function} options.onDDPNegotiationVersionFailure callback when version negotiation fails.\n */\nDDP.connect = (url, options) => {\n  const ret = new Connection(url, options);\n  allConnections.push(ret); // hack. see below.\n  return ret;\n};\n\nDDP._reconnectHook = new Hook({ bindEnvironment: false });\n\n/**\n * @summary Register a function to call as the first step of\n * reconnecting. This function can call methods which will be executed before\n * any other outstanding methods. For example, this can be used to re-establish\n * the appropriate authentication context on the connection.\n * @locus Anywhere\n * @param {Function} callback The function to call. It will be called with a\n * single argument, the [connection object](#ddp_connect) that is reconnecting.\n */\nDDP.onReconnect = callback => DDP._reconnectHook.register(callback);\n\n// Hack for `spiderable` package: a way to see if the page is done\n// loading all the data it needs.\n//\nDDP._allSubscriptionsReady = () => allConnections.every(\n  conn => Object.values(conn._subscriptions).every(sub => sub.ready)\n);\n"]}