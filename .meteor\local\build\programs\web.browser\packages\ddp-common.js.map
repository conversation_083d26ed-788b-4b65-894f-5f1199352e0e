{"version": 3, "sources": ["meteor://💻app/packages/ddp-common/namespace.js", "meteor://💻app/packages/ddp-common/heartbeat.js", "meteor://💻app/packages/ddp-common/utils.js", "meteor://💻app/packages/ddp-common/method_invocation.js", "meteor://💻app/packages/ddp-common/random_stream.js"], "names": ["DDPCommon", "Heartbeat", "constructor", "options", "heartbeatInterval", "heartbeatTimeout", "_sendPing", "sendPing", "_onTimeout", "onTimeout", "_seenPacket", "_heartbeatIntervalHandle", "_heartbeatTimeoutHandle", "stop", "_clearHeartbeatIntervalTimer", "_clearHeartbeatTimeoutTimer", "start", "_startHeartbeatIntervalTimer", "Meteor", "setInterval", "_heartbeatIntervalFired", "_startHeartbeatTimeoutTimer", "setTimeout", "_heartbeatTimeoutFired", "clearInterval", "clearTimeout", "messageReceived", "module", "export", "hasOwn", "slice", "keys", "isEmpty", "last", "Object", "prototype", "hasOwnProperty", "Array", "obj", "isArray", "length", "key", "call", "array", "n", "guard", "Math", "max", "SUPPORTED_DDP_VERSIONS", "parseDDP", "stringMessage", "msg", "JSON", "parse", "e", "_debug", "fields", "cleared", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "undefined", "field", "EJSON", "_adjustTypesFromJSONValue", "stringifyDDP", "copy", "clone", "value", "push", "_adjustTypesToJSONValue", "id", "Error", "stringify", "MethodInvocation", "name", "isSimulation", "_unblock", "unblock", "_calledUnblock", "_isFromCallAsync", "isFromCallAsync", "userId", "_setUserId", "setUserId", "connection", "randomSeed", "randomStream", "fence", "RandomStream", "seed", "concat", "randomToken", "sequences", "create", "_sequence", "self", "sequence", "sequenceSeed", "i", "Random", "createWithSeeds", "apply", "hexString", "get", "scope", "insecure", "makeRpcSeed", "enclosing", "methodName", "stream"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,SAAS,GAAG,CAAC,CAAC,C;;;;;;;;;;;ACRd;AACA;AACA;AACA;AACA;AACA;;AAEAA,SAAS,CAACC,SAAS,GAAG,MAAMA,SAAS,CAAC;EACpCC,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACC,iBAAiB,GAAGD,OAAO,CAACC,iBAAiB;IAClD,IAAI,CAACC,gBAAgB,GAAGF,OAAO,CAACE,gBAAgB;IAChD,IAAI,CAACC,SAAS,GAAGH,OAAO,CAACI,QAAQ;IACjC,IAAI,CAACC,UAAU,GAAGL,OAAO,CAACM,SAAS;IACnC,IAAI,CAACC,WAAW,GAAG,KAAK;IAExB,IAAI,CAACC,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACC,uBAAuB,GAAG,IAAI;EACrC;EAEAC,IAAIA,CAAA,EAAG;IACL,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACC,2BAA2B,CAAC,CAAC;EACpC;EAEAC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACH,IAAI,CAAC,CAAC;IACX,IAAI,CAACI,4BAA4B,CAAC,CAAC;EACrC;EAEAA,4BAA4BA,CAAA,EAAG;IAC7B,IAAI,CAACN,wBAAwB,GAAGO,MAAM,CAACC,WAAW,CAChD,MAAM,IAAI,CAACC,uBAAuB,CAAC,CAAC,EACpC,IAAI,CAAChB,iBACP,CAAC;EACH;EAEAiB,2BAA2BA,CAAA,EAAG;IAC5B,IAAI,CAACT,uBAAuB,GAAGM,MAAM,CAACI,UAAU,CAC9C,MAAM,IAAI,CAACC,sBAAsB,CAAC,CAAC,EACnC,IAAI,CAAClB,gBACP,CAAC;EACH;EAEAS,4BAA4BA,CAAA,EAAG;IAC7B,IAAI,IAAI,CAACH,wBAAwB,EAAE;MACjCO,MAAM,CAACM,aAAa,CAAC,IAAI,CAACb,wBAAwB,CAAC;MACnD,IAAI,CAACA,wBAAwB,GAAG,IAAI;IACtC;EACF;EAEAI,2BAA2BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACH,uBAAuB,EAAE;MAChCM,MAAM,CAACO,YAAY,CAAC,IAAI,CAACb,uBAAuB,CAAC;MACjD,IAAI,CAACA,uBAAuB,GAAG,IAAI;IACrC;EACF;;EAEA;EACAQ,uBAAuBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAE,IAAI,CAACV,WAAW,IAAI,CAAE,IAAI,CAACE,uBAAuB,EAAE;MACxD,IAAI,CAACN,SAAS,CAAC,CAAC;MAChB;MACA,IAAI,CAACe,2BAA2B,CAAC,CAAC;IACpC;IACA,IAAI,CAACX,WAAW,GAAG,KAAK;EAC1B;;EAEA;EACA;EACAa,sBAAsBA,CAAA,EAAG;IACvB,IAAI,CAACX,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACJ,UAAU,CAAC,CAAC;EACnB;EAEAkB,eAAeA,CAAA,EAAG;IAChB;IACA;IACA,IAAI,CAAChB,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,IAAI,CAACE,uBAAuB,EAAE;MAChC,IAAI,CAACG,2BAA2B,CAAC,CAAC;IACpC;EACF;AACF,CAAC,C;;;;;;;;;;;ACxFD,YAAY;;AAAZY,MAAM,CAACC,MAAM,CAAC;EAACC,MAAM,EAACA,CAAA,KAAIA,MAAM;EAACC,KAAK,EAACA,CAAA,KAAIA,KAAK;EAACC,IAAI,EAACA,CAAA,KAAIA,IAAI;EAACC,OAAO,EAACA,CAAA,KAAIA,OAAO;EAACC,IAAI,EAACA,CAAA,KAAIA;AAAI,CAAC,CAAC;AAE3F,MAAMJ,MAAM,GAAGK,MAAM,CAACC,SAAS,CAACC,cAAc;AAC9C,MAAMN,KAAK,GAAGO,KAAK,CAACF,SAAS,CAACL,KAAK;AAEnC,SAASC,IAAIA,CAACO,GAAG,EAAE;EACxB,OAAOJ,MAAM,CAACH,IAAI,CAACG,MAAM,CAACI,GAAG,CAAC,CAAC;AACjC;AAEO,SAASN,OAAOA,CAACM,GAAG,EAAE;EAC3B,IAAIA,GAAG,IAAI,IAAI,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAID,KAAK,CAACE,OAAO,CAACD,GAAG,CAAC,IAClB,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOA,GAAG,CAACE,MAAM,KAAK,CAAC;EACzB;EAEA,KAAK,MAAMC,GAAG,IAAIH,GAAG,EAAE;IACrB,IAAIT,MAAM,CAACa,IAAI,CAACJ,GAAG,EAAEG,GAAG,CAAC,EAAE;MACzB,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEO,SAASR,IAAIA,CAACU,KAAK,EAAEC,CAAC,EAAEC,KAAK,EAAE;EACpC,IAAIF,KAAK,IAAI,IAAI,EAAE;IACjB;EACF;EAEA,IAAKC,CAAC,IAAI,IAAI,IAAKC,KAAK,EAAE;IACxB,OAAOF,KAAK,CAACA,KAAK,CAACH,MAAM,GAAG,CAAC,CAAC;EAChC;EAEA,OAAOV,KAAK,CAACY,IAAI,CAACC,KAAK,EAAEG,IAAI,CAACC,GAAG,CAACJ,KAAK,CAACH,MAAM,GAAGI,CAAC,EAAE,CAAC,CAAC,CAAC;AACzD;AAEA5C,SAAS,CAACgD,sBAAsB,GAAG,CAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAE;AAE1DhD,SAAS,CAACiD,QAAQ,GAAG,UAAUC,aAAa,EAAE;EAC5C,IAAI;IACF,IAAIC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC;EACrC,CAAC,CAAC,OAAOI,CAAC,EAAE;IACVpC,MAAM,CAACqC,MAAM,CAAC,sCAAsC,EAAEL,aAAa,CAAC;IACpE,OAAO,IAAI;EACb;EACA;EACA,IAAIC,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3CjC,MAAM,CAACqC,MAAM,CAAC,mCAAmC,EAAEL,aAAa,CAAC;IACjE,OAAO,IAAI;EACb;;EAEA;;EAEA;EACA;EACA,IAAIrB,MAAM,CAACa,IAAI,CAACS,GAAG,EAAE,SAAS,CAAC,EAAE;IAC/B,IAAI,CAAEtB,MAAM,CAACa,IAAI,CAACS,GAAG,EAAE,QAAQ,CAAC,EAAE;MAChCA,GAAG,CAACK,MAAM,GAAG,CAAC,CAAC;IACjB;IACAL,GAAG,CAACM,OAAO,CAACC,OAAO,CAACC,QAAQ,IAAI;MAC9BR,GAAG,CAACK,MAAM,CAACG,QAAQ,CAAC,GAAGC,SAAS;IAClC,CAAC,CAAC;IACF,OAAOT,GAAG,CAACM,OAAO;EACpB;EAEA,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,OAAO,CAACG,KAAK,IAAI;IAC9C,IAAIhC,MAAM,CAACa,IAAI,CAACS,GAAG,EAAEU,KAAK,CAAC,EAAE;MAC3BV,GAAG,CAACU,KAAK,CAAC,GAAGC,KAAK,CAACC,yBAAyB,CAACZ,GAAG,CAACU,KAAK,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF,OAAOV,GAAG;AACZ,CAAC;AAEDnD,SAAS,CAACgE,YAAY,GAAG,UAAUb,GAAG,EAAE;EACtC,MAAMc,IAAI,GAAGH,KAAK,CAACI,KAAK,CAACf,GAAG,CAAC;;EAE7B;EACA;EACA,IAAItB,MAAM,CAACa,IAAI,CAACS,GAAG,EAAE,QAAQ,CAAC,EAAE;IAC9B,MAAMM,OAAO,GAAG,EAAE;IAElBvB,MAAM,CAACH,IAAI,CAACoB,GAAG,CAACK,MAAM,CAAC,CAACE,OAAO,CAACjB,GAAG,IAAI;MACrC,MAAM0B,KAAK,GAAGhB,GAAG,CAACK,MAAM,CAACf,GAAG,CAAC;MAE7B,IAAI,OAAO0B,KAAK,KAAK,WAAW,EAAE;QAChCV,OAAO,CAACW,IAAI,CAAC3B,GAAG,CAAC;QACjB,OAAOwB,IAAI,CAACT,MAAM,CAACf,GAAG,CAAC;MACzB;IACF,CAAC,CAAC;IAEF,IAAI,CAAET,OAAO,CAACyB,OAAO,CAAC,EAAE;MACtBQ,IAAI,CAACR,OAAO,GAAGA,OAAO;IACxB;IAEA,IAAIzB,OAAO,CAACiC,IAAI,CAACT,MAAM,CAAC,EAAE;MACxB,OAAOS,IAAI,CAACT,MAAM;IACpB;EACF;;EAEA;EACA,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACE,OAAO,CAACG,KAAK,IAAI;IAC9C,IAAIhC,MAAM,CAACa,IAAI,CAACuB,IAAI,EAAEJ,KAAK,CAAC,EAAE;MAC5BI,IAAI,CAACJ,KAAK,CAAC,GAAGC,KAAK,CAACO,uBAAuB,CAACJ,IAAI,CAACJ,KAAK,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF,IAAIV,GAAG,CAACmB,EAAE,IAAI,OAAOnB,GAAG,CAACmB,EAAE,KAAK,QAAQ,EAAE;IACxC,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;EAC/C;EAEA,OAAOnB,IAAI,CAACoB,SAAS,CAACP,IAAI,CAAC;AAC7B,CAAC,C;;;;;;;;;;;ACpHD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjE,SAAS,CAACyE,gBAAgB,GAAG,MAAMA,gBAAgB,CAAC;EAClDvE,WAAWA,CAACC,OAAO,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACuE,IAAI,GAAGvE,OAAO,CAACuE,IAAI;;IAExB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,YAAY,GAAGxE,OAAO,CAACwE,YAAY;;IAExC;IACA;IACA;IACA,IAAI,CAACC,QAAQ,GAAGzE,OAAO,CAAC0E,OAAO,IAAI,YAAY,CAAC,CAAC;IACjD,IAAI,CAACC,cAAc,GAAG,KAAK;;IAE3B;IACA,IAAI,CAACC,gBAAgB,GAAG5E,OAAO,CAAC6E,eAAe;;IAE/C;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM,GAAG9E,OAAO,CAAC8E,MAAM;;IAE5B;IACA;IACA,IAAI,CAACC,UAAU,GAAG/E,OAAO,CAACgF,SAAS,IAAI,YAAY,CAAC,CAAC;;IAErD;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,UAAU,GAAGjF,OAAO,CAACiF,UAAU;;IAEpC;IACA,IAAI,CAACC,UAAU,GAAGlF,OAAO,CAACkF,UAAU;;IAEpC;IACA,IAAI,CAACC,YAAY,GAAG,IAAI;IAExB,IAAI,CAACC,KAAK,GAAGpF,OAAO,CAACoF,KAAK;EAC5B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEV,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACF,QAAQ,CAAC,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMO,SAASA,CAACF,MAAM,EAAE;IACtB,IAAI,IAAI,CAACH,cAAc,EAAE;MACvB,MAAM,IAAIP,KAAK,CAAC,wDAAwD,CAAC;IAC3E;IACA,IAAI,CAACU,MAAM,GAAGA,MAAM;IACpB,MAAM,IAAI,CAACC,UAAU,CAACD,MAAM,CAAC;EAC/B;AACF,CAAC,C;;;;;;;;;;;AC5GD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjF,SAAS,CAACwF,YAAY,GAAG,MAAMA,YAAY,CAAC;EAC1CtF,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACsF,IAAI,GAAG,EAAE,CAACC,MAAM,CAACvF,OAAO,CAACsF,IAAI,IAAIE,WAAW,CAAC,CAAC,CAAC;IACpD,IAAI,CAACC,SAAS,GAAG1D,MAAM,CAAC2D,MAAM,CAAC,IAAI,CAAC;EACtC;;EAEA;EACA;EACA;EACAC,SAASA,CAACpB,IAAI,EAAE;IACd,IAAIqB,IAAI,GAAG,IAAI;IAEf,IAAIC,QAAQ,GAAGD,IAAI,CAACH,SAAS,CAAClB,IAAI,CAAC,IAAI,IAAI;IAC3C,IAAIsB,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAIC,YAAY,GAAGF,IAAI,CAACN,IAAI,CAACC,MAAM,CAAChB,IAAI,CAAC;MACzC,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAACzD,MAAM,EAAE0D,CAAC,EAAE,EAAE;QAC5C,IAAI,OAAOD,YAAY,CAACC,CAAC,CAAC,KAAK,UAAU,EAAE;UACzCD,YAAY,CAACC,CAAC,CAAC,GAAGD,YAAY,CAACC,CAAC,CAAC,CAAC,CAAC;QACrC;MACF;MACAH,IAAI,CAACH,SAAS,CAAClB,IAAI,CAAC,GAAGsB,QAAQ,GAAGG,MAAM,CAACC,eAAe,CAACC,KAAK,CAAC,IAAI,EAAEJ,YAAY,CAAC;IACpF;IACA,OAAOD,QAAQ;EACjB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASL,WAAWA,CAAA,EAAG;EACrB,OAAOQ,MAAM,CAACG,SAAS,CAAC,EAAE,CAAC;AAC7B;AAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAtG,SAAS,CAACwF,YAAY,CAACe,GAAG,GAAG,UAAUC,KAAK,EAAE9B,IAAI,EAAE;EAClD,IAAI,CAACA,IAAI,EAAE;IACTA,IAAI,GAAG,SAAS;EAClB;EACA,IAAI,CAAC8B,KAAK,EAAE;IACV;IACA;IACA;IACA;IACA,OAAOL,MAAM,CAACM,QAAQ;EACxB;EACA,IAAInB,YAAY,GAAGkB,KAAK,CAAClB,YAAY;EACrC,IAAI,CAACA,YAAY,EAAE;IACjBkB,KAAK,CAAClB,YAAY,GAAGA,YAAY,GAAG,IAAItF,SAAS,CAACwF,YAAY,CAAC;MAC7DC,IAAI,EAAEe,KAAK,CAACnB;IACd,CAAC,CAAC;EACJ;EACA,OAAOC,YAAY,CAACQ,SAAS,CAACpB,IAAI,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA1E,SAAS,CAAC0G,WAAW,GAAG,UAAUC,SAAS,EAAEC,UAAU,EAAE;EACvD,IAAIC,MAAM,GAAG7G,SAAS,CAACwF,YAAY,CAACe,GAAG,CAACI,SAAS,EAAE,OAAO,GAAGC,UAAU,CAAC;EACxE,OAAOC,MAAM,CAACP,SAAS,CAAC,EAAE,CAAC;AAC7B,CAAC,C", "file": "/packages/ddp-common.js", "sourcesContent": ["/**\n * @namespace DDPCommon\n * @summary Namespace for DDPCommon-related methods/classes. Shared between \n * `ddp-client` and `ddp-server`, where the ddp-client is the implementation\n * of a ddp client for both client AND server; and the ddp server is the\n * implementation of the livedata server and stream server. Common \n * functionality shared between both can be shared under this namespace\n */\nDDPCommon = {};\n", "// Heartbeat options:\n//   heartbeatInterval: interval to send pings, in milliseconds.\n//   heartbeatTimeout: timeout to close the connection if a reply isn't\n//     received, in milliseconds.\n//   sendPing: function to call to send a ping on the connection.\n//   onTimeout: function to call to close the connection.\n\nDDPCommon.Heartbeat = class Heartbeat {\n  constructor(options) {\n    this.heartbeatInterval = options.heartbeatInterval;\n    this.heartbeatTimeout = options.heartbeatTimeout;\n    this._sendPing = options.sendPing;\n    this._onTimeout = options.onTimeout;\n    this._seenPacket = false;\n\n    this._heartbeatIntervalHandle = null;\n    this._heartbeatTimeoutHandle = null;\n  }\n\n  stop() {\n    this._clearHeartbeatIntervalTimer();\n    this._clearHeartbeatTimeoutTimer();\n  }\n\n  start() {\n    this.stop();\n    this._startHeartbeatIntervalTimer();\n  }\n\n  _startHeartbeatIntervalTimer() {\n    this._heartbeatIntervalHandle = Meteor.setInterval(\n      () => this._heartbeatIntervalFired(),\n      this.heartbeatInterval\n    );\n  }\n\n  _startHeartbeatTimeoutTimer() {\n    this._heartbeatTimeoutHandle = Meteor.setTimeout(\n      () => this._heartbeatTimeoutFired(),\n      this.heartbeatTimeout\n    );\n  }\n\n  _clearHeartbeatIntervalTimer() {\n    if (this._heartbeatIntervalHandle) {\n      Meteor.clearInterval(this._heartbeatIntervalHandle);\n      this._heartbeatIntervalHandle = null;\n    }\n  }\n\n  _clearHeartbeatTimeoutTimer() {\n    if (this._heartbeatTimeoutHandle) {\n      Meteor.clearTimeout(this._heartbeatTimeoutHandle);\n      this._heartbeatTimeoutHandle = null;\n    }\n  }\n\n  // The heartbeat interval timer is fired when we should send a ping.\n  _heartbeatIntervalFired() {\n    // don't send ping if we've seen a packet since we last checked,\n    // *or* if we have already sent a ping and are awaiting a timeout.\n    // That shouldn't happen, but it's possible if\n    // `this.heartbeatInterval` is smaller than\n    // `this.heartbeatTimeout`.\n    if (! this._seenPacket && ! this._heartbeatTimeoutHandle) {\n      this._sendPing();\n      // Set up timeout, in case a pong doesn't arrive in time.\n      this._startHeartbeatTimeoutTimer();\n    }\n    this._seenPacket = false;\n  }\n\n  // The heartbeat timeout timer is fired when we sent a ping, but we\n  // timed out waiting for the pong.\n  _heartbeatTimeoutFired() {\n    this._heartbeatTimeoutHandle = null;\n    this._onTimeout();\n  }\n\n  messageReceived() {\n    // Tell periodic checkin that we have seen a packet, and thus it\n    // does not need to send a ping this cycle.\n    this._seenPacket = true;\n    // If we were waiting for a pong, we got it.\n    if (this._heartbeatTimeoutHandle) {\n      this._clearHeartbeatTimeoutTimer();\n    }\n  }\n};\n", "\"use strict\";\n\nexport const hasOwn = Object.prototype.hasOwnProperty;\nexport const slice = Array.prototype.slice;\n\nexport function keys(obj) {\n  return Object.keys(Object(obj));\n}\n\nexport function isEmpty(obj) {\n  if (obj == null) {\n    return true;\n  }\n\n  if (Array.isArray(obj) ||\n      typeof obj === \"string\") {\n    return obj.length === 0;\n  }\n\n  for (const key in obj) {\n    if (hasOwn.call(obj, key)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nexport function last(array, n, guard) {\n  if (array == null) {\n    return;\n  }\n\n  if ((n == null) || guard) {\n    return array[array.length - 1];\n  }\n\n  return slice.call(array, Math.max(array.length - n, 0));\n}\n\nDDPCommon.SUPPORTED_DDP_VERSIONS = [ '1', 'pre2', 'pre1' ];\n\nDDPCommon.parseDDP = function (stringMessage) {\n  try {\n    var msg = JSON.parse(stringMessage);\n  } catch (e) {\n    Meteor._debug(\"Discarding message with invalid JSON\", stringMessage);\n    return null;\n  }\n  // DDP messages must be objects.\n  if (msg === null || typeof msg !== 'object') {\n    Meteor._debug(\"Discarding non-object DDP message\", stringMessage);\n    return null;\n  }\n\n  // massage msg to get it into \"abstract ddp\" rather than \"wire ddp\" format.\n\n  // switch between \"cleared\" rep of unsetting fields and \"undefined\"\n  // rep of same\n  if (hasOwn.call(msg, 'cleared')) {\n    if (! hasOwn.call(msg, 'fields')) {\n      msg.fields = {};\n    }\n    msg.cleared.forEach(clearKey => {\n      msg.fields[clearKey] = undefined;\n    });\n    delete msg.cleared;\n  }\n\n  ['fields', 'params', 'result'].forEach(field => {\n    if (hasOwn.call(msg, field)) {\n      msg[field] = EJSON._adjustTypesFromJSONValue(msg[field]);\n    }\n  });\n\n  return msg;\n};\n\nDDPCommon.stringifyDDP = function (msg) {\n  const copy = EJSON.clone(msg);\n\n  // swizzle 'changed' messages from 'fields undefined' rep to 'fields\n  // and cleared' rep\n  if (hasOwn.call(msg, 'fields')) {\n    const cleared = [];\n\n    Object.keys(msg.fields).forEach(key => {\n      const value = msg.fields[key];\n\n      if (typeof value === \"undefined\") {\n        cleared.push(key);\n        delete copy.fields[key];\n      }\n    });\n\n    if (! isEmpty(cleared)) {\n      copy.cleared = cleared;\n    }\n\n    if (isEmpty(copy.fields)) {\n      delete copy.fields;\n    }\n  }\n\n  // adjust types to basic\n  ['fields', 'params', 'result'].forEach(field => {\n    if (hasOwn.call(copy, field)) {\n      copy[field] = EJSON._adjustTypesToJSONValue(copy[field]);\n    }\n  });\n\n  if (msg.id && typeof msg.id !== 'string') {\n    throw new Error(\"Message id is not a string\");\n  }\n\n  return JSON.stringify(copy);\n};\n", "// Instance name is this because it is usually referred to as this inside a\n// method definition\n/**\n * @summary The state for a single invocation of a method, referenced by this\n * inside a method definition.\n * @param {Object} options\n * @instanceName this\n * @showInstanceName true\n */\nDDPCommon.MethodInvocation = class MethodInvocation {\n  constructor(options) {\n    // true if we're running not the actual method, but a stub (that is,\n    // if we're on a client (which may be a browser, or in the future a\n    // server connecting to another server) and presently running a\n    // simulation of a server-side method for latency compensation\n    // purposes). not currently true except in a client such as a browser,\n    // since there's usually no point in running stubs unless you have a\n    // zero-latency connection to the user.\n\n    /**\n     * @summary The name given to the method.\n     * @locus Anywhere\n     * @name  name\n     * @memberOf DDPCommon.MethodInvocation\n     * @instance\n     * @type {String}\n     */\n    this.name = options.name;\n\n    /**\n     * @summary Access inside a method invocation.  Boolean value, true if this invocation is a stub.\n     * @locus Anywhere\n     * @name  isSimulation\n     * @memberOf DDPCommon.MethodInvocation\n     * @instance\n     * @type {Boolean}\n     */\n    this.isSimulation = options.isSimulation;\n\n    // call this function to allow other method invocations (from the\n    // same client) to continue running without waiting for this one to\n    // complete.\n    this._unblock = options.unblock || function () {};\n    this._calledUnblock = false;\n\n    // used to know when the function apply was called by callAsync\n    this._isFromCallAsync = options.isFromCallAsync;\n\n    // current user id\n\n    /**\n     * @summary The id of the user that made this method call, or `null` if no user was logged in.\n     * @locus Anywhere\n     * @name  userId\n     * @memberOf DDPCommon.MethodInvocation\n     * @instance\n     */\n    this.userId = options.userId;\n\n    // sets current user id in all appropriate server contexts and\n    // reruns subscriptions\n    this._setUserId = options.setUserId || function () {};\n\n    // On the server, the connection this method call came in on.\n\n    /**\n     * @summary Access inside a method invocation. The [connection](#meteor_onconnection) that this method was received on. `null` if the method is not associated with a connection, eg. a server initiated method call. Calls to methods made from a server method which was in turn initiated from the client share the same `connection`.\n     * @locus Server\n     * @name  connection\n     * @memberOf DDPCommon.MethodInvocation\n     * @instance\n     */\n    this.connection = options.connection;\n\n    // The seed for randomStream value generation\n    this.randomSeed = options.randomSeed;\n\n    // This is set by RandomStream.get; and holds the random stream state\n    this.randomStream = null;\n\n    this.fence = options.fence;\n  }\n\n  /**\n   * @summary Call inside a method invocation.  Allow subsequent method from this client to begin running in a new fiber.\n   * @locus Server\n   * @memberOf DDPCommon.MethodInvocation\n   * @instance\n   */\n  unblock() {\n    this._calledUnblock = true;\n    this._unblock();\n  }\n\n  /**\n   * @summary Set the logged in user.\n   * @locus Server\n   * @memberOf DDPCommon.MethodInvocation\n   * @instance\n   * @param {String | null} userId The value that should be returned by `userId` on this connection.\n   */\n  async setUserId(userId) {\n    if (this._calledUnblock) {\n      throw new Error(\"Can't call setUserId in a method after calling unblock\");\n    }\n    this.userId = userId;\n    await this._setUserId(userId);\n  }\n};\n", "// RandomStream allows for generation of pseudo-random values, from a seed.\n//\n// We use this for consistent 'random' numbers across the client and server.\n// We want to generate probably-unique IDs on the client, and we ideally want\n// the server to generate the same IDs when it executes the method.\n//\n// For generated values to be the same, we must seed ourselves the same way,\n// and we must keep track of the current state of our pseudo-random generators.\n// We call this state the scope. By default, we use the current DDP method\n// invocation as our scope.  DDP now allows the client to specify a randomSeed.\n// If a randomSeed is provided it will be used to seed our random sequences.\n// In this way, client and server method calls will generate the same values.\n//\n// We expose multiple named streams; each stream is independent\n// and is seeded differently (but predictably from the name).\n// By using multiple streams, we support reordering of requests,\n// as long as they occur on different streams.\n//\n// @param options {Optional Object}\n//   seed: Array or value - Seed value(s) for the generator.\n//                          If an array, will be used as-is\n//                          If a value, will be converted to a single-value array\n//                          If omitted, a random array will be used as the seed.\nDDPCommon.RandomStream = class RandomStream {\n  constructor(options) {\n    this.seed = [].concat(options.seed || randomToken());\n    this.sequences = Object.create(null);\n  }\n\n  // Get a random sequence with the specified name, creating it if does not exist.\n  // New sequences are seeded with the seed concatenated with the name.\n  // By passing a seed into Random.create, we use the Alea generator.\n  _sequence(name) {\n    var self = this;\n\n    var sequence = self.sequences[name] || null;\n    if (sequence === null) {\n      var sequenceSeed = self.seed.concat(name);\n      for (var i = 0; i < sequenceSeed.length; i++) {\n        if (typeof sequenceSeed[i] === \"function\") {\n          sequenceSeed[i] = sequenceSeed[i]();\n        }\n      }\n      self.sequences[name] = sequence = Random.createWithSeeds.apply(null, sequenceSeed);\n    }\n    return sequence;\n  }\n};\n\n// Returns a random string of sufficient length for a random seed.\n// This is a placeholder function; a similar function is planned\n// for Random itself; when that is added we should remove this function,\n// and call Random's randomToken instead.\nfunction randomToken() {\n  return Random.hexString(20);\n};\n\n// Returns the random stream with the specified name, in the specified\n// scope. If a scope is passed, then we use that to seed a (not\n// cryptographically secure) PRNG using the fast Alea algorithm.  If\n// scope is null (or otherwise falsey) then we use a generated seed.\n//\n// However, scope will normally be the current DDP method invocation,\n// so we'll use the stream with the specified name, and we should get\n// consistent values on the client and server sides of a method call.\nDDPCommon.RandomStream.get = function (scope, name) {\n  if (!name) {\n    name = \"default\";\n  }\n  if (!scope) {\n    // There was no scope passed in; the sequence won't actually be\n    // reproducible. but make it fast (and not cryptographically\n    // secure) anyways, so that the behavior is similar to what you'd\n    // get by passing in a scope.\n    return Random.insecure;\n  }\n  var randomStream = scope.randomStream;\n  if (!randomStream) {\n    scope.randomStream = randomStream = new DDPCommon.RandomStream({\n      seed: scope.randomSeed\n    });\n  }\n  return randomStream._sequence(name);\n};\n\n// Creates a randomSeed for passing to a method call.\n// Note that we take enclosing as an argument,\n// though we expect it to be DDP._CurrentMethodInvocation.get()\n// However, we often evaluate makeRpcSeed lazily, and thus the relevant\n// invocation may not be the one currently in scope.\n// If enclosing is null, we'll use Random and values won't be repeatable.\nDDPCommon.makeRpcSeed = function (enclosing, methodName) {\n  var stream = DDPCommon.RandomStream.get(enclosing, '/rpc/' + methodName);\n  return stream.hexString(20);\n};\n"]}