{"version": 3, "sources": ["meteor://💻app/packages/accounts-password/password_client.js"], "names": ["_objectSpread", "module", "link", "default", "v", "reportError", "error", "callback", "internalLoginWithPassword", "_ref", "selector", "password", "code", "includes", "username", "email", "Accounts", "callLoginMethod", "methodArguments", "user", "_hashPassword", "userCallback", "result", "Meteor", "loginWithPassword", "digest", "SHA256", "algorithm", "loginWithPasswordAnd2faCode", "Error", "createUser", "options", "methodName", "createUserAsync", "Promise", "resolve", "reject", "e", "changePassword", "oldPassword", "newPassword", "String", "connection", "apply", "forgotPassword", "call", "resetPassword", "token", "verifyEmail"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,aAAa;AAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArG;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACtC,IAAIA,QAAQ,EAAE;IACZA,QAAQ,CAACD,KAAK,CAAC;EACjB,CAAC,MAAM;IACL,MAAMA,KAAK;EACb;AACH,CAAC;AAED,MAAME,yBAAyB,GAAGC,IAAA,IAA4C;EAAA,IAA3C;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,IAAI;IAAEL;EAAS,CAAC,GAAAE,IAAA;EACvE,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAC9B,IAAI,CAACA,QAAQ,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAEH,QAAQ,GAAG;IAAEI,QAAQ,EAAEJ;EAAS,CAAC,CAAC,KAC1DA,QAAQ,GAAG;IAAEK,KAAK,EAAEL;EAAS,CAAC;EACrCM,QAAQ,CAACC,eAAe,CAAC;IACvBC,eAAe,EAAE,CACf;MACEC,IAAI,EAAET,QAAQ;MACdC,QAAQ,EAAEK,QAAQ,CAACI,aAAa,CAACT,QAAQ,CAAC;MAC1CC;IACF,CAAC,CACF;IACDS,YAAY,EAAEA,CAACf,KAAK,EAAEgB,MAAM,KAAK;MAC/B,IAAIhB,KAAK,EAAE;QACTD,WAAW,CAACC,KAAK,EAAEC,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACLA,QAAQ,IAAIA,QAAQ,CAACD,KAAK,EAAEgB,MAAM,CAAC;MACrC;IACF;EACF,CAAC,CAAC;EACF,OAAOZ,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAa,MAAM,CAACC,iBAAiB,GAAG,CAACd,QAAQ,EAAEC,QAAQ,EAAEJ,QAAQ,KAAK;EAC3D,OAAOC,yBAAyB,CAAC;IAAEE,QAAQ;IAAEC,QAAQ;IAAEJ;EAAS,CAAC,CAAC;AACpE,CAAC;AAEDS,QAAQ,CAACI,aAAa,GAAGT,QAAQ,KAAK;EACpCc,MAAM,EAAEC,MAAM,CAACf,QAAQ,CAAC;EACxBgB,SAAS,EAAE;AACb,CAAC,CAAC;;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAJ,MAAM,CAACK,2BAA2B,GAAG,CAAClB,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEL,QAAQ,KAAK;EAC3E,IAAIK,IAAI,IAAI,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IACrD,MAAM,IAAIW,MAAM,CAACM,KAAK,CACpB,GAAG,EACH,2EACF,CAAC;EACH;EACA,OAAOrB,yBAAyB,CAAC;IAAEE,QAAQ;IAAEC,QAAQ;IAAEC,IAAI;IAAEL;EAAS,CAAC,CAAC;AAC1E,CAAC;;AAGD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAS,QAAQ,CAACc,UAAU,GAAG,CAACC,OAAO,EAAExB,QAAQ,KAAK;EAC3CwB,OAAO,GAAA/B,aAAA,KAAQ+B,OAAO,CAAE,CAAC,CAAC;;EAE1B,IAAI,OAAOA,OAAO,CAACpB,QAAQ,KAAK,QAAQ,EACtC,MAAM,IAAIkB,KAAK,CAAC,mCAAmC,CAAC;EACtD,IAAI,CAACE,OAAO,CAACpB,QAAQ,EAAE;IACrB,OAAON,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,2BAA2B,CAAC,EAAEtB,QAAQ,CAAC;EAClF;;EAEA;EACAwB,OAAO,CAACpB,QAAQ,GAAGK,QAAQ,CAACI,aAAa,CAACW,OAAO,CAACpB,QAAQ,CAAC;EAE3DK,QAAQ,CAACC,eAAe,CAAC;IACvBe,UAAU,EAAE,YAAY;IACxBd,eAAe,EAAE,CAACa,OAAO,CAAC;IAC1BV,YAAY,EAAEd;EAChB,CAAC,CAAC;AACJ,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAS,QAAQ,CAACiB,eAAe,GAAIF,OAAO,IAAK;EACtC,OAAO,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KACjCpB,QAAQ,CAACc,UAAU,CAACC,OAAO,EAAGM,CAAC,IAAK;IAClC,IAAIA,CAAC,EAAE;MACLD,MAAM,CAACC,CAAC,CAAC;IACX,CAAC,MAAM;MACLF,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CACH,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAnB,QAAQ,CAACsB,cAAc,GAAG,CAACC,WAAW,EAAEC,WAAW,EAAEjC,QAAQ,KAAK;EAChE,IAAI,CAACgB,MAAM,CAACJ,IAAI,CAAC,CAAC,EAAE;IAClB,OAAOd,WAAW,CAAC,IAAIwB,KAAK,CAAC,uCAAuC,CAAC,EAAEtB,QAAQ,CAAC;EAClF;EAEA,IAAI,EAAE,OAAOiC,WAAW,KAAK,QAAQ,IAAIA,WAAW,YAAYC,MAAM,CAAC,EAAE;IACvE,OAAOpC,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,2BAA2B,CAAC,EAAEtB,QAAQ,CAAC;EAClF;EAEA,IAAI,CAACiC,WAAW,EAAE;IAChB,OAAOnC,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,2BAA2B,CAAC,EAAEtB,QAAQ,CAAC;EAClF;EAEAS,QAAQ,CAAC0B,UAAU,CAACC,KAAK,CACvB,gBAAgB,EAChB,CAACJ,WAAW,GAAGvB,QAAQ,CAACI,aAAa,CAACmB,WAAW,CAAC,GAAG,IAAI,EACxDvB,QAAQ,CAACI,aAAa,CAACoB,WAAW,CAAC,CAAC,EACrC,CAAClC,KAAK,EAAEgB,MAAM,KAAK;IACnB,IAAIhB,KAAK,IAAI,CAACgB,MAAM,EAAE;MAClB;MACAjB,WAAW,CACTC,KAAK,IAAI,IAAIuB,KAAK,CAAC,gCAAgC,CAAC,EAAEtB,QAAQ,CAAC;IACnE,CAAC,MAAM;MACLA,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACxB;EACF,CACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAS,QAAQ,CAAC4B,cAAc,GAAG,CAACb,OAAO,EAAExB,QAAQ,KAAK;EAC/C,IAAI,CAACwB,OAAO,CAAChB,KAAK,EAAE;IAClB,OAAOV,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,yBAAyB,CAAC,EAAEtB,QAAQ,CAAC;EAChF;EAEA,IAAIA,QAAQ,EAAE;IACZS,QAAQ,CAAC0B,UAAU,CAACG,IAAI,CAAC,gBAAgB,EAAEd,OAAO,EAAExB,QAAQ,CAAC;EAC/D,CAAC,MAAM;IACLS,QAAQ,CAAC0B,UAAU,CAACG,IAAI,CAAC,gBAAgB,EAAEd,OAAO,CAAC;EACrD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAf,QAAQ,CAAC8B,aAAa,GAAG,CAACC,KAAK,EAAEP,WAAW,EAAEjC,QAAQ,KAAK;EACzD,IAAI,EAAE,OAAOwC,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYN,MAAM,CAAC,EAAE;IAC3D,OAAOpC,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,wBAAwB,CAAC,EAAEtB,QAAQ,CAAC;EAC/E;EAEA,IAAI,EAAE,OAAOiC,WAAW,KAAK,QAAQ,IAAIA,WAAW,YAAYC,MAAM,CAAC,EAAE;IACvE,OAAOpC,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,2BAA2B,CAAC,EAAEtB,QAAQ,CAAC;EAClF;EAEA,IAAI,CAACiC,WAAW,EAAE;IAChB,OAAOnC,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,2BAA2B,CAAC,EAAEtB,QAAQ,CAAC;EAClF;EAEAS,QAAQ,CAACC,eAAe,CAAC;IACvBe,UAAU,EAAE,eAAe;IAC3Bd,eAAe,EAAE,CAAC6B,KAAK,EAAE/B,QAAQ,CAACI,aAAa,CAACoB,WAAW,CAAC,CAAC;IAC7DnB,YAAY,EAAEd;EAAQ,CAAC,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACAS,QAAQ,CAACgC,WAAW,GAAG,CAACD,KAAK,EAAExC,QAAQ,KAAK;EAC1C,IAAI,CAACwC,KAAK,EAAE;IACV,OAAO1C,WAAW,CAAC,IAAIkB,MAAM,CAACM,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,EAAEtB,QAAQ,CAAC;EAC3E;EAEAS,QAAQ,CAACC,eAAe,CAAC;IACvBe,UAAU,EAAE,aAAa;IACzBd,eAAe,EAAE,CAAC6B,KAAK,CAAC;IACxB1B,YAAY,EAAEd;EAAQ,CAAC,CAAC;AAC5B,CAAC,C", "file": "/packages/accounts-password.js", "sourcesContent": ["// Used in the various functions below to handle errors consistently\nconst reportError = (error, callback) => {\n   if (callback) {\n     callback(error);\n   } else {\n     throw error;\n   }\n};\n\nconst internalLoginWithPassword = ({ selector, password, code, callback }) => {\n  if (typeof selector === 'string')\n    if (!selector.includes('@')) selector = { username: selector };\n    else selector = { email: selector };\n  Accounts.callLoginMethod({\n    methodArguments: [\n      {\n        user: selector,\n        password: Accounts._hashPassword(password),\n        code,\n      },\n    ],\n    userCallback: (error, result) => {\n      if (error) {\n        reportError(error, callback);\n      } else {\n        callback && callback(error, result);\n      }\n    },\n  });\n  return selector;\n};\n\n// Attempt to log in with a password.\n//\n// @param selector {String|Object} One of the following:\n//   - {username: (username)}\n//   - {email: (email)}\n//   - a string which may be a username or email, depending on whether\n//     it contains \"@\".\n// @param password {String}\n// @param callback {Function(error|undefined)}\n\n/**\n * @summary Log the user in with a password.\n * @locus Client\n * @param {Object | String} selector\n *   Either a string interpreted as a username or an email; or an object with a\n *   single key: `email`, `username` or `id`. Username or email match in a case\n *   insensitive manner.\n * @param {String} password The user's password.\n * @param {Function} [callback] Optional callback.\n *   Called with no arguments on success, or with a single `Error` argument\n *   on failure.\n * @importFromPackage meteor\n */\nMeteor.loginWithPassword = (selector, password, callback) => {\n  return internalLoginWithPassword({ selector, password, callback });\n};\n\nAccounts._hashPassword = password => ({\n  digest: SHA256(password),\n  algorithm: \"sha-256\"\n});\n\n\n/**\n * @summary Log the user in with a password and token.\n * @locus Client\n * @param {Object | String} selector\n *   Either a string interpreted as a username or an email; or an object with a\n *   single key: `email`, `username` or `id`. Username or email match in a case\n *   insensitive manner.\n * @param {String} password The user's password.\n * @param {String} token Token provide by the user's authenticator app.\n * @param {Function} [callback] Optional callback.\n *   Called with no arguments on success, or with a single `Error` argument\n *   on failure.\n * @importFromPackage meteor\n */\n\nMeteor.loginWithPasswordAnd2faCode = (selector, password, code, callback) => {\n  if (code == null || typeof code !== 'string' || !code) {\n    throw new Meteor.Error(\n      400,\n      'token is required to use loginWithPasswordAnd2faCode and must be a string'\n    );\n  }\n  return internalLoginWithPassword({ selector, password, code, callback });\n};\n\n\n// Attempt to log in as a new user.\n\n/**\n * @summary Create a new user.\n * @locus Anywhere\n * @param {Object} options\n * @param {String} options.username A unique name for this user.\n * @param {String} options.email The user's email address.\n * @param {String} options.password The user's password. This is __not__ sent in plain text over the wire.\n * @param {Object} options.profile The user's profile, typically including the `name` field.\n * @param {Function} [callback] Client only, optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage accounts-base\n */\nAccounts.createUser = (options, callback) => {\n  options = { ...options }; // we'll be modifying options\n\n  if (typeof options.password !== 'string')\n    throw new Error(\"options.password must be a string\");\n  if (!options.password) {\n    return reportError(new Meteor.Error(400, \"Password may not be empty\"), callback);\n  }\n\n  // Replace password with the hashed password.\n  options.password = Accounts._hashPassword(options.password);\n\n  Accounts.callLoginMethod({\n    methodName: 'createUser',\n    methodArguments: [options],\n    userCallback: callback\n  });\n};\n\n\n/**\n * @summary Create a new user and returns a promise of its result.\n * @locus Anywhere\n * @param {Object} options\n * @param {String} options.username A unique name for this user.\n * @param {String} options.email The user's email address.\n * @param {String} options.password The user's password. This is __not__ sent in plain text over the wire.\n * @param {Object} options.profile The user's profile, typically including the `name` field.\n * @importFromPackage accounts-base\n */\nAccounts.createUserAsync = (options) => {\n  return new Promise((resolve, reject) =>\n    Accounts.createUser(options, (e) => {\n      if (e) {\n        reject(e);\n      } else {\n        resolve();\n      }\n    })\n  );\n};\n\n// Change password. Must be logged in.\n//\n// @param oldPassword {String|null} By default servers no longer allow\n//   changing password without the old password, but they could so we\n//   support passing no password to the server and letting it decide.\n// @param newPassword {String}\n// @param callback {Function(error|undefined)}\n\n/**\n * @summary Change the current user's password. Must be logged in.\n * @locus Client\n * @param {String} oldPassword The user's current password. This is __not__ sent in plain text over the wire.\n * @param {String} newPassword A new password for the user. This is __not__ sent in plain text over the wire.\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage accounts-base\n */\nAccounts.changePassword = (oldPassword, newPassword, callback) => {\n  if (!Meteor.user()) {\n    return reportError(new Error(\"Must be logged in to change password.\"), callback);\n  }\n\n  if (!(typeof newPassword === \"string\" || newPassword instanceof String)) {\n    return reportError(new Meteor.Error(400, \"Password must be a string\"), callback);\n  }\n\n  if (!newPassword) {\n    return reportError(new Meteor.Error(400, \"Password may not be empty\"), callback);\n  }\n\n  Accounts.connection.apply(\n    'changePassword',\n    [oldPassword ? Accounts._hashPassword(oldPassword) : null,\n     Accounts._hashPassword(newPassword)],\n    (error, result) => {\n    if (error || !result) {\n        // A normal error, not an error telling us to upgrade to bcrypt\n        reportError(\n          error || new Error(\"No result from changePassword.\"), callback);\n      } else {\n        callback && callback();\n      }\n    }\n  );\n};\n\n// Sends an email to a user with a link that can be used to reset\n// their password\n//\n// @param options {Object}\n//   - email: (email)\n// @param callback (optional) {Function(error|undefined)}\n\n/**\n * @summary Request a forgot password email.\n * @locus Client\n * @param {Object} options\n * @param {String} options.email The email address to send a password reset link.\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage accounts-base\n */\nAccounts.forgotPassword = (options, callback) => {\n  if (!options.email) {\n    return reportError(new Meteor.Error(400, \"Must pass options.email\"), callback);\n  }\n\n  if (callback) {\n    Accounts.connection.call(\"forgotPassword\", options, callback);\n  } else {\n    Accounts.connection.call(\"forgotPassword\", options);\n  }\n};\n\n// Resets a password based on a token originally created by\n// Accounts.forgotPassword, and then logs in the matching user.\n//\n// @param token {String}\n// @param newPassword {String}\n// @param callback (optional) {Function(error|undefined)}\n\n/**\n * @summary Reset the password for a user using a token received in email. Logs the user in afterwards if the user doesn't have 2FA enabled.\n * @locus Client\n * @param {String} token The token retrieved from the reset password URL.\n * @param {String} newPassword A new password for the user. This is __not__ sent in plain text over the wire.\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage accounts-base\n */\nAccounts.resetPassword = (token, newPassword, callback) => {\n  if (!(typeof token === \"string\" || token instanceof String)) {\n    return reportError(new Meteor.Error(400, \"Token must be a string\"), callback);\n  }\n\n  if (!(typeof newPassword === \"string\" || newPassword instanceof String)) {\n    return reportError(new Meteor.Error(400, \"Password must be a string\"), callback);\n  }\n\n  if (!newPassword) {\n    return reportError(new Meteor.Error(400, \"Password may not be empty\"), callback);\n  }\n\n  Accounts.callLoginMethod({\n    methodName: 'resetPassword',\n    methodArguments: [token, Accounts._hashPassword(newPassword)],\n    userCallback: callback});\n};\n\n// Verifies a user's email address based on a token originally\n// created by Accounts.sendVerificationEmail\n//\n// @param token {String}\n// @param callback (optional) {Function(error|undefined)}\n\n/**\n * @summary Marks the user's email address as verified. Logs the user in afterwards if the user doesn't have 2FA enabled.\n * @locus Client\n * @param {String} token The token retrieved from the verification URL.\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage accounts-base\n */\nAccounts.verifyEmail = (token, callback) => {\n  if (!token) {\n    return reportError(new Meteor.Error(400, \"Need to pass token\"), callback);\n  }\n\n  Accounts.callLoginMethod({\n    methodName: 'verifyEmail',\n    methodArguments: [token],\n    userCallback: callback});\n};\n"]}