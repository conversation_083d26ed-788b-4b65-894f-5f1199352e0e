{"version": 3, "sources": ["meteor://💻app/packages/allow-deny/allow-deny.js"], "names": ["_objectSpread", "module", "link", "default", "v", "__reifyWaitForDeps__", "hasOwn", "Object", "prototype", "hasOwnProperty", "AllowDeny", "CollectionPrototype", "allow", "options", "addValidator", "deny", "_defineMutationMethods", "self", "_restricted", "_insecure", "undefined", "_validators", "insert", "update", "remove", "insertAsync", "updateAsync", "removeAsync", "upsertAsync", "fetch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_name", "_prefix", "_connection", "Meteor", "server", "isClient", "m", "for<PERSON>ach", "method", "methodName", "useExisting", "handlerPropName", "isInsert", "name", "includes", "check", "arguments", "Match", "Any", "args", "Array", "from", "generatedId", "call", "_makeNewID", "isSimulation", "_id", "_collection", "apply", "throwIfSelectorIsNotId", "syncMethodName", "replace", "syncValidatedMethodName", "char<PERSON>t", "toUpperCase", "slice", "validatedMethodName", "length", "Error", "unshift", "userId", "push", "_isInsecure", "syncMethodsMapper", "e", "toString", "methods", "_updateFetch", "fields", "union", "create", "add", "names", "keys", "Package", "insecure", "asyncSome", "array", "predicate", "item", "asyncEvery", "_validatedInsertAsync", "doc", "validator", "result", "docToValidate", "_isPromise", "_validatedUpdateAsync", "selector", "mutator", "assign", "LocalCollection", "_selectorIsIdPerhapsAsObject", "upsert", "noReplaceError", "mutator<PERSON>eys", "modifiedFields", "op", "params", "ALLOWED_UPDATE_OPERATIONS", "field", "indexOf", "substring", "findOptions", "transform", "fieldName", "findOneAsync", "factoriedDoc", "transformDoc", "_forbid<PERSON><PERSON>lace", "$inc", "$set", "$unset", "$addToSet", "$pop", "$pullAll", "$pull", "$pushAll", "$push", "$bit", "_validatedRemoveAsync", "_callMutatorMethodAsync", "firstArgIsSelector", "alreadyInSimulation", "mutatorMethodName", "applyAsync", "returnStubValue", "resolverType", "returnServerResultPromise", "_stream", "_isStub", "_callMutatorMethod", "callback", "err", "_debug", "ret", "EJSON", "clone", "collection", "allowOrDeny", "validKeysRegEx", "key", "test", "isAsyncKey", "s<PERSON><PERSON><PERSON>", "deprecate", "concat", "Function", "_transform", "wrapTransform", "isAsyncName", "validatorSyncName", "CurrentInvocation", "DDP", "_CurrentMethodInvocation", "_CurrentInvocation", "enclosing", "get", "__reify_async_result__", "_reifyError", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAAlK;IACA;IACA;;IAEA,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;;IAE9C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEAC,SAAS,GAAG;MACVC,mBAAmB,EAAE,CAAC;IACxB,CAAC;;IAED;IACA;IACA,MAAMA,mBAAmB,GAAGD,SAAS,CAACC,mBAAmB;;IAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAA,mBAAmB,CAACC,KAAK,GAAG,UAASC,OAAO,EAAE;MAC5CC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAED,OAAO,CAAC;IACtC,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAF,mBAAmB,CAACI,IAAI,GAAG,UAASF,OAAO,EAAE;MAC3CC,YAAY,CAAC,IAAI,EAAE,MAAM,EAAED,OAAO,CAAC;IACrC,CAAC;IAEDF,mBAAmB,CAACK,sBAAsB,GAAG,UAASH,OAAO,EAAE;MAC7D,MAAMI,IAAI,GAAG,IAAI;MACjBJ,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;MAEvB;MACA;MACAI,IAAI,CAACC,WAAW,GAAG,KAAK;;MAExB;MACA;MACA;MACA;MACAD,IAAI,CAACE,SAAS,GAAGC,SAAS;MAE1BH,IAAI,CAACI,WAAW,GAAG;QACjBC,MAAM,EAAE;UAACV,KAAK,EAAE,EAAE;UAAEG,IAAI,EAAE;QAAE,CAAC;QAC7BQ,MAAM,EAAE;UAACX,KAAK,EAAE,EAAE;UAAEG,IAAI,EAAE;QAAE,CAAC;QAC7BS,MAAM,EAAE;UAACZ,KAAK,EAAE,EAAE;UAAEG,IAAI,EAAE;QAAE,CAAC;QAC7BU,WAAW,EAAE;UAACb,KAAK,EAAE,EAAE;UAAEG,IAAI,EAAE;QAAE,CAAC;QAClCW,WAAW,EAAE;UAACd,KAAK,EAAE,EAAE;UAAEG,IAAI,EAAE;QAAE,CAAC;QAClCY,WAAW,EAAE;UAACf,KAAK,EAAE,EAAE;UAAEG,IAAI,EAAE;QAAE,CAAC;QAClCa,WAAW,EAAE;UAAChB,KAAK,EAAE,EAAE;UAAEG,IAAI,EAAE;QAAE,CAAC;QAAE;QACpCc,KAAK,EAAE,EAAE;QACTC,cAAc,EAAE;MAClB,CAAC;MAED,IAAI,CAACb,IAAI,CAACc,KAAK,EACb,OAAO,CAAC;;MAEV;MACA;MACAd,IAAI,CAACe,OAAO,GAAG,GAAG,GAAGf,IAAI,CAACc,KAAK,GAAG,GAAG;;MAErC;MACA;MACA;MACA;MACA;MACA,IAAId,IAAI,CAACgB,WAAW,KAAKhB,IAAI,CAACgB,WAAW,KAAKC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACE,QAAQ,CAAC,EAAE;QAC/E,MAAMC,CAAC,GAAG,CAAC,CAAC;QAEZ,CACE,aAAa,EACb,aAAa,EACb,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT,CAACC,OAAO,CAACC,MAAM,IAAI;UAClB,MAAMC,UAAU,GAAGvB,IAAI,CAACe,OAAO,GAAGO,MAAM;UAExC,IAAI1B,OAAO,CAAC4B,WAAW,EAAE;YACvB,MAAMC,eAAe,GAAGR,MAAM,CAACE,QAAQ,GACnC,iBAAiB,GACjB,iBAAiB;YACrB;YACA;YACA,IACEnB,IAAI,CAACgB,WAAW,CAACS,eAAe,CAAC,IACjC,OAAOzB,IAAI,CAACgB,WAAW,CAACS,eAAe,CAAC,CAACF,UAAU,CAAC,KAAK,UAAU,EAEnE;UACJ;UAEA,MAAMG,QAAQ,GAAGC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC;UAEhDR,CAAC,CAACG,UAAU,CAAC,GAAG,SAAU;UAAA,GAAW;YACnC;YACAM,KAAK,CAACC,SAAS,EAAE,CAACC,KAAK,CAACC,GAAG,CAAC,CAAC;YAC7B,MAAMC,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;YAClC,IAAI;cACF;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA,IAAIM,WAAW,GAAG,IAAI;cACtB,IAAIV,QAAQ,CAACJ,MAAM,CAAC,IAAI,CAACjC,MAAM,CAACgD,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;gBACpDG,WAAW,GAAGpC,IAAI,CAACsC,UAAU,CAAC,CAAC;cACjC;cAEA,IAAI,IAAI,CAACC,YAAY,EAAE;gBACrB;gBACA;gBACA,IAAIH,WAAW,KAAK,IAAI,EAAE;kBACxBH,IAAI,CAAC,CAAC,CAAC,CAACO,GAAG,GAAGJ,WAAW;gBAC3B;gBACA,OAAOpC,IAAI,CAACyC,WAAW,CAACnB,MAAM,CAAC,CAACoB,KAAK,CAAC1C,IAAI,CAACyC,WAAW,EAAER,IAAI,CAAC;cAC/D;;cAEA;;cAEA;cACA;cACA,IAAI,CAACP,QAAQ,CAACJ,MAAM,CAAC,EAAEqB,sBAAsB,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEX,MAAM,CAAC;cAE9D,MAAMsB,cAAc,GAAGtB,MAAM,CAACuB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;cAClD,MAAMC,uBAAuB,GAAG,YAAY,GAAGxB,MAAM,CAACyB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,cAAc,CAACK,KAAK,CAAC,CAAC,CAAC;cACvG;cACA,MAAMC,mBAAmB,GAAGJ,uBAAuB,GAAG,OAAO;cAE7D,IAAI9C,IAAI,CAACC,WAAW,EAAE;gBACpB;gBACA,IAAID,IAAI,CAACI,WAAW,CAACwC,cAAc,CAAC,CAACjD,KAAK,CAACwD,MAAM,KAAK,CAAC,EAAE;kBACvD,MAAM,IAAIlC,MAAM,CAACmC,KAAK,CACpB,GAAG,EACH,uDAAuD,GACrD,yBAAyB,GACzB9B,MAAM,GACN,IACJ,CAAC;gBACH;gBAEAW,IAAI,CAACoB,OAAO,CAAC,IAAI,CAACC,MAAM,CAAC;gBACzB5B,QAAQ,CAACJ,MAAM,CAAC,IAAIW,IAAI,CAACsB,IAAI,CAACnB,WAAW,CAAC;gBAC1C,OAAOpC,IAAI,CAACkD,mBAAmB,CAAC,CAACR,KAAK,CAAC1C,IAAI,EAAEiC,IAAI,CAAC;cACpD,CAAC,MAAM,IAAIjC,IAAI,CAACwD,WAAW,CAAC,CAAC,EAAE;gBAC7B,IAAIpB,WAAW,KAAK,IAAI,EAAEH,IAAI,CAAC,CAAC,CAAC,CAACO,GAAG,GAAGJ,WAAW;gBACnD;gBACA;gBACA;gBACA,MAAMqB,iBAAiB,GAAG;kBACxBpD,MAAM,EAAE,aAAa;kBACrBC,MAAM,EAAE,aAAa;kBACrBC,MAAM,EAAE;gBACV,CAAC;;gBAGD;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,OAAOP,IAAI,CAACyC,WAAW,CAACgB,iBAAiB,CAACnC,MAAM,CAAC,IAAIA,MAAM,CAAC,CAACoB,KAAK,CAAC1C,IAAI,CAACyC,WAAW,EAAER,IAAI,CAAC;cAC5F,CAAC,MAAM;gBACL;gBACA;gBACA,MAAM,IAAIhB,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;cAC9C;YACF,CAAC,CAAC,OAAOM,CAAC,EAAE;cACV,IACEA,CAAC,CAAC/B,IAAI,KAAK,YAAY;cACvB;cACA+B,CAAC,CAAC/B,IAAI,KAAK,gBAAgB;cAC3B;cACA+B,CAAC,CAAC/B,IAAI,KAAK,qBAAqB,IAChC+B,CAAC,CAAC/B,IAAI,KAAK,gBAAgB,EAC3B;gBACA,MAAM,IAAIV,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAEM,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;cAC3C,CAAC,MAAM;gBACL,MAAMD,CAAC;cACT;YACF;UACF,CAAC;QACH,CAAC,CAAC;QAEF1D,IAAI,CAACgB,WAAW,CAAC4C,OAAO,CAACxC,CAAC,CAAC;MAC7B;IACF,CAAC;IAED1B,mBAAmB,CAACmE,YAAY,GAAG,UAAUC,MAAM,EAAE;MACnD,MAAM9D,IAAI,GAAG,IAAI;MAEjB,IAAI,CAACA,IAAI,CAACI,WAAW,CAACS,cAAc,EAAE;QACpC,IAAIiD,MAAM,EAAE;UACV,MAAMC,KAAK,GAAGzE,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;UACjC,MAAMC,GAAG,GAAGC,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAAC7C,OAAO,CAACM,IAAI,IAAIoC,KAAK,CAACpC,IAAI,CAAC,GAAG,CAAC,CAAC;UACpEsC,GAAG,CAACjE,IAAI,CAACI,WAAW,CAACQ,KAAK,CAAC;UAC3BqD,GAAG,CAACH,MAAM,CAAC;UACX9D,IAAI,CAACI,WAAW,CAACQ,KAAK,GAAGtB,MAAM,CAAC6E,IAAI,CAACJ,KAAK,CAAC;QAC7C,CAAC,MAAM;UACL/D,IAAI,CAACI,WAAW,CAACS,cAAc,GAAG,IAAI;UACtC;UACAb,IAAI,CAACI,WAAW,CAACQ,KAAK,GAAG,IAAI;QAC/B;MACF;IACF,CAAC;IAEDlB,mBAAmB,CAAC8D,WAAW,GAAG,YAAY;MAC5C,MAAMxD,IAAI,GAAG,IAAI;MACjB,IAAIA,IAAI,CAACE,SAAS,KAAKC,SAAS,EAC9B,OAAO,CAAC,CAACiE,OAAO,CAACC,QAAQ;MAC3B,OAAOrE,IAAI,CAACE,SAAS;IACvB,CAAC;IAED,eAAeoE,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAE;MACzC,KAAK,IAAIC,IAAI,IAAIF,KAAK,EAAE;QACtB,IAAI,MAAMC,SAAS,CAACC,IAAI,CAAC,EAAE;UACzB,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd;IAEA,eAAeC,UAAUA,CAACH,KAAK,EAAEC,SAAS,EAAE;MAC1C,KAAK,IAAIC,IAAI,IAAIF,KAAK,EAAE;QACtB,IAAI,EAAC,MAAMC,SAAS,CAACC,IAAI,CAAC,GAAE;UAC1B,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;IAEA/E,mBAAmB,CAACiF,qBAAqB,GAAG,gBAAerB,MAAM,EAAEsB,GAAG,EACXxC,WAAW,EAAE;MACtE,MAAMpC,IAAI,GAAG,IAAI;MACjB;MACA;MACA,IAAI,MAAMsE,SAAS,CAACtE,IAAI,CAACI,WAAW,CAACC,MAAM,CAACP,IAAI,EAAE,MAAO+E,SAAS,IAAK;QACrE,MAAMC,MAAM,GAAGD,SAAS,CAACvB,MAAM,EAAEyB,aAAa,CAACF,SAAS,EAAED,GAAG,EAAExC,WAAW,CAAC,CAAC;QAC5E,OAAOnB,MAAM,CAAC+D,UAAU,CAACF,MAAM,CAAC,GAAG,MAAMA,MAAM,GAAGA,MAAM;MAC1D,CAAC,CAAC,EAAE;QACF,MAAM,IAAI7D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;MAC9C;MACA;;MAEA,IAAI,MAAMsB,UAAU,CAAC1E,IAAI,CAACI,WAAW,CAACC,MAAM,CAACV,KAAK,EAAE,MAAOkF,SAAS,IAAK;QACvE,MAAMC,MAAM,GAAGD,SAAS,CAACvB,MAAM,EAAEyB,aAAa,CAACF,SAAS,EAAED,GAAG,EAAExC,WAAW,CAAC,CAAC;QAC5E,OAAO,EAAEnB,MAAM,CAAC+D,UAAU,CAACF,MAAM,CAAC,GAAG,MAAMA,MAAM,GAAGA,MAAM,CAAC;MAC7D,CAAC,CAAC,EAAE;QACF,MAAM,IAAI7D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;MAC9C;;MAEA;MACA;MACA,IAAIhB,WAAW,KAAK,IAAI,EACtBwC,GAAG,CAACpC,GAAG,GAAGJ,WAAW;MAEvB,OAAOpC,IAAI,CAACyC,WAAW,CAACjC,WAAW,CAAC6B,IAAI,CAACrC,IAAI,CAACyC,WAAW,EAAEmC,GAAG,CAAC;IACjE,CAAC;;IAED;IACA;IACA;IACA;IACAlF,mBAAmB,CAACuF,qBAAqB,GAAG,gBACxC3B,MAAM,EAAE4B,QAAQ,EAAEC,OAAO,EAAEvF,OAAO,EAAE;MACtC,MAAMI,IAAI,GAAG,IAAI;MAEjB6B,KAAK,CAACsD,OAAO,EAAE7F,MAAM,CAAC;MAEtBM,OAAO,GAAGN,MAAM,CAAC8F,MAAM,CAAC9F,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC,EAAEpE,OAAO,CAAC;MAErD,IAAI,CAACyF,eAAe,CAACC,4BAA4B,CAACJ,QAAQ,CAAC,EACzD,MAAM,IAAI9B,KAAK,CAAC,2CAA2C,CAAC;;MAE9D;MACA;MACA,IAAIxD,OAAO,CAAC2F,MAAM,EAChB,MAAM,IAAItE,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,6BAA6B,GAClC,qCAAqC,CAAC;MAE/D,MAAMoC,cAAc,GAAG,wDAAwD,GACzE,yEAAyE,GACzE,YAAY;MAElB,MAAMC,WAAW,GAAGnG,MAAM,CAAC6E,IAAI,CAACgB,OAAO,CAAC;;MAExC;MACA,MAAMO,cAAc,GAAG,CAAC,CAAC;MAEzB,IAAID,WAAW,CAACtC,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIlC,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAEoC,cAAc,CAAC;MAC7C;MACAC,WAAW,CAACpE,OAAO,CAAEsE,EAAE,IAAK;QAC1B,MAAMC,MAAM,GAAGT,OAAO,CAACQ,EAAE,CAAC;QAC1B,IAAIA,EAAE,CAAC5C,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACxB,MAAM,IAAI9B,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAEoC,cAAc,CAAC;QAC7C,CAAC,MAAM,IAAI,CAACnG,MAAM,CAACgD,IAAI,CAACwD,yBAAyB,EAAEF,EAAE,CAAC,EAAE;UACtD,MAAM,IAAI1E,MAAM,CAACmC,KAAK,CACpB,GAAG,EAAE,0BAA0B,GAAGuC,EAAE,GAAG,0CAA0C,CAAC;QACtF,CAAC,MAAM;UACLrG,MAAM,CAAC6E,IAAI,CAACyB,MAAM,CAAC,CAACvE,OAAO,CAAEyE,KAAK,IAAK;YACrC;YACA;YACA,IAAIA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAC3BD,KAAK,GAAGA,KAAK,CAACE,SAAS,CAAC,CAAC,EAAEF,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,CAAC;;YAEhD;YACAL,cAAc,CAACI,KAAK,CAAC,GAAG,IAAI;UAC9B,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMhC,MAAM,GAAGxE,MAAM,CAAC6E,IAAI,CAACuB,cAAc,CAAC;MAE1C,MAAMO,WAAW,GAAG;QAACC,SAAS,EAAE;MAAI,CAAC;MACrC,IAAI,CAAClG,IAAI,CAACI,WAAW,CAACS,cAAc,EAAE;QACpCoF,WAAW,CAACnC,MAAM,GAAG,CAAC,CAAC;QACvB9D,IAAI,CAACI,WAAW,CAACQ,KAAK,CAACS,OAAO,CAAE8E,SAAS,IAAK;UAC5CF,WAAW,CAACnC,MAAM,CAACqC,SAAS,CAAC,GAAG,CAAC;QACnC,CAAC,CAAC;MACJ;MAEA,MAAMvB,GAAG,GAAG,MAAM5E,IAAI,CAACyC,WAAW,CAAC2D,YAAY,CAAClB,QAAQ,EAAEe,WAAW,CAAC;MACtE,IAAI,CAACrB,GAAG;QAAG;QACT,OAAO,CAAC;;MAEV;MACA;MACA,IAAI,MAAMN,SAAS,CAACtE,IAAI,CAACI,WAAW,CAACE,MAAM,CAACR,IAAI,EAAE,MAAO+E,SAAS,IAAK;QACrE,MAAMwB,YAAY,GAAGC,YAAY,CAACzB,SAAS,EAAED,GAAG,CAAC;QACjD,MAAME,MAAM,GAAGD,SAAS,CAACvB,MAAM,EAC7B+C,YAAY,EACZvC,MAAM,EACNqB,OAAO,CAAC;QACV,OAAOlE,MAAM,CAAC+D,UAAU,CAACF,MAAM,CAAC,GAAG,MAAMA,MAAM,GAAGA,MAAM;MAC1D,CAAC,CAAC,EAAE;QACF,MAAM,IAAI7D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;MAC9C;;MAEA;MACA,IAAI,MAAMsB,UAAU,CAAC1E,IAAI,CAACI,WAAW,CAACE,MAAM,CAACX,KAAK,EAAE,MAAOkF,SAAS,IAAK;QACvE,MAAMwB,YAAY,GAAGC,YAAY,CAACzB,SAAS,EAAED,GAAG,CAAC;QACjD,MAAME,MAAM,GAAGD,SAAS,CAACvB,MAAM,EAC7B+C,YAAY,EACZvC,MAAM,EACNqB,OAAO,CAAC;QACV,OAAO,EAAElE,MAAM,CAAC+D,UAAU,CAACF,MAAM,CAAC,GAAG,MAAMA,MAAM,GAAGA,MAAM,CAAC;MAC7D,CAAC,CAAC,EAAE;QACF,MAAM,IAAI7D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;MAC9C;MAEAxD,OAAO,CAAC2G,cAAc,GAAG,IAAI;;MAE7B;MACA;MACA;MACA;;MAEA,OAAOvG,IAAI,CAACyC,WAAW,CAAChC,WAAW,CAAC4B,IAAI,CACtCrC,IAAI,CAACyC,WAAW,EAAEyC,QAAQ,EAAEC,OAAO,EAAEvF,OAAO,CAAC;IACjD,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA,MAAMiG,yBAAyB,GAAG;MAChCW,IAAI,EAAC,CAAC;MAAEC,IAAI,EAAC,CAAC;MAAEC,MAAM,EAAC,CAAC;MAAEC,SAAS,EAAC,CAAC;MAAEC,IAAI,EAAC,CAAC;MAAEC,QAAQ,EAAC,CAAC;MAAEC,KAAK,EAAC,CAAC;MAClEC,QAAQ,EAAC,CAAC;MAAEC,KAAK,EAAC,CAAC;MAAEC,IAAI,EAAC;IAC5B,CAAC;;IAED;IACA;IACAvH,mBAAmB,CAACwH,qBAAqB,GAAG,gBAAe5D,MAAM,EAAE4B,QAAQ,EAAE;MAC3E,MAAMlF,IAAI,GAAG,IAAI;MAEjB,MAAMiG,WAAW,GAAG;QAACC,SAAS,EAAE;MAAI,CAAC;MACrC,IAAI,CAAClG,IAAI,CAACI,WAAW,CAACS,cAAc,EAAE;QACpCoF,WAAW,CAACnC,MAAM,GAAG,CAAC,CAAC;QACvB9D,IAAI,CAACI,WAAW,CAACQ,KAAK,CAACS,OAAO,CAAE8E,SAAS,IAAK;UAC5CF,WAAW,CAACnC,MAAM,CAACqC,SAAS,CAAC,GAAG,CAAC;QACnC,CAAC,CAAC;MACJ;MAEA,MAAMvB,GAAG,GAAG,MAAM5E,IAAI,CAACyC,WAAW,CAAC2D,YAAY,CAAClB,QAAQ,EAAEe,WAAW,CAAC;MACtE,IAAI,CAACrB,GAAG,EACN,OAAO,CAAC;;MAEV;MACA;MACA,IAAI,MAAMN,SAAS,CAACtE,IAAI,CAACI,WAAW,CAACG,MAAM,CAACT,IAAI,EAAE,MAAO+E,SAAS,IAAK;QACrE,MAAMC,MAAM,GAAGD,SAAS,CAACvB,MAAM,EAAEgD,YAAY,CAACzB,SAAS,EAAED,GAAG,CAAC,CAAC;QAC9D,OAAO3D,MAAM,CAAC+D,UAAU,CAACF,MAAM,CAAC,GAAG,MAAMA,MAAM,GAAGA,MAAM;MAC1D,CAAC,CAAC,EAAE;QACF,MAAM,IAAI7D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;MAC9C;MACA;MACA,IAAI,MAAMsB,UAAU,CAAC1E,IAAI,CAACI,WAAW,CAACG,MAAM,CAACZ,KAAK,EAAE,MAAOkF,SAAS,IAAK;QACvE,MAAMC,MAAM,GAAGD,SAAS,CAACvB,MAAM,EAAEgD,YAAY,CAACzB,SAAS,EAAED,GAAG,CAAC,CAAC;QAC9D,OAAO,EAAE3D,MAAM,CAAC+D,UAAU,CAACF,MAAM,CAAC,GAAG,MAAMA,MAAM,GAAGA,MAAM,CAAC;MAC7D,CAAC,CAAC,EAAE;QACF,MAAM,IAAI7D,MAAM,CAACmC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC;MAC9C;;MAEA;MACA;MACA;MACA;;MAEA,OAAOpD,IAAI,CAACyC,WAAW,CAAC/B,WAAW,CAAC2B,IAAI,CAACrC,IAAI,CAACyC,WAAW,EAAEyC,QAAQ,CAAC;IACtE,CAAC;IAEDxF,mBAAmB,CAACyH,uBAAuB,GAAG,SAASA,uBAAuBA,CAACxF,IAAI,EAAEM,IAAI,EAAgB;MAAA,IAAdrC,OAAO,GAAAkC,SAAA,CAAAqB,MAAA,QAAArB,SAAA,QAAA3B,SAAA,GAAA2B,SAAA,MAAG,CAAC,CAAC;MAErG;MACA,MAAMsF,kBAAkB,GAAGzF,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,aAAa;MAC3E,IAAIyF,kBAAkB,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;QAChD;QACA;QACA;QACA1E,sBAAsB,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEN,IAAI,CAAC;MACvC;MAEA,MAAM2F,iBAAiB,GAAG,IAAI,CAACvG,OAAO,GAAGY,IAAI;MAC7C,OAAO,IAAI,CAACX,WAAW,CAACuG,UAAU,CAACD,iBAAiB,EAAErF,IAAI,EAAAlD,aAAA;QACxDyI,eAAe,EAAE,IAAI,CAACC,YAAY,KAAK,MAAM,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI;QAC1E;QACAC,yBAAyB,EAAE,CAAC,IAAI,CAAC1G,WAAW,CAAC2G,OAAO,CAACC,OAAO,IAAI,IAAI,CAACH,YAAY,KAAK;MAAM,GACzF7H,OAAO,CACX,CAAC;IACJ,CAAC;IAEDF,mBAAmB,CAACmI,kBAAkB,GAAG,SAASA,kBAAkBA,CAAClG,IAAI,EAAEM,IAAI,EAAE6F,QAAQ,EAAE;MACzF,IAAI7G,MAAM,CAACE,QAAQ,IAAI,CAAC2G,QAAQ,IAAI,CAACT,mBAAmB,CAAC,CAAC,EAAE;QAC1D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAS,QAAQ,GAAG,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,EACL9G,MAAM,CAAC+G,MAAM,CAACrG,IAAI,GAAG,SAAS,EAAEoG,GAAG,CAAC;QACxC,CAAC;MACH;;MAEA;MACA,MAAMX,kBAAkB,GAAGzF,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;MACjE,IAAIyF,kBAAkB,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;QAChD;QACA;QACA;QACA1E,sBAAsB,CAACV,IAAI,CAAC,CAAC,CAAC,EAAEN,IAAI,CAAC;MACvC;MAEA,MAAM2F,iBAAiB,GAAG,IAAI,CAACvG,OAAO,GAAGY,IAAI;MAC7C,OAAO,IAAI,CAACX,WAAW,CAAC0B,KAAK,CAC3B4E,iBAAiB,EAAErF,IAAI,EAAE;QAAEuF,eAAe,EAAE;MAAK,CAAC,EAAEM,QAAQ,CAAC;IACjE,CAAC;IAED,SAASxB,YAAYA,CAACzB,SAAS,EAAED,GAAG,EAAE;MACpC,IAAIC,SAAS,CAACqB,SAAS,EACrB,OAAOrB,SAAS,CAACqB,SAAS,CAACtB,GAAG,CAAC;MACjC,OAAOA,GAAG;IACZ;IAEA,SAASG,aAAaA,CAACF,SAAS,EAAED,GAAG,EAAExC,WAAW,EAAE;MAClD,IAAI6F,GAAG,GAAGrD,GAAG;MACb,IAAIC,SAAS,CAACqB,SAAS,EAAE;QACvB+B,GAAG,GAAGC,KAAK,CAACC,KAAK,CAACvD,GAAG,CAAC;QACtB;QACA;QACA;QACA;QACA;QACA,IAAIxC,WAAW,KAAK,IAAI,EAAE;UACxB6F,GAAG,CAACzF,GAAG,GAAGJ,WAAW;QACvB;QACA6F,GAAG,GAAGpD,SAAS,CAACqB,SAAS,CAAC+B,GAAG,CAAC;MAChC;MACA,OAAOA,GAAG;IACZ;IAEA,SAASpI,YAAYA,CAACuI,UAAU,EAAEC,WAAW,EAAEzI,OAAO,EAAE;MACtD;MACA,MAAM0I,cAAc,GAAG,gFAAgF;MACvGhJ,MAAM,CAAC6E,IAAI,CAACvE,OAAO,CAAC,CAACyB,OAAO,CAAEkH,GAAG,IAAK;QACpC,IAAI,CAACD,cAAc,CAACE,IAAI,CAACD,GAAG,CAAC,EAC3B,MAAM,IAAInF,KAAK,CAACiF,WAAW,GAAG,iBAAiB,GAAGE,GAAG,CAAC;;QAExD;QACA,MAAME,UAAU,GAAGF,GAAG,CAAC3G,QAAQ,CAAC,OAAO,CAAC;QACxC,IAAI6G,UAAU,EAAE;UACd,MAAMC,OAAO,GAAGH,GAAG,CAAC1F,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;UACxC5B,MAAM,CAAC0H,SAAS,CAACN,WAAW,cAAAO,MAAA,CAAaL,GAAG,kCAAAK,MAAA,CAA6BF,OAAO,gBAAY,CAAC;QAC/F;MACF,CAAC,CAAC;MAEFN,UAAU,CAACnI,WAAW,GAAG,IAAI;MAE7B,CACE,aAAa,EACb,aAAa,EACb,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,QAAQ,CACT,CAACoB,OAAO,CAACM,IAAI,IAAI;QAChB,IAAItC,MAAM,CAACgD,IAAI,CAACzC,OAAO,EAAE+B,IAAI,CAAC,EAAE;UAC9B,IAAI,EAAE/B,OAAO,CAAC+B,IAAI,CAAC,YAAYkH,QAAQ,CAAC,EAAE;YACxC,MAAM,IAAIzF,KAAK,CACbiF,WAAW,GAAG,eAAe,GAAG1G,IAAI,GAAG,sBACzC,CAAC;UACH;;UAEA;UACA;UACA;UACA,IAAI/B,OAAO,CAACsG,SAAS,KAAK/F,SAAS,EAAE;YACnCP,OAAO,CAAC+B,IAAI,CAAC,CAACuE,SAAS,GAAGkC,UAAU,CAACU,UAAU,CAAC,CAAC;UACnD,CAAC,MAAM;YACLlJ,OAAO,CAAC+B,IAAI,CAAC,CAACuE,SAAS,GAAGb,eAAe,CAAC0D,aAAa,CACrDnJ,OAAO,CAACsG,SACV,CAAC;UACH;UACA,MAAM8C,WAAW,GAAGrH,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC;UAC1C,MAAMqH,iBAAiB,GAAGD,WAAW,GAAGrH,IAAI,CAACkB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAGlB,IAAI;UACxEyG,UAAU,CAAChI,WAAW,CAAC6I,iBAAiB,CAAC,CAACZ,WAAW,CAAC,CAAC9E,IAAI,CAAC3D,OAAO,CAAC+B,IAAI,CAAC,CAAC;QAC5E;MACF,CAAC,CAAC;;MAEF;MACA;MACA;MACA,IAAI/B,OAAO,CAACa,WAAW,IAAIb,OAAO,CAACc,WAAW,IAAId,OAAO,CAACgB,KAAK,EAAE;QAC/D,IAAIhB,OAAO,CAACgB,KAAK,IAAI,EAAEhB,OAAO,CAACgB,KAAK,YAAYsB,KAAK,CAAC,EAAE;UACtD,MAAM,IAAIkB,KAAK,CAACiF,WAAW,GAAG,sCAAsC,CAAC;QACvE;QACAD,UAAU,CAACvE,YAAY,CAACjE,OAAO,CAACgB,KAAK,CAAC;MACxC;IACF;IAEA,SAAS+B,sBAAsBA,CAACuC,QAAQ,EAAE3D,UAAU,EAAE;MACpD,IAAI,CAAC8D,eAAe,CAACC,4BAA4B,CAACJ,QAAQ,CAAC,EAAE;QAC3D,MAAM,IAAIjE,MAAM,CAACmC,KAAK,CACpB,GAAG,EAAE,yCAAyC,GAAG7B,UAAU,GACzD,mBAAmB,CAAC;MAC1B;IACF;IAAC;;IAED;IACA,SAAS8F,mBAAmBA,CAAA,EAAG;MAC7B,IAAI6B,iBAAiB,GACnBC,GAAG,CAACC,wBAAwB;MAC5B;MACA;MACAD,GAAG,CAACE,kBAAkB;MAExB,MAAMC,SAAS,GAAGJ,iBAAiB,CAACK,GAAG,CAAC,CAAC;MACzC,OAAOD,SAAS,IAAIA,SAAS,CAAC/G,YAAY;IAC5C;IAACiH,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAxJ,IAAA;EAAA0J,KAAA;AAAA,G", "file": "/packages/allow-deny.js", "sourcesContent": ["///\n/// Remote methods and access control.\n///\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\n// Restrict default mutators on collection. allow() and deny() take the\n// same options:\n//\n// options.insertAsync {Function(userId, doc)}\n//   return true to allow/deny adding this document\n//\n// options.updateAsync {Function(userId, docs, fields, modifier)}\n//   return true to allow/deny updating these documents.\n//   `fields` is passed as an array of fields that are to be modified\n//\n// options.removeAsync {Function(userId, docs)}\n//   return true to allow/deny removing these documents\n//\n// options.fetch {Array}\n//   Fields to fetch for these validators. If any call to allow or deny\n//   does not have this option then all fields are loaded.\n//\n// allow and deny can be called multiple times. The validators are\n// evaluated as follows:\n// - If neither deny() nor allow() has been called on the collection,\n//   then the request is allowed if and only if the \"insecure\" smart\n//   package is in use.\n// - Otherwise, if any deny() function returns true, the request is denied.\n// - Otherwise, if any allow() function returns true, the request is allowed.\n// - Otherwise, the request is denied.\n//\n// Meteor may call your deny() and allow() functions in any order, and may not\n// call all of them if it is able to make a decision without calling them all\n// (so don't include side effects).\n\nAllowDeny = {\n  CollectionPrototype: {}\n};\n\n// In the `mongo` package, we will extend Mongo.Collection.prototype with these\n// methods\nconst CollectionPrototype = AllowDeny.CollectionPrototype;\n\n/**\n * @summary Allow users to write directly to this collection from client code, subject to limitations you define.\n * @locus Server\n * @method allow\n * @memberOf Mongo.Collection\n * @instance\n * @param {Object} options\n * @param {Function} options.insert,update,remove Functions that look at a proposed modification to the database and return true if it should be allowed.\n * @param {String[]} options.fetch Optional performance enhancement. Limits the fields that will be fetched from the database for inspection by your `update` and `remove` functions.\n * @param {Function} options.transform Overrides `transform` on the  [`Collection`](#collections).  Pass `null` to disable transformation.\n */\nCollectionPrototype.allow = function(options) {\n  addValidator(this, 'allow', options);\n};\n\n/**\n * @summary Override `allow` rules.\n * @locus Server\n * @method deny\n * @memberOf Mongo.Collection\n * @instance\n * @param {Object} options\n * @param {Function} options.insert,update,remove Functions that look at a proposed modification to the database and return true if it should be denied, even if an [allow](#allow) rule says otherwise.\n * @param {String[]} options.fetch Optional performance enhancement. Limits the fields that will be fetched from the database for inspection by your `update` and `remove` functions.\n * @param {Function} options.transform Overrides `transform` on the  [`Collection`](#collections).  Pass `null` to disable transformation.\n */\nCollectionPrototype.deny = function(options) {\n  addValidator(this, 'deny', options);\n};\n\nCollectionPrototype._defineMutationMethods = function(options) {\n  const self = this;\n  options = options || {};\n\n  // set to true once we call any allow or deny methods. If true, use\n  // allow/deny semantics. If false, use insecure mode semantics.\n  self._restricted = false;\n\n  // Insecure mode (default to allowing writes). Defaults to 'undefined' which\n  // means insecure iff the insecure package is loaded. This property can be\n  // overriden by tests or packages wishing to change insecure mode behavior of\n  // their collections.\n  self._insecure = undefined;\n\n  self._validators = {\n    insert: {allow: [], deny: []},\n    update: {allow: [], deny: []},\n    remove: {allow: [], deny: []},\n    insertAsync: {allow: [], deny: []},\n    updateAsync: {allow: [], deny: []},\n    removeAsync: {allow: [], deny: []},\n    upsertAsync: {allow: [], deny: []}, // dummy arrays; can't set these!\n    fetch: [],\n    fetchAllFields: false\n  };\n\n  if (!self._name)\n    return; // anonymous collection\n\n  // XXX Think about method namespacing. Maybe methods should be\n  // \"Meteor:Mongo:insertAsync/NAME\"?\n  self._prefix = '/' + self._name + '/';\n\n  // Mutation Methods\n  // Minimongo on the server gets no stubs; instead, by default\n  // it wait()s until its result is ready, yielding.\n  // This matches the behavior of macromongo on the server better.\n  // XXX see #MeteorServerNull\n  if (self._connection && (self._connection === Meteor.server || Meteor.isClient)) {\n    const m = {};\n\n    [\n      'insertAsync',\n      'updateAsync',\n      'removeAsync',\n      'insert',\n      'update',\n      'remove',\n    ].forEach(method => {\n      const methodName = self._prefix + method;\n\n      if (options.useExisting) {\n        const handlerPropName = Meteor.isClient\n          ? '_methodHandlers'\n          : 'method_handlers';\n        // Do not try to create additional methods if this has already been called.\n        // (Otherwise the .methods() call below will throw an error.)\n        if (\n          self._connection[handlerPropName] &&\n          typeof self._connection[handlerPropName][methodName] === 'function'\n        )\n          return;\n      }\n\n      const isInsert = name => name.includes('insert');\n\n      m[methodName] = function (/* ... */) {\n        // All the methods do their own validation, instead of using check().\n        check(arguments, [Match.Any]);\n        const args = Array.from(arguments);\n        try {\n          // For an insert/insertAsync, if the client didn't specify an _id, generate one\n          // now; because this uses DDP.randomStream, it will be consistent with\n          // what the client generated. We generate it now rather than later so\n          // that if (eg) an allow/deny rule does an insert/insertAsync to the same\n          // collection (not that it really should), the generated _id will\n          // still be the first use of the stream and will be consistent.\n          //\n          // However, we don't actually stick the _id onto the document yet,\n          // because we want allow/deny rules to be able to differentiate\n          // between arbitrary client-specified _id fields and merely\n          // client-controlled-via-randomSeed fields.\n          let generatedId = null;\n          if (isInsert(method) && !hasOwn.call(args[0], '_id')) {\n            generatedId = self._makeNewID();\n          }\n\n          if (this.isSimulation) {\n            // In a client simulation, you can do any mutation (even with a\n            // complex selector).\n            if (generatedId !== null) {\n              args[0]._id = generatedId;\n            }\n            return self._collection[method].apply(self._collection, args);\n          }\n\n          // This is the server receiving a method call from the client.\n\n          // We don't allow arbitrary selectors in mutations from the client: only\n          // single-ID selectors.\n          if (!isInsert(method)) throwIfSelectorIsNotId(args[0], method);\n\n          const syncMethodName = method.replace('Async', '');\n          const syncValidatedMethodName = '_validated' + method.charAt(0).toUpperCase() + syncMethodName.slice(1);\n          // it forces to use async validated behavior\n          const validatedMethodName = syncValidatedMethodName + 'Async';\n\n          if (self._restricted) {\n            // short circuit if there is no way it will pass.\n            if (self._validators[syncMethodName].allow.length === 0) {\n              throw new Meteor.Error(\n                403,\n                'Access denied. No allow validators set on restricted ' +\n                  \"collection for method '\" +\n                  method +\n                  \"'.\"\n              );\n            }\n\n            args.unshift(this.userId);\n            isInsert(method) && args.push(generatedId);\n            return self[validatedMethodName].apply(self, args);\n          } else if (self._isInsecure()) {\n            if (generatedId !== null) args[0]._id = generatedId;\n            // In insecure mode we use the server _collection methods, and these sync methods\n            // do not exist in the server anymore, so we have this mapper to call the async methods\n            // instead.\n            const syncMethodsMapper = {\n              insert: \"insertAsync\",\n              update: \"updateAsync\",\n              remove: \"removeAsync\",\n            };\n\n\n            // In insecure mode, allow any mutation (with a simple selector).\n            // XXX This is kind of bogus.  Instead of blindly passing whatever\n            //     we get from the network to this function, we should actually\n            //     know the correct arguments for the function and pass just\n            //     them.  For example, if you have an extraneous extra null\n            //     argument and this is Mongo on the server, the .wrapAsync'd\n            //     functions like update will get confused and pass the\n            //     \"fut.resolver()\" in the wrong slot, where _update will never\n            //     invoke it. Bam, broken DDP connection.  Probably should just\n            //     take this whole method and write it three times, invoking\n            //     helpers for the common code.\n            return self._collection[syncMethodsMapper[method] || method].apply(self._collection, args);\n          } else {\n            // In secure mode, if we haven't called allow or deny, then nothing\n            // is permitted.\n            throw new Meteor.Error(403, 'Access denied');\n          }\n        } catch (e) {\n          if (\n            e.name === 'MongoError' ||\n            // for old versions of MongoDB (probably not necessary but it's here just in case)\n            e.name === 'BulkWriteError' ||\n            // for newer versions of MongoDB (https://docs.mongodb.com/drivers/node/current/whats-new/#bulkwriteerror---mongobulkwriteerror)\n            e.name === 'MongoBulkWriteError' ||\n            e.name === 'MinimongoError'\n          ) {\n            throw new Meteor.Error(409, e.toString());\n          } else {\n            throw e;\n          }\n        }\n      };\n    });\n\n    self._connection.methods(m);\n  }\n};\n\nCollectionPrototype._updateFetch = function (fields) {\n  const self = this;\n\n  if (!self._validators.fetchAllFields) {\n    if (fields) {\n      const union = Object.create(null);\n      const add = names => names && names.forEach(name => union[name] = 1);\n      add(self._validators.fetch);\n      add(fields);\n      self._validators.fetch = Object.keys(union);\n    } else {\n      self._validators.fetchAllFields = true;\n      // clear fetch just to make sure we don't accidentally read it\n      self._validators.fetch = null;\n    }\n  }\n};\n\nCollectionPrototype._isInsecure = function () {\n  const self = this;\n  if (self._insecure === undefined)\n    return !!Package.insecure;\n  return self._insecure;\n};\n\nasync function asyncSome(array, predicate) {\n  for (let item of array) {\n    if (await predicate(item)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nasync function asyncEvery(array, predicate) {\n  for (let item of array) {\n    if (!await predicate(item)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nCollectionPrototype._validatedInsertAsync = async function(userId, doc,\n                                                           generatedId) {\n  const self = this;\n  // call user validators.\n  // Any deny returns true means denied.\n  if (await asyncSome(self._validators.insert.deny, async (validator) => {\n    const result = validator(userId, docToValidate(validator, doc, generatedId));\n    return Meteor._isPromise(result) ? await result : result;\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n  // Any allow returns true means proceed. Throw error if they all fail.\n\n  if (await asyncEvery(self._validators.insert.allow, async (validator) => {\n    const result = validator(userId, docToValidate(validator, doc, generatedId));\n    return !(Meteor._isPromise(result) ? await result : result);\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  // If we generated an ID above, insertAsync it now: after the validation, but\n  // before actually inserting.\n  if (generatedId !== null)\n    doc._id = generatedId;\n\n  return self._collection.insertAsync.call(self._collection, doc);\n};\n\n// Simulate a mongo `update` operation while validating that the access\n// control rules set by calls to `allow/deny` are satisfied. If all\n// pass, rewrite the mongo operation to use $in to set the list of\n// document ids to change ##ValidatedChange\nCollectionPrototype._validatedUpdateAsync = async function(\n    userId, selector, mutator, options) {\n  const self = this;\n\n  check(mutator, Object);\n\n  options = Object.assign(Object.create(null), options);\n\n  if (!LocalCollection._selectorIsIdPerhapsAsObject(selector))\n    throw new Error(\"validated update should be of a single ID\");\n\n  // We don't support upserts because they don't fit nicely into allow/deny\n  // rules.\n  if (options.upsert)\n    throw new Meteor.Error(403, \"Access denied. Upserts not \" +\n                           \"allowed in a restricted collection.\");\n\n  const noReplaceError = \"Access denied. In a restricted collection you can only\" +\n        \" update documents, not replace them. Use a Mongo update operator, such \" +\n        \"as '$set'.\";\n\n  const mutatorKeys = Object.keys(mutator);\n\n  // compute modified fields\n  const modifiedFields = {};\n\n  if (mutatorKeys.length === 0) {\n    throw new Meteor.Error(403, noReplaceError);\n  }\n  mutatorKeys.forEach((op) => {\n    const params = mutator[op];\n    if (op.charAt(0) !== '$') {\n      throw new Meteor.Error(403, noReplaceError);\n    } else if (!hasOwn.call(ALLOWED_UPDATE_OPERATIONS, op)) {\n      throw new Meteor.Error(\n        403, \"Access denied. Operator \" + op + \" not allowed in a restricted collection.\");\n    } else {\n      Object.keys(params).forEach((field) => {\n        // treat dotted fields as if they are replacing their\n        // top-level part\n        if (field.indexOf('.') !== -1)\n          field = field.substring(0, field.indexOf('.'));\n\n        // record the field we are trying to change\n        modifiedFields[field] = true;\n      });\n    }\n  });\n\n  const fields = Object.keys(modifiedFields);\n\n  const findOptions = {transform: null};\n  if (!self._validators.fetchAllFields) {\n    findOptions.fields = {};\n    self._validators.fetch.forEach((fieldName) => {\n      findOptions.fields[fieldName] = 1;\n    });\n  }\n\n  const doc = await self._collection.findOneAsync(selector, findOptions);\n  if (!doc)  // none satisfied!\n    return 0;\n\n  // call user validators.\n  // Any deny returns true means denied.\n  if (await asyncSome(self._validators.update.deny, async (validator) => {\n    const factoriedDoc = transformDoc(validator, doc);\n    const result = validator(userId,\n      factoriedDoc,\n      fields,\n      mutator);\n    return Meteor._isPromise(result) ? await result : result;\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  // Any allow returns true means proceed. Throw error if they all fail.\n  if (await asyncEvery(self._validators.update.allow, async (validator) => {\n    const factoriedDoc = transformDoc(validator, doc);\n    const result = validator(userId,\n      factoriedDoc,\n      fields,\n      mutator);\n    return !(Meteor._isPromise(result) ? await result : result);\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  options._forbidReplace = true;\n\n  // Back when we supported arbitrary client-provided selectors, we actually\n  // rewrote the selector to include an _id clause before passing to Mongo to\n  // avoid races, but since selector is guaranteed to already just be an ID, we\n  // don't have to any more.\n\n  return self._collection.updateAsync.call(\n    self._collection, selector, mutator, options);\n};\n\n// Only allow these operations in validated updates. Specifically\n// whitelist operations, rather than blacklist, so new complex\n// operations that are added aren't automatically allowed. A complex\n// operation is one that does more than just modify its target\n// field. For now this contains all update operations except '$rename'.\n// http://docs.mongodb.org/manual/reference/operators/#update\nconst ALLOWED_UPDATE_OPERATIONS = {\n  $inc:1, $set:1, $unset:1, $addToSet:1, $pop:1, $pullAll:1, $pull:1,\n  $pushAll:1, $push:1, $bit:1\n};\n\n// Simulate a mongo `remove` operation while validating access control\n// rules. See #ValidatedChange\nCollectionPrototype._validatedRemoveAsync = async function(userId, selector) {\n  const self = this;\n\n  const findOptions = {transform: null};\n  if (!self._validators.fetchAllFields) {\n    findOptions.fields = {};\n    self._validators.fetch.forEach((fieldName) => {\n      findOptions.fields[fieldName] = 1;\n    });\n  }\n\n  const doc = await self._collection.findOneAsync(selector, findOptions);\n  if (!doc)\n    return 0;\n\n  // call user validators.\n  // Any deny returns true means denied.\n  if (await asyncSome(self._validators.remove.deny, async (validator) => {\n    const result = validator(userId, transformDoc(validator, doc));\n    return Meteor._isPromise(result) ? await result : result;\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n  // Any allow returns true means proceed. Throw error if they all fail.\n  if (await asyncEvery(self._validators.remove.allow, async (validator) => {\n    const result = validator(userId, transformDoc(validator, doc));\n    return !(Meteor._isPromise(result) ? await result : result);\n  })) {\n    throw new Meteor.Error(403, \"Access denied\");\n  }\n\n  // Back when we supported arbitrary client-provided selectors, we actually\n  // rewrote the selector to {_id: {$in: [ids that we found]}} before passing to\n  // Mongo to avoid races, but since selector is guaranteed to already just be\n  // an ID, we don't have to any more.\n\n  return self._collection.removeAsync.call(self._collection, selector);\n};\n\nCollectionPrototype._callMutatorMethodAsync = function _callMutatorMethodAsync(name, args, options = {}) {\n\n  // For two out of three mutator methods, the first argument is a selector\n  const firstArgIsSelector = name === \"updateAsync\" || name === \"removeAsync\";\n  if (firstArgIsSelector && !alreadyInSimulation()) {\n    // If we're about to actually send an RPC, we should throw an error if\n    // this is a non-ID selector, because the mutation methods only allow\n    // single-ID selectors. (If we don't throw here, we'll see flicker.)\n    throwIfSelectorIsNotId(args[0], name);\n  }\n\n  const mutatorMethodName = this._prefix + name;\n  return this._connection.applyAsync(mutatorMethodName, args, {\n    returnStubValue: this.resolverType === 'stub' || this.resolverType == null,\n    // StubStream is only used for testing where you don't care about the server\n    returnServerResultPromise: !this._connection._stream._isStub && this.resolverType !== 'stub',\n    ...options,\n  });\n}\n\nCollectionPrototype._callMutatorMethod = function _callMutatorMethod(name, args, callback) {\n  if (Meteor.isClient && !callback && !alreadyInSimulation()) {\n    // Client can't block, so it can't report errors by exception,\n    // only by callback. If they forget the callback, give them a\n    // default one that logs the error, so they aren't totally\n    // baffled if their writes don't work because their database is\n    // down.\n    // Don't give a default callback in simulation, because inside stubs we\n    // want to return the results from the local collection immediately and\n    // not force a callback.\n    callback = function (err) {\n      if (err)\n        Meteor._debug(name + \" failed\", err);\n    };\n  }\n\n  // For two out of three mutator methods, the first argument is a selector\n  const firstArgIsSelector = name === \"update\" || name === \"remove\";\n  if (firstArgIsSelector && !alreadyInSimulation()) {\n    // If we're about to actually send an RPC, we should throw an error if\n    // this is a non-ID selector, because the mutation methods only allow\n    // single-ID selectors. (If we don't throw here, we'll see flicker.)\n    throwIfSelectorIsNotId(args[0], name);\n  }\n\n  const mutatorMethodName = this._prefix + name;\n  return this._connection.apply(\n    mutatorMethodName, args, { returnStubValue: true }, callback);\n}\n\nfunction transformDoc(validator, doc) {\n  if (validator.transform)\n    return validator.transform(doc);\n  return doc;\n}\n\nfunction docToValidate(validator, doc, generatedId) {\n  let ret = doc;\n  if (validator.transform) {\n    ret = EJSON.clone(doc);\n    // If you set a server-side transform on your collection, then you don't get\n    // to tell the difference between \"client specified the ID\" and \"server\n    // generated the ID\", because transforms expect to get _id.  If you want to\n    // do that check, you can do it with a specific\n    // `C.allow({insertAsync: f, transform: null})` validator.\n    if (generatedId !== null) {\n      ret._id = generatedId;\n    }\n    ret = validator.transform(ret);\n  }\n  return ret;\n}\n\nfunction addValidator(collection, allowOrDeny, options) {\n  // validate keys\n  const validKeysRegEx = /^(?:insertAsync|updateAsync|removeAsync|insert|update|remove|fetch|transform)$/;\n  Object.keys(options).forEach((key) => {\n    if (!validKeysRegEx.test(key))\n      throw new Error(allowOrDeny + \": Invalid key: \" + key);\n\n    // TODO deprecated async config on future versions\n    const isAsyncKey = key.includes('Async');\n    if (isAsyncKey) {\n      const syncKey = key.replace('Async', '');\n      Meteor.deprecate(allowOrDeny + `: The \"${key}\" key is deprecated. Use \"${syncKey}\" instead.`);\n    }\n  });\n\n  collection._restricted = true;\n\n  [\n    'insertAsync',\n    'updateAsync',\n    'removeAsync',\n    'insert',\n    'update',\n    'remove',\n  ].forEach(name => {\n    if (hasOwn.call(options, name)) {\n      if (!(options[name] instanceof Function)) {\n        throw new Error(\n          allowOrDeny + ': Value for `' + name + '` must be a function'\n        );\n      }\n\n      // If the transform is specified at all (including as 'null') in this\n      // call, then take that; otherwise, take the transform from the\n      // collection.\n      if (options.transform === undefined) {\n        options[name].transform = collection._transform; // already wrapped\n      } else {\n        options[name].transform = LocalCollection.wrapTransform(\n          options.transform\n        );\n      }\n      const isAsyncName = name.includes('Async');\n      const validatorSyncName = isAsyncName ? name.replace('Async', '') : name;\n      collection._validators[validatorSyncName][allowOrDeny].push(options[name]);\n    }\n  });\n\n  // Only updateAsync the fetch fields if we're passed things that affect\n  // fetching. This way allow({}) and allow({insertAsync: f}) don't result in\n  // setting fetchAllFields\n  if (options.updateAsync || options.removeAsync || options.fetch) {\n    if (options.fetch && !(options.fetch instanceof Array)) {\n      throw new Error(allowOrDeny + \": Value for `fetch` must be an array\");\n    }\n    collection._updateFetch(options.fetch);\n  }\n}\n\nfunction throwIfSelectorIsNotId(selector, methodName) {\n  if (!LocalCollection._selectorIsIdPerhapsAsObject(selector)) {\n    throw new Meteor.Error(\n      403, \"Not permitted. Untrusted code may only \" + methodName +\n        \" documents by ID.\");\n  }\n};\n\n// Determine if we are in a DDP method simulation\nfunction alreadyInSimulation() {\n  var CurrentInvocation =\n    DDP._CurrentMethodInvocation ||\n    // For backwards compatibility, as explained in this issue:\n    // https://github.com/meteor/meteor/issues/8947\n    DDP._CurrentInvocation;\n\n  const enclosing = CurrentInvocation.get();\n  return enclosing && enclosing.isSimulation;\n}\n"]}