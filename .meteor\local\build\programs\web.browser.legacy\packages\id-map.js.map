{"version": 3, "sources": ["meteor://💻app/packages/id-map/id-map.js"], "names": ["_regeneratorRuntime", "module", "link", "default", "v", "_readOnly<PERSON><PERSON>r", "_createForOfIteratorHelperLoose", "_slicedToArray", "export", "IdMap", "idStringify", "idParse", "_map", "Map", "_idStringify", "JSON", "stringify", "_idParse", "parse", "_proto", "prototype", "get", "id", "key", "set", "value", "remove", "delete", "has", "empty", "size", "clear", "for<PERSON>ach", "iterator", "_iterator", "_step", "done", "_ref", "_ref2", "breakIfFalse", "call", "forEachAsync", "_iterator2", "_step2", "_ref3", "_ref4", "async", "forEachAsync$", "_context", "prev", "next", "awrap", "sent", "abrupt", "stop", "Promise", "<PERSON><PERSON><PERSON><PERSON>", "def", "clone", "EJSON"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,mBAAmB;AAACC,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,mBAAmB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,cAAc;AAACJ,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,cAAc,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,+BAA+B;AAACL,MAAM,CAACC,IAAI,CAAC,uDAAuD,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACE,+BAA+B,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIG,cAAc;AAACN,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACG,cAAc,GAACH,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAtfH,MAAM,CAACO,MAAM,CAAC;EAACC,KAAK,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,KAAK;EAAA;AAAC,CAAC,CAAC;AAAC,IACnCA,KAAK;EAChB,SAAAA,MAAYC,WAAW,EAAEC,OAAO,EAAE;IAChC,IAAI,CAACC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,YAAY,GAAGJ,WAAW,IAAIK,IAAI,CAACC,SAAS;IACjD,IAAI,CAACC,QAAQ,GAAGN,OAAO,IAAII,IAAI,CAACG,KAAK;EACvC;;EAEF;EACA;EACA;EACA;EAAA,IAAAC,MAAA,GAAAV,KAAA,CAAAW,SAAA;EAAAD,MAAA,CAEEE,GAAG;IAAH,SAAAA,GAAGA,CAACC,EAAE,EAAE;MACN,IAAMC,GAAG,GAAG,IAAI,CAACT,YAAY,CAACQ,EAAE,CAAC;MACjC,OAAO,IAAI,CAACV,IAAI,CAACS,GAAG,CAACE,GAAG,CAAC;IAC3B;IAAC,OAHDF,GAAG;EAAA;EAAAF,MAAA,CAKHK,GAAG;IAAH,SAAAA,GAAGA,CAACF,EAAE,EAAEG,KAAK,EAAE;MACb,IAAMF,GAAG,GAAG,IAAI,CAACT,YAAY,CAACQ,EAAE,CAAC;MACjC,IAAI,CAACV,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEE,KAAK,CAAC;IAC3B;IAAC,OAHDD,GAAG;EAAA;EAAAL,MAAA,CAKHO,MAAM;IAAN,SAAAA,MAAMA,CAACJ,EAAE,EAAE;MACT,IAAMC,GAAG,GAAG,IAAI,CAACT,YAAY,CAACQ,EAAE,CAAC;MACjC,IAAI,CAACV,IAAI,CAACe,MAAM,CAACJ,GAAG,CAAC;IACvB;IAAC,OAHDG,MAAM;EAAA;EAAAP,MAAA,CAKNS,GAAG;IAAH,SAAAA,GAAGA,CAACN,EAAE,EAAE;MACN,IAAMC,GAAG,GAAG,IAAI,CAACT,YAAY,CAACQ,EAAE,CAAC;MACjC,OAAO,IAAI,CAACV,IAAI,CAACgB,GAAG,CAACL,GAAG,CAAC;IAC3B;IAAC,OAHDK,GAAG;EAAA;EAAAT,MAAA,CAKHU,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACjB,IAAI,CAACkB,IAAI,KAAK,CAAC;IAC7B;IAAC,OAFDD,KAAK;EAAA;EAAAV,MAAA,CAILY,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAI,CAACnB,IAAI,CAACmB,KAAK,CAAC,CAAC;IACnB;IAAC,OAFDA,KAAK;EAAA,IAIL;EAAA;EAAAZ,MAAA,CACAa,OAAO;IAAP,SAAAA,OAAOA,CAACC,QAAQ,EAAE;MAChB;MACA,SAAAC,SAAA,GAAA5B,+BAAA,CAAyB,IAAI,CAACM,IAAI,GAAAuB,KAAA,IAAAA,KAAA,GAAAD,SAAA,IAAAE,IAAA,GAAC;QAAA,IAAAC,IAAA,GAAAF,KAAA,CAAAV,KAAA;QAAA,IAAAa,KAAA,GAAA/B,cAAA,CAAA8B,IAAA;QAAA,IAAzBd,GAAG,GAAAe,KAAA;QAAA,IAAEb,KAAK,GAAAa,KAAA;QAClB,IAAMC,YAAY,GAAGN,QAAQ,CAACO,IAAI,CAChC,IAAI,EACJf,KAAK,EACL,IAAI,CAACR,QAAQ,CAACM,GAAG,CACnB,CAAC;QACD,IAAIgB,YAAY,KAAK,KAAK,EAAE;UAC1B;QACF;MACF;IACF;IAAC,OAZDP,OAAO;EAAA;EAAAb,MAAA,CAcDsB,YAAY;IAAlB,SAAMA,YAAYA,CAACR,QAAQ;MAAA,IAAAS,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAtB,GAAA,EAAAE,KAAA,EAAAc,YAAA;MAAA,OAAAvC,mBAAA,CAAA8C,KAAA;QAAA,SAAAC,cAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAR,UAAA,GAAApC,+BAAA,CACA,IAAI,CAACM,IAAI;YAAA;cAAA,KAAA+B,MAAA,GAAAD,UAAA,IAAAN,IAAA;gBAAAY,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAN,KAAA,GAAAD,MAAA,CAAAlB,KAAA;cAAAoB,KAAA,GAAAtC,cAAA,CAAAqC,KAAA;cAAxBrB,GAAG,GAAAsB,KAAA;cAAEpB,KAAK,GAAAoB,KAAA;cAAAG,QAAA,CAAAE,IAAA;cAAA,OAAAlD,mBAAA,CAAAmD,KAAA,CACSlB,QAAQ,CAACO,IAAI,CACpC,IAAI,EACJf,KAAK,EACL,IAAI,CAACR,QAAQ,CAACM,GAAG,CACrB,CAAC;YAAA;cAJKgB,YAAY,GAAAS,QAAA,CAAAI,IAAA;cAAA,MAKdb,YAAY,KAAK,KAAK;gBAAAS,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAK,MAAA;YAAA;cAAAL,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAAM,IAAA;UAAA;QAAA;QAAA,OAAAP,aAAA;MAAA,uBAAAQ,OAAA;IAAA;IAI7B,OAXKd,YAAY;EAAA;EAAAtB,MAAA,CAalBW,IAAI;IAAJ,SAAAA,IAAIA,CAAA,EAAG;MACL,OAAO,IAAI,CAAClB,IAAI,CAACkB,IAAI;IACvB;IAAC,OAFDA,IAAI;EAAA;EAAAX,MAAA,CAIJqC,UAAU;IAAV,SAAAA,UAAUA,CAAClC,EAAE,EAAEmC,GAAG,EAAE;MAClB,IAAMlC,GAAG,GAAG,IAAI,CAACT,YAAY,CAACQ,EAAE,CAAC;MACjC,IAAI,IAAI,CAACV,IAAI,CAACgB,GAAG,CAACL,GAAG,CAAC,EAAE;QACtB,OAAO,IAAI,CAACX,IAAI,CAACS,GAAG,CAACE,GAAG,CAAC;MAC3B;MACA,IAAI,CAACX,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEkC,GAAG,CAAC;MACvB,OAAOA,GAAG;IACZ;IAAC,OAPDD,UAAU;EAAA,IASV;EACA;EAAA;EAAArC,MAAA,CACAuC,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAMA,KAAK,GAAG,IAAIjD,KAAK,CAAC,IAAI,CAACK,YAAY,EAAE,IAAI,CAACG,QAAQ,CAAC;MACzD;MACA,IAAI,CAACL,IAAI,CAACoB,OAAO,CAAC,UAASP,KAAK,EAAEF,GAAG,EAAC;QACpCmC,KAAK,CAAC9C,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEoC,KAAK,CAACD,KAAK,CAACjC,KAAK,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,OAAOiC,KAAK;IACd;IAAC,OAPDA,KAAK;EAAA;EAAA,OAAAjD,KAAA;AAAA,I", "file": "/packages/id-map.js", "sourcesContent": ["\nexport class IdMap {\n  constructor(idStringify, idParse) {\n    this._map = new Map();\n    this._idStringify = idStringify || JSON.stringify;\n    this._idParse = idParse || JSON.parse;\n  }\n\n// Some of these methods are designed to match methods on OrderedDict, since\n// (eg) ObserveMultiplex and _CachingChangeObserver use them interchangeably.\n// (Conceivably, this should be replaced with \"UnorderedDict\" with a specific\n// set of methods that overlap between the two.)\n\n  get(id) {\n    const key = this._idStringify(id);\n    return this._map.get(key);\n  }\n\n  set(id, value) {\n    const key = this._idStringify(id);\n    this._map.set(key, value);\n  }\n\n  remove(id) {\n    const key = this._idStringify(id);\n    this._map.delete(key);\n  }\n\n  has(id) {\n    const key = this._idStringify(id);\n    return this._map.has(key);\n  }\n\n  empty() {\n    return this._map.size === 0;\n  }\n\n  clear() {\n    this._map.clear();\n  }\n\n  // Iterates over the items in the map. Return `false` to break the loop.\n  forEach(iterator) {\n    // don't use _.each, because we can't break out of it.\n    for (let [key, value] of this._map){\n      const breakIfFalse = iterator.call(\n        null,\n        value,\n        this._idParse(key)\n      );\n      if (breakIfFalse === false) {\n        return;\n      }\n    }\n  }\n\n  async forEachAsync(iterator) {\n    for (let [key, value] of this._map){\n      const breakIfFalse = await iterator.call(\n          null,\n          value,\n          this._idParse(key)\n      );\n      if (breakIfFalse === false) {\n        return;\n      }\n    }\n  }\n\n  size() {\n    return this._map.size;\n  }\n\n  setDefault(id, def) {\n    const key = this._idStringify(id);\n    if (this._map.has(key)) {\n      return this._map.get(key);\n    }\n    this._map.set(key, def);\n    return def;\n  }\n\n  // Assumes that values are EJSON-cloneable, and that we don't need to clone\n  // IDs (ie, that nobody is going to mutate an ObjectId).\n  clone() {\n    const clone = new IdMap(this._idStringify, this._idParse);\n    // copy directly to avoid stringify/parse overhead\n    this._map.forEach(function(value, key){\n      clone._map.set(key, EJSON.clone(value));\n    });\n    return clone;\n  }\n}\n"]}