{"version": 3, "sources": ["meteor://💻app/packages/reload/reload.js"], "names": ["module", "export", "Reload", "reloadSettings", "Meteor", "settings", "public", "packages", "reload", "debug", "message", "context", "console", "log", "concat", "JSON", "stringify", "KEY_NAME", "old_data", "old_json", "safeSessionStorage", "window", "sessionStorage", "setItem", "removeItem", "e", "_getData", "getItem", "old_parsed", "parse", "_debug", "err", "data", "providers", "_onMigrate", "name", "callback", "undefined", "push", "_migrationData", "pollProviders", "tryReload", "options", "immediateMigration", "migrationData", "allReady", "for<PERSON>ach", "p", "ready", "_migrate", "json", "_withFreshProvidersForTest", "f", "originalProviders", "slice", "reloading", "_reload", "setTimeout", "forceBrowserReload", "location", "hash", "href", "endsWith", "replace", "<PERSON><PERSON><PERSON><PERSON>", "WebAppLocalServer", "switchToPendingVersion"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,MAAM,EAACA,CAAA,KAAIA;AAAM,CAAC,CAAC;AAiC3B,MAAMA,MAAM,GAAG,CAAC,CAAC;AAExB,MAAMC,cAAc,GACjBC,MAAM,CAACC,QAAQ,IACdD,MAAM,CAACC,QAAQ,CAACC,MAAM,IACtBF,MAAM,CAACC,QAAQ,CAACC,MAAM,CAACC,QAAQ,IAC/BH,MAAM,CAACC,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,IACxC,CAAC,CAAC;AAEJ,SAASC,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC/B,IAAI,CAACR,cAAc,CAACM,KAAK,EAAE;IACzB;EACF;EACA;EACAG,OAAO,CAACC,GAAG,aAAAC,MAAA,CAAaJ,OAAO,GAAIK,IAAI,CAACC,SAAS,CAACL,OAAO,CAAC,CAAC;AAC7D;AAEA,MAAMM,QAAQ,GAAG,eAAe;AAEhC,IAAIC,QAAQ,GAAG,CAAC,CAAC;AACjB;AACA,IAAIC,QAAQ;;AAEZ;AACA,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,IAAI;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAA,kBAAkB,GAAGC,MAAM,CAACC,cAAc;;EAE1C;EACA,IAAIF,kBAAkB,EAAE;IACtBA,kBAAkB,CAACG,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;IAC5CH,kBAAkB,CAACI,UAAU,CAAC,WAAW,CAAC;EAC5C,CAAC,MAAM;IACL;IACAJ,kBAAkB,GAAG,IAAI;EAC3B;AACF,CAAC,CAAC,OAAOK,CAAC,EAAE;EACV;EACAL,kBAAkB,GAAG,IAAI;AAC3B;;AAEA;AACAlB,MAAM,CAACwB,QAAQ,GAAG,YAAY;EAC5B,OAAON,kBAAkB,IAAIA,kBAAkB,CAACO,OAAO,CAACV,QAAQ,CAAC;AACnE,CAAC;AAED,IAAIG,kBAAkB,EAAE;EACtBD,QAAQ,GAAGjB,MAAM,CAACwB,QAAQ,CAAC,CAAC;EAC5BN,kBAAkB,CAACI,UAAU,CAACP,QAAQ,CAAC;AACzC,CAAC,MAAM;EACL;EACA;EACA;AAAA;AAGF,IAAI,CAACE,QAAQ,EAAEA,QAAQ,GAAG,IAAI;AAC9B,IAAIS,UAAU,GAAG,CAAC,CAAC;AACnB,IAAI;EACFA,UAAU,GAAGb,IAAI,CAACc,KAAK,CAACV,QAAQ,CAAC;EACjC,IAAI,OAAOS,UAAU,KAAK,QAAQ,EAAE;IAClCxB,MAAM,CAAC0B,MAAM,CAAC,mCAAmC,CAAC;IAClDF,UAAU,GAAG,CAAC,CAAC;EACjB;AACF,CAAC,CAAC,OAAOG,GAAG,EAAE;EACZ3B,MAAM,CAAC0B,MAAM,CAAC,uCAAuC,CAAC;AACxD;AAEA,IAAIF,UAAU,CAACpB,MAAM,IAAI,OAAOoB,UAAU,CAACI,IAAI,KAAK,QAAQ,EAAE;EAC5D;EACAd,QAAQ,GAAGU,UAAU,CAACI,IAAI;AAC5B;AAEA,IAAIC,SAAS,GAAG,EAAE;;AAElB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA/B,MAAM,CAACgC,UAAU,GAAG,UAAUC,IAAI,EAAEC,QAAQ,EAAE;EAC5C3B,KAAK,CAAC,YAAY,EAAE;IAAC0B;EAAI,CAAC,CAAC;EAC3B,IAAI,CAACC,QAAQ,EAAE;IACb;IACAA,QAAQ,GAAGD,IAAI;IACfA,IAAI,GAAGE,SAAS;IAChB5B,KAAK,CAAC,wBAAwB,CAAC;EACjC;EAEAwB,SAAS,CAACK,IAAI,CAAC;IAACH,IAAI,EAAEA,IAAI;IAAEC,QAAQ,EAAEA;EAAQ,CAAC,CAAC;AAClD,CAAC;;AAED;AACA;AACA;AACAlC,MAAM,CAACqC,cAAc,GAAG,UAAUJ,IAAI,EAAE;EACtC1B,KAAK,CAAC,gBAAgB,EAAE;IAAC0B;EAAI,CAAC,CAAC;EAC/B,OAAOjB,QAAQ,CAACiB,IAAI,CAAC;AACvB,CAAC;;AAED;AACA,MAAMK,aAAa,GAAG,SAAAA,CAAUC,SAAS,EAAEC,OAAO,EAAE;EAClDjC,KAAK,CAAC,eAAe,EAAE;IAACiC;EAAO,CAAC,CAAC;EACjCD,SAAS,GAAGA,SAAS,IAAI,YAAY,CACrC,CAAC;EACDC,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,MAAM;IAACC;EAAkB,CAAC,GAAGD,OAAO;EACpCjC,KAAK,qBAAAK,MAAA,CACiB6B,kBAAkB,GAAG,EAAE,GAAG,MAAM,yBACpD;IAACD;EAAO,CACV,CAAC;EACD,MAAME,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIC,QAAQ,GAAG,IAAI;EACnBZ,SAAS,CAACa,OAAO,CAACC,CAAC,IAAI;IACrB,MAAM;MAACX,QAAQ;MAAED;IAAI,CAAC,GAAGY,CAAC,IAAI,CAAC,CAAC;IAChC,MAAM,CAACC,KAAK,EAAEhB,IAAI,CAAC,GAAGI,QAAQ,CAACK,SAAS,EAAEC,OAAO,CAAC,IAAI,EAAE;IAExDjC,KAAK,2BAAAK,MAAA,CACuBqB,IAAI,IAAI,SAAS,UAAArB,MAAA,CACzCkC,KAAK,GAAG,OAAO,GAAG,WAAW,GAE/B;MAACN;IAAO,CACV,CAAC;IACD,IAAI,CAACM,KAAK,EAAE;MACVH,QAAQ,GAAG,KAAK;IAClB;IAEA,IAAIb,IAAI,KAAKK,SAAS,IAAIF,IAAI,EAAE;MAC9BS,aAAa,CAACT,IAAI,CAAC,GAAGH,IAAI;IAC5B;EACF,CAAC,CAAC;EAEF,IAAIa,QAAQ,EAAE;IACZpC,KAAK,CAAC,wBAAwB,EAAE;MAACiC,OAAO;MAAEE;IAAa,CAAC,CAAC;IACzD,OAAOA,aAAa;EACtB;EAEA,IAAID,kBAAkB,EAAE;IACtBlC,KAAK,CAAC,kCAAkC,EAAE;MAACiC,OAAO;MAAEE;IAAa,CAAC,CAAC;IACnE,OAAOA,aAAa;EACtB;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA1C,MAAM,CAAC+C,QAAQ,GAAG,UAAUR,SAAS,EAAEC,OAAO,EAAE;EAC9CjC,KAAK,CAAC,UAAU,EAAE;IAACiC;EAAO,CAAC,CAAC;EAC5B;EACA;EACA,MAAME,aAAa,GAAGJ,aAAa,CAACC,SAAS,EAAEC,OAAO,CAAC;EACvD,IAAIE,aAAa,KAAK,IAAI,EAAE;IAC1B,OAAO,KAAK,CAAC,CAAC;EAChB;EAEA,IAAIM,IAAI;EACR,IAAI;IACF;IACAA,IAAI,GAAGnC,IAAI,CAACC,SAAS,CAAC;MACpBgB,IAAI,EAAEY,aAAa;MACnBpC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOuB,GAAG,EAAE;IACZ3B,MAAM,CAAC0B,MAAM,CAAC,uCAAuC,EAAEc,aAAa,CAAC;IACrE,MAAMb,GAAG;EACX;EAEA,IAAIX,kBAAkB,EAAE;IACtB,IAAI;MACFA,kBAAkB,CAACG,OAAO,CAACN,QAAQ,EAAEiC,IAAI,CAAC;IAC5C,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZ;MACA3B,MAAM,CAAC0B,MAAM,CAAC,oDAAoD,EAAEC,GAAG,CAAC;IAC1E;EACF,CAAC,MAAM;IACL3B,MAAM,CAAC0B,MAAM,CACX,sEACF,CAAC;EACH;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA5B,MAAM,CAACiD,0BAA0B,GAAG,UAAUC,CAAC,EAAE;EAC/C,MAAMC,iBAAiB,GAAGpB,SAAS,CAACqB,KAAK,CAAC,CAAC,CAAC;EAC5CrB,SAAS,GAAG,EAAE;EACd,IAAI;IACFmB,CAAC,CAAC,CAAC;EACL,CAAC,SAAS;IACRnB,SAAS,GAAGoB,iBAAiB;EAC/B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,SAAS,GAAG,KAAK;AACrBrD,MAAM,CAACsD,OAAO,GAAG,UAAUd,OAAO,EAAE;EAClCjC,KAAK,CAAC,SAAS,EAAE;IAACiC;EAAO,CAAC,CAAC;EAC3BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIa,SAAS,EAAE;IACb9C,KAAK,CAAC,+BAA+B,EAAE;MAACiC;IAAO,CAAC,CAAC;IACjD;EACF;EACAa,SAAS,GAAG,IAAI;EAEhB,SAASd,SAASA,CAAA,EAAG;IACnBhC,KAAK,CAAC,WAAW,CAAC;IAClBgD,UAAU,CAACjD,MAAM,EAAE,CAAC,CAAC;EACvB;EAEA,SAASkD,kBAAkBA,CAAA,EAAG;IAC5BjD,KAAK,CAAC,oBAAoB,CAAC;IAC3B;IACA;IACA;IACA;IACA;IACA,IAAIY,MAAM,CAACsC,QAAQ,CAACC,IAAI,IAAIvC,MAAM,CAACsC,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC9DzC,MAAM,CAACsC,QAAQ,CAACnD,MAAM,CAAC,CAAC;MACxB;IACF;IAEAa,MAAM,CAACsC,QAAQ,CAACI,OAAO,CAAC1C,MAAM,CAACsC,QAAQ,CAACE,IAAI,CAAC;EAC/C;EAEA,SAASrD,MAAMA,CAAA,EAAG;IAChBC,KAAK,CAAC,QAAQ,CAAC;IACf,IAAI,CAACP,MAAM,CAAC+C,QAAQ,CAACR,SAAS,EAAEC,OAAO,CAAC,EAAE;MACxC;IACF;IAEA,IAAItC,MAAM,CAAC4D,SAAS,EAAE;MACpBC,iBAAiB,CAACC,sBAAsB,CAAC,MAAM;QAC7CR,kBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC;MACF;IACF;IAEAA,kBAAkB,CAAC,CAAC;EACtB;EAEAjB,SAAS,CAAC,CAAC;AACb,CAAC,C", "file": "/packages/reload.js", "sourcesContent": ["/**\n * This code does _NOT_ support hot (session-restoring) reloads on\n * IE6,7. It only works on browsers with sessionStorage support.\n *\n * There are a couple approaches to add IE6,7 support:\n *\n * - use IE's \"userData\" mechanism in combination with window.name.\n * This mostly works, however the problem is that it can not get to the\n * data until after DOMReady. This is a problem for us since this API\n * relies on the data being ready before API users run. We could\n * refactor using Meteor.startup in all API users, but that might slow\n * page loads as we couldn't start the stream until after DOMReady.\n * Here are some resources on this approach:\n * https://github.com/hugeinc/USTORE.js\n * http://thudjs.tumblr.com/post/419577524/localstorage-userdata\n * http://www.javascriptkit.com/javatutors/domstorage2.shtml\n *\n * - POST the data to the server, and have the server send it back on\n * page load. This is nice because it sidesteps all the local storage\n * compatibility issues, however it is kinda tricky. We can use a unique\n * token in the URL, then get rid of it with HTML5 pushstate, but that\n * only works on pushstate browsers.\n *\n * This will all need to be reworked entirely when we add server-side\n * HTML rendering. In that case, the server will need to have access to\n * the client's session to render properly.\n */\n\n// XXX when making this API public, also expose a flag for the app\n// developer to know whether a hot code push is happening. This is\n// useful for apps using `window.onbeforeunload`. See\n// https://github.com/meteor/meteor/pull/657\n\nexport const Reload = {};\n\nconst reloadSettings =\n  (Meteor.settings &&\n    Meteor.settings.public &&\n    Meteor.settings.public.packages &&\n    Meteor.settings.public.packages.reload) ||\n  {};\n\nfunction debug(message, context) {\n  if (!reloadSettings.debug) {\n    return;\n  }\n  // eslint-disable-next-line no-console\n  console.log(`[reload] ${message}`, JSON.stringify(context));\n}\n\nconst KEY_NAME = 'Meteor_Reload';\n\nlet old_data = {};\n// read in old data at startup.\nlet old_json;\n\n// This logic for sessionStorage detection is based on browserstate/history.js\nlet safeSessionStorage = null;\ntry {\n  // This throws a SecurityError on Chrome if cookies & localStorage are\n  // explicitly disabled\n  //\n  // On Firefox with dom.storage.enabled set to false, sessionStorage is null\n  //\n  // We can't even do (typeof sessionStorage) on Chrome, it throws.  So we rely\n  // on the throw if sessionStorage == null; the alternative is browser\n  // detection, but this seems better.\n  safeSessionStorage = window.sessionStorage;\n\n  // Check we can actually use it\n  if (safeSessionStorage) {\n    safeSessionStorage.setItem('__dummy__', '1');\n    safeSessionStorage.removeItem('__dummy__');\n  } else {\n    // Be consistently null, for safety\n    safeSessionStorage = null;\n  }\n} catch (e) {\n  // Expected on chrome with strict security, or if sessionStorage not supported\n  safeSessionStorage = null;\n}\n\n// Exported for test.\nReload._getData = function () {\n  return safeSessionStorage && safeSessionStorage.getItem(KEY_NAME);\n};\n\nif (safeSessionStorage) {\n  old_json = Reload._getData();\n  safeSessionStorage.removeItem(KEY_NAME);\n} else {\n  // Unsupported browser (IE 6,7) or locked down security settings.\n  // No session resumption.\n  // Meteor._debug(\"XXX UNSUPPORTED BROWSER/SETTINGS\");\n}\n\nif (!old_json) old_json = '{}';\nlet old_parsed = {};\ntry {\n  old_parsed = JSON.parse(old_json);\n  if (typeof old_parsed !== 'object') {\n    Meteor._debug('Got bad data on reload. Ignoring.');\n    old_parsed = {};\n  }\n} catch (err) {\n  Meteor._debug('Got invalid JSON on reload. Ignoring.');\n}\n\nif (old_parsed.reload && typeof old_parsed.data === 'object') {\n  // Meteor._debug(\"Restoring reload data.\");\n  old_data = old_parsed.data;\n}\n\nlet providers = [];\n\n////////// External API //////////\n\n// Packages that support migration should register themselves by calling\n// this function. When it's time to migrate, callback will be called\n// with one argument, the \"retry function,\" and an optional 'option'\n// argument (containing a key 'immediateMigration'). If the package\n// is ready to migrate, it should return [true, data], where data is\n// its migration data, an arbitrary JSON value (or [true] if it has\n// no migration data this time). If the package needs more time\n// before it is ready to migrate, it should return false. Then, once\n// it is ready to migrating again, it should call the retry\n// function. The retry function will return immediately, but will\n// schedule the migration to be retried, meaning that every package\n// will be polled once again for its migration data. If they are all\n// ready this time, then the migration will happen. name must be set if there\n// is migration data. If 'immediateMigration' is set in the options\n// argument, then it doesn't matter whether the package is ready to\n// migrate or not; the reload will happen immediately without waiting\n// (used for OAuth redirect login).\n//\nReload._onMigrate = function (name, callback) {\n  debug('_onMigrate', {name});\n  if (!callback) {\n    // name not provided, so first arg is callback.\n    callback = name;\n    name = undefined;\n    debug('_onMigrate no callback');\n  }\n\n  providers.push({name: name, callback: callback});\n};\n\n// Called by packages when they start up.\n// Returns the object that was saved, or undefined if none saved.\n//\nReload._migrationData = function (name) {\n  debug('_migrationData', {name});\n  return old_data[name];\n};\n\n// Options are the same as for `Reload._migrate`.\nconst pollProviders = function (tryReload, options) {\n  debug('pollProviders', {options});\n  tryReload = tryReload || function () {\n  };\n  options = options || {};\n\n  const {immediateMigration} = options;\n  debug(\n    `pollProviders is ${immediateMigration ? '' : 'NOT '}immediateMigration`,\n    {options}\n  );\n  const migrationData = {};\n  let allReady = true;\n  providers.forEach(p => {\n    const {callback, name} = p || {};\n    const [ready, data] = callback(tryReload, options) || [];\n\n    debug(\n      `pollProviders provider ${name || 'unknown'} is ${\n        ready ? 'ready' : 'NOT ready'\n      }`,\n      {options}\n    );\n    if (!ready) {\n      allReady = false;\n    }\n\n    if (data !== undefined && name) {\n      migrationData[name] = data;\n    }\n  });\n\n  if (allReady) {\n    debug('pollProviders allReady', {options, migrationData});\n    return migrationData;\n  }\n\n  if (immediateMigration) {\n    debug('pollProviders immediateMigration', {options, migrationData});\n    return migrationData;\n  }\n\n  return null;\n};\n\n// Options are:\n//  - immediateMigration: true if the page will be reloaded immediately\n//    regardless of whether packages report that they are ready or not.\nReload._migrate = function (tryReload, options) {\n  debug('_migrate', {options});\n  // Make sure each package is ready to go, and collect their\n  // migration data\n  const migrationData = pollProviders(tryReload, options);\n  if (migrationData === null) {\n    return false; // not ready yet..\n  }\n\n  let json;\n  try {\n    // Persist the migration data\n    json = JSON.stringify({\n      data: migrationData,\n      reload: true,\n    });\n  } catch (err) {\n    Meteor._debug(\"Couldn't serialize data for migration\", migrationData);\n    throw err;\n  }\n\n  if (safeSessionStorage) {\n    try {\n      safeSessionStorage.setItem(KEY_NAME, json);\n    } catch (err) {\n      // We should have already checked this, but just log - don't throw\n      Meteor._debug(\"Couldn't save data for migration to sessionStorage\", err);\n    }\n  } else {\n    Meteor._debug(\n      'Browser does not support sessionStorage. Not saving migration state.'\n    );\n  }\n\n  return true;\n};\n\n// Allows tests to isolate the list of providers.\nReload._withFreshProvidersForTest = function (f) {\n  const originalProviders = providers.slice(0);\n  providers = [];\n  try {\n    f();\n  } finally {\n    providers = originalProviders;\n  }\n};\n\n// Migrating reload: reload this page (presumably to pick up a new\n// version of the code or assets), but save the program state and\n// migrate it over. This function returns immediately. The reload\n// will happen at some point in the future once all of the packages\n// are ready to migrate.\n//\nlet reloading = false;\nReload._reload = function (options) {\n  debug('_reload', {options});\n  options = options || {};\n\n  if (reloading) {\n    debug('reloading in progress already', {options});\n    return;\n  }\n  reloading = true;\n\n  function tryReload() {\n    debug('tryReload');\n    setTimeout(reload, 1);\n  }\n\n  function forceBrowserReload() {\n    debug('forceBrowserReload');\n    // We'd like to make the browser reload the page using location.replace()\n    // instead of location.reload(), because this avoids validating assets\n    // with the server if we still have a valid cached copy. This doesn't work\n    // when the location contains a hash however, because that wouldn't reload\n    // the page and just scroll to the hash location instead.\n    if (window.location.hash || window.location.href.endsWith('#')) {\n      window.location.reload();\n      return;\n    }\n\n    window.location.replace(window.location.href);\n  }\n\n  function reload() {\n    debug('reload');\n    if (!Reload._migrate(tryReload, options)) {\n      return;\n    }\n\n    if (Meteor.isCordova) {\n      WebAppLocalServer.switchToPendingVersion(() => {\n        forceBrowserReload();\n      });\n      return;\n    }\n\n    forceBrowserReload();\n  }\n\n  tryReload();\n};\n"]}