{"metadata": {}, "options": {"assumptions": {}, "compact": false, "sourceMaps": true, "ast": true, "babelrc": false, "configFile": false, "parserOpts": {"sourceType": "module", "sourceFileName": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\LoginPage.jsx", "plugins": ["*", "flow", "jsx", "asyncGenerators", "bigInt", "classPrivateMethods", "classPrivateProperties", "classProperties", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportExtensions", "exportNamespaceFrom", "functionBind", "functionSent", "importMeta", "nullishCoalescingOperator", "numericSeparator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", ["pipelineOperator", {"proposal": "minimal"}], "throwExpressions", "topLevelAwait", "classProperties", "classPrivateProperties", "jsx", "nullishCoalescingOperator", "nullishCoalescingOperator", "optionalChaining", "optionalChaining", "optionalCatchBinding", "optionalCatchBinding", "classProperties", "classPrivateProperties", "classPrivateMethods", "classProperties", "classPrivateProperties", "asyncGenerators", "asyncGenerators", "objectRestSpread", "objectRestSpread", "logicalAssignment"], "allowImportExportEverywhere": true, "allowReturnOutsideFunction": true, "allowUndeclaredExports": true, "strictMode": false}, "caller": {"name": "meteor", "arch": "web.browser"}, "sourceFileName": "imports/ui/pages/LoginPage.jsx", "filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\LoginPage.jsx", "inputSourceMap": {"version": 3, "names": ["React", "useState", "useEffect", "Meteor", "useNavigate", "Link", "<PERSON><PERSON>", "Form", "Field", "<PERSON><PERSON>", "useTracker", "LoginSchema", "object", "shape", "email", "string", "required", "password", "min", "LoginPage", "_s", "navigate", "error", "setError", "resendEmailSent", "setResendEmailSent", "showPassword", "setShowPassword", "user", "subscription", "subscribe", "isLoading", "ready", "role", "profile", "roles", "handleResendVerification", "call", "err", "reason", "handleSubmit", "setSubmitting", "loginWithPassword", "console", "message", "details", "errorType", "stack", "fullError", "JSON", "stringify", "background", "border", "padding", "cursor", "marginTop", "color", "fontSize", "errors", "touched", "isSubmitting", "v", "_c", "$RefreshReg$"], "sources": ["imports/ui/pages/LoginPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Meteor } from 'meteor/meteor';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Formik, Form, Field } from 'formik';\nimport * as Yup from 'yup';\nimport { useTracker } from 'meteor/react-meteor-data';\n\nconst LoginSchema = Yup.object().shape({\n  email: Yup.string()\n    .email('Invalid email address')\n    .required('Email is required'),\n  password: Yup.string()\n    .min(6, 'Password must be at least 6 characters')\n    .required('Password is required'),\n});\n\nexport const LoginPage = () => {\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const [resendEmailSent, setResendEmailSent] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n\n  const { user } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    return {\n      user: Meteor.user(),\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  useEffect(() => {\n    if (user) {\n      const role = user.profile?.role || user.roles?.[0];\n      navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleResendVerification = (email) => {\n    Meteor.call('users.resendVerificationEmail', email, (err) => {\n      if (err) {\n        setError(err.reason || 'Failed to resend verification email');\n      } else {\n        setResendEmailSent(true);\n      }\n    });\n  };\n\n  const handleSubmit = ({ email, password }, { setSubmitting }) => {\n    setError(''); // Clear any previous errors\n    setResendEmailSent(false);\n    \n    Meteor.loginWithPassword(email, password, (err) => {\n      if (err) {\n        console.error('Login error details:', {\n          error: err.error,\n          reason: err.reason,\n          message: err.message,\n          details: err.details,\n          errorType: err.errorType,\n          stack: err.stack,\n          fullError: JSON.stringify(err, null, 2)\n        });\n        if (err.error === 'email-not-verified') {\n          setError(\n            <div>\n              Email not verified. \n              <button\n                onClick={() => handleResendVerification(email)}\n                className=\"auth-link\"\n                style={{ \n                  background: 'none',\n                  border: 'none',\n                  padding: '0 4px',\n                  cursor: 'pointer'\n                }}\n              >\n                Resend verification email\n              </button>\n              {resendEmailSent && (\n                <div style={{ \n                  marginTop: '8px',\n                  color: 'var(--success)',\n                  fontSize: '0.875rem'\n                }}>\n                  Verification email sent!\n                </div>\n              )}\n            </div>\n          );\n        } else {\n          setError(err.reason || 'Login failed. Please check your credentials.');\n        }\n        setSubmitting(false);\n      }\n    });\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n        <h1 className=\"auth-title\">Welcome Back</h1>\n          <p className=\"auth-subtitle\">\n            Sign in to access your workspace and manage your tasks efficiently\n        </p>\n        </div>\n        \n        <Formik\n          initialValues={{ email: '', password: '' }}\n          validationSchema={LoginSchema}\n          onSubmit={handleSubmit}\n        >\n          {({ errors, touched, isSubmitting }) => (\n            <Form className=\"auth-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Email Address\n                </label>\n                <Field\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  placeholder=\"Enter your email address\"\n                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}\n                  disabled={isSubmitting}\n                />\n                {errors.email && touched.email && (\n                  <div className=\"error-message\">\n                    <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    {errors.email}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Password\n                </label>\n                <div className=\"form-control-password-wrapper\">\n                <Field\n                  id=\"password\"\n                  name=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                  placeholder=\"Enter your password\"\n                    className={`form-control ${errors.password && touched.password ? 'form-control-error' : ''}`}\n                  disabled={isSubmitting}\n                />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle-btn\"\n                    tabIndex={-1}\n                    aria-label={showPassword ? 'Hide password' : 'Show password'}\n                    onClick={() => setShowPassword((v) => !v)}\n                  >\n                    {showPassword ? (\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.password && touched.password && (\n                  <div className=\"error-message\">\n                    <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    {errors.password}\n                  </div>\n                )}\n              </div>\n\n              {error && (\n                <div className=\"error-message error-message-global\">\n                  <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  {error}\n                </div>\n              )}\n\n              <button \n                type=\"submit\" \n                className=\"btn btn-primary btn-auth\"\n                disabled={isSubmitting}\n              >\n                {isSubmitting ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    Signing in...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    Sign In\n                  </>\n                )}\n              </button>\n\n              <div className=\"auth-links-row\">\n                <Link to=\"/forgot-password\" className=\"auth-link\">Forgot password?</Link>\n              </div>\n            </Form>\n          )}\n        </Formik>\n\n        <div className=\"auth-footer\">\n          <p className=\"auth-footer-text\">\n            Don't have an account? \n            <Link to=\"/signup\" className=\"auth-link\">\n              Create an account\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,QAAQ;AAC5C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,UAAU,QAAQ,0BAA0B;AAErD,MAAMC,WAAW,GAAGF,GAAG,CAACG,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACrCC,KAAK,EAAEL,GAAG,CAACM,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,QAAQ,EAAER,GAAG,CAACM,MAAM,CAAC,CAAC,CACnBG,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDF,QAAQ,CAAC,sBAAsB;AACpC,CAAC,CAAC;AAEF,OAAO,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAE2B;EAAK,CAAC,GAAGlB,UAAU,CAAC,MAAM;IAChC,MAAMmB,YAAY,GAAG1B,MAAM,CAAC2B,SAAS,CAAC,UAAU,CAAC;IACjD,OAAO;MACLF,IAAI,EAAEzB,MAAM,CAACyB,IAAI,CAAC,CAAC;MACnBG,SAAS,EAAE,CAACF,YAAY,CAACG,KAAK,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN9B,SAAS,CAAC,MAAM;IACd,IAAI0B,IAAI,EAAE;MACR,MAAMK,IAAI,GAAGL,IAAI,CAACM,OAAO,EAAED,IAAI,IAAIL,IAAI,CAACO,KAAK,GAAG,CAAC,CAAC;MAClDd,QAAQ,CAACY,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;IACrE;EACF,CAAC,EAAE,CAACL,IAAI,EAAEP,QAAQ,CAAC,CAAC;EAEpB,MAAMe,wBAAwB,GAAItB,KAAK,IAAK;IAC1CX,MAAM,CAACkC,IAAI,CAAC,+BAA+B,EAAEvB,KAAK,EAAGwB,GAAG,IAAK;MAC3D,IAAIA,GAAG,EAAE;QACPf,QAAQ,CAACe,GAAG,CAACC,MAAM,IAAI,qCAAqC,CAAC;MAC/D,CAAC,MAAM;QACLd,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMe,YAAY,GAAGA,CAAC;IAAE1B,KAAK;IAAEG;EAAS,CAAC,EAAE;IAAEwB;EAAc,CAAC,KAAK;IAC/DlB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACdE,kBAAkB,CAAC,KAAK,CAAC;IAEzBtB,MAAM,CAACuC,iBAAiB,CAAC5B,KAAK,EAAEG,QAAQ,EAAGqB,GAAG,IAAK;MACjD,IAAIA,GAAG,EAAE;QACPK,OAAO,CAACrB,KAAK,CAAC,sBAAsB,EAAE;UACpCA,KAAK,EAAEgB,GAAG,CAAChB,KAAK;UAChBiB,MAAM,EAAED,GAAG,CAACC,MAAM;UAClBK,OAAO,EAAEN,GAAG,CAACM,OAAO;UACpBC,OAAO,EAAEP,GAAG,CAACO,OAAO;UACpBC,SAAS,EAAER,GAAG,CAACQ,SAAS;UACxBC,KAAK,EAAET,GAAG,CAACS,KAAK;UAChBC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACZ,GAAG,EAAE,IAAI,EAAE,CAAC;QACxC,CAAC,CAAC;QACF,IAAIA,GAAG,CAAChB,KAAK,KAAK,oBAAoB,EAAE;UACtCC,QAAQ,CACN,CAAC,GAAG;AAChB;AACA,cAAc,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,MAAMa,wBAAwB,CAACtB,KAAK,CAAC,CAAC,CAC/C,SAAS,CAAC,WAAW,CACrB,KAAK,CAAC,CAAC;cACLqC,UAAU,EAAE,MAAM;cAClBC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,OAAO;cAChBC,MAAM,EAAE;YACV,CAAC,CAAC;AAElB;AACA,cAAc,EAAE,MAAM;AACtB,cAAc,CAAC9B,eAAe,IACd,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;cACV+B,SAAS,EAAE,KAAK;cAChBC,KAAK,EAAE,gBAAgB;cACvBC,QAAQ,EAAE;YACZ,CAAC,CAAC;AAClB;AACA,gBAAgB,EAAE,GAAG,CACN;AACf,YAAY,EAAE,GAAG,CACP,CAAC;QACH,CAAC,MAAM;UACLlC,QAAQ,CAACe,GAAG,CAACC,MAAM,IAAI,8CAA8C,CAAC;QACxE;QACAE,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AACnC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAChC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AACpC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC1G,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,oIAAoI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC7O,YAAY,EAAE,GAAG;AACjB,UAAU,EAAE,GAAG;AACf,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE;AACnD,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe;AACtC;AACA,QAAQ,EAAE,CAAC;AACX,QAAQ,EAAE,GAAG;AACb;AACA,QAAQ,CAAC,MAAM,CACL,aAAa,CAAC,CAAC;QAAE3B,KAAK,EAAE,EAAE;QAAEG,QAAQ,EAAE;MAAG,CAAC,CAAC,CAC3C,gBAAgB,CAAC,CAACN,WAAW,CAAC,CAC9B,QAAQ,CAAC,CAAC6B,YAAY,CAAC;AAEjC,UAAU,CAAC,CAAC;UAAEkB,MAAM;UAAEC,OAAO;UAAEC;QAAa,CAAC,KACjC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW;AACvC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY;AAC7D,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,sOAAsO,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACrV,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,OAAO,CACV,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CACZ,WAAW,CAAC,0BAA0B,CACtC,SAAS,CAAC,CAAC,gBAAgBF,MAAM,CAAC5C,KAAK,IAAI6C,OAAO,CAAC7C,KAAK,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CACvF,QAAQ,CAAC,CAAC8C,YAAY,CAAC;AAEzC,gBAAgB,CAACF,MAAM,CAAC5C,KAAK,IAAI6C,OAAO,CAAC7C,KAAK,IAC5B,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe;AAChD,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACzI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,uIAAuI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACxP,oBAAoB,EAAE,GAAG;AACzB,oBAAoB,CAAC4C,MAAM,CAAC5C,KAAK;AACjC,kBAAkB,EAAE,GAAG,CACN;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY;AAChE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,+HAA+H,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC9O,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,mIAAmI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAClP,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,+BAA+B;AAC9D,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,UAAU,CACb,IAAI,CAAC,UAAU,CACb,IAAI,CAAC,CAACY,YAAY,GAAG,MAAM,GAAG,UAAU,CAAC,CAC3C,WAAW,CAAC,qBAAqB,CAC/B,SAAS,CAAC,CAAC,gBAAgBgC,MAAM,CAACzC,QAAQ,IAAI0C,OAAO,CAAC1C,QAAQ,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CAC/F,QAAQ,CAAC,CAAC2C,YAAY,CAAC;AAEzC,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,qBAAqB,CAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,UAAU,CAAC,CAAClC,YAAY,GAAG,eAAe,GAAG,eAAe,CAAC,CAC7D,OAAO,CAAC,CAAC,MAAMC,eAAe,CAAEkC,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC;AAE9D,oBAAoB,CAACnC,YAAY,GACX,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wPAAwP,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3W,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,8FAA8F,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACjN,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACxI,sBAAsB,EAAE,GAAG,CACN;AACrB,kBAAkB,EAAE,MAAM;AAC1B,gBAAgB,EAAE,GAAG;AACrB,gBAAgB,CAACgC,MAAM,CAACzC,QAAQ,IAAI0C,OAAO,CAAC1C,QAAQ,IAClC,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe;AAChD,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACzI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,uIAAuI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACxP,oBAAoB,EAAE,GAAG;AACzB,oBAAoB,CAACyC,MAAM,CAACzC,QAAQ;AACpC,kBAAkB,EAAE,GAAG,CACN;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAACK,KAAK,IACJ,CAAC,GAAG,CAAC,SAAS,CAAC,oCAAoC;AACnE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACvI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,uIAAuI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACtP,kBAAkB,EAAE,GAAG;AACvB,kBAAkB,CAACA,KAAK;AACxB,gBAAgB,EAAE,GAAG,CACN;AACf;AACA,cAAc,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,0BAA0B,CACpC,QAAQ,CAAC,CAACsC,YAAY,CAAC;AAEvC,gBAAgB,CAACA,YAAY,GACX;AAClB,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI;AAC5D;AACA,kBAAkB,GAAG,GAEH;AAClB,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACvI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,qMAAqM,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACtT,oBAAoB,EAAE,GAAG;AACzB;AACA,kBAAkB,GACD;AACjB,cAAc,EAAE,MAAM;AACtB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AAC7C,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,IAAI;AACxF,cAAc,EAAE,GAAG;AACnB,YAAY,EAAE,IAAI,CACP;AACX,QAAQ,EAAE,MAAM;AAChB;AACA,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB;AACzC;AACA,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW;AACpD;AACA,YAAY,EAAE,IAAI;AAClB,UAAU,EAAE,CAAC;AACb,QAAQ,EAAE,GAAG;AACb,MAAM,EAAE,GAAG;AACX,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACxC,EAAA,CA7NWD,SAAS;EAAA,QACHf,WAAW,EAKXM,UAAU;AAAA;AAAAoD,EAAA,GANhB3C,SAAS;AAAA,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "targets": {}, "cloneInputAst": true, "browserslistConfigFile": false, "passPerPreset": false, "envName": "development", "cwd": "C:\\Users\\<USER>\\smart-task-management-system", "root": "C:\\Users\\<USER>\\smart-task-management-system", "rootMode": "root", "plugins": [{"key": "base$0", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$0", "visitor": {"Program": {"enter": [null], "exit": [null]}, "_exploded": true, "_verified": true}, "options": {"avoidModernSyntax": false, "enforceStrictMode": false, "dynamicImport": true, "generateLetDeclarations": true}, "externalDependencies": []}, {"key": "transform-runtime", "visitor": {"MemberExpression": {"enter": [null]}, "ObjectPattern": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "Identifier": {"enter": [null]}, "JSXIdentifier": {"enter": [null]}}, "options": {"version": "7.17.2", "helpers": true, "useESModules": false, "corejs": false}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-react-jsx", "visitor": {"JSXNamespacedName": {"enter": [null]}, "JSXSpreadChild": {"enter": [null]}, "Program": {"enter": [null]}, "JSXFragment": {"exit": [null]}, "JSXElement": {"exit": [null]}, "JSXAttribute": {"enter": [null]}}, "options": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "runtime": "classic", "throwIfNamespace": true, "useBuiltIns": false}, "externalDependencies": []}, {"key": "transform-react-display-name", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-pure-annotations", "visitor": {"CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "syntax-nullish-coalescing-operator", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-nullish-coalescing-operator", "visitor": {"LogicalExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-chaining", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-chaining", "visitor": {"OptionalCallExpression": {"enter": [null]}, "OptionalMemberExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-catch-binding", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-catch-binding", "visitor": {"CatchClause": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-class-properties", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-async-generators", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-logical-assignment-operators", "visitor": {"AssignmentExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-literals", "visitor": {"NumericLiteral": {"enter": [null]}, "StringLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-template-literals", "visitor": {"TaggedTemplateExpression": {"enter": [null]}, "TemplateLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-parameters", "visitor": {"_exploded": true, "_verified": true, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-exponentiation-operator", "visitor": {"AssignmentExpression": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}], "presets": [], "generatorOpts": {"filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\LoginPage.jsx", "comments": true, "compact": false, "sourceMaps": true, "sourceFileName": "imports/ui/pages/LoginPage.jsx", "inputSourceMap": {"version": 3, "names": ["React", "useState", "useEffect", "Meteor", "useNavigate", "Link", "<PERSON><PERSON>", "Form", "Field", "<PERSON><PERSON>", "useTracker", "LoginSchema", "object", "shape", "email", "string", "required", "password", "min", "LoginPage", "_s", "navigate", "error", "setError", "resendEmailSent", "setResendEmailSent", "showPassword", "setShowPassword", "user", "subscription", "subscribe", "isLoading", "ready", "role", "profile", "roles", "handleResendVerification", "call", "err", "reason", "handleSubmit", "setSubmitting", "loginWithPassword", "console", "message", "details", "errorType", "stack", "fullError", "JSON", "stringify", "background", "border", "padding", "cursor", "marginTop", "color", "fontSize", "errors", "touched", "isSubmitting", "v", "_c", "$RefreshReg$"], "sources": ["imports/ui/pages/LoginPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Meteor } from 'meteor/meteor';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Formik, Form, Field } from 'formik';\nimport * as Yup from 'yup';\nimport { useTracker } from 'meteor/react-meteor-data';\n\nconst LoginSchema = Yup.object().shape({\n  email: Yup.string()\n    .email('Invalid email address')\n    .required('Email is required'),\n  password: Yup.string()\n    .min(6, 'Password must be at least 6 characters')\n    .required('Password is required'),\n});\n\nexport const LoginPage = () => {\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const [resendEmailSent, setResendEmailSent] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n\n  const { user } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    return {\n      user: Meteor.user(),\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  useEffect(() => {\n    if (user) {\n      const role = user.profile?.role || user.roles?.[0];\n      navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleResendVerification = (email) => {\n    Meteor.call('users.resendVerificationEmail', email, (err) => {\n      if (err) {\n        setError(err.reason || 'Failed to resend verification email');\n      } else {\n        setResendEmailSent(true);\n      }\n    });\n  };\n\n  const handleSubmit = ({ email, password }, { setSubmitting }) => {\n    setError(''); // Clear any previous errors\n    setResendEmailSent(false);\n    \n    Meteor.loginWithPassword(email, password, (err) => {\n      if (err) {\n        console.error('Login error details:', {\n          error: err.error,\n          reason: err.reason,\n          message: err.message,\n          details: err.details,\n          errorType: err.errorType,\n          stack: err.stack,\n          fullError: JSON.stringify(err, null, 2)\n        });\n        if (err.error === 'email-not-verified') {\n          setError(\n            <div>\n              Email not verified. \n              <button\n                onClick={() => handleResendVerification(email)}\n                className=\"auth-link\"\n                style={{ \n                  background: 'none',\n                  border: 'none',\n                  padding: '0 4px',\n                  cursor: 'pointer'\n                }}\n              >\n                Resend verification email\n              </button>\n              {resendEmailSent && (\n                <div style={{ \n                  marginTop: '8px',\n                  color: 'var(--success)',\n                  fontSize: '0.875rem'\n                }}>\n                  Verification email sent!\n                </div>\n              )}\n            </div>\n          );\n        } else {\n          setError(err.reason || 'Login failed. Please check your credentials.');\n        }\n        setSubmitting(false);\n      }\n    });\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n        <h1 className=\"auth-title\">Welcome Back</h1>\n          <p className=\"auth-subtitle\">\n            Sign in to access your workspace and manage your tasks efficiently\n        </p>\n        </div>\n        \n        <Formik\n          initialValues={{ email: '', password: '' }}\n          validationSchema={LoginSchema}\n          onSubmit={handleSubmit}\n        >\n          {({ errors, touched, isSubmitting }) => (\n            <Form className=\"auth-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Email Address\n                </label>\n                <Field\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  placeholder=\"Enter your email address\"\n                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}\n                  disabled={isSubmitting}\n                />\n                {errors.email && touched.email && (\n                  <div className=\"error-message\">\n                    <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    {errors.email}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Password\n                </label>\n                <div className=\"form-control-password-wrapper\">\n                <Field\n                  id=\"password\"\n                  name=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                  placeholder=\"Enter your password\"\n                    className={`form-control ${errors.password && touched.password ? 'form-control-error' : ''}`}\n                  disabled={isSubmitting}\n                />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle-btn\"\n                    tabIndex={-1}\n                    aria-label={showPassword ? 'Hide password' : 'Show password'}\n                    onClick={() => setShowPassword((v) => !v)}\n                  >\n                    {showPassword ? (\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.password && touched.password && (\n                  <div className=\"error-message\">\n                    <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    {errors.password}\n                  </div>\n                )}\n              </div>\n\n              {error && (\n                <div className=\"error-message error-message-global\">\n                  <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  {error}\n                </div>\n              )}\n\n              <button \n                type=\"submit\" \n                className=\"btn btn-primary btn-auth\"\n                disabled={isSubmitting}\n              >\n                {isSubmitting ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    Signing in...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    Sign In\n                  </>\n                )}\n              </button>\n\n              <div className=\"auth-links-row\">\n                <Link to=\"/forgot-password\" className=\"auth-link\">Forgot password?</Link>\n              </div>\n            </Form>\n          )}\n        </Formik>\n\n        <div className=\"auth-footer\">\n          <p className=\"auth-footer-text\">\n            Don't have an account? \n            <Link to=\"/signup\" className=\"auth-link\">\n              Create an account\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,QAAQ;AAC5C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,UAAU,QAAQ,0BAA0B;AAErD,MAAMC,WAAW,GAAGF,GAAG,CAACG,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACrCC,KAAK,EAAEL,GAAG,CAACM,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,QAAQ,EAAER,GAAG,CAACM,MAAM,CAAC,CAAC,CACnBG,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDF,QAAQ,CAAC,sBAAsB;AACpC,CAAC,CAAC;AAEF,OAAO,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAE2B;EAAK,CAAC,GAAGlB,UAAU,CAAC,MAAM;IAChC,MAAMmB,YAAY,GAAG1B,MAAM,CAAC2B,SAAS,CAAC,UAAU,CAAC;IACjD,OAAO;MACLF,IAAI,EAAEzB,MAAM,CAACyB,IAAI,CAAC,CAAC;MACnBG,SAAS,EAAE,CAACF,YAAY,CAACG,KAAK,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN9B,SAAS,CAAC,MAAM;IACd,IAAI0B,IAAI,EAAE;MACR,MAAMK,IAAI,GAAGL,IAAI,CAACM,OAAO,EAAED,IAAI,IAAIL,IAAI,CAACO,KAAK,GAAG,CAAC,CAAC;MAClDd,QAAQ,CAACY,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;IACrE;EACF,CAAC,EAAE,CAACL,IAAI,EAAEP,QAAQ,CAAC,CAAC;EAEpB,MAAMe,wBAAwB,GAAItB,KAAK,IAAK;IAC1CX,MAAM,CAACkC,IAAI,CAAC,+BAA+B,EAAEvB,KAAK,EAAGwB,GAAG,IAAK;MAC3D,IAAIA,GAAG,EAAE;QACPf,QAAQ,CAACe,GAAG,CAACC,MAAM,IAAI,qCAAqC,CAAC;MAC/D,CAAC,MAAM;QACLd,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMe,YAAY,GAAGA,CAAC;IAAE1B,KAAK;IAAEG;EAAS,CAAC,EAAE;IAAEwB;EAAc,CAAC,KAAK;IAC/DlB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACdE,kBAAkB,CAAC,KAAK,CAAC;IAEzBtB,MAAM,CAACuC,iBAAiB,CAAC5B,KAAK,EAAEG,QAAQ,EAAGqB,GAAG,IAAK;MACjD,IAAIA,GAAG,EAAE;QACPK,OAAO,CAACrB,KAAK,CAAC,sBAAsB,EAAE;UACpCA,KAAK,EAAEgB,GAAG,CAAChB,KAAK;UAChBiB,MAAM,EAAED,GAAG,CAACC,MAAM;UAClBK,OAAO,EAAEN,GAAG,CAACM,OAAO;UACpBC,OAAO,EAAEP,GAAG,CAACO,OAAO;UACpBC,SAAS,EAAER,GAAG,CAACQ,SAAS;UACxBC,KAAK,EAAET,GAAG,CAACS,KAAK;UAChBC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACZ,GAAG,EAAE,IAAI,EAAE,CAAC;QACxC,CAAC,CAAC;QACF,IAAIA,GAAG,CAAChB,KAAK,KAAK,oBAAoB,EAAE;UACtCC,QAAQ,CACN,CAAC,GAAG;AAChB;AACA,cAAc,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,MAAMa,wBAAwB,CAACtB,KAAK,CAAC,CAAC,CAC/C,SAAS,CAAC,WAAW,CACrB,KAAK,CAAC,CAAC;cACLqC,UAAU,EAAE,MAAM;cAClBC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,OAAO;cAChBC,MAAM,EAAE;YACV,CAAC,CAAC;AAElB;AACA,cAAc,EAAE,MAAM;AACtB,cAAc,CAAC9B,eAAe,IACd,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;cACV+B,SAAS,EAAE,KAAK;cAChBC,KAAK,EAAE,gBAAgB;cACvBC,QAAQ,EAAE;YACZ,CAAC,CAAC;AAClB;AACA,gBAAgB,EAAE,GAAG,CACN;AACf,YAAY,EAAE,GAAG,CACP,CAAC;QACH,CAAC,MAAM;UACLlC,QAAQ,CAACe,GAAG,CAACC,MAAM,IAAI,8CAA8C,CAAC;QACxE;QACAE,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AACnC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAChC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AACpC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC1G,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,oIAAoI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC7O,YAAY,EAAE,GAAG;AACjB,UAAU,EAAE,GAAG;AACf,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE;AACnD,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe;AACtC;AACA,QAAQ,EAAE,CAAC;AACX,QAAQ,EAAE,GAAG;AACb;AACA,QAAQ,CAAC,MAAM,CACL,aAAa,CAAC,CAAC;QAAE3B,KAAK,EAAE,EAAE;QAAEG,QAAQ,EAAE;MAAG,CAAC,CAAC,CAC3C,gBAAgB,CAAC,CAACN,WAAW,CAAC,CAC9B,QAAQ,CAAC,CAAC6B,YAAY,CAAC;AAEjC,UAAU,CAAC,CAAC;UAAEkB,MAAM;UAAEC,OAAO;UAAEC;QAAa,CAAC,KACjC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW;AACvC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY;AAC7D,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,sOAAsO,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACrV,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,OAAO,CACV,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CACZ,WAAW,CAAC,0BAA0B,CACtC,SAAS,CAAC,CAAC,gBAAgBF,MAAM,CAAC5C,KAAK,IAAI6C,OAAO,CAAC7C,KAAK,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CACvF,QAAQ,CAAC,CAAC8C,YAAY,CAAC;AAEzC,gBAAgB,CAACF,MAAM,CAAC5C,KAAK,IAAI6C,OAAO,CAAC7C,KAAK,IAC5B,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe;AAChD,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACzI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,uIAAuI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACxP,oBAAoB,EAAE,GAAG;AACzB,oBAAoB,CAAC4C,MAAM,CAAC5C,KAAK;AACjC,kBAAkB,EAAE,GAAG,CACN;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY;AAChE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,+HAA+H,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC9O,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,mIAAmI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAClP,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,+BAA+B;AAC9D,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,UAAU,CACb,IAAI,CAAC,UAAU,CACb,IAAI,CAAC,CAACY,YAAY,GAAG,MAAM,GAAG,UAAU,CAAC,CAC3C,WAAW,CAAC,qBAAqB,CAC/B,SAAS,CAAC,CAAC,gBAAgBgC,MAAM,CAACzC,QAAQ,IAAI0C,OAAO,CAAC1C,QAAQ,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CAC/F,QAAQ,CAAC,CAAC2C,YAAY,CAAC;AAEzC,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,qBAAqB,CAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,UAAU,CAAC,CAAClC,YAAY,GAAG,eAAe,GAAG,eAAe,CAAC,CAC7D,OAAO,CAAC,CAAC,MAAMC,eAAe,CAAEkC,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC;AAE9D,oBAAoB,CAACnC,YAAY,GACX,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wPAAwP,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3W,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,8FAA8F,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACjN,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACxI,sBAAsB,EAAE,GAAG,CACN;AACrB,kBAAkB,EAAE,MAAM;AAC1B,gBAAgB,EAAE,GAAG;AACrB,gBAAgB,CAACgC,MAAM,CAACzC,QAAQ,IAAI0C,OAAO,CAAC1C,QAAQ,IAClC,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe;AAChD,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACzI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,uIAAuI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACxP,oBAAoB,EAAE,GAAG;AACzB,oBAAoB,CAACyC,MAAM,CAACzC,QAAQ;AACpC,kBAAkB,EAAE,GAAG,CACN;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAACK,KAAK,IACJ,CAAC,GAAG,CAAC,SAAS,CAAC,oCAAoC;AACnE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACvI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,uIAAuI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACtP,kBAAkB,EAAE,GAAG;AACvB,kBAAkB,CAACA,KAAK;AACxB,gBAAgB,EAAE,GAAG,CACN;AACf;AACA,cAAc,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,0BAA0B,CACpC,QAAQ,CAAC,CAACsC,YAAY,CAAC;AAEvC,gBAAgB,CAACA,YAAY,GACX;AAClB,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI;AAC5D;AACA,kBAAkB,GAAG,GAEH;AAClB,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACvI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,qMAAqM,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACtT,oBAAoB,EAAE,GAAG;AACzB;AACA,kBAAkB,GACD;AACjB,cAAc,EAAE,MAAM;AACtB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AAC7C,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,IAAI;AACxF,cAAc,EAAE,GAAG;AACnB,YAAY,EAAE,IAAI,CACP;AACX,QAAQ,EAAE,MAAM;AAChB;AACA,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB;AACzC;AACA,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW;AACpD;AACA,YAAY,EAAE,IAAI;AAClB,UAAU,EAAE,CAAC;AACb,QAAQ,EAAE,GAAG;AACb,MAAM,EAAE,GAAG;AACX,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACxC,EAAA,CA7NWD,SAAS;EAAA,QACHf,WAAW,EAKXM,UAAU;AAAA;AAAAoD,EAAA,GANhB3C,SAAS;AAAA,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}}}, "code": "!function (module1) {\n  module1.export({\n    LoginPage: () => LoginPage\n  });\n  let React, useState, useEffect;\n  module1.link(\"react\", {\n    default(v) {\n      React = v;\n    },\n    useState(v) {\n      useState = v;\n    },\n    useEffect(v) {\n      useEffect = v;\n    }\n  }, 0);\n  let Meteor;\n  module1.link(\"meteor/meteor\", {\n    Meteor(v) {\n      Meteor = v;\n    }\n  }, 1);\n  let useNavigate, Link;\n  module1.link(\"react-router-dom\", {\n    useNavigate(v) {\n      useNavigate = v;\n    },\n    Link(v) {\n      Link = v;\n    }\n  }, 2);\n  let Formik, Form, Field;\n  module1.link(\"formik\", {\n    Formik(v) {\n      Formik = v;\n    },\n    Form(v) {\n      Form = v;\n    },\n    Field(v) {\n      Field = v;\n    }\n  }, 3);\n  let Yup;\n  module1.link(\"yup\", {\n    \"*\"(v) {\n      Yup = v;\n    }\n  }, 4);\n  let useTracker;\n  module1.link(\"meteor/react-meteor-data\", {\n    useTracker(v) {\n      useTracker = v;\n    }\n  }, 5);\n  ___INIT_METEOR_FAST_REFRESH(module);\n  var _s = $RefreshSig$();\n  const LoginSchema = Yup.object().shape({\n    email: Yup.string().email('Invalid email address').required('Email is required'),\n    password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required')\n  });\n  const LoginPage = () => {\n    _s();\n    const navigate = useNavigate();\n    const [error, setError] = useState('');\n    const [resendEmailSent, setResendEmailSent] = useState(false);\n    const [showPassword, setShowPassword] = useState(false);\n    const {\n      user\n    } = useTracker(() => {\n      const subscription = Meteor.subscribe('userData');\n      return {\n        user: Meteor.user(),\n        isLoading: !subscription.ready()\n      };\n    }, []);\n    useEffect(() => {\n      if (user) {\n        var _user$profile, _user$roles;\n        const role = ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);\n        navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');\n      }\n    }, [user, navigate]);\n    const handleResendVerification = email => {\n      Meteor.call('users.resendVerificationEmail', email, err => {\n        if (err) {\n          setError(err.reason || 'Failed to resend verification email');\n        } else {\n          setResendEmailSent(true);\n        }\n      });\n    };\n    const handleSubmit = (_ref, _ref2) => {\n      let {\n        email,\n        password\n      } = _ref;\n      let {\n        setSubmitting\n      } = _ref2;\n      setError(''); // Clear any previous errors\n      setResendEmailSent(false);\n      Meteor.loginWithPassword(email, password, err => {\n        if (err) {\n          console.error('Login error details:', {\n            error: err.error,\n            reason: err.reason,\n            message: err.message,\n            details: err.details,\n            errorType: err.errorType,\n            stack: err.stack,\n            fullError: JSON.stringify(err, null, 2)\n          });\n          if (err.error === 'email-not-verified') {\n            setError(/*#__PURE__*/React.createElement(\"div\", null, \"Email not verified.\", /*#__PURE__*/React.createElement(\"button\", {\n              onClick: () => handleResendVerification(email),\n              className: \"auth-link\",\n              style: {\n                background: 'none',\n                border: 'none',\n                padding: '0 4px',\n                cursor: 'pointer'\n              }\n            }, \"Resend verification email\"), resendEmailSent && /*#__PURE__*/React.createElement(\"div\", {\n              style: {\n                marginTop: '8px',\n                color: 'var(--success)',\n                fontSize: '0.875rem'\n              }\n            }, \"Verification email sent!\")));\n          } else {\n            setError(err.reason || 'Login failed. Please check your credentials.');\n          }\n          setSubmitting(false);\n        }\n      });\n    };\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-container\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-card\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-header\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-logo\"\n    }, /*#__PURE__*/React.createElement(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }, /*#__PURE__*/React.createElement(\"path\", {\n      d: \"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }))), /*#__PURE__*/React.createElement(\"h1\", {\n      className: \"auth-title\"\n    }, \"Welcome Back\"), /*#__PURE__*/React.createElement(\"p\", {\n      className: \"auth-subtitle\"\n    }, \"Sign in to access your workspace and manage your tasks efficiently\")), /*#__PURE__*/React.createElement(Formik, {\n      initialValues: {\n        email: '',\n        password: ''\n      },\n      validationSchema: LoginSchema,\n      onSubmit: handleSubmit\n    }, _ref3 => {\n      let {\n        errors,\n        touched,\n        isSubmitting\n      } = _ref3;\n      return /*#__PURE__*/React.createElement(Form, {\n        className: \"auth-form\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"email\",\n        className: \"form-label\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"form-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), \"Email Address\"), /*#__PURE__*/React.createElement(Field, {\n        id: \"email\",\n        name: \"email\",\n        type: \"email\",\n        placeholder: \"Enter your email address\",\n        className: \"form-control \".concat(errors.email && touched.email ? 'form-control-error' : ''),\n        disabled: isSubmitting\n      }), errors.email && touched.email && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"error-message\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"error-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), errors.email)), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"password\",\n        className: \"form-label\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"form-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }), /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), \"Password\"), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-control-password-wrapper\"\n      }, /*#__PURE__*/React.createElement(Field, {\n        id: \"password\",\n        name: \"password\",\n        type: showPassword ? 'text' : 'password',\n        placeholder: \"Enter your password\",\n        className: \"form-control \".concat(errors.password && touched.password ? 'form-control-error' : ''),\n        disabled: isSubmitting\n      }), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: \"password-toggle-btn\",\n        tabIndex: -1,\n        \"aria-label\": showPassword ? 'Hide password' : 'Show password',\n        onClick: () => setShowPassword(v => !v)\n      }, showPassword ? /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })) : /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }), /*#__PURE__*/React.createElement(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"3\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })))), errors.password && touched.password && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"error-message\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"error-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), errors.password)), error && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"error-message error-message-global\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"error-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), error), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"submit\",\n        className: \"btn btn-primary btn-auth\",\n        disabled: isSubmitting\n      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"loading-spinner\"\n      }), \"Signing in...\") : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"btn-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), \"Sign In\")), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"auth-links-row\"\n      }, /*#__PURE__*/React.createElement(Link, {\n        to: \"/forgot-password\",\n        className: \"auth-link\"\n      }, \"Forgot password?\")));\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-footer\"\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      className: \"auth-footer-text\"\n    }, \"Don't have an account?\", /*#__PURE__*/React.createElement(Link, {\n      to: \"/signup\",\n      className: \"auth-link\"\n    }, \"Create an account\")))));\n  };\n  _s(LoginPage, \"NEaA31AkfK7s/oVXFdSbcR+dzU4=\", false, function () {\n    return [useNavigate, useTracker];\n  });\n  _c = LoginPage;\n  var _c;\n  $RefreshReg$(_c, \"LoginPage\");\n}.call(this, module);", "map": {"version": 3, "names": ["LoginSchema", "<PERSON><PERSON>", "object", "shape", "email", "string", "required", "password", "min", "LoginPage", "_s", "navigate", "useNavigate", "error", "setError", "useState", "resendEmailSent", "setResendEmailSent", "showPassword", "setShowPassword", "user", "useTracker", "subscription", "Meteor", "subscribe", "isLoading", "ready", "useEffect", "_user$profile", "_user$roles", "role", "profile", "roles", "handleResendVerification", "call", "err", "reason", "handleSubmit", "_ref", "_ref2", "setSubmitting", "loginWithPassword", "console", "message", "details", "errorType", "stack", "fullError", "JSON", "stringify", "React", "createElement", "onClick", "className", "style", "background", "border", "padding", "cursor", "marginTop", "color", "fontSize", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "<PERSON><PERSON>", "initialValues", "validationSchema", "onSubmit", "_ref3", "errors", "touched", "isSubmitting", "Form", "htmlFor", "Field", "id", "name", "type", "placeholder", "concat", "disabled", "tabIndex", "v", "cx", "cy", "r", "Fragment", "Link", "to", "_c", "$RefreshReg$", "module"], "sources": ["imports/ui/pages/LoginPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Meteor } from 'meteor/meteor';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Formik, Form, Field } from 'formik';\nimport * as Yup from 'yup';\nimport { useTracker } from 'meteor/react-meteor-data';\n\nconst LoginSchema = Yup.object().shape({\n  email: Yup.string()\n    .email('Invalid email address')\n    .required('Email is required'),\n  password: Yup.string()\n    .min(6, 'Password must be at least 6 characters')\n    .required('Password is required'),\n});\n\nexport const LoginPage = () => {\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const [resendEmailSent, setResendEmailSent] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n\n  const { user } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    return {\n      user: Meteor.user(),\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  useEffect(() => {\n    if (user) {\n      const role = user.profile?.role || user.roles?.[0];\n      navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleResendVerification = (email) => {\n    Meteor.call('users.resendVerificationEmail', email, (err) => {\n      if (err) {\n        setError(err.reason || 'Failed to resend verification email');\n      } else {\n        setResendEmailSent(true);\n      }\n    });\n  };\n\n  const handleSubmit = ({ email, password }, { setSubmitting }) => {\n    setError(''); // Clear any previous errors\n    setResendEmailSent(false);\n    \n    Meteor.loginWithPassword(email, password, (err) => {\n      if (err) {\n        console.error('Login error details:', {\n          error: err.error,\n          reason: err.reason,\n          message: err.message,\n          details: err.details,\n          errorType: err.errorType,\n          stack: err.stack,\n          fullError: JSON.stringify(err, null, 2)\n        });\n        if (err.error === 'email-not-verified') {\n          setError(\n            <div>\n              Email not verified. \n              <button\n                onClick={() => handleResendVerification(email)}\n                className=\"auth-link\"\n                style={{ \n                  background: 'none',\n                  border: 'none',\n                  padding: '0 4px',\n                  cursor: 'pointer'\n                }}\n              >\n                Resend verification email\n              </button>\n              {resendEmailSent && (\n                <div style={{ \n                  marginTop: '8px',\n                  color: 'var(--success)',\n                  fontSize: '0.875rem'\n                }}>\n                  Verification email sent!\n                </div>\n              )}\n            </div>\n          );\n        } else {\n          setError(err.reason || 'Login failed. Please check your credentials.');\n        }\n        setSubmitting(false);\n      }\n    });\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n        <h1 className=\"auth-title\">Welcome Back</h1>\n          <p className=\"auth-subtitle\">\n            Sign in to access your workspace and manage your tasks efficiently\n        </p>\n        </div>\n        \n        <Formik\n          initialValues={{ email: '', password: '' }}\n          validationSchema={LoginSchema}\n          onSubmit={handleSubmit}\n        >\n          {({ errors, touched, isSubmitting }) => (\n            <Form className=\"auth-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Email Address\n                </label>\n                <Field\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  placeholder=\"Enter your email address\"\n                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}\n                  disabled={isSubmitting}\n                />\n                {errors.email && touched.email && (\n                  <div className=\"error-message\">\n                    <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    {errors.email}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Password\n                </label>\n                <div className=\"form-control-password-wrapper\">\n                <Field\n                  id=\"password\"\n                  name=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                  placeholder=\"Enter your password\"\n                    className={`form-control ${errors.password && touched.password ? 'form-control-error' : ''}`}\n                  disabled={isSubmitting}\n                />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle-btn\"\n                    tabIndex={-1}\n                    aria-label={showPassword ? 'Hide password' : 'Show password'}\n                    onClick={() => setShowPassword((v) => !v)}\n                  >\n                    {showPassword ? (\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.password && touched.password && (\n                  <div className=\"error-message\">\n                    <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    {errors.password}\n                  </div>\n                )}\n              </div>\n\n              {error && (\n                <div className=\"error-message error-message-global\">\n                  <svg className=\"error-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  {error}\n                </div>\n              )}\n\n              <button \n                type=\"submit\" \n                className=\"btn btn-primary btn-auth\"\n                disabled={isSubmitting}\n              >\n                {isSubmitting ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    Signing in...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    Sign In\n                  </>\n                )}\n              </button>\n\n              <div className=\"auth-links-row\">\n                <Link to=\"/forgot-password\" className=\"auth-link\">Forgot password?</Link>\n              </div>\n            </Form>\n          )}\n        </Formik>\n\n        <div className=\"auth-footer\">\n          <p className=\"auth-footer-text\">\n            Don't have an account? \n            <Link to=\"/signup\" className=\"auth-link\">\n              Create an account\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}; "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAOA,MAAMA,WAAW,GAAGC,GAAG,CAACC,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACrCC,KAAK,EAAEH,GAAG,CAACI,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;IAChCC,QAAQ,EAAEN,GAAG,CAACI,MAAM,CAAC,CAAC,CACnBG,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDF,QAAQ,CAAC,sBAAsB;EACpC,CAAC,CAAC;EAEK,MAAMG,SAAS,GAAGA,CAAA,KAAM;IAAAC,EAAA;IAC7B,MAAMC,QAAQ,GAAGC,WAAW,CAAC,CAAC;IAC9B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGC,QAAQ,CAAC,EAAE,CAAC;IACtC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGF,QAAQ,CAAC,KAAK,CAAC;IAC7D,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGJ,QAAQ,CAAC,KAAK,CAAC;IAEvD,MAAM;MAAEK;IAAK,CAAC,GAAGC,UAAU,CAAC,MAAM;MAChC,MAAMC,YAAY,GAAGC,MAAM,CAACC,SAAS,CAAC,UAAU,CAAC;MACjD,OAAO;QACLJ,IAAI,EAAEG,MAAM,CAACH,IAAI,CAAC,CAAC;QACnBK,SAAS,EAAE,CAACH,YAAY,CAACI,KAAK,CAAC;MACjC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IAENC,SAAS,CAAC,MAAM;MACd,IAAIP,IAAI,EAAE;QAAA,IAAAQ,aAAA,EAAAC,WAAA;QACR,MAAMC,IAAI,GAAG,EAAAF,aAAA,GAAAR,IAAI,CAACW,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcE,IAAI,OAAAD,WAAA,GAAIT,IAAI,CAACY,KAAK,cAAAH,WAAA,uBAAVA,WAAA,CAAa,CAAC,CAAC;QAClDlB,QAAQ,CAACmB,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;MACrE;IACF,CAAC,EAAE,CAACV,IAAI,EAAET,QAAQ,CAAC,CAAC;IAEpB,MAAMsB,wBAAwB,GAAI7B,KAAK,IAAK;MAC1CmB,MAAM,CAACW,IAAI,CAAC,+BAA+B,EAAE9B,KAAK,EAAG+B,GAAG,IAAK;QAC3D,IAAIA,GAAG,EAAE;UACPrB,QAAQ,CAACqB,GAAG,CAACC,MAAM,IAAI,qCAAqC,CAAC;QAC/D,CAAC,MAAM;UACLnB,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC;IAED,MAAMoB,YAAY,GAAGA,CAAAC,IAAA,EAAAC,KAAA,KAA4C;MAAA,IAA3C;QAAEnC,KAAK;QAAEG;MAAS,CAAC,GAAA+B,IAAA;MAAA,IAAE;QAAEE;MAAc,CAAC,GAAAD,KAAA;MAC1DzB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MACdG,kBAAkB,CAAC,KAAK,CAAC;MAEzBM,MAAM,CAACkB,iBAAiB,CAACrC,KAAK,EAAEG,QAAQ,EAAG4B,GAAG,IAAK;QACjD,IAAIA,GAAG,EAAE;UACPO,OAAO,CAAC7B,KAAK,CAAC,sBAAsB,EAAE;YACpCA,KAAK,EAAEsB,GAAG,CAACtB,KAAK;YAChBuB,MAAM,EAAED,GAAG,CAACC,MAAM;YAClBO,OAAO,EAAER,GAAG,CAACQ,OAAO;YACpBC,OAAO,EAAET,GAAG,CAACS,OAAO;YACpBC,SAAS,EAAEV,GAAG,CAACU,SAAS;YACxBC,KAAK,EAAEX,GAAG,CAACW,KAAK;YAChBC,SAAS,EAAEC,IAAI,CAACC,SAAS,CAACd,GAAG,EAAE,IAAI,EAAE,CAAC;UACxC,CAAC,CAAC;UACF,IAAIA,GAAG,CAACtB,KAAK,KAAK,oBAAoB,EAAE;YACtCC,QAAQ,cACNoC,KAAA,CAAAC,aAAA,cAAI,qBAEF,eAAAD,KAAA,CAAAC,aAAA;cACEC,OAAO,EAAEA,CAAA,KAAMnB,wBAAwB,CAAC7B,KAAK,CAAE;cAC/CiD,SAAS,EAAC,WAAW;cACrBC,KAAK,EAAE;gBACLC,UAAU,EAAE,MAAM;gBAClBC,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE,OAAO;gBAChBC,MAAM,EAAE;cACV;YAAE,8BAGI,GACP1C,eAAe,iBACdkC,KAAA,CAAAC,aAAA;cAAKG,KAAK,EAAE;gBACVK,SAAS,EAAE,KAAK;gBAChBC,KAAK,EAAE,gBAAgB;gBACvBC,QAAQ,EAAE;cACZ;YAAE,6BAEG,CAEJ,CACP,CAAC;UACH,CAAC,MAAM;YACL/C,QAAQ,CAACqB,GAAG,CAACC,MAAM,IAAI,8CAA8C,CAAC;UACxE;UACAI,aAAa,CAAC,KAAK,CAAC;QACtB;MACF,CAAC,CAAC;IACJ,CAAC;IAED,oBACEU,KAAA,CAAAC,aAAA;MAAKE,SAAS,EAAC;IAAgB,gBAC7BH,KAAA,CAAAC,aAAA;MAAKE,SAAS,EAAC;IAAW,gBACxBH,KAAA,CAAAC,aAAA;MAAKE,SAAS,EAAC;IAAa,gBAC1BH,KAAA,CAAAC,aAAA;MAAKE,SAAS,EAAC;IAAW,gBACxBH,KAAA,CAAAC,aAAA;MAAKW,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC;IAA4B,gBAC5FhB,KAAA,CAAAC,aAAA;MAAMgB,CAAC,EAAC,oIAAoI;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAACC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO,EAC5N,CACF,gBACPrB,KAAA,CAAAC,aAAA;MAAIE,SAAS,EAAC;IAAY,GAAC,cAAgB,gBACzCH,KAAA,CAAAC,aAAA;MAAGE,SAAS,EAAC;IAAe,uEAE3B,CACE,gBAELH,KAAA,CAAAC,aAAA,CAACqB,MAAM;MACLC,aAAa,EAAE;QAAErE,KAAK,EAAE,EAAE;QAAEG,QAAQ,EAAE;MAAG,CAAE;MAC3CmE,gBAAgB,EAAE1E,WAAY;MAC9B2E,QAAQ,EAAEtC;IAAa,GAEtBuC,KAAA;MAAA,IAAC;QAAEC,MAAM;QAAEC,OAAO;QAAEC;MAAa,CAAC,GAAAH,KAAA;MAAA,oBACjC1B,KAAA,CAAAC,aAAA,CAAC6B,IAAI;QAAC3B,SAAS,EAAC;MAAW,gBACzBH,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC;MAAY,gBACzBH,KAAA,CAAAC,aAAA;QAAO8B,OAAO,EAAC,OAAO;QAAC5B,SAAS,EAAC;MAAY,gBAC3CH,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC,WAAW;QAACS,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAClHhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,sOAAsO;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC9T,kBAEA,gBACPrB,KAAA,CAAAC,aAAA,CAAC+B,KAAK;QACJC,EAAE,EAAC,OAAO;QACVC,IAAI,EAAC,OAAO;QACZC,IAAI,EAAC,OAAO;QACZC,WAAW,EAAC,0BAA0B;QACtCjC,SAAS,kBAAAkC,MAAA,CAAkBV,MAAM,CAACzE,KAAK,IAAI0E,OAAO,CAAC1E,KAAK,GAAG,oBAAoB,GAAG,EAAE,CAAG;QACvFoF,QAAQ,EAAET;MAAa,IAExBF,MAAM,CAACzE,KAAK,IAAI0E,OAAO,CAAC1E,KAAK,iBAC5B8C,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC;MAAe,gBAC5BH,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC,YAAY;QAACS,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBACnHhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,uIAAuI;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC/N,GACJM,MAAM,CAACzE,KACL,CAEJ,gBAEL8C,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC;MAAY,gBACzBH,KAAA,CAAAC,aAAA;QAAO8B,OAAO,EAAC,UAAU;QAAC5B,SAAS,EAAC;MAAY,gBAC9CH,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC,WAAW;QAACS,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAClHhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,+HAA+H;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,iBAC1NrB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,mIAAmI;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC3N,aAEA,gBACPrB,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC;MAA+B,gBAC9CH,KAAA,CAAAC,aAAA,CAAC+B,KAAK;QACJC,EAAE,EAAC,UAAU;QACbC,IAAI,EAAC,UAAU;QACbC,IAAI,EAAEnE,YAAY,GAAG,MAAM,GAAG,UAAW;QAC3CoE,WAAW,EAAC,qBAAqB;QAC/BjC,SAAS,kBAAAkC,MAAA,CAAkBV,MAAM,CAACtE,QAAQ,IAAIuE,OAAO,CAACvE,QAAQ,GAAG,oBAAoB,GAAG,EAAE,CAAG;QAC/FiF,QAAQ,EAAET;MAAa,iBAEvB7B,KAAA,CAAAC,aAAA;QACEkC,IAAI,EAAC,QAAQ;QACbhC,SAAS,EAAC,qBAAqB;QAC/BoC,QAAQ,EAAE,CAAC,CAAE;QACb,cAAYvE,YAAY,GAAG,eAAe,GAAG,eAAgB;QAC7DkC,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAAEuE,CAAC,IAAK,CAACA,CAAC;MAAE,GAEzCxE,YAAY,gBACXgC,KAAA,CAAAC,aAAA;QAAKW,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,wPAAwP;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAChV,CAAC,gBAENrB,KAAA,CAAAC,aAAA;QAAKW,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,8FAA8F;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,iBACzLrB,KAAA,CAAAC,aAAA;QAAQwC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACzB,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC7G,CAED,CACL,GACJM,MAAM,CAACtE,QAAQ,IAAIuE,OAAO,CAACvE,QAAQ,iBAClC2C,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC;MAAe,gBAC5BH,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC,YAAY;QAACS,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBACnHhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,uIAAuI;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC/N,GACJM,MAAM,CAACtE,QACL,CAEJ,GAEJM,KAAK,iBACJqC,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC;MAAoC,gBACjDH,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC,YAAY;QAACS,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBACnHhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,uIAAuI;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC/N,GACJ1D,KACE,CACN,eAEDqC,KAAA,CAAAC,aAAA;QACEkC,IAAI,EAAC,QAAQ;QACbhC,SAAS,EAAC,0BAA0B;QACpCmC,QAAQ,EAAET;MAAa,GAEtBA,YAAY,gBACX7B,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAA4C,QAAA,qBACE5C,KAAA,CAAAC,aAAA;QAAME,SAAS,EAAC;MAAiB,CAAO,kBAE1C,CAAG,gBAEHH,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAA4C,QAAA,qBACE5C,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC,UAAU;QAACS,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBACjHhB,KAAA,CAAAC,aAAA;QAAMgB,CAAC,EAAC,qMAAqM;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC7R,YAEP,CAEI,gBAERrB,KAAA,CAAAC,aAAA;QAAKE,SAAS,EAAC;MAAgB,gBAC7BH,KAAA,CAAAC,aAAA,CAAC4C,IAAI;QAACC,EAAE,EAAC,kBAAkB;QAAC3C,SAAS,EAAC;MAAW,GAAC,kBAAsB,CACrE,CACD,CACP;IAAA,CACK,gBAERH,KAAA,CAAAC,aAAA;MAAKE,SAAS,EAAC;IAAa,gBAC1BH,KAAA,CAAAC,aAAA;MAAGE,SAAS,EAAC;IAAkB,2BAE7B,eAAAH,KAAA,CAAAC,aAAA,CAAC4C,IAAI;MAACC,EAAE,EAAC,SAAS;MAAC3C,SAAS,EAAC;IAAW,sBAElC,CACL,CACA,CACF,CACF,CAAC;EAEV,CAAC;EAAC3C,EAAA,CA7NWD,SAAS;IAAA,QACHG,WAAW,EAKXS,UAAU;EAAA;EAAA4E,EAAA,GANhBxF,SAAS;EAAA,IAAAwF,EAAA;EAAAC,YAAA,CAAAD,EAAA;AAAA,EAAA/D,IAAA,OAAAiE,MAAA", "ignoreList": []}, "sourceType": "module", "externalDependencies": {}, "hash": "43550c007cf18473cc43923c411d435e8a2724d2"}