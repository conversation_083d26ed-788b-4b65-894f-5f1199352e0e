{"name": "meteor-dev-bundle", "private": true, "dependencies": {"promise": "8.3.0", "@meteorjs/reify": "0.25.4", "@babel/parser": "7.25.0", "lru-cache": "6.0.0", "underscore": "1.13.7", "source-map-support": "https://github.com/meteor/node-source-map-support/tarball/81bce1f99625e62af73338f63afcf2b44c6cfa5e", "@types/semver": "7.5.8", "semver": "7.6.3", "node-gyp": "10.2.0", "@mapbox/node-pre-gyp": "1.0.11"}, "devDependencies": {"@types/underscore": "1.11.15", "split2": "3.2.2", "multipipe": "2.0.1", "chalk": "4.1.2"}, "scripts": {"install": "node npm-rebuild.js"}}