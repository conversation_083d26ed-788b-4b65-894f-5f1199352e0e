{"version": 3, "sources": ["meteor://💻app/packages/webapp-hashing/webapp-hashing.js"], "names": ["_objectWithoutProperties", "module", "link", "default", "v", "_excluded", "createHash", "__reifyWaitForDeps__", "WebAppHashing", "calculateClientHash", "manifest", "includeFilter", "runtimeConfigOverride", "hash", "autoupdateVersion", "autoupdateVersionRefreshable", "autoupdateVersionCordova", "__meteor_runtime_config__", "runtimeCfg", "update", "JSON", "stringify", "for<PERSON>ach", "resource", "type", "replaceable", "where", "path", "digest", "calculateCordovaCompatibilityHash", "platformVersion", "pluginVersions", "plugins", "Object", "keys", "sort", "plugin", "version", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,wBAAwB;IAACC,MAAM,CAACC,IAAI,CAAC,gDAAgD,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,wBAAwB,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,MAAAC,SAAA;IAAtI,IAAIC,UAAU;IAACL,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAC;MAACI,UAAUA,CAACF,CAAC,EAAC;QAACE,UAAU,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEjIC,aAAa,GAAG,CAAC,CAAC;;IAElB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEAA,aAAa,CAACC,mBAAmB,GAC/B,UAAUC,QAAQ,EAAEC,aAAa,EAAEC,qBAAqB,EAAE;MAC1D,IAAIC,IAAI,GAAGP,UAAU,CAAC,MAAM,CAAC;;MAE7B;MACA;MACA,IAAI;UAAEQ,iBAAiB;UAAEC,4BAA4B;UAAEC;QAAwC,CAAC,GAAGC,yBAAyB;QAAxCC,UAAU,GAAAlB,wBAAA,CAAKiB,yBAAyB,EAAAZ,SAAA;MAE5H,IAAIO,qBAAqB,EAAE;QACzBM,UAAU,GAAGN,qBAAqB;MACpC;MAEAC,IAAI,CAACM,MAAM,CAACC,IAAI,CAACC,SAAS,CAACH,UAAU,EAAE,MAAM,CAAC,CAAC;MAE/CR,QAAQ,CAACY,OAAO,CAAC,UAAUC,QAAQ,EAAE;QACjC,IAAI,CAAC,CAAEZ,aAAa,IAAIA,aAAa,CAACY,QAAQ,CAACC,IAAI,EAAED,QAAQ,CAACE,WAAW,CAAC,MACrEF,QAAQ,CAACG,KAAK,KAAK,QAAQ,IAAIH,QAAQ,CAACG,KAAK,KAAK,UAAU,CAAC,EAAE;UACpEb,IAAI,CAACM,MAAM,CAACI,QAAQ,CAACI,IAAI,CAAC;UAC1Bd,IAAI,CAACM,MAAM,CAACI,QAAQ,CAACV,IAAI,CAAC;QAC5B;MACF,CAAC,CAAC;MACF,OAAOA,IAAI,CAACe,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAEDpB,aAAa,CAACqB,iCAAiC,GAC7C,UAASC,eAAe,EAAEC,cAAc,EAAE;MAC1C,MAAMlB,IAAI,GAAGP,UAAU,CAAC,MAAM,CAAC;MAE/BO,IAAI,CAACM,MAAM,CAACW,eAAe,CAAC;;MAE5B;MACA,MAAME,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,cAAc,CAAC,CAACI,IAAI,CAAC,CAAC;MAClD,KAAK,IAAIC,MAAM,IAAIJ,OAAO,EAAE;QAC1B,MAAMK,OAAO,GAAGN,cAAc,CAACK,MAAM,CAAC;QACtCvB,IAAI,CAACM,MAAM,CAACiB,MAAM,CAAC;QACnBvB,IAAI,CAACM,MAAM,CAACkB,OAAO,CAAC;MACtB;MAEA,OAAOxB,IAAI,CAACe,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAACU,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/webapp-hashing.js", "sourcesContent": ["import { createHash } from \"crypto\";\n\nWebAppHashing = {};\n\n// Calculate a hash of all the client resources downloaded by the\n// browser, including the application HTML, runtime config, code, and\n// static files.\n//\n// This hash *must* change if any resources seen by the browser\n// change, and ideally *doesn't* change for any server-only changes\n// (but the second is a performance enhancement, not a hard\n// requirement).\n\nWebAppHashing.calculateClientHash =\n  function (manifest, includeFilter, runtimeConfigOverride) {\n  var hash = createHash('sha1');\n\n  // Omit the old hashed client values in the new hash. These may be\n  // modified in the new boilerplate.\n  var { autoupdateVersion, autoupdateVersionRefreshable, autoupdateVersionCordova, ...runtimeCfg } = __meteor_runtime_config__;\n\n  if (runtimeConfigOverride) {\n    runtimeCfg = runtimeConfigOverride;\n  }\n\n  hash.update(JSON.stringify(runtimeCfg, 'utf8'));\n\n  manifest.forEach(function (resource) {\n      if ((! includeFilter || includeFilter(resource.type, resource.replaceable)) &&\n          (resource.where === 'client' || resource.where === 'internal')) {\n      hash.update(resource.path);\n      hash.update(resource.hash);\n    }\n  });\n  return hash.digest('hex');\n};\n\nWebAppHashing.calculateCordovaCompatibilityHash =\n  function(platformVersion, pluginVersions) {\n  const hash = createHash('sha1');\n\n  hash.update(platformVersion);\n\n  // Sort plugins first so iteration order doesn't affect the hash\n  const plugins = Object.keys(pluginVersions).sort();\n  for (let plugin of plugins) {\n    const version = pluginVersions[plugin];\n    hash.update(plugin);\n    hash.update(version);\n  }\n\n  return hash.digest('hex');\n};\n"]}