{"version": 3, "sources": ["meteor://💻app/packages/id-map/id-map.js"], "names": ["module", "export", "IdMap", "constructor", "idStringify", "idParse", "_map", "Map", "_idStringify", "JSON", "stringify", "_idParse", "parse", "get", "id", "key", "set", "value", "remove", "delete", "has", "empty", "size", "clear", "for<PERSON>ach", "iterator", "breakIfFalse", "call", "forEachAsync", "<PERSON><PERSON><PERSON><PERSON>", "def", "clone", "EJSON"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,KAAK,EAACA,CAAA,KAAIA;AAAK,CAAC,CAAC;AACzB,MAAMA,KAAK,CAAC;EACjBC,WAAWA,CAACC,WAAW,EAAEC,OAAO,EAAE;IAChC,IAAI,CAACC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,YAAY,GAAGJ,WAAW,IAAIK,IAAI,CAACC,SAAS;IACjD,IAAI,CAACC,QAAQ,GAAGN,OAAO,IAAII,IAAI,CAACG,KAAK;EACvC;;EAEF;EACA;EACA;EACA;;EAEEC,GAAGA,CAACC,EAAE,EAAE;IACN,MAAMC,GAAG,GAAG,IAAI,CAACP,YAAY,CAACM,EAAE,CAAC;IACjC,OAAO,IAAI,CAACR,IAAI,CAACO,GAAG,CAACE,GAAG,CAAC;EAC3B;EAEAC,GAAGA,CAACF,EAAE,EAAEG,KAAK,EAAE;IACb,MAAMF,GAAG,GAAG,IAAI,CAACP,YAAY,CAACM,EAAE,CAAC;IACjC,IAAI,CAACR,IAAI,CAACU,GAAG,CAACD,GAAG,EAAEE,KAAK,CAAC;EAC3B;EAEAC,MAAMA,CAACJ,EAAE,EAAE;IACT,MAAMC,GAAG,GAAG,IAAI,CAACP,YAAY,CAACM,EAAE,CAAC;IACjC,IAAI,CAACR,IAAI,CAACa,MAAM,CAACJ,GAAG,CAAC;EACvB;EAEAK,GAAGA,CAACN,EAAE,EAAE;IACN,MAAMC,GAAG,GAAG,IAAI,CAACP,YAAY,CAACM,EAAE,CAAC;IACjC,OAAO,IAAI,CAACR,IAAI,CAACc,GAAG,CAACL,GAAG,CAAC;EAC3B;EAEAM,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,KAAK,CAAC;EAC7B;EAEAC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACjB,IAAI,CAACiB,KAAK,CAAC,CAAC;EACnB;;EAEA;EACAC,OAAOA,CAACC,QAAQ,EAAE;IAChB;IACA,KAAK,IAAI,CAACV,GAAG,EAAEE,KAAK,CAAC,IAAI,IAAI,CAACX,IAAI,EAAC;MACjC,MAAMoB,YAAY,GAAGD,QAAQ,CAACE,IAAI,CAChC,IAAI,EACJV,KAAK,EACL,IAAI,CAACN,QAAQ,CAACI,GAAG,CACnB,CAAC;MACD,IAAIW,YAAY,KAAK,KAAK,EAAE;QAC1B;MACF;IACF;EACF;EAEA,MAAME,YAAYA,CAACH,QAAQ,EAAE;IAC3B,KAAK,IAAI,CAACV,GAAG,EAAEE,KAAK,CAAC,IAAI,IAAI,CAACX,IAAI,EAAC;MACjC,MAAMoB,YAAY,GAAG,MAAMD,QAAQ,CAACE,IAAI,CACpC,IAAI,EACJV,KAAK,EACL,IAAI,CAACN,QAAQ,CAACI,GAAG,CACrB,CAAC;MACD,IAAIW,YAAY,KAAK,KAAK,EAAE;QAC1B;MACF;IACF;EACF;EAEAJ,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAAChB,IAAI,CAACgB,IAAI;EACvB;EAEAO,UAAUA,CAACf,EAAE,EAAEgB,GAAG,EAAE;IAClB,MAAMf,GAAG,GAAG,IAAI,CAACP,YAAY,CAACM,EAAE,CAAC;IACjC,IAAI,IAAI,CAACR,IAAI,CAACc,GAAG,CAACL,GAAG,CAAC,EAAE;MACtB,OAAO,IAAI,CAACT,IAAI,CAACO,GAAG,CAACE,GAAG,CAAC;IAC3B;IACA,IAAI,CAACT,IAAI,CAACU,GAAG,CAACD,GAAG,EAAEe,GAAG,CAAC;IACvB,OAAOA,GAAG;EACZ;;EAEA;EACA;EACAC,KAAKA,CAAA,EAAG;IACN,MAAMA,KAAK,GAAG,IAAI7B,KAAK,CAAC,IAAI,CAACM,YAAY,EAAE,IAAI,CAACG,QAAQ,CAAC;IACzD;IACA,IAAI,CAACL,IAAI,CAACkB,OAAO,CAAC,UAASP,KAAK,EAAEF,GAAG,EAAC;MACpCgB,KAAK,CAACzB,IAAI,CAACU,GAAG,CAACD,GAAG,EAAEiB,KAAK,CAACD,KAAK,CAACd,KAAK,CAAC,CAAC;IACzC,CAAC,CAAC;IACF,OAAOc,KAAK;EACd;AACF,C", "file": "/packages/id-map.js", "sourcesContent": ["\nexport class IdMap {\n  constructor(idStringify, idParse) {\n    this._map = new Map();\n    this._idStringify = idStringify || JSON.stringify;\n    this._idParse = idParse || JSON.parse;\n  }\n\n// Some of these methods are designed to match methods on OrderedDict, since\n// (eg) ObserveMultiplex and _CachingChangeObserver use them interchangeably.\n// (Conceivably, this should be replaced with \"UnorderedDict\" with a specific\n// set of methods that overlap between the two.)\n\n  get(id) {\n    const key = this._idStringify(id);\n    return this._map.get(key);\n  }\n\n  set(id, value) {\n    const key = this._idStringify(id);\n    this._map.set(key, value);\n  }\n\n  remove(id) {\n    const key = this._idStringify(id);\n    this._map.delete(key);\n  }\n\n  has(id) {\n    const key = this._idStringify(id);\n    return this._map.has(key);\n  }\n\n  empty() {\n    return this._map.size === 0;\n  }\n\n  clear() {\n    this._map.clear();\n  }\n\n  // Iterates over the items in the map. Return `false` to break the loop.\n  forEach(iterator) {\n    // don't use _.each, because we can't break out of it.\n    for (let [key, value] of this._map){\n      const breakIfFalse = iterator.call(\n        null,\n        value,\n        this._idParse(key)\n      );\n      if (breakIfFalse === false) {\n        return;\n      }\n    }\n  }\n\n  async forEachAsync(iterator) {\n    for (let [key, value] of this._map){\n      const breakIfFalse = await iterator.call(\n          null,\n          value,\n          this._idParse(key)\n      );\n      if (breakIfFalse === false) {\n        return;\n      }\n    }\n  }\n\n  size() {\n    return this._map.size;\n  }\n\n  setDefault(id, def) {\n    const key = this._idStringify(id);\n    if (this._map.has(key)) {\n      return this._map.get(key);\n    }\n    this._map.set(key, def);\n    return def;\n  }\n\n  // Assumes that values are EJSON-cloneable, and that we don't need to clone\n  // IDs (ie, that nobody is going to mutate an ObjectId).\n  clone() {\n    const clone = new IdMap(this._idStringify, this._idParse);\n    // copy directly to avoid stringify/parse overhead\n    this._map.forEach(function(value, key){\n      clone._map.set(key, EJSON.clone(value));\n    });\n    return clone;\n  }\n}\n"]}