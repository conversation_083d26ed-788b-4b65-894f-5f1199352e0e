  {/* Attachments Section */}
  {viewingTask && viewingTask.attachments && viewingTask.attachments.length > 0 && (
    <div>
      <h3 style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '8px' }}>Attachments</h3>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '8px'
      }}>
        {viewingTask.attachments.map((attachment, index) => {
          // Ensure we're working with string IDs
          const currentUserId = String(Meteor.userId());
          const uploadedById = String(attachment.uploadedBy);
          const isOwner = currentUserId === uploadedById;

          // Debug logging
          console.log('Attachment render:', {
            attachment,
            currentUserId,
            uploadedById,
            isOwner,
            hasUploadedBy: !!attachment.uploadedBy
          });

          return (
            <div
              key={index}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px',
                backgroundColor: '#f8fafc',
                borderRadius: '6px',
                fontSize: '0.875rem'
              }}
            >
              <span style={{ color: '#0f172a' }}>📎</span>
              <span style={{ color: '#0f172a' }}>{attachment.name}</span>
              <span style={{ color: '#64748b', fontSize: '0.75rem' }}>
                {new Date(attachment.uploadedAt).toLocaleDateString()}
              </span>
              {attachment.uploadedBy && (
                <span style={{ color: '#64748b', fontSize: '0.75rem' }}>
                  by {userNames[attachment.uploadedBy] || 'Unknown User'}
                </span>
              )}
              <div style={{ marginLeft: 'auto', display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => {
                    try {
                      // Remove data URL prefix if present
                      let base64Data = attachment.data;
                      if (base64Data.startsWith('data:')) {
                        base64Data = base64Data.split(',')[1];
                      }

                      // Create a blob from the base64 data
                      const byteCharacters = atob(base64Data);
                      const byteNumbers = new Array(byteCharacters.length);
                      for (let i = 0; i < byteCharacters.length; i++) {
                        byteNumbers[i] = byteCharacters.charCodeAt(i);
                      }
                      const byteArray = new Uint8Array(byteNumbers);
                      
                      // Get the correct MIME type
                      const mimeType = attachment.type || 'application/octet-stream';
                      const blob = new Blob([byteArray], { type: mimeType });

                      // Create a download link and trigger it
                      const url = window.URL.createObjectURL(blob);
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = attachment.name;
                      document.body.appendChild(link);
                      link.click();
                      
                      // Clean up
                      setTimeout(() => {
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                      }, 100);
                    } catch (error) {
                      console.error('Error downloading file:', error);
                      alert('Failed to download file. Please try again.');
                    }
                  }}
                  style={{
                    padding: '4px 8px',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '0.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}
                >
                  <span>⬇️</span>
                  Download
                </button>
                {isOwner && (
                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to remove this attachment?')) {
                        Meteor.call('tasks.removeAttachment', viewingTask._id, index, (error) => {
                          if (error) {
                            console.error('Error removing attachment:', error);
                            alert(error.reason || 'Failed to remove attachment. Please try again.');
                          } else {
                            setSuccessMessage('Attachment removed successfully!');
                            setTimeout(() => setSuccessMessage(''), 3000);
                          }
                        });
                      }
                    }}
                    style={{
                      padding: '4px 8px',
                      backgroundColor: '#ef4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '0.75rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}
                  >
                    <span>🗑️</span>
                    Remove
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  )}

  {/* Links Section */}
  {viewingTask && viewingTask.links && viewingTask.links.length > 0 && (
    <div>
      <h3 style={{ fontSize: '0.875rem', color: '#64748b', marginBottom: '8px' }}>Links</h3>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '8px'
      }}>
        {viewingTask.links.map((link, index) => {
          // Convert IDs to strings for comparison
          const currentUserId = String(Meteor.userId());
          const addedById = String(link.addedBy);
          const isOwner = currentUserId === addedById;

          return (
            <div
              key={index}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px',
                backgroundColor: '#f8fafc',
                borderRadius: '6px',
                fontSize: '0.875rem'
              }}
            >
              <span style={{ color: '#0f172a' }}>🔗</span>
              <a 
                href={link.url} 
                target="_blank" 
                rel="noopener noreferrer"
                style={{ color: '#3b82f6', textDecoration: 'none' }}
              >
                {link.url}
              </a>
              <span style={{ color: '#64748b', fontSize: '0.75rem' }}>
                {new Date(link.addedAt).toLocaleDateString()}
              </span>
              {link.addedBy && (
                <span style={{ color: '#64748b', fontSize: '0.75rem' }}>
                  by {userNames[link.addedBy] || 'Unknown User'}
                </span>
              )}
              {isOwner && (
                <button
                  onClick={() => {
                    if (window.confirm('Are you sure you want to remove this link?')) {
                      Meteor.call('tasks.removeLink', viewingTask._id, index, (error) => {
                        if (error) {
                          console.error('Error removing link:', error);
                          alert(error.reason || 'Failed to remove link. Please try again.');
                        } else {
                          setSuccessMessage('Link removed successfully!');
                          setTimeout(() => setSuccessMessage(''), 3000);
                        }
                      });
                    }
                  }}
                  style={{
                    padding: '4px 8px',
                    backgroundColor: '#ef4444',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '0.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    marginLeft: 'auto'
                  }}
                >
                  <span>🗑️</span>
                  Remove
                </button>
              )}
            </div>
          );
        })}
      </div>
    </div>
  )} 