{"version": 3, "sources": ["meteor://💻app/packages/mongo/local_collection_driver.js", "meteor://💻app/packages/mongo/collection/collection.js", "meteor://💻app/packages/mongo/collection/collection_utils.js", "meteor://💻app/packages/mongo/collection/methods_async.js", "meteor://💻app/packages/mongo/collection/methods_index.js", "meteor://💻app/packages/mongo/collection/methods_replication.js", "meteor://💻app/packages/mongo/collection/methods_sync.js", "meteor://💻app/packages/mongo/mongo_utils.js"], "names": ["module", "export", "LocalCollectionDriver", "noConnCollections", "Object", "create", "_proto", "prototype", "open", "name", "conn", "LocalCollection", "ensureCollection", "_mongo_livedata_collections", "collections", "_regeneratorRuntime", "module1", "link", "default", "v", "_objectSpread", "_slicedToArray", "normalizeProjection", "AsyncMethods", "SyncMethods", "IndexMethods", "ID_GENERATORS", "normalizeOptions", "setupAutopublish", "setupConnection", "setupDriver", "setupMutationMethods", "validateCollectionName", "ReplicationMethods", "Mongo", "Collection", "options", "_ID_GENERATORS$option", "_ID_GENERATORS", "_makeNewID", "idGeneration", "call", "_transform", "wrapTransform", "transform", "resolverType", "_connection", "driver", "_driver", "_collection", "_name", "_settingUpReplicationPromise", "_maybeSetUpReplication", "_collections", "set", "assign", "_getFindSelector", "args", "length", "_getFindOptions", "_ref", "_ref2", "newOptions", "self", "check", "Match", "Optional", "ObjectIncluding", "projection", "OneOf", "undefined", "sort", "Array", "Function", "limit", "Number", "skip", "_publishCursor", "_callee2", "cursor", "sub", "collection", "<PERSON><PERSON><PERSON><PERSON>", "async", "_callee2$", "_context2", "prev", "next", "awrap", "observe<PERSON>hanges", "added", "id", "fields", "changed", "removed", "nonMutatingCallbacks", "sent", "onStop", "_callee", "_callee$", "_context", "stop", "abrupt", "Promise", "_rewriteSelector", "selector", "_ref3", "arguments", "fallbackId", "_selectorIsId", "_id", "isArray", "Error", "Random", "_isRemoteCollection", "Meteor", "server", "dropCollectionAsync", "_callee3", "_callee3$", "_context3", "createCappedCollectionAsync", "_callee4", "byteSize", "maxDocuments", "_callee4$", "_context4", "rawCollection", "rawDatabase", "mongo", "db", "getCollection", "get", "Map", "ObjectID", "MongoID", "<PERSON><PERSON><PERSON>", "AllowDeny", "CollectionPrototype", "MONGO", "src", "DDP", "randomStream", "insecure", "hexString", "STRING", "connection", "isClient", "MongoInternals", "defaultRemoteCollectionDriver", "_require", "require", "Package", "autopublish", "_preventAutopublish", "publish", "find", "is_auto", "defineMutationMethods", "_defineMutationMethods", "useExisting", "_suppressSameNameError", "error", "message", "_debug", "methods", "manager", "findOneAsync", "_len", "_key", "_insertAsync", "doc", "getPrototypeOf", "getOwnPropertyDescriptors", "generateId", "enclosing", "_CurrentMethodInvocation", "chooseReturnValueFromCollectionResult", "result", "_isPromise", "promise", "_callMutatorMethodAsync", "then", "stubPromise", "serverPromise", "insertAsync", "updateAsync", "modifier", "insertedId", "upsert", "generatedId", "removeAsync", "upsertAsync", "_returnObject", "countDocuments", "_this$_collection", "apply", "estimatedDocumentCount", "_this$_collection2", "ensureIndexAsync", "index", "Log", "createIndexAsync", "debug", "JSON", "stringify", "_Meteor$settings", "_Meteor$settings$pack", "_Meteor$settings$pack2", "t0", "includes", "settings", "packages", "reCreateIndexOnOptionMismatch", "info", "dropIndexAsync", "console", "createIndex", "_callee6", "_registerStoreResult", "_registerStoreResult$", "wrappedStoreCommon", "wrappedStoreClient", "wrappedStoreServer", "registerStoreResult", "log<PERSON>arn", "_callee6$", "_context6", "registerStoreClient", "registerStoreServer", "saveOriginals", "retrieveOriginals", "_getCollection", "beginUpdate", "batchSize", "reset", "pauseObservers", "remove", "update", "msg", "mongoId", "idParse", "_docs", "field", "meteorBabelHelpers", "sanitizeForInObject", "value", "replace", "insert", "keys", "for<PERSON>ach", "key", "EJSON", "equals", "$unset", "$set", "endUpdate", "resumeObserversClient", "getDoc", "findOne", "resumeObserversServer", "_callee5", "_callee5$", "_context5", "warn", "log", "ok", "_len2", "_key2", "_insert", "callback", "wrappedCallback", "wrapCallback", "_callMutatorMethod", "e", "_len3", "optionsAndCallback", "_key3", "popCallbackFromArgs", "convertResult", "pop", "_objectWithoutProperties", "otherOptions", "_excluded"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,qBAAqB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,qBAAqB;EAAA;AAAC,CAAC,CAAC;AACxE,IAAMA,qBAAqB,GAAG;EACnC,SAAAA,sBAAA,EAAc;IACZ,IAAI,CAACC,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9C;EAAC,IAAAC,MAAA,GAAAJ,qBAAA,CAAAK,SAAA;EAAAD,MAAA,CAEDE,IAAI;IAAJ,SAAAA,IAAIA,CAACC,IAAI,EAAEC,IAAI,EAAE;MACf,IAAI,CAAED,IAAI,EAAE;QACV,OAAO,IAAIE,eAAe,CAAD,CAAC;MAC5B;MAEA,IAAI,CAAED,IAAI,EAAE;QACV,OAAOE,gBAAgB,CAACH,IAAI,EAAE,IAAI,CAACN,iBAAiB,CAAC;MACvD;MAEA,IAAI,CAAEO,IAAI,CAACG,2BAA2B,EAAE;QACtCH,IAAI,CAACG,2BAA2B,GAAGT,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACxD;;MAEA;MACA;MACA,OAAOO,gBAAgB,CAACH,IAAI,EAAEC,IAAI,CAACG,2BAA2B,CAAC;IACjE;IAAC,OAhBDL,IAAI;EAAA;EAAA,OAAAN,qBAAA;AAAA,KAiBL,CAAC;AAEF,SAASU,gBAAgBA,CAACH,IAAI,EAAEK,WAAW,EAAE;EAC3C,OAAQL,IAAI,IAAIK,WAAW,GACvBA,WAAW,CAACL,IAAI,CAAC,GACjBK,WAAW,CAACL,IAAI,CAAC,GAAG,IAAIE,eAAe,CAACF,IAAI,CAAC;AACnD,C;;;;;;;;;;;;EC7BA,IAAIM,mBAAmB;EAACC,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACJ,mBAAmB,GAACI,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIC,aAAa;EAACJ,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACC,aAAa,GAACD,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIE,cAAc;EAACL,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;IAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;MAACE,cAAc,GAACF,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAnV,IAAIG,mBAAmB;EAACN,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAC;IAACK,mBAAmB,EAAC,SAAAA,CAASH,CAAC,EAAC;MAACG,mBAAmB,GAACH,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAII,YAAY;EAACP,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAC;IAACM,YAAY,EAAC,SAAAA,CAASJ,CAAC,EAAC;MAACI,YAAY,GAACJ,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIK,WAAW;EAACR,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAC;IAACO,WAAW,EAAC,SAAAA,CAASL,CAAC,EAAC;MAACK,WAAW,GAACL,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIM,YAAY;EAACT,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAC;IAACQ,YAAY,EAAC,SAAAA,CAASN,CAAC,EAAC;MAACM,YAAY,GAACN,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIO,aAAa,EAACC,gBAAgB,EAACC,gBAAgB,EAACC,eAAe,EAACC,WAAW,EAACC,oBAAoB,EAACC,sBAAsB;EAAChB,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAC;IAACS,aAAa,EAAC,SAAAA,CAASP,CAAC,EAAC;MAACO,aAAa,GAACP,CAAC;IAAA,CAAC;IAACQ,gBAAgB,EAAC,SAAAA,CAASR,CAAC,EAAC;MAACQ,gBAAgB,GAACR,CAAC;IAAA,CAAC;IAACS,gBAAgB,EAAC,SAAAA,CAAST,CAAC,EAAC;MAACS,gBAAgB,GAACT,CAAC;IAAA,CAAC;IAACU,eAAe,EAAC,SAAAA,CAASV,CAAC,EAAC;MAACU,eAAe,GAACV,CAAC;IAAA,CAAC;IAACW,WAAW,EAAC,SAAAA,CAASX,CAAC,EAAC;MAACW,WAAW,GAACX,CAAC;IAAA,CAAC;IAACY,oBAAoB,EAAC,SAAAA,CAASZ,CAAC,EAAC;MAACY,oBAAoB,GAACZ,CAAC;IAAA,CAAC;IAACa,sBAAsB,EAAC,SAAAA,CAASb,CAAC,EAAC;MAACa,sBAAsB,GAACb,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIc,kBAAkB;EAACjB,OAAO,CAACC,IAAI,CAAC,uBAAuB,EAAC;IAACgB,kBAAkB,EAAC,SAAAA,CAASd,CAAC,EAAC;MAACc,kBAAkB,GAACd,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAcz/B;AACA;AACA;AACA;EACAe,KAAK,GAAG,CAAC,CAAC;;EAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA;EACAA,KAAK,CAACC,UAAU;IAAG,SAASA,UAAUA,CAAC1B,IAAI,EAAE2B,OAAO,EAAE;MAAA,IAAAC,qBAAA,EAAAC,cAAA;MACpD7B,IAAI,GAAGuB,sBAAsB,CAACvB,IAAI,CAAC;MAEnC2B,OAAO,GAAGT,gBAAgB,CAACS,OAAO,CAAC;MAEnC,IAAI,CAACG,UAAU,IAAAF,qBAAA,GAAG,CAAAC,cAAA,GAAAZ,aAAa,EAACU,OAAO,CAACI,YAAY,CAAC,cAAAH,qBAAA,uBAAnCA,qBAAA,CAAAI,IAAA,CAAAH,cAAA,EAAsC7B,IAAI,CAAC;MAE7D,IAAI,CAACiC,UAAU,GAAG/B,eAAe,CAACgC,aAAa,CAACP,OAAO,CAACQ,SAAS,CAAC;MAClE,IAAI,CAACC,YAAY,GAAGT,OAAO,CAACS,YAAY;MAExC,IAAI,CAACC,WAAW,GAAGjB,eAAe,CAACpB,IAAI,EAAE2B,OAAO,CAAC;MAEjD,IAAMW,MAAM,GAAGjB,WAAW,CAACrB,IAAI,EAAE,IAAI,CAACqC,WAAW,EAAEV,OAAO,CAAC;MAC3D,IAAI,CAACY,OAAO,GAAGD,MAAM;MAErB,IAAI,CAACE,WAAW,GAAGF,MAAM,CAACvC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACqC,WAAW,CAAC;MACtD,IAAI,CAACI,KAAK,GAAGzC,IAAI;MAEjB,IAAI,CAAC0C,4BAA4B,GAAG,IAAI,CAACC,sBAAsB,CAAC3C,IAAI,EAAE2B,OAAO,CAAC;MAE9EL,oBAAoB,CAAC,IAAI,EAAEtB,IAAI,EAAE2B,OAAO,CAAC;MAEzCR,gBAAgB,CAAC,IAAI,EAAEnB,IAAI,EAAE2B,OAAO,CAAC;MAErCF,KAAK,CAACmB,YAAY,CAACC,GAAG,CAAC7C,IAAI,EAAE,IAAI,CAAC;IACpC;IAAC,OAzB2B0B,UAAU;EAAA,GAyBrC;EAED/B,MAAM,CAACmD,MAAM,CAACrB,KAAK,CAACC,UAAU,CAAC5B,SAAS,EAAE;IACxCiD,gBAAgB,WAAAA,CAACC,IAAI,EAAE;MACrB,IAAIA,IAAI,CAACC,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAC3B,OAAOD,IAAI,CAAC,CAAC,CAAC;IACrB,CAAC;IAEDE,eAAe,WAAAA,CAACF,IAAI,EAAE;MACpB,IAAAG,IAAA,GAAoBH,IAAI,IAAI,EAAE;QAAAI,KAAA,GAAAxC,cAAA,CAAAuC,IAAA;QAArBxB,OAAO,GAAAyB,KAAA;MAChB,IAAMC,UAAU,GAAGxC,mBAAmB,CAACc,OAAO,CAAC;MAE/C,IAAI2B,IAAI,GAAG,IAAI;MACf,IAAIN,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACnB,OAAO;UAAEd,SAAS,EAAEmB,IAAI,CAACrB;QAAW,CAAC;MACvC,CAAC,MAAM;QACLsB,KAAK,CACHF,UAAU,EACVG,KAAK,CAACC,QAAQ,CACZD,KAAK,CAACE,eAAe,CAAC;UACpBC,UAAU,EAAEH,KAAK,CAACC,QAAQ,CAACD,KAAK,CAACI,KAAK,CAACjE,MAAM,EAAEkE,SAAS,CAAC,CAAC;UAC1DC,IAAI,EAAEN,KAAK,CAACC,QAAQ,CAClBD,KAAK,CAACI,KAAK,CAACjE,MAAM,EAAEoE,KAAK,EAAEC,QAAQ,EAAEH,SAAS,CAChD,CAAC;UACDI,KAAK,EAAET,KAAK,CAACC,QAAQ,CAACD,KAAK,CAACI,KAAK,CAACM,MAAM,EAAEL,SAAS,CAAC,CAAC;UACrDM,IAAI,EAAEX,KAAK,CAACC,QAAQ,CAACD,KAAK,CAACI,KAAK,CAACM,MAAM,EAAEL,SAAS,CAAC;QACrD,CAAC,CACH,CACF,CAAC;QAED,OAAAlD,aAAA;UACEwB,SAAS,EAAEmB,IAAI,CAACrB;QAAU,GACvBoB,UAAU;MAEjB;IACF;EACF,CAAC,CAAC;EAEF1D,MAAM,CAACmD,MAAM,CAACrB,KAAK,CAACC,UAAU,EAAE;IACxB0C,cAAc;MAAA,SAAAC,SAACC,MAAM,EAAEC,GAAG,EAAEC,UAAU;QAAA,IAAAC,aAAA;QAAA,OAAAnE,mBAAA,CAAAoE,KAAA;UAAA,SAAAC,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;cAAA;gBAAAF,SAAA,CAAAE,IAAA;gBAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAChBT,MAAM,CAACU,cAAc,CAC3C;kBACEC,KAAK,EAAE,SAAAA,CAASC,EAAE,EAAEC,MAAM,EAAE;oBAC1BZ,GAAG,CAACU,KAAK,CAACT,UAAU,EAAEU,EAAE,EAAEC,MAAM,CAAC;kBACnC,CAAC;kBACDC,OAAO,EAAE,SAAAA,CAASF,EAAE,EAAEC,MAAM,EAAE;oBAC5BZ,GAAG,CAACa,OAAO,CAACZ,UAAU,EAAEU,EAAE,EAAEC,MAAM,CAAC;kBACrC,CAAC;kBACDE,OAAO,EAAE,SAAAA,CAASH,EAAE,EAAE;oBACpBX,GAAG,CAACc,OAAO,CAACb,UAAU,EAAEU,EAAE,CAAC;kBAC7B;gBACF,CAAC;gBACD;gBACA;gBACA;kBAAEI,oBAAoB,EAAE;gBAAK,CACjC,CAAC;cAAA;gBAfGb,aAAa,GAAAG,SAAA,CAAAW,IAAA;gBAiBjB;gBACA;;gBAEA;gBACAhB,GAAG,CAACiB,MAAM;kBAAC,SAAAC,QAAA;oBAAA,OAAAnF,mBAAA,CAAAoE,KAAA;sBAAA,SAAAgB,SAAAC,QAAA;wBAAA,kBAAAA,QAAA,CAAAd,IAAA,GAAAc,QAAA,CAAAb,IAAA;0BAAA;4BAAAa,QAAA,CAAAb,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACIN,aAAa,CAACmB,IAAI,CAAC,CAAC;0BAAA;4BAAA,OAAAD,QAAA,CAAAE,MAAA,WAAAF,QAAA,CAAAJ,IAAA;0BAAA;0BAAA;4BAAA,OAAAI,QAAA,CAAAC,IAAA;wBAAA;sBAAA;sBAAA,OAAAF,QAAA;oBAAA,uBAAAI,OAAA;kBAAA;kBAClC,OAAAL,OAAA;gBAAA,IAAC;;gBAEF;gBAAA,OAAAb,SAAA,CAAAiB,MAAA,WACOpB,aAAa;cAAA;cAAA;gBAAA,OAAAG,SAAA,CAAAgB,IAAA;YAAA;UAAA;UAAA,OAAAjB,SAAA;QAAA,uBAAAmB,OAAA;MAAA;MAAA,OAAAzB,QAAA;IAAA;IAGtB;IACA;IACA;IACA;IACA;IACA0B,gBAAgB,WAAAA,CAACC,QAAQ,EAAuB;MAAA,IAAAC,KAAA,GAAAC,SAAA,CAAAjD,MAAA,QAAAiD,SAAA,QAAArC,SAAA,GAAAqC,SAAA,MAAJ,CAAC,CAAC;QAAjBC,UAAU,GAAAF,KAAA,CAAVE,UAAU;MACrC;MACA,IAAIjG,eAAe,CAACkG,aAAa,CAACJ,QAAQ,CAAC,EAAEA,QAAQ,GAAG;QAAEK,GAAG,EAAEL;MAAS,CAAC;MAEzE,IAAIjC,KAAK,CAACuC,OAAO,CAACN,QAAQ,CAAC,EAAE;QAC3B;QACA;QACA,MAAM,IAAIO,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEA,IAAI,CAACP,QAAQ,IAAK,KAAK,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACK,GAAI,EAAE;QACrD;QACA,OAAO;UAAEA,GAAG,EAAEF,UAAU,IAAIK,MAAM,CAACtB,EAAE,CAAC;QAAE,CAAC;MAC3C;MAEA,OAAOc,QAAQ;IACjB;EACF,CAAC,CAAC;EAEFrG,MAAM,CAACmD,MAAM,CAACrB,KAAK,CAACC,UAAU,CAAC5B,SAAS,EAAE0B,kBAAkB,EAAET,WAAW,EAAED,YAAY,EAAEE,YAAY,CAAC;EAEtGrB,MAAM,CAACmD,MAAM,CAACrB,KAAK,CAACC,UAAU,CAAC5B,SAAS,EAAE;IACxC;IACA;IACA2G,mBAAmB,WAAAA,CAAA,EAAG;MACpB;MACA,OAAO,IAAI,CAACpE,WAAW,IAAI,IAAI,CAACA,WAAW,KAAKqE,MAAM,CAACC,MAAM;IAC/D,CAAC;IAEKC,mBAAmB;MAAA,SAAAC,SAAA;QAAA,IAAAvD,IAAA;QAAA,OAAAhD,mBAAA,CAAAoE,KAAA;UAAA,SAAAoC,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;cAAA;gBACnBxB,IAAI,GAAG,IAAI;gBAAA,IACVA,IAAI,CAACd,WAAW,CAACoE,mBAAmB;kBAAAG,SAAA,CAAAjC,IAAA;kBAAA;gBAAA;gBAAA,MACjC,IAAIyB,KAAK,CAAC,yDAAyD,CAAC;cAAA;gBAAAQ,SAAA,CAAAjC,IAAA;gBAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACvEzB,IAAI,CAACd,WAAW,CAACoE,mBAAmB,CAAC,CAAC;cAAA;cAAA;gBAAA,OAAAG,SAAA,CAAAnB,IAAA;YAAA;UAAA;UAAA,OAAAkB,SAAA;QAAA,uBAAAhB,OAAA;MAAA;MAAA,OAAAe,QAAA;IAAA;IAGvCG,2BAA2B;MAAA,SAAAC,SAACC,QAAQ,EAAEC,YAAY;QAAA,IAAA7D,IAAA;QAAA,OAAAhD,mBAAA,CAAAoE,KAAA;UAAA,SAAA0C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;cAAA;gBAClDxB,IAAI,GAAG,IAAI;gBAAA+D,SAAA,CAAAvC,IAAA;gBAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACHzB,IAAI,CAACd,WAAW,CAACwE,2BAA2B;cAAA;gBAAA,IAAAK,SAAA,CAAA9B,IAAA;kBAAA8B,SAAA,CAAAvC,IAAA;kBAAA;gBAAA;gBAAA,MAChD,IAAIyB,KAAK,CACb,iEACF,CAAC;cAAA;gBAAAc,SAAA,CAAAvC,IAAA;gBAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACGzB,IAAI,CAACd,WAAW,CAACwE,2BAA2B,CAACE,QAAQ,EAAEC,YAAY,CAAC;cAAA;cAAA;gBAAA,OAAAE,SAAA,CAAAzB,IAAA;YAAA;UAAA;UAAA,OAAAwB,SAAA;QAAA,uBAAAtB,OAAA;MAAA;MAAA,OAAAmB,QAAA;IAAA;IAG5E;AACF;AACA;AACA;AACA;AACA;IACEK,aAAa,WAAAA,CAAA,EAAG;MACd,IAAIhE,IAAI,GAAG,IAAI;MACf,IAAI,CAACA,IAAI,CAACd,WAAW,CAAC8E,aAAa,EAAE;QACnC,MAAM,IAAIf,KAAK,CAAC,mDAAmD,CAAC;MACtE;MACA,OAAOjD,IAAI,CAACd,WAAW,CAAC8E,aAAa,CAAC,CAAC;IACzC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;IACEC,WAAW,WAAAA,CAAA,EAAG;MACZ,IAAIjE,IAAI,GAAG,IAAI;MACf,IAAI,EAAEA,IAAI,CAACf,OAAO,CAACiF,KAAK,IAAIlE,IAAI,CAACf,OAAO,CAACiF,KAAK,CAACC,EAAE,CAAC,EAAE;QAClD,MAAM,IAAIlB,KAAK,CAAC,iDAAiD,CAAC;MACpE;MACA,OAAOjD,IAAI,CAACf,OAAO,CAACiF,KAAK,CAACC,EAAE;IAC9B;EACF,CAAC,CAAC;EAEF9H,MAAM,CAACmD,MAAM,CAACrB,KAAK,EAAE;IACnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEiG,aAAa,WAAAA,CAAC1H,IAAI,EAAE;MAClB,OAAO,IAAI,CAAC4C,YAAY,CAAC+E,GAAG,CAAC3H,IAAI,CAAC;IACpC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;IACE4C,YAAY,EAAE,IAAIgF,GAAG,CAAC;EACxB,CAAC,CAAC;;EAIF;AACA;AACA;AACA;AACA;AACA;EACAnG,KAAK,CAACoG,QAAQ,GAAGC,OAAO,CAACD,QAAQ;;EAEjC;AACA;AACA;AACA;AACA;EACApG,KAAK,CAACsG,MAAM,GAAG7H,eAAe,CAAC6H,MAAM;;EAErC;AACA;AACA;EACAtG,KAAK,CAACC,UAAU,CAACqG,MAAM,GAAGtG,KAAK,CAACsG,MAAM;;EAEtC;AACA;AACA;EACAtG,KAAK,CAACC,UAAU,CAACmG,QAAQ,GAAGpG,KAAK,CAACoG,QAAQ;;EAE1C;AACA;AACA;EACAnB,MAAM,CAAChF,UAAU,GAAGD,KAAK,CAACC,UAAU;;EAEpC;EACA/B,MAAM,CAACmD,MAAM,CAACrB,KAAK,CAACC,UAAU,CAAC5B,SAAS,EAAEkI,SAAS,CAACC,mBAAmB,CAAC;AAAC,EAAAjG,IAAA,OAAAzC,MAAA,E;;;;;;;;;;;AC1QzE,IAAIoB,aAAa;AAACpB,MAAM,CAACiB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,aAAa,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA9GnB,MAAM,CAACC,MAAM,CAAC;EAACyB,aAAa,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,aAAa;EAAA,CAAC;EAACG,eAAe,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,eAAe;EAAA,CAAC;EAACC,WAAW,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,WAAW;EAAA,CAAC;EAACF,gBAAgB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,gBAAgB;EAAA,CAAC;EAACG,oBAAoB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,oBAAoB;EAAA,CAAC;EAACC,sBAAsB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,sBAAsB;EAAA,CAAC;EAACL,gBAAgB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,gBAAgB;EAAA;AAAC,CAAC,CAAC;AAA9X,IAAMD,aAAa,GAAG;EAC3BiH,KAAK,WAAAA,CAAClI,IAAI,EAAE;IACV,OAAO,YAAW;MAChB,IAAMmI,GAAG,GAAGnI,IAAI,GAAGoI,GAAG,CAACC,YAAY,CAAC,cAAc,GAAGrI,IAAI,CAAC,GAAGwG,MAAM,CAAC8B,QAAQ;MAC5E,OAAO,IAAI7G,KAAK,CAACoG,QAAQ,CAACM,GAAG,CAACI,SAAS,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC;EACDC,MAAM,WAAAA,CAACxI,IAAI,EAAE;IACX,OAAO,YAAW;MAChB,IAAMmI,GAAG,GAAGnI,IAAI,GAAGoI,GAAG,CAACC,YAAY,CAAC,cAAc,GAAGrI,IAAI,CAAC,GAAGwG,MAAM,CAAC8B,QAAQ;MAC5E,OAAOH,GAAG,CAACjD,EAAE,CAAC,CAAC;IACjB,CAAC;EACH;AACF,CAAC;AAEM,SAAS9D,eAAeA,CAACpB,IAAI,EAAE2B,OAAO,EAAE;EAC7C,IAAI,CAAC3B,IAAI,IAAI2B,OAAO,CAAC8G,UAAU,KAAK,IAAI,EAAE,OAAO,IAAI;EACrD,IAAI9G,OAAO,CAAC8G,UAAU,EAAE,OAAO9G,OAAO,CAAC8G,UAAU;EACjD,OAAO/B,MAAM,CAACgC,QAAQ,GAAGhC,MAAM,CAAC+B,UAAU,GAAG/B,MAAM,CAACC,MAAM;AAC5D;AAEO,SAAStF,WAAWA,CAACrB,IAAI,EAAEyI,UAAU,EAAE9G,OAAO,EAAE;EACrD,IAAIA,OAAO,CAACY,OAAO,EAAE,OAAOZ,OAAO,CAACY,OAAO;EAE3C,IAAIvC,IAAI,IACNyI,UAAU,KAAK/B,MAAM,CAACC,MAAM,IAC5B,OAAOgC,cAAc,KAAK,WAAW,IACrCA,cAAc,CAACC,6BAA6B,EAAE;IAC9C,OAAOD,cAAc,CAACC,6BAA6B,CAAC,CAAC;EACvD;EAEA,IAAAC,QAAA,GAAkCC,OAAO,CAAC,+BAA+B,CAAC;IAAlErJ,qBAAqB,GAAAoJ,QAAA,CAArBpJ,qBAAqB;EAC7B,OAAOA,qBAAqB;AAC9B;AAEO,SAAS0B,gBAAgBA,CAACqD,UAAU,EAAExE,IAAI,EAAE2B,OAAO,EAAE;EAC1D,IAAIoH,OAAO,CAACC,WAAW,IACrB,CAACrH,OAAO,CAACsH,mBAAmB,IAC5BzE,UAAU,CAACnC,WAAW,IACtBmC,UAAU,CAACnC,WAAW,CAAC6G,OAAO,EAAE;IAChC1E,UAAU,CAACnC,WAAW,CAAC6G,OAAO,CAAC,IAAI,EAAE;MAAA,OAAM1E,UAAU,CAAC2E,IAAI,CAAC,CAAC;IAAA,GAAE;MAC5DC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;AACF;AAEO,SAAS9H,oBAAoBA,CAACkD,UAAU,EAAExE,IAAI,EAAE2B,OAAO,EAAE;EAC9D,IAAIA,OAAO,CAAC0H,qBAAqB,KAAK,KAAK,EAAE;EAE7C,IAAI;IACF7E,UAAU,CAAC8E,sBAAsB,CAAC;MAChCC,WAAW,EAAE5H,OAAO,CAAC6H,sBAAsB,KAAK;IAClD,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACC,OAAO,2BAAyB1J,IAAI,qCAAkC,EAAE;MAChF,MAAM,IAAIuG,KAAK,4CAAyCvG,IAAI,OAAG,CAAC;IAClE;IACA,MAAMyJ,KAAK;EACb;AACF;AAEO,SAASlI,sBAAsBA,CAACvB,IAAI,EAAE;EAC3C,IAAI,CAACA,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC1B0G,MAAM,CAACiD,MAAM,CACX,yDAAyD,GACzD,yDAAyD,GACzD,gDACF,CAAC;IACD3J,IAAI,GAAG,IAAI;EACb;EAEA,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC7C,MAAM,IAAIuG,KAAK,CACb,iEACF,CAAC;EACH;EAEA,OAAOvG,IAAI;AACb;AAEO,SAASkB,gBAAgBA,CAACS,OAAO,EAAE;EACxC,IAAIA,OAAO,IAAIA,OAAO,CAACiI,OAAO,EAAE;IAC9B;IACAjI,OAAO,GAAG;MAAE8G,UAAU,EAAE9G;IAAQ,CAAC;EACnC;EACA;EACA,IAAIA,OAAO,IAAIA,OAAO,CAACkI,OAAO,IAAI,CAAClI,OAAO,CAAC8G,UAAU,EAAE;IACrD9G,OAAO,CAAC8G,UAAU,GAAG9G,OAAO,CAACkI,OAAO;EACtC;EAEA,OAAAlJ,aAAA;IACE8H,UAAU,EAAE5E,SAAS;IACrB9B,YAAY,EAAE,QAAQ;IACtBI,SAAS,EAAE,IAAI;IACfI,OAAO,EAAEsB,SAAS;IAClBoF,mBAAmB,EAAE;EAAK,GACvBtH,OAAO;AAEd,C;;;;;;;;;;;AClGA,IAAIrB,mBAAmB;AAACf,MAAM,CAACiB,IAAI,CAAC,4BAA4B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,mBAAmB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,aAAa;AAACpB,MAAM,CAACiB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,aAAa,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA/NnB,MAAM,CAACC,MAAM,CAAC;EAACsB,YAAY,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,YAAY;EAAA;AAAC,CAAC,CAAC;AAAtD,IAAMA,YAAY,GAAG;EAC1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEgJ,YAAY,WAAAA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAA7D,SAAA,CAAAjD,MAAA,EAAND,IAAI,OAAAe,KAAA,CAAAgG,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJhH,IAAI,CAAAgH,IAAA,IAAA9D,SAAA,CAAA8D,IAAA;IAAA;IAClB,OAAO,IAAI,CAACxH,WAAW,CAACsH,YAAY,CAClC,IAAI,CAAC/G,gBAAgB,CAACC,IAAI,CAAC,EAC3B,IAAI,CAACE,eAAe,CAACF,IAAI,CAC3B,CAAC;EACH,CAAC;EAEDiH,YAAY,WAAAA,CAACC,GAAG,EAAgB;IAAA,IAAdvI,OAAO,GAAAuE,SAAA,CAAAjD,MAAA,QAAAiD,SAAA,QAAArC,SAAA,GAAAqC,SAAA,MAAG,CAAC,CAAC;IAC5B;IACA,IAAI,CAACgE,GAAG,EAAE;MACR,MAAM,IAAI3D,KAAK,CAAC,6BAA6B,CAAC;IAChD;;IAEA;IACA2D,GAAG,GAAGvK,MAAM,CAACC,MAAM,CACjBD,MAAM,CAACwK,cAAc,CAACD,GAAG,CAAC,EAC1BvK,MAAM,CAACyK,yBAAyB,CAACF,GAAG,CACtC,CAAC;IAED,IAAI,KAAK,IAAIA,GAAG,EAAE;MAChB,IACE,CAACA,GAAG,CAAC7D,GAAG,IACR,EAAE,OAAO6D,GAAG,CAAC7D,GAAG,KAAK,QAAQ,IAAI6D,GAAG,CAAC7D,GAAG,YAAY5E,KAAK,CAACoG,QAAQ,CAAC,EACnE;QACA,MAAM,IAAItB,KAAK,CACb,0EACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,IAAI8D,UAAU,GAAG,IAAI;;MAErB;MACA;MACA;MACA,IAAI,IAAI,CAAC5D,mBAAmB,CAAC,CAAC,EAAE;QAC9B,IAAM6D,SAAS,GAAGlC,GAAG,CAACmC,wBAAwB,CAAC5C,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC2C,SAAS,EAAE;UACdD,UAAU,GAAG,KAAK;QACpB;MACF;MAEA,IAAIA,UAAU,EAAE;QACdH,GAAG,CAAC7D,GAAG,GAAG,IAAI,CAACvE,UAAU,CAAC,CAAC;MAC7B;IACF;;IAEA;IACA;IACA,IAAI0I,qCAAqC,GAAG,SAAAA,CAASC,MAAM,EAAE;MAC3D,IAAI/D,MAAM,CAACgE,UAAU,CAACD,MAAM,CAAC,EAAE,OAAOA,MAAM;MAE5C,IAAIP,GAAG,CAAC7D,GAAG,EAAE;QACX,OAAO6D,GAAG,CAAC7D,GAAG;MAChB;;MAEA;MACA;MACA;MACA6D,GAAG,CAAC7D,GAAG,GAAGoE,MAAM;MAEhB,OAAOA,MAAM;IACf,CAAC;IAED,IAAI,IAAI,CAAChE,mBAAmB,CAAC,CAAC,EAAE;MAC9B,IAAMkE,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,aAAa,EAAE,CAACV,GAAG,CAAC,EAAEvI,OAAO,CAAC;MAC3EgJ,OAAO,CAACE,IAAI,CAACL,qCAAqC,CAAC;MACnDG,OAAO,CAACG,WAAW,GAAGH,OAAO,CAACG,WAAW,CAACD,IAAI,CAACL,qCAAqC,CAAC;MACrFG,OAAO,CAACI,aAAa,GAAGJ,OAAO,CAACI,aAAa,CAACF,IAAI,CAACL,qCAAqC,CAAC;MACzF,OAAOG,OAAO;IAChB;;IAEA;IACA;IACA,OAAO,IAAI,CAACnI,WAAW,CAACwI,WAAW,CAACd,GAAG,CAAC,CACrCW,IAAI,CAACL,qCAAqC,CAAC;EAChD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEQ,WAAW,WAAAA,CAACd,GAAG,EAAEvI,OAAO,EAAE;IACxB,OAAO,IAAI,CAACsI,YAAY,CAACC,GAAG,EAAEvI,OAAO,CAAC;EACxC,CAAC;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsJ,WAAW,WAAAA,CAACjF,QAAQ,EAAEkF,QAAQ,EAAyB;IAErD;IACA;IACA,IAAMvJ,OAAO,GAAAhB,aAAA,KAAS,CAAAuF,SAAA,CAAAjD,MAAA,QAAAY,SAAA,GAAAqC,SAAA,QAAyB,IAAI,CAAG;IACtD,IAAIiF,UAAU;IACd,IAAIxJ,OAAO,IAAIA,OAAO,CAACyJ,MAAM,EAAE;MAC7B;MACA,IAAIzJ,OAAO,CAACwJ,UAAU,EAAE;QACtB,IACE,EACE,OAAOxJ,OAAO,CAACwJ,UAAU,KAAK,QAAQ,IACtCxJ,OAAO,CAACwJ,UAAU,YAAY1J,KAAK,CAACoG,QAAQ,CAC7C,EAED,MAAM,IAAItB,KAAK,CAAC,uCAAuC,CAAC;QAC1D4E,UAAU,GAAGxJ,OAAO,CAACwJ,UAAU;MACjC,CAAC,MAAM,IAAI,CAACnF,QAAQ,IAAI,CAACA,QAAQ,CAACK,GAAG,EAAE;QACrC8E,UAAU,GAAG,IAAI,CAACrJ,UAAU,CAAC,CAAC;QAC9BH,OAAO,CAAC0J,WAAW,GAAG,IAAI;QAC1B1J,OAAO,CAACwJ,UAAU,GAAGA,UAAU;MACjC;IACF;IAEAnF,QAAQ,GAAGvE,KAAK,CAACC,UAAU,CAACqE,gBAAgB,CAACC,QAAQ,EAAE;MACrDG,UAAU,EAAEgF;IACd,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC1E,mBAAmB,CAAC,CAAC,EAAE;MAC9B,IAAMzD,IAAI,GAAG,CAACgD,QAAQ,EAAEkF,QAAQ,EAAEvJ,OAAO,CAAC;MAE1C,OAAO,IAAI,CAACiJ,uBAAuB,CAAC,aAAa,EAAE5H,IAAI,EAAErB,OAAO,CAAC;IACnE;;IAEA;IACA;IACA;IACA;IACA;;IAEA,OAAO,IAAI,CAACa,WAAW,CAACyI,WAAW,CACjCjF,QAAQ,EACRkF,QAAQ,EACRvJ,OACF,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE2J,WAAW,WAAAA,CAACtF,QAAQ,EAAgB;IAAA,IAAdrE,OAAO,GAAAuE,SAAA,CAAAjD,MAAA,QAAAiD,SAAA,QAAArC,SAAA,GAAAqC,SAAA,MAAG,CAAC,CAAC;IAChCF,QAAQ,GAAGvE,KAAK,CAACC,UAAU,CAACqE,gBAAgB,CAACC,QAAQ,CAAC;IAEtD,IAAI,IAAI,CAACS,mBAAmB,CAAC,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACmE,uBAAuB,CAAC,aAAa,EAAE,CAAC5E,QAAQ,CAAC,EAAErE,OAAO,CAAC;IACzE;;IAEA;IACA;IACA,OAAO,IAAI,CAACa,WAAW,CAAC8I,WAAW,CAACtF,QAAQ,CAAC;EAC/C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACQuF,WAAW;IAAA,SAAA9F,QAACO,QAAQ,EAAEkF,QAAQ,EAAEvJ,OAAO;MAAA,OAAArB,mBAAA,CAAAoE,KAAA;QAAA,SAAAgB,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAd,IAAA,GAAAc,QAAA,CAAAb,IAAA;YAAA;cAAA,OAAAa,QAAA,CAAAE,MAAA,WACpC,IAAI,CAACoF,WAAW,CACrBjF,QAAQ,EACRkF,QAAQ,EAAAvK,aAAA,CAAAA,aAAA,KAEHgB,OAAO;gBACV6J,aAAa,EAAE,IAAI;gBACnBJ,MAAM,EAAE;cAAI,EACb,CAAC;YAAA;YAAA;cAAA,OAAAzF,QAAA,CAAAC,IAAA;UAAA;QAAA;QAAA,OAAAF,QAAA;MAAA,uBAAAI,OAAA;IAAA;IAAA,OAAAL,OAAA;EAAA;EAGN;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEgG,cAAc,WAAAA,CAAA,EAAU;IAAA,IAAAC,iBAAA;IACtB,OAAO,CAAAA,iBAAA,OAAI,CAAClJ,WAAW,EAACiJ,cAAc,CAAAE,KAAA,CAAAD,iBAAA,EAAAxF,SAAQ,CAAC;EACjD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0F,sBAAsB,WAAAA,CAAA,EAAU;IAAA,IAAAC,kBAAA;IAC9B,OAAO,CAAAA,kBAAA,OAAI,CAACrJ,WAAW,EAACoJ,sBAAsB,CAAAD,KAAA,CAAAE,kBAAA,EAAA3F,SAAQ,CAAC;EACzD;AACF,CAAC,C;;;;;;;;;;;AC3OD,IAAI5F,mBAAmB;AAACf,MAAM,CAACiB,IAAI,CAAC,4BAA4B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,mBAAmB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhHnB,MAAM,CAACC,MAAM,CAAC;EAACwB,YAAY,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,YAAY;EAAA;AAAC,CAAC,CAAC;AAAtD,IAAMA,YAAY,GAAG;EAC1B;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACQ8K,gBAAgB;IAAA,SAAArG,QAACsG,KAAK,EAAEpK,OAAO;MAAA,IAAA2B,IAAA,EAAA0I,GAAA;MAAA,OAAA1L,mBAAA,CAAAoE,KAAA;QAAA,SAAAgB,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAd,IAAA,GAAAc,QAAA,CAAAb,IAAA;YAAA;cAC/BxB,IAAI,GAAG,IAAI;cAAA,MACX,CAACA,IAAI,CAACd,WAAW,CAACsJ,gBAAgB,IAAI,CAACxI,IAAI,CAACd,WAAW,CAACyJ,gBAAgB;gBAAAtG,QAAA,CAAAb,IAAA;gBAAA;cAAA;cAAA,MACpE,IAAIyB,KAAK,CAAC,sDAAsD,CAAC;YAAA;cAAA,KACrEjD,IAAI,CAACd,WAAW,CAACyJ,gBAAgB;gBAAAtG,QAAA,CAAAb,IAAA;gBAAA;cAAA;cAAAa,QAAA,CAAAb,IAAA;cAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAC7BzB,IAAI,CAACd,WAAW,CAACyJ,gBAAgB,CAACF,KAAK,EAAEpK,OAAO,CAAC;YAAA;cAAAgE,QAAA,CAAAb,IAAA;cAAA;YAAA;cArBrDvF,MAAM,CAACiB,IAAI,CAAC,gBAAgB,EAAC;gBAACwL,GAAG,EAAC,SAAAA,CAAStL,CAAC,EAAC;kBAACsL,GAAG,GAACtL,CAAC;gBAAA;cAAC,CAAC,EAAC,CAAC,CAAC;cAyB1DsL,GAAG,CAACE,KAAK,0FAAwFvK,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE3B,IAAI,sBAAqB2B,OAAO,CAAC3B,IAAI,iBAAmBmM,IAAI,CAACC,SAAS,CAACL,KAAK,CAAI,CAAG,CAAC;cAAApG,QAAA,CAAAb,IAAA;cAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACxLzB,IAAI,CAACd,WAAW,CAACsJ,gBAAgB,CAACC,KAAK,EAAEpK,OAAO,CAAC;YAAA;YAAA;cAAA,OAAAgE,QAAA,CAAAC,IAAA;UAAA;QAAA;QAAA,OAAAF,QAAA;MAAA,uBAAAI,OAAA;IAAA;IAAA,OAAAL,OAAA;EAAA;EAI3D;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACQwG,gBAAgB;IAAA,SAAA5H,SAAC0H,KAAK,EAAEpK,OAAO;MAAA,IAAA2B,IAAA,EAAA+I,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAP,GAAA;MAAA,OAAA1L,mBAAA,CAAAoE,KAAA;QAAA,SAAAC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;YAAA;cAC/BxB,IAAI,GAAG,IAAI;cAAA,IACVA,IAAI,CAACd,WAAW,CAACyJ,gBAAgB;gBAAArH,SAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MAC9B,IAAIyB,KAAK,CAAC,sDAAsD,CAAC;YAAA;cAAA3B,SAAA,CAAAC,IAAA;cAAAD,SAAA,CAAAE,IAAA;cAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAGjEzB,IAAI,CAACd,WAAW,CAACyJ,gBAAgB,CAACF,KAAK,EAAEpK,OAAO,CAAC;YAAA;cAAAiD,SAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,SAAA,CAAAC,IAAA;cAAAD,SAAA,CAAA4H,EAAA,GAAA5H,SAAA;cAAA,MAGrDA,SAAA,CAAA4H,EAAA,CAAE9C,OAAO,CAAC+C,QAAQ,CAChB,8EACF,CAAC,KAAAJ,gBAAA,GACD3F,MAAM,CAACgG,QAAQ,cAAAL,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBM,QAAQ,cAAAL,qBAAA,gBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B9E,KAAK,cAAA+E,sBAAA,eAAhCA,sBAAA,CAAkCK,6BAA6B;gBAAAhI,SAAA,CAAAE,IAAA;gBAAA;cAAA;cAtD/DvF,MAAM,CAACiB,IAAI,CAAC,gBAAgB,EAAC;gBAACwL,GAAG,EAAC,SAAAA,CAAStL,CAAC,EAAC;kBAACsL,GAAG,GAACtL,CAAC;gBAAA;cAAC,CAAC,EAAC,CAAC,CAAC;cA0DxDsL,GAAG,CAACa,IAAI,wBAAuBd,KAAK,aAAUzI,IAAI,CAACb,KAAK,8BAA4B,CAAC;cAACmC,SAAA,CAAAE,IAAA;cAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAChFzB,IAAI,CAACd,WAAW,CAACsK,cAAc,CAACf,KAAK,CAAC;YAAA;cAAAnH,SAAA,CAAAE,IAAA;cAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACtCzB,IAAI,CAACd,WAAW,CAACyJ,gBAAgB,CAACF,KAAK,EAAEpK,OAAO,CAAC;YAAA;cAAAiD,SAAA,CAAAE,IAAA;cAAA;YAAA;cAEvDiI,OAAO,CAACtD,KAAK,CAAA7E,SAAA,CAAA4H,EAAE,CAAC;cAAC,MACX,IAAI9F,MAAM,CAACH,KAAK,gEAA8DjD,IAAI,CAACb,KAAK,UAAOmC,SAAA,CAAA4H,EAAA,CAAE9C,OAAU,CAAC;YAAA;YAAA;cAAA,OAAA9E,SAAA,CAAAgB,IAAA;UAAA;QAAA;QAAA,OAAAjB,SAAA;MAAA,2BAAAmB,OAAA;IAAA;IAAA,OAAAzB,QAAA;EAAA;EAKxH;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE2I,WAAW,WAAAA,CAACjB,KAAK,EAAEpK,OAAO,EAAC;IACzB,OAAO,IAAI,CAACsK,gBAAgB,CAACF,KAAK,EAAEpK,OAAO,CAAC;EAC9C,CAAC;EAEKmL,cAAc;IAAA,SAAAjG,SAACkF,KAAK;MAAA,IAAAzI,IAAA;MAAA,OAAAhD,mBAAA,CAAAoE,KAAA;QAAA,SAAAoC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;YAAA;cACpBxB,IAAI,GAAG,IAAI;cAAA,IACVA,IAAI,CAACd,WAAW,CAACsK,cAAc;gBAAA/F,SAAA,CAAAjC,IAAA;gBAAA;cAAA;cAAA,MAC5B,IAAIyB,KAAK,CAAC,oDAAoD,CAAC;YAAA;cAAAQ,SAAA,CAAAjC,IAAA;cAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACjEzB,IAAI,CAACd,WAAW,CAACsK,cAAc,CAACf,KAAK,CAAC;YAAA;YAAA;cAAA,OAAAhF,SAAA,CAAAnB,IAAA;UAAA;QAAA;QAAA,OAAAkB,SAAA;MAAA,uBAAAhB,OAAA;IAAA;IAAA,OAAAe,QAAA;EAAA;AAEhD,CAAC,C;;;;;;;;;;;AC1FD,IAAIvG,mBAAmB;AAACf,MAAM,CAACiB,IAAI,CAAC,4BAA4B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,mBAAmB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,aAAa;AAACpB,MAAM,CAACiB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,aAAa,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA/NnB,MAAM,CAACC,MAAM,CAAC;EAACgC,kBAAkB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,kBAAkB;EAAA;AAAC,CAAC,CAAC;AAAlE,IAAMA,kBAAkB,GAAG;EAC1BmB,sBAAsB;IAAA,SAAAsK,SAACjN,IAAI;MAAA,IAAAkN,oBAAA,EAAAC,qBAAA;MAAA,IAAA7J,IAAA,EAAA8J,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAA7D,OAAA,EAAA8D,OAAA;MAAA,OAAAlN,mBAAA,CAAAoE,KAAA;QAAA,SAAA+I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7I,IAAA,GAAA6I,SAAA,CAAA5I,IAAA;YAAA;cACzBxB,IAAI,GAAG,IAAI;cAAA,IAGbA,IAAI,CAACjB,WAAW,IAChBiB,IAAI,CAACjB,WAAW,CAACsL,mBAAmB,IACpCrK,IAAI,CAACjB,WAAW,CAACuL,mBAAmB;gBAAAF,SAAA,CAAA5I,IAAA;gBAAA;cAAA;cAAA,OAAA4I,SAAA,CAAA7H,MAAA;YAAA;cAOlCuH,kBAAkB,GAAG;gBACzB;gBACA;gBACAS,aAAa,WAAAA,CAAA,EAAG;kBACdvK,IAAI,CAACd,WAAW,CAACqL,aAAa,CAAC,CAAC;gBAClC,CAAC;gBACDC,iBAAiB,WAAAA,CAAA,EAAG;kBAClB,OAAOxK,IAAI,CAACd,WAAW,CAACsL,iBAAiB,CAAC,CAAC;gBAC7C,CAAC;gBACD;gBACAC,cAAc,WAAAA,CAAA,EAAG;kBACf,OAAOzK,IAAI;gBACb;cACF,CAAC;cACK+J,kBAAkB,GAAA1M,aAAA;gBACtB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACMqN,WAAW;kBAAA,SAAAvI,QAACwI,SAAS,EAAEC,KAAK;oBAAA,OAAA5N,mBAAA,CAAAoE,KAAA;sBAAA,SAAAgB,SAAAC,QAAA;wBAAA,kBAAAA,QAAA,CAAAd,IAAA,GAAAc,QAAA,CAAAb,IAAA;0BAAA;4BAChC;4BACA;4BACA;4BACA;4BACA;4BACA,IAAImJ,SAAS,GAAG,CAAC,IAAIC,KAAK,EAAE5K,IAAI,CAACd,WAAW,CAAC2L,cAAc,CAAC,CAAC;4BAAC,KAE1DD,KAAK;8BAAAvI,QAAA,CAAAb,IAAA;8BAAA;4BAAA;4BAAAa,QAAA,CAAAb,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAAQzB,IAAI,CAACd,WAAW,CAAC4L,MAAM,CAAC,CAAC,CAAC,CAAC;0BAAA;0BAAA;4BAAA,OAAAzI,QAAA,CAAAC,IAAA;wBAAA;sBAAA;sBAAA,OAAAF,QAAA;oBAAA,uBAAAI,OAAA;kBAAA;kBAAA,OAAAL,OAAA;gBAAA;gBAG9C;gBACA;gBACA4I,MAAM,WAAAA,CAACC,GAAG,EAAE;kBACV,IAAIC,OAAO,GAAGzG,OAAO,CAAC0G,OAAO,CAACF,GAAG,CAACpJ,EAAE,CAAC;kBACrC,IAAIgF,GAAG,GAAG5G,IAAI,CAACd,WAAW,CAACiM,KAAK,CAAC9G,GAAG,CAAC4G,OAAO,CAAC;;kBAE7C;kBACA;kBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;kBACA,IAAI7H,MAAM,CAACgC,QAAQ,EAAE;oBACnB,IAAI4F,GAAG,CAACA,GAAG,KAAK,OAAO,IAAIpE,GAAG,EAAE;sBAC9BoE,GAAG,CAACA,GAAG,GAAG,SAAS;oBACrB,CAAC,MAAM,IAAIA,GAAG,CAACA,GAAG,KAAK,SAAS,IAAI,CAACpE,GAAG,EAAE;sBACxC;oBACF,CAAC,MAAM,IAAIoE,GAAG,CAACA,GAAG,KAAK,SAAS,IAAI,CAACpE,GAAG,EAAE;sBACxCoE,GAAG,CAACA,GAAG,GAAG,OAAO;sBACjB,IAAMnL,IAAI,GAAGmL,GAAG,CAACnJ,MAAM;sBACvB,KAAK,IAAIuJ,KAAK,IAAAC,kBAAA,CAAAC,mBAAA,CAAIzL,IAAI,GAAE;wBACtB,IAAM0L,KAAK,GAAG1L,IAAI,CAACuL,KAAK,CAAC;wBACzB,IAAIG,KAAK,KAAK,KAAK,CAAC,EAAE;0BACpB,OAAOP,GAAG,CAACnJ,MAAM,CAACuJ,KAAK,CAAC;wBAC1B;sBACF;oBACF;kBACF;kBACA;kBACA;kBACA;kBACA,IAAIJ,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;oBACzB,IAAIQ,OAAO,GAAGR,GAAG,CAACQ,OAAO;oBACzB,IAAI,CAACA,OAAO,EAAE;sBACZ,IAAI5E,GAAG,EAAE5G,IAAI,CAACd,WAAW,CAAC4L,MAAM,CAACG,OAAO,CAAC;oBAC3C,CAAC,MAAM,IAAI,CAACrE,GAAG,EAAE;sBACf5G,IAAI,CAACd,WAAW,CAACuM,MAAM,CAACD,OAAO,CAAC;oBAClC,CAAC,MAAM;sBACL;sBACAxL,IAAI,CAACd,WAAW,CAAC6L,MAAM,CAACE,OAAO,EAAEO,OAAO,CAAC;oBAC3C;oBACA;kBACF,CAAC,MAAM,IAAIR,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;oBAC9B,IAAIpE,GAAG,EAAE;sBACP,MAAM,IAAI3D,KAAK,CACb,4DACF,CAAC;oBACH;oBACAjD,IAAI,CAACd,WAAW,CAACuM,MAAM,CAAApO,aAAA;sBAAG0F,GAAG,EAAEkI;oBAAO,GAAKD,GAAG,CAACnJ,MAAM,CAAE,CAAC;kBAC1D,CAAC,MAAM,IAAImJ,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;oBAChC,IAAI,CAACpE,GAAG,EACN,MAAM,IAAI3D,KAAK,CACb,yDACF,CAAC;oBACHjD,IAAI,CAACd,WAAW,CAAC4L,MAAM,CAACG,OAAO,CAAC;kBAClC,CAAC,MAAM,IAAID,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;oBAChC,IAAI,CAACpE,GAAG,EAAE,MAAM,IAAI3D,KAAK,CAAC,uCAAuC,CAAC;oBAClE,IAAMyI,IAAI,GAAGrP,MAAM,CAACqP,IAAI,CAACV,GAAG,CAACnJ,MAAM,CAAC;oBACpC,IAAI6J,IAAI,CAAC/L,MAAM,GAAG,CAAC,EAAE;sBACnB,IAAIiI,QAAQ,GAAG,CAAC,CAAC;sBACjB8D,IAAI,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;wBAClB,IAAML,KAAK,GAAGP,GAAG,CAACnJ,MAAM,CAAC+J,GAAG,CAAC;wBAC7B,IAAIC,KAAK,CAACC,MAAM,CAAClF,GAAG,CAACgF,GAAG,CAAC,EAAEL,KAAK,CAAC,EAAE;0BACjC;wBACF;wBACA,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;0BAChC,IAAI,CAAC3D,QAAQ,CAACmE,MAAM,EAAE;4BACpBnE,QAAQ,CAACmE,MAAM,GAAG,CAAC,CAAC;0BACtB;0BACAnE,QAAQ,CAACmE,MAAM,CAACH,GAAG,CAAC,GAAG,CAAC;wBAC1B,CAAC,MAAM;0BACL,IAAI,CAAChE,QAAQ,CAACoE,IAAI,EAAE;4BAClBpE,QAAQ,CAACoE,IAAI,GAAG,CAAC,CAAC;0BACpB;0BACApE,QAAQ,CAACoE,IAAI,CAACJ,GAAG,CAAC,GAAGL,KAAK;wBAC5B;sBACF,CAAC,CAAC;sBACF,IAAIlP,MAAM,CAACqP,IAAI,CAAC9D,QAAQ,CAAC,CAACjI,MAAM,GAAG,CAAC,EAAE;wBACpCK,IAAI,CAACd,WAAW,CAAC6L,MAAM,CAACE,OAAO,EAAErD,QAAQ,CAAC;sBAC5C;oBACF;kBACF,CAAC,MAAM;oBACL,MAAM,IAAI3E,KAAK,CAAC,4CAA4C,CAAC;kBAC/D;gBACF,CAAC;gBAED;gBACAgJ,SAAS,WAAAA,CAAA,EAAG;kBACVjM,IAAI,CAACd,WAAW,CAACgN,qBAAqB,CAAC,CAAC;gBAC1C,CAAC;gBAED;gBACAC,MAAM,WAAAA,CAACvK,EAAE,EAAE;kBACT,OAAO5B,IAAI,CAACoM,OAAO,CAACxK,EAAE,CAAC;gBACzB;cAAC,GAEEkI,kBAAkB;cAEjBE,kBAAkB,GAAA3M,aAAA;gBAChBqN,WAAW;kBAAA,SAAA3J,SAAC4J,SAAS,EAAEC,KAAK;oBAAA,OAAA5N,mBAAA,CAAAoE,KAAA;sBAAA,SAAAC,UAAAC,SAAA;wBAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;0BAAA;4BAChC,IAAImJ,SAAS,GAAG,CAAC,IAAIC,KAAK,EAAE5K,IAAI,CAACd,WAAW,CAAC2L,cAAc,CAAC,CAAC;4BAAC,KAE1DD,KAAK;8BAAAtJ,SAAA,CAAAE,IAAA;8BAAA;4BAAA;4BAAAF,SAAA,CAAAE,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAAQzB,IAAI,CAACd,WAAW,CAAC8I,WAAW,CAAC,CAAC,CAAC,CAAC;0BAAA;0BAAA;4BAAA,OAAA1G,SAAA,CAAAgB,IAAA;wBAAA;sBAAA;sBAAA,OAAAjB,SAAA;oBAAA,uBAAAmB,OAAA;kBAAA;kBAAA,OAAAzB,QAAA;gBAAA;gBAG7CgK,MAAM;kBAAA,SAAAxH,SAACyH,GAAG;oBAAA,IAAAC,OAAA,EAAArE,GAAA,EAAA4E,OAAA,EAAAE,IAAA,EAAA9D,QAAA;oBAAA,OAAA5K,mBAAA,CAAAoE,KAAA;sBAAA,SAAAoC,UAAAC,SAAA;wBAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;0BAAA;4BACVyJ,OAAO,GAAGzG,OAAO,CAAC0G,OAAO,CAACF,GAAG,CAACpJ,EAAE,CAAC;4BACjCgF,GAAG,GAAG5G,IAAI,CAACd,WAAW,CAACiM,KAAK,CAAC9G,GAAG,CAAC4G,OAAO,CAAC,EAE7C;4BACA;4BACA;4BAAA,MACID,GAAG,CAACA,GAAG,KAAK,SAAS;8BAAAvH,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BACnBgK,OAAO,GAAGR,GAAG,CAACQ,OAAO;4BAAA,IACpBA,OAAO;8BAAA/H,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAA,KACNoF,GAAG;8BAAAnD,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAAiC,SAAA,CAAAjC,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAAQzB,IAAI,CAACd,WAAW,CAAC8I,WAAW,CAACiD,OAAO,CAAC;0BAAA;4BAAAxH,SAAA,CAAAjC,IAAA;4BAAA;0BAAA;4BAAA,IAC1CoF,GAAG;8BAAAnD,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAAiC,SAAA,CAAAjC,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACPzB,IAAI,CAACd,WAAW,CAACwI,WAAW,CAAC8D,OAAO,CAAC;0BAAA;4BAAA/H,SAAA,CAAAjC,IAAA;4BAAA;0BAAA;4BAAAiC,SAAA,CAAAjC,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAGrCzB,IAAI,CAACd,WAAW,CAACyI,WAAW,CAACsD,OAAO,EAAEO,OAAO,CAAC;0BAAA;4BAAA,OAAA/H,SAAA,CAAAlB,MAAA;0BAAA;4BAAA,MAG7CyI,GAAG,CAACA,GAAG,KAAK,OAAO;8BAAAvH,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAA,KACxBoF,GAAG;8BAAAnD,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAA,MACC,IAAIyB,KAAK,CACb,4DACF,CAAC;0BAAA;4BAAAQ,SAAA,CAAAjC,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAEGzB,IAAI,CAACd,WAAW,CAACwI,WAAW,CAAArK,aAAA;8BAAG0F,GAAG,EAAEkI;4BAAO,GAAKD,GAAG,CAACnJ,MAAM,CAAE,CAAC;0BAAA;4BAAA4B,SAAA,CAAAjC,IAAA;4BAAA;0BAAA;4BAAA,MAC1DwJ,GAAG,CAACA,GAAG,KAAK,SAAS;8BAAAvH,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAA,IACzBoF,GAAG;8BAAAnD,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAA,MACA,IAAIyB,KAAK,CACb,yDACF,CAAC;0BAAA;4BAAAQ,SAAA,CAAAjC,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACGzB,IAAI,CAACd,WAAW,CAAC8I,WAAW,CAACiD,OAAO,CAAC;0BAAA;4BAAAxH,SAAA,CAAAjC,IAAA;4BAAA;0BAAA;4BAAA,MAClCwJ,GAAG,CAACA,GAAG,KAAK,SAAS;8BAAAvH,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAA,IACzBoF,GAAG;8BAAAnD,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAA,MAAQ,IAAIyB,KAAK,CAAC,uCAAuC,CAAC;0BAAA;4BAC5DyI,IAAI,GAAGrP,MAAM,CAACqP,IAAI,CAACV,GAAG,CAACnJ,MAAM,CAAC;4BAAA,MAChC6J,IAAI,CAAC/L,MAAM,GAAG,CAAC;8BAAA8D,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BACboG,QAAQ,GAAG,CAAC,CAAC;4BACjB8D,IAAI,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;8BAClB,IAAML,KAAK,GAAGP,GAAG,CAACnJ,MAAM,CAAC+J,GAAG,CAAC;8BAC7B,IAAIC,KAAK,CAACC,MAAM,CAAClF,GAAG,CAACgF,GAAG,CAAC,EAAEL,KAAK,CAAC,EAAE;gCACjC;8BACF;8BACA,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;gCAChC,IAAI,CAAC3D,QAAQ,CAACmE,MAAM,EAAE;kCACpBnE,QAAQ,CAACmE,MAAM,GAAG,CAAC,CAAC;gCACtB;gCACAnE,QAAQ,CAACmE,MAAM,CAACH,GAAG,CAAC,GAAG,CAAC;8BAC1B,CAAC,MAAM;gCACL,IAAI,CAAChE,QAAQ,CAACoE,IAAI,EAAE;kCAClBpE,QAAQ,CAACoE,IAAI,GAAG,CAAC,CAAC;gCACpB;gCACApE,QAAQ,CAACoE,IAAI,CAACJ,GAAG,CAAC,GAAGL,KAAK;8BAC5B;4BACF,CAAC,CAAC;4BAAC,MACClP,MAAM,CAACqP,IAAI,CAAC9D,QAAQ,CAAC,CAACjI,MAAM,GAAG,CAAC;8BAAA8D,SAAA,CAAAjC,IAAA;8BAAA;4BAAA;4BAAAiC,SAAA,CAAAjC,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CAC5BzB,IAAI,CAACd,WAAW,CAACyI,WAAW,CAACsD,OAAO,EAAErD,QAAQ,CAAC;0BAAA;4BAAAnE,SAAA,CAAAjC,IAAA;4BAAA;0BAAA;4BAAA,MAInD,IAAIyB,KAAK,CAAC,4CAA4C,CAAC;0BAAA;0BAAA;4BAAA,OAAAQ,SAAA,CAAAnB,IAAA;wBAAA;sBAAA;sBAAA,OAAAkB,SAAA;oBAAA,uBAAAhB,OAAA;kBAAA;kBAAA,OAAAe,QAAA;gBAAA;gBAIjE;gBACM0I,SAAS;kBAAA,SAAAtI,SAAA;oBAAA,OAAA3G,mBAAA,CAAAoE,KAAA;sBAAA,SAAA0C,UAAAC,SAAA;wBAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;0BAAA;4BAAAuC,SAAA,CAAAvC,IAAA;4BAAA,OAAAxE,mBAAA,CAAAyE,KAAA,CACPzB,IAAI,CAACd,WAAW,CAACmN,qBAAqB,CAAC,CAAC;0BAAA;0BAAA;4BAAA,OAAAtI,SAAA,CAAAzB,IAAA;wBAAA;sBAAA;sBAAA,OAAAwB,SAAA;oBAAA,uBAAAtB,OAAA;kBAAA;kBAAA,OAAAmB,QAAA;gBAAA;gBAGhD;gBACMwI,MAAM;kBAAA,SAAAG,SAAC1K,EAAE;oBAAA,OAAA5E,mBAAA,CAAAoE,KAAA;sBAAA,SAAAmL,UAAAC,SAAA;wBAAA,kBAAAA,SAAA,CAAAjL,IAAA,GAAAiL,SAAA,CAAAhL,IAAA;0BAAA;4BAAA,OAAAgL,SAAA,CAAAjK,MAAA,WACNvC,IAAI,CAACwG,YAAY,CAAC5E,EAAE,CAAC;0BAAA;0BAAA;4BAAA,OAAA4K,SAAA,CAAAlK,IAAA;wBAAA;sBAAA;sBAAA,OAAAiK,SAAA;oBAAA,uBAAA/J,OAAA;kBAAA;kBAAA,OAAA8J,QAAA;gBAAA;cAAA,GAE3BxC,kBAAkB,GAIvB;cACA;cACA;cAEA,IAAI1G,MAAM,CAACgC,QAAQ,EAAE;gBACnB6E,mBAAmB,GAAGjK,IAAI,CAACjB,WAAW,CAACsL,mBAAmB,CACxD3N,IAAI,EACJqN,kBACF,CAAC;cACH,CAAC,MAAM;gBACLE,mBAAmB,GAAGjK,IAAI,CAACjB,WAAW,CAACuL,mBAAmB,CACxD5N,IAAI,EACJsN,kBACF,CAAC;cACH;cAEM5D,OAAO,8CAA2C1J,IAAI;cACtDwN,OAAO,GAAG,SAAAA,CAAA,EAAM;gBACpBT,OAAO,CAACgD,IAAI,GAAGhD,OAAO,CAACgD,IAAI,CAACrG,OAAO,CAAC,GAAGqD,OAAO,CAACiD,GAAG,CAACtG,OAAO,CAAC;cAC7D,CAAC;cAAA,IAEI6D,mBAAmB;gBAAAG,SAAA,CAAA5I,IAAA;gBAAA;cAAA;cAAA,OAAA4I,SAAA,CAAA7H,MAAA,WACf2H,OAAO,CAAC,CAAC;YAAA;cAAA,OAAAE,SAAA,CAAA7H,MAAA,YAAAqH,oBAAA,GAGXK,mBAAmB,cAAAL,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqBrC,IAAI,cAAAsC,qBAAA,uBAAzBA,qBAAA,CAAAnL,IAAA,CAAAkL,oBAAA,EAA4B,UAAA+C,EAAE,EAAI;gBACvC,IAAI,CAACA,EAAE,EAAE;kBACPzC,OAAO,CAAC,CAAC;gBACX;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAA9H,IAAA;UAAA;QAAA;QAAA,OAAA6H,SAAA;MAAA,uBAAA3H,OAAA;IAAA;IAAA,OAAAmH,QAAA;EAAA;AAEN,CAAC,C;;;;;;;;;;;ACzQD,IAAItM,aAAa;AAACpB,MAAM,CAACiB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,aAAa,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA9GnB,MAAM,CAACC,MAAM,CAAC;EAACuB,WAAW,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,WAAW;EAAA;AAAC,CAAC,CAAC;AAApD,IAAMA,WAAW,GAAG;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoI,IAAI,WAAAA,CAAA,EAAU;IAAA,SAAAY,IAAA,GAAA7D,SAAA,CAAAjD,MAAA,EAAND,IAAI,OAAAe,KAAA,CAAAgG,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJhH,IAAI,CAAAgH,IAAA,IAAA9D,SAAA,CAAA8D,IAAA;IAAA;IACV;IACA;IACA;IACA,OAAO,IAAI,CAACxH,WAAW,CAAC2G,IAAI,CAC1B,IAAI,CAACpG,gBAAgB,CAACC,IAAI,CAAC,EAC3B,IAAI,CAACE,eAAe,CAACF,IAAI,CAC3B,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0M,OAAO,WAAAA,CAAA,EAAU;IAAA,SAAAQ,KAAA,GAAAhK,SAAA,CAAAjD,MAAA,EAAND,IAAI,OAAAe,KAAA,CAAAmM,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJnN,IAAI,CAAAmN,KAAA,IAAAjK,SAAA,CAAAiK,KAAA;IAAA;IACb,OAAO,IAAI,CAAC3N,WAAW,CAACkN,OAAO,CAC7B,IAAI,CAAC3M,gBAAgB,CAACC,IAAI,CAAC,EAC3B,IAAI,CAACE,eAAe,CAACF,IAAI,CAC3B,CAAC;EACH,CAAC;EAGD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAoN,OAAO,WAAAA,CAAClG,GAAG,EAAEmG,QAAQ,EAAE;IACrB;IACA,IAAI,CAACnG,GAAG,EAAE;MACR,MAAM,IAAI3D,KAAK,CAAC,6BAA6B,CAAC;IAChD;;IAGA;IACA2D,GAAG,GAAGvK,MAAM,CAACC,MAAM,CACjBD,MAAM,CAACwK,cAAc,CAACD,GAAG,CAAC,EAC1BvK,MAAM,CAACyK,yBAAyB,CAACF,GAAG,CACtC,CAAC;IAED,IAAI,KAAK,IAAIA,GAAG,EAAE;MAChB,IACE,CAACA,GAAG,CAAC7D,GAAG,IACR,EAAE,OAAO6D,GAAG,CAAC7D,GAAG,KAAK,QAAQ,IAAI6D,GAAG,CAAC7D,GAAG,YAAY5E,KAAK,CAACoG,QAAQ,CAAC,EACnE;QACA,MAAM,IAAItB,KAAK,CACb,0EACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,IAAI8D,UAAU,GAAG,IAAI;;MAErB;MACA;MACA;MACA,IAAI,IAAI,CAAC5D,mBAAmB,CAAC,CAAC,EAAE;QAC9B,IAAM6D,SAAS,GAAGlC,GAAG,CAACmC,wBAAwB,CAAC5C,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC2C,SAAS,EAAE;UACdD,UAAU,GAAG,KAAK;QACpB;MACF;MAEA,IAAIA,UAAU,EAAE;QACdH,GAAG,CAAC7D,GAAG,GAAG,IAAI,CAACvE,UAAU,CAAC,CAAC;MAC7B;IACF;;IAGA;IACA;IACA,IAAI0I,qCAAqC,GAAG,SAAAA,CAASC,MAAM,EAAE;MAC3D,IAAI/D,MAAM,CAACgE,UAAU,CAACD,MAAM,CAAC,EAAE,OAAOA,MAAM;MAE5C,IAAIP,GAAG,CAAC7D,GAAG,EAAE;QACX,OAAO6D,GAAG,CAAC7D,GAAG;MAChB;;MAEA;MACA;MACA;MACA6D,GAAG,CAAC7D,GAAG,GAAGoE,MAAM;MAEhB,OAAOA,MAAM;IACf,CAAC;IAED,IAAM6F,eAAe,GAAGC,YAAY,CAClCF,QAAQ,EACR7F,qCACF,CAAC;IAED,IAAI,IAAI,CAAC/D,mBAAmB,CAAC,CAAC,EAAE;MAC9B,IAAMgE,MAAM,GAAG,IAAI,CAAC+F,kBAAkB,CAAC,QAAQ,EAAE,CAACtG,GAAG,CAAC,EAAEoG,eAAe,CAAC;MACxE,OAAO9F,qCAAqC,CAACC,MAAM,CAAC;IACtD;;IAEA;IACA;IACA,IAAI;MACF;MACA;MACA;MACA,IAAIA,OAAM;MACV,IAAI,CAAC,CAAC6F,eAAe,EAAE;QACrB,IAAI,CAAC9N,WAAW,CAACuM,MAAM,CAAC7E,GAAG,EAAEoG,eAAe,CAAC;MAC/C,CAAC,MAAM;QACL;QACA;QACA7F,OAAM,GAAG,IAAI,CAACjI,WAAW,CAACuM,MAAM,CAAC7E,GAAG,CAAC;MACvC;MAEA,OAAOM,qCAAqC,CAACC,OAAM,CAAC;IACtD,CAAC,CAAC,OAAOgG,CAAC,EAAE;MACV,IAAIJ,QAAQ,EAAE;QACZA,QAAQ,CAACI,CAAC,CAAC;QACX,OAAO,IAAI;MACb;MACA,MAAMA,CAAC;IACT;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1B,MAAM,WAAAA,CAAC7E,GAAG,EAAEmG,QAAQ,EAAE;IACpB,OAAO,IAAI,CAACD,OAAO,CAAClG,GAAG,EAAEmG,QAAQ,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhC,MAAM,WAAAA,CAACrI,QAAQ,EAAEkF,QAAQ,EAAyB;IAAA,SAAAwF,KAAA,GAAAxK,SAAA,CAAAjD,MAAA,EAApB0N,kBAAkB,OAAA5M,KAAA,CAAA2M,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAlBD,kBAAkB,CAAAC,KAAA,QAAA1K,SAAA,CAAA0K,KAAA;IAAA;IAC9C,IAAMP,QAAQ,GAAGQ,mBAAmB,CAACF,kBAAkB,CAAC;;IAExD;IACA;IACA,IAAMhP,OAAO,GAAAhB,aAAA,KAASgQ,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAG;IACtD,IAAIxF,UAAU;IACd,IAAIxJ,OAAO,IAAIA,OAAO,CAACyJ,MAAM,EAAE;MAC7B;MACA,IAAIzJ,OAAO,CAACwJ,UAAU,EAAE;QACtB,IACE,EACE,OAAOxJ,OAAO,CAACwJ,UAAU,KAAK,QAAQ,IACtCxJ,OAAO,CAACwJ,UAAU,YAAY1J,KAAK,CAACoG,QAAQ,CAC7C,EAED,MAAM,IAAItB,KAAK,CAAC,uCAAuC,CAAC;QAC1D4E,UAAU,GAAGxJ,OAAO,CAACwJ,UAAU;MACjC,CAAC,MAAM,IAAI,CAACnF,QAAQ,IAAI,CAACA,QAAQ,CAACK,GAAG,EAAE;QACrC8E,UAAU,GAAG,IAAI,CAACrJ,UAAU,CAAC,CAAC;QAC9BH,OAAO,CAAC0J,WAAW,GAAG,IAAI;QAC1B1J,OAAO,CAACwJ,UAAU,GAAGA,UAAU;MACjC;IACF;IAEAnF,QAAQ,GAAGvE,KAAK,CAACC,UAAU,CAACqE,gBAAgB,CAACC,QAAQ,EAAE;MACrDG,UAAU,EAAEgF;IACd,CAAC,CAAC;IAEF,IAAMmF,eAAe,GAAGC,YAAY,CAACF,QAAQ,CAAC;IAE9C,IAAI,IAAI,CAAC5J,mBAAmB,CAAC,CAAC,EAAE;MAC9B,IAAMzD,IAAI,GAAG,CAACgD,QAAQ,EAAEkF,QAAQ,EAAEvJ,OAAO,CAAC;MAC1C,OAAO,IAAI,CAAC6O,kBAAkB,CAAC,QAAQ,EAAExN,IAAI,EAAEqN,QAAQ,CAAC;IAC1D;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI;MACF;MACA;MACA;MACA,OAAO,IAAI,CAAC7N,WAAW,CAAC6L,MAAM,CAC5BrI,QAAQ,EACRkF,QAAQ,EACRvJ,OAAO,EACP2O,eACF,CAAC;IACH,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV,IAAIJ,QAAQ,EAAE;QACZA,QAAQ,CAACI,CAAC,CAAC;QACX,OAAO,IAAI;MACb;MACA,MAAMA,CAAC;IACT;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErC,MAAM,WAAAA,CAACpI,QAAQ,EAAEqK,QAAQ,EAAE;IACzBrK,QAAQ,GAAGvE,KAAK,CAACC,UAAU,CAACqE,gBAAgB,CAACC,QAAQ,CAAC;IAEtD,IAAI,IAAI,CAACS,mBAAmB,CAAC,CAAC,EAAE;MAC9B,OAAO,IAAI,CAAC+J,kBAAkB,CAAC,QAAQ,EAAE,CAACxK,QAAQ,CAAC,EAAEqK,QAAQ,CAAC;IAChE;;IAGA;IACA;IACA,OAAO,IAAI,CAAC7N,WAAW,CAAC4L,MAAM,CAACpI,QAAQ,CAAC;EAC1C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoF,MAAM,WAAAA,CAACpF,QAAQ,EAAEkF,QAAQ,EAAEvJ,OAAO,EAAE0O,QAAQ,EAAE;IAC5C,IAAI,CAACA,QAAQ,IAAI,OAAO1O,OAAO,KAAK,UAAU,EAAE;MAC9C0O,QAAQ,GAAG1O,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,OAAO,IAAI,CAAC0M,MAAM,CAChBrI,QAAQ,EACRkF,QAAQ,EAAAvK,aAAA,CAAAA,aAAA,KAEHgB,OAAO;MACV6J,aAAa,EAAE,IAAI;MACnBJ,MAAM,EAAE;IAAI,EACb,CAAC;EACN;AACF,CAAC;AAED;AACA,SAASmF,YAAYA,CAACF,QAAQ,EAAES,aAAa,EAAE;EAC7C,OACET,QAAQ,IACR,UAAS5G,KAAK,EAAEgB,MAAM,EAAE;IACtB,IAAIhB,KAAK,EAAE;MACT4G,QAAQ,CAAC5G,KAAK,CAAC;IACjB,CAAC,MAAM,IAAI,OAAOqH,aAAa,KAAK,UAAU,EAAE;MAC9CT,QAAQ,CAAC5G,KAAK,EAAEqH,aAAa,CAACrG,MAAM,CAAC,CAAC;IACxC,CAAC,MAAM;MACL4F,QAAQ,CAAC5G,KAAK,EAAEgB,MAAM,CAAC;IACzB;EACF,CAAC;AAEL;AAEA,SAASoG,mBAAmBA,CAAC7N,IAAI,EAAE;EACjC;EACA;EACA,IACEA,IAAI,CAACC,MAAM,KACVD,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKY,SAAS,IAClCb,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,YAAYe,QAAQ,CAAC,EAC5C;IACA,OAAOhB,IAAI,CAAC+N,GAAG,CAAC,CAAC;EACnB;AACF,C;;;;;;;;;;;;ACzVA,IAAIpQ,aAAa;AAACpB,MAAM,CAACiB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACC,aAAa,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsQ,wBAAwB;AAACzR,MAAM,CAACiB,IAAI,CAAC,gDAAgD,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACsQ,wBAAwB,GAACtQ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA7PnB,MAAM,CAACC,MAAM,CAAC;EAACqB,mBAAmB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,mBAAmB;EAAA;AAAC,CAAC,CAAC;AAApE,IAAMA,mBAAmB,GAAG,SAAAA,CAAAc,OAAO,EAAI;EAC5C;EACA,IAAAwB,IAAA,GAAgDxB,OAAO,IAAI,CAAC,CAAC;IAArDwD,MAAM,GAAAhC,IAAA,CAANgC,MAAM;IAAExB,UAAU,GAAAR,IAAA,CAAVQ,UAAU;IAAKsN,YAAY,GAAAD,wBAAA,CAAA7N,IAAA,EAAA+N,SAAA;EAC3C;EACA;;EAEA,OAAAvQ,aAAA,CAAAA,aAAA,KACKsQ,YAAY,GACXtN,UAAU,IAAIwB,MAAM,GAAG;IAAExB,UAAU,EAAEwB,MAAM,IAAIxB;EAAW,CAAC,GAAG,CAAC,CAAC;AAExE,CAAC,C", "file": "/packages/mongo.js", "sourcesContent": ["// singleton\nexport const LocalCollectionDriver = new (class LocalCollectionDriver {\n  constructor() {\n    this.noConnCollections = Object.create(null);\n  }\n\n  open(name, conn) {\n    if (! name) {\n      return new LocalCollection;\n    }\n\n    if (! conn) {\n      return ensureCollection(name, this.noConnCollections);\n    }\n\n    if (! conn._mongo_livedata_collections) {\n      conn._mongo_livedata_collections = Object.create(null);\n    }\n\n    // XXX is there a way to keep track of a connection's collections without\n    // dangling it off the connection object?\n    return ensureCollection(name, conn._mongo_livedata_collections);\n  }\n});\n\nfunction ensureCollection(name, collections) {\n  return (name in collections)\n    ? collections[name]\n    : collections[name] = new LocalCollection(name);\n}\n", "import { normalizeProjection } from \"../mongo_utils\";\nimport { AsyncMethods } from './methods_async';\nimport { SyncMethods } from './methods_sync';\nimport { IndexMethods } from './methods_index';\nimport {\n  ID_GENERATORS, normalizeOptions,\n  setupAutopublish,\n  setupConnection,\n  setupDriver,\n  setupMutationMethods,\n  validateCollectionName\n} from './collection_utils';\nimport { ReplicationMethods } from './methods_replication';\n\n/**\n * @summary Namespace for MongoDB-related items\n * @namespace\n */\nMongo = {};\n\n/**\n * @summary Constructor for a Collection\n * @locus Anywhere\n * @instancename collection\n * @class\n * @param {String} name The name of the collection.  If null, creates an unmanaged (unsynchronized) local collection.\n * @param {Object} [options]\n * @param {Object} options.connection The server connection that will manage this collection. Uses the default connection if not specified.  Pass the return value of calling [`DDP.connect`](#DDP-connect) to specify a different server. Pass `null` to specify no connection. Unmanaged (`name` is null) collections cannot specify a connection.\n * @param {String} options.idGeneration The method of generating the `_id` fields of new documents in this collection.  Possible values:\n\n - **`'STRING'`**: random strings\n - **`'MONGO'`**:  random [`Mongo.ObjectID`](#mongo_object_id) values\n\nThe default id generation technique is `'STRING'`.\n * @param {Function} options.transform An optional transformation function. Documents will be passed through this function before being returned from `fetch` or `findOneAsync`, and before being passed to callbacks of `observe`, `map`, `forEach`, `allow`, and `deny`. Transforms are *not* applied for the callbacks of `observeChanges` or to cursors returned from publish functions.\n * @param {Boolean} options.defineMutationMethods Set to `false` to skip setting up the mutation methods that enable insert/update/remove from client code. Default `true`.\n */\n// Main Collection constructor\nMongo.Collection = function Collection(name, options) {\n  name = validateCollectionName(name);\n\n  options = normalizeOptions(options);\n\n  this._makeNewID = ID_GENERATORS[options.idGeneration]?.(name);\n\n  this._transform = LocalCollection.wrapTransform(options.transform);\n  this.resolverType = options.resolverType;\n\n  this._connection = setupConnection(name, options);\n\n  const driver = setupDriver(name, this._connection, options);\n  this._driver = driver;\n\n  this._collection = driver.open(name, this._connection);\n  this._name = name;\n\n  this._settingUpReplicationPromise = this._maybeSetUpReplication(name, options);\n\n  setupMutationMethods(this, name, options);\n\n  setupAutopublish(this, name, options);\n\n  Mongo._collections.set(name, this);\n};\n\nObject.assign(Mongo.Collection.prototype, {\n  _getFindSelector(args) {\n    if (args.length == 0) return {};\n    else return args[0];\n  },\n\n  _getFindOptions(args) {\n    const [, options] = args || [];\n    const newOptions = normalizeProjection(options);\n\n    var self = this;\n    if (args.length < 2) {\n      return { transform: self._transform };\n    } else {\n      check(\n        newOptions,\n        Match.Optional(\n          Match.ObjectIncluding({\n            projection: Match.Optional(Match.OneOf(Object, undefined)),\n            sort: Match.Optional(\n              Match.OneOf(Object, Array, Function, undefined)\n            ),\n            limit: Match.Optional(Match.OneOf(Number, undefined)),\n            skip: Match.Optional(Match.OneOf(Number, undefined)),\n          })\n        )\n      );\n\n      return {\n        transform: self._transform,\n        ...newOptions,\n      };\n    }\n  },\n});\n\nObject.assign(Mongo.Collection, {\n  async _publishCursor(cursor, sub, collection) {\n    var observeHandle = await cursor.observeChanges(\n        {\n          added: function(id, fields) {\n            sub.added(collection, id, fields);\n          },\n          changed: function(id, fields) {\n            sub.changed(collection, id, fields);\n          },\n          removed: function(id) {\n            sub.removed(collection, id);\n          },\n        },\n        // Publications don't mutate the documents\n        // This is tested by the `livedata - publish callbacks clone` test\n        { nonMutatingCallbacks: true }\n    );\n\n    // We don't call sub.ready() here: it gets called in livedata_server, after\n    // possibly calling _publishCursor on multiple returned cursors.\n\n    // register stop callback (expects lambda w/ no args).\n    sub.onStop(async function() {\n      return await observeHandle.stop();\n    });\n\n    // return the observeHandle in case it needs to be stopped early\n    return observeHandle;\n  },\n\n  // protect against dangerous selectors.  falsey and {_id: falsey} are both\n  // likely programmer error, and not what you want, particularly for destructive\n  // operations. If a falsey _id is sent in, a new string _id will be\n  // generated and returned; if a fallbackId is provided, it will be returned\n  // instead.\n  _rewriteSelector(selector, { fallbackId } = {}) {\n    // shorthand -- scalars match _id\n    if (LocalCollection._selectorIsId(selector)) selector = { _id: selector };\n\n    if (Array.isArray(selector)) {\n      // This is consistent with the Mongo console itself; if we don't do this\n      // check passing an empty array ends up selecting all items\n      throw new Error(\"Mongo selector can't be an array.\");\n    }\n\n    if (!selector || ('_id' in selector && !selector._id)) {\n      // can't match anything\n      return { _id: fallbackId || Random.id() };\n    }\n\n    return selector;\n  },\n});\n\nObject.assign(Mongo.Collection.prototype, ReplicationMethods, SyncMethods, AsyncMethods, IndexMethods);\n\nObject.assign(Mongo.Collection.prototype, {\n  // Determine if this collection is simply a minimongo representation of a real\n  // database on another server\n  _isRemoteCollection() {\n    // XXX see #MeteorServerNull\n    return this._connection && this._connection !== Meteor.server;\n  },\n\n  async dropCollectionAsync() {\n    var self = this;\n    if (!self._collection.dropCollectionAsync)\n      throw new Error('Can only call dropCollectionAsync on server collections');\n   await self._collection.dropCollectionAsync();\n  },\n\n  async createCappedCollectionAsync(byteSize, maxDocuments) {\n    var self = this;\n    if (! await self._collection.createCappedCollectionAsync)\n      throw new Error(\n        'Can only call createCappedCollectionAsync on server collections'\n      );\n    await self._collection.createCappedCollectionAsync(byteSize, maxDocuments);\n  },\n\n  /**\n   * @summary Returns the [`Collection`](http://mongodb.github.io/node-mongodb-native/3.0/api/Collection.html) object corresponding to this collection from the [npm `mongodb` driver module](https://www.npmjs.com/package/mongodb) which is wrapped by `Mongo.Collection`.\n   * @locus Server\n   * @memberof Mongo.Collection\n   * @instance\n   */\n  rawCollection() {\n    var self = this;\n    if (!self._collection.rawCollection) {\n      throw new Error('Can only call rawCollection on server collections');\n    }\n    return self._collection.rawCollection();\n  },\n\n  /**\n   * @summary Returns the [`Db`](http://mongodb.github.io/node-mongodb-native/3.0/api/Db.html) object corresponding to this collection's database connection from the [npm `mongodb` driver module](https://www.npmjs.com/package/mongodb) which is wrapped by `Mongo.Collection`.\n   * @locus Server\n   * @memberof Mongo.Collection\n   * @instance\n   */\n  rawDatabase() {\n    var self = this;\n    if (!(self._driver.mongo && self._driver.mongo.db)) {\n      throw new Error('Can only call rawDatabase on server collections');\n    }\n    return self._driver.mongo.db;\n  },\n});\n\nObject.assign(Mongo, {\n  /**\n   * @summary Retrieve a Meteor collection instance by name. Only collections defined with [`new Mongo.Collection(...)`](#collections) are available with this method. For plain MongoDB collections, you'll want to look at [`rawDatabase()`](#Mongo-Collection-rawDatabase).\n   * @locus Anywhere\n   * @memberof Mongo\n   * @static\n   * @param {string} name Name of your collection as it was defined with `new Mongo.Collection()`.\n   * @returns {Mongo.Collection | undefined}\n   */\n  getCollection(name) {\n    return this._collections.get(name);\n  },\n\n  /**\n   * @summary A record of all defined Mongo.Collection instances, indexed by collection name.\n   * @type {Map<string, Mongo.Collection>}\n   * @memberof Mongo\n   * @protected\n   */\n  _collections: new Map(),\n})\n\n\n\n/**\n * @summary Create a Mongo-style `ObjectID`.  If you don't specify a `hexString`, the `ObjectID` will be generated randomly (not using MongoDB's ID construction rules).\n * @locus Anywhere\n * @class\n * @param {String} [hexString] Optional.  The 24-character hexadecimal contents of the ObjectID to create\n */\nMongo.ObjectID = MongoID.ObjectID;\n\n/**\n * @summary To create a cursor, use find. To access the documents in a cursor, use forEach, map, or fetch.\n * @class\n * @instanceName cursor\n */\nMongo.Cursor = LocalCollection.Cursor;\n\n/**\n * @deprecated in 0.9.1\n */\nMongo.Collection.Cursor = Mongo.Cursor;\n\n/**\n * @deprecated in 0.9.1\n */\nMongo.Collection.ObjectID = Mongo.ObjectID;\n\n/**\n * @deprecated in 0.9.1\n */\nMeteor.Collection = Mongo.Collection;\n\n// Allow deny stuff is now in the allow-deny package\nObject.assign(Mongo.Collection.prototype, AllowDeny.CollectionPrototype);\n\n", "export const ID_GENERATORS = {\n  MONGO(name) {\n    return function() {\n      const src = name ? DDP.randomStream('/collection/' + name) : Random.insecure;\n      return new Mongo.ObjectID(src.hexString(24));\n    }\n  },\n  STRING(name) {\n    return function() {\n      const src = name ? DDP.randomStream('/collection/' + name) : Random.insecure;\n      return src.id();\n    }\n  }\n};\n\nexport function setupConnection(name, options) {\n  if (!name || options.connection === null) return null;\n  if (options.connection) return options.connection;\n  return Meteor.isClient ? Meteor.connection : Meteor.server;\n}\n\nexport function setupDriver(name, connection, options) {\n  if (options._driver) return options._driver;\n\n  if (name &&\n    connection === Meteor.server &&\n    typeof MongoInternals !== 'undefined' &&\n    MongoInternals.defaultRemoteCollectionDriver) {\n    return MongoInternals.defaultRemoteCollectionDriver();\n  }\n\n  const { LocalCollectionDriver } = require('../local_collection_driver.js');\n  return LocalCollectionDriver;\n}\n\nexport function setupAutopublish(collection, name, options) {\n  if (Package.autopublish &&\n    !options._preventAutopublish &&\n    collection._connection &&\n    collection._connection.publish) {\n    collection._connection.publish(null, () => collection.find(), {\n      is_auto: true\n    });\n  }\n}\n\nexport function setupMutationMethods(collection, name, options) {\n  if (options.defineMutationMethods === false) return;\n\n  try {\n    collection._defineMutationMethods({\n      useExisting: options._suppressSameNameError === true\n    });\n  } catch (error) {\n    if (error.message === `A method named '/${name}/insertAsync' is already defined`) {\n      throw new Error(`There is already a collection named \"${name}\"`);\n    }\n    throw error;\n  }\n}\n\nexport function validateCollectionName(name) {\n  if (!name && name !== null) {\n    Meteor._debug(\n      'Warning: creating anonymous collection. It will not be ' +\n      'saved or synchronized over the network. (Pass null for ' +\n      'the collection name to turn off this warning.)'\n    );\n    name = null;\n  }\n\n  if (name !== null && typeof name !== 'string') {\n    throw new Error(\n      'First argument to new Mongo.Collection must be a string or null'\n    );\n  }\n\n  return name;\n}\n\nexport function normalizeOptions(options) {\n  if (options && options.methods) {\n    // Backwards compatibility hack with original signature\n    options = { connection: options };\n  }\n  // Backwards compatibility: \"connection\" used to be called \"manager\".\n  if (options && options.manager && !options.connection) {\n    options.connection = options.manager;\n  }\n\n  return {\n    connection: undefined,\n    idGeneration: 'STRING',\n    transform: null,\n    _driver: undefined,\n    _preventAutopublish: false,\n    ...options,\n  };\n}\n", "export const AsyncMethods = {\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOneAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Object}\n   */\n  findOneAsync(...args) {\n    return this._collection.findOneAsync(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n  _insertAsync(doc, options = {}) {\n    // Make sure we were passed a document to insert\n    if (!doc) {\n      throw new Error('insert requires an argument');\n    }\n\n    // Make a shallow clone of the document, preserving its prototype.\n    doc = Object.create(\n      Object.getPrototypeOf(doc),\n      Object.getOwnPropertyDescriptors(doc)\n    );\n\n    if ('_id' in doc) {\n      if (\n        !doc._id ||\n        !(typeof doc._id === 'string' || doc._id instanceof Mongo.ObjectID)\n      ) {\n        throw new Error(\n          'Meteor requires document _id fields to be non-empty strings or ObjectIDs'\n        );\n      }\n    } else {\n      let generateId = true;\n\n      // Don't generate the id if we're the client and the 'outermost' call\n      // This optimization saves us passing both the randomSeed and the id\n      // Passing both is redundant.\n      if (this._isRemoteCollection()) {\n        const enclosing = DDP._CurrentMethodInvocation.get();\n        if (!enclosing) {\n          generateId = false;\n        }\n      }\n\n      if (generateId) {\n        doc._id = this._makeNewID();\n      }\n    }\n\n    // On inserts, always return the id that we generated; on all other\n    // operations, just return the result from the collection.\n    var chooseReturnValueFromCollectionResult = function(result) {\n      if (Meteor._isPromise(result)) return result;\n\n      if (doc._id) {\n        return doc._id;\n      }\n\n      // XXX what is this for??\n      // It's some iteraction between the callback to _callMutatorMethod and\n      // the return value conversion\n      doc._id = result;\n\n      return result;\n    };\n\n    if (this._isRemoteCollection()) {\n      const promise = this._callMutatorMethodAsync('insertAsync', [doc], options);\n      promise.then(chooseReturnValueFromCollectionResult);\n      promise.stubPromise = promise.stubPromise.then(chooseReturnValueFromCollectionResult);\n      promise.serverPromise = promise.serverPromise.then(chooseReturnValueFromCollectionResult);\n      return promise;\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    return this._collection.insertAsync(doc)\n      .then(chooseReturnValueFromCollectionResult);\n  },\n\n  /**\n   * @summary Insert a document in the collection.  Returns a promise that will return the document's unique _id when solved.\n   * @locus Anywhere\n   * @method  insert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   */\n  insertAsync(doc, options) {\n    return this._insertAsync(doc, options);\n  },\n\n\n  /**\n   * @summary Modify one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method update\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   */\n  updateAsync(selector, modifier, ...optionsAndCallback) {\n\n    // We've already popped off the callback, so we are left with an array\n    // of one or zero items\n    const options = { ...(optionsAndCallback[0] || null) };\n    let insertedId;\n    if (options && options.upsert) {\n      // set `insertedId` if absent.  `insertedId` is a Meteor extension.\n      if (options.insertedId) {\n        if (\n          !(\n            typeof options.insertedId === 'string' ||\n            options.insertedId instanceof Mongo.ObjectID\n          )\n        )\n          throw new Error('insertedId must be string or ObjectID');\n        insertedId = options.insertedId;\n      } else if (!selector || !selector._id) {\n        insertedId = this._makeNewID();\n        options.generatedId = true;\n        options.insertedId = insertedId;\n      }\n    }\n\n    selector = Mongo.Collection._rewriteSelector(selector, {\n      fallbackId: insertedId,\n    });\n\n    if (this._isRemoteCollection()) {\n      const args = [selector, modifier, options];\n\n      return this._callMutatorMethodAsync('updateAsync', args, options);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    // If the user provided a callback and the collection implements this\n    // operation asynchronously, then queryRet will be undefined, and the\n    // result will be returned through the callback instead.\n\n    return this._collection.updateAsync(\n      selector,\n      modifier,\n      options\n    );\n  },\n\n  /**\n   * @summary Asynchronously removes documents from the collection.\n   * @locus Anywhere\n   * @method remove\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   */\n  removeAsync(selector, options = {}) {\n    selector = Mongo.Collection._rewriteSelector(selector);\n\n    if (this._isRemoteCollection()) {\n      return this._callMutatorMethodAsync('removeAsync', [selector], options);\n    }\n\n    // it's my collection.  descend into the collection1 object\n    // and propagate any exception.\n    return this._collection.removeAsync(selector);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   */\n  async upsertAsync(selector, modifier, options) {\n    return this.updateAsync(\n      selector,\n      modifier,\n      {\n        ...options,\n        _returnObject: true,\n        upsert: true,\n      });\n  },\n\n  /**\n   * @summary Gets the number of documents matching the filter. For a fast count of the total documents in a collection see `estimatedDocumentCount`.\n   * @locus Anywhere\n   * @method countDocuments\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to count\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://mongodb.github.io/node-mongodb-native/4.11/interfaces/CountDocumentsOptions.html). Please note that not all of them are available on the client.\n   * @returns {Promise<number>}\n   */\n  countDocuments(...args) {\n    return this._collection.countDocuments(...args);\n  },\n\n  /**\n   * @summary Gets an estimate of the count of documents in a collection using collection metadata. For an exact count of the documents in a collection see `countDocuments`.\n   * @locus Anywhere\n   * @method estimatedDocumentCount\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://mongodb.github.io/node-mongodb-native/4.11/interfaces/EstimatedDocumentCountOptions.html). Please note that not all of them are available on the client.\n   * @returns {Promise<number>}\n   */\n  estimatedDocumentCount(...args) {\n    return this._collection.estimatedDocumentCount(...args);\n  },\n}", "export const IndexMethods = {\n  // We'll actually design an index API later. For now, we just pass through to\n  // Mongo's, but make it synchronous.\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method ensureIndexAsync\n   * @deprecated in 3.0\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  async ensureIndexAsync(index, options) {\n    var self = this;\n    if (!self._collection.ensureIndexAsync || !self._collection.createIndexAsync)\n      throw new Error('Can only call createIndexAsync on server collections');\n    if (self._collection.createIndexAsync) {\n      await self._collection.createIndexAsync(index, options);\n    } else {\n      import { Log } from 'meteor/logging';\n\n      Log.debug(`ensureIndexAsync has been deprecated, please use the new 'createIndexAsync' instead${ options?.name ? `, index name: ${ options.name }` : `, index: ${ JSON.stringify(index) }` }`)\n      await self._collection.ensureIndexAsync(index, options);\n    }\n  },\n\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method createIndexAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  async createIndexAsync(index, options) {\n    var self = this;\n    if (!self._collection.createIndexAsync)\n      throw new Error('Can only call createIndexAsync on server collections');\n\n    try {\n      await self._collection.createIndexAsync(index, options);\n    } catch (e) {\n      if (\n        e.message.includes(\n          'An equivalent index already exists with the same name but different options.'\n        ) &&\n        Meteor.settings?.packages?.mongo?.reCreateIndexOnOptionMismatch\n      ) {\n        import { Log } from 'meteor/logging';\n\n        Log.info(`Re-creating index ${ index } for ${ self._name } due to options mismatch.`);\n        await self._collection.dropIndexAsync(index);\n        await self._collection.createIndexAsync(index, options);\n      } else {\n        console.error(e);\n        throw new Meteor.Error(`An error occurred when creating an index for collection \"${ self._name }: ${ e.message }`);\n      }\n    }\n  },\n\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method createIndex\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  createIndex(index, options){\n    return this.createIndexAsync(index, options);\n  },\n\n  async dropIndexAsync(index) {\n    var self = this;\n    if (!self._collection.dropIndexAsync)\n      throw new Error('Can only call dropIndexAsync on server collections');\n    await self._collection.dropIndexAsync(index);\n  },\n}", "export const ReplicationMethods = {\n  async _maybeSetUpReplication(name) {\n    const self = this;\n    if (\n      !(\n        self._connection &&\n        self._connection.registerStoreClient &&\n        self._connection.registerStoreServer\n      )\n    ) {\n      return;\n    }\n\n\n    const wrappedStoreCommon = {\n      // Called around method stub invocations to capture the original versions\n      // of modified documents.\n      saveOriginals() {\n        self._collection.saveOriginals();\n      },\n      retrieveOriginals() {\n        return self._collection.retrieveOriginals();\n      },\n      // To be able to get back to the collection from the store.\n      _getCollection() {\n        return self;\n      },\n    };\n    const wrappedStoreClient = {\n      // Called at the beginning of a batch of updates. batchSize is the number\n      // of update calls to expect.\n      //\n      // XXX This interface is pretty janky. reset probably ought to go back to\n      // being its own function, and callers shouldn't have to calculate\n      // batchSize. The optimization of not calling pause/remove should be\n      // delayed until later: the first call to update() should buffer its\n      // message, and then we can either directly apply it at endUpdate time if\n      // it was the only update, or do pauseObservers/apply/apply at the next\n      // update() if there's another one.\n      async beginUpdate(batchSize, reset) {\n        // pause observers so users don't see flicker when updating several\n        // objects at once (including the post-reconnect reset-and-reapply\n        // stage), and so that a re-sorting of a query can take advantage of the\n        // full _diffQuery moved calculation instead of applying change one at a\n        // time.\n        if (batchSize > 1 || reset) self._collection.pauseObservers();\n\n        if (reset) await self._collection.remove({});\n      },\n\n      // Apply an update.\n      // XXX better specify this interface (not in terms of a wire message)?\n      update(msg) {\n        var mongoId = MongoID.idParse(msg.id);\n        var doc = self._collection._docs.get(mongoId);\n\n        //When the server's mergebox is disabled for a collection, the client must gracefully handle it when:\n        // *We receive an added message for a document that is already there. Instead, it will be changed\n        // *We reeive a change message for a document that is not there. Instead, it will be added\n        // *We receive a removed messsage for a document that is not there. Instead, noting wil happen.\n\n        //Code is derived from client-side code originally in peerlibrary:control-mergebox\n        //https://github.com/peerlibrary/meteor-control-mergebox/blob/master/client.coffee\n\n        //For more information, refer to discussion \"Initial support for publication strategies in livedata server\":\n        //https://github.com/meteor/meteor/pull/11151\n        if (Meteor.isClient) {\n          if (msg.msg === 'added' && doc) {\n            msg.msg = 'changed';\n          } else if (msg.msg === 'removed' && !doc) {\n            return;\n          } else if (msg.msg === 'changed' && !doc) {\n            msg.msg = 'added';\n            const _ref = msg.fields;\n            for (let field in _ref) {\n              const value = _ref[field];\n              if (value === void 0) {\n                delete msg.fields[field];\n              }\n            }\n          }\n        }\n        // Is this a \"replace the whole doc\" message coming from the quiescence\n        // of method writes to an object? (Note that 'undefined' is a valid\n        // value meaning \"remove it\".)\n        if (msg.msg === 'replace') {\n          var replace = msg.replace;\n          if (!replace) {\n            if (doc) self._collection.remove(mongoId);\n          } else if (!doc) {\n            self._collection.insert(replace);\n          } else {\n            // XXX check that replace has no $ ops\n            self._collection.update(mongoId, replace);\n          }\n          return;\n        } else if (msg.msg === 'added') {\n          if (doc) {\n            throw new Error(\n              'Expected not to find a document already present for an add'\n            );\n          }\n          self._collection.insert({ _id: mongoId, ...msg.fields });\n        } else if (msg.msg === 'removed') {\n          if (!doc)\n            throw new Error(\n              'Expected to find a document already present for removed'\n            );\n          self._collection.remove(mongoId);\n        } else if (msg.msg === 'changed') {\n          if (!doc) throw new Error('Expected to find a document to change');\n          const keys = Object.keys(msg.fields);\n          if (keys.length > 0) {\n            var modifier = {};\n            keys.forEach(key => {\n              const value = msg.fields[key];\n              if (EJSON.equals(doc[key], value)) {\n                return;\n              }\n              if (typeof value === 'undefined') {\n                if (!modifier.$unset) {\n                  modifier.$unset = {};\n                }\n                modifier.$unset[key] = 1;\n              } else {\n                if (!modifier.$set) {\n                  modifier.$set = {};\n                }\n                modifier.$set[key] = value;\n              }\n            });\n            if (Object.keys(modifier).length > 0) {\n              self._collection.update(mongoId, modifier);\n            }\n          }\n        } else {\n          throw new Error(\"I don't know how to deal with this message\");\n        }\n      },\n\n      // Called at the end of a batch of updates.livedata_connection.js:1287\n      endUpdate() {\n        self._collection.resumeObserversClient();\n      },\n\n      // Used to preserve current versions of documents across a store reset.\n      getDoc(id) {\n        return self.findOne(id);\n      },\n\n      ...wrappedStoreCommon,\n    };\n    const wrappedStoreServer = {\n      async beginUpdate(batchSize, reset) {\n        if (batchSize > 1 || reset) self._collection.pauseObservers();\n\n        if (reset) await self._collection.removeAsync({});\n      },\n\n      async update(msg) {\n        var mongoId = MongoID.idParse(msg.id);\n        var doc = self._collection._docs.get(mongoId);\n\n        // Is this a \"replace the whole doc\" message coming from the quiescence\n        // of method writes to an object? (Note that 'undefined' is a valid\n        // value meaning \"remove it\".)\n        if (msg.msg === 'replace') {\n          var replace = msg.replace;\n          if (!replace) {\n            if (doc) await self._collection.removeAsync(mongoId);\n          } else if (!doc) {\n            await self._collection.insertAsync(replace);\n          } else {\n            // XXX check that replace has no $ ops\n            await self._collection.updateAsync(mongoId, replace);\n          }\n          return;\n        } else if (msg.msg === 'added') {\n          if (doc) {\n            throw new Error(\n              'Expected not to find a document already present for an add'\n            );\n          }\n          await self._collection.insertAsync({ _id: mongoId, ...msg.fields });\n        } else if (msg.msg === 'removed') {\n          if (!doc)\n            throw new Error(\n              'Expected to find a document already present for removed'\n            );\n          await self._collection.removeAsync(mongoId);\n        } else if (msg.msg === 'changed') {\n          if (!doc) throw new Error('Expected to find a document to change');\n          const keys = Object.keys(msg.fields);\n          if (keys.length > 0) {\n            var modifier = {};\n            keys.forEach(key => {\n              const value = msg.fields[key];\n              if (EJSON.equals(doc[key], value)) {\n                return;\n              }\n              if (typeof value === 'undefined') {\n                if (!modifier.$unset) {\n                  modifier.$unset = {};\n                }\n                modifier.$unset[key] = 1;\n              } else {\n                if (!modifier.$set) {\n                  modifier.$set = {};\n                }\n                modifier.$set[key] = value;\n              }\n            });\n            if (Object.keys(modifier).length > 0) {\n              await self._collection.updateAsync(mongoId, modifier);\n            }\n          }\n        } else {\n          throw new Error(\"I don't know how to deal with this message\");\n        }\n      },\n\n      // Called at the end of a batch of updates.\n      async endUpdate() {\n        await self._collection.resumeObserversServer();\n      },\n\n      // Used to preserve current versions of documents across a store reset.\n      async getDoc(id) {\n        return self.findOneAsync(id);\n      },\n      ...wrappedStoreCommon,\n    };\n\n\n    // OK, we're going to be a slave, replicating some remote\n    // database, except possibly with some temporary divergence while\n    // we have unacknowledged RPC's.\n    let registerStoreResult;\n    if (Meteor.isClient) {\n      registerStoreResult = self._connection.registerStoreClient(\n        name,\n        wrappedStoreClient\n      );\n    } else {\n      registerStoreResult = self._connection.registerStoreServer(\n        name,\n        wrappedStoreServer\n      );\n    }\n\n    const message = `There is already a collection named \"${name}\"`;\n    const logWarn = () => {\n      console.warn ? console.warn(message) : console.log(message);\n    };\n\n    if (!registerStoreResult) {\n      return logWarn();\n    }\n\n    return registerStoreResult?.then?.(ok => {\n      if (!ok) {\n        logWarn();\n      }\n    });\n  },\n}", "export const SyncMethods = {\n  /**\n   * @summary Find the documents in a collection that match the selector.\n   * @locus Anywhere\n   * @method find\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {Number} options.limit Maximum number of results to return\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default `true`; pass `false` to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the  [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {Boolean} options.disableOplog (Server only) Pass true to disable oplog-tailing on this query. This affects the way server processes calls to `observe` on this query. Disabling the oplog can be useful when working with data that updates in large batches.\n   * @param {Number} options.pollingIntervalMs (Server only) When oplog is disabled (through the use of `disableOplog` or when otherwise not available), the frequency (in milliseconds) of how often to poll this query when observing on the server. Defaults to 10000ms (10 seconds).\n   * @param {Number} options.pollingThrottleMs (Server only) When oplog is disabled (through the use of `disableOplog` or when otherwise not available), the minimum time (in milliseconds) to allow between re-polling when observing on the server. Increasing this will save CPU and mongo load at the expense of slower updates to users. Decreasing this is not recommended. Defaults to 50ms.\n   * @param {Number} options.maxTimeMs (Server only) If set, instructs MongoDB to set a time limit for this cursor's operations. If the operation reaches the specified time limit (in milliseconds) without the having been completed, an exception will be thrown. Useful to prevent an (accidental or malicious) unoptimized query from causing a full collection scan that would disrupt other database users, at the expense of needing to handle the resulting error.\n   * @param {String|Object} options.hint (Server only) Overrides MongoDB's default index selection and query optimization process. Specify an index to force its use, either by its name or index specification. You can also specify `{ $natural : 1 }` to force a forwards collection scan, or `{ $natural : -1 }` for a reverse collection scan. Setting this is only recommended for advanced users.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for this particular cursor. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Mongo.Cursor}\n   */\n  find(...args) {\n    // Collection.find() (return all docs) behaves differently\n    // from Collection.find(undefined) (return 0 docs).  so be\n    // careful about the length of arguments.\n    return this._collection.find(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOne\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Object}\n   */\n  findOne(...args) {\n    return this._collection.findOne(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n\n  // 'insert' immediately returns the inserted document's new _id.\n  // The others return values immediately if you are in a stub, an in-memory\n  // unmanaged collection, or a mongo-backed collection and you don't pass a\n  // callback. 'update' and 'remove' return the number of affected\n  // documents. 'upsert' returns an object with keys 'numberAffected' and, if an\n  // insert happened, 'insertedId'.\n  //\n  // Otherwise, the semantics are exactly like other methods: they take\n  // a callback as an optional last argument; if no callback is\n  // provided, they block until the operation is complete, and throw an\n  // exception if it fails; if a callback is provided, then they don't\n  // necessarily block, and they call the callback when they finish with error and\n  // result arguments.  (The insert method provides the document ID as its result;\n  // update and remove provide the number of affected docs as the result; upsert\n  // provides an object with numberAffected and maybe insertedId.)\n  //\n  // On the client, blocking is impossible, so if a callback\n  // isn't provided, they just return immediately and any error\n  // information is lost.\n  //\n  // There's one more tweak. On the client, if you don't provide a\n  // callback, then if there is an error, a message will be logged with\n  // Meteor._debug.\n  //\n  // The intent (though this is actually determined by the underlying\n  // drivers) is that the operations should be done synchronously, not\n  // generating their result until the database has acknowledged\n  // them. In the future maybe we should provide a flag to turn this\n  // off.\n\n  _insert(doc, callback) {\n    // Make sure we were passed a document to insert\n    if (!doc) {\n      throw new Error('insert requires an argument');\n    }\n\n\n    // Make a shallow clone of the document, preserving its prototype.\n    doc = Object.create(\n      Object.getPrototypeOf(doc),\n      Object.getOwnPropertyDescriptors(doc)\n    );\n\n    if ('_id' in doc) {\n      if (\n        !doc._id ||\n        !(typeof doc._id === 'string' || doc._id instanceof Mongo.ObjectID)\n      ) {\n        throw new Error(\n          'Meteor requires document _id fields to be non-empty strings or ObjectIDs'\n        );\n      }\n    } else {\n      let generateId = true;\n\n      // Don't generate the id if we're the client and the 'outermost' call\n      // This optimization saves us passing both the randomSeed and the id\n      // Passing both is redundant.\n      if (this._isRemoteCollection()) {\n        const enclosing = DDP._CurrentMethodInvocation.get();\n        if (!enclosing) {\n          generateId = false;\n        }\n      }\n\n      if (generateId) {\n        doc._id = this._makeNewID();\n      }\n    }\n\n\n    // On inserts, always return the id that we generated; on all other\n    // operations, just return the result from the collection.\n    var chooseReturnValueFromCollectionResult = function(result) {\n      if (Meteor._isPromise(result)) return result;\n\n      if (doc._id) {\n        return doc._id;\n      }\n\n      // XXX what is this for??\n      // It's some iteraction between the callback to _callMutatorMethod and\n      // the return value conversion\n      doc._id = result;\n\n      return result;\n    };\n\n    const wrappedCallback = wrapCallback(\n      callback,\n      chooseReturnValueFromCollectionResult\n    );\n\n    if (this._isRemoteCollection()) {\n      const result = this._callMutatorMethod('insert', [doc], wrappedCallback);\n      return chooseReturnValueFromCollectionResult(result);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    try {\n      // If the user provided a callback and the collection implements this\n      // operation asynchronously, then queryRet will be undefined, and the\n      // result will be returned through the callback instead.\n      let result;\n      if (!!wrappedCallback) {\n        this._collection.insert(doc, wrappedCallback);\n      } else {\n        // If we don't have the callback, we assume the user is using the promise.\n        // We can't just pass this._collection.insert to the promisify because it would lose the context.\n        result = this._collection.insert(doc);\n      }\n\n      return chooseReturnValueFromCollectionResult(result);\n    } catch (e) {\n      if (callback) {\n        callback(e);\n        return null;\n      }\n      throw e;\n    }\n  },\n\n  /**\n   * @summary Insert a document in the collection.  Returns its unique _id.\n   * @locus Anywhere\n   * @method  insert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the _id as the second.\n   */\n  insert(doc, callback) {\n    return this._insert(doc, callback);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method update\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  update(selector, modifier, ...optionsAndCallback) {\n    const callback = popCallbackFromArgs(optionsAndCallback);\n\n    // We've already popped off the callback, so we are left with an array\n    // of one or zero items\n    const options = { ...(optionsAndCallback[0] || null) };\n    let insertedId;\n    if (options && options.upsert) {\n      // set `insertedId` if absent.  `insertedId` is a Meteor extension.\n      if (options.insertedId) {\n        if (\n          !(\n            typeof options.insertedId === 'string' ||\n            options.insertedId instanceof Mongo.ObjectID\n          )\n        )\n          throw new Error('insertedId must be string or ObjectID');\n        insertedId = options.insertedId;\n      } else if (!selector || !selector._id) {\n        insertedId = this._makeNewID();\n        options.generatedId = true;\n        options.insertedId = insertedId;\n      }\n    }\n\n    selector = Mongo.Collection._rewriteSelector(selector, {\n      fallbackId: insertedId,\n    });\n\n    const wrappedCallback = wrapCallback(callback);\n\n    if (this._isRemoteCollection()) {\n      const args = [selector, modifier, options];\n      return this._callMutatorMethod('update', args, callback);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    // If the user provided a callback and the collection implements this\n    // operation asynchronously, then queryRet will be undefined, and the\n    // result will be returned through the callback instead.\n    //console.log({callback, options, selector, modifier, coll: this._collection});\n    try {\n      // If the user provided a callback and the collection implements this\n      // operation asynchronously, then queryRet will be undefined, and the\n      // result will be returned through the callback instead.\n      return this._collection.update(\n        selector,\n        modifier,\n        options,\n        wrappedCallback\n      );\n    } catch (e) {\n      if (callback) {\n        callback(e);\n        return null;\n      }\n      throw e;\n    }\n  },\n\n  /**\n   * @summary Remove documents from the collection\n   * @locus Anywhere\n   * @method remove\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  remove(selector, callback) {\n    selector = Mongo.Collection._rewriteSelector(selector);\n\n    if (this._isRemoteCollection()) {\n      return this._callMutatorMethod('remove', [selector], callback);\n    }\n\n\n    // it's my collection.  descend into the collection1 object\n    // and propagate any exception.\n    return this._collection.remove(selector);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  upsert(selector, modifier, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.update(\n      selector,\n      modifier,\n      {\n        ...options,\n        _returnObject: true,\n        upsert: true,\n      });\n  },\n}\n\n// Convert the callback to not return a result if there is an error\nfunction wrapCallback(callback, convertResult) {\n  return (\n    callback &&\n    function(error, result) {\n      if (error) {\n        callback(error);\n      } else if (typeof convertResult === 'function') {\n        callback(error, convertResult(result));\n      } else {\n        callback(error, result);\n      }\n    }\n  );\n}\n\nfunction popCallbackFromArgs(args) {\n  // Pull off any callback (or perhaps a 'callback' variable that was passed\n  // in undefined, like how 'upsert' does it).\n  if (\n    args.length &&\n    (args[args.length - 1] === undefined ||\n      args[args.length - 1] instanceof Function)\n  ) {\n    return args.pop();\n  }\n}\n", "export const normalizeProjection = options => {\n  // transform fields key in projection\n  const { fields, projection, ...otherOptions } = options || {};\n  // TODO: enable this comment when deprecating the fields option\n  // Log.debug(`fields option has been deprecated, please use the new 'projection' instead`)\n\n  return {\n    ...otherOptions,\n    ...(projection || fields ? { projection: fields || projection } : {}),\n  };\n};\n"]}