{"version": 3, "sources": ["meteor://💻app/packages/random/main_client.js", "meteor://💻app/packages/random/AbstractRandomGenerator.js", "meteor://💻app/packages/random/AleaRandomGenerator.js", "meteor://💻app/packages/random/BrowserRandomGenerator.js", "meteor://💻app/packages/random/createAleaGenerator.js", "meteor://💻app/packages/random/createRandom.js"], "names": ["module", "export", "Random", "BrowserRandomGenerator", "link", "default", "v", "createAleaGeneratorWithGeneratedSeed", "createRandom", "generator", "window", "crypto", "getRandomValues", "RandomGenerator", "Meteor", "UNMISTAKABLE_CHARS", "BASE64_CHARS", "fraction", "Error", "hexString", "digits", "_randomString", "charsCount", "alphabet", "result", "i", "choice", "id", "undefined", "secret", "arrayOrString", "index", "Math", "floor", "length", "substr", "AleaRandomGenerator", "Alea", "seeds", "<PERSON><PERSON>", "n", "mash", "data", "toString", "charCodeAt", "h", "version", "s0", "s1", "s2", "c", "Date", "random", "t", "uint32", "fract53", "args", "constructor", "arguments", "alea", "array", "Uint32Array", "createAleaGenerator", "height", "innerHeight", "document", "documentElement", "clientHeight", "body", "width", "innerWidth", "clientWidth", "agent", "navigator", "userAgent", "createWithSeeds", "_len", "Array", "_key", "insecure"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,MAAM,EAACA,CAAA,KAAIA;AAAM,CAAC,CAAC;AAAC,IAAIC,sBAAsB;AAACH,MAAM,CAACI,IAAI,CAAC,0BAA0B,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACH,sBAAsB,GAACG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,oCAAoC;AAACP,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACC,oCAAoC,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,YAAY;AAACR,MAAM,CAACI,IAAI,CAAC,gBAAgB,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACE,YAAY,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAWjW,IAAIG,SAAS;AACb,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,MAAM,IAChDD,MAAM,CAACC,MAAM,CAACC,eAAe,EAAE;EAC/BH,SAAS,GAAG,IAAIN,sBAAsB,CAAC,CAAC;AAC1C,CAAC,MAAM;EACL;EACA;EACA;EACA;EACA;EACAM,SAAS,GAAGF,oCAAoC,CAAC,CAAC;AACpD;AAGO,MAAML,MAAM,GAAGM,YAAY,CAACC,SAAS,CAAC,C;;;;;;;;;;;ACzB7CT,MAAM,CAACC,MAAM,CAAC;EAACI,OAAO,EAACA,CAAA,KAAIQ;AAAe,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACd,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;EAACU,MAAMA,CAACR,CAAC,EAAC;IAACQ,MAAM,GAACR,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAW5G,MAAMS,kBAAkB,GAAG,yDAAyD;AACpF,MAAMC,YAAY,GAAG,sDAAsD,GACzE,cAAc;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACe,MAAMH,eAAe,CAAC;EAEnC;AACF;AACA;AACA;AACA;EACEI,QAAQA,CAAA,EAAI;IACV,MAAM,IAAIC,KAAK,gCAAgC,CAAC;EAClD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,SAASA,CAAEC,MAAM,EAAE;IACjB,OAAO,IAAI,CAACC,aAAa,CAACD,MAAM,EAAE,kBAAkB,CAAC;EACvD;EAEAC,aAAaA,CAAEC,UAAU,EAAEC,QAAQ,EAAE;IACnC,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,EAAEG,CAAC,EAAE,EAAE;MACnCD,MAAM,IAAI,IAAI,CAACE,MAAM,CAACH,QAAQ,CAAC;IACjC;IACA,OAAOC,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,EAAEA,CAAEL,UAAU,EAAE;IACd;IACA;IACA,IAAIA,UAAU,KAAKM,SAAS,EAAE;MAC5BN,UAAU,GAAG,EAAE;IACjB;IAEA,OAAO,IAAI,CAACD,aAAa,CAACC,UAAU,EAAEP,kBAAkB,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEc,MAAMA,CAAEP,UAAU,EAAE;IAClB;IACA;IACA,IAAIA,UAAU,KAAKM,SAAS,EAAE;MAC5BN,UAAU,GAAG,EAAE;IACjB;IAEA,OAAO,IAAI,CAACD,aAAa,CAACC,UAAU,EAAEN,YAAY,CAAC;EACrD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEU,MAAMA,CAAEI,aAAa,EAAE;IACrB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAChB,QAAQ,CAAC,CAAC,GAAGa,aAAa,CAACI,MAAM,CAAC;IAChE,IAAI,OAAOJ,aAAa,KAAK,QAAQ,EAAE;MACrC,OAAOA,aAAa,CAACK,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;IACvC;IACA,OAAOD,aAAa,CAACC,KAAK,CAAC;EAC7B;AACF,C;;;;;;;;;;;ACpGA/B,MAAM,CAACC,MAAM,CAAC;EAACI,OAAO,EAACA,CAAA,KAAI+B;AAAmB,CAAC,CAAC;AAAC,IAAIvB,eAAe;AAACb,MAAM,CAACI,IAAI,CAAC,2BAA2B,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACO,eAAe,GAACP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAE/I;AACA;AACA;AACA,SAAS+B,IAAIA,CAACC,KAAK,EAAE;EACnB,SAASC,IAAIA,CAAA,EAAG;IACd,IAAIC,CAAC,GAAG,UAAU;IAElB,MAAMC,IAAI,GAAIC,IAAI,IAAK;MACrBA,IAAI,GAAGA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACtB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,IAAI,CAACR,MAAM,EAAET,CAAC,EAAE,EAAE;QACpCe,CAAC,IAAIE,IAAI,CAACE,UAAU,CAACnB,CAAC,CAAC;QACvB,IAAIoB,CAAC,GAAG,mBAAmB,GAAGL,CAAC;QAC/BA,CAAC,GAAGK,CAAC,KAAK,CAAC;QACXA,CAAC,IAAIL,CAAC;QACNK,CAAC,IAAIL,CAAC;QACNA,CAAC,GAAGK,CAAC,KAAK,CAAC;QACXA,CAAC,IAAIL,CAAC;QACNA,CAAC,IAAIK,CAAC,GAAG,WAAW,CAAC,CAAC;MACxB;MACA,OAAO,CAACL,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC;IAC7C,CAAC;IAEDC,IAAI,CAACK,OAAO,GAAG,UAAU;IACzB,OAAOL,IAAI;EACb;EAEA,IAAIM,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIZ,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;IACtBI,KAAK,GAAG,CAAC,CAAC,IAAIa,IAAI,CAAD,CAAC,CAAC;EACrB;EACA,IAAIV,IAAI,GAAGF,IAAI,CAAC,CAAC;EACjBQ,EAAE,GAAGN,IAAI,CAAC,GAAG,CAAC;EACdO,EAAE,GAAGP,IAAI,CAAC,GAAG,CAAC;EACdQ,EAAE,GAAGR,IAAI,CAAC,GAAG,CAAC;EAEd,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,KAAK,CAACJ,MAAM,EAAET,CAAC,EAAE,EAAE;IACrCsB,EAAE,IAAIN,IAAI,CAACH,KAAK,CAACb,CAAC,CAAC,CAAC;IACpB,IAAIsB,EAAE,GAAG,CAAC,EAAE;MACVA,EAAE,IAAI,CAAC;IACT;IACAC,EAAE,IAAIP,IAAI,CAACH,KAAK,CAACb,CAAC,CAAC,CAAC;IACpB,IAAIuB,EAAE,GAAG,CAAC,EAAE;MACVA,EAAE,IAAI,CAAC;IACT;IACAC,EAAE,IAAIR,IAAI,CAACH,KAAK,CAACb,CAAC,CAAC,CAAC;IACpB,IAAIwB,EAAE,GAAG,CAAC,EAAE;MACVA,EAAE,IAAI,CAAC;IACT;EACF;EACAR,IAAI,GAAG,IAAI;EAEX,MAAMW,MAAM,GAAGA,CAAA,KAAM;IACnB,MAAMC,CAAC,GAAI,OAAO,GAAGN,EAAE,GAAKG,CAAC,GAAG,sBAAuB,CAAC,CAAC;IACzDH,EAAE,GAAGC,EAAE;IACPA,EAAE,GAAGC,EAAE;IACP,OAAOA,EAAE,GAAGI,CAAC,IAAIH,CAAC,GAAGG,CAAC,GAAG,CAAC,CAAC;EAC7B,CAAC;EAEDD,MAAM,CAACE,MAAM,GAAG,MAAMF,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;EAC9CA,MAAM,CAACG,OAAO,GAAG,MAAMH,MAAM,CAAC,CAAC,GACxB,CAACA,MAAM,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,sBAAuB,CAAC,CAAC;;EAE5DA,MAAM,CAACN,OAAO,GAAG,UAAU;EAC3BM,MAAM,CAACI,IAAI,GAAGlB,KAAK;EACnB,OAAOc,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACe,MAAMhB,mBAAmB,SAASvB,eAAe,CAAC;EAC/D4C,WAAWA,CAAA,EAAuB;IAAA,IAArB;MAAEnB,KAAK,GAAG;IAAG,CAAC,GAAAoB,SAAA,CAAAxB,MAAA,QAAAwB,SAAA,QAAA9B,SAAA,GAAA8B,SAAA,MAAG,CAAC,CAAC;IAC9B,KAAK,CAAC,CAAC;IACP,IAAI,CAACpB,KAAK,EAAE;MACV,MAAM,IAAIpB,KAAK,CAAC,sCAAsC,CAAC;IACzD;IACA,IAAI,CAACyC,IAAI,GAAGtB,IAAI,CAACC,KAAK,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;EACErB,QAAQA,CAAA,EAAI;IACV,OAAO,IAAI,CAAC0C,IAAI,CAAC,CAAC;EACpB;AACF,C;;;;;;;;;;;AC7FA3D,MAAM,CAACC,MAAM,CAAC;EAACI,OAAO,EAACA,CAAA,KAAIF;AAAsB,CAAC,CAAC;AAAC,IAAIU,eAAe;AAACb,MAAM,CAACI,IAAI,CAAC,2BAA2B,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACO,eAAe,GAACP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAGnI,MAAMH,sBAAsB,SAASU,eAAe,CAAC;EAClE;AACF;AACA;AACA;AACA;EACEI,QAAQA,CAAA,EAAI;IACV,MAAM2C,KAAK,GAAG,IAAIC,WAAW,CAAC,CAAC,CAAC;IAChCnD,MAAM,CAACC,MAAM,CAACC,eAAe,CAACgD,KAAK,CAAC;IACpC,OAAOA,KAAK,CAAC,CAAC,CAAC,GAAG,sBAAsB,CAAC,CAAC;EAC5C;AACF,C;;;;;;;;;;;ACdA5D,MAAM,CAACC,MAAM,CAAC;EAACI,OAAO,EAACA,CAAA,KAAIyD;AAAmB,CAAC,CAAC;AAAC,IAAI1B,mBAAmB;AAACpC,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAAC8B,mBAAmB,GAAC9B,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAEnJ;AACA;;AAEA;AACA,MAAMyD,MAAM,GAAI,OAAOrD,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACsD,WAAW,IAC5D,OAAOC,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACC,eAAe,IACxBD,QAAQ,CAACC,eAAe,CAACC,YAAa,IACzC,OAAOF,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACG,IAAI,IACbH,QAAQ,CAACG,IAAI,CAACD,YAAa,IAC/B,CAAC;AAEP,MAAME,KAAK,GAAI,OAAO3D,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC4D,UAAU,IAC1D,OAAOL,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACC,eAAe,IACxBD,QAAQ,CAACC,eAAe,CAACK,WAAY,IACxC,OAAON,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACG,IAAI,IACbH,QAAQ,CAACG,IAAI,CAACG,WAAY,IAC9B,CAAC;AAEP,MAAMC,KAAK,GAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAK,EAAE;AAE9D,SAASZ,mBAAmBA,CAAA,EAAG;EAC5C,OAAO,IAAI1B,mBAAmB,CAAC;IAC7BE,KAAK,EAAE,CAAC,IAAIa,IAAI,CAAD,CAAC,EAAEY,MAAM,EAAEM,KAAK,EAAEG,KAAK,EAAExC,IAAI,CAACoB,MAAM,CAAC,CAAC;EACvD,CAAC,CAAC;AACJ,C;;;;;;;;;;;AC9BApD,MAAM,CAACC,MAAM,CAAC;EAACI,OAAO,EAACA,CAAA,KAAIG;AAAY,CAAC,CAAC;AAAC,IAAI4B,mBAAmB;AAACpC,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAAC8B,mBAAmB,GAAC9B,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,oCAAoC;AAACP,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACC,oCAAoC,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAGlQ,SAASE,YAAYA,CAACC,SAAS,EAAE;EAC9C;EACA;EACAA,SAAS,CAACkE,eAAe,GAAG,YAAc;IAAA,SAAAC,IAAA,GAAAlB,SAAA,CAAAxB,MAAA,EAAVI,KAAK,OAAAuC,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAALxC,KAAK,CAAAwC,IAAA,IAAApB,SAAA,CAAAoB,IAAA;IAAA;IACnC,IAAIxC,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIhB,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IACA,OAAO,IAAIkB,mBAAmB,CAAC;MAAEE;IAAM,CAAC,CAAC;EAC3C,CAAC;;EAED;EACA;EACA7B,SAAS,CAACsE,QAAQ,GAAGxE,oCAAoC,CAAC,CAAC;EAE3D,OAAOE,SAAS;AAClB,C", "file": "/packages/random.js", "sourcesContent": ["// We use cryptographically strong PRNGs (window.crypto.getRandomValues())\n// when available. If these PRNGs fail, we fall back to the Alea PRNG, which is \n// not cryptographically strong, and we seed it with various sources \n// such as the date, Math.random, and window size on the client.\n// When using window.crypto.getRandomValues() or alea, the primitive is fraction \n// and we use that to construct hex string.\n\nimport BrowserRandomGenerator from './BrowserRandomGenerator';\nimport createAleaGeneratorWithGeneratedSeed from './createAleaGenerator';\nimport createRandom from './createRandom';\n\nlet generator;\nif (typeof window !== 'undefined' && window.crypto &&\n  window.crypto.getRandomValues) {\n  generator = new BrowserRandomGenerator();\n} else {\n  // On IE 10 and below, there's no browser crypto API\n  // available. Fall back to Alea\n  //\n  // XXX looks like at the moment, we use Alea in IE 11 as well,\n  // which has `window.msCrypto` instead of `window.crypto`.\n  generator = createAleaGeneratorWithGeneratedSeed();\n}\n\n\nexport const Random = createRandom(generator);\n", "// We use cryptographically strong PRNGs (crypto.getRandomBytes() on the server,\n// window.crypto.getRandomValues() in the browser) when available. If these\n// PRNGs fail, we fall back to the Alea PRNG, which is not cryptographically\n// strong, and we seed it with various sources such as the date, Math.random,\n// and window size on the client.  When using crypto.getRandomValues(), our\n// primitive is hexString(), from which we construct fraction(). When using\n// window.crypto.getRandomValues() or alea, the primitive is fraction and we use\n// that to construct hex string.\n\nimport { Meteor } from 'meteor/meteor';\n\nconst UNMISTAKABLE_CHARS = '23456789ABCDEFGHJKLMNPQRSTWXYZabcdefghijkmnopqrstuvwxyz';\nconst BASE64_CHARS = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ' +\n  '0123456789-_';\n\n// `type` is one of `RandomGenerator.Type` as defined below.\n//\n// options:\n// - seeds: (required, only for RandomGenerator.Type.ALEA) an array\n//   whose items will be `toString`ed and used as the seed to the Alea\n//   algorithm\nexport default class RandomGenerator {\n\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    throw new Error(`Unknown random generator type`);\n  }\n\n  /**\n   * @name Random.hexString\n   * @summary Return a random string of `n` hexadecimal digits.\n   * @locus Anywhere\n   * @param {Number} n Length of the string\n   */\n  hexString (digits) {\n    return this._randomString(digits, '0123456789abcdef');\n  }\n\n  _randomString (charsCount, alphabet) {\n    let result = '';\n    for (let i = 0; i < charsCount; i++) {\t\n      result += this.choice(alphabet);\n    }\n    return result;\n  }\n\n  /**\n   * @name Random.id\n   * @summary Return a unique identifier, such as `\"Jjwjg6gouWLXhMGKW\"`, that is\n   * likely to be unique in the whole world.\n   * @locus Anywhere\n   * @param {Number} [n] Optional length of the identifier in characters\n   *   (defaults to 17)\n   */\n  id (charsCount) {\n    // 17 characters is around 96 bits of entropy, which is the amount of\n    // state in the Alea PRNG.\n    if (charsCount === undefined) {\n      charsCount = 17;\n    }\n\n    return this._randomString(charsCount, UNMISTAKABLE_CHARS);\n  }\n\n  /**\n   * @name Random.secret\n   * @summary Return a random string of printable characters with 6 bits of\n   * entropy per character. Use `Random.secret` for security-critical secrets\n   * that are intended for machine, rather than human, consumption.\n   * @locus Anywhere\n   * @param {Number} [n] Optional length of the secret string (defaults to 43\n   *   characters, or 256 bits of entropy)\n   */\n  secret (charsCount) {\n    // Default to 256 bits of entropy, or 43 characters at 6 bits per\n    // character.\n    if (charsCount === undefined) {\n      charsCount = 43;\n    }\n\n    return this._randomString(charsCount, BASE64_CHARS);\n  }\n\n  /**\n   * @name Random.choice\n   * @summary Return a random element of the given array or string.\n   * @locus Anywhere\n   * @param {Array|String} arrayOrString Array or string to choose from\n   */\n  choice (arrayOrString) {\n    const index = Math.floor(this.fraction() * arrayOrString.length);\n    if (typeof arrayOrString === 'string') {\n      return arrayOrString.substr(index, 1);\n    }\n    return arrayOrString[index];\n  }\n}\n", "import RandomGenerator from './AbstractRandomGenerator';\n\n// Alea PRNG, which is not cryptographically strong\n// see http://baagoe.org/en/wiki/Better_random_numbers_for_javascript\n// for a full discussion and Alea implementation.\nfunction Alea(seeds) {\n  function Mash() {\n    let n = 0xefc8249d;\n\n    const mash = (data) => {\n      data = data.toString();\n      for (let i = 0; i < data.length; i++) {\n        n += data.charCodeAt(i);\n        let h = 0.02519603282416938 * n;\n        n = h >>> 0;\n        h -= n;\n        h *= n;\n        n = h >>> 0;\n        h -= n;\n        n += h * 0x100000000; // 2^32\n      }\n      return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n    };\n\n    mash.version = 'Mash 0.9';\n    return mash;\n  }\n\n  let s0 = 0;\n  let s1 = 0;\n  let s2 = 0;\n  let c = 1;\n  if (seeds.length === 0) {\n    seeds = [+new Date];\n  }\n  let mash = Mash();\n  s0 = mash(' ');\n  s1 = mash(' ');\n  s2 = mash(' ');\n\n  for (let i = 0; i < seeds.length; i++) {\n    s0 -= mash(seeds[i]);\n    if (s0 < 0) {\n      s0 += 1;\n    }\n    s1 -= mash(seeds[i]);\n    if (s1 < 0) {\n      s1 += 1;\n    }\n    s2 -= mash(seeds[i]);\n    if (s2 < 0) {\n      s2 += 1;\n    }\n  }\n  mash = null;\n\n  const random = () => {\n    const t = (2091639 * s0) + (c * 2.3283064365386963e-10); // 2^-32\n    s0 = s1;\n    s1 = s2;\n    return s2 = t - (c = t | 0);\n  };\n\n  random.uint32 = () => random() * 0x100000000; // 2^32\n  random.fract53 = () => random() +\n        ((random() * 0x200000 | 0) * 1.1102230246251565e-16); // 2^-53\n\n  random.version = 'Alea 0.9';\n  random.args = seeds;\n  return random;\n}\n\n// options:\n// - seeds: an array\n//   whose items will be `toString`ed and used as the seed to the Alea\n//   algorithm\nexport default class AleaRandomGenerator extends RandomGenerator {\n  constructor ({ seeds = [] } = {}) {\n    super();\n    if (!seeds) {\n      throw new Error('No seeds were provided for Alea PRNG');\n    }\n    this.alea = Alea(seeds);\n  }\n\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    return this.alea();\n  }\n}\n", "import RandomGenerator from './AbstractRandomGenerator';\n\n// cryptographically strong PRNGs available in modern browsers\nexport default class BrowserRandomGenerator extends RandomGenerator {\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    const array = new Uint32Array(1);\n    window.crypto.getRandomValues(array);\n    return array[0] * 2.3283064365386963e-10; // 2^-32\n  }\n}\n", "import AleaRandomGenerator from './AleaRandomGenerator';\n\n// instantiate RNG.  Heuristically collect entropy from various sources when a\n// cryptographic PRNG isn't available.\n\n// client sources\nconst height = (typeof window !== 'undefined' && window.innerHeight) ||\n      (typeof document !== 'undefined'\n       && document.documentElement\n       && document.documentElement.clientHeight) ||\n      (typeof document !== 'undefined'\n       && document.body\n       && document.body.clientHeight) ||\n      1;\n\nconst width = (typeof window !== 'undefined' && window.innerWidth) ||\n      (typeof document !== 'undefined'\n       && document.documentElement\n       && document.documentElement.clientWidth) ||\n      (typeof document !== 'undefined'\n       && document.body\n       && document.body.clientWidth) ||\n      1;\n\nconst agent = (typeof navigator !== 'undefined' && navigator.userAgent) || '';\n\nexport default function createAleaGenerator() {\n  return new AleaRandomGenerator({\n    seeds: [new Date, height, width, agent, Math.random()],\n  });\n}\n", "import AleaRandomGenerator from './AleaRandomGenerator'\nimport createAleaGeneratorWithGeneratedSeed from './createAleaGenerator';\n\nexport default function createRandom(generator) {\n  // Create a non-cryptographically secure PRNG with a given seed (using\n  // the Alea algorithm)\n  generator.createWithSeeds = (...seeds) => {\n    if (seeds.length === 0) {\n      throw new Error('No seeds were provided');\n    }\n    return new AleaRandomGenerator({ seeds });\n  };\n\n  // Used like `Random`, but much faster and not cryptographically\n  // secure\n  generator.insecure = createAleaGeneratorWithGeneratedSeed();\n\n  return generator;\n}\n"]}