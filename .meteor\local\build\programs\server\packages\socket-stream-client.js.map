{"version": 3, "sources": ["meteor://💻app/packages/socket-stream-client/server.js", "meteor://💻app/packages/socket-stream-client/node.js", "meteor://💻app/packages/socket-stream-client/common.js", "meteor://💻app/packages/socket-stream-client/urls.js"], "names": ["setMinimumBrowserVersions", "module1", "link", "v", "__reifyWaitForDeps__", "chrome", "edge", "firefox", "ie", "mobileSafari", "phantom<PERSON>s", "safari", "electron", "module", "id", "process", "env", "DISABLE_SOCKJS", "__meteor_runtime_config__", "__reify_async_result__", "_reifyError", "self", "async", "export", "ClientStream", "Meteor", "StreamClientCommon", "toWebsocketUrl", "constructor", "endpoint", "options", "client", "headers", "npmFayeOptions", "_initCommon", "_launchConnection", "send", "data", "currentStatus", "connected", "_changeUrl", "url", "_onConnect", "Error", "_forcedToDisconnect", "close", "_clearConnectionTimer", "status", "retryCount", "statusChanged", "forEachCallback", "callback", "_cleanup", "maybeError", "connectionTimer", "clearTimeout", "_getProxyUrl", "targetUrl", "proxy", "HTTP_PROXY", "http_proxy", "noproxy", "NO_PROXY", "no_proxy", "match", "HTTPS_PROXY", "https_proxy", "indexOf", "item", "split", "trim", "replace", "_this", "FayeWebSocket", "Npm", "require", "deflate", "fayeOptions", "extensions", "Object", "assign", "proxyUrl", "origin", "subprotocols", "Client", "setTimeout", "_lostConnection", "ConnectionError", "CONNECT_TIMEOUT", "on", "bindEnvironment", "clientOnIfCurrent", "event", "description", "arguments", "error", "_dontPrintErrors", "_debug", "message", "_objectSpread", "default", "Retry", "forcedReconnectError", "retry", "name", "eventCallbacks", "push", "cb", "length", "for<PERSON>ach", "create", "connectTimeoutMs", "Package", "tracker", "statusListeners", "Tracker", "Dependency", "changed", "_retry", "reconnect", "_sockjsOptions", "_force", "clear", "_retryNow", "disconnect", "_permanent", "_error", "reason", "_retryLater", "_online", "timeout", "retryLater", "bind", "retryTime", "Date", "getTime", "depend", "toSockjsUrl", "translateUrl", "newSchemeBase", "subPath", "startsWith", "absoluteUrl", "substr", "ddpUrlMatch", "httpUrlMatch", "newScheme", "urlAfterDDP", "slashPos", "host", "rest", "Math", "floor", "random", "urlAfterHttp", "_relativeToSiteRootUrl", "endsWith"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,yBAAyB;IAACC,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAC;MAACF,yBAAyBA,CAACG,CAAC,EAAC;QAACH,yBAAyB,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAI/LJ,yBAAyB,CAAC;MACxBK,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE,EAAE;MACXC,EAAE,EAAE,EAAE;MACNC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACpBC,SAAS,EAAE,CAAC;MACZC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;IAClB,CAAC,EAAEC,MAAM,CAACC,EAAE,CAAC;IAEb,IAAIC,OAAO,CAACC,GAAG,CAACC,cAAc,EAAE;MAC9BC,yBAAyB,CAACD,cAAc,GAAGF,OAAO,CAACC,GAAG,CAACC,cAAc;IACvE;IAACE,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICjBDrB,OAAO,CAACsB,MAAM,CAAC;MAACC,YAAY,EAACA,CAAA,KAAIA;IAAY,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACxB,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;MAACuB,MAAMA,CAACtB,CAAC,EAAC;QAACsB,MAAM,GAACtB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIuB,kBAAkB;IAACzB,OAAO,CAACC,IAAI,CAAC,aAAa,EAAC;MAACwB,kBAAkBA,CAACvB,CAAC,EAAC;QAACuB,kBAAkB,GAACvB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIwB,cAAc;IAAC1B,OAAO,CAACC,IAAI,CAAC,WAAW,EAAC;MAACyB,cAAcA,CAACxB,CAAC,EAAC;QAACwB,cAAc,GAACxB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAe9V,MAAMoB,YAAY,SAASE,kBAAkB,CAAC;MACnDE,WAAWA,CAACC,QAAQ,EAAEC,OAAO,EAAE;QAC7B,KAAK,CAACA,OAAO,CAAC;QAEd,IAAI,CAACC,MAAM,GAAG,IAAI,CAAC,CAAC;QACpB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;QAExB,IAAI,CAACG,OAAO,GAAG,IAAI,CAACF,OAAO,CAACE,OAAO,IAAI,CAAC,CAAC;QACzC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,OAAO,CAACG,cAAc,IAAI,CAAC,CAAC;QAEvD,IAAI,CAACC,WAAW,CAAC,IAAI,CAACJ,OAAO,CAAC;;QAE9B;QACA,IAAI,CAACK,iBAAiB,CAAC,CAAC;MAC1B;;MAEA;MACA;MACA;MACAC,IAAIA,CAACC,IAAI,EAAE;QACT,IAAI,IAAI,CAACC,aAAa,CAACC,SAAS,EAAE;UAChC,IAAI,CAACR,MAAM,CAACK,IAAI,CAACC,IAAI,CAAC;QACxB;MACF;;MAEA;MACAG,UAAUA,CAACC,GAAG,EAAE;QACd,IAAI,CAACZ,QAAQ,GAAGY,GAAG;MACrB;MAEAC,UAAUA,CAACX,MAAM,EAAE;QACjB,IAAIA,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;UAC1B;UACA;UACA;UACA;UACA,MAAM,IAAIY,KAAK,CAAC,gCAAgC,GAAG,CAAC,CAAC,IAAI,CAACZ,MAAM,CAAC;QACnE;QAEA,IAAI,IAAI,CAACa,mBAAmB,EAAE;UAC5B;UACA;UACA,IAAI,CAACb,MAAM,CAACc,KAAK,CAAC,CAAC;UACnB,IAAI,CAACd,MAAM,GAAG,IAAI;UAClB;QACF;QAEA,IAAI,IAAI,CAACO,aAAa,CAACC,SAAS,EAAE;UAChC;UACA;UACA;UACA;UACA;UACA,MAAM,IAAII,KAAK,CAAC,2BAA2B,CAAC;QAC9C;QAEA,IAAI,CAACG,qBAAqB,CAAC,CAAC;;QAE5B;QACA,IAAI,CAACR,aAAa,CAACS,MAAM,GAAG,WAAW;QACvC,IAAI,CAACT,aAAa,CAACC,SAAS,GAAG,IAAI;QACnC,IAAI,CAACD,aAAa,CAACU,UAAU,GAAG,CAAC;QACjC,IAAI,CAACC,aAAa,CAAC,CAAC;;QAEpB;QACA;QACA,IAAI,CAACC,eAAe,CAAC,OAAO,EAAEC,QAAQ,IAAI;UACxCA,QAAQ,CAAC,CAAC;QACZ,CAAC,CAAC;MACJ;MAEAC,QAAQA,CAACC,UAAU,EAAE;QACnB,IAAI,CAACP,qBAAqB,CAAC,CAAC;QAC5B,IAAI,IAAI,CAACf,MAAM,EAAE;UACf,IAAIA,MAAM,GAAG,IAAI,CAACA,MAAM;UACxB,IAAI,CAACA,MAAM,GAAG,IAAI;UAClBA,MAAM,CAACc,KAAK,CAAC,CAAC;UAEd,IAAI,CAACK,eAAe,CAAC,YAAY,EAAEC,QAAQ,IAAI;YAC7CA,QAAQ,CAACE,UAAU,CAAC;UACtB,CAAC,CAAC;QACJ;MACF;MAEAP,qBAAqBA,CAAA,EAAG;QACtB,IAAI,IAAI,CAACQ,eAAe,EAAE;UACxBC,YAAY,CAAC,IAAI,CAACD,eAAe,CAAC;UAClC,IAAI,CAACA,eAAe,GAAG,IAAI;QAC7B;MACF;MAEAE,YAAYA,CAACC,SAAS,EAAE;QACtB;QACA,IAAIC,KAAK,GAAG3C,OAAO,CAACC,GAAG,CAAC2C,UAAU,IAAI5C,OAAO,CAACC,GAAG,CAAC4C,UAAU,IAAI,IAAI;QACpE,IAAIC,OAAO,GAAG9C,OAAO,CAACC,GAAG,CAAC8C,QAAQ,IAAI/C,OAAO,CAACC,GAAG,CAAC+C,QAAQ,IAAI,IAAI;QAClE;QACA,IAAIN,SAAS,CAACO,KAAK,CAAC,OAAO,CAAC,IAAIP,SAAS,CAACO,KAAK,CAAC,SAAS,CAAC,EAAE;UAC1DN,KAAK,GAAG3C,OAAO,CAACC,GAAG,CAACiD,WAAW,IAAIlD,OAAO,CAACC,GAAG,CAACkD,WAAW,IAAIR,KAAK;QACrE;QACA,IAAID,SAAS,CAACU,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAIV,SAAS,CAACU,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE;UAChF,OAAO,IAAI;QACb;QACA,IAAIN,OAAO,EAAE;UACX,KAAK,IAAIO,IAAI,IAAIP,OAAO,CAACQ,KAAK,CAAC,GAAG,CAAC,EAAE;YACnC,IAAIZ,SAAS,CAACU,OAAO,CAACC,IAAI,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;cAC3Db,KAAK,GAAG,IAAI;YACd;UACF;QACF;QACA,OAAOA,KAAK;MACd;MAEAvB,iBAAiBA,CAAA,EAAG;QAAA,IAAAqC,KAAA;QAClB,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAEjB;QACA;QACA;QACA,IAAIqB,aAAa,GAAGC,GAAG,CAACC,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAIC,OAAO,GAAGF,GAAG,CAACC,OAAO,CAAC,qBAAqB,CAAC;QAEhD,IAAIlB,SAAS,GAAG9B,cAAc,CAAC,IAAI,CAACE,QAAQ,CAAC;QAC7C,IAAIgD,WAAW,GAAG;UAChB7C,OAAO,EAAE,IAAI,CAACA,OAAO;UACrB8C,UAAU,EAAE,CAACF,OAAO;QACtB,CAAC;QACDC,WAAW,GAAGE,MAAM,CAACC,MAAM,CAACH,WAAW,EAAE,IAAI,CAAC5C,cAAc,CAAC;QAC7D,IAAIgD,QAAQ,GAAG,IAAI,CAACzB,YAAY,CAACC,SAAS,CAAC;QAC3C,IAAIwB,QAAQ,EAAE;UACZJ,WAAW,CAACnB,KAAK,GAAG;YAAEwB,MAAM,EAAED;UAAS,CAAC;QAC1C;;QAEA;QACA;QACA;QACA;QACA;QACA,IAAIE,YAAY,GAAG,EAAE;QAErB,IAAIpD,MAAM,GAAI,IAAI,CAACA,MAAM,GAAG,IAAI0C,aAAa,CAACW,MAAM,CAClD3B,SAAS,EACT0B,YAAY,EACZN,WACF,CAAE;QAEF,IAAI,CAAC/B,qBAAqB,CAAC,CAAC;QAC5B,IAAI,CAACQ,eAAe,GAAG7B,MAAM,CAAC4D,UAAU,CAAC,MAAM;UAC7C,IAAI,CAACC,eAAe,CAAC,IAAI,IAAI,CAACC,eAAe,CAAC,0BAA0B,CAAC,CAAC;QAC5E,CAAC,EAAE,IAAI,CAACC,eAAe,CAAC;QAExB,IAAI,CAACzD,MAAM,CAAC0D,EAAE,CACZ,MAAM,EACNhE,MAAM,CAACiE,eAAe,CAAC,MAAM;UAC3B,OAAO,IAAI,CAAChD,UAAU,CAACX,MAAM,CAAC;QAChC,CAAC,EAAE,yBAAyB,CAC9B,CAAC;QAED,IAAI4D,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,WAAW,EAAE1C,QAAQ,KAAK;UACxD,IAAI,CAACpB,MAAM,CAAC0D,EAAE,CACZG,KAAK,EACLnE,MAAM,CAACiE,eAAe,CAAC,YAAa;YAClC;YACA,IAAI3D,MAAM,KAAKyC,KAAI,CAACzC,MAAM,EAAE;YAC5BoB,QAAQ,CAAC,GAAA2C,SAAO,CAAC;UACnB,CAAC,EAAED,WAAW,CAChB,CAAC;QACH,CAAC;QAEDF,iBAAiB,CAAC,OAAO,EAAE,uBAAuB,EAAEI,KAAK,IAAI;UAC3D,IAAI,CAAC,IAAI,CAACjE,OAAO,CAACkE,gBAAgB,EAChCvE,MAAM,CAACwE,MAAM,CAAC,cAAc,EAAEF,KAAK,CAACG,OAAO,CAAC;;UAE9C;UACA;UACA,IAAI,CAACZ,eAAe,CAAC,IAAI,IAAI,CAACC,eAAe,CAACQ,KAAK,CAACG,OAAO,CAAC,CAAC;QAC/D,CAAC,CAAC;QAEFP,iBAAiB,CAAC,OAAO,EAAE,uBAAuB,EAAE,MAAM;UACxD,IAAI,CAACL,eAAe,CAAC,CAAC;QACxB,CAAC,CAAC;QAEFK,iBAAiB,CAAC,SAAS,EAAE,yBAAyB,EAAEO,OAAO,IAAI;UACjE;UACA,IAAI,OAAOA,OAAO,CAAC7D,IAAI,KAAK,QAAQ,EAAE;UAEtC,IAAI,CAACa,eAAe,CAAC,SAAS,EAAEC,QAAQ,IAAI;YAC1CA,QAAQ,CAAC+C,OAAO,CAAC7D,IAAI,CAAC;UACxB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAAClB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC7MD,IAAI6E,aAAa;IAACtF,MAAM,CAACX,IAAI,CAAC,sCAAsC,EAAC;MAACkG,OAAOA,CAACjG,CAAC,EAAC;QAACgG,aAAa,GAAChG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGU,MAAM,CAACU,MAAM,CAAC;MAACG,kBAAkB,EAACA,CAAA,KAAIA;IAAkB,CAAC,CAAC;IAAC,IAAI2E,KAAK;IAACxF,MAAM,CAACX,IAAI,CAAC,cAAc,EAAC;MAACmG,KAAKA,CAAClG,CAAC,EAAC;QAACkG,KAAK,GAAClG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEnL,MAAMkG,oBAAoB,GAAG,IAAI3D,KAAK,CAAC,kBAAkB,CAAC;IAEnD,MAAMjB,kBAAkB,CAAC;MAC9BE,WAAWA,CAACE,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAAqE,aAAA;UACVI,KAAK,EAAE;QAAI,GACPzE,OAAO,IAAI,IAAI,CACpB;QAED,IAAI,CAACyD,eAAe,GAClBzD,OAAO,IAAIA,OAAO,CAACyD,eAAe,IAAI5C,KAAK;MAC/C;;MAEA;MACA8C,EAAEA,CAACe,IAAI,EAAErD,QAAQ,EAAE;QACjB,IAAIqD,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,YAAY,EACjE,MAAM,IAAI7D,KAAK,CAAC,sBAAsB,GAAG6D,IAAI,CAAC;QAEhD,IAAI,CAAC,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,EAAE,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,GAAG,EAAE;QAC9D,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,CAACE,IAAI,CAACvD,QAAQ,CAAC;MAC1C;MAEAD,eAAeA,CAACsD,IAAI,EAAEG,EAAE,EAAE;QACxB,IAAI,CAAC,IAAI,CAACF,cAAc,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAACC,cAAc,CAACD,IAAI,CAAC,CAACI,MAAM,EAAE;UACnE;QACF;QAEA,IAAI,CAACH,cAAc,CAACD,IAAI,CAAC,CAACK,OAAO,CAACF,EAAE,CAAC;MACvC;MAEAzE,WAAWA,CAACJ,OAAO,EAAE;QACnBA,OAAO,GAAGA,OAAO,IAAIiD,MAAM,CAAC+B,MAAM,CAAC,IAAI,CAAC;;QAExC;;QAEA;QACA;QACA,IAAI,CAACtB,eAAe,GAAG1D,OAAO,CAACiF,gBAAgB,IAAI,KAAK;QAExD,IAAI,CAACN,cAAc,GAAG1B,MAAM,CAAC+B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;QAE3C,IAAI,CAAClE,mBAAmB,GAAG,KAAK;;QAEhC;QACA,IAAI,CAACN,aAAa,GAAG;UACnBS,MAAM,EAAE,YAAY;UACpBR,SAAS,EAAE,KAAK;UAChBS,UAAU,EAAE;QACd,CAAC;QAED,IAAIgE,OAAO,CAACC,OAAO,EAAE;UACnB,IAAI,CAACC,eAAe,GAAG,IAAIF,OAAO,CAACC,OAAO,CAACE,OAAO,CAACC,UAAU,CAAC,CAAC;QACjE;QAEA,IAAI,CAACnE,aAAa,GAAG,MAAM;UACzB,IAAI,IAAI,CAACiE,eAAe,EAAE;YACxB,IAAI,CAACA,eAAe,CAACG,OAAO,CAAC,CAAC;UAChC;QACF,CAAC;;QAED;QACA,IAAI,CAACC,MAAM,GAAG,IAAIjB,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC/C,eAAe,GAAG,IAAI;MAC7B;;MAEA;MACAiE,SAASA,CAACzF,OAAO,EAAE;QACjBA,OAAO,GAAGA,OAAO,IAAIiD,MAAM,CAAC+B,MAAM,CAAC,IAAI,CAAC;QAExC,IAAIhF,OAAO,CAACW,GAAG,EAAE;UACf,IAAI,CAACD,UAAU,CAACV,OAAO,CAACW,GAAG,CAAC;QAC9B;QAEA,IAAIX,OAAO,CAAC0F,cAAc,EAAE;UAC1B,IAAI,CAAC1F,OAAO,CAAC0F,cAAc,GAAG1F,OAAO,CAAC0F,cAAc;QACtD;QAEA,IAAI,IAAI,CAAClF,aAAa,CAACC,SAAS,EAAE;UAChC,IAAIT,OAAO,CAAC2F,MAAM,IAAI3F,OAAO,CAACW,GAAG,EAAE;YACjC,IAAI,CAAC6C,eAAe,CAACgB,oBAAoB,CAAC;UAC5C;UACA;QACF;;QAEA;QACA,IAAI,IAAI,CAAChE,aAAa,CAACS,MAAM,KAAK,YAAY,EAAE;UAC9C;UACA,IAAI,CAACuC,eAAe,CAAC,CAAC;QACxB;QAEA,IAAI,CAACgC,MAAM,CAACI,KAAK,CAAC,CAAC;QACnB,IAAI,CAACpF,aAAa,CAACU,UAAU,IAAI,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC2E,SAAS,CAAC,CAAC;MAClB;MAEAC,UAAUA,CAAC9F,OAAO,EAAE;QAClBA,OAAO,GAAGA,OAAO,IAAIiD,MAAM,CAAC+B,MAAM,CAAC,IAAI,CAAC;;QAExC;QACA;QACA,IAAI,IAAI,CAAClE,mBAAmB,EAAE;;QAE9B;QACA;QACA;QACA;QACA,IAAId,OAAO,CAAC+F,UAAU,EAAE;UACtB,IAAI,CAACjF,mBAAmB,GAAG,IAAI;QACjC;QAEA,IAAI,CAACQ,QAAQ,CAAC,CAAC;QACf,IAAI,CAACkE,MAAM,CAACI,KAAK,CAAC,CAAC;QAEnB,IAAI,CAACpF,aAAa,GAAG;UACnBS,MAAM,EAAEjB,OAAO,CAAC+F,UAAU,GAAG,QAAQ,GAAG,SAAS;UACjDtF,SAAS,EAAE,KAAK;UAChBS,UAAU,EAAE;QACd,CAAC;QAED,IAAIlB,OAAO,CAAC+F,UAAU,IAAI/F,OAAO,CAACgG,MAAM,EACtC,IAAI,CAACxF,aAAa,CAACyF,MAAM,GAAGjG,OAAO,CAACgG,MAAM;QAE5C,IAAI,CAAC7E,aAAa,CAAC,CAAC;MACtB;;MAEA;MACAqC,eAAeA,CAACjC,UAAU,EAAE;QAC1B,IAAI,CAACD,QAAQ,CAACC,UAAU,CAAC;QACzB,IAAI,CAAC2E,WAAW,CAAC3E,UAAU,CAAC,CAAC,CAAC;MAChC;;MAEA;MACA;MACA4E,OAAOA,CAAA,EAAG;QACR;QACA,IAAI,IAAI,CAAC3F,aAAa,CAACS,MAAM,IAAI,SAAS,EAAE,IAAI,CAACwE,SAAS,CAAC,CAAC;MAC9D;MAEAS,WAAWA,CAAC3E,UAAU,EAAE;QACtB,IAAI6E,OAAO,GAAG,CAAC;QACf,IAAI,IAAI,CAACpG,OAAO,CAACyE,KAAK,IAClBlD,UAAU,KAAKiD,oBAAoB,EAAE;UACvC4B,OAAO,GAAG,IAAI,CAACZ,MAAM,CAACa,UAAU,CAC9B,IAAI,CAAC7F,aAAa,CAACU,UAAU,EAC7B,IAAI,CAAC2E,SAAS,CAACS,IAAI,CAAC,IAAI,CAC1B,CAAC;UACD,IAAI,CAAC9F,aAAa,CAACS,MAAM,GAAG,SAAS;UACrC,IAAI,CAACT,aAAa,CAAC+F,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGL,OAAO;QAC/D,CAAC,MAAM;UACL,IAAI,CAAC5F,aAAa,CAACS,MAAM,GAAG,QAAQ;UACpC,OAAO,IAAI,CAACT,aAAa,CAAC+F,SAAS;QACrC;QAEA,IAAI,CAAC/F,aAAa,CAACC,SAAS,GAAG,KAAK;QACpC,IAAI,CAACU,aAAa,CAAC,CAAC;MACtB;MAEA0E,SAASA,CAAA,EAAG;QACV,IAAI,IAAI,CAAC/E,mBAAmB,EAAE;QAE9B,IAAI,CAACN,aAAa,CAACU,UAAU,IAAI,CAAC;QAClC,IAAI,CAACV,aAAa,CAACS,MAAM,GAAG,YAAY;QACxC,IAAI,CAACT,aAAa,CAACC,SAAS,GAAG,KAAK;QACpC,OAAO,IAAI,CAACD,aAAa,CAAC+F,SAAS;QACnC,IAAI,CAACpF,aAAa,CAAC,CAAC;QAEpB,IAAI,CAACd,iBAAiB,CAAC,CAAC;MAC1B;;MAEA;MACAY,MAAMA,CAAA,EAAG;QACP,IAAI,IAAI,CAACmE,eAAe,EAAE;UACxB,IAAI,CAACA,eAAe,CAACsB,MAAM,CAAC,CAAC;QAC/B;QACA,OAAO,IAAI,CAAClG,aAAa;MAC3B;IACF;IAACnB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IClLDT,MAAM,CAACU,MAAM,CAAC;MAACkH,WAAW,EAACA,CAAA,KAAIA,WAAW;MAAC9G,cAAc,EAACA,CAAA,KAAIA;IAAc,CAAC,CAAC;IAAC,IAAIF,MAAM;IAACZ,MAAM,CAACX,IAAI,CAAC,eAAe,EAAC;MAACuB,MAAMA,CAACtB,CAAC,EAAC;QAACsB,MAAM,GAACtB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAE3M;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASsI,YAAYA,CAACjG,GAAG,EAAEkG,aAAa,EAAEC,OAAO,EAAE;MACjD,IAAI,CAACD,aAAa,EAAE;QAClBA,aAAa,GAAG,MAAM;MACxB;MAEA,IAAIC,OAAO,KAAK,QAAQ,IAAInG,GAAG,CAACoG,UAAU,CAAC,GAAG,CAAC,EAAE;QAC/CpG,GAAG,GAAGhB,MAAM,CAACqH,WAAW,CAACrG,GAAG,CAACsG,MAAM,CAAC,CAAC,CAAC,CAAC;MACzC;MAEA,IAAIC,WAAW,GAAGvG,GAAG,CAACuB,KAAK,CAAC,uBAAuB,CAAC;MACpD,IAAIiF,YAAY,GAAGxG,GAAG,CAACuB,KAAK,CAAC,gBAAgB,CAAC;MAC9C,IAAIkF,SAAS;MACb,IAAIF,WAAW,EAAE;QACf;QACA,IAAIG,WAAW,GAAG1G,GAAG,CAACsG,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAACpC,MAAM,CAAC;QACnDsC,SAAS,GAAGF,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGL,aAAa,GAAGA,aAAa,GAAG,GAAG;QACxE,IAAIS,QAAQ,GAAGD,WAAW,CAAChF,OAAO,CAAC,GAAG,CAAC;QACvC,IAAIkF,IAAI,GAAGD,QAAQ,KAAK,CAAC,CAAC,GAAGD,WAAW,GAAGA,WAAW,CAACJ,MAAM,CAAC,CAAC,EAAEK,QAAQ,CAAC;QAC1E,IAAIE,IAAI,GAAGF,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE,GAAGD,WAAW,CAACJ,MAAM,CAACK,QAAQ,CAAC;;QAE9D;QACA;QACA;QACAC,IAAI,GAAGA,IAAI,CAAC9E,OAAO,CAAC,KAAK,EAAE,MAAMgF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QAEhE,OAAOP,SAAS,GAAG,KAAK,GAAGG,IAAI,GAAGC,IAAI;MACxC,CAAC,MAAM,IAAIL,YAAY,EAAE;QACvBC,SAAS,GAAG,CAACD,YAAY,CAAC,CAAC,CAAC,GAAGN,aAAa,GAAGA,aAAa,GAAG,GAAG;QAClE,IAAIe,YAAY,GAAGjH,GAAG,CAACsG,MAAM,CAACE,YAAY,CAAC,CAAC,CAAC,CAACrC,MAAM,CAAC;QACrDnE,GAAG,GAAGyG,SAAS,GAAG,KAAK,GAAGQ,YAAY;MACxC;;MAEA;MACA,IAAIjH,GAAG,CAAC0B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC1B,GAAG,CAACoG,UAAU,CAAC,GAAG,CAAC,EAAE;QACrDpG,GAAG,GAAGkG,aAAa,GAAG,KAAK,GAAGlG,GAAG;MACnC;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAA,GAAG,GAAGhB,MAAM,CAACkI,sBAAsB,CAAClH,GAAG,CAAC;MAExC,IAAIA,GAAG,CAACmH,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAOnH,GAAG,GAAGmG,OAAO,CAAC,KACvC,OAAOnG,GAAG,GAAG,GAAG,GAAGmG,OAAO;IACjC;IAEO,SAASH,WAAWA,CAAChG,GAAG,EAAE;MAC/B,OAAOiG,YAAY,CAACjG,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC;IAC5C;IAEO,SAASd,cAAcA,CAACc,GAAG,EAAE;MAClC,OAAOiG,YAAY,CAACjG,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC;IAC7C;IAACtB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/socket-stream-client.js", "sourcesContent": ["import {\n  setMinimumBrowserVersions,\n} from \"meteor/modern-browsers\";\n\nsetMinimumBrowserVersions({\n  chrome: 16,\n  edge: 12,\n  firefox: 11,\n  ie: 10,\n  mobileSafari: [6, 1],\n  phantomjs: 2,\n  safari: 7,\n  electron: [0, 20],\n}, module.id);\n\nif (process.env.DISABLE_SOCKJS) {\n  __meteor_runtime_config__.DISABLE_SOCKJS = process.env.DISABLE_SOCKJS;\n}", "import { Meteor } from \"meteor/meteor\";\nimport { Stream<PERSON>lientCommon } from \"./common.js\";\nimport { toWebsocketUrl } from \"./urls.js\";\n\n// @param endpoint {String} URL to Meteor app\n//   \"http://subdomain.meteor.com/\" or \"/\" or\n//   \"ddp+sockjs://foo-**.meteor.com/sockjs\"\n//\n// We do some rewriting of the URL to eventually make it \"ws://\" or \"wss://\",\n// whatever was passed in.  At the very least, what Meteor.absoluteUrl() returns\n// us should work.\n//\n// We don't do any heartbeating. (The logic that did this in sockjs was removed,\n// because it used a built-in sockjs mechanism. We could do it with WebSocket\n// ping frames or with DDP-level messages.)\nexport class ClientStream extends StreamClientCommon {\n  constructor(endpoint, options) {\n    super(options);\n\n    this.client = null; // created in _launchConnection\n    this.endpoint = endpoint;\n\n    this.headers = this.options.headers || {};\n    this.npmFayeOptions = this.options.npmFayeOptions || {};\n\n    this._initCommon(this.options);\n\n    //// Kickoff!\n    this._launchConnection();\n  }\n\n  // data is a utf8 string. Data sent while not connected is dropped on\n  // the floor, and it is up the user of this API to retransmit lost\n  // messages on 'reset'\n  send(data) {\n    if (this.currentStatus.connected) {\n      this.client.send(data);\n    }\n  }\n\n  // Changes where this connection points\n  _changeUrl(url) {\n    this.endpoint = url;\n  }\n\n  _onConnect(client) {\n    if (client !== this.client) {\n      // This connection is not from the last call to _launchConnection.\n      // But _launchConnection calls _cleanup which closes previous connections.\n      // It's our belief that this stifles future 'open' events, but maybe\n      // we are wrong?\n      throw new Error('Got open from inactive client ' + !!this.client);\n    }\n\n    if (this._forcedToDisconnect) {\n      // We were asked to disconnect between trying to open the connection and\n      // actually opening it. Let's just pretend this never happened.\n      this.client.close();\n      this.client = null;\n      return;\n    }\n\n    if (this.currentStatus.connected) {\n      // We already have a connection. It must have been the case that we\n      // started two parallel connection attempts (because we wanted to\n      // 'reconnect now' on a hanging connection and we had no way to cancel the\n      // connection attempt.) But this shouldn't happen (similarly to the client\n      // !== this.client check above).\n      throw new Error('Two parallel connections?');\n    }\n\n    this._clearConnectionTimer();\n\n    // update status\n    this.currentStatus.status = 'connected';\n    this.currentStatus.connected = true;\n    this.currentStatus.retryCount = 0;\n    this.statusChanged();\n\n    // fire resets. This must come after status change so that clients\n    // can call send from within a reset callback.\n    this.forEachCallback('reset', callback => {\n      callback();\n    });\n  }\n\n  _cleanup(maybeError) {\n    this._clearConnectionTimer();\n    if (this.client) {\n      var client = this.client;\n      this.client = null;\n      client.close();\n\n      this.forEachCallback('disconnect', callback => {\n        callback(maybeError);\n      });\n    }\n  }\n\n  _clearConnectionTimer() {\n    if (this.connectionTimer) {\n      clearTimeout(this.connectionTimer);\n      this.connectionTimer = null;\n    }\n  }\n\n  _getProxyUrl(targetUrl) {\n    // Similar to code in tools/http-helpers.js.\n    var proxy = process.env.HTTP_PROXY || process.env.http_proxy || null;\n    var noproxy = process.env.NO_PROXY || process.env.no_proxy || null;\n    // if we're going to a secure url, try the https_proxy env variable first.\n    if (targetUrl.match(/^wss:/) || targetUrl.match(/^https:/)) {\n      proxy = process.env.HTTPS_PROXY || process.env.https_proxy || proxy;\n    }\n    if (targetUrl.indexOf('localhost') != -1 || targetUrl.indexOf('127.0.0.1') != -1) {\n      return null;\n    }\n    if (noproxy) {\n      for (let item of noproxy.split(',')) {\n        if (targetUrl.indexOf(item.trim().replace(/\\*/, '')) !== -1) {\n          proxy = null;\n        }\n      }\n    }\n    return proxy;\n  }\n\n  _launchConnection() {\n    this._cleanup(); // cleanup the old socket, if there was one.\n\n    // Since server-to-server DDP is still an experimental feature, we only\n    // require the module if we actually create a server-to-server\n    // connection.\n    var FayeWebSocket = Npm.require('faye-websocket');\n    var deflate = Npm.require('permessage-deflate2');\n\n    var targetUrl = toWebsocketUrl(this.endpoint);\n    var fayeOptions = {\n      headers: this.headers,\n      extensions: [deflate]\n    };\n    fayeOptions = Object.assign(fayeOptions, this.npmFayeOptions);\n    var proxyUrl = this._getProxyUrl(targetUrl);\n    if (proxyUrl) {\n      fayeOptions.proxy = { origin: proxyUrl };\n    }\n\n    // We would like to specify 'ddp' as the subprotocol here. The npm module we\n    // used to use as a client would fail the handshake if we ask for a\n    // subprotocol and the server doesn't send one back (and sockjs doesn't).\n    // Faye doesn't have that behavior; it's unclear from reading RFC 6455 if\n    // Faye is erroneous or not.  So for now, we don't specify protocols.\n    var subprotocols = [];\n\n    var client = (this.client = new FayeWebSocket.Client(\n      targetUrl,\n      subprotocols,\n      fayeOptions\n    ));\n\n    this._clearConnectionTimer();\n    this.connectionTimer = Meteor.setTimeout(() => {\n      this._lostConnection(new this.ConnectionError('DDP connection timed out'));\n    }, this.CONNECT_TIMEOUT);\n\n    this.client.on(\n      'open',\n      Meteor.bindEnvironment(() => {\n        return this._onConnect(client);\n      }, 'stream connect callback')\n    );\n\n    var clientOnIfCurrent = (event, description, callback) => {\n      this.client.on(\n        event,\n        Meteor.bindEnvironment((...args) => {\n          // Ignore events from any connection we've already cleaned up.\n          if (client !== this.client) return;\n          callback(...args);\n        }, description)\n      );\n    };\n\n    clientOnIfCurrent('error', 'stream error callback', error => {\n      if (!this.options._dontPrintErrors)\n        Meteor._debug('stream error', error.message);\n\n      // Faye's 'error' object is not a JS error (and among other things,\n      // doesn't stringify well). Convert it to one.\n      this._lostConnection(new this.ConnectionError(error.message));\n    });\n\n    clientOnIfCurrent('close', 'stream close callback', () => {\n      this._lostConnection();\n    });\n\n    clientOnIfCurrent('message', 'stream message callback', message => {\n      // Ignore binary frames, where message.data is a Buffer\n      if (typeof message.data !== 'string') return;\n\n      this.forEachCallback('message', callback => {\n        callback(message.data);\n      });\n    });\n  }\n}\n", "import { Retry } from 'meteor/retry';\n\nconst forcedReconnectError = new Error(\"forced reconnect\");\n\nexport class StreamClientCommon {\n  constructor(options) {\n    this.options = {\n      retry: true,\n      ...(options || null),\n    };\n\n    this.ConnectionError =\n      options && options.ConnectionError || Error;\n  }\n\n  // Register for callbacks.\n  on(name, callback) {\n    if (name !== 'message' && name !== 'reset' && name !== 'disconnect')\n      throw new Error('unknown event type: ' + name);\n\n    if (!this.eventCallbacks[name]) this.eventCallbacks[name] = [];\n    this.eventCallbacks[name].push(callback);\n  }\n\n  forEachCallback(name, cb) {\n    if (!this.eventCallbacks[name] || !this.eventCallbacks[name].length) {\n      return;\n    }\n\n    this.eventCallbacks[name].forEach(cb);\n  }\n\n  _initCommon(options) {\n    options = options || Object.create(null);\n\n    //// Constants\n\n    // how long to wait until we declare the connection attempt\n    // failed.\n    this.CONNECT_TIMEOUT = options.connectTimeoutMs || 10000;\n\n    this.eventCallbacks = Object.create(null); // name -> [callback]\n\n    this._forcedToDisconnect = false;\n\n    //// Reactive status\n    this.currentStatus = {\n      status: 'connecting',\n      connected: false,\n      retryCount: 0\n    };\n\n    if (Package.tracker) {\n      this.statusListeners = new Package.tracker.Tracker.Dependency();\n    }\n\n    this.statusChanged = () => {\n      if (this.statusListeners) {\n        this.statusListeners.changed();\n      }\n    };\n\n    //// Retry logic\n    this._retry = new Retry();\n    this.connectionTimer = null;\n  }\n\n  // Trigger a reconnect.\n  reconnect(options) {\n    options = options || Object.create(null);\n\n    if (options.url) {\n      this._changeUrl(options.url);\n    }\n\n    if (options._sockjsOptions) {\n      this.options._sockjsOptions = options._sockjsOptions;\n    }\n\n    if (this.currentStatus.connected) {\n      if (options._force || options.url) {\n        this._lostConnection(forcedReconnectError);\n      }\n      return;\n    }\n\n    // if we're mid-connection, stop it.\n    if (this.currentStatus.status === 'connecting') {\n      // Pretend it's a clean close.\n      this._lostConnection();\n    }\n\n    this._retry.clear();\n    this.currentStatus.retryCount -= 1; // don't count manual retries\n    this._retryNow();\n  }\n\n  disconnect(options) {\n    options = options || Object.create(null);\n\n    // Failed is permanent. If we're failed, don't let people go back\n    // online by calling 'disconnect' then 'reconnect'.\n    if (this._forcedToDisconnect) return;\n\n    // If _permanent is set, permanently disconnect a stream. Once a stream\n    // is forced to disconnect, it can never reconnect. This is for\n    // error cases such as ddp version mismatch, where trying again\n    // won't fix the problem.\n    if (options._permanent) {\n      this._forcedToDisconnect = true;\n    }\n\n    this._cleanup();\n    this._retry.clear();\n\n    this.currentStatus = {\n      status: options._permanent ? 'failed' : 'offline',\n      connected: false,\n      retryCount: 0\n    };\n\n    if (options._permanent && options._error)\n      this.currentStatus.reason = options._error;\n\n    this.statusChanged();\n  }\n\n  // maybeError is set unless it's a clean protocol-level close.\n  _lostConnection(maybeError) {\n    this._cleanup(maybeError);\n    this._retryLater(maybeError); // sets status. no need to do it here.\n  }\n\n  // fired when we detect that we've gone online. try to reconnect\n  // immediately.\n  _online() {\n    // if we've requested to be offline by disconnecting, don't reconnect.\n    if (this.currentStatus.status != 'offline') this.reconnect();\n  }\n\n  _retryLater(maybeError) {\n    var timeout = 0;\n    if (this.options.retry ||\n        maybeError === forcedReconnectError) {\n      timeout = this._retry.retryLater(\n        this.currentStatus.retryCount,\n        this._retryNow.bind(this)\n      );\n      this.currentStatus.status = 'waiting';\n      this.currentStatus.retryTime = new Date().getTime() + timeout;\n    } else {\n      this.currentStatus.status = 'failed';\n      delete this.currentStatus.retryTime;\n    }\n\n    this.currentStatus.connected = false;\n    this.statusChanged();\n  }\n\n  _retryNow() {\n    if (this._forcedToDisconnect) return;\n\n    this.currentStatus.retryCount += 1;\n    this.currentStatus.status = 'connecting';\n    this.currentStatus.connected = false;\n    delete this.currentStatus.retryTime;\n    this.statusChanged();\n\n    this._launchConnection();\n  }\n\n  // Get current status. Reactive.\n  status() {\n    if (this.statusListeners) {\n      this.statusListeners.depend();\n    }\n    return this.currentStatus;\n  }\n}\n", "import { Meteor } from \"meteor/meteor\";\n\n// @param url {String} URL to Meteor app, eg:\n//   \"/\" or \"madewith.meteor.com\" or \"https://foo.meteor.com\"\n//   or \"ddp+sockjs://ddp--****-foo.meteor.com/sockjs\"\n// @returns {String} URL to the endpoint with the specific scheme and subPath, e.g.\n// for scheme \"http\" and subPath \"sockjs\"\n//   \"http://subdomain.meteor.com/sockjs\" or \"/sockjs\"\n//   or \"https://ddp--1234-foo.meteor.com/sockjs\"\nfunction translateUrl(url, newSchemeBase, subPath) {\n  if (!newSchemeBase) {\n    newSchemeBase = 'http';\n  }\n\n  if (subPath !== \"sockjs\" && url.startsWith(\"/\")) {\n    url = Meteor.absoluteUrl(url.substr(1));\n  }\n\n  var ddpUrlMatch = url.match(/^ddp(i?)\\+sockjs:\\/\\//);\n  var httpUrlMatch = url.match(/^http(s?):\\/\\//);\n  var newScheme;\n  if (ddpUrlMatch) {\n    // Remove scheme and split off the host.\n    var urlAfterDDP = url.substr(ddpUrlMatch[0].length);\n    newScheme = ddpUrlMatch[1] === 'i' ? newSchemeBase : newSchemeBase + 's';\n    var slashPos = urlAfterDDP.indexOf('/');\n    var host = slashPos === -1 ? urlAfterDDP : urlAfterDDP.substr(0, slashPos);\n    var rest = slashPos === -1 ? '' : urlAfterDDP.substr(slashPos);\n\n    // In the host (ONLY!), change '*' characters into random digits. This\n    // allows different stream connections to connect to different hostnames\n    // and avoid browser per-hostname connection limits.\n    host = host.replace(/\\*/g, () => Math.floor(Math.random() * 10));\n\n    return newScheme + '://' + host + rest;\n  } else if (httpUrlMatch) {\n    newScheme = !httpUrlMatch[1] ? newSchemeBase : newSchemeBase + 's';\n    var urlAfterHttp = url.substr(httpUrlMatch[0].length);\n    url = newScheme + '://' + urlAfterHttp;\n  }\n\n  // Prefix FQDNs but not relative URLs\n  if (url.indexOf('://') === -1 && !url.startsWith('/')) {\n    url = newSchemeBase + '://' + url;\n  }\n\n  // XXX This is not what we should be doing: if I have a site\n  // deployed at \"/foo\", then DDP.connect(\"/\") should actually connect\n  // to \"/\", not to \"/foo\". \"/\" is an absolute path. (Contrast: if\n  // deployed at \"/foo\", it would be reasonable for DDP.connect(\"bar\")\n  // to connect to \"/foo/bar\").\n  //\n  // We should make this properly honor absolute paths rather than\n  // forcing the path to be relative to the site root. Simultaneously,\n  // we should set DDP_DEFAULT_CONNECTION_URL to include the site\n  // root. See also client_convenience.js #RationalizingRelativeDDPURLs\n  url = Meteor._relativeToSiteRootUrl(url);\n\n  if (url.endsWith('/')) return url + subPath;\n  else return url + '/' + subPath;\n}\n\nexport function toSockjsUrl(url) {\n  return translateUrl(url, 'http', 'sockjs');\n}\n\nexport function toWebsocketUrl(url) {\n  return translateUrl(url, 'ws', 'websocket');\n}\n"]}