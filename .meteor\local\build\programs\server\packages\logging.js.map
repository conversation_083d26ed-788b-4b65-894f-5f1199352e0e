{"version": 3, "sources": ["meteor://💻app/packages/logging/logging.js", "meteor://💻app/packages/logging/logging_server.js"], "names": ["_objectSpread", "module", "link", "default", "v", "export", "Log", "Meteor", "__reifyWaitForDeps__", "hasOwn", "Object", "prototype", "hasOwnProperty", "info", "arguments", "intercept", "interceptedLines", "suppress", "_intercept", "count", "_suppress", "_intercepted", "lines", "outputFormat", "showTime", "LEVEL_COLORS", "debug", "warn", "error", "META_COLOR", "isWin32", "process", "platform", "platformColor", "color", "endsWith", "concat", "RESTRICTED_KEYS", "FORMATTED_KEYS", "logInBrowser", "obj", "str", "format", "level", "console", "log", "apply", "Function", "bind", "call", "_getCallerDetails", "getStack", "err", "Error", "stack", "line", "split", "slice", "match", "file", "details", "exec", "for<PERSON>ach", "arg", "intercepted", "RegExp", "Date", "message", "String", "toString", "key", "omitCallerDetails", "time", "isProduction", "push", "EJSON", "stringify", "isServer", "parse", "startsWith", "e", "options", "length", "undefined", "timeInexact", "lineNumber", "app", "appName", "originApp", "program", "satellite", "stderr", "keys", "pad2", "n", "padStart", "pad3", "dateStamp", "getFullYear", "getMonth", "getDate", "timeStamp", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "utcOffsetStr", "getTimezoneOffset", "appInfo", "sourceInfoParts", "sourceInfo", "join", "stderrIndicator", "timeString", "metaPrefix", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON>", "prettify", "metaColor", "objFromText", "override", "__reify_async_result__", "_reifyError", "self", "async", "require"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGH,MAAM,CAACI,MAAM,CAAC;MAACC,GAAG,EAACA,CAAA,KAAIA;IAAG,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACN,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACK,MAAMA,CAACH,CAAC,EAAC;QAACG,MAAM,GAACH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEzJ,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;IAE9C,SAASN,GAAGA,CAAA,EAAU;MACpBA,GAAG,CAACO,IAAI,CAAC,GAAAC,SAAO,CAAC;IACnB;;IAEA;IACA,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,gBAAgB,GAAG,EAAE;IACzB,IAAIC,QAAQ,GAAG,CAAC;;IAEhB;IACA;IACA;IACAX,GAAG,CAACY,UAAU,GAAIC,KAAK,IAAK;MAC1BJ,SAAS,IAAII,KAAK;IACpB,CAAC;;IAED;IACA;IACA;IACAb,GAAG,CAACc,SAAS,GAAID,KAAK,IAAK;MACzBF,QAAQ,IAAIE,KAAK;IACnB,CAAC;;IAED;IACAb,GAAG,CAACe,YAAY,GAAG,MAAM;MACvB,MAAMC,KAAK,GAAGN,gBAAgB;MAC9BA,gBAAgB,GAAG,EAAE;MACrBD,SAAS,GAAG,CAAC;MACb,OAAOO,KAAK;IACd,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAhB,GAAG,CAACiB,YAAY,GAAG,MAAM;;IAEzB;IACA;IACA;IACAjB,GAAG,CAACkB,QAAQ,GAAG,IAAI;IAEnB,MAAMC,YAAY,GAAG;MACnBC,KAAK,EAAE,OAAO;MACd;MACAC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE;IACT,CAAC;IAED,MAAMC,UAAU,GAAG,MAAM;;IAEzB;IACA;IACA;IACA;IACA,MAAMC,OAAO,GAAG,OAAOC,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACC,QAAQ,KAAK,OAAO;IAC3E,MAAMC,aAAa,GAAIC,KAAK,IAAK;MAC/B,IAAIJ,OAAO,IAAI,OAAOI,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACrE,UAAAC,MAAA,CAAUF,KAAK;MACjB;MACA,OAAOA,KAAK;IACd,CAAC;;IAED;IACA,MAAMG,eAAe,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAC/C,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC;IAEtE,MAAMC,cAAc,GAAG,CAAC,GAAGD,eAAe,EAAE,KAAK,EAAE,SAAS,CAAC;IAE7D,MAAME,YAAY,GAAGC,GAAG,IAAI;MAC1B,MAAMC,GAAG,GAAGnC,GAAG,CAACoC,MAAM,CAACF,GAAG,CAAC;;MAE3B;MACA,MAAMG,KAAK,GAAGH,GAAG,CAACG,KAAK;MAEvB,IAAK,OAAOC,OAAO,KAAK,WAAW,IAAKA,OAAO,CAACD,KAAK,CAAC,EAAE;QACtDC,OAAO,CAACD,KAAK,CAAC,CAACF,GAAG,CAAC;MACrB,CAAC,MAAM;QACL;QACA;QACA;QACA,IAAI,OAAOG,OAAO,CAACC,GAAG,CAACC,KAAK,KAAK,UAAU,EAAE;UAC3C;UACAF,OAAO,CAACC,GAAG,CAACC,KAAK,CAACF,OAAO,EAAE,CAACH,GAAG,CAAC,CAAC;QAEnC,CAAC,MAAM,IAAI,OAAOM,QAAQ,CAACpC,SAAS,CAACqC,IAAI,KAAK,UAAU,EAAE;UACxD;UACA,MAAMH,GAAG,GAAGE,QAAQ,CAACpC,SAAS,CAACqC,IAAI,CAACC,IAAI,CAACL,OAAO,CAACC,GAAG,EAAED,OAAO,CAAC;UAC9DC,GAAG,CAACC,KAAK,CAACF,OAAO,EAAE,CAACH,GAAG,CAAC,CAAC;QAC3B;MACF;IACF,CAAC;;IAED;IACAnC,GAAG,CAAC4C,iBAAiB,GAAG,MAAM;MAC5B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;QACrB;QACA;QACA;QACA,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAD,CAAC;QACrB,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK;QACvB,OAAOA,KAAK;MACd,CAAC;MAED,MAAMA,KAAK,GAAGH,QAAQ,CAAC,CAAC;MAExB,IAAI,CAACG,KAAK,EAAE,OAAO,CAAC,CAAC;;MAErB;MACA;MACA,IAAIC,IAAI;MACR,MAAMjC,KAAK,GAAGgC,KAAK,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;MACxC,KAAKF,IAAI,IAAIjC,KAAK,EAAE;QAClB,IAAIiC,IAAI,CAACG,KAAK,CAAC,8BAA8B,CAAC,EAAE;UAC9C,OAAO;YAACC,IAAI,EAAE;UAAM,CAAC;QACvB;QAEA,IAAI,CAACJ,IAAI,CAACG,KAAK,CAAC,iDAAiD,CAAC,EAAE;UAClE;QACF;MACF;MAEA,MAAME,OAAO,GAAG,CAAC,CAAC;;MAElB;MACA;MACA;MACA,MAAMF,KAAK,GAAG,yCAAyC,CAACG,IAAI,CAACN,IAAI,CAAC;MAClE,IAAI,CAACG,KAAK,EAAE;QACV,OAAOE,OAAO;MAChB;;MAEA;MACAA,OAAO,CAACL,IAAI,GAAGG,KAAK,CAAC,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAErC;MACA;MACA;MACAI,OAAO,CAACD,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAE7D,OAAOI,OAAO;IAChB,CAAC;IAED,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAACE,OAAO,CAAEnB,KAAK,IAAK;MACrD;MACArC,GAAG,CAACqC,KAAK,CAAC,GAAIoB,GAAG,IAAK;QACrB,IAAI9C,QAAQ,EAAE;UACZA,QAAQ,EAAE;UACV;QACF;QAEA,IAAI+C,WAAW,GAAG,KAAK;QACvB,IAAIjD,SAAS,EAAE;UACbA,SAAS,EAAE;UACXiD,WAAW,GAAG,IAAI;QACpB;QAEA,IAAIxB,GAAG,GAAIuB,GAAG,KAAKrD,MAAM,CAACqD,GAAG,CAAC,IACzB,EAAEA,GAAG,YAAYE,MAAM,CAAC,IACxB,EAAEF,GAAG,YAAYG,IAAI,CAAC,GACvBH,GAAG,GACH;UAAEI,OAAO,EAAE,IAAIC,MAAM,CAACL,GAAG,CAAC,CAACM,QAAQ,CAAC;QAAE,CAAC;QAE3ChC,eAAe,CAACyB,OAAO,CAACQ,GAAG,IAAI;UAC7B,IAAI9B,GAAG,CAAC8B,GAAG,CAAC,EAAE;YACZ,MAAM,IAAIjB,KAAK,eAAAjB,MAAA,CAAekC,GAAG,qBAAkB,CAAC;UACtD;QACF,CAAC,CAAC;QAEF,IAAI7D,MAAM,CAACwC,IAAI,CAACT,GAAG,EAAE,SAAS,CAAC,IAAI,OAAOA,GAAG,CAAC2B,OAAO,KAAK,QAAQ,EAAE;UAClE,MAAM,IAAId,KAAK,CAAC,qDAAqD,CAAC;QACxE;QAEA,IAAI,CAACb,GAAG,CAAC+B,iBAAiB,EAAE;UAC1B/B,GAAG,GAAAxC,aAAA,CAAAA,aAAA,KAAQM,GAAG,CAAC4C,iBAAiB,CAAC,CAAC,GAAKV,GAAG,CAAE;QAC9C;QAEAA,GAAG,CAACgC,IAAI,GAAG,IAAIN,IAAI,CAAC,CAAC;QACrB1B,GAAG,CAACG,KAAK,GAAGA,KAAK;;QAEjB;QACA,IAAIA,KAAK,KAAK,OAAO,IAAIpC,MAAM,CAACkE,YAAY,EAAE;UAC5C;QACF;QAEA,IAAIT,WAAW,EAAE;UACfhD,gBAAgB,CAAC0D,IAAI,CAACC,KAAK,CAACC,SAAS,CAACpC,GAAG,CAAC,CAAC;QAC7C,CAAC,MAAM,IAAIjC,MAAM,CAACsE,QAAQ,EAAE;UAC1B,IAAIvE,GAAG,CAACiB,YAAY,KAAK,cAAc,EAAE;YACvCqB,OAAO,CAACC,GAAG,CAACvC,GAAG,CAACoC,MAAM,CAACF,GAAG,EAAE;cAACN,KAAK,EAAE;YAAI,CAAC,CAAC,CAAC;UAC7C,CAAC,MAAM,IAAI5B,GAAG,CAACiB,YAAY,KAAK,MAAM,EAAE;YACtCqB,OAAO,CAACC,GAAG,CAAC8B,KAAK,CAACC,SAAS,CAACpC,GAAG,CAAC,CAAC;UACnC,CAAC,MAAM;YACL,MAAM,IAAIa,KAAK,mCAAAjB,MAAA,CAAmC9B,GAAG,CAACiB,YAAY,CAAE,CAAC;UACvE;QACF,CAAC,MAAM;UACLgB,YAAY,CAACC,GAAG,CAAC;QACnB;MACF,CAAC;IACD,CAAC,CAAC;;IAGF;IACAlC,GAAG,CAACwE,KAAK,GAAIvB,IAAI,IAAK;MACpB,IAAIf,GAAG,GAAG,IAAI;MACd,IAAIe,IAAI,IAAIA,IAAI,CAACwB,UAAU,CAAC,GAAG,CAAC,EAAE;QAAE;QAClC,IAAI;UAAEvC,GAAG,GAAGmC,KAAK,CAACG,KAAK,CAACvB,IAAI,CAAC;QAAE,CAAC,CAAC,OAAOyB,CAAC,EAAE,CAAC;MAC9C;;MAEA;MACA,IAAIxC,GAAG,IAAIA,GAAG,CAACgC,IAAI,IAAKhC,GAAG,CAACgC,IAAI,YAAYN,IAAK,EAAE;QACjD,OAAO1B,GAAG;MACZ,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;;IAED;IACAlC,GAAG,CAACoC,MAAM,GAAG,UAACF,GAAG,EAAmB;MAAA,IAAjByC,OAAO,GAAAnE,SAAA,CAAAoE,MAAA,QAAApE,SAAA,QAAAqE,SAAA,GAAArE,SAAA,MAAG,CAAC,CAAC;MAC7B0B,GAAG,GAAAxC,aAAA,KAAQwC,GAAG,CAAE,CAAC,CAAC;MAClB,IAAI;QACFgC,IAAI;QACJY,WAAW;QACXzC,KAAK,GAAG,MAAM;QACdgB,IAAI;QACJJ,IAAI,EAAE8B,UAAU;QAChBC,GAAG,EAAEC,OAAO,GAAG,EAAE;QACjBC,SAAS;QACTrB,OAAO,GAAG,EAAE;QACZsB,OAAO,GAAG,EAAE;QACZC,SAAS,GAAG,EAAE;QACdC,MAAM,GAAG;MACX,CAAC,GAAGnD,GAAG;MAEP,IAAI,EAAEgC,IAAI,YAAYN,IAAI,CAAC,EAAE;QAC3B,MAAM,IAAIb,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEAf,cAAc,CAACwB,OAAO,CAAEQ,GAAG,IAAK;QAAE,OAAO9B,GAAG,CAAC8B,GAAG,CAAC;MAAE,CAAC,CAAC;MAErD,IAAI5D,MAAM,CAACkF,IAAI,CAACpD,GAAG,CAAC,CAAC0C,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAIf,OAAO,EAAE;UACXA,OAAO,IAAI,GAAG;QAChB;QACAA,OAAO,IAAIQ,KAAK,CAACC,SAAS,CAACpC,GAAG,CAAC;MACjC;MAEA,MAAMqD,IAAI,GAAGC,CAAC,IAAIA,CAAC,CAACzB,QAAQ,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC/C,MAAMC,IAAI,GAAGF,CAAC,IAAIA,CAAC,CAACzB,QAAQ,CAAC,CAAC,CAAC0B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAE/C,MAAME,SAAS,GAAGzB,IAAI,CAAC0B,WAAW,CAAC,CAAC,CAAC7B,QAAQ,CAAC,CAAC,GAC7CwB,IAAI,CAACrB,IAAI,CAAC2B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,GACrCN,IAAI,CAACrB,IAAI,CAAC4B,OAAO,CAAC,CAAC,CAAC;MACtB,MAAMC,SAAS,GAAGR,IAAI,CAACrB,IAAI,CAAC8B,QAAQ,CAAC,CAAC,CAAC,GACjC,GAAG,GACHT,IAAI,CAACrB,IAAI,CAAC+B,UAAU,CAAC,CAAC,CAAC,GACvB,GAAG,GACHV,IAAI,CAACrB,IAAI,CAACgC,UAAU,CAAC,CAAC,CAAC,GACvB,GAAG,GACHR,IAAI,CAACxB,IAAI,CAACiC,eAAe,CAAC,CAAC,CAAC;;MAElC;MACA,MAAMC,YAAY,OAAAtE,MAAA,CAAQ,EAAE,IAAI8B,IAAI,CAAC,CAAC,CAACyC,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAC,MAAI;MAEpE,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIrB,OAAO,EAAE;QACXqB,OAAO,IAAIrB,OAAO;MACpB;MACA,IAAIC,SAAS,IAAIA,SAAS,KAAKD,OAAO,EAAE;QACtCqB,OAAO,YAAAxE,MAAA,CAAYoD,SAAS,CAAE;MAChC;MACA,IAAIoB,OAAO,EAAE;QACXA,OAAO,OAAAxE,MAAA,CAAOwE,OAAO,OAAI;MAC3B;MAEA,MAAMC,eAAe,GAAG,EAAE;MAC1B,IAAIpB,OAAO,EAAE;QACXoB,eAAe,CAACnC,IAAI,CAACe,OAAO,CAAC;MAC/B;MACA,IAAI9B,IAAI,EAAE;QACRkD,eAAe,CAACnC,IAAI,CAACf,IAAI,CAAC;MAC5B;MACA,IAAI0B,UAAU,EAAE;QACdwB,eAAe,CAACnC,IAAI,CAACW,UAAU,CAAC;MAClC;MAEA,IAAIyB,UAAU,GAAG,CAACD,eAAe,CAAC3B,MAAM,GACtC,EAAE,OAAA9C,MAAA,CAAOyE,eAAe,CAACE,IAAI,CAAC,GAAG,CAAC,OAAI;MAExC,IAAIrB,SAAS,EACXoB,UAAU,QAAA1E,MAAA,CAAQsD,SAAS,MAAG;MAEhC,MAAMsB,eAAe,GAAGrB,MAAM,GAAG,WAAW,GAAG,EAAE;MAEjD,MAAMsB,UAAU,GAAG3G,GAAG,CAACkB,QAAQ,MAAAY,MAAA,CACxB6D,SAAS,OAAA7D,MAAA,CAAIiE,SAAS,EAAAjE,MAAA,CAAGsE,YAAY,EAAAtE,MAAA,CAAGgD,WAAW,GAAG,IAAI,GAAG,GAAG,IACnE,GAAG;MAIP,MAAM8B,UAAU,GAAG,CACjBvE,KAAK,CAACwE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAC7BH,UAAU,EACVL,OAAO,EACPE,UAAU,EACVE,eAAe,CAAC,CAACD,IAAI,CAAC,EAAE,CAAC;MAG3B,OAAOM,SAAS,CAACC,QAAQ,CAACJ,UAAU,EAAEjC,OAAO,CAAC/C,KAAK,IAAID,aAAa,CAACgD,OAAO,CAACsC,SAAS,IAAI1F,UAAU,CAAC,CAAC,GAClGwF,SAAS,CAACC,QAAQ,CAACnD,OAAO,EAAEc,OAAO,CAAC/C,KAAK,IAAID,aAAa,CAACR,YAAY,CAACkB,KAAK,CAAC,CAAC,CAAC;IACtF,CAAC;;IAED;IACA;IACA;IACArC,GAAG,CAACkH,WAAW,GAAG,CAACjE,IAAI,EAAEkE,QAAQ,KAAK;MACpC,OAAAzH,aAAA;QACEmE,OAAO,EAAEZ,IAAI;QACbZ,KAAK,EAAE,MAAM;QACb6B,IAAI,EAAE,IAAIN,IAAI,CAAC,CAAC;QAChBkB,WAAW,EAAE;MAAI,GACdqC,QAAQ;IAEf,CAAC;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AC5UFR,SAAS,GAAG,CAAC,CAAC;AACdA,SAAS,CAACC,QAAQ,GAAG,UAAS/D,IAAI,EAAErB,KAAK,EAAC;EACtC,IAAG,CAACA,KAAK,EAAE,OAAOqB,IAAI;EACtB,OAAOuE,OAAO,CAAC,OAAO,CAAC,CAAC5F,KAAK,CAAC,CAACqB,IAAI,CAAC;AACxC,CAAC,C", "file": "/packages/logging.js", "sourcesContent": ["import { Meteor } from 'meteor/meteor';\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nfunction Log(...args) {\n  Log.info(...args);\n}\n\n/// FOR TESTING\nlet intercept = 0;\nlet interceptedLines = [];\nlet suppress = 0;\n\n// Intercept the next 'count' calls to a Log function. The actual\n// lines printed to the console can be cleared and read by calling\n// Log._intercepted().\nLog._intercept = (count) => {\n  intercept += count;\n};\n\n// Suppress the next 'count' calls to a Log function. Use this to stop\n// tests from spamming the console, especially with red errors that\n// might look like a failing test.\nLog._suppress = (count) => {\n  suppress += count;\n};\n\n// Returns intercepted lines and resets the intercept counter.\nLog._intercepted = () => {\n  const lines = interceptedLines;\n  interceptedLines = [];\n  intercept = 0;\n  return lines;\n};\n\n// Either 'json' or 'colored-text'.\n//\n// When this is set to 'json', print JSON documents that are parsed by another\n// process ('satellite' or 'meteor run'). This other process should call\n// 'Log.format' for nice output.\n//\n// When this is set to 'colored-text', call 'Log.format' before printing.\n// This should be used for logging from within satellite, since there is no\n// other process that will be reading its standard output.\nLog.outputFormat = 'json';\n\n// Defaults to true for local development and for backwards compatibility.\n// for cloud environments is interesting to leave it false as most of them have the timestamp in the console.\n// Only works in server with colored-text\nLog.showTime = true;\n\nconst LEVEL_COLORS = {\n  debug: 'green',\n  // leave info as the default color\n  warn: 'magenta',\n  error: 'red'\n};\n\nconst META_COLOR = 'blue';\n\n// Default colors cause readability problems on Windows Powershell,\n// switch to bright variants. While still capable of millions of\n// operations per second, the benchmark showed a 25%+ increase in\n// ops per second (on Node 8) by caching \"process.platform\".\nconst isWin32 = typeof process === 'object' && process.platform === 'win32';\nconst platformColor = (color) => {\n  if (isWin32 && typeof color === 'string' && !color.endsWith('Bright')) {\n    return `${color}Bright`;\n  }\n  return color;\n};\n\n// XXX package\nconst RESTRICTED_KEYS = ['time', 'timeInexact', 'level', 'file', 'line',\n                        'program', 'originApp', 'satellite', 'stderr'];\n\nconst FORMATTED_KEYS = [...RESTRICTED_KEYS, 'app', 'message'];\n\nconst logInBrowser = obj => {\n  const str = Log.format(obj);\n\n  // XXX Some levels should be probably be sent to the server\n  const level = obj.level;\n\n  if ((typeof console !== 'undefined') && console[level]) {\n    console[level](str);\n  } else {\n    // IE doesn't have console.log.apply, it's not a real Object.\n    // http://stackoverflow.com/questions/5538972/console-log-apply-not-working-in-ie9\n    // http://patik.com/blog/complete-cross-browser-console-log/\n    if (typeof console.log.apply === \"function\") {\n      // Most browsers\n      console.log.apply(console, [str]);\n\n    } else if (typeof Function.prototype.bind === \"function\") {\n      // IE9\n      const log = Function.prototype.bind.call(console.log, console);\n      log.apply(console, [str]);\n    }\n  }\n};\n\n// @returns {Object: { line: Number, file: String }}\nLog._getCallerDetails = () => {\n  const getStack = () => {\n    // We do NOT use Error.prepareStackTrace here (a V8 extension that gets us a\n    // pre-parsed stack) since it's impossible to compose it with the use of\n    // Error.prepareStackTrace used on the server for source maps.\n    const err = new Error;\n    const stack = err.stack;\n    return stack;\n  };\n\n  const stack = getStack();\n\n  if (!stack) return {};\n\n  // looking for the first line outside the logging package (or an\n  // eval if we find that first)\n  let line;\n  const lines = stack.split('\\n').slice(1);\n  for (line of lines) {\n    if (line.match(/^\\s*(at eval \\(eval)|(eval:)/)) {\n      return {file: \"eval\"};\n    }\n\n    if (!line.match(/packages\\/(?:local-test[:_])?logging(?:\\/|\\.js)/)) {\n      break;\n    }\n  }\n\n  const details = {};\n\n  // The format for FF is 'functionName@filePath:lineNumber'\n  // The format for V8 is 'functionName (packages/logging/logging.js:81)' or\n  //                      'packages/logging/logging.js:81'\n  const match = /(?:[@(]| at )([^(]+?):([0-9:]+)(?:\\)|$)/.exec(line);\n  if (!match) {\n    return details;\n  }\n\n  // in case the matched block here is line:column\n  details.line = match[2].split(':')[0];\n\n  // Possible format: https://foo.bar.com/scripts/file.js?random=foobar\n  // XXX: if you can write the following in better way, please do it\n  // XXX: what about evals?\n  details.file = match[1].split('/').slice(-1)[0].split('?')[0];\n\n  return details;\n};\n\n['debug', 'info', 'warn', 'error'].forEach((level) => {\n // @param arg {String|Object}\n Log[level] = (arg) => {\n  if (suppress) {\n    suppress--;\n    return;\n  }\n\n  let intercepted = false;\n  if (intercept) {\n    intercept--;\n    intercepted = true;\n  }\n\n  let obj = (arg === Object(arg)\n    && !(arg instanceof RegExp)\n    && !(arg instanceof Date))\n    ? arg\n    : { message: new String(arg).toString() };\n\n  RESTRICTED_KEYS.forEach(key => {\n    if (obj[key]) {\n      throw new Error(`Can't set '${key}' in log message`);\n    }\n  });\n\n  if (hasOwn.call(obj, 'message') && typeof obj.message !== 'string') {\n    throw new Error(\"The 'message' field in log objects must be a string\");\n  }\n\n  if (!obj.omitCallerDetails) {\n    obj = { ...Log._getCallerDetails(), ...obj };\n  }\n\n  obj.time = new Date();\n  obj.level = level;\n\n  // If we are in production don't write out debug logs.\n  if (level === 'debug' && Meteor.isProduction) {\n    return;\n  }\n\n  if (intercepted) {\n    interceptedLines.push(EJSON.stringify(obj));\n  } else if (Meteor.isServer) {\n    if (Log.outputFormat === 'colored-text') {\n      console.log(Log.format(obj, {color: true}));\n    } else if (Log.outputFormat === 'json') {\n      console.log(EJSON.stringify(obj));\n    } else {\n      throw new Error(`Unknown logging output format: ${Log.outputFormat}`);\n    }\n  } else {\n    logInBrowser(obj);\n  }\n};\n});\n\n\n// tries to parse line as EJSON. returns object if parse is successful, or null if not\nLog.parse = (line) => {\n  let obj = null;\n  if (line && line.startsWith('{')) { // might be json generated from calling 'Log'\n    try { obj = EJSON.parse(line); } catch (e) {}\n  }\n\n  // XXX should probably check fields other than 'time'\n  if (obj && obj.time && (obj.time instanceof Date)) {\n    return obj;\n  } else {\n    return null;\n  }\n};\n\n// formats a log object into colored human and machine-readable text\nLog.format = (obj, options = {}) => {\n  obj = { ...obj }; // don't mutate the argument\n  let {\n    time,\n    timeInexact,\n    level = 'info',\n    file,\n    line: lineNumber,\n    app: appName = '',\n    originApp,\n    message = '',\n    program = '',\n    satellite = '',\n    stderr = '',\n  } = obj;\n\n  if (!(time instanceof Date)) {\n    throw new Error(\"'time' must be a Date object\");\n  }\n\n  FORMATTED_KEYS.forEach((key) => { delete obj[key]; });\n\n  if (Object.keys(obj).length > 0) {\n    if (message) {\n      message += ' ';\n    }\n    message += EJSON.stringify(obj);\n  }\n\n  const pad2 = n => n.toString().padStart(2, '0');\n  const pad3 = n => n.toString().padStart(3, '0');\n\n  const dateStamp = time.getFullYear().toString() +\n    pad2(time.getMonth() + 1 /*0-based*/) +\n    pad2(time.getDate());\n  const timeStamp = pad2(time.getHours()) +\n        ':' +\n        pad2(time.getMinutes()) +\n        ':' +\n        pad2(time.getSeconds()) +\n        '.' +\n        pad3(time.getMilliseconds());\n\n  // eg in San Francisco in June this will be '(-7)'\n  const utcOffsetStr = `(${(-(new Date().getTimezoneOffset() / 60))})`;\n\n  let appInfo = '';\n  if (appName) {\n    appInfo += appName;\n  }\n  if (originApp && originApp !== appName) {\n    appInfo += ` via ${originApp}`;\n  }\n  if (appInfo) {\n    appInfo = `[${appInfo}] `;\n  }\n\n  const sourceInfoParts = [];\n  if (program) {\n    sourceInfoParts.push(program);\n  }\n  if (file) {\n    sourceInfoParts.push(file);\n  }\n  if (lineNumber) {\n    sourceInfoParts.push(lineNumber);\n  }\n\n  let sourceInfo = !sourceInfoParts.length ?\n    '' : `(${sourceInfoParts.join(':')}) `;\n\n  if (satellite)\n    sourceInfo += `[${satellite}]`;\n\n  const stderrIndicator = stderr ? '(STDERR) ' : '';\n\n  const timeString = Log.showTime\n    ? `${dateStamp}-${timeStamp}${utcOffsetStr}${timeInexact ? '? ' : ' '}`\n    : ' ';\n\n\n\n  const metaPrefix = [\n    level.charAt(0).toUpperCase(),\n    timeString,\n    appInfo,\n    sourceInfo,\n    stderrIndicator].join('');\n\n\n  return Formatter.prettify(metaPrefix, options.color && platformColor(options.metaColor || META_COLOR)) +\n      Formatter.prettify(message, options.color && platformColor(LEVEL_COLORS[level]));\n};\n\n// Turn a line of text into a loggable object.\n// @param line {String}\n// @param override {Object}\nLog.objFromText = (line, override) => {\n  return {\n    message: line,\n    level: 'info',\n    time: new Date(),\n    timeInexact: true,\n    ...override\n  };\n};\n\nexport { Log };\n", "Formatter = {};\nFormatter.prettify = function(line, color){\n    if(!color) return line;\n    return require(\"chalk\")[color](line);\n};\n"]}