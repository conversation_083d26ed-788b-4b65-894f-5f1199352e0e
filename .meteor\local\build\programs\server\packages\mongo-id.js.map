{"version": 3, "sources": ["meteor://💻app/packages/mongo-id/id.js"], "names": ["module", "export", "MongoID", "EJSON", "link", "v", "Random", "__reifyWaitForDeps__", "_looksLikeObjectID", "str", "length", "test", "ObjectID", "constructor", "hexString", "toLowerCase", "Error", "_str", "equals", "other", "valueOf", "toString", "concat", "clone", "typeName", "getTimestamp", "Number", "parseInt", "substr", "toJSONValue", "toHexString", "addType", "idStringify", "id", "firstChar", "char<PERSON>t", "undefined", "JSON", "stringify", "idParse", "parse", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,OAAO,EAACA,CAAA,KAAIA;IAAO,CAAC,CAAC;IAAC,IAAIC,KAAK;IAACH,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACD,KAAKA,CAACE,CAAC,EAAC;QAACF,KAAK,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACN,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACE,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAG7N,MAAML,OAAO,GAAG,CAAC,CAAC;IAElBA,OAAO,CAACM,kBAAkB,GAAGC,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,EAAE,IAAI,aAAa,CAACC,IAAI,CAACF,GAAG,CAAC;IAEhFP,OAAO,CAACU,QAAQ,GAAG,MAAMA,QAAQ,CAAC;MAChCC,WAAWA,CAAEC,SAAS,EAAE;QACtB;QACA,IAAIA,SAAS,EAAE;UACbA,SAAS,GAAGA,SAAS,CAACC,WAAW,CAAC,CAAC;UACnC,IAAI,CAACb,OAAO,CAACM,kBAAkB,CAACM,SAAS,CAAC,EAAE;YAC1C,MAAM,IAAIE,KAAK,CAAC,qDAAqD,CAAC;UACxE;UACA;UACA,IAAI,CAACC,IAAI,GAAGH,SAAS;QACvB,CAAC,MAAM;UACL,IAAI,CAACG,IAAI,GAAGX,MAAM,CAACQ,SAAS,CAAC,EAAE,CAAC;QAClC;MACF;MAEAI,MAAMA,CAACC,KAAK,EAAE;QACZ,OAAOA,KAAK,YAAYjB,OAAO,CAACU,QAAQ,IACxC,IAAI,CAACQ,OAAO,CAAC,CAAC,KAAKD,KAAK,CAACC,OAAO,CAAC,CAAC;MACpC;MAEAC,QAAQA,CAAA,EAAG;QACT,qBAAAC,MAAA,CAAoB,IAAI,CAACL,IAAI;MAC/B;MAEAM,KAAKA,CAAA,EAAG;QACN,OAAO,IAAIrB,OAAO,CAACU,QAAQ,CAAC,IAAI,CAACK,IAAI,CAAC;MACxC;MAEAO,QAAQA,CAAA,EAAG;QACT,OAAO,KAAK;MACd;MAEAC,YAAYA,CAAA,EAAG;QACb,OAAOC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAACV,IAAI,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MACpD;MAEAR,OAAOA,CAAA,EAAG;QACR,OAAO,IAAI,CAACH,IAAI;MAClB;MAEAY,WAAWA,CAAA,EAAG;QACZ,OAAO,IAAI,CAACT,OAAO,CAAC,CAAC;MACvB;MAEAU,WAAWA,CAAA,EAAG;QACZ,OAAO,IAAI,CAACV,OAAO,CAAC,CAAC;MACvB;IAEF,CAAC;IAEDjB,KAAK,CAAC4B,OAAO,CAAC,KAAK,EAAEtB,GAAG,IAAI,IAAIP,OAAO,CAACU,QAAQ,CAACH,GAAG,CAAC,CAAC;IAEtDP,OAAO,CAAC8B,WAAW,GAAIC,EAAE,IAAK;MAC5B,IAAIA,EAAE,YAAY/B,OAAO,CAACU,QAAQ,EAAE;QAClC,OAAOqB,EAAE,CAACb,OAAO,CAAC,CAAC;MACrB,CAAC,MAAM,IAAI,OAAOa,EAAE,KAAK,QAAQ,EAAE;QACjC,IAAIC,SAAS,GAAGD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;QAC5B,IAAIF,EAAE,KAAK,EAAE,EAAE;UACb,OAAOA,EAAE;QACX,CAAC,MAAM,IAAIC,SAAS,KAAK,GAAG;QAAI;QACrBA,SAAS,KAAK,GAAG;QAAI;QACrBhC,OAAO,CAACM,kBAAkB,CAACyB,EAAE,CAAC;QAAI;QAClCC,SAAS,KAAK,GAAG,EAAE;UAAE;UAC9B,WAAAZ,MAAA,CAAWW,EAAE;QACf,CAAC,MAAM;UACL,OAAOA,EAAE,CAAC,CAAC;QACb;MACF,CAAC,MAAM,IAAIA,EAAE,KAAKG,SAAS,EAAE;QAC3B,OAAO,GAAG;MACZ,CAAC,MAAM,IAAI,OAAOH,EAAE,KAAK,QAAQ,IAAIA,EAAE,KAAK,IAAI,EAAE;QAChD,MAAM,IAAIjB,KAAK,CAAC,sEAAsE,CAAC;MACzF,CAAC,MAAM;QAAE;QACP,WAAAM,MAAA,CAAWe,IAAI,CAACC,SAAS,CAACL,EAAE,CAAC;MAC/B;IACF,CAAC;IAED/B,OAAO,CAACqC,OAAO,GAAIN,EAAE,IAAK;MACxB,IAAIC,SAAS,GAAGD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;MAC5B,IAAIF,EAAE,KAAK,EAAE,EAAE;QACb,OAAOA,EAAE;MACX,CAAC,MAAM,IAAIA,EAAE,KAAK,GAAG,EAAE;QACrB,OAAOG,SAAS;MAClB,CAAC,MAAM,IAAIF,SAAS,KAAK,GAAG,EAAE;QAC5B,OAAOD,EAAE,CAACL,MAAM,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM,IAAIM,SAAS,KAAK,GAAG,EAAE;QAC5B,OAAOG,IAAI,CAACG,KAAK,CAACP,EAAE,CAACL,MAAM,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,MAAM,IAAI1B,OAAO,CAACM,kBAAkB,CAACyB,EAAE,CAAC,EAAE;QACzC,OAAO,IAAI/B,OAAO,CAACU,QAAQ,CAACqB,EAAE,CAAC;MACjC,CAAC,MAAM;QACL,OAAOA,EAAE;MACX;IACF,CAAC;IAACQ,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/mongo-id.js", "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON> } from 'meteor/ejson';\nimport { Random } from 'meteor/random';\n\nconst MongoID = {};\n\nMongoID._looksLikeObjectID = str => str.length === 24 && /^[0-9a-f]*$/.test(str);\n\nMongoID.ObjectID = class ObjectID {\n  constructor (hexString) {\n    //random-based impl of Mongo ObjectID\n    if (hexString) {\n      hexString = hexString.toLowerCase();\n      if (!MongoID._looksLikeObjectID(hexString)) {\n        throw new Error('Invalid hexadecimal string for creating an ObjectID');\n      }\n      // meant to work with _.isEqual(), which relies on structural equality\n      this._str = hexString;\n    } else {\n      this._str = Random.hexString(24);\n    }\n  }\n\n  equals(other) {\n    return other instanceof MongoID.ObjectID &&\n    this.valueOf() === other.valueOf();\n  }\n\n  toString() {\n    return `ObjectID(\"${this._str}\")`;\n  }\n\n  clone() {\n    return new MongoID.ObjectID(this._str);\n  }\n\n  typeName() {\n    return 'oid';\n  }\n\n  getTimestamp() {\n    return Number.parseInt(this._str.substr(0, 8), 16);\n  }\n\n  valueOf() {\n    return this._str;\n  }\n\n  toJSONValue() {\n    return this.valueOf();\n  }\n\n  toHexString() {\n    return this.valueOf();\n  }\n\n}\n\nEJSON.addType('oid', str => new MongoID.ObjectID(str));\n\nMongoID.idStringify = (id) => {\n  if (id instanceof MongoID.ObjectID) {\n    return id.valueOf();\n  } else if (typeof id === 'string') {\n    var firstChar = id.charAt(0);\n    if (id === '') {\n      return id;\n    } else if (firstChar === '-' || // escape previously dashed strings\n               firstChar === '~' || // escape escaped numbers, true, false\n               MongoID._looksLikeObjectID(id) || // escape object-id-form strings\n               firstChar === '{') { // escape object-form strings, for maybe implementing later\n      return `-${id}`;\n    } else {\n      return id; // other strings go through unchanged.\n    }\n  } else if (id === undefined) {\n    return '-';\n  } else if (typeof id === 'object' && id !== null) {\n    throw new Error('Meteor does not currently support objects other than ObjectID as ids');\n  } else { // Numbers, true, false, null\n    return `~${JSON.stringify(id)}`;\n  }\n};\n\nMongoID.idParse = (id) => {\n  var firstChar = id.charAt(0);\n  if (id === '') {\n    return id;\n  } else if (id === '-') {\n    return undefined;\n  } else if (firstChar === '-') {\n    return id.substr(1);\n  } else if (firstChar === '~') {\n    return JSON.parse(id.substr(1));\n  } else if (MongoID._looksLikeObjectID(id)) {\n    return new MongoID.ObjectID(id);\n  } else {\n    return id;\n  }\n};\n\nexport { MongoID };\n"]}