import React, { useState, useEffect } from 'react';
import { Meteor } from 'meteor/meteor';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useTracker } from 'meteor/react-meteor-data';
import { Tasks } from '/imports/api/tasks';
import { TeamTaskView } from '../components/TeamTaskView';

export const TeamTasksPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { taskId } = useParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');

  const { tasks, isLoading, user, teamMembers } = useTracker(() => {
    const tasksHandle = Meteor.subscribe('tasks');
    const userHandle = Meteor.subscribe('userData');
    const teamMembersHandle = Meteor.subscribe('teamMembers');
    const user = Meteor.user();
    const tasks = Tasks.find(
      { assignedTo: user?._id },
      { sort: { createdAt: -1 } }
    ).fetch();
    const teamMembers = Meteor.users.find({}, {
      fields: {
        _id: 1,
        'profile.firstName': 1,
        'profile.lastName': 1,
        'profile.email': 1
      }
    }).fetch();
    return {
      tasks,
      user,
      teamMembers,
      isLoading: !tasksHandle.ready() || !userHandle.ready() || !teamMembersHandle.ready()
    };
  });

  const userNames = {};
  teamMembers?.forEach(member => {
    const firstName = member.profile?.firstName || '';
    const lastName = member.profile?.lastName || '';
    userNames[member._id] = `${firstName} ${lastName}`.trim() || member.profile?.email?.split('@')[0] || 'Unknown';
  });

  const currentTask = taskId ? tasks.find(t => t._id === taskId) : null;

  if (currentTask) {
    return (
      <TeamTaskView
        task={currentTask}
        onCancel={() => navigate('/team-dashboard/tasks')}
      />
    );
  }

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <div style={{ padding: '24px', background: '#ffffff', minHeight: 'calc(100vh - 64px)' }}>
      <div style={{ backgroundColor: '#ffffff', borderRadius: '16px', padding: '24px', boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <h2 style={{ color: '#0f172a', margin: 0, fontSize: '1.5rem', fontWeight: '600' }}>My Tasks</h2>
        </div>

        <div style={{ display: 'flex', gap: '16px', marginBottom: '24px', flexWrap: 'wrap' }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: '100%', padding: '8px 12px', borderRadius: '6px', border: '1px solid #e2e8f0', fontSize: '0.875rem' }}
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            style={{ padding: '8px 12px', borderRadius: '6px', border: '1px solid #e2e8f0', fontSize: '0.875rem', minWidth: '150px' }}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
          </select>
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            style={{ padding: '8px 12px', borderRadius: '6px', border: '1px solid #e2e8f0', fontSize: '0.875rem', minWidth: '150px' }}
          >
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>

        {isLoading ? (
          <div style={{ textAlign: 'center', padding: '24px' }}>
            <div className="loading-spinner"></div>
          </div>
        ) : filteredTasks.length > 0 ? (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: '24px', padding: '16px 0' }}>
            {filteredTasks.map(task => {
              const completed = task.checklist?.filter(item => item.completed).length || 0;
              const total = task.checklist?.length || 0;
              const progress = total > 0 ? Math.round((completed / total) * 100) : 0;
              return (
                <div
                  key={task._id}
                  onClick={() => navigate(`/team-dashboard/tasks/${task._id}`)}
                  style={{ backgroundColor: '#ffffff', border: '1px solid #e2e8f0', borderRadius: '8px', padding: '16px', cursor: 'pointer' }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                    <div>
                      <h3 style={{ margin: 0, fontSize: '1.1rem', fontWeight: '600', color: '#0f172a' }}>{task.title}</h3>
                      <p style={{ color: '#64748b', fontSize: '0.875rem', marginBottom: '12px', display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden', textOverflow: 'ellipsis', maxHeight: '2.6em' }}>{task.description}</p>
                    </div>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px', flexWrap: 'wrap' }}>
                    <span style={{ padding: '4px 8px', borderRadius: '6px', fontSize: '0.75rem', fontWeight: '500', backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef3c7' : '#dcfce7', color: task.priority === 'high' ? '#dc2626' : task.priority === 'medium' ? '#d97706' : '#16a34a' }}>{task.priority}</span>
                    <span style={{ padding: '4px 8px', borderRadius: '6px', fontSize: '0.75rem', fontWeight: '500', backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in-progress' ? '#fef3c7' : '#f1f5f9', color: task.status === 'completed' ? '#16a34a' : task.status === 'in-progress' ? '#d97706' : '#475569' }}>{task.status}</span>
                    <span style={{ fontSize: '0.85rem', color: '#64748b', display: 'flex', alignItems: 'center', gap: '4px' }}>
                      <span>Due:</span>
                      <span style={{ color: '#0f172a' }}>{new Date(task.dueDate).toLocaleDateString()}</span>
                    </span>
                    {total > 0 && (
                      <span style={{ fontSize: '0.85rem', color: '#64748b', display: 'flex', alignItems: 'center', gap: '4px' }}>
                        <span>Progress:</span>
                        <span style={{ color: '#0f172a' }}>{progress}%</span>
                      </span>
                    )}
                  </div>

                  {task.assignedTo && task.assignedTo.length > 0 && (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px', flexWrap: 'wrap' }}>
                      <span style={{ fontSize: '0.85rem', color: '#64748b' }}>Team:</span>
                      {task.assignedTo.slice(0, 3).map(userId => (
                        <span key={userId} style={{ fontSize: '0.85rem', padding: '2px 8px', borderRadius: '4px', backgroundColor: '#f1f5f9', color: '#475569', fontWeight: 500 }}>{userNames[userId] || 'Unknown'}</span>
                      ))}
                      {task.assignedTo.length > 3 && (
                        <span style={{ fontSize: '0.85rem', color: '#64748b' }}>+{task.assignedTo.length - 3} more</span>
                      )}
                    </div>
                  )}

                  {task.checklist && task.checklist.length > 0 && (
                    <div style={{ marginTop: '8px', padding: '8px', backgroundColor: '#f8fafc', borderRadius: '8px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', fontSize: '0.875rem', color: '#64748b', marginBottom: '8px' }}>
                        <span>Checklist Progress:</span>
                        <span style={{ color: '#0f172a', fontWeight: '500' }}>{completed}/{total}</span>
                      </div>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                        {task.checklist.slice(0, 3).map((item, index) => (
                          <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '0.75rem', color: '#64748b' }}>
                            <input type="checkbox" checked={item.completed} disabled />
                            <span style={{ textDecoration: item.completed ? 'line-through' : 'none' }}>{item.text}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#64748b', padding: '24px' }}>No tasks found.</div>
        )}
      </div>
    </div>
  );
};
