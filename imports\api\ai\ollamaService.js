// Initialize Ollama client
let ollamaEndpoint = Meteor.settings.private?.ollama?.endpoint || 'http://localhost:11434';
let defaultModel = Meteor.settings.private?.ollama?.model || 'tinyllama:1.1b';  // Changed to tinyllama:1.1b (extremely small model)

// Helper function to handle API errors
const handleApiError = (error, context) => {
  console.error(`Error in ${context}:`, error);
  
  if (error.code === 'ECONNREFUSED') {
    throw new Meteor.Error('connection-failed', 'Could not connect to Ollama. Please make sure Ollama is running on your machine.');
  }
  
  throw new Meteor.Error('ai-service-error', `AI service error: ${error.message}`);
};

// Helper function to call Ollama API
async function callOllama(prompt) {
  try {
    const response = await fetch(`${ollamaEndpoint}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: defaultModel,
        prompt: prompt,
        stream: false,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Meteor.Error('ai-service-error', `AI service error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    return data.response;
  } catch (error) {
    handleApiError(error, 'Ollama API call');
  }
}

export const OllamaService = {
  callOllama,
  async analyzeTask(task) {
    // Create a simplified prompt with minimal data
    const prompt = `Analyze this task: ${task.title || 'Untitled task'}. ${task.description || ''}`;
    console.log('Ollama prompt (analyzeTask):', prompt);
    return await callOllama(prompt);
  },

  async suggestTaskOptimization(taskId) {
    const task = await Tasks.findOneAsync(taskId);
    if (!task) {
      throw new Meteor.Error('task-not-found', 'Task not found');
    }
    const prompt = `Suggest improvements for this task: ${task.title || 'Untitled task'}. ${task.description || ''}`;
    console.log('Ollama prompt (suggestTaskOptimization):', prompt);
    return await callOllama(prompt);
  },

  async predictTaskCompletion(taskId) {
    const task = await Tasks.findOneAsync(taskId);
    if (!task) {
      throw new Meteor.Error('task-not-found', 'Task not found');
    }
    
    const prompt = `Predict completion time for: ${task.title || 'Untitled task'}. ${task.description || ''}`;
    console.log('Ollama prompt (predictTaskCompletion):', prompt);
    return await callOllama(prompt);
  },

  async generateTaskSummary(taskId) {
    const task = await Tasks.findOneAsync(taskId);
    if (!task) {
      throw new Meteor.Error('task-not-found', 'Task not found');
    }
    const prompt = `Summarize this task: ${task.title || 'Untitled task'}. ${task.description || ''}`;
    console.log('Ollama prompt (generateTaskSummary):', prompt);
    return await callOllama(prompt);
  },

  async suggestTaskDivision(task) {
    const prompt = `Suggest how to divide this task among team members: ${task.title || 'Untitled task'}. ${task.description || ''}`;
    console.log('Ollama prompt (suggestTaskDivision):', prompt);
    return await callOllama(prompt);
  },

  async recommendTools(taskId) {
    const task = await Tasks.findOneAsync(taskId);
    if (!task) {
      throw new Meteor.Error('task-not-found', 'Task not found');
    }
    const prompt = `Recommend tools and techniques for this task: ${task.title || 'Untitled task'}. ${task.description || ''}`;
    console.log('Ollama prompt (recommendTools):', prompt);
    return await callOllama(prompt);
  },

  async predictDailyProgress(taskId) {
    const task = await Tasks.findOneAsync(taskId);
    if (!task) {
      throw new Meteor.Error('task-not-found', 'Task not found');
    }
    
    const prompt = `Predict daily progress needed to complete by deadline for: ${task.title || 'Untitled task'}. ${task.description || ''}. Due date: ${task.dueDate || 'Not specified'}`;
    console.log('Ollama prompt (predictDailyProgress):', prompt);
    return await callOllama(prompt);
  }
}; 
