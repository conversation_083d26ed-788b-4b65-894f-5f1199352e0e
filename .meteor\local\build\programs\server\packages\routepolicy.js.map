{"version": 3, "sources": ["meteor://💻app/packages/routepolicy/main.js", "meteor://💻app/packages/routepolicy/routepolicy.js"], "names": ["module", "export", "RoutePolicy", "RoutePolicyConstructor", "link", "default", "v", "__reifyWaitForDeps__", "__reify_async_result__", "_reifyError", "self", "async", "constructor", "urlPrefixTypes", "urlPrefixMatches", "urlPrefix", "url", "startsWith", "checkType", "type", "includes", "checkUrlPrefix", "existingType", "concat", "checkForConflictWithStatic", "_testManifest", "policy", "check", "manifest", "conflict", "find", "resource", "where", "WebApp", "require", "errorMessage", "Object", "keys", "clientPrograms", "some", "arch", "declare", "problem", "Error", "isValidUrl", "classify", "prefix", "urlPrefixesFor", "entries", "filter", "_ref", "_prefix", "_type", "map", "_ref2", "sort"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,WAAW,EAACA,CAAA,KAAIA;IAAW,CAAC,CAAC;IAAC,IAAIC,sBAAsB;IAACH,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACH,sBAAsB,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IACnM,MAAML,WAAW,GAAG,IAAIC,sBAAsB,CAAC,CAAC;IAACK,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;ACDxDX,MAAM,CAACC,MAAM,CAAC;EAACI,OAAO,EAACA,CAAA,KAAIH;AAAW,CAAC,CAAC;AAsBzB,MAAMA,WAAW,CAAC;EAC/BU,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;EAC1B;EAEAC,gBAAgBA,CAACC,SAAS,EAAEC,GAAG,EAAE;IAC/B,OAAOA,GAAG,CAACC,UAAU,CAACF,SAAS,CAAC;EAClC;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,CAAC,CAACC,QAAQ,CAACD,IAAI,CAAC,EAAE;MAChD,OAAO,qDAAqD;IAC9D;IACA,OAAO,IAAI;EACb;EAEAE,cAAcA,CAACN,SAAS,EAAEI,IAAI,EAAE;IAC9B,IAAI,CAACJ,SAAS,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;MAC9B,OAAO,4CAA4C;IACrD;IAEA,IAAIF,SAAS,KAAK,GAAG,EAAE;MACrB,OAAO,gCAAgC;IACzC;IAEA,MAAMO,YAAY,GAAG,IAAI,CAACT,cAAc,CAACE,SAAS,CAAC;IACnD,IAAIO,YAAY,IAAIA,YAAY,KAAKH,IAAI,EAAE;MACzC,OAAO,wBAAAI,MAAA,CAAwBR,SAAS,oDAAAQ,MAAA,CACrBD,YAAY,CAAE;IACnC;IAEA,OAAO,IAAI;EACb;EAEAE,0BAA0BA,CAACT,SAAS,EAAEI,IAAI,EAAEM,aAAa,EAAE;IACzD,IAAIN,IAAI,KAAK,eAAe,EAAE;MAC5B,OAAO,IAAI;IACb;IAEA,MAAMO,MAAM,GAAG,IAAI;IAEnB,SAASC,KAAKA,CAACC,QAAQ,EAAE;MACvB,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,IAAI,CAACC,QAAQ,IACrCA,QAAQ,CAACZ,IAAI,KAAK,QAAQ,IAC1BY,QAAQ,CAACC,KAAK,KAAK,QAAQ,IAC3BN,MAAM,CAACZ,gBAAgB,CAACC,SAAS,EAAEgB,QAAQ,CAACf,GAAG,CAChD,CAAC;MAEF,IAAIa,QAAQ,EAAE;QACZ,OAAO,mBAAAN,MAAA,CAAmBM,QAAQ,CAACb,GAAG,sBAAAO,MAAA,CAAmBJ,IAAI,kBAAAI,MAAA,CAClDR,SAAS,CAAE;MACxB;MAEA,OAAO,IAAI;IACb;IAAC;IAED,IAAIU,aAAa,EAAE;MACjB,OAAOE,KAAK,CAACF,aAAa,CAAC;IAC7B;IAEA,MAAM;MAAEQ;IAAO,CAAC,GAAGC,OAAO,CAAC,eAAe,CAAC;IAC3C,IAAIC,YAAY,GAAG,IAAI;IAEvBC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAACK,cAAc,CAAC,CAACC,IAAI,CAACC,IAAI,IAAI;MAC9C,MAAM;QAAEZ;MAAS,CAAC,GAAGK,MAAM,CAACK,cAAc,CAACE,IAAI,CAAC;MAChD,OAAOL,YAAY,GAAGR,KAAK,CAACC,QAAQ,CAAC;IACvC,CAAC,CAAC;IAEF,OAAOO,YAAY;EACrB;EAEAM,OAAOA,CAAC1B,SAAS,EAAEI,IAAI,EAAE;IACvB,MAAMuB,OAAO,GACX,IAAI,CAACxB,SAAS,CAACC,IAAI,CAAC,IACpB,IAAI,CAACE,cAAc,CAACN,SAAS,EAAEI,IAAI,CAAC,IACpC,IAAI,CAACK,0BAA0B,CAACT,SAAS,EAAEI,IAAI,CAAC;IAClD,IAAIuB,OAAO,EAAE;MACX,MAAM,IAAIC,KAAK,CAACD,OAAO,CAAC;IAC1B;IACA;IACA,IAAI,CAAC7B,cAAc,CAACE,SAAS,CAAC,GAAGI,IAAI;EACvC;EAEAyB,UAAUA,CAAC5B,GAAG,EAAE;IACd,OAAOA,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC;EAC5B;EAEA4B,QAAQA,CAAC7B,GAAG,EAAE;IACZ,IAAI,CAAC,IAAI,CAAC4B,UAAU,CAAC5B,GAAG,CAAC,EAAE;MACzB,MAAM,IAAI2B,KAAK,gCAAApB,MAAA,CAAgCP,GAAG,CAAE,CAAC;IACvD;IAEA,MAAM8B,MAAM,GAAGV,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxB,cAAc,CAAC,CAACiB,IAAI,CAACgB,MAAM,IACzD,IAAI,CAAChC,gBAAgB,CAACgC,MAAM,EAAE9B,GAAG,CACnC,CAAC;IAED,OAAO8B,MAAM,GAAG,IAAI,CAACjC,cAAc,CAACiC,MAAM,CAAC,GAAG,IAAI;EACpD;EAEAC,cAAcA,CAAC5B,IAAI,EAAE;IACnB,OAAOiB,MAAM,CAACY,OAAO,CAAC,IAAI,CAACnC,cAAc,CAAC,CACvCoC,MAAM,CAACC,IAAA;MAAA,IAAC,CAACC,OAAO,EAAEC,KAAK,CAAC,GAAAF,IAAA;MAAA,OAAKE,KAAK,KAAKjC,IAAI;IAAA,EAAC,CAC5CkC,GAAG,CAACC,KAAA;MAAA,IAAC,CAACH,OAAO,CAAC,GAAAG,KAAA;MAAA,OAAKH,OAAO;IAAA,EAAC,CAC3BI,IAAI,CAAC,CAAC;EACX;AACF,C", "file": "/packages/routepolicy.js", "sourcesContent": ["import { default as RoutePolicyConstructor } from './routepolicy';\nexport const RoutePolicy = new RoutePolicyConstructor();\n", "// In addition to listing specific files to be cached, the browser\n// application cache manifest allows URLs to be designated as NETWORK\n// (always fetched from the Internet) and FALLBACK (which we use to\n// serve app HTML on arbitrary URLs).\n//\n// The limitation of the manifest file format is that the designations\n// are by prefix only: if \"/foo\" is declared NETWORK then \"/foobar\"\n// will also be treated as a network route.\n//\n// RoutePolicy is a low-level API for declaring the route type of URL prefixes:\n//\n// \"network\": for network routes that should not conflict with static\n// resources.  (For example, if \"/sockjs/\" is a network route, we\n// shouldn't have \"/sockjs/red-sock.jpg\" as a static resource).\n//\n// \"static-online\": for static resources which should not be cached in\n// the app cache.  This is implemented by also adding them to the\n// NETWORK section (as otherwise the browser would receive app HTML\n// for them because of the FALLBACK section), but static-online routes\n// don't need to be checked for conflict with static resources.\n\n\nexport default class RoutePolicy {\n  constructor() {\n    // maps prefix to a type\n    this.urlPrefixTypes = {};\n  }\n\n  urlPrefixMatches(urlPrefix, url) {\n    return url.startsWith(urlPrefix);\n  }\n\n  checkType(type) {\n    if (!['network', 'static-online'].includes(type)) {\n      return 'the route type must be \"network\" or \"static-online\"';\n    }\n    return null;\n  }\n\n  checkUrlPrefix(urlPrefix, type) {\n    if (!urlPrefix.startsWith('/')) {\n      return 'a route URL prefix must begin with a slash';\n    }\n\n    if (urlPrefix === '/') {\n      return 'a route URL prefix cannot be /';\n    }\n\n    const existingType = this.urlPrefixTypes[urlPrefix];\n    if (existingType && existingType !== type) {\n      return `the route URL prefix ${urlPrefix} has already been declared ` +\n        `to be of type ${existingType}`;\n    }\n\n    return null;\n  }\n\n  checkForConflictWithStatic(urlPrefix, type, _testManifest) {\n    if (type === 'static-online') {\n      return null;\n    }\n\n    const policy = this;\n\n    function check(manifest) {\n      const conflict = manifest.find(resource => (\n        resource.type === 'static' &&\n        resource.where === 'client' &&\n        policy.urlPrefixMatches(urlPrefix, resource.url)\n      ));\n\n      if (conflict) {\n        return `static resource ${conflict.url} conflicts with ${type} ` +\n          `route ${urlPrefix}`;\n      }\n\n      return null;\n    };\n\n    if (_testManifest) {\n      return check(_testManifest);\n    }\n\n    const { WebApp } = require(\"meteor/webapp\");\n    let errorMessage = null;\n\n    Object.keys(WebApp.clientPrograms).some(arch => {\n      const { manifest } = WebApp.clientPrograms[arch];\n      return errorMessage = check(manifest);\n    });\n\n    return errorMessage;\n  }\n\n  declare(urlPrefix, type) {\n    const problem =\n      this.checkType(type) ||\n      this.checkUrlPrefix(urlPrefix, type) ||\n      this.checkForConflictWithStatic(urlPrefix, type);\n    if (problem) {\n      throw new Error(problem);\n    }\n    // TODO overlapping prefixes, e.g. /foo/ and /foo/bar/\n    this.urlPrefixTypes[urlPrefix] = type;\n  }\n\n  isValidUrl(url) {\n    return url.startsWith('/');\n  }\n\n  classify(url) {\n    if (!this.isValidUrl(url)) {\n      throw new Error(`url must be a relative URL: ${url}`);\n    }\n\n    const prefix = Object.keys(this.urlPrefixTypes).find(prefix =>\n      this.urlPrefixMatches(prefix, url)\n    );\n\n    return prefix ? this.urlPrefixTypes[prefix] : null;\n  }\n\n  urlPrefixesFor(type) {\n    return Object.entries(this.urlPrefixTypes)\n      .filter(([_prefix, _type]) => _type === type)\n      .map(([_prefix]) => _prefix)\n      .sort();\n  }\n}\n"]}