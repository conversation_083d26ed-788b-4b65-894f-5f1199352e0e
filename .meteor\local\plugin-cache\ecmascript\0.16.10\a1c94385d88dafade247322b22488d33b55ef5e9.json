{"metadata": {}, "options": {"assumptions": {}, "compact": false, "sourceMaps": true, "ast": true, "babelrc": false, "configFile": false, "parserOpts": {"sourceType": "module", "sourceFileName": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "plugins": ["*", "flow", "jsx", "asyncGenerators", "bigInt", "classPrivateMethods", "classPrivateProperties", "classProperties", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportExtensions", "exportNamespaceFrom", "functionBind", "functionSent", "importMeta", "nullishCoalescingOperator", "numericSeparator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", ["pipelineOperator", {"proposal": "minimal"}], "throwExpressions", "topLevelAwait", "objectRestSpread", "objectRestSpread", "asyncGenerators", "classProperties", "classPrivateProperties", "jsx", "nullishCoalescingOperator", "nullishCoalescingOperator", "optionalChaining", "optionalChaining", "optionalCatchBinding", "optionalCatchBinding", "classProperties", "classPrivateProperties", "classPrivateMethods", "classProperties", "classPrivateProperties", "asyncGenerators", "asyncGenerators", "objectRestSpread", "objectRestSpread", "logicalAssignment"], "allowImportExportEverywhere": true, "allowReturnOutsideFunction": true, "allowUndeclaredExports": true, "strictMode": false}, "caller": {"name": "meteor", "arch": "os.windows.x86_64"}, "sourceFileName": "server/main.js", "filename": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "targets": {}, "cloneInputAst": true, "browserslistConfigFile": false, "passPerPreset": false, "envName": "development", "cwd": "C:\\Users\\<USER>\\smart-task-management-system", "root": "C:\\Users\\<USER>\\smart-task-management-system", "rootMode": "root", "plugins": [{"key": "base$0", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$0", "visitor": {"Program": {"enter": [null], "exit": [null]}, "_exploded": true, "_verified": true}, "options": {"avoidModernSyntax": false, "enforceStrictMode": false, "dynamicImport": true, "generateLetDeclarations": true, "topLevelAwait": true}, "externalDependencies": []}, {"key": "transform-runtime", "visitor": {"MemberExpression": {"enter": [null]}, "ObjectPattern": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "Identifier": {"enter": [null]}, "JSXIdentifier": {"enter": [null]}}, "options": {"version": "7.17.2", "helpers": true, "useESModules": false, "corejs": false}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-react-jsx", "visitor": {"JSXNamespacedName": {"enter": [null]}, "JSXSpreadChild": {"enter": [null]}, "Program": {"enter": [null]}, "JSXFragment": {"exit": [null]}, "JSXElement": {"exit": [null]}, "JSXAttribute": {"enter": [null]}}, "options": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "runtime": "classic", "throwIfNamespace": true, "useBuiltIns": false}, "externalDependencies": []}, {"key": "transform-react-display-name", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-pure-annotations", "visitor": {"CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "syntax-nullish-coalescing-operator", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-nullish-coalescing-operator", "visitor": {"LogicalExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-chaining", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-chaining", "visitor": {"OptionalCallExpression": {"enter": [null]}, "OptionalMemberExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-catch-binding", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-catch-binding", "visitor": {"CatchClause": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-class-properties", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-async-generators", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-logical-assignment-operators", "visitor": {"AssignmentExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-literals", "visitor": {"NumericLiteral": {"enter": [null]}, "StringLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-template-literals", "visitor": {"TaggedTemplateExpression": {"enter": [null]}, "TemplateLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-parameters", "visitor": {"_exploded": true, "_verified": true, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-exponentiation-operator", "visitor": {"AssignmentExpression": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}], "presets": [], "generatorOpts": {"filename": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "comments": true, "compact": false, "sourceMaps": true, "sourceFileName": "server/main.js"}}, "code": "!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {\n  \"use strict\";\n  try {\n    let _objectSpread;\n    module.link(\"@babel/runtime/helpers/objectSpread2\", {\n      default(v) {\n        _objectSpread = v;\n      }\n    }, 0);\n    let Meteor;\n    module.link(\"meteor/meteor\", {\n      Meteor(v) {\n        Meteor = v;\n      }\n    }, 0);\n    let LinksCollection;\n    module.link(\"/imports/api/links\", {\n      LinksCollection(v) {\n        LinksCollection = v;\n      }\n    }, 1);\n    let Accounts;\n    module.link(\"meteor/accounts-base\", {\n      Accounts(v) {\n        Accounts = v;\n      }\n    }, 2);\n    let Email;\n    module.link(\"meteor/email\", {\n      Email(v) {\n        Email = v;\n      }\n    }, 3);\n    let Tasks;\n    module.link(\"/imports/api/tasks\", {\n      Tasks(v) {\n        Tasks = v;\n      }\n    }, 4);\n    let Roles;\n    module.link(\"meteor/alanning:roles\", {\n      Roles(v) {\n        Roles = v;\n      }\n    }, 5);\n    let check;\n    module.link(\"meteor/check\", {\n      check(v) {\n        check = v;\n      }\n    }, 6);\n    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();\n    async function insertLink(_ref) {\n      let {\n        title,\n        url\n      } = _ref;\n      await LinksCollection.insertAsync({\n        title,\n        url,\n        createdAt: new Date()\n      });\n    }\n    const ADMIN_TOKEN = '123456';\n    Meteor.startup(async () => {\n      var _Meteor$settings$priv;\n      // Ensure indexes for Tasks collection\n      try {\n        await Tasks.createIndex({\n          createdAt: 1\n        });\n        await Tasks.createIndex({\n          assignedTo: 1\n        });\n        await Tasks.createIndex({\n          createdBy: 1\n        });\n\n        // Ensure indexes for Users collection\n        // Note: emails.address index is already created by Meteor accounts system\n        await Meteor.users.createIndex({\n          roles: 1\n        }, {\n          background: true\n        });\n      } catch (error) {\n        console.warn('[Startup] Index creation warning:', error.message);\n      }\n\n      // Check if we have any team members\n      try {\n        const allUsers = await Meteor.users.find().fetchAsync();\n        console.log('[Startup] All users:', allUsers.map(user => {\n          var _user$emails, _user$emails$;\n          return {\n            id: user._id,\n            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,\n            roles: user.roles,\n            profile: user.profile\n          };\n        }));\n\n        // First, ensure all users have roles and createdAt\n        for (const user of allUsers) {\n          const updates = {};\n          if (!user.roles || !Array.isArray(user.roles)) {\n            updates.roles = ['team-member'];\n          }\n          if (!user.createdAt) {\n            updates.createdAt = new Date();\n          }\n          if (Object.keys(updates).length > 0) {\n            var _user$emails2, _user$emails2$;\n            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);\n            await Meteor.users.updateAsync(user._id, {\n              $set: updates\n            });\n          }\n        }\n        const teamMembersCount = await Meteor.users.find({\n          'roles': 'team-member'\n        }).countAsync();\n        console.log('[Startup] Found team members:', teamMembersCount);\n\n        // Create test team members if none exist\n        if (teamMembersCount === 0) {\n          console.log('[Startup] Creating test team members');\n          try {\n            // Create multiple test team members\n            const testMembers = [{\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'John',\n              lastName: 'Doe'\n            }, {\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'Jane',\n              lastName: 'Smith'\n            }];\n            for (const member of testMembers) {\n              const userId = await Accounts.createUserAsync({\n                email: member.email,\n                password: member.password,\n                role: 'team-member',\n                createdAt: new Date(),\n                profile: {\n                  firstName: member.firstName,\n                  lastName: member.lastName,\n                  role: 'team-member',\n                  fullName: \"\".concat(member.firstName, \" \").concat(member.lastName)\n                }\n              });\n\n              // Set the role explicitly\n              await Meteor.users.updateAsync(userId, {\n                $set: {\n                  roles: ['team-member']\n                }\n              });\n              console.log('[Startup] Created test team member:', {\n                id: userId,\n                email: member.email,\n                name: \"\".concat(member.firstName, \" \").concat(member.lastName)\n              });\n            }\n          } catch (error) {\n            console.error('[Startup] Error creating test team members:', error);\n          }\n        }\n      } catch (error) {\n        console.error('[Startup] Error checking team members:', error);\n      }\n\n      // Email configuration from settings\n      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;\n\n      // Configure email SMTP\n      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {\n        process.env.MAIL_URL = \"smtp://\".concat(encodeURIComponent(emailSettings.username), \":\").concat(encodeURIComponent(emailSettings.password), \"@\").concat(emailSettings.server, \":\").concat(emailSettings.port);\n\n        // Test email configuration\n        try {\n          console.log('Testing email configuration...');\n          Email.send({\n            to: emailSettings.username,\n            from: emailSettings.username,\n            subject: 'Test Email',\n            text: 'If you receive this email, your email configuration is working correctly.'\n          });\n          console.log('Test email sent successfully!');\n        } catch (error) {\n          console.error('Error sending test email:', error);\n        }\n      } else {\n        console.warn('Email configuration is missing in settings.json');\n      }\n\n      // Configure account creation to require email verification\n      Accounts.config({\n        sendVerificationEmail: true,\n        forbidClientAccountCreation: false\n      });\n\n      // Customize verification email\n      Accounts.emailTemplates.siteName = \"Task Management System\";\n      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? \"Task Management System <\".concat(emailSettings.username, \">\") : \"Task Management System <<EMAIL>>\";\n      Accounts.emailTemplates.verifyEmail = {\n        subject() {\n          return \"Verify Your Email Address\";\n        },\n        text(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"Hello,\\n\\n\" + \"To verify your email address (\".concat(emailAddress, \"), please click the link below:\\n\\n\") + \"\".concat(url, \"\\n\\n\") + \"If you did not request this verification, please ignore this email.\\n\\n\" + \"Thanks,\\n\" + \"Your Task Management System Team\";\n        },\n        html(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"\\n        <html>\\n          <body style=\\\"font-family: Arial, sans-serif; padding: 20px; color: #333;\\\">\\n            <h2 style=\\\"color: #00875a;\\\">Verify Your Email Address</h2>\\n            <p>Hello,</p>\\n            <p>To verify your email address (\".concat(emailAddress, \"), please click the button below:</p>\\n            <p style=\\\"margin: 20px 0;\\\">\\n              <a href=\\\"\").concat(url, \"\\\" \\n                 style=\\\"background-color: #00875a; \\n                        color: white; \\n                        padding: 12px 25px; \\n                        text-decoration: none; \\n                        border-radius: 4px;\\n                        display: inline-block;\\\">\\n                Verify Email Address\\n              </a>\\n            </p>\\n            <p>If you did not request this verification, please ignore this email.</p>\\n            <p>Thanks,<br>Your Task Management System Team</p>\\n          </body>\\n        </html>\\n      \");\n        }\n      };\n\n      // If the Links collection is empty, add some data.\n      if ((await LinksCollection.find().countAsync()) === 0) {\n        await insertLink({\n          title: 'Do the Tutorial',\n          url: 'https://www.meteor.com/tutorials/react/creating-an-app'\n        });\n        await insertLink({\n          title: 'Follow the Guide',\n          url: 'https://guide.meteor.com'\n        });\n        await insertLink({\n          title: 'Read the Docs',\n          url: 'https://docs.meteor.com'\n        });\n        await insertLink({\n          title: 'Discussions',\n          url: 'https://forums.meteor.com'\n        });\n      }\n\n      // We publish the entire Links collection to all clients.\n      // In order to be fetched in real-time to the clients\n      Meteor.publish(\"links\", function () {\n        return LinksCollection.find();\n      });\n\n      // Add custom fields to users\n      Accounts.onCreateUser((options, user) => {\n        var _customizedUser$email, _customizedUser$email2;\n        console.log('[onCreateUser] Creating user with options:', {\n          email: options.email,\n          role: options.role,\n          profile: options.profile,\n          createdAt: options.createdAt\n        });\n        const customizedUser = _objectSpread({}, user);\n\n        // Ensure we have a profile\n        customizedUser.profile = options.profile || {};\n\n        // Add role from options\n        const role = options.role || 'team-member';\n        customizedUser.roles = [role];\n\n        // Set createdAt if provided, otherwise use current date\n        customizedUser.createdAt = options.createdAt || new Date();\n        console.log('[onCreateUser] Created user:', {\n          id: customizedUser._id,\n          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,\n          roles: customizedUser.roles,\n          profile: customizedUser.profile,\n          createdAt: customizedUser.createdAt\n        });\n        return customizedUser;\n      });\n\n      // Publish team members\n      Meteor.publish('teamMembers', function () {\n        console.log('[teamMembers] Publication called, userId:', this.userId);\n        if (!this.userId) {\n          console.log('[teamMembers] No userId, returning ready');\n          return this.ready();\n        }\n        try {\n          // Simple query to find all team members\n          const teamMembers = Meteor.users.find({\n            $or: [{\n              'roles': 'team-member'\n            }, {\n              'profile.role': 'team-member'\n            }]\n          }, {\n            fields: {\n              emails: 1,\n              roles: 1,\n              'profile.firstName': 1,\n              'profile.lastName': 1,\n              'profile.fullName': 1,\n              createdAt: 1\n            }\n          });\n          console.log('[teamMembers] Publishing team members');\n          return teamMembers;\n        } catch (error) {\n          console.error('[teamMembers] Error in publication:', error);\n          return this.ready();\n        }\n      });\n\n      // Publish user data with roles\n      Meteor.publish('userData', function () {\n        if (!this.userId) {\n          return this.ready();\n        }\n        console.log('[userData] Publishing data for user:', this.userId);\n        return Meteor.users.find({\n          _id: this.userId\n        }, {\n          fields: {\n            roles: 1,\n            emails: 1,\n            profile: 1\n          }\n        });\n      });\n    });\n\n    // Method to create a new user with role\n    Meteor.methods({\n      'users.create'(_ref2) {\n        let {\n          email,\n          password,\n          role,\n          adminToken,\n          firstName,\n          lastName\n        } = _ref2;\n        // Validate admin token if trying to create admin account\n        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n        }\n\n        // Validate password requirements\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(password)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(password)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(password)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(password)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Create the user\n        try {\n          const userId = Accounts.createUser({\n            email,\n            password,\n            role,\n            // This will be used in onCreateUser callback\n            profile: {\n              role,\n              // Store in profile as well for easy access\n              firstName,\n              lastName,\n              fullName: \"\".concat(firstName, \" \").concat(lastName)\n            }\n          });\n\n          // Send verification email\n          if (userId) {\n            Accounts.sendVerificationEmail(userId);\n          }\n          return userId;\n        } catch (error) {\n          throw new Meteor.Error('create-user-failed', error.message);\n        }\n      },\n      async 'users.getRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'User must be logged in');\n        }\n        try {\n          var _user$roles, _user$profile;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          if (!user) {\n            throw new Meteor.Error('user-not-found', 'User not found');\n          }\n\n          // Check both roles array and profile for role\n          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';\n          return role;\n        } catch (error) {\n          throw new Meteor.Error('get-role-failed', error.message);\n        }\n      },\n      'users.resendVerificationEmail'(email) {\n        // Find user by email\n        const user = Accounts.findUserByEmail(email);\n        if (!user) {\n          throw new Meteor.Error('user-not-found', 'No user found with this email address');\n        }\n\n        // Check if email is already verified\n        const userEmail = user.emails[0];\n        if (userEmail.verified) {\n          throw new Meteor.Error('already-verified', 'This email is already verified');\n        }\n\n        // Send verification email\n        try {\n          Accounts.sendVerificationEmail(user._id, email);\n          return true;\n        } catch (error) {\n          throw new Meteor.Error('verification-email-failed', error.message);\n        }\n      },\n      'users.forgotPassword'(_ref3) {\n        let {\n          email,\n          newPassword\n        } = _ref3;\n        check(email, String);\n        check(newPassword, String);\n\n        // Find user by email\n        const user = Accounts.findUserByEmail(email);\n        if (!user) {\n          throw new Meteor.Error('user-not-found', 'No user found with this email address');\n        }\n\n        // Validate password requirements (same as signup)\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(newPassword)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Update the user's password using user ID\n        try {\n          Accounts.setPassword(user._id, newPassword);\n          console.log(\"Password reset for user ID: \".concat(user._id, \", email: \").concat(email));\n          return {\n            success: true,\n            message: 'Password updated successfully'\n          };\n        } catch (error) {\n          console.error('Error updating password:', error);\n          throw new Meteor.Error('password-update-failed', 'Failed to update password. Please try again.');\n        }\n      },\n      async 'users.checkAndFixAdminRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _user$emails3, _user$emails3$;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          console.log('[checkAndFixAdminRole] Checking user:', {\n            id: user === null || user === void 0 ? void 0 : user._id,\n            email: user === null || user === void 0 ? void 0 : (_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.address,\n            roles: user === null || user === void 0 ? void 0 : user.roles\n          });\n\n          // If user has no roles array, initialize it\n          if (!user.roles) {\n            await Meteor.users.updateAsync(this.userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return 'Roles initialized';\n          }\n\n          // If user has no roles or doesn't have admin role\n          if (!user.roles.includes('admin')) {\n            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n\n            // Check if this is the first user (they should be admin)\n            const totalUsers = await Meteor.users.find().countAsync();\n            if (totalUsers === 1) {\n              console.log('[checkAndFixAdminRole] First user, setting as admin');\n              await Meteor.users.updateAsync(this.userId, {\n                $set: {\n                  roles: ['admin']\n                }\n              });\n              return 'Admin role added';\n            }\n            return 'User is not admin';\n          }\n          return 'User is already admin';\n        } catch (error) {\n          console.error('[checkAndFixAdminRole] Error:', error);\n          throw new Meteor.Error('check-role-failed', error.message);\n        }\n      },\n      async 'users.diagnoseRoles'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _currentUser$roles;\n          const currentUser = await Meteor.users.findOneAsync(this.userId);\n          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {\n            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n          }\n          const allUsers = await Meteor.users.find().fetchAsync();\n          const usersWithIssues = [];\n          const fixes = [];\n          for (const user of allUsers) {\n            var _user$profile3, _user$roles2;\n            const issues = [];\n\n            // Check if roles array exists\n            if (!user.roles || !Array.isArray(user.roles)) {\n              var _user$profile2, _user$emails4, _user$emails4$;\n              issues.push('No roles array');\n              // Fix: Initialize roles based on profile\n              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [role]\n                }\n              });\n              fixes.push(\"Initialized roles for \".concat((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.address));\n            }\n\n            // Check if role matches profile\n            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {\n              var _user$emails5, _user$emails5$;\n              issues.push('Role mismatch with profile');\n              // Fix: Update roles to match profile\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [user.profile.role]\n                }\n              });\n              fixes.push(\"Fixed role mismatch for \".concat((_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address));\n            }\n            if (issues.length > 0) {\n              var _user$emails6, _user$emails6$;\n              usersWithIssues.push({\n                email: (_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address,\n                issues\n              });\n            }\n          }\n          return {\n            usersWithIssues,\n            fixes,\n            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n          };\n        } catch (error) {\n          throw new Meteor.Error('diagnose-failed', error.message);\n        }\n      },\n      'users.createTestTeamMember'() {\n        // Only allow in development\n        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n          try {\n            const testMember = {\n              email: '<EMAIL>',\n              password: 'TestPass123!',\n              firstName: 'Test',\n              lastName: 'Member'\n            };\n            const userId = Accounts.createUser({\n              email: testMember.email,\n              password: testMember.password,\n              profile: {\n                firstName: testMember.firstName,\n                lastName: testMember.lastName,\n                role: 'team-member',\n                fullName: \"\".concat(testMember.firstName, \" \").concat(testMember.lastName)\n              }\n            });\n\n            // Set the role explicitly\n            Meteor.users.update(userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return {\n              success: true,\n              userId,\n              message: 'Test team member created successfully'\n            };\n          } catch (error) {\n            console.error('[createTestTeamMember] Error:', error);\n            throw new Meteor.Error('create-test-member-failed', error.message);\n          }\n        } else {\n          throw new Meteor.Error('not-development', 'This method is only available in development');\n        }\n      }\n    });\n    __reify_async_result__();\n  } catch (_reifyError) {\n    return __reify_async_result__(_reifyError);\n  }\n  __reify_async_result__()\n}, {\n  self: this,\n  async: false\n});", "map": {"version": 3, "names": ["_objectSpread", "module", "link", "default", "v", "Meteor", "LinksCollection", "Accounts", "Email", "Tasks", "Roles", "check", "__reifyWaitForDeps__", "insertLink", "_ref", "title", "url", "insertAsync", "createdAt", "Date", "ADMIN_TOKEN", "startup", "_Meteor$settings$priv", "createIndex", "assignedTo", "created<PERSON>y", "users", "roles", "background", "error", "console", "warn", "message", "allUsers", "find", "fetchAsync", "log", "map", "user", "_user$emails", "_user$emails$", "id", "_id", "email", "emails", "address", "profile", "updates", "Array", "isArray", "Object", "keys", "length", "_user$emails2", "_user$emails2$", "updateAsync", "$set", "teamMembersCount", "countAsync", "testMembers", "password", "firstName", "lastName", "member", "userId", "createUserAsync", "role", "fullName", "concat", "name", "emailSettings", "settings", "private", "username", "process", "env", "MAIL_URL", "encodeURIComponent", "server", "port", "send", "to", "from", "subject", "text", "config", "sendVerificationEmail", "forbidClientAccountCreation", "emailTemplates", "siteName", "verifyEmail", "emailAddress", "html", "publish", "onCreateUser", "options", "_customizedUser$email", "_customizedUser$email2", "customizedUser", "ready", "teamMembers", "$or", "fields", "methods", "users.create", "_ref2", "adminToken", "Error", "passwordRegex", "uppercase", "number", "special", "passwordErrors", "test", "push", "join", "createUser", "users.getRole", "_user$roles", "_user$profile", "findOneAsync", "users.resendVerificationEmail", "findUserByEmail", "userEmail", "verified", "users.forgotPassword", "_ref3", "newPassword", "String", "setPassword", "success", "users.checkAndFixAdminRole", "_user$emails3", "_user$emails3$", "includes", "totalUsers", "users.diagnoseRoles", "_currentUser$roles", "currentUser", "usersWithIssues", "fixes", "_user$profile3", "_user$roles2", "issues", "_user$profile2", "_user$emails4", "_user$emails4$", "_user$emails5", "_user$emails5$", "_user$emails6", "_user$emails6$", "users.createTestTeamMember", "NODE_ENV", "testMember", "update", "__reify_async_result__", "_reifyError", "self", "async"], "sources": ["server/main.js"], "sourcesContent": ["import { Meteor } from 'meteor/meteor';\nimport { LinksCollection } from '/imports/api/links';\nimport { Accounts } from 'meteor/accounts-base';\nimport { Email } from 'meteor/email';\nimport { Tasks } from '/imports/api/tasks';\nimport { Roles } from 'meteor/alanning:roles';\nimport { check } from 'meteor/check';\n\nasync function insertLink({ title, url }) {\n  await LinksCollection.insertAsync({ title, url, createdAt: new Date() });\n}\n\nconst ADMIN_TOKEN = '123456';\n\nMeteor.startup(async () => {\n  // Ensure indexes for Tasks collection\n  try {\n    await Tasks.createIndex({ createdAt: 1 });\n    await Tasks.createIndex({ assignedTo: 1 });\n    await Tasks.createIndex({ createdBy: 1 });\n\n    // Ensure indexes for Users collection\n    // Note: emails.address index is already created by Meteor accounts system\n    await Meteor.users.createIndex({ roles: 1 }, { background: true });\n  } catch (error) {\n    console.warn('[Startup] Index creation warning:', error.message);\n  }\n\n  // Check if we have any team members\n  try {\n    const allUsers = await Meteor.users.find().fetchAsync();\n    console.log('[Startup] All users:', allUsers.map(user => ({\n      id: user._id,\n      email: user.emails?.[0]?.address,\n      roles: user.roles,\n      profile: user.profile\n    })));\n\n    // First, ensure all users have roles and createdAt\n    for (const user of allUsers) {\n      const updates = {};\n      \n      if (!user.roles || !Array.isArray(user.roles)) {\n        updates.roles = ['team-member'];\n      }\n      \n      if (!user.createdAt) {\n        updates.createdAt = new Date();\n      }\n      \n      if (Object.keys(updates).length > 0) {\n        console.log('[Startup] Fixing missing fields for user:', user.emails?.[0]?.address);\n        await Meteor.users.updateAsync(user._id, {\n          $set: updates\n        });\n      }\n    }\n\n    const teamMembersCount = await Meteor.users.find({ 'roles': 'team-member' }).countAsync();\n    console.log('[Startup] Found team members:', teamMembersCount);\n\n    // Create test team members if none exist\n    if (teamMembersCount === 0) {\n      console.log('[Startup] Creating test team members');\n      try {\n        // Create multiple test team members\n        const testMembers = [\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'John',\n            lastName: 'Doe'\n          },\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'Jane',\n            lastName: 'Smith'\n          }\n        ];\n\n        for (const member of testMembers) {\n          const userId = await Accounts.createUserAsync({\n            email: member.email,\n            password: member.password,\n            role: 'team-member',\n            createdAt: new Date(),\n            profile: {\n              firstName: member.firstName,\n              lastName: member.lastName,\n              role: 'team-member',\n              fullName: `${member.firstName} ${member.lastName}`\n            }\n          });\n\n          // Set the role explicitly\n          await Meteor.users.updateAsync(userId, {\n            $set: { roles: ['team-member'] }\n          });\n\n          console.log('[Startup] Created test team member:', {\n            id: userId,\n            email: member.email,\n            name: `${member.firstName} ${member.lastName}`\n          });\n        }\n      } catch (error) {\n        console.error('[Startup] Error creating test team members:', error);\n      }\n    }\n  } catch (error) {\n    console.error('[Startup] Error checking team members:', error);\n  }\n\n  // Email configuration from settings\n  const emailSettings = Meteor.settings.private?.email;\n  \n  // Configure email SMTP\n  if (emailSettings?.username && emailSettings?.password) {\n    process.env.MAIL_URL = `smtp://${encodeURIComponent(emailSettings.username)}:${encodeURIComponent(emailSettings.password)}@${emailSettings.server}:${emailSettings.port}`;\n    \n    // Test email configuration\n    try {\n      console.log('Testing email configuration...');\n      Email.send({\n        to: emailSettings.username,\n        from: emailSettings.username,\n        subject: 'Test Email',\n        text: 'If you receive this email, your email configuration is working correctly.'\n      });\n      console.log('Test email sent successfully!');\n    } catch (error) {\n      console.error('Error sending test email:', error);\n    }\n  } else {\n    console.warn('Email configuration is missing in settings.json');\n  }\n\n  // Configure account creation to require email verification\n  Accounts.config({\n    sendVerificationEmail: true,\n    forbidClientAccountCreation: false\n  });\n\n  // Customize verification email\n  Accounts.emailTemplates.siteName = \"Task Management System\";\n  Accounts.emailTemplates.from = emailSettings?.username ? \n    `Task Management System <${emailSettings.username}>` : \n    \"Task Management System <<EMAIL>>\";\n\n  Accounts.emailTemplates.verifyEmail = {\n    subject() {\n      return \"Verify Your Email Address\";\n    },\n    text(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `Hello,\\n\\n`\n        + `To verify your email address (${emailAddress}), please click the link below:\\n\\n`\n        + `${url}\\n\\n`\n        + `If you did not request this verification, please ignore this email.\\n\\n`\n        + `Thanks,\\n`\n        + `Your Task Management System Team`;\n    },\n    html(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (${emailAddress}), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"${url}\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      `;\n    }\n  };\n\n  // If the Links collection is empty, add some data.\n  if (await LinksCollection.find().countAsync() === 0) {\n    await insertLink({\n      title: 'Do the Tutorial',\n      url: 'https://www.meteor.com/tutorials/react/creating-an-app',\n    });\n\n    await insertLink({\n      title: 'Follow the Guide',\n      url: 'https://guide.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Read the Docs',\n      url: 'https://docs.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Discussions',\n      url: 'https://forums.meteor.com',\n    });\n  }\n\n  // We publish the entire Links collection to all clients.\n  // In order to be fetched in real-time to the clients\n  Meteor.publish(\"links\", function () {\n    return LinksCollection.find();\n  });\n\n  // Add custom fields to users\n  Accounts.onCreateUser((options, user) => {\n    console.log('[onCreateUser] Creating user with options:', {\n      email: options.email,\n      role: options.role,\n      profile: options.profile,\n      createdAt: options.createdAt\n    });\n\n    const customizedUser = { ...user };\n    \n    // Ensure we have a profile\n    customizedUser.profile = options.profile || {};\n    \n    // Add role from options\n    const role = options.role || 'team-member';\n    customizedUser.roles = [role];\n    \n    // Set createdAt if provided, otherwise use current date\n    customizedUser.createdAt = options.createdAt || new Date();\n    \n    console.log('[onCreateUser] Created user:', {\n      id: customizedUser._id,\n      email: customizedUser.emails?.[0]?.address,\n      roles: customizedUser.roles,\n      profile: customizedUser.profile,\n      createdAt: customizedUser.createdAt\n    });\n    \n    return customizedUser;\n  });\n\n  // Publish team members\n  Meteor.publish('teamMembers', function() {\n    console.log('[teamMembers] Publication called, userId:', this.userId);\n    \n    if (!this.userId) {\n      console.log('[teamMembers] No userId, returning ready');\n      return this.ready();\n    }\n\n    try {\n      // Simple query to find all team members\n      const teamMembers = Meteor.users.find(\n        { \n          $or: [\n            { 'roles': 'team-member' },\n            { 'profile.role': 'team-member' }\n          ]\n        },\n        { \n          fields: { \n            emails: 1, \n            roles: 1,\n            'profile.firstName': 1,\n            'profile.lastName': 1,\n            'profile.fullName': 1,\n            createdAt: 1\n          }\n        }\n      );\n\n      console.log('[teamMembers] Publishing team members');\n      return teamMembers;\n    } catch (error) {\n      console.error('[teamMembers] Error in publication:', error);\n      return this.ready();\n    }\n  });\n\n  // Publish user data with roles\n  Meteor.publish('userData', function() {\n    if (!this.userId) {\n      return this.ready();\n    }\n\n    console.log('[userData] Publishing data for user:', this.userId);\n    \n    return Meteor.users.find(\n      { _id: this.userId },\n      { \n        fields: { \n          roles: 1, \n          emails: 1,\n          profile: 1\n        } \n      }\n    );\n  });\n});\n\n// Method to create a new user with role\nMeteor.methods({\n  'users.create'({ email, password, role, adminToken, firstName, lastName }) {\n    // Validate admin token if trying to create admin account\n    if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n      throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n    }\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(password)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(password)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(password)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(password)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Create the user\n    try {\n      const userId = Accounts.createUser({\n        email,\n        password,\n        role, // This will be used in onCreateUser callback\n        profile: {\n          role, // Store in profile as well for easy access\n          firstName,\n          lastName,\n          fullName: `${firstName} ${lastName}`\n        }\n      });\n\n      // Send verification email\n      if (userId) {\n        Accounts.sendVerificationEmail(userId);\n      }\n\n      return userId;\n    } catch (error) {\n      throw new Meteor.Error('create-user-failed', error.message);\n    }\n  },\n\n  async 'users.getRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'User must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      if (!user) {\n        throw new Meteor.Error('user-not-found', 'User not found');\n      }\n      \n      // Check both roles array and profile for role\n      const role = user.roles?.[0] || user.profile?.role || 'team-member';\n      return role;\n    } catch (error) {\n      throw new Meteor.Error('get-role-failed', error.message);\n    }\n  },\n\n  'users.resendVerificationEmail'(email) {\n    // Find user by email\n    const user = Accounts.findUserByEmail(email);\n    if (!user) {\n      throw new Meteor.Error('user-not-found', 'No user found with this email address');\n    }\n\n    // Check if email is already verified\n    const userEmail = user.emails[0];\n    if (userEmail.verified) {\n      throw new Meteor.Error('already-verified', 'This email is already verified');\n    }\n\n    // Send verification email\n    try {\n      Accounts.sendVerificationEmail(user._id, email);\n      return true;\n    } catch (error) {\n      throw new Meteor.Error('verification-email-failed', error.message);\n    }\n  },\n\n  'users.forgotPassword'({ email, newPassword }) {\n    check(email, String);\n    check(newPassword, String);\n\n    // Find user by email\n    const user = Accounts.findUserByEmail(email);\n    if (!user) {\n      throw new Meteor.Error('user-not-found', 'No user found with this email address');\n    }\n\n    // Validate password requirements (same as signup)\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(newPassword)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Update the user's password using user ID\n    try {\n      Accounts.setPassword(user._id, newPassword);\n      console.log(`Password reset for user ID: ${user._id}, email: ${email}`);\n      return { success: true, message: 'Password updated successfully' };\n    } catch (error) {\n      console.error('Error updating password:', error);\n      throw new Meteor.Error('password-update-failed', 'Failed to update password. Please try again.');\n    }\n  },\n\n  async 'users.checkAndFixAdminRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      console.log('[checkAndFixAdminRole] Checking user:', {\n        id: user?._id,\n        email: user?.emails?.[0]?.address,\n        roles: user?.roles\n      });\n      \n      // If user has no roles array, initialize it\n      if (!user.roles) {\n        await Meteor.users.updateAsync(this.userId, {\n          $set: { roles: ['team-member'] }\n        });\n        return 'Roles initialized';\n      }\n      \n      // If user has no roles or doesn't have admin role\n      if (!user.roles.includes('admin')) {\n        console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n        \n        // Check if this is the first user (they should be admin)\n        const totalUsers = await Meteor.users.find().countAsync();\n        if (totalUsers === 1) {\n          console.log('[checkAndFixAdminRole] First user, setting as admin');\n          await Meteor.users.updateAsync(this.userId, {\n            $set: { roles: ['admin'] }\n          });\n          return 'Admin role added';\n        }\n        return 'User is not admin';\n      }\n      \n      return 'User is already admin';\n    } catch (error) {\n      console.error('[checkAndFixAdminRole] Error:', error);\n      throw new Meteor.Error('check-role-failed', error.message);\n    }\n  },\n\n  async 'users.diagnoseRoles'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n\n    try {\n      const currentUser = await Meteor.users.findOneAsync(this.userId);\n      if (!currentUser.roles?.includes('admin')) {\n        throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n      }\n\n      const allUsers = await Meteor.users.find().fetchAsync();\n      const usersWithIssues = [];\n      const fixes = [];\n\n      for (const user of allUsers) {\n        const issues = [];\n        \n        // Check if roles array exists\n        if (!user.roles || !Array.isArray(user.roles)) {\n          issues.push('No roles array');\n          // Fix: Initialize roles based on profile\n          const role = user.profile?.role || 'team-member';\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [role] }\n          });\n          fixes.push(`Initialized roles for ${user.emails?.[0]?.address}`);\n        }\n        \n        // Check if role matches profile\n        if (user.profile?.role && user.roles?.[0] !== user.profile.role) {\n          issues.push('Role mismatch with profile');\n          // Fix: Update roles to match profile\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [user.profile.role] }\n          });\n          fixes.push(`Fixed role mismatch for ${user.emails?.[0]?.address}`);\n        }\n\n        if (issues.length > 0) {\n          usersWithIssues.push({\n            email: user.emails?.[0]?.address,\n            issues\n          });\n        }\n      }\n\n      return {\n        usersWithIssues,\n        fixes,\n        message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n      };\n    } catch (error) {\n      throw new Meteor.Error('diagnose-failed', error.message);\n    }\n  },\n\n  'users.createTestTeamMember'() {\n    // Only allow in development\n    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n      try {\n        const testMember = {\n          email: '<EMAIL>',\n          password: 'TestPass123!',\n          firstName: 'Test',\n          lastName: 'Member'\n        };\n\n        const userId = Accounts.createUser({\n          email: testMember.email,\n          password: testMember.password,\n          profile: {\n            firstName: testMember.firstName,\n            lastName: testMember.lastName,\n            role: 'team-member',\n            fullName: `${testMember.firstName} ${testMember.lastName}`\n          }\n        });\n\n        // Set the role explicitly\n        Meteor.users.update(userId, {\n          $set: { roles: ['team-member'] }\n        });\n\n        return {\n          success: true,\n          userId,\n          message: 'Test team member created successfully'\n        };\n      } catch (error) {\n        console.error('[createTestTeamMember] Error:', error);\n        throw new Meteor.Error('create-test-member-failed', error.message);\n      }\n    } else {\n      throw new Meteor.Error('not-development', 'This method is only available in development');\n    }\n  }\n});"], "mappings": ";;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAIC,MAAM;IAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACG,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,eAAe;IAACL,MAAM,CAACC,IAAI,CAAC,oBAAoB,EAAC;MAACI,eAAeA,CAACF,CAAC,EAAC;QAACE,eAAe,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,QAAQ;IAACN,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACK,QAAQA,CAACH,CAAC,EAAC;QAACG,QAAQ,GAACH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,KAAK;IAACP,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACM,KAAKA,CAACJ,CAAC,EAAC;QAACI,KAAK,GAACJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIK,KAAK;IAACR,MAAM,CAACC,IAAI,CAAC,oBAAoB,EAAC;MAACO,KAAKA,CAACL,CAAC,EAAC;QAACK,KAAK,GAACL,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIM,KAAK;IAACT,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACQ,KAAKA,CAACN,CAAC,EAAC;QAACM,KAAK,GAACN,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIO,KAAK;IAACV,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACS,KAAKA,CAACP,CAAC,EAAC;QAACO,KAAK,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAQxiB,eAAeC,UAAUA,CAAAC,IAAA,EAAiB;MAAA,IAAhB;QAAEC,KAAK;QAAEC;MAAI,CAAC,GAAAF,IAAA;MACtC,MAAMR,eAAe,CAACW,WAAW,CAAC;QAAEF,KAAK;QAAEC,GAAG;QAAEE,SAAS,EAAE,IAAIC,IAAI,CAAC;MAAE,CAAC,CAAC;IAC1E;IAEA,MAAMC,WAAW,GAAG,QAAQ;IAE5Bf,MAAM,CAACgB,OAAO,CAAC,YAAY;MAAA,IAAAC,qBAAA;MACzB;MACA,IAAI;QACF,MAAMb,KAAK,CAACc,WAAW,CAAC;UAAEL,SAAS,EAAE;QAAE,CAAC,CAAC;QACzC,MAAMT,KAAK,CAACc,WAAW,CAAC;UAAEC,UAAU,EAAE;QAAE,CAAC,CAAC;QAC1C,MAAMf,KAAK,CAACc,WAAW,CAAC;UAAEE,SAAS,EAAE;QAAE,CAAC,CAAC;;QAEzC;QACA;QACA,MAAMpB,MAAM,CAACqB,KAAK,CAACH,WAAW,CAAC;UAAEI,KAAK,EAAE;QAAE,CAAC,EAAE;UAAEC,UAAU,EAAE;QAAK,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mCAAmC,EAAEF,KAAK,CAACG,OAAO,CAAC;MAClE;;MAEA;MACA,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM5B,MAAM,CAACqB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;QACvDL,OAAO,CAACM,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,GAAG,CAACC,IAAI;UAAA,IAAAC,YAAA,EAAAC,aAAA;UAAA,OAAK;YACxDC,EAAE,EAAEH,IAAI,CAACI,GAAG;YACZC,KAAK,GAAAJ,YAAA,GAAED,IAAI,CAACM,MAAM,cAAAL,YAAA,wBAAAC,aAAA,GAAXD,YAAA,CAAc,CAAC,CAAC,cAAAC,aAAA,uBAAhBA,aAAA,CAAkBK,OAAO;YAChClB,KAAK,EAAEW,IAAI,CAACX,KAAK;YACjBmB,OAAO,EAAER,IAAI,CAACQ;UAChB,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA,KAAK,MAAMR,IAAI,IAAIL,QAAQ,EAAE;UAC3B,MAAMc,OAAO,GAAG,CAAC,CAAC;UAElB,IAAI,CAACT,IAAI,CAACX,KAAK,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACX,IAAI,CAACX,KAAK,CAAC,EAAE;YAC7CoB,OAAO,CAACpB,KAAK,GAAG,CAAC,aAAa,CAAC;UACjC;UAEA,IAAI,CAACW,IAAI,CAACpB,SAAS,EAAE;YACnB6B,OAAO,CAAC7B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;UAChC;UAEA,IAAI+B,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;YAAA,IAAAC,aAAA,EAAAC,cAAA;YACnCxB,OAAO,CAACM,GAAG,CAAC,2CAA2C,GAAAiB,aAAA,GAAEf,IAAI,CAACM,MAAM,cAAAS,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBT,OAAO,CAAC;YACnF,MAAMxC,MAAM,CAACqB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;cACvCc,IAAI,EAAET;YACR,CAAC,CAAC;UACJ;QACF;QAEA,MAAMU,gBAAgB,GAAG,MAAMpD,MAAM,CAACqB,KAAK,CAACQ,IAAI,CAAC;UAAE,OAAO,EAAE;QAAc,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC;QACzF5B,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEqB,gBAAgB,CAAC;;QAE9D;QACA,IAAIA,gBAAgB,KAAK,CAAC,EAAE;UAC1B3B,OAAO,CAACM,GAAG,CAAC,sCAAsC,CAAC;UACnD,IAAI;YACF;YACA,MAAMuB,WAAW,GAAG,CAClB;cACEhB,KAAK,EAAE,mBAAmB;cAC1BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,EACD;cACEnB,KAAK,EAAE,mBAAmB;cAC1BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,CACF;YAED,KAAK,MAAMC,MAAM,IAAIJ,WAAW,EAAE;cAChC,MAAMK,MAAM,GAAG,MAAMzD,QAAQ,CAAC0D,eAAe,CAAC;gBAC5CtB,KAAK,EAAEoB,MAAM,CAACpB,KAAK;gBACnBiB,QAAQ,EAAEG,MAAM,CAACH,QAAQ;gBACzBM,IAAI,EAAE,aAAa;gBACnBhD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;gBACrB2B,OAAO,EAAE;kBACPe,SAAS,EAAEE,MAAM,CAACF,SAAS;kBAC3BC,QAAQ,EAAEC,MAAM,CAACD,QAAQ;kBACzBI,IAAI,EAAE,aAAa;kBACnBC,QAAQ,KAAAC,MAAA,CAAKL,MAAM,CAACF,SAAS,OAAAO,MAAA,CAAIL,MAAM,CAACD,QAAQ;gBAClD;cACF,CAAC,CAAC;;cAEF;cACA,MAAMzD,MAAM,CAACqB,KAAK,CAAC6B,WAAW,CAACS,MAAM,EAAE;gBACrCR,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAAC,aAAa;gBAAE;cACjC,CAAC,CAAC;cAEFG,OAAO,CAACM,GAAG,CAAC,qCAAqC,EAAE;gBACjDK,EAAE,EAAEuB,MAAM;gBACVrB,KAAK,EAAEoB,MAAM,CAACpB,KAAK;gBACnB0B,IAAI,KAAAD,MAAA,CAAKL,MAAM,CAACF,SAAS,OAAAO,MAAA,CAAIL,MAAM,CAACD,QAAQ;cAC9C,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACrE;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;;MAEA;MACA,MAAMyC,aAAa,IAAAhD,qBAAA,GAAGjB,MAAM,CAACkE,QAAQ,CAACC,OAAO,cAAAlD,qBAAA,uBAAvBA,qBAAA,CAAyBqB,KAAK;;MAEpD;MACA,IAAI2B,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,IAAIH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEV,QAAQ,EAAE;QACtDc,OAAO,CAACC,GAAG,CAACC,QAAQ,aAAAR,MAAA,CAAaS,kBAAkB,CAACP,aAAa,CAACG,QAAQ,CAAC,OAAAL,MAAA,CAAIS,kBAAkB,CAACP,aAAa,CAACV,QAAQ,CAAC,OAAAQ,MAAA,CAAIE,aAAa,CAACQ,MAAM,OAAAV,MAAA,CAAIE,aAAa,CAACS,IAAI,CAAE;;QAEzK;QACA,IAAI;UACFjD,OAAO,CAACM,GAAG,CAAC,gCAAgC,CAAC;UAC7C5B,KAAK,CAACwE,IAAI,CAAC;YACTC,EAAE,EAAEX,aAAa,CAACG,QAAQ;YAC1BS,IAAI,EAAEZ,aAAa,CAACG,QAAQ;YAC5BU,OAAO,EAAE,YAAY;YACrBC,IAAI,EAAE;UACR,CAAC,CAAC;UACFtD,OAAO,CAACM,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,CAAC,OAAOP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAC;MACjE;;MAEA;MACAxB,QAAQ,CAAC8E,MAAM,CAAC;QACdC,qBAAqB,EAAE,IAAI;QAC3BC,2BAA2B,EAAE;MAC/B,CAAC,CAAC;;MAEF;MACAhF,QAAQ,CAACiF,cAAc,CAACC,QAAQ,GAAG,wBAAwB;MAC3DlF,QAAQ,CAACiF,cAAc,CAACN,IAAI,GAAGZ,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,8BAAAL,MAAA,CACzBE,aAAa,CAACG,QAAQ,SACjD,8CAA8C;MAEhDlE,QAAQ,CAACiF,cAAc,CAACE,WAAW,GAAG;QACpCP,OAAOA,CAAA,EAAG;UACR,OAAO,2BAA2B;QACpC,CAAC;QACDC,IAAIA,CAAC9C,IAAI,EAAEtB,GAAG,EAAE;UACd,MAAM2E,YAAY,GAAGrD,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO;UAC3C,OAAO,gDAAAuB,MAAA,CAC8BuB,YAAY,wCAAqC,MAAAvB,MAAA,CAC/EpD,GAAG,SAAM,4EAC6D,cAC9D,qCACuB;QACxC,CAAC;QACD4E,IAAIA,CAACtD,IAAI,EAAEtB,GAAG,EAAE;UACd,MAAM2E,YAAY,GAAGrD,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO;UAC3C,sQAAAuB,MAAA,CAKyCuB,YAAY,gHAAAvB,MAAA,CAElCpD,GAAG;QAexB;MACF,CAAC;;MAED;MACA,IAAI,OAAMV,eAAe,CAAC4B,IAAI,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC,MAAK,CAAC,EAAE;QACnD,MAAM7C,UAAU,CAAC;UACfE,KAAK,EAAE,iBAAiB;UACxBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,kBAAkB;UACzBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,eAAe;UACtBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,aAAa;UACpBC,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;;MAEA;MACA;MACAX,MAAM,CAACwF,OAAO,CAAC,OAAO,EAAE,YAAY;QAClC,OAAOvF,eAAe,CAAC4B,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACA3B,QAAQ,CAACuF,YAAY,CAAC,CAACC,OAAO,EAAEzD,IAAI,KAAK;QAAA,IAAA0D,qBAAA,EAAAC,sBAAA;QACvCnE,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAE;UACxDO,KAAK,EAAEoD,OAAO,CAACpD,KAAK;UACpBuB,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;UAClBpB,OAAO,EAAEiD,OAAO,CAACjD,OAAO;UACxB5B,SAAS,EAAE6E,OAAO,CAAC7E;QACrB,CAAC,CAAC;QAEF,MAAMgF,cAAc,GAAAlG,aAAA,KAAQsC,IAAI,CAAE;;QAElC;QACA4D,cAAc,CAACpD,OAAO,GAAGiD,OAAO,CAACjD,OAAO,IAAI,CAAC,CAAC;;QAE9C;QACA,MAAMoB,IAAI,GAAG6B,OAAO,CAAC7B,IAAI,IAAI,aAAa;QAC1CgC,cAAc,CAACvE,KAAK,GAAG,CAACuC,IAAI,CAAC;;QAE7B;QACAgC,cAAc,CAAChF,SAAS,GAAG6E,OAAO,CAAC7E,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC;QAE1DW,OAAO,CAACM,GAAG,CAAC,8BAA8B,EAAE;UAC1CK,EAAE,EAAEyD,cAAc,CAACxD,GAAG;UACtBC,KAAK,GAAAqD,qBAAA,GAAEE,cAAc,CAACtD,MAAM,cAAAoD,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4BpD,OAAO;UAC1ClB,KAAK,EAAEuE,cAAc,CAACvE,KAAK;UAC3BmB,OAAO,EAAEoD,cAAc,CAACpD,OAAO;UAC/B5B,SAAS,EAAEgF,cAAc,CAAChF;QAC5B,CAAC,CAAC;QAEF,OAAOgF,cAAc;MACvB,CAAC,CAAC;;MAEF;MACA7F,MAAM,CAACwF,OAAO,CAAC,aAAa,EAAE,YAAW;QACvC/D,OAAO,CAACM,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC4B,MAAM,CAAC;QAErE,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;UAChBlC,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC;UACvD,OAAO,IAAI,CAAC+D,KAAK,CAAC,CAAC;QACrB;QAEA,IAAI;UACF;UACA,MAAMC,WAAW,GAAG/F,MAAM,CAACqB,KAAK,CAACQ,IAAI,CACnC;YACEmE,GAAG,EAAE,CACH;cAAE,OAAO,EAAE;YAAc,CAAC,EAC1B;cAAE,cAAc,EAAE;YAAc,CAAC;UAErC,CAAC,EACD;YACEC,MAAM,EAAE;cACN1D,MAAM,EAAE,CAAC;cACTjB,KAAK,EAAE,CAAC;cACR,mBAAmB,EAAE,CAAC;cACtB,kBAAkB,EAAE,CAAC;cACrB,kBAAkB,EAAE,CAAC;cACrBT,SAAS,EAAE;YACb;UACF,CACF,CAAC;UAEDY,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC;UACpD,OAAOgE,WAAW;QACpB,CAAC,CAAC,OAAOvE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,IAAI,CAACsE,KAAK,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;;MAEF;MACA9F,MAAM,CAACwF,OAAO,CAAC,UAAU,EAAE,YAAW;QACpC,IAAI,CAAC,IAAI,CAAC7B,MAAM,EAAE;UAChB,OAAO,IAAI,CAACmC,KAAK,CAAC,CAAC;QACrB;QAEArE,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC4B,MAAM,CAAC;QAEhE,OAAO3D,MAAM,CAACqB,KAAK,CAACQ,IAAI,CACtB;UAAEQ,GAAG,EAAE,IAAI,CAACsB;QAAO,CAAC,EACpB;UACEsC,MAAM,EAAE;YACN3E,KAAK,EAAE,CAAC;YACRiB,MAAM,EAAE,CAAC;YACTE,OAAO,EAAE;UACX;QACF,CACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAzC,MAAM,CAACkG,OAAO,CAAC;MACb,cAAcC,CAAAC,KAAA,EAA6D;QAAA,IAA5D;UAAE9D,KAAK;UAAEiB,QAAQ;UAAEM,IAAI;UAAEwC,UAAU;UAAE7C,SAAS;UAAEC;QAAS,CAAC,GAAA2C,KAAA;QACvE;QACA,IAAIvC,IAAI,KAAK,OAAO,IAAIwC,UAAU,KAAKtF,WAAW,EAAE;UAClD,MAAM,IAAIf,MAAM,CAACsG,KAAK,CAAC,qBAAqB,EAAE,8BAA8B,CAAC;QAC/E;;QAEA;QACA,MAAMC,aAAa,GAAG;UACpBxD,MAAM,EAAE,OAAO;UACfyD,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAACxD,MAAM,CAAC6D,IAAI,CAACrD,QAAQ,CAAC,EAAE;UACxCoD,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAACrD,QAAQ,CAAC,EAAE;UAC3CoD,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAACrD,QAAQ,CAAC,EAAE;UACxCoD,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAACrD,QAAQ,CAAC,EAAE;UACzCoD,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAAC5D,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAI/C,MAAM,CAACsG,KAAK,CAAC,kBAAkB,EAAEK,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF,MAAMnD,MAAM,GAAGzD,QAAQ,CAAC6G,UAAU,CAAC;YACjCzE,KAAK;YACLiB,QAAQ;YACRM,IAAI;YAAE;YACNpB,OAAO,EAAE;cACPoB,IAAI;cAAE;cACNL,SAAS;cACTC,QAAQ;cACRK,QAAQ,KAAAC,MAAA,CAAKP,SAAS,OAAAO,MAAA,CAAIN,QAAQ;YACpC;UACF,CAAC,CAAC;;UAEF;UACA,IAAIE,MAAM,EAAE;YACVzD,QAAQ,CAAC+E,qBAAqB,CAACtB,MAAM,CAAC;UACxC;UAEA,OAAOA,MAAM;QACf,CAAC,CAAC,OAAOnC,KAAK,EAAE;UACd,MAAM,IAAIxB,MAAM,CAACsG,KAAK,CAAC,oBAAoB,EAAE9E,KAAK,CAACG,OAAO,CAAC;QAC7D;MACF,CAAC;MAED,MAAM,eAAeqF,CAAA,EAAG;QACtB,IAAI,CAAC,IAAI,CAACrD,MAAM,EAAE;UAChB,MAAM,IAAI3D,MAAM,CAACsG,KAAK,CAAC,gBAAgB,EAAE,wBAAwB,CAAC;QACpE;QAEA,IAAI;UAAA,IAAAW,WAAA,EAAAC,aAAA;UACF,MAAMjF,IAAI,GAAG,MAAMjC,MAAM,CAACqB,KAAK,CAAC8F,YAAY,CAAC,IAAI,CAACxD,MAAM,CAAC;UACzD,IAAI,CAAC1B,IAAI,EAAE;YACT,MAAM,IAAIjC,MAAM,CAACsG,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;UAC5D;;UAEA;UACA,MAAMzC,IAAI,GAAG,EAAAoD,WAAA,GAAAhF,IAAI,CAACX,KAAK,cAAA2F,WAAA,uBAAVA,WAAA,CAAa,CAAC,CAAC,OAAAC,aAAA,GAAIjF,IAAI,CAACQ,OAAO,cAAAyE,aAAA,uBAAZA,aAAA,CAAcrD,IAAI,KAAI,aAAa;UACnE,OAAOA,IAAI;QACb,CAAC,CAAC,OAAOrC,KAAK,EAAE;UACd,MAAM,IAAIxB,MAAM,CAACsG,KAAK,CAAC,iBAAiB,EAAE9E,KAAK,CAACG,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,+BAA+ByF,CAAC9E,KAAK,EAAE;QACrC;QACA,MAAML,IAAI,GAAG/B,QAAQ,CAACmH,eAAe,CAAC/E,KAAK,CAAC;QAC5C,IAAI,CAACL,IAAI,EAAE;UACT,MAAM,IAAIjC,MAAM,CAACsG,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;QACnF;;QAEA;QACA,MAAMgB,SAAS,GAAGrF,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;QAChC,IAAI+E,SAAS,CAACC,QAAQ,EAAE;UACtB,MAAM,IAAIvH,MAAM,CAACsG,KAAK,CAAC,kBAAkB,EAAE,gCAAgC,CAAC;QAC9E;;QAEA;QACA,IAAI;UACFpG,QAAQ,CAAC+E,qBAAqB,CAAChD,IAAI,CAACI,GAAG,EAAEC,KAAK,CAAC;UAC/C,OAAO,IAAI;QACb,CAAC,CAAC,OAAOd,KAAK,EAAE;UACd,MAAM,IAAIxB,MAAM,CAACsG,KAAK,CAAC,2BAA2B,EAAE9E,KAAK,CAACG,OAAO,CAAC;QACpE;MACF,CAAC;MAED,sBAAsB6F,CAAAC,KAAA,EAAyB;QAAA,IAAxB;UAAEnF,KAAK;UAAEoF;QAAY,CAAC,GAAAD,KAAA;QAC3CnH,KAAK,CAACgC,KAAK,EAAEqF,MAAM,CAAC;QACpBrH,KAAK,CAACoH,WAAW,EAAEC,MAAM,CAAC;;QAE1B;QACA,MAAM1F,IAAI,GAAG/B,QAAQ,CAACmH,eAAe,CAAC/E,KAAK,CAAC;QAC5C,IAAI,CAACL,IAAI,EAAE;UACT,MAAM,IAAIjC,MAAM,CAACsG,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;QACnF;;QAEA;QACA,MAAMC,aAAa,GAAG;UACpBxD,MAAM,EAAE,OAAO;UACfyD,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAACxD,MAAM,CAAC6D,IAAI,CAACc,WAAW,CAAC,EAAE;UAC3Cf,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAACc,WAAW,CAAC,EAAE;UAC9Cf,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAACc,WAAW,CAAC,EAAE;UAC3Cf,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAACc,WAAW,CAAC,EAAE;UAC5Cf,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAAC5D,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAI/C,MAAM,CAACsG,KAAK,CAAC,kBAAkB,EAAEK,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF5G,QAAQ,CAAC0H,WAAW,CAAC3F,IAAI,CAACI,GAAG,EAAEqF,WAAW,CAAC;UAC3CjG,OAAO,CAACM,GAAG,gCAAAgC,MAAA,CAAgC9B,IAAI,CAACI,GAAG,eAAA0B,MAAA,CAAYzB,KAAK,CAAE,CAAC;UACvE,OAAO;YAAEuF,OAAO,EAAE,IAAI;YAAElG,OAAO,EAAE;UAAgC,CAAC;QACpE,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,MAAM,IAAIxB,MAAM,CAACsG,KAAK,CAAC,wBAAwB,EAAE,8CAA8C,CAAC;QAClG;MACF,CAAC;MAED,MAAM,4BAA4BwB,CAAA,EAAG;QACnC,IAAI,CAAC,IAAI,CAACnE,MAAM,EAAE;UAChB,MAAM,IAAI3D,MAAM,CAACsG,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAAyB,aAAA,EAAAC,cAAA;UACF,MAAM/F,IAAI,GAAG,MAAMjC,MAAM,CAACqB,KAAK,CAAC8F,YAAY,CAAC,IAAI,CAACxD,MAAM,CAAC;UACzDlC,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAE;YACnDK,EAAE,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,GAAG;YACbC,KAAK,EAAEL,IAAI,aAAJA,IAAI,wBAAA8F,aAAA,GAAJ9F,IAAI,CAAEM,MAAM,cAAAwF,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmBxF,OAAO;YACjClB,KAAK,EAAEW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX;UACf,CAAC,CAAC;;UAEF;UACA,IAAI,CAACW,IAAI,CAACX,KAAK,EAAE;YACf,MAAMtB,MAAM,CAACqB,KAAK,CAAC6B,WAAW,CAAC,IAAI,CAACS,MAAM,EAAE;cAC1CR,IAAI,EAAE;gBAAE7B,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YACF,OAAO,mBAAmB;UAC5B;;UAEA;UACA,IAAI,CAACW,IAAI,CAACX,KAAK,CAAC2G,QAAQ,CAAC,OAAO,CAAC,EAAE;YACjCxG,OAAO,CAACM,GAAG,CAAC,kEAAkE,CAAC;;YAE/E;YACA,MAAMmG,UAAU,GAAG,MAAMlI,MAAM,CAACqB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC;YACzD,IAAI6E,UAAU,KAAK,CAAC,EAAE;cACpBzG,OAAO,CAACM,GAAG,CAAC,qDAAqD,CAAC;cAClE,MAAM/B,MAAM,CAACqB,KAAK,CAAC6B,WAAW,CAAC,IAAI,CAACS,MAAM,EAAE;gBAC1CR,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAAC,OAAO;gBAAE;cAC3B,CAAC,CAAC;cACF,OAAO,kBAAkB;YAC3B;YACA,OAAO,mBAAmB;UAC5B;UAEA,OAAO,uBAAuB;QAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,MAAM,IAAIxB,MAAM,CAACsG,KAAK,CAAC,mBAAmB,EAAE9E,KAAK,CAACG,OAAO,CAAC;QAC5D;MACF,CAAC;MAED,MAAM,qBAAqBwG,CAAA,EAAG;QAC5B,IAAI,CAAC,IAAI,CAACxE,MAAM,EAAE;UAChB,MAAM,IAAI3D,MAAM,CAACsG,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAA8B,kBAAA;UACF,MAAMC,WAAW,GAAG,MAAMrI,MAAM,CAACqB,KAAK,CAAC8F,YAAY,CAAC,IAAI,CAACxD,MAAM,CAAC;UAChE,IAAI,GAAAyE,kBAAA,GAACC,WAAW,CAAC/G,KAAK,cAAA8G,kBAAA,eAAjBA,kBAAA,CAAmBH,QAAQ,CAAC,OAAO,CAAC,GAAE;YACzC,MAAM,IAAIjI,MAAM,CAACsG,KAAK,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;UAC5E;UAEA,MAAM1E,QAAQ,GAAG,MAAM5B,MAAM,CAACqB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;UACvD,MAAMwG,eAAe,GAAG,EAAE;UAC1B,MAAMC,KAAK,GAAG,EAAE;UAEhB,KAAK,MAAMtG,IAAI,IAAIL,QAAQ,EAAE;YAAA,IAAA4G,cAAA,EAAAC,YAAA;YAC3B,MAAMC,MAAM,GAAG,EAAE;;YAEjB;YACA,IAAI,CAACzG,IAAI,CAACX,KAAK,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACX,IAAI,CAACX,KAAK,CAAC,EAAE;cAAA,IAAAqH,cAAA,EAAAC,aAAA,EAAAC,cAAA;cAC7CH,MAAM,CAAC7B,IAAI,CAAC,gBAAgB,CAAC;cAC7B;cACA,MAAMhD,IAAI,GAAG,EAAA8E,cAAA,GAAA1G,IAAI,CAACQ,OAAO,cAAAkG,cAAA,uBAAZA,cAAA,CAAc9E,IAAI,KAAI,aAAa;cAChD,MAAM7D,MAAM,CAACqB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;gBACvCc,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAACuC,IAAI;gBAAE;cACxB,CAAC,CAAC;cACF0E,KAAK,CAAC1B,IAAI,0BAAA9C,MAAA,EAAA6E,aAAA,GAA0B3G,IAAI,CAACM,MAAM,cAAAqG,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBrG,OAAO,CAAE,CAAC;YAClE;;YAEA;YACA,IAAI,CAAAgG,cAAA,GAAAvG,IAAI,CAACQ,OAAO,cAAA+F,cAAA,eAAZA,cAAA,CAAc3E,IAAI,IAAI,EAAA4E,YAAA,GAAAxG,IAAI,CAACX,KAAK,cAAAmH,YAAA,uBAAVA,YAAA,CAAa,CAAC,CAAC,MAAKxG,IAAI,CAACQ,OAAO,CAACoB,IAAI,EAAE;cAAA,IAAAiF,aAAA,EAAAC,cAAA;cAC/DL,MAAM,CAAC7B,IAAI,CAAC,4BAA4B,CAAC;cACzC;cACA,MAAM7G,MAAM,CAACqB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;gBACvCc,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAACW,IAAI,CAACQ,OAAO,CAACoB,IAAI;gBAAE;cACrC,CAAC,CAAC;cACF0E,KAAK,CAAC1B,IAAI,4BAAA9C,MAAA,EAAA+E,aAAA,GAA4B7G,IAAI,CAACM,MAAM,cAAAuG,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBvG,OAAO,CAAE,CAAC;YACpE;YAEA,IAAIkG,MAAM,CAAC3F,MAAM,GAAG,CAAC,EAAE;cAAA,IAAAiG,aAAA,EAAAC,cAAA;cACrBX,eAAe,CAACzB,IAAI,CAAC;gBACnBvE,KAAK,GAAA0G,aAAA,GAAE/G,IAAI,CAACM,MAAM,cAAAyG,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBzG,OAAO;gBAChCkG;cACF,CAAC,CAAC;YACJ;UACF;UAEA,OAAO;YACLJ,eAAe;YACfC,KAAK;YACL5G,OAAO,EAAE4G,KAAK,CAACxF,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;UACpD,CAAC;QACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACd,MAAM,IAAIxB,MAAM,CAACsG,KAAK,CAAC,iBAAiB,EAAE9E,KAAK,CAACG,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,4BAA4BuH,CAAA,EAAG;QAC7B;QACA,IAAI,CAAC7E,OAAO,CAACC,GAAG,CAAC6E,QAAQ,IAAI9E,OAAO,CAACC,GAAG,CAAC6E,QAAQ,KAAK,aAAa,EAAE;UACnE,IAAI;YACF,MAAMC,UAAU,GAAG;cACjB9G,KAAK,EAAE,wBAAwB;cAC/BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC;YAED,MAAME,MAAM,GAAGzD,QAAQ,CAAC6G,UAAU,CAAC;cACjCzE,KAAK,EAAE8G,UAAU,CAAC9G,KAAK;cACvBiB,QAAQ,EAAE6F,UAAU,CAAC7F,QAAQ;cAC7Bd,OAAO,EAAE;gBACPe,SAAS,EAAE4F,UAAU,CAAC5F,SAAS;gBAC/BC,QAAQ,EAAE2F,UAAU,CAAC3F,QAAQ;gBAC7BI,IAAI,EAAE,aAAa;gBACnBC,QAAQ,KAAAC,MAAA,CAAKqF,UAAU,CAAC5F,SAAS,OAAAO,MAAA,CAAIqF,UAAU,CAAC3F,QAAQ;cAC1D;YACF,CAAC,CAAC;;YAEF;YACAzD,MAAM,CAACqB,KAAK,CAACgI,MAAM,CAAC1F,MAAM,EAAE;cAC1BR,IAAI,EAAE;gBAAE7B,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YAEF,OAAO;cACLuG,OAAO,EAAE,IAAI;cACblE,MAAM;cACNhC,OAAO,EAAE;YACX,CAAC;UACH,CAAC,CAAC,OAAOH,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD,MAAM,IAAIxB,MAAM,CAACsG,KAAK,CAAC,2BAA2B,EAAE9E,KAAK,CAACG,OAAO,CAAC;UACpE;QACF,CAAC,MAAM;UACL,MAAM,IAAI3B,MAAM,CAACsG,KAAK,CAAC,iBAAiB,EAAE,8CAA8C,CAAC;QAC3F;MACF;IACF,CAAC,CAAC;IAACgD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA", "ignoreList": []}, "sourceType": "module", "externalDependencies": {}, "hash": "a1c94385d88dafade247322b22488d33b55ef5e9"}