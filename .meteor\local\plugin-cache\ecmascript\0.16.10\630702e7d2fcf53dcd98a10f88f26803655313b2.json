{"metadata": {}, "options": {"assumptions": {}, "compact": false, "sourceMaps": true, "ast": true, "babelrc": false, "configFile": false, "parserOpts": {"sourceType": "module", "sourceFileName": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\ForgotPasswordPage.jsx", "plugins": ["*", "flow", "jsx", "asyncGenerators", "bigInt", "classPrivateMethods", "classPrivateProperties", "classProperties", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportExtensions", "exportNamespaceFrom", "functionBind", "functionSent", "importMeta", "nullishCoalescingOperator", "numericSeparator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", ["pipelineOperator", {"proposal": "minimal"}], "throwExpressions", "topLevelAwait", "classProperties", "classPrivateProperties", "jsx", "nullishCoalescingOperator", "nullishCoalescingOperator", "optionalChaining", "optionalChaining", "optionalCatchBinding", "optionalCatchBinding", "classProperties", "classPrivateProperties", "classPrivateMethods", "classProperties", "classPrivateProperties", "asyncGenerators", "asyncGenerators", "objectRestSpread", "objectRestSpread", "logicalAssignment"], "allowImportExportEverywhere": true, "allowReturnOutsideFunction": true, "allowUndeclaredExports": true, "strictMode": false}, "caller": {"name": "meteor", "arch": "web.browser"}, "sourceFileName": "imports/ui/pages/ForgotPasswordPage.jsx", "filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\ForgotPasswordPage.jsx", "inputSourceMap": {"version": 3, "names": ["React", "useState", "Meteor", "useNavigate", "Link", "<PERSON><PERSON>", "Form", "Field", "<PERSON><PERSON>", "ForgotPasswordSchema", "object", "shape", "email", "string", "required", "newPassword", "min", "matches", "confirmPassword", "oneOf", "ref", "ForgotPasswordPage", "_s", "navigate", "error", "setError", "success", "setSuccess", "showNewPassword", "setShowNewPassword", "showConfirmPassword", "setShowConfirmPassword", "handleSubmit", "values", "setSubmitting", "call", "err", "result", "console", "reason", "log", "errors", "touched", "isSubmitting", "_c", "$RefreshReg$"], "sources": ["imports/ui/pages/ForgotPasswordPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Meteor } from 'meteor/meteor';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Formik, Form, Field } from 'formik';\nimport * as Yup from 'yup';\n\nconst ForgotPasswordSchema = Yup.object().shape({\n  email: Yup.string()\n    .email('Invalid email address')\n    .required('Email is required'),\n  newPassword: Yup.string()\n    .required('New password is required')\n    .min(8, 'Password must be at least 8 characters')\n    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')\n    .matches(/[0-9]/, 'Password must contain at least one number')\n    .matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\n  confirmPassword: Yup.string()\n    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')\n    .required('Confirm password is required'),\n});\n\nexport const ForgotPasswordPage = () => {\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState(false);\n  const [showNewPassword, setShowNewPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n\n  const handleSubmit = (values, { setSubmitting }) => {\n    const { email, newPassword } = values;\n    setError('');\n    setSuccess(false);\n\n    Meteor.call('users.forgotPassword', { email, newPassword }, (err, result) => {\n      setSubmitting(false);\n      if (err) {\n        console.error('Forgot password error:', err);\n        setError(err.reason || 'Failed to reset password. Please try again.');\n      } else {\n        setSuccess(true);\n        console.log('Password reset successful:', result);\n      }\n    });\n  };\n\n  if (success) {\n    return (\n      <div className=\"auth-container\">\n        <div className=\"auth-card\">\n          <div className=\"auth-header\">\n            <div className=\"auth-logo\">\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              </svg>\n            </div>\n            <h1 className=\"auth-title\">Password Reset Successful</h1>\n            <p className=\"auth-subtitle\">\n              Your password has been updated successfully. You can now log in with your new password.\n            </p>\n          </div>\n          <Link \n            to=\"/login\" \n            className=\"btn btn-primary btn-auth\"\n          >\n            <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Go to Login\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14ZM12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 5L17 7V9C16.45 9 16 9.45 16 10V16C16 16.55 16.45 17 17 17H19C19.55 17 20 16.55 20 16V10C20 9.45 19.55 9 19 9Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n          <h1 className=\"auth-title\">Reset Password</h1>\n          <p className=\"auth-subtitle\">\n            Enter your email address and create a new password\n          </p>\n        </div>\n        \n        <Formik\n          initialValues={{ email: '', newPassword: '', confirmPassword: '' }}\n          validationSchema={ForgotPasswordSchema}\n          onSubmit={handleSubmit}\n        >\n          {({ errors, touched, isSubmitting }) => (\n            <Form className=\"auth-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Email Address\n                </label>\n                <Field\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  className={`form-input ${errors.email && touched.email ? 'form-input-error' : ''}`}\n                  placeholder=\"Enter your email address\"\n                />\n                {errors.email && touched.email && (\n                  <div className=\"form-error\">{errors.email}</div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"newPassword\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                  </svg>\n                  New Password\n                </label>\n                <div className=\"form-input-container\">\n                  <Field\n                    id=\"newPassword\"\n                    name=\"newPassword\"\n                    type={showNewPassword ? \"text\" : \"password\"}\n                    className={`form-input ${errors.newPassword && touched.newPassword ? 'form-input-error' : ''}`}\n                    placeholder=\"Enter new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"form-input-toggle\"\n                    onClick={() => setShowNewPassword(!showNewPassword)}\n                  >\n                    {showNewPassword ? (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.newPassword && touched.newPassword && (\n                  <div className=\"form-error\">{errors.newPassword}</div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                  </svg>\n                  Confirm New Password\n                </label>\n                <div className=\"form-input-container\">\n                  <Field\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? \"text\" : \"password\"}\n                    className={`form-input ${errors.confirmPassword && touched.confirmPassword ? 'form-input-error' : ''}`}\n                    placeholder=\"Confirm new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"form-input-toggle\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.confirmPassword && touched.confirmPassword && (\n                  <div className=\"form-error\">{errors.confirmPassword}</div>\n                )}\n              </div>\n\n              {error && (\n                <div className=\"form-error-message\">\n                  {error}\n                </div>\n              )}\n\n              <button \n                type=\"submit\" \n                className=\"btn btn-primary btn-auth\"\n                disabled={isSubmitting}\n              >\n                {isSubmitting ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    Resetting Password...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    Reset Password\n                  </>\n                )}\n              </button>\n            </Form>\n          )}\n        </Formik>\n\n        <div className=\"auth-footer\">\n          <p className=\"auth-footer-text\">\n            Remember your password? \n            <Link to=\"/login\" className=\"auth-link\">\n              Back to Login\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,QAAQ;AAC5C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAE1B,MAAMC,oBAAoB,GAAGD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAC9CC,KAAK,EAAEJ,GAAG,CAACK,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,WAAW,EAAEP,GAAG,CAACK,MAAM,CAAC,CAAC,CACtBC,QAAQ,CAAC,0BAA0B,CAAC,CACpCE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,OAAO,CAAC,OAAO,EAAE,qDAAqD,CAAC,CACvEA,OAAO,CAAC,OAAO,EAAE,2CAA2C,CAAC,CAC7DA,OAAO,CAAC,YAAY,EAAE,iEAAiE,CAAC;EAC3FC,eAAe,EAAEV,GAAG,CAACK,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAACX,GAAG,CAACY,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC7DN,QAAQ,CAAC,8BAA8B;AAC5C,CAAC,CAAC;AAEF,OAAO,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM+B,YAAY,GAAGA,CAACC,MAAM,EAAE;IAAEC;EAAc,CAAC,KAAK;IAClD,MAAM;MAAEtB,KAAK;MAAEG;IAAY,CAAC,GAAGkB,MAAM;IACrCR,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,KAAK,CAAC;IAEjBzB,MAAM,CAACiC,IAAI,CAAC,sBAAsB,EAAE;MAAEvB,KAAK;MAAEG;IAAY,CAAC,EAAE,CAACqB,GAAG,EAAEC,MAAM,KAAK;MAC3EH,aAAa,CAAC,KAAK,CAAC;MACpB,IAAIE,GAAG,EAAE;QACPE,OAAO,CAACd,KAAK,CAAC,wBAAwB,EAAEY,GAAG,CAAC;QAC5CX,QAAQ,CAACW,GAAG,CAACG,MAAM,IAAI,6CAA6C,CAAC;MACvE,CAAC,MAAM;QACLZ,UAAU,CAAC,IAAI,CAAC;QAChBW,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAEH,MAAM,CAAC;MACnD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIX,OAAO,EAAE;IACX,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AACrC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAClC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACtC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AACtC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC5G,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,oIAAoI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC/O,cAAc,EAAE,GAAG;AACnB,YAAY,EAAE,GAAG;AACjB,YAAY,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,yBAAyB,EAAE,EAAE;AACpE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe;AACxC;AACA,YAAY,EAAE,CAAC;AACf,UAAU,EAAE,GAAG;AACf,UAAU,CAAC,IAAI,CACH,EAAE,CAAC,QAAQ,CACX,SAAS,CAAC,0BAA0B;AAEhD,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC/H,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,qMAAqM,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC9S,YAAY,EAAE,GAAG;AACjB;AACA,UAAU,EAAE,IAAI;AAChB,QAAQ,EAAE,GAAG;AACb,MAAM,EAAE,GAAG,CAAC;EAEV;EAEA,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AACnC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAChC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AACpC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC1G,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,kQAAkQ,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3W,YAAY,EAAE,GAAG;AACjB,UAAU,EAAE,GAAG;AACf,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE;AACvD,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe;AACtC;AACA,UAAU,EAAE,CAAC;AACb,QAAQ,EAAE,GAAG;AACb;AACA,QAAQ,CAAC,MAAM,CACL,aAAa,CAAC,CAAC;QAAEd,KAAK,EAAE,EAAE;QAAEG,WAAW,EAAE,EAAE;QAAEG,eAAe,EAAE;MAAG,CAAC,CAAC,CACnE,gBAAgB,CAAC,CAACT,oBAAoB,CAAC,CACvC,QAAQ,CAAC,CAACuB,YAAY,CAAC;AAEjC,UAAU,CAAC,CAAC;UAAES,MAAM;UAAEC,OAAO;UAAEC;QAAa,CAAC,KACjC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW;AACvC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY;AAC7D,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,sOAAsO,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACrV,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,OAAO,CACV,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CACZ,SAAS,CAAC,CAAC,cAAcF,MAAM,CAAC7B,KAAK,IAAI8B,OAAO,CAAC9B,KAAK,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC,CACnF,WAAW,CAAC,0BAA0B;AAExD,gBAAgB,CAAC6B,MAAM,CAAC7B,KAAK,IAAI8B,OAAO,CAAC9B,KAAK,IAC5B,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC6B,MAAM,CAAC7B,KAAK,CAAC,EAAE,GAAG,CAChD;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY;AACnE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjH,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACvF,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,+JAA+J,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjO,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,sBAAsB;AACrD,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,aAAa,CAChB,IAAI,CAAC,aAAa,CAClB,IAAI,CAAC,CAACgB,eAAe,GAAG,MAAM,GAAG,UAAU,CAAC,CAC5C,SAAS,CAAC,CAAC,cAAca,MAAM,CAAC1B,WAAW,IAAI2B,OAAO,CAAC3B,WAAW,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC,CAC/F,WAAW,CAAC,oBAAoB;AAEpD,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,mBAAmB,CAC7B,OAAO,CAAC,CAAC,MAAMc,kBAAkB,CAAC,CAACD,eAAe,CAAC,CAAC;AAExE,oBAAoB,CAACA,eAAe,GACd,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,mmBAAmmB,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACttB,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wEAAwE,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3L,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AAC3F,sBAAsB,EAAE,GAAG,CACN;AACrB,kBAAkB,EAAE,MAAM;AAC1B,gBAAgB,EAAE,GAAG;AACrB,gBAAgB,CAACa,MAAM,CAAC1B,WAAW,IAAI2B,OAAO,CAAC3B,WAAW,IACxC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC0B,MAAM,CAAC1B,WAAW,CAAC,EAAE,GAAG,CACtD;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAY;AACvE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjH,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACvF,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,+JAA+J,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjO,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,sBAAsB;AACrD,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,iBAAiB,CACpB,IAAI,CAAC,iBAAiB,CACtB,IAAI,CAAC,CAACe,mBAAmB,GAAG,MAAM,GAAG,UAAU,CAAC,CAChD,SAAS,CAAC,CAAC,cAAcW,MAAM,CAACvB,eAAe,IAAIwB,OAAO,CAACxB,eAAe,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC,CACvG,WAAW,CAAC,sBAAsB;AAEtD,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,mBAAmB,CAC7B,OAAO,CAAC,CAAC,MAAMa,sBAAsB,CAAC,CAACD,mBAAmB,CAAC,CAAC;AAEhF,oBAAoB,CAACA,mBAAmB,GAClB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,mmBAAmmB,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACttB,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wEAAwE,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3L,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AAC3F,sBAAsB,EAAE,GAAG,CACN;AACrB,kBAAkB,EAAE,MAAM;AAC1B,gBAAgB,EAAE,GAAG;AACrB,gBAAgB,CAACW,MAAM,CAACvB,eAAe,IAAIwB,OAAO,CAACxB,eAAe,IAChD,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAACuB,MAAM,CAACvB,eAAe,CAAC,EAAE,GAAG,CAC1D;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAACM,KAAK,IACJ,CAAC,GAAG,CAAC,SAAS,CAAC,oBAAoB;AACnD,kBAAkB,CAACA,KAAK;AACxB,gBAAgB,EAAE,GAAG,CACN;AACf;AACA,cAAc,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,0BAA0B,CACpC,QAAQ,CAAC,CAACmB,YAAY,CAAC;AAEvC,gBAAgB,CAACA,YAAY,GACX;AAClB,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI;AAC5D;AACA,kBAAkB,GAAG,GAEH;AAClB,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACvI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,wDAAwD,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACzK,oBAAoB,EAAE,GAAG;AACzB;AACA,kBAAkB,GACD;AACjB,cAAc,EAAE,MAAM;AACtB,YAAY,EAAE,IAAI,CACP;AACX,QAAQ,EAAE,MAAM;AAChB;AACA,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB;AACzC;AACA,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW;AACnD;AACA,YAAY,EAAE,IAAI;AAClB,UAAU,EAAE,CAAC;AACb,QAAQ,EAAE,GAAG;AACb,MAAM,EAAE,GAAG;AACX,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACrB,EAAA,CApNWD,kBAAkB;EAAA,QACZlB,WAAW;AAAA;AAAAyC,EAAA,GADjBvB,kBAAkB;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "targets": {}, "cloneInputAst": true, "browserslistConfigFile": false, "passPerPreset": false, "envName": "development", "cwd": "C:\\Users\\<USER>\\smart-task-management-system", "root": "C:\\Users\\<USER>\\smart-task-management-system", "rootMode": "root", "plugins": [{"key": "base$0", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$0", "visitor": {"Program": {"enter": [null], "exit": [null]}, "_exploded": true, "_verified": true}, "options": {"avoidModernSyntax": false, "enforceStrictMode": false, "dynamicImport": true, "generateLetDeclarations": true}, "externalDependencies": []}, {"key": "transform-runtime", "visitor": {"MemberExpression": {"enter": [null]}, "ObjectPattern": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "Identifier": {"enter": [null]}, "JSXIdentifier": {"enter": [null]}}, "options": {"version": "7.17.2", "helpers": true, "useESModules": false, "corejs": false}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-react-jsx", "visitor": {"JSXNamespacedName": {"enter": [null]}, "JSXSpreadChild": {"enter": [null]}, "Program": {"enter": [null]}, "JSXFragment": {"exit": [null]}, "JSXElement": {"exit": [null]}, "JSXAttribute": {"enter": [null]}}, "options": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "runtime": "classic", "throwIfNamespace": true, "useBuiltIns": false}, "externalDependencies": []}, {"key": "transform-react-display-name", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-pure-annotations", "visitor": {"CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "syntax-nullish-coalescing-operator", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-nullish-coalescing-operator", "visitor": {"LogicalExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-chaining", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-chaining", "visitor": {"OptionalCallExpression": {"enter": [null]}, "OptionalMemberExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-catch-binding", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-catch-binding", "visitor": {"CatchClause": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-class-properties", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-async-generators", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-logical-assignment-operators", "visitor": {"AssignmentExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-literals", "visitor": {"NumericLiteral": {"enter": [null]}, "StringLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-template-literals", "visitor": {"TaggedTemplateExpression": {"enter": [null]}, "TemplateLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-parameters", "visitor": {"_exploded": true, "_verified": true, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-exponentiation-operator", "visitor": {"AssignmentExpression": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}], "presets": [], "generatorOpts": {"filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\ForgotPasswordPage.jsx", "comments": true, "compact": false, "sourceMaps": true, "sourceFileName": "imports/ui/pages/ForgotPasswordPage.jsx", "inputSourceMap": {"version": 3, "names": ["React", "useState", "Meteor", "useNavigate", "Link", "<PERSON><PERSON>", "Form", "Field", "<PERSON><PERSON>", "ForgotPasswordSchema", "object", "shape", "email", "string", "required", "newPassword", "min", "matches", "confirmPassword", "oneOf", "ref", "ForgotPasswordPage", "_s", "navigate", "error", "setError", "success", "setSuccess", "showNewPassword", "setShowNewPassword", "showConfirmPassword", "setShowConfirmPassword", "handleSubmit", "values", "setSubmitting", "call", "err", "result", "console", "reason", "log", "errors", "touched", "isSubmitting", "_c", "$RefreshReg$"], "sources": ["imports/ui/pages/ForgotPasswordPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Meteor } from 'meteor/meteor';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Formik, Form, Field } from 'formik';\nimport * as Yup from 'yup';\n\nconst ForgotPasswordSchema = Yup.object().shape({\n  email: Yup.string()\n    .email('Invalid email address')\n    .required('Email is required'),\n  newPassword: Yup.string()\n    .required('New password is required')\n    .min(8, 'Password must be at least 8 characters')\n    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')\n    .matches(/[0-9]/, 'Password must contain at least one number')\n    .matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\n  confirmPassword: Yup.string()\n    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')\n    .required('Confirm password is required'),\n});\n\nexport const ForgotPasswordPage = () => {\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState(false);\n  const [showNewPassword, setShowNewPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n\n  const handleSubmit = (values, { setSubmitting }) => {\n    const { email, newPassword } = values;\n    setError('');\n    setSuccess(false);\n\n    Meteor.call('users.forgotPassword', { email, newPassword }, (err, result) => {\n      setSubmitting(false);\n      if (err) {\n        console.error('Forgot password error:', err);\n        setError(err.reason || 'Failed to reset password. Please try again.');\n      } else {\n        setSuccess(true);\n        console.log('Password reset successful:', result);\n      }\n    });\n  };\n\n  if (success) {\n    return (\n      <div className=\"auth-container\">\n        <div className=\"auth-card\">\n          <div className=\"auth-header\">\n            <div className=\"auth-logo\">\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              </svg>\n            </div>\n            <h1 className=\"auth-title\">Password Reset Successful</h1>\n            <p className=\"auth-subtitle\">\n              Your password has been updated successfully. You can now log in with your new password.\n            </p>\n          </div>\n          <Link \n            to=\"/login\" \n            className=\"btn btn-primary btn-auth\"\n          >\n            <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Go to Login\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14ZM12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 5L17 7V9C16.45 9 16 9.45 16 10V16C16 16.55 16.45 17 17 17H19C19.55 17 20 16.55 20 16V10C20 9.45 19.55 9 19 9Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n          <h1 className=\"auth-title\">Reset Password</h1>\n          <p className=\"auth-subtitle\">\n            Enter your email address and create a new password\n          </p>\n        </div>\n        \n        <Formik\n          initialValues={{ email: '', newPassword: '', confirmPassword: '' }}\n          validationSchema={ForgotPasswordSchema}\n          onSubmit={handleSubmit}\n        >\n          {({ errors, touched, isSubmitting }) => (\n            <Form className=\"auth-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Email Address\n                </label>\n                <Field\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  className={`form-input ${errors.email && touched.email ? 'form-input-error' : ''}`}\n                  placeholder=\"Enter your email address\"\n                />\n                {errors.email && touched.email && (\n                  <div className=\"form-error\">{errors.email}</div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"newPassword\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                  </svg>\n                  New Password\n                </label>\n                <div className=\"form-input-container\">\n                  <Field\n                    id=\"newPassword\"\n                    name=\"newPassword\"\n                    type={showNewPassword ? \"text\" : \"password\"}\n                    className={`form-input ${errors.newPassword && touched.newPassword ? 'form-input-error' : ''}`}\n                    placeholder=\"Enter new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"form-input-toggle\"\n                    onClick={() => setShowNewPassword(!showNewPassword)}\n                  >\n                    {showNewPassword ? (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.newPassword && touched.newPassword && (\n                  <div className=\"form-error\">{errors.newPassword}</div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                  </svg>\n                  Confirm New Password\n                </label>\n                <div className=\"form-input-container\">\n                  <Field\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? \"text\" : \"password\"}\n                    className={`form-input ${errors.confirmPassword && touched.confirmPassword ? 'form-input-error' : ''}`}\n                    placeholder=\"Confirm new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"form-input-toggle\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.confirmPassword && touched.confirmPassword && (\n                  <div className=\"form-error\">{errors.confirmPassword}</div>\n                )}\n              </div>\n\n              {error && (\n                <div className=\"form-error-message\">\n                  {error}\n                </div>\n              )}\n\n              <button \n                type=\"submit\" \n                className=\"btn btn-primary btn-auth\"\n                disabled={isSubmitting}\n              >\n                {isSubmitting ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    Resetting Password...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    Reset Password\n                  </>\n                )}\n              </button>\n            </Form>\n          )}\n        </Formik>\n\n        <div className=\"auth-footer\">\n          <p className=\"auth-footer-text\">\n            Remember your password? \n            <Link to=\"/login\" className=\"auth-link\">\n              Back to Login\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,QAAQ;AAC5C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAE1B,MAAMC,oBAAoB,GAAGD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAC9CC,KAAK,EAAEJ,GAAG,CAACK,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,WAAW,EAAEP,GAAG,CAACK,MAAM,CAAC,CAAC,CACtBC,QAAQ,CAAC,0BAA0B,CAAC,CACpCE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,OAAO,CAAC,OAAO,EAAE,qDAAqD,CAAC,CACvEA,OAAO,CAAC,OAAO,EAAE,2CAA2C,CAAC,CAC7DA,OAAO,CAAC,YAAY,EAAE,iEAAiE,CAAC;EAC3FC,eAAe,EAAEV,GAAG,CAACK,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAACX,GAAG,CAACY,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC7DN,QAAQ,CAAC,8BAA8B;AAC5C,CAAC,CAAC;AAEF,OAAO,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM+B,YAAY,GAAGA,CAACC,MAAM,EAAE;IAAEC;EAAc,CAAC,KAAK;IAClD,MAAM;MAAEtB,KAAK;MAAEG;IAAY,CAAC,GAAGkB,MAAM;IACrCR,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,KAAK,CAAC;IAEjBzB,MAAM,CAACiC,IAAI,CAAC,sBAAsB,EAAE;MAAEvB,KAAK;MAAEG;IAAY,CAAC,EAAE,CAACqB,GAAG,EAAEC,MAAM,KAAK;MAC3EH,aAAa,CAAC,KAAK,CAAC;MACpB,IAAIE,GAAG,EAAE;QACPE,OAAO,CAACd,KAAK,CAAC,wBAAwB,EAAEY,GAAG,CAAC;QAC5CX,QAAQ,CAACW,GAAG,CAACG,MAAM,IAAI,6CAA6C,CAAC;MACvE,CAAC,MAAM;QACLZ,UAAU,CAAC,IAAI,CAAC;QAChBW,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAEH,MAAM,CAAC;MACnD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIX,OAAO,EAAE;IACX,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AACrC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAClC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACtC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AACtC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC5G,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,oIAAoI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC/O,cAAc,EAAE,GAAG;AACnB,YAAY,EAAE,GAAG;AACjB,YAAY,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,yBAAyB,EAAE,EAAE;AACpE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe;AACxC;AACA,YAAY,EAAE,CAAC;AACf,UAAU,EAAE,GAAG;AACf,UAAU,CAAC,IAAI,CACH,EAAE,CAAC,QAAQ,CACX,SAAS,CAAC,0BAA0B;AAEhD,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC/H,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,qMAAqM,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC9S,YAAY,EAAE,GAAG;AACjB;AACA,UAAU,EAAE,IAAI;AAChB,QAAQ,EAAE,GAAG;AACb,MAAM,EAAE,GAAG,CAAC;EAEV;EAEA,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB;AACnC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAChC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AACpC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AAC1G,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,kQAAkQ,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3W,YAAY,EAAE,GAAG;AACjB,UAAU,EAAE,GAAG;AACf,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE;AACvD,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe;AACtC;AACA,UAAU,EAAE,CAAC;AACb,QAAQ,EAAE,GAAG;AACb;AACA,QAAQ,CAAC,MAAM,CACL,aAAa,CAAC,CAAC;QAAEd,KAAK,EAAE,EAAE;QAAEG,WAAW,EAAE,EAAE;QAAEG,eAAe,EAAE;MAAG,CAAC,CAAC,CACnE,gBAAgB,CAAC,CAACT,oBAAoB,CAAC,CACvC,QAAQ,CAAC,CAACuB,YAAY,CAAC;AAEjC,UAAU,CAAC,CAAC;UAAES,MAAM;UAAEC,OAAO;UAAEC;QAAa,CAAC,KACjC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW;AACvC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY;AAC7D,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,sOAAsO,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACrV,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,OAAO,CACV,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CACZ,SAAS,CAAC,CAAC,cAAcF,MAAM,CAAC7B,KAAK,IAAI8B,OAAO,CAAC9B,KAAK,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC,CACnF,WAAW,CAAC,0BAA0B;AAExD,gBAAgB,CAAC6B,MAAM,CAAC7B,KAAK,IAAI8B,OAAO,CAAC9B,KAAK,IAC5B,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC6B,MAAM,CAAC7B,KAAK,CAAC,EAAE,GAAG,CAChD;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY;AACnE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjH,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACvF,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,+JAA+J,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjO,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,sBAAsB;AACrD,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,aAAa,CAChB,IAAI,CAAC,aAAa,CAClB,IAAI,CAAC,CAACgB,eAAe,GAAG,MAAM,GAAG,UAAU,CAAC,CAC5C,SAAS,CAAC,CAAC,cAAca,MAAM,CAAC1B,WAAW,IAAI2B,OAAO,CAAC3B,WAAW,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC,CAC/F,WAAW,CAAC,oBAAoB;AAEpD,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,mBAAmB,CAC7B,OAAO,CAAC,CAAC,MAAMc,kBAAkB,CAAC,CAACD,eAAe,CAAC,CAAC;AAExE,oBAAoB,CAACA,eAAe,GACd,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,mmBAAmmB,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACttB,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wEAAwE,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3L,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AAC3F,sBAAsB,EAAE,GAAG,CACN;AACrB,kBAAkB,EAAE,MAAM;AAC1B,gBAAgB,EAAE,GAAG;AACrB,gBAAgB,CAACa,MAAM,CAAC1B,WAAW,IAAI2B,OAAO,CAAC3B,WAAW,IACxC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC0B,MAAM,CAAC1B,WAAW,CAAC,EAAE,GAAG,CACtD;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;AACzC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAY;AACvE,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACtI,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjH,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACvF,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,+JAA+J,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AACjO,kBAAkB,EAAE,GAAG;AACvB;AACA,gBAAgB,EAAE,KAAK;AACvB,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,sBAAsB;AACrD,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,iBAAiB,CACpB,IAAI,CAAC,iBAAiB,CACtB,IAAI,CAAC,CAACe,mBAAmB,GAAG,MAAM,GAAG,UAAU,CAAC,CAChD,SAAS,CAAC,CAAC,cAAcW,MAAM,CAACvB,eAAe,IAAIwB,OAAO,CAACxB,eAAe,GAAG,kBAAkB,GAAG,EAAE,EAAE,CAAC,CACvG,WAAW,CAAC,sBAAsB;AAEtD,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,mBAAmB,CAC7B,OAAO,CAAC,CAAC,MAAMa,sBAAsB,CAAC,CAACD,mBAAmB,CAAC,CAAC;AAEhF,oBAAoB,CAACA,mBAAmB,GAClB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,mmBAAmmB,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACttB,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACpH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wEAAwE,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AAC3L,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG;AAC3F,sBAAsB,EAAE,GAAG,CACN;AACrB,kBAAkB,EAAE,MAAM;AAC1B,gBAAgB,EAAE,GAAG;AACrB,gBAAgB,CAACW,MAAM,CAACvB,eAAe,IAAIwB,OAAO,CAACxB,eAAe,IAChD,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAACuB,MAAM,CAACvB,eAAe,CAAC,EAAE,GAAG,CAC1D;AACjB,cAAc,EAAE,GAAG;AACnB;AACA,cAAc,CAACM,KAAK,IACJ,CAAC,GAAG,CAAC,SAAS,CAAC,oBAAoB;AACnD,kBAAkB,CAACA,KAAK;AACxB,gBAAgB,EAAE,GAAG,CACN;AACf;AACA,cAAc,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,0BAA0B,CACpC,QAAQ,CAAC,CAACmB,YAAY,CAAC;AAEvC,gBAAgB,CAACA,YAAY,GACX;AAClB,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI;AAC5D;AACA,kBAAkB,GAAG,GAEH;AAClB,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B;AACvI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,wDAAwD,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;AACzK,oBAAoB,EAAE,GAAG;AACzB;AACA,kBAAkB,GACD;AACjB,cAAc,EAAE,MAAM;AACtB,YAAY,EAAE,IAAI,CACP;AACX,QAAQ,EAAE,MAAM;AAChB;AACA,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;AACpC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB;AACzC;AACA,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW;AACnD;AACA,YAAY,EAAE,IAAI;AAClB,UAAU,EAAE,CAAC;AACb,QAAQ,EAAE,GAAG;AACb,MAAM,EAAE,GAAG;AACX,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACrB,EAAA,CApNWD,kBAAkB;EAAA,QACZlB,WAAW;AAAA;AAAAyC,EAAA,GADjBvB,kBAAkB;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}}}, "code": "!function (module1) {\n  module1.export({\n    ForgotPasswordPage: () => ForgotPasswordPage\n  });\n  let React, useState;\n  module1.link(\"react\", {\n    default(v) {\n      React = v;\n    },\n    useState(v) {\n      useState = v;\n    }\n  }, 0);\n  let Meteor;\n  module1.link(\"meteor/meteor\", {\n    Meteor(v) {\n      Meteor = v;\n    }\n  }, 1);\n  let useNavigate, Link;\n  module1.link(\"react-router-dom\", {\n    useNavigate(v) {\n      useNavigate = v;\n    },\n    Link(v) {\n      Link = v;\n    }\n  }, 2);\n  let Formik, Form, Field;\n  module1.link(\"formik\", {\n    Formik(v) {\n      Formik = v;\n    },\n    Form(v) {\n      Form = v;\n    },\n    Field(v) {\n      Field = v;\n    }\n  }, 3);\n  let Yup;\n  module1.link(\"yup\", {\n    \"*\"(v) {\n      Yup = v;\n    }\n  }, 4);\n  ___INIT_METEOR_FAST_REFRESH(module);\n  var _s = $RefreshSig$();\n  const ForgotPasswordSchema = Yup.object().shape({\n    email: Yup.string().email('Invalid email address').required('Email is required'),\n    newPassword: Yup.string().required('New password is required').min(8, 'Password must be at least 8 characters').matches(/[A-Z]/, 'Password must contain at least one uppercase letter').matches(/[0-9]/, 'Password must contain at least one number').matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\n    confirmPassword: Yup.string().oneOf([Yup.ref('newPassword'), null], 'Passwords must match').required('Confirm password is required')\n  });\n  const ForgotPasswordPage = () => {\n    _s();\n    const navigate = useNavigate();\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState(false);\n    const [showNewPassword, setShowNewPassword] = useState(false);\n    const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n    const handleSubmit = (values, _ref) => {\n      let {\n        setSubmitting\n      } = _ref;\n      const {\n        email,\n        newPassword\n      } = values;\n      setError('');\n      setSuccess(false);\n      Meteor.call('users.forgotPassword', {\n        email,\n        newPassword\n      }, (err, result) => {\n        setSubmitting(false);\n        if (err) {\n          console.error('Forgot password error:', err);\n          setError(err.reason || 'Failed to reset password. Please try again.');\n        } else {\n          setSuccess(true);\n          console.log('Password reset successful:', result);\n        }\n      });\n    };\n    if (success) {\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"auth-container\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"auth-card\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"auth-header\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"auth-logo\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"48\",\n        height: \"48\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }))), /*#__PURE__*/React.createElement(\"h1\", {\n        className: \"auth-title\"\n      }, \"Password Reset Successful\"), /*#__PURE__*/React.createElement(\"p\", {\n        className: \"auth-subtitle\"\n      }, \"Your password has been updated successfully. You can now log in with your new password.\")), /*#__PURE__*/React.createElement(Link, {\n        to: \"/login\",\n        className: \"btn btn-primary btn-auth\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"btn-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), \"Go to Login\")));\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-container\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-card\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-header\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-logo\"\n    }, /*#__PURE__*/React.createElement(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }, /*#__PURE__*/React.createElement(\"path\", {\n      d: \"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14ZM12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 5L17 7V9C16.45 9 16 9.45 16 10V16C16 16.55 16.45 17 17 17H19C19.55 17 20 16.55 20 16V10C20 9.45 19.55 9 19 9Z\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }))), /*#__PURE__*/React.createElement(\"h1\", {\n      className: \"auth-title\"\n    }, \"Reset Password\"), /*#__PURE__*/React.createElement(\"p\", {\n      className: \"auth-subtitle\"\n    }, \"Enter your email address and create a new password\")), /*#__PURE__*/React.createElement(Formik, {\n      initialValues: {\n        email: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      validationSchema: ForgotPasswordSchema,\n      onSubmit: handleSubmit\n    }, _ref2 => {\n      let {\n        errors,\n        touched,\n        isSubmitting\n      } = _ref2;\n      return /*#__PURE__*/React.createElement(Form, {\n        className: \"auth-form\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"email\",\n        className: \"form-label\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"form-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), \"Email Address\"), /*#__PURE__*/React.createElement(Field, {\n        id: \"email\",\n        name: \"email\",\n        type: \"email\",\n        className: \"form-input \".concat(errors.email && touched.email ? 'form-input-error' : ''),\n        placeholder: \"Enter your email address\"\n      }), errors.email && touched.email && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-error\"\n      }, errors.email)), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"newPassword\",\n        className: \"form-label\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"form-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: \"3\",\n        y: \"11\",\n        width: \"18\",\n        height: \"11\",\n        rx: \"2\",\n        ry: \"2\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      }), /*#__PURE__*/React.createElement(\"circle\", {\n        cx: \"12\",\n        cy: \"16\",\n        r: \"1\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      }), /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      })), \"New Password\"), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-input-container\"\n      }, /*#__PURE__*/React.createElement(Field, {\n        id: \"newPassword\",\n        name: \"newPassword\",\n        type: showNewPassword ? \"text\" : \"password\",\n        className: \"form-input \".concat(errors.newPassword && touched.newPassword ? 'form-input-error' : ''),\n        placeholder: \"Enter new password\"\n      }), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: \"form-input-toggle\",\n        onClick: () => setShowNewPassword(!showNewPassword)\n      }, showNewPassword ? /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })) : /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }), /*#__PURE__*/React.createElement(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"3\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      })))), errors.newPassword && touched.newPassword && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-error\"\n      }, errors.newPassword)), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"confirmPassword\",\n        className: \"form-label\"\n      }, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"form-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: \"3\",\n        y: \"11\",\n        width: \"18\",\n        height: \"11\",\n        rx: \"2\",\n        ry: \"2\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      }), /*#__PURE__*/React.createElement(\"circle\", {\n        cx: \"12\",\n        cy: \"16\",\n        r: \"1\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      }), /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      })), \"Confirm New Password\"), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-input-container\"\n      }, /*#__PURE__*/React.createElement(Field, {\n        id: \"confirmPassword\",\n        name: \"confirmPassword\",\n        type: showConfirmPassword ? \"text\" : \"password\",\n        className: \"form-input \".concat(errors.confirmPassword && touched.confirmPassword ? 'form-input-error' : ''),\n        placeholder: \"Confirm new password\"\n      }), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: \"form-input-toggle\",\n        onClick: () => setShowConfirmPassword(!showConfirmPassword)\n      }, showConfirmPassword ? /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })) : /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }), /*#__PURE__*/React.createElement(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"3\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\"\n      })))), errors.confirmPassword && touched.confirmPassword && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-error\"\n      }, errors.confirmPassword)), error && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-error-message\"\n      }, error), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"submit\",\n        className: \"btn btn-primary btn-auth\",\n        disabled: isSubmitting\n      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"loading-spinner\"\n      }), \"Resetting Password...\") : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"svg\", {\n        className: \"btn-icon\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })), \"Reset Password\")));\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-footer\"\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      className: \"auth-footer-text\"\n    }, \"Remember your password?\", /*#__PURE__*/React.createElement(Link, {\n      to: \"/login\",\n      className: \"auth-link\"\n    }, \"Back to Login\")))));\n  };\n  _s(ForgotPasswordPage, \"N3K2zgbyp+LO+AfhbTgSSknM50g=\", false, function () {\n    return [useNavigate];\n  });\n  _c = ForgotPasswordPage;\n  var _c;\n  $RefreshReg$(_c, \"ForgotPasswordPage\");\n}.call(this, module);", "map": {"version": 3, "names": ["ForgotPasswordSchema", "<PERSON><PERSON>", "object", "shape", "email", "string", "required", "newPassword", "min", "matches", "confirmPassword", "oneOf", "ref", "ForgotPasswordPage", "_s", "navigate", "useNavigate", "error", "setError", "useState", "success", "setSuccess", "showNewPassword", "setShowNewPassword", "showConfirmPassword", "setShowConfirmPassword", "handleSubmit", "values", "_ref", "setSubmitting", "Meteor", "call", "err", "result", "console", "reason", "log", "React", "createElement", "className", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "Link", "to", "<PERSON><PERSON>", "initialValues", "validationSchema", "onSubmit", "_ref2", "errors", "touched", "isSubmitting", "Form", "htmlFor", "Field", "id", "name", "type", "concat", "placeholder", "x", "y", "rx", "ry", "cx", "cy", "r", "onClick", "disabled", "Fragment", "_c", "$RefreshReg$", "module"], "sources": ["imports/ui/pages/ForgotPasswordPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Meteor } from 'meteor/meteor';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Formik, Form, Field } from 'formik';\nimport * as Yup from 'yup';\n\nconst ForgotPasswordSchema = Yup.object().shape({\n  email: Yup.string()\n    .email('Invalid email address')\n    .required('Email is required'),\n  newPassword: Yup.string()\n    .required('New password is required')\n    .min(8, 'Password must be at least 8 characters')\n    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')\n    .matches(/[0-9]/, 'Password must contain at least one number')\n    .matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\n  confirmPassword: Yup.string()\n    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')\n    .required('Confirm password is required'),\n});\n\nexport const ForgotPasswordPage = () => {\n  const navigate = useNavigate();\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState(false);\n  const [showNewPassword, setShowNewPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n\n  const handleSubmit = (values, { setSubmitting }) => {\n    const { email, newPassword } = values;\n    setError('');\n    setSuccess(false);\n\n    Meteor.call('users.forgotPassword', { email, newPassword }, (err, result) => {\n      setSubmitting(false);\n      if (err) {\n        console.error('Forgot password error:', err);\n        setError(err.reason || 'Failed to reset password. Please try again.');\n      } else {\n        setSuccess(true);\n        console.log('Password reset successful:', result);\n      }\n    });\n  };\n\n  if (success) {\n    return (\n      <div className=\"auth-container\">\n        <div className=\"auth-card\">\n          <div className=\"auth-header\">\n            <div className=\"auth-logo\">\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              </svg>\n            </div>\n            <h1 className=\"auth-title\">Password Reset Successful</h1>\n            <p className=\"auth-subtitle\">\n              Your password has been updated successfully. You can now log in with your new password.\n            </p>\n          </div>\n          <Link \n            to=\"/login\" \n            className=\"btn btn-primary btn-auth\"\n          >\n            <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Go to Login\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <div className=\"auth-logo\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14ZM12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 5L17 7V9C16.45 9 16 9.45 16 10V16C16 16.55 16.45 17 17 17H19C19.55 17 20 16.55 20 16V10C20 9.45 19.55 9 19 9Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n          <h1 className=\"auth-title\">Reset Password</h1>\n          <p className=\"auth-subtitle\">\n            Enter your email address and create a new password\n          </p>\n        </div>\n        \n        <Formik\n          initialValues={{ email: '', newPassword: '', confirmPassword: '' }}\n          validationSchema={ForgotPasswordSchema}\n          onSubmit={handleSubmit}\n        >\n          {({ errors, touched, isSubmitting }) => (\n            <Form className=\"auth-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                  Email Address\n                </label>\n                <Field\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  className={`form-input ${errors.email && touched.email ? 'form-input-error' : ''}`}\n                  placeholder=\"Enter your email address\"\n                />\n                {errors.email && touched.email && (\n                  <div className=\"form-error\">{errors.email}</div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"newPassword\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                  </svg>\n                  New Password\n                </label>\n                <div className=\"form-input-container\">\n                  <Field\n                    id=\"newPassword\"\n                    name=\"newPassword\"\n                    type={showNewPassword ? \"text\" : \"password\"}\n                    className={`form-input ${errors.newPassword && touched.newPassword ? 'form-input-error' : ''}`}\n                    placeholder=\"Enter new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"form-input-toggle\"\n                    onClick={() => setShowNewPassword(!showNewPassword)}\n                  >\n                    {showNewPassword ? (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.newPassword && touched.newPassword && (\n                  <div className=\"form-error\">{errors.newPassword}</div>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                  <svg className=\"form-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                  </svg>\n                  Confirm New Password\n                </label>\n                <div className=\"form-input-container\">\n                  <Field\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? \"text\" : \"password\"}\n                    className={`form-input ${errors.confirmPassword && touched.confirmPassword ? 'form-input-error' : ''}`}\n                    placeholder=\"Confirm new password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"form-input-toggle\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88M3 3L21 21\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    ) : (\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    )}\n                  </button>\n                </div>\n                {errors.confirmPassword && touched.confirmPassword && (\n                  <div className=\"form-error\">{errors.confirmPassword}</div>\n                )}\n              </div>\n\n              {error && (\n                <div className=\"form-error-message\">\n                  {error}\n                </div>\n              )}\n\n              <button \n                type=\"submit\" \n                className=\"btn btn-primary btn-auth\"\n                disabled={isSubmitting}\n              >\n                {isSubmitting ? (\n                  <>\n                    <span className=\"loading-spinner\"></span>\n                    Resetting Password...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"btn-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 14L8 10L9.41 8.59L12 11.17L14.59 8.59L16 10L12 14Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                    Reset Password\n                  </>\n                )}\n              </button>\n            </Form>\n          )}\n        </Formik>\n\n        <div className=\"auth-footer\">\n          <p className=\"auth-footer-text\">\n            Remember your password? \n            <Link to=\"/login\" className=\"auth-link\">\n              Back to Login\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAMA,MAAMA,oBAAoB,GAAGC,GAAG,CAACC,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC9CC,KAAK,EAAEH,GAAG,CAACI,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;IAChCC,WAAW,EAAEN,GAAG,CAACI,MAAM,CAAC,CAAC,CACtBC,QAAQ,CAAC,0BAA0B,CAAC,CACpCE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,OAAO,CAAC,OAAO,EAAE,qDAAqD,CAAC,CACvEA,OAAO,CAAC,OAAO,EAAE,2CAA2C,CAAC,CAC7DA,OAAO,CAAC,YAAY,EAAE,iEAAiE,CAAC;IAC3FC,eAAe,EAAET,GAAG,CAACI,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAACV,GAAG,CAACW,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC7DN,QAAQ,CAAC,8BAA8B;EAC5C,CAAC,CAAC;EAEK,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAAAC,EAAA;IACtC,MAAMC,QAAQ,GAAGC,WAAW,CAAC,CAAC;IAC9B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGC,QAAQ,CAAC,EAAE,CAAC;IACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGF,QAAQ,CAAC,KAAK,CAAC;IAC7C,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGJ,QAAQ,CAAC,KAAK,CAAC;IAC7D,MAAM,CAACK,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;IAErE,MAAMO,YAAY,GAAGA,CAACC,MAAM,EAAAC,IAAA,KAAwB;MAAA,IAAtB;QAAEC;MAAc,CAAC,GAAAD,IAAA;MAC7C,MAAM;QAAExB,KAAK;QAAEG;MAAY,CAAC,GAAGoB,MAAM;MACrCT,QAAQ,CAAC,EAAE,CAAC;MACZG,UAAU,CAAC,KAAK,CAAC;MAEjBS,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAE;QAAE3B,KAAK;QAAEG;MAAY,CAAC,EAAE,CAACyB,GAAG,EAAEC,MAAM,KAAK;QAC3EJ,aAAa,CAAC,KAAK,CAAC;QACpB,IAAIG,GAAG,EAAE;UACPE,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;UAC5Cd,QAAQ,CAACc,GAAG,CAACG,MAAM,IAAI,6CAA6C,CAAC;QACvE,CAAC,MAAM;UACLd,UAAU,CAAC,IAAI,CAAC;UAChBa,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAEH,MAAM,CAAC;QACnD;MACF,CAAC,CAAC;IACJ,CAAC;IAED,IAAIb,OAAO,EAAE;MACX,oBACEiB,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAgB,gBAC7BF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAW,gBACxBF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAa,gBAC1BF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAW,gBACxBF,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,oIAAoI;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC5N,CACF,gBACLZ,KAAA,CAAAC,aAAA;QAAIC,SAAS,EAAC;MAAY,GAAC,2BAA6B,gBACxDF,KAAA,CAAAC,aAAA;QAAGC,SAAS,EAAC;MAAe,4FAEzB,CACA,gBACLF,KAAA,CAAAC,aAAA,CAACY,IAAI;QACHC,EAAE,EAAC,QAAQ;QACXZ,SAAS,EAAC;MAA0B,gBAEpCF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC,UAAU;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBACjHP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,qMAAqM;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC7R,gBAED,CACH,CACF,CAAC;IAEV;IAEA,oBACEZ,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAgB,gBAC7BF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAW,gBACxBF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAa,gBAC1BF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAW,gBACxBF,KAAA,CAAAC,aAAA;MAAKE,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC;IAA4B,gBAC5FP,KAAA,CAAAC,aAAA;MAAMO,CAAC,EAAC,kQAAkQ;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAACC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO,EAC1V,CACF,gBACLZ,KAAA,CAAAC,aAAA;MAAIC,SAAS,EAAC;IAAY,GAAC,gBAAkB,gBAC7CF,KAAA,CAAAC,aAAA;MAAGC,SAAS,EAAC;IAAe,uDAEzB,CACA,gBAELF,KAAA,CAAAC,aAAA,CAACc,MAAM;MACLC,aAAa,EAAE;QAAEjD,KAAK,EAAE,EAAE;QAAEG,WAAW,EAAE,EAAE;QAAEG,eAAe,EAAE;MAAG,CAAE;MACnE4C,gBAAgB,EAAEtD,oBAAqB;MACvCuD,QAAQ,EAAE7B;IAAa,GAEtB8B,KAAA;MAAA,IAAC;QAAEC,MAAM;QAAEC,OAAO;QAAEC;MAAa,CAAC,GAAAH,KAAA;MAAA,oBACjCnB,KAAA,CAAAC,aAAA,CAACsB,IAAI;QAACrB,SAAS,EAAC;MAAW,gBACzBF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,gBACzBF,KAAA,CAAAC,aAAA;QAAOuB,OAAO,EAAC,OAAO;QAACtB,SAAS,EAAC;MAAY,gBAC3CF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC,WAAW;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAClHP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,sOAAsO;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC9T,kBAEA,gBACPZ,KAAA,CAAAC,aAAA,CAACwB,KAAK;QACJC,EAAE,EAAC,OAAO;QACVC,IAAI,EAAC,OAAO;QACZC,IAAI,EAAC,OAAO;QACZ1B,SAAS,gBAAA2B,MAAA,CAAgBT,MAAM,CAACrD,KAAK,IAAIsD,OAAO,CAACtD,KAAK,GAAG,kBAAkB,GAAG,EAAE,CAAG;QACnF+D,WAAW,EAAC;MAA0B,IAEvCV,MAAM,CAACrD,KAAK,IAAIsD,OAAO,CAACtD,KAAK,iBAC5BiC,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,GAAEkB,MAAM,CAACrD,KAAW,CAE9C,gBAELiC,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,gBACzBF,KAAA,CAAAC,aAAA;QAAOuB,OAAO,EAAC,aAAa;QAACtB,SAAS,EAAC;MAAY,gBACjDF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC,WAAW;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAClHP,KAAA,CAAAC,aAAA;QAAM8B,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,IAAI;QAAC7B,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAAC6B,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,GAAG;QAACzB,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,iBAC7FV,KAAA,CAAAC,aAAA;QAAQkC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAAC5B,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,iBACnEV,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,+JAA+J;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,EAC1M,iBAEA,gBACPV,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAsB,gBACnCF,KAAA,CAAAC,aAAA,CAACwB,KAAK;QACJC,EAAE,EAAC,aAAa;QAChBC,IAAI,EAAC,aAAa;QAClBC,IAAI,EAAE3C,eAAe,GAAG,MAAM,GAAG,UAAW;QAC5CiB,SAAS,gBAAA2B,MAAA,CAAgBT,MAAM,CAAClD,WAAW,IAAImD,OAAO,CAACnD,WAAW,GAAG,kBAAkB,GAAG,EAAE,CAAG;QAC/F4D,WAAW,EAAC;MAAoB,iBAElC9B,KAAA,CAAAC,aAAA;QACE2B,IAAI,EAAC,QAAQ;QACb1B,SAAS,EAAC,mBAAmB;QAC7BoC,OAAO,EAAEA,CAAA,KAAMpD,kBAAkB,CAAC,CAACD,eAAe;MAAE,GAEnDA,eAAe,gBACde,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,mmBAAmmB;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC3rB,CAAC,gBAENZ,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,wEAAwE;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,iBACnKZ,KAAA,CAAAC,aAAA;QAAQkC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAAC5B,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,EAChE,CAED,CACL,GACJU,MAAM,CAAClD,WAAW,IAAImD,OAAO,CAACnD,WAAW,iBACxC8B,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,GAAEkB,MAAM,CAAClD,WAAiB,CAEpD,gBAEL8B,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,gBACzBF,KAAA,CAAAC,aAAA;QAAOuB,OAAO,EAAC,iBAAiB;QAACtB,SAAS,EAAC;MAAY,gBACrDF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC,WAAW;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAClHP,KAAA,CAAAC,aAAA;QAAM8B,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,IAAI;QAAC7B,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAAC6B,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,GAAG;QAACzB,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,iBAC7FV,KAAA,CAAAC,aAAA;QAAQkC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAAC5B,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,iBACnEV,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,+JAA+J;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,EAC1M,yBAEA,gBACPV,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAsB,gBACnCF,KAAA,CAAAC,aAAA,CAACwB,KAAK;QACJC,EAAE,EAAC,iBAAiB;QACpBC,IAAI,EAAC,iBAAiB;QACtBC,IAAI,EAAEzC,mBAAmB,GAAG,MAAM,GAAG,UAAW;QAChDe,SAAS,gBAAA2B,MAAA,CAAgBT,MAAM,CAAC/C,eAAe,IAAIgD,OAAO,CAAChD,eAAe,GAAG,kBAAkB,GAAG,EAAE,CAAG;QACvGyD,WAAW,EAAC;MAAsB,iBAEpC9B,KAAA,CAAAC,aAAA;QACE2B,IAAI,EAAC,QAAQ;QACb1B,SAAS,EAAC,mBAAmB;QAC7BoC,OAAO,EAAEA,CAAA,KAAMlD,sBAAsB,CAAC,CAACD,mBAAmB;MAAE,GAE3DA,mBAAmB,gBAClBa,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,mmBAAmmB;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC3rB,CAAC,gBAENZ,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,wEAAwE;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,iBACnKZ,KAAA,CAAAC,aAAA;QAAQkC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAAC5B,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC;MAAG,EAChE,CAED,CACL,GACJU,MAAM,CAAC/C,eAAe,IAAIgD,OAAO,CAAChD,eAAe,iBAChD2B,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,GAAEkB,MAAM,CAAC/C,eAAqB,CAExD,GAEJO,KAAK,iBACJoB,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAoB,GAChCtB,KACE,CACN,eAEDoB,KAAA,CAAAC,aAAA;QACE2B,IAAI,EAAC,QAAQ;QACb1B,SAAS,EAAC,0BAA0B;QACpCqC,QAAQ,EAAEjB;MAAa,GAEtBA,YAAY,gBACXtB,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAwC,QAAA,qBACExC,KAAA,CAAAC,aAAA;QAAMC,SAAS,EAAC;MAAiB,CAAO,0BAE1C,CAAG,gBAEHF,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAwC,QAAA,qBACExC,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC,UAAU;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBACjHP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,wDAAwD;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAChJ,mBAEP,CAEI,CACJ,CACP;IAAA,CACK,gBAERZ,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAa,gBAC1BF,KAAA,CAAAC,aAAA;MAAGC,SAAS,EAAC;IAAkB,4BAE7B,eAAAF,KAAA,CAAAC,aAAA,CAACY,IAAI;MAACC,EAAE,EAAC,QAAQ;MAACZ,SAAS,EAAC;IAAW,kBAEjC,CACL,CACA,CACF,CACF,CAAC;EAEV,CAAC;EAACzB,EAAA,CApNWD,kBAAkB;IAAA,QACZG,WAAW;EAAA;EAAA8D,EAAA,GADjBjE,kBAAkB;EAAA,IAAAiE,EAAA;EAAAC,YAAA,CAAAD,EAAA;AAAA,EAAA/C,IAAA,OAAAiD,MAAA", "ignoreList": []}, "sourceType": "module", "externalDependencies": {}, "hash": "630702e7d2fcf53dcd98a10f88f26803655313b2"}