import React from 'react';
import { Meteor } from 'meteor/meteor';
import { useTracker } from 'meteor/react-meteor-data';
import { Tasks } from '/imports/api/tasks';
import { useNavigate } from 'react-router-dom';

export const TaskAssignmentHistory = ({ memberId }) => {
  const navigate = useNavigate();
  const { tasks, isLoading } = useTracker(() => {
    const handle = Meteor.subscribe('tasks');
    const isLoading = !handle.ready();
    
    if (isLoading) {
      return { tasks: [], isLoading };
    }

    const tasks = Tasks.find(
      { assignedTo: memberId },
      { sort: { createdAt: -1 } }
    ).fetch();

    return { tasks, isLoading };
  }, [memberId]);

  const handleTaskClick = (taskId) => {
    navigate(`/admin-dashboard/tasks/${taskId}`, { 
      state: { 
        from: `/admin-dashboard/team/${memberId}`,
        memberId: memberId 
      } 
    });
  };

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: '#ffffff',
      borderRadius: '8px',
      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '20px',
        borderBottom: '1px solid #e2e8f0'
      }}>
        <h3 style={{ 
          fontSize: '1rem', 
          color: '#0f172a',
          fontWeight: '600'
        }}>Task Assignment History</h3>
      </div>
      <div style={{
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }}>
        {tasks.length > 0 ? (
          tasks.map(task => (
            <div 
              key={task._id}
              style={{
                padding: '12px',
                backgroundColor: '#f8fafc',
                borderRadius: '6px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                transition: 'all 0.2s ease'
              }}
            >
              <span style={{ 
                fontSize: '0.875rem',
                color: '#0f172a',
                fontWeight: '500'
              }}>{task.title}</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleTaskClick(task._id);
                }}
                className="ml-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors border border-gray-200 bg-white shadow-sm"
                title="View task details"
                style={{ minWidth: '32px', minHeight: '32px' }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          ))
        ) : (
          <div style={{
            padding: '12px',
            textAlign: 'center',
            color: '#64748b',
            fontSize: '0.875rem'
          }}>
            No tasks assigned yet
          </div>
        )}
      </div>
    </div>
  );
}; 