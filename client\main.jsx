import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Meteor } from 'meteor/meteor';
import { App } from '/imports/ui/App';
import '/imports/api';  // Import collections

// Add some CSS for the loading spinner
const style = document.createElement('style');
style.textContent = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(style);

// Enable hot reloading in development
if (module.hot) {
  module.hot.accept();
}

Meteor.startup(() => {
  const container = document.getElementById('react-target');
  const root = createRoot(container);
  root.render(
    <BrowserRouter>
      <App />
    </BrowserRouter>
  );
});