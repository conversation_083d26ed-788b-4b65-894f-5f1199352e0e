{"version": 3, "sources": ["meteor://💻app/packages/mongo/local_collection_driver.js", "meteor://💻app/packages/mongo/collection/collection.js", "meteor://💻app/packages/mongo/collection/collection_utils.js", "meteor://💻app/packages/mongo/collection/methods_async.js", "meteor://💻app/packages/mongo/collection/methods_index.js", "meteor://💻app/packages/mongo/collection/methods_replication.js", "meteor://💻app/packages/mongo/collection/methods_sync.js", "meteor://💻app/packages/mongo/mongo_utils.js"], "names": ["module", "export", "LocalCollectionDriver", "constructor", "noConnCollections", "Object", "create", "open", "name", "conn", "LocalCollection", "ensureCollection", "_mongo_livedata_collections", "collections", "_objectSpread", "module1", "link", "default", "v", "normalizeProjection", "AsyncMethods", "SyncMethods", "IndexMethods", "ID_GENERATORS", "normalizeOptions", "setupAutopublish", "setupConnection", "setupDriver", "setupMutationMethods", "validateCollectionName", "ReplicationMethods", "Mongo", "Collection", "options", "_ID_GENERATORS$option", "_ID_GENERATORS", "_makeNewID", "idGeneration", "call", "_transform", "wrapTransform", "transform", "resolverType", "_connection", "driver", "_driver", "_collection", "_name", "_settingUpReplicationPromise", "_maybeSetUpReplication", "_collections", "set", "assign", "prototype", "_getFindSelector", "args", "length", "_getFindOptions", "newOptions", "self", "check", "Match", "Optional", "ObjectIncluding", "projection", "OneOf", "undefined", "sort", "Array", "Function", "limit", "Number", "skip", "_publishCursor", "cursor", "sub", "collection", "<PERSON><PERSON><PERSON><PERSON>", "observe<PERSON>hanges", "added", "id", "fields", "changed", "removed", "nonMutatingCallbacks", "onStop", "stop", "_rewriteSelector", "selector", "fallbackId", "arguments", "_selectorIsId", "_id", "isArray", "Error", "Random", "_isRemoteCollection", "Meteor", "server", "dropCollectionAsync", "createCappedCollectionAsync", "byteSize", "maxDocuments", "rawCollection", "rawDatabase", "mongo", "db", "getCollection", "get", "Map", "ObjectID", "MongoID", "<PERSON><PERSON><PERSON>", "AllowDeny", "CollectionPrototype", "MONGO", "src", "DDP", "randomStream", "insecure", "hexString", "STRING", "connection", "isClient", "MongoInternals", "defaultRemoteCollectionDriver", "require", "Package", "autopublish", "_preventAutopublish", "publish", "find", "is_auto", "defineMutationMethods", "_defineMutationMethods", "useExisting", "_suppressSameNameError", "error", "message", "concat", "_debug", "methods", "manager", "findOneAsync", "_len", "_key", "_insertAsync", "doc", "getPrototypeOf", "getOwnPropertyDescriptors", "generateId", "enclosing", "_CurrentMethodInvocation", "chooseReturnValueFromCollectionResult", "result", "_isPromise", "promise", "_callMutatorMethodAsync", "then", "stubPromise", "serverPromise", "insertAsync", "updateAsync", "modifier", "insertedId", "upsert", "generatedId", "removeAsync", "upsertAsync", "_returnObject", "countDocuments", "estimatedDocumentCount", "ensureIndexAsync", "index", "createIndexAsync", "Log", "debug", "JSON", "stringify", "e", "_Meteor$settings", "_Meteor$settings$pack", "_Meteor$settings$pack2", "includes", "settings", "packages", "reCreateIndexOnOptionMismatch", "info", "dropIndexAsync", "console", "createIndex", "_registerStoreResult", "_registerStoreResult$", "registerStoreClient", "registerStoreServer", "wrappedStoreCommon", "saveOriginals", "retrieveOriginals", "_getCollection", "wrappedStoreClient", "beginUpdate", "batchSize", "reset", "pauseObservers", "remove", "update", "msg", "mongoId", "idParse", "_docs", "_ref", "field", "value", "replace", "insert", "keys", "for<PERSON>ach", "key", "EJSON", "equals", "$unset", "$set", "endUpdate", "resumeObserversClient", "getDoc", "findOne", "wrappedStoreServer", "resumeObserversServer", "registerStoreResult", "log<PERSON>arn", "warn", "log", "ok", "_len2", "_key2", "_insert", "callback", "wrappedCallback", "wrapCallback", "_callMutatorMethod", "_len3", "optionsAndCallback", "_key3", "popCallbackFromArgs", "convertResult", "pop", "_objectWithoutProperties", "otherOptions", "_excluded"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,qBAAqB,EAACA,CAAA,KAAIA;AAAqB,CAAC,CAAC;AACzD,MAAMA,qBAAqB,GAAG,IAAK,MAAMA,qBAAqB,CAAC;EACpEC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9C;EAEAC,IAAIA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACf,IAAI,CAAED,IAAI,EAAE;MACV,OAAO,IAAIE,eAAe,CAAD,CAAC;IAC5B;IAEA,IAAI,CAAED,IAAI,EAAE;MACV,OAAOE,gBAAgB,CAACH,IAAI,EAAE,IAAI,CAACJ,iBAAiB,CAAC;IACvD;IAEA,IAAI,CAAEK,IAAI,CAACG,2BAA2B,EAAE;MACtCH,IAAI,CAACG,2BAA2B,GAAGP,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACxD;;IAEA;IACA;IACA,OAAOK,gBAAgB,CAACH,IAAI,EAAEC,IAAI,CAACG,2BAA2B,CAAC;EACjE;AACF,CAAC,EAAC;AAEF,SAASD,gBAAgBA,CAACH,IAAI,EAAEK,WAAW,EAAE;EAC3C,OAAQL,IAAI,IAAIK,WAAW,GACvBA,WAAW,CAACL,IAAI,CAAC,GACjBK,WAAW,CAACL,IAAI,CAAC,GAAG,IAAIE,eAAe,CAACF,IAAI,CAAC;AACnD,C;;;;;;;;;;;;EC7BA,IAAIM,aAAa;EAACC,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;IAACC,OAAOA,CAACC,CAAC,EAAC;MAACJ,aAAa,GAACI,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAtG,IAAIC,mBAAmB;EAACJ,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAC;IAACG,mBAAmBA,CAACD,CAAC,EAAC;MAACC,mBAAmB,GAACD,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIE,YAAY;EAACL,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAC;IAACI,YAAYA,CAACF,CAAC,EAAC;MAACE,YAAY,GAACF,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIG,WAAW;EAACN,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAC;IAACK,WAAWA,CAACH,CAAC,EAAC;MAACG,WAAW,GAACH,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAII,YAAY;EAACP,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAC;IAACM,YAAYA,CAACJ,CAAC,EAAC;MAACI,YAAY,GAACJ,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIK,aAAa,EAACC,gBAAgB,EAACC,gBAAgB,EAACC,eAAe,EAACC,WAAW,EAACC,oBAAoB,EAACC,sBAAsB;EAACd,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAC;IAACO,aAAaA,CAACL,CAAC,EAAC;MAACK,aAAa,GAACL,CAAC;IAAA,CAAC;IAACM,gBAAgBA,CAACN,CAAC,EAAC;MAACM,gBAAgB,GAACN,CAAC;IAAA,CAAC;IAACO,gBAAgBA,CAACP,CAAC,EAAC;MAACO,gBAAgB,GAACP,CAAC;IAAA,CAAC;IAACQ,eAAeA,CAACR,CAAC,EAAC;MAACQ,eAAe,GAACR,CAAC;IAAA,CAAC;IAACS,WAAWA,CAACT,CAAC,EAAC;MAACS,WAAW,GAACT,CAAC;IAAA,CAAC;IAACU,oBAAoBA,CAACV,CAAC,EAAC;MAACU,oBAAoB,GAACV,CAAC;IAAA,CAAC;IAACW,sBAAsBA,CAACX,CAAC,EAAC;MAACW,sBAAsB,GAACX,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAIY,kBAAkB;EAACf,OAAO,CAACC,IAAI,CAAC,uBAAuB,EAAC;IAACc,kBAAkBA,CAACZ,CAAC,EAAC;MAACY,kBAAkB,GAACZ,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAc74B;AACA;AACA;AACA;EACAa,KAAK,GAAG,CAAC,CAAC;;EAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA;EACAA,KAAK,CAACC,UAAU,GAAG,SAASA,UAAUA,CAACxB,IAAI,EAAEyB,OAAO,EAAE;IAAA,IAAAC,qBAAA,EAAAC,cAAA;IACpD3B,IAAI,GAAGqB,sBAAsB,CAACrB,IAAI,CAAC;IAEnCyB,OAAO,GAAGT,gBAAgB,CAACS,OAAO,CAAC;IAEnC,IAAI,CAACG,UAAU,IAAAF,qBAAA,GAAG,CAAAC,cAAA,GAAAZ,aAAa,EAACU,OAAO,CAACI,YAAY,CAAC,cAAAH,qBAAA,uBAAnCA,qBAAA,CAAAI,IAAA,CAAAH,cAAA,EAAsC3B,IAAI,CAAC;IAE7D,IAAI,CAAC+B,UAAU,GAAG7B,eAAe,CAAC8B,aAAa,CAACP,OAAO,CAACQ,SAAS,CAAC;IAClE,IAAI,CAACC,YAAY,GAAGT,OAAO,CAACS,YAAY;IAExC,IAAI,CAACC,WAAW,GAAGjB,eAAe,CAAClB,IAAI,EAAEyB,OAAO,CAAC;IAEjD,MAAMW,MAAM,GAAGjB,WAAW,CAACnB,IAAI,EAAE,IAAI,CAACmC,WAAW,EAAEV,OAAO,CAAC;IAC3D,IAAI,CAACY,OAAO,GAAGD,MAAM;IAErB,IAAI,CAACE,WAAW,GAAGF,MAAM,CAACrC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACmC,WAAW,CAAC;IACtD,IAAI,CAACI,KAAK,GAAGvC,IAAI;IAEjB,IAAI,CAACwC,4BAA4B,GAAG,IAAI,CAACC,sBAAsB,CAACzC,IAAI,EAAEyB,OAAO,CAAC;IAE9EL,oBAAoB,CAAC,IAAI,EAAEpB,IAAI,EAAEyB,OAAO,CAAC;IAEzCR,gBAAgB,CAAC,IAAI,EAAEjB,IAAI,EAAEyB,OAAO,CAAC;IAErCF,KAAK,CAACmB,YAAY,CAACC,GAAG,CAAC3C,IAAI,EAAE,IAAI,CAAC;EACpC,CAAC;EAEDH,MAAM,CAAC+C,MAAM,CAACrB,KAAK,CAACC,UAAU,CAACqB,SAAS,EAAE;IACxCC,gBAAgBA,CAACC,IAAI,EAAE;MACrB,IAAIA,IAAI,CAACC,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAC3B,OAAOD,IAAI,CAAC,CAAC,CAAC;IACrB,CAAC;IAEDE,eAAeA,CAACF,IAAI,EAAE;MACpB,MAAM,GAAGtB,OAAO,CAAC,GAAGsB,IAAI,IAAI,EAAE;MAC9B,MAAMG,UAAU,GAAGvC,mBAAmB,CAACc,OAAO,CAAC;MAE/C,IAAI0B,IAAI,GAAG,IAAI;MACf,IAAIJ,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACnB,OAAO;UAAEf,SAAS,EAAEkB,IAAI,CAACpB;QAAW,CAAC;MACvC,CAAC,MAAM;QACLqB,KAAK,CACHF,UAAU,EACVG,KAAK,CAACC,QAAQ,CACZD,KAAK,CAACE,eAAe,CAAC;UACpBC,UAAU,EAAEH,KAAK,CAACC,QAAQ,CAACD,KAAK,CAACI,KAAK,CAAC5D,MAAM,EAAE6D,SAAS,CAAC,CAAC;UAC1DC,IAAI,EAAEN,KAAK,CAACC,QAAQ,CAClBD,KAAK,CAACI,KAAK,CAAC5D,MAAM,EAAE+D,KAAK,EAAEC,QAAQ,EAAEH,SAAS,CAChD,CAAC;UACDI,KAAK,EAAET,KAAK,CAACC,QAAQ,CAACD,KAAK,CAACI,KAAK,CAACM,MAAM,EAAEL,SAAS,CAAC,CAAC;UACrDM,IAAI,EAAEX,KAAK,CAACC,QAAQ,CAACD,KAAK,CAACI,KAAK,CAACM,MAAM,EAAEL,SAAS,CAAC;QACrD,CAAC,CACH,CACF,CAAC;QAED,OAAApD,aAAA;UACE2B,SAAS,EAAEkB,IAAI,CAACpB;QAAU,GACvBmB,UAAU;MAEjB;IACF;EACF,CAAC,CAAC;EAEFrD,MAAM,CAAC+C,MAAM,CAACrB,KAAK,CAACC,UAAU,EAAE;IAC9B,MAAMyC,cAAcA,CAACC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAE;MAC5C,IAAIC,aAAa,GAAG,MAAMH,MAAM,CAACI,cAAc,CAC3C;QACEC,KAAK,EAAE,SAAAA,CAASC,EAAE,EAAEC,MAAM,EAAE;UAC1BN,GAAG,CAACI,KAAK,CAACH,UAAU,EAAEI,EAAE,EAAEC,MAAM,CAAC;QACnC,CAAC;QACDC,OAAO,EAAE,SAAAA,CAASF,EAAE,EAAEC,MAAM,EAAE;UAC5BN,GAAG,CAACO,OAAO,CAACN,UAAU,EAAEI,EAAE,EAAEC,MAAM,CAAC;QACrC,CAAC;QACDE,OAAO,EAAE,SAAAA,CAASH,EAAE,EAAE;UACpBL,GAAG,CAACQ,OAAO,CAACP,UAAU,EAAEI,EAAE,CAAC;QAC7B;MACF,CAAC;MACD;MACA;MACA;QAAEI,oBAAoB,EAAE;MAAK,CACjC,CAAC;;MAED;MACA;;MAEA;MACAT,GAAG,CAACU,MAAM,CAAC,kBAAiB;QAC1B,OAAO,MAAMR,aAAa,CAACS,IAAI,CAAC,CAAC;MACnC,CAAC,CAAC;;MAEF;MACA,OAAOT,aAAa;IACtB,CAAC;IAED;IACA;IACA;IACA;IACA;IACAU,gBAAgBA,CAACC,QAAQ,EAAuB;MAAA,IAArB;QAAEC;MAAW,CAAC,GAAAC,SAAA,CAAAlC,MAAA,QAAAkC,SAAA,QAAAxB,SAAA,GAAAwB,SAAA,MAAG,CAAC,CAAC;MAC5C;MACA,IAAIhF,eAAe,CAACiF,aAAa,CAACH,QAAQ,CAAC,EAAEA,QAAQ,GAAG;QAAEI,GAAG,EAAEJ;MAAS,CAAC;MAEzE,IAAIpB,KAAK,CAACyB,OAAO,CAACL,QAAQ,CAAC,EAAE;QAC3B;QACA;QACA,MAAM,IAAIM,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEA,IAAI,CAACN,QAAQ,IAAK,KAAK,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACI,GAAI,EAAE;QACrD;QACA,OAAO;UAAEA,GAAG,EAAEH,UAAU,IAAIM,MAAM,CAACf,EAAE,CAAC;QAAE,CAAC;MAC3C;MAEA,OAAOQ,QAAQ;IACjB;EACF,CAAC,CAAC;EAEFnF,MAAM,CAAC+C,MAAM,CAACrB,KAAK,CAACC,UAAU,CAACqB,SAAS,EAAEvB,kBAAkB,EAAET,WAAW,EAAED,YAAY,EAAEE,YAAY,CAAC;EAEtGjB,MAAM,CAAC+C,MAAM,CAACrB,KAAK,CAACC,UAAU,CAACqB,SAAS,EAAE;IACxC;IACA;IACA2C,mBAAmBA,CAAA,EAAG;MACpB;MACA,OAAO,IAAI,CAACrD,WAAW,IAAI,IAAI,CAACA,WAAW,KAAKsD,MAAM,CAACC,MAAM;IAC/D,CAAC;IAED,MAAMC,mBAAmBA,CAAA,EAAG;MAC1B,IAAIxC,IAAI,GAAG,IAAI;MACf,IAAI,CAACA,IAAI,CAACb,WAAW,CAACqD,mBAAmB,EACvC,MAAM,IAAIL,KAAK,CAAC,yDAAyD,CAAC;MAC7E,MAAMnC,IAAI,CAACb,WAAW,CAACqD,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAED,MAAMC,2BAA2BA,CAACC,QAAQ,EAAEC,YAAY,EAAE;MACxD,IAAI3C,IAAI,GAAG,IAAI;MACf,IAAI,EAAE,MAAMA,IAAI,CAACb,WAAW,CAACsD,2BAA2B,GACtD,MAAM,IAAIN,KAAK,CACb,iEACF,CAAC;MACH,MAAMnC,IAAI,CAACb,WAAW,CAACsD,2BAA2B,CAACC,QAAQ,EAAEC,YAAY,CAAC;IAC5E,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;IACEC,aAAaA,CAAA,EAAG;MACd,IAAI5C,IAAI,GAAG,IAAI;MACf,IAAI,CAACA,IAAI,CAACb,WAAW,CAACyD,aAAa,EAAE;QACnC,MAAM,IAAIT,KAAK,CAAC,mDAAmD,CAAC;MACtE;MACA,OAAOnC,IAAI,CAACb,WAAW,CAACyD,aAAa,CAAC,CAAC;IACzC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;IACEC,WAAWA,CAAA,EAAG;MACZ,IAAI7C,IAAI,GAAG,IAAI;MACf,IAAI,EAAEA,IAAI,CAACd,OAAO,CAAC4D,KAAK,IAAI9C,IAAI,CAACd,OAAO,CAAC4D,KAAK,CAACC,EAAE,CAAC,EAAE;QAClD,MAAM,IAAIZ,KAAK,CAAC,iDAAiD,CAAC;MACpE;MACA,OAAOnC,IAAI,CAACd,OAAO,CAAC4D,KAAK,CAACC,EAAE;IAC9B;EACF,CAAC,CAAC;EAEFrG,MAAM,CAAC+C,MAAM,CAACrB,KAAK,EAAE;IACnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE4E,aAAaA,CAACnG,IAAI,EAAE;MAClB,OAAO,IAAI,CAAC0C,YAAY,CAAC0D,GAAG,CAACpG,IAAI,CAAC;IACpC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;IACE0C,YAAY,EAAE,IAAI2D,GAAG,CAAC;EACxB,CAAC,CAAC;;EAIF;AACA;AACA;AACA;AACA;AACA;EACA9E,KAAK,CAAC+E,QAAQ,GAAGC,OAAO,CAACD,QAAQ;;EAEjC;AACA;AACA;AACA;AACA;EACA/E,KAAK,CAACiF,MAAM,GAAGtG,eAAe,CAACsG,MAAM;;EAErC;AACA;AACA;EACAjF,KAAK,CAACC,UAAU,CAACgF,MAAM,GAAGjF,KAAK,CAACiF,MAAM;;EAEtC;AACA;AACA;EACAjF,KAAK,CAACC,UAAU,CAAC8E,QAAQ,GAAG/E,KAAK,CAAC+E,QAAQ;;EAE1C;AACA;AACA;EACAb,MAAM,CAACjE,UAAU,GAAGD,KAAK,CAACC,UAAU;;EAEpC;EACA3B,MAAM,CAAC+C,MAAM,CAACrB,KAAK,CAACC,UAAU,CAACqB,SAAS,EAAE4D,SAAS,CAACC,mBAAmB,CAAC;AAAC,EAAA5E,IAAA,OAAAtC,MAAA,E;;;;;;;;;;;AC1QzE,IAAIc,aAAa;AAACd,MAAM,CAACgB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGlB,MAAM,CAACC,MAAM,CAAC;EAACsB,aAAa,EAACA,CAAA,KAAIA,aAAa;EAACG,eAAe,EAACA,CAAA,KAAIA,eAAe;EAACC,WAAW,EAACA,CAAA,KAAIA,WAAW;EAACF,gBAAgB,EAACA,CAAA,KAAIA,gBAAgB;EAACG,oBAAoB,EAACA,CAAA,KAAIA,oBAAoB;EAACC,sBAAsB,EAACA,CAAA,KAAIA,sBAAsB;EAACL,gBAAgB,EAACA,CAAA,KAAIA;AAAgB,CAAC,CAAC;AAArR,MAAMD,aAAa,GAAG;EAC3B4F,KAAKA,CAAC3G,IAAI,EAAE;IACV,OAAO,YAAW;MAChB,MAAM4G,GAAG,GAAG5G,IAAI,GAAG6G,GAAG,CAACC,YAAY,CAAC,cAAc,GAAG9G,IAAI,CAAC,GAAGuF,MAAM,CAACwB,QAAQ;MAC5E,OAAO,IAAIxF,KAAK,CAAC+E,QAAQ,CAACM,GAAG,CAACI,SAAS,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC;EACDC,MAAMA,CAACjH,IAAI,EAAE;IACX,OAAO,YAAW;MAChB,MAAM4G,GAAG,GAAG5G,IAAI,GAAG6G,GAAG,CAACC,YAAY,CAAC,cAAc,GAAG9G,IAAI,CAAC,GAAGuF,MAAM,CAACwB,QAAQ;MAC5E,OAAOH,GAAG,CAACpC,EAAE,CAAC,CAAC;IACjB,CAAC;EACH;AACF,CAAC;AAEM,SAAStD,eAAeA,CAAClB,IAAI,EAAEyB,OAAO,EAAE;EAC7C,IAAI,CAACzB,IAAI,IAAIyB,OAAO,CAACyF,UAAU,KAAK,IAAI,EAAE,OAAO,IAAI;EACrD,IAAIzF,OAAO,CAACyF,UAAU,EAAE,OAAOzF,OAAO,CAACyF,UAAU;EACjD,OAAOzB,MAAM,CAAC0B,QAAQ,GAAG1B,MAAM,CAACyB,UAAU,GAAGzB,MAAM,CAACC,MAAM;AAC5D;AAEO,SAASvE,WAAWA,CAACnB,IAAI,EAAEkH,UAAU,EAAEzF,OAAO,EAAE;EACrD,IAAIA,OAAO,CAACY,OAAO,EAAE,OAAOZ,OAAO,CAACY,OAAO;EAE3C,IAAIrC,IAAI,IACNkH,UAAU,KAAKzB,MAAM,CAACC,MAAM,IAC5B,OAAO0B,cAAc,KAAK,WAAW,IACrCA,cAAc,CAACC,6BAA6B,EAAE;IAC9C,OAAOD,cAAc,CAACC,6BAA6B,CAAC,CAAC;EACvD;EAEA,MAAM;IAAE3H;EAAsB,CAAC,GAAG4H,OAAO,CAAC,+BAA+B,CAAC;EAC1E,OAAO5H,qBAAqB;AAC9B;AAEO,SAASuB,gBAAgBA,CAACmD,UAAU,EAAEpE,IAAI,EAAEyB,OAAO,EAAE;EAC1D,IAAI8F,OAAO,CAACC,WAAW,IACrB,CAAC/F,OAAO,CAACgG,mBAAmB,IAC5BrD,UAAU,CAACjC,WAAW,IACtBiC,UAAU,CAACjC,WAAW,CAACuF,OAAO,EAAE;IAChCtD,UAAU,CAACjC,WAAW,CAACuF,OAAO,CAAC,IAAI,EAAE,MAAMtD,UAAU,CAACuD,IAAI,CAAC,CAAC,EAAE;MAC5DC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;AACF;AAEO,SAASxG,oBAAoBA,CAACgD,UAAU,EAAEpE,IAAI,EAAEyB,OAAO,EAAE;EAC9D,IAAIA,OAAO,CAACoG,qBAAqB,KAAK,KAAK,EAAE;EAE7C,IAAI;IACFzD,UAAU,CAAC0D,sBAAsB,CAAC;MAChCC,WAAW,EAAEtG,OAAO,CAACuG,sBAAsB,KAAK;IAClD,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACC,OAAO,yBAAAC,MAAA,CAAyBnI,IAAI,qCAAkC,EAAE;MAChF,MAAM,IAAIsF,KAAK,0CAAA6C,MAAA,CAAyCnI,IAAI,OAAG,CAAC;IAClE;IACA,MAAMiI,KAAK;EACb;AACF;AAEO,SAAS5G,sBAAsBA,CAACrB,IAAI,EAAE;EAC3C,IAAI,CAACA,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC1ByF,MAAM,CAAC2C,MAAM,CACX,yDAAyD,GACzD,yDAAyD,GACzD,gDACF,CAAC;IACDpI,IAAI,GAAG,IAAI;EACb;EAEA,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC7C,MAAM,IAAIsF,KAAK,CACb,iEACF,CAAC;EACH;EAEA,OAAOtF,IAAI;AACb;AAEO,SAASgB,gBAAgBA,CAACS,OAAO,EAAE;EACxC,IAAIA,OAAO,IAAIA,OAAO,CAAC4G,OAAO,EAAE;IAC9B;IACA5G,OAAO,GAAG;MAAEyF,UAAU,EAAEzF;IAAQ,CAAC;EACnC;EACA;EACA,IAAIA,OAAO,IAAIA,OAAO,CAAC6G,OAAO,IAAI,CAAC7G,OAAO,CAACyF,UAAU,EAAE;IACrDzF,OAAO,CAACyF,UAAU,GAAGzF,OAAO,CAAC6G,OAAO;EACtC;EAEA,OAAAhI,aAAA;IACE4G,UAAU,EAAExD,SAAS;IACrB7B,YAAY,EAAE,QAAQ;IACtBI,SAAS,EAAE,IAAI;IACfI,OAAO,EAAEqB,SAAS;IAClB+D,mBAAmB,EAAE;EAAK,GACvBhG,OAAO;AAEd,C;;;;;;;;;;;AClGA,IAAInB,aAAa;AAACd,MAAM,CAACgB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGlB,MAAM,CAACC,MAAM,CAAC;EAACmB,YAAY,EAACA,CAAA,KAAIA;AAAY,CAAC,CAAC;AAAvC,MAAMA,YAAY,GAAG;EAC1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE2H,YAAYA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAAtD,SAAA,CAAAlC,MAAA,EAAND,IAAI,OAAAa,KAAA,CAAA4E,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJ1F,IAAI,CAAA0F,IAAA,IAAAvD,SAAA,CAAAuD,IAAA;IAAA;IAClB,OAAO,IAAI,CAACnG,WAAW,CAACiG,YAAY,CAClC,IAAI,CAACzF,gBAAgB,CAACC,IAAI,CAAC,EAC3B,IAAI,CAACE,eAAe,CAACF,IAAI,CAC3B,CAAC;EACH,CAAC;EAED2F,YAAYA,CAACC,GAAG,EAAgB;IAAA,IAAdlH,OAAO,GAAAyD,SAAA,CAAAlC,MAAA,QAAAkC,SAAA,QAAAxB,SAAA,GAAAwB,SAAA,MAAG,CAAC,CAAC;IAC5B;IACA,IAAI,CAACyD,GAAG,EAAE;MACR,MAAM,IAAIrD,KAAK,CAAC,6BAA6B,CAAC;IAChD;;IAEA;IACAqD,GAAG,GAAG9I,MAAM,CAACC,MAAM,CACjBD,MAAM,CAAC+I,cAAc,CAACD,GAAG,CAAC,EAC1B9I,MAAM,CAACgJ,yBAAyB,CAACF,GAAG,CACtC,CAAC;IAED,IAAI,KAAK,IAAIA,GAAG,EAAE;MAChB,IACE,CAACA,GAAG,CAACvD,GAAG,IACR,EAAE,OAAOuD,GAAG,CAACvD,GAAG,KAAK,QAAQ,IAAIuD,GAAG,CAACvD,GAAG,YAAY7D,KAAK,CAAC+E,QAAQ,CAAC,EACnE;QACA,MAAM,IAAIhB,KAAK,CACb,0EACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,IAAIwD,UAAU,GAAG,IAAI;;MAErB;MACA;MACA;MACA,IAAI,IAAI,CAACtD,mBAAmB,CAAC,CAAC,EAAE;QAC9B,MAAMuD,SAAS,GAAGlC,GAAG,CAACmC,wBAAwB,CAAC5C,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC2C,SAAS,EAAE;UACdD,UAAU,GAAG,KAAK;QACpB;MACF;MAEA,IAAIA,UAAU,EAAE;QACdH,GAAG,CAACvD,GAAG,GAAG,IAAI,CAACxD,UAAU,CAAC,CAAC;MAC7B;IACF;;IAEA;IACA;IACA,IAAIqH,qCAAqC,GAAG,SAAAA,CAASC,MAAM,EAAE;MAC3D,IAAIzD,MAAM,CAAC0D,UAAU,CAACD,MAAM,CAAC,EAAE,OAAOA,MAAM;MAE5C,IAAIP,GAAG,CAACvD,GAAG,EAAE;QACX,OAAOuD,GAAG,CAACvD,GAAG;MAChB;;MAEA;MACA;MACA;MACAuD,GAAG,CAACvD,GAAG,GAAG8D,MAAM;MAEhB,OAAOA,MAAM;IACf,CAAC;IAED,IAAI,IAAI,CAAC1D,mBAAmB,CAAC,CAAC,EAAE;MAC9B,MAAM4D,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,aAAa,EAAE,CAACV,GAAG,CAAC,EAAElH,OAAO,CAAC;MAC3E2H,OAAO,CAACE,IAAI,CAACL,qCAAqC,CAAC;MACnDG,OAAO,CAACG,WAAW,GAAGH,OAAO,CAACG,WAAW,CAACD,IAAI,CAACL,qCAAqC,CAAC;MACrFG,OAAO,CAACI,aAAa,GAAGJ,OAAO,CAACI,aAAa,CAACF,IAAI,CAACL,qCAAqC,CAAC;MACzF,OAAOG,OAAO;IAChB;;IAEA;IACA;IACA,OAAO,IAAI,CAAC9G,WAAW,CAACmH,WAAW,CAACd,GAAG,CAAC,CACrCW,IAAI,CAACL,qCAAqC,CAAC;EAChD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEQ,WAAWA,CAACd,GAAG,EAAElH,OAAO,EAAE;IACxB,OAAO,IAAI,CAACiH,YAAY,CAACC,GAAG,EAAElH,OAAO,CAAC;EACxC,CAAC;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiI,WAAWA,CAAC1E,QAAQ,EAAE2E,QAAQ,EAAyB;IAErD;IACA;IACA,MAAMlI,OAAO,GAAAnB,aAAA,KAAS,CAAA4E,SAAA,CAAAlC,MAAA,QAAAU,SAAA,GAAAwB,SAAA,QAAyB,IAAI,CAAG;IACtD,IAAI0E,UAAU;IACd,IAAInI,OAAO,IAAIA,OAAO,CAACoI,MAAM,EAAE;MAC7B;MACA,IAAIpI,OAAO,CAACmI,UAAU,EAAE;QACtB,IACE,EACE,OAAOnI,OAAO,CAACmI,UAAU,KAAK,QAAQ,IACtCnI,OAAO,CAACmI,UAAU,YAAYrI,KAAK,CAAC+E,QAAQ,CAC7C,EAED,MAAM,IAAIhB,KAAK,CAAC,uCAAuC,CAAC;QAC1DsE,UAAU,GAAGnI,OAAO,CAACmI,UAAU;MACjC,CAAC,MAAM,IAAI,CAAC5E,QAAQ,IAAI,CAACA,QAAQ,CAACI,GAAG,EAAE;QACrCwE,UAAU,GAAG,IAAI,CAAChI,UAAU,CAAC,CAAC;QAC9BH,OAAO,CAACqI,WAAW,GAAG,IAAI;QAC1BrI,OAAO,CAACmI,UAAU,GAAGA,UAAU;MACjC;IACF;IAEA5E,QAAQ,GAAGzD,KAAK,CAACC,UAAU,CAACuD,gBAAgB,CAACC,QAAQ,EAAE;MACrDC,UAAU,EAAE2E;IACd,CAAC,CAAC;IAEF,IAAI,IAAI,CAACpE,mBAAmB,CAAC,CAAC,EAAE;MAC9B,MAAMzC,IAAI,GAAG,CAACiC,QAAQ,EAAE2E,QAAQ,EAAElI,OAAO,CAAC;MAE1C,OAAO,IAAI,CAAC4H,uBAAuB,CAAC,aAAa,EAAEtG,IAAI,EAAEtB,OAAO,CAAC;IACnE;;IAEA;IACA;IACA;IACA;IACA;;IAEA,OAAO,IAAI,CAACa,WAAW,CAACoH,WAAW,CACjC1E,QAAQ,EACR2E,QAAQ,EACRlI,OACF,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEsI,WAAWA,CAAC/E,QAAQ,EAAgB;IAAA,IAAdvD,OAAO,GAAAyD,SAAA,CAAAlC,MAAA,QAAAkC,SAAA,QAAAxB,SAAA,GAAAwB,SAAA,MAAG,CAAC,CAAC;IAChCF,QAAQ,GAAGzD,KAAK,CAACC,UAAU,CAACuD,gBAAgB,CAACC,QAAQ,CAAC;IAEtD,IAAI,IAAI,CAACQ,mBAAmB,CAAC,CAAC,EAAE;MAC9B,OAAO,IAAI,CAAC6D,uBAAuB,CAAC,aAAa,EAAE,CAACrE,QAAQ,CAAC,EAAEvD,OAAO,CAAC;IACzE;;IAEA;IACA;IACA,OAAO,IAAI,CAACa,WAAW,CAACyH,WAAW,CAAC/E,QAAQ,CAAC;EAC/C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMgF,WAAWA,CAAChF,QAAQ,EAAE2E,QAAQ,EAAElI,OAAO,EAAE;IAC7C,OAAO,IAAI,CAACiI,WAAW,CACrB1E,QAAQ,EACR2E,QAAQ,EAAArJ,aAAA,CAAAA,aAAA,KAEHmB,OAAO;MACVwI,aAAa,EAAE,IAAI;MACnBJ,MAAM,EAAE;IAAI,EACb,CAAC;EACN,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEK,cAAcA,CAAA,EAAU;IACtB,OAAO,IAAI,CAAC5H,WAAW,CAAC4H,cAAc,CAAC,GAAAhF,SAAO,CAAC;EACjD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiF,sBAAsBA,CAAA,EAAU;IAC9B,OAAO,IAAI,CAAC7H,WAAW,CAAC6H,sBAAsB,CAAC,GAAAjF,SAAO,CAAC;EACzD;AACF,CAAC,C;;;;;;;;;;;AC3OD1F,MAAM,CAACC,MAAM,CAAC;EAACqB,YAAY,EAACA,CAAA,KAAIA;AAAY,CAAC,CAAC;AAAvC,MAAMA,YAAY,GAAG;EAC1B;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMsJ,gBAAgBA,CAACC,KAAK,EAAE5I,OAAO,EAAE;IACrC,IAAI0B,IAAI,GAAG,IAAI;IACf,IAAI,CAACA,IAAI,CAACb,WAAW,CAAC8H,gBAAgB,IAAI,CAACjH,IAAI,CAACb,WAAW,CAACgI,gBAAgB,EAC1E,MAAM,IAAIhF,KAAK,CAAC,sDAAsD,CAAC;IACzE,IAAInC,IAAI,CAACb,WAAW,CAACgI,gBAAgB,EAAE;MACrC,MAAMnH,IAAI,CAACb,WAAW,CAACgI,gBAAgB,CAACD,KAAK,EAAE5I,OAAO,CAAC;IACzD,CAAC,MAAM;MAtBX,IAAI8I,GAAG;MAAC/K,MAAM,CAACgB,IAAI,CAAC,gBAAgB,EAAC;QAAC+J,GAAGA,CAAC7J,CAAC,EAAC;UAAC6J,GAAG,GAAC7J,CAAC;QAAA;MAAC,CAAC,EAAC,CAAC,CAAC;MAyBjD6J,GAAG,CAACC,KAAK,uFAAArC,MAAA,CAAwF1G,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEzB,IAAI,oBAAAmI,MAAA,CAAqB1G,OAAO,CAACzB,IAAI,gBAAAmI,MAAA,CAAmBsC,IAAI,CAACC,SAAS,CAACL,KAAK,CAAC,CAAG,CAAG,CAAC;MAC9L,MAAMlH,IAAI,CAACb,WAAW,CAAC8H,gBAAgB,CAACC,KAAK,EAAE5I,OAAO,CAAC;IACzD;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM6I,gBAAgBA,CAACD,KAAK,EAAE5I,OAAO,EAAE;IACrC,IAAI0B,IAAI,GAAG,IAAI;IACf,IAAI,CAACA,IAAI,CAACb,WAAW,CAACgI,gBAAgB,EACpC,MAAM,IAAIhF,KAAK,CAAC,sDAAsD,CAAC;IAEzE,IAAI;MACF,MAAMnC,IAAI,CAACb,WAAW,CAACgI,gBAAgB,CAACD,KAAK,EAAE5I,OAAO,CAAC;IACzD,CAAC,CAAC,OAAOkJ,CAAC,EAAE;MAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACV,IACEH,CAAC,CAACzC,OAAO,CAAC6C,QAAQ,CAChB,8EACF,CAAC,KAAAH,gBAAA,GACDnF,MAAM,CAACuF,QAAQ,cAAAJ,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBK,QAAQ,cAAAJ,qBAAA,gBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B5E,KAAK,cAAA6E,sBAAA,eAAhCA,sBAAA,CAAkCI,6BAA6B,EAC/D;QAvDR,IAAIX,GAAG;QAAC/K,MAAM,CAACgB,IAAI,CAAC,gBAAgB,EAAC;UAAC+J,GAAGA,CAAC7J,CAAC,EAAC;YAAC6J,GAAG,GAAC7J,CAAC;UAAA;QAAC,CAAC,EAAC,CAAC,CAAC;QA0D/C6J,GAAG,CAACY,IAAI,sBAAAhD,MAAA,CAAuBkC,KAAK,WAAAlC,MAAA,CAAUhF,IAAI,CAACZ,KAAK,8BAA4B,CAAC;QACrF,MAAMY,IAAI,CAACb,WAAW,CAAC8I,cAAc,CAACf,KAAK,CAAC;QAC5C,MAAMlH,IAAI,CAACb,WAAW,CAACgI,gBAAgB,CAACD,KAAK,EAAE5I,OAAO,CAAC;MACzD,CAAC,MAAM;QACL4J,OAAO,CAACpD,KAAK,CAAC0C,CAAC,CAAC;QAChB,MAAM,IAAIlF,MAAM,CAACH,KAAK,8DAAA6C,MAAA,CAA8DhF,IAAI,CAACZ,KAAK,QAAA4F,MAAA,CAAOwC,CAAC,CAACzC,OAAO,CAAG,CAAC;MACpH;IACF;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoD,WAAWA,CAACjB,KAAK,EAAE5I,OAAO,EAAC;IACzB,OAAO,IAAI,CAAC6I,gBAAgB,CAACD,KAAK,EAAE5I,OAAO,CAAC;EAC9C,CAAC;EAED,MAAM2J,cAAcA,CAACf,KAAK,EAAE;IAC1B,IAAIlH,IAAI,GAAG,IAAI;IACf,IAAI,CAACA,IAAI,CAACb,WAAW,CAAC8I,cAAc,EAClC,MAAM,IAAI9F,KAAK,CAAC,oDAAoD,CAAC;IACvE,MAAMnC,IAAI,CAACb,WAAW,CAAC8I,cAAc,CAACf,KAAK,CAAC;EAC9C;AACF,CAAC,C;;;;;;;;;;;AC1FD,IAAI/J,aAAa;AAACd,MAAM,CAACgB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGlB,MAAM,CAACC,MAAM,CAAC;EAAC6B,kBAAkB,EAACA,CAAA,KAAIA;AAAkB,CAAC,CAAC;AAAnD,MAAMA,kBAAkB,GAAG;EAChC,MAAMmB,sBAAsBA,CAACzC,IAAI,EAAE;IAAA,IAAAuL,oBAAA,EAAAC,qBAAA;IACjC,MAAMrI,IAAI,GAAG,IAAI;IACjB,IACE,EACEA,IAAI,CAAChB,WAAW,IAChBgB,IAAI,CAAChB,WAAW,CAACsJ,mBAAmB,IACpCtI,IAAI,CAAChB,WAAW,CAACuJ,mBAAmB,CACrC,EACD;MACA;IACF;IAGA,MAAMC,kBAAkB,GAAG;MACzB;MACA;MACAC,aAAaA,CAAA,EAAG;QACdzI,IAAI,CAACb,WAAW,CAACsJ,aAAa,CAAC,CAAC;MAClC,CAAC;MACDC,iBAAiBA,CAAA,EAAG;QAClB,OAAO1I,IAAI,CAACb,WAAW,CAACuJ,iBAAiB,CAAC,CAAC;MAC7C,CAAC;MACD;MACAC,cAAcA,CAAA,EAAG;QACf,OAAO3I,IAAI;MACb;IACF,CAAC;IACD,MAAM4I,kBAAkB,GAAAzL,aAAA;MACtB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM0L,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;QAClC;QACA;QACA;QACA;QACA;QACA,IAAID,SAAS,GAAG,CAAC,IAAIC,KAAK,EAAE/I,IAAI,CAACb,WAAW,CAAC6J,cAAc,CAAC,CAAC;QAE7D,IAAID,KAAK,EAAE,MAAM/I,IAAI,CAACb,WAAW,CAAC8J,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC;MAED;MACA;MACAC,MAAMA,CAACC,GAAG,EAAE;QACV,IAAIC,OAAO,GAAGhG,OAAO,CAACiG,OAAO,CAACF,GAAG,CAAC9H,EAAE,CAAC;QACrC,IAAImE,GAAG,GAAGxF,IAAI,CAACb,WAAW,CAACmK,KAAK,CAACrG,GAAG,CAACmG,OAAO,CAAC;;QAE7C;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA,IAAI9G,MAAM,CAAC0B,QAAQ,EAAE;UACnB,IAAImF,GAAG,CAACA,GAAG,KAAK,OAAO,IAAI3D,GAAG,EAAE;YAC9B2D,GAAG,CAACA,GAAG,GAAG,SAAS;UACrB,CAAC,MAAM,IAAIA,GAAG,CAACA,GAAG,KAAK,SAAS,IAAI,CAAC3D,GAAG,EAAE;YACxC;UACF,CAAC,MAAM,IAAI2D,GAAG,CAACA,GAAG,KAAK,SAAS,IAAI,CAAC3D,GAAG,EAAE;YACxC2D,GAAG,CAACA,GAAG,GAAG,OAAO;YACjB,MAAMI,IAAI,GAAGJ,GAAG,CAAC7H,MAAM;YACvB,KAAK,IAAIkI,KAAK,IAAID,IAAI,EAAE;cACtB,MAAME,KAAK,GAAGF,IAAI,CAACC,KAAK,CAAC;cACzB,IAAIC,KAAK,KAAK,KAAK,CAAC,EAAE;gBACpB,OAAON,GAAG,CAAC7H,MAAM,CAACkI,KAAK,CAAC;cAC1B;YACF;UACF;QACF;QACA;QACA;QACA;QACA,IAAIL,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;UACzB,IAAIO,OAAO,GAAGP,GAAG,CAACO,OAAO;UACzB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAIlE,GAAG,EAAExF,IAAI,CAACb,WAAW,CAAC8J,MAAM,CAACG,OAAO,CAAC;UAC3C,CAAC,MAAM,IAAI,CAAC5D,GAAG,EAAE;YACfxF,IAAI,CAACb,WAAW,CAACwK,MAAM,CAACD,OAAO,CAAC;UAClC,CAAC,MAAM;YACL;YACA1J,IAAI,CAACb,WAAW,CAAC+J,MAAM,CAACE,OAAO,EAAEM,OAAO,CAAC;UAC3C;UACA;QACF,CAAC,MAAM,IAAIP,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;UAC9B,IAAI3D,GAAG,EAAE;YACP,MAAM,IAAIrD,KAAK,CACb,4DACF,CAAC;UACH;UACAnC,IAAI,CAACb,WAAW,CAACwK,MAAM,CAAAxM,aAAA;YAAG8E,GAAG,EAAEmH;UAAO,GAAKD,GAAG,CAAC7H,MAAM,CAAE,CAAC;QAC1D,CAAC,MAAM,IAAI6H,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;UAChC,IAAI,CAAC3D,GAAG,EACN,MAAM,IAAIrD,KAAK,CACb,yDACF,CAAC;UACHnC,IAAI,CAACb,WAAW,CAAC8J,MAAM,CAACG,OAAO,CAAC;QAClC,CAAC,MAAM,IAAID,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;UAChC,IAAI,CAAC3D,GAAG,EAAE,MAAM,IAAIrD,KAAK,CAAC,uCAAuC,CAAC;UAClE,MAAMyH,IAAI,GAAGlN,MAAM,CAACkN,IAAI,CAACT,GAAG,CAAC7H,MAAM,CAAC;UACpC,IAAIsI,IAAI,CAAC/J,MAAM,GAAG,CAAC,EAAE;YACnB,IAAI2G,QAAQ,GAAG,CAAC,CAAC;YACjBoD,IAAI,CAACC,OAAO,CAACC,GAAG,IAAI;cAClB,MAAML,KAAK,GAAGN,GAAG,CAAC7H,MAAM,CAACwI,GAAG,CAAC;cAC7B,IAAIC,KAAK,CAACC,MAAM,CAACxE,GAAG,CAACsE,GAAG,CAAC,EAAEL,KAAK,CAAC,EAAE;gBACjC;cACF;cACA,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;gBAChC,IAAI,CAACjD,QAAQ,CAACyD,MAAM,EAAE;kBACpBzD,QAAQ,CAACyD,MAAM,GAAG,CAAC,CAAC;gBACtB;gBACAzD,QAAQ,CAACyD,MAAM,CAACH,GAAG,CAAC,GAAG,CAAC;cAC1B,CAAC,MAAM;gBACL,IAAI,CAACtD,QAAQ,CAAC0D,IAAI,EAAE;kBAClB1D,QAAQ,CAAC0D,IAAI,GAAG,CAAC,CAAC;gBACpB;gBACA1D,QAAQ,CAAC0D,IAAI,CAACJ,GAAG,CAAC,GAAGL,KAAK;cAC5B;YACF,CAAC,CAAC;YACF,IAAI/M,MAAM,CAACkN,IAAI,CAACpD,QAAQ,CAAC,CAAC3G,MAAM,GAAG,CAAC,EAAE;cACpCG,IAAI,CAACb,WAAW,CAAC+J,MAAM,CAACE,OAAO,EAAE5C,QAAQ,CAAC;YAC5C;UACF;QACF,CAAC,MAAM;UACL,MAAM,IAAIrE,KAAK,CAAC,4CAA4C,CAAC;QAC/D;MACF,CAAC;MAED;MACAgI,SAASA,CAAA,EAAG;QACVnK,IAAI,CAACb,WAAW,CAACiL,qBAAqB,CAAC,CAAC;MAC1C,CAAC;MAED;MACAC,MAAMA,CAAChJ,EAAE,EAAE;QACT,OAAOrB,IAAI,CAACsK,OAAO,CAACjJ,EAAE,CAAC;MACzB;IAAC,GAEEmH,kBAAkB,CACtB;IACD,MAAM+B,kBAAkB,GAAApN,aAAA;MACtB,MAAM0L,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;QAClC,IAAID,SAAS,GAAG,CAAC,IAAIC,KAAK,EAAE/I,IAAI,CAACb,WAAW,CAAC6J,cAAc,CAAC,CAAC;QAE7D,IAAID,KAAK,EAAE,MAAM/I,IAAI,CAACb,WAAW,CAACyH,WAAW,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC;MAED,MAAMsC,MAAMA,CAACC,GAAG,EAAE;QAChB,IAAIC,OAAO,GAAGhG,OAAO,CAACiG,OAAO,CAACF,GAAG,CAAC9H,EAAE,CAAC;QACrC,IAAImE,GAAG,GAAGxF,IAAI,CAACb,WAAW,CAACmK,KAAK,CAACrG,GAAG,CAACmG,OAAO,CAAC;;QAE7C;QACA;QACA;QACA,IAAID,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;UACzB,IAAIO,OAAO,GAAGP,GAAG,CAACO,OAAO;UACzB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAIlE,GAAG,EAAE,MAAMxF,IAAI,CAACb,WAAW,CAACyH,WAAW,CAACwC,OAAO,CAAC;UACtD,CAAC,MAAM,IAAI,CAAC5D,GAAG,EAAE;YACf,MAAMxF,IAAI,CAACb,WAAW,CAACmH,WAAW,CAACoD,OAAO,CAAC;UAC7C,CAAC,MAAM;YACL;YACA,MAAM1J,IAAI,CAACb,WAAW,CAACoH,WAAW,CAAC6C,OAAO,EAAEM,OAAO,CAAC;UACtD;UACA;QACF,CAAC,MAAM,IAAIP,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;UAC9B,IAAI3D,GAAG,EAAE;YACP,MAAM,IAAIrD,KAAK,CACb,4DACF,CAAC;UACH;UACA,MAAMnC,IAAI,CAACb,WAAW,CAACmH,WAAW,CAAAnJ,aAAA;YAAG8E,GAAG,EAAEmH;UAAO,GAAKD,GAAG,CAAC7H,MAAM,CAAE,CAAC;QACrE,CAAC,MAAM,IAAI6H,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;UAChC,IAAI,CAAC3D,GAAG,EACN,MAAM,IAAIrD,KAAK,CACb,yDACF,CAAC;UACH,MAAMnC,IAAI,CAACb,WAAW,CAACyH,WAAW,CAACwC,OAAO,CAAC;QAC7C,CAAC,MAAM,IAAID,GAAG,CAACA,GAAG,KAAK,SAAS,EAAE;UAChC,IAAI,CAAC3D,GAAG,EAAE,MAAM,IAAIrD,KAAK,CAAC,uCAAuC,CAAC;UAClE,MAAMyH,IAAI,GAAGlN,MAAM,CAACkN,IAAI,CAACT,GAAG,CAAC7H,MAAM,CAAC;UACpC,IAAIsI,IAAI,CAAC/J,MAAM,GAAG,CAAC,EAAE;YACnB,IAAI2G,QAAQ,GAAG,CAAC,CAAC;YACjBoD,IAAI,CAACC,OAAO,CAACC,GAAG,IAAI;cAClB,MAAML,KAAK,GAAGN,GAAG,CAAC7H,MAAM,CAACwI,GAAG,CAAC;cAC7B,IAAIC,KAAK,CAACC,MAAM,CAACxE,GAAG,CAACsE,GAAG,CAAC,EAAEL,KAAK,CAAC,EAAE;gBACjC;cACF;cACA,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;gBAChC,IAAI,CAACjD,QAAQ,CAACyD,MAAM,EAAE;kBACpBzD,QAAQ,CAACyD,MAAM,GAAG,CAAC,CAAC;gBACtB;gBACAzD,QAAQ,CAACyD,MAAM,CAACH,GAAG,CAAC,GAAG,CAAC;cAC1B,CAAC,MAAM;gBACL,IAAI,CAACtD,QAAQ,CAAC0D,IAAI,EAAE;kBAClB1D,QAAQ,CAAC0D,IAAI,GAAG,CAAC,CAAC;gBACpB;gBACA1D,QAAQ,CAAC0D,IAAI,CAACJ,GAAG,CAAC,GAAGL,KAAK;cAC5B;YACF,CAAC,CAAC;YACF,IAAI/M,MAAM,CAACkN,IAAI,CAACpD,QAAQ,CAAC,CAAC3G,MAAM,GAAG,CAAC,EAAE;cACpC,MAAMG,IAAI,CAACb,WAAW,CAACoH,WAAW,CAAC6C,OAAO,EAAE5C,QAAQ,CAAC;YACvD;UACF;QACF,CAAC,MAAM;UACL,MAAM,IAAIrE,KAAK,CAAC,4CAA4C,CAAC;QAC/D;MACF,CAAC;MAED;MACA,MAAMgI,SAASA,CAAA,EAAG;QAChB,MAAMnK,IAAI,CAACb,WAAW,CAACqL,qBAAqB,CAAC,CAAC;MAChD,CAAC;MAED;MACA,MAAMH,MAAMA,CAAChJ,EAAE,EAAE;QACf,OAAOrB,IAAI,CAACoF,YAAY,CAAC/D,EAAE,CAAC;MAC9B;IAAC,GACEmH,kBAAkB,CACtB;;IAGD;IACA;IACA;IACA,IAAIiC,mBAAmB;IACvB,IAAInI,MAAM,CAAC0B,QAAQ,EAAE;MACnByG,mBAAmB,GAAGzK,IAAI,CAAChB,WAAW,CAACsJ,mBAAmB,CACxDzL,IAAI,EACJ+L,kBACF,CAAC;IACH,CAAC,MAAM;MACL6B,mBAAmB,GAAGzK,IAAI,CAAChB,WAAW,CAACuJ,mBAAmB,CACxD1L,IAAI,EACJ0N,kBACF,CAAC;IACH;IAEA,MAAMxF,OAAO,4CAAAC,MAAA,CAA2CnI,IAAI,OAAG;IAC/D,MAAM6N,OAAO,GAAGA,CAAA,KAAM;MACpBxC,OAAO,CAACyC,IAAI,GAAGzC,OAAO,CAACyC,IAAI,CAAC5F,OAAO,CAAC,GAAGmD,OAAO,CAAC0C,GAAG,CAAC7F,OAAO,CAAC;IAC7D,CAAC;IAED,IAAI,CAAC0F,mBAAmB,EAAE;MACxB,OAAOC,OAAO,CAAC,CAAC;IAClB;IAEA,QAAAtC,oBAAA,GAAOqC,mBAAmB,cAAArC,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqBjC,IAAI,cAAAkC,qBAAA,uBAAzBA,qBAAA,CAAA1J,IAAA,CAAAyJ,oBAAA,EAA4ByC,EAAE,IAAI;MACvC,IAAI,CAACA,EAAE,EAAE;QACPH,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ;AACF,CAAC,C;;;;;;;;;;;ACzQD,IAAIvN,aAAa;AAACd,MAAM,CAACgB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGlB,MAAM,CAACC,MAAM,CAAC;EAACoB,WAAW,EAACA,CAAA,KAAIA;AAAW,CAAC,CAAC;AAArC,MAAMA,WAAW,GAAG;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE8G,IAAIA,CAAA,EAAU;IAAA,SAAAa,IAAA,GAAAtD,SAAA,CAAAlC,MAAA,EAAND,IAAI,OAAAa,KAAA,CAAA4E,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJ1F,IAAI,CAAA0F,IAAA,IAAAvD,SAAA,CAAAuD,IAAA;IAAA;IACV;IACA;IACA;IACA,OAAO,IAAI,CAACnG,WAAW,CAACqF,IAAI,CAC1B,IAAI,CAAC7E,gBAAgB,CAACC,IAAI,CAAC,EAC3B,IAAI,CAACE,eAAe,CAACF,IAAI,CAC3B,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0K,OAAOA,CAAA,EAAU;IAAA,SAAAQ,KAAA,GAAA/I,SAAA,CAAAlC,MAAA,EAAND,IAAI,OAAAa,KAAA,CAAAqK,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJnL,IAAI,CAAAmL,KAAA,IAAAhJ,SAAA,CAAAgJ,KAAA;IAAA;IACb,OAAO,IAAI,CAAC5L,WAAW,CAACmL,OAAO,CAC7B,IAAI,CAAC3K,gBAAgB,CAACC,IAAI,CAAC,EAC3B,IAAI,CAACE,eAAe,CAACF,IAAI,CAC3B,CAAC;EACH,CAAC;EAGD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAoL,OAAOA,CAACxF,GAAG,EAAEyF,QAAQ,EAAE;IACrB;IACA,IAAI,CAACzF,GAAG,EAAE;MACR,MAAM,IAAIrD,KAAK,CAAC,6BAA6B,CAAC;IAChD;;IAGA;IACAqD,GAAG,GAAG9I,MAAM,CAACC,MAAM,CACjBD,MAAM,CAAC+I,cAAc,CAACD,GAAG,CAAC,EAC1B9I,MAAM,CAACgJ,yBAAyB,CAACF,GAAG,CACtC,CAAC;IAED,IAAI,KAAK,IAAIA,GAAG,EAAE;MAChB,IACE,CAACA,GAAG,CAACvD,GAAG,IACR,EAAE,OAAOuD,GAAG,CAACvD,GAAG,KAAK,QAAQ,IAAIuD,GAAG,CAACvD,GAAG,YAAY7D,KAAK,CAAC+E,QAAQ,CAAC,EACnE;QACA,MAAM,IAAIhB,KAAK,CACb,0EACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,IAAIwD,UAAU,GAAG,IAAI;;MAErB;MACA;MACA;MACA,IAAI,IAAI,CAACtD,mBAAmB,CAAC,CAAC,EAAE;QAC9B,MAAMuD,SAAS,GAAGlC,GAAG,CAACmC,wBAAwB,CAAC5C,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC2C,SAAS,EAAE;UACdD,UAAU,GAAG,KAAK;QACpB;MACF;MAEA,IAAIA,UAAU,EAAE;QACdH,GAAG,CAACvD,GAAG,GAAG,IAAI,CAACxD,UAAU,CAAC,CAAC;MAC7B;IACF;;IAGA;IACA;IACA,IAAIqH,qCAAqC,GAAG,SAAAA,CAASC,MAAM,EAAE;MAC3D,IAAIzD,MAAM,CAAC0D,UAAU,CAACD,MAAM,CAAC,EAAE,OAAOA,MAAM;MAE5C,IAAIP,GAAG,CAACvD,GAAG,EAAE;QACX,OAAOuD,GAAG,CAACvD,GAAG;MAChB;;MAEA;MACA;MACA;MACAuD,GAAG,CAACvD,GAAG,GAAG8D,MAAM;MAEhB,OAAOA,MAAM;IACf,CAAC;IAED,MAAMmF,eAAe,GAAGC,YAAY,CAClCF,QAAQ,EACRnF,qCACF,CAAC;IAED,IAAI,IAAI,CAACzD,mBAAmB,CAAC,CAAC,EAAE;MAC9B,MAAM0D,MAAM,GAAG,IAAI,CAACqF,kBAAkB,CAAC,QAAQ,EAAE,CAAC5F,GAAG,CAAC,EAAE0F,eAAe,CAAC;MACxE,OAAOpF,qCAAqC,CAACC,MAAM,CAAC;IACtD;;IAEA;IACA;IACA,IAAI;MACF;MACA;MACA;MACA,IAAIA,MAAM;MACV,IAAI,CAAC,CAACmF,eAAe,EAAE;QACrB,IAAI,CAAC/L,WAAW,CAACwK,MAAM,CAACnE,GAAG,EAAE0F,eAAe,CAAC;MAC/C,CAAC,MAAM;QACL;QACA;QACAnF,MAAM,GAAG,IAAI,CAAC5G,WAAW,CAACwK,MAAM,CAACnE,GAAG,CAAC;MACvC;MAEA,OAAOM,qCAAqC,CAACC,MAAM,CAAC;IACtD,CAAC,CAAC,OAAOyB,CAAC,EAAE;MACV,IAAIyD,QAAQ,EAAE;QACZA,QAAQ,CAACzD,CAAC,CAAC;QACX,OAAO,IAAI;MACb;MACA,MAAMA,CAAC;IACT;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmC,MAAMA,CAACnE,GAAG,EAAEyF,QAAQ,EAAE;IACpB,OAAO,IAAI,CAACD,OAAO,CAACxF,GAAG,EAAEyF,QAAQ,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/B,MAAMA,CAACrH,QAAQ,EAAE2E,QAAQ,EAAyB;IAAA,SAAA6E,KAAA,GAAAtJ,SAAA,CAAAlC,MAAA,EAApByL,kBAAkB,OAAA7K,KAAA,CAAA4K,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAlBD,kBAAkB,CAAAC,KAAA,QAAAxJ,SAAA,CAAAwJ,KAAA;IAAA;IAC9C,MAAMN,QAAQ,GAAGO,mBAAmB,CAACF,kBAAkB,CAAC;;IAExD;IACA;IACA,MAAMhN,OAAO,GAAAnB,aAAA,KAASmO,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAG;IACtD,IAAI7E,UAAU;IACd,IAAInI,OAAO,IAAIA,OAAO,CAACoI,MAAM,EAAE;MAC7B;MACA,IAAIpI,OAAO,CAACmI,UAAU,EAAE;QACtB,IACE,EACE,OAAOnI,OAAO,CAACmI,UAAU,KAAK,QAAQ,IACtCnI,OAAO,CAACmI,UAAU,YAAYrI,KAAK,CAAC+E,QAAQ,CAC7C,EAED,MAAM,IAAIhB,KAAK,CAAC,uCAAuC,CAAC;QAC1DsE,UAAU,GAAGnI,OAAO,CAACmI,UAAU;MACjC,CAAC,MAAM,IAAI,CAAC5E,QAAQ,IAAI,CAACA,QAAQ,CAACI,GAAG,EAAE;QACrCwE,UAAU,GAAG,IAAI,CAAChI,UAAU,CAAC,CAAC;QAC9BH,OAAO,CAACqI,WAAW,GAAG,IAAI;QAC1BrI,OAAO,CAACmI,UAAU,GAAGA,UAAU;MACjC;IACF;IAEA5E,QAAQ,GAAGzD,KAAK,CAACC,UAAU,CAACuD,gBAAgB,CAACC,QAAQ,EAAE;MACrDC,UAAU,EAAE2E;IACd,CAAC,CAAC;IAEF,MAAMyE,eAAe,GAAGC,YAAY,CAACF,QAAQ,CAAC;IAE9C,IAAI,IAAI,CAAC5I,mBAAmB,CAAC,CAAC,EAAE;MAC9B,MAAMzC,IAAI,GAAG,CAACiC,QAAQ,EAAE2E,QAAQ,EAAElI,OAAO,CAAC;MAC1C,OAAO,IAAI,CAAC8M,kBAAkB,CAAC,QAAQ,EAAExL,IAAI,EAAEqL,QAAQ,CAAC;IAC1D;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI;MACF;MACA;MACA;MACA,OAAO,IAAI,CAAC9L,WAAW,CAAC+J,MAAM,CAC5BrH,QAAQ,EACR2E,QAAQ,EACRlI,OAAO,EACP4M,eACF,CAAC;IACH,CAAC,CAAC,OAAO1D,CAAC,EAAE;MACV,IAAIyD,QAAQ,EAAE;QACZA,QAAQ,CAACzD,CAAC,CAAC;QACX,OAAO,IAAI;MACb;MACA,MAAMA,CAAC;IACT;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEyB,MAAMA,CAACpH,QAAQ,EAAEoJ,QAAQ,EAAE;IACzBpJ,QAAQ,GAAGzD,KAAK,CAACC,UAAU,CAACuD,gBAAgB,CAACC,QAAQ,CAAC;IAEtD,IAAI,IAAI,CAACQ,mBAAmB,CAAC,CAAC,EAAE;MAC9B,OAAO,IAAI,CAAC+I,kBAAkB,CAAC,QAAQ,EAAE,CAACvJ,QAAQ,CAAC,EAAEoJ,QAAQ,CAAC;IAChE;;IAGA;IACA;IACA,OAAO,IAAI,CAAC9L,WAAW,CAAC8J,MAAM,CAACpH,QAAQ,CAAC;EAC1C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE6E,MAAMA,CAAC7E,QAAQ,EAAE2E,QAAQ,EAAElI,OAAO,EAAE2M,QAAQ,EAAE;IAC5C,IAAI,CAACA,QAAQ,IAAI,OAAO3M,OAAO,KAAK,UAAU,EAAE;MAC9C2M,QAAQ,GAAG3M,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,OAAO,IAAI,CAAC4K,MAAM,CAChBrH,QAAQ,EACR2E,QAAQ,EAAArJ,aAAA,CAAAA,aAAA,KAEHmB,OAAO;MACVwI,aAAa,EAAE,IAAI;MACnBJ,MAAM,EAAE;IAAI,EACb,CAAC;EACN;AACF,CAAC;AAED;AACA,SAASyE,YAAYA,CAACF,QAAQ,EAAEQ,aAAa,EAAE;EAC7C,OACER,QAAQ,IACR,UAASnG,KAAK,EAAEiB,MAAM,EAAE;IACtB,IAAIjB,KAAK,EAAE;MACTmG,QAAQ,CAACnG,KAAK,CAAC;IACjB,CAAC,MAAM,IAAI,OAAO2G,aAAa,KAAK,UAAU,EAAE;MAC9CR,QAAQ,CAACnG,KAAK,EAAE2G,aAAa,CAAC1F,MAAM,CAAC,CAAC;IACxC,CAAC,MAAM;MACLkF,QAAQ,CAACnG,KAAK,EAAEiB,MAAM,CAAC;IACzB;EACF,CAAC;AAEL;AAEA,SAASyF,mBAAmBA,CAAC5L,IAAI,EAAE;EACjC;EACA;EACA,IACEA,IAAI,CAACC,MAAM,KACVD,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKU,SAAS,IAClCX,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,YAAYa,QAAQ,CAAC,EAC5C;IACA,OAAOd,IAAI,CAAC8L,GAAG,CAAC,CAAC;EACnB;AACF,C;;;;;;;;;;;;ACzVA,IAAIvO,aAAa;AAACd,MAAM,CAACgB,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACJ,aAAa,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIoO,wBAAwB;AAACtP,MAAM,CAACgB,IAAI,CAAC,gDAAgD,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACoO,wBAAwB,GAACpO,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA3OlB,MAAM,CAACC,MAAM,CAAC;EAACkB,mBAAmB,EAACA,CAAA,KAAIA;AAAmB,CAAC,CAAC;AAArD,MAAMA,mBAAmB,GAAGc,OAAO,IAAI;EAC5C;EACA,MAAAiL,IAAA,GAAgDjL,OAAO,IAAI,CAAC,CAAC;IAAvD;MAAEgD,MAAM;MAAEjB;IAA4B,CAAC,GAAAkJ,IAAA;IAAdqC,YAAY,GAAAD,wBAAA,CAAApC,IAAA,EAAAsC,SAAA;EAC3C;EACA;;EAEA,OAAA1O,aAAA,CAAAA,aAAA,KACKyO,YAAY,GACXvL,UAAU,IAAIiB,MAAM,GAAG;IAAEjB,UAAU,EAAEiB,MAAM,IAAIjB;EAAW,CAAC,GAAG,CAAC,CAAC;AAExE,CAAC,C", "file": "/packages/mongo.js", "sourcesContent": ["// singleton\nexport const LocalCollectionDriver = new (class LocalCollectionDriver {\n  constructor() {\n    this.noConnCollections = Object.create(null);\n  }\n\n  open(name, conn) {\n    if (! name) {\n      return new LocalCollection;\n    }\n\n    if (! conn) {\n      return ensureCollection(name, this.noConnCollections);\n    }\n\n    if (! conn._mongo_livedata_collections) {\n      conn._mongo_livedata_collections = Object.create(null);\n    }\n\n    // XXX is there a way to keep track of a connection's collections without\n    // dangling it off the connection object?\n    return ensureCollection(name, conn._mongo_livedata_collections);\n  }\n});\n\nfunction ensureCollection(name, collections) {\n  return (name in collections)\n    ? collections[name]\n    : collections[name] = new LocalCollection(name);\n}\n", "import { normalizeProjection } from \"../mongo_utils\";\nimport { AsyncMethods } from './methods_async';\nimport { SyncMethods } from './methods_sync';\nimport { IndexMethods } from './methods_index';\nimport {\n  ID_GENERATORS, normalizeOptions,\n  setupAutopublish,\n  setupConnection,\n  setupDriver,\n  setupMutationMethods,\n  validateCollectionName\n} from './collection_utils';\nimport { ReplicationMethods } from './methods_replication';\n\n/**\n * @summary Namespace for MongoDB-related items\n * @namespace\n */\nMongo = {};\n\n/**\n * @summary Constructor for a Collection\n * @locus Anywhere\n * @instancename collection\n * @class\n * @param {String} name The name of the collection.  If null, creates an unmanaged (unsynchronized) local collection.\n * @param {Object} [options]\n * @param {Object} options.connection The server connection that will manage this collection. Uses the default connection if not specified.  Pass the return value of calling [`DDP.connect`](#DDP-connect) to specify a different server. Pass `null` to specify no connection. Unmanaged (`name` is null) collections cannot specify a connection.\n * @param {String} options.idGeneration The method of generating the `_id` fields of new documents in this collection.  Possible values:\n\n - **`'STRING'`**: random strings\n - **`'MONGO'`**:  random [`Mongo.ObjectID`](#mongo_object_id) values\n\nThe default id generation technique is `'STRING'`.\n * @param {Function} options.transform An optional transformation function. Documents will be passed through this function before being returned from `fetch` or `findOneAsync`, and before being passed to callbacks of `observe`, `map`, `forEach`, `allow`, and `deny`. Transforms are *not* applied for the callbacks of `observeChanges` or to cursors returned from publish functions.\n * @param {Boolean} options.defineMutationMethods Set to `false` to skip setting up the mutation methods that enable insert/update/remove from client code. Default `true`.\n */\n// Main Collection constructor\nMongo.Collection = function Collection(name, options) {\n  name = validateCollectionName(name);\n\n  options = normalizeOptions(options);\n\n  this._makeNewID = ID_GENERATORS[options.idGeneration]?.(name);\n\n  this._transform = LocalCollection.wrapTransform(options.transform);\n  this.resolverType = options.resolverType;\n\n  this._connection = setupConnection(name, options);\n\n  const driver = setupDriver(name, this._connection, options);\n  this._driver = driver;\n\n  this._collection = driver.open(name, this._connection);\n  this._name = name;\n\n  this._settingUpReplicationPromise = this._maybeSetUpReplication(name, options);\n\n  setupMutationMethods(this, name, options);\n\n  setupAutopublish(this, name, options);\n\n  Mongo._collections.set(name, this);\n};\n\nObject.assign(Mongo.Collection.prototype, {\n  _getFindSelector(args) {\n    if (args.length == 0) return {};\n    else return args[0];\n  },\n\n  _getFindOptions(args) {\n    const [, options] = args || [];\n    const newOptions = normalizeProjection(options);\n\n    var self = this;\n    if (args.length < 2) {\n      return { transform: self._transform };\n    } else {\n      check(\n        newOptions,\n        Match.Optional(\n          Match.ObjectIncluding({\n            projection: Match.Optional(Match.OneOf(Object, undefined)),\n            sort: Match.Optional(\n              Match.OneOf(Object, Array, Function, undefined)\n            ),\n            limit: Match.Optional(Match.OneOf(Number, undefined)),\n            skip: Match.Optional(Match.OneOf(Number, undefined)),\n          })\n        )\n      );\n\n      return {\n        transform: self._transform,\n        ...newOptions,\n      };\n    }\n  },\n});\n\nObject.assign(Mongo.Collection, {\n  async _publishCursor(cursor, sub, collection) {\n    var observeHandle = await cursor.observeChanges(\n        {\n          added: function(id, fields) {\n            sub.added(collection, id, fields);\n          },\n          changed: function(id, fields) {\n            sub.changed(collection, id, fields);\n          },\n          removed: function(id) {\n            sub.removed(collection, id);\n          },\n        },\n        // Publications don't mutate the documents\n        // This is tested by the `livedata - publish callbacks clone` test\n        { nonMutatingCallbacks: true }\n    );\n\n    // We don't call sub.ready() here: it gets called in livedata_server, after\n    // possibly calling _publishCursor on multiple returned cursors.\n\n    // register stop callback (expects lambda w/ no args).\n    sub.onStop(async function() {\n      return await observeHandle.stop();\n    });\n\n    // return the observeHandle in case it needs to be stopped early\n    return observeHandle;\n  },\n\n  // protect against dangerous selectors.  falsey and {_id: falsey} are both\n  // likely programmer error, and not what you want, particularly for destructive\n  // operations. If a falsey _id is sent in, a new string _id will be\n  // generated and returned; if a fallbackId is provided, it will be returned\n  // instead.\n  _rewriteSelector(selector, { fallbackId } = {}) {\n    // shorthand -- scalars match _id\n    if (LocalCollection._selectorIsId(selector)) selector = { _id: selector };\n\n    if (Array.isArray(selector)) {\n      // This is consistent with the Mongo console itself; if we don't do this\n      // check passing an empty array ends up selecting all items\n      throw new Error(\"Mongo selector can't be an array.\");\n    }\n\n    if (!selector || ('_id' in selector && !selector._id)) {\n      // can't match anything\n      return { _id: fallbackId || Random.id() };\n    }\n\n    return selector;\n  },\n});\n\nObject.assign(Mongo.Collection.prototype, ReplicationMethods, SyncMethods, AsyncMethods, IndexMethods);\n\nObject.assign(Mongo.Collection.prototype, {\n  // Determine if this collection is simply a minimongo representation of a real\n  // database on another server\n  _isRemoteCollection() {\n    // XXX see #MeteorServerNull\n    return this._connection && this._connection !== Meteor.server;\n  },\n\n  async dropCollectionAsync() {\n    var self = this;\n    if (!self._collection.dropCollectionAsync)\n      throw new Error('Can only call dropCollectionAsync on server collections');\n   await self._collection.dropCollectionAsync();\n  },\n\n  async createCappedCollectionAsync(byteSize, maxDocuments) {\n    var self = this;\n    if (! await self._collection.createCappedCollectionAsync)\n      throw new Error(\n        'Can only call createCappedCollectionAsync on server collections'\n      );\n    await self._collection.createCappedCollectionAsync(byteSize, maxDocuments);\n  },\n\n  /**\n   * @summary Returns the [`Collection`](http://mongodb.github.io/node-mongodb-native/3.0/api/Collection.html) object corresponding to this collection from the [npm `mongodb` driver module](https://www.npmjs.com/package/mongodb) which is wrapped by `Mongo.Collection`.\n   * @locus Server\n   * @memberof Mongo.Collection\n   * @instance\n   */\n  rawCollection() {\n    var self = this;\n    if (!self._collection.rawCollection) {\n      throw new Error('Can only call rawCollection on server collections');\n    }\n    return self._collection.rawCollection();\n  },\n\n  /**\n   * @summary Returns the [`Db`](http://mongodb.github.io/node-mongodb-native/3.0/api/Db.html) object corresponding to this collection's database connection from the [npm `mongodb` driver module](https://www.npmjs.com/package/mongodb) which is wrapped by `Mongo.Collection`.\n   * @locus Server\n   * @memberof Mongo.Collection\n   * @instance\n   */\n  rawDatabase() {\n    var self = this;\n    if (!(self._driver.mongo && self._driver.mongo.db)) {\n      throw new Error('Can only call rawDatabase on server collections');\n    }\n    return self._driver.mongo.db;\n  },\n});\n\nObject.assign(Mongo, {\n  /**\n   * @summary Retrieve a Meteor collection instance by name. Only collections defined with [`new Mongo.Collection(...)`](#collections) are available with this method. For plain MongoDB collections, you'll want to look at [`rawDatabase()`](#Mongo-Collection-rawDatabase).\n   * @locus Anywhere\n   * @memberof Mongo\n   * @static\n   * @param {string} name Name of your collection as it was defined with `new Mongo.Collection()`.\n   * @returns {Mongo.Collection | undefined}\n   */\n  getCollection(name) {\n    return this._collections.get(name);\n  },\n\n  /**\n   * @summary A record of all defined Mongo.Collection instances, indexed by collection name.\n   * @type {Map<string, Mongo.Collection>}\n   * @memberof Mongo\n   * @protected\n   */\n  _collections: new Map(),\n})\n\n\n\n/**\n * @summary Create a Mongo-style `ObjectID`.  If you don't specify a `hexString`, the `ObjectID` will be generated randomly (not using MongoDB's ID construction rules).\n * @locus Anywhere\n * @class\n * @param {String} [hexString] Optional.  The 24-character hexadecimal contents of the ObjectID to create\n */\nMongo.ObjectID = MongoID.ObjectID;\n\n/**\n * @summary To create a cursor, use find. To access the documents in a cursor, use forEach, map, or fetch.\n * @class\n * @instanceName cursor\n */\nMongo.Cursor = LocalCollection.Cursor;\n\n/**\n * @deprecated in 0.9.1\n */\nMongo.Collection.Cursor = Mongo.Cursor;\n\n/**\n * @deprecated in 0.9.1\n */\nMongo.Collection.ObjectID = Mongo.ObjectID;\n\n/**\n * @deprecated in 0.9.1\n */\nMeteor.Collection = Mongo.Collection;\n\n// Allow deny stuff is now in the allow-deny package\nObject.assign(Mongo.Collection.prototype, AllowDeny.CollectionPrototype);\n\n", "export const ID_GENERATORS = {\n  MONGO(name) {\n    return function() {\n      const src = name ? DDP.randomStream('/collection/' + name) : Random.insecure;\n      return new Mongo.ObjectID(src.hexString(24));\n    }\n  },\n  STRING(name) {\n    return function() {\n      const src = name ? DDP.randomStream('/collection/' + name) : Random.insecure;\n      return src.id();\n    }\n  }\n};\n\nexport function setupConnection(name, options) {\n  if (!name || options.connection === null) return null;\n  if (options.connection) return options.connection;\n  return Meteor.isClient ? Meteor.connection : Meteor.server;\n}\n\nexport function setupDriver(name, connection, options) {\n  if (options._driver) return options._driver;\n\n  if (name &&\n    connection === Meteor.server &&\n    typeof MongoInternals !== 'undefined' &&\n    MongoInternals.defaultRemoteCollectionDriver) {\n    return MongoInternals.defaultRemoteCollectionDriver();\n  }\n\n  const { LocalCollectionDriver } = require('../local_collection_driver.js');\n  return LocalCollectionDriver;\n}\n\nexport function setupAutopublish(collection, name, options) {\n  if (Package.autopublish &&\n    !options._preventAutopublish &&\n    collection._connection &&\n    collection._connection.publish) {\n    collection._connection.publish(null, () => collection.find(), {\n      is_auto: true\n    });\n  }\n}\n\nexport function setupMutationMethods(collection, name, options) {\n  if (options.defineMutationMethods === false) return;\n\n  try {\n    collection._defineMutationMethods({\n      useExisting: options._suppressSameNameError === true\n    });\n  } catch (error) {\n    if (error.message === `A method named '/${name}/insertAsync' is already defined`) {\n      throw new Error(`There is already a collection named \"${name}\"`);\n    }\n    throw error;\n  }\n}\n\nexport function validateCollectionName(name) {\n  if (!name && name !== null) {\n    Meteor._debug(\n      'Warning: creating anonymous collection. It will not be ' +\n      'saved or synchronized over the network. (Pass null for ' +\n      'the collection name to turn off this warning.)'\n    );\n    name = null;\n  }\n\n  if (name !== null && typeof name !== 'string') {\n    throw new Error(\n      'First argument to new Mongo.Collection must be a string or null'\n    );\n  }\n\n  return name;\n}\n\nexport function normalizeOptions(options) {\n  if (options && options.methods) {\n    // Backwards compatibility hack with original signature\n    options = { connection: options };\n  }\n  // Backwards compatibility: \"connection\" used to be called \"manager\".\n  if (options && options.manager && !options.connection) {\n    options.connection = options.manager;\n  }\n\n  return {\n    connection: undefined,\n    idGeneration: 'STRING',\n    transform: null,\n    _driver: undefined,\n    _preventAutopublish: false,\n    ...options,\n  };\n}\n", "export const AsyncMethods = {\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOneAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Object}\n   */\n  findOneAsync(...args) {\n    return this._collection.findOneAsync(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n  _insertAsync(doc, options = {}) {\n    // Make sure we were passed a document to insert\n    if (!doc) {\n      throw new Error('insert requires an argument');\n    }\n\n    // Make a shallow clone of the document, preserving its prototype.\n    doc = Object.create(\n      Object.getPrototypeOf(doc),\n      Object.getOwnPropertyDescriptors(doc)\n    );\n\n    if ('_id' in doc) {\n      if (\n        !doc._id ||\n        !(typeof doc._id === 'string' || doc._id instanceof Mongo.ObjectID)\n      ) {\n        throw new Error(\n          'Meteor requires document _id fields to be non-empty strings or ObjectIDs'\n        );\n      }\n    } else {\n      let generateId = true;\n\n      // Don't generate the id if we're the client and the 'outermost' call\n      // This optimization saves us passing both the randomSeed and the id\n      // Passing both is redundant.\n      if (this._isRemoteCollection()) {\n        const enclosing = DDP._CurrentMethodInvocation.get();\n        if (!enclosing) {\n          generateId = false;\n        }\n      }\n\n      if (generateId) {\n        doc._id = this._makeNewID();\n      }\n    }\n\n    // On inserts, always return the id that we generated; on all other\n    // operations, just return the result from the collection.\n    var chooseReturnValueFromCollectionResult = function(result) {\n      if (Meteor._isPromise(result)) return result;\n\n      if (doc._id) {\n        return doc._id;\n      }\n\n      // XXX what is this for??\n      // It's some iteraction between the callback to _callMutatorMethod and\n      // the return value conversion\n      doc._id = result;\n\n      return result;\n    };\n\n    if (this._isRemoteCollection()) {\n      const promise = this._callMutatorMethodAsync('insertAsync', [doc], options);\n      promise.then(chooseReturnValueFromCollectionResult);\n      promise.stubPromise = promise.stubPromise.then(chooseReturnValueFromCollectionResult);\n      promise.serverPromise = promise.serverPromise.then(chooseReturnValueFromCollectionResult);\n      return promise;\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    return this._collection.insertAsync(doc)\n      .then(chooseReturnValueFromCollectionResult);\n  },\n\n  /**\n   * @summary Insert a document in the collection.  Returns a promise that will return the document's unique _id when solved.\n   * @locus Anywhere\n   * @method  insert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   */\n  insertAsync(doc, options) {\n    return this._insertAsync(doc, options);\n  },\n\n\n  /**\n   * @summary Modify one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method update\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   */\n  updateAsync(selector, modifier, ...optionsAndCallback) {\n\n    // We've already popped off the callback, so we are left with an array\n    // of one or zero items\n    const options = { ...(optionsAndCallback[0] || null) };\n    let insertedId;\n    if (options && options.upsert) {\n      // set `insertedId` if absent.  `insertedId` is a Meteor extension.\n      if (options.insertedId) {\n        if (\n          !(\n            typeof options.insertedId === 'string' ||\n            options.insertedId instanceof Mongo.ObjectID\n          )\n        )\n          throw new Error('insertedId must be string or ObjectID');\n        insertedId = options.insertedId;\n      } else if (!selector || !selector._id) {\n        insertedId = this._makeNewID();\n        options.generatedId = true;\n        options.insertedId = insertedId;\n      }\n    }\n\n    selector = Mongo.Collection._rewriteSelector(selector, {\n      fallbackId: insertedId,\n    });\n\n    if (this._isRemoteCollection()) {\n      const args = [selector, modifier, options];\n\n      return this._callMutatorMethodAsync('updateAsync', args, options);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    // If the user provided a callback and the collection implements this\n    // operation asynchronously, then queryRet will be undefined, and the\n    // result will be returned through the callback instead.\n\n    return this._collection.updateAsync(\n      selector,\n      modifier,\n      options\n    );\n  },\n\n  /**\n   * @summary Asynchronously removes documents from the collection.\n   * @locus Anywhere\n   * @method remove\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   */\n  removeAsync(selector, options = {}) {\n    selector = Mongo.Collection._rewriteSelector(selector);\n\n    if (this._isRemoteCollection()) {\n      return this._callMutatorMethodAsync('removeAsync', [selector], options);\n    }\n\n    // it's my collection.  descend into the collection1 object\n    // and propagate any exception.\n    return this._collection.removeAsync(selector);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   */\n  async upsertAsync(selector, modifier, options) {\n    return this.updateAsync(\n      selector,\n      modifier,\n      {\n        ...options,\n        _returnObject: true,\n        upsert: true,\n      });\n  },\n\n  /**\n   * @summary Gets the number of documents matching the filter. For a fast count of the total documents in a collection see `estimatedDocumentCount`.\n   * @locus Anywhere\n   * @method countDocuments\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to count\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://mongodb.github.io/node-mongodb-native/4.11/interfaces/CountDocumentsOptions.html). Please note that not all of them are available on the client.\n   * @returns {Promise<number>}\n   */\n  countDocuments(...args) {\n    return this._collection.countDocuments(...args);\n  },\n\n  /**\n   * @summary Gets an estimate of the count of documents in a collection using collection metadata. For an exact count of the documents in a collection see `countDocuments`.\n   * @locus Anywhere\n   * @method estimatedDocumentCount\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://mongodb.github.io/node-mongodb-native/4.11/interfaces/EstimatedDocumentCountOptions.html). Please note that not all of them are available on the client.\n   * @returns {Promise<number>}\n   */\n  estimatedDocumentCount(...args) {\n    return this._collection.estimatedDocumentCount(...args);\n  },\n}", "export const IndexMethods = {\n  // We'll actually design an index API later. For now, we just pass through to\n  // Mongo's, but make it synchronous.\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method ensureIndexAsync\n   * @deprecated in 3.0\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  async ensureIndexAsync(index, options) {\n    var self = this;\n    if (!self._collection.ensureIndexAsync || !self._collection.createIndexAsync)\n      throw new Error('Can only call createIndexAsync on server collections');\n    if (self._collection.createIndexAsync) {\n      await self._collection.createIndexAsync(index, options);\n    } else {\n      import { Log } from 'meteor/logging';\n\n      Log.debug(`ensureIndexAsync has been deprecated, please use the new 'createIndexAsync' instead${ options?.name ? `, index name: ${ options.name }` : `, index: ${ JSON.stringify(index) }` }`)\n      await self._collection.ensureIndexAsync(index, options);\n    }\n  },\n\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method createIndexAsync\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  async createIndexAsync(index, options) {\n    var self = this;\n    if (!self._collection.createIndexAsync)\n      throw new Error('Can only call createIndexAsync on server collections');\n\n    try {\n      await self._collection.createIndexAsync(index, options);\n    } catch (e) {\n      if (\n        e.message.includes(\n          'An equivalent index already exists with the same name but different options.'\n        ) &&\n        Meteor.settings?.packages?.mongo?.reCreateIndexOnOptionMismatch\n      ) {\n        import { Log } from 'meteor/logging';\n\n        Log.info(`Re-creating index ${ index } for ${ self._name } due to options mismatch.`);\n        await self._collection.dropIndexAsync(index);\n        await self._collection.createIndexAsync(index, options);\n      } else {\n        console.error(e);\n        throw new Meteor.Error(`An error occurred when creating an index for collection \"${ self._name }: ${ e.message }`);\n      }\n    }\n  },\n\n  /**\n   * @summary Asynchronously creates the specified index on the collection.\n   * @locus server\n   * @method createIndex\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} index A document that contains the field and value pairs where the field is the index key and the value describes the type of index for that field. For an ascending index on a field, specify a value of `1`; for descending index, specify a value of `-1`. Use `text` for text indexes.\n   * @param {Object} [options] All options are listed in [MongoDB documentation](https://docs.mongodb.com/manual/reference/method/db.collection.createIndex/#options)\n   * @param {String} options.name Name of the index\n   * @param {Boolean} options.unique Define that the index values must be unique, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-unique/)\n   * @param {Boolean} options.sparse Define that the index is sparse, more at [MongoDB documentation](https://docs.mongodb.com/manual/core/index-sparse/)\n   */\n  createIndex(index, options){\n    return this.createIndexAsync(index, options);\n  },\n\n  async dropIndexAsync(index) {\n    var self = this;\n    if (!self._collection.dropIndexAsync)\n      throw new Error('Can only call dropIndexAsync on server collections');\n    await self._collection.dropIndexAsync(index);\n  },\n}", "export const ReplicationMethods = {\n  async _maybeSetUpReplication(name) {\n    const self = this;\n    if (\n      !(\n        self._connection &&\n        self._connection.registerStoreClient &&\n        self._connection.registerStoreServer\n      )\n    ) {\n      return;\n    }\n\n\n    const wrappedStoreCommon = {\n      // Called around method stub invocations to capture the original versions\n      // of modified documents.\n      saveOriginals() {\n        self._collection.saveOriginals();\n      },\n      retrieveOriginals() {\n        return self._collection.retrieveOriginals();\n      },\n      // To be able to get back to the collection from the store.\n      _getCollection() {\n        return self;\n      },\n    };\n    const wrappedStoreClient = {\n      // Called at the beginning of a batch of updates. batchSize is the number\n      // of update calls to expect.\n      //\n      // XXX This interface is pretty janky. reset probably ought to go back to\n      // being its own function, and callers shouldn't have to calculate\n      // batchSize. The optimization of not calling pause/remove should be\n      // delayed until later: the first call to update() should buffer its\n      // message, and then we can either directly apply it at endUpdate time if\n      // it was the only update, or do pauseObservers/apply/apply at the next\n      // update() if there's another one.\n      async beginUpdate(batchSize, reset) {\n        // pause observers so users don't see flicker when updating several\n        // objects at once (including the post-reconnect reset-and-reapply\n        // stage), and so that a re-sorting of a query can take advantage of the\n        // full _diffQuery moved calculation instead of applying change one at a\n        // time.\n        if (batchSize > 1 || reset) self._collection.pauseObservers();\n\n        if (reset) await self._collection.remove({});\n      },\n\n      // Apply an update.\n      // XXX better specify this interface (not in terms of a wire message)?\n      update(msg) {\n        var mongoId = MongoID.idParse(msg.id);\n        var doc = self._collection._docs.get(mongoId);\n\n        //When the server's mergebox is disabled for a collection, the client must gracefully handle it when:\n        // *We receive an added message for a document that is already there. Instead, it will be changed\n        // *We reeive a change message for a document that is not there. Instead, it will be added\n        // *We receive a removed messsage for a document that is not there. Instead, noting wil happen.\n\n        //Code is derived from client-side code originally in peerlibrary:control-mergebox\n        //https://github.com/peerlibrary/meteor-control-mergebox/blob/master/client.coffee\n\n        //For more information, refer to discussion \"Initial support for publication strategies in livedata server\":\n        //https://github.com/meteor/meteor/pull/11151\n        if (Meteor.isClient) {\n          if (msg.msg === 'added' && doc) {\n            msg.msg = 'changed';\n          } else if (msg.msg === 'removed' && !doc) {\n            return;\n          } else if (msg.msg === 'changed' && !doc) {\n            msg.msg = 'added';\n            const _ref = msg.fields;\n            for (let field in _ref) {\n              const value = _ref[field];\n              if (value === void 0) {\n                delete msg.fields[field];\n              }\n            }\n          }\n        }\n        // Is this a \"replace the whole doc\" message coming from the quiescence\n        // of method writes to an object? (Note that 'undefined' is a valid\n        // value meaning \"remove it\".)\n        if (msg.msg === 'replace') {\n          var replace = msg.replace;\n          if (!replace) {\n            if (doc) self._collection.remove(mongoId);\n          } else if (!doc) {\n            self._collection.insert(replace);\n          } else {\n            // XXX check that replace has no $ ops\n            self._collection.update(mongoId, replace);\n          }\n          return;\n        } else if (msg.msg === 'added') {\n          if (doc) {\n            throw new Error(\n              'Expected not to find a document already present for an add'\n            );\n          }\n          self._collection.insert({ _id: mongoId, ...msg.fields });\n        } else if (msg.msg === 'removed') {\n          if (!doc)\n            throw new Error(\n              'Expected to find a document already present for removed'\n            );\n          self._collection.remove(mongoId);\n        } else if (msg.msg === 'changed') {\n          if (!doc) throw new Error('Expected to find a document to change');\n          const keys = Object.keys(msg.fields);\n          if (keys.length > 0) {\n            var modifier = {};\n            keys.forEach(key => {\n              const value = msg.fields[key];\n              if (EJSON.equals(doc[key], value)) {\n                return;\n              }\n              if (typeof value === 'undefined') {\n                if (!modifier.$unset) {\n                  modifier.$unset = {};\n                }\n                modifier.$unset[key] = 1;\n              } else {\n                if (!modifier.$set) {\n                  modifier.$set = {};\n                }\n                modifier.$set[key] = value;\n              }\n            });\n            if (Object.keys(modifier).length > 0) {\n              self._collection.update(mongoId, modifier);\n            }\n          }\n        } else {\n          throw new Error(\"I don't know how to deal with this message\");\n        }\n      },\n\n      // Called at the end of a batch of updates.livedata_connection.js:1287\n      endUpdate() {\n        self._collection.resumeObserversClient();\n      },\n\n      // Used to preserve current versions of documents across a store reset.\n      getDoc(id) {\n        return self.findOne(id);\n      },\n\n      ...wrappedStoreCommon,\n    };\n    const wrappedStoreServer = {\n      async beginUpdate(batchSize, reset) {\n        if (batchSize > 1 || reset) self._collection.pauseObservers();\n\n        if (reset) await self._collection.removeAsync({});\n      },\n\n      async update(msg) {\n        var mongoId = MongoID.idParse(msg.id);\n        var doc = self._collection._docs.get(mongoId);\n\n        // Is this a \"replace the whole doc\" message coming from the quiescence\n        // of method writes to an object? (Note that 'undefined' is a valid\n        // value meaning \"remove it\".)\n        if (msg.msg === 'replace') {\n          var replace = msg.replace;\n          if (!replace) {\n            if (doc) await self._collection.removeAsync(mongoId);\n          } else if (!doc) {\n            await self._collection.insertAsync(replace);\n          } else {\n            // XXX check that replace has no $ ops\n            await self._collection.updateAsync(mongoId, replace);\n          }\n          return;\n        } else if (msg.msg === 'added') {\n          if (doc) {\n            throw new Error(\n              'Expected not to find a document already present for an add'\n            );\n          }\n          await self._collection.insertAsync({ _id: mongoId, ...msg.fields });\n        } else if (msg.msg === 'removed') {\n          if (!doc)\n            throw new Error(\n              'Expected to find a document already present for removed'\n            );\n          await self._collection.removeAsync(mongoId);\n        } else if (msg.msg === 'changed') {\n          if (!doc) throw new Error('Expected to find a document to change');\n          const keys = Object.keys(msg.fields);\n          if (keys.length > 0) {\n            var modifier = {};\n            keys.forEach(key => {\n              const value = msg.fields[key];\n              if (EJSON.equals(doc[key], value)) {\n                return;\n              }\n              if (typeof value === 'undefined') {\n                if (!modifier.$unset) {\n                  modifier.$unset = {};\n                }\n                modifier.$unset[key] = 1;\n              } else {\n                if (!modifier.$set) {\n                  modifier.$set = {};\n                }\n                modifier.$set[key] = value;\n              }\n            });\n            if (Object.keys(modifier).length > 0) {\n              await self._collection.updateAsync(mongoId, modifier);\n            }\n          }\n        } else {\n          throw new Error(\"I don't know how to deal with this message\");\n        }\n      },\n\n      // Called at the end of a batch of updates.\n      async endUpdate() {\n        await self._collection.resumeObserversServer();\n      },\n\n      // Used to preserve current versions of documents across a store reset.\n      async getDoc(id) {\n        return self.findOneAsync(id);\n      },\n      ...wrappedStoreCommon,\n    };\n\n\n    // OK, we're going to be a slave, replicating some remote\n    // database, except possibly with some temporary divergence while\n    // we have unacknowledged RPC's.\n    let registerStoreResult;\n    if (Meteor.isClient) {\n      registerStoreResult = self._connection.registerStoreClient(\n        name,\n        wrappedStoreClient\n      );\n    } else {\n      registerStoreResult = self._connection.registerStoreServer(\n        name,\n        wrappedStoreServer\n      );\n    }\n\n    const message = `There is already a collection named \"${name}\"`;\n    const logWarn = () => {\n      console.warn ? console.warn(message) : console.log(message);\n    };\n\n    if (!registerStoreResult) {\n      return logWarn();\n    }\n\n    return registerStoreResult?.then?.(ok => {\n      if (!ok) {\n        logWarn();\n      }\n    });\n  },\n}", "export const SyncMethods = {\n  /**\n   * @summary Find the documents in a collection that match the selector.\n   * @locus Anywhere\n   * @method find\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {Number} options.limit Maximum number of results to return\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default `true`; pass `false` to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the  [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {Boolean} options.disableOplog (Server only) Pass true to disable oplog-tailing on this query. This affects the way server processes calls to `observe` on this query. Disabling the oplog can be useful when working with data that updates in large batches.\n   * @param {Number} options.pollingIntervalMs (Server only) When oplog is disabled (through the use of `disableOplog` or when otherwise not available), the frequency (in milliseconds) of how often to poll this query when observing on the server. Defaults to 10000ms (10 seconds).\n   * @param {Number} options.pollingThrottleMs (Server only) When oplog is disabled (through the use of `disableOplog` or when otherwise not available), the minimum time (in milliseconds) to allow between re-polling when observing on the server. Increasing this will save CPU and mongo load at the expense of slower updates to users. Decreasing this is not recommended. Defaults to 50ms.\n   * @param {Number} options.maxTimeMs (Server only) If set, instructs MongoDB to set a time limit for this cursor's operations. If the operation reaches the specified time limit (in milliseconds) without the having been completed, an exception will be thrown. Useful to prevent an (accidental or malicious) unoptimized query from causing a full collection scan that would disrupt other database users, at the expense of needing to handle the resulting error.\n   * @param {String|Object} options.hint (Server only) Overrides MongoDB's default index selection and query optimization process. Specify an index to force its use, either by its name or index specification. You can also specify `{ $natural : 1 }` to force a forwards collection scan, or `{ $natural : -1 }` for a reverse collection scan. Setting this is only recommended for advanced users.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for this particular cursor. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Mongo.Cursor}\n   */\n  find(...args) {\n    // Collection.find() (return all docs) behaves differently\n    // from Collection.find(undefined) (return 0 docs).  so be\n    // careful about the length of arguments.\n    return this._collection.find(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n  /**\n   * @summary Finds the first document that matches the selector, as ordered by sort and skip options. Returns `undefined` if no matching document is found.\n   * @locus Anywhere\n   * @method findOne\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} [selector] A query describing the documents to find\n   * @param {Object} [options]\n   * @param {MongoSortSpecifier} options.sort Sort order (default: natural order)\n   * @param {Number} options.skip Number of results to skip at the beginning\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   * @param {Boolean} options.reactive (Client only) Default true; pass false to disable reactivity\n   * @param {Function} options.transform Overrides `transform` on the [`Collection`](#collections) for this cursor.  Pass `null` to disable transformation.\n   * @param {String} options.readPreference (Server only) Specifies a custom MongoDB [`readPreference`](https://docs.mongodb.com/manual/core/read-preference) for fetching the document. Possible values are `primary`, `primaryPreferred`, `secondary`, `secondaryPreferred` and `nearest`.\n   * @returns {Object}\n   */\n  findOne(...args) {\n    return this._collection.findOne(\n      this._getFindSelector(args),\n      this._getFindOptions(args)\n    );\n  },\n\n\n  // 'insert' immediately returns the inserted document's new _id.\n  // The others return values immediately if you are in a stub, an in-memory\n  // unmanaged collection, or a mongo-backed collection and you don't pass a\n  // callback. 'update' and 'remove' return the number of affected\n  // documents. 'upsert' returns an object with keys 'numberAffected' and, if an\n  // insert happened, 'insertedId'.\n  //\n  // Otherwise, the semantics are exactly like other methods: they take\n  // a callback as an optional last argument; if no callback is\n  // provided, they block until the operation is complete, and throw an\n  // exception if it fails; if a callback is provided, then they don't\n  // necessarily block, and they call the callback when they finish with error and\n  // result arguments.  (The insert method provides the document ID as its result;\n  // update and remove provide the number of affected docs as the result; upsert\n  // provides an object with numberAffected and maybe insertedId.)\n  //\n  // On the client, blocking is impossible, so if a callback\n  // isn't provided, they just return immediately and any error\n  // information is lost.\n  //\n  // There's one more tweak. On the client, if you don't provide a\n  // callback, then if there is an error, a message will be logged with\n  // Meteor._debug.\n  //\n  // The intent (though this is actually determined by the underlying\n  // drivers) is that the operations should be done synchronously, not\n  // generating their result until the database has acknowledged\n  // them. In the future maybe we should provide a flag to turn this\n  // off.\n\n  _insert(doc, callback) {\n    // Make sure we were passed a document to insert\n    if (!doc) {\n      throw new Error('insert requires an argument');\n    }\n\n\n    // Make a shallow clone of the document, preserving its prototype.\n    doc = Object.create(\n      Object.getPrototypeOf(doc),\n      Object.getOwnPropertyDescriptors(doc)\n    );\n\n    if ('_id' in doc) {\n      if (\n        !doc._id ||\n        !(typeof doc._id === 'string' || doc._id instanceof Mongo.ObjectID)\n      ) {\n        throw new Error(\n          'Meteor requires document _id fields to be non-empty strings or ObjectIDs'\n        );\n      }\n    } else {\n      let generateId = true;\n\n      // Don't generate the id if we're the client and the 'outermost' call\n      // This optimization saves us passing both the randomSeed and the id\n      // Passing both is redundant.\n      if (this._isRemoteCollection()) {\n        const enclosing = DDP._CurrentMethodInvocation.get();\n        if (!enclosing) {\n          generateId = false;\n        }\n      }\n\n      if (generateId) {\n        doc._id = this._makeNewID();\n      }\n    }\n\n\n    // On inserts, always return the id that we generated; on all other\n    // operations, just return the result from the collection.\n    var chooseReturnValueFromCollectionResult = function(result) {\n      if (Meteor._isPromise(result)) return result;\n\n      if (doc._id) {\n        return doc._id;\n      }\n\n      // XXX what is this for??\n      // It's some iteraction between the callback to _callMutatorMethod and\n      // the return value conversion\n      doc._id = result;\n\n      return result;\n    };\n\n    const wrappedCallback = wrapCallback(\n      callback,\n      chooseReturnValueFromCollectionResult\n    );\n\n    if (this._isRemoteCollection()) {\n      const result = this._callMutatorMethod('insert', [doc], wrappedCallback);\n      return chooseReturnValueFromCollectionResult(result);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    try {\n      // If the user provided a callback and the collection implements this\n      // operation asynchronously, then queryRet will be undefined, and the\n      // result will be returned through the callback instead.\n      let result;\n      if (!!wrappedCallback) {\n        this._collection.insert(doc, wrappedCallback);\n      } else {\n        // If we don't have the callback, we assume the user is using the promise.\n        // We can't just pass this._collection.insert to the promisify because it would lose the context.\n        result = this._collection.insert(doc);\n      }\n\n      return chooseReturnValueFromCollectionResult(result);\n    } catch (e) {\n      if (callback) {\n        callback(e);\n        return null;\n      }\n      throw e;\n    }\n  },\n\n  /**\n   * @summary Insert a document in the collection.  Returns its unique _id.\n   * @locus Anywhere\n   * @method  insert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {Object} doc The document to insert. May not yet have an _id attribute, in which case Meteor will generate one for you.\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the _id as the second.\n   */\n  insert(doc, callback) {\n    return this._insert(doc, callback);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection. Returns the number of matched documents.\n   * @locus Anywhere\n   * @method update\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Boolean} options.upsert True to insert a document if no matching documents are found.\n   * @param {Array} options.arrayFilters Optional. Used in combination with MongoDB [filtered positional operator](https://docs.mongodb.com/manual/reference/operator/update/positional-filtered/) to specify which elements to modify in an array field.\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  update(selector, modifier, ...optionsAndCallback) {\n    const callback = popCallbackFromArgs(optionsAndCallback);\n\n    // We've already popped off the callback, so we are left with an array\n    // of one or zero items\n    const options = { ...(optionsAndCallback[0] || null) };\n    let insertedId;\n    if (options && options.upsert) {\n      // set `insertedId` if absent.  `insertedId` is a Meteor extension.\n      if (options.insertedId) {\n        if (\n          !(\n            typeof options.insertedId === 'string' ||\n            options.insertedId instanceof Mongo.ObjectID\n          )\n        )\n          throw new Error('insertedId must be string or ObjectID');\n        insertedId = options.insertedId;\n      } else if (!selector || !selector._id) {\n        insertedId = this._makeNewID();\n        options.generatedId = true;\n        options.insertedId = insertedId;\n      }\n    }\n\n    selector = Mongo.Collection._rewriteSelector(selector, {\n      fallbackId: insertedId,\n    });\n\n    const wrappedCallback = wrapCallback(callback);\n\n    if (this._isRemoteCollection()) {\n      const args = [selector, modifier, options];\n      return this._callMutatorMethod('update', args, callback);\n    }\n\n    // it's my collection.  descend into the collection object\n    // and propagate any exception.\n    // If the user provided a callback and the collection implements this\n    // operation asynchronously, then queryRet will be undefined, and the\n    // result will be returned through the callback instead.\n    //console.log({callback, options, selector, modifier, coll: this._collection});\n    try {\n      // If the user provided a callback and the collection implements this\n      // operation asynchronously, then queryRet will be undefined, and the\n      // result will be returned through the callback instead.\n      return this._collection.update(\n        selector,\n        modifier,\n        options,\n        wrappedCallback\n      );\n    } catch (e) {\n      if (callback) {\n        callback(e);\n        return null;\n      }\n      throw e;\n    }\n  },\n\n  /**\n   * @summary Remove documents from the collection\n   * @locus Anywhere\n   * @method remove\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to remove\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  remove(selector, callback) {\n    selector = Mongo.Collection._rewriteSelector(selector);\n\n    if (this._isRemoteCollection()) {\n      return this._callMutatorMethod('remove', [selector], callback);\n    }\n\n\n    // it's my collection.  descend into the collection1 object\n    // and propagate any exception.\n    return this._collection.remove(selector);\n  },\n\n  /**\n   * @summary Asynchronously modifies one or more documents in the collection, or insert one if no matching documents were found. Returns an object with keys `numberAffected` (the number of documents modified)  and `insertedId` (the unique _id of the document that was inserted, if any).\n   * @locus Anywhere\n   * @method upsert\n   * @memberof Mongo.Collection\n   * @instance\n   * @param {MongoSelector} selector Specifies which documents to modify\n   * @param {MongoModifier} modifier Specifies how to modify the documents\n   * @param {Object} [options]\n   * @param {Boolean} options.multi True to modify all matching documents; false to only modify one of the matching documents (the default).\n   * @param {Function} [callback] Optional.  If present, called with an error object as the first argument and, if no error, the number of affected documents as the second.\n   */\n  upsert(selector, modifier, options, callback) {\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    return this.update(\n      selector,\n      modifier,\n      {\n        ...options,\n        _returnObject: true,\n        upsert: true,\n      });\n  },\n}\n\n// Convert the callback to not return a result if there is an error\nfunction wrapCallback(callback, convertResult) {\n  return (\n    callback &&\n    function(error, result) {\n      if (error) {\n        callback(error);\n      } else if (typeof convertResult === 'function') {\n        callback(error, convertResult(result));\n      } else {\n        callback(error, result);\n      }\n    }\n  );\n}\n\nfunction popCallbackFromArgs(args) {\n  // Pull off any callback (or perhaps a 'callback' variable that was passed\n  // in undefined, like how 'upsert' does it).\n  if (\n    args.length &&\n    (args[args.length - 1] === undefined ||\n      args[args.length - 1] instanceof Function)\n  ) {\n    return args.pop();\n  }\n}\n", "export const normalizeProjection = options => {\n  // transform fields key in projection\n  const { fields, projection, ...otherOptions } = options || {};\n  // TODO: enable this comment when deprecating the fields option\n  // Log.debug(`fields option has been deprecated, please use the new 'projection' instead`)\n\n  return {\n    ...otherOptions,\n    ...(projection || fields ? { projection: fields || projection } : {}),\n  };\n};\n"]}