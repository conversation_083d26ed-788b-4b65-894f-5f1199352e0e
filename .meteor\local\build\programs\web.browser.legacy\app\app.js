var require = meteorInstall({"imports":{"api":{"ai":{"ollamaService.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/api/ai/ollamaService.js                                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _Meteor$settings$priv, _Meteor$settings$priv2, _Meteor$settings$priv3, _Meteor$settings$priv4;
  module1.export({
    OllamaService: function () {
      return OllamaService;
    }
  });
  ___INIT_METEOR_FAST_REFRESH(module);
  // Initialize Ollama client
  var ollamaEndpoint = ((_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : (_Meteor$settings$priv2 = _Meteor$settings$priv.ollama) === null || _Meteor$settings$priv2 === void 0 ? void 0 : _Meteor$settings$priv2.endpoint) || 'http://localhost:11434';
  var defaultModel = ((_Meteor$settings$priv3 = Meteor.settings.private) === null || _Meteor$settings$priv3 === void 0 ? void 0 : (_Meteor$settings$priv4 = _Meteor$settings$priv3.ollama) === null || _Meteor$settings$priv4 === void 0 ? void 0 : _Meteor$settings$priv4.model) || 'tinyllama:1.1b'; // Changed to tinyllama:1.1b (extremely small model)

  // Helper function to handle API errors
  var handleApiError = function (error, context) {
    console.error("Error in " + context + ":", error);
    if (error.code === 'ECONNREFUSED') {
      throw new Meteor.Error('connection-failed', 'Could not connect to Ollama. Please make sure Ollama is running on your machine.');
    }
    throw new Meteor.Error('ai-service-error', "AI service error: " + error.message);
  };

  // Helper function to call Ollama API
  function callOllama(prompt) {
    var response, errorData, data;
    return _regeneratorRuntime.async(function () {
      function callOllama$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            _context.next = 3;
            return _regeneratorRuntime.awrap(fetch(ollamaEndpoint + "/api/generate", {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                model: defaultModel,
                prompt: prompt,
                stream: false
              })
            }));
          case 3:
            response = _context.sent;
            if (response.ok) {
              _context.next = 9;
              break;
            }
            _context.next = 7;
            return _regeneratorRuntime.awrap(response.json());
          case 7:
            errorData = _context.sent;
            throw new Meteor.Error('ai-service-error', "AI service error: " + (errorData.error || response.statusText));
          case 9:
            _context.next = 11;
            return _regeneratorRuntime.awrap(response.json());
          case 11:
            data = _context.sent;
            return _context.abrupt("return", data.response);
          case 15:
            _context.prev = 15;
            _context.t0 = _context["catch"](0);
            handleApiError(_context.t0, 'Ollama API call');
          case 18:
          case "end":
            return _context.stop();
        }
      }
      return callOllama$;
    }(), null, null, [[0, 15]], Promise);
  }
  var OllamaService = {
    callOllama: callOllama,
    analyzeTask: function () {
      function _callee(task) {
        var prompt;
        return _regeneratorRuntime.async(function () {
          function _callee$(_context2) {
            while (1) switch (_context2.prev = _context2.next) {
              case 0:
                // Create a simplified prompt with minimal data
                prompt = "Analyze this task: " + (task.title || 'Untitled task') + ". " + (task.description || '');
                console.log('Ollama prompt (analyzeTask):', prompt);
                _context2.next = 4;
                return _regeneratorRuntime.awrap(callOllama(prompt));
              case 4:
                return _context2.abrupt("return", _context2.sent);
              case 5:
              case "end":
                return _context2.stop();
            }
          }
          return _callee$;
        }(), null, null, null, Promise);
      }
      return _callee;
    }(),
    suggestTaskOptimization: function () {
      function _callee2(taskId) {
        var task, prompt;
        return _regeneratorRuntime.async(function () {
          function _callee2$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                _context3.next = 2;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 2:
                task = _context3.sent;
                if (task) {
                  _context3.next = 5;
                  break;
                }
                throw new Meteor.Error('task-not-found', 'Task not found');
              case 5:
                prompt = "Suggest improvements for this task: " + (task.title || 'Untitled task') + ". " + (task.description || '');
                console.log('Ollama prompt (suggestTaskOptimization):', prompt);
                _context3.next = 9;
                return _regeneratorRuntime.awrap(callOllama(prompt));
              case 9:
                return _context3.abrupt("return", _context3.sent);
              case 10:
              case "end":
                return _context3.stop();
            }
          }
          return _callee2$;
        }(), null, null, null, Promise);
      }
      return _callee2;
    }(),
    predictTaskCompletion: function () {
      function _callee3(taskId) {
        var task, prompt;
        return _regeneratorRuntime.async(function () {
          function _callee3$(_context4) {
            while (1) switch (_context4.prev = _context4.next) {
              case 0:
                _context4.next = 2;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 2:
                task = _context4.sent;
                if (task) {
                  _context4.next = 5;
                  break;
                }
                throw new Meteor.Error('task-not-found', 'Task not found');
              case 5:
                prompt = "Predict completion time for: " + (task.title || 'Untitled task') + ". " + (task.description || '');
                console.log('Ollama prompt (predictTaskCompletion):', prompt);
                _context4.next = 9;
                return _regeneratorRuntime.awrap(callOllama(prompt));
              case 9:
                return _context4.abrupt("return", _context4.sent);
              case 10:
              case "end":
                return _context4.stop();
            }
          }
          return _callee3$;
        }(), null, null, null, Promise);
      }
      return _callee3;
    }(),
    generateTaskSummary: function () {
      function _callee4(taskId) {
        var task, prompt;
        return _regeneratorRuntime.async(function () {
          function _callee4$(_context5) {
            while (1) switch (_context5.prev = _context5.next) {
              case 0:
                _context5.next = 2;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 2:
                task = _context5.sent;
                if (task) {
                  _context5.next = 5;
                  break;
                }
                throw new Meteor.Error('task-not-found', 'Task not found');
              case 5:
                prompt = "Summarize this task: " + (task.title || 'Untitled task') + ". " + (task.description || '');
                console.log('Ollama prompt (generateTaskSummary):', prompt);
                _context5.next = 9;
                return _regeneratorRuntime.awrap(callOllama(prompt));
              case 9:
                return _context5.abrupt("return", _context5.sent);
              case 10:
              case "end":
                return _context5.stop();
            }
          }
          return _callee4$;
        }(), null, null, null, Promise);
      }
      return _callee4;
    }(),
    suggestTaskDivision: function () {
      function _callee5(task) {
        var prompt;
        return _regeneratorRuntime.async(function () {
          function _callee5$(_context6) {
            while (1) switch (_context6.prev = _context6.next) {
              case 0:
                prompt = "Suggest how to divide this task among team members: " + (task.title || 'Untitled task') + ". " + (task.description || '');
                console.log('Ollama prompt (suggestTaskDivision):', prompt);
                _context6.next = 4;
                return _regeneratorRuntime.awrap(callOllama(prompt));
              case 4:
                return _context6.abrupt("return", _context6.sent);
              case 5:
              case "end":
                return _context6.stop();
            }
          }
          return _callee5$;
        }(), null, null, null, Promise);
      }
      return _callee5;
    }(),
    recommendTools: function () {
      function _callee6(taskId) {
        var task, prompt;
        return _regeneratorRuntime.async(function () {
          function _callee6$(_context7) {
            while (1) switch (_context7.prev = _context7.next) {
              case 0:
                _context7.next = 2;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 2:
                task = _context7.sent;
                if (task) {
                  _context7.next = 5;
                  break;
                }
                throw new Meteor.Error('task-not-found', 'Task not found');
              case 5:
                prompt = "Recommend tools and techniques for this task: " + (task.title || 'Untitled task') + ". " + (task.description || '');
                console.log('Ollama prompt (recommendTools):', prompt);
                _context7.next = 9;
                return _regeneratorRuntime.awrap(callOllama(prompt));
              case 9:
                return _context7.abrupt("return", _context7.sent);
              case 10:
              case "end":
                return _context7.stop();
            }
          }
          return _callee6$;
        }(), null, null, null, Promise);
      }
      return _callee6;
    }(),
    predictDailyProgress: function () {
      function _callee7(taskId) {
        var task, prompt;
        return _regeneratorRuntime.async(function () {
          function _callee7$(_context8) {
            while (1) switch (_context8.prev = _context8.next) {
              case 0:
                _context8.next = 2;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 2:
                task = _context8.sent;
                if (task) {
                  _context8.next = 5;
                  break;
                }
                throw new Meteor.Error('task-not-found', 'Task not found');
              case 5:
                prompt = "Predict daily progress needed to complete by deadline for: " + (task.title || 'Untitled task') + ". " + (task.description || '') + ". Due date: " + (task.dueDate || 'Not specified');
                console.log('Ollama prompt (predictDailyProgress):', prompt);
                _context8.next = 9;
                return _regeneratorRuntime.awrap(callOllama(prompt));
              case 9:
                return _context8.abrupt("return", _context8.sent);
              case 10:
              case "end":
                return _context8.stop();
            }
          }
          return _callee7$;
        }(), null, null, null, Promise);
      }
      return _callee7;
    }()
  };
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"index.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/api/index.js                                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    Tasks: function () {
      return Tasks;
    }
  });
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 0);
  var Tasks;
  module1.link("./tasks", {
    Tasks: function (v) {
      Tasks = v;
    }
  }, 1);
  ___INIT_METEOR_FAST_REFRESH(module);
  // Ensure collections are available on both client and server
  if (Meteor.isClient) {
    window.Tasks = Tasks;
  }
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"tasks.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/api/tasks.js                                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _toConsumableArray;
  module1.link("@babel/runtime/helpers/toConsumableArray", {
    default: function (v) {
      _toConsumableArray = v;
    }
  }, 0);
  var _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default: function (v) {
      _objectSpread = v;
    }
  }, 1);
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 2);
  module1.export({
    Tasks: function () {
      return Tasks;
    },
    taskCategories: function () {
      return taskCategories;
    },
    taskLabels: function () {
      return taskLabels;
    }
  });
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 0);
  var Mongo;
  module1.link("meteor/mongo", {
    Mongo: function (v) {
      Mongo = v;
    }
  }, 1);
  var check;
  module1.link("meteor/check", {
    check: function (v) {
      check = v;
    }
  }, 2);
  var Match;
  module1.link("meteor/check", {
    Match: function (v) {
      Match = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  var Tasks = new Mongo.Collection('tasks');
  var taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];
  var taskLabels = [{
    name: 'Bug',
    color: '#ef4444'
  }, {
    name: 'Feature',
    color: '#3b82f6'
  }, {
    name: 'Enhancement',
    color: '#10b981'
  }, {
    name: 'Documentation',
    color: '#8b5cf6'
  }, {
    name: 'Urgent',
    color: '#f59e0b'
  }, {
    name: 'Blocked',
    color: '#6b7280'
  }];
  if (Meteor.isServer) {
    // Publications
    Meteor.publish('tasks', function () {
      function _callee() {
        var _user$roles;
        var user, isAdmin;
        return _regeneratorRuntime.async(function () {
          function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                if (this.userId) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return", this.ready());
              case 2:
                _context.next = 4;
                return _regeneratorRuntime.awrap(Meteor.users.findOneAsync(this.userId));
              case 4:
                user = _context.sent;
                isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin'); // If admin, show all tasks
                if (!isAdmin) {
                  _context.next = 8;
                  break;
                }
                return _context.abrupt("return", Tasks.find({}, {
                  sort: {
                    createdAt: -1
                  },
                  fields: {
                    title: 1,
                    description: 1,
                    startDate: 1,
                    dueDate: 1,
                    priority: 1,
                    status: 1,
                    assignedTo: 1,
                    checklist: 1,
                    category: 1,
                    labels: 1,
                    progress: 1,
                    attachments: 1,
                    links: 1,
                    createdAt: 1,
                    createdBy: 1,
                    updatedAt: 1,
                    updatedBy: 1
                  }
                }));
              case 8:
                return _context.abrupt("return", Tasks.find({
                  $or: [{
                    assignedTo: this.userId
                  }, {
                    createdBy: this.userId
                  }]
                }, {
                  sort: {
                    createdAt: -1
                  },
                  fields: {
                    title: 1,
                    description: 1,
                    startDate: 1,
                    dueDate: 1,
                    priority: 1,
                    status: 1,
                    assignedTo: 1,
                    checklist: 1,
                    category: 1,
                    labels: 1,
                    progress: 1,
                    attachments: 1,
                    links: 1,
                    createdAt: 1,
                    createdBy: 1,
                    updatedAt: 1,
                    updatedBy: 1
                  }
                }));
              case 9:
              case "end":
                return _context.stop();
            }
          }
          return _callee$;
        }(), null, this, null, Promise);
      }
      return _callee;
    }());

    // Publish user data for tasks
    Meteor.publish('taskUsers', function () {
      console.log('Starting taskUsers publication');
      if (!this.userId) {
        console.log('No userId, returning ready');
        return this.ready();
      }

      // Get all tasks
      var tasks = Tasks.find({}).fetch();
      console.log('Found tasks:', tasks.length);

      // Collect all user IDs from tasks
      var userIds = new Set();
      tasks.forEach(function (task) {
        // Add users who uploaded attachments
        if (task.attachments) {
          task.attachments.forEach(function (attachment) {
            if (attachment.uploadedBy) {
              userIds.add(String(attachment.uploadedBy));
            }
          });
        }
        // Add users who added links
        if (task.links) {
          task.links.forEach(function (link) {
            if (link.addedBy) {
              userIds.add(String(link.addedBy));
            }
          });
        }
        // Add assigned users
        if (task.assignedTo) {
          task.assignedTo.forEach(function (userId) {
            userIds.add(String(userId));
          });
        }
      });
      var userIdArray = Array.from(userIds);
      console.log('Publishing user data for IDs:', userIdArray);

      // Find users and log what we found
      var users = Meteor.users.find({
        _id: {
          $in: userIdArray
        }
      }, {
        fields: {
          _id: 1,
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      }).fetch();
      console.log('Found users:', users.map(function (u) {
        var _u$profile, _u$profile2;
        return {
          _id: u._id,
          name: ((((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '') + " " + (((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '')).trim(),
          hasProfile: !!u.profile
        };
      }));

      // Return the cursor
      return Meteor.users.find({
        _id: {
          $in: userIdArray
        }
      }, {
        fields: {
          _id: 1,
          emails: 1,
          roles: 1,
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      });
    });

    // Add a specific publication for a single task
    Meteor.publish('task', function (taskId) {
      var _user$roles2;
      check(taskId, String);
      if (!this.userId) {
        return this.ready();
      }
      var user = Meteor.users.findOne(this.userId);
      var isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');

      // Return a cursor that will update reactively
      var cursor = Tasks.find({
        _id: taskId
      }, {
        fields: {
          title: 1,
          description: 1,
          startDate: 1,
          dueDate: 1,
          priority: 1,
          status: 1,
          assignedTo: 1,
          checklist: 1,
          category: 1,
          labels: 1,
          progress: 1,
          attachments: 1,
          links: 1,
          createdAt: 1,
          createdBy: 1,
          updatedAt: 1,
          updatedBy: 1
        }
      });

      // Log for debugging
      console.log('Publishing task:', taskId);
      cursor.observe({
        added: function (doc) {
          return console.log('Task added to publication:', doc._id);
        },
        changed: function (doc) {
          return console.log('Task changed in publication:', doc._id);
        },
        removed: function (doc) {
          return console.log('Task removed from publication:', doc._id);
        }
      });
      return cursor;
    });
  }
  Meteor.methods({
    'tasks.insert': function () {
      function _callee2(task) {
        var processedChecklist, taskToInsert, result;
        return _regeneratorRuntime.async(function () {
          function _callee2$(_context2) {
            while (1) switch (_context2.prev = _context2.next) {
              case 0:
                check(task, {
                  title: String,
                  description: String,
                  startDate: Date,
                  dueDate: Date,
                  priority: String,
                  status: String,
                  assignedTo: Array,
                  checklist: Array,
                  category: String,
                  labels: Array,
                  progress: Number
                });
                if (this.userId) {
                  _context2.next = 3;
                  break;
                }
                throw new Meteor.Error('Not authorized.');
              case 3:
                console.log('Creating new task:', task); // Debug log

                // Process checklist items
                processedChecklist = task.checklist.map(function (item) {
                  return {
                    text: item.text,
                    completed: item.completed || false
                  };
                });
                taskToInsert = _objectSpread(_objectSpread({}, task), {}, {
                  createdAt: new Date(),
                  createdBy: this.userId,
                  updatedAt: new Date(),
                  updatedBy: this.userId,
                  progress: task.progress || 0,
                  status: 'pending',
                  // Default status
                  checklist: processedChecklist,
                  labels: task.labels || [],
                  category: task.category || '',
                  assignedTo: task.assignedTo || []
                });
                console.log('Inserting task with values:', taskToInsert); // Debug log
                _context2.prev = 7;
                _context2.next = 10;
                return _regeneratorRuntime.awrap(Tasks.insertAsync(taskToInsert));
              case 10:
                result = _context2.sent;
                console.log('Task created successfully:', result); // Debug log
                return _context2.abrupt("return", result);
              case 15:
                _context2.prev = 15;
                _context2.t0 = _context2["catch"](7);
                console.error('Error creating task:', _context2.t0);
                throw new Meteor.Error('task-creation-failed', _context2.t0.message);
              case 19:
              case "end":
                return _context2.stop();
            }
          }
          return _callee2$;
        }(), null, this, [[7, 15]], Promise);
      }
      return _callee2;
    }(),
    'tasks.update': function () {
      function _callee3(taskId, task) {
        var _user$roles3, existingTask, user, isAdmin, completedItems, totalItems, progress, status, taskToUpdate, result;
        return _regeneratorRuntime.async(function () {
          function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                console.log('Starting task update:', {
                  taskId: taskId,
                  task: task
                });
                check(taskId, String);
                check(task, {
                  title: String,
                  description: String,
                  startDate: Date,
                  dueDate: Date,
                  priority: String,
                  assignedTo: Array,
                  checklist: Array,
                  category: Match.Optional(String),
                  labels: Match.Optional(Array),
                  progress: Match.Optional(Number),
                  status: Match.Optional(String),
                  attachments: Match.Optional(Array)
                });
                if (this.userId) {
                  _context3.next = 6;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
              case 6:
                _context3.next = 8;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 8:
                existingTask = _context3.sent;
                if (existingTask) {
                  _context3.next = 11;
                  break;
                }
                throw new Meteor.Error('not-found', 'Task not found');
              case 11:
                _context3.next = 13;
                return _regeneratorRuntime.awrap(Meteor.users.findOneAsync(this.userId));
              case 13:
                user = _context3.sent;
                isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');
                if (!(!isAdmin && !existingTask.assignedTo.includes(this.userId))) {
                  _context3.next = 17;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
              case 17:
                // Calculate progress based on checklist
                completedItems = task.checklist.filter(function (item) {
                  return item.completed;
                }).length;
                totalItems = task.checklist.length;
                progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0; // Update task status based on progress
                status = task.status;
                if (progress === 100) {
                  status = 'completed';
                } else if (progress > 0) {
                  status = 'in-progress';
                } else {
                  status = 'pending';
                }
                taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {
                  updatedAt: new Date(),
                  updatedBy: this.userId,
                  progress: progress,
                  status: status,
                  category: task.category || existingTask.category || '',
                  labels: task.labels || existingTask.labels || [],
                  attachments: task.attachments || existingTask.attachments || []
                });
                _context3.next = 25;
                return _regeneratorRuntime.awrap(Tasks.updateAsync({
                  _id: taskId
                }, {
                  $set: taskToUpdate
                }));
              case 25:
                result = _context3.sent;
                if (!(result === 0)) {
                  _context3.next = 28;
                  break;
                }
                throw new Meteor.Error('update-failed', 'Failed to update task');
              case 28:
                return _context3.abrupt("return", result);
              case 31:
                _context3.prev = 31;
                _context3.t0 = _context3["catch"](0);
                console.error('Error updating task:', _context3.t0);
                if (!(_context3.t0 instanceof Meteor.Error)) {
                  _context3.next = 36;
                  break;
                }
                throw _context3.t0;
              case 36:
                throw new Meteor.Error('server-error', _context3.t0.message || 'An unexpected error occurred');
              case 37:
              case "end":
                return _context3.stop();
            }
          }
          return _callee3$;
        }(), null, this, [[0, 31]], Promise);
      }
      return _callee3;
    }(),
    'tasks.delete': function () {
      function _callee4(taskId) {
        var result;
        return _regeneratorRuntime.async(function () {
          function _callee4$(_context4) {
            while (1) switch (_context4.prev = _context4.next) {
              case 0:
                check(taskId, String);
                if (this.userId) {
                  _context4.next = 3;
                  break;
                }
                throw new Meteor.Error('Not authorized.');
              case 3:
                _context4.prev = 3;
                _context4.next = 6;
                return _regeneratorRuntime.awrap(Tasks.removeAsync(taskId));
              case 6:
                result = _context4.sent;
                if (!(result === 0)) {
                  _context4.next = 9;
                  break;
                }
                throw new Meteor.Error('not-found', 'Task not found');
              case 9:
                return _context4.abrupt("return", result);
              case 12:
                _context4.prev = 12;
                _context4.t0 = _context4["catch"](3);
                console.error('Error deleting task:', _context4.t0);
                throw new Meteor.Error('task-delete-failed', _context4.t0.message);
              case 16:
              case "end":
                return _context4.stop();
            }
          }
          return _callee4$;
        }(), null, this, [[3, 12]], Promise);
      }
      return _callee4;
    }(),
    'tasks.updateProgress': function (taskId, progress) {
      check(taskId, String);
      check(progress, Number);
      if (!this.userId) {
        throw new Meteor.Error('Not authorized.');
      }
      var task = Tasks.findOne(taskId);
      if (!task) {
        throw new Meteor.Error('Task not found.');
      }

      // Check if user is assigned to the task
      if (!task.assignedTo.includes(this.userId)) {
        throw new Meteor.Error('Not authorized to modify this task.');
      }

      // Update task status based on progress
      var status = task.status;
      if (progress === 100) {
        status = 'completed';
      } else if (progress > 0) {
        status = 'in-progress';
      }
      return Tasks.update(taskId, {
        $set: {
          progress: progress,
          status: status,
          updatedAt: new Date(),
          updatedBy: this.userId
        }
      });
    },
    'tasks.toggleChecklistItem': function () {
      function _callee5(taskId, itemIndex) {
        var _user$roles4, task, user, isAdmin, checklist, updatedChecklist, completedItems, totalItems, progress, status, existingTask, updateResult, updatedTask;
        return _regeneratorRuntime.async(function () {
          function _callee5$(_context5) {
            while (1) switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                console.log('Starting toggleChecklistItem with:', {
                  taskId: taskId,
                  itemIndex: itemIndex,
                  userId: this.userId
                });
                if (this.userId) {
                  _context5.next = 5;
                  break;
                }
                console.log('No user ID found');
                throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
              case 5:
                if (!(!taskId || typeof taskId !== 'string')) {
                  _context5.next = 8;
                  break;
                }
                console.log('Invalid taskId:', taskId);
                throw new Meteor.Error('invalid-input', 'Invalid task ID');
              case 8:
                if (!(typeof itemIndex !== 'number' || itemIndex < 0)) {
                  _context5.next = 11;
                  break;
                }
                console.log('Invalid itemIndex:', itemIndex);
                throw new Meteor.Error('invalid-input', 'Invalid checklist item index');
              case 11:
                _context5.next = 13;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 13:
                task = _context5.sent;
                console.log('Found task:', task);
                if (task) {
                  _context5.next = 18;
                  break;
                }
                console.log('Task not found');
                throw new Meteor.Error('not-found', 'Task not found');
              case 18:
                _context5.next = 20;
                return _regeneratorRuntime.awrap(Meteor.users.findOneAsync(this.userId));
              case 20:
                user = _context5.sent;
                console.log('Found user:', user);
                isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');
                console.log('Is admin:', isAdmin);
                if (!(!isAdmin && !task.assignedTo.includes(this.userId))) {
                  _context5.next = 27;
                  break;
                }
                console.log('User not authorized:', {
                  userId: this.userId,
                  assignedTo: task.assignedTo
                });
                throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');
              case 27:
                checklist = task.checklist || [];
                console.log('Current checklist:', checklist);
                if (!(itemIndex >= checklist.length)) {
                  _context5.next = 32;
                  break;
                }
                console.log('Invalid item index:', {
                  itemIndex: itemIndex,
                  checklistLength: checklist.length
                });
                throw new Meteor.Error('invalid-index', 'Invalid checklist item index');
              case 32:
                // Create a new array to ensure reactivity
                updatedChecklist = _toConsumableArray(checklist);
                updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {
                  completed: !updatedChecklist[itemIndex].completed
                });
                console.log('Updated checklist:', updatedChecklist);

                // Calculate progress
                completedItems = updatedChecklist.filter(function (item) {
                  return item.completed;
                }).length;
                totalItems = updatedChecklist.length;
                progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0; // Update task status based on progress
                if (progress === 100) {
                  status = 'completed';
                } else if (progress > 0) {
                  status = 'in-progress';
                } else {
                  status = 'pending';
                }
                console.log('Updating task with:', {
                  taskId: taskId,
                  updatedChecklist: updatedChecklist,
                  progress: progress,
                  status: status
                });

                // First verify the task still exists
                _context5.next = 42;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 42:
                existingTask = _context5.sent;
                if (existingTask) {
                  _context5.next = 45;
                  break;
                }
                throw new Meteor.Error('task-not-found', 'Task no longer exists');
              case 45:
                _context5.next = 47;
                return _regeneratorRuntime.awrap(Tasks.updateAsync({
                  _id: taskId
                }, {
                  $set: {
                    checklist: updatedChecklist,
                    progress: progress,
                    status: status,
                    updatedAt: new Date(),
                    updatedBy: this.userId
                  }
                }));
              case 47:
                updateResult = _context5.sent;
                console.log('Update result:', updateResult);
                if (!(updateResult === 0)) {
                  _context5.next = 51;
                  break;
                }
                throw new Meteor.Error('update-failed', 'Failed to update task');
              case 51:
                _context5.next = 53;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 53:
                updatedTask = _context5.sent;
                console.log('Task after update:', updatedTask);
                return _context5.abrupt("return", updateResult);
              case 58:
                _context5.prev = 58;
                _context5.t0 = _context5["catch"](0);
                console.error('Error in toggleChecklistItem:', _context5.t0);
                if (!(_context5.t0 instanceof Meteor.Error)) {
                  _context5.next = 63;
                  break;
                }
                throw _context5.t0;
              case 63:
                throw new Meteor.Error('server-error', _context5.t0.message || 'An unexpected error occurred');
              case 64:
              case "end":
                return _context5.stop();
            }
          }
          return _callee5$;
        }(), null, this, [[0, 58]], Promise);
      }
      return _callee5;
    }(),
    'tasks.addAttachment': function () {
      function _callee6(taskId, fileData) {
        var task, uploaderId, result, updatedTask;
        return _regeneratorRuntime.async(function () {
          function _callee6$(_context6) {
            while (1) switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                if (this.userId) {
                  _context6.next = 3;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
              case 3:
                _context6.next = 5;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 5:
                task = _context6.sent;
                if (task) {
                  _context6.next = 8;
                  break;
                }
                throw new Meteor.Error('not-found', 'Task not found');
              case 8:
                if (task.assignedTo.includes(this.userId)) {
                  _context6.next = 10;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');
              case 10:
                if (!(!fileData || !fileData.name || !fileData.data)) {
                  _context6.next = 12;
                  break;
                }
                throw new Meteor.Error('invalid-input', 'Invalid file data');
              case 12:
                // Ensure we're storing the user ID as a string
                uploaderId = String(this.userId); // Add the file data to attachments with uploader info
                _context6.next = 15;
                return _regeneratorRuntime.awrap(Tasks.updateAsync({
                  _id: taskId
                }, {
                  $push: {
                    attachments: {
                      name: fileData.name,
                      type: fileData.type,
                      data: fileData.data,
                      uploadedAt: new Date(),
                      uploadedBy: uploaderId
                    }
                  },
                  $set: {
                    updatedAt: new Date(),
                    updatedBy: uploaderId
                  }
                }));
              case 15:
                result = _context6.sent;
                if (!(result === 0)) {
                  _context6.next = 18;
                  break;
                }
                throw new Meteor.Error('update-failed', 'Failed to add attachment');
              case 18:
                _context6.next = 20;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 20:
                updatedTask = _context6.sent;
                console.log('Task after adding attachment:', updatedTask);
                return _context6.abrupt("return", updatedTask);
              case 25:
                _context6.prev = 25;
                _context6.t0 = _context6["catch"](0);
                console.error('Error adding attachment:', _context6.t0);
                if (!(_context6.t0 instanceof Meteor.Error)) {
                  _context6.next = 30;
                  break;
                }
                throw _context6.t0;
              case 30:
                throw new Meteor.Error('server-error', _context6.t0.message || 'An unexpected error occurred');
              case 31:
              case "end":
                return _context6.stop();
            }
          }
          return _callee6$;
        }(), null, this, [[0, 25]], Promise);
      }
      return _callee6;
    }(),
    'tasks.addLink': function () {
      function _callee7(taskId, link) {
        var task, result, updatedTask;
        return _regeneratorRuntime.async(function () {
          function _callee7$(_context7) {
            while (1) switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                if (this.userId) {
                  _context7.next = 3;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
              case 3:
                _context7.next = 5;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 5:
                task = _context7.sent;
                if (task) {
                  _context7.next = 8;
                  break;
                }
                throw new Meteor.Error('not-found', 'Task not found');
              case 8:
                if (task.assignedTo.includes(this.userId)) {
                  _context7.next = 10;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');
              case 10:
                if (link) {
                  _context7.next = 12;
                  break;
                }
                throw new Meteor.Error('invalid-input', 'Link URL is required');
              case 12:
                _context7.prev = 12;
                new URL(link);
                _context7.next = 19;
                break;
              case 16:
                _context7.prev = 16;
                _context7.t0 = _context7["catch"](12);
                throw new Meteor.Error('invalid-input', 'Invalid URL format');
              case 19:
                _context7.next = 21;
                return _regeneratorRuntime.awrap(Tasks.updateAsync({
                  _id: taskId
                }, {
                  $push: {
                    links: {
                      url: link,
                      addedAt: new Date(),
                      addedBy: String(this.userId)
                    }
                  },
                  $set: {
                    updatedAt: new Date(),
                    updatedBy: String(this.userId)
                  }
                }));
              case 21:
                result = _context7.sent;
                if (!(result === 0)) {
                  _context7.next = 24;
                  break;
                }
                throw new Meteor.Error('update-failed', 'Failed to add link');
              case 24:
                _context7.next = 26;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 26:
                updatedTask = _context7.sent;
                console.log('Task after adding link:', updatedTask);
                return _context7.abrupt("return", updatedTask);
              case 31:
                _context7.prev = 31;
                _context7.t1 = _context7["catch"](0);
                console.error('Error adding link:', _context7.t1);
                if (!(_context7.t1 instanceof Meteor.Error)) {
                  _context7.next = 36;
                  break;
                }
                throw _context7.t1;
              case 36:
                throw new Meteor.Error('server-error', _context7.t1.message || 'An unexpected error occurred');
              case 37:
              case "end":
                return _context7.stop();
            }
          }
          return _callee7$;
        }(), null, this, [[0, 31], [12, 16]], Promise);
      }
      return _callee7;
    }(),
    'tasks.removeAttachment': function () {
      function _callee8(taskId, attachmentIndex) {
        var task, attachment, currentUserId, uploadedById, updatedAttachments, result, updatedTask;
        return _regeneratorRuntime.async(function () {
          function _callee8$(_context8) {
            while (1) switch (_context8.prev = _context8.next) {
              case 0:
                _context8.prev = 0;
                if (this.userId) {
                  _context8.next = 3;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
              case 3:
                _context8.next = 5;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 5:
                task = _context8.sent;
                if (task) {
                  _context8.next = 8;
                  break;
                }
                throw new Meteor.Error('not-found', 'Task not found');
              case 8:
                if (!(!task.attachments || !task.attachments[attachmentIndex])) {
                  _context8.next = 10;
                  break;
                }
                throw new Meteor.Error('not-found', 'Attachment not found');
              case 10:
                attachment = task.attachments[attachmentIndex]; // Convert both IDs to strings for comparison
                currentUserId = String(this.userId);
                uploadedById = String(attachment.uploadedBy); // Only allow the uploader to remove the attachment
                if (!(currentUserId !== uploadedById)) {
                  _context8.next = 15;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');
              case 15:
                // Create a new array without the specified attachment
                updatedAttachments = _toConsumableArray(task.attachments);
                updatedAttachments.splice(attachmentIndex, 1);

                // Update the task with the new attachments array
                _context8.next = 19;
                return _regeneratorRuntime.awrap(Tasks.updateAsync({
                  _id: taskId
                }, {
                  $set: {
                    attachments: updatedAttachments,
                    updatedAt: new Date(),
                    updatedBy: currentUserId
                  }
                }));
              case 19:
                result = _context8.sent;
                if (!(result === 0)) {
                  _context8.next = 22;
                  break;
                }
                throw new Meteor.Error('update-failed', 'Failed to remove attachment');
              case 22:
                _context8.next = 24;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 24:
                updatedTask = _context8.sent;
                console.log('Task after removing attachment:', updatedTask);
                return _context8.abrupt("return", updatedTask);
              case 29:
                _context8.prev = 29;
                _context8.t0 = _context8["catch"](0);
                console.error('Error removing attachment:', _context8.t0);
                if (!(_context8.t0 instanceof Meteor.Error)) {
                  _context8.next = 34;
                  break;
                }
                throw _context8.t0;
              case 34:
                throw new Meteor.Error('server-error', _context8.t0.message || 'An unexpected error occurred');
              case 35:
              case "end":
                return _context8.stop();
            }
          }
          return _callee8$;
        }(), null, this, [[0, 29]], Promise);
      }
      return _callee8;
    }(),
    'tasks.removeLink': function () {
      function _callee9(taskId, linkIndex) {
        var task, link, currentUserId, addedById, updatedLinks, result, updatedTask;
        return _regeneratorRuntime.async(function () {
          function _callee9$(_context9) {
            while (1) switch (_context9.prev = _context9.next) {
              case 0:
                _context9.prev = 0;
                if (this.userId) {
                  _context9.next = 3;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');
              case 3:
                _context9.next = 5;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 5:
                task = _context9.sent;
                if (task) {
                  _context9.next = 8;
                  break;
                }
                throw new Meteor.Error('not-found', 'Task not found');
              case 8:
                if (!(!task.links || !task.links[linkIndex])) {
                  _context9.next = 10;
                  break;
                }
                throw new Meteor.Error('not-found', 'Link not found');
              case 10:
                link = task.links[linkIndex]; // Convert both IDs to strings for comparison
                currentUserId = String(this.userId);
                addedById = String(link.addedBy); // Only allow the user who added the link to remove it
                if (!(currentUserId !== addedById)) {
                  _context9.next = 15;
                  break;
                }
                throw new Meteor.Error('not-authorized', 'You can only remove your own links');
              case 15:
                // Create a new array without the specified link
                updatedLinks = _toConsumableArray(task.links);
                updatedLinks.splice(linkIndex, 1);

                // Update the task with the new links array
                _context9.next = 19;
                return _regeneratorRuntime.awrap(Tasks.updateAsync({
                  _id: taskId
                }, {
                  $set: {
                    links: updatedLinks,
                    updatedAt: new Date(),
                    updatedBy: currentUserId
                  }
                }));
              case 19:
                result = _context9.sent;
                if (!(result === 0)) {
                  _context9.next = 22;
                  break;
                }
                throw new Meteor.Error('update-failed', 'Failed to remove link');
              case 22:
                _context9.next = 24;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 24:
                updatedTask = _context9.sent;
                console.log('Task after removing link:', updatedTask);
                return _context9.abrupt("return", updatedTask);
              case 29:
                _context9.prev = 29;
                _context9.t0 = _context9["catch"](0);
                console.error('Error removing link:', _context9.t0);
                if (!(_context9.t0 instanceof Meteor.Error)) {
                  _context9.next = 34;
                  break;
                }
                throw _context9.t0;
              case 34:
                throw new Meteor.Error('server-error', _context9.t0.message || 'An unexpected error occurred');
              case 35:
              case "end":
                return _context9.stop();
            }
          }
          return _callee9$;
        }(), null, this, [[0, 29]], Promise);
      }
      return _callee9;
    }(),
    'tasks.findOne': function () {
      function _callee10(taskId) {
        var task;
        return _regeneratorRuntime.async(function () {
          function _callee10$(_context10) {
            while (1) switch (_context10.prev = _context10.next) {
              case 0:
                check(taskId, String);
                if (this.userId) {
                  _context10.next = 3;
                  break;
                }
                throw new Meteor.Error('Not authorized.');
              case 3:
                _context10.next = 5;
                return _regeneratorRuntime.awrap(Tasks.findOneAsync(taskId));
              case 5:
                task = _context10.sent;
                if (task) {
                  _context10.next = 8;
                  break;
                }
                throw new Meteor.Error('task-not-found', 'Task not found');
              case 8:
                return _context10.abrupt("return", task);
              case 9:
              case "end":
                return _context10.stop();
            }
          }
          return _callee10$;
        }(), null, this, null, Promise);
      }
      return _callee10;
    }()
  });
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"ui":{"components":{"AIInsightsPanel.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/AIInsightsPanel.jsx                                                                           //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 1);
  module1.export({
    AIInsightsPanel: function () {
      return AIInsightsPanel;
    }
  });
  var React, useState, useEffect;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    }
  }, 0);
  var Box, Card, CardContent, Typography, Tabs, Tab, CircularProgress, Alert, Divider, List, ListItem, ListItemText, Chip, IconButton, Tooltip, Button;
  module1.link("@mui/material", {
    Box: function (v) {
      Box = v;
    },
    Card: function (v) {
      Card = v;
    },
    CardContent: function (v) {
      CardContent = v;
    },
    Typography: function (v) {
      Typography = v;
    },
    Tabs: function (v) {
      Tabs = v;
    },
    Tab: function (v) {
      Tab = v;
    },
    CircularProgress: function (v) {
      CircularProgress = v;
    },
    Alert: function (v) {
      Alert = v;
    },
    Divider: function (v) {
      Divider = v;
    },
    List: function (v) {
      List = v;
    },
    ListItem: function (v) {
      ListItem = v;
    },
    ListItemText: function (v) {
      ListItemText = v;
    },
    Chip: function (v) {
      Chip = v;
    },
    IconButton: function (v) {
      IconButton = v;
    },
    Tooltip: function (v) {
      Tooltip = v;
    },
    Button: function (v) {
      Button = v;
    }
  }, 1);
  var AnalyticsIcon, AssignmentIcon, GroupIcon, TimelineIcon, RefreshIcon, SpeedIcon, LightbulbIcon, ErrorIcon;
  module1.link("@mui/icons-material", {
    Analytics: function (v) {
      AnalyticsIcon = v;
    },
    Assignment: function (v) {
      AssignmentIcon = v;
    },
    Group: function (v) {
      GroupIcon = v;
    },
    Timeline: function (v) {
      TimelineIcon = v;
    },
    Refresh: function (v) {
      RefreshIcon = v;
    },
    Speed: function (v) {
      SpeedIcon = v;
    },
    Lightbulb: function (v) {
      LightbulbIcon = v;
    },
    Error: function (v) {
      ErrorIcon = v;
    }
  }, 2);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 3);
  var OllamaService;
  module1.link("../../api/ai/ollamaService", {
    OllamaService: function (v) {
      OllamaService = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TabPanel = function (_ref) {
    var children = _ref.children,
      value = _ref.value,
      index = _ref.index;
    return /*#__PURE__*/React.createElement("div", {
      hidden: value !== index,
      style: {
        padding: '16px'
      }
    }, value === index && children);
  };
  _c = TabPanel;
  var AIInsightsPanel = function (_ref2) {
    var taskId = _ref2.taskId,
      propTask = _ref2.task;
    _s();
    var _useState = useState(false),
      _useState2 = _slicedToArray(_useState, 2),
      loading = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = useState(null),
      _useState4 = _slicedToArray(_useState3, 2),
      error = _useState4[0],
      setError = _useState4[1];
    var _useState5 = useState(null),
      _useState6 = _slicedToArray(_useState5, 2),
      insights = _useState6[0],
      setInsights = _useState6[1];
    var _useState7 = useState(0),
      _useState8 = _slicedToArray(_useState7, 2),
      selectedTab = _useState8[0],
      setSelectedTab = _useState8[1];
    var _useState9 = useState(0),
      _useState10 = _slicedToArray(_useState9, 2),
      retryCount = _useState10[0],
      setRetryCount = _useState10[1];
    var fetchInsights = function () {
      function _callee() {
        var result, taskObj;
        return _regeneratorRuntime.async(function () {
          function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                setLoading(true);
                setError(null);
                _context.prev = 2;
                taskObj = propTask;
                if (!(!taskObj || !taskObj.title)) {
                  _context.next = 8;
                  break;
                }
                setError('Task data is missing or incomplete.');
                setLoading(false);
                return _context.abrupt("return");
              case 8:
                _context.t0 = selectedTab;
                _context.next = _context.t0 === 0 ? 11 : _context.t0 === 1 ? 15 : _context.t0 === 2 ? 19 : _context.t0 === 3 ? 23 : _context.t0 === 4 ? 27 : 31;
                break;
              case 11:
                _context.next = 13;
                return _regeneratorRuntime.awrap(OllamaService.analyzeTask(taskObj));
              case 13:
                result = _context.sent;
                return _context.abrupt("break", 34);
              case 15:
                _context.next = 17;
                return _regeneratorRuntime.awrap(OllamaService.suggestTaskOptimization(taskObj._id));
              case 17:
                result = _context.sent;
                return _context.abrupt("break", 34);
              case 19:
                _context.next = 21;
                return _regeneratorRuntime.awrap(OllamaService.predictTaskCompletion(taskObj._id));
              case 21:
                result = _context.sent;
                return _context.abrupt("break", 34);
              case 23:
                _context.next = 25;
                return _regeneratorRuntime.awrap(OllamaService.generateTaskSummary(taskObj._id));
              case 25:
                result = _context.sent;
                return _context.abrupt("break", 34);
              case 27:
                _context.next = 29;
                return _regeneratorRuntime.awrap(OllamaService.recommendTaskAssignments(taskObj._id));
              case 29:
                result = _context.sent;
                return _context.abrupt("break", 34);
              case 31:
                _context.next = 33;
                return _regeneratorRuntime.awrap(OllamaService.analyzeTask(taskObj));
              case 33:
                result = _context.sent;
              case 34:
                setInsights(result);
                setRetryCount(0); // Reset retry count on success
                _context.next = 41;
                break;
              case 38:
                _context.prev = 38;
                _context.t1 = _context["catch"](2);
                if (_context.t1.error === 'connection-failed') {
                  setError('Could not connect to Ollama. Please make sure Ollama is running on your machine.');
                } else {
                  setError(_context.t1.message || 'Failed to fetch insights');
                }
              case 41:
                _context.prev = 41;
                setLoading(false);
                return _context.finish(41);
              case 44:
              case "end":
                return _context.stop();
            }
          }
          return _callee$;
        }(), null, null, [[2, 38, 41, 44]], Promise);
      }
      return _callee;
    }();
    useEffect(function () {
      fetchInsights();
    }, [taskId, selectedTab]);
    var handleTabChange = function (event, newValue) {
      setSelectedTab(newValue);
    };
    var handleRefresh = function () {
      setRetryCount(function (prev) {
        return prev + 1;
      });
      fetchInsights();
    };
    var renderInsightContent = function (content) {
      if (!content) return null;

      // Split content into sections based on numbered points
      var sections = content.split(/\d\./).filter(Boolean);
      return /*#__PURE__*/React.createElement(List, null, sections.map(function (section, index) {
        return /*#__PURE__*/React.createElement(ListItem, {
          key: index,
          alignItems: "flex-start"
        }, /*#__PURE__*/React.createElement(ListItemText, {
          primary: /*#__PURE__*/React.createElement(Typography, {
            variant: "subtitle1",
            color: "primary"
          }, section.split('\n')[0].trim()),
          secondary: /*#__PURE__*/React.createElement(Typography, {
            component: "span",
            variant: "body2",
            color: "text.primary",
            style: {
              whiteSpace: 'pre-line'
            }
          }, section.split('\n').slice(1).join('\n').trim())
        }));
      }));
    };
    var renderErrorContent = function () {
      if (!error) return null;
      return /*#__PURE__*/React.createElement(Box, {
        sx: {
          p: 2,
          textAlign: 'center'
        }
      }, /*#__PURE__*/React.createElement(ErrorIcon, {
        color: "error",
        sx: {
          fontSize: 48,
          mb: 2
        }
      }), /*#__PURE__*/React.createElement(Typography, {
        variant: "h6",
        color: "error",
        gutterBottom: true
      }, error), retryCount < 3 && /*#__PURE__*/React.createElement(Button, {
        variant: "contained",
        color: "primary",
        onClick: handleRefresh,
        startIcon: /*#__PURE__*/React.createElement(RefreshIcon, null),
        sx: {
          mt: 2
        }
      }, "Try Again"), retryCount >= 3 && /*#__PURE__*/React.createElement(Typography, {
        variant: "body2",
        color: "text.secondary",
        sx: {
          mt: 2
        }
      }, "Multiple attempts failed. Please try again later."));
    };
    return /*#__PURE__*/React.createElement(Card, {
      sx: {
        maxWidth: 800,
        margin: '20px auto',
        p: 2,
        height: '100%'
      }
    }, /*#__PURE__*/React.createElement(CardContent, null, /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      component: "h2"
    }, "AI Insights"), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Refresh Insights"
    }, /*#__PURE__*/React.createElement(IconButton, {
      onClick: handleRefresh,
      disabled: loading
    }, /*#__PURE__*/React.createElement(RefreshIcon, null)))), /*#__PURE__*/React.createElement(Tabs, {
      value: selectedTab,
      onChange: handleTabChange,
      variant: "fullWidth",
      sx: {
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Tooltip, {
      title: "Analysis"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(AnalyticsIcon, null),
      "aria-label": "Analysis"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Optimization"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(SpeedIcon, null),
      "aria-label": "Optimization"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Prediction"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(TimelineIcon, null),
      "aria-label": "Prediction"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Recommendations"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(GroupIcon, null),
      "aria-label": "Recommendations"
    }))), loading && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(CircularProgress, null)), error && renderErrorContent(), !loading && !error && insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        p: 1
      }
    }, renderInsightContent(insights)), !loading && !error && !insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(LightbulbIcon, {
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Typography, null, "No insights available"))));
  };
  _s(AIInsightsPanel, "Act2AOnU28qM7wyZXfGrXz8QXTY=");
  _c2 = AIInsightsPanel;
  var _c, _c2;
  $RefreshReg$(_c, "TabPanel");
  $RefreshReg$(_c2, "AIInsightsPanel");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"CreateTask.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/CreateTask.jsx                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _toConsumableArray;
  module1.link("@babel/runtime/helpers/toConsumableArray", {
    default: function (v) {
      _toConsumableArray = v;
    }
  }, 1);
  var _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default: function (v) {
      _objectSpread = v;
    }
  }, 2);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 3);
  module1.export({
    CreateTask: function () {
      return CreateTask;
    }
  });
  var React, useState, useEffect;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 2);
  var Formik, Form, Field;
  module1.link("formik", {
    Formik: function (v) {
      Formik = v;
    },
    Form: function (v) {
      Form = v;
    },
    Field: function (v) {
      Field = v;
    }
  }, 3);
  var Yup;
  module1.link("yup", {
    "*": function (v) {
      Yup = v;
    }
  }, 4);
  var taskCategories, taskLabels;
  module1.link("/imports/api/tasks", {
    taskCategories: function (v) {
      taskCategories = v;
    },
    taskLabels: function (v) {
      taskLabels = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TaskSchema = Yup.object().shape({
    title: Yup.string().min(3, 'Title must be at least 3 characters').required('Title is required'),
    description: Yup.string().min(10, 'Description must be at least 10 characters').required('Description is required'),
    startDate: Yup.date().required('Start date is required'),
    dueDate: Yup.date().min(Yup.ref('startDate'), 'Due date must be after start date').required('Due date is required'),
    priority: Yup.string().oneOf(['high', 'medium', 'low'], 'Invalid priority').required('Priority is required'),
    assignedTo: Yup.array().min(1, 'Assign at least one team member').required('Assigned members are required'),
    checklist: Yup.array().of(Yup.object().shape({
      text: Yup.string().required('Checklist item cannot be empty'),
      completed: Yup.boolean()
    }))
  });
  var CreateTask = function (_ref) {
    var onCancel = _ref.onCancel;
    _s();
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      submitError = _useState2[0],
      setSubmitError = _useState2[1];

    // Subscribe to team members data
    var _useTracker = useTracker(function () {
        var subscriptions = {
          userData: Meteor.subscribe('userData'),
          teamMembers: Meteor.subscribe('teamMembers')
        };
        var user = Meteor.user();
        if (!user) {
          return {
            isLoading: false,
            error: 'Not logged in',
            currentUser: null,
            teamMembers: []
          };
        }
        if (!subscriptions.userData.ready() || !subscriptions.teamMembers.ready()) {
          return {
            isLoading: true,
            currentUser: user,
            teamMembers: [],
            error: null
          };
        }
        try {
          var members = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1
            },
            sort: {
              'profile.firstName': 1,
              'profile.lastName': 1
            }
          }).fetch();
          return {
            teamMembers: members,
            currentUser: user,
            isLoading: false,
            error: null
          };
        } catch (err) {
          console.error('Error fetching team members:', err);
          return {
            isLoading: false,
            error: 'Error loading team members',
            currentUser: user,
            teamMembers: []
          };
        }
      }, []),
      teamMembers = _useTracker.teamMembers,
      isLoading = _useTracker.isLoading,
      currentUser = _useTracker.currentUser,
      error = _useTracker.error;
    var handleSubmit = function () {
      function _callee(values, _ref2) {
        var setSubmitting, taskToCreate, result;
        return _regeneratorRuntime.async(function () {
          function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                setSubmitting = _ref2.setSubmitting;
                _context.prev = 1;
                console.log('Creating task with values:', values);
                taskToCreate = _objectSpread(_objectSpread({}, values), {}, {
                  startDate: new Date(values.startDate),
                  dueDate: new Date(values.dueDate),
                  checklist: values.checklist.filter(function (item) {
                    return item.text.trim() !== '';
                  }),
                  progress: 0,
                  status: 'pending',
                  category: values.category || '',
                  labels: values.labels || [],
                  assignedTo: values.assignedTo || []
                });
                console.log('Sending task creation to server:', taskToCreate);
                _context.next = 7;
                return _regeneratorRuntime.awrap(new Promise(function (resolve, reject) {
                  Meteor.call('tasks.insert', taskToCreate, function (error, result) {
                    if (error) {
                      console.error('Error creating task:', error);
                      reject(error);
                    } else {
                      console.log('Task created successfully:', result);
                      resolve(result);
                    }
                  });
                }));
              case 7:
                result = _context.sent;
                console.log('Task creation result:', result);
                onCancel();
                _context.next = 16;
                break;
              case 12:
                _context.prev = 12;
                _context.t0 = _context["catch"](1);
                console.error('Error creating task:', _context.t0);
                setSubmitError(_context.t0.message || 'Failed to create task');
              case 16:
                _context.prev = 16;
                setSubmitting(false);
                return _context.finish(16);
              case 19:
              case "end":
                return _context.stop();
            }
          }
          return _callee$;
        }(), null, null, [[1, 12, 16, 19]], Promise);
      }
      return _callee;
    }();
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Create New Task"), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: onCancel,
      className: "btn btn-secondary",
      style: {
        padding: '8px'
      }
    }, /*#__PURE__*/React.createElement("svg", {
      className: "w-5 h-5",
      fill: "none",
      stroke: "currentColor",
      viewBox: "0 0 24 24"
    }, /*#__PURE__*/React.createElement("path", {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2,
      d: "M6 18L18 6M6 6l12 12"
    })))), submitError && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#fee2e2',
        color: '#dc2626',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, submitError), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        title: '',
        description: '',
        startDate: new Date().toISOString().split('T')[0],
        dueDate: new Date().toISOString().split('T')[0],
        priority: 'medium',
        status: 'pending',
        assignedTo: [],
        checklist: [],
        category: '',
        labels: [],
        progress: 0
      },
      validationSchema: TaskSchema,
      onSubmit: handleSubmit
    }, function (_ref3) {
      var values = _ref3.values,
        errors = _ref3.errors,
        touched = _ref3.touched,
        setFieldValue = _ref3.setFieldValue,
        isSubmitting = _ref3.isSubmitting;
      return /*#__PURE__*/React.createElement(Form, {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "title",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Task Title"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "title",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task title"
      }), errors.title && touched.title && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.title)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "description",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Description"), /*#__PURE__*/React.createElement(Field, {
        as: "textarea",
        name: "description",
        rows: 4,
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task description"
      }), errors.description && touched.description && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.description)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "startDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Start Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "startDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.startDate && touched.startDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.startDate)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "dueDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Due Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "dueDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.dueDate && touched.dueDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.dueDate))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "priority",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Priority"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "priority",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }, /*#__PURE__*/React.createElement("option", {
        value: "high"
      }, "High"), /*#__PURE__*/React.createElement("option", {
        value: "medium"
      }, "Medium"), /*#__PURE__*/React.createElement("option", {
        value: "low"
      }, "Low")), errors.priority && touched.priority && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.priority)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Checklist Items"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, values.checklist.map(function (item, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          style: {
            display: 'flex',
            gap: '8px',
            alignItems: 'flex-start'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: item.completed,
          onChange: function (e) {
            var newChecklist = _toConsumableArray(values.checklist);
            newChecklist[index].completed = e.target.checked;
            setFieldValue('checklist', newChecklist);
          },
          style: {
            width: '16px',
            height: '16px',
            marginTop: '4px'
          }
        }), /*#__PURE__*/React.createElement(Field, {
          type: "text",
          name: "checklist." + index + ".text",
          placeholder: "Enter checklist item",
          style: {
            flex: 1,
            padding: '8px 12px',
            borderRadius: '6px',
            border: '1px solid #e2e8f0'
          }
        }), /*#__PURE__*/React.createElement("button", {
          type: "button",
          onClick: function () {
            var newChecklist = values.checklist.filter(function (_, i) {
              return i !== index;
            });
            setFieldValue('checklist', newChecklist);
          },
          style: {
            padding: '8px',
            color: '#dc2626',
            background: '#fee2e2',
            border: '1px solid #fecaca',
            borderRadius: '4px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minWidth: '32px',
            height: '32px'
          },
          title: "Delete item"
        }, /*#__PURE__*/React.createElement("svg", {
          className: "w-5 h-5",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24"
        }, /*#__PURE__*/React.createElement("path", {
          strokeLinecap: "round",
          strokeLinejoin: "round",
          strokeWidth: 2,
          d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
        }))));
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: function () {
          setFieldValue('checklist', [].concat(_toConsumableArray(values.checklist), [{
            text: '',
            completed: false
          }]));
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          borderRadius: '6px',
          border: '1px solid #e2e8f0',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          width: 'fit-content'
        }
      }, /*#__PURE__*/React.createElement("svg", {
        className: "w-5 h-5",
        fill: "none",
        stroke: "currentColor",
        viewBox: "0 0 24 24"
      }, /*#__PURE__*/React.createElement("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M12 4v16m8-8H4"
      })), "Add Checklist Item"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Assign Team Members"), error ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem'
        }
      }, error) : isLoading ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "Loading team members...") : (teamMembers === null || teamMembers === void 0 ? void 0 : teamMembers.length) === 0 ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "No team members available") : /*#__PURE__*/React.createElement("div", {
        style: {
          maxHeight: '200px',
          overflowY: 'auto',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          padding: '8px'
        }
      }, teamMembers.map(function (member) {
        var _values$assignedTo, _values$assignedTo2;
        return /*#__PURE__*/React.createElement("div", {
          key: member._id,
          onClick: function () {
            var currentAssigned = values.assignedTo || [];
            var newAssigned = currentAssigned.includes(member._id) ? currentAssigned.filter(function (id) {
              return id !== member._id;
            }) : [].concat(_toConsumableArray(currentAssigned), [member._id]);
            setFieldValue('assignedTo', newAssigned);
          },
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '4px',
            backgroundColor: (_values$assignedTo = values.assignedTo) !== null && _values$assignedTo !== void 0 && _values$assignedTo.includes(member._id) ? '#f1f5f9' : 'transparent'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: (_values$assignedTo2 = values.assignedTo) === null || _values$assignedTo2 === void 0 ? void 0 : _values$assignedTo2.includes(member._id),
          onChange: function () {},
          style: {
            width: '16px',
            height: '16px',
            borderRadius: '4px',
            border: '1px solid #e2e8f0'
          }
        }), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.875rem',
            fontWeight: '500'
          }
        }, member.profile.firstName, " ", member.profile.lastName), /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.75rem',
            color: '#64748b'
          }
        }, member.emails[0].address)));
      })), errors.assignedTo && touched.assignedTo && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.assignedTo)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: onCancel,
        style: {
          padding: '8px 16px',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          cursor: 'pointer'
        }
      }, "Cancel"), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        disabled: isSubmitting,
        style: {
          padding: '8px 16px',
          backgroundColor: '#16a34a',
          color: '#ffffff',
          borderRadius: '6px',
          cursor: isSubmitting ? 'not-allowed' : 'pointer',
          opacity: isSubmitting ? 0.5 : 1
        }
      }, isSubmitting ? 'Creating...' : 'Create Task')));
    })));
  };
  _s(CreateTask, "UUPI7BARsPMCqciM3wKOjHElPdA=", false, function () {
    return [useTracker];
  });
  _c = CreateTask;
  var _c;
  $RefreshReg$(_c, "CreateTask");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"EditTask.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/EditTask.jsx                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _toConsumableArray;
  module1.link("@babel/runtime/helpers/toConsumableArray", {
    default: function (v) {
      _toConsumableArray = v;
    }
  }, 1);
  var _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default: function (v) {
      _objectSpread = v;
    }
  }, 2);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 3);
  module1.export({
    EditTask: function () {
      return EditTask;
    }
  });
  var React, useState;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 2);
  var Formik, Form, Field;
  module1.link("formik", {
    Formik: function (v) {
      Formik = v;
    },
    Form: function (v) {
      Form = v;
    },
    Field: function (v) {
      Field = v;
    }
  }, 3);
  var Yup;
  module1.link("yup", {
    "*": function (v) {
      Yup = v;
    }
  }, 4);
  var taskCategories, taskLabels;
  module1.link("/imports/api/tasks", {
    taskCategories: function (v) {
      taskCategories = v;
    },
    taskLabels: function (v) {
      taskLabels = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TaskSchema = Yup.object().shape({
    title: Yup.string().min(3, 'Title must be at least 3 characters').required('Title is required'),
    description: Yup.string().min(10, 'Description must be at least 10 characters').required('Description is required'),
    startDate: Yup.date().required('Start date is required'),
    dueDate: Yup.date().min(Yup.ref('startDate'), 'Due date must be after start date').required('Due date is required'),
    priority: Yup.string().oneOf(['high', 'medium', 'low'], 'Invalid priority').required('Priority is required'),
    assignedTo: Yup.array().min(1, 'Assign at least one team member').required('Assigned members are required'),
    checklist: Yup.array().of(Yup.object().shape({
      text: Yup.string().required('Checklist item cannot be empty'),
      completed: Yup.boolean()
    }))
  });
  var EditTask = function (_ref) {
    var task = _ref.task,
      onCancel = _ref.onCancel,
      onUpdate = _ref.onUpdate;
    _s();
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      submitError = _useState2[0],
      setSubmitError = _useState2[1];

    // Subscribe to team members data
    var _useTracker = useTracker(function () {
        var subscriptions = {
          userData: Meteor.subscribe('userData'),
          teamMembers: Meteor.subscribe('teamMembers')
        };
        var user = Meteor.user();
        if (!user) {
          return {
            isLoading: false,
            error: 'Not logged in',
            currentUser: null,
            teamMembers: []
          };
        }
        if (!subscriptions.userData.ready() || !subscriptions.teamMembers.ready()) {
          return {
            isLoading: true,
            currentUser: user,
            teamMembers: [],
            error: null
          };
        }
        try {
          var members = Meteor.users.find({
            $or: [{
              'roles': 'team-member'
            }, {
              'profile.role': 'team-member'
            }]
          }, {
            fields: {
              emails: 1,
              roles: 1,
              'profile.firstName': 1,
              'profile.lastName': 1,
              'profile.fullName': 1
            },
            sort: {
              'profile.firstName': 1,
              'profile.lastName': 1
            }
          }).fetch();
          return {
            teamMembers: members,
            currentUser: user,
            isLoading: false,
            error: null
          };
        } catch (err) {
          console.error('Error fetching team members:', err);
          return {
            isLoading: false,
            error: 'Error loading team members',
            currentUser: user,
            teamMembers: []
          };
        }
      }, []),
      teamMembers = _useTracker.teamMembers,
      isLoading = _useTracker.isLoading,
      currentUser = _useTracker.currentUser,
      error = _useTracker.error;
    var handleSubmit = function () {
      function _callee(values, _ref2) {
        var setSubmitting, formattedValues, result;
        return _regeneratorRuntime.async(function () {
          function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                setSubmitting = _ref2.setSubmitting;
                _context.prev = 1;
                console.log('Form submitted with values:', values); // Debug log

                // Format dates to ISO string
                formattedValues = _objectSpread(_objectSpread({}, values), {}, {
                  startDate: new Date(values.startDate),
                  dueDate: new Date(values.dueDate),
                  // Ensure all required fields are present
                  title: values.title.trim(),
                  description: values.description.trim(),
                  assignedTo: values.assignedTo || [],
                  checklist: values.checklist || []
                });
                console.log('Updating task with values:', formattedValues); // Debug log
                _context.next = 7;
                return _regeneratorRuntime.awrap(new Promise(function (resolve, reject) {
                  Meteor.call('tasks.update', task._id, formattedValues, function (error, result) {
                    if (error) {
                      console.error('Error updating task:', error);
                      reject(error);
                    } else {
                      console.log('Task updated successfully:', result);
                      resolve(result);
                    }
                  });
                }));
              case 7:
                result = _context.sent;
                console.log('Task update result:', result);
                onCancel(); // Call onCancel after successful update
                _context.next = 16;
                break;
              case 12:
                _context.prev = 12;
                _context.t0 = _context["catch"](1);
                console.error('Error updating task:', _context.t0);
                setSubmitError(_context.t0.message || 'Failed to update task');
              case 16:
                _context.prev = 16;
                setSubmitting(false);
                return _context.finish(16);
              case 19:
              case "end":
                return _context.stop();
            }
          }
          return _callee$;
        }(), null, null, [[1, 12, 16, 19]], Promise);
      }
      return _callee;
    }();
    if (!task) {
      return /*#__PURE__*/React.createElement("div", null, "Loading task data...");
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Edit Task"), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: onCancel,
      className: "btn btn-secondary",
      style: {
        padding: '8px'
      }
    }, /*#__PURE__*/React.createElement("svg", {
      className: "w-5 h-5",
      fill: "none",
      stroke: "currentColor",
      viewBox: "0 0 24 24"
    }, /*#__PURE__*/React.createElement("path", {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2,
      d: "M6 18L18 6M6 6l12 12"
    })))), submitError && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#fee2e2',
        color: '#dc2626',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, submitError), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        title: task.title || '',
        description: task.description || '',
        startDate: task.startDate ? new Date(task.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        priority: task.priority || 'medium',
        assignedTo: task.assignedTo || [],
        checklist: task.checklist || []
      },
      validationSchema: TaskSchema,
      onSubmit: handleSubmit,
      enableReinitialize: true
    }, function (_ref3) {
      var values = _ref3.values,
        errors = _ref3.errors,
        touched = _ref3.touched,
        setFieldValue = _ref3.setFieldValue,
        isSubmitting = _ref3.isSubmitting;
      return /*#__PURE__*/React.createElement(Form, {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "title",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Task Title"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "title",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task title"
      }), errors.title && touched.title && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.title)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "description",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Description"), /*#__PURE__*/React.createElement(Field, {
        as: "textarea",
        name: "description",
        rows: 4,
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        },
        placeholder: "Enter task description"
      }), errors.description && touched.description && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.description)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "startDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Start Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "startDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.startDate && touched.startDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.startDate)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "dueDate",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Due Date"), /*#__PURE__*/React.createElement(Field, {
        type: "date",
        name: "dueDate",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }), errors.dueDate && touched.dueDate && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.dueDate))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "priority",
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Priority"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "priority",
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #e2e8f0'
        }
      }, /*#__PURE__*/React.createElement("option", {
        value: "high"
      }, "High"), /*#__PURE__*/React.createElement("option", {
        value: "medium"
      }, "Medium"), /*#__PURE__*/React.createElement("option", {
        value: "low"
      }, "Low")), errors.priority && touched.priority && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.priority)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Checklist Items"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, values.checklist.map(function (item, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          style: {
            display: 'flex',
            gap: '8px',
            alignItems: 'flex-start'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: item.completed,
          onChange: function (e) {
            var newChecklist = _toConsumableArray(values.checklist);
            newChecklist[index].completed = e.target.checked;
            setFieldValue('checklist', newChecklist);
          },
          style: {
            width: '16px',
            height: '16px',
            marginTop: '4px'
          }
        }), /*#__PURE__*/React.createElement(Field, {
          type: "text",
          name: "checklist." + index + ".text",
          placeholder: "Enter checklist item",
          style: {
            flex: 1,
            padding: '8px 12px',
            borderRadius: '6px',
            border: '1px solid #e2e8f0'
          }
        }), /*#__PURE__*/React.createElement("button", {
          type: "button",
          onClick: function () {
            var newChecklist = values.checklist.filter(function (_, i) {
              return i !== index;
            });
            setFieldValue('checklist', newChecklist);
          },
          style: {
            padding: '8px',
            color: '#dc2626',
            background: '#fee2e2',
            border: '1px solid #fecaca',
            borderRadius: '4px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minWidth: '32px',
            height: '32px'
          },
          title: "Delete item"
        }, /*#__PURE__*/React.createElement("svg", {
          className: "w-5 h-5",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24"
        }, /*#__PURE__*/React.createElement("path", {
          strokeLinecap: "round",
          strokeLinejoin: "round",
          strokeWidth: 2,
          d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
        }))));
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: function () {
          setFieldValue('checklist', [].concat(_toConsumableArray(values.checklist), [{
            text: '',
            completed: false
          }]));
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          borderRadius: '6px',
          border: '1px solid #e2e8f0',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          width: 'fit-content'
        }
      }, /*#__PURE__*/React.createElement("svg", {
        className: "w-5 h-5",
        fill: "none",
        stroke: "currentColor",
        viewBox: "0 0 24 24"
      }, /*#__PURE__*/React.createElement("path", {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2,
        d: "M12 4v16m8-8H4"
      })), "Add Checklist Item"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        style: {
          display: 'block',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Assign Team Members"), error ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem'
        }
      }, error) : isLoading ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "Loading team members...") : (teamMembers === null || teamMembers === void 0 ? void 0 : teamMembers.length) === 0 ? /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem'
        }
      }, "No team members available") : /*#__PURE__*/React.createElement("div", {
        style: {
          maxHeight: '200px',
          overflowY: 'auto',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          padding: '8px'
        }
      }, teamMembers.map(function (member) {
        var _values$assignedTo, _values$assignedTo2;
        return /*#__PURE__*/React.createElement("div", {
          key: member._id,
          onClick: function () {
            var currentAssigned = values.assignedTo || [];
            var newAssigned = currentAssigned.includes(member._id) ? currentAssigned.filter(function (id) {
              return id !== member._id;
            }) : [].concat(_toConsumableArray(currentAssigned), [member._id]);
            setFieldValue('assignedTo', newAssigned);
          },
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '4px',
            backgroundColor: (_values$assignedTo = values.assignedTo) !== null && _values$assignedTo !== void 0 && _values$assignedTo.includes(member._id) ? '#f1f5f9' : 'transparent'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: (_values$assignedTo2 = values.assignedTo) === null || _values$assignedTo2 === void 0 ? void 0 : _values$assignedTo2.includes(member._id),
          onChange: function () {},
          style: {
            width: '16px',
            height: '16px',
            borderRadius: '4px',
            border: '1px solid #e2e8f0'
          }
        }), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.875rem',
            fontWeight: '500'
          }
        }, member.profile.firstName, " ", member.profile.lastName), /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.75rem',
            color: '#64748b'
          }
        }, member.emails[0].address)));
      })), errors.assignedTo && touched.assignedTo && /*#__PURE__*/React.createElement("div", {
        style: {
          color: '#dc2626',
          fontSize: '0.875rem',
          marginTop: '4px'
        }
      }, errors.assignedTo)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: onCancel,
        style: {
          padding: '8px 16px',
          border: '1px solid #e2e8f0',
          borderRadius: '6px',
          cursor: 'pointer'
        }
      }, "Cancel"), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        disabled: isSubmitting,
        style: {
          padding: '8px 16px',
          backgroundColor: '#16a34a',
          color: '#ffffff',
          borderRadius: '6px',
          cursor: isSubmitting ? 'not-allowed' : 'pointer',
          opacity: isSubmitting ? 0.5 : 1
        }
      }, isSubmitting ? 'Saving...' : 'Save Changes')));
    })));
  };
  _s(EditTask, "UUPI7BARsPMCqciM3wKOjHElPdA=", false, function () {
    return [useTracker];
  });
  _c = EditTask;
  var _c;
  $RefreshReg$(_c, "EditTask");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"Header.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/Header.jsx                                                                                    //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    Header: function () {
      return Header;
    }
  });
  var React;
  module1.link("react", {
    "default": function (v) {
      React = v;
    }
  }, 0);
  var Link, useNavigate;
  module1.link("react-router-dom", {
    Link: function (v) {
      Link = v;
    },
    useNavigate: function (v) {
      useNavigate = v;
    }
  }, 1);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 2);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var Header = function () {
    var _user$profile, _user$roles;
    _s();
    var navigate = useNavigate();
    var _useTracker = useTracker(function () {
        var subscription = Meteor.subscribe('userData');
        return {
          user: Meteor.user(),
          isLoading: !subscription.ready()
        };
      }, []),
      user = _useTracker.user;
    var userRole = (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);
    var handleLogout = function () {
      Meteor.logout(function (err) {
        if (err) {
          console.error('Logout error:', err);
        } else {
          navigate('/login', {
            replace: true
          });
        }
      });
    };
    return /*#__PURE__*/React.createElement("nav", {
      style: {
        backgroundColor: '#ffffff',
        borderBottom: '1px solid #e2e8f0',
        padding: '16px 24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '24px'
      }
    }, /*#__PURE__*/React.createElement(Link, {
      to: userRole === 'admin' ? "/admin-dashboard" : "/team-dashboard",
      style: {
        color: '#16a34a',
        textDecoration: 'none',
        fontSize: '1.25rem',
        fontWeight: '600'
      }
    }, "Task Management"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px'
      }
    }, /*#__PURE__*/React.createElement(Link, {
      to: userRole === 'admin' ? "/admin-dashboard/tasks" : "/team-dashboard/tasks",
      style: {
        color: '#475569',
        textDecoration: 'none',
        fontWeight: '500',
        ':hover': {
          color: '#16a34a'
        }
      }
    }, "Tasks"), /*#__PURE__*/React.createElement(Link, {
      to: userRole === 'admin' ? "/admin-dashboard/team" : "/team-dashboard/team",
      style: {
        color: '#475569',
        textDecoration: 'none',
        fontWeight: '500',
        ':hover': {
          color: '#16a34a'
        }
      }
    }, "Team"))), /*#__PURE__*/React.createElement("button", {
      onClick: handleLogout,
      className: "btn btn-secondary",
      style: {
        padding: '8px 16px'
      }
    }, "Logout"));
  };
  _s(Header, "R4j7jVEJP72bhi/6bIckCScI5HY=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = Header;
  var _c;
  $RefreshReg$(_c, "Header");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TaskAssignmentHistory.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TaskAssignmentHistory.jsx                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TaskAssignmentHistory: function () {
      return TaskAssignmentHistory;
    }
  });
  var React;
  module1.link("react", {
    "default": function (v) {
      React = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 2);
  var Tasks;
  module1.link("/imports/api/tasks", {
    Tasks: function (v) {
      Tasks = v;
    }
  }, 3);
  var useNavigate;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TaskAssignmentHistory = function (_ref) {
    var memberId = _ref.memberId;
    _s();
    var navigate = useNavigate();
    var _useTracker = useTracker(function () {
        var handle = Meteor.subscribe('tasks');
        var isLoading = !handle.ready();
        if (isLoading) {
          return {
            tasks: [],
            isLoading: isLoading
          };
        }
        var tasks = Tasks.find({
          assignedTo: memberId
        }, {
          sort: {
            createdAt: -1
          }
        }).fetch();
        return {
          tasks: tasks,
          isLoading: isLoading
        };
      }, [memberId]),
      tasks = _useTracker.tasks,
      isLoading = _useTracker.isLoading;
    var handleTaskClick = function (taskId) {
      navigate("/admin-dashboard/tasks/" + taskId, {
        state: {
          from: "/admin-dashboard/team/" + memberId,
          memberId: memberId
        }
      });
    };
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        className: "animate-pulse"
      }, /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-3/4 mb-4"
      }), /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-1/2"
      }));
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
        overflow: 'hidden'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '20px',
        borderBottom: '1px solid #e2e8f0'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1rem',
        color: '#0f172a',
        fontWeight: '600'
      }
    }, "Task Assignment History")), /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, tasks.length > 0 ? tasks.map(function (task) {
      return /*#__PURE__*/React.createElement("div", {
        key: task._id,
        style: {
          padding: '12px',
          backgroundColor: '#f8fafc',
          borderRadius: '6px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          transition: 'all 0.2s ease'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, task.title), /*#__PURE__*/React.createElement("button", {
        onClick: function (e) {
          e.stopPropagation();
          handleTaskClick(task._id);
        },
        className: "ml-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors border border-gray-200 bg-white shadow-sm",
        title: "View task details",
        style: {
          minWidth: '32px',
          minHeight: '32px'
        }
      }, /*#__PURE__*/React.createElement("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: "h-4 w-4",
        viewBox: "0 0 20 20",
        fill: "currentColor"
      }, /*#__PURE__*/React.createElement("path", {
        fillRule: "evenodd",
        d: "M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",
        clipRule: "evenodd"
      }))));
    }) : /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '12px',
        textAlign: 'center',
        color: '#64748b',
        fontSize: '0.875rem'
      }
    }, "No tasks assigned yet")));
  };
  _s(TaskAssignmentHistory, "MWwWFuPhxvHCHylbFRlLYyeQiNw=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = TaskAssignmentHistory;
  var _c;
  $RefreshReg$(_c, "TaskAssignmentHistory");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamAIInsightsPanel.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamAIInsightsPanel.jsx                                                                       //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 1);
  module1.export({
    TeamAIInsightsPanel: function () {
      return TeamAIInsightsPanel;
    }
  });
  var React, useState, useEffect;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    }
  }, 0);
  var Box, Card, CardContent, Typography, Tabs, Tab, CircularProgress, Alert, Divider, List, ListItem, ListItemText, Chip, IconButton, Tooltip, Button;
  module1.link("@mui/material", {
    Box: function (v) {
      Box = v;
    },
    Card: function (v) {
      Card = v;
    },
    CardContent: function (v) {
      CardContent = v;
    },
    Typography: function (v) {
      Typography = v;
    },
    Tabs: function (v) {
      Tabs = v;
    },
    Tab: function (v) {
      Tab = v;
    },
    CircularProgress: function (v) {
      CircularProgress = v;
    },
    Alert: function (v) {
      Alert = v;
    },
    Divider: function (v) {
      Divider = v;
    },
    List: function (v) {
      List = v;
    },
    ListItem: function (v) {
      ListItem = v;
    },
    ListItemText: function (v) {
      ListItemText = v;
    },
    Chip: function (v) {
      Chip = v;
    },
    IconButton: function (v) {
      IconButton = v;
    },
    Tooltip: function (v) {
      Tooltip = v;
    },
    Button: function (v) {
      Button = v;
    }
  }, 1);
  var GroupIcon, BuildIcon, TimelineIcon, RefreshIcon, ErrorIcon, LightbulbIcon;
  module1.link("@mui/icons-material", {
    Group: function (v) {
      GroupIcon = v;
    },
    Build: function (v) {
      BuildIcon = v;
    },
    Timeline: function (v) {
      TimelineIcon = v;
    },
    Refresh: function (v) {
      RefreshIcon = v;
    },
    Error: function (v) {
      ErrorIcon = v;
    },
    Lightbulb: function (v) {
      LightbulbIcon = v;
    }
  }, 2);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 3);
  var OllamaService;
  module1.link("../../api/ai/ollamaService", {
    OllamaService: function (v) {
      OllamaService = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TabPanel = function (_ref) {
    var children = _ref.children,
      value = _ref.value,
      index = _ref.index;
    return /*#__PURE__*/React.createElement("div", {
      hidden: value !== index,
      style: {
        padding: '16px'
      }
    }, value === index && children);
  };
  _c = TabPanel;
  var TeamAIInsightsPanel = function (_ref2) {
    var taskId = _ref2.taskId,
      propTask = _ref2.task;
    _s();
    var _useState = useState(false),
      _useState2 = _slicedToArray(_useState, 2),
      loading = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = useState(null),
      _useState4 = _slicedToArray(_useState3, 2),
      error = _useState4[0],
      setError = _useState4[1];
    var _useState5 = useState(null),
      _useState6 = _slicedToArray(_useState5, 2),
      insights = _useState6[0],
      setInsights = _useState6[1];
    var _useState7 = useState(0),
      _useState8 = _slicedToArray(_useState7, 2),
      selectedTab = _useState8[0],
      setSelectedTab = _useState8[1];
    var _useState9 = useState(0),
      _useState10 = _slicedToArray(_useState9, 2),
      retryCount = _useState10[0],
      setRetryCount = _useState10[1];
    var fetchInsights = function () {
      function _callee() {
        var result, taskObj;
        return _regeneratorRuntime.async(function () {
          function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                setLoading(true);
                setError(null);
                _context.prev = 2;
                taskObj = propTask;
                if (!(!taskObj || !taskObj.title)) {
                  _context.next = 8;
                  break;
                }
                setError('Task data is missing or incomplete.');
                setLoading(false);
                return _context.abrupt("return");
              case 8:
                _context.t0 = selectedTab;
                _context.next = _context.t0 === 0 ? 11 : _context.t0 === 1 ? 15 : _context.t0 === 2 ? 19 : 23;
                break;
              case 11:
                _context.next = 13;
                return _regeneratorRuntime.awrap(OllamaService.suggestTaskDivision(taskObj));
              case 13:
                result = _context.sent;
                return _context.abrupt("break", 26);
              case 15:
                _context.next = 17;
                return _regeneratorRuntime.awrap(OllamaService.recommendTools(taskObj._id));
              case 17:
                result = _context.sent;
                return _context.abrupt("break", 26);
              case 19:
                _context.next = 21;
                return _regeneratorRuntime.awrap(OllamaService.predictDailyProgress(taskObj._id));
              case 21:
                result = _context.sent;
                return _context.abrupt("break", 26);
              case 23:
                _context.next = 25;
                return _regeneratorRuntime.awrap(OllamaService.suggestTaskDivision(taskObj));
              case 25:
                result = _context.sent;
              case 26:
                setInsights(result);
                setRetryCount(0); // Reset retry count on success
                _context.next = 33;
                break;
              case 30:
                _context.prev = 30;
                _context.t1 = _context["catch"](2);
                if (_context.t1.error === 'connection-failed') {
                  setError('Could not connect to Ollama. Please make sure Ollama is running on your machine.');
                } else {
                  setError(_context.t1.message || 'Failed to fetch insights');
                }
              case 33:
                _context.prev = 33;
                setLoading(false);
                return _context.finish(33);
              case 36:
              case "end":
                return _context.stop();
            }
          }
          return _callee$;
        }(), null, null, [[2, 30, 33, 36]], Promise);
      }
      return _callee;
    }();
    useEffect(function () {
      fetchInsights();
    }, [taskId, selectedTab]);
    var handleTabChange = function (event, newValue) {
      setSelectedTab(newValue);
    };
    var handleRefresh = function () {
      setRetryCount(function (prev) {
        return prev + 1;
      });
      fetchInsights();
    };
    var renderInsightContent = function (content) {
      if (!content) return null;

      // Split content into sections based on numbered points
      var sections = content.split(/\d\./).filter(Boolean);
      return /*#__PURE__*/React.createElement(List, null, sections.map(function (section, index) {
        return /*#__PURE__*/React.createElement(ListItem, {
          key: index,
          alignItems: "flex-start"
        }, /*#__PURE__*/React.createElement(ListItemText, {
          primary: /*#__PURE__*/React.createElement(Typography, {
            variant: "subtitle1",
            color: "primary"
          }, section.split('\n')[0].trim()),
          secondary: /*#__PURE__*/React.createElement(Typography, {
            component: "span",
            variant: "body2",
            color: "text.primary",
            style: {
              whiteSpace: 'pre-line'
            }
          }, section.split('\n').slice(1).join('\n').trim())
        }));
      }));
    };
    var renderErrorContent = function () {
      if (!error) return null;
      return /*#__PURE__*/React.createElement(Box, {
        sx: {
          p: 2,
          textAlign: 'center'
        }
      }, /*#__PURE__*/React.createElement(ErrorIcon, {
        color: "error",
        sx: {
          fontSize: 48,
          mb: 2
        }
      }), /*#__PURE__*/React.createElement(Typography, {
        variant: "h6",
        color: "error",
        gutterBottom: true
      }, error), retryCount < 3 && /*#__PURE__*/React.createElement(Button, {
        variant: "contained",
        color: "primary",
        onClick: handleRefresh,
        startIcon: /*#__PURE__*/React.createElement(RefreshIcon, null),
        sx: {
          mt: 2
        }
      }, "Try Again"), retryCount >= 3 && /*#__PURE__*/React.createElement(Typography, {
        variant: "body2",
        color: "text.secondary",
        sx: {
          mt: 2
        }
      }, "Multiple attempts failed. Please try again later."));
    };
    return /*#__PURE__*/React.createElement(Card, {
      sx: {
        maxWidth: 800,
        margin: '20px auto',
        p: 2,
        height: '100%'
      }
    }, /*#__PURE__*/React.createElement(CardContent, null, /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      component: "h2"
    }, "AI Task Assistant"), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Refresh Insights"
    }, /*#__PURE__*/React.createElement(IconButton, {
      onClick: handleRefresh,
      disabled: loading
    }, /*#__PURE__*/React.createElement(RefreshIcon, null)))), /*#__PURE__*/React.createElement(Tabs, {
      value: selectedTab,
      onChange: handleTabChange,
      variant: "fullWidth",
      sx: {
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Tooltip, {
      title: "Task Division"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(GroupIcon, null),
      "aria-label": "Task Division"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Tools & Techniques"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(BuildIcon, null),
      "aria-label": "Tools & Techniques"
    })), /*#__PURE__*/React.createElement(Tooltip, {
      title: "Daily Progress"
    }, /*#__PURE__*/React.createElement(Tab, {
      icon: /*#__PURE__*/React.createElement(TimelineIcon, null),
      "aria-label": "Daily Progress"
    }))), loading && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(CircularProgress, null)), error && renderErrorContent(), !loading && !error && insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        p: 1
      }
    }, renderInsightContent(insights)), !loading && !error && !insights && /*#__PURE__*/React.createElement(Box, {
      sx: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        p: 3
      }
    }, /*#__PURE__*/React.createElement(LightbulbIcon, {
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Typography, null, "No insights available"))));
  };
  _s(TeamAIInsightsPanel, "Act2AOnU28qM7wyZXfGrXz8QXTY=");
  _c2 = TeamAIInsightsPanel;
  var _c, _c2;
  $RefreshReg$(_c, "TabPanel");
  $RefreshReg$(_c2, "TeamAIInsightsPanel");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamMemberForm.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamMemberForm.jsx                                                                            //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _toConsumableArray;
  module1.link("@babel/runtime/helpers/toConsumableArray", {
    default: function (v) {
      _toConsumableArray = v;
    }
  }, 1);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 2);
  module1.export({
    TeamMemberForm: function () {
      return TeamMemberForm;
    }
  });
  var React, useState;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var Formik, Form, Field;
  module1.link("formik", {
    Formik: function (v) {
      Formik = v;
    },
    Form: function (v) {
      Form = v;
    },
    Field: function (v) {
      Field = v;
    }
  }, 2);
  var Yup;
  module1.link("yup", {
    "*": function (v) {
      Yup = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TeamMemberSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email address').required('Email is required'),
    firstName: Yup.string().required('First name is required'),
    lastName: Yup.string().required('Last name is required'),
    role: Yup.string().required('Role is required'),
    department: Yup.string().required('Department is required'),
    skills: Yup.array().of(Yup.string()).min(1, 'At least one skill is required')
  });
  var TeamMemberForm = function (_ref) {
    var _member$emails, _member$emails$, _member$profile, _member$profile2, _member$profile3, _member$profile4, _member$profile5;
    var member = _ref.member,
      onCancel = _ref.onCancel;
    _s();
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      newSkill = _useState2[0],
      setNewSkill = _useState2[1];
    var _useState3 = useState(''),
      _useState4 = _slicedToArray(_useState3, 2),
      submitError = _useState4[0],
      setSubmitError = _useState4[1];
    var initialValues = member ? {
      email: ((_member$emails = member.emails) === null || _member$emails === void 0 ? void 0 : (_member$emails$ = _member$emails[0]) === null || _member$emails$ === void 0 ? void 0 : _member$emails$.address) || '',
      firstName: ((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '',
      lastName: ((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '',
      role: ((_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : _member$profile3.role) || '',
      department: ((_member$profile4 = member.profile) === null || _member$profile4 === void 0 ? void 0 : _member$profile4.department) || '',
      skills: ((_member$profile5 = member.profile) === null || _member$profile5 === void 0 ? void 0 : _member$profile5.skills) || []
    } : {
      email: '',
      firstName: '',
      lastName: '',
      role: '',
      department: '',
      skills: []
    };
    var handleSubmit = function () {
      function _callee(values) {
        return _regeneratorRuntime.async(function () {
          function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                setSubmitError('');
                _context.prev = 1;
                if (!member) {
                  _context.next = 7;
                  break;
                }
                _context.next = 5;
                return _regeneratorRuntime.awrap(Meteor.call('users.updateTeamMember', member._id, values));
              case 5:
                _context.next = 9;
                break;
              case 7:
                _context.next = 9;
                return _regeneratorRuntime.awrap(Meteor.call('users.addTeamMember', values));
              case 9:
                onCancel();
                _context.next = 15;
                break;
              case 12:
                _context.prev = 12;
                _context.t0 = _context["catch"](1);
                setSubmitError(_context.t0.message);
              case 15:
              case "end":
                return _context.stop();
            }
          }
          return _callee$;
        }(), null, null, [[1, 12]], Promise);
      }
      return _callee;
    }();
    return /*#__PURE__*/React.createElement("div", {
      className: "bg-white rounded-xl shadow-lg p-6 max-w-2xl mx-auto"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between items-center mb-6"
    }, /*#__PURE__*/React.createElement("h2", {
      className: "text-2xl font-bold text-gray-900"
    }, member ? 'Edit Team Member' : 'Add Team Member'), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: onCancel,
      className: "text-gray-400 hover:text-gray-500"
    }, /*#__PURE__*/React.createElement("svg", {
      className: "h-6 w-6",
      fill: "none",
      viewBox: "0 0 24 24",
      stroke: "currentColor"
    }, /*#__PURE__*/React.createElement("path", {
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2,
      d: "M6 18L18 6M6 6l12 12"
    })))), submitError && /*#__PURE__*/React.createElement("div", {
      className: "mb-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-md"
    }, submitError), /*#__PURE__*/React.createElement(Formik, {
      initialValues: initialValues,
      validationSchema: TeamMemberSchema,
      onSubmit: handleSubmit
    }, function (_ref2) {
      var values = _ref2.values,
        errors = _ref2.errors,
        touched = _ref2.touched,
        setFieldValue = _ref2.setFieldValue;
      return /*#__PURE__*/React.createElement(Form, {
        className: "space-y-6"
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "block text-sm font-medium text-gray-700"
      }, "Email"), /*#__PURE__*/React.createElement(Field, {
        type: "email",
        name: "email",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Enter email address"
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "grid grid-cols-1 md:grid-cols-2 gap-4"
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "firstName",
        className: "block text-sm font-medium text-gray-700"
      }, "First Name"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "firstName",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Enter first name"
      }), errors.firstName && touched.firstName && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.firstName)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "lastName",
        className: "block text-sm font-medium text-gray-700"
      }, "Last Name"), /*#__PURE__*/React.createElement(Field, {
        type: "text",
        name: "lastName",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Enter last name"
      }), errors.lastName && touched.lastName && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.lastName))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "role",
        className: "block text-sm font-medium text-gray-700"
      }, "Role"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "role",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500"
      }, /*#__PURE__*/React.createElement("option", {
        value: ""
      }, "Select a role"), /*#__PURE__*/React.createElement("option", {
        value: "developer"
      }, "Developer"), /*#__PURE__*/React.createElement("option", {
        value: "designer"
      }, "Designer"), /*#__PURE__*/React.createElement("option", {
        value: "manager"
      }, "Manager"), /*#__PURE__*/React.createElement("option", {
        value: "analyst"
      }, "Analyst")), errors.role && touched.role && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.role)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        htmlFor: "department",
        className: "block text-sm font-medium text-gray-700"
      }, "Department"), /*#__PURE__*/React.createElement(Field, {
        as: "select",
        name: "department",
        className: "mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500"
      }, /*#__PURE__*/React.createElement("option", {
        value: ""
      }, "Select a department"), /*#__PURE__*/React.createElement("option", {
        value: "Engineering"
      }, "Engineering"), /*#__PURE__*/React.createElement("option", {
        value: "Design"
      }, "Design"), /*#__PURE__*/React.createElement("option", {
        value: "Product"
      }, "Product"), /*#__PURE__*/React.createElement("option", {
        value: "Marketing"
      }, "Marketing"), /*#__PURE__*/React.createElement("option", {
        value: "Sales"
      }, "Sales"), /*#__PURE__*/React.createElement("option", {
        value: "Support"
      }, "Support")), errors.department && touched.department && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.department)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
        className: "block text-sm font-medium text-gray-700"
      }, "Skills"), /*#__PURE__*/React.createElement("div", {
        className: "flex gap-2 mt-1"
      }, /*#__PURE__*/React.createElement("input", {
        type: "text",
        value: newSkill,
        onChange: function (e) {
          return setNewSkill(e.target.value);
        },
        className: "flex-1 rounded-md border border-gray-300 px-3 py-2 focus:border-green-500 focus:ring-1 focus:ring-green-500",
        placeholder: "Add a skill",
        onKeyPress: function (e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            if (newSkill.trim() && !values.skills.includes(newSkill.trim())) {
              setFieldValue('skills', [].concat(_toConsumableArray(values.skills), [newSkill.trim()]));
              setNewSkill('');
            }
          }
        }
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: function () {
          if (newSkill.trim() && !values.skills.includes(newSkill.trim())) {
            setFieldValue('skills', [].concat(_toConsumableArray(values.skills), [newSkill.trim()]));
            setNewSkill('');
          }
        },
        className: "px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
      }, "Add")), /*#__PURE__*/React.createElement("div", {
        className: "mt-2 flex flex-wrap gap-2"
      }, values.skills.map(function (skill, index) {
        return /*#__PURE__*/React.createElement("span", {
          key: index,
          className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
        }, skill, /*#__PURE__*/React.createElement("button", {
          type: "button",
          onClick: function () {
            setFieldValue('skills', values.skills.filter(function (_, i) {
              return i !== index;
            }));
          },
          className: "ml-1 inline-flex items-center justify-center h-4 w-4 rounded-full hover:bg-green-200"
        }, "\xD7"));
      })), errors.skills && touched.skills && /*#__PURE__*/React.createElement("div", {
        className: "mt-1 text-sm text-red-600"
      }, errors.skills)), /*#__PURE__*/React.createElement("div", {
        className: "flex justify-end gap-3 pt-4"
      }, /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: onCancel,
        className: "px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
      }, "Cancel"), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
      }, member ? 'Save Changes' : 'Add Member')));
    }));
  };
  _s(TeamMemberForm, "hnW8fAclsRCCGBAQ6RzUgEzp6nE=");
  _c = TeamMemberForm;
  var _c;
  $RefreshReg$(_c, "TeamMemberForm");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamMemberMetrics.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamMemberMetrics.jsx                                                                         //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamMemberMetrics: function () {
      return TeamMemberMetrics;
    }
  });
  var React;
  module1.link("react", {
    "default": function (v) {
      React = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 2);
  var Tasks;
  module1.link("/imports/api/tasks", {
    Tasks: function (v) {
      Tasks = v;
    }
  }, 3);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TeamMemberMetrics = function (_ref) {
    var memberId = _ref.memberId;
    _s();
    var _useTracker = useTracker(function () {
        var handle = Meteor.subscribe('tasks');
        var isLoading = !handle.ready();
        if (isLoading) {
          return {
            metrics: null,
            isLoading: isLoading
          };
        }
        var tasks = Tasks.find({
          assignedTo: memberId
        }).fetch();
        var totalTasks = tasks.length;
        var completedTasks = tasks.filter(function (task) {
          return task.status === 'completed';
        }).length;
        var inProgressTasks = tasks.filter(function (task) {
          return task.status === 'in-progress';
        }).length;
        var pendingTasks = tasks.filter(function (task) {
          return task.status === 'pending';
        }).length;
        var highPriorityTasks = tasks.filter(function (task) {
          return task.priority === 'high';
        }).length;
        var mediumPriorityTasks = tasks.filter(function (task) {
          return task.priority === 'medium';
        }).length;
        var lowPriorityTasks = tasks.filter(function (task) {
          return task.priority === 'low';
        }).length;
        var averageProgress = tasks.length > 0 ? tasks.reduce(function (sum, task) {
          return sum + task.progress;
        }, 0) / tasks.length : 0;
        var onTimeTasks = tasks.filter(function (task) {
          if (task.status !== 'completed') return false;
          var completedDate = task.updatedAt || task.createdAt;
          return new Date(completedDate) <= new Date(task.dueDate);
        }).length;
        var completionRate = totalTasks > 0 ? completedTasks / totalTasks * 100 : 0;
        var onTimeRate = completedTasks > 0 ? onTimeTasks / completedTasks * 100 : 0;
        return {
          metrics: {
            totalTasks: totalTasks,
            completedTasks: completedTasks,
            inProgressTasks: inProgressTasks,
            pendingTasks: pendingTasks,
            highPriorityTasks: highPriorityTasks,
            mediumPriorityTasks: mediumPriorityTasks,
            lowPriorityTasks: lowPriorityTasks,
            averageProgress: averageProgress,
            completionRate: completionRate,
            onTimeRate: onTimeRate
          },
          isLoading: isLoading
        };
      }, [memberId]),
      metrics = _useTracker.metrics,
      isLoading = _useTracker.isLoading;
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        className: "animate-pulse"
      }, /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-3/4 mb-4"
      }), /*#__PURE__*/React.createElement("div", {
        className: "h-4 bg-gray-200 rounded w-1/2"
      }));
    }
    return /*#__PURE__*/React.createElement("div", {
      className: "space-y-6"
    }, /*#__PURE__*/React.createElement("div", {
      className: "grid grid-cols-1 md:grid-cols-3 gap-4"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-white p-4 rounded-lg shadow"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-semibold text-gray-900 mb-4"
    }, "Task Status"), /*#__PURE__*/React.createElement("div", {
      className: "space-y-3"
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Completed"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.completedTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-green-500 h-2 rounded-full",
      style: {
        width: metrics.completedTasks / metrics.totalTasks * 100 + "%"
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "In Progress"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.inProgressTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-blue-500 h-2 rounded-full",
      style: {
        width: metrics.inProgressTasks / metrics.totalTasks * 100 + "%"
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Pending"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.pendingTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-yellow-500 h-2 rounded-full",
      style: {
        width: metrics.pendingTasks / metrics.totalTasks * 100 + "%"
      }
    }))))), /*#__PURE__*/React.createElement("div", {
      className: "bg-white p-4 rounded-lg shadow"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-semibold text-gray-900 mb-4"
    }, "Task Priority"), /*#__PURE__*/React.createElement("div", {
      className: "space-y-3"
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "High Priority"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.highPriorityTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-red-500 h-2 rounded-full",
      style: {
        width: metrics.highPriorityTasks / metrics.totalTasks * 100 + "%"
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Medium Priority"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.mediumPriorityTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-yellow-500 h-2 rounded-full",
      style: {
        width: metrics.mediumPriorityTasks / metrics.totalTasks * 100 + "%"
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Low Priority"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.lowPriorityTasks)), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-green-500 h-2 rounded-full",
      style: {
        width: metrics.lowPriorityTasks / metrics.totalTasks * 100 + "%"
      }
    }))))), /*#__PURE__*/React.createElement("div", {
      className: "bg-white p-4 rounded-lg shadow"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-semibold text-gray-900 mb-4"
    }, "Performance"), /*#__PURE__*/React.createElement("div", {
      className: "space-y-4"
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Completion Rate"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.completionRate.toFixed(1), "%")), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-green-500 h-2 rounded-full",
      style: {
        width: metrics.completionRate + "%"
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "On-Time Rate"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.onTimeRate.toFixed(1), "%")), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-blue-500 h-2 rounded-full",
      style: {
        width: metrics.onTimeRate + "%"
      }
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between text-sm mb-1"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-600"
    }, "Average Progress"), /*#__PURE__*/React.createElement("span", {
      className: "text-gray-900 font-medium"
    }, metrics.averageProgress.toFixed(1), "%")), /*#__PURE__*/React.createElement("div", {
      className: "w-full bg-gray-200 rounded-full h-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-purple-500 h-2 rounded-full",
      style: {
        width: metrics.averageProgress + "%"
      }
    })))))));
  };
  _s(TeamMemberMetrics, "KKxl2vplfctxeDyZFWbPRqkRWww=", false, function () {
    return [useTracker];
  });
  _c = TeamMemberMetrics;
  var _c;
  $RefreshReg$(_c, "TeamMemberMetrics");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamTaskView.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/components/TeamTaskView.jsx                                                                              //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 1);
  module1.export({
    TeamTaskView: function () {
      return TeamTaskView;
    }
  });
  var React, useState, useEffect;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    }
  }, 0);
  var useNavigate;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    }
  }, 1);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 2);
  var TeamAIInsightsPanel;
  module1.link("./TeamAIInsightsPanel", {
    TeamAIInsightsPanel: function (v) {
      TeamAIInsightsPanel = v;
    }
  }, 3);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TeamTaskView = function (_ref) {
    var task = _ref.task,
      onCancel = _ref.onCancel;
    _s();
    // State for managing UI interactions and data
    var _useState = useState(false),
      _useState2 = _slicedToArray(_useState, 2),
      isSubmitting = _useState2[0],
      setIsSubmitting = _useState2[1];
    var _useState3 = useState(''),
      _useState4 = _slicedToArray(_useState3, 2),
      newLink = _useState4[0],
      setNewLink = _useState4[1];
    var _useState5 = useState(null),
      _useState6 = _slicedToArray(_useState5, 2),
      file = _useState6[0],
      setFile = _useState6[1];
    var _useState7 = useState(0),
      _useState8 = _slicedToArray(_useState7, 2),
      uploadProgress = _useState8[0],
      setUploadProgress = _useState8[1];
    var _useState9 = useState(false),
      _useState10 = _slicedToArray(_useState9, 2),
      isUploading = _useState10[0],
      setIsUploading = _useState10[1];
    var _useState11 = useState(''),
      _useState12 = _slicedToArray(_useState11, 2),
      error = _useState12[0],
      setError = _useState12[1];
    var _useState13 = useState(''),
      _useState14 = _slicedToArray(_useState13, 2),
      successMessage = _useState14[0],
      setSuccessMessage = _useState14[1];
    var _useState15 = useState({}),
      _useState16 = _slicedToArray(_useState15, 2),
      userNames = _useState16[0],
      setUserNames = _useState16[1];
    var _useState17 = useState(true),
      _useState18 = _slicedToArray(_useState17, 2),
      isLoadingUsers = _useState18[0],
      setIsLoadingUsers = _useState18[1];

    // Hook for navigation
    var navigate = useNavigate();

    // Subscribe to tasks and user data
    var _useTracker = useTracker(function () {
        var tasksHandle = Meteor.subscribe('tasks');
        var userDataHandle = Meteor.subscribe('userData');
        var taskUsersHandle = Meteor.subscribe('taskUsers');
        return {
          tasks: tasksHandle.ready() && userDataHandle.ready() && taskUsersHandle.ready()
        };
      }),
      tasks = _useTracker.tasks;

    // Load user data when task changes
    useEffect(function () {
      var loadUserData = function () {
        function _callee2() {
          var userIds, userDataPromises, userEntries, names;
          return _regeneratorRuntime.async(function () {
            function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  if (task) {
                    _context2.next = 2;
                    break;
                  }
                  return _context2.abrupt("return");
                case 2:
                  setIsLoadingUsers(true);
                  _context2.prev = 3;
                  // Collect all user IDs from the task
                  userIds = new Set(); // Add users who uploaded attachments
                  if (task.attachments) {
                    task.attachments.forEach(function (attachment) {
                      if (attachment.uploadedBy) {
                        userIds.add(String(attachment.uploadedBy));
                      }
                    });
                  }

                  // Add users who added links
                  if (task.links) {
                    task.links.forEach(function (link) {
                      if (link.addedBy) {
                        userIds.add(String(link.addedBy));
                      }
                    });
                  }

                  // Add assigned users
                  if (task.assignedTo) {
                    task.assignedTo.forEach(function (userId) {
                      userIds.add(String(userId));
                    });
                  }
                  console.log('Loading user data for IDs:', Array.from(userIds));

                  // Load user data for all collected IDs
                  userDataPromises = Array.from(userIds).map(function () {
                    function _callee(userId) {
                      var _user$profile, _user$profile2;
                      var user, firstName, lastName, fullName;
                      return _regeneratorRuntime.async(function () {
                        function _callee$(_context) {
                          while (1) switch (_context.prev = _context.next) {
                            case 0:
                              _context.next = 2;
                              return _regeneratorRuntime.awrap(Meteor.users.findOneAsync(userId));
                            case 2:
                              user = _context.sent;
                              if (user) {
                                _context.next = 5;
                                break;
                              }
                              return _context.abrupt("return", [userId, 'Unknown User']);
                            case 5:
                              firstName = ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.firstName) || '';
                              lastName = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.lastName) || '';
                              fullName = (firstName + " " + lastName).trim();
                              return _context.abrupt("return", [userId, fullName || 'Unknown User']);
                            case 9:
                            case "end":
                              return _context.stop();
                          }
                        }
                        return _callee$;
                      }(), null, null, null, Promise);
                    }
                    return _callee;
                  }());
                  _context2.next = 12;
                  return _regeneratorRuntime.awrap(Promise.all(userDataPromises));
                case 12:
                  userEntries = _context2.sent;
                  names = Object.fromEntries(userEntries);
                  console.log('Loaded user names:', names);
                  setUserNames(names);
                  _context2.next = 21;
                  break;
                case 18:
                  _context2.prev = 18;
                  _context2.t0 = _context2["catch"](3);
                  console.error('Error loading user data:', _context2.t0);
                case 21:
                  _context2.prev = 21;
                  setIsLoadingUsers(false);
                  return _context2.finish(21);
                case 24:
                case "end":
                  return _context2.stop();
              }
            }
            return _callee2$;
          }(), null, null, [[3, 18, 21, 24]], Promise);
        }
        return _callee2;
      }();
      loadUserData();
    }, [task]);

    // Handles updating the task's overall progress based on checklist completion
    var handleSubmit = function () {
      function _callee3() {
        var _task$checklist, _task$checklist2, completedItems, totalItems, progress;
        return _regeneratorRuntime.async(function () {
          function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                setIsSubmitting(true);
                setError(''); // Clear previous errors
                setSuccessMessage(''); // Clear previous success messages
                _context3.prev = 3;
                // Calculate progress based on completed checklist items
                completedItems = ((_task$checklist = task.checklist) === null || _task$checklist === void 0 ? void 0 : _task$checklist.filter(function (item) {
                  return item.completed;
                }).length) || 0;
                totalItems = ((_task$checklist2 = task.checklist) === null || _task$checklist2 === void 0 ? void 0 : _task$checklist2.length) || 0;
                progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0; // Call Meteor method to update task progress
                _context3.next = 9;
                return _regeneratorRuntime.awrap(Meteor.call('tasks.updateProgress', task._id, progress));
              case 9:
                setIsSubmitting(false);
                setSuccessMessage('Task progress updated successfully!');
                setTimeout(function () {
                  return navigate('/team-dashboard/tasks');
                }, 1500); // Navigate after a brief success message
                _context3.next = 19;
                break;
              case 14:
                _context3.prev = 14;
                _context3.t0 = _context3["catch"](3);
                console.error('Error submitting task:', _context3.t0);
                setError(_context3.t0.reason || 'Failed to update task progress.');
                setIsSubmitting(false);
              case 19:
              case "end":
                return _context3.stop();
            }
          }
          return _callee3$;
        }(), null, null, [[3, 14]], Promise);
      }
      return _callee3;
    }();

    // Handles file selection for attachment upload
    var handleFileChange = function (e) {
      var selectedFile = e.target.files[0];
      if (selectedFile) {
        // Check file size (limit to 5MB)
        if (selectedFile.size > 5 * 1024 * 1024) {
          setError('File size exceeds 5MB limit.');
          setFile(null); // Clear selected file
          return;
        }
        setFile(selectedFile);
        setError(''); // Clear any previous errors
        setSuccessMessage(''); // Clear any previous success messages
      }
    };

    // Handles uploading a selected file as an attachment
    var handleFileUpload = function () {
      if (!file) {
        setError('Please select a file to upload.');
        return;
      }
      setIsUploading(true);
      setUploadProgress(0);
      setError(''); // Clear previous errors
      setSuccessMessage(''); // Clear previous success messages

      var reader = new FileReader();

      // Update upload progress
      reader.onprogress = function (event) {
        if (event.lengthComputable) {
          var progress = Math.round(event.loaded / event.total * 100);
          setUploadProgress(progress);
        }
      };

      // When file reading is complete, call Meteor method to add attachment
      reader.onload = function (event) {
        var fileData = {
          name: file.name,
          type: file.type,
          data: event.target.result // Base64 encoded file data
        };
        Meteor.call('tasks.addAttachment', task._id, fileData, function (error, result) {
          setIsUploading(false);
          if (error) {
            console.error('Error uploading file:', error);
            setError(error.reason || 'Failed to upload file.');
          } else {
            setFile(null); // Clear selected file
            setSuccessMessage('File uploaded successfully!');
            setTimeout(function () {
              return setSuccessMessage('');
            }, 3000); // Clear message after 3 seconds
            // Reset the file input visually
            var fileInput = document.getElementById('file-upload');
            if (fileInput) fileInput.value = '';
          }
        });
      };

      // Handle file reading errors
      reader.onerror = function () {
        setIsUploading(false);
        setError('Error reading file.');
      };
      reader.readAsDataURL(file); // Read file as Data URL (base64)
    };

    // Handles adding a new link to the task
    var handleAddLink = function () {
      if (!newLink) {
        setError('Please enter a link.');
        return;
      }

      // Basic URL validation
      try {
        new URL(newLink); // Throws an error for invalid URLs
      } catch (e) {
        setError('Please enter a valid URL (e.g., include http:// or https://).');
        return;
      }
      Meteor.call('tasks.addLink', task._id, newLink, function (error, result) {
        if (error) {
          console.error('Error adding link:', error);
          setError(error.reason || 'Failed to add link.');
        } else {
          setNewLink(''); // Clear input
          setSuccessMessage('Link added successfully!');
          setTimeout(function () {
            return setSuccessMessage('');
          }, 3000); // Clear message after 3 seconds
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'row',
        gap: '32px',
        padding: '24px',
        background: '#f8fafc',
        // Light grey background
        minHeight: 'calc(100vh - 64px)',
        // Adjust for header if present
        fontFamily: 'Inter, sans-serif' // Modern sans-serif font
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 2
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
        border: '1px solid #e2e8f0' // Light border
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '24px',
        flexWrap: 'wrap',
        gap: '16px'
      }
    }, /*#__PURE__*/React.createElement("button", {
      onClick: onCancel,
      style: {
        background: 'none',
        border: 'none',
        cursor: 'pointer',
        fontSize: '1.8rem',
        color: '#64748b',
        transition: 'color 0.2s ease',
        marginRight: '12px',
        padding: 0,
        display: 'flex',
        alignItems: 'center'
      },
      onMouseOver: function (e) {
        return e.currentTarget.style.color = '#334155';
      },
      onMouseOut: function (e) {
        return e.currentTarget.style.color = '#64748b';
      },
      "aria-label": "Back"
    }, "\u2190"), /*#__PURE__*/React.createElement("h2", {
      style: {
        fontSize: '1.5rem',
        fontWeight: '600',
        color: '#0f172a',
        margin: '0',
        wordBreak: 'break-word',
        display: 'inline-block'
      }
    }, task.title)), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#334155',
        marginBottom: '8px',
        fontSize: '1.2rem',
        fontWeight: '600'
      }
    }, "Description"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        whiteSpace: 'pre-line',
        lineHeight: '1.6'
      }
    }, task.description || 'No description provided.')), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '32px',
        marginTop: '32px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Status"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in_progress' ? '#fef9c3' : '#f1f5f9',
        color: task.status === 'completed' ? '#166534' : task.status === 'in_progress' ? '#92400e' : '#475569'
      }
    }, task.status === 'not_started' ? 'Not Started' : task.status === 'in_progress' ? 'In Progress' : task.status === 'blocked' ? 'Blocked' : task.status === 'completed' ? 'Completed' : task.status)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Priority"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef9c3' : '#dcfce7',
        color: task.priority === 'high' ? '#991b1b' : task.priority === 'medium' ? '#92400e' : '#166534'
      }
    }, task.priority === 'high' ? 'High' : task.priority === 'medium' ? 'Medium' : 'Low')), task.dueDate && /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Due Date"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: '#e0e7ef',
        color: '#2563eb'
      }
    }, new Date(task.dueDate).toLocaleDateString())), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Progress"), /*#__PURE__*/React.createElement("span", {
      style: {
        padding: '8px 20px',
        borderRadius: '20px',
        fontSize: '1rem',
        fontWeight: '500',
        backgroundColor: '#e0e7ef',
        color: '#2563eb'
      }
    }, task.progress || 0, "%")), task.assignedTo && task.assignedTo.length > 0 && /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.1rem',
        color: '#0f172a',
        marginBottom: '8px'
      }
    }, "Team Members"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '8px',
        flexWrap: 'wrap'
      }
    }, task.assignedTo.map(function (userId) {
      return /*#__PURE__*/React.createElement("span", {
        key: userId,
        style: {
          padding: '8px 20px',
          borderRadius: '20px',
          fontSize: '1rem',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          fontWeight: '500'
        }
      }, userNames[userId] || 'Unknown User');
    })))), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#334155',
        marginBottom: '16px',
        fontSize: '1.2rem',
        fontWeight: '600'
      }
    }, "Checklist"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, task.checklist.map(function (item, index) {
      return /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          backgroundColor: '#f8fafc',
          padding: '12px',
          borderRadius: '8px',
          border: '1px solid #e2e8f0'
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "checkbox",
        checked: item.completed,
        onChange: function () {
          Meteor.call('tasks.toggleChecklistItem', task._id, index, function (error) {
            if (error) {
              console.error('Error toggling checklist item:', error);
              setError(error.reason || 'Failed to update checklist item.');
            } else {
              setSuccessMessage('Checklist item updated!');
              setTimeout(function () {
                return setSuccessMessage('');
              }, 3000);
            }
          });
        },
        style: {
          width: '20px',
          height: '20px',
          accentColor: '#3b82f6',
          cursor: 'pointer'
        }
      }), /*#__PURE__*/React.createElement("span", {
        style: {
          color: item.completed ? '#64748b' : '#0f172a',
          textDecoration: item.completed ? 'line-through' : 'none',
          fontSize: '1rem',
          flex: 1
        }
      }, item.text));
    }))), /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: '24px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.2rem',
        fontWeight: '600',
        color: '#0f172a',
        marginBottom: '16px'
      }
    }, "Attachments"), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '16px',
        padding: '16px',
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0',
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      id: "file-upload",
      type: "file",
      onChange: handleFileChange,
      style: {
        fontSize: '0.9rem',
        color: '#475569'
      }
    }), /*#__PURE__*/React.createElement("button", {
      onClick: handleFileUpload,
      disabled: !file || isUploading,
      style: {
        padding: '10px 20px',
        backgroundColor: '#3b82f6',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: file && !isUploading ? 'pointer' : 'not-allowed',
        opacity: file && !isUploading ? 1 : 0.7,
        fontSize: '1rem',
        fontWeight: 600,
        transition: 'background-color 0.2s ease'
      },
      onMouseOver: function (e) {
        if (file && !isUploading) e.currentTarget.style.backgroundColor = '#2563eb';
      },
      onMouseOut: function (e) {
        if (file && !isUploading) e.currentTarget.style.backgroundColor = '#3b82f6';
      }
    }, isUploading ? "Uploading... " + uploadProgress + "%" : 'Upload File'), isUploading && /*#__PURE__*/React.createElement("div", {
      style: {
        height: '8px',
        backgroundColor: '#e2e8f0',
        borderRadius: '4px',
        overflow: 'hidden',
        marginTop: '8px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: uploadProgress + "%",
        height: '100%',
        backgroundColor: '#3b82f6',
        transition: 'width 0.1s linear'
      }
    }))), task.attachments && task.attachments.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, task.attachments.map(function (attachment, index) {
      // Ensure we're working with string IDs
      var currentUserId = String(Meteor.userId());
      var uploadedById = String(attachment.uploadedBy);
      var isOwner = currentUserId === uploadedById;
      console.log('Rendering attachment:', {
        attachment: attachment,
        uploadedById: uploadedById,
        userName: userNames[uploadedById],
        allUserNames: userNames
      });
      return /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          wordBreak: 'break-word' // Ensure long names wrap
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          minWidth: 0
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1.5rem',
          color: '#64748b'
        }
      }, "\uD83D\uDCCE"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.9rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, attachment.name), /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.75rem',
          color: '#64748b'
        }
      }, "Uploaded by ", userNames[uploadedById] || 'Unknown User', " on ", new Date(attachment.uploadedAt).toLocaleDateString()))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px',
          flexShrink: 0
        }
      }, /*#__PURE__*/React.createElement("button", {
        onClick: function () {
          try {
            var base64Data = attachment.data;
            if (base64Data.startsWith('data:')) {
              base64Data = base64Data.split(',')[1];
            }
            var byteCharacters = atob(base64Data);
            var byteNumbers = new Array(byteCharacters.length);
            for (var i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            var byteArray = new Uint8Array(byteNumbers);
            var mimeType = attachment.type || 'application/octet-stream';
            var blob = new Blob([byteArray], {
              type: mimeType
            });
            var url = window.URL.createObjectURL(blob);
            var link = document.createElement('a');
            link.href = url;
            link.download = attachment.name;
            document.body.appendChild(link);
            link.click();
            setTimeout(function () {
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
            }, 100);
          } catch (downloadError) {
            console.error('Error downloading file:', downloadError);
            setError('Failed to download file. Please try again.');
          }
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#e2e8f0',
          border: 'none',
          borderRadius: '6px',
          color: '#0f172a',
          fontSize: '0.875rem',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease'
        },
        onMouseOver: function (e) {
          return e.currentTarget.style.backgroundColor = '#cbd5e1';
        },
        onMouseOut: function (e) {
          return e.currentTarget.style.backgroundColor = '#e2e8f0';
        }
      }, "Download"), isOwner && /*#__PURE__*/React.createElement("button", {
        onClick: function () {
          if (window.confirm('Are you sure you want to remove this attachment?')) {
            Meteor.call('tasks.removeAttachment', task._id, index, function (error) {
              if (error) {
                console.error('Error removing attachment:', error);
                setError(error.reason || 'Failed to remove attachment.');
              } else {
                setSuccessMessage('Attachment removed successfully!');
                setTimeout(function () {
                  return setSuccessMessage('');
                }, 3000);
              }
            });
          }
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#ef4444',
          border: 'none',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease'
        },
        onMouseOver: function (e) {
          return e.currentTarget.style.backgroundColor = '#dc2626';
        },
        onMouseOut: function (e) {
          return e.currentTarget.style.backgroundColor = '#ef4444';
        }
      }, "Remove")));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#64748b',
        fontSize: '0.9rem'
      }
    }, "No attachments yet. Upload files to share them!")), /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: '32px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        fontSize: '1.2rem',
        fontWeight: '600',
        color: '#0f172a',
        marginBottom: '16px'
      }
    }, "Links"), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: '16px',
        padding: '16px',
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0',
        display: 'flex',
        gap: '12px',
        alignItems: 'center'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: newLink,
      onChange: function (e) {
        return setNewLink(e.target.value);
      },
      placeholder: "Enter URL (https://example.com)",
      style: {
        flex: 1,
        padding: '10px 12px',
        border: '1px solid #cbd5e1',
        borderRadius: '6px',
        fontSize: '0.9rem',
        color: '#334155',
        boxSizing: 'border-box'
      }
    }), /*#__PURE__*/React.createElement("button", {
      onClick: handleAddLink,
      style: {
        padding: '10px 20px',
        backgroundColor: '#3b82f6',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        fontSize: '1rem',
        fontWeight: 600,
        transition: 'background-color 0.2s ease'
      },
      onMouseOver: function (e) {
        return e.currentTarget.style.backgroundColor = '#2563eb';
      },
      onMouseOut: function (e) {
        return e.currentTarget.style.backgroundColor = '#3b82f6';
      }
    }, "Add Link")), task.links && task.links.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }
    }, task.links.map(function (link, index) {
      // Ensure we're working with string IDs
      var currentUserId = String(Meteor.userId());
      var addedById = String(link.addedBy);
      var isOwner = currentUserId === addedById;
      return /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          wordBreak: 'break-word' // Ensure long URLs wrap
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          minWidth: 0
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1.5rem',
          color: '#64748b'
        }
      }, "\uD83D\uDD17"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("a", {
        href: link.url,
        target: "_blank",
        rel: "noopener noreferrer",
        style: {
          fontSize: '0.9rem',
          color: '#2563eb',
          textDecoration: 'none',
          fontWeight: '500'
        },
        onMouseOver: function (e) {
          return e.currentTarget.style.textDecoration = 'underline';
        },
        onMouseOut: function (e) {
          return e.currentTarget.style.textDecoration = 'none';
        }
      }, link.url), /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.75rem',
          color: '#64748b'
        }
      }, "Added by ", userNames[addedById] || 'Unknown User', " on ", new Date(link.addedAt).toLocaleDateString()))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px',
          flexShrink: 0
        }
      }, isOwner && /*#__PURE__*/React.createElement("button", {
        onClick: function () {
          if (window.confirm('Are you sure you want to remove this link?')) {
            Meteor.call('tasks.removeLink', task._id, index, function (error) {
              if (error) {
                console.error('Error removing link:', error);
                setError(error.reason || 'Failed to remove link.');
              } else {
                setSuccessMessage('Link removed successfully!');
                setTimeout(function () {
                  return setSuccessMessage('');
                }, 3000);
              }
            });
          }
        },
        style: {
          padding: '8px 16px',
          backgroundColor: '#ef4444',
          border: 'none',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          cursor: 'pointer',
          transition: 'background-color 0.2s ease'
        },
        onMouseOver: function (e) {
          return e.currentTarget.style.backgroundColor = '#dc2626';
        },
        onMouseOut: function (e) {
          return e.currentTarget.style.backgroundColor = '#ef4444';
        }
      }, "Remove")));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#64748b',
        fontSize: '0.9rem'
      }
    }, "No links yet. Add relevant URLs to this task!")), error && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#fee2e2',
        color: '#b91c1c',
        padding: '12px',
        borderRadius: '8px',
        marginTop: '24px',
        fontSize: '0.9rem',
        fontWeight: 500,
        border: '1px solid #fca5a5'
      }
    }, error), successMessage && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#dcfce7',
        color: '#166534',
        padding: '12px',
        borderRadius: '8px',
        marginTop: '24px',
        fontSize: '0.9rem',
        fontWeight: 500,
        border: '1px solid #86efac'
      }
    }, successMessage), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginTop: '32px',
        paddingTop: '24px',
        borderTop: '1px solid #e2e8f0'
      }
    }, /*#__PURE__*/React.createElement("button", {
      onClick: handleSubmit,
      disabled: isSubmitting,
      style: {
        backgroundColor: '#3b82f6',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        padding: '12px 24px',
        fontWeight: 600,
        fontSize: '1rem',
        cursor: 'pointer',
        opacity: isSubmitting ? 0.7 : 1,
        transition: 'background-color 0.2s ease'
      },
      onMouseOver: function (e) {
        if (!isSubmitting) e.currentTarget.style.backgroundColor = '#2563eb';
      },
      onMouseOut: function (e) {
        if (!isSubmitting) e.currentTarget.style.backgroundColor = '#3b82f6';
      }
    }, isSubmitting ? 'Updating Progress...' : 'Update Task Progress')))), /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: 350,
        maxWidth: 500
      }
    }, /*#__PURE__*/React.createElement(TeamAIInsightsPanel, {
      taskId: task._id,
      task: task
    })));
  };
  _s(TeamTaskView, "bVLBOqMpsgKQiYtiUBqcnu40q/Y=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = TeamTaskView;
  var _c;
  $RefreshReg$(_c, "TeamTaskView");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"pages":{"AdminDashboard.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/AdminDashboard.jsx                                                                                 //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default: function (v) {
      _objectSpread = v;
    }
  }, 1);
  var _toConsumableArray;
  module1.link("@babel/runtime/helpers/toConsumableArray", {
    default: function (v) {
      _toConsumableArray = v;
    }
  }, 2);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 3);
  module1.export({
    AdminDashboard: function () {
      return AdminDashboard;
    }
  });
  var React, useState, useEffect, useMemo;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    },
    useMemo: function (v) {
      useMemo = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useNavigate, Link, Routes, Route, useParams, useLocation;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    },
    Link: function (v) {
      Link = v;
    },
    Routes: function (v) {
      Routes = v;
    },
    Route: function (v) {
      Route = v;
    },
    useParams: function (v) {
      useParams = v;
    },
    useLocation: function (v) {
      useLocation = v;
    }
  }, 2);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 3);
  var Tasks, taskCategories, taskLabels;
  module1.link("/imports/api/tasks", {
    Tasks: function (v) {
      Tasks = v;
    },
    taskCategories: function (v) {
      taskCategories = v;
    },
    taskLabels: function (v) {
      taskLabels = v;
    }
  }, 4);
  var CreateTask;
  module1.link("../components/CreateTask", {
    CreateTask: function (v) {
      CreateTask = v;
    }
  }, 5);
  var EditTask;
  module1.link("../components/EditTask", {
    EditTask: function (v) {
      EditTask = v;
    }
  }, 6);
  var TeamMemberForm;
  module1.link("../components/TeamMemberForm", {
    TeamMemberForm: function (v) {
      TeamMemberForm = v;
    }
  }, 7);
  var TeamMemberMetrics;
  module1.link("../components/TeamMemberMetrics", {
    TeamMemberMetrics: function (v) {
      TeamMemberMetrics = v;
    }
  }, 8);
  var TaskAssignmentHistory;
  module1.link("../components/TaskAssignmentHistory", {
    TaskAssignmentHistory: function (v) {
      TaskAssignmentHistory = v;
    }
  }, 9);
  var ReportsPage;
  module1.link("./ReportsPage", {
    ReportsPage: function (v) {
      ReportsPage = v;
    }
  }, 10);
  var Formik, Form, Field;
  module1.link("formik", {
    Formik: function (v) {
      Formik = v;
    },
    Form: function (v) {
      Form = v;
    },
    Field: function (v) {
      Field = v;
    }
  }, 11);
  var Yup;
  module1.link("yup", {
    "*": function (v) {
      Yup = v;
    }
  }, 12);
  var Header;
  module1.link("../components/Header", {
    Header: function (v) {
      Header = v;
    }
  }, 13);
  var AIInsightsPanel;
  module1.link("../components/AIInsightsPanel", {
    AIInsightsPanel: function (v) {
      AIInsightsPanel = v;
    }
  }, 14);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$(),
    _s2 = $RefreshSig$(),
    _s3 = $RefreshSig$(),
    _s4 = $RefreshSig$();
  var AdminDashboardHome = function () {
    var _user$profile;
    _s();
    var _useTracker = useTracker(function () {
        var userHandle = Meteor.subscribe('userData');
        var user = Meteor.user();
        return {
          user: user
        };
      }),
      user = _useTracker.user;
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: '0 0 24px 0',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Welcome, ", (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.firstName) || 'Admin', "!"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px'
      }
    }, "\uD83D\uDCCB")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "Task Management"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "Create and manage tasks"), /*#__PURE__*/React.createElement(Link, {
      to: "/admin-dashboard/tasks",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "Manage Tasks")), /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px',
        color: '#16a34a'
      }
    }, "\uD83D\uDC65")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "Team Members"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View and manage team members"), /*#__PURE__*/React.createElement(Link, {
      to: "/admin-dashboard/team",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "Manage Team")), /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px',
        color: '#16a34a'
      }
    }, "\uD83D\uDCCA")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "Reports"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View task analytics and reports"), /*#__PURE__*/React.createElement(Link, {
      to: "/admin-dashboard/reports",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "View Reports")))));
  };
  _s(AdminDashboardHome, "8VRLU9EIGLI5z3TCuSbfcW/qD8k=", false, function () {
    return [useTracker];
  });
  _c = AdminDashboardHome;
  var TasksPage = function () {
    _s2();
    var _useState = useState(false),
      _useState2 = _slicedToArray(_useState, 2),
      showCreateForm = _useState2[0],
      setShowCreateForm = _useState2[1];
    var _useState3 = useState(null),
      _useState4 = _slicedToArray(_useState3, 2),
      editingTask = _useState4[0],
      setEditingTask = _useState4[1];
    var _useState5 = useState(null),
      _useState6 = _slicedToArray(_useState5, 2),
      viewingTask = _useState6[0],
      setViewingTask = _useState6[1];
    var _useState7 = useState(''),
      _useState8 = _slicedToArray(_useState7, 2),
      successMessage = _useState8[0],
      setSuccessMessage = _useState8[1];
    var _useState9 = useState({}),
      _useState10 = _slicedToArray(_useState9, 2),
      userEmails = _useState10[0],
      setUserEmails = _useState10[1];
    var _useState11 = useState({}),
      _useState12 = _slicedToArray(_useState11, 2),
      userNames = _useState12[0],
      setUserNames = _useState12[1];
    var _useState13 = useState(true),
      _useState14 = _slicedToArray(_useState13, 2),
      isLoadingUsers = _useState14[0],
      setIsLoadingUsers = _useState14[1];
    var _useState15 = useState(''),
      _useState16 = _slicedToArray(_useState15, 2),
      searchQuery = _useState16[0],
      setSearchQuery = _useState16[1];
    var _useState17 = useState('all'),
      _useState18 = _slicedToArray(_useState17, 2),
      filterStatus = _useState18[0],
      setFilterStatus = _useState18[1];
    var _useState19 = useState('all'),
      _useState20 = _slicedToArray(_useState19, 2),
      filterPriority = _useState20[0],
      setFilterPriority = _useState20[1];
    var _useState21 = useState('createdAt'),
      _useState22 = _slicedToArray(_useState21, 2),
      sortBy = _useState22[0],
      setSortBy = _useState22[1];
    var _useState23 = useState('desc'),
      _useState24 = _slicedToArray(_useState23, 2),
      sortOrder = _useState24[0],
      setSortOrder = _useState24[1];
    var _useParams = useParams(),
      taskId = _useParams.taskId;
    var navigate = useNavigate();
    var location = useLocation();
    var _useTracker2 = useTracker(function () {
        var tasksHandle = Meteor.subscribe('tasks');
        var userHandle = Meteor.subscribe('userData');
        var teamMembersHandle = Meteor.subscribe('teamMembers');
        var tasksReady = tasksHandle.ready();
        var userReady = userHandle.ready();
        var teamMembersReady = teamMembersHandle.ready();
        var user = Meteor.user();
        var tasks = Tasks.find({}, {
          sort: {
            createdAt: -1
          }
        }).fetch();
        var teamMembers = Meteor.users.find({
          $or: [{
            'roles': 'team-member'
          }, {
            'profile.role': 'team-member'
          }]
        }, {
          fields: {
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        var isLoading = !tasksReady || !userReady || !teamMembersReady;
        return {
          tasks: tasks,
          user: user,
          tasksReady: tasksReady,
          userReady: userReady,
          teamMembersReady: teamMembersReady,
          isLoading: isLoading,
          teamMembers: teamMembers
        };
      }, []),
      tasks = _useTracker2.tasks,
      isLoading = _useTracker2.isLoading,
      user = _useTracker2.user,
      tasksReady = _useTracker2.tasksReady,
      userReady = _useTracker2.userReady,
      teamMembers = _useTracker2.teamMembers;
    useEffect(function () {
      if (taskId) {
        var task = tasks.find(function (t) {
          return t._id === taskId;
        });
        if (task) {
          setViewingTask(task);
        } else {
          var _location$state;
          if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.from) {
            if (location.state.memberId) {
              var member = teamMembers.find(function (m) {
                return m._id === location.state.memberId;
              });
              if (member) {
                setSelectedMember(member);
              } else {
                navigate(location.state.from);
              }
            } else {
              navigate(location.state.from);
            }
          } else {
            navigate('/admin-dashboard/tasks');
          }
        }
      } else {
        setViewingTask(null);
      }
    }, [taskId, tasks, location.state, teamMembers, navigate]);
    useEffect(function () {
      var loadUserData = function () {
        function _callee2() {
          var userIds, userDataPromises, userEntries, userData;
          return _regeneratorRuntime.async(function () {
            function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  if (tasks) {
                    _context2.next = 2;
                    break;
                  }
                  return _context2.abrupt("return");
                case 2:
                  setIsLoadingUsers(true);
                  _context2.prev = 3;
                  userIds = _toConsumableArray(new Set(tasks.flatMap(function (task) {
                    return task.assignedTo;
                  })));
                  userDataPromises = userIds.map(function () {
                    function _callee(userId) {
                      var _user$profile2, _user$profile3, _user$emails, _user$emails$;
                      var user, firstName, lastName, fullName;
                      return _regeneratorRuntime.async(function () {
                        function _callee$(_context) {
                          while (1) switch (_context.prev = _context.next) {
                            case 0:
                              _context.next = 2;
                              return _regeneratorRuntime.awrap(Meteor.users.findOneAsync(userId));
                            case 2:
                              user = _context.sent;
                              if (user) {
                                _context.next = 5;
                                break;
                              }
                              return _context.abrupt("return", [userId, {
                                name: 'Unknown User',
                                email: 'Unknown'
                              }]);
                            case 5:
                              firstName = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.firstName) || '';
                              lastName = ((_user$profile3 = user.profile) === null || _user$profile3 === void 0 ? void 0 : _user$profile3.lastName) || '';
                              fullName = (firstName + " " + lastName).trim();
                              return _context.abrupt("return", [userId, {
                                email: ((_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address) || 'No email',
                                name: fullName || 'Unknown User'
                              }]);
                            case 9:
                            case "end":
                              return _context.stop();
                          }
                        }
                        return _callee$;
                      }(), null, null, null, Promise);
                    }
                    return _callee;
                  }());
                  _context2.next = 8;
                  return _regeneratorRuntime.awrap(Promise.all(userDataPromises));
                case 8:
                  userEntries = _context2.sent;
                  userData = Object.fromEntries(userEntries);
                  setUserEmails(Object.fromEntries(userEntries.map(function (_ref) {
                    var _ref2 = _slicedToArray(_ref, 2),
                      id = _ref2[0],
                      data = _ref2[1];
                    return [id, data.email];
                  })));
                  setUserNames(Object.fromEntries(userEntries.map(function (_ref3) {
                    var _ref4 = _slicedToArray(_ref3, 2),
                      id = _ref4[0],
                      data = _ref4[1];
                    return [id, data.name];
                  })));
                  _context2.next = 17;
                  break;
                case 14:
                  _context2.prev = 14;
                  _context2.t0 = _context2["catch"](3);
                  console.error('Error loading user data:', _context2.t0);
                case 17:
                  _context2.prev = 17;
                  setIsLoadingUsers(false);
                  return _context2.finish(17);
                case 20:
                case "end":
                  return _context2.stop();
              }
            }
            return _callee2$;
          }(), null, null, [[3, 14, 17, 20]], Promise);
        }
        return _callee2;
      }();
      loadUserData();
    }, [tasks]);
    var handleCreateTask = function () {
      setShowCreateForm(true);
      setEditingTask(null);
      setSuccessMessage('');
    };
    var handleEditTask = function (task) {
      setEditingTask(task);
      setViewingTask(null);
      setShowCreateForm(false);
    };
    var handleCancel = function () {
      setShowCreateForm(false);
      setEditingTask(null);
      setViewingTask(null);
      setSuccessMessage('Task saved successfully!');
      setTimeout(function () {
        return setSuccessMessage('');
      }, 3000);
    };
    var handleTaskUpdate = function () {
      function _callee3(taskId, updatedTask) {
        var taskToUpdate, result, _updatedTask;
        return _regeneratorRuntime.async(function () {
          function _callee3$(_context3) {
            while (1) switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                console.log('Received task update:', {
                  taskId: taskId,
                  updatedTask: _updatedTask
                });

                // Ensure all required fields are present
                taskToUpdate = _objectSpread(_objectSpread({}, _updatedTask), {}, {
                  title: _updatedTask.title.trim(),
                  description: _updatedTask.description.trim(),
                  assignedTo: _updatedTask.assignedTo || [],
                  checklist: _updatedTask.checklist || [],
                  labels: _updatedTask.labels || [],
                  category: _updatedTask.category || '',
                  progress: _updatedTask.progress || 0,
                  startDate: new Date(_updatedTask.startDate),
                  dueDate: new Date(_updatedTask.dueDate)
                });
                console.log('Sending task update to server:', taskToUpdate);

                // Call the server method
                _context3.next = 6;
                return _regeneratorRuntime.awrap(new Promise(function (resolve, reject) {
                  Meteor.call('tasks.update', taskId, taskToUpdate, function (error, result) {
                    if (error) {
                      console.error('Server error updating task:', error);
                      setSuccessMessage('Error updating task: ' + error.message);
                      reject(error);
                    } else {
                      console.log('Task update successful:', result);
                      setSuccessMessage('Task updated successfully!');
                      resolve(result);
                    }
                  });
                }));
              case 6:
                result = _context3.sent;
                // Force a refresh of the task data
                _updatedTask = Tasks.findOne(taskId);
                if (_updatedTask) {
                  if ((viewingTask === null || viewingTask === void 0 ? void 0 : viewingTask._id) === taskId) {
                    setViewingTask(_updatedTask);
                  }
                  if ((editingTask === null || editingTask === void 0 ? void 0 : editingTask._id) === taskId) {
                    setEditingTask(_updatedTask);
                  }
                }
                setEditingTask(null);
                setViewingTask(null);
                _context3.next = 17;
                break;
              case 13:
                _context3.prev = 13;
                _context3.t0 = _context3["catch"](0);
                console.error('Error updating task:', _context3.t0);
                setSuccessMessage('Error updating task: ' + _context3.t0.message);
              case 17:
                _context3.prev = 17;
                setTimeout(function () {
                  return setSuccessMessage('');
                }, 3000);
                return _context3.finish(17);
              case 20:
              case "end":
                return _context3.stop();
            }
          }
          return _callee3$;
        }(), null, null, [[0, 13, 17, 20]], Promise);
      }
      return _callee3;
    }();
    var handleDeleteTask = function () {
      function _callee4(taskId) {
        var task, confirmMessage;
        return _regeneratorRuntime.async(function () {
          function _callee4$(_context4) {
            while (1) switch (_context4.prev = _context4.next) {
              case 0:
                task = tasks.find(function (t) {
                  return t._id === taskId;
                });
                if (task) {
                  _context4.next = 3;
                  break;
                }
                return _context4.abrupt("return");
              case 3:
                confirmMessage = "Are you sure you want to delete the task \"" + task.title + "\"?\n\nThis action cannot be undone.";
                if (!window.confirm(confirmMessage)) {
                  _context4.next = 21;
                  break;
                }
                _context4.prev = 5;
                setSuccessMessage('Deleting task...');
                _context4.next = 9;
                return _regeneratorRuntime.awrap(new Promise(function (resolve, reject) {
                  Meteor.call('tasks.delete', taskId, function (error, result) {
                    if (error) {
                      console.error('Error deleting task:', error);
                      reject(error);
                    } else {
                      console.log('Task deleted successfully:', result);
                      resolve(result);
                    }
                  });
                }));
              case 9:
                setSuccessMessage('Task deleted successfully!');
                // Clear any active views
                setViewingTask(null);
                setEditingTask(null);
                _context4.next = 18;
                break;
              case 14:
                _context4.prev = 14;
                _context4.t0 = _context4["catch"](5);
                console.error('Error deleting task:', _context4.t0);
                setSuccessMessage("Error deleting task: " + _context4.t0.message);
              case 18:
                _context4.prev = 18;
                setTimeout(function () {
                  return setSuccessMessage('');
                }, 3000);
                return _context4.finish(18);
              case 21:
              case "end":
                return _context4.stop();
            }
          }
          return _callee4$;
        }(), null, null, [[5, 14, 18, 21]], Promise);
      }
      return _callee4;
    }();
    var filteredAndSortedTasks = useMemo(function () {
      if (!tasks) return [];
      var filtered = _toConsumableArray(tasks);

      // Apply search filter
      if (searchQuery) {
        var query = searchQuery.toLowerCase();
        filtered = filtered.filter(function (task) {
          return task.title.toLowerCase().includes(query) || task.description.toLowerCase().includes(query);
        });
      }

      // Apply status filter
      if (filterStatus !== 'all') {
        filtered = filtered.filter(function (task) {
          return task.status === filterStatus;
        });
      }

      // Apply priority filter
      if (filterPriority !== 'all') {
        filtered = filtered.filter(function (task) {
          return task.priority === filterPriority;
        });
      }

      // Apply sorting
      filtered.sort(function (a, b) {
        var comparison = 0;
        switch (sortBy) {
          case 'title':
            comparison = a.title.localeCompare(b.title);
            break;
          case 'dueDate':
            comparison = new Date(a.dueDate) - new Date(b.dueDate);
            break;
          case 'priority':
            var priorityOrder = {
              high: 3,
              medium: 2,
              low: 1
            };
            comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
            break;
          case 'status':
            comparison = a.status.localeCompare(b.status);
            break;
          default:
            comparison = new Date(b.createdAt) - new Date(a.createdAt);
        }
        return sortOrder === 'asc' ? comparison : -comparison;
      });
      return filtered;
    }, [tasks, searchQuery, filterStatus, filterPriority, sortBy, sortOrder]);

    // Subscribe to specific task updates when viewing or editing
    useEffect(function () {
      var handle;
      if (viewingTask) {
        handle = Meteor.subscribe('task', viewingTask._id);
      } else if (editingTask) {
        handle = Meteor.subscribe('task', editingTask._id);
      }
      return function () {
        var _handle;
        return (_handle = handle) === null || _handle === void 0 ? void 0 : _handle.stop();
      };
    }, [viewingTask === null || viewingTask === void 0 ? void 0 : viewingTask._id, editingTask === null || editingTask === void 0 ? void 0 : editingTask._id]);

    // Update viewingTask when the task data changes
    useEffect(function () {
      if (viewingTask) {
        var updatedTask = Tasks.findOne(viewingTask._id);
        if (updatedTask && JSON.stringify(updatedTask) !== JSON.stringify(viewingTask)) {
          console.log('Updating viewing task:', {
            old: viewingTask,
            "new": updatedTask
          });
          setViewingTask(updatedTask);
        }
      }
    }, [viewingTask === null || viewingTask === void 0 ? void 0 : viewingTask._id]);

    // Update editingTask when the task data changes
    useEffect(function () {
      if (editingTask) {
        var updatedTask = Tasks.findOne(editingTask._id);
        if (updatedTask && JSON.stringify(updatedTask) !== JSON.stringify(editingTask)) {
          console.log('Updating editing task:', {
            old: editingTask,
            "new": updatedTask
          });
          setEditingTask(updatedTask);
        }
      }
    }, [editingTask === null || editingTask === void 0 ? void 0 : editingTask._id]);
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }
    if (viewingTask) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'row',
          gap: '32px',
          padding: '24px',
          background: '#ffffff',
          minHeight: 'calc(100vh - 64px)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          flex: 2
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        onClick: function () {
          var _location$state2;
          if ((_location$state2 = location.state) !== null && _location$state2 !== void 0 && _location$state2.from) {
            if (location.state.memberId) {
              navigate("/admin-dashboard/team/" + location.state.memberId);
            } else {
              navigate(location.state.from);
            }
          } else {
            navigate('/admin-dashboard/tasks');
          }
        },
        style: {
          background: 'none',
          border: 'none',
          cursor: 'pointer',
          fontSize: '1.8rem',
          color: '#64748b',
          transition: 'color 0.2s ease',
          marginRight: '12px',
          padding: 0,
          display: 'flex',
          alignItems: 'center'
        },
        onMouseOver: function (e) {
          return e.currentTarget.style.color = '#334155';
        },
        onMouseOut: function (e) {
          return e.currentTarget.style.color = '#64748b';
        },
        "aria-label": "Back"
      }, "\u2190"), /*#__PURE__*/React.createElement("h2", {
        style: {
          fontSize: '1.5rem',
          fontWeight: '600',
          color: '#0f172a',
          margin: '0',
          wordBreak: 'break-word',
          display: 'inline-block'
        }
      }, viewingTask.title)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexWrap: 'wrap',
          gap: '12px',
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: viewingTask.status === 'completed' ? '#dcfce7' : viewingTask.status === 'in-progress' ? '#fef3c7' : '#fee2e2',
          color: viewingTask.status === 'completed' ? '#16a34a' : viewingTask.status === 'in-progress' ? '#d97706' : '#dc2626'
        }
      }, viewingTask.status === 'in-progress' ? 'In Progress' : viewingTask.status.charAt(0).toUpperCase() + viewingTask.status.slice(1)), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: viewingTask.priority === 'high' ? '#fee2e2' : viewingTask.priority === 'medium' ? '#fef3c7' : '#dcfce7',
          color: viewingTask.priority === 'high' ? '#dc2626' : viewingTask.priority === 'medium' ? '#d97706' : '#16a34a'
        }
      }, viewingTask.priority.charAt(0).toUpperCase() + viewingTask.priority.slice(1)), viewingTask.dueDate && /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCC5"), "Due: ", new Date(viewingTask.dueDate).toLocaleDateString()), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '0.875rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569',
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCCA"), "Progress: ", viewingTask.progress || 0, "%"), viewingTask.assignedTo && viewingTask.assignedTo.length > 0 && /*#__PURE__*/React.createElement("span", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          fontWeight: '500'
        }
      }, "Team:"), viewingTask.assignedTo.map(function (userId) {
        return /*#__PURE__*/React.createElement("span", {
          key: userId,
          style: {
            padding: '4px 12px',
            borderRadius: '6px',
            fontSize: '0.875rem',
            backgroundColor: '#f1f5f9',
            color: '#475569',
            fontWeight: '500'
          }
        }, userNames[userId] || 'Unknown User');
      }))), /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '8px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Description"), /*#__PURE__*/React.createElement("p", {
        style: {
          color: '#475569',
          whiteSpace: 'pre-line',
          lineHeight: '1.6'
        }
      }, viewingTask.description || 'No description provided.')), viewingTask.checklist && viewingTask.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '16px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Checklist"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, viewingTask.checklist.map(function (item, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            backgroundColor: '#f8fafc',
            padding: '12px',
            borderRadius: '8px',
            border: '1px solid #e2e8f0',
            color: item.completed ? '#64748b' : '#0f172a',
            textDecoration: item.completed ? 'line-through' : 'none',
            fontSize: '1rem'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: item.completed,
          disabled: true,
          style: {
            width: '20px',
            height: '20px',
            accentColor: '#3b82f6',
            cursor: 'pointer'
          }
        }), /*#__PURE__*/React.createElement("span", null, item.text));
      }))), viewingTask.attachments && viewingTask.attachments.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '16px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Attachments"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, viewingTask.attachments.map(function (attachment, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          style: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '12px',
            backgroundColor: '#f8fafc',
            borderRadius: '8px',
            border: '1px solid #e2e8f0',
            wordBreak: 'break-word'
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            flex: 1,
            minWidth: 0
          }
        }, /*#__PURE__*/React.createElement("span", {
          style: {
            fontSize: '1.5rem',
            color: '#64748b'
          }
        }, "\uD83D\uDCCE"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.9rem',
            color: '#0f172a',
            fontWeight: '500'
          }
        }, attachment.name), /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.75rem',
            color: '#64748b'
          }
        }, "Uploaded by ", userNames[attachment.uploadedBy] || 'Unknown User', " on ", new Date(attachment.uploadedAt).toLocaleDateString()))), /*#__PURE__*/React.createElement("button", {
          onClick: function () {
            try {
              var base64Data = attachment.data;
              if (base64Data.startsWith('data:')) {
                base64Data = base64Data.split(',')[1];
              }
              var byteCharacters = atob(base64Data);
              var byteNumbers = new Array(byteCharacters.length);
              for (var i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
              }
              var byteArray = new Uint8Array(byteNumbers);
              var mimeType = attachment.type || 'application/octet-stream';
              var blob = new Blob([byteArray], {
                type: mimeType
              });
              var url = window.URL.createObjectURL(blob);
              var link = document.createElement('a');
              link.href = url;
              link.download = attachment.name;
              document.body.appendChild(link);
              link.click();
              setTimeout(function () {
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
              }, 100);
            } catch (error) {
              console.error('Error downloading file:', error);
              alert('Failed to download file. Please try again.');
            }
          },
          style: {
            padding: '8px 16px',
            backgroundColor: '#e2e8f0',
            border: 'none',
            borderRadius: '6px',
            color: '#0f172a',
            fontSize: '0.875rem',
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          },
          onMouseOver: function (e) {
            return e.currentTarget.style.backgroundColor = '#cbd5e1';
          },
          onMouseOut: function (e) {
            return e.currentTarget.style.backgroundColor = '#e2e8f0';
          }
        }, "Download"));
      }))), viewingTask.links && viewingTask.links.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          color: '#334155',
          marginBottom: '16px',
          fontSize: '1.2rem',
          fontWeight: '600'
        }
      }, "Links"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, viewingTask.links.map(function (link, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          style: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '12px',
            backgroundColor: '#f8fafc',
            borderRadius: '8px',
            border: '1px solid #e2e8f0',
            wordBreak: 'break-word'
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            flex: 1,
            minWidth: 0
          }
        }, /*#__PURE__*/React.createElement("span", {
          style: {
            fontSize: '1.5rem',
            color: '#64748b'
          }
        }, "\uD83D\uDD17"), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("a", {
          href: link.url,
          target: "_blank",
          rel: "noopener noreferrer",
          style: {
            fontSize: '0.9rem',
            color: '#2563eb',
            textDecoration: 'none',
            fontWeight: '500'
          },
          onMouseOver: function (e) {
            return e.currentTarget.style.textDecoration = 'underline';
          },
          onMouseOut: function (e) {
            return e.currentTarget.style.textDecoration = 'none';
          }
        }, link.url), /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: '0.75rem',
            color: '#64748b'
          }
        }, "Added by ", userNames[link.addedBy] || 'Unknown User', " on ", new Date(link.addedAt).toLocaleDateString()))));
      }))))), /*#__PURE__*/React.createElement("div", {
        style: {
          flex: 1,
          minWidth: 350,
          maxWidth: 500
        }
      }, /*#__PURE__*/React.createElement(AIInsightsPanel, {
        taskId: viewingTask._id,
        task: viewingTask
      })));
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        marginBottom: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Tasks Management"), /*#__PURE__*/React.createElement("button", {
      onClick: handleCreateTask,
      className: "btn btn-primary"
    }, "Create New Task")), successMessage && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        color: '#15803d',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, successMessage), showCreateForm ? /*#__PURE__*/React.createElement(CreateTask, {
      onCancel: handleCancel
    }) : editingTask ? /*#__PURE__*/React.createElement(EditTask, {
      task: editingTask,
      onCancel: handleCancel,
      onUpdate: handleTaskUpdate
    }) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search tasks...",
      value: searchQuery,
      onChange: function (e) {
        return setSearchQuery(e.target.value);
      },
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    })), /*#__PURE__*/React.createElement("select", {
      value: filterStatus,
      onChange: function (e) {
        return setFilterStatus(e.target.value);
      },
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '120px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Status"), /*#__PURE__*/React.createElement("option", {
      value: "pending"
    }, "Pending"), /*#__PURE__*/React.createElement("option", {
      value: "in-progress"
    }, "In Progress"), /*#__PURE__*/React.createElement("option", {
      value: "completed"
    }, "Completed")), /*#__PURE__*/React.createElement("select", {
      value: filterPriority,
      onChange: function (e) {
        return setFilterPriority(e.target.value);
      },
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '120px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Priority"), /*#__PURE__*/React.createElement("option", {
      value: "high"
    }, "High"), /*#__PURE__*/React.createElement("option", {
      value: "medium"
    }, "Medium"), /*#__PURE__*/React.createElement("option", {
      value: "low"
    }, "Low")), /*#__PURE__*/React.createElement("select", {
      value: sortBy + "-" + sortOrder,
      onChange: function (e) {
        var _e$target$value$split = e.target.value.split('-'),
          _e$target$value$split2 = _slicedToArray(_e$target$value$split, 2),
          newSortBy = _e$target$value$split2[0],
          newSortOrder = _e$target$value$split2[1];
        setSortBy(newSortBy);
        setSortOrder(newSortOrder);
      },
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '150px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "createdAt-desc"
    }, "Newest First"), /*#__PURE__*/React.createElement("option", {
      value: "createdAt-asc"
    }, "Oldest First"), /*#__PURE__*/React.createElement("option", {
      value: "dueDate-asc"
    }, "Due Date (Earliest)"), /*#__PURE__*/React.createElement("option", {
      value: "dueDate-desc"
    }, "Due Date (Latest)"), /*#__PURE__*/React.createElement("option", {
      value: "priority-desc"
    }, "Priority (High to Low)"), /*#__PURE__*/React.createElement("option", {
      value: "priority-asc"
    }, "Priority (Low to High)"), /*#__PURE__*/React.createElement("option", {
      value: "title-asc"
    }, "Title (A-Z)"), /*#__PURE__*/React.createElement("option", {
      value: "title-desc"
    }, "Title (Z-A)"))), /*#__PURE__*/React.createElement("div", {
      className: "tasks-grid"
    }, isLoading ? /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "loading-spinner"
    })) : filteredAndSortedTasks.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px',
        padding: '16px 0'
      }
    }, filteredAndSortedTasks.map(function (task) {
      return /*#__PURE__*/React.createElement("div", {
        key: task._id,
        onClick: function () {
          return navigate("/admin-dashboard/tasks/" + task._id);
        },
        style: {
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          padding: '20px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e2e8f0',
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          ':hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          margin: 0,
          fontSize: '1.1rem',
          fontWeight: '600',
          color: '#0f172a'
        }
      }, task.title), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef3c7' : '#dcfce7',
          color: task.priority === 'high' ? '#dc2626' : task.priority === 'medium' ? '#d97706' : '#16a34a'
        }
      }, task.priority), task.category && /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569'
        }
      }, task.category))), /*#__PURE__*/React.createElement("p", {
        style: {
          margin: 0,
          fontSize: '0.875rem',
          color: '#475569',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxHeight: '2.6em'
        }
      }, task.description), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Status:"), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '2px 8px',
          borderRadius: '4px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in-progress' ? '#fef3c7' : '#fee2e2',
          color: task.status === 'completed' ? '#16a34a' : task.status === 'in-progress' ? '#d97706' : '#dc2626'
        }
      }, task.status)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Progress:"), /*#__PURE__*/React.createElement("div", {
        style: {
          flex: 1,
          height: '6px',
          backgroundColor: '#e2e8f0',
          borderRadius: '3px',
          overflow: 'hidden'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          width: task.progress + "%",
          height: '100%',
          backgroundColor: task.progress === 100 ? '#16a34a' : task.progress > 50 ? '#3b82f6' : task.progress > 0 ? '#f59e0b' : '#dc2626',
          transition: 'width 0.3s ease'
        }
      })), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.75rem',
          color: '#475569',
          minWidth: '40px'
        }
      }, task.progress, "%")), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Due:"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#0f172a'
        }
      }, new Date(task.dueDate).toLocaleDateString())), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'flex-start',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Assigned to:"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexWrap: 'wrap',
          gap: '4px'
        }
      }, isLoadingUsers ? /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.75rem',
          padding: '2px 6px',
          borderRadius: '4px',
          backgroundColor: '#f1f5f9',
          color: '#475569'
        }
      }, "Loading...") : task.assignedTo.length === 0 ? /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.75rem',
          padding: '2px 6px',
          borderRadius: '4px',
          backgroundColor: '#f1f5f9',
          color: '#475569'
        }
      }, "Unassigned") : /*#__PURE__*/React.createElement(React.Fragment, null, task.assignedTo.slice(0, 2).map(function (userId) {
        return /*#__PURE__*/React.createElement("span", {
          key: userId,
          style: {
            fontSize: '0.75rem',
            padding: '2px 6px',
            borderRadius: '4px',
            backgroundColor: '#f1f5f9',
            color: '#475569'
          }
        }, userNames[userId] || 'Unknown User');
      }), task.assignedTo.length > 2 && /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.75rem',
          padding: '2px 6px',
          borderRadius: '4px',
          backgroundColor: '#e2e8f0',
          color: '#475569',
          cursor: 'pointer'
        },
        onClick: function (e) {
          e.stopPropagation();
          var remainingMembers = task.assignedTo.slice(2).map(function (userId) {
            return userNames[userId] || 'Unknown User';
          });
          alert("Additional team members:\n" + remainingMembers.join('\n'));
        }
      }, "+", task.assignedTo.length - 2, " more"))))), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: '8px',
          padding: '8px',
          backgroundColor: '#f8fafc',
          borderRadius: '6px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '4px'
        }
      }, "Checklist (", task.checklist.filter(function (item) {
        return item.completed;
      }).length, "/", task.checklist.length, ")"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '4px'
        }
      }, task.checklist.slice(0, 3).map(function (item, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '0.875rem',
            color: item.completed ? '#64748b' : '#0f172a',
            textDecoration: item.completed ? 'line-through' : 'none'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: item.completed,
          disabled: true,
          style: {
            width: '16px',
            height: '16px',
            borderRadius: '4px',
            border: '1px solid #e2e8f0'
          }
        }), /*#__PURE__*/React.createElement("span", {
          style: {
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: '200px'
          }
        }, item.text));
      }), task.checklist.length > 3 && /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: '0.75rem',
          color: '#64748b',
          textAlign: 'center',
          marginTop: '4px'
        }
      }, "+", task.checklist.length - 3, " more items..."))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '8px',
          marginTop: 'auto',
          paddingTop: '12px',
          borderTop: '1px solid #e2e8f0'
        }
      }, /*#__PURE__*/React.createElement("button", {
        className: "btn btn-secondary",
        style: {
          padding: '6px 12px',
          fontSize: '0.875rem'
        },
        onClick: function (e) {
          e.stopPropagation();
          handleEditTask(task);
        }
      }, "Edit"), /*#__PURE__*/React.createElement("button", {
        className: "btn btn-secondary",
        style: {
          padding: '6px 12px',
          fontSize: '0.875rem'
        },
        onClick: function (e) {
          e.stopPropagation();
          handleDeleteTask(task._id);
        }
      }, "Delete")));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        textAlign: 'center',
        color: '#475569'
      }
    }, searchQuery || filterStatus !== 'all' || filterPriority !== 'all' ? 'No tasks match your filters' : 'No tasks found. Create your first task!')))));
  };
  _s2(TasksPage, "haIMCGJnOXw241pK4MoXfftCTNs=", false, function () {
    return [useParams, useNavigate, useLocation, useTracker];
  });
  _c2 = TasksPage;
  var TeamPage = function () {
    _s3();
    var _useState25 = useState(null),
      _useState26 = _slicedToArray(_useState25, 2),
      selectedMember = _useState26[0],
      setSelectedMember = _useState26[1];
    var _useState27 = useState(''),
      _useState28 = _slicedToArray(_useState27, 2),
      successMessage = _useState28[0],
      setSuccessMessage = _useState28[1];
    var _useState29 = useState(''),
      _useState30 = _slicedToArray(_useState29, 2),
      searchQuery = _useState30[0],
      setSearchQuery = _useState30[1];
    var _useState31 = useState(0),
      _useState32 = _slicedToArray(_useState31, 2),
      refreshKey = _useState32[0],
      setRefreshKey = _useState32[1];
    var _useParams2 = useParams(),
      memberId = _useParams2.memberId;
    var navigate = useNavigate();
    var location = useLocation();
    var _useTracker3 = useTracker(function () {
        var handle = Meteor.subscribe('teamMembers');
        var tasksHandle = Meteor.subscribe('tasks');
        var isLoading = !handle.ready() || !tasksHandle.ready();
        var members = Meteor.users.find({
          $or: [{
            'roles': 'team-member'
          }, {
            'profile.role': 'team-member'
          }]
        }, {
          fields: {
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          },
          sort: {
            'profile.firstName': 1,
            'profile.lastName': 1
          }
        }).fetch();
        var tasks = Tasks.find().fetch();
        return {
          teamMembers: members,
          tasks: tasks,
          isLoading: isLoading
        };
      }, [refreshKey]),
      teamMembers = _useTracker3.teamMembers,
      tasks = _useTracker3.tasks,
      isLoading = _useTracker3.isLoading;

    // Set selected member when memberId changes
    useEffect(function () {
      if (memberId && teamMembers) {
        var member = teamMembers.find(function (m) {
          return m._id === memberId;
        });
        if (member) {
          setSelectedMember(member);
        }
      } else {
        setSelectedMember(null);
      }
    }, [memberId, teamMembers]);
    var handleMemberClick = function (member) {
      setSelectedMember(member);
    };
    var handleBackToList = function () {
      setSelectedMember(null);
    };
    if (selectedMember) {
      var _selectedMember$profi, _selectedMember$profi2, _selectedMember$email, _selectedMember$email2, _selectedMember$profi3;
      return /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '24px',
          background: '#ffffff',
          minHeight: 'calc(100vh - 64px)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          padding: '24px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }
      }, /*#__PURE__*/React.createElement("button", {
        onClick: function () {
          var _location$state3;
          if ((_location$state3 = location.state) !== null && _location$state3 !== void 0 && _location$state3.from) {
            navigate(location.state.from);
          } else {
            setSelectedMember(null);
            navigate('/admin-dashboard/team');
          }
        },
        style: {
          background: 'none',
          border: 'none',
          cursor: 'pointer',
          fontSize: '1.8rem',
          color: '#64748b',
          transition: 'color 0.2s ease',
          marginRight: '12px',
          padding: 0,
          display: 'flex',
          alignItems: 'center'
        },
        onMouseOver: function (e) {
          return e.currentTarget.style.color = '#334155';
        },
        onMouseOut: function (e) {
          return e.currentTarget.style.color = '#64748b';
        },
        "aria-label": "Back"
      }, "\u2190"), /*#__PURE__*/React.createElement("h2", {
        style: {
          color: '#0f172a',
          margin: 0,
          fontSize: '1.5rem',
          fontWeight: '600',
          display: 'inline-block',
          wordBreak: 'break-word'
        }
      }, (_selectedMember$profi = selectedMember.profile) === null || _selectedMember$profi === void 0 ? void 0 : _selectedMember$profi.firstName, " ", (_selectedMember$profi2 = selectedMember.profile) === null || _selectedMember$profi2 === void 0 ? void 0 : _selectedMember$profi2.lastName)), /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '24px',
          padding: '24px',
          backgroundColor: '#f8fafc',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '16px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Email"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, (_selectedMember$email = selectedMember.emails) === null || _selectedMember$email === void 0 ? void 0 : (_selectedMember$email2 = _selectedMember$email[0]) === null || _selectedMember$email2 === void 0 ? void 0 : _selectedMember$email2.address)), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '16px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Role"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, ((_selectedMember$profi3 = selectedMember.profile) === null || _selectedMember$profi3 === void 0 ? void 0 : _selectedMember$profi3.role) || 'Team Member')), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '16px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Joined"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '500'
        }
      }, selectedMember.createdAt ? new Date(selectedMember.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }) : 'Not specified'))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '20px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          marginBottom: '16px',
          fontWeight: '600'
        }
      }, "Task Status"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Completed"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#dcfce7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(function (task) {
        return task.status === 'completed' && task.assignedTo.includes(selectedMember._id);
      }).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "In Progress"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fef3c7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(function (task) {
        return task.status === 'in-progress' && task.assignedTo.includes(selectedMember._id);
      }).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Pending"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fee2e2',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(function (task) {
        return task.status === 'pending' && task.assignedTo.includes(selectedMember._id);
      }).length)))), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '20px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          marginBottom: '16px',
          fontWeight: '600'
        }
      }, "Task Priority"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "High Priority"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fee2e2',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(function (task) {
        return task.priority === 'high' && task.assignedTo.includes(selectedMember._id);
      }).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Medium Priority"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#fef3c7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(function (task) {
        return task.priority === 'medium' && task.assignedTo.includes(selectedMember._id);
      }).length)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Low Priority"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#dcfce7',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, tasks.filter(function (task) {
        return task.priority === 'low' && task.assignedTo.includes(selectedMember._id);
      }).length)))), /*#__PURE__*/React.createElement("div", {
        style: {
          padding: '20px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
      }, /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          marginBottom: '16px',
          fontWeight: '600'
        }
      }, "Performance"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Completion Rate"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#f1f5f9',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, function () {
        var assignedTasks = tasks.filter(function (task) {
          return task.assignedTo.includes(selectedMember._id);
        });
        if (assignedTasks.length === 0) return '0.0%';
        var completedTasks = assignedTasks.filter(function (task) {
          return task.status === 'completed';
        }).length;
        return (completedTasks / assignedTasks.length * 100).toFixed(1) + "%";
      }())), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "On-Time Rate"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#f1f5f9',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, function () {
        var assignedTasks = tasks.filter(function (task) {
          return task.assignedTo.includes(selectedMember._id);
        });
        if (assignedTasks.length === 0) return '0.0%';
        var onTimeTasks = assignedTasks.filter(function (task) {
          if (task.status !== 'completed') return false;
          return new Date(task.completedAt) <= new Date(task.dueDate);
        }).length;
        return (onTimeTasks / assignedTasks.length * 100).toFixed(1) + "%";
      }())), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Average Progress"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '1rem',
          color: '#0f172a',
          fontWeight: '600',
          backgroundColor: '#f1f5f9',
          padding: '4px 12px',
          borderRadius: '6px'
        }
      }, function () {
        var assignedTasks = tasks.filter(function (task) {
          return task.assignedTo.includes(selectedMember._id);
        });
        if (assignedTasks.length === 0) return '0.0%';
        var totalProgress = assignedTasks.reduce(function (sum, task) {
          return sum + (task.progress || 0);
        }, 0);
        return (totalProgress / assignedTasks.length).toFixed(1) + "%";
      }()))))))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement(TaskAssignmentHistory, {
        memberId: selectedMember._id
      }))));
    }
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Team Management")), successMessage && /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        color: '#15803d',
        padding: '12px 16px',
        borderRadius: '8px',
        marginBottom: '16px'
      }
    }, successMessage), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search team members...",
      value: searchQuery,
      onChange: function (e) {
        return setSearchQuery(e.target.value);
      },
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    }))), isLoading ? /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "loading-spinner"
    })) : teamMembers.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px'
      }
    }, teamMembers.map(function (member) {
      var _member$profile, _member$profile2, _member$emails, _member$emails$, _member$profile3, _member$profile4, _member$profile5;
      return /*#__PURE__*/React.createElement("div", {
        key: member._id,
        onClick: function () {
          return handleMemberClick(member);
        },
        style: {
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          padding: '20px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e2e8f0',
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          ':hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
        style: {
          margin: 0,
          fontSize: '1.1rem',
          fontWeight: '600',
          color: '#0f172a'
        }
      }, (_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName, " ", (_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName), /*#__PURE__*/React.createElement("p", {
        style: {
          margin: '4px 0 0',
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, (_member$emails = member.emails) === null || _member$emails === void 0 ? void 0 : (_member$emails$ = _member$emails[0]) === null || _member$emails$ === void 0 ? void 0 : _member$emails$.address)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          gap: '8px',
          alignItems: 'center'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569'
        }
      }, ((_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : _member$profile3.role) || 'Team Member'))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }
      }, ((_member$profile4 = member.profile) === null || _member$profile4 === void 0 ? void 0 : _member$profile4.department) && /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Department:"), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#0f172a'
        }
      }, member.profile.department)), ((_member$profile5 = member.profile) === null || _member$profile5 === void 0 ? void 0 : _member$profile5.skills) && member.profile.skills.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          flexWrap: 'wrap'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, "Skills:"), member.profile.skills.map(function (skill, index) {
        return /*#__PURE__*/React.createElement("span", {
          key: index,
          style: {
            fontSize: '0.75rem',
            padding: '2px 6px',
            borderRadius: '4px',
            backgroundColor: '#e2e8f0',
            color: '#475569'
          }
        }, skill);
      }))));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        textAlign: 'center',
        color: '#475569'
      }
    }, searchQuery ? 'No team members match your search' : 'No team members found.')));
  };
  _s3(TeamPage, "lKdXwm6kORrbMJTCCSZUPsDTM6s=", false, function () {
    return [useParams, useNavigate, useLocation, useTracker];
  });
  _c3 = TeamPage;
  var AdminDashboard = function () {
    _s4();
    var navigate = useNavigate();
    var handleLogout = function () {
      Meteor.logout(function (err) {
        if (err) {
          console.error('Logout error:', err);
        } else {
          navigate('/login');
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        minHeight: '100vh',
        backgroundColor: '#ffffff'
      }
    }, /*#__PURE__*/React.createElement(Header, {
      userRole: "admin"
    }), /*#__PURE__*/React.createElement(Routes, null, /*#__PURE__*/React.createElement(Route, {
      path: "/",
      element: /*#__PURE__*/React.createElement(AdminDashboardHome, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks",
      element: /*#__PURE__*/React.createElement(TasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks/:taskId",
      element: /*#__PURE__*/React.createElement(TasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team",
      element: /*#__PURE__*/React.createElement(TeamPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team/:memberId",
      element: /*#__PURE__*/React.createElement(TeamPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/reports",
      element: /*#__PURE__*/React.createElement(ReportsPage, null)
    })));
  };
  _s4(AdminDashboard, "CzcTeTziyjMsSrAVmHuCCb6+Bfg=", false, function () {
    return [useNavigate];
  });
  _c4 = AdminDashboard;
  var _c, _c2, _c3, _c4;
  $RefreshReg$(_c, "AdminDashboardHome");
  $RefreshReg$(_c2, "TasksPage");
  $RefreshReg$(_c3, "TeamPage");
  $RefreshReg$(_c4, "AdminDashboard");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"ForgotPasswordPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/ForgotPasswordPage.jsx                                                                             //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 0);
  module1.export({
    ForgotPasswordPage: function () {
      return ForgotPasswordPage;
    }
  });
  var React, useState;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var Link, useNavigate;
  module1.link("react-router-dom", {
    Link: function (v) {
      Link = v;
    },
    useNavigate: function (v) {
      useNavigate = v;
    }
  }, 2);
  var Formik, Form, Field;
  module1.link("formik", {
    Formik: function (v) {
      Formik = v;
    },
    Form: function (v) {
      Form = v;
    },
    Field: function (v) {
      Field = v;
    }
  }, 3);
  var Yup;
  module1.link("yup", {
    "*": function (v) {
      Yup = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var ForgotPasswordSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email address').required('Email is required'),
    newPassword: Yup.string().required('New password is required').min(8, 'Password must be at least 8 characters').matches(/[A-Z]/, 'Password must contain at least one uppercase letter').matches(/[0-9]/, 'Password must contain at least one number').matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),
    confirmPassword: Yup.string().oneOf([Yup.ref('newPassword'), null], 'Passwords must match').required('Confirm password is required')
  });
  var ForgotPasswordPage = function () {
    _s();
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      error = _useState2[0],
      setError = _useState2[1];
    var _useState3 = useState(false),
      _useState4 = _slicedToArray(_useState3, 2),
      success = _useState4[0],
      setSuccess = _useState4[1];
    var _useState5 = useState(false),
      _useState6 = _slicedToArray(_useState5, 2),
      showPassword = _useState6[0],
      setShowPassword = _useState6[1];
    var _useState7 = useState(false),
      _useState8 = _slicedToArray(_useState7, 2),
      showConfirmPassword = _useState8[0],
      setShowConfirmPassword = _useState8[1];
    var _useState9 = useState(null),
      _useState10 = _slicedToArray(_useState9, 2),
      testResult = _useState10[0],
      setTestResult = _useState10[1];
    var navigate = useNavigate();
    var handleSubmit = function (values, _ref) {
      var setSubmitting = _ref.setSubmitting;
      var email = values.email,
        newPassword = values.newPassword;
      setError('');
      setSuccess(false);
      Meteor.call('users.forgotPassword', {
        email: email,
        newPassword: newPassword
      }, function (err, result) {
        setSubmitting(false);
        if (err) {
          console.error('Forgot password error:', err);
          setError(err.reason || 'Failed to reset password. Please try again.');
        } else {
          setSuccess(true);
          console.log('Password reset successful:', result);
          // Redirect to login page after 2 seconds
          setTimeout(function () {
            navigate('/login');
          }, 2000);
        }
      });
    };
    var testLogin = function (email, password) {
      Meteor.call('users.testLogin', {
        email: email,
        password: password
      }, function (err, result) {
        if (err) {
          console.error('Test login error:', err);
          setTestResult({
            success: false,
            error: err.reason
          });
        } else {
          console.log('Test login result:', result);
          setTestResult(result);
        }
      });
    };
    var debugUser = function (email) {
      Meteor.call('users.debugUser', {
        email: email
      }, function (err, result) {
        if (err) {
          console.error('Debug user error:', err);
          setTestResult({
            success: false,
            error: err.reason
          });
        } else {
          console.log('Debug user result:', result);
          setTestResult(result);
        }
      });
    };
    var comparePasswordFormats = function (email) {
      Meteor.call('users.comparePasswordFormats', {
        email: email
      }, function (err, result) {
        if (err) {
          console.error('Compare password formats error:', err);
          setTestResult({
            success: false,
            error: err.reason
          });
        } else {
          console.log('Compare password formats result:', result);
          setTestResult(result);
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      className: "auth-container"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-card"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-header"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-logo"
    }, /*#__PURE__*/React.createElement("svg", {
      width: "48",
      height: "48",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/React.createElement("path", {
      d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }))), /*#__PURE__*/React.createElement("h1", {
      className: "auth-title"
    }, "Reset Password"), /*#__PURE__*/React.createElement("p", {
      className: "auth-subtitle"
    }, "Enter your email and new password to reset your account password.")), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        email: '',
        newPassword: '',
        confirmPassword: ''
      },
      validationSchema: ForgotPasswordSchema,
      onSubmit: handleSubmit
    }, function (_ref2) {
      var errors = _ref2.errors,
        touched = _ref2.touched,
        isSubmitting = _ref2.isSubmitting;
      return /*#__PURE__*/React.createElement(Form, {
        className: "auth-form"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "form-label"
      }, "Email Address"), /*#__PURE__*/React.createElement(Field, {
        id: "email",
        name: "email",
        type: "email",
        placeholder: "Enter your email address",
        className: "form-control " + (errors.email && touched.email ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "newPassword",
        className: "form-label"
      }, "New Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "newPassword",
        name: "newPassword",
        type: showPassword ? 'text' : 'password',
        placeholder: "Enter new password",
        className: "form-control " + (errors.newPassword && touched.newPassword ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showPassword ? 'Hide password' : 'Show password',
        onClick: function () {
          return setShowPassword(function (v) {
            return !v;
          });
        }
      }, showPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.newPassword && touched.newPassword && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, errors.newPassword)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "confirmPassword",
        className: "form-label"
      }, "Confirm New Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "confirmPassword",
        name: "confirmPassword",
        type: showConfirmPassword ? 'text' : 'password',
        placeholder: "Confirm new password",
        className: "form-control " + (errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showConfirmPassword ? 'Hide password' : 'Show password',
        onClick: function () {
          return setShowConfirmPassword(function (v) {
            return !v;
          });
        }
      }, showConfirmPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.confirmPassword && touched.confirmPassword && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, errors.confirmPassword)), error && /*#__PURE__*/React.createElement("div", {
        className: "error-message error-message-global"
      }, error), success && /*#__PURE__*/React.createElement("div", {
        className: "success-message"
      }, "Password reset successful! Redirecting to login..."), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "btn btn-primary btn-auth",
        disabled: isSubmitting
      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
        className: "loading-spinner"
      }), "Resetting...") : 'Reset Password'), /*#__PURE__*/React.createElement("div", {
        className: "auth-links-row"
      }, /*#__PURE__*/React.createElement(Link, {
        to: "/login",
        className: "auth-link"
      }, "Back to login")));
    }), /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: '20px',
        padding: '10px',
        backgroundColor: '#f5f5f5',
        borderRadius: '5px'
      }
    }, /*#__PURE__*/React.createElement("h4", null, "Debug: Test Login"), /*#__PURE__*/React.createElement("button", {
      onClick: function () {
        var email = document.querySelector('input[name="email"]').value;
        var password = document.querySelector('input[name="newPassword"]').value;
        if (email && password) {
          testLogin(email, password);
        }
      },
      style: {
        padding: '5px 10px',
        margin: '5px'
      }
    }, "Test Password"), /*#__PURE__*/React.createElement("button", {
      onClick: function () {
        var email = document.querySelector('input[name="email"]').value;
        if (email) {
          debugUser(email);
        }
      },
      style: {
        padding: '5px 10px',
        margin: '5px'
      }
    }, "Debug User"), /*#__PURE__*/React.createElement("button", {
      onClick: function () {
        var email = document.querySelector('input[name="email"]').value;
        if (email) {
          comparePasswordFormats(email);
        }
      },
      style: {
        padding: '5px 10px',
        margin: '5px'
      }
    }, "Check Password Format"), testResult && /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: '10px',
        fontSize: '12px'
      }
    }, /*#__PURE__*/React.createElement("strong", null, "Test Result:"), " ", /*#__PURE__*/React.createElement("pre", null, JSON.stringify(testResult, null, 2))))));
  };
  _s(ForgotPasswordPage, "8EB6Z3j5g6KSDRJNJGrssmxWY/k=", false, function () {
    return [useNavigate];
  });
  _c = ForgotPasswordPage;
  var _c;
  $RefreshReg$(_c, "ForgotPasswordPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"LoginPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/LoginPage.jsx                                                                                      //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 0);
  module1.export({
    LoginPage: function () {
      return LoginPage;
    }
  });
  var React, useState, useEffect;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useNavigate, Link;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    },
    Link: function (v) {
      Link = v;
    }
  }, 2);
  var Formik, Form, Field;
  module1.link("formik", {
    Formik: function (v) {
      Formik = v;
    },
    Form: function (v) {
      Form = v;
    },
    Field: function (v) {
      Field = v;
    }
  }, 3);
  var Yup;
  module1.link("yup", {
    "*": function (v) {
      Yup = v;
    }
  }, 4);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var LoginSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email address').required('Email is required'),
    password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required')
  });
  var LoginPage = function () {
    _s();
    var navigate = useNavigate();
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      error = _useState2[0],
      setError = _useState2[1];
    var _useState3 = useState(false),
      _useState4 = _slicedToArray(_useState3, 2),
      resendEmailSent = _useState4[0],
      setResendEmailSent = _useState4[1];
    var _useState5 = useState(false),
      _useState6 = _slicedToArray(_useState5, 2),
      showPassword = _useState6[0],
      setShowPassword = _useState6[1];
    var _useTracker = useTracker(function () {
        var subscription = Meteor.subscribe('userData');
        return {
          user: Meteor.user(),
          isLoading: !subscription.ready()
        };
      }, []),
      user = _useTracker.user;
    useEffect(function () {
      if (user) {
        var _user$profile, _user$roles;
        var role = ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);
        navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');
      }
    }, [user, navigate]);
    var handleResendVerification = function (email) {
      Meteor.call('users.resendVerificationEmail', email, function (err) {
        if (err) {
          setError(err.reason || 'Failed to resend verification email');
        } else {
          setResendEmailSent(true);
        }
      });
    };
    var handleSubmit = function (_ref, _ref2) {
      var email = _ref.email,
        password = _ref.password;
      var setSubmitting = _ref2.setSubmitting;
      setError(''); // Clear any previous errors
      setResendEmailSent(false);
      Meteor.loginWithPassword(email, password, function (err) {
        if (err) {
          console.error('Login error:', err);
          if (err.error === 'email-not-verified') {
            setError(/*#__PURE__*/React.createElement("div", null, "Email not verified.", /*#__PURE__*/React.createElement("button", {
              onClick: function () {
                return handleResendVerification(email);
              },
              className: "auth-link",
              style: {
                background: 'none',
                border: 'none',
                padding: '0 4px',
                cursor: 'pointer'
              }
            }, "Resend verification email"), resendEmailSent && /*#__PURE__*/React.createElement("div", {
              style: {
                marginTop: '8px',
                color: 'var(--success)',
                fontSize: '0.875rem'
              }
            }, "Verification email sent!")));
          } else {
            setError(err.reason || 'Login failed. Please check your credentials.');
          }
          setSubmitting(false);
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      className: "auth-container"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-card"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-header"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-logo"
    }, /*#__PURE__*/React.createElement("svg", {
      width: "48",
      height: "48",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/React.createElement("path", {
      d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }))), /*#__PURE__*/React.createElement("h1", {
      className: "auth-title"
    }, "Welcome Back"), /*#__PURE__*/React.createElement("p", {
      className: "auth-subtitle"
    }, "Sign in to access your workspace and manage your tasks efficiently")), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        email: '',
        password: ''
      },
      validationSchema: LoginSchema,
      onSubmit: handleSubmit
    }, function (_ref3) {
      var errors = _ref3.errors,
        touched = _ref3.touched,
        isSubmitting = _ref3.isSubmitting;
      return /*#__PURE__*/React.createElement(Form, {
        className: "auth-form"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Email Address"), /*#__PURE__*/React.createElement(Field, {
        id: "email",
        name: "email",
        type: "email",
        placeholder: "Enter your email address",
        className: "form-control " + (errors.email && touched.email ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "password",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "password",
        name: "password",
        type: showPassword ? 'text' : 'password',
        placeholder: "Enter your password",
        className: "form-control " + (errors.password && touched.password ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showPassword ? 'Hide password' : 'Show password',
        onClick: function () {
          return setShowPassword(function (v) {
            return !v;
          });
        }
      }, showPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.password && touched.password && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.password)), error && /*#__PURE__*/React.createElement("div", {
        className: "error-message error-message-global"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), error), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "btn btn-primary btn-auth",
        disabled: isSubmitting
      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
        className: "loading-spinner"
      }), "Signing in...") : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("svg", {
        className: "btn-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15M10 17L15 12M15 12L10 7M15 12H3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Sign In")), /*#__PURE__*/React.createElement("div", {
        className: "auth-links-row"
      }, /*#__PURE__*/React.createElement(Link, {
        to: "/forgot-password",
        className: "auth-link"
      }, "Forgot password?")));
    }), /*#__PURE__*/React.createElement("div", {
      className: "auth-footer"
    }, /*#__PURE__*/React.createElement("p", {
      className: "auth-footer-text"
    }, "Don't have an account?", /*#__PURE__*/React.createElement(Link, {
      to: "/signup",
      className: "auth-link"
    }, "Create an account")))));
  };
  _s(LoginPage, "NEaA31AkfK7s/oVXFdSbcR+dzU4=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = LoginPage;
  var _c;
  $RefreshReg$(_c, "LoginPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"ReportsPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/ReportsPage.jsx                                                                                    //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _objectSpread;
  module1.link("@babel/runtime/helpers/objectSpread2", {
    default: function (v) {
      _objectSpread = v;
    }
  }, 0);
  module1.export({
    ReportsPage: function () {
      return ReportsPage;
    }
  });
  var React;
  module1.link("react", {
    "default": function (v) {
      React = v;
    }
  }, 0);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 1);
  var Tasks;
  module1.link("/imports/api/tasks", {
    Tasks: function (v) {
      Tasks = v;
    }
  }, 2);
  var ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement;
  module1.link("chart.js", {
    Chart: function (v) {
      ChartJS = v;
    },
    ArcElement: function (v) {
      ArcElement = v;
    },
    Tooltip: function (v) {
      Tooltip = v;
    },
    Legend: function (v) {
      Legend = v;
    },
    CategoryScale: function (v) {
      CategoryScale = v;
    },
    LinearScale: function (v) {
      LinearScale = v;
    },
    BarElement: function (v) {
      BarElement = v;
    }
  }, 3);
  var Pie, Bar;
  module1.link("react-chartjs-2", {
    Pie: function (v) {
      Pie = v;
    },
    Bar: function (v) {
      Bar = v;
    }
  }, 4);
  var XLSX;
  module1.link("xlsx", {
    "*": function (v) {
      XLSX = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  // Register Chart.js components
  ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);
  var ReportsPage = function () {
    _s();
    var _useTracker = useTracker(function () {
        var tasksHandle = Meteor.subscribe('tasks');
        var teamMembersHandle = Meteor.subscribe('teamMembers');
        var tasks = Tasks.find({}, {
          sort: {
            createdAt: -1
          }
        }).fetch();
        var teamMembers = Meteor.users.find({
          $or: [{
            'roles': 'team-member'
          }, {
            'profile.role': 'team-member'
          }]
        }, {
          fields: {
            _id: 1,
            'profile.firstName': 1,
            'profile.lastName': 1
          }
        }).fetch();
        return {
          tasks: tasks,
          teamMembers: teamMembers,
          isLoading: !tasksHandle.ready() || !teamMembersHandle.ready()
        };
      }),
      tasks = _useTracker.tasks,
      isLoading = _useTracker.isLoading,
      teamMembers = _useTracker.teamMembers;
    var handleDownloadReport = function () {
      // Create a map of user IDs to full names
      var userNames = {};
      teamMembers.forEach(function (member) {
        var _member$profile, _member$profile2;
        var firstName = ((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '';
        var lastName = ((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '';
        userNames[member._id] = (firstName + " " + lastName).trim() || 'Unknown User';
      });

      // Prepare data for Excel
      var excelData = tasks.map(function (task) {
        var assignedToNames = task.assignedTo.map(function (userId) {
          return userNames[userId] || 'Unknown User';
        });
        return {
          'Task ID': task._id,
          'Title': task.title,
          'Status': task.status,
          'Priority': task.priority,
          'Progress': task.progress + "%",
          'Start Date': new Date(task.startDate).toLocaleDateString(),
          'Due Date': new Date(task.dueDate).toLocaleDateString(),
          'Created At': new Date(task.createdAt).toLocaleDateString(),
          'Assigned To': assignedToNames.join('; ')
        };
      });

      // Create worksheet
      var worksheet = XLSX.utils.json_to_sheet(excelData);

      // Set column widths
      var columnWidths = [{
        wch: 36
      },
      // Task ID
      {
        wch: 30
      },
      // Title
      {
        wch: 12
      },
      // Status
      {
        wch: 10
      },
      // Priority
      {
        wch: 10
      },
      // Progress
      {
        wch: 12
      },
      // Start Date
      {
        wch: 12
      },
      // Due Date
      {
        wch: 12
      },
      // Created At
      {
        wch: 30
      } // Assigned To
      ];
      worksheet['!cols'] = columnWidths;

      // Create workbook
      var workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Task Report');

      // Generate Excel file
      XLSX.writeFile(workbook, "task-report-" + new Date().toISOString().split('T')[0] + ".xlsx");
    };
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }

    // Calculate task status distribution
    var statusData = {
      completed: tasks.filter(function (task) {
        return task.status === 'completed';
      }).length,
      'in-progress': tasks.filter(function (task) {
        return task.status === 'in-progress';
      }).length,
      pending: tasks.filter(function (task) {
        return task.status === 'pending';
      }).length
    };

    // Calculate task priority distribution
    var priorityData = {
      high: tasks.filter(function (task) {
        return task.priority === 'high';
      }).length,
      medium: tasks.filter(function (task) {
        return task.priority === 'medium';
      }).length,
      low: tasks.filter(function (task) {
        return task.priority === 'low';
      }).length
    };

    // Task Status Chart Configuration
    var statusChartData = {
      labels: ['Completed', 'In Progress', 'Pending'],
      datasets: [{
        data: [statusData.completed, statusData['in-progress'], statusData.pending],
        backgroundColor: ['rgba(22, 163, 74, 0.8)',
        // Green for completed
        'rgba(59, 130, 246, 0.8)',
        // Blue for in-progress
        'rgba(245, 158, 11, 0.8)' // Yellow for pending
        ],
        borderColor: ['rgb(22, 163, 74)', 'rgb(59, 130, 246)', 'rgb(245, 158, 11)'],
        borderWidth: 1
      }]
    };

    // Task Priority Chart Configuration
    var priorityChartData = {
      labels: ['High', 'Medium', 'Low'],
      datasets: [{
        label: 'Number of Tasks',
        data: [priorityData.high, priorityData.medium, priorityData.low],
        backgroundColor: ['rgba(239, 68, 68, 0.8)',
        // Red for high
        'rgba(245, 158, 11, 0.8)',
        // Yellow for medium
        'rgba(22, 163, 74, 0.8)' // Green for low
        ],
        borderColor: ['rgb(239, 68, 68)', 'rgb(245, 158, 11)', 'rgb(22, 163, 74)'],
        borderWidth: 1
      }]
    };
    var chartOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Task Analytics"), /*#__PURE__*/React.createElement("button", {
      onClick: handleDownloadReport,
      className: "btn btn-primary",
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }
    }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCE5"), "Download Detailed Report")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.1rem',
        marginBottom: '16px',
        fontWeight: '600'
      }
    }, "Task Status Distribution"), /*#__PURE__*/React.createElement("div", {
      style: {
        height: '300px'
      }
    }, /*#__PURE__*/React.createElement(Pie, {
      data: statusChartData,
      options: chartOptions
    }))), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.1rem',
        marginBottom: '16px',
        fontWeight: '600'
      }
    }, "Task Priority Distribution"), /*#__PURE__*/React.createElement("div", {
      style: {
        height: '300px'
      }
    }, /*#__PURE__*/React.createElement(Bar, {
      data: priorityChartData,
      options: _objectSpread(_objectSpread({}, chartOptions), {}, {
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        }
      })
    })))), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px',
        marginTop: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "Total Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, tasks.length)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "Completed Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#16a34a',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, statusData.completed)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "In Progress"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#3b82f6',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, statusData['in-progress'])), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '8px',
        fontWeight: '500'
      }
    }, "Pending Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#f59e0b',
        fontSize: '1.5rem',
        fontWeight: '600',
        margin: 0
      }
    }, statusData.pending)))));
  };
  _s(ReportsPage, "4o4tbTyPIeBeo4AlHFSH4ARbaLQ=", false, function () {
    return [useTracker];
  });
  _c = ReportsPage;
  var _c;
  $RefreshReg$(_c, "ReportsPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"SignupPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/SignupPage.jsx                                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 0);
  module1.export({
    SignupPage: function () {
      return SignupPage;
    }
  });
  var React, useState;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useNavigate, Link;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    },
    Link: function (v) {
      Link = v;
    }
  }, 2);
  var Formik, Form, Field;
  module1.link("formik", {
    Formik: function (v) {
      Formik = v;
    },
    Form: function (v) {
      Form = v;
    },
    Field: function (v) {
      Field = v;
    }
  }, 3);
  var Yup;
  module1.link("yup", {
    "*": function (v) {
      Yup = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var SignupSchema = Yup.object().shape({
    firstName: Yup.string().min(2, 'First name must be at least 2 characters').required('First name is required'),
    lastName: Yup.string().min(2, 'Last name must be at least 2 characters').required('Last name is required'),
    email: Yup.string().email('Invalid email address').required('Email is required'),
    password: Yup.string().required('Password is required').min(8, 'Password must be at least 8 characters').matches(/[A-Z]/, 'Password must contain at least one uppercase letter').matches(/[0-9]/, 'Password must contain at least one number').matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),
    confirmPassword: Yup.string().oneOf([Yup.ref('password'), null], 'Passwords must match').required('Confirm password is required'),
    role: Yup.string().oneOf(['admin', 'team-member'], 'Invalid role').required('Role is required'),
    adminToken: Yup.string().when('role', {
      is: 'admin',
      then: function () {
        return Yup.string().required('Admin token is required').length(6, 'Admin token must be 6 digits');
      },
      otherwise: function () {
        return Yup.string();
      }
    })
  });
  var SignupPage = function () {
    _s();
    var navigate = useNavigate();
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      error = _useState2[0],
      setError = _useState2[1];
    var _useState3 = useState(false),
      _useState4 = _slicedToArray(_useState3, 2),
      verificationSent = _useState4[0],
      setVerificationSent = _useState4[1];
    var _useState5 = useState(false),
      _useState6 = _slicedToArray(_useState5, 2),
      showPassword = _useState6[0],
      setShowPassword = _useState6[1];
    var _useState7 = useState(false),
      _useState8 = _slicedToArray(_useState7, 2),
      showConfirmPassword = _useState8[0],
      setShowConfirmPassword = _useState8[1];
    var handleSubmit = function (values, _ref) {
      var setSubmitting = _ref.setSubmitting;
      var email = values.email,
        password = values.password,
        role = values.role,
        adminToken = values.adminToken,
        firstName = values.firstName,
        lastName = values.lastName;
      setError('');
      Meteor.call('users.create', {
        email: email,
        password: password,
        role: role,
        adminToken: adminToken,
        firstName: firstName,
        lastName: lastName
      }, function (err) {
        if (err) {
          console.error('Signup error:', err);
          setError(err.reason || 'Signup failed');
          setSubmitting(false);
        } else {
          // Log in the user after successful signup
          Meteor.loginWithPassword(email, password, function (err) {
            setSubmitting(false);
            if (err) {
              if (err.error === 'email-not-verified') {
                setVerificationSent(true);
              } else {
                console.error('Login error:', err);
                setError(err.reason || 'Login failed after signup');
              }
            } else {
              navigate(role === 'admin' ? '/admin-dashboard' : '/team-dashboard');
            }
          });
        }
      });
    };
    if (verificationSent) {
      return /*#__PURE__*/React.createElement("div", {
        className: "auth-container"
      }, /*#__PURE__*/React.createElement("div", {
        className: "auth-card"
      }, /*#__PURE__*/React.createElement("div", {
        className: "auth-header"
      }, /*#__PURE__*/React.createElement("div", {
        className: "auth-logo"
      }, /*#__PURE__*/React.createElement("svg", {
        width: "48",
        height: "48",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }))), /*#__PURE__*/React.createElement("h1", {
        className: "auth-title"
      }, "Verify Your Email"), /*#__PURE__*/React.createElement("p", {
        className: "auth-subtitle"
      }, "We've sent a verification email to your address. Please check your inbox and click the verification link to activate your account.")), /*#__PURE__*/React.createElement(Link, {
        to: "/login",
        className: "btn btn-primary btn-auth"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "btn-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M19 12H5M12 19L5 12L12 5",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Return to Login")));
    }
    return /*#__PURE__*/React.createElement("div", {
      className: "auth-container"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-card"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-header"
    }, /*#__PURE__*/React.createElement("div", {
      className: "auth-logo"
    }, /*#__PURE__*/React.createElement("svg", {
      width: "48",
      height: "48",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/React.createElement("path", {
      d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }), /*#__PURE__*/React.createElement("path", {
      d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
      stroke: "currentColor",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }))), /*#__PURE__*/React.createElement("h1", {
      className: "auth-title"
    }, "Create Account"), /*#__PURE__*/React.createElement("p", {
      className: "auth-subtitle"
    }, "Join your team's workspace and start managing tasks efficiently")), /*#__PURE__*/React.createElement(Formik, {
      initialValues: {
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        role: 'team-member',
        adminToken: ''
      },
      validationSchema: SignupSchema,
      onSubmit: handleSubmit
    }, function (_ref2) {
      var errors = _ref2.errors,
        touched = _ref2.touched,
        values = _ref2.values,
        isSubmitting = _ref2.isSubmitting;
      return /*#__PURE__*/React.createElement(Form, {
        className: "auth-form"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-row"
      }, /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "firstName",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "First Name"), /*#__PURE__*/React.createElement(Field, {
        id: "firstName",
        name: "firstName",
        type: "text",
        placeholder: "Enter your first name",
        className: "form-control " + (errors.firstName && touched.firstName ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.firstName && touched.firstName && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.firstName)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "lastName",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Last Name"), /*#__PURE__*/React.createElement(Field, {
        id: "lastName",
        name: "lastName",
        type: "text",
        placeholder: "Enter your last name",
        className: "form-control " + (errors.lastName && touched.lastName ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.lastName && touched.lastName && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.lastName))), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "email",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Email Address"), /*#__PURE__*/React.createElement(Field, {
        id: "email",
        name: "email",
        type: "email",
        placeholder: "Enter your email address",
        className: "form-control " + (errors.email && touched.email ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.email && touched.email && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.email)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "password",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "password",
        name: "password",
        type: showPassword ? 'text' : 'password',
        placeholder: "Create a secure password",
        className: "form-control " + (errors.password && touched.password ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showPassword ? 'Hide password' : 'Show password',
        onClick: function () {
          return setShowPassword(function (v) {
            return !v;
          });
        }
      }, showPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), /*#__PURE__*/React.createElement("div", {
        className: "password-requirements"
      }, /*#__PURE__*/React.createElement("p", {
        className: "password-requirements-title"
      }, "Password requirements:"), /*#__PURE__*/React.createElement("ul", {
        className: "password-requirements-list"
      }, /*#__PURE__*/React.createElement("li", {
        className: values.password.length >= 8 ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "At least 8 characters"), /*#__PURE__*/React.createElement("li", {
        className: /[A-Z]/.test(values.password) ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "One uppercase letter"), /*#__PURE__*/React.createElement("li", {
        className: /[0-9]/.test(values.password) ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "One number"), /*#__PURE__*/React.createElement("li", {
        className: /[!@#$%^&*]/.test(values.password) ? 'requirement-met' : ''
      }, /*#__PURE__*/React.createElement("svg", {
        className: "requirement-icon",
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "One special character (!@#$%^&*)"))), errors.password && touched.password && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.password)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "confirmPassword",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Confirm Password"), /*#__PURE__*/React.createElement("div", {
        className: "form-control-password-wrapper"
      }, /*#__PURE__*/React.createElement(Field, {
        id: "confirmPassword",
        name: "confirmPassword",
        type: showConfirmPassword ? 'text' : 'password',
        placeholder: "Confirm your password",
        className: "form-control " + (errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), /*#__PURE__*/React.createElement("button", {
        type: "button",
        className: "password-toggle-btn",
        tabIndex: -1,
        "aria-label": showConfirmPassword ? 'Hide password' : 'Show password',
        onClick: function () {
          return setShowConfirmPassword(function (v) {
            return !v;
          });
        }
      }, showConfirmPassword ? /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })) : /*#__PURE__*/React.createElement("svg", {
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("circle", {
        cx: "12",
        cy: "12",
        r: "3",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })))), errors.confirmPassword && touched.confirmPassword && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.confirmPassword)), /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "role",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 2L2 7L12 12L22 7L12 2Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M2 17L12 22L22 17",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M2 12L12 17L22 12",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Account Type"), /*#__PURE__*/React.createElement(Field, {
        id: "role",
        name: "role",
        as: "select",
        className: "form-control " + (errors.role && touched.role ? 'form-control-error' : ''),
        disabled: isSubmitting
      }, /*#__PURE__*/React.createElement("option", {
        value: "team-member"
      }, "Team Member"), /*#__PURE__*/React.createElement("option", {
        value: "admin"
      }, "Administrator")), errors.role && touched.role && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.role)), values.role === 'admin' && /*#__PURE__*/React.createElement("div", {
        className: "form-group"
      }, /*#__PURE__*/React.createElement("label", {
        htmlFor: "adminToken",
        className: "form-label"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "form-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M15 7C15 7 15 7 15 7C15 9.76142 12.7614 12 10 12C7.23858 12 5 9.76142 5 7C5 4.23858 7.23858 2 10 2C12.7614 2 15 4.23858 15 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M10 12C10 12 10 12 10 12C10 14.7614 7.76142 17 5 17C2.23858 17 0 14.7614 0 12C0 9.23858 2.23858 7 5 7C7.76142 7 10 9.23858 10 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Admin Token"), /*#__PURE__*/React.createElement(Field, {
        id: "adminToken",
        name: "adminToken",
        type: "text",
        placeholder: "Enter 6-digit admin token",
        className: "form-control " + (errors.adminToken && touched.adminToken ? 'form-control-error' : ''),
        disabled: isSubmitting
      }), errors.adminToken && touched.adminToken && /*#__PURE__*/React.createElement("div", {
        className: "error-message"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), errors.adminToken)), error && /*#__PURE__*/React.createElement("div", {
        className: "error-message error-message-global"
      }, /*#__PURE__*/React.createElement("svg", {
        className: "error-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), error), /*#__PURE__*/React.createElement("button", {
        type: "submit",
        className: "btn btn-primary btn-auth",
        disabled: isSubmitting
      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
        className: "loading-spinner"
      }), "Creating Account...") : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("svg", {
        className: "btn-icon",
        width: "16",
        height: "16",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }, /*#__PURE__*/React.createElement("path", {
        d: "M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }), /*#__PURE__*/React.createElement("path", {
        d: "M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z",
        stroke: "currentColor",
        strokeWidth: "2",
        strokeLinecap: "round",
        strokeLinejoin: "round"
      })), "Create Account")));
    }), /*#__PURE__*/React.createElement("div", {
      className: "auth-footer"
    }, /*#__PURE__*/React.createElement("p", {
      className: "auth-footer-text"
    }, "Already have an account?", /*#__PURE__*/React.createElement(Link, {
      to: "/login",
      className: "auth-link"
    }, "Log In")))));
  };
  _s(SignupPage, "Kr2Rw59cPwlTmhH2LL47KLyTCzw=", false, function () {
    return [useNavigate];
  });
  _c = SignupPage;
  var _c;
  $RefreshReg$(_c, "SignupPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TaskDetailPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TaskDetailPage.jsx                                                                                 //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _regeneratorRuntime;
  module1.link("@babel/runtime/regenerator", {
    default: function (v) {
      _regeneratorRuntime = v;
    }
  }, 0);
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 1);
  module1.export({
    TaskDetailPage: function () {
      return TaskDetailPage;
    }
  });
  var React, useState, useEffect;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    }
  }, 0);
  var useParams;
  module1.link("react-router-dom", {
    useParams: function (v) {
      useParams = v;
    }
  }, 1);
  var Container, Paper, Typography, Box, Grid, Chip, Divider, CircularProgress, Alert;
  module1.link("@mui/material", {
    Container: function (v) {
      Container = v;
    },
    Paper: function (v) {
      Paper = v;
    },
    Typography: function (v) {
      Typography = v;
    },
    Box: function (v) {
      Box = v;
    },
    Grid: function (v) {
      Grid = v;
    },
    Chip: function (v) {
      Chip = v;
    },
    Divider: function (v) {
      Divider = v;
    },
    CircularProgress: function (v) {
      CircularProgress = v;
    },
    Alert: function (v) {
      Alert = v;
    }
  }, 2);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 3);
  var AIInsightsPanel;
  module1.link("../components/AIInsightsPanel", {
    AIInsightsPanel: function (v) {
      AIInsightsPanel = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TaskDetailPage = function () {
    _s();
    var _useParams = useParams(),
      taskId = _useParams.taskId;
    var _useState = useState(null),
      _useState2 = _slicedToArray(_useState, 2),
      task = _useState2[0],
      setTask = _useState2[1];
    var _useState3 = useState(true),
      _useState4 = _slicedToArray(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = useState(null),
      _useState6 = _slicedToArray(_useState5, 2),
      error = _useState6[0],
      setError = _useState6[1];
    useEffect(function () {
      var fetchTask = function () {
        function _callee() {
          var result;
          return _regeneratorRuntime.async(function () {
            function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return _regeneratorRuntime.awrap(Meteor.call('tasks.findOne', taskId));
                case 3:
                  result = _context.sent;
                  setTask(result);
                  _context.next = 10;
                  break;
                case 7:
                  _context.prev = 7;
                  _context.t0 = _context["catch"](0);
                  setError(_context.t0.message);
                case 10:
                  _context.prev = 10;
                  setLoading(false);
                  return _context.finish(10);
                case 13:
                case "end":
                  return _context.stop();
              }
            }
            return _callee$;
          }(), null, null, [[0, 7, 10, 13]], Promise);
        }
        return _callee;
      }();
      fetchTask();
    }, [taskId]);
    if (loading) {
      return /*#__PURE__*/React.createElement(Box, {
        sx: {
          display: 'flex',
          justifyContent: 'center',
          p: 3
        }
      }, /*#__PURE__*/React.createElement(CircularProgress, null));
    }
    if (error) {
      return /*#__PURE__*/React.createElement(Container, {
        maxWidth: "md",
        sx: {
          mt: 4
        }
      }, /*#__PURE__*/React.createElement(Alert, {
        severity: "error"
      }, error));
    }
    if (!task) {
      return /*#__PURE__*/React.createElement(Container, {
        maxWidth: "md",
        sx: {
          mt: 4
        }
      }, /*#__PURE__*/React.createElement(Alert, {
        severity: "warning"
      }, "Task not found"));
    }
    return /*#__PURE__*/React.createElement(Container, {
      maxWidth: "lg",
      sx: {
        mt: 4,
        mb: 4
      }
    }, /*#__PURE__*/React.createElement(Grid, {
      container: true,
      spacing: 3
    }, /*#__PURE__*/React.createElement(Grid, {
      item: true,
      xs: 12,
      md: 8
    }, /*#__PURE__*/React.createElement(Paper, {
      sx: {
        p: 3,
        mb: 3
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "h4",
      gutterBottom: true
    }, task.title), /*#__PURE__*/React.createElement(Box, {
      sx: {
        mb: 2
      }
    }, /*#__PURE__*/React.createElement(Chip, {
      label: task.status,
      color: task.status === 'completed' ? 'success' : 'primary',
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Chip, {
      label: task.priority,
      color: task.priority === 'high' ? 'error' : task.priority === 'medium' ? 'warning' : 'info',
      sx: {
        mr: 1
      }
    }), /*#__PURE__*/React.createElement(Chip, {
      label: task.category,
      variant: "outlined"
    })), /*#__PURE__*/React.createElement(Divider, {
      sx: {
        my: 2
      }
    }), /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      gutterBottom: true
    }, "Description"), /*#__PURE__*/React.createElement(Typography, {
      variant: "body1",
      paragraph: true
    }, task.description), /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      gutterBottom: true
    }, "Progress"), /*#__PURE__*/React.createElement(Box, {
      sx: {
        width: '100%',
        bgcolor: 'background.paper'
      }
    }, /*#__PURE__*/React.createElement(Typography, {
      variant: "body2",
      color: "text.secondary"
    }, task.progress, "% Complete")), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Typography, {
      variant: "h6",
      gutterBottom: true,
      sx: {
        mt: 2
      }
    }, "Checklist"), /*#__PURE__*/React.createElement(Box, {
      component: "ul",
      sx: {
        pl: 2
      }
    }, task.checklist.map(function (item, index) {
      return /*#__PURE__*/React.createElement(Typography, {
        component: "li",
        key: index,
        sx: {
          textDecoration: item.completed ? 'line-through' : 'none',
          color: item.completed ? 'text.secondary' : 'text.primary'
        }
      }, item.text);
    }))))), /*#__PURE__*/React.createElement(Grid, {
      item: true,
      xs: 12,
      md: 4
    }, /*#__PURE__*/React.createElement(AIInsightsPanel, {
      taskId: taskId
    }))));
  };
  _s(TaskDetailPage, "5YVkBmKnY966FhgZjti6CLprjNQ=", false, function () {
    return [useParams];
  });
  _c = TaskDetailPage;
  var _c;
  $RefreshReg$(_c, "TaskDetailPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamDashboard.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TeamDashboard.jsx                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    TeamDashboard: function () {
      return TeamDashboard;
    }
  });
  var React, useState;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useNavigate, Routes, Route, Link;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    },
    Routes: function (v) {
      Routes = v;
    },
    Route: function (v) {
      Route = v;
    },
    Link: function (v) {
      Link = v;
    }
  }, 2);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 3);
  var Tasks;
  module1.link("/imports/api/tasks", {
    Tasks: function (v) {
      Tasks = v;
    }
  }, 4);
  var TeamTasksPage;
  module1.link("./TeamTasksPage", {
    TeamTasksPage: function (v) {
      TeamTasksPage = v;
    }
  }, 5);
  var TeamMembersPage;
  module1.link("./TeamMembersPage", {
    TeamMembersPage: function (v) {
      TeamMembersPage = v;
    }
  }, 6);
  var Header;
  module1.link("../components/Header", {
    Header: function (v) {
      Header = v;
    }
  }, 7);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$(),
    _s2 = $RefreshSig$();
  var TeamDashboardHome = function () {
    var _user$profile;
    _s();
    var _useTracker = useTracker(function () {
        var tasksHandle = Meteor.subscribe('tasks');
        var userHandle = Meteor.subscribe('userData');
        var user = Meteor.user();
        var tasks = Tasks.find({
          assignedTo: user === null || user === void 0 ? void 0 : user._id
        }, {
          sort: {
            createdAt: -1
          }
        }).fetch();
        return {
          tasks: tasks,
          user: user,
          isLoading: !tasksHandle.ready() || !userHandle.ready()
        };
      }),
      tasks = _useTracker.tasks,
      isLoading = _useTracker.isLoading,
      user = _useTracker.user;
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }
    var pendingTasks = tasks.filter(function (task) {
      return task.status === 'pending';
    }).length;
    var inProgressTasks = tasks.filter(function (task) {
      return task.status === 'in-progress';
    }).length;
    var completedTasks = tasks.filter(function (task) {
      return task.status === 'completed';
    }).length;
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: '0 0 24px 0',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Welcome, ", (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.firstName) || 'Team Member', "!"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px'
      }
    }, "\uD83D\uDCCB")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "My Tasks"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View and manage your assigned tasks"), /*#__PURE__*/React.createElement(Link, {
      to: "/team-dashboard/tasks",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "View Tasks")), /*#__PURE__*/React.createElement("div", {
      className: "dashboard-card",
      style: {
        backgroundColor: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '24px',
        transition: 'all 0.2s ease',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: '48px',
        height: '48px',
        borderRadius: '12px',
        backgroundColor: 'rgba(22, 163, 74, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '16px'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontSize: '24px'
      }
    }, "\uD83D\uDC65")), /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '8px',
        fontWeight: '600'
      }
    }, "My Team"), /*#__PURE__*/React.createElement("p", {
      style: {
        color: '#475569',
        marginBottom: '16px'
      }
    }, "View your team members and collaborators"), /*#__PURE__*/React.createElement(Link, {
      to: "/team-dashboard/team",
      className: "btn btn-primary",
      style: {
        width: '100%'
      }
    }, "View Team"))), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#f8fafc',
        borderRadius: '12px',
        padding: '24px',
        marginBottom: '32px'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      style: {
        color: '#0f172a',
        fontSize: '1.25rem',
        marginBottom: '24px',
        fontWeight: '600'
      }
    }, "Task Overview"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '4px'
      }
    }, "Pending Tasks"), /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, pendingTasks)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '4px'
      }
    }, "In Progress"), /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, inProgressTasks)), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#64748b',
        fontSize: '0.875rem',
        marginBottom: '4px'
      }
    }, "Completed Tasks"), /*#__PURE__*/React.createElement("div", {
      style: {
        color: '#0f172a',
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, completedTasks))))));
  };
  _s(TeamDashboardHome, "F+yCUgZLHn51yhMWFM7HnexYYgg=", false, function () {
    return [useTracker];
  });
  _c = TeamDashboardHome;
  var TeamDashboard = function () {
    _s2();
    var navigate = useNavigate();
    var handleLogout = function () {
      Meteor.logout(function (err) {
        if (err) {
          console.error('Logout error:', err);
        } else {
          navigate('/login');
        }
      });
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        minHeight: '100vh',
        backgroundColor: '#ffffff'
      }
    }, /*#__PURE__*/React.createElement(Header, {
      userRole: "team-member"
    }), /*#__PURE__*/React.createElement(Routes, null, /*#__PURE__*/React.createElement(Route, {
      path: "/",
      element: /*#__PURE__*/React.createElement(TeamDashboardHome, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks",
      element: /*#__PURE__*/React.createElement(TeamTasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks/:taskId",
      element: /*#__PURE__*/React.createElement(TeamTasksPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team",
      element: /*#__PURE__*/React.createElement(TeamMembersPage, null)
    })));
  };
  _s2(TeamDashboard, "CzcTeTziyjMsSrAVmHuCCb6+Bfg=", false, function () {
    return [useNavigate];
  });
  _c2 = TeamDashboard;
  var _c, _c2;
  $RefreshReg$(_c, "TeamDashboardHome");
  $RefreshReg$(_c2, "TeamDashboard");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamMembersPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TeamMembersPage.jsx                                                                                //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 0);
  module1.export({
    TeamMembersPage: function () {
      return TeamMembersPage;
    }
  });
  var React, useState;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useNavigate;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    }
  }, 2);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 3);
  var Tasks;
  module1.link("/imports/api/tasks", {
    Tasks: function (v) {
      Tasks = v;
    }
  }, 4);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TeamMembersPage = function () {
    _s();
    var navigate = useNavigate();
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      searchQuery = _useState2[0],
      setSearchQuery = _useState2[1];
    var _useState3 = useState(null),
      _useState4 = _slicedToArray(_useState3, 2),
      selectedMemberId = _useState4[0],
      setSelectedMemberId = _useState4[1];
    var _useTracker = useTracker(function () {
        var teamMembersHandle = Meteor.subscribe('teamMembers');
        var tasksHandle = Meteor.subscribe('tasks');
        var userHandle = Meteor.subscribe('userData');
        var currentUser = Meteor.user();
        var tasks = Tasks.find({}, {
          sort: {
            createdAt: -1
          }
        }).fetch();

        // Get all tasks that the current user is assigned to
        var userTasks = Tasks.find({
          assignedTo: currentUser === null || currentUser === void 0 ? void 0 : currentUser._id
        }).fetch();

        // Get unique team member IDs from those tasks
        var teamMemberIds = new Set();
        userTasks.forEach(function (task) {
          task.assignedTo.forEach(function (memberId) {
            if (memberId !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser._id)) {
              teamMemberIds.add(memberId);
            }
          });
        });

        // Fetch team members who share tasks with the current user
        var members = Meteor.users.find({
          _id: {
            $in: Array.from(teamMemberIds)
          }
        }, {
          fields: {
            emails: 1,
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.role': 1,
            'profile.department': 1,
            'profile.skills': 1,
            'profile.joinDate': 1,
            createdAt: 1
          }
        }).fetch();
        return {
          teamMembers: members,
          tasks: tasks,
          currentUser: currentUser,
          isLoading: !teamMembersHandle.ready() || !tasksHandle.ready() || !userHandle.ready()
        };
      }),
      teamMembers = _useTracker.teamMembers,
      tasks = _useTracker.tasks,
      isLoading = _useTracker.isLoading,
      currentUser = _useTracker.currentUser;
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: 'center',
          padding: '24px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        className: "loading-spinner"
      }));
    }
    var filteredMembers = teamMembers.filter(function (member) {
      var _member$profile, _member$profile2, _member$emails, _member$emails$, _member$emails$$addre;
      var fullName = ((((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '') + " " + (((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '')).toLowerCase();
      var email = ((_member$emails = member.emails) === null || _member$emails === void 0 ? void 0 : (_member$emails$ = _member$emails[0]) === null || _member$emails$ === void 0 ? void 0 : (_member$emails$$addre = _member$emails$.address) === null || _member$emails$$addre === void 0 ? void 0 : _member$emails$$addre.toLowerCase()) || '';
      return searchQuery === '' || fullName.includes(searchQuery.toLowerCase()) || email.includes(searchQuery.toLowerCase());
    });

    // Get shared tasks, attachments, and links for a member
    var getMemberContributions = function (memberId) {
      // Get tasks that both the current user and the member are assigned to
      var sharedTasks = tasks.filter(function (task) {
        return task.assignedTo.includes(memberId) && task.assignedTo.includes(currentUser === null || currentUser === void 0 ? void 0 : currentUser._id);
      });
      var attachments = sharedTasks.flatMap(function (task) {
        return (task.attachments || []).filter(function (attachment) {
          return attachment.uploadedBy === memberId;
        });
      });
      var links = sharedTasks.flatMap(function (task) {
        return (task.links || []).filter(function (link) {
          return link.addedBy === memberId;
        });
      });
      return {
        sharedTasks: sharedTasks,
        attachments: attachments,
        links: links
      };
    };
    var handleMemberClick = function (memberId) {
      setSelectedMemberId(selectedMemberId === memberId ? null : memberId);
    };
    var handleAttachmentClick = function (e, attachment) {
      e.stopPropagation(); // Prevent card click event
      try {
        // Remove data URL prefix if present
        var base64Data = attachment.data;
        if (base64Data.startsWith('data:')) {
          base64Data = base64Data.split(',')[1];
        }

        // Create a blob from the base64 data
        var byteCharacters = atob(base64Data);
        var byteNumbers = new Array(byteCharacters.length);
        for (var i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        var byteArray = new Uint8Array(byteNumbers);

        // Get the correct MIME type
        var mimeType = attachment.type || 'application/octet-stream';
        var blob = new Blob([byteArray], {
          type: mimeType
        });

        // Create a download link and trigger it
        var url = window.URL.createObjectURL(blob);
        var link = document.createElement('a');
        link.href = url;
        link.download = attachment.name;
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(function () {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);
      } catch (error) {
        console.error('Error downloading file:', error);
        alert('Failed to download file. Please try again.');
      }
    };
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "Team Members")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search team members...",
      value: searchQuery,
      onChange: function (e) {
        return setSearchQuery(e.target.value);
      },
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    }))), filteredMembers.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px',
        width: '100%'
      },
      className: "team-member-grid"
    }, filteredMembers.map(function (member) {
      var _member$profile3, _member$profile4, _member$emails2, _member$emails2$, _member$profile5;
      var _getMemberContributio = getMemberContributions(member._id),
        sharedTasks = _getMemberContributio.sharedTasks,
        attachments = _getMemberContributio.attachments,
        links = _getMemberContributio.links;
      var isSelected = selectedMemberId === member._id;
      return /*#__PURE__*/React.createElement("div", {
        key: member._id,
        onClick: function () {
          return handleMemberClick(member._id);
        },
        style: {
          backgroundColor: '#ffffff',
          border: '1px solid #e2e8f0',
          borderRadius: '12px',
          padding: '24px',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
          cursor: 'pointer',
          ':hover': {
            borderColor: '#16a34a',
            transform: 'translateY(-2px)'
          }
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
        style: {
          margin: 0,
          fontSize: '1.1rem',
          fontWeight: '600',
          color: '#0f172a'
        }
      }, (_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : _member$profile3.firstName, " ", (_member$profile4 = member.profile) === null || _member$profile4 === void 0 ? void 0 : _member$profile4.lastName), /*#__PURE__*/React.createElement("p", {
        style: {
          margin: '4px 0 0',
          fontSize: '0.875rem',
          color: '#64748b'
        }
      }, (_member$emails2 = member.emails) === null || _member$emails2 === void 0 ? void 0 : (_member$emails2$ = _member$emails2[0]) === null || _member$emails2$ === void 0 ? void 0 : _member$emails2$.address)), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: '#f1f5f9',
          color: '#475569'
        }
      }, ((_member$profile5 = member.profile) === null || _member$profile5 === void 0 ? void 0 : _member$profile5.role) || 'Team Member')), sharedTasks.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: '16px'
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "Shared Tasks"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '4px'
        }
      }, sharedTasks.map(function (task) {
        return /*#__PURE__*/React.createElement("div", {
          key: task._id,
          style: {
            padding: '6px 8px',
            backgroundColor: '#f8fafc',
            borderRadius: '6px',
            fontSize: '0.875rem',
            color: '#0f172a',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: '100%'
          }
        }, task.title);
      }))), isSelected && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: '16px'
        }
      }, attachments.length > 1 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '16px'
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "All Files"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }
      }, attachments.slice(1).map(function (attachment, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          onClick: function (e) {
            return handleAttachmentClick(e, attachment);
          },
          style: {
            padding: '8px',
            backgroundColor: '#f8fafc',
            borderRadius: '6px',
            fontSize: '0.875rem',
            color: '#0f172a',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            cursor: 'pointer'
          }
        }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDCCE"), attachment.name);
      }))), links.length > 1 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: '16px'
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px',
          fontWeight: '500'
        }
      }, "All Links"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px'
        }
      }, links.slice(1).map(function (link, index) {
        return /*#__PURE__*/React.createElement("a", {
          key: index,
          href: link.url,
          target: "_blank",
          rel: "noopener noreferrer",
          onClick: function (e) {
            return e.stopPropagation();
          },
          style: {
            padding: '8px',
            backgroundColor: '#f8fafc',
            borderRadius: '6px',
            fontSize: '0.875rem',
            color: '#0f172a',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            textDecoration: 'none',
            cursor: 'pointer'
          }
        }, /*#__PURE__*/React.createElement("span", null, "\uD83D\uDD17"), link.url);
      })))));
    })) : /*#__PURE__*/React.createElement("p", {
      style: {
        textAlign: 'center',
        color: '#475569'
      }
    }, searchQuery ? 'No team members match your search' : 'No team members share tasks with you yet')));
  };
  _s(TeamMembersPage, "NPArPGcOg4DCRnm96SeCCnjTQy4=", false, function () {
    return [useNavigate, useTracker];
  });
  _c = TeamMembersPage;
  var _c;
  $RefreshReg$(_c, "TeamMembersPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"TeamTasksPage.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/pages/TeamTasksPage.jsx                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var _slicedToArray;
  module1.link("@babel/runtime/helpers/slicedToArray", {
    default: function (v) {
      _slicedToArray = v;
    }
  }, 0);
  module1.export({
    TeamTasksPage: function () {
      return TeamTasksPage;
    }
  });
  var React, useState, useEffect;
  module1.link("react", {
    "default": function (v) {
      React = v;
    },
    useState: function (v) {
      useState = v;
    },
    useEffect: function (v) {
      useEffect = v;
    }
  }, 0);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 1);
  var useNavigate, useParams, useLocation;
  module1.link("react-router-dom", {
    useNavigate: function (v) {
      useNavigate = v;
    },
    useParams: function (v) {
      useParams = v;
    },
    useLocation: function (v) {
      useLocation = v;
    }
  }, 2);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 3);
  var Tasks;
  module1.link("/imports/api/tasks", {
    Tasks: function (v) {
      Tasks = v;
    }
  }, 4);
  var TeamTaskView;
  module1.link("../components/TeamTaskView", {
    TeamTaskView: function (v) {
      TeamTaskView = v;
    }
  }, 5);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$();
  var TeamTasksPage = function () {
    _s();
    var navigate = useNavigate();
    var location = useLocation();
    var _useParams = useParams(),
      taskId = _useParams.taskId;
    var _useState = useState(''),
      _useState2 = _slicedToArray(_useState, 2),
      searchQuery = _useState2[0],
      setSearchQuery = _useState2[1];
    var _useState3 = useState('all'),
      _useState4 = _slicedToArray(_useState3, 2),
      statusFilter = _useState4[0],
      setStatusFilter = _useState4[1];
    var _useState5 = useState('all'),
      _useState6 = _slicedToArray(_useState5, 2),
      priorityFilter = _useState6[0],
      setPriorityFilter = _useState6[1];
    var _useTracker = useTracker(function () {
        var tasksHandle = Meteor.subscribe('tasks');
        var userHandle = Meteor.subscribe('userData');
        var teamMembersHandle = Meteor.subscribe('teamMembers');
        var user = Meteor.user();
        var tasks = Tasks.find({
          assignedTo: user === null || user === void 0 ? void 0 : user._id
        }, {
          sort: {
            createdAt: -1
          }
        }).fetch();
        var teamMembers = Meteor.users.find({}, {
          fields: {
            _id: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.email': 1
          }
        }).fetch();
        return {
          tasks: tasks,
          user: user,
          teamMembers: teamMembers,
          isLoading: !tasksHandle.ready() || !userHandle.ready() || !teamMembersHandle.ready()
        };
      }),
      tasks = _useTracker.tasks,
      isLoading = _useTracker.isLoading,
      user = _useTracker.user,
      teamMembers = _useTracker.teamMembers;
    var userNames = {};
    teamMembers === null || teamMembers === void 0 ? void 0 : teamMembers.forEach(function (member) {
      var _member$profile, _member$profile2, _member$profile3, _member$profile3$emai;
      var firstName = ((_member$profile = member.profile) === null || _member$profile === void 0 ? void 0 : _member$profile.firstName) || '';
      var lastName = ((_member$profile2 = member.profile) === null || _member$profile2 === void 0 ? void 0 : _member$profile2.lastName) || '';
      userNames[member._id] = (firstName + " " + lastName).trim() || ((_member$profile3 = member.profile) === null || _member$profile3 === void 0 ? void 0 : (_member$profile3$emai = _member$profile3.email) === null || _member$profile3$emai === void 0 ? void 0 : _member$profile3$emai.split('@')[0]) || 'Unknown';
    });
    var currentTask = taskId ? tasks.find(function (t) {
      return t._id === taskId;
    }) : null;
    if (currentTask) {
      return /*#__PURE__*/React.createElement(TeamTaskView, {
        task: currentTask,
        onCancel: function () {
          return navigate('/team-dashboard/tasks');
        }
      });
    }
    var filteredTasks = tasks.filter(function (task) {
      var matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) || task.description.toLowerCase().includes(searchQuery.toLowerCase());
      var matchesStatus = statusFilter === 'all' || task.status === statusFilter;
      var matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
      return matchesSearch && matchesStatus && matchesPriority;
    });
    return /*#__PURE__*/React.createElement("div", {
      style: {
        padding: '24px',
        background: '#ffffff',
        minHeight: 'calc(100vh - 64px)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }
    }, /*#__PURE__*/React.createElement("h2", {
      style: {
        color: '#0f172a',
        margin: 0,
        fontSize: '1.5rem',
        fontWeight: '600'
      }
    }, "My Tasks")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        flex: 1,
        minWidth: '200px'
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      placeholder: "Search tasks...",
      value: searchQuery,
      onChange: function (e) {
        return setSearchQuery(e.target.value);
      },
      style: {
        width: '100%',
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem'
      }
    })), /*#__PURE__*/React.createElement("select", {
      value: statusFilter,
      onChange: function (e) {
        return setStatusFilter(e.target.value);
      },
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '150px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Status"), /*#__PURE__*/React.createElement("option", {
      value: "pending"
    }, "Pending"), /*#__PURE__*/React.createElement("option", {
      value: "in-progress"
    }, "In Progress"), /*#__PURE__*/React.createElement("option", {
      value: "completed"
    }, "Completed")), /*#__PURE__*/React.createElement("select", {
      value: priorityFilter,
      onChange: function (e) {
        return setPriorityFilter(e.target.value);
      },
      style: {
        padding: '8px 12px',
        borderRadius: '6px',
        border: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        minWidth: '150px'
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "all"
    }, "All Priority"), /*#__PURE__*/React.createElement("option", {
      value: "high"
    }, "High"), /*#__PURE__*/React.createElement("option", {
      value: "medium"
    }, "Medium"), /*#__PURE__*/React.createElement("option", {
      value: "low"
    }, "Low"))), isLoading ? /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '24px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "loading-spinner"
    })) : filteredTasks.length > 0 ? /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '24px',
        padding: '16px 0'
      }
    }, filteredTasks.map(function (task) {
      var _task$checklist, _task$checklist2;
      var completed = ((_task$checklist = task.checklist) === null || _task$checklist === void 0 ? void 0 : _task$checklist.filter(function (item) {
        return item.completed;
      }).length) || 0;
      var total = ((_task$checklist2 = task.checklist) === null || _task$checklist2 === void 0 ? void 0 : _task$checklist2.length) || 0;
      var progress = total > 0 ? Math.round(completed / total * 100) : 0;
      return /*#__PURE__*/React.createElement("div", {
        key: task._id,
        onClick: function () {
          return navigate("/team-dashboard/tasks/" + task._id);
        },
        style: {
          backgroundColor: '#ffffff',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '16px',
          cursor: 'pointer'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: '12px'
        }
      }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h3", {
        style: {
          margin: 0,
          fontSize: '1.1rem',
          fontWeight: '600',
          color: '#0f172a'
        }
      }, task.title), /*#__PURE__*/React.createElement("p", {
        style: {
          color: '#64748b',
          fontSize: '0.875rem',
          marginBottom: '12px',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxHeight: '2.6em'
        }
      }, task.description))), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          marginBottom: '8px',
          flexWrap: 'wrap'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef3c7' : '#dcfce7',
          color: task.priority === 'high' ? '#dc2626' : task.priority === 'medium' ? '#d97706' : '#16a34a'
        }
      }, task.priority), /*#__PURE__*/React.createElement("span", {
        style: {
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: '500',
          backgroundColor: task.status === 'completed' ? '#dcfce7' : task.status === 'in-progress' ? '#fef3c7' : '#f1f5f9',
          color: task.status === 'completed' ? '#16a34a' : task.status === 'in-progress' ? '#d97706' : '#475569'
        }
      }, task.status), /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "Due:"), /*#__PURE__*/React.createElement("span", {
        style: {
          color: '#0f172a'
        }
      }, new Date(task.dueDate).toLocaleDateString())), total > 0 && /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "Progress:"), /*#__PURE__*/React.createElement("span", {
        style: {
          color: '#0f172a'
        }
      }, progress, "%"))), task.assignedTo && task.assignedTo.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginBottom: '8px',
          flexWrap: 'wrap'
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b'
        }
      }, "Team:"), task.assignedTo.slice(0, 3).map(function (userId) {
        return /*#__PURE__*/React.createElement("span", {
          key: userId,
          style: {
            fontSize: '0.85rem',
            padding: '2px 8px',
            borderRadius: '4px',
            backgroundColor: '#f1f5f9',
            color: '#475569',
            fontWeight: 500
          }
        }, userNames[userId] || 'Unknown');
      }), task.assignedTo.length > 3 && /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: '0.85rem',
          color: '#64748b'
        }
      }, "+", task.assignedTo.length - 3, " more")), task.checklist && task.checklist.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: '8px',
          padding: '8px',
          backgroundColor: '#f8fafc',
          borderRadius: '8px'
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          fontSize: '0.875rem',
          color: '#64748b',
          marginBottom: '8px'
        }
      }, /*#__PURE__*/React.createElement("span", null, "Checklist Progress:"), /*#__PURE__*/React.createElement("span", {
        style: {
          color: '#0f172a',
          fontWeight: '500'
        }
      }, completed, "/", total)), /*#__PURE__*/React.createElement("div", {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '4px'
        }
      }, task.checklist.slice(0, 3).map(function (item, index) {
        return /*#__PURE__*/React.createElement("div", {
          key: index,
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '0.75rem',
            color: '#64748b'
          }
        }, /*#__PURE__*/React.createElement("input", {
          type: "checkbox",
          checked: item.completed,
          disabled: true
        }), /*#__PURE__*/React.createElement("span", {
          style: {
            textDecoration: item.completed ? 'line-through' : 'none'
          }
        }, item.text));
      }))));
    })) : /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: 'center',
        color: '#64748b',
        padding: '24px'
      }
    }, "No tasks found.")));
  };
  _s(TeamTasksPage, "WgKy44ipTVWXTbwc1iNrDYm2hdA=", false, function () {
    return [useNavigate, useLocation, useParams, useTracker];
  });
  _c = TeamTasksPage;
  var _c;
  $RefreshReg$(_c, "TeamTasksPage");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}},"App.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// imports/ui/App.jsx                                                                                                  //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  module1.export({
    App: function () {
      return App;
    }
  });
  var React;
  module1.link("react", {
    "default": function (v) {
      React = v;
    }
  }, 0);
  var Routes, Route, Navigate, useLocation;
  module1.link("react-router-dom", {
    Routes: function (v) {
      Routes = v;
    },
    Route: function (v) {
      Route = v;
    },
    Navigate: function (v) {
      Navigate = v;
    },
    useLocation: function (v) {
      useLocation = v;
    }
  }, 1);
  var useTracker;
  module1.link("meteor/react-meteor-data", {
    useTracker: function (v) {
      useTracker = v;
    }
  }, 2);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 3);
  var LoginPage;
  module1.link("./pages/LoginPage", {
    LoginPage: function (v) {
      LoginPage = v;
    }
  }, 4);
  var SignupPage;
  module1.link("./pages/SignupPage", {
    SignupPage: function (v) {
      SignupPage = v;
    }
  }, 5);
  var AdminDashboard;
  module1.link("./pages/AdminDashboard", {
    AdminDashboard: function (v) {
      AdminDashboard = v;
    }
  }, 6);
  var TeamDashboard;
  module1.link("./pages/TeamDashboard", {
    TeamDashboard: function (v) {
      TeamDashboard = v;
    }
  }, 7);
  var TeamTasksPage;
  module1.link("./pages/TeamTasksPage", {
    TeamTasksPage: function (v) {
      TeamTasksPage = v;
    }
  }, 8);
  var TeamMembersPage;
  module1.link("./pages/TeamMembersPage", {
    TeamMembersPage: function (v) {
      TeamMembersPage = v;
    }
  }, 9);
  var TaskDetailPage;
  module1.link("./pages/TaskDetailPage", {
    TaskDetailPage: function (v) {
      TaskDetailPage = v;
    }
  }, 10);
  var ForgotPasswordPage;
  module1.link("./pages/ForgotPasswordPage", {
    ForgotPasswordPage: function (v) {
      ForgotPasswordPage = v;
    }
  }, 11);
  ___INIT_METEOR_FAST_REFRESH(module);
  var _s = $RefreshSig$(),
    _s2 = $RefreshSig$();
  // Protected Route component
  var ProtectedRoute = function (_ref) {
    var children = _ref.children,
      allowedRoles = _ref.allowedRoles;
    _s();
    var location = useLocation();
    var _useTracker = useTracker(function () {
        var _user$profile, _user$roles;
        var subscription = Meteor.subscribe('userData');
        var user = Meteor.user();
        var userRole = (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);
        return {
          user: user,
          userRole: userRole,
          isLoading: !subscription.ready()
        };
      }, []),
      user = _useTracker.user,
      userRole = _useTracker.userRole,
      isLoading = _useTracker.isLoading;
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", null, "Loading...");
    }
    if (!user) {
      return /*#__PURE__*/React.createElement(Navigate, {
        to: "/login",
        state: {
          from: location
        },
        replace: true
      });
    }
    if (allowedRoles && !allowedRoles.includes(userRole)) {
      return /*#__PURE__*/React.createElement(Navigate, {
        to: userRole === 'admin' ? '/admin-dashboard' : '/team-dashboard',
        replace: true
      });
    }
    return children;
  };
  _s(ProtectedRoute, "mLGqI+cBY0PttkscNXiFj6AZFEU=", false, function () {
    return [useLocation, useTracker];
  });
  _c = ProtectedRoute;
  var App = function () {
    var _user$profile2, _user$profile3, _user$profile4;
    _s2();
    var _useTracker2 = useTracker(function () {
        var subscription = Meteor.subscribe('userData');
        return {
          user: Meteor.user(),
          isLoading: !subscription.ready()
        };
      }, []),
      user = _useTracker2.user,
      isLoading = _useTracker2.isLoading;
    if (isLoading) {
      return /*#__PURE__*/React.createElement("div", null, "Loading...");
    }
    return /*#__PURE__*/React.createElement("div", {
      className: "container"
    }, /*#__PURE__*/React.createElement(Routes, null, /*#__PURE__*/React.createElement(Route, {
      path: "/login",
      element: user ? /*#__PURE__*/React.createElement(Navigate, {
        to: ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard',
        replace: true
      }) : /*#__PURE__*/React.createElement(LoginPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/signup",
      element: user ? /*#__PURE__*/React.createElement(Navigate, {
        to: ((_user$profile3 = user.profile) === null || _user$profile3 === void 0 ? void 0 : _user$profile3.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard',
        replace: true
      }) : /*#__PURE__*/React.createElement(SignupPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/admin-dashboard/*",
      element: /*#__PURE__*/React.createElement(ProtectedRoute, {
        allowedRoles: ['admin']
      }, /*#__PURE__*/React.createElement(AdminDashboard, null))
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/team-dashboard/*",
      element: /*#__PURE__*/React.createElement(ProtectedRoute, {
        allowedRoles: ['team-member']
      }, /*#__PURE__*/React.createElement(TeamDashboard, null))
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/forgot-password",
      element: /*#__PURE__*/React.createElement(ForgotPasswordPage, null)
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/",
      element: /*#__PURE__*/React.createElement(Navigate, {
        to: user ? ((_user$profile4 = user.profile) === null || _user$profile4 === void 0 ? void 0 : _user$profile4.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard' : '/login',
        replace: true
      })
    }), /*#__PURE__*/React.createElement(Route, {
      path: "/tasks/:taskId",
      element: /*#__PURE__*/React.createElement(TaskDetailPage, null)
    })));
  };
  _s2(App, "FQsDSDpz35b7IMQ9/L3SUoeMc8w=", false, function () {
    return [useTracker];
  });
  _c2 = App;
  var _c, _c2;
  $RefreshReg$(_c, "ProtectedRoute");
  $RefreshReg$(_c2, "App");
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},"client":{"main.jsx":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                     //
// client/main.jsx                                                                                                     //
//                                                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                       //
!function (module1) {
  var React;
  module1.link("react", {
    "default": function (v) {
      React = v;
    }
  }, 0);
  var createRoot;
  module1.link("react-dom/client", {
    createRoot: function (v) {
      createRoot = v;
    }
  }, 1);
  var BrowserRouter;
  module1.link("react-router-dom", {
    BrowserRouter: function (v) {
      BrowserRouter = v;
    }
  }, 2);
  var Meteor;
  module1.link("meteor/meteor", {
    Meteor: function (v) {
      Meteor = v;
    }
  }, 3);
  var App;
  module1.link("/imports/ui/App", {
    App: function (v) {
      App = v;
    }
  }, 4);
  module1.link("/imports/api");
  ___INIT_METEOR_FAST_REFRESH(module);
  // Import collections

  // Add some CSS for the loading spinner
  var style = document.createElement('style');
  style.textContent = "\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n";
  document.head.appendChild(style);

  // Enable hot reloading in development
  if (module.hot) {
    module.hot.accept();
  }
  Meteor.startup(function () {
    var container = document.getElementById('react-target');
    var root = createRoot(container);
    root.render(/*#__PURE__*/React.createElement(BrowserRouter, null, /*#__PURE__*/React.createElement(App, null)));
  });
}.call(this, module);
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}},{
  "extensions": [
    ".js",
    ".json",
    ".html",
    ".ts",
    ".mjs",
    ".css",
    ".tsx",
    ".jsx"
  ]
});

require("/client/main.jsx");