{"version": 3, "sources": ["meteor://💻app/packages/ordered-dict/ordered_dict.js"], "names": ["module", "export", "OrderedDict", "element", "key", "value", "next", "prev", "constructor", "_dict", "Object", "create", "_first", "_last", "_size", "_len", "arguments", "length", "args", "Array", "_key", "_stringify", "shift", "x", "for<PERSON>ach", "kv", "putBefore", "_k", "empty", "size", "_linkEltIn", "elt", "_linkEltOut", "item", "before", "Error", "append", "remove", "get", "has", "prototype", "hasOwnProperty", "call", "iter", "context", "undefined", "i", "b", "BREAK", "forEachAsync", "asyncIter", "first", "firstValue", "last", "lastValue", "moveBefore", "eltBefore", "indexOf", "ret", "v", "k", "_checkRep", "keys"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,WAAW,EAACA,CAAA,KAAIA;AAAW,CAAC,CAAC;AAA5C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACvC,OAAO;IACLH,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEA;EACR,CAAC;AACH;AAEO,MAAML,WAAW,CAAC;EACvBM,WAAWA,CAAA,EAAU;IACnB,IAAI,CAACC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAChC,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAJFC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAMjB,IAAI,OAAOF,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;MACjC,IAAI,CAACG,UAAU,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC;IAChC,CAAC,MAAM;MACL,IAAI,CAACD,UAAU,GAAG,UAAUE,CAAC,EAAE;QAAE,OAAOA,CAAC;MAAE,CAAC;IAC9C;IAEAL,IAAI,CAACM,OAAO,CAACC,EAAE,IAAI,IAAI,CAACC,SAAS,CAACD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EACxD;;EAEA;EACA;EACAE,EAAEA,CAACvB,GAAG,EAAE;IACN,OAAO,GAAG,GAAG,IAAI,CAACiB,UAAU,CAACjB,GAAG,CAAC;EACnC;EAEAwB,KAAKA,CAAA,EAAG;IACN,OAAO,CAAC,IAAI,CAAChB,MAAM;EACrB;EAEAiB,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACf,KAAK;EACnB;EAEAgB,UAAUA,CAACC,GAAG,EAAE;IACd,IAAI,CAACA,GAAG,CAACzB,IAAI,EAAE;MACbyB,GAAG,CAACxB,IAAI,GAAG,IAAI,CAACM,KAAK;MACrB,IAAI,IAAI,CAACA,KAAK,EACZ,IAAI,CAACA,KAAK,CAACP,IAAI,GAAGyB,GAAG;MACvB,IAAI,CAAClB,KAAK,GAAGkB,GAAG;IAClB,CAAC,MAAM;MACLA,GAAG,CAACxB,IAAI,GAAGwB,GAAG,CAACzB,IAAI,CAACC,IAAI;MACxBwB,GAAG,CAACzB,IAAI,CAACC,IAAI,GAAGwB,GAAG;MACnB,IAAIA,GAAG,CAACxB,IAAI,EACVwB,GAAG,CAACxB,IAAI,CAACD,IAAI,GAAGyB,GAAG;IACvB;IACA,IAAI,IAAI,CAACnB,MAAM,KAAK,IAAI,IAAI,IAAI,CAACA,MAAM,KAAKmB,GAAG,CAACzB,IAAI,EAClD,IAAI,CAACM,MAAM,GAAGmB,GAAG;EACrB;EAEAC,WAAWA,CAACD,GAAG,EAAE;IACf,IAAIA,GAAG,CAACzB,IAAI,EACVyB,GAAG,CAACzB,IAAI,CAACC,IAAI,GAAGwB,GAAG,CAACxB,IAAI;IAC1B,IAAIwB,GAAG,CAACxB,IAAI,EACVwB,GAAG,CAACxB,IAAI,CAACD,IAAI,GAAGyB,GAAG,CAACzB,IAAI;IAC1B,IAAIyB,GAAG,KAAK,IAAI,CAAClB,KAAK,EACpB,IAAI,CAACA,KAAK,GAAGkB,GAAG,CAACxB,IAAI;IACvB,IAAIwB,GAAG,KAAK,IAAI,CAACnB,MAAM,EACrB,IAAI,CAACA,MAAM,GAAGmB,GAAG,CAACzB,IAAI;EAC1B;EAEAoB,SAASA,CAACtB,GAAG,EAAE6B,IAAI,EAAEC,MAAM,EAAE;IAC3B,IAAI,IAAI,CAACzB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC,EAC1B,MAAM,IAAI+B,KAAK,CAAC,OAAO,GAAG/B,GAAG,GAAG,iCAAiC,CAAC;IACpE,IAAI2B,GAAG,GAAGG,MAAM,GACd/B,OAAO,CAACC,GAAG,EAAE6B,IAAI,EAAE,IAAI,CAACxB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACO,MAAM,CAAC,CAAC,CAAC,GAC/C/B,OAAO,CAACC,GAAG,EAAE6B,IAAI,EAAE,IAAI,CAAC;IAC1B,IAAI,OAAOF,GAAG,CAACzB,IAAI,KAAK,WAAW,EACjC,MAAM,IAAI6B,KAAK,CAAC,4CAA4C,CAAC;IAC/D,IAAI,CAACL,UAAU,CAACC,GAAG,CAAC;IACpB,IAAI,CAACtB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC,GAAG2B,GAAG;IAC9B,IAAI,CAACjB,KAAK,EAAE;EACd;EAEAsB,MAAMA,CAAChC,GAAG,EAAE6B,IAAI,EAAE;IAChB,IAAI,CAACP,SAAS,CAACtB,GAAG,EAAE6B,IAAI,EAAE,IAAI,CAAC;EACjC;EAEAI,MAAMA,CAACjC,GAAG,EAAE;IACV,IAAI2B,GAAG,GAAG,IAAI,CAACtB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC;IAClC,IAAI,OAAO2B,GAAG,KAAK,WAAW,EAC5B,MAAM,IAAII,KAAK,CAAC,OAAO,GAAG/B,GAAG,GAAG,6BAA6B,CAAC;IAChE,IAAI,CAAC4B,WAAW,CAACD,GAAG,CAAC;IACrB,IAAI,CAACjB,KAAK,EAAE;IACZ,OAAO,IAAI,CAACL,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC;IAC/B,OAAO2B,GAAG,CAAC1B,KAAK;EAClB;EAEAiC,GAAGA,CAAClC,GAAG,EAAE;IACP,IAAI,IAAI,CAACmC,GAAG,CAACnC,GAAG,CAAC,EAAE;MACjB,OAAO,IAAI,CAACK,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC,CAACC,KAAK;IACvC;EACF;EAEAkC,GAAGA,CAACnC,GAAG,EAAE;IACP,OAAOM,MAAM,CAAC8B,SAAS,CAACC,cAAc,CAACC,IAAI,CACzC,IAAI,CAACjC,KAAK,EACV,IAAI,CAACkB,EAAE,CAACvB,GAAG,CACb,CAAC;EACH;;EAEA;EACA;;EAEA;EACAoB,OAAOA,CAACmB,IAAI,EAAkB;IAAA,IAAhBC,OAAO,GAAA5B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA6B,SAAA,GAAA7B,SAAA,MAAG,IAAI;IAC1B,IAAI8B,CAAC,GAAG,CAAC;IACT,IAAIf,GAAG,GAAG,IAAI,CAACnB,MAAM;IACrB,OAAOmB,GAAG,KAAK,IAAI,EAAE;MACnB,IAAIgB,CAAC,GAAGJ,IAAI,CAACD,IAAI,CAACE,OAAO,EAAEb,GAAG,CAAC1B,KAAK,EAAE0B,GAAG,CAAC3B,GAAG,EAAE0C,CAAC,CAAC;MACjD,IAAIC,CAAC,KAAK7C,WAAW,CAAC8C,KAAK,EAAE;MAC7BjB,GAAG,GAAGA,GAAG,CAACzB,IAAI;MACdwC,CAAC,EAAE;IACL;EACF;EAEA,MAAMG,YAAYA,CAACC,SAAS,EAAkB;IAAA,IAAhBN,OAAO,GAAA5B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA6B,SAAA,GAAA7B,SAAA,MAAG,IAAI;IAC1C,IAAI8B,CAAC,GAAG,CAAC;IACT,IAAIf,GAAG,GAAG,IAAI,CAACnB,MAAM;IACrB,OAAOmB,GAAG,KAAK,IAAI,EAAE;MACnB,MAAMgB,CAAC,GAAG,MAAMG,SAAS,CAACR,IAAI,CAACE,OAAO,EAAEb,GAAG,CAAC1B,KAAK,EAAE0B,GAAG,CAAC3B,GAAG,EAAE0C,CAAC,CAAC;MAC9D,IAAIC,CAAC,KAAK7C,WAAW,CAAC8C,KAAK,EAAE;MAC7BjB,GAAG,GAAGA,GAAG,CAACzB,IAAI;MACdwC,CAAC,EAAE;IACL;EACF;EAEAK,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACvB,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACA,OAAO,IAAI,CAAChB,MAAM,CAACR,GAAG;EACxB;EAEAgD,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACxB,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACA,OAAO,IAAI,CAAChB,MAAM,CAACP,KAAK;EAC1B;EAEAgD,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAACzB,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACA,OAAO,IAAI,CAACf,KAAK,CAACT,GAAG;EACvB;EAEAkD,SAASA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC1B,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACA,OAAO,IAAI,CAACf,KAAK,CAACR,KAAK;EACzB;EAEAE,IAAIA,CAACH,GAAG,EAAE;IACR,IAAI,IAAI,CAACmC,GAAG,CAACnC,GAAG,CAAC,EAAE;MACjB,IAAI2B,GAAG,GAAG,IAAI,CAACtB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC;MAClC,IAAI2B,GAAG,CAACxB,IAAI,EACV,OAAOwB,GAAG,CAACxB,IAAI,CAACH,GAAG;IACvB;IACA,OAAO,IAAI;EACb;EAEAE,IAAIA,CAACF,GAAG,EAAE;IACR,IAAI,IAAI,CAACmC,GAAG,CAACnC,GAAG,CAAC,EAAE;MACjB,IAAI2B,GAAG,GAAG,IAAI,CAACtB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC;MAClC,IAAI2B,GAAG,CAACzB,IAAI,EACV,OAAOyB,GAAG,CAACzB,IAAI,CAACF,GAAG;IACvB;IACA,OAAO,IAAI;EACb;EAEAmD,UAAUA,CAACnD,GAAG,EAAE8B,MAAM,EAAE;IACtB,IAAIH,GAAG,GAAG,IAAI,CAACtB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACvB,GAAG,CAAC,CAAC;IAClC,IAAIoD,SAAS,GAAGtB,MAAM,GAAG,IAAI,CAACzB,KAAK,CAAC,IAAI,CAACkB,EAAE,CAACO,MAAM,CAAC,CAAC,GAAG,IAAI;IAC3D,IAAI,OAAOH,GAAG,KAAK,WAAW,EAAE;MAC9B,MAAM,IAAII,KAAK,CAAC,6BAA6B,CAAC;IAChD;IACA,IAAI,OAAOqB,SAAS,KAAK,WAAW,EAAE;MACpC,MAAM,IAAIrB,KAAK,CAAC,gDAAgD,CAAC;IACnE;IACA,IAAIqB,SAAS,KAAKzB,GAAG,CAACzB,IAAI;MAAE;MAC1B;IACF;IACA,IAAI,CAAC0B,WAAW,CAACD,GAAG,CAAC;IACrB;IACAA,GAAG,CAACzB,IAAI,GAAGkD,SAAS;IACpB,IAAI,CAAC1B,UAAU,CAACC,GAAG,CAAC;EACtB;;EAEA;EACA0B,OAAOA,CAACrD,GAAG,EAAE;IACX,IAAIsD,GAAG,GAAG,IAAI;IACd,IAAI,CAAClC,OAAO,CAAC,CAACmC,CAAC,EAAEC,CAAC,EAAEd,CAAC,KAAK;MACxB,IAAI,IAAI,CAACnB,EAAE,CAACiC,CAAC,CAAC,KAAK,IAAI,CAACjC,EAAE,CAACvB,GAAG,CAAC,EAAE;QAC/BsD,GAAG,GAAGZ,CAAC;QACP,OAAO5C,WAAW,CAAC8C,KAAK;MAC1B;MACA;IACF,CAAC,CAAC;IACF,OAAOU,GAAG;EACZ;EAEAG,SAASA,CAAA,EAAG;IACVnD,MAAM,CAACoD,IAAI,CAAC,IAAI,CAACrD,KAAK,CAAC,CAACe,OAAO,CAACoC,CAAC,IAAI;MACnC,MAAMD,CAAC,GAAG,IAAI,CAAClD,KAAK,CAACmD,CAAC,CAAC;MACvB,IAAID,CAAC,CAACrD,IAAI,KAAKqD,CAAC,EAAE;QAChB,MAAM,IAAIxB,KAAK,CAAC,gBAAgB,CAAC;MACnC;MACA,IAAIwB,CAAC,CAACpD,IAAI,KAAKoD,CAAC,EAAE;QAChB,MAAM,IAAIxB,KAAK,CAAC,gBAAgB,CAAC;MACnC;IACF,CAAC,CAAC;EACJ;AACF;AAEAjC,WAAW,CAAC8C,KAAK,GAAG;EAAC,OAAO,EAAE;AAAI,CAAC,C", "file": "/packages/ordered-dict.js", "sourcesContent": ["// This file defines an ordered dictionary abstraction that is useful for\n// maintaining a dataset backed by observe<PERSON>hanges.  It supports ordering items\n// by specifying the item they now come before.\n\n// The implementation is a dictionary that contains nodes of a doubly-linked\n// list as its values.\n\n// constructs a new element struct\n// next and prev are whole elements, not keys.\nfunction element(key, value, next, prev) {\n  return {\n    key: key,\n    value: value,\n    next: next,\n    prev: prev\n  };\n}\n\nexport class OrderedDict {\n  constructor(...args) {\n    this._dict = Object.create(null);\n    this._first = null;\n    this._last = null;\n    this._size = 0;\n\n    if (typeof args[0] === 'function') {\n      this._stringify = args.shift();\n    } else {\n      this._stringify = function (x) { return x; };\n    }\n\n    args.forEach(kv => this.putBefore(kv[0], kv[1], null));\n  }\n\n  // the \"prefix keys with a space\" thing comes from here\n  // https://github.com/documentcloud/underscore/issues/376#issuecomment-2815649\n  _k(key) {\n    return \" \" + this._stringify(key);\n  }\n\n  empty() {\n    return !this._first;\n  }\n\n  size() {\n    return this._size;\n  }\n\n  _linkEltIn(elt) {\n    if (!elt.next) {\n      elt.prev = this._last;\n      if (this._last)\n        this._last.next = elt;\n      this._last = elt;\n    } else {\n      elt.prev = elt.next.prev;\n      elt.next.prev = elt;\n      if (elt.prev)\n        elt.prev.next = elt;\n    }\n    if (this._first === null || this._first === elt.next)\n      this._first = elt;\n  }\n\n  _linkEltOut(elt) {\n    if (elt.next)\n      elt.next.prev = elt.prev;\n    if (elt.prev)\n      elt.prev.next = elt.next;\n    if (elt === this._last)\n      this._last = elt.prev;\n    if (elt === this._first)\n      this._first = elt.next;\n  }\n\n  putBefore(key, item, before) {\n    if (this._dict[this._k(key)])\n      throw new Error(\"Item \" + key + \" already present in OrderedDict\");\n    var elt = before ?\n      element(key, item, this._dict[this._k(before)]) :\n      element(key, item, null);\n    if (typeof elt.next === \"undefined\")\n      throw new Error(\"could not find item to put this one before\");\n    this._linkEltIn(elt);\n    this._dict[this._k(key)] = elt;\n    this._size++;\n  }\n\n  append(key, item) {\n    this.putBefore(key, item, null);\n  }\n\n  remove(key) {\n    var elt = this._dict[this._k(key)];\n    if (typeof elt === \"undefined\")\n      throw new Error(\"Item \" + key + \" not present in OrderedDict\");\n    this._linkEltOut(elt);\n    this._size--;\n    delete this._dict[this._k(key)];\n    return elt.value;\n  }\n\n  get(key) {\n    if (this.has(key)) {\n      return this._dict[this._k(key)].value;\n    }\n  }\n\n  has(key) {\n    return Object.prototype.hasOwnProperty.call(\n      this._dict,\n      this._k(key)\n    );\n  }\n\n  // Iterate through the items in this dictionary in order, calling\n  // iter(value, key, index) on each one.\n\n  // Stops whenever iter returns OrderedDict.BREAK, or after the last element.\n  forEach(iter, context = null) {\n    var i = 0;\n    var elt = this._first;\n    while (elt !== null) {\n      var b = iter.call(context, elt.value, elt.key, i);\n      if (b === OrderedDict.BREAK) return;\n      elt = elt.next;\n      i++;\n    }\n  }\n\n  async forEachAsync(asyncIter, context = null) {\n    let i = 0;\n    let elt = this._first;\n    while (elt !== null) {\n      const b = await asyncIter.call(context, elt.value, elt.key, i);\n      if (b === OrderedDict.BREAK) return;\n      elt = elt.next;\n      i++;\n    }\n  }\n\n  first() {\n    if (this.empty()) {\n      return;\n    }\n    return this._first.key;\n  }\n\n  firstValue() {\n    if (this.empty()) {\n      return;\n    }\n    return this._first.value;\n  }\n\n  last() {\n    if (this.empty()) {\n      return;\n    }\n    return this._last.key;\n  }\n\n  lastValue() {\n    if (this.empty()) {\n      return;\n    }\n    return this._last.value;\n  }\n\n  prev(key) {\n    if (this.has(key)) {\n      var elt = this._dict[this._k(key)];\n      if (elt.prev)\n        return elt.prev.key;\n    }\n    return null;\n  }\n\n  next(key) {\n    if (this.has(key)) {\n      var elt = this._dict[this._k(key)];\n      if (elt.next)\n        return elt.next.key;\n    }\n    return null;\n  }\n\n  moveBefore(key, before) {\n    var elt = this._dict[this._k(key)];\n    var eltBefore = before ? this._dict[this._k(before)] : null;\n    if (typeof elt === \"undefined\") {\n      throw new Error(\"Item to move is not present\");\n    }\n    if (typeof eltBefore === \"undefined\") {\n      throw new Error(\"Could not find element to move this one before\");\n    }\n    if (eltBefore === elt.next) // no moving necessary\n      return;\n    // remove from its old place\n    this._linkEltOut(elt);\n    // patch into its new place\n    elt.next = eltBefore;\n    this._linkEltIn(elt);\n  }\n\n  // Linear, sadly.\n  indexOf(key) {\n    var ret = null;\n    this.forEach((v, k, i) => {\n      if (this._k(k) === this._k(key)) {\n        ret = i;\n        return OrderedDict.BREAK;\n      }\n      return;\n    });\n    return ret;\n  }\n\n  _checkRep() {\n    Object.keys(this._dict).forEach(k => {\n      const v = this._dict[k];\n      if (v.next === v) {\n        throw new Error(\"Next is a loop\");\n      }\n      if (v.prev === v) {\n        throw new Error(\"Prev is a loop\");\n      }\n    });\n  }\n}\n\nOrderedDict.BREAK = {\"break\": true};\n"]}