{"version": 3, "sources": ["meteor://💻app/packages/react-meteor-data/index.js", "meteor://💻app/packages/react-meteor-data/useFind.ts", "meteor://💻app/packages/react-meteor-data/useSubscribe.ts", "meteor://💻app/packages/react-meteor-data/useTracker.ts", "meteor://💻app/packages/react-meteor-data/withTracker.tsx"], "names": ["React", "module", "link", "default", "v", "useTracker", "withTracker", "useFind", "useSubscribe", "Meteor", "isDevelopment", "version", "split", "console", "warn", "_typeof", "_slicedToArray", "_toConsumableArray", "export", "Mongo", "useReducer", "useMemo", "useEffect", "useRef", "Tracker", "useFindReducer", "data", "action", "type", "concat", "slice", "atIndex", "document", "doc", "fromIndex", "copy", "splice", "toIndex", "checkCursor", "cursor", "undefined", "<PERSON><PERSON><PERSON>", "_mongo", "_cursorDescription", "fetchData", "observer", "observe", "addedAt", "before", "stop", "useFindClient", "factory", "deps", "arguments", "length", "nonreactive", "_useReducer", "_useReducer2", "dispatch", "didMount", "current", "changedAt", "newDocument", "oldDocument", "removedAt", "movedTo", "_suppress_initial", "useFindServer", "_cursor$fetch", "_cursor$fetch2", "fetch", "call", "isServer", "useFindDev", "expects", "pos", "arg", "Array", "isArray", "exportDefault", "useSubscribeClient", "name", "_len", "args", "_key", "updateOnReady", "subscription", "isReady", "_Meteor", "subscribe", "apply", "ready", "useSubscribeServer", "<PERSON><PERSON><PERSON><PERSON>", "Package", "mongo", "Object", "getPrototypeOf", "prototype", "keys", "for<PERSON>ach", "key", "fur", "x", "useForceUpdate", "useTrackerNoDeps", "reactiveFn", "skipUpdate", "_useRef", "isMounted", "trackerData", "refs", "forceUpdate", "computation", "autorun", "c", "firstRun", "defer", "_refs$computation", "useTrackerWithDeps", "_useRef2", "comp", "useTrackerClient", "useTrackerServer", "_useTracker", "useTrackerDev", "_extends", "forwardRef", "memo", "options", "Component", "getMeteorData", "WithTracker", "props", "ref", "createElement", "_options$pure", "pure"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,KAAK;AAACC,MAAM,CAACC,IAAI,CAAC,OAAO,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACJ,KAAK,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAACH,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;EAACG,UAAU,EAAC;AAAY,CAAC,EAAC,CAAC,CAAC;AAACJ,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;EAACI,WAAW,EAAC;AAAa,CAAC,EAAC,CAAC,CAAC;AAACL,MAAM,CAACC,IAAI,CAAC,WAAW,EAAC;EAACK,OAAO,EAAC;AAAS,CAAC,EAAC,CAAC,CAAC;AAACN,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;EAACM,YAAY,EAAC;AAAc,CAAC,EAAC,CAAC,CAAC;AAGnS,IAAIC,MAAM,CAACC,aAAa,EAAE;EACxB,IAAMN,CAAC,GAAGJ,KAAK,CAACW,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC;EAClC,IAAIR,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAKA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,EAAE;IACzCS,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;EACvE;AACF,C;;;;;;;;;;;ACRA,IAAAC,OAAS;AAAAd,MAAQ,CAAAC,IAAM,gCAAe;EAAAC,OAAA,WAAAA,CAAAC,CAAA;IAAAW,OAAA,GAAAX,CAAA;EAAA;AAAA;AAAA,IAAAY,cAAA;AAAAf,MAAA,CAAAC,IAAA;EAAAC,OAAA,WAAAA,CAAAC,CAAA;IAAAY,cAAA,GAAAZ,CAAA;EAAA;AAAA;AAAA,IAAAa,kBAAA;AAAAhB,MAAA,CAAAC,IAAA;EAAAC,OAAA,WAAAA,CAAAC,CAAA;IAAAa,kBAAA,GAAAb,CAAA;EAAA;AAAA;AAAtCH,MAAA,CAAOiB,MAAE;EAAMX,OAAE,EAAM,SAAAA,CAAA;IAAA,OAAeA,OAAA;EAAA;AAAA;AAAA,IAAAE,MAAA;AAAAR,MAAA,CAAAC,IAAA;EAAAO,MAAA,WAAAA,CAAAL,CAAA;IAAAK,MAAA,GAAAL,CAAA;EAAA;AAAA;AAAA,IAAAe,KAAA;AAAAlB,MAAA,CAAAC,IAAA;EAAAiB,KAAA,WAAAA,CAAAf,CAAA;IAAAe,KAAA,GAAAf,CAAA;EAAA;AAAA;AAAA,IAAAgB,UAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,MAAA;AAAAtB,MAAA,CAAAC,IAAA;EAAAkB,UAAA,WAAAA,CAAAhB,CAAA;IAAAgB,UAAA,GAAAhB,CAAA;EAAA;EAAAiB,OAAA,WAAAA,CAAAjB,CAAA;IAAAiB,OAAA,GAAAjB,CAAA;EAAA;EAAAkB,SAAA,WAAAA,CAAAlB,CAAA;IAAAkB,SAAA,GAAAlB,CAAA;EAAA;EAAAmB,MAAA,WAAAA,CAAAnB,CAAA;IAAAmB,MAAA,GAAAnB,CAAA;EAAA;AAAA;AAAA,IAAAoB,OAAA;AAAAvB,MAAA,CAAAC,IAAA;EAAAsB,OAAA,WAAAA,CAAApB,CAAA;IAAAoB,OAAA,GAAApB,CAAA;EAAA;AAAA;AAYtC,IAAMqB,cAAc,GAAG,SAAAA,CAAIC,IAAS,EAAEC,MAAyB,EAAS;EACtE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,SAAS;MACZ,OAAOD,MAAM,CAACD,IAAI;IACpB,KAAK,SAAS;MACZ,UAAAG,MAAA,CAAAZ,kBAAA,CACKS,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,MAAM,CAACI,OAAO,CAAC,IAChCJ,MAAM,CAACK,QAAQ,GAAAf,kBAAA,CACZS,IAAI,CAACI,KAAK,CAACH,MAAM,CAACI,OAAO,CAAC;IAEjC,KAAK,WAAW;MACd,UAAAF,MAAA,CAAAZ,kBAAA,CACKS,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,MAAM,CAACI,OAAO,CAAC,IAChCJ,MAAM,CAACK,QAAQ,GAAAf,kBAAA,CACZS,IAAI,CAACI,KAAK,CAACH,MAAM,CAACI,OAAO,GAAG,CAAC,CAAC;IAErC,KAAK,WAAW;MACd,UAAAF,MAAA,CAAAZ,kBAAA,CACKS,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,MAAM,CAACI,OAAO,CAAC,GAAAd,kBAAA,CAC7BS,IAAI,CAACI,KAAK,CAACH,MAAM,CAACI,OAAO,GAAG,CAAC,CAAC;IAErC,KAAK,SAAS;MACZ,IAAME,GAAG,GAAGP,IAAI,CAACC,MAAM,CAACO,SAAS,CAAC;MAClC,IAAMC,IAAI,MAAAN,MAAA,CAAAZ,kBAAA,CACLS,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,MAAM,CAACO,SAAS,CAAC,GAAAjB,kBAAA,CAC/BS,IAAI,CAACI,KAAK,CAACH,MAAM,CAACO,SAAS,GAAG,CAAC,CAAC,EACpC;MACDC,IAAI,CAACC,MAAM,CAACT,MAAM,CAACU,OAAO,EAAE,CAAC,EAAEJ,GAAG,CAAC;MACnC,OAAOE,IAAI;EACf;AACF,CAAC;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMG,WAAW,GAAG,SAAAA,CAAIC,MAA8F,EAAI;EACxH,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKC,SAAS,IAAI,EAAED,MAAM,YAAYpB,KAAK,CAACsB,MAAM,CAAC,IAC5E,EAAEF,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACI,kBAAkB,CAAC,EAAE;IACjD9B,OAAO,CAACC,IAAI,CACV,yDAAyD,GACvD,oDAAoD,CACvD;EACH;AACF,CAAC;AAED;AACA,IAAM8B,SAAS,GAAG,SAAAA,CAAIL,MAAuB,EAAI;EAC/C,IAAMb,IAAI,GAAQ,EAAE;EACpB,IAAMmB,QAAQ,GAAGN,MAAM,CAACO,OAAO,CAAC;IAC9BC,OAAO,WAAAA,CAAEf,QAAQ,EAAED,OAAO,EAAEiB,MAAM;MAChCtB,IAAI,CAACU,MAAM,CAACL,OAAO,EAAE,CAAC,EAAEC,QAAQ,CAAC;IACnC;GACD,CAAC;EACFa,QAAQ,CAACI,IAAI,EAAE;EACf,OAAOvB,IAAI;AACb,CAAC;AAED,IAAMwB,aAAa,GAAG,SAAAA,CAAUC,OAAmD,EAA+B;EAAA,IAA7BC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAuB,EAAE;EAC5G,IAAMd,MAAM,GAAGlB,OAAO,CAAC,YAAK;IAC1B;IACA;IACA,IAAMkB,MAAM,GAAGf,OAAO,CAAC+B,WAAW,CAACJ,OAAO,CAAC;IAC3C,IAAI1C,MAAM,CAACC,aAAa,EAAE;MACxB4B,WAAW,CAACC,MAAM,CAAC;IACrB;IACA,OAAOA,MAAM;EACf,CAAC,EAAEa,IAAI,CAAC;EAER,IAAAI,WAAA,GAAyBpC,UAAU,CACjCK,cAAc,EACd,IAAI,EACJ,YAAK;MACH,IAAI,EAAEc,MAAM,YAAYpB,KAAK,CAACsB,MAAM,CAAC,EAAE;QACrC,OAAO,EAAE;MACX;MAEA,OAAOG,SAAS,CAACL,MAAM,CAAC;IAC1B,CAAC,CACF;IAAAkB,YAAA,GAAAzC,cAAA,CAAAwC,WAAA;IAVM9B,IAAI,GAAA+B,YAAA;IAAEC,QAAQ,GAAAD,YAAA;EAYrB;EACA;EACA,IAAME,QAAQ,GAAGpC,MAAM,CAAC,KAAK,CAAC;EAE9BD,SAAS,CAAC,YAAK;IACb;IACA,IAAIqC,QAAQ,CAACC,OAAO,EAAE;MACpB,IAAI,EAAErB,MAAM,YAAYpB,KAAK,CAACsB,MAAM,CAAC,EAAE;QACrC;MACF;MAEA,IAAMf,KAAI,GAAGkB,SAAS,CAACL,MAAM,CAAC;MAC9BmB,QAAQ,CAAC;QAAE9B,IAAI,EAAE,SAAS;QAAEF,IAAI,EAAJA;MAAI,CAAE,CAAC;IACrC,CAAC,MAAM;MACLiC,QAAQ,CAACC,OAAO,GAAG,IAAI;IACzB;IAEA,IAAI,EAAErB,MAAM,YAAYpB,KAAK,CAACsB,MAAM,CAAC,EAAE;MACrC;IACF;IAEA,IAAMI,QAAQ,GAAGN,MAAM,CAACO,OAAO,CAAC;MAC9BC,OAAO,WAAAA,CAAEf,QAAQ,EAAED,OAAO,EAAEiB,MAAM;QAChCU,QAAQ,CAAC;UAAE9B,IAAI,EAAE,SAAS;UAAEI,QAAQ,EAARA,QAAQ;UAAED,OAAO,EAAPA;QAAO,CAAE,CAAC;MAClD,CAAC;MACD8B,SAAS,WAAAA,CAAEC,WAAW,EAAEC,WAAW,EAAEhC,OAAO;QAC1C2B,QAAQ,CAAC;UAAE9B,IAAI,EAAE,WAAW;UAAEI,QAAQ,EAAE8B,WAAW;UAAE/B,OAAO,EAAPA;QAAO,CAAE,CAAC;MACjE,CAAC;MACDiC,SAAS,WAAAA,CAAED,WAAW,EAAEhC,OAAO;QAC7B2B,QAAQ,CAAC;UAAE9B,IAAI,EAAE,WAAW;UAAEG,OAAO,EAAPA;QAAO,CAAE,CAAC;MAC1C,CAAC;MACDkC,OAAO,WAAAA,CAAEjC,QAAQ,EAAEE,SAAS,EAAEG,OAAO,EAAEW,MAAM;QAC3CU,QAAQ,CAAC;UAAE9B,IAAI,EAAE,SAAS;UAAEM,SAAS,EAATA,SAAS;UAAEG,OAAO,EAAPA;QAAO,CAAE,CAAC;MACnD,CAAC;MACD;MACA6B,iBAAiB,EAAE;KACpB,CAAC;IAEF,OAAO,YAAK;MACVrB,QAAQ,CAACI,IAAI,EAAE;IACjB,CAAC;EACH,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EAEZ,OAAOA,MAAM,GAAGb,IAAI,GAAGa,MAAM;AAC/B,CAAC;AAED,IAAM4B,aAAa,GAAG,SAAAA,CAAUhB,OAAiD,EAAEC,IAAoB;EAAA,OACrG5B,OAAO,CAAC+B,WAAW,CAAC,YAAK;IAAA,IAAAa,aAAA,EAAAC,cAAA;IACvB,IAAM9B,MAAM,GAAGY,OAAO,EAAE;IACxB,IAAI1C,MAAM,CAACC,aAAa,EAAE4B,WAAW,CAACC,MAAM,CAAC;IAC7C,QAAA6B,aAAA,GAAO7B,MAAM,aAANA,MAAM,wBAAA8B,cAAA,GAAN9B,MAAM,CAAE+B,KAAK,cAAAD,cAAA,uBAAbA,cAAA,CAAAE,IAAA,CAAAhC,MAAe,CAAE,cAAA6B,aAAA,cAAAA,aAAA,GAAI,IAAI;EAClC,CAAC,CAAC;AAAA,CACH;AAEM,IAAM7D,OAAO,GAAGE,MAAM,CAAC+D,QAAQ,GAClCL,aAAa,GACbjB,aAAa;AAEjB,SAASuB,UAAUA,CAAWtB,OAAmD,EAA2B;EAAA,IAAzBC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAuB,EAAE;EAC1G,SAASvC,IAAIA,CAAE4D,OAAe,EAAEC,GAAW,EAAEC,GAAW,EAAEhD,IAAY;IACpEf,OAAO,CAACC,IAAI,CACV,iCAA+B4D,OAAO,iBAAaC,GAAG,yBAC9CC,GAAG,4BAAwBhD,IAAI,QAAK,CAC7C;EACH;EAEA,IAAI,OAAOuB,OAAO,KAAK,UAAU,EAAE;IACjCrC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,EAAEqC,OAAO,CAAC;EAChD;EAEA,IAAI,CAACC,IAAI,IAAI,CAACyB,KAAK,CAACC,OAAO,CAAC1B,IAAI,CAAC,EAAE;IACjCtC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAAC,OAAA,CAASqC,IAAI,EAAC;EAC3C;EAEA,OAAO7C,OAAO,CAAC4C,OAAO,EAAEC,IAAI,CAAC;AAC/B;AA3KAnD,MAAA,CAAO8E,aAAU,CA6KFtE,MAAM,CAACC,aAAa,GAC/B+D,UAAU,GACVlE,OA/KmB,E;;;;;;;;;;;ACAvBN,MAAA,CAAOiB,MAAE;EAAMV,YAAQ,WAAAA,CAAA,EAAe;IAAA,OAAAA,YAAA;EAAA;AAAA;AAAA,IAAAC,MAAA;AAAAR,MAAA,CAAAC,IAAA;EAAAO,MAAA,WAAAA,CAAAL,CAAA;IAAAK,MAAA,GAAAL,CAAA;EAAA;AAAA;AAAA,IAAAC,UAAA;AAAAJ,MAAA,CAAAC,IAAA;EAAAG,UAAA,WAAAA,CAAAD,CAAA;IAAAC,UAAA,GAAAD,CAAA;EAAA;AAAA;AAGtC,IAAM4E,kBAAkB,GAAG,SAAAA,CAACC,IAAa,EAAmC;EAAA,SAAAC,IAAA,GAAA7B,SAAA,CAAAC,MAAA,EAA9B6B,IAAW,OAAAN,KAAA,CAAAK,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAAXD,IAAW,CAAAC,IAAA,QAAA/B,SAAA,CAAA+B,IAAA;EAAA;EACvD,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,YAAuC;EAE3C,IAAMC,OAAO,GAAGlF,UAAU,CAAC,YAAK;IAAA,IAAAmF,OAAA;IAC9B,IAAI,CAACP,IAAI,EAAE,OAAO,IAAI;IAEtBK,YAAY,GAAG,CAAAE,OAAA,GAAA/E,MAAM,EAACgF,SAAS,CAAAC,KAAA,CAAAF,OAAA,GAACP,IAAI,EAAApD,MAAA,CAAKsD,IAAI,EAAC;IAE9C,OAAOG,YAAY,CAACK,KAAK,EAAE;EAC7B,CAAC,EAAE;IAAA,OAAO,CAACN,aAAa;EAAA,CAAC,CAAC;EAE1B,OAAO,YAAK;IACVA,aAAa,GAAG,IAAI;IACpB,OAAO,CAACE,OAAO;EACjB,CAAC;AACH,CAAC;AAED,IAAMK,kBAAkB,GAAG,SAAAA,CAACX,IAAa;EAAA,OACvC;IAAA,OAAM,KAAK;EAAA;AAAA,CACZ;AAEM,IAAMzE,YAAY,GAAGC,MAAM,CAAC+D,QAAQ,GACvCoB,kBAAkB,GAClBZ,kBAAkB,C;;;;;;;;;;;AC1BtB,IAAAjE,OAAS;AAAAd,MAAQ,CAAAC,IAAM,gCAAgB;EAAAC,OAAA,WAAAA,CAAAC,CAAA;IAAAW,OAAA,GAAAX,CAAA;EAAA;AAAA;AAAvCH,MAAA,CAAOiB,MAAE;EAAMb,UAAQ,WAAAA,CAAA;IAAA,OAAgBA,UAAA;EAAA;AAAA;AAAA,IAAAI,MAAA;AAAAR,MAAA,CAAAC,IAAA;EAAAO,MAAA,WAAAA,CAAAL,CAAA;IAAAK,MAAA,GAAAL,CAAA;EAAA;AAAA;AAAA,IAAAoB,OAAA;AAAAvB,MAAA,CAAAC,IAAA;EAAAsB,OAAA,WAAAA,CAAApB,CAAA;IAAAoB,OAAA,GAAApB,CAAA;EAAA;AAAA;AAAA,IAAAgB,UAAA,EAAAE,SAAA,EAAAC,MAAA,EAAAF,OAAA;AAAApB,MAAA,CAAAC,IAAA;EAAAkB,UAAA,WAAAA,CAAAhB,CAAA;IAAAgB,UAAA,GAAAhB,CAAA;EAAA;EAAAkB,SAAA,WAAAA,CAAAlB,CAAA;IAAAkB,SAAA,GAAAlB,CAAA;EAAA;EAAAmB,MAAA,WAAAA,CAAAnB,CAAA;IAAAmB,MAAA,GAAAnB,CAAA;EAAA;EAAAiB,OAAA,WAAAA,CAAAjB,CAAA;IAAAiB,OAAA,GAAAjB,CAAA;EAAA;AAAA;AAIvC;AACA,SAASkC,WAAWA,CAAEZ,IAAS;EAC7B,IAAImE,UAAU,GAAG,KAAK;EACtB,IAAIC,OAAO,CAACC,KAAK,IAAID,OAAO,CAACC,KAAK,CAAC5E,KAAK,IAAIO,IAAI,IAAIX,OAAA,CAAOW,IAAI,MAAK,QAAQ,EAAE;IAC5E,IAAIA,IAAI,YAAYoE,OAAO,CAACC,KAAK,CAAC5E,KAAK,CAACsB,MAAM,EAAE;MAC9CoD,UAAU,GAAG,IAAI;IACnB,CAAC,MAAM,IAAIG,MAAM,CAACC,cAAc,CAACvE,IAAI,CAAC,KAAKsE,MAAM,CAACE,SAAS,EAAE;MAC3DF,MAAM,CAACG,IAAI,CAACzE,IAAI,CAAC,CAAC0E,OAAO,CAAC,UAACC,GAAG,EAAI;QAChC,IAAI3E,IAAI,CAAC2E,GAAG,CAAC,YAAYP,OAAO,CAACC,KAAK,CAAC5E,KAAK,CAACsB,MAAM,EAAE;UACnDoD,UAAU,GAAG,IAAI;QACnB;MACF,CAAC,CAAC;IACJ;EACF;EACA,IAAIA,UAAU,EAAE;IACdhF,OAAO,CAACC,IAAI,CACV,+DAA+D,GAC7D,6DAA6D,GAC7D,+CAA+C,CAClD;EACH;AACF;AAEA;AACA;AACA,IAAMwF,GAAG,GAAG,SAAAA,CAACC,CAAS;EAAA,OAAaA,CAAC,GAAG,CAAC;AAAA;AACxC,IAAMC,cAAc,GAAG,SAAAA,CAAA;EAAA,OAAMpF,UAAU,CAACkF,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAgBlD,IAAMG,gBAAgB,GAAG,SAAAA,CAAUC,UAA0B,EAAuC;EAAA,IAArCC,UAAA,GAAAtD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAA6B,IAAI;EAC9F,IAAAuD,OAAA,GAA0BrF,MAAM,CAAc;MAC5CsF,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE;KACd,CAAC;IAHeC,IAAI,GAAAH,OAAA,CAAbhD,OAAO;EAIf,IAAMoD,WAAW,GAAGR,cAAc,EAAE;EAEpC;EACA,IAAIO,IAAI,CAACE,WAAW,EAAE;IACpBF,IAAI,CAACE,WAAW,CAAChE,IAAI,EAAE;IACvB;IACA,OAAO8D,IAAI,CAACE,WAAW;EACzB;EAEA;EACA;EACA;EACA;EACA;EACAzF,OAAO,CAAC+B,WAAW,CAAC;IAAA,OAAM/B,OAAO,CAAC0F,OAAO,CAAC,UAACC,CAAsB,EAAI;MACnEJ,IAAI,CAACE,WAAW,GAAGE,CAAC;MACpB,IAAMzF,IAAI,GAAGgF,UAAU,CAACS,CAAC,CAAC;MAC1B,IAAIA,CAAC,CAACC,QAAQ,EAAE;QACd;QACAL,IAAI,CAACD,WAAW,GAAGpF,IAAI;MACzB,CAAC,MAAM,IAAI,CAACiF,UAAU,IAAI,CAACA,UAAU,CAACI,IAAI,CAACD,WAAW,EAAEpF,IAAI,CAAC,EAAE;QAC7D;QACAsF,WAAW,EAAE;MACf;IACF,CAAC,CAAC;EAAA,EAAC;EAEH;EACA,IAAI,CAACD,IAAI,CAACF,SAAS,EAAE;IACnBpG,MAAM,CAAC4G,KAAK,CAAC,YAAK;MAChB,IAAI,CAACN,IAAI,CAACF,SAAS,IAAIE,IAAI,CAACE,WAAW,EAAE;QACvCF,IAAI,CAACE,WAAW,CAAChE,IAAI,EAAE;QACvB,OAAO8D,IAAI,CAACE,WAAW;MACzB;IACF,CAAC,CAAC;EACJ;EAEA3F,SAAS,CAAC,YAAK;IACb;IACAyF,IAAI,CAACF,SAAS,GAAG,IAAI;IAErB;IACA;IACA;IACA,IAAI,CAACE,IAAI,CAACE,WAAW,EAAE;MACrB;MACA;MACA,IAAI,CAACN,UAAU,EAAE;QACfK,WAAW,EAAE;MACf,CAAC,MAAM;QACLxF,OAAO,CAAC+B,WAAW,CAAC;UAAA,OAAM/B,OAAO,CAAC0F,OAAO,CAAC,UAACC,CAAsB,EAAI;YACnE,IAAMzF,IAAI,GAAGgF,UAAU,CAACS,CAAC,CAAC;YAC1BJ,IAAI,CAACE,WAAW,GAAGE,CAAC;YACpB,IAAI,CAACR,UAAU,CAACI,IAAI,CAACD,WAAW,EAAEpF,IAAI,CAAC,EAAE;cACvC;cACAsF,WAAW,EAAE;YACf;UACF,CAAC,CAAC;QAAA,EAAC;MACL;IACF;IAEA;IACA,OAAO,YAAK;MAAA,IAAAM,iBAAA;MACV,CAAAA,iBAAA,GAAAP,IAAI,CAACE,WAAW,cAAAK,iBAAA,uBAAhBA,iBAAA,CAAkBrE,IAAI,EAAE;MACxB,OAAO8D,IAAI,CAACE,WAAW;MACvBF,IAAI,CAACF,SAAS,GAAG,KAAK;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOE,IAAI,CAACD,WAAW;AACzB,CAAC;AAED,IAAMS,kBAAkB,GAAG,SAAAA,CAAUb,UAA0B,EAAEtD,IAAoB,EAA0C;EAAA,IAAxCuD,UAAA,GAAAtD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAA6B,IAAI;EACtH,IAAM2D,WAAW,GAAGR,cAAc,EAAE;EAEpC,IAAAgB,QAAA,GAA0BjG,MAAM,CAK7B;MAAEmF,UAAU,EAAVA;IAAU,CAAE,CAAC;IALDK,IAAI,GAAAS,QAAA,CAAb5D,OAAO;EAOf;EACAmD,IAAI,CAACL,UAAU,GAAGA,UAAU;EAE5BrF,OAAO,CAAC,YAAK;IACX;IACA;IACA,IAAMoG,IAAI,GAAGjG,OAAO,CAAC+B,WAAW,CAC9B;MAAA,OAAM/B,OAAO,CAAC0F,OAAO,CAAC,UAACC,CAAsB,EAAI;QAC/C,IAAMzF,IAAI,GAAGqF,IAAI,CAACL,UAAU,EAAE;QAC9B,IAAIS,CAAC,CAACC,QAAQ,EAAE;UACdL,IAAI,CAACrF,IAAI,GAAGA,IAAI;QAClB,CAAC,MAAM,IAAI,CAACiF,UAAU,IAAI,CAACA,UAAU,CAACI,IAAI,CAACrF,IAAI,EAAEA,IAAI,CAAC,EAAE;UACtDqF,IAAI,CAACrF,IAAI,GAAGA,IAAI;UAChBsF,WAAW,EAAE;QACf;MACF,CAAC,CAAC;IAAA,EACH;IAED;IACA;IACA;IACA;IACA,IAAID,IAAI,CAACU,IAAI,EAAEV,IAAI,CAACU,IAAI,CAACxE,IAAI,EAAE;IAE/B;IACA;IACA;IACA8D,IAAI,CAACU,IAAI,GAAGA,IAAI;IAChB;IACAhH,MAAM,CAAC4G,KAAK,CAAC,YAAK;MAChB,IAAI,CAACN,IAAI,CAACF,SAAS,IAAIE,IAAI,CAACU,IAAI,EAAE;QAChCV,IAAI,CAACU,IAAI,CAACxE,IAAI,EAAE;QAChB,OAAO8D,IAAI,CAACU,IAAI;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAErE,IAAI,CAAC;EAER9B,SAAS,CAAC,YAAK;IACb;IACAyF,IAAI,CAACF,SAAS,GAAG,IAAI;IAErB,IAAI,CAACE,IAAI,CAACU,IAAI,EAAE;MACdV,IAAI,CAACU,IAAI,GAAGjG,OAAO,CAAC+B,WAAW,CAC7B;QAAA,OAAM/B,OAAO,CAAC0F,OAAO,CAAC,UAACC,CAAC,EAAI;UAC1B,IAAMzF,IAAI,GAAMqF,IAAI,CAACL,UAAU,CAACS,CAAC,CAAC;UAClC,IAAI,CAACR,UAAU,IAAI,CAACA,UAAU,CAACI,IAAI,CAACrF,IAAI,EAAEA,IAAI,CAAC,EAAE;YAC/CqF,IAAI,CAACrF,IAAI,GAAGA,IAAI;YAChBsF,WAAW,EAAE;UACf;QACF,CAAC,CAAC;MAAA,EACH;IACH;IAEA,OAAO,YAAK;MACVD,IAAI,CAACU,IAAI,CAACxE,IAAI,EAAE;MAChB,OAAO8D,IAAI,CAACU,IAAI;MAChBV,IAAI,CAACF,SAAS,GAAG,KAAK;IACxB,CAAC;EACH,CAAC,EAAEzD,IAAI,CAAC;EAER,OAAO2D,IAAI,CAACrF,IAAS;AACvB,CAAC;AAID,SAASgG,gBAAgBA,CAAWhB,UAA0B,EAAiF;EAAA,IAA/EtD,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAwC,IAAI;EAAA,IAAEsD,UAAA,GAAAtD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAA6B,IAAI;EAC7I,IAAID,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKZ,SAAS,IAAI,CAACqC,KAAK,CAACC,OAAO,CAAC1B,IAAI,CAAC,EAAE;IAC/D,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MAC9BuD,UAAU,GAAGvD,IAAI;IACnB;IACA,OAAOqD,gBAAgB,CAACC,UAAU,EAAEC,UAAU,CAAC;EACjD,CAAC,MAAM;IACL,OAAOY,kBAAkB,CAACb,UAAU,EAAEtD,IAAI,EAAEuD,UAAU,CAAC;EACzD;AACF;AAEA,IAAMgB,gBAAgB,GAA4B,SAAAA,CAACjB,UAAU,EAAI;EAC/D,OAAOlF,OAAO,CAAC+B,WAAW,CAACmD,UAAU,CAAC;AACxC,CAAC;AAED;AACA;AACA,IAAMkB,WAAW,GAAGnH,MAAM,CAAC+D,QAAQ,GAC/BmD,gBAAgB,GAChBD,gBAAgB;AAEpB,SAASG,aAAaA,CAAEnB,UAAU,EAAgC;EAAA,IAA9BtD,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAG,IAAI;EAAA,IAAEsD,UAAU,GAAAtD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAG,IAAI;EAChE,SAASvC,IAAIA,CAAE4D,OAAe,EAAEC,GAAW,EAAEC,GAAW,EAAEhD,IAAY;IACpEf,OAAO,CAACC,IAAI,CACV,oCAAkC4D,OAAO,iBAAaC,GAAG,yBACjDC,GAAG,4BAAwBhD,IAAI,QAAK,CAC7C;EACH;EAEA,IAAI,OAAO8E,UAAU,KAAK,UAAU,EAAE;IACpC5F,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE4F,UAAU,CAAC;EACnD;EAEA,IAAItD,IAAI,IAAIuD,UAAU,IAAI,CAAC9B,KAAK,CAACC,OAAO,CAAC1B,IAAI,CAAC,IAAI,OAAOuD,UAAU,KAAK,UAAU,EAAE;IAClF7F,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,kBAAkB,EAAAC,OAAA,CAC9CqC,IAAI,YAAArC,OAAA,CAAa4F,UAAU,CAAE,CAAC;EAC5C,CAAC,MAAM;IACL,IAAIvD,IAAI,IAAI,CAACyB,KAAK,CAACC,OAAO,CAAC1B,IAAI,CAAC,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MAC9DtC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,oBAAoB,EAAAC,OAAA,CAASqC,IAAI,EAAC;IACrE;IACA,IAAIuD,UAAU,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;MAClD7F,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,EAAAC,OAAA,CAAS4F,UAAU,EAAC;IAC1D;EACF;EAEA,IAAMjF,IAAI,GAAGkG,WAAW,CAAClB,UAAU,EAAEtD,IAAI,EAAEuD,UAAU,CAAC;EACtDrE,WAAW,CAACZ,IAAI,CAAC;EACjB,OAAOA,IAAI;AACb;AAEO,IAAMrB,UAAU,GAAGI,MAAM,CAACC,aAAa,GAC1CmH,aAAwC,GACxCD,WAAW,C;;;;;;;;;;;AC1Pf,IAAAE,QAAY;AAAA7H,MAAI,CAAAC,IAAA,iCAAgC;EAAAC,OAAA,WAAAA,CAAAC,CAAA;IAAA0H,QAAA,GAAA1H,CAAA;EAAA;AAAA;AAAhDH,MAAA,CAAOiB,MAAK,CAAE;EAAAZ,WAAY,WAAAA,CAAA,EAAQ;IAAA,OAAMA,WAAQ;EAAA;AAAA;AAAA,IAAAN,KAAA,EAAA+H,UAAA,EAAAC,IAAA;AAAA/H,MAAA,CAAAC,IAAA;EAAA,oBAAAC,CAAAC,CAAA;IAAAJ,KAAA,GAAAI,CAAA;EAAA;EAAA2H,UAAA,WAAAA,CAAA3H,CAAA;IAAA2H,UAAA,GAAA3H,CAAA;EAAA;EAAA4H,IAAA,WAAAA,CAAA5H,CAAA;IAAA4H,IAAA,GAAA5H,CAAA;EAAA;AAAA;AAAA,IAAAC,UAAA;AAAAJ,MAAA,CAAAC,IAAA;EAAAG,UAAA,WAAAA,CAAAD,CAAA;IAAAC,UAAA,GAAAD,CAAA;EAAA;AAAA;AAUzC,IAAME,WAAW,GAAG,SAAAA,CAAC2H,OAAqC,EAAI;EACnE,OAAO,UAACC,SAA8B,EAAI;IACxC,IAAMC,aAAa,GAAG,OAAOF,OAAO,KAAK,UAAU,GAC/CA,OAAO,GACPA,OAAO,CAACE,aAAa;IAEzB,IAAMC,WAAW,gBAAGL,UAAU,CAAC,UAACM,KAAK,EAAEC,GAAG,EAAI;MAC5C,IAAM5G,IAAI,GAAGrB,UAAU,CACrB;QAAA,OAAM8H,aAAa,CAACE,KAAK,CAAC,IAAI,EAAE;MAAA,GAC/BJ,OAA2B,CAACtB,UAAU,CACxC;MACD,oBACE3G,KAAA,CAAAuI,aAAA,CAACL,SAAS,EAAAJ,QAAA;QAACQ,GAAG,EAAEA;MAAI,GAAKD,KAAK,EAAM3G,IAAI,CAAC,CAAG;IAEhD,CAAC,CAAC;IAEF,IAAA8G,aAAA,GAAwBP,OAA0B,CAA1CQ,IAAI;MAAJA,IAAI,GAAAD,aAAA,cAAG,IAAI,GAAAA,aAAA;IACnB,OAAOC,IAAI,gBAAGT,IAAI,CAACI,WAAW,CAAC,GAAGA,WAAW;EAC/C,CAAC;AACH,CAAC,C", "file": "/packages/react-meteor-data.js", "sourcesContent": ["/* global Meteor*/\nimport React from 'react';\n\nif (Meteor.isDevelopment) {\n  const v = React.version.split('.');\n  if (v[0] < 16 || (v[0] == 16 && v[1] < 8)) {\n    console.warn('react-meteor-data 2.x requires React version >= 16.8.');\n  }\n}\n\nexport { useTracker } from './useTracker';\nexport { withTracker } from './withTracker.tsx';\nexport { useFind } from './useFind';\nexport { useSubscribe } from './useSubscribe';", "import { Meteor } from 'meteor/meteor'\nimport { Mongo } from 'meteor/mongo'\nimport { useReducer, useMemo, useEffect, Reducer, DependencyList, useRef } from 'react'\nimport { Tracker } from 'meteor/tracker'\n\ntype useFindActions<T> =\n  | { type: 'refresh', data: T[] }\n  | { type: 'addedAt', document: T, atIndex: number }\n  | { type: 'changedAt', document: T, atIndex: number }\n  | { type: 'removedAt', atIndex: number }\n  | { type: 'movedTo', fromIndex: number, toIndex: number }\n\nconst useFindReducer = <T>(data: T[], action: useFindActions<T>): T[] => {\n  switch (action.type) {\n    case 'refresh':\n      return action.data\n    case 'addedAt':\n      return [\n        ...data.slice(0, action.atIndex),\n        action.document,\n        ...data.slice(action.atIndex)\n      ]\n    case 'changedAt':\n      return [\n        ...data.slice(0, action.atIndex),\n        action.document,\n        ...data.slice(action.atIndex + 1)\n      ]\n    case 'removedAt':\n      return [\n        ...data.slice(0, action.atIndex),\n        ...data.slice(action.atIndex + 1)\n      ]\n    case 'movedTo':\n      const doc = data[action.fromIndex]\n      const copy = [\n        ...data.slice(0, action.fromIndex),\n        ...data.slice(action.fromIndex + 1)\n      ]\n      copy.splice(action.toIndex, 0, doc)\n      return copy\n  }\n}\n\n// Check for valid Cursor or null.\n// On client, we should have a Mongo.Cursor (defined in\n// https://github.com/meteor/meteor/blob/devel/packages/minimongo/cursor.js and\n// https://github.com/meteor/meteor/blob/devel/packages/mongo/collection.js).\n// On server, however, we instead get a private Cursor type from\n// https://github.com/meteor/meteor/blob/devel/packages/mongo/mongo_driver.js\n// which has fields _mongo and _cursorDescription.\nconst checkCursor = <T>(cursor: Mongo.Cursor<T> | Partial<{ _mongo: any, _cursorDescription: any }> | undefined | null) => {\n  if (cursor !== null && cursor !== undefined && !(cursor instanceof Mongo.Cursor) &&\n      !(cursor._mongo && cursor._cursorDescription)) {\n    console.warn(\n      'Warning: useFind requires an instance of Mongo.Cursor. '\n      + 'Make sure you do NOT call .fetch() on your cursor.'\n    );\n  }\n}\n\n// Synchronous data fetch. It uses cursor observing instead of cursor.fetch() because synchronous fetch will be deprecated.\nconst fetchData = <T>(cursor: Mongo.Cursor<T>) => {\n  const data: T[] = []\n  const observer = cursor.observe({\n    addedAt (document, atIndex, before) {\n      data.splice(atIndex, 0, document)\n    },\n  })\n  observer.stop()\n  return data\n}\n\nconst useFindClient = <T = any>(factory: () => (Mongo.Cursor<T> | undefined | null), deps: DependencyList = []) => {\n  const cursor = useMemo(() => {\n    // To avoid creating side effects in render, opt out\n    // of Tracker integration altogether.\n    const cursor = Tracker.nonreactive(factory);\n    if (Meteor.isDevelopment) {\n      checkCursor(cursor)\n    }\n    return cursor\n  }, deps)\n\n  const [data, dispatch] = useReducer<Reducer<T[], useFindActions<T>>, null>(\n    useFindReducer,\n    null,\n    () => {\n      if (!(cursor instanceof Mongo.Cursor)) {\n        return []\n      }\n\n      return fetchData(cursor)\n    }\n  )\n\n  // Store information about mounting the component.\n  // It will be used to run code only if the component is updated.\n  const didMount = useRef(false)\n\n  useEffect(() => {\n    // Fetch intitial data if cursor was changed.\n    if (didMount.current) {\n      if (!(cursor instanceof Mongo.Cursor)) {\n        return\n      }\n\n      const data = fetchData(cursor)\n      dispatch({ type: 'refresh', data })\n    } else {\n      didMount.current = true\n    }\n\n    if (!(cursor instanceof Mongo.Cursor)) {\n      return\n    }\n\n    const observer = cursor.observe({\n      addedAt (document, atIndex, before) {\n        dispatch({ type: 'addedAt', document, atIndex })\n      },\n      changedAt (newDocument, oldDocument, atIndex) {\n        dispatch({ type: 'changedAt', document: newDocument, atIndex })\n      },\n      removedAt (oldDocument, atIndex) {\n        dispatch({ type: 'removedAt', atIndex })\n      },\n      movedTo (document, fromIndex, toIndex, before) {\n        dispatch({ type: 'movedTo', fromIndex, toIndex })\n      },\n      // @ts-ignore\n      _suppress_initial: true\n    })\n\n    return () => {\n      observer.stop()\n    }\n  }, [cursor])\n\n  return cursor ? data : cursor\n}\n\nconst useFindServer = <T = any>(factory: () => Mongo.Cursor<T> | undefined | null, deps: DependencyList) => (\n  Tracker.nonreactive(() => {\n    const cursor = factory()\n    if (Meteor.isDevelopment) checkCursor(cursor)\n    return cursor?.fetch?.() ?? null\n  })\n)\n\nexport const useFind = Meteor.isServer\n  ? useFindServer\n  : useFindClient\n\nfunction useFindDev <T = any>(factory: () => (Mongo.Cursor<T> | undefined | null), deps: DependencyList = []) {\n  function warn (expects: string, pos: string, arg: string, type: string) {\n    console.warn(\n      `Warning: useFind expected a ${expects} in it\\'s ${pos} argument `\n        + `(${arg}), but got type of \\`${type}\\`.`\n    );\n  }\n\n  if (typeof factory !== 'function') {\n    warn(\"function\", \"1st\", \"reactiveFn\", factory);\n  }\n\n  if (!deps || !Array.isArray(deps)) {\n    warn(\"array\", \"2nd\", \"deps\", typeof deps);\n  }\n\n  return useFind(factory, deps);\n}\n\nexport default Meteor.isDevelopment\n  ? useFindDev\n  : useFind;\n", "import { Meteor } from 'meteor/meteor'\nimport { useTracker } from './useTracker'\n\nconst useSubscribeClient = (name?: string, ...args: any[]): () => boolean => {\n  let updateOnReady = false\n  let subscription: Meteor.SubscriptionHandle\n\n  const isReady = useTracker(() => {\n    if (!name) return true\n\n    subscription = Meteor.subscribe(name, ...args)\n\n    return subscription.ready()\n  }, () => (!updateOnReady))\n\n  return () => {\n    updateOnReady = true\n    return !isReady\n  }\n}\n\nconst useSubscribeServer = (name?: string, ...args: any[]): () => boolean => (\n  () => false\n)\n\nexport const useSubscribe = Meteor.isServer\n  ? useSubscribeServer\n  : useSubscribeClient\n", "declare var Package: any\nimport { Meteor } from 'meteor/meteor';\nimport { Tracker } from 'meteor/tracker';\nimport { useReducer, useEffect, useRef, useMemo, DependencyList } from 'react';\n\n// Warns if data is a Mongo.Cursor or a POJO containing a Mongo.Cursor.\nfunction checkCursor (data: any): void {\n  let shouldWarn = false;\n  if (Package.mongo && Package.mongo.Mongo && data && typeof data === 'object') {\n    if (data instanceof Package.mongo.Mongo.Cursor) {\n      shouldWarn = true;\n    } else if (Object.getPrototypeOf(data) === Object.prototype) {\n      Object.keys(data).forEach((key) => {\n        if (data[key] instanceof Package.mongo.Mongo.Cursor) {\n          shouldWarn = true;\n        }\n      });\n    }\n  }\n  if (shouldWarn) {\n    console.warn(\n      'Warning: your reactive function is returning a Mongo cursor. '\n      + 'This value will not be reactive. You probably want to call '\n      + '`.fetch()` on the cursor before returning it.'\n    );\n  }\n}\n\n// Used to create a forceUpdate from useReducer. Forces update by\n// incrementing a number whenever the dispatch method is invoked.\nconst fur = (x: number): number => x + 1;\nconst useForceUpdate = () => useReducer(fur, 0)[1];\n\nexport interface IReactiveFn<T> {\n  (c?: Tracker.Computation): T\n}\n\nexport interface ISkipUpdate<T> {\n  <T>(prev: T, next: T): boolean\n}\n\ntype TrackerRefs = {\n  computation?: Tracker.Computation;\n  isMounted: boolean;\n  trackerData: any;\n}\n\nconst useTrackerNoDeps = <T = any>(reactiveFn: IReactiveFn<T>, skipUpdate: ISkipUpdate<T> = null) => {\n  const { current: refs } = useRef<TrackerRefs>({\n    isMounted: false,\n    trackerData: null\n  });\n  const forceUpdate = useForceUpdate();\n\n  // Without deps, always dispose and recreate the computation with every render.\n  if (refs.computation) {\n    refs.computation.stop();\n    // @ts-ignore This makes TS think ref.computation is \"never\" set\n    delete refs.computation;\n  }\n\n  // Use Tracker.nonreactive in case we are inside a Tracker Computation.\n  // This can happen if someone calls `ReactDOM.render` inside a Computation.\n  // In that case, we want to opt out of the normal behavior of nested\n  // Computations, where if the outer one is invalidated or stopped,\n  // it stops the inner one.\n  Tracker.nonreactive(() => Tracker.autorun((c: Tracker.Computation) => {\n    refs.computation = c;\n    const data = reactiveFn(c);\n    if (c.firstRun) {\n      // Always run the reactiveFn on firstRun\n      refs.trackerData = data;\n    } else if (!skipUpdate || !skipUpdate(refs.trackerData, data)) {\n      // For any reactive change, forceUpdate and let the next render rebuild the computation.\n      forceUpdate();\n    }\n  }));\n\n  // To clean up side effects in render, stop the computation immediately\n  if (!refs.isMounted) {\n    Meteor.defer(() => {\n      if (!refs.isMounted && refs.computation) {\n        refs.computation.stop();\n        delete refs.computation;\n      }\n    });\n  }\n\n  useEffect(() => {\n    // Let subsequent renders know we are mounted (render is committed).\n    refs.isMounted = true;\n\n    // In some cases, the useEffect hook will run before Meteor.defer, such as\n    // when React.lazy is used. In those cases, we might as well leave the\n    // computation alone!\n    if (!refs.computation) {\n      // Render is committed, but we no longer have a computation. Invoke\n      // forceUpdate and let the next render recreate the computation.\n      if (!skipUpdate) {\n        forceUpdate();\n      } else {\n        Tracker.nonreactive(() => Tracker.autorun((c: Tracker.Computation) => {\n          const data = reactiveFn(c);\n          refs.computation = c;\n          if (!skipUpdate(refs.trackerData, data)) {\n            // For any reactive change, forceUpdate and let the next render rebuild the computation.\n            forceUpdate();\n          }\n        }));\n      }\n    }\n\n    // stop the computation on unmount\n    return () =>{\n      refs.computation?.stop();\n      delete refs.computation;\n      refs.isMounted = false;\n    }\n  }, []);\n\n  return refs.trackerData;\n}\n\nconst useTrackerWithDeps = <T = any>(reactiveFn: IReactiveFn<T>, deps: DependencyList, skipUpdate: ISkipUpdate<T> = null): T => {\n  const forceUpdate = useForceUpdate();\n\n  const { current: refs } = useRef<{\n    reactiveFn: IReactiveFn<T>;\n    data?: T;\n    comp?: Tracker.Computation;\n    isMounted?: boolean;\n  }>({ reactiveFn });\n\n  // keep reactiveFn ref fresh\n  refs.reactiveFn = reactiveFn;\n\n  useMemo(() => {\n    // To jive with the lifecycle interplay between Tracker/Subscribe, run the\n    // reactive function in a computation, then stop it, to force flush cycle.\n    const comp = Tracker.nonreactive(\n      () => Tracker.autorun((c: Tracker.Computation) => {\n        const data = refs.reactiveFn();\n        if (c.firstRun) {\n          refs.data = data;\n        } else if (!skipUpdate || !skipUpdate(refs.data, data)) {\n          refs.data = data;\n          forceUpdate();\n        }\n      })\n    );\n\n    // Stop the computation immediately to avoid creating side effects in render.\n    // refers to this issues:\n    // https://github.com/meteor/react-packages/issues/382\n    // https://github.com/meteor/react-packages/issues/381\n    if (refs.comp) refs.comp.stop();\n\n    // In some cases, the useEffect hook will run before Meteor.defer, such as\n    // when React.lazy is used. This will allow it to be stopped earlier in\n    // useEffect if needed.\n    refs.comp = comp;\n    // To avoid creating side effects in render, stop the computation immediately\n    Meteor.defer(() => {\n      if (!refs.isMounted && refs.comp) {\n        refs.comp.stop();\n        delete refs.comp;\n      }\n    });\n  }, deps);\n\n  useEffect(() => {\n    // Let subsequent renders know we are mounted (render is committed).\n    refs.isMounted = true;\n\n    if (!refs.comp) {\n      refs.comp = Tracker.nonreactive(\n        () => Tracker.autorun((c) => {\n          const data: T = refs.reactiveFn(c);\n          if (!skipUpdate || !skipUpdate(refs.data, data)) {\n            refs.data = data;\n            forceUpdate();\n          }\n        })\n      );\n    }\n\n    return () => {\n      refs.comp.stop();\n      delete refs.comp;\n      refs.isMounted = false;\n    };\n  }, deps);\n\n  return refs.data as T;\n};\n\nfunction useTrackerClient <T = any>(reactiveFn: IReactiveFn<T>, skipUpdate?: ISkipUpdate<T>): T;\nfunction useTrackerClient <T = any>(reactiveFn: IReactiveFn<T>, deps?: DependencyList, skipUpdate?: ISkipUpdate<T>): T;\nfunction useTrackerClient <T = any>(reactiveFn: IReactiveFn<T>, deps: DependencyList | ISkipUpdate<T> = null, skipUpdate: ISkipUpdate<T> = null): T {\n  if (deps === null || deps === undefined || !Array.isArray(deps)) {\n    if (typeof deps === \"function\") {\n      skipUpdate = deps;\n    }\n    return useTrackerNoDeps(reactiveFn, skipUpdate);\n  } else {\n    return useTrackerWithDeps(reactiveFn, deps, skipUpdate);\n  }\n}\n\nconst useTrackerServer: typeof useTrackerClient = (reactiveFn) => {\n  return Tracker.nonreactive(reactiveFn);\n}\n\n// When rendering on the server, we don't want to use the Tracker.\n// We only do the first rendering on the server so we can get the data right away\nconst _useTracker = Meteor.isServer\n  ? useTrackerServer\n  : useTrackerClient;\n\nfunction useTrackerDev (reactiveFn, deps = null, skipUpdate = null) {\n  function warn (expects: string, pos: string, arg: string, type: string) {\n    console.warn(\n      `Warning: useTracker expected a ${expects} in it\\'s ${pos} argument `\n        + `(${arg}), but got type of \\`${type}\\`.`\n    );\n  }\n\n  if (typeof reactiveFn !== 'function') {\n    warn(\"function\", \"1st\", \"reactiveFn\", reactiveFn);\n  }\n\n  if (deps && skipUpdate && !Array.isArray(deps) && typeof skipUpdate === \"function\") {\n    warn(\"array & function\", \"2nd and 3rd\", \"deps, skipUpdate\",\n      `${typeof deps} & ${typeof skipUpdate}`);\n  } else {\n    if (deps && !Array.isArray(deps) && typeof deps !== \"function\") {\n      warn(\"array or function\", \"2nd\", \"deps or skipUpdate\", typeof deps);\n    }\n    if (skipUpdate && typeof skipUpdate !== \"function\") {\n      warn(\"function\", \"3rd\", \"skipUpdate\", typeof skipUpdate);\n    }\n  }\n\n  const data = _useTracker(reactiveFn, deps, skipUpdate);\n  checkCursor(data);\n  return data;\n}\n\nexport const useTracker = Meteor.isDevelopment\n  ? useTrackerDev as typeof useTrackerClient\n  : _useTracker;\n", "import React, { forwardRef, memo } from 'react';\nimport { useTracker } from './useTracker';\n\ntype ReactiveFn = (props: object) => any;\ntype ReactiveOptions = {\n  getMeteorData: ReactiveFn;\n  pure?: boolean;\n  skipUpdate?: (prev: any, next: any) => boolean;\n}\n\nexport const withTracker = (options: ReactiveFn | ReactiveOptions) => {\n  return (Component: React.ComponentType) => {\n    const getMeteorData = typeof options === 'function'\n      ? options\n      : options.getMeteorData;\n\n    const WithTracker = forwardRef((props, ref) => {\n      const data = useTracker(\n        () => getMeteorData(props) || {},\n        (options as ReactiveOptions).skipUpdate\n      );\n      return (\n        <Component ref={ref} {...props} {...data} />\n      );\n    });\n\n    const { pure = true } = options as ReactiveOptions;\n    return pure ? memo(WithTracker) : WithTracker;\n  };\n}\n"]}