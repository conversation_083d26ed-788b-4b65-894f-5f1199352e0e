{"version": 3, "sources": ["meteor://💻app/packages/ejson/ejson.js", "meteor://💻app/packages/ejson/stringify.js", "meteor://💻app/packages/ejson/utils.js"], "names": ["module", "export", "EJSON", "isFunction", "isObject", "keysOf", "lengthOf", "hasOwn", "convertMapToObject", "isArguments", "isInfOrNaN", "handleError", "link", "v", "customTypes", "Map", "addType", "name", "factory", "has", "Error", "set", "builtinConverters", "matchJSONValue", "obj", "matchObject", "Date", "toJSONValue", "$date", "getTime", "fromJSONValue", "RegExp", "regexp", "$regexp", "source", "$flags", "flags", "slice", "replace", "sign", "Number", "isNaN", "Infinity", "$InfNaN", "Uint8Array", "$binary", "Base64", "encode", "decode", "match", "keyCount", "some", "converter", "newObj", "for<PERSON>ach", "key", "$escape", "_isCustomType", "jsonValue", "Meteor", "_noYieldsAllowed", "$type", "typeName", "$value", "get", "_getTypes", "isOriginal", "arguments", "length", "undefined", "_getConverters", "toJSONValueHelper", "item", "i", "adjustTypesToJSONValue", "maybeChanged", "value", "changed", "_adjustTypesToJSONValue", "newItem", "clone", "fromJSONValueHelper", "keys", "every", "k", "substr", "adjustTypesFromJSONValue", "_adjustTypesFromJSONValue", "stringify", "options", "serialized", "json", "canonical", "indent", "canonicalStringify", "default", "JSON", "parse", "isBinary", "$Uint8ArrayPolyfill", "equals", "a", "b", "keyOrderSensitive", "valueOf", "aIsArray", "Array", "isArray", "bIsArray", "ret", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "newBinary", "map", "from", "_typeof", "quote", "string", "str", "holder", "singleIndent", "outerIndent", "isFinite", "String", "innerIndent", "partial", "hasOwnProperty", "call", "join", "Object", "sort", "push", "allOptions", "assign", "newIndent", "exportDefault", "_slicedToArray", "checkError", "fn", "prop", "prototype", "reduce", "acc", "_ref", "_ref2", "maxStack", "msgError", "test", "apply", "error", "isMaxStack", "message"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,KAAK,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,KAAK;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,UAAU,EAACC,QAAQ,EAACC,MAAM,EAACC,QAAQ,EAACC,MAAM,EAACC,kBAAkB,EAACC,WAAW,EAACC,UAAU,EAACC,WAAW;AAACX,MAAM,CAACY,IAAI,CAAC,SAAS,EAAC;EAACT,UAAU,EAAC,SAAAA,CAASU,CAAC,EAAC;IAACV,UAAU,GAACU,CAAC;EAAA,CAAC;EAACT,QAAQ,EAAC,SAAAA,CAASS,CAAC,EAAC;IAACT,QAAQ,GAACS,CAAC;EAAA,CAAC;EAACR,MAAM,EAAC,SAAAA,CAASQ,CAAC,EAAC;IAACR,MAAM,GAACQ,CAAC;EAAA,CAAC;EAACP,QAAQ,EAAC,SAAAA,CAASO,CAAC,EAAC;IAACP,QAAQ,GAACO,CAAC;EAAA,CAAC;EAACN,MAAM,EAAC,SAAAA,CAASM,CAAC,EAAC;IAACN,MAAM,GAACM,CAAC;EAAA,CAAC;EAACL,kBAAkB,EAAC,SAAAA,CAASK,CAAC,EAAC;IAACL,kBAAkB,GAACK,CAAC;EAAA,CAAC;EAACJ,WAAW,EAAC,SAAAA,CAASI,CAAC,EAAC;IAACJ,WAAW,GAACI,CAAC;EAAA,CAAC;EAACH,UAAU,EAAC,SAAAA,CAASG,CAAC,EAAC;IAACH,UAAU,GAACG,CAAC;EAAA,CAAC;EAACF,WAAW,EAAC,SAAAA,CAASE,CAAC,EAAC;IAACF,WAAW,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAYxf;AACA;AACA;AACA;AACA,IAAMX,KAAK,GAAG,CAAC,CAAC;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAMY,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAb,KAAK,CAACc,OAAO,GAAG,UAACC,IAAI,EAAEC,OAAO,EAAK;EACjC,IAAIJ,WAAW,CAACK,GAAG,CAACF,IAAI,CAAC,EAAE;IACzB,MAAM,IAAIG,KAAK,WAASH,IAAI,qBAAkB,CAAC;EACjD;EACAH,WAAW,CAACO,GAAG,CAACJ,IAAI,EAAEC,OAAO,CAAC;AAChC,CAAC;AAED,IAAMI,iBAAiB,GAAG,CACxB;EAAE;EACAC,cAAc,WAAAA,CAACC,GAAG,EAAE;IAClB,OAAOjB,MAAM,CAACiB,GAAG,EAAE,OAAO,CAAC,IAAIlB,QAAQ,CAACkB,GAAG,CAAC,KAAK,CAAC;EACpD,CAAC;EACDC,WAAW,WAAAA,CAACD,GAAG,EAAE;IACf,OAAOA,GAAG,YAAYE,IAAI;EAC5B,CAAC;EACDC,WAAW,WAAAA,CAACH,GAAG,EAAE;IACf,OAAO;MAACI,KAAK,EAAEJ,GAAG,CAACK,OAAO,CAAC;IAAC,CAAC;EAC/B,CAAC;EACDC,aAAa,WAAAA,CAACN,GAAG,EAAE;IACjB,OAAO,IAAIE,IAAI,CAACF,GAAG,CAACI,KAAK,CAAC;EAC5B;AACF,CAAC,EACD;EAAE;EACAL,cAAc,WAAAA,CAACC,GAAG,EAAE;IAClB,OAAOjB,MAAM,CAACiB,GAAG,EAAE,SAAS,CAAC,IACxBjB,MAAM,CAACiB,GAAG,EAAE,QAAQ,CAAC,IACrBlB,QAAQ,CAACkB,GAAG,CAAC,KAAK,CAAC;EAC1B,CAAC;EACDC,WAAW,WAAAA,CAACD,GAAG,EAAE;IACf,OAAOA,GAAG,YAAYO,MAAM;EAC9B,CAAC;EACDJ,WAAW,WAAAA,CAACK,MAAM,EAAE;IAClB,OAAO;MACLC,OAAO,EAAED,MAAM,CAACE,MAAM;MACtBC,MAAM,EAAEH,MAAM,CAACI;IACjB,CAAC;EACH,CAAC;EACDN,aAAa,WAAAA,CAACN,GAAG,EAAE;IACjB;IACA,OAAO,IAAIO,MAAM,CACfP,GAAG,CAACS,OAAO,EACXT,GAAG,CAACW;IACF;IAAA,CACCE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZC,OAAO,CAAC,WAAW,EAAC,EAAE,CAAC,CACvBA,OAAO,CAAC,cAAc,EAAE,EAAE,CAC/B,CAAC;EACH;AACF,CAAC,EACD;EAAE;EACA;EACAf,cAAc,WAAAA,CAACC,GAAG,EAAE;IAClB,OAAOjB,MAAM,CAACiB,GAAG,EAAE,SAAS,CAAC,IAAIlB,QAAQ,CAACkB,GAAG,CAAC,KAAK,CAAC;EACtD,CAAC;EACDC,WAAW,EAAEf,UAAU;EACvBiB,WAAW,WAAAA,CAACH,GAAG,EAAE;IACf,IAAIe,IAAI;IACR,IAAIC,MAAM,CAACC,KAAK,CAACjB,GAAG,CAAC,EAAE;MACrBe,IAAI,GAAG,CAAC;IACV,CAAC,MAAM,IAAIf,GAAG,KAAKkB,QAAQ,EAAE;MAC3BH,IAAI,GAAG,CAAC;IACV,CAAC,MAAM;MACLA,IAAI,GAAG,CAAC,CAAC;IACX;IACA,OAAO;MAACI,OAAO,EAAEJ;IAAI,CAAC;EACxB,CAAC;EACDT,aAAa,WAAAA,CAACN,GAAG,EAAE;IACjB,OAAOA,GAAG,CAACmB,OAAO,GAAG,CAAC;EACxB;AACF,CAAC,EACD;EAAE;EACApB,cAAc,WAAAA,CAACC,GAAG,EAAE;IAClB,OAAOjB,MAAM,CAACiB,GAAG,EAAE,SAAS,CAAC,IAAIlB,QAAQ,CAACkB,GAAG,CAAC,KAAK,CAAC;EACtD,CAAC;EACDC,WAAW,WAAAA,CAACD,GAAG,EAAE;IACf,OAAO,OAAOoB,UAAU,KAAK,WAAW,IAAIpB,GAAG,YAAYoB,UAAU,IAC/DpB,GAAG,IAAIjB,MAAM,CAACiB,GAAG,EAAE,qBAAqB,CAAE;EAClD,CAAC;EACDG,WAAW,WAAAA,CAACH,GAAG,EAAE;IACf,OAAO;MAACqB,OAAO,EAAEC,MAAM,CAACC,MAAM,CAACvB,GAAG;IAAC,CAAC;EACtC,CAAC;EACDM,aAAa,WAAAA,CAACN,GAAG,EAAE;IACjB,OAAOsB,MAAM,CAACE,MAAM,CAACxB,GAAG,CAACqB,OAAO,CAAC;EACnC;AACF,CAAC,EACD;EAAE;EACAtB,cAAc,WAAAA,CAACC,GAAG,EAAE;IAClB,OAAOjB,MAAM,CAACiB,GAAG,EAAE,SAAS,CAAC,IAAIlB,QAAQ,CAACkB,GAAG,CAAC,KAAK,CAAC;EACtD,CAAC;EACDC,WAAW,WAAAA,CAACD,GAAG,EAAE;IACf,IAAIyB,KAAK,GAAG,KAAK;IACjB,IAAIzB,GAAG,EAAE;MACP,IAAM0B,QAAQ,GAAG5C,QAAQ,CAACkB,GAAG,CAAC;MAC9B,IAAI0B,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC,EAAE;QACpCD,KAAK,GACH3B,iBAAiB,CAAC6B,IAAI,CAAC,UAAAC,SAAS;UAAA,OAAIA,SAAS,CAAC7B,cAAc,CAACC,GAAG,CAAC;QAAA,EAAC;MACtE;IACF;IACA,OAAOyB,KAAK;EACd,CAAC;EACDtB,WAAW,WAAAA,CAACH,GAAG,EAAE;IACf,IAAM6B,MAAM,GAAG,CAAC,CAAC;IACjBhD,MAAM,CAACmB,GAAG,CAAC,CAAC8B,OAAO,CAAC,UAAAC,GAAG,EAAI;MACzBF,MAAM,CAACE,GAAG,CAAC,GAAGrD,KAAK,CAACyB,WAAW,CAACH,GAAG,CAAC+B,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC;IACF,OAAO;MAACC,OAAO,EAAEH;IAAM,CAAC;EAC1B,CAAC;EACDvB,aAAa,WAAAA,CAACN,GAAG,EAAE;IACjB,IAAM6B,MAAM,GAAG,CAAC,CAAC;IACjBhD,MAAM,CAACmB,GAAG,CAACgC,OAAO,CAAC,CAACF,OAAO,CAAC,UAAAC,GAAG,EAAI;MACjCF,MAAM,CAACE,GAAG,CAAC,GAAGrD,KAAK,CAAC4B,aAAa,CAACN,GAAG,CAACgC,OAAO,CAACD,GAAG,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,OAAOF,MAAM;EACf;AACF,CAAC,EACD;EAAE;EACA9B,cAAc,WAAAA,CAACC,GAAG,EAAE;IAClB,OAAOjB,MAAM,CAACiB,GAAG,EAAE,OAAO,CAAC,IACtBjB,MAAM,CAACiB,GAAG,EAAE,QAAQ,CAAC,IAAIlB,QAAQ,CAACkB,GAAG,CAAC,KAAK,CAAC;EACnD,CAAC;EACDC,WAAW,WAAAA,CAACD,GAAG,EAAE;IACf,OAAOtB,KAAK,CAACuD,aAAa,CAACjC,GAAG,CAAC;EACjC,CAAC;EACDG,WAAW,WAAAA,CAACH,GAAG,EAAE;IACf,IAAMkC,SAAS,GAAGC,MAAM,CAACC,gBAAgB,CAAC;MAAA,OAAMpC,GAAG,CAACG,WAAW,CAAC,CAAC;IAAA,EAAC;IAClE,OAAO;MAACkC,KAAK,EAAErC,GAAG,CAACsC,QAAQ,CAAC,CAAC;MAAEC,MAAM,EAAEL;IAAS,CAAC;EACnD,CAAC;EACD5B,aAAa,WAAAA,CAACN,GAAG,EAAE;IACjB,IAAMsC,QAAQ,GAAGtC,GAAG,CAACqC,KAAK;IAC1B,IAAI,CAAC/C,WAAW,CAACK,GAAG,CAAC2C,QAAQ,CAAC,EAAE;MAC9B,MAAM,IAAI1C,KAAK,wBAAsB0C,QAAQ,oBAAiB,CAAC;IACjE;IACA,IAAMV,SAAS,GAAGtC,WAAW,CAACkD,GAAG,CAACF,QAAQ,CAAC;IAC3C,OAAOH,MAAM,CAACC,gBAAgB,CAAC;MAAA,OAAMR,SAAS,CAAC5B,GAAG,CAACuC,MAAM,CAAC;IAAA,EAAC;EAC7D;AACF,CAAC,CACF;AAED7D,KAAK,CAACuD,aAAa,GAAG,UAACjC,GAAG;EAAA,OACxBA,GAAG,IACHrB,UAAU,CAACqB,GAAG,CAACG,WAAW,CAAC,IAC3BxB,UAAU,CAACqB,GAAG,CAACsC,QAAQ,CAAC,IACxBhD,WAAW,CAACK,GAAG,CAACK,GAAG,CAACsC,QAAQ,CAAC,CAAC,CAAC;AAAA,CAChC;AAED5D,KAAK,CAAC+D,SAAS,GAAG;EAAA,IAACC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,OAAMD,UAAU,GAAGpD,WAAW,GAAGN,kBAAkB,CAACM,WAAW,CAAC;AAAA,CAAC;AAEtGZ,KAAK,CAACoE,cAAc,GAAG;EAAA,OAAMhD,iBAAiB;AAAA;;AAE9C;AACA;AACA,IAAMiD,iBAAiB,GAAG,SAAAA,CAAAC,IAAI,EAAI;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnD,iBAAiB,CAAC8C,MAAM,EAAEK,CAAC,EAAE,EAAE;IACjD,IAAMrB,SAAS,GAAG9B,iBAAiB,CAACmD,CAAC,CAAC;IACtC,IAAIrB,SAAS,CAAC3B,WAAW,CAAC+C,IAAI,CAAC,EAAE;MAC/B,OAAOpB,SAAS,CAACzB,WAAW,CAAC6C,IAAI,CAAC;IACpC;EACF;EACA,OAAOH,SAAS;AAClB,CAAC;;AAED;AACA,IAAMK,sBAAsB,GAAG,SAAAA,CAAAlD,GAAG,EAAI;EACpC;EACA,IAAIA,GAAG,KAAK,IAAI,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,IAAMmD,YAAY,GAAGJ,iBAAiB,CAAC/C,GAAG,CAAC;EAC3C,IAAImD,YAAY,KAAKN,SAAS,EAAE;IAC9B,OAAOM,YAAY;EACrB;;EAEA;EACA,IAAI,CAACvE,QAAQ,CAACoB,GAAG,CAAC,EAAE;IAClB,OAAOA,GAAG;EACZ;;EAEA;EACAnB,MAAM,CAACmB,GAAG,CAAC,CAAC8B,OAAO,CAAC,UAAAC,GAAG,EAAI;IACzB,IAAMqB,KAAK,GAAGpD,GAAG,CAAC+B,GAAG,CAAC;IACtB,IAAI,CAACnD,QAAQ,CAACwE,KAAK,CAAC,IAAIA,KAAK,KAAKP,SAAS,IACvC,CAAC3D,UAAU,CAACkE,KAAK,CAAC,EAAE;MACtB,OAAO,CAAC;IACV;IAEA,IAAMC,OAAO,GAAGN,iBAAiB,CAACK,KAAK,CAAC;IACxC,IAAIC,OAAO,EAAE;MACXrD,GAAG,CAAC+B,GAAG,CAAC,GAAGsB,OAAO;MAClB,OAAO,CAAC;IACV;IACA;IACA;IACAH,sBAAsB,CAACE,KAAK,CAAC;EAC/B,CAAC,CAAC;EACF,OAAOpD,GAAG;AACZ,CAAC;AAEDtB,KAAK,CAAC4E,uBAAuB,GAAGJ,sBAAsB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACAxE,KAAK,CAACyB,WAAW,GAAG,UAAA6C,IAAI,EAAI;EAC1B,IAAMK,OAAO,GAAGN,iBAAiB,CAACC,IAAI,CAAC;EACvC,IAAIK,OAAO,KAAKR,SAAS,EAAE;IACzB,OAAOQ,OAAO;EAChB;EAEA,IAAIE,OAAO,GAAGP,IAAI;EAClB,IAAIpE,QAAQ,CAACoE,IAAI,CAAC,EAAE;IAClBO,OAAO,GAAG7E,KAAK,CAAC8E,KAAK,CAACR,IAAI,CAAC;IAC3BE,sBAAsB,CAACK,OAAO,CAAC;EACjC;EACA,OAAOA,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAME,mBAAmB,GAAG,SAAAA,CAAAL,KAAK,EAAI;EACnC,IAAIxE,QAAQ,CAACwE,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,EAAE;IACrC,IAAMM,IAAI,GAAG7E,MAAM,CAACuE,KAAK,CAAC;IAC1B,IAAIM,IAAI,CAACd,MAAM,IAAI,CAAC,IACbc,IAAI,CAACC,KAAK,CAAC,UAAAC,CAAC;MAAA,OAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;IAAA,EAAC,EAAE;MACvE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnD,iBAAiB,CAAC8C,MAAM,EAAEK,CAAC,EAAE,EAAE;QACjD,IAAMrB,SAAS,GAAG9B,iBAAiB,CAACmD,CAAC,CAAC;QACtC,IAAIrB,SAAS,CAAC7B,cAAc,CAACqD,KAAK,CAAC,EAAE;UACnC,OAAOxB,SAAS,CAACtB,aAAa,CAAC8C,KAAK,CAAC;QACvC;MACF;IACF;EACF;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,IAAMU,wBAAwB,GAAG,SAAAA,CAAA9D,GAAG,EAAI;EACtC,IAAIA,GAAG,KAAK,IAAI,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,IAAMmD,YAAY,GAAGM,mBAAmB,CAACzD,GAAG,CAAC;EAC7C,IAAImD,YAAY,KAAKnD,GAAG,EAAE;IACxB,OAAOmD,YAAY;EACrB;;EAEA;EACA,IAAI,CAACvE,QAAQ,CAACoB,GAAG,CAAC,EAAE;IAClB,OAAOA,GAAG;EACZ;EAEAnB,MAAM,CAACmB,GAAG,CAAC,CAAC8B,OAAO,CAAC,UAAAC,GAAG,EAAI;IACzB,IAAMqB,KAAK,GAAGpD,GAAG,CAAC+B,GAAG,CAAC;IACtB,IAAInD,QAAQ,CAACwE,KAAK,CAAC,EAAE;MACnB,IAAMC,OAAO,GAAGI,mBAAmB,CAACL,KAAK,CAAC;MAC1C,IAAIA,KAAK,KAAKC,OAAO,EAAE;QACrBrD,GAAG,CAAC+B,GAAG,CAAC,GAAGsB,OAAO;QAClB;MACF;MACA;MACA;MACAS,wBAAwB,CAACV,KAAK,CAAC;IACjC;EACF,CAAC,CAAC;EACF,OAAOpD,GAAG;AACZ,CAAC;AAEDtB,KAAK,CAACqF,yBAAyB,GAAGD,wBAAwB;;AAE1D;AACA;AACA;AACA;AACA;AACApF,KAAK,CAAC4B,aAAa,GAAG,UAAA0C,IAAI,EAAI;EAC5B,IAAIK,OAAO,GAAGI,mBAAmB,CAACT,IAAI,CAAC;EACvC,IAAIK,OAAO,KAAKL,IAAI,IAAIpE,QAAQ,CAACoE,IAAI,CAAC,EAAE;IACtCK,OAAO,GAAG3E,KAAK,CAAC8E,KAAK,CAACR,IAAI,CAAC;IAC3Bc,wBAAwB,CAACT,OAAO,CAAC;EACnC;EACA,OAAOA,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3E,KAAK,CAACsF,SAAS,GAAG7E,WAAW,CAAC,UAAC6D,IAAI,EAAEiB,OAAO,EAAK;EAC/C,IAAIC,UAAU;EACd,IAAMC,IAAI,GAAGzF,KAAK,CAACyB,WAAW,CAAC6C,IAAI,CAAC;EACpC,IAAIiB,OAAO,KAAKA,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,MAAM,CAAC,EAAE;IA5YxD,IAAIC,kBAAkB;IAAC9F,MAAM,CAACY,IAAI,CAAC,aAAa,EAAC;MAAC,WAAQ,SAAAmF,CAASlF,CAAC,EAAC;QAACiF,kBAAkB,GAACjF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IA8Y3F6E,UAAU,GAAGI,kBAAkB,CAACH,IAAI,EAAEF,OAAO,CAAC;EAChD,CAAC,MAAM;IACLC,UAAU,GAAGM,IAAI,CAACR,SAAS,CAACG,IAAI,CAAC;EACnC;EACA,OAAOD,UAAU;AACnB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACAxF,KAAK,CAAC+F,KAAK,GAAG,UAAAzB,IAAI,EAAI;EACpB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIpD,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EACA,OAAOlB,KAAK,CAAC4B,aAAa,CAACkE,IAAI,CAACC,KAAK,CAACzB,IAAI,CAAC,CAAC;AAC9C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAtE,KAAK,CAACgG,QAAQ,GAAG,UAAA1E,GAAG,EAAI;EACtB,OAAO,CAAC,EAAG,OAAOoB,UAAU,KAAK,WAAW,IAAIpB,GAAG,YAAYoB,UAAU,IACtEpB,GAAG,IAAIA,GAAG,CAAC2E,mBAAoB,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjG,KAAK,CAACkG,MAAM,GAAG,UAACC,CAAC,EAAEC,CAAC,EAAEb,OAAO,EAAK;EAChC,IAAIhB,CAAC;EACL,IAAM8B,iBAAiB,GAAG,CAAC,EAAEd,OAAO,IAAIA,OAAO,CAACc,iBAAiB,CAAC;EAClE,IAAIF,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;;EAEA;EACA;EACA,IAAI9D,MAAM,CAACC,KAAK,CAAC4D,CAAC,CAAC,IAAI7D,MAAM,CAACC,KAAK,CAAC6D,CAAC,CAAC,EAAE;IACtC,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;IACZ,OAAO,KAAK;EACd;EAEA,IAAI,EAAElG,QAAQ,CAACiG,CAAC,CAAC,IAAIjG,QAAQ,CAACkG,CAAC,CAAC,CAAC,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,IAAID,CAAC,YAAY3E,IAAI,IAAI4E,CAAC,YAAY5E,IAAI,EAAE;IAC1C,OAAO2E,CAAC,CAACG,OAAO,CAAC,CAAC,KAAKF,CAAC,CAACE,OAAO,CAAC,CAAC;EACpC;EAEA,IAAItG,KAAK,CAACgG,QAAQ,CAACG,CAAC,CAAC,IAAInG,KAAK,CAACgG,QAAQ,CAACI,CAAC,CAAC,EAAE;IAC1C,IAAID,CAAC,CAACjC,MAAM,KAAKkC,CAAC,CAAClC,MAAM,EAAE;MACzB,OAAO,KAAK;IACd;IACA,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,CAAC,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC7B,IAAI4B,CAAC,CAAC5B,CAAC,CAAC,KAAK6B,CAAC,CAAC7B,CAAC,CAAC,EAAE;QACjB,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEA,IAAItE,UAAU,CAACkG,CAAC,CAACD,MAAM,CAAC,EAAE;IACxB,OAAOC,CAAC,CAACD,MAAM,CAACE,CAAC,EAAEb,OAAO,CAAC;EAC7B;EAEA,IAAItF,UAAU,CAACmG,CAAC,CAACF,MAAM,CAAC,EAAE;IACxB,OAAOE,CAAC,CAACF,MAAM,CAACC,CAAC,EAAEZ,OAAO,CAAC;EAC7B;;EAEA;EACA,IAAMgB,QAAQ,GAAGC,KAAK,CAACC,OAAO,CAACN,CAAC,CAAC;EACjC,IAAMO,QAAQ,GAAGF,KAAK,CAACC,OAAO,CAACL,CAAC,CAAC;;EAEjC;EACA,IAAIG,QAAQ,KAAKG,QAAQ,EAAE;IACzB,OAAO,KAAK;EACd;EAEA,IAAIH,QAAQ,IAAIG,QAAQ,EAAE;IACxB,IAAIP,CAAC,CAACjC,MAAM,KAAKkC,CAAC,CAAClC,MAAM,EAAE;MACzB,OAAO,KAAK;IACd;IACA,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,CAAC,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC7B,IAAI,CAACvE,KAAK,CAACkG,MAAM,CAACC,CAAC,CAAC5B,CAAC,CAAC,EAAE6B,CAAC,CAAC7B,CAAC,CAAC,EAAEgB,OAAO,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;;EAEA;EACA,QAAQvF,KAAK,CAACuD,aAAa,CAAC4C,CAAC,CAAC,GAAGnG,KAAK,CAACuD,aAAa,CAAC6C,CAAC,CAAC;IACrD,KAAK,CAAC;MAAE,OAAO,KAAK;IACpB,KAAK,CAAC;MAAE,OAAOpG,KAAK,CAACkG,MAAM,CAAClG,KAAK,CAACyB,WAAW,CAAC0E,CAAC,CAAC,EAAEnG,KAAK,CAACyB,WAAW,CAAC2E,CAAC,CAAC,CAAC;IACvE,QAAQ,CAAC;EACX;;EAEA;EACA,IAAIO,GAAG;EACP,IAAMC,KAAK,GAAGzG,MAAM,CAACgG,CAAC,CAAC;EACvB,IAAMU,KAAK,GAAG1G,MAAM,CAACiG,CAAC,CAAC;EACvB,IAAIC,iBAAiB,EAAE;IACrB9B,CAAC,GAAG,CAAC;IACLoC,GAAG,GAAGC,KAAK,CAAC3B,KAAK,CAAC,UAAA5B,GAAG,EAAI;MACvB,IAAIkB,CAAC,IAAIsC,KAAK,CAAC3C,MAAM,EAAE;QACrB,OAAO,KAAK;MACd;MACA,IAAIb,GAAG,KAAKwD,KAAK,CAACtC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK;MACd;MACA,IAAI,CAACvE,KAAK,CAACkG,MAAM,CAACC,CAAC,CAAC9C,GAAG,CAAC,EAAE+C,CAAC,CAACS,KAAK,CAACtC,CAAC,CAAC,CAAC,EAAEgB,OAAO,CAAC,EAAE;QAC/C,OAAO,KAAK;MACd;MACAhB,CAAC,EAAE;MACH,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,MAAM;IACLA,CAAC,GAAG,CAAC;IACLoC,GAAG,GAAGC,KAAK,CAAC3B,KAAK,CAAC,UAAA5B,GAAG,EAAI;MACvB,IAAI,CAAChD,MAAM,CAAC+F,CAAC,EAAE/C,GAAG,CAAC,EAAE;QACnB,OAAO,KAAK;MACd;MACA,IAAI,CAACrD,KAAK,CAACkG,MAAM,CAACC,CAAC,CAAC9C,GAAG,CAAC,EAAE+C,CAAC,CAAC/C,GAAG,CAAC,EAAEkC,OAAO,CAAC,EAAE;QAC1C,OAAO,KAAK;MACd;MACAhB,CAAC,EAAE;MACH,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EACA,OAAOoC,GAAG,IAAIpC,CAAC,KAAKsC,KAAK,CAAC3C,MAAM;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAlE,KAAK,CAAC8E,KAAK,GAAG,UAAAnE,CAAC,EAAI;EACjB,IAAIgG,GAAG;EACP,IAAI,CAACzG,QAAQ,CAACS,CAAC,CAAC,EAAE;IAChB,OAAOA,CAAC;EACV;EAEA,IAAIA,CAAC,KAAK,IAAI,EAAE;IACd,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,IAAIA,CAAC,YAAYa,IAAI,EAAE;IACrB,OAAO,IAAIA,IAAI,CAACb,CAAC,CAACgB,OAAO,CAAC,CAAC,CAAC;EAC9B;;EAEA;EACA;EACA,IAAIhB,CAAC,YAAYkB,MAAM,EAAE;IACvB,OAAOlB,CAAC;EACV;EAEA,IAAIX,KAAK,CAACgG,QAAQ,CAACrF,CAAC,CAAC,EAAE;IACrBgG,GAAG,GAAG3G,KAAK,CAAC8G,SAAS,CAACnG,CAAC,CAACuD,MAAM,CAAC;IAC/B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,CAAC,CAACuD,MAAM,EAAEK,CAAC,EAAE,EAAE;MACjCoC,GAAG,CAACpC,CAAC,CAAC,GAAG5D,CAAC,CAAC4D,CAAC,CAAC;IACf;IACA,OAAOoC,GAAG;EACZ;EAEA,IAAIH,KAAK,CAACC,OAAO,CAAC9F,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC,CAACoG,GAAG,CAAC/G,KAAK,CAAC8E,KAAK,CAAC;EAC3B;EAEA,IAAIvE,WAAW,CAACI,CAAC,CAAC,EAAE;IAClB,OAAO6F,KAAK,CAACQ,IAAI,CAACrG,CAAC,CAAC,CAACoG,GAAG,CAAC/G,KAAK,CAAC8E,KAAK,CAAC;EACvC;;EAEA;EACA,IAAI7E,UAAU,CAACU,CAAC,CAACmE,KAAK,CAAC,EAAE;IACvB,OAAOnE,CAAC,CAACmE,KAAK,CAAC,CAAC;EAClB;;EAEA;EACA,IAAI9E,KAAK,CAACuD,aAAa,CAAC5C,CAAC,CAAC,EAAE;IAC1B,OAAOX,KAAK,CAAC4B,aAAa,CAAC5B,KAAK,CAAC8E,KAAK,CAAC9E,KAAK,CAACyB,WAAW,CAACd,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EACrE;;EAEA;EACAgG,GAAG,GAAG,CAAC,CAAC;EACRxG,MAAM,CAACQ,CAAC,CAAC,CAACyC,OAAO,CAAC,UAACC,GAAG,EAAK;IACzBsD,GAAG,CAACtD,GAAG,CAAC,GAAGrD,KAAK,CAAC8E,KAAK,CAACnE,CAAC,CAAC0C,GAAG,CAAC,CAAC;EAChC,CAAC,CAAC;EACF,OAAOsD,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3G,KAAK,CAAC8G,SAAS,GAAGlE,MAAM,CAACkE,SAAS,C;;;;;;;;;;;AC5mBlC,IAAIG,OAAO;AAACnH,MAAM,CAACY,IAAI,CAAC,+BAA+B,EAAC;EAACmF,OAAO,EAAC,SAAAA,CAASlF,CAAC,EAAC;IAACsG,OAAO,GAACtG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASuG,KAAKA,CAACC,MAAM,EAAE;EACrB,OAAOrB,IAAI,CAACR,SAAS,CAAC6B,MAAM,CAAC;AAC/B;AAEA,IAAMC,GAAG,GAAG,SAAAA,CAAC/D,GAAG,EAAEgE,MAAM,EAAEC,YAAY,EAAEC,WAAW,EAAE7B,SAAS,EAAK;EACjE,IAAMhB,KAAK,GAAG2C,MAAM,CAAChE,GAAG,CAAC;;EAEzB;EACA,QAAA4D,OAAA,CAAevC,KAAK;IACpB,KAAK,QAAQ;MACX,OAAOwC,KAAK,CAACxC,KAAK,CAAC;IACrB,KAAK,QAAQ;MACX;MACA,OAAO8C,QAAQ,CAAC9C,KAAK,CAAC,GAAG+C,MAAM,CAAC/C,KAAK,CAAC,GAAG,MAAM;IACjD,KAAK,SAAS;MACZ,OAAO+C,MAAM,CAAC/C,KAAK,CAAC;IACtB;IACA;IACA,KAAK,QAAQ;MAAE;QACb;QACA;QACA,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,MAAM;QACf;QACA;QACA;QACA,IAAMgD,WAAW,GAAGH,WAAW,GAAGD,YAAY;QAC9C,IAAMK,OAAO,GAAG,EAAE;QAClB,IAAIhH,CAAC;;QAEL;QACA,IAAI6F,KAAK,CAACC,OAAO,CAAC/B,KAAK,CAAC,IAAK,CAAC,CAAC,CAAEkD,cAAc,CAACC,IAAI,CAACnD,KAAK,EAAE,QAAQ,CAAC,EAAE;UACrE;UACA;UACA,IAAMR,MAAM,GAAGQ,KAAK,CAACR,MAAM;UAC3B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;YAClCoD,OAAO,CAACpD,CAAC,CAAC,GACR6C,GAAG,CAAC7C,CAAC,EAAEG,KAAK,EAAE4C,YAAY,EAAEI,WAAW,EAAEhC,SAAS,CAAC,IAAI,MAAM;UACjE;;UAEA;UACA;UACA,IAAIiC,OAAO,CAACzD,MAAM,KAAK,CAAC,EAAE;YACxBvD,CAAC,GAAG,IAAI;UACV,CAAC,MAAM,IAAI+G,WAAW,EAAE;YACtB/G,CAAC,GAAG,KAAK,GACP+G,WAAW,GACXC,OAAO,CAACG,IAAI,CAAC,KAAK,GAClBJ,WAAW,CAAC,GACZ,IAAI,GACJH,WAAW,GACX,GAAG;UACP,CAAC,MAAM;YACL5G,CAAC,GAAG,GAAG,GAAGgH,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;UACnC;UACA,OAAOnH,CAAC;QACV;;QAEA;QACA,IAAIqE,IAAI,GAAG+C,MAAM,CAAC/C,IAAI,CAACN,KAAK,CAAC;QAC7B,IAAIgB,SAAS,EAAE;UACbV,IAAI,GAAGA,IAAI,CAACgD,IAAI,CAAC,CAAC;QACpB;QACAhD,IAAI,CAAC5B,OAAO,CAAC,UAAA8B,CAAC,EAAI;UAChBvE,CAAC,GAAGyG,GAAG,CAAClC,CAAC,EAAER,KAAK,EAAE4C,YAAY,EAAEI,WAAW,EAAEhC,SAAS,CAAC;UACvD,IAAI/E,CAAC,EAAE;YACLgH,OAAO,CAACM,IAAI,CAACf,KAAK,CAAChC,CAAC,CAAC,IAAIwC,WAAW,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG/G,CAAC,CAAC;UACzD;QACF,CAAC,CAAC;;QAEF;QACA;QACA,IAAIgH,OAAO,CAACzD,MAAM,KAAK,CAAC,EAAE;UACxBvD,CAAC,GAAG,IAAI;QACV,CAAC,MAAM,IAAI+G,WAAW,EAAE;UACtB/G,CAAC,GAAG,KAAK,GACP+G,WAAW,GACXC,OAAO,CAACG,IAAI,CAAC,KAAK,GAClBJ,WAAW,CAAC,GACZ,IAAI,GACJH,WAAW,GACX,GAAG;QACP,CAAC,MAAM;UACL5G,CAAC,GAAG,GAAG,GAAGgH,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;QACnC;QACA,OAAOnH,CAAC;MACV;IAEA,QAAQ,CAAC;EACT;AACF,CAAC;;AAED;AACA,IAAMiF,kBAAkB,GAAG,SAAAA,CAAClB,KAAK,EAAEa,OAAO,EAAK;EAC7C;EACA;EACA,IAAM2C,UAAU,GAAGH,MAAM,CAACI,MAAM,CAAC;IAC/BxC,MAAM,EAAE,EAAE;IACVD,SAAS,EAAE;EACb,CAAC,EAAEH,OAAO,CAAC;EACX,IAAI2C,UAAU,CAACvC,MAAM,KAAK,IAAI,EAAE;IAC9BuC,UAAU,CAACvC,MAAM,GAAG,IAAI;EAC1B,CAAC,MAAM,IAAI,OAAOuC,UAAU,CAACvC,MAAM,KAAK,QAAQ,EAAE;IAChD,IAAIyC,SAAS,GAAG,EAAE;IAClB,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,UAAU,CAACvC,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC1C6D,SAAS,IAAI,GAAG;IAClB;IACAF,UAAU,CAACvC,MAAM,GAAGyC,SAAS;EAC/B;EACA,OAAOhB,GAAG,CAAC,EAAE,EAAE;IAAC,EAAE,EAAE1C;EAAK,CAAC,EAAEwD,UAAU,CAACvC,MAAM,EAAE,EAAE,EAAEuC,UAAU,CAACxC,SAAS,CAAC;AAC1E,CAAC;AAvHD5F,MAAM,CAACuI,aAAa,CAyHLzC,kBAzHS,CAAC,C;;;;;;;;;;;ACAzB,IAAI0C,cAAc;AAACxI,MAAM,CAACY,IAAI,CAAC,sCAAsC,EAAC;EAACmF,OAAO,EAAC,SAAAA,CAASlF,CAAC,EAAC;IAAC2H,cAAc,GAAC3H,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIsG,OAAO;AAACnH,MAAM,CAACY,IAAI,CAAC,+BAA+B,EAAC;EAACmF,OAAO,EAAC,SAAAA,CAASlF,CAAC,EAAC;IAACsG,OAAO,GAACtG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAA5Mb,MAAM,CAACC,MAAM,CAAC;EAACE,UAAU,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,UAAU;EAAA,CAAC;EAACC,QAAQ,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,QAAQ;EAAA,CAAC;EAACC,MAAM,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,MAAM;EAAA,CAAC;EAACC,QAAQ,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,QAAQ;EAAA,CAAC;EAACC,MAAM,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,MAAM;EAAA,CAAC;EAACC,kBAAkB,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,kBAAkB;EAAA,CAAC;EAACC,WAAW,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,WAAW;EAAA,CAAC;EAACC,UAAU,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,UAAU;EAAA,CAAC;EAAC+H,UAAU,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,UAAU;EAAA,CAAC;EAAC9H,WAAW,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,WAAW;EAAA;AAAC,CAAC,CAAC;AAA/Z,IAAMR,UAAU,GAAG,SAAAA,CAACuI,EAAE;EAAA,OAAK,OAAOA,EAAE,KAAK,UAAU;AAAA;AAEnD,IAAMtI,QAAQ,GAAG,SAAAA,CAACsI,EAAE;EAAA,OAAKvB,OAAA,CAAOuB,EAAE,MAAK,QAAQ;AAAA;AAE/C,IAAMrI,MAAM,GAAG,SAAAA,CAACmB,GAAG;EAAA,OAAKyG,MAAM,CAAC/C,IAAI,CAAC1D,GAAG,CAAC;AAAA;AAExC,IAAMlB,QAAQ,GAAG,SAAAA,CAACkB,GAAG;EAAA,OAAKyG,MAAM,CAAC/C,IAAI,CAAC1D,GAAG,CAAC,CAAC4C,MAAM;AAAA;AAEjD,IAAM7D,MAAM,GAAG,SAAAA,CAACiB,GAAG,EAAEmH,IAAI;EAAA,OAAKV,MAAM,CAACW,SAAS,CAACd,cAAc,CAACC,IAAI,CAACvG,GAAG,EAAEmH,IAAI,CAAC;AAAA;AAE7E,IAAMnI,kBAAkB,GAAG,SAAAA,CAACyG,GAAG;EAAA,OAAKP,KAAK,CAACQ,IAAI,CAACD,GAAG,CAAC,CAAC4B,MAAM,CAAC,UAACC,GAAG,EAAAC,IAAA,EAAmB;IAAA,IAAAC,KAAA,GAAAR,cAAA,CAAAO,IAAA;MAAhBxF,GAAG,GAAAyF,KAAA;MAAEpE,KAAK,GAAAoE,KAAA;IACjF;IACAF,GAAG,CAACvF,GAAG,CAAC,GAAGqB,KAAK;IAChB,OAAOkE,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA;AAEC,IAAMrI,WAAW,GAAG,SAAAA,CAAAe,GAAG;EAAA,OAAIA,GAAG,IAAI,IAAI,IAAIjB,MAAM,CAACiB,GAAG,EAAE,QAAQ,CAAC;AAAA;AAE/D,IAAMd,UAAU,GACrB,SAAAA,CAAAc,GAAG;EAAA,OAAIgB,MAAM,CAACC,KAAK,CAACjB,GAAG,CAAC,IAAIA,GAAG,KAAKkB,QAAQ,IAAIlB,GAAG,KAAK,CAACkB,QAAQ;AAAA;AAE5D,IAAM+F,UAAU,GAAG;EACxBQ,QAAQ,EAAE,SAAAA,CAACC,QAAQ;IAAA,OAAK,IAAInH,MAAM,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAACoH,IAAI,CAACD,QAAQ,CAAC;EAAA;AAC5F,CAAC;AAEM,IAAMvI,WAAW,GAAG,SAAAA,CAAC+H,EAAE;EAAA,OAAK,YAAW;IAC5C,IAAI;MACF,OAAOA,EAAE,CAACU,KAAK,CAAC,IAAI,EAAEjF,SAAS,CAAC;IAClC,CAAC,CAAC,OAAOkF,KAAK,EAAE;MACd,IAAMC,UAAU,GAAGb,UAAU,CAACQ,QAAQ,CAACI,KAAK,CAACE,OAAO,CAAC;MACrD,IAAID,UAAU,EAAE;QACd,MAAM,IAAIlI,KAAK,CAAC,uCAAuC,CAAC;MAC1D;MACA,MAAMiI,KAAK;IACb;EACF,CAAC;AAAA,E", "file": "/packages/ejson.js", "sourcesContent": ["import {\n  isFunction,\n  isObject,\n  keysOf,\n  lengthOf,\n  hasOwn,\n  convertMapToObject,\n  isArguments,\n  isInfOrNaN,\n  handleError,\n} from './utils';\n\n/**\n * @namespace\n * @summary Namespace for EJSON functions\n */\nconst EJSON = {};\n\n// Custom type interface definition\n/**\n * @class CustomType\n * @instanceName customType\n * @memberOf EJSON\n * @summary The interface that a class must satisfy to be able to become an\n * EJSON custom type via EJSON.addType.\n */\n\n/**\n * @function typeName\n * @memberOf EJSON.CustomType\n * @summary Return the tag used to identify this type.  This must match the\n *          tag used to register this type with\n *          [`EJSON.addType`](#ejson_add_type).\n * @locus Anywhere\n * @instance\n */\n\n/**\n * @function toJSONValue\n * @memberOf EJSON.CustomType\n * @summary Serialize this instance into a JSON-compatible value.\n * @locus Anywhere\n * @instance\n */\n\n/**\n * @function clone\n * @memberOf EJSON.CustomType\n * @summary Return a value `r` such that `this.equals(r)` is true, and\n *          modifications to `r` do not affect `this` and vice versa.\n * @locus Anywhere\n * @instance\n */\n\n/**\n * @function equals\n * @memberOf EJSON.CustomType\n * @summary Return `true` if `other` has a value equal to `this`; `false`\n *          otherwise.\n * @locus Anywhere\n * @param {Object} other Another object to compare this to.\n * @instance\n */\n\nconst customTypes = new Map();\n\n// Add a custom type, using a method of your choice to get to and\n// from a basic JSON-able representation.  The factory argument\n// is a function of JSON-able --> your object\n// The type you add must have:\n// - A toJSONValue() method, so that Meteor can serialize it\n// - a typeName() method, to show how to look it up in our type table.\n// It is okay if these methods are monkey-patched on.\n// EJSON.clone will use toJSONValue and the given factory to produce\n// a clone, but you may specify a method clone() that will be\n// used instead.\n// Similarly, EJSON.equals will use toJSONValue to make comparisons,\n// but you may provide a method equals() instead.\n/**\n * @summary Add a custom datatype to EJSON.\n * @locus Anywhere\n * @param {String} name A tag for your custom type; must be unique among\n *                      custom data types defined in your project, and must\n *                      match the result of your type's `typeName` method.\n * @param {Function} factory A function that deserializes a JSON-compatible\n *                           value into an instance of your type.  This should\n *                           match the serialization performed by your\n *                           type's `toJSONValue` method.\n */\nEJSON.addType = (name, factory) => {\n  if (customTypes.has(name)) {\n    throw new Error(`Type ${name} already present`);\n  }\n  customTypes.set(name, factory);\n};\n\nconst builtinConverters = [\n  { // Date\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$date') && lengthOf(obj) === 1;\n    },\n    matchObject(obj) {\n      return obj instanceof Date;\n    },\n    toJSONValue(obj) {\n      return {$date: obj.getTime()};\n    },\n    fromJSONValue(obj) {\n      return new Date(obj.$date);\n    },\n  },\n  { // RegExp\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$regexp')\n        && hasOwn(obj, '$flags')\n        && lengthOf(obj) === 2;\n    },\n    matchObject(obj) {\n      return obj instanceof RegExp;\n    },\n    toJSONValue(regexp) {\n      return {\n        $regexp: regexp.source,\n        $flags: regexp.flags\n      };\n    },\n    fromJSONValue(obj) {\n      // Replaces duplicate / invalid flags.\n      return new RegExp(\n        obj.$regexp,\n        obj.$flags\n          // Cut off flags at 50 chars to avoid abusing RegExp for DOS.\n          .slice(0, 50)\n          .replace(/[^gimuy]/g,'')\n          .replace(/(.)(?=.*\\1)/g, '')\n      );\n    },\n  },\n  { // NaN, Inf, -Inf. (These are the only objects with typeof !== 'object'\n    // which we match.)\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$InfNaN') && lengthOf(obj) === 1;\n    },\n    matchObject: isInfOrNaN,\n    toJSONValue(obj) {\n      let sign;\n      if (Number.isNaN(obj)) {\n        sign = 0;\n      } else if (obj === Infinity) {\n        sign = 1;\n      } else {\n        sign = -1;\n      }\n      return {$InfNaN: sign};\n    },\n    fromJSONValue(obj) {\n      return obj.$InfNaN / 0;\n    },\n  },\n  { // Binary\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$binary') && lengthOf(obj) === 1;\n    },\n    matchObject(obj) {\n      return typeof Uint8Array !== 'undefined' && obj instanceof Uint8Array\n        || (obj && hasOwn(obj, '$Uint8ArrayPolyfill'));\n    },\n    toJSONValue(obj) {\n      return {$binary: Base64.encode(obj)};\n    },\n    fromJSONValue(obj) {\n      return Base64.decode(obj.$binary);\n    },\n  },\n  { // Escaping one level\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$escape') && lengthOf(obj) === 1;\n    },\n    matchObject(obj) {\n      let match = false;\n      if (obj) {\n        const keyCount = lengthOf(obj);\n        if (keyCount === 1 || keyCount === 2) {\n          match =\n            builtinConverters.some(converter => converter.matchJSONValue(obj));\n        }\n      }\n      return match;\n    },\n    toJSONValue(obj) {\n      const newObj = {};\n      keysOf(obj).forEach(key => {\n        newObj[key] = EJSON.toJSONValue(obj[key]);\n      });\n      return {$escape: newObj};\n    },\n    fromJSONValue(obj) {\n      const newObj = {};\n      keysOf(obj.$escape).forEach(key => {\n        newObj[key] = EJSON.fromJSONValue(obj.$escape[key]);\n      });\n      return newObj;\n    },\n  },\n  { // Custom\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$type')\n        && hasOwn(obj, '$value') && lengthOf(obj) === 2;\n    },\n    matchObject(obj) {\n      return EJSON._isCustomType(obj);\n    },\n    toJSONValue(obj) {\n      const jsonValue = Meteor._noYieldsAllowed(() => obj.toJSONValue());\n      return {$type: obj.typeName(), $value: jsonValue};\n    },\n    fromJSONValue(obj) {\n      const typeName = obj.$type;\n      if (!customTypes.has(typeName)) {\n        throw new Error(`Custom EJSON type ${typeName} is not defined`);\n      }\n      const converter = customTypes.get(typeName);\n      return Meteor._noYieldsAllowed(() => converter(obj.$value));\n    },\n  },\n];\n\nEJSON._isCustomType = (obj) => (\n  obj &&\n  isFunction(obj.toJSONValue) &&\n  isFunction(obj.typeName) &&\n  customTypes.has(obj.typeName())\n);\n\nEJSON._getTypes = (isOriginal = false) => (isOriginal ? customTypes : convertMapToObject(customTypes));\n\nEJSON._getConverters = () => builtinConverters;\n\n// Either return the JSON-compatible version of the argument, or undefined (if\n// the item isn't itself replaceable, but maybe some fields in it are)\nconst toJSONValueHelper = item => {\n  for (let i = 0; i < builtinConverters.length; i++) {\n    const converter = builtinConverters[i];\n    if (converter.matchObject(item)) {\n      return converter.toJSONValue(item);\n    }\n  }\n  return undefined;\n};\n\n// for both arrays and objects, in-place modification.\nconst adjustTypesToJSONValue = obj => {\n  // Is it an atom that we need to adjust?\n  if (obj === null) {\n    return null;\n  }\n\n  const maybeChanged = toJSONValueHelper(obj);\n  if (maybeChanged !== undefined) {\n    return maybeChanged;\n  }\n\n  // Other atoms are unchanged.\n  if (!isObject(obj)) {\n    return obj;\n  }\n\n  // Iterate over array or object structure.\n  keysOf(obj).forEach(key => {\n    const value = obj[key];\n    if (!isObject(value) && value !== undefined &&\n        !isInfOrNaN(value)) {\n      return; // continue\n    }\n\n    const changed = toJSONValueHelper(value);\n    if (changed) {\n      obj[key] = changed;\n      return; // on to the next key\n    }\n    // if we get here, value is an object but not adjustable\n    // at this level.  recurse.\n    adjustTypesToJSONValue(value);\n  });\n  return obj;\n};\n\nEJSON._adjustTypesToJSONValue = adjustTypesToJSONValue;\n\n/**\n * @summary Serialize an EJSON-compatible value into its plain JSON\n *          representation.\n * @locus Anywhere\n * @param {EJSON} val A value to serialize to plain JSON.\n */\nEJSON.toJSONValue = item => {\n  const changed = toJSONValueHelper(item);\n  if (changed !== undefined) {\n    return changed;\n  }\n\n  let newItem = item;\n  if (isObject(item)) {\n    newItem = EJSON.clone(item);\n    adjustTypesToJSONValue(newItem);\n  }\n  return newItem;\n};\n\n// Either return the argument changed to have the non-json\n// rep of itself (the Object version) or the argument itself.\n// DOES NOT RECURSE.  For actually getting the fully-changed value, use\n// EJSON.fromJSONValue\nconst fromJSONValueHelper = value => {\n  if (isObject(value) && value !== null) {\n    const keys = keysOf(value);\n    if (keys.length <= 2\n        && keys.every(k => typeof k === 'string' && k.substr(0, 1) === '$')) {\n      for (let i = 0; i < builtinConverters.length; i++) {\n        const converter = builtinConverters[i];\n        if (converter.matchJSONValue(value)) {\n          return converter.fromJSONValue(value);\n        }\n      }\n    }\n  }\n  return value;\n};\n\n// for both arrays and objects. Tries its best to just\n// use the object you hand it, but may return something\n// different if the object you hand it itself needs changing.\nconst adjustTypesFromJSONValue = obj => {\n  if (obj === null) {\n    return null;\n  }\n\n  const maybeChanged = fromJSONValueHelper(obj);\n  if (maybeChanged !== obj) {\n    return maybeChanged;\n  }\n\n  // Other atoms are unchanged.\n  if (!isObject(obj)) {\n    return obj;\n  }\n\n  keysOf(obj).forEach(key => {\n    const value = obj[key];\n    if (isObject(value)) {\n      const changed = fromJSONValueHelper(value);\n      if (value !== changed) {\n        obj[key] = changed;\n        return;\n      }\n      // if we get here, value is an object but not adjustable\n      // at this level.  recurse.\n      adjustTypesFromJSONValue(value);\n    }\n  });\n  return obj;\n};\n\nEJSON._adjustTypesFromJSONValue = adjustTypesFromJSONValue;\n\n/**\n * @summary Deserialize an EJSON value from its plain JSON representation.\n * @locus Anywhere\n * @param {JSONCompatible} val A value to deserialize into EJSON.\n */\nEJSON.fromJSONValue = item => {\n  let changed = fromJSONValueHelper(item);\n  if (changed === item && isObject(item)) {\n    changed = EJSON.clone(item);\n    adjustTypesFromJSONValue(changed);\n  }\n  return changed;\n};\n\n/**\n * @summary Serialize a value to a string. For EJSON values, the serialization\n *          fully represents the value. For non-EJSON values, serializes the\n *          same way as `JSON.stringify`.\n * @locus Anywhere\n * @param {EJSON} val A value to stringify.\n * @param {Object} [options]\n * @param {Boolean | Integer | String} [options.indent] Indents objects and\n * arrays for easy readability.  When `true`, indents by 2 spaces; when an\n * integer, indents by that number of spaces; and when a string, uses the\n * string as the indentation pattern.\n * @param {Boolean} [options.canonical] When `true`, stringifies keys in an\n *                                    object in sorted order.\n */\nEJSON.stringify = handleError((item, options) => {\n  let serialized;\n  const json = EJSON.toJSONValue(item);\n  if (options && (options.canonical || options.indent)) {\n    import canonicalStringify from './stringify';\n    serialized = canonicalStringify(json, options);\n  } else {\n    serialized = JSON.stringify(json);\n  }\n  return serialized;\n});\n\n/**\n * @summary Parse a string into an EJSON value. Throws an error if the string\n *          is not valid EJSON.\n * @locus Anywhere\n * @param {String} str A string to parse into an EJSON value.\n */\nEJSON.parse = item => {\n  if (typeof item !== 'string') {\n    throw new Error('EJSON.parse argument should be a string');\n  }\n  return EJSON.fromJSONValue(JSON.parse(item));\n};\n\n/**\n * @summary Returns true if `x` is a buffer of binary data, as returned from\n *          [`EJSON.newBinary`](#ejson_new_binary).\n * @param {Object} x The variable to check.\n * @locus Anywhere\n */\nEJSON.isBinary = obj => {\n  return !!((typeof Uint8Array !== 'undefined' && obj instanceof Uint8Array) ||\n    (obj && obj.$Uint8ArrayPolyfill));\n};\n\n/**\n * @summary Return true if `a` and `b` are equal to each other.  Return false\n *          otherwise.  Uses the `equals` method on `a` if present, otherwise\n *          performs a deep comparison.\n * @locus Anywhere\n * @param {EJSON} a\n * @param {EJSON} b\n * @param {Object} [options]\n * @param {Boolean} options.keyOrderSensitive Compare in key sensitive order,\n * if supported by the JavaScript implementation.  For example, `{a: 1, b: 2}`\n * is equal to `{b: 2, a: 1}` only when `keyOrderSensitive` is `false`.  The\n * default is `false`.\n */\nEJSON.equals = (a, b, options) => {\n  let i;\n  const keyOrderSensitive = !!(options && options.keyOrderSensitive);\n  if (a === b) {\n    return true;\n  }\n\n  // This differs from the IEEE spec for NaN equality, b/c we don't want\n  // anything ever with a NaN to be poisoned from becoming equal to anything.\n  if (Number.isNaN(a) && Number.isNaN(b)) {\n    return true;\n  }\n\n  // if either one is falsy, they'd have to be === to be equal\n  if (!a || !b) {\n    return false;\n  }\n\n  if (!(isObject(a) && isObject(b))) {\n    return false;\n  }\n\n  if (a instanceof Date && b instanceof Date) {\n    return a.valueOf() === b.valueOf();\n  }\n\n  if (EJSON.isBinary(a) && EJSON.isBinary(b)) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  if (isFunction(a.equals)) {\n    return a.equals(b, options);\n  }\n\n  if (isFunction(b.equals)) {\n    return b.equals(a, options);\n  }\n\n  // Array.isArray works across iframes while instanceof won't\n  const aIsArray = Array.isArray(a);\n  const bIsArray = Array.isArray(b);\n\n  // if not both or none are array they are not equal\n  if (aIsArray !== bIsArray) {\n    return false;\n  }\n\n  if (aIsArray && bIsArray) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (i = 0; i < a.length; i++) {\n      if (!EJSON.equals(a[i], b[i], options)) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // fallback for custom types that don't implement their own equals\n  switch (EJSON._isCustomType(a) + EJSON._isCustomType(b)) {\n    case 1: return false;\n    case 2: return EJSON.equals(EJSON.toJSONValue(a), EJSON.toJSONValue(b));\n    default: // Do nothing\n  }\n\n  // fall back to structural equality of objects\n  let ret;\n  const aKeys = keysOf(a);\n  const bKeys = keysOf(b);\n  if (keyOrderSensitive) {\n    i = 0;\n    ret = aKeys.every(key => {\n      if (i >= bKeys.length) {\n        return false;\n      }\n      if (key !== bKeys[i]) {\n        return false;\n      }\n      if (!EJSON.equals(a[key], b[bKeys[i]], options)) {\n        return false;\n      }\n      i++;\n      return true;\n    });\n  } else {\n    i = 0;\n    ret = aKeys.every(key => {\n      if (!hasOwn(b, key)) {\n        return false;\n      }\n      if (!EJSON.equals(a[key], b[key], options)) {\n        return false;\n      }\n      i++;\n      return true;\n    });\n  }\n  return ret && i === bKeys.length;\n};\n\n/**\n * @summary Return a deep copy of `val`.\n * @locus Anywhere\n * @param {EJSON} val A value to copy.\n */\nEJSON.clone = v => {\n  let ret;\n  if (!isObject(v)) {\n    return v;\n  }\n\n  if (v === null) {\n    return null; // null has typeof \"object\"\n  }\n\n  if (v instanceof Date) {\n    return new Date(v.getTime());\n  }\n\n  // RegExps are not really EJSON elements (eg we don't define a serialization\n  // for them), but they're immutable anyway, so we can support them in clone.\n  if (v instanceof RegExp) {\n    return v;\n  }\n\n  if (EJSON.isBinary(v)) {\n    ret = EJSON.newBinary(v.length);\n    for (let i = 0; i < v.length; i++) {\n      ret[i] = v[i];\n    }\n    return ret;\n  }\n\n  if (Array.isArray(v)) {\n    return v.map(EJSON.clone);\n  }\n\n  if (isArguments(v)) {\n    return Array.from(v).map(EJSON.clone);\n  }\n\n  // handle general user-defined typed Objects if they have a clone method\n  if (isFunction(v.clone)) {\n    return v.clone();\n  }\n\n  // handle other custom types\n  if (EJSON._isCustomType(v)) {\n    return EJSON.fromJSONValue(EJSON.clone(EJSON.toJSONValue(v)), true);\n  }\n\n  // handle other objects\n  ret = {};\n  keysOf(v).forEach((key) => {\n    ret[key] = EJSON.clone(v[key]);\n  });\n  return ret;\n};\n\n/**\n * @summary Allocate a new buffer of binary data that EJSON can serialize.\n * @locus Anywhere\n * @param {Number} size The number of bytes of binary data to allocate.\n */\n// EJSON.newBinary is the public documented API for this functionality,\n// but the implementation is in the 'base64' package to avoid\n// introducing a circular dependency. (If the implementation were here,\n// then 'base64' would have to use EJSON.newBinary, and 'ejson' would\n// also have to use 'base64'.)\nEJSON.newBinary = Base64.newBinary;\n\nexport { EJSON };\n", "// Based on json2.js from https://github.com/douglascrockford/JSON-js\n//\n//    json2.js\n//    2012-10-08\n//\n//    Public Domain.\n//\n//    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\nfunction quote(string) {\n  return JSON.stringify(string);\n}\n\nconst str = (key, holder, singleIndent, outerIndent, canonical) => {\n  const value = holder[key];\n\n  // What happens next depends on the value's type.\n  switch (typeof value) {\n  case 'string':\n    return quote(value);\n  case 'number':\n    // JSON numbers must be finite. Encode non-finite numbers as null.\n    return isFinite(value) ? String(value) : 'null';\n  case 'boolean':\n    return String(value);\n  // If the type is 'object', we might be dealing with an object or an array or\n  // null.\n  case 'object': {\n    // Due to a specification blunder in ECMAScript, typeof null is 'object',\n    // so watch out for that case.\n    if (!value) {\n      return 'null';\n    }\n    // Make an array to hold the partial results of stringifying this object\n    // value.\n    const innerIndent = outerIndent + singleIndent;\n    const partial = [];\n    let v;\n\n    // Is the value an array?\n    if (Array.isArray(value) || ({}).hasOwnProperty.call(value, 'callee')) {\n      // The value is an array. Stringify every element. Use null as a\n      // placeholder for non-JSON values.\n      const length = value.length;\n      for (let i = 0; i < length; i += 1) {\n        partial[i] =\n          str(i, value, singleIndent, innerIndent, canonical) || 'null';\n      }\n\n      // Join all of the elements together, separated with commas, and wrap\n      // them in brackets.\n      if (partial.length === 0) {\n        v = '[]';\n      } else if (innerIndent) {\n        v = '[\\n' +\n          innerIndent +\n          partial.join(',\\n' +\n          innerIndent) +\n          '\\n' +\n          outerIndent +\n          ']';\n      } else {\n        v = '[' + partial.join(',') + ']';\n      }\n      return v;\n    }\n\n    // Iterate through all of the keys in the object.\n    let keys = Object.keys(value);\n    if (canonical) {\n      keys = keys.sort();\n    }\n    keys.forEach(k => {\n      v = str(k, value, singleIndent, innerIndent, canonical);\n      if (v) {\n        partial.push(quote(k) + (innerIndent ? ': ' : ':') + v);\n      }\n    });\n\n    // Join all of the member texts together, separated with commas,\n    // and wrap them in braces.\n    if (partial.length === 0) {\n      v = '{}';\n    } else if (innerIndent) {\n      v = '{\\n' +\n        innerIndent +\n        partial.join(',\\n' +\n        innerIndent) +\n        '\\n' +\n        outerIndent +\n        '}';\n    } else {\n      v = '{' + partial.join(',') + '}';\n    }\n    return v;\n  }\n\n  default: // Do nothing\n  }\n};\n\n// If the JSON object does not yet have a stringify method, give it one.\nconst canonicalStringify = (value, options) => {\n  // Make a fake root object containing our value under the key of ''.\n  // Return the result of stringifying the value.\n  const allOptions = Object.assign({\n    indent: '',\n    canonical: false,\n  }, options);\n  if (allOptions.indent === true) {\n    allOptions.indent = '  ';\n  } else if (typeof allOptions.indent === 'number') {\n    let newIndent = '';\n    for (let i = 0; i < allOptions.indent; i++) {\n      newIndent += ' ';\n    }\n    allOptions.indent = newIndent;\n  }\n  return str('', {'': value}, allOptions.indent, '', allOptions.canonical);\n};\n\nexport default canonicalStringify;\n", "export const isFunction = (fn) => typeof fn === 'function';\n\nexport const isObject = (fn) => typeof fn === 'object';\n\nexport const keysOf = (obj) => Object.keys(obj);\n\nexport const lengthOf = (obj) => Object.keys(obj).length;\n\nexport const hasOwn = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);\n\nexport const convertMapToObject = (map) => Array.from(map).reduce((acc, [key, value]) => {\n  // reassign to not create new object\n  acc[key] = value;\n  return acc;\n}, {});\n\nexport const isArguments = obj => obj != null && hasOwn(obj, 'callee');\n\nexport const isInfOrNaN =\n  obj => Number.isNaN(obj) || obj === Infinity || obj === -Infinity;\n\nexport const checkError = {\n  maxStack: (msgError) => new RegExp('Maximum call stack size exceeded', 'g').test(msgError),\n};\n\nexport const handleError = (fn) => function() {\n  try {\n    return fn.apply(this, arguments);\n  } catch (error) {\n    const isMaxStack = checkError.maxStack(error.message);\n    if (isMaxStack) {\n      throw new Error('Converting circular structure to JSON')\n    }\n    throw error;\n  }\n};\n"]}