{"version": 3, "names": ["assert", "require", "fs", "path", "_", "files", "serverJson", "topLevelIdPattern", "statOrNull", "statSync", "e", "findAppDirHelper", "absOSPath", "isDirectory", "join", "parentDir", "dirname", "Error", "findAppDir", "abs<PERSON>ath", "convertToPosixPath", "convertToOSPath", "nodeModulesRegistry", "Object", "create", "each", "load", "fileInfo", "node_modules", "match", "exec", "registerNodeModules", "name", "addByPath", "keys", "for<PERSON>ach", "strictEqual", "parts", "split", "pathSep", "shift", "pathIsAbsolute", "appDir", "relPathWithinApp", "pathRelative", "addByParts", "part", "i", "slice", "unshift", "pathResolve", "__dirname", "ok", "length", "notEqual", "getRelID", "id", "char<PERSON>t", "replace", "sortedNodeModulesPaths", "sort", "a", "b", "npmRequire", "resolve", "resolveCache", "res", "idParts", "meteorAddTip", "code", "resolveInLocalBuild", "resolveInNodeModules", "resolveInDevBundle", "tryResolve", "absId", "some", "prefix", "relId", "pathJoin", "test", "exports", "call", "module"], "sources": ["/tools/static-assets/server/npm-require.js"], "sourcesContent": ["var assert = require(\"assert\");\nvar fs = require(\"fs\");\nvar path = require(\"path\");\nvar _ = require('underscore');\nvar files = require('./mini-files');\nvar serverJson = require(\"./server-json.js\");\nvar topLevelIdPattern = /^[^./]/;\n\nfunction statOrNull(path) {\n  try {\n    return fs.statSync(path);\n  } catch (e) {\n    return null;\n  }\n}\n\nfunction findAppDirHelper(absOSPath) {\n  if (fs.statSync(absOSPath).isDirectory() &&\n      statOrNull(path.join(absOSPath, \".meteor\"))) {\n    return absOSPath;\n  }\n\n  var parentDir = path.dirname(absOSPath);\n  if (parentDir !== absOSPath) {\n    return findAppDirHelper(parentDir);\n  }\n\n  throw new Error(\"Cannot find application root directory\");\n}\n\nfunction findAppDir(absPath) {\n  return files.convertToPosixPath(\n    findAppDirHelper(files.convertToOSPath(absPath)));\n}\n\n// Map from virtual module identifiers for node_modules directories (like\n// \"/node_modules/meteor/blaze/node_modules\") to the absolute paths of the\n// read node_modules directories on disk. The npmRequire function below\n// needs to look up absolute paths using virtual identifiers as input.\nvar nodeModulesRegistry = Object.create(null);\n\n_.each(serverJson.load, function (fileInfo) {\n  if (fileInfo.node_modules) {\n    var match = /^(packages|app)\\/(\\S+)?\\.js/.exec(fileInfo.path);\n    if (match) {\n      if (match[1] === \"packages\") {\n        registerNodeModules(match[2], fileInfo.node_modules);\n      } else if (match[1] === \"app\") {\n        registerNodeModules(null, fileInfo.node_modules);\n      }\n    }\n  }\n});\n\nfunction registerNodeModules(name, node_modules) {\n  if (typeof node_modules === \"string\") {\n    addByPath(node_modules);\n  } else {\n    Object.keys(node_modules).forEach(addByPath);\n  }\n\n  function addByPath(node_modules) {\n    assert.strictEqual(typeof node_modules, \"string\");\n\n    var parts = node_modules.split(files.pathSep);\n    if (parts[0] === \"\") parts.shift();\n\n    if (files.pathIsAbsolute(node_modules)) {\n      if (! name) {\n        var appDir = findAppDir(node_modules);\n        var relPathWithinApp = files.pathRelative(appDir, node_modules);\n        addByParts(relPathWithinApp.split(files.pathSep), node_modules);\n        return;\n      }\n\n      parts.forEach(function (part, i) {\n        if (part === \"npm\") {\n          addByParts(parts.slice(i + 1), node_modules);\n\n        } else if (part === \".npm\") {\n          if (name) {\n            parts.unshift(\"node_modules\", \"meteor\", name);\n          }\n\n          if (parts[i + 1] === \"package\") {\n            addByParts(parts.slice(i + 2), node_modules);\n\n          } else if (parts[i + 1] === \"plugin\") {\n            assert.strictEqual(parts[i + 2], name);\n            addByParts(parts.slice(i + 3), node_modules);\n          }\n        }\n      });\n\n    } else if (parts[0] === \"npm\") {\n      var absPath = files.pathResolve(__dirname, parts.join(files.pathSep));\n      addByParts(parts.slice(1), absPath);\n\n    } else {\n      throw new Error(\"unknown node_modules path: \" + node_modules);\n    }\n  }\n\n  function addByParts(parts, absPath) {\n    assert.ok(parts.length > 0);\n    assert.notEqual(parts[0], \"\");\n    assert.notEqual(parts[0], \"..\");\n\n    // Ensure a leading / character.\n    if (parts[0].length > 0) {\n      parts.unshift(\"\");\n    }\n\n    nodeModulesRegistry[parts.join(\"/\")] = absPath;\n  }\n}\n\nfunction getRelID(id) {\n  assert.strictEqual(id.charAt(0), \"/\");\n  return \"./npm\" + id.replace(/:/g, \"_\");\n}\n\n// Sort the keys in reverse alphabetical order so that longer paths will\n// come before their prefixes.\nvar sortedNodeModulesPaths =\n  Object.keys(nodeModulesRegistry).sort(function (a, b) {\n    if (a < b) return 1;\n    if (b < a) return -1;\n    return 0;\n  });\n\nfunction npmRequire(id) {\n  return require(resolve(id));\n}\n\nvar resolveCache = Object.create(null);\n\nfunction resolve(id) {\n  var res = resolveCache[id];\n\n  if (typeof res === \"string\") {\n    return res;\n  }\n\n  if (res === null) {\n    var idParts = id.split(\"/\");\n    var meteorAddTip = \"\";\n    // If it looks like `meteor/xxx`, the user may forgot to add the\n    // package before importing it.\n    if (idParts.length === 2 &&\n        idParts[0] === \"meteor\") {\n          meteorAddTip = \". Try `meteor add \" + idParts[1] + \"` \" +\n          \"as it looks like you tried to import it without adding \" +\n          \"to the project.\";\n    }\n    res = new Error(\"Cannot find module '\" + id + \"'\" + meteorAddTip);\n    res.code = \"MODULE_NOT_FOUND\";\n    throw res;\n  }\n\n  resolveCache[id] =\n    resolveInLocalBuild(id) ||\n    resolveInNodeModules(id) ||\n    resolveInDevBundle(id) ||\n    null;\n\n  return resolve(id);\n}\n\nfunction resolveInLocalBuild(id) {\n  return tryResolve(getRelID(id));\n}\n\nfunction resolveInNodeModules(id) {\n  var absId;\n\n  sortedNodeModulesPaths.some(function (prefix) {\n    var relId = files.pathRelative(\n      files.pathJoin(\".\", prefix),\n      files.pathJoin(\".\", id)\n    );\n\n    if (relId.slice(0, 2) !== \"..\") {\n      return absId =\n        files.pathJoin(nodeModulesRegistry[prefix], relId);\n    }\n  });\n\n  return absId && tryResolve(files.convertToOSPath(absId));\n}\n\nfunction resolveInDevBundle(id) {\n  // Fall back to dev_bundle/lib/node_modules and built-in modules.\n  return topLevelIdPattern.test(id) && tryResolve(id);\n}\n\nfunction tryResolve(id) {\n  try {\n    return require.resolve(id);\n  } catch (e) {\n    return null;\n  }\n}\n\nexports.require = npmRequire;\nexports.resolve = npmRequire.resolve = resolve;\n"], "mappings": ";EAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;EAC9B,IAAIC,EAAE,GAAGD,OAAO,CAAC,IAAI,CAAC;EACtB,IAAIE,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;EAC1B,IAAIG,CAAC,GAAGH,OAAO,CAAC,YAAY,CAAC;EAC7B,IAAII,KAAK,GAAGJ,OAAO,CAAC,cAAc,CAAC;EACnC,IAAIK,UAAU,GAAGL,OAAO,CAAC,kBAAkB,CAAC;EAC5C,IAAIM,iBAAiB,GAAG,QAAQ;EAEhC,SAASC,UAAUA,CAACL,IAAI,EAAE;IACxB,IAAI;MACF,OAAOD,EAAE,CAACO,QAAQ,CAACN,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOO,CAAC,EAAE;MACV,OAAO,IAAI;IACb;EACF;EAEA,SAASC,gBAAgBA,CAACC,SAAS,EAAE;IACnC,IAAIV,EAAE,CAACO,QAAQ,CAACG,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC,IACpCL,UAAU,CAACL,IAAI,CAACW,IAAI,CAACF,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;MAC/C,OAAOA,SAAS;IAClB;IAEA,IAAIG,SAAS,GAAGZ,IAAI,CAACa,OAAO,CAACJ,SAAS,CAAC;IACvC,IAAIG,SAAS,KAAKH,SAAS,EAAE;MAC3B,OAAOD,gBAAgB,CAACI,SAAS,CAAC;IACpC;IAEA,MAAM,IAAIE,KAAK,CAAC,wCAAwC,CAAC;EAC3D;EAEA,SAASC,UAAUA,CAACC,OAAO,EAAE;IAC3B,OAAOd,KAAK,CAACe,kBAAkB,CAC7BT,gBAAgB,CAACN,KAAK,CAACgB,eAAe,CAACF,OAAO,CAAC,CAAC,CAAC;EACrD;;EAEA;EACA;EACA;EACA;EACA,IAAIG,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAE7CpB,CAAC,CAACqB,IAAI,CAACnB,UAAU,CAACoB,IAAI,EAAE,UAAUC,QAAQ,EAAE;IAC1C,IAAIA,QAAQ,CAACC,YAAY,EAAE;MACzB,IAAIC,KAAK,GAAG,6BAA6B,CAACC,IAAI,CAACH,QAAQ,CAACxB,IAAI,CAAC;MAC7D,IAAI0B,KAAK,EAAE;QACT,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;UAC3BE,mBAAmB,CAACF,KAAK,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAACC,YAAY,CAAC;QACtD,CAAC,MAAM,IAAIC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;UAC7BE,mBAAmB,CAAC,IAAI,EAAEJ,QAAQ,CAACC,YAAY,CAAC;QAClD;MACF;IACF;EACF,CAAC,CAAC;EAEF,SAASG,mBAAmBA,CAACC,IAAI,EAAEJ,YAAY,EAAE;IAC/C,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpCK,SAAS,CAACL,YAAY,CAAC;IACzB,CAAC,MAAM;MACLL,MAAM,CAACW,IAAI,CAACN,YAAY,CAAC,CAACO,OAAO,CAACF,SAAS,CAAC;IAC9C;IAEA,SAASA,SAASA,CAACL,YAAY,EAAE;MAC/B5B,MAAM,CAACoC,WAAW,CAAC,OAAOR,YAAY,EAAE,QAAQ,CAAC;MAEjD,IAAIS,KAAK,GAAGT,YAAY,CAACU,KAAK,CAACjC,KAAK,CAACkC,OAAO,CAAC;MAC7C,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAEA,KAAK,CAACG,KAAK,CAAC,CAAC;MAElC,IAAInC,KAAK,CAACoC,cAAc,CAACb,YAAY,CAAC,EAAE;QACtC,IAAI,CAAEI,IAAI,EAAE;UACV,IAAIU,MAAM,GAAGxB,UAAU,CAACU,YAAY,CAAC;UACrC,IAAIe,gBAAgB,GAAGtC,KAAK,CAACuC,YAAY,CAACF,MAAM,EAAEd,YAAY,CAAC;UAC/DiB,UAAU,CAACF,gBAAgB,CAACL,KAAK,CAACjC,KAAK,CAACkC,OAAO,CAAC,EAAEX,YAAY,CAAC;UAC/D;QACF;QAEAS,KAAK,CAACF,OAAO,CAAC,UAAUW,IAAI,EAAEC,CAAC,EAAE;UAC/B,IAAID,IAAI,KAAK,KAAK,EAAE;YAClBD,UAAU,CAACR,KAAK,CAACW,KAAK,CAACD,CAAC,GAAG,CAAC,CAAC,EAAEnB,YAAY,CAAC;UAE9C,CAAC,MAAM,IAAIkB,IAAI,KAAK,MAAM,EAAE;YAC1B,IAAId,IAAI,EAAE;cACRK,KAAK,CAACY,OAAO,CAAC,cAAc,EAAE,QAAQ,EAAEjB,IAAI,CAAC;YAC/C;YAEA,IAAIK,KAAK,CAACU,CAAC,GAAG,CAAC,CAAC,KAAK,SAAS,EAAE;cAC9BF,UAAU,CAACR,KAAK,CAACW,KAAK,CAACD,CAAC,GAAG,CAAC,CAAC,EAAEnB,YAAY,CAAC;YAE9C,CAAC,MAAM,IAAIS,KAAK,CAACU,CAAC,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;cACpC/C,MAAM,CAACoC,WAAW,CAACC,KAAK,CAACU,CAAC,GAAG,CAAC,CAAC,EAAEf,IAAI,CAAC;cACtCa,UAAU,CAACR,KAAK,CAACW,KAAK,CAACD,CAAC,GAAG,CAAC,CAAC,EAAEnB,YAAY,CAAC;YAC9C;UACF;QACF,CAAC,CAAC;MAEJ,CAAC,MAAM,IAAIS,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;QAC7B,IAAIlB,OAAO,GAAGd,KAAK,CAAC6C,WAAW,CAACC,SAAS,EAAEd,KAAK,CAACvB,IAAI,CAACT,KAAK,CAACkC,OAAO,CAAC,CAAC;QACrEM,UAAU,CAACR,KAAK,CAACW,KAAK,CAAC,CAAC,CAAC,EAAE7B,OAAO,CAAC;MAErC,CAAC,MAAM;QACL,MAAM,IAAIF,KAAK,CAAC,6BAA6B,GAAGW,YAAY,CAAC;MAC/D;IACF;IAEA,SAASiB,UAAUA,CAACR,KAAK,EAAElB,OAAO,EAAE;MAClCnB,MAAM,CAACoD,EAAE,CAACf,KAAK,CAACgB,MAAM,GAAG,CAAC,CAAC;MAC3BrD,MAAM,CAACsD,QAAQ,CAACjB,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC7BrC,MAAM,CAACsD,QAAQ,CAACjB,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;;MAE/B;MACA,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,MAAM,GAAG,CAAC,EAAE;QACvBhB,KAAK,CAACY,OAAO,CAAC,EAAE,CAAC;MACnB;MAEA3B,mBAAmB,CAACe,KAAK,CAACvB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGK,OAAO;IAChD;EACF;EAEA,SAASoC,QAAQA,CAACC,EAAE,EAAE;IACpBxD,MAAM,CAACoC,WAAW,CAACoB,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;IACrC,OAAO,OAAO,GAAGD,EAAE,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACxC;;EAEA;EACA;EACA,IAAIC,sBAAsB,GACxBpC,MAAM,CAACW,IAAI,CAACZ,mBAAmB,CAAC,CAACsC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACpD,IAAID,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC;IACnB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC,CAAC;IACpB,OAAO,CAAC;EACV,CAAC,CAAC;EAEJ,SAASE,UAAUA,CAACP,EAAE,EAAE;IACtB,OAAOvD,OAAO,CAAC+D,OAAO,CAACR,EAAE,CAAC,CAAC;EAC7B;EAEA,IAAIS,YAAY,GAAG1C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAEtC,SAASwC,OAAOA,CAACR,EAAE,EAAE;IACnB,IAAIU,GAAG,GAAGD,YAAY,CAACT,EAAE,CAAC;IAE1B,IAAI,OAAOU,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOA,GAAG;IACZ;IAEA,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB,IAAIC,OAAO,GAAGX,EAAE,CAAClB,KAAK,CAAC,GAAG,CAAC;MAC3B,IAAI8B,YAAY,GAAG,EAAE;MACrB;MACA;MACA,IAAID,OAAO,CAACd,MAAM,KAAK,CAAC,IACpBc,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACvBC,YAAY,GAAG,oBAAoB,GAAGD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,GACvD,yDAAyD,GACzD,iBAAiB;MACvB;MACAD,GAAG,GAAG,IAAIjD,KAAK,CAAC,sBAAsB,GAAGuC,EAAE,GAAG,GAAG,GAAGY,YAAY,CAAC;MACjEF,GAAG,CAACG,IAAI,GAAG,kBAAkB;MAC7B,MAAMH,GAAG;IACX;IAEAD,YAAY,CAACT,EAAE,CAAC,GACdc,mBAAmB,CAACd,EAAE,CAAC,IACvBe,oBAAoB,CAACf,EAAE,CAAC,IACxBgB,kBAAkB,CAAChB,EAAE,CAAC,IACtB,IAAI;IAEN,OAAOQ,OAAO,CAACR,EAAE,CAAC;EACpB;EAEA,SAASc,mBAAmBA,CAACd,EAAE,EAAE;IAC/B,OAAOiB,UAAU,CAAClB,QAAQ,CAACC,EAAE,CAAC,CAAC;EACjC;EAEA,SAASe,oBAAoBA,CAACf,EAAE,EAAE;IAChC,IAAIkB,KAAK;IAETf,sBAAsB,CAACgB,IAAI,CAAC,UAAUC,MAAM,EAAE;MAC5C,IAAIC,KAAK,GAAGxE,KAAK,CAACuC,YAAY,CAC5BvC,KAAK,CAACyE,QAAQ,CAAC,GAAG,EAAEF,MAAM,CAAC,EAC3BvE,KAAK,CAACyE,QAAQ,CAAC,GAAG,EAAEtB,EAAE,CACxB,CAAC;MAED,IAAIqB,KAAK,CAAC7B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;QAC9B,OAAO0B,KAAK,GACVrE,KAAK,CAACyE,QAAQ,CAACxD,mBAAmB,CAACsD,MAAM,CAAC,EAAEC,KAAK,CAAC;MACtD;IACF,CAAC,CAAC;IAEF,OAAOH,KAAK,IAAID,UAAU,CAACpE,KAAK,CAACgB,eAAe,CAACqD,KAAK,CAAC,CAAC;EAC1D;EAEA,SAASF,kBAAkBA,CAAChB,EAAE,EAAE;IAC9B;IACA,OAAOjD,iBAAiB,CAACwE,IAAI,CAACvB,EAAE,CAAC,IAAIiB,UAAU,CAACjB,EAAE,CAAC;EACrD;EAEA,SAASiB,UAAUA,CAACjB,EAAE,EAAE;IACtB,IAAI;MACF,OAAOvD,OAAO,CAAC+D,OAAO,CAACR,EAAE,CAAC;IAC5B,CAAC,CAAC,OAAO9C,CAAC,EAAE;MACV,OAAO,IAAI;IACb;EACF;EAEAsE,OAAO,CAAC/E,OAAO,GAAG8D,UAAU;EAC5BiB,OAAO,CAAChB,OAAO,GAAGD,UAAU,CAACC,OAAO,GAAGA,OAAO;AAAC,EAAAiB,IAAA,OAAAC,MAAA", "ignoreList": [], "file": "tools/static-assets/server/npm-require.js.map"}