{"version": 3, "sources": ["meteor://💻app/packages/mongo-decimal/decimal.js"], "names": ["module", "export", "Decimal", "EJSON", "link", "v", "__reifyWaitForDeps__", "prototype", "typeName", "toJSONValue", "toJSON", "clone", "toString", "addType", "str", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,OAAO,EAACA,CAAA,KAAIA;IAAO,CAAC,CAAC;IAAC,IAAIC,KAAK;IAACH,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACD,KAAKA,CAACE,CAAC,EAAC;QAACF,KAAK,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIH,OAAO;IAACF,MAAM,CAACI,IAAI,CAAC,YAAY,EAAC;MAACF,OAAOA,CAACG,CAAC,EAAC;QAACH,OAAO,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAG7NJ,OAAO,CAACK,SAAS,CAACC,QAAQ,GAAG,YAAW;MACtC,OAAO,SAAS;IAClB,CAAC;IAEDN,OAAO,CAACK,SAAS,CAACE,WAAW,GAAG,YAAY;MAC1C,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC;IACtB,CAAC;IAEDR,OAAO,CAACK,SAAS,CAACI,KAAK,GAAG,YAAY;MACpC,OAAOT,OAAO,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAC;IACjC,CAAC;IAEDT,KAAK,CAACU,OAAO,CAAC,SAAS,EAAE,UAAUC,GAAG,EAAE;MACtC,OAAOZ,OAAO,CAACY,GAAG,CAAC;IACrB,CAAC,CAAC;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/mongo-decimal.js", "sourcesContent": ["import { EJSON } from 'meteor/ejson';\nimport { Decimal } from 'decimal.js';\n\nDecimal.prototype.typeName = function() {\n  return 'Decimal';\n};\n\nDecimal.prototype.toJSONValue = function () {\n  return this.toJSON();\n};\n\nDecimal.prototype.clone = function () {\n  return Decimal(this.toString());\n};\n\nEJSON.addType('Decimal', function (str) {\n  return Decimal(str);\n});\n\nexport { Decimal };\n"]}