{"version": 3, "sources": ["meteor://💻app/packages/ddp-rate-limiter/ddp-rate-limiter.js"], "names": ["module", "export", "DDPRateLimiter", "RateLimiter", "link", "v", "__reifyWaitForDeps__", "errorMessage", "rateLimitResult", "concat", "Math", "ceil", "timeToReset", "errorMessageByRule", "Map", "rateLimiter", "getErrorMessage", "has", "ruleId", "message", "get", "setErrorMessage", "setErrorMessageOnRule", "set", "addRule", "matcher", "numRequests", "timeInterval", "callback", "printRules", "rules", "removeRule", "id", "_increment", "input", "increment", "_check", "check", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,cAAc,EAACA,CAAA,KAAIA;IAAc,CAAC,CAAC;IAAC,IAAIC,WAAW;IAACH,MAAM,CAACI,IAAI,CAAC,mBAAmB,EAAC;MAACD,WAAWA,CAACE,CAAC,EAAC;QAACF,WAAW,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAElM;IACA;IACA,MAAMJ,cAAc,GAAG,CAAC,CAAC;IAEzB,IAAIK,YAAY,GAAIC,eAAe,IAAK;MACtC,OAAO,4DAA4D,MAAAC,MAAA,CAC9DC,IAAI,CAACC,IAAI,CAACH,eAAe,CAACI,WAAW,GAAG,IAAI,CAAC,qBAAkB,GAClE,eAAe;IACnB,CAAC;;IAED;IACA,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAEpC,MAAMC,WAAW,GAAG,IAAIZ,WAAW,CAAC,CAAC;IAErCD,cAAc,CAACc,eAAe,GAAIR,eAAe,IAAK;MACpD;MACA,IAAIK,kBAAkB,CAACI,GAAG,CAACT,eAAe,CAACU,MAAM,CAAC,EAAE;QAClD,MAAMC,OAAO,GAAGN,kBAAkB,CAACO,GAAG,CAACZ,eAAe,CAACU,MAAM,CAAC;QAC9D;QACA,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;UACjC;UACA,OAAOA,OAAO,CAACX,eAAe,CAAC;QACjC;QACA;QACA,OAAOW,OAAO;MACjB;;MAEC;MACA,IAAI,OAAOZ,YAAY,KAAK,UAAU,EAAE;QACtC,OAAOA,YAAY,CAACC,eAAe,CAAC;MACtC;MACA,OAAOD,YAAY;IACrB,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAL,cAAc,CAACmB,eAAe,GAAIF,OAAO,IAAK;MAC5CZ,YAAY,GAAGY,OAAO;IACxB,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAjB,cAAc,CAACoB,qBAAqB,GAAG,CAACJ,MAAM,EAAEC,OAAO,KAAK;MAC1DN,kBAAkB,CAACU,GAAG,CAACL,MAAM,EAAEC,OAAO,CAAC;IACzC,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAjB,cAAc,CAACsB,OAAO,GAAG,CAACC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,KACpEb,WAAW,CAACS,OAAO,CAACC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,CAAC;IAEnE1B,cAAc,CAAC2B,UAAU,GAAG,MAAMd,WAAW,CAACe,KAAK;;IAEnD;AACA;AACA;AACA;AACA;AACA;AACA;IACA5B,cAAc,CAAC6B,UAAU,GAAGC,EAAE,IAAIjB,WAAW,CAACgB,UAAU,CAACC,EAAE,CAAC;;IAE5D;IACA;IACA9B,cAAc,CAAC+B,UAAU,GAAIC,KAAK,IAAK;MACrCnB,WAAW,CAACoB,SAAS,CAACD,KAAK,CAAC;IAC9B,CAAC;IAEDhC,cAAc,CAACkC,MAAM,GAAGF,KAAK,IAAInB,WAAW,CAACsB,KAAK,CAACH,KAAK,CAAC;IAACI,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/ddp-rate-limiter.js", "sourcesContent": ["import { RateLimiter } from 'meteor/rate-limit';\n\n// Rate Limiter built into DDP with a default error message. See README or\n// online documentation for more details.\nconst DDPRateLimiter = {};\n\nlet errorMessage = (rateLimitResult) => {\n  return 'Error, too many requests. Please slow down. You must wait ' +\n    `${Math.ceil(rateLimitResult.timeToReset / 1000)} seconds before ` +\n    'trying again.';\n};\n\n// Store rule specific error messages.\nconst errorMessageByRule = new Map();\n\nconst rateLimiter = new RateLimiter();\n\nDDPRateLimiter.getErrorMessage = (rateLimitResult) => {\n  // If there is a specific error message for this rule, use it.\n  if (errorMessageByRule.has(rateLimitResult.ruleId)) {\n    const message = errorMessageByRule.get(rateLimitResult.ruleId);\n    // if it's a function, we need to call it\n    if (typeof message === 'function') {\n      // call the function with the rateLimitResult\n      return message(rateLimitResult);\n    }\n    // otherwise, just return the string\n    return message;\n }\n\n  // Otherwise, use the default error message.\n  if (typeof errorMessage === 'function') {\n    return errorMessage(rateLimitResult);\n  }\n  return errorMessage;\n};\n\n/**\n * @summary Set error message text when method or subscription rate limit\n * exceeded.\n * @param {string|function} message Functions are passed in an object with a\n * `timeToReset` field that specifies the number of milliseconds until the next\n * method or subscription is allowed to run. The function must return a string\n * of the error message.\n * @locus Server\n */\nDDPRateLimiter.setErrorMessage = (message) => {\n  errorMessage = message;\n};\n\n/**\n * @summary Set error message text when method or subscription rate limit\n * exceeded for a specific rule.\n * @param {string} ruleId The ruleId returned from `addRule`\n * @param {string|function} message Functions are passed in an object with a\n * `timeToReset` field that specifies the number of milliseconds until the next\n * method or subscription is allowed to run. The function must return a string\n * of the error message.\n * @locus Server\n */\nDDPRateLimiter.setErrorMessageOnRule = (ruleId, message) => {\n  errorMessageByRule.set(ruleId, message);\n};\n\n/**\n * @summary\n * Add a rule that matches against a stream of events describing method or\n * subscription attempts. Each event is an object with the following\n * properties:\n *\n * - `type`: Either \"method\" or \"subscription\"\n * - `name`: The name of the method or subscription being called\n * - `userId`: The user ID attempting the method or subscription\n * - `connectionId`: A string representing the user's DDP connection\n * - `clientAddress`: The IP address of the user\n *\n * Returns unique `ruleId` that can be passed to `removeRule` and `setErrorMessageOnRule`\n *\n * @param {Object} matcher\n *   Matchers specify which events are counted towards a rate limit. A matcher\n *   is an object that has a subset of the same properties as the event objects\n *   described above. Each value in a matcher object is one of the following:\n *\n *   - a string: for the event to satisfy the matcher, this value must be equal\n *   to the value of the same property in the event object\n *\n *   - a function: for the event to satisfy the matcher, the function must\n *   evaluate to true when passed the value of the same property\n *   in the event object\n *\n * Here's how events are counted: Each event that satisfies the matcher's\n * filter is mapped to a bucket. Buckets are uniquely determined by the\n * event object's values for all properties present in both the matcher and\n * event objects.\n *\n * @param {number} numRequests  number of requests allowed per time interval.\n * Default = 10.\n * @param {number} timeInterval time interval in milliseconds after which\n * rule's counters are reset. Default = 1000.\n * @param {function} callback function to be called after a rule is executed.\n * @locus Server\n */\nDDPRateLimiter.addRule = (matcher, numRequests, timeInterval, callback) => \n  rateLimiter.addRule(matcher, numRequests, timeInterval, callback);\n\nDDPRateLimiter.printRules = () => rateLimiter.rules;\n\n/**\n * @summary Removes the specified rule from the rate limiter. If rule had\n * hit a rate limit, that limit is removed as well.\n * @param  {string} id 'ruleId' returned from `addRule`\n * @return {boolean}    True if a rule was removed.\n * @locus Server\n */\nDDPRateLimiter.removeRule = id => rateLimiter.removeRule(id);\n\n// This is accessed inside livedata_server.js, but shouldn't be called by any\n// user.\nDDPRateLimiter._increment = (input) => {\n  rateLimiter.increment(input);\n};\n\nDDPRateLimiter._check = input => rateLimiter.check(input);\n\nexport { DDPRateLimiter };\n"]}