[{"type": "js", "data": "Package[\"core-runtime\"].queue(\"null\",function () {/* Imports for global scope */\n\nMongoInternals = Package.mongo.MongoInternals;\nMongo = Package.mongo.Mongo;\nReactiveVar = Package['reactive-var'].ReactiveVar;\nECMAScript = Package.ecmascript.ECMAScript;\nAccounts = Package['accounts-base'].Accounts;\nEmail = Package.email.Email;\nEmailInternals = Package.email.EmailInternals;\nRoles = Package['alanning:roles'].Roles;\nRolesCollection = Package['alanning:roles'].RolesCollection;\nRoleAssignmentCollection = Package['alanning:roles'].RoleAssignmentCollection;\nMeteor = Package.meteor.Meteor;\nglobal = Package.meteor.global;\nmeteorEnv = Package.meteor.meteorEnv;\nEmitterPromise = Package.meteor.EmitterPromise;\nWebApp = Package.webapp.WebApp;\nWebAppInternals = Package.webapp.WebAppInternals;\nmain = Package.webapp.main;\nDDP = Package['ddp-client'].DDP;\nDDPServer = Package['ddp-server'].DDPServer;\nLaunchScreen = Package['launch-screen'].LaunchScreen;\nmeteorInstall = Package.modules.meteorInstall;\nPromise = Package.promise.Promise;\nAutoupdate = Package.autoupdate.Autoupdate;\n\nvar require = meteorInstall({\"imports\":{\"api\":{\"links.js\":function module(require,exports,module){\n\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n//                                                                                                                  //\n// imports/api/links.js                                                                                             //\n//                                                                                                                  //\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n                                                                                                                    //\n!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {\n  \"use strict\";\n  try {\n    module.export({\n      LinksCollection: () => LinksCollection\n    });\n    let Mongo;\n    module.link(\"meteor/mongo\", {\n      Mongo(v) {\n        Mongo = v;\n      }\n    }, 0);\n    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();\n    const LinksCollection = new Mongo.Collection('links');\n    __reify_async_result__();\n  } catch (_reifyError) {\n    return __reify_async_result__(_reifyError);\n  }\n  __reify_async_result__()\n}, {\n  self: this,\n  async: false\n});\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n},\"tasks.js\":function module(require,exports,module){\n\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n//                                                                                                                  //\n// imports/api/tasks.js                                                                                             //\n//                                                                                                                  //\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n                                                                                                                    //\n!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {\n  \"use strict\";\n  try {\n    let _objectSpread;\n    module.link(\"@babel/runtime/helpers/objectSpread2\", {\n      default(v) {\n        _objectSpread = v;\n      }\n    }, 0);\n    module.export({\n      Tasks: () => Tasks,\n      taskCategories: () => taskCategories,\n      taskLabels: () => taskLabels\n    });\n    let Meteor;\n    module.link(\"meteor/meteor\", {\n      Meteor(v) {\n        Meteor = v;\n      }\n    }, 0);\n    let Mongo;\n    module.link(\"meteor/mongo\", {\n      Mongo(v) {\n        Mongo = v;\n      }\n    }, 1);\n    let check;\n    module.link(\"meteor/check\", {\n      check(v) {\n        check = v;\n      }\n    }, 2);\n    let Match;\n    module.link(\"meteor/check\", {\n      Match(v) {\n        Match = v;\n      }\n    }, 3);\n    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();\n    const Tasks = new Mongo.Collection('tasks');\n    const taskCategories = ['Development', 'Design', 'Marketing', 'Sales', 'Support', 'Planning', 'Research', 'Other'];\n    const taskLabels = [{\n      name: 'Bug',\n      color: '#ef4444'\n    }, {\n      name: 'Feature',\n      color: '#3b82f6'\n    }, {\n      name: 'Enhancement',\n      color: '#10b981'\n    }, {\n      name: 'Documentation',\n      color: '#8b5cf6'\n    }, {\n      name: 'Urgent',\n      color: '#f59e0b'\n    }, {\n      name: 'Blocked',\n      color: '#6b7280'\n    }];\n    if (Meteor.isServer) {\n      // Publications\n      Meteor.publish('tasks', async function () {\n        var _user$roles;\n        if (!this.userId) {\n          return this.ready();\n        }\n\n        // Get user's role\n        const user = await Meteor.users.findOneAsync(this.userId);\n        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes('admin');\n\n        // If admin, show all tasks\n        if (isAdmin) {\n          return Tasks.find({}, {\n            sort: {\n              createdAt: -1\n            },\n            fields: {\n              title: 1,\n              description: 1,\n              startDate: 1,\n              dueDate: 1,\n              priority: 1,\n              status: 1,\n              assignedTo: 1,\n              checklist: 1,\n              category: 1,\n              labels: 1,\n              progress: 1,\n              attachments: 1,\n              links: 1,\n              createdAt: 1,\n              createdBy: 1,\n              updatedAt: 1,\n              updatedBy: 1\n            }\n          });\n        }\n\n        // For team members, show tasks they're assigned to or created\n        return Tasks.find({\n          $or: [{\n            assignedTo: this.userId\n          }, {\n            createdBy: this.userId\n          }]\n        }, {\n          sort: {\n            createdAt: -1\n          },\n          fields: {\n            title: 1,\n            description: 1,\n            startDate: 1,\n            dueDate: 1,\n            priority: 1,\n            status: 1,\n            assignedTo: 1,\n            checklist: 1,\n            category: 1,\n            labels: 1,\n            progress: 1,\n            attachments: 1,\n            links: 1,\n            createdAt: 1,\n            createdBy: 1,\n            updatedAt: 1,\n            updatedBy: 1\n          }\n        });\n      });\n\n      // Publish user data for tasks\n      Meteor.publish('taskUsers', function () {\n        console.log('Starting taskUsers publication');\n        if (!this.userId) {\n          console.log('No userId, returning ready');\n          return this.ready();\n        }\n\n        // Get all tasks\n        const tasks = Tasks.find({}).fetch();\n        console.log('Found tasks:', tasks.length);\n\n        // Collect all user IDs from tasks\n        const userIds = new Set();\n        tasks.forEach(task => {\n          // Add users who uploaded attachments\n          if (task.attachments) {\n            task.attachments.forEach(attachment => {\n              if (attachment.uploadedBy) {\n                userIds.add(String(attachment.uploadedBy));\n              }\n            });\n          }\n          // Add users who added links\n          if (task.links) {\n            task.links.forEach(link => {\n              if (link.addedBy) {\n                userIds.add(String(link.addedBy));\n              }\n            });\n          }\n          // Add assigned users\n          if (task.assignedTo) {\n            task.assignedTo.forEach(userId => {\n              userIds.add(String(userId));\n            });\n          }\n        });\n        const userIdArray = Array.from(userIds);\n        console.log('Publishing user data for IDs:', userIdArray);\n\n        // Find users and log what we found\n        const users = Meteor.users.find({\n          _id: {\n            $in: userIdArray\n          }\n        }, {\n          fields: {\n            _id: 1,\n            emails: 1,\n            roles: 1,\n            'profile.firstName': 1,\n            'profile.lastName': 1,\n            'profile.role': 1,\n            'profile.department': 1,\n            'profile.skills': 1,\n            'profile.joinDate': 1,\n            createdAt: 1\n          }\n        }).fetch();\n        console.log('Found users:', users.map(u => {\n          var _u$profile, _u$profile2;\n          return {\n            _id: u._id,\n            name: \"\".concat(((_u$profile = u.profile) === null || _u$profile === void 0 ? void 0 : _u$profile.firstName) || '', \" \").concat(((_u$profile2 = u.profile) === null || _u$profile2 === void 0 ? void 0 : _u$profile2.lastName) || '').trim(),\n            hasProfile: !!u.profile\n          };\n        }));\n\n        // Return the cursor\n        return Meteor.users.find({\n          _id: {\n            $in: userIdArray\n          }\n        }, {\n          fields: {\n            _id: 1,\n            emails: 1,\n            roles: 1,\n            'profile.firstName': 1,\n            'profile.lastName': 1,\n            'profile.role': 1,\n            'profile.department': 1,\n            'profile.skills': 1,\n            'profile.joinDate': 1,\n            createdAt: 1\n          }\n        });\n      });\n\n      // Add a specific publication for a single task\n      Meteor.publish('task', function (taskId) {\n        var _user$roles2;\n        check(taskId, String);\n        if (!this.userId) {\n          return this.ready();\n        }\n        const user = Meteor.users.findOne(this.userId);\n        const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes('admin');\n\n        // Return a cursor that will update reactively\n        const cursor = Tasks.find({\n          _id: taskId\n        }, {\n          fields: {\n            title: 1,\n            description: 1,\n            startDate: 1,\n            dueDate: 1,\n            priority: 1,\n            status: 1,\n            assignedTo: 1,\n            checklist: 1,\n            category: 1,\n            labels: 1,\n            progress: 1,\n            attachments: 1,\n            links: 1,\n            createdAt: 1,\n            createdBy: 1,\n            updatedAt: 1,\n            updatedBy: 1\n          }\n        });\n\n        // Log for debugging\n        console.log('Publishing task:', taskId);\n        cursor.observe({\n          added: doc => console.log('Task added to publication:', doc._id),\n          changed: doc => console.log('Task changed in publication:', doc._id),\n          removed: doc => console.log('Task removed from publication:', doc._id)\n        });\n        return cursor;\n      });\n    }\n    Meteor.methods({\n      async 'tasks.insert'(task) {\n        check(task, {\n          title: String,\n          description: String,\n          startDate: Date,\n          dueDate: Date,\n          priority: String,\n          status: String,\n          assignedTo: Array,\n          checklist: Array,\n          category: String,\n          labels: Array,\n          progress: Number\n        });\n        if (!this.userId) {\n          throw new Meteor.Error('Not authorized.');\n        }\n        console.log('Creating new task:', task); // Debug log\n\n        // Process checklist items\n        const processedChecklist = task.checklist.map(item => ({\n          text: item.text,\n          completed: item.completed || false\n        }));\n        const taskToInsert = _objectSpread(_objectSpread({}, task), {}, {\n          createdAt: new Date(),\n          createdBy: this.userId,\n          updatedAt: new Date(),\n          updatedBy: this.userId,\n          progress: task.progress || 0,\n          status: 'pending',\n          // Default status\n          checklist: processedChecklist,\n          labels: task.labels || [],\n          category: task.category || '',\n          assignedTo: task.assignedTo || []\n        });\n        console.log('Inserting task with values:', taskToInsert); // Debug log\n\n        try {\n          const result = await Tasks.insertAsync(taskToInsert);\n          console.log('Task created successfully:', result); // Debug log\n          return result;\n        } catch (error) {\n          console.error('Error creating task:', error);\n          throw new Meteor.Error('task-creation-failed', error.message);\n        }\n      },\n      async 'tasks.update'(taskId, task) {\n        try {\n          var _user$roles3;\n          console.log('Starting task update:', {\n            taskId,\n            task\n          });\n          check(taskId, String);\n          check(task, {\n            title: String,\n            description: String,\n            startDate: Date,\n            dueDate: Date,\n            priority: String,\n            assignedTo: Array,\n            checklist: Array,\n            category: Match.Optional(String),\n            labels: Match.Optional(Array),\n            progress: Match.Optional(Number),\n            status: Match.Optional(String),\n            attachments: Match.Optional(Array)\n          });\n          if (!this.userId) {\n            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n          }\n          const existingTask = await Tasks.findOneAsync(taskId);\n          if (!existingTask) {\n            throw new Meteor.Error('not-found', 'Task not found');\n          }\n\n          // Check if user is assigned to the task or is admin\n          const user = await Meteor.users.findOneAsync(this.userId);\n          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles3 = user.roles) === null || _user$roles3 === void 0 ? void 0 : _user$roles3.includes('admin');\n          if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {\n            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');\n          }\n\n          // Calculate progress based on checklist\n          const completedItems = task.checklist.filter(item => item.completed).length;\n          const totalItems = task.checklist.length;\n          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;\n\n          // Update task status based on progress\n          let status = task.status;\n          if (progress === 100) {\n            status = 'completed';\n          } else if (progress > 0) {\n            status = 'in-progress';\n          } else {\n            status = 'pending';\n          }\n          const taskToUpdate = _objectSpread(_objectSpread({}, task), {}, {\n            updatedAt: new Date(),\n            updatedBy: this.userId,\n            progress,\n            status,\n            category: task.category || existingTask.category || '',\n            labels: task.labels || existingTask.labels || [],\n            attachments: task.attachments || existingTask.attachments || []\n          });\n          const result = await Tasks.updateAsync({\n            _id: taskId\n          }, {\n            $set: taskToUpdate\n          });\n          if (result === 0) {\n            throw new Meteor.Error('update-failed', 'Failed to update task');\n          }\n          return result;\n        } catch (error) {\n          console.error('Error updating task:', error);\n          if (error instanceof Meteor.Error) {\n            throw error;\n          }\n          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n        }\n      },\n      async 'tasks.delete'(taskId) {\n        check(taskId, String);\n        if (!this.userId) {\n          throw new Meteor.Error('Not authorized.');\n        }\n        try {\n          const result = await Tasks.removeAsync(taskId);\n          if (result === 0) {\n            throw new Meteor.Error('not-found', 'Task not found');\n          }\n          return result;\n        } catch (error) {\n          console.error('Error deleting task:', error);\n          throw new Meteor.Error('task-delete-failed', error.message);\n        }\n      },\n      'tasks.updateProgress'(taskId, progress) {\n        check(taskId, String);\n        check(progress, Number);\n        if (!this.userId) {\n          throw new Meteor.Error('Not authorized.');\n        }\n        const task = Tasks.findOne(taskId);\n        if (!task) {\n          throw new Meteor.Error('Task not found.');\n        }\n\n        // Check if user is assigned to the task\n        if (!task.assignedTo.includes(this.userId)) {\n          throw new Meteor.Error('Not authorized to modify this task.');\n        }\n\n        // Update task status based on progress\n        let status = task.status;\n        if (progress === 100) {\n          status = 'completed';\n        } else if (progress > 0) {\n          status = 'in-progress';\n        }\n        return Tasks.update(taskId, {\n          $set: {\n            progress,\n            status,\n            updatedAt: new Date(),\n            updatedBy: this.userId\n          }\n        });\n      },\n      async 'tasks.toggleChecklistItem'(taskId, itemIndex) {\n        try {\n          var _user$roles4;\n          console.log('Starting toggleChecklistItem with:', {\n            taskId,\n            itemIndex,\n            userId: this.userId\n          });\n          if (!this.userId) {\n            console.log('No user ID found');\n            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n          }\n\n          // Validate inputs\n          if (!taskId || typeof taskId !== 'string') {\n            console.log('Invalid taskId:', taskId);\n            throw new Meteor.Error('invalid-input', 'Invalid task ID');\n          }\n          if (typeof itemIndex !== 'number' || itemIndex < 0) {\n            console.log('Invalid itemIndex:', itemIndex);\n            throw new Meteor.Error('invalid-input', 'Invalid checklist item index');\n          }\n          const task = await Tasks.findOneAsync(taskId);\n          console.log('Found task:', task);\n          if (!task) {\n            console.log('Task not found');\n            throw new Meteor.Error('not-found', 'Task not found');\n          }\n\n          // Check if user is assigned to the task or is admin\n          const user = await Meteor.users.findOneAsync(this.userId);\n          console.log('Found user:', user);\n          const isAdmin = user === null || user === void 0 ? void 0 : (_user$roles4 = user.roles) === null || _user$roles4 === void 0 ? void 0 : _user$roles4.includes('admin');\n          console.log('Is admin:', isAdmin);\n          if (!isAdmin && !task.assignedTo.includes(this.userId)) {\n            console.log('User not authorized:', {\n              userId: this.userId,\n              assignedTo: task.assignedTo\n            });\n            throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');\n          }\n          const checklist = task.checklist || [];\n          console.log('Current checklist:', checklist);\n          if (itemIndex >= checklist.length) {\n            console.log('Invalid item index:', {\n              itemIndex,\n              checklistLength: checklist.length\n            });\n            throw new Meteor.Error('invalid-index', 'Invalid checklist item index');\n          }\n\n          // Create a new array to ensure reactivity\n          const updatedChecklist = [...checklist];\n          updatedChecklist[itemIndex] = _objectSpread(_objectSpread({}, updatedChecklist[itemIndex]), {}, {\n            completed: !updatedChecklist[itemIndex].completed\n          });\n          console.log('Updated checklist:', updatedChecklist);\n\n          // Calculate progress\n          const completedItems = updatedChecklist.filter(item => item.completed).length;\n          const totalItems = updatedChecklist.length;\n          const progress = totalItems > 0 ? Math.round(completedItems / totalItems * 100) : 0;\n\n          // Update task status based on progress\n          let status;\n          if (progress === 100) {\n            status = 'completed';\n          } else if (progress > 0) {\n            status = 'in-progress';\n          } else {\n            status = 'pending';\n          }\n          console.log('Updating task with:', {\n            taskId,\n            updatedChecklist,\n            progress,\n            status\n          });\n\n          // First verify the task still exists\n          const existingTask = await Tasks.findOneAsync(taskId);\n          if (!existingTask) {\n            throw new Meteor.Error('task-not-found', 'Task no longer exists');\n          }\n\n          // Perform the update\n          const updateResult = await Tasks.updateAsync({\n            _id: taskId\n          }, {\n            $set: {\n              checklist: updatedChecklist,\n              progress,\n              status,\n              updatedAt: new Date(),\n              updatedBy: this.userId\n            }\n          });\n          console.log('Update result:', updateResult);\n          if (updateResult === 0) {\n            throw new Meteor.Error('update-failed', 'Failed to update task');\n          }\n\n          // Verify the update\n          const updatedTask = await Tasks.findOneAsync(taskId);\n          console.log('Task after update:', updatedTask);\n          return updateResult;\n        } catch (error) {\n          console.error('Error in toggleChecklistItem:', error);\n          if (error instanceof Meteor.Error) {\n            throw error;\n          }\n          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n        }\n      },\n      async 'tasks.addAttachment'(taskId, fileData) {\n        try {\n          if (!this.userId) {\n            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n          }\n          const task = await Tasks.findOneAsync(taskId);\n          if (!task) {\n            throw new Meteor.Error('not-found', 'Task not found');\n          }\n\n          // Check if user is assigned to the task\n          if (!task.assignedTo.includes(this.userId)) {\n            throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');\n          }\n          if (!fileData || !fileData.name || !fileData.data) {\n            throw new Meteor.Error('invalid-input', 'Invalid file data');\n          }\n\n          // Ensure we're storing the user ID as a string\n          const uploaderId = String(this.userId);\n\n          // Add the file data to attachments with uploader info\n          const result = await Tasks.updateAsync({\n            _id: taskId\n          }, {\n            $push: {\n              attachments: {\n                name: fileData.name,\n                type: fileData.type,\n                data: fileData.data,\n                uploadedAt: new Date(),\n                uploadedBy: uploaderId\n              }\n            },\n            $set: {\n              updatedAt: new Date(),\n              updatedBy: uploaderId\n            }\n          });\n          if (result === 0) {\n            throw new Meteor.Error('update-failed', 'Failed to add attachment');\n          }\n\n          // Get and return the updated task\n          const updatedTask = await Tasks.findOneAsync(taskId);\n          console.log('Task after adding attachment:', updatedTask);\n          return updatedTask;\n        } catch (error) {\n          console.error('Error adding attachment:', error);\n          if (error instanceof Meteor.Error) {\n            throw error;\n          }\n          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n        }\n      },\n      async 'tasks.addLink'(taskId, link) {\n        try {\n          if (!this.userId) {\n            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n          }\n          const task = await Tasks.findOneAsync(taskId);\n          if (!task) {\n            throw new Meteor.Error('not-found', 'Task not found');\n          }\n\n          // Check if user is assigned to the task\n          if (!task.assignedTo.includes(this.userId)) {\n            throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');\n          }\n          if (!link) {\n            throw new Meteor.Error('invalid-input', 'Link URL is required');\n          }\n\n          // Validate URL format\n          try {\n            new URL(link);\n          } catch (e) {\n            throw new Meteor.Error('invalid-input', 'Invalid URL format');\n          }\n\n          // Add the link to links array with adder info\n          const result = await Tasks.updateAsync({\n            _id: taskId\n          }, {\n            $push: {\n              links: {\n                url: link,\n                addedAt: new Date(),\n                addedBy: String(this.userId)\n              }\n            },\n            $set: {\n              updatedAt: new Date(),\n              updatedBy: String(this.userId)\n            }\n          });\n          if (result === 0) {\n            throw new Meteor.Error('update-failed', 'Failed to add link');\n          }\n\n          // Get and return the updated task\n          const updatedTask = await Tasks.findOneAsync(taskId);\n          console.log('Task after adding link:', updatedTask);\n          return updatedTask;\n        } catch (error) {\n          console.error('Error adding link:', error);\n          if (error instanceof Meteor.Error) {\n            throw error;\n          }\n          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n        }\n      },\n      async 'tasks.removeAttachment'(taskId, attachmentIndex) {\n        try {\n          if (!this.userId) {\n            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n          }\n          const task = await Tasks.findOneAsync(taskId);\n          if (!task) {\n            throw new Meteor.Error('not-found', 'Task not found');\n          }\n\n          // Check if the attachment exists\n          if (!task.attachments || !task.attachments[attachmentIndex]) {\n            throw new Meteor.Error('not-found', 'Attachment not found');\n          }\n          const attachment = task.attachments[attachmentIndex];\n\n          // Convert both IDs to strings for comparison\n          const currentUserId = String(this.userId);\n          const uploadedById = String(attachment.uploadedBy);\n\n          // Only allow the uploader to remove the attachment\n          if (currentUserId !== uploadedById) {\n            throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');\n          }\n\n          // Create a new array without the specified attachment\n          const updatedAttachments = [...task.attachments];\n          updatedAttachments.splice(attachmentIndex, 1);\n\n          // Update the task with the new attachments array\n          const result = await Tasks.updateAsync({\n            _id: taskId\n          }, {\n            $set: {\n              attachments: updatedAttachments,\n              updatedAt: new Date(),\n              updatedBy: currentUserId\n            }\n          });\n          if (result === 0) {\n            throw new Meteor.Error('update-failed', 'Failed to remove attachment');\n          }\n\n          // Get and return the updated task\n          const updatedTask = await Tasks.findOneAsync(taskId);\n          console.log('Task after removing attachment:', updatedTask);\n          return updatedTask;\n        } catch (error) {\n          console.error('Error removing attachment:', error);\n          if (error instanceof Meteor.Error) {\n            throw error;\n          }\n          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n        }\n      },\n      async 'tasks.removeLink'(taskId, linkIndex) {\n        try {\n          if (!this.userId) {\n            throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n          }\n          const task = await Tasks.findOneAsync(taskId);\n          if (!task) {\n            throw new Meteor.Error('not-found', 'Task not found');\n          }\n\n          // Check if the link exists\n          if (!task.links || !task.links[linkIndex]) {\n            throw new Meteor.Error('not-found', 'Link not found');\n          }\n          const link = task.links[linkIndex];\n\n          // Convert both IDs to strings for comparison\n          const currentUserId = String(this.userId);\n          const addedById = String(link.addedBy);\n\n          // Only allow the user who added the link to remove it\n          if (currentUserId !== addedById) {\n            throw new Meteor.Error('not-authorized', 'You can only remove your own links');\n          }\n\n          // Create a new array without the specified link\n          const updatedLinks = [...task.links];\n          updatedLinks.splice(linkIndex, 1);\n\n          // Update the task with the new links array\n          const result = await Tasks.updateAsync({\n            _id: taskId\n          }, {\n            $set: {\n              links: updatedLinks,\n              updatedAt: new Date(),\n              updatedBy: currentUserId\n            }\n          });\n          if (result === 0) {\n            throw new Meteor.Error('update-failed', 'Failed to remove link');\n          }\n\n          // Get and return the updated task\n          const updatedTask = await Tasks.findOneAsync(taskId);\n          console.log('Task after removing link:', updatedTask);\n          return updatedTask;\n        } catch (error) {\n          console.error('Error removing link:', error);\n          if (error instanceof Meteor.Error) {\n            throw error;\n          }\n          throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n        }\n      },\n      async 'tasks.findOne'(taskId) {\n        check(taskId, String);\n        if (!this.userId) {\n          throw new Meteor.Error('Not authorized.');\n        }\n        const task = await Tasks.findOneAsync(taskId);\n        if (!task) {\n          throw new Meteor.Error('task-not-found', 'Task not found');\n        }\n        return task;\n      }\n    });\n    __reify_async_result__();\n  } catch (_reifyError) {\n    return __reify_async_result__(_reifyError);\n  }\n  __reify_async_result__()\n}, {\n  self: this,\n  async: false\n});\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n}}},\"server\":{\"main.js\":function module(require,exports,module){\n\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n//                                                                                                                  //\n// server/main.js                                                                                                   //\n//                                                                                                                  //\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n                                                                                                                    //\n!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {\n  \"use strict\";\n  try {\n    let _objectSpread;\n    module.link(\"@babel/runtime/helpers/objectSpread2\", {\n      default(v) {\n        _objectSpread = v;\n      }\n    }, 0);\n    let Meteor;\n    module.link(\"meteor/meteor\", {\n      Meteor(v) {\n        Meteor = v;\n      }\n    }, 0);\n    let LinksCollection;\n    module.link(\"/imports/api/links\", {\n      LinksCollection(v) {\n        LinksCollection = v;\n      }\n    }, 1);\n    let Accounts;\n    module.link(\"meteor/accounts-base\", {\n      Accounts(v) {\n        Accounts = v;\n      }\n    }, 2);\n    let Email;\n    module.link(\"meteor/email\", {\n      Email(v) {\n        Email = v;\n      }\n    }, 3);\n    let Tasks;\n    module.link(\"/imports/api/tasks\", {\n      Tasks(v) {\n        Tasks = v;\n      }\n    }, 4);\n    let Roles;\n    module.link(\"meteor/alanning:roles\", {\n      Roles(v) {\n        Roles = v;\n      }\n    }, 5);\n    let check;\n    module.link(\"meteor/check\", {\n      check(v) {\n        check = v;\n      }\n    }, 6);\n    let bcrypt;\n    module.link(\"bcrypt\", {\n      default(v) {\n        bcrypt = v;\n      }\n    }, 7);\n    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();\n    async function insertLink(_ref) {\n      let {\n        title,\n        url\n      } = _ref;\n      await LinksCollection.insertAsync({\n        title,\n        url,\n        createdAt: new Date()\n      });\n    }\n    const ADMIN_TOKEN = '123456';\n    Meteor.startup(async () => {\n      var _Meteor$settings$priv;\n      // Ensure indexes for Tasks collection\n      try {\n        await Tasks.createIndex({\n          createdAt: 1\n        });\n        await Tasks.createIndex({\n          assignedTo: 1\n        });\n        await Tasks.createIndex({\n          createdBy: 1\n        });\n\n        // Ensure indexes for Users collection\n        // Note: emails.address index is already created by Meteor accounts system\n        await Meteor.users.createIndex({\n          roles: 1\n        }, {\n          background: true\n        });\n      } catch (error) {\n        console.warn('[Startup] Index creation warning:', error.message);\n      }\n\n      // Check if we have any team members\n      try {\n        const allUsers = await Meteor.users.find().fetchAsync();\n        console.log('[Startup] All users:', allUsers.map(user => {\n          var _user$emails, _user$emails$;\n          return {\n            id: user._id,\n            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,\n            roles: user.roles,\n            profile: user.profile\n          };\n        }));\n\n        // First, ensure all users have roles and createdAt\n        for (const user of allUsers) {\n          const updates = {};\n          if (!user.roles || !Array.isArray(user.roles)) {\n            updates.roles = ['team-member'];\n          }\n          if (!user.createdAt) {\n            updates.createdAt = new Date();\n          }\n          if (Object.keys(updates).length > 0) {\n            var _user$emails2, _user$emails2$;\n            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);\n            await Meteor.users.updateAsync(user._id, {\n              $set: updates\n            });\n          }\n        }\n        const teamMembersCount = await Meteor.users.find({\n          'roles': 'team-member'\n        }).countAsync();\n        console.log('[Startup] Found team members:', teamMembersCount);\n\n        // Create test team members if none exist\n        if (teamMembersCount === 0) {\n          console.log('[Startup] Creating test team members');\n          try {\n            // Create multiple test team members\n            const testMembers = [{\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'John',\n              lastName: 'Doe'\n            }, {\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'Jane',\n              lastName: 'Smith'\n            }];\n            for (const member of testMembers) {\n              const userId = await Accounts.createUserAsync({\n                email: member.email,\n                password: member.password,\n                role: 'team-member',\n                createdAt: new Date(),\n                profile: {\n                  firstName: member.firstName,\n                  lastName: member.lastName,\n                  role: 'team-member',\n                  fullName: \"\".concat(member.firstName, \" \").concat(member.lastName)\n                }\n              });\n\n              // Set the role explicitly\n              await Meteor.users.updateAsync(userId, {\n                $set: {\n                  roles: ['team-member']\n                }\n              });\n              console.log('[Startup] Created test team member:', {\n                id: userId,\n                email: member.email,\n                name: \"\".concat(member.firstName, \" \").concat(member.lastName)\n              });\n            }\n          } catch (error) {\n            console.error('[Startup] Error creating test team members:', error);\n          }\n        }\n      } catch (error) {\n        console.error('[Startup] Error checking team members:', error);\n      }\n\n      // Email configuration from settings\n      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;\n\n      // Configure email SMTP\n      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {\n        process.env.MAIL_URL = \"smtp://\".concat(encodeURIComponent(emailSettings.username), \":\").concat(encodeURIComponent(emailSettings.password), \"@\").concat(emailSettings.server, \":\").concat(emailSettings.port);\n\n        // Test email configuration\n        try {\n          console.log('Testing email configuration...');\n          Email.send({\n            to: emailSettings.username,\n            from: emailSettings.username,\n            subject: 'Test Email',\n            text: 'If you receive this email, your email configuration is working correctly.'\n          });\n          console.log('Test email sent successfully!');\n        } catch (error) {\n          console.error('Error sending test email:', error);\n        }\n      } else {\n        console.warn('Email configuration is missing in settings.json');\n      }\n\n      // Configure account creation - allow login without email verification\n      Accounts.config({\n        sendVerificationEmail: false,\n        // Disable email verification requirement\n        forbidClientAccountCreation: false\n      });\n\n      // Add login validation hook to explicitly allow all login attempts\n      Accounts.validateLoginAttempt(attempt => {\n        var _attempt$error, _attempt$user;\n        console.log('[validateLoginAttempt] Login attempt:', {\n          type: attempt.type,\n          allowed: attempt.allowed,\n          error: (_attempt$error = attempt.error) === null || _attempt$error === void 0 ? void 0 : _attempt$error.message,\n          userId: (_attempt$user = attempt.user) === null || _attempt$user === void 0 ? void 0 : _attempt$user._id,\n          methodName: attempt.methodName\n        });\n\n        // Always allow login attempts - bypass any built-in restrictions\n        if (attempt.user && attempt.type === 'password') {\n          console.log('[validateLoginAttempt] Allowing password login for user:', attempt.user._id);\n          return true;\n        }\n\n        // Allow other types of login attempts as well\n        return true;\n      });\n\n      // Customize verification email\n      Accounts.emailTemplates.siteName = \"Task Management System\";\n      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? \"Task Management System <\".concat(emailSettings.username, \">\") : \"Task Management System <<EMAIL>>\";\n      Accounts.emailTemplates.verifyEmail = {\n        subject() {\n          return \"Verify Your Email Address\";\n        },\n        text(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"Hello,\\n\\n\" + \"To verify your email address (\".concat(emailAddress, \"), please click the link below:\\n\\n\") + \"\".concat(url, \"\\n\\n\") + \"If you did not request this verification, please ignore this email.\\n\\n\" + \"Thanks,\\n\" + \"Your Task Management System Team\";\n        },\n        html(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"\\n        <html>\\n          <body style=\\\"font-family: Arial, sans-serif; padding: 20px; color: #333;\\\">\\n            <h2 style=\\\"color: #00875a;\\\">Verify Your Email Address</h2>\\n            <p>Hello,</p>\\n            <p>To verify your email address (\".concat(emailAddress, \"), please click the button below:</p>\\n            <p style=\\\"margin: 20px 0;\\\">\\n              <a href=\\\"\").concat(url, \"\\\" \\n                 style=\\\"background-color: #00875a; \\n                        color: white; \\n                        padding: 12px 25px; \\n                        text-decoration: none; \\n                        border-radius: 4px;\\n                        display: inline-block;\\\">\\n                Verify Email Address\\n              </a>\\n            </p>\\n            <p>If you did not request this verification, please ignore this email.</p>\\n            <p>Thanks,<br>Your Task Management System Team</p>\\n          </body>\\n        </html>\\n      \");\n        }\n      };\n\n      // If the Links collection is empty, add some data.\n      if ((await LinksCollection.find().countAsync()) === 0) {\n        await insertLink({\n          title: 'Do the Tutorial',\n          url: 'https://www.meteor.com/tutorials/react/creating-an-app'\n        });\n        await insertLink({\n          title: 'Follow the Guide',\n          url: 'https://guide.meteor.com'\n        });\n        await insertLink({\n          title: 'Read the Docs',\n          url: 'https://docs.meteor.com'\n        });\n        await insertLink({\n          title: 'Discussions',\n          url: 'https://forums.meteor.com'\n        });\n      }\n\n      // We publish the entire Links collection to all clients.\n      // In order to be fetched in real-time to the clients\n      Meteor.publish(\"links\", function () {\n        return LinksCollection.find();\n      });\n\n      // Add custom fields to users\n      Accounts.onCreateUser((options, user) => {\n        var _customizedUser$email, _customizedUser$email2;\n        console.log('[onCreateUser] Creating user with options:', {\n          email: options.email,\n          role: options.role,\n          profile: options.profile,\n          createdAt: options.createdAt\n        });\n        const customizedUser = _objectSpread({}, user);\n\n        // Ensure we have a profile\n        customizedUser.profile = options.profile || {};\n\n        // Add role from options\n        const role = options.role || 'team-member';\n        customizedUser.roles = [role];\n\n        // Set createdAt if provided, otherwise use current date\n        customizedUser.createdAt = options.createdAt || new Date();\n        console.log('[onCreateUser] Created user:', {\n          id: customizedUser._id,\n          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,\n          roles: customizedUser.roles,\n          profile: customizedUser.profile,\n          createdAt: customizedUser.createdAt\n        });\n        return customizedUser;\n      });\n\n      // Publish team members\n      Meteor.publish('teamMembers', function () {\n        console.log('[teamMembers] Publication called, userId:', this.userId);\n        if (!this.userId) {\n          console.log('[teamMembers] No userId, returning ready');\n          return this.ready();\n        }\n        try {\n          // Simple query to find all team members\n          const teamMembers = Meteor.users.find({\n            $or: [{\n              'roles': 'team-member'\n            }, {\n              'profile.role': 'team-member'\n            }]\n          }, {\n            fields: {\n              emails: 1,\n              roles: 1,\n              'profile.firstName': 1,\n              'profile.lastName': 1,\n              'profile.fullName': 1,\n              createdAt: 1\n            }\n          });\n          console.log('[teamMembers] Publishing team members');\n          return teamMembers;\n        } catch (error) {\n          console.error('[teamMembers] Error in publication:', error);\n          return this.ready();\n        }\n      });\n\n      // Publish user data with roles\n      Meteor.publish('userData', function () {\n        if (!this.userId) {\n          return this.ready();\n        }\n        console.log('[userData] Publishing data for user:', this.userId);\n        return Meteor.users.find({\n          _id: this.userId\n        }, {\n          fields: {\n            roles: 1,\n            emails: 1,\n            profile: 1\n          }\n        });\n      });\n    });\n\n    // Method to create a new user with role\n    Meteor.methods({\n      'users.create'(_ref2) {\n        let {\n          email,\n          password,\n          role,\n          adminToken,\n          firstName,\n          lastName\n        } = _ref2;\n        // Validate admin token if trying to create admin account\n        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n        }\n\n        // Validate password requirements\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(password)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(password)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(password)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(password)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Create the user\n        try {\n          const userId = Accounts.createUser({\n            email,\n            password,\n            role,\n            // This will be used in onCreateUser callback\n            profile: {\n              role,\n              // Store in profile as well for easy access\n              firstName,\n              lastName,\n              fullName: \"\".concat(firstName, \" \").concat(lastName)\n            }\n          });\n\n          // Send verification email\n          if (userId) {\n            Accounts.sendVerificationEmail(userId);\n          }\n          return userId;\n        } catch (error) {\n          throw new Meteor.Error('create-user-failed', error.message);\n        }\n      },\n      async 'users.getRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'User must be logged in');\n        }\n        try {\n          var _user$roles, _user$profile;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          if (!user) {\n            throw new Meteor.Error('user-not-found', 'User not found');\n          }\n\n          // Check both roles array and profile for role\n          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';\n          return role;\n        } catch (error) {\n          throw new Meteor.Error('get-role-failed', error.message);\n        }\n      },\n      'users.resendVerificationEmail'(email) {\n        // Find user by email\n        const user = Accounts.findUserByEmail(email);\n        if (!user) {\n          throw new Meteor.Error('user-not-found', 'No user found with this email address');\n        }\n\n        // Check if email is already verified\n        const userEmail = user.emails[0];\n        if (userEmail.verified) {\n          throw new Meteor.Error('already-verified', 'This email is already verified');\n        }\n\n        // Send verification email\n        try {\n          Accounts.sendVerificationEmail(user._id, email);\n          return true;\n        } catch (error) {\n          throw new Meteor.Error('verification-email-failed', error.message);\n        }\n      },\n      async 'users.forgotPassword'(data) {\n        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));\n        check(data, {\n          email: String,\n          newPassword: String\n        });\n        const {\n          email,\n          newPassword\n        } = data;\n        console.log('[forgotPassword] Processing request for email:', email);\n\n        // Find user by email using async method\n        console.log('[forgotPassword] Searching for user...');\n        let targetUser = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!targetUser) {\n          console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');\n          targetUser = await Meteor.users.findOneAsync({\n            'emails.address': {\n              $regex: new RegExp(\"^\".concat(email, \"$\"), 'i')\n            }\n          });\n          if (!targetUser) {\n            throw new Meteor.Error('user-not-found', 'No user found with this email address');\n          }\n          console.log('[forgotPassword] User found with case-insensitive search');\n        } else {\n          console.log('[forgotPassword] User found with direct search');\n        }\n\n        // Ensure we have a valid user with ID\n        if (!targetUser || !targetUser._id) {\n          throw new Meteor.Error('user-invalid', 'Found user but missing ID');\n        }\n        console.log('[forgotPassword] Final user ID:', targetUser._id);\n        console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);\n\n        // Validate password requirements\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(newPassword)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Comprehensive password update with debugging\n        try {\n          console.log('[forgotPassword] Starting password update...');\n\n          // First, check current user document\n          console.log('[forgotPassword] Checking current user document...');\n          const currentUser = await Meteor.users.findOneAsync(targetUser._id);\n          console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));\n          if (!currentUser) {\n            throw new Meteor.Error('user-not-found', 'User document not found during update');\n          }\n\n          // Create password hash using bcrypt directly\n          const bcrypt = require('bcrypt');\n          const saltRounds = 10;\n          const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);\n          console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);\n          console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');\n\n          // Try multiple update approaches\n          let updateResult = 0;\n          let successMethod = null;\n\n          // Method 1: Update services.password.bcrypt directly\n          console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');\n          try {\n            updateResult = await Meteor.users.updateAsync(targetUser._id, {\n              $set: {\n                'services.password.bcrypt': hashedPassword\n              }\n            });\n            console.log('[forgotPassword] Method 1 result:', updateResult);\n            if (updateResult === 1) {\n              successMethod = 'Method 1: services.password.bcrypt';\n            }\n          } catch (method1Error) {\n            console.error('[forgotPassword] Method 1 error:', method1Error);\n          }\n\n          // Method 2: Update entire services.password object\n          if (updateResult !== 1) {\n            console.log('[forgotPassword] Method 2: Updating entire services.password object...');\n            try {\n              updateResult = await Meteor.users.updateAsync(targetUser._id, {\n                $set: {\n                  'services.password': {\n                    bcrypt: hashedPassword\n                  }\n                }\n              });\n              console.log('[forgotPassword] Method 2 result:', updateResult);\n              if (updateResult === 1) {\n                successMethod = 'Method 2: entire services.password object';\n              }\n            } catch (method2Error) {\n              console.error('[forgotPassword] Method 2 error:', method2Error);\n            }\n          }\n\n          // Method 3: Update entire services object\n          if (updateResult !== 1) {\n            console.log('[forgotPassword] Method 3: Updating entire services object...');\n            try {\n              var _currentUser$services, _currentUser$services2;\n              updateResult = await Meteor.users.updateAsync(targetUser._id, {\n                $set: {\n                  services: {\n                    password: {\n                      bcrypt: hashedPassword\n                    },\n                    resume: ((_currentUser$services = currentUser.services) === null || _currentUser$services === void 0 ? void 0 : _currentUser$services.resume) || {\n                      loginTokens: []\n                    },\n                    email: ((_currentUser$services2 = currentUser.services) === null || _currentUser$services2 === void 0 ? void 0 : _currentUser$services2.email) || {}\n                  }\n                }\n              });\n              console.log('[forgotPassword] Method 3 result:', updateResult);\n              if (updateResult === 1) {\n                successMethod = 'Method 3: entire services object';\n              }\n            } catch (method3Error) {\n              console.error('[forgotPassword] Method 3 error:', method3Error);\n            }\n          }\n\n          // Method 4: Test basic update capability\n          if (updateResult !== 1) {\n            console.log('[forgotPassword] Method 4: Testing basic update capability...');\n            try {\n              const testResult = await Meteor.users.updateAsync(targetUser._id, {\n                $set: {\n                  'profile.passwordResetTest': new Date()\n                }\n              });\n              console.log('[forgotPassword] Basic update test result:', testResult);\n              if (testResult === 1) {\n                console.log('[forgotPassword] Basic updates work, trying password with $unset first...');\n                // Try unsetting and then setting\n                await Meteor.users.updateAsync(targetUser._id, {\n                  $unset: {\n                    'services.password': ''\n                  }\n                });\n                updateResult = await Meteor.users.updateAsync(targetUser._id, {\n                  $set: {\n                    'services.password': {\n                      bcrypt: hashedPassword\n                    }\n                  }\n                });\n                console.log('[forgotPassword] Unset/Set method result:', updateResult);\n                if (updateResult === 1) {\n                  successMethod = 'Method 4: unset then set';\n                }\n              }\n            } catch (method4Error) {\n              console.error('[forgotPassword] Method 4 error:', method4Error);\n            }\n          }\n          if (updateResult === 1) {\n            var _updatedUser$services, _updatedUser$services2;\n            console.log(\"[forgotPassword] Password update successful using: \".concat(successMethod));\n\n            // Verify the update worked\n            const updatedUser = await Meteor.users.findOneAsync(targetUser._id);\n            console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));\n\n            // Test password verification\n            if ((_updatedUser$services = updatedUser.services) !== null && _updatedUser$services !== void 0 && (_updatedUser$services2 = _updatedUser$services.password) !== null && _updatedUser$services2 !== void 0 && _updatedUser$services2.bcrypt) {\n              const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);\n              console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');\n            }\n            return {\n              success: true,\n              message: 'Password updated successfully'\n            };\n          } else {\n            console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);\n\n            // Log user permissions and collection info\n            console.log('[forgotPassword] User ID:', targetUser._id);\n            console.log('[forgotPassword] User ID type:', typeof targetUser._id);\n            console.log('[forgotPassword] Current user exists:', !!currentUser);\n            console.log('[forgotPassword] User roles:', currentUser.roles);\n            throw new Meteor.Error('password-update-failed', 'Failed to update password in database');\n          }\n        } catch (error) {\n          console.error('[forgotPassword] Error during password update:', error);\n          throw new Meteor.Error('password-update-failed', \"Failed to update password: \".concat(error.message));\n        }\n      },\n      async 'users.debugUser'(_ref3) {\n        let {\n          email\n        } = _ref3;\n        try {\n          var _fullUser$services, _fullUser$services2, _fullUser$services2$p;\n          check(email, String);\n          console.log('[debugUser] Debugging user:', email);\n\n          // Find user using async method\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            console.log('[debugUser] User not found');\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[debugUser] User found:', user._id);\n\n          // Get full user document using async method\n          const fullUser = await Meteor.users.findOneAsync(user._id);\n          console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));\n          if (!fullUser) {\n            console.log('[debugUser] Full user document not found');\n            return {\n              success: false,\n              error: 'Full user document not found'\n            };\n          }\n\n          // Test basic update using async method\n          let testUpdateResult = null;\n          try {\n            testUpdateResult = await Meteor.users.updateAsync(user._id, {\n              $set: {\n                'profile.debugTest': new Date()\n              }\n            });\n            console.log('[debugUser] Test update result:', testUpdateResult);\n          } catch (updateError) {\n            console.error('[debugUser] Test update error:', updateError);\n          }\n\n          // Try using Accounts.setPassword if available\n          let hasSetPassword = false;\n          try {\n            hasSetPassword = typeof Accounts.setPassword === 'function';\n            console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);\n          } catch (setPasswordError) {\n            console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);\n          }\n          const result = {\n            success: true,\n            userId: user._id,\n            userIdType: typeof user._id,\n            hasServices: !!fullUser.services,\n            hasPassword: !!((_fullUser$services = fullUser.services) !== null && _fullUser$services !== void 0 && _fullUser$services.password),\n            hasBcrypt: !!((_fullUser$services2 = fullUser.services) !== null && _fullUser$services2 !== void 0 && (_fullUser$services2$p = _fullUser$services2.password) !== null && _fullUser$services2$p !== void 0 && _fullUser$services2$p.bcrypt),\n            roles: fullUser.roles || [],\n            profile: fullUser.profile || {},\n            testUpdateResult: testUpdateResult,\n            hasSetPassword: hasSetPassword,\n            servicesStructure: fullUser.services || {}\n          };\n          console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));\n          return result;\n        } catch (error) {\n          console.error('[debugUser] Error in debug method:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.testLogin'(_ref4) {\n        let {\n          email,\n          password\n        } = _ref4;\n        check(email, String);\n        check(password, String);\n        console.log('[testLogin] Testing login for email:', email);\n\n        // Find user using async method\n        const user = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!user) {\n          console.log('[testLogin] User not found');\n          return {\n            success: false,\n            error: 'User not found'\n          };\n        }\n        console.log('[testLogin] User found:', user._id);\n        console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));\n\n        // Test password verification\n        try {\n          // Check if password service exists\n          if (!user.services || !user.services.password || !user.services.password.bcrypt) {\n            console.log('[testLogin] No password hash found in user services');\n            return {\n              success: false,\n              error: 'No password hash found',\n              userId: user._id,\n              services: user.services\n            };\n          }\n          const bcrypt = require('bcrypt');\n          const storedHash = user.services.password.bcrypt;\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n          console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n          console.log('[testLogin] Password length:', password.length);\n          return {\n            success: passwordMatch,\n            userId: user._id,\n            hashPreview: storedHash.substring(0, 20) + '...',\n            passwordLength: password.length\n          };\n        } catch (error) {\n          console.error('[testLogin] Error during password test:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.comparePasswordFormats'(_ref5) {\n        var _user$services, _user$services$passwo;\n        let {\n          email\n        } = _ref5;\n        check(email, String);\n        console.log('[comparePasswordFormats] Checking password format for:', email);\n\n        // Find user\n        const user = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!user) {\n          return {\n            success: false,\n            error: 'User not found'\n          };\n        }\n        console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));\n\n        // Check if user has password\n        if (!((_user$services = user.services) !== null && _user$services !== void 0 && (_user$services$passwo = _user$services.password) !== null && _user$services$passwo !== void 0 && _user$services$passwo.bcrypt)) {\n          return {\n            success: false,\n            error: 'No password found'\n          };\n        }\n        const storedHash = user.services.password.bcrypt;\n        console.log('[comparePasswordFormats] Stored hash:', storedHash);\n        console.log('[comparePasswordFormats] Hash length:', storedHash.length);\n        console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));\n\n        // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)\n        const isBcrypt = /^\\$2[aby]\\$/.test(storedHash);\n        console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);\n        return {\n          success: true,\n          userId: user._id,\n          hashLength: storedHash.length,\n          hashPreview: storedHash.substring(0, 20) + '...',\n          isBcryptFormat: isBcrypt,\n          fullServices: user.services\n        };\n      },\n      async 'users.testActualLogin'(_ref6) {\n        let {\n          email,\n          password\n        } = _ref6;\n        check(email, String);\n        check(password, String);\n        console.log('[testActualLogin] Testing actual login for:', email);\n        try {\n          var _user$services2, _user$services2$passw, _user$emails3, _user$emails3$;\n          // Try to simulate what Meteor.loginWithPassword does\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            console.log('[testActualLogin] User not found');\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[testActualLogin] User found:', user._id);\n          console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));\n\n          // Check if password service exists\n          if (!((_user$services2 = user.services) !== null && _user$services2 !== void 0 && (_user$services2$passw = _user$services2.password) !== null && _user$services2$passw !== void 0 && _user$services2$passw.bcrypt)) {\n            console.log('[testActualLogin] No password hash found');\n            return {\n              success: false,\n              error: 'No password hash found'\n            };\n          }\n\n          // Test bcrypt verification\n          const bcrypt = require('bcrypt');\n          const storedHash = user.services.password.bcrypt;\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n          console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n\n          // Check if user has any login restrictions\n          const isVerified = ((_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.verified) || false;\n          console.log('[testActualLogin] Email verified:', isVerified);\n\n          // Check user roles\n          console.log('[testActualLogin] User roles:', user.roles);\n\n          // Try to create a login token manually to test if that works\n          let loginTokenTest = null;\n          try {\n            // This is what Meteor does internally for login\n            const stampedToken = Accounts._generateStampedLoginToken();\n            console.log('[testActualLogin] Generated login token:', !!stampedToken);\n            loginTokenTest = 'Token generation successful';\n          } catch (tokenError) {\n            console.error('[testActualLogin] Token generation error:', tokenError);\n            loginTokenTest = tokenError.message;\n          }\n          return {\n            success: passwordMatch,\n            userId: user._id,\n            passwordVerification: passwordMatch,\n            emailVerified: isVerified,\n            userRoles: user.roles,\n            hashPreview: storedHash.substring(0, 20) + '...',\n            loginTokenTest: loginTokenTest,\n            fullUserStructure: {\n              _id: user._id,\n              emails: user.emails,\n              services: user.services,\n              roles: user.roles,\n              profile: user.profile\n            }\n          };\n        } catch (error) {\n          console.error('[testActualLogin] Error:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.simulateLogin'(_ref7) {\n        let {\n          email,\n          password\n        } = _ref7;\n        check(email, String);\n        check(password, String);\n        console.log('[simulateLogin] Simulating login for:', email);\n        try {\n          var _user$services3, _user$services3$passw, _user$emails4, _user$emails4$;\n          // Find user\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[simulateLogin] User found:', user._id);\n\n          // Check password\n          const bcrypt = require('bcrypt');\n          const storedHash = (_user$services3 = user.services) === null || _user$services3 === void 0 ? void 0 : (_user$services3$passw = _user$services3.password) === null || _user$services3$passw === void 0 ? void 0 : _user$services3$passw.bcrypt;\n          if (!storedHash) {\n            return {\n              success: false,\n              error: 'No password hash found'\n            };\n          }\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[simulateLogin] Password match:', passwordMatch);\n          if (!passwordMatch) {\n            return {\n              success: false,\n              error: 'Invalid password'\n            };\n          }\n\n          // Check if there are any login restrictions\n          const restrictions = [];\n\n          // Check email verification requirement\n          const emailVerified = ((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.verified) || false;\n          if (!emailVerified) {\n            restrictions.push('Email not verified');\n          }\n\n          // Check if user is active (no disabled flag)\n          if (user.disabled) {\n            restrictions.push('User account disabled');\n          }\n\n          // Check roles\n          if (!user.roles || user.roles.length === 0) {\n            restrictions.push('No roles assigned');\n          }\n          console.log('[simulateLogin] Login restrictions:', restrictions);\n\n          // Try to manually create what loginWithPassword would do\n          let loginSimulation = 'Not attempted';\n          try {\n            // Check if we can generate a login token\n            const stampedToken = Accounts._generateStampedLoginToken();\n            if (stampedToken) {\n              loginSimulation = 'Login token generation successful';\n            }\n          } catch (tokenError) {\n            loginSimulation = \"Token error: \".concat(tokenError.message);\n          }\n          return {\n            success: passwordMatch && restrictions.length === 0,\n            userId: user._id,\n            passwordMatch: passwordMatch,\n            emailVerified: emailVerified,\n            restrictions: restrictions,\n            loginSimulation: loginSimulation,\n            userStructure: {\n              _id: user._id,\n              emails: user.emails,\n              roles: user.roles,\n              profile: user.profile,\n              disabled: user.disabled || false\n            }\n          };\n        } catch (error) {\n          console.error('[simulateLogin] Error:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.checkAndFixAdminRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _user$emails5, _user$emails5$;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          console.log('[checkAndFixAdminRole] Checking user:', {\n            id: user === null || user === void 0 ? void 0 : user._id,\n            email: user === null || user === void 0 ? void 0 : (_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address,\n            roles: user === null || user === void 0 ? void 0 : user.roles\n          });\n\n          // If user has no roles array, initialize it\n          if (!user.roles) {\n            await Meteor.users.updateAsync(this.userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return 'Roles initialized';\n          }\n\n          // If user has no roles or doesn't have admin role\n          if (!user.roles.includes('admin')) {\n            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n\n            // Check if this is the first user (they should be admin)\n            const totalUsers = await Meteor.users.find().countAsync();\n            if (totalUsers === 1) {\n              console.log('[checkAndFixAdminRole] First user, setting as admin');\n              await Meteor.users.updateAsync(this.userId, {\n                $set: {\n                  roles: ['admin']\n                }\n              });\n              return 'Admin role added';\n            }\n            return 'User is not admin';\n          }\n          return 'User is already admin';\n        } catch (error) {\n          console.error('[checkAndFixAdminRole] Error:', error);\n          throw new Meteor.Error('check-role-failed', error.message);\n        }\n      },\n      async 'users.diagnoseRoles'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _currentUser$roles;\n          const currentUser = await Meteor.users.findOneAsync(this.userId);\n          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {\n            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n          }\n          const allUsers = await Meteor.users.find().fetchAsync();\n          const usersWithIssues = [];\n          const fixes = [];\n          for (const user of allUsers) {\n            var _user$profile3, _user$roles2;\n            const issues = [];\n\n            // Check if roles array exists\n            if (!user.roles || !Array.isArray(user.roles)) {\n              var _user$profile2, _user$emails6, _user$emails6$;\n              issues.push('No roles array');\n              // Fix: Initialize roles based on profile\n              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [role]\n                }\n              });\n              fixes.push(\"Initialized roles for \".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));\n            }\n\n            // Check if role matches profile\n            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {\n              var _user$emails7, _user$emails7$;\n              issues.push('Role mismatch with profile');\n              // Fix: Update roles to match profile\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [user.profile.role]\n                }\n              });\n              fixes.push(\"Fixed role mismatch for \".concat((_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address));\n            }\n            if (issues.length > 0) {\n              var _user$emails8, _user$emails8$;\n              usersWithIssues.push({\n                email: (_user$emails8 = user.emails) === null || _user$emails8 === void 0 ? void 0 : (_user$emails8$ = _user$emails8[0]) === null || _user$emails8$ === void 0 ? void 0 : _user$emails8$.address,\n                issues\n              });\n            }\n          }\n          return {\n            usersWithIssues,\n            fixes,\n            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n          };\n        } catch (error) {\n          throw new Meteor.Error('diagnose-failed', error.message);\n        }\n      },\n      'users.createTestTeamMember'() {\n        // Only allow in development\n        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n          try {\n            const testMember = {\n              email: '<EMAIL>',\n              password: 'TestPass123!',\n              firstName: 'Test',\n              lastName: 'Member'\n            };\n            const userId = Accounts.createUser({\n              email: testMember.email,\n              password: testMember.password,\n              profile: {\n                firstName: testMember.firstName,\n                lastName: testMember.lastName,\n                role: 'team-member',\n                fullName: \"\".concat(testMember.firstName, \" \").concat(testMember.lastName)\n              }\n            });\n\n            // Set the role explicitly\n            Meteor.users.update(userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return {\n              success: true,\n              userId,\n              message: 'Test team member created successfully'\n            };\n          } catch (error) {\n            console.error('[createTestTeamMember] Error:', error);\n            throw new Meteor.Error('create-test-member-failed', error.message);\n          }\n        } else {\n          throw new Meteor.Error('not-development', 'This method is only available in development');\n        }\n      }\n    });\n    __reify_async_result__();\n  } catch (_reifyError) {\n    return __reify_async_result__(_reifyError);\n  }\n  __reify_async_result__()\n}, {\n  self: this,\n  async: false\n});\n//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n}}},{\n  \"extensions\": [\n    \".js\",\n    \".json\",\n    \".ts\",\n    \".mjs\",\n    \".tsx\",\n    \".jsx\"\n  ]\n});\n\n\n/* Exports */\nreturn {\n  require: require,\n  eagerModulePaths: [\n    \"/server/main.js\"\n  ]\n}});\n", "servePath": "/app.js", "sourceMap": {"version": 3, "sources": ["imports/api/links.js", "imports/api/tasks.js", "server/main.js"], "names": ["module", "export", "LinksCollection", "Mongo", "link", "v", "__reifyWaitForDeps__", "Collection", "__reify_async_result__", "_reifyError", "self", "async", "_objectSpread", "default", "Tasks", "taskCategories", "taskLabels", "Meteor", "check", "Match", "name", "color", "isServer", "publish", "_user$roles", "userId", "ready", "user", "users", "findOneAsync", "isAdmin", "roles", "includes", "find", "sort", "createdAt", "fields", "title", "description", "startDate", "dueDate", "priority", "status", "assignedTo", "checklist", "category", "labels", "progress", "attachments", "links", "created<PERSON>y", "updatedAt", "updatedBy", "$or", "console", "log", "tasks", "fetch", "length", "userIds", "Set", "for<PERSON>ach", "task", "attachment", "uploadedBy", "add", "String", "added<PERSON>y", "userIdArray", "Array", "from", "_id", "$in", "emails", "map", "u", "_u$profile", "_u$profile2", "concat", "profile", "firstName", "lastName", "trim", "hasProfile", "taskId", "_user$roles2", "findOne", "cursor", "observe", "added", "doc", "changed", "removed", "methods", "tasks.insert", "Date", "Number", "Error", "processedChecklist", "item", "text", "completed", "taskToInsert", "result", "insertAsync", "error", "message", "tasks.update", "_user$roles3", "Optional", "existingTask", "completedItems", "filter", "totalItems", "Math", "round", "taskToUpdate", "updateAsync", "$set", "tasks.delete", "removeAsync", "tasks.updateProgress", "update", "tasks.toggleChecklistItem", "itemIndex", "_user$roles4", "checklistLength", "updatedChecklist", "updateResult", "updatedTask", "tasks.addAttachment", "fileData", "data", "uploaderId", "$push", "type", "uploadedAt", "tasks.addLink", "URL", "e", "url", "addedAt", "tasks.removeAttachment", "attachmentIndex", "currentUserId", "uploadedById", "updatedAttachments", "splice", "tasks.removeLink", "linkIndex", "addedById", "updatedLinks", "tasks.findOne", "Accounts", "Email", "Roles", "bcrypt", "insertLink", "_ref", "ADMIN_TOKEN", "startup", "_Meteor$settings$priv", "createIndex", "background", "warn", "allUsers", "fetchAsync", "_user$emails", "_user$emails$", "id", "email", "address", "updates", "isArray", "Object", "keys", "_user$emails2", "_user$emails2$", "teamMembersCount", "countAsync", "testMembers", "password", "member", "createUserAsync", "role", "fullName", "emailSettings", "settings", "private", "username", "process", "env", "MAIL_URL", "encodeURIComponent", "server", "port", "send", "to", "subject", "config", "sendVerificationEmail", "forbidClientAccountCreation", "validateLoginAttempt", "attempt", "_attempt$error", "_attempt$user", "allowed", "methodName", "emailTemplates", "siteName", "verifyEmail", "emailAddress", "html", "onCreateUser", "options", "_customizedUser$email", "_customizedUser$email2", "customizedUser", "teamMembers", "users.create", "_ref2", "adminToken", "passwordRegex", "uppercase", "number", "special", "passwordErrors", "test", "push", "join", "createUser", "users.getRole", "_user$profile", "users.resendVerificationEmail", "findUserByEmail", "userEmail", "verified", "users.forgotPassword", "JSON", "stringify", "newPassword", "targetUser", "$regex", "RegExp", "currentUser", "require", "saltRounds", "hashedPassword", "hashSync", "substring", "successMethod", "method1Error", "method2Error", "_currentUser$services", "_currentUser$services2", "services", "resume", "loginTokens", "method3Error", "testResult", "$unset", "method4Error", "_updatedUser$services", "_updatedUser$services2", "updatedUser", "testVerification", "compareSync", "success", "users.debugUser", "_ref3", "_fullUser$services", "_fullUser$services2", "_fullUser$services2$p", "fullUser", "testUpdateResult", "updateError", "hasSetPassword", "setPassword", "setPasswordError", "userIdType", "hasServices", "hasPassword", "hasBcrypt", "servicesStructure", "users.testLogin", "_ref4", "storedHash", "passwordMatch", "hashPreview", "<PERSON><PERSON><PERSON><PERSON>", "users.comparePasswordFormats", "_ref5", "_user$services", "_user$services$passwo", "isBcrypt", "hash<PERSON><PERSON><PERSON>", "isBcryptFormat", "fullServices", "users.testActualLogin", "_ref6", "_user$services2", "_user$services2$passw", "_user$emails3", "_user$emails3$", "isVerified", "loginTokenTest", "stampedToken", "_generateStampedLoginToken", "tokenError", "passwordVerification", "emailVerified", "userRoles", "fullUserStructure", "users.simulateLogin", "_ref7", "_user$services3", "_user$services3$passw", "_user$emails4", "_user$emails4$", "restrictions", "disabled", "loginSimulation", "userStructure", "users.checkAndFixAdminRole", "_user$emails5", "_user$emails5$", "totalUsers", "users.diagnoseRoles", "_currentUser$roles", "usersWithIssues", "fixes", "_user$profile3", "issues", "_user$profile2", "_user$emails6", "_user$emails6$", "_user$emails7", "_user$emails7$", "_user$emails8", "_user$emails8$", "users.createTestTeamMember", "NODE_ENV", "testMember"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,eAAe,EAACA,CAAA,KAAIA;IAAe,CAAC,CAAC;IAAC,IAAIC,KAAK;IAACH,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACD,KAAKA,CAACE,CAAC,EAAC;QAACF,KAAK,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEtK,MAAMJ,eAAe,GAAG,IAAIC,KAAK,CAACI,UAAU,CAAC,OAAO,CAAC;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICF7D,IAAIC,aAAa;IAACZ,MAAM,CAACI,IAAI,CAAC,sCAAsC,EAAC;MAACS,OAAOA,CAACR,CAAC,EAAC;QAACO,aAAa,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGL,MAAM,CAACC,MAAM,CAAC;MAACa,KAAK,EAACA,CAAA,KAAIA,KAAK;MAACC,cAAc,EAACA,CAAA,KAAIA,cAAc;MAACC,UAAU,EAACA,CAAA,KAAIA;IAAU,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACjB,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACa,MAAMA,CAACZ,CAAC,EAAC;QAACY,MAAM,GAACZ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIF,KAAK;IAACH,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACD,KAAKA,CAACE,CAAC,EAAC;QAACF,KAAK,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIa,KAAK;IAAClB,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACc,KAAKA,CAACb,CAAC,EAAC;QAACa,KAAK,GAACb,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIc,KAAK;IAACnB,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACe,KAAKA,CAACd,CAAC,EAAC;QAACc,KAAK,GAACd,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAKtY,MAAMQ,KAAK,GAAG,IAAIX,KAAK,CAACI,UAAU,CAAC,OAAO,CAAC;IAE3C,MAAMQ,cAAc,GAAG,CAC5B,aAAa,EACb,QAAQ,EACR,WAAW,EACX,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,OAAO,CACR;IAEM,MAAMC,UAAU,GAAG,CACxB;MAAEI,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC,EACjC;MAAED,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACrC;MAAED,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAU,CAAC,EACzC;MAAED,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAU,CAAC,EAC3C;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAU,CAAC,EACpC;MAAED,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,CACtC;IAED,IAAIJ,MAAM,CAACK,QAAQ,EAAE;MACnB;MACAL,MAAM,CAACM,OAAO,CAAC,OAAO,EAAE,kBAAiB;QAAA,IAAAC,WAAA;QACvC,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;UAChB,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC;QACrB;;QAEA;QACA,MAAMC,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;QACzD,MAAMK,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAAH,WAAA,GAAJG,IAAI,CAAEI,KAAK,cAAAP,WAAA,uBAAXA,WAAA,CAAaQ,QAAQ,CAAC,OAAO,CAAC;;QAE9C;QACA,IAAIF,OAAO,EAAE;UACX,OAAOhB,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC,EAAE;YACpBC,IAAI,EAAE;cAAEC,SAAS,EAAE,CAAC;YAAE,CAAC;YACvBC,MAAM,EAAE;cACNC,KAAK,EAAE,CAAC;cACRC,WAAW,EAAE,CAAC;cACdC,SAAS,EAAE,CAAC;cACZC,OAAO,EAAE,CAAC;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,CAAC;cACbC,SAAS,EAAE,CAAC;cACZC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cACTC,QAAQ,EAAE,CAAC;cACXC,WAAW,EAAE,CAAC;cACdC,KAAK,EAAE,CAAC;cACRd,SAAS,EAAE,CAAC;cACZe,SAAS,EAAE,CAAC;cACZC,SAAS,EAAE,CAAC;cACZC,SAAS,EAAE;YACb;UACF,CAAC,CAAC;QACJ;;QAEA;QACA,OAAOtC,KAAK,CAACmB,IAAI,CAAC;UAChBoB,GAAG,EAAE,CACH;YAAEV,UAAU,EAAE,IAAI,CAAClB;UAAO,CAAC,EAC3B;YAAEyB,SAAS,EAAE,IAAI,CAACzB;UAAO,CAAC;QAE9B,CAAC,EAAE;UACDS,IAAI,EAAE;YAAEC,SAAS,EAAE,CAAC;UAAE,CAAC;UACvBC,MAAM,EAAE;YACNC,KAAK,EAAE,CAAC;YACRC,WAAW,EAAE,CAAC;YACdC,SAAS,EAAE,CAAC;YACZC,OAAO,EAAE,CAAC;YACVC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,CAAC;YACbC,SAAS,EAAE,CAAC;YACZC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAE,CAAC;YACdC,KAAK,EAAE,CAAC;YACRd,SAAS,EAAE,CAAC;YACZe,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAnC,MAAM,CAACM,OAAO,CAAC,WAAW,EAAE,YAAW;QACrC+B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC9B,MAAM,EAAE;UAChB6B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzC,OAAO,IAAI,CAAC7B,KAAK,CAAC,CAAC;QACrB;;QAEA;QACA,MAAM8B,KAAK,GAAG1C,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC;QACpCH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,KAAK,CAACE,MAAM,CAAC;;QAEzC;QACA,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;QACzBJ,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;UACpB;UACA,IAAIA,IAAI,CAACd,WAAW,EAAE;YACpBc,IAAI,CAACd,WAAW,CAACa,OAAO,CAACE,UAAU,IAAI;cACrC,IAAIA,UAAU,CAACC,UAAU,EAAE;gBACzBL,OAAO,CAACM,GAAG,CAACC,MAAM,CAACH,UAAU,CAACC,UAAU,CAAC,CAAC;cAC5C;YACF,CAAC,CAAC;UACJ;UACA;UACA,IAAIF,IAAI,CAACb,KAAK,EAAE;YACda,IAAI,CAACb,KAAK,CAACY,OAAO,CAACzD,IAAI,IAAI;cACzB,IAAIA,IAAI,CAAC+D,OAAO,EAAE;gBAChBR,OAAO,CAACM,GAAG,CAACC,MAAM,CAAC9D,IAAI,CAAC+D,OAAO,CAAC,CAAC;cACnC;YACF,CAAC,CAAC;UACJ;UACA;UACA,IAAIL,IAAI,CAACnB,UAAU,EAAE;YACnBmB,IAAI,CAACnB,UAAU,CAACkB,OAAO,CAACpC,MAAM,IAAI;cAChCkC,OAAO,CAACM,GAAG,CAACC,MAAM,CAACzC,MAAM,CAAC,CAAC;YAC7B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QAEF,MAAM2C,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACX,OAAO,CAAC;QACvCL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,WAAW,CAAC;;QAEzD;QACA,MAAMxC,KAAK,GAAGX,MAAM,CAACW,KAAK,CAACK,IAAI,CAC7B;UAAEsC,GAAG,EAAE;YAAEC,GAAG,EAAEJ;UAAY;QAAE,CAAC,EAC7B;UACEhC,MAAM,EAAE;YACNmC,GAAG,EAAE,CAAC;YACNE,MAAM,EAAE,CAAC;YACT1C,KAAK,EAAE,CAAC;YACR,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;YACrBI,SAAS,EAAE;UACb;QACF,CACF,CAAC,CAACsB,KAAK,CAAC,CAAC;QAETH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE3B,KAAK,CAAC8C,GAAG,CAACC,CAAC;UAAA,IAAAC,UAAA,EAAAC,WAAA;UAAA,OAAK;YAC1CN,GAAG,EAAEI,CAAC,CAACJ,GAAG;YACVnD,IAAI,EAAE,GAAA0D,MAAA,CAAG,EAAAF,UAAA,GAAAD,CAAC,CAACI,OAAO,cAAAH,UAAA,uBAATA,UAAA,CAAWI,SAAS,KAAI,EAAE,OAAAF,MAAA,CAAI,EAAAD,WAAA,GAAAF,CAAC,CAACI,OAAO,cAAAF,WAAA,uBAATA,WAAA,CAAWI,QAAQ,KAAI,EAAE,EAAGC,IAAI,CAAC,CAAC;YACzEC,UAAU,EAAE,CAAC,CAACR,CAAC,CAACI;UAClB,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA,OAAO9D,MAAM,CAACW,KAAK,CAACK,IAAI,CACtB;UAAEsC,GAAG,EAAE;YAAEC,GAAG,EAAEJ;UAAY;QAAE,CAAC,EAC7B;UACAhC,MAAM,EAAE;YACNmC,GAAG,EAAE,CAAC;YACJE,MAAM,EAAE,CAAC;YACT1C,KAAK,EAAE,CAAC;YACV,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,CAAC;YACnB,cAAc,EAAE,CAAC;YACjB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;YACrBI,SAAS,EAAE;UACb;QACF,CACF,CAAC;MACH,CAAC,CAAC;;MAEF;MACAlB,MAAM,CAACM,OAAO,CAAC,MAAM,EAAE,UAAS6D,MAAM,EAAE;QAAA,IAAAC,YAAA;QACtCnE,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAACzC,MAAM,EAAE;UAChB,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC;QACrB;QAEA,MAAMC,IAAI,GAAGV,MAAM,CAACW,KAAK,CAAC0D,OAAO,CAAC,IAAI,CAAC7D,MAAM,CAAC;QAC9C,MAAMK,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAA0D,YAAA,GAAJ1D,IAAI,CAAEI,KAAK,cAAAsD,YAAA,uBAAXA,YAAA,CAAarD,QAAQ,CAAC,OAAO,CAAC;;QAE9C;QACA,MAAMuD,MAAM,GAAGzE,KAAK,CAACmB,IAAI,CACvB;UAAEsC,GAAG,EAAEa;QAAO,CAAC,EACf;UACEhD,MAAM,EAAE;YACNC,KAAK,EAAE,CAAC;YACRC,WAAW,EAAE,CAAC;YACdC,SAAS,EAAE,CAAC;YACZC,OAAO,EAAE,CAAC;YACVC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,CAAC;YACbC,SAAS,EAAE,CAAC;YACZC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAE,CAAC;YACdC,KAAK,EAAE,CAAC;YACRd,SAAS,EAAE,CAAC;YACZe,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE,CAAC;YACZC,SAAS,EAAE;UACb;QACF,CACF,CAAC;;QAED;QACAE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE6B,MAAM,CAAC;QACvCG,MAAM,CAACC,OAAO,CAAC;UACbC,KAAK,EAAGC,GAAG,IAAKpC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEmC,GAAG,CAACnB,GAAG,CAAC;UAClEoB,OAAO,EAAGD,GAAG,IAAKpC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmC,GAAG,CAACnB,GAAG,CAAC;UACtEqB,OAAO,EAAGF,GAAG,IAAKpC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmC,GAAG,CAACnB,GAAG;QACzE,CAAC,CAAC;QAEF,OAAOgB,MAAM;MACf,CAAC,CAAC;IACJ;IAEAtE,MAAM,CAAC4E,OAAO,CAAC;MACb,MAAM,cAAcC,CAAChC,IAAI,EAAE;QACzB5C,KAAK,CAAC4C,IAAI,EAAE;UACVzB,KAAK,EAAE6B,MAAM;UACb5B,WAAW,EAAE4B,MAAM;UACnB3B,SAAS,EAAEwD,IAAI;UACfvD,OAAO,EAAEuD,IAAI;UACbtD,QAAQ,EAAEyB,MAAM;UAChBxB,MAAM,EAAEwB,MAAM;UACdvB,UAAU,EAAE0B,KAAK;UACjBzB,SAAS,EAAEyB,KAAK;UAChBxB,QAAQ,EAAEqB,MAAM;UAChBpB,MAAM,EAAEuB,KAAK;UACbtB,QAAQ,EAAEiD;QACZ,CAAC,CAAC;QAEF,IAAI,CAAC,IAAI,CAACvE,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA3C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,IAAI,CAAC,CAAC,CAAC;;QAEzC;QACA,MAAMoC,kBAAkB,GAAGpC,IAAI,CAAClB,SAAS,CAAC8B,GAAG,CAACyB,IAAI,KAAK;UACrDC,IAAI,EAAED,IAAI,CAACC,IAAI;UACfC,SAAS,EAAEF,IAAI,CAACE,SAAS,IAAI;QAC/B,CAAC,CAAC,CAAC;QAEH,MAAMC,YAAY,GAAA1F,aAAA,CAAAA,aAAA,KACbkD,IAAI;UACP3B,SAAS,EAAE,IAAI4D,IAAI,CAAC,CAAC;UACrB7C,SAAS,EAAE,IAAI,CAACzB,MAAM;UACtB0B,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;UACrB3C,SAAS,EAAE,IAAI,CAAC3B,MAAM;UACtBsB,QAAQ,EAAEe,IAAI,CAACf,QAAQ,IAAI,CAAC;UAC5BL,MAAM,EAAE,SAAS;UAAE;UACnBE,SAAS,EAAEsD,kBAAkB;UAC7BpD,MAAM,EAAEgB,IAAI,CAAChB,MAAM,IAAI,EAAE;UACzBD,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ,IAAI,EAAE;UAC7BF,UAAU,EAAEmB,IAAI,CAACnB,UAAU,IAAI;QAAE,EAClC;QAEDW,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+C,YAAY,CAAC,CAAC,CAAC;;QAE1D,IAAI;UACF,MAAMC,MAAM,GAAG,MAAMzF,KAAK,CAAC0F,WAAW,CAACF,YAAY,CAAC;UACpDhD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgD,MAAM,CAAC,CAAC,CAAC;UACnD,OAAOA,MAAM;QACf,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,sBAAsB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC/D;MACF,CAAC;MAED,MAAM,cAAcC,CAACvB,MAAM,EAAEtB,IAAI,EAAE;QACjC,IAAI;UAAA,IAAA8C,YAAA;UACFtD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YAAE6B,MAAM;YAAEtB;UAAK,CAAC,CAAC;UAEtD5C,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;UACrBhD,KAAK,CAAC4C,IAAI,EAAE;YACVzB,KAAK,EAAE6B,MAAM;YACb5B,WAAW,EAAE4B,MAAM;YACnB3B,SAAS,EAAEwD,IAAI;YACfvD,OAAO,EAAEuD,IAAI;YACbtD,QAAQ,EAAEyB,MAAM;YAChBvB,UAAU,EAAE0B,KAAK;YACjBzB,SAAS,EAAEyB,KAAK;YAChBxB,QAAQ,EAAE1B,KAAK,CAAC0F,QAAQ,CAAC3C,MAAM,CAAC;YAChCpB,MAAM,EAAE3B,KAAK,CAAC0F,QAAQ,CAACxC,KAAK,CAAC;YAC7BtB,QAAQ,EAAE5B,KAAK,CAAC0F,QAAQ,CAACb,MAAM,CAAC;YAChCtD,MAAM,EAAEvB,KAAK,CAAC0F,QAAQ,CAAC3C,MAAM,CAAC;YAC9BlB,WAAW,EAAE7B,KAAK,CAAC0F,QAAQ,CAACxC,KAAK;UACnC,CAAC,CAAC;UAEF,IAAI,CAAC,IAAI,CAAC5C,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMa,YAAY,GAAG,MAAMhG,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACrD,IAAI,CAAC0B,YAAY,EAAE;YACjB,MAAM,IAAI7F,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,MAAMtE,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD,MAAMK,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAAiF,YAAA,GAAJjF,IAAI,CAAEI,KAAK,cAAA6E,YAAA,uBAAXA,YAAA,CAAa5E,QAAQ,CAAC,OAAO,CAAC;UAE9C,IAAI,CAACF,OAAO,IAAI,CAACgF,YAAY,CAACnE,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YAC9D,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,4CAA4C,CAAC;UACxF;;UAEA;UACA,MAAMc,cAAc,GAAGjD,IAAI,CAAClB,SAAS,CAACoE,MAAM,CAACb,IAAI,IAAIA,IAAI,CAACE,SAAS,CAAC,CAAC3C,MAAM;UAC3E,MAAMuD,UAAU,GAAGnD,IAAI,CAAClB,SAAS,CAACc,MAAM;UACxC,MAAMX,QAAQ,GAAGkE,UAAU,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAEJ,cAAc,GAAGE,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAErF;UACA,IAAIvE,MAAM,GAAGoB,IAAI,CAACpB,MAAM;UACxB,IAAIK,QAAQ,KAAK,GAAG,EAAE;YACpBL,MAAM,GAAG,WAAW;UACtB,CAAC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;YACvBL,MAAM,GAAG,aAAa;UACxB,CAAC,MAAM;YACLA,MAAM,GAAG,SAAS;UACpB;UAEA,MAAM0E,YAAY,GAAAxG,aAAA,CAAAA,aAAA,KACbkD,IAAI;YACPX,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;YACrB3C,SAAS,EAAE,IAAI,CAAC3B,MAAM;YACtBsB,QAAQ;YACRL,MAAM;YACNG,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ,IAAIiE,YAAY,CAACjE,QAAQ,IAAI,EAAE;YACtDC,MAAM,EAAEgB,IAAI,CAAChB,MAAM,IAAIgE,YAAY,CAAChE,MAAM,IAAI,EAAE;YAChDE,WAAW,EAAEc,IAAI,CAACd,WAAW,IAAI8D,YAAY,CAAC9D,WAAW,IAAI;UAAE,EAChE;UAED,MAAMuD,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YAAEkC,IAAI,EAAEF;UAAa,CACvB,CAAC;UAED,IAAIb,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,uBAAuB,CAAC;UAClE;UAEA,OAAOM,MAAM;QACf,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,cAAca,CAACnC,MAAM,EAAE;QAC3BlE,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAACzC,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA,IAAI;UACF,MAAMM,MAAM,GAAG,MAAMzF,KAAK,CAAC0G,WAAW,CAACpC,MAAM,CAAC;UAE9C,IAAImB,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;UAEA,OAAOM,MAAM;QACf,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,oBAAoB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC7D;MACF,CAAC;MAED,sBAAsBe,CAACrC,MAAM,EAAErC,QAAQ,EAAE;QACvC7B,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QACrBhD,KAAK,CAAC6B,QAAQ,EAAEiD,MAAM,CAAC;QAEvB,IAAI,CAAC,IAAI,CAACvE,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA,MAAMnC,IAAI,GAAGhD,KAAK,CAACwE,OAAO,CAACF,MAAM,CAAC;QAClC,IAAI,CAACtB,IAAI,EAAE;UACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;;QAEA;QACA,IAAI,CAACnC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;UAC1C,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,qCAAqC,CAAC;QAC/D;;QAEA;QACA,IAAIvD,MAAM,GAAGoB,IAAI,CAACpB,MAAM;QACxB,IAAIK,QAAQ,KAAK,GAAG,EAAE;UACpBL,MAAM,GAAG,WAAW;QACtB,CAAC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;UACvBL,MAAM,GAAG,aAAa;QACxB;QAEA,OAAO5B,KAAK,CAAC4G,MAAM,CAACtC,MAAM,EAAE;UAC1BkC,IAAI,EAAE;YACJvE,QAAQ;YACRL,MAAM;YACNS,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;YACrB3C,SAAS,EAAE,IAAI,CAAC3B;UAClB;QACF,CAAC,CAAC;MACJ,CAAC;MAED,MAAM,2BAA2BkG,CAACvC,MAAM,EAAEwC,SAAS,EAAE;QACnD,IAAI;UAAA,IAAAC,YAAA;UACFvE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;YAAE6B,MAAM;YAAEwC,SAAS;YAAEnG,MAAM,EAAE,IAAI,CAACA;UAAO,CAAC,CAAC;UAE7F,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;YAChB6B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;YAC/B,MAAM,IAAItC,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;;UAEA;UACA,IAAI,CAACb,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;YACzC9B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6B,MAAM,CAAC;YACtC,MAAM,IAAInE,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,iBAAiB,CAAC;UAC5D;UAEA,IAAI,OAAO2B,SAAS,KAAK,QAAQ,IAAIA,SAAS,GAAG,CAAC,EAAE;YAClDtE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqE,SAAS,CAAC;YAC5C,MAAM,IAAI3G,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,8BAA8B,CAAC;UACzE;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C9B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEO,IAAI,CAAC;UAEhC,IAAI,CAACA,IAAI,EAAE;YACTR,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;YAC7B,MAAM,IAAItC,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,MAAMtE,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD6B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE5B,IAAI,CAAC;UAEhC,MAAMG,OAAO,GAAGH,IAAI,aAAJA,IAAI,wBAAAkG,YAAA,GAAJlG,IAAI,CAAEI,KAAK,cAAA8F,YAAA,uBAAXA,YAAA,CAAa7F,QAAQ,CAAC,OAAO,CAAC;UAC9CsB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEzB,OAAO,CAAC;UAEjC,IAAI,CAACA,OAAO,IAAI,CAACgC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YACtD6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;cAAE9B,MAAM,EAAE,IAAI,CAACA,MAAM;cAAEkB,UAAU,EAAEmB,IAAI,CAACnB;YAAW,CAAC,CAAC;YACzF,MAAM,IAAI1B,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,4CAA4C,CAAC;UACxF;UAEA,MAAMrD,SAAS,GAAGkB,IAAI,CAAClB,SAAS,IAAI,EAAE;UACtCU,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEX,SAAS,CAAC;UAE5C,IAAIgF,SAAS,IAAIhF,SAAS,CAACc,MAAM,EAAE;YACjCJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;cAAEqE,SAAS;cAAEE,eAAe,EAAElF,SAAS,CAACc;YAAO,CAAC,CAAC;YACpF,MAAM,IAAIzC,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,8BAA8B,CAAC;UACzE;;UAEA;UACA,MAAM8B,gBAAgB,GAAG,CAAC,GAAGnF,SAAS,CAAC;UACvCmF,gBAAgB,CAACH,SAAS,CAAC,GAAAhH,aAAA,CAAAA,aAAA,KACtBmH,gBAAgB,CAACH,SAAS,CAAC;YAC9BvB,SAAS,EAAE,CAAC0B,gBAAgB,CAACH,SAAS,CAAC,CAACvB;UAAS,EAClD;UAED/C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEwE,gBAAgB,CAAC;;UAEnD;UACA,MAAMhB,cAAc,GAAGgB,gBAAgB,CAACf,MAAM,CAACb,IAAI,IAAIA,IAAI,CAACE,SAAS,CAAC,CAAC3C,MAAM;UAC7E,MAAMuD,UAAU,GAAGc,gBAAgB,CAACrE,MAAM;UAC1C,MAAMX,QAAQ,GAAGkE,UAAU,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAEJ,cAAc,GAAGE,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAErF;UACA,IAAIvE,MAAM;UACV,IAAIK,QAAQ,KAAK,GAAG,EAAE;YACpBL,MAAM,GAAG,WAAW;UACtB,CAAC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;YACvBL,MAAM,GAAG,aAAa;UACxB,CAAC,MAAM;YACLA,MAAM,GAAG,SAAS;UACpB;UAEAY,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;YACjC6B,MAAM;YACN2C,gBAAgB;YAChBhF,QAAQ;YACRL;UACF,CAAC,CAAC;;UAEF;UACA,MAAMoE,YAAY,GAAG,MAAMhG,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACrD,IAAI,CAAC0B,YAAY,EAAE;YACjB,MAAM,IAAI7F,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;UACnE;;UAEA;UACA,MAAM+B,YAAY,GAAG,MAAMlH,KAAK,CAACuG,WAAW,CAC1C;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkC,IAAI,EAAE;cACJ1E,SAAS,EAAEmF,gBAAgB;cAC3BhF,QAAQ;cACRL,MAAM;cACNS,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAE,IAAI,CAAC3B;YAClB;UACF,CACF,CAAC;UAED6B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyE,YAAY,CAAC;UAE3C,IAAIA,YAAY,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI/G,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,uBAAuB,CAAC;UAClE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0E,WAAW,CAAC;UAE9C,OAAOD,YAAY;QACrB,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,qBAAqBwB,CAAC9C,MAAM,EAAE+C,QAAQ,EAAE;QAC5C,IAAI;UACF,IAAI,CAAC,IAAI,CAAC1G,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YAC1C,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,wDAAwD,CAAC;UACpG;UAEA,IAAI,CAACkC,QAAQ,IAAI,CAACA,QAAQ,CAAC/G,IAAI,IAAI,CAAC+G,QAAQ,CAACC,IAAI,EAAE;YACjD,MAAM,IAAInH,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,mBAAmB,CAAC;UAC9D;;UAEA;UACA,MAAMoC,UAAU,GAAGnE,MAAM,CAAC,IAAI,CAACzC,MAAM,CAAC;;UAEtC;UACA,MAAM8E,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkD,KAAK,EAAE;cACLtF,WAAW,EAAE;gBACX5B,IAAI,EAAE+G,QAAQ,CAAC/G,IAAI;gBACnBmH,IAAI,EAAEJ,QAAQ,CAACI,IAAI;gBACnBH,IAAI,EAAED,QAAQ,CAACC,IAAI;gBACnBI,UAAU,EAAE,IAAIzC,IAAI,CAAC,CAAC;gBACtB/B,UAAU,EAAEqE;cACd;YACF,CAAC;YACDf,IAAI,EAAE;cACJnE,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAEiF;YACb;UACF,CACF,CAAC;UAED,IAAI9B,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,0BAA0B,CAAC;UACrE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0E,WAAW,CAAC;UACzD,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,eAAe+B,CAACrD,MAAM,EAAEhF,IAAI,EAAE;QAClC,IAAI;UACF,IAAI,CAAC,IAAI,CAACqB,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACnB,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;YAC1C,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,kDAAkD,CAAC;UAC9F;UAEA,IAAI,CAAC7F,IAAI,EAAE;YACT,MAAM,IAAIa,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,sBAAsB,CAAC;UACjE;;UAEA;UACA,IAAI;YACF,IAAIyC,GAAG,CAACtI,IAAI,CAAC;UACf,CAAC,CAAC,OAAOuI,CAAC,EAAE;YACV,MAAM,IAAI1H,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,oBAAoB,CAAC;UAC/D;;UAEA;UACA,MAAMM,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkD,KAAK,EAAE;cACLrF,KAAK,EAAE;gBACL2F,GAAG,EAAExI,IAAI;gBACTyI,OAAO,EAAE,IAAI9C,IAAI,CAAC,CAAC;gBACnB5B,OAAO,EAAED,MAAM,CAAC,IAAI,CAACzC,MAAM;cAC7B;YACF,CAAC;YACD6F,IAAI,EAAE;cACJnE,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAEc,MAAM,CAAC,IAAI,CAACzC,MAAM;YAC/B;UACF,CACF,CAAC;UAED,IAAI8E,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,oBAAoB,CAAC;UAC/D;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE0E,WAAW,CAAC;UACnD,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1C,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,wBAAwBoC,CAAC1D,MAAM,EAAE2D,eAAe,EAAE;QACtD,IAAI;UACF,IAAI,CAAC,IAAI,CAACtH,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACd,WAAW,IAAI,CAACc,IAAI,CAACd,WAAW,CAAC+F,eAAe,CAAC,EAAE;YAC3D,MAAM,IAAI9H,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,sBAAsB,CAAC;UAC7D;UAEA,MAAMlC,UAAU,GAAGD,IAAI,CAACd,WAAW,CAAC+F,eAAe,CAAC;;UAEpD;UACA,MAAMC,aAAa,GAAG9E,MAAM,CAAC,IAAI,CAACzC,MAAM,CAAC;UACzC,MAAMwH,YAAY,GAAG/E,MAAM,CAACH,UAAU,CAACC,UAAU,CAAC;;UAElD;UACA,IAAIgF,aAAa,KAAKC,YAAY,EAAE;YAClC,MAAM,IAAIhI,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,0CAA0C,CAAC;UACtF;;UAEA;UACA,MAAMiD,kBAAkB,GAAG,CAAC,GAAGpF,IAAI,CAACd,WAAW,CAAC;UAChDkG,kBAAkB,CAACC,MAAM,CAACJ,eAAe,EAAE,CAAC,CAAC;;UAE7C;UACA,MAAMxC,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkC,IAAI,EAAE;cACJtE,WAAW,EAAEkG,kBAAkB;cAC/B/F,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAE4F;YACb;UACF,CACF,CAAC;UAED,IAAIzC,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,6BAA6B,CAAC;UACxE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE0E,WAAW,CAAC;UAC3D,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,kBAAkB0C,CAAChE,MAAM,EAAEiE,SAAS,EAAE;QAC1C,IAAI;UACF,IAAI,CAAC,IAAI,CAAC5H,MAAM,EAAE;YAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,8CAA8C,CAAC;UAC1F;UAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UAC7C,IAAI,CAACtB,IAAI,EAAE;YACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;;UAEA;UACA,IAAI,CAACnC,IAAI,CAACb,KAAK,IAAI,CAACa,IAAI,CAACb,KAAK,CAACoG,SAAS,CAAC,EAAE;YACzC,MAAM,IAAIpI,MAAM,CAACgF,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC;UACvD;UAEA,MAAM7F,IAAI,GAAG0D,IAAI,CAACb,KAAK,CAACoG,SAAS,CAAC;;UAElC;UACA,MAAML,aAAa,GAAG9E,MAAM,CAAC,IAAI,CAACzC,MAAM,CAAC;UACzC,MAAM6H,SAAS,GAAGpF,MAAM,CAAC9D,IAAI,CAAC+D,OAAO,CAAC;;UAEtC;UACA,IAAI6E,aAAa,KAAKM,SAAS,EAAE;YAC/B,MAAM,IAAIrI,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,oCAAoC,CAAC;UAChF;;UAEA;UACA,MAAMsD,YAAY,GAAG,CAAC,GAAGzF,IAAI,CAACb,KAAK,CAAC;UACpCsG,YAAY,CAACJ,MAAM,CAACE,SAAS,EAAE,CAAC,CAAC;;UAEjC;UACA,MAAM9C,MAAM,GAAG,MAAMzF,KAAK,CAACuG,WAAW,CACpC;YAAE9C,GAAG,EAAEa;UAAO,CAAC,EACf;YACEkC,IAAI,EAAE;cACJrE,KAAK,EAAEsG,YAAY;cACnBpG,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC;cACrB3C,SAAS,EAAE4F;YACb;UACF,CACF,CAAC;UAED,IAAIzC,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAItF,MAAM,CAACgF,KAAK,CAAC,eAAe,EAAE,uBAAuB,CAAC;UAClE;;UAEA;UACA,MAAMgC,WAAW,GAAG,MAAMnH,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;UACpD9B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0E,WAAW,CAAC;UACrD,OAAOA,WAAW;QACpB,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAIA,KAAK,YAAYxF,MAAM,CAACgF,KAAK,EAAE;YACjC,MAAMQ,KAAK;UACb;UACA,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAEQ,KAAK,CAACC,OAAO,IAAI,8BAA8B,CAAC;QACzF;MACF,CAAC;MAED,MAAM,eAAe8C,CAACpE,MAAM,EAAE;QAC5BlE,KAAK,CAACkE,MAAM,EAAElB,MAAM,CAAC;QAErB,IAAI,CAAC,IAAI,CAACzC,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,iBAAiB,CAAC;QAC3C;QAEA,MAAMnC,IAAI,GAAG,MAAMhD,KAAK,CAACe,YAAY,CAACuD,MAAM,CAAC;QAC7C,IAAI,CAACtB,IAAI,EAAE;UACT,MAAM,IAAI7C,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;QAC5D;QAEA,OAAOnC,IAAI;MACb;IACF,CAAC,CAAC;IAACtD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC9xBH,IAAIC,aAAa;IAACZ,MAAM,CAACI,IAAI,CAAC,sCAAsC,EAAC;MAACS,OAAOA,CAACR,CAAC,EAAC;QAACO,aAAa,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAIY,MAAM;IAACjB,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACa,MAAMA,CAACZ,CAAC,EAAC;QAACY,MAAM,GAACZ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIH,eAAe;IAACF,MAAM,CAACI,IAAI,CAAC,oBAAoB,EAAC;MAACF,eAAeA,CAACG,CAAC,EAAC;QAACH,eAAe,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIoJ,QAAQ;IAACzJ,MAAM,CAACI,IAAI,CAAC,sBAAsB,EAAC;MAACqJ,QAAQA,CAACpJ,CAAC,EAAC;QAACoJ,QAAQ,GAACpJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIqJ,KAAK;IAAC1J,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACsJ,KAAKA,CAACrJ,CAAC,EAAC;QAACqJ,KAAK,GAACrJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIS,KAAK;IAACd,MAAM,CAACI,IAAI,CAAC,oBAAoB,EAAC;MAACU,KAAKA,CAACT,CAAC,EAAC;QAACS,KAAK,GAACT,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIsJ,KAAK;IAAC3J,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;MAACuJ,KAAKA,CAACtJ,CAAC,EAAC;QAACsJ,KAAK,GAACtJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIa,KAAK;IAAClB,MAAM,CAACI,IAAI,CAAC,cAAc,EAAC;MAACc,KAAKA,CAACb,CAAC,EAAC;QAACa,KAAK,GAACb,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIuJ,MAAM;IAAC5J,MAAM,CAACI,IAAI,CAAC,QAAQ,EAAC;MAACS,OAAOA,CAACR,CAAC,EAAC;QAACuJ,MAAM,GAACvJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IASlmB,eAAeuJ,UAAUA,CAAAC,IAAA,EAAiB;MAAA,IAAhB;QAAEzH,KAAK;QAAEuG;MAAI,CAAC,GAAAkB,IAAA;MACtC,MAAM5J,eAAe,CAACsG,WAAW,CAAC;QAAEnE,KAAK;QAAEuG,GAAG;QAAEzG,SAAS,EAAE,IAAI4D,IAAI,CAAC;MAAE,CAAC,CAAC;IAC1E;IAEA,MAAMgE,WAAW,GAAG,QAAQ;IAE5B9I,MAAM,CAAC+I,OAAO,CAAC,YAAY;MAAA,IAAAC,qBAAA;MACzB;MACA,IAAI;QACF,MAAMnJ,KAAK,CAACoJ,WAAW,CAAC;UAAE/H,SAAS,EAAE;QAAE,CAAC,CAAC;QACzC,MAAMrB,KAAK,CAACoJ,WAAW,CAAC;UAAEvH,UAAU,EAAE;QAAE,CAAC,CAAC;QAC1C,MAAM7B,KAAK,CAACoJ,WAAW,CAAC;UAAEhH,SAAS,EAAE;QAAE,CAAC,CAAC;;QAEzC;QACA;QACA,MAAMjC,MAAM,CAACW,KAAK,CAACsI,WAAW,CAAC;UAAEnI,KAAK,EAAE;QAAE,CAAC,EAAE;UAAEoI,UAAU,EAAE;QAAK,CAAC,CAAC;MACpE,CAAC,CAAC,OAAO1D,KAAK,EAAE;QACdnD,OAAO,CAAC8G,IAAI,CAAC,mCAAmC,EAAE3D,KAAK,CAACC,OAAO,CAAC;MAClE;;MAEA;MACA,IAAI;QACF,MAAM2D,QAAQ,GAAG,MAAMpJ,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC,CAAC,CAACqI,UAAU,CAAC,CAAC;QACvDhH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8G,QAAQ,CAAC3F,GAAG,CAAC/C,IAAI;UAAA,IAAA4I,YAAA,EAAAC,aAAA;UAAA,OAAK;YACxDC,EAAE,EAAE9I,IAAI,CAAC4C,GAAG;YACZmG,KAAK,GAAAH,YAAA,GAAE5I,IAAI,CAAC8C,MAAM,cAAA8F,YAAA,wBAAAC,aAAA,GAAXD,YAAA,CAAc,CAAC,CAAC,cAAAC,aAAA,uBAAhBA,aAAA,CAAkBG,OAAO;YAChC5I,KAAK,EAAEJ,IAAI,CAACI,KAAK;YACjBgD,OAAO,EAAEpD,IAAI,CAACoD;UAChB,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA,KAAK,MAAMpD,IAAI,IAAI0I,QAAQ,EAAE;UAC3B,MAAMO,OAAO,GAAG,CAAC,CAAC;UAElB,IAAI,CAACjJ,IAAI,CAACI,KAAK,IAAI,CAACsC,KAAK,CAACwG,OAAO,CAAClJ,IAAI,CAACI,KAAK,CAAC,EAAE;YAC7C6I,OAAO,CAAC7I,KAAK,GAAG,CAAC,aAAa,CAAC;UACjC;UAEA,IAAI,CAACJ,IAAI,CAACQ,SAAS,EAAE;YACnByI,OAAO,CAACzI,SAAS,GAAG,IAAI4D,IAAI,CAAC,CAAC;UAChC;UAEA,IAAI+E,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAAClH,MAAM,GAAG,CAAC,EAAE;YAAA,IAAAsH,aAAA,EAAAC,cAAA;YACnC3H,OAAO,CAACC,GAAG,CAAC,2CAA2C,GAAAyH,aAAA,GAAErJ,IAAI,CAAC8C,MAAM,cAAAuG,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBN,OAAO,CAAC;YACnF,MAAM1J,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC1F,IAAI,CAAC4C,GAAG,EAAE;cACvC+C,IAAI,EAAEsD;YACR,CAAC,CAAC;UACJ;QACF;QAEA,MAAMM,gBAAgB,GAAG,MAAMjK,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC;UAAE,OAAO,EAAE;QAAc,CAAC,CAAC,CAACkJ,UAAU,CAAC,CAAC;QACzF7H,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2H,gBAAgB,CAAC;;QAE9D;QACA,IAAIA,gBAAgB,KAAK,CAAC,EAAE;UAC1B5H,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnD,IAAI;YACF;YACA,MAAM6H,WAAW,GAAG,CAClB;cACEV,KAAK,EAAE,mBAAmB;cAC1BW,QAAQ,EAAE,cAAc;cACxBrG,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,EACD;cACEyF,KAAK,EAAE,mBAAmB;cAC1BW,QAAQ,EAAE,cAAc;cACxBrG,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,CACF;YAED,KAAK,MAAMqG,MAAM,IAAIF,WAAW,EAAE;cAChC,MAAM3J,MAAM,GAAG,MAAMgI,QAAQ,CAAC8B,eAAe,CAAC;gBAC5Cb,KAAK,EAAEY,MAAM,CAACZ,KAAK;gBACnBW,QAAQ,EAAEC,MAAM,CAACD,QAAQ;gBACzBG,IAAI,EAAE,aAAa;gBACnBrJ,SAAS,EAAE,IAAI4D,IAAI,CAAC,CAAC;gBACrBhB,OAAO,EAAE;kBACPC,SAAS,EAAEsG,MAAM,CAACtG,SAAS;kBAC3BC,QAAQ,EAAEqG,MAAM,CAACrG,QAAQ;kBACzBuG,IAAI,EAAE,aAAa;kBACnBC,QAAQ,KAAA3G,MAAA,CAAKwG,MAAM,CAACtG,SAAS,OAAAF,MAAA,CAAIwG,MAAM,CAACrG,QAAQ;gBAClD;cACF,CAAC,CAAC;;cAEF;cACA,MAAMhE,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC5F,MAAM,EAAE;gBACrC6F,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAAC,aAAa;gBAAE;cACjC,CAAC,CAAC;cAEFuB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;gBACjDkH,EAAE,EAAEhJ,MAAM;gBACViJ,KAAK,EAAEY,MAAM,CAACZ,KAAK;gBACnBtJ,IAAI,KAAA0D,MAAA,CAAKwG,MAAM,CAACtG,SAAS,OAAAF,MAAA,CAAIwG,MAAM,CAACrG,QAAQ;cAC9C,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;YACdnD,OAAO,CAACmD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACrE;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdnD,OAAO,CAACmD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;;MAEA;MACA,MAAMiF,aAAa,IAAAzB,qBAAA,GAAGhJ,MAAM,CAAC0K,QAAQ,CAACC,OAAO,cAAA3B,qBAAA,uBAAvBA,qBAAA,CAAyBS,KAAK;;MAEpD;MACA,IAAIgB,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,IAAIH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEL,QAAQ,EAAE;QACtDS,OAAO,CAACC,GAAG,CAACC,QAAQ,aAAAlH,MAAA,CAAamH,kBAAkB,CAACP,aAAa,CAACG,QAAQ,CAAC,OAAA/G,MAAA,CAAImH,kBAAkB,CAACP,aAAa,CAACL,QAAQ,CAAC,OAAAvG,MAAA,CAAI4G,aAAa,CAACQ,MAAM,OAAApH,MAAA,CAAI4G,aAAa,CAACS,IAAI,CAAE;;QAEzK;QACA,IAAI;UACF7I,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CmG,KAAK,CAAC0C,IAAI,CAAC;YACTC,EAAE,EAAEX,aAAa,CAACG,QAAQ;YAC1BvH,IAAI,EAAEoH,aAAa,CAACG,QAAQ;YAC5BS,OAAO,EAAE,YAAY;YACrBlG,IAAI,EAAE;UACR,CAAC,CAAC;UACF9C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,CAAC,OAAOkD,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC,MAAM;QACLnD,OAAO,CAAC8G,IAAI,CAAC,iDAAiD,CAAC;MACjE;;MAEA;MACAX,QAAQ,CAAC8C,MAAM,CAAC;QACdC,qBAAqB,EAAE,KAAK;QAAG;QAC/BC,2BAA2B,EAAE;MAC/B,CAAC,CAAC;;MAEF;MACAhD,QAAQ,CAACiD,oBAAoB,CAAEC,OAAO,IAAK;QAAA,IAAAC,cAAA,EAAAC,aAAA;QACzCvJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;UACnDgF,IAAI,EAAEoE,OAAO,CAACpE,IAAI;UAClBuE,OAAO,EAAEH,OAAO,CAACG,OAAO;UACxBrG,KAAK,GAAAmG,cAAA,GAAED,OAAO,CAAClG,KAAK,cAAAmG,cAAA,uBAAbA,cAAA,CAAelG,OAAO;UAC7BjF,MAAM,GAAAoL,aAAA,GAAEF,OAAO,CAAChL,IAAI,cAAAkL,aAAA,uBAAZA,aAAA,CAActI,GAAG;UACzBwI,UAAU,EAAEJ,OAAO,CAACI;QACtB,CAAC,CAAC;;QAEF;QACA,IAAIJ,OAAO,CAAChL,IAAI,IAAIgL,OAAO,CAACpE,IAAI,KAAK,UAAU,EAAE;UAC/CjF,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEoJ,OAAO,CAAChL,IAAI,CAAC4C,GAAG,CAAC;UACzF,OAAO,IAAI;QACb;;QAEA;QACA,OAAO,IAAI;MACb,CAAC,CAAC;;MAEF;MACAkF,QAAQ,CAACuD,cAAc,CAACC,QAAQ,GAAG,wBAAwB;MAC3DxD,QAAQ,CAACuD,cAAc,CAAC1I,IAAI,GAAGoH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,8BAAA/G,MAAA,CACzB4G,aAAa,CAACG,QAAQ,SACjD,8CAA8C;MAEhDpC,QAAQ,CAACuD,cAAc,CAACE,WAAW,GAAG;QACpCZ,OAAOA,CAAA,EAAG;UACR,OAAO,2BAA2B;QACpC,CAAC;QACDlG,IAAIA,CAACzE,IAAI,EAAEiH,GAAG,EAAE;UACd,MAAMuE,YAAY,GAAGxL,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAAC,CAACkG,OAAO;UAC3C,OAAO,gDAAA7F,MAAA,CAC8BqI,YAAY,wCAAqC,MAAArI,MAAA,CAC/E8D,GAAG,SAAM,4EAC6D,cAC9D,qCACuB;QACxC,CAAC;QACDwE,IAAIA,CAACzL,IAAI,EAAEiH,GAAG,EAAE;UACd,MAAMuE,YAAY,GAAGxL,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAAC,CAACkG,OAAO;UAC3C,sQAAA7F,MAAA,CAKyCqI,YAAY,gHAAArI,MAAA,CAElC8D,GAAG;QAexB;MACF,CAAC;;MAED;MACA,IAAI,OAAM1I,eAAe,CAAC+B,IAAI,CAAC,CAAC,CAACkJ,UAAU,CAAC,CAAC,MAAK,CAAC,EAAE;QACnD,MAAMtB,UAAU,CAAC;UACfxH,KAAK,EAAE,iBAAiB;UACxBuG,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMiB,UAAU,CAAC;UACfxH,KAAK,EAAE,kBAAkB;UACzBuG,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMiB,UAAU,CAAC;UACfxH,KAAK,EAAE,eAAe;UACtBuG,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMiB,UAAU,CAAC;UACfxH,KAAK,EAAE,aAAa;UACpBuG,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;;MAEA;MACA;MACA3H,MAAM,CAACM,OAAO,CAAC,OAAO,EAAE,YAAY;QAClC,OAAOrB,eAAe,CAAC+B,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACAwH,QAAQ,CAAC4D,YAAY,CAAC,CAACC,OAAO,EAAE3L,IAAI,KAAK;QAAA,IAAA4L,qBAAA,EAAAC,sBAAA;QACvClK,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;UACxDmH,KAAK,EAAE4C,OAAO,CAAC5C,KAAK;UACpBc,IAAI,EAAE8B,OAAO,CAAC9B,IAAI;UAClBzG,OAAO,EAAEuI,OAAO,CAACvI,OAAO;UACxB5C,SAAS,EAAEmL,OAAO,CAACnL;QACrB,CAAC,CAAC;QAEF,MAAMsL,cAAc,GAAA7M,aAAA,KAAQe,IAAI,CAAE;;QAElC;QACA8L,cAAc,CAAC1I,OAAO,GAAGuI,OAAO,CAACvI,OAAO,IAAI,CAAC,CAAC;;QAE9C;QACA,MAAMyG,IAAI,GAAG8B,OAAO,CAAC9B,IAAI,IAAI,aAAa;QAC1CiC,cAAc,CAAC1L,KAAK,GAAG,CAACyJ,IAAI,CAAC;;QAE7B;QACAiC,cAAc,CAACtL,SAAS,GAAGmL,OAAO,CAACnL,SAAS,IAAI,IAAI4D,IAAI,CAAC,CAAC;QAE1DzC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAC1CkH,EAAE,EAAEgD,cAAc,CAAClJ,GAAG;UACtBmG,KAAK,GAAA6C,qBAAA,GAAEE,cAAc,CAAChJ,MAAM,cAAA8I,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4B7C,OAAO;UAC1C5I,KAAK,EAAE0L,cAAc,CAAC1L,KAAK;UAC3BgD,OAAO,EAAE0I,cAAc,CAAC1I,OAAO;UAC/B5C,SAAS,EAAEsL,cAAc,CAACtL;QAC5B,CAAC,CAAC;QAEF,OAAOsL,cAAc;MACvB,CAAC,CAAC;;MAEF;MACAxM,MAAM,CAACM,OAAO,CAAC,aAAa,EAAE,YAAW;QACvC+B,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC9B,MAAM,CAAC;QAErE,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;UAChB6B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD,OAAO,IAAI,CAAC7B,KAAK,CAAC,CAAC;QACrB;QAEA,IAAI;UACF;UACA,MAAMgM,WAAW,GAAGzM,MAAM,CAACW,KAAK,CAACK,IAAI,CACnC;YACEoB,GAAG,EAAE,CACH;cAAE,OAAO,EAAE;YAAc,CAAC,EAC1B;cAAE,cAAc,EAAE;YAAc,CAAC;UAErC,CAAC,EACD;YACEjB,MAAM,EAAE;cACNqC,MAAM,EAAE,CAAC;cACT1C,KAAK,EAAE,CAAC;cACR,mBAAmB,EAAE,CAAC;cACtB,kBAAkB,EAAE,CAAC;cACrB,kBAAkB,EAAE,CAAC;cACrBI,SAAS,EAAE;YACb;UACF,CACF,CAAC;UAEDmB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpD,OAAOmK,WAAW;QACpB,CAAC,CAAC,OAAOjH,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,IAAI,CAAC/E,KAAK,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;;MAEF;MACAT,MAAM,CAACM,OAAO,CAAC,UAAU,EAAE,YAAW;QACpC,IAAI,CAAC,IAAI,CAACE,MAAM,EAAE;UAChB,OAAO,IAAI,CAACC,KAAK,CAAC,CAAC;QACrB;QAEA4B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC9B,MAAM,CAAC;QAEhE,OAAOR,MAAM,CAACW,KAAK,CAACK,IAAI,CACtB;UAAEsC,GAAG,EAAE,IAAI,CAAC9C;QAAO,CAAC,EACpB;UACEW,MAAM,EAAE;YACNL,KAAK,EAAE,CAAC;YACR0C,MAAM,EAAE,CAAC;YACTM,OAAO,EAAE;UACX;QACF,CACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA9D,MAAM,CAAC4E,OAAO,CAAC;MACb,cAAc8H,CAAAC,KAAA,EAA6D;QAAA,IAA5D;UAAElD,KAAK;UAAEW,QAAQ;UAAEG,IAAI;UAAEqC,UAAU;UAAE7I,SAAS;UAAEC;QAAS,CAAC,GAAA2I,KAAA;QACvE;QACA,IAAIpC,IAAI,KAAK,OAAO,IAAIqC,UAAU,KAAK9D,WAAW,EAAE;UAClD,MAAM,IAAI9I,MAAM,CAACgF,KAAK,CAAC,qBAAqB,EAAE,8BAA8B,CAAC;QAC/E;;QAEA;QACA,MAAM6H,aAAa,GAAG;UACpBpK,MAAM,EAAE,OAAO;UACfqK,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAACpK,MAAM,CAACyK,IAAI,CAAC9C,QAAQ,CAAC,EAAE;UACxC6C,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAAC9C,QAAQ,CAAC,EAAE;UAC3C6C,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAAC9C,QAAQ,CAAC,EAAE;UACxC6C,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAAC9C,QAAQ,CAAC,EAAE;UACzC6C,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAACxK,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAIzC,MAAM,CAACgF,KAAK,CAAC,kBAAkB,EAAEiI,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF,MAAM5M,MAAM,GAAGgI,QAAQ,CAAC6E,UAAU,CAAC;YACjC5D,KAAK;YACLW,QAAQ;YACRG,IAAI;YAAE;YACNzG,OAAO,EAAE;cACPyG,IAAI;cAAE;cACNxG,SAAS;cACTC,QAAQ;cACRwG,QAAQ,KAAA3G,MAAA,CAAKE,SAAS,OAAAF,MAAA,CAAIG,QAAQ;YACpC;UACF,CAAC,CAAC;;UAEF;UACA,IAAIxD,MAAM,EAAE;YACVgI,QAAQ,CAAC+C,qBAAqB,CAAC/K,MAAM,CAAC;UACxC;UAEA,OAAOA,MAAM;QACf,CAAC,CAAC,OAAOgF,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,oBAAoB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC7D;MACF,CAAC;MAED,MAAM,eAAe6H,CAAA,EAAG;QACtB,IAAI,CAAC,IAAI,CAAC9M,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,wBAAwB,CAAC;QACpE;QAEA,IAAI;UAAA,IAAAzE,WAAA,EAAAgN,aAAA;UACF,MAAM7M,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD,IAAI,CAACE,IAAI,EAAE;YACT,MAAM,IAAIV,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;UAC5D;;UAEA;UACA,MAAMuF,IAAI,GAAG,EAAAhK,WAAA,GAAAG,IAAI,CAACI,KAAK,cAAAP,WAAA,uBAAVA,WAAA,CAAa,CAAC,CAAC,OAAAgN,aAAA,GAAI7M,IAAI,CAACoD,OAAO,cAAAyJ,aAAA,uBAAZA,aAAA,CAAchD,IAAI,KAAI,aAAa;UACnE,OAAOA,IAAI;QACb,CAAC,CAAC,OAAO/E,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,iBAAiB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,+BAA+B+H,CAAC/D,KAAK,EAAE;QACrC;QACA,MAAM/I,IAAI,GAAG8H,QAAQ,CAACiF,eAAe,CAAChE,KAAK,CAAC;QAC5C,IAAI,CAAC/I,IAAI,EAAE;UACT,MAAM,IAAIV,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;QACnF;;QAEA;QACA,MAAM0I,SAAS,GAAGhN,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAAC;QAChC,IAAIkK,SAAS,CAACC,QAAQ,EAAE;UACtB,MAAM,IAAI3N,MAAM,CAACgF,KAAK,CAAC,kBAAkB,EAAE,gCAAgC,CAAC;QAC9E;;QAEA;QACA,IAAI;UACFwD,QAAQ,CAAC+C,qBAAqB,CAAC7K,IAAI,CAAC4C,GAAG,EAAEmG,KAAK,CAAC;UAC/C,OAAO,IAAI;QACb,CAAC,CAAC,OAAOjE,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,2BAA2B,EAAEQ,KAAK,CAACC,OAAO,CAAC;QACpE;MACF,CAAC;MAED,MAAM,sBAAsBmI,CAACzG,IAAI,EAAE;QACjC9E,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEuL,IAAI,CAACC,SAAS,CAAC3G,IAAI,CAAC,CAAC;QAE9ElH,KAAK,CAACkH,IAAI,EAAE;UACVsC,KAAK,EAAExG,MAAM;UACb8K,WAAW,EAAE9K;QACf,CAAC,CAAC;QAEF,MAAM;UAAEwG,KAAK;UAAEsE;QAAY,CAAC,GAAG5G,IAAI;QACnC9E,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEmH,KAAK,CAAC;;QAEpE;QACApH,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,IAAI0L,UAAU,GAAG,MAAMhO,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC;UAC/C,gBAAgB,EAAE6I;QACpB,CAAC,CAAC;QAEF,IAAI,CAACuE,UAAU,EAAE;UACf3L,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;UAC7F0L,UAAU,GAAG,MAAMhO,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC;YAC3C,gBAAgB,EAAE;cAAEqN,MAAM,EAAE,IAAIC,MAAM,KAAArK,MAAA,CAAK4F,KAAK,QAAK,GAAG;YAAE;UAC5D,CAAC,CAAC;UAEF,IAAI,CAACuE,UAAU,EAAE;YACf,MAAM,IAAIhO,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;UACnF;UAEA3C,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACzE,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC/D;;QAEA;QACA,IAAI,CAAC0L,UAAU,IAAI,CAACA,UAAU,CAAC1K,GAAG,EAAE;UAClC,MAAM,IAAItD,MAAM,CAACgF,KAAK,CAAC,cAAc,EAAE,2BAA2B,CAAC;QACrE;QAEA3C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE0L,UAAU,CAAC1K,GAAG,CAAC;QAC9DjB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,OAAO0L,UAAU,CAAC1K,GAAG,CAAC;;QAE1E;QACA,MAAMuJ,aAAa,GAAG;UACpBpK,MAAM,EAAE,OAAO;UACfqK,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAACpK,MAAM,CAACyK,IAAI,CAACa,WAAW,CAAC,EAAE;UAC3Cd,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAACa,WAAW,CAAC,EAAE;UAC9Cd,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAACa,WAAW,CAAC,EAAE;UAC3Cd,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAACa,WAAW,CAAC,EAAE;UAC5Cd,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAACxK,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAIzC,MAAM,CAACgF,KAAK,CAAC,kBAAkB,EAAEiI,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF/K,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;UAE3D;UACAD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,MAAM6L,WAAW,GAAG,MAAMnO,MAAM,CAACW,KAAK,CAACC,YAAY,CAACoN,UAAU,CAAC1K,GAAG,CAAC;UACnEjB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEuL,IAAI,CAACC,SAAS,CAACK,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAE5F,IAAI,CAACA,WAAW,EAAE;YAChB,MAAM,IAAInO,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;UACnF;;UAEA;UACA,MAAM2D,MAAM,GAAGyF,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMC,UAAU,GAAG,EAAE;UACrB,MAAMC,cAAc,GAAG3F,MAAM,CAAC4F,QAAQ,CAACR,WAAW,EAAEM,UAAU,CAAC;UAC/DhM,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEgM,cAAc,CAAC7L,MAAM,CAAC;UACrFJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgM,cAAc,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;;UAEtF;UACA,IAAIzH,YAAY,GAAG,CAAC;UACpB,IAAI0H,aAAa,GAAG,IAAI;;UAExB;UACApM,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;UAC9E,IAAI;YACFyE,YAAY,GAAG,MAAM/G,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC4H,UAAU,CAAC1K,GAAG,EAAE;cAC5D+C,IAAI,EAAE;gBACJ,0BAA0B,EAAEiI;cAC9B;YACF,CAAC,CAAC;YACFjM,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyE,YAAY,CAAC;YAC9D,IAAIA,YAAY,KAAK,CAAC,EAAE;cACtB0H,aAAa,GAAG,oCAAoC;YACtD;UACF,CAAC,CAAC,OAAOC,YAAY,EAAE;YACrBrM,OAAO,CAACmD,KAAK,CAAC,kCAAkC,EAAEkJ,YAAY,CAAC;UACjE;;UAEA;UACA,IAAI3H,YAAY,KAAK,CAAC,EAAE;YACtB1E,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;YACrF,IAAI;cACFyE,YAAY,GAAG,MAAM/G,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC4H,UAAU,CAAC1K,GAAG,EAAE;gBAC5D+C,IAAI,EAAE;kBACJ,mBAAmB,EAAE;oBACnBsC,MAAM,EAAE2F;kBACV;gBACF;cACF,CAAC,CAAC;cACFjM,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyE,YAAY,CAAC;cAC9D,IAAIA,YAAY,KAAK,CAAC,EAAE;gBACtB0H,aAAa,GAAG,2CAA2C;cAC7D;YACF,CAAC,CAAC,OAAOE,YAAY,EAAE;cACrBtM,OAAO,CAACmD,KAAK,CAAC,kCAAkC,EAAEmJ,YAAY,CAAC;YACjE;UACF;;UAEA;UACA,IAAI5H,YAAY,KAAK,CAAC,EAAE;YACtB1E,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;YAC5E,IAAI;cAAA,IAAAsM,qBAAA,EAAAC,sBAAA;cACF9H,YAAY,GAAG,MAAM/G,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC4H,UAAU,CAAC1K,GAAG,EAAE;gBAC5D+C,IAAI,EAAE;kBACJyI,QAAQ,EAAE;oBACR1E,QAAQ,EAAE;sBACRzB,MAAM,EAAE2F;oBACV,CAAC;oBACDS,MAAM,EAAE,EAAAH,qBAAA,GAAAT,WAAW,CAACW,QAAQ,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsBG,MAAM,KAAI;sBAAEC,WAAW,EAAE;oBAAG,CAAC;oBAC3DvF,KAAK,EAAE,EAAAoF,sBAAA,GAAAV,WAAW,CAACW,QAAQ,cAAAD,sBAAA,uBAApBA,sBAAA,CAAsBpF,KAAK,KAAI,CAAC;kBACzC;gBACF;cACF,CAAC,CAAC;cACFpH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyE,YAAY,CAAC;cAC9D,IAAIA,YAAY,KAAK,CAAC,EAAE;gBACtB0H,aAAa,GAAG,kCAAkC;cACpD;YACF,CAAC,CAAC,OAAOQ,YAAY,EAAE;cACrB5M,OAAO,CAACmD,KAAK,CAAC,kCAAkC,EAAEyJ,YAAY,CAAC;YACjE;UACF;;UAEA;UACA,IAAIlI,YAAY,KAAK,CAAC,EAAE;YACtB1E,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;YAC5E,IAAI;cACF,MAAM4M,UAAU,GAAG,MAAMlP,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC4H,UAAU,CAAC1K,GAAG,EAAE;gBAChE+C,IAAI,EAAE;kBACJ,2BAA2B,EAAE,IAAIvB,IAAI,CAAC;gBACxC;cACF,CAAC,CAAC;cACFzC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE4M,UAAU,CAAC;cAErE,IAAIA,UAAU,KAAK,CAAC,EAAE;gBACpB7M,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;gBACxF;gBACA,MAAMtC,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC4H,UAAU,CAAC1K,GAAG,EAAE;kBAC7C6L,MAAM,EAAE;oBAAE,mBAAmB,EAAE;kBAAG;gBACpC,CAAC,CAAC;gBAEFpI,YAAY,GAAG,MAAM/G,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC4H,UAAU,CAAC1K,GAAG,EAAE;kBAC5D+C,IAAI,EAAE;oBACJ,mBAAmB,EAAE;sBACnBsC,MAAM,EAAE2F;oBACV;kBACF;gBACF,CAAC,CAAC;gBACFjM,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEyE,YAAY,CAAC;gBACtE,IAAIA,YAAY,KAAK,CAAC,EAAE;kBACtB0H,aAAa,GAAG,0BAA0B;gBAC5C;cACF;YACF,CAAC,CAAC,OAAOW,YAAY,EAAE;cACrB/M,OAAO,CAACmD,KAAK,CAAC,kCAAkC,EAAE4J,YAAY,CAAC;YACjE;UACF;UAEA,IAAIrI,YAAY,KAAK,CAAC,EAAE;YAAA,IAAAsI,qBAAA,EAAAC,sBAAA;YACtBjN,OAAO,CAACC,GAAG,uDAAAuB,MAAA,CAAuD4K,aAAa,CAAE,CAAC;;YAElF;YACA,MAAMc,WAAW,GAAG,MAAMvP,MAAM,CAACW,KAAK,CAACC,YAAY,CAACoN,UAAU,CAAC1K,GAAG,CAAC;YACnEjB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEuL,IAAI,CAACC,SAAS,CAACyB,WAAW,CAACT,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;YAErG;YACA,KAAAO,qBAAA,GAAIE,WAAW,CAACT,QAAQ,cAAAO,qBAAA,gBAAAC,sBAAA,GAApBD,qBAAA,CAAsBjF,QAAQ,cAAAkF,sBAAA,eAA9BA,sBAAA,CAAgC3G,MAAM,EAAE;cAC1C,MAAM6G,gBAAgB,GAAG7G,MAAM,CAAC8G,WAAW,CAAC1B,WAAW,EAAEwB,WAAW,CAACT,QAAQ,CAAC1E,QAAQ,CAACzB,MAAM,CAAC;cAC9FtG,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEkN,gBAAgB,GAAG,MAAM,GAAG,MAAM,CAAC;YACjG;YAEA,OAAO;cAAEE,OAAO,EAAE,IAAI;cAAEjK,OAAO,EAAE;YAAgC,CAAC;UACpE,CAAC,MAAM;YACLpD,OAAO,CAACmD,KAAK,CAAC,oEAAoE,EAAEuB,YAAY,CAAC;;YAEjG;YACA1E,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0L,UAAU,CAAC1K,GAAG,CAAC;YACxDjB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,OAAO0L,UAAU,CAAC1K,GAAG,CAAC;YACpEjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,CAAC,CAAC6L,WAAW,CAAC;YACnE9L,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE6L,WAAW,CAACrN,KAAK,CAAC;YAE9D,MAAM,IAAId,MAAM,CAACgF,KAAK,CAAC,wBAAwB,EAAE,uCAAuC,CAAC;UAC3F;QAEF,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;UACtE,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,wBAAwB,gCAAAnB,MAAA,CAAgC2B,KAAK,CAACC,OAAO,CAAE,CAAC;QACjG;MACF,CAAC;MAED,MAAM,iBAAiBkK,CAAAC,KAAA,EAAY;QAAA,IAAX;UAAEnG;QAAM,CAAC,GAAAmG,KAAA;QAC/B,IAAI;UAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA;UACF9P,KAAK,CAACwJ,KAAK,EAAExG,MAAM,CAAC;UAEpBZ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmH,KAAK,CAAC;;UAEjD;UACA,MAAM/I,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC;YAC3C,gBAAgB,EAAE6I;UACpB,CAAC,CAAC;UAEF,IAAI,CAAC/I,IAAI,EAAE;YACT2B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;YACzC,OAAO;cAAEoN,OAAO,EAAE,KAAK;cAAElK,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAnD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE5B,IAAI,CAAC4C,GAAG,CAAC;;UAEhD;UACA,MAAM0M,QAAQ,GAAG,MAAMhQ,MAAM,CAACW,KAAK,CAACC,YAAY,CAACF,IAAI,CAAC4C,GAAG,CAAC;UAC1DjB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEuL,IAAI,CAACC,SAAS,CAACkC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEjF,IAAI,CAACA,QAAQ,EAAE;YACb3N,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;YACvD,OAAO;cAAEoN,OAAO,EAAE,KAAK;cAAElK,KAAK,EAAE;YAA+B,CAAC;UAClE;;UAEA;UACA,IAAIyK,gBAAgB,GAAG,IAAI;UAC3B,IAAI;YACFA,gBAAgB,GAAG,MAAMjQ,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC1F,IAAI,CAAC4C,GAAG,EAAE;cAC1D+C,IAAI,EAAE;gBAAE,mBAAmB,EAAE,IAAIvB,IAAI,CAAC;cAAE;YAC1C,CAAC,CAAC;YACFzC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE2N,gBAAgB,CAAC;UAClE,CAAC,CAAC,OAAOC,WAAW,EAAE;YACpB7N,OAAO,CAACmD,KAAK,CAAC,gCAAgC,EAAE0K,WAAW,CAAC;UAC9D;;UAEA;UACA,IAAIC,cAAc,GAAG,KAAK;UAC1B,IAAI;YACFA,cAAc,GAAG,OAAO3H,QAAQ,CAAC4H,WAAW,KAAK,UAAU;YAC3D/N,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE6N,cAAc,CAAC;UAC5E,CAAC,CAAC,OAAOE,gBAAgB,EAAE;YACzBhO,OAAO,CAACmD,KAAK,CAAC,+CAA+C,EAAE6K,gBAAgB,CAAC;UAClF;UAEA,MAAM/K,MAAM,GAAG;YACboK,OAAO,EAAE,IAAI;YACblP,MAAM,EAAEE,IAAI,CAAC4C,GAAG;YAChBgN,UAAU,EAAE,OAAO5P,IAAI,CAAC4C,GAAG;YAC3BiN,WAAW,EAAE,CAAC,CAACP,QAAQ,CAAClB,QAAQ;YAChC0B,WAAW,EAAE,CAAC,GAAAX,kBAAA,GAAEG,QAAQ,CAAClB,QAAQ,cAAAe,kBAAA,eAAjBA,kBAAA,CAAmBzF,QAAQ,CAAC;YAC5CqG,SAAS,EAAE,CAAC,GAAAX,mBAAA,GAAEE,QAAQ,CAAClB,QAAQ,cAAAgB,mBAAA,gBAAAC,qBAAA,GAAjBD,mBAAA,CAAmB1F,QAAQ,cAAA2F,qBAAA,eAA3BA,qBAAA,CAA6BpH,MAAM,CAAC;YAClD7H,KAAK,EAAEkP,QAAQ,CAAClP,KAAK,IAAI,EAAE;YAC3BgD,OAAO,EAAEkM,QAAQ,CAAClM,OAAO,IAAI,CAAC,CAAC;YAC/BmM,gBAAgB,EAAEA,gBAAgB;YAClCE,cAAc,EAAEA,cAAc;YAC9BO,iBAAiB,EAAEV,QAAQ,CAAClB,QAAQ,IAAI,CAAC;UAC3C,CAAC;UAEDzM,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuL,IAAI,CAACC,SAAS,CAACxI,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UACzE,OAAOA,MAAM;QAEf,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,OAAO;YAAEkK,OAAO,EAAE,KAAK;YAAElK,KAAK,EAAEA,KAAK,CAACC;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,iBAAiBkL,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAEnH,KAAK;UAAEW;QAAS,CAAC,GAAAwG,KAAA;QACzC3Q,KAAK,CAACwJ,KAAK,EAAExG,MAAM,CAAC;QACpBhD,KAAK,CAACmK,QAAQ,EAAEnH,MAAM,CAAC;QAEvBZ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEmH,KAAK,CAAC;;QAE1D;QACA,MAAM/I,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC;UAC3C,gBAAgB,EAAE6I;QACpB,CAAC,CAAC;QAEF,IAAI,CAAC/I,IAAI,EAAE;UACT2B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzC,OAAO;YAAEoN,OAAO,EAAE,KAAK;YAAElK,KAAK,EAAE;UAAiB,CAAC;QACpD;QAEAnD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE5B,IAAI,CAAC4C,GAAG,CAAC;QAChDjB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuL,IAAI,CAACC,SAAS,CAACpN,IAAI,CAACoO,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;QAEjF;QACA,IAAI;UACF;UACA,IAAI,CAACpO,IAAI,CAACoO,QAAQ,IAAI,CAACpO,IAAI,CAACoO,QAAQ,CAAC1E,QAAQ,IAAI,CAAC1J,IAAI,CAACoO,QAAQ,CAAC1E,QAAQ,CAACzB,MAAM,EAAE;YAC/EtG,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;YAClE,OAAO;cACLoN,OAAO,EAAE,KAAK;cACdlK,KAAK,EAAE,wBAAwB;cAC/BhF,MAAM,EAAEE,IAAI,CAAC4C,GAAG;cAChBwL,QAAQ,EAAEpO,IAAI,CAACoO;YACjB,CAAC;UACH;UAEA,MAAMnG,MAAM,GAAGyF,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMyC,UAAU,GAAGnQ,IAAI,CAACoO,QAAQ,CAAC1E,QAAQ,CAACzB,MAAM;UAChD,MAAMmI,aAAa,GAAGnI,MAAM,CAAC8G,WAAW,CAACrF,QAAQ,EAAEyG,UAAU,CAAC;UAE9DxO,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEwO,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;UAClFzO,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEuO,UAAU,CAACrC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;UAC5EnM,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE8H,QAAQ,CAAC3H,MAAM,CAAC;UAE5D,OAAO;YACLiN,OAAO,EAAEoB,aAAa;YACtBtQ,MAAM,EAAEE,IAAI,CAAC4C,GAAG;YAChByN,WAAW,EAAEF,UAAU,CAACrC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAChDwC,cAAc,EAAE5G,QAAQ,CAAC3H;UAC3B,CAAC;QACH,CAAC,CAAC,OAAO+C,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D,OAAO;YAAEkK,OAAO,EAAE,KAAK;YAAElK,KAAK,EAAEA,KAAK,CAACC;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,8BAA8BwL,CAAAC,KAAA,EAAY;QAAA,IAAAC,cAAA,EAAAC,qBAAA;QAAA,IAAX;UAAE3H;QAAM,CAAC,GAAAyH,KAAA;QAC5CjR,KAAK,CAACwJ,KAAK,EAAExG,MAAM,CAAC;QAEpBZ,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEmH,KAAK,CAAC;;QAE5E;QACA,MAAM/I,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC;UAC3C,gBAAgB,EAAE6I;QACpB,CAAC,CAAC;QAEF,IAAI,CAAC/I,IAAI,EAAE;UACT,OAAO;YAAEgP,OAAO,EAAE,KAAK;YAAElK,KAAK,EAAE;UAAiB,CAAC;QACpD;QAEAnD,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEuL,IAAI,CAACC,SAAS,CAACpN,IAAI,CAACoO,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;QAExG;QACA,IAAI,GAAAqC,cAAA,GAACzQ,IAAI,CAACoO,QAAQ,cAAAqC,cAAA,gBAAAC,qBAAA,GAAbD,cAAA,CAAe/G,QAAQ,cAAAgH,qBAAA,eAAvBA,qBAAA,CAAyBzI,MAAM,GAAE;UACpC,OAAO;YAAE+G,OAAO,EAAE,KAAK;YAAElK,KAAK,EAAE;UAAoB,CAAC;QACvD;QAEA,MAAMqL,UAAU,GAAGnQ,IAAI,CAACoO,QAAQ,CAAC1E,QAAQ,CAACzB,MAAM;QAChDtG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuO,UAAU,CAAC;QAChExO,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuO,UAAU,CAACpO,MAAM,CAAC;QACvEJ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEuO,UAAU,CAACrC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEtF;QACA,MAAM6C,QAAQ,GAAG,aAAa,CAACnE,IAAI,CAAC2D,UAAU,CAAC;QAC/CxO,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE+O,QAAQ,CAAC;QAEnE,OAAO;UACL3B,OAAO,EAAE,IAAI;UACblP,MAAM,EAAEE,IAAI,CAAC4C,GAAG;UAChBgO,UAAU,EAAET,UAAU,CAACpO,MAAM;UAC7BsO,WAAW,EAAEF,UAAU,CAACrC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;UAChD+C,cAAc,EAAEF,QAAQ;UACxBG,YAAY,EAAE9Q,IAAI,CAACoO;QACrB,CAAC;MACH,CAAC;MAED,MAAM,uBAAuB2C,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAEjI,KAAK;UAAEW;QAAS,CAAC,GAAAsH,KAAA;QAC/CzR,KAAK,CAACwJ,KAAK,EAAExG,MAAM,CAAC;QACpBhD,KAAK,CAACmK,QAAQ,EAAEnH,MAAM,CAAC;QAEvBZ,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEmH,KAAK,CAAC;QAEjE,IAAI;UAAA,IAAAkI,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA;UACF;UACA,MAAMpR,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC;YAC3C,gBAAgB,EAAE6I;UACpB,CAAC,CAAC;UAEF,IAAI,CAAC/I,IAAI,EAAE;YACT2B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;YAC/C,OAAO;cAAEoN,OAAO,EAAE,KAAK;cAAElK,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAnD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE5B,IAAI,CAAC4C,GAAG,CAAC;UACtDjB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEuL,IAAI,CAACC,SAAS,CAACpN,IAAI,CAACoO,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEvF;UACA,IAAI,GAAA6C,eAAA,GAACjR,IAAI,CAACoO,QAAQ,cAAA6C,eAAA,gBAAAC,qBAAA,GAAbD,eAAA,CAAevH,QAAQ,cAAAwH,qBAAA,eAAvBA,qBAAA,CAAyBjJ,MAAM,GAAE;YACpCtG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;YACvD,OAAO;cAAEoN,OAAO,EAAE,KAAK;cAAElK,KAAK,EAAE;YAAyB,CAAC;UAC5D;;UAEA;UACA,MAAMmD,MAAM,GAAGyF,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMyC,UAAU,GAAGnQ,IAAI,CAACoO,QAAQ,CAAC1E,QAAQ,CAACzB,MAAM;UAChD,MAAMmI,aAAa,GAAGnI,MAAM,CAAC8G,WAAW,CAACrF,QAAQ,EAAEyG,UAAU,CAAC;UAE9DxO,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEwO,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;UACxFzO,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuO,UAAU,CAACrC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;;UAElF;UACA,MAAMuD,UAAU,GAAG,EAAAF,aAAA,GAAAnR,IAAI,CAAC8C,MAAM,cAAAqO,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBnE,QAAQ,KAAI,KAAK;UACtDtL,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyP,UAAU,CAAC;;UAE5D;UACA1P,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE5B,IAAI,CAACI,KAAK,CAAC;;UAExD;UACA,IAAIkR,cAAc,GAAG,IAAI;UACzB,IAAI;YACF;YACA,MAAMC,YAAY,GAAGzJ,QAAQ,CAAC0J,0BAA0B,CAAC,CAAC;YAC1D7P,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,CAAC,CAAC2P,YAAY,CAAC;YACvED,cAAc,GAAG,6BAA6B;UAChD,CAAC,CAAC,OAAOG,UAAU,EAAE;YACnB9P,OAAO,CAACmD,KAAK,CAAC,2CAA2C,EAAE2M,UAAU,CAAC;YACtEH,cAAc,GAAGG,UAAU,CAAC1M,OAAO;UACrC;UAEA,OAAO;YACLiK,OAAO,EAAEoB,aAAa;YACtBtQ,MAAM,EAAEE,IAAI,CAAC4C,GAAG;YAChB8O,oBAAoB,EAAEtB,aAAa;YACnCuB,aAAa,EAAEN,UAAU;YACzBO,SAAS,EAAE5R,IAAI,CAACI,KAAK;YACrBiQ,WAAW,EAAEF,UAAU,CAACrC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAChDwD,cAAc,EAAEA,cAAc;YAC9BO,iBAAiB,EAAE;cACjBjP,GAAG,EAAE5C,IAAI,CAAC4C,GAAG;cACbE,MAAM,EAAE9C,IAAI,CAAC8C,MAAM;cACnBsL,QAAQ,EAAEpO,IAAI,CAACoO,QAAQ;cACvBhO,KAAK,EAAEJ,IAAI,CAACI,KAAK;cACjBgD,OAAO,EAAEpD,IAAI,CAACoD;YAChB;UACF,CAAC;QAEH,CAAC,CAAC,OAAO0B,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,OAAO;YAAEkK,OAAO,EAAE,KAAK;YAAElK,KAAK,EAAEA,KAAK,CAACC;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,qBAAqB+M,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAEhJ,KAAK;UAAEW;QAAS,CAAC,GAAAqI,KAAA;QAC7CxS,KAAK,CAACwJ,KAAK,EAAExG,MAAM,CAAC;QACpBhD,KAAK,CAACmK,QAAQ,EAAEnH,MAAM,CAAC;QAEvBZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEmH,KAAK,CAAC;QAE3D,IAAI;UAAA,IAAAiJ,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA;UACF;UACA,MAAMnS,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC;YAC3C,gBAAgB,EAAE6I;UACpB,CAAC,CAAC;UAEF,IAAI,CAAC/I,IAAI,EAAE;YACT,OAAO;cAAEgP,OAAO,EAAE,KAAK;cAAElK,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAnD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE5B,IAAI,CAAC4C,GAAG,CAAC;;UAEpD;UACA,MAAMqF,MAAM,GAAGyF,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMyC,UAAU,IAAA6B,eAAA,GAAGhS,IAAI,CAACoO,QAAQ,cAAA4D,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAetI,QAAQ,cAAAuI,qBAAA,uBAAvBA,qBAAA,CAAyBhK,MAAM;UAElD,IAAI,CAACkI,UAAU,EAAE;YACf,OAAO;cAAEnB,OAAO,EAAE,KAAK;cAAElK,KAAK,EAAE;YAAyB,CAAC;UAC5D;UAEA,MAAMsL,aAAa,GAAGnI,MAAM,CAAC8G,WAAW,CAACrF,QAAQ,EAAEyG,UAAU,CAAC;UAC9DxO,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEwO,aAAa,CAAC;UAE7D,IAAI,CAACA,aAAa,EAAE;YAClB,OAAO;cAAEpB,OAAO,EAAE,KAAK;cAAElK,KAAK,EAAE;YAAmB,CAAC;UACtD;;UAEA;UACA,MAAMsN,YAAY,GAAG,EAAE;;UAEvB;UACA,MAAMT,aAAa,GAAG,EAAAO,aAAA,GAAAlS,IAAI,CAAC8C,MAAM,cAAAoP,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBlF,QAAQ,KAAI,KAAK;UACzD,IAAI,CAAC0E,aAAa,EAAE;YAClBS,YAAY,CAAC3F,IAAI,CAAC,oBAAoB,CAAC;UACzC;;UAEA;UACA,IAAIzM,IAAI,CAACqS,QAAQ,EAAE;YACjBD,YAAY,CAAC3F,IAAI,CAAC,uBAAuB,CAAC;UAC5C;;UAEA;UACA,IAAI,CAACzM,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACI,KAAK,CAAC2B,MAAM,KAAK,CAAC,EAAE;YAC1CqQ,YAAY,CAAC3F,IAAI,CAAC,mBAAmB,CAAC;UACxC;UAEA9K,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEwQ,YAAY,CAAC;;UAEhE;UACA,IAAIE,eAAe,GAAG,eAAe;UACrC,IAAI;YACF;YACA,MAAMf,YAAY,GAAGzJ,QAAQ,CAAC0J,0BAA0B,CAAC,CAAC;YAC1D,IAAID,YAAY,EAAE;cAChBe,eAAe,GAAG,mCAAmC;YACvD;UACF,CAAC,CAAC,OAAOb,UAAU,EAAE;YACnBa,eAAe,mBAAAnP,MAAA,CAAmBsO,UAAU,CAAC1M,OAAO,CAAE;UACxD;UAEA,OAAO;YACLiK,OAAO,EAAEoB,aAAa,IAAIgC,YAAY,CAACrQ,MAAM,KAAK,CAAC;YACnDjC,MAAM,EAAEE,IAAI,CAAC4C,GAAG;YAChBwN,aAAa,EAAEA,aAAa;YAC5BuB,aAAa,EAAEA,aAAa;YAC5BS,YAAY,EAAEA,YAAY;YAC1BE,eAAe,EAAEA,eAAe;YAChCC,aAAa,EAAE;cACb3P,GAAG,EAAE5C,IAAI,CAAC4C,GAAG;cACbE,MAAM,EAAE9C,IAAI,CAAC8C,MAAM;cACnB1C,KAAK,EAAEJ,IAAI,CAACI,KAAK;cACjBgD,OAAO,EAAEpD,IAAI,CAACoD,OAAO;cACrBiP,QAAQ,EAAErS,IAAI,CAACqS,QAAQ,IAAI;YAC7B;UACF,CAAC;QAEH,CAAC,CAAC,OAAOvN,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,OAAO;YAAEkK,OAAO,EAAE,KAAK;YAAElK,KAAK,EAAEA,KAAK,CAACC;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,4BAA4ByN,CAAA,EAAG;QACnC,IAAI,CAAC,IAAI,CAAC1S,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAAmO,aAAA,EAAAC,cAAA;UACF,MAAM1S,IAAI,GAAG,MAAMV,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UACzD6B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;YACnDkH,EAAE,EAAE9I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,GAAG;YACbmG,KAAK,EAAE/I,IAAI,aAAJA,IAAI,wBAAAyS,aAAA,GAAJzS,IAAI,CAAE8C,MAAM,cAAA2P,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmB1J,OAAO;YACjC5I,KAAK,EAAEJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI;UACf,CAAC,CAAC;;UAEF;UACA,IAAI,CAACJ,IAAI,CAACI,KAAK,EAAE;YACf,MAAMd,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC,IAAI,CAAC5F,MAAM,EAAE;cAC1C6F,IAAI,EAAE;gBAAEvF,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YACF,OAAO,mBAAmB;UAC5B;;UAEA;UACA,IAAI,CAACJ,IAAI,CAACI,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACjCsB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;;YAE/E;YACA,MAAM+Q,UAAU,GAAG,MAAMrT,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC,CAAC,CAACkJ,UAAU,CAAC,CAAC;YACzD,IAAImJ,UAAU,KAAK,CAAC,EAAE;cACpBhR,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;cAClE,MAAMtC,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC,IAAI,CAAC5F,MAAM,EAAE;gBAC1C6F,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAAC,OAAO;gBAAE;cAC3B,CAAC,CAAC;cACF,OAAO,kBAAkB;YAC3B;YACA,OAAO,mBAAmB;UAC5B;UAEA,OAAO,uBAAuB;QAChC,CAAC,CAAC,OAAO0E,KAAK,EAAE;UACdnD,OAAO,CAACmD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,mBAAmB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC5D;MACF,CAAC;MAED,MAAM,qBAAqB6N,CAAA,EAAG;QAC5B,IAAI,CAAC,IAAI,CAAC9S,MAAM,EAAE;UAChB,MAAM,IAAIR,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAAuO,kBAAA;UACF,MAAMpF,WAAW,GAAG,MAAMnO,MAAM,CAACW,KAAK,CAACC,YAAY,CAAC,IAAI,CAACJ,MAAM,CAAC;UAChE,IAAI,GAAA+S,kBAAA,GAACpF,WAAW,CAACrN,KAAK,cAAAyS,kBAAA,eAAjBA,kBAAA,CAAmBxS,QAAQ,CAAC,OAAO,CAAC,GAAE;YACzC,MAAM,IAAIf,MAAM,CAACgF,KAAK,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;UAC5E;UAEA,MAAMoE,QAAQ,GAAG,MAAMpJ,MAAM,CAACW,KAAK,CAACK,IAAI,CAAC,CAAC,CAACqI,UAAU,CAAC,CAAC;UACvD,MAAMmK,eAAe,GAAG,EAAE;UAC1B,MAAMC,KAAK,GAAG,EAAE;UAEhB,KAAK,MAAM/S,IAAI,IAAI0I,QAAQ,EAAE;YAAA,IAAAsK,cAAA,EAAAtP,YAAA;YAC3B,MAAMuP,MAAM,GAAG,EAAE;;YAEjB;YACA,IAAI,CAACjT,IAAI,CAACI,KAAK,IAAI,CAACsC,KAAK,CAACwG,OAAO,CAAClJ,IAAI,CAACI,KAAK,CAAC,EAAE;cAAA,IAAA8S,cAAA,EAAAC,aAAA,EAAAC,cAAA;cAC7CH,MAAM,CAACxG,IAAI,CAAC,gBAAgB,CAAC;cAC7B;cACA,MAAM5C,IAAI,GAAG,EAAAqJ,cAAA,GAAAlT,IAAI,CAACoD,OAAO,cAAA8P,cAAA,uBAAZA,cAAA,CAAcrJ,IAAI,KAAI,aAAa;cAChD,MAAMvK,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC1F,IAAI,CAAC4C,GAAG,EAAE;gBACvC+C,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAACyJ,IAAI;gBAAE;cACxB,CAAC,CAAC;cACFkJ,KAAK,CAACtG,IAAI,0BAAAtJ,MAAA,EAAAgQ,aAAA,GAA0BnT,IAAI,CAAC8C,MAAM,cAAAqQ,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBpK,OAAO,CAAE,CAAC;YAClE;;YAEA;YACA,IAAI,CAAAgK,cAAA,GAAAhT,IAAI,CAACoD,OAAO,cAAA4P,cAAA,eAAZA,cAAA,CAAcnJ,IAAI,IAAI,EAAAnG,YAAA,GAAA1D,IAAI,CAACI,KAAK,cAAAsD,YAAA,uBAAVA,YAAA,CAAa,CAAC,CAAC,MAAK1D,IAAI,CAACoD,OAAO,CAACyG,IAAI,EAAE;cAAA,IAAAwJ,aAAA,EAAAC,cAAA;cAC/DL,MAAM,CAACxG,IAAI,CAAC,4BAA4B,CAAC;cACzC;cACA,MAAMnN,MAAM,CAACW,KAAK,CAACyF,WAAW,CAAC1F,IAAI,CAAC4C,GAAG,EAAE;gBACvC+C,IAAI,EAAE;kBAAEvF,KAAK,EAAE,CAACJ,IAAI,CAACoD,OAAO,CAACyG,IAAI;gBAAE;cACrC,CAAC,CAAC;cACFkJ,KAAK,CAACtG,IAAI,4BAAAtJ,MAAA,EAAAkQ,aAAA,GAA4BrT,IAAI,CAAC8C,MAAM,cAAAuQ,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBtK,OAAO,CAAE,CAAC;YACpE;YAEA,IAAIiK,MAAM,CAAClR,MAAM,GAAG,CAAC,EAAE;cAAA,IAAAwR,aAAA,EAAAC,cAAA;cACrBV,eAAe,CAACrG,IAAI,CAAC;gBACnB1D,KAAK,GAAAwK,aAAA,GAAEvT,IAAI,CAAC8C,MAAM,cAAAyQ,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBxK,OAAO;gBAChCiK;cACF,CAAC,CAAC;YACJ;UACF;UAEA,OAAO;YACLH,eAAe;YACfC,KAAK;YACLhO,OAAO,EAAEgO,KAAK,CAAChR,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;UACpD,CAAC;QACH,CAAC,CAAC,OAAO+C,KAAK,EAAE;UACd,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,iBAAiB,EAAEQ,KAAK,CAACC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,4BAA4B0O,CAAA,EAAG;QAC7B;QACA,IAAI,CAACtJ,OAAO,CAACC,GAAG,CAACsJ,QAAQ,IAAIvJ,OAAO,CAACC,GAAG,CAACsJ,QAAQ,KAAK,aAAa,EAAE;UACnE,IAAI;YACF,MAAMC,UAAU,GAAG;cACjB5K,KAAK,EAAE,wBAAwB;cAC/BW,QAAQ,EAAE,cAAc;cACxBrG,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC;YAED,MAAMxD,MAAM,GAAGgI,QAAQ,CAAC6E,UAAU,CAAC;cACjC5D,KAAK,EAAE4K,UAAU,CAAC5K,KAAK;cACvBW,QAAQ,EAAEiK,UAAU,CAACjK,QAAQ;cAC7BtG,OAAO,EAAE;gBACPC,SAAS,EAAEsQ,UAAU,CAACtQ,SAAS;gBAC/BC,QAAQ,EAAEqQ,UAAU,CAACrQ,QAAQ;gBAC7BuG,IAAI,EAAE,aAAa;gBACnBC,QAAQ,KAAA3G,MAAA,CAAKwQ,UAAU,CAACtQ,SAAS,OAAAF,MAAA,CAAIwQ,UAAU,CAACrQ,QAAQ;cAC1D;YACF,CAAC,CAAC;;YAEF;YACAhE,MAAM,CAACW,KAAK,CAAC8F,MAAM,CAACjG,MAAM,EAAE;cAC1B6F,IAAI,EAAE;gBAAEvF,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YAEF,OAAO;cACL4O,OAAO,EAAE,IAAI;cACblP,MAAM;cACNiF,OAAO,EAAE;YACX,CAAC;UACH,CAAC,CAAC,OAAOD,KAAK,EAAE;YACdnD,OAAO,CAACmD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD,MAAM,IAAIxF,MAAM,CAACgF,KAAK,CAAC,2BAA2B,EAAEQ,KAAK,CAACC,OAAO,CAAC;UACpE;QACF,CAAC,MAAM;UACL,MAAM,IAAIzF,MAAM,CAACgF,KAAK,CAAC,iBAAiB,EAAE,8CAA8C,CAAC;QAC3F;MACF;IACF,CAAC,CAAC;IAACzF,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/app.js", "sourcesContent": ["import { Mongo } from 'meteor/mongo';\r\n\r\nexport const LinksCollection = new Mongo.Collection('links');\r\n", "import { Meteor } from 'meteor/meteor';\nimport { Mongo } from 'meteor/mongo';\nimport { check } from 'meteor/check';\nimport { Match } from 'meteor/check';\n\nexport const Tasks = new Mongo.Collection('tasks');\n\nexport const taskCategories = [\n  'Development',\n  'Design',\n  'Marketing',\n  'Sales',\n  'Support',\n  'Planning',\n  'Research',\n  'Other'\n];\n\nexport const taskLabels = [\n  { name: 'Bug', color: '#ef4444' },\n  { name: 'Feature', color: '#3b82f6' },\n  { name: 'Enhancement', color: '#10b981' },\n  { name: 'Documentation', color: '#8b5cf6' },\n  { name: 'Urgent', color: '#f59e0b' },\n  { name: 'Blocked', color: '#6b7280' }\n];\n\nif (Meteor.isServer) {\n  // Publications\n  Meteor.publish('tasks', async function() {\n    if (!this.userId) {\n      return this.ready();\n    }\n\n    // Get user's role\n    const user = await Meteor.users.findOneAsync(this.userId);\n    const isAdmin = user?.roles?.includes('admin');\n\n    // If admin, show all tasks\n    if (isAdmin) {\n      return Tasks.find({}, { \n        sort: { createdAt: -1 },\n        fields: {\n          title: 1,\n          description: 1,\n          startDate: 1,\n          dueDate: 1,\n          priority: 1,\n          status: 1,\n          assignedTo: 1,\n          checklist: 1,\n          category: 1,\n          labels: 1,\n          progress: 1,\n          attachments: 1,\n          links: 1,\n          createdAt: 1,\n          createdBy: 1,\n          updatedAt: 1,\n          updatedBy: 1\n        }\n      });\n    }\n\n    // For team members, show tasks they're assigned to or created\n    return Tasks.find({\n      $or: [\n        { assignedTo: this.userId },\n        { createdBy: this.userId }\n      ]\n    }, { \n      sort: { createdAt: -1 },\n      fields: {\n        title: 1,\n        description: 1,\n        startDate: 1,\n        dueDate: 1,\n        priority: 1,\n        status: 1,\n        assignedTo: 1,\n        checklist: 1,\n        category: 1,\n        labels: 1,\n        progress: 1,\n        attachments: 1,\n        links: 1,\n        createdAt: 1,\n        createdBy: 1,\n        updatedAt: 1,\n        updatedBy: 1\n      }\n    });\n  });\n\n  // Publish user data for tasks\n  Meteor.publish('taskUsers', function() {\n    console.log('Starting taskUsers publication');\n    if (!this.userId) {\n      console.log('No userId, returning ready');\n      return this.ready();\n    }\n\n    // Get all tasks\n    const tasks = Tasks.find({}).fetch();\n    console.log('Found tasks:', tasks.length);\n    \n    // Collect all user IDs from tasks\n    const userIds = new Set();\n    tasks.forEach(task => {\n      // Add users who uploaded attachments\n      if (task.attachments) {\n        task.attachments.forEach(attachment => {\n          if (attachment.uploadedBy) {\n            userIds.add(String(attachment.uploadedBy));\n          }\n        });\n      }\n      // Add users who added links\n      if (task.links) {\n        task.links.forEach(link => {\n          if (link.addedBy) {\n            userIds.add(String(link.addedBy));\n          }\n        });\n      }\n      // Add assigned users\n      if (task.assignedTo) {\n        task.assignedTo.forEach(userId => {\n          userIds.add(String(userId));\n        });\n      }\n    });\n\n    const userIdArray = Array.from(userIds);\n    console.log('Publishing user data for IDs:', userIdArray);\n\n    // Find users and log what we found\n    const users = Meteor.users.find(\n      { _id: { $in: userIdArray } },\n      { \n        fields: {\n          _id: 1,\n          emails: 1,\n          roles: 1,\n          'profile.firstName': 1,\n          'profile.lastName': 1,\n          'profile.role': 1,\n          'profile.department': 1,\n          'profile.skills': 1,\n          'profile.joinDate': 1,\n          createdAt: 1\n        } \n      }\n    ).fetch();\n\n    console.log('Found users:', users.map(u => ({\n      _id: u._id,\n      name: `${u.profile?.firstName || ''} ${u.profile?.lastName || ''}`.trim(),\n      hasProfile: !!u.profile\n    })));\n\n    // Return the cursor\n    return Meteor.users.find(\n      { _id: { $in: userIdArray } },\n      { \n      fields: {\n        _id: 1,\n          emails: 1,\n          roles: 1,\n        'profile.firstName': 1,\n        'profile.lastName': 1,\n          'profile.role': 1,\n          'profile.department': 1,\n          'profile.skills': 1,\n          'profile.joinDate': 1,\n          createdAt: 1\n        } \n      }\n    );\n  });\n\n  // Add a specific publication for a single task\n  Meteor.publish('task', function(taskId) {\n    check(taskId, String);\n    \n    if (!this.userId) {\n      return this.ready();\n    }\n\n    const user = Meteor.users.findOne(this.userId);\n    const isAdmin = user?.roles?.includes('admin');\n\n    // Return a cursor that will update reactively\n    const cursor = Tasks.find(\n      { _id: taskId },\n      {\n        fields: {\n          title: 1,\n          description: 1,\n          startDate: 1,\n          dueDate: 1,\n          priority: 1,\n          status: 1,\n          assignedTo: 1,\n          checklist: 1,\n          category: 1,\n          labels: 1,\n          progress: 1,\n          attachments: 1,\n          links: 1,\n          createdAt: 1,\n          createdBy: 1,\n          updatedAt: 1,\n          updatedBy: 1\n        }\n      }\n    );\n\n    // Log for debugging\n    console.log('Publishing task:', taskId);\n    cursor.observe({\n      added: (doc) => console.log('Task added to publication:', doc._id),\n      changed: (doc) => console.log('Task changed in publication:', doc._id),\n      removed: (doc) => console.log('Task removed from publication:', doc._id)\n    });\n\n    return cursor;\n  });\n}\n\nMeteor.methods({\n  async 'tasks.insert'(task) {\n    check(task, {\n      title: String,\n      description: String,\n      startDate: Date,\n      dueDate: Date,\n      priority: String,\n      status: String,\n      assignedTo: Array,\n      checklist: Array,\n      category: String,\n      labels: Array,\n      progress: Number\n    });\n\n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    console.log('Creating new task:', task); // Debug log\n\n    // Process checklist items\n    const processedChecklist = task.checklist.map(item => ({\n      text: item.text,\n      completed: item.completed || false\n    }));\n\n    const taskToInsert = {\n      ...task,\n      createdAt: new Date(),\n      createdBy: this.userId,\n      updatedAt: new Date(),\n      updatedBy: this.userId,\n      progress: task.progress || 0,\n      status: 'pending', // Default status\n      checklist: processedChecklist,\n      labels: task.labels || [],\n      category: task.category || '',\n      assignedTo: task.assignedTo || []\n    };\n\n    console.log('Inserting task with values:', taskToInsert); // Debug log\n\n    try {\n      const result = await Tasks.insertAsync(taskToInsert);\n      console.log('Task created successfully:', result); // Debug log\n      return result;\n    } catch (error) {\n      console.error('Error creating task:', error);\n      throw new Meteor.Error('task-creation-failed', error.message);\n    }\n  },\n\n  async 'tasks.update'(taskId, task) {\n    try {\n      console.log('Starting task update:', { taskId, task });\n\n      check(taskId, String);\n      check(task, {\n        title: String,\n        description: String,\n        startDate: Date,\n        dueDate: Date,\n        priority: String,\n        assignedTo: Array,\n        checklist: Array,\n        category: Match.Optional(String),\n        labels: Match.Optional(Array),\n        progress: Match.Optional(Number),\n        status: Match.Optional(String),\n        attachments: Match.Optional(Array)\n      });\n\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const existingTask = await Tasks.findOneAsync(taskId);\n      if (!existingTask) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task or is admin\n      const user = await Meteor.users.findOneAsync(this.userId);\n      const isAdmin = user?.roles?.includes('admin');\n      \n      if (!isAdmin && !existingTask.assignedTo.includes(this.userId)) {\n        throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');\n      }\n\n      // Calculate progress based on checklist\n      const completedItems = task.checklist.filter(item => item.completed).length;\n      const totalItems = task.checklist.length;\n      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;\n\n      // Update task status based on progress\n      let status = task.status;\n      if (progress === 100) {\n        status = 'completed';\n      } else if (progress > 0) {\n        status = 'in-progress';\n      } else {\n        status = 'pending';\n      }\n\n      const taskToUpdate = {\n        ...task,\n        updatedAt: new Date(),\n        updatedBy: this.userId,\n        progress,\n        status,\n        category: task.category || existingTask.category || '',\n        labels: task.labels || existingTask.labels || [],\n        attachments: task.attachments || existingTask.attachments || []\n      };\n\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        { $set: taskToUpdate }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to update task');\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error updating task:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.delete'(taskId) {\n    check(taskId, String);\n\n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    try {\n      const result = await Tasks.removeAsync(taskId);\n      \n      if (result === 0) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error deleting task:', error);\n      throw new Meteor.Error('task-delete-failed', error.message);\n    }\n  },\n\n  'tasks.updateProgress'(taskId, progress) {\n    check(taskId, String);\n    check(progress, Number);\n\n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    const task = Tasks.findOne(taskId);\n    if (!task) {\n      throw new Meteor.Error('Task not found.');\n    }\n\n    // Check if user is assigned to the task\n    if (!task.assignedTo.includes(this.userId)) {\n      throw new Meteor.Error('Not authorized to modify this task.');\n    }\n\n    // Update task status based on progress\n    let status = task.status;\n    if (progress === 100) {\n      status = 'completed';\n    } else if (progress > 0) {\n      status = 'in-progress';\n    }\n\n    return Tasks.update(taskId, {\n      $set: {\n        progress,\n        status,\n        updatedAt: new Date(),\n        updatedBy: this.userId\n      }\n    });\n  },\n\n  async 'tasks.toggleChecklistItem'(taskId, itemIndex) {\n    try {\n      console.log('Starting toggleChecklistItem with:', { taskId, itemIndex, userId: this.userId });\n\n      if (!this.userId) {\n        console.log('No user ID found');\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      // Validate inputs\n      if (!taskId || typeof taskId !== 'string') {\n        console.log('Invalid taskId:', taskId);\n        throw new Meteor.Error('invalid-input', 'Invalid task ID');\n      }\n\n      if (typeof itemIndex !== 'number' || itemIndex < 0) {\n        console.log('Invalid itemIndex:', itemIndex);\n        throw new Meteor.Error('invalid-input', 'Invalid checklist item index');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      console.log('Found task:', task);\n\n      if (!task) {\n        console.log('Task not found');\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task or is admin\n      const user = await Meteor.users.findOneAsync(this.userId);\n      console.log('Found user:', user);\n      \n      const isAdmin = user?.roles?.includes('admin');\n      console.log('Is admin:', isAdmin);\n      \n      if (!isAdmin && !task.assignedTo.includes(this.userId)) {\n        console.log('User not authorized:', { userId: this.userId, assignedTo: task.assignedTo });\n        throw new Meteor.Error('not-authorized', 'You are not authorized to modify this task');\n      }\n\n      const checklist = task.checklist || [];\n      console.log('Current checklist:', checklist);\n\n      if (itemIndex >= checklist.length) {\n        console.log('Invalid item index:', { itemIndex, checklistLength: checklist.length });\n        throw new Meteor.Error('invalid-index', 'Invalid checklist item index');\n      }\n\n      // Create a new array to ensure reactivity\n      const updatedChecklist = [...checklist];\n      updatedChecklist[itemIndex] = {\n        ...updatedChecklist[itemIndex],\n        completed: !updatedChecklist[itemIndex].completed\n      };\n\n      console.log('Updated checklist:', updatedChecklist);\n\n      // Calculate progress\n      const completedItems = updatedChecklist.filter(item => item.completed).length;\n      const totalItems = updatedChecklist.length;\n      const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;\n\n      // Update task status based on progress\n      let status;\n      if (progress === 100) {\n        status = 'completed';\n      } else if (progress > 0) {\n        status = 'in-progress';\n      } else {\n        status = 'pending';\n      }\n\n      console.log('Updating task with:', {\n        taskId,\n        updatedChecklist,\n        progress,\n        status\n      });\n\n      // First verify the task still exists\n      const existingTask = await Tasks.findOneAsync(taskId);\n      if (!existingTask) {\n        throw new Meteor.Error('task-not-found', 'Task no longer exists');\n      }\n\n      // Perform the update\n      const updateResult = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $set: {\n            checklist: updatedChecklist,\n            progress,\n            status,\n            updatedAt: new Date(),\n            updatedBy: this.userId\n          }\n        }\n      );\n\n      console.log('Update result:', updateResult);\n\n      if (updateResult === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to update task');\n      }\n\n      // Verify the update\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after update:', updatedTask);\n\n      return updateResult;\n    } catch (error) {\n      console.error('Error in toggleChecklistItem:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.addAttachment'(taskId, fileData) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task\n      if (!task.assignedTo.includes(this.userId)) {\n        throw new Meteor.Error('not-authorized', 'You are not authorized to add attachments to this task');\n      }\n\n      if (!fileData || !fileData.name || !fileData.data) {\n        throw new Meteor.Error('invalid-input', 'Invalid file data');\n      }\n\n      // Ensure we're storing the user ID as a string\n      const uploaderId = String(this.userId);\n\n      // Add the file data to attachments with uploader info\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $push: {\n            attachments: {\n              name: fileData.name,\n              type: fileData.type,\n              data: fileData.data,\n              uploadedAt: new Date(),\n              uploadedBy: uploaderId\n            }\n          },\n          $set: {\n            updatedAt: new Date(),\n            updatedBy: uploaderId\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to add attachment');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after adding attachment:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error adding attachment:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.addLink'(taskId, link) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if user is assigned to the task\n      if (!task.assignedTo.includes(this.userId)) {\n        throw new Meteor.Error('not-authorized', 'You are not authorized to add links to this task');\n      }\n\n      if (!link) {\n        throw new Meteor.Error('invalid-input', 'Link URL is required');\n      }\n\n      // Validate URL format\n      try {\n        new URL(link);\n      } catch (e) {\n        throw new Meteor.Error('invalid-input', 'Invalid URL format');\n      }\n\n      // Add the link to links array with adder info\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $push: {\n            links: {\n              url: link,\n              addedAt: new Date(),\n              addedBy: String(this.userId)\n            }\n          },\n          $set: {\n            updatedAt: new Date(),\n            updatedBy: String(this.userId)\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to add link');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after adding link:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error adding link:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.removeAttachment'(taskId, attachmentIndex) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if the attachment exists\n      if (!task.attachments || !task.attachments[attachmentIndex]) {\n        throw new Meteor.Error('not-found', 'Attachment not found');\n      }\n\n      const attachment = task.attachments[attachmentIndex];\n      \n      // Convert both IDs to strings for comparison\n      const currentUserId = String(this.userId);\n      const uploadedById = String(attachment.uploadedBy);\n      \n      // Only allow the uploader to remove the attachment\n      if (currentUserId !== uploadedById) {\n        throw new Meteor.Error('not-authorized', 'You can only remove your own attachments');\n      }\n\n      // Create a new array without the specified attachment\n      const updatedAttachments = [...task.attachments];\n      updatedAttachments.splice(attachmentIndex, 1);\n\n      // Update the task with the new attachments array\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $set: {\n            attachments: updatedAttachments,\n            updatedAt: new Date(),\n            updatedBy: currentUserId\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to remove attachment');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after removing attachment:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error removing attachment:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.removeLink'(taskId, linkIndex) {\n    try {\n      if (!this.userId) {\n        throw new Meteor.Error('not-authorized', 'You must be logged in to perform this action');\n      }\n\n      const task = await Tasks.findOneAsync(taskId);\n      if (!task) {\n        throw new Meteor.Error('not-found', 'Task not found');\n      }\n\n      // Check if the link exists\n      if (!task.links || !task.links[linkIndex]) {\n        throw new Meteor.Error('not-found', 'Link not found');\n      }\n\n      const link = task.links[linkIndex];\n      \n      // Convert both IDs to strings for comparison\n      const currentUserId = String(this.userId);\n      const addedById = String(link.addedBy);\n      \n      // Only allow the user who added the link to remove it\n      if (currentUserId !== addedById) {\n        throw new Meteor.Error('not-authorized', 'You can only remove your own links');\n      }\n\n      // Create a new array without the specified link\n      const updatedLinks = [...task.links];\n      updatedLinks.splice(linkIndex, 1);\n\n      // Update the task with the new links array\n      const result = await Tasks.updateAsync(\n        { _id: taskId },\n        {\n          $set: {\n            links: updatedLinks,\n            updatedAt: new Date(),\n            updatedBy: currentUserId\n          }\n        }\n      );\n\n      if (result === 0) {\n        throw new Meteor.Error('update-failed', 'Failed to remove link');\n      }\n\n      // Get and return the updated task\n      const updatedTask = await Tasks.findOneAsync(taskId);\n      console.log('Task after removing link:', updatedTask);\n      return updatedTask;\n    } catch (error) {\n      console.error('Error removing link:', error);\n      if (error instanceof Meteor.Error) {\n        throw error;\n      }\n      throw new Meteor.Error('server-error', error.message || 'An unexpected error occurred');\n    }\n  },\n\n  async 'tasks.findOne'(taskId) {\n    check(taskId, String);\n    \n    if (!this.userId) {\n      throw new Meteor.Error('Not authorized.');\n    }\n\n    const task = await Tasks.findOneAsync(taskId);\n    if (!task) {\n      throw new Meteor.Error('task-not-found', 'Task not found');\n    }\n\n    return task;\n  },\n}); ", "import { Meteor } from 'meteor/meteor';\nimport { LinksCollection } from '/imports/api/links';\nimport { Accounts } from 'meteor/accounts-base';\nimport { Email } from 'meteor/email';\nimport { Tasks } from '/imports/api/tasks';\nimport { Roles } from 'meteor/alanning:roles';\nimport { check } from 'meteor/check';\nimport bcrypt from 'bcrypt';\n\nasync function insertLink({ title, url }) {\n  await LinksCollection.insertAsync({ title, url, createdAt: new Date() });\n}\n\nconst ADMIN_TOKEN = '123456';\n\nMeteor.startup(async () => {\n  // Ensure indexes for Tasks collection\n  try {\n    await Tasks.createIndex({ createdAt: 1 });\n    await Tasks.createIndex({ assignedTo: 1 });\n    await Tasks.createIndex({ createdBy: 1 });\n\n    // Ensure indexes for Users collection\n    // Note: emails.address index is already created by Meteor accounts system\n    await Meteor.users.createIndex({ roles: 1 }, { background: true });\n  } catch (error) {\n    console.warn('[Startup] Index creation warning:', error.message);\n  }\n\n  // Check if we have any team members\n  try {\n    const allUsers = await Meteor.users.find().fetchAsync();\n    console.log('[Startup] All users:', allUsers.map(user => ({\n      id: user._id,\n      email: user.emails?.[0]?.address,\n      roles: user.roles,\n      profile: user.profile\n    })));\n\n    // First, ensure all users have roles and createdAt\n    for (const user of allUsers) {\n      const updates = {};\n      \n      if (!user.roles || !Array.isArray(user.roles)) {\n        updates.roles = ['team-member'];\n      }\n      \n      if (!user.createdAt) {\n        updates.createdAt = new Date();\n      }\n      \n      if (Object.keys(updates).length > 0) {\n        console.log('[Startup] Fixing missing fields for user:', user.emails?.[0]?.address);\n        await Meteor.users.updateAsync(user._id, {\n          $set: updates\n        });\n      }\n    }\n\n    const teamMembersCount = await Meteor.users.find({ 'roles': 'team-member' }).countAsync();\n    console.log('[Startup] Found team members:', teamMembersCount);\n\n    // Create test team members if none exist\n    if (teamMembersCount === 0) {\n      console.log('[Startup] Creating test team members');\n      try {\n        // Create multiple test team members\n        const testMembers = [\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'John',\n            lastName: 'Doe'\n          },\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'Jane',\n            lastName: 'Smith'\n          }\n        ];\n\n        for (const member of testMembers) {\n          const userId = await Accounts.createUserAsync({\n            email: member.email,\n            password: member.password,\n            role: 'team-member',\n            createdAt: new Date(),\n            profile: {\n              firstName: member.firstName,\n              lastName: member.lastName,\n              role: 'team-member',\n              fullName: `${member.firstName} ${member.lastName}`\n            }\n          });\n\n          // Set the role explicitly\n          await Meteor.users.updateAsync(userId, {\n            $set: { roles: ['team-member'] }\n          });\n\n          console.log('[Startup] Created test team member:', {\n            id: userId,\n            email: member.email,\n            name: `${member.firstName} ${member.lastName}`\n          });\n        }\n      } catch (error) {\n        console.error('[Startup] Error creating test team members:', error);\n      }\n    }\n  } catch (error) {\n    console.error('[Startup] Error checking team members:', error);\n  }\n\n  // Email configuration from settings\n  const emailSettings = Meteor.settings.private?.email;\n  \n  // Configure email SMTP\n  if (emailSettings?.username && emailSettings?.password) {\n    process.env.MAIL_URL = `smtp://${encodeURIComponent(emailSettings.username)}:${encodeURIComponent(emailSettings.password)}@${emailSettings.server}:${emailSettings.port}`;\n    \n    // Test email configuration\n    try {\n      console.log('Testing email configuration...');\n      Email.send({\n        to: emailSettings.username,\n        from: emailSettings.username,\n        subject: 'Test Email',\n        text: 'If you receive this email, your email configuration is working correctly.'\n      });\n      console.log('Test email sent successfully!');\n    } catch (error) {\n      console.error('Error sending test email:', error);\n    }\n  } else {\n    console.warn('Email configuration is missing in settings.json');\n  }\n\n  // Configure account creation - allow login without email verification\n  Accounts.config({\n    sendVerificationEmail: false,  // Disable email verification requirement\n    forbidClientAccountCreation: false\n  });\n\n  // Add login validation hook to explicitly allow all login attempts\n  Accounts.validateLoginAttempt((attempt) => {\n    console.log('[validateLoginAttempt] Login attempt:', {\n      type: attempt.type,\n      allowed: attempt.allowed,\n      error: attempt.error?.message,\n      userId: attempt.user?._id,\n      methodName: attempt.methodName\n    });\n\n    // Always allow login attempts - bypass any built-in restrictions\n    if (attempt.user && attempt.type === 'password') {\n      console.log('[validateLoginAttempt] Allowing password login for user:', attempt.user._id);\n      return true;\n    }\n\n    // Allow other types of login attempts as well\n    return true;\n  });\n\n  // Customize verification email\n  Accounts.emailTemplates.siteName = \"Task Management System\";\n  Accounts.emailTemplates.from = emailSettings?.username ? \n    `Task Management System <${emailSettings.username}>` : \n    \"Task Management System <<EMAIL>>\";\n\n  Accounts.emailTemplates.verifyEmail = {\n    subject() {\n      return \"Verify Your Email Address\";\n    },\n    text(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `Hello,\\n\\n`\n        + `To verify your email address (${emailAddress}), please click the link below:\\n\\n`\n        + `${url}\\n\\n`\n        + `If you did not request this verification, please ignore this email.\\n\\n`\n        + `Thanks,\\n`\n        + `Your Task Management System Team`;\n    },\n    html(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (${emailAddress}), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"${url}\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      `;\n    }\n  };\n\n  // If the Links collection is empty, add some data.\n  if (await LinksCollection.find().countAsync() === 0) {\n    await insertLink({\n      title: 'Do the Tutorial',\n      url: 'https://www.meteor.com/tutorials/react/creating-an-app',\n    });\n\n    await insertLink({\n      title: 'Follow the Guide',\n      url: 'https://guide.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Read the Docs',\n      url: 'https://docs.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Discussions',\n      url: 'https://forums.meteor.com',\n    });\n  }\n\n  // We publish the entire Links collection to all clients.\n  // In order to be fetched in real-time to the clients\n  Meteor.publish(\"links\", function () {\n    return LinksCollection.find();\n  });\n\n  // Add custom fields to users\n  Accounts.onCreateUser((options, user) => {\n    console.log('[onCreateUser] Creating user with options:', {\n      email: options.email,\n      role: options.role,\n      profile: options.profile,\n      createdAt: options.createdAt\n    });\n\n    const customizedUser = { ...user };\n    \n    // Ensure we have a profile\n    customizedUser.profile = options.profile || {};\n    \n    // Add role from options\n    const role = options.role || 'team-member';\n    customizedUser.roles = [role];\n    \n    // Set createdAt if provided, otherwise use current date\n    customizedUser.createdAt = options.createdAt || new Date();\n    \n    console.log('[onCreateUser] Created user:', {\n      id: customizedUser._id,\n      email: customizedUser.emails?.[0]?.address,\n      roles: customizedUser.roles,\n      profile: customizedUser.profile,\n      createdAt: customizedUser.createdAt\n    });\n    \n    return customizedUser;\n  });\n\n  // Publish team members\n  Meteor.publish('teamMembers', function() {\n    console.log('[teamMembers] Publication called, userId:', this.userId);\n    \n    if (!this.userId) {\n      console.log('[teamMembers] No userId, returning ready');\n      return this.ready();\n    }\n\n    try {\n      // Simple query to find all team members\n      const teamMembers = Meteor.users.find(\n        { \n          $or: [\n            { 'roles': 'team-member' },\n            { 'profile.role': 'team-member' }\n          ]\n        },\n        { \n          fields: { \n            emails: 1, \n            roles: 1,\n            'profile.firstName': 1,\n            'profile.lastName': 1,\n            'profile.fullName': 1,\n            createdAt: 1\n          }\n        }\n      );\n\n      console.log('[teamMembers] Publishing team members');\n      return teamMembers;\n    } catch (error) {\n      console.error('[teamMembers] Error in publication:', error);\n      return this.ready();\n    }\n  });\n\n  // Publish user data with roles\n  Meteor.publish('userData', function() {\n    if (!this.userId) {\n      return this.ready();\n    }\n\n    console.log('[userData] Publishing data for user:', this.userId);\n    \n    return Meteor.users.find(\n      { _id: this.userId },\n      { \n        fields: { \n          roles: 1, \n          emails: 1,\n          profile: 1\n        } \n      }\n    );\n  });\n});\n\n// Method to create a new user with role\nMeteor.methods({\n  'users.create'({ email, password, role, adminToken, firstName, lastName }) {\n    // Validate admin token if trying to create admin account\n    if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n      throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n    }\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(password)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(password)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(password)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(password)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Create the user\n    try {\n      const userId = Accounts.createUser({\n        email,\n        password,\n        role, // This will be used in onCreateUser callback\n        profile: {\n          role, // Store in profile as well for easy access\n          firstName,\n          lastName,\n          fullName: `${firstName} ${lastName}`\n        }\n      });\n\n      // Send verification email\n      if (userId) {\n        Accounts.sendVerificationEmail(userId);\n      }\n\n      return userId;\n    } catch (error) {\n      throw new Meteor.Error('create-user-failed', error.message);\n    }\n  },\n\n  async 'users.getRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'User must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      if (!user) {\n        throw new Meteor.Error('user-not-found', 'User not found');\n      }\n      \n      // Check both roles array and profile for role\n      const role = user.roles?.[0] || user.profile?.role || 'team-member';\n      return role;\n    } catch (error) {\n      throw new Meteor.Error('get-role-failed', error.message);\n    }\n  },\n\n  'users.resendVerificationEmail'(email) {\n    // Find user by email\n    const user = Accounts.findUserByEmail(email);\n    if (!user) {\n      throw new Meteor.Error('user-not-found', 'No user found with this email address');\n    }\n\n    // Check if email is already verified\n    const userEmail = user.emails[0];\n    if (userEmail.verified) {\n      throw new Meteor.Error('already-verified', 'This email is already verified');\n    }\n\n    // Send verification email\n    try {\n      Accounts.sendVerificationEmail(user._id, email);\n      return true;\n    } catch (error) {\n      throw new Meteor.Error('verification-email-failed', error.message);\n    }\n  },\n\n  async 'users.forgotPassword'(data) {\n    console.log('[forgotPassword] Method called with data:', JSON.stringify(data));\n\n    check(data, {\n      email: String,\n      newPassword: String\n    });\n\n    const { email, newPassword } = data;\n    console.log('[forgotPassword] Processing request for email:', email);\n\n    // Find user by email using async method\n    console.log('[forgotPassword] Searching for user...');\n    let targetUser = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!targetUser) {\n      console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');\n      targetUser = await Meteor.users.findOneAsync({\n        'emails.address': { $regex: new RegExp(`^${email}$`, 'i') }\n      });\n\n      if (!targetUser) {\n        throw new Meteor.Error('user-not-found', 'No user found with this email address');\n      }\n\n      console.log('[forgotPassword] User found with case-insensitive search');\n    } else {\n      console.log('[forgotPassword] User found with direct search');\n    }\n\n    // Ensure we have a valid user with ID\n    if (!targetUser || !targetUser._id) {\n      throw new Meteor.Error('user-invalid', 'Found user but missing ID');\n    }\n\n    console.log('[forgotPassword] Final user ID:', targetUser._id);\n    console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(newPassword)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Comprehensive password update with debugging\n    try {\n      console.log('[forgotPassword] Starting password update...');\n\n      // First, check current user document\n      console.log('[forgotPassword] Checking current user document...');\n      const currentUser = await Meteor.users.findOneAsync(targetUser._id);\n      console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));\n\n      if (!currentUser) {\n        throw new Meteor.Error('user-not-found', 'User document not found during update');\n      }\n\n      // Create password hash using bcrypt directly\n      const bcrypt = require('bcrypt');\n      const saltRounds = 10;\n      const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);\n      console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);\n      console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');\n\n      // Try multiple update approaches\n      let updateResult = 0;\n      let successMethod = null;\n\n      // Method 1: Update services.password.bcrypt directly\n      console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');\n      try {\n        updateResult = await Meteor.users.updateAsync(targetUser._id, {\n          $set: {\n            'services.password.bcrypt': hashedPassword\n          }\n        });\n        console.log('[forgotPassword] Method 1 result:', updateResult);\n        if (updateResult === 1) {\n          successMethod = 'Method 1: services.password.bcrypt';\n        }\n      } catch (method1Error) {\n        console.error('[forgotPassword] Method 1 error:', method1Error);\n      }\n\n      // Method 2: Update entire services.password object\n      if (updateResult !== 1) {\n        console.log('[forgotPassword] Method 2: Updating entire services.password object...');\n        try {\n          updateResult = await Meteor.users.updateAsync(targetUser._id, {\n            $set: {\n              'services.password': {\n                bcrypt: hashedPassword\n              }\n            }\n          });\n          console.log('[forgotPassword] Method 2 result:', updateResult);\n          if (updateResult === 1) {\n            successMethod = 'Method 2: entire services.password object';\n          }\n        } catch (method2Error) {\n          console.error('[forgotPassword] Method 2 error:', method2Error);\n        }\n      }\n\n      // Method 3: Update entire services object\n      if (updateResult !== 1) {\n        console.log('[forgotPassword] Method 3: Updating entire services object...');\n        try {\n          updateResult = await Meteor.users.updateAsync(targetUser._id, {\n            $set: {\n              services: {\n                password: {\n                  bcrypt: hashedPassword\n                },\n                resume: currentUser.services?.resume || { loginTokens: [] },\n                email: currentUser.services?.email || {}\n              }\n            }\n          });\n          console.log('[forgotPassword] Method 3 result:', updateResult);\n          if (updateResult === 1) {\n            successMethod = 'Method 3: entire services object';\n          }\n        } catch (method3Error) {\n          console.error('[forgotPassword] Method 3 error:', method3Error);\n        }\n      }\n\n      // Method 4: Test basic update capability\n      if (updateResult !== 1) {\n        console.log('[forgotPassword] Method 4: Testing basic update capability...');\n        try {\n          const testResult = await Meteor.users.updateAsync(targetUser._id, {\n            $set: {\n              'profile.passwordResetTest': new Date()\n            }\n          });\n          console.log('[forgotPassword] Basic update test result:', testResult);\n\n          if (testResult === 1) {\n            console.log('[forgotPassword] Basic updates work, trying password with $unset first...');\n            // Try unsetting and then setting\n            await Meteor.users.updateAsync(targetUser._id, {\n              $unset: { 'services.password': '' }\n            });\n\n            updateResult = await Meteor.users.updateAsync(targetUser._id, {\n              $set: {\n                'services.password': {\n                  bcrypt: hashedPassword\n                }\n              }\n            });\n            console.log('[forgotPassword] Unset/Set method result:', updateResult);\n            if (updateResult === 1) {\n              successMethod = 'Method 4: unset then set';\n            }\n          }\n        } catch (method4Error) {\n          console.error('[forgotPassword] Method 4 error:', method4Error);\n        }\n      }\n\n      if (updateResult === 1) {\n        console.log(`[forgotPassword] Password update successful using: ${successMethod}`);\n\n        // Verify the update worked\n        const updatedUser = await Meteor.users.findOneAsync(targetUser._id);\n        console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));\n\n        // Test password verification\n        if (updatedUser.services?.password?.bcrypt) {\n          const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);\n          console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');\n        }\n\n        return { success: true, message: 'Password updated successfully' };\n      } else {\n        console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);\n\n        // Log user permissions and collection info\n        console.log('[forgotPassword] User ID:', targetUser._id);\n        console.log('[forgotPassword] User ID type:', typeof targetUser._id);\n        console.log('[forgotPassword] Current user exists:', !!currentUser);\n        console.log('[forgotPassword] User roles:', currentUser.roles);\n\n        throw new Meteor.Error('password-update-failed', 'Failed to update password in database');\n      }\n\n    } catch (error) {\n      console.error('[forgotPassword] Error during password update:', error);\n      throw new Meteor.Error('password-update-failed', `Failed to update password: ${error.message}`);\n    }\n  },\n\n  async 'users.debugUser'({ email }) {\n    try {\n      check(email, String);\n\n      console.log('[debugUser] Debugging user:', email);\n\n      // Find user using async method\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        console.log('[debugUser] User not found');\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[debugUser] User found:', user._id);\n\n      // Get full user document using async method\n      const fullUser = await Meteor.users.findOneAsync(user._id);\n      console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));\n\n      if (!fullUser) {\n        console.log('[debugUser] Full user document not found');\n        return { success: false, error: 'Full user document not found' };\n      }\n\n      // Test basic update using async method\n      let testUpdateResult = null;\n      try {\n        testUpdateResult = await Meteor.users.updateAsync(user._id, {\n          $set: { 'profile.debugTest': new Date() }\n        });\n        console.log('[debugUser] Test update result:', testUpdateResult);\n      } catch (updateError) {\n        console.error('[debugUser] Test update error:', updateError);\n      }\n\n      // Try using Accounts.setPassword if available\n      let hasSetPassword = false;\n      try {\n        hasSetPassword = typeof Accounts.setPassword === 'function';\n        console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);\n      } catch (setPasswordError) {\n        console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);\n      }\n\n      const result = {\n        success: true,\n        userId: user._id,\n        userIdType: typeof user._id,\n        hasServices: !!fullUser.services,\n        hasPassword: !!(fullUser.services?.password),\n        hasBcrypt: !!(fullUser.services?.password?.bcrypt),\n        roles: fullUser.roles || [],\n        profile: fullUser.profile || {},\n        testUpdateResult: testUpdateResult,\n        hasSetPassword: hasSetPassword,\n        servicesStructure: fullUser.services || {}\n      };\n\n      console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));\n      return result;\n\n    } catch (error) {\n      console.error('[debugUser] Error in debug method:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.testLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[testLogin] Testing login for email:', email);\n\n    // Find user using async method\n    const user = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!user) {\n      console.log('[testLogin] User not found');\n      return { success: false, error: 'User not found' };\n    }\n\n    console.log('[testLogin] User found:', user._id);\n    console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));\n\n    // Test password verification\n    try {\n      // Check if password service exists\n      if (!user.services || !user.services.password || !user.services.password.bcrypt) {\n        console.log('[testLogin] No password hash found in user services');\n        return {\n          success: false,\n          error: 'No password hash found',\n          userId: user._id,\n          services: user.services\n        };\n      }\n\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services.password.bcrypt;\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n\n      console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n      console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n      console.log('[testLogin] Password length:', password.length);\n\n      return {\n        success: passwordMatch,\n        userId: user._id,\n        hashPreview: storedHash.substring(0, 20) + '...',\n        passwordLength: password.length\n      };\n    } catch (error) {\n      console.error('[testLogin] Error during password test:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.comparePasswordFormats'({ email }) {\n    check(email, String);\n\n    console.log('[comparePasswordFormats] Checking password format for:', email);\n\n    // Find user\n    const user = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!user) {\n      return { success: false, error: 'User not found' };\n    }\n\n    console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));\n\n    // Check if user has password\n    if (!user.services?.password?.bcrypt) {\n      return { success: false, error: 'No password found' };\n    }\n\n    const storedHash = user.services.password.bcrypt;\n    console.log('[comparePasswordFormats] Stored hash:', storedHash);\n    console.log('[comparePasswordFormats] Hash length:', storedHash.length);\n    console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));\n\n    // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)\n    const isBcrypt = /^\\$2[aby]\\$/.test(storedHash);\n    console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);\n\n    return {\n      success: true,\n      userId: user._id,\n      hashLength: storedHash.length,\n      hashPreview: storedHash.substring(0, 20) + '...',\n      isBcryptFormat: isBcrypt,\n      fullServices: user.services\n    };\n  },\n\n  async 'users.testActualLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[testActualLogin] Testing actual login for:', email);\n\n    try {\n      // Try to simulate what Meteor.loginWithPassword does\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        console.log('[testActualLogin] User not found');\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[testActualLogin] User found:', user._id);\n      console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));\n\n      // Check if password service exists\n      if (!user.services?.password?.bcrypt) {\n        console.log('[testActualLogin] No password hash found');\n        return { success: false, error: 'No password hash found' };\n      }\n\n      // Test bcrypt verification\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services.password.bcrypt;\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n\n      console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n      console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n\n      // Check if user has any login restrictions\n      const isVerified = user.emails?.[0]?.verified || false;\n      console.log('[testActualLogin] Email verified:', isVerified);\n\n      // Check user roles\n      console.log('[testActualLogin] User roles:', user.roles);\n\n      // Try to create a login token manually to test if that works\n      let loginTokenTest = null;\n      try {\n        // This is what Meteor does internally for login\n        const stampedToken = Accounts._generateStampedLoginToken();\n        console.log('[testActualLogin] Generated login token:', !!stampedToken);\n        loginTokenTest = 'Token generation successful';\n      } catch (tokenError) {\n        console.error('[testActualLogin] Token generation error:', tokenError);\n        loginTokenTest = tokenError.message;\n      }\n\n      return {\n        success: passwordMatch,\n        userId: user._id,\n        passwordVerification: passwordMatch,\n        emailVerified: isVerified,\n        userRoles: user.roles,\n        hashPreview: storedHash.substring(0, 20) + '...',\n        loginTokenTest: loginTokenTest,\n        fullUserStructure: {\n          _id: user._id,\n          emails: user.emails,\n          services: user.services,\n          roles: user.roles,\n          profile: user.profile\n        }\n      };\n\n    } catch (error) {\n      console.error('[testActualLogin] Error:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.simulateLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[simulateLogin] Simulating login for:', email);\n\n    try {\n      // Find user\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[simulateLogin] User found:', user._id);\n\n      // Check password\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services?.password?.bcrypt;\n\n      if (!storedHash) {\n        return { success: false, error: 'No password hash found' };\n      }\n\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n      console.log('[simulateLogin] Password match:', passwordMatch);\n\n      if (!passwordMatch) {\n        return { success: false, error: 'Invalid password' };\n      }\n\n      // Check if there are any login restrictions\n      const restrictions = [];\n\n      // Check email verification requirement\n      const emailVerified = user.emails?.[0]?.verified || false;\n      if (!emailVerified) {\n        restrictions.push('Email not verified');\n      }\n\n      // Check if user is active (no disabled flag)\n      if (user.disabled) {\n        restrictions.push('User account disabled');\n      }\n\n      // Check roles\n      if (!user.roles || user.roles.length === 0) {\n        restrictions.push('No roles assigned');\n      }\n\n      console.log('[simulateLogin] Login restrictions:', restrictions);\n\n      // Try to manually create what loginWithPassword would do\n      let loginSimulation = 'Not attempted';\n      try {\n        // Check if we can generate a login token\n        const stampedToken = Accounts._generateStampedLoginToken();\n        if (stampedToken) {\n          loginSimulation = 'Login token generation successful';\n        }\n      } catch (tokenError) {\n        loginSimulation = `Token error: ${tokenError.message}`;\n      }\n\n      return {\n        success: passwordMatch && restrictions.length === 0,\n        userId: user._id,\n        passwordMatch: passwordMatch,\n        emailVerified: emailVerified,\n        restrictions: restrictions,\n        loginSimulation: loginSimulation,\n        userStructure: {\n          _id: user._id,\n          emails: user.emails,\n          roles: user.roles,\n          profile: user.profile,\n          disabled: user.disabled || false\n        }\n      };\n\n    } catch (error) {\n      console.error('[simulateLogin] Error:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.checkAndFixAdminRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      console.log('[checkAndFixAdminRole] Checking user:', {\n        id: user?._id,\n        email: user?.emails?.[0]?.address,\n        roles: user?.roles\n      });\n      \n      // If user has no roles array, initialize it\n      if (!user.roles) {\n        await Meteor.users.updateAsync(this.userId, {\n          $set: { roles: ['team-member'] }\n        });\n        return 'Roles initialized';\n      }\n      \n      // If user has no roles or doesn't have admin role\n      if (!user.roles.includes('admin')) {\n        console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n        \n        // Check if this is the first user (they should be admin)\n        const totalUsers = await Meteor.users.find().countAsync();\n        if (totalUsers === 1) {\n          console.log('[checkAndFixAdminRole] First user, setting as admin');\n          await Meteor.users.updateAsync(this.userId, {\n            $set: { roles: ['admin'] }\n          });\n          return 'Admin role added';\n        }\n        return 'User is not admin';\n      }\n      \n      return 'User is already admin';\n    } catch (error) {\n      console.error('[checkAndFixAdminRole] Error:', error);\n      throw new Meteor.Error('check-role-failed', error.message);\n    }\n  },\n\n  async 'users.diagnoseRoles'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n\n    try {\n      const currentUser = await Meteor.users.findOneAsync(this.userId);\n      if (!currentUser.roles?.includes('admin')) {\n        throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n      }\n\n      const allUsers = await Meteor.users.find().fetchAsync();\n      const usersWithIssues = [];\n      const fixes = [];\n\n      for (const user of allUsers) {\n        const issues = [];\n        \n        // Check if roles array exists\n        if (!user.roles || !Array.isArray(user.roles)) {\n          issues.push('No roles array');\n          // Fix: Initialize roles based on profile\n          const role = user.profile?.role || 'team-member';\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [role] }\n          });\n          fixes.push(`Initialized roles for ${user.emails?.[0]?.address}`);\n        }\n        \n        // Check if role matches profile\n        if (user.profile?.role && user.roles?.[0] !== user.profile.role) {\n          issues.push('Role mismatch with profile');\n          // Fix: Update roles to match profile\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [user.profile.role] }\n          });\n          fixes.push(`Fixed role mismatch for ${user.emails?.[0]?.address}`);\n        }\n\n        if (issues.length > 0) {\n          usersWithIssues.push({\n            email: user.emails?.[0]?.address,\n            issues\n          });\n        }\n      }\n\n      return {\n        usersWithIssues,\n        fixes,\n        message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n      };\n    } catch (error) {\n      throw new Meteor.Error('diagnose-failed', error.message);\n    }\n  },\n\n  'users.createTestTeamMember'() {\n    // Only allow in development\n    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n      try {\n        const testMember = {\n          email: '<EMAIL>',\n          password: 'TestPass123!',\n          firstName: 'Test',\n          lastName: 'Member'\n        };\n\n        const userId = Accounts.createUser({\n          email: testMember.email,\n          password: testMember.password,\n          profile: {\n            firstName: testMember.firstName,\n            lastName: testMember.lastName,\n            role: 'team-member',\n            fullName: `${testMember.firstName} ${testMember.lastName}`\n          }\n        });\n\n        // Set the role explicitly\n        Meteor.users.update(userId, {\n          $set: { roles: ['team-member'] }\n        });\n\n        return {\n          success: true,\n          userId,\n          message: 'Test team member created successfully'\n        };\n      } catch (error) {\n        console.error('[createTestTeamMember] Error:', error);\n        throw new Meteor.Error('create-test-member-failed', error.message);\n      }\n    } else {\n      throw new Meteor.Error('not-development', 'This method is only available in development');\n    }\n  }\n});"]}}]