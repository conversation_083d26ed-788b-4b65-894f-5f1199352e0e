{"name": "smart-task-management-system", "private": true, "scripts": {"start": "meteor run --settings settings.json", "dev": "meteor run --settings settings.json --hot"}, "dependencies": {"@babel/runtime": "^7.20.7", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.13.0", "bcrypt": "^5.1.0", "chart.js": "^4.4.9", "formik": "^2.4.6", "meteor-node-stubs": "^1.2.5", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.0", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@types/meteor": "^2.9.2", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.1"}, "meteor": {"mainModule": {"client": "client/main.jsx", "server": "server/main.js"}, "testModule": "tests/main.js"}}