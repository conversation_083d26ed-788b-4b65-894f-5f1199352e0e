{"version": 3, "sources": ["meteor://💻app/packages/ejson/ejson.js", "meteor://💻app/packages/ejson/stringify.js", "meteor://💻app/packages/ejson/utils.js"], "names": ["module", "export", "EJSON", "isFunction", "isObject", "keysOf", "lengthOf", "hasOwn", "convertMapToObject", "isArguments", "isInfOrNaN", "handleError", "link", "v", "__reifyWaitForDeps__", "customTypes", "Map", "addType", "name", "factory", "has", "Error", "concat", "set", "builtinConverters", "matchJSONValue", "obj", "matchObject", "Date", "toJSONValue", "$date", "getTime", "fromJSONValue", "RegExp", "regexp", "$regexp", "source", "$flags", "flags", "slice", "replace", "sign", "Number", "isNaN", "Infinity", "$InfNaN", "Uint8Array", "$binary", "Base64", "encode", "decode", "match", "keyCount", "some", "converter", "newObj", "for<PERSON>ach", "key", "$escape", "_isCustomType", "jsonValue", "Meteor", "_noYieldsAllowed", "$type", "typeName", "$value", "get", "_getTypes", "isOriginal", "arguments", "length", "undefined", "_getConverters", "toJSONValueHelper", "item", "i", "adjustTypesToJSONValue", "maybeChanged", "value", "changed", "_adjustTypesToJSONValue", "newItem", "clone", "fromJSONValueHelper", "keys", "every", "k", "substr", "adjustTypesFromJSONValue", "_adjustTypesFromJSONValue", "stringify", "options", "serialized", "json", "canonical", "indent", "canonicalStringify", "default", "JSON", "parse", "isBinary", "$Uint8ArrayPolyfill", "equals", "a", "b", "keyOrderSensitive", "valueOf", "aIsArray", "Array", "isArray", "bIsArray", "ret", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "newBinary", "map", "from", "__reify_async_result__", "_reifyError", "self", "async", "quote", "string", "str", "holder", "singleIndent", "outerIndent", "isFinite", "String", "innerIndent", "partial", "hasOwnProperty", "call", "join", "Object", "sort", "push", "allOptions", "assign", "newIndent", "exportDefault", "checkError", "fn", "prop", "prototype", "reduce", "acc", "_ref", "maxStack", "msgError", "test", "apply", "error", "isMaxStack", "message"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,KAAK,EAACA,CAAA,KAAIA;IAAK,CAAC,CAAC;IAAC,IAAIC,UAAU,EAACC,QAAQ,EAACC,MAAM,EAACC,QAAQ,EAACC,MAAM,EAACC,kBAAkB,EAACC,WAAW,EAACC,UAAU,EAACC,WAAW;IAACX,MAAM,CAACY,IAAI,CAAC,SAAS,EAAC;MAACT,UAAUA,CAACU,CAAC,EAAC;QAACV,UAAU,GAACU,CAAC;MAAA,CAAC;MAACT,QAAQA,CAACS,CAAC,EAAC;QAACT,QAAQ,GAACS,CAAC;MAAA,CAAC;MAACR,MAAMA,CAACQ,CAAC,EAAC;QAACR,MAAM,GAACQ,CAAC;MAAA,CAAC;MAACP,QAAQA,CAACO,CAAC,EAAC;QAACP,QAAQ,GAACO,CAAC;MAAA,CAAC;MAACN,MAAMA,CAACM,CAAC,EAAC;QAACN,MAAM,GAACM,CAAC;MAAA,CAAC;MAACL,kBAAkBA,CAACK,CAAC,EAAC;QAACL,kBAAkB,GAACK,CAAC;MAAA,CAAC;MAACJ,WAAWA,CAACI,CAAC,EAAC;QAACJ,WAAW,GAACI,CAAC;MAAA,CAAC;MAACH,UAAUA,CAACG,CAAC,EAAC;QAACH,UAAU,GAACG,CAAC;MAAA,CAAC;MAACF,WAAWA,CAACE,CAAC,EAAC;QAACF,WAAW,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAYrd;AACA;AACA;AACA;IACA,MAAMZ,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA,MAAMa,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;;IAE7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAd,KAAK,CAACe,OAAO,GAAG,CAACC,IAAI,EAAEC,OAAO,KAAK;MACjC,IAAIJ,WAAW,CAACK,GAAG,CAACF,IAAI,CAAC,EAAE;QACzB,MAAM,IAAIG,KAAK,SAAAC,MAAA,CAASJ,IAAI,qBAAkB,CAAC;MACjD;MACAH,WAAW,CAACQ,GAAG,CAACL,IAAI,EAAEC,OAAO,CAAC;IAChC,CAAC;IAED,MAAMK,iBAAiB,GAAG,CACxB;MAAE;MACAC,cAAcA,CAACC,GAAG,EAAE;QAClB,OAAOnB,MAAM,CAACmB,GAAG,EAAE,OAAO,CAAC,IAAIpB,QAAQ,CAACoB,GAAG,CAAC,KAAK,CAAC;MACpD,CAAC;MACDC,WAAWA,CAACD,GAAG,EAAE;QACf,OAAOA,GAAG,YAAYE,IAAI;MAC5B,CAAC;MACDC,WAAWA,CAACH,GAAG,EAAE;QACf,OAAO;UAACI,KAAK,EAAEJ,GAAG,CAACK,OAAO,CAAC;QAAC,CAAC;MAC/B,CAAC;MACDC,aAAaA,CAACN,GAAG,EAAE;QACjB,OAAO,IAAIE,IAAI,CAACF,GAAG,CAACI,KAAK,CAAC;MAC5B;IACF,CAAC,EACD;MAAE;MACAL,cAAcA,CAACC,GAAG,EAAE;QAClB,OAAOnB,MAAM,CAACmB,GAAG,EAAE,SAAS,CAAC,IACxBnB,MAAM,CAACmB,GAAG,EAAE,QAAQ,CAAC,IACrBpB,QAAQ,CAACoB,GAAG,CAAC,KAAK,CAAC;MAC1B,CAAC;MACDC,WAAWA,CAACD,GAAG,EAAE;QACf,OAAOA,GAAG,YAAYO,MAAM;MAC9B,CAAC;MACDJ,WAAWA,CAACK,MAAM,EAAE;QAClB,OAAO;UACLC,OAAO,EAAED,MAAM,CAACE,MAAM;UACtBC,MAAM,EAAEH,MAAM,CAACI;QACjB,CAAC;MACH,CAAC;MACDN,aAAaA,CAACN,GAAG,EAAE;QACjB;QACA,OAAO,IAAIO,MAAM,CACfP,GAAG,CAACS,OAAO,EACXT,GAAG,CAACW;QACF;QAAA,CACCE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZC,OAAO,CAAC,WAAW,EAAC,EAAE,CAAC,CACvBA,OAAO,CAAC,cAAc,EAAE,EAAE,CAC/B,CAAC;MACH;IACF,CAAC,EACD;MAAE;MACA;MACAf,cAAcA,CAACC,GAAG,EAAE;QAClB,OAAOnB,MAAM,CAACmB,GAAG,EAAE,SAAS,CAAC,IAAIpB,QAAQ,CAACoB,GAAG,CAAC,KAAK,CAAC;MACtD,CAAC;MACDC,WAAW,EAAEjB,UAAU;MACvBmB,WAAWA,CAACH,GAAG,EAAE;QACf,IAAIe,IAAI;QACR,IAAIC,MAAM,CAACC,KAAK,CAACjB,GAAG,CAAC,EAAE;UACrBe,IAAI,GAAG,CAAC;QACV,CAAC,MAAM,IAAIf,GAAG,KAAKkB,QAAQ,EAAE;UAC3BH,IAAI,GAAG,CAAC;QACV,CAAC,MAAM;UACLA,IAAI,GAAG,CAAC,CAAC;QACX;QACA,OAAO;UAACI,OAAO,EAAEJ;QAAI,CAAC;MACxB,CAAC;MACDT,aAAaA,CAACN,GAAG,EAAE;QACjB,OAAOA,GAAG,CAACmB,OAAO,GAAG,CAAC;MACxB;IACF,CAAC,EACD;MAAE;MACApB,cAAcA,CAACC,GAAG,EAAE;QAClB,OAAOnB,MAAM,CAACmB,GAAG,EAAE,SAAS,CAAC,IAAIpB,QAAQ,CAACoB,GAAG,CAAC,KAAK,CAAC;MACtD,CAAC;MACDC,WAAWA,CAACD,GAAG,EAAE;QACf,OAAO,OAAOoB,UAAU,KAAK,WAAW,IAAIpB,GAAG,YAAYoB,UAAU,IAC/DpB,GAAG,IAAInB,MAAM,CAACmB,GAAG,EAAE,qBAAqB,CAAE;MAClD,CAAC;MACDG,WAAWA,CAACH,GAAG,EAAE;QACf,OAAO;UAACqB,OAAO,EAAEC,MAAM,CAACC,MAAM,CAACvB,GAAG;QAAC,CAAC;MACtC,CAAC;MACDM,aAAaA,CAACN,GAAG,EAAE;QACjB,OAAOsB,MAAM,CAACE,MAAM,CAACxB,GAAG,CAACqB,OAAO,CAAC;MACnC;IACF,CAAC,EACD;MAAE;MACAtB,cAAcA,CAACC,GAAG,EAAE;QAClB,OAAOnB,MAAM,CAACmB,GAAG,EAAE,SAAS,CAAC,IAAIpB,QAAQ,CAACoB,GAAG,CAAC,KAAK,CAAC;MACtD,CAAC;MACDC,WAAWA,CAACD,GAAG,EAAE;QACf,IAAIyB,KAAK,GAAG,KAAK;QACjB,IAAIzB,GAAG,EAAE;UACP,MAAM0B,QAAQ,GAAG9C,QAAQ,CAACoB,GAAG,CAAC;UAC9B,IAAI0B,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC,EAAE;YACpCD,KAAK,GACH3B,iBAAiB,CAAC6B,IAAI,CAACC,SAAS,IAAIA,SAAS,CAAC7B,cAAc,CAACC,GAAG,CAAC,CAAC;UACtE;QACF;QACA,OAAOyB,KAAK;MACd,CAAC;MACDtB,WAAWA,CAACH,GAAG,EAAE;QACf,MAAM6B,MAAM,GAAG,CAAC,CAAC;QACjBlD,MAAM,CAACqB,GAAG,CAAC,CAAC8B,OAAO,CAACC,GAAG,IAAI;UACzBF,MAAM,CAACE,GAAG,CAAC,GAAGvD,KAAK,CAAC2B,WAAW,CAACH,GAAG,CAAC+B,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC;QACF,OAAO;UAACC,OAAO,EAAEH;QAAM,CAAC;MAC1B,CAAC;MACDvB,aAAaA,CAACN,GAAG,EAAE;QACjB,MAAM6B,MAAM,GAAG,CAAC,CAAC;QACjBlD,MAAM,CAACqB,GAAG,CAACgC,OAAO,CAAC,CAACF,OAAO,CAACC,GAAG,IAAI;UACjCF,MAAM,CAACE,GAAG,CAAC,GAAGvD,KAAK,CAAC8B,aAAa,CAACN,GAAG,CAACgC,OAAO,CAACD,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC;QACF,OAAOF,MAAM;MACf;IACF,CAAC,EACD;MAAE;MACA9B,cAAcA,CAACC,GAAG,EAAE;QAClB,OAAOnB,MAAM,CAACmB,GAAG,EAAE,OAAO,CAAC,IACtBnB,MAAM,CAACmB,GAAG,EAAE,QAAQ,CAAC,IAAIpB,QAAQ,CAACoB,GAAG,CAAC,KAAK,CAAC;MACnD,CAAC;MACDC,WAAWA,CAACD,GAAG,EAAE;QACf,OAAOxB,KAAK,CAACyD,aAAa,CAACjC,GAAG,CAAC;MACjC,CAAC;MACDG,WAAWA,CAACH,GAAG,EAAE;QACf,MAAMkC,SAAS,GAAGC,MAAM,CAACC,gBAAgB,CAAC,MAAMpC,GAAG,CAACG,WAAW,CAAC,CAAC,CAAC;QAClE,OAAO;UAACkC,KAAK,EAAErC,GAAG,CAACsC,QAAQ,CAAC,CAAC;UAAEC,MAAM,EAAEL;QAAS,CAAC;MACnD,CAAC;MACD5B,aAAaA,CAACN,GAAG,EAAE;QACjB,MAAMsC,QAAQ,GAAGtC,GAAG,CAACqC,KAAK;QAC1B,IAAI,CAAChD,WAAW,CAACK,GAAG,CAAC4C,QAAQ,CAAC,EAAE;UAC9B,MAAM,IAAI3C,KAAK,sBAAAC,MAAA,CAAsB0C,QAAQ,oBAAiB,CAAC;QACjE;QACA,MAAMV,SAAS,GAAGvC,WAAW,CAACmD,GAAG,CAACF,QAAQ,CAAC;QAC3C,OAAOH,MAAM,CAACC,gBAAgB,CAAC,MAAMR,SAAS,CAAC5B,GAAG,CAACuC,MAAM,CAAC,CAAC;MAC7D;IACF,CAAC,CACF;IAED/D,KAAK,CAACyD,aAAa,GAAIjC,GAAG,IACxBA,GAAG,IACHvB,UAAU,CAACuB,GAAG,CAACG,WAAW,CAAC,IAC3B1B,UAAU,CAACuB,GAAG,CAACsC,QAAQ,CAAC,IACxBjD,WAAW,CAACK,GAAG,CAACM,GAAG,CAACsC,QAAQ,CAAC,CAAC,CAC/B;IAED9D,KAAK,CAACiE,SAAS,GAAG;MAAA,IAACC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAAA,OAAMD,UAAU,GAAGrD,WAAW,GAAGP,kBAAkB,CAACO,WAAW,CAAC;IAAA,CAAC;IAEtGb,KAAK,CAACsE,cAAc,GAAG,MAAMhD,iBAAiB;;IAE9C;IACA;IACA,MAAMiD,iBAAiB,GAAGC,IAAI,IAAI;MAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnD,iBAAiB,CAAC8C,MAAM,EAAEK,CAAC,EAAE,EAAE;QACjD,MAAMrB,SAAS,GAAG9B,iBAAiB,CAACmD,CAAC,CAAC;QACtC,IAAIrB,SAAS,CAAC3B,WAAW,CAAC+C,IAAI,CAAC,EAAE;UAC/B,OAAOpB,SAAS,CAACzB,WAAW,CAAC6C,IAAI,CAAC;QACpC;MACF;MACA,OAAOH,SAAS;IAClB,CAAC;;IAED;IACA,MAAMK,sBAAsB,GAAGlD,GAAG,IAAI;MACpC;MACA,IAAIA,GAAG,KAAK,IAAI,EAAE;QAChB,OAAO,IAAI;MACb;MAEA,MAAMmD,YAAY,GAAGJ,iBAAiB,CAAC/C,GAAG,CAAC;MAC3C,IAAImD,YAAY,KAAKN,SAAS,EAAE;QAC9B,OAAOM,YAAY;MACrB;;MAEA;MACA,IAAI,CAACzE,QAAQ,CAACsB,GAAG,CAAC,EAAE;QAClB,OAAOA,GAAG;MACZ;;MAEA;MACArB,MAAM,CAACqB,GAAG,CAAC,CAAC8B,OAAO,CAACC,GAAG,IAAI;QACzB,MAAMqB,KAAK,GAAGpD,GAAG,CAAC+B,GAAG,CAAC;QACtB,IAAI,CAACrD,QAAQ,CAAC0E,KAAK,CAAC,IAAIA,KAAK,KAAKP,SAAS,IACvC,CAAC7D,UAAU,CAACoE,KAAK,CAAC,EAAE;UACtB,OAAO,CAAC;QACV;QAEA,MAAMC,OAAO,GAAGN,iBAAiB,CAACK,KAAK,CAAC;QACxC,IAAIC,OAAO,EAAE;UACXrD,GAAG,CAAC+B,GAAG,CAAC,GAAGsB,OAAO;UAClB,OAAO,CAAC;QACV;QACA;QACA;QACAH,sBAAsB,CAACE,KAAK,CAAC;MAC/B,CAAC,CAAC;MACF,OAAOpD,GAAG;IACZ,CAAC;IAEDxB,KAAK,CAAC8E,uBAAuB,GAAGJ,sBAAsB;;IAEtD;AACA;AACA;AACA;AACA;AACA;IACA1E,KAAK,CAAC2B,WAAW,GAAG6C,IAAI,IAAI;MAC1B,MAAMK,OAAO,GAAGN,iBAAiB,CAACC,IAAI,CAAC;MACvC,IAAIK,OAAO,KAAKR,SAAS,EAAE;QACzB,OAAOQ,OAAO;MAChB;MAEA,IAAIE,OAAO,GAAGP,IAAI;MAClB,IAAItE,QAAQ,CAACsE,IAAI,CAAC,EAAE;QAClBO,OAAO,GAAG/E,KAAK,CAACgF,KAAK,CAACR,IAAI,CAAC;QAC3BE,sBAAsB,CAACK,OAAO,CAAC;MACjC;MACA,OAAOA,OAAO;IAChB,CAAC;;IAED;IACA;IACA;IACA;IACA,MAAME,mBAAmB,GAAGL,KAAK,IAAI;MACnC,IAAI1E,QAAQ,CAAC0E,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,EAAE;QACrC,MAAMM,IAAI,GAAG/E,MAAM,CAACyE,KAAK,CAAC;QAC1B,IAAIM,IAAI,CAACd,MAAM,IAAI,CAAC,IACbc,IAAI,CAACC,KAAK,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;UACvE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnD,iBAAiB,CAAC8C,MAAM,EAAEK,CAAC,EAAE,EAAE;YACjD,MAAMrB,SAAS,GAAG9B,iBAAiB,CAACmD,CAAC,CAAC;YACtC,IAAIrB,SAAS,CAAC7B,cAAc,CAACqD,KAAK,CAAC,EAAE;cACnC,OAAOxB,SAAS,CAACtB,aAAa,CAAC8C,KAAK,CAAC;YACvC;UACF;QACF;MACF;MACA,OAAOA,KAAK;IACd,CAAC;;IAED;IACA;IACA;IACA,MAAMU,wBAAwB,GAAG9D,GAAG,IAAI;MACtC,IAAIA,GAAG,KAAK,IAAI,EAAE;QAChB,OAAO,IAAI;MACb;MAEA,MAAMmD,YAAY,GAAGM,mBAAmB,CAACzD,GAAG,CAAC;MAC7C,IAAImD,YAAY,KAAKnD,GAAG,EAAE;QACxB,OAAOmD,YAAY;MACrB;;MAEA;MACA,IAAI,CAACzE,QAAQ,CAACsB,GAAG,CAAC,EAAE;QAClB,OAAOA,GAAG;MACZ;MAEArB,MAAM,CAACqB,GAAG,CAAC,CAAC8B,OAAO,CAACC,GAAG,IAAI;QACzB,MAAMqB,KAAK,GAAGpD,GAAG,CAAC+B,GAAG,CAAC;QACtB,IAAIrD,QAAQ,CAAC0E,KAAK,CAAC,EAAE;UACnB,MAAMC,OAAO,GAAGI,mBAAmB,CAACL,KAAK,CAAC;UAC1C,IAAIA,KAAK,KAAKC,OAAO,EAAE;YACrBrD,GAAG,CAAC+B,GAAG,CAAC,GAAGsB,OAAO;YAClB;UACF;UACA;UACA;UACAS,wBAAwB,CAACV,KAAK,CAAC;QACjC;MACF,CAAC,CAAC;MACF,OAAOpD,GAAG;IACZ,CAAC;IAEDxB,KAAK,CAACuF,yBAAyB,GAAGD,wBAAwB;;IAE1D;AACA;AACA;AACA;AACA;IACAtF,KAAK,CAAC8B,aAAa,GAAG0C,IAAI,IAAI;MAC5B,IAAIK,OAAO,GAAGI,mBAAmB,CAACT,IAAI,CAAC;MACvC,IAAIK,OAAO,KAAKL,IAAI,IAAItE,QAAQ,CAACsE,IAAI,CAAC,EAAE;QACtCK,OAAO,GAAG7E,KAAK,CAACgF,KAAK,CAACR,IAAI,CAAC;QAC3Bc,wBAAwB,CAACT,OAAO,CAAC;MACnC;MACA,OAAOA,OAAO;IAChB,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA7E,KAAK,CAACwF,SAAS,GAAG/E,WAAW,CAAC,CAAC+D,IAAI,EAAEiB,OAAO,KAAK;MAC/C,IAAIC,UAAU;MACd,MAAMC,IAAI,GAAG3F,KAAK,CAAC2B,WAAW,CAAC6C,IAAI,CAAC;MACpC,IAAIiB,OAAO,KAAKA,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,MAAM,CAAC,EAAE;QA5YxD,IAAIC,kBAAkB;QAAChG,MAAM,CAACY,IAAI,CAAC,aAAa,EAAC;UAACqF,OAAOA,CAACpF,CAAC,EAAC;YAACmF,kBAAkB,GAACnF,CAAC;UAAA;QAAC,CAAC,EAAC,CAAC,CAAC;QA8YlF+E,UAAU,GAAGI,kBAAkB,CAACH,IAAI,EAAEF,OAAO,CAAC;MAChD,CAAC,MAAM;QACLC,UAAU,GAAGM,IAAI,CAACR,SAAS,CAACG,IAAI,CAAC;MACnC;MACA,OAAOD,UAAU;IACnB,CAAC,CAAC;;IAEF;AACA;AACA;AACA;AACA;AACA;IACA1F,KAAK,CAACiG,KAAK,GAAGzB,IAAI,IAAI;MACpB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5B,MAAM,IAAIrD,KAAK,CAAC,yCAAyC,CAAC;MAC5D;MACA,OAAOnB,KAAK,CAAC8B,aAAa,CAACkE,IAAI,CAACC,KAAK,CAACzB,IAAI,CAAC,CAAC;IAC9C,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;IACAxE,KAAK,CAACkG,QAAQ,GAAG1E,GAAG,IAAI;MACtB,OAAO,CAAC,EAAG,OAAOoB,UAAU,KAAK,WAAW,IAAIpB,GAAG,YAAYoB,UAAU,IACtEpB,GAAG,IAAIA,GAAG,CAAC2E,mBAAoB,CAAC;IACrC,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAnG,KAAK,CAACoG,MAAM,GAAG,CAACC,CAAC,EAAEC,CAAC,EAAEb,OAAO,KAAK;MAChC,IAAIhB,CAAC;MACL,MAAM8B,iBAAiB,GAAG,CAAC,EAAEd,OAAO,IAAIA,OAAO,CAACc,iBAAiB,CAAC;MAClE,IAAIF,CAAC,KAAKC,CAAC,EAAE;QACX,OAAO,IAAI;MACb;;MAEA;MACA;MACA,IAAI9D,MAAM,CAACC,KAAK,CAAC4D,CAAC,CAAC,IAAI7D,MAAM,CAACC,KAAK,CAAC6D,CAAC,CAAC,EAAE;QACtC,OAAO,IAAI;MACb;;MAEA;MACA,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;QACZ,OAAO,KAAK;MACd;MAEA,IAAI,EAAEpG,QAAQ,CAACmG,CAAC,CAAC,IAAInG,QAAQ,CAACoG,CAAC,CAAC,CAAC,EAAE;QACjC,OAAO,KAAK;MACd;MAEA,IAAID,CAAC,YAAY3E,IAAI,IAAI4E,CAAC,YAAY5E,IAAI,EAAE;QAC1C,OAAO2E,CAAC,CAACG,OAAO,CAAC,CAAC,KAAKF,CAAC,CAACE,OAAO,CAAC,CAAC;MACpC;MAEA,IAAIxG,KAAK,CAACkG,QAAQ,CAACG,CAAC,CAAC,IAAIrG,KAAK,CAACkG,QAAQ,CAACI,CAAC,CAAC,EAAE;QAC1C,IAAID,CAAC,CAACjC,MAAM,KAAKkC,CAAC,CAAClC,MAAM,EAAE;UACzB,OAAO,KAAK;QACd;QACA,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,CAAC,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;UAC7B,IAAI4B,CAAC,CAAC5B,CAAC,CAAC,KAAK6B,CAAC,CAAC7B,CAAC,CAAC,EAAE;YACjB,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;MAEA,IAAIxE,UAAU,CAACoG,CAAC,CAACD,MAAM,CAAC,EAAE;QACxB,OAAOC,CAAC,CAACD,MAAM,CAACE,CAAC,EAAEb,OAAO,CAAC;MAC7B;MAEA,IAAIxF,UAAU,CAACqG,CAAC,CAACF,MAAM,CAAC,EAAE;QACxB,OAAOE,CAAC,CAACF,MAAM,CAACC,CAAC,EAAEZ,OAAO,CAAC;MAC7B;;MAEA;MACA,MAAMgB,QAAQ,GAAGC,KAAK,CAACC,OAAO,CAACN,CAAC,CAAC;MACjC,MAAMO,QAAQ,GAAGF,KAAK,CAACC,OAAO,CAACL,CAAC,CAAC;;MAEjC;MACA,IAAIG,QAAQ,KAAKG,QAAQ,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAIH,QAAQ,IAAIG,QAAQ,EAAE;QACxB,IAAIP,CAAC,CAACjC,MAAM,KAAKkC,CAAC,CAAClC,MAAM,EAAE;UACzB,OAAO,KAAK;QACd;QACA,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,CAAC,CAACjC,MAAM,EAAEK,CAAC,EAAE,EAAE;UAC7B,IAAI,CAACzE,KAAK,CAACoG,MAAM,CAACC,CAAC,CAAC5B,CAAC,CAAC,EAAE6B,CAAC,CAAC7B,CAAC,CAAC,EAAEgB,OAAO,CAAC,EAAE;YACtC,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;;MAEA;MACA,QAAQzF,KAAK,CAACyD,aAAa,CAAC4C,CAAC,CAAC,GAAGrG,KAAK,CAACyD,aAAa,CAAC6C,CAAC,CAAC;QACrD,KAAK,CAAC;UAAE,OAAO,KAAK;QACpB,KAAK,CAAC;UAAE,OAAOtG,KAAK,CAACoG,MAAM,CAACpG,KAAK,CAAC2B,WAAW,CAAC0E,CAAC,CAAC,EAAErG,KAAK,CAAC2B,WAAW,CAAC2E,CAAC,CAAC,CAAC;QACvE,QAAQ,CAAC;MACX;;MAEA;MACA,IAAIO,GAAG;MACP,MAAMC,KAAK,GAAG3G,MAAM,CAACkG,CAAC,CAAC;MACvB,MAAMU,KAAK,GAAG5G,MAAM,CAACmG,CAAC,CAAC;MACvB,IAAIC,iBAAiB,EAAE;QACrB9B,CAAC,GAAG,CAAC;QACLoC,GAAG,GAAGC,KAAK,CAAC3B,KAAK,CAAC5B,GAAG,IAAI;UACvB,IAAIkB,CAAC,IAAIsC,KAAK,CAAC3C,MAAM,EAAE;YACrB,OAAO,KAAK;UACd;UACA,IAAIb,GAAG,KAAKwD,KAAK,CAACtC,CAAC,CAAC,EAAE;YACpB,OAAO,KAAK;UACd;UACA,IAAI,CAACzE,KAAK,CAACoG,MAAM,CAACC,CAAC,CAAC9C,GAAG,CAAC,EAAE+C,CAAC,CAACS,KAAK,CAACtC,CAAC,CAAC,CAAC,EAAEgB,OAAO,CAAC,EAAE;YAC/C,OAAO,KAAK;UACd;UACAhB,CAAC,EAAE;UACH,OAAO,IAAI;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACLA,CAAC,GAAG,CAAC;QACLoC,GAAG,GAAGC,KAAK,CAAC3B,KAAK,CAAC5B,GAAG,IAAI;UACvB,IAAI,CAAClD,MAAM,CAACiG,CAAC,EAAE/C,GAAG,CAAC,EAAE;YACnB,OAAO,KAAK;UACd;UACA,IAAI,CAACvD,KAAK,CAACoG,MAAM,CAACC,CAAC,CAAC9C,GAAG,CAAC,EAAE+C,CAAC,CAAC/C,GAAG,CAAC,EAAEkC,OAAO,CAAC,EAAE;YAC1C,OAAO,KAAK;UACd;UACAhB,CAAC,EAAE;UACH,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;MACA,OAAOoC,GAAG,IAAIpC,CAAC,KAAKsC,KAAK,CAAC3C,MAAM;IAClC,CAAC;;IAED;AACA;AACA;AACA;AACA;IACApE,KAAK,CAACgF,KAAK,GAAGrE,CAAC,IAAI;MACjB,IAAIkG,GAAG;MACP,IAAI,CAAC3G,QAAQ,CAACS,CAAC,CAAC,EAAE;QAChB,OAAOA,CAAC;MACV;MAEA,IAAIA,CAAC,KAAK,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAC;MACf;MAEA,IAAIA,CAAC,YAAYe,IAAI,EAAE;QACrB,OAAO,IAAIA,IAAI,CAACf,CAAC,CAACkB,OAAO,CAAC,CAAC,CAAC;MAC9B;;MAEA;MACA;MACA,IAAIlB,CAAC,YAAYoB,MAAM,EAAE;QACvB,OAAOpB,CAAC;MACV;MAEA,IAAIX,KAAK,CAACkG,QAAQ,CAACvF,CAAC,CAAC,EAAE;QACrBkG,GAAG,GAAG7G,KAAK,CAACgH,SAAS,CAACrG,CAAC,CAACyD,MAAM,CAAC;QAC/B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9D,CAAC,CAACyD,MAAM,EAAEK,CAAC,EAAE,EAAE;UACjCoC,GAAG,CAACpC,CAAC,CAAC,GAAG9D,CAAC,CAAC8D,CAAC,CAAC;QACf;QACA,OAAOoC,GAAG;MACZ;MAEA,IAAIH,KAAK,CAACC,OAAO,CAAChG,CAAC,CAAC,EAAE;QACpB,OAAOA,CAAC,CAACsG,GAAG,CAACjH,KAAK,CAACgF,KAAK,CAAC;MAC3B;MAEA,IAAIzE,WAAW,CAACI,CAAC,CAAC,EAAE;QAClB,OAAO+F,KAAK,CAACQ,IAAI,CAACvG,CAAC,CAAC,CAACsG,GAAG,CAACjH,KAAK,CAACgF,KAAK,CAAC;MACvC;;MAEA;MACA,IAAI/E,UAAU,CAACU,CAAC,CAACqE,KAAK,CAAC,EAAE;QACvB,OAAOrE,CAAC,CAACqE,KAAK,CAAC,CAAC;MAClB;;MAEA;MACA,IAAIhF,KAAK,CAACyD,aAAa,CAAC9C,CAAC,CAAC,EAAE;QAC1B,OAAOX,KAAK,CAAC8B,aAAa,CAAC9B,KAAK,CAACgF,KAAK,CAAChF,KAAK,CAAC2B,WAAW,CAAChB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;MACrE;;MAEA;MACAkG,GAAG,GAAG,CAAC,CAAC;MACR1G,MAAM,CAACQ,CAAC,CAAC,CAAC2C,OAAO,CAAEC,GAAG,IAAK;QACzBsD,GAAG,CAACtD,GAAG,CAAC,GAAGvD,KAAK,CAACgF,KAAK,CAACrE,CAAC,CAAC4C,GAAG,CAAC,CAAC;MAChC,CAAC,CAAC;MACF,OAAOsD,GAAG;IACZ,CAAC;;IAED;AACA;AACA;AACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA7G,KAAK,CAACgH,SAAS,GAAGlE,MAAM,CAACkE,SAAS;IAACG,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AC5mBnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,KAAKA,CAACC,MAAM,EAAE;EACrB,OAAOxB,IAAI,CAACR,SAAS,CAACgC,MAAM,CAAC;AAC/B;AAEA,MAAMC,GAAG,GAAGA,CAAClE,GAAG,EAAEmE,MAAM,EAAEC,YAAY,EAAEC,WAAW,EAAEhC,SAAS,KAAK;EACjE,MAAMhB,KAAK,GAAG8C,MAAM,CAACnE,GAAG,CAAC;;EAEzB;EACA,QAAQ,OAAOqB,KAAK;IACpB,KAAK,QAAQ;MACX,OAAO2C,KAAK,CAAC3C,KAAK,CAAC;IACrB,KAAK,QAAQ;MACX;MACA,OAAOiD,QAAQ,CAACjD,KAAK,CAAC,GAAGkD,MAAM,CAAClD,KAAK,CAAC,GAAG,MAAM;IACjD,KAAK,SAAS;MACZ,OAAOkD,MAAM,CAAClD,KAAK,CAAC;IACtB;IACA;IACA,KAAK,QAAQ;MAAE;QACb;QACA;QACA,IAAI,CAACA,KAAK,EAAE;UACV,OAAO,MAAM;QACf;QACA;QACA;QACA,MAAMmD,WAAW,GAAGH,WAAW,GAAGD,YAAY;QAC9C,MAAMK,OAAO,GAAG,EAAE;QAClB,IAAIrH,CAAC;;QAEL;QACA,IAAI+F,KAAK,CAACC,OAAO,CAAC/B,KAAK,CAAC,IAAK,CAAC,CAAC,CAAEqD,cAAc,CAACC,IAAI,CAACtD,KAAK,EAAE,QAAQ,CAAC,EAAE;UACrE;UACA;UACA,MAAMR,MAAM,GAAGQ,KAAK,CAACR,MAAM;UAC3B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;YAClCuD,OAAO,CAACvD,CAAC,CAAC,GACRgD,GAAG,CAAChD,CAAC,EAAEG,KAAK,EAAE+C,YAAY,EAAEI,WAAW,EAAEnC,SAAS,CAAC,IAAI,MAAM;UACjE;;UAEA;UACA;UACA,IAAIoC,OAAO,CAAC5D,MAAM,KAAK,CAAC,EAAE;YACxBzD,CAAC,GAAG,IAAI;UACV,CAAC,MAAM,IAAIoH,WAAW,EAAE;YACtBpH,CAAC,GAAG,KAAK,GACPoH,WAAW,GACXC,OAAO,CAACG,IAAI,CAAC,KAAK,GAClBJ,WAAW,CAAC,GACZ,IAAI,GACJH,WAAW,GACX,GAAG;UACP,CAAC,MAAM;YACLjH,CAAC,GAAG,GAAG,GAAGqH,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;UACnC;UACA,OAAOxH,CAAC;QACV;;QAEA;QACA,IAAIuE,IAAI,GAAGkD,MAAM,CAAClD,IAAI,CAACN,KAAK,CAAC;QAC7B,IAAIgB,SAAS,EAAE;UACbV,IAAI,GAAGA,IAAI,CAACmD,IAAI,CAAC,CAAC;QACpB;QACAnD,IAAI,CAAC5B,OAAO,CAAC8B,CAAC,IAAI;UAChBzE,CAAC,GAAG8G,GAAG,CAACrC,CAAC,EAAER,KAAK,EAAE+C,YAAY,EAAEI,WAAW,EAAEnC,SAAS,CAAC;UACvD,IAAIjF,CAAC,EAAE;YACLqH,OAAO,CAACM,IAAI,CAACf,KAAK,CAACnC,CAAC,CAAC,IAAI2C,WAAW,GAAG,IAAI,GAAG,GAAG,CAAC,GAAGpH,CAAC,CAAC;UACzD;QACF,CAAC,CAAC;;QAEF;QACA;QACA,IAAIqH,OAAO,CAAC5D,MAAM,KAAK,CAAC,EAAE;UACxBzD,CAAC,GAAG,IAAI;QACV,CAAC,MAAM,IAAIoH,WAAW,EAAE;UACtBpH,CAAC,GAAG,KAAK,GACPoH,WAAW,GACXC,OAAO,CAACG,IAAI,CAAC,KAAK,GAClBJ,WAAW,CAAC,GACZ,IAAI,GACJH,WAAW,GACX,GAAG;QACP,CAAC,MAAM;UACLjH,CAAC,GAAG,GAAG,GAAGqH,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;QACnC;QACA,OAAOxH,CAAC;MACV;IAEA,QAAQ,CAAC;EACT;AACF,CAAC;;AAED;AACA,MAAMmF,kBAAkB,GAAGA,CAAClB,KAAK,EAAEa,OAAO,KAAK;EAC7C;EACA;EACA,MAAM8C,UAAU,GAAGH,MAAM,CAACI,MAAM,CAAC;IAC/B3C,MAAM,EAAE,EAAE;IACVD,SAAS,EAAE;EACb,CAAC,EAAEH,OAAO,CAAC;EACX,IAAI8C,UAAU,CAAC1C,MAAM,KAAK,IAAI,EAAE;IAC9B0C,UAAU,CAAC1C,MAAM,GAAG,IAAI;EAC1B,CAAC,MAAM,IAAI,OAAO0C,UAAU,CAAC1C,MAAM,KAAK,QAAQ,EAAE;IAChD,IAAI4C,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,UAAU,CAAC1C,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC1CgE,SAAS,IAAI,GAAG;IAClB;IACAF,UAAU,CAAC1C,MAAM,GAAG4C,SAAS;EAC/B;EACA,OAAOhB,GAAG,CAAC,EAAE,EAAE;IAAC,EAAE,EAAE7C;EAAK,CAAC,EAAE2D,UAAU,CAAC1C,MAAM,EAAE,EAAE,EAAE0C,UAAU,CAAC3C,SAAS,CAAC;AAC1E,CAAC;AAvHD9F,MAAM,CAAC4I,aAAa,CAyHL5C,kBAzHS,CAAC,C;;;;;;;;;;;ACAzBhG,MAAM,CAACC,MAAM,CAAC;EAACE,UAAU,EAACA,CAAA,KAAIA,UAAU;EAACC,QAAQ,EAACA,CAAA,KAAIA,QAAQ;EAACC,MAAM,EAACA,CAAA,KAAIA,MAAM;EAACC,QAAQ,EAACA,CAAA,KAAIA,QAAQ;EAACC,MAAM,EAACA,CAAA,KAAIA,MAAM;EAACC,kBAAkB,EAACA,CAAA,KAAIA,kBAAkB;EAACC,WAAW,EAACA,CAAA,KAAIA,WAAW;EAACC,UAAU,EAACA,CAAA,KAAIA,UAAU;EAACmI,UAAU,EAACA,CAAA,KAAIA,UAAU;EAAClI,WAAW,EAACA,CAAA,KAAIA;AAAW,CAAC,CAAC;AAAzQ,MAAMR,UAAU,GAAI2I,EAAE,IAAK,OAAOA,EAAE,KAAK,UAAU;AAEnD,MAAM1I,QAAQ,GAAI0I,EAAE,IAAK,OAAOA,EAAE,KAAK,QAAQ;AAE/C,MAAMzI,MAAM,GAAIqB,GAAG,IAAK4G,MAAM,CAAClD,IAAI,CAAC1D,GAAG,CAAC;AAExC,MAAMpB,QAAQ,GAAIoB,GAAG,IAAK4G,MAAM,CAAClD,IAAI,CAAC1D,GAAG,CAAC,CAAC4C,MAAM;AAEjD,MAAM/D,MAAM,GAAGA,CAACmB,GAAG,EAAEqH,IAAI,KAAKT,MAAM,CAACU,SAAS,CAACb,cAAc,CAACC,IAAI,CAAC1G,GAAG,EAAEqH,IAAI,CAAC;AAE7E,MAAMvI,kBAAkB,GAAI2G,GAAG,IAAKP,KAAK,CAACQ,IAAI,CAACD,GAAG,CAAC,CAAC8B,MAAM,CAAC,CAACC,GAAG,EAAAC,IAAA,KAAmB;EAAA,IAAjB,CAAC1F,GAAG,EAAEqB,KAAK,CAAC,GAAAqE,IAAA;EAClF;EACAD,GAAG,CAACzF,GAAG,CAAC,GAAGqB,KAAK;EAChB,OAAOoE,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AAEC,MAAMzI,WAAW,GAAGiB,GAAG,IAAIA,GAAG,IAAI,IAAI,IAAInB,MAAM,CAACmB,GAAG,EAAE,QAAQ,CAAC;AAE/D,MAAMhB,UAAU,GACrBgB,GAAG,IAAIgB,MAAM,CAACC,KAAK,CAACjB,GAAG,CAAC,IAAIA,GAAG,KAAKkB,QAAQ,IAAIlB,GAAG,KAAK,CAACkB,QAAQ;AAE5D,MAAMiG,UAAU,GAAG;EACxBO,QAAQ,EAAGC,QAAQ,IAAK,IAAIpH,MAAM,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAACqH,IAAI,CAACD,QAAQ;AAC3F,CAAC;AAEM,MAAM1I,WAAW,GAAImI,EAAE,IAAK,YAAW;EAC5C,IAAI;IACF,OAAOA,EAAE,CAACS,KAAK,CAAC,IAAI,EAAElF,SAAS,CAAC;EAClC,CAAC,CAAC,OAAOmF,KAAK,EAAE;IACd,MAAMC,UAAU,GAAGZ,UAAU,CAACO,QAAQ,CAACI,KAAK,CAACE,OAAO,CAAC;IACrD,IAAID,UAAU,EAAE;MACd,MAAM,IAAIpI,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IACA,MAAMmI,KAAK;EACb;AACF,CAAC,C", "file": "/packages/ejson.js", "sourcesContent": ["import {\n  isFunction,\n  isObject,\n  keysOf,\n  lengthOf,\n  hasOwn,\n  convertMapToObject,\n  isArguments,\n  isInfOrNaN,\n  handleError,\n} from './utils';\n\n/**\n * @namespace\n * @summary Namespace for EJSON functions\n */\nconst EJSON = {};\n\n// Custom type interface definition\n/**\n * @class CustomType\n * @instanceName customType\n * @memberOf EJSON\n * @summary The interface that a class must satisfy to be able to become an\n * EJSON custom type via EJSON.addType.\n */\n\n/**\n * @function typeName\n * @memberOf EJSON.CustomType\n * @summary Return the tag used to identify this type.  This must match the\n *          tag used to register this type with\n *          [`EJSON.addType`](#ejson_add_type).\n * @locus Anywhere\n * @instance\n */\n\n/**\n * @function toJSONValue\n * @memberOf EJSON.CustomType\n * @summary Serialize this instance into a JSON-compatible value.\n * @locus Anywhere\n * @instance\n */\n\n/**\n * @function clone\n * @memberOf EJSON.CustomType\n * @summary Return a value `r` such that `this.equals(r)` is true, and\n *          modifications to `r` do not affect `this` and vice versa.\n * @locus Anywhere\n * @instance\n */\n\n/**\n * @function equals\n * @memberOf EJSON.CustomType\n * @summary Return `true` if `other` has a value equal to `this`; `false`\n *          otherwise.\n * @locus Anywhere\n * @param {Object} other Another object to compare this to.\n * @instance\n */\n\nconst customTypes = new Map();\n\n// Add a custom type, using a method of your choice to get to and\n// from a basic JSON-able representation.  The factory argument\n// is a function of JSON-able --> your object\n// The type you add must have:\n// - A toJSONValue() method, so that Meteor can serialize it\n// - a typeName() method, to show how to look it up in our type table.\n// It is okay if these methods are monkey-patched on.\n// EJSON.clone will use toJSONValue and the given factory to produce\n// a clone, but you may specify a method clone() that will be\n// used instead.\n// Similarly, EJSON.equals will use toJSONValue to make comparisons,\n// but you may provide a method equals() instead.\n/**\n * @summary Add a custom datatype to EJSON.\n * @locus Anywhere\n * @param {String} name A tag for your custom type; must be unique among\n *                      custom data types defined in your project, and must\n *                      match the result of your type's `typeName` method.\n * @param {Function} factory A function that deserializes a JSON-compatible\n *                           value into an instance of your type.  This should\n *                           match the serialization performed by your\n *                           type's `toJSONValue` method.\n */\nEJSON.addType = (name, factory) => {\n  if (customTypes.has(name)) {\n    throw new Error(`Type ${name} already present`);\n  }\n  customTypes.set(name, factory);\n};\n\nconst builtinConverters = [\n  { // Date\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$date') && lengthOf(obj) === 1;\n    },\n    matchObject(obj) {\n      return obj instanceof Date;\n    },\n    toJSONValue(obj) {\n      return {$date: obj.getTime()};\n    },\n    fromJSONValue(obj) {\n      return new Date(obj.$date);\n    },\n  },\n  { // RegExp\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$regexp')\n        && hasOwn(obj, '$flags')\n        && lengthOf(obj) === 2;\n    },\n    matchObject(obj) {\n      return obj instanceof RegExp;\n    },\n    toJSONValue(regexp) {\n      return {\n        $regexp: regexp.source,\n        $flags: regexp.flags\n      };\n    },\n    fromJSONValue(obj) {\n      // Replaces duplicate / invalid flags.\n      return new RegExp(\n        obj.$regexp,\n        obj.$flags\n          // Cut off flags at 50 chars to avoid abusing RegExp for DOS.\n          .slice(0, 50)\n          .replace(/[^gimuy]/g,'')\n          .replace(/(.)(?=.*\\1)/g, '')\n      );\n    },\n  },\n  { // NaN, Inf, -Inf. (These are the only objects with typeof !== 'object'\n    // which we match.)\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$InfNaN') && lengthOf(obj) === 1;\n    },\n    matchObject: isInfOrNaN,\n    toJSONValue(obj) {\n      let sign;\n      if (Number.isNaN(obj)) {\n        sign = 0;\n      } else if (obj === Infinity) {\n        sign = 1;\n      } else {\n        sign = -1;\n      }\n      return {$InfNaN: sign};\n    },\n    fromJSONValue(obj) {\n      return obj.$InfNaN / 0;\n    },\n  },\n  { // Binary\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$binary') && lengthOf(obj) === 1;\n    },\n    matchObject(obj) {\n      return typeof Uint8Array !== 'undefined' && obj instanceof Uint8Array\n        || (obj && hasOwn(obj, '$Uint8ArrayPolyfill'));\n    },\n    toJSONValue(obj) {\n      return {$binary: Base64.encode(obj)};\n    },\n    fromJSONValue(obj) {\n      return Base64.decode(obj.$binary);\n    },\n  },\n  { // Escaping one level\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$escape') && lengthOf(obj) === 1;\n    },\n    matchObject(obj) {\n      let match = false;\n      if (obj) {\n        const keyCount = lengthOf(obj);\n        if (keyCount === 1 || keyCount === 2) {\n          match =\n            builtinConverters.some(converter => converter.matchJSONValue(obj));\n        }\n      }\n      return match;\n    },\n    toJSONValue(obj) {\n      const newObj = {};\n      keysOf(obj).forEach(key => {\n        newObj[key] = EJSON.toJSONValue(obj[key]);\n      });\n      return {$escape: newObj};\n    },\n    fromJSONValue(obj) {\n      const newObj = {};\n      keysOf(obj.$escape).forEach(key => {\n        newObj[key] = EJSON.fromJSONValue(obj.$escape[key]);\n      });\n      return newObj;\n    },\n  },\n  { // Custom\n    matchJSONValue(obj) {\n      return hasOwn(obj, '$type')\n        && hasOwn(obj, '$value') && lengthOf(obj) === 2;\n    },\n    matchObject(obj) {\n      return EJSON._isCustomType(obj);\n    },\n    toJSONValue(obj) {\n      const jsonValue = Meteor._noYieldsAllowed(() => obj.toJSONValue());\n      return {$type: obj.typeName(), $value: jsonValue};\n    },\n    fromJSONValue(obj) {\n      const typeName = obj.$type;\n      if (!customTypes.has(typeName)) {\n        throw new Error(`Custom EJSON type ${typeName} is not defined`);\n      }\n      const converter = customTypes.get(typeName);\n      return Meteor._noYieldsAllowed(() => converter(obj.$value));\n    },\n  },\n];\n\nEJSON._isCustomType = (obj) => (\n  obj &&\n  isFunction(obj.toJSONValue) &&\n  isFunction(obj.typeName) &&\n  customTypes.has(obj.typeName())\n);\n\nEJSON._getTypes = (isOriginal = false) => (isOriginal ? customTypes : convertMapToObject(customTypes));\n\nEJSON._getConverters = () => builtinConverters;\n\n// Either return the JSON-compatible version of the argument, or undefined (if\n// the item isn't itself replaceable, but maybe some fields in it are)\nconst toJSONValueHelper = item => {\n  for (let i = 0; i < builtinConverters.length; i++) {\n    const converter = builtinConverters[i];\n    if (converter.matchObject(item)) {\n      return converter.toJSONValue(item);\n    }\n  }\n  return undefined;\n};\n\n// for both arrays and objects, in-place modification.\nconst adjustTypesToJSONValue = obj => {\n  // Is it an atom that we need to adjust?\n  if (obj === null) {\n    return null;\n  }\n\n  const maybeChanged = toJSONValueHelper(obj);\n  if (maybeChanged !== undefined) {\n    return maybeChanged;\n  }\n\n  // Other atoms are unchanged.\n  if (!isObject(obj)) {\n    return obj;\n  }\n\n  // Iterate over array or object structure.\n  keysOf(obj).forEach(key => {\n    const value = obj[key];\n    if (!isObject(value) && value !== undefined &&\n        !isInfOrNaN(value)) {\n      return; // continue\n    }\n\n    const changed = toJSONValueHelper(value);\n    if (changed) {\n      obj[key] = changed;\n      return; // on to the next key\n    }\n    // if we get here, value is an object but not adjustable\n    // at this level.  recurse.\n    adjustTypesToJSONValue(value);\n  });\n  return obj;\n};\n\nEJSON._adjustTypesToJSONValue = adjustTypesToJSONValue;\n\n/**\n * @summary Serialize an EJSON-compatible value into its plain JSON\n *          representation.\n * @locus Anywhere\n * @param {EJSON} val A value to serialize to plain JSON.\n */\nEJSON.toJSONValue = item => {\n  const changed = toJSONValueHelper(item);\n  if (changed !== undefined) {\n    return changed;\n  }\n\n  let newItem = item;\n  if (isObject(item)) {\n    newItem = EJSON.clone(item);\n    adjustTypesToJSONValue(newItem);\n  }\n  return newItem;\n};\n\n// Either return the argument changed to have the non-json\n// rep of itself (the Object version) or the argument itself.\n// DOES NOT RECURSE.  For actually getting the fully-changed value, use\n// EJSON.fromJSONValue\nconst fromJSONValueHelper = value => {\n  if (isObject(value) && value !== null) {\n    const keys = keysOf(value);\n    if (keys.length <= 2\n        && keys.every(k => typeof k === 'string' && k.substr(0, 1) === '$')) {\n      for (let i = 0; i < builtinConverters.length; i++) {\n        const converter = builtinConverters[i];\n        if (converter.matchJSONValue(value)) {\n          return converter.fromJSONValue(value);\n        }\n      }\n    }\n  }\n  return value;\n};\n\n// for both arrays and objects. Tries its best to just\n// use the object you hand it, but may return something\n// different if the object you hand it itself needs changing.\nconst adjustTypesFromJSONValue = obj => {\n  if (obj === null) {\n    return null;\n  }\n\n  const maybeChanged = fromJSONValueHelper(obj);\n  if (maybeChanged !== obj) {\n    return maybeChanged;\n  }\n\n  // Other atoms are unchanged.\n  if (!isObject(obj)) {\n    return obj;\n  }\n\n  keysOf(obj).forEach(key => {\n    const value = obj[key];\n    if (isObject(value)) {\n      const changed = fromJSONValueHelper(value);\n      if (value !== changed) {\n        obj[key] = changed;\n        return;\n      }\n      // if we get here, value is an object but not adjustable\n      // at this level.  recurse.\n      adjustTypesFromJSONValue(value);\n    }\n  });\n  return obj;\n};\n\nEJSON._adjustTypesFromJSONValue = adjustTypesFromJSONValue;\n\n/**\n * @summary Deserialize an EJSON value from its plain JSON representation.\n * @locus Anywhere\n * @param {JSONCompatible} val A value to deserialize into EJSON.\n */\nEJSON.fromJSONValue = item => {\n  let changed = fromJSONValueHelper(item);\n  if (changed === item && isObject(item)) {\n    changed = EJSON.clone(item);\n    adjustTypesFromJSONValue(changed);\n  }\n  return changed;\n};\n\n/**\n * @summary Serialize a value to a string. For EJSON values, the serialization\n *          fully represents the value. For non-EJSON values, serializes the\n *          same way as `JSON.stringify`.\n * @locus Anywhere\n * @param {EJSON} val A value to stringify.\n * @param {Object} [options]\n * @param {Boolean | Integer | String} [options.indent] Indents objects and\n * arrays for easy readability.  When `true`, indents by 2 spaces; when an\n * integer, indents by that number of spaces; and when a string, uses the\n * string as the indentation pattern.\n * @param {Boolean} [options.canonical] When `true`, stringifies keys in an\n *                                    object in sorted order.\n */\nEJSON.stringify = handleError((item, options) => {\n  let serialized;\n  const json = EJSON.toJSONValue(item);\n  if (options && (options.canonical || options.indent)) {\n    import canonicalStringify from './stringify';\n    serialized = canonicalStringify(json, options);\n  } else {\n    serialized = JSON.stringify(json);\n  }\n  return serialized;\n});\n\n/**\n * @summary Parse a string into an EJSON value. Throws an error if the string\n *          is not valid EJSON.\n * @locus Anywhere\n * @param {String} str A string to parse into an EJSON value.\n */\nEJSON.parse = item => {\n  if (typeof item !== 'string') {\n    throw new Error('EJSON.parse argument should be a string');\n  }\n  return EJSON.fromJSONValue(JSON.parse(item));\n};\n\n/**\n * @summary Returns true if `x` is a buffer of binary data, as returned from\n *          [`EJSON.newBinary`](#ejson_new_binary).\n * @param {Object} x The variable to check.\n * @locus Anywhere\n */\nEJSON.isBinary = obj => {\n  return !!((typeof Uint8Array !== 'undefined' && obj instanceof Uint8Array) ||\n    (obj && obj.$Uint8ArrayPolyfill));\n};\n\n/**\n * @summary Return true if `a` and `b` are equal to each other.  Return false\n *          otherwise.  Uses the `equals` method on `a` if present, otherwise\n *          performs a deep comparison.\n * @locus Anywhere\n * @param {EJSON} a\n * @param {EJSON} b\n * @param {Object} [options]\n * @param {Boolean} options.keyOrderSensitive Compare in key sensitive order,\n * if supported by the JavaScript implementation.  For example, `{a: 1, b: 2}`\n * is equal to `{b: 2, a: 1}` only when `keyOrderSensitive` is `false`.  The\n * default is `false`.\n */\nEJSON.equals = (a, b, options) => {\n  let i;\n  const keyOrderSensitive = !!(options && options.keyOrderSensitive);\n  if (a === b) {\n    return true;\n  }\n\n  // This differs from the IEEE spec for NaN equality, b/c we don't want\n  // anything ever with a NaN to be poisoned from becoming equal to anything.\n  if (Number.isNaN(a) && Number.isNaN(b)) {\n    return true;\n  }\n\n  // if either one is falsy, they'd have to be === to be equal\n  if (!a || !b) {\n    return false;\n  }\n\n  if (!(isObject(a) && isObject(b))) {\n    return false;\n  }\n\n  if (a instanceof Date && b instanceof Date) {\n    return a.valueOf() === b.valueOf();\n  }\n\n  if (EJSON.isBinary(a) && EJSON.isBinary(b)) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  if (isFunction(a.equals)) {\n    return a.equals(b, options);\n  }\n\n  if (isFunction(b.equals)) {\n    return b.equals(a, options);\n  }\n\n  // Array.isArray works across iframes while instanceof won't\n  const aIsArray = Array.isArray(a);\n  const bIsArray = Array.isArray(b);\n\n  // if not both or none are array they are not equal\n  if (aIsArray !== bIsArray) {\n    return false;\n  }\n\n  if (aIsArray && bIsArray) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (i = 0; i < a.length; i++) {\n      if (!EJSON.equals(a[i], b[i], options)) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // fallback for custom types that don't implement their own equals\n  switch (EJSON._isCustomType(a) + EJSON._isCustomType(b)) {\n    case 1: return false;\n    case 2: return EJSON.equals(EJSON.toJSONValue(a), EJSON.toJSONValue(b));\n    default: // Do nothing\n  }\n\n  // fall back to structural equality of objects\n  let ret;\n  const aKeys = keysOf(a);\n  const bKeys = keysOf(b);\n  if (keyOrderSensitive) {\n    i = 0;\n    ret = aKeys.every(key => {\n      if (i >= bKeys.length) {\n        return false;\n      }\n      if (key !== bKeys[i]) {\n        return false;\n      }\n      if (!EJSON.equals(a[key], b[bKeys[i]], options)) {\n        return false;\n      }\n      i++;\n      return true;\n    });\n  } else {\n    i = 0;\n    ret = aKeys.every(key => {\n      if (!hasOwn(b, key)) {\n        return false;\n      }\n      if (!EJSON.equals(a[key], b[key], options)) {\n        return false;\n      }\n      i++;\n      return true;\n    });\n  }\n  return ret && i === bKeys.length;\n};\n\n/**\n * @summary Return a deep copy of `val`.\n * @locus Anywhere\n * @param {EJSON} val A value to copy.\n */\nEJSON.clone = v => {\n  let ret;\n  if (!isObject(v)) {\n    return v;\n  }\n\n  if (v === null) {\n    return null; // null has typeof \"object\"\n  }\n\n  if (v instanceof Date) {\n    return new Date(v.getTime());\n  }\n\n  // RegExps are not really EJSON elements (eg we don't define a serialization\n  // for them), but they're immutable anyway, so we can support them in clone.\n  if (v instanceof RegExp) {\n    return v;\n  }\n\n  if (EJSON.isBinary(v)) {\n    ret = EJSON.newBinary(v.length);\n    for (let i = 0; i < v.length; i++) {\n      ret[i] = v[i];\n    }\n    return ret;\n  }\n\n  if (Array.isArray(v)) {\n    return v.map(EJSON.clone);\n  }\n\n  if (isArguments(v)) {\n    return Array.from(v).map(EJSON.clone);\n  }\n\n  // handle general user-defined typed Objects if they have a clone method\n  if (isFunction(v.clone)) {\n    return v.clone();\n  }\n\n  // handle other custom types\n  if (EJSON._isCustomType(v)) {\n    return EJSON.fromJSONValue(EJSON.clone(EJSON.toJSONValue(v)), true);\n  }\n\n  // handle other objects\n  ret = {};\n  keysOf(v).forEach((key) => {\n    ret[key] = EJSON.clone(v[key]);\n  });\n  return ret;\n};\n\n/**\n * @summary Allocate a new buffer of binary data that EJSON can serialize.\n * @locus Anywhere\n * @param {Number} size The number of bytes of binary data to allocate.\n */\n// EJSON.newBinary is the public documented API for this functionality,\n// but the implementation is in the 'base64' package to avoid\n// introducing a circular dependency. (If the implementation were here,\n// then 'base64' would have to use EJSON.newBinary, and 'ejson' would\n// also have to use 'base64'.)\nEJSON.newBinary = Base64.newBinary;\n\nexport { EJSON };\n", "// Based on json2.js from https://github.com/douglascrockford/JSON-js\n//\n//    json2.js\n//    2012-10-08\n//\n//    Public Domain.\n//\n//    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\nfunction quote(string) {\n  return JSON.stringify(string);\n}\n\nconst str = (key, holder, singleIndent, outerIndent, canonical) => {\n  const value = holder[key];\n\n  // What happens next depends on the value's type.\n  switch (typeof value) {\n  case 'string':\n    return quote(value);\n  case 'number':\n    // JSON numbers must be finite. Encode non-finite numbers as null.\n    return isFinite(value) ? String(value) : 'null';\n  case 'boolean':\n    return String(value);\n  // If the type is 'object', we might be dealing with an object or an array or\n  // null.\n  case 'object': {\n    // Due to a specification blunder in ECMAScript, typeof null is 'object',\n    // so watch out for that case.\n    if (!value) {\n      return 'null';\n    }\n    // Make an array to hold the partial results of stringifying this object\n    // value.\n    const innerIndent = outerIndent + singleIndent;\n    const partial = [];\n    let v;\n\n    // Is the value an array?\n    if (Array.isArray(value) || ({}).hasOwnProperty.call(value, 'callee')) {\n      // The value is an array. Stringify every element. Use null as a\n      // placeholder for non-JSON values.\n      const length = value.length;\n      for (let i = 0; i < length; i += 1) {\n        partial[i] =\n          str(i, value, singleIndent, innerIndent, canonical) || 'null';\n      }\n\n      // Join all of the elements together, separated with commas, and wrap\n      // them in brackets.\n      if (partial.length === 0) {\n        v = '[]';\n      } else if (innerIndent) {\n        v = '[\\n' +\n          innerIndent +\n          partial.join(',\\n' +\n          innerIndent) +\n          '\\n' +\n          outerIndent +\n          ']';\n      } else {\n        v = '[' + partial.join(',') + ']';\n      }\n      return v;\n    }\n\n    // Iterate through all of the keys in the object.\n    let keys = Object.keys(value);\n    if (canonical) {\n      keys = keys.sort();\n    }\n    keys.forEach(k => {\n      v = str(k, value, singleIndent, innerIndent, canonical);\n      if (v) {\n        partial.push(quote(k) + (innerIndent ? ': ' : ':') + v);\n      }\n    });\n\n    // Join all of the member texts together, separated with commas,\n    // and wrap them in braces.\n    if (partial.length === 0) {\n      v = '{}';\n    } else if (innerIndent) {\n      v = '{\\n' +\n        innerIndent +\n        partial.join(',\\n' +\n        innerIndent) +\n        '\\n' +\n        outerIndent +\n        '}';\n    } else {\n      v = '{' + partial.join(',') + '}';\n    }\n    return v;\n  }\n\n  default: // Do nothing\n  }\n};\n\n// If the JSON object does not yet have a stringify method, give it one.\nconst canonicalStringify = (value, options) => {\n  // Make a fake root object containing our value under the key of ''.\n  // Return the result of stringifying the value.\n  const allOptions = Object.assign({\n    indent: '',\n    canonical: false,\n  }, options);\n  if (allOptions.indent === true) {\n    allOptions.indent = '  ';\n  } else if (typeof allOptions.indent === 'number') {\n    let newIndent = '';\n    for (let i = 0; i < allOptions.indent; i++) {\n      newIndent += ' ';\n    }\n    allOptions.indent = newIndent;\n  }\n  return str('', {'': value}, allOptions.indent, '', allOptions.canonical);\n};\n\nexport default canonicalStringify;\n", "export const isFunction = (fn) => typeof fn === 'function';\n\nexport const isObject = (fn) => typeof fn === 'object';\n\nexport const keysOf = (obj) => Object.keys(obj);\n\nexport const lengthOf = (obj) => Object.keys(obj).length;\n\nexport const hasOwn = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);\n\nexport const convertMapToObject = (map) => Array.from(map).reduce((acc, [key, value]) => {\n  // reassign to not create new object\n  acc[key] = value;\n  return acc;\n}, {});\n\nexport const isArguments = obj => obj != null && hasOwn(obj, 'callee');\n\nexport const isInfOrNaN =\n  obj => Number.isNaN(obj) || obj === Infinity || obj === -Infinity;\n\nexport const checkError = {\n  maxStack: (msgError) => new RegExp('Maximum call stack size exceeded', 'g').test(msgError),\n};\n\nexport const handleError = (fn) => function() {\n  try {\n    return fn.apply(this, arguments);\n  } catch (error) {\n    const isMaxStack = checkError.maxStack(error.message);\n    if (isMaxStack) {\n      throw new Error('Converting circular structure to JSON')\n    }\n    throw error;\n  }\n};\n"]}