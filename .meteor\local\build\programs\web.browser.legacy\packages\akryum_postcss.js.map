{"version": 3, "sources": ["meteor://💻app/packages/akryum:postcss/postcss.js"], "names": ["module", "export", "name", "postcss", "selector<PERSON><PERSON><PERSON>", "autoprefixer", "addHash", "postcssLib", "link", "default", "v", "selectorParserLib", "autoprefixerLib", "plugin", "opts", "root", "each", "rewriteSelector", "node", "selector", "type", "selectors", "n", "insertAfter", "attribute", "hash", "process", "result"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,IAAI,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,IAAI;EAAA,CAAC;EAACC,OAAO,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,OAAO;EAAA,CAAC;EAACC,cAAc,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,cAAc;EAAA,CAAC;EAACC,YAAY,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,YAAY;EAAA,CAAC;EAACC,OAAO,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,OAAO;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,UAAU;AAACP,MAAM,CAACQ,IAAI,CAAC,SAAS,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACH,UAAU,GAACG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,iBAAiB;AAACX,MAAM,CAACQ,IAAI,CAAC,yBAAyB,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACC,iBAAiB,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,eAAe;AAACZ,MAAM,CAACQ,IAAI,CAAC,cAAc,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACE,eAAe,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAI3d,IAAMR,IAAI,GAAG,SAAS;AAEtB,IAAMC,OAAO,GAAGI,UAAU;AAC1B,IAAMH,cAAc,GAAGO,iBAAiB;AACxC,IAAMN,YAAY,GAAGO,eAAe;AAEpC,IAAMN,OAAO,GAAGH,OAAO,CAACU,MAAM,CAAC,UAAU,EAAE,UAAUC,IAAI,EAAE;EAChE,OAAO,UAAUC,IAAI,EAAE;IACrBA,IAAI,CAACC,IAAI;MAAC,SAASC,eAAeA,CAAEC,IAAI,EAAE;QACxC,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;UAClB;UACA,IAAID,IAAI,CAACE,IAAI,KAAK,QAAQ,IAAIF,IAAI,CAAChB,IAAI,KAAK,OAAO,EAAE;YACnDgB,IAAI,CAACF,IAAI,CAACC,eAAe,CAAC;UAC5B;UACA;QACF;QACAC,IAAI,CAACC,QAAQ,GAAGf,cAAc,CAAC,UAAUiB,SAAS,EAAE;UAClDA,SAAS,CAACL,IAAI,CAAC,UAAUG,QAAQ,EAAE;YACjC,IAAID,IAAI,GAAG,IAAI;YACfC,QAAQ,CAACH,IAAI,CAAC,UAAUM,CAAC,EAAE;cACzB,IAAIA,CAAC,CAACF,IAAI,KAAK,QAAQ,EAAEF,IAAI,GAAGI,CAAC;YACnC,CAAC,CAAC;YACFH,QAAQ,CAACI,WAAW,CAACL,IAAI,EAAEd,cAAc,CAACoB,SAAS,CAAC;cAClDA,SAAS,EAAEV,IAAI,CAACW;YAClB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC;QACJ,CAAC,CAAC,CAACC,OAAO,CAACR,IAAI,CAACC,QAAQ,CAAC,CAACQ,MAAM;MAClC;MAAC,OAnBkBV,eAAe;IAAA,GAmBjC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC,C", "file": "/packages/akryum_postcss.js", "sourcesContent": ["import postcssLib from 'postcss'\nimport selectorParser<PERSON>ib from 'postcss-selector-parser'\nimport autoprefixer<PERSON>ib from 'autoprefixer'\n\nexport const name = 'postcss';\n\nexport const postcss = postcssLib;\nexport const selectorParser = selectorParserLib;\nexport const autoprefixer = autoprefixerLib;\n\nexport const addHash = postcss.plugin('add-hash', function (opts) {\n  return function (root) {\n    root.each(function rewriteSelector (node) {\n      if (!node.selector) {\n        // handle media queries\n        if (node.type === 'atrule' && node.name === 'media') {\n          node.each(rewriteSelector)\n        }\n        return\n      }\n      node.selector = selectorParser(function (selectors) {\n        selectors.each(function (selector) {\n          var node = null\n          selector.each(function (n) {\n            if (n.type !== 'pseudo') node = n\n          })\n          selector.insertAfter(node, selectorParser.attribute({\n            attribute: opts.hash\n          }))\n        })\n      }).process(node.selector).result\n    })\n  }\n})\n"]}