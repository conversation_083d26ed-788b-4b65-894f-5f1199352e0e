{"version": 3, "sources": ["meteor://💻app/packages/webapp/webapp_server.js", "meteor://💻app/packages/webapp/socket_file.js"], "names": ["_objectSpread", "module1", "link", "default", "v", "export", "WebApp", "WebAppInternals", "getGroupInfo", "assert", "readFileSync", "chmodSync", "chownSync", "createServer", "userInfo", "pathJoin", "pathDirname", "join", "dirname", "parseUrl", "parse", "createHash", "express", "compress", "cookie<PERSON>arser", "qs", "parseRequest", "lookupUserAgent", "lookup", "isModern", "send", "removeExistingSocketFile", "registerSocketFileCleanup", "cluster", "execSync", "onMessage", "__reifyWaitForDeps__", "SHORT_SOCKET_TIMEOUT", "LONG_SOCKET_TIMEOUT", "createExpressApp", "app", "set", "hasOwn", "Object", "prototype", "hasOwnProperty", "NpmModules", "version", "Npm", "require", "module", "defaultArch", "clientPrograms", "archPath", "bundledJsCssUrlRewriteHook", "url", "bundledPrefix", "__meteor_runtime_config__", "ROOT_URL_PATH_PREFIX", "sha1", "contents", "hash", "update", "digest", "shouldCompress", "req", "res", "headers", "filter", "camelCase", "name", "parts", "split", "toLowerCase", "i", "length", "char<PERSON>t", "toUpperCase", "substring", "<PERSON><PERSON><PERSON>er", "userAgentString", "major", "minor", "patch", "userAgent", "family", "categorizeRequest", "browser", "arch", "modern", "path", "pathname", "categorized", "dynamicHead", "dynamicBody", "cookies", "pathParts", "<PERSON><PERSON><PERSON>", "startsWith", "archCleaned", "slice", "call", "splice", "assign", "preferredArchOrder", "htmlAttributeHooks", "getHtmlAttributes", "request", "combinedAttributes", "for<PERSON>ach", "hook", "attributes", "Error", "addHtmlAttributeHook", "push", "appUrl", "RoutePolicy", "classify", "Meteor", "startup", "getter", "key", "program", "value", "calculateClientHash", "clientHash", "calculateClientHashRefreshable", "calculateClientHashNonRefreshable", "calculateClientHashReplaceable", "getRefreshableAssets", "_timeoutAdjustmentRequestCallback", "setTimeout", "finishListeners", "listeners", "removeAllListeners", "on", "values", "l", "boilerplateByArch", "boilerplateDataCallbacks", "create", "registerBoilerplateDataCallback", "callback", "previousCallback", "strictEqual", "getBoilerplate", "getBoilerplateAsync", "encodeRuntimeConfig", "rtimeConfig", "JSON", "stringify", "encodeURIComponent", "decodeRuntimeConfig", "rtimeConfigStr", "decodeURIComponent", "runtimeConfig", "hooks", "Hook", "updateHooks", "isUpdatedByArch", "addRuntimeConfigHook", "register", "boilerplate", "forEachAsync", "meteorRuntimeConfig", "encodedCurrentConfig", "baseData", "updated", "data", "htmlAttributes", "madeChanges", "promise", "Promise", "resolve", "keys", "then", "result", "stream", "toHTMLStream", "statusCode", "addUpdatedNotifyHook", "handler", "generateBoilerplateInstance", "manifest", "additionalOptions", "runtimeConfigOverrides", "cb", "Boilerplate", "pathMapper", "itemPath", "baseDataExtension", "additionalStaticJs", "entries", "map", "_ref", "meteorRuntimeHash", "rootUrlPathPrefix", "sriMode", "inlineScriptsAllowed", "inline", "staticFilesMiddleware", "staticFilesByArch", "next", "_Meteor$settings$pack3", "_Meteor$settings$pack4", "e", "serveStaticJs", "s", "_Meteor$settings$pack", "_Meteor$settings$pack2", "method", "settings", "packages", "webapp", "alwaysReturnContent", "writeHead", "<PERSON><PERSON><PERSON>", "byteLength", "write", "end", "status", "Allow", "paused", "concat", "info", "getStaticFileInfo", "maxAge", "cacheable", "<PERSON><PERSON><PERSON><PERSON>", "sourceMapUrl", "type", "content", "absolutePath", "maxage", "dotfiles", "lastModified", "err", "Log", "error", "pipe", "originalPath", "staticArchList", "archIndex", "indexOf", "unshift", "some", "staticFiles", "finalize", "parsePort", "port", "parsedPort", "parseInt", "Number", "isNaN", "_ref2", "pauseClient", "_ref3", "generateClientProgram", "runWebAppServer", "shuttingDown", "syncQueue", "_AsynchronousQueue", "getItemPathname", "itemUrl", "reloadClientPrograms", "runTask", "config<PERSON><PERSON>", "__meteor_bootstrap__", "clientArchs", "clientPaths", "stack", "process", "exit", "unpause", "arguments", "undefined", "clientDir", "serverDir", "programJsonPath", "programJson", "code", "format", "item", "where", "sourceMap", "PUBLIC_SETTINGS", "configOverrides", "oldProgram", "newProgram", "WebAppHashing", "versionRefreshable", "versionNonRefreshable", "replaceable", "versionReplaceable", "_type", "cordovaCompatibilityVersions", "hmrVersion", "manifestUrlPrefix", "replace", "manifestUrl", "Package", "autoupdate", "AUTOUPDATE_VERSION", "Autoupdate", "autoupdateVersion", "env", "generateBoilerplateForArch", "defaultOptionsForArch", "DDP_DEFAULT_CONNECTION_URL", "MOBILE_DDP_URL", "absoluteUrl", "ROOT_URL", "MOBILE_ROOT_URL", "generateBoilerplate", "refreshableAssets", "css", "file", "rawExpressHandlers", "use", "isValidUrl", "getPathParts", "shift", "isPrefixOf", "prefix", "array", "every", "part", "response", "pathPrefix", "search", "prefixParts", "meteorInternalHandlers", "packageAndAppHandlers", "suppressExpressErrors", "_Meteor$settings$pack5", "_Meteor$settings$pack6", "query", "isDevelopment", "_ref4", "newHeaders", "catch", "httpServer", "onListeningCallbacks", "socket", "destroyed", "message", "destroy", "suppressErrors", "warnedAboutConnectUsage", "connectHandlers", "handlers", "rawConnectHandlers", "rawHandlers", "expressApp", "suppressConnectErrors", "_debug", "_suppressExpressErrors", "onListening", "f", "startListening", "listenOptions", "listen", "exports", "main", "argv", "startHttpServer", "bindEnvironment", "METEOR_PRINT_ON_LISTEN", "console", "log", "callbacks", "localPort", "PORT", "unixSocketPath", "UNIX_SOCKET_PATH", "isWorker", "worker<PERSON>ame", "worker", "id", "unixSocketPermissions", "UNIX_SOCKET_PERMISSIONS", "trim", "test", "unixSocketGroup", "UNIX_SOCKET_GROUP", "unixSocketGroupInfo", "uid", "gid", "host", "BIND_IP", "isGetentAvailable", "_unused", "getGroupInfoUsingGetent", "groupName", "stdout", "encoding", "getGroupInfoFromFile", "groupLine", "find", "line", "groupInfo", "setInlineScriptsAllowed", "enableSubresourceIntegrity", "use_credentials", "setBundledJsCssUrlRewriteHook", "hookFn", "setBundledJsCssPrefix", "self", "addStaticJs", "__reify_async_result__", "_reifyError", "async", "statSync", "unlinkSync", "existsSync", "socketPath", "isSocket", "eventEmitter", "signal"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAtGH,OAAO,CAACI,MAAM,CAAC;MAACC,MAAM,EAACA,CAAA,KAAIA,MAAM;MAACC,eAAe,EAACA,CAAA,KAAIA,eAAe;MAACC,YAAY,EAACA,CAAA,KAAIA;IAAY,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACR,OAAO,CAACC,IAAI,CAAC,QAAQ,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACK,MAAM,GAACL,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIM,YAAY,EAACC,SAAS,EAACC,SAAS;IAACX,OAAO,CAACC,IAAI,CAAC,IAAI,EAAC;MAACQ,YAAYA,CAACN,CAAC,EAAC;QAACM,YAAY,GAACN,CAAC;MAAA,CAAC;MAACO,SAASA,CAACP,CAAC,EAAC;QAACO,SAAS,GAACP,CAAC;MAAA,CAAC;MAACQ,SAASA,CAACR,CAAC,EAAC;QAACQ,SAAS,GAACR,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIS,YAAY;IAACZ,OAAO,CAACC,IAAI,CAAC,MAAM,EAAC;MAACW,YAAYA,CAACT,CAAC,EAAC;QAACS,YAAY,GAACT,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIU,QAAQ;IAACb,OAAO,CAACC,IAAI,CAAC,IAAI,EAAC;MAACY,QAAQA,CAACV,CAAC,EAAC;QAACU,QAAQ,GAACV,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIW,QAAQ,EAACC,WAAW;IAACf,OAAO,CAACC,IAAI,CAAC,MAAM,EAAC;MAACe,IAAIA,CAACb,CAAC,EAAC;QAACW,QAAQ,GAACX,CAAC;MAAA,CAAC;MAACc,OAAOA,CAACd,CAAC,EAAC;QAACY,WAAW,GAACZ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIe,QAAQ;IAAClB,OAAO,CAACC,IAAI,CAAC,KAAK,EAAC;MAACkB,KAAKA,CAAChB,CAAC,EAAC;QAACe,QAAQ,GAACf,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIiB,UAAU;IAACpB,OAAO,CAACC,IAAI,CAAC,QAAQ,EAAC;MAACmB,UAAUA,CAACjB,CAAC,EAAC;QAACiB,UAAU,GAACjB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIkB,OAAO;IAACrB,OAAO,CAACC,IAAI,CAAC,SAAS,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACkB,OAAO,GAAClB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAImB,QAAQ;IAACtB,OAAO,CAACC,IAAI,CAAC,aAAa,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACmB,QAAQ,GAACnB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIoB,YAAY;IAACvB,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACoB,YAAY,GAACpB,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIqB,EAAE;IAACxB,OAAO,CAACC,IAAI,CAAC,IAAI,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACqB,EAAE,GAACrB,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIsB,YAAY;IAACzB,OAAO,CAACC,IAAI,CAAC,UAAU,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACsB,YAAY,GAACtB,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIuB,eAAe;IAAC1B,OAAO,CAACC,IAAI,CAAC,cAAc,EAAC;MAAC0B,MAAMA,CAACxB,CAAC,EAAC;QAACuB,eAAe,GAACvB,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIyB,QAAQ;IAAC5B,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAC;MAAC2B,QAAQA,CAACzB,CAAC,EAAC;QAACyB,QAAQ,GAACzB,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAI0B,IAAI;IAAC7B,OAAO,CAACC,IAAI,CAAC,MAAM,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAAC0B,IAAI,GAAC1B,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAI2B,wBAAwB,EAACC,yBAAyB;IAAC/B,OAAO,CAACC,IAAI,CAAC,kBAAkB,EAAC;MAAC6B,wBAAwBA,CAAC3B,CAAC,EAAC;QAAC2B,wBAAwB,GAAC3B,CAAC;MAAA,CAAC;MAAC4B,yBAAyBA,CAAC5B,CAAC,EAAC;QAAC4B,yBAAyB,GAAC5B,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAI6B,OAAO;IAAChC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAAC6B,OAAO,GAAC7B,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAI8B,QAAQ;IAACjC,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;MAACgC,QAAQA,CAAC9B,CAAC,EAAC;QAAC8B,QAAQ,GAAC9B,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAI+B,SAAS;IAAClC,OAAO,CAACC,IAAI,CAAC,gCAAgC,EAAC;MAACiC,SAASA,CAAC/B,CAAC,EAAC;QAAC+B,SAAS,GAAC/B,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIgC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAsBzqD,IAAIC,oBAAoB,GAAG,CAAC,GAAG,IAAI;IACnC,IAAIC,mBAAmB,GAAG,GAAG,GAAG,IAAI;IAEpC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAMC,GAAG,GAAGlB,OAAO,CAAC,CAAC;MACrB;MACA;MACAkB,GAAG,CAACC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC;MAC9BD,GAAG,CAACC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;MACtBD,GAAG,CAACC,GAAG,CAAC,cAAc,EAAEhB,EAAE,CAACL,KAAK,CAAC;MACjC,OAAOoB,GAAG;IACZ,CAAC;IACM,MAAMlC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,eAAe,GAAG,CAAC,CAAC;IAEjC,MAAMmC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;IAG9CtC,eAAe,CAACuC,UAAU,GAAG;MAC3BxB,OAAO,EAAG;QACRyB,OAAO,EAAEC,GAAG,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAACF,OAAO;QACpDG,MAAM,EAAE5B;MACV;IACF,CAAC;;IAED;IACAhB,MAAM,CAACgB,OAAO,GAAGA,OAAO;;IAExB;IACA;IACAhB,MAAM,CAAC6C,WAAW,GAAG,oBAAoB;;IAEzC;IACA7C,MAAM,CAAC8C,cAAc,GAAG,CAAC,CAAC;;IAE1B;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;IAEjB,IAAIC,0BAA0B,GAAG,SAAAA,CAASC,GAAG,EAAE;MAC7C,IAAIC,aAAa,GAAGC,yBAAyB,CAACC,oBAAoB,IAAI,EAAE;MACxE,OAAOF,aAAa,GAAGD,GAAG;IAC5B,CAAC;IAED,IAAII,IAAI,GAAG,SAAAA,CAASC,QAAQ,EAAE;MAC5B,IAAIC,IAAI,GAAGxC,UAAU,CAAC,MAAM,CAAC;MAC7BwC,IAAI,CAACC,MAAM,CAACF,QAAQ,CAAC;MACrB,OAAOC,IAAI,CAACE,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED,SAASC,cAAcA,CAACC,GAAG,EAAEC,GAAG,EAAE;MAChC,IAAID,GAAG,CAACE,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnC;QACA,OAAO,KAAK;MACd;;MAEA;MACA,OAAO5C,QAAQ,CAAC6C,MAAM,CAACH,GAAG,EAAEC,GAAG,CAAC;IAClC;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,IAAIG,SAAS,GAAG,SAAAA,CAASC,IAAI,EAAE;MAC7B,IAAIC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;MAC3BD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAE,EAAED,CAAC,EAAE;QACrCH,KAAK,CAACG,CAAC,CAAC,GAAGH,KAAK,CAACG,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGN,KAAK,CAACG,CAAC,CAAC,CAACI,SAAS,CAAC,CAAC,CAAC;MACrE;MACA,OAAOP,KAAK,CAACtD,IAAI,CAAC,EAAE,CAAC;IACvB,CAAC;IAED,IAAI8D,eAAe,GAAG,SAAAA,CAASC,eAAe,EAAE;MAC9C,IAAI,CAACA,eAAe,EAAE;QACpB,OAAO;UACLV,IAAI,EAAE,SAAS;UACfW,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE;QACT,CAAC;MACH;MACA,IAAIC,SAAS,GAAGzD,eAAe,CAACqD,eAAe,CAACF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;MAClE,OAAO;QACLR,IAAI,EAAED,SAAS,CAACe,SAAS,CAACC,MAAM,CAAC;QACjCJ,KAAK,EAAE,CAACG,SAAS,CAACH,KAAK;QACvBC,KAAK,EAAE,CAACE,SAAS,CAACF,KAAK;QACvBC,KAAK,EAAE,CAACC,SAAS,CAACD;MACpB,CAAC;IACH,CAAC;;IAED;IACA5E,eAAe,CAACwE,eAAe,GAAGA,eAAe;IAEjDzE,MAAM,CAACgF,iBAAiB,GAAG,UAASrB,GAAG,EAAE;MACvC,IAAIA,GAAG,CAACsB,OAAO,IAAItB,GAAG,CAACuB,IAAI,IAAI,OAAOvB,GAAG,CAACwB,MAAM,KAAK,SAAS,EAAE;QAC9D;QACA,OAAOxB,GAAG;MACZ;MAEA,MAAMsB,OAAO,GAAGR,eAAe,CAACd,GAAG,CAACE,OAAO,CAAC,YAAY,CAAC,CAAC;MAC1D,MAAMsB,MAAM,GAAG5D,QAAQ,CAAC0D,OAAO,CAAC;MAChC,MAAMG,IAAI,GACR,OAAOzB,GAAG,CAAC0B,QAAQ,KAAK,QAAQ,GAC5B1B,GAAG,CAAC0B,QAAQ,GACZjE,YAAY,CAACuC,GAAG,CAAC,CAAC0B,QAAQ;MAEhC,MAAMC,WAAW,GAAG;QAClBL,OAAO;QACPE,MAAM;QACNC,IAAI;QACJF,IAAI,EAAElF,MAAM,CAAC6C,WAAW;QACxBI,GAAG,EAAEpC,QAAQ,CAAC8C,GAAG,CAACV,GAAG,EAAE,IAAI,CAAC;QAC5BsC,WAAW,EAAE5B,GAAG,CAAC4B,WAAW;QAC5BC,WAAW,EAAE7B,GAAG,CAAC6B,WAAW;QAC5B3B,OAAO,EAAEF,GAAG,CAACE,OAAO;QACpB4B,OAAO,EAAE9B,GAAG,CAAC8B;MACf,CAAC;MAED,MAAMC,SAAS,GAAGN,IAAI,CAAClB,KAAK,CAAC,GAAG,CAAC;MACjC,MAAMyB,OAAO,GAAGD,SAAS,CAAC,CAAC,CAAC;MAE5B,IAAIC,OAAO,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;QAC5B,MAAMC,WAAW,GAAG,MAAM,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC;QAC7C,IAAI1D,MAAM,CAAC2D,IAAI,CAAC/F,MAAM,CAAC8C,cAAc,EAAE+C,WAAW,CAAC,EAAE;UACnDH,SAAS,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACxB,OAAO3D,MAAM,CAAC4D,MAAM,CAACX,WAAW,EAAE;YAChCJ,IAAI,EAAEW,WAAW;YACjBT,IAAI,EAAEM,SAAS,CAAC/E,IAAI,CAAC,GAAG;UAC1B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA;MACA,MAAMuF,kBAAkB,GAAG3E,QAAQ,CAAC0D,OAAO,CAAC,GACxC,CAAC,aAAa,EAAE,oBAAoB,CAAC,GACrC,CAAC,oBAAoB,EAAE,aAAa,CAAC;MAEzC,KAAK,MAAMC,IAAI,IAAIgB,kBAAkB,EAAE;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI9D,MAAM,CAAC2D,IAAI,CAAC/F,MAAM,CAAC8C,cAAc,EAAEoC,IAAI,CAAC,EAAE;UAC5C,OAAO7C,MAAM,CAAC4D,MAAM,CAACX,WAAW,EAAE;YAAEJ;UAAK,CAAC,CAAC;QAC7C;MACF;MAEA,OAAOI,WAAW;IACpB,CAAC;;IAED;IACA;IACA;IACA,IAAIa,kBAAkB,GAAG,EAAE;IAC3B,IAAIC,iBAAiB,GAAG,SAAAA,CAASC,OAAO,EAAE;MACxC,IAAIC,kBAAkB,GAAG,CAAC,CAAC;MAC3B,CAACH,kBAAkB,IAAI,EAAE,EAAEI,OAAO,CAAC,UAASC,IAAI,EAAE;QAChD,IAAIC,UAAU,GAAGD,IAAI,CAACH,OAAO,CAAC;QAC9B,IAAII,UAAU,KAAK,IAAI,EAAE;QACzB,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAChC,MAAMC,KAAK,CAAC,gDAAgD,CAAC;QAC/DrE,MAAM,CAAC4D,MAAM,CAACK,kBAAkB,EAAEG,UAAU,CAAC;MAC/C,CAAC,CAAC;MACF,OAAOH,kBAAkB;IAC3B,CAAC;IACDtG,MAAM,CAAC2G,oBAAoB,GAAG,UAASH,IAAI,EAAE;MAC3CL,kBAAkB,CAACS,IAAI,CAACJ,IAAI,CAAC;IAC/B,CAAC;;IAED;IACA,IAAIK,MAAM,GAAG,SAAAA,CAAS5D,GAAG,EAAE;MACzB,IAAIA,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,aAAa,EAAE,OAAO,KAAK;;MAEjE;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,GAAG,KAAK,eAAe,EAAE,OAAO,KAAK;;MAEzC;MACA,IAAI6D,WAAW,CAACC,QAAQ,CAAC9D,GAAG,CAAC,EAAE,OAAO,KAAK;;MAE3C;MACA,OAAO,IAAI;IACb,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA+D,MAAM,CAACC,OAAO,CAAC,YAAW;MACxB,SAASC,MAAMA,CAACC,GAAG,EAAE;QACnB,OAAO,UAASjC,IAAI,EAAE;UACpBA,IAAI,GAAGA,IAAI,IAAIlF,MAAM,CAAC6C,WAAW;UACjC,MAAMuE,OAAO,GAAGpH,MAAM,CAAC8C,cAAc,CAACoC,IAAI,CAAC;UAC3C,MAAMmC,KAAK,GAAGD,OAAO,IAAIA,OAAO,CAACD,GAAG,CAAC;UACrC;UACA;UACA;UACA,OAAO,OAAOE,KAAK,KAAK,UAAU,GAAID,OAAO,CAACD,GAAG,CAAC,GAAGE,KAAK,CAAC,CAAC,GAAIA,KAAK;QACvE,CAAC;MACH;MAEArH,MAAM,CAACsH,mBAAmB,GAAGtH,MAAM,CAACuH,UAAU,GAAGL,MAAM,CAAC,SAAS,CAAC;MAClElH,MAAM,CAACwH,8BAA8B,GAAGN,MAAM,CAAC,oBAAoB,CAAC;MACpElH,MAAM,CAACyH,iCAAiC,GAAGP,MAAM,CAAC,uBAAuB,CAAC;MAC1ElH,MAAM,CAAC0H,8BAA8B,GAAGR,MAAM,CAAC,oBAAoB,CAAC;MACpElH,MAAM,CAAC2H,oBAAoB,GAAGT,MAAM,CAAC,mBAAmB,CAAC;IAC3D,CAAC,CAAC;;IAEF;IACA;IACA;IACA;IACA;IACAlH,MAAM,CAAC4H,iCAAiC,GAAG,UAASjE,GAAG,EAAEC,GAAG,EAAE;MAC5D;MACAD,GAAG,CAACkE,UAAU,CAAC7F,mBAAmB,CAAC;MACnC;MACA;MACA,IAAI8F,eAAe,GAAGlE,GAAG,CAACmE,SAAS,CAAC,QAAQ,CAAC;MAC7C;MACA;MACA;MACA;MACAnE,GAAG,CAACoE,kBAAkB,CAAC,QAAQ,CAAC;MAChCpE,GAAG,CAACqE,EAAE,CAAC,QAAQ,EAAE,YAAW;QAC1BrE,GAAG,CAACiE,UAAU,CAAC9F,oBAAoB,CAAC;MACtC,CAAC,CAAC;MACFM,MAAM,CAAC6F,MAAM,CAACJ,eAAe,CAAC,CAACvB,OAAO,CAAC,UAAS4B,CAAC,EAAE;QACjDvE,GAAG,CAACqE,EAAE,CAAC,QAAQ,EAAEE,CAAC,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA,IAAIC,iBAAiB,GAAG,CAAC,CAAC;;IAE1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,wBAAwB,GAAGhG,MAAM,CAACiG,MAAM,CAAC,IAAI,CAAC;IACpDrI,eAAe,CAACsI,+BAA+B,GAAG,UAASpB,GAAG,EAAEqB,QAAQ,EAAE;MACxE,MAAMC,gBAAgB,GAAGJ,wBAAwB,CAAClB,GAAG,CAAC;MAEtD,IAAI,OAAOqB,QAAQ,KAAK,UAAU,EAAE;QAClCH,wBAAwB,CAAClB,GAAG,CAAC,GAAGqB,QAAQ;MAC1C,CAAC,MAAM;QACLrI,MAAM,CAACuI,WAAW,CAACF,QAAQ,EAAE,IAAI,CAAC;QAClC,OAAOH,wBAAwB,CAAClB,GAAG,CAAC;MACtC;;MAEA;MACA;MACA,OAAOsB,gBAAgB,IAAI,IAAI;IACjC,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASE,cAAcA,CAACtC,OAAO,EAAEnB,IAAI,EAAE;MACrC,OAAO0D,mBAAmB,CAACvC,OAAO,EAAEnB,IAAI,CAAC;IAC3C;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACAlF,MAAM,CAAC6I,mBAAmB,GAAG,UAASC,WAAW,EAAE;MACjD,OAAOC,IAAI,CAACC,SAAS,CAACC,kBAAkB,CAACF,IAAI,CAACC,SAAS,CAACF,WAAW,CAAC,CAAC,CAAC;IACxE,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;IACA9I,MAAM,CAACkJ,mBAAmB,GAAG,UAASC,cAAc,EAAE;MACpD,OAAOJ,IAAI,CAACjI,KAAK,CAACsI,kBAAkB,CAACL,IAAI,CAACjI,KAAK,CAACqI,cAAc,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,MAAME,aAAa,GAAG;MACpB;MACA;MACAC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC;MACjB;MACA;MACAC,WAAW,EAAE,IAAID,IAAI,CAAC,CAAC;MACvB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAE,eAAe,EAAE,CAAC;IACpB,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAzJ,MAAM,CAAC0J,oBAAoB,GAAG,UAASlB,QAAQ,EAAE;MAC/C,OAAOa,aAAa,CAACC,KAAK,CAACK,QAAQ,CAACnB,QAAQ,CAAC;IAC/C,CAAC;IAED,eAAeI,mBAAmBA,CAACvC,OAAO,EAAEnB,IAAI,EAAE;MAChD,IAAI0E,WAAW,GAAGxB,iBAAiB,CAAClD,IAAI,CAAC;MACzC,MAAMmE,aAAa,CAACC,KAAK,CAACO,YAAY,CAAC,MAAMrD,IAAI,IAAI;QACnD,MAAMsD,mBAAmB,GAAG,MAAMtD,IAAI,CAAC;UACrCtB,IAAI;UACJmB,OAAO;UACP0D,oBAAoB,EAAEH,WAAW,CAACI,QAAQ,CAACF,mBAAmB;UAC9DG,OAAO,EAAEZ,aAAa,CAACI,eAAe,CAACvE,IAAI;QAC7C,CAAC,CAAC;QACF,IAAI,CAAC4E,mBAAmB,EAAE,OAAO,IAAI;QACrCF,WAAW,CAACI,QAAQ,GAAG3H,MAAM,CAAC4D,MAAM,CAAC,CAAC,CAAC,EAAE2D,WAAW,CAACI,QAAQ,EAAE;UAC7DF;QACF,CAAC,CAAC;QACF,OAAO,IAAI;MACb,CAAC,CAAC;MACFT,aAAa,CAACI,eAAe,CAACvE,IAAI,CAAC,GAAG,KAAK;MAC3C,MAAM;QAAEK,WAAW;QAAEC;MAAY,CAAC,GAAGa,OAAO;MAC5C,MAAM6D,IAAI,GAAG7H,MAAM,CAAC4D,MAAM,CACxB,CAAC,CAAC,EACF2D,WAAW,CAACI,QAAQ,EACpB;QACEG,cAAc,EAAE/D,iBAAiB,CAACC,OAAO;MAC3C,CAAC,EACD;QAAEd,WAAW;QAAEC;MAAY,CAC7B,CAAC;MAED,IAAI4E,WAAW,GAAG,KAAK;MACvB,IAAIC,OAAO,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;MAE/BlI,MAAM,CAACmI,IAAI,CAACnC,wBAAwB,CAAC,CAAC9B,OAAO,CAACY,GAAG,IAAI;QACnDkD,OAAO,GAAGA,OAAO,CACdI,IAAI,CAAC,MAAM;UACV,MAAMjC,QAAQ,GAAGH,wBAAwB,CAAClB,GAAG,CAAC;UAC9C,OAAOqB,QAAQ,CAACnC,OAAO,EAAE6D,IAAI,EAAEhF,IAAI,CAAC;QACtC,CAAC,CAAC,CACDuF,IAAI,CAACC,MAAM,IAAI;UACd;UACA,IAAIA,MAAM,KAAK,KAAK,EAAE;YACpBN,WAAW,GAAG,IAAI;UACpB;QACF,CAAC,CAAC;MACN,CAAC,CAAC;MAEF,OAAOC,OAAO,CAACI,IAAI,CAAC,OAAO;QACzBE,MAAM,EAAEf,WAAW,CAACgB,YAAY,CAACV,IAAI,CAAC;QACtCW,UAAU,EAAEX,IAAI,CAACW,UAAU;QAC3BhH,OAAO,EAAEqG,IAAI,CAACrG;MAChB,CAAC,CAAC,CAAC;IACL;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA7D,MAAM,CAAC8K,oBAAoB,GAAG,UAASC,OAAO,EAAE;MAC9C,OAAO1B,aAAa,CAACG,WAAW,CAACG,QAAQ,CAACoB,OAAO,CAAC;IACpD,CAAC;IAED9K,eAAe,CAAC+K,2BAA2B,GAAG,UAC5C9F,IAAI,EACJ+F,QAAQ,EACRC,iBAAiB,EACjB;MACAA,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC,CAAC;MAE3C7B,aAAa,CAACI,eAAe,CAACvE,IAAI,CAAC,GAAG,IAAI;MAC1C,MAAM4D,WAAW,GAAApJ,aAAA,CAAAA,aAAA,KACZyD,yBAAyB,GACxB+H,iBAAiB,CAACC,sBAAsB,IAAI,CAAC,CAAC,CACnD;MACD9B,aAAa,CAACG,WAAW,CAACjD,OAAO,CAAC6E,EAAE,IAAI;QACtCA,EAAE,CAAC;UAAElG,IAAI;UAAE+F,QAAQ;UAAE5B,aAAa,EAAEP;QAAY,CAAC,CAAC;QAClD,OAAO,IAAI;MACb,CAAC,CAAC;MAEF,MAAMgB,mBAAmB,GAAGf,IAAI,CAACC,SAAS,CACxCC,kBAAkB,CAACF,IAAI,CAACC,SAAS,CAACF,WAAW,CAAC,CAChD,CAAC;MAED,OAAO,IAAIuC,WAAW,CACpBnG,IAAI,EACJ+F,QAAQ,EACR5I,MAAM,CAAC4D,MAAM,CACX;QACEqF,UAAUA,CAACC,QAAQ,EAAE;UACnB,OAAO9K,QAAQ,CAACsC,QAAQ,CAACmC,IAAI,CAAC,EAAEqG,QAAQ,CAAC;QAC3C,CAAC;QACDC,iBAAiB,EAAE;UACjBC,kBAAkB,EAAE,CAACpJ,MAAM,CAACqJ,OAAO,CAACD,kBAAkB,CAAC,IAAI,EAAE,EAAEE,GAAG,CAAC,UAAAC,IAAA,EAEjE;YAAA,IADA,CAACvG,QAAQ,EAAE/B,QAAQ,CAAC,GAAAsI,IAAA;YAEpB,OAAO;cACLvG,QAAQ,EAAEA,QAAQ;cAClB/B,QAAQ,EAAEA;YACZ,CAAC;UACH,CAAC,CAAC;UACF;UACA;UACA;UACA;UACA;UACA;UACAwG,mBAAmB;UACnB+B,iBAAiB,EAAExI,IAAI,CAACyG,mBAAmB,CAAC;UAC5CgC,iBAAiB,EACf3I,yBAAyB,CAACC,oBAAoB,IAAI,EAAE;UACtDJ,0BAA0B,EAAEA,0BAA0B;UACtD+I,OAAO,EAAEA,OAAO;UAChBC,oBAAoB,EAAE/L,eAAe,CAAC+L,oBAAoB,CAAC,CAAC;UAC5DC,MAAM,EAAEf,iBAAiB,CAACe;QAC5B;MACF,CAAC,EACDf,iBACF,CACF,CAAC;IACH,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACAjL,eAAe,CAACiM,qBAAqB,GAAG,gBACtCC,iBAAiB,EACjBxI,GAAG,EACHC,GAAG,EACHwI,IAAI,EACJ;MAAA,IAAAC,sBAAA,EAAAC,sBAAA;MACA,IAAIjH,QAAQ,GAAGjE,YAAY,CAACuC,GAAG,CAAC,CAAC0B,QAAQ;MACzC,IAAI;QACFA,QAAQ,GAAG+D,kBAAkB,CAAC/D,QAAQ,CAAC;MACzC,CAAC,CAAC,OAAOkH,CAAC,EAAE;QACVH,IAAI,CAAC,CAAC;QACN;MACF;MAEA,IAAII,aAAa,GAAG,SAAAA,CAASC,CAAC,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAC9B,IACEhJ,GAAG,CAACiJ,MAAM,KAAK,KAAK,IACpBjJ,GAAG,CAACiJ,MAAM,KAAK,MAAM,KAAAF,qBAAA,GACrB1F,MAAM,CAAC6F,QAAQ,CAACC,QAAQ,cAAAJ,qBAAA,gBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BK,MAAM,cAAAJ,sBAAA,eAAhCA,sBAAA,CAAkCK,mBAAmB,EACrD;UACApJ,GAAG,CAACqJ,SAAS,CAAC,GAAG,EAAE;YACjB,cAAc,EAAE,uCAAuC;YACvD,gBAAgB,EAAEC,MAAM,CAACC,UAAU,CAACV,CAAC;UACvC,CAAC,CAAC;UACF7I,GAAG,CAACwJ,KAAK,CAACX,CAAC,CAAC;UACZ7I,GAAG,CAACyJ,GAAG,CAAC,CAAC;QACX,CAAC,MAAM;UACL,MAAMC,MAAM,GAAG3J,GAAG,CAACiJ,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG;UACnDhJ,GAAG,CAACqJ,SAAS,CAACK,MAAM,EAAE;YACpBC,KAAK,EAAE,oBAAoB;YAC3B,gBAAgB,EAAE;UACpB,CAAC,CAAC;UACF3J,GAAG,CAACyJ,GAAG,CAAC,CAAC;QACX;MACF,CAAC;MAED,IACEhI,QAAQ,IAAIoG,kBAAkB,IAC9B,CAACxL,eAAe,CAAC+L,oBAAoB,CAAC,CAAC,EACvC;QACAQ,aAAa,CAACf,kBAAkB,CAACpG,QAAQ,CAAC,CAAC;QAC3C;MACF;MAEA,MAAM;QAAEH,IAAI;QAAEE;MAAK,CAAC,GAAGpF,MAAM,CAACgF,iBAAiB,CAACrB,GAAG,CAAC;MAEpD,IAAI,CAACvB,MAAM,CAAC2D,IAAI,CAAC/F,MAAM,CAAC8C,cAAc,EAAEoC,IAAI,CAAC,EAAE;QAC7C;QACAkH,IAAI,CAAC,CAAC;QACN;MACF;;MAEA;MACA;MACA,MAAMhF,OAAO,GAAGpH,MAAM,CAAC8C,cAAc,CAACoC,IAAI,CAAC;MAC3C,MAAMkC,OAAO,CAACoG,MAAM;MAEpB,IACEpI,IAAI,KAAK,2BAA2B,IACpC,CAACnF,eAAe,CAAC+L,oBAAoB,CAAC,CAAC,EACvC;QACAQ,aAAa,gCAAAiB,MAAA,CACoBrG,OAAO,CAAC0C,mBAAmB,MAC5D,CAAC;QACD;MACF;MAEA,MAAM4D,IAAI,GAAGC,iBAAiB,CAACxB,iBAAiB,EAAE9G,QAAQ,EAAED,IAAI,EAAEF,IAAI,CAAC;MACvE,IAAI,CAACwI,IAAI,EAAE;QACTtB,IAAI,CAAC,CAAC;QACN;MACF;MACA;MACA,IACEzI,GAAG,CAACiJ,MAAM,KAAK,MAAM,IACrBjJ,GAAG,CAACiJ,MAAM,KAAK,KAAK,IACpB,GAAAP,sBAAA,GAACrF,MAAM,CAAC6F,QAAQ,CAACC,QAAQ,cAAAT,sBAAA,gBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BU,MAAM,cAAAT,sBAAA,eAAhCA,sBAAA,CAAkCU,mBAAmB,GACtD;QACA,MAAMM,MAAM,GAAG3J,GAAG,CAACiJ,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG;QACnDhJ,GAAG,CAACqJ,SAAS,CAACK,MAAM,EAAE;UACpBC,KAAK,EAAE,oBAAoB;UAC3B,gBAAgB,EAAE;QACpB,CAAC,CAAC;QACF3J,GAAG,CAACyJ,GAAG,CAAC,CAAC;QACT;MACF;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA,MAAMO,MAAM,GAAGF,IAAI,CAACG,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;MAE7D,IAAIH,IAAI,CAACG,SAAS,EAAE;QAClB;QACA;QACA;QACA;QACAjK,GAAG,CAACkK,SAAS,CAAC,MAAM,EAAE,YAAY,CAAC;MACrC;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIJ,IAAI,CAACK,YAAY,EAAE;QACrBnK,GAAG,CAACkK,SAAS,CACX,aAAa,EACb3K,yBAAyB,CAACC,oBAAoB,GAAGsK,IAAI,CAACK,YACxD,CAAC;MACH;MAEA,IAAIL,IAAI,CAACM,IAAI,KAAK,IAAI,IAAIN,IAAI,CAACM,IAAI,KAAK,YAAY,EAAE;QACpDpK,GAAG,CAACkK,SAAS,CAAC,cAAc,EAAE,uCAAuC,CAAC;MACxE,CAAC,MAAM,IAAIJ,IAAI,CAACM,IAAI,KAAK,KAAK,EAAE;QAC9BpK,GAAG,CAACkK,SAAS,CAAC,cAAc,EAAE,yBAAyB,CAAC;MAC1D,CAAC,MAAM,IAAIJ,IAAI,CAACM,IAAI,KAAK,MAAM,EAAE;QAC/BpK,GAAG,CAACkK,SAAS,CAAC,cAAc,EAAE,iCAAiC,CAAC;MAClE;MAEA,IAAIJ,IAAI,CAACnK,IAAI,EAAE;QACbK,GAAG,CAACkK,SAAS,CAAC,MAAM,EAAE,GAAG,GAAGJ,IAAI,CAACnK,IAAI,GAAG,GAAG,CAAC;MAC9C;MAEA,IAAImK,IAAI,CAACO,OAAO,EAAE;QAChBrK,GAAG,CAACkK,SAAS,CAAC,gBAAgB,EAAEZ,MAAM,CAACC,UAAU,CAACO,IAAI,CAACO,OAAO,CAAC,CAAC;QAChErK,GAAG,CAACwJ,KAAK,CAACM,IAAI,CAACO,OAAO,CAAC;QACvBrK,GAAG,CAACyJ,GAAG,CAAC,CAAC;MACX,CAAC,MAAM;QACL7L,IAAI,CAACmC,GAAG,EAAE+J,IAAI,CAACQ,YAAY,EAAE;UAC3BC,MAAM,EAAEP,MAAM;UACdQ,QAAQ,EAAE,OAAO;UAAE;UACnBC,YAAY,EAAE,KAAK,CAAE;QACvB,CAAC,CAAC,CACCpG,EAAE,CAAC,OAAO,EAAE,UAASqG,GAAG,EAAE;UACzBC,GAAG,CAACC,KAAK,CAAC,4BAA4B,GAAGF,GAAG,CAAC;UAC7C1K,GAAG,CAACqJ,SAAS,CAAC,GAAG,CAAC;UAClBrJ,GAAG,CAACyJ,GAAG,CAAC,CAAC;QACX,CAAC,CAAC,CACDpF,EAAE,CAAC,WAAW,EAAE,YAAW;UAC1BsG,GAAG,CAACC,KAAK,CAAC,uBAAuB,GAAGd,IAAI,CAACQ,YAAY,CAAC;UACtDtK,GAAG,CAACqJ,SAAS,CAAC,GAAG,CAAC;UAClBrJ,GAAG,CAACyJ,GAAG,CAAC,CAAC;QACX,CAAC,CAAC,CACDoB,IAAI,CAAC7K,GAAG,CAAC;MACd;IACF,CAAC;IAED,SAAS+J,iBAAiBA,CAACxB,iBAAiB,EAAEuC,YAAY,EAAEtJ,IAAI,EAAEF,IAAI,EAAE;MACtE,IAAI,CAAC9C,MAAM,CAAC2D,IAAI,CAAC/F,MAAM,CAAC8C,cAAc,EAAEoC,IAAI,CAAC,EAAE;QAC7C,OAAO,IAAI;MACb;;MAEA;MACA;MACA,MAAMyJ,cAAc,GAAGtM,MAAM,CAACmI,IAAI,CAAC2B,iBAAiB,CAAC;MACrD,MAAMyC,SAAS,GAAGD,cAAc,CAACE,OAAO,CAAC3J,IAAI,CAAC;MAC9C,IAAI0J,SAAS,GAAG,CAAC,EAAE;QACjBD,cAAc,CAACG,OAAO,CAACH,cAAc,CAAC3I,MAAM,CAAC4I,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE;MAEA,IAAIlB,IAAI,GAAG,IAAI;MAEfiB,cAAc,CAACI,IAAI,CAAC7J,IAAI,IAAI;QAC1B,MAAM8J,WAAW,GAAG7C,iBAAiB,CAACjH,IAAI,CAAC;QAE3C,SAAS+J,QAAQA,CAAC7J,IAAI,EAAE;UACtBsI,IAAI,GAAGsB,WAAW,CAAC5J,IAAI,CAAC;UACxB;UACA;UACA,IAAI,OAAOsI,IAAI,KAAK,UAAU,EAAE;YAC9BA,IAAI,GAAGsB,WAAW,CAAC5J,IAAI,CAAC,GAAGsI,IAAI,CAAC,CAAC;UACnC;UACA,OAAOA,IAAI;QACb;;QAEA;QACA;QACA,IAAItL,MAAM,CAAC2D,IAAI,CAACiJ,WAAW,EAAEN,YAAY,CAAC,EAAE;UAC1C,OAAOO,QAAQ,CAACP,YAAY,CAAC;QAC/B;;QAEA;QACA,IAAItJ,IAAI,KAAKsJ,YAAY,IAAItM,MAAM,CAAC2D,IAAI,CAACiJ,WAAW,EAAE5J,IAAI,CAAC,EAAE;UAC3D,OAAO6J,QAAQ,CAAC7J,IAAI,CAAC;QACvB;MACF,CAAC,CAAC;MAEF,OAAOsI,IAAI;IACb;;IAEA;IACA;IACA;IACA;IACA;IACA;IACAzN,eAAe,CAACiP,SAAS,GAAGC,IAAI,IAAI;MAClC,IAAIC,UAAU,GAAGC,QAAQ,CAACF,IAAI,CAAC;MAC/B,IAAIG,MAAM,CAACC,KAAK,CAACH,UAAU,CAAC,EAAE;QAC5BA,UAAU,GAAGD,IAAI;MACnB;MACA,OAAOC,UAAU;IACnB,CAAC;IAIDvN,SAAS,CAAC,qBAAqB,EAAE,MAAA2N,KAAA,IAAoB;MAAA,IAAb;QAAEtK;MAAK,CAAC,GAAAsK,KAAA;MAC9C,MAAMvP,eAAe,CAACwP,WAAW,CAACvK,IAAI,CAAC;IACzC,CAAC,CAAC;IAEFrD,SAAS,CAAC,sBAAsB,EAAE,MAAA6N,KAAA,IAAoB;MAAA,IAAb;QAAExK;MAAK,CAAC,GAAAwK,KAAA;MAC/C,MAAMzP,eAAe,CAAC0P,qBAAqB,CAACzK,IAAI,CAAC;IACnD,CAAC,CAAC;IAEF,eAAe0K,eAAeA,CAAA,EAAG;MAC/B,IAAIC,YAAY,GAAG,KAAK;MACxB,IAAIC,SAAS,GAAG,IAAI9I,MAAM,CAAC+I,kBAAkB,CAAC,CAAC;MAE/C,IAAIC,eAAe,GAAG,SAAAA,CAASC,OAAO,EAAE;QACtC,OAAO7G,kBAAkB,CAACvI,QAAQ,CAACoP,OAAO,CAAC,CAAC5K,QAAQ,CAAC;MACvD,CAAC;MAEDpF,eAAe,CAACiQ,oBAAoB,GAAG,kBAAiB;QACtD,MAAMJ,SAAS,CAACK,OAAO,CAAC,YAAW;UACjC,MAAMhE,iBAAiB,GAAG9J,MAAM,CAACiG,MAAM,CAAC,IAAI,CAAC;UAE7C,MAAM;YAAE8H;UAAW,CAAC,GAAGC,oBAAoB;UAC3C,MAAMC,WAAW,GACfF,UAAU,CAACE,WAAW,IAAIjO,MAAM,CAACmI,IAAI,CAAC4F,UAAU,CAACG,WAAW,CAAC;UAE/D,IAAI;YACFD,WAAW,CAAC/J,OAAO,CAACrB,IAAI,IAAI;cAC1ByK,qBAAqB,CAACzK,IAAI,EAAEiH,iBAAiB,CAAC;YAChD,CAAC,CAAC;YACFlM,eAAe,CAACkM,iBAAiB,GAAGA,iBAAiB;UACvD,CAAC,CAAC,OAAOI,CAAC,EAAE;YACVgC,GAAG,CAACC,KAAK,CAAC,sCAAsC,GAAGjC,CAAC,CAACiE,KAAK,CAAC;YAC3DC,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;UACjB;QACF,CAAC,CAAC;MACJ,CAAC;;MAED;MACA;MACAzQ,eAAe,CAACwP,WAAW,GAAG,gBAAevK,IAAI,EAAE;QACjD,MAAM4K,SAAS,CAACK,OAAO,CAAC,MAAM;UAC5B,MAAM/I,OAAO,GAAGpH,MAAM,CAAC8C,cAAc,CAACoC,IAAI,CAAC;UAC3C,MAAM;YAAEyL;UAAQ,CAAC,GAAGvJ,OAAO;UAC3BA,OAAO,CAACoG,MAAM,GAAG,IAAIlD,OAAO,CAACC,OAAO,IAAI;YACtC,IAAI,OAAOoG,OAAO,KAAK,UAAU,EAAE;cACjC;cACA;cACAvJ,OAAO,CAACuJ,OAAO,GAAG,YAAW;gBAC3BA,OAAO,CAAC,CAAC;gBACTpG,OAAO,CAAC,CAAC;cACX,CAAC;YACH,CAAC,MAAM;cACLnD,OAAO,CAACuJ,OAAO,GAAGpG,OAAO;YAC3B;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MAEDtK,eAAe,CAAC0P,qBAAqB,GAAG,gBAAezK,IAAI,EAAE;QAC3D,MAAM4K,SAAS,CAACK,OAAO,CAAC,MAAMR,qBAAqB,CAACzK,IAAI,CAAC,CAAC;MAC5D,CAAC;MAED,SAASyK,qBAAqBA,CAC5BzK,IAAI,EAEJ;QAAA,IADAiH,iBAAiB,GAAAyE,SAAA,CAAAvM,MAAA,QAAAuM,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG3Q,eAAe,CAACkM,iBAAiB;QAErD,MAAM2E,SAAS,GAAGrQ,QAAQ,CACxBC,WAAW,CAAC2P,oBAAoB,CAACU,SAAS,CAAC,EAC3C7L,IACF,CAAC;;QAED;QACA,MAAM8L,eAAe,GAAGvQ,QAAQ,CAACqQ,SAAS,EAAE,cAAc,CAAC;QAE3D,IAAIG,WAAW;QACf,IAAI;UACFA,WAAW,GAAGlI,IAAI,CAACjI,KAAK,CAACV,YAAY,CAAC4Q,eAAe,CAAC,CAAC;QACzD,CAAC,CAAC,OAAOzE,CAAC,EAAE;UACV,IAAIA,CAAC,CAAC2E,IAAI,KAAK,QAAQ,EAAE;UACzB,MAAM3E,CAAC;QACT;QAEA,IAAI0E,WAAW,CAACE,MAAM,KAAK,kBAAkB,EAAE;UAC7C,MAAM,IAAIzK,KAAK,CACb,wCAAwC,GACtCqC,IAAI,CAACC,SAAS,CAACiI,WAAW,CAACE,MAAM,CACrC,CAAC;QACH;QAEA,IAAI,CAACH,eAAe,IAAI,CAACF,SAAS,IAAI,CAACG,WAAW,EAAE;UAClD,MAAM,IAAIvK,KAAK,CAAC,gCAAgC,CAAC;QACnD;QAEA3D,QAAQ,CAACmC,IAAI,CAAC,GAAG4L,SAAS;QAC1B,MAAM9B,WAAW,GAAI7C,iBAAiB,CAACjH,IAAI,CAAC,GAAG7C,MAAM,CAACiG,MAAM,CAAC,IAAI,CAAE;QAEnE,MAAM;UAAE2C;QAAS,CAAC,GAAGgG,WAAW;QAChChG,QAAQ,CAAC1E,OAAO,CAAC6K,IAAI,IAAI;UACvB,IAAIA,IAAI,CAACnO,GAAG,IAAImO,IAAI,CAACC,KAAK,KAAK,QAAQ,EAAE;YACvCrC,WAAW,CAACgB,eAAe,CAACoB,IAAI,CAACnO,GAAG,CAAC,CAAC,GAAG;cACvCiL,YAAY,EAAEzN,QAAQ,CAACqQ,SAAS,EAAEM,IAAI,CAAChM,IAAI,CAAC;cAC5CyI,SAAS,EAAEuD,IAAI,CAACvD,SAAS;cACzBtK,IAAI,EAAE6N,IAAI,CAAC7N,IAAI;cACf;cACAwK,YAAY,EAAEqD,IAAI,CAACrD,YAAY;cAC/BC,IAAI,EAAEoD,IAAI,CAACpD;YACb,CAAC;YAED,IAAIoD,IAAI,CAACE,SAAS,EAAE;cAClB;cACA;cACAtC,WAAW,CAACgB,eAAe,CAACoB,IAAI,CAACrD,YAAY,CAAC,CAAC,GAAG;gBAChDG,YAAY,EAAEzN,QAAQ,CAACqQ,SAAS,EAAEM,IAAI,CAACE,SAAS,CAAC;gBACjDzD,SAAS,EAAE;cACb,CAAC;YACH;UACF;QACF,CAAC,CAAC;QAEF,MAAM;UAAE0D;QAAgB,CAAC,GAAGpO,yBAAyB;QACrD,MAAMqO,eAAe,GAAG;UACtBD;QACF,CAAC;QAED,MAAME,UAAU,GAAGzR,MAAM,CAAC8C,cAAc,CAACoC,IAAI,CAAC;QAC9C,MAAMwM,UAAU,GAAI1R,MAAM,CAAC8C,cAAc,CAACoC,IAAI,CAAC,GAAG;UAChDiM,MAAM,EAAE,kBAAkB;UAC1BlG,QAAQ,EAAEA,QAAQ;UAClB;UACA;UACA;UACA;UACA;UACA;UACA;UACAxI,OAAO,EAAEA,CAAA,KACPkP,aAAa,CAACrK,mBAAmB,CAAC2D,QAAQ,EAAE,IAAI,EAAEuG,eAAe,CAAC;UACpEI,kBAAkB,EAAEA,CAAA,KAClBD,aAAa,CAACrK,mBAAmB,CAC/B2D,QAAQ,EACR+C,IAAI,IAAIA,IAAI,KAAK,KAAK,EACtBwD,eACF,CAAC;UACHK,qBAAqB,EAAEA,CAAA,KACrBF,aAAa,CAACrK,mBAAmB,CAC/B2D,QAAQ,EACR,CAAC+C,IAAI,EAAE8D,WAAW,KAAK9D,IAAI,KAAK,KAAK,IAAI,CAAC8D,WAAW,EACrDN,eACF,CAAC;UACHO,kBAAkB,EAAEA,CAAA,KAClBJ,aAAa,CAACrK,mBAAmB,CAC/B2D,QAAQ,EACR,CAAC+G,KAAK,EAAEF,WAAW,KAAKA,WAAW,EACnCN,eACF,CAAC;UACHS,4BAA4B,EAAEhB,WAAW,CAACgB,4BAA4B;UACtEV,eAAe;UACfW,UAAU,EAAEjB,WAAW,CAACiB;QAC1B,CAAE;;QAEF;QACA,MAAMC,iBAAiB,GAAG,KAAK,GAAGjN,IAAI,CAACkN,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QAC5D,MAAMC,WAAW,GAAGF,iBAAiB,GAAGnC,eAAe,CAAC,gBAAgB,CAAC;QAEzEhB,WAAW,CAACqD,WAAW,CAAC,GAAG,MAAM;UAC/B,IAAIC,OAAO,CAACC,UAAU,EAAE;YACtB,MAAM;cACJC,kBAAkB,GAAGF,OAAO,CAACC,UAAU,CAACE,UAAU,CAACC;YACrD,CAAC,GAAGjC,OAAO,CAACkC,GAAG;YAEf,IAAIH,kBAAkB,EAAE;cACtBd,UAAU,CAACjP,OAAO,GAAG+P,kBAAkB;YACzC;UACF;UAEA,IAAI,OAAOd,UAAU,CAACjP,OAAO,KAAK,UAAU,EAAE;YAC5CiP,UAAU,CAACjP,OAAO,GAAGiP,UAAU,CAACjP,OAAO,CAAC,CAAC;UAC3C;UAEA,OAAO;YACLwL,OAAO,EAAElF,IAAI,CAACC,SAAS,CAAC0I,UAAU,CAAC;YACnC7D,SAAS,EAAE,KAAK;YAChBtK,IAAI,EAAEmO,UAAU,CAACjP,OAAO;YACxBuL,IAAI,EAAE;UACR,CAAC;QACH,CAAC;QAED4E,0BAA0B,CAAC1N,IAAI,CAAC;;QAEhC;QACA;QACA,IAAIuM,UAAU,IAAIA,UAAU,CAACjE,MAAM,EAAE;UACnCiE,UAAU,CAACd,OAAO,CAAC,CAAC;QACtB;MACF;MAEA,MAAMkC,qBAAqB,GAAG;QAC5B,aAAa,EAAE;UACb1H,sBAAsB,EAAE;YACtB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA2H,0BAA0B,EACxBrC,OAAO,CAACkC,GAAG,CAACI,cAAc,IAAI/L,MAAM,CAACgM,WAAW,CAAC,CAAC;YACpDC,QAAQ,EAAExC,OAAO,CAACkC,GAAG,CAACO,eAAe,IAAIlM,MAAM,CAACgM,WAAW,CAAC;UAC9D;QACF,CAAC;QAED,aAAa,EAAE;UACb7H,sBAAsB,EAAE;YACtB5J,QAAQ,EAAE;UACZ;QACF,CAAC;QAED,oBAAoB,EAAE;UACpB4J,sBAAsB,EAAE;YACtB5J,QAAQ,EAAE;UACZ;QACF;MACF,CAAC;MAEDtB,eAAe,CAACkT,mBAAmB,GAAG,kBAAiB;QACrD;QACA;QACA;QACA;QACA,MAAMrD,SAAS,CAACK,OAAO,CAAC,YAAW;UACjC9N,MAAM,CAACmI,IAAI,CAACxK,MAAM,CAAC8C,cAAc,CAAC,CAACyD,OAAO,CAACqM,0BAA0B,CAAC;QACxE,CAAC,CAAC;MACJ,CAAC;MAED,SAASA,0BAA0BA,CAAC1N,IAAI,EAAE;QACxC,MAAMkC,OAAO,GAAGpH,MAAM,CAAC8C,cAAc,CAACoC,IAAI,CAAC;QAC3C,MAAMgG,iBAAiB,GAAG2H,qBAAqB,CAAC3N,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM;UAAE8E;QAAS,CAAC,GAAI5B,iBAAiB,CACrClD,IAAI,CACL,GAAGjF,eAAe,CAAC+K,2BAA2B,CAC7C9F,IAAI,EACJkC,OAAO,CAAC6D,QAAQ,EAChBC,iBACF,CAAE;QACF;QACA9D,OAAO,CAAC0C,mBAAmB,GAAGf,IAAI,CAACC,SAAS,CAAAtJ,aAAA,CAAAA,aAAA,KACvCyD,yBAAyB,GACxB+H,iBAAiB,CAACC,sBAAsB,IAAI,IAAI,CACrD,CAAC;QACF/D,OAAO,CAACgM,iBAAiB,GAAGpJ,QAAQ,CAACqJ,GAAG,CAAC1H,GAAG,CAAC2H,IAAI,KAAK;UACpDrQ,GAAG,EAAED,0BAA0B,CAACsQ,IAAI,CAACrQ,GAAG;QAC1C,CAAC,CAAC,CAAC;MACL;MAEA,MAAMhD,eAAe,CAACiQ,oBAAoB,CAAC,CAAC;;MAE5C;MACA,IAAIhO,GAAG,GAAGD,gBAAgB,CAAC,CAAC;;MAE5B;MACA;MACA,IAAIsR,kBAAkB,GAAGtR,gBAAgB,CAAC,CAAC;MAC3CC,GAAG,CAACsR,GAAG,CAACD,kBAAkB,CAAC;;MAE3B;MACArR,GAAG,CAACsR,GAAG,CAACvS,QAAQ,CAAC;QAAE6C,MAAM,EAAEJ;MAAe,CAAC,CAAC,CAAC;;MAE7C;MACAxB,GAAG,CAACsR,GAAG,CAACtS,YAAY,CAAC,CAAC,CAAC;;MAEvB;MACA;MACAgB,GAAG,CAACsR,GAAG,CAAC,UAAS7P,GAAG,EAAEC,GAAG,EAAEwI,IAAI,EAAE;QAC/B,IAAItF,WAAW,CAAC2M,UAAU,CAAC9P,GAAG,CAACV,GAAG,CAAC,EAAE;UACnCmJ,IAAI,CAAC,CAAC;UACN;QACF;QACAxI,GAAG,CAACqJ,SAAS,CAAC,GAAG,CAAC;QAClBrJ,GAAG,CAACwJ,KAAK,CAAC,aAAa,CAAC;QACxBxJ,GAAG,CAACyJ,GAAG,CAAC,CAAC;MACX,CAAC,CAAC;MAEF,SAASqG,YAAYA,CAACtO,IAAI,EAAE;QAC1B,MAAMnB,KAAK,GAAGmB,IAAI,CAAClB,KAAK,CAAC,GAAG,CAAC;QAC7B,OAAOD,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAEA,KAAK,CAAC0P,KAAK,CAAC,CAAC;QACrC,OAAO1P,KAAK;MACd;MAEA,SAAS2P,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;QACjC,OACED,MAAM,CAACxP,MAAM,IAAIyP,KAAK,CAACzP,MAAM,IAC7BwP,MAAM,CAACE,KAAK,CAAC,CAACC,IAAI,EAAE5P,CAAC,KAAK4P,IAAI,KAAKF,KAAK,CAAC1P,CAAC,CAAC,CAAC;MAEhD;;MAEA;MACAlC,GAAG,CAACsR,GAAG,CAAC,UAASnN,OAAO,EAAE4N,QAAQ,EAAE7H,IAAI,EAAE;QACxC,MAAM8H,UAAU,GAAG/Q,yBAAyB,CAACC,oBAAoB;QACjE,MAAM;UAAEiC,QAAQ;UAAE8O;QAAO,CAAC,GAAGtT,QAAQ,CAACwF,OAAO,CAACpD,GAAG,CAAC;;QAElD;QACA,IAAIiR,UAAU,EAAE;UACd,MAAME,WAAW,GAAGV,YAAY,CAACQ,UAAU,CAAC;UAC5C,MAAMxO,SAAS,GAAGgO,YAAY,CAACrO,QAAQ,CAAC;UACxC,IAAIuO,UAAU,CAACQ,WAAW,EAAE1O,SAAS,CAAC,EAAE;YACtCW,OAAO,CAACpD,GAAG,GAAG,GAAG,GAAGyC,SAAS,CAACI,KAAK,CAACsO,WAAW,CAAC/P,MAAM,CAAC,CAAC1D,IAAI,CAAC,GAAG,CAAC;YACjE,IAAIwT,MAAM,EAAE;cACV9N,OAAO,CAACpD,GAAG,IAAIkR,MAAM;YACvB;YACA,OAAO/H,IAAI,CAAC,CAAC;UACf;QACF;QAEA,IAAI/G,QAAQ,KAAK,cAAc,IAAIA,QAAQ,KAAK,aAAa,EAAE;UAC7D,OAAO+G,IAAI,CAAC,CAAC;QACf;QAEA,IAAI8H,UAAU,EAAE;UACdD,QAAQ,CAAChH,SAAS,CAAC,GAAG,CAAC;UACvBgH,QAAQ,CAAC7G,KAAK,CAAC,cAAc,CAAC;UAC9B6G,QAAQ,CAAC5G,GAAG,CAAC,CAAC;UACd;QACF;QAEAjB,IAAI,CAAC,CAAC;MACR,CAAC,CAAC;;MAEF;MACA;MACAlK,GAAG,CAACsR,GAAG,CAAC,UAAS7P,GAAG,EAAEC,GAAG,EAAEwI,IAAI,EAAE;QAC/B;QACAnM,eAAe,CAACiM,qBAAqB,CACnCjM,eAAe,CAACkM,iBAAiB,EACjCxI,GAAG,EACHC,GAAG,EACHwI,IACF,CAAC;MACH,CAAC,CAAC;;MAEF;MACA;MACAlK,GAAG,CAACsR,GAAG,CAAEvT,eAAe,CAACoU,sBAAsB,GAAGpS,gBAAgB,CAAC,CAAE,CAAC;;MAEtE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE;MACA;MACA,IAAIqS,qBAAqB,GAAGrS,gBAAgB,CAAC,CAAC;MAC9CC,GAAG,CAACsR,GAAG,CAACc,qBAAqB,CAAC;MAE9B,IAAIC,qBAAqB,GAAG,KAAK;MACjC;MACA;MACA;MACArS,GAAG,CAACsR,GAAG,CAAC,UAASlF,GAAG,EAAE3K,GAAG,EAAEC,GAAG,EAAEwI,IAAI,EAAE;QACpC,IAAI,CAACkC,GAAG,IAAI,CAACiG,qBAAqB,IAAI,CAAC5Q,GAAG,CAACE,OAAO,CAAC,kBAAkB,CAAC,EAAE;UACtEuI,IAAI,CAACkC,GAAG,CAAC;UACT;QACF;QACA1K,GAAG,CAACqJ,SAAS,CAACqB,GAAG,CAAChB,MAAM,EAAE;UAAE,cAAc,EAAE;QAAa,CAAC,CAAC;QAC3D1J,GAAG,CAACyJ,GAAG,CAAC,kBAAkB,CAAC;MAC7B,CAAC,CAAC;MAEFnL,GAAG,CAACsR,GAAG,CAAC,gBAAe7P,GAAG,EAAEC,GAAG,EAAEwI,IAAI,EAAE;QAAA,IAAAoI,sBAAA,EAAAC,sBAAA;QACrC,IAAI,CAAC5N,MAAM,CAAClD,GAAG,CAACV,GAAG,CAAC,EAAE;UACpB,OAAOmJ,IAAI,CAAC,CAAC;QACf,CAAC,MAAM,IACLzI,GAAG,CAACiJ,MAAM,KAAK,MAAM,IACrBjJ,GAAG,CAACiJ,MAAM,KAAK,KAAK,IACpB,GAAA4H,sBAAA,GAACxN,MAAM,CAAC6F,QAAQ,CAACC,QAAQ,cAAA0H,sBAAA,gBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BzH,MAAM,cAAA0H,sBAAA,eAAhCA,sBAAA,CAAkCzH,mBAAmB,GACtD;UACA,MAAMM,MAAM,GAAG3J,GAAG,CAACiJ,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,GAAG;UACnDhJ,GAAG,CAACqJ,SAAS,CAACK,MAAM,EAAE;YACpBC,KAAK,EAAE,oBAAoB;YAC3B,gBAAgB,EAAE;UACpB,CAAC,CAAC;UACF3J,GAAG,CAACyJ,GAAG,CAAC,CAAC;QACX,CAAC,MAAM;UACL,IAAIxJ,OAAO,GAAG;YACZ,cAAc,EAAE;UAClB,CAAC;UAED,IAAIgM,YAAY,EAAE;YAChBhM,OAAO,CAAC,YAAY,CAAC,GAAG,OAAO;UACjC;UAEA,IAAIwC,OAAO,GAAGrG,MAAM,CAACgF,iBAAiB,CAACrB,GAAG,CAAC;UAE3C,IAAI0C,OAAO,CAACpD,GAAG,CAACyR,KAAK,IAAIrO,OAAO,CAACpD,GAAG,CAACyR,KAAK,CAAC,qBAAqB,CAAC,EAAE;YACjE;YACA;YACA;YACA;YACA;YACA;YACA;YACA7Q,OAAO,CAAC,cAAc,CAAC,GAAG,yBAAyB;YACnDA,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU;YACrCD,GAAG,CAACqJ,SAAS,CAAC,GAAG,EAAEpJ,OAAO,CAAC;YAC3BD,GAAG,CAACwJ,KAAK,CAAC,4CAA4C,CAAC;YACvDxJ,GAAG,CAACyJ,GAAG,CAAC,CAAC;YACT;UACF;UAEA,IAAIhH,OAAO,CAACpD,GAAG,CAACyR,KAAK,IAAIrO,OAAO,CAACpD,GAAG,CAACyR,KAAK,CAAC,oBAAoB,CAAC,EAAE;YAChE;YACA;YACA;YACA;YACA7Q,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU;YACrCD,GAAG,CAACqJ,SAAS,CAAC,GAAG,EAAEpJ,OAAO,CAAC;YAC3BD,GAAG,CAACyJ,GAAG,CAAC,eAAe,CAAC;YACxB;UACF;UAEA,IAAIhH,OAAO,CAACpD,GAAG,CAACyR,KAAK,IAAIrO,OAAO,CAACpD,GAAG,CAACyR,KAAK,CAAC,yBAAyB,CAAC,EAAE;YACrE;YACA;YACA;YACA;YACA7Q,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU;YACrCD,GAAG,CAACqJ,SAAS,CAAC,GAAG,EAAEpJ,OAAO,CAAC;YAC3BD,GAAG,CAACyJ,GAAG,CAAC,eAAe,CAAC;YACxB;UACF;UAEA,MAAM;YAAEnI;UAAK,CAAC,GAAGmB,OAAO;UACxBlG,MAAM,CAACuI,WAAW,CAAC,OAAOxD,IAAI,EAAE,QAAQ,EAAE;YAAEA;UAAK,CAAC,CAAC;UAEnD,IAAI,CAAC9C,MAAM,CAAC2D,IAAI,CAAC/F,MAAM,CAAC8C,cAAc,EAAEoC,IAAI,CAAC,EAAE;YAC7C;YACArB,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU;YACrCD,GAAG,CAACqJ,SAAS,CAAC,GAAG,EAAEpJ,OAAO,CAAC;YAC3B,IAAImD,MAAM,CAAC2N,aAAa,EAAE;cACxB/Q,GAAG,CAACyJ,GAAG,oCAAAI,MAAA,CAAoCvI,IAAI,mBAAgB,CAAC;YAClE,CAAC,MAAM;cACL;cACAtB,GAAG,CAACyJ,GAAG,CAAC,eAAe,CAAC;YAC1B;YACA;UACF;;UAEA;UACA;UACA,MAAMrN,MAAM,CAAC8C,cAAc,CAACoC,IAAI,CAAC,CAACsI,MAAM;UAExC,OAAO5E,mBAAmB,CAACvC,OAAO,EAAEnB,IAAI,CAAC,CACtCuF,IAAI,CAACmK,KAAA,IAAiD;YAAA,IAAhD;cAAEjK,MAAM;cAAEE,UAAU;cAAEhH,OAAO,EAAEgR;YAAW,CAAC,GAAAD,KAAA;YAChD,IAAI,CAAC/J,UAAU,EAAE;cACfA,UAAU,GAAGjH,GAAG,CAACiH,UAAU,GAAGjH,GAAG,CAACiH,UAAU,GAAG,GAAG;YACpD;YAEA,IAAIgK,UAAU,EAAE;cACdxS,MAAM,CAAC4D,MAAM,CAACpC,OAAO,EAAEgR,UAAU,CAAC;YACpC;YAEAjR,GAAG,CAACqJ,SAAS,CAACpC,UAAU,EAAEhH,OAAO,CAAC;YAElC8G,MAAM,CAAC8D,IAAI,CAAC7K,GAAG,EAAE;cACf;cACAyJ,GAAG,EAAE;YACP,CAAC,CAAC;UACJ,CAAC,CAAC,CACDyH,KAAK,CAACtG,KAAK,IAAI;YACdD,GAAG,CAACC,KAAK,CAAC,0BAA0B,GAAGA,KAAK,CAACgC,KAAK,CAAC;YACnD5M,GAAG,CAACqJ,SAAS,CAAC,GAAG,EAAEpJ,OAAO,CAAC;YAC3BD,GAAG,CAACyJ,GAAG,CAAC,CAAC;UACX,CAAC,CAAC;QACN;MACF,CAAC,CAAC;;MAEF;MACAnL,GAAG,CAACsR,GAAG,CAAC,UAAS7P,GAAG,EAAEC,GAAG,EAAE;QACzBA,GAAG,CAACqJ,SAAS,CAAC,GAAG,CAAC;QAClBrJ,GAAG,CAACyJ,GAAG,CAAC,CAAC;MACX,CAAC,CAAC;MAEF,IAAI0H,UAAU,GAAGxU,YAAY,CAAC2B,GAAG,CAAC;MAClC,IAAI8S,oBAAoB,GAAG,EAAE;;MAE7B;MACA;MACA;MACAD,UAAU,CAAClN,UAAU,CAAC9F,oBAAoB,CAAC;;MAE3C;MACA;MACA;MACAgT,UAAU,CAAC9M,EAAE,CAAC,SAAS,EAAEjI,MAAM,CAAC4H,iCAAiC,CAAC;;MAElE;MACA;MACA;MACA;MACA;MACA;MACA;MACAmN,UAAU,CAAC9M,EAAE,CAAC,aAAa,EAAE,CAACqG,GAAG,EAAE2G,MAAM,KAAK;QAC5C;QACA,IAAIA,MAAM,CAACC,SAAS,EAAE;UACpB;QACF;QAEA,IAAI5G,GAAG,CAAC6G,OAAO,KAAK,aAAa,EAAE;UACjCF,MAAM,CAAC5H,GAAG,CAAC,kCAAkC,CAAC;QAChD,CAAC,MAAM;UACL;UACA;UACA4H,MAAM,CAACG,OAAO,CAAC9G,GAAG,CAAC;QACrB;MACF,CAAC,CAAC;MAEF,MAAM+G,cAAc,GAAG,SAAAA,CAAA,EAAW;QAChCd,qBAAqB,GAAG,IAAI;MAC9B,CAAC;MAED,IAAIe,uBAAuB,GAAG,KAAK;;MAEnC;MACAjT,MAAM,CAAC4D,MAAM,CAACjG,MAAM,EAAE;QACpBuV,eAAe,EAAEjB,qBAAqB;QACtCkB,QAAQ,EAAElB,qBAAqB;QAC/BmB,kBAAkB,EAAElC,kBAAkB;QACtCmC,WAAW,EAAEnC,kBAAkB;QAC/BwB,UAAU,EAAEA,UAAU;QACtBY,UAAU,EAAEzT,GAAG;QACf;QACA0T,qBAAqB,EAAEA,CAAA,KAAM;UAC3B,IAAI,CAAEN,uBAAuB,EAAE;YAC7BtO,MAAM,CAAC6O,MAAM,CAAC,qHAAqH,CAAC;YACpIP,uBAAuB,GAAG,IAAI;UAChC;UACAD,cAAc,CAAC,CAAC;QAClB,CAAC;QACDS,sBAAsB,EAAET,cAAc;QACtCU,WAAW,EAAE,SAAAA,CAASC,CAAC,EAAE;UACvB,IAAIhB,oBAAoB,EAAEA,oBAAoB,CAACpO,IAAI,CAACoP,CAAC,CAAC,CAAC,KAClDA,CAAC,CAAC,CAAC;QACV,CAAC;QACD;QACA;QACAC,cAAc,EAAE,SAAAA,CAASlB,UAAU,EAAEmB,aAAa,EAAE9K,EAAE,EAAE;UACtD2J,UAAU,CAACoB,MAAM,CAACD,aAAa,EAAE9K,EAAE,CAAC;QACtC;MACF,CAAC,CAAC;;MAEA;AACJ;AACA;AACA;AACA;AACA;AACA;MACE;MACA;MACA;MACAgL,OAAO,CAACC,IAAI,GAAG,MAAMC,IAAI,IAAI;QAC3B,MAAMrW,eAAe,CAACkT,mBAAmB,CAAC,CAAC;QAE3C,MAAMoD,eAAe,GAAGL,aAAa,IAAI;UACvClW,MAAM,CAACiW,cAAc,CACnB,CAAAK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEvB,UAAU,KAAIA,UAAU,EAC9BmB,aAAa,EACblP,MAAM,CAACwP,eAAe,CACpB,MAAM;YACJ,IAAI/F,OAAO,CAACkC,GAAG,CAAC8D,sBAAsB,EAAE;cACtCC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;YAC1B;YACA,MAAMC,SAAS,GAAG5B,oBAAoB;YACtCA,oBAAoB,GAAG,IAAI;YAC3B4B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAErQ,OAAO,CAACiC,QAAQ,IAAI;cAC7BA,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC;UACJ,CAAC,EACD+D,CAAC,IAAI;YACHmK,OAAO,CAAClI,KAAK,CAAC,kBAAkB,EAAEjC,CAAC,CAAC;YACpCmK,OAAO,CAAClI,KAAK,CAACjC,CAAC,IAAIA,CAAC,CAACiE,KAAK,CAAC;UAC7B,CACF,CACF,CAAC;QACH,CAAC;QAED,IAAIqG,SAAS,GAAGpG,OAAO,CAACkC,GAAG,CAACmE,IAAI,IAAI,CAAC;QACrC,IAAIC,cAAc,GAAGtG,OAAO,CAACkC,GAAG,CAACqE,gBAAgB;QAEjD,IAAID,cAAc,EAAE;UAClB,IAAIpV,OAAO,CAACsV,QAAQ,EAAE;YACpB,MAAMC,UAAU,GAAGvV,OAAO,CAACwV,MAAM,CAAC1G,OAAO,CAACkC,GAAG,CAAC3O,IAAI,IAAIrC,OAAO,CAACwV,MAAM,CAACC,EAAE;YACvEL,cAAc,IAAI,GAAG,GAAGG,UAAU,GAAG,OAAO;UAC9C;UACA;UACAzV,wBAAwB,CAACsV,cAAc,CAAC;UACxCR,eAAe,CAAC;YAAEnR,IAAI,EAAE2R;UAAe,CAAC,CAAC;UAEzC,MAAMM,qBAAqB,GAAG,CAC5B5G,OAAO,CAACkC,GAAG,CAAC2E,uBAAuB,IAAI,EAAE,EACzCC,IAAI,CAAC,CAAC;UACR,IAAIF,qBAAqB,EAAE;YACzB,IAAI,YAAY,CAACG,IAAI,CAACH,qBAAqB,CAAC,EAAE;cAC5ChX,SAAS,CAAC0W,cAAc,EAAE1H,QAAQ,CAACgI,qBAAqB,EAAE,CAAC,CAAC,CAAC;YAC/D,CAAC,MAAM;cACL,MAAM,IAAI3Q,KAAK,CAAC,2CAA2C,CAAC;YAC9D;UACF;UAEA,MAAM+Q,eAAe,GAAG,CAAChH,OAAO,CAACkC,GAAG,CAAC+E,iBAAiB,IAAI,EAAE,EAAEH,IAAI,CAAC,CAAC;UACpE,IAAIE,eAAe,EAAE;YACnB,MAAME,mBAAmB,GAAGzX,YAAY,CAACuX,eAAe,CAAC;YACzD,IAAIE,mBAAmB,KAAK,IAAI,EAAE;cAChC,MAAM,IAAIjR,KAAK,CAAC,0CAA0C,CAAC;YAC7D;YACApG,SAAS,CAACyW,cAAc,EAAEvW,QAAQ,CAAC,CAAC,CAACoX,GAAG,EAAED,mBAAmB,CAACE,GAAG,CAAC;UACpE;UAEAnW,yBAAyB,CAACqV,cAAc,CAAC;QAC3C,CAAC,MAAM;UACLF,SAAS,GAAGtH,KAAK,CAACD,MAAM,CAACuH,SAAS,CAAC,CAAC,GAAGA,SAAS,GAAGvH,MAAM,CAACuH,SAAS,CAAC;UACpE,IAAI,oBAAoB,CAACW,IAAI,CAACX,SAAS,CAAC,EAAE;YACxC;YACAN,eAAe,CAAC;cAAEnR,IAAI,EAAEyR;YAAU,CAAC,CAAC;UACtC,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;YACxC;YACAN,eAAe,CAAC;cACdpH,IAAI,EAAE0H,SAAS;cACfiB,IAAI,EAAErH,OAAO,CAACkC,GAAG,CAACoF,OAAO,IAAI;YAC/B,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAM,IAAIrR,KAAK,CAAC,wBAAwB,CAAC;UAC3C;QACF;QAEA,OAAO,QAAQ;MACjB,CAAC;IACH;IAEA,MAAMsR,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAI;QACFpW,QAAQ,CAAC,cAAc,CAAC;QACxB,OAAO,IAAI;MACb,CAAC,CAAC,OAAAqW,OAAA,EAAM;QACN,OAAO,KAAK;MACd;IACF,CAAC;IAED,MAAMC,uBAAuB,GAAIC,SAAS,IAAK;MAC7C,IAAI;QACF,MAAMC,MAAM,GAAGxW,QAAQ,iBAAA6L,MAAA,CAAiB0K,SAAS,GAAI;UAAEE,QAAQ,EAAE;QAAO,CAAC,CAAC;QAC1E,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;QACxB,MAAM,CAACpU,IAAI,GAAI6T,GAAG,CAAC,GAAGO,MAAM,CAACb,IAAI,CAAC,CAAC,CAACrT,KAAK,CAAC,GAAG,CAAC;QAC9C,IAAIF,IAAI,IAAI,IAAI,IAAI6T,GAAG,IAAI,IAAI,EAAE,OAAO,IAAI;QAC5C,OAAO;UAAE7T,IAAI;UAAE6T,GAAG,EAAEvI,MAAM,CAACuI,GAAG;QAAE,CAAC;MACnC,CAAC,CAAC,OAAOrJ,KAAK,EAAE;QACd,OAAO,IAAI;MACb;IACF,CAAC;IAED,MAAM8J,oBAAoB,GAAIH,SAAS,IAAK;MAC1C,IAAI;QACF,MAAMjO,IAAI,GAAG9J,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;QAC/C,MAAMmY,SAAS,GAAGrO,IAAI,CAACqN,IAAI,CAAC,CAAC,CAACrT,KAAK,CAAC,IAAI,CAAC,CAACsU,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7S,UAAU,IAAA6H,MAAA,CAAI0K,SAAS,MAAG,CAAC,CAAC;QACxF,IAAI,CAACI,SAAS,EAAE,OAAO,IAAI;QAC3B,MAAM,CAACvU,IAAI,GAAI6T,GAAG,CAAC,GAAGU,SAAS,CAAChB,IAAI,CAAC,CAAC,CAACrT,KAAK,CAAC,GAAG,CAAC;QACjD,IAAIF,IAAI,IAAI,IAAI,IAAI6T,GAAG,IAAI,IAAI,EAAE,OAAO,IAAI;QAC5C,OAAO;UAAE7T,IAAI;UAAE6T,GAAG,EAAEvI,MAAM,CAACuI,GAAG;QAAE,CAAC;MACnC,CAAC,CAAC,OAAOrJ,KAAK,EAAE;QACd,OAAO,IAAI;MACb;IACF,CAAC;IAEM,MAAMtO,YAAY,GAAIiY,SAAS,IAAK;MACzC,IAAIO,SAAS,GAAGJ,oBAAoB,CAACH,SAAS,CAAC;MAC/C,IAAI,CAACO,SAAS,IAAIV,iBAAiB,CAAC,CAAC,EAAE;QACrCU,SAAS,GAAGR,uBAAuB,CAACC,SAAS,CAAC;MAChD;MACA,OAAOO,SAAS;IAClB,CAAC;IAED,IAAI1M,oBAAoB,GAAG,IAAI;IAE/B/L,eAAe,CAAC+L,oBAAoB,GAAG,YAAW;MAChD,OAAOA,oBAAoB;IAC7B,CAAC;IAED/L,eAAe,CAAC0Y,uBAAuB,GAAG,gBAAetR,KAAK,EAAE;MAC9D2E,oBAAoB,GAAG3E,KAAK;MAC5B,MAAMpH,eAAe,CAACkT,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAED,IAAIpH,OAAO;IAEX9L,eAAe,CAAC2Y,0BAA0B,GAAG,kBAAwC;MAAA,IAAzBC,eAAe,GAAAjI,SAAA,CAAAvM,MAAA,QAAAuM,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MACjF7E,OAAO,GAAG8M,eAAe,GAAG,iBAAiB,GAAG,WAAW;MAC3D,MAAM5Y,eAAe,CAACkT,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAEDlT,eAAe,CAAC6Y,6BAA6B,GAAG,gBAAeC,MAAM,EAAE;MACrE/V,0BAA0B,GAAG+V,MAAM;MACnC,MAAM9Y,eAAe,CAACkT,mBAAmB,CAAC,CAAC;IAC7C,CAAC;IAEDlT,eAAe,CAAC+Y,qBAAqB,GAAG,gBAAenF,MAAM,EAAE;MAC7D,IAAIoF,IAAI,GAAG,IAAI;MACf,MAAMA,IAAI,CAACH,6BAA6B,CAAC,UAAS7V,GAAG,EAAE;QACrD,OAAO4Q,MAAM,GAAG5Q,GAAG;MACrB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA;IACA;IACA;IACA,IAAIwI,kBAAkB,GAAG,CAAC,CAAC;IAC3BxL,eAAe,CAACiZ,WAAW,GAAG,UAAS5V,QAAQ,EAAE;MAC/CmI,kBAAkB,CAAC,GAAG,GAAGpI,IAAI,CAACC,QAAQ,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ;IAC7D,CAAC;;IAED;IACArD,eAAe,CAAC0I,cAAc,GAAGA,cAAc;IAC/C1I,eAAe,CAACwL,kBAAkB,GAAGA,kBAAkB;IAEvD,MAAMmE,eAAe,CAAC,CAAC;IAACuJ,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAF,IAAA;EAAAI,KAAA;AAAA,G;;;;;;;;;;;;;;ICrhDxBzW,MAAM,CAAC7C,MAAM,CAAC;MAAC0B,wBAAwB,EAACA,CAAA,KAAIA,wBAAwB;MAACC,yBAAyB,EAACA,CAAA,KAAIA;IAAyB,CAAC,CAAC;IAAC,IAAI4X,QAAQ,EAACC,UAAU,EAACC,UAAU;IAAC5W,MAAM,CAAChD,IAAI,CAAC,IAAI,EAAC;MAAC0Z,QAAQA,CAACxZ,CAAC,EAAC;QAACwZ,QAAQ,GAACxZ,CAAC;MAAA,CAAC;MAACyZ,UAAUA,CAACzZ,CAAC,EAAC;QAACyZ,UAAU,GAACzZ,CAAC;MAAA,CAAC;MAAC0Z,UAAUA,CAAC1Z,CAAC,EAAC;QAAC0Z,UAAU,GAAC1Z,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIgC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAyB7T,MAAML,wBAAwB,GAAIgY,UAAU,IAAK;MACtD,IAAI;QACF,IAAIH,QAAQ,CAACG,UAAU,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;UACnC;UACA;UACAH,UAAU,CAACE,UAAU,CAAC;QACxB,CAAC,MAAM;UACL,MAAM,IAAI/S,KAAK,CACb,mCAAA+G,MAAA,CAAkCgM,UAAU,yBAC5C,8DAA8D,GAC9D,2BACF,CAAC;QACH;MACF,CAAC,CAAC,OAAOjL,KAAK,EAAE;QACd;QACA;QACA;QACA,IAAIA,KAAK,CAAC0C,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAM1C,KAAK;QACb;MACF;IACF,CAAC;IAKM,MAAM9M,yBAAyB,GACpC,SAAAA,CAAC+X,UAAU,EAA6B;MAAA,IAA3BE,YAAY,GAAA/I,SAAA,CAAAvM,MAAA,QAAAuM,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGH,OAAO;MACjC,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAClK,OAAO,CAACqT,MAAM,IAAI;QACxDD,YAAY,CAAC1R,EAAE,CAAC2R,MAAM,EAAE5S,MAAM,CAACwP,eAAe,CAAC,MAAM;UACnD,IAAIgD,UAAU,CAACC,UAAU,CAAC,EAAE;YAC1BF,UAAU,CAACE,UAAU,CAAC;UACxB;QACF,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ,CAAC;IAACN,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAF,IAAA;EAAAI,KAAA;AAAA,G", "file": "/packages/webapp.js", "sourcesContent": ["import assert from 'assert';\nimport { readFileSync, chmodSync, chownSync } from 'fs';\nimport { createServer } from 'http';\nimport { userInfo } from 'os';\nimport { join as pathJoin, dirname as pathDirname } from 'path';\nimport { parse as parseUrl } from 'url';\nimport { createHash } from 'crypto';\nimport express from 'express';\nimport compress from 'compression';\nimport cookieParser from 'cookie-parser';\nimport qs from 'qs';\nimport parseRequest from 'parseurl';\nimport { lookup as lookupUserAgent } from 'useragent-ng';\nimport { isModern } from 'meteor/modern-browsers';\nimport send from 'send';\nimport {\n  removeExistingSocketFile,\n  registerSocketFileCleanup,\n} from './socket_file.js';\nimport cluster from 'cluster';\nimport { execSync } from 'child_process';\n\nvar SHORT_SOCKET_TIMEOUT = 5 * 1000;\nvar LONG_SOCKET_TIMEOUT = 120 * 1000;\n\nconst createExpressApp = () => {\n  const app = express();\n  // Security and performace headers\n  // these headers come from these docs: https://expressjs.com/en/api.html#app.settings.table\n  app.set('x-powered-by', false);\n  app.set('etag', false);\n  app.set('query parser', qs.parse);\n  return app;\n}\nexport const WebApp = {};\nexport const WebAppInternals = {};\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\n\nWebAppInternals.NpmModules = {\n  express : {\n    version: Npm.require('express/package.json').version,\n    module: express,\n  }\n};\n\n// More of a convenience for the end user\nWebApp.express = express;\n\n// Though we might prefer to use web.browser (modern) as the default\n// architecture, safety requires a more compatible defaultArch.\nWebApp.defaultArch = 'web.browser.legacy';\n\n// XXX maps archs to manifests\nWebApp.clientPrograms = {};\n\n// XXX maps archs to program path on filesystem\nvar archPath = {};\n\nvar bundledJsCssUrlRewriteHook = function(url) {\n  var bundledPrefix = __meteor_runtime_config__.ROOT_URL_PATH_PREFIX || '';\n  return bundledPrefix + url;\n};\n\nvar sha1 = function(contents) {\n  var hash = createHash('sha1');\n  hash.update(contents);\n  return hash.digest('hex');\n};\n\nfunction shouldCompress(req, res) {\n  if (req.headers['x-no-compression']) {\n    // don't compress responses with this request header\n    return false;\n  }\n\n  // fallback to standard filter function\n  return compress.filter(req, res);\n}\n\n// #BrowserIdentification\n//\n// We have multiple places that want to identify the browser: the\n// unsupported browser page, the appcache package, and, eventually\n// delivering browser polyfills only as needed.\n//\n// To avoid detecting the browser in multiple places ad-hoc, we create a\n// Meteor \"browser\" object. It uses but does not expose the npm\n// useragent module (we could choose a different mechanism to identify\n// the browser in the future if we wanted to).  The browser object\n// contains\n//\n// * `name`: the name of the browser in camel case\n// * `major`, `minor`, `patch`: integers describing the browser version\n//\n// Also here is an early version of a Meteor `request` object, intended\n// to be a high-level description of the request without exposing\n// details of Express's low-level `req`.  Currently it contains:\n//\n// * `browser`: browser identification object described above\n// * `url`: parsed url, including parsed query params\n//\n// As a temporary hack there is a `categorizeRequest` function on WebApp which\n// converts a Express `req` to a Meteor `request`. This can go away once smart\n// packages such as appcache are being passed a `request` object directly when\n// they serve content.\n//\n// This allows `request` to be used uniformly: it is passed to the html\n// attributes hook, and the appcache package can use it when deciding\n// whether to generate a 404 for the manifest.\n//\n// Real routing / server side rendering will probably refactor this\n// heavily.\n\n// e.g. \"Mobile Safari\" => \"mobileSafari\"\nvar camelCase = function(name) {\n  var parts = name.split(' ');\n  parts[0] = parts[0].toLowerCase();\n  for (var i = 1; i < parts.length; ++i) {\n    parts[i] = parts[i].charAt(0).toUpperCase() + parts[i].substring(1);\n  }\n  return parts.join('');\n};\n\nvar identifyBrowser = function(userAgentString) {\n  if (!userAgentString) {\n    return {\n      name: 'unknown',\n      major: 0,\n      minor: 0,\n      patch: 0\n    };\n  }\n  var userAgent = lookupUserAgent(userAgentString.substring(0, 150));\n  return {\n    name: camelCase(userAgent.family),\n    major: +userAgent.major,\n    minor: +userAgent.minor,\n    patch: +userAgent.patch,\n  };\n};\n\n// XXX Refactor as part of implementing real routing.\nWebAppInternals.identifyBrowser = identifyBrowser;\n\nWebApp.categorizeRequest = function(req) {\n  if (req.browser && req.arch && typeof req.modern === 'boolean') {\n    // Already categorized.\n    return req;\n  }\n\n  const browser = identifyBrowser(req.headers['user-agent']);\n  const modern = isModern(browser);\n  const path =\n    typeof req.pathname === 'string'\n      ? req.pathname\n      : parseRequest(req).pathname;\n\n  const categorized = {\n    browser,\n    modern,\n    path,\n    arch: WebApp.defaultArch,\n    url: parseUrl(req.url, true),\n    dynamicHead: req.dynamicHead,\n    dynamicBody: req.dynamicBody,\n    headers: req.headers,\n    cookies: req.cookies,\n  };\n\n  const pathParts = path.split('/');\n  const archKey = pathParts[1];\n\n  if (archKey.startsWith('__')) {\n    const archCleaned = 'web.' + archKey.slice(2);\n    if (hasOwn.call(WebApp.clientPrograms, archCleaned)) {\n      pathParts.splice(1, 1); // Remove the archKey part.\n      return Object.assign(categorized, {\n        arch: archCleaned,\n        path: pathParts.join('/'),\n      });\n    }\n  }\n\n  // TODO Perhaps one day we could infer Cordova clients here, so that we\n  // wouldn't have to use prefixed \"/__cordova/...\" URLs.\n  const preferredArchOrder = isModern(browser)\n    ? ['web.browser', 'web.browser.legacy']\n    : ['web.browser.legacy', 'web.browser'];\n\n  for (const arch of preferredArchOrder) {\n    // If our preferred arch is not available, it's better to use another\n    // client arch that is available than to guarantee the site won't work\n    // by returning an unknown arch. For example, if web.browser.legacy is\n    // excluded using the --exclude-archs command-line option, legacy\n    // clients are better off receiving web.browser (which might actually\n    // work) than receiving an HTTP 404 response. If none of the archs in\n    // preferredArchOrder are defined, only then should we send a 404.\n    if (hasOwn.call(WebApp.clientPrograms, arch)) {\n      return Object.assign(categorized, { arch });\n    }\n  }\n\n  return categorized;\n};\n\n// HTML attribute hooks: functions to be called to determine any attributes to\n// be added to the '<html>' tag. Each function is passed a 'request' object (see\n// #BrowserIdentification) and should return null or object.\nvar htmlAttributeHooks = [];\nvar getHtmlAttributes = function(request) {\n  var combinedAttributes = {};\n  (htmlAttributeHooks || []).forEach(function(hook) {\n    var attributes = hook(request);\n    if (attributes === null) return;\n    if (typeof attributes !== 'object')\n      throw Error('HTML attribute hook must return null or object');\n    Object.assign(combinedAttributes, attributes);\n  });\n  return combinedAttributes;\n};\nWebApp.addHtmlAttributeHook = function(hook) {\n  htmlAttributeHooks.push(hook);\n};\n\n// Serve app HTML for this URL?\nvar appUrl = function(url) {\n  if (url === '/favicon.ico' || url === '/robots.txt') return false;\n\n  // NOTE: app.manifest is not a web standard like favicon.ico and\n  // robots.txt. It is a file name we have chosen to use for HTML5\n  // appcache URLs. It is included here to prevent using an appcache\n  // then removing it from poisoning an app permanently. Eventually,\n  // once we have server side routing, this won't be needed as\n  // unknown URLs with return a 404 automatically.\n  if (url === '/app.manifest') return false;\n\n  // Avoid serving app HTML for declared routes such as /sockjs/.\n  if (RoutePolicy.classify(url)) return false;\n\n  // we currently return app HTML on all URLs by default\n  return true;\n};\n\n// We need to calculate the client hash after all packages have loaded\n// to give them a chance to populate __meteor_runtime_config__.\n//\n// Calculating the hash during startup means that packages can only\n// populate __meteor_runtime_config__ during load, not during startup.\n//\n// Calculating instead it at the beginning of main after all startup\n// hooks had run would allow packages to also populate\n// __meteor_runtime_config__ during startup, but that's too late for\n// autoupdate because it needs to have the client hash at startup to\n// insert the auto update version itself into\n// __meteor_runtime_config__ to get it to the client.\n//\n// An alternative would be to give autoupdate a \"post-start,\n// pre-listen\" hook to allow it to insert the auto update version at\n// the right moment.\n\nMeteor.startup(function() {\n  function getter(key) {\n    return function(arch) {\n      arch = arch || WebApp.defaultArch;\n      const program = WebApp.clientPrograms[arch];\n      const value = program && program[key];\n      // If this is the first time we have calculated this hash,\n      // program[key] will be a thunk (lazy function with no parameters)\n      // that we should call to do the actual computation.\n      return typeof value === 'function' ? (program[key] = value()) : value;\n    };\n  }\n\n  WebApp.calculateClientHash = WebApp.clientHash = getter('version');\n  WebApp.calculateClientHashRefreshable = getter('versionRefreshable');\n  WebApp.calculateClientHashNonRefreshable = getter('versionNonRefreshable');\n  WebApp.calculateClientHashReplaceable = getter('versionReplaceable');\n  WebApp.getRefreshableAssets = getter('refreshableAssets');\n});\n\n// When we have a request pending, we want the socket timeout to be long, to\n// give ourselves a while to serve it, and to allow sockjs long polls to\n// complete.  On the other hand, we want to close idle sockets relatively\n// quickly, so that we can shut down relatively promptly but cleanly, without\n// cutting off anyone's response.\nWebApp._timeoutAdjustmentRequestCallback = function(req, res) {\n  // this is really just req.socket.setTimeout(LONG_SOCKET_TIMEOUT);\n  req.setTimeout(LONG_SOCKET_TIMEOUT);\n  // Insert our new finish listener to run BEFORE the existing one which removes\n  // the response from the socket.\n  var finishListeners = res.listeners('finish');\n  // XXX Apparently in Node 0.12 this event was called 'prefinish'.\n  // https://github.com/joyent/node/commit/7c9b6070\n  // But it has switched back to 'finish' in Node v4:\n  // https://github.com/nodejs/node/pull/1411\n  res.removeAllListeners('finish');\n  res.on('finish', function() {\n    res.setTimeout(SHORT_SOCKET_TIMEOUT);\n  });\n  Object.values(finishListeners).forEach(function(l) {\n    res.on('finish', l);\n  });\n};\n\n// Will be updated by main before we listen.\n// Map from client arch to boilerplate object.\n// Boilerplate object has:\n//   - func: XXX\n//   - baseData: XXX\nvar boilerplateByArch = {};\n\n// Register a callback function that can selectively modify boilerplate\n// data given arguments (request, data, arch). The key should be a unique\n// identifier, to prevent accumulating duplicate callbacks from the same\n// call site over time. Callbacks will be called in the order they were\n// registered. A callback should return false if it did not make any\n// changes affecting the boilerplate. Passing null deletes the callback.\n// Any previous callback registered for this key will be returned.\nconst boilerplateDataCallbacks = Object.create(null);\nWebAppInternals.registerBoilerplateDataCallback = function(key, callback) {\n  const previousCallback = boilerplateDataCallbacks[key];\n\n  if (typeof callback === 'function') {\n    boilerplateDataCallbacks[key] = callback;\n  } else {\n    assert.strictEqual(callback, null);\n    delete boilerplateDataCallbacks[key];\n  }\n\n  // Return the previous callback in case the new callback needs to call\n  // it; for example, when the new callback is a wrapper for the old.\n  return previousCallback || null;\n};\n\n// Given a request (as returned from `categorizeRequest`), return the\n// boilerplate HTML to serve for that request.\n//\n// If a previous Express middleware has rendered content for the head or body,\n// returns the boilerplate with that content patched in otherwise\n// memoizes on HTML attributes (used by, eg, appcache) and whether inline\n// scripts are currently allowed.\n// XXX so far this function is always called with arch === 'web.browser'\nfunction getBoilerplate(request, arch) {\n  return getBoilerplateAsync(request, arch);\n}\n\n/**\n * @summary Takes a runtime configuration object and\n * returns an encoded runtime string.\n * @locus Server\n * @param {Object} rtimeConfig\n * @returns {String}\n */\nWebApp.encodeRuntimeConfig = function(rtimeConfig) {\n  return JSON.stringify(encodeURIComponent(JSON.stringify(rtimeConfig)));\n};\n\n/**\n * @summary Takes an encoded runtime string and returns\n * a runtime configuration object.\n * @locus Server\n * @param {String} rtimeConfigString\n * @returns {Object}\n */\nWebApp.decodeRuntimeConfig = function(rtimeConfigStr) {\n  return JSON.parse(decodeURIComponent(JSON.parse(rtimeConfigStr)));\n};\n\nconst runtimeConfig = {\n  // hooks will contain the callback functions\n  // set by the caller to addRuntimeConfigHook\n  hooks: new Hook(),\n  // updateHooks will contain the callback functions\n  // set by the caller to addUpdatedNotifyHook\n  updateHooks: new Hook(),\n  // isUpdatedByArch is an object containing fields for each arch\n  // that this server supports.\n  // - Each field will be true when the server updates the runtimeConfig for that arch.\n  // - When the hook callback is called the update field in the callback object will be\n  // set to isUpdatedByArch[arch].\n  // = isUpdatedyByArch[arch] is reset to false after the callback.\n  // This enables the caller to cache data efficiently so they do not need to\n  // decode & update data on every callback when the runtimeConfig is not changing.\n  isUpdatedByArch: {},\n};\n\n/**\n * @name addRuntimeConfigHookCallback(options)\n * @locus Server\n * @isprototype true\n * @summary Callback for `addRuntimeConfigHook`.\n *\n * If the handler returns a _falsy_ value the hook will not\n * modify the runtime configuration.\n *\n * If the handler returns a _String_ the hook will substitute\n * the string for the encoded configuration string.\n *\n * **Warning:** the hook does not check the return value at all it is\n * the responsibility of the caller to get the formatting correct using\n * the helper functions.\n *\n * `addRuntimeConfigHookCallback` takes only one `Object` argument\n * with the following fields:\n * @param {Object} options\n * @param {String} options.arch The architecture of the client\n * requesting a new runtime configuration. This can be one of\n * `web.browser`, `web.browser.legacy` or `web.cordova`.\n * @param {Object} options.request\n * A NodeJs [IncomingMessage](https://nodejs.org/api/http.html#http_class_http_incomingmessage)\n * https://nodejs.org/api/http.html#http_class_http_incomingmessage\n * `Object` that can be used to get information about the incoming request.\n * @param {String} options.encodedCurrentConfig The current configuration object\n * encoded as a string for inclusion in the root html.\n * @param {Boolean} options.updated `true` if the config for this architecture\n * has been updated since last called, otherwise `false`. This flag can be used\n * to cache the decoding/encoding for each architecture.\n */\n\n/**\n * @summary Hook that calls back when the meteor runtime configuration,\n * `__meteor_runtime_config__` is being sent to any client.\n *\n * **returns**: <small>_Object_</small> `{ stop: function, callback: function }`\n * - `stop` <small>_Function_</small> Call `stop()` to stop getting callbacks.\n * - `callback` <small>_Function_</small> The passed in `callback`.\n * @locus Server\n * @param {addRuntimeConfigHookCallback} callback\n * See `addRuntimeConfigHookCallback` description.\n * @returns {Object} {{ stop: function, callback: function }}\n * Call the returned `stop()` to stop getting callbacks.\n * The passed in `callback` is returned also.\n */\nWebApp.addRuntimeConfigHook = function(callback) {\n  return runtimeConfig.hooks.register(callback);\n};\n\nasync function getBoilerplateAsync(request, arch) {\n  let boilerplate = boilerplateByArch[arch];\n  await runtimeConfig.hooks.forEachAsync(async hook => {\n    const meteorRuntimeConfig = await hook({\n      arch,\n      request,\n      encodedCurrentConfig: boilerplate.baseData.meteorRuntimeConfig,\n      updated: runtimeConfig.isUpdatedByArch[arch],\n    });\n    if (!meteorRuntimeConfig) return true;\n    boilerplate.baseData = Object.assign({}, boilerplate.baseData, {\n      meteorRuntimeConfig,\n    });\n    return true;\n  });\n  runtimeConfig.isUpdatedByArch[arch] = false;\n  const { dynamicHead, dynamicBody } = request;\n  const data = Object.assign(\n    {},\n    boilerplate.baseData,\n    {\n      htmlAttributes: getHtmlAttributes(request),\n    },\n    { dynamicHead, dynamicBody }\n  );\n\n  let madeChanges = false;\n  let promise = Promise.resolve();\n\n  Object.keys(boilerplateDataCallbacks).forEach(key => {\n    promise = promise\n      .then(() => {\n        const callback = boilerplateDataCallbacks[key];\n        return callback(request, data, arch);\n      })\n      .then(result => {\n        // Callbacks should return false if they did not make any changes.\n        if (result !== false) {\n          madeChanges = true;\n        }\n      });\n  });\n\n  return promise.then(() => ({\n    stream: boilerplate.toHTMLStream(data),\n    statusCode: data.statusCode,\n    headers: data.headers,\n  }));\n}\n\n/**\n * @name addUpdatedNotifyHookCallback(options)\n * @summary callback handler for `addupdatedNotifyHook`\n * @isprototype true\n * @locus Server\n * @param {Object} options\n * @param {String} options.arch The architecture that is being updated.\n * This can be one of `web.browser`, `web.browser.legacy` or `web.cordova`.\n * @param {Object} options.manifest The new updated manifest object for\n * this `arch`.\n * @param {Object} options.runtimeConfig The new updated configuration\n * object for this `arch`.\n */\n\n/**\n * @summary Hook that runs when the meteor runtime configuration\n * is updated.  Typically the configuration only changes during development mode.\n * @locus Server\n * @param {addUpdatedNotifyHookCallback} handler\n * The `handler` is called on every change to an `arch` runtime configuration.\n * See `addUpdatedNotifyHookCallback`.\n * @returns {Object} {{ stop: function, callback: function }}\n */\nWebApp.addUpdatedNotifyHook = function(handler) {\n  return runtimeConfig.updateHooks.register(handler);\n};\n\nWebAppInternals.generateBoilerplateInstance = function(\n  arch,\n  manifest,\n  additionalOptions\n) {\n  additionalOptions = additionalOptions || {};\n\n  runtimeConfig.isUpdatedByArch[arch] = true;\n  const rtimeConfig = {\n    ...__meteor_runtime_config__,\n    ...(additionalOptions.runtimeConfigOverrides || {}),\n  };\n  runtimeConfig.updateHooks.forEach(cb => {\n    cb({ arch, manifest, runtimeConfig: rtimeConfig });\n    return true;\n  });\n\n  const meteorRuntimeConfig = JSON.stringify(\n    encodeURIComponent(JSON.stringify(rtimeConfig))\n  );\n\n  return new Boilerplate(\n    arch,\n    manifest,\n    Object.assign(\n      {\n        pathMapper(itemPath) {\n          return pathJoin(archPath[arch], itemPath);\n        },\n        baseDataExtension: {\n          additionalStaticJs: (Object.entries(additionalStaticJs) || []).map(function(\n            [pathname, contents]\n          ) {\n            return {\n              pathname: pathname,\n              contents: contents,\n            };\n          }),\n          // Convert to a JSON string, then get rid of most weird characters, then\n          // wrap in double quotes. (The outermost JSON.stringify really ought to\n          // just be \"wrap in double quotes\" but we use it to be safe.) This might\n          // end up inside a <script> tag so we need to be careful to not include\n          // \"</script>\", but normal {{spacebars}} escaping escapes too much! See\n          // https://github.com/meteor/meteor/issues/3730\n          meteorRuntimeConfig,\n          meteorRuntimeHash: sha1(meteorRuntimeConfig),\n          rootUrlPathPrefix:\n            __meteor_runtime_config__.ROOT_URL_PATH_PREFIX || '',\n          bundledJsCssUrlRewriteHook: bundledJsCssUrlRewriteHook,\n          sriMode: sriMode,\n          inlineScriptsAllowed: WebAppInternals.inlineScriptsAllowed(),\n          inline: additionalOptions.inline,\n        },\n      },\n      additionalOptions\n    )\n  );\n};\n\n// A mapping from url path to architecture (e.g. \"web.browser\") to static\n// file information with the following fields:\n// - type: the type of file to be served\n// - cacheable: optionally, whether the file should be cached or not\n// - sourceMapUrl: optionally, the url of the source map\n//\n// Info also contains one of the following:\n// - content: the stringified content that should be served at this path\n// - absolutePath: the absolute path on disk to the file\n\n// Serve static files from the manifest or added with\n// `addStaticJs`. Exported for tests.\nWebAppInternals.staticFilesMiddleware = async function(\n  staticFilesByArch,\n  req,\n  res,\n  next\n) {\n  var pathname = parseRequest(req).pathname;\n  try {\n    pathname = decodeURIComponent(pathname);\n  } catch (e) {\n    next();\n    return;\n  }\n\n  var serveStaticJs = function(s) {\n    if (\n      req.method === 'GET' ||\n      req.method === 'HEAD' ||\n      Meteor.settings.packages?.webapp?.alwaysReturnContent\n    ) {\n      res.writeHead(200, {\n        'Content-type': 'application/javascript; charset=UTF-8',\n        'Content-Length': Buffer.byteLength(s),\n      });\n      res.write(s);\n      res.end();\n    } else {\n      const status = req.method === 'OPTIONS' ? 200 : 405;\n      res.writeHead(status, {\n        Allow: 'OPTIONS, GET, HEAD',\n        'Content-Length': '0',\n      });\n      res.end();\n    }\n  };\n\n  if (\n    pathname in additionalStaticJs &&\n    !WebAppInternals.inlineScriptsAllowed()\n  ) {\n    serveStaticJs(additionalStaticJs[pathname]);\n    return;\n  }\n\n  const { arch, path } = WebApp.categorizeRequest(req);\n\n  if (!hasOwn.call(WebApp.clientPrograms, arch)) {\n    // We could come here in case we run with some architectures excluded\n    next();\n    return;\n  }\n\n  // If pauseClient(arch) has been called, program.paused will be a\n  // Promise that will be resolved when the program is unpaused.\n  const program = WebApp.clientPrograms[arch];\n  await program.paused;\n\n  if (\n    path === '/meteor_runtime_config.js' &&\n    !WebAppInternals.inlineScriptsAllowed()\n  ) {\n    serveStaticJs(\n      `__meteor_runtime_config__ = ${program.meteorRuntimeConfig};`\n    );\n    return;\n  }\n\n  const info = getStaticFileInfo(staticFilesByArch, pathname, path, arch);\n  if (!info) {\n    next();\n    return;\n  }\n  // \"send\" will handle HEAD & GET requests\n  if (\n    req.method !== 'HEAD' &&\n    req.method !== 'GET' &&\n    !Meteor.settings.packages?.webapp?.alwaysReturnContent\n  ) {\n    const status = req.method === 'OPTIONS' ? 200 : 405;\n    res.writeHead(status, {\n      Allow: 'OPTIONS, GET, HEAD',\n      'Content-Length': '0',\n    });\n    res.end();\n    return;\n  }\n\n  // We don't need to call pause because, unlike 'static', once we call into\n  // 'send' and yield to the event loop, we never call another handler with\n  // 'next'.\n\n  // Cacheable files are files that should never change. Typically\n  // named by their hash (eg meteor bundled js and css files).\n  // We cache them ~forever (1yr).\n  const maxAge = info.cacheable ? 1000 * 60 * 60 * 24 * 365 : 0;\n\n  if (info.cacheable) {\n    // Since we use req.headers[\"user-agent\"] to determine whether the\n    // client should receive modern or legacy resources, tell the client\n    // to invalidate cached resources when/if its user agent string\n    // changes in the future.\n    res.setHeader('Vary', 'User-Agent');\n  }\n\n  // Set the X-SourceMap header, which current Chrome, FireFox, and Safari\n  // understand.  (The SourceMap header is slightly more spec-correct but FF\n  // doesn't understand it.)\n  //\n  // You may also need to enable source maps in Chrome: open dev tools, click\n  // the gear in the bottom right corner, and select \"enable source maps\".\n  if (info.sourceMapUrl) {\n    res.setHeader(\n      'X-SourceMap',\n      __meteor_runtime_config__.ROOT_URL_PATH_PREFIX + info.sourceMapUrl\n    );\n  }\n\n  if (info.type === 'js' || info.type === 'dynamic js') {\n    res.setHeader('Content-Type', 'application/javascript; charset=UTF-8');\n  } else if (info.type === 'css') {\n    res.setHeader('Content-Type', 'text/css; charset=UTF-8');\n  } else if (info.type === 'json') {\n    res.setHeader('Content-Type', 'application/json; charset=UTF-8');\n  }\n\n  if (info.hash) {\n    res.setHeader('ETag', '\"' + info.hash + '\"');\n  }\n\n  if (info.content) {\n    res.setHeader('Content-Length', Buffer.byteLength(info.content));\n    res.write(info.content);\n    res.end();\n  } else {\n    send(req, info.absolutePath, {\n      maxage: maxAge,\n      dotfiles: 'allow', // if we specified a dotfile in the manifest, serve it\n      lastModified: false, // don't set last-modified based on the file date\n    })\n      .on('error', function(err) {\n        Log.error('Error serving static file ' + err);\n        res.writeHead(500);\n        res.end();\n      })\n      .on('directory', function() {\n        Log.error('Unexpected directory ' + info.absolutePath);\n        res.writeHead(500);\n        res.end();\n      })\n      .pipe(res);\n  }\n};\n\nfunction getStaticFileInfo(staticFilesByArch, originalPath, path, arch) {\n  if (!hasOwn.call(WebApp.clientPrograms, arch)) {\n    return null;\n  }\n\n  // Get a list of all available static file architectures, with arch\n  // first in the list if it exists.\n  const staticArchList = Object.keys(staticFilesByArch);\n  const archIndex = staticArchList.indexOf(arch);\n  if (archIndex > 0) {\n    staticArchList.unshift(staticArchList.splice(archIndex, 1)[0]);\n  }\n\n  let info = null;\n\n  staticArchList.some(arch => {\n    const staticFiles = staticFilesByArch[arch];\n\n    function finalize(path) {\n      info = staticFiles[path];\n      // Sometimes we register a lazy function instead of actual data in\n      // the staticFiles manifest.\n      if (typeof info === 'function') {\n        info = staticFiles[path] = info();\n      }\n      return info;\n    }\n\n    // If staticFiles contains originalPath with the arch inferred above,\n    // use that information.\n    if (hasOwn.call(staticFiles, originalPath)) {\n      return finalize(originalPath);\n    }\n\n    // If categorizeRequest returned an alternate path, try that instead.\n    if (path !== originalPath && hasOwn.call(staticFiles, path)) {\n      return finalize(path);\n    }\n  });\n\n  return info;\n}\n\n// Parse the passed in port value. Return the port as-is if it's a String\n// (e.g. a Windows Server style named pipe), otherwise return the port as an\n// integer.\n//\n// DEPRECATED: Direct use of this function is not recommended; it is no\n// longer used internally, and will be removed in a future release.\nWebAppInternals.parsePort = port => {\n  let parsedPort = parseInt(port);\n  if (Number.isNaN(parsedPort)) {\n    parsedPort = port;\n  }\n  return parsedPort;\n};\n\nimport { onMessage } from 'meteor/inter-process-messaging';\n\nonMessage('webapp-pause-client', async ({ arch }) => {\n  await WebAppInternals.pauseClient(arch);\n});\n\nonMessage('webapp-reload-client', async ({ arch }) => {\n  await WebAppInternals.generateClientProgram(arch);\n});\n\nasync function runWebAppServer() {\n  var shuttingDown = false;\n  var syncQueue = new Meteor._AsynchronousQueue();\n\n  var getItemPathname = function(itemUrl) {\n    return decodeURIComponent(parseUrl(itemUrl).pathname);\n  };\n\n  WebAppInternals.reloadClientPrograms = async function() {\n    await syncQueue.runTask(function() {\n      const staticFilesByArch = Object.create(null);\n\n      const { configJson } = __meteor_bootstrap__;\n      const clientArchs =\n        configJson.clientArchs || Object.keys(configJson.clientPaths);\n\n      try {\n        clientArchs.forEach(arch => {\n          generateClientProgram(arch, staticFilesByArch);\n        });\n        WebAppInternals.staticFilesByArch = staticFilesByArch;\n      } catch (e) {\n        Log.error('Error reloading the client program: ' + e.stack);\n        process.exit(1);\n      }\n    });\n  };\n\n  // Pause any incoming requests and make them wait for the program to be\n  // unpaused the next time generateClientProgram(arch) is called.\n  WebAppInternals.pauseClient = async function(arch) {\n    await syncQueue.runTask(() => {\n      const program = WebApp.clientPrograms[arch];\n      const { unpause } = program;\n      program.paused = new Promise(resolve => {\n        if (typeof unpause === 'function') {\n          // If there happens to be an existing program.unpause function,\n          // compose it with the resolve function.\n          program.unpause = function() {\n            unpause();\n            resolve();\n          };\n        } else {\n          program.unpause = resolve;\n        }\n      });\n    });\n  };\n\n  WebAppInternals.generateClientProgram = async function(arch) {\n    await syncQueue.runTask(() => generateClientProgram(arch));\n  };\n\n  function generateClientProgram(\n    arch,\n    staticFilesByArch = WebAppInternals.staticFilesByArch\n  ) {\n    const clientDir = pathJoin(\n      pathDirname(__meteor_bootstrap__.serverDir),\n      arch\n    );\n\n    // read the control for the client we'll be serving up\n    const programJsonPath = pathJoin(clientDir, 'program.json');\n\n    let programJson;\n    try {\n      programJson = JSON.parse(readFileSync(programJsonPath));\n    } catch (e) {\n      if (e.code === 'ENOENT') return;\n      throw e;\n    }\n\n    if (programJson.format !== 'web-program-pre1') {\n      throw new Error(\n        'Unsupported format for client assets: ' +\n          JSON.stringify(programJson.format)\n      );\n    }\n\n    if (!programJsonPath || !clientDir || !programJson) {\n      throw new Error('Client config file not parsed.');\n    }\n\n    archPath[arch] = clientDir;\n    const staticFiles = (staticFilesByArch[arch] = Object.create(null));\n\n    const { manifest } = programJson;\n    manifest.forEach(item => {\n      if (item.url && item.where === 'client') {\n        staticFiles[getItemPathname(item.url)] = {\n          absolutePath: pathJoin(clientDir, item.path),\n          cacheable: item.cacheable,\n          hash: item.hash,\n          // Link from source to its map\n          sourceMapUrl: item.sourceMapUrl,\n          type: item.type,\n        };\n\n        if (item.sourceMap) {\n          // Serve the source map too, under the specified URL. We assume\n          // all source maps are cacheable.\n          staticFiles[getItemPathname(item.sourceMapUrl)] = {\n            absolutePath: pathJoin(clientDir, item.sourceMap),\n            cacheable: true,\n          };\n        }\n      }\n    });\n\n    const { PUBLIC_SETTINGS } = __meteor_runtime_config__;\n    const configOverrides = {\n      PUBLIC_SETTINGS,\n    };\n\n    const oldProgram = WebApp.clientPrograms[arch];\n    const newProgram = (WebApp.clientPrograms[arch] = {\n      format: 'web-program-pre1',\n      manifest: manifest,\n      // Use arrow functions so that these versions can be lazily\n      // calculated later, and so that they will not be included in the\n      // staticFiles[manifestUrl].content string below.\n      //\n      // Note: these version calculations must be kept in agreement with\n      // CordovaBuilder#appendVersion in tools/cordova/builder.js, or hot\n      // code push will reload Cordova apps unnecessarily.\n      version: () =>\n        WebAppHashing.calculateClientHash(manifest, null, configOverrides),\n      versionRefreshable: () =>\n        WebAppHashing.calculateClientHash(\n          manifest,\n          type => type === 'css',\n          configOverrides\n        ),\n      versionNonRefreshable: () =>\n        WebAppHashing.calculateClientHash(\n          manifest,\n          (type, replaceable) => type !== 'css' && !replaceable,\n          configOverrides\n        ),\n      versionReplaceable: () =>\n        WebAppHashing.calculateClientHash(\n          manifest,\n          (_type, replaceable) => replaceable,\n          configOverrides\n        ),\n      cordovaCompatibilityVersions: programJson.cordovaCompatibilityVersions,\n      PUBLIC_SETTINGS,\n      hmrVersion: programJson.hmrVersion,\n    });\n\n    // Expose program details as a string reachable via the following URL.\n    const manifestUrlPrefix = '/__' + arch.replace(/^web\\./, '');\n    const manifestUrl = manifestUrlPrefix + getItemPathname('/manifest.json');\n\n    staticFiles[manifestUrl] = () => {\n      if (Package.autoupdate) {\n        const {\n          AUTOUPDATE_VERSION = Package.autoupdate.Autoupdate.autoupdateVersion,\n        } = process.env;\n\n        if (AUTOUPDATE_VERSION) {\n          newProgram.version = AUTOUPDATE_VERSION;\n        }\n      }\n\n      if (typeof newProgram.version === 'function') {\n        newProgram.version = newProgram.version();\n      }\n\n      return {\n        content: JSON.stringify(newProgram),\n        cacheable: false,\n        hash: newProgram.version,\n        type: 'json',\n      };\n    };\n\n    generateBoilerplateForArch(arch);\n\n    // If there are any requests waiting on oldProgram.paused, let them\n    // continue now (using the new program).\n    if (oldProgram && oldProgram.paused) {\n      oldProgram.unpause();\n    }\n  }\n\n  const defaultOptionsForArch = {\n    'web.cordova': {\n      runtimeConfigOverrides: {\n        // XXX We use absoluteUrl() here so that we serve https://\n        // URLs to cordova clients if force-ssl is in use. If we were\n        // to use __meteor_runtime_config__.ROOT_URL instead of\n        // absoluteUrl(), then Cordova clients would immediately get a\n        // HCP setting their DDP_DEFAULT_CONNECTION_URL to\n        // http://example.meteor.com. This breaks the app, because\n        // force-ssl doesn't serve CORS headers on 302\n        // redirects. (Plus it's undesirable to have clients\n        // connecting to http://example.meteor.com when force-ssl is\n        // in use.)\n        DDP_DEFAULT_CONNECTION_URL:\n          process.env.MOBILE_DDP_URL || Meteor.absoluteUrl(),\n        ROOT_URL: process.env.MOBILE_ROOT_URL || Meteor.absoluteUrl(),\n      },\n    },\n\n    'web.browser': {\n      runtimeConfigOverrides: {\n        isModern: true,\n      },\n    },\n\n    'web.browser.legacy': {\n      runtimeConfigOverrides: {\n        isModern: false,\n      },\n    },\n  };\n\n  WebAppInternals.generateBoilerplate = async function() {\n    // This boilerplate will be served to the mobile devices when used with\n    // Meteor/Cordova for the Hot-Code Push and since the file will be served by\n    // the device's server, it is important to set the DDP url to the actual\n    // Meteor server accepting DDP connections and not the device's file server.\n    await syncQueue.runTask(function() {\n      Object.keys(WebApp.clientPrograms).forEach(generateBoilerplateForArch);\n    });\n  };\n\n  function generateBoilerplateForArch(arch) {\n    const program = WebApp.clientPrograms[arch];\n    const additionalOptions = defaultOptionsForArch[arch] || {};\n    const { baseData } = (boilerplateByArch[\n      arch\n    ] = WebAppInternals.generateBoilerplateInstance(\n      arch,\n      program.manifest,\n      additionalOptions\n    ));\n    // We need the runtime config with overrides for meteor_runtime_config.js:\n    program.meteorRuntimeConfig = JSON.stringify({\n      ...__meteor_runtime_config__,\n      ...(additionalOptions.runtimeConfigOverrides || null),\n    });\n    program.refreshableAssets = baseData.css.map(file => ({\n      url: bundledJsCssUrlRewriteHook(file.url),\n    }));\n  }\n\n  await WebAppInternals.reloadClientPrograms();\n\n  // webserver\n  var app = createExpressApp()\n\n  // Packages and apps can add handlers that run before any other Meteor\n  // handlers via WebApp.rawExpressHandlers.\n  var rawExpressHandlers = createExpressApp()\n  app.use(rawExpressHandlers);\n\n  // Auto-compress any json, javascript, or text.\n  app.use(compress({ filter: shouldCompress }));\n\n  // parse cookies into an object\n  app.use(cookieParser());\n\n  // We're not a proxy; reject (without crashing) attempts to treat us like\n  // one. (See #1212.)\n  app.use(function(req, res, next) {\n    if (RoutePolicy.isValidUrl(req.url)) {\n      next();\n      return;\n    }\n    res.writeHead(400);\n    res.write('Not a proxy');\n    res.end();\n  });\n\n  function getPathParts(path) {\n    const parts = path.split('/');\n    while (parts[0] === '') parts.shift();\n    return parts;\n  }\n\n  function isPrefixOf(prefix, array) {\n    return (\n      prefix.length <= array.length &&\n      prefix.every((part, i) => part === array[i])\n    );\n  }\n\n  // Strip off the path prefix, if it exists.\n  app.use(function(request, response, next) {\n    const pathPrefix = __meteor_runtime_config__.ROOT_URL_PATH_PREFIX;\n    const { pathname, search } = parseUrl(request.url);\n\n    // check if the path in the url starts with the path prefix\n    if (pathPrefix) {\n      const prefixParts = getPathParts(pathPrefix);\n      const pathParts = getPathParts(pathname);\n      if (isPrefixOf(prefixParts, pathParts)) {\n        request.url = '/' + pathParts.slice(prefixParts.length).join('/');\n        if (search) {\n          request.url += search;\n        }\n        return next();\n      }\n    }\n\n    if (pathname === '/favicon.ico' || pathname === '/robots.txt') {\n      return next();\n    }\n\n    if (pathPrefix) {\n      response.writeHead(404);\n      response.write('Unknown path');\n      response.end();\n      return;\n    }\n\n    next();\n  });\n\n  // Serve static files from the manifest.\n  // This is inspired by the 'static' middleware.\n  app.use(function(req, res, next) {\n    // console.log(String(arguments.callee));\n    WebAppInternals.staticFilesMiddleware(\n      WebAppInternals.staticFilesByArch,\n      req,\n      res,\n      next\n    );\n  });\n\n  // Core Meteor packages like dynamic-import can add handlers before\n  // other handlers added by package and application code.\n  app.use((WebAppInternals.meteorInternalHandlers = createExpressApp()));\n\n  /**\n   * @name expressHandlersCallback(req, res, next)\n   * @locus Server\n   * @isprototype true\n   * @summary callback handler for `WebApp.expressHandlers`\n   * @param {Object} req\n   * a Node.js\n   * [IncomingMessage](https://nodejs.org/api/http.html#class-httpincomingmessage)\n   * object with some extra properties. This argument can be used\n   *  to get information about the incoming request.\n   * @param {Object} res\n   * a Node.js\n   * [ServerResponse](https://nodejs.org/api/http.html#class-httpserverresponse)\n   * object. Use this to write data that should be sent in response to the\n   * request, and call `res.end()` when you are done.\n   * @param {Function} next\n   * Calling this function will pass on the handling of\n   * this request to the next relevant handler.\n   *\n   */\n\n  /**\n   * @method handlers\n   * @memberof WebApp\n   * @locus Server\n   * @summary Register a handler for all HTTP requests.\n   * @param {String} [path]\n   * This handler will only be called on paths that match\n   * this string. The match has to border on a `/` or a `.`.\n   *\n   * For example, `/hello` will match `/hello/world` and\n   * `/hello.world`, but not `/hello_world`.\n   * @param {expressHandlersCallback} handler\n   * A handler function that will be called on HTTP requests.\n   * See `expressHandlersCallback`\n   *\n   */\n  // Packages and apps can add handlers to this via WebApp.expressHandlers.\n  // They are inserted before our default handler.\n  var packageAndAppHandlers = createExpressApp()\n  app.use(packageAndAppHandlers);\n\n  let suppressExpressErrors = false;\n  // Express knows it is an error handler because it has 4 arguments instead of\n  // 3. go figure.  (It is not smart enough to find such a thing if it's hidden\n  // inside packageAndAppHandlers.)\n  app.use(function(err, req, res, next) {\n    if (!err || !suppressExpressErrors || !req.headers['x-suppress-error']) {\n      next(err);\n      return;\n    }\n    res.writeHead(err.status, { 'Content-Type': 'text/plain' });\n    res.end('An error message');\n  });\n\n  app.use(async function(req, res, next) {\n    if (!appUrl(req.url)) {\n      return next();\n    } else if (\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      !Meteor.settings.packages?.webapp?.alwaysReturnContent\n    ) {\n      const status = req.method === 'OPTIONS' ? 200 : 405;\n      res.writeHead(status, {\n        Allow: 'OPTIONS, GET, HEAD',\n        'Content-Length': '0',\n      });\n      res.end();\n    } else {\n      var headers = {\n        'Content-Type': 'text/html; charset=utf-8',\n      };\n\n      if (shuttingDown) {\n        headers['Connection'] = 'Close';\n      }\n\n      var request = WebApp.categorizeRequest(req);\n\n      if (request.url.query && request.url.query['meteor_css_resource']) {\n        // In this case, we're requesting a CSS resource in the meteor-specific\n        // way, but we don't have it.  Serve a static css file that indicates that\n        // we didn't have it, so we can detect that and refresh.  Make sure\n        // that any proxies or CDNs don't cache this error!  (Normally proxies\n        // or CDNs are smart enough not to cache error pages, but in order to\n        // make this hack work, we need to return the CSS file as a 200, which\n        // would otherwise be cached.)\n        headers['Content-Type'] = 'text/css; charset=utf-8';\n        headers['Cache-Control'] = 'no-cache';\n        res.writeHead(200, headers);\n        res.write('.meteor-css-not-found-error { width: 0px;}');\n        res.end();\n        return;\n      }\n\n      if (request.url.query && request.url.query['meteor_js_resource']) {\n        // Similarly, we're requesting a JS resource that we don't have.\n        // Serve an uncached 404. (We can't use the same hack we use for CSS,\n        // because actually acting on that hack requires us to have the JS\n        // already!)\n        headers['Cache-Control'] = 'no-cache';\n        res.writeHead(404, headers);\n        res.end('404 Not Found');\n        return;\n      }\n\n      if (request.url.query && request.url.query['meteor_dont_serve_index']) {\n        // When downloading files during a Cordova hot code push, we need\n        // to detect if a file is not available instead of inadvertently\n        // downloading the default index page.\n        // So similar to the situation above, we serve an uncached 404.\n        headers['Cache-Control'] = 'no-cache';\n        res.writeHead(404, headers);\n        res.end('404 Not Found');\n        return;\n      }\n\n      const { arch } = request;\n      assert.strictEqual(typeof arch, 'string', { arch });\n\n      if (!hasOwn.call(WebApp.clientPrograms, arch)) {\n        // We could come here in case we run with some architectures excluded\n        headers['Cache-Control'] = 'no-cache';\n        res.writeHead(404, headers);\n        if (Meteor.isDevelopment) {\n          res.end(`No client program found for the ${arch} architecture.`);\n        } else {\n          // Safety net, but this branch should not be possible.\n          res.end('404 Not Found');\n        }\n        return;\n      }\n\n      // If pauseClient(arch) has been called, program.paused will be a\n      // Promise that will be resolved when the program is unpaused.\n      await WebApp.clientPrograms[arch].paused;\n\n      return getBoilerplateAsync(request, arch)\n        .then(({ stream, statusCode, headers: newHeaders }) => {\n          if (!statusCode) {\n            statusCode = res.statusCode ? res.statusCode : 200;\n          }\n\n          if (newHeaders) {\n            Object.assign(headers, newHeaders);\n          }\n\n          res.writeHead(statusCode, headers);\n\n          stream.pipe(res, {\n            // End the response when the stream ends.\n            end: true,\n          });\n        })\n        .catch(error => {\n          Log.error('Error running template: ' + error.stack);\n          res.writeHead(500, headers);\n          res.end();\n        });\n    }\n  });\n\n  // Return 404 by default, if no other handlers serve this URL.\n  app.use(function(req, res) {\n    res.writeHead(404);\n    res.end();\n  });\n\n  var httpServer = createServer(app);\n  var onListeningCallbacks = [];\n\n  // After 5 seconds w/o data on a socket, kill it.  On the other hand, if\n  // there's an outstanding request, give it a higher timeout instead (to avoid\n  // killing long-polling requests)\n  httpServer.setTimeout(SHORT_SOCKET_TIMEOUT);\n\n  // Do this here, and then also in livedata/stream_server.js, because\n  // stream_server.js kills all the current request handlers when installing its\n  // own.\n  httpServer.on('request', WebApp._timeoutAdjustmentRequestCallback);\n\n  // If the client gave us a bad request, tell it instead of just closing the\n  // socket. This lets load balancers in front of us differentiate between \"a\n  // server is randomly closing sockets for no reason\" and \"client sent a bad\n  // request\".\n  //\n  // This will only work on Node 6; Node 4 destroys the socket before calling\n  // this event. See https://github.com/nodejs/node/pull/4557/ for details.\n  httpServer.on('clientError', (err, socket) => {\n    // Pre-Node-6, do nothing.\n    if (socket.destroyed) {\n      return;\n    }\n\n    if (err.message === 'Parse Error') {\n      socket.end('HTTP/1.1 400 Bad Request\\r\\n\\r\\n');\n    } else {\n      // For other errors, use the default behavior as if we had no clientError\n      // handler.\n      socket.destroy(err);\n    }\n  });\n\n  const suppressErrors = function() {\n    suppressExpressErrors = true;\n  };\n\n  let warnedAboutConnectUsage = false;\n\n  // start up app\n  Object.assign(WebApp, {\n    connectHandlers: packageAndAppHandlers,\n    handlers: packageAndAppHandlers,\n    rawConnectHandlers: rawExpressHandlers,\n    rawHandlers: rawExpressHandlers,\n    httpServer: httpServer,\n    expressApp: app,\n    // For testing.\n    suppressConnectErrors: () => {\n      if (! warnedAboutConnectUsage) {\n        Meteor._debug(\"WebApp.suppressConnectErrors has been renamed to Meteor._suppressExpressErrors and it should be used only in tests.\");\n        warnedAboutConnectUsage = true;\n      }\n      suppressErrors();\n    },\n    _suppressExpressErrors: suppressErrors,\n    onListening: function(f) {\n      if (onListeningCallbacks) onListeningCallbacks.push(f);\n      else f();\n    },\n    // This can be overridden by users who want to modify how listening works\n    // (eg, to run a proxy like Apollo Engine Proxy in front of the server).\n    startListening: function(httpServer, listenOptions, cb) {\n      httpServer.listen(listenOptions, cb);\n    },\n  });\n\n    /**\n   * @name main\n   * @locus Server\n   * @summary Starts the HTTP server.\n   *  If `UNIX_SOCKET_PATH` is present Meteor's HTTP server will use that socket file for inter-process communication, instead of TCP.\n   * If you choose to not include webapp package in your application this method still must be defined for your Meteor application to work.\n   */\n  // Let the rest of the packages (and Meteor.startup hooks) insert Express\n  // middlewares and update __meteor_runtime_config__, then keep going to set up\n  // actually serving HTML.\n  exports.main = async argv => {\n    await WebAppInternals.generateBoilerplate();\n\n    const startHttpServer = listenOptions => {\n      WebApp.startListening(\n        argv?.httpServer || httpServer,\n        listenOptions,\n        Meteor.bindEnvironment(\n          () => {\n            if (process.env.METEOR_PRINT_ON_LISTEN) {\n              console.log('LISTENING');\n            }\n            const callbacks = onListeningCallbacks;\n            onListeningCallbacks = null;\n            callbacks?.forEach(callback => {\n              callback();\n            });\n          },\n          e => {\n            console.error('Error listening:', e);\n            console.error(e && e.stack);\n          }\n        )\n      );\n    };\n\n    let localPort = process.env.PORT || 0;\n    let unixSocketPath = process.env.UNIX_SOCKET_PATH;\n\n    if (unixSocketPath) {\n      if (cluster.isWorker) {\n        const workerName = cluster.worker.process.env.name || cluster.worker.id;\n        unixSocketPath += '.' + workerName + '.sock';\n      }\n      // Start the HTTP server using a socket file.\n      removeExistingSocketFile(unixSocketPath);\n      startHttpServer({ path: unixSocketPath });\n\n      const unixSocketPermissions = (\n        process.env.UNIX_SOCKET_PERMISSIONS || ''\n      ).trim();\n      if (unixSocketPermissions) {\n        if (/^[0-7]{3}$/.test(unixSocketPermissions)) {\n          chmodSync(unixSocketPath, parseInt(unixSocketPermissions, 8));\n        } else {\n          throw new Error('Invalid UNIX_SOCKET_PERMISSIONS specified');\n        }\n      }\n\n      const unixSocketGroup = (process.env.UNIX_SOCKET_GROUP || '').trim();\n      if (unixSocketGroup) {\n        const unixSocketGroupInfo = getGroupInfo(unixSocketGroup);\n        if (unixSocketGroupInfo === null) {\n          throw new Error('Invalid UNIX_SOCKET_GROUP name specified');\n        }\n        chownSync(unixSocketPath, userInfo().uid, unixSocketGroupInfo.gid);\n      }\n\n      registerSocketFileCleanup(unixSocketPath);\n    } else {\n      localPort = isNaN(Number(localPort)) ? localPort : Number(localPort);\n      if (/\\\\\\\\?.+\\\\pipe\\\\?.+/.test(localPort)) {\n        // Start the HTTP server using Windows Server style named pipe.\n        startHttpServer({ path: localPort });\n      } else if (typeof localPort === 'number') {\n        // Start the HTTP server using TCP.\n        startHttpServer({\n          port: localPort,\n          host: process.env.BIND_IP || '0.0.0.0',\n        });\n      } else {\n        throw new Error('Invalid PORT specified');\n      }\n    }\n\n    return 'DAEMON';\n  };\n}\n\nconst isGetentAvailable = () => {\n  try {\n    execSync('which getent');\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nconst getGroupInfoUsingGetent = (groupName) => {\n  try {\n    const stdout = execSync(`getent group ${groupName}`, { encoding: 'utf8' });\n    if (!stdout) return null;\n    const [name, , gid] = stdout.trim().split(':');\n    if (name == null || gid == null) return null;\n    return { name, gid: Number(gid) };\n  } catch (error) {\n    return null;\n  }\n};\n\nconst getGroupInfoFromFile = (groupName) => {\n  try {\n    const data = readFileSync('/etc/group', 'utf8');\n    const groupLine = data.trim().split('\\n').find(line => line.startsWith(`${groupName}:`));\n    if (!groupLine) return null;\n    const [name, , gid] = groupLine.trim().split(':');\n    if (name == null || gid == null) return null;\n    return { name, gid: Number(gid) };\n  } catch (error) {\n    return null;\n  }\n};\n\nexport const getGroupInfo = (groupName) => {\n  let groupInfo = getGroupInfoFromFile(groupName);\n  if (!groupInfo && isGetentAvailable()) {\n    groupInfo = getGroupInfoUsingGetent(groupName);\n  }\n  return groupInfo;\n};\n\nvar inlineScriptsAllowed = true;\n\nWebAppInternals.inlineScriptsAllowed = function() {\n  return inlineScriptsAllowed;\n};\n\nWebAppInternals.setInlineScriptsAllowed = async function(value) {\n  inlineScriptsAllowed = value;\n  await WebAppInternals.generateBoilerplate();\n};\n\nvar sriMode;\n\nWebAppInternals.enableSubresourceIntegrity = async function(use_credentials = false) {\n  sriMode = use_credentials ? 'use-credentials' : 'anonymous';\n  await WebAppInternals.generateBoilerplate();\n};\n\nWebAppInternals.setBundledJsCssUrlRewriteHook = async function(hookFn) {\n  bundledJsCssUrlRewriteHook = hookFn;\n  await WebAppInternals.generateBoilerplate();\n};\n\nWebAppInternals.setBundledJsCssPrefix = async function(prefix) {\n  var self = this;\n  await self.setBundledJsCssUrlRewriteHook(function(url) {\n    return prefix + url;\n  });\n};\n\n// Packages can call `WebAppInternals.addStaticJs` to specify static\n// JavaScript to be included in the app. This static JS will be inlined,\n// unless inline scripts have been disabled, in which case it will be\n// served under `/<sha1 of contents>`.\nvar additionalStaticJs = {};\nWebAppInternals.addStaticJs = function(contents) {\n  additionalStaticJs['/' + sha1(contents) + '.js'] = contents;\n};\n\n// Exported for tests\nWebAppInternals.getBoilerplate = getBoilerplate;\nWebAppInternals.additionalStaticJs = additionalStaticJs;\n\nawait runWebAppServer();\n", "import { statSync, unlinkSync, existsSync } from 'fs';\n\n// Since a new socket file will be created when the HTTP server\n// starts up, if found remove the existing file.\n//\n// WARNING:\n// This will remove the configured socket file without warning. If\n// the configured socket file is already in use by another application,\n// it will still be removed. Node does not provide a reliable way to\n// differentiate between a socket file that is already in use by\n// another application or a stale socket file that has been\n// left over after a SIGKILL. Since we have no reliable way to\n// differentiate between these two scenarios, the best course of\n// action during startup is to remove any existing socket file. This\n// is not the safest course of action as removing the existing socket\n// file could impact an application using it, but this approach helps\n// ensure the HTTP server can startup without manual\n// intervention (e.g. asking for the verification and cleanup of socket\n// files before allowing the HTTP server to be started).\n//\n// The above being said, as long as the socket file path is\n// configured carefully when the application is deployed (and extra\n// care is taken to make sure the configured path is unique and doesn't\n// conflict with another socket file path), then there should not be\n// any issues with this approach.\nexport const removeExistingSocketFile = (socketPath) => {\n  try {\n    if (statSync(socketPath).isSocket()) {\n      // Since a new socket file will be created, remove the existing\n      // file.\n      unlinkSync(socketPath);\n    } else {\n      throw new Error(\n        `An existing file was found at \"${socketPath}\" and it is not ` +\n        'a socket file. Please confirm PORT is pointing to valid and ' +\n        'un-used socket file path.'\n      );\n    }\n  } catch (error) {\n    // If there is no existing socket file to cleanup, great, we'll\n    // continue normally. If the caught exception represents any other\n    // issue, re-throw.\n    if (error.code !== 'ENOENT') {\n      throw error;\n    }\n  }\n};\n\n// Remove the socket file when done to avoid leaving behind a stale one.\n// Note - a stale socket file is still left behind if the running node\n// process is killed via signal 9 - SIGKILL.\nexport const registerSocketFileCleanup =\n  (socketPath, eventEmitter = process) => {\n    ['exit', 'SIGINT', 'SIGHUP', 'SIGTERM'].forEach(signal => {\n      eventEmitter.on(signal, Meteor.bindEnvironment(() => {\n        if (existsSync(socketPath)) {\n          unlinkSync(socketPath);\n        }\n      }));\n    });\n  };\n"]}