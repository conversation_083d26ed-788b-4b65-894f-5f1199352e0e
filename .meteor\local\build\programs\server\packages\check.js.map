{"version": 3, "sources": ["meteor://💻app/packages/check/match.js", "meteor://💻app/packages/check/isPlainObject.js"], "names": ["module", "export", "check", "Match", "isPlainObject", "link", "v", "__reifyWaitForDeps__", "currentArgumentChecker", "Meteor", "EnvironmentVariable", "hasOwn", "Object", "prototype", "hasOwnProperty", "format", "result", "err", "Error", "message", "path", "concat", "value", "pattern", "options", "arguments", "length", "undefined", "throwAllErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getOrNullIfOutsideFiber", "checking", "testSubtree", "Array", "isArray", "map", "r", "Optional", "Maybe", "OneOf", "_len", "args", "_key", "Any", "Where", "condition", "ObjectIncluding", "ObjectWithValues", "Integer", "makeErrorType", "msg", "sanitizedError", "test", "_failIfArgumentsAreNotAllChecked", "f", "context", "description", "<PERSON>rg<PERSON><PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON>", "apply", "throwUnlessAllArgumentsHaveBeenChecked", "constructor", "choices", "stringForErrorMessage", "onlyShowType", "EJSON", "stringify", "JSON", "stringifyError", "name", "typeofChecks", "String", "Number", "Boolean", "Function", "collectErrors", "errors", "i", "isArguments", "arr<PERSON><PERSON>", "_prependPath", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>llowed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredPatterns", "create", "optionalPatterns", "keys", "for<PERSON>ach", "key", "subPattern", "subValue", "obj<PERSON><PERSON>", "call", "createMissingError", "reverse", "_checkingOneValue", "bind", "isNaN", "splice", "_jsKeywords", "base", "match", "indexOf", "isObject", "baseIsArguments", "item", "toString", "callee", "__reify_async_result__", "_reifyError", "self", "async", "class2type", "fnToString", "ObjectFunctionString", "getProto", "getPrototypeOf", "obj", "proto", "Ctor"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,KAAK,EAACA,CAAA,KAAIA,KAAK;MAACC,KAAK,EAACA,CAAA,KAAIA;IAAK,CAAC,CAAC;IAAC,IAAIC,aAAa;IAACJ,MAAM,CAACK,IAAI,CAAC,iBAAiB,EAAC;MAACD,aAAaA,CAACE,CAAC,EAAC;QAACF,aAAa,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAGpM;IACA;;IAEA,MAAMC,sBAAsB,GAAG,IAAIC,MAAM,CAACC,mBAAmB,CAAD,CAAC;IAC7D,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;IAE9C,MAAMC,MAAM,GAAGC,MAAM,IAAI;MACvB,MAAMC,GAAG,GAAG,IAAId,KAAK,CAACe,KAAK,CAACF,MAAM,CAACG,OAAO,CAAC;MAC3C,IAAIH,MAAM,CAACI,IAAI,EAAE;QACfH,GAAG,CAACE,OAAO,iBAAAE,MAAA,CAAiBL,MAAM,CAACI,IAAI,CAAE;QACzCH,GAAG,CAACG,IAAI,GAAGJ,MAAM,CAACI,IAAI;MACxB;MAEA,OAAOH,GAAG;IACZ,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACO,SAASf,KAAKA,CAACoB,KAAK,EAAEC,OAAO,EAAuC;MAAA,IAArCC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;QAAEG,cAAc,EAAE;MAAM,CAAC;MACvE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,UAAU,GAAGrB,sBAAsB,CAACsB,uBAAuB,CAAC,CAAC;MACnE,IAAID,UAAU,EAAE;QACdA,UAAU,CAACE,QAAQ,CAACT,KAAK,CAAC;MAC5B;MAEA,MAAMN,MAAM,GAAGgB,WAAW,CAACV,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAACI,cAAc,CAAC;MAElE,IAAIZ,MAAM,EAAE;QACV,IAAIQ,OAAO,CAACI,cAAc,EAAE;UAC1B,MAAMK,KAAK,CAACC,OAAO,CAAClB,MAAM,CAAC,GAAGA,MAAM,CAACmB,GAAG,CAACC,CAAC,IAAIrB,MAAM,CAACqB,CAAC,CAAC,CAAC,GAAG,CAACrB,MAAM,CAACC,MAAM,CAAC,CAAC;QAC7E,CAAC,MAAM;UACL,MAAMD,MAAM,CAACC,MAAM,CAAC;QACtB;MACF;IACF;IAAC;;IAED;AACA;AACA;AACA;IACO,MAAMb,KAAK,GAAG;MACnBkC,QAAQ,EAAE,SAAAA,CAASd,OAAO,EAAE;QAC1B,OAAO,IAAIc,QAAQ,CAACd,OAAO,CAAC;MAC9B,CAAC;MAEDe,KAAK,EAAE,SAAAA,CAASf,OAAO,EAAE;QACvB,OAAO,IAAIe,KAAK,CAACf,OAAO,CAAC;MAC3B,CAAC;MAEDgB,KAAK,EAAE,SAAAA,CAAA,EAAkB;QAAA,SAAAC,IAAA,GAAAf,SAAA,CAAAC,MAAA,EAANe,IAAI,OAAAR,KAAA,CAAAO,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;UAAJD,IAAI,CAAAC,IAAA,IAAAjB,SAAA,CAAAiB,IAAA;QAAA;QACrB,OAAO,IAAIH,KAAK,CAACE,IAAI,CAAC;MACxB,CAAC;MAEDE,GAAG,EAAE,CAAC,SAAS,CAAC;MAChBC,KAAK,EAAE,SAAAA,CAASC,SAAS,EAAE;QACzB,OAAO,IAAID,KAAK,CAACC,SAAS,CAAC;MAC7B,CAAC;MAEDC,eAAe,EAAE,SAAAA,CAASvB,OAAO,EAAE;QACjC,OAAO,IAAIuB,eAAe,CAACvB,OAAO,CAAC;MACrC,CAAC;MAEDwB,gBAAgB,EAAE,SAAAA,CAASxB,OAAO,EAAE;QAClC,OAAO,IAAIwB,gBAAgB,CAACxB,OAAO,CAAC;MACtC,CAAC;MAED;MACAyB,OAAO,EAAE,CAAC,aAAa,CAAC;MAExB;MACA9B,KAAK,EAAET,MAAM,CAACwC,aAAa,CAAC,aAAa,EAAE,UAAUC,GAAG,EAAE;QACxD,IAAI,CAAC/B,OAAO,mBAAAE,MAAA,CAAmB6B,GAAG,CAAE;;QAEpC;QACA;QACA;QACA;QACA,IAAI,CAAC9B,IAAI,GAAG,EAAE;;QAEd;QACA;QACA,IAAI,CAAC+B,cAAc,GAAG,IAAI1C,MAAM,CAACS,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;MAC7D,CAAC,CAAC;MAEF;MACA;MACA;MACA;MACA;MACA;;MAEA;AACF;AACA;AACA;AACA;AACA;MACEkC,IAAIA,CAAC9B,KAAK,EAAEC,OAAO,EAAE;QACnB,OAAO,CAACS,WAAW,CAACV,KAAK,EAAEC,OAAO,CAAC;MACrC,CAAC;MAED;MACA;MACA;MACA8B,gCAAgCA,CAACC,CAAC,EAAEC,OAAO,EAAEd,IAAI,EAAEe,WAAW,EAAE;QAC9D,MAAM3B,UAAU,GAAG,IAAI4B,eAAe,CAAChB,IAAI,EAAEe,WAAW,CAAC;QACzD,MAAMxC,MAAM,GAAGR,sBAAsB,CAACkD,SAAS,CAC7C7B,UAAU,EACV,MAAMyB,CAAC,CAACK,KAAK,CAACJ,OAAO,EAAEd,IAAI,CAC7B,CAAC;;QAED;QACAZ,UAAU,CAAC+B,sCAAsC,CAAC,CAAC;QACnD,OAAO5C,MAAM;MACf;IACF,CAAC;IAED,MAAMqB,QAAQ,CAAC;MACbwB,WAAWA,CAACtC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB;IACF;IAEA,MAAMe,KAAK,CAAC;MACVuB,WAAWA,CAACtC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB;IACF;IAEA,MAAMgB,KAAK,CAAC;MACVsB,WAAWA,CAACC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACpC,MAAM,KAAK,CAAC,EAAE;UACpC,MAAM,IAAIR,KAAK,CAAC,iDAAiD,CAAC;QACpE;QAEA,IAAI,CAAC4C,OAAO,GAAGA,OAAO;MACxB;IACF;IAEA,MAAMlB,KAAK,CAAC;MACViB,WAAWA,CAAChB,SAAS,EAAE;QACrB,IAAI,CAACA,SAAS,GAAGA,SAAS;MAC5B;IACF;IAEA,MAAMC,eAAe,CAAC;MACpBe,WAAWA,CAACtC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB;IACF;IAEA,MAAMwB,gBAAgB,CAAC;MACrBc,WAAWA,CAACtC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACxB;IACF;IAEA,MAAMwC,qBAAqB,GAAG,SAAAA,CAACzC,KAAK,EAAmB;MAAA,IAAjBE,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAChD,IAAKH,KAAK,KAAK,IAAI,EAAG;QACpB,OAAO,MAAM;MACf;MAEA,IAAKE,OAAO,CAACwC,YAAY,EAAG;QAC1B,OAAO,OAAO1C,KAAK;MACrB;;MAEA;MACA,IAAK,OAAOA,KAAK,KAAK,QAAQ,EAAG;QAC/B,OAAO2C,KAAK,CAACC,SAAS,CAAC5C,KAAK,CAAC;MAC/B;MAEA,IAAI;QAEF;QACA;QACA6C,IAAI,CAACD,SAAS,CAAC5C,KAAK,CAAC;MACvB,CAAC,CAAC,OAAO8C,cAAc,EAAE;QACvB,IAAKA,cAAc,CAACC,IAAI,KAAK,WAAW,EAAG;UACzC,OAAO,OAAO/C,KAAK;QACrB;MACF;MAEA,OAAO2C,KAAK,CAACC,SAAS,CAAC5C,KAAK,CAAC;IAC/B,CAAC;IAED,MAAMgD,YAAY,GAAG,CACnB,CAACC,MAAM,EAAE,QAAQ,CAAC,EAClB,CAACC,MAAM,EAAE,QAAQ,CAAC,EAClB,CAACC,OAAO,EAAE,SAAS,CAAC;IAEpB;IACA;IACA,CAACC,QAAQ,EAAE,UAAU,CAAC,EACtB,CAAC/C,SAAS,EAAE,WAAW,CAAC,CACzB;;IAED;IACA,MAAMK,WAAW,GAAG,SAAAA,CAACV,KAAK,EAAEC,OAAO,EAAoD;MAAA,IAAlDoD,aAAa,GAAAlD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAAA,IAAEmD,MAAM,GAAAnD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MAAA,IAAEL,IAAI,GAAAK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MAChF;MACA,IAAIF,OAAO,KAAKpB,KAAK,CAACwC,GAAG,EAAE;QACzB,OAAO,KAAK;MACd;;MAEA;MACA;MACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,YAAY,CAAC5C,MAAM,EAAE,EAAEmD,CAAC,EAAE;QAC5C,IAAItD,OAAO,KAAK+C,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,IAAI,OAAOvD,KAAK,KAAKgD,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,OAAO,KAAK;UACd;UAEA,OAAO;YACL1D,OAAO,cAAAE,MAAA,CAAciD,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,YAAAxD,MAAA,CAAS0C,qBAAqB,CAACzC,KAAK,EAAE;cAAE0C,YAAY,EAAE;YAAK,CAAC,CAAC,CAAE;YACtG5C,IAAI,EAAE;UACR,CAAC;QACH;MACF;MAEA,IAAIG,OAAO,KAAK,IAAI,EAAE;QACpB,IAAID,KAAK,KAAK,IAAI,EAAE;UAClB,OAAO,KAAK;QACd;QAEA,OAAO;UACLH,OAAO,wBAAAE,MAAA,CAAwB0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;UAC7DF,IAAI,EAAE;QACR,CAAC;MACH;;MAEA;MACA,IAAI,OAAOG,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAC9F,IAAID,KAAK,KAAKC,OAAO,EAAE;UACrB,OAAO,KAAK;QACd;QAEA,OAAO;UACLJ,OAAO,cAAAE,MAAA,CAAcE,OAAO,YAAAF,MAAA,CAAS0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;UACnEF,IAAI,EAAE;QACR,CAAC;MACH;;MAEA;MACA,IAAIG,OAAO,KAAKpB,KAAK,CAAC6C,OAAO,EAAE;QAE7B;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,OAAO1B,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,GAAG,CAAC,MAAMA,KAAK,EAAE;UACtD,OAAO,KAAK;QACd;QAEA,OAAO;UACLH,OAAO,2BAAAE,MAAA,CAA2B0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;UAChEF,IAAI,EAAE;QACR,CAAC;MACH;;MAEA;MACA,IAAIG,OAAO,KAAKX,MAAM,EAAE;QACtBW,OAAO,GAAGpB,KAAK,CAAC2C,eAAe,CAAC,CAAC,CAAC,CAAC;MACrC;;MAEA;MACA,IAAIvB,OAAO,YAAYU,KAAK,EAAE;QAC5B,IAAIV,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;UACxB,OAAO;YACLP,OAAO,oDAAAE,MAAA,CAAoD0C,qBAAqB,CAACxC,OAAO,CAAC,CAAE;YAC3FH,IAAI,EAAE;UACR,CAAC;QACH;QAEA,IAAI,CAACa,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,IAAI,CAACwD,WAAW,CAACxD,KAAK,CAAC,EAAE;UAChD,OAAO;YACLH,OAAO,yBAAAE,MAAA,CAAyB0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;YAC9DF,IAAI,EAAE;UACR,CAAC;QACH;QAGA,KAAK,IAAIyD,CAAC,GAAG,CAAC,EAAEnD,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAEmD,CAAC,GAAGnD,MAAM,EAAEmD,CAAC,EAAE,EAAE;UACtD,MAAME,OAAO,MAAA1D,MAAA,CAAMD,IAAI,OAAAC,MAAA,CAAIwD,CAAC,MAAG;UAC/B,MAAM7D,MAAM,GAAGgB,WAAW,CAACV,KAAK,CAACuD,CAAC,CAAC,EAAEtD,OAAO,CAAC,CAAC,CAAC,EAAEoD,aAAa,EAAEC,MAAM,EAAEG,OAAO,CAAC;UAChF,IAAI/D,MAAM,EAAE;YACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGI,OAAO,GAAGF,CAAC,EAAE7D,MAAM,CAACI,IAAI,CAAC;YACpE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;YACjC,IAAI,OAAOM,KAAK,CAACuD,CAAC,CAAC,KAAK,QAAQ,IAAI7D,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;UACzE;QACF;QAEA,IAAI,CAAC2D,aAAa,EAAE,OAAO,KAAK;QAChC,OAAOC,MAAM,CAAClD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAGkD,MAAM;MAC7C;;MAEA;MACA;MACA,IAAIrD,OAAO,YAAYqB,KAAK,EAAE;QAC5B,IAAI5B,MAAM;QACV,IAAI;UACFA,MAAM,GAAGO,OAAO,CAACsB,SAAS,CAACvB,KAAK,CAAC;QACnC,CAAC,CAAC,OAAOL,GAAG,EAAE;UACZ,IAAI,EAAEA,GAAG,YAAYd,KAAK,CAACe,KAAK,CAAC,EAAE;YACjC,MAAMD,GAAG;UACX;UAEA,OAAO;YACLE,OAAO,EAAEF,GAAG,CAACE,OAAO;YACpBC,IAAI,EAAEH,GAAG,CAACG;UACZ,CAAC;QACH;QAEA,IAAIJ,MAAM,EAAE;UACV,OAAO,KAAK;QACd;;QAEA;;QAEA,OAAO;UACLG,OAAO,EAAE,+BAA+B;UACxCC,IAAI,EAAE;QACR,CAAC;MACH;MAEA,IAAIG,OAAO,YAAYe,KAAK,EAAE;QAC5Bf,OAAO,GAAGpB,KAAK,CAACoC,KAAK,CAACZ,SAAS,EAAE,IAAI,EAAEJ,OAAO,CAACA,OAAO,CAAC;MACzD,CAAC,MAAM,IAAIA,OAAO,YAAYc,QAAQ,EAAE;QACtCd,OAAO,GAAGpB,KAAK,CAACoC,KAAK,CAACZ,SAAS,EAAEJ,OAAO,CAACA,OAAO,CAAC;MACnD;MAEA,IAAIA,OAAO,YAAYgB,KAAK,EAAE;QAC5B,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,OAAO,CAACuC,OAAO,CAACpC,MAAM,EAAE,EAAEmD,CAAC,EAAE;UAC/C,MAAM7D,MAAM,GAAGgB,WAAW,CAACV,KAAK,EAAEC,OAAO,CAACuC,OAAO,CAACe,CAAC,CAAC,CAAC;UACrD,IAAI,CAAC7D,MAAM,EAAE;YAEX;YACA,OAAO,KAAK;UACd;;UAEA;QACF;;QAEA;QACA,OAAO;UACLG,OAAO,EAAE,8DAA8D;UACvEC,IAAI,EAAE;QACR,CAAC;MACH;;MAEA;MACA;MACA,IAAIG,OAAO,YAAYmD,QAAQ,EAAE;QAC/B,IAAIpD,KAAK,YAAYC,OAAO,EAAE;UAC5B,OAAO,KAAK;QACd;QAEA,OAAO;UACLJ,OAAO,cAAAE,MAAA,CAAcE,OAAO,CAAC8C,IAAI,IAAI,wBAAwB,CAAE;UAC/DjD,IAAI,EAAE;QACR,CAAC;MACH;MAEA,IAAI8D,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,iBAAiB;MACrB,IAAI5D,OAAO,YAAYuB,eAAe,EAAE;QACtCoC,kBAAkB,GAAG,IAAI;QACzB3D,OAAO,GAAGA,OAAO,CAACA,OAAO;MAC3B;MAEA,IAAIA,OAAO,YAAYwB,gBAAgB,EAAE;QACvCmC,kBAAkB,GAAG,IAAI;QACzBC,iBAAiB,GAAG,CAAC5D,OAAO,CAACA,OAAO,CAAC;QACrCA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAE;MACjB;MAEA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO;UACLJ,OAAO,EAAE,mCAAmC;UAC5CC,IAAI,EAAE;QACR,CAAC;MACH;;MAEA;MACA;MACA;MACA,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO;UACLH,OAAO,0BAAAE,MAAA,CAA0B,OAAOC,KAAK,CAAE;UAC/CF,IAAI,EAAE;QACR,CAAC;MACH;MAEA,IAAIE,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO;UACLH,OAAO,6BAA6B;UACpCC,IAAI,EAAE;QACR,CAAC;MACH;MAEA,IAAI,CAAEhB,aAAa,CAACkB,KAAK,CAAC,EAAE;QAC1B,OAAO;UACLH,OAAO,yBAAyB;UAChCC,IAAI,EAAE;QACR,CAAC;MACH;MAEA,MAAMgE,gBAAgB,GAAGxE,MAAM,CAACyE,MAAM,CAAC,IAAI,CAAC;MAC5C,MAAMC,gBAAgB,GAAG1E,MAAM,CAACyE,MAAM,CAAC,IAAI,CAAC;MAE5CzE,MAAM,CAAC2E,IAAI,CAAChE,OAAO,CAAC,CAACiE,OAAO,CAACC,GAAG,IAAI;QAClC,MAAMC,UAAU,GAAGnE,OAAO,CAACkE,GAAG,CAAC;QAC/B,IAAIC,UAAU,YAAYrD,QAAQ,IAC9BqD,UAAU,YAAYpD,KAAK,EAAE;UAC/BgD,gBAAgB,CAACG,GAAG,CAAC,GAAGC,UAAU,CAACnE,OAAO;QAC5C,CAAC,MAAM;UACL6D,gBAAgB,CAACK,GAAG,CAAC,GAAGC,UAAU;QACpC;MACF,CAAC,CAAC;MAEF,KAAK,IAAID,GAAG,IAAI7E,MAAM,CAACU,KAAK,CAAC,EAAE;QAC7B,MAAMqE,QAAQ,GAAGrE,KAAK,CAACmE,GAAG,CAAC;QAC3B,MAAMG,OAAO,GAAGxE,IAAI,MAAAC,MAAA,CAAMD,IAAI,OAAAC,MAAA,CAAIoE,GAAG,IAAKA,GAAG;QAC7C,IAAI9E,MAAM,CAACkF,IAAI,CAACT,gBAAgB,EAAEK,GAAG,CAAC,EAAE;UACtC,MAAMzE,MAAM,GAAGgB,WAAW,CAAC2D,QAAQ,EAAEP,gBAAgB,CAACK,GAAG,CAAC,EAAEd,aAAa,EAAEC,MAAM,EAAEgB,OAAO,CAAC;UAC3F,IAAI5E,MAAM,EAAE;YACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGiB,OAAO,GAAGH,GAAG,EAAEzE,MAAM,CAACI,IAAI,CAAC;YACtE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;YACjC,IAAI,OAAO2E,QAAQ,KAAK,QAAQ,IAAI3E,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;UACzE;UAEA,OAAOoE,gBAAgB,CAACK,GAAG,CAAC;QAC9B,CAAC,MAAM,IAAI9E,MAAM,CAACkF,IAAI,CAACP,gBAAgB,EAAEG,GAAG,CAAC,EAAE;UAC7C,MAAMzE,MAAM,GAAGgB,WAAW,CAAC2D,QAAQ,EAAEL,gBAAgB,CAACG,GAAG,CAAC,EAAEd,aAAa,EAAEC,MAAM,EAAEgB,OAAO,CAAC;UAC3F,IAAI5E,MAAM,EAAE;YACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGiB,OAAO,GAAGH,GAAG,EAAEzE,MAAM,CAACI,IAAI,CAAC;YACtE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;YACjC,IAAI,OAAO2E,QAAQ,KAAK,QAAQ,IAAI3E,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;UACzE;QAEF,CAAC,MAAM;UACL,IAAI,CAACkE,kBAAkB,EAAE;YACvB,MAAMlE,MAAM,GAAG;cACbG,OAAO,EAAE,aAAa;cACtBC,IAAI,EAAEqE;YACR,CAAC;YACD,IAAI,CAACd,aAAa,EAAE,OAAO3D,MAAM;YACjC4D,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;UACrB;UAEA,IAAImE,iBAAiB,EAAE;YACrB,MAAMnE,MAAM,GAAGgB,WAAW,CAAC2D,QAAQ,EAAER,iBAAiB,CAAC,CAAC,CAAC,EAAER,aAAa,EAAEC,MAAM,EAAEgB,OAAO,CAAC;YAC1F,IAAI5E,MAAM,EAAE;cACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGiB,OAAO,GAAGH,GAAG,EAAEzE,MAAM,CAACI,IAAI,CAAC;cACtE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;cACjC,IAAI,OAAO2E,QAAQ,KAAK,QAAQ,IAAI3E,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;YACzE;UACF;QACF;MACF;MAEA,MAAMuE,IAAI,GAAG3E,MAAM,CAAC2E,IAAI,CAACH,gBAAgB,CAAC;MAC1C,IAAIG,IAAI,CAAC7D,MAAM,EAAE;QACf,MAAMoE,kBAAkB,GAAGL,GAAG,KAAK;UACjCtE,OAAO,kBAAAE,MAAA,CAAkBoE,GAAG,MAAG;UAC/BrE,IAAI,EAAEuD,aAAa,GAAGvD,IAAI,GAAG;QAC/B,CAAC,CAAC;QAEF,IAAI,CAACuD,aAAa,EAAE;UAClB,OAAOmB,kBAAkB,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC;QAEA,KAAK,MAAME,GAAG,IAAIF,IAAI,EAAE;UACtBX,MAAM,CAACK,IAAI,CAACa,kBAAkB,CAACL,GAAG,CAAC,CAAC;QACtC;MACF;MAEA,IAAI,CAACd,aAAa,EAAE,OAAO,KAAK;MAChC,OAAOC,MAAM,CAAClD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAGkD,MAAM;IAC7C,CAAC;IAED,MAAMnB,eAAe,CAAC;MACpBI,WAAWA,CAAEpB,IAAI,EAAEe,WAAW,EAAE;QAE9B;QACA;QACA,IAAI,CAACf,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC;;QAErB;QACA;QACA;QACA,IAAI,CAACA,IAAI,CAACsD,OAAO,CAAC,CAAC;QACnB,IAAI,CAACvC,WAAW,GAAGA,WAAW;MAChC;MAEAzB,QAAQA,CAACT,KAAK,EAAE;QACd,IAAI,IAAI,CAAC0E,iBAAiB,CAAC1E,KAAK,CAAC,EAAE;UACjC;QACF;;QAEA;QACA;QACA;QACA,IAAIW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,IAAIwD,WAAW,CAACxD,KAAK,CAAC,EAAE;UAC9CW,KAAK,CAACpB,SAAS,CAAC2E,OAAO,CAACK,IAAI,CAACvE,KAAK,EAAE,IAAI,CAAC0E,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE;MACF;MAEAD,iBAAiBA,CAAC1E,KAAK,EAAE;QACvB,KAAK,IAAIuD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpC,IAAI,CAACf,MAAM,EAAE,EAAEmD,CAAC,EAAE;UAEzC;UACA;UACA;UACA;UACA,IAAIvD,KAAK,KAAK,IAAI,CAACmB,IAAI,CAACoC,CAAC,CAAC,IACrBL,MAAM,CAAC0B,KAAK,CAAC5E,KAAK,CAAC,IAAIkD,MAAM,CAAC0B,KAAK,CAAC,IAAI,CAACzD,IAAI,CAACoC,CAAC,CAAC,CAAE,EAAE;YACvD,IAAI,CAACpC,IAAI,CAAC0D,MAAM,CAACtB,CAAC,EAAE,CAAC,CAAC;YACtB,OAAO,IAAI;UACb;QACF;QACA,OAAO,KAAK;MACd;MAEAjB,sCAAsCA,CAAA,EAAG;QACvC,IAAI,IAAI,CAACnB,IAAI,CAACf,MAAM,GAAG,CAAC,EACtB,MAAM,IAAIR,KAAK,yCAAAG,MAAA,CAAyC,IAAI,CAACmC,WAAW,CAAE,CAAC;MAC/E;IACF;IAEA,MAAM4C,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAC9E,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACvE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EACpE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAC3E,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAC3E,YAAY,CAAC;;IAEf;IACA;IACA,MAAMpB,YAAY,GAAGA,CAACS,GAAG,EAAEY,IAAI,KAAK;MAClC,IAAK,OAAOZ,GAAG,KAAM,QAAQ,IAAIA,GAAG,CAACa,KAAK,CAAC,UAAU,CAAC,EAAE;QACtDb,GAAG,OAAApE,MAAA,CAAOoE,GAAG,MAAG;MAClB,CAAC,MAAM,IAAI,CAACA,GAAG,CAACa,KAAK,CAAC,2BAA2B,CAAC,IACvCF,WAAW,CAACG,OAAO,CAACd,GAAG,CAAC,IAAI,CAAC,EAAE;QACxCA,GAAG,GAAGtB,IAAI,CAACD,SAAS,CAAC,CAACuB,GAAG,CAAC,CAAC;MAC7B;MAEA,IAAIY,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3B,UAAAhF,MAAA,CAAUoE,GAAG,OAAApE,MAAA,CAAIgF,IAAI;MACvB;MAEA,OAAOZ,GAAG,GAAGY,IAAI;IACnB,CAAC;IAED,MAAMG,QAAQ,GAAGlF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;IAErE,MAAMmF,eAAe,GAAGC,IAAI,IAC1BF,QAAQ,CAACE,IAAI,CAAC,IACd9F,MAAM,CAACC,SAAS,CAAC8F,QAAQ,CAACd,IAAI,CAACa,IAAI,CAAC,KAAK,oBAAoB;IAE/D,MAAM5B,WAAW,GAAG2B,eAAe,CAAC,YAAW;MAAE,OAAOhF,SAAS;IAAE,CAAC,CAAC,CAAC,CAAC,GACrEgF,eAAe,GACfnF,KAAK,IAAIkF,QAAQ,CAAClF,KAAK,CAAC,IAAI,OAAOA,KAAK,CAACsF,MAAM,KAAK,UAAU;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;ACxkBjEhH,MAAM,CAACC,MAAM,CAAC;EAACG,aAAa,EAACA,CAAA,KAAIA;AAAa,CAAC,CAAC;AAAhD;;AAEA,MAAM6G,UAAU,GAAG,CAAC,CAAC;AAErB,MAAMN,QAAQ,GAAGM,UAAU,CAACN,QAAQ;AAEpC,MAAMhG,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,MAAMoG,UAAU,GAAGvG,MAAM,CAACgG,QAAQ;AAElC,MAAMQ,oBAAoB,GAAGD,UAAU,CAACrB,IAAI,CAACjF,MAAM,CAAC;AAEpD,MAAMwG,QAAQ,GAAGxG,MAAM,CAACyG,cAAc;AAE/B,MAAMjH,aAAa,GAAGkH,GAAG,IAAI;EAClC,IAAIC,KAAK;EACT,IAAIC,IAAI;;EAER;EACA;EACA,IAAI,CAACF,GAAG,IAAIX,QAAQ,CAACd,IAAI,CAACyB,GAAG,CAAC,KAAK,iBAAiB,EAAE;IACpD,OAAO,KAAK;EACd;EAEAC,KAAK,GAAGH,QAAQ,CAACE,GAAG,CAAC;;EAErB;EACA,IAAI,CAACC,KAAK,EAAE;IACV,OAAO,IAAI;EACb;;EAEA;EACAC,IAAI,GAAG7G,MAAM,CAACkF,IAAI,CAAC0B,KAAK,EAAE,aAAa,CAAC,IAAIA,KAAK,CAAC1D,WAAW;EAC7D,OAAO,OAAO2D,IAAI,KAAK,UAAU,IAC/BN,UAAU,CAACrB,IAAI,CAAC2B,IAAI,CAAC,KAAKL,oBAAoB;AAClD,CAAC,C", "file": "/packages/check.js", "sourcesContent": ["// XXX docs\nimport { isPlainObject } from './isPlainObject';\n\n// Things we explicitly do NOT support:\n//    - heterogenous arrays\n\nconst currentArgumentChecker = new Meteor.EnvironmentVariable;\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nconst format = result => {\n  const err = new Match.Error(result.message);\n  if (result.path) {\n    err.message += ` in field ${result.path}`;\n    err.path = result.path;\n  }\n\n  return err;\n}\n\n/**\n * @summary Check that a value matches a [pattern](#matchpatterns).\n * If the value does not match the pattern, throw a `Match.Error`.\n * By default, it will throw immediately at the first error encountered. Pass in { throwAllErrors: true } to throw all errors.\n *\n * Particularly useful to assert that arguments to a function have the right\n * types and structure.\n * @locus Anywhere\n * @param {Any} value The value to check\n * @param {MatchPattern} pattern The pattern to match `value` against\n * @param {Object} [options={}] Additional options for check\n * @param {Boolean} [options.throwAllErrors=false] If true, throw all errors\n */\nexport function check(value, pattern, options = { throwAllErrors: false }) {\n  // Record that check got called, if somebody cared.\n  //\n  // We use getOrNullIfOutsideFiber so that it's OK to call check()\n  // from non-Fiber server contexts; the downside is that if you forget to\n  // bindEnvironment on some random callback in your method/publisher,\n  // it might not find the argumentChecker and you'll get an error about\n  // not checking an argument that it looks like you're checking (instead\n  // of just getting a \"Node code must run in a Fiber\" error).\n  const argChecker = currentArgumentChecker.getOrNullIfOutsideFiber();\n  if (argChecker) {\n    argChecker.checking(value);\n  }\n\n  const result = testSubtree(value, pattern, options.throwAllErrors);\n\n  if (result) {\n    if (options.throwAllErrors) {\n      throw Array.isArray(result) ? result.map(r => format(r)) : [format(result)]\n    } else {\n      throw format(result)\n    }\n  }\n};\n\n/**\n * @namespace Match\n * @summary The namespace for all Match types and methods.\n */\nexport const Match = {\n  Optional: function(pattern) {\n    return new Optional(pattern);\n  },\n\n  Maybe: function(pattern) {\n    return new Maybe(pattern);\n  },\n\n  OneOf: function(...args) {\n    return new OneOf(args);\n  },\n\n  Any: ['__any__'],\n  Where: function(condition) {\n    return new Where(condition);\n  },\n\n  ObjectIncluding: function(pattern) {\n    return new ObjectIncluding(pattern)\n  },\n\n  ObjectWithValues: function(pattern) {\n    return new ObjectWithValues(pattern);\n  },\n\n  // Matches only signed 32-bit integers\n  Integer: ['__integer__'],\n\n  // XXX matchers should know how to describe themselves for errors\n  Error: Meteor.makeErrorType('Match.Error', function (msg) {\n    this.message = `Match error: ${msg}`;\n\n    // The path of the value that failed to match. Initially empty, this gets\n    // populated by catching and rethrowing the exception as it goes back up the\n    // stack.\n    // E.g.: \"vals[3].entity.created\"\n    this.path = '';\n\n    // If this gets sent over DDP, don't give full internal details but at least\n    // provide something better than 500 Internal server error.\n    this.sanitizedError = new Meteor.Error(400, 'Match failed');\n  }),\n\n  // Tests to see if value matches pattern. Unlike check, it merely returns true\n  // or false (unless an error other than Match.Error was thrown). It does not\n  // interact with _failIfArgumentsAreNotAllChecked.\n  // XXX maybe also implement a Match.match which returns more information about\n  //     failures but without using exception handling or doing what check()\n  //     does with _failIfArgumentsAreNotAllChecked and Meteor.Error conversion\n\n  /**\n   * @summary Returns true if the value matches the pattern.\n   * @locus Anywhere\n   * @param {Any} value The value to check\n   * @param {MatchPattern} pattern The pattern to match `value` against\n   */\n  test(value, pattern) {\n    return !testSubtree(value, pattern);\n  },\n\n  // Runs `f.apply(context, args)`. If check() is not called on every element of\n  // `args` (either directly or in the first level of an array), throws an error\n  // (using `description` in the message).\n  _failIfArgumentsAreNotAllChecked(f, context, args, description) {\n    const argChecker = new ArgumentChecker(args, description);\n    const result = currentArgumentChecker.withValue(\n      argChecker,\n      () => f.apply(context, args)\n    );\n\n    // If f didn't itself throw, make sure it checked all of its arguments.\n    argChecker.throwUnlessAllArgumentsHaveBeenChecked();\n    return result;\n  }\n};\n\nclass Optional {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass Maybe {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass OneOf {\n  constructor(choices) {\n    if (!choices || choices.length === 0) {\n      throw new Error('Must provide at least one choice to Match.OneOf');\n    }\n\n    this.choices = choices;\n  }\n}\n\nclass Where {\n  constructor(condition) {\n    this.condition = condition;\n  }\n}\n\nclass ObjectIncluding {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass ObjectWithValues {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nconst stringForErrorMessage = (value, options = {}) => {\n  if ( value === null ) {\n    return 'null';\n  }\n\n  if ( options.onlyShowType ) {\n    return typeof value;\n  }\n\n  // Your average non-object things.  Saves from doing the try/catch below for.\n  if ( typeof value !== 'object' ) {\n    return EJSON.stringify(value)\n  }\n\n  try {\n\n    // Find objects with circular references since EJSON doesn't support them yet (Issue #4778 + Unaccepted PR)\n    // If the native stringify is going to choke, EJSON.stringify is going to choke too.\n    JSON.stringify(value);\n  } catch (stringifyError) {\n    if ( stringifyError.name === 'TypeError' ) {\n      return typeof value;\n    }\n  }\n\n  return EJSON.stringify(value);\n};\n\nconst typeofChecks = [\n  [String, 'string'],\n  [Number, 'number'],\n  [Boolean, 'boolean'],\n\n  // While we don't allow undefined/function in EJSON, this is good for optional\n  // arguments with OneOf.\n  [Function, 'function'],\n  [undefined, 'undefined'],\n];\n\n// Return `false` if it matches. Otherwise, returns an object with a `message` and a `path` field or an array of objects each with a `message` and a `path` field when collecting errors.\nconst testSubtree = (value, pattern, collectErrors = false, errors = [], path = '') => {\n  // Match anything!\n  if (pattern === Match.Any) {\n    return false;\n  }\n\n  // Basic atomic types.\n  // Do not match boxed objects (e.g. String, Boolean)\n  for (let i = 0; i < typeofChecks.length; ++i) {\n    if (pattern === typeofChecks[i][0]) {\n      if (typeof value === typeofChecks[i][1]) {\n        return false;\n      }\n\n      return {\n        message: `Expected ${typeofChecks[i][1]}, got ${stringForErrorMessage(value, { onlyShowType: true })}`,\n        path: '',\n      };\n    }\n  }\n\n  if (pattern === null) {\n    if (value === null) {\n      return false;\n    }\n\n    return {\n      message: `Expected null, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // Strings, numbers, and booleans match literally. Goes well with Match.OneOf.\n  if (typeof pattern === 'string' || typeof pattern === 'number' || typeof pattern === 'boolean') {\n    if (value === pattern) {\n      return false;\n    }\n\n    return {\n      message: `Expected ${pattern}, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // Match.Integer is special type encoded with array\n  if (pattern === Match.Integer) {\n\n    // There is no consistent and reliable way to check if variable is a 64-bit\n    // integer. One of the popular solutions is to get reminder of division by 1\n    // but this method fails on really large floats with big precision.\n    // E.g.: 1.348192308491824e+23 % 1 === 0 in V8\n    // Bitwise operators work consistantly but always cast variable to 32-bit\n    // signed integer according to JavaScript specs.\n    if (typeof value === 'number' && (value | 0) === value) {\n      return false;\n    }\n\n    return {\n      message: `Expected Integer, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // 'Object' is shorthand for Match.ObjectIncluding({});\n  if (pattern === Object) {\n    pattern = Match.ObjectIncluding({});\n  }\n\n  // Array (checked AFTER Any, which is implemented as an Array).\n  if (pattern instanceof Array) {\n    if (pattern.length !== 1) {\n      return {\n        message: `Bad pattern: arrays must have one type element ${stringForErrorMessage(pattern)}`,\n        path: '',\n      };\n    }\n\n    if (!Array.isArray(value) && !isArguments(value)) {\n      return {\n        message: `Expected array, got ${stringForErrorMessage(value)}`,\n        path: '',\n      };\n    }\n\n\n    for (let i = 0, length = value.length; i < length; i++) {\n      const arrPath = `${path}[${i}]`\n      const result = testSubtree(value[i], pattern[0], collectErrors, errors, arrPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? arrPath : i, result.path)\n        if (!collectErrors) return result;\n        if (typeof value[i] !== 'object' || result.message) errors.push(result)\n      }\n    }\n\n    if (!collectErrors) return false;\n    return errors.length === 0 ? false : errors;\n  }\n\n  // Arbitrary validation checks. The condition can return false or throw a\n  // Match.Error (ie, it can internally use check()) to fail.\n  if (pattern instanceof Where) {\n    let result;\n    try {\n      result = pattern.condition(value);\n    } catch (err) {\n      if (!(err instanceof Match.Error)) {\n        throw err;\n      }\n\n      return {\n        message: err.message,\n        path: err.path\n      };\n    }\n\n    if (result) {\n      return false;\n    }\n\n    // XXX this error is terrible\n\n    return {\n      message: 'Failed Match.Where validation',\n      path: '',\n    };\n  }\n\n  if (pattern instanceof Maybe) {\n    pattern = Match.OneOf(undefined, null, pattern.pattern);\n  } else if (pattern instanceof Optional) {\n    pattern = Match.OneOf(undefined, pattern.pattern);\n  }\n\n  if (pattern instanceof OneOf) {\n    for (let i = 0; i < pattern.choices.length; ++i) {\n      const result = testSubtree(value, pattern.choices[i]);\n      if (!result) {\n\n        // No error? Yay, return.\n        return false;\n      }\n\n      // Match errors just mean try another choice.\n    }\n\n    // XXX this error is terrible\n    return {\n      message: 'Failed Match.OneOf, Match.Maybe or Match.Optional validation',\n      path: '',\n    };\n  }\n\n  // A function that isn't something we special-case is assumed to be a\n  // constructor.\n  if (pattern instanceof Function) {\n    if (value instanceof pattern) {\n      return false;\n    }\n\n    return {\n      message: `Expected ${pattern.name || 'particular constructor'}`,\n      path: '',\n    };\n  }\n\n  let unknownKeysAllowed = false;\n  let unknownKeyPattern;\n  if (pattern instanceof ObjectIncluding) {\n    unknownKeysAllowed = true;\n    pattern = pattern.pattern;\n  }\n\n  if (pattern instanceof ObjectWithValues) {\n    unknownKeysAllowed = true;\n    unknownKeyPattern = [pattern.pattern];\n    pattern = {};  // no required keys\n  }\n\n  if (typeof pattern !== 'object') {\n    return {\n      message: 'Bad pattern: unknown pattern type',\n      path: '',\n    };\n  }\n\n  // An object, with required and optional keys. Note that this does NOT do\n  // structural matches against objects of special types that happen to match\n  // the pattern: this really needs to be a plain old {Object}!\n  if (typeof value !== 'object') {\n    return {\n      message: `Expected object, got ${typeof value}`,\n      path: '',\n    };\n  }\n\n  if (value === null) {\n    return {\n      message: `Expected object, got null`,\n      path: '',\n    };\n  }\n\n  if (! isPlainObject(value)) {\n    return {\n      message: `Expected plain object`,\n      path: '',\n    };\n  }\n\n  const requiredPatterns = Object.create(null);\n  const optionalPatterns = Object.create(null);\n\n  Object.keys(pattern).forEach(key => {\n    const subPattern = pattern[key];\n    if (subPattern instanceof Optional ||\n        subPattern instanceof Maybe) {\n      optionalPatterns[key] = subPattern.pattern;\n    } else {\n      requiredPatterns[key] = subPattern;\n    }\n  });\n\n  for (let key in Object(value)) {\n    const subValue = value[key];\n    const objPath = path ? `${path}.${key}` : key;\n    if (hasOwn.call(requiredPatterns, key)) {\n      const result = testSubtree(subValue, requiredPatterns[key], collectErrors, errors, objPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? objPath : key, result.path)\n        if (!collectErrors) return result;\n        if (typeof subValue !== 'object' || result.message) errors.push(result);\n      }\n\n      delete requiredPatterns[key];\n    } else if (hasOwn.call(optionalPatterns, key)) {\n      const result = testSubtree(subValue, optionalPatterns[key], collectErrors, errors, objPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? objPath : key, result.path)\n        if (!collectErrors) return result;\n        if (typeof subValue !== 'object' || result.message) errors.push(result);\n      }\n\n    } else {\n      if (!unknownKeysAllowed) {\n        const result = {\n          message: 'Unknown key',\n          path: key,\n        };\n        if (!collectErrors) return result;\n        errors.push(result);\n      }\n\n      if (unknownKeyPattern) {\n        const result = testSubtree(subValue, unknownKeyPattern[0], collectErrors, errors, objPath);\n        if (result) {\n          result.path = _prependPath(collectErrors ? objPath : key, result.path)\n          if (!collectErrors) return result;\n          if (typeof subValue !== 'object' || result.message) errors.push(result);\n        }\n      }\n    }\n  }\n\n  const keys = Object.keys(requiredPatterns);\n  if (keys.length) {\n    const createMissingError = key => ({\n      message: `Missing key '${key}'`,\n      path: collectErrors ? path : '',\n    });\n\n    if (!collectErrors) {\n      return createMissingError(keys[0]);\n    }\n\n    for (const key of keys) {\n      errors.push(createMissingError(key));\n    }\n  }\n\n  if (!collectErrors) return false;\n  return errors.length === 0 ? false : errors;\n};\n\nclass ArgumentChecker {\n  constructor (args, description) {\n\n    // Make a SHALLOW copy of the arguments. (We'll be doing identity checks\n    // against its contents.)\n    this.args = [...args];\n\n    // Since the common case will be to check arguments in order, and we splice\n    // out arguments when we check them, make it so we splice out from the end\n    // rather than the beginning.\n    this.args.reverse();\n    this.description = description;\n  }\n\n  checking(value) {\n    if (this._checkingOneValue(value)) {\n      return;\n    }\n\n    // Allow check(arguments, [String]) or check(arguments.slice(1), [String])\n    // or check([foo, bar], [String]) to count... but only if value wasn't\n    // itself an argument.\n    if (Array.isArray(value) || isArguments(value)) {\n      Array.prototype.forEach.call(value, this._checkingOneValue.bind(this));\n    }\n  }\n\n  _checkingOneValue(value) {\n    for (let i = 0; i < this.args.length; ++i) {\n\n      // Is this value one of the arguments? (This can have a false positive if\n      // the argument is an interned primitive, but it's still a good enough\n      // check.)\n      // (NaN is not === to itself, so we have to check specially.)\n      if (value === this.args[i] ||\n          (Number.isNaN(value) && Number.isNaN(this.args[i]))) {\n        this.args.splice(i, 1);\n        return true;\n      }\n    }\n    return false;\n  }\n\n  throwUnlessAllArgumentsHaveBeenChecked() {\n    if (this.args.length > 0)\n      throw new Error(`Did not check() all arguments during ${this.description}`);\n  }\n}\n\nconst _jsKeywords = ['do', 'if', 'in', 'for', 'let', 'new', 'try', 'var', 'case',\n  'else', 'enum', 'eval', 'false', 'null', 'this', 'true', 'void', 'with',\n  'break', 'catch', 'class', 'const', 'super', 'throw', 'while', 'yield',\n  'delete', 'export', 'import', 'public', 'return', 'static', 'switch',\n  'typeof', 'default', 'extends', 'finally', 'package', 'private', 'continue',\n  'debugger', 'function', 'arguments', 'interface', 'protected', 'implements',\n  'instanceof'];\n\n// Assumes the base of path is already escaped properly\n// returns key + base\nconst _prependPath = (key, base) => {\n  if ((typeof key) === 'number' || key.match(/^[0-9]+$/)) {\n    key = `[${key}]`;\n  } else if (!key.match(/^[a-z_$][0-9a-z_$.[\\]]*$/i) ||\n             _jsKeywords.indexOf(key) >= 0) {\n    key = JSON.stringify([key]);\n  }\n\n  if (base && base[0] !== '[') {\n    return `${key}.${base}`;\n  }\n\n  return key + base;\n}\n\nconst isObject = value => typeof value === 'object' && value !== null;\n\nconst baseIsArguments = item =>\n  isObject(item) &&\n  Object.prototype.toString.call(item) === '[object Arguments]';\n\nconst isArguments = baseIsArguments(function() { return arguments; }()) ?\n  baseIsArguments :\n  value => isObject(value) && typeof value.callee === 'function';\n", "// Copy of jQuery.isPlainObject for the server side from jQuery v3.1.1.\n\nconst class2type = {};\n\nconst toString = class2type.toString;\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nconst fnToString = hasOwn.toString;\n\nconst ObjectFunctionString = fnToString.call(Object);\n\nconst getProto = Object.getPrototypeOf;\n\nexport const isPlainObject = obj => {\n  let proto;\n  let Ctor;\n\n  // Detect obvious negatives\n  // Use toString instead of jQuery.type to catch host objects\n  if (!obj || toString.call(obj) !== '[object Object]') {\n    return false;\n  }\n\n  proto = getProto(obj);\n\n  // Objects with no prototype (e.g., `Object.create( null )`) are plain\n  if (!proto) {\n    return true;\n  }\n\n  // Objects with prototype are plain iff they were constructed by a global Object function\n  Ctor = hasOwn.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor === 'function' && \n    fnToString.call(Ctor) === ObjectFunctionString;\n};\n"]}