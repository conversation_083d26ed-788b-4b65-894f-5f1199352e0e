{"version": 3, "sources": ["meteor://💻app/packages/base64/base64.js"], "names": ["module", "export", "Base64", "BASE_64_CHARS", "BASE_64_VALS", "Object", "create", "getChar", "val", "char<PERSON>t", "getVal", "ch", "i", "length", "encode", "array", "str", "newBinary", "charCodeAt", "Error", "answer", "a", "b", "c", "d", "push", "join", "len", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret", "$Uint8ArrayPolyfill", "decode", "Math", "floor", "arr", "one", "two", "three", "j", "v"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,MAAM,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,MAAM;EAAA;AAAC,CAAC,CAAC;AAAjD;;AAEA,IAAMC,aAAa,GAAG,kEAAkE;AAExF,IAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAExC,IAAMC,OAAO,GAAG,SAAAA,CAAAC,GAAG;EAAA,OAAIL,aAAa,CAACM,MAAM,CAACD,GAAG,CAAC;AAAA;AAChD,IAAME,MAAM,GAAG,SAAAA,CAAAC,EAAE;EAAA,OAAIA,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAGP,YAAY,CAACO,EAAE,CAAC;AAAA;AAEvD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,aAAa,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;EAC7CR,YAAY,CAACG,OAAO,CAACK,CAAC,CAAC,CAAC,GAAGA,CAAC;AAC9B;AAAC;AAED,IAAME,MAAM,GAAG,SAAAA,CAAAC,KAAK,EAAI;EACtB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAMC,GAAG,GAAGD,KAAK;IACjBA,KAAK,GAAGE,SAAS,CAACD,GAAG,CAACH,MAAM,CAAC;IAC7B,KAAK,IAAID,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGI,GAAG,CAACH,MAAM,EAAED,EAAC,EAAE,EAAE;MACnC,IAAMD,EAAE,GAAGK,GAAG,CAACE,UAAU,CAACN,EAAC,CAAC;MAC5B,IAAID,EAAE,GAAG,IAAI,EAAE;QACb,MAAM,IAAIQ,KAAK,CACb,uDAAuD,CAAC;MAC5D;MAEAJ,KAAK,CAACH,EAAC,CAAC,GAAGD,EAAE;IACf;EACF;EAEA,IAAMS,MAAM,GAAG,EAAE;EACjB,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,IAAI;EAEZ,KAAK,IAAIZ,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGG,KAAK,CAACF,MAAM,EAAED,GAAC,EAAE,EAAE;IACrC,QAAQA,GAAC,GAAG,CAAC;MACX,KAAK,CAAC;QACJS,CAAC,GAAIN,KAAK,CAACH,GAAC,CAAC,IAAI,CAAC,GAAI,IAAI;QAC1BU,CAAC,GAAG,CAACP,KAAK,CAACH,GAAC,CAAC,GAAG,IAAI,KAAK,CAAC;QAC1B;MACF,KAAK,CAAC;QACJU,CAAC,GAAGA,CAAC,GAAIP,KAAK,CAACH,GAAC,CAAC,IAAI,CAAC,GAAI,GAAG;QAC7BW,CAAC,GAAG,CAACR,KAAK,CAACH,GAAC,CAAC,GAAG,GAAG,KAAK,CAAC;QACzB;MACF,KAAK,CAAC;QACJW,CAAC,GAAGA,CAAC,GAAIR,KAAK,CAACH,GAAC,CAAC,IAAI,CAAC,GAAI,IAAI;QAC9BY,CAAC,GAAGT,KAAK,CAACH,GAAC,CAAC,GAAG,IAAI;QACnBQ,MAAM,CAACK,IAAI,CAAClB,OAAO,CAACc,CAAC,CAAC,CAAC;QACvBD,MAAM,CAACK,IAAI,CAAClB,OAAO,CAACe,CAAC,CAAC,CAAC;QACvBF,MAAM,CAACK,IAAI,CAAClB,OAAO,CAACgB,CAAC,CAAC,CAAC;QACvBH,MAAM,CAACK,IAAI,CAAClB,OAAO,CAACiB,CAAC,CAAC,CAAC;QACvBH,CAAC,GAAG,IAAI;QACRC,CAAC,GAAG,IAAI;QACRC,CAAC,GAAG,IAAI;QACRC,CAAC,GAAG,IAAI;QACR;IACJ;EACF;EAEA,IAAIH,CAAC,IAAI,IAAI,EAAE;IACbD,MAAM,CAACK,IAAI,CAAClB,OAAO,CAACc,CAAC,CAAC,CAAC;IACvBD,MAAM,CAACK,IAAI,CAAClB,OAAO,CAACe,CAAC,CAAC,CAAC;IACvB,IAAIC,CAAC,IAAI,IAAI,EAAE;MACbH,MAAM,CAACK,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC,MAAM;MACLL,MAAM,CAACK,IAAI,CAAClB,OAAO,CAACgB,CAAC,CAAC,CAAC;IACzB;IAEA,IAAIC,CAAC,IAAI,IAAI,EAAE;MACbJ,MAAM,CAACK,IAAI,CAAC,GAAG,CAAC;IAClB;EACF;EAEA,OAAOL,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC;AACxB,CAAC;;AAID;AACA;AACA;AACA;AACA;AACA,IAAMT,SAAS,GAAG,SAAAA,CAAAU,GAAG,EAAI;EACvB,IAAI,OAAOC,UAAU,KAAK,WAAW,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAE;IAC3E,IAAMC,GAAG,GAAG,EAAE;IACd,KAAK,IAAIlB,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGe,GAAG,EAAEf,GAAC,EAAE,EAAE;MAC5BkB,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC;IACb;IAEAK,GAAG,CAACC,mBAAmB,GAAG,IAAI;IAC9B,OAAOD,GAAG;EACZ;EACA,OAAO,IAAIF,UAAU,CAAC,IAAIC,WAAW,CAACF,GAAG,CAAC,CAAC;AAC7C,CAAC;AAED,IAAMK,MAAM,GAAG,SAAAA,CAAAhB,GAAG,EAAI;EACpB,IAAIW,GAAG,GAAGM,IAAI,CAACC,KAAK,CAAElB,GAAG,CAACH,MAAM,GAAG,CAAC,GAAI,CAAC,CAAC;EAC1C,IAAIG,GAAG,CAACP,MAAM,CAACO,GAAG,CAACH,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;IACrCc,GAAG,EAAE;IACL,IAAIX,GAAG,CAACP,MAAM,CAACO,GAAG,CAACH,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;MACrCc,GAAG,EAAE;IACP;EACF;EAEA,IAAMQ,GAAG,GAAGlB,SAAS,CAACU,GAAG,CAAC;EAE1B,IAAIS,GAAG,GAAG,IAAI;EACd,IAAIC,GAAG,GAAG,IAAI;EACd,IAAIC,KAAK,GAAG,IAAI;EAEhB,IAAIC,CAAC,GAAG,CAAC;EAET,KAAK,IAAI3B,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGI,GAAG,CAACH,MAAM,EAAED,GAAC,EAAE,EAAE;IACnC,IAAMW,CAAC,GAAGP,GAAG,CAACP,MAAM,CAACG,GAAC,CAAC;IACvB,IAAM4B,CAAC,GAAG9B,MAAM,CAACa,CAAC,CAAC;IACnB,QAAQX,GAAC,GAAG,CAAC;MACX,KAAK,CAAC;QACJ,IAAI4B,CAAC,GAAG,CAAC,EAAE;UACT,MAAM,IAAIrB,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEAiB,GAAG,GAAGI,CAAC,IAAI,CAAC;QACZ;MACF,KAAK,CAAC;QACJ,IAAIA,CAAC,GAAG,CAAC,EAAE;UACT,MAAM,IAAIrB,KAAK,CAAC,uBAAuB,CAAC;QAC1C;QAEAiB,GAAG,GAAGA,GAAG,GAAII,CAAC,IAAI,CAAE;QACpBL,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGH,GAAG;QACdC,GAAG,GAAG,CAACG,CAAC,GAAG,IAAI,KAAK,CAAC;QACrB;MACF,KAAK,CAAC;QACJ,IAAIA,CAAC,IAAI,CAAC,EAAE;UACVH,GAAG,GAAGA,GAAG,GAAIG,CAAC,IAAI,CAAE;UACpBL,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGF,GAAG;UACdC,KAAK,GAAG,CAACE,CAAC,GAAG,IAAI,KAAK,CAAC;QACzB;QAEA;MACF,KAAK,CAAC;QACJ,IAAIA,CAAC,IAAI,CAAC,EAAE;UACVL,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGD,KAAK,GAAGE,CAAC;QACtB;QAEA;IACJ;EACF;EAEA,OAAOL,GAAG;AACZ,CAAC;AAEM,IAAMjC,MAAM,GAAG;EAAEY,MAAM,EAANA,MAAM;EAAEkB,MAAM,EAANA,MAAM;EAAEf,SAAS,EAATA;AAAU,CAAC,C", "file": "/packages/base64.js", "sourcesContent": ["// Base 64 encoding\n\nconst BASE_64_CHARS = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n\nconst BASE_64_VALS = Object.create(null);\n\nconst getChar = val => BASE_64_CHARS.charAt(val);\nconst getVal = ch => ch === '=' ? -1 : BASE_64_VALS[ch];\n\nfor (let i = 0; i < BASE_64_CHARS.length; i++) {\n  BASE_64_VALS[getChar(i)] = i;\n};\n\nconst encode = array => {\n  if (typeof array === \"string\") {\n    const str = array;\n    array = newBinary(str.length);\n    for (let i = 0; i < str.length; i++) {\n      const ch = str.charCodeAt(i);\n      if (ch > 0xFF) {\n        throw new Error(\n          \"Not ascii. Base64.encode can only take ascii strings.\");\n      }\n\n      array[i] = ch;\n    }\n  }\n\n  const answer = [];\n  let a = null;\n  let b = null;\n  let c = null;\n  let d = null;\n\n  for (let i = 0; i < array.length; i++) {\n    switch (i % 3) {\n      case 0:\n        a = (array[i] >> 2) & 0x3F;\n        b = (array[i] & 0x03) << 4;\n        break;\n      case 1:\n        b = b | (array[i] >> 4) & 0xF;\n        c = (array[i] & 0xF) << 2;\n        break;\n      case 2:\n        c = c | (array[i] >> 6) & 0x03;\n        d = array[i] & 0x3F;\n        answer.push(getChar(a));\n        answer.push(getChar(b));\n        answer.push(getChar(c));\n        answer.push(getChar(d));\n        a = null;\n        b = null;\n        c = null;\n        d = null;\n        break;\n    }\n  }\n\n  if (a != null) {\n    answer.push(getChar(a));\n    answer.push(getChar(b));\n    if (c == null) {\n      answer.push('=');\n    } else {\n      answer.push(getChar(c));\n    }\n\n    if (d == null) {\n      answer.push('=');\n    }\n  }\n\n  return answer.join(\"\");\n};\n\n\n\n// XXX This is a weird place for this to live, but it's used both by\n// this package and 'ejson', and we can't put it in 'ejson' without\n// introducing a circular dependency. It should probably be in its own\n// package or as a helper in a package that both 'base64' and 'ejson'\n// use.\nconst newBinary = len => {\n  if (typeof Uint8Array === 'undefined' || typeof ArrayBuffer === 'undefined') {\n    const ret = [];\n    for (let i = 0; i < len; i++) {\n      ret.push(0);\n    }\n\n    ret.$Uint8ArrayPolyfill = true;\n    return ret;\n  }\n  return new Uint8Array(new ArrayBuffer(len));\n};\n\nconst decode = str => {\n  let len = Math.floor((str.length * 3) / 4);\n  if (str.charAt(str.length - 1) == '=') {\n    len--;\n    if (str.charAt(str.length - 2) == '=') {\n      len--;\n    }\n  }\n\n  const arr = newBinary(len);\n\n  let one = null;\n  let two = null;\n  let three = null;\n\n  let j = 0;\n\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charAt(i);\n    const v = getVal(c);\n    switch (i % 4) {\n      case 0:\n        if (v < 0) {\n          throw new Error('invalid base64 string');\n        }\n\n        one = v << 2;\n        break;\n      case 1:\n        if (v < 0) {\n          throw new Error('invalid base64 string');\n        }\n\n        one = one | (v >> 4);\n        arr[j++] = one;\n        two = (v & 0x0F) << 4;\n        break;\n      case 2:\n        if (v >= 0) {\n          two = two | (v >> 2);\n          arr[j++] = two;\n          three = (v & 0x03) << 6;\n        }\n\n        break;\n      case 3:\n        if (v >= 0) {\n          arr[j++] = three | v;\n        }\n\n        break;\n    }\n  }\n\n  return arr;\n};\n\nexport const Base64 = { encode, decode, newBinary };\n"]}