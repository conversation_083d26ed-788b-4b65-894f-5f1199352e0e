import React, { useState } from 'react';
import { Meteor } from 'meteor/meteor';
import { useNavigate } from 'react-router-dom';
import { useTracker } from 'meteor/react-meteor-data';
import { Tasks } from '/imports/api/tasks';

export const TeamMembersPage = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMemberId, setSelectedMemberId] = useState(null);

  const { teamMembers, tasks, isLoading, currentUser } = useTracker(() => {
    const teamMembersHandle = Meteor.subscribe('teamMembers');
    const tasksHandle = Meteor.subscribe('tasks');
    const userHandle = Meteor.subscribe('userData');
    
    const currentUser = Meteor.user();
    const tasks = Tasks.find({}, { sort: { createdAt: -1 } }).fetch();
    
    // Get all tasks that the current user is assigned to
    const userTasks = Tasks.find({
      assignedTo: currentUser?._id
    }).fetch();

    // Get unique team member IDs from those tasks
    const teamMemberIds = new Set();
    userTasks.forEach(task => {
      task.assignedTo.forEach(memberId => {
        if (memberId !== currentUser?._id) {
          teamMemberIds.add(memberId);
        }
      });
    });

    // Fetch team members who share tasks with the current user
    const members = Meteor.users.find(
      { 
        _id: { $in: Array.from(teamMemberIds) }
      },
      { 
        fields: { 
          emails: 1, 
          roles: 1, 
          'profile.firstName': 1,
          'profile.lastName': 1,
          'profile.role': 1,
          'profile.department': 1,
          'profile.skills': 1,
          'profile.joinDate': 1,
          createdAt: 1
        }
      }
    ).fetch();
    
    return {
      teamMembers: members,
      tasks,
      currentUser,
      isLoading: !teamMembersHandle.ready() || !tasksHandle.ready() || !userHandle.ready()
    };
  });

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '24px' }}>
        <div className="loading-spinner"></div>
      </div>
    );
  }

  const filteredMembers = teamMembers.filter(member => {
    const fullName = `${member.profile?.firstName || ''} ${member.profile?.lastName || ''}`.toLowerCase();
    const email = member.emails?.[0]?.address?.toLowerCase() || '';
    
    return searchQuery === '' || 
      fullName.includes(searchQuery.toLowerCase()) ||
      email.includes(searchQuery.toLowerCase());
  });

  // Get shared tasks, attachments, and links for a member
  const getMemberContributions = (memberId) => {
    // Get tasks that both the current user and the member are assigned to
    const sharedTasks = tasks.filter(task => 
      task.assignedTo.includes(memberId) && task.assignedTo.includes(currentUser?._id)
    );

    const attachments = sharedTasks.flatMap(task => 
      (task.attachments || []).filter(attachment => attachment.uploadedBy === memberId)
    );

    const links = sharedTasks.flatMap(task => 
      (task.links || []).filter(link => link.addedBy === memberId)
    );

    return {
      sharedTasks,
      attachments,
      links
    };
  };

  const handleMemberClick = (memberId) => {
    setSelectedMemberId(selectedMemberId === memberId ? null : memberId);
  };

  const handleAttachmentClick = (e, attachment) => {
    e.stopPropagation(); // Prevent card click event
    try {
      // Remove data URL prefix if present
      let base64Data = attachment.data;
      if (base64Data.startsWith('data:')) {
        base64Data = base64Data.split(',')[1];
      }

      // Create a blob from the base64 data
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      
      // Get the correct MIME type
      const mimeType = attachment.type || 'application/octet-stream';
      const blob = new Blob([byteArray], { type: mimeType });

      // Create a download link and trigger it
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = attachment.name;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error('Error downloading file:', error);
      alert('Failed to download file. Please try again.');
    }
  };

  return (
    <div style={{ 
      padding: '24px',
      background: '#ffffff',
      minHeight: 'calc(100vh - 64px)'
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <h2 style={{ 
            color: '#0f172a',
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600'
          }}>Team Members</h2>
        </div>

        <div style={{
          display: 'flex',
          gap: '16px',
          marginBottom: '24px',
          flexWrap: 'wrap'
        }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <input
              type="text"
              placeholder="Search team members..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e2e8f0',
                fontSize: '0.875rem'
              }}
            />
          </div>
        </div>

        {filteredMembers.length > 0 ? (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '24px',
            width: '100%',
          }} className="team-member-grid">
            {filteredMembers.map(member => {
              const { sharedTasks, attachments, links } = getMemberContributions(member._id);
              const isSelected = selectedMemberId === member._id;
              
              return (
                <div 
                  key={member._id} 
                  onClick={() => handleMemberClick(member._id)}
                  style={{
                    backgroundColor: '#ffffff',
                    border: '1px solid #e2e8f0',
                    borderRadius: '12px',
                    padding: '24px',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
                    cursor: 'pointer',
                    ':hover': {
                      borderColor: '#16a34a',
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start'
                  }}>
                    <div>
                      <h3 style={{
                        margin: 0,
                        fontSize: '1.1rem',
                        fontWeight: '600',
                        color: '#0f172a'
                      }}>
                        {member.profile?.firstName} {member.profile?.lastName}
                      </h3>
                      <p style={{
                        margin: '4px 0 0',
                        fontSize: '0.875rem',
                        color: '#64748b'
                      }}>
                        {member.emails?.[0]?.address}
                      </p>
                    </div>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '6px',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      backgroundColor: '#f1f5f9',
                      color: '#475569'
                    }}>
                      {member.profile?.role || 'Team Member'}
                    </span>
                  </div>

                  {/* Shared Task Titles (always show) */}
                  {sharedTasks.length > 0 && (
                    <div style={{ marginTop: '16px' }}>
                      <h4 style={{
                        fontSize: '0.875rem',
                        color: '#64748b',
                        marginBottom: '8px',
                        fontWeight: '500'
                      }}>Shared Tasks</h4>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                        {sharedTasks.map(task => (
                          <div key={task._id} style={{
                            padding: '6px 8px',
                            backgroundColor: '#f8fafc',
                            borderRadius: '6px',
                            fontSize: '0.875rem',
                            color: '#0f172a',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            maxWidth: '100%'
                          }}>
                            {task.title}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Expanded View */}
                  {isSelected && (
                    <div style={{ marginTop: '16px' }}>
                      {/* Show all attachments */}
                      {attachments.length > 1 && (
                        <div style={{ marginBottom: '16px' }}>
                          <h4 style={{
                            fontSize: '0.875rem',
                            color: '#64748b',
                            marginBottom: '8px',
                            fontWeight: '500'
                          }}>All Files</h4>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                            {attachments.slice(1).map((attachment, index) => (
                              <div
                                key={index}
                                onClick={(e) => handleAttachmentClick(e, attachment)}
                                style={{
                                  padding: '8px',
                                  backgroundColor: '#f8fafc',
                                  borderRadius: '6px',
                                  fontSize: '0.875rem',
                                  color: '#0f172a',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '8px',
                                  cursor: 'pointer'
                                }}
                              >
                                <span>📎</span>
                                {attachment.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      {/* Show all links */}
                      {links.length > 1 && (
                        <div style={{ marginBottom: '16px' }}>
                          <h4 style={{
                            fontSize: '0.875rem',
                            color: '#64748b',
                            marginBottom: '8px',
                            fontWeight: '500'
                          }}>All Links</h4>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                            {links.slice(1).map((link, index) => (
                              <a
                                key={index}
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                onClick={(e) => e.stopPropagation()}
                                style={{
                                  padding: '8px',
                                  backgroundColor: '#f8fafc',
                                  borderRadius: '6px',
                                  fontSize: '0.875rem',
                                  color: '#0f172a',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '8px',
                                  textDecoration: 'none',
                                  cursor: 'pointer'
                                }}
                              >
                                <span>🔗</span>
                                {link.url}
                              </a>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <p style={{ textAlign: 'center', color: '#475569' }}>
            {searchQuery
              ? 'No team members match your search'
              : 'No team members share tasks with you yet'}
          </p>
        )}
      </div>
    </div>
  );
}; 