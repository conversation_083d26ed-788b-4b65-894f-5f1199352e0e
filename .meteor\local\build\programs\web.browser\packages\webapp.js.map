{"version": 3, "sources": ["meteor://💻app/packages/webapp/webapp_client.js"], "names": ["module", "export", "WebApp", "_isCssLoaded", "document", "styleSheets", "length", "Array", "prototype", "find", "call", "sheet", "cssText", "cssRules", "match", "rule", "selectorText"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,MAAM,EAACA,CAAA,KAAIA;AAAM,CAAC,CAAC;AAA3B,MAAMA,MAAM,GAAG;EACpBC,YAAYA,CAAA,EAAG;IACb,IAAIC,QAAQ,CAACC,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;MACrC,OAAO,IAAI;IACb;IAEA,OAAOC,KAAK,CAACC,SAAS,CAACC,IAAI,CAACC,IAAI,CAACN,QAAQ,CAACC,WAAW,EAAEM,KAAK,IAAI;MAC9D,IAAIA,KAAK,CAACC,OAAO,IAAI,CAAED,KAAK,CAACE,QAAQ,EAAE;QAAE;QACvC,OAAO,CAAEF,KAAK,CAACC,OAAO,CAACE,KAAK,CAAC,4BAA4B,CAAC;MAC5D;MAEA,OAAO,CAAEP,KAAK,CAACC,SAAS,CAACC,IAAI,CAACC,IAAI,CAChCC,KAAK,CAACE,QAAQ,EACdE,IAAI,IAAIA,IAAI,CAACC,YAAY,KAAK,6BAChC,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC,C", "file": "/packages/webapp.js", "sourcesContent": ["export const WebApp = {\n  _isCssLoaded() {\n    if (document.styleSheets.length === 0) {\n      return true;\n    }\n\n    return Array.prototype.find.call(document.styleSheets, sheet => {\n      if (sheet.cssText && ! sheet.cssRules) { // IE8\n        return ! sheet.cssText.match(/meteor-css-not-found-error/);\n      }\n\n      return ! Array.prototype.find.call(\n        sheet.cssRules,\n        rule => rule.selectorText === '.meteor-css-not-found-error'\n      );\n    });\n  }\n};\n"]}