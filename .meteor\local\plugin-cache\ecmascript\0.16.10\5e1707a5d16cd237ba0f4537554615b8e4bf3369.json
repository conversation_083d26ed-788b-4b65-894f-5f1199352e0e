{"metadata": {}, "options": {"assumptions": {}, "compact": false, "sourceMaps": true, "ast": true, "babelrc": false, "configFile": false, "parserOpts": {"sourceType": "module", "sourceFileName": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\ForgotPasswordPage.jsx", "plugins": ["*", "flow", "jsx", "asyncGenerators", "bigInt", "classPrivateMethods", "classPrivateProperties", "classProperties", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportExtensions", "exportNamespaceFrom", "functionBind", "functionSent", "importMeta", "nullishCoalescingOperator", "numericSeparator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", ["pipelineOperator", {"proposal": "minimal"}], "throwExpressions", "topLevelAwait", "classProperties", "classPrivateProperties", "jsx", "nullishCoalescingOperator", "nullishCoalescingOperator", "optionalChaining", "optionalChaining", "optionalCatchBinding", "optionalCatchBinding", "classProperties", "classPrivateProperties", "classPrivateMethods", "classProperties", "classPrivateProperties", "asyncGenerators", "asyncGenerators", "objectRestSpread", "objectRestSpread", "logicalAssignment"], "allowImportExportEverywhere": true, "allowReturnOutsideFunction": true, "allowUndeclaredExports": true, "strictMode": false}, "caller": {"name": "meteor", "arch": "web.browser.legacy"}, "sourceFileName": "imports/ui/pages/ForgotPasswordPage.jsx", "filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\ForgotPasswordPage.jsx", "inputSourceMap": {"version": 3, "names": ["React", "useState", "Meteor", "Link", "useNavigate", "<PERSON><PERSON>", "Form", "Field", "<PERSON><PERSON>", "ForgotPasswordSchema", "object", "shape", "email", "string", "required", "newPassword", "min", "matches", "confirmPassword", "oneOf", "ref", "ForgotPasswordPage", "_s", "error", "setError", "success", "setSuccess", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "testResult", "setTestResult", "navigate", "handleSubmit", "values", "setSubmitting", "call", "err", "result", "console", "reason", "log", "setTimeout", "testLogin", "password", "debugUser", "errors", "touched", "isSubmitting", "v", "marginTop", "padding", "backgroundColor", "borderRadius", "document", "querySelector", "value", "margin", "fontSize", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["imports/ui/pages/ForgotPasswordPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Meteor } from 'meteor/meteor';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { Formik, Form, Field } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\nconst ForgotPasswordSchema = Yup.object().shape({\r\n  email: Yup.string()\r\n    .email('Invalid email address')\r\n    .required('Email is required'),\r\n  newPassword: Yup.string()\r\n    .required('New password is required')\r\n    .min(8, 'Password must be at least 8 characters')\r\n    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')\r\n    .matches(/[0-9]/, 'Password must contain at least one number')\r\n    .matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\r\n  confirmPassword: Yup.string()\r\n    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')\r\n    .required('Confirm password is required'),\r\n});\r\n\r\nexport const ForgotPasswordPage = () => {\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [testResult, setTestResult] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = (values, { setSubmitting }) => {\r\n    const { email, newPassword } = values;\r\n    setError('');\r\n    setSuccess(false);\r\n\r\n    Meteor.call('users.forgotPassword', { email, newPassword }, (err, result) => {\r\n      setSubmitting(false);\r\n      if (err) {\r\n        console.error('Forgot password error:', err);\r\n        setError(err.reason || 'Failed to reset password. Please try again.');\r\n      } else {\r\n        setSuccess(true);\r\n        console.log('Password reset successful:', result);\r\n        // Redirect to login page after 2 seconds\r\n        setTimeout(() => {\r\n          navigate('/login');\r\n        }, 2000);\r\n      }\r\n    });\r\n  };\r\n\r\n  const testLogin = (email, password) => {\r\n    Meteor.call('users.testLogin', { email, password }, (err, result) => {\r\n      if (err) {\r\n        console.error('Test login error:', err);\r\n        setTestResult({ success: false, error: err.reason });\r\n      } else {\r\n        console.log('Test login result:', result);\r\n        setTestResult(result);\r\n      }\r\n    });\r\n  };\r\n\r\n  const debugUser = (email) => {\r\n    Meteor.call('users.debugUser', { email }, (err, result) => {\r\n      if (err) {\r\n        console.error('Debug user error:', err);\r\n        setTestResult({ success: false, error: err.reason });\r\n      } else {\r\n        console.log('Debug user result:', result);\r\n        setTestResult(result);\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card\">\r\n        <div className=\"auth-header\">\r\n          <div className=\"auth-logo\">\r\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n          </div>\r\n          <h1 className=\"auth-title\">Reset Password</h1>\r\n          <p className=\"auth-subtitle\">Enter your email and new password to reset your account password.</p>\r\n        </div>\r\n        <Formik\r\n          initialValues={{ email: '', newPassword: '', confirmPassword: '' }}\r\n          validationSchema={ForgotPasswordSchema}\r\n          onSubmit={handleSubmit}\r\n        >\r\n          {({ errors, touched, isSubmitting }) => (\r\n            <Form className=\"auth-form\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"email\" className=\"form-label\">Email Address</label>\r\n                <Field\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  placeholder=\"Enter your email address\"\r\n                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}\r\n                  disabled={isSubmitting}\r\n                />\r\n                {errors.email && touched.email && (\r\n                  <div className=\"error-message\">{errors.email}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"newPassword\" className=\"form-label\">New Password</label>\r\n                <div className=\"form-control-password-wrapper\">\r\n                  <Field\r\n                    id=\"newPassword\"\r\n                    name=\"newPassword\"\r\n                    type={showPassword ? 'text' : 'password'}\r\n                    placeholder=\"Enter new password\"\r\n                    className={`form-control ${errors.newPassword && touched.newPassword ? 'form-control-error' : ''}`}\r\n                    disabled={isSubmitting}\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"password-toggle-btn\"\r\n                    tabIndex={-1}\r\n                    aria-label={showPassword ? 'Hide password' : 'Show password'}\r\n                    onClick={() => setShowPassword((v) => !v)}\r\n                  >\r\n                    {showPassword ? (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    ) : (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n                {errors.newPassword && touched.newPassword && (\r\n                  <div className=\"error-message\">{errors.newPassword}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"confirmPassword\" className=\"form-label\">Confirm New Password</label>\r\n                <div className=\"form-control-password-wrapper\">\r\n                  <Field\r\n                    id=\"confirmPassword\"\r\n                    name=\"confirmPassword\"\r\n                    type={showConfirmPassword ? 'text' : 'password'}\r\n                    placeholder=\"Confirm new password\"\r\n                    className={`form-control ${errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''}`}\r\n                    disabled={isSubmitting}\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"password-toggle-btn\"\r\n                    tabIndex={-1}\r\n                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}\r\n                    onClick={() => setShowConfirmPassword((v) => !v)}\r\n                  >\r\n                    {showConfirmPassword ? (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    ) : (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n                {errors.confirmPassword && touched.confirmPassword && (\r\n                  <div className=\"error-message\">{errors.confirmPassword}</div>\r\n                )}\r\n              </div>\r\n              {error && (\r\n                <div className=\"error-message error-message-global\">{error}</div>\r\n              )}\r\n              {success && (\r\n                <div className=\"success-message\">Password reset successful! Redirecting to login...</div>\r\n              )}\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary btn-auth\"\r\n                disabled={isSubmitting}\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <span className=\"loading-spinner\"></span>\r\n                    Resetting...\r\n                  </>\r\n                ) : (\r\n                  'Reset Password'\r\n                )}\r\n              </button>\r\n              <div className=\"auth-links-row\">\r\n                <Link to=\"/login\" className=\"auth-link\">Back to login</Link>\r\n              </div>\r\n            </Form>\r\n          )}\r\n        </Formik>\r\n\r\n        {/* Debug section */}\r\n        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>\r\n          <h4>Debug: Test Login</h4>\r\n          <button\r\n            onClick={() => {\r\n              const email = document.querySelector('input[name=\"email\"]').value;\r\n              const password = document.querySelector('input[name=\"newPassword\"]').value;\r\n              if (email && password) {\r\n                testLogin(email, password);\r\n              }\r\n            }}\r\n            style={{ padding: '5px 10px', margin: '5px' }}\r\n          >\r\n            Test Password\r\n          </button>\r\n          {testResult && (\r\n            <div style={{ marginTop: '10px', fontSize: '12px' }}>\r\n              <strong>Test Result:</strong> <pre>{JSON.stringify(testResult, null, 2)}</pre>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,QAAQ;AAC5C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAE1B,MAAMC,oBAAoB,GAAGD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAC9CC,KAAK,EAAEJ,GAAG,CAACK,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,WAAW,EAAEP,GAAG,CAACK,MAAM,CAAC,CAAC,CACtBC,QAAQ,CAAC,0BAA0B,CAAC,CACpCE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,OAAO,CAAC,OAAO,EAAE,qDAAqD,CAAC,CACvEA,OAAO,CAAC,OAAO,EAAE,2CAA2C,CAAC,CAC7DA,OAAO,CAAC,YAAY,EAAE,iEAAiE,CAAC;EAC3FC,eAAe,EAAEV,GAAG,CAACK,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAACX,GAAG,CAACY,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC7DN,QAAQ,CAAC,8BAA8B;AAC5C,CAAC,CAAC;AAEF,OAAO,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMgC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9B,MAAM8B,YAAY,GAAGA,CAACC,MAAM,EAAE;IAAEC;EAAc,CAAC,KAAK;IAClD,MAAM;MAAExB,KAAK;MAAEG;IAAY,CAAC,GAAGoB,MAAM;IACrCX,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,KAAK,CAAC;IAEjBxB,MAAM,CAACmC,IAAI,CAAC,sBAAsB,EAAE;MAAEzB,KAAK;MAAEG;IAAY,CAAC,EAAE,CAACuB,GAAG,EAAEC,MAAM,KAAK;MAC3EH,aAAa,CAAC,KAAK,CAAC;MACpB,IAAIE,GAAG,EAAE;QACPE,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;QAC5Cd,QAAQ,CAACc,GAAG,CAACG,MAAM,IAAI,6CAA6C,CAAC;MACvE,CAAC,MAAM;QACLf,UAAU,CAAC,IAAI,CAAC;QAChBc,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAEH,MAAM,CAAC;QACjD;QACAI,UAAU,CAAC,MAAM;UACfV,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,SAAS,GAAGA,CAAChC,KAAK,EAAEiC,QAAQ,KAAK;IACrC3C,MAAM,CAACmC,IAAI,CAAC,iBAAiB,EAAE;MAAEzB,KAAK;MAAEiC;IAAS,CAAC,EAAE,CAACP,GAAG,EAAEC,MAAM,KAAK;MACnE,IAAID,GAAG,EAAE;QACPE,OAAO,CAACjB,KAAK,CAAC,mBAAmB,EAAEe,GAAG,CAAC;QACvCN,aAAa,CAAC;UAAEP,OAAO,EAAE,KAAK;UAAEF,KAAK,EAAEe,GAAG,CAACG;QAAO,CAAC,CAAC;MACtD,CAAC,MAAM;QACLD,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,MAAM,CAAC;QACzCP,aAAa,CAACO,MAAM,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,SAAS,GAAIlC,KAAK,IAAK;IAC3BV,MAAM,CAACmC,IAAI,CAAC,iBAAiB,EAAE;MAAEzB;IAAM,CAAC,EAAE,CAAC0B,GAAG,EAAEC,MAAM,KAAK;MACzD,IAAID,GAAG,EAAE;QACPE,OAAO,CAACjB,KAAK,CAAC,mBAAmB,EAAEe,GAAG,CAAC;QACvCN,aAAa,CAAC;UAAEP,OAAO,EAAE,KAAK;UAAEF,KAAK,EAAEe,GAAG,CAACG;QAAO,CAAC,CAAC;MACtD,CAAC,MAAM;QACLD,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,MAAM,CAAC;QACzCP,aAAa,CAACO,MAAM,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC;AACpC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC;AACjC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC;AACrC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC;AACrC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AAC3G,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,oIAAoI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC/O,YAAY,EAAE,GAAG,CAAC;AAClB,UAAU,EAAE,GAAG,CAAC;AAChB,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE,CAAC;AACxD,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,iEAAiE,EAAE,CAAC,CAAC;AAC5G,QAAQ,EAAE,GAAG,CAAC;AACd,QAAQ,CAAC,MAAM,CACL,aAAa,CAAC,CAAC;QAAE3B,KAAK,EAAE,EAAE;QAAEG,WAAW,EAAE,EAAE;QAAEG,eAAe,EAAE;MAAG,CAAC,CAAC,CACnE,gBAAgB,CAAC,CAACT,oBAAoB,CAAC,CACvC,QAAQ,CAAC,CAACyB,YAAY,CAAC,CACxB;AACT,UAAU,CAAC,CAAC;UAAEa,MAAM;UAAEC,OAAO;UAAEC;QAAa,CAAC,KACjC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AACxC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC;AACnF,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,OAAO,CACV,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CACZ,WAAW,CAAC,0BAA0B,CACtC,SAAS,CAAC,CAAC,gBAAgBF,MAAM,CAACnC,KAAK,IAAIoC,OAAO,CAACpC,KAAK,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CACvF,QAAQ,CAAC,CAACqC,YAAY,CAAC,GACvB;AAClB,gBAAgB,CAACF,MAAM,CAACnC,KAAK,IAAIoC,OAAO,CAACpC,KAAK,IAC5B,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAACmC,MAAM,CAACnC,KAAK,CAAC,EAAE,GAAG,CACnD,CAAC;AAClB,cAAc,EAAE,GAAG,CAAC;AACpB,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;AACxF,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,+BAA+B,CAAC;AAC/D,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,aAAa,CAChB,IAAI,CAAC,aAAa,CAClB,IAAI,CAAC,CAACe,YAAY,GAAG,MAAM,GAAG,UAAU,CAAC,CACzC,WAAW,CAAC,oBAAoB,CAChC,SAAS,CAAC,CAAC,gBAAgBoB,MAAM,CAAChC,WAAW,IAAIiC,OAAO,CAACjC,WAAW,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CACnG,QAAQ,CAAC,CAACkC,YAAY,CAAC,GACvB;AACpB,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,qBAAqB,CAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,UAAU,CAAC,CAACtB,YAAY,GAAG,eAAe,GAAG,eAAe,CAAC,CAC7D,OAAO,CAAC,CAAC,MAAMC,eAAe,CAAEsB,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,CAC3C;AACnB,oBAAoB,CAACvB,YAAY,GACX,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wPAAwP,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC7W,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,8FAA8F,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AACnN,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC1I,sBAAsB,EAAE,GAAG,CACN,CAAC;AACtB,kBAAkB,EAAE,MAAM,CAAC;AAC3B,gBAAgB,EAAE,GAAG,CAAC;AACtB,gBAAgB,CAACoB,MAAM,CAAChC,WAAW,IAAIiC,OAAO,CAACjC,WAAW,IACxC,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAACgC,MAAM,CAAChC,WAAW,CAAC,EAAE,GAAG,CACzD,CAAC;AAClB,cAAc,EAAE,GAAG,CAAC;AACpB,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAY,CAAC,oBAAoB,EAAE,KAAK,CAAC;AACpG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,+BAA+B,CAAC;AAC/D,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,iBAAiB,CACpB,IAAI,CAAC,iBAAiB,CACtB,IAAI,CAAC,CAACc,mBAAmB,GAAG,MAAM,GAAG,UAAU,CAAC,CAChD,WAAW,CAAC,sBAAsB,CAClC,SAAS,CAAC,CAAC,gBAAgBkB,MAAM,CAAC7B,eAAe,IAAI8B,OAAO,CAAC9B,eAAe,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CAC3G,QAAQ,CAAC,CAAC+B,YAAY,CAAC,GACvB;AACpB,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,qBAAqB,CAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,UAAU,CAAC,CAACpB,mBAAmB,GAAG,eAAe,GAAG,eAAe,CAAC,CACpE,OAAO,CAAC,CAAC,MAAMC,sBAAsB,CAAEoB,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,CAClD;AACnB,oBAAoB,CAACrB,mBAAmB,GAClB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wPAAwP,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC7W,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,8FAA8F,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AACnN,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC1I,sBAAsB,EAAE,GAAG,CACN,CAAC;AACtB,kBAAkB,EAAE,MAAM,CAAC;AAC3B,gBAAgB,EAAE,GAAG,CAAC;AACtB,gBAAgB,CAACkB,MAAM,CAAC7B,eAAe,IAAI8B,OAAO,CAAC9B,eAAe,IAChD,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC6B,MAAM,CAAC7B,eAAe,CAAC,EAAE,GAAG,CAC7D,CAAC;AAClB,cAAc,EAAE,GAAG,CAAC;AACpB,cAAc,CAACK,KAAK,IACJ,CAAC,GAAG,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAACA,KAAK,CAAC,EAAE,GAAG,CACjE,CAAC;AAChB,cAAc,CAACE,OAAO,IACN,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC,kDAAkD,EAAE,GAAG,CACzF,CAAC;AAChB,cAAc,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,0BAA0B,CACpC,QAAQ,CAAC,CAACwB,YAAY,CAAC,CACxB;AACf,gBAAgB,CAACA,YAAY,GACX,EAAE;AACpB,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC;AAC7D;AACA,kBAAkB,GAAG,GAEH,gBACD,CAAC;AAClB,cAAc,EAAE,MAAM,CAAC;AACvB,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC;AAC9C,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC;AAC5E,cAAc,EAAE,GAAG,CAAC;AACpB,YAAY,EAAE,IAAI,CACP,CAAC;AACZ,QAAQ,EAAE,MAAM,CAAC;AACjB;AACA,QAAQ,CAAC,mBAAmB,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAAEE,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAM,CAAC,CAAC,CAAC;AAC7G,UAAU,CAAC,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC;AACpC,UAAU,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,MAAM;UACb,MAAM1C,KAAK,GAAG2C,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC,CAACC,KAAK;UACjE,MAAMZ,QAAQ,GAAGU,QAAQ,CAACC,aAAa,CAAC,2BAA2B,CAAC,CAACC,KAAK;UAC1E,IAAI7C,KAAK,IAAIiC,QAAQ,EAAE;YACrBD,SAAS,CAAChC,KAAK,EAAEiC,QAAQ,CAAC;UAC5B;QACF,CAAC,CAAC,CACF,KAAK,CAAC,CAAC;UAAEO,OAAO,EAAE,UAAU;UAAEM,MAAM,EAAE;QAAM,CAAC,CAAC,CAC/C;AACX;AACA,UAAU,EAAE,MAAM,CAAC;AACnB,UAAU,CAAC3B,UAAU,IACT,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;UAAEoB,SAAS,EAAE,MAAM;UAAEQ,QAAQ,EAAE;QAAO,CAAC,CAAC,CAAC;AACjE,cAAc,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAACC,IAAI,CAACC,SAAS,CAAC9B,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;AAC5F,YAAY,EAAE,GAAG,CACN,CAAC;AACZ,QAAQ,EAAE,GAAG,CAAC;AACd,MAAM,EAAE,GAAG,CAAC;AACZ,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACT,EAAA,CA7MWD,kBAAkB;EAAA,QAMZjB,WAAW;AAAA;AAAA0D,EAAA,GANjBzC,kBAAkB;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "targets": {}, "cloneInputAst": true, "browserslistConfigFile": false, "passPerPreset": false, "envName": "development", "cwd": "C:\\Users\\<USER>\\smart-task-management-system", "root": "C:\\Users\\<USER>\\smart-task-management-system", "rootMode": "root", "plugins": [{"key": "base$0", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$0", "visitor": {"Program": {"enter": [null], "exit": [null]}, "_exploded": true, "_verified": true}, "options": {"avoidModernSyntax": true, "enforceStrictMode": false, "dynamicImport": true}, "externalDependencies": []}, {"key": "transform-runtime", "visitor": {"MemberExpression": {"enter": [null]}, "ObjectPattern": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "Identifier": {"enter": [null]}, "JSXIdentifier": {"enter": [null]}}, "options": {"version": "7.17.2", "helpers": true, "useESModules": false, "corejs": false}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {"loose": true}, "externalDependencies": []}, {"key": "base$0$3", "visitor": {"FunctionExpression": {"exit": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$4", "visitor": {"ForInStatement": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-jsx", "visitor": {"JSXNamespacedName": {"enter": [null]}, "JSXSpreadChild": {"enter": [null]}, "Program": {"enter": [null]}, "JSXFragment": {"exit": [null]}, "JSXElement": {"exit": [null]}, "JSXAttribute": {"enter": [null]}}, "options": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "runtime": "classic", "throwIfNamespace": true, "useBuiltIns": false}, "externalDependencies": []}, {"key": "transform-react-display-name", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-pure-annotations", "visitor": {"CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "syntax-nullish-coalescing-operator", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-nullish-coalescing-operator", "visitor": {"LogicalExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-chaining", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-chaining", "visitor": {"OptionalCallExpression": {"enter": [null]}, "OptionalMemberExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-catch-binding", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-catch-binding", "visitor": {"CatchClause": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-class-properties", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-async-generators", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-logical-assignment-operators", "visitor": {"AssignmentExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-arrow-functions", "visitor": {"ArrowFunctionExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-block-scoped-functions", "visitor": {"BlockStatement": {"enter": [null]}, "SwitchCase": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-block-scoping", "visitor": {"VariableDeclaration": {"enter": [null, null]}, "BlockStatement": {"enter": [null]}, "SwitchCase": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}, "DoWhileStatement": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForStatement": {"enter": [null]}, "WhileStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-classes", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}, "ClassExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-computed-properties", "visitor": {"ObjectExpression": {"exit": [null]}, "_exploded": true, "_verified": true}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-destructuring", "visitor": {"ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "VariableDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-for-of", "visitor": {"ForOfStatement": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-literals", "visitor": {"NumericLiteral": {"enter": [null]}, "StringLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-object-super", "visitor": {"ObjectExpression": {"enter": [null]}, "_exploded": true, "_verified": true, "DoWhileStatement": {"exit": [null]}, "ForInStatement": {"exit": [null]}, "ForStatement": {"exit": [null]}, "WhileStatement": {"exit": [null]}, "ForOfStatement": {"exit": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-parameters", "visitor": {"_exploded": true, "_verified": true, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-shorthand-properties", "visitor": {"ObjectMethod": {"enter": [null]}, "ObjectProperty": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-spread", "visitor": {"ArrayExpression": {"enter": [null]}, "CallExpression": {"enter": [null]}, "NewExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-sticky-regex", "visitor": {"RegExpLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-template-literals", "visitor": {"TaggedTemplateExpression": {"enter": [null]}, "TemplateLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-typeof-symbol", "visitor": {"UnaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true, "BlockStatement": {"enter": [null]}, "CatchClause": {"enter": [null]}, "DoWhileStatement": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForStatement": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "Program": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "SwitchStatement": {"enter": [null]}, "WhileStatement": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}, "ForOfStatement": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "StaticBlock": {"enter": [null]}, "TSModuleBlock": {"enter": [null]}, "AssignmentPattern": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectPattern": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-unicode-regex", "visitor": {"RegExpLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-property-literals", "visitor": {"ObjectProperty": {"exit": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-exponentiation-operator", "visitor": {"AssignmentExpression": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-regenerator", "visitor": {"ObjectMethod": {"enter": [null], "exit": [null]}, "ClassMethod": {"enter": [null], "exit": [null]}, "ClassPrivateMethod": {"enter": [null], "exit": [null]}, "FunctionDeclaration": {"exit": [null]}, "FunctionExpression": {"exit": [null]}, "ArrowFunctionExpression": {"exit": [null]}, "CallExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}], "presets": [], "generatorOpts": {"filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\pages\\ForgotPasswordPage.jsx", "comments": true, "compact": false, "sourceMaps": true, "sourceFileName": "imports/ui/pages/ForgotPasswordPage.jsx", "inputSourceMap": {"version": 3, "names": ["React", "useState", "Meteor", "Link", "useNavigate", "<PERSON><PERSON>", "Form", "Field", "<PERSON><PERSON>", "ForgotPasswordSchema", "object", "shape", "email", "string", "required", "newPassword", "min", "matches", "confirmPassword", "oneOf", "ref", "ForgotPasswordPage", "_s", "error", "setError", "success", "setSuccess", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "testResult", "setTestResult", "navigate", "handleSubmit", "values", "setSubmitting", "call", "err", "result", "console", "reason", "log", "setTimeout", "testLogin", "password", "debugUser", "errors", "touched", "isSubmitting", "v", "marginTop", "padding", "backgroundColor", "borderRadius", "document", "querySelector", "value", "margin", "fontSize", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["imports/ui/pages/ForgotPasswordPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Meteor } from 'meteor/meteor';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { Formik, Form, Field } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\nconst ForgotPasswordSchema = Yup.object().shape({\r\n  email: Yup.string()\r\n    .email('Invalid email address')\r\n    .required('Email is required'),\r\n  newPassword: Yup.string()\r\n    .required('New password is required')\r\n    .min(8, 'Password must be at least 8 characters')\r\n    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')\r\n    .matches(/[0-9]/, 'Password must contain at least one number')\r\n    .matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\r\n  confirmPassword: Yup.string()\r\n    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')\r\n    .required('Confirm password is required'),\r\n});\r\n\r\nexport const ForgotPasswordPage = () => {\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [testResult, setTestResult] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = (values, { setSubmitting }) => {\r\n    const { email, newPassword } = values;\r\n    setError('');\r\n    setSuccess(false);\r\n\r\n    Meteor.call('users.forgotPassword', { email, newPassword }, (err, result) => {\r\n      setSubmitting(false);\r\n      if (err) {\r\n        console.error('Forgot password error:', err);\r\n        setError(err.reason || 'Failed to reset password. Please try again.');\r\n      } else {\r\n        setSuccess(true);\r\n        console.log('Password reset successful:', result);\r\n        // Redirect to login page after 2 seconds\r\n        setTimeout(() => {\r\n          navigate('/login');\r\n        }, 2000);\r\n      }\r\n    });\r\n  };\r\n\r\n  const testLogin = (email, password) => {\r\n    Meteor.call('users.testLogin', { email, password }, (err, result) => {\r\n      if (err) {\r\n        console.error('Test login error:', err);\r\n        setTestResult({ success: false, error: err.reason });\r\n      } else {\r\n        console.log('Test login result:', result);\r\n        setTestResult(result);\r\n      }\r\n    });\r\n  };\r\n\r\n  const debugUser = (email) => {\r\n    Meteor.call('users.debugUser', { email }, (err, result) => {\r\n      if (err) {\r\n        console.error('Debug user error:', err);\r\n        setTestResult({ success: false, error: err.reason });\r\n      } else {\r\n        console.log('Debug user result:', result);\r\n        setTestResult(result);\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card\">\r\n        <div className=\"auth-header\">\r\n          <div className=\"auth-logo\">\r\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n          </div>\r\n          <h1 className=\"auth-title\">Reset Password</h1>\r\n          <p className=\"auth-subtitle\">Enter your email and new password to reset your account password.</p>\r\n        </div>\r\n        <Formik\r\n          initialValues={{ email: '', newPassword: '', confirmPassword: '' }}\r\n          validationSchema={ForgotPasswordSchema}\r\n          onSubmit={handleSubmit}\r\n        >\r\n          {({ errors, touched, isSubmitting }) => (\r\n            <Form className=\"auth-form\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"email\" className=\"form-label\">Email Address</label>\r\n                <Field\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  placeholder=\"Enter your email address\"\r\n                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}\r\n                  disabled={isSubmitting}\r\n                />\r\n                {errors.email && touched.email && (\r\n                  <div className=\"error-message\">{errors.email}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"newPassword\" className=\"form-label\">New Password</label>\r\n                <div className=\"form-control-password-wrapper\">\r\n                  <Field\r\n                    id=\"newPassword\"\r\n                    name=\"newPassword\"\r\n                    type={showPassword ? 'text' : 'password'}\r\n                    placeholder=\"Enter new password\"\r\n                    className={`form-control ${errors.newPassword && touched.newPassword ? 'form-control-error' : ''}`}\r\n                    disabled={isSubmitting}\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"password-toggle-btn\"\r\n                    tabIndex={-1}\r\n                    aria-label={showPassword ? 'Hide password' : 'Show password'}\r\n                    onClick={() => setShowPassword((v) => !v)}\r\n                  >\r\n                    {showPassword ? (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    ) : (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n                {errors.newPassword && touched.newPassword && (\r\n                  <div className=\"error-message\">{errors.newPassword}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"confirmPassword\" className=\"form-label\">Confirm New Password</label>\r\n                <div className=\"form-control-password-wrapper\">\r\n                  <Field\r\n                    id=\"confirmPassword\"\r\n                    name=\"confirmPassword\"\r\n                    type={showConfirmPassword ? 'text' : 'password'}\r\n                    placeholder=\"Confirm new password\"\r\n                    className={`form-control ${errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''}`}\r\n                    disabled={isSubmitting}\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"password-toggle-btn\"\r\n                    tabIndex={-1}\r\n                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}\r\n                    onClick={() => setShowConfirmPassword((v) => !v)}\r\n                  >\r\n                    {showConfirmPassword ? (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    ) : (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n                {errors.confirmPassword && touched.confirmPassword && (\r\n                  <div className=\"error-message\">{errors.confirmPassword}</div>\r\n                )}\r\n              </div>\r\n              {error && (\r\n                <div className=\"error-message error-message-global\">{error}</div>\r\n              )}\r\n              {success && (\r\n                <div className=\"success-message\">Password reset successful! Redirecting to login...</div>\r\n              )}\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary btn-auth\"\r\n                disabled={isSubmitting}\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <span className=\"loading-spinner\"></span>\r\n                    Resetting...\r\n                  </>\r\n                ) : (\r\n                  'Reset Password'\r\n                )}\r\n              </button>\r\n              <div className=\"auth-links-row\">\r\n                <Link to=\"/login\" className=\"auth-link\">Back to login</Link>\r\n              </div>\r\n            </Form>\r\n          )}\r\n        </Formik>\r\n\r\n        {/* Debug section */}\r\n        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>\r\n          <h4>Debug: Test Login</h4>\r\n          <button\r\n            onClick={() => {\r\n              const email = document.querySelector('input[name=\"email\"]').value;\r\n              const password = document.querySelector('input[name=\"newPassword\"]').value;\r\n              if (email && password) {\r\n                testLogin(email, password);\r\n              }\r\n            }}\r\n            style={{ padding: '5px 10px', margin: '5px' }}\r\n          >\r\n            Test Password\r\n          </button>\r\n          {testResult && (\r\n            <div style={{ marginTop: '10px', fontSize: '12px' }}>\r\n              <strong>Test Result:</strong> <pre>{JSON.stringify(testResult, null, 2)}</pre>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,QAAQ;AAC5C,OAAO,KAAKC,GAAG,MAAM,KAAK;AAE1B,MAAMC,oBAAoB,GAAGD,GAAG,CAACE,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAC9CC,KAAK,EAAEJ,GAAG,CAACK,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,WAAW,EAAEP,GAAG,CAACK,MAAM,CAAC,CAAC,CACtBC,QAAQ,CAAC,0BAA0B,CAAC,CACpCE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,OAAO,CAAC,OAAO,EAAE,qDAAqD,CAAC,CACvEA,OAAO,CAAC,OAAO,EAAE,2CAA2C,CAAC,CAC7DA,OAAO,CAAC,YAAY,EAAE,iEAAiE,CAAC;EAC3FC,eAAe,EAAEV,GAAG,CAACK,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAACX,GAAG,CAACY,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC7DN,QAAQ,CAAC,8BAA8B;AAC5C,CAAC,CAAC;AAEF,OAAO,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMgC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9B,MAAM8B,YAAY,GAAGA,CAACC,MAAM,EAAE;IAAEC;EAAc,CAAC,KAAK;IAClD,MAAM;MAAExB,KAAK;MAAEG;IAAY,CAAC,GAAGoB,MAAM;IACrCX,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,KAAK,CAAC;IAEjBxB,MAAM,CAACmC,IAAI,CAAC,sBAAsB,EAAE;MAAEzB,KAAK;MAAEG;IAAY,CAAC,EAAE,CAACuB,GAAG,EAAEC,MAAM,KAAK;MAC3EH,aAAa,CAAC,KAAK,CAAC;MACpB,IAAIE,GAAG,EAAE;QACPE,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;QAC5Cd,QAAQ,CAACc,GAAG,CAACG,MAAM,IAAI,6CAA6C,CAAC;MACvE,CAAC,MAAM;QACLf,UAAU,CAAC,IAAI,CAAC;QAChBc,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAEH,MAAM,CAAC;QACjD;QACAI,UAAU,CAAC,MAAM;UACfV,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,SAAS,GAAGA,CAAChC,KAAK,EAAEiC,QAAQ,KAAK;IACrC3C,MAAM,CAACmC,IAAI,CAAC,iBAAiB,EAAE;MAAEzB,KAAK;MAAEiC;IAAS,CAAC,EAAE,CAACP,GAAG,EAAEC,MAAM,KAAK;MACnE,IAAID,GAAG,EAAE;QACPE,OAAO,CAACjB,KAAK,CAAC,mBAAmB,EAAEe,GAAG,CAAC;QACvCN,aAAa,CAAC;UAAEP,OAAO,EAAE,KAAK;UAAEF,KAAK,EAAEe,GAAG,CAACG;QAAO,CAAC,CAAC;MACtD,CAAC,MAAM;QACLD,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,MAAM,CAAC;QACzCP,aAAa,CAACO,MAAM,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,SAAS,GAAIlC,KAAK,IAAK;IAC3BV,MAAM,CAACmC,IAAI,CAAC,iBAAiB,EAAE;MAAEzB;IAAM,CAAC,EAAE,CAAC0B,GAAG,EAAEC,MAAM,KAAK;MACzD,IAAID,GAAG,EAAE;QACPE,OAAO,CAACjB,KAAK,CAAC,mBAAmB,EAAEe,GAAG,CAAC;QACvCN,aAAa,CAAC;UAAEP,OAAO,EAAE,KAAK;UAAEF,KAAK,EAAEe,GAAG,CAACG;QAAO,CAAC,CAAC;MACtD,CAAC,MAAM;QACLD,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,MAAM,CAAC;QACzCP,aAAa,CAACO,MAAM,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC;AACpC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC;AACjC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC;AACrC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC;AACrC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AAC3G,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,oIAAoI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC/O,YAAY,EAAE,GAAG,CAAC;AAClB,UAAU,EAAE,GAAG,CAAC;AAChB,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE,CAAC;AACxD,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,iEAAiE,EAAE,CAAC,CAAC;AAC5G,QAAQ,EAAE,GAAG,CAAC;AACd,QAAQ,CAAC,MAAM,CACL,aAAa,CAAC,CAAC;QAAE3B,KAAK,EAAE,EAAE;QAAEG,WAAW,EAAE,EAAE;QAAEG,eAAe,EAAE;MAAG,CAAC,CAAC,CACnE,gBAAgB,CAAC,CAACT,oBAAoB,CAAC,CACvC,QAAQ,CAAC,CAACyB,YAAY,CAAC,CACxB;AACT,UAAU,CAAC,CAAC;UAAEa,MAAM;UAAEC,OAAO;UAAEC;QAAa,CAAC,KACjC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AACxC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC;AACnF,gBAAgB,CAAC,KAAK,CACJ,EAAE,CAAC,OAAO,CACV,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CACZ,WAAW,CAAC,0BAA0B,CACtC,SAAS,CAAC,CAAC,gBAAgBF,MAAM,CAACnC,KAAK,IAAIoC,OAAO,CAACpC,KAAK,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CACvF,QAAQ,CAAC,CAACqC,YAAY,CAAC,GACvB;AAClB,gBAAgB,CAACF,MAAM,CAACnC,KAAK,IAAIoC,OAAO,CAACpC,KAAK,IAC5B,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAACmC,MAAM,CAACnC,KAAK,CAAC,EAAE,GAAG,CACnD,CAAC;AAClB,cAAc,EAAE,GAAG,CAAC;AACpB,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;AACxF,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,+BAA+B,CAAC;AAC/D,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,aAAa,CAChB,IAAI,CAAC,aAAa,CAClB,IAAI,CAAC,CAACe,YAAY,GAAG,MAAM,GAAG,UAAU,CAAC,CACzC,WAAW,CAAC,oBAAoB,CAChC,SAAS,CAAC,CAAC,gBAAgBoB,MAAM,CAAChC,WAAW,IAAIiC,OAAO,CAACjC,WAAW,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CACnG,QAAQ,CAAC,CAACkC,YAAY,CAAC,GACvB;AACpB,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,qBAAqB,CAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,UAAU,CAAC,CAACtB,YAAY,GAAG,eAAe,GAAG,eAAe,CAAC,CAC7D,OAAO,CAAC,CAAC,MAAMC,eAAe,CAAEsB,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,CAC3C;AACnB,oBAAoB,CAACvB,YAAY,GACX,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wPAAwP,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC7W,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,8FAA8F,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AACnN,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC1I,sBAAsB,EAAE,GAAG,CACN,CAAC;AACtB,kBAAkB,EAAE,MAAM,CAAC;AAC3B,gBAAgB,EAAE,GAAG,CAAC;AACtB,gBAAgB,CAACoB,MAAM,CAAChC,WAAW,IAAIiC,OAAO,CAACjC,WAAW,IACxC,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAACgC,MAAM,CAAChC,WAAW,CAAC,EAAE,GAAG,CACzD,CAAC;AAClB,cAAc,EAAE,GAAG,CAAC;AACpB,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC;AAC1C,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAY,CAAC,oBAAoB,EAAE,KAAK,CAAC;AACpG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,+BAA+B,CAAC;AAC/D,kBAAkB,CAAC,KAAK,CACJ,EAAE,CAAC,iBAAiB,CACpB,IAAI,CAAC,iBAAiB,CACtB,IAAI,CAAC,CAACc,mBAAmB,GAAG,MAAM,GAAG,UAAU,CAAC,CAChD,WAAW,CAAC,sBAAsB,CAClC,SAAS,CAAC,CAAC,gBAAgBkB,MAAM,CAAC7B,eAAe,IAAI8B,OAAO,CAAC9B,eAAe,GAAG,oBAAoB,GAAG,EAAE,EAAE,CAAC,CAC3G,QAAQ,CAAC,CAAC+B,YAAY,CAAC,GACvB;AACpB,kBAAkB,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,qBAAqB,CAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,UAAU,CAAC,CAACpB,mBAAmB,GAAG,eAAe,GAAG,eAAe,CAAC,CACpE,OAAO,CAAC,CAAC,MAAMC,sBAAsB,CAAEoB,CAAC,IAAK,CAACA,CAAC,CAAC,CAAC,CAClD;AACnB,oBAAoB,CAACrB,mBAAmB,GAClB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,wPAAwP,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC7W,sBAAsB,EAAE,GAAG,CAAC,GAEN,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACrH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,8FAA8F,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AACnN,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAE;AAC1I,sBAAsB,EAAE,GAAG,CACN,CAAC;AACtB,kBAAkB,EAAE,MAAM,CAAC;AAC3B,gBAAgB,EAAE,GAAG,CAAC;AACtB,gBAAgB,CAACkB,MAAM,CAAC7B,eAAe,IAAI8B,OAAO,CAAC9B,eAAe,IAChD,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC6B,MAAM,CAAC7B,eAAe,CAAC,EAAE,GAAG,CAC7D,CAAC;AAClB,cAAc,EAAE,GAAG,CAAC;AACpB,cAAc,CAACK,KAAK,IACJ,CAAC,GAAG,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAACA,KAAK,CAAC,EAAE,GAAG,CACjE,CAAC;AAChB,cAAc,CAACE,OAAO,IACN,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC,kDAAkD,EAAE,GAAG,CACzF,CAAC;AAChB,cAAc,CAAC,MAAM,CACL,IAAI,CAAC,QAAQ,CACb,SAAS,CAAC,0BAA0B,CACpC,QAAQ,CAAC,CAACwB,YAAY,CAAC,CACxB;AACf,gBAAgB,CAACA,YAAY,GACX,EAAE;AACpB,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC;AAC7D;AACA,kBAAkB,GAAG,GAEH,gBACD,CAAC;AAClB,cAAc,EAAE,MAAM,CAAC;AACvB,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC;AAC9C,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC;AAC5E,cAAc,EAAE,GAAG,CAAC;AACpB,YAAY,EAAE,IAAI,CACP,CAAC;AACZ,QAAQ,EAAE,MAAM,CAAC;AACjB;AACA,QAAQ,CAAC,mBAAmB,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAAEE,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAM,CAAC,CAAC,CAAC;AAC7G,UAAU,CAAC,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC;AACpC,UAAU,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,MAAM;UACb,MAAM1C,KAAK,GAAG2C,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC,CAACC,KAAK;UACjE,MAAMZ,QAAQ,GAAGU,QAAQ,CAACC,aAAa,CAAC,2BAA2B,CAAC,CAACC,KAAK;UAC1E,IAAI7C,KAAK,IAAIiC,QAAQ,EAAE;YACrBD,SAAS,CAAChC,KAAK,EAAEiC,QAAQ,CAAC;UAC5B;QACF,CAAC,CAAC,CACF,KAAK,CAAC,CAAC;UAAEO,OAAO,EAAE,UAAU;UAAEM,MAAM,EAAE;QAAM,CAAC,CAAC,CAC/C;AACX;AACA,UAAU,EAAE,MAAM,CAAC;AACnB,UAAU,CAAC3B,UAAU,IACT,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;UAAEoB,SAAS,EAAE,MAAM;UAAEQ,QAAQ,EAAE;QAAO,CAAC,CAAC,CAAC;AACjE,cAAc,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAACC,IAAI,CAACC,SAAS,CAAC9B,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;AAC5F,YAAY,EAAE,GAAG,CACN,CAAC;AACZ,QAAQ,EAAE,GAAG,CAAC;AACd,MAAM,EAAE,GAAG,CAAC;AACZ,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACT,EAAA,CA7MWD,kBAAkB;EAAA,QAMZjB,WAAW;AAAA;AAAA0D,EAAA,GANjBzC,kBAAkB;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}}}, "code": "!function (module1) {\n  var _slicedToArray;\n  module1.link(\"@babel/runtime/helpers/slicedToArray\", {\n    default: function (v) {\n      _slicedToArray = v;\n    }\n  }, 0);\n  module1.export({\n    ForgotPasswordPage: function () {\n      return ForgotPasswordPage;\n    }\n  });\n  var React, useState;\n  module1.link(\"react\", {\n    \"default\": function (v) {\n      React = v;\n    },\n    useState: function (v) {\n      useState = v;\n    }\n  }, 0);\n  var Meteor;\n  module1.link(\"meteor/meteor\", {\n    Meteor: function (v) {\n      Meteor = v;\n    }\n  }, 1);\n  var Link, useNavigate;\n  module1.link(\"react-router-dom\", {\n    Link: function (v) {\n      Link = v;\n    },\n    useNavigate: function (v) {\n      useNavigate = v;\n    }\n  }, 2);\n  var Formik, Form, Field;\n  module1.link(\"formik\", {\n    Formik: function (v) {\n      Formik = v;\n    },\n    Form: function (v) {\n      Form = v;\n    },\n    Field: function (v) {\n      Field = v;\n    }\n  }, 3);\n  var Yup;\n  module1.link(\"yup\", {\n    \"*\": function (v) {\n      Yup = v;\n    }\n  }, 4);\n  ___INIT_METEOR_FAST_REFRESH(module);\n  var _s = $RefreshSig$();\n  var ForgotPasswordSchema = Yup.object().shape({\n    email: Yup.string().email('Invalid email address').required('Email is required'),\n    newPassword: Yup.string().required('New password is required').min(8, 'Password must be at least 8 characters').matches(/[A-Z]/, 'Password must contain at least one uppercase letter').matches(/[0-9]/, 'Password must contain at least one number').matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\n    confirmPassword: Yup.string().oneOf([Yup.ref('newPassword'), null], 'Passwords must match').required('Confirm password is required')\n  });\n  var ForgotPasswordPage = function () {\n    _s();\n    var _useState = useState(''),\n      _useState2 = _slicedToArray(_useState, 2),\n      error = _useState2[0],\n      setError = _useState2[1];\n    var _useState3 = useState(false),\n      _useState4 = _slicedToArray(_useState3, 2),\n      success = _useState4[0],\n      setSuccess = _useState4[1];\n    var _useState5 = useState(false),\n      _useState6 = _slicedToArray(_useState5, 2),\n      showPassword = _useState6[0],\n      setShowPassword = _useState6[1];\n    var _useState7 = useState(false),\n      _useState8 = _slicedToArray(_useState7, 2),\n      showConfirmPassword = _useState8[0],\n      setShowConfirmPassword = _useState8[1];\n    var _useState9 = useState(null),\n      _useState10 = _slicedToArray(_useState9, 2),\n      testResult = _useState10[0],\n      setTestResult = _useState10[1];\n    var navigate = useNavigate();\n    var handleSubmit = function (values, _ref) {\n      var setSubmitting = _ref.setSubmitting;\n      var email = values.email,\n        newPassword = values.newPassword;\n      setError('');\n      setSuccess(false);\n      Meteor.call('users.forgotPassword', {\n        email: email,\n        newPassword: newPassword\n      }, function (err, result) {\n        setSubmitting(false);\n        if (err) {\n          console.error('Forgot password error:', err);\n          setError(err.reason || 'Failed to reset password. Please try again.');\n        } else {\n          setSuccess(true);\n          console.log('Password reset successful:', result);\n          // Redirect to login page after 2 seconds\n          setTimeout(function () {\n            navigate('/login');\n          }, 2000);\n        }\n      });\n    };\n    var testLogin = function (email, password) {\n      Meteor.call('users.testLogin', {\n        email: email,\n        password: password\n      }, function (err, result) {\n        if (err) {\n          console.error('Test login error:', err);\n          setTestResult({\n            success: false,\n            error: err.reason\n          });\n        } else {\n          console.log('Test login result:', result);\n          setTestResult(result);\n        }\n      });\n    };\n    var debugUser = function (email) {\n      Meteor.call('users.debugUser', {\n        email: email\n      }, function (err, result) {\n        if (err) {\n          console.error('Debug user error:', err);\n          setTestResult({\n            success: false,\n            error: err.reason\n          });\n        } else {\n          console.log('Debug user result:', result);\n          setTestResult(result);\n        }\n      });\n    };\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-container\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-card\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-header\"\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-logo\"\n    }, /*#__PURE__*/React.createElement(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }, /*#__PURE__*/React.createElement(\"path\", {\n      d: \"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }))), /*#__PURE__*/React.createElement(\"h1\", {\n      className: \"auth-title\"\n    }, \"Reset Password\"), /*#__PURE__*/React.createElement(\"p\", {\n      className: \"auth-subtitle\"\n    }, \"Enter your email and new password to reset your account password.\")), /*#__PURE__*/React.createElement(Formik, {\n      initialValues: {\n        email: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      validationSchema: ForgotPasswordSchema,\n      onSubmit: handleSubmit\n    }, function (_ref2) {\n      var errors = _ref2.errors,\n        touched = _ref2.touched,\n        isSubmitting = _ref2.isSubmitting;\n      return /*#__PURE__*/React.createElement(Form, {\n        className: \"auth-form\"\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"email\",\n        className: \"form-label\"\n      }, \"Email Address\"), /*#__PURE__*/React.createElement(Field, {\n        id: \"email\",\n        name: \"email\",\n        type: \"email\",\n        placeholder: \"Enter your email address\",\n        className: \"form-control \" + (errors.email && touched.email ? 'form-control-error' : ''),\n        disabled: isSubmitting\n      }), errors.email && touched.email && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"error-message\"\n      }, errors.email)), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"newPassword\",\n        className: \"form-label\"\n      }, \"New Password\"), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-control-password-wrapper\"\n      }, /*#__PURE__*/React.createElement(Field, {\n        id: \"newPassword\",\n        name: \"newPassword\",\n        type: showPassword ? 'text' : 'password',\n        placeholder: \"Enter new password\",\n        className: \"form-control \" + (errors.newPassword && touched.newPassword ? 'form-control-error' : ''),\n        disabled: isSubmitting\n      }), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: \"password-toggle-btn\",\n        tabIndex: -1,\n        \"aria-label\": showPassword ? 'Hide password' : 'Show password',\n        onClick: function () {\n          return setShowPassword(function (v) {\n            return !v;\n          });\n        }\n      }, showPassword ? /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })) : /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }), /*#__PURE__*/React.createElement(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"3\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })))), errors.newPassword && touched.newPassword && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"error-message\"\n      }, errors.newPassword)), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-group\"\n      }, /*#__PURE__*/React.createElement(\"label\", {\n        htmlFor: \"confirmPassword\",\n        className: \"form-label\"\n      }, \"Confirm New Password\"), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"form-control-password-wrapper\"\n      }, /*#__PURE__*/React.createElement(Field, {\n        id: \"confirmPassword\",\n        name: \"confirmPassword\",\n        type: showConfirmPassword ? 'text' : 'password',\n        placeholder: \"Confirm new password\",\n        className: \"form-control \" + (errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''),\n        disabled: isSubmitting\n      }), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        className: \"password-toggle-btn\",\n        tabIndex: -1,\n        \"aria-label\": showConfirmPassword ? 'Hide password' : 'Show password',\n        onClick: function () {\n          return setShowConfirmPassword(function (v) {\n            return !v;\n          });\n        }\n      }, showConfirmPassword ? /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })) : /*#__PURE__*/React.createElement(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\"\n      }, /*#__PURE__*/React.createElement(\"path\", {\n        d: \"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }), /*#__PURE__*/React.createElement(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"3\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      })))), errors.confirmPassword && touched.confirmPassword && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"error-message\"\n      }, errors.confirmPassword)), error && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"error-message error-message-global\"\n      }, error), success && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"success-message\"\n      }, \"Password reset successful! Redirecting to login...\"), /*#__PURE__*/React.createElement(\"button\", {\n        type: \"submit\",\n        className: \"btn btn-primary btn-auth\",\n        disabled: isSubmitting\n      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"loading-spinner\"\n      }), \"Resetting...\") : 'Reset Password'), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"auth-links-row\"\n      }, /*#__PURE__*/React.createElement(Link, {\n        to: \"/login\",\n        className: \"auth-link\"\n      }, \"Back to login\")));\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        marginTop: '20px',\n        padding: '10px',\n        backgroundColor: '#f5f5f5',\n        borderRadius: '5px'\n      }\n    }, /*#__PURE__*/React.createElement(\"h4\", null, \"Debug: Test Login\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: function () {\n        var email = document.querySelector('input[name=\"email\"]').value;\n        var password = document.querySelector('input[name=\"newPassword\"]').value;\n        if (email && password) {\n          testLogin(email, password);\n        }\n      },\n      style: {\n        padding: '5px 10px',\n        margin: '5px'\n      }\n    }, \"Test Password\"), testResult && /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        marginTop: '10px',\n        fontSize: '12px'\n      }\n    }, /*#__PURE__*/React.createElement(\"strong\", null, \"Test Result:\"), \" \", /*#__PURE__*/React.createElement(\"pre\", null, JSON.stringify(testResult, null, 2))))));\n  };\n  _s(ForgotPasswordPage, \"8EB6Z3j5g6KSDRJNJGrssmxWY/k=\", false, function () {\n    return [useNavigate];\n  });\n  _c = ForgotPasswordPage;\n  var _c;\n  $RefreshReg$(_c, \"ForgotPasswordPage\");\n}.call(this, module);", "map": {"version": 3, "names": ["ForgotPasswordSchema", "<PERSON><PERSON>", "object", "shape", "email", "string", "required", "newPassword", "min", "matches", "confirmPassword", "oneOf", "ref", "ForgotPasswordPage", "_s", "_useState", "useState", "_useState2", "_slicedToArray", "error", "setError", "_useState3", "_useState4", "success", "setSuccess", "_useState5", "_useState6", "showPassword", "setShowPassword", "_useState7", "_useState8", "showConfirmPassword", "setShowConfirmPassword", "_useState9", "_useState10", "testResult", "setTestResult", "navigate", "useNavigate", "handleSubmit", "values", "_ref", "setSubmitting", "Meteor", "call", "err", "result", "console", "reason", "log", "setTimeout", "testLogin", "password", "debugUser", "React", "createElement", "className", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "<PERSON><PERSON>", "initialValues", "validationSchema", "onSubmit", "_ref2", "errors", "touched", "isSubmitting", "Form", "htmlFor", "Field", "id", "name", "type", "placeholder", "disabled", "tabIndex", "onClick", "v", "cx", "cy", "r", "Fragment", "Link", "to", "style", "marginTop", "padding", "backgroundColor", "borderRadius", "document", "querySelector", "value", "margin", "fontSize", "JSON", "stringify", "_c", "$RefreshReg$", "module"], "sources": ["imports/ui/pages/ForgotPasswordPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Meteor } from 'meteor/meteor';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { Formik, Form, Field } from 'formik';\r\nimport * as Yup from 'yup';\r\n\r\nconst ForgotPasswordSchema = Yup.object().shape({\r\n  email: Yup.string()\r\n    .email('Invalid email address')\r\n    .required('Email is required'),\r\n  newPassword: Yup.string()\r\n    .required('New password is required')\r\n    .min(8, 'Password must be at least 8 characters')\r\n    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')\r\n    .matches(/[0-9]/, 'Password must contain at least one number')\r\n    .matches(/[!@#$%^&*]/, 'Password must contain at least one special character (!@#$%^&*)'),\r\n  confirmPassword: Yup.string()\r\n    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')\r\n    .required('Confirm password is required'),\r\n});\r\n\r\nexport const ForgotPasswordPage = () => {\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [testResult, setTestResult] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = (values, { setSubmitting }) => {\r\n    const { email, newPassword } = values;\r\n    setError('');\r\n    setSuccess(false);\r\n\r\n    Meteor.call('users.forgotPassword', { email, newPassword }, (err, result) => {\r\n      setSubmitting(false);\r\n      if (err) {\r\n        console.error('Forgot password error:', err);\r\n        setError(err.reason || 'Failed to reset password. Please try again.');\r\n      } else {\r\n        setSuccess(true);\r\n        console.log('Password reset successful:', result);\r\n        // Redirect to login page after 2 seconds\r\n        setTimeout(() => {\r\n          navigate('/login');\r\n        }, 2000);\r\n      }\r\n    });\r\n  };\r\n\r\n  const testLogin = (email, password) => {\r\n    Meteor.call('users.testLogin', { email, password }, (err, result) => {\r\n      if (err) {\r\n        console.error('Test login error:', err);\r\n        setTestResult({ success: false, error: err.reason });\r\n      } else {\r\n        console.log('Test login result:', result);\r\n        setTestResult(result);\r\n      }\r\n    });\r\n  };\r\n\r\n  const debugUser = (email) => {\r\n    Meteor.call('users.debugUser', { email }, (err, result) => {\r\n      if (err) {\r\n        console.error('Debug user error:', err);\r\n        setTestResult({ success: false, error: err.reason });\r\n      } else {\r\n        console.log('Debug user result:', result);\r\n        setTestResult(result);\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card\">\r\n        <div className=\"auth-header\">\r\n          <div className=\"auth-logo\">\r\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n          </div>\r\n          <h1 className=\"auth-title\">Reset Password</h1>\r\n          <p className=\"auth-subtitle\">Enter your email and new password to reset your account password.</p>\r\n        </div>\r\n        <Formik\r\n          initialValues={{ email: '', newPassword: '', confirmPassword: '' }}\r\n          validationSchema={ForgotPasswordSchema}\r\n          onSubmit={handleSubmit}\r\n        >\r\n          {({ errors, touched, isSubmitting }) => (\r\n            <Form className=\"auth-form\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"email\" className=\"form-label\">Email Address</label>\r\n                <Field\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  placeholder=\"Enter your email address\"\r\n                  className={`form-control ${errors.email && touched.email ? 'form-control-error' : ''}`}\r\n                  disabled={isSubmitting}\r\n                />\r\n                {errors.email && touched.email && (\r\n                  <div className=\"error-message\">{errors.email}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"newPassword\" className=\"form-label\">New Password</label>\r\n                <div className=\"form-control-password-wrapper\">\r\n                  <Field\r\n                    id=\"newPassword\"\r\n                    name=\"newPassword\"\r\n                    type={showPassword ? 'text' : 'password'}\r\n                    placeholder=\"Enter new password\"\r\n                    className={`form-control ${errors.newPassword && touched.newPassword ? 'form-control-error' : ''}`}\r\n                    disabled={isSubmitting}\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"password-toggle-btn\"\r\n                    tabIndex={-1}\r\n                    aria-label={showPassword ? 'Hide password' : 'Show password'}\r\n                    onClick={() => setShowPassword((v) => !v)}\r\n                  >\r\n                    {showPassword ? (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    ) : (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n                {errors.newPassword && touched.newPassword && (\r\n                  <div className=\"error-message\">{errors.newPassword}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"confirmPassword\" className=\"form-label\">Confirm New Password</label>\r\n                <div className=\"form-control-password-wrapper\">\r\n                  <Field\r\n                    id=\"confirmPassword\"\r\n                    name=\"confirmPassword\"\r\n                    type={showConfirmPassword ? 'text' : 'password'}\r\n                    placeholder=\"Confirm new password\"\r\n                    className={`form-control ${errors.confirmPassword && touched.confirmPassword ? 'form-control-error' : ''}`}\r\n                    disabled={isSubmitting}\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"password-toggle-btn\"\r\n                    tabIndex={-1}\r\n                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}\r\n                    onClick={() => setShowConfirmPassword((v) => !v)}\r\n                  >\r\n                    {showConfirmPassword ? (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M17.94 17.94C16.11 19.25 14.11 20 12 20C7 20 2.73 16.11 1 12C1.73 10.19 2.91 8.6 4.44 7.35M9.9 5.08C10.59 5.03 11.29 5 12 5C17 5 21.27 8.89 23 13C22.37 14.53 21.34 15.91 19.97 17.03M15 12A3 3 0 0 1 12 15M12 15A3 3 0 0 1 9 12M12 15V15.01M2 2L22 22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    ) : (\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M1 12C2.73 16.11 7 20 12 20C17 20 21.27 16.11 23 12C21.27 7.89 17 4 12 4C7 4 2.73 7.89 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n                {errors.confirmPassword && touched.confirmPassword && (\r\n                  <div className=\"error-message\">{errors.confirmPassword}</div>\r\n                )}\r\n              </div>\r\n              {error && (\r\n                <div className=\"error-message error-message-global\">{error}</div>\r\n              )}\r\n              {success && (\r\n                <div className=\"success-message\">Password reset successful! Redirecting to login...</div>\r\n              )}\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary btn-auth\"\r\n                disabled={isSubmitting}\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <span className=\"loading-spinner\"></span>\r\n                    Resetting...\r\n                  </>\r\n                ) : (\r\n                  'Reset Password'\r\n                )}\r\n              </button>\r\n              <div className=\"auth-links-row\">\r\n                <Link to=\"/login\" className=\"auth-link\">Back to login</Link>\r\n              </div>\r\n            </Form>\r\n          )}\r\n        </Formik>\r\n\r\n        {/* Debug section */}\r\n        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>\r\n          <h4>Debug: Test Login</h4>\r\n          <button\r\n            onClick={() => {\r\n              const email = document.querySelector('input[name=\"email\"]').value;\r\n              const password = document.querySelector('input[name=\"newPassword\"]').value;\r\n              if (email && password) {\r\n                testLogin(email, password);\r\n              }\r\n            }}\r\n            style={{ padding: '5px 10px', margin: '5px' }}\r\n          >\r\n            Test Password\r\n          </button>\r\n          {testResult && (\r\n            <div style={{ marginTop: '10px', fontSize: '12px' }}>\r\n              <strong>Test Result:</strong> <pre>{JSON.stringify(testResult, null, 2)}</pre>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAMA,IAAMA,oBAAoB,GAAGC,GAAG,CAACC,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC9CC,KAAK,EAAEH,GAAG,CAACI,MAAM,CAAC,CAAC,CAChBD,KAAK,CAAC,uBAAuB,CAAC,CAC9BE,QAAQ,CAAC,mBAAmB,CAAC;IAChCC,WAAW,EAAEN,GAAG,CAACI,MAAM,CAAC,CAAC,CACtBC,QAAQ,CAAC,0BAA0B,CAAC,CACpCE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,OAAO,CAAC,OAAO,EAAE,qDAAqD,CAAC,CACvEA,OAAO,CAAC,OAAO,EAAE,2CAA2C,CAAC,CAC7DA,OAAO,CAAC,YAAY,EAAE,iEAAiE,CAAC;IAC3FC,eAAe,EAAET,GAAG,CAACI,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAACV,GAAG,CAACW,GAAG,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC7DN,QAAQ,CAAC,8BAA8B;EAC5C,CAAC,CAAC;EAEK,IAAMO,kBAAkB,GAAG,SAAAA,CAAA,EAAM;IAAAC,EAAA;IACtC,IAAAC,SAAA,GAA0BC,QAAQ,CAAC,EAAE,CAAC;MAAAC,UAAA,GAAAC,cAAA,CAAAH,SAAA;MAA/BI,KAAK,GAAAF,UAAA;MAAEG,QAAQ,GAAAH,UAAA;IACtB,IAAAI,UAAA,GAA8BL,QAAQ,CAAC,KAAK,CAAC;MAAAM,UAAA,GAAAJ,cAAA,CAAAG,UAAA;MAAtCE,OAAO,GAAAD,UAAA;MAAEE,UAAU,GAAAF,UAAA;IAC1B,IAAAG,UAAA,GAAwCT,QAAQ,CAAC,KAAK,CAAC;MAAAU,UAAA,GAAAR,cAAA,CAAAO,UAAA;MAAhDE,YAAY,GAAAD,UAAA;MAAEE,eAAe,GAAAF,UAAA;IACpC,IAAAG,UAAA,GAAsDb,QAAQ,CAAC,KAAK,CAAC;MAAAc,UAAA,GAAAZ,cAAA,CAAAW,UAAA;MAA9DE,mBAAmB,GAAAD,UAAA;MAAEE,sBAAsB,GAAAF,UAAA;IAClD,IAAAG,UAAA,GAAoCjB,QAAQ,CAAC,IAAI,CAAC;MAAAkB,WAAA,GAAAhB,cAAA,CAAAe,UAAA;MAA3CE,UAAU,GAAAD,WAAA;MAAEE,aAAa,GAAAF,WAAA;IAChC,IAAMG,QAAQ,GAAGC,WAAW,CAAC,CAAC;IAE9B,IAAMC,YAAY,GAAG,SAAAA,CAACC,MAAM,EAAAC,IAAA,EAAwB;MAAA,IAApBC,aAAA,GAAAD,IAAA,CAAAC,aAAA;MAC9B,IAAQtC,KAAK,GAAkBoC,MAAM,CAA7BpC,KAAK;QAAEG,WAAA,GAAgBiC,MAAM,CAAtBjC,WAAA;MACfa,QAAQ,CAAC,EAAE,CAAC;MACZI,UAAU,CAAC,KAAK,CAAC;MAEjBmB,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAE;QAAExC,KAAK,EAALA,KAAK;QAAEG,WAAA,EAAAA;MAAY,CAAC,EAAE,UAACsC,GAAG,EAAEC,MAAM,EAAK;QAC3EJ,aAAa,CAAC,KAAK,CAAC;QACpB,IAAIG,GAAG,EAAE;UACPE,OAAO,CAAC5B,KAAK,CAAC,wBAAwB,EAAE0B,GAAG,CAAC;UAC5CzB,QAAQ,CAACyB,GAAG,CAACG,MAAM,IAAI,6CAA6C,CAAC;QACvE,CAAC,MAAM;UACLxB,UAAU,CAAC,IAAI,CAAC;UAChBuB,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAEH,MAAM,CAAC;UACjD;UACAI,UAAU,CAAC,YAAM;YACfb,QAAQ,CAAC,QAAQ,CAAC;UACpB,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC;IAED,IAAMc,SAAS,GAAG,SAAAA,CAAC/C,KAAK,EAAEgD,QAAQ,EAAK;MACrCT,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAE;QAAExC,KAAK,EAALA,KAAK;QAAEgD,QAAA,EAAAA;MAAS,CAAC,EAAE,UAACP,GAAG,EAAEC,MAAM,EAAK;QACnE,IAAID,GAAG,EAAE;UACPE,OAAO,CAAC5B,KAAK,CAAC,mBAAmB,EAAE0B,GAAG,CAAC;UACvCT,aAAa,CAAC;YAAEb,OAAO,EAAE,KAAK;YAAEJ,KAAK,EAAE0B,GAAG,CAACG;UAAO,CAAC,CAAC;QACtD,CAAC,MAAM;UACLD,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,MAAM,CAAC;UACzCV,aAAa,CAACU,MAAM,CAAC;QACvB;MACF,CAAC,CAAC;IACJ,CAAC;IAED,IAAMO,SAAS,GAAI,SAAAA,CAAAjD,KAAK,EAAK;MAC3BuC,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAE;QAAExC,KAAA,EAAAA;MAAM,CAAC,EAAE,UAACyC,GAAG,EAAEC,MAAM,EAAK;QACzD,IAAID,GAAG,EAAE;UACPE,OAAO,CAAC5B,KAAK,CAAC,mBAAmB,EAAE0B,GAAG,CAAC;UACvCT,aAAa,CAAC;YAAEb,OAAO,EAAE,KAAK;YAAEJ,KAAK,EAAE0B,GAAG,CAACG;UAAO,CAAC,CAAC;QACtD,CAAC,MAAM;UACLD,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,MAAM,CAAC;UACzCV,aAAa,CAACU,MAAM,CAAC;QACvB;MACF,CAAC,CAAC;IACJ,CAAC;IAED,oBACEQ,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAgB,gBAC7BF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAW,gBACxBF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAa,gBAC1BF,KAAA,CAAAC,aAAA;MAAKC,SAAS,EAAC;IAAW,gBACxBF,KAAA,CAAAC,aAAA;MAAKE,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC;IAA4B,gBAC5FP,KAAA,CAAAC,aAAA;MAAMO,CAAC,EAAC,oIAAoI;MAACC,MAAM,EAAC,cAAc;MAACC,WAAW,EAAC,GAAG;MAACC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO,EAC5N,CACF,CAAC,eACNZ,KAAA,CAAAC,aAAA;MAAIC,SAAS,EAAC;IAAY,GAAC,gBAAkB,CAAC,eAC9CF,KAAA,CAAAC,aAAA;MAAGC,SAAS,EAAC;IAAe,GAAC,mEAAoE,CAC9F,CAAC,eACNF,KAAA,CAAAC,aAAA,CAACY,MAAM;MACLC,aAAa,EAAE;QAAEhE,KAAK,EAAE,EAAE;QAAEG,WAAW,EAAE,EAAE;QAAEG,eAAe,EAAE;MAAG,CAAE;MACnE2D,gBAAgB,EAAErE,oBAAqB;MACvCsE,QAAQ,EAAE/B;IAAa,GAEtB,UAAAgC,KAAA;MAAA,IAAGC,MAAM,GAAAD,KAAA,CAANC,MAAM;QAAEC,OAAO,GAAAF,KAAA,CAAPE,OAAO;QAAEC,YAAA,GAAAH,KAAA,CAAAG,YAAA;MAAA,oBACnBpB,KAAA,CAAAC,aAAA,CAACoB,IAAI;QAACnB,SAAS,EAAC;MAAW,gBACzBF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,gBACzBF,KAAA,CAAAC,aAAA;QAAOqB,OAAO,EAAC,OAAO;QAACpB,SAAS,EAAC;MAAY,GAAC,eAAoB,CAAC,eACnEF,KAAA,CAAAC,aAAA,CAACsB,KAAK;QACJC,EAAE,EAAC,OAAO;QACVC,IAAI,EAAC,OAAO;QACZC,IAAI,EAAC,OAAO;QACZC,WAAW,EAAC,0BAA0B;QACtCzB,SAAS,qBAAkBgB,MAAM,CAACpE,KAAK,IAAIqE,OAAO,CAACrE,KAAK,GAAG,oBAAoB,GAAG,EAAE,CAAG;QACvF8E,QAAQ,EAAER;MAAa,EACvB,EACDF,MAAM,CAACpE,KAAK,IAAIqE,OAAO,CAACrE,KAAK,iBAC5BkD,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAe,GAAEgB,MAAM,CAACpE,KAAW,CAEjD,CAAC,eACNkD,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,gBACzBF,KAAA,CAAAC,aAAA;QAAOqB,OAAO,EAAC,aAAa;QAACpB,SAAS,EAAC;MAAY,GAAC,cAAmB,CAAC,eACxEF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAA+B,gBAC5CF,KAAA,CAAAC,aAAA,CAACsB,KAAK;QACJC,EAAE,EAAC,aAAa;QAChBC,IAAI,EAAC,aAAa;QAClBC,IAAI,EAAErD,YAAY,GAAG,MAAM,GAAG,UAAW;QACzCsD,WAAW,EAAC,oBAAoB;QAChCzB,SAAS,qBAAkBgB,MAAM,CAACjE,WAAW,IAAIkE,OAAO,CAAClE,WAAW,GAAG,oBAAoB,GAAG,EAAE,CAAG;QACnG2E,QAAQ,EAAER;MAAa,EACvB,eACFpB,KAAA,CAAAC,aAAA;QACEyB,IAAI,EAAC,QAAQ;QACbxB,SAAS,EAAC,qBAAqB;QAC/B2B,QAAQ,EAAE,CAAC,CAAE;QACb,cAAYxD,YAAY,GAAG,eAAe,GAAG,eAAgB;QAC7DyD,OAAO,EAAE,SAAAA,CAAA;UAAA,OAAMxD,eAAe,CAAE,UAAAyD,CAAC;YAAA,OAAK,CAACA,CAAC;UAAA,EAAC;QAAA;MAAC,GAEzC1D,YAAY,gBACX2B,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,wPAAwP;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAChV,CAAC,gBAENZ,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,8FAA8F;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAAE,eAC3LZ,KAAA,CAAAC,aAAA;QAAQ+B,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACzB,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC7G,CAED,CACL,CAAC,EACLM,MAAM,CAACjE,WAAW,IAAIkE,OAAO,CAAClE,WAAW,iBACxC+C,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAe,GAAEgB,MAAM,CAACjE,WAAiB,CAEvD,CAAC,eACN+C,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAY,gBACzBF,KAAA,CAAAC,aAAA;QAAOqB,OAAO,EAAC,iBAAiB;QAACpB,SAAS,EAAC;MAAY,GAAC,sBAA2B,CAAC,eACpFF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAA+B,gBAC5CF,KAAA,CAAAC,aAAA,CAACsB,KAAK;QACJC,EAAE,EAAC,iBAAiB;QACpBC,IAAI,EAAC,iBAAiB;QACtBC,IAAI,EAAEjD,mBAAmB,GAAG,MAAM,GAAG,UAAW;QAChDkD,WAAW,EAAC,sBAAsB;QAClCzB,SAAS,qBAAkBgB,MAAM,CAAC9D,eAAe,IAAI+D,OAAO,CAAC/D,eAAe,GAAG,oBAAoB,GAAG,EAAE,CAAG;QAC3GwE,QAAQ,EAAER;MAAa,EACvB,eACFpB,KAAA,CAAAC,aAAA;QACEyB,IAAI,EAAC,QAAQ;QACbxB,SAAS,EAAC,qBAAqB;QAC/B2B,QAAQ,EAAE,CAAC,CAAE;QACb,cAAYpD,mBAAmB,GAAG,eAAe,GAAG,eAAgB;QACpEqD,OAAO,EAAE,SAAAA,CAAA;UAAA,OAAMpD,sBAAsB,CAAE,UAAAqD,CAAC;YAAA,OAAK,CAACA,CAAC;UAAA,EAAC;QAAA;MAAC,GAEhDtD,mBAAmB,gBAClBuB,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,wPAAwP;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAChV,CAAC,gBAENZ,KAAA,CAAAC,aAAA;QAAKE,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC;MAA4B,gBAC5FP,KAAA,CAAAC,aAAA;QAAMO,CAAC,EAAC,8FAA8F;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAAE,eAC3LZ,KAAA,CAAAC,aAAA;QAAQ+B,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,GAAG;QAACzB,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC;MAAO,EAC7G,CAED,CACL,CAAC,EACLM,MAAM,CAAC9D,eAAe,IAAI+D,OAAO,CAAC/D,eAAe,iBAChD4C,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAe,GAAEgB,MAAM,CAAC9D,eAAqB,CAE3D,CAAC,EACLS,KAAK,iBACJmC,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAoC,GAAErC,KAAW,CACjE,EACAI,OAAO,iBACN+B,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAiB,GAAC,oDAAuD,CACzF,eACDF,KAAA,CAAAC,aAAA;QACEyB,IAAI,EAAC,QAAQ;QACbxB,SAAS,EAAC,0BAA0B;QACpC0B,QAAQ,EAAER;MAAa,GAEtBA,YAAY,gBACXpB,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAmC,QAAA,qBACEnC,KAAA,CAAAC,aAAA;QAAMC,SAAS,EAAC;MAAiB,CAAO,CAAC,gBAE3C,CAAG,GAEH,gBAEI,CAAC,eACTF,KAAA,CAAAC,aAAA;QAAKC,SAAS,EAAC;MAAgB,gBAC7BF,KAAA,CAAAC,aAAA,CAACmC,IAAI;QAACC,EAAE,EAAC,QAAQ;QAACnC,SAAS,EAAC;MAAW,GAAC,eAAmB,CACxD,CACD,CACP;IAAA,CACK,CAAC,eAGTF,KAAA,CAAAC,aAAA;MAAKqC,KAAK,EAAE;QAAEC,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAM;IAAE,gBAClG1C,KAAA,CAAAC,aAAA,aAAI,mBAAqB,CAAC,eAC1BD,KAAA,CAAAC,aAAA;MACE6B,OAAO,EAAE,SAAAA,CAAA,EAAM;QACb,IAAMhF,KAAK,GAAG6F,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC,CAACC,KAAK;QACjE,IAAM/C,QAAQ,GAAG6C,QAAQ,CAACC,aAAa,CAAC,2BAA2B,CAAC,CAACC,KAAK;QAC1E,IAAI/F,KAAK,IAAIgD,QAAQ,EAAE;UACrBD,SAAS,CAAC/C,KAAK,EAAEgD,QAAQ,CAAC;QAC5B;MACF,CAAE;MACFwC,KAAK,EAAE;QAAEE,OAAO,EAAE,UAAU;QAAEM,MAAM,EAAE;MAAM;IAAE,GAC/C,eAEO,CAAC,EACRjE,UAAU,iBACTmB,KAAA,CAAAC,aAAA;MAAKqC,KAAK,EAAE;QAAEC,SAAS,EAAE,MAAM;QAAEQ,QAAQ,EAAE;MAAO;IAAE,gBAClD/C,KAAA,CAAAC,aAAA,iBAAQ,cAAoB,CAAC,KAAC,eAAAD,KAAA,CAAAC,aAAA,cAAM+C,IAAI,CAACC,SAAS,CAACpE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAO,CAC1E,CAEJ,CACF,CACF,CAAC;EAEV,CAAC;EAACrB,EAAA,CA7MWD,kBAAkB;IAAA,QAMZyB,WAAW;EAAA;EAAAkE,EAAA,GANjB3F,kBAAkB;EAAA,IAAA2F,EAAA;EAAAC,YAAA,CAAAD,EAAA;AAAA,EAAA5D,IAAA,OAAA8D,MAAA", "ignoreList": []}, "sourceType": "module", "externalDependencies": {}, "hash": "5e1707a5d16cd237ba0f4537554615b8e4bf3369"}