{"version": 3, "sources": ["meteor://💻app/packages/akryum:postcss/postcss.js"], "names": ["module", "export", "name", "postcss", "selector<PERSON><PERSON><PERSON>", "autoprefixer", "addHash", "postcssLib", "link", "default", "v", "selectorParserLib", "autoprefixerLib", "plugin", "opts", "root", "each", "rewriteSelector", "node", "selector", "type", "selectors", "n", "insertAfter", "attribute", "hash", "process", "result"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,IAAI,EAACA,CAAA,KAAIA,IAAI;EAACC,OAAO,EAACA,CAAA,KAAIA,OAAO;EAACC,cAAc,EAACA,CAAA,KAAIA,cAAc;EAACC,YAAY,EAACA,CAAA,KAAIA,YAAY;EAACC,OAAO,EAACA,CAAA,KAAIA;AAAO,CAAC,CAAC;AAAC,IAAIC,UAAU;AAACP,MAAM,CAACQ,IAAI,CAAC,SAAS,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACH,UAAU,GAACG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,iBAAiB;AAACX,MAAM,CAACQ,IAAI,CAAC,yBAAyB,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACC,iBAAiB,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,eAAe;AAACZ,MAAM,CAACQ,IAAI,CAAC,cAAc,EAAC;EAACC,OAAOA,CAACC,CAAC,EAAC;IAACE,eAAe,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAIrX,MAAMR,IAAI,GAAG,SAAS;AAEtB,MAAMC,OAAO,GAAGI,UAAU;AAC1B,MAAMH,cAAc,GAAGO,iBAAiB;AACxC,MAAMN,YAAY,GAAGO,eAAe;AAEpC,MAAMN,OAAO,GAAGH,OAAO,CAACU,MAAM,CAAC,UAAU,EAAE,UAAUC,IAAI,EAAE;EAChE,OAAO,UAAUC,IAAI,EAAE;IACrBA,IAAI,CAACC,IAAI,CAAC,SAASC,eAAeA,CAAEC,IAAI,EAAE;MACxC,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;QAClB;QACA,IAAID,IAAI,CAACE,IAAI,KAAK,QAAQ,IAAIF,IAAI,CAAChB,IAAI,KAAK,OAAO,EAAE;UACnDgB,IAAI,CAACF,IAAI,CAACC,eAAe,CAAC;QAC5B;QACA;MACF;MACAC,IAAI,CAACC,QAAQ,GAAGf,cAAc,CAAC,UAAUiB,SAAS,EAAE;QAClDA,SAAS,CAACL,IAAI,CAAC,UAAUG,QAAQ,EAAE;UACjC,IAAID,IAAI,GAAG,IAAI;UACfC,QAAQ,CAACH,IAAI,CAAC,UAAUM,CAAC,EAAE;YACzB,IAAIA,CAAC,CAACF,IAAI,KAAK,QAAQ,EAAEF,IAAI,GAAGI,CAAC;UACnC,CAAC,CAAC;UACFH,QAAQ,CAACI,WAAW,CAACL,IAAI,EAAEd,cAAc,CAACoB,SAAS,CAAC;YAClDA,SAAS,EAAEV,IAAI,CAACW;UAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;MACJ,CAAC,CAAC,CAACC,OAAO,CAACR,IAAI,CAACC,QAAQ,CAAC,CAACQ,MAAM;IAClC,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC,C", "file": "/packages/akryum_postcss.js", "sourcesContent": ["import postcssLib from 'postcss'\nimport selectorParser<PERSON>ib from 'postcss-selector-parser'\nimport autoprefixer<PERSON>ib from 'autoprefixer'\n\nexport const name = 'postcss';\n\nexport const postcss = postcssLib;\nexport const selectorParser = selectorParserLib;\nexport const autoprefixer = autoprefixerLib;\n\nexport const addHash = postcss.plugin('add-hash', function (opts) {\n  return function (root) {\n    root.each(function rewriteSelector (node) {\n      if (!node.selector) {\n        // handle media queries\n        if (node.type === 'atrule' && node.name === 'media') {\n          node.each(rewriteSelector)\n        }\n        return\n      }\n      node.selector = selectorParser(function (selectors) {\n        selectors.each(function (selector) {\n          var node = null\n          selector.each(function (n) {\n            if (n.type !== 'pseudo') node = n\n          })\n          selector.insertAfter(node, selectorParser.attribute({\n            attribute: opts.hash\n          }))\n        })\n      }).process(node.selector).result\n    })\n  }\n})\n"]}