{"version": 3, "sources": ["meteor://💻app/packages/akryum:postcss/postcss.js"], "names": ["module", "export", "name", "postcss", "selector<PERSON><PERSON><PERSON>", "autoprefixer", "addHash", "postcssLib", "link", "default", "v", "selectorParserLib", "autoprefixerLib", "__reifyWaitForDeps__", "plugin", "opts", "root", "each", "rewriteSelector", "node", "selector", "type", "selectors", "n", "insertAfter", "attribute", "hash", "process", "result", "__reify_async_result__", "_reifyError", "self", "async"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,IAAI,EAACA,CAAA,KAAIA,IAAI;MAACC,OAAO,EAACA,CAAA,KAAIA,OAAO;MAACC,cAAc,EAACA,CAAA,KAAIA,cAAc;MAACC,YAAY,EAACA,CAAA,KAAIA,YAAY;MAACC,OAAO,EAACA,CAAA,KAAIA;IAAO,CAAC,CAAC;IAAC,IAAIC,UAAU;IAACP,MAAM,CAACQ,IAAI,CAAC,SAAS,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACH,UAAU,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,iBAAiB;IAACX,MAAM,CAACQ,IAAI,CAAC,yBAAyB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACC,iBAAiB,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,eAAe;IAACZ,MAAM,CAACQ,IAAI,CAAC,cAAc,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACE,eAAe,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAIlb,MAAMX,IAAI,GAAG,SAAS;IAEtB,MAAMC,OAAO,GAAGI,UAAU;IAC1B,MAAMH,cAAc,GAAGO,iBAAiB;IACxC,MAAMN,YAAY,GAAGO,eAAe;IAEpC,MAAMN,OAAO,GAAGH,OAAO,CAACW,MAAM,CAAC,UAAU,EAAE,UAAUC,IAAI,EAAE;MAChE,OAAO,UAAUC,IAAI,EAAE;QACrBA,IAAI,CAACC,IAAI,CAAC,SAASC,eAAeA,CAAEC,IAAI,EAAE;UACxC,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;YAClB;YACA,IAAID,IAAI,CAACE,IAAI,KAAK,QAAQ,IAAIF,IAAI,CAACjB,IAAI,KAAK,OAAO,EAAE;cACnDiB,IAAI,CAACF,IAAI,CAACC,eAAe,CAAC;YAC5B;YACA;UACF;UACAC,IAAI,CAACC,QAAQ,GAAGhB,cAAc,CAAC,UAAUkB,SAAS,EAAE;YAClDA,SAAS,CAACL,IAAI,CAAC,UAAUG,QAAQ,EAAE;cACjC,IAAID,IAAI,GAAG,IAAI;cACfC,QAAQ,CAACH,IAAI,CAAC,UAAUM,CAAC,EAAE;gBACzB,IAAIA,CAAC,CAACF,IAAI,KAAK,QAAQ,EAAEF,IAAI,GAAGI,CAAC;cACnC,CAAC,CAAC;cACFH,QAAQ,CAACI,WAAW,CAACL,IAAI,EAAEf,cAAc,CAACqB,SAAS,CAAC;gBAClDA,SAAS,EAAEV,IAAI,CAACW;cAClB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;UACJ,CAAC,CAAC,CAACC,OAAO,CAACR,IAAI,CAACC,QAAQ,CAAC,CAACQ,MAAM;QAClC,CAAC,CAAC;MACJ,CAAC;IACH,CAAC,CAAC;IAAAC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/akryum_postcss.js", "sourcesContent": ["import postcssLib from 'postcss'\nimport selectorParser<PERSON>ib from 'postcss-selector-parser'\nimport autoprefixer<PERSON>ib from 'autoprefixer'\n\nexport const name = 'postcss';\n\nexport const postcss = postcssLib;\nexport const selectorParser = selectorParserLib;\nexport const autoprefixer = autoprefixerLib;\n\nexport const addHash = postcss.plugin('add-hash', function (opts) {\n  return function (root) {\n    root.each(function rewriteSelector (node) {\n      if (!node.selector) {\n        // handle media queries\n        if (node.type === 'atrule' && node.name === 'media') {\n          node.each(rewriteSelector)\n        }\n        return\n      }\n      node.selector = selectorParser(function (selectors) {\n        selectors.each(function (selector) {\n          var node = null\n          selector.each(function (n) {\n            if (n.type !== 'pseudo') node = n\n          })\n          selector.insertAfter(node, selectorParser.attribute({\n            attribute: opts.hash\n          }))\n        })\n      }).process(node.selector).result\n    })\n  }\n})\n"]}