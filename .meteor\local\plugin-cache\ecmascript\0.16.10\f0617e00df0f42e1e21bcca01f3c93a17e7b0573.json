{"metadata": {}, "options": {"assumptions": {}, "compact": false, "sourceMaps": true, "ast": true, "babelrc": false, "configFile": false, "parserOpts": {"sourceType": "module", "sourceFileName": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\App.jsx", "plugins": ["*", "flow", "jsx", "asyncGenerators", "bigInt", "classPrivateMethods", "classPrivateProperties", "classProperties", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportExtensions", "exportNamespaceFrom", "functionBind", "functionSent", "importMeta", "nullishCoalescingOperator", "numericSeparator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", ["pipelineOperator", {"proposal": "minimal"}], "throwExpressions", "topLevelAwait", "classProperties", "classPrivateProperties", "jsx", "nullishCoalescingOperator", "nullishCoalescingOperator", "optionalChaining", "optionalChaining", "optionalCatchBinding", "optionalCatchBinding", "classProperties", "classPrivateProperties", "classPrivateMethods", "classProperties", "classPrivateProperties", "asyncGenerators", "asyncGenerators", "objectRestSpread", "objectRestSpread", "logicalAssignment"], "allowImportExportEverywhere": true, "allowReturnOutsideFunction": true, "allowUndeclaredExports": true, "strictMode": false}, "caller": {"name": "meteor", "arch": "web.browser"}, "sourceFileName": "imports/ui/App.jsx", "filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\App.jsx", "inputSourceMap": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "useLocation", "useTracker", "Meteor", "LoginPage", "SignupPage", "ForgotPasswordPage", "AdminDashboard", "TeamDashboard", "TeamTasksPage", "TeamMembersPage", "TaskDetailPage", "ProtectedRoute", "children", "allowedRoles", "_s", "location", "user", "userRole", "isLoading", "subscription", "subscribe", "profile", "role", "roles", "ready", "from", "includes", "_c", "App", "_s2", "_c2", "$RefreshReg$"], "sources": ["imports/ui/App.jsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { useTracker } from 'meteor/react-meteor-data';\nimport { Meteor } from 'meteor/meteor';\nimport { LoginPage } from './pages/LoginPage';\nimport { SignupPage } from './pages/SignupPage';\nimport { ForgotPasswordPage } from './pages/ForgotPasswordPage';\nimport { AdminDashboard } from './pages/AdminDashboard';\nimport { TeamDashboard } from './pages/TeamDashboard';\nimport { TeamTasksPage } from './pages/TeamTasksPage';\nimport { TeamMembersPage } from './pages/TeamMembersPage';\nimport { TaskDetailPage } from './pages/TaskDetailPage';\n\n// Protected Route component\nconst ProtectedRoute = ({ children, allowedRoles }) => {\n  const location = useLocation();\n  \n  const { user, userRole, isLoading } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    const user = Meteor.user();\n    const userRole = user?.profile?.role || user?.roles?.[0];\n    \n    return {\n      user,\n      userRole,\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  if (isLoading) {\n    return <div>Loading...</div>;\n  }\n\n  if (!user) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  if (allowedRoles && !allowedRoles.includes(userRole)) {\n    return <Navigate to={userRole === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace />;\n  }\n\n  return children;\n};\n\nexport const App = () => {\n  const { user, isLoading } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    return {\n      user: Meteor.user(),\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  if (isLoading) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div className=\"container\">\n      <Routes>\n        <Route \n          path=\"/login\" \n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <LoginPage />} \n        />\n        <Route\n          path=\"/signup\"\n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <SignupPage />}\n        />\n        <Route\n          path=\"/forgot-password\"\n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <ForgotPasswordPage />}\n        />\n        <Route\n          path=\"/admin-dashboard/*\"\n          element={\n            <ProtectedRoute allowedRoles={['admin']}>\n              <AdminDashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/team-dashboard/*\"\n          element={\n            <ProtectedRoute allowedRoles={['team-member']}>\n              <TeamDashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route path=\"/\" element={<Navigate to={user ? (user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard') : '/login'} replace />} />\n        <Route path=\"/tasks/:taskId\" element={<TaskDetailPage />} />\n      </Routes>\n    </div>\n  );\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACvE,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,cAAc,QAAQ,wBAAwB;;AAEvD;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEgB,IAAI;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGjB,UAAU,CAAC,MAAM;IACrD,MAAMkB,YAAY,GAAGjB,MAAM,CAACkB,SAAS,CAAC,UAAU,CAAC;IACjD,MAAMJ,IAAI,GAAGd,MAAM,CAACc,IAAI,CAAC,CAAC;IAC1B,MAAMC,QAAQ,GAAGD,IAAI,EAAEK,OAAO,EAAEC,IAAI,IAAIN,IAAI,EAAEO,KAAK,GAAG,CAAC,CAAC;IAExD,OAAO;MACLP,IAAI;MACJC,QAAQ;MACRC,SAAS,EAAE,CAACC,YAAY,CAACK,KAAK,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,SAAS,EAAE;IACb,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;EAC9B;EAEA,IAAI,CAACF,IAAI,EAAE;IACT,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;MAAES,IAAI,EAAEV;IAAS,CAAC,CAAC,CAAC,OAAO,GAAG;EACpE;EAEA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAACa,QAAQ,CAACT,QAAQ,CAAC,EAAE;IACpD,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,QAAQ,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG;EAChG;EAEA,OAAOL,QAAQ;AACjB,CAAC;AAACE,EAAA,CA5BIH,cAAc;EAAA,QACDX,WAAW,EAEUC,UAAU;AAAA;AAAA0B,EAAA,GAH5ChB,cAAc;AA8BpB,OAAO,MAAMiB,GAAG,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAEb,IAAI;IAAEE;EAAU,CAAC,GAAGjB,UAAU,CAAC,MAAM;IAC3C,MAAMkB,YAAY,GAAGjB,MAAM,CAACkB,SAAS,CAAC,UAAU,CAAC;IACjD,OAAO;MACLJ,IAAI,EAAEd,MAAM,CAACc,IAAI,CAAC,CAAC;MACnBE,SAAS,EAAE,CAACC,YAAY,CAACK,KAAK,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,SAAS,EAAE;IACb,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;EAC9B;EAEA,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAC9B,MAAM,CAAC,MAAM;AACb,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,QAAQ,CACb,OAAO,CAAC,CAACF,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS,GAAG,CAAC;AAE5I,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,SAAS,CACd,OAAO,CAAC,CAACN,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,UAAU,GAAG,CAAC;AAE7I,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,kBAAkB,CACvB,OAAO,CAAC,CAACN,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,kBAAkB,GAAG,CAAC;AAErJ,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,oBAAoB,CACzB,OAAO,CAAC,CACN,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACpD,cAAc,CAAC,cAAc;AAC7B,YAAY,EAAE,cAAc,CAClB,CAAC;AAEX,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,mBAAmB,CACxB,OAAO,CAAC,CACN,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AAC1D,cAAc,CAAC,aAAa;AAC5B,YAAY,EAAE,cAAc,CAClB,CAAC;AAEX,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACN,IAAI,GAAIA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,GAAI,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC;AACvJ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC;AACjE,MAAM,EAAE,MAAM;AACd,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACO,GAAA,CAjDWD,GAAG;EAAA,QACc3B,UAAU;AAAA;AAAA6B,GAAA,GAD3BF,GAAG;AAAA,IAAAD,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "targets": {}, "cloneInputAst": true, "browserslistConfigFile": false, "passPerPreset": false, "envName": "development", "cwd": "C:\\Users\\<USER>\\smart-task-management-system", "root": "C:\\Users\\<USER>\\smart-task-management-system", "rootMode": "root", "plugins": [{"key": "base$0", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$0", "visitor": {"Program": {"enter": [null], "exit": [null]}, "_exploded": true, "_verified": true}, "options": {"avoidModernSyntax": false, "enforceStrictMode": false, "dynamicImport": true, "generateLetDeclarations": true}, "externalDependencies": []}, {"key": "transform-runtime", "visitor": {"MemberExpression": {"enter": [null]}, "ObjectPattern": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "Identifier": {"enter": [null]}, "JSXIdentifier": {"enter": [null]}}, "options": {"version": "7.17.2", "helpers": true, "useESModules": false, "corejs": false}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-react-jsx", "visitor": {"JSXNamespacedName": {"enter": [null]}, "JSXSpreadChild": {"enter": [null]}, "Program": {"enter": [null]}, "JSXFragment": {"exit": [null]}, "JSXElement": {"exit": [null]}, "JSXAttribute": {"enter": [null]}}, "options": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "runtime": "classic", "throwIfNamespace": true, "useBuiltIns": false}, "externalDependencies": []}, {"key": "transform-react-display-name", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-pure-annotations", "visitor": {"CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "syntax-nullish-coalescing-operator", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-nullish-coalescing-operator", "visitor": {"LogicalExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-chaining", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-chaining", "visitor": {"OptionalCallExpression": {"enter": [null]}, "OptionalMemberExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-catch-binding", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-catch-binding", "visitor": {"CatchClause": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-class-properties", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-async-generators", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-logical-assignment-operators", "visitor": {"AssignmentExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-literals", "visitor": {"NumericLiteral": {"enter": [null]}, "StringLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-template-literals", "visitor": {"TaggedTemplateExpression": {"enter": [null]}, "TemplateLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-parameters", "visitor": {"_exploded": true, "_verified": true, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-exponentiation-operator", "visitor": {"AssignmentExpression": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}], "presets": [], "generatorOpts": {"filename": "C:\\Users\\<USER>\\smart-task-management-system\\imports\\ui\\App.jsx", "comments": true, "compact": false, "sourceMaps": true, "sourceFileName": "imports/ui/App.jsx", "inputSourceMap": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "useLocation", "useTracker", "Meteor", "LoginPage", "SignupPage", "ForgotPasswordPage", "AdminDashboard", "TeamDashboard", "TeamTasksPage", "TeamMembersPage", "TaskDetailPage", "ProtectedRoute", "children", "allowedRoles", "_s", "location", "user", "userRole", "isLoading", "subscription", "subscribe", "profile", "role", "roles", "ready", "from", "includes", "_c", "App", "_s2", "_c2", "$RefreshReg$"], "sources": ["imports/ui/App.jsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { useTracker } from 'meteor/react-meteor-data';\nimport { Meteor } from 'meteor/meteor';\nimport { LoginPage } from './pages/LoginPage';\nimport { SignupPage } from './pages/SignupPage';\nimport { ForgotPasswordPage } from './pages/ForgotPasswordPage';\nimport { AdminDashboard } from './pages/AdminDashboard';\nimport { TeamDashboard } from './pages/TeamDashboard';\nimport { TeamTasksPage } from './pages/TeamTasksPage';\nimport { TeamMembersPage } from './pages/TeamMembersPage';\nimport { TaskDetailPage } from './pages/TaskDetailPage';\n\n// Protected Route component\nconst ProtectedRoute = ({ children, allowedRoles }) => {\n  const location = useLocation();\n  \n  const { user, userRole, isLoading } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    const user = Meteor.user();\n    const userRole = user?.profile?.role || user?.roles?.[0];\n    \n    return {\n      user,\n      userRole,\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  if (isLoading) {\n    return <div>Loading...</div>;\n  }\n\n  if (!user) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  if (allowedRoles && !allowedRoles.includes(userRole)) {\n    return <Navigate to={userRole === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace />;\n  }\n\n  return children;\n};\n\nexport const App = () => {\n  const { user, isLoading } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    return {\n      user: Meteor.user(),\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  if (isLoading) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div className=\"container\">\n      <Routes>\n        <Route \n          path=\"/login\" \n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <LoginPage />} \n        />\n        <Route\n          path=\"/signup\"\n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <SignupPage />}\n        />\n        <Route\n          path=\"/forgot-password\"\n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <ForgotPasswordPage />}\n        />\n        <Route\n          path=\"/admin-dashboard/*\"\n          element={\n            <ProtectedRoute allowedRoles={['admin']}>\n              <AdminDashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/team-dashboard/*\"\n          element={\n            <ProtectedRoute allowedRoles={['team-member']}>\n              <TeamDashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route path=\"/\" element={<Navigate to={user ? (user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard') : '/login'} replace />} />\n        <Route path=\"/tasks/:taskId\" element={<TaskDetailPage />} />\n      </Routes>\n    </div>\n  );\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACvE,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,cAAc,QAAQ,wBAAwB;;AAEvD;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEgB,IAAI;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGjB,UAAU,CAAC,MAAM;IACrD,MAAMkB,YAAY,GAAGjB,MAAM,CAACkB,SAAS,CAAC,UAAU,CAAC;IACjD,MAAMJ,IAAI,GAAGd,MAAM,CAACc,IAAI,CAAC,CAAC;IAC1B,MAAMC,QAAQ,GAAGD,IAAI,EAAEK,OAAO,EAAEC,IAAI,IAAIN,IAAI,EAAEO,KAAK,GAAG,CAAC,CAAC;IAExD,OAAO;MACLP,IAAI;MACJC,QAAQ;MACRC,SAAS,EAAE,CAACC,YAAY,CAACK,KAAK,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,SAAS,EAAE;IACb,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;EAC9B;EAEA,IAAI,CAACF,IAAI,EAAE;IACT,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;MAAES,IAAI,EAAEV;IAAS,CAAC,CAAC,CAAC,OAAO,GAAG;EACpE;EAEA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAACa,QAAQ,CAACT,QAAQ,CAAC,EAAE;IACpD,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,QAAQ,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG;EAChG;EAEA,OAAOL,QAAQ;AACjB,CAAC;AAACE,EAAA,CA5BIH,cAAc;EAAA,QACDX,WAAW,EAEUC,UAAU;AAAA;AAAA0B,EAAA,GAH5ChB,cAAc;AA8BpB,OAAO,MAAMiB,GAAG,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAEb,IAAI;IAAEE;EAAU,CAAC,GAAGjB,UAAU,CAAC,MAAM;IAC3C,MAAMkB,YAAY,GAAGjB,MAAM,CAACkB,SAAS,CAAC,UAAU,CAAC;IACjD,OAAO;MACLJ,IAAI,EAAEd,MAAM,CAACc,IAAI,CAAC,CAAC;MACnBE,SAAS,EAAE,CAACC,YAAY,CAACK,KAAK,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,SAAS,EAAE;IACb,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;EAC9B;EAEA,OACE,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW;AAC9B,MAAM,CAAC,MAAM;AACb,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,QAAQ,CACb,OAAO,CAAC,CAACF,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS,GAAG,CAAC;AAE5I,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,SAAS,CACd,OAAO,CAAC,CAACN,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,UAAU,GAAG,CAAC;AAE7I,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,kBAAkB,CACvB,OAAO,CAAC,CAACN,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,kBAAkB,GAAG,CAAC;AAErJ,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,oBAAoB,CACzB,OAAO,CAAC,CACN,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACpD,cAAc,CAAC,cAAc;AAC7B,YAAY,EAAE,cAAc,CAClB,CAAC;AAEX,QAAQ,CAAC,KAAK,CACJ,IAAI,CAAC,mBAAmB,CACxB,OAAO,CAAC,CACN,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AAC1D,cAAc,CAAC,aAAa;AAC5B,YAAY,EAAE,cAAc,CAClB,CAAC;AAEX,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAACN,IAAI,GAAIA,IAAI,CAACK,OAAO,EAAEC,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,GAAI,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC;AACvJ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC;AACjE,MAAM,EAAE,MAAM;AACd,IAAI,EAAE,GAAG,CAAC;AAEV,CAAC;AAACO,GAAA,CAjDWD,GAAG;EAAA,QACc3B,UAAU;AAAA;AAAA6B,GAAA,GAD3BF,GAAG;AAAA,IAAAD,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}}}, "code": "!function (module1) {\n  module1.export({\n    App: () => App\n  });\n  let React;\n  module1.link(\"react\", {\n    default(v) {\n      React = v;\n    }\n  }, 0);\n  let Routes, Route, Navigate, useLocation;\n  module1.link(\"react-router-dom\", {\n    Routes(v) {\n      Routes = v;\n    },\n    Route(v) {\n      Route = v;\n    },\n    Navigate(v) {\n      Navigate = v;\n    },\n    useLocation(v) {\n      useLocation = v;\n    }\n  }, 1);\n  let useTracker;\n  module1.link(\"meteor/react-meteor-data\", {\n    useTracker(v) {\n      useTracker = v;\n    }\n  }, 2);\n  let Meteor;\n  module1.link(\"meteor/meteor\", {\n    Meteor(v) {\n      Meteor = v;\n    }\n  }, 3);\n  let LoginPage;\n  module1.link(\"./pages/LoginPage\", {\n    LoginPage(v) {\n      LoginPage = v;\n    }\n  }, 4);\n  let SignupPage;\n  module1.link(\"./pages/SignupPage\", {\n    SignupPage(v) {\n      SignupPage = v;\n    }\n  }, 5);\n  let ForgotPasswordPage;\n  module1.link(\"./pages/ForgotPasswordPage\", {\n    ForgotPasswordPage(v) {\n      ForgotPasswordPage = v;\n    }\n  }, 6);\n  let AdminDashboard;\n  module1.link(\"./pages/AdminDashboard\", {\n    AdminDashboard(v) {\n      AdminDashboard = v;\n    }\n  }, 7);\n  let TeamDashboard;\n  module1.link(\"./pages/TeamDashboard\", {\n    TeamDashboard(v) {\n      TeamDashboard = v;\n    }\n  }, 8);\n  let TeamTasksPage;\n  module1.link(\"./pages/TeamTasksPage\", {\n    TeamTasksPage(v) {\n      TeamTasksPage = v;\n    }\n  }, 9);\n  let TeamMembersPage;\n  module1.link(\"./pages/TeamMembersPage\", {\n    TeamMembersPage(v) {\n      TeamMembersPage = v;\n    }\n  }, 10);\n  let TaskDetailPage;\n  module1.link(\"./pages/TaskDetailPage\", {\n    TaskDetailPage(v) {\n      TaskDetailPage = v;\n    }\n  }, 11);\n  ___INIT_METEOR_FAST_REFRESH(module);\n  var _s = $RefreshSig$(),\n    _s2 = $RefreshSig$();\n  // Protected Route component\n  const ProtectedRoute = _ref => {\n    let {\n      children,\n      allowedRoles\n    } = _ref;\n    _s();\n    const location = useLocation();\n    const {\n      user,\n      userRole,\n      isLoading\n    } = useTracker(() => {\n      var _user$profile, _user$roles;\n      const subscription = Meteor.subscribe('userData');\n      const user = Meteor.user();\n      const userRole = (user === null || user === void 0 ? void 0 : (_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]);\n      return {\n        user,\n        userRole,\n        isLoading: !subscription.ready()\n      };\n    }, []);\n    if (isLoading) {\n      return /*#__PURE__*/React.createElement(\"div\", null, \"Loading...\");\n    }\n    if (!user) {\n      return /*#__PURE__*/React.createElement(Navigate, {\n        to: \"/login\",\n        state: {\n          from: location\n        },\n        replace: true\n      });\n    }\n    if (allowedRoles && !allowedRoles.includes(userRole)) {\n      return /*#__PURE__*/React.createElement(Navigate, {\n        to: userRole === 'admin' ? '/admin-dashboard' : '/team-dashboard',\n        replace: true\n      });\n    }\n    return children;\n  };\n  _s(ProtectedRoute, \"mLGqI+cBY0PttkscNXiFj6AZFEU=\", false, function () {\n    return [useLocation, useTracker];\n  });\n  _c = ProtectedRoute;\n  const App = () => {\n    var _user$profile2, _user$profile3, _user$profile4, _user$profile5;\n    _s2();\n    const {\n      user,\n      isLoading\n    } = useTracker(() => {\n      const subscription = Meteor.subscribe('userData');\n      return {\n        user: Meteor.user(),\n        isLoading: !subscription.ready()\n      };\n    }, []);\n    if (isLoading) {\n      return /*#__PURE__*/React.createElement(\"div\", null, \"Loading...\");\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"container\"\n    }, /*#__PURE__*/React.createElement(Routes, null, /*#__PURE__*/React.createElement(Route, {\n      path: \"/login\",\n      element: user ? /*#__PURE__*/React.createElement(Navigate, {\n        to: ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard',\n        replace: true\n      }) : /*#__PURE__*/React.createElement(LoginPage, null)\n    }), /*#__PURE__*/React.createElement(Route, {\n      path: \"/signup\",\n      element: user ? /*#__PURE__*/React.createElement(Navigate, {\n        to: ((_user$profile3 = user.profile) === null || _user$profile3 === void 0 ? void 0 : _user$profile3.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard',\n        replace: true\n      }) : /*#__PURE__*/React.createElement(SignupPage, null)\n    }), /*#__PURE__*/React.createElement(Route, {\n      path: \"/forgot-password\",\n      element: user ? /*#__PURE__*/React.createElement(Navigate, {\n        to: ((_user$profile4 = user.profile) === null || _user$profile4 === void 0 ? void 0 : _user$profile4.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard',\n        replace: true\n      }) : /*#__PURE__*/React.createElement(ForgotPasswordPage, null)\n    }), /*#__PURE__*/React.createElement(Route, {\n      path: \"/admin-dashboard/*\",\n      element: /*#__PURE__*/React.createElement(ProtectedRoute, {\n        allowedRoles: ['admin']\n      }, /*#__PURE__*/React.createElement(AdminDashboard, null))\n    }), /*#__PURE__*/React.createElement(Route, {\n      path: \"/team-dashboard/*\",\n      element: /*#__PURE__*/React.createElement(ProtectedRoute, {\n        allowedRoles: ['team-member']\n      }, /*#__PURE__*/React.createElement(TeamDashboard, null))\n    }), /*#__PURE__*/React.createElement(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/React.createElement(Navigate, {\n        to: user ? ((_user$profile5 = user.profile) === null || _user$profile5 === void 0 ? void 0 : _user$profile5.role) === 'admin' ? '/admin-dashboard' : '/team-dashboard' : '/login',\n        replace: true\n      })\n    }), /*#__PURE__*/React.createElement(Route, {\n      path: \"/tasks/:taskId\",\n      element: /*#__PURE__*/React.createElement(TaskDetailPage, null)\n    })));\n  };\n  _s2(App, \"FQsDSDpz35b7IMQ9/L3SUoeMc8w=\", false, function () {\n    return [useTracker];\n  });\n  _c2 = App;\n  var _c, _c2;\n  $RefreshReg$(_c, \"ProtectedRoute\");\n  $RefreshReg$(_c2, \"App\");\n}.call(this, module);", "map": {"version": 3, "names": ["ProtectedRoute", "_ref", "children", "allowedRoles", "_s", "location", "useLocation", "user", "userRole", "isLoading", "useTracker", "_user$profile", "_user$roles", "subscription", "Meteor", "subscribe", "profile", "role", "roles", "ready", "React", "createElement", "Navigate", "to", "state", "from", "replace", "includes", "_c", "App", "_user$profile2", "_user$profile3", "_user$profile4", "_user$profile5", "_s2", "className", "Routes", "Route", "path", "element", "LoginPage", "SignupPage", "ForgotPasswordPage", "AdminDashboard", "TeamDashboard", "TaskDetailPage", "_c2", "$RefreshReg$", "call", "module"], "sources": ["imports/ui/App.jsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { useTracker } from 'meteor/react-meteor-data';\nimport { Meteor } from 'meteor/meteor';\nimport { LoginPage } from './pages/LoginPage';\nimport { SignupPage } from './pages/SignupPage';\nimport { ForgotPasswordPage } from './pages/ForgotPasswordPage';\nimport { AdminDashboard } from './pages/AdminDashboard';\nimport { TeamDashboard } from './pages/TeamDashboard';\nimport { TeamTasksPage } from './pages/TeamTasksPage';\nimport { TeamMembersPage } from './pages/TeamMembersPage';\nimport { TaskDetailPage } from './pages/TaskDetailPage';\n\n// Protected Route component\nconst ProtectedRoute = ({ children, allowedRoles }) => {\n  const location = useLocation();\n  \n  const { user, userRole, isLoading } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    const user = Meteor.user();\n    const userRole = user?.profile?.role || user?.roles?.[0];\n    \n    return {\n      user,\n      userRole,\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  if (isLoading) {\n    return <div>Loading...</div>;\n  }\n\n  if (!user) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  if (allowedRoles && !allowedRoles.includes(userRole)) {\n    return <Navigate to={userRole === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace />;\n  }\n\n  return children;\n};\n\nexport const App = () => {\n  const { user, isLoading } = useTracker(() => {\n    const subscription = Meteor.subscribe('userData');\n    return {\n      user: Meteor.user(),\n      isLoading: !subscription.ready()\n    };\n  }, []);\n\n  if (isLoading) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div className=\"container\">\n      <Routes>\n        <Route \n          path=\"/login\" \n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <LoginPage />} \n        />\n        <Route\n          path=\"/signup\"\n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <SignupPage />}\n        />\n        <Route\n          path=\"/forgot-password\"\n          element={user ? <Navigate to={user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard'} replace /> : <ForgotPasswordPage />}\n        />\n        <Route\n          path=\"/admin-dashboard/*\"\n          element={\n            <ProtectedRoute allowedRoles={['admin']}>\n              <AdminDashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/team-dashboard/*\"\n          element={\n            <ProtectedRoute allowedRoles={['team-member']}>\n              <TeamDashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route path=\"/\" element={<Navigate to={user ? (user.profile?.role === 'admin' ? '/admin-dashboard' : '/team-dashboard') : '/login'} replace />} />\n        <Route path=\"/tasks/:taskId\" element={<TaskDetailPage />} />\n      </Routes>\n    </div>\n  );\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAaA;EACA,MAAMA,cAAc,GAAGC,IAAA,IAAgC;IAAA,IAA/B;MAAEC,QAAQ;MAAEC;IAAa,CAAC,GAAAF,IAAA;IAAKG,EAAA;IACrD,MAAMC,QAAQ,GAAGC,WAAW,CAAC,CAAC;IAE9B,MAAM;MAAEC,IAAI;MAAEC,QAAQ;MAAEC;IAAU,CAAC,GAAGC,UAAU,CAAC,MAAM;MAAA,IAAAC,aAAA,EAAAC,WAAA;MACrD,MAAMC,YAAY,GAAGC,MAAM,CAACC,SAAS,CAAC,UAAU,CAAC;MACjD,MAAMR,IAAI,GAAGO,MAAM,CAACP,IAAI,CAAC,CAAC;MAC1B,MAAMC,QAAQ,GAAG,CAAAD,IAAI,aAAJA,IAAI,wBAAAI,aAAA,GAAJJ,IAAI,CAAES,OAAO,cAAAL,aAAA,uBAAbA,aAAA,CAAeM,IAAI,MAAIV,IAAI,aAAJA,IAAI,wBAAAK,WAAA,GAAJL,IAAI,CAAEW,KAAK,cAAAN,WAAA,uBAAXA,WAAA,CAAc,CAAC,CAAC;MAExD,OAAO;QACLL,IAAI;QACJC,QAAQ;QACRC,SAAS,EAAE,CAACI,YAAY,CAACM,KAAK,CAAC;MACjC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IAEN,IAAIV,SAAS,EAAE;MACb,oBAAOW,KAAA,CAAAC,aAAA,cAAK,YAAe,CAAC;IAC9B;IAEA,IAAI,CAACd,IAAI,EAAE;MACT,oBAAOa,KAAA,CAAAC,aAAA,CAACC,QAAQ;QAACC,EAAE,EAAC,QAAQ;QAACC,KAAK,EAAE;UAAEC,IAAI,EAAEpB;QAAS,CAAE;QAACqB,OAAO;MAAA,EAAG;IACpE;IAEA,IAAIvB,YAAY,IAAI,CAACA,YAAY,CAACwB,QAAQ,CAACnB,QAAQ,CAAC,EAAE;MACpD,oBAAOY,KAAA,CAAAC,aAAA,CAACC,QAAQ;QAACC,EAAE,EAAEf,QAAQ,KAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAkB;QAACkB,OAAO;MAAA,EAAG;IAChG;IAEA,OAAOxB,QAAQ;EACjB,CAAC;EAACE,EAAA,CA5BIJ,cAAc;IAAA,QACDM,WAAW,EAEUI,UAAU;EAAA;EAAAkB,EAAA,GAH5C5B,cAAc;EA8Bb,MAAM6B,GAAG,GAAGA,CAAA,KAAM;IAAA,IAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;IAAAC,GAAA;IACvB,MAAM;MAAE3B,IAAI;MAAEE;IAAU,CAAC,GAAGC,UAAU,CAAC,MAAM;MAC3C,MAAMG,YAAY,GAAGC,MAAM,CAACC,SAAS,CAAC,UAAU,CAAC;MACjD,OAAO;QACLR,IAAI,EAAEO,MAAM,CAACP,IAAI,CAAC,CAAC;QACnBE,SAAS,EAAE,CAACI,YAAY,CAACM,KAAK,CAAC;MACjC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IAEN,IAAIV,SAAS,EAAE;MACb,oBAAOW,KAAA,CAAAC,aAAA,cAAK,YAAe,CAAC;IAC9B;IAEA,oBACED,KAAA,CAAAC,aAAA;MAAKc,SAAS,EAAC;IAAW,gBACxBf,KAAA,CAAAC,aAAA,CAACe,MAAM,qBACLhB,KAAA,CAAAC,aAAA,CAACgB,KAAK;MACJC,IAAI,EAAC,QAAQ;MACbC,OAAO,EAAEhC,IAAI,gBAAGa,KAAA,CAAAC,aAAA,CAACC,QAAQ;QAACC,EAAE,EAAE,EAAAO,cAAA,GAAAvB,IAAI,CAACS,OAAO,cAAAc,cAAA,uBAAZA,cAAA,CAAcb,IAAI,MAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAkB;QAACS,OAAO;MAAA,EAAG,gBAAGN,KAAA,CAAAC,aAAA,CAACmB,SAAS;IAAI,iBAEpIpB,KAAA,CAAAC,aAAA,CAACgB,KAAK;MACJC,IAAI,EAAC,SAAS;MACdC,OAAO,EAAEhC,IAAI,gBAAGa,KAAA,CAAAC,aAAA,CAACC,QAAQ;QAACC,EAAE,EAAE,EAAAQ,cAAA,GAAAxB,IAAI,CAACS,OAAO,cAAAe,cAAA,uBAAZA,cAAA,CAAcd,IAAI,MAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAkB;QAACS,OAAO;MAAA,EAAG,gBAAGN,KAAA,CAAAC,aAAA,CAACoB,UAAU;IAAI,iBAErIrB,KAAA,CAAAC,aAAA,CAACgB,KAAK;MACJC,IAAI,EAAC,kBAAkB;MACvBC,OAAO,EAAEhC,IAAI,gBAAGa,KAAA,CAAAC,aAAA,CAACC,QAAQ;QAACC,EAAE,EAAE,EAAAS,cAAA,GAAAzB,IAAI,CAACS,OAAO,cAAAgB,cAAA,uBAAZA,cAAA,CAAcf,IAAI,MAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAkB;QAACS,OAAO;MAAA,EAAG,gBAAGN,KAAA,CAAAC,aAAA,CAACqB,kBAAkB;IAAI,iBAE7ItB,KAAA,CAAAC,aAAA,CAACgB,KAAK;MACJC,IAAI,EAAC,oBAAoB;MACzBC,OAAO,eACLnB,KAAA,CAAAC,aAAA,CAACrB,cAAc;QAACG,YAAY,EAAE,CAAC,OAAO;MAAE,gBACtCiB,KAAA,CAAAC,aAAA,CAACsB,cAAc,OACD;IACjB,iBAEHvB,KAAA,CAAAC,aAAA,CAACgB,KAAK;MACJC,IAAI,EAAC,mBAAmB;MACxBC,OAAO,eACLnB,KAAA,CAAAC,aAAA,CAACrB,cAAc;QAACG,YAAY,EAAE,CAAC,aAAa;MAAE,gBAC5CiB,KAAA,CAAAC,aAAA,CAACuB,aAAa,OACA;IACjB,iBAEHxB,KAAA,CAAAC,aAAA,CAACgB,KAAK;MAACC,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEnB,KAAA,CAAAC,aAAA,CAACC,QAAQ;QAACC,EAAE,EAAEhB,IAAI,GAAI,EAAA0B,cAAA,GAAA1B,IAAI,CAACS,OAAO,cAAAiB,cAAA,uBAAZA,cAAA,CAAchB,IAAI,MAAK,OAAO,GAAG,kBAAkB,GAAG,iBAAiB,GAAI,QAAS;QAACS,OAAO;MAAA;IAAI,iBAC/IN,KAAA,CAAAC,aAAA,CAACgB,KAAK;MAACC,IAAI,EAAC,gBAAgB;MAACC,OAAO,eAAEnB,KAAA,CAAAC,aAAA,CAACwB,cAAc;IAAI,EACnD,CACL,CAAC;EAEV,CAAC;EAACX,GAAA,CAjDWL,GAAG;IAAA,QACcnB,UAAU;EAAA;EAAAoC,GAAA,GAD3BjB,GAAG;EAAA,IAAAD,EAAA,EAAAkB,GAAA;EAAAC,YAAA,CAAAnB,EAAA;EAAAmB,YAAA,CAAAD,GAAA;AAAA,EAAAE,IAAA,OAAAC,MAAA", "ignoreList": []}, "sourceType": "module", "externalDependencies": {}, "hash": "f0617e00df0f42e1e21bcca01f3c93a17e7b0573"}