{"version": 3, "names": ["module", "export", "isWindowsLikeFilesystem", "toPosixPath", "convertToPosixPath", "to<PERSON>os<PERSON><PERSON>", "convertToWindowsPath", "convertToOSPath", "convertToStandardPath", "convertToOSLineEndings", "convertToStandardLineEndings", "unicodeNormalizePath", "wrapPathFunction", "pathJoin", "pathNormalize", "pathRelative", "pathResolve", "pathDirname", "pathBasename", "pathExtname", "pathIsAbsolute", "pathSep", "pathDelimiter", "pathOsDelimiter", "path", "link", "default", "v", "release", "EOL", "process", "platform", "toLowerCase", "includes", "p", "partialPath", "arguments", "length", "undefined", "env", "SystemDrive", "replace", "slice", "test", "Error", "standardPath", "osPath", "fileContents", "RegExp", "normalize", "f", "wrapper", "result", "apply", "Array", "prototype", "map", "call", "join", "relative", "resolve", "dirname", "basename", "extname", "isAbsolute", "delimiter"], "sources": ["tools/static-assets/server/mini-files.ts"], "sourcesContent": ["import path from \"path\";\nimport { release, EOL } from \"os\";\n\n// All of these functions are attached to files.js for the tool;\n// they live here because we need them in boot.js as well to avoid duplicating\n// a lot of the code.\n//\n// Note that this file does NOT contain any of the \"perform I/O maybe\n// synchronously\" functions from files.js; this is intentional, because we want\n// to make it very hard to accidentally use fs.*Sync functions in the app server\n// after bootup (since they block all concurrency!)\n\n// Detect that we are on a Windows-like Filesystem, such as that in a WSL\n// (Windows Subsystem for Linux) even if it otherwise looks like we're on Unix.\n// https://github.com/Microsoft/BashOnWindows/issues/423#issuecomment-221627364\nexport function isWindowsLikeFilesystem() {\n  return process.platform === \"win32\" || release().toLowerCase().includes(\"microsoft\");\n}\n\nexport function toPosixPath(p: string, partialPath: boolean = false) {\n  // Sometimes, you can have a path like \\Users\\IEUser on windows, and this\n  // actually means you want C:\\Users\\<USER>\\\\\" && (! partialPath)) {\n    p = process.env.SystemDrive + p;\n  }\n\n  p = p.replace(/\\\\/g, '/');\n  if (p[1] === ':' && ! partialPath) {\n    // transform \"C:/bla/bla\" to \"/c/bla/bla\"\n    p = '/' + p[0] + p.slice(2);\n  }\n\n  return p;\n}\n\nexport const convertToPosixPath = toPosixPath;\n\nexport function toDosPath(p: string, partialPath: boolean = false) {\n  if (p[0] === '/' && ! partialPath) {\n    if (! /^\\/[A-Za-z](\\/|$)/.test(p))\n      throw new Error(\"Surprising path: \" + p);\n    // transform a previously windows path back\n    // \"/C/something\" to \"c:/something\"\n    p = p[1] + \":\" + p.slice(2);\n  }\n\n  p = p.replace(/\\//g, '\\\\');\n  return p;\n}\n\nexport const convertToWindowsPath = toDosPath;\n\nexport function convertToOSPath(standardPath: string, partialPath: boolean = false) {\n  if (process.platform === \"win32\") {\n    return toDosPath(standardPath, partialPath);\n  }\n  return standardPath;\n}\n\nexport function convertToStandardPath(osPath: string, partialPath: boolean = false) {\n  if (process.platform === \"win32\") {\n    return toPosixPath(osPath, partialPath);\n  }\n  return osPath;\n}\n\nexport function convertToOSLineEndings(fileContents: string) {\n  return fileContents.replace(/\\n/g, EOL);\n}\n\nexport function convertToStandardLineEndings(fileContents: string) {\n  // Convert all kinds of end-of-line chars to linuxy \"\\n\".\n  return fileContents.replace(new RegExp(\"\\r\\n\", \"g\"), \"\\n\")\n                     .replace(new RegExp(\"\\r\", \"g\"), \"\\n\");\n}\n\n\n// Return the Unicode Normalization Form of the passed in path string, using\n// \"Normalization Form Canonical Composition\"\nexport function unicodeNormalizePath(path: string) {\n  return (path) ? path.normalize('NFC') : path;\n}\n\n// wrappings for path functions that always run as they were on unix (using\n// forward slashes)\nexport function wrapPathFunction<\n  TArgs extends any[],\n  TResult,\n  F extends (...args: TArgs) => TResult,\n>(\n  f: F,\n  partialPath: boolean = false,\n): F {\n  return function wrapper() {\n    if (process.platform === 'win32') {\n      const result = f.apply(path, Array.prototype.map.call(\n        arguments,\n        // if partialPaths is turned on (for path.join mostly)\n        // forget about conversion of absolute paths for Windows\n        p => toDosPath(p, partialPath),\n      ) as TArgs);\n\n      return typeof result === \"string\"\n        ? toPosixPath(result, partialPath)\n        : result;\n    }\n    return f.apply(path, arguments as any);\n  } as F;\n}\n\nexport const pathJoin = wrapPathFunction(path.join, true);\nexport const pathNormalize = wrapPathFunction(path.normalize);\nexport const pathRelative = wrapPathFunction(path.relative);\nexport const pathResolve = wrapPathFunction(path.resolve);\nexport const pathDirname = wrapPathFunction(path.dirname);\nexport const pathBasename = wrapPathFunction(path.basename);\nexport const pathExtname = wrapPathFunction(path.extname);\nexport const pathIsAbsolute = wrapPathFunction(path.isAbsolute);\nexport const pathSep = '/';\nexport const pathDelimiter = ':';\nexport const pathOsDelimiter = path.delimiter;\n"], "mappings": "AAAAA,MAAA,CAAOC,MAAI;EAAAC,uBAAa,EAAAA,CAAA,KAAAA,uBAAA;EAAAC,WAAA,EAAAA,CAAA,KAAAA,WAAA;EAAAC,kBAAA,EAAAA,CAAA,KAAAA,kBAAA;EAAAC,SAAA,EAAAA,CAAA,KAAAA,SAAA;EAAAC,oBAAA,EAAAA,CAAA,KAAAA,oBAAA;EAAAC,eAAA,EAAAA,CAAA,KAAAA,eAAA;EAAAC,qBAAA,EAAAA,CAAA,KAAAA,qBAAA;EAAAC,sBAAA,EAAAA,CAAA,KAAAA,sBAAA;EAAAC,4BAAA,EAAAA,CAAA,KAAAA,4BAAA;EAAAC,oBAAA,EAAAA,CAAA,KAAAA,oBAAA;EAAAC,gBAAA,EAAAA,CAAA,KAAAA,gBAAA;EAAAC,QAAA,EAAAA,CAAA,KAAAA,QAAA;EAAAC,aAAA,EAAAA,CAAA,KAAAA,aAAA;EAAAC,YAAA,EAAAA,CAAA,KAAAA,YAAA;EAAAC,WAAA,EAAAA,CAAA,KAAAA,WAAA;EAAAC,WAAA,EAAAA,CAAA,KAAAA,WAAA;EAAAC,YAAA,EAAAA,CAAA,KAAAA,YAAA;EAAAC,WAAA,EAAAA,CAAA,KAAAA,WAAA;EAAAC,cAAA,EAAAA,CAAA,KAAAA,cAAA;EAAAC,OAAA,EAAAA,CAAA,KAAAA,OAAA;EAAAC,aAAA,EAAAA,CAAA,KAAAA,aAAA;EAAAC,eAAA,EAAAA,CAAA,KAAAA;AAAA;AAAA,IAAAC,IAAA;AAAAxB,MAAA,CAAAyB,IAAA;EAAAC,QAAAC,CAAA;IAAAH,IAAA,GAAAG,CAAA;EAAA;AAAA;AAAA,IAAAC,OAAA,EAAAC,GAAA;AAAA7B,MAAA,CAAAyB,IAAA;EAAAG,QAAAD,CAAA;IAAAC,OAAA,GAAAD,CAAA;EAAA;EAAAE,IAAAF,CAAA;IAAAE,GAAA,GAAAF,CAAA;EAAA;AAAA;AAelB,SAAUzB,uBAAuBA,CAAA;EACrC,OAAO4B,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIH,OAAO,EAAE,CAACI,WAAW,EAAE,CAACC,QAAQ,CAAC,WAAW,CAAC;AACtF;AAEM,SAAU9B,WAAWA,CAAC+B,CAAS,EAA8B;EAAA,IAA5BC,WAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB,KAAK;EACjE;EACA;EACA,IAAIF,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAK,CAAEC,WAAY,EAAE;IACpCD,CAAC,GAAGJ,OAAO,CAACS,GAAG,CAACC,WAAW,GAAGN,CAAC;EACjC;EAEAA,CAAC,GAAGA,CAAC,CAACO,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACzB,IAAIP,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAEC,WAAW,EAAE;IACjC;IACAD,CAAC,GAAG,GAAG,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC;EAC7B;EAEA,OAAOR,CAAC;AACV;AAEO,MAAM9B,kBAAkB,GAAGD,WAAW;AAEvC,SAAUE,SAASA,CAAC6B,CAAS,EAA8B;EAAA,IAA5BC,WAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB,KAAK;EAC/D,IAAIF,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAEC,WAAW,EAAE;IACjC,IAAI,CAAE,mBAAmB,CAACQ,IAAI,CAACT,CAAC,CAAC,EAC/B,MAAM,IAAIU,KAAK,CAAC,mBAAmB,GAAGV,CAAC,CAAC;IAC1C;IACA;IACAA,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC;EAC7B;EAEAR,CAAC,GAAGA,CAAC,CAACO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;EAC1B,OAAOP,CAAC;AACV;AAEO,MAAM5B,oBAAoB,GAAGD,SAAS;AAEvC,SAAUE,eAAeA,CAACsC,YAAoB,EAA8B;EAAA,IAA5BV,WAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB,KAAK;EAChF,IAAIN,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;IAChC,OAAO1B,SAAS,CAACwC,YAAY,EAAEV,WAAW,CAAC;EAC7C;EACA,OAAOU,YAAY;AACrB;AAEM,SAAUrC,qBAAqBA,CAACsC,MAAc,EAA8B;EAAA,IAA5BX,WAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB,KAAK;EAChF,IAAIN,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;IAChC,OAAO5B,WAAW,CAAC2C,MAAM,EAAEX,WAAW,CAAC;EACzC;EACA,OAAOW,MAAM;AACf;AAEM,SAAUrC,sBAAsBA,CAACsC,YAAoB;EACzD,OAAOA,YAAY,CAACN,OAAO,CAAC,KAAK,EAAEZ,GAAG,CAAC;AACzC;AAEM,SAAUnB,4BAA4BA,CAACqC,YAAoB;EAC/D;EACA,OAAOA,YAAY,CAACN,OAAO,CAAC,IAAIO,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CACtCP,OAAO,CAAC,IAAIO,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;AAC1D;AAKM,SAAUrC,oBAAoBA,CAACa,IAAY;EAC/C,OAAQA,IAAI,GAAIA,IAAI,CAACyB,SAAS,CAAC,KAAK,CAAC,GAAGzB,IAAI;AAC9C;AAIM,SAAUZ,gBAAgBA,CAK9BsC,CAAI,EACwB;EAAA,IAA5Bf,WAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB,KAAK;EAE5B,OAAO,SAASe,OAAOA,CAAA;IACrB,IAAIrB,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;MAChC,MAAMqB,MAAM,GAAGF,CAAC,CAACG,KAAK,CAAC7B,IAAI,EAAE8B,KAAK,CAACC,SAAS,CAACC,GAAG,CAACC,IAAI,CACnDrB,SAAS;MACT;MACA;MACAF,CAAC,IAAI7B,SAAS,CAAC6B,CAAC,EAAEC,WAAW,CAAC,CACtB,CAAC;MAEX,OAAO,OAAOiB,MAAM,KAAK,QAAQ,GAC7BjD,WAAW,CAACiD,MAAM,EAAEjB,WAAW,CAAC,GAChCiB,MAAM;IACZ;IACA,OAAOF,CAAC,CAACG,KAAK,CAAC7B,IAAI,EAAEY,SAAgB,CAAC;EACxC,CAAM;AACR;AAEO,MAAMvB,QAAQ,GAAGD,gBAAgB,CAACY,IAAI,CAACkC,IAAI,EAAE,IAAI,CAAC;AAClD,MAAM5C,aAAa,GAAGF,gBAAgB,CAACY,IAAI,CAACyB,SAAS,CAAC;AACtD,MAAMlC,YAAY,GAAGH,gBAAgB,CAACY,IAAI,CAACmC,QAAQ,CAAC;AACpD,MAAM3C,WAAW,GAAGJ,gBAAgB,CAACY,IAAI,CAACoC,OAAO,CAAC;AAClD,MAAM3C,WAAW,GAAGL,gBAAgB,CAACY,IAAI,CAACqC,OAAO,CAAC;AAClD,MAAM3C,YAAY,GAAGN,gBAAgB,CAACY,IAAI,CAACsC,QAAQ,CAAC;AACpD,MAAM3C,WAAW,GAAGP,gBAAgB,CAACY,IAAI,CAACuC,OAAO,CAAC;AAClD,MAAM3C,cAAc,GAAGR,gBAAgB,CAACY,IAAI,CAACwC,UAAU,CAAC;AACxD,MAAM3C,OAAO,GAAG,GAAG;AACnB,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,eAAe,GAAGC,IAAI,CAACyC,SAAS", "ignoreList": [], "file": "tools/static-assets/server/mini-files.js.map"}