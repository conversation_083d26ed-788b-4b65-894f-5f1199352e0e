{"version": 3, "sources": ["meteor://💻app/packages/random/main_client.js", "meteor://💻app/packages/random/AbstractRandomGenerator.js", "meteor://💻app/packages/random/AleaRandomGenerator.js", "meteor://💻app/packages/random/BrowserRandomGenerator.js", "meteor://💻app/packages/random/createAleaGenerator.js", "meteor://💻app/packages/random/createRandom.js"], "names": ["module", "export", "Random", "BrowserRandomGenerator", "link", "default", "v", "createAleaGeneratorWithGeneratedSeed", "createRandom", "generator", "window", "crypto", "getRandomValues", "RandomGenerator", "Meteor", "UNMISTAKABLE_CHARS", "BASE64_CHARS", "_proto", "prototype", "fraction", "Error", "hexString", "digits", "_randomString", "charsCount", "alphabet", "result", "i", "choice", "id", "undefined", "secret", "arrayOrString", "index", "Math", "floor", "length", "substr", "_inherits<PERSON><PERSON>e", "AleaRandomGenerator", "Alea", "seeds", "<PERSON><PERSON>", "n", "mash", "data", "toString", "charCodeAt", "h", "version", "s0", "s1", "s2", "c", "Date", "random", "t", "uint32", "fract53", "args", "_RandomGenerator", "_this", "_ref", "arguments", "_ref$seeds", "call", "alea", "apply", "array", "Uint32Array", "createAleaGenerator", "height", "innerHeight", "document", "documentElement", "clientHeight", "body", "width", "innerWidth", "clientWidth", "agent", "navigator", "userAgent", "createWithSeeds", "_len", "Array", "_key", "insecure"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,MAAM,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,MAAM;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,sBAAsB;AAACH,MAAM,CAACI,IAAI,CAAC,0BAA0B,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACH,sBAAsB,GAACG,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,oCAAoC;AAACP,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACC,oCAAoC,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIE,YAAY;AAACR,MAAM,CAACI,IAAI,CAAC,gBAAgB,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACE,YAAY,GAACF,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAW3Y,IAAIG,SAAS;AACb,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,MAAM,IAChDD,MAAM,CAACC,MAAM,CAACC,eAAe,EAAE;EAC/BH,SAAS,GAAG,IAAIN,sBAAsB,CAAC,CAAC;AAC1C,CAAC,MAAM;EACL;EACA;EACA;EACA;EACA;EACAM,SAAS,GAAGF,oCAAoC,CAAC,CAAC;AACpD;AAGO,IAAML,MAAM,GAAGM,YAAY,CAACC,SAAS,CAAC,C;;;;;;;;;;;ACzB7CT,MAAM,CAACC,MAAM,CAAC;EAAC,WAAQ,SAAAI,CAAA,EAAU;IAAC,OAAOQ,eAAe;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIC,MAAM;AAACd,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;EAACU,MAAM,EAAC,SAAAA,CAASR,CAAC,EAAC;IAACQ,MAAM,GAACR,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAWpI,IAAMS,kBAAkB,GAAG,yDAAyD;AACpF,IAAMC,YAAY,GAAG,sDAAsD,GACzE,cAAc;;AAEhB;AACA;AACA;AACA;AACA;AACA;AAAA,IACqBH,eAAe;EAAA,SAAAA,gBAAA;EAAA,IAAAI,MAAA,GAAAJ,eAAA,CAAAK,SAAA;EAElC;AACF;AACA;AACA;AACA;EAJED,MAAA,CAKAE,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAI;MACV,MAAM,IAAIC,KAAK,gCAAgC,CAAC;IAClD;IAAC,OAFDD,QAAQ;EAAA;EAIR;AACF;AACA;AACA;AACA;AACA;EALE;EAAAF,MAAA,CAMAI,SAAS;IAAT,SAAAA,SAASA,CAAEC,MAAM,EAAE;MACjB,OAAO,IAAI,CAACC,aAAa,CAACD,MAAM,EAAE,kBAAkB,CAAC;IACvD;IAAC,OAFDD,SAAS;EAAA;EAAAJ,MAAA,CAITM,aAAa;IAAb,SAAAA,aAAaA,CAAEC,UAAU,EAAEC,QAAQ,EAAE;MACnC,IAAIC,MAAM,GAAG,EAAE;MACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,EAAEG,CAAC,EAAE,EAAE;QACnCD,MAAM,IAAI,IAAI,CAACE,MAAM,CAACH,QAAQ,CAAC;MACjC;MACA,OAAOC,MAAM;IACf;IAAC,OANDH,aAAa;EAAA;EAQb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;EAAAN,MAAA,CAQAY,EAAE;IAAF,SAAAA,EAAEA,CAAEL,UAAU,EAAE;MACd;MACA;MACA,IAAIA,UAAU,KAAKM,SAAS,EAAE;QAC5BN,UAAU,GAAG,EAAE;MACjB;MAEA,OAAO,IAAI,CAACD,aAAa,CAACC,UAAU,EAAET,kBAAkB,CAAC;IAC3D;IAAC,OARDc,EAAE;EAAA;EAUF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EARE;EAAAZ,MAAA,CASAc,MAAM;IAAN,SAAAA,MAAMA,CAAEP,UAAU,EAAE;MAClB;MACA;MACA,IAAIA,UAAU,KAAKM,SAAS,EAAE;QAC5BN,UAAU,GAAG,EAAE;MACjB;MAEA,OAAO,IAAI,CAACD,aAAa,CAACC,UAAU,EAAER,YAAY,CAAC;IACrD;IAAC,OARDe,MAAM;EAAA;EAUN;AACF;AACA;AACA;AACA;AACA;EALE;EAAAd,MAAA,CAMAW,MAAM;IAAN,SAAAA,MAAMA,CAAEI,aAAa,EAAE;MACrB,IAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAChB,QAAQ,CAAC,CAAC,GAAGa,aAAa,CAACI,MAAM,CAAC;MAChE,IAAI,OAAOJ,aAAa,KAAK,QAAQ,EAAE;QACrC,OAAOA,aAAa,CAACK,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;MACvC;MACA,OAAOD,aAAa,CAACC,KAAK,CAAC;IAC7B;IAAC,OANDL,MAAM;EAAA;EAAA,OAAAf,eAAA;AAAA,I;;;;;;;;;;;AC7FR,IAAIyB,cAAc;AAACtC,MAAM,CAACI,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACgC,cAAc,GAAChC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhHN,MAAM,CAACC,MAAM,CAAC;EAAC,WAAQ,SAAAI,CAAA,EAAU;IAAC,OAAOkC,mBAAmB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI1B,eAAe;AAACb,MAAM,CAACI,IAAI,CAAC,2BAA2B,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACO,eAAe,GAACP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAEvK;AACA;AACA;AACA,SAASkC,IAAIA,CAACC,KAAK,EAAE;EACnB,SAASC,IAAIA,CAAA,EAAG;IACd,IAAIC,CAAC,GAAG,UAAU;IAElB,IAAMC,IAAI,GAAG,SAAAA,CAACC,IAAI,EAAK;MACrBA,IAAI,GAAGA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACtB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,IAAI,CAACT,MAAM,EAAET,CAAC,EAAE,EAAE;QACpCgB,CAAC,IAAIE,IAAI,CAACE,UAAU,CAACpB,CAAC,CAAC;QACvB,IAAIqB,CAAC,GAAG,mBAAmB,GAAGL,CAAC;QAC/BA,CAAC,GAAGK,CAAC,KAAK,CAAC;QACXA,CAAC,IAAIL,CAAC;QACNK,CAAC,IAAIL,CAAC;QACNA,CAAC,GAAGK,CAAC,KAAK,CAAC;QACXA,CAAC,IAAIL,CAAC;QACNA,CAAC,IAAIK,CAAC,GAAG,WAAW,CAAC,CAAC;MACxB;MACA,OAAO,CAACL,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC;IAC7C,CAAC;IAEDC,IAAI,CAACK,OAAO,GAAG,UAAU;IACzB,OAAOL,IAAI;EACb;EAEA,IAAIM,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIZ,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;IACtBK,KAAK,GAAG,CAAC,CAAC,IAAIa,IAAI,CAAD,CAAC,CAAC;EACrB;EACA,IAAIV,IAAI,GAAGF,IAAI,CAAC,CAAC;EACjBQ,EAAE,GAAGN,IAAI,CAAC,GAAG,CAAC;EACdO,EAAE,GAAGP,IAAI,CAAC,GAAG,CAAC;EACdQ,EAAE,GAAGR,IAAI,CAAC,GAAG,CAAC;EAEd,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,KAAK,CAACL,MAAM,EAAET,CAAC,EAAE,EAAE;IACrCuB,EAAE,IAAIN,IAAI,CAACH,KAAK,CAACd,CAAC,CAAC,CAAC;IACpB,IAAIuB,EAAE,GAAG,CAAC,EAAE;MACVA,EAAE,IAAI,CAAC;IACT;IACAC,EAAE,IAAIP,IAAI,CAACH,KAAK,CAACd,CAAC,CAAC,CAAC;IACpB,IAAIwB,EAAE,GAAG,CAAC,EAAE;MACVA,EAAE,IAAI,CAAC;IACT;IACAC,EAAE,IAAIR,IAAI,CAACH,KAAK,CAACd,CAAC,CAAC,CAAC;IACpB,IAAIyB,EAAE,GAAG,CAAC,EAAE;MACVA,EAAE,IAAI,CAAC;IACT;EACF;EACAR,IAAI,GAAG,IAAI;EAEX,IAAMW,MAAM,GAAG,SAAAA,CAAA,EAAM;IACnB,IAAMC,CAAC,GAAI,OAAO,GAAGN,EAAE,GAAKG,CAAC,GAAG,sBAAuB,CAAC,CAAC;IACzDH,EAAE,GAAGC,EAAE;IACPA,EAAE,GAAGC,EAAE;IACP,OAAOA,EAAE,GAAGI,CAAC,IAAIH,CAAC,GAAGG,CAAC,GAAG,CAAC,CAAC;EAC7B,CAAC;EAEDD,MAAM,CAACE,MAAM,GAAG;IAAA,OAAMF,MAAM,CAAC,CAAC,GAAG,WAAW;EAAA,EAAC,CAAC;EAC9CA,MAAM,CAACG,OAAO,GAAG;IAAA,OAAMH,MAAM,CAAC,CAAC,GACxB,CAACA,MAAM,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,sBAAuB;EAAA,EAAC,CAAC;;EAE5DA,MAAM,CAACN,OAAO,GAAG,UAAU;EAC3BM,MAAM,CAACI,IAAI,GAAGlB,KAAK;EACnB,OAAOc,MAAM;AACf;;AAEA;AACA;AACA;AACA;AAAA,IACqBhB,mBAAmB,0BAAAqB,gBAAA;EACtC,SAAArB,oBAAA,EAAkC;IAAA,IAAAsB,KAAA;IAAA,IAAAC,IAAA,GAAAC,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAjC,SAAA,GAAAiC,SAAA,MAAJ,CAAC,CAAC;MAAAC,UAAA,GAAAF,IAAA,CAAjBrB,KAAK;MAALA,KAAK,GAAAuB,UAAA,cAAG,EAAE,GAAAA,UAAA;IACvBH,KAAA,GAAAD,gBAAA,CAAAK,IAAA,KAAM,CAAC;IACP,IAAI,CAACxB,KAAK,EAAE;MACV,MAAM,IAAIrB,KAAK,CAAC,sCAAsC,CAAC;IACzD;IACAyC,KAAA,CAAKK,IAAI,GAAG1B,IAAI,CAACC,KAAK,CAAC;IAAC,OAAAoB,KAAA;EAC1B;;EAEA;AACF;AACA;AACA;AACA;EAJEvB,cAAA,CAAAC,mBAAA,EAAAqB,gBAAA;EAAA,IAAA3C,MAAA,GAAAsB,mBAAA,CAAArB,SAAA;EAAAD,MAAA,CAKAE,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAI;MACV,OAAO,IAAI,CAAC+C,IAAI,CAAC,CAAC;IACpB;IAAC,OAFD/C,QAAQ;EAAA;EAAA,OAAAoB,mBAAA;AAAA,EAduC1B,eAAe,E;;;;;;;;;;;AC5EhE,IAAIyB,cAAc;AAACtC,MAAM,CAACI,IAAI,CAAC,sCAAsC,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACgC,cAAc,GAAChC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhHN,MAAM,CAACC,MAAM,CAAC;EAAC,WAAQ,SAAAI,CAAA,EAAU;IAAC,OAAOF,sBAAsB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAIU,eAAe;AAACb,MAAM,CAACI,IAAI,CAAC,2BAA2B,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACO,eAAe,GAACP,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAGtJH,sBAAsB,0BAAAyD,gBAAA;EAAA,SAAAzD,uBAAA;IAAA,OAAAyD,gBAAA,CAAAO,KAAA,OAAAJ,SAAA;EAAA;EAAAzB,cAAA,CAAAnC,sBAAA,EAAAyD,gBAAA;EAAA,IAAA3C,MAAA,GAAAd,sBAAA,CAAAe,SAAA;EACzC;AACF;AACA;AACA;AACA;EAJED,MAAA,CAKAE,QAAQ;IAAR,SAAAA,QAAQA,CAAA,EAAI;MACV,IAAMiD,KAAK,GAAG,IAAIC,WAAW,CAAC,CAAC,CAAC;MAChC3D,MAAM,CAACC,MAAM,CAACC,eAAe,CAACwD,KAAK,CAAC;MACpC,OAAOA,KAAK,CAAC,CAAC,CAAC,GAAG,sBAAsB,CAAC,CAAC;IAC5C;IAAC,OAJDjD,QAAQ;EAAA;EAAA,OAAAhB,sBAAA;AAAA,EAN0CU,eAAe,E;;;;;;;;;;;ACHnEb,MAAM,CAACC,MAAM,CAAC;EAAC,WAAQ,SAAAI,CAAA,EAAU;IAAC,OAAOiE,mBAAmB;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI/B,mBAAmB;AAACvC,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACiC,mBAAmB,GAACjC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAE3K;AACA;;AAEA;AACA,IAAMiE,MAAM,GAAI,OAAO7D,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC8D,WAAW,IAC5D,OAAOC,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACC,eAAe,IACxBD,QAAQ,CAACC,eAAe,CAACC,YAAa,IACzC,OAAOF,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACG,IAAI,IACbH,QAAQ,CAACG,IAAI,CAACD,YAAa,IAC/B,CAAC;AAEP,IAAME,KAAK,GAAI,OAAOnE,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACoE,UAAU,IAC1D,OAAOL,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACC,eAAe,IACxBD,QAAQ,CAACC,eAAe,CAACK,WAAY,IACxC,OAAON,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACG,IAAI,IACbH,QAAQ,CAACG,IAAI,CAACG,WAAY,IAC9B,CAAC;AAEP,IAAMC,KAAK,GAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAK,EAAE;AAE9D,SAASZ,mBAAmBA,CAAA,EAAG;EAC5C,OAAO,IAAI/B,mBAAmB,CAAC;IAC7BE,KAAK,EAAE,CAAC,IAAIa,IAAI,CAAD,CAAC,EAAEiB,MAAM,EAAEM,KAAK,EAAEG,KAAK,EAAE9C,IAAI,CAACqB,MAAM,CAAC,CAAC;EACvD,CAAC,CAAC;AACJ,C;;;;;;;;;;;AC9BAvD,MAAM,CAACC,MAAM,CAAC;EAAC,WAAQ,SAAAI,CAAA,EAAU;IAAC,OAAOG,YAAY;EAAA;AAAC,CAAC,CAAC;AAAC,IAAI+B,mBAAmB;AAACvC,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACiC,mBAAmB,GAACjC,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAC,IAAIC,oCAAoC;AAACP,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;EAAC,WAAQ,SAAAC,CAASC,CAAC,EAAC;IAACC,oCAAoC,GAACD,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAGnS,SAASE,YAAYA,CAACC,SAAS,EAAE;EAC9C;EACA;EACAA,SAAS,CAAC0E,eAAe,GAAG,YAAc;IAAA,SAAAC,IAAA,GAAArB,SAAA,CAAA3B,MAAA,EAAVK,KAAK,OAAA4C,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAL7C,KAAK,CAAA6C,IAAA,IAAAvB,SAAA,CAAAuB,IAAA;IAAA;IACnC,IAAI7C,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIhB,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IACA,OAAO,IAAImB,mBAAmB,CAAC;MAAEE,KAAK,EAALA;IAAM,CAAC,CAAC;EAC3C,CAAC;;EAED;EACA;EACAhC,SAAS,CAAC8E,QAAQ,GAAGhF,oCAAoC,CAAC,CAAC;EAE3D,OAAOE,SAAS;AAClB,C", "file": "/packages/random.js", "sourcesContent": ["// We use cryptographically strong PRNGs (window.crypto.getRandomValues())\n// when available. If these PRNGs fail, we fall back to the Alea PRNG, which is \n// not cryptographically strong, and we seed it with various sources \n// such as the date, Math.random, and window size on the client.\n// When using window.crypto.getRandomValues() or alea, the primitive is fraction \n// and we use that to construct hex string.\n\nimport BrowserRandomGenerator from './BrowserRandomGenerator';\nimport createAleaGeneratorWithGeneratedSeed from './createAleaGenerator';\nimport createRandom from './createRandom';\n\nlet generator;\nif (typeof window !== 'undefined' && window.crypto &&\n  window.crypto.getRandomValues) {\n  generator = new BrowserRandomGenerator();\n} else {\n  // On IE 10 and below, there's no browser crypto API\n  // available. Fall back to Alea\n  //\n  // XXX looks like at the moment, we use Alea in IE 11 as well,\n  // which has `window.msCrypto` instead of `window.crypto`.\n  generator = createAleaGeneratorWithGeneratedSeed();\n}\n\n\nexport const Random = createRandom(generator);\n", "// We use cryptographically strong PRNGs (crypto.getRandomBytes() on the server,\n// window.crypto.getRandomValues() in the browser) when available. If these\n// PRNGs fail, we fall back to the Alea PRNG, which is not cryptographically\n// strong, and we seed it with various sources such as the date, Math.random,\n// and window size on the client.  When using crypto.getRandomValues(), our\n// primitive is hexString(), from which we construct fraction(). When using\n// window.crypto.getRandomValues() or alea, the primitive is fraction and we use\n// that to construct hex string.\n\nimport { Meteor } from 'meteor/meteor';\n\nconst UNMISTAKABLE_CHARS = '23456789ABCDEFGHJKLMNPQRSTWXYZabcdefghijkmnopqrstuvwxyz';\nconst BASE64_CHARS = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ' +\n  '0123456789-_';\n\n// `type` is one of `RandomGenerator.Type` as defined below.\n//\n// options:\n// - seeds: (required, only for RandomGenerator.Type.ALEA) an array\n//   whose items will be `toString`ed and used as the seed to the Alea\n//   algorithm\nexport default class RandomGenerator {\n\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    throw new Error(`Unknown random generator type`);\n  }\n\n  /**\n   * @name Random.hexString\n   * @summary Return a random string of `n` hexadecimal digits.\n   * @locus Anywhere\n   * @param {Number} n Length of the string\n   */\n  hexString (digits) {\n    return this._randomString(digits, '0123456789abcdef');\n  }\n\n  _randomString (charsCount, alphabet) {\n    let result = '';\n    for (let i = 0; i < charsCount; i++) {\t\n      result += this.choice(alphabet);\n    }\n    return result;\n  }\n\n  /**\n   * @name Random.id\n   * @summary Return a unique identifier, such as `\"Jjwjg6gouWLXhMGKW\"`, that is\n   * likely to be unique in the whole world.\n   * @locus Anywhere\n   * @param {Number} [n] Optional length of the identifier in characters\n   *   (defaults to 17)\n   */\n  id (charsCount) {\n    // 17 characters is around 96 bits of entropy, which is the amount of\n    // state in the Alea PRNG.\n    if (charsCount === undefined) {\n      charsCount = 17;\n    }\n\n    return this._randomString(charsCount, UNMISTAKABLE_CHARS);\n  }\n\n  /**\n   * @name Random.secret\n   * @summary Return a random string of printable characters with 6 bits of\n   * entropy per character. Use `Random.secret` for security-critical secrets\n   * that are intended for machine, rather than human, consumption.\n   * @locus Anywhere\n   * @param {Number} [n] Optional length of the secret string (defaults to 43\n   *   characters, or 256 bits of entropy)\n   */\n  secret (charsCount) {\n    // Default to 256 bits of entropy, or 43 characters at 6 bits per\n    // character.\n    if (charsCount === undefined) {\n      charsCount = 43;\n    }\n\n    return this._randomString(charsCount, BASE64_CHARS);\n  }\n\n  /**\n   * @name Random.choice\n   * @summary Return a random element of the given array or string.\n   * @locus Anywhere\n   * @param {Array|String} arrayOrString Array or string to choose from\n   */\n  choice (arrayOrString) {\n    const index = Math.floor(this.fraction() * arrayOrString.length);\n    if (typeof arrayOrString === 'string') {\n      return arrayOrString.substr(index, 1);\n    }\n    return arrayOrString[index];\n  }\n}\n", "import RandomGenerator from './AbstractRandomGenerator';\n\n// Alea PRNG, which is not cryptographically strong\n// see http://baagoe.org/en/wiki/Better_random_numbers_for_javascript\n// for a full discussion and Alea implementation.\nfunction Alea(seeds) {\n  function Mash() {\n    let n = 0xefc8249d;\n\n    const mash = (data) => {\n      data = data.toString();\n      for (let i = 0; i < data.length; i++) {\n        n += data.charCodeAt(i);\n        let h = 0.02519603282416938 * n;\n        n = h >>> 0;\n        h -= n;\n        h *= n;\n        n = h >>> 0;\n        h -= n;\n        n += h * 0x100000000; // 2^32\n      }\n      return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n    };\n\n    mash.version = 'Mash 0.9';\n    return mash;\n  }\n\n  let s0 = 0;\n  let s1 = 0;\n  let s2 = 0;\n  let c = 1;\n  if (seeds.length === 0) {\n    seeds = [+new Date];\n  }\n  let mash = Mash();\n  s0 = mash(' ');\n  s1 = mash(' ');\n  s2 = mash(' ');\n\n  for (let i = 0; i < seeds.length; i++) {\n    s0 -= mash(seeds[i]);\n    if (s0 < 0) {\n      s0 += 1;\n    }\n    s1 -= mash(seeds[i]);\n    if (s1 < 0) {\n      s1 += 1;\n    }\n    s2 -= mash(seeds[i]);\n    if (s2 < 0) {\n      s2 += 1;\n    }\n  }\n  mash = null;\n\n  const random = () => {\n    const t = (2091639 * s0) + (c * 2.3283064365386963e-10); // 2^-32\n    s0 = s1;\n    s1 = s2;\n    return s2 = t - (c = t | 0);\n  };\n\n  random.uint32 = () => random() * 0x100000000; // 2^32\n  random.fract53 = () => random() +\n        ((random() * 0x200000 | 0) * 1.1102230246251565e-16); // 2^-53\n\n  random.version = 'Alea 0.9';\n  random.args = seeds;\n  return random;\n}\n\n// options:\n// - seeds: an array\n//   whose items will be `toString`ed and used as the seed to the Alea\n//   algorithm\nexport default class AleaRandomGenerator extends RandomGenerator {\n  constructor ({ seeds = [] } = {}) {\n    super();\n    if (!seeds) {\n      throw new Error('No seeds were provided for Alea PRNG');\n    }\n    this.alea = Alea(seeds);\n  }\n\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    return this.alea();\n  }\n}\n", "import RandomGenerator from './AbstractRandomGenerator';\n\n// cryptographically strong PRNGs available in modern browsers\nexport default class BrowserRandomGenerator extends RandomGenerator {\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    const array = new Uint32Array(1);\n    window.crypto.getRandomValues(array);\n    return array[0] * 2.3283064365386963e-10; // 2^-32\n  }\n}\n", "import AleaRandomGenerator from './AleaRandomGenerator';\n\n// instantiate RNG.  Heuristically collect entropy from various sources when a\n// cryptographic PRNG isn't available.\n\n// client sources\nconst height = (typeof window !== 'undefined' && window.innerHeight) ||\n      (typeof document !== 'undefined'\n       && document.documentElement\n       && document.documentElement.clientHeight) ||\n      (typeof document !== 'undefined'\n       && document.body\n       && document.body.clientHeight) ||\n      1;\n\nconst width = (typeof window !== 'undefined' && window.innerWidth) ||\n      (typeof document !== 'undefined'\n       && document.documentElement\n       && document.documentElement.clientWidth) ||\n      (typeof document !== 'undefined'\n       && document.body\n       && document.body.clientWidth) ||\n      1;\n\nconst agent = (typeof navigator !== 'undefined' && navigator.userAgent) || '';\n\nexport default function createAleaGenerator() {\n  return new AleaRandomGenerator({\n    seeds: [new Date, height, width, agent, Math.random()],\n  });\n}\n", "import AleaRandomGenerator from './AleaRandomGenerator'\nimport createAleaGeneratorWithGeneratedSeed from './createAleaGenerator';\n\nexport default function createRandom(generator) {\n  // Create a non-cryptographically secure PRNG with a given seed (using\n  // the Alea algorithm)\n  generator.createWithSeeds = (...seeds) => {\n    if (seeds.length === 0) {\n      throw new Error('No seeds were provided');\n    }\n    return new AleaRandomGenerator({ seeds });\n  };\n\n  // Used like `Random`, but much faster and not cryptographically\n  // secure\n  generator.insecure = createAleaGeneratorWithGeneratedSeed();\n\n  return generator;\n}\n"]}