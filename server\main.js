import { Meteor } from 'meteor/meteor';
import { LinksCollection } from '/imports/api/links';
import { Accounts } from 'meteor/accounts-base';
import { Email } from 'meteor/email';
import { Tasks } from '/imports/api/tasks';
import { Roles } from 'meteor/alanning:roles';
import { check } from 'meteor/check';
import bcrypt from 'bcrypt';

async function insertLink({ title, url }) {
  await LinksCollection.insertAsync({ title, url, createdAt: new Date() });
}

const ADMIN_TOKEN = '123456';

Meteor.startup(async () => {
  // Ensure indexes for Tasks collection
  try {
    await Tasks.createIndex({ createdAt: 1 });
    await Tasks.createIndex({ assignedTo: 1 });
    await Tasks.createIndex({ createdBy: 1 });

    // Ensure indexes for Users collection
    // Note: emails.address index is already created by Meteor accounts system
    await Meteor.users.createIndex({ roles: 1 }, { background: true });
  } catch (error) {
    console.warn('[Startup] Index creation warning:', error.message);
  }

  // Check if we have any team members
  try {
    const allUsers = await Meteor.users.find().fetchAsync();
    console.log('[Startup] All users:', allUsers.map(user => ({
      id: user._id,
      email: user.emails?.[0]?.address,
      roles: user.roles,
      profile: user.profile
    })));

    // First, ensure all users have roles and createdAt
    for (const user of allUsers) {
      const updates = {};
      
      if (!user.roles || !Array.isArray(user.roles)) {
        updates.roles = ['team-member'];
      }
      
      if (!user.createdAt) {
        updates.createdAt = new Date();
      }
      
      if (Object.keys(updates).length > 0) {
        console.log('[Startup] Fixing missing fields for user:', user.emails?.[0]?.address);
        await Meteor.users.updateAsync(user._id, {
          $set: updates
        });
      }
    }

    const teamMembersCount = await Meteor.users.find({ 'roles': 'team-member' }).countAsync();
    console.log('[Startup] Found team members:', teamMembersCount);

    // Create test team members if none exist
    if (teamMembersCount === 0) {
      console.log('[Startup] Creating test team members');
      try {
        // Create multiple test team members
        const testMembers = [
          {
            email: '<EMAIL>',
            password: 'TeamPass123!',
            firstName: 'John',
            lastName: 'Doe'
          },
          {
            email: '<EMAIL>',
            password: 'TeamPass123!',
            firstName: 'Jane',
            lastName: 'Smith'
          }
        ];

        for (const member of testMembers) {
          const userId = await Accounts.createUserAsync({
            email: member.email,
            password: member.password,
            role: 'team-member',
            createdAt: new Date(),
            profile: {
              firstName: member.firstName,
              lastName: member.lastName,
              role: 'team-member',
              fullName: `${member.firstName} ${member.lastName}`
            }
          });

          // Set the role explicitly
          await Meteor.users.updateAsync(userId, {
            $set: { roles: ['team-member'] }
          });

          console.log('[Startup] Created test team member:', {
            id: userId,
            email: member.email,
            name: `${member.firstName} ${member.lastName}`
          });
        }
      } catch (error) {
        console.error('[Startup] Error creating test team members:', error);
      }
    }
  } catch (error) {
    console.error('[Startup] Error checking team members:', error);
  }

  // Email configuration from settings
  const emailSettings = Meteor.settings.private?.email;
  
  // Configure email SMTP
  if (emailSettings?.username && emailSettings?.password) {
    process.env.MAIL_URL = `smtp://${encodeURIComponent(emailSettings.username)}:${encodeURIComponent(emailSettings.password)}@${emailSettings.server}:${emailSettings.port}`;
    
    // Test email configuration
    try {
      console.log('Testing email configuration...');
      Email.send({
        to: emailSettings.username,
        from: emailSettings.username,
        subject: 'Test Email',
        text: 'If you receive this email, your email configuration is working correctly.'
      });
      console.log('Test email sent successfully!');
    } catch (error) {
      console.error('Error sending test email:', error);
    }
  } else {
    console.warn('Email configuration is missing in settings.json');
  }

  // Configure account creation to require email verification
  Accounts.config({
    sendVerificationEmail: true,
    forbidClientAccountCreation: false
  });

  // Customize verification email
  Accounts.emailTemplates.siteName = "Task Management System";
  Accounts.emailTemplates.from = emailSettings?.username ? 
    `Task Management System <${emailSettings.username}>` : 
    "Task Management System <<EMAIL>>";

  Accounts.emailTemplates.verifyEmail = {
    subject() {
      return "Verify Your Email Address";
    },
    text(user, url) {
      const emailAddress = user.emails[0].address;
      return `Hello,\n\n`
        + `To verify your email address (${emailAddress}), please click the link below:\n\n`
        + `${url}\n\n`
        + `If you did not request this verification, please ignore this email.\n\n`
        + `Thanks,\n`
        + `Your Task Management System Team`;
    },
    html(user, url) {
      const emailAddress = user.emails[0].address;
      return `
        <html>
          <body style="font-family: Arial, sans-serif; padding: 20px; color: #333;">
            <h2 style="color: #00875a;">Verify Your Email Address</h2>
            <p>Hello,</p>
            <p>To verify your email address (${emailAddress}), please click the button below:</p>
            <p style="margin: 20px 0;">
              <a href="${url}" 
                 style="background-color: #00875a; 
                        color: white; 
                        padding: 12px 25px; 
                        text-decoration: none; 
                        border-radius: 4px;
                        display: inline-block;">
                Verify Email Address
              </a>
            </p>
            <p>If you did not request this verification, please ignore this email.</p>
            <p>Thanks,<br>Your Task Management System Team</p>
          </body>
        </html>
      `;
    }
  };

  // If the Links collection is empty, add some data.
  if (await LinksCollection.find().countAsync() === 0) {
    await insertLink({
      title: 'Do the Tutorial',
      url: 'https://www.meteor.com/tutorials/react/creating-an-app',
    });

    await insertLink({
      title: 'Follow the Guide',
      url: 'https://guide.meteor.com',
    });

    await insertLink({
      title: 'Read the Docs',
      url: 'https://docs.meteor.com',
    });

    await insertLink({
      title: 'Discussions',
      url: 'https://forums.meteor.com',
    });
  }

  // We publish the entire Links collection to all clients.
  // In order to be fetched in real-time to the clients
  Meteor.publish("links", function () {
    return LinksCollection.find();
  });

  // Add custom fields to users
  Accounts.onCreateUser((options, user) => {
    console.log('[onCreateUser] Creating user with options:', {
      email: options.email,
      role: options.role,
      profile: options.profile,
      createdAt: options.createdAt
    });

    const customizedUser = { ...user };
    
    // Ensure we have a profile
    customizedUser.profile = options.profile || {};
    
    // Add role from options
    const role = options.role || 'team-member';
    customizedUser.roles = [role];
    
    // Set createdAt if provided, otherwise use current date
    customizedUser.createdAt = options.createdAt || new Date();
    
    console.log('[onCreateUser] Created user:', {
      id: customizedUser._id,
      email: customizedUser.emails?.[0]?.address,
      roles: customizedUser.roles,
      profile: customizedUser.profile,
      createdAt: customizedUser.createdAt
    });
    
    return customizedUser;
  });

  // Publish team members
  Meteor.publish('teamMembers', function() {
    console.log('[teamMembers] Publication called, userId:', this.userId);
    
    if (!this.userId) {
      console.log('[teamMembers] No userId, returning ready');
      return this.ready();
    }

    try {
      // Simple query to find all team members
      const teamMembers = Meteor.users.find(
        { 
          $or: [
            { 'roles': 'team-member' },
            { 'profile.role': 'team-member' }
          ]
        },
        { 
          fields: { 
            emails: 1, 
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.fullName': 1,
            createdAt: 1
          }
        }
      );

      console.log('[teamMembers] Publishing team members');
      return teamMembers;
    } catch (error) {
      console.error('[teamMembers] Error in publication:', error);
      return this.ready();
    }
  });

  // Publish user data with roles
  Meteor.publish('userData', function() {
    if (!this.userId) {
      return this.ready();
    }

    console.log('[userData] Publishing data for user:', this.userId);
    
    return Meteor.users.find(
      { _id: this.userId },
      { 
        fields: { 
          roles: 1, 
          emails: 1,
          profile: 1
        } 
      }
    );
  });
});

// Method to create a new user with role
Meteor.methods({
  'users.create'({ email, password, role, adminToken, firstName, lastName }) {
    // Validate admin token if trying to create admin account
    if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
      throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
    }

    // Validate password requirements
    const passwordRegex = {
      length: /.{8,}/,
      uppercase: /[A-Z]/,
      number: /[0-9]/,
      special: /[!@#$%^&*]/
    };

    const passwordErrors = [];
    if (!passwordRegex.length.test(password)) {
      passwordErrors.push('Password must be at least 8 characters long');
    }
    if (!passwordRegex.uppercase.test(password)) {
      passwordErrors.push('Password must contain at least one uppercase letter');
    }
    if (!passwordRegex.number.test(password)) {
      passwordErrors.push('Password must contain at least one number');
    }
    if (!passwordRegex.special.test(password)) {
      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
    }

    if (passwordErrors.length > 0) {
      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
    }

    // Create the user
    try {
      const userId = Accounts.createUser({
        email,
        password,
        role, // This will be used in onCreateUser callback
        profile: {
          role, // Store in profile as well for easy access
          firstName,
          lastName,
          fullName: `${firstName} ${lastName}`
        }
      });

      // Send verification email
      if (userId) {
        Accounts.sendVerificationEmail(userId);
      }

      return userId;
    } catch (error) {
      throw new Meteor.Error('create-user-failed', error.message);
    }
  },

  async 'users.getRole'() {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'User must be logged in');
    }
    
    try {
      const user = await Meteor.users.findOneAsync(this.userId);
      if (!user) {
        throw new Meteor.Error('user-not-found', 'User not found');
      }
      
      // Check both roles array and profile for role
      const role = user.roles?.[0] || user.profile?.role || 'team-member';
      return role;
    } catch (error) {
      throw new Meteor.Error('get-role-failed', error.message);
    }
  },

  'users.resendVerificationEmail'(email) {
    // Find user by email
    const user = Accounts.findUserByEmail(email);
    if (!user) {
      throw new Meteor.Error('user-not-found', 'No user found with this email address');
    }

    // Check if email is already verified
    const userEmail = user.emails[0];
    if (userEmail.verified) {
      throw new Meteor.Error('already-verified', 'This email is already verified');
    }

    // Send verification email
    try {
      Accounts.sendVerificationEmail(user._id, email);
      return true;
    } catch (error) {
      throw new Meteor.Error('verification-email-failed', error.message);
    }
  },

  async 'users.forgotPassword'(data) {
    console.log('[forgotPassword] Method called with data:', JSON.stringify(data));

    check(data, {
      email: String,
      newPassword: String
    });

    const { email, newPassword } = data;
    console.log('[forgotPassword] Processing request for email:', email);

    // Find user by email using async method
    console.log('[forgotPassword] Searching for user...');
    let targetUser = await Meteor.users.findOneAsync({
      'emails.address': email
    });

    if (!targetUser) {
      console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');
      targetUser = await Meteor.users.findOneAsync({
        'emails.address': { $regex: new RegExp(`^${email}$`, 'i') }
      });

      if (!targetUser) {
        throw new Meteor.Error('user-not-found', 'No user found with this email address');
      }

      console.log('[forgotPassword] User found with case-insensitive search');
    } else {
      console.log('[forgotPassword] User found with direct search');
    }

    // Ensure we have a valid user with ID
    if (!targetUser || !targetUser._id) {
      throw new Meteor.Error('user-invalid', 'Found user but missing ID');
    }

    console.log('[forgotPassword] Final user ID:', targetUser._id);
    console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);

    // Validate password requirements
    const passwordRegex = {
      length: /.{8,}/,
      uppercase: /[A-Z]/,
      number: /[0-9]/,
      special: /[!@#$%^&*]/
    };

    const passwordErrors = [];
    if (!passwordRegex.length.test(newPassword)) {
      passwordErrors.push('Password must be at least 8 characters long');
    }
    if (!passwordRegex.uppercase.test(newPassword)) {
      passwordErrors.push('Password must contain at least one uppercase letter');
    }
    if (!passwordRegex.number.test(newPassword)) {
      passwordErrors.push('Password must contain at least one number');
    }
    if (!passwordRegex.special.test(newPassword)) {
      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
    }

    if (passwordErrors.length > 0) {
      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
    }

    // Comprehensive password update with debugging
    try {
      console.log('[forgotPassword] Starting password update...');

      // First, check current user document
      console.log('[forgotPassword] Checking current user document...');
      const currentUser = await Meteor.users.findOneAsync(targetUser._id);
      console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));

      if (!currentUser) {
        throw new Meteor.Error('user-not-found', 'User document not found during update');
      }

      // Create password hash using bcrypt directly
      const bcrypt = require('bcrypt');
      const saltRounds = 10;
      const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);
      console.log('[forgotPassword] Password hash created, length:', hashedPassword.length);
      console.log('[forgotPassword] Hash preview:', hashedPassword.substring(0, 20) + '...');

      // Try multiple update approaches
      let updateResult = 0;
      let successMethod = null;

      // Method 1: Update services.password.bcrypt directly
      console.log('[forgotPassword] Method 1: Updating services.password.bcrypt...');
      try {
        updateResult = await Meteor.users.updateAsync(targetUser._id, {
          $set: {
            'services.password.bcrypt': hashedPassword
          }
        });
        console.log('[forgotPassword] Method 1 result:', updateResult);
        if (updateResult === 1) {
          successMethod = 'Method 1: services.password.bcrypt';
        }
      } catch (method1Error) {
        console.error('[forgotPassword] Method 1 error:', method1Error);
      }

      // Method 2: Update entire services.password object
      if (updateResult !== 1) {
        console.log('[forgotPassword] Method 2: Updating entire services.password object...');
        try {
          updateResult = await Meteor.users.updateAsync(targetUser._id, {
            $set: {
              'services.password': {
                bcrypt: hashedPassword
              }
            }
          });
          console.log('[forgotPassword] Method 2 result:', updateResult);
          if (updateResult === 1) {
            successMethod = 'Method 2: entire services.password object';
          }
        } catch (method2Error) {
          console.error('[forgotPassword] Method 2 error:', method2Error);
        }
      }

      // Method 3: Update entire services object
      if (updateResult !== 1) {
        console.log('[forgotPassword] Method 3: Updating entire services object...');
        try {
          updateResult = await Meteor.users.updateAsync(targetUser._id, {
            $set: {
              services: {
                password: {
                  bcrypt: hashedPassword
                },
                resume: currentUser.services?.resume || { loginTokens: [] },
                email: currentUser.services?.email || {}
              }
            }
          });
          console.log('[forgotPassword] Method 3 result:', updateResult);
          if (updateResult === 1) {
            successMethod = 'Method 3: entire services object';
          }
        } catch (method3Error) {
          console.error('[forgotPassword] Method 3 error:', method3Error);
        }
      }

      // Method 4: Test basic update capability
      if (updateResult !== 1) {
        console.log('[forgotPassword] Method 4: Testing basic update capability...');
        try {
          const testResult = await Meteor.users.updateAsync(targetUser._id, {
            $set: {
              'profile.passwordResetTest': new Date()
            }
          });
          console.log('[forgotPassword] Basic update test result:', testResult);

          if (testResult === 1) {
            console.log('[forgotPassword] Basic updates work, trying password with $unset first...');
            // Try unsetting and then setting
            await Meteor.users.updateAsync(targetUser._id, {
              $unset: { 'services.password': '' }
            });

            updateResult = await Meteor.users.updateAsync(targetUser._id, {
              $set: {
                'services.password': {
                  bcrypt: hashedPassword
                }
              }
            });
            console.log('[forgotPassword] Unset/Set method result:', updateResult);
            if (updateResult === 1) {
              successMethod = 'Method 4: unset then set';
            }
          }
        } catch (method4Error) {
          console.error('[forgotPassword] Method 4 error:', method4Error);
        }
      }

      if (updateResult === 1) {
        console.log(`[forgotPassword] Password update successful using: ${successMethod}`);

        // Verify the update worked
        const updatedUser = await Meteor.users.findOneAsync(targetUser._id);
        console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));

        // Test password verification
        if (updatedUser.services?.password?.bcrypt) {
          const testVerification = bcrypt.compareSync(newPassword, updatedUser.services.password.bcrypt);
          console.log('[forgotPassword] Password verification test:', testVerification ? 'PASS' : 'FAIL');
        }

        return { success: true, message: 'Password updated successfully' };
      } else {
        console.error('[forgotPassword] All password update methods failed. Final result:', updateResult);

        // Log user permissions and collection info
        console.log('[forgotPassword] User ID:', targetUser._id);
        console.log('[forgotPassword] User ID type:', typeof targetUser._id);
        console.log('[forgotPassword] Current user exists:', !!currentUser);
        console.log('[forgotPassword] User roles:', currentUser.roles);

        throw new Meteor.Error('password-update-failed', 'Failed to update password in database');
      }

    } catch (error) {
      console.error('[forgotPassword] Error during password update:', error);
      throw new Meteor.Error('password-update-failed', `Failed to update password: ${error.message}`);
    }
  },

  async 'users.debugUser'({ email }) {
    try {
      check(email, String);

      console.log('[debugUser] Debugging user:', email);

      // Find user using async method
      const user = await Meteor.users.findOneAsync({
        'emails.address': email
      });

      if (!user) {
        console.log('[debugUser] User not found');
        return { success: false, error: 'User not found' };
      }

      console.log('[debugUser] User found:', user._id);

      // Get full user document using async method
      const fullUser = await Meteor.users.findOneAsync(user._id);
      console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));

      if (!fullUser) {
        console.log('[debugUser] Full user document not found');
        return { success: false, error: 'Full user document not found' };
      }

      // Test basic update using async method
      let testUpdateResult = null;
      try {
        testUpdateResult = await Meteor.users.updateAsync(user._id, {
          $set: { 'profile.debugTest': new Date() }
        });
        console.log('[debugUser] Test update result:', testUpdateResult);
      } catch (updateError) {
        console.error('[debugUser] Test update error:', updateError);
      }

      // Try using Accounts.setPassword if available
      let hasSetPassword = false;
      try {
        hasSetPassword = typeof Accounts.setPassword === 'function';
        console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);
      } catch (setPasswordError) {
        console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);
      }

      const result = {
        success: true,
        userId: user._id,
        userIdType: typeof user._id,
        hasServices: !!fullUser.services,
        hasPassword: !!(fullUser.services?.password),
        hasBcrypt: !!(fullUser.services?.password?.bcrypt),
        roles: fullUser.roles || [],
        profile: fullUser.profile || {},
        testUpdateResult: testUpdateResult,
        hasSetPassword: hasSetPassword,
        servicesStructure: fullUser.services || {}
      };

      console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));
      return result;

    } catch (error) {
      console.error('[debugUser] Error in debug method:', error);
      return { success: false, error: error.message };
    }
  },

  async 'users.testLogin'({ email, password }) {
    check(email, String);
    check(password, String);

    console.log('[testLogin] Testing login for email:', email);

    // Find user using async method
    const user = await Meteor.users.findOneAsync({
      'emails.address': email
    });

    if (!user) {
      console.log('[testLogin] User not found');
      return { success: false, error: 'User not found' };
    }

    console.log('[testLogin] User found:', user._id);
    console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));

    // Test password verification
    try {
      // Check if password service exists
      if (!user.services || !user.services.password || !user.services.password.bcrypt) {
        console.log('[testLogin] No password hash found in user services');
        return {
          success: false,
          error: 'No password hash found',
          userId: user._id,
          services: user.services
        };
      }

      const bcrypt = require('bcrypt');
      const storedHash = user.services.password.bcrypt;
      const passwordMatch = bcrypt.compareSync(password, storedHash);

      console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
      console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');
      console.log('[testLogin] Password length:', password.length);

      return {
        success: passwordMatch,
        userId: user._id,
        hashPreview: storedHash.substring(0, 20) + '...',
        passwordLength: password.length
      };
    } catch (error) {
      console.error('[testLogin] Error during password test:', error);
      return { success: false, error: error.message };
    }
  },

  async 'users.comparePasswordFormats'({ email }) {
    check(email, String);

    console.log('[comparePasswordFormats] Checking password format for:', email);

    // Find user
    const user = await Meteor.users.findOneAsync({
      'emails.address': email
    });

    if (!user) {
      return { success: false, error: 'User not found' };
    }

    console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));

    // Check if user has password
    if (!user.services?.password?.bcrypt) {
      return { success: false, error: 'No password found' };
    }

    const storedHash = user.services.password.bcrypt;
    console.log('[comparePasswordFormats] Stored hash:', storedHash);
    console.log('[comparePasswordFormats] Hash length:', storedHash.length);
    console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));

    // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)
    const isBcrypt = /^\$2[aby]\$/.test(storedHash);
    console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);

    return {
      success: true,
      userId: user._id,
      hashLength: storedHash.length,
      hashPreview: storedHash.substring(0, 20) + '...',
      isBcryptFormat: isBcrypt,
      fullServices: user.services
    };
  },

  async 'users.testActualLogin'({ email, password }) {
    check(email, String);
    check(password, String);

    console.log('[testActualLogin] Testing actual login for:', email);

    try {
      // Try to simulate what Meteor.loginWithPassword does
      const user = await Meteor.users.findOneAsync({
        'emails.address': email
      });

      if (!user) {
        console.log('[testActualLogin] User not found');
        return { success: false, error: 'User not found' };
      }

      console.log('[testActualLogin] User found:', user._id);
      console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));

      // Check if password service exists
      if (!user.services?.password?.bcrypt) {
        console.log('[testActualLogin] No password hash found');
        return { success: false, error: 'No password hash found' };
      }

      // Test bcrypt verification
      const bcrypt = require('bcrypt');
      const storedHash = user.services.password.bcrypt;
      const passwordMatch = bcrypt.compareSync(password, storedHash);

      console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');
      console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');

      // Check if user has any login restrictions
      const isVerified = user.emails?.[0]?.verified || false;
      console.log('[testActualLogin] Email verified:', isVerified);

      // Check user roles
      console.log('[testActualLogin] User roles:', user.roles);

      // Try to create a login token manually to test if that works
      let loginTokenTest = null;
      try {
        // This is what Meteor does internally for login
        const stampedToken = Accounts._generateStampedLoginToken();
        console.log('[testActualLogin] Generated login token:', !!stampedToken);
        loginTokenTest = 'Token generation successful';
      } catch (tokenError) {
        console.error('[testActualLogin] Token generation error:', tokenError);
        loginTokenTest = tokenError.message;
      }

      return {
        success: passwordMatch,
        userId: user._id,
        passwordVerification: passwordMatch,
        emailVerified: isVerified,
        userRoles: user.roles,
        hashPreview: storedHash.substring(0, 20) + '...',
        loginTokenTest: loginTokenTest,
        fullUserStructure: {
          _id: user._id,
          emails: user.emails,
          services: user.services,
          roles: user.roles,
          profile: user.profile
        }
      };

    } catch (error) {
      console.error('[testActualLogin] Error:', error);
      return { success: false, error: error.message };
    }
  },

  async 'users.checkAndFixAdminRole'() {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in');
    }
    
    try {
      const user = await Meteor.users.findOneAsync(this.userId);
      console.log('[checkAndFixAdminRole] Checking user:', {
        id: user?._id,
        email: user?.emails?.[0]?.address,
        roles: user?.roles
      });
      
      // If user has no roles array, initialize it
      if (!user.roles) {
        await Meteor.users.updateAsync(this.userId, {
          $set: { roles: ['team-member'] }
        });
        return 'Roles initialized';
      }
      
      // If user has no roles or doesn't have admin role
      if (!user.roles.includes('admin')) {
        console.log('[checkAndFixAdminRole] User is not admin, checking if first user');
        
        // Check if this is the first user (they should be admin)
        const totalUsers = await Meteor.users.find().countAsync();
        if (totalUsers === 1) {
          console.log('[checkAndFixAdminRole] First user, setting as admin');
          await Meteor.users.updateAsync(this.userId, {
            $set: { roles: ['admin'] }
          });
          return 'Admin role added';
        }
        return 'User is not admin';
      }
      
      return 'User is already admin';
    } catch (error) {
      console.error('[checkAndFixAdminRole] Error:', error);
      throw new Meteor.Error('check-role-failed', error.message);
    }
  },

  async 'users.diagnoseRoles'() {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in');
    }

    try {
      const currentUser = await Meteor.users.findOneAsync(this.userId);
      if (!currentUser.roles?.includes('admin')) {
        throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
      }

      const allUsers = await Meteor.users.find().fetchAsync();
      const usersWithIssues = [];
      const fixes = [];

      for (const user of allUsers) {
        const issues = [];
        
        // Check if roles array exists
        if (!user.roles || !Array.isArray(user.roles)) {
          issues.push('No roles array');
          // Fix: Initialize roles based on profile
          const role = user.profile?.role || 'team-member';
          await Meteor.users.updateAsync(user._id, {
            $set: { roles: [role] }
          });
          fixes.push(`Initialized roles for ${user.emails?.[0]?.address}`);
        }
        
        // Check if role matches profile
        if (user.profile?.role && user.roles?.[0] !== user.profile.role) {
          issues.push('Role mismatch with profile');
          // Fix: Update roles to match profile
          await Meteor.users.updateAsync(user._id, {
            $set: { roles: [user.profile.role] }
          });
          fixes.push(`Fixed role mismatch for ${user.emails?.[0]?.address}`);
        }

        if (issues.length > 0) {
          usersWithIssues.push({
            email: user.emails?.[0]?.address,
            issues
          });
        }
      }

      return {
        usersWithIssues,
        fixes,
        message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
      };
    } catch (error) {
      throw new Meteor.Error('diagnose-failed', error.message);
    }
  },

  'users.createTestTeamMember'() {
    // Only allow in development
    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
      try {
        const testMember = {
          email: '<EMAIL>',
          password: 'TestPass123!',
          firstName: 'Test',
          lastName: 'Member'
        };

        const userId = Accounts.createUser({
          email: testMember.email,
          password: testMember.password,
          profile: {
            firstName: testMember.firstName,
            lastName: testMember.lastName,
            role: 'team-member',
            fullName: `${testMember.firstName} ${testMember.lastName}`
          }
        });

        // Set the role explicitly
        Meteor.users.update(userId, {
          $set: { roles: ['team-member'] }
        });

        return {
          success: true,
          userId,
          message: 'Test team member created successfully'
        };
      } catch (error) {
        console.error('[createTestTeamMember] Error:', error);
        throw new Meteor.Error('create-test-member-failed', error.message);
      }
    } else {
      throw new Meteor.Error('not-development', 'This method is only available in development');
    }
  }
});