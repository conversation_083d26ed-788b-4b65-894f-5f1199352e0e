import { Meteor } from 'meteor/meteor';
import { LinksCollection } from '/imports/api/links';
import { Accounts } from 'meteor/accounts-base';
import { Email } from 'meteor/email';
import { Tasks } from '/imports/api/tasks';
import { Roles } from 'meteor/alanning:roles';

async function insertLink({ title, url }) {
  await LinksCollection.insertAsync({ title, url, createdAt: new Date() });
}

const ADMIN_TOKEN = '123456';

Meteor.startup(async () => {
  // Ensure indexes for Tasks collection
  try {
    await Tasks.createIndex({ createdAt: 1 });
    await Tasks.createIndex({ assignedTo: 1 });
    await Tasks.createIndex({ createdBy: 1 });

    // Ensure indexes for Users collection
    // Note: emails.address index is already created by Meteor accounts system
    await Meteor.users.createIndex({ roles: 1 }, { background: true });
  } catch (error) {
    console.warn('[Startup] Index creation warning:', error.message);
  }

  // Check if we have any team members
  try {
    const allUsers = await Meteor.users.find().fetchAsync();
    console.log('[Startup] All users:', allUsers.map(user => ({
      id: user._id,
      email: user.emails?.[0]?.address,
      roles: user.roles,
      profile: user.profile
    })));

    // First, ensure all users have roles and createdAt
    for (const user of allUsers) {
      const updates = {};
      
      if (!user.roles || !Array.isArray(user.roles)) {
        updates.roles = ['team-member'];
      }
      
      if (!user.createdAt) {
        updates.createdAt = new Date();
      }
      
      if (Object.keys(updates).length > 0) {
        console.log('[Startup] Fixing missing fields for user:', user.emails?.[0]?.address);
        await Meteor.users.updateAsync(user._id, {
          $set: updates
        });
      }
    }

    const teamMembersCount = await Meteor.users.find({ 'roles': 'team-member' }).countAsync();
    console.log('[Startup] Found team members:', teamMembersCount);

    // Create test team members if none exist
    if (teamMembersCount === 0) {
      console.log('[Startup] Creating test team members');
      try {
        // Create multiple test team members
        const testMembers = [
          {
            email: '<EMAIL>',
            password: 'TeamPass123!',
            firstName: 'John',
            lastName: 'Doe'
          },
          {
            email: '<EMAIL>',
            password: 'TeamPass123!',
            firstName: 'Jane',
            lastName: 'Smith'
          }
        ];

        for (const member of testMembers) {
          const userId = await Accounts.createUserAsync({
            email: member.email,
            password: member.password,
            role: 'team-member',
            createdAt: new Date(),
            profile: {
              firstName: member.firstName,
              lastName: member.lastName,
              role: 'team-member',
              fullName: `${member.firstName} ${member.lastName}`
            }
          });

          // Set the role explicitly
          await Meteor.users.updateAsync(userId, {
            $set: { roles: ['team-member'] }
          });

          console.log('[Startup] Created test team member:', {
            id: userId,
            email: member.email,
            name: `${member.firstName} ${member.lastName}`
          });
        }
      } catch (error) {
        console.error('[Startup] Error creating test team members:', error);
      }
    }
  } catch (error) {
    console.error('[Startup] Error checking team members:', error);
  }

  // Email configuration from settings
  const emailSettings = Meteor.settings.private?.email;
  
  // Configure email SMTP
  if (emailSettings?.username && emailSettings?.password) {
    process.env.MAIL_URL = `smtp://${encodeURIComponent(emailSettings.username)}:${encodeURIComponent(emailSettings.password)}@${emailSettings.server}:${emailSettings.port}`;
    
    // Test email configuration
    try {
      console.log('Testing email configuration...');
      Email.send({
        to: emailSettings.username,
        from: emailSettings.username,
        subject: 'Test Email',
        text: 'If you receive this email, your email configuration is working correctly.'
      });
      console.log('Test email sent successfully!');
    } catch (error) {
      console.error('Error sending test email:', error);
    }
  } else {
    console.warn('Email configuration is missing in settings.json');
  }

  // Configure account creation to require email verification
  Accounts.config({
    sendVerificationEmail: true,
    forbidClientAccountCreation: false
  });

  // Customize verification email
  Accounts.emailTemplates.siteName = "Task Management System";
  Accounts.emailTemplates.from = emailSettings?.username ? 
    `Task Management System <${emailSettings.username}>` : 
    "Task Management System <<EMAIL>>";

  Accounts.emailTemplates.verifyEmail = {
    subject() {
      return "Verify Your Email Address";
    },
    text(user, url) {
      const emailAddress = user.emails[0].address;
      return `Hello,\n\n`
        + `To verify your email address (${emailAddress}), please click the link below:\n\n`
        + `${url}\n\n`
        + `If you did not request this verification, please ignore this email.\n\n`
        + `Thanks,\n`
        + `Your Task Management System Team`;
    },
    html(user, url) {
      const emailAddress = user.emails[0].address;
      return `
        <html>
          <body style="font-family: Arial, sans-serif; padding: 20px; color: #333;">
            <h2 style="color: #00875a;">Verify Your Email Address</h2>
            <p>Hello,</p>
            <p>To verify your email address (${emailAddress}), please click the button below:</p>
            <p style="margin: 20px 0;">
              <a href="${url}" 
                 style="background-color: #00875a; 
                        color: white; 
                        padding: 12px 25px; 
                        text-decoration: none; 
                        border-radius: 4px;
                        display: inline-block;">
                Verify Email Address
              </a>
            </p>
            <p>If you did not request this verification, please ignore this email.</p>
            <p>Thanks,<br>Your Task Management System Team</p>
          </body>
        </html>
      `;
    }
  };

  // If the Links collection is empty, add some data.
  if (await LinksCollection.find().countAsync() === 0) {
    await insertLink({
      title: 'Do the Tutorial',
      url: 'https://www.meteor.com/tutorials/react/creating-an-app',
    });

    await insertLink({
      title: 'Follow the Guide',
      url: 'https://guide.meteor.com',
    });

    await insertLink({
      title: 'Read the Docs',
      url: 'https://docs.meteor.com',
    });

    await insertLink({
      title: 'Discussions',
      url: 'https://forums.meteor.com',
    });
  }

  // We publish the entire Links collection to all clients.
  // In order to be fetched in real-time to the clients
  Meteor.publish("links", function () {
    return LinksCollection.find();
  });

  // Add custom fields to users
  Accounts.onCreateUser((options, user) => {
    console.log('[onCreateUser] Creating user with options:', {
      email: options.email,
      role: options.role,
      profile: options.profile,
      createdAt: options.createdAt
    });

    const customizedUser = { ...user };
    
    // Ensure we have a profile
    customizedUser.profile = options.profile || {};
    
    // Add role from options
    const role = options.role || 'team-member';
    customizedUser.roles = [role];
    
    // Set createdAt if provided, otherwise use current date
    customizedUser.createdAt = options.createdAt || new Date();
    
    console.log('[onCreateUser] Created user:', {
      id: customizedUser._id,
      email: customizedUser.emails?.[0]?.address,
      roles: customizedUser.roles,
      profile: customizedUser.profile,
      createdAt: customizedUser.createdAt
    });
    
    return customizedUser;
  });

  // Publish team members
  Meteor.publish('teamMembers', function() {
    console.log('[teamMembers] Publication called, userId:', this.userId);
    
    if (!this.userId) {
      console.log('[teamMembers] No userId, returning ready');
      return this.ready();
    }

    try {
      // Simple query to find all team members
      const teamMembers = Meteor.users.find(
        { 
          $or: [
            { 'roles': 'team-member' },
            { 'profile.role': 'team-member' }
          ]
        },
        { 
          fields: { 
            emails: 1, 
            roles: 1,
            'profile.firstName': 1,
            'profile.lastName': 1,
            'profile.fullName': 1,
            createdAt: 1
          }
        }
      );

      console.log('[teamMembers] Publishing team members');
      return teamMembers;
    } catch (error) {
      console.error('[teamMembers] Error in publication:', error);
      return this.ready();
    }
  });

  // Publish user data with roles
  Meteor.publish('userData', function() {
    if (!this.userId) {
      return this.ready();
    }

    console.log('[userData] Publishing data for user:', this.userId);
    
    return Meteor.users.find(
      { _id: this.userId },
      { 
        fields: { 
          roles: 1, 
          emails: 1,
          profile: 1
        } 
      }
    );
  });
});

// Method to create a new user with role
Meteor.methods({
  'users.create'({ email, password, role, adminToken, firstName, lastName }) {
    // Validate admin token if trying to create admin account
    if (role === 'admin' && adminToken !== ADMIN_TOKEN) {
      throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');
    }

    // Validate password requirements
    const passwordRegex = {
      length: /.{8,}/,
      uppercase: /[A-Z]/,
      number: /[0-9]/,
      special: /[!@#$%^&*]/
    };

    const passwordErrors = [];
    if (!passwordRegex.length.test(password)) {
      passwordErrors.push('Password must be at least 8 characters long');
    }
    if (!passwordRegex.uppercase.test(password)) {
      passwordErrors.push('Password must contain at least one uppercase letter');
    }
    if (!passwordRegex.number.test(password)) {
      passwordErrors.push('Password must contain at least one number');
    }
    if (!passwordRegex.special.test(password)) {
      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
    }

    if (passwordErrors.length > 0) {
      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
    }

    // Create the user
    try {
      const userId = Accounts.createUser({
        email,
        password,
        role, // This will be used in onCreateUser callback
        profile: {
          role, // Store in profile as well for easy access
          firstName,
          lastName,
          fullName: `${firstName} ${lastName}`
        }
      });

      // Send verification email
      if (userId) {
        Accounts.sendVerificationEmail(userId);
      }

      return userId;
    } catch (error) {
      throw new Meteor.Error('create-user-failed', error.message);
    }
  },

  async 'users.getRole'() {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'User must be logged in');
    }
    
    try {
      const user = await Meteor.users.findOneAsync(this.userId);
      if (!user) {
        throw new Meteor.Error('user-not-found', 'User not found');
      }
      
      // Check both roles array and profile for role
      const role = user.roles?.[0] || user.profile?.role || 'team-member';
      return role;
    } catch (error) {
      throw new Meteor.Error('get-role-failed', error.message);
    }
  },

  'users.resendVerificationEmail'(email) {
    // Find user by email
    const user = Accounts.findUserByEmail(email);
    if (!user) {
      throw new Meteor.Error('user-not-found', 'No user found with this email address');
    }

    // Check if email is already verified
    const userEmail = user.emails[0];
    if (userEmail.verified) {
      throw new Meteor.Error('already-verified', 'This email is already verified');
    }

    // Send verification email
    try {
      Accounts.sendVerificationEmail(user._id, email);
      return true;
    } catch (error) {
      throw new Meteor.Error('verification-email-failed', error.message);
    }
  },

  'users.forgotPassword'(data) {
    check(data, {
      email: String,
      newPassword: String
    });

    const { email, newPassword } = data;
    console.log('[forgotPassword] Processing request for email:', email);

    // Find user by email using Accounts method (most reliable)
    const user = Accounts.findUserByEmail(email);
    if (!user) {
      throw new Meteor.Error('user-not-found', 'No user found with this email address');
    }

    console.log('[forgotPassword] User found:', user._id);

    // Validate password requirements
    const passwordRegex = {
      length: /.{8,}/,
      uppercase: /[A-Z]/,
      number: /[0-9]/,
      special: /[!@#$%^&*]/
    };

    const passwordErrors = [];
    if (!passwordRegex.length.test(newPassword)) {
      passwordErrors.push('Password must be at least 8 characters long');
    }
    if (!passwordRegex.uppercase.test(newPassword)) {
      passwordErrors.push('Password must contain at least one uppercase letter');
    }
    if (!passwordRegex.number.test(newPassword)) {
      passwordErrors.push('Password must contain at least one number');
    }
    if (!passwordRegex.special.test(newPassword)) {
      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
    }

    if (passwordErrors.length > 0) {
      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));
    }

    // Update password using the most reliable method
    try {
      // Create a temporary user to get the correct password hash structure
      const tempEmail = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}@temp.local`;
      const tempUserId = Accounts.createUser({
        email: tempEmail,
        password: newPassword
      });

      // Get the password hash from the temporary user
      const tempUser = Meteor.users.findOne(tempUserId);
      const passwordHash = tempUser.services.password;

      // Update the target user's password
      const updateResult = Meteor.users.update(user._id, {
        $set: {
          'services.password': passwordHash
        }
      });

      // Clean up temporary user
      Meteor.users.remove(tempUserId);

      if (updateResult === 1) {
        console.log(`[forgotPassword] Password reset successful for user: ${email}`);
        return { success: true, message: 'Password updated successfully' };
      } else {
        throw new Error('Password update failed');
      }
    } catch (error) {
      console.error('[forgotPassword] Error:', error);
      throw new Meteor.Error('password-update-failed', `Failed to update password: ${error.message}`);
    }
  },

  async 'users.checkAndFixAdminRole'() {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in');
    }
    
    try {
      const user = await Meteor.users.findOneAsync(this.userId);
      console.log('[checkAndFixAdminRole] Checking user:', {
        id: user?._id,
        email: user?.emails?.[0]?.address,
        roles: user?.roles
      });
      
      // If user has no roles array, initialize it
      if (!user.roles) {
        await Meteor.users.updateAsync(this.userId, {
          $set: { roles: ['team-member'] }
        });
        return 'Roles initialized';
      }
      
      // If user has no roles or doesn't have admin role
      if (!user.roles.includes('admin')) {
        console.log('[checkAndFixAdminRole] User is not admin, checking if first user');
        
        // Check if this is the first user (they should be admin)
        const totalUsers = await Meteor.users.find().countAsync();
        if (totalUsers === 1) {
          console.log('[checkAndFixAdminRole] First user, setting as admin');
          await Meteor.users.updateAsync(this.userId, {
            $set: { roles: ['admin'] }
          });
          return 'Admin role added';
        }
        return 'User is not admin';
      }
      
      return 'User is already admin';
    } catch (error) {
      console.error('[checkAndFixAdminRole] Error:', error);
      throw new Meteor.Error('check-role-failed', error.message);
    }
  },

  async 'users.diagnoseRoles'() {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'You must be logged in');
    }

    try {
      const currentUser = await Meteor.users.findOneAsync(this.userId);
      if (!currentUser.roles?.includes('admin')) {
        throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');
      }

      const allUsers = await Meteor.users.find().fetchAsync();
      const usersWithIssues = [];
      const fixes = [];

      for (const user of allUsers) {
        const issues = [];
        
        // Check if roles array exists
        if (!user.roles || !Array.isArray(user.roles)) {
          issues.push('No roles array');
          // Fix: Initialize roles based on profile
          const role = user.profile?.role || 'team-member';
          await Meteor.users.updateAsync(user._id, {
            $set: { roles: [role] }
          });
          fixes.push(`Initialized roles for ${user.emails?.[0]?.address}`);
        }
        
        // Check if role matches profile
        if (user.profile?.role && user.roles?.[0] !== user.profile.role) {
          issues.push('Role mismatch with profile');
          // Fix: Update roles to match profile
          await Meteor.users.updateAsync(user._id, {
            $set: { roles: [user.profile.role] }
          });
          fixes.push(`Fixed role mismatch for ${user.emails?.[0]?.address}`);
        }

        if (issues.length > 0) {
          usersWithIssues.push({
            email: user.emails?.[0]?.address,
            issues
          });
        }
      }

      return {
        usersWithIssues,
        fixes,
        message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'
      };
    } catch (error) {
      throw new Meteor.Error('diagnose-failed', error.message);
    }
  },

  'users.createTestTeamMember'() {
    // Only allow in development
    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
      try {
        const testMember = {
          email: '<EMAIL>',
          password: 'TestPass123!',
          firstName: 'Test',
          lastName: 'Member'
        };

        const userId = Accounts.createUser({
          email: testMember.email,
          password: testMember.password,
          profile: {
            firstName: testMember.firstName,
            lastName: testMember.lastName,
            role: 'team-member',
            fullName: `${testMember.firstName} ${testMember.lastName}`
          }
        });

        // Set the role explicitly
        Meteor.users.update(userId, {
          $set: { roles: ['team-member'] }
        });

        return {
          success: true,
          userId,
          message: 'Test team member created successfully'
        };
      } catch (error) {
        console.error('[createTestTeamMember] Error:', error);
        throw new Meteor.Error('create-test-member-failed', error.message);
      }
    } else {
      throw new Meteor.Error('not-development', 'This method is only available in development');
    }
  }
});