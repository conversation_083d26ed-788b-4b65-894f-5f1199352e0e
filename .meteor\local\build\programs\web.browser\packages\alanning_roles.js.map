{"version": 3, "sources": ["meteor://💻app/packages/alanning:roles/roles/roles_client.js", "meteor://💻app/packages/alanning:roles/roles/roles_common_async.js", "meteor://💻app/packages/alanning:roles/roles/client/debug.js", "meteor://💻app/packages/alanning:roles/roles/client/uiHelpers.js"], "names": ["Meteor", "module1", "link", "v", "Roles", "getGroupsForUserDeprecationWarning", "Object", "assign", "GLOBAL_GROUP", "createRole", "<PERSON><PERSON><PERSON>", "options", "_checkRoleName", "unlessExists", "result", "roles", "upsert", "_id", "$setOnInsert", "children", "insertedId", "Error", "deleteRole", "inheritedRoles", "roleAssignment", "remove", "_getParentRoleNames", "findOne", "r", "find", "$in", "fetch", "update", "$pull", "_getInheritedRoleNames", "$set", "map", "r2", "multi", "length", "renameRole", "old<PERSON>ame", "newName", "count", "role", "insert", "addRolesToParent", "rolesNames", "parentName", "Array", "isArray", "_addRoleToParent", "includes", "$ne", "$push", "$each", "removeRolesFromParent", "_removeRoleFromParent", "fields", "addUsersToRoles", "users", "id", "_normalizeOptions", "_checkScopeName", "scope", "ifExists", "user", "_addUserToRole", "setUserRoles", "anyScope", "selector", "userId", "res", "parentRoles", "Set", "for<PERSON>ach", "parentRole", "add", "delete", "nestedRoles", "removeUsersFromRoles", "_removeUserFromRole", "userIsInRole", "filter", "some", "limit", "getRolesForUser", "fullObjects", "onlyAssigned", "onlyScoped", "push", "reduce", "rev", "current", "concat", "getAllRoles", "queryOptions", "sort", "getUsersInRole", "ids", "getUserAssignmentsForRole", "a", "_getUsersInRoleCursor", "getGroupsForUser", "console", "warn", "getScopesForUser", "arguments", "scopes", "obi", "renameScope", "removeScope", "name", "trim", "isParentOf", "parentRoleName", "child<PERSON>ole<PERSON>ame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "undefined", "_normalizeScopeName", "scopeName", "call", "module", "export", "RolesCollection", "RoleAssignmentCollection", "Mongo", "Collection", "asyncSome", "arr", "predicate", "e", "createRoleAsync", "existingRole", "findOneAsync", "updateAsync", "insertAsync", "deleteRoleAsync", "removeAsync", "_getParentRoleNamesAsync", "fetchAsync", "_getInheritedRoleNamesAsync", "renameRoleAsync", "addRolesToParentAsync", "_addRoleToParentAsync", "removeRolesFromParentAsync", "_removeRoleFromParentAsync", "addUsersToRolesAsync", "_addUserToRoleAsync", "setUserRolesAsync", "existingAssignment", "removeUsersFromRolesAsync", "_removeUserFromRoleAsync", "userIsInRoleAsync", "out", "countDocuments", "getRolesForUserAsync", "getUsersInRoleAsync", "getGroupsForUserAsync", "getScopesForUserAsync", "renameScopeAsync", "removeScopeAsync", "isParentOfAsync", "debug", "localStorage", "temp", "getItem", "ex", "_uiHelpers", "isInRole", "comma", "indexOf", "Match", "test", "String", "split", "memo", "Package", "blaze", "Blaze", "registerHelper", "entries", "_ref", "func"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAIA,MAAM;EAACC,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;IAACF,MAAMA,CAACG,CAAC,EAAC;MAACH,MAAM,GAACG,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAGhE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEA;AACA;AACA;EACA,IAAI,OAAOC,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAG,CAAC,CAAC,EAAC;EACb;EAEA,IAAIC,kCAAkC,GAAG,KAAK;EAE9CC,MAAM,CAACC,MAAM,CAACH,KAAK,EAAE;IAEnB;AACF;AACA;AACA;AACA;AACA;AACA;IACEI,YAAY,EAAE,IAAI;IAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,UAAU,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,OAAO,EAAE;MACvCP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAE9BC,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtBM,YAAY,EAAE;MAChB,CAAC,EAAEF,OAAO,CAAC;MAEX,MAAMG,MAAM,GAAGd,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC;QAAEC,GAAG,EAAEP;MAAS,CAAC,EAAE;QAAEQ,YAAY,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE,CAAC,CAAC;MAEzF,IAAI,CAACL,MAAM,CAACM,UAAU,EAAE;QACtB,IAAIT,OAAO,CAACE,YAAY,EAAE,OAAO,IAAI;QACrC,MAAM,IAAIQ,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;MAC9D;MAEA,OAAOI,MAAM,CAACM,UAAU;IAC1B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEE,UAAU,EAAE,SAAAA,CAAUZ,QAAQ,EAAE;MAC9B,IAAIK,KAAK;MACT,IAAIQ,cAAc;MAElBnB,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;;MAE9B;MACAV,MAAM,CAACwB,cAAc,CAACC,MAAM,CAAC;QAC3B,UAAU,EAAEf;MACd,CAAC,CAAC;MAEF,GAAG;QACD;QACAK,KAAK,GAAGX,KAAK,CAACsB,mBAAmB,CAAC1B,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;UAAEV,GAAG,EAAEP;QAAS,CAAC,CAAC,CAAC;QAE1E,KAAK,MAAMkB,CAAC,IAAI5B,MAAM,CAACe,KAAK,CAACc,IAAI,CAAC;UAAEZ,GAAG,EAAE;YAAEa,GAAG,EAAEf;UAAM;QAAE,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAE;UAClE/B,MAAM,CAACe,KAAK,CAACiB,MAAM,CAAC;YAClBf,GAAG,EAAEW,CAAC,CAACX;UACT,CAAC,EAAE;YACDgB,KAAK,EAAE;cACLd,QAAQ,EAAE;gBACRF,GAAG,EAAEP;cACP;YACF;UACF,CAAC,CAAC;UAEFa,cAAc,GAAGnB,KAAK,CAAC8B,sBAAsB,CAAClC,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;YAAEV,GAAG,EAAEW,CAAC,CAACX;UAAI,CAAC,CAAC,CAAC;UACnFjB,MAAM,CAACwB,cAAc,CAACQ,MAAM,CAAC;YAC3B,UAAU,EAAEJ,CAAC,CAACX;UAChB,CAAC,EAAE;YACDkB,IAAI,EAAE;cACJZ,cAAc,EAAE,CAACK,CAAC,CAACX,GAAG,EAAE,GAAGM,cAAc,CAAC,CAACa,GAAG,CAACC,EAAE,KAAK;gBAAEpB,GAAG,EAAEoB;cAAG,CAAC,CAAC;YACpE;UACF,CAAC,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAC,CAAC;QACrB;MACF,CAAC,QAAQvB,KAAK,CAACwB,MAAM,GAAG,CAAC;;MAEzB;MACAvC,MAAM,CAACe,KAAK,CAACU,MAAM,CAAC;QAAER,GAAG,EAAEP;MAAS,CAAC,CAAC;IACxC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE8B,UAAU,EAAE,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;MACtC,IAAIC,KAAK;MAETvC,KAAK,CAACQ,cAAc,CAAC6B,OAAO,CAAC;MAC7BrC,KAAK,CAACQ,cAAc,CAAC8B,OAAO,CAAC;MAE7B,IAAID,OAAO,KAAKC,OAAO,EAAE;MAEzB,MAAME,IAAI,GAAG5C,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEwB;MAAQ,CAAC,CAAC;MAEnD,IAAI,CAACG,IAAI,EAAE;QACT,MAAM,IAAIvB,KAAK,CAAC,SAAS,GAAGoB,OAAO,GAAG,oBAAoB,CAAC;MAC7D;MAEAG,IAAI,CAAC3B,GAAG,GAAGyB,OAAO;MAElB1C,MAAM,CAACe,KAAK,CAAC8B,MAAM,CAACD,IAAI,CAAC;MAEzB,GAAG;QACDD,KAAK,GAAG3C,MAAM,CAACwB,cAAc,CAACQ,MAAM,CAAC;UACnC,UAAU,EAAES;QACd,CAAC,EAAE;UACDN,IAAI,EAAE;YACJ,UAAU,EAAEO;UACd;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB,GAAG;QACDA,KAAK,GAAG3C,MAAM,CAACwB,cAAc,CAACQ,MAAM,CAAC;UACnC,oBAAoB,EAAES;QACxB,CAAC,EAAE;UACDN,IAAI,EAAE;YACJ,sBAAsB,EAAEO;UAC1B;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB,GAAG;QACDA,KAAK,GAAG3C,MAAM,CAACe,KAAK,CAACiB,MAAM,CAAC;UAC1B,cAAc,EAAES;QAClB,CAAC,EAAE;UACDN,IAAI,EAAE;YACJ,gBAAgB,EAAEO;UACpB;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB3C,MAAM,CAACe,KAAK,CAACU,MAAM,CAAC;QAAER,GAAG,EAAEwB;MAAQ,CAAC,CAAC;IACvC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEK,gBAAgB,EAAE,SAAAA,CAAUC,UAAU,EAAEC,UAAU,EAAE;MAClD;MACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAEzD,KAAK,MAAMrC,QAAQ,IAAIqC,UAAU,EAAE;QACjC3C,KAAK,CAAC+C,gBAAgB,CAACzC,QAAQ,EAAEsC,UAAU,CAAC;MAC9C;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACEG,gBAAgB,EAAE,SAAAA,CAAUzC,QAAQ,EAAEsC,UAAU,EAAE;MAChD5C,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACQ,cAAc,CAACoC,UAAU,CAAC;;MAEhC;MACA,MAAMJ,IAAI,GAAG5C,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEP;MAAS,CAAC,CAAC;MAEpD,IAAI,CAACkC,IAAI,EAAE;QACT,MAAM,IAAIvB,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;MAC9D;;MAEA;MACA,IAAIN,KAAK,CAAC8B,sBAAsB,CAACU,IAAI,CAAC,CAACQ,QAAQ,CAACJ,UAAU,CAAC,EAAE;QAC3D,MAAM,IAAI3B,KAAK,CAAC,UAAU,GAAGX,QAAQ,GAAG,WAAW,GAAGsC,UAAU,GAAG,wBAAwB,CAAC;MAC9F;MAEA,MAAML,KAAK,GAAG3C,MAAM,CAACe,KAAK,CAACiB,MAAM,CAAC;QAChCf,GAAG,EAAE+B,UAAU;QACf,cAAc,EAAE;UACdK,GAAG,EAAET,IAAI,CAAC3B;QACZ;MACF,CAAC,EAAE;QACDqC,KAAK,EAAE;UACLnC,QAAQ,EAAE;YACRF,GAAG,EAAE2B,IAAI,CAAC3B;UACZ;QACF;MACF,CAAC,CAAC;;MAEF;MACA;MACA,IAAI,CAAC0B,KAAK,EAAE;MAEZ3C,MAAM,CAACwB,cAAc,CAACQ,MAAM,CAAC;QAC3B,oBAAoB,EAAEgB;MACxB,CAAC,EAAE;QACDM,KAAK,EAAE;UACL/B,cAAc,EAAE;YAAEgC,KAAK,EAAE,CAACX,IAAI,CAAC3B,GAAG,EAAE,GAAGb,KAAK,CAAC8B,sBAAsB,CAACU,IAAI,CAAC,CAAC,CAACR,GAAG,CAACR,CAAC,KAAK;cAAEX,GAAG,EAAEW;YAAE,CAAC,CAAC;UAAE;QACpG;MACF,CAAC,EAAE;QAAEU,KAAK,EAAE;MAAK,CAAC,CAAC;IACrB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEkB,qBAAqB,EAAE,SAAAA,CAAUT,UAAU,EAAEC,UAAU,EAAE;MACvD;MACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAEzD,KAAK,MAAMrC,QAAQ,IAAIqC,UAAU,EAAE;QACjC3C,KAAK,CAACqD,qBAAqB,CAAC/C,QAAQ,EAAEsC,UAAU,CAAC;MACnD;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACES,qBAAqB,EAAE,SAAAA,CAAU/C,QAAQ,EAAEsC,UAAU,EAAE;MACrD5C,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACQ,cAAc,CAACoC,UAAU,CAAC;;MAEhC;MACA;MACA,MAAMJ,IAAI,GAAG5C,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEP;MAAS,CAAC,EAAE;QAAEgD,MAAM,EAAE;UAAEzC,GAAG,EAAE;QAAE;MAAE,CAAC,CAAC;MAE5E,IAAI,CAAC2B,IAAI,EAAE;QACT,MAAM,IAAIvB,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;MAC9D;MAEA,MAAMiC,KAAK,GAAG3C,MAAM,CAACe,KAAK,CAACiB,MAAM,CAAC;QAChCf,GAAG,EAAE+B;MACP,CAAC,EAAE;QACDf,KAAK,EAAE;UACLd,QAAQ,EAAE;YACRF,GAAG,EAAE2B,IAAI,CAAC3B;UACZ;QACF;MACF,CAAC,CAAC;;MAEF;MACA;MACA,IAAI,CAAC0B,KAAK,EAAE;;MAEZ;MACA,MAAM5B,KAAK,GAAG,CAAC,GAAGX,KAAK,CAACsB,mBAAmB,CAAC1B,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAE+B;MAAW,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC;MAEnG,KAAK,MAAMpB,CAAC,IAAI5B,MAAM,CAACe,KAAK,CAACc,IAAI,CAAC;QAAEZ,GAAG,EAAE;UAAEa,GAAG,EAAEf;QAAM;MAAE,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAE;QAClE,MAAMR,cAAc,GAAGnB,KAAK,CAAC8B,sBAAsB,CAAClC,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;UAAEV,GAAG,EAAEW,CAAC,CAACX;QAAI,CAAC,CAAC,CAAC;QACzFjB,MAAM,CAACwB,cAAc,CAACQ,MAAM,CAAC;UAC3B,UAAU,EAAEJ,CAAC,CAACX,GAAG;UACjB,oBAAoB,EAAE2B,IAAI,CAAC3B;QAC7B,CAAC,EAAE;UACDkB,IAAI,EAAE;YACJZ,cAAc,EAAE,CAACK,CAAC,CAACX,GAAG,EAAE,GAAGM,cAAc,CAAC,CAACa,GAAG,CAACC,EAAE,KAAK;cAAEpB,GAAG,EAAEoB;YAAG,CAAC,CAAC;UACpE;QACF,CAAC,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEqB,eAAe,EAAE,SAAAA,CAAUC,KAAK,EAAE7C,KAAK,EAAEJ,OAAO,EAAE;MAChD,IAAIkD,EAAE;MAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIvC,KAAK,CAAC,0BAA0B,CAAC;MACvD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;MAEvDV,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACX,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB0D,QAAQ,EAAE;MACZ,CAAC,EAAEtD,OAAO,CAAC;MAEX,KAAK,MAAMuD,IAAI,IAAIN,KAAK,EAAE;QACxB,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;UAC5BL,EAAE,GAAGK,IAAI,CAACjD,GAAG;QACf,CAAC,MAAM;UACL4C,EAAE,GAAGK,IAAI;QACX;QAEA,KAAK,MAAMtB,IAAI,IAAI7B,KAAK,EAAE;UACxBX,KAAK,CAAC+D,cAAc,CAACN,EAAE,EAAEjB,IAAI,EAAEjC,OAAO,CAAC;QACzC;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEyD,YAAY,EAAE,SAAAA,CAAUR,KAAK,EAAE7C,KAAK,EAAEJ,OAAO,EAAE;MAC7C,IAAIkD,EAAE;MAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIvC,KAAK,CAAC,0BAA0B,CAAC;MACvD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;MAEvDV,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACX,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB0D,QAAQ,EAAE,KAAK;QACfI,QAAQ,EAAE;MACZ,CAAC,EAAE1D,OAAO,CAAC;MAEX,KAAK,MAAMuD,IAAI,IAAIN,KAAK,EAAE;QACxB,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;UAC5BL,EAAE,GAAGK,IAAI,CAACjD,GAAG;QACf,CAAC,MAAM;UACL4C,EAAE,GAAGK,IAAI;QACX;QACA;QACA,MAAMI,QAAQ,GAAG;UAAE,UAAU,EAAET;QAAG,CAAC;QACnC,IAAI,CAAClD,OAAO,CAAC0D,QAAQ,EAAE;UACrBC,QAAQ,CAACN,KAAK,GAAGrD,OAAO,CAACqD,KAAK;QAChC;QAEAhE,MAAM,CAACwB,cAAc,CAACC,MAAM,CAAC6C,QAAQ,CAAC;;QAEtC;QACA,KAAK,MAAM1B,IAAI,IAAI7B,KAAK,EAAE;UACxBX,KAAK,CAAC+D,cAAc,CAACN,EAAE,EAAEjB,IAAI,EAAEjC,OAAO,CAAC;QACzC;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEwD,cAAc,EAAE,SAAAA,CAAUI,MAAM,EAAE7D,QAAQ,EAAEC,OAAO,EAAE;MACnDP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpC,IAAI,CAACO,MAAM,EAAE;QACX;MACF;MAEA,MAAM3B,IAAI,GAAG5C,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;QAAEV,GAAG,EAAEP;MAAS,CAAC,EAAE;QAAEgD,MAAM,EAAE;UAAEvC,QAAQ,EAAE;QAAE;MAAE,CAAC,CAAC;MAEjF,IAAI,CAACyB,IAAI,EAAE;QACT,IAAIjC,OAAO,CAACsD,QAAQ,EAAE;UACpB,OAAO,EAAE;QACX,CAAC,MAAM;UACL,MAAM,IAAI5C,KAAK,CAAC,SAAS,GAAGX,QAAQ,GAAG,oBAAoB,CAAC;QAC9D;MACF;;MAEA;MACA,MAAM8D,GAAG,GAAGxE,MAAM,CAACwB,cAAc,CAACR,MAAM,CAAC;QACvC,UAAU,EAAEuD,MAAM;QAClB,UAAU,EAAE7D,QAAQ;QACpBsD,KAAK,EAAErD,OAAO,CAACqD;MACjB,CAAC,EAAE;QACD9C,YAAY,EAAE;UACZgD,IAAI,EAAE;YAAEjD,GAAG,EAAEsD;UAAO,CAAC;UACrB3B,IAAI,EAAE;YAAE3B,GAAG,EAAEP;UAAS,CAAC;UACvBsD,KAAK,EAAErD,OAAO,CAACqD;QACjB;MACF,CAAC,CAAC;MAEF,IAAIQ,GAAG,CAACpD,UAAU,EAAE;QAClBpB,MAAM,CAACwB,cAAc,CAACQ,MAAM,CAAC;UAAEf,GAAG,EAAEuD,GAAG,CAACpD;QAAW,CAAC,EAAE;UACpDe,IAAI,EAAE;YACJZ,cAAc,EAAE,CAACb,QAAQ,EAAE,GAAGN,KAAK,CAAC8B,sBAAsB,CAACU,IAAI,CAAC,CAAC,CAACR,GAAG,CAACR,CAAC,KAAK;cAAEX,GAAG,EAAEW;YAAE,CAAC,CAAC;UACzF;QACF,CAAC,CAAC;MACJ;MAEA,OAAO4C,GAAG;IACZ,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE9C,mBAAmB,EAAE,SAAAA,CAAUkB,IAAI,EAAE;MACnC,IAAI,CAACA,IAAI,EAAE;QACT,OAAO,EAAE;MACX;MAEA,MAAM6B,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC9B,IAAI,CAAC3B,GAAG,CAAC,CAAC;MAEvC,KAAK,MAAMP,QAAQ,IAAI+D,WAAW,EAAE;QAClCzE,MAAM,CAACe,KAAK,CAACc,IAAI,CAAC;UAAE,cAAc,EAAEnB;QAAS,CAAC,CAAC,CAACqB,KAAK,CAAC,CAAC,CAAC4C,OAAO,CAACC,UAAU,IAAI;UAC5EH,WAAW,CAACI,GAAG,CAACD,UAAU,CAAC3D,GAAG,CAAC;QACjC,CAAC,CAAC;MACJ;MAEAwD,WAAW,CAACK,MAAM,CAAClC,IAAI,CAAC3B,GAAG,CAAC;MAE5B,OAAO,CAAC,GAAGwD,WAAW,CAAC;IACzB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEvC,sBAAsB,EAAE,SAAAA,CAAUU,IAAI,EAAE;MACtC,MAAMrB,cAAc,GAAG,IAAImD,GAAG,CAAC,CAAC;MAChC,MAAMK,WAAW,GAAG,IAAIL,GAAG,CAAC,CAAC9B,IAAI,CAAC,CAAC;MAEnC,KAAK,MAAMhB,CAAC,IAAImD,WAAW,EAAE;QAC3B,MAAMhE,KAAK,GAAGf,MAAM,CAACe,KAAK,CAACc,IAAI,CAAC;UAAEZ,GAAG,EAAE;YAAEa,GAAG,EAAEF,CAAC,CAACT,QAAQ,CAACiB,GAAG,CAACR,CAAC,IAAIA,CAAC,CAACX,GAAG;UAAE;QAAE,CAAC,EAAE;UAAEyC,MAAM,EAAE;YAAEvC,QAAQ,EAAE;UAAE;QAAE,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC;QAElH,KAAK,MAAMM,EAAE,IAAItB,KAAK,EAAE;UACtBQ,cAAc,CAACsD,GAAG,CAACxC,EAAE,CAACpB,GAAG,CAAC;UAC1B8D,WAAW,CAACF,GAAG,CAACxC,EAAE,CAAC;QACrB;MACF;MAEA,OAAO,CAAC,GAAGd,cAAc,CAAC;IAC5B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEyD,oBAAoB,EAAE,SAAAA,CAAUpB,KAAK,EAAE7C,KAAK,EAAEJ,OAAO,EAAE;MACrD,IAAI,CAACiD,KAAK,EAAE,MAAM,IAAIvC,KAAK,CAAC,0BAA0B,CAAC;MACvD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;MAEvDV,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACX,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpC,KAAK,MAAME,IAAI,IAAIN,KAAK,EAAE;QACxB,IAAI,CAACM,IAAI,EAAE;QAEX,KAAK,MAAMtB,IAAI,IAAI7B,KAAK,EAAE;UACxB,IAAI8C,EAAE;UACN,IAAI,OAAOK,IAAI,KAAK,QAAQ,EAAE;YAC5BL,EAAE,GAAGK,IAAI,CAACjD,GAAG;UACf,CAAC,MAAM;YACL4C,EAAE,GAAGK,IAAI;UACX;UAEA9D,KAAK,CAAC6E,mBAAmB,CAACpB,EAAE,EAAEjB,IAAI,EAAEjC,OAAO,CAAC;QAC9C;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEsE,mBAAmB,EAAE,SAAAA,CAAUV,MAAM,EAAE7D,QAAQ,EAAEC,OAAO,EAAE;MACxDP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpC,IAAI,CAACO,MAAM,EAAE;MAEb,MAAMD,QAAQ,GAAG;QACf,UAAU,EAAEC,MAAM;QAClB,UAAU,EAAE7D;MACd,CAAC;MAED,IAAI,CAACC,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAGrD,OAAO,CAACqD,KAAK;MAChC;MAEAhE,MAAM,CAACwB,cAAc,CAACC,MAAM,CAAC6C,QAAQ,CAAC;IACxC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEY,YAAY,EAAE,SAAAA,CAAUhB,IAAI,EAAEnD,KAAK,EAAEJ,OAAO,EAAE;MAC5C,IAAIkD,EAAE;MACNlD,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CA,KAAK,GAAGA,KAAK,CAACoE,MAAM,CAACvD,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC;MAEpC,IAAI,CAACb,KAAK,CAACwB,MAAM,EAAE,OAAO,KAAK;MAE/BnC,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB8D,QAAQ,EAAE;MACZ,CAAC,EAAE1D,OAAO,CAAC;MAEX,IAAIuD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACpCL,EAAE,GAAGK,IAAI,CAACjD,GAAG;MACf,CAAC,MAAM;QACL4C,EAAE,GAAGK,IAAI;MACX;MAEA,IAAI,CAACL,EAAE,EAAE,OAAO,KAAK;MACrB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAO,KAAK;MAExC,MAAMS,QAAQ,GAAG;QAAE,UAAU,EAAET;MAAG,CAAC;MAEnC,IAAI,CAAClD,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAG;UAAElC,GAAG,EAAE,CAACnB,OAAO,CAACqD,KAAK,EAAE,IAAI;QAAE,CAAC;MACjD;MAEA,OAAOjD,KAAK,CAACqE,IAAI,CAAE1E,QAAQ,IAAK;QAC9B4D,QAAQ,CAAC,oBAAoB,CAAC,GAAG5D,QAAQ;QAEzC,OAAOV,MAAM,CAACwB,cAAc,CAACK,IAAI,CAACyC,QAAQ,EAAE;UAAEe,KAAK,EAAE;QAAE,CAAC,CAAC,CAAC1C,KAAK,CAAC,CAAC,GAAG,CAAC;MACvE,CAAC,CAAC;IACJ,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE2C,eAAe,EAAE,SAAAA,CAAUpB,IAAI,EAAEvD,OAAO,EAAE;MACxC,IAAIkD,EAAE;MAENlD,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;MAE1CP,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtBgF,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,KAAK;QACnBnB,QAAQ,EAAE,KAAK;QACfoB,UAAU,EAAE;MACd,CAAC,EAAE9E,OAAO,CAAC;MAEX,IAAIuD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACpCL,EAAE,GAAGK,IAAI,CAACjD,GAAG;MACf,CAAC,MAAM;QACL4C,EAAE,GAAGK,IAAI;MACX;MAEA,IAAI,CAACL,EAAE,EAAE,OAAO,EAAE;MAElB,MAAMS,QAAQ,GAAG;QAAE,UAAU,EAAET;MAAG,CAAC;MACnC,MAAMsB,MAAM,GAAG;QAAEzB,MAAM,EAAE;UAAE,oBAAoB,EAAE;QAAE;MAAE,CAAC;MAEtD,IAAI,CAAC/C,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAG;UAAElC,GAAG,EAAE,CAACnB,OAAO,CAACqD,KAAK;QAAE,CAAC;QAEzC,IAAI,CAACrD,OAAO,CAAC8E,UAAU,EAAE;UACvBnB,QAAQ,CAACN,KAAK,CAAClC,GAAG,CAAC4D,IAAI,CAAC,IAAI,CAAC;QAC/B;MACF;MAEA,IAAI/E,OAAO,CAAC6E,YAAY,EAAE;QACxB,OAAOL,MAAM,CAACzB,MAAM,CAAC,oBAAoB,CAAC;QAC1CyB,MAAM,CAACzB,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;MAC/B;MAEA,IAAI/C,OAAO,CAAC4E,WAAW,EAAE;QACvB,OAAOJ,MAAM,CAACzB,MAAM;MACtB;MAEA,MAAM3C,KAAK,GAAGf,MAAM,CAACwB,cAAc,CAACK,IAAI,CAACyC,QAAQ,EAAEa,MAAM,CAAC,CAACpD,KAAK,CAAC,CAAC;MAElE,IAAIpB,OAAO,CAAC4E,WAAW,EAAE;QACvB,OAAOxE,KAAK;MACd;MAEA,OAAO,CAAC,GAAG,IAAI2D,GAAG,CAAC3D,KAAK,CAAC4E,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;QAChD,IAAIA,OAAO,CAACtE,cAAc,EAAE;UAC1B,OAAOqE,GAAG,CAACE,MAAM,CAACD,OAAO,CAACtE,cAAc,CAACa,GAAG,CAACR,CAAC,IAAIA,CAAC,CAACX,GAAG,CAAC,CAAC;QAC3D,CAAC,MAAM,IAAI4E,OAAO,CAACjD,IAAI,EAAE;UACvBgD,GAAG,CAACF,IAAI,CAACG,OAAO,CAACjD,IAAI,CAAC3B,GAAG,CAAC;QAC5B;QACA,OAAO2E,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,WAAW,EAAE,SAAAA,CAAUC,YAAY,EAAE;MACnCA,YAAY,GAAGA,YAAY,IAAI;QAAEC,IAAI,EAAE;UAAEhF,GAAG,EAAE;QAAE;MAAE,CAAC;MAEnD,OAAOjB,MAAM,CAACe,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,EAAEmE,YAAY,CAAC;IAC5C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEE,cAAc,EAAE,SAAAA,CAAUnF,KAAK,EAAEJ,OAAO,EAAEqF,YAAY,EAAE;MACtD,MAAMG,GAAG,GAAG/F,KAAK,CAACgG,yBAAyB,CAACrF,KAAK,EAAEJ,OAAO,CAAC,CAACoB,KAAK,CAAC,CAAC,CAACK,GAAG,CAACiE,CAAC,IAAIA,CAAC,CAACnC,IAAI,CAACjD,GAAG,CAAC;MAExF,OAAOjB,MAAM,CAAC4D,KAAK,CAAC/B,IAAI,CAAC;QAAEZ,GAAG,EAAE;UAAEa,GAAG,EAAEqE;QAAI;MAAE,CAAC,EAAIxF,OAAO,IAAIA,OAAO,CAACqF,YAAY,IAAKA,YAAY,IAAK,CAAC,CAAC,CAAC;IAC5G,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEI,yBAAyB,EAAE,SAAAA,CAAUrF,KAAK,EAAEJ,OAAO,EAAE;MACnDA,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB8D,QAAQ,EAAE,KAAK;QACf2B,YAAY,EAAE,CAAC;MACjB,CAAC,EAAErF,OAAO,CAAC;MAEX,OAAOP,KAAK,CAACkG,qBAAqB,CAACvF,KAAK,EAAEJ,OAAO,EAAEA,OAAO,CAACqF,YAAY,CAAC;IAC1E,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEM,qBAAqB,EAAE,SAAAA,CAAUvF,KAAK,EAAEJ,OAAO,EAAEwE,MAAM,EAAE;MACvDxE,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtB8D,QAAQ,EAAE,KAAK;QACfoB,UAAU,EAAE;MACd,CAAC,EAAE9E,OAAO,CAAC;;MAEX;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCmB,MAAM,GAAG7E,MAAM,CAACC,MAAM,CAAC;QACrBmD,MAAM,EAAE;UAAE,UAAU,EAAE;QAAE;MAC1B,CAAC,EAAEyB,MAAM,CAAC;MAEV,MAAMb,QAAQ,GAAG;QAAE,oBAAoB,EAAE;UAAExC,GAAG,EAAEf;QAAM;MAAE,CAAC;MAEzD,IAAI,CAACJ,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAG;UAAElC,GAAG,EAAE,CAACnB,OAAO,CAACqD,KAAK;QAAE,CAAC;QAEzC,IAAI,CAACrD,OAAO,CAAC8E,UAAU,EAAE;UACvBnB,QAAQ,CAACN,KAAK,CAAClC,GAAG,CAAC4D,IAAI,CAAC,IAAI,CAAC;QAC/B;MACF;MAEA,OAAO1F,MAAM,CAACwB,cAAc,CAACK,IAAI,CAACyC,QAAQ,EAAEa,MAAM,CAAC;IACrD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACEoB,gBAAgB,EAAE,SAAAA,CAAA,EAAmB;MACnC,IAAI,CAAClG,kCAAkC,EAAE;QACvCA,kCAAkC,GAAG,IAAI;QACzCmG,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,qEAAqE,CAAC;MAChG;MAEA,OAAOrG,KAAK,CAACsG,gBAAgB,CAAC,GAAAC,SAAO,CAAC;IACxC,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACED,gBAAgB,EAAE,SAAAA,CAAUxC,IAAI,EAAEnD,KAAK,EAAE;MACvC,IAAI8C,EAAE;MAEN,IAAI9C,KAAK,IAAI,CAACkC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAEnD,IAAImD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACpCL,EAAE,GAAGK,IAAI,CAACjD,GAAG;MACf,CAAC,MAAM;QACL4C,EAAE,GAAGK,IAAI;MACX;MAEA,IAAI,CAACL,EAAE,EAAE,OAAO,EAAE;MAElB,MAAMS,QAAQ,GAAG;QACf,UAAU,EAAET,EAAE;QACdG,KAAK,EAAE;UAAEX,GAAG,EAAE;QAAK;MACrB,CAAC;MAED,IAAItC,KAAK,EAAE;QACTuD,QAAQ,CAAC,oBAAoB,CAAC,GAAG;UAAExC,GAAG,EAAEf;QAAM,CAAC;MACjD;MAEA,MAAM6F,MAAM,GAAG5G,MAAM,CAACwB,cAAc,CAACK,IAAI,CAACyC,QAAQ,EAAE;QAAEZ,MAAM,EAAE;UAAEM,KAAK,EAAE;QAAE;MAAE,CAAC,CAAC,CAACjC,KAAK,CAAC,CAAC,CAACK,GAAG,CAACyE,GAAG,IAAIA,GAAG,CAAC7C,KAAK,CAAC;MAE3G,OAAO,CAAC,GAAG,IAAIU,GAAG,CAACkC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEE,WAAW,EAAE,SAAAA,CAAUrE,OAAO,EAAEC,OAAO,EAAE;MACvC,IAAIC,KAAK;MAETvC,KAAK,CAAC2D,eAAe,CAACtB,OAAO,CAAC;MAC9BrC,KAAK,CAAC2D,eAAe,CAACrB,OAAO,CAAC;MAE9B,IAAID,OAAO,KAAKC,OAAO,EAAE;MAEzB,GAAG;QACDC,KAAK,GAAG3C,MAAM,CAACwB,cAAc,CAACQ,MAAM,CAAC;UACnCgC,KAAK,EAAEvB;QACT,CAAC,EAAE;UACDN,IAAI,EAAE;YACJ6B,KAAK,EAAEtB;UACT;QACF,CAAC,EAAE;UAAEJ,KAAK,EAAE;QAAK,CAAC,CAAC;MACrB,CAAC,QAAQK,KAAK,GAAG,CAAC;IACpB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEoE,WAAW,EAAE,SAAAA,CAAUC,IAAI,EAAE;MAC3B5G,KAAK,CAAC2D,eAAe,CAACiD,IAAI,CAAC;MAE3BhH,MAAM,CAACwB,cAAc,CAACC,MAAM,CAAC;QAAEuC,KAAK,EAAEgD;MAAK,CAAC,CAAC;IAC/C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEpG,cAAc,EAAE,SAAAA,CAAUF,QAAQ,EAAE;MAClC,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACuG,IAAI,CAAC,CAAC,KAAKvG,QAAQ,EAAE;QAC7E,MAAM,IAAIW,KAAK,CAAC,sBAAsB,GAAGX,QAAQ,GAAG,KAAK,CAAC;MAC5D;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEwG,UAAU,EAAE,SAAAA,CAAUC,cAAc,EAAEC,aAAa,EAAE;MACnD,IAAID,cAAc,KAAKC,aAAa,EAAE;QACpC,OAAO,IAAI;MACb;MAEA,IAAID,cAAc,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;QACnD,OAAO,KAAK;MACd;MAEAhH,KAAK,CAACQ,cAAc,CAACuG,cAAc,CAAC;MACpC/G,KAAK,CAACQ,cAAc,CAACwG,aAAa,CAAC;MAEnC,IAAIC,YAAY,GAAG,CAACF,cAAc,CAAC;MACnC,OAAOE,YAAY,CAAC9E,MAAM,KAAK,CAAC,EAAE;QAChC,MAAM7B,QAAQ,GAAG2G,YAAY,CAACC,GAAG,CAAC,CAAC;QAEnC,IAAI5G,QAAQ,KAAK0G,aAAa,EAAE;UAC9B,OAAO,IAAI;QACb;QAEA,MAAMxE,IAAI,GAAG5C,MAAM,CAACe,KAAK,CAACY,OAAO,CAAC;UAAEV,GAAG,EAAEP;QAAS,CAAC,CAAC;;QAEpD;QACA,IAAI,CAACkC,IAAI,EAAE;QAEXyE,YAAY,GAAGA,YAAY,CAACvB,MAAM,CAAClD,IAAI,CAACzB,QAAQ,CAACiB,GAAG,CAACR,CAAC,IAAIA,CAAC,CAACX,GAAG,CAAC,CAAC;MACnE;MAEA,OAAO,KAAK;IACd,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE6C,iBAAiB,EAAE,SAAAA,CAAUnD,OAAO,EAAE;MACpCA,OAAO,GAAGA,OAAO,KAAK4G,SAAS,GAAG,CAAC,CAAC,GAAG5G,OAAO;MAE9C,IAAIA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACnDA,OAAO,GAAG;UAAEqD,KAAK,EAAErD;QAAQ,CAAC;MAC9B;MAEAA,OAAO,CAACqD,KAAK,GAAG5D,KAAK,CAACoH,mBAAmB,CAAC7G,OAAO,CAACqD,KAAK,CAAC;MAExD,OAAOrD,OAAO;IAChB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE6G,mBAAmB,EAAE,SAAAA,CAAUC,SAAS,EAAE;MACxC;MACA,IAAIA,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOA,SAAS;MAClB;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE1D,eAAe,EAAE,SAAAA,CAAU0D,SAAS,EAAE;MACpC,IAAIA,SAAS,KAAK,IAAI,EAAE;MAExB,IACE,CAACA,SAAS,IACV,OAAOA,SAAS,KAAK,QAAQ,IAC7BA,SAAS,CAACR,IAAI,CAAC,CAAC,KAAKQ,SAAS,EAC9B;QACA,MAAM,IAAIpG,KAAK,wBAAAyE,MAAA,CAAwB2B,SAAS,OAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;AAAA,EAAAC,IAAA,OAAAC,MAAA,E;;;;;;;;;;;;EC7kCF1H,OAAO,CAAC2H,MAAM,CAAC;IAACC,eAAe,EAACA,CAAA,KAAIA,eAAe;IAACC,wBAAwB,EAACA,CAAA,KAAIA;EAAwB,CAAC,CAAC;EAAC,IAAI9H,MAAM;EAACC,OAAO,CAACC,IAAI,CAAC,eAAe,EAAC;IAACF,MAAMA,CAACG,CAAC,EAAC;MAACH,MAAM,GAACG,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EAAC,IAAI4H,KAAK;EAAC9H,OAAO,CAACC,IAAI,CAAC,cAAc,EAAC;IAAC6H,KAAKA,CAAC5H,CAAC,EAAC;MAAC4H,KAAK,GAAC5H,CAAC;IAAA;EAAC,CAAC,EAAC,CAAC,CAAC;EA4BlO,MAAM0H,eAAe,GAAG,IAAIE,KAAK,CAACC,UAAU,CAAC,OAAO,CAAC;EAE5D,IAAI,CAAChI,MAAM,CAACe,KAAK,EAAE;IACjBf,MAAM,CAACe,KAAK,GAAG8G,eAAe;EAChC;EAEO,MAAMC,wBAAwB,GAAG,IAAIC,KAAK,CAACC,UAAU,CAAC,iBAAiB,CAAC;EAE/E,IAAI,CAAChI,MAAM,CAACwB,cAAc,EAAE;IAC1BxB,MAAM,CAACwB,cAAc,GAAGsG,wBAAwB;EAClD;;EAEA;AACA;AACA;EACA,IAAI,OAAO1H,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAG,CAAC,CAAC,EAAC;EACb;EAEA,IAAIC,kCAAkC,GAAG,KAAK;;EAE9C;AACA;AACA;AACA;AACA;AACA;EACA,MAAM4H,SAAS,GAAG,MAAAA,CAAOC,GAAG,EAAEC,SAAS,KAAK;IAC1C,KAAK,MAAMC,CAAC,IAAIF,GAAG,EAAE;MACnB,IAAI,MAAMC,SAAS,CAACC,CAAC,CAAC,EAAE,OAAO,IAAI;IACrC;IACA,OAAO,KAAK;EACd,CAAC;EAED9H,MAAM,CAACC,MAAM,CAACH,KAAK,EAAE;IACnB;AACF;AACA;AACA;AACA;AACA;AACA;IACEI,YAAY,EAAE,IAAI;IAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE6H,eAAe,EAAE,eAAAA,CAAgB3H,QAAQ,EAAEC,OAAO,EAAE;MAClDP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAE9BC,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACEM,YAAY,EAAE;MAChB,CAAC,EACDF,OACF,CAAC;MAED,IAAIS,UAAU,GAAG,IAAI;MAErB,MAAMkH,YAAY,GAAG,MAAMtI,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;QAAEtH,GAAG,EAAEP;MAAS,CAAC,CAAC;MAEvE,IAAI4H,YAAY,EAAE;QAChB,MAAMtI,MAAM,CAACe,KAAK,CAACyH,WAAW,CAC5B;UAAEvH,GAAG,EAAEP;QAAS,CAAC,EACjB;UAAEQ,YAAY,EAAE;YAAEC,QAAQ,EAAE;UAAG;QAAE,CACnC,CAAC;QACD,OAAO,IAAI;MACb,CAAC,MAAM;QACLC,UAAU,GAAG,MAAMpB,MAAM,CAACe,KAAK,CAAC0H,WAAW,CAAC;UAC1CxH,GAAG,EAAEP,QAAQ;UACbS,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;MAEA,IAAI,CAACC,UAAU,EAAE;QACf,IAAIT,OAAO,CAACE,YAAY,EAAE,OAAO,IAAI;QACrC,MAAM,IAAIQ,KAAK,CAAC,QAAQ,GAAGX,QAAQ,GAAG,mBAAmB,CAAC;MAC5D;MAEA,OAAOU,UAAU;IACnB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEsH,eAAe,EAAE,eAAAA,CAAgBhI,QAAQ,EAAE;MACzC,IAAIK,KAAK;MACT,IAAIQ,cAAc;MAElBnB,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;;MAE9B;MACA,MAAMV,MAAM,CAACwB,cAAc,CAACmH,WAAW,CAAC;QACtC,UAAU,EAAEjI;MACd,CAAC,CAAC;MAEF,GAAG;QACD;QACAK,KAAK,GAAG,MAAMX,KAAK,CAACwI,wBAAwB,CAC1C,MAAM5I,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;UAAEtH,GAAG,EAAEP;QAAS,CAAC,CACnD,CAAC;QAED,KAAK,MAAMkB,CAAC,IAAI,MAAM5B,MAAM,CAACe,KAAK,CAC/Bc,IAAI,CAAC;UAAEZ,GAAG,EAAE;YAAEa,GAAG,EAAEf;UAAM;QAAE,CAAC,CAAC,CAC7B8H,UAAU,CAAC,CAAC,EAAE;UACf,MAAM7I,MAAM,CAACe,KAAK,CAACyH,WAAW,CAC5B;YACEvH,GAAG,EAAEW,CAAC,CAACX;UACT,CAAC,EACD;YACEgB,KAAK,EAAE;cACLd,QAAQ,EAAE;gBACRF,GAAG,EAAEP;cACP;YACF;UACF,CACF,CAAC;UAEDa,cAAc,GAAG,MAAMnB,KAAK,CAAC0I,2BAA2B,CACtD,MAAM9I,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;YAAEtH,GAAG,EAAEW,CAAC,CAACX;UAAI,CAAC,CAChD,CAAC;UACD,MAAMjB,MAAM,CAACwB,cAAc,CAACgH,WAAW,CACrC;YACE,UAAU,EAAE5G,CAAC,CAACX;UAChB,CAAC,EACD;YACEkB,IAAI,EAAE;cACJZ,cAAc,EAAE,CAACK,CAAC,CAACX,GAAG,EAAE,GAAGM,cAAc,CAAC,CAACa,GAAG,CAAEC,EAAE,KAAM;gBACtDpB,GAAG,EAAEoB;cACP,CAAC,CAAC;YACJ;UACF,CAAC,EACD;YAAEC,KAAK,EAAE;UAAK,CAChB,CAAC;QACH;MACF,CAAC,QAAQvB,KAAK,CAACwB,MAAM,GAAG,CAAC;;MAEzB;MACA,MAAMvC,MAAM,CAACe,KAAK,CAAC4H,WAAW,CAAC;QAAE1H,GAAG,EAAEP;MAAS,CAAC,CAAC;IACnD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEqI,eAAe,EAAE,eAAAA,CAAgBtG,OAAO,EAAEC,OAAO,EAAE;MACjD,IAAIC,KAAK;MAETvC,KAAK,CAACQ,cAAc,CAAC6B,OAAO,CAAC;MAC7BrC,KAAK,CAACQ,cAAc,CAAC8B,OAAO,CAAC;MAE7B,IAAID,OAAO,KAAKC,OAAO,EAAE;MAEzB,MAAME,IAAI,GAAG,MAAM5C,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;QAAEtH,GAAG,EAAEwB;MAAQ,CAAC,CAAC;MAE9D,IAAI,CAACG,IAAI,EAAE;QACT,MAAM,IAAIvB,KAAK,CAAC,QAAQ,GAAGoB,OAAO,GAAG,mBAAmB,CAAC;MAC3D;MAEAG,IAAI,CAAC3B,GAAG,GAAGyB,OAAO;MAElB,MAAM1C,MAAM,CAACe,KAAK,CAAC0H,WAAW,CAAC7F,IAAI,CAAC;MAEpC,GAAG;QACDD,KAAK,GAAG,MAAM3C,MAAM,CAACwB,cAAc,CAACgH,WAAW,CAC7C;UACE,UAAU,EAAE/F;QACd,CAAC,EACD;UACEN,IAAI,EAAE;YACJ,UAAU,EAAEO;UACd;QACF,CAAC,EACD;UAAEJ,KAAK,EAAE;QAAK,CAChB,CAAC;MACH,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB,GAAG;QACDA,KAAK,GAAG,MAAM3C,MAAM,CAACwB,cAAc,CAACgH,WAAW,CAC7C;UACE,oBAAoB,EAAE/F;QACxB,CAAC,EACD;UACEN,IAAI,EAAE;YACJ,sBAAsB,EAAEO;UAC1B;QACF,CAAC,EACD;UAAEJ,KAAK,EAAE;QAAK,CAChB,CAAC;MACH,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB,GAAG;QACDA,KAAK,GAAG,MAAM3C,MAAM,CAACe,KAAK,CAACyH,WAAW,CACpC;UACE,cAAc,EAAE/F;QAClB,CAAC,EACD;UACEN,IAAI,EAAE;YACJ,gBAAgB,EAAEO;UACpB;QACF,CAAC,EACD;UAAEJ,KAAK,EAAE;QAAK,CAChB,CAAC;MACH,CAAC,QAAQK,KAAK,GAAG,CAAC;MAElB,MAAM3C,MAAM,CAACe,KAAK,CAAC4H,WAAW,CAAC;QAAE1H,GAAG,EAAEwB;MAAQ,CAAC,CAAC;IAClD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEuG,qBAAqB,EAAE,eAAAA,CAAgBjG,UAAU,EAAEC,UAAU,EAAE;MAC7D;MACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAEzD,KAAK,MAAMrC,QAAQ,IAAIqC,UAAU,EAAE;QACjC,MAAM3C,KAAK,CAAC6I,qBAAqB,CAACvI,QAAQ,EAAEsC,UAAU,CAAC;MACzD;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEiG,qBAAqB,EAAE,eAAAA,CAAgBvI,QAAQ,EAAEsC,UAAU,EAAE;MAC3D5C,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACQ,cAAc,CAACoC,UAAU,CAAC;;MAEhC;MACA,MAAMJ,IAAI,GAAG,MAAM5C,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;QAAEtH,GAAG,EAAEP;MAAS,CAAC,CAAC;MAE/D,IAAI,CAACkC,IAAI,EAAE;QACT,MAAM,IAAIvB,KAAK,UAAAyE,MAAA,CAAUpF,QAAQ,sBAAmB,CAAC;MACvD;;MAEA;MACA,IAAI,CAAC,MAAMN,KAAK,CAAC0I,2BAA2B,CAAClG,IAAI,CAAC,EAAEQ,QAAQ,CAACJ,UAAU,CAAC,EAAE;QACxE,MAAM,IAAI3B,KAAK,WAAAyE,MAAA,CACHpF,QAAQ,aAAAoF,MAAA,CAAU9C,UAAU,0BACxC,CAAC;MACH;MAEA,MAAML,KAAK,GAAG,MAAM3C,MAAM,CAACe,KAAK,CAACyH,WAAW,CAC1C;QACEvH,GAAG,EAAE+B,UAAU;QACf,cAAc,EAAE;UACdK,GAAG,EAAET,IAAI,CAAC3B;QACZ;MACF,CAAC,EACD;QACEqC,KAAK,EAAE;UACLnC,QAAQ,EAAE;YACRF,GAAG,EAAE2B,IAAI,CAAC3B;UACZ;QACF;MACF,CACF,CAAC;;MAED;MACA;MACA,IAAI,CAAC0B,KAAK,EAAE;MAEZ,MAAM3C,MAAM,CAACwB,cAAc,CAACgH,WAAW,CACrC;QACE,oBAAoB,EAAExF;MACxB,CAAC,EACD;QACEM,KAAK,EAAE;UACL/B,cAAc,EAAE;YACdgC,KAAK,EAAE,CACLX,IAAI,CAAC3B,GAAG,EACR,IAAI,MAAMb,KAAK,CAAC0I,2BAA2B,CAAClG,IAAI,CAAC,CAAC,CACnD,CAACR,GAAG,CAAER,CAAC,KAAM;cAAEX,GAAG,EAAEW;YAAE,CAAC,CAAC;UAC3B;QACF;MACF,CAAC,EACD;QAAEU,KAAK,EAAE;MAAK,CAChB,CAAC;IACH,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE4G,0BAA0B,EAAE,eAAAA,CAAgBnG,UAAU,EAAEC,UAAU,EAAE;MAClE;MACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;MAEzD,KAAK,MAAMrC,QAAQ,IAAIqC,UAAU,EAAE;QACjC,MAAM3C,KAAK,CAAC+I,0BAA0B,CAACzI,QAAQ,EAAEsC,UAAU,CAAC;MAC9D;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEmG,0BAA0B,EAAE,eAAAA,CAAgBzI,QAAQ,EAAEsC,UAAU,EAAE;MAChE5C,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAACQ,cAAc,CAACoC,UAAU,CAAC;;MAEhC;MACA;MACA,MAAMJ,IAAI,GAAG,MAAM5C,MAAM,CAACe,KAAK,CAACwH,YAAY,CAC1C;QAAEtH,GAAG,EAAEP;MAAS,CAAC,EACjB;QAAEgD,MAAM,EAAE;UAAEzC,GAAG,EAAE;QAAE;MAAE,CACvB,CAAC;MAED,IAAI,CAAC2B,IAAI,EAAE;QACT,MAAM,IAAIvB,KAAK,UAAAyE,MAAA,CAAUpF,QAAQ,sBAAmB,CAAC;MACvD;MAEA,MAAMiC,KAAK,GAAG,MAAM3C,MAAM,CAACe,KAAK,CAACyH,WAAW,CAC1C;QACEvH,GAAG,EAAE+B;MACP,CAAC,EACD;QACEf,KAAK,EAAE;UACLd,QAAQ,EAAE;YACRF,GAAG,EAAE2B,IAAI,CAAC3B;UACZ;QACF;MACF,CACF,CAAC;;MAED;MACA;MACA,IAAI,CAAC0B,KAAK,EAAE;;MAEZ;MACA,MAAM5B,KAAK,GAAG,CACZ,IAAI,MAAMX,KAAK,CAACwI,wBAAwB,CACtC,MAAM5I,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;QAAEtH,GAAG,EAAE+B;MAAW,CAAC,CACrD,CAAC,CAAC,EACFA,UAAU,CACX;MAED,KAAK,MAAMpB,CAAC,IAAI,MAAM5B,MAAM,CAACe,KAAK,CAC/Bc,IAAI,CAAC;QAAEZ,GAAG,EAAE;UAAEa,GAAG,EAAEf;QAAM;MAAE,CAAC,CAAC,CAC7B8H,UAAU,CAAC,CAAC,EAAE;QACf,MAAMtH,cAAc,GAAG,MAAMnB,KAAK,CAAC0I,2BAA2B,CAC5D,MAAM9I,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;UAAEtH,GAAG,EAAEW,CAAC,CAACX;QAAI,CAAC,CAChD,CAAC;QACD,MAAMjB,MAAM,CAACwB,cAAc,CAACgH,WAAW,CACrC;UACE,UAAU,EAAE5G,CAAC,CAACX,GAAG;UACjB,oBAAoB,EAAE2B,IAAI,CAAC3B;QAC7B,CAAC,EACD;UACEkB,IAAI,EAAE;YACJZ,cAAc,EAAE,CAACK,CAAC,CAACX,GAAG,EAAE,GAAGM,cAAc,CAAC,CAACa,GAAG,CAAEC,EAAE,KAAM;cACtDpB,GAAG,EAAEoB;YACP,CAAC,CAAC;UACJ;QACF,CAAC,EACD;UAAEC,KAAK,EAAE;QAAK,CAChB,CAAC;MACH;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE8G,oBAAoB,EAAE,eAAAA,CAAgBxF,KAAK,EAAE7C,KAAK,EAAEJ,OAAO,EAAE;MAC3D,IAAIkD,EAAE;MAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIvC,KAAK,CAAC,wBAAwB,CAAC;MACrD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,wBAAwB,CAAC;MAErDV,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACX,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACE0D,QAAQ,EAAE;MACZ,CAAC,EACDtD,OACF,CAAC;MAED,KAAK,MAAMuD,IAAI,IAAIN,KAAK,EAAE;QACxB,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;UAC5BL,EAAE,GAAGK,IAAI,CAACjD,GAAG;QACf,CAAC,MAAM;UACL4C,EAAE,GAAGK,IAAI;QACX;QAEA,KAAK,MAAMtB,IAAI,IAAI7B,KAAK,EAAE;UACxB,MAAMX,KAAK,CAACiJ,mBAAmB,CAACxF,EAAE,EAAEjB,IAAI,EAAEjC,OAAO,CAAC;QACpD;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE2I,iBAAiB,EAAE,eAAAA,CAAgB1F,KAAK,EAAE7C,KAAK,EAAEJ,OAAO,EAAE;MACxD,IAAIkD,EAAE;MAEN,IAAI,CAACD,KAAK,EAAE,MAAM,IAAIvC,KAAK,CAAC,wBAAwB,CAAC;MACrD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,wBAAwB,CAAC;MAErDV,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACX,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACE0D,QAAQ,EAAE,KAAK;QACfI,QAAQ,EAAE;MACZ,CAAC,EACD1D,OACF,CAAC;MAED,KAAK,MAAMuD,IAAI,IAAIN,KAAK,EAAE;QACxB,IAAI,OAAOM,IAAI,KAAK,QAAQ,EAAE;UAC5BL,EAAE,GAAGK,IAAI,CAACjD,GAAG;QACf,CAAC,MAAM;UACL4C,EAAE,GAAGK,IAAI;QACX;QACA;QACA,MAAMI,QAAQ,GAAG;UAAE,UAAU,EAAET;QAAG,CAAC;QACnC,IAAI,CAAClD,OAAO,CAAC0D,QAAQ,EAAE;UACrBC,QAAQ,CAACN,KAAK,GAAGrD,OAAO,CAACqD,KAAK;QAChC;QAEA,MAAMhE,MAAM,CAACwB,cAAc,CAACmH,WAAW,CAACrE,QAAQ,CAAC;;QAEjD;QACA,KAAK,MAAM1B,IAAI,IAAI7B,KAAK,EAAE;UACxB,MAAMX,KAAK,CAACiJ,mBAAmB,CAACxF,EAAE,EAAEjB,IAAI,EAAEjC,OAAO,CAAC;QACpD;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE0I,mBAAmB,EAAE,eAAAA,CAAgB9E,MAAM,EAAE7D,QAAQ,EAAEC,OAAO,EAAE;MAC9DP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpC,IAAI,CAACO,MAAM,EAAE;QACX;MACF;MAEA,MAAM3B,IAAI,GAAG,MAAM5C,MAAM,CAACe,KAAK,CAACwH,YAAY,CAC1C;QAAEtH,GAAG,EAAEP;MAAS,CAAC,EACjB;QAAEgD,MAAM,EAAE;UAAEvC,QAAQ,EAAE;QAAE;MAAE,CAC5B,CAAC;MAED,IAAI,CAACyB,IAAI,EAAE;QACT,IAAIjC,OAAO,CAACsD,QAAQ,EAAE;UACpB,OAAO,EAAE;QACX,CAAC,MAAM;UACL,MAAM,IAAI5C,KAAK,CAAC,QAAQ,GAAGX,QAAQ,GAAG,mBAAmB,CAAC;QAC5D;MACF;;MAEA;MACA;MACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI,MAAM6I,kBAAkB,GAAG,MAAMvJ,MAAM,CAACwB,cAAc,CAAC+G,YAAY,CAAC;QAClE,UAAU,EAAEhE,MAAM;QAClB,UAAU,EAAE7D,QAAQ;QACpBsD,KAAK,EAAErD,OAAO,CAACqD;MACjB,CAAC,CAAC;MAEF,IAAI5C,UAAU;MACd,IAAIoD,GAAG;MACP,IAAI+E,kBAAkB,EAAE;QACtB,MAAMvJ,MAAM,CAACwB,cAAc,CAACgH,WAAW,CAACe,kBAAkB,CAACtI,GAAG,EAAE;UAC9DkB,IAAI,EAAE;YACJ+B,IAAI,EAAE;cAAEjD,GAAG,EAAEsD;YAAO,CAAC;YACrB3B,IAAI,EAAE;cAAE3B,GAAG,EAAEP;YAAS,CAAC;YACvBsD,KAAK,EAAErD,OAAO,CAACqD;UACjB;QACF,CAAC,CAAC;QAEFQ,GAAG,GAAG,MAAMxE,MAAM,CAACwB,cAAc,CAAC+G,YAAY,CAACgB,kBAAkB,CAACtI,GAAG,CAAC;MACxE,CAAC,MAAM;QACLG,UAAU,GAAG,MAAMpB,MAAM,CAACwB,cAAc,CAACiH,WAAW,CAAC;UACnDvE,IAAI,EAAE;YAAEjD,GAAG,EAAEsD;UAAO,CAAC;UACrB3B,IAAI,EAAE;YAAE3B,GAAG,EAAEP;UAAS,CAAC;UACvBsD,KAAK,EAAErD,OAAO,CAACqD;QACjB,CAAC,CAAC;MACJ;MAEA,IAAI5C,UAAU,EAAE;QACd,MAAMpB,MAAM,CAACwB,cAAc,CAACgH,WAAW,CACrC;UAAEvH,GAAG,EAAEG;QAAW,CAAC,EACnB;UACEe,IAAI,EAAE;YACJZ,cAAc,EAAE,CACdb,QAAQ,EACR,IAAI,MAAMN,KAAK,CAAC0I,2BAA2B,CAAClG,IAAI,CAAC,CAAC,CACnD,CAACR,GAAG,CAAER,CAAC,KAAM;cAAEX,GAAG,EAAEW;YAAE,CAAC,CAAC;UAC3B;QACF,CACF,CAAC;QAED4C,GAAG,GAAG,MAAMxE,MAAM,CAACwB,cAAc,CAAC+G,YAAY,CAAC;UAAEtH,GAAG,EAAEG;QAAW,CAAC,CAAC;MACrE;MACAoD,GAAG,CAACpD,UAAU,GAAGA,UAAU,EAAC;;MAE5B,OAAOoD,GAAG;IACZ,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEoE,wBAAwB,EAAE,eAAAA,CAAgBhG,IAAI,EAAE;MAC9C,IAAI,CAACA,IAAI,EAAE;QACT,OAAO,EAAE;MACX;MAEA,MAAM6B,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC9B,IAAI,CAAC3B,GAAG,CAAC,CAAC;MAEvC,KAAK,MAAMP,QAAQ,IAAI+D,WAAW,EAAE;QAClC,KAAK,MAAMG,UAAU,IAAI,MAAM5E,MAAM,CAACe,KAAK,CACxCc,IAAI,CAAC;UAAE,cAAc,EAAEnB;QAAS,CAAC,CAAC,CAClCmI,UAAU,CAAC,CAAC,EAAE;UACfpE,WAAW,CAACI,GAAG,CAACD,UAAU,CAAC3D,GAAG,CAAC;QACjC;MACF;MAEAwD,WAAW,CAACK,MAAM,CAAClC,IAAI,CAAC3B,GAAG,CAAC;MAE5B,OAAO,CAAC,GAAGwD,WAAW,CAAC;IACzB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEqE,2BAA2B,EAAE,eAAAA,CAAgBlG,IAAI,EAAE;MACjD,MAAMrB,cAAc,GAAG,IAAImD,GAAG,CAAC,CAAC;MAChC,MAAMK,WAAW,GAAG,IAAIL,GAAG,CAAC,CAAC9B,IAAI,CAAC,CAAC;MAEnC,KAAK,MAAMhB,CAAC,IAAImD,WAAW,EAAE;QAC3B,MAAMhE,KAAK,GAAG,MAAMf,MAAM,CAACe,KAAK,CAC7Bc,IAAI,CACH;UAAEZ,GAAG,EAAE;YAAEa,GAAG,EAAEF,CAAC,CAACT,QAAQ,CAACiB,GAAG,CAAER,CAAC,IAAKA,CAAC,CAACX,GAAG;UAAE;QAAE,CAAC,EAC9C;UAAEyC,MAAM,EAAE;YAAEvC,QAAQ,EAAE;UAAE;QAAE,CAC5B,CAAC,CACA0H,UAAU,CAAC,CAAC;QAEf,KAAK,MAAMxG,EAAE,IAAItB,KAAK,EAAE;UACtBQ,cAAc,CAACsD,GAAG,CAACxC,EAAE,CAACpB,GAAG,CAAC;UAC1B8D,WAAW,CAACF,GAAG,CAACxC,EAAE,CAAC;QACrB;MACF;MAEA,OAAO,CAAC,GAAGd,cAAc,CAAC;IAC5B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEiI,yBAAyB,EAAE,eAAAA,CAAgB5F,KAAK,EAAE7C,KAAK,EAAEJ,OAAO,EAAE;MAChE,IAAI,CAACiD,KAAK,EAAE,MAAM,IAAIvC,KAAK,CAAC,wBAAwB,CAAC;MACrD,IAAI,CAACN,KAAK,EAAE,MAAM,IAAIM,KAAK,CAAC,wBAAwB,CAAC;MAErDV,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAC1C,IAAI,CAACX,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpC,KAAK,MAAME,IAAI,IAAIN,KAAK,EAAE;QACxB,IAAI,CAACM,IAAI,EAAE;QAEX,KAAK,MAAMtB,IAAI,IAAI7B,KAAK,EAAE;UACxB,IAAI8C,EAAE;UACN,IAAI,OAAOK,IAAI,KAAK,QAAQ,EAAE;YAC5BL,EAAE,GAAGK,IAAI,CAACjD,GAAG;UACf,CAAC,MAAM;YACL4C,EAAE,GAAGK,IAAI;UACX;UAEA,MAAM9D,KAAK,CAACqJ,wBAAwB,CAAC5F,EAAE,EAAEjB,IAAI,EAAEjC,OAAO,CAAC;QACzD;MACF;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE8I,wBAAwB,EAAE,eAAAA,CAAgBlF,MAAM,EAAE7D,QAAQ,EAAEC,OAAO,EAAE;MACnEP,KAAK,CAACQ,cAAc,CAACF,QAAQ,CAAC;MAC9BN,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpC,IAAI,CAACO,MAAM,EAAE;MAEb,MAAMD,QAAQ,GAAG;QACf,UAAU,EAAEC,MAAM;QAClB,UAAU,EAAE7D;MACd,CAAC;MAED,IAAI,CAACC,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAGrD,OAAO,CAACqD,KAAK;MAChC;MAEA,MAAMhE,MAAM,CAACwB,cAAc,CAACmH,WAAW,CAACrE,QAAQ,CAAC;IACnD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEoF,iBAAiB,EAAE,eAAAA,CAAgBxF,IAAI,EAAEnD,KAAK,EAAEJ,OAAO,EAAE;MACvD,IAAIkD,EAAE;MAENlD,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CA,KAAK,GAAGA,KAAK,CAACoE,MAAM,CAAEvD,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC;MAEtC,IAAI,CAACb,KAAK,CAACwB,MAAM,EAAE,OAAO,KAAK;MAE/BnC,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACE8D,QAAQ,EAAE;MACZ,CAAC,EACD1D,OACF,CAAC;MAED,IAAIuD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACpCL,EAAE,GAAGK,IAAI,CAACjD,GAAG;MACf,CAAC,MAAM;QACL4C,EAAE,GAAGK,IAAI;MACX;MAEA,IAAI,CAACL,EAAE,EAAE,OAAO,KAAK;MACrB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAO,KAAK;MAExC,MAAMS,QAAQ,GAAG;QACf,UAAU,EAAET;MACd,CAAC;MAED,IAAI,CAAClD,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAG;UAAElC,GAAG,EAAE,CAACnB,OAAO,CAACqD,KAAK,EAAE,IAAI;QAAE,CAAC;MACjD;MAEA,MAAMQ,GAAG,GAAG,MAAMyD,SAAS,CAAClH,KAAK,EAAE,MAAOL,QAAQ,IAAK;QACrD4D,QAAQ,CAAC,oBAAoB,CAAC,GAAG5D,QAAQ;QACzC,MAAMiJ,GAAG,GACP,CAAC,MAAM3J,MAAM,CAACwB,cAAc,CAACoI,cAAc,CAACtF,QAAQ,EAAE;UAAEe,KAAK,EAAE;QAAE,CAAC,CAAC,IAAI,CAAC;QAC1E,OAAOsE,GAAG;MACZ,CAAC,CAAC;MAEF,OAAOnF,GAAG;IACZ,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEqF,oBAAoB,EAAE,eAAAA,CAAgB3F,IAAI,EAAEvD,OAAO,EAAE;MACnD,IAAIkD,EAAE;MAENlD,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;MAE1CP,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCrD,OAAO,GAAGL,MAAM,CAACC,MAAM,CAAC;QACtBgF,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,KAAK;QACnBnB,QAAQ,EAAE,KAAK;QACfoB,UAAU,EAAE;MACd,CAAC,EAAE9E,OAAO,CAAC;MAEX,IAAIuD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACpCL,EAAE,GAAGK,IAAI,CAACjD,GAAG;MACf,CAAC,MAAM;QACL4C,EAAE,GAAGK,IAAI;MACX;MAEA,IAAI,CAACL,EAAE,EAAE,OAAO,EAAE;MAElB,MAAMS,QAAQ,GAAG;QACf,UAAU,EAAET;MACd,CAAC;MAED,MAAMsB,MAAM,GAAG;QACbzB,MAAM,EAAE;UAAE,oBAAoB,EAAE;QAAE;MACpC,CAAC;MAED,IAAI,CAAC/C,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAG;UAAElC,GAAG,EAAE,CAACnB,OAAO,CAACqD,KAAK;QAAE,CAAC;QAEzC,IAAI,CAACrD,OAAO,CAAC8E,UAAU,EAAE;UACvBnB,QAAQ,CAACN,KAAK,CAAClC,GAAG,CAAC4D,IAAI,CAAC,IAAI,CAAC;QAC/B;MACF;MAEA,IAAI/E,OAAO,CAAC6E,YAAY,EAAE;QACxB,OAAOL,MAAM,CAACzB,MAAM,CAAC,oBAAoB,CAAC;QAC1CyB,MAAM,CAACzB,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;MAC/B;MAEA,IAAI/C,OAAO,CAAC4E,WAAW,EAAE;QACvB,OAAOJ,MAAM,CAACzB,MAAM;MACtB;MAEA,MAAM3C,KAAK,GAAG,MAAMf,MAAM,CAACwB,cAAc,CAACK,IAAI,CAACyC,QAAQ,EAAEa,MAAM,CAAC,CAAC0D,UAAU,CAAC,CAAC;MAE7E,IAAIlI,OAAO,CAAC4E,WAAW,EAAE;QACvB,OAAOxE,KAAK;MACd;MAEA,OAAO,CACL,GAAG,IAAI2D,GAAG,CACR3D,KAAK,CAAC4E,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;QAC7B,IAAIA,OAAO,CAACtE,cAAc,EAAE;UAC1B,OAAOqE,GAAG,CAACE,MAAM,CAACD,OAAO,CAACtE,cAAc,CAACa,GAAG,CAAER,CAAC,IAAKA,CAAC,CAACX,GAAG,CAAC,CAAC;QAC7D,CAAC,MAAM,IAAI4E,OAAO,CAACjD,IAAI,EAAE;UACvBgD,GAAG,CAACF,IAAI,CAACG,OAAO,CAACjD,IAAI,CAAC3B,GAAG,CAAC;QAC5B;QACA,OAAO2E,GAAG;MACZ,CAAC,EAAE,EAAE,CACP,CAAC,CACF;IACH,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEG,WAAW,EAAE,SAAAA,CAAUC,YAAY,EAAE;MACnCA,YAAY,GAAGA,YAAY,IAAI;QAAEC,IAAI,EAAE;UAAEhF,GAAG,EAAE;QAAE;MAAE,CAAC;MAEnD,OAAOjB,MAAM,CAACe,KAAK,CAACc,IAAI,CAAC,CAAC,CAAC,EAAEmE,YAAY,CAAC;IAC5C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE8D,mBAAmB,EAAE,eAAAA,CAAgB/I,KAAK,EAAEJ,OAAO,EAAEqF,YAAY,EAAE;MACjE,MAAMG,GAAG,GAAG,CACV,MAAM/F,KAAK,CAACgG,yBAAyB,CAACrF,KAAK,EAAEJ,OAAO,CAAC,CAACkI,UAAU,CAAC,CAAC,EAClEzG,GAAG,CAAEiE,CAAC,IAAKA,CAAC,CAACnC,IAAI,CAACjD,GAAG,CAAC;MAExB,OAAOjB,MAAM,CAAC4D,KAAK,CAAC/B,IAAI,CACtB;QAAEZ,GAAG,EAAE;UAAEa,GAAG,EAAEqE;QAAI;MAAE,CAAC,EACpBxF,OAAO,IAAIA,OAAO,CAACqF,YAAY,IAAKA,YAAY,IAAI,CAAC,CACxD,CAAC;IACH,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEEI,yBAAyB,EAAE,SAAAA,CAAUrF,KAAK,EAAEJ,OAAO,EAAE;MACnDA,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACE8D,QAAQ,EAAE,KAAK;QACf2B,YAAY,EAAE,CAAC;MACjB,CAAC,EACDrF,OACF,CAAC;MAED,OAAOP,KAAK,CAACkG,qBAAqB,CAACvF,KAAK,EAAEJ,OAAO,EAAEA,OAAO,CAACqF,YAAY,CAAC;IAC1E,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEM,qBAAqB,EAAE,SAAAA,CAAUvF,KAAK,EAAEJ,OAAO,EAAEwE,MAAM,EAAE;MACvDxE,OAAO,GAAGP,KAAK,CAAC0D,iBAAiB,CAACnD,OAAO,CAAC;MAE1CA,OAAO,GAAGL,MAAM,CAACC,MAAM,CACrB;QACE8D,QAAQ,EAAE,KAAK;QACfoB,UAAU,EAAE;MACd,CAAC,EACD9E,OACF,CAAC;;MAED;MACA,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAE1CX,KAAK,CAAC2D,eAAe,CAACpD,OAAO,CAACqD,KAAK,CAAC;MAEpCmB,MAAM,GAAG7E,MAAM,CAACC,MAAM,CACpB;QACEmD,MAAM,EAAE;UAAE,UAAU,EAAE;QAAE;MAC1B,CAAC,EACDyB,MACF,CAAC;MAED,MAAMb,QAAQ,GAAG;QACf,oBAAoB,EAAE;UAAExC,GAAG,EAAEf;QAAM;MACrC,CAAC;MAED,IAAI,CAACJ,OAAO,CAAC0D,QAAQ,EAAE;QACrBC,QAAQ,CAACN,KAAK,GAAG;UAAElC,GAAG,EAAE,CAACnB,OAAO,CAACqD,KAAK;QAAE,CAAC;QAEzC,IAAI,CAACrD,OAAO,CAAC8E,UAAU,EAAE;UACvBnB,QAAQ,CAACN,KAAK,CAAClC,GAAG,CAAC4D,IAAI,CAAC,IAAI,CAAC;QAC/B;MACF;MAEA,OAAO1F,MAAM,CAACwB,cAAc,CAACK,IAAI,CAACyC,QAAQ,EAAEa,MAAM,CAAC;IACrD,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE4E,qBAAqB,EAAE,eAAAA,CAAA,EAAyB;MAC9C,IAAI,CAAC1J,kCAAkC,EAAE;QACvCA,kCAAkC,GAAG,IAAI;QACzCmG,OAAO,IACLA,OAAO,CAACC,IAAI,CACV,qEACF,CAAC;MACL;MAEA,OAAO,MAAMrG,KAAK,CAACsG,gBAAgB,CAAC,GAAAC,SAAO,CAAC;IAC9C,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEqD,qBAAqB,EAAE,eAAAA,CAAgB9F,IAAI,EAAEnD,KAAK,EAAE;MAClD,IAAI8C,EAAE;MAEN,IAAI9C,KAAK,IAAI,CAACkC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,EAAEA,KAAK,GAAG,CAACA,KAAK,CAAC;MAEnD,IAAImD,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QACpCL,EAAE,GAAGK,IAAI,CAACjD,GAAG;MACf,CAAC,MAAM;QACL4C,EAAE,GAAGK,IAAI;MACX;MAEA,IAAI,CAACL,EAAE,EAAE,OAAO,EAAE;MAElB,MAAMS,QAAQ,GAAG;QACf,UAAU,EAAET,EAAE;QACdG,KAAK,EAAE;UAAEX,GAAG,EAAE;QAAK;MACrB,CAAC;MAED,IAAItC,KAAK,EAAE;QACTuD,QAAQ,CAAC,oBAAoB,CAAC,GAAG;UAAExC,GAAG,EAAEf;QAAM,CAAC;MACjD;MAEA,MAAM6F,MAAM,GAAG,CACb,MAAM5G,MAAM,CAACwB,cAAc,CACxBK,IAAI,CAACyC,QAAQ,EAAE;QAAEZ,MAAM,EAAE;UAAEM,KAAK,EAAE;QAAE;MAAE,CAAC,CAAC,CACxC6E,UAAU,CAAC,CAAC,EACfzG,GAAG,CAAEyE,GAAG,IAAKA,GAAG,CAAC7C,KAAK,CAAC;MAEzB,OAAO,CAAC,GAAG,IAAIU,GAAG,CAACkC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEqD,gBAAgB,EAAE,eAAAA,CAAgBxH,OAAO,EAAEC,OAAO,EAAE;MAClD,IAAIC,KAAK;MAETvC,KAAK,CAAC2D,eAAe,CAACtB,OAAO,CAAC;MAC9BrC,KAAK,CAAC2D,eAAe,CAACrB,OAAO,CAAC;MAE9B,IAAID,OAAO,KAAKC,OAAO,EAAE;MAEzB,GAAG;QACDC,KAAK,GAAG,MAAM3C,MAAM,CAACwB,cAAc,CAACgH,WAAW,CAC7C;UACExE,KAAK,EAAEvB;QACT,CAAC,EACD;UACEN,IAAI,EAAE;YACJ6B,KAAK,EAAEtB;UACT;QACF,CAAC,EACD;UAAEJ,KAAK,EAAE;QAAK,CAChB,CAAC;MACH,CAAC,QAAQK,KAAK,GAAG,CAAC;IACpB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEuH,gBAAgB,EAAE,eAAAA,CAAgBlD,IAAI,EAAE;MACtC5G,KAAK,CAAC2D,eAAe,CAACiD,IAAI,CAAC;MAE3B,MAAMhH,MAAM,CAACwB,cAAc,CAACmH,WAAW,CAAC;QAAE3E,KAAK,EAAEgD;MAAK,CAAC,CAAC;IAC1D,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACEpG,cAAc,EAAE,SAAAA,CAAUF,QAAQ,EAAE;MAClC,IACE,CAACA,QAAQ,IACT,OAAOA,QAAQ,KAAK,QAAQ,IAC5BA,QAAQ,CAACuG,IAAI,CAAC,CAAC,KAAKvG,QAAQ,EAC5B;QACA,MAAM,IAAIW,KAAK,uBAAAyE,MAAA,CAAuBpF,QAAQ,OAAI,CAAC;MACrD;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEyJ,eAAe,EAAE,eAAAA,CAAgBhD,cAAc,EAAEC,aAAa,EAAE;MAC9D,IAAID,cAAc,KAAKC,aAAa,EAAE;QACpC,OAAO,IAAI;MACb;MAEA,IAAID,cAAc,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;QACnD,OAAO,KAAK;MACd;MAEAhH,KAAK,CAACQ,cAAc,CAACuG,cAAc,CAAC;MACpC/G,KAAK,CAACQ,cAAc,CAACwG,aAAa,CAAC;MAEnC,IAAIC,YAAY,GAAG,CAACF,cAAc,CAAC;MACnC,OAAOE,YAAY,CAAC9E,MAAM,KAAK,CAAC,EAAE;QAChC,MAAM7B,QAAQ,GAAG2G,YAAY,CAACC,GAAG,CAAC,CAAC;QAEnC,IAAI5G,QAAQ,KAAK0G,aAAa,EAAE;UAC9B,OAAO,IAAI;QACb;QAEA,MAAMxE,IAAI,GAAG,MAAM5C,MAAM,CAACe,KAAK,CAACwH,YAAY,CAAC;UAAEtH,GAAG,EAAEP;QAAS,CAAC,CAAC;;QAE/D;QACA,IAAI,CAACkC,IAAI,EAAE;QAEXyE,YAAY,GAAGA,YAAY,CAACvB,MAAM,CAAClD,IAAI,CAACzB,QAAQ,CAACiB,GAAG,CAAER,CAAC,IAAKA,CAAC,CAACX,GAAG,CAAC,CAAC;MACrE;MAEA,OAAO,KAAK;IACd,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE6C,iBAAiB,EAAE,SAAAA,CAAUnD,OAAO,EAAE;MACpCA,OAAO,GAAGA,OAAO,KAAK4G,SAAS,GAAG,CAAC,CAAC,GAAG5G,OAAO;;MAE9C;MACA;MACA,IAAIA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAClFA,OAAO,GAAG;UAAEqD,KAAK,EAAErD;QAAQ,CAAC;MAC9B;MAEAA,OAAO,CAACqD,KAAK,GAAG5D,KAAK,CAACoH,mBAAmB,CAAC7G,OAAO,CAACqD,KAAK,CAAC;MAExD,OAAOrD,OAAO;IAChB,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE6G,mBAAmB,EAAE,SAAAA,CAAUC,SAAS,EAAE;MACxC;MACA,IAAIA,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOA,SAAS;MAClB;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE1D,eAAe,EAAE,SAAAA,CAAU0D,SAAS,EAAE;MACpC,IAAIA,SAAS,KAAK,IAAI,EAAE;MAExB,IACE,CAACA,SAAS,IACV,OAAOA,SAAS,KAAK,QAAQ,IAC7BA,SAAS,CAACR,IAAI,CAAC,CAAC,KAAKQ,SAAS,EAC9B;QACA,MAAM,IAAIpG,KAAK,wBAAAyE,MAAA,CAAwB2B,SAAS,OAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;AAAA,EAAAC,IAAA,OAAAC,MAAA,E;;;;;;;;;;;AC/yCF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAvH,KAAK,CAACgK,KAAK,GAAG,KAAK;AAEnB,IAAI;EACF,IAAIC,YAAY,EAAE;IAChB,MAAMC,IAAI,GAAGD,YAAY,CAACE,OAAO,CAAC,aAAa,CAAC;IAEhD,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/BlK,KAAK,CAACgK,KAAK,GAAG,CAAC,CAACE,IAAI;IACtB;EACF;AACF,CAAC,CAAC,OAAOE,EAAE,EAAE;EACX;EACA;AAAA,C;;;;;;;;;;;;ECvBF;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACApK,KAAK,CAACqK,UAAU,GAAG;IAEjB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEC,QAAQ,EAAE,SAAAA,CAAU9H,IAAI,EAAEoB,KAAK,EAAE;MAC/B,MAAME,IAAI,GAAGlE,MAAM,CAACkE,IAAI,CAAC,CAAC;MAC1B,MAAMyG,KAAK,GAAG,CAAC/H,IAAI,IAAI,EAAE,EAAEgI,OAAO,CAAC,GAAG,CAAC;MACvC,IAAI7J,KAAK;MAET,IAAI,CAACmD,IAAI,EAAE,OAAO,KAAK;MACvB,IAAI,CAAC2G,KAAK,CAACC,IAAI,CAAClI,IAAI,EAAEmI,MAAM,CAAC,EAAE,OAAO,KAAK;MAE3C,IAAIJ,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB5J,KAAK,GAAG6B,IAAI,CAACoI,KAAK,CAAC,GAAG,CAAC,CAACrF,MAAM,CAAC,UAAUsF,IAAI,EAAErJ,CAAC,EAAE;UAChD,IAAI,CAACA,CAAC,EAAE;YACN,OAAOqJ,IAAI;UACb;UACAA,IAAI,CAACvF,IAAI,CAAC9D,CAAC,CAAC;UACZ,OAAOqJ,IAAI;QACb,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,MAAM;QACLlK,KAAK,GAAG,CAAC6B,IAAI,CAAC;MAChB;MAEA,IAAIiI,KAAK,CAACC,IAAI,CAAC9G,KAAK,EAAE+G,MAAM,CAAC,EAAE;QAC7B,OAAO3K,KAAK,CAAC8E,YAAY,CAAChB,IAAI,EAAEnD,KAAK,EAAEiD,KAAK,CAAC;MAC/C;MAEA,OAAO5D,KAAK,CAAC8E,YAAY,CAAChB,IAAI,EAAEnD,KAAK,CAAC;IACxC;EACF,CAAC;;EAED;EACA;EACA;;EAEA,IAAIX,KAAK,CAACgK,KAAK,IAAI5D,OAAO,CAAC4D,KAAK,EAAE;IAChC5D,OAAO,CAAC4D,KAAK,CAAC,uBAAuB,EAAEhK,KAAK,CAACgK,KAAK,CAAC;EACrD;EAEA,IAAI,OAAOc,OAAO,CAACC,KAAK,KAAK,WAAW,IACpC,OAAOD,OAAO,CAACC,KAAK,CAACC,KAAK,KAAK,WAAW,IAC1C,OAAOF,OAAO,CAACC,KAAK,CAACC,KAAK,CAACC,cAAc,KAAK,UAAU,EAAE;IAC5D/K,MAAM,CAACgL,OAAO,CAAClL,KAAK,CAACqK,UAAU,CAAC,CAAC9F,OAAO,CAAC4G,IAAA,IAAkB;MAAA,IAAjB,CAACvE,IAAI,EAAEwE,IAAI,CAAC,GAAAD,IAAA;MACpD,IAAInL,KAAK,CAACgK,KAAK,IAAI5D,OAAO,CAAC4D,KAAK,EAAE;QAChC5D,OAAO,CAAC4D,KAAK,CAAC,qCAAqC,GAAGpD,IAAI,GAAG,IAAI,CAAC;MACpE;MACAkE,OAAO,CAACC,KAAK,CAACC,KAAK,CAACC,cAAc,CAACrE,IAAI,EAAEwE,IAAI,CAAC;IAChD,CAAC,CAAC;EACJ;AAAC,EAAA9D,IAAA,OAAAC,MAAA,E", "file": "/packages/alanning_roles.js", "sourcesContent": ["/* global Roles */\nimport { Meteor } from 'meteor/meteor'\n\n/**\n * Provides functions related to user authorization. Compatible with built-in Meteor accounts packages.\n *\n * Roles are accessible through `Meteor.roles` collection and documents consist of:\n *  - `_id`: role name\n *  - `children`: list of subdocuments:\n *    - `_id`\n *\n * Children list elements are subdocuments so that they can be easier extended in the future or by plugins.\n *\n * Roles can have multiple parents and can be children (subroles) of multiple roles.\n *\n * Example: `{_id: 'admin', children: [{_id: 'editor'}]}`\n *\n * The assignment of a role to a user is stored in a collection, accessible through `RoleAssignmentCollection`.\n * It's documents consist of\n *  - `_id`: Internal MongoDB id\n *  - `role`: A role object which got assigned. Usually only contains the `_id` property\n *  - `user`: A user object, usually only contains the `_id` property\n *  - `scope`: scope name\n *  - `inheritedRoles`: A list of all the roles objects inherited by the assigned role.\n *\n * @module Roles\n */\n\n/**\n * @class Roles\n */\nif (typeof Roles === 'undefined') {\n  Roles = {} // eslint-disable-line no-global-assign\n}\n\nlet getGroupsForUserDeprecationWarning = false\n\nObject.assign(Roles, {\n\n  /**\n   * Used as a global group (now scope) name. Not used anymore.\n   *\n   * @property GLOBAL_GROUP\n   * @static\n   * @deprecated\n   */\n  GLOBAL_GROUP: null,\n\n  /**\n   * Create a new role.\n   *\n   * @method createRole\n   * @param {String} roleName Name of role.\n   * @param {Object} [options] Options:\n   *   - `unlessExists`: if `true`, exception will not be thrown in the role already exists\n   * @return {String} ID of the new role or null.\n   * @static\n   */\n  createRole: function (roleName, options) {\n    Roles._checkRoleName(roleName)\n\n    options = Object.assign({\n      unlessExists: false\n    }, options)\n\n    const result = Meteor.roles.upsert({ _id: roleName }, { $setOnInsert: { children: [] } })\n\n    if (!result.insertedId) {\n      if (options.unlessExists) return null\n      throw new Error('Role \\'' + roleName + '\\' already exists.')\n    }\n\n    return result.insertedId\n  },\n\n  /**\n   * Delete an existing role.\n   *\n   * If the role is set for any user, it is automatically unset.\n   *\n   * @method deleteRole\n   * @param {String} roleName Name of role.\n   * @static\n   */\n  deleteRole: function (roleName) {\n    let roles\n    let inheritedRoles\n\n    Roles._checkRoleName(roleName)\n\n    // Remove all assignments\n    Meteor.roleAssignment.remove({\n      'role._id': roleName\n    })\n\n    do {\n      // For all roles who have it as a dependency ...\n      roles = Roles._getParentRoleNames(Meteor.roles.findOne({ _id: roleName }))\n\n      for (const r of Meteor.roles.find({ _id: { $in: roles } }).fetch()) {\n        Meteor.roles.update({\n          _id: r._id\n        }, {\n          $pull: {\n            children: {\n              _id: roleName\n            }\n          }\n        })\n\n        inheritedRoles = Roles._getInheritedRoleNames(Meteor.roles.findOne({ _id: r._id }))\n        Meteor.roleAssignment.update({\n          'role._id': r._id\n        }, {\n          $set: {\n            inheritedRoles: [r._id, ...inheritedRoles].map(r2 => ({ _id: r2 }))\n          }\n        }, { multi: true })\n      }\n    } while (roles.length > 0)\n\n    // And finally remove the role itself\n    Meteor.roles.remove({ _id: roleName })\n  },\n\n  /**\n   * Rename an existing role.\n   *\n   * @method renameRole\n   * @param {String} oldName Old name of a role.\n   * @param {String} newName New name of a role.\n   * @static\n   */\n  renameRole: function (oldName, newName) {\n    let count\n\n    Roles._checkRoleName(oldName)\n    Roles._checkRoleName(newName)\n\n    if (oldName === newName) return\n\n    const role = Meteor.roles.findOne({ _id: oldName })\n\n    if (!role) {\n      throw new Error('Role \\'' + oldName + '\\' does not exist.')\n    }\n\n    role._id = newName\n\n    Meteor.roles.insert(role)\n\n    do {\n      count = Meteor.roleAssignment.update({\n        'role._id': oldName\n      }, {\n        $set: {\n          'role._id': newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n\n    do {\n      count = Meteor.roleAssignment.update({\n        'inheritedRoles._id': oldName\n      }, {\n        $set: {\n          'inheritedRoles.$._id': newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n\n    do {\n      count = Meteor.roles.update({\n        'children._id': oldName\n      }, {\n        $set: {\n          'children.$._id': newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n\n    Meteor.roles.remove({ _id: oldName })\n  },\n\n  /**\n   * Add role parent to roles.\n   *\n   * Previous parents are kept (role can have multiple parents). For users which have the\n   * parent role set, new subroles are added automatically.\n   *\n   * @method addRolesToParent\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @static\n   */\n  addRolesToParent: function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      Roles._addRoleToParent(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _addRoleToParent\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @private\n   * @static\n   */\n  _addRoleToParent: function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // query to get role's children\n    const role = Meteor.roles.findOne({ _id: roleName })\n\n    if (!role) {\n      throw new Error('Role \\'' + roleName + '\\' does not exist.')\n    }\n\n    // detect cycles\n    if (Roles._getInheritedRoleNames(role).includes(parentName)) {\n      throw new Error('Roles \\'' + roleName + '\\' and \\'' + parentName + '\\' would form a cycle.')\n    }\n\n    const count = Meteor.roles.update({\n      _id: parentName,\n      'children._id': {\n        $ne: role._id\n      }\n    }, {\n      $push: {\n        children: {\n          _id: role._id\n        }\n      }\n    })\n\n    // if there was no change, parent role might not exist, or role is\n    // already a subrole; in any case we do not have anything more to do\n    if (!count) return\n\n    Meteor.roleAssignment.update({\n      'inheritedRoles._id': parentName\n    }, {\n      $push: {\n        inheritedRoles: { $each: [role._id, ...Roles._getInheritedRoleNames(role)].map(r => ({ _id: r })) }\n      }\n    }, { multi: true })\n  },\n\n  /**\n   * Remove role parent from roles.\n   *\n   * Other parents are kept (role can have multiple parents). For users which have the\n   * parent role set, removed subrole is removed automatically.\n   *\n   * @method removeRolesFromParent\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @static\n   */\n  removeRolesFromParent: function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      Roles._removeRoleFromParent(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _removeRoleFromParent\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @private\n   * @static\n   */\n  _removeRoleFromParent: function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // check for role existence\n    // this would not really be needed, but we are trying to match addRolesToParent\n    const role = Meteor.roles.findOne({ _id: roleName }, { fields: { _id: 1 } })\n\n    if (!role) {\n      throw new Error('Role \\'' + roleName + '\\' does not exist.')\n    }\n\n    const count = Meteor.roles.update({\n      _id: parentName\n    }, {\n      $pull: {\n        children: {\n          _id: role._id\n        }\n      }\n    })\n\n    // if there was no change, parent role might not exist, or role was\n    // already not a subrole; in any case we do not have anything more to do\n    if (!count) return\n\n    // For all roles who have had it as a dependency ...\n    const roles = [...Roles._getParentRoleNames(Meteor.roles.findOne({ _id: parentName })), parentName]\n\n    for (const r of Meteor.roles.find({ _id: { $in: roles } }).fetch()) {\n      const inheritedRoles = Roles._getInheritedRoleNames(Meteor.roles.findOne({ _id: r._id }))\n      Meteor.roleAssignment.update({\n        'role._id': r._id,\n        'inheritedRoles._id': role._id\n      }, {\n        $set: {\n          inheritedRoles: [r._id, ...inheritedRoles].map(r2 => ({ _id: r2 }))\n        }\n      }, { multi: true })\n    }\n  },\n\n  /**\n   * Add users to roles.\n   *\n   * Adds roles to existing roles for each user.\n   *\n   * @example\n   *     Roles.addUsersToRoles(userId, 'admin')\n   *     Roles.addUsersToRoles(userId, ['view-secrets'], 'example.com')\n   *     Roles.addUsersToRoles([user1, user2], ['user','editor'])\n   *     Roles.addUsersToRoles([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method addUsersToRoles\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  addUsersToRoles: function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error('Missing \\'users\\' param.')\n    if (!roles) throw new Error('Missing \\'roles\\' param.')\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      ifExists: false\n    }, options)\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n\n      for (const role of roles) {\n        Roles._addUserToRole(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Set users' roles.\n   *\n   * Replaces all existing roles with a new set of roles.\n   *\n   * @example\n   *     Roles.setUserRoles(userId, 'admin')\n   *     Roles.setUserRoles(userId, ['view-secrets'], 'example.com')\n   *     Roles.setUserRoles([user1, user2], ['user','editor'])\n   *     Roles.setUserRoles([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method setUserRoles\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if `true`, remove all roles the user has, of any scope, if `false`, only the one in the same scope\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  setUserRoles: function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error('Missing \\'users\\' param.')\n    if (!roles) throw new Error('Missing \\'roles\\' param.')\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      ifExists: false,\n      anyScope: false\n    }, options)\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n      // we first clear all roles for the user\n      const selector = { 'user._id': id }\n      if (!options.anyScope) {\n        selector.scope = options.scope\n      }\n\n      Meteor.roleAssignment.remove(selector)\n\n      // and then add all\n      for (const role of roles) {\n        Roles._addUserToRole(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Add one user to one role.\n   *\n   * @method _addUserToRole\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @private\n   * @static\n   */\n  _addUserToRole: function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) {\n      return\n    }\n\n    const role = Meteor.roles.findOne({ _id: roleName }, { fields: { children: 1 } })\n\n    if (!role) {\n      if (options.ifExists) {\n        return []\n      } else {\n        throw new Error('Role \\'' + roleName + '\\' does not exist.')\n      }\n    }\n\n    // This might create duplicates, because we don't have a unique index, but that's all right. In case there are two, withdrawing the role will effectively kill them both.\n    const res = Meteor.roleAssignment.upsert({\n      'user._id': userId,\n      'role._id': roleName,\n      scope: options.scope\n    }, {\n      $setOnInsert: {\n        user: { _id: userId },\n        role: { _id: roleName },\n        scope: options.scope\n      }\n    })\n\n    if (res.insertedId) {\n      Meteor.roleAssignment.update({ _id: res.insertedId }, {\n        $set: {\n          inheritedRoles: [roleName, ...Roles._getInheritedRoleNames(role)].map(r => ({ _id: r }))\n        }\n      })\n    }\n\n    return res\n  },\n\n  /**\n   * Returns an array of role names the given role name is a child of.\n   *\n   * @example\n   *     Roles._getParentRoleNames({ _id: 'admin', children; [] })\n   *\n   * @method _getParentRoleNames\n   * @param {object} role The role object\n   * @private\n   * @static\n   */\n  _getParentRoleNames: function (role) {\n    if (!role) {\n      return []\n    }\n\n    const parentRoles = new Set([role._id])\n\n    for (const roleName of parentRoles) {\n      Meteor.roles.find({ 'children._id': roleName }).fetch().forEach(parentRole => {\n        parentRoles.add(parentRole._id)\n      })\n    }\n\n    parentRoles.delete(role._id)\n\n    return [...parentRoles]\n  },\n\n  /**\n   * Returns an array of role names the given role name is a parent of.\n   *\n   * @example\n   *     Roles._getInheritedRoleNames({ _id: 'admin', children; [] })\n   *\n   * @method _getInheritedRoleNames\n   * @param {object} role The role object\n   * @private\n   * @static\n   */\n  _getInheritedRoleNames: function (role) {\n    const inheritedRoles = new Set()\n    const nestedRoles = new Set([role])\n\n    for (const r of nestedRoles) {\n      const roles = Meteor.roles.find({ _id: { $in: r.children.map(r => r._id) } }, { fields: { children: 1 } }).fetch()\n\n      for (const r2 of roles) {\n        inheritedRoles.add(r2._id)\n        nestedRoles.add(r2)\n      }\n    }\n\n    return [...inheritedRoles]\n  },\n\n  /**\n   * Remove users from assigned roles.\n   *\n   * @example\n   *     Roles.removeUsersFromRoles(userId, 'admin')\n   *     Roles.removeUsersFromRoles([userId, user2], ['editor'])\n   *     Roles.removeUsersFromRoles(userId, ['user'], 'group1')\n   *\n   * @method removeUsersFromRoles\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to remove users from. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  removeUsersFromRoles: function (users, roles, options) {\n    if (!users) throw new Error('Missing \\'users\\' param.')\n    if (!roles) throw new Error('Missing \\'roles\\' param.')\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    for (const user of users) {\n      if (!user) continue\n\n      for (const role of roles) {\n        let id\n        if (typeof user === 'object') {\n          id = user._id\n        } else {\n          id = user\n        }\n\n        Roles._removeUserFromRole(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Remove one user from one role.\n   *\n   * @method _removeUserFromRole\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @private\n   * @static\n   */\n  _removeUserFromRole: function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) return\n\n    const selector = {\n      'user._id': userId,\n      'role._id': roleName\n    }\n\n    if (!options.anyScope) {\n      selector.scope = options.scope\n    }\n\n    Meteor.roleAssignment.remove(selector)\n  },\n\n  /**\n   * Check if user has specified roles.\n   *\n   * @example\n   *     // global roles\n   *     Roles.userIsInRole(user, 'admin')\n   *     Roles.userIsInRole(user, ['admin','editor'])\n   *     Roles.userIsInRole(userId, 'admin')\n   *     Roles.userIsInRole(userId, ['admin','editor'])\n   *\n   *     // scope roles (global roles are still checked)\n   *     Roles.userIsInRole(user, 'admin', 'group1')\n   *     Roles.userIsInRole(userId, ['admin','editor'], 'group1')\n   *     Roles.userIsInRole(userId, ['admin','editor'], {scope: 'group1'})\n   *\n   * @method userIsInRole\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} roles Name of role or an array of roles to check against. If array,\n   *                             will return `true` if user is in _any_ role.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope; if supplied, limits check to just that scope\n   *     the user's global roles will always be checked whether scope is specified or not\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Boolean} `true` if user is in _any_ of the target roles\n   * @static\n   */\n  userIsInRole: function (user, roles, options) {\n    let id\n    options = Roles._normalizeOptions(options)\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    roles = roles.filter(r => r != null)\n\n    if (!roles.length) return false\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      anyScope: false\n    }, options)\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return false\n    if (typeof id !== 'string') return false\n\n    const selector = { 'user._id': id }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope, null] }\n    }\n\n    return roles.some((roleName) => {\n      selector['inheritedRoles._id'] = roleName\n\n      return Meteor.roleAssignment.find(selector, { limit: 1 }).count() > 0\n    })\n  },\n\n  /**\n   * Retrieve user's roles.\n   *\n   * @method getRolesForUser\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of scope to provide roles for; if not specified, global roles are returned\n   *   - `anyScope`: if set, role can be in any scope (`scope` and `onlyAssigned` options are ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `onlyAssigned`: return only assigned roles and not automatically inferred (like subroles)\n   *   - `fullObjects`: return full roles objects (`true`) or just names (`false`) (`onlyAssigned` option is ignored) (default `false`)\n   *     If you have a use-case for this option, please file a feature-request. You shouldn't need to use it as it's\n   *     result strongly dependent on the internal data structure of this plugin.\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Array} Array of user's roles, unsorted.\n   * @static\n   */\n  getRolesForUser: function (user, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      fullObjects: false,\n      onlyAssigned: false,\n      anyScope: false,\n      onlyScoped: false\n    }, options)\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = { 'user._id': id }\n    const filter = { fields: { 'inheritedRoles._id': 1 } }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    if (options.onlyAssigned) {\n      delete filter.fields['inheritedRoles._id']\n      filter.fields['role._id'] = 1\n    }\n\n    if (options.fullObjects) {\n      delete filter.fields\n    }\n\n    const roles = Meteor.roleAssignment.find(selector, filter).fetch()\n\n    if (options.fullObjects) {\n      return roles\n    }\n\n    return [...new Set(roles.reduce((rev, current) => {\n      if (current.inheritedRoles) {\n        return rev.concat(current.inheritedRoles.map(r => r._id))\n      } else if (current.role) {\n        rev.push(current.role._id)\n      }\n      return rev\n    }, []))]\n  },\n\n  /**\n   * Retrieve cursor of all existing roles.\n   *\n   * @method getAllRoles\n   * @param {Object} queryOptions Options which are passed directly\n   *                                through to `RolesCollection.find(query, options)`.\n   * @return {Cursor} Cursor of existing roles.\n   * @static\n   */\n  getAllRoles: function (queryOptions) {\n    queryOptions = queryOptions || { sort: { _id: 1 } }\n\n    return Meteor.roles.find({}, queryOptions)\n  },\n\n  /**\n   * Retrieve all users who are in target role.\n   *\n   * Options:\n   *\n   * @method getUsersInRole\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `queryOptions`: options which are passed directly\n   *     through to `Meteor.users.find(query, options)`\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `Meteor.users.find(query, options)`\n   * @return {Cursor} Cursor of users in roles.\n   * @static\n   */\n  getUsersInRole: function (roles, options, queryOptions) {\n    const ids = Roles.getUserAssignmentsForRole(roles, options).fetch().map(a => a.user._id)\n\n    return Meteor.users.find({ _id: { $in: ids } }, ((options && options.queryOptions) || queryOptions) || {})\n  },\n\n  /**\n   * Retrieve all assignments of a user which are for the target role.\n   *\n   * Options:\n   *\n   * @method getUserAssignmentsForRole\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `queryOptions`: options which are passed directly\n   *     through to `RoleAssignmentCollection.find(query, options)`\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Cursor} Cursor of user assignments for roles.\n   * @static\n   */\n  getUserAssignmentsForRole: function (roles, options) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign({\n      anyScope: false,\n      queryOptions: {}\n    }, options)\n\n    return Roles._getUsersInRoleCursor(roles, options, options.queryOptions)\n  },\n\n  /**\n   * @method _getUsersInRoleCursor\n   * @param {Array|String} roles Name of role or an array of roles. If array, ids of users are\n   *                             returned which have at least one of the roles\n   *                             assigned but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [filter] Options which are passed directly\n   *                                through to `RoleAssignmentCollection.find(query, options)`\n   * @return {Object} Cursor to the assignment documents\n   * @private\n   * @static\n   */\n  _getUsersInRoleCursor: function (roles, options, filter) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign({\n      anyScope: false,\n      onlyScoped: false\n    }, options)\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    filter = Object.assign({\n      fields: { 'user._id': 1 }\n    }, filter)\n\n    const selector = { 'inheritedRoles._id': { $in: roles } }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    return Meteor.roleAssignment.find(selector, filter)\n  },\n\n  /**\n   * Deprecated. Use `getScopesForUser` instead.\n   *\n   * @method getGroupsForUser\n   * @static\n   * @deprecated\n   */\n  getGroupsForUser: function (...args) {\n    if (!getGroupsForUserDeprecationWarning) {\n      getGroupsForUserDeprecationWarning = true\n      console && console.warn('getGroupsForUser has been deprecated. Use getScopesForUser instead.')\n    }\n\n    return Roles.getScopesForUser(...args)\n  },\n\n  /**\n   * Retrieve users scopes, if any.\n   *\n   * @method getScopesForUser\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} [roles] Name of roles to restrict scopes to.\n   *\n   * @return {Array} Array of user's scopes, unsorted.\n   * @static\n   */\n  getScopesForUser: function (user, roles) {\n    let id\n\n    if (roles && !Array.isArray(roles)) roles = [roles]\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id,\n      scope: { $ne: null }\n    }\n\n    if (roles) {\n      selector['inheritedRoles._id'] = { $in: roles }\n    }\n\n    const scopes = Meteor.roleAssignment.find(selector, { fields: { scope: 1 } }).fetch().map(obi => obi.scope)\n\n    return [...new Set(scopes)]\n  },\n\n  /**\n   * Rename a scope.\n   *\n   * Roles assigned with a given scope are changed to be under the new scope.\n   *\n   * @method renameScope\n   * @param {String} oldName Old name of a scope.\n   * @param {String} newName New name of a scope.\n   * @static\n   */\n  renameScope: function (oldName, newName) {\n    let count\n\n    Roles._checkScopeName(oldName)\n    Roles._checkScopeName(newName)\n\n    if (oldName === newName) return\n\n    do {\n      count = Meteor.roleAssignment.update({\n        scope: oldName\n      }, {\n        $set: {\n          scope: newName\n        }\n      }, { multi: true })\n    } while (count > 0)\n  },\n\n  /**\n   * Remove a scope.\n   *\n   * Roles assigned with a given scope are removed.\n   *\n   * @method removeScope\n   * @param {String} name The name of a scope.\n   * @static\n   */\n  removeScope: function (name) {\n    Roles._checkScopeName(name)\n\n    Meteor.roleAssignment.remove({ scope: name })\n  },\n\n  /**\n   * Throw an exception if `roleName` is an invalid role name.\n   *\n   * @method _checkRoleName\n   * @param {String} roleName A role name to match against.\n   * @private\n   * @static\n   */\n  _checkRoleName: function (roleName) {\n    if (!roleName || typeof roleName !== 'string' || roleName.trim() !== roleName) {\n      throw new Error('Invalid role name \\'' + roleName + '\\'.')\n    }\n  },\n\n  /**\n   * Find out if a role is an ancestor of another role.\n   *\n   * WARNING: If you check this on the client, please make sure all roles are published.\n   *\n   * @method isParentOf\n   * @param {String} parentRoleName The role you want to research.\n   * @param {String} childRoleName The role you expect to be among the children of parentRoleName.\n   * @static\n   */\n  isParentOf: function (parentRoleName, childRoleName) {\n    if (parentRoleName === childRoleName) {\n      return true\n    }\n\n    if (parentRoleName == null || childRoleName == null) {\n      return false\n    }\n\n    Roles._checkRoleName(parentRoleName)\n    Roles._checkRoleName(childRoleName)\n\n    let rolesToCheck = [parentRoleName]\n    while (rolesToCheck.length !== 0) {\n      const roleName = rolesToCheck.pop()\n\n      if (roleName === childRoleName) {\n        return true\n      }\n\n      const role = Meteor.roles.findOne({ _id: roleName })\n\n      // This should not happen, but this is a problem to address at some other time.\n      if (!role) continue\n\n      rolesToCheck = rolesToCheck.concat(role.children.map(r => r._id))\n    }\n\n    return false\n  },\n\n  /**\n   * Normalize options.\n   *\n   * @method _normalizeOptions\n   * @param {Object} options Options to normalize.\n   * @return {Object} Normalized options.\n   * @private\n   * @static\n   */\n  _normalizeOptions: function (options) {\n    options = options === undefined ? {} : options\n\n    if (options === null || typeof options === 'string') {\n      options = { scope: options }\n    }\n\n    options.scope = Roles._normalizeScopeName(options.scope)\n\n    return options\n  },\n\n  /**\n   * Normalize scope name.\n   *\n   * @method _normalizeScopeName\n   * @param {String} scopeName A scope name to normalize.\n   * @return {String} Normalized scope name.\n   * @private\n   * @static\n   */\n  _normalizeScopeName: function (scopeName) {\n    // map undefined and null to null\n    if (scopeName == null) {\n      return null\n    } else {\n      return scopeName\n    }\n  },\n\n  /**\n   * Throw an exception if `scopeName` is an invalid scope name.\n   *\n   * @method _checkRoleName\n   * @param {String} scopeName A scope name to match against.\n   * @private\n   * @static\n   */\n  _checkScopeName: function (scopeName) {\n    if (scopeName === null) return\n\n    if (\n      !scopeName ||\n      typeof scopeName !== 'string' ||\n      scopeName.trim() !== scopeName\n    ) {\n      throw new Error(`Invalid scope name '${scopeName}'.`)\n    }\n  }\n})\n", "/* global Roles */\nimport { Meteor } from 'meteor/meteor'\nimport { Mongo } from 'meteor/mongo'\n\n/**\n * Provides functions related to user authorization. Compatible with built-in Meteor accounts packages.\n *\n * Roles are accessible throgh `Meteor.roles` collection and documents consist of:\n *  - `_id`: role name\n *  - `children`: list of subdocuments:\n *    - `_id`\n *\n * Children list elements are subdocuments so that they can be easier extended in the future or by plugins.\n *\n * Roles can have multiple parents and can be children (subroles) of multiple roles.\n *\n * Example: `{_id: 'admin', children: [{_id: 'editor'}]}`\n *\n * The assignment of a role to a user is stored in a collection, accessible through `Meteor.roleAssignment`.\n * It's documents consist of\n *  - `_id`: Internal MongoDB id\n *  - `role`: A role object which got assigned. Usually only contains the `_id` property\n *  - `user`: A user object, usually only contains the `_id` property\n *  - `scope`: scope name\n *  - `inheritedRoles`: A list of all the roles objects inherited by the assigned role.\n *\n * @module Roles\n */\nexport const RolesCollection = new Mongo.Collection('roles')\n\nif (!Meteor.roles) {\n  Meteor.roles = RolesCollection\n}\n\nexport const RoleAssignmentCollection = new Mongo.Collection('role-assignment')\n\nif (!Meteor.roleAssignment) {\n  Meteor.roleAssignment = RoleAssignmentCollection\n}\n\n/**\n * @class Roles\n */\nif (typeof Roles === 'undefined') {\n  Roles = {} // eslint-disable-line no-global-assign\n}\n\nlet getGroupsForUserDeprecationWarning = false\n\n/**\n * Helper, resolves async some\n * @param {*} arr\n * @param {*} predicate\n * @returns {Promise<Boolean>}\n */\nconst asyncSome = async (arr, predicate) => {\n  for (const e of arr) {\n    if (await predicate(e)) return true\n  }\n  return false\n}\n\nObject.assign(Roles, {\n  /**\n   * Used as a global group (now scope) name. Not used anymore.\n   *\n   * @property GLOBAL_GROUP\n   * @static\n   * @deprecated\n   */\n  GLOBAL_GROUP: null,\n\n  /**\n   * Create a new role.\n   *\n   * @method createRoleAsync\n   * @param {String} roleName Name of role.\n   * @param {Object} [options] Options:\n   *   - `unlessExists`: if `true`, exception will not be thrown in the role already exists\n   * @return {Promise<String>} ID of the new role or null.\n   * @static\n   */\n  createRoleAsync: async function (roleName, options) {\n    Roles._checkRoleName(roleName)\n\n    options = Object.assign(\n      {\n        unlessExists: false\n      },\n      options\n    )\n\n    let insertedId = null\n\n    const existingRole = await Meteor.roles.findOneAsync({ _id: roleName })\n\n    if (existingRole) {\n      await Meteor.roles.updateAsync(\n        { _id: roleName },\n        { $setOnInsert: { children: [] } }\n      )\n      return null\n    } else {\n      insertedId = await Meteor.roles.insertAsync({\n        _id: roleName,\n        children: []\n      })\n    }\n\n    if (!insertedId) {\n      if (options.unlessExists) return null\n      throw new Error(\"Role '\" + roleName + \"' already exists.\")\n    }\n\n    return insertedId\n  },\n\n  /**\n   * Delete an existing role.\n   *\n   * If the role is set for any user, it is automatically unset.\n   *\n   * @method deleteRoleAsync\n   * @param {String} roleName Name of role.\n   * @returns {Promise}\n   * @static\n   */\n  deleteRoleAsync: async function (roleName) {\n    let roles\n    let inheritedRoles\n\n    Roles._checkRoleName(roleName)\n\n    // Remove all assignments\n    await Meteor.roleAssignment.removeAsync({\n      'role._id': roleName\n    })\n\n    do {\n      // For all roles who have it as a dependency ...\n      roles = await Roles._getParentRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: roleName })\n      )\n\n      for (const r of await Meteor.roles\n        .find({ _id: { $in: roles } })\n        .fetchAsync()) {\n        await Meteor.roles.updateAsync(\n          {\n            _id: r._id\n          },\n          {\n            $pull: {\n              children: {\n                _id: roleName\n              }\n            }\n          }\n        )\n\n        inheritedRoles = await Roles._getInheritedRoleNamesAsync(\n          await Meteor.roles.findOneAsync({ _id: r._id })\n        )\n        await Meteor.roleAssignment.updateAsync(\n          {\n            'role._id': r._id\n          },\n          {\n            $set: {\n              inheritedRoles: [r._id, ...inheritedRoles].map((r2) => ({\n                _id: r2\n              }))\n            }\n          },\n          { multi: true }\n        )\n      }\n    } while (roles.length > 0)\n\n    // And finally remove the role itself\n    await Meteor.roles.removeAsync({ _id: roleName })\n  },\n\n  /**\n   * Rename an existing role.\n   *\n   * @method renameRoleAsync\n   * @param {String} oldName Old name of a role.\n   * @param {String} newName New name of a role.\n   * @returns {Promise}\n   * @static\n   */\n  renameRoleAsync: async function (oldName, newName) {\n    let count\n\n    Roles._checkRoleName(oldName)\n    Roles._checkRoleName(newName)\n\n    if (oldName === newName) return\n\n    const role = await Meteor.roles.findOneAsync({ _id: oldName })\n\n    if (!role) {\n      throw new Error(\"Role '\" + oldName + \"' does not exist.\")\n    }\n\n    role._id = newName\n\n    await Meteor.roles.insertAsync(role)\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          'role._id': oldName\n        },\n        {\n          $set: {\n            'role._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          'inheritedRoles._id': oldName\n        },\n        {\n          $set: {\n            'inheritedRoles.$._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    do {\n      count = await Meteor.roles.updateAsync(\n        {\n          'children._id': oldName\n        },\n        {\n          $set: {\n            'children.$._id': newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n\n    await Meteor.roles.removeAsync({ _id: oldName })\n  },\n\n  /**\n   * Add role parent to roles.\n   *\n   * Previous parents are kept (role can have multiple parents). For users which have the\n   * parent role set, new subroles are added automatically.\n   *\n   * @method addRolesToParentAsync\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @static\n   */\n  addRolesToParentAsync: async function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      await Roles._addRoleToParentAsync(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _addRoleToParentAsync\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _addRoleToParentAsync: async function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // query to get role's children\n    const role = await Meteor.roles.findOneAsync({ _id: roleName })\n\n    if (!role) {\n      throw new Error(`Role '${roleName}' does not exist.`)\n    }\n\n    // detect cycles\n    if ((await Roles._getInheritedRoleNamesAsync(role)).includes(parentName)) {\n      throw new Error(\n        `Roles '${roleName}' and '${parentName}' would form a cycle.`\n      )\n    }\n\n    const count = await Meteor.roles.updateAsync(\n      {\n        _id: parentName,\n        'children._id': {\n          $ne: role._id\n        }\n      },\n      {\n        $push: {\n          children: {\n            _id: role._id\n          }\n        }\n      }\n    )\n\n    // if there was no change, parent role might not exist, or role is\n    // already a sub-role; in any case we do not have anything more to do\n    if (!count) return\n\n    await Meteor.roleAssignment.updateAsync(\n      {\n        'inheritedRoles._id': parentName\n      },\n      {\n        $push: {\n          inheritedRoles: {\n            $each: [\n              role._id,\n              ...(await Roles._getInheritedRoleNamesAsync(role))\n            ].map((r) => ({ _id: r }))\n          }\n        }\n      },\n      { multi: true }\n    )\n  },\n\n  /**\n   * Remove role parent from roles.\n   *\n   * Other parents are kept (role can have multiple parents). For users which have the\n   * parent role set, removed subrole is removed automatically.\n   *\n   * @method removeRolesFromParentAsync\n   * @param {Array|String} rolesNames Name(s) of role(s).\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @static\n   */\n  removeRolesFromParentAsync: async function (rolesNames, parentName) {\n    // ensure arrays\n    if (!Array.isArray(rolesNames)) rolesNames = [rolesNames]\n\n    for (const roleName of rolesNames) {\n      await Roles._removeRoleFromParentAsync(roleName, parentName)\n    }\n  },\n\n  /**\n   * @method _removeRoleFromParentAsync\n   * @param {String} roleName Name of role.\n   * @param {String} parentName Name of parent role.\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _removeRoleFromParentAsync: async function (roleName, parentName) {\n    Roles._checkRoleName(roleName)\n    Roles._checkRoleName(parentName)\n\n    // check for role existence\n    // this would not really be needed, but we are trying to match addRolesToParent\n    const role = await Meteor.roles.findOneAsync(\n      { _id: roleName },\n      { fields: { _id: 1 } }\n    )\n\n    if (!role) {\n      throw new Error(`Role '${roleName}' does not exist.`)\n    }\n\n    const count = await Meteor.roles.updateAsync(\n      {\n        _id: parentName\n      },\n      {\n        $pull: {\n          children: {\n            _id: role._id\n          }\n        }\n      }\n    )\n\n    // if there was no change, parent role might not exist, or role was\n    // already not a subrole; in any case we do not have anything more to do\n    if (!count) return\n\n    // For all roles who have had it as a dependency ...\n    const roles = [\n      ...(await Roles._getParentRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: parentName })\n      )),\n      parentName\n    ]\n\n    for (const r of await Meteor.roles\n      .find({ _id: { $in: roles } })\n      .fetchAsync()) {\n      const inheritedRoles = await Roles._getInheritedRoleNamesAsync(\n        await Meteor.roles.findOneAsync({ _id: r._id })\n      )\n      await Meteor.roleAssignment.updateAsync(\n        {\n          'role._id': r._id,\n          'inheritedRoles._id': role._id\n        },\n        {\n          $set: {\n            inheritedRoles: [r._id, ...inheritedRoles].map((r2) => ({\n              _id: r2\n            }))\n          }\n        },\n        { multi: true }\n      )\n    }\n  },\n\n  /**\n   * Add users to roles.\n   *\n   * Adds roles to existing roles for each user.\n   *\n   * @example\n   *     Roles.addUsersToRolesAsync(userId, 'admin')\n   *     Roles.addUsersToRolesAsync(userId, ['view-secrets'], 'example.com')\n   *     Roles.addUsersToRolesAsync([user1, user2], ['user','editor'])\n   *     Roles.addUsersToRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method addUsersToRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  addUsersToRolesAsync: async function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        ifExists: false\n      },\n      options\n    )\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n\n      for (const role of roles) {\n        await Roles._addUserToRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Set users' roles.\n   *\n   * Replaces all existing roles with a new set of roles.\n   *\n   * @example\n   *     await Roles.setUserRolesAsync(userId, 'admin')\n   *     await Roles.setUserRolesAsync(userId, ['view-secrets'], 'example.com')\n   *     await Roles.setUserRolesAsync([user1, user2], ['user','editor'])\n   *     await Roles.setUserRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')\n   *\n   * @method setUserRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if `true`, remove all roles the user has, of any scope, if `false`, only the one in the same scope\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  setUserRolesAsync: async function (users, roles, options) {\n    let id\n\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        ifExists: false,\n        anyScope: false\n      },\n      options\n    )\n\n    for (const user of users) {\n      if (typeof user === 'object') {\n        id = user._id\n      } else {\n        id = user\n      }\n      // we first clear all roles for the user\n      const selector = { 'user._id': id }\n      if (!options.anyScope) {\n        selector.scope = options.scope\n      }\n\n      await Meteor.roleAssignment.removeAsync(selector)\n\n      // and then add all\n      for (const role of roles) {\n        await Roles._addUserToRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Add one user to one role.\n   *\n   * @method _addUserToRoleAsync\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `ifExists`: if `true`, do not throw an exception if the role does not exist\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _addUserToRoleAsync: async function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) {\n      return\n    }\n\n    const role = await Meteor.roles.findOneAsync(\n      { _id: roleName },\n      { fields: { children: 1 } }\n    )\n\n    if (!role) {\n      if (options.ifExists) {\n        return []\n      } else {\n        throw new Error(\"Role '\" + roleName + \"' does not exist.\")\n      }\n    }\n\n    // This might create duplicates, because we don't have a unique index, but that's all right. In case there are two, withdrawing the role will effectively kill them both.\n    // TODO revisit this\n    /* const res = await RoleAssignmentCollection.upsertAsync(\n      {\n        \"user._id\": userId,\n        \"role._id\": roleName,\n        scope: options.scope,\n      },\n      {\n        $setOnInsert: {\n          user: { _id: userId },\n          role: { _id: roleName },\n          scope: options.scope,\n        },\n      }\n    ); */\n    const existingAssignment = await Meteor.roleAssignment.findOneAsync({\n      'user._id': userId,\n      'role._id': roleName,\n      scope: options.scope\n    })\n\n    let insertedId\n    let res\n    if (existingAssignment) {\n      await Meteor.roleAssignment.updateAsync(existingAssignment._id, {\n        $set: {\n          user: { _id: userId },\n          role: { _id: roleName },\n          scope: options.scope\n        }\n      })\n\n      res = await Meteor.roleAssignment.findOneAsync(existingAssignment._id)\n    } else {\n      insertedId = await Meteor.roleAssignment.insertAsync({\n        user: { _id: userId },\n        role: { _id: roleName },\n        scope: options.scope\n      })\n    }\n\n    if (insertedId) {\n      await Meteor.roleAssignment.updateAsync(\n        { _id: insertedId },\n        {\n          $set: {\n            inheritedRoles: [\n              roleName,\n              ...(await Roles._getInheritedRoleNamesAsync(role))\n            ].map((r) => ({ _id: r }))\n          }\n        }\n      )\n\n      res = await Meteor.roleAssignment.findOneAsync({ _id: insertedId })\n    }\n    res.insertedId = insertedId // For backward compatibility\n\n    return res\n  },\n\n  /**\n   * Returns an array of role names the given role name is a child of.\n   *\n   * @example\n   *     Roles._getParentRoleNamesAsync({ _id: 'admin', children; [] })\n   *\n   * @method _getParentRoleNamesAsync\n   * @param {object} role The role object\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _getParentRoleNamesAsync: async function (role) {\n    if (!role) {\n      return []\n    }\n\n    const parentRoles = new Set([role._id])\n\n    for (const roleName of parentRoles) {\n      for (const parentRole of await Meteor.roles\n        .find({ 'children._id': roleName })\n        .fetchAsync()) {\n        parentRoles.add(parentRole._id)\n      }\n    }\n\n    parentRoles.delete(role._id)\n\n    return [...parentRoles]\n  },\n\n  /**\n   * Returns an array of role names the given role name is a parent of.\n   *\n   * @example\n   *     Roles._getInheritedRoleNames({ _id: 'admin', children; [] })\n   *\n   * @method _getInheritedRoleNames\n   * @param {object} role The role object\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _getInheritedRoleNamesAsync: async function (role) {\n    const inheritedRoles = new Set()\n    const nestedRoles = new Set([role])\n\n    for (const r of nestedRoles) {\n      const roles = await Meteor.roles\n        .find(\n          { _id: { $in: r.children.map((r) => r._id) } },\n          { fields: { children: 1 } }\n        )\n        .fetchAsync()\n\n      for (const r2 of roles) {\n        inheritedRoles.add(r2._id)\n        nestedRoles.add(r2)\n      }\n    }\n\n    return [...inheritedRoles]\n  },\n\n  /**\n   * Remove users from assigned roles.\n   *\n   * @example\n   *     await Roles.removeUsersFromRolesAsync(userId, 'admin')\n   *     await Roles.removeUsersFromRolesAsync([userId, user2], ['editor'])\n   *     await Roles.removeUsersFromRolesAsync(userId, ['user'], 'group1')\n   *\n   * @method removeUsersFromRolesAsync\n   * @param {Array|String} users User ID(s) or object(s) with an `_id` field.\n   * @param {Array|String} roles Name(s) of roles to remove users from. Roles have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @returns {Promise}\n   *\n   * Alternatively, it can be a scope name string.\n   * @static\n   */\n  removeUsersFromRolesAsync: async function (users, roles, options) {\n    if (!users) throw new Error(\"Missing 'users' param.\")\n    if (!roles) throw new Error(\"Missing 'roles' param.\")\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure arrays\n    if (!Array.isArray(users)) users = [users]\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    for (const user of users) {\n      if (!user) return\n\n      for (const role of roles) {\n        let id\n        if (typeof user === 'object') {\n          id = user._id\n        } else {\n          id = user\n        }\n\n        await Roles._removeUserFromRoleAsync(id, role, options)\n      }\n    }\n  },\n\n  /**\n   * Remove one user from one role.\n   *\n   * @method _removeUserFromRoleAsync\n   * @param {String} userId The user ID.\n   * @param {String} roleName Name of the role to add the user to. The role have to exist.\n   * @param {Object} options Options:\n   *   - `scope`: name of the scope, or `null` for the global role\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   * @returns {Promise}\n   * @private\n   * @static\n   */\n  _removeUserFromRoleAsync: async function (userId, roleName, options) {\n    Roles._checkRoleName(roleName)\n    Roles._checkScopeName(options.scope)\n\n    if (!userId) return\n\n    const selector = {\n      'user._id': userId,\n      'role._id': roleName\n    }\n\n    if (!options.anyScope) {\n      selector.scope = options.scope\n    }\n\n    await Meteor.roleAssignment.removeAsync(selector)\n  },\n\n  /**\n   * Check if user has specified roles.\n   *\n   * @example\n   *     // global roles\n   *     await Roles.userIsInRoleAsync(user, 'admin')\n   *     await Roles.userIsInRoleAsync(user, ['admin','editor'])\n   *     await Roles.userIsInRoleAsync(userId, 'admin')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'])\n   *\n   *     // scope roles (global roles are still checked)\n   *     await Roles.userIsInRoleAsync(user, 'admin', 'group1')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], 'group1')\n   *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], {scope: 'group1'})\n   *\n   * @method userIsInRoleAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} roles Name of role or an array of roles to check against. If array,\n   *                             will return `true` if user is in _any_ role.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope; if supplied, limits check to just that scope\n   *     the user's global roles will always be checked whether scope is specified or not\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Promise<Boolean>} `true` if user is in _any_ of the target roles\n   * @static\n   */\n  userIsInRoleAsync: async function (user, roles, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    roles = roles.filter((r) => r != null)\n\n    if (!roles.length) return false\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign(\n      {\n        anyScope: false\n      },\n      options\n    )\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return false\n    if (typeof id !== 'string') return false\n\n    const selector = {\n      'user._id': id\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope, null] }\n    }\n\n    const res = await asyncSome(roles, async (roleName) => {\n      selector['inheritedRoles._id'] = roleName\n      const out =\n        (await Meteor.roleAssignment.countDocuments(selector, { limit: 1 })) > 0\n      return out\n    })\n\n    return res\n  },\n\n  /**\n   * Retrieve user's roles.\n   *\n   * @method getRolesForUserAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of scope to provide roles for; if not specified, global roles are returned\n   *   - `anyScope`: if set, role can be in any scope (`scope` and `onlyAssigned` options are ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `onlyAssigned`: return only assigned roles and not automatically inferred (like subroles)\n   *   - `fullObjects`: return full roles objects (`true`) or just names (`false`) (`onlyAssigned` option is ignored) (default `false`)\n   *     If you have a use-case for this option, please file a feature-request. You shouldn't need to use it as it's\n   *     result strongly dependent on the internal data structure of this plugin.\n   *\n   * Alternatively, it can be a scope name string.\n   * @return {Promise<Array>} Array of user's roles, unsorted.\n   * @static\n   */\n  getRolesForUserAsync: async function (user, options) {\n    let id\n\n    options = Roles._normalizeOptions(options)\n\n    Roles._checkScopeName(options.scope)\n\n    options = Object.assign({\n      fullObjects: false,\n      onlyAssigned: false,\n      anyScope: false,\n      onlyScoped: false\n    }, options)\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id\n    }\n\n    const filter = {\n      fields: { 'inheritedRoles._id': 1 }\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    if (options.onlyAssigned) {\n      delete filter.fields['inheritedRoles._id']\n      filter.fields['role._id'] = 1\n    }\n\n    if (options.fullObjects) {\n      delete filter.fields\n    }\n\n    const roles = await Meteor.roleAssignment.find(selector, filter).fetchAsync()\n\n    if (options.fullObjects) {\n      return roles\n    }\n\n    return [\n      ...new Set(\n        roles.reduce((rev, current) => {\n          if (current.inheritedRoles) {\n            return rev.concat(current.inheritedRoles.map((r) => r._id))\n          } else if (current.role) {\n            rev.push(current.role._id)\n          }\n          return rev\n        }, [])\n      )\n    ]\n  },\n\n  /**\n   * Retrieve cursor of all existing roles.\n   *\n   * @method getAllRoles\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `RolesCollection.find(query, options)`.\n   * @return {Cursor} Cursor of existing roles.\n   * @static\n   */\n  getAllRoles: function (queryOptions) {\n    queryOptions = queryOptions || { sort: { _id: 1 } }\n\n    return Meteor.roles.find({}, queryOptions)\n  },\n\n  /**\n   * Retrieve all users who are in target role.\n   *\n   * Options:\n   *\n   * @method getUsersInRoleAsync\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `onlyScoped`: if set, only roles in the specified scope are returned\n   *   - `queryOptions`: options which are passed directly\n   *     through to `Meteor.users.find(query, options)`\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [queryOptions] Options which are passed directly\n   *                                through to `Meteor.users.find(query, options)`\n   * @return {Promise<Cursor>} Cursor of users in roles.\n   * @static\n   */\n  getUsersInRoleAsync: async function (roles, options, queryOptions) {\n    const ids = (\n      await Roles.getUserAssignmentsForRole(roles, options).fetchAsync()\n    ).map((a) => a.user._id)\n\n    return Meteor.users.find(\n      { _id: { $in: ids } },\n      (options && options.queryOptions) || queryOptions || {}\n    )\n  },\n\n  /**\n   * Retrieve all assignments of a user which are for the target role.\n   *\n   * Options:\n   *\n   * @method getUserAssignmentsForRole\n   * @param {Array|String} roles Name of role or an array of roles. If array, users\n   *                             returned will have at least one of the roles\n   *                             specified but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *   - `queryOptions`: options which are passed directly\n   *     through to `RoleAssignmentCollection.find(query, options)`\n\n   * Alternatively, it can be a scope name string.\n   * @return {Cursor} Cursor of user assignments for roles.\n   * @static\n   */\n  getUserAssignmentsForRole: function (roles, options) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign(\n      {\n        anyScope: false,\n        queryOptions: {}\n      },\n      options\n    )\n\n    return Roles._getUsersInRoleCursor(roles, options, options.queryOptions)\n  },\n\n  /**\n   * @method _getUsersInRoleCursor\n   * @param {Array|String} roles Name of role or an array of roles. If array, ids of users are\n   *                             returned which have at least one of the roles\n   *                             assigned but need not have _all_ roles.\n   *                             Roles do not have to exist.\n   * @param {Object|String} [options] Options:\n   *   - `scope`: name of the scope to restrict roles to; user's global\n   *     roles will also be checked\n   *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)\n   *\n   * Alternatively, it can be a scope name string.\n   * @param {Object} [filter] Options which are passed directly\n   *                                through to `RoleAssignmentCollection.find(query, options)`\n   * @return {Object} Cursor to the assignment documents\n   * @private\n   * @static\n   */\n  _getUsersInRoleCursor: function (roles, options, filter) {\n    options = Roles._normalizeOptions(options)\n\n    options = Object.assign(\n      {\n        anyScope: false,\n        onlyScoped: false\n      },\n      options\n    )\n\n    // ensure array to simplify code\n    if (!Array.isArray(roles)) roles = [roles]\n\n    Roles._checkScopeName(options.scope)\n\n    filter = Object.assign(\n      {\n        fields: { 'user._id': 1 }\n      },\n      filter\n    )\n\n    const selector = {\n      'inheritedRoles._id': { $in: roles }\n    }\n\n    if (!options.anyScope) {\n      selector.scope = { $in: [options.scope] }\n\n      if (!options.onlyScoped) {\n        selector.scope.$in.push(null)\n      }\n    }\n\n    return Meteor.roleAssignment.find(selector, filter)\n  },\n\n  /**\n   * Deprecated. Use `getScopesForUser` instead.\n   *\n   * @method getGroupsForUserAsync\n   * @returns {Promise<Array>}\n   * @static\n   * @deprecated\n   */\n  getGroupsForUserAsync: async function (...args) {\n    if (!getGroupsForUserDeprecationWarning) {\n      getGroupsForUserDeprecationWarning = true\n      console &&\n        console.warn(\n          'getGroupsForUser has been deprecated. Use getScopesForUser instead.'\n        )\n    }\n\n    return await Roles.getScopesForUser(...args)\n  },\n\n  /**\n   * Retrieve users scopes, if any.\n   *\n   * @method getScopesForUserAsync\n   * @param {String|Object} user User ID or an actual user object.\n   * @param {Array|String} [roles] Name of roles to restrict scopes to.\n   *\n   * @return {Promise<Array>} Array of user's scopes, unsorted.\n   * @static\n   */\n  getScopesForUserAsync: async function (user, roles) {\n    let id\n\n    if (roles && !Array.isArray(roles)) roles = [roles]\n\n    if (user && typeof user === 'object') {\n      id = user._id\n    } else {\n      id = user\n    }\n\n    if (!id) return []\n\n    const selector = {\n      'user._id': id,\n      scope: { $ne: null }\n    }\n\n    if (roles) {\n      selector['inheritedRoles._id'] = { $in: roles }\n    }\n\n    const scopes = (\n      await Meteor.roleAssignment\n        .find(selector, { fields: { scope: 1 } })\n        .fetchAsync()\n    ).map((obi) => obi.scope)\n\n    return [...new Set(scopes)]\n  },\n\n  /**\n   * Rename a scope.\n   *\n   * Roles assigned with a given scope are changed to be under the new scope.\n   *\n   * @method renameScopeAsync\n   * @param {String} oldName Old name of a scope.\n   * @param {String} newName New name of a scope.\n   * @returns {Promise}\n   * @static\n   */\n  renameScopeAsync: async function (oldName, newName) {\n    let count\n\n    Roles._checkScopeName(oldName)\n    Roles._checkScopeName(newName)\n\n    if (oldName === newName) return\n\n    do {\n      count = await Meteor.roleAssignment.updateAsync(\n        {\n          scope: oldName\n        },\n        {\n          $set: {\n            scope: newName\n          }\n        },\n        { multi: true }\n      )\n    } while (count > 0)\n  },\n\n  /**\n   * Remove a scope.\n   *\n   * Roles assigned with a given scope are removed.\n   *\n   * @method removeScopeAsync\n   * @param {String} name The name of a scope.\n   * @returns {Promise}\n   * @static\n   */\n  removeScopeAsync: async function (name) {\n    Roles._checkScopeName(name)\n\n    await Meteor.roleAssignment.removeAsync({ scope: name })\n  },\n\n  /**\n   * Throw an exception if `roleName` is an invalid role name.\n   *\n   * @method _checkRoleName\n   * @param {String} roleName A role name to match against.\n   * @private\n   * @static\n   */\n  _checkRoleName: function (roleName) {\n    if (\n      !roleName ||\n      typeof roleName !== 'string' ||\n      roleName.trim() !== roleName\n    ) {\n      throw new Error(`Invalid role name '${roleName}'.`)\n    }\n  },\n\n  /**\n   * Find out if a role is an ancestor of another role.\n   *\n   * WARNING: If you check this on the client, please make sure all roles are published.\n   *\n   * @method isParentOfAsync\n   * @param {String} parentRoleName The role you want to research.\n   * @param {String} childRoleName The role you expect to be among the children of parentRoleName.\n   * @returns {Promise}\n   * @static\n   */\n  isParentOfAsync: async function (parentRoleName, childRoleName) {\n    if (parentRoleName === childRoleName) {\n      return true\n    }\n\n    if (parentRoleName == null || childRoleName == null) {\n      return false\n    }\n\n    Roles._checkRoleName(parentRoleName)\n    Roles._checkRoleName(childRoleName)\n\n    let rolesToCheck = [parentRoleName]\n    while (rolesToCheck.length !== 0) {\n      const roleName = rolesToCheck.pop()\n\n      if (roleName === childRoleName) {\n        return true\n      }\n\n      const role = await Meteor.roles.findOneAsync({ _id: roleName })\n\n      // This should not happen, but this is a problem to address at some other time.\n      if (!role) continue\n\n      rolesToCheck = rolesToCheck.concat(role.children.map((r) => r._id))\n    }\n\n    return false\n  },\n\n  /**\n   * Normalize options.\n   *\n   * @method _normalizeOptions\n   * @param {Object} options Options to normalize.\n   * @return {Object} Normalized options.\n   * @private\n   * @static\n   */\n  _normalizeOptions: function (options) {\n    options = options === undefined ? {} : options\n\n    // TODO Number will error out on scope validation, we can either error it out here\n    // or make it into a string and hence a valid input.\n    if (options === null || typeof options === 'string' || typeof options === 'number') {\n      options = { scope: options }\n    }\n\n    options.scope = Roles._normalizeScopeName(options.scope)\n\n    return options\n  },\n\n  /**\n   * Normalize scope name.\n   *\n   * @method _normalizeScopeName\n   * @param {String} scopeName A scope name to normalize.\n   * @return {String} Normalized scope name.\n   * @private\n   * @static\n   */\n  _normalizeScopeName: function (scopeName) {\n    // map undefined and null to null\n    if (scopeName == null) {\n      return null\n    } else {\n      return scopeName\n    }\n  },\n\n  /**\n   * Throw an exception if `scopeName` is an invalid scope name.\n   *\n   * @method _checkScopeName\n   * @param {String} scopeName A scope name to match against.\n   * @private\n   * @static\n   */\n  _checkScopeName: function (scopeName) {\n    if (scopeName === null) return\n\n    if (\n      !scopeName ||\n      typeof scopeName !== 'string' ||\n      scopeName.trim() !== scopeName\n    ) {\n      throw new Error(`Invalid scope name '${scopeName}'.`)\n    }\n  }\n})\n", "/* global Roles, localStorage */\n\n// //////////////////////////////////////////////////////////\n// Debugging helpers\n//\n// Run this in your browser console to turn on debugging\n// for this package:\n//\n//   localstorage.setItem('Roles.debug', true)\n//\n\nRoles.debug = false\n\ntry {\n  if (localStorage) {\n    const temp = localStorage.getItem('Roles.debug')\n\n    if (typeof temp !== 'undefined') {\n      Roles.debug = !!temp\n    }\n  }\n} catch (ex) {\n  // ignore: accessing localStorage when its disabled throws\n  // https://github.com/meteor/meteor/issues/5759\n}\n", "/* global Meteor, Roles, Match, Package */\n\n/**\n * Convenience functions for use on client.\n *\n * NOTE: You must restrict user actions on the server-side; any\n * client-side checks are strictly for convenience and must not be\n * trusted.\n *\n * @module UIHelpers\n */\n\n// //////////////////////////////////////////////////////////\n// UI helpers\n//\n// Use a semi-private variable rather than declaring UI\n// helpers directly so that we can unit test the helpers.\n// XXX For some reason, the UI helpers are not registered\n// before the tests run.\n//\nRoles._uiHelpers = {\n\n  /**\n   * UI helper to check if current user is in at least one\n   * of the target roles.  For use in client-side templates.\n   *\n   * @example\n   *     {{#if isInRole 'admin'}}\n   *     {{/if}}\n   *\n   *     {{#if isInRole 'editor,user'}}\n   *     {{/if}}\n   *\n   *     {{#if isInRole 'editor,user' 'scope1'}}\n   *     {{/if}}\n   *\n   * @method isInRole\n   * @param {String} role Name of role or comma-seperated list of roles.\n   * @param {String} [scope] Optional, name of scope to check.\n   * @return {Boolean} `true` if current user is in at least one of the target roles.\n   * @static\n   * @for UIHelpers\n   */\n  isInRole: function (role, scope) {\n    const user = Meteor.user()\n    const comma = (role || '').indexOf(',')\n    let roles\n\n    if (!user) return false\n    if (!Match.test(role, String)) return false\n\n    if (comma !== -1) {\n      roles = role.split(',').reduce(function (memo, r) {\n        if (!r) {\n          return memo\n        }\n        memo.push(r)\n        return memo\n      }, [])\n    } else {\n      roles = [role]\n    }\n\n    if (Match.test(scope, String)) {\n      return Roles.userIsInRole(user, roles, scope)\n    }\n\n    return Roles.userIsInRole(user, roles)\n  }\n}\n\n// //////////////////////////////////////////////////////////\n// Register UI helpers\n//\n\nif (Roles.debug && console.debug) {\n  console.debug('[roles] Roles.debug =', Roles.debug)\n}\n\nif (typeof Package.blaze !== 'undefined' &&\n    typeof Package.blaze.Blaze !== 'undefined' &&\n    typeof Package.blaze.Blaze.registerHelper === 'function') {\n  Object.entries(Roles._uiHelpers).forEach(([name, func]) => {\n    if (Roles.debug && console.debug) {\n      console.debug('[roles] registering Blaze helper \\'' + name + '\\'')\n    }\n    Package.blaze.Blaze.registerHelper(name, func)\n  })\n}\n"]}