{"version": 3, "sources": ["meteor://💻app/packages/random/main_server.js", "meteor://💻app/packages/random/AbstractRandomGenerator.js", "meteor://💻app/packages/random/AleaRandomGenerator.js", "meteor://💻app/packages/random/NodeRandomGenerator.js", "meteor://💻app/packages/random/createAleaGenerator.js", "meteor://💻app/packages/random/createRandom.js"], "names": ["module", "export", "Random", "NodeRandomGenerator", "link", "default", "v", "createRandom", "__reifyWaitForDeps__", "__reify_async_result__", "_reifyError", "self", "async", "RandomGenerator", "Meteor", "UNMISTAKABLE_CHARS", "BASE64_CHARS", "fraction", "Error", "hexString", "digits", "_randomString", "charsCount", "alphabet", "result", "i", "choice", "id", "undefined", "secret", "arrayOrString", "index", "Math", "floor", "length", "substr", "AleaRandomGenerator", "Alea", "seeds", "<PERSON><PERSON>", "n", "mash", "data", "toString", "charCodeAt", "h", "version", "s0", "s1", "s2", "c", "Date", "random", "t", "uint32", "fract53", "args", "constructor", "arguments", "alea", "crypto", "numerator", "Number", "parseInt", "numBytes", "ceil", "bytes", "randomBytes", "e", "pseudoRandomBytes", "substring", "createAleaGenerator", "height", "window", "innerHeight", "document", "documentElement", "clientHeight", "body", "width", "innerWidth", "clientWidth", "agent", "navigator", "userAgent", "createAleaGeneratorWithGeneratedSeed", "generator", "createWithSeeds", "_len", "Array", "_key", "insecure"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,MAAM,CAAC;MAACC,MAAM,EAACA,CAAA,KAAIA;IAAM,CAAC,CAAC;IAAC,IAAIC,mBAAmB;IAACH,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACH,mBAAmB,GAACG,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,YAAY;IAACP,MAAM,CAACI,IAAI,CAAC,gBAAgB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACC,YAAY,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAOzQ,MAAMN,MAAM,GAAGK,YAAY,CAAC,IAAIJ,mBAAmB,CAAC,CAAC,CAAC;IAACM,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICP9DZ,MAAM,CAACC,MAAM,CAAC;MAACI,OAAO,EAACA,CAAA,KAAIQ;IAAe,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACd,MAAM,CAACI,IAAI,CAAC,eAAe,EAAC;MAACU,MAAMA,CAACR,CAAC,EAAC;QAACQ,MAAM,GAACR,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAWzK,MAAMO,kBAAkB,GAAG,yDAAyD;IACpF,MAAMC,YAAY,GAAG,sDAAsD,GACzE,cAAc;;IAEhB;IACA;IACA;IACA;IACA;IACA;IACe,MAAMH,eAAe,CAAC;MAEnC;AACF;AACA;AACA;AACA;MACEI,QAAQA,CAAA,EAAI;QACV,MAAM,IAAIC,KAAK,gCAAgC,CAAC;MAClD;;MAEA;AACF;AACA;AACA;AACA;AACA;MACEC,SAASA,CAAEC,MAAM,EAAE;QACjB,OAAO,IAAI,CAACC,aAAa,CAACD,MAAM,EAAE,kBAAkB,CAAC;MACvD;MAEAC,aAAaA,CAAEC,UAAU,EAAEC,QAAQ,EAAE;QACnC,IAAIC,MAAM,GAAG,EAAE;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,EAAEG,CAAC,EAAE,EAAE;UACnCD,MAAM,IAAI,IAAI,CAACE,MAAM,CAACH,QAAQ,CAAC;QACjC;QACA,OAAOC,MAAM;MACf;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEG,EAAEA,CAAEL,UAAU,EAAE;QACd;QACA;QACA,IAAIA,UAAU,KAAKM,SAAS,EAAE;UAC5BN,UAAU,GAAG,EAAE;QACjB;QAEA,OAAO,IAAI,CAACD,aAAa,CAACC,UAAU,EAAEP,kBAAkB,CAAC;MAC3D;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEc,MAAMA,CAAEP,UAAU,EAAE;QAClB;QACA;QACA,IAAIA,UAAU,KAAKM,SAAS,EAAE;UAC5BN,UAAU,GAAG,EAAE;QACjB;QAEA,OAAO,IAAI,CAACD,aAAa,CAACC,UAAU,EAAEN,YAAY,CAAC;MACrD;;MAEA;AACF;AACA;AACA;AACA;AACA;MACEU,MAAMA,CAAEI,aAAa,EAAE;QACrB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAChB,QAAQ,CAAC,CAAC,GAAGa,aAAa,CAACI,MAAM,CAAC;QAChE,IAAI,OAAOJ,aAAa,KAAK,QAAQ,EAAE;UACrC,OAAOA,aAAa,CAACK,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;QACvC;QACA,OAAOD,aAAa,CAACC,KAAK,CAAC;MAC7B;IACF;IAACtB,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICpGDZ,MAAM,CAACC,MAAM,CAAC;MAACI,OAAO,EAACA,CAAA,KAAI+B;IAAmB,CAAC,CAAC;IAAC,IAAIvB,eAAe;IAACb,MAAM,CAACI,IAAI,CAAC,2BAA2B,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACO,eAAe,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAE5M;IACA;IACA;IACA,SAAS6B,IAAIA,CAACC,KAAK,EAAE;MACnB,SAASC,IAAIA,CAAA,EAAG;QACd,IAAIC,CAAC,GAAG,UAAU;QAElB,MAAMC,IAAI,GAAIC,IAAI,IAAK;UACrBA,IAAI,GAAGA,IAAI,CAACC,QAAQ,CAAC,CAAC;UACtB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,IAAI,CAACR,MAAM,EAAET,CAAC,EAAE,EAAE;YACpCe,CAAC,IAAIE,IAAI,CAACE,UAAU,CAACnB,CAAC,CAAC;YACvB,IAAIoB,CAAC,GAAG,mBAAmB,GAAGL,CAAC;YAC/BA,CAAC,GAAGK,CAAC,KAAK,CAAC;YACXA,CAAC,IAAIL,CAAC;YACNK,CAAC,IAAIL,CAAC;YACNA,CAAC,GAAGK,CAAC,KAAK,CAAC;YACXA,CAAC,IAAIL,CAAC;YACNA,CAAC,IAAIK,CAAC,GAAG,WAAW,CAAC,CAAC;UACxB;UACA,OAAO,CAACL,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC;QAC7C,CAAC;QAEDC,IAAI,CAACK,OAAO,GAAG,UAAU;QACzB,OAAOL,IAAI;MACb;MAEA,IAAIM,EAAE,GAAG,CAAC;MACV,IAAIC,EAAE,GAAG,CAAC;MACV,IAAIC,EAAE,GAAG,CAAC;MACV,IAAIC,CAAC,GAAG,CAAC;MACT,IAAIZ,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;QACtBI,KAAK,GAAG,CAAC,CAAC,IAAIa,IAAI,CAAD,CAAC,CAAC;MACrB;MACA,IAAIV,IAAI,GAAGF,IAAI,CAAC,CAAC;MACjBQ,EAAE,GAAGN,IAAI,CAAC,GAAG,CAAC;MACdO,EAAE,GAAGP,IAAI,CAAC,GAAG,CAAC;MACdQ,EAAE,GAAGR,IAAI,CAAC,GAAG,CAAC;MAEd,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,KAAK,CAACJ,MAAM,EAAET,CAAC,EAAE,EAAE;QACrCsB,EAAE,IAAIN,IAAI,CAACH,KAAK,CAACb,CAAC,CAAC,CAAC;QACpB,IAAIsB,EAAE,GAAG,CAAC,EAAE;UACVA,EAAE,IAAI,CAAC;QACT;QACAC,EAAE,IAAIP,IAAI,CAACH,KAAK,CAACb,CAAC,CAAC,CAAC;QACpB,IAAIuB,EAAE,GAAG,CAAC,EAAE;UACVA,EAAE,IAAI,CAAC;QACT;QACAC,EAAE,IAAIR,IAAI,CAACH,KAAK,CAACb,CAAC,CAAC,CAAC;QACpB,IAAIwB,EAAE,GAAG,CAAC,EAAE;UACVA,EAAE,IAAI,CAAC;QACT;MACF;MACAR,IAAI,GAAG,IAAI;MAEX,MAAMW,MAAM,GAAGA,CAAA,KAAM;QACnB,MAAMC,CAAC,GAAI,OAAO,GAAGN,EAAE,GAAKG,CAAC,GAAG,sBAAuB,CAAC,CAAC;QACzDH,EAAE,GAAGC,EAAE;QACPA,EAAE,GAAGC,EAAE;QACP,OAAOA,EAAE,GAAGI,CAAC,IAAIH,CAAC,GAAGG,CAAC,GAAG,CAAC,CAAC;MAC7B,CAAC;MAEDD,MAAM,CAACE,MAAM,GAAG,MAAMF,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;MAC9CA,MAAM,CAACG,OAAO,GAAG,MAAMH,MAAM,CAAC,CAAC,GACxB,CAACA,MAAM,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,sBAAuB,CAAC,CAAC;;MAE5DA,MAAM,CAACN,OAAO,GAAG,UAAU;MAC3BM,MAAM,CAACI,IAAI,GAAGlB,KAAK;MACnB,OAAOc,MAAM;IACf;;IAEA;IACA;IACA;IACA;IACe,MAAMhB,mBAAmB,SAASvB,eAAe,CAAC;MAC/D4C,WAAWA,CAAA,EAAuB;QAAA,IAArB;UAAEnB,KAAK,GAAG;QAAG,CAAC,GAAAoB,SAAA,CAAAxB,MAAA,QAAAwB,SAAA,QAAA9B,SAAA,GAAA8B,SAAA,MAAG,CAAC,CAAC;QAC9B,KAAK,CAAC,CAAC;QACP,IAAI,CAACpB,KAAK,EAAE;UACV,MAAM,IAAIpB,KAAK,CAAC,sCAAsC,CAAC;QACzD;QACA,IAAI,CAACyC,IAAI,GAAGtB,IAAI,CAACC,KAAK,CAAC;MACzB;;MAEA;AACF;AACA;AACA;AACA;MACErB,QAAQA,CAAA,EAAI;QACV,OAAO,IAAI,CAAC0C,IAAI,CAAC,CAAC;MACpB;IACF;IAAClD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC7FDZ,MAAM,CAACC,MAAM,CAAC;MAACI,OAAO,EAACA,CAAA,KAAIF;IAAmB,CAAC,CAAC;IAAC,IAAIyD,MAAM;IAAC5D,MAAM,CAACI,IAAI,CAAC,QAAQ,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACsD,MAAM,GAACtD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIO,eAAe;IAACb,MAAM,CAACI,IAAI,CAAC,2BAA2B,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACO,eAAe,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAGvP,MAAML,mBAAmB,SAASU,eAAe,CAAC;MAC/D;AACF;AACA;AACA;AACA;MACEI,QAAQA,CAAA,EAAI;QACV,MAAM4C,SAAS,GAAGC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAC5C,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACxD,OAAO0C,SAAS,GAAG,sBAAsB,CAAC,CAAC;MAC7C;;MAEA;AACF;AACA;AACA;AACA;AACA;MACE1C,SAASA,CAAEC,MAAM,EAAE;QACjB,MAAM4C,QAAQ,GAAGhC,IAAI,CAACiC,IAAI,CAAC7C,MAAM,GAAG,CAAC,CAAC;QACtC,IAAI8C,KAAK;QACT;QACA;QACA,IAAI;UACFA,KAAK,GAAGN,MAAM,CAACO,WAAW,CAACH,QAAQ,CAAC;QACtC,CAAC,CAAC,OAAOI,CAAC,EAAE;UACV;UACAF,KAAK,GAAGN,MAAM,CAACS,iBAAiB,CAACL,QAAQ,CAAC;QAC5C;QACA,MAAMxC,MAAM,GAAG0C,KAAK,CAACvB,QAAQ,CAAC,KAAK,CAAC;QACpC;QACA;QACA,OAAOnB,MAAM,CAAC8C,SAAS,CAAC,CAAC,EAAElD,MAAM,CAAC;MACpC;IACF;IAACX,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICpCDZ,MAAM,CAACC,MAAM,CAAC;MAACI,OAAO,EAACA,CAAA,KAAIkE;IAAmB,CAAC,CAAC;IAAC,IAAInC,mBAAmB;IAACpC,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAAC8B,mBAAmB,GAAC9B,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAEhN;IACA;;IAEA;IACA,MAAMgE,MAAM,GAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,WAAW,IAC5D,OAAOC,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACC,eAAe,IACxBD,QAAQ,CAACC,eAAe,CAACC,YAAa,IACzC,OAAOF,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACG,IAAI,IACbH,QAAQ,CAACG,IAAI,CAACD,YAAa,IAC/B,CAAC;IAEP,MAAME,KAAK,GAAI,OAAON,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACO,UAAU,IAC1D,OAAOL,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACC,eAAe,IACxBD,QAAQ,CAACC,eAAe,CAACK,WAAY,IACxC,OAAON,QAAQ,KAAK,WAAW,IAC5BA,QAAQ,CAACG,IAAI,IACbH,QAAQ,CAACG,IAAI,CAACG,WAAY,IAC9B,CAAC;IAEP,MAAMC,KAAK,GAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,IAAK,EAAE;IAE9D,SAASb,mBAAmBA,CAAA,EAAG;MAC5C,OAAO,IAAInC,mBAAmB,CAAC;QAC7BE,KAAK,EAAE,CAAC,IAAIa,IAAI,CAAD,CAAC,EAAEqB,MAAM,EAAEO,KAAK,EAAEG,KAAK,EAAElD,IAAI,CAACoB,MAAM,CAAC,CAAC;MACvD,CAAC,CAAC;IACJ;IAAC3C,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC9BDZ,MAAM,CAACC,MAAM,CAAC;MAACI,OAAO,EAACA,CAAA,KAAIE;IAAY,CAAC,CAAC;IAAC,IAAI6B,mBAAmB;IAACpC,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAAC8B,mBAAmB,GAAC9B,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI+E,oCAAoC;IAACrF,MAAM,CAACI,IAAI,CAAC,uBAAuB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAAC+E,oCAAoC,GAAC/E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAG/T,SAASD,YAAYA,CAAC+E,SAAS,EAAE;MAC9C;MACA;MACAA,SAAS,CAACC,eAAe,GAAG,YAAc;QAAA,SAAAC,IAAA,GAAA9B,SAAA,CAAAxB,MAAA,EAAVI,KAAK,OAAAmD,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;UAALpD,KAAK,CAAAoD,IAAA,IAAAhC,SAAA,CAAAgC,IAAA;QAAA;QACnC,IAAIpD,KAAK,CAACJ,MAAM,KAAK,CAAC,EAAE;UACtB,MAAM,IAAIhB,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QACA,OAAO,IAAIkB,mBAAmB,CAAC;UAAEE;QAAM,CAAC,CAAC;MAC3C,CAAC;;MAED;MACA;MACAgD,SAAS,CAACK,QAAQ,GAAGN,oCAAoC,CAAC,CAAC;MAE3D,OAAOC,SAAS;IAClB;IAAC7E,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/random.js", "sourcesContent": ["// We use cryptographically strong PRNGs (crypto.getRandomBytes())\n// When using crypto.getRandomValues(), our primitive is hexString(),\n// from which we construct fraction().\n\nimport NodeRandomGenerator from './NodeRandomGenerator';\nimport createRandom from './createRandom';\n\nexport const Random = createRandom(new NodeRandomGenerator());\n", "// We use cryptographically strong PRNGs (crypto.getRandomBytes() on the server,\n// window.crypto.getRandomValues() in the browser) when available. If these\n// PRNGs fail, we fall back to the Alea PRNG, which is not cryptographically\n// strong, and we seed it with various sources such as the date, Math.random,\n// and window size on the client.  When using crypto.getRandomValues(), our\n// primitive is hexString(), from which we construct fraction(). When using\n// window.crypto.getRandomValues() or alea, the primitive is fraction and we use\n// that to construct hex string.\n\nimport { Meteor } from 'meteor/meteor';\n\nconst UNMISTAKABLE_CHARS = '23456789ABCDEFGHJKLMNPQRSTWXYZabcdefghijkmnopqrstuvwxyz';\nconst BASE64_CHARS = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ' +\n  '0123456789-_';\n\n// `type` is one of `RandomGenerator.Type` as defined below.\n//\n// options:\n// - seeds: (required, only for RandomGenerator.Type.ALEA) an array\n//   whose items will be `toString`ed and used as the seed to the Alea\n//   algorithm\nexport default class RandomGenerator {\n\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    throw new Error(`Unknown random generator type`);\n  }\n\n  /**\n   * @name Random.hexString\n   * @summary Return a random string of `n` hexadecimal digits.\n   * @locus Anywhere\n   * @param {Number} n Length of the string\n   */\n  hexString (digits) {\n    return this._randomString(digits, '0123456789abcdef');\n  }\n\n  _randomString (charsCount, alphabet) {\n    let result = '';\n    for (let i = 0; i < charsCount; i++) {\t\n      result += this.choice(alphabet);\n    }\n    return result;\n  }\n\n  /**\n   * @name Random.id\n   * @summary Return a unique identifier, such as `\"Jjwjg6gouWLXhMGKW\"`, that is\n   * likely to be unique in the whole world.\n   * @locus Anywhere\n   * @param {Number} [n] Optional length of the identifier in characters\n   *   (defaults to 17)\n   */\n  id (charsCount) {\n    // 17 characters is around 96 bits of entropy, which is the amount of\n    // state in the Alea PRNG.\n    if (charsCount === undefined) {\n      charsCount = 17;\n    }\n\n    return this._randomString(charsCount, UNMISTAKABLE_CHARS);\n  }\n\n  /**\n   * @name Random.secret\n   * @summary Return a random string of printable characters with 6 bits of\n   * entropy per character. Use `Random.secret` for security-critical secrets\n   * that are intended for machine, rather than human, consumption.\n   * @locus Anywhere\n   * @param {Number} [n] Optional length of the secret string (defaults to 43\n   *   characters, or 256 bits of entropy)\n   */\n  secret (charsCount) {\n    // Default to 256 bits of entropy, or 43 characters at 6 bits per\n    // character.\n    if (charsCount === undefined) {\n      charsCount = 43;\n    }\n\n    return this._randomString(charsCount, BASE64_CHARS);\n  }\n\n  /**\n   * @name Random.choice\n   * @summary Return a random element of the given array or string.\n   * @locus Anywhere\n   * @param {Array|String} arrayOrString Array or string to choose from\n   */\n  choice (arrayOrString) {\n    const index = Math.floor(this.fraction() * arrayOrString.length);\n    if (typeof arrayOrString === 'string') {\n      return arrayOrString.substr(index, 1);\n    }\n    return arrayOrString[index];\n  }\n}\n", "import RandomGenerator from './AbstractRandomGenerator';\n\n// Alea PRNG, which is not cryptographically strong\n// see http://baagoe.org/en/wiki/Better_random_numbers_for_javascript\n// for a full discussion and Alea implementation.\nfunction Alea(seeds) {\n  function Mash() {\n    let n = 0xefc8249d;\n\n    const mash = (data) => {\n      data = data.toString();\n      for (let i = 0; i < data.length; i++) {\n        n += data.charCodeAt(i);\n        let h = 0.02519603282416938 * n;\n        n = h >>> 0;\n        h -= n;\n        h *= n;\n        n = h >>> 0;\n        h -= n;\n        n += h * 0x100000000; // 2^32\n      }\n      return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n    };\n\n    mash.version = 'Mash 0.9';\n    return mash;\n  }\n\n  let s0 = 0;\n  let s1 = 0;\n  let s2 = 0;\n  let c = 1;\n  if (seeds.length === 0) {\n    seeds = [+new Date];\n  }\n  let mash = Mash();\n  s0 = mash(' ');\n  s1 = mash(' ');\n  s2 = mash(' ');\n\n  for (let i = 0; i < seeds.length; i++) {\n    s0 -= mash(seeds[i]);\n    if (s0 < 0) {\n      s0 += 1;\n    }\n    s1 -= mash(seeds[i]);\n    if (s1 < 0) {\n      s1 += 1;\n    }\n    s2 -= mash(seeds[i]);\n    if (s2 < 0) {\n      s2 += 1;\n    }\n  }\n  mash = null;\n\n  const random = () => {\n    const t = (2091639 * s0) + (c * 2.3283064365386963e-10); // 2^-32\n    s0 = s1;\n    s1 = s2;\n    return s2 = t - (c = t | 0);\n  };\n\n  random.uint32 = () => random() * 0x100000000; // 2^32\n  random.fract53 = () => random() +\n        ((random() * 0x200000 | 0) * 1.1102230246251565e-16); // 2^-53\n\n  random.version = 'Alea 0.9';\n  random.args = seeds;\n  return random;\n}\n\n// options:\n// - seeds: an array\n//   whose items will be `toString`ed and used as the seed to the Alea\n//   algorithm\nexport default class AleaRandomGenerator extends RandomGenerator {\n  constructor ({ seeds = [] } = {}) {\n    super();\n    if (!seeds) {\n      throw new Error('No seeds were provided for Alea PRNG');\n    }\n    this.alea = Alea(seeds);\n  }\n\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    return this.alea();\n  }\n}\n", "import crypto from 'crypto';\nimport RandomGenerator from './AbstractRandomGenerator';\n\nexport default class NodeRandomGenerator extends RandomGenerator {\n  /**\n   * @name Random.fraction\n   * @summary Return a number between 0 and 1, like `Math.random`.\n   * @locus Anywhere\n   */\n  fraction () {\n    const numerator = Number.parseInt(this.hexString(8), 16);\n    return numerator * 2.3283064365386963e-10; // 2^-3;\n  }\n\n  /**\n   * @name Random.hexString\n   * @summary Return a random string of `n` hexadecimal digits.\n   * @locus Anywhere\n   * @param {Number} n Length of the string\n   */\n  hexString (digits) {\n    const numBytes = Math.ceil(digits / 2);\n    let bytes;\n    // Try to get cryptographically strong randomness. Fall back to\n    // non-cryptographically strong if not available.\n    try {\n      bytes = crypto.randomBytes(numBytes);\n    } catch (e) {\n      // XXX should re-throw any error except insufficient entropy\n      bytes = crypto.pseudoRandomBytes(numBytes);\n    }\n    const result = bytes.toString('hex');\n    // If the number of digits is odd, we'll have generated an extra 4 bits\n    // of randomness, so we need to trim the last digit.\n    return result.substring(0, digits);\n  }\n}\n", "import AleaRandomGenerator from './AleaRandomGenerator';\n\n// instantiate RNG.  Heuristically collect entropy from various sources when a\n// cryptographic PRNG isn't available.\n\n// client sources\nconst height = (typeof window !== 'undefined' && window.innerHeight) ||\n      (typeof document !== 'undefined'\n       && document.documentElement\n       && document.documentElement.clientHeight) ||\n      (typeof document !== 'undefined'\n       && document.body\n       && document.body.clientHeight) ||\n      1;\n\nconst width = (typeof window !== 'undefined' && window.innerWidth) ||\n      (typeof document !== 'undefined'\n       && document.documentElement\n       && document.documentElement.clientWidth) ||\n      (typeof document !== 'undefined'\n       && document.body\n       && document.body.clientWidth) ||\n      1;\n\nconst agent = (typeof navigator !== 'undefined' && navigator.userAgent) || '';\n\nexport default function createAleaGenerator() {\n  return new AleaRandomGenerator({\n    seeds: [new Date, height, width, agent, Math.random()],\n  });\n}\n", "import AleaRandomGenerator from './AleaRandomGenerator'\nimport createAleaGeneratorWithGeneratedSeed from './createAleaGenerator';\n\nexport default function createRandom(generator) {\n  // Create a non-cryptographically secure PRNG with a given seed (using\n  // the Alea algorithm)\n  generator.createWithSeeds = (...seeds) => {\n    if (seeds.length === 0) {\n      throw new Error('No seeds were provided');\n    }\n    return new AleaRandomGenerator({ seeds });\n  };\n\n  // Used like `Random`, but much faster and not cryptographically\n  // secure\n  generator.insecure = createAleaGeneratorWithGeneratedSeed();\n\n  return generator;\n}\n"]}