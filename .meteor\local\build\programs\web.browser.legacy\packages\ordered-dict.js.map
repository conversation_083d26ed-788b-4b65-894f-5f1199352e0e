{"version": 3, "sources": ["meteor://💻app/packages/ordered-dict/ordered_dict.js"], "names": ["_regeneratorRuntime", "module", "link", "default", "v", "export", "OrderedDict", "element", "key", "value", "next", "prev", "_this", "_dict", "Object", "create", "_first", "_last", "_size", "_len", "arguments", "length", "args", "Array", "_key", "_stringify", "shift", "x", "for<PERSON>ach", "kv", "putBefore", "_proto", "prototype", "_k", "empty", "size", "_linkEltIn", "elt", "_linkEltOut", "item", "before", "Error", "append", "remove", "get", "has", "hasOwnProperty", "call", "iter", "context", "undefined", "i", "b", "BREAK", "forEachAsync", "asyncIter", "_args", "async", "forEachAsync$", "_context", "awrap", "sent", "abrupt", "stop", "Promise", "first", "firstValue", "last", "lastValue", "moveBefore", "eltBefore", "indexOf", "_this2", "ret", "k", "_checkRep", "_this3", "keys"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA,mBAAmB;AAACC,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAC;EAACC,OAAO,EAAC,SAAAA,CAASC,CAAC,EAAC;IAACJ,mBAAmB,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAAhHH,MAAM,CAACI,MAAM,CAAC;EAACC,WAAW,EAAC,SAAAA,CAAA,EAAU;IAAC,OAAOA,WAAW;EAAA;AAAC,CAAC,CAAC;AAA3D;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACvC,OAAO;IACLH,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEA;EACR,CAAC;AACH;AAAC,IAEYL,WAAW;EACtB,SAAAA,YAAA,EAAqB;IAAA,IAAAM,KAAA;IACnB,IAAI,CAACC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAChC,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAJFC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAMjB,IAAI,OAAOF,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;MACjC,IAAI,CAACG,UAAU,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC;IAChC,CAAC,MAAM;MACL,IAAI,CAACD,UAAU,GAAG,UAAUE,CAAC,EAAE;QAAE,OAAOA,CAAC;MAAE,CAAC;IAC9C;IAEAL,IAAI,CAACM,OAAO,CAAC,UAAAC,EAAE;MAAA,OAAIjB,KAAI,CAACkB,SAAS,CAACD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;IAAA,EAAC;EACxD;;EAEA;EACA;EAAA,IAAAE,MAAA,GAAAzB,WAAA,CAAA0B,SAAA;EAAAD,MAAA,CACAE,EAAE;IAAF,SAAAA,EAAEA,CAACzB,GAAG,EAAE;MACN,OAAO,GAAG,GAAG,IAAI,CAACiB,UAAU,CAACjB,GAAG,CAAC;IACnC;IAAC,OAFDyB,EAAE;EAAA;EAAAF,MAAA,CAIFG,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,CAAC,IAAI,CAAClB,MAAM;IACrB;IAAC,OAFDkB,KAAK;EAAA;EAAAH,MAAA,CAILI,IAAI;IAAJ,SAAAA,IAAIA,CAAA,EAAG;MACL,OAAO,IAAI,CAACjB,KAAK;IACnB;IAAC,OAFDiB,IAAI;EAAA;EAAAJ,MAAA,CAIJK,UAAU;IAAV,SAAAA,UAAUA,CAACC,GAAG,EAAE;MACd,IAAI,CAACA,GAAG,CAAC3B,IAAI,EAAE;QACb2B,GAAG,CAAC1B,IAAI,GAAG,IAAI,CAACM,KAAK;QACrB,IAAI,IAAI,CAACA,KAAK,EACZ,IAAI,CAACA,KAAK,CAACP,IAAI,GAAG2B,GAAG;QACvB,IAAI,CAACpB,KAAK,GAAGoB,GAAG;MAClB,CAAC,MAAM;QACLA,GAAG,CAAC1B,IAAI,GAAG0B,GAAG,CAAC3B,IAAI,CAACC,IAAI;QACxB0B,GAAG,CAAC3B,IAAI,CAACC,IAAI,GAAG0B,GAAG;QACnB,IAAIA,GAAG,CAAC1B,IAAI,EACV0B,GAAG,CAAC1B,IAAI,CAACD,IAAI,GAAG2B,GAAG;MACvB;MACA,IAAI,IAAI,CAACrB,MAAM,KAAK,IAAI,IAAI,IAAI,CAACA,MAAM,KAAKqB,GAAG,CAAC3B,IAAI,EAClD,IAAI,CAACM,MAAM,GAAGqB,GAAG;IACrB;IAAC,OAdDD,UAAU;EAAA;EAAAL,MAAA,CAgBVO,WAAW;IAAX,SAAAA,WAAWA,CAACD,GAAG,EAAE;MACf,IAAIA,GAAG,CAAC3B,IAAI,EACV2B,GAAG,CAAC3B,IAAI,CAACC,IAAI,GAAG0B,GAAG,CAAC1B,IAAI;MAC1B,IAAI0B,GAAG,CAAC1B,IAAI,EACV0B,GAAG,CAAC1B,IAAI,CAACD,IAAI,GAAG2B,GAAG,CAAC3B,IAAI;MAC1B,IAAI2B,GAAG,KAAK,IAAI,CAACpB,KAAK,EACpB,IAAI,CAACA,KAAK,GAAGoB,GAAG,CAAC1B,IAAI;MACvB,IAAI0B,GAAG,KAAK,IAAI,CAACrB,MAAM,EACrB,IAAI,CAACA,MAAM,GAAGqB,GAAG,CAAC3B,IAAI;IAC1B;IAAC,OATD4B,WAAW;EAAA;EAAAP,MAAA,CAWXD,SAAS;IAAT,SAAAA,SAASA,CAACtB,GAAG,EAAE+B,IAAI,EAAEC,MAAM,EAAE;MAC3B,IAAI,IAAI,CAAC3B,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC,EAC1B,MAAM,IAAIiC,KAAK,CAAC,OAAO,GAAGjC,GAAG,GAAG,iCAAiC,CAAC;MACpE,IAAI6B,GAAG,GAAGG,MAAM,GACdjC,OAAO,CAACC,GAAG,EAAE+B,IAAI,EAAE,IAAI,CAAC1B,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACO,MAAM,CAAC,CAAC,CAAC,GAC/CjC,OAAO,CAACC,GAAG,EAAE+B,IAAI,EAAE,IAAI,CAAC;MAC1B,IAAI,OAAOF,GAAG,CAAC3B,IAAI,KAAK,WAAW,EACjC,MAAM,IAAI+B,KAAK,CAAC,4CAA4C,CAAC;MAC/D,IAAI,CAACL,UAAU,CAACC,GAAG,CAAC;MACpB,IAAI,CAACxB,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC,GAAG6B,GAAG;MAC9B,IAAI,CAACnB,KAAK,EAAE;IACd;IAAC,OAXDY,SAAS;EAAA;EAAAC,MAAA,CAaTW,MAAM;IAAN,SAAAA,MAAMA,CAAClC,GAAG,EAAE+B,IAAI,EAAE;MAChB,IAAI,CAACT,SAAS,CAACtB,GAAG,EAAE+B,IAAI,EAAE,IAAI,CAAC;IACjC;IAAC,OAFDG,MAAM;EAAA;EAAAX,MAAA,CAINY,MAAM;IAAN,SAAAA,MAAMA,CAACnC,GAAG,EAAE;MACV,IAAI6B,GAAG,GAAG,IAAI,CAACxB,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC;MAClC,IAAI,OAAO6B,GAAG,KAAK,WAAW,EAC5B,MAAM,IAAII,KAAK,CAAC,OAAO,GAAGjC,GAAG,GAAG,6BAA6B,CAAC;MAChE,IAAI,CAAC8B,WAAW,CAACD,GAAG,CAAC;MACrB,IAAI,CAACnB,KAAK,EAAE;MACZ,OAAO,IAAI,CAACL,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC;MAC/B,OAAO6B,GAAG,CAAC5B,KAAK;IAClB;IAAC,OARDkC,MAAM;EAAA;EAAAZ,MAAA,CAUNa,GAAG;IAAH,SAAAA,GAAGA,CAACpC,GAAG,EAAE;MACP,IAAI,IAAI,CAACqC,GAAG,CAACrC,GAAG,CAAC,EAAE;QACjB,OAAO,IAAI,CAACK,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC,CAACC,KAAK;MACvC;IACF;IAAC,OAJDmC,GAAG;EAAA;EAAAb,MAAA,CAMHc,GAAG;IAAH,SAAAA,GAAGA,CAACrC,GAAG,EAAE;MACP,OAAOM,MAAM,CAACkB,SAAS,CAACc,cAAc,CAACC,IAAI,CACzC,IAAI,CAAClC,KAAK,EACV,IAAI,CAACoB,EAAE,CAACzB,GAAG,CACb,CAAC;IACH;IAAC,OALDqC,GAAG;EAAA,IAOH;EACA;EAEA;EAAA;EAAAd,MAAA,CACAH,OAAO;IAAP,SAAAA,OAAOA,CAACoB,IAAI,EAAkB;MAAA,IAAhBC,OAAO,GAAA7B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA8B,SAAA,GAAA9B,SAAA,MAAG,IAAI;MAC1B,IAAI+B,CAAC,GAAG,CAAC;MACT,IAAId,GAAG,GAAG,IAAI,CAACrB,MAAM;MACrB,OAAOqB,GAAG,KAAK,IAAI,EAAE;QACnB,IAAIe,CAAC,GAAGJ,IAAI,CAACD,IAAI,CAACE,OAAO,EAAEZ,GAAG,CAAC5B,KAAK,EAAE4B,GAAG,CAAC7B,GAAG,EAAE2C,CAAC,CAAC;QACjD,IAAIC,CAAC,KAAK9C,WAAW,CAAC+C,KAAK,EAAE;QAC7BhB,GAAG,GAAGA,GAAG,CAAC3B,IAAI;QACdyC,CAAC,EAAE;MACL;IACF;IAAC,OATDvB,OAAO;EAAA;EAAAG,MAAA,CAWDuB,YAAY;IAAlB,SAAMA,YAAYA,CAACC,SAAS;MAAA,IAAAN,OAAA;QAAAE,CAAA;QAAAd,GAAA;QAAAe,CAAA;QAAAI,KAAA,GAAApC,SAAA;MAAA,OAAApB,mBAAA,CAAAyD,KAAA;QAAA,SAAAC,cAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAhD,IAAA,GAAAgD,QAAA,CAAAjD,IAAA;YAAA;cAAEuC,OAAO,GAAAO,KAAA,CAAAnC,MAAA,QAAAmC,KAAA,QAAAN,SAAA,GAAAM,KAAA,MAAG,IAAI;cACtCL,CAAC,GAAG,CAAC;cACLd,GAAG,GAAG,IAAI,CAACrB,MAAM;YAAA;cAAA,MACdqB,GAAG,KAAK,IAAI;gBAAAsB,QAAA,CAAAjD,IAAA;gBAAA;cAAA;cAAAiD,QAAA,CAAAjD,IAAA;cAAA,OAAAV,mBAAA,CAAA4D,KAAA,CACDL,SAAS,CAACR,IAAI,CAACE,OAAO,EAAEZ,GAAG,CAAC5B,KAAK,EAAE4B,GAAG,CAAC7B,GAAG,EAAE2C,CAAC,CAAC;YAAA;cAAxDC,CAAC,GAAAO,QAAA,CAAAE,IAAA;cAAA,MACHT,CAAC,KAAK9C,WAAW,CAAC+C,KAAK;gBAAAM,QAAA,CAAAjD,IAAA;gBAAA;cAAA;cAAA,OAAAiD,QAAA,CAAAG,MAAA;YAAA;cAC3BzB,GAAG,GAAGA,GAAG,CAAC3B,IAAI;cACdyC,CAAC,EAAE;cAACQ,QAAA,CAAAjD,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAiD,QAAA,CAAAI,IAAA;UAAA;QAAA;QAAA,OAAAL,aAAA;MAAA,uBAAAM,OAAA;IAAA;IAEP,OATKV,YAAY;EAAA;EAAAvB,MAAA,CAWlBkC,KAAK;IAAL,SAAAA,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAAC/B,KAAK,CAAC,CAAC,EAAE;QAChB;MACF;MACA,OAAO,IAAI,CAAClB,MAAM,CAACR,GAAG;IACxB;IAAC,OALDyD,KAAK;EAAA;EAAAlC,MAAA,CAOLmC,UAAU;IAAV,SAAAA,UAAUA,CAAA,EAAG;MACX,IAAI,IAAI,CAAChC,KAAK,CAAC,CAAC,EAAE;QAChB;MACF;MACA,OAAO,IAAI,CAAClB,MAAM,CAACP,KAAK;IAC1B;IAAC,OALDyD,UAAU;EAAA;EAAAnC,MAAA,CAOVoC,IAAI;IAAJ,SAAAA,IAAIA,CAAA,EAAG;MACL,IAAI,IAAI,CAACjC,KAAK,CAAC,CAAC,EAAE;QAChB;MACF;MACA,OAAO,IAAI,CAACjB,KAAK,CAACT,GAAG;IACvB;IAAC,OALD2D,IAAI;EAAA;EAAApC,MAAA,CAOJqC,SAAS;IAAT,SAAAA,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAAClC,KAAK,CAAC,CAAC,EAAE;QAChB;MACF;MACA,OAAO,IAAI,CAACjB,KAAK,CAACR,KAAK;IACzB;IAAC,OALD2D,SAAS;EAAA;EAAArC,MAAA,CAOTpB,IAAI;IAAJ,SAAAA,IAAIA,CAACH,GAAG,EAAE;MACR,IAAI,IAAI,CAACqC,GAAG,CAACrC,GAAG,CAAC,EAAE;QACjB,IAAI6B,GAAG,GAAG,IAAI,CAACxB,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC;QAClC,IAAI6B,GAAG,CAAC1B,IAAI,EACV,OAAO0B,GAAG,CAAC1B,IAAI,CAACH,GAAG;MACvB;MACA,OAAO,IAAI;IACb;IAAC,OAPDG,IAAI;EAAA;EAAAoB,MAAA,CASJrB,IAAI;IAAJ,SAAAA,IAAIA,CAACF,GAAG,EAAE;MACR,IAAI,IAAI,CAACqC,GAAG,CAACrC,GAAG,CAAC,EAAE;QACjB,IAAI6B,GAAG,GAAG,IAAI,CAACxB,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC;QAClC,IAAI6B,GAAG,CAAC3B,IAAI,EACV,OAAO2B,GAAG,CAAC3B,IAAI,CAACF,GAAG;MACvB;MACA,OAAO,IAAI;IACb;IAAC,OAPDE,IAAI;EAAA;EAAAqB,MAAA,CASJsC,UAAU;IAAV,SAAAA,UAAUA,CAAC7D,GAAG,EAAEgC,MAAM,EAAE;MACtB,IAAIH,GAAG,GAAG,IAAI,CAACxB,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACzB,GAAG,CAAC,CAAC;MAClC,IAAI8D,SAAS,GAAG9B,MAAM,GAAG,IAAI,CAAC3B,KAAK,CAAC,IAAI,CAACoB,EAAE,CAACO,MAAM,CAAC,CAAC,GAAG,IAAI;MAC3D,IAAI,OAAOH,GAAG,KAAK,WAAW,EAAE;QAC9B,MAAM,IAAII,KAAK,CAAC,6BAA6B,CAAC;MAChD;MACA,IAAI,OAAO6B,SAAS,KAAK,WAAW,EAAE;QACpC,MAAM,IAAI7B,KAAK,CAAC,gDAAgD,CAAC;MACnE;MACA,IAAI6B,SAAS,KAAKjC,GAAG,CAAC3B,IAAI;QAAE;QAC1B;MACF;MACA,IAAI,CAAC4B,WAAW,CAACD,GAAG,CAAC;MACrB;MACAA,GAAG,CAAC3B,IAAI,GAAG4D,SAAS;MACpB,IAAI,CAAClC,UAAU,CAACC,GAAG,CAAC;IACtB;IAAC,OAhBDgC,UAAU;EAAA,IAkBV;EAAA;EAAAtC,MAAA,CACAwC,OAAO;IAAP,SAAAA,OAAOA,CAAC/D,GAAG,EAAE;MAAA,IAAAgE,MAAA;MACX,IAAIC,GAAG,GAAG,IAAI;MACd,IAAI,CAAC7C,OAAO,CAAC,UAACxB,CAAC,EAAEsE,CAAC,EAAEvB,CAAC,EAAK;QACxB,IAAIqB,MAAI,CAACvC,EAAE,CAACyC,CAAC,CAAC,KAAKF,MAAI,CAACvC,EAAE,CAACzB,GAAG,CAAC,EAAE;UAC/BiE,GAAG,GAAGtB,CAAC;UACP,OAAO7C,WAAW,CAAC+C,KAAK;QAC1B;QACA;MACF,CAAC,CAAC;MACF,OAAOoB,GAAG;IACZ;IAAC,OAVDF,OAAO;EAAA;EAAAxC,MAAA,CAYP4C,SAAS;IAAT,SAAAA,SAASA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACV9D,MAAM,CAAC+D,IAAI,CAAC,IAAI,CAAChE,KAAK,CAAC,CAACe,OAAO,CAAC,UAAA8C,CAAC,EAAI;QACnC,IAAMtE,CAAC,GAAGwE,MAAI,CAAC/D,KAAK,CAAC6D,CAAC,CAAC;QACvB,IAAItE,CAAC,CAACM,IAAI,KAAKN,CAAC,EAAE;UAChB,MAAM,IAAIqC,KAAK,CAAC,gBAAgB,CAAC;QACnC;QACA,IAAIrC,CAAC,CAACO,IAAI,KAAKP,CAAC,EAAE;UAChB,MAAM,IAAIqC,KAAK,CAAC,gBAAgB,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAAC,OAVDkC,SAAS;EAAA;EAAA,OAAArE,WAAA;AAAA;AAaXA,WAAW,CAAC+C,KAAK,GAAG;EAAC,OAAO,EAAE;AAAI,CAAC,C", "file": "/packages/ordered-dict.js", "sourcesContent": ["// This file defines an ordered dictionary abstraction that is useful for\n// maintaining a dataset backed by observe<PERSON>hanges.  It supports ordering items\n// by specifying the item they now come before.\n\n// The implementation is a dictionary that contains nodes of a doubly-linked\n// list as its values.\n\n// constructs a new element struct\n// next and prev are whole elements, not keys.\nfunction element(key, value, next, prev) {\n  return {\n    key: key,\n    value: value,\n    next: next,\n    prev: prev\n  };\n}\n\nexport class OrderedDict {\n  constructor(...args) {\n    this._dict = Object.create(null);\n    this._first = null;\n    this._last = null;\n    this._size = 0;\n\n    if (typeof args[0] === 'function') {\n      this._stringify = args.shift();\n    } else {\n      this._stringify = function (x) { return x; };\n    }\n\n    args.forEach(kv => this.putBefore(kv[0], kv[1], null));\n  }\n\n  // the \"prefix keys with a space\" thing comes from here\n  // https://github.com/documentcloud/underscore/issues/376#issuecomment-2815649\n  _k(key) {\n    return \" \" + this._stringify(key);\n  }\n\n  empty() {\n    return !this._first;\n  }\n\n  size() {\n    return this._size;\n  }\n\n  _linkEltIn(elt) {\n    if (!elt.next) {\n      elt.prev = this._last;\n      if (this._last)\n        this._last.next = elt;\n      this._last = elt;\n    } else {\n      elt.prev = elt.next.prev;\n      elt.next.prev = elt;\n      if (elt.prev)\n        elt.prev.next = elt;\n    }\n    if (this._first === null || this._first === elt.next)\n      this._first = elt;\n  }\n\n  _linkEltOut(elt) {\n    if (elt.next)\n      elt.next.prev = elt.prev;\n    if (elt.prev)\n      elt.prev.next = elt.next;\n    if (elt === this._last)\n      this._last = elt.prev;\n    if (elt === this._first)\n      this._first = elt.next;\n  }\n\n  putBefore(key, item, before) {\n    if (this._dict[this._k(key)])\n      throw new Error(\"Item \" + key + \" already present in OrderedDict\");\n    var elt = before ?\n      element(key, item, this._dict[this._k(before)]) :\n      element(key, item, null);\n    if (typeof elt.next === \"undefined\")\n      throw new Error(\"could not find item to put this one before\");\n    this._linkEltIn(elt);\n    this._dict[this._k(key)] = elt;\n    this._size++;\n  }\n\n  append(key, item) {\n    this.putBefore(key, item, null);\n  }\n\n  remove(key) {\n    var elt = this._dict[this._k(key)];\n    if (typeof elt === \"undefined\")\n      throw new Error(\"Item \" + key + \" not present in OrderedDict\");\n    this._linkEltOut(elt);\n    this._size--;\n    delete this._dict[this._k(key)];\n    return elt.value;\n  }\n\n  get(key) {\n    if (this.has(key)) {\n      return this._dict[this._k(key)].value;\n    }\n  }\n\n  has(key) {\n    return Object.prototype.hasOwnProperty.call(\n      this._dict,\n      this._k(key)\n    );\n  }\n\n  // Iterate through the items in this dictionary in order, calling\n  // iter(value, key, index) on each one.\n\n  // Stops whenever iter returns OrderedDict.BREAK, or after the last element.\n  forEach(iter, context = null) {\n    var i = 0;\n    var elt = this._first;\n    while (elt !== null) {\n      var b = iter.call(context, elt.value, elt.key, i);\n      if (b === OrderedDict.BREAK) return;\n      elt = elt.next;\n      i++;\n    }\n  }\n\n  async forEachAsync(asyncIter, context = null) {\n    let i = 0;\n    let elt = this._first;\n    while (elt !== null) {\n      const b = await asyncIter.call(context, elt.value, elt.key, i);\n      if (b === OrderedDict.BREAK) return;\n      elt = elt.next;\n      i++;\n    }\n  }\n\n  first() {\n    if (this.empty()) {\n      return;\n    }\n    return this._first.key;\n  }\n\n  firstValue() {\n    if (this.empty()) {\n      return;\n    }\n    return this._first.value;\n  }\n\n  last() {\n    if (this.empty()) {\n      return;\n    }\n    return this._last.key;\n  }\n\n  lastValue() {\n    if (this.empty()) {\n      return;\n    }\n    return this._last.value;\n  }\n\n  prev(key) {\n    if (this.has(key)) {\n      var elt = this._dict[this._k(key)];\n      if (elt.prev)\n        return elt.prev.key;\n    }\n    return null;\n  }\n\n  next(key) {\n    if (this.has(key)) {\n      var elt = this._dict[this._k(key)];\n      if (elt.next)\n        return elt.next.key;\n    }\n    return null;\n  }\n\n  moveBefore(key, before) {\n    var elt = this._dict[this._k(key)];\n    var eltBefore = before ? this._dict[this._k(before)] : null;\n    if (typeof elt === \"undefined\") {\n      throw new Error(\"Item to move is not present\");\n    }\n    if (typeof eltBefore === \"undefined\") {\n      throw new Error(\"Could not find element to move this one before\");\n    }\n    if (eltBefore === elt.next) // no moving necessary\n      return;\n    // remove from its old place\n    this._linkEltOut(elt);\n    // patch into its new place\n    elt.next = eltBefore;\n    this._linkEltIn(elt);\n  }\n\n  // Linear, sadly.\n  indexOf(key) {\n    var ret = null;\n    this.forEach((v, k, i) => {\n      if (this._k(k) === this._k(key)) {\n        ret = i;\n        return OrderedDict.BREAK;\n      }\n      return;\n    });\n    return ret;\n  }\n\n  _checkRep() {\n    Object.keys(this._dict).forEach(k => {\n      const v = this._dict[k];\n      if (v.next === v) {\n        throw new Error(\"Next is a loop\");\n      }\n      if (v.prev === v) {\n        throw new Error(\"Prev is a loop\");\n      }\n    });\n  }\n}\n\nOrderedDict.BREAK = {\"break\": true};\n"]}