{"version": 3, "sources": ["meteor://💻app/packages/ddp-client/server/server.js", "meteor://💻app/packages/ddp-client/common/connection_stream_handlers.js", "meteor://💻app/packages/ddp-client/common/document_processors.js", "meteor://💻app/packages/ddp-client/common/livedata_connection.js", "meteor://💻app/packages/ddp-client/common/message_processors.js", "meteor://💻app/packages/ddp-client/common/method_invoker.js", "meteor://💻app/packages/ddp-client/common/mongo_id_map.js", "meteor://💻app/packages/ddp-client/common/namespace.js"], "names": ["module", "link", "DDP", "__reifyWaitForDeps__", "__reify_async_result__", "_reifyError", "self", "async", "export", "ConnectionStreamHandlers", "DDPCommon", "v", "Meteor", "constructor", "connection", "_connection", "onMessage", "raw_msg", "msg", "parseDDP", "e", "_debug", "_heartbeat", "messageReceived", "testMessageOnConnect", "Object", "keys", "length", "server_id", "_version", "_versionSuggestion", "_routeMessage", "_livedata_connected", "options", "onConnected", "_handleFailedMessage", "respondToPings", "_send", "id", "_livedata_data", "_livedata_nosub", "_livedata_result", "_livedata_error", "_supportedDDPVersions", "indexOf", "version", "_stream", "reconnect", "_force", "description", "disconnect", "_permanent", "_error", "onDDPVersionNegotiationFailure", "onReset", "_buildConnectMessage", "_handleOutstandingMethodsOnReset", "_callOnReconnectAndSendAppropriateOutstandingMethods", "_resendSubscriptions", "_lastSessionId", "session", "support", "blocks", "_outstandingMethodBlocks", "currentMethodBlock", "methods", "filter", "methodInvoker", "sentMessage", "noRetry", "receiveResult", "Error", "shift", "values", "_methodInvokers", "for<PERSON>ach", "invoker", "entries", "_subscriptions", "_ref", "sub", "_sendQueued", "name", "params", "DocumentProcessors", "MongoID", "DiffSequence", "hasOwn", "isEmpty", "_process_added", "updates", "idParse", "serverDoc", "_getServerDoc", "collection", "isExisting", "document", "undefined", "fields", "create", "_id", "_resetStores", "currentDoc", "_stores", "getDoc", "_pushUpdate", "_process_changed", "applyChanges", "_process_removed", "_process_ready", "subs", "subId", "_runWhenAllServerDocsAreFlushed", "subRecord", "ready", "readyCallback", "readyDeps", "changed", "_process_updated", "methodId", "docs", "_documentsWrittenByStub", "written", "JSON", "stringify", "writtenByStubs", "idStringify", "replace", "flushCallbacks", "c", "_serverDocuments", "remove", "callbackInvoker", "dataVisible", "arguments", "call", "push", "serverDocsForCollection", "get", "_objectWithoutProperties", "default", "_objectSpread", "_excluded", "_excluded2", "Connection", "Tracker", "EJSON", "Random", "MethodInvoker", "slice", "last", "MongoIDMap", "MessageProcessors", "url", "heartbeatInterval", "heartbeatTimeout", "npmFayeOptions", "reloadWithOutstanding", "supportedDDPVersions", "SUPPORTED_DDP_VERSIONS", "retry", "bufferedWritesInterval", "bufferedWritesMaxAge", "onReconnect", "ClientStream", "ConnectionError", "headers", "_sockjsOptions", "_dontPrintErrors", "connectTimeoutMs", "_methodHandlers", "_nextMethodId", "_heartbeatInterval", "_heartbeatTimeout", "_afterUpdateCallbacks", "_messagesBufferedUntilQuiescence", "_methodsBlockingQuiescence", "_subsBeingRevived", "_updatesForUnknownStores", "_retryMigrate", "_bufferedWrites", "_bufferedWritesFlushAt", "_bufferedWritesFlushHandle", "_bufferedWritesInterval", "_bufferedWritesMaxAge", "_userId", "_userIdDeps", "Dependency", "isClient", "Package", "reload", "Reload", "_onMigrate", "_readyToMigrate", "_streamHandlers", "onDisconnect", "stop", "isServer", "on", "bindEnvironment", "_messageProcessors", "_documentProcessors", "createStoreMethods", "wrappedStore", "store", "keysOfStore", "method", "registerStoreClient", "queued", "Array", "isArray", "beginUpdate", "update", "endUpdate", "registerStoreServer", "subscribe", "callbacks", "lastPara<PERSON>", "onReady", "pop", "onError", "onStop", "some", "f", "existing", "find", "inactive", "equals", "<PERSON><PERSON><PERSON><PERSON>", "stopCallback", "clone", "handle", "record", "depend", "subscriptionId", "active", "onInvalidate", "afterFlush", "isAsyncCall", "_CurrentMethodInvocation", "_isCallAsyncMethodRunning", "func", "_getIsSimulation", "_ref2", "isFromCallAsync", "alreadyInSimulation", "args", "callback", "apply", "callAsync", "applyAsync", "returnServerResultPromise", "_this$_stubCall", "_stubCall", "stubInvocation", "invocation", "stubOptions", "hasStub", "_saveOriginals", "stubReturnValue", "with<PERSON><PERSON><PERSON>", "_isPromise", "concat", "exception", "_apply", "stubPromise", "_applyAsyncStubInvocation", "promise", "_applyAsync", "then", "o", "serverPromise", "Promise", "resolve", "reject", "catch", "_this$_stubCall2", "currentContext", "_setNewContextAndGetCurrent", "_set", "_ref3", "stubCallValue", "randomSeed", "result", "_returnMethodInvoker", "_retrieveAndStoreOriginals", "message", "throwStubExceptions", "_expectedByTest", "returnStubValue", "err", "_len", "allArgs", "_key", "from", "value", "onResultReceived", "wait", "_addOutstandingMethod", "enclosing", "stub", "isSimulation", "_isFromCallAsync", "defaultReturn", "randomSeedGenerator", "makeRpcSeed", "setUserId", "userId", "MethodInvocation", "_noYieldsAllowed", "_waitingFor<PERSON>uiescence", "_flushBufferedWrites", "saveOriginals", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "originals", "retrieveOriginals", "doc", "<PERSON><PERSON><PERSON><PERSON>", "_unsubscribeAll", "obj", "send", "stringifyDDP", "_lostConnection", "error", "status", "close", "_anyMethodsAreOutstanding", "invokers", "_processOneDataMessage", "messageType", "_prepareBuffersToFlush", "clearTimeout", "writes", "_performWritesServer", "_updates$store$_name", "_name", "storeName", "messages", "CHUNK_SIZE", "i", "chunk", "Math", "min", "process", "nextTick", "_runAfterUpdateCallbacks", "_performWritesClient", "_updates$store$_name2", "_ref5", "runFAfterUpdates", "unflushedServerDocCount", "onServerDocFlush", "serverDocuments", "writtenByStubForAMethodWithSentMessage", "sendMessage", "_outstandingMethodFinished", "firstBlock", "_sendOutstandingMethods", "_maybeMigrate", "m", "_sendOutstandingMethodBlocksMessages", "oldOutstandingMethodBlocks", "_reconnectHook", "each", "Heartbeat", "onTimeout", "sendPing", "start", "reconnectedToPreviousSession", "gotResult", "bufferedMessages", "bufferedMessage", "standardWrite", "Date", "valueOf", "setTimeout", "_liveDataWritesPromise", "finally", "idx", "found", "splice", "reason", "details", "meteorErrorFromMsg", "msgArg", "offendingMessage", "_callback", "_message", "_onResultReceived", "_wait", "_methodResult", "_dataVisible", "_maybeInvokeCallback", "IdMap", "allConnections", "EnvironmentVariable", "_CurrentPublicationInvocation", "_CurrentInvocation", "_CurrentCallAsyncInvocation", "connectionErrorConstructor", "makeErrorType", "ForcedReconnectError", "randomStream", "scope", "RandomStream", "connect", "ret", "Hook", "register", "_allSubscriptionsReady", "every", "conn"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAAA,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;MAACC,GAAG,EAAC;IAAK,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICAjHP,MAAM,CAACQ,MAAM,CAAC;MAACC,wBAAwB,EAACA,CAAA,KAAIA;IAAwB,CAAC,CAAC;IAAC,IAAIC,SAAS;IAACV,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;MAACS,SAASA,CAACC,CAAC,EAAC;QAACD,SAAS,GAACC,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACZ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACW,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIR,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAGzQ,MAAMM,wBAAwB,CAAC;MACpCI,WAAWA,CAACC,UAAU,EAAE;QACtB,IAAI,CAACC,WAAW,GAAGD,UAAU;MAC/B;;MAEA;AACF;AACA;AACA;MACE,MAAME,SAASA,CAACC,OAAO,EAAE;QACvB,IAAIC,GAAG;QACP,IAAI;UACFA,GAAG,GAAGR,SAAS,CAACS,QAAQ,CAACF,OAAO,CAAC;QACnC,CAAC,CAAC,OAAOG,CAAC,EAAE;UACVR,MAAM,CAACS,MAAM,CAAC,6BAA6B,EAAED,CAAC,CAAC;UAC/C;QACF;;QAEA;QACA;QACA,IAAI,IAAI,CAACL,WAAW,CAACO,UAAU,EAAE;UAC/B,IAAI,CAACP,WAAW,CAACO,UAAU,CAACC,eAAe,CAAC,CAAC;QAC/C;QAEA,IAAIL,GAAG,KAAK,IAAI,IAAI,CAACA,GAAG,CAACA,GAAG,EAAE;UAC5B,IAAG,CAACA,GAAG,IAAI,CAACA,GAAG,CAACM,oBAAoB,EAAE;YACpC,IAAIC,MAAM,CAACC,IAAI,CAACR,GAAG,CAAC,CAACS,MAAM,KAAK,CAAC,IAAIT,GAAG,CAACU,SAAS,EAAE;YACpDhB,MAAM,CAACS,MAAM,CAAC,qCAAqC,EAAEH,GAAG,CAAC;UAC3D;UACA;QACF;;QAEA;QACA;QACA,IAAIA,GAAG,CAACA,GAAG,KAAK,WAAW,EAAE;UAC3B,IAAI,CAACH,WAAW,CAACc,QAAQ,GAAG,IAAI,CAACd,WAAW,CAACe,kBAAkB;QACjE;QAEA,MAAM,IAAI,CAACC,aAAa,CAACb,GAAG,CAAC;MAC/B;;MAEA;AACF;AACA;AACA;AACA;MACE,MAAMa,aAAaA,CAACb,GAAG,EAAE;QACvB,QAAQA,GAAG,CAACA,GAAG;UACb,KAAK,WAAW;YACd,MAAM,IAAI,CAACH,WAAW,CAACiB,mBAAmB,CAACd,GAAG,CAAC;YAC/C,IAAI,CAACH,WAAW,CAACkB,OAAO,CAACC,WAAW,CAAC,CAAC;YACtC;UAEF,KAAK,QAAQ;YACX,MAAM,IAAI,CAACC,oBAAoB,CAACjB,GAAG,CAAC;YACpC;UAEF,KAAK,MAAM;YACT,IAAI,IAAI,CAACH,WAAW,CAACkB,OAAO,CAACG,cAAc,EAAE;cAC3C,IAAI,CAACrB,WAAW,CAACsB,KAAK,CAAC;gBAAEnB,GAAG,EAAE,MAAM;gBAAEoB,EAAE,EAAEpB,GAAG,CAACoB;cAAG,CAAC,CAAC;YACrD;YACA;UAEF,KAAK,MAAM;YACT;YACA;UAEF,KAAK,OAAO;UACZ,KAAK,SAAS;UACd,KAAK,SAAS;UACd,KAAK,OAAO;UACZ,KAAK,SAAS;YACZ,MAAM,IAAI,CAACvB,WAAW,CAACwB,cAAc,CAACrB,GAAG,CAAC;YAC1C;UAEF,KAAK,OAAO;YACV,MAAM,IAAI,CAACH,WAAW,CAACyB,eAAe,CAACtB,GAAG,CAAC;YAC3C;UAEF,KAAK,QAAQ;YACX,MAAM,IAAI,CAACH,WAAW,CAAC0B,gBAAgB,CAACvB,GAAG,CAAC;YAC5C;UAEF,KAAK,OAAO;YACV,IAAI,CAACH,WAAW,CAAC2B,eAAe,CAACxB,GAAG,CAAC;YACrC;UAEF;YACEN,MAAM,CAACS,MAAM,CAAC,0CAA0C,EAAEH,GAAG,CAAC;QAClE;MACF;;MAEA;AACF;AACA;AACA;AACA;MACEiB,oBAAoBA,CAACjB,GAAG,EAAE;QACxB,IAAI,IAAI,CAACH,WAAW,CAAC4B,qBAAqB,CAACC,OAAO,CAAC1B,GAAG,CAAC2B,OAAO,CAAC,IAAI,CAAC,EAAE;UACpE,IAAI,CAAC9B,WAAW,CAACe,kBAAkB,GAAGZ,GAAG,CAAC2B,OAAO;UACjD,IAAI,CAAC9B,WAAW,CAAC+B,OAAO,CAACC,SAAS,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC,CAAC;QACtD,CAAC,MAAM;UACL,MAAMC,WAAW,GACf,2DAA2D,GAC3D/B,GAAG,CAAC2B,OAAO;UACb,IAAI,CAAC9B,WAAW,CAAC+B,OAAO,CAACI,UAAU,CAAC;YAAEC,UAAU,EAAE,IAAI;YAAEC,MAAM,EAAEH;UAAY,CAAC,CAAC;UAC9E,IAAI,CAAClC,WAAW,CAACkB,OAAO,CAACoB,8BAA8B,CAACJ,WAAW,CAAC;QACtE;MACF;;MAEA;AACF;AACA;MACEK,OAAOA,CAAA,EAAG;QACR;QACA;QACA,MAAMpC,GAAG,GAAG,IAAI,CAACqC,oBAAoB,CAAC,CAAC;QACvC,IAAI,CAACxC,WAAW,CAACsB,KAAK,CAACnB,GAAG,CAAC;;QAE3B;QACA,IAAI,CAACsC,gCAAgC,CAAC,CAAC;;QAEvC;QACA;QACA;QACA,IAAI,CAACzC,WAAW,CAAC0C,oDAAoD,CAAC,CAAC;QACvE,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC7B;;MAEA;AACF;AACA;AACA;AACA;MACEH,oBAAoBA,CAAA,EAAG;QACrB,MAAMrC,GAAG,GAAG;UAAEA,GAAG,EAAE;QAAU,CAAC;QAC9B,IAAI,IAAI,CAACH,WAAW,CAAC4C,cAAc,EAAE;UACnCzC,GAAG,CAAC0C,OAAO,GAAG,IAAI,CAAC7C,WAAW,CAAC4C,cAAc;QAC/C;QACAzC,GAAG,CAAC2B,OAAO,GAAG,IAAI,CAAC9B,WAAW,CAACe,kBAAkB,IAAI,IAAI,CAACf,WAAW,CAAC4B,qBAAqB,CAAC,CAAC,CAAC;QAC9F,IAAI,CAAC5B,WAAW,CAACe,kBAAkB,GAAGZ,GAAG,CAAC2B,OAAO;QACjD3B,GAAG,CAAC2C,OAAO,GAAG,IAAI,CAAC9C,WAAW,CAAC4B,qBAAqB;QACpD,OAAOzB,GAAG;MACZ;;MAEA;AACF;AACA;AACA;MACEsC,gCAAgCA,CAAA,EAAG;QACjC,MAAMM,MAAM,GAAG,IAAI,CAAC/C,WAAW,CAACgD,wBAAwB;QACxD,IAAID,MAAM,CAACnC,MAAM,KAAK,CAAC,EAAE;QAEzB,MAAMqC,kBAAkB,GAAGF,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO;QAC5CH,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,GAAGD,kBAAkB,CAACE,MAAM,CAC3CC,aAAa,IAAI;UACf;UACA;UACA,IAAIA,aAAa,CAACC,WAAW,IAAID,aAAa,CAACE,OAAO,EAAE;YACtDF,aAAa,CAACG,aAAa,CACzB,IAAI1D,MAAM,CAAC2D,KAAK,CACd,mBAAmB,EACnB,iEAAiE,GACjE,8DACF,CACF,CAAC;UACH;;UAEA;UACA,OAAO,EAAEJ,aAAa,CAACC,WAAW,IAAID,aAAa,CAACE,OAAO,CAAC;QAC9D,CACF,CAAC;;QAED;QACA,IAAIP,MAAM,CAACnC,MAAM,GAAG,CAAC,IAAImC,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,CAACtC,MAAM,KAAK,CAAC,EAAE;UACvDmC,MAAM,CAACU,KAAK,CAAC,CAAC;QAChB;;QAEA;QACA/C,MAAM,CAACgD,MAAM,CAAC,IAAI,CAAC1D,WAAW,CAAC2D,eAAe,CAAC,CAACC,OAAO,CAACC,OAAO,IAAI;UACjEA,OAAO,CAACR,WAAW,GAAG,KAAK;QAC7B,CAAC,CAAC;MACJ;;MAEA;AACF;AACA;AACA;MACEV,oBAAoBA,CAAA,EAAG;QACrBjC,MAAM,CAACoD,OAAO,CAAC,IAAI,CAAC9D,WAAW,CAAC+D,cAAc,CAAC,CAACH,OAAO,CAACI,IAAA,IAAe;UAAA,IAAd,CAACzC,EAAE,EAAE0C,GAAG,CAAC,GAAAD,IAAA;UAChE,IAAI,CAAChE,WAAW,CAACkE,WAAW,CAAC;YAC3B/D,GAAG,EAAE,KAAK;YACVoB,EAAE,EAAEA,EAAE;YACN4C,IAAI,EAAEF,GAAG,CAACE,IAAI;YACdC,MAAM,EAAEH,GAAG,CAACG;UACd,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAAC/E,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICzMDP,MAAM,CAACQ,MAAM,CAAC;MAAC4E,kBAAkB,EAACA,CAAA,KAAIA;IAAkB,CAAC,CAAC;IAAC,IAAIC,OAAO;IAACrF,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;MAACoF,OAAOA,CAAC1E,CAAC,EAAC;QAAC0E,OAAO,GAAC1E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI2E,YAAY;IAACtF,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACqF,YAAYA,CAAC3E,CAAC,EAAC;QAAC2E,YAAY,GAAC3E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4E,MAAM;IAACvF,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;MAACsF,MAAMA,CAAC5E,CAAC,EAAC;QAAC4E,MAAM,GAAC5E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI6E,OAAO;IAACxF,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;MAACuF,OAAOA,CAAC7E,CAAC,EAAC;QAAC6E,OAAO,GAAC7E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIR,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAKra,MAAMiF,kBAAkB,CAAC;MAC9BvE,WAAWA,CAACC,UAAU,EAAE;QACtB,IAAI,CAACC,WAAW,GAAGD,UAAU;MAC/B;;MAEA;AACF;AACA;AACA;AACA;MACE,MAAM2E,cAAcA,CAACvE,GAAG,EAAEwE,OAAO,EAAE;QACjC,MAAMpF,IAAI,GAAG,IAAI,CAACS,WAAW;QAC7B,MAAMuB,EAAE,GAAG+C,OAAO,CAACM,OAAO,CAACzE,GAAG,CAACoB,EAAE,CAAC;QAClC,MAAMsD,SAAS,GAAGtF,IAAI,CAACuF,aAAa,CAAC3E,GAAG,CAAC4E,UAAU,EAAExD,EAAE,CAAC;QAExD,IAAIsD,SAAS,EAAE;UACb;UACA,MAAMG,UAAU,GAAGH,SAAS,CAACI,QAAQ,KAAKC,SAAS;UAEnDL,SAAS,CAACI,QAAQ,GAAG9E,GAAG,CAACgF,MAAM,IAAIzE,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;UACtDP,SAAS,CAACI,QAAQ,CAACI,GAAG,GAAG9D,EAAE;UAE3B,IAAIhC,IAAI,CAAC+F,YAAY,EAAE;YACrB;YACA;YACA;YACA;YACA,MAAMC,UAAU,GAAG,MAAMhG,IAAI,CAACiG,OAAO,CAACrF,GAAG,CAAC4E,UAAU,CAAC,CAACU,MAAM,CAACtF,GAAG,CAACoB,EAAE,CAAC;YACpE,IAAIgE,UAAU,KAAKL,SAAS,EAAE/E,GAAG,CAACgF,MAAM,GAAGI,UAAU;YAErDhG,IAAI,CAACmG,WAAW,CAACf,OAAO,EAAExE,GAAG,CAAC4E,UAAU,EAAE5E,GAAG,CAAC;UAChD,CAAC,MAAM,IAAI6E,UAAU,EAAE;YACrB,MAAM,IAAIxB,KAAK,CAAC,mCAAmC,GAAGrD,GAAG,CAACoB,EAAE,CAAC;UAC/D;QACF,CAAC,MAAM;UACLhC,IAAI,CAACmG,WAAW,CAACf,OAAO,EAAExE,GAAG,CAAC4E,UAAU,EAAE5E,GAAG,CAAC;QAChD;MACF;;MAEA;AACF;AACA;AACA;AACA;MACEwF,gBAAgBA,CAACxF,GAAG,EAAEwE,OAAO,EAAE;QAC7B,MAAMpF,IAAI,GAAG,IAAI,CAACS,WAAW;QAC7B,MAAM6E,SAAS,GAAGtF,IAAI,CAACuF,aAAa,CAAC3E,GAAG,CAAC4E,UAAU,EAAET,OAAO,CAACM,OAAO,CAACzE,GAAG,CAACoB,EAAE,CAAC,CAAC;QAE7E,IAAIsD,SAAS,EAAE;UACb,IAAIA,SAAS,CAACI,QAAQ,KAAKC,SAAS,EAAE;YACpC,MAAM,IAAI1B,KAAK,CAAC,0CAA0C,GAAGrD,GAAG,CAACoB,EAAE,CAAC;UACtE;UACAgD,YAAY,CAACqB,YAAY,CAACf,SAAS,CAACI,QAAQ,EAAE9E,GAAG,CAACgF,MAAM,CAAC;QAC3D,CAAC,MAAM;UACL5F,IAAI,CAACmG,WAAW,CAACf,OAAO,EAAExE,GAAG,CAAC4E,UAAU,EAAE5E,GAAG,CAAC;QAChD;MACF;;MAEA;AACF;AACA;AACA;AACA;MACE0F,gBAAgBA,CAAC1F,GAAG,EAAEwE,OAAO,EAAE;QAC7B,MAAMpF,IAAI,GAAG,IAAI,CAACS,WAAW;QAC7B,MAAM6E,SAAS,GAAGtF,IAAI,CAACuF,aAAa,CAAC3E,GAAG,CAAC4E,UAAU,EAAET,OAAO,CAACM,OAAO,CAACzE,GAAG,CAACoB,EAAE,CAAC,CAAC;QAE7E,IAAIsD,SAAS,EAAE;UACb;UACA,IAAIA,SAAS,CAACI,QAAQ,KAAKC,SAAS,EAAE;YACpC,MAAM,IAAI1B,KAAK,CAAC,yCAAyC,GAAGrD,GAAG,CAACoB,EAAE,CAAC;UACrE;UACAsD,SAAS,CAACI,QAAQ,GAAGC,SAAS;QAChC,CAAC,MAAM;UACL3F,IAAI,CAACmG,WAAW,CAACf,OAAO,EAAExE,GAAG,CAAC4E,UAAU,EAAE;YACxC5E,GAAG,EAAE,SAAS;YACd4E,UAAU,EAAE5E,GAAG,CAAC4E,UAAU;YAC1BxD,EAAE,EAAEpB,GAAG,CAACoB;UACV,CAAC,CAAC;QACJ;MACF;;MAEA;AACF;AACA;AACA;AACA;MACEuE,cAAcA,CAAC3F,GAAG,EAAEwE,OAAO,EAAE;QAC3B,MAAMpF,IAAI,GAAG,IAAI,CAACS,WAAW;;QAE7B;QACA;QACA;QACAG,GAAG,CAAC4F,IAAI,CAACnC,OAAO,CAAEoC,KAAK,IAAK;UAC1BzG,IAAI,CAAC0G,+BAA+B,CAAC,MAAM;YACzC,MAAMC,SAAS,GAAG3G,IAAI,CAACwE,cAAc,CAACiC,KAAK,CAAC;YAC5C;YACA,IAAI,CAACE,SAAS,EAAE;YAChB;YACA,IAAIA,SAAS,CAACC,KAAK,EAAE;YACrBD,SAAS,CAACC,KAAK,GAAG,IAAI;YACtBD,SAAS,CAACE,aAAa,IAAIF,SAAS,CAACE,aAAa,CAAC,CAAC;YACpDF,SAAS,CAACG,SAAS,CAACC,OAAO,CAAC,CAAC;UAC/B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;;MAEA;AACF;AACA;AACA;AACA;MACEC,gBAAgBA,CAACpG,GAAG,EAAEwE,OAAO,EAAE;QAC7B,MAAMpF,IAAI,GAAG,IAAI,CAACS,WAAW;QAC7B;QACAG,GAAG,CAAC+C,OAAO,CAACU,OAAO,CAAE4C,QAAQ,IAAK;UAChC,MAAMC,IAAI,GAAGlH,IAAI,CAACmH,uBAAuB,CAACF,QAAQ,CAAC,IAAI,CAAC,CAAC;UACzD9F,MAAM,CAACgD,MAAM,CAAC+C,IAAI,CAAC,CAAC7C,OAAO,CAAE+C,OAAO,IAAK;YACvC,MAAM9B,SAAS,GAAGtF,IAAI,CAACuF,aAAa,CAAC6B,OAAO,CAAC5B,UAAU,EAAE4B,OAAO,CAACpF,EAAE,CAAC;YACpE,IAAI,CAACsD,SAAS,EAAE;cACd,MAAM,IAAIrB,KAAK,CAAC,qBAAqB,GAAGoD,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,CAAC;YAClE;YACA,IAAI,CAAC9B,SAAS,CAACiC,cAAc,CAACN,QAAQ,CAAC,EAAE;cACvC,MAAM,IAAIhD,KAAK,CACb,MAAM,GACNoD,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,GACvB,yBAAyB,GACzBH,QACF,CAAC;YACH;YACA,OAAO3B,SAAS,CAACiC,cAAc,CAACN,QAAQ,CAAC;YACzC,IAAI/B,OAAO,CAACI,SAAS,CAACiC,cAAc,CAAC,EAAE;cACrC;cACA;cACA;cACA;;cAEA;cACA;cACA;cACAvH,IAAI,CAACmG,WAAW,CAACf,OAAO,EAAEgC,OAAO,CAAC5B,UAAU,EAAE;gBAC5C5E,GAAG,EAAE,SAAS;gBACdoB,EAAE,EAAE+C,OAAO,CAACyC,WAAW,CAACJ,OAAO,CAACpF,EAAE,CAAC;gBACnCyF,OAAO,EAAEnC,SAAS,CAACI;cACrB,CAAC,CAAC;cACF;cACAJ,SAAS,CAACoC,cAAc,CAACrD,OAAO,CAAEsD,CAAC,IAAK;gBACtCA,CAAC,CAAC,CAAC;cACL,CAAC,CAAC;;cAEF;cACA;cACA;cACA3H,IAAI,CAAC4H,gBAAgB,CAACR,OAAO,CAAC5B,UAAU,CAAC,CAACqC,MAAM,CAACT,OAAO,CAACpF,EAAE,CAAC;YAC9D;UACF,CAAC,CAAC;UACF,OAAOhC,IAAI,CAACmH,uBAAuB,CAACF,QAAQ,CAAC;;UAE7C;UACA;UACA,MAAMa,eAAe,GAAG9H,IAAI,CAACoE,eAAe,CAAC6C,QAAQ,CAAC;UACtD,IAAI,CAACa,eAAe,EAAE;YACpB,MAAM,IAAI7D,KAAK,CAAC,iCAAiC,GAAGgD,QAAQ,CAAC;UAC/D;UAEAjH,IAAI,CAAC0G,+BAA+B,CAClC;YAAA,OAAaoB,eAAe,CAACC,WAAW,CAAC,GAAAC,SAAO,CAAC;UAAA,CACnD,CAAC;QACH,CAAC,CAAC;MACJ;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;MACE7B,WAAWA,CAACf,OAAO,EAAEI,UAAU,EAAE5E,GAAG,EAAE;QACpC,IAAI,CAACqE,MAAM,CAACgD,IAAI,CAAC7C,OAAO,EAAEI,UAAU,CAAC,EAAE;UACrCJ,OAAO,CAACI,UAAU,CAAC,GAAG,EAAE;QAC1B;QACAJ,OAAO,CAACI,UAAU,CAAC,CAAC0C,IAAI,CAACtH,GAAG,CAAC;MAC/B;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;MACE2E,aAAaA,CAACC,UAAU,EAAExD,EAAE,EAAE;QAC5B,MAAMhC,IAAI,GAAG,IAAI,CAACS,WAAW;QAC7B,IAAI,CAACwE,MAAM,CAACgD,IAAI,CAACjI,IAAI,CAAC4H,gBAAgB,EAAEpC,UAAU,CAAC,EAAE;UACnD,OAAO,IAAI;QACb;QACA,MAAM2C,uBAAuB,GAAGnI,IAAI,CAAC4H,gBAAgB,CAACpC,UAAU,CAAC;QACjE,OAAO2C,uBAAuB,CAACC,GAAG,CAACpG,EAAE,CAAC,IAAI,IAAI;MAChD;IACF;IAAClC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC7MD,IAAIoI,wBAAwB;IAAC3I,MAAM,CAACC,IAAI,CAAC,gDAAgD,EAAC;MAAC2I,OAAOA,CAACjI,CAAC,EAAC;QAACgI,wBAAwB,GAAChI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIkI,aAAa;IAAC7I,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAAC2I,OAAOA,CAACjI,CAAC,EAAC;QAACkI,aAAa,GAAClI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,MAAAmI,SAAA;MAAAC,UAAA;IAA5O/I,MAAM,CAACQ,MAAM,CAAC;MAACwI,UAAU,EAACA,CAAA,KAAIA;IAAU,CAAC,CAAC;IAAC,IAAIpI,MAAM;IAACZ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACW,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAID,SAAS;IAACV,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;MAACS,SAASA,CAACC,CAAC,EAAC;QAACD,SAAS,GAACC,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIsI,OAAO;IAACjJ,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;MAACgJ,OAAOA,CAACtI,CAAC,EAAC;QAACsI,OAAO,GAACtI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIuI,KAAK;IAAClJ,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACiJ,KAAKA,CAACvI,CAAC,EAAC;QAACuI,KAAK,GAACvI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIwI,MAAM;IAACnJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACkJ,MAAMA,CAACxI,CAAC,EAAC;QAACwI,MAAM,GAACxI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI0E,OAAO;IAACrF,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;MAACoF,OAAOA,CAAC1E,CAAC,EAAC;QAAC0E,OAAO,GAAC1E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIT,GAAG;IAACF,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;MAACC,GAAGA,CAACS,CAAC,EAAC;QAACT,GAAG,GAACS,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIyI,aAAa;IAACpJ,MAAM,CAACC,IAAI,CAAC,kBAAkB,EAAC;MAACmJ,aAAaA,CAACzI,CAAC,EAAC;QAACyI,aAAa,GAACzI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4E,MAAM,EAAC8D,KAAK,EAAC3H,IAAI,EAAC8D,OAAO,EAAC8D,IAAI;IAACtJ,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;MAACsF,MAAMA,CAAC5E,CAAC,EAAC;QAAC4E,MAAM,GAAC5E,CAAC;MAAA,CAAC;MAAC0I,KAAKA,CAAC1I,CAAC,EAAC;QAAC0I,KAAK,GAAC1I,CAAC;MAAA,CAAC;MAACe,IAAIA,CAACf,CAAC,EAAC;QAACe,IAAI,GAACf,CAAC;MAAA,CAAC;MAAC6E,OAAOA,CAAC7E,CAAC,EAAC;QAAC6E,OAAO,GAAC7E,CAAC;MAAA,CAAC;MAAC2I,IAAIA,CAAC3I,CAAC,EAAC;QAAC2I,IAAI,GAAC3I,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIF,wBAAwB;IAACT,MAAM,CAACC,IAAI,CAAC,8BAA8B,EAAC;MAACQ,wBAAwBA,CAACE,CAAC,EAAC;QAACF,wBAAwB,GAACE,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI4I,UAAU;IAACvJ,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;MAACsJ,UAAUA,CAAC5I,CAAC,EAAC;QAAC4I,UAAU,GAAC5I,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAI6I,iBAAiB;IAACxJ,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACuJ,iBAAiBA,CAAC7I,CAAC,EAAC;QAAC6I,iBAAiB,GAAC7I,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIyE,kBAAkB;IAACpF,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACmF,kBAAkBA,CAACzE,CAAC,EAAC;QAACyE,kBAAkB,GAACzE,CAAC;MAAA;IAAC,CAAC,EAAC,EAAE,CAAC;IAAC,IAAIR,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAwCrtC,MAAM6I,UAAU,CAAC;MACtBnI,WAAWA,CAAC4I,GAAG,EAAExH,OAAO,EAAE;QACxB,MAAM3B,IAAI,GAAG,IAAI;QAEjB,IAAI,CAAC2B,OAAO,GAAGA,OAAO,GAAA4G,aAAA;UACpB3G,WAAWA,CAAA,EAAG,CAAC,CAAC;UAChBmB,8BAA8BA,CAACJ,WAAW,EAAE;YAC1CrC,MAAM,CAACS,MAAM,CAAC4B,WAAW,CAAC;UAC5B,CAAC;UACDyG,iBAAiB,EAAE,KAAK;UACxBC,gBAAgB,EAAE,KAAK;UACvBC,cAAc,EAAEnI,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;UACnC;UACA0D,qBAAqB,EAAE,KAAK;UAC5BC,oBAAoB,EAAEpJ,SAAS,CAACqJ,sBAAsB;UACtDC,KAAK,EAAE,IAAI;UACX5H,cAAc,EAAE,IAAI;UACpB;UACA6H,sBAAsB,EAAE,CAAC;UACzB;UACAC,oBAAoB,EAAE;QAAG,GAEtBjI,OAAO,CACX;;QAED;QACA;QACA;QACA;QACA;QACA3B,IAAI,CAAC6J,WAAW,GAAG,IAAI;;QAEvB;QACA,IAAI,OAAOV,GAAG,KAAK,QAAQ,EAAE;UAC3BnJ,IAAI,CAACwC,OAAO,GAAG2G,GAAG;QACpB,CAAC,MAAM;UA3EX,IAAIW,YAAY;UAACpK,MAAM,CAACC,IAAI,CAAC,6BAA6B,EAAC;YAACmK,YAAYA,CAACzJ,CAAC,EAAC;cAACyJ,YAAY,GAACzJ,CAAC;YAAA;UAAC,CAAC,EAAC,EAAE,CAAC;UA8E1FL,IAAI,CAACwC,OAAO,GAAG,IAAIsH,YAAY,CAACX,GAAG,EAAE;YACnCO,KAAK,EAAE/H,OAAO,CAAC+H,KAAK;YACpBK,eAAe,EAAEnK,GAAG,CAACmK,eAAe;YACpCC,OAAO,EAAErI,OAAO,CAACqI,OAAO;YACxBC,cAAc,EAAEtI,OAAO,CAACsI,cAAc;YACtC;YACA;YACA;YACA;YACA;YACAC,gBAAgB,EAAEvI,OAAO,CAACuI,gBAAgB;YAC1CC,gBAAgB,EAAExI,OAAO,CAACwI,gBAAgB;YAC1Cb,cAAc,EAAE3H,OAAO,CAAC2H;UAC1B,CAAC,CAAC;QACJ;QAEAtJ,IAAI,CAACqD,cAAc,GAAG,IAAI;QAC1BrD,IAAI,CAACwB,kBAAkB,GAAG,IAAI,CAAC,CAAC;QAChCxB,IAAI,CAACuB,QAAQ,GAAG,IAAI,CAAC,CAAC;QACtBvB,IAAI,CAACiG,OAAO,GAAG9E,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACpC7F,IAAI,CAACoK,eAAe,GAAGjJ,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5C7F,IAAI,CAACqK,aAAa,GAAG,CAAC;QACtBrK,IAAI,CAACqC,qBAAqB,GAAGV,OAAO,CAAC6H,oBAAoB;QAEzDxJ,IAAI,CAACsK,kBAAkB,GAAG3I,OAAO,CAACyH,iBAAiB;QACnDpJ,IAAI,CAACuK,iBAAiB,GAAG5I,OAAO,CAAC0H,gBAAgB;;QAEjD;QACA;QACA;QACA;QACArJ,IAAI,CAACoE,eAAe,GAAGjD,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;;QAE1C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA7F,IAAI,CAACyD,wBAAwB,GAAG,EAAE;;QAElC;QACA;QACA;QACA;QACAzD,IAAI,CAACmH,uBAAuB,GAAG,CAAC,CAAC;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACAnH,IAAI,CAAC4H,gBAAgB,GAAG,CAAC,CAAC;;QAE1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA5H,IAAI,CAACwK,qBAAqB,GAAG,EAAE;;QAE/B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACAxK,IAAI,CAACyK,gCAAgC,GAAG,EAAE;QAC1C;QACA;QACA;QACAzK,IAAI,CAAC0K,0BAA0B,GAAG,CAAC,CAAC;QACpC;QACA;QACA1K,IAAI,CAAC2K,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7B;QACA;QACA3K,IAAI,CAAC+F,YAAY,GAAG,KAAK;;QAEzB;QACA/F,IAAI,CAAC4K,wBAAwB,GAAG,CAAC,CAAC;QAClC;QACA5K,IAAI,CAAC6K,aAAa,GAAG,IAAI;QACzB;QACA7K,IAAI,CAAC8K,eAAe,GAAG,CAAC,CAAC;QACzB;QACA9K,IAAI,CAAC+K,sBAAsB,GAAG,IAAI;QAClC;QACA/K,IAAI,CAACgL,0BAA0B,GAAG,IAAI;QAEtChL,IAAI,CAACiL,uBAAuB,GAAGtJ,OAAO,CAACgI,sBAAsB;QAC7D3J,IAAI,CAACkL,qBAAqB,GAAGvJ,OAAO,CAACiI,oBAAoB;;QAEzD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA5J,IAAI,CAACwE,cAAc,GAAG,CAAC,CAAC;;QAExB;QACAxE,IAAI,CAACmL,OAAO,GAAG,IAAI;QACnBnL,IAAI,CAACoL,WAAW,GAAG,IAAIzC,OAAO,CAAC0C,UAAU,CAAC,CAAC;;QAE3C;QACA,IAAI/K,MAAM,CAACgL,QAAQ,IACjBC,OAAO,CAACC,MAAM,IACd,CAAE7J,OAAO,CAAC4H,qBAAqB,EAAE;UACjCgC,OAAO,CAACC,MAAM,CAACC,MAAM,CAACC,UAAU,CAAChC,KAAK,IAAI;YACxC,IAAI,CAAE1J,IAAI,CAAC2L,eAAe,CAAC,CAAC,EAAE;cAC5B3L,IAAI,CAAC6K,aAAa,GAAGnB,KAAK;cAC1B,OAAO,CAAC,KAAK,CAAC;YAChB,CAAC,MAAM;cACL,OAAO,CAAC,IAAI,CAAC;YACf;UACF,CAAC,CAAC;QACJ;QAEA,IAAI,CAACkC,eAAe,GAAG,IAAIzL,wBAAwB,CAAC,IAAI,CAAC;QAEzD,MAAM0L,YAAY,GAAGA,CAAA,KAAM;UACzB,IAAI,IAAI,CAAC7K,UAAU,EAAE;YACnB,IAAI,CAACA,UAAU,CAAC8K,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC9K,UAAU,GAAG,IAAI;UACxB;QACF,CAAC;QAED,IAAIV,MAAM,CAACyL,QAAQ,EAAE;UACnB,IAAI,CAACvJ,OAAO,CAACwJ,EAAE,CACb,SAAS,EACT1L,MAAM,CAAC2L,eAAe,CACpBrL,GAAG,IAAI,IAAI,CAACgL,eAAe,CAAClL,SAAS,CAACE,GAAG,CAAC,EAC1C,sBACF,CACF,CAAC;UACD,IAAI,CAAC4B,OAAO,CAACwJ,EAAE,CACb,OAAO,EACP1L,MAAM,CAAC2L,eAAe,CACpB,MAAM,IAAI,CAACL,eAAe,CAAC5I,OAAO,CAAC,CAAC,EACpC,oBACF,CACF,CAAC;UACD,IAAI,CAACR,OAAO,CAACwJ,EAAE,CACb,YAAY,EACZ1L,MAAM,CAAC2L,eAAe,CAACJ,YAAY,EAAE,yBAAyB,CAChE,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACrJ,OAAO,CAACwJ,EAAE,CAAC,SAAS,EAAEpL,GAAG,IAAI,IAAI,CAACgL,eAAe,CAAClL,SAAS,CAACE,GAAG,CAAC,CAAC;UACtE,IAAI,CAAC4B,OAAO,CAACwJ,EAAE,CAAC,OAAO,EAAE,MAAM,IAAI,CAACJ,eAAe,CAAC5I,OAAO,CAAC,CAAC,CAAC;UAC9D,IAAI,CAACR,OAAO,CAACwJ,EAAE,CAAC,YAAY,EAAEH,YAAY,CAAC;QAC7C;QAEA,IAAI,CAACK,kBAAkB,GAAG,IAAIhD,iBAAiB,CAAC,IAAI,CAAC;;QAErD;QACA,IAAI,CAACxH,mBAAmB,GAAId,GAAG,IAAK,IAAI,CAACsL,kBAAkB,CAACxK,mBAAmB,CAACd,GAAG,CAAC;QACpF,IAAI,CAACqB,cAAc,GAAIrB,GAAG,IAAK,IAAI,CAACsL,kBAAkB,CAACjK,cAAc,CAACrB,GAAG,CAAC;QAC1E,IAAI,CAACsB,eAAe,GAAItB,GAAG,IAAK,IAAI,CAACsL,kBAAkB,CAAChK,eAAe,CAACtB,GAAG,CAAC;QAC5E,IAAI,CAACuB,gBAAgB,GAAIvB,GAAG,IAAK,IAAI,CAACsL,kBAAkB,CAAC/J,gBAAgB,CAACvB,GAAG,CAAC;QAC9E,IAAI,CAACwB,eAAe,GAAIxB,GAAG,IAAK,IAAI,CAACsL,kBAAkB,CAAC9J,eAAe,CAACxB,GAAG,CAAC;QAE5E,IAAI,CAACuL,mBAAmB,GAAG,IAAIrH,kBAAkB,CAAC,IAAI,CAAC;;QAEvD;QACA,IAAI,CAACK,cAAc,GAAG,CAACvE,GAAG,EAAEwE,OAAO,KAAK,IAAI,CAAC+G,mBAAmB,CAAChH,cAAc,CAACvE,GAAG,EAAEwE,OAAO,CAAC;QAC7F,IAAI,CAACgB,gBAAgB,GAAG,CAACxF,GAAG,EAAEwE,OAAO,KAAK,IAAI,CAAC+G,mBAAmB,CAAC/F,gBAAgB,CAACxF,GAAG,EAAEwE,OAAO,CAAC;QACjG,IAAI,CAACkB,gBAAgB,GAAG,CAAC1F,GAAG,EAAEwE,OAAO,KAAK,IAAI,CAAC+G,mBAAmB,CAAC7F,gBAAgB,CAAC1F,GAAG,EAAEwE,OAAO,CAAC;QACjG,IAAI,CAACmB,cAAc,GAAG,CAAC3F,GAAG,EAAEwE,OAAO,KAAK,IAAI,CAAC+G,mBAAmB,CAAC5F,cAAc,CAAC3F,GAAG,EAAEwE,OAAO,CAAC;QAC7F,IAAI,CAAC4B,gBAAgB,GAAG,CAACpG,GAAG,EAAEwE,OAAO,KAAK,IAAI,CAAC+G,mBAAmB,CAACnF,gBAAgB,CAACpG,GAAG,EAAEwE,OAAO,CAAC;;QAEjG;QACA,IAAI,CAACe,WAAW,GAAG,CAACf,OAAO,EAAEI,UAAU,EAAE5E,GAAG,KAC1C,IAAI,CAACuL,mBAAmB,CAAChG,WAAW,CAACf,OAAO,EAAEI,UAAU,EAAE5E,GAAG,CAAC;QAChE,IAAI,CAAC2E,aAAa,GAAG,CAACC,UAAU,EAAExD,EAAE,KAClC,IAAI,CAACmK,mBAAmB,CAAC5G,aAAa,CAACC,UAAU,EAAExD,EAAE,CAAC;MAC1D;;MAEA;MACA;MACA;MACAoK,kBAAkBA,CAACxH,IAAI,EAAEyH,YAAY,EAAE;QACrC,MAAMrM,IAAI,GAAG,IAAI;QAEjB,IAAI4E,IAAI,IAAI5E,IAAI,CAACiG,OAAO,EAAE,OAAO,KAAK;;QAEtC;QACA;QACA,MAAMqG,KAAK,GAAGnL,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QACjC,MAAM0G,WAAW,GAAG,CAClB,QAAQ,EACR,aAAa,EACb,WAAW,EACX,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,gBAAgB,CACjB;QACDA,WAAW,CAAClI,OAAO,CAAEmI,MAAM,IAAK;UAC9BF,KAAK,CAACE,MAAM,CAAC,GAAG,YAAa;YAC3B,IAAIH,YAAY,CAACG,MAAM,CAAC,EAAE;cACxB,OAAOH,YAAY,CAACG,MAAM,CAAC,CAAC,GAAAxE,SAAO,CAAC;YACtC;UACF,CAAC;QACH,CAAC,CAAC;QACFhI,IAAI,CAACiG,OAAO,CAACrB,IAAI,CAAC,GAAG0H,KAAK;QAC1B,OAAOA,KAAK;MACd;MAEAG,mBAAmBA,CAAC7H,IAAI,EAAEyH,YAAY,EAAE;QACtC,MAAMrM,IAAI,GAAG,IAAI;QAEjB,MAAMsM,KAAK,GAAGtM,IAAI,CAACoM,kBAAkB,CAACxH,IAAI,EAAEyH,YAAY,CAAC;QAEzD,MAAMK,MAAM,GAAG1M,IAAI,CAAC4K,wBAAwB,CAAChG,IAAI,CAAC;QAClD,IAAI+H,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;UACzBJ,KAAK,CAACO,WAAW,CAACH,MAAM,CAACrL,MAAM,EAAE,KAAK,CAAC;UACvCqL,MAAM,CAACrI,OAAO,CAACzD,GAAG,IAAI;YACpB0L,KAAK,CAACQ,MAAM,CAAClM,GAAG,CAAC;UACnB,CAAC,CAAC;UACF0L,KAAK,CAACS,SAAS,CAAC,CAAC;UACjB,OAAO/M,IAAI,CAAC4K,wBAAwB,CAAChG,IAAI,CAAC;QAC5C;QAEA,OAAO,IAAI;MACb;MACA,MAAMoI,mBAAmBA,CAACpI,IAAI,EAAEyH,YAAY,EAAE;QAC5C,MAAMrM,IAAI,GAAG,IAAI;QAEjB,MAAMsM,KAAK,GAAGtM,IAAI,CAACoM,kBAAkB,CAACxH,IAAI,EAAEyH,YAAY,CAAC;QAEzD,MAAMK,MAAM,GAAG1M,IAAI,CAAC4K,wBAAwB,CAAChG,IAAI,CAAC;QAClD,IAAI+H,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;UACzB,MAAMJ,KAAK,CAACO,WAAW,CAACH,MAAM,CAACrL,MAAM,EAAE,KAAK,CAAC;UAC7C,KAAK,MAAMT,GAAG,IAAI8L,MAAM,EAAE;YACxB,MAAMJ,KAAK,CAACQ,MAAM,CAAClM,GAAG,CAAC;UACzB;UACA,MAAM0L,KAAK,CAACS,SAAS,CAAC,CAAC;UACvB,OAAO/M,IAAI,CAAC4K,wBAAwB,CAAChG,IAAI,CAAC;QAC5C;QAEA,OAAO,IAAI;MACb;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEqI,SAASA,CAACrI,IAAI,CAAC,8CAA8C;QAC3D,MAAM5E,IAAI,GAAG,IAAI;QAEjB,MAAM6E,MAAM,GAAGkE,KAAK,CAACd,IAAI,CAACD,SAAS,EAAE,CAAC,CAAC;QACvC,IAAIkF,SAAS,GAAG/L,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QACnC,IAAIhB,MAAM,CAACxD,MAAM,EAAE;UACjB,MAAM8L,SAAS,GAAGtI,MAAM,CAACA,MAAM,CAACxD,MAAM,GAAG,CAAC,CAAC;UAC3C,IAAI,OAAO8L,SAAS,KAAK,UAAU,EAAE;YACnCD,SAAS,CAACE,OAAO,GAAGvI,MAAM,CAACwI,GAAG,CAAC,CAAC;UAClC,CAAC,MAAM,IAAIF,SAAS,IAAI,CACtBA,SAAS,CAACC,OAAO;UACjB;UACA;UACAD,SAAS,CAACG,OAAO,EACjBH,SAAS,CAACI,MAAM,CACjB,CAACC,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,CAAC,EAAE;YACpCP,SAAS,GAAGrI,MAAM,CAACwI,GAAG,CAAC,CAAC;UAC1B;QACF;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMK,QAAQ,GAAGvM,MAAM,CAACgD,MAAM,CAACnE,IAAI,CAACwE,cAAc,CAAC,CAACmJ,IAAI,CACtDjJ,GAAG,IAAKA,GAAG,CAACkJ,QAAQ,IAAIlJ,GAAG,CAACE,IAAI,KAAKA,IAAI,IAAIgE,KAAK,CAACiF,MAAM,CAACnJ,GAAG,CAACG,MAAM,EAAEA,MAAM,CAC9E,CAAC;QAED,IAAI7C,EAAE;QACN,IAAI0L,QAAQ,EAAE;UACZ1L,EAAE,GAAG0L,QAAQ,CAAC1L,EAAE;UAChB0L,QAAQ,CAACE,QAAQ,GAAG,KAAK,CAAC,CAAC;;UAE3B,IAAIV,SAAS,CAACE,OAAO,EAAE;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAIM,QAAQ,CAAC9G,KAAK,EAAE;cAClBsG,SAAS,CAACE,OAAO,CAAC,CAAC;YACrB,CAAC,MAAM;cACLM,QAAQ,CAAC7G,aAAa,GAAGqG,SAAS,CAACE,OAAO;YAC5C;UACF;;UAEA;UACA;UACA,IAAIF,SAAS,CAACI,OAAO,EAAE;YACrB;YACA;YACAI,QAAQ,CAACI,aAAa,GAAGZ,SAAS,CAACI,OAAO;UAC5C;UAEA,IAAIJ,SAAS,CAACK,MAAM,EAAE;YACpBG,QAAQ,CAACK,YAAY,GAAGb,SAAS,CAACK,MAAM;UAC1C;QACF,CAAC,MAAM;UACL;UACAvL,EAAE,GAAG6G,MAAM,CAAC7G,EAAE,CAAC,CAAC;UAChBhC,IAAI,CAACwE,cAAc,CAACxC,EAAE,CAAC,GAAG;YACxBA,EAAE,EAAEA,EAAE;YACN4C,IAAI,EAAEA,IAAI;YACVC,MAAM,EAAE+D,KAAK,CAACoF,KAAK,CAACnJ,MAAM,CAAC;YAC3B+I,QAAQ,EAAE,KAAK;YACfhH,KAAK,EAAE,KAAK;YACZE,SAAS,EAAE,IAAI6B,OAAO,CAAC0C,UAAU,CAAC,CAAC;YACnCxE,aAAa,EAAEqG,SAAS,CAACE,OAAO;YAChC;YACAU,aAAa,EAAEZ,SAAS,CAACI,OAAO;YAChCS,YAAY,EAAEb,SAAS,CAACK,MAAM;YAC9B/M,UAAU,EAAER,IAAI;YAChB6H,MAAMA,CAAA,EAAG;cACP,OAAO,IAAI,CAACrH,UAAU,CAACgE,cAAc,CAAC,IAAI,CAACxC,EAAE,CAAC;cAC9C,IAAI,CAAC4E,KAAK,IAAI,IAAI,CAACE,SAAS,CAACC,OAAO,CAAC,CAAC;YACxC,CAAC;YACD+E,IAAIA,CAAA,EAAG;cACL,IAAI,CAACtL,UAAU,CAACmE,WAAW,CAAC;gBAAE/D,GAAG,EAAE,OAAO;gBAAEoB,EAAE,EAAEA;cAAG,CAAC,CAAC;cACrD,IAAI,CAAC6F,MAAM,CAAC,CAAC;cAEb,IAAIqF,SAAS,CAACK,MAAM,EAAE;gBACpBL,SAAS,CAACK,MAAM,CAAC,CAAC;cACpB;YACF;UACF,CAAC;UACDvN,IAAI,CAAC+B,KAAK,CAAC;YAAEnB,GAAG,EAAE,KAAK;YAAEoB,EAAE,EAAEA,EAAE;YAAE4C,IAAI,EAAEA,IAAI;YAAEC,MAAM,EAAEA;UAAO,CAAC,CAAC;QAChE;;QAEA;QACA,MAAMoJ,MAAM,GAAG;UACbnC,IAAIA,CAAA,EAAG;YACL,IAAI,CAAE7G,MAAM,CAACgD,IAAI,CAACjI,IAAI,CAACwE,cAAc,EAAExC,EAAE,CAAC,EAAE;cAC1C;YACF;YACAhC,IAAI,CAACwE,cAAc,CAACxC,EAAE,CAAC,CAAC8J,IAAI,CAAC,CAAC;UAChC,CAAC;UACDlF,KAAKA,CAAA,EAAG;YACN;YACA,IAAI,CAAC3B,MAAM,CAACgD,IAAI,CAACjI,IAAI,CAACwE,cAAc,EAAExC,EAAE,CAAC,EAAE;cACzC,OAAO,KAAK;YACd;YACA,MAAMkM,MAAM,GAAGlO,IAAI,CAACwE,cAAc,CAACxC,EAAE,CAAC;YACtCkM,MAAM,CAACpH,SAAS,CAACqH,MAAM,CAAC,CAAC;YACzB,OAAOD,MAAM,CAACtH,KAAK;UACrB,CAAC;UACDwH,cAAc,EAAEpM;QAClB,CAAC;QAED,IAAI2G,OAAO,CAAC0F,MAAM,EAAE;UAClB;UACA;UACA;UACA;UACA;UACA;UACA1F,OAAO,CAAC2F,YAAY,CAAE3G,CAAC,IAAK;YAC1B,IAAI1C,MAAM,CAACgD,IAAI,CAACjI,IAAI,CAACwE,cAAc,EAAExC,EAAE,CAAC,EAAE;cACxChC,IAAI,CAACwE,cAAc,CAACxC,EAAE,CAAC,CAAC4L,QAAQ,GAAG,IAAI;YACzC;YAEAjF,OAAO,CAAC4F,UAAU,CAAC,MAAM;cACvB,IAAItJ,MAAM,CAACgD,IAAI,CAACjI,IAAI,CAACwE,cAAc,EAAExC,EAAE,CAAC,IACpChC,IAAI,CAACwE,cAAc,CAACxC,EAAE,CAAC,CAAC4L,QAAQ,EAAE;gBACpCK,MAAM,CAACnC,IAAI,CAAC,CAAC;cACf;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;QAEA,OAAOmC,MAAM;MACf;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MACEO,WAAWA,CAAA,EAAE;QACX,OAAO5O,GAAG,CAAC6O,wBAAwB,CAACC,yBAAyB,CAAC,CAAC;MACjE;MACA/K,OAAOA,CAACA,OAAO,EAAE;QACfxC,MAAM,CAACoD,OAAO,CAACZ,OAAO,CAAC,CAACU,OAAO,CAACI,IAAA,IAAkB;UAAA,IAAjB,CAACG,IAAI,EAAE+J,IAAI,CAAC,GAAAlK,IAAA;UAC3C,IAAI,OAAOkK,IAAI,KAAK,UAAU,EAAE;YAC9B,MAAM,IAAI1K,KAAK,CAAC,UAAU,GAAGW,IAAI,GAAG,sBAAsB,CAAC;UAC7D;UACA,IAAI,IAAI,CAACwF,eAAe,CAACxF,IAAI,CAAC,EAAE;YAC9B,MAAM,IAAIX,KAAK,CAAC,kBAAkB,GAAGW,IAAI,GAAG,sBAAsB,CAAC;UACrE;UACA,IAAI,CAACwF,eAAe,CAACxF,IAAI,CAAC,GAAG+J,IAAI;QACnC,CAAC,CAAC;MACJ;MAEAC,gBAAgBA,CAAAC,KAAA,EAAyC;QAAA,IAAxC;UAACC,eAAe;UAAEC;QAAmB,CAAC,GAAAF,KAAA;QACrD,IAAI,CAACC,eAAe,EAAE;UACpB,OAAOC,mBAAmB;QAC5B;QACA,OAAOA,mBAAmB,IAAInP,GAAG,CAAC6O,wBAAwB,CAACC,yBAAyB,CAAC,CAAC;MACxF;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEzG,IAAIA,CAACrD,IAAI,CAAC,kCAAkC;QAC1C;QACA;QACA,MAAMoK,IAAI,GAAGjG,KAAK,CAACd,IAAI,CAACD,SAAS,EAAE,CAAC,CAAC;QACrC,IAAIiH,QAAQ;QACZ,IAAID,IAAI,CAAC3N,MAAM,IAAI,OAAO2N,IAAI,CAACA,IAAI,CAAC3N,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;UAC9D4N,QAAQ,GAAGD,IAAI,CAAC3B,GAAG,CAAC,CAAC;QACvB;QACA,OAAO,IAAI,CAAC6B,KAAK,CAACtK,IAAI,EAAEoK,IAAI,EAAEC,QAAQ,CAAC;MACzC;MACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEE,SAASA,CAACvK,IAAI,CAAC,yBAAyB;QACtC,MAAMoK,IAAI,GAAGjG,KAAK,CAACd,IAAI,CAACD,SAAS,EAAE,CAAC,CAAC;QACrC,IAAIgH,IAAI,CAAC3N,MAAM,IAAI,OAAO2N,IAAI,CAACA,IAAI,CAAC3N,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;UAC9D,MAAM,IAAI4C,KAAK,CACb,+FACF,CAAC;QACH;QAEA,OAAO,IAAI,CAACmL,UAAU,CAACxK,IAAI,EAAEoK,IAAI,EAAE;UAAEK,yBAAyB,EAAE;QAAK,CAAC,CAAC;MACzE;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEH,KAAKA,CAACtK,IAAI,EAAEoK,IAAI,EAAErN,OAAO,EAAEsN,QAAQ,EAAE;QACnC,MAAAK,eAAA,GAAuD,IAAI,CAACC,SAAS,CAAC3K,IAAI,EAAEgE,KAAK,CAACoF,KAAK,CAACgB,IAAI,CAAC,CAAC;UAAxF;YAAEQ,cAAc;YAAEC;UAA2B,CAAC,GAAAH,eAAA;UAAbI,WAAW,GAAArH,wBAAA,CAAAiH,eAAA,EAAA9G,SAAA;QAElD,IAAIkH,WAAW,CAACC,OAAO,EAAE;UACvB,IACE,CAAC,IAAI,CAACf,gBAAgB,CAAC;YACrBG,mBAAmB,EAAEW,WAAW,CAACX,mBAAmB;YACpDD,eAAe,EAAEY,WAAW,CAACZ;UAC/B,CAAC,CAAC,EACF;YACA,IAAI,CAACc,cAAc,CAAC,CAAC;UACvB;UACA,IAAI;YACFF,WAAW,CAACG,eAAe,GAAGjQ,GAAG,CAAC6O,wBAAwB,CACvDqB,SAAS,CAACL,UAAU,EAAED,cAAc,CAAC;YACxC,IAAIlP,MAAM,CAACyP,UAAU,CAACL,WAAW,CAACG,eAAe,CAAC,EAAE;cAClDvP,MAAM,CAACS,MAAM,WAAAiP,MAAA,CACDpL,IAAI,yIAChB,CAAC;YACH;UACF,CAAC,CAAC,OAAO9D,CAAC,EAAE;YACV4O,WAAW,CAACO,SAAS,GAAGnP,CAAC;UAC3B;QACF;QACA,OAAO,IAAI,CAACoP,MAAM,CAACtL,IAAI,EAAE8K,WAAW,EAAEV,IAAI,EAAErN,OAAO,EAAEsN,QAAQ,CAAC;MAChE;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEG,UAAUA,CAACxK,IAAI,EAAEoK,IAAI,EAAErN,OAAO,EAAmB;QAAA,IAAjBsN,QAAQ,GAAAjH,SAAA,CAAA3G,MAAA,QAAA2G,SAAA,QAAArC,SAAA,GAAAqC,SAAA,MAAG,IAAI;QAC7C,MAAMmI,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAACxL,IAAI,EAAEoK,IAAI,EAAErN,OAAO,CAAC;QAEvE,MAAM0O,OAAO,GAAG,IAAI,CAACC,WAAW,CAAC;UAC/B1L,IAAI;UACJoK,IAAI;UACJrN,OAAO;UACPsN,QAAQ;UACRkB;QACF,CAAC,CAAC;QACF,IAAI7P,MAAM,CAACgL,QAAQ,EAAE;UACnB;UACA+E,OAAO,CAACF,WAAW,GAAGA,WAAW,CAACI,IAAI,CAACC,CAAC,IAAI;YAC1C,IAAIA,CAAC,CAACP,SAAS,EAAE;cACf,MAAMO,CAAC,CAACP,SAAS;YACnB;YACA,OAAOO,CAAC,CAACX,eAAe;UAC1B,CAAC,CAAC;UACF;UACAQ,OAAO,CAACI,aAAa,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAClDP,OAAO,CAACE,IAAI,CAACI,OAAO,CAAC,CAACE,KAAK,CAACD,MAAM,CACpC,CAAC;QACH;QACA,OAAOP,OAAO;MAChB;MACA,MAAMD,yBAAyBA,CAACxL,IAAI,EAAEoK,IAAI,EAAErN,OAAO,EAAE;QACnD,MAAAmP,gBAAA,GAAuD,IAAI,CAACvB,SAAS,CAAC3K,IAAI,EAAEgE,KAAK,CAACoF,KAAK,CAACgB,IAAI,CAAC,EAAErN,OAAO,CAAC;UAAjG;YAAE6N,cAAc;YAAEC;UAA2B,CAAC,GAAAqB,gBAAA;UAAbpB,WAAW,GAAArH,wBAAA,CAAAyI,gBAAA,EAAArI,UAAA;QAClD,IAAIiH,WAAW,CAACC,OAAO,EAAE;UACvB,IACE,CAAC,IAAI,CAACf,gBAAgB,CAAC;YACrBG,mBAAmB,EAAEW,WAAW,CAACX,mBAAmB;YACpDD,eAAe,EAAEY,WAAW,CAACZ;UAC/B,CAAC,CAAC,EACF;YACA,IAAI,CAACc,cAAc,CAAC,CAAC;UACvB;UACA,IAAI;YACF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;YACQ,MAAMmB,cAAc,GAAGnR,GAAG,CAAC6O,wBAAwB,CAACuC,2BAA2B,CAC7EvB,UACF,CAAC;YACD,IAAI;cACFC,WAAW,CAACG,eAAe,GAAG,MAAML,cAAc,CAAC,CAAC;YACtD,CAAC,CAAC,OAAO1O,CAAC,EAAE;cACV4O,WAAW,CAACO,SAAS,GAAGnP,CAAC;YAC3B,CAAC,SAAS;cACRlB,GAAG,CAAC6O,wBAAwB,CAACwC,IAAI,CAACF,cAAc,CAAC;YACnD;UACF,CAAC,CAAC,OAAOjQ,CAAC,EAAE;YACV4O,WAAW,CAACO,SAAS,GAAGnP,CAAC;UAC3B;QACF;QACA,OAAO4O,WAAW;MACpB;MACA,MAAMY,WAAWA,CAAAY,KAAA,EAAiD;QAAA,IAAhD;UAAEtM,IAAI;UAAEoK,IAAI;UAAErN,OAAO;UAAEsN,QAAQ;UAAEkB;QAAY,CAAC,GAAAe,KAAA;QAC9D,MAAMxB,WAAW,GAAG,MAAMS,WAAW;QACrC,OAAO,IAAI,CAACD,MAAM,CAACtL,IAAI,EAAE8K,WAAW,EAAEV,IAAI,EAAErN,OAAO,EAAEsN,QAAQ,CAAC;MAChE;MAEAiB,MAAMA,CAACtL,IAAI,EAAEuM,aAAa,EAAEnC,IAAI,EAAErN,OAAO,EAAEsN,QAAQ,EAAE;QACnD,MAAMjP,IAAI,GAAG,IAAI;;QAEjB;QACA;QACA,IAAI,CAACiP,QAAQ,IAAI,OAAOtN,OAAO,KAAK,UAAU,EAAE;UAC9CsN,QAAQ,GAAGtN,OAAO;UAClBA,OAAO,GAAGR,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QAC/B;QACAlE,OAAO,GAAGA,OAAO,IAAIR,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QAExC,IAAIoJ,QAAQ,EAAE;UACZ;UACA;UACA;UACAA,QAAQ,GAAG3O,MAAM,CAAC2L,eAAe,CAC/BgD,QAAQ,EACR,iCAAiC,GAAGrK,IAAI,GAAG,GAC7C,CAAC;QACH;QACA,MAAM;UACJ+K,OAAO;UACPM,SAAS;UACTJ,eAAe;UACfd,mBAAmB;UACnBqC;QACF,CAAC,GAAGD,aAAa;;QAEjB;QACA;QACAnC,IAAI,GAAGpG,KAAK,CAACoF,KAAK,CAACgB,IAAI,CAAC;QACxB;QACA;QACA;QACA,IACE,IAAI,CAACJ,gBAAgB,CAAC;UACpBG,mBAAmB;UACnBD,eAAe,EAAEqC,aAAa,CAACrC;QACjC,CAAC,CAAC,EACF;UACA,IAAIuC,MAAM;UAEV,IAAIpC,QAAQ,EAAE;YACZA,QAAQ,CAACgB,SAAS,EAAEJ,eAAe,CAAC;UACtC,CAAC,MAAM;YACL,IAAII,SAAS,EAAE,MAAMA,SAAS;YAC9BoB,MAAM,GAAGxB,eAAe;UAC1B;UAEA,OAAOlO,OAAO,CAAC2P,oBAAoB,GAAG;YAAED;UAAO,CAAC,GAAGA,MAAM;QAC3D;;QAEA;QACA;QACA,MAAMpK,QAAQ,GAAG,EAAE,GAAGjH,IAAI,CAACqK,aAAa,EAAE;QAC1C,IAAIsF,OAAO,EAAE;UACX3P,IAAI,CAACuR,0BAA0B,CAACtK,QAAQ,CAAC;QAC3C;;QAEA;QACA;QACA;QACA;QACA,MAAMuK,OAAO,GAAG;UACd5Q,GAAG,EAAE,QAAQ;UACboB,EAAE,EAAEiF,QAAQ;UACZuF,MAAM,EAAE5H,IAAI;UACZC,MAAM,EAAEmK;QACV,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIiB,SAAS,EAAE;UACb,IAAItO,OAAO,CAAC8P,mBAAmB,EAAE;YAC/B,MAAMxB,SAAS;UACjB,CAAC,MAAM,IAAI,CAACA,SAAS,CAACyB,eAAe,EAAE;YACrCpR,MAAM,CAACS,MAAM,CACX,qDAAqD,GAAG6D,IAAI,GAAG,GAAG,EAClEqL,SACF,CAAC;UACH;QACF;;QAEA;QACA;;QAEA;QACA,IAAII,OAAO;QACX,IAAI,CAACpB,QAAQ,EAAE;UACb,IACE3O,MAAM,CAACgL,QAAQ,IACf,CAAC3J,OAAO,CAAC0N,yBAAyB,KACjC,CAAC1N,OAAO,CAACmN,eAAe,IAAInN,OAAO,CAACgQ,eAAe,CAAC,EACrD;YACA1C,QAAQ,GAAI2C,GAAG,IAAK;cAClBA,GAAG,IAAItR,MAAM,CAACS,MAAM,CAAC,yBAAyB,GAAG6D,IAAI,GAAG,GAAG,EAAEgN,GAAG,CAAC;YACnE,CAAC;UACH,CAAC,MAAM;YACLvB,OAAO,GAAG,IAAIK,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;cACzC3B,QAAQ,GAAG,SAAAA,CAAA,EAAgB;gBAAA,SAAA4C,IAAA,GAAA7J,SAAA,CAAA3G,MAAA,EAAZyQ,OAAO,OAAAnF,KAAA,CAAAkF,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;kBAAPD,OAAO,CAAAC,IAAA,IAAA/J,SAAA,CAAA+J,IAAA;gBAAA;gBACpB,IAAI/C,IAAI,GAAGrC,KAAK,CAACqF,IAAI,CAACF,OAAO,CAAC;gBAC9B,IAAIF,GAAG,GAAG5C,IAAI,CAAC9K,KAAK,CAAC,CAAC;gBACtB,IAAI0N,GAAG,EAAE;kBACPhB,MAAM,CAACgB,GAAG,CAAC;kBACX;gBACF;gBACAjB,OAAO,CAAC,GAAG3B,IAAI,CAAC;cAClB,CAAC;YACH,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,IAAIoC,UAAU,CAACa,KAAK,KAAK,IAAI,EAAE;UAC7BT,OAAO,CAACJ,UAAU,GAAGA,UAAU,CAACa,KAAK;QACvC;QAEA,MAAMpO,aAAa,GAAG,IAAIiF,aAAa,CAAC;UACtC7B,QAAQ;UACRgI,QAAQ,EAAEA,QAAQ;UAClBzO,UAAU,EAAER,IAAI;UAChBkS,gBAAgB,EAAEvQ,OAAO,CAACuQ,gBAAgB;UAC1CC,IAAI,EAAE,CAAC,CAACxQ,OAAO,CAACwQ,IAAI;UACpBX,OAAO,EAAEA,OAAO;UAChBzN,OAAO,EAAE,CAAC,CAACpC,OAAO,CAACoC;QACrB,CAAC,CAAC;QAEF,IAAIsN,MAAM;QAEV,IAAIhB,OAAO,EAAE;UACXgB,MAAM,GAAG1P,OAAO,CAACgQ,eAAe,GAAGtB,OAAO,CAACE,IAAI,CAAC,MAAMV,eAAe,CAAC,GAAGQ,OAAO;QAClF,CAAC,MAAM;UACLgB,MAAM,GAAG1P,OAAO,CAACgQ,eAAe,GAAG9B,eAAe,GAAGlK,SAAS;QAChE;QAEA,IAAIhE,OAAO,CAAC2P,oBAAoB,EAAE;UAChC,OAAO;YACLzN,aAAa;YACbwN;UACF,CAAC;QACH;QAEArR,IAAI,CAACoS,qBAAqB,CAACvO,aAAa,EAAElC,OAAO,CAAC;QAClD,OAAO0P,MAAM;MACf;MAEA9B,SAASA,CAAC3K,IAAI,EAAEoK,IAAI,EAAErN,OAAO,EAAE;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM3B,IAAI,GAAG,IAAI;QACjB,MAAMqS,SAAS,GAAGzS,GAAG,CAAC6O,wBAAwB,CAACrG,GAAG,CAAC,CAAC;QACpD,MAAMkK,IAAI,GAAGtS,IAAI,CAACoK,eAAe,CAACxF,IAAI,CAAC;QACvC,MAAMmK,mBAAmB,GAAGsD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,YAAY;QACnD,MAAMzD,eAAe,GAAGuD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,gBAAgB;QACnD,MAAMpB,UAAU,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAC;QAEjC,MAAMQ,aAAa,GAAG;UACpB1D,mBAAmB;UACnBqC,UAAU;UACVtC;QACF,CAAC;QACD,IAAI,CAACwD,IAAI,EAAE;UACT,OAAA/J,aAAA,CAAAA,aAAA,KAAYkK,aAAa;YAAE9C,OAAO,EAAE;UAAK;QAC3C;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,MAAM+C,mBAAmB,GAAGA,CAAA,KAAM;UAChC,IAAItB,UAAU,CAACa,KAAK,KAAK,IAAI,EAAE;YAC7Bb,UAAU,CAACa,KAAK,GAAG7R,SAAS,CAACuS,WAAW,CAACN,SAAS,EAAEzN,IAAI,CAAC;UAC3D;UACA,OAAOwM,UAAU,CAACa,KAAK;QACzB,CAAC;QAED,MAAMW,SAAS,GAAGC,MAAM,IAAI;UAC1B7S,IAAI,CAAC4S,SAAS,CAACC,MAAM,CAAC;QACxB,CAAC;QAED,MAAMpD,UAAU,GAAG,IAAIrP,SAAS,CAAC0S,gBAAgB,CAAC;UAChDlO,IAAI;UACJ2N,YAAY,EAAE,IAAI;UAClBM,MAAM,EAAE7S,IAAI,CAAC6S,MAAM,CAAC,CAAC;UACrB/D,eAAe,EAAEnN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmN,eAAe;UACzC8D,SAAS,EAAEA,SAAS;UACpBxB,UAAUA,CAAA,EAAG;YACX,OAAOsB,mBAAmB,CAAC,CAAC;UAC9B;QACF,CAAC,CAAC;;QAEF;QACA;QACA,MAAMlD,cAAc,GAAGA,CAAA,KAAM;UACzB,IAAIlP,MAAM,CAACyL,QAAQ,EAAE;YACnB;YACA;YACA,OAAOzL,MAAM,CAACyS,gBAAgB,CAAC,MAAM;cACnC;cACA,OAAOT,IAAI,CAACpD,KAAK,CAACO,UAAU,EAAE7G,KAAK,CAACoF,KAAK,CAACgB,IAAI,CAAC,CAAC;YAClD,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,OAAOsD,IAAI,CAACpD,KAAK,CAACO,UAAU,EAAE7G,KAAK,CAACoF,KAAK,CAACgB,IAAI,CAAC,CAAC;UAClD;QACJ,CAAC;QACD,OAAAzG,aAAA,CAAAA,aAAA,KAAYkK,aAAa;UAAE9C,OAAO,EAAE,IAAI;UAAEH,cAAc;UAAEC;QAAU;MACtE;;MAEA;MACA;MACA;MACAG,cAAcA,CAAA,EAAG;QACf,IAAI,CAAE,IAAI,CAACoD,qBAAqB,CAAC,CAAC,EAAE;UAClC,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC7B;QAEA9R,MAAM,CAACgD,MAAM,CAAC,IAAI,CAAC8B,OAAO,CAAC,CAAC5B,OAAO,CAAEiI,KAAK,IAAK;UAC7CA,KAAK,CAAC4G,aAAa,CAAC,CAAC;QACvB,CAAC,CAAC;MACJ;;MAEA;MACA;MACA;MACA3B,0BAA0BA,CAACtK,QAAQ,EAAE;QACnC,MAAMjH,IAAI,GAAG,IAAI;QACjB,IAAIA,IAAI,CAACmH,uBAAuB,CAACF,QAAQ,CAAC,EACxC,MAAM,IAAIhD,KAAK,CAAC,kDAAkD,CAAC;QAErE,MAAMkP,WAAW,GAAG,EAAE;QAEtBhS,MAAM,CAACoD,OAAO,CAACvE,IAAI,CAACiG,OAAO,CAAC,CAAC5B,OAAO,CAAC+O,KAAA,IAAyB;UAAA,IAAxB,CAAC5N,UAAU,EAAE8G,KAAK,CAAC,GAAA8G,KAAA;UACvD,MAAMC,SAAS,GAAG/G,KAAK,CAACgH,iBAAiB,CAAC,CAAC;UAC3C;UACA,IAAI,CAAED,SAAS,EAAE;UACjBA,SAAS,CAAChP,OAAO,CAAC,CAACkP,GAAG,EAAEvR,EAAE,KAAK;YAC7BmR,WAAW,CAACjL,IAAI,CAAC;cAAE1C,UAAU;cAAExD;YAAG,CAAC,CAAC;YACpC,IAAI,CAAEiD,MAAM,CAACgD,IAAI,CAACjI,IAAI,CAAC4H,gBAAgB,EAAEpC,UAAU,CAAC,EAAE;cACpDxF,IAAI,CAAC4H,gBAAgB,CAACpC,UAAU,CAAC,GAAG,IAAIyD,UAAU,CAAC,CAAC;YACtD;YACA,MAAM3D,SAAS,GAAGtF,IAAI,CAAC4H,gBAAgB,CAACpC,UAAU,CAAC,CAACgO,UAAU,CAC5DxR,EAAE,EACFb,MAAM,CAAC0E,MAAM,CAAC,IAAI,CACpB,CAAC;YACD,IAAIP,SAAS,CAACiC,cAAc,EAAE;cAC5B;cACA;cACAjC,SAAS,CAACiC,cAAc,CAACN,QAAQ,CAAC,GAAG,IAAI;YAC3C,CAAC,MAAM;cACL;cACA3B,SAAS,CAACI,QAAQ,GAAG6N,GAAG;cACxBjO,SAAS,CAACoC,cAAc,GAAG,EAAE;cAC7BpC,SAAS,CAACiC,cAAc,GAAGpG,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;cAC9CP,SAAS,CAACiC,cAAc,CAACN,QAAQ,CAAC,GAAG,IAAI;YAC3C;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,IAAI,CAAE/B,OAAO,CAACiO,WAAW,CAAC,EAAE;UAC1BnT,IAAI,CAACmH,uBAAuB,CAACF,QAAQ,CAAC,GAAGkM,WAAW;QACtD;MACF;;MAEA;MACA;MACAM,eAAeA,CAAA,EAAG;QAChBtS,MAAM,CAACgD,MAAM,CAAC,IAAI,CAACK,cAAc,CAAC,CAACH,OAAO,CAAEK,GAAG,IAAK;UAClD;UACA;UACA;UACA;UACA;UACA;UACA,IAAIA,GAAG,CAACE,IAAI,KAAK,kCAAkC,EAAE;YACnDF,GAAG,CAACoH,IAAI,CAAC,CAAC;UACZ;QACF,CAAC,CAAC;MACJ;;MAEA;MACA/J,KAAKA,CAAC2R,GAAG,EAAE;QACT,IAAI,CAAClR,OAAO,CAACmR,IAAI,CAACvT,SAAS,CAACwT,YAAY,CAACF,GAAG,CAAC,CAAC;MAChD;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA/O,WAAWA,CAAC+O,GAAG,EAAE;QACf,IAAI,CAAC3R,KAAK,CAAC2R,GAAG,EAAE,IAAI,CAAC;MACvB;;MAEA;MACA;MACA;MACAG,eAAeA,CAACC,KAAK,EAAE;QACrB,IAAI,CAACtR,OAAO,CAACqR,eAAe,CAACC,KAAK,CAAC;MACrC;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;MACEC,MAAMA,CAAA,EAAU;QACd,OAAO,IAAI,CAACvR,OAAO,CAACuR,MAAM,CAAC,GAAA/L,SAAO,CAAC;MACrC;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;MAEEvF,SAASA,CAAA,EAAU;QACjB,OAAO,IAAI,CAACD,OAAO,CAACC,SAAS,CAAC,GAAAuF,SAAO,CAAC;MACxC;;MAEA;AACF;AACA;AACA;AACA;AACA;AACA;MACEpF,UAAUA,CAAA,EAAU;QAClB,OAAO,IAAI,CAACJ,OAAO,CAACI,UAAU,CAAC,GAAAoF,SAAO,CAAC;MACzC;MAEAgM,KAAKA,CAAA,EAAG;QACN,OAAO,IAAI,CAACxR,OAAO,CAACI,UAAU,CAAC;UAAEC,UAAU,EAAE;QAAK,CAAC,CAAC;MACtD;;MAEA;MACA;MACA;MACAgQ,MAAMA,CAAA,EAAG;QACP,IAAI,IAAI,CAACzH,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC+C,MAAM,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAChD,OAAO;MACrB;MAEAyH,SAASA,CAACC,MAAM,EAAE;QAChB;QACA,IAAI,IAAI,CAAC1H,OAAO,KAAK0H,MAAM,EAAE;QAC7B,IAAI,CAAC1H,OAAO,GAAG0H,MAAM;QACrB,IAAI,IAAI,CAACzH,WAAW,EAAE,IAAI,CAACA,WAAW,CAACrE,OAAO,CAAC,CAAC;MAClD;;MAEA;MACA;MACA;MACAiM,qBAAqBA,CAAA,EAAG;QACtB,OACE,CAAE9N,OAAO,CAAC,IAAI,CAACyF,iBAAiB,CAAC,IACjC,CAAEzF,OAAO,CAAC,IAAI,CAACwF,0BAA0B,CAAC;MAE9C;;MAEA;MACA;MACAuJ,yBAAyBA,CAAA,EAAG;QAC1B,MAAMC,QAAQ,GAAG,IAAI,CAAC9P,eAAe;QACrC,OAAOjD,MAAM,CAACgD,MAAM,CAAC+P,QAAQ,CAAC,CAAC1G,IAAI,CAAElJ,OAAO,IAAK,CAAC,CAACA,OAAO,CAACR,WAAW,CAAC;MACzE;MAEA,MAAMqQ,sBAAsBA,CAACvT,GAAG,EAAEwE,OAAO,EAAE;QACzC,MAAMgP,WAAW,GAAGxT,GAAG,CAACA,GAAG;;QAE3B;QACA,IAAIwT,WAAW,KAAK,OAAO,EAAE;UAC3B,MAAM,IAAI,CAACjP,cAAc,CAACvE,GAAG,EAAEwE,OAAO,CAAC;QACzC,CAAC,MAAM,IAAIgP,WAAW,KAAK,SAAS,EAAE;UACpC,IAAI,CAAChO,gBAAgB,CAACxF,GAAG,EAAEwE,OAAO,CAAC;QACrC,CAAC,MAAM,IAAIgP,WAAW,KAAK,SAAS,EAAE;UACpC,IAAI,CAAC9N,gBAAgB,CAAC1F,GAAG,EAAEwE,OAAO,CAAC;QACrC,CAAC,MAAM,IAAIgP,WAAW,KAAK,OAAO,EAAE;UAClC,IAAI,CAAC7N,cAAc,CAAC3F,GAAG,EAAEwE,OAAO,CAAC;QACnC,CAAC,MAAM,IAAIgP,WAAW,KAAK,SAAS,EAAE;UACpC,IAAI,CAACpN,gBAAgB,CAACpG,GAAG,EAAEwE,OAAO,CAAC;QACrC,CAAC,MAAM,IAAIgP,WAAW,KAAK,OAAO,EAAE;UAClC;QAAA,CACD,MAAM;UACL9T,MAAM,CAACS,MAAM,CAAC,+CAA+C,EAAEH,GAAG,CAAC;QACrE;MACF;MAEAyT,sBAAsBA,CAAA,EAAG;QACvB,MAAMrU,IAAI,GAAG,IAAI;QACjB,IAAIA,IAAI,CAACgL,0BAA0B,EAAE;UACnCsJ,YAAY,CAACtU,IAAI,CAACgL,0BAA0B,CAAC;UAC7ChL,IAAI,CAACgL,0BAA0B,GAAG,IAAI;QACxC;QAEAhL,IAAI,CAAC+K,sBAAsB,GAAG,IAAI;QAClC;QACA;QACA;QACA,MAAMwJ,MAAM,GAAGvU,IAAI,CAAC8K,eAAe;QACnC9K,IAAI,CAAC8K,eAAe,GAAG3J,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QAC1C,OAAO0O,MAAM;MACf;;MAEA;AACF;AACA;AACA;MACE,MAAMC,oBAAoBA,CAACpP,OAAO,EAAE;QAClC,MAAMpF,IAAI,GAAG,IAAI;QAEjB,IAAIA,IAAI,CAAC+F,YAAY,IAAI,CAACb,OAAO,CAACE,OAAO,CAAC,EAAE;UAC1C;UACA,KAAK,MAAMkH,KAAK,IAAInL,MAAM,CAACgD,MAAM,CAACnE,IAAI,CAACiG,OAAO,CAAC,EAAE;YAAA,IAAAwO,oBAAA;YAC/C,MAAMnI,KAAK,CAACO,WAAW,CACrB,EAAA4H,oBAAA,GAAArP,OAAO,CAACkH,KAAK,CAACoI,KAAK,CAAC,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBpT,MAAM,KAAI,CAAC,EACjCrB,IAAI,CAAC+F,YACP,CAAC;UACH;UAEA/F,IAAI,CAAC+F,YAAY,GAAG,KAAK;;UAEzB;UACA,KAAK,MAAM,CAAC4O,SAAS,EAAEC,QAAQ,CAAC,IAAIzT,MAAM,CAACoD,OAAO,CAACa,OAAO,CAAC,EAAE;YAC3D,MAAMkH,KAAK,GAAGtM,IAAI,CAACiG,OAAO,CAAC0O,SAAS,CAAC;YACrC,IAAIrI,KAAK,EAAE;cACT;cACA;cACA,MAAMuI,UAAU,GAAG,GAAG;cACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACvT,MAAM,EAAEyT,CAAC,IAAID,UAAU,EAAE;gBACpD,MAAME,KAAK,GAAGH,QAAQ,CAAC7L,KAAK,CAAC+L,CAAC,EAAEE,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGD,UAAU,EAAED,QAAQ,CAACvT,MAAM,CAAC,CAAC;gBAE1E,KAAK,MAAMT,GAAG,IAAImU,KAAK,EAAE;kBACvB,MAAMzI,KAAK,CAACQ,MAAM,CAAClM,GAAG,CAAC;gBACzB;gBAEA,MAAM,IAAI8P,OAAO,CAACC,OAAO,IAAIuE,OAAO,CAACC,QAAQ,CAACxE,OAAO,CAAC,CAAC;cACzD;YACF,CAAC,MAAM;cACL;cACA3Q,IAAI,CAAC4K,wBAAwB,CAAC+J,SAAS,CAAC,GACtC3U,IAAI,CAAC4K,wBAAwB,CAAC+J,SAAS,CAAC,IAAI,EAAE;cAChD3U,IAAI,CAAC4K,wBAAwB,CAAC+J,SAAS,CAAC,CAACzM,IAAI,CAAC,GAAG0M,QAAQ,CAAC;YAC5D;UACF;;UAEA;UACA,KAAK,MAAMtI,KAAK,IAAInL,MAAM,CAACgD,MAAM,CAACnE,IAAI,CAACiG,OAAO,CAAC,EAAE;YAC/C,MAAMqG,KAAK,CAACS,SAAS,CAAC,CAAC;UACzB;QACF;QAEA/M,IAAI,CAACoV,wBAAwB,CAAC,CAAC;MACjC;;MAEA;AACF;AACA;AACA;MACEC,oBAAoBA,CAACjQ,OAAO,EAAE;QAC5B,MAAMpF,IAAI,GAAG,IAAI;QAEjB,IAAIA,IAAI,CAAC+F,YAAY,IAAI,CAACb,OAAO,CAACE,OAAO,CAAC,EAAE;UAC1C;UACAjE,MAAM,CAACgD,MAAM,CAACnE,IAAI,CAACiG,OAAO,CAAC,CAAC5B,OAAO,CAACiI,KAAK,IAAI;YAAA,IAAAgJ,qBAAA;YAC3ChJ,KAAK,CAACO,WAAW,CACf,EAAAyI,qBAAA,GAAAlQ,OAAO,CAACkH,KAAK,CAACoI,KAAK,CAAC,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBjU,MAAM,KAAI,CAAC,EACjCrB,IAAI,CAAC+F,YACP,CAAC;UACH,CAAC,CAAC;UAEF/F,IAAI,CAAC+F,YAAY,GAAG,KAAK;UAEzB5E,MAAM,CAACoD,OAAO,CAACa,OAAO,CAAC,CAACf,OAAO,CAACkR,KAAA,IAA2B;YAAA,IAA1B,CAACZ,SAAS,EAAEC,QAAQ,CAAC,GAAAW,KAAA;YACpD,MAAMjJ,KAAK,GAAGtM,IAAI,CAACiG,OAAO,CAAC0O,SAAS,CAAC;YACrC,IAAIrI,KAAK,EAAE;cACTsI,QAAQ,CAACvQ,OAAO,CAACzD,GAAG,IAAI0L,KAAK,CAACQ,MAAM,CAAClM,GAAG,CAAC,CAAC;YAC5C,CAAC,MAAM;cACLZ,IAAI,CAAC4K,wBAAwB,CAAC+J,SAAS,CAAC,GACtC3U,IAAI,CAAC4K,wBAAwB,CAAC+J,SAAS,CAAC,IAAI,EAAE;cAChD3U,IAAI,CAAC4K,wBAAwB,CAAC+J,SAAS,CAAC,CAACzM,IAAI,CAAC,GAAG0M,QAAQ,CAAC;YAC5D;UACF,CAAC,CAAC;UAEFzT,MAAM,CAACgD,MAAM,CAACnE,IAAI,CAACiG,OAAO,CAAC,CAAC5B,OAAO,CAACiI,KAAK,IAAIA,KAAK,CAACS,SAAS,CAAC,CAAC,CAAC;QACjE;QAEA/M,IAAI,CAACoV,wBAAwB,CAAC,CAAC;MACjC;;MAEA;AACF;AACA;AACA;MACE,MAAMnC,oBAAoBA,CAAA,EAAG;QAC3B,MAAMjT,IAAI,GAAG,IAAI;QACjB,MAAMuU,MAAM,GAAGvU,IAAI,CAACqU,sBAAsB,CAAC,CAAC;QAE5C,OAAO/T,MAAM,CAACgL,QAAQ,GAClBtL,IAAI,CAACqV,oBAAoB,CAACd,MAAM,CAAC,GACjCvU,IAAI,CAACwU,oBAAoB,CAACD,MAAM,CAAC;MACvC;;MAEA;MACA;MACA;MACAa,wBAAwBA,CAAA,EAAG;QACzB,MAAMpV,IAAI,GAAG,IAAI;QACjB,MAAMkN,SAAS,GAAGlN,IAAI,CAACwK,qBAAqB;QAC5CxK,IAAI,CAACwK,qBAAqB,GAAG,EAAE;QAC/B0C,SAAS,CAAC7I,OAAO,CAAEsD,CAAC,IAAK;UACvBA,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;MACJ;;MAEA;MACA;MACA;MACAjB,+BAA+BA,CAAC+G,CAAC,EAAE;QACjC,MAAMzN,IAAI,GAAG,IAAI;QACjB,MAAMwV,gBAAgB,GAAGA,CAAA,KAAM;UAC7BxV,IAAI,CAACwK,qBAAqB,CAACtC,IAAI,CAACuF,CAAC,CAAC;QACpC,CAAC;QACD,IAAIgI,uBAAuB,GAAG,CAAC;QAC/B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;UAC7B,EAAED,uBAAuB;UACzB,IAAIA,uBAAuB,KAAK,CAAC,EAAE;YACjC;YACA;YACAD,gBAAgB,CAAC,CAAC;UACpB;QACF,CAAC;QAEDrU,MAAM,CAACgD,MAAM,CAACnE,IAAI,CAAC4H,gBAAgB,CAAC,CAACvD,OAAO,CAAEsR,eAAe,IAAK;UAChEA,eAAe,CAACtR,OAAO,CAAEiB,SAAS,IAAK;YACrC,MAAMsQ,sCAAsC,GAC1CxU,IAAI,CAACkE,SAAS,CAACiC,cAAc,CAAC,CAACiG,IAAI,CAACvG,QAAQ,IAAI;cAC9C,MAAM3C,OAAO,GAAGtE,IAAI,CAACoE,eAAe,CAAC6C,QAAQ,CAAC;cAC9C,OAAO3C,OAAO,IAAIA,OAAO,CAACR,WAAW;YACvC,CAAC,CAAC;YAEJ,IAAI8R,sCAAsC,EAAE;cAC1C,EAAEH,uBAAuB;cACzBnQ,SAAS,CAACoC,cAAc,CAACQ,IAAI,CAACwN,gBAAgB,CAAC;YACjD;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,IAAID,uBAAuB,KAAK,CAAC,EAAE;UACjC;UACA;UACAD,gBAAgB,CAAC,CAAC;QACpB;MACF;MAEApD,qBAAqBA,CAACvO,aAAa,EAAElC,OAAO,EAAE;QAC5C,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEwQ,IAAI,EAAE;UACjB;UACA,IAAI,CAAC1O,wBAAwB,CAACyE,IAAI,CAAC;YACjCiK,IAAI,EAAE,IAAI;YACVxO,OAAO,EAAE,CAACE,aAAa;UACzB,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA;UACA,IAAIqB,OAAO,CAAC,IAAI,CAACzB,wBAAwB,CAAC,IACtCuF,IAAI,CAAC,IAAI,CAACvF,wBAAwB,CAAC,CAAC0O,IAAI,EAAE;YAC5C,IAAI,CAAC1O,wBAAwB,CAACyE,IAAI,CAAC;cACjCiK,IAAI,EAAE,KAAK;cACXxO,OAAO,EAAE;YACX,CAAC,CAAC;UACJ;UAEAqF,IAAI,CAAC,IAAI,CAACvF,wBAAwB,CAAC,CAACE,OAAO,CAACuE,IAAI,CAACrE,aAAa,CAAC;QACjE;;QAEA;QACA,IAAI,IAAI,CAACJ,wBAAwB,CAACpC,MAAM,KAAK,CAAC,EAAE;UAC9CwC,aAAa,CAACgS,WAAW,CAAC,CAAC;QAC7B;MACF;;MAEA;MACA;MACA;MACAC,0BAA0BA,CAAA,EAAG;QAC3B,MAAM9V,IAAI,GAAG,IAAI;QACjB,IAAIA,IAAI,CAACiU,yBAAyB,CAAC,CAAC,EAAE;;QAEtC;QACA;QACA;QACA,IAAI,CAAE/O,OAAO,CAAClF,IAAI,CAACyD,wBAAwB,CAAC,EAAE;UAC5C,MAAMsS,UAAU,GAAG/V,IAAI,CAACyD,wBAAwB,CAACS,KAAK,CAAC,CAAC;UACxD,IAAI,CAAEgB,OAAO,CAAC6Q,UAAU,CAACpS,OAAO,CAAC,EAC/B,MAAM,IAAIM,KAAK,CACb,6CAA6C,GAC3CoD,IAAI,CAACC,SAAS,CAACyO,UAAU,CAC7B,CAAC;;UAEH;UACA,IAAI,CAAE7Q,OAAO,CAAClF,IAAI,CAACyD,wBAAwB,CAAC,EAC1CzD,IAAI,CAACgW,uBAAuB,CAAC,CAAC;QAClC;;QAEA;QACAhW,IAAI,CAACiW,aAAa,CAAC,CAAC;MACtB;;MAEA;MACA;MACAD,uBAAuBA,CAAA,EAAG;QACxB,MAAMhW,IAAI,GAAG,IAAI;QAEjB,IAAIkF,OAAO,CAAClF,IAAI,CAACyD,wBAAwB,CAAC,EAAE;UAC1C;QACF;QAEAzD,IAAI,CAACyD,wBAAwB,CAAC,CAAC,CAAC,CAACE,OAAO,CAACU,OAAO,CAAC6R,CAAC,IAAI;UACpDA,CAAC,CAACL,WAAW,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ;MAEAM,oCAAoCA,CAACC,0BAA0B,EAAE;QAC/D,MAAMpW,IAAI,GAAG,IAAI;QACjB,IAAIkF,OAAO,CAACkR,0BAA0B,CAAC,EAAE;;QAEzC;QACA;QACA;QACA,IAAIlR,OAAO,CAAClF,IAAI,CAACyD,wBAAwB,CAAC,EAAE;UAC1CzD,IAAI,CAACyD,wBAAwB,GAAG2S,0BAA0B;UAC1DpW,IAAI,CAACgW,uBAAuB,CAAC,CAAC;UAC9B;QACF;;QAEA;QACA;QACA;QACA,IACE,CAAChN,IAAI,CAAChJ,IAAI,CAACyD,wBAAwB,CAAC,CAAC0O,IAAI,IACzC,CAACiE,0BAA0B,CAAC,CAAC,CAAC,CAACjE,IAAI,EACnC;UACAiE,0BAA0B,CAAC,CAAC,CAAC,CAACzS,OAAO,CAACU,OAAO,CAAE6R,CAAC,IAAK;YACnDlN,IAAI,CAAChJ,IAAI,CAACyD,wBAAwB,CAAC,CAACE,OAAO,CAACuE,IAAI,CAACgO,CAAC,CAAC;;YAEnD;YACA,IAAIlW,IAAI,CAACyD,wBAAwB,CAACpC,MAAM,KAAK,CAAC,EAAE;cAC9C6U,CAAC,CAACL,WAAW,CAAC,CAAC;YACjB;UACF,CAAC,CAAC;UAEFO,0BAA0B,CAAClS,KAAK,CAAC,CAAC;QACpC;;QAEA;QACAlE,IAAI,CAACyD,wBAAwB,CAACyE,IAAI,CAAC,GAAGkO,0BAA0B,CAAC;MACnE;MAEAjT,oDAAoDA,CAAA,EAAG;QACrD,MAAMnD,IAAI,GAAG,IAAI;QACjB,MAAMoW,0BAA0B,GAAGpW,IAAI,CAACyD,wBAAwB;QAChEzD,IAAI,CAACyD,wBAAwB,GAAG,EAAE;QAElCzD,IAAI,CAAC6J,WAAW,IAAI7J,IAAI,CAAC6J,WAAW,CAAC,CAAC;QACtCjK,GAAG,CAACyW,cAAc,CAACC,IAAI,CAAErH,QAAQ,IAAK;UACpCA,QAAQ,CAACjP,IAAI,CAAC;UACd,OAAO,IAAI;QACb,CAAC,CAAC;QAEFA,IAAI,CAACmW,oCAAoC,CAACC,0BAA0B,CAAC;MACvE;;MAEA;MACAzK,eAAeA,CAAA,EAAG;QAChB,OAAOzG,OAAO,CAAC,IAAI,CAACd,eAAe,CAAC;MACtC;;MAEA;MACA;MACA6R,aAAaA,CAAA,EAAG;QACd,MAAMjW,IAAI,GAAG,IAAI;QACjB,IAAIA,IAAI,CAAC6K,aAAa,IAAI7K,IAAI,CAAC2L,eAAe,CAAC,CAAC,EAAE;UAChD3L,IAAI,CAAC6K,aAAa,CAAC,CAAC;UACpB7K,IAAI,CAAC6K,aAAa,GAAG,IAAI;QAC3B;MACF;IACF;IAAC/K,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICj6CDP,MAAM,CAACQ,MAAM,CAAC;MAACgJ,iBAAiB,EAACA,CAAA,KAAIA;IAAiB,CAAC,CAAC;IAAC,IAAI9I,SAAS;IAACV,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;MAACS,SAASA,CAACC,CAAC,EAAC;QAACD,SAAS,GAACC,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACZ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACW,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIT,GAAG;IAACF,MAAM,CAACC,IAAI,CAAC,gBAAgB,EAAC;MAACC,GAAGA,CAACS,CAAC,EAAC;QAACT,GAAG,GAACS,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIuI,KAAK;IAAClJ,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACiJ,KAAKA,CAACvI,CAAC,EAAC;QAACuI,KAAK,GAACvI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAI6E,OAAO,EAACD,MAAM;IAACvF,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAC;MAACuF,OAAOA,CAAC7E,CAAC,EAAC;QAAC6E,OAAO,GAAC7E,CAAC;MAAA,CAAC;MAAC4E,MAAMA,CAAC5E,CAAC,EAAC;QAAC4E,MAAM,GAAC5E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIR,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAMvd,MAAMqJ,iBAAiB,CAAC;MAC7B3I,WAAWA,CAACC,UAAU,EAAE;QACtB,IAAI,CAACC,WAAW,GAAGD,UAAU;MAC/B;;MAEA;AACF;AACA;AACA;MACE,MAAMkB,mBAAmBA,CAACd,GAAG,EAAE;QAC7B,MAAMZ,IAAI,GAAG,IAAI,CAACS,WAAW;QAE7B,IAAIT,IAAI,CAACuB,QAAQ,KAAK,MAAM,IAAIvB,IAAI,CAACsK,kBAAkB,KAAK,CAAC,EAAE;UAC7DtK,IAAI,CAACgB,UAAU,GAAG,IAAIZ,SAAS,CAACmW,SAAS,CAAC;YACxCnN,iBAAiB,EAAEpJ,IAAI,CAACsK,kBAAkB;YAC1CjB,gBAAgB,EAAErJ,IAAI,CAACuK,iBAAiB;YACxCiM,SAASA,CAAA,EAAG;cACVxW,IAAI,CAAC6T,eAAe,CAClB,IAAIjU,GAAG,CAACmK,eAAe,CAAC,yBAAyB,CACnD,CAAC;YACH,CAAC;YACD0M,QAAQA,CAAA,EAAG;cACTzW,IAAI,CAAC+B,KAAK,CAAC;gBAAEnB,GAAG,EAAE;cAAO,CAAC,CAAC;YAC7B;UACF,CAAC,CAAC;UACFZ,IAAI,CAACgB,UAAU,CAAC0V,KAAK,CAAC,CAAC;QACzB;;QAEA;QACA,IAAI1W,IAAI,CAACqD,cAAc,EAAErD,IAAI,CAAC+F,YAAY,GAAG,IAAI;QAEjD,IAAI4Q,4BAA4B;QAChC,IAAI,OAAO/V,GAAG,CAAC0C,OAAO,KAAK,QAAQ,EAAE;UACnCqT,4BAA4B,GAAG3W,IAAI,CAACqD,cAAc,KAAKzC,GAAG,CAAC0C,OAAO;UAClEtD,IAAI,CAACqD,cAAc,GAAGzC,GAAG,CAAC0C,OAAO;QACnC;QAEA,IAAIqT,4BAA4B,EAAE;UAChC;UACA;QACF;;QAEA;;QAEA;QACA;QACA3W,IAAI,CAAC4K,wBAAwB,GAAGzJ,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QAEnD,IAAI7F,IAAI,CAAC+F,YAAY,EAAE;UACrB;UACA;UACA/F,IAAI,CAACmH,uBAAuB,GAAGhG,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;UAClD7F,IAAI,CAAC4H,gBAAgB,GAAGzG,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QAC7C;;QAEA;QACA7F,IAAI,CAACwK,qBAAqB,GAAG,EAAE;;QAE/B;QACAxK,IAAI,CAAC2K,iBAAiB,GAAGxJ,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QAC5C1E,MAAM,CAACoD,OAAO,CAACvE,IAAI,CAACwE,cAAc,CAAC,CAACH,OAAO,CAACI,IAAA,IAAe;UAAA,IAAd,CAACzC,EAAE,EAAE0C,GAAG,CAAC,GAAAD,IAAA;UACpD,IAAIC,GAAG,CAACkC,KAAK,EAAE;YACb5G,IAAI,CAAC2K,iBAAiB,CAAC3I,EAAE,CAAC,GAAG,IAAI;UACnC;QACF,CAAC,CAAC;;QAEF;QACA;QACA;QACA;QACA;QACA;QACA;QACAhC,IAAI,CAAC0K,0BAA0B,GAAGvJ,MAAM,CAAC0E,MAAM,CAAC,IAAI,CAAC;QACrD,IAAI7F,IAAI,CAAC+F,YAAY,EAAE;UACrB,MAAMmO,QAAQ,GAAGlU,IAAI,CAACoE,eAAe;UACrCjD,MAAM,CAACC,IAAI,CAAC8S,QAAQ,CAAC,CAAC7P,OAAO,CAACrC,EAAE,IAAI;YAClC,MAAMsC,OAAO,GAAG4P,QAAQ,CAAClS,EAAE,CAAC;YAC5B,IAAIsC,OAAO,CAACsS,SAAS,CAAC,CAAC,EAAE;cACvB;cACA;cACA;cACA;cACA5W,IAAI,CAACwK,qBAAqB,CAACtC,IAAI,CAC7B;gBAAA,OAAa5D,OAAO,CAACyD,WAAW,CAAC,GAAAC,SAAO,CAAC;cAAA,CAC3C,CAAC;YACH,CAAC,MAAM,IAAI1D,OAAO,CAACR,WAAW,EAAE;cAC9B;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA9D,IAAI,CAAC0K,0BAA0B,CAACpG,OAAO,CAAC2C,QAAQ,CAAC,GAAG,IAAI;YAC1D;UACF,CAAC,CAAC;QACJ;QAEAjH,IAAI,CAACyK,gCAAgC,GAAG,EAAE;;QAE1C;QACA;QACA,IAAI,CAACzK,IAAI,CAACgT,qBAAqB,CAAC,CAAC,EAAE;UACjC,IAAIhT,IAAI,CAAC+F,YAAY,EAAE;YACrB,KAAK,MAAMuG,KAAK,IAAInL,MAAM,CAACgD,MAAM,CAACnE,IAAI,CAACiG,OAAO,CAAC,EAAE;cAC/C,MAAMqG,KAAK,CAACO,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;cAChC,MAAMP,KAAK,CAACS,SAAS,CAAC,CAAC;YACzB;YACA/M,IAAI,CAAC+F,YAAY,GAAG,KAAK;UAC3B;UACA/F,IAAI,CAACoV,wBAAwB,CAAC,CAAC;QACjC;MACF;;MAEA;AACF;AACA;AACA;MACE,MAAMnT,cAAcA,CAACrB,GAAG,EAAE;QACxB,MAAMZ,IAAI,GAAG,IAAI,CAACS,WAAW;QAE7B,IAAIT,IAAI,CAACgT,qBAAqB,CAAC,CAAC,EAAE;UAChChT,IAAI,CAACyK,gCAAgC,CAACvC,IAAI,CAACtH,GAAG,CAAC;UAE/C,IAAIA,GAAG,CAACA,GAAG,KAAK,OAAO,EAAE;YACvB,OAAOZ,IAAI,CAAC2K,iBAAiB,CAAC/J,GAAG,CAACoB,EAAE,CAAC;UACvC;UAEA,IAAIpB,GAAG,CAAC4F,IAAI,EAAE;YACZ5F,GAAG,CAAC4F,IAAI,CAACnC,OAAO,CAACoC,KAAK,IAAI;cACxB,OAAOzG,IAAI,CAAC2K,iBAAiB,CAAClE,KAAK,CAAC;YACtC,CAAC,CAAC;UACJ;UAEA,IAAI7F,GAAG,CAAC+C,OAAO,EAAE;YACf/C,GAAG,CAAC+C,OAAO,CAACU,OAAO,CAAC4C,QAAQ,IAAI;cAC9B,OAAOjH,IAAI,CAAC0K,0BAA0B,CAACzD,QAAQ,CAAC;YAClD,CAAC,CAAC;UACJ;UAEA,IAAIjH,IAAI,CAACgT,qBAAqB,CAAC,CAAC,EAAE;YAChC;UACF;;UAEA;UACA;UACA;UACA,MAAM6D,gBAAgB,GAAG7W,IAAI,CAACyK,gCAAgC;UAC9D,KAAK,MAAMqM,eAAe,IAAI3V,MAAM,CAACgD,MAAM,CAAC0S,gBAAgB,CAAC,EAAE;YAC7D,MAAM,IAAI,CAAC1C,sBAAsB,CAC/B2C,eAAe,EACf9W,IAAI,CAAC8K,eACP,CAAC;UACH;UACA9K,IAAI,CAACyK,gCAAgC,GAAG,EAAE;QAC5C,CAAC,MAAM;UACL,MAAM,IAAI,CAAC0J,sBAAsB,CAACvT,GAAG,EAAEZ,IAAI,CAAC8K,eAAe,CAAC;QAC9D;;QAEA;QACA;QACA;QACA,MAAMiM,aAAa,GACjBnW,GAAG,CAACA,GAAG,KAAK,OAAO,IACnBA,GAAG,CAACA,GAAG,KAAK,SAAS,IACrBA,GAAG,CAACA,GAAG,KAAK,SAAS;QAEvB,IAAIZ,IAAI,CAACiL,uBAAuB,KAAK,CAAC,IAAI,CAAC8L,aAAa,EAAE;UACxD,MAAM/W,IAAI,CAACiT,oBAAoB,CAAC,CAAC;UACjC;QACF;QAEA,IAAIjT,IAAI,CAAC+K,sBAAsB,KAAK,IAAI,EAAE;UACxC/K,IAAI,CAAC+K,sBAAsB,GACzB,IAAIiM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGjX,IAAI,CAACkL,qBAAqB;QACrD,CAAC,MAAM,IAAIlL,IAAI,CAAC+K,sBAAsB,GAAG,IAAIiM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE;UAC7D,MAAMjX,IAAI,CAACiT,oBAAoB,CAAC,CAAC;UACjC;QACF;QAEA,IAAIjT,IAAI,CAACgL,0BAA0B,EAAE;UACnCsJ,YAAY,CAACtU,IAAI,CAACgL,0BAA0B,CAAC;QAC/C;QACAhL,IAAI,CAACgL,0BAA0B,GAAGkM,UAAU,CAAC,MAAM;UACjDlX,IAAI,CAACmX,sBAAsB,GAAGnX,IAAI,CAACiT,oBAAoB,CAAC,CAAC;UACzD,IAAI3S,MAAM,CAACyP,UAAU,CAAC/P,IAAI,CAACmX,sBAAsB,CAAC,EAAE;YAClDnX,IAAI,CAACmX,sBAAsB,CAACC,OAAO,CACjC,MAAOpX,IAAI,CAACmX,sBAAsB,GAAGxR,SACvC,CAAC;UACH;QACF,CAAC,EAAE3F,IAAI,CAACiL,uBAAuB,CAAC;MAClC;;MAEA;AACF;AACA;AACA;MACE,MAAMkJ,sBAAsBA,CAACvT,GAAG,EAAEwE,OAAO,EAAE;QACzC,MAAMgP,WAAW,GAAGxT,GAAG,CAACA,GAAG;QAE3B,QAAQwT,WAAW;UACjB,KAAK,OAAO;YACV,MAAM,IAAI,CAAC3T,WAAW,CAAC0E,cAAc,CAACvE,GAAG,EAAEwE,OAAO,CAAC;YACnD;UACF,KAAK,SAAS;YACZ,IAAI,CAAC3E,WAAW,CAAC2F,gBAAgB,CAACxF,GAAG,EAAEwE,OAAO,CAAC;YAC/C;UACF,KAAK,SAAS;YACZ,IAAI,CAAC3E,WAAW,CAAC6F,gBAAgB,CAAC1F,GAAG,EAAEwE,OAAO,CAAC;YAC/C;UACF,KAAK,OAAO;YACV,IAAI,CAAC3E,WAAW,CAAC8F,cAAc,CAAC3F,GAAG,EAAEwE,OAAO,CAAC;YAC7C;UACF,KAAK,SAAS;YACZ,IAAI,CAAC3E,WAAW,CAACuG,gBAAgB,CAACpG,GAAG,EAAEwE,OAAO,CAAC;YAC/C;UACF,KAAK,OAAO;YACV;YACA;UACF;YACE9E,MAAM,CAACS,MAAM,CAAC,+CAA+C,EAAEH,GAAG,CAAC;QACvE;MACF;;MAEA;AACF;AACA;AACA;MACE,MAAMuB,gBAAgBA,CAACvB,GAAG,EAAE;QAC1B,MAAMZ,IAAI,GAAG,IAAI,CAACS,WAAW;;QAE7B;QACA,IAAI,CAACyE,OAAO,CAAClF,IAAI,CAAC8K,eAAe,CAAC,EAAE;UAClC,MAAM9K,IAAI,CAACiT,oBAAoB,CAAC,CAAC;QACnC;;QAEA;QACA;QACA,IAAI/N,OAAO,CAAClF,IAAI,CAACyD,wBAAwB,CAAC,EAAE;UAC1CnD,MAAM,CAACS,MAAM,CAAC,mDAAmD,CAAC;UAClE;QACF;QACA,MAAM2C,kBAAkB,GAAG1D,IAAI,CAACyD,wBAAwB,CAAC,CAAC,CAAC,CAACE,OAAO;QACnE,IAAImR,CAAC;QACL,MAAMoB,CAAC,GAAGxS,kBAAkB,CAACiK,IAAI,CAAC,CAACnB,MAAM,EAAE6K,GAAG,KAAK;UACjD,MAAMC,KAAK,GAAG9K,MAAM,CAACvF,QAAQ,KAAKrG,GAAG,CAACoB,EAAE;UACxC,IAAIsV,KAAK,EAAExC,CAAC,GAAGuC,GAAG;UAClB,OAAOC,KAAK;QACd,CAAC,CAAC;QACF,IAAI,CAACpB,CAAC,EAAE;UACN5V,MAAM,CAACS,MAAM,CAAC,qDAAqD,EAAEH,GAAG,CAAC;UACzE;QACF;;QAEA;QACA;QACA;QACA8C,kBAAkB,CAAC6T,MAAM,CAACzC,CAAC,EAAE,CAAC,CAAC;QAE/B,IAAI7P,MAAM,CAACgD,IAAI,CAACrH,GAAG,EAAE,OAAO,CAAC,EAAE;UAC7BsV,CAAC,CAAClS,aAAa,CACb,IAAI1D,MAAM,CAAC2D,KAAK,CAACrD,GAAG,CAACkT,KAAK,CAACA,KAAK,EAAElT,GAAG,CAACkT,KAAK,CAAC0D,MAAM,EAAE5W,GAAG,CAACkT,KAAK,CAAC2D,OAAO,CACvE,CAAC;QACH,CAAC,MAAM;UACL;UACAvB,CAAC,CAAClS,aAAa,CAAC2B,SAAS,EAAE/E,GAAG,CAACyQ,MAAM,CAAC;QACxC;MACF;;MAEA;AACF;AACA;AACA;MACE,MAAMnP,eAAeA,CAACtB,GAAG,EAAE;QACzB,MAAMZ,IAAI,GAAG,IAAI,CAACS,WAAW;;QAE7B;QACA;QACA,MAAM,IAAI,CAACwB,cAAc,CAACrB,GAAG,CAAC;;QAE9B;QACA;;QAEA;QACA,IAAI,CAACqE,MAAM,CAACgD,IAAI,CAACjI,IAAI,CAACwE,cAAc,EAAE5D,GAAG,CAACoB,EAAE,CAAC,EAAE;UAC7C;QACF;;QAEA;QACA,MAAM8L,aAAa,GAAG9N,IAAI,CAACwE,cAAc,CAAC5D,GAAG,CAACoB,EAAE,CAAC,CAAC8L,aAAa;QAC/D,MAAMC,YAAY,GAAG/N,IAAI,CAACwE,cAAc,CAAC5D,GAAG,CAACoB,EAAE,CAAC,CAAC+L,YAAY;QAE7D/N,IAAI,CAACwE,cAAc,CAAC5D,GAAG,CAACoB,EAAE,CAAC,CAAC6F,MAAM,CAAC,CAAC;QAEpC,MAAM6P,kBAAkB,GAAGC,MAAM,IAAI;UACnC,OACEA,MAAM,IACNA,MAAM,CAAC7D,KAAK,IACZ,IAAIxT,MAAM,CAAC2D,KAAK,CACd0T,MAAM,CAAC7D,KAAK,CAACA,KAAK,EAClB6D,MAAM,CAAC7D,KAAK,CAAC0D,MAAM,EACnBG,MAAM,CAAC7D,KAAK,CAAC2D,OACf,CAAC;QAEL,CAAC;;QAED;QACA,IAAI3J,aAAa,IAAIlN,GAAG,CAACkT,KAAK,EAAE;UAC9BhG,aAAa,CAAC4J,kBAAkB,CAAC9W,GAAG,CAAC,CAAC;QACxC;QAEA,IAAImN,YAAY,EAAE;UAChBA,YAAY,CAAC2J,kBAAkB,CAAC9W,GAAG,CAAC,CAAC;QACvC;MACF;;MAEA;AACF;AACA;AACA;MACEwB,eAAeA,CAACxB,GAAG,EAAE;QACnBN,MAAM,CAACS,MAAM,CAAC,8BAA8B,EAAEH,GAAG,CAAC4W,MAAM,CAAC;QACzD,IAAI5W,GAAG,CAACgX,gBAAgB,EAAEtX,MAAM,CAACS,MAAM,CAAC,OAAO,EAAEH,GAAG,CAACgX,gBAAgB,CAAC;MACxE;;MAEA;IACF;IAAC9X,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;AC/UDP,MAAM,CAACQ,MAAM,CAAC;EAAC4I,aAAa,EAACA,CAAA,KAAIA;AAAa,CAAC,CAAC;AAKzC,MAAMA,aAAa,CAAC;EACzBvI,WAAWA,CAACoB,OAAO,EAAE;IACnB;IACA,IAAI,CAACsF,QAAQ,GAAGtF,OAAO,CAACsF,QAAQ;IAChC,IAAI,CAACnD,WAAW,GAAG,KAAK;IAExB,IAAI,CAAC+T,SAAS,GAAGlW,OAAO,CAACsN,QAAQ;IACjC,IAAI,CAACxO,WAAW,GAAGkB,OAAO,CAACnB,UAAU;IACrC,IAAI,CAACsX,QAAQ,GAAGnW,OAAO,CAAC6P,OAAO;IAC/B,IAAI,CAACuG,iBAAiB,GAAGpW,OAAO,CAACuQ,gBAAgB,KAAK,MAAM,CAAC,CAAC,CAAC;IAC/D,IAAI,CAAC8F,KAAK,GAAGrW,OAAO,CAACwQ,IAAI;IACzB,IAAI,CAACpO,OAAO,GAAGpC,OAAO,CAACoC,OAAO;IAC9B,IAAI,CAACkU,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,YAAY,GAAG,KAAK;;IAEzB;IACA,IAAI,CAACzX,WAAW,CAAC2D,eAAe,CAAC,IAAI,CAAC6C,QAAQ,CAAC,GAAG,IAAI;EACxD;EACA;EACA;EACA4O,WAAWA,CAAA,EAAG;IACZ;IACA;IACA;IACA,IAAI,IAAI,CAACe,SAAS,CAAC,CAAC,EAClB,MAAM,IAAI3S,KAAK,CAAC,+CAA+C,CAAC;;IAElE;IACA;IACA,IAAI,CAACiU,YAAY,GAAG,KAAK;IACzB,IAAI,CAACpU,WAAW,GAAG,IAAI;;IAEvB;IACA;IACA,IAAI,IAAI,CAACkU,KAAK,EACZ,IAAI,CAACvX,WAAW,CAACiK,0BAA0B,CAAC,IAAI,CAACzD,QAAQ,CAAC,GAAG,IAAI;;IAEnE;IACA,IAAI,CAACxG,WAAW,CAACsB,KAAK,CAAC,IAAI,CAAC+V,QAAQ,CAAC;EACvC;EACA;EACA;EACAK,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACF,aAAa,IAAI,IAAI,CAACC,YAAY,EAAE;MAC3C;MACA;MACA,IAAI,CAACL,SAAS,CAAC,IAAI,CAACI,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,CAAC;;MAE5D;MACA,OAAO,IAAI,CAACxX,WAAW,CAAC2D,eAAe,CAAC,IAAI,CAAC6C,QAAQ,CAAC;;MAEtD;MACA;MACA,IAAI,CAACxG,WAAW,CAACqV,0BAA0B,CAAC,CAAC;IAC/C;EACF;EACA;EACA;EACA;EACA;EACA9R,aAAaA,CAAC4N,GAAG,EAAEP,MAAM,EAAE;IACzB,IAAI,IAAI,CAACuF,SAAS,CAAC,CAAC,EAClB,MAAM,IAAI3S,KAAK,CAAC,0CAA0C,CAAC;IAC7D,IAAI,CAACgU,aAAa,GAAG,CAACrG,GAAG,EAAEP,MAAM,CAAC;IAClC,IAAI,CAAC0G,iBAAiB,CAACnG,GAAG,EAAEP,MAAM,CAAC;IACnC,IAAI,CAAC8G,oBAAoB,CAAC,CAAC;EAC7B;EACA;EACA;EACA;EACA;EACApQ,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACmQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B;EACA;EACAvB,SAASA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACqB,aAAa;EAC7B;AACF,C;;;;;;;;;;;;;;ICpFAvY,MAAM,CAACQ,MAAM,CAAC;MAAC+I,UAAU,EAACA,CAAA,KAAIA;IAAU,CAAC,CAAC;IAAC,IAAIlE,OAAO;IAACrF,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;MAACoF,OAAOA,CAAC1E,CAAC,EAAC;QAAC0E,OAAO,GAAC1E,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIR,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAErK,MAAMoJ,UAAU,SAASmP,KAAK,CAAC;MACpC7X,WAAWA,CAAA,EAAG;QACZ,KAAK,CAACwE,OAAO,CAACyC,WAAW,EAAEzC,OAAO,CAACM,OAAO,CAAC;MAC7C;IACF;IAACvF,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICNDP,MAAM,CAACQ,MAAM,CAAC;MAACN,GAAG,EAACA,CAAA,KAAIA;IAAG,CAAC,CAAC;IAAC,IAAIQ,SAAS;IAACV,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAC;MAACS,SAASA,CAACC,CAAC,EAAC;QAACD,SAAS,GAACC,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIC,MAAM;IAACZ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACW,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIqI,UAAU;IAAChJ,MAAM,CAACC,IAAI,CAAC,0BAA0B,EAAC;MAAC+I,UAAUA,CAACrI,CAAC,EAAC;QAACqI,UAAU,GAACrI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIR,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAK7T;IACA;IACA;IACA,MAAMwY,cAAc,GAAG,EAAE;;IAEzB;AACA;AACA;AACA;IACO,MAAMzY,GAAG,GAAG,CAAC,CAAC;IAErB;IACA;IACA;IACAA,GAAG,CAAC6O,wBAAwB,GAAG,IAAInO,MAAM,CAACgY,mBAAmB,CAAC,CAAC;IAC/D1Y,GAAG,CAAC2Y,6BAA6B,GAAG,IAAIjY,MAAM,CAACgY,mBAAmB,CAAC,CAAC;;IAEpE;IACA1Y,GAAG,CAAC4Y,kBAAkB,GAAG5Y,GAAG,CAAC6O,wBAAwB;IAErD7O,GAAG,CAAC6Y,2BAA2B,GAAG,IAAInY,MAAM,CAACgY,mBAAmB,CAAC,CAAC;;IAElE;IACA;IACA,SAASI,0BAA0BA,CAAClH,OAAO,EAAE;MAC3C,IAAI,CAACA,OAAO,GAAGA,OAAO;IACxB;IAEA5R,GAAG,CAACmK,eAAe,GAAGzJ,MAAM,CAACqY,aAAa,CACxC,qBAAqB,EACrBD,0BACF,CAAC;IAED9Y,GAAG,CAACgZ,oBAAoB,GAAGtY,MAAM,CAACqY,aAAa,CAC7C,0BAA0B,EAC1B,MAAM,CAAC,CACT,CAAC;;IAED;IACA;IACA;IACA/Y,GAAG,CAACiZ,YAAY,GAAGjU,IAAI,IAAI;MACzB,MAAMkU,KAAK,GAAGlZ,GAAG,CAAC6O,wBAAwB,CAACrG,GAAG,CAAC,CAAC;MAChD,OAAOhI,SAAS,CAAC2Y,YAAY,CAAC3Q,GAAG,CAAC0Q,KAAK,EAAElU,IAAI,CAAC;IAChD,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAhF,GAAG,CAACoZ,OAAO,GAAG,CAAC7P,GAAG,EAAExH,OAAO,KAAK;MAC9B,MAAMsX,GAAG,GAAG,IAAIvQ,UAAU,CAACS,GAAG,EAAExH,OAAO,CAAC;MACxC0W,cAAc,CAACnQ,IAAI,CAAC+Q,GAAG,CAAC,CAAC,CAAC;MAC1B,OAAOA,GAAG;IACZ,CAAC;IAEDrZ,GAAG,CAACyW,cAAc,GAAG,IAAI6C,IAAI,CAAC;MAAEjN,eAAe,EAAE;IAAM,CAAC,CAAC;;IAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACArM,GAAG,CAACiK,WAAW,GAAGoF,QAAQ,IAAIrP,GAAG,CAACyW,cAAc,CAAC8C,QAAQ,CAAClK,QAAQ,CAAC;;IAEnE;IACA;IACA;IACArP,GAAG,CAACwZ,sBAAsB,GAAG,MAAMf,cAAc,CAACgB,KAAK,CACrDC,IAAI,IAAInY,MAAM,CAACgD,MAAM,CAACmV,IAAI,CAAC9U,cAAc,CAAC,CAAC6U,KAAK,CAAC3U,GAAG,IAAIA,GAAG,CAACkC,KAAK,CACnE,CAAC;IAAC9G,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/ddp-client.js", "sourcesContent": ["export { DDP } from '../common/namespace.js';\n", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\n\nexport class ConnectionStreamHandlers {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * Handles incoming raw messages from the DDP stream\n   * @param {String} raw_msg The raw message received from the stream\n   */\n  async onMessage(raw_msg) {\n    let msg;\n    try {\n      msg = DDPCommon.parseDDP(raw_msg);\n    } catch (e) {\n      Meteor._debug('Exception while parsing DDP', e);\n      return;\n    }\n\n    // Any message counts as receiving a pong, as it demonstrates that\n    // the server is still alive.\n    if (this._connection._heartbeat) {\n      this._connection._heartbeat.messageReceived();\n    }\n\n    if (msg === null || !msg.msg) {\n      if(!msg || !msg.testMessageOnConnect) {\n        if (Object.keys(msg).length === 1 && msg.server_id) return;\n        Meteor._debug('discarding invalid livedata message', msg);\n      }\n      return;\n    }\n\n    // Important: This was missing from previous version\n    // We need to set the current version before routing the message\n    if (msg.msg === 'connected') {\n      this._connection._version = this._connection._versionSuggestion;\n    }\n\n    await this._routeMessage(msg);\n  }\n\n  /**\n   * Routes messages to their appropriate handlers based on message type\n   * @private\n   * @param {Object} msg The parsed DDP message\n   */\n  async _routeMessage(msg) {\n    switch (msg.msg) {\n      case 'connected':\n        await this._connection._livedata_connected(msg);\n        this._connection.options.onConnected();\n        break;\n\n      case 'failed':\n        await this._handleFailedMessage(msg);\n        break;\n\n      case 'ping':\n        if (this._connection.options.respondToPings) {\n          this._connection._send({ msg: 'pong', id: msg.id });\n        }\n        break;\n\n      case 'pong':\n        // noop, as we assume everything's a pong\n        break;\n\n      case 'added':\n      case 'changed':\n      case 'removed':\n      case 'ready':\n      case 'updated':\n        await this._connection._livedata_data(msg);\n        break;\n\n      case 'nosub':\n        await this._connection._livedata_nosub(msg);\n        break;\n\n      case 'result':\n        await this._connection._livedata_result(msg);\n        break;\n\n      case 'error':\n        this._connection._livedata_error(msg);\n        break;\n\n      default:\n        Meteor._debug('discarding unknown livedata message type', msg);\n    }\n  }\n\n  /**\n   * Handles failed connection messages\n   * @private\n   * @param {Object} msg The failed message object\n   */\n  _handleFailedMessage(msg) {\n    if (this._connection._supportedDDPVersions.indexOf(msg.version) >= 0) {\n      this._connection._versionSuggestion = msg.version;\n      this._connection._stream.reconnect({ _force: true });\n    } else {\n      const description =\n        'DDP version negotiation failed; server requested version ' +\n        msg.version;\n      this._connection._stream.disconnect({ _permanent: true, _error: description });\n      this._connection.options.onDDPVersionNegotiationFailure(description);\n    }\n  }\n\n  /**\n   * Handles connection reset events\n   */\n  onReset() {\n    // Reset is called even on the first connection, so this is\n    // the only place we send this message.\n    const msg = this._buildConnectMessage();\n    this._connection._send(msg);\n\n    // Mark non-retry calls as failed and handle outstanding methods\n    this._handleOutstandingMethodsOnReset();\n\n    // Now, to minimize setup latency, go ahead and blast out all of\n    // our pending methods ands subscriptions before we've even taken\n    // the necessary RTT to know if we successfully reconnected.\n    this._connection._callOnReconnectAndSendAppropriateOutstandingMethods();\n    this._resendSubscriptions();\n  }\n\n  /**\n   * Builds the initial connect message\n   * @private\n   * @returns {Object} The connect message object\n   */\n  _buildConnectMessage() {\n    const msg = { msg: 'connect' };\n    if (this._connection._lastSessionId) {\n      msg.session = this._connection._lastSessionId;\n    }\n    msg.version = this._connection._versionSuggestion || this._connection._supportedDDPVersions[0];\n    this._connection._versionSuggestion = msg.version;\n    msg.support = this._connection._supportedDDPVersions;\n    return msg;\n  }\n\n  /**\n   * Handles outstanding methods during a reset\n   * @private\n   */\n  _handleOutstandingMethodsOnReset() {\n    const blocks = this._connection._outstandingMethodBlocks;\n    if (blocks.length === 0) return;\n\n    const currentMethodBlock = blocks[0].methods;\n    blocks[0].methods = currentMethodBlock.filter(\n      methodInvoker => {\n        // Methods with 'noRetry' option set are not allowed to re-send after\n        // recovering dropped connection.\n        if (methodInvoker.sentMessage && methodInvoker.noRetry) {\n          methodInvoker.receiveResult(\n            new Meteor.Error(\n              'invocation-failed',\n              'Method invocation might have failed due to dropped connection. ' +\n              'Failing because `noRetry` option was passed to Meteor.apply.'\n            )\n          );\n        }\n\n        // Only keep a method if it wasn't sent or it's allowed to retry.\n        return !(methodInvoker.sentMessage && methodInvoker.noRetry);\n      }\n    );\n\n    // Clear empty blocks\n    if (blocks.length > 0 && blocks[0].methods.length === 0) {\n      blocks.shift();\n    }\n\n    // Reset all method invokers as unsent\n    Object.values(this._connection._methodInvokers).forEach(invoker => {\n      invoker.sentMessage = false;\n    });\n  }\n\n  /**\n   * Resends all active subscriptions\n   * @private\n   */\n  _resendSubscriptions() {\n    Object.entries(this._connection._subscriptions).forEach(([id, sub]) => {\n      this._connection._sendQueued({\n        msg: 'sub',\n        id: id,\n        name: sub.name,\n        params: sub.params\n      });\n    });\n  }\n}", "import { MongoID } from 'meteor/mongo-id';\nimport { DiffSequence } from 'meteor/diff-sequence';\nimport { hasOwn } from \"meteor/ddp-common/utils\";\nimport { isEmpty } from \"meteor/ddp-common/utils\";\n\nexport class DocumentProcessors {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * @summary Process an 'added' message from the server\n   * @param {Object} msg The added message\n   * @param {Object} updates The updates accumulator\n   */\n  async _process_added(msg, updates) {\n    const self = this._connection;\n    const id = MongoID.idParse(msg.id);\n    const serverDoc = self._getServerDoc(msg.collection, id);\n\n    if (serverDoc) {\n      // Some outstanding stub wrote here.\n      const isExisting = serverDoc.document !== undefined;\n\n      serverDoc.document = msg.fields || Object.create(null);\n      serverDoc.document._id = id;\n\n      if (self._resetStores) {\n        // During reconnect the server is sending adds for existing ids.\n        // Always push an update so that document stays in the store after\n        // reset. Use current version of the document for this update, so\n        // that stub-written values are preserved.\n        const currentDoc = await self._stores[msg.collection].getDoc(msg.id);\n        if (currentDoc !== undefined) msg.fields = currentDoc;\n\n        self._pushUpdate(updates, msg.collection, msg);\n      } else if (isExisting) {\n        throw new Error('Server sent add for existing id: ' + msg.id);\n      }\n    } else {\n      self._pushUpdate(updates, msg.collection, msg);\n    }\n  }\n\n  /**\n   * @summary Process a 'changed' message from the server\n   * @param {Object} msg The changed message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_changed(msg, updates) {\n    const self = this._connection;\n    const serverDoc = self._getServerDoc(msg.collection, MongoID.idParse(msg.id));\n\n    if (serverDoc) {\n      if (serverDoc.document === undefined) {\n        throw new Error('Server sent changed for nonexisting id: ' + msg.id);\n      }\n      DiffSequence.applyChanges(serverDoc.document, msg.fields);\n    } else {\n      self._pushUpdate(updates, msg.collection, msg);\n    }\n  }\n\n  /**\n   * @summary Process a 'removed' message from the server\n   * @param {Object} msg The removed message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_removed(msg, updates) {\n    const self = this._connection;\n    const serverDoc = self._getServerDoc(msg.collection, MongoID.idParse(msg.id));\n\n    if (serverDoc) {\n      // Some outstanding stub wrote here.\n      if (serverDoc.document === undefined) {\n        throw new Error('Server sent removed for nonexisting id:' + msg.id);\n      }\n      serverDoc.document = undefined;\n    } else {\n      self._pushUpdate(updates, msg.collection, {\n        msg: 'removed',\n        collection: msg.collection,\n        id: msg.id\n      });\n    }\n  }\n\n  /**\n   * @summary Process a 'ready' message from the server\n   * @param {Object} msg The ready message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_ready(msg, updates) {\n    const self = this._connection;\n\n    // Process \"sub ready\" messages. \"sub ready\" messages don't take effect\n    // until all current server documents have been flushed to the local\n    // database. We can use a write fence to implement this.\n    msg.subs.forEach((subId) => {\n      self._runWhenAllServerDocsAreFlushed(() => {\n        const subRecord = self._subscriptions[subId];\n        // Did we already unsubscribe?\n        if (!subRecord) return;\n        // Did we already receive a ready message? (Oops!)\n        if (subRecord.ready) return;\n        subRecord.ready = true;\n        subRecord.readyCallback && subRecord.readyCallback();\n        subRecord.readyDeps.changed();\n      });\n    });\n  }\n\n  /**\n   * @summary Process an 'updated' message from the server\n   * @param {Object} msg The updated message\n   * @param {Object} updates The updates accumulator\n   */\n  _process_updated(msg, updates) {\n    const self = this._connection;\n    // Process \"method done\" messages.\n    msg.methods.forEach((methodId) => {\n      const docs = self._documentsWrittenByStub[methodId] || {};\n      Object.values(docs).forEach((written) => {\n        const serverDoc = self._getServerDoc(written.collection, written.id);\n        if (!serverDoc) {\n          throw new Error('Lost serverDoc for ' + JSON.stringify(written));\n        }\n        if (!serverDoc.writtenByStubs[methodId]) {\n          throw new Error(\n            'Doc ' +\n            JSON.stringify(written) +\n            ' not written by method ' +\n            methodId\n          );\n        }\n        delete serverDoc.writtenByStubs[methodId];\n        if (isEmpty(serverDoc.writtenByStubs)) {\n          // All methods whose stubs wrote this method have completed! We can\n          // now copy the saved document to the database (reverting the stub's\n          // change if the server did not write to this object, or applying the\n          // server's writes if it did).\n\n          // This is a fake ddp 'replace' message.  It's just for talking\n          // between livedata connections and minimongo.  (We have to stringify\n          // the ID because it's supposed to look like a wire message.)\n          self._pushUpdate(updates, written.collection, {\n            msg: 'replace',\n            id: MongoID.idStringify(written.id),\n            replace: serverDoc.document\n          });\n          // Call all flush callbacks.\n          serverDoc.flushCallbacks.forEach((c) => {\n            c();\n          });\n\n          // Delete this completed serverDocument. Don't bother to GC empty\n          // IdMaps inside self._serverDocuments, since there probably aren't\n          // many collections and they'll be written repeatedly.\n          self._serverDocuments[written.collection].remove(written.id);\n        }\n      });\n      delete self._documentsWrittenByStub[methodId];\n\n      // We want to call the data-written callback, but we can't do so until all\n      // currently buffered messages are flushed.\n      const callbackInvoker = self._methodInvokers[methodId];\n      if (!callbackInvoker) {\n        throw new Error('No callback invoker for method ' + methodId);\n      }\n\n      self._runWhenAllServerDocsAreFlushed(\n        (...args) => callbackInvoker.dataVisible(...args)\n      );\n    });\n  }\n\n  /**\n   * @summary Push an update to the buffer\n   * @private\n   * @param {Object} updates The updates accumulator\n   * @param {String} collection The collection name\n   * @param {Object} msg The update message\n   */\n  _pushUpdate(updates, collection, msg) {\n    if (!hasOwn.call(updates, collection)) {\n      updates[collection] = [];\n    }\n    updates[collection].push(msg);\n  }\n\n  /**\n   * @summary Get a server document by collection and id\n   * @private\n   * @param {String} collection The collection name\n   * @param {String} id The document id\n   * @returns {Object|null} The server document or null\n   */\n  _getServerDoc(collection, id) {\n    const self = this._connection;\n    if (!hasOwn.call(self._serverDocuments, collection)) {\n      return null;\n    }\n    const serverDocsForCollection = self._serverDocuments[collection];\n    return serverDocsForCollection.get(id) || null;\n  }\n}", "import { Meteor } from 'meteor/meteor';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'meteor/ddp-common';\nimport { Tracker } from 'meteor/tracker';\nimport { EJSON } from 'meteor/ejson';\nimport { Random } from 'meteor/random';\nimport { MongoID } from 'meteor/mongo-id';\nimport { DDP } from './namespace.js';\nimport { MethodInvoker } from './method_invoker';\nimport {\n  hasOwn,\n  slice,\n  keys,\n  isEmpty,\n  last,\n} from \"meteor/ddp-common/utils\";\nimport { ConnectionStreamHandlers } from './connection_stream_handlers';\nimport { MongoIDMap } from './mongo_id_map';\nimport { MessageProcessors } from './message_processors';\nimport { DocumentProcessors } from './document_processors';\n\n// @param url {String|Object} URL to Meteor app,\n//   or an object as a test hook (see code)\n// Options:\n//   reloadWithOutstanding: is it OK to reload if there are outstanding methods?\n//   headers: extra headers to send on the websockets connection, for\n//     server-to-server DDP only\n//   _sockjsOptions: Specifies options to pass through to the sockjs client\n//   onDDPNegotiationVersionFailure: callback when version negotiation fails.\n//\n// XXX There should be a way to destroy a DDP connection, causing all\n// outstanding method calls to fail.\n//\n// XXX Our current way of handling failure and reconnection is great\n// for an app (where we want to tolerate being disconnected as an\n// expect state, and keep trying forever to reconnect) but cumbersome\n// for something like a command line tool that wants to make a\n// connection, call a method, and print an error if connection\n// fails. We should have better usability in the latter case (while\n// still transparently reconnecting if it's just a transient failure\n// or the server migrating us).\nexport class Connection {\n  constructor(url, options) {\n    const self = this;\n\n    this.options = options = {\n      onConnected() {},\n      onDDPVersionNegotiationFailure(description) {\n        Meteor._debug(description);\n      },\n      heartbeatInterval: 17500,\n      heartbeatTimeout: 15000,\n      npmFayeOptions: Object.create(null),\n      // These options are only for testing.\n      reloadWithOutstanding: false,\n      supportedDDPVersions: DDPCommon.SUPPORTED_DDP_VERSIONS,\n      retry: true,\n      respondToPings: true,\n      // When updates are coming within this ms interval, batch them together.\n      bufferedWritesInterval: 5,\n      // Flush buffers immediately if writes are happening continuously for more than this many ms.\n      bufferedWritesMaxAge: 500,\n\n      ...options\n    };\n\n    // If set, called when we reconnect, queuing method calls _before_ the\n    // existing outstanding ones.\n    // NOTE: This feature has been preserved for backwards compatibility. The\n    // preferred method of setting a callback on reconnect is to use\n    // DDP.onReconnect.\n    self.onReconnect = null;\n\n    // as a test hook, allow passing a stream instead of a url.\n    if (typeof url === 'object') {\n      self._stream = url;\n    } else {\n      import { ClientStream } from \"meteor/socket-stream-client\";\n\n      self._stream = new ClientStream(url, {\n        retry: options.retry,\n        ConnectionError: DDP.ConnectionError,\n        headers: options.headers,\n        _sockjsOptions: options._sockjsOptions,\n        // Used to keep some tests quiet, or for other cases in which\n        // the right thing to do with connection errors is to silently\n        // fail (e.g. sending package usage stats). At some point we\n        // should have a real API for handling client-stream-level\n        // errors.\n        _dontPrintErrors: options._dontPrintErrors,\n        connectTimeoutMs: options.connectTimeoutMs,\n        npmFayeOptions: options.npmFayeOptions\n      });\n    }\n\n    self._lastSessionId = null;\n    self._versionSuggestion = null; // The last proposed DDP version.\n    self._version = null; // The DDP version agreed on by client and server.\n    self._stores = Object.create(null); // name -> object with methods\n    self._methodHandlers = Object.create(null); // name -> func\n    self._nextMethodId = 1;\n    self._supportedDDPVersions = options.supportedDDPVersions;\n\n    self._heartbeatInterval = options.heartbeatInterval;\n    self._heartbeatTimeout = options.heartbeatTimeout;\n\n    // Tracks methods which the user has tried to call but which have not yet\n    // called their user callback (ie, they are waiting on their result or for all\n    // of their writes to be written to the local cache). Map from method ID to\n    // MethodInvoker object.\n    self._methodInvokers = Object.create(null);\n\n    // Tracks methods which the user has called but whose result messages have not\n    // arrived yet.\n    //\n    // _outstandingMethodBlocks is an array of blocks of methods. Each block\n    // represents a set of methods that can run at the same time. The first block\n    // represents the methods which are currently in flight; subsequent blocks\n    // must wait for previous blocks to be fully finished before they can be sent\n    // to the server.\n    //\n    // Each block is an object with the following fields:\n    // - methods: a list of MethodInvoker objects\n    // - wait: a boolean; if true, this block had a single method invoked with\n    //         the \"wait\" option\n    //\n    // There will never be adjacent blocks with wait=false, because the only thing\n    // that makes methods need to be serialized is a wait method.\n    //\n    // Methods are removed from the first block when their \"result\" is\n    // received. The entire first block is only removed when all of the in-flight\n    // methods have received their results (so the \"methods\" list is empty) *AND*\n    // all of the data written by those methods are visible in the local cache. So\n    // it is possible for the first block's methods list to be empty, if we are\n    // still waiting for some objects to quiesce.\n    //\n    // Example:\n    //  _outstandingMethodBlocks = [\n    //    {wait: false, methods: []},\n    //    {wait: true, methods: [<MethodInvoker for 'login'>]},\n    //    {wait: false, methods: [<MethodInvoker for 'foo'>,\n    //                            <MethodInvoker for 'bar'>]}]\n    // This means that there were some methods which were sent to the server and\n    // which have returned their results, but some of the data written by\n    // the methods may not be visible in the local cache. Once all that data is\n    // visible, we will send a 'login' method. Once the login method has returned\n    // and all the data is visible (including re-running subs if userId changes),\n    // we will send the 'foo' and 'bar' methods in parallel.\n    self._outstandingMethodBlocks = [];\n\n    // method ID -> array of objects with keys 'collection' and 'id', listing\n    // documents written by a given method's stub. keys are associated with\n    // methods whose stub wrote at least one document, and whose data-done message\n    // has not yet been received.\n    self._documentsWrittenByStub = {};\n    // collection -> IdMap of \"server document\" object. A \"server document\" has:\n    // - \"document\": the version of the document according the\n    //   server (ie, the snapshot before a stub wrote it, amended by any changes\n    //   received from the server)\n    //   It is undefined if we think the document does not exist\n    // - \"writtenByStubs\": a set of method IDs whose stubs wrote to the document\n    //   whose \"data done\" messages have not yet been processed\n    self._serverDocuments = {};\n\n    // Array of callbacks to be called after the next update of the local\n    // cache. Used for:\n    //  - Calling methodInvoker.dataVisible and sub ready callbacks after\n    //    the relevant data is flushed.\n    //  - Invoking the callbacks of \"half-finished\" methods after reconnect\n    //    quiescence. Specifically, methods whose result was received over the old\n    //    connection (so we don't re-send it) but whose data had not been made\n    //    visible.\n    self._afterUpdateCallbacks = [];\n\n    // In two contexts, we buffer all incoming data messages and then process them\n    // all at once in a single update:\n    //   - During reconnect, we buffer all data messages until all subs that had\n    //     been ready before reconnect are ready again, and all methods that are\n    //     active have returned their \"data done message\"; then\n    //   - During the execution of a \"wait\" method, we buffer all data messages\n    //     until the wait method gets its \"data done\" message. (If the wait method\n    //     occurs during reconnect, it doesn't get any special handling.)\n    // all data messages are processed in one update.\n    //\n    // The following fields are used for this \"quiescence\" process.\n\n    // This buffers the messages that aren't being processed yet.\n    self._messagesBufferedUntilQuiescence = [];\n    // Map from method ID -> true. Methods are removed from this when their\n    // \"data done\" message is received, and we will not quiesce until it is\n    // empty.\n    self._methodsBlockingQuiescence = {};\n    // map from sub ID -> true for subs that were ready (ie, called the sub\n    // ready callback) before reconnect but haven't become ready again yet\n    self._subsBeingRevived = {}; // map from sub._id -> true\n    // if true, the next data update should reset all stores. (set during\n    // reconnect.)\n    self._resetStores = false;\n\n    // name -> array of updates for (yet to be created) collections\n    self._updatesForUnknownStores = {};\n    // if we're blocking a migration, the retry func\n    self._retryMigrate = null;\n    // Collection name -> array of messages.\n    self._bufferedWrites = {};\n    // When current buffer of updates must be flushed at, in ms timestamp.\n    self._bufferedWritesFlushAt = null;\n    // Timeout handle for the next processing of all pending writes\n    self._bufferedWritesFlushHandle = null;\n\n    self._bufferedWritesInterval = options.bufferedWritesInterval;\n    self._bufferedWritesMaxAge = options.bufferedWritesMaxAge;\n\n    // metadata for subscriptions.  Map from sub ID to object with keys:\n    //   - id\n    //   - name\n    //   - params\n    //   - inactive (if true, will be cleaned up if not reused in re-run)\n    //   - ready (has the 'ready' message been received?)\n    //   - readyCallback (an optional callback to call when ready)\n    //   - errorCallback (an optional callback to call if the sub terminates with\n    //                    an error, XXX COMPAT WITH *******)\n    //   - stopCallback (an optional callback to call when the sub terminates\n    //     for any reason, with an error argument if an error triggered the stop)\n    self._subscriptions = {};\n\n    // Reactive userId.\n    self._userId = null;\n    self._userIdDeps = new Tracker.Dependency();\n\n    // Block auto-reload while we're waiting for method responses.\n    if (Meteor.isClient &&\n      Package.reload &&\n      ! options.reloadWithOutstanding) {\n      Package.reload.Reload._onMigrate(retry => {\n        if (! self._readyToMigrate()) {\n          self._retryMigrate = retry;\n          return [false];\n        } else {\n          return [true];\n        }\n      });\n    }\n\n    this._streamHandlers = new ConnectionStreamHandlers(this);\n\n    const onDisconnect = () => {\n      if (this._heartbeat) {\n        this._heartbeat.stop();\n        this._heartbeat = null;\n      }\n    };\n\n    if (Meteor.isServer) {\n      this._stream.on(\n        'message',\n        Meteor.bindEnvironment(\n          msg => this._streamHandlers.onMessage(msg),\n          'handling DDP message'\n        )\n      );\n      this._stream.on(\n        'reset',\n        Meteor.bindEnvironment(\n          () => this._streamHandlers.onReset(),\n          'handling DDP reset'\n        )\n      );\n      this._stream.on(\n        'disconnect',\n        Meteor.bindEnvironment(onDisconnect, 'handling DDP disconnect')\n      );\n    } else {\n      this._stream.on('message', msg => this._streamHandlers.onMessage(msg));\n      this._stream.on('reset', () => this._streamHandlers.onReset());\n      this._stream.on('disconnect', onDisconnect);\n    }\n\n    this._messageProcessors = new MessageProcessors(this);\n\n    // Expose message processor methods to maintain backward compatibility\n    this._livedata_connected = (msg) => this._messageProcessors._livedata_connected(msg);\n    this._livedata_data = (msg) => this._messageProcessors._livedata_data(msg);\n    this._livedata_nosub = (msg) => this._messageProcessors._livedata_nosub(msg);\n    this._livedata_result = (msg) => this._messageProcessors._livedata_result(msg);\n    this._livedata_error = (msg) => this._messageProcessors._livedata_error(msg);\n\n    this._documentProcessors = new DocumentProcessors(this);\n\n    // Expose document processor methods to maintain backward compatibility\n    this._process_added = (msg, updates) => this._documentProcessors._process_added(msg, updates);\n    this._process_changed = (msg, updates) => this._documentProcessors._process_changed(msg, updates);\n    this._process_removed = (msg, updates) => this._documentProcessors._process_removed(msg, updates);\n    this._process_ready = (msg, updates) => this._documentProcessors._process_ready(msg, updates);\n    this._process_updated = (msg, updates) => this._documentProcessors._process_updated(msg, updates);\n\n    // Also expose utility methods used by other parts of the system\n    this._pushUpdate = (updates, collection, msg) =>\n      this._documentProcessors._pushUpdate(updates, collection, msg);\n    this._getServerDoc = (collection, id) =>\n      this._documentProcessors._getServerDoc(collection, id);\n  }\n\n  // 'name' is the name of the data on the wire that should go in the\n  // store. 'wrappedStore' should be an object with methods beginUpdate, update,\n  // endUpdate, saveOriginals, retrieveOriginals. see Collection for an example.\n  createStoreMethods(name, wrappedStore) {\n    const self = this;\n\n    if (name in self._stores) return false;\n\n    // Wrap the input object in an object which makes any store method not\n    // implemented by 'store' into a no-op.\n    const store = Object.create(null);\n    const keysOfStore = [\n      'update',\n      'beginUpdate',\n      'endUpdate',\n      'saveOriginals',\n      'retrieveOriginals',\n      'getDoc',\n      '_getCollection'\n    ];\n    keysOfStore.forEach((method) => {\n      store[method] = (...args) => {\n        if (wrappedStore[method]) {\n          return wrappedStore[method](...args);\n        }\n      };\n    });\n    self._stores[name] = store;\n    return store;\n  }\n\n  registerStoreClient(name, wrappedStore) {\n    const self = this;\n\n    const store = self.createStoreMethods(name, wrappedStore);\n\n    const queued = self._updatesForUnknownStores[name];\n    if (Array.isArray(queued)) {\n      store.beginUpdate(queued.length, false);\n      queued.forEach(msg => {\n        store.update(msg);\n      });\n      store.endUpdate();\n      delete self._updatesForUnknownStores[name];\n    }\n\n    return true;\n  }\n  async registerStoreServer(name, wrappedStore) {\n    const self = this;\n\n    const store = self.createStoreMethods(name, wrappedStore);\n\n    const queued = self._updatesForUnknownStores[name];\n    if (Array.isArray(queued)) {\n      await store.beginUpdate(queued.length, false);\n      for (const msg of queued) {\n        await store.update(msg);\n      }\n      await store.endUpdate();\n      delete self._updatesForUnknownStores[name];\n    }\n\n    return true;\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.subscribe\n   * @summary Subscribe to a record set.  Returns a handle that provides\n   * `stop()` and `ready()` methods.\n   * @locus Client\n   * @param {String} name Name of the subscription.  Matches the name of the\n   * server's `publish()` call.\n   * @param {EJSONable} [arg1,arg2...] Optional arguments passed to publisher\n   * function on server.\n   * @param {Function|Object} [callbacks] Optional. May include `onStop`\n   * and `onReady` callbacks. If there is an error, it is passed as an\n   * argument to `onStop`. If a function is passed instead of an object, it\n   * is interpreted as an `onReady` callback.\n   */\n  subscribe(name /* .. [arguments] .. (callback|callbacks) */) {\n    const self = this;\n\n    const params = slice.call(arguments, 1);\n    let callbacks = Object.create(null);\n    if (params.length) {\n      const lastParam = params[params.length - 1];\n      if (typeof lastParam === 'function') {\n        callbacks.onReady = params.pop();\n      } else if (lastParam && [\n        lastParam.onReady,\n        // XXX COMPAT WITH ******* onError used to exist, but now we use\n        // onStop with an error callback instead.\n        lastParam.onError,\n        lastParam.onStop\n      ].some(f => typeof f === \"function\")) {\n        callbacks = params.pop();\n      }\n    }\n\n    // Is there an existing sub with the same name and param, run in an\n    // invalidated Computation? This will happen if we are rerunning an\n    // existing computation.\n    //\n    // For example, consider a rerun of:\n    //\n    //     Tracker.autorun(function () {\n    //       Meteor.subscribe(\"foo\", Session.get(\"foo\"));\n    //       Meteor.subscribe(\"bar\", Session.get(\"bar\"));\n    //     });\n    //\n    // If \"foo\" has changed but \"bar\" has not, we will match the \"bar\"\n    // subcribe to an existing inactive subscription in order to not\n    // unsub and resub the subscription unnecessarily.\n    //\n    // We only look for one such sub; if there are N apparently-identical subs\n    // being invalidated, we will require N matching subscribe calls to keep\n    // them all active.\n    const existing = Object.values(self._subscriptions).find(\n      sub => (sub.inactive && sub.name === name && EJSON.equals(sub.params, params))\n    );\n\n    let id;\n    if (existing) {\n      id = existing.id;\n      existing.inactive = false; // reactivate\n\n      if (callbacks.onReady) {\n        // If the sub is not already ready, replace any ready callback with the\n        // one provided now. (It's not really clear what users would expect for\n        // an onReady callback inside an autorun; the semantics we provide is\n        // that at the time the sub first becomes ready, we call the last\n        // onReady callback provided, if any.)\n        // If the sub is already ready, run the ready callback right away.\n        // It seems that users would expect an onReady callback inside an\n        // autorun to trigger once the sub first becomes ready and also\n        // when re-subs happens.\n        if (existing.ready) {\n          callbacks.onReady();\n        } else {\n          existing.readyCallback = callbacks.onReady;\n        }\n      }\n\n      // XXX COMPAT WITH ******* we used to have onError but now we call\n      // onStop with an optional error argument\n      if (callbacks.onError) {\n        // Replace existing callback if any, so that errors aren't\n        // double-reported.\n        existing.errorCallback = callbacks.onError;\n      }\n\n      if (callbacks.onStop) {\n        existing.stopCallback = callbacks.onStop;\n      }\n    } else {\n      // New sub! Generate an id, save it locally, and send message.\n      id = Random.id();\n      self._subscriptions[id] = {\n        id: id,\n        name: name,\n        params: EJSON.clone(params),\n        inactive: false,\n        ready: false,\n        readyDeps: new Tracker.Dependency(),\n        readyCallback: callbacks.onReady,\n        // XXX COMPAT WITH ******* #errorCallback\n        errorCallback: callbacks.onError,\n        stopCallback: callbacks.onStop,\n        connection: self,\n        remove() {\n          delete this.connection._subscriptions[this.id];\n          this.ready && this.readyDeps.changed();\n        },\n        stop() {\n          this.connection._sendQueued({ msg: 'unsub', id: id });\n          this.remove();\n\n          if (callbacks.onStop) {\n            callbacks.onStop();\n          }\n        }\n      };\n      self._send({ msg: 'sub', id: id, name: name, params: params });\n    }\n\n    // return a handle to the application.\n    const handle = {\n      stop() {\n        if (! hasOwn.call(self._subscriptions, id)) {\n          return;\n        }\n        self._subscriptions[id].stop();\n      },\n      ready() {\n        // return false if we've unsubscribed.\n        if (!hasOwn.call(self._subscriptions, id)) {\n          return false;\n        }\n        const record = self._subscriptions[id];\n        record.readyDeps.depend();\n        return record.ready;\n      },\n      subscriptionId: id\n    };\n\n    if (Tracker.active) {\n      // We're in a reactive computation, so we'd like to unsubscribe when the\n      // computation is invalidated... but not if the rerun just re-subscribes\n      // to the same subscription!  When a rerun happens, we use onInvalidate\n      // as a change to mark the subscription \"inactive\" so that it can\n      // be reused from the rerun.  If it isn't reused, it's killed from\n      // an afterFlush.\n      Tracker.onInvalidate((c) => {\n        if (hasOwn.call(self._subscriptions, id)) {\n          self._subscriptions[id].inactive = true;\n        }\n\n        Tracker.afterFlush(() => {\n          if (hasOwn.call(self._subscriptions, id) &&\n              self._subscriptions[id].inactive) {\n            handle.stop();\n          }\n        });\n      });\n    }\n\n    return handle;\n  }\n\n  /**\n   * @summary Tells if the method call came from a call or a callAsync.\n   * @alias Meteor.isAsyncCall\n   * @locus Anywhere\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @returns boolean\n   */\n  isAsyncCall(){\n    return DDP._CurrentMethodInvocation._isCallAsyncMethodRunning()\n  }\n  methods(methods) {\n    Object.entries(methods).forEach(([name, func]) => {\n      if (typeof func !== 'function') {\n        throw new Error(\"Method '\" + name + \"' must be a function\");\n      }\n      if (this._methodHandlers[name]) {\n        throw new Error(\"A method named '\" + name + \"' is already defined\");\n      }\n      this._methodHandlers[name] = func;\n    });\n  }\n\n  _getIsSimulation({isFromCallAsync, alreadyInSimulation}) {\n    if (!isFromCallAsync) {\n      return alreadyInSimulation;\n    }\n    return alreadyInSimulation && DDP._CurrentMethodInvocation._isCallAsyncMethodRunning();\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.call\n   * @summary Invokes a method with a sync stub, passing any number of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable} [arg1,arg2...] Optional method arguments\n   * @param {Function} [asyncCallback] Optional callback, which is called asynchronously with the error or result after the method is complete. If not provided, the method runs synchronously if possible (see below).\n   */\n  call(name /* .. [arguments] .. callback */) {\n    // if it's a function, the last argument is the result callback,\n    // not a parameter to the remote method.\n    const args = slice.call(arguments, 1);\n    let callback;\n    if (args.length && typeof args[args.length - 1] === 'function') {\n      callback = args.pop();\n    }\n    return this.apply(name, args, callback);\n  }\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.callAsync\n   * @summary Invokes a method with an async stub, passing any number of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable} [arg1,arg2...] Optional method arguments\n   * @returns {Promise}\n   */\n  callAsync(name /* .. [arguments] .. */) {\n    const args = slice.call(arguments, 1);\n    if (args.length && typeof args[args.length - 1] === 'function') {\n      throw new Error(\n        \"Meteor.callAsync() does not accept a callback. You should 'await' the result, or use .then().\"\n      );\n    }\n\n    return this.applyAsync(name, args, { returnServerResultPromise: true });\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.apply\n   * @summary Invoke a method passing an array of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable[]} args Method arguments\n   * @param {Object} [options]\n   * @param {Boolean} options.wait (Client only) If true, don't send this method until all previous method calls have completed, and don't send any subsequent method calls until this one is completed.\n   * @param {Function} options.onResultReceived (Client only) This callback is invoked with the error or result of the method (just like `asyncCallback`) as soon as the error or result is available. The local cache may not yet reflect the writes performed by the method.\n   * @param {Boolean} options.noRetry (Client only) if true, don't send this method again on reload, simply call the callback an error with the error code 'invocation-failed'.\n   * @param {Boolean} options.throwStubExceptions (Client only) If true, exceptions thrown by method stubs will be thrown instead of logged, and the method will not be invoked on the server.\n   * @param {Boolean} options.returnStubValue (Client only) If true then in cases where we would have otherwise discarded the stub's return value and returned undefined, instead we go ahead and return it. Specifically, this is any time other than when (a) we are already inside a stub or (b) we are in Node and no callback was provided. Currently we require this flag to be explicitly passed to reduce the likelihood that stub return values will be confused with server return values; we may improve this in future.\n   * @param {Function} [asyncCallback] Optional callback; same semantics as in [`Meteor.call`](#meteor_call).\n   */\n  apply(name, args, options, callback) {\n    const { stubInvocation, invocation, ...stubOptions } = this._stubCall(name, EJSON.clone(args));\n\n    if (stubOptions.hasStub) {\n      if (\n        !this._getIsSimulation({\n          alreadyInSimulation: stubOptions.alreadyInSimulation,\n          isFromCallAsync: stubOptions.isFromCallAsync,\n        })\n      ) {\n        this._saveOriginals();\n      }\n      try {\n        stubOptions.stubReturnValue = DDP._CurrentMethodInvocation\n          .withValue(invocation, stubInvocation);\n        if (Meteor._isPromise(stubOptions.stubReturnValue)) {\n          Meteor._debug(\n            `Method ${name}: Calling a method that has an async method stub with call/apply can lead to unexpected behaviors. Use callAsync/applyAsync instead.`\n          );\n        }\n      } catch (e) {\n        stubOptions.exception = e;\n      }\n    }\n    return this._apply(name, stubOptions, args, options, callback);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.applyAsync\n   * @summary Invoke a method passing an array of arguments.\n   * @locus Anywhere\n   * @param {String} name Name of method to invoke\n   * @param {EJSONable[]} args Method arguments\n   * @param {Object} [options]\n   * @param {Boolean} options.wait (Client only) If true, don't send this method until all previous method calls have completed, and don't send any subsequent method calls until this one is completed.\n   * @param {Function} options.onResultReceived (Client only) This callback is invoked with the error or result of the method (just like `asyncCallback`) as soon as the error or result is available. The local cache may not yet reflect the writes performed by the method.\n   * @param {Boolean} options.noRetry (Client only) if true, don't send this method again on reload, simply call the callback an error with the error code 'invocation-failed'.\n   * @param {Boolean} options.throwStubExceptions (Client only) If true, exceptions thrown by method stubs will be thrown instead of logged, and the method will not be invoked on the server.\n   * @param {Boolean} options.returnStubValue (Client only) If true then in cases where we would have otherwise discarded the stub's return value and returned undefined, instead we go ahead and return it. Specifically, this is any time other than when (a) we are already inside a stub or (b) we are in Node and no callback was provided. Currently we require this flag to be explicitly passed to reduce the likelihood that stub return values will be confused with server return values; we may improve this in future.\n   * @param {Boolean} options.returnServerResultPromise (Client only) If true, the promise returned by applyAsync will resolve to the server's return value, rather than the stub's return value. This is useful when you want to ensure that the server's return value is used, even if the stub returns a promise. The same behavior as `callAsync`.\n   */\n  applyAsync(name, args, options, callback = null) {\n    const stubPromise = this._applyAsyncStubInvocation(name, args, options);\n\n    const promise = this._applyAsync({\n      name,\n      args,\n      options,\n      callback,\n      stubPromise,\n    });\n    if (Meteor.isClient) {\n      // only return the stubReturnValue\n      promise.stubPromise = stubPromise.then(o => {\n        if (o.exception) {\n          throw o.exception;\n        }\n        return o.stubReturnValue;\n      });\n      // this avoids attribute recursion\n      promise.serverPromise = new Promise((resolve, reject) =>\n        promise.then(resolve).catch(reject),\n      );\n    }\n    return promise;\n  }\n  async _applyAsyncStubInvocation(name, args, options) {\n    const { stubInvocation, invocation, ...stubOptions } = this._stubCall(name, EJSON.clone(args), options);\n    if (stubOptions.hasStub) {\n      if (\n        !this._getIsSimulation({\n          alreadyInSimulation: stubOptions.alreadyInSimulation,\n          isFromCallAsync: stubOptions.isFromCallAsync,\n        })\n      ) {\n        this._saveOriginals();\n      }\n      try {\n        /*\n         * The code below follows the same logic as the function withValues().\n         *\n         * But as the Meteor package is not compiled by ecmascript, it is unable to use newer syntax in the browser,\n         * such as, the async/await.\n         *\n         * So, to keep supporting old browsers, like IE 11, we're creating the logic one level above.\n         */\n        const currentContext = DDP._CurrentMethodInvocation._setNewContextAndGetCurrent(\n          invocation\n        );\n        try {\n          stubOptions.stubReturnValue = await stubInvocation();\n        } catch (e) {\n          stubOptions.exception = e;\n        } finally {\n          DDP._CurrentMethodInvocation._set(currentContext);\n        }\n      } catch (e) {\n        stubOptions.exception = e;\n      }\n    }\n    return stubOptions;\n  }\n  async _applyAsync({ name, args, options, callback, stubPromise }) {\n    const stubOptions = await stubPromise;\n    return this._apply(name, stubOptions, args, options, callback);\n  }\n\n  _apply(name, stubCallValue, args, options, callback) {\n    const self = this;\n\n    // We were passed 3 arguments. They may be either (name, args, options)\n    // or (name, args, callback)\n    if (!callback && typeof options === 'function') {\n      callback = options;\n      options = Object.create(null);\n    }\n    options = options || Object.create(null);\n\n    if (callback) {\n      // XXX would it be better form to do the binding in stream.on,\n      // or caller, instead of here?\n      // XXX improve error message (and how we report it)\n      callback = Meteor.bindEnvironment(\n        callback,\n        \"delivering result of invoking '\" + name + \"'\"\n      );\n    }\n    const {\n      hasStub,\n      exception,\n      stubReturnValue,\n      alreadyInSimulation,\n      randomSeed,\n    } = stubCallValue;\n\n    // Keep our args safe from mutation (eg if we don't send the message for a\n    // while because of a wait method).\n    args = EJSON.clone(args);\n    // If we're in a simulation, stop and return the result we have,\n    // rather than going on to do an RPC. If there was no stub,\n    // we'll end up returning undefined.\n    if (\n      this._getIsSimulation({\n        alreadyInSimulation,\n        isFromCallAsync: stubCallValue.isFromCallAsync,\n      })\n    ) {\n      let result;\n\n      if (callback) {\n        callback(exception, stubReturnValue);\n      } else {\n        if (exception) throw exception;\n        result = stubReturnValue;\n      }\n\n      return options._returnMethodInvoker ? { result } : result;\n    }\n\n    // We only create the methodId here because we don't actually need one if\n    // we're already in a simulation\n    const methodId = '' + self._nextMethodId++;\n    if (hasStub) {\n      self._retrieveAndStoreOriginals(methodId);\n    }\n\n    // Generate the DDP message for the method call. Note that on the client,\n    // it is important that the stub have finished before we send the RPC, so\n    // that we know we have a complete list of which local documents the stub\n    // wrote.\n    const message = {\n      msg: 'method',\n      id: methodId,\n      method: name,\n      params: args\n    };\n\n    // If an exception occurred in a stub, and we're ignoring it\n    // because we're doing an RPC and want to use what the server\n    // returns instead, log it so the developer knows\n    // (unless they explicitly ask to see the error).\n    //\n    // Tests can set the '_expectedByTest' flag on an exception so it won't\n    // go to log.\n    if (exception) {\n      if (options.throwStubExceptions) {\n        throw exception;\n      } else if (!exception._expectedByTest) {\n        Meteor._debug(\n          \"Exception while simulating the effect of invoking '\" + name + \"'\",\n          exception\n        );\n      }\n    }\n\n    // At this point we're definitely doing an RPC, and we're going to\n    // return the value of the RPC to the caller.\n\n    // If the caller didn't give a callback, decide what to do.\n    let promise;\n    if (!callback) {\n      if (\n        Meteor.isClient &&\n        !options.returnServerResultPromise &&\n        (!options.isFromCallAsync || options.returnStubValue)\n      ) {\n        callback = (err) => {\n          err && Meteor._debug(\"Error invoking Method '\" + name + \"'\", err);\n        };\n      } else {\n        promise = new Promise((resolve, reject) => {\n          callback = (...allArgs) => {\n            let args = Array.from(allArgs);\n            let err = args.shift();\n            if (err) {\n              reject(err);\n              return;\n            }\n            resolve(...args);\n          };\n        });\n      }\n    }\n\n    // Send the randomSeed only if we used it\n    if (randomSeed.value !== null) {\n      message.randomSeed = randomSeed.value;\n    }\n\n    const methodInvoker = new MethodInvoker({\n      methodId,\n      callback: callback,\n      connection: self,\n      onResultReceived: options.onResultReceived,\n      wait: !!options.wait,\n      message: message,\n      noRetry: !!options.noRetry\n    });\n\n    let result;\n\n    if (promise) {\n      result = options.returnStubValue ? promise.then(() => stubReturnValue) : promise;\n    } else {\n      result = options.returnStubValue ? stubReturnValue : undefined;\n    }\n\n    if (options._returnMethodInvoker) {\n      return {\n        methodInvoker,\n        result,\n      };\n    }\n\n    self._addOutstandingMethod(methodInvoker, options);\n    return result;\n  }\n\n  _stubCall(name, args, options) {\n    // Run the stub, if we have one. The stub is supposed to make some\n    // temporary writes to the database to give the user a smooth experience\n    // until the actual result of executing the method comes back from the\n    // server (whereupon the temporary writes to the database will be reversed\n    // during the beginUpdate/endUpdate process.)\n    //\n    // Normally, we ignore the return value of the stub (even if it is an\n    // exception), in favor of the real return value from the server. The\n    // exception is if the *caller* is a stub. In that case, we're not going\n    // to do a RPC, so we use the return value of the stub as our return\n    // value.\n    const self = this;\n    const enclosing = DDP._CurrentMethodInvocation.get();\n    const stub = self._methodHandlers[name];\n    const alreadyInSimulation = enclosing?.isSimulation;\n    const isFromCallAsync = enclosing?._isFromCallAsync;\n    const randomSeed = { value: null};\n\n    const defaultReturn = {\n      alreadyInSimulation,\n      randomSeed,\n      isFromCallAsync,\n    };\n    if (!stub) {\n      return { ...defaultReturn, hasStub: false };\n    }\n\n    // Lazily generate a randomSeed, only if it is requested by the stub.\n    // The random streams only have utility if they're used on both the client\n    // and the server; if the client doesn't generate any 'random' values\n    // then we don't expect the server to generate any either.\n    // Less commonly, the server may perform different actions from the client,\n    // and may in fact generate values where the client did not, but we don't\n    // have any client-side values to match, so even here we may as well just\n    // use a random seed on the server.  In that case, we don't pass the\n    // randomSeed to save bandwidth, and we don't even generate it to save a\n    // bit of CPU and to avoid consuming entropy.\n\n    const randomSeedGenerator = () => {\n      if (randomSeed.value === null) {\n        randomSeed.value = DDPCommon.makeRpcSeed(enclosing, name);\n      }\n      return randomSeed.value;\n    };\n\n    const setUserId = userId => {\n      self.setUserId(userId);\n    };\n\n    const invocation = new DDPCommon.MethodInvocation({\n      name,\n      isSimulation: true,\n      userId: self.userId(),\n      isFromCallAsync: options?.isFromCallAsync,\n      setUserId: setUserId,\n      randomSeed() {\n        return randomSeedGenerator();\n      }\n    });\n\n    // Note that unlike in the corresponding server code, we never audit\n    // that stubs check() their arguments.\n    const stubInvocation = () => {\n        if (Meteor.isServer) {\n          // Because saveOriginals and retrieveOriginals aren't reentrant,\n          // don't allow stubs to yield.\n          return Meteor._noYieldsAllowed(() => {\n            // re-clone, so that the stub can't affect our caller's values\n            return stub.apply(invocation, EJSON.clone(args));\n          });\n        } else {\n          return stub.apply(invocation, EJSON.clone(args));\n        }\n    };\n    return { ...defaultReturn, hasStub: true, stubInvocation, invocation };\n  }\n\n  // Before calling a method stub, prepare all stores to track changes and allow\n  // _retrieveAndStoreOriginals to get the original versions of changed\n  // documents.\n  _saveOriginals() {\n    if (! this._waitingForQuiescence()) {\n      this._flushBufferedWrites();\n    }\n\n    Object.values(this._stores).forEach((store) => {\n      store.saveOriginals();\n    });\n  }\n\n  // Retrieves the original versions of all documents modified by the stub for\n  // method 'methodId' from all stores and saves them to _serverDocuments (keyed\n  // by document) and _documentsWrittenByStub (keyed by method ID).\n  _retrieveAndStoreOriginals(methodId) {\n    const self = this;\n    if (self._documentsWrittenByStub[methodId])\n      throw new Error('Duplicate methodId in _retrieveAndStoreOriginals');\n\n    const docsWritten = [];\n\n    Object.entries(self._stores).forEach(([collection, store]) => {\n      const originals = store.retrieveOriginals();\n      // not all stores define retrieveOriginals\n      if (! originals) return;\n      originals.forEach((doc, id) => {\n        docsWritten.push({ collection, id });\n        if (! hasOwn.call(self._serverDocuments, collection)) {\n          self._serverDocuments[collection] = new MongoIDMap();\n        }\n        const serverDoc = self._serverDocuments[collection].setDefault(\n          id,\n          Object.create(null)\n        );\n        if (serverDoc.writtenByStubs) {\n          // We're not the first stub to write this doc. Just add our method ID\n          // to the record.\n          serverDoc.writtenByStubs[methodId] = true;\n        } else {\n          // First stub! Save the original value and our method ID.\n          serverDoc.document = doc;\n          serverDoc.flushCallbacks = [];\n          serverDoc.writtenByStubs = Object.create(null);\n          serverDoc.writtenByStubs[methodId] = true;\n        }\n      });\n    });\n    if (! isEmpty(docsWritten)) {\n      self._documentsWrittenByStub[methodId] = docsWritten;\n    }\n  }\n\n  // This is very much a private function we use to make the tests\n  // take up fewer server resources after they complete.\n  _unsubscribeAll() {\n    Object.values(this._subscriptions).forEach((sub) => {\n      // Avoid killing the autoupdate subscription so that developers\n      // still get hot code pushes when writing tests.\n      //\n      // XXX it's a hack to encode knowledge about autoupdate here,\n      // but it doesn't seem worth it yet to have a special API for\n      // subscriptions to preserve after unit tests.\n      if (sub.name !== 'meteor_autoupdate_clientVersions') {\n        sub.stop();\n      }\n    });\n  }\n\n  // Sends the DDP stringification of the given message object\n  _send(obj) {\n    this._stream.send(DDPCommon.stringifyDDP(obj));\n  }\n\n  // Always queues the call before sending the message\n  // Used, for example, on subscription.[id].stop() to make sure a \"sub\" message is always called before an \"unsub\" message\n  // https://github.com/meteor/meteor/issues/13212\n  //\n  // This is part of the actual fix for the rest check:\n  // https://github.com/meteor/meteor/pull/13236\n  _sendQueued(obj) {\n    this._send(obj, true);\n  }\n\n  // We detected via DDP-level heartbeats that we've lost the\n  // connection.  Unlike `disconnect` or `close`, a lost connection\n  // will be automatically retried.\n  _lostConnection(error) {\n    this._stream._lostConnection(error);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.status\n   * @summary Get the current connection status. A reactive data source.\n   * @locus Client\n   */\n  status(...args) {\n    return this._stream.status(...args);\n  }\n\n  /**\n   * @summary Force an immediate reconnection attempt if the client is not connected to the server.\n\n  This method does nothing if the client is already connected.\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.reconnect\n   * @locus Client\n   */\n  reconnect(...args) {\n    return this._stream.reconnect(...args);\n  }\n\n  /**\n   * @memberOf Meteor\n   * @importFromPackage meteor\n   * @alias Meteor.disconnect\n   * @summary Disconnect the client from the server.\n   * @locus Client\n   */\n  disconnect(...args) {\n    return this._stream.disconnect(...args);\n  }\n\n  close() {\n    return this._stream.disconnect({ _permanent: true });\n  }\n\n  ///\n  /// Reactive user system\n  ///\n  userId() {\n    if (this._userIdDeps) this._userIdDeps.depend();\n    return this._userId;\n  }\n\n  setUserId(userId) {\n    // Avoid invalidating dependents if setUserId is called with current value.\n    if (this._userId === userId) return;\n    this._userId = userId;\n    if (this._userIdDeps) this._userIdDeps.changed();\n  }\n\n  // Returns true if we are in a state after reconnect of waiting for subs to be\n  // revived or early methods to finish their data, or we are waiting for a\n  // \"wait\" method to finish.\n  _waitingForQuiescence() {\n    return (\n      ! isEmpty(this._subsBeingRevived) ||\n      ! isEmpty(this._methodsBlockingQuiescence)\n    );\n  }\n\n  // Returns true if any method whose message has been sent to the server has\n  // not yet invoked its user callback.\n  _anyMethodsAreOutstanding() {\n    const invokers = this._methodInvokers;\n    return Object.values(invokers).some((invoker) => !!invoker.sentMessage);\n  }\n\n  async _processOneDataMessage(msg, updates) {\n    const messageType = msg.msg;\n\n    // msg is one of ['added', 'changed', 'removed', 'ready', 'updated']\n    if (messageType === 'added') {\n      await this._process_added(msg, updates);\n    } else if (messageType === 'changed') {\n      this._process_changed(msg, updates);\n    } else if (messageType === 'removed') {\n      this._process_removed(msg, updates);\n    } else if (messageType === 'ready') {\n      this._process_ready(msg, updates);\n    } else if (messageType === 'updated') {\n      this._process_updated(msg, updates);\n    } else if (messageType === 'nosub') {\n      // ignore this\n    } else {\n      Meteor._debug('discarding unknown livedata data message type', msg);\n    }\n  }\n\n  _prepareBuffersToFlush() {\n    const self = this;\n    if (self._bufferedWritesFlushHandle) {\n      clearTimeout(self._bufferedWritesFlushHandle);\n      self._bufferedWritesFlushHandle = null;\n    }\n\n    self._bufferedWritesFlushAt = null;\n    // We need to clear the buffer before passing it to\n    //  performWrites. As there's no guarantee that it\n    //  will exit cleanly.\n    const writes = self._bufferedWrites;\n    self._bufferedWrites = Object.create(null);\n    return writes;\n  }\n\n  /**\n   * Server-side store updates handled asynchronously\n   * @private\n   */\n  async _performWritesServer(updates) {\n    const self = this;\n\n    if (self._resetStores || !isEmpty(updates)) {\n      // Start all store updates - keeping original loop structure\n      for (const store of Object.values(self._stores)) {\n        await store.beginUpdate(\n          updates[store._name]?.length || 0,\n          self._resetStores\n        );\n      }\n\n      self._resetStores = false;\n\n      // Process each store's updates sequentially as before\n      for (const [storeName, messages] of Object.entries(updates)) {\n        const store = self._stores[storeName];\n        if (store) {\n          // Batch each store's messages in modest chunks to prevent event loop blocking\n          // while maintaining operation order\n          const CHUNK_SIZE = 100;\n          for (let i = 0; i < messages.length; i += CHUNK_SIZE) {\n            const chunk = messages.slice(i, Math.min(i + CHUNK_SIZE, messages.length));\n\n            for (const msg of chunk) {\n              await store.update(msg);\n            }\n\n            await new Promise(resolve => process.nextTick(resolve));\n          }\n        } else {\n          // Queue updates for uninitialized stores\n          self._updatesForUnknownStores[storeName] =\n            self._updatesForUnknownStores[storeName] || [];\n          self._updatesForUnknownStores[storeName].push(...messages);\n        }\n      }\n\n      // Complete all updates\n      for (const store of Object.values(self._stores)) {\n        await store.endUpdate();\n      }\n    }\n\n    self._runAfterUpdateCallbacks();\n  }\n\n  /**\n   * Client-side store updates handled synchronously for optimistic UI\n   * @private\n   */\n  _performWritesClient(updates) {\n    const self = this;\n\n    if (self._resetStores || !isEmpty(updates)) {\n      // Synchronous store updates for client\n      Object.values(self._stores).forEach(store => {\n        store.beginUpdate(\n          updates[store._name]?.length || 0,\n          self._resetStores\n        );\n      });\n\n      self._resetStores = false;\n\n      Object.entries(updates).forEach(([storeName, messages]) => {\n        const store = self._stores[storeName];\n        if (store) {\n          messages.forEach(msg => store.update(msg));\n        } else {\n          self._updatesForUnknownStores[storeName] =\n            self._updatesForUnknownStores[storeName] || [];\n          self._updatesForUnknownStores[storeName].push(...messages);\n        }\n      });\n\n      Object.values(self._stores).forEach(store => store.endUpdate());\n    }\n\n    self._runAfterUpdateCallbacks();\n  }\n\n  /**\n   * Executes buffered writes either synchronously (client) or async (server)\n   * @private\n   */\n  async _flushBufferedWrites() {\n    const self = this;\n    const writes = self._prepareBuffersToFlush();\n\n    return Meteor.isClient\n      ? self._performWritesClient(writes)\n      : self._performWritesServer(writes);\n  }\n\n  // Call any callbacks deferred with _runWhenAllServerDocsAreFlushed whose\n  // relevant docs have been flushed, as well as dataVisible callbacks at\n  // reconnect-quiescence time.\n  _runAfterUpdateCallbacks() {\n    const self = this;\n    const callbacks = self._afterUpdateCallbacks;\n    self._afterUpdateCallbacks = [];\n    callbacks.forEach((c) => {\n      c();\n    });\n  }\n\n  // Ensures that \"f\" will be called after all documents currently in\n  // _serverDocuments have been written to the local cache. f will not be called\n  // if the connection is lost before then!\n  _runWhenAllServerDocsAreFlushed(f) {\n    const self = this;\n    const runFAfterUpdates = () => {\n      self._afterUpdateCallbacks.push(f);\n    };\n    let unflushedServerDocCount = 0;\n    const onServerDocFlush = () => {\n      --unflushedServerDocCount;\n      if (unflushedServerDocCount === 0) {\n        // This was the last doc to flush! Arrange to run f after the updates\n        // have been applied.\n        runFAfterUpdates();\n      }\n    };\n\n    Object.values(self._serverDocuments).forEach((serverDocuments) => {\n      serverDocuments.forEach((serverDoc) => {\n        const writtenByStubForAMethodWithSentMessage =\n          keys(serverDoc.writtenByStubs).some(methodId => {\n            const invoker = self._methodInvokers[methodId];\n            return invoker && invoker.sentMessage;\n          });\n\n        if (writtenByStubForAMethodWithSentMessage) {\n          ++unflushedServerDocCount;\n          serverDoc.flushCallbacks.push(onServerDocFlush);\n        }\n      });\n    });\n    if (unflushedServerDocCount === 0) {\n      // There aren't any buffered docs --- we can call f as soon as the current\n      // round of updates is applied!\n      runFAfterUpdates();\n    }\n  }\n\n  _addOutstandingMethod(methodInvoker, options) {\n    if (options?.wait) {\n      // It's a wait method! Wait methods go in their own block.\n      this._outstandingMethodBlocks.push({\n        wait: true,\n        methods: [methodInvoker]\n      });\n    } else {\n      // Not a wait method. Start a new block if the previous block was a wait\n      // block, and add it to the last block of methods.\n      if (isEmpty(this._outstandingMethodBlocks) ||\n          last(this._outstandingMethodBlocks).wait) {\n        this._outstandingMethodBlocks.push({\n          wait: false,\n          methods: [],\n        });\n      }\n\n      last(this._outstandingMethodBlocks).methods.push(methodInvoker);\n    }\n\n    // If we added it to the first block, send it out now.\n    if (this._outstandingMethodBlocks.length === 1) {\n      methodInvoker.sendMessage();\n    }\n  }\n\n  // Called by MethodInvoker after a method's callback is invoked.  If this was\n  // the last outstanding method in the current block, runs the next block. If\n  // there are no more methods, consider accepting a hot code push.\n  _outstandingMethodFinished() {\n    const self = this;\n    if (self._anyMethodsAreOutstanding()) return;\n\n    // No methods are outstanding. This should mean that the first block of\n    // methods is empty. (Or it might not exist, if this was a method that\n    // half-finished before disconnect/reconnect.)\n    if (! isEmpty(self._outstandingMethodBlocks)) {\n      const firstBlock = self._outstandingMethodBlocks.shift();\n      if (! isEmpty(firstBlock.methods))\n        throw new Error(\n          'No methods outstanding but nonempty block: ' +\n            JSON.stringify(firstBlock)\n        );\n\n      // Send the outstanding methods now in the first block.\n      if (! isEmpty(self._outstandingMethodBlocks))\n        self._sendOutstandingMethods();\n    }\n\n    // Maybe accept a hot code push.\n    self._maybeMigrate();\n  }\n\n  // Sends messages for all the methods in the first block in\n  // _outstandingMethodBlocks.\n  _sendOutstandingMethods() {\n    const self = this;\n\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      return;\n    }\n\n    self._outstandingMethodBlocks[0].methods.forEach(m => {\n      m.sendMessage();\n    });\n  }\n\n  _sendOutstandingMethodBlocksMessages(oldOutstandingMethodBlocks) {\n    const self = this;\n    if (isEmpty(oldOutstandingMethodBlocks)) return;\n\n    // We have at least one block worth of old outstanding methods to try\n    // again. First: did onReconnect actually send anything? If not, we just\n    // restore all outstanding methods and run the first block.\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      self._outstandingMethodBlocks = oldOutstandingMethodBlocks;\n      self._sendOutstandingMethods();\n      return;\n    }\n\n    // OK, there are blocks on both sides. Special case: merge the last block of\n    // the reconnect methods with the first block of the original methods, if\n    // neither of them are \"wait\" blocks.\n    if (\n      !last(self._outstandingMethodBlocks).wait &&\n      !oldOutstandingMethodBlocks[0].wait\n    ) {\n      oldOutstandingMethodBlocks[0].methods.forEach((m) => {\n        last(self._outstandingMethodBlocks).methods.push(m);\n\n        // If this \"last block\" is also the first block, send the message.\n        if (self._outstandingMethodBlocks.length === 1) {\n          m.sendMessage();\n        }\n      });\n\n      oldOutstandingMethodBlocks.shift();\n    }\n\n    // Now add the rest of the original blocks on.\n    self._outstandingMethodBlocks.push(...oldOutstandingMethodBlocks);\n  }\n\n  _callOnReconnectAndSendAppropriateOutstandingMethods() {\n    const self = this;\n    const oldOutstandingMethodBlocks = self._outstandingMethodBlocks;\n    self._outstandingMethodBlocks = [];\n\n    self.onReconnect && self.onReconnect();\n    DDP._reconnectHook.each((callback) => {\n      callback(self);\n      return true;\n    });\n\n    self._sendOutstandingMethodBlocksMessages(oldOutstandingMethodBlocks);\n  }\n\n  // We can accept a hot code push if there are no methods in flight.\n  _readyToMigrate() {\n    return isEmpty(this._methodInvokers);\n  }\n\n  // If we were blocking a migration, see if it's now possible to continue.\n  // Call whenever the set of outstanding/blocked methods shrinks.\n  _maybeMigrate() {\n    const self = this;\n    if (self._retryMigrate && self._readyToMigrate()) {\n      self._retryMigrate();\n      self._retryMigrate = null;\n    }\n  }\n}\n", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\nimport { DDP } from './namespace.js';\nimport { EJSON } from 'meteor/ejson';\nimport { isEmpty, hasOwn } from \"meteor/ddp-common/utils\";\n\nexport class MessageProcessors {\n  constructor(connection) {\n    this._connection = connection;\n  }\n\n  /**\n   * @summary Process the connection message and set up the session\n   * @param {Object} msg The connection message\n   */\n  async _livedata_connected(msg) {\n    const self = this._connection;\n\n    if (self._version !== 'pre1' && self._heartbeatInterval !== 0) {\n      self._heartbeat = new DDPCommon.Heartbeat({\n        heartbeatInterval: self._heartbeatInterval,\n        heartbeatTimeout: self._heartbeatTimeout,\n        onTimeout() {\n          self._lostConnection(\n            new DDP.ConnectionError('DDP heartbeat timed out')\n          );\n        },\n        sendPing() {\n          self._send({ msg: 'ping' });\n        }\n      });\n      self._heartbeat.start();\n    }\n\n    // If this is a reconnect, we'll have to reset all stores.\n    if (self._lastSessionId) self._resetStores = true;\n\n    let reconnectedToPreviousSession;\n    if (typeof msg.session === 'string') {\n      reconnectedToPreviousSession = self._lastSessionId === msg.session;\n      self._lastSessionId = msg.session;\n    }\n\n    if (reconnectedToPreviousSession) {\n      // Successful reconnection -- pick up where we left off.\n      return;\n    }\n\n    // Server doesn't have our data anymore. Re-sync a new session.\n\n    // Forget about messages we were buffering for unknown collections. They'll\n    // be resent if still relevant.\n    self._updatesForUnknownStores = Object.create(null);\n\n    if (self._resetStores) {\n      // Forget about the effects of stubs. We'll be resetting all collections\n      // anyway.\n      self._documentsWrittenByStub = Object.create(null);\n      self._serverDocuments = Object.create(null);\n    }\n\n    // Clear _afterUpdateCallbacks.\n    self._afterUpdateCallbacks = [];\n\n    // Mark all named subscriptions which are ready as needing to be revived.\n    self._subsBeingRevived = Object.create(null);\n    Object.entries(self._subscriptions).forEach(([id, sub]) => {\n      if (sub.ready) {\n        self._subsBeingRevived[id] = true;\n      }\n    });\n\n    // Arrange for \"half-finished\" methods to have their callbacks run, and\n    // track methods that were sent on this connection so that we don't\n    // quiesce until they are all done.\n    //\n    // Start by clearing _methodsBlockingQuiescence: methods sent before\n    // reconnect don't matter, and any \"wait\" methods sent on the new connection\n    // that we drop here will be restored by the loop below.\n    self._methodsBlockingQuiescence = Object.create(null);\n    if (self._resetStores) {\n      const invokers = self._methodInvokers;\n      Object.keys(invokers).forEach(id => {\n        const invoker = invokers[id];\n        if (invoker.gotResult()) {\n          // This method already got its result, but it didn't call its callback\n          // because its data didn't become visible. We did not resend the\n          // method RPC. We'll call its callback when we get a full quiesce,\n          // since that's as close as we'll get to \"data must be visible\".\n          self._afterUpdateCallbacks.push(\n            (...args) => invoker.dataVisible(...args)\n          );\n        } else if (invoker.sentMessage) {\n          // This method has been sent on this connection (maybe as a resend\n          // from the last connection, maybe from onReconnect, maybe just very\n          // quickly before processing the connected message).\n          //\n          // We don't need to do anything special to ensure its callbacks get\n          // called, but we'll count it as a method which is preventing\n          // reconnect quiescence. (eg, it might be a login method that was run\n          // from onReconnect, and we don't want to see flicker by seeing a\n          // logged-out state.)\n          self._methodsBlockingQuiescence[invoker.methodId] = true;\n        }\n      });\n    }\n\n    self._messagesBufferedUntilQuiescence = [];\n\n    // If we're not waiting on any methods or subs, we can reset the stores and\n    // call the callbacks immediately.\n    if (!self._waitingForQuiescence()) {\n      if (self._resetStores) {\n        for (const store of Object.values(self._stores)) {\n          await store.beginUpdate(0, true);\n          await store.endUpdate();\n        }\n        self._resetStores = false;\n      }\n      self._runAfterUpdateCallbacks();\n    }\n  }\n\n  /**\n   * @summary Process various data messages from the server\n   * @param {Object} msg The data message\n   */\n  async _livedata_data(msg) {\n    const self = this._connection;\n\n    if (self._waitingForQuiescence()) {\n      self._messagesBufferedUntilQuiescence.push(msg);\n\n      if (msg.msg === 'nosub') {\n        delete self._subsBeingRevived[msg.id];\n      }\n\n      if (msg.subs) {\n        msg.subs.forEach(subId => {\n          delete self._subsBeingRevived[subId];\n        });\n      }\n\n      if (msg.methods) {\n        msg.methods.forEach(methodId => {\n          delete self._methodsBlockingQuiescence[methodId];\n        });\n      }\n\n      if (self._waitingForQuiescence()) {\n        return;\n      }\n\n      // No methods or subs are blocking quiescence!\n      // We'll now process and all of our buffered messages, reset all stores,\n      // and apply them all at once.\n      const bufferedMessages = self._messagesBufferedUntilQuiescence;\n      for (const bufferedMessage of Object.values(bufferedMessages)) {\n        await this._processOneDataMessage(\n          bufferedMessage,\n          self._bufferedWrites\n        );\n      }\n      self._messagesBufferedUntilQuiescence = [];\n    } else {\n      await this._processOneDataMessage(msg, self._bufferedWrites);\n    }\n\n    // Immediately flush writes when:\n    //  1. Buffering is disabled. Or;\n    //  2. any non-(added/changed/removed) message arrives.\n    const standardWrite =\n      msg.msg === \"added\" ||\n      msg.msg === \"changed\" ||\n      msg.msg === \"removed\";\n\n    if (self._bufferedWritesInterval === 0 || !standardWrite) {\n      await self._flushBufferedWrites();\n      return;\n    }\n\n    if (self._bufferedWritesFlushAt === null) {\n      self._bufferedWritesFlushAt =\n        new Date().valueOf() + self._bufferedWritesMaxAge;\n    } else if (self._bufferedWritesFlushAt < new Date().valueOf()) {\n      await self._flushBufferedWrites();\n      return;\n    }\n\n    if (self._bufferedWritesFlushHandle) {\n      clearTimeout(self._bufferedWritesFlushHandle);\n    }\n    self._bufferedWritesFlushHandle = setTimeout(() => {\n      self._liveDataWritesPromise = self._flushBufferedWrites();\n      if (Meteor._isPromise(self._liveDataWritesPromise)) {\n        self._liveDataWritesPromise.finally(\n          () => (self._liveDataWritesPromise = undefined)\n        );\n      }\n    }, self._bufferedWritesInterval);\n  }\n\n  /**\n   * @summary Process individual data messages by type\n   * @private\n   */\n  async _processOneDataMessage(msg, updates) {\n    const messageType = msg.msg;\n\n    switch (messageType) {\n      case 'added':\n        await this._connection._process_added(msg, updates);\n        break;\n      case 'changed':\n        this._connection._process_changed(msg, updates);\n        break;\n      case 'removed':\n        this._connection._process_removed(msg, updates);\n        break;\n      case 'ready':\n        this._connection._process_ready(msg, updates);\n        break;\n      case 'updated':\n        this._connection._process_updated(msg, updates);\n        break;\n      case 'nosub':\n        // ignore this\n        break;\n      default:\n        Meteor._debug('discarding unknown livedata data message type', msg);\n    }\n  }\n\n  /**\n   * @summary Handle method results arriving from the server\n   * @param {Object} msg The method result message\n   */\n  async _livedata_result(msg) {\n    const self = this._connection;\n\n    // Lets make sure there are no buffered writes before returning result.\n    if (!isEmpty(self._bufferedWrites)) {\n      await self._flushBufferedWrites();\n    }\n\n    // find the outstanding request\n    // should be O(1) in nearly all realistic use cases\n    if (isEmpty(self._outstandingMethodBlocks)) {\n      Meteor._debug('Received method result but no methods outstanding');\n      return;\n    }\n    const currentMethodBlock = self._outstandingMethodBlocks[0].methods;\n    let i;\n    const m = currentMethodBlock.find((method, idx) => {\n      const found = method.methodId === msg.id;\n      if (found) i = idx;\n      return found;\n    });\n    if (!m) {\n      Meteor._debug(\"Can't match method response to original method call\", msg);\n      return;\n    }\n\n    // Remove from current method block. This may leave the block empty, but we\n    // don't move on to the next block until the callback has been delivered, in\n    // _outstandingMethodFinished.\n    currentMethodBlock.splice(i, 1);\n\n    if (hasOwn.call(msg, 'error')) {\n      m.receiveResult(\n        new Meteor.Error(msg.error.error, msg.error.reason, msg.error.details)\n      );\n    } else {\n      // msg.result may be undefined if the method didn't return a value\n      m.receiveResult(undefined, msg.result);\n    }\n  }\n\n  /**\n   * @summary Handle \"nosub\" messages arriving from the server\n   * @param {Object} msg The nosub message\n   */\n  async _livedata_nosub(msg) {\n    const self = this._connection;\n\n    // First pass it through _livedata_data, which only uses it to help get\n    // towards quiescence.\n    await this._livedata_data(msg);\n\n    // Do the rest of our processing immediately, with no\n    // buffering-until-quiescence.\n\n    // we weren't subbed anyway, or we initiated the unsub.\n    if (!hasOwn.call(self._subscriptions, msg.id)) {\n      return;\n    }\n\n    // XXX COMPAT WITH ******* #errorCallback\n    const errorCallback = self._subscriptions[msg.id].errorCallback;\n    const stopCallback = self._subscriptions[msg.id].stopCallback;\n\n    self._subscriptions[msg.id].remove();\n\n    const meteorErrorFromMsg = msgArg => {\n      return (\n        msgArg &&\n        msgArg.error &&\n        new Meteor.Error(\n          msgArg.error.error,\n          msgArg.error.reason,\n          msgArg.error.details\n        )\n      );\n    };\n\n    // XXX COMPAT WITH ******* #errorCallback\n    if (errorCallback && msg.error) {\n      errorCallback(meteorErrorFromMsg(msg));\n    }\n\n    if (stopCallback) {\n      stopCallback(meteorErrorFromMsg(msg));\n    }\n  }\n\n  /**\n   * @summary Handle errors from the server\n   * @param {Object} msg The error message\n   */\n  _livedata_error(msg) {\n    Meteor._debug('Received error from server: ', msg.reason);\n    if (msg.offendingMessage) Meteor._debug('For: ', msg.offendingMessage);\n  }\n\n  // Document change message processors will be defined in a separate class\n}", "// A MethodInvoker manages sending a method to the server and calling the user's\n// callbacks. On construction, it registers itself in the connection's\n// _methodInvokers map; it removes itself once the method is fully finished and\n// the callback is invoked. This occurs when it has both received a result,\n// and the data written by it is fully visible.\nexport class MethodInvoker {\n  constructor(options) {\n    // Public (within this file) fields.\n    this.methodId = options.methodId;\n    this.sentMessage = false;\n\n    this._callback = options.callback;\n    this._connection = options.connection;\n    this._message = options.message;\n    this._onResultReceived = options.onResultReceived || (() => {});\n    this._wait = options.wait;\n    this.noRetry = options.noRetry;\n    this._methodResult = null;\n    this._dataVisible = false;\n\n    // Register with the connection.\n    this._connection._methodInvokers[this.methodId] = this;\n  }\n  // Sends the method message to the server. May be called additional times if\n  // we lose the connection and reconnect before receiving a result.\n  sendMessage() {\n    // This function is called before sending a method (including resending on\n    // reconnect). We should only (re)send methods where we don't already have a\n    // result!\n    if (this.gotResult())\n      throw new Error('sendingMethod is called on method with result');\n\n    // If we're re-sending it, it doesn't matter if data was written the first\n    // time.\n    this._dataVisible = false;\n    this.sentMessage = true;\n\n    // If this is a wait method, make all data messages be buffered until it is\n    // done.\n    if (this._wait)\n      this._connection._methodsBlockingQuiescence[this.methodId] = true;\n\n    // Actually send the message.\n    this._connection._send(this._message);\n  }\n  // Invoke the callback, if we have both a result and know that all data has\n  // been written to the local cache.\n  _maybeInvokeCallback() {\n    if (this._methodResult && this._dataVisible) {\n      // Call the callback. (This won't throw: the callback was wrapped with\n      // bindEnvironment.)\n      this._callback(this._methodResult[0], this._methodResult[1]);\n\n      // Forget about this method.\n      delete this._connection._methodInvokers[this.methodId];\n\n      // Let the connection know that this method is finished, so it can try to\n      // move on to the next block of methods.\n      this._connection._outstandingMethodFinished();\n    }\n  }\n  // Call with the result of the method from the server. Only may be called\n  // once; once it is called, you should not call sendMessage again.\n  // If the user provided an onResultReceived callback, call it immediately.\n  // Then invoke the main callback if data is also visible.\n  receiveResult(err, result) {\n    if (this.gotResult())\n      throw new Error('Methods should only receive results once');\n    this._methodResult = [err, result];\n    this._onResultReceived(err, result);\n    this._maybeInvokeCallback();\n  }\n  // Call this when all data written by the method is visible. This means that\n  // the method has returns its \"data is done\" message *AND* all server\n  // documents that are buffered at that time have been written to the local\n  // cache. Invokes the main callback if the result has been received.\n  dataVisible() {\n    this._dataVisible = true;\n    this._maybeInvokeCallback();\n  }\n  // True if receiveResult has been called.\n  gotResult() {\n    return !!this._methodResult;\n  }\n}\n", "import { MongoID } from 'meteor/mongo-id';\n\nexport class MongoIDMap extends IdMap {\n  constructor() {\n    super(MongoID.idStringify, MongoID.idParse);\n  }\n}", "import { DDPCommon } from 'meteor/ddp-common';\nimport { Meteor } from 'meteor/meteor';\n\nimport { Connection } from './livedata_connection.js';\n\n// This array allows the `_allSubscriptionsReady` method below, which\n// is used by the `spiderable` package, to keep track of whether all\n// data is ready.\nconst allConnections = [];\n\n/**\n * @namespace DDP\n * @summary Namespace for DDP-related methods/classes.\n */\nexport const DDP = {};\n\n// This is private but it's used in a few places. accounts-base uses\n// it to get the current user. Meteor.setTimeout and friends clear\n// it. We can probably find a better way to factor this.\nDDP._CurrentMethodInvocation = new Meteor.EnvironmentVariable();\nDDP._CurrentPublicationInvocation = new Meteor.EnvironmentVariable();\n\n// XXX: Keep DDP._CurrentInvocation for backwards-compatibility.\nDDP._CurrentInvocation = DDP._CurrentMethodInvocation;\n\nDDP._CurrentCallAsyncInvocation = new Meteor.EnvironmentVariable();\n\n// This is passed into a weird `makeErrorType` function that expects its thing\n// to be a constructor\nfunction connectionErrorConstructor(message) {\n  this.message = message;\n}\n\nDDP.ConnectionError = Meteor.makeErrorType(\n  'DDP.ConnectionError',\n  connectionErrorConstructor\n);\n\nDDP.ForcedReconnectError = Meteor.makeErrorType(\n  'DDP.ForcedReconnectError',\n  () => {}\n);\n\n// Returns the named sequence of pseudo-random values.\n// The scope will be DDP._CurrentMethodInvocation.get(), so the stream will produce\n// consistent values for method calls on the client and server.\nDDP.randomStream = name => {\n  const scope = DDP._CurrentMethodInvocation.get();\n  return DDPCommon.RandomStream.get(scope, name);\n};\n\n// @param url {String} URL to Meteor app,\n//     e.g.:\n//     \"subdomain.meteor.com\",\n//     \"http://subdomain.meteor.com\",\n//     \"/\",\n//     \"ddp+sockjs://ddp--****-foo.meteor.com/sockjs\"\n\n/**\n * @summary Connect to the server of a different Meteor application to subscribe to its document sets and invoke its remote methods.\n * @locus Anywhere\n * @param {String} url The URL of another Meteor application.\n * @param {Object} [options]\n * @param {Boolean} options.reloadWithOutstanding is it OK to reload if there are outstanding methods?\n * @param {Object} options.headers extra headers to send on the websockets connection, for server-to-server DDP only\n * @param {Object} options._sockjsOptions Specifies options to pass through to the sockjs client\n * @param {Function} options.onDDPNegotiationVersionFailure callback when version negotiation fails.\n */\nDDP.connect = (url, options) => {\n  const ret = new Connection(url, options);\n  allConnections.push(ret); // hack. see below.\n  return ret;\n};\n\nDDP._reconnectHook = new Hook({ bindEnvironment: false });\n\n/**\n * @summary Register a function to call as the first step of\n * reconnecting. This function can call methods which will be executed before\n * any other outstanding methods. For example, this can be used to re-establish\n * the appropriate authentication context on the connection.\n * @locus Anywhere\n * @param {Function} callback The function to call. It will be called with a\n * single argument, the [connection object](#ddp_connect) that is reconnecting.\n */\nDDP.onReconnect = callback => DDP._reconnectHook.register(callback);\n\n// Hack for `spiderable` package: a way to see if the page is done\n// loading all the data it needs.\n//\nDDP._allSubscriptionsReady = () => allConnections.every(\n  conn => Object.values(conn._subscriptions).every(sub => sub.ready)\n);\n"]}