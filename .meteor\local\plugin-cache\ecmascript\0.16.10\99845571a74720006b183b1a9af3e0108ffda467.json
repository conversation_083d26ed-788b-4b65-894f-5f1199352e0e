{"metadata": {}, "options": {"assumptions": {}, "compact": false, "sourceMaps": true, "ast": true, "babelrc": false, "configFile": false, "parserOpts": {"sourceType": "module", "sourceFileName": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "plugins": ["*", "flow", "jsx", "asyncGenerators", "bigInt", "classPrivateMethods", "classPrivateProperties", "classProperties", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportExtensions", "exportNamespaceFrom", "functionBind", "functionSent", "importMeta", "nullishCoalescingOperator", "numericSeparator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", ["pipelineOperator", {"proposal": "minimal"}], "throwExpressions", "topLevelAwait", "objectRestSpread", "objectRestSpread", "asyncGenerators", "classProperties", "classPrivateProperties", "jsx", "nullishCoalescingOperator", "nullishCoalescingOperator", "optionalChaining", "optionalChaining", "optionalCatchBinding", "optionalCatchBinding", "classProperties", "classPrivateProperties", "classPrivateMethods", "classProperties", "classPrivateProperties", "asyncGenerators", "asyncGenerators", "objectRestSpread", "objectRestSpread", "logicalAssignment"], "allowImportExportEverywhere": true, "allowReturnOutsideFunction": true, "allowUndeclaredExports": true, "strictMode": false}, "caller": {"name": "meteor", "arch": "os.windows.x86_64"}, "sourceFileName": "server/main.js", "filename": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "targets": {}, "cloneInputAst": true, "browserslistConfigFile": false, "passPerPreset": false, "envName": "development", "cwd": "C:\\Users\\<USER>\\smart-task-management-system", "root": "C:\\Users\\<USER>\\smart-task-management-system", "rootMode": "root", "plugins": [{"key": "base$0", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "base$0$0", "visitor": {"Program": {"enter": [null], "exit": [null]}, "_exploded": true, "_verified": true}, "options": {"avoidModernSyntax": false, "enforceStrictMode": false, "dynamicImport": true, "generateLetDeclarations": true, "topLevelAwait": true}, "externalDependencies": []}, {"key": "transform-runtime", "visitor": {"MemberExpression": {"enter": [null]}, "ObjectPattern": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "Identifier": {"enter": [null]}, "JSXIdentifier": {"enter": [null]}}, "options": {"version": "7.17.2", "helpers": true, "useESModules": false, "corejs": false}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {"loose": true}, "externalDependencies": []}, {"key": "transform-react-jsx", "visitor": {"JSXNamespacedName": {"enter": [null]}, "JSXSpreadChild": {"enter": [null]}, "Program": {"enter": [null]}, "JSXFragment": {"exit": [null]}, "JSXElement": {"exit": [null]}, "JSXAttribute": {"enter": [null]}}, "options": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "runtime": "classic", "throwIfNamespace": true, "useBuiltIns": false}, "externalDependencies": []}, {"key": "transform-react-display-name", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-react-pure-annotations", "visitor": {"CallExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "syntax-nullish-coalescing-operator", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-nullish-coalescing-operator", "visitor": {"LogicalExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-chaining", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-chaining", "visitor": {"OptionalCallExpression": {"enter": [null]}, "OptionalMemberExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-optional-catch-binding", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-optional-catch-binding", "visitor": {"CatchClause": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-class-properties", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-class-properties", "visitor": {"ExportDefaultDeclaration": {"enter": [null]}, "_exploded": true, "_verified": true, "ClassExpression": {"enter": [null]}, "ClassDeclaration": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-async-generators", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-async-generator-functions", "visitor": {"Program": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "syntax-object-rest-spread", "visitor": {"_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "proposal-object-rest-spread", "visitor": {"VariableDeclarator": {"enter": [null]}, "ExportNamedDeclaration": {"enter": [null]}, "CatchClause": {"enter": [null]}, "AssignmentExpression": {"enter": [null]}, "ArrayPattern": {"enter": [null]}, "ObjectExpression": {"enter": [null]}, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}, "ForInStatement": {"enter": [null]}, "ForOfStatement": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "proposal-logical-assignment-operators", "visitor": {"AssignmentExpression": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-literals", "visitor": {"NumericLiteral": {"enter": [null]}, "StringLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-template-literals", "visitor": {"TaggedTemplateExpression": {"enter": [null]}, "TemplateLiteral": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}, {"key": "transform-parameters", "visitor": {"_exploded": true, "_verified": true, "FunctionDeclaration": {"enter": [null]}, "FunctionExpression": {"enter": [null]}, "ObjectMethod": {"enter": [null]}, "ArrowFunctionExpression": {"enter": [null]}, "ClassMethod": {"enter": [null]}, "ClassPrivateMethod": {"enter": [null]}}, "options": {}, "externalDependencies": []}, {"key": "transform-exponentiation-operator", "visitor": {"AssignmentExpression": {"enter": [null]}, "BinaryExpression": {"enter": [null]}, "_exploded": true, "_verified": true}, "options": {}, "externalDependencies": []}], "presets": [], "generatorOpts": {"filename": "C:\\Users\\<USER>\\smart-task-management-system\\server\\main.js", "comments": true, "compact": false, "sourceMaps": true, "sourceFileName": "server/main.js"}}, "code": "!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {\n  \"use strict\";\n  try {\n    let _objectSpread;\n    module.link(\"@babel/runtime/helpers/objectSpread2\", {\n      default(v) {\n        _objectSpread = v;\n      }\n    }, 0);\n    let Meteor;\n    module.link(\"meteor/meteor\", {\n      Meteor(v) {\n        Meteor = v;\n      }\n    }, 0);\n    let LinksCollection;\n    module.link(\"/imports/api/links\", {\n      LinksCollection(v) {\n        LinksCollection = v;\n      }\n    }, 1);\n    let Accounts;\n    module.link(\"meteor/accounts-base\", {\n      Accounts(v) {\n        Accounts = v;\n      }\n    }, 2);\n    let Email;\n    module.link(\"meteor/email\", {\n      Email(v) {\n        Email = v;\n      }\n    }, 3);\n    let Tasks;\n    module.link(\"/imports/api/tasks\", {\n      Tasks(v) {\n        Tasks = v;\n      }\n    }, 4);\n    let Roles;\n    module.link(\"meteor/alanning:roles\", {\n      Roles(v) {\n        Roles = v;\n      }\n    }, 5);\n    let check;\n    module.link(\"meteor/check\", {\n      check(v) {\n        check = v;\n      }\n    }, 6);\n    let bcrypt;\n    module.link(\"bcrypt\", {\n      default(v) {\n        bcrypt = v;\n      }\n    }, 7);\n    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();\n    async function insertLink(_ref) {\n      let {\n        title,\n        url\n      } = _ref;\n      await LinksCollection.insertAsync({\n        title,\n        url,\n        createdAt: new Date()\n      });\n    }\n    const ADMIN_TOKEN = '123456';\n    Meteor.startup(async () => {\n      var _Meteor$settings$priv;\n      // Ensure indexes for Tasks collection\n      try {\n        await Tasks.createIndex({\n          createdAt: 1\n        });\n        await Tasks.createIndex({\n          assignedTo: 1\n        });\n        await Tasks.createIndex({\n          createdBy: 1\n        });\n\n        // Ensure indexes for Users collection\n        // Note: emails.address index is already created by Meteor accounts system\n        await Meteor.users.createIndex({\n          roles: 1\n        }, {\n          background: true\n        });\n      } catch (error) {\n        console.warn('[Startup] Index creation warning:', error.message);\n      }\n\n      // Check if we have any team members\n      try {\n        const allUsers = await Meteor.users.find().fetchAsync();\n        console.log('[Startup] All users:', allUsers.map(user => {\n          var _user$emails, _user$emails$;\n          return {\n            id: user._id,\n            email: (_user$emails = user.emails) === null || _user$emails === void 0 ? void 0 : (_user$emails$ = _user$emails[0]) === null || _user$emails$ === void 0 ? void 0 : _user$emails$.address,\n            roles: user.roles,\n            profile: user.profile\n          };\n        }));\n\n        // First, ensure all users have roles and createdAt\n        for (const user of allUsers) {\n          const updates = {};\n          if (!user.roles || !Array.isArray(user.roles)) {\n            updates.roles = ['team-member'];\n          }\n          if (!user.createdAt) {\n            updates.createdAt = new Date();\n          }\n          if (Object.keys(updates).length > 0) {\n            var _user$emails2, _user$emails2$;\n            console.log('[Startup] Fixing missing fields for user:', (_user$emails2 = user.emails) === null || _user$emails2 === void 0 ? void 0 : (_user$emails2$ = _user$emails2[0]) === null || _user$emails2$ === void 0 ? void 0 : _user$emails2$.address);\n            await Meteor.users.updateAsync(user._id, {\n              $set: updates\n            });\n          }\n        }\n        const teamMembersCount = await Meteor.users.find({\n          'roles': 'team-member'\n        }).countAsync();\n        console.log('[Startup] Found team members:', teamMembersCount);\n\n        // Create test team members if none exist\n        if (teamMembersCount === 0) {\n          console.log('[Startup] Creating test team members');\n          try {\n            // Create multiple test team members\n            const testMembers = [{\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'John',\n              lastName: 'Doe'\n            }, {\n              email: '<EMAIL>',\n              password: 'TeamPass123!',\n              firstName: 'Jane',\n              lastName: 'Smith'\n            }];\n            for (const member of testMembers) {\n              const userId = await Accounts.createUserAsync({\n                email: member.email,\n                password: member.password,\n                role: 'team-member',\n                createdAt: new Date(),\n                profile: {\n                  firstName: member.firstName,\n                  lastName: member.lastName,\n                  role: 'team-member',\n                  fullName: \"\".concat(member.firstName, \" \").concat(member.lastName)\n                }\n              });\n\n              // Set the role explicitly\n              await Meteor.users.updateAsync(userId, {\n                $set: {\n                  roles: ['team-member']\n                }\n              });\n              console.log('[Startup] Created test team member:', {\n                id: userId,\n                email: member.email,\n                name: \"\".concat(member.firstName, \" \").concat(member.lastName)\n              });\n            }\n          } catch (error) {\n            console.error('[Startup] Error creating test team members:', error);\n          }\n        }\n      } catch (error) {\n        console.error('[Startup] Error checking team members:', error);\n      }\n\n      // Email configuration from settings\n      const emailSettings = (_Meteor$settings$priv = Meteor.settings.private) === null || _Meteor$settings$priv === void 0 ? void 0 : _Meteor$settings$priv.email;\n\n      // Configure email SMTP\n      if (emailSettings !== null && emailSettings !== void 0 && emailSettings.username && emailSettings !== null && emailSettings !== void 0 && emailSettings.password) {\n        process.env.MAIL_URL = \"smtp://\".concat(encodeURIComponent(emailSettings.username), \":\").concat(encodeURIComponent(emailSettings.password), \"@\").concat(emailSettings.server, \":\").concat(emailSettings.port);\n\n        // Test email configuration\n        try {\n          console.log('Testing email configuration...');\n          Email.send({\n            to: emailSettings.username,\n            from: emailSettings.username,\n            subject: 'Test Email',\n            text: 'If you receive this email, your email configuration is working correctly.'\n          });\n          console.log('Test email sent successfully!');\n        } catch (error) {\n          console.error('Error sending test email:', error);\n        }\n      } else {\n        console.warn('Email configuration is missing in settings.json');\n      }\n\n      // Configure account creation - allow login without email verification\n      Accounts.config({\n        sendVerificationEmail: false,\n        // Disable email verification requirement\n        forbidClientAccountCreation: false\n      });\n\n      // Add login validation hook to explicitly allow all login attempts\n      Accounts.validateLoginAttempt(attempt => {\n        var _attempt$error, _attempt$user;\n        console.log('[validateLoginAttempt] Login attempt:', {\n          type: attempt.type,\n          allowed: attempt.allowed,\n          error: (_attempt$error = attempt.error) === null || _attempt$error === void 0 ? void 0 : _attempt$error.message,\n          userId: (_attempt$user = attempt.user) === null || _attempt$user === void 0 ? void 0 : _attempt$user._id,\n          methodName: attempt.methodName\n        });\n\n        // Always allow login attempts - bypass any built-in restrictions\n        if (attempt.user && attempt.type === 'password') {\n          console.log('[validateLoginAttempt] Allowing password login for user:', attempt.user._id);\n          return true;\n        }\n\n        // Allow other types of login attempts as well\n        return true;\n      });\n\n      // Customize verification email\n      Accounts.emailTemplates.siteName = \"Task Management System\";\n      Accounts.emailTemplates.from = emailSettings !== null && emailSettings !== void 0 && emailSettings.username ? \"Task Management System <\".concat(emailSettings.username, \">\") : \"Task Management System <<EMAIL>>\";\n      Accounts.emailTemplates.verifyEmail = {\n        subject() {\n          return \"Verify Your Email Address\";\n        },\n        text(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"Hello,\\n\\n\" + \"To verify your email address (\".concat(emailAddress, \"), please click the link below:\\n\\n\") + \"\".concat(url, \"\\n\\n\") + \"If you did not request this verification, please ignore this email.\\n\\n\" + \"Thanks,\\n\" + \"Your Task Management System Team\";\n        },\n        html(user, url) {\n          const emailAddress = user.emails[0].address;\n          return \"\\n        <html>\\n          <body style=\\\"font-family: Arial, sans-serif; padding: 20px; color: #333;\\\">\\n            <h2 style=\\\"color: #00875a;\\\">Verify Your Email Address</h2>\\n            <p>Hello,</p>\\n            <p>To verify your email address (\".concat(emailAddress, \"), please click the button below:</p>\\n            <p style=\\\"margin: 20px 0;\\\">\\n              <a href=\\\"\").concat(url, \"\\\" \\n                 style=\\\"background-color: #00875a; \\n                        color: white; \\n                        padding: 12px 25px; \\n                        text-decoration: none; \\n                        border-radius: 4px;\\n                        display: inline-block;\\\">\\n                Verify Email Address\\n              </a>\\n            </p>\\n            <p>If you did not request this verification, please ignore this email.</p>\\n            <p>Thanks,<br>Your Task Management System Team</p>\\n          </body>\\n        </html>\\n      \");\n        }\n      };\n\n      // If the Links collection is empty, add some data.\n      if ((await LinksCollection.find().countAsync()) === 0) {\n        await insertLink({\n          title: 'Do the Tutorial',\n          url: 'https://www.meteor.com/tutorials/react/creating-an-app'\n        });\n        await insertLink({\n          title: 'Follow the Guide',\n          url: 'https://guide.meteor.com'\n        });\n        await insertLink({\n          title: 'Read the Docs',\n          url: 'https://docs.meteor.com'\n        });\n        await insertLink({\n          title: 'Discussions',\n          url: 'https://forums.meteor.com'\n        });\n      }\n\n      // We publish the entire Links collection to all clients.\n      // In order to be fetched in real-time to the clients\n      Meteor.publish(\"links\", function () {\n        return LinksCollection.find();\n      });\n\n      // Add custom fields to users\n      Accounts.onCreateUser((options, user) => {\n        var _customizedUser$email, _customizedUser$email2;\n        console.log('[onCreateUser] Creating user with options:', {\n          email: options.email,\n          role: options.role,\n          profile: options.profile,\n          createdAt: options.createdAt\n        });\n        const customizedUser = _objectSpread({}, user);\n\n        // Ensure we have a profile\n        customizedUser.profile = options.profile || {};\n\n        // Add role from options\n        const role = options.role || 'team-member';\n        customizedUser.roles = [role];\n\n        // Set createdAt if provided, otherwise use current date\n        customizedUser.createdAt = options.createdAt || new Date();\n        console.log('[onCreateUser] Created user:', {\n          id: customizedUser._id,\n          email: (_customizedUser$email = customizedUser.emails) === null || _customizedUser$email === void 0 ? void 0 : (_customizedUser$email2 = _customizedUser$email[0]) === null || _customizedUser$email2 === void 0 ? void 0 : _customizedUser$email2.address,\n          roles: customizedUser.roles,\n          profile: customizedUser.profile,\n          createdAt: customizedUser.createdAt\n        });\n        return customizedUser;\n      });\n\n      // Publish team members\n      Meteor.publish('teamMembers', function () {\n        console.log('[teamMembers] Publication called, userId:', this.userId);\n        if (!this.userId) {\n          console.log('[teamMembers] No userId, returning ready');\n          return this.ready();\n        }\n        try {\n          // Simple query to find all team members\n          const teamMembers = Meteor.users.find({\n            $or: [{\n              'roles': 'team-member'\n            }, {\n              'profile.role': 'team-member'\n            }]\n          }, {\n            fields: {\n              emails: 1,\n              roles: 1,\n              'profile.firstName': 1,\n              'profile.lastName': 1,\n              'profile.fullName': 1,\n              createdAt: 1\n            }\n          });\n          console.log('[teamMembers] Publishing team members');\n          return teamMembers;\n        } catch (error) {\n          console.error('[teamMembers] Error in publication:', error);\n          return this.ready();\n        }\n      });\n\n      // Publish user data with roles\n      Meteor.publish('userData', function () {\n        if (!this.userId) {\n          return this.ready();\n        }\n        console.log('[userData] Publishing data for user:', this.userId);\n        return Meteor.users.find({\n          _id: this.userId\n        }, {\n          fields: {\n            roles: 1,\n            emails: 1,\n            profile: 1\n          }\n        });\n      });\n    });\n\n    // Method to create a new user with role\n    Meteor.methods({\n      'users.create'(_ref2) {\n        let {\n          email,\n          password,\n          role,\n          adminToken,\n          firstName,\n          lastName\n        } = _ref2;\n        // Validate admin token if trying to create admin account\n        if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n          throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n        }\n\n        // Validate password requirements\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(password)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(password)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(password)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(password)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Create the user\n        try {\n          const userId = Accounts.createUser({\n            email,\n            password,\n            role,\n            // This will be used in onCreateUser callback\n            profile: {\n              role,\n              // Store in profile as well for easy access\n              firstName,\n              lastName,\n              fullName: \"\".concat(firstName, \" \").concat(lastName)\n            }\n          });\n\n          // Send verification email\n          if (userId) {\n            Accounts.sendVerificationEmail(userId);\n          }\n          return userId;\n        } catch (error) {\n          throw new Meteor.Error('create-user-failed', error.message);\n        }\n      },\n      async 'users.getRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'User must be logged in');\n        }\n        try {\n          var _user$roles, _user$profile;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          if (!user) {\n            throw new Meteor.Error('user-not-found', 'User not found');\n          }\n\n          // Check both roles array and profile for role\n          const role = ((_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles[0]) || ((_user$profile = user.profile) === null || _user$profile === void 0 ? void 0 : _user$profile.role) || 'team-member';\n          return role;\n        } catch (error) {\n          throw new Meteor.Error('get-role-failed', error.message);\n        }\n      },\n      'users.resendVerificationEmail'(email) {\n        // Find user by email\n        const user = Accounts.findUserByEmail(email);\n        if (!user) {\n          throw new Meteor.Error('user-not-found', 'No user found with this email address');\n        }\n\n        // Check if email is already verified\n        const userEmail = user.emails[0];\n        if (userEmail.verified) {\n          throw new Meteor.Error('already-verified', 'This email is already verified');\n        }\n\n        // Send verification email\n        try {\n          Accounts.sendVerificationEmail(user._id, email);\n          return true;\n        } catch (error) {\n          throw new Meteor.Error('verification-email-failed', error.message);\n        }\n      },\n      async 'users.forgotPassword'(data) {\n        console.log('[forgotPassword] Method called with data:', JSON.stringify(data));\n        check(data, {\n          email: String,\n          newPassword: String\n        });\n        const {\n          email,\n          newPassword\n        } = data;\n        console.log('[forgotPassword] Processing request for email:', email);\n\n        // Find user by email using async method\n        console.log('[forgotPassword] Searching for user...');\n        let targetUser = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!targetUser) {\n          console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');\n          targetUser = await Meteor.users.findOneAsync({\n            'emails.address': {\n              $regex: new RegExp(\"^\".concat(email, \"$\"), 'i')\n            }\n          });\n          if (!targetUser) {\n            throw new Meteor.Error('user-not-found', 'No user found with this email address');\n          }\n          console.log('[forgotPassword] User found with case-insensitive search');\n        } else {\n          console.log('[forgotPassword] User found with direct search');\n        }\n\n        // Ensure we have a valid user with ID\n        if (!targetUser || !targetUser._id) {\n          throw new Meteor.Error('user-invalid', 'Found user but missing ID');\n        }\n        console.log('[forgotPassword] Final user ID:', targetUser._id);\n        console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);\n\n        // Validate password requirements\n        const passwordRegex = {\n          length: /.{8,}/,\n          uppercase: /[A-Z]/,\n          number: /[0-9]/,\n          special: /[!@#$%^&*]/\n        };\n        const passwordErrors = [];\n        if (!passwordRegex.length.test(newPassword)) {\n          passwordErrors.push('Password must be at least 8 characters long');\n        }\n        if (!passwordRegex.uppercase.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one uppercase letter');\n        }\n        if (!passwordRegex.number.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one number');\n        }\n        if (!passwordRegex.special.test(newPassword)) {\n          passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n        }\n        if (passwordErrors.length > 0) {\n          throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n        }\n\n        // Comprehensive password update with debugging\n        try {\n          console.log('[forgotPassword] Starting password update...');\n\n          // First, check current user document\n          console.log('[forgotPassword] Checking current user document...');\n          const currentUser = await Meteor.users.findOneAsync(targetUser._id);\n          console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));\n          if (!currentUser) {\n            throw new Meteor.Error('user-not-found', 'User document not found during update');\n          }\n\n          // Use Meteor's built-in password setting method for proper compatibility\n          console.log('[forgotPassword] Using Accounts.setPasswordAsync for proper password hashing...');\n          try {\n            await Accounts.setPasswordAsync(targetUser._id, newPassword, {\n              logout: false\n            });\n            console.log('[forgotPassword] Password set successfully using Accounts.setPasswordAsync');\n\n            // Also verify the email during password reset\n            const emailUpdateResult = await Meteor.users.updateAsync(targetUser._id, {\n              $set: {\n                'emails.0.verified': true // Automatically verify email during password reset\n              }\n            });\n            console.log('[forgotPassword] Email verification update result:', emailUpdateResult);\n            successMethod = 'Accounts.setPasswordAsync + email verification';\n            updateResult = 1; // Mark as successful\n          } catch (setPasswordError) {\n            console.error('[forgotPassword] Accounts.setPasswordAsync error:', setPasswordError);\n            throw new Meteor.Error('password-update-failed', 'Failed to update password using Meteor accounts system');\n          }\n\n          // Verify the password was set successfully\n          console.log(\"[forgotPassword] Password update successful using: \".concat(successMethod));\n\n          // Get updated user to verify the changes\n          const updatedUser = await Meteor.users.findOneAsync(targetUser._id);\n          console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));\n          return {\n            success: true,\n            message: 'Password updated successfully using Meteor accounts system'\n          };\n        } catch (error) {\n          console.error('[forgotPassword] Error during password update:', error);\n          throw new Meteor.Error('password-update-failed', \"Failed to update password: \".concat(error.message));\n        }\n      },\n      async 'users.debugUser'(_ref3) {\n        let {\n          email\n        } = _ref3;\n        try {\n          var _fullUser$services, _fullUser$services2, _fullUser$services2$p;\n          check(email, String);\n          console.log('[debugUser] Debugging user:', email);\n\n          // Find user using async method\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            console.log('[debugUser] User not found');\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[debugUser] User found:', user._id);\n\n          // Get full user document using async method\n          const fullUser = await Meteor.users.findOneAsync(user._id);\n          console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));\n          if (!fullUser) {\n            console.log('[debugUser] Full user document not found');\n            return {\n              success: false,\n              error: 'Full user document not found'\n            };\n          }\n\n          // Test basic update using async method\n          let testUpdateResult = null;\n          try {\n            testUpdateResult = await Meteor.users.updateAsync(user._id, {\n              $set: {\n                'profile.debugTest': new Date()\n              }\n            });\n            console.log('[debugUser] Test update result:', testUpdateResult);\n          } catch (updateError) {\n            console.error('[debugUser] Test update error:', updateError);\n          }\n\n          // Try using Accounts.setPassword if available\n          let hasSetPassword = false;\n          try {\n            hasSetPassword = typeof Accounts.setPassword === 'function';\n            console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);\n          } catch (setPasswordError) {\n            console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);\n          }\n          const result = {\n            success: true,\n            userId: user._id,\n            userIdType: typeof user._id,\n            hasServices: !!fullUser.services,\n            hasPassword: !!((_fullUser$services = fullUser.services) !== null && _fullUser$services !== void 0 && _fullUser$services.password),\n            hasBcrypt: !!((_fullUser$services2 = fullUser.services) !== null && _fullUser$services2 !== void 0 && (_fullUser$services2$p = _fullUser$services2.password) !== null && _fullUser$services2$p !== void 0 && _fullUser$services2$p.bcrypt),\n            roles: fullUser.roles || [],\n            profile: fullUser.profile || {},\n            testUpdateResult: testUpdateResult,\n            hasSetPassword: hasSetPassword,\n            servicesStructure: fullUser.services || {}\n          };\n          console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));\n          return result;\n        } catch (error) {\n          console.error('[debugUser] Error in debug method:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.testLogin'(_ref4) {\n        let {\n          email,\n          password\n        } = _ref4;\n        check(email, String);\n        check(password, String);\n        console.log('[testLogin] Testing login for email:', email);\n\n        // Find user using async method\n        const user = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!user) {\n          console.log('[testLogin] User not found');\n          return {\n            success: false,\n            error: 'User not found'\n          };\n        }\n        console.log('[testLogin] User found:', user._id);\n        console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));\n\n        // Test password verification\n        try {\n          // Check if password service exists\n          if (!user.services || !user.services.password || !user.services.password.bcrypt) {\n            console.log('[testLogin] No password hash found in user services');\n            return {\n              success: false,\n              error: 'No password hash found',\n              userId: user._id,\n              services: user.services\n            };\n          }\n          const bcrypt = require('bcrypt');\n          const storedHash = user.services.password.bcrypt;\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n          console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n          console.log('[testLogin] Password length:', password.length);\n          return {\n            success: passwordMatch,\n            userId: user._id,\n            hashPreview: storedHash.substring(0, 20) + '...',\n            passwordLength: password.length\n          };\n        } catch (error) {\n          console.error('[testLogin] Error during password test:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.comparePasswordFormats'(_ref5) {\n        var _user$services, _user$services$passwo;\n        let {\n          email\n        } = _ref5;\n        check(email, String);\n        console.log('[comparePasswordFormats] Checking password format for:', email);\n\n        // Find user\n        const user = await Meteor.users.findOneAsync({\n          'emails.address': email\n        });\n        if (!user) {\n          return {\n            success: false,\n            error: 'User not found'\n          };\n        }\n        console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));\n\n        // Check if user has password\n        if (!((_user$services = user.services) !== null && _user$services !== void 0 && (_user$services$passwo = _user$services.password) !== null && _user$services$passwo !== void 0 && _user$services$passwo.bcrypt)) {\n          return {\n            success: false,\n            error: 'No password found'\n          };\n        }\n        const storedHash = user.services.password.bcrypt;\n        console.log('[comparePasswordFormats] Stored hash:', storedHash);\n        console.log('[comparePasswordFormats] Hash length:', storedHash.length);\n        console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));\n\n        // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)\n        const isBcrypt = /^\\$2[aby]\\$/.test(storedHash);\n        console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);\n        return {\n          success: true,\n          userId: user._id,\n          hashLength: storedHash.length,\n          hashPreview: storedHash.substring(0, 20) + '...',\n          isBcryptFormat: isBcrypt,\n          fullServices: user.services\n        };\n      },\n      async 'users.testActualLogin'(_ref6) {\n        let {\n          email,\n          password\n        } = _ref6;\n        check(email, String);\n        check(password, String);\n        console.log('[testActualLogin] Testing actual login for:', email);\n        try {\n          var _user$services2, _user$services2$passw, _user$emails3, _user$emails3$;\n          // Try to simulate what Meteor.loginWithPassword does\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            console.log('[testActualLogin] User not found');\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[testActualLogin] User found:', user._id);\n          console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));\n\n          // Check if password service exists\n          if (!((_user$services2 = user.services) !== null && _user$services2 !== void 0 && (_user$services2$passw = _user$services2.password) !== null && _user$services2$passw !== void 0 && _user$services2$passw.bcrypt)) {\n            console.log('[testActualLogin] No password hash found');\n            return {\n              success: false,\n              error: 'No password hash found'\n            };\n          }\n\n          // Test bcrypt verification\n          const bcrypt = require('bcrypt');\n          const storedHash = user.services.password.bcrypt;\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n          console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n\n          // Check if user has any login restrictions\n          const isVerified = ((_user$emails3 = user.emails) === null || _user$emails3 === void 0 ? void 0 : (_user$emails3$ = _user$emails3[0]) === null || _user$emails3$ === void 0 ? void 0 : _user$emails3$.verified) || false;\n          console.log('[testActualLogin] Email verified:', isVerified);\n\n          // Check user roles\n          console.log('[testActualLogin] User roles:', user.roles);\n\n          // Try to create a login token manually to test if that works\n          let loginTokenTest = null;\n          try {\n            // This is what Meteor does internally for login\n            const stampedToken = Accounts._generateStampedLoginToken();\n            console.log('[testActualLogin] Generated login token:', !!stampedToken);\n            loginTokenTest = 'Token generation successful';\n          } catch (tokenError) {\n            console.error('[testActualLogin] Token generation error:', tokenError);\n            loginTokenTest = tokenError.message;\n          }\n          return {\n            success: passwordMatch,\n            userId: user._id,\n            passwordVerification: passwordMatch,\n            emailVerified: isVerified,\n            userRoles: user.roles,\n            hashPreview: storedHash.substring(0, 20) + '...',\n            loginTokenTest: loginTokenTest,\n            fullUserStructure: {\n              _id: user._id,\n              emails: user.emails,\n              services: user.services,\n              roles: user.roles,\n              profile: user.profile\n            }\n          };\n        } catch (error) {\n          console.error('[testActualLogin] Error:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.simulateLogin'(_ref7) {\n        let {\n          email,\n          password\n        } = _ref7;\n        check(email, String);\n        check(password, String);\n        console.log('[simulateLogin] Simulating login for:', email);\n        try {\n          var _user$services3, _user$services3$passw, _user$emails4, _user$emails4$;\n          // Find user\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[simulateLogin] User found:', user._id);\n\n          // Check password\n          const bcrypt = require('bcrypt');\n          const storedHash = (_user$services3 = user.services) === null || _user$services3 === void 0 ? void 0 : (_user$services3$passw = _user$services3.password) === null || _user$services3$passw === void 0 ? void 0 : _user$services3$passw.bcrypt;\n          if (!storedHash) {\n            return {\n              success: false,\n              error: 'No password hash found'\n            };\n          }\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[simulateLogin] Password match:', passwordMatch);\n          if (!passwordMatch) {\n            return {\n              success: false,\n              error: 'Invalid password'\n            };\n          }\n\n          // Check if there are any login restrictions\n          const restrictions = [];\n\n          // Check email verification requirement - DISABLED for testing\n          const emailVerified = ((_user$emails4 = user.emails) === null || _user$emails4 === void 0 ? void 0 : (_user$emails4$ = _user$emails4[0]) === null || _user$emails4$ === void 0 ? void 0 : _user$emails4$.verified) || false;\n          // if (!emailVerified) {\n          //   restrictions.push('Email not verified');\n          // }\n\n          // Check if user is active (no disabled flag)\n          if (user.disabled) {\n            restrictions.push('User account disabled');\n          }\n\n          // Check roles\n          if (!user.roles || user.roles.length === 0) {\n            restrictions.push('No roles assigned');\n          }\n          console.log('[simulateLogin] Login restrictions:', restrictions);\n\n          // Try to manually create what loginWithPassword would do\n          let loginSimulation = 'Not attempted';\n          try {\n            // Check if we can generate a login token\n            const stampedToken = Accounts._generateStampedLoginToken();\n            if (stampedToken) {\n              loginSimulation = 'Login token generation successful';\n            }\n          } catch (tokenError) {\n            loginSimulation = \"Token error: \".concat(tokenError.message);\n          }\n          return {\n            success: passwordMatch && restrictions.length === 0,\n            userId: user._id,\n            passwordMatch: passwordMatch,\n            emailVerified: emailVerified,\n            restrictions: restrictions,\n            loginSimulation: loginSimulation,\n            userStructure: {\n              _id: user._id,\n              emails: user.emails,\n              roles: user.roles,\n              profile: user.profile,\n              disabled: user.disabled || false\n            }\n          };\n        } catch (error) {\n          console.error('[simulateLogin] Error:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.testServerLogin'(_ref8) {\n        let {\n          email,\n          password\n        } = _ref8;\n        check(email, String);\n        check(password, String);\n        console.log('[testServerLogin] Testing server-side login for:', email);\n        try {\n          var _user$services4, _user$services4$passw;\n          // Try to use Meteor's internal login method\n          const user = await Meteor.users.findOneAsync({\n            'emails.address': email\n          });\n          if (!user) {\n            return {\n              success: false,\n              error: 'User not found'\n            };\n          }\n          console.log('[testServerLogin] User found:', user._id);\n\n          // Check if password matches\n          const bcrypt = require('bcrypt');\n          const storedHash = (_user$services4 = user.services) === null || _user$services4 === void 0 ? void 0 : (_user$services4$passw = _user$services4.password) === null || _user$services4$passw === void 0 ? void 0 : _user$services4$passw.bcrypt;\n          if (!storedHash) {\n            return {\n              success: false,\n              error: 'No password hash found'\n            };\n          }\n          const passwordMatch = bcrypt.compareSync(password, storedHash);\n          console.log('[testServerLogin] Password match:', passwordMatch);\n          if (!passwordMatch) {\n            return {\n              success: false,\n              error: 'Invalid password'\n            };\n          }\n\n          // Try to create a login token manually\n          try {\n            const stampedToken = Accounts._generateStampedLoginToken();\n            console.log('[testServerLogin] Login token generated:', !!stampedToken);\n\n            // Try to add the token to the user\n            await Meteor.users.updateAsync(user._id, {\n              $push: {\n                'services.resume.loginTokens': stampedToken\n              }\n            });\n            console.log('[testServerLogin] Login token added to user');\n            return {\n              success: true,\n              userId: user._id,\n              token: stampedToken.token,\n              message: 'Server-side login simulation successful'\n            };\n          } catch (tokenError) {\n            console.error('[testServerLogin] Token error:', tokenError);\n            return {\n              success: false,\n              error: \"Token error: \".concat(tokenError.message)\n            };\n          }\n        } catch (error) {\n          console.error('[testServerLogin] Error:', error);\n          return {\n            success: false,\n            error: error.message\n          };\n        }\n      },\n      async 'users.checkAndFixAdminRole'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _user$emails5, _user$emails5$;\n          const user = await Meteor.users.findOneAsync(this.userId);\n          console.log('[checkAndFixAdminRole] Checking user:', {\n            id: user === null || user === void 0 ? void 0 : user._id,\n            email: user === null || user === void 0 ? void 0 : (_user$emails5 = user.emails) === null || _user$emails5 === void 0 ? void 0 : (_user$emails5$ = _user$emails5[0]) === null || _user$emails5$ === void 0 ? void 0 : _user$emails5$.address,\n            roles: user === null || user === void 0 ? void 0 : user.roles\n          });\n\n          // If user has no roles array, initialize it\n          if (!user.roles) {\n            await Meteor.users.updateAsync(this.userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return 'Roles initialized';\n          }\n\n          // If user has no roles or doesn't have admin role\n          if (!user.roles.includes('admin')) {\n            console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n\n            // Check if this is the first user (they should be admin)\n            const totalUsers = await Meteor.users.find().countAsync();\n            if (totalUsers === 1) {\n              console.log('[checkAndFixAdminRole] First user, setting as admin');\n              await Meteor.users.updateAsync(this.userId, {\n                $set: {\n                  roles: ['admin']\n                }\n              });\n              return 'Admin role added';\n            }\n            return 'User is not admin';\n          }\n          return 'User is already admin';\n        } catch (error) {\n          console.error('[checkAndFixAdminRole] Error:', error);\n          throw new Meteor.Error('check-role-failed', error.message);\n        }\n      },\n      async 'users.diagnoseRoles'() {\n        if (!this.userId) {\n          throw new Meteor.Error('not-authorized', 'You must be logged in');\n        }\n        try {\n          var _currentUser$roles;\n          const currentUser = await Meteor.users.findOneAsync(this.userId);\n          if (!((_currentUser$roles = currentUser.roles) !== null && _currentUser$roles !== void 0 && _currentUser$roles.includes('admin'))) {\n            throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n          }\n          const allUsers = await Meteor.users.find().fetchAsync();\n          const usersWithIssues = [];\n          const fixes = [];\n          for (const user of allUsers) {\n            var _user$profile3, _user$roles2;\n            const issues = [];\n\n            // Check if roles array exists\n            if (!user.roles || !Array.isArray(user.roles)) {\n              var _user$profile2, _user$emails6, _user$emails6$;\n              issues.push('No roles array');\n              // Fix: Initialize roles based on profile\n              const role = ((_user$profile2 = user.profile) === null || _user$profile2 === void 0 ? void 0 : _user$profile2.role) || 'team-member';\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [role]\n                }\n              });\n              fixes.push(\"Initialized roles for \".concat((_user$emails6 = user.emails) === null || _user$emails6 === void 0 ? void 0 : (_user$emails6$ = _user$emails6[0]) === null || _user$emails6$ === void 0 ? void 0 : _user$emails6$.address));\n            }\n\n            // Check if role matches profile\n            if ((_user$profile3 = user.profile) !== null && _user$profile3 !== void 0 && _user$profile3.role && ((_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2[0]) !== user.profile.role) {\n              var _user$emails7, _user$emails7$;\n              issues.push('Role mismatch with profile');\n              // Fix: Update roles to match profile\n              await Meteor.users.updateAsync(user._id, {\n                $set: {\n                  roles: [user.profile.role]\n                }\n              });\n              fixes.push(\"Fixed role mismatch for \".concat((_user$emails7 = user.emails) === null || _user$emails7 === void 0 ? void 0 : (_user$emails7$ = _user$emails7[0]) === null || _user$emails7$ === void 0 ? void 0 : _user$emails7$.address));\n            }\n            if (issues.length > 0) {\n              var _user$emails8, _user$emails8$;\n              usersWithIssues.push({\n                email: (_user$emails8 = user.emails) === null || _user$emails8 === void 0 ? void 0 : (_user$emails8$ = _user$emails8[0]) === null || _user$emails8$ === void 0 ? void 0 : _user$emails8$.address,\n                issues\n              });\n            }\n          }\n          return {\n            usersWithIssues,\n            fixes,\n            message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n          };\n        } catch (error) {\n          throw new Meteor.Error('diagnose-failed', error.message);\n        }\n      },\n      'users.createTestTeamMember'() {\n        // Only allow in development\n        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n          try {\n            const testMember = {\n              email: '<EMAIL>',\n              password: 'TestPass123!',\n              firstName: 'Test',\n              lastName: 'Member'\n            };\n            const userId = Accounts.createUser({\n              email: testMember.email,\n              password: testMember.password,\n              profile: {\n                firstName: testMember.firstName,\n                lastName: testMember.lastName,\n                role: 'team-member',\n                fullName: \"\".concat(testMember.firstName, \" \").concat(testMember.lastName)\n              }\n            });\n\n            // Set the role explicitly\n            Meteor.users.update(userId, {\n              $set: {\n                roles: ['team-member']\n              }\n            });\n            return {\n              success: true,\n              userId,\n              message: 'Test team member created successfully'\n            };\n          } catch (error) {\n            console.error('[createTestTeamMember] Error:', error);\n            throw new Meteor.Error('create-test-member-failed', error.message);\n          }\n        } else {\n          throw new Meteor.Error('not-development', 'This method is only available in development');\n        }\n      }\n    });\n    __reify_async_result__();\n  } catch (_reifyError) {\n    return __reify_async_result__(_reifyError);\n  }\n  __reify_async_result__()\n}, {\n  self: this,\n  async: false\n});", "map": {"version": 3, "names": ["_objectSpread", "module", "link", "default", "v", "Meteor", "LinksCollection", "Accounts", "Email", "Tasks", "Roles", "check", "bcrypt", "__reifyWaitForDeps__", "insertLink", "_ref", "title", "url", "insertAsync", "createdAt", "Date", "ADMIN_TOKEN", "startup", "_Meteor$settings$priv", "createIndex", "assignedTo", "created<PERSON>y", "users", "roles", "background", "error", "console", "warn", "message", "allUsers", "find", "fetchAsync", "log", "map", "user", "_user$emails", "_user$emails$", "id", "_id", "email", "emails", "address", "profile", "updates", "Array", "isArray", "Object", "keys", "length", "_user$emails2", "_user$emails2$", "updateAsync", "$set", "teamMembersCount", "countAsync", "testMembers", "password", "firstName", "lastName", "member", "userId", "createUserAsync", "role", "fullName", "concat", "name", "emailSettings", "settings", "private", "username", "process", "env", "MAIL_URL", "encodeURIComponent", "server", "port", "send", "to", "from", "subject", "text", "config", "sendVerificationEmail", "forbidClientAccountCreation", "validateLoginAttempt", "attempt", "_attempt$error", "_attempt$user", "type", "allowed", "methodName", "emailTemplates", "siteName", "verifyEmail", "emailAddress", "html", "publish", "onCreateUser", "options", "_customizedUser$email", "_customizedUser$email2", "customizedUser", "ready", "teamMembers", "$or", "fields", "methods", "users.create", "_ref2", "adminToken", "Error", "passwordRegex", "uppercase", "number", "special", "passwordErrors", "test", "push", "join", "createUser", "users.getRole", "_user$roles", "_user$profile", "findOneAsync", "users.resendVerificationEmail", "findUserByEmail", "userEmail", "verified", "users.forgotPassword", "data", "JSON", "stringify", "String", "newPassword", "targetUser", "$regex", "RegExp", "currentUser", "setPasswordAsync", "logout", "emailUpdateResult", "successMethod", "updateResult", "setPasswordError", "updatedUser", "services", "success", "users.debugUser", "_ref3", "_fullUser$services", "_fullUser$services2", "_fullUser$services2$p", "fullUser", "testUpdateResult", "updateError", "hasSetPassword", "setPassword", "result", "userIdType", "hasServices", "hasPassword", "hasBcrypt", "servicesStructure", "users.testLogin", "_ref4", "require", "storedHash", "passwordMatch", "compareSync", "substring", "hashPreview", "<PERSON><PERSON><PERSON><PERSON>", "users.comparePasswordFormats", "_ref5", "_user$services", "_user$services$passwo", "isBcrypt", "hash<PERSON><PERSON><PERSON>", "isBcryptFormat", "fullServices", "users.testActualLogin", "_ref6", "_user$services2", "_user$services2$passw", "_user$emails3", "_user$emails3$", "isVerified", "loginTokenTest", "stampedToken", "_generateStampedLoginToken", "tokenError", "passwordVerification", "emailVerified", "userRoles", "fullUserStructure", "users.simulateLogin", "_ref7", "_user$services3", "_user$services3$passw", "_user$emails4", "_user$emails4$", "restrictions", "disabled", "loginSimulation", "userStructure", "users.testServerLogin", "_ref8", "_user$services4", "_user$services4$passw", "$push", "token", "users.checkAndFixAdminRole", "_user$emails5", "_user$emails5$", "includes", "totalUsers", "users.diagnoseRoles", "_currentUser$roles", "usersWithIssues", "fixes", "_user$profile3", "_user$roles2", "issues", "_user$profile2", "_user$emails6", "_user$emails6$", "_user$emails7", "_user$emails7$", "_user$emails8", "_user$emails8$", "users.createTestTeamMember", "NODE_ENV", "testMember", "update", "__reify_async_result__", "_reifyError", "self", "async"], "sources": ["server/main.js"], "sourcesContent": ["import { Meteor } from 'meteor/meteor';\nimport { LinksCollection } from '/imports/api/links';\nimport { Accounts } from 'meteor/accounts-base';\nimport { Email } from 'meteor/email';\nimport { Tasks } from '/imports/api/tasks';\nimport { Roles } from 'meteor/alanning:roles';\nimport { check } from 'meteor/check';\nimport bcrypt from 'bcrypt';\n\nasync function insertLink({ title, url }) {\n  await LinksCollection.insertAsync({ title, url, createdAt: new Date() });\n}\n\nconst ADMIN_TOKEN = '123456';\n\nMeteor.startup(async () => {\n  // Ensure indexes for Tasks collection\n  try {\n    await Tasks.createIndex({ createdAt: 1 });\n    await Tasks.createIndex({ assignedTo: 1 });\n    await Tasks.createIndex({ createdBy: 1 });\n\n    // Ensure indexes for Users collection\n    // Note: emails.address index is already created by Meteor accounts system\n    await Meteor.users.createIndex({ roles: 1 }, { background: true });\n  } catch (error) {\n    console.warn('[Startup] Index creation warning:', error.message);\n  }\n\n  // Check if we have any team members\n  try {\n    const allUsers = await Meteor.users.find().fetchAsync();\n    console.log('[Startup] All users:', allUsers.map(user => ({\n      id: user._id,\n      email: user.emails?.[0]?.address,\n      roles: user.roles,\n      profile: user.profile\n    })));\n\n    // First, ensure all users have roles and createdAt\n    for (const user of allUsers) {\n      const updates = {};\n      \n      if (!user.roles || !Array.isArray(user.roles)) {\n        updates.roles = ['team-member'];\n      }\n      \n      if (!user.createdAt) {\n        updates.createdAt = new Date();\n      }\n      \n      if (Object.keys(updates).length > 0) {\n        console.log('[Startup] Fixing missing fields for user:', user.emails?.[0]?.address);\n        await Meteor.users.updateAsync(user._id, {\n          $set: updates\n        });\n      }\n    }\n\n    const teamMembersCount = await Meteor.users.find({ 'roles': 'team-member' }).countAsync();\n    console.log('[Startup] Found team members:', teamMembersCount);\n\n    // Create test team members if none exist\n    if (teamMembersCount === 0) {\n      console.log('[Startup] Creating test team members');\n      try {\n        // Create multiple test team members\n        const testMembers = [\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'John',\n            lastName: 'Doe'\n          },\n          {\n            email: '<EMAIL>',\n            password: 'TeamPass123!',\n            firstName: 'Jane',\n            lastName: 'Smith'\n          }\n        ];\n\n        for (const member of testMembers) {\n          const userId = await Accounts.createUserAsync({\n            email: member.email,\n            password: member.password,\n            role: 'team-member',\n            createdAt: new Date(),\n            profile: {\n              firstName: member.firstName,\n              lastName: member.lastName,\n              role: 'team-member',\n              fullName: `${member.firstName} ${member.lastName}`\n            }\n          });\n\n          // Set the role explicitly\n          await Meteor.users.updateAsync(userId, {\n            $set: { roles: ['team-member'] }\n          });\n\n          console.log('[Startup] Created test team member:', {\n            id: userId,\n            email: member.email,\n            name: `${member.firstName} ${member.lastName}`\n          });\n        }\n      } catch (error) {\n        console.error('[Startup] Error creating test team members:', error);\n      }\n    }\n  } catch (error) {\n    console.error('[Startup] Error checking team members:', error);\n  }\n\n  // Email configuration from settings\n  const emailSettings = Meteor.settings.private?.email;\n  \n  // Configure email SMTP\n  if (emailSettings?.username && emailSettings?.password) {\n    process.env.MAIL_URL = `smtp://${encodeURIComponent(emailSettings.username)}:${encodeURIComponent(emailSettings.password)}@${emailSettings.server}:${emailSettings.port}`;\n    \n    // Test email configuration\n    try {\n      console.log('Testing email configuration...');\n      Email.send({\n        to: emailSettings.username,\n        from: emailSettings.username,\n        subject: 'Test Email',\n        text: 'If you receive this email, your email configuration is working correctly.'\n      });\n      console.log('Test email sent successfully!');\n    } catch (error) {\n      console.error('Error sending test email:', error);\n    }\n  } else {\n    console.warn('Email configuration is missing in settings.json');\n  }\n\n  // Configure account creation - allow login without email verification\n  Accounts.config({\n    sendVerificationEmail: false,  // Disable email verification requirement\n    forbidClientAccountCreation: false\n  });\n\n  // Add login validation hook to explicitly allow all login attempts\n  Accounts.validateLoginAttempt((attempt) => {\n    console.log('[validateLoginAttempt] Login attempt:', {\n      type: attempt.type,\n      allowed: attempt.allowed,\n      error: attempt.error?.message,\n      userId: attempt.user?._id,\n      methodName: attempt.methodName\n    });\n\n    // Always allow login attempts - bypass any built-in restrictions\n    if (attempt.user && attempt.type === 'password') {\n      console.log('[validateLoginAttempt] Allowing password login for user:', attempt.user._id);\n      return true;\n    }\n\n    // Allow other types of login attempts as well\n    return true;\n  });\n\n  // Customize verification email\n  Accounts.emailTemplates.siteName = \"Task Management System\";\n  Accounts.emailTemplates.from = emailSettings?.username ? \n    `Task Management System <${emailSettings.username}>` : \n    \"Task Management System <<EMAIL>>\";\n\n  Accounts.emailTemplates.verifyEmail = {\n    subject() {\n      return \"Verify Your Email Address\";\n    },\n    text(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `Hello,\\n\\n`\n        + `To verify your email address (${emailAddress}), please click the link below:\\n\\n`\n        + `${url}\\n\\n`\n        + `If you did not request this verification, please ignore this email.\\n\\n`\n        + `Thanks,\\n`\n        + `Your Task Management System Team`;\n    },\n    html(user, url) {\n      const emailAddress = user.emails[0].address;\n      return `\n        <html>\n          <body style=\"font-family: Arial, sans-serif; padding: 20px; color: #333;\">\n            <h2 style=\"color: #00875a;\">Verify Your Email Address</h2>\n            <p>Hello,</p>\n            <p>To verify your email address (${emailAddress}), please click the button below:</p>\n            <p style=\"margin: 20px 0;\">\n              <a href=\"${url}\" \n                 style=\"background-color: #00875a; \n                        color: white; \n                        padding: 12px 25px; \n                        text-decoration: none; \n                        border-radius: 4px;\n                        display: inline-block;\">\n                Verify Email Address\n              </a>\n            </p>\n            <p>If you did not request this verification, please ignore this email.</p>\n            <p>Thanks,<br>Your Task Management System Team</p>\n          </body>\n        </html>\n      `;\n    }\n  };\n\n  // If the Links collection is empty, add some data.\n  if (await LinksCollection.find().countAsync() === 0) {\n    await insertLink({\n      title: 'Do the Tutorial',\n      url: 'https://www.meteor.com/tutorials/react/creating-an-app',\n    });\n\n    await insertLink({\n      title: 'Follow the Guide',\n      url: 'https://guide.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Read the Docs',\n      url: 'https://docs.meteor.com',\n    });\n\n    await insertLink({\n      title: 'Discussions',\n      url: 'https://forums.meteor.com',\n    });\n  }\n\n  // We publish the entire Links collection to all clients.\n  // In order to be fetched in real-time to the clients\n  Meteor.publish(\"links\", function () {\n    return LinksCollection.find();\n  });\n\n  // Add custom fields to users\n  Accounts.onCreateUser((options, user) => {\n    console.log('[onCreateUser] Creating user with options:', {\n      email: options.email,\n      role: options.role,\n      profile: options.profile,\n      createdAt: options.createdAt\n    });\n\n    const customizedUser = { ...user };\n    \n    // Ensure we have a profile\n    customizedUser.profile = options.profile || {};\n    \n    // Add role from options\n    const role = options.role || 'team-member';\n    customizedUser.roles = [role];\n    \n    // Set createdAt if provided, otherwise use current date\n    customizedUser.createdAt = options.createdAt || new Date();\n    \n    console.log('[onCreateUser] Created user:', {\n      id: customizedUser._id,\n      email: customizedUser.emails?.[0]?.address,\n      roles: customizedUser.roles,\n      profile: customizedUser.profile,\n      createdAt: customizedUser.createdAt\n    });\n    \n    return customizedUser;\n  });\n\n  // Publish team members\n  Meteor.publish('teamMembers', function() {\n    console.log('[teamMembers] Publication called, userId:', this.userId);\n    \n    if (!this.userId) {\n      console.log('[teamMembers] No userId, returning ready');\n      return this.ready();\n    }\n\n    try {\n      // Simple query to find all team members\n      const teamMembers = Meteor.users.find(\n        { \n          $or: [\n            { 'roles': 'team-member' },\n            { 'profile.role': 'team-member' }\n          ]\n        },\n        { \n          fields: { \n            emails: 1, \n            roles: 1,\n            'profile.firstName': 1,\n            'profile.lastName': 1,\n            'profile.fullName': 1,\n            createdAt: 1\n          }\n        }\n      );\n\n      console.log('[teamMembers] Publishing team members');\n      return teamMembers;\n    } catch (error) {\n      console.error('[teamMembers] Error in publication:', error);\n      return this.ready();\n    }\n  });\n\n  // Publish user data with roles\n  Meteor.publish('userData', function() {\n    if (!this.userId) {\n      return this.ready();\n    }\n\n    console.log('[userData] Publishing data for user:', this.userId);\n    \n    return Meteor.users.find(\n      { _id: this.userId },\n      { \n        fields: { \n          roles: 1, \n          emails: 1,\n          profile: 1\n        } \n      }\n    );\n  });\n});\n\n// Method to create a new user with role\nMeteor.methods({\n  'users.create'({ email, password, role, adminToken, firstName, lastName }) {\n    // Validate admin token if trying to create admin account\n    if (role === 'admin' && adminToken !== ADMIN_TOKEN) {\n      throw new Meteor.Error('invalid-admin-token', 'Invalid admin token provided');\n    }\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(password)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(password)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(password)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(password)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Create the user\n    try {\n      const userId = Accounts.createUser({\n        email,\n        password,\n        role, // This will be used in onCreateUser callback\n        profile: {\n          role, // Store in profile as well for easy access\n          firstName,\n          lastName,\n          fullName: `${firstName} ${lastName}`\n        }\n      });\n\n      // Send verification email\n      if (userId) {\n        Accounts.sendVerificationEmail(userId);\n      }\n\n      return userId;\n    } catch (error) {\n      throw new Meteor.Error('create-user-failed', error.message);\n    }\n  },\n\n  async 'users.getRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'User must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      if (!user) {\n        throw new Meteor.Error('user-not-found', 'User not found');\n      }\n      \n      // Check both roles array and profile for role\n      const role = user.roles?.[0] || user.profile?.role || 'team-member';\n      return role;\n    } catch (error) {\n      throw new Meteor.Error('get-role-failed', error.message);\n    }\n  },\n\n  'users.resendVerificationEmail'(email) {\n    // Find user by email\n    const user = Accounts.findUserByEmail(email);\n    if (!user) {\n      throw new Meteor.Error('user-not-found', 'No user found with this email address');\n    }\n\n    // Check if email is already verified\n    const userEmail = user.emails[0];\n    if (userEmail.verified) {\n      throw new Meteor.Error('already-verified', 'This email is already verified');\n    }\n\n    // Send verification email\n    try {\n      Accounts.sendVerificationEmail(user._id, email);\n      return true;\n    } catch (error) {\n      throw new Meteor.Error('verification-email-failed', error.message);\n    }\n  },\n\n  async 'users.forgotPassword'(data) {\n    console.log('[forgotPassword] Method called with data:', JSON.stringify(data));\n\n    check(data, {\n      email: String,\n      newPassword: String\n    });\n\n    const { email, newPassword } = data;\n    console.log('[forgotPassword] Processing request for email:', email);\n\n    // Find user by email using async method\n    console.log('[forgotPassword] Searching for user...');\n    let targetUser = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!targetUser) {\n      console.log('[forgotPassword] User not found with direct search, trying case-insensitive...');\n      targetUser = await Meteor.users.findOneAsync({\n        'emails.address': { $regex: new RegExp(`^${email}$`, 'i') }\n      });\n\n      if (!targetUser) {\n        throw new Meteor.Error('user-not-found', 'No user found with this email address');\n      }\n\n      console.log('[forgotPassword] User found with case-insensitive search');\n    } else {\n      console.log('[forgotPassword] User found with direct search');\n    }\n\n    // Ensure we have a valid user with ID\n    if (!targetUser || !targetUser._id) {\n      throw new Meteor.Error('user-invalid', 'Found user but missing ID');\n    }\n\n    console.log('[forgotPassword] Final user ID:', targetUser._id);\n    console.log('[forgotPassword] Final user ID type:', typeof targetUser._id);\n\n    // Validate password requirements\n    const passwordRegex = {\n      length: /.{8,}/,\n      uppercase: /[A-Z]/,\n      number: /[0-9]/,\n      special: /[!@#$%^&*]/\n    };\n\n    const passwordErrors = [];\n    if (!passwordRegex.length.test(newPassword)) {\n      passwordErrors.push('Password must be at least 8 characters long');\n    }\n    if (!passwordRegex.uppercase.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one uppercase letter');\n    }\n    if (!passwordRegex.number.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one number');\n    }\n    if (!passwordRegex.special.test(newPassword)) {\n      passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');\n    }\n\n    if (passwordErrors.length > 0) {\n      throw new Meteor.Error('invalid-password', passwordErrors.join(', '));\n    }\n\n    // Comprehensive password update with debugging\n    try {\n      console.log('[forgotPassword] Starting password update...');\n\n      // First, check current user document\n      console.log('[forgotPassword] Checking current user document...');\n      const currentUser = await Meteor.users.findOneAsync(targetUser._id);\n      console.log('[forgotPassword] Current user document:', JSON.stringify(currentUser, null, 2));\n\n      if (!currentUser) {\n        throw new Meteor.Error('user-not-found', 'User document not found during update');\n      }\n\n      // Use Meteor's built-in password setting method for proper compatibility\n      console.log('[forgotPassword] Using Accounts.setPasswordAsync for proper password hashing...');\n      try {\n        await Accounts.setPasswordAsync(targetUser._id, newPassword, { logout: false });\n        console.log('[forgotPassword] Password set successfully using Accounts.setPasswordAsync');\n\n        // Also verify the email during password reset\n        const emailUpdateResult = await Meteor.users.updateAsync(targetUser._id, {\n          $set: {\n            'emails.0.verified': true  // Automatically verify email during password reset\n          }\n        });\n        console.log('[forgotPassword] Email verification update result:', emailUpdateResult);\n\n        successMethod = 'Accounts.setPasswordAsync + email verification';\n        updateResult = 1; // Mark as successful\n\n      } catch (setPasswordError) {\n        console.error('[forgotPassword] Accounts.setPasswordAsync error:', setPasswordError);\n        throw new Meteor.Error('password-update-failed', 'Failed to update password using Meteor accounts system');\n      }\n\n      // Verify the password was set successfully\n      console.log(`[forgotPassword] Password update successful using: ${successMethod}`);\n\n      // Get updated user to verify the changes\n      const updatedUser = await Meteor.users.findOneAsync(targetUser._id);\n      console.log('[forgotPassword] Updated user services:', JSON.stringify(updatedUser.services, null, 2));\n\n      return { success: true, message: 'Password updated successfully using Meteor accounts system' };\n\n    } catch (error) {\n      console.error('[forgotPassword] Error during password update:', error);\n      throw new Meteor.Error('password-update-failed', `Failed to update password: ${error.message}`);\n    }\n  },\n\n  async 'users.debugUser'({ email }) {\n    try {\n      check(email, String);\n\n      console.log('[debugUser] Debugging user:', email);\n\n      // Find user using async method\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        console.log('[debugUser] User not found');\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[debugUser] User found:', user._id);\n\n      // Get full user document using async method\n      const fullUser = await Meteor.users.findOneAsync(user._id);\n      console.log('[debugUser] Full user document:', JSON.stringify(fullUser, null, 2));\n\n      if (!fullUser) {\n        console.log('[debugUser] Full user document not found');\n        return { success: false, error: 'Full user document not found' };\n      }\n\n      // Test basic update using async method\n      let testUpdateResult = null;\n      try {\n        testUpdateResult = await Meteor.users.updateAsync(user._id, {\n          $set: { 'profile.debugTest': new Date() }\n        });\n        console.log('[debugUser] Test update result:', testUpdateResult);\n      } catch (updateError) {\n        console.error('[debugUser] Test update error:', updateError);\n      }\n\n      // Try using Accounts.setPassword if available\n      let hasSetPassword = false;\n      try {\n        hasSetPassword = typeof Accounts.setPassword === 'function';\n        console.log('[debugUser] Accounts.setPassword available:', hasSetPassword);\n      } catch (setPasswordError) {\n        console.error('[debugUser] Accounts.setPassword check error:', setPasswordError);\n      }\n\n      const result = {\n        success: true,\n        userId: user._id,\n        userIdType: typeof user._id,\n        hasServices: !!fullUser.services,\n        hasPassword: !!(fullUser.services?.password),\n        hasBcrypt: !!(fullUser.services?.password?.bcrypt),\n        roles: fullUser.roles || [],\n        profile: fullUser.profile || {},\n        testUpdateResult: testUpdateResult,\n        hasSetPassword: hasSetPassword,\n        servicesStructure: fullUser.services || {}\n      };\n\n      console.log('[debugUser] Debug result:', JSON.stringify(result, null, 2));\n      return result;\n\n    } catch (error) {\n      console.error('[debugUser] Error in debug method:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.testLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[testLogin] Testing login for email:', email);\n\n    // Find user using async method\n    const user = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!user) {\n      console.log('[testLogin] User not found');\n      return { success: false, error: 'User not found' };\n    }\n\n    console.log('[testLogin] User found:', user._id);\n    console.log('[testLogin] User services:', JSON.stringify(user.services, null, 2));\n\n    // Test password verification\n    try {\n      // Check if password service exists\n      if (!user.services || !user.services.password || !user.services.password.bcrypt) {\n        console.log('[testLogin] No password hash found in user services');\n        return {\n          success: false,\n          error: 'No password hash found',\n          userId: user._id,\n          services: user.services\n        };\n      }\n\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services.password.bcrypt;\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n\n      console.log('[testLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n      console.log('[testLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n      console.log('[testLogin] Password length:', password.length);\n\n      return {\n        success: passwordMatch,\n        userId: user._id,\n        hashPreview: storedHash.substring(0, 20) + '...',\n        passwordLength: password.length\n      };\n    } catch (error) {\n      console.error('[testLogin] Error during password test:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.comparePasswordFormats'({ email }) {\n    check(email, String);\n\n    console.log('[comparePasswordFormats] Checking password format for:', email);\n\n    // Find user\n    const user = await Meteor.users.findOneAsync({\n      'emails.address': email\n    });\n\n    if (!user) {\n      return { success: false, error: 'User not found' };\n    }\n\n    console.log('[comparePasswordFormats] User services structure:', JSON.stringify(user.services, null, 2));\n\n    // Check if user has password\n    if (!user.services?.password?.bcrypt) {\n      return { success: false, error: 'No password found' };\n    }\n\n    const storedHash = user.services.password.bcrypt;\n    console.log('[comparePasswordFormats] Stored hash:', storedHash);\n    console.log('[comparePasswordFormats] Hash length:', storedHash.length);\n    console.log('[comparePasswordFormats] Hash starts with:', storedHash.substring(0, 10));\n\n    // Check if it's a bcrypt hash (should start with $2a$, $2b$, or $2y$)\n    const isBcrypt = /^\\$2[aby]\\$/.test(storedHash);\n    console.log('[comparePasswordFormats] Is bcrypt format:', isBcrypt);\n\n    return {\n      success: true,\n      userId: user._id,\n      hashLength: storedHash.length,\n      hashPreview: storedHash.substring(0, 20) + '...',\n      isBcryptFormat: isBcrypt,\n      fullServices: user.services\n    };\n  },\n\n  async 'users.testActualLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[testActualLogin] Testing actual login for:', email);\n\n    try {\n      // Try to simulate what Meteor.loginWithPassword does\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        console.log('[testActualLogin] User not found');\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[testActualLogin] User found:', user._id);\n      console.log('[testActualLogin] User services:', JSON.stringify(user.services, null, 2));\n\n      // Check if password service exists\n      if (!user.services?.password?.bcrypt) {\n        console.log('[testActualLogin] No password hash found');\n        return { success: false, error: 'No password hash found' };\n      }\n\n      // Test bcrypt verification\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services.password.bcrypt;\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n\n      console.log('[testActualLogin] Password verification:', passwordMatch ? 'PASS' : 'FAIL');\n      console.log('[testActualLogin] Stored hash:', storedHash.substring(0, 20) + '...');\n\n      // Check if user has any login restrictions\n      const isVerified = user.emails?.[0]?.verified || false;\n      console.log('[testActualLogin] Email verified:', isVerified);\n\n      // Check user roles\n      console.log('[testActualLogin] User roles:', user.roles);\n\n      // Try to create a login token manually to test if that works\n      let loginTokenTest = null;\n      try {\n        // This is what Meteor does internally for login\n        const stampedToken = Accounts._generateStampedLoginToken();\n        console.log('[testActualLogin] Generated login token:', !!stampedToken);\n        loginTokenTest = 'Token generation successful';\n      } catch (tokenError) {\n        console.error('[testActualLogin] Token generation error:', tokenError);\n        loginTokenTest = tokenError.message;\n      }\n\n      return {\n        success: passwordMatch,\n        userId: user._id,\n        passwordVerification: passwordMatch,\n        emailVerified: isVerified,\n        userRoles: user.roles,\n        hashPreview: storedHash.substring(0, 20) + '...',\n        loginTokenTest: loginTokenTest,\n        fullUserStructure: {\n          _id: user._id,\n          emails: user.emails,\n          services: user.services,\n          roles: user.roles,\n          profile: user.profile\n        }\n      };\n\n    } catch (error) {\n      console.error('[testActualLogin] Error:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.simulateLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[simulateLogin] Simulating login for:', email);\n\n    try {\n      // Find user\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[simulateLogin] User found:', user._id);\n\n      // Check password\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services?.password?.bcrypt;\n\n      if (!storedHash) {\n        return { success: false, error: 'No password hash found' };\n      }\n\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n      console.log('[simulateLogin] Password match:', passwordMatch);\n\n      if (!passwordMatch) {\n        return { success: false, error: 'Invalid password' };\n      }\n\n      // Check if there are any login restrictions\n      const restrictions = [];\n\n      // Check email verification requirement - DISABLED for testing\n      const emailVerified = user.emails?.[0]?.verified || false;\n      // if (!emailVerified) {\n      //   restrictions.push('Email not verified');\n      // }\n\n      // Check if user is active (no disabled flag)\n      if (user.disabled) {\n        restrictions.push('User account disabled');\n      }\n\n      // Check roles\n      if (!user.roles || user.roles.length === 0) {\n        restrictions.push('No roles assigned');\n      }\n\n      console.log('[simulateLogin] Login restrictions:', restrictions);\n\n      // Try to manually create what loginWithPassword would do\n      let loginSimulation = 'Not attempted';\n      try {\n        // Check if we can generate a login token\n        const stampedToken = Accounts._generateStampedLoginToken();\n        if (stampedToken) {\n          loginSimulation = 'Login token generation successful';\n        }\n      } catch (tokenError) {\n        loginSimulation = `Token error: ${tokenError.message}`;\n      }\n\n      return {\n        success: passwordMatch && restrictions.length === 0,\n        userId: user._id,\n        passwordMatch: passwordMatch,\n        emailVerified: emailVerified,\n        restrictions: restrictions,\n        loginSimulation: loginSimulation,\n        userStructure: {\n          _id: user._id,\n          emails: user.emails,\n          roles: user.roles,\n          profile: user.profile,\n          disabled: user.disabled || false\n        }\n      };\n\n    } catch (error) {\n      console.error('[simulateLogin] Error:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.testServerLogin'({ email, password }) {\n    check(email, String);\n    check(password, String);\n\n    console.log('[testServerLogin] Testing server-side login for:', email);\n\n    try {\n      // Try to use Meteor's internal login method\n      const user = await Meteor.users.findOneAsync({\n        'emails.address': email\n      });\n\n      if (!user) {\n        return { success: false, error: 'User not found' };\n      }\n\n      console.log('[testServerLogin] User found:', user._id);\n\n      // Check if password matches\n      const bcrypt = require('bcrypt');\n      const storedHash = user.services?.password?.bcrypt;\n\n      if (!storedHash) {\n        return { success: false, error: 'No password hash found' };\n      }\n\n      const passwordMatch = bcrypt.compareSync(password, storedHash);\n      console.log('[testServerLogin] Password match:', passwordMatch);\n\n      if (!passwordMatch) {\n        return { success: false, error: 'Invalid password' };\n      }\n\n      // Try to create a login token manually\n      try {\n        const stampedToken = Accounts._generateStampedLoginToken();\n        console.log('[testServerLogin] Login token generated:', !!stampedToken);\n\n        // Try to add the token to the user\n        await Meteor.users.updateAsync(user._id, {\n          $push: {\n            'services.resume.loginTokens': stampedToken\n          }\n        });\n\n        console.log('[testServerLogin] Login token added to user');\n\n        return {\n          success: true,\n          userId: user._id,\n          token: stampedToken.token,\n          message: 'Server-side login simulation successful'\n        };\n\n      } catch (tokenError) {\n        console.error('[testServerLogin] Token error:', tokenError);\n        return { success: false, error: `Token error: ${tokenError.message}` };\n      }\n\n    } catch (error) {\n      console.error('[testServerLogin] Error:', error);\n      return { success: false, error: error.message };\n    }\n  },\n\n  async 'users.checkAndFixAdminRole'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n    \n    try {\n      const user = await Meteor.users.findOneAsync(this.userId);\n      console.log('[checkAndFixAdminRole] Checking user:', {\n        id: user?._id,\n        email: user?.emails?.[0]?.address,\n        roles: user?.roles\n      });\n      \n      // If user has no roles array, initialize it\n      if (!user.roles) {\n        await Meteor.users.updateAsync(this.userId, {\n          $set: { roles: ['team-member'] }\n        });\n        return 'Roles initialized';\n      }\n      \n      // If user has no roles or doesn't have admin role\n      if (!user.roles.includes('admin')) {\n        console.log('[checkAndFixAdminRole] User is not admin, checking if first user');\n        \n        // Check if this is the first user (they should be admin)\n        const totalUsers = await Meteor.users.find().countAsync();\n        if (totalUsers === 1) {\n          console.log('[checkAndFixAdminRole] First user, setting as admin');\n          await Meteor.users.updateAsync(this.userId, {\n            $set: { roles: ['admin'] }\n          });\n          return 'Admin role added';\n        }\n        return 'User is not admin';\n      }\n      \n      return 'User is already admin';\n    } catch (error) {\n      console.error('[checkAndFixAdminRole] Error:', error);\n      throw new Meteor.Error('check-role-failed', error.message);\n    }\n  },\n\n  async 'users.diagnoseRoles'() {\n    if (!this.userId) {\n      throw new Meteor.Error('not-authorized', 'You must be logged in');\n    }\n\n    try {\n      const currentUser = await Meteor.users.findOneAsync(this.userId);\n      if (!currentUser.roles?.includes('admin')) {\n        throw new Meteor.Error('not-authorized', 'Only admins can diagnose roles');\n      }\n\n      const allUsers = await Meteor.users.find().fetchAsync();\n      const usersWithIssues = [];\n      const fixes = [];\n\n      for (const user of allUsers) {\n        const issues = [];\n        \n        // Check if roles array exists\n        if (!user.roles || !Array.isArray(user.roles)) {\n          issues.push('No roles array');\n          // Fix: Initialize roles based on profile\n          const role = user.profile?.role || 'team-member';\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [role] }\n          });\n          fixes.push(`Initialized roles for ${user.emails?.[0]?.address}`);\n        }\n        \n        // Check if role matches profile\n        if (user.profile?.role && user.roles?.[0] !== user.profile.role) {\n          issues.push('Role mismatch with profile');\n          // Fix: Update roles to match profile\n          await Meteor.users.updateAsync(user._id, {\n            $set: { roles: [user.profile.role] }\n          });\n          fixes.push(`Fixed role mismatch for ${user.emails?.[0]?.address}`);\n        }\n\n        if (issues.length > 0) {\n          usersWithIssues.push({\n            email: user.emails?.[0]?.address,\n            issues\n          });\n        }\n      }\n\n      return {\n        usersWithIssues,\n        fixes,\n        message: fixes.length > 0 ? 'Fixed role issues' : 'No issues found'\n      };\n    } catch (error) {\n      throw new Meteor.Error('diagnose-failed', error.message);\n    }\n  },\n\n  'users.createTestTeamMember'() {\n    // Only allow in development\n    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {\n      try {\n        const testMember = {\n          email: '<EMAIL>',\n          password: 'TestPass123!',\n          firstName: 'Test',\n          lastName: 'Member'\n        };\n\n        const userId = Accounts.createUser({\n          email: testMember.email,\n          password: testMember.password,\n          profile: {\n            firstName: testMember.firstName,\n            lastName: testMember.lastName,\n            role: 'team-member',\n            fullName: `${testMember.firstName} ${testMember.lastName}`\n          }\n        });\n\n        // Set the role explicitly\n        Meteor.users.update(userId, {\n          $set: { roles: ['team-member'] }\n        });\n\n        return {\n          success: true,\n          userId,\n          message: 'Test team member created successfully'\n        };\n      } catch (error) {\n        console.error('[createTestTeamMember] Error:', error);\n        throw new Meteor.Error('create-test-member-failed', error.message);\n      }\n    } else {\n      throw new Meteor.Error('not-development', 'This method is only available in development');\n    }\n  }\n});"], "mappings": ";;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArG,IAAIC,MAAM;IAACJ,MAAM,CAACC,IAAI,CAAC,eAAe,EAAC;MAACG,MAAMA,CAACD,CAAC,EAAC;QAACC,MAAM,GAACD,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIE,eAAe;IAACL,MAAM,CAACC,IAAI,CAAC,oBAAoB,EAAC;MAACI,eAAeA,CAACF,CAAC,EAAC;QAACE,eAAe,GAACF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIG,QAAQ;IAACN,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACK,QAAQA,CAACH,CAAC,EAAC;QAACG,QAAQ,GAACH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,KAAK;IAACP,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACM,KAAKA,CAACJ,CAAC,EAAC;QAACI,KAAK,GAACJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIK,KAAK;IAACR,MAAM,CAACC,IAAI,CAAC,oBAAoB,EAAC;MAACO,KAAKA,CAACL,CAAC,EAAC;QAACK,KAAK,GAACL,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIM,KAAK;IAACT,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAC;MAACQ,KAAKA,CAACN,CAAC,EAAC;QAACM,KAAK,GAACN,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIO,KAAK;IAACV,MAAM,CAACC,IAAI,CAAC,cAAc,EAAC;MAACS,KAAKA,CAACP,CAAC,EAAC;QAACO,KAAK,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,MAAM;IAACX,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACQ,MAAM,GAACR,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIS,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IASlmB,eAAeC,UAAUA,CAAAC,IAAA,EAAiB;MAAA,IAAhB;QAAEC,KAAK;QAAEC;MAAI,CAAC,GAAAF,IAAA;MACtC,MAAMT,eAAe,CAACY,WAAW,CAAC;QAAEF,KAAK;QAAEC,GAAG;QAAEE,SAAS,EAAE,IAAIC,IAAI,CAAC;MAAE,CAAC,CAAC;IAC1E;IAEA,MAAMC,WAAW,GAAG,QAAQ;IAE5BhB,MAAM,CAACiB,OAAO,CAAC,YAAY;MAAA,IAAAC,qBAAA;MACzB;MACA,IAAI;QACF,MAAMd,KAAK,CAACe,WAAW,CAAC;UAAEL,SAAS,EAAE;QAAE,CAAC,CAAC;QACzC,MAAMV,KAAK,CAACe,WAAW,CAAC;UAAEC,UAAU,EAAE;QAAE,CAAC,CAAC;QAC1C,MAAMhB,KAAK,CAACe,WAAW,CAAC;UAAEE,SAAS,EAAE;QAAE,CAAC,CAAC;;QAEzC;QACA;QACA,MAAMrB,MAAM,CAACsB,KAAK,CAACH,WAAW,CAAC;UAAEI,KAAK,EAAE;QAAE,CAAC,EAAE;UAAEC,UAAU,EAAE;QAAK,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mCAAmC,EAAEF,KAAK,CAACG,OAAO,CAAC;MAClE;;MAEA;MACA,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM7B,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;QACvDL,OAAO,CAACM,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAACI,GAAG,CAACC,IAAI;UAAA,IAAAC,YAAA,EAAAC,aAAA;UAAA,OAAK;YACxDC,EAAE,EAAEH,IAAI,CAACI,GAAG;YACZC,KAAK,GAAAJ,YAAA,GAAED,IAAI,CAACM,MAAM,cAAAL,YAAA,wBAAAC,aAAA,GAAXD,YAAA,CAAc,CAAC,CAAC,cAAAC,aAAA,uBAAhBA,aAAA,CAAkBK,OAAO;YAChClB,KAAK,EAAEW,IAAI,CAACX,KAAK;YACjBmB,OAAO,EAAER,IAAI,CAACQ;UAChB,CAAC;QAAA,CAAC,CAAC,CAAC;;QAEJ;QACA,KAAK,MAAMR,IAAI,IAAIL,QAAQ,EAAE;UAC3B,MAAMc,OAAO,GAAG,CAAC,CAAC;UAElB,IAAI,CAACT,IAAI,CAACX,KAAK,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACX,IAAI,CAACX,KAAK,CAAC,EAAE;YAC7CoB,OAAO,CAACpB,KAAK,GAAG,CAAC,aAAa,CAAC;UACjC;UAEA,IAAI,CAACW,IAAI,CAACpB,SAAS,EAAE;YACnB6B,OAAO,CAAC7B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;UAChC;UAEA,IAAI+B,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;YAAA,IAAAC,aAAA,EAAAC,cAAA;YACnCxB,OAAO,CAACM,GAAG,CAAC,2CAA2C,GAAAiB,aAAA,GAAEf,IAAI,CAACM,MAAM,cAAAS,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBT,OAAO,CAAC;YACnF,MAAMzC,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;cACvCc,IAAI,EAAET;YACR,CAAC,CAAC;UACJ;QACF;QAEA,MAAMU,gBAAgB,GAAG,MAAMrD,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC;UAAE,OAAO,EAAE;QAAc,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC;QACzF5B,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEqB,gBAAgB,CAAC;;QAE9D;QACA,IAAIA,gBAAgB,KAAK,CAAC,EAAE;UAC1B3B,OAAO,CAACM,GAAG,CAAC,sCAAsC,CAAC;UACnD,IAAI;YACF;YACA,MAAMuB,WAAW,GAAG,CAClB;cACEhB,KAAK,EAAE,mBAAmB;cAC1BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,EACD;cACEnB,KAAK,EAAE,mBAAmB;cAC1BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC,CACF;YAED,KAAK,MAAMC,MAAM,IAAIJ,WAAW,EAAE;cAChC,MAAMK,MAAM,GAAG,MAAM1D,QAAQ,CAAC2D,eAAe,CAAC;gBAC5CtB,KAAK,EAAEoB,MAAM,CAACpB,KAAK;gBACnBiB,QAAQ,EAAEG,MAAM,CAACH,QAAQ;gBACzBM,IAAI,EAAE,aAAa;gBACnBhD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;gBACrB2B,OAAO,EAAE;kBACPe,SAAS,EAAEE,MAAM,CAACF,SAAS;kBAC3BC,QAAQ,EAAEC,MAAM,CAACD,QAAQ;kBACzBI,IAAI,EAAE,aAAa;kBACnBC,QAAQ,KAAAC,MAAA,CAAKL,MAAM,CAACF,SAAS,OAAAO,MAAA,CAAIL,MAAM,CAACD,QAAQ;gBAClD;cACF,CAAC,CAAC;;cAEF;cACA,MAAM1D,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACS,MAAM,EAAE;gBACrCR,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAAC,aAAa;gBAAE;cACjC,CAAC,CAAC;cAEFG,OAAO,CAACM,GAAG,CAAC,qCAAqC,EAAE;gBACjDK,EAAE,EAAEuB,MAAM;gBACVrB,KAAK,EAAEoB,MAAM,CAACpB,KAAK;gBACnB0B,IAAI,KAAAD,MAAA,CAAKL,MAAM,CAACF,SAAS,OAAAO,MAAA,CAAIL,MAAM,CAACD,QAAQ;cAC9C,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACrE;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;;MAEA;MACA,MAAMyC,aAAa,IAAAhD,qBAAA,GAAGlB,MAAM,CAACmE,QAAQ,CAACC,OAAO,cAAAlD,qBAAA,uBAAvBA,qBAAA,CAAyBqB,KAAK;;MAEpD;MACA,IAAI2B,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,IAAIH,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEV,QAAQ,EAAE;QACtDc,OAAO,CAACC,GAAG,CAACC,QAAQ,aAAAR,MAAA,CAAaS,kBAAkB,CAACP,aAAa,CAACG,QAAQ,CAAC,OAAAL,MAAA,CAAIS,kBAAkB,CAACP,aAAa,CAACV,QAAQ,CAAC,OAAAQ,MAAA,CAAIE,aAAa,CAACQ,MAAM,OAAAV,MAAA,CAAIE,aAAa,CAACS,IAAI,CAAE;;QAEzK;QACA,IAAI;UACFjD,OAAO,CAACM,GAAG,CAAC,gCAAgC,CAAC;UAC7C7B,KAAK,CAACyE,IAAI,CAAC;YACTC,EAAE,EAAEX,aAAa,CAACG,QAAQ;YAC1BS,IAAI,EAAEZ,aAAa,CAACG,QAAQ;YAC5BU,OAAO,EAAE,YAAY;YACrBC,IAAI,EAAE;UACR,CAAC,CAAC;UACFtD,OAAO,CAACM,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,CAAC,OAAOP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAC;MACjE;;MAEA;MACAzB,QAAQ,CAAC+E,MAAM,CAAC;QACdC,qBAAqB,EAAE,KAAK;QAAG;QAC/BC,2BAA2B,EAAE;MAC/B,CAAC,CAAC;;MAEF;MACAjF,QAAQ,CAACkF,oBAAoB,CAAEC,OAAO,IAAK;QAAA,IAAAC,cAAA,EAAAC,aAAA;QACzC7D,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAE;UACnDwD,IAAI,EAAEH,OAAO,CAACG,IAAI;UAClBC,OAAO,EAAEJ,OAAO,CAACI,OAAO;UACxBhE,KAAK,GAAA6D,cAAA,GAAED,OAAO,CAAC5D,KAAK,cAAA6D,cAAA,uBAAbA,cAAA,CAAe1D,OAAO;UAC7BgC,MAAM,GAAA2B,aAAA,GAAEF,OAAO,CAACnD,IAAI,cAAAqD,aAAA,uBAAZA,aAAA,CAAcjD,GAAG;UACzBoD,UAAU,EAAEL,OAAO,CAACK;QACtB,CAAC,CAAC;;QAEF;QACA,IAAIL,OAAO,CAACnD,IAAI,IAAImD,OAAO,CAACG,IAAI,KAAK,UAAU,EAAE;UAC/C9D,OAAO,CAACM,GAAG,CAAC,0DAA0D,EAAEqD,OAAO,CAACnD,IAAI,CAACI,GAAG,CAAC;UACzF,OAAO,IAAI;QACb;;QAEA;QACA,OAAO,IAAI;MACb,CAAC,CAAC;;MAEF;MACApC,QAAQ,CAACyF,cAAc,CAACC,QAAQ,GAAG,wBAAwB;MAC3D1F,QAAQ,CAACyF,cAAc,CAACb,IAAI,GAAGZ,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEG,QAAQ,8BAAAL,MAAA,CACzBE,aAAa,CAACG,QAAQ,SACjD,8CAA8C;MAEhDnE,QAAQ,CAACyF,cAAc,CAACE,WAAW,GAAG;QACpCd,OAAOA,CAAA,EAAG;UACR,OAAO,2BAA2B;QACpC,CAAC;QACDC,IAAIA,CAAC9C,IAAI,EAAEtB,GAAG,EAAE;UACd,MAAMkF,YAAY,GAAG5D,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO;UAC3C,OAAO,gDAAAuB,MAAA,CAC8B8B,YAAY,wCAAqC,MAAA9B,MAAA,CAC/EpD,GAAG,SAAM,4EAC6D,cAC9D,qCACuB;QACxC,CAAC;QACDmF,IAAIA,CAAC7D,IAAI,EAAEtB,GAAG,EAAE;UACd,MAAMkF,YAAY,GAAG5D,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO;UAC3C,sQAAAuB,MAAA,CAKyC8B,YAAY,gHAAA9B,MAAA,CAElCpD,GAAG;QAexB;MACF,CAAC;;MAED;MACA,IAAI,OAAMX,eAAe,CAAC6B,IAAI,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC,MAAK,CAAC,EAAE;QACnD,MAAM7C,UAAU,CAAC;UACfE,KAAK,EAAE,iBAAiB;UACxBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,kBAAkB;UACzBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,eAAe;UACtBC,GAAG,EAAE;QACP,CAAC,CAAC;QAEF,MAAMH,UAAU,CAAC;UACfE,KAAK,EAAE,aAAa;UACpBC,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;;MAEA;MACA;MACAZ,MAAM,CAACgG,OAAO,CAAC,OAAO,EAAE,YAAY;QAClC,OAAO/F,eAAe,CAAC6B,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACA5B,QAAQ,CAAC+F,YAAY,CAAC,CAACC,OAAO,EAAEhE,IAAI,KAAK;QAAA,IAAAiE,qBAAA,EAAAC,sBAAA;QACvC1E,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAE;UACxDO,KAAK,EAAE2D,OAAO,CAAC3D,KAAK;UACpBuB,IAAI,EAAEoC,OAAO,CAACpC,IAAI;UAClBpB,OAAO,EAAEwD,OAAO,CAACxD,OAAO;UACxB5B,SAAS,EAAEoF,OAAO,CAACpF;QACrB,CAAC,CAAC;QAEF,MAAMuF,cAAc,GAAA1G,aAAA,KAAQuC,IAAI,CAAE;;QAElC;QACAmE,cAAc,CAAC3D,OAAO,GAAGwD,OAAO,CAACxD,OAAO,IAAI,CAAC,CAAC;;QAE9C;QACA,MAAMoB,IAAI,GAAGoC,OAAO,CAACpC,IAAI,IAAI,aAAa;QAC1CuC,cAAc,CAAC9E,KAAK,GAAG,CAACuC,IAAI,CAAC;;QAE7B;QACAuC,cAAc,CAACvF,SAAS,GAAGoF,OAAO,CAACpF,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC;QAE1DW,OAAO,CAACM,GAAG,CAAC,8BAA8B,EAAE;UAC1CK,EAAE,EAAEgE,cAAc,CAAC/D,GAAG;UACtBC,KAAK,GAAA4D,qBAAA,GAAEE,cAAc,CAAC7D,MAAM,cAAA2D,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAwB,CAAC,CAAC,cAAAC,sBAAA,uBAA1BA,sBAAA,CAA4B3D,OAAO;UAC1ClB,KAAK,EAAE8E,cAAc,CAAC9E,KAAK;UAC3BmB,OAAO,EAAE2D,cAAc,CAAC3D,OAAO;UAC/B5B,SAAS,EAAEuF,cAAc,CAACvF;QAC5B,CAAC,CAAC;QAEF,OAAOuF,cAAc;MACvB,CAAC,CAAC;;MAEF;MACArG,MAAM,CAACgG,OAAO,CAAC,aAAa,EAAE,YAAW;QACvCtE,OAAO,CAACM,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC4B,MAAM,CAAC;QAErE,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;UAChBlC,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC;UACvD,OAAO,IAAI,CAACsE,KAAK,CAAC,CAAC;QACrB;QAEA,IAAI;UACF;UACA,MAAMC,WAAW,GAAGvG,MAAM,CAACsB,KAAK,CAACQ,IAAI,CACnC;YACE0E,GAAG,EAAE,CACH;cAAE,OAAO,EAAE;YAAc,CAAC,EAC1B;cAAE,cAAc,EAAE;YAAc,CAAC;UAErC,CAAC,EACD;YACEC,MAAM,EAAE;cACNjE,MAAM,EAAE,CAAC;cACTjB,KAAK,EAAE,CAAC;cACR,mBAAmB,EAAE,CAAC;cACtB,kBAAkB,EAAE,CAAC;cACrB,kBAAkB,EAAE,CAAC;cACrBT,SAAS,EAAE;YACb;UACF,CACF,CAAC;UAEDY,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC;UACpD,OAAOuE,WAAW;QACpB,CAAC,CAAC,OAAO9E,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,OAAO,IAAI,CAAC6E,KAAK,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;;MAEF;MACAtG,MAAM,CAACgG,OAAO,CAAC,UAAU,EAAE,YAAW;QACpC,IAAI,CAAC,IAAI,CAACpC,MAAM,EAAE;UAChB,OAAO,IAAI,CAAC0C,KAAK,CAAC,CAAC;QACrB;QAEA5E,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC4B,MAAM,CAAC;QAEhE,OAAO5D,MAAM,CAACsB,KAAK,CAACQ,IAAI,CACtB;UAAEQ,GAAG,EAAE,IAAI,CAACsB;QAAO,CAAC,EACpB;UACE6C,MAAM,EAAE;YACNlF,KAAK,EAAE,CAAC;YACRiB,MAAM,EAAE,CAAC;YACTE,OAAO,EAAE;UACX;QACF,CACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA1C,MAAM,CAAC0G,OAAO,CAAC;MACb,cAAcC,CAAAC,KAAA,EAA6D;QAAA,IAA5D;UAAErE,KAAK;UAAEiB,QAAQ;UAAEM,IAAI;UAAE+C,UAAU;UAAEpD,SAAS;UAAEC;QAAS,CAAC,GAAAkD,KAAA;QACvE;QACA,IAAI9C,IAAI,KAAK,OAAO,IAAI+C,UAAU,KAAK7F,WAAW,EAAE;UAClD,MAAM,IAAIhB,MAAM,CAAC8G,KAAK,CAAC,qBAAqB,EAAE,8BAA8B,CAAC;QAC/E;;QAEA;QACA,MAAMC,aAAa,GAAG;UACpB/D,MAAM,EAAE,OAAO;UACfgE,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAAC/D,MAAM,CAACoE,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UACxC2D,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UAC3C2D,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UACxC2D,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAAC5D,QAAQ,CAAC,EAAE;UACzC2D,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAACnE,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAIhD,MAAM,CAAC8G,KAAK,CAAC,kBAAkB,EAAEK,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF,MAAM1D,MAAM,GAAG1D,QAAQ,CAACqH,UAAU,CAAC;YACjChF,KAAK;YACLiB,QAAQ;YACRM,IAAI;YAAE;YACNpB,OAAO,EAAE;cACPoB,IAAI;cAAE;cACNL,SAAS;cACTC,QAAQ;cACRK,QAAQ,KAAAC,MAAA,CAAKP,SAAS,OAAAO,MAAA,CAAIN,QAAQ;YACpC;UACF,CAAC,CAAC;;UAEF;UACA,IAAIE,MAAM,EAAE;YACV1D,QAAQ,CAACgF,qBAAqB,CAACtB,MAAM,CAAC;UACxC;UAEA,OAAOA,MAAM;QACf,CAAC,CAAC,OAAOnC,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,oBAAoB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC7D;MACF,CAAC;MAED,MAAM,eAAe4F,CAAA,EAAG;QACtB,IAAI,CAAC,IAAI,CAAC5D,MAAM,EAAE;UAChB,MAAM,IAAI5D,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,wBAAwB,CAAC;QACpE;QAEA,IAAI;UAAA,IAAAW,WAAA,EAAAC,aAAA;UACF,MAAMxF,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC,IAAI,CAAC/D,MAAM,CAAC;UACzD,IAAI,CAAC1B,IAAI,EAAE;YACT,MAAM,IAAIlC,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;UAC5D;;UAEA;UACA,MAAMhD,IAAI,GAAG,EAAA2D,WAAA,GAAAvF,IAAI,CAACX,KAAK,cAAAkG,WAAA,uBAAVA,WAAA,CAAa,CAAC,CAAC,OAAAC,aAAA,GAAIxF,IAAI,CAACQ,OAAO,cAAAgF,aAAA,uBAAZA,aAAA,CAAc5D,IAAI,KAAI,aAAa;UACnE,OAAOA,IAAI;QACb,CAAC,CAAC,OAAOrC,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,iBAAiB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,+BAA+BgG,CAACrF,KAAK,EAAE;QACrC;QACA,MAAML,IAAI,GAAGhC,QAAQ,CAAC2H,eAAe,CAACtF,KAAK,CAAC;QAC5C,IAAI,CAACL,IAAI,EAAE;UACT,MAAM,IAAIlC,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;QACnF;;QAEA;QACA,MAAMgB,SAAS,GAAG5F,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;QAChC,IAAIsF,SAAS,CAACC,QAAQ,EAAE;UACtB,MAAM,IAAI/H,MAAM,CAAC8G,KAAK,CAAC,kBAAkB,EAAE,gCAAgC,CAAC;QAC9E;;QAEA;QACA,IAAI;UACF5G,QAAQ,CAACgF,qBAAqB,CAAChD,IAAI,CAACI,GAAG,EAAEC,KAAK,CAAC;UAC/C,OAAO,IAAI;QACb,CAAC,CAAC,OAAOd,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,2BAA2B,EAAErF,KAAK,CAACG,OAAO,CAAC;QACpE;MACF,CAAC;MAED,MAAM,sBAAsBoG,CAACC,IAAI,EAAE;QACjCvG,OAAO,CAACM,GAAG,CAAC,2CAA2C,EAAEkG,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC;QAE9E3H,KAAK,CAAC2H,IAAI,EAAE;UACV1F,KAAK,EAAE6F,MAAM;UACbC,WAAW,EAAED;QACf,CAAC,CAAC;QAEF,MAAM;UAAE7F,KAAK;UAAE8F;QAAY,CAAC,GAAGJ,IAAI;QACnCvG,OAAO,CAACM,GAAG,CAAC,gDAAgD,EAAEO,KAAK,CAAC;;QAEpE;QACAb,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;QACrD,IAAIsG,UAAU,GAAG,MAAMtI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;UAC/C,gBAAgB,EAAEpF;QACpB,CAAC,CAAC;QAEF,IAAI,CAAC+F,UAAU,EAAE;UACf5G,OAAO,CAACM,GAAG,CAAC,gFAAgF,CAAC;UAC7FsG,UAAU,GAAG,MAAMtI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAE;cAAEY,MAAM,EAAE,IAAIC,MAAM,KAAAxE,MAAA,CAAKzB,KAAK,QAAK,GAAG;YAAE;UAC5D,CAAC,CAAC;UAEF,IAAI,CAAC+F,UAAU,EAAE;YACf,MAAM,IAAItI,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;UACnF;UAEApF,OAAO,CAACM,GAAG,CAAC,0DAA0D,CAAC;QACzE,CAAC,MAAM;UACLN,OAAO,CAACM,GAAG,CAAC,gDAAgD,CAAC;QAC/D;;QAEA;QACA,IAAI,CAACsG,UAAU,IAAI,CAACA,UAAU,CAAChG,GAAG,EAAE;UAClC,MAAM,IAAItC,MAAM,CAAC8G,KAAK,CAAC,cAAc,EAAE,2BAA2B,CAAC;QACrE;QAEApF,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEsG,UAAU,CAAChG,GAAG,CAAC;QAC9DZ,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAE,OAAOsG,UAAU,CAAChG,GAAG,CAAC;;QAE1E;QACA,MAAMyE,aAAa,GAAG;UACpB/D,MAAM,EAAE,OAAO;UACfgE,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,cAAc,GAAG,EAAE;QACzB,IAAI,CAACJ,aAAa,CAAC/D,MAAM,CAACoE,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC3ClB,cAAc,CAACE,IAAI,CAAC,6CAA6C,CAAC;QACpE;QACA,IAAI,CAACN,aAAa,CAACC,SAAS,CAACI,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC9ClB,cAAc,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC5E;QACA,IAAI,CAACN,aAAa,CAACE,MAAM,CAACG,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC3ClB,cAAc,CAACE,IAAI,CAAC,2CAA2C,CAAC;QAClE;QACA,IAAI,CAACN,aAAa,CAACG,OAAO,CAACE,IAAI,CAACiB,WAAW,CAAC,EAAE;UAC5ClB,cAAc,CAACE,IAAI,CAAC,iEAAiE,CAAC;QACxF;QAEA,IAAIF,cAAc,CAACnE,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM,IAAIhD,MAAM,CAAC8G,KAAK,CAAC,kBAAkB,EAAEK,cAAc,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI;UACF5F,OAAO,CAACM,GAAG,CAAC,8CAA8C,CAAC;;UAE3D;UACAN,OAAO,CAACM,GAAG,CAAC,oDAAoD,CAAC;UACjE,MAAMyG,WAAW,GAAG,MAAMzI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAACW,UAAU,CAAChG,GAAG,CAAC;UACnEZ,OAAO,CAACM,GAAG,CAAC,yCAAyC,EAAEkG,IAAI,CAACC,SAAS,CAACM,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAE5F,IAAI,CAACA,WAAW,EAAE;YAChB,MAAM,IAAIzI,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;UACnF;;UAEA;UACApF,OAAO,CAACM,GAAG,CAAC,iFAAiF,CAAC;UAC9F,IAAI;YACF,MAAM9B,QAAQ,CAACwI,gBAAgB,CAACJ,UAAU,CAAChG,GAAG,EAAE+F,WAAW,EAAE;cAAEM,MAAM,EAAE;YAAM,CAAC,CAAC;YAC/EjH,OAAO,CAACM,GAAG,CAAC,4EAA4E,CAAC;;YAEzF;YACA,MAAM4G,iBAAiB,GAAG,MAAM5I,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACmF,UAAU,CAAChG,GAAG,EAAE;cACvEc,IAAI,EAAE;gBACJ,mBAAmB,EAAE,IAAI,CAAE;cAC7B;YACF,CAAC,CAAC;YACF1B,OAAO,CAACM,GAAG,CAAC,oDAAoD,EAAE4G,iBAAiB,CAAC;YAEpFC,aAAa,GAAG,gDAAgD;YAChEC,YAAY,GAAG,CAAC,CAAC,CAAC;UAEpB,CAAC,CAAC,OAAOC,gBAAgB,EAAE;YACzBrH,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEsH,gBAAgB,CAAC;YACpF,MAAM,IAAI/I,MAAM,CAAC8G,KAAK,CAAC,wBAAwB,EAAE,wDAAwD,CAAC;UAC5G;;UAEA;UACApF,OAAO,CAACM,GAAG,uDAAAgC,MAAA,CAAuD6E,aAAa,CAAE,CAAC;;UAElF;UACA,MAAMG,WAAW,GAAG,MAAMhJ,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAACW,UAAU,CAAChG,GAAG,CAAC;UACnEZ,OAAO,CAACM,GAAG,CAAC,yCAAyC,EAAEkG,IAAI,CAACC,SAAS,CAACa,WAAW,CAACC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAErG,OAAO;YAAEC,OAAO,EAAE,IAAI;YAAEtH,OAAO,EAAE;UAA6D,CAAC;QAEjG,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;UACtE,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,wBAAwB,gCAAA9C,MAAA,CAAgCvC,KAAK,CAACG,OAAO,CAAE,CAAC;QACjG;MACF,CAAC;MAED,MAAM,iBAAiBuH,CAAAC,KAAA,EAAY;QAAA,IAAX;UAAE7G;QAAM,CAAC,GAAA6G,KAAA;QAC/B,IAAI;UAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA;UACFjJ,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;UAEpB1G,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEO,KAAK,CAAC;;UAEjD;UACA,MAAML,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAEpF;UACpB,CAAC,CAAC;UAEF,IAAI,CAACL,IAAI,EAAE;YACTR,OAAO,CAACM,GAAG,CAAC,4BAA4B,CAAC;YACzC,OAAO;cAAEkH,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAC,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEE,IAAI,CAACI,GAAG,CAAC;;UAEhD;UACA,MAAMkH,QAAQ,GAAG,MAAMxJ,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAACzF,IAAI,CAACI,GAAG,CAAC;UAC1DZ,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEkG,IAAI,CAACC,SAAS,CAACqB,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEjF,IAAI,CAACA,QAAQ,EAAE;YACb9H,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC;YACvD,OAAO;cAAEkH,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAA+B,CAAC;UAClE;;UAEA;UACA,IAAIgI,gBAAgB,GAAG,IAAI;UAC3B,IAAI;YACFA,gBAAgB,GAAG,MAAMzJ,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;cAC1Dc,IAAI,EAAE;gBAAE,mBAAmB,EAAE,IAAIrC,IAAI,CAAC;cAAE;YAC1C,CAAC,CAAC;YACFW,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEyH,gBAAgB,CAAC;UAClE,CAAC,CAAC,OAAOC,WAAW,EAAE;YACpBhI,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEiI,WAAW,CAAC;UAC9D;;UAEA;UACA,IAAIC,cAAc,GAAG,KAAK;UAC1B,IAAI;YACFA,cAAc,GAAG,OAAOzJ,QAAQ,CAAC0J,WAAW,KAAK,UAAU;YAC3DlI,OAAO,CAACM,GAAG,CAAC,6CAA6C,EAAE2H,cAAc,CAAC;UAC5E,CAAC,CAAC,OAAOZ,gBAAgB,EAAE;YACzBrH,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEsH,gBAAgB,CAAC;UAClF;UAEA,MAAMc,MAAM,GAAG;YACbX,OAAO,EAAE,IAAI;YACbtF,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChBwH,UAAU,EAAE,OAAO5H,IAAI,CAACI,GAAG;YAC3ByH,WAAW,EAAE,CAAC,CAACP,QAAQ,CAACP,QAAQ;YAChCe,WAAW,EAAE,CAAC,GAAAX,kBAAA,GAAEG,QAAQ,CAACP,QAAQ,cAAAI,kBAAA,eAAjBA,kBAAA,CAAmB7F,QAAQ,CAAC;YAC5CyG,SAAS,EAAE,CAAC,GAAAX,mBAAA,GAAEE,QAAQ,CAACP,QAAQ,cAAAK,mBAAA,gBAAAC,qBAAA,GAAjBD,mBAAA,CAAmB9F,QAAQ,cAAA+F,qBAAA,eAA3BA,qBAAA,CAA6BhJ,MAAM,CAAC;YAClDgB,KAAK,EAAEiI,QAAQ,CAACjI,KAAK,IAAI,EAAE;YAC3BmB,OAAO,EAAE8G,QAAQ,CAAC9G,OAAO,IAAI,CAAC,CAAC;YAC/B+G,gBAAgB,EAAEA,gBAAgB;YAClCE,cAAc,EAAEA,cAAc;YAC9BO,iBAAiB,EAAEV,QAAQ,CAACP,QAAQ,IAAI,CAAC;UAC3C,CAAC;UAEDvH,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAEkG,IAAI,CAACC,SAAS,CAAC0B,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UACzE,OAAOA,MAAM;QAEf,CAAC,CAAC,OAAOpI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,OAAO;YAAEyH,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,iBAAiBuI,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAE7H,KAAK;UAAEiB;QAAS,CAAC,GAAA4G,KAAA;QACzC9J,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QACpB9H,KAAK,CAACkD,QAAQ,EAAE4E,MAAM,CAAC;QAEvB1G,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAEO,KAAK,CAAC;;QAE1D;QACA,MAAML,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;UAC3C,gBAAgB,EAAEpF;QACpB,CAAC,CAAC;QAEF,IAAI,CAACL,IAAI,EAAE;UACTR,OAAO,CAACM,GAAG,CAAC,4BAA4B,CAAC;UACzC,OAAO;YAAEkH,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAE;UAAiB,CAAC;QACpD;QAEAC,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEE,IAAI,CAACI,GAAG,CAAC;QAChDZ,OAAO,CAACM,GAAG,CAAC,4BAA4B,EAAEkG,IAAI,CAACC,SAAS,CAACjG,IAAI,CAAC+G,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;QAEjF;QACA,IAAI;UACF;UACA,IAAI,CAAC/G,IAAI,CAAC+G,QAAQ,IAAI,CAAC/G,IAAI,CAAC+G,QAAQ,CAACzF,QAAQ,IAAI,CAACtB,IAAI,CAAC+G,QAAQ,CAACzF,QAAQ,CAACjD,MAAM,EAAE;YAC/EmB,OAAO,CAACM,GAAG,CAAC,qDAAqD,CAAC;YAClE,OAAO;cACLkH,OAAO,EAAE,KAAK;cACdzH,KAAK,EAAE,wBAAwB;cAC/BmC,MAAM,EAAE1B,IAAI,CAACI,GAAG;cAChB2G,QAAQ,EAAE/G,IAAI,CAAC+G;YACjB,CAAC;UACH;UAEA,MAAM1I,MAAM,GAAG8J,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMC,UAAU,GAAGpI,IAAI,CAAC+G,QAAQ,CAACzF,QAAQ,CAACjD,MAAM;UAChD,MAAMgK,aAAa,GAAGhK,MAAM,CAACiK,WAAW,CAAChH,QAAQ,EAAE8G,UAAU,CAAC;UAE9D5I,OAAO,CAACM,GAAG,CAAC,oCAAoC,EAAEuI,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;UAClF7I,OAAO,CAACM,GAAG,CAAC,0BAA0B,EAAEsI,UAAU,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;UAC5E/I,OAAO,CAACM,GAAG,CAAC,8BAA8B,EAAEwB,QAAQ,CAACR,MAAM,CAAC;UAE5D,OAAO;YACLkG,OAAO,EAAEqB,aAAa;YACtB3G,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChBoI,WAAW,EAAEJ,UAAU,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAChDE,cAAc,EAAEnH,QAAQ,CAACR;UAC3B,CAAC;QACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D,OAAO;YAAEyH,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,8BAA8BgJ,CAAAC,KAAA,EAAY;QAAA,IAAAC,cAAA,EAAAC,qBAAA;QAAA,IAAX;UAAExI;QAAM,CAAC,GAAAsI,KAAA;QAC5CvK,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QAEpB1G,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAEO,KAAK,CAAC;;QAE5E;QACA,MAAML,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;UAC3C,gBAAgB,EAAEpF;QACpB,CAAC,CAAC;QAEF,IAAI,CAACL,IAAI,EAAE;UACT,OAAO;YAAEgH,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAE;UAAiB,CAAC;QACpD;QAEAC,OAAO,CAACM,GAAG,CAAC,mDAAmD,EAAEkG,IAAI,CAACC,SAAS,CAACjG,IAAI,CAAC+G,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;QAExG;QACA,IAAI,GAAA6B,cAAA,GAAC5I,IAAI,CAAC+G,QAAQ,cAAA6B,cAAA,gBAAAC,qBAAA,GAAbD,cAAA,CAAetH,QAAQ,cAAAuH,qBAAA,eAAvBA,qBAAA,CAAyBxK,MAAM,GAAE;UACpC,OAAO;YAAE2I,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAE;UAAoB,CAAC;QACvD;QAEA,MAAM6I,UAAU,GAAGpI,IAAI,CAAC+G,QAAQ,CAACzF,QAAQ,CAACjD,MAAM;QAChDmB,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEsI,UAAU,CAAC;QAChE5I,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEsI,UAAU,CAACtH,MAAM,CAAC;QACvEtB,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAEsI,UAAU,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEtF;QACA,MAAMO,QAAQ,GAAG,aAAa,CAAC5D,IAAI,CAACkD,UAAU,CAAC;QAC/C5I,OAAO,CAACM,GAAG,CAAC,4CAA4C,EAAEgJ,QAAQ,CAAC;QAEnE,OAAO;UACL9B,OAAO,EAAE,IAAI;UACbtF,MAAM,EAAE1B,IAAI,CAACI,GAAG;UAChB2I,UAAU,EAAEX,UAAU,CAACtH,MAAM;UAC7B0H,WAAW,EAAEJ,UAAU,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;UAChDS,cAAc,EAAEF,QAAQ;UACxBG,YAAY,EAAEjJ,IAAI,CAAC+G;QACrB,CAAC;MACH,CAAC;MAED,MAAM,uBAAuBmC,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAE9I,KAAK;UAAEiB;QAAS,CAAC,GAAA6H,KAAA;QAC/C/K,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QACpB9H,KAAK,CAACkD,QAAQ,EAAE4E,MAAM,CAAC;QAEvB1G,OAAO,CAACM,GAAG,CAAC,6CAA6C,EAAEO,KAAK,CAAC;QAEjE,IAAI;UAAA,IAAA+I,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA;UACF;UACA,MAAMvJ,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAEpF;UACpB,CAAC,CAAC;UAEF,IAAI,CAACL,IAAI,EAAE;YACTR,OAAO,CAACM,GAAG,CAAC,kCAAkC,CAAC;YAC/C,OAAO;cAAEkH,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAC,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEE,IAAI,CAACI,GAAG,CAAC;UACtDZ,OAAO,CAACM,GAAG,CAAC,kCAAkC,EAAEkG,IAAI,CAACC,SAAS,CAACjG,IAAI,CAAC+G,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEvF;UACA,IAAI,GAAAqC,eAAA,GAACpJ,IAAI,CAAC+G,QAAQ,cAAAqC,eAAA,gBAAAC,qBAAA,GAAbD,eAAA,CAAe9H,QAAQ,cAAA+H,qBAAA,eAAvBA,qBAAA,CAAyBhL,MAAM,GAAE;YACpCmB,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC;YACvD,OAAO;cAAEkH,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAyB,CAAC;UAC5D;;UAEA;UACA,MAAMlB,MAAM,GAAG8J,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMC,UAAU,GAAGpI,IAAI,CAAC+G,QAAQ,CAACzF,QAAQ,CAACjD,MAAM;UAChD,MAAMgK,aAAa,GAAGhK,MAAM,CAACiK,WAAW,CAAChH,QAAQ,EAAE8G,UAAU,CAAC;UAE9D5I,OAAO,CAACM,GAAG,CAAC,0CAA0C,EAAEuI,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;UACxF7I,OAAO,CAACM,GAAG,CAAC,gCAAgC,EAAEsI,UAAU,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;;UAElF;UACA,MAAMiB,UAAU,GAAG,EAAAF,aAAA,GAAAtJ,IAAI,CAACM,MAAM,cAAAgJ,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkB1D,QAAQ,KAAI,KAAK;UACtDrG,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAE0J,UAAU,CAAC;;UAE5D;UACAhK,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEE,IAAI,CAACX,KAAK,CAAC;;UAExD;UACA,IAAIoK,cAAc,GAAG,IAAI;UACzB,IAAI;YACF;YACA,MAAMC,YAAY,GAAG1L,QAAQ,CAAC2L,0BAA0B,CAAC,CAAC;YAC1DnK,OAAO,CAACM,GAAG,CAAC,0CAA0C,EAAE,CAAC,CAAC4J,YAAY,CAAC;YACvED,cAAc,GAAG,6BAA6B;UAChD,CAAC,CAAC,OAAOG,UAAU,EAAE;YACnBpK,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEqK,UAAU,CAAC;YACtEH,cAAc,GAAGG,UAAU,CAAClK,OAAO;UACrC;UAEA,OAAO;YACLsH,OAAO,EAAEqB,aAAa;YACtB3G,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChByJ,oBAAoB,EAAExB,aAAa;YACnCyB,aAAa,EAAEN,UAAU;YACzBO,SAAS,EAAE/J,IAAI,CAACX,KAAK;YACrBmJ,WAAW,EAAEJ,UAAU,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YAChDkB,cAAc,EAAEA,cAAc;YAC9BO,iBAAiB,EAAE;cACjB5J,GAAG,EAAEJ,IAAI,CAACI,GAAG;cACbE,MAAM,EAAEN,IAAI,CAACM,MAAM;cACnByG,QAAQ,EAAE/G,IAAI,CAAC+G,QAAQ;cACvB1H,KAAK,EAAEW,IAAI,CAACX,KAAK;cACjBmB,OAAO,EAAER,IAAI,CAACQ;YAChB;UACF,CAAC;QAEH,CAAC,CAAC,OAAOjB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,OAAO;YAAEyH,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,qBAAqBuK,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAE7J,KAAK;UAAEiB;QAAS,CAAC,GAAA4I,KAAA;QAC7C9L,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QACpB9H,KAAK,CAACkD,QAAQ,EAAE4E,MAAM,CAAC;QAEvB1G,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEO,KAAK,CAAC;QAE3D,IAAI;UAAA,IAAA8J,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA;UACF;UACA,MAAMtK,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAEpF;UACpB,CAAC,CAAC;UAEF,IAAI,CAACL,IAAI,EAAE;YACT,OAAO;cAAEgH,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAC,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEE,IAAI,CAACI,GAAG,CAAC;;UAEpD;UACA,MAAM/B,MAAM,GAAG8J,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMC,UAAU,IAAA+B,eAAA,GAAGnK,IAAI,CAAC+G,QAAQ,cAAAoD,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAe7I,QAAQ,cAAA8I,qBAAA,uBAAvBA,qBAAA,CAAyB/L,MAAM;UAElD,IAAI,CAAC+J,UAAU,EAAE;YACf,OAAO;cAAEpB,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAyB,CAAC;UAC5D;UAEA,MAAM8I,aAAa,GAAGhK,MAAM,CAACiK,WAAW,CAAChH,QAAQ,EAAE8G,UAAU,CAAC;UAC9D5I,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAEuI,aAAa,CAAC;UAE7D,IAAI,CAACA,aAAa,EAAE;YAClB,OAAO;cAAErB,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAmB,CAAC;UACtD;;UAEA;UACA,MAAMgL,YAAY,GAAG,EAAE;;UAEvB;UACA,MAAMT,aAAa,GAAG,EAAAO,aAAA,GAAArK,IAAI,CAACM,MAAM,cAAA+J,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBzE,QAAQ,KAAI,KAAK;UACzD;UACA;UACA;;UAEA;UACA,IAAI7F,IAAI,CAACwK,QAAQ,EAAE;YACjBD,YAAY,CAACpF,IAAI,CAAC,uBAAuB,CAAC;UAC5C;;UAEA;UACA,IAAI,CAACnF,IAAI,CAACX,KAAK,IAAIW,IAAI,CAACX,KAAK,CAACyB,MAAM,KAAK,CAAC,EAAE;YAC1CyJ,YAAY,CAACpF,IAAI,CAAC,mBAAmB,CAAC;UACxC;UAEA3F,OAAO,CAACM,GAAG,CAAC,qCAAqC,EAAEyK,YAAY,CAAC;;UAEhE;UACA,IAAIE,eAAe,GAAG,eAAe;UACrC,IAAI;YACF;YACA,MAAMf,YAAY,GAAG1L,QAAQ,CAAC2L,0BAA0B,CAAC,CAAC;YAC1D,IAAID,YAAY,EAAE;cAChBe,eAAe,GAAG,mCAAmC;YACvD;UACF,CAAC,CAAC,OAAOb,UAAU,EAAE;YACnBa,eAAe,mBAAA3I,MAAA,CAAmB8H,UAAU,CAAClK,OAAO,CAAE;UACxD;UAEA,OAAO;YACLsH,OAAO,EAAEqB,aAAa,IAAIkC,YAAY,CAACzJ,MAAM,KAAK,CAAC;YACnDY,MAAM,EAAE1B,IAAI,CAACI,GAAG;YAChBiI,aAAa,EAAEA,aAAa;YAC5ByB,aAAa,EAAEA,aAAa;YAC5BS,YAAY,EAAEA,YAAY;YAC1BE,eAAe,EAAEA,eAAe;YAChCC,aAAa,EAAE;cACbtK,GAAG,EAAEJ,IAAI,CAACI,GAAG;cACbE,MAAM,EAAEN,IAAI,CAACM,MAAM;cACnBjB,KAAK,EAAEW,IAAI,CAACX,KAAK;cACjBmB,OAAO,EAAER,IAAI,CAACQ,OAAO;cACrBgK,QAAQ,EAAExK,IAAI,CAACwK,QAAQ,IAAI;YAC7B;UACF,CAAC;QAEH,CAAC,CAAC,OAAOjL,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,OAAO;YAAEyH,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,uBAAuBiL,CAAAC,KAAA,EAAsB;QAAA,IAArB;UAAEvK,KAAK;UAAEiB;QAAS,CAAC,GAAAsJ,KAAA;QAC/CxM,KAAK,CAACiC,KAAK,EAAE6F,MAAM,CAAC;QACpB9H,KAAK,CAACkD,QAAQ,EAAE4E,MAAM,CAAC;QAEvB1G,OAAO,CAACM,GAAG,CAAC,kDAAkD,EAAEO,KAAK,CAAC;QAEtE,IAAI;UAAA,IAAAwK,eAAA,EAAAC,qBAAA;UACF;UACA,MAAM9K,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC;YAC3C,gBAAgB,EAAEpF;UACpB,CAAC,CAAC;UAEF,IAAI,CAACL,IAAI,EAAE;YACT,OAAO;cAAEgH,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAiB,CAAC;UACpD;UAEAC,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEE,IAAI,CAACI,GAAG,CAAC;;UAEtD;UACA,MAAM/B,MAAM,GAAG8J,OAAO,CAAC,QAAQ,CAAC;UAChC,MAAMC,UAAU,IAAAyC,eAAA,GAAG7K,IAAI,CAAC+G,QAAQ,cAAA8D,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAevJ,QAAQ,cAAAwJ,qBAAA,uBAAvBA,qBAAA,CAAyBzM,MAAM;UAElD,IAAI,CAAC+J,UAAU,EAAE;YACf,OAAO;cAAEpB,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAyB,CAAC;UAC5D;UAEA,MAAM8I,aAAa,GAAGhK,MAAM,CAACiK,WAAW,CAAChH,QAAQ,EAAE8G,UAAU,CAAC;UAC9D5I,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAEuI,aAAa,CAAC;UAE/D,IAAI,CAACA,aAAa,EAAE;YAClB,OAAO;cAAErB,OAAO,EAAE,KAAK;cAAEzH,KAAK,EAAE;YAAmB,CAAC;UACtD;;UAEA;UACA,IAAI;YACF,MAAMmK,YAAY,GAAG1L,QAAQ,CAAC2L,0BAA0B,CAAC,CAAC;YAC1DnK,OAAO,CAACM,GAAG,CAAC,0CAA0C,EAAE,CAAC,CAAC4J,YAAY,CAAC;;YAEvE;YACA,MAAM5L,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;cACvC2K,KAAK,EAAE;gBACL,6BAA6B,EAAErB;cACjC;YACF,CAAC,CAAC;YAEFlK,OAAO,CAACM,GAAG,CAAC,6CAA6C,CAAC;YAE1D,OAAO;cACLkH,OAAO,EAAE,IAAI;cACbtF,MAAM,EAAE1B,IAAI,CAACI,GAAG;cAChB4K,KAAK,EAAEtB,YAAY,CAACsB,KAAK;cACzBtL,OAAO,EAAE;YACX,CAAC;UAEH,CAAC,CAAC,OAAOkK,UAAU,EAAE;YACnBpK,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEqK,UAAU,CAAC;YAC3D,OAAO;cAAE5C,OAAO,EAAE,KAAK;cAAEzH,KAAK,kBAAAuC,MAAA,CAAkB8H,UAAU,CAAClK,OAAO;YAAG,CAAC;UACxE;QAEF,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,OAAO;YAAEyH,OAAO,EAAE,KAAK;YAAEzH,KAAK,EAAEA,KAAK,CAACG;UAAQ,CAAC;QACjD;MACF,CAAC;MAED,MAAM,4BAA4BuL,CAAA,EAAG;QACnC,IAAI,CAAC,IAAI,CAACvJ,MAAM,EAAE;UAChB,MAAM,IAAI5D,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAAsG,aAAA,EAAAC,cAAA;UACF,MAAMnL,IAAI,GAAG,MAAMlC,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC,IAAI,CAAC/D,MAAM,CAAC;UACzDlC,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAE;YACnDK,EAAE,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,GAAG;YACbC,KAAK,EAAEL,IAAI,aAAJA,IAAI,wBAAAkL,aAAA,GAAJlL,IAAI,CAAEM,MAAM,cAAA4K,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,uBAAjBA,cAAA,CAAmB5K,OAAO;YACjClB,KAAK,EAAEW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX;UACf,CAAC,CAAC;;UAEF;UACA,IAAI,CAACW,IAAI,CAACX,KAAK,EAAE;YACf,MAAMvB,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAAC,IAAI,CAACS,MAAM,EAAE;cAC1CR,IAAI,EAAE;gBAAE7B,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YACF,OAAO,mBAAmB;UAC5B;;UAEA;UACA,IAAI,CAACW,IAAI,CAACX,KAAK,CAAC+L,QAAQ,CAAC,OAAO,CAAC,EAAE;YACjC5L,OAAO,CAACM,GAAG,CAAC,kEAAkE,CAAC;;YAE/E;YACA,MAAMuL,UAAU,GAAG,MAAMvN,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACwB,UAAU,CAAC,CAAC;YACzD,IAAIiK,UAAU,KAAK,CAAC,EAAE;cACpB7L,OAAO,CAACM,GAAG,CAAC,qDAAqD,CAAC;cAClE,MAAMhC,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAAC,IAAI,CAACS,MAAM,EAAE;gBAC1CR,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAAC,OAAO;gBAAE;cAC3B,CAAC,CAAC;cACF,OAAO,kBAAkB;YAC3B;YACA,OAAO,mBAAmB;UAC5B;UAEA,OAAO,uBAAuB;QAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,mBAAmB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC5D;MACF,CAAC;MAED,MAAM,qBAAqB4L,CAAA,EAAG;QAC5B,IAAI,CAAC,IAAI,CAAC5J,MAAM,EAAE;UAChB,MAAM,IAAI5D,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACnE;QAEA,IAAI;UAAA,IAAA2G,kBAAA;UACF,MAAMhF,WAAW,GAAG,MAAMzI,MAAM,CAACsB,KAAK,CAACqG,YAAY,CAAC,IAAI,CAAC/D,MAAM,CAAC;UAChE,IAAI,GAAA6J,kBAAA,GAAChF,WAAW,CAAClH,KAAK,cAAAkM,kBAAA,eAAjBA,kBAAA,CAAmBH,QAAQ,CAAC,OAAO,CAAC,GAAE;YACzC,MAAM,IAAItN,MAAM,CAAC8G,KAAK,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;UAC5E;UAEA,MAAMjF,QAAQ,GAAG,MAAM7B,MAAM,CAACsB,KAAK,CAACQ,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;UACvD,MAAM2L,eAAe,GAAG,EAAE;UAC1B,MAAMC,KAAK,GAAG,EAAE;UAEhB,KAAK,MAAMzL,IAAI,IAAIL,QAAQ,EAAE;YAAA,IAAA+L,cAAA,EAAAC,YAAA;YAC3B,MAAMC,MAAM,GAAG,EAAE;;YAEjB;YACA,IAAI,CAAC5L,IAAI,CAACX,KAAK,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACX,IAAI,CAACX,KAAK,CAAC,EAAE;cAAA,IAAAwM,cAAA,EAAAC,aAAA,EAAAC,cAAA;cAC7CH,MAAM,CAACzG,IAAI,CAAC,gBAAgB,CAAC;cAC7B;cACA,MAAMvD,IAAI,GAAG,EAAAiK,cAAA,GAAA7L,IAAI,CAACQ,OAAO,cAAAqL,cAAA,uBAAZA,cAAA,CAAcjK,IAAI,KAAI,aAAa;cAChD,MAAM9D,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;gBACvCc,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAACuC,IAAI;gBAAE;cACxB,CAAC,CAAC;cACF6J,KAAK,CAACtG,IAAI,0BAAArD,MAAA,EAAAgK,aAAA,GAA0B9L,IAAI,CAACM,MAAM,cAAAwL,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkBxL,OAAO,CAAE,CAAC;YAClE;;YAEA;YACA,IAAI,CAAAmL,cAAA,GAAA1L,IAAI,CAACQ,OAAO,cAAAkL,cAAA,eAAZA,cAAA,CAAc9J,IAAI,IAAI,EAAA+J,YAAA,GAAA3L,IAAI,CAACX,KAAK,cAAAsM,YAAA,uBAAVA,YAAA,CAAa,CAAC,CAAC,MAAK3L,IAAI,CAACQ,OAAO,CAACoB,IAAI,EAAE;cAAA,IAAAoK,aAAA,EAAAC,cAAA;cAC/DL,MAAM,CAACzG,IAAI,CAAC,4BAA4B,CAAC;cACzC;cACA,MAAMrH,MAAM,CAACsB,KAAK,CAAC6B,WAAW,CAACjB,IAAI,CAACI,GAAG,EAAE;gBACvCc,IAAI,EAAE;kBAAE7B,KAAK,EAAE,CAACW,IAAI,CAACQ,OAAO,CAACoB,IAAI;gBAAE;cACrC,CAAC,CAAC;cACF6J,KAAK,CAACtG,IAAI,4BAAArD,MAAA,EAAAkK,aAAA,GAA4BhM,IAAI,CAACM,MAAM,cAAA0L,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkB1L,OAAO,CAAE,CAAC;YACpE;YAEA,IAAIqL,MAAM,CAAC9K,MAAM,GAAG,CAAC,EAAE;cAAA,IAAAoL,aAAA,EAAAC,cAAA;cACrBX,eAAe,CAACrG,IAAI,CAAC;gBACnB9E,KAAK,GAAA6L,aAAA,GAAElM,IAAI,CAACM,MAAM,cAAA4L,aAAA,wBAAAC,cAAA,GAAXD,aAAA,CAAc,CAAC,CAAC,cAAAC,cAAA,uBAAhBA,cAAA,CAAkB5L,OAAO;gBAChCqL;cACF,CAAC,CAAC;YACJ;UACF;UAEA,OAAO;YACLJ,eAAe;YACfC,KAAK;YACL/L,OAAO,EAAE+L,KAAK,CAAC3K,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;UACpD,CAAC;QACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACd,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,iBAAiB,EAAErF,KAAK,CAACG,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,4BAA4B0M,CAAA,EAAG;QAC7B;QACA,IAAI,CAAChK,OAAO,CAACC,GAAG,CAACgK,QAAQ,IAAIjK,OAAO,CAACC,GAAG,CAACgK,QAAQ,KAAK,aAAa,EAAE;UACnE,IAAI;YACF,MAAMC,UAAU,GAAG;cACjBjM,KAAK,EAAE,wBAAwB;cAC/BiB,QAAQ,EAAE,cAAc;cACxBC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE;YACZ,CAAC;YAED,MAAME,MAAM,GAAG1D,QAAQ,CAACqH,UAAU,CAAC;cACjChF,KAAK,EAAEiM,UAAU,CAACjM,KAAK;cACvBiB,QAAQ,EAAEgL,UAAU,CAAChL,QAAQ;cAC7Bd,OAAO,EAAE;gBACPe,SAAS,EAAE+K,UAAU,CAAC/K,SAAS;gBAC/BC,QAAQ,EAAE8K,UAAU,CAAC9K,QAAQ;gBAC7BI,IAAI,EAAE,aAAa;gBACnBC,QAAQ,KAAAC,MAAA,CAAKwK,UAAU,CAAC/K,SAAS,OAAAO,MAAA,CAAIwK,UAAU,CAAC9K,QAAQ;cAC1D;YACF,CAAC,CAAC;;YAEF;YACA1D,MAAM,CAACsB,KAAK,CAACmN,MAAM,CAAC7K,MAAM,EAAE;cAC1BR,IAAI,EAAE;gBAAE7B,KAAK,EAAE,CAAC,aAAa;cAAE;YACjC,CAAC,CAAC;YAEF,OAAO;cACL2H,OAAO,EAAE,IAAI;cACbtF,MAAM;cACNhC,OAAO,EAAE;YACX,CAAC;UACH,CAAC,CAAC,OAAOH,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrD,MAAM,IAAIzB,MAAM,CAAC8G,KAAK,CAAC,2BAA2B,EAAErF,KAAK,CAACG,OAAO,CAAC;UACpE;QACF,CAAC,MAAM;UACL,MAAM,IAAI5B,MAAM,CAAC8G,KAAK,CAAC,iBAAiB,EAAE,8CAA8C,CAAC;QAC3F;MACF;IACF,CAAC,CAAC;IAAC4H,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA", "ignoreList": []}, "sourceType": "module", "externalDependencies": {}, "hash": "99845571a74720006b183b1a9af3e0108ffda467"}