Package["core-runtime"].queue("ecmascript-runtime-server",function () {/* Imports */
var Meteor = Package.meteor.Meteor;
var global = Package.meteor.global;
var meteorEnv = Package.meteor.meteorEnv;
var EmitterPromise = Package.meteor.EmitterPromise;
var meteorInstall = Package.modules.meteorInstall;

var require = meteorInstall({"node_modules":{"meteor":{"ecmascript-runtime-server":{"runtime.js":function module(require){

/////////////////////////////////////////////////////////////////////////////
//                                                                         //
// packages/ecmascript-runtime-server/runtime.js                           //
//                                                                         //
/////////////////////////////////////////////////////////////////////////////
                                                                           //
// The ecmascript-runtime-server package depends on its own copy of
// core-js using Npm.depends, so we don't have to check that core-js is
// available (as we do in ecmascript-runtime-client/runtime.js).

// List of polyfills generated by babel-preset-env with the following
// .babelrc configuration:
//
// {
//   "presets": [
//     ["env", {
//       "targets": {
//         "node": 8
//       },
//       "modules": false,
//       "polyfill": true,
//       "useBuiltIns": true
//     }]
//   ]
// }
//
// Note that the es6.reflect.* and es6.typed.* modules have been commented
// out for bundle size reasons.

require("core-js/modules/es.symbol.async-iterator");

/////////////////////////////////////////////////////////////////////////////

},"node_modules":{"core-js":{"modules":{"es.symbol.async-iterator.js":function module(require,exports,module){

/////////////////////////////////////////////////////////////////////////////
//                                                                         //
// node_modules/meteor/ecmascript-runtime-server/node_modules/core-js/modu //
//                                                                         //
/////////////////////////////////////////////////////////////////////////////
                                                                           //
module.useNode();
/////////////////////////////////////////////////////////////////////////////

}}}}}}}},{
  "extensions": [
    ".js",
    ".json"
  ]
});


/* Exports */
return {
  require: require,
  eagerModulePaths: [
    "/node_modules/meteor/ecmascript-runtime-server/runtime.js"
  ],
  mainModulePath: "/node_modules/meteor/ecmascript-runtime-server/runtime.js"
}});
