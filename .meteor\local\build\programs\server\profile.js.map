{"version": 3, "names": ["module", "export", "Profile", "inspector", "link", "*", "v", "fs", "path", "INSPECTOR_CONFIG", "enabled", "process", "env", "METEOR_INSPECT", "filter", "split", "context", "METEOR_INSPECT_CONTEXT", "outputDir", "METEOR_INSPECT_OUTPUT", "join", "cwd", "samplingInterval", "METEOR_INSPECT_INTERVAL", "parseInt", "undefined", "maxProfileSize", "METEOR_INSPECT_MAX_SIZE", "parseFloat", "METEOR_PROFILE", "bucketStats", "Object", "create", "SPACES_STR", "spaces", "len", "length", "slice", "DOTS_STR", "dots", "leftRightAlign", "str1", "str2", "middle", "Math", "max", "leftRightDots", "printIndentation", "isLastLeafStack", "init", "i", "isLastLeaf", "last", "formatMs", "n", "String", "round", "replace", "encodeEntryKey", "entry", "decodeEntryKey", "key", "running", "bucketName", "f", "assign", "profileWrapper", "args", "Array", "from", "arguments", "apply", "asyncLocalStorage", "global", "__METEOR_ASYNC_LOCAL_STORAGE", "store", "getStore", "currentEntry", "name", "profileInfo", "isActive", "isCompleted", "startTime", "Date", "now", "shouldRunInspectorProfiling", "startInspectorProfiling", "handleTermination", "stopInspectorProfiling", "catch", "err", "stdout", "write", "concat", "Promise", "resolve", "on", "signals", "for<PERSON>ach", "signal", "once", "finally", "exit", "completeProfiler", "completeIfSync", "result", "run", "runWithContext", "error", "inspectorActive", "rootSession", "rootProfileName", "profileStartTime", "includes", "open", "session", "Session", "connect", "post", "reject", "duration", "cleanupAndResolve", "_result$profile$nodes", "_result$profile$sampl", "_result$profile$timeD", "profile", "console", "profileStr", "JSON", "stringify", "profileSize", "nodes", "samples", "timeDeltas", "size", "toFixed", "_result$profile$nodes2", "_result$profile$sampl2", "_result$profile$timeD2", "reducedProfile", "endTime", "_warning", "saveProfile", "reduceErr", "saveErr", "processErr", "disconnect", "close", "gc", "gcErr", "cleanupErr", "filename", "existsSync", "mkdirSync", "recursive", "timestamp", "toISOString", "safeFilename", "filepath", "writeFileSync", "start", "hrtime", "finalizeProfiling", "elapsed", "stats", "time", "count", "isOther", "pop", "_runAsync", "bucket", "<PERSON><PERSON><PERSON>", "print", "reportNum", "report", "_runSync", "log", "isAsyncFn", "constructor", "setupReport", "reportHierarchy", "reportHotLeaves", "getTopLevelTotal", "runSetters", "entries", "prefix", "entryName", "entryStats", "entryTime", "isTopLevelEntry", "topLevelEntries", "text", "<PERSON><PERSON><PERSON><PERSON>", "entry1", "entry2", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasSignificantChildren", "some", "<PERSON><PERSON><PERSON><PERSON>", "otherTime", "total", "child", "injectOtherTime", "other", "push", "reportOn", "isParent", "childrenList", "allLeafs", "set", "map", "keys", "sort", "leafTotals", "leafName", "leaf", "totals", "info", "a", "b", "topTotal", "parent"], "sources": ["tools/tool-env/profile.ts"], "sourcesContent": ["// Tiny profiler\n//\n// Enable by setting the environment variable `METEOR_PROFILE`.\n//\n// The main entry point is `Profile`, which wraps an existing function\n// and returns a new function which, when called, calls the original\n// function and profiles it.\n//\n// before:\n//\n//     foo: function (a) {\n//       return a + this.b;\n//     },\n//\n// after:\n//\n//     foo: Profile(\"foo\", function (a) {\n//       return a + this.b;\n//     }),\n//\n// The advantage of this form is that it doesn't change the\n// indentation of the wrapped code, which makes merging changes from\n// other code branches easier.\n//\n// If profiling is disabled (if `METEOR_PROFILE` isn't set), `Profile`\n// simply returns the original function.\n//\n// To run a profiling session and print the report, call `Profile.run`:\n//\n//     var createBundle = function () {\n//       Profile.run(\"bundle\", function () {\n//         ...code to create the bundle which includes calls to `Profile`.\n//       });\n//     };\n//\n// Code is not profiled when called outside of a `Profile.run`, so the\n// times in the report only include the time spent inside of the call\n// to `Profile.run`.\n//\n// Sometimes you'll want to use a name for the profile bucket which\n// depends on the arguments passed to the function or the value of\n// `this`.  In this case you can pass a function for the bucket\n// argument, which will be called to get the bucket name.\n//\n// before:\n//     build: function (target) {\n//       ... build target ...\n//     },\n//\n// after:\n//     build: Profile(\n//       function (target) { return \"build \" + target; },\n//       function (target) {\n//         ... build target ...\n//       }),\n//\n// But if it's easier, you can use `Profile.time` instead, which\n// immediately calls the passed function with no arguments and\n// profiles it, and returns what the function returns.\n//\n//     foo: function (a) {\n//       var self = this;\n//       return Profile.time(\"foo\", function () {\n//         return a + self.b;\n//       });\n//     },\n//\n//     build: function (target) {\n//       var self = this;\n//       self.doSomeSetup();\n//       Profile.time(\"build \" + target, function () {\n//         ... build target ...\n//       });\n//       self.doSomeCleanup();\n//     },\n//\n// The disadvantage is that you end up changing the indentation of the\n// profiled code, which makes merging branches more painful.  But you\n// can profile anywhere in the code; you don't have to just profile at\n// function boundaries.\n//\n// Note profiling code will itself add a bit of execution time.\n// If you profile in a tight loop and your total execution time is\n// going up, you're probably starting to profile how long it takes to\n// profile things :).\n//\n// If another profile (such as \"compile js\") is called while the first\n// function is currently being profiled, this creates an entry like\n// this:\n//\n//    build client : compile js\n//\n// which can continue to be nested, e.g.,\n//\n//    build client : compile js : read source files\n//\n// The total time reported for a bucket such as \"build client\" doesn't\n// change regardless of whether it has child entries or not.  However,\n// if an entry has child entries, it automatically gets an \"other\"\n// entry:\n//\n//     build client: 400.0\n//       compile js: 300.0\n//         read source files: 20.0\n//         other compile js: 280.0\n//       other build client: 100.0\n//\n// The \"other\" entry reports how much time was spent in the \"build\n// client\" entry not spent in the other child entries.\n//\n// The are two reports displayed: the hierarchical report and the\n// leaf time report.  The hierarchical report looks like the example\n// above and shows how much time was spent in each entry within its\n// parent entry.\n//\n// The primary purpose of the hierarchical report is to be able to see\n// where times are unaccounted for.  If you see a lot of time being\n// spent in an \"other\" bucket, and you don't know what it is, you can\n// add more profiling to dig deeper.\n//\n// The leaf time report shows the total time spent within leaf\n// buckets.  For example, if if multiple steps have \"read source\n// files\", the leaf time reports shows the total amount of time spent\n// in \"read source files\" across all calls.\n//\n// Once you see in the hierarchical report that you have a good handle\n// on accounting for most of the time, the leaf report shows you which\n// buckets are the most expensive.\n//\n// By only including leaf buckets, the times in the leaf report are\n// non-overlapping.  (The total of the times equals the elapsed time\n// being profiled).\n//\n// For example, suppose \"A\" is profiled for a total time of 200ms, and\n// that includes a call to \"B\" of 150ms:\n//\n//     B: 150\n//     A (without B): 50\n//\n// and suppose there's another call to \"A\" which *doesn't* include a\n// call to \"B\":\n//\n//     A: 300\n//\n// and there's a call to \"B\" directly:\n//\n//     B: 100\n//\n// All for a total time of 600ms.  In the hierarchical report, this\n// looks like:\n//\n//     A: 500.0\n//       B: 150.0\n//       other A: 350.0\n//     B: 100.0\n//\n// and in the leaf report:\n//\n//     other A: 350.0\n//     B: 250.0\n//\n// In both reports the grand total is 600ms.\n\n// Profiler Usage Documentation\n/**\n* To use the Meteor profiler:\n* \n* 1. For basic profiling:\n* METEOR_PROFILE=1 meteor <command>\n* \n* 2. For profiling with inspector (generating .cpuprofile files):\n* METEOR_INSPECT=bundler.bundle,<other_function_names> meteor <command>\n* \n* 3. Additional settings:\n* METEOR_INSPECT_CONTEXT=context_name (identification for files)\n* METEOR_INSPECT_OUTPUT=path/to/directory (location where to save files)\n* \n* 4. To view .cpuprofile files:\n* - Open Chrome DevTools\n* - Go to the \"Performance\" or \"Profiler\" tab\n* - Click \"Load Profile\" and select the .cpuprofile file\n*/\n\nimport * as inspector from 'inspector';\nimport * as fs from 'fs';\nimport * as path from 'path';\n\ninterface MeteorAsyncLocalStorage {\n  getStore: () => ProfileStore | undefined;\n  run: <T>(store: ProfileStore, fn: () => T) => T;\n}\n\ninterface ProfileStore {\n  currentEntry: string[];\n  [key: string]: any;\n}\n\ndeclare global {\n  var __METEOR_ASYNC_LOCAL_STORAGE: MeteorAsyncLocalStorage;\n}\n\ninterface InspectorConfigType {\n  enabled: boolean;\n  filter: string[];\n  context: string;\n  outputDir: string;\n  samplingInterval: number | undefined;\n  maxProfileSize: number;\n}\n\nconst INSPECTOR_CONFIG: InspectorConfigType = {\n  enabled: !!process.env.METEOR_INSPECT,\n  filter: process.env.METEOR_INSPECT ? process.env.METEOR_INSPECT.split(',') : [],\n  context: process.env.METEOR_INSPECT_CONTEXT || '',\n  outputDir: process.env.METEOR_INSPECT_OUTPUT || path.join(process.cwd(), 'profiling'),\n  // Interval in ms (smaller = more details, but more memory)\n  samplingInterval: process.env.METEOR_INSPECT_INTERVAL ? parseInt(process.env.METEOR_INSPECT_INTERVAL || '1000', 10) : undefined,\n  maxProfileSize: parseInt(process.env.METEOR_INSPECT_MAX_SIZE || '2000', 10)\n};\n\nconst filter = parseFloat(process.env.METEOR_PROFILE || \"100\"); // ms\n\ntype Stats = {\n  time: number;\n  count: number;\n  isOther: boolean;\n}\n\nlet bucketStats: Record<string, Stats> = Object.create(null);\n\nlet SPACES_STR = ' ';\n// return a string of `x` spaces\nfunction spaces(len: number) {\n  while (SPACES_STR.length < len) {\n    SPACES_STR = SPACES_STR + SPACES_STR;\n  }\n  return SPACES_STR.slice(0, len);\n}\n\nlet DOTS_STR = '.';\n// return a string of `x` dots\nfunction dots(len: number) {\n  while (DOTS_STR.length < len) {\n    DOTS_STR = DOTS_STR + DOTS_STR;\n  }\n  return DOTS_STR.slice(0, len);\n}\n\nfunction leftRightAlign(str1: string, str2: string, len: number) {\n  var middle = Math.max(1, len - str1.length - str2.length);\n  return str1 + spaces(middle) + str2;\n}\n\nfunction leftRightDots(str1: string, str2: string, len: number) {\n  var middle = Math.max(1, len - str1.length - str2.length);\n  return str1 + dots(middle) + str2;\n}\n\nfunction printIndentation(isLastLeafStack: boolean[]) {\n  if (!isLastLeafStack.length) {\n    return '';\n  }\n\n  const { length } = isLastLeafStack;\n  let init = '';\n  for (let i = 0; i < length - 1; ++i) {\n    const isLastLeaf = isLastLeafStack[i];\n    init += isLastLeaf ? '   ' : '│  ';\n  }\n\n  const last = isLastLeafStack[length - 1] ? '└─ ' : '├─ ';\n\n  return init + last;\n}\n\nfunction formatMs(n: number) {\n  // integer with thousands separators\n  return String(Math.round(n)).replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\") + \" ms\";\n}\n\nfunction encodeEntryKey(entry: string[]) {\n  return entry.join('\\t');\n}\n\nfunction decodeEntryKey(key: string) {\n  return key.split('\\t');\n}\n\nlet running = false;\n\nexport function Profile<\n  TArgs extends any[],\n  TResult,\n>(\n  bucketName: string | ((...args: TArgs) => string),\n  f: (...args: TArgs) => TResult | Promise<TResult>,\n): typeof f {\n  if (!Profile.enabled) {\n    return f;\n  }\n\n  return Object.assign(function profileWrapper(this: any) {\n    const args = Array.from(arguments) as TArgs;\n\n    if (!running) {\n      return f.apply(this, args);\n    }\n\n    const asyncLocalStorage = global.__METEOR_ASYNC_LOCAL_STORAGE;\n    let store = asyncLocalStorage.getStore() || { currentEntry: [] };\n\n    const name = typeof bucketName === 'function' ? bucketName.apply(this, args) : bucketName;\n    \n    // callbacks with observer to track when the function finishes\n    const profileInfo = {\n      name,\n      isActive: false,\n      isCompleted: false,\n      startTime: Date.now()\n    };\n\n    if (shouldRunInspectorProfiling(name)) {\n      profileInfo.isActive = startInspectorProfiling(name);\n      \n      if (profileInfo.isActive) {\n        const handleTermination = (context: string) => {\n          if (profileInfo.isActive && !profileInfo.isCompleted) {\n            return stopInspectorProfiling(name, true).catch(err => {\n              process.stdout.write(`[PROFILING_${context}] Error stopping profiling: ${err}\\n`);\n            });\n          }\n          return Promise.resolve();\n        };\n\n        process.on('exit', () => { handleTermination('EXIT'); });\n\n        const signals = ['SIGINT', 'SIGTERM', 'SIGHUP'];\n        signals.forEach(signal => {\n          process.once(signal, () => {\n            handleTermination('SIGNAL').finally(() => {\n              process.exit(130);\n            });\n          });\n        });\n      }\n    }\n\n    const completeProfiler = () => {\n      if (profileInfo.isActive && !profileInfo.isCompleted) {\n        profileInfo.isCompleted = true;\n        return stopInspectorProfiling(name, true).catch(err => {\n          process.stdout.write(`[PROFILING_COMPLETE] Error stopping profiling: ${err}`);\n        });\n      }\n      return Promise.resolve();\n    };\n\n    function completeIfSync(result:  TResult | Promise<TResult>){\n      if (!(result instanceof Promise)) {\n        completeProfiler();\n      }\n    }\n\n    try {\n      if (!asyncLocalStorage.getStore()) {\n        const result = asyncLocalStorage.run(store, () => \n          runWithContext(name, store, f, this, args, completeProfiler));\n          \n        // For sync results, complete profiling here\n        completeIfSync(result)\n        return result;\n      }\n\n      // if there is already a store, use the current context\n      const result = runWithContext(name, store, f, this, args, completeProfiler);\n      \n      // For sync results, complete profiling here\n      completeIfSync(result)\n      return result;\n    } catch (error) {\n      completeProfiler();\n      throw error;\n    }\n  }, f) as typeof f;\n}\n\n// ================================\n// Inspector Profiling\n// ================================\nlet inspectorActive = false;\nlet rootSession: inspector.Session | null = null;\nlet rootProfileName: string | null = null;\nlet profileStartTime: number | null = null;\n\nfunction shouldRunInspectorProfiling(name: string): boolean {\n  if (!INSPECTOR_CONFIG.enabled) return false;\n  return INSPECTOR_CONFIG.filter.includes(name);\n}\n\nfunction startInspectorProfiling(name: string): boolean {\n  if (!shouldRunInspectorProfiling(name)) {\n    return false;\n  }\n\n  try {\n    if (rootSession) {\n      return false;\n    }\n    \n    profileStartTime = Date.now();\n    \n    // Open the inspector only if it's not active\n    if (!inspectorActive) {\n      inspector.open();\n      inspectorActive = true;\n    }\n    \n    // Create a single session for the duration of profiling\n    const session = new inspector.Session();\n    session.connect();\n    session.post('Profiler.enable');\n    session.post('Profiler.start', {\n      samplingInterval: INSPECTOR_CONFIG.samplingInterval\n    });\n\n    // Store the root session for later use\n    rootSession = session;\n    rootProfileName = name;\n    \n    return true;\n  } catch (err) {\n    process.stdout.write(`[PROFILING_START] Error starting profiling for ${name}: ${err}\\n`);\n    return false;\n  }\n}\n\nfunction stopInspectorProfiling(name: string, isActive: boolean): Promise<void> {\n  if (!isActive || !rootSession || name !== rootProfileName) {\n    return Promise.resolve();\n  }\n  \n  return new Promise((resolve, reject) => {\n    try {\n      const duration = profileStartTime ? Date.now() - profileStartTime : 0;\n      const session = rootSession;\n      if (!session) {\n        return resolve();\n      }\n      \n      session.post('Profiler.stop', (err: Error | null, result: any) => {\n        if (err) {\n          cleanupAndResolve(resolve);\n          reject(err);\n          return;\n        }\n        \n        try {\n          // check if we have data in the profile\n          if (!result || !result.profile) {\n            console.error(`[PROFILING_STOP] Empty profile for ${name}`);\n            cleanupAndResolve(resolve);\n            return;\n          }\n          \n          // check the approximate size of the profile\n          const profileStr = JSON.stringify(result.profile);\n          const profileSize = profileStr.length / (1024 * 1024); // in MB\n          \n          process.stdout.write(`[PROFILING_STOP] Profile captured successfully for ${name}: ${JSON.stringify({\n            nodes: result.profile.nodes?.length || 0,\n            samples: result.profile.samples?.length || 0,\n            timeDeltas: result.profile.timeDeltas?.length || 0,\n            duration: duration,\n            size: profileSize.toFixed(2) + \" MB\"\n          })}`);\n          \n          if (profileSize > INSPECTOR_CONFIG.maxProfileSize) {\n            process.stdout.write(`[PROFILING_STOP] Profile too large (${profileSize.toFixed(2)}MB > ${INSPECTOR_CONFIG.maxProfileSize}MB)`);\n            process.stdout.write('[PROFILING_STOP] To avoid OOM, a reduced profile will be saved');\n            process.stdout.write('[PROFILING_STOP] Increase METEOR_INSPECT_MAX_SIZE or METEOR_INSPECT_INTERVAL to adjust');\n            \n            // Try to save a reduced profile\n            try {\n              // Simplify the profile to reduce size\n              const reducedProfile = {\n                nodes: result.profile.nodes?.slice(0, 10000) || [],\n                samples: result.profile.samples?.slice(0, 10000) || [],\n                timeDeltas: result.profile.timeDeltas?.slice(0, 10000) || [],\n                startTime: result.profile.startTime,\n                endTime: result.profile.endTime,\n                _warning: \"profile truncated to avoid OOM. Use a larger interval.\"\n              };\n              \n              saveProfile(reducedProfile, name, `${name}_reduced`, duration);\n            } catch (reduceErr) {\n              process.stdout.write(`[PROFILING_STOP] Error saving reduced profile: ${reduceErr}`);\n            }\n            \n            cleanupAndResolve(resolve);\n            return;\n          }\n          \n          try {\n            saveProfile(result.profile, name, name, duration);\n          } catch (saveErr) {\n            process.stdout.write(`[PROFILING_STOP] Error saving profile: ${saveErr}`);\n          }\n          \n          cleanupAndResolve(resolve);\n        } catch (processErr) {\n          process.stdout.write(`[PROFILING_STOP] Error processing profile for ${name}: ${processErr}`);\n          cleanupAndResolve(resolve);\n          reject(processErr);\n        }\n      });\n    } catch (err) {\n      process.stdout.write(`[PROFILING_STOP] Error in stopInspectorProfiling for ${name}: ${err}`);\n      cleanupAndResolve(resolve);\n      reject(err);\n    }\n  });\n  \n  function cleanupAndResolve(resolve: (value?: void | PromiseLike<void>) => void) {\n    try {\n      if (rootSession) {\n        rootSession.post('Profiler.disable');\n        rootSession.disconnect();\n      }\n      \n      if (inspectorActive) {\n        inspector.close();\n        inspectorActive = false;\n      }\n      \n      rootSession = null;\n      rootProfileName = null;\n      profileStartTime = null;\n      \n      // Force GC if available\n      if (typeof global.gc === 'function') {\n        try {\n          global.gc();\n          process.stdout.write('[PROFILING_STOP] Garbage collector executed successfully');\n        } catch (gcErr) {\n          process.stdout.write(`[PROFILING_STOP] Error executing garbage collector: ${gcErr}`);\n        }\n      }\n      \n      return resolve();\n    } catch (cleanupErr) {\n      process.stdout.write(`[PROFILING_STOP] Error during cleanup: ${cleanupErr}`);\n      return resolve();\n    }\n  }\n}\n\nfunction saveProfile(profile: any, name: string, filename: string, duration: number): void {\n  if (!fs.existsSync(INSPECTOR_CONFIG.outputDir)) {\n    fs.mkdirSync(INSPECTOR_CONFIG.outputDir, { recursive: true });\n  }\n  \n  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n  const safeFilename = filename.replace(/[\\/\\\\:]/g, '_');\n  const filepath = path.join(INSPECTOR_CONFIG.outputDir, `${safeFilename}-${INSPECTOR_CONFIG.context}-${timestamp}.cpuprofile`);\n  \n  fs.writeFileSync(filepath, JSON.stringify(profile));\n  \n  const profileSize = JSON.stringify(profile).length / (1024 * 1024);\n  \n  process.stdout.write(`[PROFILING_SAVE] Profile for ${name} saved in: ${filepath}`);\n  process.stdout.write(`[PROFILING_SAVE] Duration: ${duration}ms, size: ${profileSize.toFixed(2)}MB`);\n}\n\nfunction runWithContext<TArgs extends any[], TResult>(\n    bucketName: string | ((...args: TArgs) => string),\n    store: { currentEntry: string[]; [key: string]: any },\n    f: (...args: TArgs) => TResult | Promise<TResult>,\n    context: any,\n    args: any[],\n    completeProfiler: () => Promise<void>\n): TResult | Promise<TResult> {\n  const name = typeof bucketName === \"function\" ? bucketName.apply(context, args as TArgs) : bucketName;\n  store.currentEntry = [...store.currentEntry || [], name];\n  const key = encodeEntryKey(store.currentEntry);\n  const start = process.hrtime();\n\n  let result: TResult | Promise<TResult>;\n  try {\n    result = f.apply(context, args as TArgs);\n\n    if (result instanceof Promise) {\n      // Return a promise if async\n      return result.finally(() => finalizeProfiling(key, start, store.currentEntry, completeProfiler));\n    }\n\n    // Return directly if sync\n    return result;\n  } finally {\n    if (!(result! instanceof Promise)) {\n      finalizeProfiling(key, start, store.currentEntry, completeProfiler);\n    }\n  }\n}\n\nfunction finalizeProfiling(key: string, start: [number, number], currentEntry: string[], completeProfiler: () => Promise<void>) {\n  const elapsed = process.hrtime(start);\n  const stats = (bucketStats[key] || (bucketStats[key] = {\n    time: 0.0,\n    count: 0,\n    isOther: false,\n  }));\n  stats.time += elapsed[0] * 1000 + elapsed[1] / 1_000_000;\n  stats.count++;\n  currentEntry.pop();\n  completeProfiler();\n}\n\nexport namespace Profile {\n  export let enabled = !! process.env.METEOR_PROFILE || !! process.env.METEOR_INSPECT;\n\n  async function _runAsync<TResult>(bucket: string, f: () => TResult) {\n    runningName = bucket;\n    print(`(#${reportNum}) Profiling: ${runningName}`);\n    start();\n    try {\n      return await time(bucket, f);\n    } finally {\n      report();\n      reportNum++;\n    }\n  }\n\n  function _runSync<TResult>(bucket: string, f: () => TResult) {\n    runningName = bucket;\n    print(`(#${reportNum}) Profiling: ${runningName}`);\n    start();\n    try {\n      return time(bucket, f);\n    } finally {\n      report();\n      reportNum++;\n    }\n  }\n\n  export function time<TResult>(bucket: string, f: () => TResult) {\n    return Profile(bucket, f)();\n  }\n\n  export function run<TResult>(bucket: string, f: () => TResult) {\n    if (! Profile.enabled) {\n      return f();\n    }\n\n    if (running) {\n      // We've kept the calls to Profile.run in the tool disjoint so far,\n      // and should probably keep doing so, but if we mess up, warn and continue.\n      console.log(\"Warning: Nested Profile.run at \" + bucket);\n      return time(bucket, f);\n    }\n\n    const isAsyncFn = f.constructor.name === \"AsyncFunction\";\n    if (!isAsyncFn) {\n      return _runSync(bucket, f);\n    }\n\n    return _runAsync(bucket, f);\n  }\n\n  function start() {\n    bucketStats = {};\n    running = true;\n  }\n\n  let runningName: string;\n  let reportNum = 1;\n  function report() {\n    if (! Profile.enabled) {\n      return;\n    }\n    running = false;\n    print('');\n    setupReport();\n    reportHierarchy();\n    print('');\n    reportHotLeaves();\n    print('');\n    print(`(#${reportNum}) Total: ${formatMs(getTopLevelTotal())}` +\n          ` (${runningName})`);\n    print('');\n  }\n}\n\ntype Entry = string[];\nlet entries: Entry[] = [];\n\nconst prefix = \"| \";\n\nfunction entryName(entry: Entry) {\n  return entry[entry.length - 1];\n}\n\nfunction entryStats(entry: Entry) {\n  return bucketStats[encodeEntryKey(entry)];\n}\n\nfunction entryTime(entry: Entry) {\n  return entryStats(entry).time;\n}\n\nfunction isTopLevelEntry(entry: Entry) {\n  return entry.length === 1;\n}\n\nfunction topLevelEntries() {\n  return entries.filter(isTopLevelEntry);\n}\n\nfunction print(text: string) {\n  console.log(prefix + text);\n}\n\nfunction isChild(entry1: Entry, entry2: Entry) {\n  if (entry2.length !== entry1.length + 1) {\n    return false;\n  }\n  for (var i = entry1.length - 1; i >= 0; i--) {\n    if (entry1[i] !== entry2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction children(entry1: Entry) {\n  return entries.filter(entry2 => isChild(entry1, entry2));\n}\n\nfunction hasChildren(entry: Entry) {\n  return children(entry).length > 0;\n}\n\nfunction hasSignificantChildren(entry: Entry) {\n  return children(entry).some(entry => entryTime(entry) >= filter);\n}\n\nfunction isLeaf(entry: Entry) {\n  return ! hasChildren(entry);\n}\n\nfunction otherTime(entry: Entry) {\n  let total = 0;\n  children(entry).forEach(child => {\n    total += entryTime(child);\n  });\n  return entryTime(entry) - total;\n}\n\nfunction injectOtherTime(entry: Entry) {\n  const other: Entry = entry.slice(0);\n  other.push(\"other \" + entryName(entry));\n  bucketStats[encodeEntryKey(other)] = {\n    time: otherTime(entry),\n    count: entryStats(entry).count,\n    isOther: true\n  };\n  entries.push(other);\n};\n\nfunction reportOn(entry: Entry, isLastLeafStack: boolean[] = []) {\n  const stats = entryStats(entry);\n  const isParent = hasSignificantChildren(entry);\n  const name = entryName(entry);\n\n  print((isParent ? leftRightDots : leftRightAlign)\n        (printIndentation(isLastLeafStack) + name, formatMs(stats.time), 70)\n        + (stats.isOther ? \"\" : (\" (\" + stats.count + \")\")));\n\n  if (isParent) {\n    const childrenList = children(entry).filter(entry => {\n      return entryStats(entry).time > filter;\n    });\n    childrenList.forEach((child, i) => {\n      const isLastLeaf = i === childrenList.length - 1;\n      reportOn(child, isLastLeafStack.concat(isLastLeaf));\n    });\n  }\n}\n\nfunction reportHierarchy() {\n  topLevelEntries().forEach(entry => reportOn(entry));\n}\n\nfunction allLeafs() {\n  const set: { [name: string]: any } = Object.create(null);\n  entries.filter(isLeaf).map(entryName).forEach(name => set[name] = true);\n  return Object.keys(set).sort();\n}\n\nfunction leafTotals(leafName: string) {\n  let time = 0;\n  let count = 0;\n\n  entries.filter(entry => {\n    return entryName(entry) === leafName && isLeaf(entry);\n  }).forEach(leaf => {\n    const stats = entryStats(leaf);\n    time += stats.time;\n    count += stats.count;\n  });\n\n  return { time, count };\n}\n\nfunction reportHotLeaves() {\n  print('Top leaves:');\n\n  const totals = allLeafs().map(leaf => {\n    const info = leafTotals(leaf);\n    return {\n      name: leaf,\n      time: info.time,\n      count: info.count,\n    };\n  }).sort((a, b) => {\n    return a.time === b.time ? 0 : a.time > b.time ? -1 : 1;\n  });\n\n  totals.forEach(total => {\n    if (total.time < 100) { // hard-coded larger filter to quality as \"hot\" here\n      return;\n    }\n    print(leftRightDots(total.name, formatMs(total.time), 65) + ` (${total.count})`);\n  });\n}\n\nfunction getTopLevelTotal() {\n  let topTotal = 0;\n  topLevelEntries().forEach(entry => {\n    topTotal += entryTime(entry);\n  });\n  return topTotal;\n}\n\nfunction setupReport() {\n  entries = Object.keys(bucketStats).map(decodeEntryKey);\n  entries.filter(hasSignificantChildren).forEach(parent => {\n    injectOtherTime(parent);\n  });\n}\n\n"], "mappings": "AAAAA,MAAA,CAAAC,MAAA;EAAAC,OAAgB,EAAAA,CAAA,KAAAA;AAAA;AAAA,IAAAC,SAAA;AAAAH,MAAA,CAAAI,IAAA;EAAA,GAAAC,CAAAC,CAAA;IAAAH,SAAA,GAAAG,CAAA;EAAA;AAAA;AAAA,IAAAC,EAAA;AAAAP,MAAA,CAAAI,IAAA;EAAA,GAAAC,CAAAC,CAAA;IAAAC,EAAA,GAAAD,CAAA;EAAA;AAAA;AAAA,IAAAE,IAAA;AAAAR,MAAA,CAAAI,IAAA;EAAA,GAAAC,CAAAC,CAAA;IAAAE,IAAA,GAAAF,CAAA;EAAA;AAAA;AAkNhB,MAAMG,gBAAgB,GAAwB;EAC5CC,OAAO,EAAE,CAAC,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc;EACrCC,MAAM,EAAEH,OAAO,CAACC,GAAG,CAACC,cAAc,GAAGF,OAAO,CAACC,GAAG,CAACC,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EAC/EC,OAAO,EAAEL,OAAO,CAACC,GAAG,CAACK,sBAAsB,IAAI,EAAE;EACjDC,SAAS,EAAEP,OAAO,CAACC,GAAG,CAACO,qBAAqB,IAAIX,IAAI,CAACY,IAAI,CAACT,OAAO,CAACU,GAAG,EAAE,EAAE,WAAW,CAAC;EACrF;EACAC,gBAAgB,EAAEX,OAAO,CAACC,GAAG,CAACW,uBAAuB,GAAGC,QAAQ,CAACb,OAAO,CAACC,GAAG,CAACW,uBAAuB,IAAI,MAAM,EAAE,EAAE,CAAC,GAAGE,SAAS;EAC/HC,cAAc,EAAEF,QAAQ,CAACb,OAAO,CAACC,GAAG,CAACe,uBAAuB,IAAI,MAAM,EAAE,EAAE;CAC3E;AAED,MAAMb,MAAM,GAAGc,UAAU,CAACjB,OAAO,CAACC,GAAG,CAACiB,cAAc,IAAI,KAAK,CAAC,CAAC,CAAC;AAQhE,IAAIC,WAAW,GAA0BC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAE5D,IAAIC,UAAU,GAAG,GAAG;AACpB;AACA,SAASC,MAAMA,CAACC,GAAW;EACzB,OAAOF,UAAU,CAACG,MAAM,GAAGD,GAAG,EAAE;IAC9BF,UAAU,GAAGA,UAAU,GAAGA,UAAU;EACtC;EACA,OAAOA,UAAU,CAACI,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC;AACjC;AAEA,IAAIG,QAAQ,GAAG,GAAG;AAClB;AACA,SAASC,IAAIA,CAACJ,GAAW;EACvB,OAAOG,QAAQ,CAACF,MAAM,GAAGD,GAAG,EAAE;IAC5BG,QAAQ,GAAGA,QAAQ,GAAGA,QAAQ;EAChC;EACA,OAAOA,QAAQ,CAACD,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC;AAC/B;AAEA,SAASK,cAAcA,CAACC,IAAY,EAAEC,IAAY,EAAEP,GAAW;EAC7D,IAAIQ,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEV,GAAG,GAAGM,IAAI,CAACL,MAAM,GAAGM,IAAI,CAACN,MAAM,CAAC;EACzD,OAAOK,IAAI,GAAGP,MAAM,CAACS,MAAM,CAAC,GAAGD,IAAI;AACrC;AAEA,SAASI,aAAaA,CAACL,IAAY,EAAEC,IAAY,EAAEP,GAAW;EAC5D,IAAIQ,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEV,GAAG,GAAGM,IAAI,CAACL,MAAM,GAAGM,IAAI,CAACN,MAAM,CAAC;EACzD,OAAOK,IAAI,GAAGF,IAAI,CAACI,MAAM,CAAC,GAAGD,IAAI;AACnC;AAEA,SAASK,gBAAgBA,CAACC,eAA0B;EAClD,IAAI,CAACA,eAAe,CAACZ,MAAM,EAAE;IAC3B,OAAO,EAAE;EACX;EAEA,MAAM;IAAEA;EAAM,CAAE,GAAGY,eAAe;EAClC,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,GAAG,CAAC,EAAE,EAAEc,CAAC,EAAE;IACnC,MAAMC,UAAU,GAAGH,eAAe,CAACE,CAAC,CAAC;IACrCD,IAAI,IAAIE,UAAU,GAAG,KAAK,GAAG,KAAK;EACpC;EAEA,MAAMC,IAAI,GAAGJ,eAAe,CAACZ,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK;EAExD,OAAOa,IAAI,GAAGG,IAAI;AACpB;AAEA,SAASC,QAAQA,CAACC,CAAS;EACzB;EACA,OAAOC,MAAM,CAACX,IAAI,CAACY,KAAK,CAACF,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC,GAAG,KAAK;AAC5E;AAEA,SAASC,cAAcA,CAACC,KAAe;EACrC,OAAOA,KAAK,CAACvC,IAAI,CAAC,IAAI,CAAC;AACzB;AAEA,SAASwC,cAAcA,CAACC,GAAW;EACjC,OAAOA,GAAG,CAAC9C,KAAK,CAAC,IAAI,CAAC;AACxB;AAEA,IAAI+C,OAAO,GAAG,KAAK;AAEb,SAAU5D,OAAOA,CAIrB6D,UAAiD,EACjDC,CAAiD;EAEjD,IAAI,CAAC9D,OAAO,CAACQ,OAAO,EAAE;IACpB,OAAOsD,CAAC;EACV;EAEA,OAAOjC,MAAM,CAACkC,MAAM,CAAC,SAASC,cAAcA,CAAA;IAC1C,MAAMC,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACC,SAAS,CAAU;IAE3C,IAAI,CAACR,OAAO,EAAE;MACZ,OAAOE,CAAC,CAACO,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;IAC5B;IAEA,MAAMK,iBAAiB,GAAGC,MAAM,CAACC,4BAA4B;IAC7D,IAAIC,KAAK,GAAGH,iBAAiB,CAACI,QAAQ,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAE,CAAE;IAEhE,MAAMC,IAAI,GAAG,OAAOf,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACQ,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,GAAGJ,UAAU;IAEzF;IACA,MAAMgB,WAAW,GAAG;MAClBD,IAAI;MACJE,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAEC,IAAI,CAACC,GAAG;KACpB;IAED,IAAIC,2BAA2B,CAACP,IAAI,CAAC,EAAE;MACrCC,WAAW,CAACC,QAAQ,GAAGM,uBAAuB,CAACR,IAAI,CAAC;MAEpD,IAAIC,WAAW,CAACC,QAAQ,EAAE;QACxB,MAAMO,iBAAiB,GAAIvE,OAAe,IAAI;UAC5C,IAAI+D,WAAW,CAACC,QAAQ,IAAI,CAACD,WAAW,CAACE,WAAW,EAAE;YACpD,OAAOO,sBAAsB,CAACV,IAAI,EAAE,IAAI,CAAC,CAACW,KAAK,CAACC,GAAG,IAAG;cACpD/E,OAAO,CAACgF,MAAM,CAACC,KAAK,eAAAC,MAAA,CAAe7E,OAAO,kCAAA6E,MAAA,CAA+BH,GAAG,OAAI,CAAC;YACnF,CAAC,CAAC;UACJ;UACA,OAAOI,OAAO,CAACC,OAAO,EAAE;QAC1B,CAAC;QAEDpF,OAAO,CAACqF,EAAE,CAAC,MAAM,EAAE,MAAK;UAAGT,iBAAiB,CAAC,MAAM,CAAC;QAAE,CAAC,CAAC;QAExD,MAAMU,OAAO,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;QAC/CA,OAAO,CAACC,OAAO,CAACC,MAAM,IAAG;UACvBxF,OAAO,CAACyF,IAAI,CAACD,MAAM,EAAE,MAAK;YACxBZ,iBAAiB,CAAC,QAAQ,CAAC,CAACc,OAAO,CAAC,MAAK;cACvC1F,OAAO,CAAC2F,IAAI,CAAC,GAAG,CAAC;YACnB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,MAAMC,gBAAgB,GAAGA,CAAA,KAAK;MAC5B,IAAIxB,WAAW,CAACC,QAAQ,IAAI,CAACD,WAAW,CAACE,WAAW,EAAE;QACpDF,WAAW,CAACE,WAAW,GAAG,IAAI;QAC9B,OAAOO,sBAAsB,CAACV,IAAI,EAAE,IAAI,CAAC,CAACW,KAAK,CAACC,GAAG,IAAG;UACpD/E,OAAO,CAACgF,MAAM,CAACC,KAAK,mDAAAC,MAAA,CAAmDH,GAAG,CAAE,CAAC;QAC/E,CAAC,CAAC;MACJ;MACA,OAAOI,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC;IAED,SAASS,cAAcA,CAACC,MAAmC;MACzD,IAAI,EAAEA,MAAM,YAAYX,OAAO,CAAC,EAAE;QAChCS,gBAAgB,EAAE;MACpB;IACF;IAEA,IAAI;MACF,IAAI,CAAC/B,iBAAiB,CAACI,QAAQ,EAAE,EAAE;QACjC,MAAM6B,MAAM,GAAGjC,iBAAiB,CAACkC,GAAG,CAAC/B,KAAK,EAAE,MAC1CgC,cAAc,CAAC7B,IAAI,EAAEH,KAAK,EAAEX,CAAC,EAAE,IAAI,EAAEG,IAAI,EAAEoC,gBAAgB,CAAC,CAAC;QAE/D;QACAC,cAAc,CAACC,MAAM,CAAC;QACtB,OAAOA,MAAM;MACf;MAEA;MACA,MAAMA,MAAM,GAAGE,cAAc,CAAC7B,IAAI,EAAEH,KAAK,EAAEX,CAAC,EAAE,IAAI,EAAEG,IAAI,EAAEoC,gBAAgB,CAAC;MAE3E;MACAC,cAAc,CAACC,MAAM,CAAC;MACtB,OAAOA,MAAM;IACf,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdL,gBAAgB,EAAE;MAClB,MAAMK,KAAK;IACb;EACF,CAAC,EAAE5C,CAAC,CAAa;AACnB;AAEA;AACA;AACA;AACA,IAAI6C,eAAe,GAAG,KAAK;AAC3B,IAAIC,WAAW,GAA6B,IAAI;AAChD,IAAIC,eAAe,GAAkB,IAAI;AACzC,IAAIC,gBAAgB,GAAkB,IAAI;AAE1C,SAAS3B,2BAA2BA,CAACP,IAAY;EAC/C,IAAI,CAACrE,gBAAgB,CAACC,OAAO,EAAE,OAAO,KAAK;EAC3C,OAAOD,gBAAgB,CAACK,MAAM,CAACmG,QAAQ,CAACnC,IAAI,CAAC;AAC/C;AAEA,SAASQ,uBAAuBA,CAACR,IAAY;EAC3C,IAAI,CAACO,2BAA2B,CAACP,IAAI,CAAC,EAAE;IACtC,OAAO,KAAK;EACd;EAEA,IAAI;IACF,IAAIgC,WAAW,EAAE;MACf,OAAO,KAAK;IACd;IAEAE,gBAAgB,GAAG7B,IAAI,CAACC,GAAG,EAAE;IAE7B;IACA,IAAI,CAACyB,eAAe,EAAE;MACpB1G,SAAS,CAAC+G,IAAI,EAAE;MAChBL,eAAe,GAAG,IAAI;IACxB;IAEA;IACA,MAAMM,OAAO,GAAG,IAAIhH,SAAS,CAACiH,OAAO,EAAE;IACvCD,OAAO,CAACE,OAAO,EAAE;IACjBF,OAAO,CAACG,IAAI,CAAC,iBAAiB,CAAC;IAC/BH,OAAO,CAACG,IAAI,CAAC,gBAAgB,EAAE;MAC7BhG,gBAAgB,EAAEb,gBAAgB,CAACa;KACpC,CAAC;IAEF;IACAwF,WAAW,GAAGK,OAAO;IACrBJ,eAAe,GAAGjC,IAAI;IAEtB,OAAO,IAAI;EACb,CAAC,CAAC,OAAOY,GAAG,EAAE;IACZ/E,OAAO,CAACgF,MAAM,CAACC,KAAK,mDAAAC,MAAA,CAAmDf,IAAI,QAAAe,MAAA,CAAKH,GAAG,OAAI,CAAC;IACxF,OAAO,KAAK;EACd;AACF;AAEA,SAASF,sBAAsBA,CAACV,IAAY,EAAEE,QAAiB;EAC7D,IAAI,CAACA,QAAQ,IAAI,CAAC8B,WAAW,IAAIhC,IAAI,KAAKiC,eAAe,EAAE;IACzD,OAAOjB,OAAO,CAACC,OAAO,EAAE;EAC1B;EAEA,OAAO,IAAID,OAAO,CAAC,CAACC,OAAO,EAAEwB,MAAM,KAAI;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAGR,gBAAgB,GAAG7B,IAAI,CAACC,GAAG,EAAE,GAAG4B,gBAAgB,GAAG,CAAC;MACrE,MAAMG,OAAO,GAAGL,WAAW;MAC3B,IAAI,CAACK,OAAO,EAAE;QACZ,OAAOpB,OAAO,EAAE;MAClB;MAEAoB,OAAO,CAACG,IAAI,CAAC,eAAe,EAAE,CAAC5B,GAAiB,EAAEe,MAAW,KAAI;QAC/D,IAAIf,GAAG,EAAE;UACP+B,iBAAiB,CAAC1B,OAAO,CAAC;UAC1BwB,MAAM,CAAC7B,GAAG,CAAC;UACX;QACF;QAEA,IAAI;UAAA,IAAAgC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;UACF;UACA,IAAI,CAACnB,MAAM,IAAI,CAACA,MAAM,CAACoB,OAAO,EAAE;YAC9BC,OAAO,CAAClB,KAAK,uCAAAf,MAAA,CAAuCf,IAAI,CAAE,CAAC;YAC3D2C,iBAAiB,CAAC1B,OAAO,CAAC;YAC1B;UACF;UAEA;UACA,MAAMgC,UAAU,GAAGC,IAAI,CAACC,SAAS,CAACxB,MAAM,CAACoB,OAAO,CAAC;UACjD,MAAMK,WAAW,GAAGH,UAAU,CAAC3F,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;UAEvDzB,OAAO,CAACgF,MAAM,CAACC,KAAK,uDAAAC,MAAA,CAAuDf,IAAI,QAAAe,MAAA,CAAKmC,IAAI,CAACC,SAAS,CAAC;YACjGE,KAAK,EAAE,EAAAT,qBAAA,GAAAjB,MAAM,CAACoB,OAAO,CAACM,KAAK,cAAAT,qBAAA,uBAApBA,qBAAA,CAAsBtF,MAAM,KAAI,CAAC;YACxCgG,OAAO,EAAE,EAAAT,qBAAA,GAAAlB,MAAM,CAACoB,OAAO,CAACO,OAAO,cAAAT,qBAAA,uBAAtBA,qBAAA,CAAwBvF,MAAM,KAAI,CAAC;YAC5CiG,UAAU,EAAE,EAAAT,qBAAA,GAAAnB,MAAM,CAACoB,OAAO,CAACQ,UAAU,cAAAT,qBAAA,uBAAzBA,qBAAA,CAA2BxF,MAAM,KAAI,CAAC;YAClDoF,QAAQ,EAAEA,QAAQ;YAClBc,IAAI,EAAEJ,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC,GAAG;WAChC,CAAC,CAAE,CAAC;UAEL,IAAIL,WAAW,GAAGzH,gBAAgB,CAACiB,cAAc,EAAE;YACjDf,OAAO,CAACgF,MAAM,CAACC,KAAK,wCAAAC,MAAA,CAAwCqC,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC,WAAA1C,MAAA,CAAQpF,gBAAgB,CAACiB,cAAc,QAAK,CAAC;YAC/Hf,OAAO,CAACgF,MAAM,CAACC,KAAK,CAAC,gEAAgE,CAAC;YACtFjF,OAAO,CAACgF,MAAM,CAACC,KAAK,CAAC,wFAAwF,CAAC;YAE9G;YACA,IAAI;cAAA,IAAA4C,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cACF;cACA,MAAMC,cAAc,GAAG;gBACrBR,KAAK,EAAE,EAAAK,sBAAA,GAAA/B,MAAM,CAACoB,OAAO,CAACM,KAAK,cAAAK,sBAAA,uBAApBA,sBAAA,CAAsBnG,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAI,EAAE;gBAClD+F,OAAO,EAAE,EAAAK,sBAAA,GAAAhC,MAAM,CAACoB,OAAO,CAACO,OAAO,cAAAK,sBAAA,uBAAtBA,sBAAA,CAAwBpG,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAI,EAAE;gBACtDgG,UAAU,EAAE,EAAAK,sBAAA,GAAAjC,MAAM,CAACoB,OAAO,CAACQ,UAAU,cAAAK,sBAAA,uBAAzBA,sBAAA,CAA2BrG,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAI,EAAE;gBAC5D6C,SAAS,EAAEuB,MAAM,CAACoB,OAAO,CAAC3C,SAAS;gBACnC0D,OAAO,EAAEnC,MAAM,CAACoB,OAAO,CAACe,OAAO;gBAC/BC,QAAQ,EAAE;eACX;cAEDC,WAAW,CAACH,cAAc,EAAE7D,IAAI,KAAAe,MAAA,CAAKf,IAAI,eAAY0C,QAAQ,CAAC;YAChE,CAAC,CAAC,OAAOuB,SAAS,EAAE;cAClBpI,OAAO,CAACgF,MAAM,CAACC,KAAK,mDAAAC,MAAA,CAAmDkD,SAAS,CAAE,CAAC;YACrF;YAEAtB,iBAAiB,CAAC1B,OAAO,CAAC;YAC1B;UACF;UAEA,IAAI;YACF+C,WAAW,CAACrC,MAAM,CAACoB,OAAO,EAAE/C,IAAI,EAAEA,IAAI,EAAE0C,QAAQ,CAAC;UACnD,CAAC,CAAC,OAAOwB,OAAO,EAAE;YAChBrI,OAAO,CAACgF,MAAM,CAACC,KAAK,2CAAAC,MAAA,CAA2CmD,OAAO,CAAE,CAAC;UAC3E;UAEAvB,iBAAiB,CAAC1B,OAAO,CAAC;QAC5B,CAAC,CAAC,OAAOkD,UAAU,EAAE;UACnBtI,OAAO,CAACgF,MAAM,CAACC,KAAK,kDAAAC,MAAA,CAAkDf,IAAI,QAAAe,MAAA,CAAKoD,UAAU,CAAE,CAAC;UAC5FxB,iBAAiB,CAAC1B,OAAO,CAAC;UAC1BwB,MAAM,CAAC0B,UAAU,CAAC;QACpB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvD,GAAG,EAAE;MACZ/E,OAAO,CAACgF,MAAM,CAACC,KAAK,yDAAAC,MAAA,CAAyDf,IAAI,QAAAe,MAAA,CAAKH,GAAG,CAAE,CAAC;MAC5F+B,iBAAiB,CAAC1B,OAAO,CAAC;MAC1BwB,MAAM,CAAC7B,GAAG,CAAC;IACb;EACF,CAAC,CAAC;EAEF,SAAS+B,iBAAiBA,CAAC1B,OAAmD;IAC5E,IAAI;MACF,IAAIe,WAAW,EAAE;QACfA,WAAW,CAACQ,IAAI,CAAC,kBAAkB,CAAC;QACpCR,WAAW,CAACoC,UAAU,EAAE;MAC1B;MAEA,IAAIrC,eAAe,EAAE;QACnB1G,SAAS,CAACgJ,KAAK,EAAE;QACjBtC,eAAe,GAAG,KAAK;MACzB;MAEAC,WAAW,GAAG,IAAI;MAClBC,eAAe,GAAG,IAAI;MACtBC,gBAAgB,GAAG,IAAI;MAEvB;MACA,IAAI,OAAOvC,MAAM,CAAC2E,EAAE,KAAK,UAAU,EAAE;QACnC,IAAI;UACF3E,MAAM,CAAC2E,EAAE,EAAE;UACXzI,OAAO,CAACgF,MAAM,CAACC,KAAK,CAAC,0DAA0D,CAAC;QAClF,CAAC,CAAC,OAAOyD,KAAK,EAAE;UACd1I,OAAO,CAACgF,MAAM,CAACC,KAAK,wDAAAC,MAAA,CAAwDwD,KAAK,CAAE,CAAC;QACtF;MACF;MAEA,OAAOtD,OAAO,EAAE;IAClB,CAAC,CAAC,OAAOuD,UAAU,EAAE;MACnB3I,OAAO,CAACgF,MAAM,CAACC,KAAK,2CAAAC,MAAA,CAA2CyD,UAAU,CAAE,CAAC;MAC5E,OAAOvD,OAAO,EAAE;IAClB;EACF;AACF;AAEA,SAAS+C,WAAWA,CAACjB,OAAY,EAAE/C,IAAY,EAAEyE,QAAgB,EAAE/B,QAAgB;EACjF,IAAI,CAACjH,EAAE,CAACiJ,UAAU,CAAC/I,gBAAgB,CAACS,SAAS,CAAC,EAAE;IAC9CX,EAAE,CAACkJ,SAAS,CAAChJ,gBAAgB,CAACS,SAAS,EAAE;MAAEwI,SAAS,EAAE;IAAI,CAAE,CAAC;EAC/D;EAEA,MAAMC,SAAS,GAAG,IAAIxE,IAAI,EAAE,CAACyE,WAAW,EAAE,CAACnG,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;EAChE,MAAMoG,YAAY,GAAGN,QAAQ,CAAC9F,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;EACtD,MAAMqG,QAAQ,GAAGtJ,IAAI,CAACY,IAAI,CAACX,gBAAgB,CAACS,SAAS,KAAA2E,MAAA,CAAKgE,YAAY,OAAAhE,MAAA,CAAIpF,gBAAgB,CAACO,OAAO,OAAA6E,MAAA,CAAI8D,SAAS,gBAAa,CAAC;EAE7HpJ,EAAE,CAACwJ,aAAa,CAACD,QAAQ,EAAE9B,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,CAAC;EAEnD,MAAMK,WAAW,GAAGF,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,CAACzF,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC;EAElEzB,OAAO,CAACgF,MAAM,CAACC,KAAK,iCAAAC,MAAA,CAAiCf,IAAI,iBAAAe,MAAA,CAAciE,QAAQ,CAAE,CAAC;EAClFnJ,OAAO,CAACgF,MAAM,CAACC,KAAK,+BAAAC,MAAA,CAA+B2B,QAAQ,gBAAA3B,MAAA,CAAaqC,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC,OAAI,CAAC;AACrG;AAEA,SAAS5B,cAAcA,CACnB5C,UAAiD,EACjDY,KAAqD,EACrDX,CAAiD,EACjDhD,OAAY,EACZmD,IAAW,EACXoC,gBAAqC;EAEvC,MAAMzB,IAAI,GAAG,OAAOf,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACQ,KAAK,CAACvD,OAAO,EAAEmD,IAAa,CAAC,GAAGJ,UAAU;EACrGY,KAAK,CAACE,YAAY,GAAG,CAAC,IAAGF,KAAK,CAACE,YAAY,IAAI,EAAE,GAAEC,IAAI,CAAC;EACxD,MAAMjB,GAAG,GAAGH,cAAc,CAACiB,KAAK,CAACE,YAAY,CAAC;EAC9C,MAAMmF,KAAK,GAAGrJ,OAAO,CAACsJ,MAAM,EAAE;EAE9B,IAAIxD,MAAkC;EACtC,IAAI;IACFA,MAAM,GAAGzC,CAAC,CAACO,KAAK,CAACvD,OAAO,EAAEmD,IAAa,CAAC;IAExC,IAAIsC,MAAM,YAAYX,OAAO,EAAE;MAC7B;MACA,OAAOW,MAAM,CAACJ,OAAO,CAAC,MAAM6D,iBAAiB,CAACrG,GAAG,EAAEmG,KAAK,EAAErF,KAAK,CAACE,YAAY,EAAE0B,gBAAgB,CAAC,CAAC;IAClG;IAEA;IACA,OAAOE,MAAM;EACf,CAAC,SAAS;IACR,IAAI,EAAEA,MAAO,YAAYX,OAAO,CAAC,EAAE;MACjCoE,iBAAiB,CAACrG,GAAG,EAAEmG,KAAK,EAAErF,KAAK,CAACE,YAAY,EAAE0B,gBAAgB,CAAC;IACrE;EACF;AACF;AAEA,SAAS2D,iBAAiBA,CAACrG,GAAW,EAAEmG,KAAuB,EAAEnF,YAAsB,EAAE0B,gBAAqC;EAC5H,MAAM4D,OAAO,GAAGxJ,OAAO,CAACsJ,MAAM,CAACD,KAAK,CAAC;EACrC,MAAMI,KAAK,GAAItI,WAAW,CAAC+B,GAAG,CAAC,KAAK/B,WAAW,CAAC+B,GAAG,CAAC,GAAG;IACrDwG,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;GACV,CAAE;EACHH,KAAK,CAACC,IAAI,IAAIF,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS;EACxDC,KAAK,CAACE,KAAK,EAAE;EACbzF,YAAY,CAAC2F,GAAG,EAAE;EAClBjE,gBAAgB,EAAE;AACpB;AAEA,WAAiBrG,OAAO;EACXA,OAAA,CAAAQ,OAAO,GAAG,CAAC,CAAEC,OAAO,CAACC,GAAG,CAACiB,cAAc,IAAI,CAAC,CAAElB,OAAO,CAACC,GAAG,CAACC,cAAc;EAEnF,eAAe4J,SAASA,CAAUC,MAAc,EAAE1G,CAAgB;IAChE2G,WAAW,GAAGD,MAAM;IACpBE,KAAK,MAAA/E,MAAA,CAAMgF,SAAS,mBAAAhF,MAAA,CAAgB8E,WAAW,CAAE,CAAC;IAClDX,KAAK,EAAE;IACP,IAAI;MACF,OAAO,MAAMK,IAAI,CAACK,MAAM,EAAE1G,CAAC,CAAC;IAC9B,CAAC,SAAS;MACR8G,MAAM,EAAE;MACRD,SAAS,EAAE;IACb;EACF;EAEA,SAASE,QAAQA,CAAUL,MAAc,EAAE1G,CAAgB;IACzD2G,WAAW,GAAGD,MAAM;IACpBE,KAAK,MAAA/E,MAAA,CAAMgF,SAAS,mBAAAhF,MAAA,CAAgB8E,WAAW,CAAE,CAAC;IAClDX,KAAK,EAAE;IACP,IAAI;MACF,OAAOK,IAAI,CAACK,MAAM,EAAE1G,CAAC,CAAC;IACxB,CAAC,SAAS;MACR8G,MAAM,EAAE;MACRD,SAAS,EAAE;IACb;EACF;EAEA,SAAgBR,IAAIA,CAAUK,MAAc,EAAE1G,CAAgB;IAC5D,OAAO9D,OAAO,CAACwK,MAAM,EAAE1G,CAAC,CAAC,EAAE;EAC7B;EAFgB9D,OAAA,CAAAmK,IAAI,GAAAA,IAEnB;EAED,SAAgB3D,GAAGA,CAAUgE,MAAc,EAAE1G,CAAgB;IAC3D,IAAI,CAAE9D,OAAO,CAACQ,OAAO,EAAE;MACrB,OAAOsD,CAAC,EAAE;IACZ;IAEA,IAAIF,OAAO,EAAE;MACX;MACA;MACAgE,OAAO,CAACkD,GAAG,CAAC,iCAAiC,GAAGN,MAAM,CAAC;MACvD,OAAOL,IAAI,CAACK,MAAM,EAAE1G,CAAC,CAAC;IACxB;IAEA,MAAMiH,SAAS,GAAGjH,CAAC,CAACkH,WAAW,CAACpG,IAAI,KAAK,eAAe;IACxD,IAAI,CAACmG,SAAS,EAAE;MACd,OAAOF,QAAQ,CAACL,MAAM,EAAE1G,CAAC,CAAC;IAC5B;IAEA,OAAOyG,SAAS,CAACC,MAAM,EAAE1G,CAAC,CAAC;EAC7B;EAlBgB9D,OAAA,CAAAwG,GAAG,GAAAA,GAkBlB;EAED,SAASsD,KAAKA,CAAA;IACZlI,WAAW,GAAG,EAAE;IAChBgC,OAAO,GAAG,IAAI;EAChB;EAEA,IAAI6G,WAAmB;EACvB,IAAIE,SAAS,GAAG,CAAC;EACjB,SAASC,MAAMA,CAAA;IACb,IAAI,CAAE5K,OAAO,CAACQ,OAAO,EAAE;MACrB;IACF;IACAoD,OAAO,GAAG,KAAK;IACf8G,KAAK,CAAC,EAAE,CAAC;IACTO,WAAW,EAAE;IACbC,eAAe,EAAE;IACjBR,KAAK,CAAC,EAAE,CAAC;IACTS,eAAe,EAAE;IACjBT,KAAK,CAAC,EAAE,CAAC;IACTA,KAAK,CAAC,KAAA/E,MAAA,CAAKgF,SAAS,eAAAhF,MAAA,CAAYxC,QAAQ,CAACiI,gBAAgB,EAAE,CAAC,SAAAzF,MAAA,CACjD8E,WAAW,MAAG,CAAC;IAC1BC,KAAK,CAAC,EAAE,CAAC;EACX;AACF,CAAC,EAzEgB1K,OAAO,IAAAF,MAAA,CAAAuL,UAAA,CAAPrL,OAAO;AA4ExB,IAAIsL,OAAO,GAAY,EAAE;AAEzB,MAAMC,MAAM,GAAG,IAAI;AAEnB,SAASC,SAASA,CAAC/H,KAAY;EAC7B,OAAOA,KAAK,CAACA,KAAK,CAACvB,MAAM,GAAG,CAAC,CAAC;AAChC;AAEA,SAASuJ,UAAUA,CAAChI,KAAY;EAC9B,OAAO7B,WAAW,CAAC4B,cAAc,CAACC,KAAK,CAAC,CAAC;AAC3C;AAEA,SAASiI,SAASA,CAACjI,KAAY;EAC7B,OAAOgI,UAAU,CAAChI,KAAK,CAAC,CAAC0G,IAAI;AAC/B;AAEA,SAASwB,eAAeA,CAAClI,KAAY;EACnC,OAAOA,KAAK,CAACvB,MAAM,KAAK,CAAC;AAC3B;AAEA,SAAS0J,eAAeA,CAAA;EACtB,OAAON,OAAO,CAAC1K,MAAM,CAAC+K,eAAe,CAAC;AACxC;AAEA,SAASjB,KAAKA,CAACmB,IAAY;EACzBjE,OAAO,CAACkD,GAAG,CAACS,MAAM,GAAGM,IAAI,CAAC;AAC5B;AAEA,SAASC,OAAOA,CAACC,MAAa,EAAEC,MAAa;EAC3C,IAAIA,MAAM,CAAC9J,MAAM,KAAK6J,MAAM,CAAC7J,MAAM,GAAG,CAAC,EAAE;IACvC,OAAO,KAAK;EACd;EACA,KAAK,IAAIc,CAAC,GAAG+I,MAAM,CAAC7J,MAAM,GAAG,CAAC,EAAEc,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,IAAI+I,MAAM,CAAC/I,CAAC,CAAC,KAAKgJ,MAAM,CAAChJ,CAAC,CAAC,EAAE;MAC3B,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASiJ,QAAQA,CAACF,MAAa;EAC7B,OAAOT,OAAO,CAAC1K,MAAM,CAACoL,MAAM,IAAIF,OAAO,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAC;AAC1D;AAEA,SAASE,WAAWA,CAACzI,KAAY;EAC/B,OAAOwI,QAAQ,CAACxI,KAAK,CAAC,CAACvB,MAAM,GAAG,CAAC;AACnC;AAEA,SAASiK,sBAAsBA,CAAC1I,KAAY;EAC1C,OAAOwI,QAAQ,CAACxI,KAAK,CAAC,CAAC2I,IAAI,CAAC3I,KAAK,IAAIiI,SAAS,CAACjI,KAAK,CAAC,IAAI7C,MAAM,CAAC;AAClE;AAEA,SAASyL,MAAMA,CAAC5I,KAAY;EAC1B,OAAO,CAAEyI,WAAW,CAACzI,KAAK,CAAC;AAC7B;AAEA,SAAS6I,SAASA,CAAC7I,KAAY;EAC7B,IAAI8I,KAAK,GAAG,CAAC;EACbN,QAAQ,CAACxI,KAAK,CAAC,CAACuC,OAAO,CAACwG,KAAK,IAAG;IAC9BD,KAAK,IAAIb,SAAS,CAACc,KAAK,CAAC;EAC3B,CAAC,CAAC;EACF,OAAOd,SAAS,CAACjI,KAAK,CAAC,GAAG8I,KAAK;AACjC;AAEA,SAASE,eAAeA,CAAChJ,KAAY;EACnC,MAAMiJ,KAAK,GAAUjJ,KAAK,CAACtB,KAAK,CAAC,CAAC,CAAC;EACnCuK,KAAK,CAACC,IAAI,CAAC,QAAQ,GAAGnB,SAAS,CAAC/H,KAAK,CAAC,CAAC;EACvC7B,WAAW,CAAC4B,cAAc,CAACkJ,KAAK,CAAC,CAAC,GAAG;IACnCvC,IAAI,EAAEmC,SAAS,CAAC7I,KAAK,CAAC;IACtB2G,KAAK,EAAEqB,UAAU,CAAChI,KAAK,CAAC,CAAC2G,KAAK;IAC9BC,OAAO,EAAE;GACV;EACDiB,OAAO,CAACqB,IAAI,CAACD,KAAK,CAAC;AACrB;AAAC;AAED,SAASE,QAAQA,CAACnJ,KAAY,EAAiC;EAAA,IAA/BX,eAAA,GAAAsB,SAAA,CAAAlC,MAAA,QAAAkC,SAAA,QAAA7C,SAAA,GAAA6C,SAAA,MAA6B,EAAE;EAC7D,MAAM8F,KAAK,GAAGuB,UAAU,CAAChI,KAAK,CAAC;EAC/B,MAAMoJ,QAAQ,GAAGV,sBAAsB,CAAC1I,KAAK,CAAC;EAC9C,MAAMmB,IAAI,GAAG4G,SAAS,CAAC/H,KAAK,CAAC;EAE7BiH,KAAK,CAAC,CAACmC,QAAQ,GAAGjK,aAAa,GAAGN,cAAc,EACzCO,gBAAgB,CAACC,eAAe,CAAC,GAAG8B,IAAI,EAAEzB,QAAQ,CAAC+G,KAAK,CAACC,IAAI,CAAC,EAAE,EAAE,CAAC,IACjED,KAAK,CAACG,OAAO,GAAG,EAAE,GAAI,IAAI,GAAGH,KAAK,CAACE,KAAK,GAAG,GAAI,CAAC,CAAC;EAE1D,IAAIyC,QAAQ,EAAE;IACZ,MAAMC,YAAY,GAAGb,QAAQ,CAACxI,KAAK,CAAC,CAAC7C,MAAM,CAAC6C,KAAK,IAAG;MAClD,OAAOgI,UAAU,CAAChI,KAAK,CAAC,CAAC0G,IAAI,GAAGvJ,MAAM;IACxC,CAAC,CAAC;IACFkM,YAAY,CAAC9G,OAAO,CAAC,CAACwG,KAAK,EAAExJ,CAAC,KAAI;MAChC,MAAMC,UAAU,GAAGD,CAAC,KAAK8J,YAAY,CAAC5K,MAAM,GAAG,CAAC;MAChD0K,QAAQ,CAACJ,KAAK,EAAE1J,eAAe,CAAC6C,MAAM,CAAC1C,UAAU,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ;AACF;AAEA,SAASiI,eAAeA,CAAA;EACtBU,eAAe,EAAE,CAAC5F,OAAO,CAACvC,KAAK,IAAImJ,QAAQ,CAACnJ,KAAK,CAAC,CAAC;AACrD;AAEA,SAASsJ,QAAQA,CAAA;EACf,MAAMC,GAAG,GAA4BnL,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACxDwJ,OAAO,CAAC1K,MAAM,CAACyL,MAAM,CAAC,CAACY,GAAG,CAACzB,SAAS,CAAC,CAACxF,OAAO,CAACpB,IAAI,IAAIoI,GAAG,CAACpI,IAAI,CAAC,GAAG,IAAI,CAAC;EACvE,OAAO/C,MAAM,CAACqL,IAAI,CAACF,GAAG,CAAC,CAACG,IAAI,EAAE;AAChC;AAEA,SAASC,UAAUA,CAACC,QAAgB;EAClC,IAAIlD,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC;EAEbkB,OAAO,CAAC1K,MAAM,CAAC6C,KAAK,IAAG;IACrB,OAAO+H,SAAS,CAAC/H,KAAK,CAAC,KAAK4J,QAAQ,IAAIhB,MAAM,CAAC5I,KAAK,CAAC;EACvD,CAAC,CAAC,CAACuC,OAAO,CAACsH,IAAI,IAAG;IAChB,MAAMpD,KAAK,GAAGuB,UAAU,CAAC6B,IAAI,CAAC;IAC9BnD,IAAI,IAAID,KAAK,CAACC,IAAI;IAClBC,KAAK,IAAIF,KAAK,CAACE,KAAK;EACtB,CAAC,CAAC;EAEF,OAAO;IAAED,IAAI;IAAEC;EAAK,CAAE;AACxB;AAEA,SAASe,eAAeA,CAAA;EACtBT,KAAK,CAAC,aAAa,CAAC;EAEpB,MAAM6C,MAAM,GAAGR,QAAQ,EAAE,CAACE,GAAG,CAACK,IAAI,IAAG;IACnC,MAAME,IAAI,GAAGJ,UAAU,CAACE,IAAI,CAAC;IAC7B,OAAO;MACL1I,IAAI,EAAE0I,IAAI;MACVnD,IAAI,EAAEqD,IAAI,CAACrD,IAAI;MACfC,KAAK,EAAEoD,IAAI,CAACpD;KACb;EACH,CAAC,CAAC,CAAC+C,IAAI,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAI;IACf,OAAOD,CAAC,CAACtD,IAAI,KAAKuD,CAAC,CAACvD,IAAI,GAAG,CAAC,GAAGsD,CAAC,CAACtD,IAAI,GAAGuD,CAAC,CAACvD,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EACzD,CAAC,CAAC;EAEFoD,MAAM,CAACvH,OAAO,CAACuG,KAAK,IAAG;IACrB,IAAIA,KAAK,CAACpC,IAAI,GAAG,GAAG,EAAE;MAAE;MACtB;IACF;IACAO,KAAK,CAAC9H,aAAa,CAAC2J,KAAK,CAAC3H,IAAI,EAAEzB,QAAQ,CAACoJ,KAAK,CAACpC,IAAI,CAAC,EAAE,EAAE,CAAC,QAAAxE,MAAA,CAAQ4G,KAAK,CAACnC,KAAK,MAAG,CAAC;EAClF,CAAC,CAAC;AACJ;AAEA,SAASgB,gBAAgBA,CAAA;EACvB,IAAIuC,QAAQ,GAAG,CAAC;EAChB/B,eAAe,EAAE,CAAC5F,OAAO,CAACvC,KAAK,IAAG;IAChCkK,QAAQ,IAAIjC,SAAS,CAACjI,KAAK,CAAC;EAC9B,CAAC,CAAC;EACF,OAAOkK,QAAQ;AACjB;AAEA,SAAS1C,WAAWA,CAAA;EAClBK,OAAO,GAAGzJ,MAAM,CAACqL,IAAI,CAACtL,WAAW,CAAC,CAACqL,GAAG,CAACvJ,cAAc,CAAC;EACtD4H,OAAO,CAAC1K,MAAM,CAACuL,sBAAsB,CAAC,CAACnG,OAAO,CAAC4H,MAAM,IAAG;IACtDnB,eAAe,CAACmB,MAAM,CAAC;EACzB,CAAC,CAAC;AACJ", "ignoreList": [], "file": "tools/tool-env/profile.js.map"}