{"version": 3, "sources": ["meteor://💻app/packages/boilerplate-generator/generator.js", "meteor://💻app/packages/boilerplate-generator/template-web.browser.js", "meteor://💻app/packages/boilerplate-generator/template-web.cordova.js", "meteor://💻app/packages/boilerplate-generator/template.js"], "names": ["_objectSpread", "module", "link", "default", "v", "export", "Boilerplate", "readFileSync", "createStream", "create", "WebBrowserTemplate", "WebCordovaTemplate", "__reifyWaitForDeps__", "readUtf8FileSync", "filename", "identity", "value", "appendToStream", "chunk", "stream", "append", "<PERSON><PERSON><PERSON>", "from", "<PERSON><PERSON><PERSON><PERSON>", "read", "constructor", "arch", "manifest", "options", "arguments", "length", "undefined", "headTemplate", "closeTemplate", "getTemplate", "baseData", "_generateBoilerplateFromManifest", "toHTML", "extraData", "Error", "toHTMLAsync", "Promise", "resolve", "reject", "toHTMLStream", "chunks", "on", "push", "concat", "toString", "data", "start", "body", "dynamicBody", "end", "response", "urlMapper", "pathMapper", "baseDataExtension", "inline", "boilerplateBaseData", "css", "js", "head", "meteorManifest", "JSON", "stringify", "for<PERSON>ach", "item", "url<PERSON><PERSON>", "url", "itemObj", "scriptContent", "path", "sri", "type", "where", "startsWith", "prefix", "split", "join", "__reify_async_result__", "_reifyError", "self", "async", "template", "mode", "_ref", "htmlAttributes", "bundledJsCssUrlRewriteHook", "sriMode", "dynamicHead", "headSections", "cssBundle", "map", "file", "href", "Object", "keys", "key", "attrName", "attrValue", "_ref2", "meteorRuntimeConfig", "meteorRuntimeHash", "rootUrlPathPrefix", "inlineScriptsAllowed", "additionalStaticJs", "conf", "src", "hash", "_ref3", "contents", "pathname", "lodashTemplate", "text", "evaluate", "interpolate", "escape"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGH,MAAM,CAACI,MAAM,CAAC;MAACC,WAAW,EAACA,CAAA,KAAIA;IAAW,CAAC,CAAC;IAAC,IAAIC,YAAY;IAACN,MAAM,CAACC,IAAI,CAAC,IAAI,EAAC;MAACK,YAAYA,CAACH,CAAC,EAAC;QAACG,YAAY,GAACH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,YAAY;IAACP,MAAM,CAACC,IAAI,CAAC,kBAAkB,EAAC;MAACO,MAAMA,CAACL,CAAC,EAAC;QAACI,YAAY,GAACJ,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIM,kBAAkB;IAACT,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACM,kBAAkB,GAACN,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIO,kBAAkB;IAACV,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACO,kBAAkB,GAACP,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAMnc;IACA,MAAMC,gBAAgB,GAAGC,QAAQ,IAAIP,YAAY,CAACO,QAAQ,EAAE,MAAM,CAAC;IAEnE,MAAMC,QAAQ,GAAGC,KAAK,IAAIA,KAAK;IAE/B,SAASC,cAAcA,CAACC,KAAK,EAAEC,MAAM,EAAE;MACrC,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;QAC7BC,MAAM,CAACC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACJ,KAAK,EAAE,MAAM,CAAC,CAAC;MAC3C,CAAC,MAAM,IAAIG,MAAM,CAACE,QAAQ,CAACL,KAAK,CAAC,IACtB,OAAOA,KAAK,CAACM,IAAI,KAAK,UAAU,EAAE;QAC3CL,MAAM,CAACC,MAAM,CAACF,KAAK,CAAC;MACtB;IACF;IAEO,MAAMZ,WAAW,CAAC;MACvBmB,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAgB;QAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;QACtC,MAAM;UAAEG,YAAY;UAAEC;QAAc,CAAC,GAAGC,WAAW,CAACR,IAAI,CAAC;QACzD,IAAI,CAACM,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACC,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACE,QAAQ,GAAG,IAAI;QAEpB,IAAI,CAACC,gCAAgC,CACnCT,QAAQ,EACRC,OACF,CAAC;MACH;MAEAS,MAAMA,CAACC,SAAS,EAAE;QAChB,MAAM,IAAIC,KAAK,CACb,kDAAkD,GAChD,8CACJ,CAAC;MACH;;MAEA;MACAC,WAAWA,CAACF,SAAS,EAAE;QACrB,OAAO,IAAIG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACtC,MAAMxB,MAAM,GAAG,IAAI,CAACyB,YAAY,CAACN,SAAS,CAAC;UAC3C,MAAMO,MAAM,GAAG,EAAE;UACjB1B,MAAM,CAAC2B,EAAE,CAAC,MAAM,EAAE5B,KAAK,IAAI2B,MAAM,CAACE,IAAI,CAAC7B,KAAK,CAAC,CAAC;UAC9CC,MAAM,CAAC2B,EAAE,CAAC,KAAK,EAAE,MAAM;YACrBJ,OAAO,CAACrB,MAAM,CAAC2B,MAAM,CAACH,MAAM,CAAC,CAACI,QAAQ,CAAC,MAAM,CAAC,CAAC;UACjD,CAAC,CAAC;UACF9B,MAAM,CAAC2B,EAAE,CAAC,OAAO,EAAEH,MAAM,CAAC;QAC5B,CAAC,CAAC;MACJ;;MAEA;MACA;MACA;MACA;MACA;MACAC,YAAYA,CAACN,SAAS,EAAE;QACtB,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAI,CAAC,IAAI,CAACH,YAAY,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;UAC/D,MAAM,IAAIM,KAAK,CAAC,4CAA4C,CAAC;QAC/D;QAEA,MAAMW,IAAI,GAAAlD,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACmC,QAAQ,GAAKG,SAAS,CAAC;QAC7C,MAAMa,KAAK,GAAG,mBAAmB,GAAG,IAAI,CAACnB,YAAY,CAACkB,IAAI,CAAC;QAE3D,MAAM;UAAEE,IAAI;UAAEC;QAAY,CAAC,GAAGH,IAAI;QAElC,MAAMI,GAAG,GAAG,IAAI,CAACrB,aAAa,CAACiB,IAAI,CAAC;QACpC,MAAMK,QAAQ,GAAG/C,YAAY,CAAC,CAAC;QAE/BS,cAAc,CAACkC,KAAK,EAAEI,QAAQ,CAAC;QAE/B,IAAIH,IAAI,EAAE;UACRnC,cAAc,CAACmC,IAAI,EAAEG,QAAQ,CAAC;QAChC;QAEA,IAAIF,WAAW,EAAE;UACfpC,cAAc,CAACoC,WAAW,EAAEE,QAAQ,CAAC;QACvC;QAEAtC,cAAc,CAACqC,GAAG,EAAEC,QAAQ,CAAC;QAE7B,OAAOA,QAAQ;MACjB;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACAnB,gCAAgCA,CAACT,QAAQ,EAKjC;QAAA,IALmC;UACzC6B,SAAS,GAAGzC,QAAQ;UACpB0C,UAAU,GAAG1C,QAAQ;UACrB2C,iBAAiB;UACjBC;QACF,CAAC,GAAA9B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;QAEJ,MAAM+B,mBAAmB,GAAA5D,aAAA;UACvB6D,GAAG,EAAE,EAAE;UACPC,EAAE,EAAE,EAAE;UACNC,IAAI,EAAE,EAAE;UACRX,IAAI,EAAE,EAAE;UACRY,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACvC,QAAQ;QAAC,GACrC+B,iBAAiB,CACrB;QAED/B,QAAQ,CAACwC,OAAO,CAACC,IAAI,IAAI;UACvB,MAAMC,OAAO,GAAGb,SAAS,CAACY,IAAI,CAACE,GAAG,CAAC;UACnC,MAAMC,OAAO,GAAG;YAAED,GAAG,EAAED;UAAQ,CAAC;UAEhC,IAAIV,MAAM,EAAE;YACVY,OAAO,CAACC,aAAa,GAAG3D,gBAAgB,CACtC4C,UAAU,CAACW,IAAI,CAACK,IAAI,CAAC,CAAC;YACxBF,OAAO,CAACZ,MAAM,GAAG,IAAI;UACvB,CAAC,MAAM,IAAIS,IAAI,CAACM,GAAG,EAAE;YACnBH,OAAO,CAACG,GAAG,GAAGN,IAAI,CAACM,GAAG;UACxB;UAEA,IAAIN,IAAI,CAACO,IAAI,KAAK,KAAK,IAAIP,IAAI,CAACQ,KAAK,KAAK,QAAQ,EAAE;YAClDhB,mBAAmB,CAACC,GAAG,CAACd,IAAI,CAACwB,OAAO,CAAC;UACvC;UAEA,IAAIH,IAAI,CAACO,IAAI,KAAK,IAAI,IAAIP,IAAI,CAACQ,KAAK,KAAK,QAAQ;UAC/C;UACA;UACA,CAACR,IAAI,CAACK,IAAI,CAACI,UAAU,CAAC,UAAU,CAAC,EAAE;YACnCjB,mBAAmB,CAACE,EAAE,CAACf,IAAI,CAACwB,OAAO,CAAC;UACtC;UAEA,IAAIH,IAAI,CAACO,IAAI,KAAK,MAAM,EAAE;YACxBf,mBAAmB,CAACG,IAAI,GACtBlD,gBAAgB,CAAC4C,UAAU,CAACW,IAAI,CAACK,IAAI,CAAC,CAAC;UAC3C;UAEA,IAAIL,IAAI,CAACO,IAAI,KAAK,MAAM,EAAE;YACxBf,mBAAmB,CAACR,IAAI,GACtBvC,gBAAgB,CAAC4C,UAAU,CAACW,IAAI,CAACK,IAAI,CAAC,CAAC;UAC3C;QACF,CAAC,CAAC;QAEF,IAAI,CAACtC,QAAQ,GAAGyB,mBAAmB;MACrC;IACF;IAAC;;IAED;IACA;IACA,SAAS1B,WAAWA,CAACR,IAAI,EAAE;MACzB,MAAMoD,MAAM,GAAGpD,IAAI,CAACqD,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAE3C,IAAIF,MAAM,KAAK,aAAa,EAAE;QAC5B,OAAOpE,kBAAkB;MAC3B;MAEA,IAAIoE,MAAM,KAAK,aAAa,EAAE;QAC5B,OAAOnE,kBAAkB;MAC3B;MAEA,MAAM,IAAI4B,KAAK,CAAC,oBAAoB,GAAGb,IAAI,CAAC;IAC9C;IAACuD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICjKDnF,MAAM,CAACI,MAAM,CAAC;MAAC2B,YAAY,EAACA,CAAA,KAAIA,YAAY;MAACC,aAAa,EAACA,CAAA,KAAIA;IAAa,CAAC,CAAC;IAAC,IAAIoD,QAAQ;IAACpF,MAAM,CAACC,IAAI,CAAC,YAAY,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACiF,QAAQ,GAACjF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAE7M,MAAM8D,GAAG,GAAGA,CAACA,GAAG,EAAEY,IAAI,KACnBZ,GAAG,IAAIY,IAAI,0BAAAtC,MAAA,CAA0B0B,GAAG,uBAAA1B,MAAA,CAAkBsC,IAAI,UAAM,EAAE;IAElE,MAAMtD,YAAY,GAAGuD,IAAA,IAOtB;MAAA,IAPuB;QAC3B1B,GAAG;QACH2B,cAAc;QACdC,0BAA0B;QAC1BC,OAAO;QACP3B,IAAI;QACJ4B;MACF,CAAC,GAAAJ,IAAA;MACC,IAAIK,YAAY,GAAG7B,IAAI,CAACgB,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC;MAC9D,IAAIc,SAAS,GAAG,CAAC,GAAG,CAAChC,GAAG,IAAI,EAAE,EAAEiC,GAAG,CAACC,IAAI,IACtCV,QAAQ,CAAC,+FAA+F,CAAC,CAAC;QACxGW,IAAI,EAAEP,0BAA0B,CAACM,IAAI,CAACzB,GAAG,CAAC;QAC1CI,GAAG,EAAEA,GAAG,CAACqB,IAAI,CAACrB,GAAG,EAAEgB,OAAO;MAC5B,CAAC,CACH,CAAC,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC;MAEb,OAAO,CACL,OAAO,GAAGiB,MAAM,CAACC,IAAI,CAACV,cAAc,IAAI,CAAC,CAAC,CAAC,CAACM,GAAG,CAC7CK,GAAG,IAAId,QAAQ,CAAC,qCAAqC,CAAC,CAAC;QACrDe,QAAQ,EAAED,GAAG;QACbE,SAAS,EAAEb,cAAc,CAACW,GAAG;MAC/B,CAAC,CACH,CAAC,CAACnB,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,EAEhB,QAAQ,EAEPY,YAAY,CAAC9D,MAAM,KAAK,CAAC,GACtB,CAAC+D,SAAS,EAAED,YAAY,CAAC,CAAC,CAAC,CAAC,CAACZ,IAAI,CAAC,IAAI,CAAC,GACvC,CAACY,YAAY,CAAC,CAAC,CAAC,EAAEC,SAAS,EAAED,YAAY,CAAC,CAAC,CAAC,CAAC,CAACZ,IAAI,CAAC,IAAI,CAAC,EAE5DW,WAAW,EACX,SAAS,EACT,QAAQ,CACT,CAACX,IAAI,CAAC,IAAI,CAAC;IACd,CAAC;IAGM,MAAM/C,aAAa,GAAGqE,KAAA;MAAA,IAAC;QAC5BC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,oBAAoB;QACpB5C,EAAE;QACF6C,kBAAkB;QAClBlB,0BAA0B;QAC1BC;MACF,CAAC,GAAAY,KAAA;MAAA,OAAK,CACJ,EAAE,EACFI,oBAAoB,GAChBrB,QAAQ,CAAC,mHAAmH,CAAC,CAAC;QAC9HuB,IAAI,EAAEL;MACR,CAAC,CAAC,GACAlB,QAAQ,CAAC,uGAAuG,CAAC,CAAC;QAClHwB,GAAG,EAAEJ,iBAAiB;QACtBK,IAAI,EAAEN;MACR,CAAC,CAAC,EACJ,EAAE,EAEF,GAAG,CAAC1C,EAAE,IAAI,EAAE,EAAEgC,GAAG,CAACC,IAAI,IACpBV,QAAQ,CAAC,uEAAuE,CAAC,CAAC;QAChFwB,GAAG,EAAEpB,0BAA0B,CAACM,IAAI,CAACzB,GAAG,CAAC;QACzCI,GAAG,EAAEA,GAAG,CAACqB,IAAI,CAACrB,GAAG,EAAEgB,OAAO;MAC5B,CAAC,CACH,CAAC,EAED,GAAG,CAACiB,kBAAkB,IAAI,EAAE,EAAEb,GAAG,CAACiB,KAAA;QAAA,IAAC;UAAEC,QAAQ;UAAEC;QAAS,CAAC,GAAAF,KAAA;QAAA,OACvDL,oBAAoB,GAChBrB,QAAQ,CAAC,oCAAoC,CAAC,CAAC;UAC/C2B;QACF,CAAC,CAAC,GACA3B,QAAQ,CAAC,6DAA6D,CAAC,CAAC;UACxEwB,GAAG,EAAEJ,iBAAiB,GAAGQ;QAC3B,CAAC,CAAC;MAAA,CACL,CAAC,EAEF,EAAE,EACF,EAAE,EACF,SAAS,EACT,SAAS,CACV,CAACjC,IAAI,CAAC,IAAI,CAAC;IAAA;IAACC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;ICpFbnF,MAAM,CAACI,MAAM,CAAC;MAAC2B,YAAY,EAACA,CAAA,KAAIA,YAAY;MAACC,aAAa,EAACA,CAAA,KAAIA;IAAa,CAAC,CAAC;IAAC,IAAIoD,QAAQ;IAACpF,MAAM,CAACC,IAAI,CAAC,YAAY,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACiF,QAAQ,GAACjF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAGtM,MAAMoB,YAAY,GAAGuD,IAAA,IAWtB;MAAA,IAXuB;QAC3BgB,mBAAmB;QACnBE,iBAAiB;QACjBC,oBAAoB;QACpB7C,GAAG;QACHC,EAAE;QACF6C,kBAAkB;QAClBnB,cAAc;QACdC,0BAA0B;QAC1B1B,IAAI;QACJ4B;MACF,CAAC,GAAAJ,IAAA;MACC,IAAIK,YAAY,GAAG7B,IAAI,CAACgB,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC;MAC9D,IAAIc,SAAS,GAAG;MACd;MACA,GAAG,CAAChC,GAAG,IAAI,EAAE,EAAEiC,GAAG,CAACC,IAAI,IACrBV,QAAQ,CAAC,qFAAqF,CAAC,CAAC;QAC9FW,IAAI,EAAED,IAAI,CAACzB;MACb,CAAC,CACL,CAAC,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;MAEb,OAAO,CACL,QAAQ,EACR,QAAQ,EACR,0BAA0B,EAC1B,yDAAyD,EACzD,sKAAsK,EACtK,0DAA0D,EAC1D,kKAAkK,EAEnKY,YAAY,CAAC9D,MAAM,KAAK,CAAC,GACtB,CAAC+D,SAAS,EAAED,YAAY,CAAC,CAAC,CAAC,CAAC,CAACZ,IAAI,CAAC,IAAI,CAAC,GACvC,CAACY,YAAY,CAAC,CAAC,CAAC,EAAEC,SAAS,EAAED,YAAY,CAAC,CAAC,CAAC,CAAC,CAACZ,IAAI,CAAC,IAAI,CAAC,EAE1D,mCAAmC,EACnCK,QAAQ,CAAC,8EAA8E,CAAC,CAAC;QACvFuB,IAAI,EAAEL;MACR,CAAC,CAAC,EACF,iDAAiD;MACjD;MACA;MACA;MACA,uDAAuD,EACvD,gIAAgI,EAChI,oKAAoK,EACpK,SAAS,EACT,OAAO,EACP,aAAa,EACb,EAAE,EACF,8DAA8D,EAE9D,GAAG,CAACzC,EAAE,IAAI,EAAE,EAAEgC,GAAG,CAACC,IAAI,IACpBV,QAAQ,CAAC,6DAA6D,CAAC,CAAC;QACtEwB,GAAG,EAAEd,IAAI,CAACzB;MACZ,CAAC,CACH,CAAC,EAED,GAAG,CAACqC,kBAAkB,IAAI,EAAE,EAAEb,GAAG,CAACQ,KAAA;QAAA,IAAC;UAAEU,QAAQ;UAAEC;QAAS,CAAC,GAAAX,KAAA;QAAA,OACvDI,oBAAoB,GAChBrB,QAAQ,CAAC,oCAAoC,CAAC,CAAC;UAC/C2B;QACF,CAAC,CAAC,GACA3B,QAAQ,CAAC,6DAA6D,CAAC,CAAC;UACxEwB,GAAG,EAAEJ,iBAAiB,GAAGQ;QAC3B,CAAC,CAAC;MAAA,CACL,CAAC,EACF,EAAE,EACF,SAAS,EACT,EAAE,EACF,QAAQ,CACT,CAACjC,IAAI,CAAC,IAAI,CAAC;IACd,CAAC;IAEM,SAAS/C,aAAaA,CAAA,EAAG;MAC9B,OAAO,kBAAkB;IAC3B;IAACgD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC9EDnF,MAAM,CAACI,MAAM,CAAC;MAACF,OAAO,EAACA,CAAA,KAAIkF;IAAQ,CAAC,CAAC;IAAC,IAAI6B,cAAc;IAACjH,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAAC8G,cAAc,GAAC9G,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAIQ,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAOtK,SAASyE,QAAQA,CAAC8B,IAAI,EAAE;MACrC,OAAOD,cAAc,CAACC,IAAI,EAAE,IAAI,EAAE;QAChCC,QAAQ,EAAM,iBAAiB;QAC/BC,WAAW,EAAG,kBAAkB;QAChCC,MAAM,EAAQ;MAChB,CAAC,CAAC;IACJ;IAAC;IAACrC,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/boilerplate-generator.js", "sourcesContent": ["import {readFileSync} from 'fs';\nimport { create as createStream } from \"combined-stream2\";\n\nimport WebBrowserTemplate from './template-web.browser';\nimport WebCordovaTemplate from './template-web.cordova';\n\n// Copied from webapp_server\nconst readUtf8FileSync = filename => readFileSync(filename, 'utf8');\n\nconst identity = value => value;\n\nfunction appendToStream(chunk, stream) {\n  if (typeof chunk === \"string\") {\n    stream.append(Buffer.from(chunk, \"utf8\"));\n  } else if (Buffer.isBuffer(chunk) ||\n             typeof chunk.read === \"function\") {\n    stream.append(chunk);\n  }\n}\n\nexport class Boilerplate {\n  constructor(arch, manifest, options = {}) {\n    const { headTemplate, closeTemplate } = getTemplate(arch);\n    this.headTemplate = headTemplate;\n    this.closeTemplate = closeTemplate;\n    this.baseData = null;\n\n    this._generateBoilerplateFromManifest(\n      manifest,\n      options\n    );\n  }\n\n  toHTML(extraData) {\n    throw new Error(\n      \"The Boilerplate#toHTML method has been removed. \" +\n        \"Please use Boilerplate#toHTMLStream instead.\"\n    );\n  }\n\n  // Returns a Promise that resolves to a string of HTML.\n  toHTMLAsync(extraData) {\n    return new Promise((resolve, reject) => {\n      const stream = this.toHTMLStream(extraData);\n      const chunks = [];\n      stream.on(\"data\", chunk => chunks.push(chunk));\n      stream.on(\"end\", () => {\n        resolve(Buffer.concat(chunks).toString(\"utf8\"));\n      });\n      stream.on(\"error\", reject);\n    });\n  }\n\n  // The 'extraData' argument can be used to extend 'self.baseData'. Its\n  // purpose is to allow you to specify data that you might not know at\n  // the time that you construct the Boilerplate object. (e.g. it is used\n  // by 'webapp' to specify data that is only known at request-time).\n  // this returns a stream\n  toHTMLStream(extraData) {\n    if (!this.baseData || !this.headTemplate || !this.closeTemplate) {\n      throw new Error('Boilerplate did not instantiate correctly.');\n    }\n\n    const data = {...this.baseData, ...extraData};\n    const start = \"<!DOCTYPE html>\\n\" + this.headTemplate(data);\n\n    const { body, dynamicBody } = data;\n\n    const end = this.closeTemplate(data);\n    const response = createStream();\n\n    appendToStream(start, response);\n\n    if (body) {\n      appendToStream(body, response);\n    }\n\n    if (dynamicBody) {\n      appendToStream(dynamicBody, response);\n    }\n\n    appendToStream(end, response);\n\n    return response;\n  }\n\n  // XXX Exported to allow client-side only changes to rebuild the boilerplate\n  // without requiring a full server restart.\n  // Produces an HTML string with given manifest and boilerplateSource.\n  // Optionally takes urlMapper in case urls from manifest need to be prefixed\n  // or rewritten.\n  // Optionally takes pathMapper for resolving relative file system paths.\n  // Optionally allows to override fields of the data context.\n  _generateBoilerplateFromManifest(manifest, {\n    urlMapper = identity,\n    pathMapper = identity,\n    baseDataExtension,\n    inline,\n  } = {}) {\n\n    const boilerplateBaseData = {\n      css: [],\n      js: [],\n      head: '',\n      body: '',\n      meteorManifest: JSON.stringify(manifest),\n      ...baseDataExtension,\n    };\n\n    manifest.forEach(item => {\n      const urlPath = urlMapper(item.url);\n      const itemObj = { url: urlPath };\n\n      if (inline) {\n        itemObj.scriptContent = readUtf8FileSync(\n          pathMapper(item.path));\n        itemObj.inline = true;\n      } else if (item.sri) {\n        itemObj.sri = item.sri;\n      }\n\n      if (item.type === 'css' && item.where === 'client') {\n        boilerplateBaseData.css.push(itemObj);\n      }\n\n      if (item.type === 'js' && item.where === 'client' &&\n        // Dynamic JS modules should not be loaded eagerly in the\n        // initial HTML of the app.\n        !item.path.startsWith('dynamic/')) {\n        boilerplateBaseData.js.push(itemObj);\n      }\n\n      if (item.type === 'head') {\n        boilerplateBaseData.head =\n          readUtf8FileSync(pathMapper(item.path));\n      }\n\n      if (item.type === 'body') {\n        boilerplateBaseData.body =\n          readUtf8FileSync(pathMapper(item.path));\n      }\n    });\n\n    this.baseData = boilerplateBaseData;\n  }\n};\n\n// Returns a template function that, when called, produces the boilerplate\n// html as a string.\nfunction getTemplate(arch) {\n  const prefix = arch.split(\".\", 2).join(\".\");\n\n  if (prefix === \"web.browser\") {\n    return WebBrowserTemplate;\n  }\n\n  if (prefix === \"web.cordova\") {\n    return WebCordovaTemplate;\n  }\n\n  throw new Error(\"Unsupported arch: \" + arch);\n}\n", "import template from './template';\n\nconst sri = (sri, mode) =>\n  (sri && mode) ? ` integrity=\"sha512-${sri}\" crossorigin=\"${mode}\"` : '';\n\nexport const headTemplate = ({\n  css,\n  htmlAttributes,\n  bundledJsCssUrlRewriteHook,\n  sriMode,\n  head,\n  dynamicHead,\n}) => {\n  var headSections = head.split(/<meteor-bundled-css[^<>]*>/, 2);\n  var cssBundle = [...(css || []).map(file =>\n    template('  <link rel=\"stylesheet\" type=\"text/css\" class=\"__meteor-css__\" href=\"<%- href %>\"<%= sri %>>')({\n      href: bundledJsCssUrlRewriteHook(file.url),\n      sri: sri(file.sri, sriMode),\n    })\n  )].join('\\n');\n\n  return [\n    '<html' + Object.keys(htmlAttributes || {}).map(\n      key => template(' <%= attrName %>=\"<%- attrValue %>\"')({\n        attrName: key,\n        attrValue: htmlAttributes[key],\n      })\n    ).join('') + '>',\n\n    '<head>',\n\n    (headSections.length === 1)\n      ? [cssBundle, headSections[0]].join('\\n')\n      : [headSections[0], cssBundle, headSections[1]].join('\\n'),\n\n    dynamicHead,\n    '</head>',\n    '<body>',\n  ].join('\\n');\n};\n\n// Template function for rendering the boilerplate html for browsers\nexport const closeTemplate = ({\n  meteorRuntimeConfig,\n  meteorRuntimeHash,\n  rootUrlPathPrefix,\n  inlineScriptsAllowed,\n  js,\n  additionalStaticJs,\n  bundledJsCssUrlRewriteHook,\n  sriMode,\n}) => [\n  '',\n  inlineScriptsAllowed\n    ? template('  <script type=\"text/javascript\">__meteor_runtime_config__ = JSON.parse(decodeURIComponent(<%= conf %>))</script>')({\n      conf: meteorRuntimeConfig,\n    })\n    : template('  <script type=\"text/javascript\" src=\"<%- src %>/meteor_runtime_config.js?hash=<%- hash %>\"></script>')({\n      src: rootUrlPathPrefix,\n      hash: meteorRuntimeHash,\n    }),\n  '',\n\n  ...(js || []).map(file =>\n    template('  <script type=\"text/javascript\" src=\"<%- src %>\"<%= sri %>></script>')({\n      src: bundledJsCssUrlRewriteHook(file.url),\n      sri: sri(file.sri, sriMode),\n    })\n  ),\n\n  ...(additionalStaticJs || []).map(({ contents, pathname }) => (\n    inlineScriptsAllowed\n      ? template('  <script><%= contents %></script>')({\n        contents,\n      })\n      : template('  <script type=\"text/javascript\" src=\"<%- src %>\"></script>')({\n        src: rootUrlPathPrefix + pathname,\n      })\n  )),\n\n  '',\n  '',\n  '</body>',\n  '</html>'\n].join('\\n');\n", "import template from './template';\n\n// Template function for rendering the boilerplate html for cordova\nexport const headTemplate = ({\n  meteorRuntimeConfig,\n  rootUrlPathPrefix,\n  inlineScriptsAllowed,\n  css,\n  js,\n  additionalStaticJs,\n  htmlAttributes,\n  bundledJsCssUrlRewriteHook,\n  head,\n  dynamicHead,\n}) => {\n  var headSections = head.split(/<meteor-bundled-css[^<>]*>/, 2);\n  var cssBundle = [\n    // We are explicitly not using bundledJsCssUrlRewriteHook: in cordova we serve assets up directly from disk, so rewriting the URL does not make sense\n    ...(css || []).map(file =>\n      template('  <link rel=\"stylesheet\" type=\"text/css\" class=\"__meteor-css__\" href=\"<%- href %>\">')({\n        href: file.url,\n      })\n  )].join('\\n');\n\n  return [\n    '<html>',\n    '<head>',\n    '  <meta charset=\"utf-8\">',\n    '  <meta name=\"format-detection\" content=\"telephone=no\">',\n    '  <meta name=\"viewport\" content=\"user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, height=device-height, viewport-fit=cover\">',\n    '  <meta name=\"msapplication-tap-highlight\" content=\"no\">',\n    '  <meta http-equiv=\"Content-Security-Policy\" content=\"default-src * android-webview-video-poster: gap: data: blob: \\'unsafe-inline\\' \\'unsafe-eval\\' ws: wss:;\">',\n\n  (headSections.length === 1)\n    ? [cssBundle, headSections[0]].join('\\n')\n    : [headSections[0], cssBundle, headSections[1]].join('\\n'),\n\n    '  <script type=\"text/javascript\">',\n    template('    __meteor_runtime_config__ = JSON.parse(decodeURIComponent(<%= conf %>));')({\n      conf: meteorRuntimeConfig,\n    }),\n    '    if (/Android/i.test(navigator.userAgent)) {',\n    // When Android app is emulated, it cannot connect to localhost,\n    // instead it should connect to ********\n    // (unless we\\'re using an http proxy; then it works!)\n    '      if (!__meteor_runtime_config__.httpProxyPort) {',\n    '        __meteor_runtime_config__.ROOT_URL = (__meteor_runtime_config__.ROOT_URL || \\'\\').replace(/localhost/i, \\'********\\');',\n    '        __meteor_runtime_config__.DDP_DEFAULT_CONNECTION_URL = (__meteor_runtime_config__.DDP_DEFAULT_CONNECTION_URL || \\'\\').replace(/localhost/i, \\'********\\');',\n    '      }',\n    '    }',\n    '  </script>',\n    '',\n    '  <script type=\"text/javascript\" src=\"/cordova.js\"></script>',\n\n    ...(js || []).map(file =>\n      template('  <script type=\"text/javascript\" src=\"<%- src %>\"></script>')({\n        src: file.url,\n      })\n    ),\n\n    ...(additionalStaticJs || []).map(({ contents, pathname }) => (\n      inlineScriptsAllowed\n        ? template('  <script><%= contents %></script>')({\n          contents,\n        })\n        : template('  <script type=\"text/javascript\" src=\"<%- src %>\"></script>')({\n          src: rootUrlPathPrefix + pathname\n        })\n    )),\n    '',\n    '</head>',\n    '',\n    '<body>',\n  ].join('\\n');\n};\n\nexport function closeTemplate() {\n  return \"</body>\\n</html>\";\n}\n", "import lodashTemplate from 'lodash.template';\n\n// As identified in issue #9149, when an application overrides the default\n// _.template settings using _.templateSettings, those new settings are\n// used anywhere _.template is used, including within the\n// boilerplate-generator. To handle this, _.template settings that have\n// been verified to work are overridden here on each _.template call.\nexport default function template(text) {\n  return lodashTemplate(text, null, {\n    evaluate    : /<%([\\s\\S]+?)%>/g,\n    interpolate : /<%=([\\s\\S]+?)%>/g,\n    escape      : /<%-([\\s\\S]+?)%>/g,\n  });\n};"]}