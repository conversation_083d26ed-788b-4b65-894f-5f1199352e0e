{"version": 3, "sources": ["meteor://💻app/packages/autoupdate/autoupdate_server.js", "meteor://💻app/packages/autoupdate/client_versions.js"], "names": ["_objectSpread", "module1", "link", "default", "v", "export", "Autoupdate", "ClientVersions", "__reifyWaitForDeps__", "__meteor_runtime_config__", "autoupdate", "versions", "clientVersions", "autoupdateVersion", "autoupdateVersionRefreshable", "autoupdateVersionCordova", "appId", "process", "env", "APP_ID", "syncQueue", "Meteor", "_AsynchronousQueue", "updateVersions", "shouldReloadClientProgram", "WebAppInternals", "reloadClientPrograms", "AUTOUPDATE_VERSION", "clientArchs", "Object", "keys", "WebApp", "clientPrograms", "for<PERSON>ach", "arch", "version", "calculateClientHash", "versionRefreshable", "calculateClientHashRefreshable", "versionNonRefreshable", "calculateClientHashNonRefreshable", "versionReplaceable", "calculateClientHashReplaceable", "versionHmr", "hmrVersion", "generateBoilerplate", "onListening", "payload", "assets", "getRefreshableAssets", "set", "publish", "check", "Match", "OneOf", "String", "undefined", "stop", "watch", "isNew", "added", "changed", "call", "_id", "onStop", "ready", "is_auto", "startup", "enqueueVersionsRefresh", "queueTask", "setupListeners", "onMessage", "on", "bindEnvironment", "_isFibersEnabled", "Future", "Npm", "require", "fut", "wait", "return", "Promise", "resolve", "__reify_async_result__", "_reifyError", "self", "async", "module", "Tracker", "constructor", "_versions", "Map", "_watchCallbacks", "Set", "createStore", "update", "_ref", "id", "msg", "fields", "hasVersions", "size", "get", "assign", "_ref2", "fn", "filter", "skipInitial", "arguments", "length", "resolved", "then", "callback", "add", "delete", "newClientAvailable", "currentVersion", "isNewVersion", "some", "field", "dependency", "Dependency", "depend"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IAAIA,aAAa;IAACC,OAAO,CAACC,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAtGH,OAAO,CAACI,MAAM,CAAC;MAACC,UAAU,EAACA,CAAA,KAAIA;IAAU,CAAC,CAAC;IAAC,IAAIC,cAAc;IAACN,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAC;MAACK,cAAcA,CAACH,CAAC,EAAC;QAACG,cAAc,GAACH,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IA6BjM,MAAMF,UAAU,GAAGG,yBAAyB,CAACC,UAAU,GAAG;MAC/D;MACA;MACA;MACA;MACA;MACA;MACAC,QAAQ,EAAE,CAAC;IACb,CAAC;IAED;IACA,MAAMC,cAAc,GAAG,IAAIL,cAAc,CAAC,CAAC;;IAE3C;IACA;IACA;IACA;;IAEA;IACA;IACAD,UAAU,CAACO,iBAAiB,GAAG,IAAI;IACnCP,UAAU,CAACQ,4BAA4B,GAAG,IAAI;IAC9CR,UAAU,CAACS,wBAAwB,GAAG,IAAI;IAC1CT,UAAU,CAACU,KAAK,GAAGP,yBAAyB,CAACO,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,MAAM;IAEvE,IAAIC,SAAS,GAAG,IAAIC,MAAM,CAACC,kBAAkB,CAAC,CAAC;IAE/C,eAAeC,cAAcA,CAACC,yBAAyB,EAAE;MACvD;MACA,IAAIA,yBAAyB,EAAE;QAC7B,MAAMC,eAAe,CAACC,oBAAoB,CAAC,CAAC;MAC9C;MAEA,MAAM;QACJ;QACA;QACA;QACAC,kBAAkB,GAAGrB,UAAU,CAACO;MAClC,CAAC,GAAGI,OAAO,CAACC,GAAG;;MAEf;MACA,MAAMU,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACC,MAAM,CAACC,cAAc,CAAC;MACtDJ,WAAW,CAACK,OAAO,CAACC,IAAI,IAAI;QAC1B5B,UAAU,CAACK,QAAQ,CAACuB,IAAI,CAAC,GAAG;UAC1BC,OAAO,EAAER,kBAAkB,IACzBI,MAAM,CAACK,mBAAmB,CAACF,IAAI,CAAC;UAClCG,kBAAkB,EAAEV,kBAAkB,IACpCI,MAAM,CAACO,8BAA8B,CAACJ,IAAI,CAAC;UAC7CK,qBAAqB,EAAEZ,kBAAkB,IACvCI,MAAM,CAACS,iCAAiC,CAACN,IAAI,CAAC;UAChDO,kBAAkB,EAAEd,kBAAkB,IACpCI,MAAM,CAACW,8BAA8B,CAACR,IAAI,CAAC;UAC7CS,UAAU,EAAEZ,MAAM,CAACC,cAAc,CAACE,IAAI,CAAC,CAACU;QAC1C,CAAC;MACH,CAAC,CAAC;;MAEF;MACA;MACA,IAAIpB,yBAAyB,EAAE;QAC7B,MAAMC,eAAe,CAACoB,mBAAmB,CAAC,CAAC;MAC7C;;MAEA;MACA;MACA;MACA;MACAd,MAAM,CAACe,WAAW,CAAC,MAAM;QACvBlB,WAAW,CAACK,OAAO,CAACC,IAAI,IAAI;UAC1B,MAAMa,OAAO,GAAA/C,aAAA,CAAAA,aAAA,KACRM,UAAU,CAACK,QAAQ,CAACuB,IAAI,CAAC;YAC5Bc,MAAM,EAAEjB,MAAM,CAACkB,oBAAoB,CAACf,IAAI;UAAC,EAC1C;UAEDtB,cAAc,CAACsC,GAAG,CAAChB,IAAI,EAAEa,OAAO,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA1B,MAAM,CAAC8B,OAAO,CACZ,kCAAkC,EAClC,UAAUnC,KAAK,EAAE;MACf;MACA;MACA;MACAoC,KAAK,CAACpC,KAAK,EAAEqC,KAAK,CAACC,KAAK,CAACC,MAAM,EAAEC,SAAS,EAAE,IAAI,CAAC,CAAC;;MAElD;MACA;MACA,IAAIlD,UAAU,CAACU,KAAK,IAAIA,KAAK,IAAIV,UAAU,CAACU,KAAK,KAAKA,KAAK,EACzD,OAAO,EAAE;MAEX,MAAMyC,IAAI,GAAG7C,cAAc,CAAC8C,KAAK,CAAC,CAACvB,OAAO,EAAEwB,KAAK,KAAK;QACpD,CAACA,KAAK,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,OAAO,EAC/BC,IAAI,CAAC,IAAI,EAAE,kCAAkC,EAAE3B,OAAO,CAAC4B,GAAG,EAAE5B,OAAO,CAAC;MACzE,CAAC,CAAC;MAEF,IAAI,CAAC6B,MAAM,CAAC,MAAMP,IAAI,CAAC,CAAC,CAAC;MACzB,IAAI,CAACQ,KAAK,CAAC,CAAC;IACd,CAAC,EACD;MAACC,OAAO,EAAE;IAAI,CAChB,CAAC;IAED7C,MAAM,CAAC8C,OAAO,CAAC,kBAAkB;MAC/B,MAAM5C,cAAc,CAAC,KAAK,CAAC;;MAE3B;MACA;MACA,CAAC,SAAS,EACT,qBAAqB,EACrB,iBAAiB,CACjB,CAACU,OAAO,CAAC8B,GAAG,IAAI;QACfnD,cAAc,CAACsC,GAAG,CAACa,GAAG,EAAE;UACtB5B,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,SAASiC,sBAAsBA,CAAA,EAAG;MAChChD,SAAS,CAACiD,SAAS,CAAC,kBAAkB;QACpC,MAAM9C,cAAc,CAAC,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ;IAEA,MAAM+C,cAAc,GAAGA,CAAA,KAAM;MAxJ7B,IAAIC,SAAS;MAACtE,OAAO,CAACC,IAAI,CAAC,gCAAgC,EAAC;QAACqE,SAASA,CAACnE,CAAC,EAAC;UAACmE,SAAS,GAACnE,CAAC;QAAA;MAAC,CAAC,EAAC,CAAC,CAAC;MA2JxFmE,SAAS,CAAC,gBAAgB,EAAEH,sBAAsB,CAAC;;MAEnD;MACAnD,OAAO,CAACuD,EAAE,CAAC,QAAQ,EAAEnD,MAAM,CAACoD,eAAe,CAAC,YAAY;QACtDL,sBAAsB,CAAC,CAAC;MAC1B,CAAC,EAAE,oCAAoC,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI/C,MAAM,CAACqD,gBAAgB,EAAE;MAC3B,IAAIC,MAAM,GAAGC,GAAG,CAACC,OAAO,CAAC,eAAe,CAAC;MAEzC,IAAIC,GAAG,GAAG,IAAIH,MAAM,CAAC,CAAC;;MAEtB;MACA;MACA;MACA;MACA;;MAEAvD,SAAS,CAACiD,SAAS,CAAC,YAAY;QAC9BS,GAAG,CAACC,IAAI,CAAC,CAAC;MACZ,CAAC,CAAC;MAEFhD,MAAM,CAACe,WAAW,CAAC,YAAY;QAC7BgC,GAAG,CAACE,MAAM,CAAC,CAAC;MACd,CAAC,CAAC;MAEFV,cAAc,CAAC,CAAC;IAElB,CAAC,MAAM;MACLvC,MAAM,CAACe,WAAW,CAAC,YAAY;QAC7BmC,OAAO,CAACC,OAAO,CAACZ,cAAc,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC;IACJ;IAACa,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G;;;;;;;;;;;;;;IC5LD,IAAItF,aAAa;IAACuF,MAAM,CAACrF,IAAI,CAAC,sCAAsC,EAAC;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACJ,aAAa,GAACI,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAArGmF,MAAM,CAAClF,MAAM,CAAC;MAACE,cAAc,EAACA,CAAA,KAAIA;IAAc,CAAC,CAAC;IAAC,IAAIiF,OAAO;IAACD,MAAM,CAACrF,IAAI,CAAC,gBAAgB,EAAC;MAACsF,OAAOA,CAACpF,CAAC,EAAC;QAACoF,OAAO,GAACpF,CAAC;MAAA;IAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAII,oBAAoB,CAAC,CAAC,EAAE,CAAC,MAAMA,oBAAoB,CAAC,CAAC,EAAE,CAAC;IAE5K,MAAMD,cAAc,CAAC;MAC1BkF,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;MAClC;;MAEA;MACA;MACA;MACAC,WAAWA,CAAA,EAAG;QACZ,OAAO;UACLC,MAAM,EAAEC,IAAA,IAAyB;YAAA,IAAxB;cAAEC,EAAE;cAAEC,GAAG;cAAEC;YAAO,CAAC,GAAAH,IAAA;YAC1B,IAAIE,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,SAAS,EAAE;cACxC,IAAI,CAAChD,GAAG,CAAC+C,EAAE,EAAEE,MAAM,CAAC;YACtB;UACF;QACF,CAAC;MACH;MAEAC,WAAWA,CAAA,EAAG;QACZ,OAAO,IAAI,CAACV,SAAS,CAACW,IAAI,GAAG,CAAC;MAChC;MAEAC,GAAGA,CAACL,EAAE,EAAE;QACN,OAAO,IAAI,CAACP,SAAS,CAACY,GAAG,CAACL,EAAE,CAAC;MAC/B;;MAEA;MACA;MACA;MACA/C,GAAGA,CAAC+C,EAAE,EAAEE,MAAM,EAAE;QACd,IAAIhE,OAAO,GAAG,IAAI,CAACuD,SAAS,CAACY,GAAG,CAACL,EAAE,CAAC;QACpC,IAAItC,KAAK,GAAG,KAAK;QAEjB,IAAIxB,OAAO,EAAE;UACXN,MAAM,CAAC0E,MAAM,CAACpE,OAAO,EAAEgE,MAAM,CAAC;QAChC,CAAC,MAAM;UACLhE,OAAO,GAAAnC,aAAA;YACL+D,GAAG,EAAEkC;UAAE,GACJE,MAAM,CACV;UAEDxC,KAAK,GAAG,IAAI;UACZ,IAAI,CAAC+B,SAAS,CAACxC,GAAG,CAAC+C,EAAE,EAAE9D,OAAO,CAAC;QACjC;QAEA,IAAI,CAACyD,eAAe,CAAC3D,OAAO,CAACuE,KAAA,IAAoB;UAAA,IAAnB;YAAEC,EAAE;YAAEC;UAAO,CAAC,GAAAF,KAAA;UAC1C,IAAI,CAAEE,MAAM,IAAIA,MAAM,KAAKvE,OAAO,CAAC4B,GAAG,EAAE;YACtC0C,EAAE,CAACtE,OAAO,EAAEwB,KAAK,CAAC;UACpB;QACF,CAAC,CAAC;MACJ;;MAEA;MACA;MACA;MACA;MACA;MACAD,KAAKA,CAAC+C,EAAE,EAAgC;QAAA,IAA9B;UAAEE,WAAW;UAAED;QAAO,CAAC,GAAAE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAApD,SAAA,GAAAoD,SAAA,MAAG,CAAC,CAAC;QACpC,IAAI,CAAED,WAAW,EAAE;UACjB,MAAMG,QAAQ,GAAG7B,OAAO,CAACC,OAAO,CAAC,CAAC;UAElC,IAAI,CAACQ,SAAS,CAACzD,OAAO,CAAEE,OAAO,IAAK;YAClC,IAAI,CAAEuE,MAAM,IAAIA,MAAM,KAAKvE,OAAO,CAAC4B,GAAG,EAAE;cACtC+C,QAAQ,CAACC,IAAI,CAAC,MAAMN,EAAE,CAACtE,OAAO,EAAE,IAAI,CAAC,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;QAEA,MAAM6E,QAAQ,GAAG;UAAEP,EAAE;UAAEC;QAAO,CAAC;QAC/B,IAAI,CAACd,eAAe,CAACqB,GAAG,CAACD,QAAQ,CAAC;QAElC,OAAO,MAAM,IAAI,CAACpB,eAAe,CAACsB,MAAM,CAACF,QAAQ,CAAC;MACpD;;MAEA;MACAG,kBAAkBA,CAAClB,EAAE,EAAEE,MAAM,EAAEiB,cAAc,EAAE;QAC7C,SAASC,YAAYA,CAAClF,OAAO,EAAE;UAC7B,OACEA,OAAO,CAAC4B,GAAG,KAAKkC,EAAE,IAClBE,MAAM,CAACmB,IAAI,CAAEC,KAAK,IAAKpF,OAAO,CAACoF,KAAK,CAAC,KAAKH,cAAc,CAACG,KAAK,CAAC,CAAC;QAEpE;QAEA,MAAMC,UAAU,GAAG,IAAIhC,OAAO,CAACiC,UAAU,CAAC,CAAC;QAC3C,MAAMtF,OAAO,GAAG,IAAI,CAACmE,GAAG,CAACL,EAAE,CAAC;QAE5BuB,UAAU,CAACE,MAAM,CAAC,CAAC;QAEnB,MAAMjE,IAAI,GAAG,IAAI,CAACC,KAAK,CACpBvB,OAAO,IAAK;UACX,IAAIkF,YAAY,CAAClF,OAAO,CAAC,EAAE;YACzBqF,UAAU,CAAC3D,OAAO,CAAC,CAAC;YACpBJ,IAAI,CAAC,CAAC;UACR;QACF,CAAC,EACD;UAAEkD,WAAW,EAAE;QAAK,CACtB,CAAC;QAED,OAAO,CAAC,CAAExE,OAAO,IAAIkF,YAAY,CAAClF,OAAO,CAAC;MAC5C;IACF;IAACgD,sBAAA;EAAA,SAAAC,WAAA;IAAA,OAAAD,sBAAA,CAAAC,WAAA;EAAA;EAAAD,sBAAA;AAAA;EAAAE,IAAA;EAAAC,KAAA;AAAA,G", "file": "/packages/autoupdate.js", "sourcesContent": ["// Publish the current client versions for each client architecture\n// (web.browser, web.browser.legacy, web.cordova). When a client observes\n// a change in the versions associated with its client architecture,\n// it will refresh itself, either by swapping out CSS assets or by\n// reloading the page. Changes to the replaceable version are ignored\n// and handled by the hot-module-replacement package.\n//\n// There are four versions for any given client architecture: `version`,\n// `versionRefreshable`, `versionNonRefreshable`, and\n// `versionReplaceable`. The refreshable version is a hash of just the\n// client resources that are refreshable, such as CSS. The replaceable\n// version is a hash of files that can be updated with HMR. The\n// non-refreshable version is a hash of the rest of the client assets,\n// excluding the refreshable ones: HTML, JS that is not replaceable, and\n// static files in the `public` directory. The `version` version is a\n// combined hash of everything.\n//\n// If the environment variable `AUTOUPDATE_VERSION` is set, it will be\n// used in place of all client versions. You can use this variable to\n// control when the client reloads. For example, if you want to force a\n// reload only after major changes, use a custom AUTOUPDATE_VERSION and\n// change it only when something worth pushing to clients happens.\n//\n// The server publishes a `meteor_autoupdate_clientVersions` collection.\n// The ID of each document is the client architecture, and the fields of\n// the document are the versions described above.\n\nimport { ClientVersions } from \"./client_versions.js\";\n\nexport const Autoupdate = __meteor_runtime_config__.autoupdate = {\n  // Map from client architectures (web.browser, web.browser.legacy,\n  // web.cordova) to version fields { version, versionRefreshable,\n  // versionNonRefreshable, refreshable } that will be stored in\n  // ClientVersions documents (whose IDs are client architectures). This\n  // data gets serialized into the boilerplate because it's stored in\n  // __meteor_runtime_config__.autoupdate.versions.\n  versions: {}\n};\n\n// Stores acceptable client versions.\nconst clientVersions = new ClientVersions();\n\n// The client hash includes __meteor_runtime_config__, so wait until\n// all packages have loaded and have had a chance to populate the\n// runtime config before using the client hash as our default auto\n// update version id.\n\n// Note: Tests allow people to override Autoupdate.autoupdateVersion before\n// startup.\nAutoupdate.autoupdateVersion = null;\nAutoupdate.autoupdateVersionRefreshable = null;\nAutoupdate.autoupdateVersionCordova = null;\nAutoupdate.appId = __meteor_runtime_config__.appId = process.env.APP_ID;\n\nvar syncQueue = new Meteor._AsynchronousQueue();\n\nasync function updateVersions(shouldReloadClientProgram) {\n  // Step 1: load the current client program on the server\n  if (shouldReloadClientProgram) {\n    await WebAppInternals.reloadClientPrograms();\n  }\n\n  const {\n    // If the AUTOUPDATE_VERSION environment variable is defined, it takes\n    // precedence, but Autoupdate.autoupdateVersion is still supported as\n    // a fallback. In most cases neither of these values will be defined.\n    AUTOUPDATE_VERSION = Autoupdate.autoupdateVersion\n  } = process.env;\n\n  // Step 2: update __meteor_runtime_config__.autoupdate.versions.\n  const clientArchs = Object.keys(WebApp.clientPrograms);\n  clientArchs.forEach(arch => {\n    Autoupdate.versions[arch] = {\n      version: AUTOUPDATE_VERSION ||\n        WebApp.calculateClientHash(arch),\n      versionRefreshable: AUTOUPDATE_VERSION ||\n        WebApp.calculateClientHashRefreshable(arch),\n      versionNonRefreshable: AUTOUPDATE_VERSION ||\n        WebApp.calculateClientHashNonRefreshable(arch),\n      versionReplaceable: AUTOUPDATE_VERSION ||\n        WebApp.calculateClientHashReplaceable(arch),\n      versionHmr: WebApp.clientPrograms[arch].hmrVersion\n    };\n  });\n\n  // Step 3: form the new client boilerplate which contains the updated\n  // assets and __meteor_runtime_config__.\n  if (shouldReloadClientProgram) {\n    await WebAppInternals.generateBoilerplate();\n  }\n\n  // Step 4: update the ClientVersions collection.\n  // We use `onListening` here because we need to use\n  // `WebApp.getRefreshableAssets`, which is only set after\n  // `WebApp.generateBoilerplate` is called by `main` in webapp.\n  WebApp.onListening(() => {\n    clientArchs.forEach(arch => {\n      const payload = {\n        ...Autoupdate.versions[arch],\n        assets: WebApp.getRefreshableAssets(arch),\n      };\n\n      clientVersions.set(arch, payload);\n    });\n  });\n}\n\nMeteor.publish(\n  \"meteor_autoupdate_clientVersions\",\n  function (appId) {\n    // `null` happens when a client doesn't have an appId and passes\n    // `undefined` to `Meteor.subscribe`. `undefined` is translated to\n    // `null` as JSON doesn't have `undefined.\n    check(appId, Match.OneOf(String, undefined, null));\n\n    // Don't notify clients using wrong appId such as mobile apps built with a\n    // different server but pointing at the same local url\n    if (Autoupdate.appId && appId && Autoupdate.appId !== appId)\n      return [];\n\n    const stop = clientVersions.watch((version, isNew) => {\n      (isNew ? this.added : this.changed)\n        .call(this, \"meteor_autoupdate_clientVersions\", version._id, version);\n    });\n\n    this.onStop(() => stop());\n    this.ready();\n  },\n  {is_auto: true}\n);\n\nMeteor.startup(async function () {\n  await updateVersions(false);\n\n  // Force any connected clients that are still looking for these older\n  // document IDs to reload.\n  [\"version\",\n   \"version-refreshable\",\n   \"version-cordova\",\n  ].forEach(_id => {\n    clientVersions.set(_id, {\n      version: \"outdated\"\n    });\n  });\n});\n\nfunction enqueueVersionsRefresh() {\n  syncQueue.queueTask(async function () {\n    await updateVersions(true);\n  });\n}\n\nconst setupListeners = () => {\n  // Listen for messages pertaining to the client-refresh topic.\n  import { onMessage } from \"meteor/inter-process-messaging\";\n  onMessage(\"client-refresh\", enqueueVersionsRefresh);\n\n  // Another way to tell the process to refresh: send SIGHUP signal\n  process.on('SIGHUP', Meteor.bindEnvironment(function () {\n    enqueueVersionsRefresh();\n  }, \"handling SIGHUP signal for refresh\"));\n};\n\nif (Meteor._isFibersEnabled) {\n  var Future = Npm.require(\"fibers/future\");\n\n  var fut = new Future();\n\n  // We only want 'refresh' to trigger 'updateVersions' AFTER onListen,\n  // so we add a queued task that waits for onListen before 'refresh' can queue\n  // tasks. Note that the `onListening` callbacks do not fire until after\n  // Meteor.startup, so there is no concern that the 'updateVersions' calls from\n  // 'refresh' will overlap with the `updateVersions` call from Meteor.startup.\n\n  syncQueue.queueTask(function () {\n    fut.wait();\n  });\n\n  WebApp.onListening(function () {\n    fut.return();\n  });\n\n  setupListeners();\n\n} else {\n  WebApp.onListening(function () {\n    Promise.resolve(setupListeners());\n  });\n}\n", "import { Tracker } from \"meteor/tracker\";\n\nexport class ClientVersions {\n  constructor() {\n    this._versions = new Map();\n    this._watchCallbacks = new Set();\n  }\n\n  // Creates a Livedata store for use with `Meteor.connection.registerStore`.\n  // After the store is registered, document updates reported by Livedata are\n  // merged with the documents in this `ClientVersions` instance.\n  createStore() {\n    return {\n      update: ({ id, msg, fields }) => {\n        if (msg === \"added\" || msg === \"changed\") {\n          this.set(id, fields);\n        }\n      }\n    };\n  }\n\n  hasVersions() {\n    return this._versions.size > 0;\n  }\n\n  get(id) {\n    return this._versions.get(id);\n  }\n\n  // Adds or updates a version document and invokes registered callbacks for the\n  // added/updated document. If a document with the given ID already exists, its\n  // fields are merged with `fields`.\n  set(id, fields) {\n    let version = this._versions.get(id);\n    let isNew = false;\n\n    if (version) {\n      Object.assign(version, fields);\n    } else {\n      version = {\n        _id: id,\n        ...fields\n      };\n\n      isNew = true;\n      this._versions.set(id, version);\n    }\n\n    this._watchCallbacks.forEach(({ fn, filter }) => {\n      if (! filter || filter === version._id) {\n        fn(version, isNew);\n      }\n    });\n  }\n\n  // Registers a callback that will be invoked when a version document is added\n  // or changed. Calling the function returned by `watch` removes the callback.\n  // If `skipInitial` is true, the callback isn't be invoked for existing\n  // documents. If `filter` is set, the callback is only invoked for documents\n  // with ID `filter`.\n  watch(fn, { skipInitial, filter } = {}) {\n    if (! skipInitial) {\n      const resolved = Promise.resolve();\n\n      this._versions.forEach((version) => {\n        if (! filter || filter === version._id) {\n          resolved.then(() => fn(version, true));\n        }\n      });\n    }\n\n    const callback = { fn, filter };\n    this._watchCallbacks.add(callback);\n\n    return () => this._watchCallbacks.delete(callback);\n  }\n\n  // A reactive data source for `Autoupdate.newClientAvailable`.\n  newClientAvailable(id, fields, currentVersion) {\n    function isNewVersion(version) {\n      return (\n        version._id === id &&\n        fields.some((field) => version[field] !== currentVersion[field])\n      );\n    }\n\n    const dependency = new Tracker.Dependency();\n    const version = this.get(id);\n\n    dependency.depend();\n\n    const stop = this.watch(\n      (version) => {\n        if (isNewVersion(version)) {\n          dependency.changed();\n          stop();\n        }\n      },\n      { skipInitial: true }\n    );\n\n    return !! version && isNewVersion(version);\n  }\n}\n"]}