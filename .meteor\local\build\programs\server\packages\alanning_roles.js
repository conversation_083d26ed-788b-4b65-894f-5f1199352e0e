Package["core-runtime"].queue("alanning:roles",function () {/* Imports */
var Meteor = Package.meteor.Meteor;
var global = Package.meteor.global;
var meteorEnv = Package.meteor.meteorEnv;
var EmitterPromise = Package.meteor.EmitterPromise;
var ECMAScript = Package.ecmascript.ECMAScript;
var Accounts = Package['accounts-base'].Accounts;
var Tracker = Package.tracker.Tracker;
var Deps = Package.tracker.Deps;
var MongoInternals = Package.mongo.MongoInternals;
var Mongo = Package.mongo.Mongo;
var check = Package.check.check;
var Match = Package.check.Match;
var DDP = Package['ddp-client'].DDP;
var DDPServer = Package['ddp-server'].DDPServer;
var meteorInstall = Package.modules.meteorInstall;
var Promise = Package.promise.Promise;

/* Package-scope variables */
var Roles, RolesCollection, RoleAssignmentCollection;

var require = meteorInstall({"node_modules":{"meteor":{"alanning:roles":{"roles":{"roles_common_async.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                              //
// packages/alanning_roles/roles/roles_common_async.js                                                          //
//                                                                                                              //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                //
!module.wrapAsync(async function (module1, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    module1.export({
      RolesCollection: () => RolesCollection,
      RoleAssignmentCollection: () => RoleAssignmentCollection
    });
    let Meteor;
    module1.link("meteor/meteor", {
      Meteor(v) {
        Meteor = v;
      }
    }, 0);
    let Mongo;
    module1.link("meteor/mongo", {
      Mongo(v) {
        Mongo = v;
      }
    }, 1);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const RolesCollection = new Mongo.Collection('roles');
    if (!Meteor.roles) {
      Meteor.roles = RolesCollection;
    }
    const RoleAssignmentCollection = new Mongo.Collection('role-assignment');
    if (!Meteor.roleAssignment) {
      Meteor.roleAssignment = RoleAssignmentCollection;
    }

    /**
     * @class Roles
     */
    if (typeof Roles === 'undefined') {
      Roles = {}; // eslint-disable-line no-global-assign
    }
    let getGroupsForUserDeprecationWarning = false;

    /**
     * Helper, resolves async some
     * @param {*} arr
     * @param {*} predicate
     * @returns {Promise<Boolean>}
     */
    const asyncSome = async (arr, predicate) => {
      for (const e of arr) {
        if (await predicate(e)) return true;
      }
      return false;
    };
    Object.assign(Roles, {
      /**
       * Used as a global group (now scope) name. Not used anymore.
       *
       * @property GLOBAL_GROUP
       * @static
       * @deprecated
       */
      GLOBAL_GROUP: null,
      /**
       * Create a new role.
       *
       * @method createRoleAsync
       * @param {String} roleName Name of role.
       * @param {Object} [options] Options:
       *   - `unlessExists`: if `true`, exception will not be thrown in the role already exists
       * @return {Promise<String>} ID of the new role or null.
       * @static
       */
      createRoleAsync: async function (roleName, options) {
        Roles._checkRoleName(roleName);
        options = Object.assign({
          unlessExists: false
        }, options);
        let insertedId = null;
        const existingRole = await Meteor.roles.findOneAsync({
          _id: roleName
        });
        if (existingRole) {
          await Meteor.roles.updateAsync({
            _id: roleName
          }, {
            $setOnInsert: {
              children: []
            }
          });
          return null;
        } else {
          insertedId = await Meteor.roles.insertAsync({
            _id: roleName,
            children: []
          });
        }
        if (!insertedId) {
          if (options.unlessExists) return null;
          throw new Error("Role '" + roleName + "' already exists.");
        }
        return insertedId;
      },
      /**
       * Delete an existing role.
       *
       * If the role is set for any user, it is automatically unset.
       *
       * @method deleteRoleAsync
       * @param {String} roleName Name of role.
       * @returns {Promise}
       * @static
       */
      deleteRoleAsync: async function (roleName) {
        let roles;
        let inheritedRoles;
        Roles._checkRoleName(roleName);

        // Remove all assignments
        await Meteor.roleAssignment.removeAsync({
          'role._id': roleName
        });
        do {
          // For all roles who have it as a dependency ...
          roles = await Roles._getParentRoleNamesAsync(await Meteor.roles.findOneAsync({
            _id: roleName
          }));
          for (const r of await Meteor.roles.find({
            _id: {
              $in: roles
            }
          }).fetchAsync()) {
            await Meteor.roles.updateAsync({
              _id: r._id
            }, {
              $pull: {
                children: {
                  _id: roleName
                }
              }
            });
            inheritedRoles = await Roles._getInheritedRoleNamesAsync(await Meteor.roles.findOneAsync({
              _id: r._id
            }));
            await Meteor.roleAssignment.updateAsync({
              'role._id': r._id
            }, {
              $set: {
                inheritedRoles: [r._id, ...inheritedRoles].map(r2 => ({
                  _id: r2
                }))
              }
            }, {
              multi: true
            });
          }
        } while (roles.length > 0);

        // And finally remove the role itself
        await Meteor.roles.removeAsync({
          _id: roleName
        });
      },
      /**
       * Rename an existing role.
       *
       * @method renameRoleAsync
       * @param {String} oldName Old name of a role.
       * @param {String} newName New name of a role.
       * @returns {Promise}
       * @static
       */
      renameRoleAsync: async function (oldName, newName) {
        let count;
        Roles._checkRoleName(oldName);
        Roles._checkRoleName(newName);
        if (oldName === newName) return;
        const role = await Meteor.roles.findOneAsync({
          _id: oldName
        });
        if (!role) {
          throw new Error("Role '" + oldName + "' does not exist.");
        }
        role._id = newName;
        await Meteor.roles.insertAsync(role);
        do {
          count = await Meteor.roleAssignment.updateAsync({
            'role._id': oldName
          }, {
            $set: {
              'role._id': newName
            }
          }, {
            multi: true
          });
        } while (count > 0);
        do {
          count = await Meteor.roleAssignment.updateAsync({
            'inheritedRoles._id': oldName
          }, {
            $set: {
              'inheritedRoles.$._id': newName
            }
          }, {
            multi: true
          });
        } while (count > 0);
        do {
          count = await Meteor.roles.updateAsync({
            'children._id': oldName
          }, {
            $set: {
              'children.$._id': newName
            }
          }, {
            multi: true
          });
        } while (count > 0);
        await Meteor.roles.removeAsync({
          _id: oldName
        });
      },
      /**
       * Add role parent to roles.
       *
       * Previous parents are kept (role can have multiple parents). For users which have the
       * parent role set, new subroles are added automatically.
       *
       * @method addRolesToParentAsync
       * @param {Array|String} rolesNames Name(s) of role(s).
       * @param {String} parentName Name of parent role.
       * @returns {Promise}
       * @static
       */
      addRolesToParentAsync: async function (rolesNames, parentName) {
        // ensure arrays
        if (!Array.isArray(rolesNames)) rolesNames = [rolesNames];
        for (const roleName of rolesNames) {
          await Roles._addRoleToParentAsync(roleName, parentName);
        }
      },
      /**
       * @method _addRoleToParentAsync
       * @param {String} roleName Name of role.
       * @param {String} parentName Name of parent role.
       * @returns {Promise}
       * @private
       * @static
       */
      _addRoleToParentAsync: async function (roleName, parentName) {
        Roles._checkRoleName(roleName);
        Roles._checkRoleName(parentName);

        // query to get role's children
        const role = await Meteor.roles.findOneAsync({
          _id: roleName
        });
        if (!role) {
          throw new Error("Role '".concat(roleName, "' does not exist."));
        }

        // detect cycles
        if ((await Roles._getInheritedRoleNamesAsync(role)).includes(parentName)) {
          throw new Error("Roles '".concat(roleName, "' and '").concat(parentName, "' would form a cycle."));
        }
        const count = await Meteor.roles.updateAsync({
          _id: parentName,
          'children._id': {
            $ne: role._id
          }
        }, {
          $push: {
            children: {
              _id: role._id
            }
          }
        });

        // if there was no change, parent role might not exist, or role is
        // already a sub-role; in any case we do not have anything more to do
        if (!count) return;
        await Meteor.roleAssignment.updateAsync({
          'inheritedRoles._id': parentName
        }, {
          $push: {
            inheritedRoles: {
              $each: [role._id, ...(await Roles._getInheritedRoleNamesAsync(role))].map(r => ({
                _id: r
              }))
            }
          }
        }, {
          multi: true
        });
      },
      /**
       * Remove role parent from roles.
       *
       * Other parents are kept (role can have multiple parents). For users which have the
       * parent role set, removed subrole is removed automatically.
       *
       * @method removeRolesFromParentAsync
       * @param {Array|String} rolesNames Name(s) of role(s).
       * @param {String} parentName Name of parent role.
       * @returns {Promise}
       * @static
       */
      removeRolesFromParentAsync: async function (rolesNames, parentName) {
        // ensure arrays
        if (!Array.isArray(rolesNames)) rolesNames = [rolesNames];
        for (const roleName of rolesNames) {
          await Roles._removeRoleFromParentAsync(roleName, parentName);
        }
      },
      /**
       * @method _removeRoleFromParentAsync
       * @param {String} roleName Name of role.
       * @param {String} parentName Name of parent role.
       * @returns {Promise}
       * @private
       * @static
       */
      _removeRoleFromParentAsync: async function (roleName, parentName) {
        Roles._checkRoleName(roleName);
        Roles._checkRoleName(parentName);

        // check for role existence
        // this would not really be needed, but we are trying to match addRolesToParent
        const role = await Meteor.roles.findOneAsync({
          _id: roleName
        }, {
          fields: {
            _id: 1
          }
        });
        if (!role) {
          throw new Error("Role '".concat(roleName, "' does not exist."));
        }
        const count = await Meteor.roles.updateAsync({
          _id: parentName
        }, {
          $pull: {
            children: {
              _id: role._id
            }
          }
        });

        // if there was no change, parent role might not exist, or role was
        // already not a subrole; in any case we do not have anything more to do
        if (!count) return;

        // For all roles who have had it as a dependency ...
        const roles = [...(await Roles._getParentRoleNamesAsync(await Meteor.roles.findOneAsync({
          _id: parentName
        }))), parentName];
        for (const r of await Meteor.roles.find({
          _id: {
            $in: roles
          }
        }).fetchAsync()) {
          const inheritedRoles = await Roles._getInheritedRoleNamesAsync(await Meteor.roles.findOneAsync({
            _id: r._id
          }));
          await Meteor.roleAssignment.updateAsync({
            'role._id': r._id,
            'inheritedRoles._id': role._id
          }, {
            $set: {
              inheritedRoles: [r._id, ...inheritedRoles].map(r2 => ({
                _id: r2
              }))
            }
          }, {
            multi: true
          });
        }
      },
      /**
       * Add users to roles.
       *
       * Adds roles to existing roles for each user.
       *
       * @example
       *     Roles.addUsersToRolesAsync(userId, 'admin')
       *     Roles.addUsersToRolesAsync(userId, ['view-secrets'], 'example.com')
       *     Roles.addUsersToRolesAsync([user1, user2], ['user','editor'])
       *     Roles.addUsersToRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')
       *
       * @method addUsersToRolesAsync
       * @param {Array|String} users User ID(s) or object(s) with an `_id` field.
       * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of the scope, or `null` for the global role
       *   - `ifExists`: if `true`, do not throw an exception if the role does not exist
       * @returns {Promise}
       *
       * Alternatively, it can be a scope name string.
       * @static
       */
      addUsersToRolesAsync: async function (users, roles, options) {
        let id;
        if (!users) throw new Error("Missing 'users' param.");
        if (!roles) throw new Error("Missing 'roles' param.");
        options = Roles._normalizeOptions(options);

        // ensure arrays
        if (!Array.isArray(users)) users = [users];
        if (!Array.isArray(roles)) roles = [roles];
        Roles._checkScopeName(options.scope);
        options = Object.assign({
          ifExists: false
        }, options);
        for (const user of users) {
          if (typeof user === 'object') {
            id = user._id;
          } else {
            id = user;
          }
          for (const role of roles) {
            await Roles._addUserToRoleAsync(id, role, options);
          }
        }
      },
      /**
       * Set users' roles.
       *
       * Replaces all existing roles with a new set of roles.
       *
       * @example
       *     await Roles.setUserRolesAsync(userId, 'admin')
       *     await Roles.setUserRolesAsync(userId, ['view-secrets'], 'example.com')
       *     await Roles.setUserRolesAsync([user1, user2], ['user','editor'])
       *     await Roles.setUserRolesAsync([user1, user2], ['glorious-admin', 'perform-action'], 'example.org')
       *
       * @method setUserRolesAsync
       * @param {Array|String} users User ID(s) or object(s) with an `_id` field.
       * @param {Array|String} roles Name(s) of roles to add users to. Roles have to exist.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of the scope, or `null` for the global role
       *   - `anyScope`: if `true`, remove all roles the user has, of any scope, if `false`, only the one in the same scope
       *   - `ifExists`: if `true`, do not throw an exception if the role does not exist
       * @returns {Promise}
       *
       * Alternatively, it can be a scope name string.
       * @static
       */
      setUserRolesAsync: async function (users, roles, options) {
        let id;
        if (!users) throw new Error("Missing 'users' param.");
        if (!roles) throw new Error("Missing 'roles' param.");
        options = Roles._normalizeOptions(options);

        // ensure arrays
        if (!Array.isArray(users)) users = [users];
        if (!Array.isArray(roles)) roles = [roles];
        Roles._checkScopeName(options.scope);
        options = Object.assign({
          ifExists: false,
          anyScope: false
        }, options);
        for (const user of users) {
          if (typeof user === 'object') {
            id = user._id;
          } else {
            id = user;
          }
          // we first clear all roles for the user
          const selector = {
            'user._id': id
          };
          if (!options.anyScope) {
            selector.scope = options.scope;
          }
          await Meteor.roleAssignment.removeAsync(selector);

          // and then add all
          for (const role of roles) {
            await Roles._addUserToRoleAsync(id, role, options);
          }
        }
      },
      /**
       * Add one user to one role.
       *
       * @method _addUserToRoleAsync
       * @param {String} userId The user ID.
       * @param {String} roleName Name of the role to add the user to. The role have to exist.
       * @param {Object} options Options:
       *   - `scope`: name of the scope, or `null` for the global role
       *   - `ifExists`: if `true`, do not throw an exception if the role does not exist
       * @returns {Promise}
       * @private
       * @static
       */
      _addUserToRoleAsync: async function (userId, roleName, options) {
        Roles._checkRoleName(roleName);
        Roles._checkScopeName(options.scope);
        if (!userId) {
          return;
        }
        const role = await Meteor.roles.findOneAsync({
          _id: roleName
        }, {
          fields: {
            children: 1
          }
        });
        if (!role) {
          if (options.ifExists) {
            return [];
          } else {
            throw new Error("Role '" + roleName + "' does not exist.");
          }
        }

        // This might create duplicates, because we don't have a unique index, but that's all right. In case there are two, withdrawing the role will effectively kill them both.
        // TODO revisit this
        /* const res = await RoleAssignmentCollection.upsertAsync(
          {
            "user._id": userId,
            "role._id": roleName,
            scope: options.scope,
          },
          {
            $setOnInsert: {
              user: { _id: userId },
              role: { _id: roleName },
              scope: options.scope,
            },
          }
        ); */
        const existingAssignment = await Meteor.roleAssignment.findOneAsync({
          'user._id': userId,
          'role._id': roleName,
          scope: options.scope
        });
        let insertedId;
        let res;
        if (existingAssignment) {
          await Meteor.roleAssignment.updateAsync(existingAssignment._id, {
            $set: {
              user: {
                _id: userId
              },
              role: {
                _id: roleName
              },
              scope: options.scope
            }
          });
          res = await Meteor.roleAssignment.findOneAsync(existingAssignment._id);
        } else {
          insertedId = await Meteor.roleAssignment.insertAsync({
            user: {
              _id: userId
            },
            role: {
              _id: roleName
            },
            scope: options.scope
          });
        }
        if (insertedId) {
          await Meteor.roleAssignment.updateAsync({
            _id: insertedId
          }, {
            $set: {
              inheritedRoles: [roleName, ...(await Roles._getInheritedRoleNamesAsync(role))].map(r => ({
                _id: r
              }))
            }
          });
          res = await Meteor.roleAssignment.findOneAsync({
            _id: insertedId
          });
        }
        res.insertedId = insertedId; // For backward compatibility

        return res;
      },
      /**
       * Returns an array of role names the given role name is a child of.
       *
       * @example
       *     Roles._getParentRoleNamesAsync({ _id: 'admin', children; [] })
       *
       * @method _getParentRoleNamesAsync
       * @param {object} role The role object
       * @returns {Promise}
       * @private
       * @static
       */
      _getParentRoleNamesAsync: async function (role) {
        if (!role) {
          return [];
        }
        const parentRoles = new Set([role._id]);
        for (const roleName of parentRoles) {
          for (const parentRole of await Meteor.roles.find({
            'children._id': roleName
          }).fetchAsync()) {
            parentRoles.add(parentRole._id);
          }
        }
        parentRoles.delete(role._id);
        return [...parentRoles];
      },
      /**
       * Returns an array of role names the given role name is a parent of.
       *
       * @example
       *     Roles._getInheritedRoleNames({ _id: 'admin', children; [] })
       *
       * @method _getInheritedRoleNames
       * @param {object} role The role object
       * @returns {Promise}
       * @private
       * @static
       */
      _getInheritedRoleNamesAsync: async function (role) {
        const inheritedRoles = new Set();
        const nestedRoles = new Set([role]);
        for (const r of nestedRoles) {
          const roles = await Meteor.roles.find({
            _id: {
              $in: r.children.map(r => r._id)
            }
          }, {
            fields: {
              children: 1
            }
          }).fetchAsync();
          for (const r2 of roles) {
            inheritedRoles.add(r2._id);
            nestedRoles.add(r2);
          }
        }
        return [...inheritedRoles];
      },
      /**
       * Remove users from assigned roles.
       *
       * @example
       *     await Roles.removeUsersFromRolesAsync(userId, 'admin')
       *     await Roles.removeUsersFromRolesAsync([userId, user2], ['editor'])
       *     await Roles.removeUsersFromRolesAsync(userId, ['user'], 'group1')
       *
       * @method removeUsersFromRolesAsync
       * @param {Array|String} users User ID(s) or object(s) with an `_id` field.
       * @param {Array|String} roles Name(s) of roles to remove users from. Roles have to exist.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of the scope, or `null` for the global role
       *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)
       * @returns {Promise}
       *
       * Alternatively, it can be a scope name string.
       * @static
       */
      removeUsersFromRolesAsync: async function (users, roles, options) {
        if (!users) throw new Error("Missing 'users' param.");
        if (!roles) throw new Error("Missing 'roles' param.");
        options = Roles._normalizeOptions(options);

        // ensure arrays
        if (!Array.isArray(users)) users = [users];
        if (!Array.isArray(roles)) roles = [roles];
        Roles._checkScopeName(options.scope);
        for (const user of users) {
          if (!user) return;
          for (const role of roles) {
            let id;
            if (typeof user === 'object') {
              id = user._id;
            } else {
              id = user;
            }
            await Roles._removeUserFromRoleAsync(id, role, options);
          }
        }
      },
      /**
       * Remove one user from one role.
       *
       * @method _removeUserFromRoleAsync
       * @param {String} userId The user ID.
       * @param {String} roleName Name of the role to add the user to. The role have to exist.
       * @param {Object} options Options:
       *   - `scope`: name of the scope, or `null` for the global role
       *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)
       * @returns {Promise}
       * @private
       * @static
       */
      _removeUserFromRoleAsync: async function (userId, roleName, options) {
        Roles._checkRoleName(roleName);
        Roles._checkScopeName(options.scope);
        if (!userId) return;
        const selector = {
          'user._id': userId,
          'role._id': roleName
        };
        if (!options.anyScope) {
          selector.scope = options.scope;
        }
        await Meteor.roleAssignment.removeAsync(selector);
      },
      /**
       * Check if user has specified roles.
       *
       * @example
       *     // global roles
       *     await Roles.userIsInRoleAsync(user, 'admin')
       *     await Roles.userIsInRoleAsync(user, ['admin','editor'])
       *     await Roles.userIsInRoleAsync(userId, 'admin')
       *     await Roles.userIsInRoleAsync(userId, ['admin','editor'])
       *
       *     // scope roles (global roles are still checked)
       *     await Roles.userIsInRoleAsync(user, 'admin', 'group1')
       *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], 'group1')
       *     await Roles.userIsInRoleAsync(userId, ['admin','editor'], {scope: 'group1'})
       *
       * @method userIsInRoleAsync
       * @param {String|Object} user User ID or an actual user object.
       * @param {Array|String} roles Name of role or an array of roles to check against. If array,
       *                             will return `true` if user is in _any_ role.
       *                             Roles do not have to exist.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of the scope; if supplied, limits check to just that scope
       *     the user's global roles will always be checked whether scope is specified or not
       *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)
       *
       * Alternatively, it can be a scope name string.
       * @return {Promise<Boolean>} `true` if user is in _any_ of the target roles
       * @static
       */
      userIsInRoleAsync: async function (user, roles, options) {
        let id;
        options = Roles._normalizeOptions(options);

        // ensure array to simplify code
        if (!Array.isArray(roles)) roles = [roles];
        roles = roles.filter(r => r != null);
        if (!roles.length) return false;
        Roles._checkScopeName(options.scope);
        options = Object.assign({
          anyScope: false
        }, options);
        if (user && typeof user === 'object') {
          id = user._id;
        } else {
          id = user;
        }
        if (!id) return false;
        if (typeof id !== 'string') return false;
        const selector = {
          'user._id': id
        };
        if (!options.anyScope) {
          selector.scope = {
            $in: [options.scope, null]
          };
        }
        const res = await asyncSome(roles, async roleName => {
          selector['inheritedRoles._id'] = roleName;
          const out = (await Meteor.roleAssignment.countDocuments(selector, {
            limit: 1
          })) > 0;
          return out;
        });
        return res;
      },
      /**
       * Retrieve user's roles.
       *
       * @method getRolesForUserAsync
       * @param {String|Object} user User ID or an actual user object.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of scope to provide roles for; if not specified, global roles are returned
       *   - `anyScope`: if set, role can be in any scope (`scope` and `onlyAssigned` options are ignored)
       *   - `onlyScoped`: if set, only roles in the specified scope are returned
       *   - `onlyAssigned`: return only assigned roles and not automatically inferred (like subroles)
       *   - `fullObjects`: return full roles objects (`true`) or just names (`false`) (`onlyAssigned` option is ignored) (default `false`)
       *     If you have a use-case for this option, please file a feature-request. You shouldn't need to use it as it's
       *     result strongly dependent on the internal data structure of this plugin.
       *
       * Alternatively, it can be a scope name string.
       * @return {Promise<Array>} Array of user's roles, unsorted.
       * @static
       */
      getRolesForUserAsync: async function (user, options) {
        let id;
        options = Roles._normalizeOptions(options);
        Roles._checkScopeName(options.scope);
        options = Object.assign({
          fullObjects: false,
          onlyAssigned: false,
          anyScope: false,
          onlyScoped: false
        }, options);
        if (user && typeof user === 'object') {
          id = user._id;
        } else {
          id = user;
        }
        if (!id) return [];
        const selector = {
          'user._id': id
        };
        const filter = {
          fields: {
            'inheritedRoles._id': 1
          }
        };
        if (!options.anyScope) {
          selector.scope = {
            $in: [options.scope]
          };
          if (!options.onlyScoped) {
            selector.scope.$in.push(null);
          }
        }
        if (options.onlyAssigned) {
          delete filter.fields['inheritedRoles._id'];
          filter.fields['role._id'] = 1;
        }
        if (options.fullObjects) {
          delete filter.fields;
        }
        const roles = await Meteor.roleAssignment.find(selector, filter).fetchAsync();
        if (options.fullObjects) {
          return roles;
        }
        return [...new Set(roles.reduce((rev, current) => {
          if (current.inheritedRoles) {
            return rev.concat(current.inheritedRoles.map(r => r._id));
          } else if (current.role) {
            rev.push(current.role._id);
          }
          return rev;
        }, []))];
      },
      /**
       * Retrieve cursor of all existing roles.
       *
       * @method getAllRoles
       * @param {Object} [queryOptions] Options which are passed directly
       *                                through to `RolesCollection.find(query, options)`.
       * @return {Cursor} Cursor of existing roles.
       * @static
       */
      getAllRoles: function (queryOptions) {
        queryOptions = queryOptions || {
          sort: {
            _id: 1
          }
        };
        return Meteor.roles.find({}, queryOptions);
      },
      /**
       * Retrieve all users who are in target role.
       *
       * Options:
       *
       * @method getUsersInRoleAsync
       * @param {Array|String} roles Name of role or an array of roles. If array, users
       *                             returned will have at least one of the roles
       *                             specified but need not have _all_ roles.
       *                             Roles do not have to exist.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of the scope to restrict roles to; user's global
       *     roles will also be checked
       *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)
       *   - `onlyScoped`: if set, only roles in the specified scope are returned
       *   - `queryOptions`: options which are passed directly
       *     through to `Meteor.users.find(query, options)`
       *
       * Alternatively, it can be a scope name string.
       * @param {Object} [queryOptions] Options which are passed directly
       *                                through to `Meteor.users.find(query, options)`
       * @return {Promise<Cursor>} Cursor of users in roles.
       * @static
       */
      getUsersInRoleAsync: async function (roles, options, queryOptions) {
        const ids = (await Roles.getUserAssignmentsForRole(roles, options).fetchAsync()).map(a => a.user._id);
        return Meteor.users.find({
          _id: {
            $in: ids
          }
        }, options && options.queryOptions || queryOptions || {});
      },
      /**
       * Retrieve all assignments of a user which are for the target role.
       *
       * Options:
       *
       * @method getUserAssignmentsForRole
       * @param {Array|String} roles Name of role or an array of roles. If array, users
       *                             returned will have at least one of the roles
       *                             specified but need not have _all_ roles.
       *                             Roles do not have to exist.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of the scope to restrict roles to; user's global
       *     roles will also be checked
       *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)
       *   - `queryOptions`: options which are passed directly
       *     through to `RoleAssignmentCollection.find(query, options)`
        * Alternatively, it can be a scope name string.
       * @return {Cursor} Cursor of user assignments for roles.
       * @static
       */
      getUserAssignmentsForRole: function (roles, options) {
        options = Roles._normalizeOptions(options);
        options = Object.assign({
          anyScope: false,
          queryOptions: {}
        }, options);
        return Roles._getUsersInRoleCursor(roles, options, options.queryOptions);
      },
      /**
       * @method _getUsersInRoleCursor
       * @param {Array|String} roles Name of role or an array of roles. If array, ids of users are
       *                             returned which have at least one of the roles
       *                             assigned but need not have _all_ roles.
       *                             Roles do not have to exist.
       * @param {Object|String} [options] Options:
       *   - `scope`: name of the scope to restrict roles to; user's global
       *     roles will also be checked
       *   - `anyScope`: if set, role can be in any scope (`scope` option is ignored)
       *
       * Alternatively, it can be a scope name string.
       * @param {Object} [filter] Options which are passed directly
       *                                through to `RoleAssignmentCollection.find(query, options)`
       * @return {Object} Cursor to the assignment documents
       * @private
       * @static
       */
      _getUsersInRoleCursor: function (roles, options, filter) {
        options = Roles._normalizeOptions(options);
        options = Object.assign({
          anyScope: false,
          onlyScoped: false
        }, options);

        // ensure array to simplify code
        if (!Array.isArray(roles)) roles = [roles];
        Roles._checkScopeName(options.scope);
        filter = Object.assign({
          fields: {
            'user._id': 1
          }
        }, filter);
        const selector = {
          'inheritedRoles._id': {
            $in: roles
          }
        };
        if (!options.anyScope) {
          selector.scope = {
            $in: [options.scope]
          };
          if (!options.onlyScoped) {
            selector.scope.$in.push(null);
          }
        }
        return Meteor.roleAssignment.find(selector, filter);
      },
      /**
       * Deprecated. Use `getScopesForUser` instead.
       *
       * @method getGroupsForUserAsync
       * @returns {Promise<Array>}
       * @static
       * @deprecated
       */
      getGroupsForUserAsync: async function () {
        if (!getGroupsForUserDeprecationWarning) {
          getGroupsForUserDeprecationWarning = true;
          console && console.warn('getGroupsForUser has been deprecated. Use getScopesForUser instead.');
        }
        return await Roles.getScopesForUser(...arguments);
      },
      /**
       * Retrieve users scopes, if any.
       *
       * @method getScopesForUserAsync
       * @param {String|Object} user User ID or an actual user object.
       * @param {Array|String} [roles] Name of roles to restrict scopes to.
       *
       * @return {Promise<Array>} Array of user's scopes, unsorted.
       * @static
       */
      getScopesForUserAsync: async function (user, roles) {
        let id;
        if (roles && !Array.isArray(roles)) roles = [roles];
        if (user && typeof user === 'object') {
          id = user._id;
        } else {
          id = user;
        }
        if (!id) return [];
        const selector = {
          'user._id': id,
          scope: {
            $ne: null
          }
        };
        if (roles) {
          selector['inheritedRoles._id'] = {
            $in: roles
          };
        }
        const scopes = (await Meteor.roleAssignment.find(selector, {
          fields: {
            scope: 1
          }
        }).fetchAsync()).map(obi => obi.scope);
        return [...new Set(scopes)];
      },
      /**
       * Rename a scope.
       *
       * Roles assigned with a given scope are changed to be under the new scope.
       *
       * @method renameScopeAsync
       * @param {String} oldName Old name of a scope.
       * @param {String} newName New name of a scope.
       * @returns {Promise}
       * @static
       */
      renameScopeAsync: async function (oldName, newName) {
        let count;
        Roles._checkScopeName(oldName);
        Roles._checkScopeName(newName);
        if (oldName === newName) return;
        do {
          count = await Meteor.roleAssignment.updateAsync({
            scope: oldName
          }, {
            $set: {
              scope: newName
            }
          }, {
            multi: true
          });
        } while (count > 0);
      },
      /**
       * Remove a scope.
       *
       * Roles assigned with a given scope are removed.
       *
       * @method removeScopeAsync
       * @param {String} name The name of a scope.
       * @returns {Promise}
       * @static
       */
      removeScopeAsync: async function (name) {
        Roles._checkScopeName(name);
        await Meteor.roleAssignment.removeAsync({
          scope: name
        });
      },
      /**
       * Throw an exception if `roleName` is an invalid role name.
       *
       * @method _checkRoleName
       * @param {String} roleName A role name to match against.
       * @private
       * @static
       */
      _checkRoleName: function (roleName) {
        if (!roleName || typeof roleName !== 'string' || roleName.trim() !== roleName) {
          throw new Error("Invalid role name '".concat(roleName, "'."));
        }
      },
      /**
       * Find out if a role is an ancestor of another role.
       *
       * WARNING: If you check this on the client, please make sure all roles are published.
       *
       * @method isParentOfAsync
       * @param {String} parentRoleName The role you want to research.
       * @param {String} childRoleName The role you expect to be among the children of parentRoleName.
       * @returns {Promise}
       * @static
       */
      isParentOfAsync: async function (parentRoleName, childRoleName) {
        if (parentRoleName === childRoleName) {
          return true;
        }
        if (parentRoleName == null || childRoleName == null) {
          return false;
        }
        Roles._checkRoleName(parentRoleName);
        Roles._checkRoleName(childRoleName);
        let rolesToCheck = [parentRoleName];
        while (rolesToCheck.length !== 0) {
          const roleName = rolesToCheck.pop();
          if (roleName === childRoleName) {
            return true;
          }
          const role = await Meteor.roles.findOneAsync({
            _id: roleName
          });

          // This should not happen, but this is a problem to address at some other time.
          if (!role) continue;
          rolesToCheck = rolesToCheck.concat(role.children.map(r => r._id));
        }
        return false;
      },
      /**
       * Normalize options.
       *
       * @method _normalizeOptions
       * @param {Object} options Options to normalize.
       * @return {Object} Normalized options.
       * @private
       * @static
       */
      _normalizeOptions: function (options) {
        options = options === undefined ? {} : options;

        // TODO Number will error out on scope validation, we can either error it out here
        // or make it into a string and hence a valid input.
        if (options === null || typeof options === 'string' || typeof options === 'number') {
          options = {
            scope: options
          };
        }
        options.scope = Roles._normalizeScopeName(options.scope);
        return options;
      },
      /**
       * Normalize scope name.
       *
       * @method _normalizeScopeName
       * @param {String} scopeName A scope name to normalize.
       * @return {String} Normalized scope name.
       * @private
       * @static
       */
      _normalizeScopeName: function (scopeName) {
        // map undefined and null to null
        if (scopeName == null) {
          return null;
        } else {
          return scopeName;
        }
      },
      /**
       * Throw an exception if `scopeName` is an invalid scope name.
       *
       * @method _checkScopeName
       * @param {String} scopeName A scope name to match against.
       * @private
       * @static
       */
      _checkScopeName: function (scopeName) {
        if (scopeName === null) return;
        if (!scopeName || typeof scopeName !== 'string' || scopeName.trim() !== scopeName) {
          throw new Error("Invalid scope name '".concat(scopeName, "'."));
        }
      }
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////

},"roles_server.js":function module(require,exports,module){

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                              //
// packages/alanning_roles/roles/roles_server.js                                                                //
//                                                                                                              //
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                                                                                                                //
!module.wrapAsync(async function (module, __reifyWaitForDeps__, __reify_async_result__) {
  "use strict";
  try {
    let RolesCollection, RoleAssignmentCollection;
    module.link("./roles_common_async", {
      RolesCollection(v) {
        RolesCollection = v;
      },
      RoleAssignmentCollection(v) {
        RoleAssignmentCollection = v;
      }
    }, 0);
    if (__reifyWaitForDeps__()) (await __reifyWaitForDeps__())();
    const indexFnAssignment = RoleAssignmentCollection.createIndexAsync.bind(RoleAssignmentCollection);
    const indexFnRoles = RolesCollection.createIndexAsync.bind(RolesCollection);
    const indexes = [{
      'user._id': 1,
      'inheritedRoles._id': 1,
      scope: 1
    }, {
      'user._id': 1,
      'role._id': 1,
      scope: 1
    }, {
      'role._id': 1
    }, {
      scope: 1,
      'user._id': 1,
      'inheritedRoles._id': 1
    },
    // Adding userId and roleId might speed up other queries depending on the first index
    {
      'inheritedRoles._id': 1
    }];
    for (const index of indexes) {
      indexFnAssignment(index);
    }
    indexFnRoles({
      'children._id': 1
    });
    __reify_async_result__();
  } catch (_reifyError) {
    return __reify_async_result__(_reifyError);
  }
  __reify_async_result__()
}, {
  self: this,
  async: false
});
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////

}}}}}},{
  "extensions": [
    ".js",
    ".json",
    ".d.ts"
  ]
});


/* Exports */
return {
  export: function () { return {
      Roles: Roles,
      RolesCollection: RolesCollection,
      RoleAssignmentCollection: RoleAssignmentCollection
    };},
  require: require,
  eagerModulePaths: [
    "/node_modules/meteor/alanning:roles/roles/roles_common_async.js",
    "/node_modules/meteor/alanning:roles/roles/roles_server.js"
  ]
}});

//# sourceURL=meteor://💻app/packages/alanning_roles.js
//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1ldGVvcjovL/CfkrthcHAvcGFja2FnZXMvYWxhbm5pbmc6cm9sZXMvcm9sZXMvcm9sZXNfY29tbW9uX2FzeW5jLmpzIiwibWV0ZW9yOi8v8J+Su2FwcC9wYWNrYWdlcy9hbGFubmluZzpyb2xlcy9yb2xlcy9yb2xlc19zZXJ2ZXIuanMiXSwibmFtZXMiOlsibW9kdWxlMSIsImV4cG9ydCIsIlJvbGVzQ29sbGVjdGlvbiIsIlJvbGVBc3NpZ25tZW50Q29sbGVjdGlvbiIsIk1ldGVvciIsImxpbmsiLCJ2IiwiTW9uZ28iLCJfX3JlaWZ5V2FpdEZvckRlcHNfXyIsIkNvbGxlY3Rpb24iLCJyb2xlcyIsInJvbGVBc3NpZ25tZW50IiwiUm9sZXMiLCJnZXRHcm91cHNGb3JVc2VyRGVwcmVjYXRpb25XYXJuaW5nIiwiYXN5bmNTb21lIiwiYXJyIiwicHJlZGljYXRlIiwiZSIsIk9iamVjdCIsImFzc2lnbiIsIkdMT0JBTF9HUk9VUCIsImNyZWF0ZVJvbGVBc3luYyIsInJvbGVOYW1lIiwib3B0aW9ucyIsIl9jaGVja1JvbGVOYW1lIiwidW5sZXNzRXhpc3RzIiwiaW5zZXJ0ZWRJZCIsImV4aXN0aW5nUm9sZSIsImZpbmRPbmVBc3luYyIsIl9pZCIsInVwZGF0ZUFzeW5jIiwiJHNldE9uSW5zZXJ0IiwiY2hpbGRyZW4iLCJpbnNlcnRBc3luYyIsIkVycm9yIiwiZGVsZXRlUm9sZUFzeW5jIiwiaW5oZXJpdGVkUm9sZXMiLCJyZW1vdmVBc3luYyIsIl9nZXRQYXJlbnRSb2xlTmFtZXNBc3luYyIsInIiLCJmaW5kIiwiJGluIiwiZmV0Y2hBc3luYyIsIiRwdWxsIiwiX2dldEluaGVyaXRlZFJvbGVOYW1lc0FzeW5jIiwiJHNldCIsIm1hcCIsInIyIiwibXVsdGkiLCJsZW5ndGgiLCJyZW5hbWVSb2xlQXN5bmMiLCJvbGROYW1lIiwibmV3TmFtZSIsImNvdW50Iiwicm9sZSIsImFkZFJvbGVzVG9QYXJlbnRBc3luYyIsInJvbGVzTmFtZXMiLCJwYXJlbnROYW1lIiwiQXJyYXkiLCJpc0FycmF5IiwiX2FkZFJvbGVUb1BhcmVudEFzeW5jIiwiY29uY2F0IiwiaW5jbHVkZXMiLCIkbmUiLCIkcHVzaCIsIiRlYWNoIiwicmVtb3ZlUm9sZXNGcm9tUGFyZW50QXN5bmMiLCJfcmVtb3ZlUm9sZUZyb21QYXJlbnRBc3luYyIsImZpZWxkcyIsImFkZFVzZXJzVG9Sb2xlc0FzeW5jIiwidXNlcnMiLCJpZCIsIl9ub3JtYWxpemVPcHRpb25zIiwiX2NoZWNrU2NvcGVOYW1lIiwic2NvcGUiLCJpZkV4aXN0cyIsInVzZXIiLCJfYWRkVXNlclRvUm9sZUFzeW5jIiwic2V0VXNlclJvbGVzQXN5bmMiLCJhbnlTY29wZSIsInNlbGVjdG9yIiwidXNlcklkIiwiZXhpc3RpbmdBc3NpZ25tZW50IiwicmVzIiwicGFyZW50Um9sZXMiLCJTZXQiLCJwYXJlbnRSb2xlIiwiYWRkIiwiZGVsZXRlIiwibmVzdGVkUm9sZXMiLCJyZW1vdmVVc2Vyc0Zyb21Sb2xlc0FzeW5jIiwiX3JlbW92ZVVzZXJGcm9tUm9sZUFzeW5jIiwidXNlcklzSW5Sb2xlQXN5bmMiLCJmaWx0ZXIiLCJvdXQiLCJjb3VudERvY3VtZW50cyIsImxpbWl0IiwiZ2V0Um9sZXNGb3JVc2VyQXN5bmMiLCJmdWxsT2JqZWN0cyIsIm9ubHlBc3NpZ25lZCIsIm9ubHlTY29wZWQiLCJwdXNoIiwicmVkdWNlIiwicmV2IiwiY3VycmVudCIsImdldEFsbFJvbGVzIiwicXVlcnlPcHRpb25zIiwic29ydCIsImdldFVzZXJzSW5Sb2xlQXN5bmMiLCJpZHMiLCJnZXRVc2VyQXNzaWdubWVudHNGb3JSb2xlIiwiYSIsIl9nZXRVc2Vyc0luUm9sZUN1cnNvciIsImdldEdyb3Vwc0ZvclVzZXJBc3luYyIsImNvbnNvbGUiLCJ3YXJuIiwiZ2V0U2NvcGVzRm9yVXNlciIsImFyZ3VtZW50cyIsImdldFNjb3Blc0ZvclVzZXJBc3luYyIsInNjb3BlcyIsIm9iaSIsInJlbmFtZVNjb3BlQXN5bmMiLCJyZW1vdmVTY29wZUFzeW5jIiwibmFtZSIsInRyaW0iLCJpc1BhcmVudE9mQXN5bmMiLCJwYXJlbnRSb2xlTmFtZSIsImNoaWxkUm9sZU5hbWUiLCJyb2xlc1RvQ2hlY2siLCJwb3AiLCJ1bmRlZmluZWQiLCJfbm9ybWFsaXplU2NvcGVOYW1lIiwic2NvcGVOYW1lIiwiX19yZWlmeV9hc3luY19yZXN1bHRfXyIsIl9yZWlmeUVycm9yIiwic2VsZiIsImFzeW5jIiwibW9kdWxlIiwiaW5kZXhGbkFzc2lnbm1lbnQiLCJjcmVhdGVJbmRleEFzeW5jIiwiYmluZCIsImluZGV4Rm5Sb2xlcyIsImluZGV4ZXMiLCJpbmRleCJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFBQUEsT0FBTyxDQUFDQyxNQUFNLENBQUM7TUFBQ0MsZUFBZSxFQUFDQSxDQUFBLEtBQUlBLGVBQWU7TUFBQ0Msd0JBQXdCLEVBQUNBLENBQUEsS0FBSUE7SUFBd0IsQ0FBQyxDQUFDO0lBQUMsSUFBSUMsTUFBTTtJQUFDSixPQUFPLENBQUNLLElBQUksQ0FBQyxlQUFlLEVBQUM7TUFBQ0QsTUFBTUEsQ0FBQ0UsQ0FBQyxFQUFDO1FBQUNGLE1BQU0sR0FBQ0UsQ0FBQztNQUFBO0lBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQztJQUFDLElBQUlDLEtBQUs7SUFBQ1AsT0FBTyxDQUFDSyxJQUFJLENBQUMsY0FBYyxFQUFDO01BQUNFLEtBQUtBLENBQUNELENBQUMsRUFBQztRQUFDQyxLQUFLLEdBQUNELENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJRSxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxNQUFNQSxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQztJQTRCL1IsTUFBTU4sZUFBZSxHQUFHLElBQUlLLEtBQUssQ0FBQ0UsVUFBVSxDQUFDLE9BQU8sQ0FBQztJQUU1RCxJQUFJLENBQUNMLE1BQU0sQ0FBQ00sS0FBSyxFQUFFO01BQ2pCTixNQUFNLENBQUNNLEtBQUssR0FBR1IsZUFBZTtJQUNoQztJQUVPLE1BQU1DLHdCQUF3QixHQUFHLElBQUlJLEtBQUssQ0FBQ0UsVUFBVSxDQUFDLGlCQUFpQixDQUFDO0lBRS9FLElBQUksQ0FBQ0wsTUFBTSxDQUFDTyxjQUFjLEVBQUU7TUFDMUJQLE1BQU0sQ0FBQ08sY0FBYyxHQUFHUix3QkFBd0I7SUFDbEQ7O0lBRUE7QUFDQTtBQUNBO0lBQ0EsSUFBSSxPQUFPUyxLQUFLLEtBQUssV0FBVyxFQUFFO01BQ2hDQSxLQUFLLEdBQUcsQ0FBQyxDQUFDLEVBQUM7SUFDYjtJQUVBLElBQUlDLGtDQUFrQyxHQUFHLEtBQUs7O0lBRTlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtJQUNBLE1BQU1DLFNBQVMsR0FBRyxNQUFBQSxDQUFPQyxHQUFHLEVBQUVDLFNBQVMsS0FBSztNQUMxQyxLQUFLLE1BQU1DLENBQUMsSUFBSUYsR0FBRyxFQUFFO1FBQ25CLElBQUksTUFBTUMsU0FBUyxDQUFDQyxDQUFDLENBQUMsRUFBRSxPQUFPLElBQUk7TUFDckM7TUFDQSxPQUFPLEtBQUs7SUFDZCxDQUFDO0lBRURDLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDUCxLQUFLLEVBQUU7TUFDbkI7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRVEsWUFBWSxFQUFFLElBQUk7TUFFbEI7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRUMsZUFBZSxFQUFFLGVBQUFBLENBQWdCQyxRQUFRLEVBQUVDLE9BQU8sRUFBRTtRQUNsRFgsS0FBSyxDQUFDWSxjQUFjLENBQUNGLFFBQVEsQ0FBQztRQUU5QkMsT0FBTyxHQUFHTCxNQUFNLENBQUNDLE1BQU0sQ0FDckI7VUFDRU0sWUFBWSxFQUFFO1FBQ2hCLENBQUMsRUFDREYsT0FDRixDQUFDO1FBRUQsSUFBSUcsVUFBVSxHQUFHLElBQUk7UUFFckIsTUFBTUMsWUFBWSxHQUFHLE1BQU12QixNQUFNLENBQUNNLEtBQUssQ0FBQ2tCLFlBQVksQ0FBQztVQUFFQyxHQUFHLEVBQUVQO1FBQVMsQ0FBQyxDQUFDO1FBRXZFLElBQUlLLFlBQVksRUFBRTtVQUNoQixNQUFNdkIsTUFBTSxDQUFDTSxLQUFLLENBQUNvQixXQUFXLENBQzVCO1lBQUVELEdBQUcsRUFBRVA7VUFBUyxDQUFDLEVBQ2pCO1lBQUVTLFlBQVksRUFBRTtjQUFFQyxRQUFRLEVBQUU7WUFBRztVQUFFLENBQ25DLENBQUM7VUFDRCxPQUFPLElBQUk7UUFDYixDQUFDLE1BQU07VUFDTE4sVUFBVSxHQUFHLE1BQU10QixNQUFNLENBQUNNLEtBQUssQ0FBQ3VCLFdBQVcsQ0FBQztZQUMxQ0osR0FBRyxFQUFFUCxRQUFRO1lBQ2JVLFFBQVEsRUFBRTtVQUNaLENBQUMsQ0FBQztRQUNKO1FBRUEsSUFBSSxDQUFDTixVQUFVLEVBQUU7VUFDZixJQUFJSCxPQUFPLENBQUNFLFlBQVksRUFBRSxPQUFPLElBQUk7VUFDckMsTUFBTSxJQUFJUyxLQUFLLENBQUMsUUFBUSxHQUFHWixRQUFRLEdBQUcsbUJBQW1CLENBQUM7UUFDNUQ7UUFFQSxPQUFPSSxVQUFVO01BQ25CLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFUyxlQUFlLEVBQUUsZUFBQUEsQ0FBZ0JiLFFBQVEsRUFBRTtRQUN6QyxJQUFJWixLQUFLO1FBQ1QsSUFBSTBCLGNBQWM7UUFFbEJ4QixLQUFLLENBQUNZLGNBQWMsQ0FBQ0YsUUFBUSxDQUFDOztRQUU5QjtRQUNBLE1BQU1sQixNQUFNLENBQUNPLGNBQWMsQ0FBQzBCLFdBQVcsQ0FBQztVQUN0QyxVQUFVLEVBQUVmO1FBQ2QsQ0FBQyxDQUFDO1FBRUYsR0FBRztVQUNEO1VBQ0FaLEtBQUssR0FBRyxNQUFNRSxLQUFLLENBQUMwQix3QkFBd0IsQ0FDMUMsTUFBTWxDLE1BQU0sQ0FBQ00sS0FBSyxDQUFDa0IsWUFBWSxDQUFDO1lBQUVDLEdBQUcsRUFBRVA7VUFBUyxDQUFDLENBQ25ELENBQUM7VUFFRCxLQUFLLE1BQU1pQixDQUFDLElBQUksTUFBTW5DLE1BQU0sQ0FBQ00sS0FBSyxDQUMvQjhCLElBQUksQ0FBQztZQUFFWCxHQUFHLEVBQUU7Y0FBRVksR0FBRyxFQUFFL0I7WUFBTTtVQUFFLENBQUMsQ0FBQyxDQUM3QmdDLFVBQVUsQ0FBQyxDQUFDLEVBQUU7WUFDZixNQUFNdEMsTUFBTSxDQUFDTSxLQUFLLENBQUNvQixXQUFXLENBQzVCO2NBQ0VELEdBQUcsRUFBRVUsQ0FBQyxDQUFDVjtZQUNULENBQUMsRUFDRDtjQUNFYyxLQUFLLEVBQUU7Z0JBQ0xYLFFBQVEsRUFBRTtrQkFDUkgsR0FBRyxFQUFFUDtnQkFDUDtjQUNGO1lBQ0YsQ0FDRixDQUFDO1lBRURjLGNBQWMsR0FBRyxNQUFNeEIsS0FBSyxDQUFDZ0MsMkJBQTJCLENBQ3RELE1BQU14QyxNQUFNLENBQUNNLEtBQUssQ0FBQ2tCLFlBQVksQ0FBQztjQUFFQyxHQUFHLEVBQUVVLENBQUMsQ0FBQ1Y7WUFBSSxDQUFDLENBQ2hELENBQUM7WUFDRCxNQUFNekIsTUFBTSxDQUFDTyxjQUFjLENBQUNtQixXQUFXLENBQ3JDO2NBQ0UsVUFBVSxFQUFFUyxDQUFDLENBQUNWO1lBQ2hCLENBQUMsRUFDRDtjQUNFZ0IsSUFBSSxFQUFFO2dCQUNKVCxjQUFjLEVBQUUsQ0FBQ0csQ0FBQyxDQUFDVixHQUFHLEVBQUUsR0FBR08sY0FBYyxDQUFDLENBQUNVLEdBQUcsQ0FBRUMsRUFBRSxLQUFNO2tCQUN0RGxCLEdBQUcsRUFBRWtCO2dCQUNQLENBQUMsQ0FBQztjQUNKO1lBQ0YsQ0FBQyxFQUNEO2NBQUVDLEtBQUssRUFBRTtZQUFLLENBQ2hCLENBQUM7VUFDSDtRQUNGLENBQUMsUUFBUXRDLEtBQUssQ0FBQ3VDLE1BQU0sR0FBRyxDQUFDOztRQUV6QjtRQUNBLE1BQU03QyxNQUFNLENBQUNNLEtBQUssQ0FBQzJCLFdBQVcsQ0FBQztVQUFFUixHQUFHLEVBQUVQO1FBQVMsQ0FBQyxDQUFDO01BQ25ELENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRTRCLGVBQWUsRUFBRSxlQUFBQSxDQUFnQkMsT0FBTyxFQUFFQyxPQUFPLEVBQUU7UUFDakQsSUFBSUMsS0FBSztRQUVUekMsS0FBSyxDQUFDWSxjQUFjLENBQUMyQixPQUFPLENBQUM7UUFDN0J2QyxLQUFLLENBQUNZLGNBQWMsQ0FBQzRCLE9BQU8sQ0FBQztRQUU3QixJQUFJRCxPQUFPLEtBQUtDLE9BQU8sRUFBRTtRQUV6QixNQUFNRSxJQUFJLEdBQUcsTUFBTWxELE1BQU0sQ0FBQ00sS0FBSyxDQUFDa0IsWUFBWSxDQUFDO1VBQUVDLEdBQUcsRUFBRXNCO1FBQVEsQ0FBQyxDQUFDO1FBRTlELElBQUksQ0FBQ0csSUFBSSxFQUFFO1VBQ1QsTUFBTSxJQUFJcEIsS0FBSyxDQUFDLFFBQVEsR0FBR2lCLE9BQU8sR0FBRyxtQkFBbUIsQ0FBQztRQUMzRDtRQUVBRyxJQUFJLENBQUN6QixHQUFHLEdBQUd1QixPQUFPO1FBRWxCLE1BQU1oRCxNQUFNLENBQUNNLEtBQUssQ0FBQ3VCLFdBQVcsQ0FBQ3FCLElBQUksQ0FBQztRQUVwQyxHQUFHO1VBQ0RELEtBQUssR0FBRyxNQUFNakQsTUFBTSxDQUFDTyxjQUFjLENBQUNtQixXQUFXLENBQzdDO1lBQ0UsVUFBVSxFQUFFcUI7VUFDZCxDQUFDLEVBQ0Q7WUFDRU4sSUFBSSxFQUFFO2NBQ0osVUFBVSxFQUFFTztZQUNkO1VBQ0YsQ0FBQyxFQUNEO1lBQUVKLEtBQUssRUFBRTtVQUFLLENBQ2hCLENBQUM7UUFDSCxDQUFDLFFBQVFLLEtBQUssR0FBRyxDQUFDO1FBRWxCLEdBQUc7VUFDREEsS0FBSyxHQUFHLE1BQU1qRCxNQUFNLENBQUNPLGNBQWMsQ0FBQ21CLFdBQVcsQ0FDN0M7WUFDRSxvQkFBb0IsRUFBRXFCO1VBQ3hCLENBQUMsRUFDRDtZQUNFTixJQUFJLEVBQUU7Y0FDSixzQkFBc0IsRUFBRU87WUFDMUI7VUFDRixDQUFDLEVBQ0Q7WUFBRUosS0FBSyxFQUFFO1VBQUssQ0FDaEIsQ0FBQztRQUNILENBQUMsUUFBUUssS0FBSyxHQUFHLENBQUM7UUFFbEIsR0FBRztVQUNEQSxLQUFLLEdBQUcsTUFBTWpELE1BQU0sQ0FBQ00sS0FBSyxDQUFDb0IsV0FBVyxDQUNwQztZQUNFLGNBQWMsRUFBRXFCO1VBQ2xCLENBQUMsRUFDRDtZQUNFTixJQUFJLEVBQUU7Y0FDSixnQkFBZ0IsRUFBRU87WUFDcEI7VUFDRixDQUFDLEVBQ0Q7WUFBRUosS0FBSyxFQUFFO1VBQUssQ0FDaEIsQ0FBQztRQUNILENBQUMsUUFBUUssS0FBSyxHQUFHLENBQUM7UUFFbEIsTUFBTWpELE1BQU0sQ0FBQ00sS0FBSyxDQUFDMkIsV0FBVyxDQUFDO1VBQUVSLEdBQUcsRUFBRXNCO1FBQVEsQ0FBQyxDQUFDO01BQ2xELENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRUkscUJBQXFCLEVBQUUsZUFBQUEsQ0FBZ0JDLFVBQVUsRUFBRUMsVUFBVSxFQUFFO1FBQzdEO1FBQ0EsSUFBSSxDQUFDQyxLQUFLLENBQUNDLE9BQU8sQ0FBQ0gsVUFBVSxDQUFDLEVBQUVBLFVBQVUsR0FBRyxDQUFDQSxVQUFVLENBQUM7UUFFekQsS0FBSyxNQUFNbEMsUUFBUSxJQUFJa0MsVUFBVSxFQUFFO1VBQ2pDLE1BQU01QyxLQUFLLENBQUNnRCxxQkFBcUIsQ0FBQ3RDLFFBQVEsRUFBRW1DLFVBQVUsQ0FBQztRQUN6RDtNQUNGLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0VHLHFCQUFxQixFQUFFLGVBQUFBLENBQWdCdEMsUUFBUSxFQUFFbUMsVUFBVSxFQUFFO1FBQzNEN0MsS0FBSyxDQUFDWSxjQUFjLENBQUNGLFFBQVEsQ0FBQztRQUM5QlYsS0FBSyxDQUFDWSxjQUFjLENBQUNpQyxVQUFVLENBQUM7O1FBRWhDO1FBQ0EsTUFBTUgsSUFBSSxHQUFHLE1BQU1sRCxNQUFNLENBQUNNLEtBQUssQ0FBQ2tCLFlBQVksQ0FBQztVQUFFQyxHQUFHLEVBQUVQO1FBQVMsQ0FBQyxDQUFDO1FBRS9ELElBQUksQ0FBQ2dDLElBQUksRUFBRTtVQUNULE1BQU0sSUFBSXBCLEtBQUssVUFBQTJCLE1BQUEsQ0FBVXZDLFFBQVEsc0JBQW1CLENBQUM7UUFDdkQ7O1FBRUE7UUFDQSxJQUFJLENBQUMsTUFBTVYsS0FBSyxDQUFDZ0MsMkJBQTJCLENBQUNVLElBQUksQ0FBQyxFQUFFUSxRQUFRLENBQUNMLFVBQVUsQ0FBQyxFQUFFO1VBQ3hFLE1BQU0sSUFBSXZCLEtBQUssV0FBQTJCLE1BQUEsQ0FDSHZDLFFBQVEsYUFBQXVDLE1BQUEsQ0FBVUosVUFBVSwwQkFDeEMsQ0FBQztRQUNIO1FBRUEsTUFBTUosS0FBSyxHQUFHLE1BQU1qRCxNQUFNLENBQUNNLEtBQUssQ0FBQ29CLFdBQVcsQ0FDMUM7VUFDRUQsR0FBRyxFQUFFNEIsVUFBVTtVQUNmLGNBQWMsRUFBRTtZQUNkTSxHQUFHLEVBQUVULElBQUksQ0FBQ3pCO1VBQ1o7UUFDRixDQUFDLEVBQ0Q7VUFDRW1DLEtBQUssRUFBRTtZQUNMaEMsUUFBUSxFQUFFO2NBQ1JILEdBQUcsRUFBRXlCLElBQUksQ0FBQ3pCO1lBQ1o7VUFDRjtRQUNGLENBQ0YsQ0FBQzs7UUFFRDtRQUNBO1FBQ0EsSUFBSSxDQUFDd0IsS0FBSyxFQUFFO1FBRVosTUFBTWpELE1BQU0sQ0FBQ08sY0FBYyxDQUFDbUIsV0FBVyxDQUNyQztVQUNFLG9CQUFvQixFQUFFMkI7UUFDeEIsQ0FBQyxFQUNEO1VBQ0VPLEtBQUssRUFBRTtZQUNMNUIsY0FBYyxFQUFFO2NBQ2Q2QixLQUFLLEVBQUUsQ0FDTFgsSUFBSSxDQUFDekIsR0FBRyxFQUNSLElBQUksTUFBTWpCLEtBQUssQ0FBQ2dDLDJCQUEyQixDQUFDVSxJQUFJLENBQUMsQ0FBQyxDQUNuRCxDQUFDUixHQUFHLENBQUVQLENBQUMsS0FBTTtnQkFBRVYsR0FBRyxFQUFFVTtjQUFFLENBQUMsQ0FBQztZQUMzQjtVQUNGO1FBQ0YsQ0FBQyxFQUNEO1VBQUVTLEtBQUssRUFBRTtRQUFLLENBQ2hCLENBQUM7TUFDSCxDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0VrQiwwQkFBMEIsRUFBRSxlQUFBQSxDQUFnQlYsVUFBVSxFQUFFQyxVQUFVLEVBQUU7UUFDbEU7UUFDQSxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsT0FBTyxDQUFDSCxVQUFVLENBQUMsRUFBRUEsVUFBVSxHQUFHLENBQUNBLFVBQVUsQ0FBQztRQUV6RCxLQUFLLE1BQU1sQyxRQUFRLElBQUlrQyxVQUFVLEVBQUU7VUFDakMsTUFBTTVDLEtBQUssQ0FBQ3VELDBCQUEwQixDQUFDN0MsUUFBUSxFQUFFbUMsVUFBVSxDQUFDO1FBQzlEO01BQ0YsQ0FBQztNQUVEO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRVUsMEJBQTBCLEVBQUUsZUFBQUEsQ0FBZ0I3QyxRQUFRLEVBQUVtQyxVQUFVLEVBQUU7UUFDaEU3QyxLQUFLLENBQUNZLGNBQWMsQ0FBQ0YsUUFBUSxDQUFDO1FBQzlCVixLQUFLLENBQUNZLGNBQWMsQ0FBQ2lDLFVBQVUsQ0FBQzs7UUFFaEM7UUFDQTtRQUNBLE1BQU1ILElBQUksR0FBRyxNQUFNbEQsTUFBTSxDQUFDTSxLQUFLLENBQUNrQixZQUFZLENBQzFDO1VBQUVDLEdBQUcsRUFBRVA7UUFBUyxDQUFDLEVBQ2pCO1VBQUU4QyxNQUFNLEVBQUU7WUFBRXZDLEdBQUcsRUFBRTtVQUFFO1FBQUUsQ0FDdkIsQ0FBQztRQUVELElBQUksQ0FBQ3lCLElBQUksRUFBRTtVQUNULE1BQU0sSUFBSXBCLEtBQUssVUFBQTJCLE1BQUEsQ0FBVXZDLFFBQVEsc0JBQW1CLENBQUM7UUFDdkQ7UUFFQSxNQUFNK0IsS0FBSyxHQUFHLE1BQU1qRCxNQUFNLENBQUNNLEtBQUssQ0FBQ29CLFdBQVcsQ0FDMUM7VUFDRUQsR0FBRyxFQUFFNEI7UUFDUCxDQUFDLEVBQ0Q7VUFDRWQsS0FBSyxFQUFFO1lBQ0xYLFFBQVEsRUFBRTtjQUNSSCxHQUFHLEVBQUV5QixJQUFJLENBQUN6QjtZQUNaO1VBQ0Y7UUFDRixDQUNGLENBQUM7O1FBRUQ7UUFDQTtRQUNBLElBQUksQ0FBQ3dCLEtBQUssRUFBRTs7UUFFWjtRQUNBLE1BQU0zQyxLQUFLLEdBQUcsQ0FDWixJQUFJLE1BQU1FLEtBQUssQ0FBQzBCLHdCQUF3QixDQUN0QyxNQUFNbEMsTUFBTSxDQUFDTSxLQUFLLENBQUNrQixZQUFZLENBQUM7VUFBRUMsR0FBRyxFQUFFNEI7UUFBVyxDQUFDLENBQ3JELENBQUMsQ0FBQyxFQUNGQSxVQUFVLENBQ1g7UUFFRCxLQUFLLE1BQU1sQixDQUFDLElBQUksTUFBTW5DLE1BQU0sQ0FBQ00sS0FBSyxDQUMvQjhCLElBQUksQ0FBQztVQUFFWCxHQUFHLEVBQUU7WUFBRVksR0FBRyxFQUFFL0I7VUFBTTtRQUFFLENBQUMsQ0FBQyxDQUM3QmdDLFVBQVUsQ0FBQyxDQUFDLEVBQUU7VUFDZixNQUFNTixjQUFjLEdBQUcsTUFBTXhCLEtBQUssQ0FBQ2dDLDJCQUEyQixDQUM1RCxNQUFNeEMsTUFBTSxDQUFDTSxLQUFLLENBQUNrQixZQUFZLENBQUM7WUFBRUMsR0FBRyxFQUFFVSxDQUFDLENBQUNWO1VBQUksQ0FBQyxDQUNoRCxDQUFDO1VBQ0QsTUFBTXpCLE1BQU0sQ0FBQ08sY0FBYyxDQUFDbUIsV0FBVyxDQUNyQztZQUNFLFVBQVUsRUFBRVMsQ0FBQyxDQUFDVixHQUFHO1lBQ2pCLG9CQUFvQixFQUFFeUIsSUFBSSxDQUFDekI7VUFDN0IsQ0FBQyxFQUNEO1lBQ0VnQixJQUFJLEVBQUU7Y0FDSlQsY0FBYyxFQUFFLENBQUNHLENBQUMsQ0FBQ1YsR0FBRyxFQUFFLEdBQUdPLGNBQWMsQ0FBQyxDQUFDVSxHQUFHLENBQUVDLEVBQUUsS0FBTTtnQkFDdERsQixHQUFHLEVBQUVrQjtjQUNQLENBQUMsQ0FBQztZQUNKO1VBQ0YsQ0FBQyxFQUNEO1lBQUVDLEtBQUssRUFBRTtVQUFLLENBQ2hCLENBQUM7UUFDSDtNQUNGLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFcUIsb0JBQW9CLEVBQUUsZUFBQUEsQ0FBZ0JDLEtBQUssRUFBRTVELEtBQUssRUFBRWEsT0FBTyxFQUFFO1FBQzNELElBQUlnRCxFQUFFO1FBRU4sSUFBSSxDQUFDRCxLQUFLLEVBQUUsTUFBTSxJQUFJcEMsS0FBSyxDQUFDLHdCQUF3QixDQUFDO1FBQ3JELElBQUksQ0FBQ3hCLEtBQUssRUFBRSxNQUFNLElBQUl3QixLQUFLLENBQUMsd0JBQXdCLENBQUM7UUFFckRYLE9BQU8sR0FBR1gsS0FBSyxDQUFDNEQsaUJBQWlCLENBQUNqRCxPQUFPLENBQUM7O1FBRTFDO1FBQ0EsSUFBSSxDQUFDbUMsS0FBSyxDQUFDQyxPQUFPLENBQUNXLEtBQUssQ0FBQyxFQUFFQSxLQUFLLEdBQUcsQ0FBQ0EsS0FBSyxDQUFDO1FBQzFDLElBQUksQ0FBQ1osS0FBSyxDQUFDQyxPQUFPLENBQUNqRCxLQUFLLENBQUMsRUFBRUEsS0FBSyxHQUFHLENBQUNBLEtBQUssQ0FBQztRQUUxQ0UsS0FBSyxDQUFDNkQsZUFBZSxDQUFDbEQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDO1FBRXBDbkQsT0FBTyxHQUFHTCxNQUFNLENBQUNDLE1BQU0sQ0FDckI7VUFDRXdELFFBQVEsRUFBRTtRQUNaLENBQUMsRUFDRHBELE9BQ0YsQ0FBQztRQUVELEtBQUssTUFBTXFELElBQUksSUFBSU4sS0FBSyxFQUFFO1VBQ3hCLElBQUksT0FBT00sSUFBSSxLQUFLLFFBQVEsRUFBRTtZQUM1QkwsRUFBRSxHQUFHSyxJQUFJLENBQUMvQyxHQUFHO1VBQ2YsQ0FBQyxNQUFNO1lBQ0wwQyxFQUFFLEdBQUdLLElBQUk7VUFDWDtVQUVBLEtBQUssTUFBTXRCLElBQUksSUFBSTVDLEtBQUssRUFBRTtZQUN4QixNQUFNRSxLQUFLLENBQUNpRSxtQkFBbUIsQ0FBQ04sRUFBRSxFQUFFakIsSUFBSSxFQUFFL0IsT0FBTyxDQUFDO1VBQ3BEO1FBQ0Y7TUFDRixDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFdUQsaUJBQWlCLEVBQUUsZUFBQUEsQ0FBZ0JSLEtBQUssRUFBRTVELEtBQUssRUFBRWEsT0FBTyxFQUFFO1FBQ3hELElBQUlnRCxFQUFFO1FBRU4sSUFBSSxDQUFDRCxLQUFLLEVBQUUsTUFBTSxJQUFJcEMsS0FBSyxDQUFDLHdCQUF3QixDQUFDO1FBQ3JELElBQUksQ0FBQ3hCLEtBQUssRUFBRSxNQUFNLElBQUl3QixLQUFLLENBQUMsd0JBQXdCLENBQUM7UUFFckRYLE9BQU8sR0FBR1gsS0FBSyxDQUFDNEQsaUJBQWlCLENBQUNqRCxPQUFPLENBQUM7O1FBRTFDO1FBQ0EsSUFBSSxDQUFDbUMsS0FBSyxDQUFDQyxPQUFPLENBQUNXLEtBQUssQ0FBQyxFQUFFQSxLQUFLLEdBQUcsQ0FBQ0EsS0FBSyxDQUFDO1FBQzFDLElBQUksQ0FBQ1osS0FBSyxDQUFDQyxPQUFPLENBQUNqRCxLQUFLLENBQUMsRUFBRUEsS0FBSyxHQUFHLENBQUNBLEtBQUssQ0FBQztRQUUxQ0UsS0FBSyxDQUFDNkQsZUFBZSxDQUFDbEQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDO1FBRXBDbkQsT0FBTyxHQUFHTCxNQUFNLENBQUNDLE1BQU0sQ0FDckI7VUFDRXdELFFBQVEsRUFBRSxLQUFLO1VBQ2ZJLFFBQVEsRUFBRTtRQUNaLENBQUMsRUFDRHhELE9BQ0YsQ0FBQztRQUVELEtBQUssTUFBTXFELElBQUksSUFBSU4sS0FBSyxFQUFFO1VBQ3hCLElBQUksT0FBT00sSUFBSSxLQUFLLFFBQVEsRUFBRTtZQUM1QkwsRUFBRSxHQUFHSyxJQUFJLENBQUMvQyxHQUFHO1VBQ2YsQ0FBQyxNQUFNO1lBQ0wwQyxFQUFFLEdBQUdLLElBQUk7VUFDWDtVQUNBO1VBQ0EsTUFBTUksUUFBUSxHQUFHO1lBQUUsVUFBVSxFQUFFVDtVQUFHLENBQUM7VUFDbkMsSUFBSSxDQUFDaEQsT0FBTyxDQUFDd0QsUUFBUSxFQUFFO1lBQ3JCQyxRQUFRLENBQUNOLEtBQUssR0FBR25ELE9BQU8sQ0FBQ21ELEtBQUs7VUFDaEM7VUFFQSxNQUFNdEUsTUFBTSxDQUFDTyxjQUFjLENBQUMwQixXQUFXLENBQUMyQyxRQUFRLENBQUM7O1VBRWpEO1VBQ0EsS0FBSyxNQUFNMUIsSUFBSSxJQUFJNUMsS0FBSyxFQUFFO1lBQ3hCLE1BQU1FLEtBQUssQ0FBQ2lFLG1CQUFtQixDQUFDTixFQUFFLEVBQUVqQixJQUFJLEVBQUUvQixPQUFPLENBQUM7VUFDcEQ7UUFDRjtNQUNGLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFc0QsbUJBQW1CLEVBQUUsZUFBQUEsQ0FBZ0JJLE1BQU0sRUFBRTNELFFBQVEsRUFBRUMsT0FBTyxFQUFFO1FBQzlEWCxLQUFLLENBQUNZLGNBQWMsQ0FBQ0YsUUFBUSxDQUFDO1FBQzlCVixLQUFLLENBQUM2RCxlQUFlLENBQUNsRCxPQUFPLENBQUNtRCxLQUFLLENBQUM7UUFFcEMsSUFBSSxDQUFDTyxNQUFNLEVBQUU7VUFDWDtRQUNGO1FBRUEsTUFBTTNCLElBQUksR0FBRyxNQUFNbEQsTUFBTSxDQUFDTSxLQUFLLENBQUNrQixZQUFZLENBQzFDO1VBQUVDLEdBQUcsRUFBRVA7UUFBUyxDQUFDLEVBQ2pCO1VBQUU4QyxNQUFNLEVBQUU7WUFBRXBDLFFBQVEsRUFBRTtVQUFFO1FBQUUsQ0FDNUIsQ0FBQztRQUVELElBQUksQ0FBQ3NCLElBQUksRUFBRTtVQUNULElBQUkvQixPQUFPLENBQUNvRCxRQUFRLEVBQUU7WUFDcEIsT0FBTyxFQUFFO1VBQ1gsQ0FBQyxNQUFNO1lBQ0wsTUFBTSxJQUFJekMsS0FBSyxDQUFDLFFBQVEsR0FBR1osUUFBUSxHQUFHLG1CQUFtQixDQUFDO1VBQzVEO1FBQ0Y7O1FBRUE7UUFDQTtRQUNBO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7UUFDSSxNQUFNNEQsa0JBQWtCLEdBQUcsTUFBTTlFLE1BQU0sQ0FBQ08sY0FBYyxDQUFDaUIsWUFBWSxDQUFDO1VBQ2xFLFVBQVUsRUFBRXFELE1BQU07VUFDbEIsVUFBVSxFQUFFM0QsUUFBUTtVQUNwQm9ELEtBQUssRUFBRW5ELE9BQU8sQ0FBQ21EO1FBQ2pCLENBQUMsQ0FBQztRQUVGLElBQUloRCxVQUFVO1FBQ2QsSUFBSXlELEdBQUc7UUFDUCxJQUFJRCxrQkFBa0IsRUFBRTtVQUN0QixNQUFNOUUsTUFBTSxDQUFDTyxjQUFjLENBQUNtQixXQUFXLENBQUNvRCxrQkFBa0IsQ0FBQ3JELEdBQUcsRUFBRTtZQUM5RGdCLElBQUksRUFBRTtjQUNKK0IsSUFBSSxFQUFFO2dCQUFFL0MsR0FBRyxFQUFFb0Q7Y0FBTyxDQUFDO2NBQ3JCM0IsSUFBSSxFQUFFO2dCQUFFekIsR0FBRyxFQUFFUDtjQUFTLENBQUM7Y0FDdkJvRCxLQUFLLEVBQUVuRCxPQUFPLENBQUNtRDtZQUNqQjtVQUNGLENBQUMsQ0FBQztVQUVGUyxHQUFHLEdBQUcsTUFBTS9FLE1BQU0sQ0FBQ08sY0FBYyxDQUFDaUIsWUFBWSxDQUFDc0Qsa0JBQWtCLENBQUNyRCxHQUFHLENBQUM7UUFDeEUsQ0FBQyxNQUFNO1VBQ0xILFVBQVUsR0FBRyxNQUFNdEIsTUFBTSxDQUFDTyxjQUFjLENBQUNzQixXQUFXLENBQUM7WUFDbkQyQyxJQUFJLEVBQUU7Y0FBRS9DLEdBQUcsRUFBRW9EO1lBQU8sQ0FBQztZQUNyQjNCLElBQUksRUFBRTtjQUFFekIsR0FBRyxFQUFFUDtZQUFTLENBQUM7WUFDdkJvRCxLQUFLLEVBQUVuRCxPQUFPLENBQUNtRDtVQUNqQixDQUFDLENBQUM7UUFDSjtRQUVBLElBQUloRCxVQUFVLEVBQUU7VUFDZCxNQUFNdEIsTUFBTSxDQUFDTyxjQUFjLENBQUNtQixXQUFXLENBQ3JDO1lBQUVELEdBQUcsRUFBRUg7VUFBVyxDQUFDLEVBQ25CO1lBQ0VtQixJQUFJLEVBQUU7Y0FDSlQsY0FBYyxFQUFFLENBQ2RkLFFBQVEsRUFDUixJQUFJLE1BQU1WLEtBQUssQ0FBQ2dDLDJCQUEyQixDQUFDVSxJQUFJLENBQUMsQ0FBQyxDQUNuRCxDQUFDUixHQUFHLENBQUVQLENBQUMsS0FBTTtnQkFBRVYsR0FBRyxFQUFFVTtjQUFFLENBQUMsQ0FBQztZQUMzQjtVQUNGLENBQ0YsQ0FBQztVQUVENEMsR0FBRyxHQUFHLE1BQU0vRSxNQUFNLENBQUNPLGNBQWMsQ0FBQ2lCLFlBQVksQ0FBQztZQUFFQyxHQUFHLEVBQUVIO1VBQVcsQ0FBQyxDQUFDO1FBQ3JFO1FBQ0F5RCxHQUFHLENBQUN6RCxVQUFVLEdBQUdBLFVBQVUsRUFBQzs7UUFFNUIsT0FBT3lELEdBQUc7TUFDWixDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0U3Qyx3QkFBd0IsRUFBRSxlQUFBQSxDQUFnQmdCLElBQUksRUFBRTtRQUM5QyxJQUFJLENBQUNBLElBQUksRUFBRTtVQUNULE9BQU8sRUFBRTtRQUNYO1FBRUEsTUFBTThCLFdBQVcsR0FBRyxJQUFJQyxHQUFHLENBQUMsQ0FBQy9CLElBQUksQ0FBQ3pCLEdBQUcsQ0FBQyxDQUFDO1FBRXZDLEtBQUssTUFBTVAsUUFBUSxJQUFJOEQsV0FBVyxFQUFFO1VBQ2xDLEtBQUssTUFBTUUsVUFBVSxJQUFJLE1BQU1sRixNQUFNLENBQUNNLEtBQUssQ0FDeEM4QixJQUFJLENBQUM7WUFBRSxjQUFjLEVBQUVsQjtVQUFTLENBQUMsQ0FBQyxDQUNsQ29CLFVBQVUsQ0FBQyxDQUFDLEVBQUU7WUFDZjBDLFdBQVcsQ0FBQ0csR0FBRyxDQUFDRCxVQUFVLENBQUN6RCxHQUFHLENBQUM7VUFDakM7UUFDRjtRQUVBdUQsV0FBVyxDQUFDSSxNQUFNLENBQUNsQyxJQUFJLENBQUN6QixHQUFHLENBQUM7UUFFNUIsT0FBTyxDQUFDLEdBQUd1RCxXQUFXLENBQUM7TUFDekIsQ0FBQztNQUVEO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFeEMsMkJBQTJCLEVBQUUsZUFBQUEsQ0FBZ0JVLElBQUksRUFBRTtRQUNqRCxNQUFNbEIsY0FBYyxHQUFHLElBQUlpRCxHQUFHLENBQUMsQ0FBQztRQUNoQyxNQUFNSSxXQUFXLEdBQUcsSUFBSUosR0FBRyxDQUFDLENBQUMvQixJQUFJLENBQUMsQ0FBQztRQUVuQyxLQUFLLE1BQU1mLENBQUMsSUFBSWtELFdBQVcsRUFBRTtVQUMzQixNQUFNL0UsS0FBSyxHQUFHLE1BQU1OLE1BQU0sQ0FBQ00sS0FBSyxDQUM3QjhCLElBQUksQ0FDSDtZQUFFWCxHQUFHLEVBQUU7Y0FBRVksR0FBRyxFQUFFRixDQUFDLENBQUNQLFFBQVEsQ0FBQ2MsR0FBRyxDQUFFUCxDQUFDLElBQUtBLENBQUMsQ0FBQ1YsR0FBRztZQUFFO1VBQUUsQ0FBQyxFQUM5QztZQUFFdUMsTUFBTSxFQUFFO2NBQUVwQyxRQUFRLEVBQUU7WUFBRTtVQUFFLENBQzVCLENBQUMsQ0FDQVUsVUFBVSxDQUFDLENBQUM7VUFFZixLQUFLLE1BQU1LLEVBQUUsSUFBSXJDLEtBQUssRUFBRTtZQUN0QjBCLGNBQWMsQ0FBQ21ELEdBQUcsQ0FBQ3hDLEVBQUUsQ0FBQ2xCLEdBQUcsQ0FBQztZQUMxQjRELFdBQVcsQ0FBQ0YsR0FBRyxDQUFDeEMsRUFBRSxDQUFDO1VBQ3JCO1FBQ0Y7UUFFQSxPQUFPLENBQUMsR0FBR1gsY0FBYyxDQUFDO01BQzVCLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFc0QseUJBQXlCLEVBQUUsZUFBQUEsQ0FBZ0JwQixLQUFLLEVBQUU1RCxLQUFLLEVBQUVhLE9BQU8sRUFBRTtRQUNoRSxJQUFJLENBQUMrQyxLQUFLLEVBQUUsTUFBTSxJQUFJcEMsS0FBSyxDQUFDLHdCQUF3QixDQUFDO1FBQ3JELElBQUksQ0FBQ3hCLEtBQUssRUFBRSxNQUFNLElBQUl3QixLQUFLLENBQUMsd0JBQXdCLENBQUM7UUFFckRYLE9BQU8sR0FBR1gsS0FBSyxDQUFDNEQsaUJBQWlCLENBQUNqRCxPQUFPLENBQUM7O1FBRTFDO1FBQ0EsSUFBSSxDQUFDbUMsS0FBSyxDQUFDQyxPQUFPLENBQUNXLEtBQUssQ0FBQyxFQUFFQSxLQUFLLEdBQUcsQ0FBQ0EsS0FBSyxDQUFDO1FBQzFDLElBQUksQ0FBQ1osS0FBSyxDQUFDQyxPQUFPLENBQUNqRCxLQUFLLENBQUMsRUFBRUEsS0FBSyxHQUFHLENBQUNBLEtBQUssQ0FBQztRQUUxQ0UsS0FBSyxDQUFDNkQsZUFBZSxDQUFDbEQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDO1FBRXBDLEtBQUssTUFBTUUsSUFBSSxJQUFJTixLQUFLLEVBQUU7VUFDeEIsSUFBSSxDQUFDTSxJQUFJLEVBQUU7VUFFWCxLQUFLLE1BQU10QixJQUFJLElBQUk1QyxLQUFLLEVBQUU7WUFDeEIsSUFBSTZELEVBQUU7WUFDTixJQUFJLE9BQU9LLElBQUksS0FBSyxRQUFRLEVBQUU7Y0FDNUJMLEVBQUUsR0FBR0ssSUFBSSxDQUFDL0MsR0FBRztZQUNmLENBQUMsTUFBTTtjQUNMMEMsRUFBRSxHQUFHSyxJQUFJO1lBQ1g7WUFFQSxNQUFNaEUsS0FBSyxDQUFDK0Usd0JBQXdCLENBQUNwQixFQUFFLEVBQUVqQixJQUFJLEVBQUUvQixPQUFPLENBQUM7VUFDekQ7UUFDRjtNQUNGLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFb0Usd0JBQXdCLEVBQUUsZUFBQUEsQ0FBZ0JWLE1BQU0sRUFBRTNELFFBQVEsRUFBRUMsT0FBTyxFQUFFO1FBQ25FWCxLQUFLLENBQUNZLGNBQWMsQ0FBQ0YsUUFBUSxDQUFDO1FBQzlCVixLQUFLLENBQUM2RCxlQUFlLENBQUNsRCxPQUFPLENBQUNtRCxLQUFLLENBQUM7UUFFcEMsSUFBSSxDQUFDTyxNQUFNLEVBQUU7UUFFYixNQUFNRCxRQUFRLEdBQUc7VUFDZixVQUFVLEVBQUVDLE1BQU07VUFDbEIsVUFBVSxFQUFFM0Q7UUFDZCxDQUFDO1FBRUQsSUFBSSxDQUFDQyxPQUFPLENBQUN3RCxRQUFRLEVBQUU7VUFDckJDLFFBQVEsQ0FBQ04sS0FBSyxHQUFHbkQsT0FBTyxDQUFDbUQsS0FBSztRQUNoQztRQUVBLE1BQU10RSxNQUFNLENBQUNPLGNBQWMsQ0FBQzBCLFdBQVcsQ0FBQzJDLFFBQVEsQ0FBQztNQUNuRCxDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFWSxpQkFBaUIsRUFBRSxlQUFBQSxDQUFnQmhCLElBQUksRUFBRWxFLEtBQUssRUFBRWEsT0FBTyxFQUFFO1FBQ3ZELElBQUlnRCxFQUFFO1FBRU5oRCxPQUFPLEdBQUdYLEtBQUssQ0FBQzRELGlCQUFpQixDQUFDakQsT0FBTyxDQUFDOztRQUUxQztRQUNBLElBQUksQ0FBQ21DLEtBQUssQ0FBQ0MsT0FBTyxDQUFDakQsS0FBSyxDQUFDLEVBQUVBLEtBQUssR0FBRyxDQUFDQSxLQUFLLENBQUM7UUFFMUNBLEtBQUssR0FBR0EsS0FBSyxDQUFDbUYsTUFBTSxDQUFFdEQsQ0FBQyxJQUFLQSxDQUFDLElBQUksSUFBSSxDQUFDO1FBRXRDLElBQUksQ0FBQzdCLEtBQUssQ0FBQ3VDLE1BQU0sRUFBRSxPQUFPLEtBQUs7UUFFL0JyQyxLQUFLLENBQUM2RCxlQUFlLENBQUNsRCxPQUFPLENBQUNtRCxLQUFLLENBQUM7UUFFcENuRCxPQUFPLEdBQUdMLE1BQU0sQ0FBQ0MsTUFBTSxDQUNyQjtVQUNFNEQsUUFBUSxFQUFFO1FBQ1osQ0FBQyxFQUNEeEQsT0FDRixDQUFDO1FBRUQsSUFBSXFELElBQUksSUFBSSxPQUFPQSxJQUFJLEtBQUssUUFBUSxFQUFFO1VBQ3BDTCxFQUFFLEdBQUdLLElBQUksQ0FBQy9DLEdBQUc7UUFDZixDQUFDLE1BQU07VUFDTDBDLEVBQUUsR0FBR0ssSUFBSTtRQUNYO1FBRUEsSUFBSSxDQUFDTCxFQUFFLEVBQUUsT0FBTyxLQUFLO1FBQ3JCLElBQUksT0FBT0EsRUFBRSxLQUFLLFFBQVEsRUFBRSxPQUFPLEtBQUs7UUFFeEMsTUFBTVMsUUFBUSxHQUFHO1VBQ2YsVUFBVSxFQUFFVDtRQUNkLENBQUM7UUFFRCxJQUFJLENBQUNoRCxPQUFPLENBQUN3RCxRQUFRLEVBQUU7VUFDckJDLFFBQVEsQ0FBQ04sS0FBSyxHQUFHO1lBQUVqQyxHQUFHLEVBQUUsQ0FBQ2xCLE9BQU8sQ0FBQ21ELEtBQUssRUFBRSxJQUFJO1VBQUUsQ0FBQztRQUNqRDtRQUVBLE1BQU1TLEdBQUcsR0FBRyxNQUFNckUsU0FBUyxDQUFDSixLQUFLLEVBQUUsTUFBT1ksUUFBUSxJQUFLO1VBQ3JEMEQsUUFBUSxDQUFDLG9CQUFvQixDQUFDLEdBQUcxRCxRQUFRO1VBQ3pDLE1BQU13RSxHQUFHLEdBQ1AsQ0FBQyxNQUFNMUYsTUFBTSxDQUFDTyxjQUFjLENBQUNvRixjQUFjLENBQUNmLFFBQVEsRUFBRTtZQUFFZ0IsS0FBSyxFQUFFO1VBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQztVQUMxRSxPQUFPRixHQUFHO1FBQ1osQ0FBQyxDQUFDO1FBRUYsT0FBT1gsR0FBRztNQUNaLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRWMsb0JBQW9CLEVBQUUsZUFBQUEsQ0FBZ0JyQixJQUFJLEVBQUVyRCxPQUFPLEVBQUU7UUFDbkQsSUFBSWdELEVBQUU7UUFFTmhELE9BQU8sR0FBR1gsS0FBSyxDQUFDNEQsaUJBQWlCLENBQUNqRCxPQUFPLENBQUM7UUFFMUNYLEtBQUssQ0FBQzZELGVBQWUsQ0FBQ2xELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQztRQUVwQ25ELE9BQU8sR0FBR0wsTUFBTSxDQUFDQyxNQUFNLENBQUM7VUFDdEIrRSxXQUFXLEVBQUUsS0FBSztVQUNsQkMsWUFBWSxFQUFFLEtBQUs7VUFDbkJwQixRQUFRLEVBQUUsS0FBSztVQUNmcUIsVUFBVSxFQUFFO1FBQ2QsQ0FBQyxFQUFFN0UsT0FBTyxDQUFDO1FBRVgsSUFBSXFELElBQUksSUFBSSxPQUFPQSxJQUFJLEtBQUssUUFBUSxFQUFFO1VBQ3BDTCxFQUFFLEdBQUdLLElBQUksQ0FBQy9DLEdBQUc7UUFDZixDQUFDLE1BQU07VUFDTDBDLEVBQUUsR0FBR0ssSUFBSTtRQUNYO1FBRUEsSUFBSSxDQUFDTCxFQUFFLEVBQUUsT0FBTyxFQUFFO1FBRWxCLE1BQU1TLFFBQVEsR0FBRztVQUNmLFVBQVUsRUFBRVQ7UUFDZCxDQUFDO1FBRUQsTUFBTXNCLE1BQU0sR0FBRztVQUNiekIsTUFBTSxFQUFFO1lBQUUsb0JBQW9CLEVBQUU7VUFBRTtRQUNwQyxDQUFDO1FBRUQsSUFBSSxDQUFDN0MsT0FBTyxDQUFDd0QsUUFBUSxFQUFFO1VBQ3JCQyxRQUFRLENBQUNOLEtBQUssR0FBRztZQUFFakMsR0FBRyxFQUFFLENBQUNsQixPQUFPLENBQUNtRCxLQUFLO1VBQUUsQ0FBQztVQUV6QyxJQUFJLENBQUNuRCxPQUFPLENBQUM2RSxVQUFVLEVBQUU7WUFDdkJwQixRQUFRLENBQUNOLEtBQUssQ0FBQ2pDLEdBQUcsQ0FBQzRELElBQUksQ0FBQyxJQUFJLENBQUM7VUFDL0I7UUFDRjtRQUVBLElBQUk5RSxPQUFPLENBQUM0RSxZQUFZLEVBQUU7VUFDeEIsT0FBT04sTUFBTSxDQUFDekIsTUFBTSxDQUFDLG9CQUFvQixDQUFDO1VBQzFDeUIsTUFBTSxDQUFDekIsTUFBTSxDQUFDLFVBQVUsQ0FBQyxHQUFHLENBQUM7UUFDL0I7UUFFQSxJQUFJN0MsT0FBTyxDQUFDMkUsV0FBVyxFQUFFO1VBQ3ZCLE9BQU9MLE1BQU0sQ0FBQ3pCLE1BQU07UUFDdEI7UUFFQSxNQUFNMUQsS0FBSyxHQUFHLE1BQU1OLE1BQU0sQ0FBQ08sY0FBYyxDQUFDNkIsSUFBSSxDQUFDd0MsUUFBUSxFQUFFYSxNQUFNLENBQUMsQ0FBQ25ELFVBQVUsQ0FBQyxDQUFDO1FBRTdFLElBQUluQixPQUFPLENBQUMyRSxXQUFXLEVBQUU7VUFDdkIsT0FBT3hGLEtBQUs7UUFDZDtRQUVBLE9BQU8sQ0FDTCxHQUFHLElBQUkyRSxHQUFHLENBQ1IzRSxLQUFLLENBQUM0RixNQUFNLENBQUMsQ0FBQ0MsR0FBRyxFQUFFQyxPQUFPLEtBQUs7VUFDN0IsSUFBSUEsT0FBTyxDQUFDcEUsY0FBYyxFQUFFO1lBQzFCLE9BQU9tRSxHQUFHLENBQUMxQyxNQUFNLENBQUMyQyxPQUFPLENBQUNwRSxjQUFjLENBQUNVLEdBQUcsQ0FBRVAsQ0FBQyxJQUFLQSxDQUFDLENBQUNWLEdBQUcsQ0FBQyxDQUFDO1VBQzdELENBQUMsTUFBTSxJQUFJMkUsT0FBTyxDQUFDbEQsSUFBSSxFQUFFO1lBQ3ZCaUQsR0FBRyxDQUFDRixJQUFJLENBQUNHLE9BQU8sQ0FBQ2xELElBQUksQ0FBQ3pCLEdBQUcsQ0FBQztVQUM1QjtVQUNBLE9BQU8wRSxHQUFHO1FBQ1osQ0FBQyxFQUFFLEVBQUUsQ0FDUCxDQUFDLENBQ0Y7TUFDSCxDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0VFLFdBQVcsRUFBRSxTQUFBQSxDQUFVQyxZQUFZLEVBQUU7UUFDbkNBLFlBQVksR0FBR0EsWUFBWSxJQUFJO1VBQUVDLElBQUksRUFBRTtZQUFFOUUsR0FBRyxFQUFFO1VBQUU7UUFBRSxDQUFDO1FBRW5ELE9BQU96QixNQUFNLENBQUNNLEtBQUssQ0FBQzhCLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRWtFLFlBQVksQ0FBQztNQUM1QyxDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0VFLG1CQUFtQixFQUFFLGVBQUFBLENBQWdCbEcsS0FBSyxFQUFFYSxPQUFPLEVBQUVtRixZQUFZLEVBQUU7UUFDakUsTUFBTUcsR0FBRyxHQUFHLENBQ1YsTUFBTWpHLEtBQUssQ0FBQ2tHLHlCQUF5QixDQUFDcEcsS0FBSyxFQUFFYSxPQUFPLENBQUMsQ0FBQ21CLFVBQVUsQ0FBQyxDQUFDLEVBQ2xFSSxHQUFHLENBQUVpRSxDQUFDLElBQUtBLENBQUMsQ0FBQ25DLElBQUksQ0FBQy9DLEdBQUcsQ0FBQztRQUV4QixPQUFPekIsTUFBTSxDQUFDa0UsS0FBSyxDQUFDOUIsSUFBSSxDQUN0QjtVQUFFWCxHQUFHLEVBQUU7WUFBRVksR0FBRyxFQUFFb0U7VUFBSTtRQUFFLENBQUMsRUFDcEJ0RixPQUFPLElBQUlBLE9BQU8sQ0FBQ21GLFlBQVksSUFBS0EsWUFBWSxJQUFJLENBQUMsQ0FDeEQsQ0FBQztNQUNILENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BRUVJLHlCQUF5QixFQUFFLFNBQUFBLENBQVVwRyxLQUFLLEVBQUVhLE9BQU8sRUFBRTtRQUNuREEsT0FBTyxHQUFHWCxLQUFLLENBQUM0RCxpQkFBaUIsQ0FBQ2pELE9BQU8sQ0FBQztRQUUxQ0EsT0FBTyxHQUFHTCxNQUFNLENBQUNDLE1BQU0sQ0FDckI7VUFDRTRELFFBQVEsRUFBRSxLQUFLO1VBQ2YyQixZQUFZLEVBQUUsQ0FBQztRQUNqQixDQUFDLEVBQ0RuRixPQUNGLENBQUM7UUFFRCxPQUFPWCxLQUFLLENBQUNvRyxxQkFBcUIsQ0FBQ3RHLEtBQUssRUFBRWEsT0FBTyxFQUFFQSxPQUFPLENBQUNtRixZQUFZLENBQUM7TUFDMUUsQ0FBQztNQUVEO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFTSxxQkFBcUIsRUFBRSxTQUFBQSxDQUFVdEcsS0FBSyxFQUFFYSxPQUFPLEVBQUVzRSxNQUFNLEVBQUU7UUFDdkR0RSxPQUFPLEdBQUdYLEtBQUssQ0FBQzRELGlCQUFpQixDQUFDakQsT0FBTyxDQUFDO1FBRTFDQSxPQUFPLEdBQUdMLE1BQU0sQ0FBQ0MsTUFBTSxDQUNyQjtVQUNFNEQsUUFBUSxFQUFFLEtBQUs7VUFDZnFCLFVBQVUsRUFBRTtRQUNkLENBQUMsRUFDRDdFLE9BQ0YsQ0FBQzs7UUFFRDtRQUNBLElBQUksQ0FBQ21DLEtBQUssQ0FBQ0MsT0FBTyxDQUFDakQsS0FBSyxDQUFDLEVBQUVBLEtBQUssR0FBRyxDQUFDQSxLQUFLLENBQUM7UUFFMUNFLEtBQUssQ0FBQzZELGVBQWUsQ0FBQ2xELE9BQU8sQ0FBQ21ELEtBQUssQ0FBQztRQUVwQ21CLE1BQU0sR0FBRzNFLE1BQU0sQ0FBQ0MsTUFBTSxDQUNwQjtVQUNFaUQsTUFBTSxFQUFFO1lBQUUsVUFBVSxFQUFFO1VBQUU7UUFDMUIsQ0FBQyxFQUNEeUIsTUFDRixDQUFDO1FBRUQsTUFBTWIsUUFBUSxHQUFHO1VBQ2Ysb0JBQW9CLEVBQUU7WUFBRXZDLEdBQUcsRUFBRS9CO1VBQU07UUFDckMsQ0FBQztRQUVELElBQUksQ0FBQ2EsT0FBTyxDQUFDd0QsUUFBUSxFQUFFO1VBQ3JCQyxRQUFRLENBQUNOLEtBQUssR0FBRztZQUFFakMsR0FBRyxFQUFFLENBQUNsQixPQUFPLENBQUNtRCxLQUFLO1VBQUUsQ0FBQztVQUV6QyxJQUFJLENBQUNuRCxPQUFPLENBQUM2RSxVQUFVLEVBQUU7WUFDdkJwQixRQUFRLENBQUNOLEtBQUssQ0FBQ2pDLEdBQUcsQ0FBQzRELElBQUksQ0FBQyxJQUFJLENBQUM7VUFDL0I7UUFDRjtRQUVBLE9BQU9qRyxNQUFNLENBQUNPLGNBQWMsQ0FBQzZCLElBQUksQ0FBQ3dDLFFBQVEsRUFBRWEsTUFBTSxDQUFDO01BQ3JELENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0VvQixxQkFBcUIsRUFBRSxlQUFBQSxDQUFBLEVBQXlCO1FBQzlDLElBQUksQ0FBQ3BHLGtDQUFrQyxFQUFFO1VBQ3ZDQSxrQ0FBa0MsR0FBRyxJQUFJO1VBQ3pDcUcsT0FBTyxJQUNMQSxPQUFPLENBQUNDLElBQUksQ0FDVixxRUFDRixDQUFDO1FBQ0w7UUFFQSxPQUFPLE1BQU12RyxLQUFLLENBQUN3RyxnQkFBZ0IsQ0FBQyxHQUFBQyxTQUFPLENBQUM7TUFDOUMsQ0FBQztNQUVEO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0VDLHFCQUFxQixFQUFFLGVBQUFBLENBQWdCMUMsSUFBSSxFQUFFbEUsS0FBSyxFQUFFO1FBQ2xELElBQUk2RCxFQUFFO1FBRU4sSUFBSTdELEtBQUssSUFBSSxDQUFDZ0QsS0FBSyxDQUFDQyxPQUFPLENBQUNqRCxLQUFLLENBQUMsRUFBRUEsS0FBSyxHQUFHLENBQUNBLEtBQUssQ0FBQztRQUVuRCxJQUFJa0UsSUFBSSxJQUFJLE9BQU9BLElBQUksS0FBSyxRQUFRLEVBQUU7VUFDcENMLEVBQUUsR0FBR0ssSUFBSSxDQUFDL0MsR0FBRztRQUNmLENBQUMsTUFBTTtVQUNMMEMsRUFBRSxHQUFHSyxJQUFJO1FBQ1g7UUFFQSxJQUFJLENBQUNMLEVBQUUsRUFBRSxPQUFPLEVBQUU7UUFFbEIsTUFBTVMsUUFBUSxHQUFHO1VBQ2YsVUFBVSxFQUFFVCxFQUFFO1VBQ2RHLEtBQUssRUFBRTtZQUFFWCxHQUFHLEVBQUU7VUFBSztRQUNyQixDQUFDO1FBRUQsSUFBSXJELEtBQUssRUFBRTtVQUNUc0UsUUFBUSxDQUFDLG9CQUFvQixDQUFDLEdBQUc7WUFBRXZDLEdBQUcsRUFBRS9CO1VBQU0sQ0FBQztRQUNqRDtRQUVBLE1BQU02RyxNQUFNLEdBQUcsQ0FDYixNQUFNbkgsTUFBTSxDQUFDTyxjQUFjLENBQ3hCNkIsSUFBSSxDQUFDd0MsUUFBUSxFQUFFO1VBQUVaLE1BQU0sRUFBRTtZQUFFTSxLQUFLLEVBQUU7VUFBRTtRQUFFLENBQUMsQ0FBQyxDQUN4Q2hDLFVBQVUsQ0FBQyxDQUFDLEVBQ2ZJLEdBQUcsQ0FBRTBFLEdBQUcsSUFBS0EsR0FBRyxDQUFDOUMsS0FBSyxDQUFDO1FBRXpCLE9BQU8sQ0FBQyxHQUFHLElBQUlXLEdBQUcsQ0FBQ2tDLE1BQU0sQ0FBQyxDQUFDO01BQzdCLENBQUM7TUFFRDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0VFLGdCQUFnQixFQUFFLGVBQUFBLENBQWdCdEUsT0FBTyxFQUFFQyxPQUFPLEVBQUU7UUFDbEQsSUFBSUMsS0FBSztRQUVUekMsS0FBSyxDQUFDNkQsZUFBZSxDQUFDdEIsT0FBTyxDQUFDO1FBQzlCdkMsS0FBSyxDQUFDNkQsZUFBZSxDQUFDckIsT0FBTyxDQUFDO1FBRTlCLElBQUlELE9BQU8sS0FBS0MsT0FBTyxFQUFFO1FBRXpCLEdBQUc7VUFDREMsS0FBSyxHQUFHLE1BQU1qRCxNQUFNLENBQUNPLGNBQWMsQ0FBQ21CLFdBQVcsQ0FDN0M7WUFDRTRDLEtBQUssRUFBRXZCO1VBQ1QsQ0FBQyxFQUNEO1lBQ0VOLElBQUksRUFBRTtjQUNKNkIsS0FBSyxFQUFFdEI7WUFDVDtVQUNGLENBQUMsRUFDRDtZQUFFSixLQUFLLEVBQUU7VUFBSyxDQUNoQixDQUFDO1FBQ0gsQ0FBQyxRQUFRSyxLQUFLLEdBQUcsQ0FBQztNQUNwQixDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRXFFLGdCQUFnQixFQUFFLGVBQUFBLENBQWdCQyxJQUFJLEVBQUU7UUFDdEMvRyxLQUFLLENBQUM2RCxlQUFlLENBQUNrRCxJQUFJLENBQUM7UUFFM0IsTUFBTXZILE1BQU0sQ0FBQ08sY0FBYyxDQUFDMEIsV0FBVyxDQUFDO1VBQUVxQyxLQUFLLEVBQUVpRDtRQUFLLENBQUMsQ0FBQztNQUMxRCxDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFbkcsY0FBYyxFQUFFLFNBQUFBLENBQVVGLFFBQVEsRUFBRTtRQUNsQyxJQUNFLENBQUNBLFFBQVEsSUFDVCxPQUFPQSxRQUFRLEtBQUssUUFBUSxJQUM1QkEsUUFBUSxDQUFDc0csSUFBSSxDQUFDLENBQUMsS0FBS3RHLFFBQVEsRUFDNUI7VUFDQSxNQUFNLElBQUlZLEtBQUssdUJBQUEyQixNQUFBLENBQXVCdkMsUUFBUSxPQUFJLENBQUM7UUFDckQ7TUFDRixDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFdUcsZUFBZSxFQUFFLGVBQUFBLENBQWdCQyxjQUFjLEVBQUVDLGFBQWEsRUFBRTtRQUM5RCxJQUFJRCxjQUFjLEtBQUtDLGFBQWEsRUFBRTtVQUNwQyxPQUFPLElBQUk7UUFDYjtRQUVBLElBQUlELGNBQWMsSUFBSSxJQUFJLElBQUlDLGFBQWEsSUFBSSxJQUFJLEVBQUU7VUFDbkQsT0FBTyxLQUFLO1FBQ2Q7UUFFQW5ILEtBQUssQ0FBQ1ksY0FBYyxDQUFDc0csY0FBYyxDQUFDO1FBQ3BDbEgsS0FBSyxDQUFDWSxjQUFjLENBQUN1RyxhQUFhLENBQUM7UUFFbkMsSUFBSUMsWUFBWSxHQUFHLENBQUNGLGNBQWMsQ0FBQztRQUNuQyxPQUFPRSxZQUFZLENBQUMvRSxNQUFNLEtBQUssQ0FBQyxFQUFFO1VBQ2hDLE1BQU0zQixRQUFRLEdBQUcwRyxZQUFZLENBQUNDLEdBQUcsQ0FBQyxDQUFDO1VBRW5DLElBQUkzRyxRQUFRLEtBQUt5RyxhQUFhLEVBQUU7WUFDOUIsT0FBTyxJQUFJO1VBQ2I7VUFFQSxNQUFNekUsSUFBSSxHQUFHLE1BQU1sRCxNQUFNLENBQUNNLEtBQUssQ0FBQ2tCLFlBQVksQ0FBQztZQUFFQyxHQUFHLEVBQUVQO1VBQVMsQ0FBQyxDQUFDOztVQUUvRDtVQUNBLElBQUksQ0FBQ2dDLElBQUksRUFBRTtVQUVYMEUsWUFBWSxHQUFHQSxZQUFZLENBQUNuRSxNQUFNLENBQUNQLElBQUksQ0FBQ3RCLFFBQVEsQ0FBQ2MsR0FBRyxDQUFFUCxDQUFDLElBQUtBLENBQUMsQ0FBQ1YsR0FBRyxDQUFDLENBQUM7UUFDckU7UUFFQSxPQUFPLEtBQUs7TUFDZCxDQUFDO01BRUQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO01BQ0UyQyxpQkFBaUIsRUFBRSxTQUFBQSxDQUFVakQsT0FBTyxFQUFFO1FBQ3BDQSxPQUFPLEdBQUdBLE9BQU8sS0FBSzJHLFNBQVMsR0FBRyxDQUFDLENBQUMsR0FBRzNHLE9BQU87O1FBRTlDO1FBQ0E7UUFDQSxJQUFJQSxPQUFPLEtBQUssSUFBSSxJQUFJLE9BQU9BLE9BQU8sS0FBSyxRQUFRLElBQUksT0FBT0EsT0FBTyxLQUFLLFFBQVEsRUFBRTtVQUNsRkEsT0FBTyxHQUFHO1lBQUVtRCxLQUFLLEVBQUVuRDtVQUFRLENBQUM7UUFDOUI7UUFFQUEsT0FBTyxDQUFDbUQsS0FBSyxHQUFHOUQsS0FBSyxDQUFDdUgsbUJBQW1CLENBQUM1RyxPQUFPLENBQUNtRCxLQUFLLENBQUM7UUFFeEQsT0FBT25ELE9BQU87TUFDaEIsQ0FBQztNQUVEO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtNQUNFNEcsbUJBQW1CLEVBQUUsU0FBQUEsQ0FBVUMsU0FBUyxFQUFFO1FBQ3hDO1FBQ0EsSUFBSUEsU0FBUyxJQUFJLElBQUksRUFBRTtVQUNyQixPQUFPLElBQUk7UUFDYixDQUFDLE1BQU07VUFDTCxPQUFPQSxTQUFTO1FBQ2xCO01BQ0YsQ0FBQztNQUVEO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7TUFDRTNELGVBQWUsRUFBRSxTQUFBQSxDQUFVMkQsU0FBUyxFQUFFO1FBQ3BDLElBQUlBLFNBQVMsS0FBSyxJQUFJLEVBQUU7UUFFeEIsSUFDRSxDQUFDQSxTQUFTLElBQ1YsT0FBT0EsU0FBUyxLQUFLLFFBQVEsSUFDN0JBLFNBQVMsQ0FBQ1IsSUFBSSxDQUFDLENBQUMsS0FBS1EsU0FBUyxFQUM5QjtVQUNBLE1BQU0sSUFBSWxHLEtBQUssd0JBQUEyQixNQUFBLENBQXdCdUUsU0FBUyxPQUFJLENBQUM7UUFDdkQ7TUFDRjtJQUNGLENBQUMsQ0FBQztJQUFBQyxzQkFBQTtFQUFBLFNBQUFDLFdBQUE7SUFBQSxPQUFBRCxzQkFBQSxDQUFBQyxXQUFBO0VBQUE7RUFBQUQsc0JBQUE7QUFBQTtFQUFBRSxJQUFBO0VBQUFDLEtBQUE7QUFBQSxHOzs7Ozs7Ozs7Ozs7OztJQy95Q0YsSUFBSXRJLGVBQWUsRUFBQ0Msd0JBQXdCO0lBQUNzSSxNQUFNLENBQUNwSSxJQUFJLENBQUMsc0JBQXNCLEVBQUM7TUFBQ0gsZUFBZUEsQ0FBQ0ksQ0FBQyxFQUFDO1FBQUNKLGVBQWUsR0FBQ0ksQ0FBQztNQUFBLENBQUM7TUFBQ0gsd0JBQXdCQSxDQUFDRyxDQUFDLEVBQUM7UUFBQ0gsd0JBQXdCLEdBQUNHLENBQUM7TUFBQTtJQUFDLENBQUMsRUFBQyxDQUFDLENBQUM7SUFBQyxJQUFJRSxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxNQUFNQSxvQkFBb0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQztJQUUvTyxNQUFNa0ksaUJBQWlCLEdBQUd2SSx3QkFBd0IsQ0FBQ3dJLGdCQUFnQixDQUFDQyxJQUFJLENBQUN6SSx3QkFBd0IsQ0FBQztJQUNsRyxNQUFNMEksWUFBWSxHQUFHM0ksZUFBZSxDQUFDeUksZ0JBQWdCLENBQUNDLElBQUksQ0FBQzFJLGVBQWUsQ0FBQztJQUUzRSxNQUFNNEksT0FBTyxHQUFHLENBQ2Q7TUFBRSxVQUFVLEVBQUUsQ0FBQztNQUFFLG9CQUFvQixFQUFFLENBQUM7TUFBRXBFLEtBQUssRUFBRTtJQUFFLENBQUMsRUFDcEQ7TUFBRSxVQUFVLEVBQUUsQ0FBQztNQUFFLFVBQVUsRUFBRSxDQUFDO01BQUVBLEtBQUssRUFBRTtJQUFFLENBQUMsRUFDMUM7TUFBRSxVQUFVLEVBQUU7SUFBRSxDQUFDLEVBQ2pCO01BQUVBLEtBQUssRUFBRSxDQUFDO01BQUUsVUFBVSxFQUFFLENBQUM7TUFBRSxvQkFBb0IsRUFBRTtJQUFFLENBQUM7SUFBRTtJQUN0RDtNQUFFLG9CQUFvQixFQUFFO0lBQUUsQ0FBQyxDQUM1QjtJQUNELEtBQUssTUFBTXFFLEtBQUssSUFBSUQsT0FBTyxFQUFFO01BQzNCSixpQkFBaUIsQ0FBQ0ssS0FBSyxDQUFDO0lBQzFCO0lBQ0FGLFlBQVksQ0FBQztNQUFFLGNBQWMsRUFBRTtJQUFFLENBQUMsQ0FBQztJQUFBUixzQkFBQTtFQUFBLFNBQUFDLFdBQUE7SUFBQSxPQUFBRCxzQkFBQSxDQUFBQyxXQUFBO0VBQUE7RUFBQUQsc0JBQUE7QUFBQTtFQUFBRSxJQUFBO0VBQUFDLEtBQUE7QUFBQSxHIiwiZmlsZSI6Ii9wYWNrYWdlcy9hbGFubmluZ19yb2xlcy5qcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIGdsb2JhbCBSb2xlcyAqL1xuaW1wb3J0IHsgTWV0ZW9yIH0gZnJvbSAnbWV0ZW9yL21ldGVvcidcbmltcG9ydCB7IE1vbmdvIH0gZnJvbSAnbWV0ZW9yL21vbmdvJ1xuXG4vKipcbiAqIFByb3ZpZGVzIGZ1bmN0aW9ucyByZWxhdGVkIHRvIHVzZXIgYXV0aG9yaXphdGlvbi4gQ29tcGF0aWJsZSB3aXRoIGJ1aWx0LWluIE1ldGVvciBhY2NvdW50cyBwYWNrYWdlcy5cbiAqXG4gKiBSb2xlcyBhcmUgYWNjZXNzaWJsZSB0aHJvZ2ggYE1ldGVvci5yb2xlc2AgY29sbGVjdGlvbiBhbmQgZG9jdW1lbnRzIGNvbnNpc3Qgb2Y6XG4gKiAgLSBgX2lkYDogcm9sZSBuYW1lXG4gKiAgLSBgY2hpbGRyZW5gOiBsaXN0IG9mIHN1YmRvY3VtZW50czpcbiAqICAgIC0gYF9pZGBcbiAqXG4gKiBDaGlsZHJlbiBsaXN0IGVsZW1lbnRzIGFyZSBzdWJkb2N1bWVudHMgc28gdGhhdCB0aGV5IGNhbiBiZSBlYXNpZXIgZXh0ZW5kZWQgaW4gdGhlIGZ1dHVyZSBvciBieSBwbHVnaW5zLlxuICpcbiAqIFJvbGVzIGNhbiBoYXZlIG11bHRpcGxlIHBhcmVudHMgYW5kIGNhbiBiZSBjaGlsZHJlbiAoc3Vicm9sZXMpIG9mIG11bHRpcGxlIHJvbGVzLlxuICpcbiAqIEV4YW1wbGU6IGB7X2lkOiAnYWRtaW4nLCBjaGlsZHJlbjogW3tfaWQ6ICdlZGl0b3InfV19YFxuICpcbiAqIFRoZSBhc3NpZ25tZW50IG9mIGEgcm9sZSB0byBhIHVzZXIgaXMgc3RvcmVkIGluIGEgY29sbGVjdGlvbiwgYWNjZXNzaWJsZSB0aHJvdWdoIGBNZXRlb3Iucm9sZUFzc2lnbm1lbnRgLlxuICogSXQncyBkb2N1bWVudHMgY29uc2lzdCBvZlxuICogIC0gYF9pZGA6IEludGVybmFsIE1vbmdvREIgaWRcbiAqICAtIGByb2xlYDogQSByb2xlIG9iamVjdCB3aGljaCBnb3QgYXNzaWduZWQuIFVzdWFsbHkgb25seSBjb250YWlucyB0aGUgYF9pZGAgcHJvcGVydHlcbiAqICAtIGB1c2VyYDogQSB1c2VyIG9iamVjdCwgdXN1YWxseSBvbmx5IGNvbnRhaW5zIHRoZSBgX2lkYCBwcm9wZXJ0eVxuICogIC0gYHNjb3BlYDogc2NvcGUgbmFtZVxuICogIC0gYGluaGVyaXRlZFJvbGVzYDogQSBsaXN0IG9mIGFsbCB0aGUgcm9sZXMgb2JqZWN0cyBpbmhlcml0ZWQgYnkgdGhlIGFzc2lnbmVkIHJvbGUuXG4gKlxuICogQG1vZHVsZSBSb2xlc1xuICovXG5leHBvcnQgY29uc3QgUm9sZXNDb2xsZWN0aW9uID0gbmV3IE1vbmdvLkNvbGxlY3Rpb24oJ3JvbGVzJylcblxuaWYgKCFNZXRlb3Iucm9sZXMpIHtcbiAgTWV0ZW9yLnJvbGVzID0gUm9sZXNDb2xsZWN0aW9uXG59XG5cbmV4cG9ydCBjb25zdCBSb2xlQXNzaWdubWVudENvbGxlY3Rpb24gPSBuZXcgTW9uZ28uQ29sbGVjdGlvbigncm9sZS1hc3NpZ25tZW50JylcblxuaWYgKCFNZXRlb3Iucm9sZUFzc2lnbm1lbnQpIHtcbiAgTWV0ZW9yLnJvbGVBc3NpZ25tZW50ID0gUm9sZUFzc2lnbm1lbnRDb2xsZWN0aW9uXG59XG5cbi8qKlxuICogQGNsYXNzIFJvbGVzXG4gKi9cbmlmICh0eXBlb2YgUm9sZXMgPT09ICd1bmRlZmluZWQnKSB7XG4gIFJvbGVzID0ge30gLy8gZXNsaW50LWRpc2FibGUtbGluZSBuby1nbG9iYWwtYXNzaWduXG59XG5cbmxldCBnZXRHcm91cHNGb3JVc2VyRGVwcmVjYXRpb25XYXJuaW5nID0gZmFsc2VcblxuLyoqXG4gKiBIZWxwZXIsIHJlc29sdmVzIGFzeW5jIHNvbWVcbiAqIEBwYXJhbSB7Kn0gYXJyXG4gKiBAcGFyYW0geyp9IHByZWRpY2F0ZVxuICogQHJldHVybnMge1Byb21pc2U8Qm9vbGVhbj59XG4gKi9cbmNvbnN0IGFzeW5jU29tZSA9IGFzeW5jIChhcnIsIHByZWRpY2F0ZSkgPT4ge1xuICBmb3IgKGNvbnN0IGUgb2YgYXJyKSB7XG4gICAgaWYgKGF3YWl0IHByZWRpY2F0ZShlKSkgcmV0dXJuIHRydWVcbiAgfVxuICByZXR1cm4gZmFsc2Vcbn1cblxuT2JqZWN0LmFzc2lnbihSb2xlcywge1xuICAvKipcbiAgICogVXNlZCBhcyBhIGdsb2JhbCBncm91cCAobm93IHNjb3BlKSBuYW1lLiBOb3QgdXNlZCBhbnltb3JlLlxuICAgKlxuICAgKiBAcHJvcGVydHkgR0xPQkFMX0dST1VQXG4gICAqIEBzdGF0aWNcbiAgICogQGRlcHJlY2F0ZWRcbiAgICovXG4gIEdMT0JBTF9HUk9VUDogbnVsbCxcblxuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3IHJvbGUuXG4gICAqXG4gICAqIEBtZXRob2QgY3JlYXRlUm9sZUFzeW5jXG4gICAqIEBwYXJhbSB7U3RyaW5nfSByb2xlTmFtZSBOYW1lIG9mIHJvbGUuXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBbb3B0aW9uc10gT3B0aW9uczpcbiAgICogICAtIGB1bmxlc3NFeGlzdHNgOiBpZiBgdHJ1ZWAsIGV4Y2VwdGlvbiB3aWxsIG5vdCBiZSB0aHJvd24gaW4gdGhlIHJvbGUgYWxyZWFkeSBleGlzdHNcbiAgICogQHJldHVybiB7UHJvbWlzZTxTdHJpbmc+fSBJRCBvZiB0aGUgbmV3IHJvbGUgb3IgbnVsbC5cbiAgICogQHN0YXRpY1xuICAgKi9cbiAgY3JlYXRlUm9sZUFzeW5jOiBhc3luYyBmdW5jdGlvbiAocm9sZU5hbWUsIG9wdGlvbnMpIHtcbiAgICBSb2xlcy5fY2hlY2tSb2xlTmFtZShyb2xlTmFtZSlcblxuICAgIG9wdGlvbnMgPSBPYmplY3QuYXNzaWduKFxuICAgICAge1xuICAgICAgICB1bmxlc3NFeGlzdHM6IGZhbHNlXG4gICAgICB9LFxuICAgICAgb3B0aW9uc1xuICAgIClcblxuICAgIGxldCBpbnNlcnRlZElkID0gbnVsbFxuXG4gICAgY29uc3QgZXhpc3RpbmdSb2xlID0gYXdhaXQgTWV0ZW9yLnJvbGVzLmZpbmRPbmVBc3luYyh7IF9pZDogcm9sZU5hbWUgfSlcblxuICAgIGlmIChleGlzdGluZ1JvbGUpIHtcbiAgICAgIGF3YWl0IE1ldGVvci5yb2xlcy51cGRhdGVBc3luYyhcbiAgICAgICAgeyBfaWQ6IHJvbGVOYW1lIH0sXG4gICAgICAgIHsgJHNldE9uSW5zZXJ0OiB7IGNoaWxkcmVuOiBbXSB9IH1cbiAgICAgIClcbiAgICAgIHJldHVybiBudWxsXG4gICAgfSBlbHNlIHtcbiAgICAgIGluc2VydGVkSWQgPSBhd2FpdCBNZXRlb3Iucm9sZXMuaW5zZXJ0QXN5bmMoe1xuICAgICAgICBfaWQ6IHJvbGVOYW1lLFxuICAgICAgICBjaGlsZHJlbjogW11cbiAgICAgIH0pXG4gICAgfVxuXG4gICAgaWYgKCFpbnNlcnRlZElkKSB7XG4gICAgICBpZiAob3B0aW9ucy51bmxlc3NFeGlzdHMpIHJldHVybiBudWxsXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSb2xlICdcIiArIHJvbGVOYW1lICsgXCInIGFscmVhZHkgZXhpc3RzLlwiKVxuICAgIH1cblxuICAgIHJldHVybiBpbnNlcnRlZElkXG4gIH0sXG5cbiAgLyoqXG4gICAqIERlbGV0ZSBhbiBleGlzdGluZyByb2xlLlxuICAgKlxuICAgKiBJZiB0aGUgcm9sZSBpcyBzZXQgZm9yIGFueSB1c2VyLCBpdCBpcyBhdXRvbWF0aWNhbGx5IHVuc2V0LlxuICAgKlxuICAgKiBAbWV0aG9kIGRlbGV0ZVJvbGVBc3luY1xuICAgKiBAcGFyYW0ge1N0cmluZ30gcm9sZU5hbWUgTmFtZSBvZiByb2xlLlxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICogQHN0YXRpY1xuICAgKi9cbiAgZGVsZXRlUm9sZUFzeW5jOiBhc3luYyBmdW5jdGlvbiAocm9sZU5hbWUpIHtcbiAgICBsZXQgcm9sZXNcbiAgICBsZXQgaW5oZXJpdGVkUm9sZXNcblxuICAgIFJvbGVzLl9jaGVja1JvbGVOYW1lKHJvbGVOYW1lKVxuXG4gICAgLy8gUmVtb3ZlIGFsbCBhc3NpZ25tZW50c1xuICAgIGF3YWl0IE1ldGVvci5yb2xlQXNzaWdubWVudC5yZW1vdmVBc3luYyh7XG4gICAgICAncm9sZS5faWQnOiByb2xlTmFtZVxuICAgIH0pXG5cbiAgICBkbyB7XG4gICAgICAvLyBGb3IgYWxsIHJvbGVzIHdobyBoYXZlIGl0IGFzIGEgZGVwZW5kZW5jeSAuLi5cbiAgICAgIHJvbGVzID0gYXdhaXQgUm9sZXMuX2dldFBhcmVudFJvbGVOYW1lc0FzeW5jKFxuICAgICAgICBhd2FpdCBNZXRlb3Iucm9sZXMuZmluZE9uZUFzeW5jKHsgX2lkOiByb2xlTmFtZSB9KVxuICAgICAgKVxuXG4gICAgICBmb3IgKGNvbnN0IHIgb2YgYXdhaXQgTWV0ZW9yLnJvbGVzXG4gICAgICAgIC5maW5kKHsgX2lkOiB7ICRpbjogcm9sZXMgfSB9KVxuICAgICAgICAuZmV0Y2hBc3luYygpKSB7XG4gICAgICAgIGF3YWl0IE1ldGVvci5yb2xlcy51cGRhdGVBc3luYyhcbiAgICAgICAgICB7XG4gICAgICAgICAgICBfaWQ6IHIuX2lkXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICAkcHVsbDoge1xuICAgICAgICAgICAgICBjaGlsZHJlbjoge1xuICAgICAgICAgICAgICAgIF9pZDogcm9sZU5hbWVcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgKVxuXG4gICAgICAgIGluaGVyaXRlZFJvbGVzID0gYXdhaXQgUm9sZXMuX2dldEluaGVyaXRlZFJvbGVOYW1lc0FzeW5jKFxuICAgICAgICAgIGF3YWl0IE1ldGVvci5yb2xlcy5maW5kT25lQXN5bmMoeyBfaWQ6IHIuX2lkIH0pXG4gICAgICAgIClcbiAgICAgICAgYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50LnVwZGF0ZUFzeW5jKFxuICAgICAgICAgIHtcbiAgICAgICAgICAgICdyb2xlLl9pZCc6IHIuX2lkXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICAkc2V0OiB7XG4gICAgICAgICAgICAgIGluaGVyaXRlZFJvbGVzOiBbci5faWQsIC4uLmluaGVyaXRlZFJvbGVzXS5tYXAoKHIyKSA9PiAoe1xuICAgICAgICAgICAgICAgIF9pZDogcjJcbiAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICB7IG11bHRpOiB0cnVlIH1cbiAgICAgICAgKVxuICAgICAgfVxuICAgIH0gd2hpbGUgKHJvbGVzLmxlbmd0aCA+IDApXG5cbiAgICAvLyBBbmQgZmluYWxseSByZW1vdmUgdGhlIHJvbGUgaXRzZWxmXG4gICAgYXdhaXQgTWV0ZW9yLnJvbGVzLnJlbW92ZUFzeW5jKHsgX2lkOiByb2xlTmFtZSB9KVxuICB9LFxuXG4gIC8qKlxuICAgKiBSZW5hbWUgYW4gZXhpc3Rpbmcgcm9sZS5cbiAgICpcbiAgICogQG1ldGhvZCByZW5hbWVSb2xlQXN5bmNcbiAgICogQHBhcmFtIHtTdHJpbmd9IG9sZE5hbWUgT2xkIG5hbWUgb2YgYSByb2xlLlxuICAgKiBAcGFyYW0ge1N0cmluZ30gbmV3TmFtZSBOZXcgbmFtZSBvZiBhIHJvbGUuXG4gICAqIEByZXR1cm5zIHtQcm9taXNlfVxuICAgKiBAc3RhdGljXG4gICAqL1xuICByZW5hbWVSb2xlQXN5bmM6IGFzeW5jIGZ1bmN0aW9uIChvbGROYW1lLCBuZXdOYW1lKSB7XG4gICAgbGV0IGNvdW50XG5cbiAgICBSb2xlcy5fY2hlY2tSb2xlTmFtZShvbGROYW1lKVxuICAgIFJvbGVzLl9jaGVja1JvbGVOYW1lKG5ld05hbWUpXG5cbiAgICBpZiAob2xkTmFtZSA9PT0gbmV3TmFtZSkgcmV0dXJuXG5cbiAgICBjb25zdCByb2xlID0gYXdhaXQgTWV0ZW9yLnJvbGVzLmZpbmRPbmVBc3luYyh7IF9pZDogb2xkTmFtZSB9KVxuXG4gICAgaWYgKCFyb2xlKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSb2xlICdcIiArIG9sZE5hbWUgKyBcIicgZG9lcyBub3QgZXhpc3QuXCIpXG4gICAgfVxuXG4gICAgcm9sZS5faWQgPSBuZXdOYW1lXG5cbiAgICBhd2FpdCBNZXRlb3Iucm9sZXMuaW5zZXJ0QXN5bmMocm9sZSlcblxuICAgIGRvIHtcbiAgICAgIGNvdW50ID0gYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50LnVwZGF0ZUFzeW5jKFxuICAgICAgICB7XG4gICAgICAgICAgJ3JvbGUuX2lkJzogb2xkTmFtZVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgJ3JvbGUuX2lkJzogbmV3TmFtZVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgeyBtdWx0aTogdHJ1ZSB9XG4gICAgICApXG4gICAgfSB3aGlsZSAoY291bnQgPiAwKVxuXG4gICAgZG8ge1xuICAgICAgY291bnQgPSBhd2FpdCBNZXRlb3Iucm9sZUFzc2lnbm1lbnQudXBkYXRlQXN5bmMoXG4gICAgICAgIHtcbiAgICAgICAgICAnaW5oZXJpdGVkUm9sZXMuX2lkJzogb2xkTmFtZVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgJ2luaGVyaXRlZFJvbGVzLiQuX2lkJzogbmV3TmFtZVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgeyBtdWx0aTogdHJ1ZSB9XG4gICAgICApXG4gICAgfSB3aGlsZSAoY291bnQgPiAwKVxuXG4gICAgZG8ge1xuICAgICAgY291bnQgPSBhd2FpdCBNZXRlb3Iucm9sZXMudXBkYXRlQXN5bmMoXG4gICAgICAgIHtcbiAgICAgICAgICAnY2hpbGRyZW4uX2lkJzogb2xkTmFtZVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgJHNldDoge1xuICAgICAgICAgICAgJ2NoaWxkcmVuLiQuX2lkJzogbmV3TmFtZVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgeyBtdWx0aTogdHJ1ZSB9XG4gICAgICApXG4gICAgfSB3aGlsZSAoY291bnQgPiAwKVxuXG4gICAgYXdhaXQgTWV0ZW9yLnJvbGVzLnJlbW92ZUFzeW5jKHsgX2lkOiBvbGROYW1lIH0pXG4gIH0sXG5cbiAgLyoqXG4gICAqIEFkZCByb2xlIHBhcmVudCB0byByb2xlcy5cbiAgICpcbiAgICogUHJldmlvdXMgcGFyZW50cyBhcmUga2VwdCAocm9sZSBjYW4gaGF2ZSBtdWx0aXBsZSBwYXJlbnRzKS4gRm9yIHVzZXJzIHdoaWNoIGhhdmUgdGhlXG4gICAqIHBhcmVudCByb2xlIHNldCwgbmV3IHN1YnJvbGVzIGFyZSBhZGRlZCBhdXRvbWF0aWNhbGx5LlxuICAgKlxuICAgKiBAbWV0aG9kIGFkZFJvbGVzVG9QYXJlbnRBc3luY1xuICAgKiBAcGFyYW0ge0FycmF5fFN0cmluZ30gcm9sZXNOYW1lcyBOYW1lKHMpIG9mIHJvbGUocykuXG4gICAqIEBwYXJhbSB7U3RyaW5nfSBwYXJlbnROYW1lIE5hbWUgb2YgcGFyZW50IHJvbGUuXG4gICAqIEByZXR1cm5zIHtQcm9taXNlfVxuICAgKiBAc3RhdGljXG4gICAqL1xuICBhZGRSb2xlc1RvUGFyZW50QXN5bmM6IGFzeW5jIGZ1bmN0aW9uIChyb2xlc05hbWVzLCBwYXJlbnROYW1lKSB7XG4gICAgLy8gZW5zdXJlIGFycmF5c1xuICAgIGlmICghQXJyYXkuaXNBcnJheShyb2xlc05hbWVzKSkgcm9sZXNOYW1lcyA9IFtyb2xlc05hbWVzXVxuXG4gICAgZm9yIChjb25zdCByb2xlTmFtZSBvZiByb2xlc05hbWVzKSB7XG4gICAgICBhd2FpdCBSb2xlcy5fYWRkUm9sZVRvUGFyZW50QXN5bmMocm9sZU5hbWUsIHBhcmVudE5hbWUpXG4gICAgfVxuICB9LFxuXG4gIC8qKlxuICAgKiBAbWV0aG9kIF9hZGRSb2xlVG9QYXJlbnRBc3luY1xuICAgKiBAcGFyYW0ge1N0cmluZ30gcm9sZU5hbWUgTmFtZSBvZiByb2xlLlxuICAgKiBAcGFyYW0ge1N0cmluZ30gcGFyZW50TmFtZSBOYW1lIG9mIHBhcmVudCByb2xlLlxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICogQHByaXZhdGVcbiAgICogQHN0YXRpY1xuICAgKi9cbiAgX2FkZFJvbGVUb1BhcmVudEFzeW5jOiBhc3luYyBmdW5jdGlvbiAocm9sZU5hbWUsIHBhcmVudE5hbWUpIHtcbiAgICBSb2xlcy5fY2hlY2tSb2xlTmFtZShyb2xlTmFtZSlcbiAgICBSb2xlcy5fY2hlY2tSb2xlTmFtZShwYXJlbnROYW1lKVxuXG4gICAgLy8gcXVlcnkgdG8gZ2V0IHJvbGUncyBjaGlsZHJlblxuICAgIGNvbnN0IHJvbGUgPSBhd2FpdCBNZXRlb3Iucm9sZXMuZmluZE9uZUFzeW5jKHsgX2lkOiByb2xlTmFtZSB9KVxuXG4gICAgaWYgKCFyb2xlKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFJvbGUgJyR7cm9sZU5hbWV9JyBkb2VzIG5vdCBleGlzdC5gKVxuICAgIH1cblxuICAgIC8vIGRldGVjdCBjeWNsZXNcbiAgICBpZiAoKGF3YWl0IFJvbGVzLl9nZXRJbmhlcml0ZWRSb2xlTmFtZXNBc3luYyhyb2xlKSkuaW5jbHVkZXMocGFyZW50TmFtZSkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgYFJvbGVzICcke3JvbGVOYW1lfScgYW5kICcke3BhcmVudE5hbWV9JyB3b3VsZCBmb3JtIGEgY3ljbGUuYFxuICAgICAgKVxuICAgIH1cblxuICAgIGNvbnN0IGNvdW50ID0gYXdhaXQgTWV0ZW9yLnJvbGVzLnVwZGF0ZUFzeW5jKFxuICAgICAge1xuICAgICAgICBfaWQ6IHBhcmVudE5hbWUsXG4gICAgICAgICdjaGlsZHJlbi5faWQnOiB7XG4gICAgICAgICAgJG5lOiByb2xlLl9pZFxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICAkcHVzaDoge1xuICAgICAgICAgIGNoaWxkcmVuOiB7XG4gICAgICAgICAgICBfaWQ6IHJvbGUuX2lkXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgKVxuXG4gICAgLy8gaWYgdGhlcmUgd2FzIG5vIGNoYW5nZSwgcGFyZW50IHJvbGUgbWlnaHQgbm90IGV4aXN0LCBvciByb2xlIGlzXG4gICAgLy8gYWxyZWFkeSBhIHN1Yi1yb2xlOyBpbiBhbnkgY2FzZSB3ZSBkbyBub3QgaGF2ZSBhbnl0aGluZyBtb3JlIHRvIGRvXG4gICAgaWYgKCFjb3VudCkgcmV0dXJuXG5cbiAgICBhd2FpdCBNZXRlb3Iucm9sZUFzc2lnbm1lbnQudXBkYXRlQXN5bmMoXG4gICAgICB7XG4gICAgICAgICdpbmhlcml0ZWRSb2xlcy5faWQnOiBwYXJlbnROYW1lXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICAkcHVzaDoge1xuICAgICAgICAgIGluaGVyaXRlZFJvbGVzOiB7XG4gICAgICAgICAgICAkZWFjaDogW1xuICAgICAgICAgICAgICByb2xlLl9pZCxcbiAgICAgICAgICAgICAgLi4uKGF3YWl0IFJvbGVzLl9nZXRJbmhlcml0ZWRSb2xlTmFtZXNBc3luYyhyb2xlKSlcbiAgICAgICAgICAgIF0ubWFwKChyKSA9PiAoeyBfaWQ6IHIgfSkpXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgeyBtdWx0aTogdHJ1ZSB9XG4gICAgKVxuICB9LFxuXG4gIC8qKlxuICAgKiBSZW1vdmUgcm9sZSBwYXJlbnQgZnJvbSByb2xlcy5cbiAgICpcbiAgICogT3RoZXIgcGFyZW50cyBhcmUga2VwdCAocm9sZSBjYW4gaGF2ZSBtdWx0aXBsZSBwYXJlbnRzKS4gRm9yIHVzZXJzIHdoaWNoIGhhdmUgdGhlXG4gICAqIHBhcmVudCByb2xlIHNldCwgcmVtb3ZlZCBzdWJyb2xlIGlzIHJlbW92ZWQgYXV0b21hdGljYWxseS5cbiAgICpcbiAgICogQG1ldGhvZCByZW1vdmVSb2xlc0Zyb21QYXJlbnRBc3luY1xuICAgKiBAcGFyYW0ge0FycmF5fFN0cmluZ30gcm9sZXNOYW1lcyBOYW1lKHMpIG9mIHJvbGUocykuXG4gICAqIEBwYXJhbSB7U3RyaW5nfSBwYXJlbnROYW1lIE5hbWUgb2YgcGFyZW50IHJvbGUuXG4gICAqIEByZXR1cm5zIHtQcm9taXNlfVxuICAgKiBAc3RhdGljXG4gICAqL1xuICByZW1vdmVSb2xlc0Zyb21QYXJlbnRBc3luYzogYXN5bmMgZnVuY3Rpb24gKHJvbGVzTmFtZXMsIHBhcmVudE5hbWUpIHtcbiAgICAvLyBlbnN1cmUgYXJyYXlzXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHJvbGVzTmFtZXMpKSByb2xlc05hbWVzID0gW3JvbGVzTmFtZXNdXG5cbiAgICBmb3IgKGNvbnN0IHJvbGVOYW1lIG9mIHJvbGVzTmFtZXMpIHtcbiAgICAgIGF3YWl0IFJvbGVzLl9yZW1vdmVSb2xlRnJvbVBhcmVudEFzeW5jKHJvbGVOYW1lLCBwYXJlbnROYW1lKVxuICAgIH1cbiAgfSxcblxuICAvKipcbiAgICogQG1ldGhvZCBfcmVtb3ZlUm9sZUZyb21QYXJlbnRBc3luY1xuICAgKiBAcGFyYW0ge1N0cmluZ30gcm9sZU5hbWUgTmFtZSBvZiByb2xlLlxuICAgKiBAcGFyYW0ge1N0cmluZ30gcGFyZW50TmFtZSBOYW1lIG9mIHBhcmVudCByb2xlLlxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICogQHByaXZhdGVcbiAgICogQHN0YXRpY1xuICAgKi9cbiAgX3JlbW92ZVJvbGVGcm9tUGFyZW50QXN5bmM6IGFzeW5jIGZ1bmN0aW9uIChyb2xlTmFtZSwgcGFyZW50TmFtZSkge1xuICAgIFJvbGVzLl9jaGVja1JvbGVOYW1lKHJvbGVOYW1lKVxuICAgIFJvbGVzLl9jaGVja1JvbGVOYW1lKHBhcmVudE5hbWUpXG5cbiAgICAvLyBjaGVjayBmb3Igcm9sZSBleGlzdGVuY2VcbiAgICAvLyB0aGlzIHdvdWxkIG5vdCByZWFsbHkgYmUgbmVlZGVkLCBidXQgd2UgYXJlIHRyeWluZyB0byBtYXRjaCBhZGRSb2xlc1RvUGFyZW50XG4gICAgY29uc3Qgcm9sZSA9IGF3YWl0IE1ldGVvci5yb2xlcy5maW5kT25lQXN5bmMoXG4gICAgICB7IF9pZDogcm9sZU5hbWUgfSxcbiAgICAgIHsgZmllbGRzOiB7IF9pZDogMSB9IH1cbiAgICApXG5cbiAgICBpZiAoIXJvbGUpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgUm9sZSAnJHtyb2xlTmFtZX0nIGRvZXMgbm90IGV4aXN0LmApXG4gICAgfVxuXG4gICAgY29uc3QgY291bnQgPSBhd2FpdCBNZXRlb3Iucm9sZXMudXBkYXRlQXN5bmMoXG4gICAgICB7XG4gICAgICAgIF9pZDogcGFyZW50TmFtZVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgJHB1bGw6IHtcbiAgICAgICAgICBjaGlsZHJlbjoge1xuICAgICAgICAgICAgX2lkOiByb2xlLl9pZFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIClcblxuICAgIC8vIGlmIHRoZXJlIHdhcyBubyBjaGFuZ2UsIHBhcmVudCByb2xlIG1pZ2h0IG5vdCBleGlzdCwgb3Igcm9sZSB3YXNcbiAgICAvLyBhbHJlYWR5IG5vdCBhIHN1YnJvbGU7IGluIGFueSBjYXNlIHdlIGRvIG5vdCBoYXZlIGFueXRoaW5nIG1vcmUgdG8gZG9cbiAgICBpZiAoIWNvdW50KSByZXR1cm5cblxuICAgIC8vIEZvciBhbGwgcm9sZXMgd2hvIGhhdmUgaGFkIGl0IGFzIGEgZGVwZW5kZW5jeSAuLi5cbiAgICBjb25zdCByb2xlcyA9IFtcbiAgICAgIC4uLihhd2FpdCBSb2xlcy5fZ2V0UGFyZW50Um9sZU5hbWVzQXN5bmMoXG4gICAgICAgIGF3YWl0IE1ldGVvci5yb2xlcy5maW5kT25lQXN5bmMoeyBfaWQ6IHBhcmVudE5hbWUgfSlcbiAgICAgICkpLFxuICAgICAgcGFyZW50TmFtZVxuICAgIF1cblxuICAgIGZvciAoY29uc3QgciBvZiBhd2FpdCBNZXRlb3Iucm9sZXNcbiAgICAgIC5maW5kKHsgX2lkOiB7ICRpbjogcm9sZXMgfSB9KVxuICAgICAgLmZldGNoQXN5bmMoKSkge1xuICAgICAgY29uc3QgaW5oZXJpdGVkUm9sZXMgPSBhd2FpdCBSb2xlcy5fZ2V0SW5oZXJpdGVkUm9sZU5hbWVzQXN5bmMoXG4gICAgICAgIGF3YWl0IE1ldGVvci5yb2xlcy5maW5kT25lQXN5bmMoeyBfaWQ6IHIuX2lkIH0pXG4gICAgICApXG4gICAgICBhd2FpdCBNZXRlb3Iucm9sZUFzc2lnbm1lbnQudXBkYXRlQXN5bmMoXG4gICAgICAgIHtcbiAgICAgICAgICAncm9sZS5faWQnOiByLl9pZCxcbiAgICAgICAgICAnaW5oZXJpdGVkUm9sZXMuX2lkJzogcm9sZS5faWRcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgICRzZXQ6IHtcbiAgICAgICAgICAgIGluaGVyaXRlZFJvbGVzOiBbci5faWQsIC4uLmluaGVyaXRlZFJvbGVzXS5tYXAoKHIyKSA9PiAoe1xuICAgICAgICAgICAgICBfaWQ6IHIyXG4gICAgICAgICAgICB9KSlcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHsgbXVsdGk6IHRydWUgfVxuICAgICAgKVxuICAgIH1cbiAgfSxcblxuICAvKipcbiAgICogQWRkIHVzZXJzIHRvIHJvbGVzLlxuICAgKlxuICAgKiBBZGRzIHJvbGVzIHRvIGV4aXN0aW5nIHJvbGVzIGZvciBlYWNoIHVzZXIuXG4gICAqXG4gICAqIEBleGFtcGxlXG4gICAqICAgICBSb2xlcy5hZGRVc2Vyc1RvUm9sZXNBc3luYyh1c2VySWQsICdhZG1pbicpXG4gICAqICAgICBSb2xlcy5hZGRVc2Vyc1RvUm9sZXNBc3luYyh1c2VySWQsIFsndmlldy1zZWNyZXRzJ10sICdleGFtcGxlLmNvbScpXG4gICAqICAgICBSb2xlcy5hZGRVc2Vyc1RvUm9sZXNBc3luYyhbdXNlcjEsIHVzZXIyXSwgWyd1c2VyJywnZWRpdG9yJ10pXG4gICAqICAgICBSb2xlcy5hZGRVc2Vyc1RvUm9sZXNBc3luYyhbdXNlcjEsIHVzZXIyXSwgWydnbG9yaW91cy1hZG1pbicsICdwZXJmb3JtLWFjdGlvbiddLCAnZXhhbXBsZS5vcmcnKVxuICAgKlxuICAgKiBAbWV0aG9kIGFkZFVzZXJzVG9Sb2xlc0FzeW5jXG4gICAqIEBwYXJhbSB7QXJyYXl8U3RyaW5nfSB1c2VycyBVc2VyIElEKHMpIG9yIG9iamVjdChzKSB3aXRoIGFuIGBfaWRgIGZpZWxkLlxuICAgKiBAcGFyYW0ge0FycmF5fFN0cmluZ30gcm9sZXMgTmFtZShzKSBvZiByb2xlcyB0byBhZGQgdXNlcnMgdG8uIFJvbGVzIGhhdmUgdG8gZXhpc3QuXG4gICAqIEBwYXJhbSB7T2JqZWN0fFN0cmluZ30gW29wdGlvbnNdIE9wdGlvbnM6XG4gICAqICAgLSBgc2NvcGVgOiBuYW1lIG9mIHRoZSBzY29wZSwgb3IgYG51bGxgIGZvciB0aGUgZ2xvYmFsIHJvbGVcbiAgICogICAtIGBpZkV4aXN0c2A6IGlmIGB0cnVlYCwgZG8gbm90IHRocm93IGFuIGV4Y2VwdGlvbiBpZiB0aGUgcm9sZSBkb2VzIG5vdCBleGlzdFxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICpcbiAgICogQWx0ZXJuYXRpdmVseSwgaXQgY2FuIGJlIGEgc2NvcGUgbmFtZSBzdHJpbmcuXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIGFkZFVzZXJzVG9Sb2xlc0FzeW5jOiBhc3luYyBmdW5jdGlvbiAodXNlcnMsIHJvbGVzLCBvcHRpb25zKSB7XG4gICAgbGV0IGlkXG5cbiAgICBpZiAoIXVzZXJzKSB0aHJvdyBuZXcgRXJyb3IoXCJNaXNzaW5nICd1c2VycycgcGFyYW0uXCIpXG4gICAgaWYgKCFyb2xlcykgdGhyb3cgbmV3IEVycm9yKFwiTWlzc2luZyAncm9sZXMnIHBhcmFtLlwiKVxuXG4gICAgb3B0aW9ucyA9IFJvbGVzLl9ub3JtYWxpemVPcHRpb25zKG9wdGlvbnMpXG5cbiAgICAvLyBlbnN1cmUgYXJyYXlzXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHVzZXJzKSkgdXNlcnMgPSBbdXNlcnNdXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHJvbGVzKSkgcm9sZXMgPSBbcm9sZXNdXG5cbiAgICBSb2xlcy5fY2hlY2tTY29wZU5hbWUob3B0aW9ucy5zY29wZSlcblxuICAgIG9wdGlvbnMgPSBPYmplY3QuYXNzaWduKFxuICAgICAge1xuICAgICAgICBpZkV4aXN0czogZmFsc2VcbiAgICAgIH0sXG4gICAgICBvcHRpb25zXG4gICAgKVxuXG4gICAgZm9yIChjb25zdCB1c2VyIG9mIHVzZXJzKSB7XG4gICAgICBpZiAodHlwZW9mIHVzZXIgPT09ICdvYmplY3QnKSB7XG4gICAgICAgIGlkID0gdXNlci5faWRcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGlkID0gdXNlclxuICAgICAgfVxuXG4gICAgICBmb3IgKGNvbnN0IHJvbGUgb2Ygcm9sZXMpIHtcbiAgICAgICAgYXdhaXQgUm9sZXMuX2FkZFVzZXJUb1JvbGVBc3luYyhpZCwgcm9sZSwgb3B0aW9ucylcbiAgICAgIH1cbiAgICB9XG4gIH0sXG5cbiAgLyoqXG4gICAqIFNldCB1c2Vycycgcm9sZXMuXG4gICAqXG4gICAqIFJlcGxhY2VzIGFsbCBleGlzdGluZyByb2xlcyB3aXRoIGEgbmV3IHNldCBvZiByb2xlcy5cbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogICAgIGF3YWl0IFJvbGVzLnNldFVzZXJSb2xlc0FzeW5jKHVzZXJJZCwgJ2FkbWluJylcbiAgICogICAgIGF3YWl0IFJvbGVzLnNldFVzZXJSb2xlc0FzeW5jKHVzZXJJZCwgWyd2aWV3LXNlY3JldHMnXSwgJ2V4YW1wbGUuY29tJylcbiAgICogICAgIGF3YWl0IFJvbGVzLnNldFVzZXJSb2xlc0FzeW5jKFt1c2VyMSwgdXNlcjJdLCBbJ3VzZXInLCdlZGl0b3InXSlcbiAgICogICAgIGF3YWl0IFJvbGVzLnNldFVzZXJSb2xlc0FzeW5jKFt1c2VyMSwgdXNlcjJdLCBbJ2dsb3Jpb3VzLWFkbWluJywgJ3BlcmZvcm0tYWN0aW9uJ10sICdleGFtcGxlLm9yZycpXG4gICAqXG4gICAqIEBtZXRob2Qgc2V0VXNlclJvbGVzQXN5bmNcbiAgICogQHBhcmFtIHtBcnJheXxTdHJpbmd9IHVzZXJzIFVzZXIgSUQocykgb3Igb2JqZWN0KHMpIHdpdGggYW4gYF9pZGAgZmllbGQuXG4gICAqIEBwYXJhbSB7QXJyYXl8U3RyaW5nfSByb2xlcyBOYW1lKHMpIG9mIHJvbGVzIHRvIGFkZCB1c2VycyB0by4gUm9sZXMgaGF2ZSB0byBleGlzdC5cbiAgICogQHBhcmFtIHtPYmplY3R8U3RyaW5nfSBbb3B0aW9uc10gT3B0aW9uczpcbiAgICogICAtIGBzY29wZWA6IG5hbWUgb2YgdGhlIHNjb3BlLCBvciBgbnVsbGAgZm9yIHRoZSBnbG9iYWwgcm9sZVxuICAgKiAgIC0gYGFueVNjb3BlYDogaWYgYHRydWVgLCByZW1vdmUgYWxsIHJvbGVzIHRoZSB1c2VyIGhhcywgb2YgYW55IHNjb3BlLCBpZiBgZmFsc2VgLCBvbmx5IHRoZSBvbmUgaW4gdGhlIHNhbWUgc2NvcGVcbiAgICogICAtIGBpZkV4aXN0c2A6IGlmIGB0cnVlYCwgZG8gbm90IHRocm93IGFuIGV4Y2VwdGlvbiBpZiB0aGUgcm9sZSBkb2VzIG5vdCBleGlzdFxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICpcbiAgICogQWx0ZXJuYXRpdmVseSwgaXQgY2FuIGJlIGEgc2NvcGUgbmFtZSBzdHJpbmcuXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIHNldFVzZXJSb2xlc0FzeW5jOiBhc3luYyBmdW5jdGlvbiAodXNlcnMsIHJvbGVzLCBvcHRpb25zKSB7XG4gICAgbGV0IGlkXG5cbiAgICBpZiAoIXVzZXJzKSB0aHJvdyBuZXcgRXJyb3IoXCJNaXNzaW5nICd1c2VycycgcGFyYW0uXCIpXG4gICAgaWYgKCFyb2xlcykgdGhyb3cgbmV3IEVycm9yKFwiTWlzc2luZyAncm9sZXMnIHBhcmFtLlwiKVxuXG4gICAgb3B0aW9ucyA9IFJvbGVzLl9ub3JtYWxpemVPcHRpb25zKG9wdGlvbnMpXG5cbiAgICAvLyBlbnN1cmUgYXJyYXlzXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHVzZXJzKSkgdXNlcnMgPSBbdXNlcnNdXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHJvbGVzKSkgcm9sZXMgPSBbcm9sZXNdXG5cbiAgICBSb2xlcy5fY2hlY2tTY29wZU5hbWUob3B0aW9ucy5zY29wZSlcblxuICAgIG9wdGlvbnMgPSBPYmplY3QuYXNzaWduKFxuICAgICAge1xuICAgICAgICBpZkV4aXN0czogZmFsc2UsXG4gICAgICAgIGFueVNjb3BlOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIG9wdGlvbnNcbiAgICApXG5cbiAgICBmb3IgKGNvbnN0IHVzZXIgb2YgdXNlcnMpIHtcbiAgICAgIGlmICh0eXBlb2YgdXNlciA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgaWQgPSB1c2VyLl9pZFxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWQgPSB1c2VyXG4gICAgICB9XG4gICAgICAvLyB3ZSBmaXJzdCBjbGVhciBhbGwgcm9sZXMgZm9yIHRoZSB1c2VyXG4gICAgICBjb25zdCBzZWxlY3RvciA9IHsgJ3VzZXIuX2lkJzogaWQgfVxuICAgICAgaWYgKCFvcHRpb25zLmFueVNjb3BlKSB7XG4gICAgICAgIHNlbGVjdG9yLnNjb3BlID0gb3B0aW9ucy5zY29wZVxuICAgICAgfVxuXG4gICAgICBhd2FpdCBNZXRlb3Iucm9sZUFzc2lnbm1lbnQucmVtb3ZlQXN5bmMoc2VsZWN0b3IpXG5cbiAgICAgIC8vIGFuZCB0aGVuIGFkZCBhbGxcbiAgICAgIGZvciAoY29uc3Qgcm9sZSBvZiByb2xlcykge1xuICAgICAgICBhd2FpdCBSb2xlcy5fYWRkVXNlclRvUm9sZUFzeW5jKGlkLCByb2xlLCBvcHRpb25zKVxuICAgICAgfVxuICAgIH1cbiAgfSxcblxuICAvKipcbiAgICogQWRkIG9uZSB1c2VyIHRvIG9uZSByb2xlLlxuICAgKlxuICAgKiBAbWV0aG9kIF9hZGRVc2VyVG9Sb2xlQXN5bmNcbiAgICogQHBhcmFtIHtTdHJpbmd9IHVzZXJJZCBUaGUgdXNlciBJRC5cbiAgICogQHBhcmFtIHtTdHJpbmd9IHJvbGVOYW1lIE5hbWUgb2YgdGhlIHJvbGUgdG8gYWRkIHRoZSB1c2VyIHRvLiBUaGUgcm9sZSBoYXZlIHRvIGV4aXN0LlxuICAgKiBAcGFyYW0ge09iamVjdH0gb3B0aW9ucyBPcHRpb25zOlxuICAgKiAgIC0gYHNjb3BlYDogbmFtZSBvZiB0aGUgc2NvcGUsIG9yIGBudWxsYCBmb3IgdGhlIGdsb2JhbCByb2xlXG4gICAqICAgLSBgaWZFeGlzdHNgOiBpZiBgdHJ1ZWAsIGRvIG5vdCB0aHJvdyBhbiBleGNlcHRpb24gaWYgdGhlIHJvbGUgZG9lcyBub3QgZXhpc3RcbiAgICogQHJldHVybnMge1Byb21pc2V9XG4gICAqIEBwcml2YXRlXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIF9hZGRVc2VyVG9Sb2xlQXN5bmM6IGFzeW5jIGZ1bmN0aW9uICh1c2VySWQsIHJvbGVOYW1lLCBvcHRpb25zKSB7XG4gICAgUm9sZXMuX2NoZWNrUm9sZU5hbWUocm9sZU5hbWUpXG4gICAgUm9sZXMuX2NoZWNrU2NvcGVOYW1lKG9wdGlvbnMuc2NvcGUpXG5cbiAgICBpZiAoIXVzZXJJZCkge1xuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgY29uc3Qgcm9sZSA9IGF3YWl0IE1ldGVvci5yb2xlcy5maW5kT25lQXN5bmMoXG4gICAgICB7IF9pZDogcm9sZU5hbWUgfSxcbiAgICAgIHsgZmllbGRzOiB7IGNoaWxkcmVuOiAxIH0gfVxuICAgIClcblxuICAgIGlmICghcm9sZSkge1xuICAgICAgaWYgKG9wdGlvbnMuaWZFeGlzdHMpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJSb2xlICdcIiArIHJvbGVOYW1lICsgXCInIGRvZXMgbm90IGV4aXN0LlwiKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFRoaXMgbWlnaHQgY3JlYXRlIGR1cGxpY2F0ZXMsIGJlY2F1c2Ugd2UgZG9uJ3QgaGF2ZSBhIHVuaXF1ZSBpbmRleCwgYnV0IHRoYXQncyBhbGwgcmlnaHQuIEluIGNhc2UgdGhlcmUgYXJlIHR3bywgd2l0aGRyYXdpbmcgdGhlIHJvbGUgd2lsbCBlZmZlY3RpdmVseSBraWxsIHRoZW0gYm90aC5cbiAgICAvLyBUT0RPIHJldmlzaXQgdGhpc1xuICAgIC8qIGNvbnN0IHJlcyA9IGF3YWl0IFJvbGVBc3NpZ25tZW50Q29sbGVjdGlvbi51cHNlcnRBc3luYyhcbiAgICAgIHtcbiAgICAgICAgXCJ1c2VyLl9pZFwiOiB1c2VySWQsXG4gICAgICAgIFwicm9sZS5faWRcIjogcm9sZU5hbWUsXG4gICAgICAgIHNjb3BlOiBvcHRpb25zLnNjb3BlLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgJHNldE9uSW5zZXJ0OiB7XG4gICAgICAgICAgdXNlcjogeyBfaWQ6IHVzZXJJZCB9LFxuICAgICAgICAgIHJvbGU6IHsgX2lkOiByb2xlTmFtZSB9LFxuICAgICAgICAgIHNjb3BlOiBvcHRpb25zLnNjb3BlLFxuICAgICAgICB9LFxuICAgICAgfVxuICAgICk7ICovXG4gICAgY29uc3QgZXhpc3RpbmdBc3NpZ25tZW50ID0gYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50LmZpbmRPbmVBc3luYyh7XG4gICAgICAndXNlci5faWQnOiB1c2VySWQsXG4gICAgICAncm9sZS5faWQnOiByb2xlTmFtZSxcbiAgICAgIHNjb3BlOiBvcHRpb25zLnNjb3BlXG4gICAgfSlcblxuICAgIGxldCBpbnNlcnRlZElkXG4gICAgbGV0IHJlc1xuICAgIGlmIChleGlzdGluZ0Fzc2lnbm1lbnQpIHtcbiAgICAgIGF3YWl0IE1ldGVvci5yb2xlQXNzaWdubWVudC51cGRhdGVBc3luYyhleGlzdGluZ0Fzc2lnbm1lbnQuX2lkLCB7XG4gICAgICAgICRzZXQ6IHtcbiAgICAgICAgICB1c2VyOiB7IF9pZDogdXNlcklkIH0sXG4gICAgICAgICAgcm9sZTogeyBfaWQ6IHJvbGVOYW1lIH0sXG4gICAgICAgICAgc2NvcGU6IG9wdGlvbnMuc2NvcGVcbiAgICAgICAgfVxuICAgICAgfSlcblxuICAgICAgcmVzID0gYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50LmZpbmRPbmVBc3luYyhleGlzdGluZ0Fzc2lnbm1lbnQuX2lkKVxuICAgIH0gZWxzZSB7XG4gICAgICBpbnNlcnRlZElkID0gYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50Lmluc2VydEFzeW5jKHtcbiAgICAgICAgdXNlcjogeyBfaWQ6IHVzZXJJZCB9LFxuICAgICAgICByb2xlOiB7IF9pZDogcm9sZU5hbWUgfSxcbiAgICAgICAgc2NvcGU6IG9wdGlvbnMuc2NvcGVcbiAgICAgIH0pXG4gICAgfVxuXG4gICAgaWYgKGluc2VydGVkSWQpIHtcbiAgICAgIGF3YWl0IE1ldGVvci5yb2xlQXNzaWdubWVudC51cGRhdGVBc3luYyhcbiAgICAgICAgeyBfaWQ6IGluc2VydGVkSWQgfSxcbiAgICAgICAge1xuICAgICAgICAgICRzZXQ6IHtcbiAgICAgICAgICAgIGluaGVyaXRlZFJvbGVzOiBbXG4gICAgICAgICAgICAgIHJvbGVOYW1lLFxuICAgICAgICAgICAgICAuLi4oYXdhaXQgUm9sZXMuX2dldEluaGVyaXRlZFJvbGVOYW1lc0FzeW5jKHJvbGUpKVxuICAgICAgICAgICAgXS5tYXAoKHIpID0+ICh7IF9pZDogciB9KSlcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIClcblxuICAgICAgcmVzID0gYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50LmZpbmRPbmVBc3luYyh7IF9pZDogaW5zZXJ0ZWRJZCB9KVxuICAgIH1cbiAgICByZXMuaW5zZXJ0ZWRJZCA9IGluc2VydGVkSWQgLy8gRm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcblxuICAgIHJldHVybiByZXNcbiAgfSxcblxuICAvKipcbiAgICogUmV0dXJucyBhbiBhcnJheSBvZiByb2xlIG5hbWVzIHRoZSBnaXZlbiByb2xlIG5hbWUgaXMgYSBjaGlsZCBvZi5cbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogICAgIFJvbGVzLl9nZXRQYXJlbnRSb2xlTmFtZXNBc3luYyh7IF9pZDogJ2FkbWluJywgY2hpbGRyZW47IFtdIH0pXG4gICAqXG4gICAqIEBtZXRob2QgX2dldFBhcmVudFJvbGVOYW1lc0FzeW5jXG4gICAqIEBwYXJhbSB7b2JqZWN0fSByb2xlIFRoZSByb2xlIG9iamVjdFxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICogQHByaXZhdGVcbiAgICogQHN0YXRpY1xuICAgKi9cbiAgX2dldFBhcmVudFJvbGVOYW1lc0FzeW5jOiBhc3luYyBmdW5jdGlvbiAocm9sZSkge1xuICAgIGlmICghcm9sZSkge1xuICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgY29uc3QgcGFyZW50Um9sZXMgPSBuZXcgU2V0KFtyb2xlLl9pZF0pXG5cbiAgICBmb3IgKGNvbnN0IHJvbGVOYW1lIG9mIHBhcmVudFJvbGVzKSB7XG4gICAgICBmb3IgKGNvbnN0IHBhcmVudFJvbGUgb2YgYXdhaXQgTWV0ZW9yLnJvbGVzXG4gICAgICAgIC5maW5kKHsgJ2NoaWxkcmVuLl9pZCc6IHJvbGVOYW1lIH0pXG4gICAgICAgIC5mZXRjaEFzeW5jKCkpIHtcbiAgICAgICAgcGFyZW50Um9sZXMuYWRkKHBhcmVudFJvbGUuX2lkKVxuICAgICAgfVxuICAgIH1cblxuICAgIHBhcmVudFJvbGVzLmRlbGV0ZShyb2xlLl9pZClcblxuICAgIHJldHVybiBbLi4ucGFyZW50Um9sZXNdXG4gIH0sXG5cbiAgLyoqXG4gICAqIFJldHVybnMgYW4gYXJyYXkgb2Ygcm9sZSBuYW1lcyB0aGUgZ2l2ZW4gcm9sZSBuYW1lIGlzIGEgcGFyZW50IG9mLlxuICAgKlxuICAgKiBAZXhhbXBsZVxuICAgKiAgICAgUm9sZXMuX2dldEluaGVyaXRlZFJvbGVOYW1lcyh7IF9pZDogJ2FkbWluJywgY2hpbGRyZW47IFtdIH0pXG4gICAqXG4gICAqIEBtZXRob2QgX2dldEluaGVyaXRlZFJvbGVOYW1lc1xuICAgKiBAcGFyYW0ge29iamVjdH0gcm9sZSBUaGUgcm9sZSBvYmplY3RcbiAgICogQHJldHVybnMge1Byb21pc2V9XG4gICAqIEBwcml2YXRlXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIF9nZXRJbmhlcml0ZWRSb2xlTmFtZXNBc3luYzogYXN5bmMgZnVuY3Rpb24gKHJvbGUpIHtcbiAgICBjb25zdCBpbmhlcml0ZWRSb2xlcyA9IG5ldyBTZXQoKVxuICAgIGNvbnN0IG5lc3RlZFJvbGVzID0gbmV3IFNldChbcm9sZV0pXG5cbiAgICBmb3IgKGNvbnN0IHIgb2YgbmVzdGVkUm9sZXMpIHtcbiAgICAgIGNvbnN0IHJvbGVzID0gYXdhaXQgTWV0ZW9yLnJvbGVzXG4gICAgICAgIC5maW5kKFxuICAgICAgICAgIHsgX2lkOiB7ICRpbjogci5jaGlsZHJlbi5tYXAoKHIpID0+IHIuX2lkKSB9IH0sXG4gICAgICAgICAgeyBmaWVsZHM6IHsgY2hpbGRyZW46IDEgfSB9XG4gICAgICAgIClcbiAgICAgICAgLmZldGNoQXN5bmMoKVxuXG4gICAgICBmb3IgKGNvbnN0IHIyIG9mIHJvbGVzKSB7XG4gICAgICAgIGluaGVyaXRlZFJvbGVzLmFkZChyMi5faWQpXG4gICAgICAgIG5lc3RlZFJvbGVzLmFkZChyMilcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gWy4uLmluaGVyaXRlZFJvbGVzXVxuICB9LFxuXG4gIC8qKlxuICAgKiBSZW1vdmUgdXNlcnMgZnJvbSBhc3NpZ25lZCByb2xlcy5cbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICogICAgIGF3YWl0IFJvbGVzLnJlbW92ZVVzZXJzRnJvbVJvbGVzQXN5bmModXNlcklkLCAnYWRtaW4nKVxuICAgKiAgICAgYXdhaXQgUm9sZXMucmVtb3ZlVXNlcnNGcm9tUm9sZXNBc3luYyhbdXNlcklkLCB1c2VyMl0sIFsnZWRpdG9yJ10pXG4gICAqICAgICBhd2FpdCBSb2xlcy5yZW1vdmVVc2Vyc0Zyb21Sb2xlc0FzeW5jKHVzZXJJZCwgWyd1c2VyJ10sICdncm91cDEnKVxuICAgKlxuICAgKiBAbWV0aG9kIHJlbW92ZVVzZXJzRnJvbVJvbGVzQXN5bmNcbiAgICogQHBhcmFtIHtBcnJheXxTdHJpbmd9IHVzZXJzIFVzZXIgSUQocykgb3Igb2JqZWN0KHMpIHdpdGggYW4gYF9pZGAgZmllbGQuXG4gICAqIEBwYXJhbSB7QXJyYXl8U3RyaW5nfSByb2xlcyBOYW1lKHMpIG9mIHJvbGVzIHRvIHJlbW92ZSB1c2VycyBmcm9tLiBSb2xlcyBoYXZlIHRvIGV4aXN0LlxuICAgKiBAcGFyYW0ge09iamVjdHxTdHJpbmd9IFtvcHRpb25zXSBPcHRpb25zOlxuICAgKiAgIC0gYHNjb3BlYDogbmFtZSBvZiB0aGUgc2NvcGUsIG9yIGBudWxsYCBmb3IgdGhlIGdsb2JhbCByb2xlXG4gICAqICAgLSBgYW55U2NvcGVgOiBpZiBzZXQsIHJvbGUgY2FuIGJlIGluIGFueSBzY29wZSAoYHNjb3BlYCBvcHRpb24gaXMgaWdub3JlZClcbiAgICogQHJldHVybnMge1Byb21pc2V9XG4gICAqXG4gICAqIEFsdGVybmF0aXZlbHksIGl0IGNhbiBiZSBhIHNjb3BlIG5hbWUgc3RyaW5nLlxuICAgKiBAc3RhdGljXG4gICAqL1xuICByZW1vdmVVc2Vyc0Zyb21Sb2xlc0FzeW5jOiBhc3luYyBmdW5jdGlvbiAodXNlcnMsIHJvbGVzLCBvcHRpb25zKSB7XG4gICAgaWYgKCF1c2VycykgdGhyb3cgbmV3IEVycm9yKFwiTWlzc2luZyAndXNlcnMnIHBhcmFtLlwiKVxuICAgIGlmICghcm9sZXMpIHRocm93IG5ldyBFcnJvcihcIk1pc3NpbmcgJ3JvbGVzJyBwYXJhbS5cIilcblxuICAgIG9wdGlvbnMgPSBSb2xlcy5fbm9ybWFsaXplT3B0aW9ucyhvcHRpb25zKVxuXG4gICAgLy8gZW5zdXJlIGFycmF5c1xuICAgIGlmICghQXJyYXkuaXNBcnJheSh1c2VycykpIHVzZXJzID0gW3VzZXJzXVxuICAgIGlmICghQXJyYXkuaXNBcnJheShyb2xlcykpIHJvbGVzID0gW3JvbGVzXVxuXG4gICAgUm9sZXMuX2NoZWNrU2NvcGVOYW1lKG9wdGlvbnMuc2NvcGUpXG5cbiAgICBmb3IgKGNvbnN0IHVzZXIgb2YgdXNlcnMpIHtcbiAgICAgIGlmICghdXNlcikgcmV0dXJuXG5cbiAgICAgIGZvciAoY29uc3Qgcm9sZSBvZiByb2xlcykge1xuICAgICAgICBsZXQgaWRcbiAgICAgICAgaWYgKHR5cGVvZiB1c2VyID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgIGlkID0gdXNlci5faWRcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpZCA9IHVzZXJcbiAgICAgICAgfVxuXG4gICAgICAgIGF3YWl0IFJvbGVzLl9yZW1vdmVVc2VyRnJvbVJvbGVBc3luYyhpZCwgcm9sZSwgb3B0aW9ucylcbiAgICAgIH1cbiAgICB9XG4gIH0sXG5cbiAgLyoqXG4gICAqIFJlbW92ZSBvbmUgdXNlciBmcm9tIG9uZSByb2xlLlxuICAgKlxuICAgKiBAbWV0aG9kIF9yZW1vdmVVc2VyRnJvbVJvbGVBc3luY1xuICAgKiBAcGFyYW0ge1N0cmluZ30gdXNlcklkIFRoZSB1c2VyIElELlxuICAgKiBAcGFyYW0ge1N0cmluZ30gcm9sZU5hbWUgTmFtZSBvZiB0aGUgcm9sZSB0byBhZGQgdGhlIHVzZXIgdG8uIFRoZSByb2xlIGhhdmUgdG8gZXhpc3QuXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zIE9wdGlvbnM6XG4gICAqICAgLSBgc2NvcGVgOiBuYW1lIG9mIHRoZSBzY29wZSwgb3IgYG51bGxgIGZvciB0aGUgZ2xvYmFsIHJvbGVcbiAgICogICAtIGBhbnlTY29wZWA6IGlmIHNldCwgcm9sZSBjYW4gYmUgaW4gYW55IHNjb3BlIChgc2NvcGVgIG9wdGlvbiBpcyBpZ25vcmVkKVxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICogQHByaXZhdGVcbiAgICogQHN0YXRpY1xuICAgKi9cbiAgX3JlbW92ZVVzZXJGcm9tUm9sZUFzeW5jOiBhc3luYyBmdW5jdGlvbiAodXNlcklkLCByb2xlTmFtZSwgb3B0aW9ucykge1xuICAgIFJvbGVzLl9jaGVja1JvbGVOYW1lKHJvbGVOYW1lKVxuICAgIFJvbGVzLl9jaGVja1Njb3BlTmFtZShvcHRpb25zLnNjb3BlKVxuXG4gICAgaWYgKCF1c2VySWQpIHJldHVyblxuXG4gICAgY29uc3Qgc2VsZWN0b3IgPSB7XG4gICAgICAndXNlci5faWQnOiB1c2VySWQsXG4gICAgICAncm9sZS5faWQnOiByb2xlTmFtZVxuICAgIH1cblxuICAgIGlmICghb3B0aW9ucy5hbnlTY29wZSkge1xuICAgICAgc2VsZWN0b3Iuc2NvcGUgPSBvcHRpb25zLnNjb3BlXG4gICAgfVxuXG4gICAgYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50LnJlbW92ZUFzeW5jKHNlbGVjdG9yKVxuICB9LFxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiB1c2VyIGhhcyBzcGVjaWZpZWQgcm9sZXMuXG4gICAqXG4gICAqIEBleGFtcGxlXG4gICAqICAgICAvLyBnbG9iYWwgcm9sZXNcbiAgICogICAgIGF3YWl0IFJvbGVzLnVzZXJJc0luUm9sZUFzeW5jKHVzZXIsICdhZG1pbicpXG4gICAqICAgICBhd2FpdCBSb2xlcy51c2VySXNJblJvbGVBc3luYyh1c2VyLCBbJ2FkbWluJywnZWRpdG9yJ10pXG4gICAqICAgICBhd2FpdCBSb2xlcy51c2VySXNJblJvbGVBc3luYyh1c2VySWQsICdhZG1pbicpXG4gICAqICAgICBhd2FpdCBSb2xlcy51c2VySXNJblJvbGVBc3luYyh1c2VySWQsIFsnYWRtaW4nLCdlZGl0b3InXSlcbiAgICpcbiAgICogICAgIC8vIHNjb3BlIHJvbGVzIChnbG9iYWwgcm9sZXMgYXJlIHN0aWxsIGNoZWNrZWQpXG4gICAqICAgICBhd2FpdCBSb2xlcy51c2VySXNJblJvbGVBc3luYyh1c2VyLCAnYWRtaW4nLCAnZ3JvdXAxJylcbiAgICogICAgIGF3YWl0IFJvbGVzLnVzZXJJc0luUm9sZUFzeW5jKHVzZXJJZCwgWydhZG1pbicsJ2VkaXRvciddLCAnZ3JvdXAxJylcbiAgICogICAgIGF3YWl0IFJvbGVzLnVzZXJJc0luUm9sZUFzeW5jKHVzZXJJZCwgWydhZG1pbicsJ2VkaXRvciddLCB7c2NvcGU6ICdncm91cDEnfSlcbiAgICpcbiAgICogQG1ldGhvZCB1c2VySXNJblJvbGVBc3luY1xuICAgKiBAcGFyYW0ge1N0cmluZ3xPYmplY3R9IHVzZXIgVXNlciBJRCBvciBhbiBhY3R1YWwgdXNlciBvYmplY3QuXG4gICAqIEBwYXJhbSB7QXJyYXl8U3RyaW5nfSByb2xlcyBOYW1lIG9mIHJvbGUgb3IgYW4gYXJyYXkgb2Ygcm9sZXMgdG8gY2hlY2sgYWdhaW5zdC4gSWYgYXJyYXksXG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWxsIHJldHVybiBgdHJ1ZWAgaWYgdXNlciBpcyBpbiBfYW55XyByb2xlLlxuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUm9sZXMgZG8gbm90IGhhdmUgdG8gZXhpc3QuXG4gICAqIEBwYXJhbSB7T2JqZWN0fFN0cmluZ30gW29wdGlvbnNdIE9wdGlvbnM6XG4gICAqICAgLSBgc2NvcGVgOiBuYW1lIG9mIHRoZSBzY29wZTsgaWYgc3VwcGxpZWQsIGxpbWl0cyBjaGVjayB0byBqdXN0IHRoYXQgc2NvcGVcbiAgICogICAgIHRoZSB1c2VyJ3MgZ2xvYmFsIHJvbGVzIHdpbGwgYWx3YXlzIGJlIGNoZWNrZWQgd2hldGhlciBzY29wZSBpcyBzcGVjaWZpZWQgb3Igbm90XG4gICAqICAgLSBgYW55U2NvcGVgOiBpZiBzZXQsIHJvbGUgY2FuIGJlIGluIGFueSBzY29wZSAoYHNjb3BlYCBvcHRpb24gaXMgaWdub3JlZClcbiAgICpcbiAgICogQWx0ZXJuYXRpdmVseSwgaXQgY2FuIGJlIGEgc2NvcGUgbmFtZSBzdHJpbmcuXG4gICAqIEByZXR1cm4ge1Byb21pc2U8Qm9vbGVhbj59IGB0cnVlYCBpZiB1c2VyIGlzIGluIF9hbnlfIG9mIHRoZSB0YXJnZXQgcm9sZXNcbiAgICogQHN0YXRpY1xuICAgKi9cbiAgdXNlcklzSW5Sb2xlQXN5bmM6IGFzeW5jIGZ1bmN0aW9uICh1c2VyLCByb2xlcywgb3B0aW9ucykge1xuICAgIGxldCBpZFxuXG4gICAgb3B0aW9ucyA9IFJvbGVzLl9ub3JtYWxpemVPcHRpb25zKG9wdGlvbnMpXG5cbiAgICAvLyBlbnN1cmUgYXJyYXkgdG8gc2ltcGxpZnkgY29kZVxuICAgIGlmICghQXJyYXkuaXNBcnJheShyb2xlcykpIHJvbGVzID0gW3JvbGVzXVxuXG4gICAgcm9sZXMgPSByb2xlcy5maWx0ZXIoKHIpID0+IHIgIT0gbnVsbClcblxuICAgIGlmICghcm9sZXMubGVuZ3RoKSByZXR1cm4gZmFsc2VcblxuICAgIFJvbGVzLl9jaGVja1Njb3BlTmFtZShvcHRpb25zLnNjb3BlKVxuXG4gICAgb3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oXG4gICAgICB7XG4gICAgICAgIGFueVNjb3BlOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIG9wdGlvbnNcbiAgICApXG5cbiAgICBpZiAodXNlciAmJiB0eXBlb2YgdXNlciA9PT0gJ29iamVjdCcpIHtcbiAgICAgIGlkID0gdXNlci5faWRcbiAgICB9IGVsc2Uge1xuICAgICAgaWQgPSB1c2VyXG4gICAgfVxuXG4gICAgaWYgKCFpZCkgcmV0dXJuIGZhbHNlXG4gICAgaWYgKHR5cGVvZiBpZCAhPT0gJ3N0cmluZycpIHJldHVybiBmYWxzZVxuXG4gICAgY29uc3Qgc2VsZWN0b3IgPSB7XG4gICAgICAndXNlci5faWQnOiBpZFxuICAgIH1cblxuICAgIGlmICghb3B0aW9ucy5hbnlTY29wZSkge1xuICAgICAgc2VsZWN0b3Iuc2NvcGUgPSB7ICRpbjogW29wdGlvbnMuc2NvcGUsIG51bGxdIH1cbiAgICB9XG5cbiAgICBjb25zdCByZXMgPSBhd2FpdCBhc3luY1NvbWUocm9sZXMsIGFzeW5jIChyb2xlTmFtZSkgPT4ge1xuICAgICAgc2VsZWN0b3JbJ2luaGVyaXRlZFJvbGVzLl9pZCddID0gcm9sZU5hbWVcbiAgICAgIGNvbnN0IG91dCA9XG4gICAgICAgIChhd2FpdCBNZXRlb3Iucm9sZUFzc2lnbm1lbnQuY291bnREb2N1bWVudHMoc2VsZWN0b3IsIHsgbGltaXQ6IDEgfSkpID4gMFxuICAgICAgcmV0dXJuIG91dFxuICAgIH0pXG5cbiAgICByZXR1cm4gcmVzXG4gIH0sXG5cbiAgLyoqXG4gICAqIFJldHJpZXZlIHVzZXIncyByb2xlcy5cbiAgICpcbiAgICogQG1ldGhvZCBnZXRSb2xlc0ZvclVzZXJBc3luY1xuICAgKiBAcGFyYW0ge1N0cmluZ3xPYmplY3R9IHVzZXIgVXNlciBJRCBvciBhbiBhY3R1YWwgdXNlciBvYmplY3QuXG4gICAqIEBwYXJhbSB7T2JqZWN0fFN0cmluZ30gW29wdGlvbnNdIE9wdGlvbnM6XG4gICAqICAgLSBgc2NvcGVgOiBuYW1lIG9mIHNjb3BlIHRvIHByb3ZpZGUgcm9sZXMgZm9yOyBpZiBub3Qgc3BlY2lmaWVkLCBnbG9iYWwgcm9sZXMgYXJlIHJldHVybmVkXG4gICAqICAgLSBgYW55U2NvcGVgOiBpZiBzZXQsIHJvbGUgY2FuIGJlIGluIGFueSBzY29wZSAoYHNjb3BlYCBhbmQgYG9ubHlBc3NpZ25lZGAgb3B0aW9ucyBhcmUgaWdub3JlZClcbiAgICogICAtIGBvbmx5U2NvcGVkYDogaWYgc2V0LCBvbmx5IHJvbGVzIGluIHRoZSBzcGVjaWZpZWQgc2NvcGUgYXJlIHJldHVybmVkXG4gICAqICAgLSBgb25seUFzc2lnbmVkYDogcmV0dXJuIG9ubHkgYXNzaWduZWQgcm9sZXMgYW5kIG5vdCBhdXRvbWF0aWNhbGx5IGluZmVycmVkIChsaWtlIHN1YnJvbGVzKVxuICAgKiAgIC0gYGZ1bGxPYmplY3RzYDogcmV0dXJuIGZ1bGwgcm9sZXMgb2JqZWN0cyAoYHRydWVgKSBvciBqdXN0IG5hbWVzIChgZmFsc2VgKSAoYG9ubHlBc3NpZ25lZGAgb3B0aW9uIGlzIGlnbm9yZWQpIChkZWZhdWx0IGBmYWxzZWApXG4gICAqICAgICBJZiB5b3UgaGF2ZSBhIHVzZS1jYXNlIGZvciB0aGlzIG9wdGlvbiwgcGxlYXNlIGZpbGUgYSBmZWF0dXJlLXJlcXVlc3QuIFlvdSBzaG91bGRuJ3QgbmVlZCB0byB1c2UgaXQgYXMgaXQnc1xuICAgKiAgICAgcmVzdWx0IHN0cm9uZ2x5IGRlcGVuZGVudCBvbiB0aGUgaW50ZXJuYWwgZGF0YSBzdHJ1Y3R1cmUgb2YgdGhpcyBwbHVnaW4uXG4gICAqXG4gICAqIEFsdGVybmF0aXZlbHksIGl0IGNhbiBiZSBhIHNjb3BlIG5hbWUgc3RyaW5nLlxuICAgKiBAcmV0dXJuIHtQcm9taXNlPEFycmF5Pn0gQXJyYXkgb2YgdXNlcidzIHJvbGVzLCB1bnNvcnRlZC5cbiAgICogQHN0YXRpY1xuICAgKi9cbiAgZ2V0Um9sZXNGb3JVc2VyQXN5bmM6IGFzeW5jIGZ1bmN0aW9uICh1c2VyLCBvcHRpb25zKSB7XG4gICAgbGV0IGlkXG5cbiAgICBvcHRpb25zID0gUm9sZXMuX25vcm1hbGl6ZU9wdGlvbnMob3B0aW9ucylcblxuICAgIFJvbGVzLl9jaGVja1Njb3BlTmFtZShvcHRpb25zLnNjb3BlKVxuXG4gICAgb3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oe1xuICAgICAgZnVsbE9iamVjdHM6IGZhbHNlLFxuICAgICAgb25seUFzc2lnbmVkOiBmYWxzZSxcbiAgICAgIGFueVNjb3BlOiBmYWxzZSxcbiAgICAgIG9ubHlTY29wZWQ6IGZhbHNlXG4gICAgfSwgb3B0aW9ucylcblxuICAgIGlmICh1c2VyICYmIHR5cGVvZiB1c2VyID09PSAnb2JqZWN0Jykge1xuICAgICAgaWQgPSB1c2VyLl9pZFxuICAgIH0gZWxzZSB7XG4gICAgICBpZCA9IHVzZXJcbiAgICB9XG5cbiAgICBpZiAoIWlkKSByZXR1cm4gW11cblxuICAgIGNvbnN0IHNlbGVjdG9yID0ge1xuICAgICAgJ3VzZXIuX2lkJzogaWRcbiAgICB9XG5cbiAgICBjb25zdCBmaWx0ZXIgPSB7XG4gICAgICBmaWVsZHM6IHsgJ2luaGVyaXRlZFJvbGVzLl9pZCc6IDEgfVxuICAgIH1cblxuICAgIGlmICghb3B0aW9ucy5hbnlTY29wZSkge1xuICAgICAgc2VsZWN0b3Iuc2NvcGUgPSB7ICRpbjogW29wdGlvbnMuc2NvcGVdIH1cblxuICAgICAgaWYgKCFvcHRpb25zLm9ubHlTY29wZWQpIHtcbiAgICAgICAgc2VsZWN0b3Iuc2NvcGUuJGluLnB1c2gobnVsbClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucy5vbmx5QXNzaWduZWQpIHtcbiAgICAgIGRlbGV0ZSBmaWx0ZXIuZmllbGRzWydpbmhlcml0ZWRSb2xlcy5faWQnXVxuICAgICAgZmlsdGVyLmZpZWxkc1sncm9sZS5faWQnXSA9IDFcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucy5mdWxsT2JqZWN0cykge1xuICAgICAgZGVsZXRlIGZpbHRlci5maWVsZHNcbiAgICB9XG5cbiAgICBjb25zdCByb2xlcyA9IGF3YWl0IE1ldGVvci5yb2xlQXNzaWdubWVudC5maW5kKHNlbGVjdG9yLCBmaWx0ZXIpLmZldGNoQXN5bmMoKVxuXG4gICAgaWYgKG9wdGlvbnMuZnVsbE9iamVjdHMpIHtcbiAgICAgIHJldHVybiByb2xlc1xuICAgIH1cblxuICAgIHJldHVybiBbXG4gICAgICAuLi5uZXcgU2V0KFxuICAgICAgICByb2xlcy5yZWR1Y2UoKHJldiwgY3VycmVudCkgPT4ge1xuICAgICAgICAgIGlmIChjdXJyZW50LmluaGVyaXRlZFJvbGVzKSB7XG4gICAgICAgICAgICByZXR1cm4gcmV2LmNvbmNhdChjdXJyZW50LmluaGVyaXRlZFJvbGVzLm1hcCgocikgPT4gci5faWQpKVxuICAgICAgICAgIH0gZWxzZSBpZiAoY3VycmVudC5yb2xlKSB7XG4gICAgICAgICAgICByZXYucHVzaChjdXJyZW50LnJvbGUuX2lkKVxuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gcmV2XG4gICAgICAgIH0sIFtdKVxuICAgICAgKVxuICAgIF1cbiAgfSxcblxuICAvKipcbiAgICogUmV0cmlldmUgY3Vyc29yIG9mIGFsbCBleGlzdGluZyByb2xlcy5cbiAgICpcbiAgICogQG1ldGhvZCBnZXRBbGxSb2xlc1xuICAgKiBAcGFyYW0ge09iamVjdH0gW3F1ZXJ5T3B0aW9uc10gT3B0aW9ucyB3aGljaCBhcmUgcGFzc2VkIGRpcmVjdGx5XG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdWdoIHRvIGBSb2xlc0NvbGxlY3Rpb24uZmluZChxdWVyeSwgb3B0aW9ucylgLlxuICAgKiBAcmV0dXJuIHtDdXJzb3J9IEN1cnNvciBvZiBleGlzdGluZyByb2xlcy5cbiAgICogQHN0YXRpY1xuICAgKi9cbiAgZ2V0QWxsUm9sZXM6IGZ1bmN0aW9uIChxdWVyeU9wdGlvbnMpIHtcbiAgICBxdWVyeU9wdGlvbnMgPSBxdWVyeU9wdGlvbnMgfHwgeyBzb3J0OiB7IF9pZDogMSB9IH1cblxuICAgIHJldHVybiBNZXRlb3Iucm9sZXMuZmluZCh7fSwgcXVlcnlPcHRpb25zKVxuICB9LFxuXG4gIC8qKlxuICAgKiBSZXRyaWV2ZSBhbGwgdXNlcnMgd2hvIGFyZSBpbiB0YXJnZXQgcm9sZS5cbiAgICpcbiAgICogT3B0aW9uczpcbiAgICpcbiAgICogQG1ldGhvZCBnZXRVc2Vyc0luUm9sZUFzeW5jXG4gICAqIEBwYXJhbSB7QXJyYXl8U3RyaW5nfSByb2xlcyBOYW1lIG9mIHJvbGUgb3IgYW4gYXJyYXkgb2Ygcm9sZXMuIElmIGFycmF5LCB1c2Vyc1xuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuZWQgd2lsbCBoYXZlIGF0IGxlYXN0IG9uZSBvZiB0aGUgcm9sZXNcbiAgICogICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNwZWNpZmllZCBidXQgbmVlZCBub3QgaGF2ZSBfYWxsXyByb2xlcy5cbiAgICogICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJvbGVzIGRvIG5vdCBoYXZlIHRvIGV4aXN0LlxuICAgKiBAcGFyYW0ge09iamVjdHxTdHJpbmd9IFtvcHRpb25zXSBPcHRpb25zOlxuICAgKiAgIC0gYHNjb3BlYDogbmFtZSBvZiB0aGUgc2NvcGUgdG8gcmVzdHJpY3Qgcm9sZXMgdG87IHVzZXIncyBnbG9iYWxcbiAgICogICAgIHJvbGVzIHdpbGwgYWxzbyBiZSBjaGVja2VkXG4gICAqICAgLSBgYW55U2NvcGVgOiBpZiBzZXQsIHJvbGUgY2FuIGJlIGluIGFueSBzY29wZSAoYHNjb3BlYCBvcHRpb24gaXMgaWdub3JlZClcbiAgICogICAtIGBvbmx5U2NvcGVkYDogaWYgc2V0LCBvbmx5IHJvbGVzIGluIHRoZSBzcGVjaWZpZWQgc2NvcGUgYXJlIHJldHVybmVkXG4gICAqICAgLSBgcXVlcnlPcHRpb25zYDogb3B0aW9ucyB3aGljaCBhcmUgcGFzc2VkIGRpcmVjdGx5XG4gICAqICAgICB0aHJvdWdoIHRvIGBNZXRlb3IudXNlcnMuZmluZChxdWVyeSwgb3B0aW9ucylgXG4gICAqXG4gICAqIEFsdGVybmF0aXZlbHksIGl0IGNhbiBiZSBhIHNjb3BlIG5hbWUgc3RyaW5nLlxuICAgKiBAcGFyYW0ge09iamVjdH0gW3F1ZXJ5T3B0aW9uc10gT3B0aW9ucyB3aGljaCBhcmUgcGFzc2VkIGRpcmVjdGx5XG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdWdoIHRvIGBNZXRlb3IudXNlcnMuZmluZChxdWVyeSwgb3B0aW9ucylgXG4gICAqIEByZXR1cm4ge1Byb21pc2U8Q3Vyc29yPn0gQ3Vyc29yIG9mIHVzZXJzIGluIHJvbGVzLlxuICAgKiBAc3RhdGljXG4gICAqL1xuICBnZXRVc2Vyc0luUm9sZUFzeW5jOiBhc3luYyBmdW5jdGlvbiAocm9sZXMsIG9wdGlvbnMsIHF1ZXJ5T3B0aW9ucykge1xuICAgIGNvbnN0IGlkcyA9IChcbiAgICAgIGF3YWl0IFJvbGVzLmdldFVzZXJBc3NpZ25tZW50c0ZvclJvbGUocm9sZXMsIG9wdGlvbnMpLmZldGNoQXN5bmMoKVxuICAgICkubWFwKChhKSA9PiBhLnVzZXIuX2lkKVxuXG4gICAgcmV0dXJuIE1ldGVvci51c2Vycy5maW5kKFxuICAgICAgeyBfaWQ6IHsgJGluOiBpZHMgfSB9LFxuICAgICAgKG9wdGlvbnMgJiYgb3B0aW9ucy5xdWVyeU9wdGlvbnMpIHx8IHF1ZXJ5T3B0aW9ucyB8fCB7fVxuICAgIClcbiAgfSxcblxuICAvKipcbiAgICogUmV0cmlldmUgYWxsIGFzc2lnbm1lbnRzIG9mIGEgdXNlciB3aGljaCBhcmUgZm9yIHRoZSB0YXJnZXQgcm9sZS5cbiAgICpcbiAgICogT3B0aW9uczpcbiAgICpcbiAgICogQG1ldGhvZCBnZXRVc2VyQXNzaWdubWVudHNGb3JSb2xlXG4gICAqIEBwYXJhbSB7QXJyYXl8U3RyaW5nfSByb2xlcyBOYW1lIG9mIHJvbGUgb3IgYW4gYXJyYXkgb2Ygcm9sZXMuIElmIGFycmF5LCB1c2Vyc1xuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuZWQgd2lsbCBoYXZlIGF0IGxlYXN0IG9uZSBvZiB0aGUgcm9sZXNcbiAgICogICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNwZWNpZmllZCBidXQgbmVlZCBub3QgaGF2ZSBfYWxsXyByb2xlcy5cbiAgICogICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJvbGVzIGRvIG5vdCBoYXZlIHRvIGV4aXN0LlxuICAgKiBAcGFyYW0ge09iamVjdHxTdHJpbmd9IFtvcHRpb25zXSBPcHRpb25zOlxuICAgKiAgIC0gYHNjb3BlYDogbmFtZSBvZiB0aGUgc2NvcGUgdG8gcmVzdHJpY3Qgcm9sZXMgdG87IHVzZXIncyBnbG9iYWxcbiAgICogICAgIHJvbGVzIHdpbGwgYWxzbyBiZSBjaGVja2VkXG4gICAqICAgLSBgYW55U2NvcGVgOiBpZiBzZXQsIHJvbGUgY2FuIGJlIGluIGFueSBzY29wZSAoYHNjb3BlYCBvcHRpb24gaXMgaWdub3JlZClcbiAgICogICAtIGBxdWVyeU9wdGlvbnNgOiBvcHRpb25zIHdoaWNoIGFyZSBwYXNzZWQgZGlyZWN0bHlcbiAgICogICAgIHRocm91Z2ggdG8gYFJvbGVBc3NpZ25tZW50Q29sbGVjdGlvbi5maW5kKHF1ZXJ5LCBvcHRpb25zKWBcblxuICAgKiBBbHRlcm5hdGl2ZWx5LCBpdCBjYW4gYmUgYSBzY29wZSBuYW1lIHN0cmluZy5cbiAgICogQHJldHVybiB7Q3Vyc29yfSBDdXJzb3Igb2YgdXNlciBhc3NpZ25tZW50cyBmb3Igcm9sZXMuXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIGdldFVzZXJBc3NpZ25tZW50c0ZvclJvbGU6IGZ1bmN0aW9uIChyb2xlcywgb3B0aW9ucykge1xuICAgIG9wdGlvbnMgPSBSb2xlcy5fbm9ybWFsaXplT3B0aW9ucyhvcHRpb25zKVxuXG4gICAgb3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oXG4gICAgICB7XG4gICAgICAgIGFueVNjb3BlOiBmYWxzZSxcbiAgICAgICAgcXVlcnlPcHRpb25zOiB7fVxuICAgICAgfSxcbiAgICAgIG9wdGlvbnNcbiAgICApXG5cbiAgICByZXR1cm4gUm9sZXMuX2dldFVzZXJzSW5Sb2xlQ3Vyc29yKHJvbGVzLCBvcHRpb25zLCBvcHRpb25zLnF1ZXJ5T3B0aW9ucylcbiAgfSxcblxuICAvKipcbiAgICogQG1ldGhvZCBfZ2V0VXNlcnNJblJvbGVDdXJzb3JcbiAgICogQHBhcmFtIHtBcnJheXxTdHJpbmd9IHJvbGVzIE5hbWUgb2Ygcm9sZSBvciBhbiBhcnJheSBvZiByb2xlcy4gSWYgYXJyYXksIGlkcyBvZiB1c2VycyBhcmVcbiAgICogICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybmVkIHdoaWNoIGhhdmUgYXQgbGVhc3Qgb25lIG9mIHRoZSByb2xlc1xuICAgKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXNzaWduZWQgYnV0IG5lZWQgbm90IGhhdmUgX2FsbF8gcm9sZXMuXG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSb2xlcyBkbyBub3QgaGF2ZSB0byBleGlzdC5cbiAgICogQHBhcmFtIHtPYmplY3R8U3RyaW5nfSBbb3B0aW9uc10gT3B0aW9uczpcbiAgICogICAtIGBzY29wZWA6IG5hbWUgb2YgdGhlIHNjb3BlIHRvIHJlc3RyaWN0IHJvbGVzIHRvOyB1c2VyJ3MgZ2xvYmFsXG4gICAqICAgICByb2xlcyB3aWxsIGFsc28gYmUgY2hlY2tlZFxuICAgKiAgIC0gYGFueVNjb3BlYDogaWYgc2V0LCByb2xlIGNhbiBiZSBpbiBhbnkgc2NvcGUgKGBzY29wZWAgb3B0aW9uIGlzIGlnbm9yZWQpXG4gICAqXG4gICAqIEFsdGVybmF0aXZlbHksIGl0IGNhbiBiZSBhIHNjb3BlIG5hbWUgc3RyaW5nLlxuICAgKiBAcGFyYW0ge09iamVjdH0gW2ZpbHRlcl0gT3B0aW9ucyB3aGljaCBhcmUgcGFzc2VkIGRpcmVjdGx5XG4gICAqICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdWdoIHRvIGBSb2xlQXNzaWdubWVudENvbGxlY3Rpb24uZmluZChxdWVyeSwgb3B0aW9ucylgXG4gICAqIEByZXR1cm4ge09iamVjdH0gQ3Vyc29yIHRvIHRoZSBhc3NpZ25tZW50IGRvY3VtZW50c1xuICAgKiBAcHJpdmF0ZVxuICAgKiBAc3RhdGljXG4gICAqL1xuICBfZ2V0VXNlcnNJblJvbGVDdXJzb3I6IGZ1bmN0aW9uIChyb2xlcywgb3B0aW9ucywgZmlsdGVyKSB7XG4gICAgb3B0aW9ucyA9IFJvbGVzLl9ub3JtYWxpemVPcHRpb25zKG9wdGlvbnMpXG5cbiAgICBvcHRpb25zID0gT2JqZWN0LmFzc2lnbihcbiAgICAgIHtcbiAgICAgICAgYW55U2NvcGU6IGZhbHNlLFxuICAgICAgICBvbmx5U2NvcGVkOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIG9wdGlvbnNcbiAgICApXG5cbiAgICAvLyBlbnN1cmUgYXJyYXkgdG8gc2ltcGxpZnkgY29kZVxuICAgIGlmICghQXJyYXkuaXNBcnJheShyb2xlcykpIHJvbGVzID0gW3JvbGVzXVxuXG4gICAgUm9sZXMuX2NoZWNrU2NvcGVOYW1lKG9wdGlvbnMuc2NvcGUpXG5cbiAgICBmaWx0ZXIgPSBPYmplY3QuYXNzaWduKFxuICAgICAge1xuICAgICAgICBmaWVsZHM6IHsgJ3VzZXIuX2lkJzogMSB9XG4gICAgICB9LFxuICAgICAgZmlsdGVyXG4gICAgKVxuXG4gICAgY29uc3Qgc2VsZWN0b3IgPSB7XG4gICAgICAnaW5oZXJpdGVkUm9sZXMuX2lkJzogeyAkaW46IHJvbGVzIH1cbiAgICB9XG5cbiAgICBpZiAoIW9wdGlvbnMuYW55U2NvcGUpIHtcbiAgICAgIHNlbGVjdG9yLnNjb3BlID0geyAkaW46IFtvcHRpb25zLnNjb3BlXSB9XG5cbiAgICAgIGlmICghb3B0aW9ucy5vbmx5U2NvcGVkKSB7XG4gICAgICAgIHNlbGVjdG9yLnNjb3BlLiRpbi5wdXNoKG51bGwpXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIE1ldGVvci5yb2xlQXNzaWdubWVudC5maW5kKHNlbGVjdG9yLCBmaWx0ZXIpXG4gIH0sXG5cbiAgLyoqXG4gICAqIERlcHJlY2F0ZWQuIFVzZSBgZ2V0U2NvcGVzRm9yVXNlcmAgaW5zdGVhZC5cbiAgICpcbiAgICogQG1ldGhvZCBnZXRHcm91cHNGb3JVc2VyQXN5bmNcbiAgICogQHJldHVybnMge1Byb21pc2U8QXJyYXk+fVxuICAgKiBAc3RhdGljXG4gICAqIEBkZXByZWNhdGVkXG4gICAqL1xuICBnZXRHcm91cHNGb3JVc2VyQXN5bmM6IGFzeW5jIGZ1bmN0aW9uICguLi5hcmdzKSB7XG4gICAgaWYgKCFnZXRHcm91cHNGb3JVc2VyRGVwcmVjYXRpb25XYXJuaW5nKSB7XG4gICAgICBnZXRHcm91cHNGb3JVc2VyRGVwcmVjYXRpb25XYXJuaW5nID0gdHJ1ZVxuICAgICAgY29uc29sZSAmJlxuICAgICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICAgJ2dldEdyb3Vwc0ZvclVzZXIgaGFzIGJlZW4gZGVwcmVjYXRlZC4gVXNlIGdldFNjb3Blc0ZvclVzZXIgaW5zdGVhZC4nXG4gICAgICAgIClcbiAgICB9XG5cbiAgICByZXR1cm4gYXdhaXQgUm9sZXMuZ2V0U2NvcGVzRm9yVXNlciguLi5hcmdzKVxuICB9LFxuXG4gIC8qKlxuICAgKiBSZXRyaWV2ZSB1c2VycyBzY29wZXMsIGlmIGFueS5cbiAgICpcbiAgICogQG1ldGhvZCBnZXRTY29wZXNGb3JVc2VyQXN5bmNcbiAgICogQHBhcmFtIHtTdHJpbmd8T2JqZWN0fSB1c2VyIFVzZXIgSUQgb3IgYW4gYWN0dWFsIHVzZXIgb2JqZWN0LlxuICAgKiBAcGFyYW0ge0FycmF5fFN0cmluZ30gW3JvbGVzXSBOYW1lIG9mIHJvbGVzIHRvIHJlc3RyaWN0IHNjb3BlcyB0by5cbiAgICpcbiAgICogQHJldHVybiB7UHJvbWlzZTxBcnJheT59IEFycmF5IG9mIHVzZXIncyBzY29wZXMsIHVuc29ydGVkLlxuICAgKiBAc3RhdGljXG4gICAqL1xuICBnZXRTY29wZXNGb3JVc2VyQXN5bmM6IGFzeW5jIGZ1bmN0aW9uICh1c2VyLCByb2xlcykge1xuICAgIGxldCBpZFxuXG4gICAgaWYgKHJvbGVzICYmICFBcnJheS5pc0FycmF5KHJvbGVzKSkgcm9sZXMgPSBbcm9sZXNdXG5cbiAgICBpZiAodXNlciAmJiB0eXBlb2YgdXNlciA9PT0gJ29iamVjdCcpIHtcbiAgICAgIGlkID0gdXNlci5faWRcbiAgICB9IGVsc2Uge1xuICAgICAgaWQgPSB1c2VyXG4gICAgfVxuXG4gICAgaWYgKCFpZCkgcmV0dXJuIFtdXG5cbiAgICBjb25zdCBzZWxlY3RvciA9IHtcbiAgICAgICd1c2VyLl9pZCc6IGlkLFxuICAgICAgc2NvcGU6IHsgJG5lOiBudWxsIH1cbiAgICB9XG5cbiAgICBpZiAocm9sZXMpIHtcbiAgICAgIHNlbGVjdG9yWydpbmhlcml0ZWRSb2xlcy5faWQnXSA9IHsgJGluOiByb2xlcyB9XG4gICAgfVxuXG4gICAgY29uc3Qgc2NvcGVzID0gKFxuICAgICAgYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50XG4gICAgICAgIC5maW5kKHNlbGVjdG9yLCB7IGZpZWxkczogeyBzY29wZTogMSB9IH0pXG4gICAgICAgIC5mZXRjaEFzeW5jKClcbiAgICApLm1hcCgob2JpKSA9PiBvYmkuc2NvcGUpXG5cbiAgICByZXR1cm4gWy4uLm5ldyBTZXQoc2NvcGVzKV1cbiAgfSxcblxuICAvKipcbiAgICogUmVuYW1lIGEgc2NvcGUuXG4gICAqXG4gICAqIFJvbGVzIGFzc2lnbmVkIHdpdGggYSBnaXZlbiBzY29wZSBhcmUgY2hhbmdlZCB0byBiZSB1bmRlciB0aGUgbmV3IHNjb3BlLlxuICAgKlxuICAgKiBAbWV0aG9kIHJlbmFtZVNjb3BlQXN5bmNcbiAgICogQHBhcmFtIHtTdHJpbmd9IG9sZE5hbWUgT2xkIG5hbWUgb2YgYSBzY29wZS5cbiAgICogQHBhcmFtIHtTdHJpbmd9IG5ld05hbWUgTmV3IG5hbWUgb2YgYSBzY29wZS5cbiAgICogQHJldHVybnMge1Byb21pc2V9XG4gICAqIEBzdGF0aWNcbiAgICovXG4gIHJlbmFtZVNjb3BlQXN5bmM6IGFzeW5jIGZ1bmN0aW9uIChvbGROYW1lLCBuZXdOYW1lKSB7XG4gICAgbGV0IGNvdW50XG5cbiAgICBSb2xlcy5fY2hlY2tTY29wZU5hbWUob2xkTmFtZSlcbiAgICBSb2xlcy5fY2hlY2tTY29wZU5hbWUobmV3TmFtZSlcblxuICAgIGlmIChvbGROYW1lID09PSBuZXdOYW1lKSByZXR1cm5cblxuICAgIGRvIHtcbiAgICAgIGNvdW50ID0gYXdhaXQgTWV0ZW9yLnJvbGVBc3NpZ25tZW50LnVwZGF0ZUFzeW5jKFxuICAgICAgICB7XG4gICAgICAgICAgc2NvcGU6IG9sZE5hbWVcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgICRzZXQ6IHtcbiAgICAgICAgICAgIHNjb3BlOiBuZXdOYW1lXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICB7IG11bHRpOiB0cnVlIH1cbiAgICAgIClcbiAgICB9IHdoaWxlIChjb3VudCA+IDApXG4gIH0sXG5cbiAgLyoqXG4gICAqIFJlbW92ZSBhIHNjb3BlLlxuICAgKlxuICAgKiBSb2xlcyBhc3NpZ25lZCB3aXRoIGEgZ2l2ZW4gc2NvcGUgYXJlIHJlbW92ZWQuXG4gICAqXG4gICAqIEBtZXRob2QgcmVtb3ZlU2NvcGVBc3luY1xuICAgKiBAcGFyYW0ge1N0cmluZ30gbmFtZSBUaGUgbmFtZSBvZiBhIHNjb3BlLlxuICAgKiBAcmV0dXJucyB7UHJvbWlzZX1cbiAgICogQHN0YXRpY1xuICAgKi9cbiAgcmVtb3ZlU2NvcGVBc3luYzogYXN5bmMgZnVuY3Rpb24gKG5hbWUpIHtcbiAgICBSb2xlcy5fY2hlY2tTY29wZU5hbWUobmFtZSlcblxuICAgIGF3YWl0IE1ldGVvci5yb2xlQXNzaWdubWVudC5yZW1vdmVBc3luYyh7IHNjb3BlOiBuYW1lIH0pXG4gIH0sXG5cbiAgLyoqXG4gICAqIFRocm93IGFuIGV4Y2VwdGlvbiBpZiBgcm9sZU5hbWVgIGlzIGFuIGludmFsaWQgcm9sZSBuYW1lLlxuICAgKlxuICAgKiBAbWV0aG9kIF9jaGVja1JvbGVOYW1lXG4gICAqIEBwYXJhbSB7U3RyaW5nfSByb2xlTmFtZSBBIHJvbGUgbmFtZSB0byBtYXRjaCBhZ2FpbnN0LlxuICAgKiBAcHJpdmF0ZVxuICAgKiBAc3RhdGljXG4gICAqL1xuICBfY2hlY2tSb2xlTmFtZTogZnVuY3Rpb24gKHJvbGVOYW1lKSB7XG4gICAgaWYgKFxuICAgICAgIXJvbGVOYW1lIHx8XG4gICAgICB0eXBlb2Ygcm9sZU5hbWUgIT09ICdzdHJpbmcnIHx8XG4gICAgICByb2xlTmFtZS50cmltKCkgIT09IHJvbGVOYW1lXG4gICAgKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEludmFsaWQgcm9sZSBuYW1lICcke3JvbGVOYW1lfScuYClcbiAgICB9XG4gIH0sXG5cbiAgLyoqXG4gICAqIEZpbmQgb3V0IGlmIGEgcm9sZSBpcyBhbiBhbmNlc3RvciBvZiBhbm90aGVyIHJvbGUuXG4gICAqXG4gICAqIFdBUk5JTkc6IElmIHlvdSBjaGVjayB0aGlzIG9uIHRoZSBjbGllbnQsIHBsZWFzZSBtYWtlIHN1cmUgYWxsIHJvbGVzIGFyZSBwdWJsaXNoZWQuXG4gICAqXG4gICAqIEBtZXRob2QgaXNQYXJlbnRPZkFzeW5jXG4gICAqIEBwYXJhbSB7U3RyaW5nfSBwYXJlbnRSb2xlTmFtZSBUaGUgcm9sZSB5b3Ugd2FudCB0byByZXNlYXJjaC5cbiAgICogQHBhcmFtIHtTdHJpbmd9IGNoaWxkUm9sZU5hbWUgVGhlIHJvbGUgeW91IGV4cGVjdCB0byBiZSBhbW9uZyB0aGUgY2hpbGRyZW4gb2YgcGFyZW50Um9sZU5hbWUuXG4gICAqIEByZXR1cm5zIHtQcm9taXNlfVxuICAgKiBAc3RhdGljXG4gICAqL1xuICBpc1BhcmVudE9mQXN5bmM6IGFzeW5jIGZ1bmN0aW9uIChwYXJlbnRSb2xlTmFtZSwgY2hpbGRSb2xlTmFtZSkge1xuICAgIGlmIChwYXJlbnRSb2xlTmFtZSA9PT0gY2hpbGRSb2xlTmFtZSkge1xuICAgICAgcmV0dXJuIHRydWVcbiAgICB9XG5cbiAgICBpZiAocGFyZW50Um9sZU5hbWUgPT0gbnVsbCB8fCBjaGlsZFJvbGVOYW1lID09IG51bGwpIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cblxuICAgIFJvbGVzLl9jaGVja1JvbGVOYW1lKHBhcmVudFJvbGVOYW1lKVxuICAgIFJvbGVzLl9jaGVja1JvbGVOYW1lKGNoaWxkUm9sZU5hbWUpXG5cbiAgICBsZXQgcm9sZXNUb0NoZWNrID0gW3BhcmVudFJvbGVOYW1lXVxuICAgIHdoaWxlIChyb2xlc1RvQ2hlY2subGVuZ3RoICE9PSAwKSB7XG4gICAgICBjb25zdCByb2xlTmFtZSA9IHJvbGVzVG9DaGVjay5wb3AoKVxuXG4gICAgICBpZiAocm9sZU5hbWUgPT09IGNoaWxkUm9sZU5hbWUpIHtcbiAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgIH1cblxuICAgICAgY29uc3Qgcm9sZSA9IGF3YWl0IE1ldGVvci5yb2xlcy5maW5kT25lQXN5bmMoeyBfaWQ6IHJvbGVOYW1lIH0pXG5cbiAgICAgIC8vIFRoaXMgc2hvdWxkIG5vdCBoYXBwZW4sIGJ1dCB0aGlzIGlzIGEgcHJvYmxlbSB0byBhZGRyZXNzIGF0IHNvbWUgb3RoZXIgdGltZS5cbiAgICAgIGlmICghcm9sZSkgY29udGludWVcblxuICAgICAgcm9sZXNUb0NoZWNrID0gcm9sZXNUb0NoZWNrLmNvbmNhdChyb2xlLmNoaWxkcmVuLm1hcCgocikgPT4gci5faWQpKVxuICAgIH1cblxuICAgIHJldHVybiBmYWxzZVxuICB9LFxuXG4gIC8qKlxuICAgKiBOb3JtYWxpemUgb3B0aW9ucy5cbiAgICpcbiAgICogQG1ldGhvZCBfbm9ybWFsaXplT3B0aW9uc1xuICAgKiBAcGFyYW0ge09iamVjdH0gb3B0aW9ucyBPcHRpb25zIHRvIG5vcm1hbGl6ZS5cbiAgICogQHJldHVybiB7T2JqZWN0fSBOb3JtYWxpemVkIG9wdGlvbnMuXG4gICAqIEBwcml2YXRlXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIF9ub3JtYWxpemVPcHRpb25zOiBmdW5jdGlvbiAob3B0aW9ucykge1xuICAgIG9wdGlvbnMgPSBvcHRpb25zID09PSB1bmRlZmluZWQgPyB7fSA6IG9wdGlvbnNcblxuICAgIC8vIFRPRE8gTnVtYmVyIHdpbGwgZXJyb3Igb3V0IG9uIHNjb3BlIHZhbGlkYXRpb24sIHdlIGNhbiBlaXRoZXIgZXJyb3IgaXQgb3V0IGhlcmVcbiAgICAvLyBvciBtYWtlIGl0IGludG8gYSBzdHJpbmcgYW5kIGhlbmNlIGEgdmFsaWQgaW5wdXQuXG4gICAgaWYgKG9wdGlvbnMgPT09IG51bGwgfHwgdHlwZW9mIG9wdGlvbnMgPT09ICdzdHJpbmcnIHx8IHR5cGVvZiBvcHRpb25zID09PSAnbnVtYmVyJykge1xuICAgICAgb3B0aW9ucyA9IHsgc2NvcGU6IG9wdGlvbnMgfVxuICAgIH1cblxuICAgIG9wdGlvbnMuc2NvcGUgPSBSb2xlcy5fbm9ybWFsaXplU2NvcGVOYW1lKG9wdGlvbnMuc2NvcGUpXG5cbiAgICByZXR1cm4gb3B0aW9uc1xuICB9LFxuXG4gIC8qKlxuICAgKiBOb3JtYWxpemUgc2NvcGUgbmFtZS5cbiAgICpcbiAgICogQG1ldGhvZCBfbm9ybWFsaXplU2NvcGVOYW1lXG4gICAqIEBwYXJhbSB7U3RyaW5nfSBzY29wZU5hbWUgQSBzY29wZSBuYW1lIHRvIG5vcm1hbGl6ZS5cbiAgICogQHJldHVybiB7U3RyaW5nfSBOb3JtYWxpemVkIHNjb3BlIG5hbWUuXG4gICAqIEBwcml2YXRlXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIF9ub3JtYWxpemVTY29wZU5hbWU6IGZ1bmN0aW9uIChzY29wZU5hbWUpIHtcbiAgICAvLyBtYXAgdW5kZWZpbmVkIGFuZCBudWxsIHRvIG51bGxcbiAgICBpZiAoc2NvcGVOYW1lID09IG51bGwpIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBzY29wZU5hbWVcbiAgICB9XG4gIH0sXG5cbiAgLyoqXG4gICAqIFRocm93IGFuIGV4Y2VwdGlvbiBpZiBgc2NvcGVOYW1lYCBpcyBhbiBpbnZhbGlkIHNjb3BlIG5hbWUuXG4gICAqXG4gICAqIEBtZXRob2QgX2NoZWNrU2NvcGVOYW1lXG4gICAqIEBwYXJhbSB7U3RyaW5nfSBzY29wZU5hbWUgQSBzY29wZSBuYW1lIHRvIG1hdGNoIGFnYWluc3QuXG4gICAqIEBwcml2YXRlXG4gICAqIEBzdGF0aWNcbiAgICovXG4gIF9jaGVja1Njb3BlTmFtZTogZnVuY3Rpb24gKHNjb3BlTmFtZSkge1xuICAgIGlmIChzY29wZU5hbWUgPT09IG51bGwpIHJldHVyblxuXG4gICAgaWYgKFxuICAgICAgIXNjb3BlTmFtZSB8fFxuICAgICAgdHlwZW9mIHNjb3BlTmFtZSAhPT0gJ3N0cmluZycgfHxcbiAgICAgIHNjb3BlTmFtZS50cmltKCkgIT09IHNjb3BlTmFtZVxuICAgICkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIHNjb3BlIG5hbWUgJyR7c2NvcGVOYW1lfScuYClcbiAgICB9XG4gIH1cbn0pXG4iLCJpbXBvcnQgeyBSb2xlc0NvbGxlY3Rpb24sIFJvbGVBc3NpZ25tZW50Q29sbGVjdGlvbiB9IGZyb20gJy4vcm9sZXNfY29tbW9uX2FzeW5jJ1xuXG5jb25zdCBpbmRleEZuQXNzaWdubWVudCA9IFJvbGVBc3NpZ25tZW50Q29sbGVjdGlvbi5jcmVhdGVJbmRleEFzeW5jLmJpbmQoUm9sZUFzc2lnbm1lbnRDb2xsZWN0aW9uKVxuY29uc3QgaW5kZXhGblJvbGVzID0gUm9sZXNDb2xsZWN0aW9uLmNyZWF0ZUluZGV4QXN5bmMuYmluZChSb2xlc0NvbGxlY3Rpb24pXG5cbmNvbnN0IGluZGV4ZXMgPSBbXG4gIHsgJ3VzZXIuX2lkJzogMSwgJ2luaGVyaXRlZFJvbGVzLl9pZCc6IDEsIHNjb3BlOiAxIH0sXG4gIHsgJ3VzZXIuX2lkJzogMSwgJ3JvbGUuX2lkJzogMSwgc2NvcGU6IDEgfSxcbiAgeyAncm9sZS5faWQnOiAxIH0sXG4gIHsgc2NvcGU6IDEsICd1c2VyLl9pZCc6IDEsICdpbmhlcml0ZWRSb2xlcy5faWQnOiAxIH0sIC8vIEFkZGluZyB1c2VySWQgYW5kIHJvbGVJZCBtaWdodCBzcGVlZCB1cCBvdGhlciBxdWVyaWVzIGRlcGVuZGluZyBvbiB0aGUgZmlyc3QgaW5kZXhcbiAgeyAnaW5oZXJpdGVkUm9sZXMuX2lkJzogMSB9XG5dXG5mb3IgKGNvbnN0IGluZGV4IG9mIGluZGV4ZXMpIHtcbiAgaW5kZXhGbkFzc2lnbm1lbnQoaW5kZXgpXG59XG5pbmRleEZuUm9sZXMoeyAnY2hpbGRyZW4uX2lkJzogMSB9KVxuIl19
