{"version": 3, "sources": ["meteor://💻app/packages/check/match.js", "meteor://💻app/packages/check/isPlainObject.js"], "names": ["module", "export", "check", "Match", "isPlainObject", "link", "v", "currentArgumentChecker", "Meteor", "EnvironmentVariable", "hasOwn", "Object", "prototype", "hasOwnProperty", "format", "result", "err", "Error", "message", "path", "concat", "value", "pattern", "options", "arguments", "length", "undefined", "throwAllErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getOrNullIfOutsideFiber", "checking", "testSubtree", "Array", "isArray", "map", "r", "Optional", "Maybe", "OneOf", "_len", "args", "_key", "Any", "Where", "condition", "ObjectIncluding", "ObjectWithValues", "Integer", "makeErrorType", "msg", "sanitizedError", "test", "_failIfArgumentsAreNotAllChecked", "f", "context", "description", "<PERSON>rg<PERSON><PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON>", "apply", "throwUnlessAllArgumentsHaveBeenChecked", "constructor", "choices", "stringForErrorMessage", "onlyShowType", "EJSON", "stringify", "JSON", "stringifyError", "name", "typeofChecks", "String", "Number", "Boolean", "Function", "collectErrors", "errors", "i", "isArguments", "arr<PERSON><PERSON>", "_prependPath", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>llowed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredPatterns", "create", "optionalPatterns", "keys", "for<PERSON>ach", "key", "subPattern", "subValue", "obj<PERSON><PERSON>", "call", "createMissingError", "reverse", "_checkingOneValue", "bind", "isNaN", "splice", "_jsKeywords", "base", "match", "indexOf", "isObject", "baseIsArguments", "item", "toString", "callee", "class2type", "fnToString", "ObjectFunctionString", "getProto", "getPrototypeOf", "obj", "proto", "Ctor"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,KAAK,EAACA,CAAA,KAAIA,KAAK;EAACC,KAAK,EAACA,CAAA,KAAIA;AAAK,CAAC,CAAC;AAAC,IAAIC,aAAa;AAACJ,MAAM,CAACK,IAAI,CAAC,iBAAiB,EAAC;EAACD,aAAaA,CAACE,CAAC,EAAC;IAACF,aAAa,GAACE,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAGvI;AACA;;AAEA,MAAMC,sBAAsB,GAAG,IAAIC,MAAM,CAACC,mBAAmB,CAAD,CAAC;AAC7D,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,MAAMC,MAAM,GAAGC,MAAM,IAAI;EACvB,MAAMC,GAAG,GAAG,IAAIb,KAAK,CAACc,KAAK,CAACF,MAAM,CAACG,OAAO,CAAC;EAC3C,IAAIH,MAAM,CAACI,IAAI,EAAE;IACfH,GAAG,CAACE,OAAO,iBAAAE,MAAA,CAAiBL,MAAM,CAACI,IAAI,CAAE;IACzCH,GAAG,CAACG,IAAI,GAAGJ,MAAM,CAACI,IAAI;EACxB;EAEA,OAAOH,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASd,KAAKA,CAACmB,KAAK,EAAEC,OAAO,EAAuC;EAAA,IAArCC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IAAEG,cAAc,EAAE;EAAM,CAAC;EACvE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,UAAU,GAAGrB,sBAAsB,CAACsB,uBAAuB,CAAC,CAAC;EACnE,IAAID,UAAU,EAAE;IACdA,UAAU,CAACE,QAAQ,CAACT,KAAK,CAAC;EAC5B;EAEA,MAAMN,MAAM,GAAGgB,WAAW,CAACV,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAACI,cAAc,CAAC;EAElE,IAAIZ,MAAM,EAAE;IACV,IAAIQ,OAAO,CAACI,cAAc,EAAE;MAC1B,MAAMK,KAAK,CAACC,OAAO,CAAClB,MAAM,CAAC,GAAGA,MAAM,CAACmB,GAAG,CAACC,CAAC,IAAIrB,MAAM,CAACqB,CAAC,CAAC,CAAC,GAAG,CAACrB,MAAM,CAACC,MAAM,CAAC,CAAC;IAC7E,CAAC,MAAM;MACL,MAAMD,MAAM,CAACC,MAAM,CAAC;IACtB;EACF;AACF;AAAC;;AAED;AACA;AACA;AACA;AACO,MAAMZ,KAAK,GAAG;EACnBiC,QAAQ,EAAE,SAAAA,CAASd,OAAO,EAAE;IAC1B,OAAO,IAAIc,QAAQ,CAACd,OAAO,CAAC;EAC9B,CAAC;EAEDe,KAAK,EAAE,SAAAA,CAASf,OAAO,EAAE;IACvB,OAAO,IAAIe,KAAK,CAACf,OAAO,CAAC;EAC3B,CAAC;EAEDgB,KAAK,EAAE,SAAAA,CAAA,EAAkB;IAAA,SAAAC,IAAA,GAAAf,SAAA,CAAAC,MAAA,EAANe,IAAI,OAAAR,KAAA,CAAAO,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAJD,IAAI,CAAAC,IAAA,IAAAjB,SAAA,CAAAiB,IAAA;IAAA;IACrB,OAAO,IAAIH,KAAK,CAACE,IAAI,CAAC;EACxB,CAAC;EAEDE,GAAG,EAAE,CAAC,SAAS,CAAC;EAChBC,KAAK,EAAE,SAAAA,CAASC,SAAS,EAAE;IACzB,OAAO,IAAID,KAAK,CAACC,SAAS,CAAC;EAC7B,CAAC;EAEDC,eAAe,EAAE,SAAAA,CAASvB,OAAO,EAAE;IACjC,OAAO,IAAIuB,eAAe,CAACvB,OAAO,CAAC;EACrC,CAAC;EAEDwB,gBAAgB,EAAE,SAAAA,CAASxB,OAAO,EAAE;IAClC,OAAO,IAAIwB,gBAAgB,CAACxB,OAAO,CAAC;EACtC,CAAC;EAED;EACAyB,OAAO,EAAE,CAAC,aAAa,CAAC;EAExB;EACA9B,KAAK,EAAET,MAAM,CAACwC,aAAa,CAAC,aAAa,EAAE,UAAUC,GAAG,EAAE;IACxD,IAAI,CAAC/B,OAAO,mBAAAE,MAAA,CAAmB6B,GAAG,CAAE;;IAEpC;IACA;IACA;IACA;IACA,IAAI,CAAC9B,IAAI,GAAG,EAAE;;IAEd;IACA;IACA,IAAI,CAAC+B,cAAc,GAAG,IAAI1C,MAAM,CAACS,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;EAC7D,CAAC,CAAC;EAEF;EACA;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEkC,IAAIA,CAAC9B,KAAK,EAAEC,OAAO,EAAE;IACnB,OAAO,CAACS,WAAW,CAACV,KAAK,EAAEC,OAAO,CAAC;EACrC,CAAC;EAED;EACA;EACA;EACA8B,gCAAgCA,CAACC,CAAC,EAAEC,OAAO,EAAEd,IAAI,EAAEe,WAAW,EAAE;IAC9D,MAAM3B,UAAU,GAAG,IAAI4B,eAAe,CAAChB,IAAI,EAAEe,WAAW,CAAC;IACzD,MAAMxC,MAAM,GAAGR,sBAAsB,CAACkD,SAAS,CAC7C7B,UAAU,EACV,MAAMyB,CAAC,CAACK,KAAK,CAACJ,OAAO,EAAEd,IAAI,CAC7B,CAAC;;IAED;IACAZ,UAAU,CAAC+B,sCAAsC,CAAC,CAAC;IACnD,OAAO5C,MAAM;EACf;AACF,CAAC;AAED,MAAMqB,QAAQ,CAAC;EACbwB,WAAWA,CAACtC,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAEA,MAAMe,KAAK,CAAC;EACVuB,WAAWA,CAACtC,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAEA,MAAMgB,KAAK,CAAC;EACVsB,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACpC,MAAM,KAAK,CAAC,EAAE;MACpC,MAAM,IAAIR,KAAK,CAAC,iDAAiD,CAAC;IACpE;IAEA,IAAI,CAAC4C,OAAO,GAAGA,OAAO;EACxB;AACF;AAEA,MAAMlB,KAAK,CAAC;EACViB,WAAWA,CAAChB,SAAS,EAAE;IACrB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC5B;AACF;AAEA,MAAMC,eAAe,CAAC;EACpBe,WAAWA,CAACtC,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAEA,MAAMwB,gBAAgB,CAAC;EACrBc,WAAWA,CAACtC,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF;AAEA,MAAMwC,qBAAqB,GAAG,SAAAA,CAACzC,KAAK,EAAmB;EAAA,IAAjBE,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAChD,IAAKH,KAAK,KAAK,IAAI,EAAG;IACpB,OAAO,MAAM;EACf;EAEA,IAAKE,OAAO,CAACwC,YAAY,EAAG;IAC1B,OAAO,OAAO1C,KAAK;EACrB;;EAEA;EACA,IAAK,OAAOA,KAAK,KAAK,QAAQ,EAAG;IAC/B,OAAO2C,KAAK,CAACC,SAAS,CAAC5C,KAAK,CAAC;EAC/B;EAEA,IAAI;IAEF;IACA;IACA6C,IAAI,CAACD,SAAS,CAAC5C,KAAK,CAAC;EACvB,CAAC,CAAC,OAAO8C,cAAc,EAAE;IACvB,IAAKA,cAAc,CAACC,IAAI,KAAK,WAAW,EAAG;MACzC,OAAO,OAAO/C,KAAK;IACrB;EACF;EAEA,OAAO2C,KAAK,CAACC,SAAS,CAAC5C,KAAK,CAAC;AAC/B,CAAC;AAED,MAAMgD,YAAY,GAAG,CACnB,CAACC,MAAM,EAAE,QAAQ,CAAC,EAClB,CAACC,MAAM,EAAE,QAAQ,CAAC,EAClB,CAACC,OAAO,EAAE,SAAS,CAAC;AAEpB;AACA;AACA,CAACC,QAAQ,EAAE,UAAU,CAAC,EACtB,CAAC/C,SAAS,EAAE,WAAW,CAAC,CACzB;;AAED;AACA,MAAMK,WAAW,GAAG,SAAAA,CAACV,KAAK,EAAEC,OAAO,EAAoD;EAAA,IAAlDoD,aAAa,GAAAlD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAAEmD,MAAM,GAAAnD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,IAAEL,IAAI,GAAAK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAChF;EACA,IAAIF,OAAO,KAAKnB,KAAK,CAACuC,GAAG,EAAE;IACzB,OAAO,KAAK;EACd;;EAEA;EACA;EACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,YAAY,CAAC5C,MAAM,EAAE,EAAEmD,CAAC,EAAE;IAC5C,IAAItD,OAAO,KAAK+C,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC,IAAI,OAAOvD,KAAK,KAAKgD,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MACd;MAEA,OAAO;QACL1D,OAAO,cAAAE,MAAA,CAAciD,YAAY,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,YAAAxD,MAAA,CAAS0C,qBAAqB,CAACzC,KAAK,EAAE;UAAE0C,YAAY,EAAE;QAAK,CAAC,CAAC,CAAE;QACtG5C,IAAI,EAAE;MACR,CAAC;IACH;EACF;EAEA,IAAIG,OAAO,KAAK,IAAI,EAAE;IACpB,IAAID,KAAK,KAAK,IAAI,EAAE;MAClB,OAAO,KAAK;IACd;IAEA,OAAO;MACLH,OAAO,wBAAAE,MAAA,CAAwB0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;MAC7DF,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA,IAAI,OAAOG,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;IAC9F,IAAID,KAAK,KAAKC,OAAO,EAAE;MACrB,OAAO,KAAK;IACd;IAEA,OAAO;MACLJ,OAAO,cAAAE,MAAA,CAAcE,OAAO,YAAAF,MAAA,CAAS0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;MACnEF,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA,IAAIG,OAAO,KAAKnB,KAAK,CAAC4C,OAAO,EAAE;IAE7B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,OAAO1B,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,GAAG,CAAC,MAAMA,KAAK,EAAE;MACtD,OAAO,KAAK;IACd;IAEA,OAAO;MACLH,OAAO,2BAAAE,MAAA,CAA2B0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;MAChEF,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA,IAAIG,OAAO,KAAKX,MAAM,EAAE;IACtBW,OAAO,GAAGnB,KAAK,CAAC0C,eAAe,CAAC,CAAC,CAAC,CAAC;EACrC;;EAEA;EACA,IAAIvB,OAAO,YAAYU,KAAK,EAAE;IAC5B,IAAIV,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO;QACLP,OAAO,oDAAAE,MAAA,CAAoD0C,qBAAqB,CAACxC,OAAO,CAAC,CAAE;QAC3FH,IAAI,EAAE;MACR,CAAC;IACH;IAEA,IAAI,CAACa,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,IAAI,CAACwD,WAAW,CAACxD,KAAK,CAAC,EAAE;MAChD,OAAO;QACLH,OAAO,yBAAAE,MAAA,CAAyB0C,qBAAqB,CAACzC,KAAK,CAAC,CAAE;QAC9DF,IAAI,EAAE;MACR,CAAC;IACH;IAGA,KAAK,IAAIyD,CAAC,GAAG,CAAC,EAAEnD,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAEmD,CAAC,GAAGnD,MAAM,EAAEmD,CAAC,EAAE,EAAE;MACtD,MAAME,OAAO,MAAA1D,MAAA,CAAMD,IAAI,OAAAC,MAAA,CAAIwD,CAAC,MAAG;MAC/B,MAAM7D,MAAM,GAAGgB,WAAW,CAACV,KAAK,CAACuD,CAAC,CAAC,EAAEtD,OAAO,CAAC,CAAC,CAAC,EAAEoD,aAAa,EAAEC,MAAM,EAAEG,OAAO,CAAC;MAChF,IAAI/D,MAAM,EAAE;QACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGI,OAAO,GAAGF,CAAC,EAAE7D,MAAM,CAACI,IAAI,CAAC;QACpE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;QACjC,IAAI,OAAOM,KAAK,CAACuD,CAAC,CAAC,KAAK,QAAQ,IAAI7D,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;MACzE;IACF;IAEA,IAAI,CAAC2D,aAAa,EAAE,OAAO,KAAK;IAChC,OAAOC,MAAM,CAAClD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAGkD,MAAM;EAC7C;;EAEA;EACA;EACA,IAAIrD,OAAO,YAAYqB,KAAK,EAAE;IAC5B,IAAI5B,MAAM;IACV,IAAI;MACFA,MAAM,GAAGO,OAAO,CAACsB,SAAS,CAACvB,KAAK,CAAC;IACnC,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZ,IAAI,EAAEA,GAAG,YAAYb,KAAK,CAACc,KAAK,CAAC,EAAE;QACjC,MAAMD,GAAG;MACX;MAEA,OAAO;QACLE,OAAO,EAAEF,GAAG,CAACE,OAAO;QACpBC,IAAI,EAAEH,GAAG,CAACG;MACZ,CAAC;IACH;IAEA,IAAIJ,MAAM,EAAE;MACV,OAAO,KAAK;IACd;;IAEA;;IAEA,OAAO;MACLG,OAAO,EAAE,+BAA+B;MACxCC,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAIG,OAAO,YAAYe,KAAK,EAAE;IAC5Bf,OAAO,GAAGnB,KAAK,CAACmC,KAAK,CAACZ,SAAS,EAAE,IAAI,EAAEJ,OAAO,CAACA,OAAO,CAAC;EACzD,CAAC,MAAM,IAAIA,OAAO,YAAYc,QAAQ,EAAE;IACtCd,OAAO,GAAGnB,KAAK,CAACmC,KAAK,CAACZ,SAAS,EAAEJ,OAAO,CAACA,OAAO,CAAC;EACnD;EAEA,IAAIA,OAAO,YAAYgB,KAAK,EAAE;IAC5B,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,OAAO,CAACuC,OAAO,CAACpC,MAAM,EAAE,EAAEmD,CAAC,EAAE;MAC/C,MAAM7D,MAAM,GAAGgB,WAAW,CAACV,KAAK,EAAEC,OAAO,CAACuC,OAAO,CAACe,CAAC,CAAC,CAAC;MACrD,IAAI,CAAC7D,MAAM,EAAE;QAEX;QACA,OAAO,KAAK;MACd;;MAEA;IACF;;IAEA;IACA,OAAO;MACLG,OAAO,EAAE,8DAA8D;MACvEC,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA;EACA,IAAIG,OAAO,YAAYmD,QAAQ,EAAE;IAC/B,IAAIpD,KAAK,YAAYC,OAAO,EAAE;MAC5B,OAAO,KAAK;IACd;IAEA,OAAO;MACLJ,OAAO,cAAAE,MAAA,CAAcE,OAAO,CAAC8C,IAAI,IAAI,wBAAwB,CAAE;MAC/DjD,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAI8D,kBAAkB,GAAG,KAAK;EAC9B,IAAIC,iBAAiB;EACrB,IAAI5D,OAAO,YAAYuB,eAAe,EAAE;IACtCoC,kBAAkB,GAAG,IAAI;IACzB3D,OAAO,GAAGA,OAAO,CAACA,OAAO;EAC3B;EAEA,IAAIA,OAAO,YAAYwB,gBAAgB,EAAE;IACvCmC,kBAAkB,GAAG,IAAI;IACzBC,iBAAiB,GAAG,CAAC5D,OAAO,CAACA,OAAO,CAAC;IACrCA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAE;EACjB;EAEA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAO;MACLJ,OAAO,EAAE,mCAAmC;MAC5CC,IAAI,EAAE;IACR,CAAC;EACH;;EAEA;EACA;EACA;EACA,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO;MACLH,OAAO,0BAAAE,MAAA,CAA0B,OAAOC,KAAK,CAAE;MAC/CF,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAIE,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO;MACLH,OAAO,6BAA6B;MACpCC,IAAI,EAAE;IACR,CAAC;EACH;EAEA,IAAI,CAAEf,aAAa,CAACiB,KAAK,CAAC,EAAE;IAC1B,OAAO;MACLH,OAAO,yBAAyB;MAChCC,IAAI,EAAE;IACR,CAAC;EACH;EAEA,MAAMgE,gBAAgB,GAAGxE,MAAM,CAACyE,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMC,gBAAgB,GAAG1E,MAAM,CAACyE,MAAM,CAAC,IAAI,CAAC;EAE5CzE,MAAM,CAAC2E,IAAI,CAAChE,OAAO,CAAC,CAACiE,OAAO,CAACC,GAAG,IAAI;IAClC,MAAMC,UAAU,GAAGnE,OAAO,CAACkE,GAAG,CAAC;IAC/B,IAAIC,UAAU,YAAYrD,QAAQ,IAC9BqD,UAAU,YAAYpD,KAAK,EAAE;MAC/BgD,gBAAgB,CAACG,GAAG,CAAC,GAAGC,UAAU,CAACnE,OAAO;IAC5C,CAAC,MAAM;MACL6D,gBAAgB,CAACK,GAAG,CAAC,GAAGC,UAAU;IACpC;EACF,CAAC,CAAC;EAEF,KAAK,IAAID,GAAG,IAAI7E,MAAM,CAACU,KAAK,CAAC,EAAE;IAC7B,MAAMqE,QAAQ,GAAGrE,KAAK,CAACmE,GAAG,CAAC;IAC3B,MAAMG,OAAO,GAAGxE,IAAI,MAAAC,MAAA,CAAMD,IAAI,OAAAC,MAAA,CAAIoE,GAAG,IAAKA,GAAG;IAC7C,IAAI9E,MAAM,CAACkF,IAAI,CAACT,gBAAgB,EAAEK,GAAG,CAAC,EAAE;MACtC,MAAMzE,MAAM,GAAGgB,WAAW,CAAC2D,QAAQ,EAAEP,gBAAgB,CAACK,GAAG,CAAC,EAAEd,aAAa,EAAEC,MAAM,EAAEgB,OAAO,CAAC;MAC3F,IAAI5E,MAAM,EAAE;QACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGiB,OAAO,GAAGH,GAAG,EAAEzE,MAAM,CAACI,IAAI,CAAC;QACtE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;QACjC,IAAI,OAAO2E,QAAQ,KAAK,QAAQ,IAAI3E,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;MACzE;MAEA,OAAOoE,gBAAgB,CAACK,GAAG,CAAC;IAC9B,CAAC,MAAM,IAAI9E,MAAM,CAACkF,IAAI,CAACP,gBAAgB,EAAEG,GAAG,CAAC,EAAE;MAC7C,MAAMzE,MAAM,GAAGgB,WAAW,CAAC2D,QAAQ,EAAEL,gBAAgB,CAACG,GAAG,CAAC,EAAEd,aAAa,EAAEC,MAAM,EAAEgB,OAAO,CAAC;MAC3F,IAAI5E,MAAM,EAAE;QACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGiB,OAAO,GAAGH,GAAG,EAAEzE,MAAM,CAACI,IAAI,CAAC;QACtE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;QACjC,IAAI,OAAO2E,QAAQ,KAAK,QAAQ,IAAI3E,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;MACzE;IAEF,CAAC,MAAM;MACL,IAAI,CAACkE,kBAAkB,EAAE;QACvB,MAAMlE,MAAM,GAAG;UACbG,OAAO,EAAE,aAAa;UACtBC,IAAI,EAAEqE;QACR,CAAC;QACD,IAAI,CAACd,aAAa,EAAE,OAAO3D,MAAM;QACjC4D,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;MACrB;MAEA,IAAImE,iBAAiB,EAAE;QACrB,MAAMnE,MAAM,GAAGgB,WAAW,CAAC2D,QAAQ,EAAER,iBAAiB,CAAC,CAAC,CAAC,EAAER,aAAa,EAAEC,MAAM,EAAEgB,OAAO,CAAC;QAC1F,IAAI5E,MAAM,EAAE;UACVA,MAAM,CAACI,IAAI,GAAG4D,YAAY,CAACL,aAAa,GAAGiB,OAAO,GAAGH,GAAG,EAAEzE,MAAM,CAACI,IAAI,CAAC;UACtE,IAAI,CAACuD,aAAa,EAAE,OAAO3D,MAAM;UACjC,IAAI,OAAO2E,QAAQ,KAAK,QAAQ,IAAI3E,MAAM,CAACG,OAAO,EAAEyD,MAAM,CAACK,IAAI,CAACjE,MAAM,CAAC;QACzE;MACF;IACF;EACF;EAEA,MAAMuE,IAAI,GAAG3E,MAAM,CAAC2E,IAAI,CAACH,gBAAgB,CAAC;EAC1C,IAAIG,IAAI,CAAC7D,MAAM,EAAE;IACf,MAAMoE,kBAAkB,GAAGL,GAAG,KAAK;MACjCtE,OAAO,kBAAAE,MAAA,CAAkBoE,GAAG,MAAG;MAC/BrE,IAAI,EAAEuD,aAAa,GAAGvD,IAAI,GAAG;IAC/B,CAAC,CAAC;IAEF,IAAI,CAACuD,aAAa,EAAE;MAClB,OAAOmB,kBAAkB,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC;IAEA,KAAK,MAAME,GAAG,IAAIF,IAAI,EAAE;MACtBX,MAAM,CAACK,IAAI,CAACa,kBAAkB,CAACL,GAAG,CAAC,CAAC;IACtC;EACF;EAEA,IAAI,CAACd,aAAa,EAAE,OAAO,KAAK;EAChC,OAAOC,MAAM,CAAClD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAGkD,MAAM;AAC7C,CAAC;AAED,MAAMnB,eAAe,CAAC;EACpBI,WAAWA,CAAEpB,IAAI,EAAEe,WAAW,EAAE;IAE9B;IACA;IACA,IAAI,CAACf,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC;;IAErB;IACA;IACA;IACA,IAAI,CAACA,IAAI,CAACsD,OAAO,CAAC,CAAC;IACnB,IAAI,CAACvC,WAAW,GAAGA,WAAW;EAChC;EAEAzB,QAAQA,CAACT,KAAK,EAAE;IACd,IAAI,IAAI,CAAC0E,iBAAiB,CAAC1E,KAAK,CAAC,EAAE;MACjC;IACF;;IAEA;IACA;IACA;IACA,IAAIW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,IAAIwD,WAAW,CAACxD,KAAK,CAAC,EAAE;MAC9CW,KAAK,CAACpB,SAAS,CAAC2E,OAAO,CAACK,IAAI,CAACvE,KAAK,EAAE,IAAI,CAAC0E,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE;EACF;EAEAD,iBAAiBA,CAAC1E,KAAK,EAAE;IACvB,KAAK,IAAIuD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpC,IAAI,CAACf,MAAM,EAAE,EAAEmD,CAAC,EAAE;MAEzC;MACA;MACA;MACA;MACA,IAAIvD,KAAK,KAAK,IAAI,CAACmB,IAAI,CAACoC,CAAC,CAAC,IACrBL,MAAM,CAAC0B,KAAK,CAAC5E,KAAK,CAAC,IAAIkD,MAAM,CAAC0B,KAAK,CAAC,IAAI,CAACzD,IAAI,CAACoC,CAAC,CAAC,CAAE,EAAE;QACvD,IAAI,CAACpC,IAAI,CAAC0D,MAAM,CAACtB,CAAC,EAAE,CAAC,CAAC;QACtB,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEAjB,sCAAsCA,CAAA,EAAG;IACvC,IAAI,IAAI,CAACnB,IAAI,CAACf,MAAM,GAAG,CAAC,EACtB,MAAM,IAAIR,KAAK,yCAAAG,MAAA,CAAyC,IAAI,CAACmC,WAAW,CAAE,CAAC;EAC/E;AACF;AAEA,MAAM4C,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAC9E,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACvE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACtE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EACpE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAC3E,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAC3E,YAAY,CAAC;;AAEf;AACA;AACA,MAAMpB,YAAY,GAAGA,CAACS,GAAG,EAAEY,IAAI,KAAK;EAClC,IAAK,OAAOZ,GAAG,KAAM,QAAQ,IAAIA,GAAG,CAACa,KAAK,CAAC,UAAU,CAAC,EAAE;IACtDb,GAAG,OAAApE,MAAA,CAAOoE,GAAG,MAAG;EAClB,CAAC,MAAM,IAAI,CAACA,GAAG,CAACa,KAAK,CAAC,2BAA2B,CAAC,IACvCF,WAAW,CAACG,OAAO,CAACd,GAAG,CAAC,IAAI,CAAC,EAAE;IACxCA,GAAG,GAAGtB,IAAI,CAACD,SAAS,CAAC,CAACuB,GAAG,CAAC,CAAC;EAC7B;EAEA,IAAIY,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3B,UAAAhF,MAAA,CAAUoE,GAAG,OAAApE,MAAA,CAAIgF,IAAI;EACvB;EAEA,OAAOZ,GAAG,GAAGY,IAAI;AACnB,CAAC;AAED,MAAMG,QAAQ,GAAGlF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AAErE,MAAMmF,eAAe,GAAGC,IAAI,IAC1BF,QAAQ,CAACE,IAAI,CAAC,IACd9F,MAAM,CAACC,SAAS,CAAC8F,QAAQ,CAACd,IAAI,CAACa,IAAI,CAAC,KAAK,oBAAoB;AAE/D,MAAM5B,WAAW,GAAG2B,eAAe,CAAC,YAAW;EAAE,OAAOhF,SAAS;AAAE,CAAC,CAAC,CAAC,CAAC,GACrEgF,eAAe,GACfnF,KAAK,IAAIkF,QAAQ,CAAClF,KAAK,CAAC,IAAI,OAAOA,KAAK,CAACsF,MAAM,KAAK,UAAU,C;;;;;;;;;;;ACxkBhE3G,MAAM,CAACC,MAAM,CAAC;EAACG,aAAa,EAACA,CAAA,KAAIA;AAAa,CAAC,CAAC;AAAhD;;AAEA,MAAMwG,UAAU,GAAG,CAAC,CAAC;AAErB,MAAMF,QAAQ,GAAGE,UAAU,CAACF,QAAQ;AAEpC,MAAMhG,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,MAAMgG,UAAU,GAAGnG,MAAM,CAACgG,QAAQ;AAElC,MAAMI,oBAAoB,GAAGD,UAAU,CAACjB,IAAI,CAACjF,MAAM,CAAC;AAEpD,MAAMoG,QAAQ,GAAGpG,MAAM,CAACqG,cAAc;AAE/B,MAAM5G,aAAa,GAAG6G,GAAG,IAAI;EAClC,IAAIC,KAAK;EACT,IAAIC,IAAI;;EAER;EACA;EACA,IAAI,CAACF,GAAG,IAAIP,QAAQ,CAACd,IAAI,CAACqB,GAAG,CAAC,KAAK,iBAAiB,EAAE;IACpD,OAAO,KAAK;EACd;EAEAC,KAAK,GAAGH,QAAQ,CAACE,GAAG,CAAC;;EAErB;EACA,IAAI,CAACC,KAAK,EAAE;IACV,OAAO,IAAI;EACb;;EAEA;EACAC,IAAI,GAAGzG,MAAM,CAACkF,IAAI,CAACsB,KAAK,EAAE,aAAa,CAAC,IAAIA,KAAK,CAACtD,WAAW;EAC7D,OAAO,OAAOuD,IAAI,KAAK,UAAU,IAC/BN,UAAU,CAACjB,IAAI,CAACuB,IAAI,CAAC,KAAKL,oBAAoB;AAClD,CAAC,C", "file": "/packages/check.js", "sourcesContent": ["// XXX docs\nimport { isPlainObject } from './isPlainObject';\n\n// Things we explicitly do NOT support:\n//    - heterogenous arrays\n\nconst currentArgumentChecker = new Meteor.EnvironmentVariable;\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nconst format = result => {\n  const err = new Match.Error(result.message);\n  if (result.path) {\n    err.message += ` in field ${result.path}`;\n    err.path = result.path;\n  }\n\n  return err;\n}\n\n/**\n * @summary Check that a value matches a [pattern](#matchpatterns).\n * If the value does not match the pattern, throw a `Match.Error`.\n * By default, it will throw immediately at the first error encountered. Pass in { throwAllErrors: true } to throw all errors.\n *\n * Particularly useful to assert that arguments to a function have the right\n * types and structure.\n * @locus Anywhere\n * @param {Any} value The value to check\n * @param {MatchPattern} pattern The pattern to match `value` against\n * @param {Object} [options={}] Additional options for check\n * @param {Boolean} [options.throwAllErrors=false] If true, throw all errors\n */\nexport function check(value, pattern, options = { throwAllErrors: false }) {\n  // Record that check got called, if somebody cared.\n  //\n  // We use getOrNullIfOutsideFiber so that it's OK to call check()\n  // from non-Fiber server contexts; the downside is that if you forget to\n  // bindEnvironment on some random callback in your method/publisher,\n  // it might not find the argumentChecker and you'll get an error about\n  // not checking an argument that it looks like you're checking (instead\n  // of just getting a \"Node code must run in a Fiber\" error).\n  const argChecker = currentArgumentChecker.getOrNullIfOutsideFiber();\n  if (argChecker) {\n    argChecker.checking(value);\n  }\n\n  const result = testSubtree(value, pattern, options.throwAllErrors);\n\n  if (result) {\n    if (options.throwAllErrors) {\n      throw Array.isArray(result) ? result.map(r => format(r)) : [format(result)]\n    } else {\n      throw format(result)\n    }\n  }\n};\n\n/**\n * @namespace Match\n * @summary The namespace for all Match types and methods.\n */\nexport const Match = {\n  Optional: function(pattern) {\n    return new Optional(pattern);\n  },\n\n  Maybe: function(pattern) {\n    return new Maybe(pattern);\n  },\n\n  OneOf: function(...args) {\n    return new OneOf(args);\n  },\n\n  Any: ['__any__'],\n  Where: function(condition) {\n    return new Where(condition);\n  },\n\n  ObjectIncluding: function(pattern) {\n    return new ObjectIncluding(pattern)\n  },\n\n  ObjectWithValues: function(pattern) {\n    return new ObjectWithValues(pattern);\n  },\n\n  // Matches only signed 32-bit integers\n  Integer: ['__integer__'],\n\n  // XXX matchers should know how to describe themselves for errors\n  Error: Meteor.makeErrorType('Match.Error', function (msg) {\n    this.message = `Match error: ${msg}`;\n\n    // The path of the value that failed to match. Initially empty, this gets\n    // populated by catching and rethrowing the exception as it goes back up the\n    // stack.\n    // E.g.: \"vals[3].entity.created\"\n    this.path = '';\n\n    // If this gets sent over DDP, don't give full internal details but at least\n    // provide something better than 500 Internal server error.\n    this.sanitizedError = new Meteor.Error(400, 'Match failed');\n  }),\n\n  // Tests to see if value matches pattern. Unlike check, it merely returns true\n  // or false (unless an error other than Match.Error was thrown). It does not\n  // interact with _failIfArgumentsAreNotAllChecked.\n  // XXX maybe also implement a Match.match which returns more information about\n  //     failures but without using exception handling or doing what check()\n  //     does with _failIfArgumentsAreNotAllChecked and Meteor.Error conversion\n\n  /**\n   * @summary Returns true if the value matches the pattern.\n   * @locus Anywhere\n   * @param {Any} value The value to check\n   * @param {MatchPattern} pattern The pattern to match `value` against\n   */\n  test(value, pattern) {\n    return !testSubtree(value, pattern);\n  },\n\n  // Runs `f.apply(context, args)`. If check() is not called on every element of\n  // `args` (either directly or in the first level of an array), throws an error\n  // (using `description` in the message).\n  _failIfArgumentsAreNotAllChecked(f, context, args, description) {\n    const argChecker = new ArgumentChecker(args, description);\n    const result = currentArgumentChecker.withValue(\n      argChecker,\n      () => f.apply(context, args)\n    );\n\n    // If f didn't itself throw, make sure it checked all of its arguments.\n    argChecker.throwUnlessAllArgumentsHaveBeenChecked();\n    return result;\n  }\n};\n\nclass Optional {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass Maybe {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass OneOf {\n  constructor(choices) {\n    if (!choices || choices.length === 0) {\n      throw new Error('Must provide at least one choice to Match.OneOf');\n    }\n\n    this.choices = choices;\n  }\n}\n\nclass Where {\n  constructor(condition) {\n    this.condition = condition;\n  }\n}\n\nclass ObjectIncluding {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nclass ObjectWithValues {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n}\n\nconst stringForErrorMessage = (value, options = {}) => {\n  if ( value === null ) {\n    return 'null';\n  }\n\n  if ( options.onlyShowType ) {\n    return typeof value;\n  }\n\n  // Your average non-object things.  Saves from doing the try/catch below for.\n  if ( typeof value !== 'object' ) {\n    return EJSON.stringify(value)\n  }\n\n  try {\n\n    // Find objects with circular references since EJSON doesn't support them yet (Issue #4778 + Unaccepted PR)\n    // If the native stringify is going to choke, EJSON.stringify is going to choke too.\n    JSON.stringify(value);\n  } catch (stringifyError) {\n    if ( stringifyError.name === 'TypeError' ) {\n      return typeof value;\n    }\n  }\n\n  return EJSON.stringify(value);\n};\n\nconst typeofChecks = [\n  [String, 'string'],\n  [Number, 'number'],\n  [Boolean, 'boolean'],\n\n  // While we don't allow undefined/function in EJSON, this is good for optional\n  // arguments with OneOf.\n  [Function, 'function'],\n  [undefined, 'undefined'],\n];\n\n// Return `false` if it matches. Otherwise, returns an object with a `message` and a `path` field or an array of objects each with a `message` and a `path` field when collecting errors.\nconst testSubtree = (value, pattern, collectErrors = false, errors = [], path = '') => {\n  // Match anything!\n  if (pattern === Match.Any) {\n    return false;\n  }\n\n  // Basic atomic types.\n  // Do not match boxed objects (e.g. String, Boolean)\n  for (let i = 0; i < typeofChecks.length; ++i) {\n    if (pattern === typeofChecks[i][0]) {\n      if (typeof value === typeofChecks[i][1]) {\n        return false;\n      }\n\n      return {\n        message: `Expected ${typeofChecks[i][1]}, got ${stringForErrorMessage(value, { onlyShowType: true })}`,\n        path: '',\n      };\n    }\n  }\n\n  if (pattern === null) {\n    if (value === null) {\n      return false;\n    }\n\n    return {\n      message: `Expected null, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // Strings, numbers, and booleans match literally. Goes well with Match.OneOf.\n  if (typeof pattern === 'string' || typeof pattern === 'number' || typeof pattern === 'boolean') {\n    if (value === pattern) {\n      return false;\n    }\n\n    return {\n      message: `Expected ${pattern}, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // Match.Integer is special type encoded with array\n  if (pattern === Match.Integer) {\n\n    // There is no consistent and reliable way to check if variable is a 64-bit\n    // integer. One of the popular solutions is to get reminder of division by 1\n    // but this method fails on really large floats with big precision.\n    // E.g.: 1.348192308491824e+23 % 1 === 0 in V8\n    // Bitwise operators work consistantly but always cast variable to 32-bit\n    // signed integer according to JavaScript specs.\n    if (typeof value === 'number' && (value | 0) === value) {\n      return false;\n    }\n\n    return {\n      message: `Expected Integer, got ${stringForErrorMessage(value)}`,\n      path: '',\n    };\n  }\n\n  // 'Object' is shorthand for Match.ObjectIncluding({});\n  if (pattern === Object) {\n    pattern = Match.ObjectIncluding({});\n  }\n\n  // Array (checked AFTER Any, which is implemented as an Array).\n  if (pattern instanceof Array) {\n    if (pattern.length !== 1) {\n      return {\n        message: `Bad pattern: arrays must have one type element ${stringForErrorMessage(pattern)}`,\n        path: '',\n      };\n    }\n\n    if (!Array.isArray(value) && !isArguments(value)) {\n      return {\n        message: `Expected array, got ${stringForErrorMessage(value)}`,\n        path: '',\n      };\n    }\n\n\n    for (let i = 0, length = value.length; i < length; i++) {\n      const arrPath = `${path}[${i}]`\n      const result = testSubtree(value[i], pattern[0], collectErrors, errors, arrPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? arrPath : i, result.path)\n        if (!collectErrors) return result;\n        if (typeof value[i] !== 'object' || result.message) errors.push(result)\n      }\n    }\n\n    if (!collectErrors) return false;\n    return errors.length === 0 ? false : errors;\n  }\n\n  // Arbitrary validation checks. The condition can return false or throw a\n  // Match.Error (ie, it can internally use check()) to fail.\n  if (pattern instanceof Where) {\n    let result;\n    try {\n      result = pattern.condition(value);\n    } catch (err) {\n      if (!(err instanceof Match.Error)) {\n        throw err;\n      }\n\n      return {\n        message: err.message,\n        path: err.path\n      };\n    }\n\n    if (result) {\n      return false;\n    }\n\n    // XXX this error is terrible\n\n    return {\n      message: 'Failed Match.Where validation',\n      path: '',\n    };\n  }\n\n  if (pattern instanceof Maybe) {\n    pattern = Match.OneOf(undefined, null, pattern.pattern);\n  } else if (pattern instanceof Optional) {\n    pattern = Match.OneOf(undefined, pattern.pattern);\n  }\n\n  if (pattern instanceof OneOf) {\n    for (let i = 0; i < pattern.choices.length; ++i) {\n      const result = testSubtree(value, pattern.choices[i]);\n      if (!result) {\n\n        // No error? Yay, return.\n        return false;\n      }\n\n      // Match errors just mean try another choice.\n    }\n\n    // XXX this error is terrible\n    return {\n      message: 'Failed Match.OneOf, Match.Maybe or Match.Optional validation',\n      path: '',\n    };\n  }\n\n  // A function that isn't something we special-case is assumed to be a\n  // constructor.\n  if (pattern instanceof Function) {\n    if (value instanceof pattern) {\n      return false;\n    }\n\n    return {\n      message: `Expected ${pattern.name || 'particular constructor'}`,\n      path: '',\n    };\n  }\n\n  let unknownKeysAllowed = false;\n  let unknownKeyPattern;\n  if (pattern instanceof ObjectIncluding) {\n    unknownKeysAllowed = true;\n    pattern = pattern.pattern;\n  }\n\n  if (pattern instanceof ObjectWithValues) {\n    unknownKeysAllowed = true;\n    unknownKeyPattern = [pattern.pattern];\n    pattern = {};  // no required keys\n  }\n\n  if (typeof pattern !== 'object') {\n    return {\n      message: 'Bad pattern: unknown pattern type',\n      path: '',\n    };\n  }\n\n  // An object, with required and optional keys. Note that this does NOT do\n  // structural matches against objects of special types that happen to match\n  // the pattern: this really needs to be a plain old {Object}!\n  if (typeof value !== 'object') {\n    return {\n      message: `Expected object, got ${typeof value}`,\n      path: '',\n    };\n  }\n\n  if (value === null) {\n    return {\n      message: `Expected object, got null`,\n      path: '',\n    };\n  }\n\n  if (! isPlainObject(value)) {\n    return {\n      message: `Expected plain object`,\n      path: '',\n    };\n  }\n\n  const requiredPatterns = Object.create(null);\n  const optionalPatterns = Object.create(null);\n\n  Object.keys(pattern).forEach(key => {\n    const subPattern = pattern[key];\n    if (subPattern instanceof Optional ||\n        subPattern instanceof Maybe) {\n      optionalPatterns[key] = subPattern.pattern;\n    } else {\n      requiredPatterns[key] = subPattern;\n    }\n  });\n\n  for (let key in Object(value)) {\n    const subValue = value[key];\n    const objPath = path ? `${path}.${key}` : key;\n    if (hasOwn.call(requiredPatterns, key)) {\n      const result = testSubtree(subValue, requiredPatterns[key], collectErrors, errors, objPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? objPath : key, result.path)\n        if (!collectErrors) return result;\n        if (typeof subValue !== 'object' || result.message) errors.push(result);\n      }\n\n      delete requiredPatterns[key];\n    } else if (hasOwn.call(optionalPatterns, key)) {\n      const result = testSubtree(subValue, optionalPatterns[key], collectErrors, errors, objPath);\n      if (result) {\n        result.path = _prependPath(collectErrors ? objPath : key, result.path)\n        if (!collectErrors) return result;\n        if (typeof subValue !== 'object' || result.message) errors.push(result);\n      }\n\n    } else {\n      if (!unknownKeysAllowed) {\n        const result = {\n          message: 'Unknown key',\n          path: key,\n        };\n        if (!collectErrors) return result;\n        errors.push(result);\n      }\n\n      if (unknownKeyPattern) {\n        const result = testSubtree(subValue, unknownKeyPattern[0], collectErrors, errors, objPath);\n        if (result) {\n          result.path = _prependPath(collectErrors ? objPath : key, result.path)\n          if (!collectErrors) return result;\n          if (typeof subValue !== 'object' || result.message) errors.push(result);\n        }\n      }\n    }\n  }\n\n  const keys = Object.keys(requiredPatterns);\n  if (keys.length) {\n    const createMissingError = key => ({\n      message: `Missing key '${key}'`,\n      path: collectErrors ? path : '',\n    });\n\n    if (!collectErrors) {\n      return createMissingError(keys[0]);\n    }\n\n    for (const key of keys) {\n      errors.push(createMissingError(key));\n    }\n  }\n\n  if (!collectErrors) return false;\n  return errors.length === 0 ? false : errors;\n};\n\nclass ArgumentChecker {\n  constructor (args, description) {\n\n    // Make a SHALLOW copy of the arguments. (We'll be doing identity checks\n    // against its contents.)\n    this.args = [...args];\n\n    // Since the common case will be to check arguments in order, and we splice\n    // out arguments when we check them, make it so we splice out from the end\n    // rather than the beginning.\n    this.args.reverse();\n    this.description = description;\n  }\n\n  checking(value) {\n    if (this._checkingOneValue(value)) {\n      return;\n    }\n\n    // Allow check(arguments, [String]) or check(arguments.slice(1), [String])\n    // or check([foo, bar], [String]) to count... but only if value wasn't\n    // itself an argument.\n    if (Array.isArray(value) || isArguments(value)) {\n      Array.prototype.forEach.call(value, this._checkingOneValue.bind(this));\n    }\n  }\n\n  _checkingOneValue(value) {\n    for (let i = 0; i < this.args.length; ++i) {\n\n      // Is this value one of the arguments? (This can have a false positive if\n      // the argument is an interned primitive, but it's still a good enough\n      // check.)\n      // (NaN is not === to itself, so we have to check specially.)\n      if (value === this.args[i] ||\n          (Number.isNaN(value) && Number.isNaN(this.args[i]))) {\n        this.args.splice(i, 1);\n        return true;\n      }\n    }\n    return false;\n  }\n\n  throwUnlessAllArgumentsHaveBeenChecked() {\n    if (this.args.length > 0)\n      throw new Error(`Did not check() all arguments during ${this.description}`);\n  }\n}\n\nconst _jsKeywords = ['do', 'if', 'in', 'for', 'let', 'new', 'try', 'var', 'case',\n  'else', 'enum', 'eval', 'false', 'null', 'this', 'true', 'void', 'with',\n  'break', 'catch', 'class', 'const', 'super', 'throw', 'while', 'yield',\n  'delete', 'export', 'import', 'public', 'return', 'static', 'switch',\n  'typeof', 'default', 'extends', 'finally', 'package', 'private', 'continue',\n  'debugger', 'function', 'arguments', 'interface', 'protected', 'implements',\n  'instanceof'];\n\n// Assumes the base of path is already escaped properly\n// returns key + base\nconst _prependPath = (key, base) => {\n  if ((typeof key) === 'number' || key.match(/^[0-9]+$/)) {\n    key = `[${key}]`;\n  } else if (!key.match(/^[a-z_$][0-9a-z_$.[\\]]*$/i) ||\n             _jsKeywords.indexOf(key) >= 0) {\n    key = JSON.stringify([key]);\n  }\n\n  if (base && base[0] !== '[') {\n    return `${key}.${base}`;\n  }\n\n  return key + base;\n}\n\nconst isObject = value => typeof value === 'object' && value !== null;\n\nconst baseIsArguments = item =>\n  isObject(item) &&\n  Object.prototype.toString.call(item) === '[object Arguments]';\n\nconst isArguments = baseIsArguments(function() { return arguments; }()) ?\n  baseIsArguments :\n  value => isObject(value) && typeof value.callee === 'function';\n", "// Copy of jQuery.isPlainObject for the server side from jQuery v3.1.1.\n\nconst class2type = {};\n\nconst toString = class2type.toString;\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nconst fnToString = hasOwn.toString;\n\nconst ObjectFunctionString = fnToString.call(Object);\n\nconst getProto = Object.getPrototypeOf;\n\nexport const isPlainObject = obj => {\n  let proto;\n  let Ctor;\n\n  // Detect obvious negatives\n  // Use toString instead of jQuery.type to catch host objects\n  if (!obj || toString.call(obj) !== '[object Object]') {\n    return false;\n  }\n\n  proto = getProto(obj);\n\n  // Objects with no prototype (e.g., `Object.create( null )`) are plain\n  if (!proto) {\n    return true;\n  }\n\n  // Objects with prototype are plain iff they were constructed by a global Object function\n  Ctor = hasOwn.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor === 'function' && \n    fnToString.call(Ctor) === ObjectFunctionString;\n};\n"]}