{"version": 3, "sources": ["meteor://💻app/packages/accounts-base/client_main.js", "meteor://💻app/packages/accounts-base/accounts_client.js", "meteor://💻app/packages/accounts-base/accounts_common.js"], "names": ["module", "export", "Accounts", "AccountsClient", "AccountsTest", "default", "exports", "link", "v", "runSetters", "_Meteor$settings", "Meteor", "settings", "_Meteor$settings$publ", "public", "_Meteor$settings$publ2", "packages", "accounts", "users", "_objectSpread", "Accounts<PERSON><PERSON><PERSON>", "constructor", "options", "_loggingIn", "ReactiveVar", "_loggingOut", "_loginServicesHandle", "connection", "subscribe", "_pageLoadLoginCallbacks", "_pageLoadLoginAttemptInfo", "savedHash", "window", "location", "hash", "_initUrlMatching", "initStorageLocation", "_initLocalStorage", "_loginFuncs", "_loginCallbacksCalled", "_Meteor$settings$publ3", "storageLocation", "clientStorage", "sessionStorage", "_localStorage", "config", "userId", "_setLoggingIn", "x", "set", "loggingIn", "get", "loggingOut", "registerClientLoginFunction", "funcName", "func", "Error", "concat", "callLoginFunction", "_len", "arguments", "length", "funcArgs", "Array", "_key", "apply", "applyLoginFunction", "logout", "callback", "applyAsync", "wait", "then", "result", "makeClientLoggedOut", "catch", "e", "logoutOtherClients", "err", "_storeLoginToken", "token", "tokenExpires", "callLoginMethod", "methodName", "methodArguments", "_suppressLoggingIn", "for<PERSON>ach", "f", "called", "loginCallbacks", "_ref", "error", "loginDetails", "_on<PERSON>ogin<PERSON><PERSON>", "_onLoginFailureHook", "userCallback", "reconnected", "onResultReceived", "_reconnectStopper", "stop", "DDP", "onReconnect", "conn", "storedToken", "_storedLoginToken", "_storedLoginTokenExpires", "_tokenExpiration", "Date", "_tokenExpiresSoon", "resume", "storedTokenNow", "loggedInAndDataReadyCallback", "validateResult", "makeClientLoggedIn", "id", "Tracker", "autorun", "computation", "user", "withComputation", "userAsync", "_userId", "_onLogoutHook", "each", "_unstoreLoginToken", "setUserId", "loginServicesConfigured", "ready", "onPageLoadLogin", "push", "_pageLoadLogin", "attemptInfo", "_debug", "_startupCallback", "setTimeout", "type", "loginWithToken", "_enableAutoLogin", "_autoLoginEnabled", "_pollStoredLoginToken", "_isolateLoginTokenForTest", "LOGIN_TOKEN_KEY", "Random", "USER_ID_KEY", "setItem", "LOGIN_TOKEN_EXPIRES_KEY", "_last<PERSON><PERSON>in<PERSON><PERSON><PERSON>henPolled", "removeItem", "getItem", "_storedUserId", "_unstoreLoginTokenIfExpiresSoon", "rootUrlPathPrefix", "__meteor_runtime_config__", "ROOT_URL_PATH_PREFIX", "namespace", "_stream", "rawUrl", "allowed", "_pollIntervalTimer", "clearInterval", "setInterval", "currentLoginToken", "_accountsCallbacks", "_attemptToMatchHash", "attemptToMatchHash", "defaultSuccessHandler", "onResetPasswordLink", "onEmailVerificationLink", "onEnrollmentLink", "Package", "blaze", "Template", "Blaze", "registerHelper", "urlPart", "startup", "success", "tokenRegex", "RegExp", "match", "_resetPasswordToken", "_verifyEmailToken", "_enrollAccountToken", "call", "EXPIRE_TOKENS_INTERVAL_MS", "VALID_CONFIG_KEYS", "key", "Object", "keys", "includes", "console", "_options", "undefined", "_initConnection", "_initializeCollection", "Hook", "bindEnvironment", "debugPrintExceptions", "DEFAULT_LOGIN_EXPIRATION_DAYS", "LOGIN_UNEXPIRING_TOKEN_DAYS", "lceName", "LoginCancelledError", "makeErrorType", "description", "message", "prototype", "name", "numericError", "collection", "Mongo", "Collection", "collectionName", "_preventAutopublish", "_addDefaultFieldSelector", "defaultFieldSelector", "fields", "keys2", "isServer", "warn", "join", "self", "findOne", "isClient", "findOneAsync", "accountsConfigCalled", "hasOwnProperty", "OAuthEncryption", "loadKey", "oauth<PERSON><PERSON><PERSON>", "isTest", "_name", "onLogin", "ret", "register", "onLoginFailure", "onLogout", "ddpUrl", "connect", "ACCOUNTS_CONNECTION_URL", "_getTokenLifetimeMs", "loginExpirationInDays", "loginExpiration", "_getPasswordResetTokenLifetimeMs", "passwordResetTokenExpiration", "passwordResetTokenExpirationInDays", "DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS", "_getPasswordEnrollTokenLifetimeMs", "passwordEnrollTokenExpiration", "passwordEnrollTokenExpirationInDays", "DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS", "when", "getTime", "minLifetimeMs", "minLifetimeCapMs", "MIN_TOKEN_LIFETIME_CAP_SECS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,MAAM,CAACC,MAAM,CAAC;EAACC,QAAQ,EAACA,CAAA,KAAIA,QAAQ;EAACC,cAAc,EAACA,CAAA,KAAIA,cAAc;EAACC,YAAY,EAACA,CAAA,KAAIA,YAAY;EAACC,OAAO,EAACA,CAAA,KAAIC;AAAO,CAAC,CAAC;AAAC,IAAIH,cAAc,EAACC,YAAY;AAACJ,MAAM,CAACO,IAAI,CAAC,sBAAsB,EAAC;EAACJ,cAAcA,CAACK,CAAC,EAAC;IAACL,cAAc,GAACK,CAAC;EAAA,CAAC;EAACJ,YAAYA,CAACI,CAAC,EAAC;IAACJ,YAAY,GAACI,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAKtQ;AACA;AACA;AACA;AACAR,MAAA,CAAAS,UAAA,CAAAP,QAAQ,GAAG,IAAIC,cAAc,CAAC,EAAAO,gBAAA,GAAAC,MAAM,CAACC,QAAQ,cAAAF,gBAAA,wBAAAG,qBAAA,GAAfH,gBAAA,CAAiBI,MAAM,cAAAD,qBAAA,wBAAAE,sBAAA,GAAvBF,qBAAA,CAAyBG,QAAQ,cAAAD,sBAAA,uBAAjCA,sBAAA,CAAmCE,QAAQ,KAAI,CAAC,CAAC,CAAC;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACAN,MAAM,CAACO,KAAK,GAAGhB,QAAQ,CAACgB,KAAK,C;;;;;;;;;;;ACjB7B,IAAIC,aAAa;AAACnB,MAAM,CAACO,IAAI,CAAC,sCAAsC,EAAC;EAACF,OAAOA,CAACG,CAAC,EAAC;IAACW,aAAa,GAACX,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGR,MAAM,CAACC,MAAM,CAAC;EAACE,cAAc,EAACA,CAAA,KAAIA,cAAc;EAACC,YAAY,EAACA,CAAA,KAAIA;AAAY,CAAC,CAAC;AAAC,IAAIgB,cAAc;AAACpB,MAAM,CAACO,IAAI,CAAC,sBAAsB,EAAC;EAACa,cAAcA,CAACZ,CAAC,EAAC;IAACY,cAAc,GAACZ,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAaxK,MAAML,cAAc,SAASiB,cAAc,CAAC;EACjDC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAAC;IAEd,IAAI,CAACC,UAAU,GAAG,IAAIC,WAAW,CAAC,KAAK,CAAC;IACxC,IAAI,CAACC,WAAW,GAAG,IAAID,WAAW,CAAC,KAAK,CAAC;IAEzC,IAAI,CAACE,oBAAoB,GACvB,IAAI,CAACC,UAAU,CAACC,SAAS,CAAC,kCAAkC,CAAC;IAE/D,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACC,yBAAyB,GAAG,IAAI;IAErC,IAAI,CAACC,SAAS,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;IACrC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACC,mBAAmB,CAAC,CAAC;;IAE1B;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;;IAExB;IACA,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;;IAErB;IACA;IACA,IAAI,CAACC,qBAAqB,GAAG,KAAK;EACpC;EAEAH,mBAAmBA,CAACd,OAAO,EAAE;IAAA,IAAAZ,gBAAA,EAAAG,qBAAA,EAAAE,sBAAA,EAAAyB,sBAAA;IAC3B;IACA,IAAI,CAACC,eAAe,GAAI,CAAAnB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoB,aAAa,MAAK,SAAS,IAAI,EAAAhC,gBAAA,GAAAC,MAAM,CAACC,QAAQ,cAAAF,gBAAA,wBAAAG,qBAAA,GAAfH,gBAAA,CAAiBI,MAAM,cAAAD,qBAAA,wBAAAE,sBAAA,GAAvBF,qBAAA,CAAyBG,QAAQ,cAAAD,sBAAA,wBAAAyB,sBAAA,GAAjCzB,sBAAA,CAAmCE,QAAQ,cAAAuB,sBAAA,uBAA3CA,sBAAA,CAA6CE,aAAa,MAAK,SAAS,GAAIV,MAAM,CAACW,cAAc,GAAGhC,MAAM,CAACiC,aAAa;EAC1L;EAEAC,MAAMA,CAACvB,OAAO,EAAE;IACd,KAAK,CAACuB,MAAM,CAACvB,OAAO,CAAC;IAErB,IAAI,CAACc,mBAAmB,CAACd,OAAO,CAAC;EACnC;;EAEA;EACA;EACA;;EAEA;EACAwB,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnB,UAAU,CAACmB,MAAM,CAAC,CAAC;EACjC;;EAEA;EACA;EACA;EACAC,aAAaA,CAACC,CAAC,EAAE;IACf,IAAI,CAACzB,UAAU,CAAC0B,GAAG,CAACD,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACEE,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC3B,UAAU,CAAC4B,GAAG,CAAC,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;EACEC,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC3B,WAAW,CAAC0B,GAAG,CAAC,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,2BAA2BA,CAACC,QAAQ,EAAEC,IAAI,EAAE;IAC1C,IAAI,IAAI,CAACjB,WAAW,CAACgB,QAAQ,CAAC,EAAE;MAC9B,MAAM,IAAIE,KAAK,IAAAC,MAAA,CAAIH,QAAQ,8BAA2B,CAAC;IACzD;IACA,IAAI,CAAChB,WAAW,CAACgB,QAAQ,CAAC,GAAGC,IAAI;EACnC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEG,iBAAiBA,CAACJ,QAAQ,EAAe;IACvC,IAAI,CAAC,IAAI,CAAChB,WAAW,CAACgB,QAAQ,CAAC,EAAE;MAC/B,MAAM,IAAIE,KAAK,IAAAC,MAAA,CAAIH,QAAQ,qBAAkB,CAAC;IAChD;IAAC,SAAAK,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAH4BC,QAAQ,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAARF,QAAQ,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAIrC,OAAO,IAAI,CAAC1B,WAAW,CAACgB,QAAQ,CAAC,CAACW,KAAK,CAAC,IAAI,EAAEH,QAAQ,CAAC;EACzD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEI,kBAAkBA,CAACZ,QAAQ,EAAEQ,QAAQ,EAAE;IACrC,IAAI,CAAC,IAAI,CAACxB,WAAW,CAACgB,QAAQ,CAAC,EAAE;MAC/B,MAAM,IAAIE,KAAK,IAAAC,MAAA,CAAIH,QAAQ,qBAAkB,CAAC;IAChD;IACA,OAAO,IAAI,CAAChB,WAAW,CAACgB,QAAQ,CAAC,CAACW,KAAK,CAAC,IAAI,EAAEH,QAAQ,CAAC;EACzD;;EAEA;AACF;AACA;AACA;AACA;EACEK,MAAMA,CAACC,QAAQ,EAAE;IACf,IAAI,CAAC3C,WAAW,CAACwB,GAAG,CAAC,IAAI,CAAC;IAE1B,IAAI,CAACtB,UAAU,CAAC0C,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE;MACvC;MACAC,IAAI,EAAE;IACR,CAAC,CAAC,CACCC,IAAI,CAAEC,MAAM,IAAK;MAChB,IAAI,CAAC/C,WAAW,CAACwB,GAAG,CAAC,KAAK,CAAC;MAC3B,IAAI,CAACV,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACkC,mBAAmB,CAAC,CAAC;MAC1BL,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACxB,CAAC,CAAC,CACDM,KAAK,CAAEC,CAAC,IAAK;MACZ,IAAI,CAAClD,WAAW,CAACwB,GAAG,CAAC,KAAK,CAAC;MAC3BmB,QAAQ,IAAIA,QAAQ,CAACO,CAAC,CAAC;IACzB,CAAC,CAAC;EACN;;EAEA;AACF;AACA;AACA;AACA;EACEC,kBAAkBA,CAACR,QAAQ,EAAE;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACzC,UAAU,CAACsC,KAAK,CACnB,aAAa,EACb,EAAE,EACF;MAAEK,IAAI,EAAE;IAAK,CAAC,EACd,CAACO,GAAG,EAAEL,MAAM,KAAK;MACf,IAAI,CAAEK,GAAG,EAAE;QACT,IAAI,CAACC,gBAAgB,CACnB,IAAI,CAAChC,MAAM,CAAC,CAAC,EACb0B,MAAM,CAACO,KAAK,EACZP,MAAM,CAACQ,YACT,CAAC;MACH;IACF,CACF,CAAC;IAED,IAAI,CAACrD,UAAU,CAACsC,KAAK,CACnB,mBAAmB,EACnB,EAAE,EACF;MAAEK,IAAI,EAAE;IAAK,CAAC,EACdO,GAAG,IAAIT,QAAQ,IAAIA,QAAQ,CAACS,GAAG,CACjC,CAAC;EACH;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAI,eAAeA,CAAC3D,OAAO,EAAE;IACvBA,OAAO,GAAAH,aAAA;MACL+D,UAAU,EAAE,OAAO;MACnBC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC;MACrBC,kBAAkB,EAAE;IAAK,GACtB9D,OAAO,CACX;;IAED;IACA;IACA,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC+D,OAAO,CAACC,CAAC,IAAI;MAC9C,IAAI,CAAChE,OAAO,CAACgE,CAAC,CAAC,EACbhE,OAAO,CAACgE,CAAC,CAAC,GAAG,MAAM,IAAI;IAC3B,CAAC,CAAC;IAEF,IAAIC,MAAM;IACV;IACA,MAAMC,cAAc,GAAGC,IAAA,IAA6B;MAAA,IAA5B;QAAEC,KAAK;QAAEC;MAAa,CAAC,GAAAF,IAAA;MAC7C,IAAI,CAACF,MAAM,EAAE;QACXA,MAAM,GAAG,IAAI;QACb,IAAI,CAACG,KAAK,EAAE;UACV,IAAI,CAACE,YAAY,CAACP,OAAO,CAACjB,QAAQ,IAAI;YACpCA,QAAQ,CAACuB,YAAY,CAAC;YACtB,OAAO,IAAI;UACb,CAAC,CAAC;UACF,IAAI,CAACpD,qBAAqB,GAAG,IAAI;QACnC,CAAC,MAAM;UACL,IAAI,CAACA,qBAAqB,GAAG,KAAK;UAClC,IAAI,CAACsD,mBAAmB,CAACR,OAAO,CAACjB,QAAQ,IAAI;YAC3CA,QAAQ,CAAC;cAAEsB;YAAM,CAAC,CAAC;YACnB,OAAO,IAAI;UACb,CAAC,CAAC;QACJ;QACApE,OAAO,CAACwE,YAAY,CAACJ,KAAK,EAAEC,YAAY,CAAC;MAC3C;IACF,CAAC;IAED,IAAII,WAAW,GAAG,KAAK;;IAEvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,gBAAgB,GAAGA,CAACnB,GAAG,EAAEL,MAAM,KAAK;MACxC,IAAIK,GAAG,IAAI,CAACL,MAAM,IAAI,CAACA,MAAM,CAACO,KAAK,EAAE;QACnC;QACA;QACA;MAAA,CACD,MAAM;QACL;QACA;QACA;QACA,IAAI,IAAI,CAACkB,iBAAiB,EAAE;UAC1B,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAC,CAAC;QAC/B;QAEA,IAAI,CAACD,iBAAiB,GAAGE,GAAG,CAACC,WAAW,CAACC,IAAI,IAAI;UAC/C,IAAIA,IAAI,IAAI,IAAI,CAAC1E,UAAU,EAAE;YAC3B;UACF;UACAoE,WAAW,GAAG,IAAI;UAClB;UACA,MAAMO,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;UAC5C,IAAID,WAAW,EAAE;YACf9B,MAAM,GAAG;cACPO,KAAK,EAAEuB,WAAW;cAClBtB,YAAY,EAAE,IAAI,CAACwB,wBAAwB,CAAC;YAC9C,CAAC;UACH;UACA,IAAI,CAAChC,MAAM,CAACQ,YAAY,EACtBR,MAAM,CAACQ,YAAY,GAAG,IAAI,CAACyB,gBAAgB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;UACzD,IAAI,IAAI,CAACC,iBAAiB,CAACnC,MAAM,CAACQ,YAAY,CAAC,EAAE;YAC/C,IAAI,CAACP,mBAAmB,CAAC,CAAC;UAC5B,CAAC,MAAM;YACL,IAAI,CAACQ,eAAe,CAAC;cACnBE,eAAe,EAAE,CAAC;gBAACyB,MAAM,EAAEpC,MAAM,CAACO;cAAK,CAAC,CAAC;cACzC;cACA;cACA;cACAK,kBAAkB,EAAE,IAAI;cACxBU,YAAY,EAAEA,CAACJ,KAAK,EAAEC,YAAY,KAAK;gBACrC,MAAMkB,cAAc,GAAG,IAAI,CAACN,iBAAiB,CAAC,CAAC;gBAC/C,IAAIb,KAAK,EAAE;kBACT;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA,IAAImB,cAAc,IAAIA,cAAc,KAAKrC,MAAM,CAACO,KAAK,EAAE;oBACrD,IAAI,CAACN,mBAAmB,CAAC,CAAC;kBAC5B;gBACF;gBACA;gBACA;gBACA;gBACAe,cAAc,CAAC;kBAAEE,KAAK;kBAAEC;gBAAa,CAAC,CAAC;cACzC;YAAC,CAAC,CAAC;UACP;QACF,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACA;IACA;IACA,MAAMmB,4BAA4B,GAAGA,CAACpB,KAAK,EAAElB,MAAM,KAAK;MACtD;MACA;MACA;MACA;MACA;MACA,IAAIuB,WAAW,EACb;;MAEF;MACA;MACA;MACA,IAAIL,KAAK,IAAI,CAAClB,MAAM,EAAE;QACpBkB,KAAK,GAAGA,KAAK,IAAI,IAAIlC,KAAK,2BAAAC,MAAA,CACEnC,OAAO,CAAC4D,UAAU,CAC9C,CAAC;QACDM,cAAc,CAAC;UAAEE;QAAM,CAAC,CAAC;QACzB,IAAI,CAAC3C,aAAa,CAAC,KAAK,CAAC;QACzB;MACF;MACA,IAAI;QACFzB,OAAO,CAACyF,cAAc,CAACvC,MAAM,CAAC;MAChC,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVa,cAAc,CAAC;UAAEE,KAAK,EAAEf;QAAE,CAAC,CAAC;QAC5B,IAAI,CAAC5B,aAAa,CAAC,KAAK,CAAC;QACzB;MACF;;MAEA;MACA,IAAI,CAACiE,kBAAkB,CAACxC,MAAM,CAACyC,EAAE,EAAEzC,MAAM,CAACO,KAAK,EAAEP,MAAM,CAACQ,YAAY,CAAC;;MAErE;MACAkC,OAAO,CAACC,OAAO,CAAC,MAAOC,WAAW,IAAK;QACrC,MAAMC,IAAI,GAAG,MAAMH,OAAO,CAACI,eAAe,CAACF,WAAW,EAAE,MACtDzG,MAAM,CAAC4G,SAAS,CAAC,CACnB,CAAC;QAED,IAAIF,IAAI,EAAE;UACR7B,cAAc,CAAC;YAAEG,YAAY,EAAEnB;UAAO,CAAC,CAAC;UACxC,IAAI,CAACzB,aAAa,CAAC,KAAK,CAAC;UACzBqE,WAAW,CAAClB,IAAI,CAAC,CAAC;QACpB;MACF,CAAC,CAAC;IAEJ,CAAC;IAED,IAAI,CAAC5E,OAAO,CAAC8D,kBAAkB,EAAE;MAC/B,IAAI,CAACrC,aAAa,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACpB,UAAU,CAAC0C,UAAU,CACxB/C,OAAO,CAAC4D,UAAU,EAClB5D,OAAO,CAAC6D,eAAe,EACvB;MAAEb,IAAI,EAAE,IAAI;MAAE0B;IAAiB,CAAC,EAChCc,4BAA4B,CAAC;EACjC;EAEArC,mBAAmBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAAC9C,UAAU,CAAC6F,OAAO,EAAE;MAC3B,IAAI,CAACC,aAAa,CAACC,IAAI,CAACtD,QAAQ,IAAI;QAClCA,QAAQ,CAAC,CAAC;QACV,OAAO,IAAI;MACb,CAAC,CAAC;IACJ;IACA,IAAI,CAACuD,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAChG,UAAU,CAACiG,SAAS,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAC3B,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAC,CAAC;EACzD;EAEAc,kBAAkBA,CAAClE,MAAM,EAAEiC,KAAK,EAAEC,YAAY,EAAE;IAC9C,IAAI,CAACF,gBAAgB,CAAChC,MAAM,EAAEiC,KAAK,EAAEC,YAAY,CAAC;IAClD,IAAI,CAACrD,UAAU,CAACiG,SAAS,CAAC9E,MAAM,CAAC;EACnC;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA+E,uBAAuBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACnG,oBAAoB,CAACoG,KAAK,CAAC,CAAC;EAC1C;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACAC,eAAeA,CAACzC,CAAC,EAAE;IACjB,IAAI,IAAI,CAACxD,yBAAyB,EAAE;MAClCwD,CAAC,CAAC,IAAI,CAACxD,yBAAyB,CAAC;IACnC,CAAC,MAAM;MACL,IAAI,CAACD,uBAAuB,CAACmG,IAAI,CAAC1C,CAAC,CAAC;IACtC;EACF;EAEA;EACA;EACA;EACA;EACA2C,cAAcA,CAACC,WAAW,EAAE;IAC1B,IAAI,IAAI,CAACpG,yBAAyB,EAAE;MAClCnB,MAAM,CAACwH,MAAM,CACX,4DACF,CAAC;MACD;IACF;IAEA,IAAI,CAACtG,uBAAuB,CAACwD,OAAO,CAACjB,QAAQ,IAAIA,QAAQ,CAAC8D,WAAW,CAAC,CAAC;IACvE,IAAI,CAACrG,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACC,yBAAyB,GAAGoG,WAAW;EAC9C;;EAEA;EACA;EACA;EACA;EACAE,gBAAgBA,CAAChE,QAAQ,EAAE;IACzB;IACA,IAAI,IAAI,CAAC7B,qBAAqB,EAAE;MAC9B;MACA;MACA;MACA;MACA5B,MAAM,CAAC0H,UAAU,CAAC,MAAMjE,QAAQ,CAAC;QAAEkE,IAAI,EAAE;MAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1D;EACF;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEAC,cAAcA,CAACxD,KAAK,EAAEX,QAAQ,EAAE;IAC9B,IAAI,CAACa,eAAe,CAAC;MACnBE,eAAe,EAAE,CAAC;QAChByB,MAAM,EAAE7B;MACV,CAAC,CAAC;MACFe,YAAY,EAAE1B;IAChB,CAAC,CAAC;EACJ;EAEA;EACA;EACAoE,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACAC,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,GAAGC,MAAM,CAAC5B,EAAE,CAAC,CAAC;IACzD,IAAI,CAAC6B,WAAW,GAAG,IAAI,CAACA,WAAW,GAAGD,MAAM,CAAC5B,EAAE,CAAC,CAAC;EACnD;EAEAnC,gBAAgBA,CAAChC,MAAM,EAAEiC,KAAK,EAAEC,YAAY,EAAE;IAC5C,IAAI,CAACvC,eAAe,CAACsG,OAAO,CAAC,IAAI,CAACD,WAAW,EAAEhG,MAAM,CAAC;IACtD,IAAI,CAACL,eAAe,CAACsG,OAAO,CAAC,IAAI,CAACH,eAAe,EAAE7D,KAAK,CAAC;IACzD,IAAI,CAAEC,YAAY,EAChBA,YAAY,GAAG,IAAI,CAACyB,gBAAgB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAClD,IAAI,CAACjE,eAAe,CAACsG,OAAO,CAAC,IAAI,CAACC,uBAAuB,EAAEhE,YAAY,CAAC;;IAExE;IACA;IACA,IAAI,CAACiE,yBAAyB,GAAGlE,KAAK;EACxC;EAEA4C,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAClF,eAAe,CAACyG,UAAU,CAAC,IAAI,CAACJ,WAAW,CAAC;IACjD,IAAI,CAACrG,eAAe,CAACyG,UAAU,CAAC,IAAI,CAACN,eAAe,CAAC;IACrD,IAAI,CAACnG,eAAe,CAACyG,UAAU,CAAC,IAAI,CAACF,uBAAuB,CAAC;;IAE7D;IACA;IACA,IAAI,CAACC,yBAAyB,GAAG,IAAI;EACvC;EAEA;EACA;EACA1C,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9D,eAAe,CAAC0G,OAAO,CAAC,IAAI,CAACP,eAAe,CAAC;EAC3D;EAEApC,wBAAwBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAAC/D,eAAe,CAAC0G,OAAO,CAAC,IAAI,CAACH,uBAAuB,CAAC;EACnE;EAEAI,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3G,eAAe,CAAC0G,OAAO,CAAC,IAAI,CAACL,WAAW,CAAC;EACvD;EAEAO,+BAA+BA,CAAA,EAAG;IAChC,MAAMrE,YAAY,GAAG,IAAI,CAACwB,wBAAwB,CAAC,CAAC;IACpD,IAAIxB,YAAY,IAAI,IAAI,CAAC2B,iBAAiB,CAAC,IAAID,IAAI,CAAC1B,YAAY,CAAC,CAAC,EAAE;MAClE,IAAI,CAAC2C,kBAAkB,CAAC,CAAC;IAC3B;EACF;EAEA;EACA;EACA;;EAEAtF,iBAAiBA,CAAA,EAAG;IAClB;IACA,IAAI,CAACuG,eAAe,GAAG,mBAAmB;IAC1C,IAAI,CAACI,uBAAuB,GAAG,0BAA0B;IACzD,IAAI,CAACF,WAAW,GAAG,eAAe;IAElC,MAAMQ,iBAAiB,GAAGC,yBAAyB,CAACC,oBAAoB;IACxE,IAAIF,iBAAiB,IAAI,IAAI,CAAC3H,UAAU,KAAKhB,MAAM,CAACgB,UAAU,EAAE;MAC9D;MACA;MACA;MACA;MACA;MACA;MACA,IAAI8H,SAAS,OAAAhG,MAAA,CAAO,IAAI,CAAC9B,UAAU,CAAC+H,OAAO,CAACC,MAAM,CAAE;MACpD,IAAIL,iBAAiB,EAAE;QACrBG,SAAS,QAAAhG,MAAA,CAAQ6F,iBAAiB,CAAE;MACtC;MACA,IAAI,CAACV,eAAe,IAAIa,SAAS;MACjC,IAAI,CAACT,uBAAuB,IAAIS,SAAS;MACzC,IAAI,CAACX,WAAW,IAAIW,SAAS;IAC/B;IAEA,IAAI1E,KAAK;IACT,IAAI,IAAI,CAAC0D,iBAAiB,EAAE;MAC1B;MACA;MACA,IAAI,CAACY,+BAA+B,CAAC,CAAC;MACtCtE,KAAK,GAAG,IAAI,CAACwB,iBAAiB,CAAC,CAAC;MAChC,IAAIxB,KAAK,EAAE;QACT;QACA;QACA,MAAMjC,MAAM,GAAG,IAAI,CAACsG,aAAa,CAAC,CAAC;QACnCtG,MAAM,IAAI,IAAI,CAACnB,UAAU,CAACiG,SAAS,CAAC9E,MAAM,CAAC;QAC3C,IAAI,CAACyF,cAAc,CAACxD,KAAK,EAAEF,GAAG,IAAI;UAChC,IAAIA,GAAG,EAAE;YACPlE,MAAM,CAACwH,MAAM,iCAAA1E,MAAA,CAAiCoB,GAAG,CAAE,CAAC;YACpD,IAAI,CAACJ,mBAAmB,CAAC,CAAC;UAC5B;UAEA,IAAI,CAACwD,cAAc,CAAC;YAClBK,IAAI,EAAE,QAAQ;YACdsB,OAAO,EAAE,CAAC/E,GAAG;YACba,KAAK,EAAEb,GAAG;YACVK,UAAU,EAAE,OAAO;YACnB;YACA;YACA;YACAC,eAAe,EAAE,CAAC;cAACyB,MAAM,EAAE7B;YAAK,CAAC;UACnC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;;IAEA;IACA;IACA,IAAI,CAACkE,yBAAyB,GAAGlE,KAAK;IAEtC,IAAI,IAAI,CAAC8E,kBAAkB,EAAE;MAC3B;MACA;MACAC,aAAa,CAAC,IAAI,CAACD,kBAAkB,CAAC;IACxC;IAEA,IAAI,CAACA,kBAAkB,GAAGE,WAAW,CAAC,MAAM;MAC1C,IAAI,CAACrB,qBAAqB,CAAC,CAAC;IAC9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAE,IAAI,CAACD,iBAAiB,EAAE;MAC5B;IACF;IAEA,MAAMuB,iBAAiB,GAAG,IAAI,CAACzD,iBAAiB,CAAC,CAAC;;IAElD;IACA,IAAI,IAAI,CAAC0C,yBAAyB,IAAIe,iBAAiB,EAAE;MACvD,IAAIA,iBAAiB,EAAE;QACrB,IAAI,CAACzB,cAAc,CAACyB,iBAAiB,EAAGnF,GAAG,IAAK;UAC9C,IAAIA,GAAG,EAAE;YACP,IAAI,CAACJ,mBAAmB,CAAC,CAAC;UAC5B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACN,MAAM,CAAC,CAAC;MACf;IACF;IAEA,IAAI,CAAC8E,yBAAyB,GAAGe,iBAAiB;EACpD;EAEA;EACA;EACA;;EAEA7H,gBAAgBA,CAAA,EAAG;IACjB;IACA,IAAI,CAACsG,iBAAiB,GAAG,IAAI;;IAE7B;IACA,IAAI,CAACwB,kBAAkB,GAAG,CAAC,CAAC;;IAE5B;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B;EAEA;EACAA,mBAAmBA,CAAA,EAAG;IACpBC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAACpI,SAAS,EAAEqI,qBAAqB,CAAC;EACjE;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,mBAAmBA,CAACjG,QAAQ,EAAE;IAC5B,IAAI,IAAI,CAAC6F,kBAAkB,CAAC,gBAAgB,CAAC,EAAE;MAC7CtJ,MAAM,CAACwH,MAAM,CAAC,0DAA0D,GACtE,2CAA2C,CAAC;IAChD;IAEA,IAAI,CAAC8B,kBAAkB,CAAC,gBAAgB,CAAC,GAAG7F,QAAQ;EACtD;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkG,uBAAuBA,CAAClG,QAAQ,EAAE;IAChC,IAAI,IAAI,CAAC6F,kBAAkB,CAAC,cAAc,CAAC,EAAE;MAC3CtJ,MAAM,CAACwH,MAAM,CAAC,8DAA8D,GAC1E,2CAA2C,CAAC;IAChD;IAEA,IAAI,CAAC8B,kBAAkB,CAAC,cAAc,CAAC,GAAG7F,QAAQ;EACpD;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmG,gBAAgBA,CAACnG,QAAQ,EAAE;IACzB,IAAI,IAAI,CAAC6F,kBAAkB,CAAC,gBAAgB,CAAC,EAAE;MAC7CtJ,MAAM,CAACwH,MAAM,CAAC,uDAAuD,GACnE,2CAA2C,CAAC;IAChD;IAEA,IAAI,CAAC8B,kBAAkB,CAAC,gBAAgB,CAAC,GAAG7F,QAAQ;EACtD;AAEF;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACAzD,MAAM,CAACuC,SAAS,GAAG,MAAMhD,QAAQ,CAACgD,SAAS,CAAC,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACAvC,MAAM,CAACyC,UAAU,GAAG,MAAMlD,QAAQ,CAACkD,UAAU,CAAC,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACAzC,MAAM,CAACwD,MAAM,GAAGC,QAAQ,IAAIlE,QAAQ,CAACiE,MAAM,CAACC,QAAQ,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACAzD,MAAM,CAACiE,kBAAkB,GAAGR,QAAQ,IAAIlE,QAAQ,CAAC0E,kBAAkB,CAACR,QAAQ,CAAC;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAzD,MAAM,CAAC4H,cAAc,GAAG,CAACxD,KAAK,EAAEX,QAAQ,KACtClE,QAAQ,CAACqI,cAAc,CAACxD,KAAK,EAAEX,QAAQ,CAAC;;AAE1C;AACA;AACA;;AAEA;AACA;AACA,IAAIoG,OAAO,CAACC,KAAK,EAAE;EACjB,MAAM;IAAEC;EAAS,CAAC,GAAGF,OAAO,CAACC,KAAK,CAACE,KAAK;;EAExC;AACF;AACA;AACA;AACA;AACA;EACED,QAAQ,CAACE,cAAc,CAAC,aAAa,EAAE,MAAMjK,MAAM,CAAC0G,IAAI,CAAC,CAAC,CAAC;;EAE3D;EACA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEqD,QAAQ,CAACE,cAAc,CAAC,WAAW,EAAE,MAAMjK,MAAM,CAACuC,SAAS,CAAC,CAAC,CAAC;;EAE9D;AACF;AACA;AACA;AACA;AACA;EACEwH,QAAQ,CAACE,cAAc,CAAC,YAAY,EAAE,MAAMjK,MAAM,CAACyC,UAAU,CAAC,CAAC,CAAC;;EAEhE;AACF;AACA;AACA;AACA;AACA;EACEsH,QAAQ,CAACE,cAAc,CACrB,gBAAgB,EAChB,MAAMjK,MAAM,CAACuC,SAAS,CAAC,CAAC,IAAIvC,MAAM,CAACyC,UAAU,CAAC,CAChD,CAAC;AACH;AAEA,MAAMgH,qBAAqB,GAAG,SAAAA,CAASrF,KAAK,EAAE8F,OAAO,EAAE;EACrD;EACA,IAAI,CAACpC,iBAAiB,GAAG,KAAK;;EAE9B;EACA9H,MAAM,CAACmK,OAAO,CAAC,MAAM;IACnB;IACA,IAAI,IAAI,CAACb,kBAAkB,CAACY,OAAO,CAAC,EAAE;MACpC,IAAI,CAACZ,kBAAkB,CAACY,OAAO,CAAC,CAAC9F,KAAK,EAAE,MAAM,IAAI,CAACyD,gBAAgB,CAAC,CAAC,CAAC;IACxE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA,MAAM2B,kBAAkB,GAAGA,CAAClJ,QAAQ,EAAEiB,IAAI,EAAE6I,OAAO,KAAK;EACtD;EACA,CAAC,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC1F,OAAO,CAACwF,OAAO,IAAI;IACtE,IAAI9F,KAAK;IAET,MAAMiG,UAAU,GAAG,IAAIC,MAAM,WAAAxH,MAAA,CAAWoH,OAAO,aAAU,CAAC;IAC1D,MAAMK,KAAK,GAAGhJ,IAAI,CAACgJ,KAAK,CAACF,UAAU,CAAC;IAEpC,IAAIE,KAAK,EAAE;MACTnG,KAAK,GAAGmG,KAAK,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAIL,OAAO,KAAK,gBAAgB,EAAE;QAChC5J,QAAQ,CAACkK,mBAAmB,GAAGpG,KAAK;MACtC,CAAC,MAAM,IAAI8F,OAAO,KAAK,cAAc,EAAE;QACrC5J,QAAQ,CAACmK,iBAAiB,GAAGrG,KAAK;MACpC,CAAC,MAAM,IAAI8F,OAAO,KAAK,gBAAgB,EAAE;QACvC5J,QAAQ,CAACoK,mBAAmB,GAAGtG,KAAK;MACtC;IACF,CAAC,MAAM;MACL;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA/C,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,EAAE;;IAEzB;IACA6I,OAAO,CAACO,IAAI,CAACrK,QAAQ,EAAE8D,KAAK,EAAE8F,OAAO,CAAC;EACxC,CAAC,CAAC;AACJ,CAAC;;AAED;AACO,MAAMzK,YAAY,GAAG;EAC1B+J,kBAAkB,EAAEA,CAACjI,IAAI,EAAE6I,OAAO,KAChCZ,kBAAkB,CAACjK,QAAQ,EAAEgC,IAAI,EAAE6I,OAAO;AAC9C,CAAC,C;;;;;;;;;;;ACv5BD,IAAI5J,aAAa;AAACnB,MAAM,CAACO,IAAI,CAAC,sCAAsC,EAAC;EAACF,OAAOA,CAACG,CAAC,EAAC;IAACW,aAAa,GAACX,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAArGR,MAAM,CAACC,MAAM,CAAC;EAACmB,cAAc,EAACA,CAAA,KAAIA,cAAc;EAACmK,yBAAyB,EAACA,CAAA,KAAIA;AAAyB,CAAC,CAAC;AAAC,IAAI5K,MAAM;AAACX,MAAM,CAACO,IAAI,CAAC,eAAe,EAAC;EAACI,MAAMA,CAACH,CAAC,EAAC;IAACG,MAAM,GAACH,CAAC;EAAA;AAAC,CAAC,EAAC,CAAC,CAAC;AAE1K;AACA,MAAMgL,iBAAiB,GAAG,CACxB,uBAAuB,EACvB,6BAA6B,EAC7B,+BAA+B,EAC/B,iBAAiB,EACjB,uBAAuB,EACvB,gBAAgB,EAChB,oCAAoC,EACpC,8BAA8B,EAC9B,qCAAqC,EACrC,+BAA+B,EAC/B,wBAAwB,EACxB,cAAc,EACd,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,YAAY,EACZ,2BAA2B,EAC3B,qBAAqB,EACrB,eAAe,EACf,QAAQ,EACR,YAAY,CACb;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMpK,cAAc,CAAC;EAC1BC,WAAWA,CAACC,OAAO,EAAE;IACnB;IACA,KAAK,MAAMmK,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACrK,OAAO,CAAC,EAAE;MACtC,IAAI,CAACkK,iBAAiB,CAACI,QAAQ,CAACH,GAAG,CAAC,EAAE;QACpCI,OAAO,CAACnG,KAAK,kCAAAjC,MAAA,CAAkCgI,GAAG,CAAE,CAAC;MACvD;IACF;;IAEA;IACA;IACA,IAAI,CAACK,QAAQ,GAAGxK,OAAO,IAAI,CAAC,CAAC;;IAE7B;IACA;IACA,IAAI,CAACK,UAAU,GAAGoK,SAAS;IAC3B,IAAI,CAACC,eAAe,CAAC1K,OAAO,IAAI,CAAC,CAAC,CAAC;;IAEnC;IACA;IACA,IAAI,CAACJ,KAAK,GAAG,IAAI,CAAC+K,qBAAqB,CAAC3K,OAAO,IAAI,CAAC,CAAC,CAAC;;IAEtD;IACA,IAAI,CAACsE,YAAY,GAAG,IAAIsG,IAAI,CAAC;MAC3BC,eAAe,EAAE,KAAK;MACtBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;IAEF,IAAI,CAACvG,mBAAmB,GAAG,IAAIqG,IAAI,CAAC;MAClCC,eAAe,EAAE,KAAK;MACtBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;IAEF,IAAI,CAAC3E,aAAa,GAAG,IAAIyE,IAAI,CAAC;MAC5BC,eAAe,EAAE,KAAK;MACtBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;;IAE9D;IACA;IACA,MAAMC,OAAO,GAAG,8BAA8B;IAC9C,IAAI,CAACC,mBAAmB,GAAG7L,MAAM,CAAC8L,aAAa,CAACF,OAAO,EAAE,UACvDG,WAAW,EACX;MACA,IAAI,CAACC,OAAO,GAAGD,WAAW;IAC5B,CAAC,CAAC;IACF,IAAI,CAACF,mBAAmB,CAACI,SAAS,CAACC,IAAI,GAAGN,OAAO;;IAEjD;IACA;IACA;IACA,IAAI,CAACC,mBAAmB,CAACM,YAAY,GAAG,SAAS;EACnD;EAEAb,qBAAqBA,CAAC3K,OAAO,EAAE;IAC7B,IAAIA,OAAO,CAACyL,UAAU,IAAI,OAAOzL,OAAO,CAACyL,UAAU,KAAK,QAAQ,IAAI,EAAEzL,OAAO,CAACyL,UAAU,YAAYC,KAAK,CAACC,UAAU,CAAC,EAAE;MACrH,MAAM,IAAItM,MAAM,CAAC6C,KAAK,CAAC,uEAAuE,CAAC;IACjG;IAEA,IAAI0J,cAAc,GAAG,OAAO;IAC5B,IAAI,OAAO5L,OAAO,CAACyL,UAAU,KAAK,QAAQ,EAAE;MAC1CG,cAAc,GAAG5L,OAAO,CAACyL,UAAU;IACrC;IAEA,IAAIA,UAAU;IACd,IAAIzL,OAAO,CAACyL,UAAU,YAAYC,KAAK,CAACC,UAAU,EAAE;MAClDF,UAAU,GAAGzL,OAAO,CAACyL,UAAU;IACjC,CAAC,MAAM;MACLA,UAAU,GAAG,IAAIC,KAAK,CAACC,UAAU,CAACC,cAAc,EAAE;QAChDC,mBAAmB,EAAE,IAAI;QACzBxL,UAAU,EAAE,IAAI,CAACA;MACnB,CAAC,CAAC;IACJ;IAEA,OAAOoL,UAAU;EACnB;;EAEA;AACF;AACA;AACA;EACEjK,MAAMA,CAAA,EAAG;IACP,MAAM,IAAIU,KAAK,CAAC,+BAA+B,CAAC;EAClD;;EAEA;EACA4J,wBAAwBA,CAAA,EAAe;IAAA,IAAd9L,OAAO,GAAAsC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAmI,SAAA,GAAAnI,SAAA,MAAG,CAAC,CAAC;IACnC;IACA,IAAI,CAAC,IAAI,CAACkI,QAAQ,CAACuB,oBAAoB,EAAE,OAAO/L,OAAO;;IAEvD;IACA,IAAI,CAACA,OAAO,CAACgM,MAAM,EACjB,OAAAnM,aAAA,CAAAA,aAAA,KACKG,OAAO;MACVgM,MAAM,EAAE,IAAI,CAACxB,QAAQ,CAACuB;IAAoB;;IAG9C;IACA,MAAM1B,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACrK,OAAO,CAACgM,MAAM,CAAC;IACxC,IAAI,CAAC3B,IAAI,CAAC9H,MAAM,EAAE,OAAOvC,OAAO;;IAEhC;IACA;IACA,IAAI,CAAC,CAACA,OAAO,CAACgM,MAAM,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,OAAOrK,OAAO;;IAE7C;IACA;IACA,MAAMiM,KAAK,GAAG7B,MAAM,CAACC,IAAI,CAAC,IAAI,CAACG,QAAQ,CAACuB,oBAAoB,CAAC;IAC7D,OAAO,IAAI,CAACvB,QAAQ,CAACuB,oBAAoB,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,GAC/CjM,OAAO,GAAAH,aAAA,CAAAA,aAAA,KAEFG,OAAO;MACVgM,MAAM,EAAAnM,aAAA,CAAAA,aAAA,KACDG,OAAO,CAACgM,MAAM,GACd,IAAI,CAACxB,QAAQ,CAACuB,oBAAoB;IACtC,EACF;EACP;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEhG,IAAIA,CAAC/F,OAAO,EAAE;IACZ,IAAIX,MAAM,CAAC6M,QAAQ,EAAE;MACnB3B,OAAO,CAAC4B,IAAI,CAAC,CACX,mDAAmD,EACnD,qDAAqD,EACrD,uCAAuC,CACxC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf;IAEA,MAAMC,IAAI,GAAG,IAAI;IACjB,MAAM7K,MAAM,GAAG6K,IAAI,CAAC7K,MAAM,CAAC,CAAC;IAC5B,MAAM8K,OAAO,GAAG,SAAAA,CAAA;MAAA,OAAajN,MAAM,CAACkN,QAAQ,GACxCF,IAAI,CAACzM,KAAK,CAAC0M,OAAO,CAAC,GAAAhK,SAAO,CAAC,GAC3B+J,IAAI,CAACzM,KAAK,CAAC4M,YAAY,CAAC,GAAAlK,SAAO,CAAC;IAAA;IACpC,OAAOd,MAAM,GACT8K,OAAO,CAAC9K,MAAM,EAAE,IAAI,CAACsK,wBAAwB,CAAC9L,OAAO,CAAC,CAAC,GACvD,IAAI;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMiG,SAASA,CAACjG,OAAO,EAAE;IACvB,MAAMwB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;IAC5B,OAAOA,MAAM,GACT,IAAI,CAAC5B,KAAK,CAAC4M,YAAY,CAAChL,MAAM,EAAE,IAAI,CAACsK,wBAAwB,CAAC9L,OAAO,CAAC,CAAC,GACvE,IAAI;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEuB,MAAMA,CAACvB,OAAO,EAAE;IACd;IACA;IACA;IACA;IACA;IACA,IAAIX,MAAM,CAAC6M,QAAQ,EAAE;MACnBjE,yBAAyB,CAACwE,oBAAoB,GAAG,IAAI;IACvD,CAAC,MAAM,IAAI,CAACxE,yBAAyB,CAACwE,oBAAoB,EAAE;MAC1D;MACA;MACApN,MAAM,CAACwH,MAAM,CACX,0DAA0D,GACxD,yDACJ,CAAC;IACH;;IAEA;IACA;IACA;IACA,IAAIuD,MAAM,CAACkB,SAAS,CAACoB,cAAc,CAAC1C,IAAI,CAAChK,OAAO,EAAE,gBAAgB,CAAC,EAAE;MACnE,IAAIX,MAAM,CAACkN,QAAQ,EAAE;QACnB,MAAM,IAAIrK,KAAK,CACb,+DACF,CAAC;MACH;MACA,IAAI,CAACgH,OAAO,CAAC,kBAAkB,CAAC,EAAE;QAChC,MAAM,IAAIhH,KAAK,CACb,mEACF,CAAC;MACH;MACAgH,OAAO,CAAC,kBAAkB,CAAC,CAACyD,eAAe,CAACC,OAAO,CACjD5M,OAAO,CAAC6M,cACV,CAAC;MACD7M,OAAO,GAAAH,aAAA,KAAQG,OAAO,CAAE;MACxB,OAAOA,OAAO,CAAC6M,cAAc;IAC/B;;IAEA;IACA,KAAK,MAAM1C,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACrK,OAAO,CAAC,EAAE;MACtC,IAAI,CAACkK,iBAAiB,CAACI,QAAQ,CAACH,GAAG,CAAC,EAAE;QACpCI,OAAO,CAACnG,KAAK,kCAAAjC,MAAA,CAAkCgI,GAAG,CAAE,CAAC;MACvD;IACF;;IAEA;IACA,KAAK,MAAMA,GAAG,IAAID,iBAAiB,EAAE;MACnC,IAAIC,GAAG,IAAInK,OAAO,EAAE;QAClB,IAAImK,GAAG,IAAI,IAAI,CAACK,QAAQ,EAAE;UACxB,IAAIL,GAAG,KAAK,YAAY,IAAK9K,MAAM,CAACyN,MAAM,IAAI3C,GAAG,KAAK,eAAgB,EAAE;YACtE,MAAM,IAAI9K,MAAM,CAAC6C,KAAK,eAAAC,MAAA,CAAgBgI,GAAG,qBAAmB,CAAC;UAC/D;QACF;QACA,IAAI,CAACK,QAAQ,CAACL,GAAG,CAAC,GAAGnK,OAAO,CAACmK,GAAG,CAAC;MACnC;IACF;IAEA,IAAInK,OAAO,CAACyL,UAAU,IAAIzL,OAAO,CAACyL,UAAU,KAAK,IAAI,CAAC7L,KAAK,CAACmN,KAAK,IAAI/M,OAAO,CAACyL,UAAU,KAAK,IAAI,CAAC7L,KAAK,EAAE;MACtG,IAAI,CAACA,KAAK,GAAG,IAAI,CAAC+K,qBAAqB,CAAC3K,OAAO,CAAC;IAClD;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEgN,OAAOA,CAAC/K,IAAI,EAAE;IACZ,IAAIgL,GAAG,GAAG,IAAI,CAAC3I,YAAY,CAAC4I,QAAQ,CAACjL,IAAI,CAAC;IAC1C;IACA,IAAI,CAAC6E,gBAAgB,CAACmG,GAAG,CAACnK,QAAQ,CAAC;IACnC,OAAOmK,GAAG;EACZ;;EAEA;AACF;AACA;AACA;AACA;EACEE,cAAcA,CAAClL,IAAI,EAAE;IACnB,OAAO,IAAI,CAACsC,mBAAmB,CAAC2I,QAAQ,CAACjL,IAAI,CAAC;EAChD;;EAEA;AACF;AACA;AACA;AACA;EACEmL,QAAQA,CAACnL,IAAI,EAAE;IACb,OAAO,IAAI,CAACkE,aAAa,CAAC+G,QAAQ,CAACjL,IAAI,CAAC;EAC1C;EAEAyI,eAAeA,CAAC1K,OAAO,EAAE;IACvB,IAAI,CAACX,MAAM,CAACkN,QAAQ,EAAE;MACpB;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIvM,OAAO,CAACK,UAAU,EAAE;MACtB,IAAI,CAACA,UAAU,GAAGL,OAAO,CAACK,UAAU;IACtC,CAAC,MAAM,IAAIL,OAAO,CAACqN,MAAM,EAAE;MACzB,IAAI,CAAChN,UAAU,GAAGwE,GAAG,CAACyI,OAAO,CAACtN,OAAO,CAACqN,MAAM,CAAC;IAC/C,CAAC,MAAM,IACL,OAAOpF,yBAAyB,KAAK,WAAW,IAChDA,yBAAyB,CAACsF,uBAAuB,EACjD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAClN,UAAU,GAAGwE,GAAG,CAACyI,OAAO,CAC3BrF,yBAAyB,CAACsF,uBAC5B,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAAClN,UAAU,GAAGhB,MAAM,CAACgB,UAAU;IACrC;EACF;EAEAmN,mBAAmBA,CAAA,EAAG;IACpB;IACA;IACA;IACA,MAAMC,qBAAqB,GACzB,IAAI,CAACjD,QAAQ,CAACiD,qBAAqB,KAAK,IAAI,GACxCzC,2BAA2B,GAC3B,IAAI,CAACR,QAAQ,CAACiD,qBAAqB;IACzC,OACE,IAAI,CAACjD,QAAQ,CAACkD,eAAe,IAC7B,CAACD,qBAAqB,IAAI1C,6BAA6B,IAAI,QAAQ;EAEvE;EAEA4C,gCAAgCA,CAAA,EAAG;IACjC,OACE,IAAI,CAACnD,QAAQ,CAACoD,4BAA4B,IAC1C,CAAC,IAAI,CAACpD,QAAQ,CAACqD,kCAAkC,IAC/CC,4CAA4C,IAAI,QAAQ;EAE9D;EAEAC,iCAAiCA,CAAA,EAAG;IAClC,OACE,IAAI,CAACvD,QAAQ,CAACwD,6BAA6B,IAC3C,CAAC,IAAI,CAACxD,QAAQ,CAACyD,mCAAmC,IAChDC,6CAA6C,IAAI,QAAQ;EAE/D;EAEA/I,gBAAgBA,CAACgJ,IAAI,EAAE;IACrB;IACA;IACA,OAAO,IAAI/I,IAAI,CAAC,IAAIA,IAAI,CAAC+I,IAAI,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAACZ,mBAAmB,CAAC,CAAC,CAAC;EACxE;EAEAnI,iBAAiBA,CAAC8I,IAAI,EAAE;IACtB,IAAIE,aAAa,GAAG,GAAG,GAAG,IAAI,CAACb,mBAAmB,CAAC,CAAC;IACpD,MAAMc,gBAAgB,GAAGC,2BAA2B,GAAG,IAAI;IAC3D,IAAIF,aAAa,GAAGC,gBAAgB,EAAE;MACpCD,aAAa,GAAGC,gBAAgB;IAClC;IACA,OAAO,IAAIlJ,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAAC+I,IAAI,CAAC,GAAGE,aAAa;EACpD;;EAEA;EACAvH,gBAAgBA,CAAChE,QAAQ,EAAE,CAAC;AAC9B;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACAzD,MAAM,CAACmC,MAAM,GAAG,MAAM5C,QAAQ,CAAC4C,MAAM,CAAC,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACAnC,MAAM,CAAC0G,IAAI,GAAG/F,OAAO,IAAIpB,QAAQ,CAACmH,IAAI,CAAC/F,OAAO,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACAX,MAAM,CAAC4G,SAAS,GAAGjG,OAAO,IAAIpB,QAAQ,CAACqH,SAAS,CAACjG,OAAO,CAAC;;AAEzD;AACA,MAAM+K,6BAA6B,GAAG,EAAE;AACxC;AACA,MAAM+C,4CAA4C,GAAG,CAAC;AACtD;AACA,MAAMI,6CAA6C,GAAG,EAAE;AACxD;AACA;AACA;AACA,MAAMK,2BAA2B,GAAG,IAAI,CAAC,CAAC;AAC1C;AACO,MAAMtE,yBAAyB,GAAG,GAAG,GAAG,IAAI;AAAE;AACrD;AACA;AACA,MAAMe,2BAA2B,GAAG,GAAG,GAAG,GAAG,C", "file": "/packages/accounts-base.js", "sourcesContent": ["import {\n  AccountsClient,\n  AccountsTest,\n} from \"./accounts_client.js\";\n\n/**\n * @namespace Accounts\n * @summary The namespace for all client-side accounts-related methods.\n */\nAccounts = new AccountsClient(Meteor.settings?.public?.packages?.accounts || {});\n\n/**\n * @summary A [Mongo.Collection](#collections) containing user documents.\n * @locus Anywhere\n * @type {Mongo.Collection}\n * @importFromPackage meteor\n */\nMeteor.users = Accounts.users;\n\nexport {\n  Accounts,\n  AccountsClient,\n  AccountsTest,\n  // For backwards compatibility. Note that exporting an object as the\n  // default export is *not* the same as exporting its properties as named\n  // exports, as was previously assumed.\n  exports as default,\n};\n", "import {AccountsCommon} from \"./accounts_common.js\";\n\n/**\n * @summary Constructor for the `Accounts` object on the client.\n * @locus Client\n * @class AccountsClient\n * @extends AccountsCommon\n * @instancename accountsClient\n * @param {Object} options an object with fields:\n * @param {Object} options.connection Optional DDP connection to reuse.\n * @param {String} options.ddpUrl Optional URL for creating a new DDP connection.\n * @param {'session' | 'local'} options.clientStorage Optional Define what kind of storage you want for credentials on the client. Default is 'local' to use `localStorage`. Set to 'session' to use session storage.\n */\nexport class AccountsClient extends AccountsCommon {\n  constructor(options) {\n    super(options);\n\n    this._loggingIn = new ReactiveVar(false);\n    this._loggingOut = new ReactiveVar(false);\n\n    this._loginServicesHandle =\n      this.connection.subscribe(\"meteor.loginServiceConfiguration\");\n\n    this._pageLoadLoginCallbacks = [];\n    this._pageLoadLoginAttemptInfo = null;\n\n    this.savedHash = window.location.hash;\n    this._initUrlMatching();\n\n    this.initStorageLocation();\n\n    // Defined in localstorage_token.js.\n    this._initLocalStorage();\n\n    // This is for .registerClientLoginFunction & .callLoginFunction.\n    this._loginFuncs = {};\n\n    // This tracks whether callbacks registered with\n    // Accounts.onLogin have been called\n    this._loginCallbacksCalled = false;\n  }\n\n  initStorageLocation(options) {\n    // Determine whether to use local or session storage to storage credentials and anything else.\n    this.storageLocation = (options?.clientStorage === 'session' || Meteor.settings?.public?.packages?.accounts?.clientStorage === 'session') ? window.sessionStorage : Meteor._localStorage;\n  }\n\n  config(options) {\n    super.config(options);\n\n    this.initStorageLocation(options);\n  }\n\n  ///\n  /// CURRENT USER\n  ///\n\n  // @override\n  userId() {\n    return this.connection.userId();\n  }\n\n  // This is mostly just called within this file, but Meteor.loginWithPassword\n  // also uses it to make loggingIn() be true during the beginPasswordExchange\n  // method call too.\n  _setLoggingIn(x) {\n    this._loggingIn.set(x);\n  }\n\n  /**\n   * @summary True if a login method (such as `Meteor.loginWithPassword`, `Meteor.loginWithFacebook`, or `Accounts.createUser`) is currently in progress. A reactive data source.\n   * @locus Client\n   */\n  loggingIn() {\n    return this._loggingIn.get();\n  }\n\n  /**\n   * @summary True if a logout method (such as `Meteor.logout`) is currently in progress. A reactive data source.\n   * @locus Client\n   */\n  loggingOut() {\n    return this._loggingOut.get();\n  }\n\n  /**\n   * @summary Register a new login function on the client. Intended for OAuth package authors. You can call the login function by using\n   `Accounts.callLoginFunction` or `Accounts.callLoginFunction`.\n   * @locus Client\n   * @param {String} funcName The name of your login function. Used by `Accounts.callLoginFunction` and `Accounts.applyLoginFunction`.\n   Should be the OAuth provider name accordingly.\n   * @param {Function} func The actual function you want to call. Just write it in the manner of `loginWithFoo`.\n   */\n  registerClientLoginFunction(funcName, func) {\n    if (this._loginFuncs[funcName]) {\n      throw new Error(`${funcName} has been defined already`);\n    }\n    this._loginFuncs[funcName] = func;\n  }\n\n  /**\n   * @summary Call a login function defined using `Accounts.registerClientLoginFunction`. Excluding the first argument, all remaining\n   arguments are passed to the login function accordingly. Use `applyLoginFunction` if you want to pass in an arguments array that contains\n   all arguments for the login function.\n   * @locus Client\n   * @param {String} funcName The name of the login function you wanted to call.\n   */\n  callLoginFunction(funcName, ...funcArgs) {\n    if (!this._loginFuncs[funcName]) {\n      throw new Error(`${funcName} was not defined`);\n    }\n    return this._loginFuncs[funcName].apply(this, funcArgs);\n  }\n\n  /**\n   * @summary Same as ``callLoginFunction` but accept an `arguments` which contains all arguments for the login\n   function.\n   * @locus Client\n   * @param {String} funcName The name of the login function you wanted to call.\n   * @param {Array} funcArgs The `arguments` for the login function.\n   */\n  applyLoginFunction(funcName, funcArgs) {\n    if (!this._loginFuncs[funcName]) {\n      throw new Error(`${funcName} was not defined`);\n    }\n    return this._loginFuncs[funcName].apply(this, funcArgs);\n  }\n\n  /**\n   * @summary Log the user out.\n   * @locus Client\n   * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n   */\n  logout(callback) {\n    this._loggingOut.set(true);\n\n    this.connection.applyAsync('logout', [], {\n      // TODO[FIBERS]: Look this { wait: true } later.\n      wait: true\n    })\n      .then((result) => {\n        this._loggingOut.set(false);\n        this._loginCallbacksCalled = false;\n        this.makeClientLoggedOut();\n        callback && callback();\n      })\n      .catch((e) => {\n        this._loggingOut.set(false);\n        callback && callback(e);\n      });\n  }\n\n  /**\n   * @summary Log out other clients logged in as the current user, but does not log out the client that calls this function.\n   * @locus Client\n   * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n   */\n  logoutOtherClients(callback) {\n    // We need to make two method calls: one to replace our current token,\n    // and another to remove all tokens except the current one. We want to\n    // call these two methods one after the other, without any other\n    // methods running between them. For example, we don't want `logout`\n    // to be called in between our two method calls (otherwise the second\n    // method call would return an error). Another example: we don't want\n    // logout to be called before the callback for `getNewToken`;\n    // otherwise we would momentarily log the user out and then write a\n    // new token to localStorage.\n    //\n    // To accomplish this, we make both calls as wait methods, and queue\n    // them one after the other, without spinning off the event loop in\n    // between. Even though we queue `removeOtherTokens` before\n    // `getNewToken`, we won't actually send the `removeOtherTokens` call\n    // until the `getNewToken` callback has finished running, because they\n    // are both wait methods.\n    this.connection.apply(\n      'getNewToken',\n      [],\n      { wait: true },\n      (err, result) => {\n        if (! err) {\n          this._storeLoginToken(\n            this.userId(),\n            result.token,\n            result.tokenExpires\n          );\n        }\n      }\n    );\n\n    this.connection.apply(\n      'removeOtherTokens',\n      [],\n      { wait: true },\n      err => callback && callback(err)\n    );\n  }\n\n  ///\n  /// LOGIN METHODS\n  ///\n\n  // Call a login method on the server.\n  //\n  // A login method is a method which on success calls `this.setUserId(id)` and\n  // `Accounts._setLoginToken` on the server and returns an object with fields\n  // 'id' (containing the user id), 'token' (containing a resume token), and\n  // optionally `tokenExpires`.\n  //\n  // This function takes care of:\n  //   - Updating the Meteor.loggingIn() reactive data source\n  //   - Calling the method in 'wait' mode\n  //   - On success, saving the resume token to localStorage\n  //   - On success, calling Accounts.connection.setUserId()\n  //   - Setting up an onReconnect handler which logs in with\n  //     the resume token\n  //\n  // Options:\n  // - methodName: The method to call (default 'login')\n  // - methodArguments: The arguments for the method\n  // - validateResult: If provided, will be called with the result of the\n  //                 method. If it throws, the client will not be logged in (and\n  //                 its error will be passed to the callback).\n  // - userCallback: Will be called with no arguments once the user is fully\n  //                 logged in, or with the error on error.\n  //\n  callLoginMethod(options) {\n    options = {\n      methodName: 'login',\n      methodArguments: [{}],\n      _suppressLoggingIn: false,\n      ...options,\n    };\n\n    // Set defaults for callback arguments to no-op functions; make sure we\n    // override falsey values too.\n    ['validateResult', 'userCallback'].forEach(f => {\n      if (!options[f])\n        options[f] = () => null;\n    });\n\n    let called;\n    // Prepare callbacks: user provided and onLogin/onLoginFailure hooks.\n    const loginCallbacks = ({ error, loginDetails }) => {\n      if (!called) {\n        called = true;\n        if (!error) {\n          this._onLoginHook.forEach(callback => {\n            callback(loginDetails);\n            return true;\n          });\n          this._loginCallbacksCalled = true;\n        } else {\n          this._loginCallbacksCalled = false;\n          this._onLoginFailureHook.forEach(callback => {\n            callback({ error });\n            return true;\n          });\n        }\n        options.userCallback(error, loginDetails);\n      }\n    };\n\n    let reconnected = false;\n\n    // We want to set up onReconnect as soon as we get a result token back from\n    // the server, without having to wait for subscriptions to rerun. This is\n    // because if we disconnect and reconnect between getting the result and\n    // getting the results of subscription rerun, we WILL NOT re-send this\n    // method (because we never re-send methods whose results we've received)\n    // but we WILL call loggedInAndDataReadyCallback at \"reconnect quiesce\"\n    // time. This will lead to makeClientLoggedIn(result.id) even though we\n    // haven't actually sent a login method!\n    //\n    // But by making sure that we send this \"resume\" login in that case (and\n    // calling makeClientLoggedOut if it fails), we'll end up with an accurate\n    // client-side userId. (It's important that livedata_connection guarantees\n    // that the \"reconnect quiesce\"-time call to loggedInAndDataReadyCallback\n    // will occur before the callback from the resume login call.)\n    const onResultReceived = (err, result) => {\n      if (err || !result || !result.token) {\n        // Leave onReconnect alone if there was an error, so that if the user was\n        // already logged in they will still get logged in on reconnect.\n        // See issue #4970.\n      } else {\n        // First clear out any previously set Acccounts login onReconnect\n        // callback (to make sure we don't keep piling up duplicate callbacks,\n        // which would then all be triggered when reconnecting).\n        if (this._reconnectStopper) {\n          this._reconnectStopper.stop();\n        }\n\n        this._reconnectStopper = DDP.onReconnect(conn => {\n          if (conn != this.connection) {\n            return;\n          }\n          reconnected = true;\n          // If our token was updated in storage, use the latest one.\n          const storedToken = this._storedLoginToken();\n          if (storedToken) {\n            result = {\n              token: storedToken,\n              tokenExpires: this._storedLoginTokenExpires()\n            };\n          }\n          if (!result.tokenExpires)\n            result.tokenExpires = this._tokenExpiration(new Date());\n          if (this._tokenExpiresSoon(result.tokenExpires)) {\n            this.makeClientLoggedOut();\n          } else {\n            this.callLoginMethod({\n              methodArguments: [{resume: result.token}],\n              // Reconnect quiescence ensures that the user doesn't see an\n              // intermediate state before the login method finishes. So we don't\n              // need to show a logging-in animation.\n              _suppressLoggingIn: true,\n              userCallback: (error, loginDetails) => {\n                const storedTokenNow = this._storedLoginToken();\n                if (error) {\n                  // If we had a login error AND the current stored token is the\n                  // one that we tried to log in with, then declare ourselves\n                  // logged out. If there's a token in storage but it's not the\n                  // token that we tried to log in with, we don't know anything\n                  // about whether that token is valid or not, so do nothing. The\n                  // periodic localStorage poll will decide if we are logged in or\n                  // out with this token, if it hasn't already. Of course, even\n                  // with this check, another tab could insert a new valid token\n                  // immediately before we clear localStorage here, which would\n                  // lead to both tabs being logged out, but by checking the token\n                  // in storage right now we hope to make that unlikely to happen.\n                  //\n                  // If there is no token in storage right now, we don't have to\n                  // do anything; whatever code removed the token from storage was\n                  // responsible for calling `makeClientLoggedOut()`, or the\n                  // periodic localStorage poll will call `makeClientLoggedOut`\n                  // eventually if another tab wiped the token from storage.\n                  if (storedTokenNow && storedTokenNow === result.token) {\n                    this.makeClientLoggedOut();\n                  }\n                }\n                // Possibly a weird callback to call, but better than nothing if\n                // there is a reconnect between \"login result received\" and \"data\n                // ready\".\n                loginCallbacks({ error, loginDetails });\n              }});\n          }\n        });\n      }\n    };\n\n    // This callback is called once the local cache of the current-user\n    // subscription (and all subscriptions, in fact) are guaranteed to be up to\n    // date.\n    const loggedInAndDataReadyCallback = (error, result) => {\n      // If the login method returns its result but the connection is lost\n      // before the data is in the local cache, it'll set an onReconnect (see\n      // above). The onReconnect will try to log in using the token, and *it*\n      // will call userCallback via its own version of this\n      // loggedInAndDataReadyCallback. So we don't have to do anything here.\n      if (reconnected)\n        return;\n\n      // Note that we need to call this even if _suppressLoggingIn is true,\n      // because it could be matching a _setLoggingIn(true) from a\n      // half-completed pre-reconnect login method.\n      if (error || !result) {\n        error = error || new Error(\n          `No result from call to ${options.methodName}`\n        );\n        loginCallbacks({ error });\n        this._setLoggingIn(false);\n        return;\n      }\n      try {\n        options.validateResult(result);\n      } catch (e) {\n        loginCallbacks({ error: e });\n        this._setLoggingIn(false);\n        return;\n      }\n\n      // Make the client logged in. (The user data should already be loaded!)\n      this.makeClientLoggedIn(result.id, result.token, result.tokenExpires);\n\n      // use Tracker to make we sure have a user before calling the callbacks\n      Tracker.autorun(async (computation) => {\n        const user = await Tracker.withComputation(computation, () =>\n          Meteor.userAsync(),\n        );\n\n        if (user) {\n          loginCallbacks({ loginDetails: result });\n          this._setLoggingIn(false);\n          computation.stop();\n        }\n      });\n\n    };\n\n    if (!options._suppressLoggingIn) {\n      this._setLoggingIn(true);\n    }\n    this.connection.applyAsync(\n      options.methodName,\n      options.methodArguments,\n      { wait: true, onResultReceived },\n      loggedInAndDataReadyCallback);\n  }\n\n  makeClientLoggedOut() {\n    // Ensure client was successfully logged in before running logout hooks.\n    if (this.connection._userId) {\n      this._onLogoutHook.each(callback => {\n        callback();\n        return true;\n      });\n    }\n    this._unstoreLoginToken();\n    this.connection.setUserId(null);\n    this._reconnectStopper && this._reconnectStopper.stop();\n  }\n\n  makeClientLoggedIn(userId, token, tokenExpires) {\n    this._storeLoginToken(userId, token, tokenExpires);\n    this.connection.setUserId(userId);\n  }\n\n  ///\n  /// LOGIN SERVICES\n  ///\n\n  // A reactive function returning whether the loginServiceConfiguration\n  // subscription is ready. Used by accounts-ui to hide the login button\n  // until we have all the configuration loaded\n  //\n  loginServicesConfigured() {\n    return this._loginServicesHandle.ready();\n  };\n\n  // Some login services such as the redirect login flow or the resume\n  // login handler can log the user in at page load time.  The\n  // Meteor.loginWithX functions have a callback argument, but the\n  // callback function instance won't be in memory any longer if the\n  // page was reloaded.  The `onPageLoadLogin` function allows a\n  // callback to be registered for the case where the login was\n  // initiated in a previous VM, and we now have the result of the login\n  // attempt in a new VM.\n\n  // Register a callback to be called if we have information about a\n  // login attempt at page load time.  Call the callback immediately if\n  // we already have the page load login attempt info, otherwise stash\n  // the callback to be called if and when we do get the attempt info.\n  //\n  onPageLoadLogin(f) {\n    if (this._pageLoadLoginAttemptInfo) {\n      f(this._pageLoadLoginAttemptInfo);\n    } else {\n      this._pageLoadLoginCallbacks.push(f);\n    }\n  };\n\n  // Receive the information about the login attempt at page load time.\n  // Call registered callbacks, and also record the info in case\n  // someone's callback hasn't been registered yet.\n  //\n  _pageLoadLogin(attemptInfo) {\n    if (this._pageLoadLoginAttemptInfo) {\n      Meteor._debug(\n        'Ignoring unexpected duplicate page load login attempt info'\n      );\n      return;\n    }\n\n    this._pageLoadLoginCallbacks.forEach(callback => callback(attemptInfo));\n    this._pageLoadLoginCallbacks = [];\n    this._pageLoadLoginAttemptInfo = attemptInfo;\n  }\n\n  // _startupCallback executes on onLogin callbacks\n  // at registration time if already logged in\n  // this can happen when new AccountsClient is created\n  // before callbacks are registered see #10157\n  _startupCallback(callback) {\n    // Are we already logged in?\n    if (this._loginCallbacksCalled) {\n      // If already logged in before handler is registered, it's safe to\n      // assume type is a 'resume', so we execute the callback at the end\n      // of the queue so that Meteor.startup can complete before any\n      // embedded onLogin callbacks would execute.\n      Meteor.setTimeout(() => callback({ type: 'resume' }), 0);\n    }\n  }\n\n  ///\n  /// LOGIN TOKENS\n  ///\n\n  // These methods deal with storing a login token and user id in the\n  // browser's localStorage facility. It polls local storage every few\n  // seconds to synchronize login state between multiple tabs in the same\n  // browser.\n\n  loginWithToken(token, callback) {\n    this.callLoginMethod({\n      methodArguments: [{\n        resume: token\n      }],\n      userCallback: callback\n    });\n  };\n\n  // Semi-internal API. Call this function to re-enable auto login after\n  // if it was disabled at startup.\n  _enableAutoLogin() {\n    this._autoLoginEnabled = true;\n    this._pollStoredLoginToken();\n  };\n\n  ///\n  /// STORING\n  ///\n\n  // Call this from the top level of the test file for any test that does\n  // logging in and out, to protect multiple tabs running the same tests\n  // simultaneously from interfering with each others' localStorage.\n  _isolateLoginTokenForTest() {\n    this.LOGIN_TOKEN_KEY = this.LOGIN_TOKEN_KEY + Random.id();\n    this.USER_ID_KEY = this.USER_ID_KEY + Random.id();\n  };\n\n  _storeLoginToken(userId, token, tokenExpires) {\n    this.storageLocation.setItem(this.USER_ID_KEY, userId);\n    this.storageLocation.setItem(this.LOGIN_TOKEN_KEY, token);\n    if (! tokenExpires)\n      tokenExpires = this._tokenExpiration(new Date());\n    this.storageLocation.setItem(this.LOGIN_TOKEN_EXPIRES_KEY, tokenExpires);\n\n    // to ensure that the localstorage poller doesn't end up trying to\n    // connect a second time\n    this._lastLoginTokenWhenPolled = token;\n  };\n\n  _unstoreLoginToken() {\n    this.storageLocation.removeItem(this.USER_ID_KEY);\n    this.storageLocation.removeItem(this.LOGIN_TOKEN_KEY);\n    this.storageLocation.removeItem(this.LOGIN_TOKEN_EXPIRES_KEY);\n\n    // to ensure that the localstorage poller doesn't end up trying to\n    // connect a second time\n    this._lastLoginTokenWhenPolled = null;\n  };\n\n  // This is private, but it is exported for now because it is used by a\n  // test in accounts-password.\n  _storedLoginToken() {\n    return this.storageLocation.getItem(this.LOGIN_TOKEN_KEY);\n  };\n\n  _storedLoginTokenExpires() {\n    return this.storageLocation.getItem(this.LOGIN_TOKEN_EXPIRES_KEY);\n  };\n\n  _storedUserId() {\n    return this.storageLocation.getItem(this.USER_ID_KEY);\n  };\n\n  _unstoreLoginTokenIfExpiresSoon() {\n    const tokenExpires = this._storedLoginTokenExpires();\n    if (tokenExpires && this._tokenExpiresSoon(new Date(tokenExpires))) {\n      this._unstoreLoginToken();\n    }\n  };\n\n  ///\n  /// AUTO-LOGIN\n  ///\n\n  _initLocalStorage() {\n    // Key names to use in localStorage\n    this.LOGIN_TOKEN_KEY = \"Meteor.loginToken\";\n    this.LOGIN_TOKEN_EXPIRES_KEY = \"Meteor.loginTokenExpires\";\n    this.USER_ID_KEY = \"Meteor.userId\";\n\n    const rootUrlPathPrefix = __meteor_runtime_config__.ROOT_URL_PATH_PREFIX;\n    if (rootUrlPathPrefix || this.connection !== Meteor.connection) {\n      // We want to keep using the same keys for existing apps that do not\n      // set a custom ROOT_URL_PATH_PREFIX, so that most users will not have\n      // to log in again after an app updates to a version of Meteor that\n      // contains this code, but it's generally preferable to namespace the\n      // keys so that connections from distinct apps to distinct DDP URLs\n      // will be distinct in Meteor._localStorage.\n      let namespace = `:${this.connection._stream.rawUrl}`;\n      if (rootUrlPathPrefix) {\n        namespace += `:${rootUrlPathPrefix}`;\n      }\n      this.LOGIN_TOKEN_KEY += namespace;\n      this.LOGIN_TOKEN_EXPIRES_KEY += namespace;\n      this.USER_ID_KEY += namespace;\n    }\n\n    let token;\n    if (this._autoLoginEnabled) {\n      // Immediately try to log in via local storage, so that any DDP\n      // messages are sent after we have established our user account\n      this._unstoreLoginTokenIfExpiresSoon();\n      token = this._storedLoginToken();\n      if (token) {\n        // On startup, optimistically present us as logged in while the\n        // request is in flight. This reduces page flicker on startup.\n        const userId = this._storedUserId();\n        userId && this.connection.setUserId(userId);\n        this.loginWithToken(token, err => {\n          if (err) {\n            Meteor._debug(`Error logging in with token: ${err}`);\n            this.makeClientLoggedOut();\n          }\n\n          this._pageLoadLogin({\n            type: \"resume\",\n            allowed: !err,\n            error: err,\n            methodName: \"login\",\n            // XXX This is duplicate code with loginWithToken, but\n            // loginWithToken can also be called at other times besides\n            // page load.\n            methodArguments: [{resume: token}]\n          });\n        });\n      }\n    }\n\n    // Poll local storage every 3 seconds to login if someone logged in in\n    // another tab\n    this._lastLoginTokenWhenPolled = token;\n\n    if (this._pollIntervalTimer) {\n      // Unlikely that _initLocalStorage will be called more than once for\n      // the same AccountsClient instance, but just in case...\n      clearInterval(this._pollIntervalTimer);\n    }\n\n    this._pollIntervalTimer = setInterval(() => {\n      this._pollStoredLoginToken();\n    }, 3000);\n  };\n\n  _pollStoredLoginToken() {\n    if (! this._autoLoginEnabled) {\n      return;\n    }\n\n    const currentLoginToken = this._storedLoginToken();\n\n    // != instead of !== just to make sure undefined and null are treated the same\n    if (this._lastLoginTokenWhenPolled != currentLoginToken) {\n      if (currentLoginToken) {\n        this.loginWithToken(currentLoginToken, (err) => {\n          if (err) {\n            this.makeClientLoggedOut();\n          }\n        });\n      } else {\n        this.logout();\n      }\n    }\n\n    this._lastLoginTokenWhenPolled = currentLoginToken;\n  };\n\n  ///\n  /// URLS\n  ///\n\n  _initUrlMatching() {\n    // By default, allow the autologin process to happen.\n    this._autoLoginEnabled = true;\n\n    // We only support one callback per URL.\n    this._accountsCallbacks = {};\n\n    // Try to match the saved value of window.location.hash.\n    this._attemptToMatchHash();\n  };\n\n  // Separate out this functionality for testing\n  _attemptToMatchHash() {\n    attemptToMatchHash(this, this.savedHash, defaultSuccessHandler);\n  };\n\n  /**\n   * @summary Register a function to call when a reset password link is clicked\n   * in an email sent by\n   * [`Accounts.sendResetPasswordEmail`](#accounts_sendresetpasswordemail).\n   * This function should be called in top-level code, not inside\n   * `Meteor.startup()`.\n   * @memberof! Accounts\n   * @name onResetPasswordLink\n   * @param  {Function} callback The function to call. It is given two arguments:\n   *\n   * 1. `token`: A password reset token that can be passed to\n   * [`Accounts.resetPassword`](#accounts_resetpassword).\n   * 2. `done`: A function to call when the password reset UI flow is complete. The normal\n   * login process is suspended until this function is called, so that the\n   * password for user A can be reset even if user B was logged in.\n   * @locus Client\n   */\n  onResetPasswordLink(callback) {\n    if (this._accountsCallbacks[\"reset-password\"]) {\n      Meteor._debug(\"Accounts.onResetPasswordLink was called more than once. \" +\n        \"Only one callback added will be executed.\");\n    }\n\n    this._accountsCallbacks[\"reset-password\"] = callback;\n  };\n\n  /**\n   * @summary Register a function to call when an email verification link is\n   * clicked in an email sent by\n   * [`Accounts.sendVerificationEmail`](#accounts_sendverificationemail).\n   * This function should be called in top-level code, not inside\n   * `Meteor.startup()`.\n   * @memberof! Accounts\n   * @name onEmailVerificationLink\n   * @param  {Function} callback The function to call. It is given two arguments:\n   *\n   * 1. `token`: An email verification token that can be passed to\n   * [`Accounts.verifyEmail`](#accounts_verifyemail).\n   * 2. `done`: A function to call when the email verification UI flow is complete.\n   * The normal login process is suspended until this function is called, so\n   * that the user can be notified that they are verifying their email before\n   * being logged in.\n   * @locus Client\n   */\n  onEmailVerificationLink(callback) {\n    if (this._accountsCallbacks[\"verify-email\"]) {\n      Meteor._debug(\"Accounts.onEmailVerificationLink was called more than once. \" +\n        \"Only one callback added will be executed.\");\n    }\n\n    this._accountsCallbacks[\"verify-email\"] = callback;\n  };\n\n  /**\n   * @summary Register a function to call when an account enrollment link is\n   * clicked in an email sent by\n   * [`Accounts.sendEnrollmentEmail`](#accounts_sendenrollmentemail).\n   * This function should be called in top-level code, not inside\n   * `Meteor.startup()`.\n   * @memberof! Accounts\n   * @name onEnrollmentLink\n   * @param  {Function} callback The function to call. It is given two arguments:\n   *\n   * 1. `token`: A password reset token that can be passed to\n   * [`Accounts.resetPassword`](#accounts_resetpassword) to give the newly\n   * enrolled account a password.\n   * 2. `done`: A function to call when the enrollment UI flow is complete.\n   * The normal login process is suspended until this function is called, so that\n   * user A can be enrolled even if user B was logged in.\n   * @locus Client\n   */\n  onEnrollmentLink(callback) {\n    if (this._accountsCallbacks[\"enroll-account\"]) {\n      Meteor._debug(\"Accounts.onEnrollmentLink was called more than once. \" +\n        \"Only one callback added will be executed.\");\n    }\n\n    this._accountsCallbacks[\"enroll-account\"] = callback;\n  };\n\n}\n\n/**\n * @summary True if a login method (such as `Meteor.loginWithPassword`,\n * `Meteor.loginWithFacebook`, or `Accounts.createUser`) is currently in\n * progress. A reactive data source.\n * @locus Client\n * @importFromPackage meteor\n */\nMeteor.loggingIn = () => Accounts.loggingIn();\n\n/**\n * @summary True if a logout method (such as `Meteor.logout`) is currently in\n * progress. A reactive data source.\n * @locus Client\n * @importFromPackage meteor\n */\nMeteor.loggingOut = () => Accounts.loggingOut();\n\n/**\n * @summary Log the user out.\n * @locus Client\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage meteor\n */\nMeteor.logout = callback => Accounts.logout(callback);\n\n/**\n * @summary Log out other clients logged in as the current user, but does not log out the client that calls this function.\n * @locus Client\n * @param {Function} [callback] Optional callback. Called with no arguments on success, or with a single `Error` argument on failure.\n * @importFromPackage meteor\n */\nMeteor.logoutOtherClients = callback => Accounts.logoutOtherClients(callback);\n\n/**\n * @summary Login with a Meteor access token.\n * @locus Client\n * @param {Object} [token] Local storage token for use with login across\n * multiple tabs in the same browser.\n * @param {Function} [callback] Optional callback. Called with no arguments on\n * success.\n * @importFromPackage meteor\n */\nMeteor.loginWithToken = (token, callback) =>\n  Accounts.loginWithToken(token, callback);\n\n///\n/// HANDLEBARS HELPERS\n///\n\n// If our app has a Blaze, register the {{currentUser}} and {{loggingIn}}\n// global helpers.\nif (Package.blaze) {\n  const { Template } = Package.blaze.Blaze;\n\n  /**\n   * @global\n   * @name  currentUser\n   * @isHelper true\n   * @summary Calls [Meteor.user()](#meteor_user). Use `{{#if currentUser}}` to check whether the user is logged in.\n   */\n  Template.registerHelper('currentUser', () => Meteor.user());\n\n  // TODO: the code above needs to be changed to Meteor.userAsync() when we have\n  // a way to make it reactive using async.\n  // Template.registerHelper('currentUserAsync',\n  //  async () => await Meteor.userAsync());\n\n  /**\n   * @global\n   * @name  loggingIn\n   * @isHelper true\n   * @summary Calls [Meteor.loggingIn()](#meteor_loggingin).\n   */\n  Template.registerHelper('loggingIn', () => Meteor.loggingIn());\n\n  /**\n   * @global\n   * @name  loggingOut\n   * @isHelper true\n   * @summary Calls [Meteor.loggingOut()](#meteor_loggingout).\n   */\n  Template.registerHelper('loggingOut', () => Meteor.loggingOut());\n\n  /**\n   * @global\n   * @name  loggingInOrOut\n   * @isHelper true\n   * @summary Calls [Meteor.loggingIn()](#meteor_loggingin) or [Meteor.loggingOut()](#meteor_loggingout).\n   */\n  Template.registerHelper(\n    'loggingInOrOut',\n    () => Meteor.loggingIn() || Meteor.loggingOut()\n  );\n}\n\nconst defaultSuccessHandler = function(token, urlPart) {\n  // put login in a suspended state to wait for the interaction to finish\n  this._autoLoginEnabled = false;\n\n  // wait for other packages to register callbacks\n  Meteor.startup(() => {\n    // if a callback has been registered for this kind of token, call it\n    if (this._accountsCallbacks[urlPart]) {\n      this._accountsCallbacks[urlPart](token, () => this._enableAutoLogin());\n    }\n  });\n}\n\n// Note that both arguments are optional and are currently only passed by\n// accounts_url_tests.js.\nconst attemptToMatchHash = (accounts, hash, success) => {\n  // All of the special hash URLs we support for accounts interactions\n  [\"reset-password\", \"verify-email\", \"enroll-account\"].forEach(urlPart => {\n    let token;\n\n    const tokenRegex = new RegExp(`^\\\\#\\\\/${urlPart}\\\\/(.*)$`);\n    const match = hash.match(tokenRegex);\n\n    if (match) {\n      token = match[1];\n\n      // XXX COMPAT WITH 0.9.3\n      if (urlPart === \"reset-password\") {\n        accounts._resetPasswordToken = token;\n      } else if (urlPart === \"verify-email\") {\n        accounts._verifyEmailToken = token;\n      } else if (urlPart === \"enroll-account\") {\n        accounts._enrollAccountToken = token;\n      }\n    } else {\n      return;\n    }\n\n    // If no handlers match the hash, then maybe it's meant to be consumed\n    // by some entirely different code, so we only clear it the first time\n    // a handler successfully matches. Note that later handlers reuse the\n    // savedHash, so clearing window.location.hash here will not interfere\n    // with their needs.\n    window.location.hash = \"\";\n\n    // Do some stuff with the token we matched\n    success.call(accounts, token, urlPart);\n  });\n}\n\n// Export for testing\nexport const AccountsTest = {\n  attemptToMatchHash: (hash, success) =>\n    attemptToMatchHash(Accounts, hash, success),\n};\n", "import { Meteor } from 'meteor/meteor';\n\n// config option keys\nconst VALID_CONFIG_KEYS = [\n  'sendVerificationEmail',\n  'forbidClientAccountCreation',\n  'restrictCreationByEmailDomain',\n  'loginExpiration',\n  'loginExpirationInDays',\n  'oauthSecretKey',\n  'passwordResetTokenExpirationInDays',\n  'passwordResetTokenExpiration',\n  'passwordEnrollTokenExpirationInDays',\n  'passwordEnrollTokenExpiration',\n  'ambiguousErrorMessages',\n  'bcryptRounds',\n  'argon2Enabled',\n  'argon2Type',\n  'argon2TimeCost',\n  'argon2MemoryCost',\n  'argon2Parallelism',\n  'defaultFieldSelector',\n  'collection',\n  'loginTokenExpirationHours',\n  'tokenSequenceLength',\n  'clientStorage',\n  'ddpUrl',\n  'connection',\n];\n\n/**\n * @summary Super-constructor for AccountsClient and AccountsServer.\n * @locus Anywhere\n * @class AccountsCommon\n * @instancename accountsClientOrServer\n * @param options {Object} an object with fields:\n * - connection {Object} Optional DDP connection to reuse.\n * - ddpUrl {String} Optional URL for creating a new DDP connection.\n * - collection {String|Mongo.Collection} The name of the Mongo.Collection\n *     or the Mongo.Collection object to hold the users.\n */\nexport class AccountsCommon {\n  constructor(options) {\n    // Validate config options keys\n    for (const key of Object.keys(options)) {\n      if (!VALID_CONFIG_KEYS.includes(key)) {\n        console.error(`Accounts.config: Invalid key: ${key}`);\n      }\n    }\n\n    // Currently this is read directly by packages like accounts-password\n    // and accounts-ui-unstyled.\n    this._options = options || {};\n\n    // Note that setting this.connection = null causes this.users to be a\n    // LocalCollection, which is not what we want.\n    this.connection = undefined;\n    this._initConnection(options || {});\n\n    // There is an allow call in accounts_server.js that restricts writes to\n    // this collection.\n    this.users = this._initializeCollection(options || {});\n\n    // Callback exceptions are printed with Meteor._debug and ignored.\n    this._onLoginHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLogin callback',\n    });\n\n    this._onLoginFailureHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLoginFailure callback',\n    });\n\n    this._onLogoutHook = new Hook({\n      bindEnvironment: false,\n      debugPrintExceptions: 'onLogout callback',\n    });\n\n    // Expose for testing.\n    this.DEFAULT_LOGIN_EXPIRATION_DAYS = DEFAULT_LOGIN_EXPIRATION_DAYS;\n    this.LOGIN_UNEXPIRING_TOKEN_DAYS = LOGIN_UNEXPIRING_TOKEN_DAYS;\n\n    // Thrown when the user cancels the login process (eg, closes an oauth\n    // popup, declines retina scan, etc)\n    const lceName = 'Accounts.LoginCancelledError';\n    this.LoginCancelledError = Meteor.makeErrorType(lceName, function(\n      description\n    ) {\n      this.message = description;\n    });\n    this.LoginCancelledError.prototype.name = lceName;\n\n    // This is used to transmit specific subclass errors over the wire. We\n    // should come up with a more generic way to do this (eg, with some sort of\n    // symbolic error code rather than a number).\n    this.LoginCancelledError.numericError = 0x8acdc2f;\n  }\n\n  _initializeCollection(options) {\n    if (options.collection && typeof options.collection !== 'string' && !(options.collection instanceof Mongo.Collection)) {\n      throw new Meteor.Error('Collection parameter can be only of type string or \"Mongo.Collection\"');\n    }\n\n    let collectionName = 'users';\n    if (typeof options.collection === 'string') {\n      collectionName = options.collection;\n    }\n\n    let collection;\n    if (options.collection instanceof Mongo.Collection) {\n      collection = options.collection;\n    } else {\n      collection = new Mongo.Collection(collectionName, {\n        _preventAutopublish: true,\n        connection: this.connection,\n      });\n    }\n\n    return collection;\n  }\n\n  /**\n   * @summary Get the current user id, or `null` if no user is logged in. A reactive data source.\n   * @locus Anywhere\n   */\n  userId() {\n    throw new Error('userId method not implemented');\n  }\n\n  // merge the defaultFieldSelector with an existing options object\n  _addDefaultFieldSelector(options = {}) {\n    // this will be the most common case for most people, so make it quick\n    if (!this._options.defaultFieldSelector) return options;\n\n    // if no field selector then just use defaultFieldSelector\n    if (!options.fields)\n      return {\n        ...options,\n        fields: this._options.defaultFieldSelector,\n      };\n\n    // if empty field selector then the full user object is explicitly requested, so obey\n    const keys = Object.keys(options.fields);\n    if (!keys.length) return options;\n\n    // if the requested fields are +ve then ignore defaultFieldSelector\n    // assume they are all either +ve or -ve because Mongo doesn't like mixed\n    if (!!options.fields[keys[0]]) return options;\n\n    // The requested fields are -ve.\n    // If the defaultFieldSelector is +ve then use requested fields, otherwise merge them\n    const keys2 = Object.keys(this._options.defaultFieldSelector);\n    return this._options.defaultFieldSelector[keys2[0]]\n      ? options\n      : {\n          ...options,\n          fields: {\n            ...options.fields,\n            ...this._options.defaultFieldSelector,\n          },\n        };\n  }\n\n  /**\n   * @summary Get the current user record, or `null` if no user is logged in. A reactive data source. In the server this fuction returns a promise.\n   * @locus Anywhere\n   * @param {Object} [options]\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   */\n  user(options) {\n    if (Meteor.isServer) {\n      console.warn([\n        \"`Meteor.user()` is deprecated on the server side.\",\n        \"    To fetch the current user record on the server,\",\n        \"    use `Meteor.userAsync()` instead.\",\n      ].join(\"\\n\"));\n    }\n\n    const self = this;\n    const userId = self.userId();\n    const findOne = (...args) => Meteor.isClient\n      ? self.users.findOne(...args)\n      : self.users.findOneAsync(...args);\n    return userId\n      ? findOne(userId, this._addDefaultFieldSelector(options))\n      : null;\n  }\n\n  /**\n   * @summary Get the current user record, or `null` if no user is logged in.\n   * @locus Anywhere\n   * @param {Object} [options]\n   * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n   */\n  async userAsync(options) {\n    const userId = this.userId();\n    return userId\n      ? this.users.findOneAsync(userId, this._addDefaultFieldSelector(options))\n      : null;\n  }\n\n  /**\n   * @summary Set global accounts options. You can also set these in `Meteor.settings.packages.accounts` without the need to call this function.\n   * @locus Anywhere\n   * @param {Object} options\n   * @param {Boolean} options.sendVerificationEmail New users with an email address will receive an address verification email.\n   * @param {Boolean} options.forbidClientAccountCreation Calls to [`createUser`](#accounts_createuser) from the client will be rejected. In addition, if you are using [accounts-ui](#accountsui), the \"Create account\" link will not be available.\n   * @param {String | Function} options.restrictCreationByEmailDomain If set to a string, only allows new users if the domain part of their email address matches the string. If set to a function, only allows new users if the function returns true.  The function is passed the full email address of the proposed new user.  Works with password-based sign-in and external services that expose email addresses (Google, Facebook, GitHub). All existing users still can log in after enabling this option. Example: `Accounts.config({ restrictCreationByEmailDomain: 'school.edu' })`.\n   * @param {Number} options.loginExpiration The number of milliseconds from when a user logs in until their token expires and they are logged out, for a more granular control. If `loginExpirationInDays` is set, it takes precedent.\n   * @param {Number} options.loginExpirationInDays The number of days from when a user logs in until their token expires and they are logged out. Defaults to 90. Set to `null` to disable login expiration.\n   * @param {String} options.oauthSecretKey When using the `oauth-encryption` package, the 16 byte key using to encrypt sensitive account credentials in the database, encoded in base64.  This option may only be specified on the server.  See packages/oauth-encryption/README.md for details.\n   * @param {Number} options.passwordResetTokenExpirationInDays The number of days from when a link to reset password is sent until token expires and user can't reset password with the link anymore. Defaults to 3.\n   * @param {Number} options.passwordResetTokenExpiration The number of milliseconds from when a link to reset password is sent until token expires and user can't reset password with the link anymore. If `passwordResetTokenExpirationInDays` is set, it takes precedent.\n   * @param {Number} options.passwordEnrollTokenExpirationInDays The number of days from when a link to set initial password is sent until token expires and user can't set password with the link anymore. Defaults to 30.\n   * @param {Number} options.passwordEnrollTokenExpiration The number of milliseconds from when a link to set initial password is sent until token expires and user can't set password with the link anymore. If `passwordEnrollTokenExpirationInDays` is set, it takes precedent.\n   * @param {Boolean} options.ambiguousErrorMessages Return ambiguous error messages from login failures to prevent user enumeration. Defaults to `true`.\n   * @param {Number} options.bcryptRounds Allows override of number of bcrypt rounds (aka work factor) used to store passwords. The default is 10.\n   * @param {Boolean} options.argon2Enabled Enable argon2 algorithm usage in replacement for bcrypt. The default is `false`.\n   * @param {'argon2id' | 'argon2i' | 'argon2d'} options.argon2Type Allows override of the argon2 algorithm type. The default is `argon2id`.\n   * @param {Number} options.argon2TimeCost Allows override of number of argon2 iterations (aka time cost) used to store passwords. The default is 2.\n   * @param {Number} options.argon2MemoryCost Allows override of the amount of memory (in KiB) used by the argon2 algorithm. The default is 19456 (19MB).\n   * @param {Number} options.argon2Parallelism Allows override of the number of threads used by the argon2 algorithm. The default is 1.\n   * @param {MongoFieldSpecifier} options.defaultFieldSelector To exclude by default large custom fields from `Meteor.user()` and `Meteor.findUserBy...()` functions when called without a field selector, and all `onLogin`, `onLoginFailure` and `onLogout` callbacks.  Example: `Accounts.config({ defaultFieldSelector: { myBigArray: 0 }})`. Beware when using this. If, for instance, you do not include `email` when excluding the fields, you can have problems with functions like `forgotPassword` that will break because they won't have the required data available. It's recommend that you always keep the fields `_id`, `username`, and `email`.\n   * @param {String|Mongo.Collection} options.collection A collection name or a Mongo.Collection object to hold the users.\n   * @param {Number} options.loginTokenExpirationHours When using the package `accounts-2fa`, use this to set the amount of time a token sent is valid. As it's just a number, you can use, for example, 0.5 to make the token valid for just half hour. The default is 1 hour.\n   * @param {Number} options.tokenSequenceLength When using the package `accounts-2fa`, use this to the size of the token sequence generated. The default is 6.\n   * @param {'session' | 'local'} options.clientStorage By default login credentials are stored in local storage, setting this to true will switch to using session storage.\n   */\n  config(options) {\n    // We don't want users to accidentally only call Accounts.config on the\n    // client, where some of the options will have partial effects (eg removing\n    // the \"create account\" button from accounts-ui if forbidClientAccountCreation\n    // is set, or redirecting Google login to a specific-domain page) without\n    // having their full effects.\n    if (Meteor.isServer) {\n      __meteor_runtime_config__.accountsConfigCalled = true;\n    } else if (!__meteor_runtime_config__.accountsConfigCalled) {\n      // XXX would be nice to \"crash\" the client and replace the UI with an error\n      // message, but there's no trivial way to do this.\n      Meteor._debug(\n        'Accounts.config was called on the client but not on the ' +\n          'server; some configuration options may not take effect.'\n      );\n    }\n\n    // We need to validate the oauthSecretKey option at the time\n    // Accounts.config is called. We also deliberately don't store the\n    // oauthSecretKey in Accounts._options.\n    if (Object.prototype.hasOwnProperty.call(options, 'oauthSecretKey')) {\n      if (Meteor.isClient) {\n        throw new Error(\n          'The oauthSecretKey option may only be specified on the server'\n        );\n      }\n      if (!Package['oauth-encryption']) {\n        throw new Error(\n          'The oauth-encryption package must be loaded to set oauthSecretKey'\n        );\n      }\n      Package['oauth-encryption'].OAuthEncryption.loadKey(\n        options.oauthSecretKey\n      );\n      options = { ...options };\n      delete options.oauthSecretKey;\n    }\n\n    // Validate config options keys\n    for (const key of Object.keys(options)) {\n      if (!VALID_CONFIG_KEYS.includes(key)) {\n        console.error(`Accounts.config: Invalid key: ${key}`);\n      }\n    }\n\n    // set values in Accounts._options\n    for (const key of VALID_CONFIG_KEYS) {\n      if (key in options) {\n        if (key in this._options) {\n          if (key !== 'collection' && (Meteor.isTest && key !== 'clientStorage')) {\n            throw new Meteor.Error(`Can't set \\`${key}\\` more than once`);\n          }\n        }\n        this._options[key] = options[key];\n      }\n    }\n\n    if (options.collection && options.collection !== this.users._name && options.collection !== this.users) {\n      this.users = this._initializeCollection(options);\n    }\n  }\n\n  /**\n   * @summary Register a callback to be called after a login attempt succeeds.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called when login is successful.\n   *                        The callback receives a single object that\n   *                        holds login details. This object contains the login\n   *                        result type (password, resume, etc.) on both the\n   *                        client and server. `onLogin` callbacks registered\n   *                        on the server also receive extra data, such\n   *                        as user details, connection information, etc.\n   */\n  onLogin(func) {\n    let ret = this._onLoginHook.register(func);\n    // call the just registered callback if already logged in\n    this._startupCallback(ret.callback);\n    return ret;\n  }\n\n  /**\n   * @summary Register a callback to be called after a login attempt fails.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called after the login has failed.\n   */\n  onLoginFailure(func) {\n    return this._onLoginFailureHook.register(func);\n  }\n\n  /**\n   * @summary Register a callback to be called after a logout attempt succeeds.\n   * @locus Anywhere\n   * @param {Function} func The callback to be called when logout is successful.\n   */\n  onLogout(func) {\n    return this._onLogoutHook.register(func);\n  }\n\n  _initConnection(options) {\n    if (!Meteor.isClient) {\n      return;\n    }\n\n    // The connection used by the Accounts system. This is the connection\n    // that will get logged in by Meteor.login(), and this is the\n    // connection whose login state will be reflected by Meteor.userId().\n    //\n    // It would be much preferable for this to be in accounts_client.js,\n    // but it has to be here because it's needed to create the\n    // Meteor.users collection.\n    if (options.connection) {\n      this.connection = options.connection;\n    } else if (options.ddpUrl) {\n      this.connection = DDP.connect(options.ddpUrl);\n    } else if (\n      typeof __meteor_runtime_config__ !== 'undefined' &&\n      __meteor_runtime_config__.ACCOUNTS_CONNECTION_URL\n    ) {\n      // Temporary, internal hook to allow the server to point the client\n      // to a different authentication server. This is for a very\n      // particular use case that comes up when implementing a oauth\n      // server. Unsupported and may go away at any point in time.\n      //\n      // We will eventually provide a general way to use account-base\n      // against any DDP connection, not just one special one.\n      this.connection = DDP.connect(\n        __meteor_runtime_config__.ACCOUNTS_CONNECTION_URL\n      );\n    } else {\n      this.connection = Meteor.connection;\n    }\n  }\n\n  _getTokenLifetimeMs() {\n    // When loginExpirationInDays is set to null, we'll use a really high\n    // number of days (LOGIN_UNEXPIRABLE_TOKEN_DAYS) to simulate an\n    // unexpiring token.\n    const loginExpirationInDays =\n      this._options.loginExpirationInDays === null\n        ? LOGIN_UNEXPIRING_TOKEN_DAYS\n        : this._options.loginExpirationInDays;\n    return (\n      this._options.loginExpiration ||\n      (loginExpirationInDays || DEFAULT_LOGIN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _getPasswordResetTokenLifetimeMs() {\n    return (\n      this._options.passwordResetTokenExpiration ||\n      (this._options.passwordResetTokenExpirationInDays ||\n        DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _getPasswordEnrollTokenLifetimeMs() {\n    return (\n      this._options.passwordEnrollTokenExpiration ||\n      (this._options.passwordEnrollTokenExpirationInDays ||\n        DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS) * ********\n    );\n  }\n\n  _tokenExpiration(when) {\n    // We pass when through the Date constructor for backwards compatibility;\n    // `when` used to be a number.\n    return new Date(new Date(when).getTime() + this._getTokenLifetimeMs());\n  }\n\n  _tokenExpiresSoon(when) {\n    let minLifetimeMs = 0.1 * this._getTokenLifetimeMs();\n    const minLifetimeCapMs = MIN_TOKEN_LIFETIME_CAP_SECS * 1000;\n    if (minLifetimeMs > minLifetimeCapMs) {\n      minLifetimeMs = minLifetimeCapMs;\n    }\n    return new Date() > new Date(when) - minLifetimeMs;\n  }\n\n  // No-op on the server, overridden on the client.\n  _startupCallback(callback) {}\n}\n\n// Note that Accounts is defined separately in accounts_client.js and\n// accounts_server.js.\n\n/**\n * @summary Get the current user id, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n */\nMeteor.userId = () => Accounts.userId();\n\n/**\n * @summary Get the current user record, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n */\nMeteor.user = options => Accounts.user(options);\n\n/**\n * @summary Get the current user record, or `null` if no user is logged in. A reactive data source.\n * @locus Anywhere\n * @importFromPackage meteor\n * @param {Object} [options]\n * @param {MongoFieldSpecifier} options.fields Dictionary of fields to return or exclude.\n */\nMeteor.userAsync = options => Accounts.userAsync(options);\n\n// how long (in days) until a login token expires\nconst DEFAULT_LOGIN_EXPIRATION_DAYS = 90;\n// how long (in days) until reset password token expires\nconst DEFAULT_PASSWORD_RESET_TOKEN_EXPIRATION_DAYS = 3;\n// how long (in days) until enrol password token expires\nconst DEFAULT_PASSWORD_ENROLL_TOKEN_EXPIRATION_DAYS = 30;\n// Clients don't try to auto-login with a token that is going to expire within\n// .1 * DEFAULT_LOGIN_EXPIRATION_DAYS, capped at MIN_TOKEN_LIFETIME_CAP_SECS.\n// Tries to avoid abrupt disconnects from expiring tokens.\nconst MIN_TOKEN_LIFETIME_CAP_SECS = 3600; // one hour\n// how often (in milliseconds) we check for expired tokens\nexport const EXPIRE_TOKENS_INTERVAL_MS = 600 * 1000; // 10 minutes\n// A large number of expiration days (approximately 100 years worth) that is\n// used when creating unexpiring tokens.\nconst LOGIN_UNEXPIRING_TOKEN_DAYS = 365 * 100;\n"]}